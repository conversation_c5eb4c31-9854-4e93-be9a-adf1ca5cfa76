(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5f4a0678"],{1551:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"container-inner"},[n("el-form",{ref:"form",attrs:{inline:"",model:t.form,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"构件名称",prop:"Code"}},[n("el-input",{attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text"},model:{value:t.form.Code,callback:function(e){t.$set(t.form,"Code",e)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[n("el-select",{attrs:{filterable:"",disabled:!t.form.Area_Id,placeholder:"请选择",clearable:""},model:{value:t.form.InstallUnit_Id,callback:function(e){t.$set(t.form,"InstallUnit_Id",e)},expression:"form.InstallUnit_Id"}},t._l(t.SetupPositionData,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"构件类型",prop:"Component_Type"}},[n("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",staticStyle:{width:"100%"},attrs:{"select-params":t.treeSelectParams,"tree-params":t.ObjectTypeList,"value-key":"Id"},on:{searchFun:t._searchFun},model:{value:t.form.Component_Type,callback:function(e){t.$set(t.form,"Component_Type",e)},expression:"form.Component_Type"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.fetchData(),t.getTotalData()}}},[t._v("搜索")]),n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.$refs["form"].resetFields(),t.fetchData(),t.getTotalData()}}},[t._v("重置")])],1)],1),n("div",{staticClass:"total"},[n("div",[n("span",{staticStyle:{color:"#333333"}},[t._v(t._s(t.projectName))])]),n("div",[n("span",[t._v("构件总数")]),t._v(" "),n("span",{staticStyle:{"margin-right":"10px"}},[t._v(t._s(t.list.Total_Num)+"件")]),n("span",[t._v("构件总重")]),t._v(" "),n("span",[t._v(t._s(t.list.Total_Weight)+" t")])])]),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"fff cs-z-tb-wrapper"},[n("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:t.tbConfig,columns:t.columns,data:t.tbData,total:t.total,page:t.queryInfo.Page,stripe:"",border:""},on:{gridSizeChange:t.handlePageChange,gridPageChange:t.handlePageChange}})],1)],1)},r=[]},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),r=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,i=t.Data,s=t.Message;if(a){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var u=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),u=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=u.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||r.tablePageSize[0]),o(e.columns)}else e.$message({message:s,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var r=this.columns[a];if(r.Code===e){n.Type=r.Type,n.Filter_Type=r.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"2b59":function(t,e,n){"use strict";n.r(e);var a=n("4d76"),r=n("ebf4");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("3f75");var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"56cf405c",null);e["default"]=s.exports},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=c,e.GeAreaTrees=C,e.GetFileSync=T,e.GetInstallUnitIdNameList=I,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=S,e.GetProjectAreaTreeList=v,e.GetProjectEntity=u,e.GetProjectList=s,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=b,e.GetSchedulingPartList=j,e.IsEnableProjectMonomer=d,e.SaveProject=l,e.UpdateProjectTemplateBase=P,e.UpdateProjectTemplateContacts=g,e.UpdateProjectTemplateContract=_,e.UpdateProjectTemplateOther=y;var r=a(n("b775")),o=a(n("4328"));function i(t){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(t)})}function l(t){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(t)})}function d(t){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function T(t){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"31e5":function(t,e,n){"use strict";n.r(e);var a=n("b57e"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},"3f75":function(t,e,n){"use strict";n("e098")},"4d76":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[n("div",{staticClass:"cs-z-page-main-content",staticStyle:{padding:"0"}},[n("top-header",{staticStyle:{height:"auto","border-bottom":"16px solid #f2f3f5"},attrs:{padding:"16px 20px"},scopedSlots:t._u([{key:"left",fn:function(){return[n("div",[n("el-form",{ref:"form",attrs:{model:t.form,"label-width":"100px",inline:!0}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[n("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择..."},on:{change:t.getAreaList},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}},[t._l(t.ProjectNameData,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Sys_Project_Id}})}))],2)],1),n("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[n("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!t.form.Sys_Project_Id,"select-params":{clearable:!0},"tree-params":t.treeParamsArea},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1),n("el-form-item",{attrs:{label:"进度状态",prop:"Progress_Name"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Progress_Name,callback:function(e){t.$set(t.form,"Progress_Name",e)},expression:"form.Progress_Name"}},[n("el-option",{attrs:{label:"已延期",value:"已延期"}}),n("el-option",{attrs:{label:"未延期",value:"未延期"}})],1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),n("el-button",{on:{click:function(e){t.resetForm(),t.handleSearch()}}},[t._v("重置")]),n("el-button",{attrs:{loading:t.exporting,type:"success"},on:{click:t.handleExport}},[t._v("导 出")])],1)],1)],1)]},proxy:!0}])}),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-z-tb-wrapper fff"},[n("div",{staticClass:"table-wapper"},[n("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:t.columns,data:t.tbData,config:t.tbConfig,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridSizeChange:t.handlePageChange,gridPageChange:t.handlePageChange},scopedSlots:t._u([{key:"Progress_Name",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Progress_Name?a.Progress_Name:"-"))])]}},{key:"Contract_Used",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(null!==a.Contract_Used?a.Contract_Used:"-"))])]}},{key:"Import_Amount",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Import_Amount?a.Import_Amount:"-"))])]}},{key:"Production_Completion_Rate",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Production_Completion_Rate?a.Production_Completion_Rate+"%":"-"))])]}},{key:"To_StockIn_Amount",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.To_StockIn_Amount?a.To_StockIn_Amount:"-"))])]}},{key:"Stock_Amount",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Stock_Amount?a.Stock_Amount:"-"))])]}},{key:"Send_Amount",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Send_Amount?a.Send_Amount:"-"))])]}},{key:"Send_Num",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Send_Num?a.Send_Num:"-"))])]}},{key:"Send_Completion_Rate",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Send_Completion_Rate?a.Send_Completion_Rate+"%":"-"))])]}},{key:"Send_Punctuality_Rate",fn:function(e){var a=e.row;return[n("span",[t._v(t._s(a.Send_Punctuality_Rate?a.Send_Punctuality_Rate+"%":"-"))])]}},{key:"To_Schedule_Amount",fn:function(e){var a=e.row;return[a.To_Schedule_Amount?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleDetail(a,0)}}},[t._v(t._s(a.To_Schedule_Amount))]):n("span",{attrs:{type:"text"}},[t._v("-")])]}},{key:"To_Produce_Amount",fn:function(e){var a=e.row;return[a.To_Produce_Amount?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleDetail(a,1)}}},[t._v(t._s(a.To_Produce_Amount))]):n("span",{attrs:{type:"text"}},[t._v("-")])]}},{key:"Producing_Amount",fn:function(e){var a=e.row;return[a.Producing_Amount?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleDetail(a,2)}}},[t._v(t._s(a.Producing_Amount))]):n("span",{attrs:{type:"text"}},[t._v("-")])]}}])})],1)]),t.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.width},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:t.fetchData}})],1):t._e()],1)])},r=[]},"4e82":function(t,e,n){"use strict";var a=n("23e7"),r=n("e330"),o=n("59ed"),i=n("7b0b"),s=n("07fa"),u=n("083a"),l=n("577e"),c=n("d039"),d=n("addb"),f=n("a640"),m=n("3f7e"),p=n("99f4"),h=n("1212"),P=n("ea83"),g=[],_=r(g.sort),y=r(g.push),b=c((function(){g.sort(void 0)})),v=c((function(){g.sort(null)})),I=f("sort"),C=!c((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(P)return P<603;var t,e,n,a,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)g.push({k:e+a,v:n})}for(g.sort((function(t,e){return e.v-t.v})),a=0;a<g.length;a++)e=g[a].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),S=b||!v||!I||!C,j=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:l(e)>l(n)?1:-1}};a({target:"Array",proto:!0,forced:S},{sort:function(t){void 0!==t&&o(t);var e=i(this);if(C)return void 0===t?_(e):_(e,t);var n,a,r=[],l=s(e);for(a=0;a<l;a++)a in e&&y(r,e[a]);d(r,j(t)),n=s(r),a=0;while(a<n)e[a]=r[a++];while(a<l)u(e,a++);return e}})},"548d":function(t,e,n){"use strict";n.r(e);var a=n("1551"),r=n("31e5");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("95cf");var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"7f004be2",null);e["default"]=s.exports},"8d9e":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("7d54"),n("d3b7"),n("159b");var r=a(n("5530")),o=a(n("c14f")),i=a(n("1da1")),s=a(n("34e9")),u=a(n("0f97")),l=a(n("15ac")),c=n("586a"),d=a(n("bae0")),f=a(n("548d")),m=n("6186"),p=(n("8975"),n("3166")),h=n("ed08"),P=n("f84a");e.default={name:"PROAeraProgressTracking",components:{TopHeader:s.default,DynamicDataTable:u.default,ProcessDetail:f.default},mixins:[l.default,d.default],data:function(){return{warning:"warning",ProjectNameData:[],dialogVisible:!1,currentComponent:"",title:"",width:"",columns:[],tbData:[],selectList:[],exporting:!1,tbLoading:!1,btLoading:!1,tbConfig:{},total:0,ProjectStatusData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},form:{Progress_Name:"",Sys_Project_Id:"",Area_Id:""},queryInfo:{Page:1,PageSize:20}}},created:function(){this.getProjectOption()},mounted:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return t.getProTypeList(),e.n=1,t.getTypeList();case 1:return e.n=2,t.getTableConfig("AreaTrace,"+t.code);case 2:return e.n=3,t.fetchData();case 3:return e.a(2)}}),e)})))()},methods:{getProjectOption:function(){var t=this;(0,p.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getAreaList:function(){var t,e=this,n=null===(t=this.ProjectNameData.find((function(t){return t.Sys_Project_Id===e.form.Sys_Project_Id})))||void 0===t?void 0:t.Id;(0,p.GeAreaTrees)({projectId:n}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var n=t.Children;n&&n.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(n))}))},resetForm:function(){this.form.Project_Name="",this.$refs["form"].resetFields()},handleSearch:function(){this.queryInfo.Page=1,this.debounce(this.fetchData(),500)},handleClose:function(){},debounce:function(t,e){null!=this.timer&&clearTimeout(this.timer),this.timer=setTimeout(t,e)},handleDetail:function(t,e){var n=this;switch(this.width="50%",this.currentComponent="ProcessDetail",e){case 0:this.title="待排产量";break;case 1:this.title="待生产量";break;case 2:this.title="生产中量";break}this.dialogVisible=!0,this.$nextTick((function(a){n.$refs["content"].init(t,e)}))},handleExport:function(){var t=this;this.exporting=!0,(0,P.ExportProjectAreaProgress)((0,r.default)((0,r.default)({},this.queryInfo),this.form)).then((function(e){e.IsSucceed?window.open((0,h.combineURL)(t.$baseUrl,e.Data),"_blank"):t.$message({message:e.Message,type:"error"}),t.exporting=!1}))},fetchData:function(){var t=this;this.tbLoading=!0,(0,c.GetProjectAreaProgressList)((0,r.default)((0,r.default)({},this.queryInfo),this.form)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.tbLoading=!1}))},getProTypeList:function(){var t=this;(0,m.GetDictionaryDetailListByCode)({dictionaryCode:"ProjectStatus"}).then((function(e){e.IsSucceed&&(t.ProjectStatusData=e.Data)}))}}}},"95cf":function(t,e,n){"use strict";n("b23c6")},b23c6:function(t,e,n){},b57e:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("e9f5"),n("910d"),n("d3b7"),n("ac1f"),n("5319"),n("5b81"),n("498a");var r=a(n("5530")),o=a(n("c14f")),i=a(n("1da1")),s=a(n("0f97")),u=n("586a"),l=n("f2f6"),c=a(n("bae0")),d=a(n("f16f"));e.default={name:"HelloWorld",components:{DynamicDataTable:s.default},mixins:[d.default,c.default],provide:{},data:function(){return{form:{Sys_Project_Id:"",Area_Id:"",InstallUnit_Id:"",Component_Type:[],Code:"",Status:0},loading:!1,tbConfig:{},columns:[],tbData:[],SetupPositionData:[],ProjectNameData:[],TypeData:[],queryInfo:{Page:1,PageSize:20},total:0,projectName:"",list:{Total_Num:0,Total_Weight:0}}},mounted:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTypeList();case 1:return e.n=2,t.getTableConfig("AreaTraceDialog,"+t.code);case 2:return e.n=3,t.getObjectTypeList();case 3:return e.n=4,t.filterColumn();case 4:return e.a(2)}}),e)})))()},methods:{init:function(t,e){var n=this;return(0,i.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return n.form.Sys_Project_Id=t.Sys_Project_Id,n.form.Area_Id=t.Area_Id,n.projectName=t.Short_Name,n.form.Status=e,a.n=1,n.getInstall();case 1:n.fetchData(),n.getTotalData();case 2:return a.a(2)}}),a)})))()},filterColumn:function(){this.columns=2!==this.form.Status?this.columns.filter((function(t){return"Process_Team"!==t.Code})):this.columns},fetchData:function(){var t=this;this.loading=!0;var e=this.form.Code.trim().replaceAll(" ","\n");(0,u.GetComponentListByStatus)((0,r.default)((0,r.default)({},this.form),{},{Code:e},this.queryInfo)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1}))},getTotalData:function(){var t=this,e=this.form.Code.trim().replaceAll(" ","\n");(0,u.GetComponentYieldByStatus)((0,r.default)((0,r.default)({},this.form),{},{Code:e})).then((function(e){e.IsSucceed?t.list=e.Data:t.$message({message:e.Message,type:"error"})}))},getInstall:function(){var t=this;(0,l.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.SetupPositionData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))}}}},bae0:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("dca8"),n("d3b7");var r=a(n("c14f")),o=a(n("1da1")),i=n("fd31");e.default={data:function(){return{code:[],TypeId:[],typeOption:[],ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},treeSelectParams:{placeholder:"请选择",clearable:!0}}},mounted:function(){},methods:{getTypeList:function(){var t=this;return(0,o.default)((0,r.default)().m((function e(){var n,a,o;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,i.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:n=e.v,a=n.Data,n.IsSucceed?(t.typeOption=Object.freeze(a),t.typeOption.length>0&&(t.TypeId=null===(o=t.typeOption[0])||void 0===o?void 0:o.Id,t.code=t.typeOption.find((function(e){return e.Id===t.TypeId})).Code)):t.$message({message:n.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},getObjectTypeList:function(){var t=this;(0,i.GetCompTypeTree)({professional:this.code}).then((function(e){e.IsSucceed?(t.ObjectTypeList.data=e.Data,t.$nextTick((function(n){t.$refs.treeSelectObjectType.treeDataUpdateFun(e.Data)}))):t.$message({type:"error",message:e.Message})}))},_searchFun:function(t){this.$refs.treeSelectObjectType.filterFun(t)}}}},dca8:function(t,e,n){"use strict";var a=n("23e7"),r=n("bb2f"),o=n("d039"),i=n("861d"),s=n("f183").onFreeze,u=Object.freeze,l=o((function(){u(1)}));a({target:"Object",stat:!0,forced:l,sham:!r},{freeze:function(t){return u&&i(t)?u(s(t)):t}})},e098:function(t,e,n){},ebf4:function(t,e,n){"use strict";n.r(e);var a=n("8d9e"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},f16f:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186");e.default={methods:{getTableConfig:function(t){var e=this;return new Promise((function(n){(0,a.GetGridByCode)({code:t}).then((function(t){var a=t.IsSucceed,r=t.Data,o=t.Message;if(a){if(!r)return void e.$message({message:"表格配置不存在",type:"error"});e.tbConfig=Object.assign({},e.tbConfig,r.Grid),e.columns=(r.ColumnList.filter((function(t){return t.Is_Display}))||[]).map((function(t){return t.Is_Resizable=!0,t})),r.Grid.Is_Page&&(e.queryInfo.PageSize=+r.Grid.Row_Number),n(e.columns)}else e.$message({message:o,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit;this.queryInfo.Page=e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var r=this.columns[a];if(r.Code===e){n.Type=r.Type,n.Filter_Type=r.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=u,e.CheckPlanTime=l,e.DeleteInstallUnit=m,e.GetCompletePercent=_,e.GetEntity=b,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=g,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=s,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=y,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=P,e.SaveOhterSourceInstallUnit=v;var r=a(n("b775")),o=a(n("4328"));function i(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function l(t){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function b(t){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(t)})}function v(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},f84a:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportAllProjectComponentProductionFlowData=j,e.ExportProjectAreaProgress=D,e.ExportProjectComponentProductionFlowData=S,e.ExportSchedulingPlanProcess=T,e.GetCompFinishProcess=i,e.GetCompInPercent=l,e.GetCompOutPercent=c,e.GetCompPercent=d,e.GetComponentListByStatus=b,e.GetComponentYieldByStatus=v,e.GetDaysAndWeight=f,e.GetInstallUnitProducedCount1=h,e.GetInstallUnitProducedCount2=P,e.GetNestingFinishPercent=s,e.GetPartFinishPercent=u,e.GetProcessLoad=m,e.GetProjectAreaProgressList=_,e.GetProjectAreaProgressSummary=y,e.GetProjectComponentProductionFlowPageList=I,e.GetProjectSchdulingSendingData=C,e.GetProjectWarehouseDataStatistics=g,e.GetTeamLoad=p;var r=a(n("b775")),o=a(n("4328"));function i(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompFinishProcess",method:"post",data:o.default.stringify(t)})}function s(t){return(0,r.default)({url:"/PRO/ProductionCount/GetNestingFinishPercent",method:"post",data:o.default.stringify(t)})}function u(t){return(0,r.default)({url:"/PRO/ProductionCount/GetPartFinishPercent",method:"post",data:o.default.stringify(t)})}function l(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompInPercent",method:"post",data:o.default.stringify(t)})}function c(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompOutPercent",method:"post",data:o.default.stringify(t)})}function d(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompPercent",method:"post",data:o.default.stringify(t)})}function f(t){return(0,r.default)({url:"/PRO/ProductionCount/GetDaysAndWeight",method:"post",data:o.default.stringify(t)})}function m(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProcessLoad",method:"post",data:o.default.stringify(t)})}function p(t){return(0,r.default)({url:"/PRO/ProductionCount/GetTeamLoad",method:"post",data:o.default.stringify(t)})}function h(t){return(0,r.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount1",method:"post",data:o.default.stringify(t)})}function P(t){return(0,r.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount2",method:"post",data:o.default.stringify(t)})}function g(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectWarehouseDataStatistics",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressList",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressSummary",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentListByStatus",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentYieldByStatus",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectComponentProductionFlowPageList",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectSchdulingSendingData",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/ProductionCount/ExportProjectComponentProductionFlowData",method:"post",data:t})}function j(t){return(0,r.default)({url:"/pro/ProductionCount/ExportAllProjectComponentProductionFlowData",method:"post",data:t})}function T(t){return(0,r.default)({url:"/pro/ProductionCount/ExportSchedulingPlanProcess",method:"post",data:t})}function D(t){return(0,r.default)({url:"/pro/ProductionCount/ExportProjectAreaProgress",method:"post",data:t})}}}]);