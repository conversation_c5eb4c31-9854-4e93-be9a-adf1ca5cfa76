(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-c4697c38"],{"05e0":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.StatisticalDateW=e.ProjectStatusW=e.ProjectNumberW=e.ProjectAbbreviationW=void 0;e.ProjectAbbreviationW=140,e.ProjectNumberW=120,e.ProjectStatusW=100,e.StatisticalDateW=120},"0825":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var r=n(a("6612"));e.default={filters:{formatNum:function(t){return(0,r.default)(t).format("0,0.[00]")}},props:{amount:{type:[String,Number],default:0},label:{type:String,default:""},bg:{type:String,default:""}}}},"0c4c":function(t,e,a){"use strict";a.r(e);var n=a("c6039"),r=a("dbec");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("2a47");var o=a("2877"),u=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"76785c33",null);e["default"]=u.exports},"2a47":function(t,e,a){"use strict";a("fa11")},"3a87":function(t,e,a){},"4e82":function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),i=a("59ed"),o=a("7b0b"),u=a("07fa"),s=a("083a"),c=a("577e"),l=a("d039"),d=a("addb"),f=a("a640"),h=a("3f7e"),m=a("99f4"),p=a("1212"),b=a("ea83"),v=[],g=r(v.sort),y=r(v.push),D=l((function(){v.sort(void 0)})),O=l((function(){v.sort(null)})),S=f("sort"),w=!l((function(){if(p)return p<70;if(!(h&&h>3)){if(m)return!0;if(b)return b<603;var t,e,a,n,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)v.push({k:e+n,v:a})}for(v.sort((function(t,e){return e.v-t.v})),n=0;n<v.length;n++)e=v[n].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),A=D||!O||!S||!w,I=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:A},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(w)return void 0===t?g(e):g(e,t);var a,n,r=[],c=u(e);for(n=0;n<c;n++)n in e&&y(r,e[n]);d(r,I(t)),a=u(r),n=0;while(n<a)e[n]=r[n++];while(n<c)s(e,n++);return e}})},"596c":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.curModuleKey=void 0;e.curModuleKey="others"},8386:function(t,e,a){"use strict";a("3a87")},9216:function(t,e,a){"use strict";a.r(e);var n=a("0825"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"963e":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tag-x"},[a("span",[t._v(t._s(t.label)+"：")]),a("span",{staticClass:"cs-num"},[t._v(t._s(t._f("formatNum")(t.amount)))]),a("span",[t._v(" 元")])])},r=[]},ab88:function(t,e,a){"use strict";a.r(e);var n=a("963e"),r=a("9216");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("8386");var o=a("2877"),u=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"83134542",null);e["default"]=u.exports},b651:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("4de4"),a("14d9"),a("13d5"),a("4e82"),a("b0c0"),a("e9f5"),a("910d"),a("7d54"),a("9485"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var r=n(a("5530")),i=n(a("c14f")),o=n(a("1da1")),u=n(a("ac03")),s=n(a("ab88")),c=n(a("2082")),l=n(a("3502")),d=a("2f62"),f=a("7757"),h=a("bc1b"),m=a("596c"),p=a("05e0");e.default={name:"PROOtherReport",components:{Tags:s.default,VTable:l.default},mixins:[u.default,c.default],data:function(){return{addPageArray:[],totalInfo:{"1|subtotal":0,"2|subtotal":0,"3|subtotal":0,"4|subtotal":0,"5|subtotal":0},factoryOption:[],staticTime:"",showExport:!1,activeName:""}},beforeCreate:function(){this.curModuleKey=m.curModuleKey},mounted:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return t.loading=!0,t.$store.commit("oma/INIT",t.curModuleKey),t.$store.commit("oma/SAVE_KEY",t.curModuleKey),e.n=1,t.saveRoles({key:t.curModuleKey,value:t.$route.meta.Id});case 1:return t.initRoute(),e.n=2,t.init();case 2:return e.n=3,t.getDep();case 3:t.fetchData();case 4:return e.a(2)}}),e)})))()},methods:(0,r.default)((0,r.default)({},(0,d.mapActions)({saveRoles:"oma/saveRoles",saveFactoryId:"oma/saveFactoryId",saveOriginDate:"oma/saveOriginDate",saveDepartment:"oma/saveDepartment"})),{},{init:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getFactory();case 1:return t.factoryOption=e.v,t.factoryOption.length&&(t.activeName=t.factoryOption[0].Id,t.saveFactoryId({key:t.curModuleKey,value:t.activeName})),e.n=2,t.getDate();case 2:t.staticTime=e.v,t.saveOriginDate({key:t.curModuleKey,value:t.staticTime}),t.showExport=t.getRoles("OMAOtherExport");case 3:return e.a(2)}}),e)})))()},initRoute:function(){(this.getRoles("OMAOtherCheck")||this.getRoles("OMAOtherView"))&&this.addPageArray.push({path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-dd3daf90").then(a.bind(null,"41a7"))},name:"PROOtherReportDetail",meta:{title:"其他部门汇总详情",noCache:!0}});var t=[{path:this.$route.path+"/mInfo",hidden:!0,component:function(){return a.e("chunk-9b04fbdc").then(a.bind(null,"7fd8d"))},name:"OMAOtherOutputDetailInfo",meta:{title:"营业外收入明细",noCache:!0}},{path:this.$route.path+"/costInfo",hidden:!0,component:function(){return a.e("chunk-3abae211").then(a.bind(null,"0ab1"))},name:"OMAOtherCostDetailInfo",meta:{title:"主材成本明细",noCache:!0}},{path:this.$route.path+"/feeInfo",hidden:!0,component:function(){return a.e("chunk-b70207a6").then(a.bind(null,"3226"))},name:"OMAOtherFeeDetailInfo",meta:{title:"辅料费用明细",noCache:!0}},{path:this.$route.path+"/otherFeeInfo",hidden:!0,component:function(){return a.e("chunk-c5261d7a").then(a.bind(null,"2cf1"))},name:"OMAOtherOtherFeeDetailInfo",meta:{title:"其他费用明细",noCache:!0}}];this.getRoles("OMAOtherDetailOutDetail")&&this.addPageArray.push(t[0]),this.getRoles("OMAOtherDetailCostDetail")&&this.addPageArray.push(t[1]),this.getRoles("OMAOtherDetailFeeDetail")&&this.addPageArray.push(t[2]),this.getRoles("OMAOtherDetailOtherDetail")&&this.addPageArray.push(t[3]),this.addPageArray.length&&this.initPage(this.$route.name)},getTotalInfos:function(){var t=this,e=function(t){return 3===t.AccountingStatus},a=function(a){return t.tableData.filter(e).reduce((function(t,e){return t+e[a]}),0)};this.totalInfo["1|subtotal"]=a("1|subtotal"),this.totalInfo["2|subtotal"]=a("2|subtotal"),this.totalInfo["3|subtotal"]=a("3|subtotal"),this.totalInfo["4|subtotal"]=a("4|subtotal"),this.totalInfo["5|subtotal"]=a("5|subtotal")},fetchData:function(){var t=this;this.checkDate(this.staticTime)&&(this.loading=!0,(0,h.GetQTSummaryList)({StatisticalDate:this.staticTime,FactoryId:this.activeName}).then((function(e){if(e.IsSucceed){t.tableData=((null===e||void 0===e?void 0:e.Data)||[]).sort((function(t,e){return new Date(e.Statics_Date)-new Date(t.Statics_Date)}));var a=t.setTotalData(t.tableData,t.generateColumn(),!0),n=a.column;t.columns=n,t.$refs["tb"].setColumns(n),t.getTotalInfos()}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},getDate:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){var a;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,f.GetReportLastDate)({FactoryId:t.activeName});case 1:if(a=e.v,!a.IsSucceed){e.n=2;break}return e.a(2,a.Data);case 2:t.$message({message:a.Message,type:"error"});case 3:return e.a(2)}}),e)})))()},changeTab:function(t){this.saveFactoryId({key:this.curModuleKey,value:t.name}),this.getDep(),this.fetchData()},handleCheck:function(t){this.$router.push({name:"PROOtherReportDetail",query:{pg_redirect:this.$route.name,type:"check",d:t.Statics_Date}})},handleView:function(t){this.$router.push({name:"PROOtherReportDetail",query:{pg_redirect:this.$route.name,type:"view",d:t.Statics_Date}})},getDep:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){var a;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,h.GetQTDepartList)({FactoryId:t.activeName});case 1:a=e.v,a.IsSucceed?t.saveDepartment({key:t.curModuleKey,value:a.Data||[]}):t.$message({message:a.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},generateColumn:function(){var t=this,e=this.$createElement,a=140,n=[{title:"统计日期",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{field:"Statics_Date",minWidth:p.StatisticalDateW,title:"当月合计",formatter:["formatDateTime","yyyy-MM-dd"]}]}]},{title:"营业外收入(元)",params:{bg:"bg-cyan"},children:[],cKey:"1"},{params:{bg:"bg-blue"},title:"主材成本(元)",children:[],cKey:"2"},{title:"辅料费用(元)",params:{bg:"bg-orange"},children:[],cKey:"3"},{title:"资产折旧(元)",params:{bg:"bg-purple"},children:[],cKey:"5"},{title:"其他费用(元)",params:{bg:"bg-yellow"},children:[],cKey:"4"},{field:"AccountingStatus",title:"核算状态(元)",minWidth:a,slots:{default:function(t){var a=t.row;return[e("el-tag",{attrs:{type:1===a.AccountingStatus?"danger":2===a.AccountingStatus?"warning":3===a.AccountingStatus?"success":""}},[1===a.AccountingStatus?"未核算":2===a.AccountingStatus?"待确认":3===a.AccountingStatus?"已核算":""])]}}},{title:"操作",params:{empty:!0},visible:!0,fixed:"right",minWidth:a,slots:{default:function(a){var n=a.row,r=a.column,i=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleView(n)}}},["查看"]),o=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleCheck(n)}}},["审核"]),u=[];return t.getRoles("OMAOtherView")&&u.push(i),2===n.AccountingStatus&&t.getRoles("OMAOtherCheck")&&u.push(o),u.length&&(r.params.empty=!1),u}}}];return n.forEach((function(e){if(e.cKey){var n=[];t.department.forEach((function(t,r){var i={title:t.Display_Name,children:[{minWidth:a,field:"".concat(e.cKey,"|").concat(t.Id),title:0,isTotal:!0}]};n.push(i)})),n.push({title:"小计",children:[{minWidth:a,field:"".concat(e.cKey,"|subtotal"),title:0,isTotal:!0}]}),e.children=n}})),n}})}},bc1b:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetFeesDetailList=h,e.GetMaterialCostDetailList=f,e.GetOperationalSummaryDetail=b,e.GetOperationalSummaryList=p,e.GetOtherFeesDetailList=m,e.GetQTDepartList=u,e.GetQTNonBusinessDetailList=d,e.GetQTProjectSummaryList=s,e.GetQTSummaryList=o,e.GetSalesReportList=g,e.GetUnallocatedDepartmentSummary=v,e.ReviewStockControlSummary=l,e.SubmitQTAccounting=c;var r=n(a("b775")),i="";function o(t){return(0,r.default)({url:i+"/oma/qtreport/GetQTSummaryList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/oma/qtreport/GetQTDepartList",method:"post",data:t})}function s(t){return(0,r.default)({url:i+"/oma/qtreport/GetQTProjectSummaryList",method:"post",data:t})}function c(t){return(0,r.default)({url:i+"/oma/qtreport/SubmitQTAccounting",method:"post",data:t})}function l(t){return(0,r.default)({url:i+"/oma/qtreport/ReviewStockControlSummary",method:"post",data:t})}function d(t){return(0,r.default)({url:i+"/oma/qtreport/GetQTNonBusinessDetailList",method:"post",data:t})}function f(t){return(0,r.default)({url:i+"/oma/qtreport/GetMaterialCostDetailList",method:"post",data:t})}function h(t){return(0,r.default)({url:i+"/oma/qtreport/GetFeesDetailList",method:"post",data:t})}function m(t){return(0,r.default)({url:i+"/oma/qtreport/GetOtherFeesDetailList",method:"post",data:t})}function p(t){return(0,r.default)({url:i+"/oma/qtreport/GetOperationalSummaryList",method:"post",data:t})}function b(t){return(0,r.default)({url:i+"/oma/qtreport/GetOperationalSummaryDetail",method:"post",data:t})}function v(t){return(0,r.default)({url:i+"/oma/qtreport/GetUnallocatedDepartmentSummary",method:"post",data:t})}function g(t){return(0,r.default)({url:i+"/oma/qtreport/GetSalesReportList",method:"post",data:t})}},c6039:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-tabs",{on:{"tab-click":t.changeTab},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.factoryOption,(function(t){return a("el-tab-pane",{key:t.Id,attrs:{label:t.Short_Name,name:t.Id}})})),1),a("div",{staticClass:"search-x"},[a("label",[t._v("统计时间： "),a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"month","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.staticTime,callback:function(e){t.staticTime=e},expression:"staticTime"}})],1),a("el-button",{attrs:{disabled:t.loading,type:"primary"},on:{click:function(e){return t.fetchData()}}},[t._v("搜 索")])],1),a("el-divider"),a("div",{staticClass:"toolbar"},[a("Tags",{staticClass:"bg-cyan",attrs:{label:"营业外收入",amount:t.totalInfo["1|subtotal"]}}),a("Tags",{staticClass:"bg-blue",attrs:{label:"主材成本(元)",amount:t.totalInfo["2|subtotal"]}}),a("Tags",{staticClass:"bg-orange",attrs:{label:"辅料费用",amount:t.totalInfo["3|subtotal"]}}),a("Tags",{staticClass:"bg-purple",attrs:{label:"资产折旧",amount:t.totalInfo["5|subtotal"]}}),a("Tags",{staticClass:"bg-yellow",attrs:{label:"其他费用",amount:t.totalInfo["4|subtotal"]}})],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("label",[t._v("数据列表")]),t.showExport?a("el-button",{staticClass:"mr-10",attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.staticTime+"其他部门汇总表")}}},[t._v("导出报表")]):t._e()],1),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},r=[]},dbec:function(t,e,a){"use strict";a.r(e);var n=a("b651"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},fa11:function(t,e,a){}}]);