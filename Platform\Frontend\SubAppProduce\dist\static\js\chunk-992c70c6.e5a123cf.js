(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-992c70c6"],{"06b1":function(e,t,a){"use strict";a.r(t);var l=a("2fab"),r=a.n(l);for(var i in l)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(i);t["default"]=r.a},"1c4e":function(e,t,a){},"21c3":function(e,t,a){"use strict";a.r(t);var l=a("d844"),r=a("b28b");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var n=a("2877"),o=Object(n["a"])(r["default"],l["a"],l["b"],!1,null,null,null);t["default"]=o.exports},"2fab":function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=l(a("5530")),i=a("6186"),n=l(a("21c3")),o=l(a("7980"));t.default={name:"PROGeneralSettings",components:{EditDialog:n.default,Subordinate:o.default},data:function(){return{data:[],form:{Display_Name:"",Value:"",Remark:""},rules:{Display_Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Value:[{required:!0,message:"请输入项目值",trigger:"blur"}]},defaultProps:{children:"Children",label:"Display_Name"},tableData:[],dialogVisible:!1,type:"leftAdd",Id:""}},created:function(){this.fetchData()},methods:{handleNodeClick:function(e){var t=this;this.Id=e.Id,(0,i.GetDictionaryTreeDetailList)({DictionaryId:e.Id}).then((function(e){e.IsSucceed&&(t.tableData=e.Data)}))},handleChangeSwitch:function(e,t){var a=this;(0,i.UpdateDictionaryDetailIsValid)({is_valid:e,id:t.Id}).then((function(e){e.IsSucceed?(a.$message({message:"修改成功",type:"success"}),a.fetchTableData(a.Id)):a.$message({message:e.Message,type:"error"})}))},fetchData:function(){var e=this;(0,i.GetDictionaryListProjectModify)({}).then((function(t){t.IsSucceed&&(e.data=t.Data,e.fetchTableData(e.data[0].Id),e.$nextTick((function(){e.$refs.tree.setCurrentKey(e.data[0].Id),e.Id=e.data[0].Id})))}))},fetchTableData:function(e){var t=this;(0,i.GetDictionaryTreeDetailList)({DictionaryId:e}).then((function(e){e.IsSucceed&&(t.tableData=e.Data)}))},edit:function(e){this.$refs.EditDialog.open(e)},submit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;var a=localStorage.getItem("Last_Working_Object_Id");(0,i.SaveDictionaryDetail)((0,r.default)((0,r.default)({Dictionary_Id:e.Id},e.form),{},{Working_object_Id:a})).then((function(t){t.IsSucceed?(e.$message({message:"保存成功！",type:"success"}),e.fetchTableData(e.Id),e.$refs.form.resetFields(),e.dialogVisible=!1):e.$message({message:"保存失败！",type:"error"})}))}))},close:function(){this.$refs.form.resetFields(),this.dialogVisible=!1},delTable:function(e){var t=this;this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,i.DeleteDictionaryDetail)({Id:e}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchTableData(t.Id)):t.$message({type:"error",message:e.Message})}))}))},handleAdd:function(e){this.type=e,this.dialogVisible=!0},Subordinate:function(e){this.$refs.SubordinateDialog.open(e)}}}},3114:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=l(a("5530")),i=a("6186");t.default={data:function(){return{dialogVisible:!1,form:{Display_Name:"",Value:"",Is_Enabled:!0,Remark:""},rules:{Display_Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Value:[{required:!0,message:"请输入项目值",trigger:"blur"}]}}},methods:{open:function(e){var t=this;(0,i.GetDictionaryDetailEntity)({Id:e}).then((function(e){e.IsSucceed&&(t.form=Object.assign({},e.Data))})),this.dialogVisible=!0},submit:function(){var e=this,t=localStorage.getItem("Last_Working_Object_Id");(0,i.SaveDictionaryDetail)((0,r.default)((0,r.default)({},this.form),{},{Working_object_Id:t})).then((function(t){t.IsSucceed?(e.$message({message:"保存成功！",type:"success"}),e.dialogVisible=!1,e.$emit("tablechange"),e.close()):e.$message({message:t.Message,type:"error"})}))},close:function(){this.form={Display_Name:"",Value:"",Is_Enabled:!0,Remark:""},this.dialogVisible=!1}}}},"43b7":function(e,t,a){"use strict";a.r(t);var l=a("cb96"),r=a.n(l);for(var i in l)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(i);t["default"]=r.a},7980:function(e,t,a){"use strict";a.r(t);var l=a("d124"),r=a("43b7");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var n=a("2877"),o=Object(n["a"])(r["default"],l["a"],l["b"],!1,null,null,null);t["default"]=o.exports},8865:function(e,t,a){"use strict";a("1c4e")},b1e4:function(e,t,a){"use strict";a.r(t);var l=a("e496"),r=a("06b1");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("8865");var n=a("2877"),o=Object(n["a"])(r["default"],l["a"],l["b"],!1,null,"4ff2ac11",null);t["default"]=o.exports},b28b:function(e,t,a){"use strict";a.r(t);var l=a("3114"),r=a.n(l);for(var i in l)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(i);t["default"]=r.a},cb96:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=l(a("5530")),i=a("6186");t.default={data:function(){return{dialogVisible:!1,form:{Parent_Id:"",Display_Name:"",Value:"",Is_Enabled:!0,Remark:""},rules:{Display_Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Value:[{required:!0,message:"请输入项目值",trigger:"blur"}]}}},methods:{open:function(e){this.dialogVisible=!0,this.form.Parent_Id=e},submit:function(){var e=this,t=localStorage.getItem("Last_Working_Object_Id");(0,i.SaveDictionaryDetail)((0,r.default)((0,r.default)({},this.form),{},{Working_object_Id:t})).then((function(t){t.IsSucceed?(e.$message({message:"保存成功！",type:"success"}),e.dialogVisible=!1,e.$emit("tablechange"),e.close()):e.$message({message:t.Message,type:"error"})}))},close:function(){this.form={Parent_Id:"",Display_Name:"",Value:"",Is_Enabled:!0,Remark:""},this.dialogVisible=!1}}}},d124:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return r}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"新增下级",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1),a("el-form-item",{attrs:{label:"项目值",prop:"Value"}},[a("el-input",{model:{value:e.form.Value,callback:function(t){e.$set(e.form,"Value",t)},expression:"form.Value"}})],1),a("el-form-item",{attrs:{label:"是否有效",prop:"Is_Enabled"}},[a("el-switch",{attrs:{"active-color":"#13ce66"},model:{value:e.form.Is_Enabled,callback:function(t){e.$set(e.form,"Is_Enabled",t)},expression:"form.Is_Enabled"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},d844:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return r}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"编辑",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1),a("el-form-item",{attrs:{label:"项目值",prop:"Value"}},[a("el-input",{model:{value:e.form.Value,callback:function(t){e.$set(e.form,"Value",t)},expression:"form.Value"}})],1),a("el-form-item",{attrs:{label:"是否有效",prop:"Is_Enabled"}},[a("el-switch",{attrs:{"active-color":"#13ce66"},model:{value:e.form.Is_Enabled,callback:function(t){e.$set(e.form,"Is_Enabled",t)},expression:"form.Is_Enabled"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},e496:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return r}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-row",{staticClass:"h100",attrs:{type:"flex",gutter:15}},[a("el-col",{attrs:{span:5}},[a("el-card",{staticClass:"box-card h100"},[a("div",{staticStyle:{"text-align":"right"}},[e._e()],1),a("el-tree",{ref:"tree",attrs:{data:e.data,"node-key":"Id","default-expand-all":"","highlight-current":"",props:e.defaultProps},on:{"node-click":e.handleNodeClick}})],1)],1),a("el-col",{attrs:{span:19}},[a("el-card",{staticClass:"box-card h100"},[a("el-button",{staticClass:"topbutton",attrs:{type:"primary"},on:{click:function(t){return e.handleAdd("rightAdd")}}},[e._v("新增")]),a("el-table",{staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{stripe:"",border:"",data:e.tableData,"max-height":"700","row-key":"Id","tree-props":{children:"Children",hasChildren:"hasChildren"}}},[a("el-table-column",{attrs:{prop:"Display_Name",label:"项目名称",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(e._s(a.Data.Display_Name))]}}])}),a("el-table-column",{attrs:{prop:"Value",label:"项目值",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(e._s(a.Data.Value))]}}])}),a("el-table-column",{attrs:{prop:"Remark",label:"备注",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(e._s(a.Data.Remark))]}}])}),a("el-table-column",{attrs:{prop:"Is_Enabled",label:"有效",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-switch",{attrs:{disabled:null===l.Data.Working_object_Id},on:{change:function(t){return e.handleChangeSwitch(t,l)}},model:{value:l.Data.Is_Enabled,callback:function(t){e.$set(l.Data,"Is_Enabled",t)},expression:"row.Data.Is_Enabled"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{attrs:{disabled:null===l.Data.Working_object_Id,type:"text"},on:{click:function(t){return e.edit(l.Id)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.Subordinate(l.Data.Id)}}},[e._v("下级配置")]),null!==l.Data.Working_object_Id?a("el-button",{staticStyle:{color:"#fb6b7f"},attrs:{type:"text"},on:{click:function(t){return e.delTable(l.Data.Id)}}},[e._v("删除")]):e._e()]}}])})],1)],1)],1)],1),a("el-dialog",{attrs:{title:"新增",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},["leftAdd"===e.type?a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1):[a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1),a("el-form-item",{attrs:{label:"项目值",prop:"Value"}},[a("el-input",{model:{value:e.form.Value,callback:function(t){e.$set(e.form,"Value",t)},expression:"form.Value"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)]],2),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1),a("EditDialog",{ref:"EditDialog",on:{tablechange:function(t){return e.fetchTableData(e.Id)}}}),a("Subordinate",{ref:"SubordinateDialog",on:{tablechange:function(t){return e.fetchTableData(e.Id)}}})],1)},r=[]}}]);