(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ab542dec"],{"0e40":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530"));a("d81d"),a("14d9"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var o=n(a("0f97")),i=(a("d51a"),a("fd31"),n(a("641e"))),d=a("ed08"),l=n(a("7063")),c=(a("6186"),a("f4e9")),u=a("1b69"),s=a("7015f"),f=n(a("2082"));t.default={components:{DynamicDataTable:o.default,addCc:l.default},mixins:[i.default,f.default],data:function(){return{dialogVisible:!1,dialogVisible2:!1,loading:!1,form:{Bill_No:"",Moc_Type_Id:"",Sys_Project_Id:"",dateRange:"",Approve_State:"",Approve_Result:"",Is_Change_Model:"",Type:"",PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},addPageArray:[{path:this.$route.path+"/addChangeOrder",hidden:!0,component:function(){return Promise.all([a.e("chunk-2d0c91c4"),a.e("chunk-4c038d99")]).then(a.bind(null,"9609b"))},name:"PROAddChangeOrder",meta:{title:"变更单详情"}}],typeList:[],projectList:[],userList:[]}},created:function(){this.getBasicData(),this.getFactoryTypeOption("pro_change_apply_list")},mounted:function(){},methods:{getBasicData:function(){var e=this;(0,s.GetChangeType)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.typeList=t.Data.Data)})),(0,u.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)})),(0,c.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},fetchData:function(){var e=this;this.loading=!0;var t=(0,r.default)({},this.form);delete t["dateRange"],delete t["PageInfo"],t.Create_Begin=this.form.dateRange?(0,d.parseTime)(this.form.dateRange[0],"{y}-{m}-{d}"):"",t.Create_End=this.form.dateRange?(0,d.parseTime)(this.form.dateRange[1],"{y}-{m}-{d}"):"",(0,s.GetMyChangeOrderPageList)((0,r.default)((0,r.default)({},t),this.form.PageInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Launch_Date=e.Launch_Date?(0,d.parseTime)(new Date(e.Launch_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Launch_Date,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).catch(console.error).finally((function(){e.loading=!1}))},datePickerwrapper:function(){},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},handleClose:function(){this.$refs.addCc.resetForm(),this.dialogVisible=!1},handleChange:function(e,t){var a={Id:e,type:t};this.$router.push({name:"PROAddChangeOrder",query:{pg_redirect:"PROChangeApply",data:a}})},handleCc:function(e){var t=this;this.dialogVisible=!0,this.$nextTick((function(){t.$refs.addCc.init(e)}))},handleSub:function(e){var t=this;this.$confirm("提交选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e||t.selectList.forEach((function(e){e.Id+","}))})).catch((function(){}))},handleWithdraw:function(e){var t=this;this.$confirm("撤回选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="";e||t.selectList.forEach((function(e){a+=e.Id+","})),(0,s.CancelChangeOrder)({changeId:e||a}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handlePass:function(e){var t=this;this.$confirm("提交选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e||t.selectList.forEach((function(e){e.Id+","}))})).catch((function(){}))},handleDel:function(e){var t=this;this.$confirm("通过选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e||t.selectList.forEach((function(e){e.Id+","}))})).catch((function(){}))},handleExp:function(e){var t=this;this.$confirm("导出选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e||t.selectList.forEach((function(e){e.Id+","}))})).catch((function(){}))}}}},"2b2a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5b5b")),o=n(a("af55"));t.default={name:"PROStartInspect",components:{myLaunch:r.default,drafts:o.default},data:function(){return{activeName:"我发起的"}}}},"3c8e":function(e,t,a){},"4e82":function(e,t,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),i=a("7b0b"),d=a("07fa"),l=a("083a"),c=a("577e"),u=a("d039"),s=a("addb"),f=a("a640"),h=a("3f7e"),p=a("99f4"),g=a("1212"),m=a("ea83"),C=[],P=r(C.sort),y=r(C.push),v=u((function(){C.sort(void 0)})),b=u((function(){C.sort(null)})),O=f("sort"),_=!u((function(){if(g)return g<70;if(!(h&&h>3)){if(p)return!0;if(m)return m<603;var e,t,a,n,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)C.push({k:t+n,v:a})}for(C.sort((function(e,t){return t.v-e.v})),n=0;n<C.length;n++)t=C[n].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),S=v||!b||!O||!_,I=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:c(t)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:S},{sort:function(e){void 0!==e&&o(e);var t=i(this);if(_)return void 0===e?P(t):P(t,e);var a,n,r=[],c=d(t);for(n=0;n<c;n++)n in t&&y(r,t[n]);s(r,I(e)),a=d(r),n=0;while(n<a)t[n]=r[n++];while(n<c)l(t,n++);return t}})},"5b5b":function(e,t,a){"use strict";a.r(t);var n=a("a873"),r=a("986c");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("ab80");var i=a("2877"),d=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"54e0c294",null);t["default"]=d.exports},"5ba8":function(e,t,a){"use strict";a.r(t);var n=a("f2ed"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"62bf":function(e,t,a){"use strict";a.r(t);var n=a("b84e"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"641e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,r.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(a){a.IsSucceed?(t.ProfessionalType=a.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:a.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,n.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=t.tbConfig.Code.split(",");"plm_component_page_list"!==i[0]&&"plm_parts_page_list"!==i[0]||(t.tbConfig.Is_Page=!0),t.columns=(r.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+r.Grid.Row_Number:t.form.PageSize=+r.Grid.Row_Number,a(t.columns),t.fetchData()}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},"6c5c":function(e,t,a){"use strict";a("afe4")},"7015f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=O,t.AgainSubmitChangeOrder=v,t.BatchReuseEngineeringContactChangedComponentPart=ie,t.BatchReuseEngineeringContactMocComponentPart=de,t.CancelChangeOrder=y,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=F,t.DeleteChangeOrder=s,t.DeleteChangeOrderV2=w,t.DeleteChangeReason=g,t.DeleteChangeType=d,t.DeleteEngineeringContactChangeOrder=J,t.DeleteMocOrder=W,t.DeleteMocType=Pe,t.ExportEngineeringContactChangedAddComponentPart=ue,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=re,t.ExportMocAddComponentPart=se,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=S,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=c,t.GetChangeOrderTaskInfo=x,t.GetChangeOrderTaskPageList=D,t.GetChangeOrderV2=A,t.GetChangeOrderV2PageList=M,t.GetChangeReason=h,t.GetChangeType=o,t.GetChangedComponentPartPageList=N,t.GetChangedComponentPartProductionList=V,t.GetCompAndPartSchdulingPageList=G,t.GetCompAndPartTaskList=k,t.GetCompanyUserPageList=T,t.GetEngineeringContactChangeOrder=q,t.GetEngineeringContactChangeOrderPageList=Q,t.GetEngineeringContactChangedAddComponentPartPageList=le,t.GetEngineeringContactChangedAddComponentPartSummary=ce,t.GetEngineeringContactChangedComponentPartPageList=Y,t.GetEngineeringContactChangedSummary=X,t.GetEngineeringContactFileInfo=U,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=K,t.GetEngineeringContactMocSummary=ne,t.GetFactoryChangeTypeListV2=z,t.GetFactoryPeoplelist=l,t.GetMocAddComponentPartPageList=he,t.GetMocModelList=ve,t.GetMocOrderInfo=me,t.GetMocOrderPageList=pe,t.GetMocOrderTypeList=Ce,t.GetMyChangeOrderPageList=P,t.GetProjectAreaChangeTreeList=j,t.GetProjectChangeOrderList=R,t.GetTypeReason=m,t.ImportChangFile=ge,t.ImportChangeDeependFile=C,t.QueryHistories=_,t.ReuseEngineeringContactChangedComponentPart=ae,t.ReuseEngineeringContactMocComponentPart=oe,t.SaveChangeOrder=u,t.SaveChangeOrderTask=L,t.SaveChangeOrderV2=E,t.SaveChangeReason=p,t.SaveChangeType=i,t.SaveEngineeringContactChangeOrder=H,t.SaveMocOrder=be,t.SaveMocOrderType=ye,t.SubmitChangeOrder=b,t.SubmitChangeOrderV2=$,t.SubmitMocOrder=B,t.Verification=I;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function _(e){return(0,r.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function S(e){return(0,r.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function T(e){return(0,r.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function ye(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function be(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},7063:function(e,t,a){"use strict";a.r(t);var n=a("b979"),r=a("62bf");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("9ed2");var i=a("2877"),d=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"4cb4506f",null);t["default"]=d.exports},"79a7":function(e,t,a){"use strict";a("3c8e")},"7aac":function(e,t,a){"use strict";a.r(t);var n=a("2b2a"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"868f":function(e,t,a){},"986c":function(e,t,a){"use strict";a.r(t);var n=a("0e40"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"989b":function(e,t,a){},"9ed2":function(e,t,a){"use strict";a("989b")},a873:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"110px"}},[a("el-form-item",{attrs:{label:"变更单号",prop:"Bill_No"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No",t)},expression:"form.Bill_No"}})],1),a("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.typeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"发起时间",prop:"dateRange"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange,callback:function(t){e.$set(e.form,"dateRange",t)},expression:"form.dateRange"}})],1),a("el-form-item",{attrs:{label:"审批状态",prop:"Approve_State"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Approve_State,callback:function(t){e.$set(e.form,"Approve_State",t)},expression:"form.Approve_State"}},[a("el-option",{attrs:{label:"待审批",value:"待审批"}}),a("el-option",{attrs:{label:"已审批",value:"已审批"}})],1)],1),a("el-form-item",{attrs:{label:"审批结果",prop:"Approve_Result"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Approve_Result,callback:function(t){e.$set(e.form,"Approve_Result",t)},expression:"form.Approve_Result"}},[a("el-option",{attrs:{label:"无",value:"无"}}),a("el-option",{attrs:{label:"已通过",value:"已通过"}}),a("el-option",{attrs:{label:"已退回",value:"已退回"}})],1)],1),a("el-form-item",{attrs:{label:"是否有变更任务",prop:"Is_Change_Model"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Is_Change_Model,callback:function(t){e.$set(e.form,"Is_Change_Model",t)},expression:"form.Is_Change_Model"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleChange("",0)}}},[e._v("发起变更")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Is_Change_Model",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Is_Change_Model?"是":"否"))])]}},{key:"op",fn:function(t){var n=t.row,r=t.index;return[a("div",[a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleChange(n.Id,1)}}},[e._v("查看")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleCc(n.Id)}}},[e._v("抄送")]),"待审批"==n.Approve_State?a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleWithdraw(n.Id)}}},[e._v("撤回")]):e._e(),"已退回"==n.Approve_Result?a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleChange(n.Id,4)}}},[e._v("再次发起")]):e._e()],1)]}}])})],1)],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"选择抄送人",visible:e.dialogVisible,width:"576px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("add-Cc",{ref:"addCc",on:{close:e.handleClose,refresh:e.fetchData}})],1)],1)},r=[]},ab80:function(e,t,a){"use strict";a("868f")},af55:function(e,t,a){"use strict";a.r(t);var n=a("dc2b"),r=a("5ba8");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("6c5c");var i=a("2877"),d=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"f7f22348",null);t["default"]=d.exports},afe4:function(e,t,a){},b84e:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b");var r=n(a("5530")),o=a("7015f");t.default={data:function(){return{form:{Id:"",CC_UserId:[]},btnLoading:!1,userList:[]}},created:function(){this.getBasicData()},mounted:function(){},methods:{init:function(e){this.form.Id=e},getBasicData:function(){var e=this;(0,o.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;var a=(0,r.default)({},e.form),n="";0!==a.CC_UserId.length&&(n=a.CC_UserId.join(",")),(0,o.AddChangeCopyHistory)({changeId:e.form.Id,userIds:n}).then((function(t){t.IsSucceed?(e.$message({message:"抄送成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"})}))}))},resetForm:function(){this.$refs.form.resetFields(),this.form.Id="",this.form.CC_UserId=[],this.btnLoading=!1}}}},b979:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"选择抄送人",prop:"CC_UserId",rules:{required:!0,message:"请选择",trigger:"change"}}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:e.form.CC_UserId,callback:function(t){e.$set(e.form,"CC_UserId",t)},expression:"form.CC_UserId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1),a("el-form-item",[a("div",{staticClass:"btn-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submitForm()}}},[e._v("提交")])],1)])],1)],1)},r=[]},bc05:function(e,t,a){"use strict";a.r(t);var n=a("c021"),r=a("7aac");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("79a7");var i=a("2877"),d=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"5d23b02a",null);t["default"]=d.exports},c021:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"header_tab"},[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"我发起的",name:"我发起的"}}),a("el-tab-pane",{attrs:{label:"草稿箱",name:"草稿箱"}})],1)],1),"我发起的"==e.activeName?a("my-launch"):e._e(),"草稿箱"==e.activeName?a("drafts"):e._e()],1)])},r=[]},d51a:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddLanch=d,t.BatchManageSaveCheck=P,t.DelLanch=C,t.DeleteToleranceSetting=x,t.EntityQualityManagement=s,t.ExportQISummary=D,t.GetCheckingEntity=v,t.GetCompPartForSpotCheckPageList=S,t.GetCompQISummary=R,t.GetDictionaryDetailListByCode=l,t.GetEditById=y,t.GetFactoryPeoplelist=h,t.GetNodeList=c,t.GetPageFeedBack=b,t.GetPageQualityManagement=o,t.GetPartAndSteelBacrode=u,t.GetSheetDwg=p,t.GetSpotCheckingEntity=I,t.GetToleranceSettingList=T,t.ImportQISummary=G,t.ManageAdd=i,t.RectificationRecord=_,t.SaveFeedBack=O,t.SavePass=g,t.SaveQIReportData=L,t.SaveTesting=f,t.SaveToleranceSetting=k,t.SubmitLanch=m;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/PRO/Inspection/GetPageQualityManagement",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Inspection/ManageAdd",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:e})}function l(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Inspection/EntityQualityManagement",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Inspection/SaveTesting",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Inspection/GetSheetDwg",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Inspection/SavePass",method:"post",data:e,timeout:12e5})}function m(e){return(0,r.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Inspection/BatchManageSaveCheck",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckingEntity",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Inspection/GetPageFeedBack",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Inspection/SaveFeedBack",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Inspection/GetCompPartForSpotCheckPageList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Inspection/GetSpotCheckingEntity",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/Inspection/GetCompQISummary",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/Inspection/ExportQISummary",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/Inspection/ImportQISummary",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/Inspection/SaveQIReportData",method:"post",data:e})}function k(e){return(0,r.default)({url:"/pro/Inspection/SaveToleranceSetting",method:"post",data:e})}function x(e){return(0,r.default)({url:"/pro/Inspection/DeleteToleranceSetting",method:"post",data:e})}function T(e){return(0,r.default)({url:"/pro/Inspection/GetToleranceSettingList",method:"post",data:e})}},dc2b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"110px"}},[a("el-form-item",{attrs:{label:"变更单号",prop:"Bill_No"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No",t)},expression:"form.Bill_No"}})],1),a("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.typeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"创建时间",prop:"dateRange"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange,callback:function(t){e.$set(e.form,"dateRange",t)},expression:"form.dateRange"}})],1),a("el-form-item",{attrs:{label:"是否有变更任务",prop:"Is_Change_Model"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Is_Change_Model,callback:function(t){e.$set(e.form,"Is_Change_Model",t)},expression:"form.Is_Change_Model"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"primary",disabled:0===e.selectList.length},on:{click:function(t){return e.handleSub()}}},[e._v("批量提交")]),a("el-button",{attrs:{disabled:0===e.selectList.length},on:{click:function(t){return e.handleDel()}}},[e._v("批量删除")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Is_Change_Model",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Is_Change_Model?"是":"否"))])]}},{key:"op",fn:function(t){var n=t.row,r=t.index;return[a("div",[a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleSub(n.Id)}}},[e._v("提交")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleChange(n.Id,2)}}},[e._v("编辑")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleDel(n.Id)}}},[e._v("删除")])],1)]}}])})],1)],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"选择抄送人",visible:e.dialogVisible,width:"576px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("add-Cc",{ref:"addCc",on:{close:e.handleClose,refresh:e.fetchData}})],1)],1)},r=[]},f2ed:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530"));a("d81d"),a("14d9"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var o=n(a("0f97")),i=(a("d51a"),a("fd31"),n(a("641e"))),d=a("ed08"),l=n(a("7063")),c=(a("6186"),a("f4e9")),u=a("1b69"),s=a("7015f"),f=n(a("2082"));t.default={components:{DynamicDataTable:o.default,addCc:l.default},mixins:[i.default,f.default],data:function(){return{dialogVisible:!1,dialogVisible2:!1,loading:!1,form:{Bill_No:"",Moc_Type_Id:"",Sys_Project_Id:"",dateRange:"",Is_Change_Model:"",Is_Draft:!0,Type:"Draft",PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},addPageArray:[{path:this.$route.path+"/addChangeOrder",hidden:!0,component:function(){return Promise.all([a.e("chunk-2d0c91c4"),a.e("chunk-4c038d99")]).then(a.bind(null,"9609b"))},name:"PROAddChangeOrder",meta:{title:"变更单详情"}}],typeList:[],projectList:[],userList:[]}},created:function(){this.getBasicData(),this.getFactoryTypeOption("pro_change_apply_drafts_list")},mounted:function(){},methods:{getBasicData:function(){var e=this;(0,s.GetChangeType)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.typeList=t.Data.Data)})),(0,u.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)})),(0,c.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},fetchData:function(){var e=this;this.loading=!0;var t=(0,r.default)({},this.form);delete t["dateRange"],delete t["PageInfo"],t.Create_Begin=this.form.dateRange?(0,d.parseTime)(this.form.dateRange[0],"{y}-{m}-{d}"):"",t.Create_End=this.form.dateRange?(0,d.parseTime)(this.form.dateRange[1],"{y}-{m}-{d}"):"",(0,s.GetChangeOrderPageList)((0,r.default)((0,r.default)({},t),this.form.PageInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Create_Date=e.Create_Date?(0,d.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Create_Date,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).catch(console.error).finally((function(){e.loading=!1}))},datePickerwrapper:function(){},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},handleClose:function(){this.$refs.addCc.resetForm(),this.dialogVisible=!1},handleChange:function(e,t){var a={Id:e,type:t};this.$router.push({name:"PROAddChangeOrder",query:{pg_redirect:"PROChangeApply",data:a}})},handleCc:function(e){var t=this;this.dialogVisible=!0,this.$nextTick((function(){t.$refs.addCc.init(e)}))},handleSub:function(e){var t=this;this.$confirm("提交选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="";e||t.selectList.forEach((function(e){a+=e.Id+","})),(0,s.SubmitChangeOrder)({changeIds:e||a,webId:"changeFrom"}).then((function(e){e.IsSucceed?(t.$message({message:"提交成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleWithdraw:function(e){},handlePass:function(e){var t=this;this.$confirm("提交选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e||t.selectList.forEach((function(e){e.Id+","}))})).catch((function(){}))},handleDel:function(e){var t=this;this.$confirm("删除选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="";e||t.selectList.forEach((function(e){a+=e.Id+","})),(0,s.DeleteChangeOrder)({ids:e||a}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleExp:function(e){var t=this;this.$confirm("导出选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e||t.selectList.forEach((function(e){e.Id+","}))})).catch((function(){}))}}}},f4e9:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteQuotaList=s,t.DeleteSalary=d,t.ExportPlanSalary=m,t.GetFactoryPeoplelist=c,t.GetFactorySalaryPageList=l,t.GetPlanSalaryDetailList=g,t.GetPlanSalaryPageList=p,t.GetQuotaDetail=h,t.GetQuotaPageList=f,t.ImportSalaryFiles=o,t.SaveQuotaEntity=u,t.UpdateImportSalaryFile=i;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Factory/ImportSalaryFiles",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Factory/UpdateImportSalaryFile",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Factory/DeleteSalary",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Factory/GetFactorySalaryPageList",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ProductionSalary/SaveQuotaEntity",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ProductionSalary/DeleteQuotaList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetQuotaPageList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetQuotaDetail",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetPlanSalaryPageList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetPlanSalaryDetailList",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProductionSalary/ExportPlanSalary",method:"post",data:e})}}}]);