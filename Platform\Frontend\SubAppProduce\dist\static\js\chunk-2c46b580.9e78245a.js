(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2c46b580"],{"1bb2":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddApproach=st,e.AddBound=$,e.AddCheck=_t,e.AddComponentOperation=et,e.AddLocation=R,e.AddMaterial=_,e.AddMaterialType=c,e.AddSettlement=rt,e.AddVendor=O,e.AddWareHouse=P,e.DeleteApproach=dt,e.DeleteBound=V,e.DeleteCheck=Lt,e.DeleteLocation=T,e.DeleteMaterial=L,e.DeleteMaterialType=f,e.DeleteSettlement=it,e.DeleteVendor=B,e.DeleteWareHouse=G,e.EditApproach=ct,e.EditBound=U,e.EditCheck=yt,e.EditLocation=F,e.EditMaterial=y,e.EditMaterialType=d,e.EditSettlement=ot,e.EditVendor=x,e.EditWareHouse=b,e.ExportApproach=ht,e.ExportCheck=gt,e.ExportInBound=Pt,e.ExportOutBound=bt,e.ExportSettlement=ut,e.GetApproach=ft,e.GetBoundDetailList=H,e.GetBoundEntity=j,e.GetBoundPageList=W,e.GetCheckDetailList=kt,e.GetComponentLog=K,e.GetDetailEntity=Mt,e.GetDictionaryDetailListByCode=J,e.GetLocationEntity=S,e.GetLocationList=D,e.GetLocationPageList=v,e.GetLocationTree=I,e.GetMaterialInfoEntity=p,e.GetMaterialList=h,e.GetMaterialPageList=m,e.GetMaterialTypeEntity=s,e.GetMaterialTypeList=l,e.GetMaterialTypePageList=u,e.GetMaterialTypeTree=n,e.GetProfessionalType=Q,e.GetProjectsNodeList=z,e.GetProjectsNodebyType=Y,e.GetSettlement=nt,e.GetStockList=mt,e.GetStockPageList=q,e.GetStorageData=tt,e.GetStorageSteelData=Z,e.GetTraceData=X,e.GetTraceDetail=at,e.GetVendorEntity=N,e.GetVendorList=A,e.GetVendorPageList=w,e.GetWareHouseEntity=C,e.GetWareHouseList=g,e.GetWareHousePageList=M,e.GetWareHouseTree=E,e.GetWorking_ObjectList=i,e.ImportApproach=pt,e.ImportCheck=Ct,e.ImportMaterial=k,e.ImportSettlement=lt;var o=r(a("b775"));function i(t){return(0,o.default)({url:"/PLM/Plm_Projects/GetWorking_ObjectList",method:"post",data:t})}function n(){return(0,o.default)({url:"/PLM/MaterialType/GetTree",method:"post"})}function l(t){return(0,o.default)({url:"/PLM/MaterialType/GetList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PLM/MaterialType/GetPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PLM/MaterialType/GetEntity",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PLM/MaterialType/Add",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PLM/MaterialType/Edit",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PLM/MaterialType/Delete",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PLM/MaterialInfo/GetEntity",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PLM/MaterialInfo/GetList",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PLM/MaterialInfo/GetPageList",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PLM/MaterialInfo/Add",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PLM/MaterialInfo/Edit",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PLM/MaterialInfo/Delete",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PLM/MaterialInfo/ImportMaterial",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PLM/MaterialWareHouse/GetEntity",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PLM/MaterialWareHouse/GetList",method:"post",data:t})}function M(t){return(0,o.default)({url:"/PLM/MaterialWareHouse/GetPageList",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PLM/MaterialWareHouse/Add",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PLM/MaterialWareHouse/Edit",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PLM/MaterialWareHouse/Delete",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PLM/MaterialLocation/GetEntity",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PLM/MaterialLocation/GetPageList",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PLM/MaterialLocation/GetList",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PLM/MaterialLocation/GetAppTree",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PLM/MaterialLocation/Add",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PLM/MaterialLocation/Edit",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PLM/MaterialLocation/Delete",method:"post",data:t})}function E(){return(0,o.default)({url:"/PLM/MaterialLocation/GetTree",method:"post"})}function N(t){return(0,o.default)({url:"/PLM/MaterialVendor/GetEntity",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PLM/MaterialVendor/GetPageList",method:"post",data:t})}function A(){return(0,o.default)({url:"/PLM/MaterialVendor/GetList",method:"post"})}function O(t){return(0,o.default)({url:"/PLM/MaterialVendor/Add",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PLM/MaterialVendor/Edit",method:"post",data:t})}function B(t){return(0,o.default)({url:"/PLM/MaterialVendor/Delete",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PLM/MaterialBound/GetEntity",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PLM/MaterialBound/GetPageList",method:"post",data:t})}function $(t){return(0,o.default)({url:"/PLM/MaterialBound/Add",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PLM/MaterialBound/Edit",method:"post",data:t})}function V(t){return(0,o.default)({url:"/PLM/MaterialBound/Delete",method:"post",data:t})}function H(t){return(0,o.default)({url:"/PLM/MaterialBound/GetDetailList",method:"post",data:t})}function q(t){return(0,o.default)({url:"/PLM/MaterialBound/GetStockPageList",method:"post",data:t})}function J(t){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function Q(t){return(0,o.default)({url:"/PLM/Plm_Professional_Type/GetAllEntities",method:"post",data:t})}function Y(t){return(0,o.default)({url:"/PLM/Plm_Projects_Node/GetEntities",method:"post",data:t})}function z(t){return(0,o.default)({url:"/PLM/Plm_Projects_Node/GetNodeList",method:"post",data:t})}function K(t){return(0,o.default)({url:"/PLM/ComponentLog/GetComponentLog",method:"post",data:t})}function X(t){return(0,o.default)({url:"/PLM/ComponentLog/GetTraceData",method:"post",data:t})}function Z(t){return(0,o.default)({url:"/PLM/ComponentLog/GetStorageSteelData",method:"post",data:t})}function tt(t){return(0,o.default)({url:"/PLM/ComponentLog/GetStorageData",method:"post",data:t})}function et(t){return(0,o.default)({url:"/PRO/Component/AddComponentOperation",method:"post",data:t})}function at(t){return(0,o.default)({url:"/PLM/ComponentLog/GetTraceDetail",method:"post",data:t})}function rt(t){return(0,o.default)({url:"/PLM/MaterialRegister/Add",method:"post",data:t})}function ot(t){return(0,o.default)({url:"/PLM/MaterialRegister/Edit",method:"post",data:t})}function it(t){return(0,o.default)({url:"/PLM/MaterialRegister/Delete",method:"post",data:t})}function nt(t){return(0,o.default)({url:"/PLM/MaterialRegister/GetEntity",method:"post",data:t})}function lt(t){return(0,o.default)({url:"/PLM/MaterialRegister/ImportSettlement",method:"post",data:t})}function ut(t){return(0,o.default)({url:"/PLM/MaterialRegister/ExportSettlement",method:"post",data:t})}function st(t){return(0,o.default)({url:"/PLM/MaterialRegister/AddApproach",method:"post",data:t})}function ct(t){return(0,o.default)({url:"/PLM/MaterialRegister/EditApproach",method:"post",data:t})}function dt(t){return(0,o.default)({url:"/PLM/MaterialRegister/DeleteApproach",method:"post",data:t})}function ft(t){return(0,o.default)({url:"/PLM/MaterialRegister/GetApproachEntity",method:"post",data:t})}function pt(t){return(0,o.default)({url:"/PLM/MaterialRegister/ImportApproach",method:"post",data:t})}function ht(t){return(0,o.default)({url:"/PLM/MaterialRegister/ExportApproach",method:"post",data:t})}function mt(t){return(0,o.default)({url:"/PLM/MaterialCheck/GetStockList",method:"post",data:t})}function _t(t){return(0,o.default)({url:"/PLM/MaterialCheck/AddCheck",method:"post",data:t})}function yt(t){return(0,o.default)({url:"/PLM/MaterialCheck/EditCheck",method:"post",data:t})}function Lt(t){return(0,o.default)({url:"/PLM/MaterialCheck/DeleteCheck",method:"post",data:t})}function kt(t){return(0,o.default)({url:"/PLM/MaterialCheck/GetCheckDetailList",method:"post",data:t})}function Ct(t){return(0,o.default)({url:"/PLM/MaterialCheck/ImportCheck",method:"post",data:t})}function gt(t){return(0,o.default)({url:"/PLM/MaterialCheck/ExportCheck",method:"post",data:t})}function Mt(t){return(0,o.default)({url:"/PLM/MaterialBound/GetDetailEntity",method:"post",data:t})}function Pt(t){return(0,o.default)({url:"/PLM/MaterialBound/ExportInBound",method:"post",data:t})}function bt(t){return(0,o.default)({url:"/PLM/MaterialBound/ExportOutBound",method:"post",data:t})}},3653:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return o}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100"},[a("div",{staticClass:"app-container h100"},[a("div",{staticClass:"top-btn",on:{click:t.toBack}},[a("el-button",[t._v("返回")])],1),a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-top"},[a("div",{staticClass:"top-title"},[a("span",[t._v("项目名称："+t._s(t.form.Short_Name||"—"))])])]),a("div",{staticClass:"wrapper-main"},[a("div",{staticClass:"basic-information"},[a("header",[t._v("构件信息")]),a("el-form",{ref:"form",staticClass:"demo-form-inline",staticStyle:{"padding-left":"20px"},attrs:{inline:!0,model:t.form,rules:t.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"SteelName"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:t.form.SteelName,callback:function(e){t.$set(t.form,"SteelName",e)},expression:"form.SteelName"}})],1),a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-input",{attrs:{value:0==t.form.Check_Object_Type?"构件":"零件",type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Name"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:t.form.Check_Node_Name,callback:function(e){t.$set(t.form,"Check_Node_Name",e)},expression:"form.Check_Node_Name"}})],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-input",{attrs:{value:1==t.form.Check_Type?"质量":"探伤",type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"质检结果",prop:"Sheet_Result"}},[0!==t.CheckFeeback.length||t.isCheck?a("el-input",{attrs:{value:t.form.Sheet_Result,type:"text",disabled:""}}):a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.Sheet_Result,callback:function(e){t.$set(t.form,"Sheet_Result",e)},expression:"form.Sheet_Result"}},[a("el-option",{attrs:{label:"合格",value:"合格"}}),a("el-option",{attrs:{label:"不合格",value:"不合格"}})],1)],1),"合格"!==t.form.Sheet_Result?a("el-form-item",{attrs:{label:"整改时限",prop:"Rectify_Date"}},[t.isCheck?a("el-input",{attrs:{value:t.form.Rectify_Date,type:"text",disabled:""}}):a("el-date-picker",{attrs:{align:"right",type:"date",placeholder:"选择日期"},model:{value:t.form.Rectify_Date,callback:function(e){t.$set(t.form,"Rectify_Date",e)},expression:"form.Rectify_Date"}})],1):t._e(),"合格"!==t.form.Sheet_Result?a("el-form-item",{attrs:{label:"整改人",prop:"Rectifier_Id"}},[t.isCheck?a("el-input",{attrs:{value:t.form.Rectifier_Name,type:"text",disabled:""}}):a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.Rectifier_Id,callback:function(e){t.$set(t.form,"Rectifier_Id",e)},expression:"form.Rectifier_Id"}},t._l(t.userList,(function(t){return a("el-option",{key:t.Id,attrs:{value:t.Id,label:t.Display_Name}})})),1)],1):t._e(),"合格"!==t.form.Sheet_Result?a("el-form-item",{attrs:{label:"参与人",prop:"Participant_Id"}},[t.isCheck?a("el-input",{attrs:{value:t.form.Participant_Name,type:"text",disabled:""}}):a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:t.changeCategory},model:{value:t.form.Participant_Id,callback:function(e){t.$set(t.form,"Participant_Id",e)},expression:"form.Participant_Id"}},t._l(t.userList,(function(t){return a("el-option",{key:t.Id,attrs:{value:t.Id,label:t.Display_Name}})})),1)],1):t._e()],1)],1),a("div",{staticClass:"inspection-type"},[a("header",[t._v("检查类型")]),0!==t.CheckFeeback.length?a("div",{staticClass:"radio-items"},t._l(t.CheckFeeback,(function(e){return a("div",{key:e.CheckId,staticClass:"radio-item"},[a("span",[t._v(t._s(e.CheckName))]),a("el-radio-group",{attrs:{disabled:t.isCheck},on:{change:t.changePass},model:{value:e.isPass,callback:function(a){t.$set(e,"isPass",a)},expression:"item.isPass"}},[a("el-radio",{attrs:{label:!0}},[t._v("合格")]),a("el-radio",{attrs:{label:!1}},[t._v("不合格")])],1)],1)})),0):a("div",{staticClass:"no-radio-items"},[t._v("暂无检查类型")])]),0==t.form.Check_Object_Type?a("div",{staticClass:"detailed-drawings"},[a("header",[t._v("深化图纸")]),0!==t.steelCadList.length?a("div",{staticClass:"deep-img"},t._l(t.steelCadList,(function(e,r){return a("div",{key:r,staticClass:"dwg_ico",on:{click:function(a){return t.openDwg(e.url)}}})})),0):a("div",{staticClass:"font-cad"},[t._v("无cad")])]):t._e(),a("div",{staticClass:"inspection-description"},[a("header",[t._v("质检描述")]),a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",rows:4,placeholder:"请输入内容",disabled:t.isCheck},model:{value:t.form.Rectify_Description,callback:function(e){t.$set(t.form,"Rectify_Description",e)},expression:"form.Rectify_Description"}})],1),"合格"!==t.form.Sheet_Result?a("div",{staticClass:"rectification"},[a("header",[t._v("整改问题")]),a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",rows:4,placeholder:"请输入内容",disabled:t.isCheck},model:{value:t.form.Suggestion,callback:function(e){t.$set(t.form,"Suggestion",e)},expression:"form.Suggestion"}})],1):t._e(),a("div",{staticClass:"img-up"},[a("header",[t._v("图片上传")]),t.isCheck?a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.fileList,border:""}},[a("el-table-column",{attrs:{prop:"name",label:"文件名",width:"300"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("div",{staticStyle:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},[t._v(t._s(r.name||"-"))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"83",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.handlePreview(r)}}},[t._v("下载")])]}}])})],1)],1):a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:"image/*","file-list":t.fileList,multiple:"",limit:5,"on-success":function(e,a,r){t.uploadSuccess(e,a,r)},"on-remove":t.uploadRemove,"on-preview":t.handlePreview,"on-exceed":t.uploadExceed,disabled:t.isCheck}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v(" 将文件拖到此处，或"),a("em",[t._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("文件上传数量最多为5个")])])],1),a("div",{staticClass:"check-items"},[a("header",[t._v("检查项")]),0!==t.CheckFeeback.length?t._l(t.CheckFeeback,(function(e){return a("div",{key:e.CheckId,staticClass:"check-table"},[a("div",{staticClass:"check-table-title"},[t._v(" 检查类型："+t._s(e.CheckName)+" ")]),a("el-table",{staticStyle:{width:"50%"},attrs:{data:e.Check_Item,stripe:""}},[a("el-table-column",{attrs:{prop:"Check_Content",label:"检查项",width:"180"}}),a("el-table-column",{attrs:{prop:"Eligibility_Criteria",label:"合格标准",width:"180"}}),a("el-table-column",{attrs:{prop:"Actual_Measurement",label:"检查内容"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("div",[a("el-input",{staticStyle:{width:"80%",border:"1px solid #eee","border-radius":"4px"},attrs:{disabled:t.isCheck,type:"text"},on:{blur:function(e){t.inputBlur(e,r.Actual_Measurement,r)}},model:{value:r.Actual_Measurement,callback:function(e){t.$set(r,"Actual_Measurement",e)},expression:"row.Actual_Measurement"}})],1)]}}],null,!0)})],1)],1)})):a("div",{staticClass:"no-check-table"},[t._v("暂无检查项")])],2)]),t.isCheck?t._e():a("div",{staticClass:"submit-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("提交")])],1)])])])},o=[]},3760:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=r(a("2909"));a("99af"),a("7db0"),a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("d866"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("25f0"),a("159b");a("1bb2"),a("ac26");var i=a("d51a"),n=r(a("bbc2")),l=a("ed08");e.default={name:"PROQualityManagement",components:{OSSUpload:n.default},data:function(){return{form:{SteelName:"",Check_Object_Type:"",Check_Object_Type_Id:"",Check_Node_Id:"",Check_Node_Name:"",Check_Type:"",Sheet_Result:"合格",Rectifier_Id:"",Rectifier_Name:"",Rectify_Date:"",Participant_Id:"",Participant_Name:"",Participant_Ids:[],Rectify_Description:"",Suggestion:"",dateValue:"",Short_Name:""},rules:{SteelName:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Check_Node_Name:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Check_Object_Type:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Check_Type:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Sheet_Result:[{required:!0,message:"请选择",trigger:"change"}],Rectifier_Id:[{required:!0,message:"请选择",trigger:"change"}],dateValue:[{required:!0,message:"请选择",trigger:"change"}]},comType:[],form2:{},CheckFeeback:[],textarea1:"",textarea2:"",fileList:[],Attachments:[],steelCadList:[],isEdit:!0,checkDateList:[],Group_Ids:[],isAllPass:!0,isCheck:!1}},created:function(){this.isCheck="true"===this.$route.query.isCheck.toString(),this.getFactoryPeoplelist()},methods:{getFactoryPeoplelist:function(){var t=this;(0,i.GetFactoryPeoplelist)().then((function(e){e.IsSucceed&&(t.userList=e.Data,t.getEntityQualityManagement(t.$route.query.sheetId))}))},getEntityQualityManagement:function(t){var e=this;(0,i.EntityQualityManagement)({sheetid:t}).then((function(t){if(t.IsSucceed){if(e.isCheck){if(e.checkDateList=t.Data,e.form.Short_Name=e.checkDateList.Short_Name,e.form.SteelName=e.checkDateList.SteelName,e.form.Check_Object_Type=e.checkDateList.Check_Object_Type,e.form.Check_Object_Type_Id=e.checkDateList.Check_Object_Type_Id,e.form.Check_Node_Id=e.checkDateList.Check_Node_Id,e.form.Check_Node_Name=e.checkDateList.Check_Node_Name,e.form.Check_Type=e.checkDateList.Check_Type,e.form.Sheet_Result=e.checkDateList.Sheet_Result,e.form.Rectifier_Id=e.checkDateList.Rectifier_Id,e.checkDateList.Rectify_Date?e.form.Rectify_Date=(0,l.parseTime)(new Date(e.checkDateList.Rectify_Date),"{y}-{m}-{d}"):e.form.Rectify_Date="",e.form.Participant_Ids=e.checkDateList.Participant_Ids,e.form.Rectify_Description=e.checkDateList.Rectify_Description,e.form.Suggestion=e.checkDateList.Suggestion,e.CheckFeeback=JSON.parse(e.checkDateList.Check_Type_Result_Json),"不合格"===e.form.Sheet_Result){var a=e.userList.find((function(t){return t.Id===e.form.Rectifier_Id}));e.form.Rectifier_Name=a.Display_Name;var r=e.userList.find((function(t){return t.Id===e.form.Participant_Ids[0]}));e.form.Participant_Name=r.Display_Name}0!=e.checkDateList.Attachments.length&&e.checkDateList.Attachments.map((function(t){var a={name:"",url:""};a.name=t.File_Name,a.url=t.File_Url,e.fileList.push(a)}))}else e.checkDateList=t.Data,e.form.Short_Name=e.checkDateList.Short_Name,e.form.SteelName=e.checkDateList.SteelName,e.form.Check_Object_Type=e.checkDateList.Check_Object_Type,e.form.Check_Object_Type_Id=e.checkDateList.Check_Object_Type_Id,e.form.Check_Node_Id=e.checkDateList.Check_Node_Id,e.form.Check_Node_Name=e.checkDateList.Check_Node_Name,e.form.Check_Type=e.checkDateList.Check_Type,e.Group_Ids=e.checkDateList.Group_Ids,0!==t.Data.CheckFeebacks.length&&(e.CheckFeeback=t.Data.CheckFeebacks.map((function(t){return t.isPass=!0,0!==t.Check_Item.length&&t.Check_Item.map((function(e){e.Actual_Measurement="",e.Check_Type_Id=t.CheckId})),t}))),e.CheckFeeback=JSON.parse(JSON.stringify(e.CheckFeeback));0==e.form.Check_Object_Type&&e.getSheetDwg(e.$route.query.sheetId)}else e.$message({message:t.Message,type:"error"})}))},getEditById:function(t){var e=this;(0,i.GetEditById)({sheetId:t}).then((function(t){t.IsSucceed||e.$message({message:t.Message,type:"error"})}))},changePass:function(t){var e=this.CheckFeeback.every((function(t){return t.isPass}));this.form.Sheet_Result=e?"合格":"不合格"},changeCategory:function(t){t&&this.form.Participant_Ids.push(t)},uploadSuccess:function(t,e,a){var r=this;this.Attachments=[],a.map((function(t){var e={File_Name:"",File_Url:""};e.File_Name=t.name,t.hasOwnProperty("response")?e.File_Url=t.response.encryptionUrl:e.File_Url=t.url,r.Attachments.push(e)}))},uploadExceed:function(t,e){this.$message({type:"warning",message:"已超过文件上传最大数量"})},uploadRemove:function(t,e){var a=this;this.Attachments=[],e.map((function(t){var e={File_Name:"",File_Url:""};e.File_Name=t.name,t.hasOwnProperty("response")?e.File_Url=t.response.encryptionUrl:e.File_Url=t.url,a.Attachments.push(e)}))},handlePreview:function(t){var e=null;e=t.hasOwnProperty("response")?t.response.encryptionUrl:t.url,window.open(e,"_blank")},inputBlur:function(t,e,a){},getSheetDwg:function(t){var e=this;(0,i.GetSheetDwg)({id:t}).then((function(t){t.IsSucceed&&(e.steelCadList=t.Data||[])}))},openDwg:function(t){var e=t.lastIndexOf("."),a=t.lastIndexOf("?"),r=t.substring(e,a);if(".pdf"===r)window.open(t+"#toolbar=0","_blank");else{var o=t.split(".com/");o[1]=encodeURIComponent(o[1]),t=o.join(".com/"),window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+t,"_blank")}},submit:function(){var t=this,e=this.form,a=e.SteelName,r=e.Check_Object_Type,n=e.Check_Node_Id,u=e.Check_Type,s=e.Sheet_Result,c=e.Check_Object_Type_Id,d=e.Rectify_Date,f=e.Rectifier_Id,p=e.Participant_Ids,h=e.Rectify_Description,m=e.Suggestion,_={Sheets:[{SteelName:a,Check_Object_Type:r,Check_Object_Type_Id:c,Check_Node_Id:n,Check_Type:u,Sheet_Result:s,Check_Type_Result_Json:JSON.stringify(this.CheckFeeback),SheetId:this.$route.query.sheetId,Rectify_Date:d?(0,l.parseTime)(d,"{y}-{m}-{d}"):"",Rectifier_Id:f,Participant_Ids:p,Rectify_Description:h,Suggestion:m,Group_Ids:this.Group_Ids||[]}],_Attachments:this.Attachments,plm_Factory_Check:[]},y=[];this.CheckFeeback.map((function(t){y=[].concat((0,o.default)(y),(0,o.default)(t.Check_Item))})),y.forEach((function(t){var e=t.Id,a=t.Check_Content,r=t.Eligibility_Criteria,o=t.Actual_Measurement,i=t.Check_Type_Id,n={Item_Id:e||"",Check_Name:a||"",Eligibility_Criteria:r||"",Actual_Measurement:o||"",Check_Type_Id:i||""};_.plm_Factory_Check.push(n)})),(0,i.SaveTesting)(_).then((function(e){e.IsSucceed?(t.$message({message:"质检成功",type:"success"}),(0,l.closeTagView)(t.$store,t.$route)):t.$message({message:e.Message,type:"error"})}))},toBack:function(){(0,l.closeTagView)(this.$store,this.$route)}}}},"79ab":function(t,e,a){"use strict";a.r(e);var r=a("3653"),o=a("df76");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("b56b");var n=a("2877"),l=Object(n["a"])(o["default"],r["a"],r["b"],!1,null,"74fcfb26",null);e["default"]=l.exports},81349:function(t,e,a){},ac26:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetFactoryEntity=et,e.GetFactoryFileEntities=j,e.GetFactoryOutlineList=B,e.GetLately30CheckCount=H,e.GetLately30CheckRate=q,e.GetLately30CheckWeight=V,e.GetMonthWeightData=$,e.GetPersonalCheckCountRank=Q,e.GetProcessListBase=Z,e.GetProjectsList=A,e.GetQuestionRank=U,e.GetSysProfessionalByCode=X,e.GetTeamGroupRank=J,e.GetTypeList=K,e.GetWarehouseCapacity=rt,e.GetWorkingTeamBase=tt,e.GetYearTotalData=W,e.SearchComponentPageList=at,e.addClassification=f,e.addFactoryCheck=S,e.addFactorySettingGroup=u,e.deleteClassification=h,e.deleteFactoryCheck=D,e.deleteFactorySettingGroup=c,e.editClassification=p,e.editFactoryCheck=v,e.editFactorySettingGroup=s,e.exportManageList=z,e.getAllProcessList=O,e.getCategoryList=n,e.getClassificationEntity=m,e.getClassificationList=k,e.getComponentTypeList=l,e.getDwgPaint=b,e.getFactoryAreaEntities=N,e.getFactoryCheckEntity=G,e.getFactoryList=i,e.getFactorySteelList=T,e.getFactoryWorkingTeam=x,e.getGroupEntity=d,e.getGroupItemsList=C,e.getProjectsByPlatform=w,e.getProjectsNodeList=E,e.getRelatedUser=y,e.getSheetDwg=g,e.getSheetDwgById=P,e.getSheetDwgByName=M,e.getSheetDwgList=Y,e.getUserList=_,e.gtComponentList=L,e.multiAddFactoryCheck=F,e.replyFactoryCheck=R,e.submitFactoryCheck=I;var o=r(a("b775"));function i(t){return(0,o.default)({url:"/PLM/FactorySetting/GetAuthFactoryList",method:"post",data:t})}function n(t){return(0,o.default)({url:"/PLM/FactorySetting/GetCategoryList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PLM/FactorySetting/AddGroup",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PLM/FactorySetting/EditGroup",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PLM/FactorySetting/DeleteGroup",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PLM/FactorySetting/GetGroupEntity",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PLM/FactorySetting/AddClassification",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PLM/FactorySetting/EditClassification",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PLM/FactorySetting/DeleteClassification",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PLM/FactorySetting/GetClassificationEntity",method:"post",data:t})}function _(t){return(0,o.default)({url:"/SYS/User/GetUserList",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PLM/FactorySetting/GetRelatedUser",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/Component/GetComponentList",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PLM/FactorySetting/GetClassificationList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PLM/FactorySetting/GetGroupItemsList",method:"post",data:t})}function g(t){return(0,o.default)({url:"PLM/FactoryCheck/GetSheetDwg",method:"post",data:t})}function M(t){return(0,o.default)({url:"/PLM/FactoryCheck/GetSheetDwgByName",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PLM/FactoryCheck/GetSheetDwg",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PLM/FactoryCheck/GetDwgPaint",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PLM/FactoryCheck/GetEntity",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PLM/FactoryCheck/Add",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PLM/FactoryCheck/Edit",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PLM/FactoryCheck/Delete",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PLM/FactoryCheck/Submit",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PLM/FactoryCheck/Reply",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PLM/FactoryCheck/MultiAdd",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PLM/FactorySetting/GetSteelList",method:"post",data:t})}function E(t){return(0,o.default)({url:"/plm/plm_projects_node/GetEntities",method:"post",data:t})}function N(t){return(0,o.default)({url:"/PLM/FactorySetting/GetAreaEntities",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PLM/QSCheck/GetProjectsByPlatform",method:"post",data:t})}function A(t){return(0,o.default)({url:"/PLM/FactorySetting/GetProjectsList",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PLM/FactorySetting/GetProcessList",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:t})}function B(t){return(0,o.default)({url:"/PLM/FactoryReport/GetOutlineList",method:"post",data:t})}function j(t){return(0,o.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PLM/FactoryReport/GetYearTotalData",method:"post",data:t})}function $(t){return(0,o.default)({url:"/PLM/FactoryReport/GetMonthWeightData",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PLM/FactoryReport/GetQuestionRank",method:"post",data:t})}function V(t){return(0,o.default)({url:"/PLM/FactoryReport/GetLately30CheckWeight",method:"post",data:t})}function H(t){return(0,o.default)({url:"/PLM/FactoryReport/GetLately30CheckCount",method:"post",data:t})}function q(t){return(0,o.default)({url:"/PLM/FactoryReport/GetLately30CheckRate",method:"post",data:t})}function J(t){return(0,o.default)({url:"/PLM/FactoryReport/GetTeamGroupRank",method:"post",data:t})}function Q(t){return(0,o.default)({url:"/PLM/FactoryReport/GetPersonalCheckCountRank",method:"post",data:t})}function Y(t){return(0,o.default)({url:"/PLM/FactoryCheck/GetSheetDwg",method:"post",data:t})}function z(t){return(0,o.default)({url:"/PLM/FactoryReport/ExportManageList",method:"post",data:t})}function K(t){return(0,o.default)({url:"/PLM/Plm_Professional_Type/GetTypeList",method:"post",data:t})}function X(t){return(0,o.default)({url:"/Plm/Plm_Professional_Type/GetSysProfessionalByCode",method:"post",data:t})}function Z(t){return(0,o.default)({url:"/Pro/TechnologyLib/GetProcessListBase",method:"post",data:t})}function tt(t){return(0,o.default)({url:"/Pro/TechnologyLib/GetWorkingTeamBase",method:"post",data:t})}function et(t){return(0,o.default)({url:"/Pro/Factory/GetFactoryEntity",method:"post",data:t})}function at(t){return(0,o.default)({url:"/PRO/Component/SearchCheckComponentPageList",method:"post",data:t})}function rt(t){return(0,o.default)({url:"/PRO/Component/GetWarehouseCapacity",method:"post",data:t})}},b56b:function(t,e,a){"use strict";a("81349")},df76:function(t,e,a){"use strict";a.r(e);var r=a("3760"),o=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=o.a}}]);