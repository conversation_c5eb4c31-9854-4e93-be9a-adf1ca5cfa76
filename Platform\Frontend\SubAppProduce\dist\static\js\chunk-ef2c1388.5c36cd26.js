(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ef2c1388"],{"1122c":function(t,e,n){"use strict";n.d(e,"a",(function(){return u})),n.d(e,"b",(function(){return a}));var u=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("detail",{attrs:{"is-edit":!0}})},a=[]},3572:function(t,e,n){"use strict";n.r(e);var u=n("8c4a"),a=n.n(u);for(var r in u)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(r);e["default"]=a.a},"4a91":function(t,e,n){"use strict";n.r(e);var u=n("1122c"),a=n("3572");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);var c=n("2877"),f=Object(c["a"])(a["default"],u["a"],u["b"],!1,null,"1b1be52a",null);e["default"]=f.exports},"8c4a":function(t,e,n){"use strict";var u=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=u(n("74f4"));e.default={components:{detail:a.default},data:function(){return{}}}}}]);