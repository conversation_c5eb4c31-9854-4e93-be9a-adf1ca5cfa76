(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-fbaf68dc"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=o,Math.easeInOutQuad=function(t,e,a,r){return t/=r/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=i(),u=t-o,l=20,s=0;e="undefined"===typeof e?500:e;var c=function(){s+=l;var t=Math.easeInOutQuad(s,o,u,e);n(t),s<e?r(c):a&&"function"===typeof a&&a()};c()}},1309:function(t,e,a){},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186"),n=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,r.GetGridByCode)({code:t,IsAll:a}).then((function(t){var r=t.IsSucceed,o=t.Data,u=t.Message;if(r){if(!o)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,o.Grid),l=a?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+o.Grid.Row_Number||n.tablePageSize[0]),i(e.columns)}else e.$message({message:u,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,r=t.type;this.queryInfo.Page="limit"===r?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===e){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},2245:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ActiveAuxMaterial=S,e.ActiveRawMaterial=y,e.DeleteAuxMaterial=_,e.DeleteMaterialCategory=d,e.DeleteMaterials=s,e.DeleteRawMaterial=R,e.DeleteWarehouseReceipt=I,e.ExportPurchaseDetail=q,e.GetAuxMaterialEntity=b,e.GetAuxMaterialPageList=g,e.GetAuxStandardsList=E,e.GetAuxWarehouseReceiptEntity=A,e.GetMaterialCategoryList=c,e.GetMaterialImportPageList=o,e.GetPurchaseDetail=L,e.GetPurchaseDetailList=W,e.GetRawMaterialEntity=m,e.GetRawMaterialPageList=h,e.GetRawStandardsList=D,e.GetRawWarehouseReceiptEntity=C,e.GetWarehouseReceiptPageList=O,e.ImportMatAux=P,e.ImportMatAuxRcpt=$,e.ImportMatRaw=M,e.ImportMatRawRcpt=G,e.ImportMaterial=l,e.MaterialDataTemplate=u,e.SaveAuxMaterialEntity=w,e.SaveAuxWarehouseReceipt=T,e.SaveMaterialCategory=f,e.SaveRawMaterialEntity=p,e.SaveRawWarehouseReceipt=x,e.SubmitWarehouseReceipt=k,e.TemplateDownload=v;var n=r(a("b775")),i=r(a("4328"));function o(t){return(0,n.default)({url:"/PRO/Material/GetMaterialImportPageList",method:"post",data:t})}function u(){return(0,n.default)({url:"/PRO/Material/MaterialDataTemplate",method:"post"})}function l(t){return(0,n.default)({url:"/PRO/Material/ImportMaterial",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/Material/DeleteMaterials",method:"post",data:i.default.stringify(t)})}function c(t){return(0,n.default)({url:"/PRO/Material/GetMaterialCategoryList",method:"post",params:t})}function d(t){return(0,n.default)({url:"/PRO/Material/DeleteMaterialCategory",method:"post",data:t})}function f(t){return(0,n.default)({url:"/PRO/Material/SaveMaterialCategory",method:"post",data:t})}function p(t){return(0,n.default)({url:"/PRO/Material/SaveRawMaterialEntity",method:"post",data:t})}function m(t){return(0,n.default)({url:"/PRO/Material/GetRawMaterialEntity",method:"post",data:t})}function h(t){return(0,n.default)({url:"/PRO/Material/GetRawMaterialPageList",method:"post",data:t})}function y(t){return(0,n.default)({url:"/PRO/Material/ActiveRawMaterial",method:"post",data:t})}function R(t){return(0,n.default)({url:"/PRO/Material/DeleteRawMaterial",method:"post",data:t})}function g(t){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialPageList",method:"post",data:t})}function b(t){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialEntity",method:"post",data:t})}function v(t){return(0,n.default)({url:"/PRO/Material/TemplateDownload",method:"post",data:t})}function M(t){return(0,n.default)({url:"/PRO/Material/ImportMatRaw",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/Material/ImportMatAux",method:"post",data:t})}function w(t){return(0,n.default)({url:"/PRO/Material/SaveAuxMaterialEntity",method:"post",data:t})}function S(t){return(0,n.default)({url:"/PRO/Material/ActiveAuxMaterial",method:"post",data:t})}function _(t){return(0,n.default)({url:"/PRO/Material/DeleteAuxMaterial",method:"post",data:t})}function O(t){return(0,n.default)({url:"/PRO/Material/GetWarehouseReceiptPageList",method:"post",data:t})}function D(t){return(0,n.default)({url:"/PRO/Material/GetRawStandardsList",method:"post",data:t})}function x(t){return(0,n.default)({url:"/PRO/Material/SaveRawWarehouseReceipt",method:"post",data:t})}function I(t){return(0,n.default)({url:"/PRO/Material/DeleteWarehouseReceipt",method:"post",data:t})}function C(t){return(0,n.default)({url:"/PRO/Material/GetRawWarehouseReceiptEntity",method:"post",data:t})}function k(t){return(0,n.default)({url:"/PRO/Material/SubmitWarehouseReceipt",method:"post",data:t})}function A(t){return(0,n.default)({url:"/PRO/Material/GetAuxWarehouseReceiptEntity",method:"post",data:t})}function T(t){return(0,n.default)({url:"/PRO/Material/SaveAuxWarehouseReceipt",method:"post",data:t})}function E(t){return(0,n.default)({url:"/PRO/Material/GetAuxStandardsList",method:"post",data:t})}function G(t){return(0,n.default)({url:"/PRO/Material/ImportMatRawRcpt",method:"post",data:t})}function $(t){return(0,n.default)({url:"/PRO/Material/ImportMatAuxRcpt",method:"post",data:t})}function L(t){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetail",method:"post",data:t})}function q(t){return(0,n.default)({url:"/PRO/Material/ExportPurchaseDetail",method:"post",data:t})}function W(t){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetailList",method:"post",data:t})}},7258:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("5530")),i=r(a("c14f")),o=r(a("1da1"));a("14d9"),a("b0c0"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("ddb0");var u=a("ed08"),l=r(a("15ac")),s=r(a("333d")),c=a("c685"),d=r(a("2082")),f=a("2245");e.default={name:"PRORawMaterialReceipt",components:{Pagination:s.default},mixins:[l.default,d.default],data:function(){return{addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-72cd76c0"),a.e("chunk-254f2670"),a.e("chunk-8d9667fc")]).then(a.bind(null,"4f04"))},name:"PRORawMaterialReceiptAdd",meta:{title:"新建入库单"}},{path:this.$route.path+"/edit/:id",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-72cd76c0"),a.e("chunk-254f2670"),a.e("chunk-71df6e12")]).then(a.bind(null,"5c5b"))},name:"PRORawMaterialReceiptEdit",meta:{title:"编辑入库单"}},{path:this.$route.path+"/view/:id",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-72cd76c0"),a.e("chunk-254f2670"),a.e("chunk-63fc0642")]).then(a.bind(null,"491b"))},name:"PRORawMaterialReceiptView",meta:{title:"查看入库单"}}],tablePageSize:c.tablePageSize,tbLoading:!1,columns:[],tbData:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1,PageSize:c.tablePageSize[0]},total:0,form:{ReceiptNumber:"",EntryType:"",EntryDate:"",Status:void 0,Type:0},rules:{},search:function(){return{}}}},mounted:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return t.search=(0,u.debounce)(t.fetchData,800,!0),e.n=1,t.getTableConfig("PRORawMaterialReceiptList");case 1:t.fetchData();case 2:return e.a(2)}}),e)})))()},activated:function(){this.fetchData(1)},methods:{fetchData:function(t){var e=this;t&&(this.queryInfo.Page=t),(0,f.GetWarehouseReceiptPageList)((0,n.default)({PageInfo:this.queryInfo},this.form)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).finally((function(t){e.multipleSelection=[],e.tbLoading=!1}))},handleAdd:function(){this.$router.push({name:"PRORawMaterialReceiptAdd",query:{pg_redirect:this.$route.name}})},handleReset:function(){this.$refs["form"].resetFields(),this.search(1)},handleView:function(t){this.$router.push({name:"PRORawMaterialReceiptView",query:{pg_redirect:this.$route.name},params:{id:t.Id}})},tbSelectChange:function(t){this.multipleSelection=t.records},handleDetail:function(t){},handleDelete:function(t){var e=this;this.$confirm("是否删除该入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DeleteWarehouseReceipt)({id:[t.Id]}).then((function(t){t.IsSucceed?(e.$message({message:"删除成功",type:"success"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(t){this.$router.push({name:"PRORawMaterialReceiptEdit",query:{pg_redirect:this.$route.name},params:{id:t.Id}})},handleSubmit:function(t){var e=this;this.$confirm("确认提交入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.SubmitWarehouseReceipt)({id:[t.Id]}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"提交成功!"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}},af4c:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card h100"},[a("el-form",{ref:"form",attrs:{inline:"",model:t.form,rules:t.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"单据编号",prop:"ReceiptNumber"}},[a("el-input",{attrs:{clearable:""},model:{value:t.form.ReceiptNumber,callback:function(e){t.$set(t.form,"ReceiptNumber",e)},expression:"form.ReceiptNumber"}})],1),a("el-form-item",{attrs:{label:"入库类型",prop:"EntryType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.EntryType,callback:function(e){t.$set(t.form,"EntryType",e)},expression:"form.EntryType"}},[a("el-option",{attrs:{label:"手动入库",value:0}}),a("el-option",{attrs:{label:"退料入库",value:1}}),a("el-option",{attrs:{label:"采购入库",value:2}})],1)],1),a("el-form-item",{attrs:{label:"入库日期",prop:"EntryDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"date"},model:{value:t.form.EntryDate,callback:function(e){t.$set(t.form,"EntryDate",e)},expression:"form.EntryDate"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"Status"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Status,callback:function(e){t.$set(t.form,"Status",e)},expression:"form.Status"}},[a("el-option",{attrs:{label:"草稿",value:0}}),a("el-option",{attrs:{label:"已提交",value:1}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.search(1)}}},[t._v("搜索")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider",{staticClass:"elDivder"}),a("vxe-toolbar",{scopedSlots:t._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新增入库单")])]},proxy:!0}])}),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:t.tbLoading,stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,"min-width":e.Width,sortable:""},scopedSlots:t._u(["EntryDate"===e.Code?{key:"default",fn:function(a){var r=a.row;return[t._v(" "+t._s(t._f("timeFormat")(r[e.Code],"{y}-{m}-{d}"))+" ")]}}:"CreateDate"===e.Code?{key:"default",fn:function(a){var r=a.row;return[t._v(" "+t._s(t._f("timeFormat")(r[e.Code],"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}:"Status"===e.Code?{key:"default",fn:function(e){var r=e.row;return[0===r.Status?a("el-tag",{attrs:{type:"danger"}},[t._v("草稿")]):t._e(),1===r.Status?a("el-tag",{attrs:{type:"success"}},[t._v("已提交")]):t._e()]}}:"EntryType"===e.Code?{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(0===a.EntryType?"手动入库":1===a.EntryType?"退料入库":"采购入库")+" ")]}}:{key:"default",fn:function(a){var r=a.row;return[t._v(" "+t._s(t._f("displayValue")(r[e.Code]))+" ")]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleView(r)}}},[t._v("查看")]),0===r.Status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleSubmit(r)}}},[t._v("提交")]):t._e(),0===r.Status?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleEdit(r)}}},[t._v("编辑")]):t._e(),0===r.Status?a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(e){return t.handleDelete(r)}}},[t._v("删除")]):t._e()]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)],1)],1)},n=[]},cd9d:function(t,e,a){"use strict";a("1309")},e2b2:function(t,e,a){"use strict";a.r(e);var r=a("af4c"),n=a("f830");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("cd9d");var o=a("2877"),u=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"4fff08ec",null);e["default"]=u.exports},f830:function(t,e,a){"use strict";a.r(e);var r=a("7258"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a}}]);