(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2ed4fa78"],{"04f7c":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("2909"));a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("159b"),a("ddb0");var r=n(a("15ac")),i=a("a024"),s=a("8ab4");e.default={name:"Consume",mixins:[r.default],props:{processList:{type:Array,default:function(){return[]}}},data:function(){return{tbLoading:!1,tbData:[],columns:[{Code:"Part_Code",Display_Name:"零件名称"},{Code:"Working_Team_Id",Display_Name:"消耗班组"},{Code:"Stock_Count",Display_Name:"可消耗数量"},{Code:"Use_Count",Display_Name:"消耗数量"}],queryInfo:{PageSize:-1},workTeamsOption:[],Working_Team_Id:"",btnLoading:!1}},computed:{},created:function(){this.getWorkingTeams()},methods:{init:function(t){var e=this;this.tbData=t.map((function(t){return e.$set(t,"Working_Team_Id",""),e.$set(t,"Use_Count",0),t}))},changeAllWorkingTeam:function(t){this.tbData.forEach((function(e){e.Working_Team_Id=t}))},changeSingleWorkingTeam:function(t){var e=(0,o.default)(new Set(this.tbData.map((function(t){return t.Working_Team_Id}))));1===e.length?this.Working_Team_Id=e[0]:this.Working_Team_Id=""},getWorkingTeams:function(){var t=this;(0,i.GetWorkingTeams)({type:1}).then((function(e){e.IsSucceed?t.workTeamsOption=e.Data:t.$message({message:e.Message,type:"error"})}))},confirm:function(){var t=this,e=this.tbData.map((function(t){var e={Part_Aggregate_Id:t.Part_Aggregate_Id,Working_Team_Id:t.Working_Team_Id,Use_Count:t.Use_Count};return e})).filter((function(t){return t.Use_Count>0&&t.Working_Team_Id}));0!==e.length?(this.btnLoading=!0,(0,s.AggregatePartUse)(e).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"领用成功"}),t.$emit("refresh"),t.$emit("close")):t.$message({message:e.Message,type:"error"})})).finally((function(){t.btnLoading=!1}))):this.$message({type:"info",message:"请至少填写完成一条数据"})},cancel:function(){this.$emit("close")}}}},"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,a){var i=r(),s=t-i,l=20,u=0;e="undefined"===typeof e?500:e;var c=function(){u+=l;var t=Math.easeInOutQuad(u,i,s,e);o(t),u<e?n(c):a&&"function"===typeof a&&a()};c()}},"0bf5b":function(t,e,a){},1276:function(t,e,a){"use strict";var n=a("c65b"),o=a("e330"),r=a("d784"),i=a("825a"),s=a("861d"),l=a("1d80"),u=a("4840"),c=a("8aa5"),d=a("50c4"),f=a("577e"),m=a("dc4a"),h=a("14c3"),p=a("9f7f"),g=a("d039"),P=p.UNSUPPORTED_Y,b=4294967295,v=Math.min,_=o([].push),y=o("".slice),T=!g((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var a="ab".split(t);return 2!==a.length||"a"!==a[0]||"b"!==a[1]})),L="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;r("split",(function(t,e,a){var o="0".split(void 0,0).length?function(t,a){return void 0===t&&0===a?[]:n(e,this,t,a)}:e;return[function(e,a){var r=l(this),i=s(e)?m(e,t):void 0;return i?n(i,e,r,a):n(o,f(r),e,a)},function(t,n){var r=i(this),s=f(t);if(!L){var l=a(o,r,s,n,o!==e);if(l.done)return l.value}var m=u(r,RegExp),p=r.unicode,g=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(P?"g":"y"),T=new m(P?"^(?:"+r.source+")":r,g),C=void 0===n?b:n>>>0;if(0===C)return[];if(0===s.length)return null===h(T,s)?[s]:[];var k=0,I=0,w=[];while(I<s.length){T.lastIndex=P?0:I;var x,D=h(T,P?y(s,I):s);if(null===D||(x=v(d(T.lastIndex+(P?I:0)),s.length))===k)I=c(s,I,p);else{if(_(w,y(s,k,I)),w.length===C)return w;for(var G=1;G<=D.length-1;G++)if(_(w,D[G]),w.length===C)return w;I=k=x}}return _(w,y(s,k)),w}]}),L||!T,P)},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:t,IsAll:a}).then((function(t){var n=t.IsSucceed,i=t.Data,s=t.Message;if(n){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(e.columns)}else e.$message({message:s,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,n=t.type;this.queryInfo.Page="limit"===n?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===e){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},1662:function(t,e,a){"use strict";a("38bc")},3166:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=c,e.GeAreaTrees=L,e.GetFileSync=I,e.GetInstallUnitIdNameList=T,e.GetNoBindProjectList=h,e.GetPartDeepenFileList=C,e.GetProjectAreaTreeList=y,e.GetProjectEntity=l,e.GetProjectList=s,e.GetProjectPageList=i,e.GetProjectTemplate=p,e.GetPushProjectPageList=_,e.GetSchedulingPartList=k,e.IsEnableProjectMonomer=d,e.SaveProject=u,e.UpdateProjectTemplateBase=g,e.UpdateProjectTemplateContacts=P,e.UpdateProjectTemplateContract=b,e.UpdateProjectTemplateOther=v;var o=n(a("b775")),r=n(a("4328"));function i(t){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function u(t){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function I(t){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"38bc":function(t,e,a){},"4fc4":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetSubAssemblyStockPool=r;var o=n(a("b775"));n(a("4328"));function r(t){return(0,o.default)({url:"/PRO/Productiontask/GetSubAssemblyStockPool",method:"post",data:t})}},"55ff":function(t,e,a){"use strict";a.r(e);var n=a("a0a5"),o=a("f088");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("1662");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"77692a3e",null);e["default"]=s.exports},"560e":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a732"),a("b64b"),a("d3b7"),a("ac1f"),a("1276"),a("159b");var o=n(a("2909")),r=n(a("5530")),i=n(a("c14f")),s=n(a("1da1")),l=n(a("bae6")),u=n(a("1463")),c=n(a("333d")),d=n(a("6b2f")),f=n(a("d808")),m=a("c685"),h=a("3166"),p=a("a024"),g=n(a("15ac")),P=a("4fc4"),b=a("7f9d"),v=a("0e9a"),_=n(a("e8ae")),y=a("21a6"),T="$_$";e.default={name:"PROSectionWarehouse",components:{ExpandableSection:l.default,TreeDetail:u.default,Pagination:c.default,Consume:d.default,Record:f.default},mixins:[g.default],data:function(){return{showExpand:!0,treeData:[],treeLoading:!1,expandedKey:"",projectName:"",statusType:"",currentNode:{},customParams:{Sys_Project_Id:"",Project_Id:"",Area_Id:"",Working_Team_Id:"",Working_Process_Id:"",Code_Like:"",Component:"",InstallUnit_Id:"",Waiting_For_Shipment:null},installUnitIdNameList:[],processList:[],teamList:[],columns:[],secondColumns:[],firstColumns:[],tbData:[],total:0,rightLoading:!1,pgLoading:!1,tbLoading:!1,tablePageSize:m.tablePageSize,queryInfo:{Page:1,PageSize:10},dialogVisible:!1,currentComponent:"",title:"",width:"300",selectList:[],drawer:!1,iframeKey:"",fullscreenid:"",iframeUrl:"",fullbimid:"",fileBim:"",btnloading:!1,WaitingForShipmentData:[{label:"是",value:!0},{label:"否",value:!1}]}},computed:{filterText:function(){return this.projectName+T+this.statusType},nowColumns:function(){return this.firstColumns},Component_Codes:function(){return this.customParams.Component?this.customParams.Component.split("\n"):[]}},mounted:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return t.pgLoading=!0,e.n=1,t.getTableConfig("PROProducedSectionPool");case 1:return e.n=2,t.fetchTreeData();case 2:return e.a(2)}}),e)})))()},methods:{fetchTreeData:function(){var t=this;this.treeLoading=!0,(0,h.GetProjectAreaTreeList)({MenuId:this.$route.meta.Id,type:8}).then((function(e){if(e.IsSucceed){if(0===e.Data.length)return void(t.treeLoading=!1);var a=e.Data;a.map((function(t){return 0===t.Children.length?t.Data.Is_Imported=!1:(t.Data.Is_Imported=t.Children.some((function(t){return!0===t.Data.Is_Imported})),t.Is_Directory=!0,t.Children.map((function(t){t.Children.length>0&&(t.Is_Directory=!0)}))),t})),t.treeData=a,0===Object.keys(t.currentNode).length?t.setKey():t.handleNodeClick(t.currentNode)}})).finally((function(){t.treeLoading=!1}))},setKey:function(){var t=this,e=function(a){for(var n=0;n<a.length;n++){var o=a[n],r=o.Data,i=o.Children;if(r.ParentId&&(null===i||void 0===i||!i.length))return t.currentNode=r,void t.handleNodeClick(o);if(i&&i.length>0)return e(i)}};return e(this.treeData)},handleNodeClick:function(t){this.currentNode=t,this.expandedKey=t.Id,this.customParams.Sys_Project_Id=t.Data.Sys_Project_Id,this.customParams.Project_Id=t.Data.Project_Id,this.customParams.Area_Id=t.Data.Id,this.customParams.Area_Id&&(this.pgLoading=!0,this.getInstallUnitIdNameList(this.customParams.Area_Id,t)),this.handleClick()},fetchData:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){var a;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return a=P.GetSubAssemblyStockPool,t.tbLoading=!0,e.n=1,a((0,r.default)((0,r.default)((0,r.default)({},t.customParams),t.queryInfo),{},{Component_Codes:t.Component_Codes})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message.error(e.Message)})).finally((function(){t.tbLoading=!1,t.pgLoading=!1,t.rightLoading=!1}));case 1:return e.a(2)}}),e)})))()},handleClick:function(t,e){var a=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a.rightLoading=!0,a.selectList=[],a.$nextTick((function(){a.$refs.customParams.resetFields()})),t.n=1,a.getTableConfig("PROProducedSectionPool");case 1:return t.n=2,a.fetchData();case 2:return t.a(2)}}),t)})))()},handelsearch:function(t){var e=this;return(0,s.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return t&&(e.$refs.customParams.resetFields(),e.teamList=[]),e.selectList=[],e.queryInfo.Page=1,a.n=1,e.fetchData();case 1:return a.a(2)}}),a)})))()},setDefaultTeam:function(){this.customParams.Working_Team_Id=this.teamList[0].Id},getInstallUnitIdNameList:function(t,e){var a=this;""===t||e.Children.length>0?this.installUnitIdNameList=[]:(0,h.GetInstallUnitIdNameList)({Area_Id:t}).then((function(t){a.installUnitIdNameList=t.Data}))},getProcessListBase:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,p.GetProcessList)({type:1}).then((function(e){if(e.IsSucceed){var a,n={Name:"未分配",Id:"未分配"};t.processList=e.Data,t.processList.push(n),t.customParams.Working_Process_Id=null===(a=e.Data[0])||void 0===a?void 0:a.Id}else t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},handleProcessChange:function(t){var e=this;return(0,s.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(e.customParams.Working_Team_Id="",!t){a.n=2;break}return a.n=1,e.getWorkingTeams(t);case 1:e.setDefaultTeam(),a.n=3;break;case 2:e.teamList=[];case 3:return a.a(2)}}),a)})))()},getWorkingTeams:function(t){var e=this;return(0,s.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,p.GetWorkingTeamBase)({processId:t}).then((function(t){t.IsSucceed?e.teamList=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return a.a(2)}}),a)})))()},handleClose:function(){this.dialogVisible=!1},generateComponent:function(t,e){this.title=t,this.currentComponent=e,this.dialogVisible=!0},handelConsume:function(){var t=this;this.generateComponent("消耗",d.default),this.$nextTick((function(){t.$refs["content"].init(t.selectList)}))},handelRecord:function(t,e){var a=this;this.generateComponent(1===t?"转入记录":"消耗记录",f.default),this.$nextTick((function(){a.$refs["content"].init(t,e)}))},tbSelectChange:function(t){this.selectList=t.records},handleExport:function(){var t=this;this.btnloading=!0;var e=this.$refs.xTable.getCheckboxRecords();this.$refs.xTable.exportData({filename:"零件配送表",type:"xlsx",data:e,columnFilterMethod:function(t){var e=t.column;return!!e.field}}).then((function(){t.btnloading=!1}))},exportDataToExcel:function(t,e,a){return(0,s.default)((0,i.default)().m((function n(){var o,r,s;return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return o=new _.default.Workbook,r=o.addWorksheet("Sheet 1"),r.columns=e.map((function(t){return{header:t.Display_Name,key:t.Code,width:t.Width/10}})),t.forEach((function(t){var a=e.map((function(e){return t[e.Code]}));r.addRow(a)})),n.n=1,o.xlsx.writeBuffer();case 1:s=n.v,(0,y.saveAs)(new Blob([s]),"".concat(a,".xlsx"));case 2:return n.a(2)}}),n)})))()},handleDwg:function(t){var e=this;(0,b.GetDwg)({Part_Id:t.Part_Aggregate_Id}).then((function(t){if(t.IsSucceed){var a,n=(null===t||void 0===t||null===(a=t.Data)||void 0===a?void 0:a.length)&&t.Data[0].File_Url;window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+(0,v.parseOssUrl)(n),"_blank")}else e.$message({message:t.Message,type:"error"})}))},checCheckboxkMethod3:function(t){var e=t.row;return e.Can_Allocate_Count>0},customFilterFun:function(t,e,a){var n=t.split(T),r=n[0],i=n[1];if(!t)return!0;var s=a.parent,l=[a.label],u=[e.Data.Part_Use_Status],c=1;while(c<a.level)l=[].concat((0,o.default)(l),[s.label]),u=[].concat((0,o.default)(u),[e.Data.Part_Use_Status]),s=s.parent,c++;l=l.filter((function(t){return!!t})),u=u.filter((function(t){return!!t}));var d=!0,f=!0;return this.statusType&&(f=u.some((function(t){return-1!==t.indexOf(i)}))),this.projectName&&(d=l.some((function(t){return-1!==t.indexOf(r)}))),d&&f}}}},"6b2f":function(t,e,a){"use strict";a.r(e);var n=a("772c1"),o=a("a387");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("973f");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"059d3ad1",null);e["default"]=s.exports},"772c1":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"inner-wapper"},[a("div",{staticClass:"info"},[a("span",{staticClass:"title"},[t._v("批量选择消耗班组")]),a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},on:{change:t.changeAllWorkingTeam},model:{value:t.Working_Team_Id,callback:function(e){t.Working_Team_Id=e},expression:"Working_Team_Id"}},t._l(t.workTeamsOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("div",{staticClass:"table-wapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},t._l(t.columns,(function(e,n){return a("vxe-column",{key:n,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,width:"auto",field:e.Code,title:e.Display_Name,"min-width":e.Width},scopedSlots:t._u([{key:"default",fn:function(n){var o=n.row;return["Working_Team_Id"===e.Code?a("div",[a("vxe-select",{attrs:{transfer:""},on:{change:t.changeSingleWorkingTeam},model:{value:o.Working_Team_Id,callback:function(e){t.$set(o,"Working_Team_Id",e)},expression:"row.Working_Team_Id"}},t._l(t.workTeamsOption,(function(t){return a("vxe-option",{key:t.Id,attrs:{value:t.Id,label:t.Name}})})),1)],1):"Use_Count"===e.Code?a("div",[a("vxe-input",{attrs:{min:0,max:o.Stock_Count,type:"number"},model:{value:o[e.Code],callback:function(a){t.$set(o,e.Code,t._n(a))},expression:"row[item.Code]"}})],1):a("div",[a("span",[t._v(t._s(t._f("displayValue")(o[e.Code])))])])]}}],null,!0)})})),1)],1),a("div",{staticClass:"btn-wapper"},[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.confirm}},[t._v("领用")])],1)])},o=[]},"78cd":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("13d5"),a("e9f5"),a("910d"),a("9485"),a("a9e3"),a("b680"),a("d3b7");var o=a("8ab4"),r=n(a("15ac"));e.default={mixins:[r.default],data:function(){return{tbLoading:!1,tbData:[],columns:[],queryInfo:{PageSize:10},partName:"",totalNum:0,totalWeight:0,type:1,Waiting_For_Shipment:""}},mounted:function(){},methods:{init:function(t,e){var a=this;this.partName=e.Part_Code,this.Waiting_For_Shipment=e.Waiting_For_Shipment,this.type=t,1===t?this.getTableConfig("PROSectionTransferRecord"):this.getTableConfig("PROSectionConsumptionRecord").then((function(t){"是"===e.Waiting_For_Shipment?a.columns=a.columns.filter((function(t){return"Comp_Code"!==t.Code})):"否"===e.Waiting_For_Shipment&&(a.columns=a.columns.filter((function(t){return"Send_Code"!==t.Code})))})),this.fetchData(t,e.Part_Aggregate_Id)},fetchData:function(t,e){var a=this,n=1===t?o.GetAggregatePartProducedHistory:o.GetAggregatePartUseRecordHistory;this.tbLoading=!0;var r=1===t?"Actual_Transfer_Count":"Use_Count";n({Part_Aggregate_Id:e}).then((function(t){a.tbData=t.Data,a.totalNum=t.Data.reduce((function(t,e){return t+Number(e[r])}),0);var e=t.Data.reduce((function(t,e){return t+e[r]*e.Weight}),0);a.totalWeight=parseFloat((e/1e3).toFixed(3))})).finally((function(){a.tbLoading=!1}))}}}},"8ab4":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AggregatePartUse=l,e.GetAggregatePartProducedHistory=i,e.GetAggregatePartUseRecordHistory=s,e.GetPartDeliveryPageList=u,e.GetProducedPartPool=r;var o=n(a("b775"));n(a("4328"));function r(t){return(0,o.default)({url:"/PRO/Productiontask/GetProducedPartPool",method:"post",data:t})}function i(t){return(0,o.default)({url:"/pro/productiontask/GetAggregatePartProducedHistory",method:"post",data:t})}function s(t){return(0,o.default)({url:"/pro/productiontask/GetAggregatePartUseRecordHistory",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Productiontask/AggregatePartUse",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Productiontask/GetPartDeliveryPageList",method:"post",data:t})}},"971e":function(t,e,a){"use strict";a("0bf5b")},"973f":function(t,e,a){"use strict";a("f199")},"9f35":function(t,e,a){"use strict";a.r(e);var n=a("78cd"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},a024:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProcessFlow=u,e.AddProessLib=j,e.AddTechnology=l,e.AddWorkingProcess=s,e.DelLib=N,e.DeleteProcess=L,e.DeleteProcessFlow=y,e.DeleteTechnology=T,e.DeleteWorkingTeams=w,e.GetAllProcessList=f,e.GetCheckGroupList=R,e.GetChildComponentTypeList=W,e.GetFactoryAllProcessList=m,e.GetFactoryPeoplelist=S,e.GetFactoryWorkingTeam=P,e.GetGroupItemsList=_,e.GetLibList=i,e.GetLibListType=A,e.GetProcessFlow=h,e.GetProcessFlowListWithTechnology=p,e.GetProcessList=c,e.GetProcessListBase=d,e.GetProcessListTeamBase=G,e.GetProcessListWithUserBase=O,e.GetProcessWorkingTeamBase=F,e.GetTeamListByUser=U,e.GetTeamProcessList=v,e.GetWorkingTeam=b,e.GetWorkingTeamBase=D,e.GetWorkingTeamInfo=x,e.GetWorkingTeams=C,e.GetWorkingTeamsPageList=k,e.SaveWorkingTeams=I,e.UpdateProcessTeam=g;var o=n(a("b775")),r=n(a("4328"));function i(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(t)})}function l(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(t)})}function u(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(t)})}function c(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(t)})}function f(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(t)})}function m(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(t)})}function p(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(t)})}function P(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function b(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(t)})}function v(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(t)})}function _(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(t)})}function y(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(t)})}function T(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(t)})}function L(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(t)})}function D(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(t)})}function G(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(t)})}function O(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:t})}function N(t){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:t})}function A(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}},a0a5:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pgLoading,expression:"pgLoading"}],staticClass:"h100 app-wrapper",attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff",attrs:{width:300},model:{value:t.showExpand,callback:function(e){t.showExpand=e},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[a("div",{staticClass:"tree-search"},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"请选择"},model:{value:t.statusType,callback:function(e){t.statusType=e},expression:"statusType"}},[a("el-option",{attrs:{label:"未消耗完成",value:"未消耗完成"}}),a("el-option",{attrs:{label:"已消耗完成",value:"已消耗完成"}}),a("el-option",{attrs:{label:"部件未生产",value:"部件未生产"}})],1),a("el-input",{attrs:{placeholder:"搜索...",size:"small",clearable:"","suffix-icon":"el-icon-search"},model:{value:t.projectName,callback:function(e){t.projectName="string"===typeof e?e.trim():e},expression:"projectName"}})],1),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":t.customFilterFun,loading:t.treeLoading,"tree-data":t.treeData,"show-status":"","show-detail":"","can-node-click":!1,"filter-text":t.filterText,"expanded-key":t.expandedKey},on:{handleNodeClick:t.handleNodeClick},scopedSlots:t._u([{key:"csLabel",fn:function(e){var n=e.showStatus,o=e.data;return[o.ParentNodes?t._e():a("span",{staticClass:"cs-blue"},[t._v("("+t._s(o.Code)+")")]),t._v(t._s(o.Label)+" "),n?[o.Data["Part_Use_Status"]?a("i",{class:["已消耗完成"==o.Data["Part_Use_Status"]?"fourGreen":"未消耗完成"==o.Data["Part_Use_Status"]?"fourOrange":"部件未生产"==o.Data["Part_Use_Status"]?"fourRed":""]},[a("span",[t._v("("+t._s(o.Data["Part_Use_Status"])+")")])]):t._e()]:t._e()]}}])})],1)])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.rightLoading,expression:"rightLoading"}],staticClass:"cs-right",attrs:{"element-loading-text":"加载中"}},[a("div",{staticClass:"container"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",staticClass:"demo-form-inline",attrs:{inline:!0,model:t.customParams,"label-width":"100px"}},[[a("el-form-item",{attrs:{label:"所属构件名",prop:"Component"}},[a("el-input",{attrs:{type:"textarea",placeholder:"换行多个查询"},model:{value:t.customParams.Component,callback:function(e){t.$set(t.customParams,"Component",e)},expression:"customParams.Component"}})],1),a("el-form-item",{attrs:{label:"部件名称",prop:"Code_Like"}},[a("el-input",{attrs:{placeholder:"请输入部件名称"},model:{value:t.customParams.Code_Like,callback:function(e){t.$set(t.customParams,"Code_Like",e)},expression:"customParams.Code_Like"}})],1),a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"250px"},attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!Boolean(t.customParams.Area_Id)},model:{value:t.customParams.InstallUnit_Id,callback:function(e){t.$set(t.customParams,"InstallUnit_Id",e)},expression:"customParams.InstallUnit_Id"}},t._l(t.installUnitIdNameList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"是否直发",prop:"Waiting_For_Shipment"}},[a("el-select",{staticStyle:{width:"250px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.customParams.Waiting_For_Shipment,callback:function(e){t.$set(t.customParams,"Waiting_For_Shipment",e)},expression:"customParams.Waiting_For_Shipment"}},t._l(t.WaitingForShipmentData,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.handelsearch()}}},[t._v("搜索")]),a("el-button",{on:{click:function(e){return t.handelsearch("reset")}}},[t._v("重置")])],1)],2)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},[a("el-button",{attrs:{type:"primary",disabled:0===t.selectList.length},on:{click:t.handelConsume}},[t._v("消耗")])],1),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0},"checkbox-config":{checkField:"checked",trigger:"row",checkMethod:t.checCheckboxkMethod3}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),t._l(t.columns,(function(e,n){return a("vxe-column",{key:e.Column_Id,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,width:"auto",field:e.Code,title:e.Display_Name,"min-width":e.Width},scopedSlots:t._u(["Part_Code"===e.Code?{key:"default",fn:function(n){var o=n.row;return[a("div",[o.DwgCount?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleDwg(o)}}},[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))]):a("span",[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))])],1)]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[a("span",[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))])])]}}],null,!0)})})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"180","show-overflow":""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.handelRecord(1,n)}}},[t._v("转入记录")]),a("el-divider",{attrs:{direction:"vertical"}}),a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.handelRecord(2,n)}}},[t._v("消耗记录")])]}}])})],2)],1),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)])])],1),t.dialogVisible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.width},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a(t.currentComponent,{ref:"content",tag:"component",attrs:{"process-list":t.processList},on:{close:t.handleClose,refresh:t.fetchData}})],1):t._e()],1)},o=[]},a387:function(t,e,a){"use strict";a.r(e);var n=a("04f7c"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},d808:function(t,e,a){"use strict";a.r(e);var n=a("eaa4"),o=a("9f35");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("971e");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"07f658aa",null);e["default"]=s.exports},e41b:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteByIds=l,e.GetPartsImportTemplate=c,e.GetPartsList=s,e.GetProjectAreaTreeList=r,e.ImportParts=u,e.SaveProjectAreaSort=i;var o=n(a("b775"));function r(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:t})}},eaa4:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"inner-wapper"},[a("div",{staticClass:"info"},[a("span",[t._v("部件名称: "),a("i",[t._v(t._s(t.partName))])]),a("span",[t._v(" 共"+t._s(1===t.type?"转入":"消耗")+": "),a("i",[t._v(t._s(t.totalNum)+"件")])]),a("span",[t._v(" 总重: "),a("i",[t._v(t._s(t.totalWeight)+"t")])])]),a("div",{staticClass:"table-wapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},t._l(t.columns,(function(e,n){return a("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,width:"auto",field:e.Code,title:e.Display_Name,"min-width":e.Width},scopedSlots:t._u(["Type"===e.Code?{key:"default",fn:function(e){var n=e.row;return[a("div",[a("span",[t._v(t._s(1===n.Type?"齐套自动消耗":2===n.Type?"手动消耗":4===n.Type?"发货消耗":"系统自动消耗"))])])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[a("span",[t._v(t._s(t._f("displayValue")(o[e.Code])))])])]}}],null,!0)})})),1)],1)])},o=[]},f088:function(t,e,a){"use strict";a.r(e);var n=a("560e"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},f199:function(t,e,a){}}]);