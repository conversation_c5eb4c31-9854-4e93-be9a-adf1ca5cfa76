(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-commons"],{"0078":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"form",style:{textAlign:e.textAlign},attrs:{inline:"",model:e.form,rules:e.rules,"label-width":e.defaultLabelWidth}},[e._l(e.formItems,(function(t){return a("el-form-item",{key:t.prop,attrs:{label:t.label,prop:t.prop,"label-width":t.labelWidth||e.defaultLabelWidth}},["input"===t.type?a("el-input",{attrs:{placeholder:t.placeholder||"请输入",clearable:""},model:{value:e.form[t.prop],callback:function(a){e.$set(e.form,t.prop,a)},expression:"form[item.prop]"}}):"select"===t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:t.placeholder||"请选择",clearable:"",filterable:""},model:{value:e.form[t.prop],callback:function(a){e.$set(e.form,t.prop,a)},expression:"form[item.prop]"}},e._l(t.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):"date"===t.type?a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form[t.prop],callback:function(a){e.$set(e.form,t.prop,a)},expression:"form[item.prop]"}}):"daterange"===t.type?a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","unlink-panels":"","range-separator":"至","start-placeholder":t.startPlaceholder||"开始日期","end-placeholder":t.endPlaceholder||"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.form[t.prop],callback:function(a){e.$set(e.form,t.prop,a)},expression:"form[item.prop]"}}):"component"===t.type?a(t.component,e._b({tag:"component",model:{value:e.form[t.prop],callback:function(a){e.$set(e.form,t.prop,a)},expression:"form[item.prop]"}},"component",t.props||{},!1)):e._e()],1)})),e.searchButton?a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1):e._e()],2)},r=[]},"03a5":function(e,t,a){},"042b":function(e,t,a){"use strict";a.r(t);var n=a("fbcb"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"0490":function(e,t,a){"use strict";a.r(t);var n=a("f5dd"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"0553":function(e,t,a){},"06dc":function(e,t,a){"use strict";a("8a0a7")},"0892":function(e,t,a){"use strict";a("1d08")},"0a0e":function(e,t,a){"use strict";a.r(t);var n=a("ebc3"),r=a("b834");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("88bb");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"9f5880fa",null);t["default"]=s.exports},"0a8b":function(e,t,a){"use strict";a("85e5")},"0a98":function(e,t,a){},"0b09":function(e,t,a){"use strict";a.r(t);var n=a("eafb"),r=a("1c1f");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("6705");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"dec90158",null);t["default"]=s.exports},"0c62":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.isProductWeight?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.disabled},on:{change:e.handleChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1):a("el-tree-select",{ref:"treeSelectDepart",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0,disabled:e.disabled},"tree-params":e.treeParamsDepart},on:{"select-clear":e.handleChange,"node-click":e.handleChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},r=[]},"0c93":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("a732"),a("d3b7");t.default={props:{treeData:{type:Array,default:function(){return[]}},showDetail:{type:Boolean,default:function(){return!1}},showIcon:{type:Boolean,default:function(){return!0}},expandOnClickNode:{type:Boolean,default:function(){return!0}},buttonTypeArray:{type:Array,default:function(){return[]}},showCode:{type:Boolean,default:function(){return!1}},showType:{type:Boolean,default:function(){return!1}},showLeader:{type:Boolean,default:function(){return!1}},loading:Boolean,icon:{type:String,default:""},sameIcon:{type:Boolean,default:function(){return!1}},expandedKey:{type:String,default:""},canNodeClick:{type:Boolean,default:!0},nodeKey:{type:String,default:"Id"},defaultExpandAll:{type:Boolean,default:!0},showCheckbox:{type:Boolean,default:!1},checkStrictly:{type:Boolean,default:!1},highlightCurrent:{type:Boolean,default:!0},defaultCheckedKeys:{type:Array,default:function(){return[]}}},data:function(){return{defaultProps:{children:"Children",label:"Label"},currentNode:null,filterText:""}},watch:{filterText:function(e){this.$refs.tree.filter(e)},treeData:function(){var e=this;this.$nextTick((function(t){if(e.expandedKey){e.$refs.tree.setCurrentKey(e.expandedKey),e.currentNode=e.$refs.tree.getCurrentNode(),e.$emit("getCurrentNode",e.$refs.tree.getCurrentNode());var a=e.currentNode&&e.currentNode.Label;e.$emit("currentNodeLabel",a)}}))}},methods:{handleNodeClick:function(e,t){(t.isLeaf||this.canNodeClick)&&this.$emit("handleNodeClick",e)},handleButtonClick:function(e,t){switch(e){case"delete":this.$emit("handleNodeButtonDelete",t);break;case"copy":this.$emit("handleNodeButtonCopy",t);break;case"edit":this.$emit("handleNodeButtonEdit",t);break}},checkPermission:function(e){return this.buttonTypeArray.some((function(t){return t===e}))},getCheckedNodes:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=this.$refs.tree.getCheckedNodes(e,t);this.$emit("getCheckedNodes",a)},check:function(e,t){this.$emit("check",{dataArray:t,data:e})},setCheckedKeys:function(e){this.$refs.tree.setCheckedKeys(e)},filterNode:function(e,t){return!e||-1!==t[this.defaultProps.label].indexOf(e)}}}},"0cf3":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1")),o=a("9d7e2"),s=a("ed08");t.default={name:"SelectExternal",props:{value:{type:String,default:""}},data:function(){return{list:[],selectedValue:""}},watch:{value:function(){this.selectedValue=Array.isArray(this.value)?(0,s.deepClone)(this.value):this.value}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,o.GetListForContractSettingForMateriel)();case 1:a=t.v,e.list=a.Data;case 2:return t.a(2)}}),t)})))()}}}},"0cf5":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tree-select",{ref:"treeSelectDepart",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0,disabled:e.disabled},"tree-params":e.treeParamsDepart},on:{"select-clear":e.handleChange,"node-click":e.handleChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1)},r=[]},"0df4":function(e,t,a){"use strict";a.r(t);var n=a("8532"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"0e29":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("span",{staticClass:"cell-span"},[e.editable&&e.editMode&&e.column.Is_Edit?[e.column.Dropdown_Name?[a("el-select",{ref:"control",staticClass:"cell-control",attrs:{placeholder:"请选择"},on:{change:e.valueChanged,blur:e.blurHandler},model:{value:e.row[e.column.Code],callback:function(t){e.$set(e.row,e.column.Code,t)},expression:"row[column.Code]"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)]:["number"===e.type?a("el-input-number",{ref:"control",staticClass:"cell-control",attrs:{"controls-position":"right",size:"mini"},on:{change:e.valueChanged,blur:e.blurHandler},model:{value:e.row[e.column.Code],callback:function(t){e.$set(e.row,e.column.Code,t)},expression:"row[column.Code]"}}):e._e(),"text"===e.type?a("el-input",{ref:"control",staticClass:"cell-control",attrs:{type:e.type,size:"mini",placeholder:""},on:{change:e.valueChanged,blur:e.blurHandler},model:{value:e.row[e.column.Code],callback:function(t){e.$set(e.row,e.column.Code,t)},expression:"row[column.Code]"}}):e._e(),"time"===e.type?a("el-time-select",{ref:"control",staticClass:"cell-control",attrs:{size:"mini","picker-options":{start:"00:00",step:"00:30",end:"23:59"},placeholder:"选择时间"},on:{change:e.valueChanged,blur:e.blurHandler},model:{value:e.row[e.column.Code],callback:function(t){e.$set(e.row,e.column.Code,t)},expression:"row[column.Code]"}}):e._e(),"date"===e.type?a("el-date-picker",{ref:"control",staticClass:"cell-control",attrs:{type:"date",size:"mini",placeholder:"选择日期","value-format":"yyyy-M-d"},on:{change:e.valueChanged,blur:e.blurHandler},model:{value:e.row[e.column.Code],callback:function(t){e.$set(e.row,e.column.Code,t)},expression:"row[column.Code]"}}):e._e(),"daterange"===e.type?a("el-date-picker",{staticStyle:{width:"96%"},attrs:{type:"daterange","value-format":"yyyy-M-d",format:"yyyy-M-d","range-separator":"~","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.valueChanged,blur:e.blurHandler},model:{value:e.row[e.column.Code],callback:function(t){e.$set(e.row,e.column.Code,t)},expression:"row[column.Code]"}}):e._e()]]:[a("FormatedCellText",{attrs:{text:e.row[e.column.Code],"tool-tip-text":e.row["StatusToolTip"]?e.row["StatusToolTip"]:e.row[e.column.Code],"show-tooltip":e.showTooltip,type:e.type,"is-format":e.column.Is_Formatter,formatter:e.column.Formatter,range:e.column.Range,"cus-style":e.column.Style}})]],2)},r=[]},"0e6e":function(e,t,a){"use strict";a.r(t);var n=a("2f19"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"0f6c":function(e,t,a){},"0f97":function(e,t,a){"use strict";a.r(t);var n=a("6ab8"),r=a("7ee7");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("b799"),a("4a77");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"648bff5d",null);t["default"]=s.exports},"0fb3":function(e,t,a){},"0ffd":function(e,t,a){"use strict";a("21316")},"11ca":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"tree",attrs:{"check-strictly":e.checkStrictly,data:e.treeData,"default-checked-keys":e.defaultCheckedKeys,"default-expand-all":e.defaultExpandAll,"expand-on-click-node":e.expandOnClickNode,"default-expanded-keys":e.innerExpandedKeys,"highlight-current":e.highlightCurrent,"node-key":e.nodeKey,props:e.defaultProps,"show-checkbox":e.showCheckbox,"filter-node-method":e.filterNode,"check-on-click-node":e.checkOnClickNode,draggable:"","allow-drop":e.allowDrop,"allow-drag":e.allowDrag},on:{check:e.check,"current-change":e.handleNodeClick,"node-drop":e.handleDrop},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.node,r=t.data;return a("span",{staticClass:"custom-tree-node"},[a("div",{staticClass:"tree-container",staticStyle:{display:"flex"}},[a("div",{staticClass:"first"},[a("div",[e.showIcon?a("span",[e.sameIcon?a("span",[a("svg-icon",{attrs:{"icon-class":e.icon,"class-name":"class-icon"}})],1):a("span",[r.Is_Directory?a("svg-icon",{attrs:{"icon-class":n.expanded?"icon-folder-open":"icon-folder","class-name":"class-icon"}}):a("svg-icon",{attrs:{"icon-class":e.icon,"class-name":"class-icon"}})],1)]):e._e(),a("el-tooltip",{attrs:{placement:"top",content:r.Label}},[a("div",{staticStyle:{"vertical-align":"bottom",display:"inline-block","text-overflow":"ellipsis",overflow:"hidden"},style:e.nodeLabelWidth?"width:"+e.nodeLabelWidth+"px":"auto"},[e._t("csLabel",[e._v(" "+e._s(r.Label)+" "),e.showStatus&&"全部"!=r.Label?a("i",{class:[r.Data&&r.Data.Is_Imported?r.Data.Is_Imported?"fourGreen":"fourOrange":""]},[e._v(" "+e._s(r.Data&&r.Data.Is_Imported?r.Data.Is_Imported?"已导入":"未导入":"")+" ")]):e._e(),e.showLabelInfo?a("span",{staticStyle:{color:"#999"}},[e._v(" ("+e._s(e.filterDiyContent(r.Level))+") ")]):e._e()],{showStatus:e.showStatus,data:r})],2)]),e.itemEditable(r)?a("el-popover",{ref:"modpop_"+r.Id,attrs:{name:"modpop",placement:"top",width:"160"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入节点名称"},model:{value:r.Label,callback:function(t){e.$set(r,"Label",t)},expression:"data.Label"}}),a("div",{staticStyle:{"text-align":"right",margin:"10px 0px 0px 10px"}},[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(t){return e.popCancel(r)}}},[e._v("取消")]),a("el-button",{attrs:{disabled:!r.Label,type:"primary",size:"mini"},on:{click:function(t){return e.popConfirm(r)}}},[e._v("确定")])],1),a("i",{staticClass:"el-icon-edit",staticStyle:{color:"#f56c6c"},attrs:{slot:"reference"},on:{click:function(t){return t.stopPropagation(),e.modItem(r)}},slot:"reference"})],1):e._e()],1)]),e.showDetail?a("div",{staticClass:"clearfix",staticStyle:{display:"flex","flex-direction":"row-reverse",flex:"1"}},[e.showCode?a("div",{staticClass:"third"},[e._v(" "+e._s(r.Data&&r.Data.Code)+" "),a("span",{staticClass:"cs-w1"})]):e._e(),e.showType?a("div",{staticClass:"four"},[e._v(" "+e._s(r.Data&&r.Data.TypeName)+" "),a("span",{staticClass:"cs-w1"})]):e._e(),e.showLeader?a("div",{staticClass:"four"},[e._v(" "+e._s(r.Data&&r.Data.Leader_Name)+" leader "),a("span",{staticClass:"cs-w1"})]):e._e(),n.isCurrent?a("div",{staticClass:"seconds"},[e.buttonTypeArray.length>0?[e.checkPermission("userSetting")?a("i",{staticClass:"icon-user-setting",on:{click:function(t){return t.stopPropagation(),e.handleButtonClick("userSetting",r)}}}):e._e(),e.checkPermission("delete")?a("i",{staticClass:"el-icon-delete",on:{click:function(t){return t.stopPropagation(),e.handleButtonClick("delete",r)}}}):e._e(),e.checkPermission("copy")?a("i",{staticClass:"el-icon-copy-document",on:{click:function(t){return t.stopPropagation(),e.handleButtonClick("copy",r)}}}):e._e(),e.checkPermission("edit")?a("i",{staticClass:"el-icon-edit",on:{click:function(t){return t.stopPropagation(),e.handleButtonClick("edit",r)}}}):e._e()]:e._e(),[e._t("default",null,{slotProps:{node:n,data:r}})]],2):e._e()]):e._e()])])}}],null,!0)})},r=[]},1246:function(e,t,a){"use strict";a("0fb3")},"13f3":function(e,t,a){"use strict";a.r(t);var n=a("3a6c8"),r=a("0490");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("0ffd"),a("5bfd9");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"49deacc6",null);t["default"]=s.exports},"142f5":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{staticStyle:{display:"flex","flex-direction":"column","flex-flow":"column",height:"100%"}},[a("el-header",{staticStyle:{height:"32px"}},[a("el-input",{attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}})],1),a("el-main",[a("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"tree",attrs:{"check-strictly":e.checkStrictly,data:e.treeData,"default-checked-keys":e.defaultCheckedKeys,"default-expand-all":e.defaultExpandAll,"expand-on-click-node":e.expandOnClickNode,"highlight-current":e.highlightCurrent,"node-key":e.nodeKey,props:e.defaultProps,"show-checkbox":e.showCheckbox,"filter-node-method":e.filterNode},on:{check:e.check,"current-change":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.node,r=t.data;return a("span",{staticClass:"custom-tree-node"},[a("div",{staticClass:"tree-container"},[a("div",{staticClass:"first"},[a("span",{staticStyle:{width:"100%"}},[e.showIcon?a("span",[e.sameIcon?a("span",[a("svg-icon",{attrs:{"icon-class":e.icon,"class-name":"class-icon"}})],1):a("span",[r.Is_Directory?a("svg-icon",{attrs:{"icon-class":n.expanded?"icon-folder-open":"icon-folder","class-name":"class-icon"}}):a("svg-icon",{attrs:{"icon-class":e.icon,"class-name":"class-icon"}})],1)]):e._e(),e._v(" "+e._s(r.Label)+" ")])]),e.showDetail?a("div",[e.showCode?a("div",{staticClass:"third"},[e._v(" "+e._s(r.Data&&r.Data.Code)+" "),a("span",{staticClass:"cs-w1"})]):e._e(),e.showType?a("div",{staticClass:"four"},[e._v(" "+e._s(r.Data&&r.Data.TypeName)+" "),a("span",{staticClass:"cs-w1"})]):e._e(),e.showLeader?a("div",{staticClass:"four"},[e._v(" "+e._s(r.Data&&r.Data.Leader_Name)+" "),a("span",{staticClass:"cs-w1"})]):e._e(),e.buttonTypeArray.length>0&&n.isCurrent?a("div",{staticClass:"seconds"},[e.checkPermission("delete")?a("i",{staticClass:"el-icon-delete",on:{click:function(t){return t.stopPropagation(),e.handleButtonClick("delete",r)}}}):e._e(),e.checkPermission("copy")?a("i",{staticClass:"el-icon-copy-document",on:{click:function(t){return t.stopPropagation(),e.handleButtonClick("copy",r)}}}):e._e(),e.checkPermission("edit")?a("i",{staticClass:"el-icon-edit",on:{click:function(t){return t.stopPropagation(),e.handleButtonClick("edit",r)}}}):e._e()]):e._e()]):e._e()])])}}])})],1)],1)},r=[]},1455:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("bimdialog",{attrs:{"dialog-title":e.title+"监控","dialog-width":"1400px",visible:e.dialogVisible,hidebtn:""},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:e.handleSubmit,cancelbtn:e.handleClose,handleClose:e.handleClose}},[a("div",{staticClass:"createPost-container"},[a("el-timeline",{attrs:{reverse:!0}},e._l(e.histories,(function(t,n){return a("el-timeline-item",{key:n,attrs:{timestamp:e._f("timeFormat")(t.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}")}},[e._v(" "+e._s(t.Content)+" "),a("div",[a("span",{},[e._v(e._s(t.Create_UserName))]),t.Next_Maker_List?a("span",[a("el-divider",{attrs:{direction:"vertical"}}),e._v(" 下一步： ")],1):e._e(),a("span",[e._v(e._s(t.Next_Maker_List))])])])})),1),a("created-flow",{attrs:{"form-template":null,"is-edit":!0,"is-show-content":!0,"scheme-content":e.schemeContent}})],1)])],1)},r=[]},1463:function(e,t,a){"use strict";a.r(t);var n=a("11ca"),r=a("c120");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("0a8b");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"19209dbe",null);t["default"]=s.exports},"14a1":function(e,t,a){"use strict";a.r(t);var n=a("b89a"),r=a("6b16");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"158a":function(e,t,a){},"16fe":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1")),o=n(a("5530")),s=n(a("2909"));a("4de4"),a("b0c0"),a("e9f5"),a("910d"),a("a732"),a("a9e3"),a("d3b7"),a("25f0");var l=a("e41b");t.default={props:{editable:{type:Boolean,default:!1},editOpts:{type:Object,default:function(){return{}}},filterText:{type:String,default:""},treeData:{type:Array,default:function(){return[]}},showDetail:{type:Boolean,default:function(){return!1}},showIcon:{type:Boolean,default:function(){return!0}},showLabelInfo:{type:Boolean,default:function(){return!1}},expandOnClickNode:{type:Boolean,default:function(){return!0}},buttonTypeArray:{type:Array,default:function(){return[]}},showCode:{type:Boolean,default:function(){return!1}},showType:{type:Boolean,default:function(){return!1}},showLeader:{type:Boolean,default:function(){return!1}},loading:Boolean,icon:{type:String,default:""},sameIcon:{type:Boolean,default:function(){return!1}},expandedKey:{type:String,default:""},canNodeClick:{type:Boolean,default:!0},nodeKey:{type:String,default:"Id"},defaultExpandedKeys:{type:Array,default:function(){return[]}},defaultExpandAll:{type:Boolean,default:!1},showCheckbox:{type:Boolean,default:!1},checkOnClickNode:{type:Boolean,default:!1},checkStrictly:{type:Boolean,default:!1},highlightCurrent:{type:Boolean,default:!0},defaultCheckedKeys:{type:Array,default:function(){return[]}},nodeLabelWidth:{type:Number,default:0},showStatus:{type:Boolean,default:function(){return!1}},isCustomFilter:{type:Boolean,default:function(){return!1}},customFilterFun:{type:Function,default:function(){}}},data:function(){return{defaultProps:{children:"Children",label:"Label"},innerExpandedKeys:[],currentNode:null,toMod:null,childrenArea:!1}},watch:{expandedKey:function(e,t){var a=this;this.$nextTick((function(e){a.setExpandKey()}))},treeData:function(){var e=this;this.$nextTick((function(t){e.defaultExpandedKeys.length&&(e.innerExpandedKeys=(0,s.default)(e.defaultExpandedKeys))}))},filterText:function(e){this.$refs.tree.filter(e)}},methods:{setExpandKey:function(){if(this.expandedKey){this.$refs.tree.setCurrentKey(this.expandedKey),this.currentNode=this.$refs.tree.getCurrentNode(),this.$emit("getCurrentNode",this.$refs.tree.getCurrentNode());var e=this.currentNode&&this.currentNode.Label;this.$emit("currentNodeLabel",e),this.innerExpandedKeys=[this.expandedKey]}},getNodeByKey:function(e){return e?this.$refs.tree.getNode(e):null},filterRef:function(e){this.$refs.tree.filter(e)},isModing:function(e){return this.toMod&&this.toMod.Id===e.Id},filterDiyContent:function(e){var t,a=parseInt(e),n={1:"类别",2:"检查内容",3:"检查项目"};return null!==(t=n[a])&&void 0!==t?t:a},filterNode:function(e,t,a){return!e||(this.isCustomFilter?this.customFilterFun(e,t,a):-1!==t.Label.indexOf(e))},itemEditable:function(e){if(!this.editable||!e)return!1;if(!this.editOpts||!this.editOpts.check||!this.editOpts.api)return!1;var t=Object.prototype.hasOwnProperty.call(e,this.editOpts.check);return t&&e[this.editOpts.check]},modItem:function(e){for(var t in this.$refs){var a,n;null!==(a=this.$refs[t].$attrs)&&void 0!==a&&a.name&&"modpop"===(null===(n=this.$refs[t].$attrs)||void 0===n?void 0:n.name)&&t!==e.Id&&this.$refs[t].doClose()}this.toMod=e},popCancel:function(e){this.$refs["modpop_".concat(e.Id)].doClose(),e.Label=e.Data.Display_Name},popConfirm:function(e){var t=this,a=e.Data.Display_Name;this.$refs["modpop_".concat(e.Id)].doClose();var n=this.editOpts.api;"[object Function]"===Object.prototype.toString.call(n)&&n((0,o.default)((0,o.default)({},e.Data),{},{Display_Name:e.Label})).then((function(n){n.IsSucceed?e.Data.Display_Name=e.Label:(e.Label=a,t.$message.warning(n.Message))})).catch((function(t){e.Label=a}))},handleNodeClick:function(e,t){(t.isLeaf||this.canNodeClick)&&this.$emit("handleNodeClick",e)},handleButtonClick:function(e,t){switch(e){case"delete":this.$emit("handleNodeButtonDelete",t);break;case"copy":this.$emit("handleNodeButtonCopy",t);break;case"edit":this.$emit("handleNodeButtonEdit",t);break}},checkPermission:function(e){return this.buttonTypeArray.some((function(t){return t===e}))},getCheckedNodes:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=this.$refs.tree.getCheckedNodes(e,t);this.$emit("getCheckedNodes",a)},check:function(e,t){this.$emit("check",{dataArray:t,data:e})},setCheckedKeys:function(e){this.$refs.tree.setCheckedKeys(e)},handleDrop:function(e,t,a,n){this.saveProjectAreaSort({MenuId:this.$route.meta.Id,Model:this.treeData})},saveProjectAreaSort:function(e){var t=this;return(0,i.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,(0,l.SaveProjectAreaSort)(e);case 1:t.$emit("saveSortFinish"),t.$message.success("排序保存成功"),a.n=3;break;case 2:a.p=2,a.v,t.$message.error("保存失败");case 3:return a.a(2)}}),a,null,[[0,2]])})))()},allowDrop:function(e,t,a){var n,r,i,o;if("inner"===a)return!1;var s=null!==(n=null===(r=e.parent)||void 0===r?void 0:r.id)&&void 0!==n?n:null,l=null!==(i=null===(o=t.parent)||void 0===o?void 0:o.id)&&void 0!==i?i:null;return s===l},allowDrag:function(e){return!0}}}},"171e":function(e,t,a){"use strict";a.r(t);var n=a("e6ad"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},1999:function(e,t,a){"use strict";a.r(t);var n=a("8bab"),r=a("b956");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("b71b");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"1707be18",null);t["default"]=s.exports},"19cb":function(e,t,a){"use strict";var n=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.calc=i,a("99af"),a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("13d5"),a("4e82"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("b680"),a("d3b7"),a("159b");var r=n(a("c1df"));n(a("32fd"));function i(e,t,a,n){return new Promise((function(i,l){for(var d=t.Plan_Start_Date,u=t.Plan_End_Date,f=null,p=null,h=0;h<a.length;h++)(null==f||f>a[h].Plan_Start_Date)&&(f=a[h].Plan_Start_Date),(null==p||p<a[h].Plan_End_Date)&&(p=a[h].Plan_End_Date),a[h]["iscriticalPath"]=0,a[h]["isCalcforward"]=!1,a[h]["isCalcbackward"]=!1;null==d&&null!=f&&(d=f),null==u&&null!=p&&(u=p);var m=d;if(t.Cur_Data_Date&&(m=t.Cur_Data_Date),null==m||""==m)return l("状态日期必须不为空!");var _=a.filter((function(e){return"project"!=e.type&&""!=e.Actual_Start_Date&&null!=e.Actual_Start_Date&&e.Actual_Start_Date>m}));null!=_&&_.length,_=a.filter((function(e){return"project"!=e.type&&""!=e.Actual_End_Date&&null!=e.Actual_End_Date&&e.Actual_End_Date>m})),null!=_&&_.length;var g=o(a,n);if("error"==g.status)return l(g.message);var v=s(a,n);if("error"==v.status)return l(v.message);c(a,n,m,d,u,t,e),t.Plan_Start_Date=a.map((function(e){return e.Plan_Start_Date})).sort((function(e,t){return e<=t?-1:1})).shift(),t.Plan_End_Date=a.map((function(e){return e.Plan_End_Date})).sort((function(e,t){return e<=t?-1:1})).pop(),t.Plan_Duration=Math.abs(e.calculateDuration({start_date:t.Plan_Start_Date,end_date:t.Plan_End_Date}))+1,t.Actual_Start_Date=a.filter((function(e){return Boolean(e.Actual_Start_Date)})).map((function(e){return e.Actual_Start_Date})).sort((function(e,t){return e<=t?-1:1})).shift()||null;var y=a.filter((function(e){return Boolean(e.Actual_End_Date)})).map((function(e){return e.Actual_End_Date}));t.Actual_End_Date=y.sort((function(e,t){return e<=t?-1:1})).pop()||null;var b=y.length!==a.length;if(b&&(t.Actual_End_Date=null),t.Actual_Start_Date){var D=t.Actual_End_Date||t.Cur_Data_Date;t.Actual_Duration=Math.abs(e.calculateDuration({start_date:t.Actual_Start_Date,end_date:D}))+1}else t.Actual_Duration=0;t.Dynamic_Start_Date=t.Actual_Start_Date?r(t.Actual_Start_Date).format("YYYY-MM-DD[A]"):r(t.Plan_Start_Date).format("YYYY-MM-DD"),t.Dynamic_End_Date=t.Actual_End_Date?r(t.Actual_End_Date).format("YYYY-MM-DD[A]"):r(t.Plan_End_Date).format("YYYY-MM-DD"),t.Dynamic_Duration=Math.abs(e.calculateDuration({start_date:t.Actual_Start_Date||t.Plan_Start_Date,end_date:t.Actual_End_Date||t.Plan_End_Date}))+1;var S=0,k=0;a.forEach((function(e){"task"===e.type&&(k+=e.Plan_Duration,e.Actual_Start_Date&&(S+=e.Plan_Duration*e.Actual_Progress))})),t.Percent_Complete=0===k?0:(S/k).toFixed(4),i(!0)}))}function o(e,t){for(var a=0;a<t.length;a++){var n=t[a].source,r=e.filter((function(e){return e.id==n}))[0],i=t[a].target,o=e.filter((function(e){return e.id==i}))[0];try{if(null==r||null==o)return{status:"error",message:"关联关系数据异常,请检查或者联系开发人员"};if("project"==r.type||"project"==o.type){var s="名称为"+r.text+"的wbs节点参与关联关系!请检查";return{status:"error",message:s}}}catch(l){}}return{status:"success",message:""}}function s(e,t){for(var a=0;a<t.length;a++){var n=new Array;n.push(t[a].source);var r=l(e,t,n,t[a]);if("error"==r.status)return r}return{status:"success",message:""}}function l(e,t,a,n){n.source;var r=n.target;if(!(a.indexOf(r)<0)){var i=e.filter((function(e){return e.id==r}))[0],o="名称为"+i.text+"的任务存在错误的关联关系,造成圆形回路,请检查",s={status:"error",message:o};return s}var c=t.filter((function(e){return e.source==r}));if(null!=c&&c.length>0){a.push(r);for(var d=0;d<c.length;d++){var u=l(e,t,a,c[d]);if("error"==u.status)return u}a.splice(1,r)}return{status:"success",message:""}}function c(e,t,a,n,i,o,s){for(var l=e.filter((function(e){return"task"==e.type||"milestone"==e.type})),c=0;c<l.length;c++)d(e,t,a,n,l[c],s);for(c=0;c<l.length;c++)i<l[c].Plan_End_Date&&(i=l[c].Plan_End_Date),i<l[c].Actual_End_Date&&(i=l[c].Actual_End_Date);for(c=0;c<l.length;c++)u(e,t,a,i,l[c],s);var p=!1;for(c=0;c<l.length;c++)if(l[c].Actual_End_Date)l[c].Free_Float=null,l[c].Total_Float=null;else{l[c].Total_Float=(new Date(l[c].Laster_Start_Date)-new Date(l[c].Early_Start_Date))/864e5,l[c].Total_Float<=0&&(p=!0),l[c].Total_Float<=0&&(l[c]["iscriticalPath"]=1);var h=t.filter((function(e){return e.source==l[c].id}));if(l[c].Free_Float=null,h&&h.length>0)for(var m=0;m<h.length;m++){var _=e.filter((function(e){return e.id==h[m].target}))[0],g=0;g="0"==h[m].type?(new Date(_.Early_Start_Date)-new Date(l[c].Early_End_Date))/864e5-parseInt(h[m].lag||"0")-1:"1"==h[m].type?(new Date(_.Early_Start_Date)-new Date(l[c].Early_Start_Date))/864e5-parseInt(h[m].lag||"0"):"2"==h[m].type?(new Date(_.Early_End_Date)-new Date(l[c].Early_End_Date))/864e5-parseInt(h[m].lag||"0"):"3"==h[m].type?(new Date(_.Early_End_Date)-new Date(l[c].Early_Start_Date))/864e5-parseInt(h[m].lag||"0")+1:(new Date(_.Early_Start_Date)-new Date(l[c].Early_End_Date))/864e5-parseInt(h[m].lag||"0")-1,(null==l[c].Free_Float||g<l[c].Free_Float)&&(l[c].Free_Float=g),l[c].Free_Float<0&&(l[c].Free_Float=0)}else l[c].Free_Float=(new Date(i)-new Date(l[c].Early_End_Date))/864e5,l[c].Free_Float<0&&(l[c].Free_Float=0)}if(!p){var v=e.filter((function(e){return e.Total_Float>=0})),y=v.reduce((function(e,t){return e&&e.Total_Float<t.Total_Float?e.Total_Float:t.Total_Float})),b=e.filter((function(e){return e.Total_Float==y}));for(m=0;m<b.length;m++)b[m]["iscriticalPath"]=1}var D=e.filter((function(e){return"project"==e.type}));if(null!=D)for(c=0;c<D.length;c++){var S,k,w=void 0,C=void 0,x=f(D[c],e),T=0,P=0,N=null,E=null,O=null,I=null,$=null;for(m=0;m<x.length;m++)parseInt(x[m].duration),T+=parseInt(x[m].Plan_Duration),P+=x[m].Actual_Progress*parseInt(x[m].Plan_Duration),(null==N||N>x[m].start_date)&&(N=x[m].start_date,w=x[m]),(null==E||E<r(x[m].Dynamic_End_Date.split("A")[0]).toDate())&&(E=r(x[m].Dynamic_End_Date.split("A")[0]).toDate(),C=x[m]),null!=x[m].Actual_Start_Date&&(null==O||O>x[m].Actual_Start_Date)&&(O=x[m].Actual_Start_Date),x[m].Actual_Start_Date&&(!I||x[m].Actual_Start_Date<I)&&(I=x[m].Actual_Start_Date),x[m].Actual_End_Date&&($&&!(x[m].Actual_End_Date>$)||x.find((function(e){return Boolean(e.Actual_End_Date)}))||($=x[m].Actual_End_Date));if(null!=N&&null!=E){if(D[c].Plan_Start_Date=N,D[c].start_date=N,D[c].Actual_Start_Date=O,D[c].Dynamic_Start_Date=w.Actual_Start_Date?r(N).format("YYYY-MM-DD[A]"):r(N).format("YYYY-MM-DD"),"milestone"===w.type&&w.parent==D[c].Code){var L=s.calculateEndDate({start_date:w.start_date,duration:-1});D[c].Dynamic_Start_Date=r(L).format("YYYY-MM-DD[A]")}D[c].Dynamic_End_Date=C.Actual_End_Date?r(E).format("YYYY-MM-DD[A]"):r(E).format("YYYY-MM-DD"),D[c].Plan_End_Date=E,D[c].Dynamic_Duration=Math.abs(s.calculateDuration({start_date:r(D[c].Dynamic_Start_Date.split("A")[0]).toDate(),end_date:r(D[c].Dynamic_End_Date.split("A")[0]).toDate()}))+1,D[c].duration=Math.abs(s.calculateDuration({start_date:new Date(E),end_date:new Date(N)}))+1,"milestone"===C.type&&(D[c].Dynamic_Duration-=1,C.start_date=C.end_date=D[c].end_date=r(E).add(1,"days").startOf("date").toDate())}T>0&&(D[c].progress=a>=N?(P/T).toFixed(4):0,D[c].Actual_Progress=(P/T).toFixed(4),1===parseInt(D[c].Actual_Progress)?(D[c].Actual_End_Date=E,$=E):D[c].Actual_End_Date=null),D[c].Early_Start_Date=null===(S=w)||void 0===S?void 0:S.Early_Start_Date,D[c].Early_End_Date=null===(k=C)||void 0===k?void 0:k.Early_End_Date;var A=x.map((function(e){return e.Needed_Start_Date})).filter((function(e){return Boolean(e)})).map((function(e){return r(e).toDate().getTime()})).sort((function(e,t){return e-t})).shift(),M=x.map((function(e){return e.Needed_End_Date})).filter((function(e){return Boolean(e)})).map((function(e){return r(e).toDate().getTime()})).sort((function(e,t){return e-t})).pop();D[c].Needed_Start_Date=A?r(A).format("YYYY-MM-DD"):"",D[c].Needed_End_Date=M?r(M).format("YYYY-MM-DD"):"",D[c].Needed_Duration=D[c].Needed_Start_Date&&D[c].Needed_End_Date?Math.abs(s.calculateDuration({start_date:r(D[c].Needed_Start_Date).toDate(),end_date:r(D[c].Needed_End_Date).toDate()}))+1:0,I&&(D[c].Actual_Start_Date=I),$&&(D[c].Actual_End_Date=$),D[c].Actual_Duration=$&&I?Math.abs(s.calculateDuration({start_date:r(I).toDate(),end_date:r($).toDate()}))+1:I&&!$?Math.abs(s.calculateDuration({start_date:r(I).toDate(),end_date:r(a).toDate()}))+1:0,D[c].Plan_Duration=D[c].Plan_Start_Date&&D[c].Plan_End_Date?Math.abs(s.calculateDuration({start_date:r(D[c].Plan_Start_Date).toDate(),end_date:r(D[c].Plan_End_Date).toDate()}))+1:0,D[c].Target_Duration=D[c].Target_Start_Date&&D[c].Target_End_Date?Math.abs(s.calculateDuration({start_date:r(D[c].Target_Start_Date).toDate(),end_date:r(D[c].Target_End_Date).toDate()})):0,x.length<=0&&(D[c].start_date=r(o.Plan_Start_Date).startOf("date").toDate(),D[c].duration=1,D[c].end_date=r(D[c].start_date).add(1,"days").startOf("date").toDate(),D[c].Actual_Start_Date="",D[c].Actual_End_Date="",D[c].Needed_Start_Date="",D[c].Needed_End_Date="",D[c].Dynamic_Start_Date="",D[c].Dynamic_End_Date="",D[c].progress=0,D[c].Actual_Progress=0,D[c].Dynamic_Duration=0,D[c].Actual_Duration=0,D[c].Needed_Duration=0,D[c].Plan_Duration=1)}o.Plan_Start_Date=n,o.Plan_End_Date=i}function d(e,t,a,n,i,o){if(0==i.isCalcforward){var s=null,l=null,c=null,u=t.filter((function(e){return e.target==i.id}));if(null==u||0==u.length)s=null!=n?n:i.Plan_Start_Date;else for(var f=0;f<u.length;f++){var p,h,m=u[f].source,_=u[f].type,g=0;null!=u[f].lag&&(g=parseInt(u[f].lag)),c=e.filter((function(e){return e.id==m}))[0],d(e,t,a,n,c,o),c.Actual_End_Date||("milestone"!=c.type?"0"==_?(g+=1,p=o.calculateEndDate({start_date:c.Needed_End_Date,duration:g})):"1"==_?p=o.calculateEndDate({start_date:c.Needed_Start_Date,duration:g}):"2"==_?(h=o.calculateEndDate({start_date:c.Needed_End_Date,duration:g}),p=o.calculateEndDate({start_date:h,duration:-1*parseInt(i.duration)+1})):"3"==_?(g-=1,h=o.calculateEndDate({start_date:c.Needed_Start_Date,duration:g}),p=o.calculateEndDate({start_date:h,duration:-1*parseInt(i.duration)+1})):(g+=1,p=o.calculateEndDate({start_date:c.Needed_End_Date,duration:g})):"0"==_||"1"==_?p=o.calculateEndDate({start_date:c.Early_Start_Date,duration:g}):"2"==_||"3"==_?(g-=1,h=o.calculateEndDate({start_date:c.Early_Start_Date,duration:g}),p=h):p=o.calculateEndDate({start_date:c.Early_End_Date,duration:g}),(null==s||s<p)&&(s=p),(null==l||l<h)&&(l=h))}if(null!=i.Actual_Start_Date&&""!=i.Actual_Start_Date){var v=s,y=l;s=i.Actual_Start_Date,i.start_date=i.Actual_Start_Date;var b=o.calculateEndDate({start_date:i.Actual_Start_Date,duration:i.Plan_Duration-1<0?0:i.Plan_Duration-1});if(i.Dynamic_Start_Date=r(i.Actual_Start_Date).format("YYYY-MM-DD[A]"),""!=i.Actual_End_Date&&null!=i.Actual_End_Date)i.Needed_Duration=0,i.Needed_End_Date="",i.Actual_Progress=1,i.progress=1,i.duration=o.calculateDuration({start_date:i.Actual_Start_Date,end_date:i.Actual_End_Date})+1,i.Dynamic_End_Date=r(i.Actual_End_Date).format("YYYY-MM-DD[A]");else{i.Needed_Duration=Math.ceil((1-i.Actual_Progress)*i.Plan_Duration),i.Needed_Start_Date=a,v>a&&(i.Needed_Start_Date=v),i.Needed_End_Date=o.calculateEndDate({start_date:i.Needed_Start_Date,duration:i.Needed_Duration-1}),y>i.Needed_End_Date&&(i.Needed_End_Date=y,v=i.Needed_Start_Date=o.calculateEndDate({start_date:i.Needed_End_Date,duration:-1*i.Needed_Duration+1})),i.Actual_Start_Date>i.Needed_Start_Date&&(i.start_date=i.Needed_Start_Date);var D=o.calculateDuration({start_date:i.Actual_Start_Date,end_date:v>a?v:a});v>a&&(D-=o.calculateDuration({start_date:a,end_date:v})),D=D<0?0:D,i.Actual_Duration=D,i.duration=D+parseInt(i.Needed_Duration),i.Dynamic_End_Date=r(i.Needed_End_Date).format("YYYY-MM-DD"),i.end_date=o.calculateEndDate({start_date:i.Needed_End_Date,duration:1}),i.Dynamic_Duration=i.duration,i.progress=(i.Actual_Duration/i.duration).toFixed(4)}}else{if(null==i.constraint_type||""==i.constraint_type||"asap"==i.constraint_type);else if("snet"==i.constraint_type)s<i.constraint_date&&(s=i.constraint_date);else if("fnet"==i.constraint_type){var S=o.calculateEndDate({start_date:i.constraint_date,duration:-1*parseInt(i.duration)+1});s<S&&(s=S)}else"snlt"==i.constraint_type||"fnlt"==i.constraint_type||"1alap"==i.constraint_type||"mso"==i.constraint_type&&(s=i.constraint_date);var k=o.calculateEndDate({start_date:a,duration:0});if("mso"!==i.constraint_type&&"snlt"!==i.constraint_type&&k>s&&(s=k),i.start_date=s,"milestone"!=i.type){b=o.calculateEndDate({start_date:i.start_date,duration:parseInt(i.Plan_Duration)-1});i.duration=i.Plan_Duration,i.end_date=o.calculateEndDate({start_date:b,duration:1}),i.Needed_Duration=i.Plan_Duration,i.Needed_End_Date=b,i.Needed_Start_Date=s,i.Dynamic_End_Date=r(b).format("YYYY-MM-DD"),i.Dynamic_Start_Date=r(s).format("YYYY-MM-DD"),i.Dynamic_Duration=Math.abs(o.calculateDuration({start_date:s,end_date:b}))+1,i.Plan_Start_Date=s,i.Plan_End_Date=b}else i.Plan_Start_Date=s,i.Plan_Duration=i.duration=i.Needed_Duration=i.Dynamic_Duration=0,i.Dynamic_Start_Date=r(s).format("YYYY-MM-DD"),i.Dynamic_End_Date=i.Dynamic_Start_Date,i.Plan_End_Date=i.Plan_Start_Date;i.progress=0}i.Early_Start_Date=s,"milestone"!=i.type?i.Early_End_Date=o.calculateEndDate({start_date:s,duration:i.duration-1}):i.Early_End_Date=i.Early_Start_Date,i.Start_Difference=0,i.End_Difference=0,i.Duration_Difference=0,i.Target_Start_Date&&(i.Start_Difference=o.calculateDuration({start_date:r(i.Dynamic_Start_Date.split("A")[0]).toDate(),end_date:r(i.Target_Start_Date).toDate()})),i.Target_End_Date&&(i.End_Difference=o.calculateDuration({start_date:r(i.Dynamic_End_Date.split("A")[0]).toDate(),end_date:r(i.Target_End_Date).toDate()})),""!==i.Target_Duration&&null!==i.Target_Duration&&void 0!==i.Target_Duration&&(i.Duration_Difference=i.Target_Duration?Number(i.Target_Duration)-Number(i.Dynamic_Duration):0),i.isCalcforward=!0}}function u(e,t,a,n,i,o){if(0==i.isCalcbackward){var s=null,l=t.filter((function(e){return e.source==i.id}));if(null==l||0==l.length)s=null!=n?n:i.Plan_End_Date;else for(var c=0;c<l.length;c++){var d=l[c].target,f=l[c].type,p=0;null!=l[c].lag&&(p=parseInt(l[c].lag));var h,m=e.filter((function(e){return e.id==d}))[0];if(u(e,t,a,n,m,o),"milestone"!=i.type)if("0"==f)p+=1,h=o.calculateEndDate({start_date:m.Laster_Start_Date,duration:-1*parseInt(p)});else if("1"==f){var _=o.calculateEndDate({start_date:m.Laster_Start_Date,duration:-1*parseInt(p)});h=o.calculateEndDate({start_date:_,duration:m.duration})}else if("2"==f)h=o.calculateEndDate({start_date:m.Laster_End_Date,duration:-1*parseInt(p)});else if("3"==f){p-=1;_=o.calculateEndDate({start_date:m.Laster_End_Date,duration:-1*parseInt(p)});h=o.calculateEndDate({start_date:_,duration:m.duration})}else p+=1,h=o.calculateEndDate({start_date:m.Laster_Start_Date,duration:-1*parseInt(p)});else if("0"==f)h=o.calculateEndDate({start_date:m.Laster_Start_Date,duration:-1*parseInt(p)});else if("1"==f){_=o.calculateEndDate({start_date:m.Laster_Start_Date,duration:-1*parseInt(p)});h=_}else if("2"==f)p-=1,h=o.calculateEndDate({start_date:m.Laster_End_Date,duration:-1*parseInt(p)});else if("3"==f){p-=1;_=o.calculateEndDate({start_date:m.Laster_End_Date,duration:-1*parseInt(p)});h=_}else h=o.calculateEndDate({start_date:m.Laster_Start_Date,duration:-1*parseInt(p)});(null==s||s>h)&&(s=h)}if(null==i.constraint_type||""==i.constraint_type||"asap"==i.constraint_type);else if("snet"==i.constraint_type);else if("fnet"==i.constraint_type);else if("snlt"==i.constraint_type){var g=o.calculateEndDate({start_date:i.constraint_date,duration:i.duration});s>g&&(s=g)}else if("fnlt"==i.constraint_type)s>i.constraint_date&&(s=i.constraint_date);else if("1"==i.constraint_type){var v=o.calculateEndDate({start_date:s,duration:i.duration+1}),y=o.calculateEndDate({start_date:a,duration:1});if(y>v&&(v=y),i.start_date=v,i.Plan_Start_Date=v,"milestone"!=i.type){var b=o.calculateEndDate({start_date:i.Plan_Start_Date,duration:i.Plan_Duration});i.Plan_End_Date=b,i.duration=i.Plan_Duration,i.Needed_Duration=i.Plan_Duration,i.Needed_End_Date=i.Plan_End_Date,i.Dynamic_End_Date=r(i.Plan_End_Date).format("YYYY-MM-DD")}}"milestone"!=i.type?(i.Laster_End_Date=s,i.Laster_Start_Date=o.calculateEndDate({start_date:i.Laster_End_Date,duration:-1*parseInt(i.duration)+1})):(i.Laster_End_Date=s,i.Laster_Start_Date=s),i.isCalcbackward=!0}}function f(e,t){var a=t.filter((function(t){return t.parent==e.id&&("task"==t.type||"milestone"==t.type)})),n=a,r=t.filter((function(t){return t.parent==e.id&&"project"==t.type}));if(null!=r&&r.length>0)for(var i=0;i<r.length;i++){var o=f(r[i],t);n=n.concat(o)}return n}},"19d9":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"expandable-section",class:[e.isExpanded?"expand":"shrink"],style:{width:e.computedWidth},attrs:{id:"sliderLeft"}},[a("el-tooltip",{attrs:{disabled:e.toolTipDisabled,content:e.isExpanded?"收起":"展开",placement:"top"}},[a("div",{staticClass:"toggle-button",on:{click:function(t){return t.stopPropagation(),e.toggleContent(t)}}},[a("span",{staticClass:"icon-x"},[a("i",{class:e.isExpanded?"el-icon-s-fold":"el-icon-s-unfold"})])])]),a("div",{staticClass:"content"},[e._t("default")],2),e.isExpanded?a("div",{directives:[{name:"resize-width",rawName:"v-resize-width"}],staticClass:"drag-bar"}):e._e()],1)},r=[]},"1adb":function(e,t,a){"use strict";a("0553")},"1b9a":function(e,t,a){"use strict";a.r(t);var n=a("ba7d"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"1be0":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("tree-detail",{ref:"tree",attrs:{"show-checkbox":"","expanded-key":e.expandedKey,loading:e.treeLoading,"tree-data":e.treeData,"can-node-click":!1,icon:"icon-user","node-key":"Id","check-strictly":"","default-checked-keys":e.defaultCheckedKeys},on:{getCheckedNodes:e.getCheckedNodes,check:e.check}})],1)},r=[]},"1c1f":function(e,t,a){"use strict";a.r(t);var n=a("a2db"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"1c87":function(e,t,a){"use strict";a("7359")},"1c89":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:e.multiple,disabled:e.disabled},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},[e.hasNoProject?a("el-option",{attrs:{label:"无项目",value:"无项目"}}):e._e(),e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})}))],2)},r=[]},"1d04":function(e,t,a){"use strict";a.r(t);var n=a("9140"),r=a("3f4f");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"1d08":function(e,t,a){},"1dfa":function(e,t,a){"use strict";a.r(t);var n=a("b926"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"1f399":function(e,t,a){},"1f58":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"task-detail"},[a("el-link",{staticClass:"closeme",attrs:{underline:!1,icon:"el-icon-close"},on:{click:e.closeMe}}),a("el-tabs",{attrs:{type:"border-card"},model:{value:e.tabName,callback:function(t){e.tabName=t},expression:"tabName"}},[a("el-tab-pane",{attrs:{label:"人工资源分析",name:"analzer"}},[a("div",{staticClass:"a-panel"},[a("div",{staticClass:"board"},[a("div",{staticClass:"item"},[a("el-image",{staticStyle:{width:"184px",height:"104px"},attrs:{src:e.boards.p}}),a("div",{staticClass:"content"},[a("h3",[e._v("计划总工日")]),a("p",[a("strong",[e._v(e._s(Number(e.planNum||0).toFixed(2)))])])])],1),a("div",{staticClass:"item"},[a("el-image",{staticStyle:{width:"184px",height:"104px"},attrs:{src:e.boards.b}}),a("div",{staticClass:"content"},[a("h3",[e._v("实际总工日")]),a("p",[a("strong",[e._v(e._s(Number(e.actNum||0).toFixed(2)))])])])],1),a("div",{staticClass:"item"},[a("el-image",{staticStyle:{width:"184px",height:"104px"},attrs:{src:e.boards.y}}),a("div",{staticClass:"content"},[a("h3",[e._v("剩余总工日")]),a("p",[a("strong",[e._v(e._s(Number(e.subNum||0).toFixed(2)))])])])],1),a("div",{staticClass:"item"},[a("el-image",{staticStyle:{width:"184px",height:"104px"},attrs:{src:e.boards.o}}),a("div",{staticClass:"content"},[a("h3",[e._v("高峰期预计工日")]),a("p",[a("strong",[e._v(e._s(Number(e.highNum||0).toFixed(2)))])])])],1)]),a("div",{staticClass:"chart-wrap"},[a("span",[e._v("单位：工日")]),a("div",{attrs:{id:"analyzer-charts"}})])])])],1)],1)},r=[]},21316:function(e,t,a){},"21f5":function(e,t,a){"use strict";a.r(t);var n=function(e,t){t._c;return t._m(0)},r=[function(e,t){var a=t._c;return a("div",{staticClass:"empty-x"},[a("div",{staticClass:"empty"}),a("p",[t._v("暂无内容")])])}],i=(a("e0f6"),a("7b1b"),a("2877")),o={},s=Object(i["a"])(o,n,r,!0,null,"7dca3f3a",null);t["default"]=s.exports},2305:function(e,t,a){"use strict";a.r(t);var n=a("1be0"),r=a("8781");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"449faa5b",null);t["default"]=s.exports},2348:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4e82");var n=a("472f");t.default={name:"TargetSet",props:{plan:{type:Object,default:function(){return{}}},editMode:{type:Boolean,default:!1}},data:function(){return{list:[],form:{target:""},editmode:!1}},computed:{sortedList:function(){var e=this,t=this.list.concat([]);return t.sort((function(t,a){return t.Id===e.plan.Id?-1:1})),t}},created:function(){var e=this;this.editmode=this.editMode,this.form.target=this.plan.Target_Plan_Id||"",(0,n.GetPlanList)({Page:1,PageSize:200}).then((function(t){t.IsSucceed&&(e.list=t.Data)}))},methods:{submit:function(){if(!this.editmode)return this.$emit("dialogCancel");this.$emit("dialogFormSubmitSuccess",{type:"setTargetPlan",data:this.form.target})}}}},24286:function(e,t,a){"use strict";var n=a("dbce").default,r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530"));a("99af"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var o=r(a("d682")),s=r(a("262c")),l=r(a("4f1a")),c=r(a("61ce")),d=n(a("313e")),u=a("472f"),f=[{legend:"计划人工",type:"bar",itemStyle:{color:"#2962FF"},prop:"PlanManDay"},{legend:"实际人工",type:"bar",itemStyle:{color:"#11C6FF"},prop:"ActualManDay"},{legend:"计划累计",type:"line",yAxisIndex:1,lineStyle:{color:"#FF75B1"},itemStyle:{color:"#FF75B1"},prop:"SumPlanManDay"},{legend:"实际累计",type:"line",yAxisIndex:1,lineStyle:{color:"#F5C15A"},itemStyle:{color:"#F5C15A"},prop:"SumActualManDay"}];t.default={name:"ResourceAnalyzer",props:{plan:{type:Object,default:function(){return{}}}},data:function(){return{tabName:"analzer",chart:null,option:{tooltip:{trigger:"axis",appendToBody:!0,backgroundColor:"#434F71",borderColor:"#434F71",textStyle:{color:"#FFF"},axisPointer:{type:"cross",crossStyle:{color:"#999"}}},grid:{right:140,left:50,top:18,bottom:64},legend:{left:"right",orient:"vertical",top:32,align:"right",data:f.map((function(e){return e.legend}))},xAxis:[{type:"category",data:[],splitLine:{show:!0},axisPointer:{type:"shadow"}}],yAxis:[{type:"value",name:"",min:0,axisLabel:{formatter:"{value}"}},{type:"value",name:"",min:0,axisLabel:{formatter:"{value}"}}],series:[]},boards:{p:o.default,b:s.default,y:l.default,o:c.default},report:[],planNum:0,actNum:0,subNum:0,highNum:0}},computed:{computedMonths:function(){return this.report.map((function(e){return"".concat(e.Year,"-").concat(e.Month)}))},dataSeries:function(){var e=this;return f.map((function(t){var a=[];a=e.report.map((function(e){return e[t.prop]}));var n=(0,i.default)((0,i.default)({},t),{},{name:t["legend"],data:a});return delete n["legend"],delete n["prop"],n}))}},created:function(){},mounted:function(){var e=this;this.chart=d.init(document.getElementById("analyzer-charts")),this.plan&&this.plan.Id&&this.getReport().then((function(){e.option.xAxis[0].data=e.computedMonths;var t=(0,i.default)((0,i.default)({},e.option),{},{series:e.dataSeries});e.chart.setOption(t)}))},methods:{closeMe:function(){this.$emit("drawerCancel")},getReport:function(){var e=this;return(0,u.GetPlanResouseReport)(this.plan.Id,"month").then((function(t){t.IsSucceed&&(e.report=t.Data,null!=t.Data&&t.Data.length>0&&(e.planNum=t.Data[t.Data.length-1].SumPlanManDay,e.actNum=t.Data[t.Data.length-1].SumActualManDay,e.subNum=e.planNum-e.actNum,e.highNum=t.Data[t.Data.length-1].HighManDay))}))}}}},"261d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("a630"),a("a15b"),a("14d9"),a("e9f5"),a("910d"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("ddb0");var r=n(a("5530")),i=a("def8"),o=n(a("611b"));a("c78c");var s=a("2f62");t.default={components:{vdr:o.default},props:["select","selectGroup","node","plumb","currentTool","isShowContent"],data:function(){return{contentHtml:"",currentSelectGroup:this.selectGroup,setInfo:{NodeRejectType:0,NodeConfluenceType:"",NodeDesignate:"",ThirdPartyUrl:"",NodeDesignateData:{users:[],roles:[],departments:[],usersText:"",rolesText:"",departmentsText:"",Texts:""}}}},mounted:function(){this.isShowContent||this.registerNode()},computed:(0,r.default)({},(0,s.mapGetters)({currentSelect:"currentSelect"})),methods:(0,r.default)((0,r.default)({},(0,s.mapActions)({saveCurrentSelect:"saveCurrentSelect"})),{},{onResize:function(e,t,a,n){this.node.width=a,this.node.height=n},registerNode:function(){var e=this;e.node.id&&e.plumb.draggable(e.node.id,{containment:"parent",handle:function(e,t){for(var a=t.parentNode.querySelectorAll(".common-circle-node,.common-rectangle-node,.common-diamond-node,.lane-text-div"),n=0;n<a.length;n++)if(a[n]===t||"lane-text"===e.target.className)return!0;return!1},grid:i.flowConfig.defaultStyle.alignGridPX,drag:function(t){"x-lane"!==e.node.type&&"y-lane"!==e.node.type||(e.node.left=t.pos[0],e.node.top=t.pos[1]),i.flowConfig.defaultStyle.isOpenAuxiliaryLine&&e.$emit("alignForLine",t)},stop:function(t){"x-lane"!==e.node.type&&"y-lane"!==e.node.type&&(e.node.left=t.pos[0],e.node.top=t.pos[1]),e.currentSelectGroup.length>1&&e.$emit("updateNodePos"),e.$emit("hideAlignLine")}});var t=(0,r.default)((0,r.default)({},e.node),{},{setInfo:e.node.setInfo||this.setInfo});e.saveCurrentSelect(t),e.currentSelectGroup=[]},selectNode:function(){if(!(this.currentSelectGroup.length>0)){var e=this,t=Object.assign({},this.node);e.saveCurrentSelect(t),e.$emit("isMultiple",(function(t){if(t){var a=e.currentSelectGroup.filter((function(t){return t.id===e.node.id}));a.length<=0&&(e.plumb.addToDragSelection(e.node.id),e.currentSelectGroup.push(e.node))}else e.currentSelectGroup=[]}))}},showNodeContextMenu:function(e){this.$emit("showNodeContextMenu",e),this.selectNode()},tagStatusClass:function(e){var t="iconfont  icon-bookmark-filled ";switch(e.Taged){case 1:t+="tag-pass-bg";break;case 2:t+="tag-not-bg";break;case 3:t+="tag-back-bg";break}return t},stateClass:function(){var e="";return this.isActive()&&(e+="active"),void 0!==this.node.setInfo&&null!==this.node.setInfo&&void 0!==this.node.setInfo.Taged&&null!==this.node.setInfo.Taged&&(2===this.node.setInfo.Taged?e+=" node-not-bg":1===this.node.setInfo.Taged?e+=" node-pass-bg":e+=" node-back-bg"),e},isActive:function(){var e=this;if(e.currentSelect.id===e.node.id)return!0;var t=e.currentSelectGroup.filter((function(t){return t.id===e.node.id}));return t.length>0},isDisabled:function(){var e=!0;if(void 0!==this.node.setInfo&&null!==this.node.setInfo&&"end round"!==this.node.type&&"start round mix"!==this.node.type){var t,a,n,r,i,o,s,l='<div style="text-align:left">',c={1:"通过",2:"不通过",3:"驳回"};l+="<p>处理人："+(this.node.setInfo.Taged?this.node.setInfo.UserName||"":"ALL_USER"===this.node.setInfo.NodeDesignate?"所有人":Array.from(new Set([null===(t=this.node.setInfo)||void 0===t?void 0:t.NodeDesignateData.usersText,null===(a=this.node.setInfo)||void 0===a?void 0:a.NodeDesignateData.rolesText,null===(n=this.node.setInfo)||void 0===n?void 0:n.NodeDesignateData.departmentsText,null===(r=this.node.setInfo)||void 0===r?void 0:r.NodeDesignateData.groupText])).filter((function(e){return Boolean(e)})).join(","))+"</p>",l+="<p>结果："+(null!==(i=c[this.node.setInfo.Taged])&&void 0!==i?i:"")+"</p>",l+="<p>处理时间："+(null!==(o=this.node.setInfo.TagedTime)&&void 0!==o?o:"")+"</p>",l+="<p>备注："+(null!==(s=this.node.setInfo.Description)&&void 0!==s?s:"")+"</p></div>",this.node.addInfo&&null!==this.node.addInfo.Taged&&(l+='<hr style="margin:5px"/>',l+="<p>【加签】</p>",l+="<p>处理人："+this.node.addInfo.UserName+"</p>",l+="<p>结果："+c[this.node.addInfo.Taged]+"</p>",l+="<p>处理时间："+this.node.addInfo.TagedTime+"</p>",l+="<p>备注："+(this.node.addInfo.Description||"")+"</p></div>"),this.contentHtml=l,e=!1}return e}}),watch:{selectGroup:function(e){this.currentSelectGroup=e},currentSelectGroup:{handler:function(e){this.$emit("update:selectGroup",e)},deep:!0}}}},"287a":function(e,t,a){"use strict";a.r(t);var n=a("042b");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);var i,o,s=a("2877"),l=Object(s["a"])(n["default"],i,o,!1,null,null,null);t["default"]=l.exports},"289b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("ade3")),i=n(a("3835")),o=n(a("2909"));a("99af"),a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("13d5"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("9485"),a("a732"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("159b"),a("ddb0");var s=a("3741");t.default={name:"WlTreeTransfer",props:{sjr:{type:Array,default:function(){return[]}},csr:{type:Array,default:function(){return[]}},msr:{type:Array,default:function(){return[]}},width:{type:String,default:"100%"},height:{type:String,default:"320px"},title:{type:Array,default:function(){return["源列表","目标列表"]}},button_text:Array,all_data:{type:Array,default:function(){return[]}},from_data:{type:Array,default:function(){return[]}},to_data:{type:Array,default:function(){return[]}},defaultProps:{type:Object,default:function(){return{label:"label",children:"children"}}},node_key:{type:String,default:"id"},pid:{type:String,default:"pid"},rootPidValue:{type:[String,Number],default:0},upDownDisable:{type:Boolean,default:!1},filter:{type:Boolean,default:!1},showbutton:{type:Boolean,default:!1},openAll:{type:Boolean,default:!1},renderContentLeft:Function,renderContentRight:Function,mode:{type:String,default:"transfer"},addressOptions:{type:Object,default:function(){return{num:3,suffix:"suffix",connector:"-"}}},transferOpenNode:{type:Boolean,default:!0},defaultCheckedKeys:{type:Array,default:function(){return[]}},defaultExpandedKeys:{type:Array,default:function(){return[]}},placeholder:{type:String,default:"输入关键字进行过滤"},filterNode:Function,defaultTransfer:{type:Boolean,default:!1},arrayToTree:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1},lazyRight:{type:Boolean,default:!1},lazyFn:Function,highLight:{type:Boolean,default:!1},checkStrictly:{type:Boolean,default:!1},accordion:{type:Boolean,default:!1},renderAfterExpand:{type:Boolean,default:!0},expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:{type:Boolean,default:!1},indent:{type:Number,default:16},iconClass:String,draggable:Boolean,allowDrag:Function,allowDrop:Function},data:function(){return{from_is_indeterminate:!1,from_check_all:!1,to_is_indeterminate:!1,to_check_all:!1,from_expanded_keys:[],to_expanded_keys:[],from_disabled:!0,to_disabled:!0,from_check_keys:[],to_check_keys:[],filterFrom:"",filterTo:"",filterListFirst:"",filterListSecond:"",filterListThird:"",archiveFirst:[],archiveSecond:[],archiveThird:[],addressee:[],Cc:[],secret_receiver:[],move_up:!1}},computed:{self_from_data:function(){var e=(0,o.default)(this.from_data);return this.arrayToTree?(0,s.arrayToTree)(e,{id:this.node_key,pid:this.pid,children:this.defaultProps.children}):e},self_to_data:function(){var e=(0,o.default)(this.to_data);return this.arrayToTree?(0,s.arrayToTree)(e,{id:this.node_key,pid:this.pid,children:this.defaultProps.children}):e},fromTitle:function(){var e=(0,i.default)(this.title,1),t=e[0];return t},toTitle:function(){var e=(0,i.default)(this.title,2),t=e[1];return t},toTitleSecond:function(){var e=(0,i.default)(this.title,3),t=e[2];return t},toTitleThird:function(){var e=(0,i.default)(this.title,4),t=e[3];return t},fromButton:function(){if(void 0!=this.button_text){var e=(0,i.default)(this.button_text,1),t=e[0];return t}},toButton:function(){if(void 0!=this.button_text){var e=(0,i.default)(this.button_text,2),t=e[1];return t}}},watch:{from_check_keys:function(e){var t=this;if(e.length>0){this.from_disabled=!1,this.from_is_indeterminate=!0;var a=e.filter((function(e){return 0==e[t.pid]}));a.length==this.self_from_data.length?(this.from_is_indeterminate=!1,this.from_check_all=!0):(this.from_is_indeterminate=!0,this.from_check_all=!1)}else this.from_disabled=!0,this.from_is_indeterminate=!1,this.from_check_all=!1},to_check_keys:function(e){var t=this;if(e.length>0){this.to_disabled=!1,this.to_is_indeterminate=!0;var a=e.filter((function(e){return 0==e[t.pid]}));a.length==this.self_to_data.length?(this.to_is_indeterminate=!1,this.to_check_all=!0):(this.to_is_indeterminate=!0,this.to_check_all=!1)}else this.to_disabled=!0,this.to_is_indeterminate=!1,this.to_check_all=!1},filterFrom:function(e){this.$refs["from-tree"].filter(e)},filterTo:function(e){this.$refs["to-tree"].filter(e)},filterListFirst:function(e,t){""==t&&(this.archiveFirst=this.addressee),""==e&&(this.addressee=this.archiveFirst);var a=RegExp(e);this.addressee=this.addressee.filter((function(e){return a.test(e.label)}))},filterListSecond:function(e,t){""==t&&(this.archiveSecond=this.Cc),""==e&&(this.Cc=this.archiveSecond);var a=RegExp(e);this.Cc=this.Cc.filter((function(e){return a.test(e.label)}))},filterListThird:function(e,t){""==t&&(this.archiveThird=this.secret_receiver),""==e&&(this.secret_receiver=this.archiveThird);var a=RegExp(e);this.secret_receiver=this.secret_receiver.filter((function(e){return a.test(e.label)}))},defaultCheckedKeys:{handler:function(e){var t=this;this.from_check_keys=e||[],this.defaultTransfer&&this.from_check_keys.length&&this.$nextTick((function(){t.addToAims(!1)}))},immediate:!0},defaultExpandedKeys:{handler:function(e){var t=new Set(this.from_expanded_keys.concat(e));this.from_expanded_keys=(0,o.default)(t);var a=new Set(this.to_expanded_keys.concat(e));this.to_expanded_keys=(0,o.default)(a)},immediate:!0},sjr:{handler:function(e){var t;(t=this.addressee).push.apply(t,(0,o.default)(e))},immediate:!0},csr:{handler:function(e){var t;(t=this.Cc).push.apply(t,(0,o.default)(e))},immediate:!0},msr:{handler:function(e){var t;(t=this.secret_receiver).push.apply(t,(0,o.default)(e))},immediate:!0}},methods:{cancel:function(){this.$emit("cancelDialog")},submit:function(){this.$emit("submitDialog",this.self_to_data)},addToAims:function(e){var t=this,a=this.$refs["from-tree"].getCheckedKeys(),n=this.$refs["from-tree"].getHalfCheckedKeys(),r=this.$refs["from-tree"].getCheckedNodes(),i=JSON.parse(JSON.stringify(r)),o=this.$refs["from-tree"].getHalfCheckedNodes(),l=JSON.parse(JSON.stringify(o)),c=this.defaultProps.children||"children",d=this.pid||"pid",u=this["node_key"]||"id",f=this.rootPidValue||0;if(this.checkStrictly)this.checkStrictlyTransfer(r,{children__:c,pid__:d,id__:u,root__:f},!0);else{var p=JSON.parse(JSON.stringify(o)),h=[];p.forEach((function(e){var a=(0,s.valInDeep)(t.self_to_data,e[u],u,c);a.length||h.push(e)})),h.forEach((function(e){e[c]=[],f!==e[d]?t.$refs["to-tree"].append(e,e[d]):t.$refs["to-tree"].append(e)}));var m=[];i.forEach((function(e){var a=(0,s.valInDeep)(t.self_to_data,e[u],u,c);a.length||m.push(e)})),m.forEach((function(e){e[c]&&e[c].length>0&&(e[c]=[],f!==e[d]?t.$refs["to-tree"].append(e,e[d]):t.$refs["to-tree"].append(e))}));var _=r.filter((function(e){return!e[c]||0==e[c].length}));_.forEach((function(e){var a=(0,s.valInDeep)(t.self_to_data,e[u],u,c);a.length||t.$refs["to-tree"].append(e,e[d])})),r.map((function(e){return t.$refs["from-tree"].remove(e)}))}this.from_check_keys=[],this.$refs["to-tree"].setCheckedKeys([]),this.to_check_all=!1,this.to_is_indeterminate=!1,this.transferOpenNode&&!this.lazy&&(this.to_expanded_keys=a),e&&this.$emit("add-btn",this.self_from_data,this.self_to_data,{keys:a,nodes:i,harfKeys:n,halfNodes:l}),this.$refs["from-tree"].setCheckedKeys([])},addToAimsSort:function(e){var t=this,a=this.$refs["from-tree"].getCheckedKeys(),n=this.$refs["from-tree"].getHalfCheckedKeys(),r=this.$refs["from-tree"].getCheckedNodes(),i=JSON.parse(JSON.stringify(r)),o=this.$refs["from-tree"].getHalfCheckedNodes(),l=JSON.parse(JSON.stringify(o)),c=this.defaultProps.children||"children",d=this.pid||"pid",u=this["node_key"]||"id",f=this.rootPidValue||0;if(this.checkStrictly)this.checkStrictlyTransfer(r,{children__:c,pid__:d,id__:u,root__:f},!0);else{var p=JSON.parse(JSON.stringify(o)),h=[];p.forEach((function(e){var a=(0,s.valInDeep)(t.self_to_data,e[u],u,c);a.length||h.push(e)}));var m=[];i.forEach((function(e){var a=(0,s.valInDeep)(t.self_to_data,e[u],u,c);a.length||m.push(e)}));var _=r.filter((function(e){return!e[c]||0==e[c].length}));_.forEach((function(e){t.$refs["to-tree"].append(e)})),r.map((function(e){return t.$refs["from-tree"].remove(e)}))}this.from_check_keys=[],this.$refs["to-tree"].setCheckedKeys([]),this.to_check_all=!1,this.to_is_indeterminate=!1,this.transferOpenNode&&!this.lazy&&(this.to_expanded_keys=a),e&&this.$emit("add-btn",this.self_from_data,this.self_to_data,{keys:a,nodes:i,harfKeys:n,halfNodes:l}),this.$refs["from-tree"].setCheckedKeys([])},removeToSource:function(){var e=this,t=this.$refs["to-tree"].getCheckedKeys(),a=this.$refs["to-tree"].getHalfCheckedKeys(),n=this.$refs["to-tree"].getCheckedNodes(),r=JSON.parse(JSON.stringify(n)),i=this.$refs["to-tree"].getHalfCheckedNodes(),o=JSON.parse(JSON.stringify(i)),l=this.defaultProps.children||"children",c=this.pid||"pid",d=this["node_key"]||"id",u=this.rootPidValue||0;if(this.checkStrictly)this.checkStrictlyTransfer(n,{children__:l,pid__:c,id__:d,root__:u},!1);else{var f=JSON.parse(JSON.stringify(i)),p=[];f.forEach((function(t){var a=(0,s.valInDeep)(e.self_from_data,t[d],d,l);a.length||p.push(t)})),p.forEach((function(t){t[l]=[],u!==t[c]?e.$refs["from-tree"].append(t,t[c]):e.$refs["from-tree"].append(t)}));var h=[];r.forEach((function(t){var a=(0,s.valInDeep)(e.self_from_data,t[d],d,l);a.length||h.push(t)})),h.forEach((function(t){t[l]&&t[l].length>0&&(t[l]=[],u!==t[c]?e.$refs["from-tree"].append(t,t[c]):e.$refs["from-tree"].append(t))}));var m=n.filter((function(e){return!e[l]||0==e[l].length}));m.forEach((function(t){var a=(0,s.valInDeep)(e.self_from_data,t[d],d,l);a.length||e.$refs["from-tree"].append(t,t[c])})),n.map((function(t){return e.$refs["to-tree"].remove(t)}))}this.to_check_keys=[],this.$refs["from-tree"].setCheckedKeys([]),this.from_check_all=!1,this.from_is_indeterminate=!1,this.transferOpenNode&&!this.lazy&&(this.from_expanded_keys=t),this.$emit("remove-btn",this.self_from_data,this.self_to_data,{keys:t,nodes:r,harfKeys:a,halfNodes:o}),this.$refs["to-tree"].setCheckedKeys([])},removeToSourceSort:function(){var e=this,t=this.$refs["to-tree"].getCheckedKeys(),a=this.$refs["to-tree"].getHalfCheckedKeys(),n=this.$refs["to-tree"].getCheckedNodes(),r=JSON.parse(JSON.stringify(n)),i=this.$refs["to-tree"].getHalfCheckedNodes(),o=JSON.parse(JSON.stringify(i)),l=this.defaultProps.children||"children",c=this.pid||"pid",d=this["node_key"]||"id",u=this.rootPidValue||0;if(this.checkStrictly)this.checkStrictlyTransfer(n,{children__:l,pid__:c,id__:d,root__:u},!1);else{var f=n.filter((function(e){return!e[l]||0==e[l].length}));f.forEach((function(t){var a=(0,s.valInDeep)(e.self_from_data,t[d],d,l);if(!a.length){if(null!=t[c]){var n=(0,s.valInDeep)(e.self_from_data,t[c],d,l);if(!n.length){var r=e.all_data.filter((function(e){return e[d]==t[c]}));null!=r&&r.length>0&&e.$refs["from-tree"].append(r[0])}}e.$refs["from-tree"].append(t,t[c])}})),n.map((function(t){return e.$refs["to-tree"].remove(t)}))}this.to_check_keys=[],this.$refs["from-tree"].setCheckedKeys([]),this.from_check_all=!1,this.from_is_indeterminate=!1,this.transferOpenNode&&!this.lazy&&(this.from_expanded_keys=t),this.$emit("remove-btn",this.self_from_data,this.self_to_data,{keys:t,nodes:r,harfKeys:a,halfNodes:o}),this.$refs["to-tree"].setCheckedKeys([])},upData:function(){var e=this["node_key"]||"id",t=this.defaultProps.children||"children",a=this.$refs["to-tree"].getCheckedNodes(),n=a.filter((function(e){return!e[t]||0==e[t].length}));if(n.length<0)this.$message({type:"warning",message:"请选择节点"});else if(n.length>1)this.$message({type:"warning",message:"仅支持单选调顺序"});else{for(var r,i=this.self_to_data.length-1;i>=0;i--)if(this.self_to_data[i][e]==a[0][e]&&i>=1){r=this.self_to_data[i-1];break}null!=r&&(this.$refs["to-tree"].remove(a[0]),this.$refs["to-tree"].insertBefore(a[0],r),this.$emit("sort-btn",this.self_from_data,this.self_to_data,a[0][e]),this.$refs["to-tree"].setChecked(a[0][e],!0))}},downData:function(){var e=this["node_key"]||"id",t=this.defaultProps.children||"children",a=this.$refs["to-tree"].getCheckedNodes(),n=a.filter((function(e){return!e[t]||0==e[t].length}));if(n.length<0)this.$message({type:"warning",message:"请选择节点"});else if(n.length>1)this.$message({type:"warning",message:"仅支持单选调顺序"});else{for(var r,i=0;i<this.self_to_data.length;i++)if(this.self_to_data[i][e]==a[0][e]&&i<=this.self_to_data.length-2){r=this.self_to_data[i+1];break}null!=r&&(this.$refs["to-tree"].remove(a[0]),this.$refs["to-tree"].insertAfter(a[0],r),this.$emit("sort-btn",this.self_from_data,this.self_to_data,a[0][e]),this.$refs["to-tree"].setChecked(a[0][e],!0))}},checkStrictlyTransfer:function(e,t,a){var n=this,i=[],o=[],l="",c="";a?(i=this.self_from_data,o=this.self_to_data,l="from-tree",c="to-tree"):(i=this.self_to_data,o=this.self_from_data,l="to-tree",c="from-tree");var d=e.map((function(e){var a=Object.assign({},e,(0,r.default)((0,r.default)({},t.children__,[]),"__childrenLength",Array.isArray(e[t.children__])?e[t.children__].length:0));return a})),u=d.reduce((function(e,a,n,r){var i=r.find((function(e){return e[t.id__]==a[t.pid__]}));return i?(Array.isArray(i[t.children__])?i[t.children__].push(a):i[t.children__]=[a],e):e.concat(a)}),[]);u.forEach((function(e){var r=(0,s.valInDeep)(o,e[t.id__],t.id__,t.children__);if(r.length)n.$refs[l].remove(e);else{var d=Array.isArray(e[t.children__])?e[t.children__].length:0,u=d===e.__childrenLength;delete e.__childrenLength,n.findParentInTarget(e,t,i,o,l,c,a),u&&n.$refs[l].remove(e)}}))},findParentInTarget:function(e,t,a,n,o,l,c){var d=this,u=(0,s.valInDeep)(n,e[t.pid__],t.id__,t.children__);if(u.length){if(this.$refs[l].append(e,e[t.pid__]),!c)return;var f=(0,s.valInDeep)(a,e[t.pid__],t.id__,t.children__),p=(0,i.default)(f,1),h=p[0];this.$nextTick((function(){0===h[t.children__].length&&d.$refs[o].remove(h)}))}else if(e[t.pid__]!==t.root__){var m=(0,s.valInDeep)(a,e[t.pid__],t.id__,t.children__),_=(0,i.default)(m,1),g=_[0],v=Object.assign({},g,(0,r.default)({},t.children__,[e]));c&&this.$refs[o].remove(e),this.findParentInTarget(v,t,a,n,o,l,c)}else this.$refs[l].append(e)},leftloadNode:function(e,t){this.lazyFn&&this.lazyFn(e,t,"left")},rightloadNode:function(e,t){this.lazyFn&&this.lazyFn(e,t,"right")},fromTreeChecked:function(e,t){var a=this;this.from_check_keys=t.checkedNodes,this.$nextTick((function(){a.$emit("left-check-change",e,t,a.from_check_all)}))},toTreeChecked:function(e,t){var a=this;this.to_check_keys=t.checkedNodes,this.$nextTick((function(){a.$emit("right-check-change",e,t,a.to_check_all)}))},fromAllBoxChange:function(e){0!=this.self_from_data.length&&(e?(this.from_check_keys=this.self_from_data,this.$refs["from-tree"].setCheckedNodes(this.self_from_data)):(this.$refs["from-tree"].setCheckedNodes([]),this.from_check_keys=[]),this.$emit("left-check-change",null,null,this.from_check_all))},toAllBoxChange:function(e){0!=this.self_to_data.length&&(e?(this.to_check_keys=this.self_to_data,this.$refs["to-tree"].setCheckedNodes(this.self_to_data)):(this.$refs["to-tree"].setCheckedNodes([]),this.to_check_keys=[]),this.$emit("right-check-change",null,null,this.to_check_all))},filterNodeFrom:function(e,t){return this.filterNode?this.filterNode(e,t,"form"):!e||-1!==t[this.defaultProps.label].indexOf(e)},filterNodeTo:function(e,t){return this.filterNode?this.filterNode(e,t,"to"):!e||-1!==t[this.defaultProps.label].indexOf(e)},addressListTransfer:function(e){var t=this,a=this.$refs["from-tree"].getCheckedNodes(!0),n=[];switch(e){case 0:n=a.filter((function(e){if(!t.addressee.some((function(a){return a[t.node_key]==e[t.node_key]})))return e})),this.addressee=[].concat((0,o.default)(this.addressee),(0,o.default)(n));break;case 1:n=a.filter((function(e){if(!t.Cc.some((function(a){return a[t.node_key]==e[t.node_key]})))return e})),this.Cc=[].concat((0,o.default)(this.Cc),(0,o.default)(n));break;case 2:n=a.filter((function(e){if(!t.secret_receiver.some((function(a){return a[t.node_key]==e[t.node_key]})))return e})),this.secret_receiver=[].concat((0,o.default)(this.secret_receiver),(0,o.default)(n));break}this.$refs["from-tree"].setCheckedKeys([]),this.from_check_keys=[],this.$emit("add-btn",this.addressee,this.Cc,this.secret_receiver)},clearList:function(e,t){var a=this;switch(e){case 0:this.addressee="all"==t?[]:this.addressee.filter((function(e){return e[a.node_key]!=t}));break;case 1:this.Cc="all"==t?[]:this.Cc.filter((function(e){return e[a.node_key]!=t}));break;case 2:this.secret_receiver="all"==t?[]:this.secret_receiver.filter((function(e){return e[a.node_key]!=t}));break}this.$emit("remove-btn",this.addressee,this.Cc,this.secret_receiver)},moveUp:function(e){this.move_up="up"==e},nodeDragStartLeft:function(e,t){this.$emit("node-drag-start","left",e,t)},nodeDragEnterLeft:function(e,t,a){this.$emit("node-drag-enter","left",e,t,a)},nodeDragLeaveLeft:function(e,t,a){this.$emit("node-drag-leave","left",e,t,a)},nodeDragOverLeft:function(e,t,a){this.$emit("node-drag-over","left",e,t,a)},nodeDragEndLeft:function(e,t,a,n){this.$emit("node-drag-end","left",e,t,a,n)},nodeDropLeft:function(e,t,a,n){this.$emit("node-drop","left",e,t,a,n)},nodeDragStartRight:function(e,t){this.$emit("node-drag-start","right",e,t)},nodeDragEnterRight:function(e,t,a){this.$emit("node-drag-enter","right",e,t,a)},nodeDragLeaveRight:function(e,t,a){this.$emit("node-drag-leave","right",e,t,a)},nodeDragOverRight:function(e,t,a){this.$emit("node-drag-over","right",e,t,a)},nodeDragEndRight:function(e,t,a,n){this.$emit("node-drag-end","right",e,t,a,n)},nodeDropRight:function(e,t,a,n){this.$emit("node-drop","right",e,t,a,n)},clearChecked:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"all";"left"===e?(this.$refs["from-tree"].setCheckedKeys([]),this.from_is_indeterminate=!1,this.from_check_all=!1):"right"===e?(this.$refs["to-tree"].setCheckedKeys([]),this.to_is_indeterminate=!1,this.to_check_all=!1):(this.$refs["from-tree"].setCheckedKeys([]),this.$refs["to-tree"].setCheckedKeys([]),this.from_is_indeterminate=!1,this.from_check_all=!1,this.to_is_indeterminate=!1,this.to_check_all=!1)},getChecked:function(){var e=this.$refs["from-tree"].getCheckedKeys(),t=this.$refs["from-tree"].getHalfCheckedKeys(),a=this.$refs["from-tree"].getCheckedNodes(),n=this.$refs["from-tree"].getHalfCheckedNodes(),r=this.$refs["to-tree"].getCheckedKeys(),i=this.$refs["to-tree"].getHalfCheckedKeys(),o=this.$refs["to-tree"].getCheckedNodes(),s=this.$refs["to-tree"].getHalfCheckedNodes();return{leftKeys:e,leftHarfKeys:t,leftNodes:a,leftHalfNodes:n,rightKeys:r,rightHarfKeys:i,rightNodes:o,rightHalfNodes:s}},setChecked:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.$refs["from-tree"].setCheckedKeys(e),this.$refs["to-tree"].setCheckedKeys(t)}}}},"28a5":function(e,t,a){"use strict";a.r(t);var n=a("e37f"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"28d7":function(e,t,a){"use strict";var n=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("d3b7"),a("159b");var r=n(a("c1df")),i=a("3543");t.default={name:"FilterSet",props:{plan:{type:Object,default:function(){return{}}},checkers:{type:Array,default:function(){return[]}},matchto:{type:String,default:"ONE"}},data:function(){return{form:{type:"ONE",checkList:[],dates:[]}}},computed:{threeWeekRange:function(){var e=this.plan.Cur_Data_Date?r(this.plan.Cur_Data_Date):r(this.plan.Plan_Start_Date),t=r(e).startOf("isoweek").toDate(),a=r(t).add(-7,"days").toDate(),n=r(t).add(13,"days").toDate();return[r(a).format("YYYY-MM-DD"),r(n).format("YYYY-MM-DD")]}},created:function(){var e=this;this.checkers.forEach((function(t){"field"===t.type?e.form.checkList.push(t.value):"critical"===t.type?e.form.checkList.push(t.type):"daterange"===t.type||"threeweeks"===t.type?(e.form.checkList.push(t.type),e.form.dates=t.value.concat([])):"keyword"===t.type&&e.form.checkList.push(t.type)})),this.form.type=this.matchto},methods:{submit:function(){var e=this,t=new i.GantFilters([],this.form.type);this.form.checkList.forEach((function(a){"milestone"===a||"project"===a||"task"===a?t.checkers.push({type:"field",field:"type",value:a}):"critical"===a?t.checkers.push({type:"critical",field:"iscriticalPath",value:1}):"threeweeks"!==a&&"daterange"!==a||t.checkers.push({type:a,value:e.form.dates})})),this.$emit("dialogFormSubmitSuccess",{type:"setGanttFilters",data:t})},changeCheckList:function(e,t){"daterange"===e&&!0===t&&(this.form.checkList=this.form.checkList.filter((function(e){return"threeweeks"!==e}))),"threeweeks"===e&&!0===t&&(this.form.checkList=this.form.checkList.filter((function(e){return"daterange"!==e})),this.form.dates=this.threeWeekRange.concat([])),"threeweeks"!==e&&"daterange"!==e||!1!==t||(this.form.dates=[])}}}},"29d9":function(e,t,a){"use strict";a.r(t);var n=a("9ad9"),r=a("da20");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"2a91":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b"),a("d81d"),a("e9f5"),a("d866"),a("ab43"),a("d3b7");var r=n(a("6e7d")),i=n(a("fb6a9")),o=n(a("e242")),s=n(a("2305"));t.default={components:{users:r.default,Roles:i.default,department:o.default,userGroup:s.default},props:{group:{type:Array,default:function(){return[]}},users:{type:Array,default:function(){return[]}},roles:{type:Array,default:function(){return[]}},departments:{type:Array,default:function(){return[]}},groupNames:{type:String,default:""},userNames:{type:String,default:""},roleNames:{type:String,default:""},departmentNames:{type:String,default:""},orgId:{type:String,default:""},show:{type:Boolean,default:!1}},data:function(){return{activeName:"group",selectGroupList:[],selectUserList:[],selectRoleList:[],selectDepartmentList:[],status:[!1,!1,!1,!1]}},computed:{selectGroup:{get:function(){return this.group},set:function(e){this.$emit("update:group",e)}},selectUsers:{get:function(){return this.users},set:function(e){this.$emit("update:users",e)}},selectRoles:{get:function(){return this.roles},set:function(e){this.$emit("update:roles",e)}},selectDepartments:{get:function(){return this.departments},set:function(e){this.$emit("update:departments",e)}},groupNamesTxt:{get:function(){return this.groupNames},set:function(e){this.$emit("update:groupNames",e)}},userNamesTxt:{get:function(){return this.userNames},set:function(e){this.$emit("update:userNames",e)}},roleNamesTxt:{get:function(){return this.roleNames},set:function(e){this.$emit("update:roleNames",e)}},departmentNamesTxt:{get:function(){return this.departmentNames},set:function(e){this.$emit("update:departmentNames",e)}},showDialog:{get:function(){return this.show},set:function(e){this.$emit("update:show",e)}}},watch:{groupNames:function(){this.groupNamesTxt=this.groupNames,this.getGroupList()},userNames:function(){this.userNamesTxt=this.userNames,this.getUserGroupList()},roleNames:function(){this.roleNamesTxt=this.roleNames,this.getRoleGroupList()},departmentNames:function(){this.departmentNamesTxt=this.departmentNames,this.getDepartmentGroupList()},selectGroupList:function(e){this.selectGroup=e&&e.length>0&&e.map((function(e){return e.Id}))||[],this.groupNamesTxt=e&&e.length>0&&e.map((function(e){return e.Label})).join(",")||""},selectUserList:function(e){this.selectUsers=e&&e.length>0&&e.map((function(e){return e.Id}))||[],this.userNamesTxt=e&&e.length>0&&e.map((function(e){return e.Display_Name})).join(",")||""},selectRoleList:function(e){this.selectRoles=e&&e.length>0&&e.map((function(e){return e.Id}))||[],this.roleNamesTxt=e&&e.length>0&&e.map((function(e){return e.Label})).join(",")||""},selectDepartmentList:function(e){this.selectDepartments=e&&e.length>0&&e.map((function(e){return e.Id}))||[],this.departmentNamesTxt=e&&e.length>0&&e.map((function(e){return e.Label})).join(",")||""}},mounted:function(){this.getGroupList(),this.getUserGroupList(),this.getRoleGroupList(),this.getDepartmentGroupList()},methods:{getGroupList:function(){if(this.groupNames){var e=this.groupNames&&this.groupNames.split(",");this.selectGroupList=this.selectGroup.map((function(t,a){return{Id:t,Label:e[a]}}))}else this.selectGroupList=[]},getUserGroupList:function(){if(this.userNames){var e=this.userNames&&this.userNames.split(",");this.selectUserList=this.selectUsers.map((function(t,a){return{Id:t,Display_Name:e[a]}}))}else this.selectUserList=[]},getRoleGroupList:function(){if(this.roleNames){var e=this.roleNames&&this.roleNames.split(",");this.selectRoleList=this.selectRoles.map((function(t,a){return{Id:t,Label:e[a]}}))}else this.selectRoleList=[]},getDepartmentGroupList:function(){if(this.departmentNames){var e=this.departmentNames&&this.departmentNames.split(",");this.selectDepartmentList=this.selectDepartments.map((function(t,a){return{Id:t,Label:e[a]}}))}else this.selectDepartmentList=[]},handleOpen:function(){this.dialogVisible=!0},handleSubmit:function(){this.$refs.users.getCheckedData(),this.$refs.roles.getCheckedData(),this.$refs.departments.getCheckedData(),this.$refs.group.getCheckedData()},handleUpdate:function(e){var t=this;this.status[e]=!0,this.status.every((function(e){return!!e}))&&this.$nextTick((function(e){t.showDialog=!1}))}}}},"2bd2":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("tree-detail",{ref:"tree",attrs:{"show-checkbox":"","expanded-key":e.expandedKey,loading:e.treeLoading,"tree-data":e.treeData,"can-node-click":!1,icon:"icon-user","default-checked-keys":e.defaultCheckedKeys},on:{getCheckedNodes:e.getCheckedNodes}})],1)},r=[]},"2c5c3":function(e,t,a){"use strict";a.r(t);var n=a("efc3"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"2cb4":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:"",disabled:!e.warehouseId&&!e.nullIsFetch||e.disabled},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)},r=[]},"2e18":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("a9e3");var o=a("ac6b"),s=a("ed08");t.default={name:"SelectTeam",props:{value:{type:[Array,Number,String],default:""},multiple:{type:Boolean,default:!1}},data:function(){return{list:[],selectedValue:this.value}},watch:{value:{handler:function(e){this.selectedValue=Array.isArray(e)?(0,s.deepClone)(e):e},immediate:!0}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:(0,o.GetWorkingTeams)({FactoryId:localStorage.getItem("CurReferenceId")}).then((function(t){e.list=t.Data}));case 1:return t.a(2)}}),t)})))()}}}},"2e8b":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:e.multiple},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)},r=[]},"2f19":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("d4b4")),i=n(a("f82c"));t.default={name:"ElUploadList",mixins:[r.default],data:function(){return{focusing:!1}},components:{ElProgress:i.default},props:{files:{type:Array,default:function(){return[]}},disabled:{type:Boolean,default:!1},handlePreview:Function,listType:String},methods:{parsePercentage:function(e){return parseInt(e,10)},handleClick:function(e){this.handlePreview&&this.handlePreview(e)}}}},"2f53":function(e,t,a){"use strict";a.r(t);var n=a("bf95"),r=a("e720");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("ec75");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"0ec91e3c",null);t["default"]=s.exports},"301cf":function(e,t,a){"use strict";a.r(t);var n=a("28d7"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"30bf":function(e,t,a){"use strict";a.r(t);var n=a("a8038"),r=a("0df4");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("b13e");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"89107bf8",null);t["default"]=s.exports},"312c":function(e,t,a){"use strict";var n=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");var r=n(a("c1df"));t.default={name:"CalendarSet",components:{},props:{origin:{type:Object,default:function(){return{week_calendar:[1,1,1,1,1,1,1],out_calendar:[]}}},editMode:{type:Boolean,default:!0}},data:function(){return{weekdays:[{value:1,label:"周一"},{value:2,label:"周二"},{value:3,label:"周三"},{value:4,label:"周四"},{value:5,label:"周五"},{value:6,label:"周六"},{value:0,label:"周日"}],workWeek:[],calendarRules:{weekend:[],holiday:[],workday:[]},exceptions:[]}},watch:{exceptions:function(e){this.resetCalendarRules()}},created:function(){var e=this;this.calcOrignRules(),this.workWeek=this.weekdays.filter((function(t){return e.calendarRules.weekend.indexOf(t.value)<0})).map((function(e){return e.value}))},methods:{calcOrignRules:function(){var e=this;this.origin.week_calendar.forEach((function(t,a){0===Number(t)&&e.calendarRules.weekend.push(a)})),this.origin.out_calendar.forEach((function(t){e.exceptions.push({name:t.name,date:[t.start_date,t.end_date],isWorkday:1==t.type})}))},exceptionType:function(e){var t="normal";return t=this.calendarRules.workday.indexOf(e)>-1?"workday":this.calendarRules.holiday.indexOf(e)>-1||this.calendarRules.weekend.indexOf(new Date(e).getDay())>-1?"holiday":"normal",t},exceptionChange:function(e,t,a){e[t]=a,this.resetCalendarRules()},resetCalendarRules:function(){var e=this;this.calendarRules.weekend=this.weekdays.filter((function(t){return e.workWeek.indexOf(t.value)<0})).map((function(e){return e.value})),this.calendarRules.workday=[],this.calendarRules.holiday=[],this.exceptions.forEach((function(t){if(t.date){var a,n=[];if(t.date[0]&&t.date[1])for(var i=r(t.date[1]).diff(t.date[0],"days"),o=0;o<=i;o++){var s=r(t.date[0]).add(o,"days").format("YYYY-MM-DD");n.push(s)}else n.push(null!==(a=t.date[0])&&void 0!==a?a:t.date[1]);t.isWorkday?e.calendarRules.workday=e.calendarRules.workday.concat(n):e.calendarRules.holiday=e.calendarRules.holiday.concat(n)}}))},prevYear:function(){var e=new Date(this.$refs.myCalendar.realSelectedDay||this.$refs.myCalendar.formatedToday),t=e.getMonth()+1,a=e.getFullYear();this.$refs.myCalendar.pickDay("".concat(a-1,"-").concat(t<10?"0"+t:t,"-01"))},prevMonth:function(){this.$refs.myCalendar.selectDate("prev-month")},nextYear:function(){var e=new Date(this.$refs.myCalendar.realSelectedDay||this.$refs.myCalendar.formatedToday),t=e.getMonth()+1,a=e.getFullYear();this.$refs.myCalendar.pickDay("".concat(a+1,"-").concat(t<10?"0"+t:t,"-01"))},nextMonth:function(){this.$refs.myCalendar.selectDate("next-month")},workWeekChange:function(e){var t=this;this.calendarRules.weekend=this.weekdays.filter((function(e){return t.workWeek.indexOf(e.value)<0})).map((function(e){return e.value}))},addException:function(){this.exceptions.push({name:"例外",date:"",isWorkday:!0})},removeException:function(e){this.exceptions.splice(e,1)},saveCalendar:function(){if(!this.editMode)return this.$emit("dialogCancel");var e={week_calendar:[0,0,0,0,0,0,0]};this.workWeek.forEach((function(t){e.week_calendar[t]=1})),e.out_calendar=[],this.exceptions.forEach((function(t){e.out_calendar.push({name:t.name,type:t.isWorkday?1:0,start_date:t.date[0],end_date:t.date[1]})})),this.$emit("dialogFormSubmitSuccess",{type:"setCalendar",data:e})}}}},"32fd":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ALL_TASK_COLUMNS",{enumerable:!0,get:function(){return i.ALL_TASK_COLUMNS}}),Object.defineProperty(t,"DATE_FIELDS",{enumerable:!0,get:function(){return i.DATE_FIELDS}}),Object.defineProperty(t,"GantFilters",{enumerable:!0,get:function(){return r.GantFilters}}),Object.defineProperty(t,"PlanAuth",{enumerable:!0,get:function(){return r.PlanAuth}}),Object.defineProperty(t,"TASK_FIELDS",{enumerable:!0,get:function(){return i.TASK_FIELDS}}),Object.defineProperty(t,"ZOOM_LEVELS",{enumerable:!0,get:function(){return r.ZOOM_LEVELS}}),Object.defineProperty(t,"calcPlanDurations",{enumerable:!0,get:function(){return n.calcPlanDurations}}),Object.defineProperty(t,"compute",{enumerable:!0,get:function(){return n.compute}}),Object.defineProperty(t,"createEmptyPlan",{enumerable:!0,get:function(){return n.createEmptyPlan}}),Object.defineProperty(t,"createGanttInstance",{enumerable:!0,get:function(){return r.createGanttInstance}}),Object.defineProperty(t,"createNewTask",{enumerable:!0,get:function(){return i.createNewTask}}),Object.defineProperty(t,"openDialog",{enumerable:!0,get:function(){return r.openDialog}}),Object.defineProperty(t,"openDrawer",{enumerable:!0,get:function(){return r.openDrawer}}),Object.defineProperty(t,"parseServerPlanEntity",{enumerable:!0,get:function(){return n.parseServerPlanEntity}}),Object.defineProperty(t,"setTaskConstraintDate",{enumerable:!0,get:function(){return i.setTaskConstraintDate}}),Object.defineProperty(t,"setTaskConstraintType",{enumerable:!0,get:function(){return i.setTaskConstraintType}}),Object.defineProperty(t,"setTaskSize",{enumerable:!0,get:function(){return i.setTaskSize}}),Object.defineProperty(t,"toDateStr",{enumerable:!0,get:function(){return r.toDateStr}}),Object.defineProperty(t,"updateCalendar",{enumerable:!0,get:function(){return n.updateCalendar}}),Object.defineProperty(t,"updateGanttTask",{enumerable:!0,get:function(){return i.updateGanttTask}}),Object.defineProperty(t,"updateParentWBS",{enumerable:!0,get:function(){return i.updateParentWBS}}),Object.defineProperty(t,"updatePlanField",{enumerable:!0,get:function(){return n.updatePlanField}});var n=a("5585"),r=a("3543"),i=a("43fe")},3339:function(e,t,a){"use strict";a.r(t);var n=a("ead9"),r=a("49bd");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"333d":function(e,t,a){"use strict";a.r(t);var n=a("4c15"),r=a("d331");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("1c87");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"32995d16",null);t["default"]=s.exports},"335a1":function(e,t,a){},3543:function(e,t,a){"use strict";var n=a("dbce").default,r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ZOOM_LEVELS=t.PlanAuth=t.GantFilters=void 0,t.createGanttInstance=m,t.openDialog=p,t.openDrawer=_,t.toDateStr=g;var i=r(a("d4ec")),o=r(a("bee2")),s=r(a("ade3")),l=r(a("5530"));a("99af"),a("4de4"),a("d81d"),a("14d9"),a("4e82"),a("b0c0"),a("e9f5"),a("7d54"),a("ab43"),a("a9e3"),a("b680"),a("d3b7"),a("25f0"),a("159b");var c=n(a("c1df")),d=a("6bfb"),u=a("5c96"),f=a("5585");function p(e,t){e&&"[object Object]"===Object.prototype.toString.call(e)||(e={}),t.dialogCfgs=Object.assign({},{component:"",title:"",width:"360px"},e),t.dialogShow=!0}var h=t.ZOOM_LEVELS=[{name:"day",scale_height:60,scales:[{unit:"month",format:"<strong>%Y年%m月</strong>",step:1},{unit:"day",step:1,format:"<span data-date=%Y-%m-%d>%d</span>"}]},{name:"week",scale_height:60,scales:[{unit:"year",format:"<strong>%Y年</strong>",step:1},{unit:"week",step:1,format:function(e){var t=c(e);return"<span>第".concat(t.format("W"),"周(").concat(t.format("M/D"),"~").concat(c(t).add(6,"days").format("M/D"),")</span>")}}]},{name:"month",scale_height:60,scales:[{unit:"year",format:"<strong>%Y年</strong>",step:1},{unit:"month",step:1,format:"<span data-date=%Y-%m-%d>%m月</span>"}]},{name:"quarter",scale_height:60,scales:[{unit:"year",format:"<strong>%Y年</strong>",step:1},{unit:"quarter",step:1,format:function(e){var t=c(e);return t.format("第Q季度(M-".concat(c(e).add(2,"months").format("M"),"月)"))}}]},{name:"year",scale_height:60,scales:[{unit:"year",step:5,format:function(e){var t=c(e);return"<strong>".concat(t.format("YYYY")," - ").concat(c(e).add(5,"years").format("YYYY"),"</strong>")}},{unit:"year",step:1,format:"<span data-date=%Y-%m-%d>%Y</span>"}]}];function m(e){var t,a,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{el:"gantt-chart",locale:"cn",filters:null,editMode:!0,showBaseLine:!1},r=arguments.length>2?arguments[2]:void 0;e.gantt&&e.gantt.destructor(),e.gantt=d.Gantt.getGanttInstance(),e.gantt.config.readonly=!n.editMode,e.gantt.i18n.setLocale(null!==(t=n.locale)&&void 0!==t?t:"cn"),e.gantt.config.date_format="%Y-%m-%d",e.gantt.config.drag_progress=!1,e.gantt.config.scale_height=60,e.gantt.config.show_task_cells=!0,e.gantt.config.min_duration=864e5,n.start&&(e.gantt.config.start_date=n.start),e.gantt.templates.rightside_text=function(t,a,n){return n.type===e.gantt.config.types.milestone?n.text:""},e.gantt.templates.grid_folder=function(e){return e.Is_Sync&&"0"!=e.Is_Sync?'<i class="el-icon-link" style="margin-top:10px;margin-right:4px;color:#298DFF;"></i>':""},e.gantt.templates.grid_file=function(e){return e.Is_Sync&&"0"!=e.Is_Sync&&"project"===e.type?'<i class="el-icon-link" style="margin-top:10px;margin-right:4px;color:#298DFF;"></i>':""};var i={align:"center",hide:!1,label:'<a id="column-set" class="el-link el-link--primary">\n      <span class="el-link--inner" style="pointer-events: none;">\n        <i style="cursor:pointer;font-size:1.2em;pointer-events: none;" title="栏位设置" class="el-icon-set-up"></i>\n      </span>\n    </a>',min_width:80,max_width:80,resize:!1,template:e.gantt.getWBSCode};e.gantt.config.columns=(n.gridColumns?[i].concat(n.gridColumns):[i]).map((function(e){var t=(0,l.default)({},e);return t.name&&(t.name.indexOf("Float")>-1||t.name.indexOf("Difference")>-1)&&(t.template=function(e){var a=Number(e[t.name])&&Number(e[t.name])<0;return'<span style="color:'.concat(a?"#FB6B7F":"",";font-weight:").concat(a?"bold":"normal",';">').concat(void 0===e[t.name]||null===e[t.name]?"":e[t.name],"</span>")}),t})),e.gantt.config.work_time=!0,e.gantt.config.skip_off_time=!0,e.gantt.config.duration_unit="day",e.gantt.config.inherit_calendar=!0,e.gantt.setWorkTime({day:6,hours:["0:00-23:59"]}),e.gantt.setWorkTime({day:0,hours:["0:00-23:59"]}),n.calendar&&(n.calendar.week_calendar.forEach((function(t,a){var n=a,r=0!==t&&["0:00-23:59"];e.gantt.setWorkTime({day:n,hours:r})})),n.calendar.out_calendar.forEach((function(t){var a=0!==t.type&&["0:00-23:59"],n=(0,f.getDatesRange)(t);n.forEach((function(t){e.gantt.setWorkTime({date:c(t).startOf("date").toDate(),hours:a})}))}))),e.gantt.ignore_time=function(t){if(!e.gantt.isWorkTime(t))return!0},e.gantt.plugins({marker:!0,tooltip:!0,fullscreen:!0,drag_timeline:!0,auto_scheduling:!0,critical_path:!0,overlay:!0,click_drag:!1}),e.gantt.config.highlight_critical_path=!0,e.gantt.config.auto_scheduling=!1,e.gantt.config.drag_timeline={ignore:".gantt_task_line, .gantt_task_link, .gantt_marker",useKey:!1},e.gantt.config.sort=!0,e.gantt.config.order_branch="marker",e.gantt.config.order_branch_free=!1,e.gantt.attachEvent("onBeforeTaskMove",(function(t,a,n){var r=e.gantt.getTask(a);return!r||"project"===r.type})),e.gantt.attachEvent("onAfterTaskMove",(function(t,a,n){e.onAfterTaskMove&&"[object Function]"===Object.prototype.toString.call(e.onAfterTaskMove)&&e.onAfterTaskMove(t,a,n)})),e.gantt.attachEvent("onGridResizeEnd",(function(){e.onGridResizeEnd&&"[object Function]"===Object.prototype.toString.call(e.onGridResizeEnd)&&e.onGridResizeEnd()})),e.gantt.attachEvent("onGanttScroll",(function(t,a){e.onGanttScroll&&"[object Function]"===Object.prototype.toString.call(e.onGanttScroll)&&e.onGanttScroll(t,a)})),e.gantt.config.click_drag={callback:null!==(a=e.onClickDragEnd)&&void 0!==a?a:function(){},singleRow:!0},null!==n&&void 0!==n&&n.showBaseLine&&(e.gantt.config.row_height=48,e.gantt.config.bar_height=e.gantt.config.row_height/2-4,e.gantt.addTaskLayer({renderer:{render:function(t){if(t.Target_Start_Date&&t.Target_End_Date){var a=e.gantt.getTaskPosition(t,c(t.Target_Start_Date).startOf("date").toDate(),c(t.Target_End_Date).add(1,"days").startOf("date").toDate()),n=document.createElement("div"),r=document.createElement("span");return n.appendChild(r),r.style.width=100*t.Actual_Progress+"%",n.className="milestone"===t.type?"basediamond":"baseline",n.style.left=a.left+"px","milestone"!==t.type&&(n.style.width=a.width+"px"),n.style.top=a.top+e.gantt.config.bar_height+13+"px",n}return!1}}})),e.gantt.attachEvent("onGanttReady",(function(){e.gantt.ext.tooltips;e.gantt.templates.tooltip_text=function(e,t,a){var n;if("milestone"===a.type)return"";var r=g(e),i=g(c(t).add(-1,"days")),o=c(t).diff(e,"days"),s=0;return"task"==a.type&&(s=a.Actual_Progress),"project"==a.type&&(s=a.progress),'<div class="custom-plan-tooltip">\n        <i class="cross-icon el-icon-close" onclick="this.parentNode.parentNode.remove()" class="el-icon-close"></i>\n      <header><h3>'.concat(a.text,"</h3><p>").concat(r," ~ ").concat(i,"</p></header><div>\n        <b>工期：</b>").concat(o," 天<br/>\n        <b>进度：</b>").concat((100*s).toFixed(1),"%<br/>\n        <b>备注：</b>").concat(null!==(n=a.Remark)&&void 0!==n?n:"","</div></div>")}})),e.gantt.templates.grid_row_class=function(t,a,n){var r=e.gantt.getMarker("spot-marker");return r&&(c(r.start_date).isSameOrAfter(t)&&c(r.end_date).isSameOrBefore(a)||c(a).isSameOrAfter(r.start_date)&&c(a).isSameOrBefore(r.end_date)||c(t).isSameOrAfter(r.start_date)&&c(t).isSameOrBefore(r.end_date))?"spotted":""},e.gantt.templates.task_class=function(e,t,a){switch(a.type){case"task":return"custom-gantt-bar-task";case"milestone":return"custom-gantt-bar-milestone";case"project":return"custom-gantt-bar-project"}},e.gantt.templates.task_row_class=function(e,t,a){switch(a.type){case"task":return"custom-gantt-row-task";case"milestone":return"custom-gantt-row-milestone";case"project":return"custom-gantt-row-project"}};var o=e.plan.Cur_Data_Date?c(e.plan.Cur_Data_Date).toDate():c(e.plan.Plan_Start_Date).toDate();e.gantt.addMarker({id:"data-date",start_date:o,css:"today",text:"数据日期",title:"".concat(c(o).format("YYYY-MM-DD"))});e.gantt.attachEvent("onGanttRender",(function(){e.onGanttRendered&&"[object Function]"===Object.prototype.toString.call(e.onGanttRendered)&&e.onGanttRendered()})),e.gantt.attachEvent("onBeforeTaskDisplay",(function(t,a){return e.gantt.isCriticalTask(a)?a.iscriticalPath=1:delete a.iscriticalPath,a.Wbs_Code=a.$wbs,"project"===a.type&&setTimeout((function(){var e=document.querySelector('.custom-gantt-bar-project[task_id="'+t+'"]');if(e){var a=e.querySelector(".lbag");a||(a=document.createElement("span"),a.className="lbag",e.appendChild(a));var n=e.querySelector(".rbag");n||(n=document.createElement("span"),n.className="rbag",e.appendChild(n))}}),100),!n.filters||!!n.filters.check(a)})),e.gantt.config.layout={css:"gantt_container",cols:[{width:n.gridWidth?n.gridWidth:500,min_width:n.gridWidth?n.gridWidth:500,rows:[{view:"grid",scrollX:"gridScroll",scrollable:!0,scrollY:"scrollVer"},{view:"scrollbar",id:"gridScroll",group:"horizontal"}]},{resizer:!0,width:1},{rows:[{view:"timeline",scrollX:"scrollHor",scrollY:"scrollVer"},{view:"scrollbar",id:"scrollHor",group:"horizontal"}]},{view:"scrollbar",id:"scrollVer"}]},e.gantt.ext.zoom.init({levels:h,element:function(){return e.gantt.$root.querySelector(".gantt_task")}}),e.gantt.ext.zoom.setLevel("day"),e.gantt.$zoomToFit=!1,e.gantt.attachEvent("onExpand",(function(){setTimeout((function(){var t=e.gantt.ext.fullscreen.getFullscreenElement();t.style.zIndex=9999}),120),e.onFullScreenChanged&&"[object Function]"===Object.prototype.toString.call(e.onFullScreenChanged)&&e.onFullScreenChanged()})),e.gantt.attachEvent("onCollapse",(function(){setTimeout((function(){var t=e.gantt.ext.fullscreen.getFullscreenElement();t.style.zIndex=1}),120),e.onFullScreenChanged&&"[object Function]"===Object.prototype.toString.call(e.onFullScreenChanged)&&e.onFullScreenChanged()})),e.gantt.attachEvent("onEmptyClick",(function(t){e.gantt.unselectTask(),e.onTaskUnselected&&"[object Function]"===Object.prototype.toString.call(e.onTaskUnselected)&&e.onTaskUnselected()})),e.gantt.attachEvent("onTaskDblClick",(function(t,a){return e.onTaskDblClick&&"[object Function]"===Object.prototype.toString.call(e.onTaskDblClick)&&e.onTaskDblClick(t,a),!1})),e.gantt.attachEvent("onBeforeTaskDrag",(function(t,a,n){var r=e.gantt.getTask(t);return!(e.canDragAndResizeTask&&!e.canDragAndResizeTask(r))&&!(r.Actual_Progress||r.Actual_Start_Date||r.Actual_End_Date)})),e.gantt.attachEvent("onAfterTaskDrag",(function(t,a,n){var r=e.gantt.config.drag_mode;switch(a){case r.move:e.onTaskDragMoveEnd&&"[object Function]"===Object.prototype.toString.call(e.onTaskDragMoveEnd)&&e.onTaskDragMoveEnd(t);break;case r.resize:e.onTaskResizeEnd&&"[object Function]"===Object.prototype.toString.call(e.onTaskResizeEnd)&&e.onTaskResizeEnd(t);break;case r.progress:break}})),e.gantt.attachEvent("onLinkDblClick",(function(t,a){if(e.onLinkDblClick&&"[object Function]"===Object.prototype.toString.call(e.onLinkDblClick)){if(e.canAddLink&&!e.canAddLink(t))return e.$message.warning("无权限"),!1;e.onLinkDblClick(t)}return!1})),e.gantt.attachEvent("onBeforeLinkAdd",(function(t,a){if(e.canAddLink&&!e.canAddLink(a))return e.$message.warning("无权限"),!1;var n=e.gantt.getTask(a.source),r=e.gantt.getTask(a.target);return"project"===n.type||"project"===r.type?((0,u.Message)({type:"warning",message:"不支持在WBS作业上创建连接"}),!1):void 0})),e.gantt.attachEvent("onTaskSelected",(function(t){e.onTaskSelected&&"[object Function]"===Object.prototype.toString.call(e.onTaskSelected)&&e.onTaskSelected(t)})),e.gantt.init(n.el);try{e.gantt.parse(r||e.ganttData)}catch(s){}return e.gantt}t.GantFilters=function(){function e(t,a){(0,i.default)(this,e),(0,s.default)(this,"checkers",void 0),(0,s.default)(this,"matchto",void 0),this.checkers=null!==t&&void 0!==t?t:[],this.matchto=null!==a&&void 0!==a?a:"ONE"}return(0,o.default)(e,[{key:"check",value:function(e){var t=this;if(this.checkers.length<=0)return!0;var a=[];return this.checkers.forEach((function(n){"field"==n.type||"critical"==n.type?a.push(t.fieldMatch(e[n.field],n.value)):"daterange"==n.type||"threeweeks"===n.type?a.push(t.dateRangeMatch(e,n.value)):"keyword"==n.type&&a.push(t.textMatch(e,n.value))})),"ALL"===this.matchto?a.filter((function(e){return e})).length===this.checkers.length:a.filter((function(e){return e})).length>0}},{key:"fieldMatch",value:function(e,t){return e===t}},{key:"textMatch",value:function(e,t){return e.text.indexOf(t)>-1||e.Responsible_UserName&&e.Responsible_UserName.indexOf(t)>-1}},{key:"dateRangeMatch",value:function(e,t){var a=c(e.Dynamic_Start_Date.split("A")[0]).toDate(),n=c(e.Dynamic_End_Date.split("A")[0]);return c(a).isSameOrAfter(t[0])&&c(n).isSameOrBefore(t[1])}}])}();function _(e,t){t.$refs.drawer.closeDrawer(),e&&"[object Object]"===Object.prototype.toString.call(e)||(e={});var a=setInterval((function(){t.canOpenNewDrawer&&(t.drawerCfgs=Object.assign({},t.drawerCfgs,e,{props:Object.assign({},e.props)}),t.drawerShow=!0,clearInterval(a))}),30)}function g(e){return e?c(e).startOf("date").format("YYYY-MM-DD"):""}var v=t.PlanAuth=function(){function e(t){(0,i.default)(this,e),this.auth=t,this.access=this.getAuth(this.auth)}return(0,o.default)(e,[{key:"getAuth",value:function(){return e.Roles[this.auth]?e.Roles[this.auth].map((function(t){return e.ACCESSES[t]})):[]}},{key:"check",value:function(e){var t;return this.access.forEach((function(e){void 0===t?t=e:t|=e})),(t&e)>0}},{key:"getValue",value:function(){var e;return this.access.forEach((function(t){void 0===e?e=t:e|=t})),null!==e&&void 0!==e?e:0}}])}();(0,s.default)(v,"ACCESSES",{READ:1,EDIT:2,ADD:4,DELETE:8,RELATION:16,WBS:32}),(0,s.default)(v,"Roles",{"负责":["READ","EDIT","ADD","DELETE","RELATION","WBS"],"参与":["READ","EDIT","ADD","DELETE"],"查看":["READ"]})},"359a":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{style:{height:e.height+"px",zIndex:e.zIndex}},[a("div",{class:e.className,style:{top:e.isSticky?e.stickyTop+"px":"",zIndex:e.zIndex,position:e.position,width:e.width,height:e.height+"px"}},[e._t("default",[a("div",[e._v("sticky")])])],2)])},r=[]},"36af":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");t.default={name:"NumberRange",model:{prop:"value",event:"change"},props:{value:{type:Array,default:function(){return[null,null]}},min:{type:Number,default:null},max:{type:Number,default:null}},data:function(){return{maxV:null,minV:null}},created:function(){},methods:{textInputMin:function(e){NaN===Number(e)&&(this.minV=null)},textInputMax:function(e){NaN===Number(e)&&(this.maxV=null)},miniChange:function(e){this.minV=this.min?Number(this.min):Number(this.minV)||null,this.maxV=this.max?Number(this.max):Number(this.maxV)||null,this.$emit("change",[this.minV,this.maxV])},maxChange:function(e){this.minV=this.min?Number(this.min):Number(this.minV)||null,this.maxV=this.max?Number(this.max):Number(this.maxV)||null,this.$emit("change",[this.minV,this.maxV])}}}},3796:function(e,t,a){"use strict";a.r(t);var n=a("c1e6"),r=a("e6d3");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("d1a0");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"13222ed3",null);t["default"]=s.exports},"37fd":function(e,t,a){"use strict";a("38ba4")},"382d":function(e,t,a){"use strict";var n=a("dbce").default,r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530")),o=r(a("2909"));a("99af"),a("4de4"),a("7db0"),a("a630"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("25f0"),a("3ca3"),a("9911"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("f44c"),a("03a5");var s=a("472f"),l=a("affc"),c=r(a("1d04")),d=r(a("5254")),u=r(a("5e1a9")),f=r(a("6f5e")),p=r(a("1999")),h=r(a("9084")),m=r(a("f7c3")),_=r(a("9f55")),g=r(a("13f3")),v=r(a("0a0e")),y=r(a("d89c")),b=n(a("32fd")),D=a("3543"),S=a("3741"),k=a("eb4a"),w=n(a("c1df")),C=a("bb2b"),x=r(a("97ff"));t.default={name:"BimGanttDemo",components:{PlanAddDialog:c.default,CalendarSet:d.default,TaskDetail:u.default,GridSet:f.default,TargetSet:p.default,PlanImport:g.default,UpdateSet:h.default,FilterSet:m.default,ScheduleDialog:_.default,PlanHistory:v.default,ResourceAnalyzer:y.default},mixins:[x.default],props:{plan:{type:Object,default:function(){return{}}},editmode:{type:Boolean,default:!0},needApprovePlan:{type:Boolean,default:!1},existFlow:{type:Boolean,default:!1},extend:{type:Boolean,default:!1},extendfields:{type:Array,default:function(){return[]}}},data:function(){return{loading:!1,loadingStr:"",editMode:!0,wbsMode:!1,showBaseLine:!1,showFowardLine:!1,showSpotLight:!1,spot_range:{},fowards:{overlay:""},legendShow:!1,needApprove:!1,flowReady:!1,dialogShow:!1,dialogCfgs:{},drawerShow:!1,canOpenNewDrawer:!0,drawerCfgs:{direction:"btt",title:"",size:"260px",modal:!1,wrapperClosable:!1,modalAppendToBody:!1,withHeader:!0,component:""},drawerProps:{},planTypes:[],members:[],gantt:null,reshecdeledData:null,gantFilters:new D.GantFilters([],"ONE"),keyword:"",AllSettings:[],AvailableSettings:[],fromData:[],maxDeep:10,currentLvl:0,foldedTask:!1,saveParam:{isSaveHistory:!1}}},computed:{role:function(){return new b.PlanAuth(this.plan.Plan_Auth)},ACCESSES:function(){return b.PlanAuth.ACCESSES},ganttData:function(){var e,t=(null===(e=this.plan)||void 0===e?void 0:e.Plan_Data)||{data:[],links:[]};return t},ganttColumns:function(){var e=this,t=this.AvailableSettings.map((function(t){var a,n={label:t.Label,align:["text","remark"].indexOf(t.Code)>-1?"left":"center",name:t.Code,hide:!1,resize:!0,tree:"text"===t.Code,Display_Name:t.Label};return"Responsible_User"===t.Code&&(n.template=function(t){var a,n;return null!==(a=null===(n=e.members.find((function(e){return e.User_Id===t["Responsible_User"]})))||void 0===n?void 0:n.UserName)&&void 0!==a?a:""}),0!==(null===(a=t.Data)||void 0===a?void 0:a.Width)?n.min_width=t.Data.Width:"text"===t.Code&&(n.min_width=200,n.max_width=360),"text"===t.Code&&(n.label='<div id="wbs2task"><span class="tag '.concat(e.foldedTask?"wbs":"task",'"><span style="pointer-events: none;">').concat(e.foldedTask?"W":"作","</span></span>").concat(n.Display_Name,"</div>")),"Dynamic_Start_Date"!==t.Code&&"Dynamic_End_Date"!==t.Code||(n.template=function(e){var a=e[t.Code];return e.Control_Plan_Start_Date&&e.Control_Plan_End_Date&&w(e.Control_Plan_End_Date).isBefore(e["Dynamic_End_Date"].split("A")[0])&&(a='<span style="font-weight:bold;color:#e63030;">'.concat(a,"</span>")),a}),(t.Code.indexOf("progress")>-1||t.Code.indexOf("Progress")>-1)&&"Laster_Progress_Date"!==t.Code&&(n.template=function(e){return(100*e[t.Code]).toFixed(2)+"%"}),n}));return t}},watch:{$route:function(e){var t=document.querySelector(".gantt_tooltip");t&&t.remove()},plan:function(){this.buildGantt()},wbsMode:function(e,t){var a,n;null===(a=this.gantt)||void 0===a||a.unselectTask(),!0===e?this.gantFilters.checkers.push({type:"field",field:"type",value:"project"}):this.gantFilters.checkers=this.gantFilters.checkers.filter((function(e){return"type"!==e.field||"project"!==e.value})),null===(n=this.gantt)||void 0===n||n.refreshData(),this.reRenderGantt()},drawerShow:function(e){var t=this;this.$nextTick((function(){var e;null===(e=t.gantt)||void 0===e||e.render()}))},keyword:function(e){var t,a=this.gantFilters.checkers.find((function(e){return"keyword"===e.type}));e?a?a.value=e:this.gantFilters.checkers.push({type:"keyword",value:e}):this.gantFilters.checkers=this.gantFilters.checkers.filter((function(e){return"keyword"!==e.type})),null===(t=this.gantt)||void 0===t||t.refreshData(),this.reRenderGantt()}},created:function(){var e=this;this.needApprove=this.needApprovePlan,this.flowReady=this.existFlow,this.editMode=this.editmode,this.plan.Id&&this.editmode&&!this.role.check(b.PlanAuth.ACCESSES.EDIT)&&(this.editMode=!1),(0,l.GetPlanTypeTree)(this.$store.state.user.Last_Working_Object_Id).then((function(t){t.IsSucceed&&(e.planTypes=t.Data)})),(0,s.GetUserListByObjId)(this.$store.getters.Last_Working_Object_Id,{Page:1,PageSize:1e3}).then((function(t){t.IsSucceed&&(e.members=t.Data.Data)})),(0,k.CheckExistFlow)({webId:"PlanView"}).then((function(t){e.flowReady=t.Data})),(0,s.CheckNeedApprovePlan)().then((function(t){e.needApprove=t.Data}))},mounted:function(){this.buildGantt(),this.bindEvents(),(0,C.ep1)(),(0,C.ep2)()},methods:{reRenderGantt:function(){var e,t=this;this.showFowardLine&&(this.removeFowardLine(),this.drawFowardLine()),null===(e=this.gantt)||void 0===e||e.render(),this.showSpotLight&&setTimeout((function(){t.bindSpotMarkerEvents(document.querySelector("[data-marker-id=spot-marker]"),t.gantt.getMarker("spot-marker"))}),30)},onAfterTaskMove:function(e,t,a){this.reRenderGantt()},changeId:function(e){var t=this;e.map((function(e){e.id=e.Id,"0"===e.ParentNodes&&(e.ParentNodes=0),delete e.Id,e.Children&&t.changeId(e.Children)}))},loadUserColumns:function(e,t){return(0,s.GetPlanFieldSetting)(e,t)},changeData:function(e,t){var a=[],n=[];t.map((function(e){n.push(e)})),e.map((function(e){var t;(t=a).push.apply(t,(0,o.default)(e.Children)),delete e.Children,a.push(e)})),n.map((function(e){a.map((function(t,n){t.Label===e.Label&&t.ParentNodes===e.ParentNodes&&a.splice(n,1)}))})),a=(0,S.arrayToTree)(a,{id:"id",pid:"ParentNodes",children:"Children"}),this.fromData=a.filter((function(e){return 0!==e.Children.length}))},buildGantt:function(){var e=this;this.fowards={overlay:""},this.showFowardLine=!1,this.loadUserColumns(this.plan.Id||"X",!1).then((function(t){if(t.IsSucceed){var a=JSON.parse(JSON.stringify(t.Data.AllSettings));e.changeId(a),e.AvailableSettings=t.Data.AvailableSettings,e.changeId(e.AvailableSettings),e.AllSettings=a,e.changeData(a,e.AvailableSettings),null!==e.plan.Target_Plan_Id&&void 0!==e.plan.Target_Plan_Id&&""!==e.plan.Target_Plan_Id?e.showBaseLine=!0:e.showBaseLine=!1,b.createGanttInstance(e,{el:"gantt-chart",locale:"cn",calendar:e.plan.Plan_Calendar,filters:e.gantFilters,gridColumns:e.ganttColumns,editMode:e.editMode,showBaseLine:e.showBaseLine,start:e.plan.Plan_Start_Date}),!e.plan.Id&&e.gantt.getTaskByTime().length<=0&&b.createNewTask(e.gantt,e.plan,{parent:"0",type:"project",text:e.plan.Name})}}))},bindEvents:function(){var e=this;this.$el.addEventListener("click",(function(t){var a=e.$el.querySelector("#column-set"),n=e.$el.querySelector("#wbs2task>.tag");if(t.target==a)e.onColumnsSetOpen();else if(t.target==n){var r=Array.from(t.target.classList).indexOf("task")>-1;r?(t.target.classList.remove("task"),t.target.classList.add("wbs"),t.target.querySelector("span").innerText="W",e.foldedTask=!0):(t.target.classList.remove("wbs"),t.target.classList.add("task"),t.target.querySelector("span").innerText="作",e.foldedTask=!1);var i=e.gantt.getGridColumn("text");i.label='<div id="wbs2task"><span class="tag '.concat(e.foldedTask?"wbs":"task",'"><span style="pointer-events: none;">').concat(e.foldedTask?"W":"作","</span></span>").concat(i.Display_Name,"</div>");var o=r?"project":"task";e.foldTaskType(o),e.reRenderGantt(),e.$forceUpdate()}})),window.addEventListener("resize",(function(t){e.showFowardLine&&e.reRenderGantt()}))},foldTaskType:function(e){var t=this;this.gantt.batchUpdate((function(){for(var a=t.gantt.getTaskByTime(),n=0;n<a.length;n++){var r=a[n];if(r.$open=!0,"project"===e){var i=a.filter((function(e){return e.parent===r.id}));"project"!=r.type||i.find((function(e){return"project"==e.type}))||(r.$open=!1)}t.gantt.updateTask(r.id)}}))},foldTaskLevel:function(e){var t=this;this.gantt.batchUpdate((function(){for(var a=t.gantt.getTaskByTime(),n=0;n<a.length;n++){var r=a[n];r.$open=!0,r.$level>e-1&&(r.$open=!1),t.gantt.updateTask(r.id)}})),this.reRenderGantt()},savePlan:function(e,t,a){var n=this;if(!this.plan.Plan_End_Date)return this.$message.warning("当前计划无计划完成时间，请先完善计划信息");if(this.plan.Dynamic_Duration=Math.abs(this.gantt.calculateDuration({start_date:w(this.plan.Actual_Start_Date||this.plan.Plan_Start_Date).toDate(),end_date:w(this.plan.Actual_End_Date||this.plan.Plan_End_Date).toDate()}))+1,"1"!=e||!this.plan.Is_Main_Plan||this.flowReady){this.loading=!0;var r=this.gantt.serialize();r.data.forEach((function(e){Object.keys(e).forEach((function(t){(t.indexOf("_Date")>-1||t.indexOf("_date")>-1)&&(e[t]||(e[t]=null))}))}));var o=(0,i.default)((0,i.default)({},this.plan),{},{Plan_Data:r,Observer:JSON.stringify(this.plan.Observer),Admin:JSON.stringify(this.plan.Admin)});Object.keys(o).forEach((function(e){["Plan_End_Date","Plan_Start_Date","Cur_Data_Date"].indexOf(e)>-1&&(o[e]=o[e]?w(o[e]).startOf("date").format("YYYY-MM-DD"):null),(e.indexOf("_Date")>-1||e.indexOf("_date")>-1)&&(o[e]||(o[e]=null))}));var l=!1;return this.plan.Id&&"参与"===this.plan.Plan_Auth&&(l=!0),(0,s.SavePlan)({},{planModel:o,webfromId:"PlanView",saveModel:e,isSubmitControl:null!==t&&void 0!==t?t:"",isCollaboration:l,isSaveHistory:this.saveParam.isSaveHistory,isTargetPlan:null!==a&&void 0!==a&&a}).then((function(t){if(t.IsSucceed){if(n.$message.success(t.Message),n.saveParam.isSaveHistory&&(n.saveParam.isSaveHistory=!1),n.plan.Id||(n.plan.Id=t.Data,n.$router.push("/plan/edit/"+n.plan.Id).then((function(){n.$store.dispatch("tagsView/delView",n.$store.state.tagsView.visitedViews.splice(n.$store.state.tagsView.visitedViews.length-2,1))}))),a)return t;"1"!=e&&"2"!=e&&"3"!=e||n.tagBack()}else n.$message.warning(t.Message)})).finally((function(){n.loading=!1}))}this.$message({type:"error",message:"尚未配置审核流程！"})},onAddTask:function(){var e=this.canAddTaskInfo();if(!e.can)return this.$message.warning(e.msg);var t=b.createNewTask(this.gantt,this.plan,{type:this.wbsMode?"project":"task",parent:this.gantt.getSelectedId()||"0"});this.gantt.open(t.id),t.parent&&"0"!==t.parent&&this.gantt.open(this.gantt.getSelectedId()),this.reRenderGantt()},onDeleteTask:function(){var e=this,t=this.gantt.getSelectedId();if(!t)return this.$message.warning("当前没有选中的作业");var a=this.gantt.getTask(t),n=this.canDelTaskInfo(a);if(!n.can)return this.$message.warning(n.msg);this.$confirm("如果 [".concat(a.text,"] 具有子作业，也将被同时删除。是否确定继续删除?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.gantt.deleteTask(t),e.reRenderGantt()})).catch((function(){}))},onWbsModeChange:function(e){this.wbsMode="project"===e},onColumnsSetOpen:function(){b.openDialog({title:"栏位设置",width:"750px",component:"GridSet",props:{origin:this.AvailableSettings,all:this.fromData,oall:this.AllSettings,gantt:this.gantt}},this)},onDetailOpen:function(){var e=this,t=this.gantt.getSelectedId();if(!t)return this.$message.warning("当前没有选中的任务");var a=this.gantt.getTask(t);this.drawerProps={drawclass:"taskdetail",tabname:"profile",taskid:t,planid:this.plan.Id,plan:this.plan,task:a,constraints:s.CONSTRAINT_TYPES,members:this.members,gantt:this.gantt,editMode:this.editMode,canEditWBS:this.canEditWBS(a),canEditTask:this.canEditTask(a),canUpdateProgress:this.canUpdateProgress(a),extend:this.extend,extendfields:this.extendfields},this.drawerCfgs.props=this.drawerProps,this.$refs["TaskDetail"]||(setTimeout((function(){e.reRenderGantt()}),60),b.openDrawer({title:"查看工作任务",withHeader:!1,component:"TaskDetail",direction:"btt",wrapperClosable:!0,size:"260px",props:this.drawerCfgs.props},this))},onTaskDblClick:function(e,t){this.onDetailOpen()},onTaskUnselected:function(){(this.$refs["TaskDetail"]||this.$refs["ResourceAnalyzer"])&&this.drawerShow&&this.drawerCancel()},onTaskSelected:function(e){this.$refs["TaskDetail"]&&this.drawerShow&&this.onDetailOpen()},onHistoryOpen:function(){this.plan.Id&&(this.drawerProps={plan:this.plan,begin_date:"",end_date:"",drawclass:"history"},this.drawerCfgs.props=this.drawerProps,b.openDrawer({title:"历史版本查看",withHeader:!0,component:"PlanHistory",direction:"rtl",wrapperClosable:!0,size:"320px",props:this.drawerCfgs.props},this))},toggleInfo:function(){this.legendShow=!this.legendShow},onCommandChange:function(e){switch(e){case"calendar":this.onOpenCalendarSet();break;case"target":this.onOpenTargetSet();break;case"updateset":this.onOpenUpdateSet();break}},onExportChange:function(e){if(this.gantt&&this.gantt["exportTo"+e]){var t={};"Excel"===e&&(t.columns=this.ganttColumns.map((function(e){return{id:e.name,header:"text"===e.name?"作业名称":e.label,type:["Actual_Progress","Start_Difference","End_Difference","Duration_Difference","Needed_Duration","Plan_Duration","Actual_Duration","Target_Duration","Dynamic_Duration","Actual_Resources","Difference","Free_Float","Old_Actual_Progress","Plan_Resources","Target_Resources","Show_Duration","Show_Progress","Total_Float"].indexOf(e.name)>-1?"number":"string"}}))),this.gantt["exportTo"+e](t)}},onOpenCalendarSet:function(){b.openDialog({title:"计划日历设置",width:"950px",component:"CalendarSet",props:{origin:this.plan.Plan_Calendar,editMode:this.editMode&&["1","2"].indexOf(this.plan.Status)<0}},this)},onOpenTargetSet:function(){b.openDialog({title:"目标计划设置",width:"560px",component:"TargetSet",props:{plan:this.plan,editMode:this.editMode}},this)},onOpenUpdateSet:function(){b.openDialog({title:"填报任务设置",width:"560px",component:"UpdateSet",props:{plan:this.plan,editMode:this.editMode}},this)},onZoom:function(e){e>0?this.gantt.ext.zoom.zoomIn():this.gantt.ext.zoom.zoomOut(),this.gantt.$zoomToFit=!1,this.reRenderGantt()},onFullScreen:function(){var e;null===(e=this.gantt)||void 0===e||e.ext.fullscreen.toggle()},onFullScreenChanged:function(){var e=this;setTimeout((function(){e.reRenderGantt()}),0)},onGridResizeEnd:function(){var e=this;this.$nextTick((function(){e.reRenderGantt()}))},applyConfig:function(e,t){var a=this;this.gantt.config.scales=e.scales;var n=e.scales.reverse()[0];t&&t.start_date&&t.end_date?(this.gantt.config.start_date=this.gantt.date.add(t.start_date,-1,n.unit),this.gantt.config.end_date=this.gantt.date.add(this.gantt.date[n.unit+"_start"](t.end_date),2,n.unit)):this.gantt.config.start_date=this.gantt.config.end_date=null,e.scroll_position&&setTimeout((function(){a.gantt.scrollTo(e.scroll_position.x,e.scroll_position.y)}),4)},zoomToFit:function(){if(this.cachedSettings)return this.applyConfig(this.cachedSettings),void(this.cachedSettings=null);var e=this.gantt.config;this.cachedSettings={},this.cachedSettings.scales=e.scales,this.cachedSettings.start_date=e.start_date,this.cachedSettings.end_date=e.end_date,this.cachedSettings.scroll_position=this.gantt.getScrollState();for(var t=this.gantt.getSubtaskDates(),a=this.gantt.$task.offsetWidth,n=b.ZOOM_LEVELS,r=0;r<n.length;r++){var i=this.getUnitsBetween(t.start_date,t.end_date,n[r].scales[n[r].scales.length-1].unit,n[r].scales[0].step);if((i+2)*this.gantt.config.min_column_width<=a)break}r==n.length&&r--,this.gantt.ext.zoom.setLevel(n[r].name),this.applyConfig(n[r],t)},getUnitsBetween:function(e,t,a,n){var r=new Date(e),i=new Date(t),o=0;while(r.valueOf()<i.valueOf())o++,r=this.gantt.date.add(r,n,a);return o},onFixAll:function(){this.gantt.$zoomToFit=!this.gantt.$zoomToFit,this.zoomToFit(),this.reRenderGantt()},onTaskDragMoveEnd:function(e){var t,a=this.gantt.getTask(e);b.setTaskConstraintType(a,"snet",this.gantt),b.setTaskConstraintDate(a,a.start_date,this.gantt),this.gantt.updateTask(a.id,a),this.reRenderGantt(),b.updateParentWBS(this.gantt),null===(t=this.$refs["TaskDetail"])||void 0===t||t.buildFormFromProp()},onTaskResizeEnd:function(e){var t,a=this.gantt.getTask(e);b.setTaskSize(a),this.gantt.updateTask(a.id,a),b.updateParentWBS(this.gantt,a),this.reRenderGantt(),null===(t=this.$refs["TaskDetail"])||void 0===t||t.buildFormFromProp()},onClickDragEnd:function(e,t,a,n,r,i){},onFilterTasksSet:function(){b.openDialog({title:"过滤器",width:"560px",component:"FilterSet",props:{plan:this.plan,checkers:this.gantFilters.checkers,matchto:this.gantFilters.matchto}},this)},onPlanImport:function(){if(this.plan.Is_Main_Plan)return this.$message.warning("主计划不支持导入");var e=new URL("/Plan/Plan/ImportPlan",this.$baseUrl).href;b.openDialog({title:"导入",width:"450px",component:"PlanImport",props:{action:e,exts:["mpp"],filesize:50}},this)},onOpenPlanSet:function(){b.openDialog({title:"计划设置",width:"50%",component:"PlanAddDialog",props:{typetree:this.planTypes,members:this.members,plan:this.plan,editMode:this.editMode}},this)},onScheduleOpen:function(){b.openDialog({title:"进度计算",width:"480px",component:"ScheduleDialog",props:{gantt:this.gantt,plan:this.plan}},this)},onAnalyzerOpen:function(){var e=this;this.drawerCancel(),this.drawerProps={drawclass:"analyzer-detail",plan:this.plan,gantt:this.gantt},this.drawerCfgs.props=this.drawerProps,this.$refs["ResourceAnalyzer"]||(setTimeout((function(){e.reRenderGantt()}),60),b.openDrawer({title:"人工资源分析",withHeader:!1,component:"ResourceAnalyzer",direction:"btt",wrapperClosable:!0,size:"260px",props:this.drawerCfgs.props},this))},onLinkDblClick:function(e){var t=this,a=this.gantt.getLink(e),n=this.gantt.getTask(a.source),r=this.gantt.getTask(a.target);this.$confirm("确认要删除 [".concat(n.text,"] 和 [").concat(r.text,"] 之间的连接吗?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.gantt.deleteLink(a.id)})).catch((function(){}))},onGanttScroll:function(e,t){var a=document.querySelector(".gantt_overlay_area");if(this.fowards.overlay){var n=document.querySelector(".gantt_overlay");setTimeout((function(){n&&(n.style.top="-".concat(a.style.top))}),10)}},drawerCancel:function(){var e=this;this.$refs["TaskDetail"]&&setTimeout((function(){e.reRenderGantt()}),60),this.$refs.drawer.closeDrawer()},drawerContentUpdate:function(e){var t=e.type,a=e.data;this[t]&&"[object Function]"===Object.prototype.toString.call(this[t])&&this[t](a)},updateGanttTask:function(e){var t,a=e.gantt,n=e.task,r=e.field,i=e.value;b.updateGanttTask({gantt:a,task:n,field:r,value:i}),null===(t=this.$refs["TaskDetail"])||void 0===t||t.buildFormFromProp(),this.reRenderGantt()},updateLagDays:function(e){e.gantt;var t=e.link,a=e.value,n=this.gantt.getTask(t.target);this.updateGanttTask({gantt:this.gantt,task:n,field:"Plan_Start_Date",value:this.gantt.calculateEndDate({start_date:n.Plan_Start_Date,duration:a})})},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(e){var t=this,a=e.type,n=e.data;this.dialogCancel(),this[a]&&"[object Function]"===Object.prototype.toString.call(this[a])&&(this[a](n),"setGanttColumns"===a&&(0,s.SavePlanFieldSetting)({filedSetting:JSON.stringify(n)}).then((function(e){e.IsSucceed||t.$message({type:"error",message:e.Message})})))},restoreDefault:function(){var e=this;this.dialogCancel(),this.loadUserColumns(this.plan.Id||"X",!0).then((function(t){t.IsSucceed&&e.setGanttColumns(t.Data.AvailableSettings)}))},onMppImported:function(e){var t=this;this.removeFowardLine(),this.showFowardLine=!1;var a=b.parseServerPlanEntity(e);Object.keys(this.plan).forEach((function(e){"Plan_Data"!==e&&"Plan_Calendar"!==e&&void 0!==t.plan[e]&&null!==t.plan||"Id"===e||(t.plan[e]=a[e])})),b.createGanttInstance(this,{el:"gantt-chart",locale:"cn",calendar:this.plan.Plan_Calendar,filters:this.gantFilters,gridColumns:this.ganttColumns,editMode:this.editMode,showBaseLine:this.showBaseLine,start:this.plan.Plan_Start_Date})},setPlanBase:function(e){for(var t in e)b.updatePlanField(this.plan,t,e[t])},setCalendar:function(e){b.updateCalendar(this.plan,this.gantt,e)},setGanttColumns:function(e){this.AvailableSettings=e,this.gantt.config.columns=[this.gantt.config.columns[0]].concat((0,o.default)(this.ganttColumns)),this.gantt.render()},setTargetPlan:function(e){var t=this;this.plan.Target_Plan_Id=e;var a=this.gantt.getTaskByTime();new Promise((function(n,r){0===e?(t.savePlan(0,!1,!0).then((function(e){e.IsSucceed&&(t.plan.Target_Plan_Id=e.Data)})),a.forEach((function(e){e.Target_Start_Date=e.Plan_Start_Date,e.Target_End_Date=w(e.Plan_End_Date).toDate(),e.Target_Resources=e.Plan_Resources,e.Target_Duration=e.Plan_Duration})),n()):""===e?(t.plan.Target_Plan_Id=null,a.forEach((function(e){e.Target_Start_Date=e.Target_End_Date=null,e.Target_Duration=e.Target_Resources=0})),n()):(0,s.GetPlanEntity)(e).then((function(e){if(e.IsSucceed){var t=e.Data.Plan_Data.data;a.forEach((function(e){var a=t.find((function(t){return t.id===e.id}));a&&(e.Target_Start_Date=a.Plan_Start_Date,e.Target_End_Date=w(a.Plan_End_Date).toDate(),e.Target_Resources=a.Plan_Resources,e.Target_Duration=a.Plan_Duration)}))}n()}))})).then((function(){t.plan.Plan_Data={data:t.gantt.getTaskByTime(),links:t.gantt.getLinks()}})).finally((function(){null!==t.plan.Target_Plan_Id&&void 0!==t.plan.Target_Plan_Id&&""!==t.plan.Target_Plan_Id?t.showBaseLine=!0:t.showBaseLine=!1,b.createGanttInstance(t,{el:"gantt-chart",locale:"cn",calendar:t.plan.Plan_Calendar,filters:t.gantFilters,gridColumns:t.ganttColumns,editMode:t.editMode,showBaseLine:t.showBaseLine,start:t.plan.Plan_Start_Date})}))},setUpdatePlan:function(e){var t=this,a=e.date,n=e.time,r=e.data_date;(0,s.SendTaskInputMessage)({plan_id:this.plan.Id,inputEndDate:w(a).format("YYYY-MM-DD")+" "+n,dataDate:w(r).format("YYYY-MM-DD")}).then((function(e){e.IsSucceed&&t.$message({type:"success",message:e.Message})}))},setGanttFilters:function(e){var t;this.gantFilters.checkers=e.checkers,this.gantFilters.matchto=e.matchto,null===(t=this.gantt)||void 0===t||t.refreshData(),this.reRenderGantt()},onLevelChange:function(e){this.foldTaskLevel(this.maxDeep-e)},canDrawLine:function(e,t){return"task"===e.type&&(!e.Actual_End_Date&&(!!e.Target_Start_Date&&!w(e.Target_Start_Date).isSameOrAfter(t)))},drawFowardLine:function(){var e,t=this,a=new Date(w(null!==(e=this.plan.Cur_Data_Date)&&void 0!==e?e:this.plan.Plan_Start_Date)),n=this.gantt.getScrollState(),r=this.gantt.ext.overlay.addOverlay((function(e){e.style.top=-n.y+"px";var r=document.querySelector(".gantt_task_bg"),i=document.createElement("div");i.style.height=r.offsetHeight+"px",i.style.width=r.offsetWidth+"px",e.appendChild(i);var o=t.gantt.getTaskByTime(),s='<svg style="width:'.concat(r.offsetWidth,"px;height:").concat(r.offsetHeight,'px;">'),l={h:0,w:0},c=t.gantt.posFromDate(a);return o.forEach((function(e){var n=t.gantt.getTaskPosition(e,e.Target_Start_Date,e.Target_End_Date);l.h=n.height,l.w=n.width/t.gantt.calculateDuration({start_date:e.Target_Start_Date,end_date:e.Target_End_Date});var r=t.gantt.posFromDate(w(e.Target_Start_Date).startOf("date").toDate()),i=t.gantt.posFromDate(w(e.Target_End_Date).startOf("date").toDate()),o=(i-r+l.w)*e.Actual_Progress+r;t.canDrawLine(e,a)&&(s+='<line x1="'.concat(c,'" y1="').concat(n.top+n.height+4,'" x2="').concat(o,'" y2="').concat(n.top+n.height+4+n.height/2,'" style="stroke:rgb(255,0,0);stroke-width:2" />'),s+='<line x1="'.concat(o,'" y1="').concat(n.top+n.height+n.height/2+4,'" x2="').concat(c,'" y2="').concat(n.top+n.rowHeight,'" style="stroke:rgb(255,0,0);stroke-width:2" />'))})),s+="</svg>",i.innerHTML=s,i}));this.fowards.overlay=r,this.gantt.ext.overlay.showOverlay(r),this.gantt.render()},removeFowardLine:function(){var e=(0,i.default)({},this.fowards),t=e.overlay;this.gantt.ext.overlay.deleteOverlay(t);var a=document.querySelector('[data-overlay-id="'.concat(t,'"]'));null===a||void 0===a||a.remove(),this.fowards={overlay:""},this.gantt.render()},onToggleFowardLine:function(e){e?this.drawFowardLine():this.removeFowardLine()},onToggleSpotLight:function(e){if(!e)return this.gantt.deleteMarker("spot-marker"),this.reRenderGantt();var t=this.gantt.getSubtaskDuration();if(this.plan&&this.plan.Cur_Data_Date){var a=w(this.spot_range.start||this.plan.Cur_Data_Date).toDate(),n=this.spot_range.end||this.gantt.calculateEndDate({start_date:a,duration:t<6?t:6});this.gantt.addMarker({start_date:a,end_date:n,id:"spot-marker",css:"spot-marker",text:"聚光灯",title:"".concat(w(a).format("YYYY-MM-DD")," ~ ").concat(w(n).format("YYYY-MM-DD"))}),this.gantt.render(),this.bindSpotMarkerEvents(document.querySelector("[data-marker-id=spot-marker]"),this.gantt.getMarker("spot-marker"))}},bindSpotMarkerEvents:function(e,t){var a,n=this,r=document.createElement("div");r.className="l-bar";var i=document.createElement("div");i.className="r-bar",null===e||void 0===e||e.append(r,i);var o=null===(a=document.querySelector(".gantt_task_bg"))||void 0===a?void 0:a.offsetWidth;if(e){i.addEventListener("mousedown",(function(e){e.stopPropagation(),i.__start_pos=[e.pageX,e.pageY],i.__draging=!0})),i.addEventListener("mouseup",(function(e){i.__draging&&n.dragEnd(e)})),i.addEventListener("mouseleave",(function(e){i.__draging&&n.dragEnd(e)})),i.addEventListener("mousemove",(function(t){if(i.__draging){if(Number(e.style.left.split("px")[0])+10+Number(e.style.width.split("px")[0])>o)return e.style.left=Number(e.style.left.split("px")[0])-2+"px",n.dragEnd(t);i.__to_pos=[t.pageX,t.pageY],e.style.width=Math.max(Number(e.style.width.split("px")[0])+(i.__to_pos[0]-i.__start_pos[0]),60)+"px",i.__start_pos=i.__to_pos}})),r.addEventListener("mousedown",(function(e){e.stopPropagation(),r.__start_pos=[e.pageX,e.pageY],r.__draging=!0})),r.addEventListener("mouseup",(function(e){r.__draging&&n.dragEnd(e)})),r.addEventListener("mouseleave",(function(e){r.__draging&&n.dragEnd(e)})),r.addEventListener("mousemove",(function(t){if(r.__draging){if(Number(e.style.left.split("px")[0])<10)return e.style.left="10px",n.dragEnd(t);r.__to_pos=[t.pageX,t.pageY];var a=r.__to_pos[0]-r.__start_pos[0];e.style.width=Math.max(Number(e.style.width.split("px")[0])-a,60)+"px",e.style.left=Number(e.style.left.split("px")[0])+a+"px",r.__start_pos=r.__to_pos}}));var s=Math.abs(w(t.start_date).diff(t.end_date,"days"));e.clientWidth;e.addEventListener("mousedown",(function(t){e.__draging=!0,e.__start_pos=[t.pageX,t.pageY]})),e.addEventListener("mouseup",(function(t){e.__draging&&n.dragEnd(t)})),e.addEventListener("mouseleave",(function(t){e.__draging&&n.dragEnd(t)})),e.addEventListener("mousemove",(function(t){if(e.__draging){if(Number(e.style.left.split("px")[0])<10)return e.style.left="10px",n.dragEnd(t);if(Number(e.style.left.split("px")[0])+10+Number(e.style.width.split("px")[0])>o)return e.style.left=Number(e.style.left.split("px")[0])-2+"px",n.dragEnd(t);e.__to_pos=[t.pageX,t.pageY],e.style.left=Number(e.style.left.split("px")[0])+(e.__to_pos[0]-e.__start_pos[0])+"px",e.__start_pos=e.__to_pos}}));var l=document.querySelectorAll(".gantt_marker_area");l[l.length-1].style.pointerEvents="none",e.style.pointerEvents="initial"}},dragEnd:function(e){var t=document.querySelector("[data-marker-id=spot-marker]");e.target.__draging=!1,e.target.__start_pos=null,e.target.__to_pos=null;var a=this.gantt.dateFromPos(Number(t.style.left.split("px")[0])),n=this.gantt.dateFromPos(Number(t.style.left.split("px")[0])+Number(t.style.width.split("px")[0])),r=this.gantt.getMarker("spot-marker");r.start_date=a,r.end_date=n,this.spot_range.start=r.start_date,this.spot_range.end=r.end_date,this.gantt.updateMarker("spot-marker"),this.gantt.render(),this.bindSpotMarkerEvents(document.querySelector("[data-marker-id=spot-marker]"),this.gantt.getMarker("spot-marker"))},onComputedSchedule:function(e){var t=this,a=e.Cur_Data_Date,n=e.Is_Save_History;this.drawerCancel(),this.removeFowardLine(),this.showFowardLine=!1,this.plan.Plan_Data.data=this.gantt.getTaskByTime(),this.plan.Plan_Data.links=this.gantt.getLinks(),this.loading=!0,this.loadingStr="正在为您计算结果...",this.plan.Cur_Data_Date=a,this.saveParam.isSaveHistory=n,b.compute(this.gantt,this.plan).then((function(e){var a;t.plan.Plan_Data.data=e.tasks,t.plan.Plan_Data=(0,i.default)({},t.plan.Plan_Data),t.reshecdeledData=null===(a=t.plan)||void 0===a?void 0:a.Plan_Data,b.createGanttInstance(t,{el:"gantt-chart",locale:"cn",calendar:t.plan.Plan_Calendar,filters:t.gantFilters,gridColumns:t.ganttColumns,editMode:t.editMode,showBaseLine:t.showBaseLine,start:t.plan.Plan_Start_Date},t.reshecdeledData),t.reRenderGantt()})).catch((function(e){t.$message.warning(e)})).finally((function(){t.loading=!1,t.loadingStr=""}))},moment:function(e){return w(e)},canDelTaskInfo:function(e){var t=!0,a="ok";return e.Is_Sync&&"0"!=e.Is_Sync&&(t=!1,a="通过动态运营表同步，无法删除"),(e.Actual_Start_Date||e.Actual_End_Date)&&(t=!1,a="作业已经开始，无法删除"),this.role.check(this.ACCESSES.DELETE)||(t=!1,a="您无权删除当前作业"),"task"===e.type||this.role.check(this.ACCESSES.WBS)||(t=!1,a="您无权删除当前作业"),this.role.check(this.ACCESSES.DELETE)&&"0"!=e.parent&&this.gantt.getTask(e.parent).Responsible_User!==this.$store.state.user.userId&&!this.role.check(this.ACCESSES.WBS)&&(t=!1,a="您无权删该WBS下的作业"),{can:t,msg:a}},canAddTaskInfo:function(){var e=!0,t="ok";if(this.plan.Id){this.role.check(this.ACCESSES.ADD)||(e=!1,t="你无权新增作业"),!this.role.check(this.ACCESSES.WBS)&&this.wbsMode&&(e=!1,t="你无权操作WBS");var a=this.gantt.getSelectedId(),n=a?this.gantt.getTask(a):null;n&&"project"===n.type&&n.Responsible_User!==this.$store.state.user.userId&&!this.role.check(this.ACCESSES.WBS)&&(e=!1,t="你无权操作当前WBS")}return{can:e,msg:t}},canHandlePlanStatus:function(){var e,t;return!this.plan.Id||("0"===(null===(e=this.plan.Status)||void 0===e?void 0:e.toString())||"3"===(null===(t=this.plan.Status)||void 0===t?void 0:t.toString()))&&this.role.check(b.PlanAuth.ACCESSES.WBS)},canDragAndResizeTask:function(e){if("1"===this.plan.Status||"2"===this.plan.Status)return!1;if(this.plan.Id){if(!this.role.check(this.ACCESSES.EDIT))return!1;if(this.role.check(this.ACCESSES.WBS))return!0;if("0"!==e.parent.toString()){var t=this.gantt.getTask(e.parent);if(t.Responsible_User!==this.$store.state.user.userId)return!1}}return!0},canAddLink:function(e){if("1"===this.plan.Status||"2"===this.plan.Status)return!1;var t=this.gantt.getTask(e.source),a=this.gantt.getTask(e.targat);return!this.plan.Id||(!!this.role.check(this.ACCESSES.RELATION)||t.Responsible_User===this.$store.state.user.userId&&a.Responsible_User===this.$store.state.user.userId)},canEditTask:function(e){if(!this.plan.Id)return!0;if("1"===this.plan.Status||"2"===this.plan.Status)return!1;if(e.Is_Sync&&"0"!=e.Is_Sync)return!1;if(this.role.check(this.ACCESSES.WBS))return!0;if("project"===e.type&&e.Responsible_User==this.$store.state.user.userId)return!0;if("0"!==e.parent.toString()){var t=this.gantt.getTask(e.parent);if(t.Responsible_User==this.$store.state.user.userId)return!0}return!1},canEditWBS:function(e){return!this.plan.Id||this.role.check(this.ACCESSES.WBS)},canUpdateProgress:function(e){if(!this.plan.Id)return!0;if(e.Responsible_User==this.$store.state.user.userId)return!0;if(this.role.check(this.ACCESSES.WBS))return!0;if("task"===e.type){var t=this.gantt.getTask(e.parent);if(t&&t.Responsible_User==this.$store.state.user.userId)return!0}return!!this.canEditTask(e)}}}},"38ba4":function(e,t,a){},"3a6c8":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"c-upload"},[e._m(0),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{background:"#F7F8F9",padding:"20px",border:"1px solid #D9DBE2"}},[a("div",{staticStyle:{"margin-bottom":"16px"}},[e._v(" 2.选择本地文件 ")]),a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:"",action:e.action,headers:e.reqHeader,multiple:e.multiple,accept:e.exts.map((function(e){return"."+e})).join(","),"file-list":e.fileList,"before-upload":e.beforeUpload,"on-progress":e.uploadProgressChange,"on-change":e.uploadStatusChange,"on-error":e.uploadError,"on-success":e.uploadSuccess,"auto-upload":!1,name:"files"},scopedSlots:e._u([{key:"file",fn:function(t){var n=t.file;return a("div",{},[a("div",{class:{"up-item":!0,error:"fail"===n.status}},[e.progresses[n.name]>0?a("div",{staticClass:"percent",style:{width:e.progresses[n.name]+"%"}}):e._e(),a("div",{staticClass:"bar"},[a("div",{staticClass:"title"},[a("svg-icon",{staticStyle:{height:"30px",width:"30px"},attrs:{name:e.extIcon(n),"icon-class":e.extIcon(n)}}),e._v(" "+e._s(n.name)+" ")],1),a("div",{staticClass:"remove"},["fail"===n.status?a("i",{staticClass:"el-icon-refresh-right",attrs:{title:"重新上传"},on:{click:function(t){return e.reUpload(n)}}}):e._e(),a("i",{staticClass:"el-icon-close",attrs:{title:"移除文件"},on:{click:function(t){return e.removeFile(n)}}})])])])])}}])},[a("svg-icon",{staticClass:"icon-svg",attrs:{name:"upload-icon","icon-class":"upload-icon",width:"200",height:"200"}}),a("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),a("em",[e._v("点击选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 支持格式："+e._s(e.exts.join("、"))+"，最大文件限制："+e._s(e.filesize)+"M ")])])],1)],1),a("div",{staticStyle:{"text-align":"right","margin-top":"20px"}},[a("el-button",{attrs:{size:"mini"},on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"success",size:"mini"},on:{click:e.beginUpload}},[e._v("导入")])],1)])},r=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{background:"#F7F8F9",padding:"20px","margin-bottom":"20px",border:"1px solid #D9DBE2",display:"flex","flex-direction":"row","align-items":"center","justify-content":"space-between"}},[a("p",[e._v("1.其它软件导出文件")])])}]},"3c22":function(e,t,a){"use strict";a.r(t);var n=a("0c93"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"3c2d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticClass:"detail-card-box",attrs:{shadow:"none"}},[a("span",{staticClass:"title"},[e._v(e._s(e.title))]),a("div",{staticClass:"content"},e._l(e.list,(function(t,n){return a("div",{key:n,staticClass:"item"},[a("label",{staticClass:"content-label"},[e._v(e._s(t.label)+"：")]),a("span",[e._v(e._s(t.value))])])})),0)])},r=[]},"3db1":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2638")),i=n(a("c14f")),o=n(a("1da1"));a("fb6a"),a("b0c0"),a("e9f5"),a("7d54"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("159b");var s=n(a("9e80")),l=n(a("4dd4")),c=a("4d8b");t.default={inject:["uploader"],components:{UploadDragger:s.default},props:{type:String,action:{type:String,required:!0},name:{type:String,default:"file"},data:Object,headers:Object,withCredentials:Boolean,multiple:Boolean,accept:String,onStart:Function,onProgress:Function,onSuccess:Function,onError:Function,beforeUpload:Function,drag:Boolean,onPreview:{type:Function,default:function(){}},onRemove:{type:Function,default:function(){}},fileList:Array,autoUpload:Boolean,listType:String,httpRequest:{type:Function,default:l.default},disabled:Boolean,limit:Number,onExceed:Function},data:function(){return{mouseover:!1,reqs:{},tokenInfo:null}},methods:{isImage:function(e){return-1!==e.indexOf("image")},handleChange:function(e){var t=e.target.files;t&&this.uploadFiles(t)},uploadFiles:function(e){var t=this;return(0,o.default)((0,i.default)().m((function a(){var n,r;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(r=t.httpRequest===l.default,!r){a.n=2;break}return a.n=1,(0,c.getToken)();case 1:t.tokenInfo=a.v;case 2:if(!(t.limit&&t.fileList.length+e.length>t.limit)){a.n=3;break}return t.onExceed&&t.onExceed(e,t.fileList),a.a(2);case 3:if(n=Array.prototype.slice.call(e),t.multiple||(n=n.slice(0,1)),0!==n.length){a.n=4;break}return a.a(2);case 4:n.forEach((function(e){t.onStart(e),t.autoUpload&&t.upload(e)}));case 5:return a.a(2)}}),a)})))()},upload:function(e){var t=this;if(this.$refs.input.value=null,!this.beforeUpload)return this.post(e);var a=this.beforeUpload(e);a&&a.then?a.then((function(a){var n=Object.prototype.toString.call(a);if("[object File]"===n||"[object Blob]"===n){for(var r in"[object Blob]"===n&&(a=new File([a],e.name,{type:e.type})),e)e.hasOwnProperty(r)&&(a[r]=e[r]);t.post(a)}else t.post(e)}),(function(){t.onRemove(null,e)})):!1!==a?this.post(e):this.onRemove(null,e)},abort:function(e){var t=this.reqs;if(e){var a=e;e.uid&&(a=e.uid),t[a]&&t[a].abort()}else Object.keys(t).forEach((function(e){t[e]&&t[e].abort(),delete t[e]}))},post:function(e,t){var a=this,n=e.uid,r={headers:this.headers,withCredentials:this.withCredentials,file:e,data:this.data,filename:this.name,action:this.action,pointFile:t,onProgress:function(t){a.onProgress(t,e)},onSuccess:function(t){a.onSuccess(t,e),delete a.reqs[n]},onError:function(t,r){a.onError(t,e,r),delete a.reqs[n]}},i=this.httpRequest(r,this.tokenInfo);this.reqs[n]=i,i&&i.then&&i.then(r.onSuccess,r.onError)},handleClick:function(){this.disabled||(this.$refs.input.value=null,this.$refs.input.click())},handleKeydown:function(e){e.target===e.currentTarget&&(13!==e.keyCode&&32!==e.keyCode||this.handleClick())}},render:function(e){var t=this.handleClick,a=this.drag,n=this.name,i=this.handleChange,o=this.multiple,s=this.accept,l=this.listType,c=this.uploadFiles,d=this.disabled,u=this.handleKeydown,f={class:{"el-upload":!0},on:{click:t,keydown:u}};return f.class["el-upload--".concat(l)]=!0,e("div",(0,r.default)([{},f,{attrs:{tabindex:"0"}}]),[a?e("upload-dragger",{attrs:{disabled:d},on:{file:c}},[this.$slots.default]):this.$slots.default,e("input",{class:"el-upload__input",attrs:{type:"file",name:n,multiple:o,accept:s},ref:"input",on:{change:i}})])}}},"3df3":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("a9e3");var o=a("9643"),s=a("ed08");t.default={name:"SelectWarehouse",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},type:{type:Number,default:0}},data:function(){return{list:[],selectedValue:this.value}},watch:{value:function(){this.selectedValue=Array.isArray(this.value)?(0,s.deepClone)(this.value):this.value}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:(0,o.GetWarehouseListOfCurFactory)({type:0===e.type?"原材料仓库":"辅料仓库"}).then((function(t){e.list=t.Data}));case 1:return t.a(2)}}),t)})))()}}}},"3e51":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-input",{staticStyle:{width:"40px","text-align":"center"},attrs:{placeholder:"最小值",size:"mini"},on:{input:e.textInputMin,change:e.miniChange,focus:function(t){return e.$emit("focus")}},model:{value:e.minV,callback:function(t){e.minV=t},expression:"minV"}}),e._v(" ~ "),a("el-input",{staticStyle:{width:"40px","text-align":"center"},attrs:{placeholder:"最大值",size:"mini"},on:{input:e.textInputMax,change:e.maxChange,focus:function(t){return e.$emit("focus")}},model:{value:e.maxV,callback:function(t){e.maxV=t},expression:"maxV"}})],1)},r=[]},"3f12":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var r=n(a("a807")),i=a("6186");t.default={components:{TreeDetail:r.default},props:{departments:{type:Array,default:function(){return[]}}},data:function(){return{expandedKey:"",treeLoading:!1,treeData:[],defaultCheckedKeys:[]}},mounted:function(){this.fetchTreeData()},methods:{fetchTreeData:function(){var e=this;this.treeLoading=!0,(0,i.GetDepartmentTree)().then((function(t){e.treeData=t.Data,e.treeLoading=!1,e.$nextTick((function(t){e.defaultCheckedKeys=e.departments.map((function(e){return e.Id}))}))}))},getCheckedData:function(){var e=this;this.$nextTick((function(t){e.$refs.tree.getCheckedNodes()}))},getCheckedNodes:function(e){this.$emit("update:departments",e),this.$emit("handleUpdate",2)}}}},"3f4f":function(e,t,a){"use strict";a.r(t);var n=a("cfd3"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"401f":function(e,t,a){"use strict";a.r(t);var n=a("ef66"),r=a("9177");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("7218");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"409f":function(e,t,a){"use strict";a("b62c")},"43fe":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.TASK_FIELDS=t.DATE_FIELDS=t.ALL_TASK_COLUMNS=void 0,t.createNewTask=l,t.parseServeTask=w,t.setTaskConstraintDate=h,t.setTaskConstraintType=p,t.setTaskSize=m,t.updateGanttTask=u,t.updateParentWBS=c,a("7db0"),a("e9f5"),a("f665"),a("7d54"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("159b");var r=a("5c96"),i=n(a("c1df")),o=a("472f"),s=a("3543");function l(e,t){var a,n,o,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{parent:"0",type:"task"},l=e.getTask(s.parent);if(l&&["task","milestone"].indexOf(l.type)>-1)return(0,r.Message)({type:"warning",message:"不允许在非WBS作业下新建子作业"});var c={};Object.keys(C).forEach((function(e){c[e]=C[e]}));var d=new Date,u=null!==(a=s.type)&&void 0!==a?a:"task",f="project"===u?"新建WBS":"milestone"===u?"里程碑":"新建作业";s.text&&(f=s.text);var p=(null===l||void 0===l?void 0:l.start_date)||(null!==t&&void 0!==t&&t.Plan_Start_Date?null===t||void 0===t?void 0:t.Plan_Start_Date:null)||new Date;return c.id=d.getTime(),c.type=null!==(n=s.type)&&void 0!==n?n:"task",c.parent=null!==(o=s.parent)&&void 0!==o?o:"0",c.text=f,c.start_date=(0,i.default)(p).startOf("date").toDate(),c.duration=1,c.end_date=(0,i.default)(p).add(1,"days").startOf("date").toDate(),c.Dynamic_Start_Date=(0,i.default)(c.start_date).format("YYYY-MM-DD"),c.Dynamic_End_Date=(0,i.default)(c.start_date).format("YYYY-MM-DD"),c.Dynamic_Duration=c.duration,c.Plan_Start_Date=c.start_date,c.Plan_End_Date=c.start_date,c.Plan_Duration=1,c.Needed_Start_Date=c.start_date,c.Needed_End_Date=c.start_date,c.Needed_Duration=1,c.Plan_Resources=0,c.Actual_Resources=0,c.Plan_Id=t.Id,e.addTask(c),e.render(),e.getTask(c.id)}function c(e){}var d=["text","Responsible_User","Plan_Resources","Actual_Resources","Remark","Existing_Problems","Solutions","Need_Coordinate","Coordinate_Department"];function u(e){var t=e.gantt,a=e.task,n=e.field,r=e.value;if(d.indexOf(n)>-1||!C.hasOwnProperty(n))return a[n]=r,t.updateTask(a.id);"constraint_type"===n&&p(a,r,t),"constraint_date"===n&&h(a,r,t),"Plan_Start_Date"===n&&g(a,r,t),"Plan_End_Date"===n&&v(a,r,t),"Plan_Duration"===n&&y(a,r,t),"Actual_Start_Date"===n&&b(a,r,t),"Actual_End_Date"===n&&D(a,r,t),"Actual_Progress"===n&&S(a,r,t),"type"===n&&_(a,r,t)}function f(e){return e.Actual_Start_Date||e.Actual_End_Date}function p(e,t,a){"milestone"!==e.type&&(e.constraint_type=t,["asap","alap"].indexOf(t)>-1?e.constraint_date="":["snet","snlt","mso"].indexOf(t)>-1?e.constraint_date||(e.constraint_date=e.Plan_Start_Date):["mfo","fnet","fnlt"].indexOf(t)>-1&&(e.constraint_date||(e.constraint_date=e.Plan_End_Date)))}function h(e,t,a){var n=f(e);if("milestone"===e.type&&!n)return e.start_date=e.Plan_Start_Date=e.Needed_Start_Date=t,e.end_date=e.Plan_End_Date=e.End_Start_Date=t,e.duration=e.Plan_Duration=e.Needed_Duration=e.Dynamic_Duration=0,e.Dynamic_Start_Date=(0,i.default)(e.start_date).format("YYYY-MM-DD"),void(e.Dynamic_End_Date=(0,i.default)(e.end_date).format("YYYY-MM-DD"));e.constraint_date=t,!t||["asap","alap"].indexOf(e.constraint_type)>-1||n||(["snet","snlt","mso"].indexOf(e.constraint_type)>-1?(e.start_date=e.Plan_Start_Date=e.Needed_Start_Date=t,e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD"),e.end_date=a.calculateEndDate({start_date:t,duration:e.Plan_Duration}),e.Plan_End_Date=e.Needed_End_Date=(0,i.default)(e.end_date).add(-1,"days").startOf("date").toDate(),e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD")):["mfo","fnet","fnlt"].indexOf(e.constraint_type)>-1&&(e.Plan_End_Date=e.Needed_End_Date=t,e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"),e.end_date=(0,i.default)(t).add(1,"days").startOf("date").toDate(),e.start_date=e.Plan_Start_Date=e.Needed_Start_Date=a.calculateEndDate({start_date:e.end_date,duration:-e.Plan_Duration}),e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD")))}function m(e){var t=f(e);t||(e.Plan_Start_Date=e.Needed_Start_Date=e.start_date,e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD"),e.Plan_Duration=e.Dynamic_Duration=e.Needed_Duration=e.duration,e.Plan_End_Date=e.Needed_End_Date=(0,i.default)(e.end_date).add(-1,"days").startOf("date").toDate(),e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"))}function _(e,t,a){e.Actual_Start_Date||(e.type=t,"milestone"===t&&(e.start_date=e.Needed_Start_Date=e.Plan_Start_Date,e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD"),e.duration=e.Plan_Duration=e.Needed_Duration=e.Actual_Progress=e.progress=0,e.end_date=e.Plan_End_Date=e.Needed_End_Date=e.start_date,e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"),e.constraint_type="",e.constraint_date="",e.Dynamic_Duration=0),"task"===t&&(e.start_date=e.Needed_Start_Date=e.Plan_Start_Date,e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD"),e.duration=e.Plan_Duration=e.Needed_Duration=e.Dynamic_Duration=1,e.end_date=a.calculateEndDate({start_date:e.start_date,duration:e.Plan_Duration}),e.Plan_End_Date=e.Needed_End_Date=a.calculateEndDate({start_date:e.start_date,duration:e.Plan_Duration-1}),e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"),e.constraint_type="asap",e.constraint_date=""))}function g(e,t,a){if(t){if("milestone"===e.type)return e.Needed_Start_Date=e.Plan_Start_Date=e.start_date=t,e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD"),e.duration=e.Plan_Duration=e.Needed_Duration=0,e.end_date=e.Plan_End_Date=e.Needed_End_Date=e.start_date,e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"),e.constraint_type="",void(e.constraint_date="");e.Plan_Start_Date=t,e.Plan_End_Date=a.calculateEndDate({start_date:t,duration:e.Plan_Duration-1});var n=f(e);n||(e.start_date=e.Needed_Start_Date=t,e.Dynamic_Start_Date=(0,i.default)(e.start_date).format("YYYY-MM-DD"),e.Needed_End_Date=e.Plan_End_Date,e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"),e.end_date=a.calculateEndDate({start_date:t,duration:e.Plan_Duration}))}}function v(e,t,a){if(t){if("milestone"===e.type)return e.Needed_Start_Date=e.Plan_Start_Date=e.start_date=t,e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD"),e.duration=e.Plan_Duration=e.Needed_Duration=0,e.end_date=e.Plan_End_Date=e.Needed_End_Date=e.start_date,e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"),e.constraint_type="",void(e.constraint_date="");e.Plan_End_Date=t,e.Plan_Duration=a.calculateDuration({start_date:e.Plan_Start_Date,end_date:e.Plan_End_Date})+1;var n=f(e);n||(e.Needed_End_Date=t,e.Dynamic_End_Date=(0,i.default)(e.Needed_End_Date).format("YYYY-MM-DD"),e.Needed_Duration=e.Dynamic_Duration=e.duration=e.Plan_Duration,e.end_date=a.calculateEndDate({start_date:e.start_date,duration:e.duration}))}}function y(e,t,a){if(t&&"milestone"!==e.type){e.Plan_Duration=Number(t),e.Plan_End_Date=a.calculateEndDate({start_date:e.Plan_Start_Date,duration:e.Plan_Duration-1});var n=f(e);n||(e.duration=e.Needed_Duration=e.Dynamic_Duration=e.Plan_Duration,e.end_date=a.calculateEndDate({start_date:e.start_date,duration:e.duration}),e.Needed_End_Date=e.Plan_End_Date=(0,i.default)(e.end_date).add(-1,"days").startOf("date").toDate(),e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"))}}function b(e,t,a){if("milestone"!==e.type||t){if("milestone"===e.type&&t)return e.Actual_End_Date=e.Actual_Start_Date=t,e.Dynamic_Start_Date=e.Dynamic_End_Date=(0,i.default)(e.Actual_End_Date).format("YYYY-MM-DD[A]"),void(e.Actual_Progress=e.progress=1);if(!t)return e.Actual_Progress=e.progress=0,e.Actual_End_Date="",e.Actual_Duration=0,e.Actual_Start_Date=t,e.Needed_Start_Date=e.start_date=e.Plan_Start_Date,e.Dynamic_Start_Date=(0,i.default)(e.Plan_Start_Date).format("YYYY-MM-DD"),e.Needed_Duration=e.Dynamic_Duration=e.duration=e.Plan_Duration,e.end_date=a.calculateEndDate({start_date:e.start_date,duration:e.duration}),e.Needed_End_Date=e.Plan_End_Date,void(e.Dynamic_End_Date=(0,i.default)(e.Plan_End_Date).format("YYYY-MM-DD"));e.Actual_Start_Date=t,e.start_date=t,e.Dynamic_Start_Date=(0,i.default)(e.start_date).format("YYYY-MM-DD[A]"),e.Actual_End_Date||(e.duration=e.Dynamic_Duration=e.Plan_Duration,e.end_date=a.calculateEndDate({start_date:e.start_date,duration:e.Plan_Duration})),e.Dynamic_End_Date=(0,i.default)(e.end_date).add(-1,"days").startOf("date").toDate(),e.Dynamic_Duration=e.duration,e.Dynamic_End_Date=(0,i.default)(e.Dynamic_End_Date).format("YYYY-MM-DD")}}function D(e,t,a){return"milestone"!==e.type||t?"milestone"===e.type&&t?(e.Actual_End_Date=e.Actual_Start_Date=t,e.Dynamic_Start_Date=e.Dynamic_End_Date=(0,i.default)(e.Actual_End_Date).format("YYYY-MM-DD[A]"),void(e.Actual_Progress=e.progress=1)):t?(e.Actual_End_Date=t,e.end_date=(0,i.default)(t).add(1,"days").startOf("date").toDate(),e.Dynamic_End_Date=(0,i.default)(e.Actual_End_Date).format("YYYY-MM-DD[A]"),e.progress=e.Actual_Progress=1,e.Actual_Duration=e.Dynamic_Duration=a.calculateDuration({start_date:e.Actual_Start_Date,end_date:e.Actual_End_Date})+1,e.duration=e.Actual_Duration,e.Needed_Duration=0,void(e.Needed_Start_Date=e.Needed_End_Date="")):(e.Actual_Duration=0,e.duration=e.Dynamic_Duration=e.Plan_Duration,e.end_date=a.calculateEndDate({start_date:e.start_date,duration:e.duration}),e.Dynamic_End_Date=(0,i.default)(e.end_date).add(-1,"days").startOf("date").toDate(),e.Dynamic_End_Date=(0,i.default)(e.Dynamic_End_Date).format("YYYY-MM-DD"),void(e.Actual_End_Date=t)):(e.Actual_End_Date=e.Actual_Start_Date=t,e.Actual_Progress=e.progress=0,e.Dynamic_End_Date=e.Plan_Start_Date,e.Dynamic_Start_Date=e.Plan_Start_Date,void(e.Dynamic_Duration=e.duration=0))}function S(e,t,a){t>=1&&(t=1),(!t||t<0)&&(t=0),e.Actual_Progress=e.progress=Number(t),D(e,1===e.Actual_Progress?(0,i.default)(new Date).startOf("date").toDate():"",a)}var k=t.DATE_FIELDS=["Data_Date","Actual_End_Date","Actual_Start_Date","Cur_Data_Date","Plan_End_Date","Plan_Start_Date","Control_Plan_Start_Date","Control_Plan_End_Date","Needed_Start_Date","Needed_End_Date","Target_Start_Date","Target_End_Date","Early_Start_Date","Laster_Start_Date","Early_End_Date","Laster_End_Date","start_date","end_date","constraint_date"];function w(e){var t={};return Object.keys(e).forEach((function(a){k.indexOf(a)>-1?t[a]=e[a]?(0,i.default)(e[a]).startOf("date").toDate():"":"type"===a?t[a]=e[a].toLowerCase():a.toLowerCase().indexOf("progress")>-1?t[a]=e[a]&&Number(e[a])>1?Number(e[a])/100:e[a]:t[a]=e[a]})),t}var C=t.TASK_FIELDS={id:"",type:"",parent:"0",text:"",start_date:"",end_date:"",duration:0,progress:0,Dynamic_Start_Date:"",Dynamic_End_Date:"",Dynamic_Duration:"",constraint_type:"asap",constraint_date:"",Plan_Start_Date:"",Plan_End_Date:"",Plan_Duration:0,Actual_Start_Date:"",Actual_End_Date:"",Actual_Duration:0,Actual_Progress:0,Needed_Start_Date:"",Needed_End_Date:"",Needed_Duration:0,Target_Start_Date:"",Target_End_Date:"",Target_Duration:0,Start_Difference:0,End_Difference:0,Duration_Difference:0,Free_Float:0,Total_Float:0,Early_Start_Date:"",Laster_Start_Date:"",Laster_End_Date:"",Early_End_Date:"",Plan_Resources:0,Actual_Resources:0,Target_Resources:0,Laster_Progress_Date:"",Remark:"",Plan_Id:"",Control_Plan_Start_Date:"",Control_Plan_End_Date:"",Existing_Problems:"",Solutions:"",Need_Coordinate:"",Coordinate_Department:"",Responsible_User:""};t.ALL_TASK_COLUMNS=[{label:"作业名称",width:200,min_width:200,align:"left",name:"text",hide:!1,resize:!0,tree:!0},{label:"开始时间",width:120,align:"center",name:"Dynamic_Start_Date",hide:!1,resize:!0},{label:"完成时间",width:120,align:"center",name:"Dynamic_End_Date",hide:!1,resize:!0},{label:"工期",width:90,align:"center",name:"Dynamic_Duration",hide:!1,resize:!0},{label:"进度%",width:90,align:"center",name:"Actual_Progress",hide:!1,resize:!0,template:function(e){return"".concat((100*(e.Actual_Progress||0)).toFixed(1))}},{label:"责任人",width:100,align:"center",name:"Responsible_UserName",hide:!1,resize:!0},{label:"作业类型",width:100,align:"center",name:"type",hide:!1,resize:!0,template:function(e){return"project"===e.type?"WBS":"milestone"===e.type?"里程碑":"作业"}},{label:"备注",width:240,align:"center",name:"Remark",hide:!1,resize:!0},{label:"限制条件",width:160,align:"center",name:"constraint_type",hide:!1,resize:!0,template:function(e){return o.CONSTRAINT_TYPES.find((function(t){return t.value===e.constraint_type})).label}},{label:"限制日期",width:120,align:"center",name:"constraint_date",hide:!1,resize:!0,template:function(e){return(0,s.toDateStr)(e.constraint_date)}},{label:"计划工期",width:80,align:"center",name:"Plan_Duration",hide:!1,resize:!0},{label:"尚需工期",width:80,align:"center",name:"Needed_Duration",hide:!1,resize:!0},{label:"实际工期",width:80,align:"center",name:"Actual_Duration",hide:!1,resize:!0},{label:"自由时差",width:80,align:"center",name:"Free_Float",hide:!1,resize:!0},{label:"总时差",width:80,align:"center",name:"Total_Float",hide:!1,resize:!0},{label:"计划开始",width:120,align:"center",name:"Plan_Start_Date",hide:!1,resize:!0,template:function(e){return(0,s.toDateStr)(e.Plan_Start_Date)}},{label:"计划完成",width:120,align:"center",name:"Plan_End_Date",hide:!1,resize:!0,template:function(e){return(0,s.toDateStr)(e.Plan_End_Date)}},{label:"实际开始",width:120,align:"center",name:"Actual_Start_Date",hide:!1,resize:!0,template:function(e){return(0,s.toDateStr)(e.Actual_Start_Date)}},{label:"实际完成",width:120,align:"center",name:"Actual_End_Date",hide:!1,resize:!0,template:function(e){return(0,s.toDateStr)(e.Actual_Start_Date)}},{label:"目标开始",width:120,align:"center",name:"Actual_End_Date",hide:!1,resize:!0,template:function(e){return(0,s.toDateStr)(e.Actual_Start_Date)}}]},"447e":function(e,t,a){"use strict";a.r(t);var n=a("0078"),r=a("44aa");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("63bf");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"01c917ee",null);t["default"]=s.exports},"44aa":function(e,t,a){"use strict";a.r(t);var n=a("b26b"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"4678f":function(e,t,a){},"46a2":function(e,t,a){},"49bd":function(e,t,a){"use strict";a.r(t);var n=a("db05"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"4a77":function(e,t,a){"use strict";a("1f399")},"4bac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"ScheduleDialog",props:{plan:{type:Object,default:function(){return{}}},gantt:{type:Object,default:function(){return{}}}},data:function(){return{form:{Cur_Data_Date:"",Is_Save_History:!0}}},computed:{pickerOptions:function(){var e=this;return{disabledDate:function(t){return t<e.plan.Plan_Start_Date}}}},created:function(){this.form.Cur_Data_Date=this.plan.Cur_Data_Date},methods:{submit:function(){if(!this.form.Cur_Data_Date)return this.$message.warning("必须选择数据日期");this.$emit("dialogFormSubmitSuccess",{type:"onComputedSchedule",data:this.form})}}}},"4c15":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[a("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1),[e._t("default")],2)],1)},r=[]},"4cc7":function(e,t,a){"use strict";var n=a("4ea4").default,r=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("d81d"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("b680"),a("d3b7"),a("25f0"),a("159b");var i=r(a("f8e1")),o=n(a("e7cc")),s=a("2f7b"),l=a("a210"),c=(t.default={name:"DynamicDataTable",components:{GTableCell:i.default,NumberRange:o.default},props:{config:{type:Object,default:function(){return{}}},data:{type:Array,default:function(){return[]}},total:{type:Number,default:0},selectWidth:{type:Number,default:40},page:{type:Number,default:0},border:{type:Boolean,default:!1},stripe:{type:Boolean,default:!1},columns:{type:Array,default:function(){return[]}},cellEditorBlurSaveModel:{type:Boolean,default:!0},query:{type:Object,default:function(){return{}}},expends:{type:Array,default:function(){return[]}},sumValues:{type:Array,default:function(){return[]}},size:{type:String,default:"medium"},pagerCount:{type:Number,default:7},isRadio:{type:Boolean,default:!1}},data:function(){return{opts:{AutoIncrm_Id:0,Code:"",Create_Date:"",Create_UserId:"",Create_UserName:"",Data_Url:"",Data_Params:[],Display_Name:"",Height:null,Id:"",Is_Auto_Width:!0,Is_Deleted:!1,Is_Reserve:!1,Is_Edit:!1,Is_Filter:!1,Is_Page:!1,Is_Row_Number:!1,Is_Select:!1,Is_Sub_Grid:!1,Sub_Grid_Code:"",Is_Sortable:!0,Is_CustomSort:!1,Is_Summary:!1,Is_Break_Line:!1,Menu_Id:"",Modify_Date:"",Modify_UserId:"",Modify_UserName:"",Pager_Id:"",Remark:"",Row_Number:20,Sort_Column:"",Sort_Type:"desc",Table_Name:"",Width:0,Op_Width:160,Op_Label:"操作",Is_Expand_All:!1,Tree_Key:"Id",Is_Lazy:!1,Children_Field:"children",Has_Children_Field:"hasChildren"},CurrentPage:1,Total:0,currentRow:null,searchedField:{},searchBtnShown:!1,outerHeight:null,pagerH:66,editingRowStatus:{},checkedRows:[]}},computed:{tbHeight:function(){var e;return e=null===this.opts.Height||void 0===this.opts.Height?null:0===Number(this.opts.Height)?this.outerHeight:Number(this.opts.Height),this.opts.Is_Page&&null!==e&&(e-=this.pagerH),e},maxHeight:function(){return this.outerHeight&&null!==this.tbHeight?Number(this.opts.Height)>0?Number(this.opts.Height):this.opts.Is_Page?this.outerHeight-this.pagerH:this.outerHeight:null},tableData:function(){return this.sortData(this.$props.data)},headSearchEnable:function(){return this.opts.Is_Filter},orderedColumns:function(){var e=this.columns.concat([]);return e.sort((function(e,t){return Number(e.Sort)-Number(t.Sort)})),e.filter((function(e){return e.Is_Display}))},pageSizes:function(){if(this.opts.Page_Sizes)return this.opts.Page_Sizes;var e=l.tablePageSize,t=Number(this.opts.Row_Number);return t&&e.indexOf(t)<0&&(e=[t].concat(e),e.sort((function(e,t){return e-t}))),e}},watch:{total:function(e){this.Total=e},page:function(e){this.CurrentPage=e},sumValues:{handler:function(e){this.sumValues=e,this.customSummary({})},deep:!0},config:function(e){this.opts=Object.assign({},this.opts,e)},data:function(e){var t=this;this.opts.Is_Break_Line||setTimeout((function(){t.cellBreakLineHandle()}),100)}},created:function(){this.opts=Object.assign({},this.opts,this.config),this.searchedField=Object.assign({},this.query)},beforeDestroy:function(){(0,s.removeResizeListener)(this.$el,this.windowResizeHandler)},updated:function(){var e=this;this.opts.Is_Filter&&this.reposeSearchBtn(),setTimeout((function(){var t;null===(t=e.$refs.dtable)||void 0===t||t.doLayout()}),500)},mounted:function(){(0,s.addResizeListener)(this.$el,this.windowResizeHandler),this.getOuterHeight()},methods:{checkSelectable:function(e){return!this.opts.checkSelectable||"[object Function]"!==Object.prototype.toString.call(this.opts.checkSelectable)||this.opts.checkSelectable(e)},doEdit:function(e,t){this.$refs["g-cell-"+e+"-"+t][0].setEditMode(null)},editCell:function(e,t){var a;null===(a=this.$refs["g-cell-"+e+"-"+t][0])||void 0===a||a.setEditMode("ready")},cellBreakLineHandle:function(){var e;null===(e=this.$refs.cellbox)||void 0===e||e.forEach((function(e){var t=e.querySelector(".cell-span"),a=0;e.lastChild!==t&&"#comment"!==e.lastChild.nodeName&&(a=24),e.offsetWidth<t.offsetWidth+a&&(t.style.width=e.offsetWidth-a+"px")}))},windowResizeHandler:function(){var e;this.getOuterHeight(),this.opts.Is_Filter&&this.reposeSearchBtn(),null===(e=this.$refs.dtable)||void 0===e||e.doLayout()},getOuterHeight:function(){var e,t;this.outerHeight=null!==(e=null===(t=this.$el)||void 0===t||null===(t=t.parentNode)||void 0===t?void 0:t.offsetHeight)&&void 0!==e?e:null},showSearch:function(){this.searchBtnShown=!0},hideSearch:function(){this.searchBtnShown=!1},doSearch:function(){this.$emit("tableSearch",this.searchedField),this.hideSearch()},tableCustomSortChange:function(e){var t=e.column,a=e.prop,n=e.order;this.$emit("sort-change",{column:t,prop:a,order:n})},reposeSearchBtn:function(){var e,t,a,n=null===(e=this.$refs.dtable)||void 0===e||null===(e=e.$refs.headerWrapper)||void 0===e?void 0:e.offsetHeight,r=0;(this.$scopedSlots.op&&(r=this.opts.Op_Width+2),null!==(t=this.$refs.dtable)&&void 0!==t&&t.$refs.rightFixedPatch)&&(r+=null===(a=this.$refs.dtable)||void 0===a?void 0:a.$refs.rightFixedPatch.offsetWidth);this.$refs.searchbtn&&(this.$refs.searchbtn.style.top=n-8+"px",this.$refs.searchbtn.style.right=r+"px")},sortData:function(e){return"[object Array]"!==Object.prototype.toString.call(this.$props.data)&&(e=[]),e.concat([])},cdebug:function(e){},tableRowClassName:function(e){var t=e.row,a=(e.rowIndex,"");return t.onEdit&&(a="warning-row"),a},tableRowStyle:function(e){var t,a=e.row,n=e.rowIndex;return this.$emit("setTableRowStyle",{row:a,rowIndex:n},(function(e){t=e})),t},sortChange:function(e){e.column,e.order,e.prop},indexMethod:function(e){return e+1},columnSearchInput:function(e){e.property},columnSearchChange:function(e){e.property},handleSizeChange:function(e){this.$emit("gridSizeChange",{page:this.CurrentPage,size:e})},handleCurrentChange:function(e){this.$emit("gridPageChange",{page:e})},closeEditRow:function(e){delete this.editingRowStatus[e],this.editingRowStatus=Object.assign({},this.editingRowStatus)},handleCurrentRowChange:function(e){this.currentRow=e,this.$emit("currentRowChange",{row:e})},handleRowClick:function(e,t,a){this.$emit("handleRowClick",{row:e,column:t,event:a})},handleCellClick:function(e,t,a,n){},expandChange:function(e,t){this.$emit("rowExpanded",{row:e,expandedRows:t})},inlineEdited:function(e){var t=e.index,a=e.row,n=e.key,r=e.value;this.$emit("cellEditorChanged",{index:t,row:a,key:n,value:r})},lazyLoadChildren:function(e,t,a){this.$emit("lazyLoadChildrenNodes",{tree:e,treeNode:t,resolve:a})},customSummary:function(e){var t=this,a=e.columns,n=e.data;if(this.sumValues&&this.sumValues.length)return this.sumValues;var r={};return a.forEach((function(e,a){var i=t.columns.find((function(t){return t.Code===e.property}));if(0===a)r[a]="计";else if(r[a]="",null!==i&&void 0!==i&&i.SummaryMethod&&c[i.SummaryMethod]){var o=n.map((function(t){return Number(t[e.property])}));r[a]="".concat(c[i.SummaryMethod].label,": ").concat(c[i.SummaryMethod].func(o))}})),r},headerDragend:function(e,t,a,n){this.opts.Is_Filter&&this.reposeSearchBtn()},handleSelectionChange:function(e){this.checkedRows=e,this.$emit("multiSelectedChange",e)},changeRowEditMode:function(e){var t=e.index,a=e.mode,n={};n[t]=a,this.editingRowStatus=Object.assign({},this.editingRowStatus,n),this.$emit("rowEditModeChanged",this.editingRowStatus)},parseJsonRange:function(e){return(0,i.parseJsonRange)(e)},handleSelect:function(e,t){if(this.isRadio&&e.length>1){var a=e.shift();this.$refs.dtable.toggleRowSelection(a,!1)}this.$emit("select",{selection:e,row:t})},handleSelectAll:function(e){this.isRadio&&e.length>1&&(e.length=1),this.$emit("selectAll",e)},clearSelection:function(){this.$refs.dtable.clearSelection()},toggleRowSelection:function(e,t){this.$refs.dtable.toggleRowSelection(e,t)}}},{sum:{label:"总",func:function(e){var t=0;return e.forEach((function(e){return t+=Number(e)})),t.toFixed(2)}},avg:{label:"均",func:function(e){var t=0;return e.forEach((function(e){return t+=Number(e)})),(t/e.length).toFixed(2)}}})},"4d4a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("e9f5"),a("a732"),a("a9e3"),a("d3b7");var o=a("9643"),s=a("ed08");t.default={name:"SelectLocation",props:{value:{type:[Array,Number,String],default:""},disabled:{type:Boolean,default:!1},warehouseId:{type:String,default:""},nullIsFetch:{type:Boolean,default:!1}},data:function(){return{list:[],selectedValue:this.value}},watch:{warehouseId:function(){this.getList()},value:function(){this.selectedValue=Array.isArray(this.value)?(0,s.deepClone)(this.value):this.value}},created:function(){this.getList()},methods:{handleChange:function(e){this.$emit("input",e),this.$emit("change",e)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.warehouseId||e.nullIsFetch){t.n=1;break}return t.a(2);case 1:(0,o.GetLocationList)({Warehouse_Id:e.warehouseId}).then((function(t){e.list=t.Data||[],e.list.some((function(t){return t.Id===e.value}))||e.handleChange("")}));case 2:return t.a(2)}}),t)})))()}}}},"4dd4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=s,a("b0c0");var r=n(a("9b15")),i=a("21c4"),o=a("6186");function s(e,t){var a=t.AccessKeyId,n=t.AccessKeySecret,s=t.bucket,l=t.regionId,c=t.SecurityToken,d=new r.default({region:"oss-".concat(l),secure:!0,stsToken:c,accessKeyId:a,accessKeySecret:n,bucket:s}),u=null,f=e.pointFile||null,p=new Date,h=e.file,m=(0,i.getTenantId)()+"/"+p.getFullYear()+"/"+(p.getMonth()+1)+"/"+p.getDate()+"/"+h.name;d.multipartUpload(m,e.file,{parallel:6,partSize:2097152,checkpoint:f,progress:function(t,a){u=a,e.onProgress({percent:Math.floor(100*t)})}}).then((function(t){if(200===t.res.statusCode){var a=t.res.requestUrls[0]&&t.res.requestUrls[0].split("?")[0];(0,o.GetOssUrl)({url:a}).then((function(t){e.onSuccess({Data:a+"*"+h.size+"*"+h.name.substr(h.name.lastIndexOf("."))+"*"+h.name,encryptionUrl:t.Data})}))}}),(function(t){e.onError(t,u)}))}},"4de7":function(e,t,a){"use strict";a("158a")},5254:function(e,t,a){"use strict";a.r(t);var n=a("7a3c"),r=a("73876");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("cf1f"),a("9d35");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"459bbb4d",null);t["default"]=s.exports},5585:function(e,t,a){"use strict";var n=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.PLAN_FIELDS=void 0,t.calcPlanDurations=h,t.compute=y,t.createDefaultCalendar=l,t.createEmptePlanData=c,t.createEmptyPlan=d,t.getDatesRange=_,t.parseServerPlanEntity=v,t.updateCalendar=m,t.updatePlanField=u,a("99af"),a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("b64b"),a("d3b7"),a("159b"),a("c73d");var r=n(a("c1df")),i=a("43fe"),o=a("19cb"),s=t.PLAN_FIELDS={Id:"",Code:"",Plan_Auth:"负责",Name:"新建计划",Type_Id:"0",Is_Main_Plan:!1,Is_Target_Plan:!1,Is_Allow_Collaboration:!1,Dynamic_Start_Date:r(new Date).startOf("date").format("YYYY-MM-DD"),Dynamic_End_Date:void 0,Dynamic_Duration:0,Percent_Complete:0,Plan_Start_Date:r(new Date).startOf("date").toDate(),Plan_End_Date:void 0,Plan_Duration:0,Actual_Start_Date:void 0,Actual_End_Date:void 0,Actual_Duration:0,Resources:"1",Plan_Oauth:"0",Admin:[],Observer:[],Responsible_User:"",Target_Plan_Id:"",Plan_Calendar:JSON.parse('{"week_calendar":[1,1,1,1,1,1,1], "out_calendar":[]}'),Cur_Data_Date:r(new Date).startOf("date").toDate(),Remark:"",Plan_Data:JSON.parse('{"data":[], "links":[]}')};function l(){return JSON.parse('{"week_calendar":[1,1,1,1,1,1,1], "out_calendar":[]}')}function c(){return JSON.parse('{"data":[], "links":[]}')}function d(){var e={};for(var t in s)e[t]=s[t];return e}function u(e,t,a){if("Plan_Start_Date"==t||"Plan_End_Date"==t)return f(e,t,a);e[t]=a}function f(e,t,a){e[t]=r(a).startOf("date").toDate(),e.Plan_Start_Date&&e.Plan_End_Date?e.Plan_Duration=p(e.Plan_Start_Date,e.Plan_End_Date):e.Plan_Duration=0}function p(e,t){var a=[r(e).format("x"),r(t).format("x")];return a.sort(),r(parseInt(a[1])).diff(parseInt(a[0]),"days")+1}function h(e){e.Plan_Start_Date&&e.Plan_End_Date?e.Plan_Duration=p(e.Plan_Start_Date,e.Plan_End_Date):e.Plan_Duration=0}function m(e,t,a){e.Plan_Calendar=a,a.week_calendar.forEach((function(e,a){var n=a,r=0!==e&&["0:00-23:59"];t.setWorkTime({day:n,hours:r})})),a.out_calendar.forEach((function(e){var a=0!==e.type&&["0:00-23:59"],n=_(e);n.forEach((function(e){t.setWorkTime({date:r(e).startOf("date").toDate(),hours:a})}))})),t.render()}function _(e){for(var t=e.start_date,a=e.end_date,n=[],i=r(a).diff(t,"days"),o=0;o<=i;o++){var s=r(t).add(o,"days").format("YYYY-MM-DD");n.push(s)}return n}var g=["Actual_End_Date","Actual_Start_Date","Cur_Data_Date","Plan_End_Date","Plan_Start_Date"];function v(e){var t,a,n,o,s={};if(Object.keys(e).forEach((function(t){g.indexOf(t)>-1?s[t]=e[t]?r(e[t]).startOf("date").toDate():null:s[t]=e[t]})),!s["Plan_Calendar"])try{var c;s["Plan_Calendar"]=null!==(c=JSON.parse(s["Calendar"]))&&void 0!==c?c:l()}catch(f){s["Plan_Calendar"]=l()}if(s["Observer"]&&s["Observer"].indexOf("[")>-1)try{var d;s["Observer"]=null!==(d=JSON.parse(s["Observer"]))&&void 0!==d?d:[]}catch(f){s["Observer"]=[]}else s["Observer"]=null!==(t=null===(a=s["Observer"])||void 0===a?void 0:a.split(","))&&void 0!==t?t:[];if(s["Admin"]&&s["Admin"].indexOf("[")>-1)try{var u;s["Admin"]=null!==(u=JSON.parse(s["Admin"]))&&void 0!==u?u:[]}catch(f){s["Admin"]=[]}else s["Admin"]=null!==(n=null===(o=s["Admin"])||void 0===o?void 0:o.split(","))&&void 0!==n?n:[];return s["Plan_Data"].data.forEach((function(e,t){s["Plan_Data"].data[t]=(0,i.parseServeTask)(e)})),s}function y(e,t){return new Promise((function(a,n){if(!t.Cur_Data_Date)return n("计划状态日期不能为空");var r=e.getTaskByTime(),i=e.getLinks();b(t,r,i,e).then((function(){a({plan:t,tasks:r})})).catch((function(e){n(e)}))}))}function b(e,t,a,n){return(0,o.calc)(n,e,t,a).then((function(e){})).catch((function(e){throw e}))}},"5b55":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"-16px",padding:"0 10px"}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"当前计划"}},[a("h2",{staticStyle:{"font-size":"1.8em",color:"#222"}},[e._v(e._s(e.plan.Name))])]),a("el-form-item",{attrs:{label:"数据日期"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期","picker-options":e.pickerOptions},model:{value:e.form.Cur_Data_Date,callback:function(t){e.$set(e.form,"Cur_Data_Date",t)},expression:"form.Cur_Data_Date"}})],1),a("el-form-item",[a("p",[e._v(" 保存当前进度更新结果为历史版本 "),a("el-switch",{staticStyle:{"margin-top":"-2px"},attrs:{"active-color":"#13ce66","inactive-color":"#DDD"},model:{value:e.form.Is_Save_History,callback:function(t){e.$set(e.form,"Is_Save_History",t)},expression:"form.Is_Save_History"}})],1)]),a("el-form-item",{staticStyle:{"margin-top":"32px"},attrs:{align:"right"}},[a("el-button",{on:{click:function(t){return e.$emit("dialogCancel")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确定")])],1)],1)],1)},r=[]},"5bfd9":function(e,t,a){"use strict";a("a46f")},"5d4b":function(e,t,a){"use strict";a.r(t);var n=a("f0d4"),r=a("9ed6");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"5e1a9":function(e,t,a){"use strict";a.r(t);var n=a("ea05"),r=a("c9d8");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("1adb");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"63bf":function(e,t,a){"use strict";a("c670")},6705:function(e,t,a){"use strict";a("c145")},6941:function(e,t,a){"use strict";a("46a2")},6971:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9f5"),a("7d54"),a("d3b7"),a("159b");var r=n(a("c14f")),i=n(a("1da1")),o=a("3c4a"),s=a("4744"),l=a("6186");t.default={name:"SelectDepartment",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1}},data:function(){return{list:[],isProductWeight:!0,treeParamsDepart:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}}}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.value),this.$emit("change",this.value)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:(0,s.GetPreferenceSettingValue)({code:"Productweight"}).then((function(t){t.IsSucceed?(e.isProductWeight="true"===t.Data,e.isProductWeight?(0,o.GetFirstLevelDepartsUnderFactory)({FactoryId:localStorage.getItem("CurReferenceId")}).then((function(t){e.list=t.Data})):e.getDepartmentTree()):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getDepartmentTree:function(){var e=this,t=function(e){e&&e.forEach((function(e){var a=e.Children;!0===e.Data.Is_Company||"1"==e.Data.Type?e.disabled=!0:e.disabled=!1,a.length>0&&t(a)}))};(0,l.GetCompanyDepartTree)({isAll:!0}).then((function(a){if(a.IsSucceed){var n=a.Data;t(n),e.$nextTick((function(t){var a;null===(a=e.$refs)||void 0===a||null===(a=a.treeSelectDepart)||void 0===a||a.treeDataUpdateFun(n)}))}else e.$message({message:a.Message,type:"error"})}))}}}},"6ab8":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{class:{"dynamic-table":!0,"head-search":e.headSearchEnable},style:{minHeight:Number(e.opts.Height)>0&&Number(e.opts.Height)+"px"}},[a("div",{ref:"tableWrapper",staticClass:"table-wrapper",staticStyle:{position:"relative",overflow:"hidden"}},[e.opts.Is_Filter&&e.searchBtnShown?a("div",{ref:"searchbtn",staticClass:"fixed-search",style:{position:"absolute",zIndex:1e3}},[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:e.doSearch}},[e._v("搜索")])],1):e._e(),a("el-table",{key:e.opts.Code,ref:"dtable",style:{width:!e.opts.Is_Auto_Width&&(0!=e.opts.Width&&Number(e.opts.Width)+"px")},attrs:{size:e.size,border:e.border,stripe:e.stripe,data:e.tableData,"highlight-current-row":"","row-style":e.tableRowStyle,"row-class-name":e.tableRowClassName,"default-expand-all":e.opts.Is_Expand_All,"row-key":e.opts.Tree_Key,"expand-row-keys":e.expends,height:e.tbHeight,"max-height":e.maxHeight,lazy:e.opts.Is_Lazy,load:e.lazyLoadChildren,"tree-props":{children:e.opts.Children_Field,hasChildren:e.opts.Has_Children_Field},"show-summary":e.opts.Is_Summary,"summary-method":e.customSummary},on:{"current-change":e.handleCurrentRowChange,"row-click":e.handleRowClick,"cell-click":e.handleCellClick,"expand-change":e.expandChange,"header-dragend":e.headerDragend,"selection-change":e.handleSelectionChange,select:e.handleSelect,"select-all":e.handleSelectAll,"sort-change":e.tableCustomSortChange}},[e.opts.Is_Select?a("el-table-column",{attrs:{type:"selection","reserve-selection":e.opts.Is_Reserve,width:e.selectWidth,fixed:"left",align:"center",resizable:!1,selectable:e.checkSelectable,"show-overflow-tooltip":""},scopedSlots:e._u([{key:"header",fn:function(t){var n=t.column;return[e._v(" "+e._s(n.label)+" "),e.opts.Is_Filter?a("div",{staticClass:"custom-filter",on:{click:function(e){e.stopPropagation()}}}):e._e()]}}],null,!1,2603080089)}):e._e(),e.opts.Is_Sub_Grid?a("el-table-column",{attrs:{type:"expand",label:"展开",resizable:!1},scopedSlots:e._u([{key:"header",fn:function(t){var n=t.column;return[e._v(" "+e._s(n.label)+" "),e.opts.Is_Filter?a("div",{staticClass:"custom-filter",on:{click:function(e){e.stopPropagation()}}}):e._e()]}},{key:"default",fn:function(t){var a=t.row,n=t.column,r=t.$index;return[e._t("expand",null,{row:a,column:n,$index:r})]}}],null,!0)}):e._e(),e.opts.Is_Row_Number?a("el-table-column",{attrs:{type:"index",label:"序号",width:"50",align:"center",index:e.indexMethod,fixed:"left",resizable:!1},scopedSlots:e._u([{key:"header",fn:function(t){var n=t.column;return[e._v(" "+e._s(n.label)+" "),e.opts.Is_Filter?a("div",{staticClass:"custom-filter",on:{click:function(e){e.stopPropagation()}}}):e._e()]}}],null,!1,2603080089)}):e._e(),e._l(e.orderedColumns,(function(t){return a("el-table-column",{key:t.Column_Id,attrs:{prop:t.Code,sortable:t.Is_CustomSort?"custom":!!t.Is_Sortable,"min-width":Number(t.Width)<130?130:Number(t.Width),label:t.Display_Name,resizable:t.Is_Resizable||!0,fixed:!!t.Is_Frozen&&(t.Frozen_To?t.Frozen_To:"left"),"show-summary":t.SummaryMethod,align:t.Align?t.Align:"left","show-overflow-tooltip":"","sort-method":t.sortMethod},scopedSlots:e._u([{key:"header",fn:function(n){var r=n.column;return[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:r.label,placement:"top-start"}},[a("span",{staticClass:"cs-header-title"},[e._v(e._s(r.label))])]),e.opts.Is_Filter?a("div",{staticClass:"custom-filter",on:{click:function(e){e.stopPropagation()}}},[t.Is_Filter?[e.$scopedSlots["hsearch_"+t.Code]?e._t("hsearch_"+t.Code,null,{column:t}):"date"===t.Filter_Type?a("el-date-picker",{staticClass:"cell-control",staticStyle:{width:"96%"},attrs:{type:"date",size:"mini",placeholder:"选择日期","value-format":t.Formatter,format:t.Formatter,clearable:""},on:{focus:e.showSearch,change:function(a){return e.$emit("columnSearchChange",{column:t,value:a})}},model:{value:e.searchedField[t.Code],callback:function(a){e.$set(e.searchedField,t.Code,a)},expression:"searchedField[col.Code]"}}):"daterange"===t.Filter_Type?a("el-date-picker",{staticClass:"cell-control",staticStyle:{width:"96%"},attrs:{type:"daterange","range-separator":"~",size:"mini","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":t.Formatter,format:t.Formatter,clearable:""},on:{focus:e.showSearch,change:function(a){return e.$emit("columnSearchChange",{column:t,value:a})}},model:{value:e.searchedField[t.Code],callback:function(a){e.$set(e.searchedField,t.Code,a)},expression:"searchedField[col.Code]"}}):"radio"===t.Filter_Type||"switch"===t.Filter_Type||"boolean"===t.Filter_Type?a("el-select",{staticStyle:{width:"96%"},attrs:{filterable:"",placeholder:"请选择",clearable:""},on:{focus:e.showSearch,change:function(a){return e.$emit("columnSearchChange",{column:t,value:a})}},model:{value:e.searchedField[t.Code],callback:function(a){e.$set(e.searchedField,t.Code,a)},expression:"searchedField[col.Code]"}},e._l(e.parseJsonRange(t.Range),(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):"numberrange"===t.Filter_Type?a("NumberRange",{attrs:{min:e.parseJsonRange(t.Range)[0],max:e.parseJsonRange(t.Range)[1]},on:{focus:e.showSearch,change:function(a){return e.$emit("columnSearchChange",{column:t,value:a})}},model:{value:e.searchedField[t.Code],callback:function(a){e.$set(e.searchedField,t.Code,a)},expression:"searchedField[col.Code]"}}):a("el-input",{attrs:{placeholder:"搜索...",clearable:""},on:{focus:e.showSearch,change:function(a){return e.$emit("columnSearchChange",{column:t,value:a})}},model:{value:e.searchedField[t.Code],callback:function(a){e.$set(e.searchedField,t.Code,a)},expression:"searchedField[col.Code]"}})]:e._e()],2):e._e()]}},{key:"default",fn:function(n){var r=n.row,i=n.$index,o=n.store;return[e.$scopedSlots[t.Code]?e._t(t.Code,null,{$index:i,column:t,row:r,store:o}):a("el-tooltip",{attrs:{disabled:!t.IS_ToolTip,effect:"light"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._t("tooltip",null,{column:t,row:r,$index:i})],2),a("div",{ref:"cellbox",refInFor:!0,class:{"dtb-cell-break-line":e.opts.Is_Break_Line,"dtb-cell-nowrap":!e.opts.Is_Break_Line},style:{textOverflow:t.IS_ToolTip?"ellipsis":""}},[a("GTableCell",{key:t.Code,ref:"g-cell-"+t.Code+"-"+i,refInFor:!0,attrs:{"show-tooltip":!e.opts.Is_Break_Line,editable:e.opts.Is_Edit,"edit-mode":e.editingRowStatus[i],"row-index":i,column:t,row:r,options:[],"cell-editor-blur-save-model":e.cellEditorBlurSaveModel},on:{inlineEdited:e.inlineEdited,changeRowEditMode:e.changeRowEditMode}}),e.opts.Is_Edit&&t.Is_Edit&&!e.editingRowStatus[i]?a("span",{staticClass:"editme",on:{click:function(a){return a.stopPropagation(),e.editCell(t.Code,i)}}},[a("i",{staticClass:"el-icon-edit"})]):e._e(),e.opts.Is_Edit&&t.Is_Edit&&e.editingRowStatus[i]?a("span",{staticClass:"editme",staticStyle:{color:"#67C23A"},on:{click:function(a){return a.stopPropagation(),e.doEdit(t.Code,i)}}},[a("i",{staticClass:"el-icon-check"})]):e._e(),e._t("innertip",null,{column:t,row:r,$index:i,store:o}),e.$scopedSlots["innertip-"+t.Code]?[e._t("innertip-"+t.Code,null,{column:t,$index:i,row:r})]:e._e()],2)])]}}],null,!0)})})),e.$scopedSlots.op?[a("el-table-column",{staticClass:"cs-op",attrs:{align:"center","min-width":e.opts.Op_Width,width:e.opts.Op_Width,label:e.opts.Op_Label,fixed:"right",resizable:!1,"show-overflow-tooltip":""},scopedSlots:e._u([{key:"header",fn:function(t){var n=t.column;return[e._v(" "+e._s(n.label)+" "),e.opts.Is_Filter?a("div",{staticClass:"custom-filter",on:{click:function(e){e.stopPropagation()}}}):e._e()]}},{key:"default",fn:function(t){var a=t.row,n=t.$index;return[e._t("op",null,{row:a,$index:n})]}}],null,!0)})]:e._e(),e.$slots.append?a("template",{slot:"append"},[e._t("append")],2):e._e(),a("template",{slot:"empty"},[a("div",{staticClass:"empty-x"},[a("div",{staticClass:"empty"}),a("p",[e._v("暂无内容")])])])],2)],1),a("div",{staticClass:"custom-pagination"},[e.opts.Is_Select&&e.opts.Is_Page?a("div",{staticClass:"checked-count"},[a("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[e._v("已选"+e._s(e.checkedRows.length)+"条数据")]),e._t("tipLabel",null,null,{})],2):e._e(),e.opts.Is_Page?a("el-pagination",{staticClass:"pagination",style:{textAlign:e.opts.Pager_Align||"left",display:"flex",alignItems:"center",justifyContent:"right"===e.opts.Pager_Align?"flex-end":"center"===e.opts.Pager_Align?"center":"flex-end"},attrs:{"pager-count":e.pagerCount,background:"","current-page":e.CurrentPage,"page-sizes":e.pageSizes,"page-size":Number(e.opts.Row_Number),layout:e.opts.Pager_Layout?e.opts.Pager_Layout:"total, sizes, prev, pager, next, jumper",total:e.Total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}):e._e()],1)])},r=[]},"6b16":function(e,t,a){"use strict";a.r(t);var n=a("8b35"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"6d9f":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"flex-direction":"column","align-items":"center",position:"relative"}},[a("el-button",{attrs:{size:"mini",id:"restore"},on:{click:e.restoreDefault}},[e._v("恢复默认")]),a("TreeTransfer",{attrs:{mode:"transferSort","check-strictly":!1,"up-down-disable":!0,from_data:e.all,to_data:e.selected,all_data:e.oall,title:["可用的选项","已选的选项"],height:"540px",pid:"ParentNodes","default-props":{label:"Label",children:"Children"},filter:"","open-all":"",showbutton:""},on:{cancelDialog:e.cancel,submitDialog:e.submit}}),e._e()],1)},r=[]},"6e7d":function(e,t,a){"use strict";a.r(t);var n=a("e7ed"),r=a("c95f");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("0892");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"1e4f62d4",null);t["default"]=s.exports},"6f1b":function(e,t,a){},"6f5e":function(e,t,a){"use strict";a.r(t);var n=a("6d9f"),r=a("fe99");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("6941");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"6e77dd3a",null);t["default"]=s.exports},7218:function(e,t,a){"use strict";a("6f1b")},7359:function(e,t,a){},73876:function(e,t,a){"use strict";a.r(t);var n=a("312c"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"74a4":function(e,t,a){"use strict";a.r(t);var n=a("9812"),r=a("ea81");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},7962:function(e,t,a){"use strict";a.r(t);var n=a("1455"),r=a("28a5");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("1246");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"73cee068",null);t["default"]=s.exports},"79ed":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("a9e3");var o=a("1b69"),s=a("ed08");t.default={name:"SelectProject",props:{value:{type:[Array,Number,String],default:""},hasNoProject:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{projectList:[],selectedValue:""}},watch:{value:function(){this.selectedValue=Array.isArray(this.value)?(0,s.deepClone)(this.value):this.value}},created:function(){this.getFactoryProjectList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getFactoryProjectList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,o.GetProjectPageList)({PageSize:-1});case 1:a=t.v,e.projectList=a.Data.Data;case 2:return t.a(2)}}),t)})))()}}}},"7a3c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"calendar-set gantt-calendar"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:10}},[a("div",{staticClass:"custom-calendar-wrapper"},[a("div",{staticClass:"month-year-controls"},[a("div",{staticClass:"backward"},[a("i",{staticClass:"el-icon-d-arrow-left",attrs:{title:"上一年"},on:{click:e.prevYear}}),a("i",{staticClass:"el-icon-arrow-left",attrs:{title:"上个月"},on:{click:e.prevMonth}})]),a("div",{staticClass:"forward"},[a("i",{staticClass:"el-icon-arrow-right",attrs:{title:"下个月"},on:{click:e.nextMonth}}),a("i",{staticClass:"el-icon-d-arrow-right",attrs:{title:"下一年"},on:{click:e.nextYear}})])]),a("el-calendar",{ref:"myCalendar",staticClass:"custom-calendar",scopedSlots:e._u([{key:"dateCell",fn:function(t){var n=t.date,r=t.data;return[a("div",{class:"c-date "+e.exceptionType(r.day),attrs:{date:r.day}},["normal"!==e.exceptionType(r.day)?a("div",{staticClass:"ctag"},[e._v(" "+e._s("workday"===e.exceptionType(r.day)?"班":"holiday"===e.exceptionType(r.day)?"休":"")+" ")]):e._e(),e._v(" "+e._s(n.getDate())+" ")])]}}])})],1),a("p",{staticStyle:{"text-align":"center",color:"#CCC"}},[e._v("日历预览")])]),a("el-col",{attrs:{span:14}},[a("div",{staticClass:"weekend-set"},[a("h3",[e._v("工作时间")]),a("el-checkbox-group",{on:{change:e.workWeekChange},model:{value:e.workWeek,callback:function(t){e.workWeek=t},expression:"workWeek"}},[e._l(e.weekdays,(function(t){return[a("el-checkbox",{key:t.value,attrs:{disabled:!e.editMode,label:t.value}},[e._v(e._s(t.label))])]}))],2)],1),a("div",{staticClass:"exception-set"},[a("div",{staticClass:"header"},[a("strong",[e._v("例外名称")]),a("strong",[e._v("例外日期")]),a("strong",[e._v("设为")])]),a("div",{staticClass:"scroll"},e._l(e.exceptions,(function(t,n){return a("div",{key:n,staticClass:"s-row"},[a("div",[e.editMode?a("el-input",{staticStyle:{width:"90%"},attrs:{size:"mini",type:"text",placeholder:"例外名称"},on:{change:function(a){return e.exceptionChange(t,"name",a)}},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"ex.name"}}):a("span",[e._v(e._s(t.name))])],1),a("div",[e.editMode?a("el-date-picker",{staticStyle:{width:"90%"},attrs:{type:"daterange",size:"mini",placeholder:"例外时间","value-format":"yyyy-MM-dd"},on:{change:function(a){return e.exceptionChange(t,"date",a)}},model:{value:t.date,callback:function(a){e.$set(t,"date",a)},expression:"ex.date"}}):a("span",[e._v(" "+e._s(t.date.join(" ~ "))+" ")])],1),a("div",[a("el-switch",{staticStyle:{display:"block"},attrs:{disabled:!e.editMode,width:42,"active-color":"#ff4949","inactive-color":"#13ce66","active-text":"班","inactive-text":"休"},on:{change:function(a){return e.exceptionChange(t,"isWorkday",a)}},model:{value:t.isWorkday,callback:function(a){e.$set(t,"isWorkday",a)},expression:"ex.isWorkday"}}),e.editMode?a("el-link",{attrs:{underline:!1,icon:"el-icon-close"},on:{click:function(t){return e.removeException(n)}}}):e._e()],1)])})),0),a("div",[e.editMode?a("el-link",{attrs:{underline:!1,icon:"el-icon-plus",type:"primary"},on:{click:e.addException}},[e._v("新增例外")]):e._e()],1)])])],1),a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{on:{click:function(t){return e.$emit("dialogCancel")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.saveCalendar}},[e._v("确定")])],1)],1)},r=[]},"7b1b":function(e,t,a){"use strict";a("d0fe")},"7b38":function(e,t,a){},"7c48":function(e,t,a){"use strict";a.r(t);var n=a("b407"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"7cf8":function(e,t,a){"use strict";a.r(t);var n=a("b17b"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"7e87":function(e,t,a){"use strict";a.r(t);var n=a("d68c"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"7ee7":function(e,t,a){"use strict";a.r(t);var n=a("4cc7"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"7f6a":function(e,t,a){"use strict";a("c7ad")},8215:function(e,t,a){"use strict";a.r(t);var n=a("a14b0"),r=a("97c9");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"83b8e":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"flow-attr-box",staticStyle:{height:"100%"}},[a("el-tabs",{staticStyle:{height:"100%"},attrs:{value:e.activeKey,size:"small"}},[a("el-tab-pane",{attrs:{disabled:"",name:"flow-attr"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("i",{staticClass:"el-icon-s-operation"}),e._v(" 流程属性 ")]),a("el-form",{attrs:{layout:"horizontal"}},[a("el-form-item",{attrs:{"label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,label:"流程id",size:"small"}},[a("el-input",{attrs:{value:e.flowData.attr.id,disabled:""}})],1)],1)],1),a("el-tab-pane",{attrs:{disabled:"",name:"node-attr"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("i",{staticClass:"el-icon-notebook-2"}),e._v(" 节点属性 ")]),e.currentSelect&&e.currentSelect.id?[a("el-form",{ref:"dataForm",attrs:{model:e.currentSelect,rules:e.rules}},[a("el-form-item",{attrs:{label:"Id",prop:"id",size:"small"}},[a("el-input",{attrs:{disabled:"",readonly:""},model:{value:e.currentSelect.id,callback:function(t){e.$set(e.currentSelect,"id",t)},expression:"currentSelect.id"}})],1),a("el-form-item",{attrs:{size:"small"}},[a("el-checkbox",{model:{value:e.currentSelect.isadd,callback:function(t){e.$set(e.currentSelect,"isadd",t)},expression:"currentSelect.isadd"}},[e._v("是否加签")]),a("el-checkbox",{model:{value:e.currentSelect.mustadd,callback:function(t){e.$set(e.currentSelect,"mustadd",t)},expression:"currentSelect.mustadd"}},[e._v("必须加签")]),a("el-checkbox",{model:{value:e.currentSelect.Is_CallBack,callback:function(t){e.$set(e.currentSelect,"Is_CallBack",t)},expression:"currentSelect.Is_CallBack"}},[e._v("是否自动回调")])],1),a("el-form-item",{attrs:{label:"编号",prop:"Code",size:"small"}},[a("el-input",{attrs:{placeholder:"非开发人员可不填"},model:{value:e.currentSelect.Code,callback:function(t){e.$set(e.currentSelect,"Code",t)},expression:"currentSelect.Code"}})],1),a("el-form-item",{attrs:{label:"名称",prop:"name",size:"small"}},[a("el-input",{model:{value:e.currentSelect.name,callback:function(t){e.$set(e.currentSelect,"name",t)},expression:"currentSelect.name"}})],1),e.currentSelect.setInfo?[a("el-form-item",{attrs:{label:"三方回调URL",prop:"ThirdPartyUrl",size:"small"}},[a("el-input",{model:{value:e.currentSelect.setInfo.ThirdPartyUrl,callback:function(t){e.$set(e.currentSelect.setInfo,"ThirdPartyUrl",t)},expression:"currentSelect.setInfo.ThirdPartyUrl"}})],1),a("el-form-item",{attrs:{label:"执行权限",prop:"NodeDesignate",size:"small"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{"popper-append-to-body":!1,placeholder:"请选择"},on:{change:e.handleChangeRoles},model:{value:e.currentSelect.setInfo.NodeDesignate,callback:function(t){e.$set(e.currentSelect.setInfo,"NodeDesignate",t)},expression:"currentSelect.setInfo.NodeDesignate"}},e._l(e.NodeDesignates,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),"SPECIAL_USER"===e.currentSelect.setInfo.NodeDesignate?a("el-form-item",{attrs:{label:"指定用户",prop:"NodeDesignateUsers",size:"small"}},[a("select-users",{attrs:{"group-names":e.currentSelect.setInfo.NodeDesignateData.groupText,"user-names":e.currentSelect.setInfo.NodeDesignateData.usersText,"role-names":e.currentSelect.setInfo.NodeDesignateData.rolesText,"department-names":e.currentSelect.setInfo.NodeDesignateData.departmentsText,group:e.currentSelect.setInfo.NodeDesignateData.group,users:e.currentSelect.setInfo.NodeDesignateData.users,roles:e.currentSelect.setInfo.NodeDesignateData.roles,departments:e.currentSelect.setInfo.NodeDesignateData.departments},on:{"update:groupNames":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"groupText",t)},"update:group-names":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"groupText",t)},"update:userNames":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"usersText",t)},"update:user-names":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"usersText",t)},"update:roleNames":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"rolesText",t)},"update:role-names":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"rolesText",t)},"update:departmentNames":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"departmentsText",t)},"update:department-names":function(t){return e.$set(e.currentSelect.setInfo.NodeDesignateData,"departmentsText",t)},"users-change":e.usersChange,"roles-change":e.rolesChange,"group-change":e.groupChange,"departments-change":e.departmentsChange}})],1):e._e(),"fork"===e.currentSelect.type?a("el-form-item",{attrs:{label:"会签类型",prop:"NodeConfluenceType",size:"small"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"100%"},attrs:{"popper-append-to-body":!1,placeholder:"请选择"},model:{value:e.currentSelect.setInfo.NodeConfluenceType,callback:function(t){e.$set(e.currentSelect.setInfo,"NodeConfluenceType",t)},expression:"currentSelect.setInfo.NodeConfluenceType"}},e._l(e.NodeConfluenceTypes,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e._e()]:e._e()],2)]:e._e()],2),a("el-tab-pane",{attrs:{disabled:"",name:"link-attr"}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("i",{staticClass:"el-icon-share"}),e._v(" 连线属性 ")]),a("el-form",{attrs:{"label-position":"top"}},[a("el-form-item",{attrs:{"label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,label:"id",size:"small"}},[a("el-input",{attrs:{value:e.currentSelect.id,disabled:""}})],1),a("el-form-item",{attrs:{"label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,label:"源节点",size:"small"}},[a("el-input",{attrs:{value:e.currentSelect.from,disabled:""}})],1),a("el-form-item",{attrs:{"label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,label:"目标节点",size:"small"}},[a("el-input",{attrs:{value:e.currentSelect.to,disabled:""}})],1),a("el-form-item",{attrs:{"label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,label:"文本",size:"small"}},[a("el-input",{on:{change:e.linkLabelChange},model:{value:e.currentSelect.label,callback:function(t){e.$set(e.currentSelect,"label",t)},expression:"currentSelect.label"}})],1),a("el-form-item",{staticStyle:{display:"inline-block"},attrs:{"label-col":e.formItemLayout.labelCol,"wrapper-col":e.formItemLayout.wrapperCol,label:"表单字段条件",size:"small"}},e._l(e.Compares,(function(t,n){return a("div",{key:n,staticStyle:{"margin-bottom":"5px"}},[a("el-select",{staticStyle:{width:"110px"},attrs:{"popper-append-to-body":!1,placeholder:"请选择"},model:{value:t.FieldName,callback:function(a){e.$set(t,"FieldName",a)},expression:"compare.FieldName"}},e._l(e.formTemplate,(function(e){return a("el-option",{key:e.Value,attrs:{label:e.Text,value:e.Value}})})),1),a("el-select",{staticStyle:{width:"85px"},attrs:{disabled:!t.FieldName,"popper-append-to-body":!1,placeholder:"请选择"},model:{value:t.Operation,callback:function(a){e.$set(t,"Operation",a)},expression:"compare.Operation"}},[a("el-option",{attrs:{label:">",value:">"}}),a("el-option",{attrs:{label:">=",value:">="}}),a("el-option",{attrs:{label:"<",value:"<"}}),a("el-option",{attrs:{label:"<=",value:"<="}}),a("el-option",{attrs:{label:"=",value:"="}}),a("el-option",{attrs:{label:"!=",value:"!="}})],1),a("el-input",{staticStyle:{width:"80px"},attrs:{disabled:!t.FieldName,placeholder:"值"},model:{value:t.Value,callback:function(a){e.$set(t,"Value",a)},expression:"compare.Value"}}),0===n?a("el-button",{staticStyle:{"margin-left":"5px"},attrs:{icon:"el-icon-plus",size:"mini",title:"并且",type:"primary"},on:{click:e.btnAddCompare}}):e._e(),0!==n?a("el-button",{attrs:{icon:"el-icon-delete",size:"mini",title:"删除",type:"danger"},on:{click:function(t){return e.btnDelCompare(n)}}}):e._e()],1)})),0)],1)],1)],1)],1)},r=[]},8532:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("e9f5"),a("910d"),a("d3b7"),a("25f0");var r=n(a("f917"));t.default={components:{selectTabCom:r.default},props:{group:{type:Array,default:function(){return[]}},users:{type:Array,default:function(){return[]}},roles:{type:Array,default:function(){return[]}},departments:{type:Array,default:function(){return[]}},groupNames:{type:String,default:""},userNames:{type:String,default:""},roleNames:{type:String,default:""},departmentNames:{type:String,default:""},orgId:{type:String,default:""}},data:function(){return{dialogVisible:!1,selectUserList:[]}},computed:{selectGroup:{get:function(){return this.group},set:function(e){this.$emit("group-change","group",e)}},selectUsers:{get:function(){return this.users},set:function(e){this.$emit("users-change","users",e)}},selectDepartments:{get:function(){return this.departments},set:function(e){this.$emit("departments-change","departments",e)}},selectRoles:{get:function(){return this.roles},set:function(e){this.$emit("roles-change","roles",e)}},groupNamesText:{get:function(){return this.groupNames},set:function(e){this.$emit("group-change","groupText",e)}},userNamesText:{get:function(){return this.userNames},set:function(e){this.$emit("users-change","usersText",e)}},roleNamesText:{get:function(){return this.roleNames},set:function(e){this.$emit("roles-change","rolesText",e)}},departmentNamesText:{get:function(){return this.departmentNames},set:function(e){this.$emit("departments-change","departmentsText",e)}},comNames:function(){return this.groupNamesText||this.userNamesText||this.roleNamesText||this.departmentNamesText?"".concat(this.groupNamesText,",").concat(this.userNamesText,",").concat(this.roleNamesText,",").concat(this.departmentNamesText).split(",").filter((function(e){return!!e})).toString():""}},methods:{handleSubmit:function(){this.$refs.tabs.handleSubmit()}}}},"85c9":function(e,t,a){},"85e5":function(e,t,a){},8666:function(e,t,a){"use strict";var n=a("4ea4").default,r=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("d81d"),a("b0c0"),a("e9f5"),a("ab43"),a("d3b7");var i=r(a("32fd")),o=n(a("c330"));t.default={name:"GridSet",components:{TreeTransfer:o.default},props:{origin:{type:Array,default:function(){return[]}},all:{type:Array,default:function(){return[]}},oall:{type:Array,default:function(){return[]}},gantt:{type:Object,default:null}},data:function(){return{selected:[]}},computed:{allColumns:function(){return[{label:"标识",key:"",disabled:!0}].concat(i.ALL_TASK_COLUMNS.map((function(e){return{label:e.label,key:e.name,disabled:["text"].indexOf(e.name)>-1}})))}},created:function(){this.selected=this.origin},methods:{restoreDefault:function(){this.$emit("restoreDefault")},cancel:function(){this.$emit("dialogCancel")},submit:function(e){this.selected=e,this.$emit("dialogFormSubmitSuccess",{type:"setGanttColumns",data:this.selected})},add:function(e,t,a){this.selected=t},remove:function(e,t,a){this.selected=t}}}},8781:function(e,t,a){"use strict";a.r(t);var n=a("fbd1"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"88bb":function(e,t,a){"use strict";a("ee30")},"8a0a7":function(e,t,a){},"8b35":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("a9e3");var o=a("a024"),s=a("ed08");t.default={name:"SelectProcess",props:{value:{type:[Array,Number,String],default:""},multiple:{type:Boolean,default:!1}},data:function(){return{list:[],selectedValue:Array.isArray(this.value)?(0,s.deepClone)(this.value):this.value}},watch:{value:{handler:function(e){this.selectedValue=Array.isArray(e)?(0,s.deepClone)(e):e},immediate:!0}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:(0,o.GetProcessListBase)({FactoryId:localStorage.getItem("CurReferenceId")}).then((function(t){e.list=t.Data}));case 1:return t.a(2)}}),t)})))()}}}},"8ba7":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.tools=t.laneNodes=t.highNodes=t.commonNodes=void 0;t.tools=[{type:"drag",icon:"el-icon-rank",defaultIcon:"el-icon-rank",name:"拖拽"},{type:"connection",icon:"el-icon-share",defaultIcon:"el-icon-share",name:"连线"}],t.commonNodes=[{type:"start round mix",name:"开始",icon:"iconfont icon-play",defaultIcon:"iconfont icon-play",belongto:"commonNodes"},{type:"end round",name:"结束",icon:"iconfont icon-end",defaultIcon:"iconfont icon-end",belongto:"commonNodes"},{type:"node",name:"任务节点",icon:"iconfont icon-expand2",defaultIcon:"iconfont icon-expand2",belongto:"commonNodes"},{type:"fork",name:"会签开始",icon:"iconfont icon-flow-start",defaultIcon:"iconfont icon-flow-start",belongto:"commonNodes"},{type:"join",name:"会签结束",icon:"iconfont icon-flow-end",defaultIcon:"iconfont icon-flow-end",belongto:"commonNodes"}],t.highNodes=[{type:"child-flow",name:"子流程",icon:"ChildFlowIcon",defaultIcon:"ChildFlowIcon",belongto:"highNodes"}],t.laneNodes=[{type:"x-lane",name:"横向泳道",icon:"iconfont icon-lane-horizontal",defaultIcon:"iconfont icon-lane-horizontal",belongto:"laneNodes"},{type:"y-lane",name:"纵向泳道",icon:"iconfont icon-lane-vertical",defaultIcon:"iconfont icon-lane-vertical",belongto:"laneNodes"}]},"8bab":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"-16px",padding:"0 10px"}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"当前计划"}},[a("h2",{staticStyle:{"font-size":"1.8em",color:"#222"}},[e._v(e._s(e.plan.Name))])]),a("el-form-item",{attrs:{label:"选定目标计划"}},[e.editmode?a("el-select",{attrs:{placeholder:"请选择活动区域"},model:{value:e.form.target,callback:function(t){e.$set(e.form,"target",t)},expression:"form.target"}},[a("el-option",{attrs:{label:"未设置",value:""}}),"2"===e.plan.Status?a("el-option",{attrs:{label:"当前进度计划副本",value:0}}):e._e(),e._l(e.sortedList,(function(t){return["2"===t.Status&&t.Id!=e.plan.Id?a("el-option",{key:t.Id,attrs:{label:t.Id===e.plan.Id?"当前进度计划副本":t.Name,value:t.Id}}):e._e()]}))],2):a("span",[e._v(e._s("-"))])],1),a("el-form-item",{attrs:{label:"说明"}},[a("ol",{staticClass:"my-ol"},[a("li",[e._v(" 设置目标计划后，目标计划以 "),a("span",{staticClass:"square"}),e._v(" 出现在横道图中，前锋线也会根据计划与目标对比显示折线； ")]),a("li",[e._v(" 选择“当前进度计划副本”，确认后将在进度计划列表创建一个当前进度计划的副本，并标记为目标计划。 ")])])]),a("el-form-item",{attrs:{align:"right"}},[a("el-button",{on:{click:function(t){return e.$emit("dialogCancel")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确定")])],1)],1)],1)},r=[]},"8c02":function(e,t,a){"use strict";a.r(t);var n=a("0c62"),r=a("d50e");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"8d0e":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9f5"),a("a732"),a("d3b7");var r=n(a("c14f")),i=n(a("1da1")),o=a("7196");t.default={name:"SelectDepartmentUser",props:{value:{type:String,default:""},departmentId:{type:String,default:""},nullIsFetch:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1}},data:function(){return{list:[]}},watch:{departmentId:function(){this.getList()}},created:function(){this.getList()},methods:{handleChange:function(e){this.$emit("input",e),this.$emit("change",e)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.departmentId||e.nullIsFetch){t.n=1;break}return t.a(2);case 1:return t.n=2,(0,o.GetFactoryPeoplelist)({DepartmentId:e.departmentId}).then((function(t){e.list=t.Data,e.list.some((function(t){return t.Id===e.value}))||e.handleChange("")}));case 2:return t.a(2)}}),t)})))()}}}},9084:function(e,t,a){"use strict";a.r(t);var n=a("9439"),r=a("7e87");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("409f");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"92e7fed0",null);t["default"]=s.exports},9096:function(e,t,a){"use strict";a.r(t);var n=a("4d4a"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"90bd":function(e,t,a){"use strict";a("0f6c")},9140:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"0 16px 0 10px"}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px",rules:e.rules}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"类别",required:"",prop:"Type_Id"}},[e.editmode?a("el-popover",{attrs:{placement:"bottom",width:"240",height:"240",trigger:"manual"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[a("el-menu",{staticStyle:{"border-right":"0"},attrs:{"default-active":e.form.Type_Id},on:{select:e.handleSelectType}},[a("TypeTreeItem",{attrs:{data:e.typetree}})],1),a("el-input",{attrs:{slot:"reference",value:e.getPlanType(e.form.Type_Id,e.typetree).Label,type:"text"},slot:"reference"},[a("el-button",{attrs:{slot:"append",icon:"el-icon-arrow-down"},on:{click:function(t){e.visible=!e.visible}},slot:"append"})],1)],1):a("span",[e._v(" "+e._s(e.getPlanType(e.form.Type_Id,e.typetree).Label)+" ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"编号",required:"",prop:"Code"}},[e.editmode?a("el-input",{model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}}):a("span",[e._v(e._s(e.form.Code))])],1)],1)],1),a("el-form-item",{attrs:{label:"名称",required:"",prop:"Name"}},[e.editmode?a("el-input",{attrs:{placeholder:"计划名称，最多30字"},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}}):a("span",[e._v(e._s(e.form.Name))])],1),a("el-form-item",{attrs:{label:"计划开始",required:"",prop:"Plan_Start_Date"}},[e.editmode?a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:e.form.Plan_Start_Date,callback:function(t){e.$set(e.form,"Plan_Start_Date",t)},expression:"form.Plan_Start_Date"}}):a("span",[e._v(e._s(e.toDateStr(e.form.Plan_Start_Date)))])],1),a("el-form-item",{attrs:{label:"计划完成",required:"",prop:"Plan_End_Date"}},[e.editmode?a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:e.form.Plan_End_Date,callback:function(t){e.$set(e.form,"Plan_End_Date",t)},expression:"form.Plan_End_Date"}}):a("span",[e._v(e._s(e.toDateStr(e.form.Plan_End_Date)))]),a("span",[e._v("（计划工期："),a("span",{staticStyle:{color:"#298DFF"}},[e._v(e._s(e.form.Plan_Duration))]),e._v("自然天）")])],1),a("el-form-item",{attrs:{label:"人工资源显示",prop:"Resources"}},[e.editmode?a("el-radio-group",{model:{value:e.form.Resources,callback:function(t){e.$set(e.form,"Resources",t)},expression:"form.Resources"}},[a("el-radio",{attrs:{label:1}},[e._v("工日")]),a("el-radio",{attrs:{label:2}},[e._v("人数")])],1):a("span",[e._v(e._s(e.form.Resources?"1"===e.form.Resources.toString()?"工日":"2"===e.form.Resources.toString()?"人数":"":""))])],1),a("el-form-item",{attrs:{label:"计划权限",prop:"Plan_Oauth"}},[e.editmode?a("el-radio-group",{model:{value:e.form.Plan_Oauth,callback:function(t){e.$set(e.form,"Plan_Oauth",t)},expression:"form.Plan_Oauth"}},[a("el-radio",{attrs:{label:"0"}},[e._v("私密")]),a("el-radio",{attrs:{label:"1"}},[e._v("公开")])],1):a("span",[e._v(e._s(e.form.Plan_Oauth?"0"===e.form.Plan_Oauth.toString()?"私密":"1"===e.form.Plan_Oauth.toString()?"公开":"":""))])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所有者",required:"",prop:"Admin"}},[e.editmode?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择计划所有者",multiple:"",filterable:""},model:{value:e.form.Admin,callback:function(t){e.$set(e.form,"Admin",t)},expression:"form.Admin"}},[e._l(e.members,(function(e){return[a("el-option",{key:e.User_Id,attrs:{label:e.UserName,value:e.User_Id}})]}))],2):a("span",[e._v(" "+e._s(e.getUsers(e.form.Admin))+" ")])],1)],1)],1),a("el-form-item",{attrs:{label:"查看者",prop:"Observer"}},[e.editmode?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择计划查看者",multiple:"",filterable:""},model:{value:e.form.Observer,callback:function(t){e.$set(e.form,"Observer",t)},expression:"form.Observer"}},[e._l(e.members,(function(e){return[a("el-option",{key:e.User_Id,attrs:{label:e.UserName,value:e.User_Id}})]}))],2):a("span",[e._v(" "+e._s(e.getUsers(e.form.Observer))+" ")])],1),a("el-form-item",{attrs:{label:"说明"}},[e.editmode?a("el-input",{attrs:{type:"textarea",rows:"5"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}}):a("span",[e._v(" "+e._s(e.form.Remark)+" ")])],1),a("el-form-item",{attrs:{align:"right"}},[a("el-button",{on:{click:function(t){return e.$emit("dialogCancel")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.onSubmit}},[e._v("确定")])],1)],1)],1)},r=[]},9177:function(e,t,a){"use strict";a.r(t);var n=a("289b"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"926d0":function(e,t,a){"use strict";var n=a("dbce").default,r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530"));a("7db0"),a("14d9"),a("b0c0"),a("e9f5"),a("f665"),a("7d54"),a("a9e3"),a("d3b7"),a("25f0"),a("159b");var o=a("472f"),s=n(a("c1df"));t.default={name:"TaskDetail",props:{tabname:{type:String,default:"profile"},taskid:{type:String|Number,default:""},planid:{type:String|Number,default:""},plan:{type:Object,default:function(){return{}}},task:{type:Object,default:function(){return{}}},constraints:{type:Array,default:function(){return[]}},members:{type:Array,default:function(){return[]}},gantt:{type:Object,default:null},editMode:{type:Boolean,default:!0},canEditWBS:{type:Boolean},canEditTask:{type:Boolean},canUpdateProgress:{type:Boolean},extend:{type:Boolean,default:!1},extendfields:{type:Array,default:function(){return[]}}},data:function(){return{form:{},taskChanged:!1,tabName:"profile",tempProgress:0,history:[]}},computed:{preTasks:function(){var e,t,a=this,n=null!==(e=null===(t=this.gantt)||void 0===t?void 0:t.getLinks())&&void 0!==e?e:[],r=[];return n.forEach((function(e){var t,n;(null===(t=e.target)||void 0===t?void 0:t.toString())===(null===(n=a.taskid)||void 0===n?void 0:n.toString())&&r.push({task:a.gantt.getTask(e.source),link:e})})),r},pastTasks:function(){var e,t,a=this,n=null!==(e=null===(t=this.gantt)||void 0===t?void 0:t.getLinks())&&void 0!==e?e:[],r=[];return n.forEach((function(e){var t,n;(null===(t=e.source)||void 0===t?void 0:t.toString())===(null===(n=a.taskid)||void 0===n?void 0:n.toString())&&r.push({task:a.gantt.getTask(e.target),link:e})})),r},canEdit:function(){return!!this.canEditWBS}},watch:{task:function(e,t){var a=this;e.id!==t.id&&(this.taskChanged=!0,this.buildFormFromProp(),setTimeout((function(){a.taskChanged=!1}),100))},tabName:function(e){"history"===e&&this.loadTaskHistory()},"form.type":function(e,t){t&&t!==e&&!this.taskChanged&&this.formChange("type",e)}},created:function(){var e;this.tabName=null!==(e=this.tabname)&&void 0!==e?e:"profile",this.buildFormFromProp()},methods:{buildFormFromProp:function(){"history"===this.tabName&&this.loadTaskHistory(),this.form=(0,i.default)({},this.task),this.tempProgress=100*this.form.Actual_Progress,this.$forceUpdate()},closeMe:function(){this.$emit("drawerCancel")},formChange:function(e,t){"tempProgress"===e&&(e="Actual_Progress",t/=100),this.$emit("drawerContentUpdate",{type:"updateGanttTask",data:{gantt:this.gantt,task:this.task,field:e,value:t}}),this.gantt.render()},updateLagDays:function(e,t){(!Number(t)||Number(t)<0)&&(t=0),this.$emit("drawerContentUpdate",{type:"updateLagDays",data:{gantt:this.gantt,link:e,value:t}})},getLinkType:function(e){var t;return null!==(t=o.LINK_TYPES.find((function(t){return t.value==e})))&&void 0!==t?t:{}},moment:function(e){return s(e)},toDateStr:function(e){return e?s(e).startOf("date").format("YYYY-MM-DD"):"-"},loadTaskHistory:function(){var e=this;(0,o.GetTaskProgressList)({Plan_Id:this.planid,Task_Id:this.task.id}).then((function(t){t.IsSucceed&&(e.history=t.Data)}))},handleClick:function(e,t){this.tabName=e.name},getUser:function(e){var t=this.members.find((function(t){return t.User_Id===e}));return null!==t&&void 0!==t?t:{}},getConstraintType:function(e){var t;return null!==(t=this.constraints.find((function(t){return t.value===e})))&&void 0!==t?t:{}}}}},"929d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("transition-group",{class:["el-upload-list","el-upload-list--"+e.listType,{"is-disabled":e.disabled}],attrs:{tag:"ul",name:"el-list"}},e._l(e.files,(function(t){return a("li",{key:t.uid,class:["el-upload-list__item","is-"+t.status,e.focusing?"focusing":""],attrs:{tabindex:"0"},on:{keydown:function(a){if(!a.type.indexOf("key")&&e._k(a.keyCode,"delete",[8,46],a.key,["Backspace","Delete","Del"]))return null;!e.disabled&&e.$emit("remove",t)},focus:function(t){e.focusing=!0},blur:function(t){e.focusing=!1},click:function(t){e.focusing=!1}}},[e._t("default",["uploading"!==t.status&&["picture-card","picture"].indexOf(e.listType)>-1?a("img",{staticClass:"el-upload-list__item-thumbnail",attrs:{src:t.url,alt:""}}):e._e(),a("a",{staticClass:"el-upload-list__item-name",on:{click:function(a){return e.handleClick(t)}}},[a("i",{staticClass:"el-icon-document"}),e._v(e._s(t.name)+" ")]),a("label",{staticClass:"el-upload-list__item-status-label"},[a("i",{class:{"el-icon-upload-success":!0,"el-icon-circle-check":"text"===e.listType,"el-icon-check":["picture-card","picture"].indexOf(e.listType)>-1}})]),e.disabled?e._e():a("i",{staticClass:"el-icon-close",on:{click:function(a){return e.$emit("remove",t)}}}),e.disabled?e._e():a("i",{staticClass:"el-icon-close-tip"},[e._v(e._s(e.t("el.upload.deleteTip")))]),e._v(" "),"uploading"===t.status?a("el-progress",{attrs:{type:"picture-card"===e.listType?"circle":"line","stroke-width":"picture-card"===e.listType?6:2,percentage:e.parsePercentage(t.percentage)}}):e._e(),"picture-card"===e.listType?a("span",{staticClass:"el-upload-list__item-actions"},[e.handlePreview&&"picture-card"===e.listType?a("span",{staticClass:"el-upload-list__item-preview",on:{click:function(a){return e.handlePreview(t)}}},[a("i",{staticClass:"el-icon-zoom-in"})]):e._e(),e.disabled?e._e():a("span",{staticClass:"el-upload-list__item-delete",on:{click:function(a){return e.$emit("remove",t)}}},[a("i",{staticClass:"el-icon-delete"})])]):e._e()],{file:t})],2)})),0)},r=[]},9439:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"update-set"},[a("div",{staticClass:"title"},[e._v(e._s(e.plan.Name))]),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"140px"}},[a("el-form-item",{attrs:{label:"填报截止时间"}},[e.editmode?a("div",{staticClass:"timer-group"},[a("el-date-picker",{staticStyle:{width:"160px"},attrs:{type:"date",format:"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.form.date,callback:function(t){e.$set(e.form,"date",t)},expression:"form.date"}}),a("el-time-select",{staticStyle:{width:"120px"},attrs:{"picker-options":{start:"08:30",step:"00:15",end:"18:30"},placeholder:"选择时间"},model:{value:e.form.time,callback:function(t){e.$set(e.form,"time",t)},expression:"form.time"}})],1):a("span",[e._v(" - ")])]),a("el-form-item",{attrs:{label:"数据日期"}},[e.editmode?a("el-date-picker",{staticStyle:{width:"284px"},attrs:{type:"date",format:"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.form.data_date,callback:function(t){e.$set(e.form,"data_date",t)},expression:"form.data_date"}}):a("span",[e._v(e._s(e.moment(e.plan.Cur_Data_Date).format("YYYY-MM-DD")))])],1),a("el-form-item",{attrs:{align:"right"}},[a("el-button",{on:{click:function(t){return e.$emit("dialogCancel")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确定")])],1)],1)],1)},r=[]},"943a":function(e,t,a){"use strict";a.r(t);var n=a("4bac"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"951d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"el-upload-dragger",class:{"is-dragover":e.dragover},on:{drop:function(t){return t.preventDefault(),e.onDrop(t)},dragover:function(t){return t.preventDefault(),e.onDragover(t)},dragleave:function(t){t.preventDefault(),e.dragover=!1}}},[e._t("default")],2)},r=[]},"97c9":function(e,t,a){"use strict";a.r(t);var n=a("d2db"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},9812:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"bim-gantt",attrs:{"element-loading-background":"rgba(255, 255, 255, 0.4)","element-loading-text":e.loadingStr}},[a("header",[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:14}},[a("div",{staticClass:"proj-title"},[a("div",{staticClass:"flag"},[a("i",{staticClass:"iconfont icon-gantt"})]),a("h3",[e._v(e._s(e.plan.Name))]),a("el-link",{staticClass:"set-icon",attrs:{underline:!1,type:"primary",icon:"el-icon-setting"},on:{click:e.onOpenPlanSet}})],1)]),a("el-col",{staticStyle:{"text-align":"right","padding-right":"20px"},attrs:{span:10}},[e.plan.Modify_Date?a("span",{staticStyle:{display:"inline-block","margin-right":"20px",color:"#AAA","font-size":".9em"}},[a("i",{staticClass:"iconfont icon-check-circle"}),e._v(" 最近保存:"+e._s(e.moment(e.plan.Modify_Date).format("HH:mm")))]):e._e(),e.editMode&&e.canHandlePlanStatus()&&!e.plan.Is_Main_Plan?a("el-button",{attrs:{type:"warning"},on:{click:e.onPlanImport}},[e._v("导入")]):e._e(),e.role.check(e.ACCESSES.WBS)?a("el-dropdown",{staticStyle:{"margin-right":"12px","margin-left":"12px"},attrs:{trigger:"click"},on:{command:e.onExportChange}},[a("el-button",{attrs:{type:"success"}},[e._v("导出")]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"PNG"}},[e._v("导出为PNG")]),a("el-dropdown-item",{attrs:{command:"PDF"}},[e._v("导出为PDF")]),a("el-dropdown-item",{attrs:{command:"Excel"}},[e._v("导出为Excel")]),a("el-dropdown-item",{attrs:{command:"ICal"}},[e._v("导出为ICal")]),a("el-dropdown-item",{attrs:{command:"JSON"}},[e._v("导出为JSON")]),a("el-dropdown-item",{attrs:{command:"MSProject"}},[e._v("导出为MSProject")]),a("el-dropdown-item",{attrs:{command:"PrimaveraP6"}},[e._v("导出为PrimaveraP6")])],1)],1):e._e(),e.editMode?a("el-button",{ref:"btn_save",attrs:{type:"primary"},on:{click:function(t){return e.savePlan(0)}}},[e._v("保存")]):e._e(),e.editMode&&e.canHandlePlanStatus()&&!0===e.needApprove?a("el-button",{staticStyle:{"padding-left":"32px"},attrs:{type:"primary"},on:{click:function(t){return e.savePlan(1)}}},[a("i",{staticClass:"iconfont icon-stamp",staticStyle:{"margin-left":"-20px","margin-top":"-3px"}}),e._v(" 提交"+e._s(e.plan.Is_Main_Plan?"审核":"发布"))]):e._e(),e.editMode&&e.canHandlePlanStatus()&&!1===e.needApprove?a("el-button",{staticStyle:{"padding-left":"32px"},attrs:{type:"primary"},on:{click:function(t){return e.savePlan(2)}}},[a("i",{staticClass:"iconfont icon-stamp",staticStyle:{"margin-left":"-20px","margin-top":"-3px"}}),e._v(" 发布")]):e._e(),e.role.check(e.ACCESSES.WBS)&&"2"===e.plan.Status&&e.plan.Is_Main_Plan?a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.savePlan(2,!0)}}},[e._v(" 提交至运营")]):e._e()],1)],1)],1),a("div",{staticClass:"toolbar"},[a("div",{staticClass:"flex-toolbar"},[a("div",{staticClass:"toolbar-group"},[e.editMode&&["1","2"].indexOf(e.plan.Status)<0?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:e.onAddTask}},[e._v("新增")]):e._e(),e.editMode&&["1","2"].indexOf(e.plan.Status)<0?a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-minus"},on:{click:e.onDeleteTask}},[e._v("删除")]):e._e(),e.editMode&&e.wbsMode?a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-set-up"},on:{click:function(t){return e.onWbsModeChange("task")}}},[e._v("作业编制")]):e._e(),!e.editMode||e.wbsMode||!e.role.check(e.ACCESSES.WBS)&&e.plan.Id?e._e():a("el-button",{attrs:{type:"warning",size:"mini",icon:"iconfont icon-org-vertical"},on:{click:function(t){return e.onWbsModeChange("project")}}},[e._v("WBS编制")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"iconfont icon-edit-file"},on:{click:e.onDetailOpen}},[e._v("详情")])],1),a("div",{staticClass:"toolbar-group"},[a("div",{staticClass:"tool-item"},[a("span",[e._v("大纲级别")]),a("el-slider",{staticStyle:{width:"150px"},attrs:{size:"mini",step:1,max:e.maxDeep,min:0,"show-tooltip":!1},on:{change:e.onLevelChange},model:{value:e.currentLvl,callback:function(t){e.currentLvl=t},expression:"currentLvl"}})],1),a("div",{staticClass:"tool-item"},[[a("span",[e._v("前锋线")]),a("el-switch",{on:{change:e.onToggleFowardLine},model:{value:e.showFowardLine,callback:function(t){e.showFowardLine=t},expression:"showFowardLine"}})]],2),a("div",{staticClass:"tool-item"},[a("span",[e._v("聚光灯")]),a("el-switch",{on:{change:e.onToggleSpotLight},model:{value:e.showSpotLight,callback:function(t){e.showSpotLight=t},expression:"showSpotLight"}})],1)]),a("div",{staticClass:"toolbar-group"},[a("div",{staticClass:"tool-item"},[a("el-input",{staticStyle:{"border-width":"0px !important",width:"160px"},attrs:{placeholder:"检索作业/责任人",size:"mini","suffix-icon":"el-icon-search"},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1),a("div",{staticClass:"tool-item"},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"筛选作业",placement:"top-start"}},[a("el-badge",{class:{hasfilter:e.gantFilters.checkers.length>0},staticStyle:{"margin-left":"6px"},attrs:{"is-dot":"",hidden:e.gantFilters.checkers.length<=0}},[a("el-link",{attrs:{underline:!1,icon:"iconfont icon-filter-filled"},on:{click:e.onFilterTasksSet}})],1)],1)],1)]),a("div",{staticClass:"toolbar-group"},[e.role.check(e.ACCESSES.WBS)||"zhh"==e.$store.state.user.account?[a("div",{staticClass:"tool-item"},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"人力资源分析",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"iconfont icon-chart-bar"},on:{click:e.onAnalyzerOpen}})],1)],1),a("div",{staticClass:"tool-item"},[e.editMode||"zhh"==e.$store.state.user.account?a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"进度计算",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"iconfont icon-calculate"},on:{click:e.onScheduleOpen}})],1):e._e()],1)]:e._e(),a("div",{staticClass:"tool-item"},[e.plan.Id?a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"更新历史",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"el-icon-time"},on:{click:e.onHistoryOpen}})],1):e._e()],1),e.role.check(e.ACCESSES.WBS)?[a("div",{staticClass:"tool-item"},[a("el-dropdown",{attrs:{trigger:"click"},on:{command:e.onCommandChange}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"更多操作",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"iconfont icon-more-vertical"}})],1),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"calendar"}},[e._v("日历设置")]),e.plan.Id?a("el-dropdown-item",{attrs:{command:"target"}},[e._v("目标计划")]):e._e(),a("el-dropdown-item",{attrs:{command:"updateset"}},[e._v("填报任务设置")])],1)],1)],1)]:e._e()],2),a("div",{staticClass:"toolbar-group"},[a("div",{staticClass:"tool-item"},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"缩小时间线",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"el-icon-zoom-out"},on:{click:function(t){return e.onZoom(-1)}}})],1)],1),a("div",{staticClass:"tool-item"},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"放大时间线",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"el-icon-zoom-in"},on:{click:function(t){return e.onZoom(1)}}})],1)],1),a("div",{staticClass:"tool-item"},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"显示全部",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"iconfont icon-compress2"},on:{click:e.onFixAll}})],1)],1),a("div",{staticClass:"tool-item"},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"切换全屏",placement:"top-start"}},[a("el-link",{attrs:{underline:!1,icon:"iconfont icon-expand"},on:{click:e.onFullScreen}})],1)],1),a("div",{staticClass:"tool-item"},[a("el-popover",{attrs:{placement:"bottom",title:"图例",width:"200",trigger:"click"}},[a("ul",{staticClass:"gantt-leggend"},[a("li",[a("span",{class:["project","rect"]}),a("label",[e._v("WBS")])]),a("li",[a("span",{class:["plan","rect"]}),a("label",[e._v("计划作业")])]),a("li",[a("span",{class:["key","rect"]}),a("label",[e._v("关键作业")])]),a("li",[a("span",{class:["diamond"]},[a("i")]),a("label",[e._v("里程碑")])]),a("li",[a("span",{class:["actual","project","rect"]}),a("label",[e._v("实际WBS")])]),a("li",[a("span",{class:["actual","rect"]}),a("label",[e._v("实际作业")])]),a("li",[a("span",{class:["target","rect"]}),a("label",[e._v("目标作业")])]),a("li",[a("span",{class:["target","diamond"]},[a("i")]),a("label",[e._v("目标里程碑")])])]),a("el-tooltip",{staticClass:"item",attrs:{slot:"reference",effect:"light",content:"图例",placement:"top-start"},slot:"reference"},[a("el-link",{attrs:{underline:!1,icon:"el-icon-warning-outline"},on:{click:e.toggleInfo}})],1)],1)],1)])])]),a("div",{staticClass:"gantt-container"},[a("div",{ref:"gantt",class:{hasbaseline:e.showBaseLine,hasfowardline:e.showFowardLine,"has-bottom-drawer":e.drawerShow&&"btt"==e.drawerCfgs.direction},attrs:{id:"gantt-chart"}})]),a("el-dialog",{attrs:{title:e.dialogCfgs.title,visible:e.dialogShow,width:e.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(t){e.dialogShow=t}}},[a("keep-alive",[e.dialogShow?a(e.dialogCfgs.component,e._b({tag:"component",attrs:{name:e.dialogCfgs.title},on:{dialogCancel:e.dialogCancel,dialogFormSubmitSuccess:e.dialogFormSubmitSuccess,restoreDefault:e.restoreDefault}},"component",e.dialogCfgs.props,!1)):e._e()],1)],1),a("el-drawer",{ref:"drawer",class:{gantdrawer:!0,ismodal:e.drawerCfgs.modal,history:"history"===e.drawerProps.drawclass&&e.drawerShow,taskdetail:("taskdetail"===e.drawerProps.drawclass||"analyzer-detail"===e.drawerProps.drawclass)&&e.drawerShow},style:{height:"btt"!=e.drawerCfgs.direction&&"ttb"!=e.drawerCfgs.direction||!e.drawerCfgs.size?"100%":e.drawerCfgs.size,width:"ltr"!=e.drawerCfgs.direction&&"rtl"!=e.drawerCfgs.direction||!e.drawerCfgs.size?"100%":e.drawerCfgs.size},attrs:{title:e.drawerCfgs.title,size:e.drawerCfgs.size,visible:e.drawerShow,modal:e.drawerCfgs.modal,"wrapper-closable":e.drawerCfgs.wrapperClosable,"modal-append-to-body":e.drawerCfgs.modalAppendToBody,direction:e.drawerCfgs.direction,"with-header":e.drawerCfgs.withHeader,"destroy-on-close":""},on:{"update:visible":function(t){e.drawerShow=t},closed:function(t){e.canOpenNewDrawer=!0,e.drawerShow=!1},opened:function(t){e.canOpenNewDrawer=!1}}},[a("keep-alive",[e.drawerShow?a(e.drawerCfgs.component,e._b({ref:e.drawerCfgs.component,tag:"component",on:{drawerCancel:e.drawerCancel,drawerContentUpdate:e.drawerContentUpdate}},"component",e.drawerCfgs.props,!1)):e._e()],1)],1)],1)},r=[]},9925:function(e,t,a){"use strict";a.r(t);var n=a("0cf5"),r=a("1b9a");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"9ad9":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:"",disabled:e.disabled},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)},r=[]},"9b4c1":function(e,t,a){},"9d35":function(e,t,a){"use strict";a("335a1")},"9e22":function(e,t,a){"use strict";a.r(t);var n=a("3f12"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"9e80":function(e,t,a){"use strict";a.r(t);var n=a("951d"),r=a("171e");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"9ed6":function(e,t,a){"use strict";a.r(t);var n=a("e3b0"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"9f55":function(e,t,a){"use strict";a.r(t);var n=a("5b55"),r=a("943a");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},a0a3:function(e,t,a){},a14b0:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.data,(function(t){return[t.Children&&t.Children.length>0?[a("el-submenu",{key:"c-"+t.Id,attrs:{index:t.Id}},[a("template",{slot:"title"},[a("span",[e._v(e._s(t.Label))])]),a("TypeTreeItem",{key:"c-"+t.Id,attrs:{data:t.Children}})],2)]:[a("el-menu-item",{key:"c-"+t.Id,attrs:{index:t.Id}},[a("span",[e._v(e._s(t.Label))])])]]}))],2)},r=[]},a18c4:function(e,t,a){"use strict";a("0a98")},a210:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.tablePageSize=void 0;t.tablePageSize=[20,50,100,200,500,1e3,2e3,5e3]},a2db:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("c740"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("7d54"),a("e9c4"),a("b64b"),a("d3b7"),a("18a5"),a("159b");var r=n(a("5530")),i=n(a("b76a")),o=n(a("f67b")),s=a("e193"),l=a("8ba7"),c=a("def8"),d=a("b6b8"),u=n(a("e100")),f=a("2f62");t.default={name:"Vfd",components:{Draggable:i.default,FlowArea:o.default,FlowAttr:u.default},props:["schemeContent","isEdit","formTemplate","isShowContent"],data:function(){return{isDrag:!1,loadFlowArea:!1,isShowPopover:!1,tag:{checked0:!0,checked1:!0,checked2:!0,checked3:!0},browserType:3,plumb:"",field:{tools:l.tools,commonNodes:l.commonNodes,highNodes:l.highNodes,laneNodes:l.laneNodes},flowData:{nodes:[],lines:[],attr:{id:""},config:{showGrid:!0,showGridText:"隐藏网格",showGridIcon:"el-icon-view"},status:c.flowConfig.flowStatus.CREATE,remarks:[]},currentTool:{type:"drag",icon:"drag",name:"拖拽"},currentSelectGroup:[],activeShortcut:!0,linkContextMenuData:c.flowConfig.contextMenu.sl,flowPicture:{url:"",modalVisible:!1,closable:!1,maskClosable:!1}}},mounted:function(){var e=this;e.dealCompatibility(),this.schemeContent?(this.flowData=this.groupSchemeContent(),this.flowData.status=c.flowConfig.flowStatus.MODIFY,this.initJsPlumb(),this.loadFlow()):(this.initJsPlumb(),this.loadFlow()),e.listenShortcut()},computed:(0,r.default)({},(0,f.mapGetters)({currentSelect:"currentSelect"})),watch:{currentSelect:{deep:!0,handler:function(){var e=this,t="sl"===this.currentSelect.type?this.flowData.lines:this.flowData.nodes,a=t.findIndex((function(t){return t.id===e.currentSelect.id}));a>=0&&this.$set(t,a,this.currentSelect)}},schemeContent:function(){this.schemeContent&&this.isEdit&&(this.flowData=this.groupSchemeContent(),this.flowData.status=c.flowConfig.flowStatus.MODIFY,this.plumb&&this.plumb.deleteEveryConnection(),this.initJsPlumb(),this.loadFlow())}},methods:(0,r.default)((0,r.default)({},(0,f.mapActions)({saveCurrentSelect:"saveCurrentSelect"})),{},{groupSchemeContent:function(){var e=Object.assign({},JSON.parse(this.schemeContent));if(!e.attr||!e.attr.id){var t=e.lines,a=e.nodes;a.length>0&&a.forEach((function(e){e.setInfo=e.setInfo||{NodeRejectType:0,NodeConfluenceType:"",NodeDesignate:"",ThirdPartyUrl:"",NodeDesignateData:{users:[],roles:[],departments:[],usersText:"",rolesText:"",departmentsText:"",Texts:""}}})),t.length>0&&t.forEach((function(e){e.label=e.label||e.name,e.cls={linkType:"Flowchart",linkColor:"#2a2929",linkThickness:2}})),e.attr={id:e.id||""},e.config={showGrid:!0,showGridText:"隐藏网格",showGridIcon:"el-icon-view"}}return e},handleMoveEnd:function(){this.isDrag=!1},handleMoveStart:function(e){e.oldIndex;this.isDrag=!0},handleMove:function(){return!0},getBrowserType:function(){var e=navigator.userAgent,t=e.indexOf("Opera")>-1;return t?1:e.indexOf("Firefox")>-1?2:e.indexOf("Chrome")>-1?3:e.indexOf("Safari")>-1?4:e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1&&!t?(alert("IE浏览器支持性较差，推荐使用Firefox或Chrome"),5):e.indexOf("Trident")>-1?(alert("Edge浏览器支持性较差，推荐使用Firefox或Chrome"),6):void 0},dealCompatibility:function(){var e=this;e.browserType=e.getBrowserType(),2===e.browserType&&(c.flowConfig.shortcut.scaleContainer={code:16,codeName:"SHIFT(chrome下为ALT)",shortcutName:"画布缩放"})},initJsPlumb:function(){var e=this;e.plumb=s.jsPlumb.getInstance(c.flowConfig.jsPlumbInsConfig),this.loadFlowArea=!0,this.plumb.ready((function(){e.plumb.bind("beforeDrop",(function(t){var a=t.sourceId,n=t.targetId;if(a===n)return!1;var r=e.flowData.lines.filter((function(e){return e.from===a&&e.to===n}));return!(r.length>0)||(e.$message.error("同方向的两节点连线只能有一条！"),!1)}));var t=0;e.plumb.bind("connection",(function(a){var n,r=a.connection.canvas,i={},o="";if(e.flowData.status===c.flowConfig.flowStatus.CREATE||e.flowData.status===c.flowConfig.flowStatus.MODIFY)o="link-"+d.ZFSN.getId(),n="";else if(e.flowData.status===c.flowConfig.flowStatus.LOADING){var s=e.flowData.lines[t];o=s.id,n=s.label,t++}r.id=o,i.type="sl",i.id=o,i.from=a.sourceId,i.to=a.targetId,i.label=n,i.cls={linkType:c.flowConfig.jsPlumbInsConfig.Connector[0],linkColor:c.flowConfig.jsPlumbInsConfig.PaintStyle.stroke,linkThickness:c.flowConfig.jsPlumbInsConfig.PaintStyle.strokeWidth},document.getElementById(o).addEventListener("contextmenu",(function(t){e.showLinkContextMenu(t);var a=e.flowData.lines.filter((function(e){return e.id===o}))[0];e.saveCurrentSelect(a)})),document.getElementById(o).addEventListener("click",(function(t){var a=window.event||t;a.stopPropagation();var n=e.flowData.lines.filter((function(e){return e.id===o}))[0],r=[{FieldName:"",Operation:"",Value:""}];n.Compares=n.Compares||r,e.saveCurrentSelect(n)})),e.flowData.status!==c.flowConfig.flowStatus.LOADING&&e.flowData.lines.push(i)})),e.plumb.importDefaults({ConnectionsDetachable:c.flowConfig.jsPlumbConfig.conn.isDetachable})}))},listenShortcut:function(){var e=this;document.onkeydown=function(t){var a=window.event||t;if(e.activeShortcut){var n=a.keyCode;switch(n){case c.flowConfig.shortcut.multiple.code:e.$refs.flowArea.rectangleMultiple.flag=!0;break;case c.flowConfig.shortcut.dragContainer.code:e.$refs.flowArea.container.dragFlag=!0;break;case c.flowConfig.shortcut.scaleContainer.code:e.$refs.flowArea.container.scaleFlag=!0;break;case c.flowConfig.shortcut.dragTool.code:e.selectTool("drag");break;case c.flowConfig.shortcut.connTool.code:e.selectTool("connection");break;case c.flowConfig.shortcut.zoomInTool.code:e.selectTool("zoom-in");break;case c.flowConfig.shortcut.zoomOutTool.code:e.selectTool("zoom-out");break;case 37:e.moveNode("left");break;case 38:e.moveNode("up");break;case 39:e.moveNode("right");break;case 40:e.moveNode("down");break;default:break}if(a.ctrlKey&&a.altKey)switch(n){case c.flowConfig.shortcut.settingModal.code:e.setting();break;case c.flowConfig.shortcut.testModal.code:e.openTest();break}}},document.onkeyup=function(t){var a=window.event||t,n=a.keyCode;n===c.flowConfig.shortcut.dragContainer.code?e.$refs.flowArea.container.dragFlag=!1:n===c.flowConfig.shortcut.scaleContainer.code?(a.preventDefault(),e.$refs.flowArea.container.scaleFlag=!1):n===c.flowConfig.shortcut.multiple.code&&(e.$refs.flowArea.rectangleMultiple.flag=!1)}},listenPage:function(){window.onbeforeunload=function(e){return e=e||window.event,e&&(e.returnValue="关闭提示"),"关闭提示"}},initFlow:function(){var e=this;e.flowData.status===c.flowConfig.flowStatus.CREATE&&(e.flowData.attr.id="flow-"+d.ZFSN.getId())},loadFlow:function(){var e=this;e.flowData.status=c.flowConfig.flowStatus.LOADING,this.plumb.ready((function(){var t=Object.assign([],e.flowData.lines);e.$nextTick((function(){t.forEach((function(t){var a=e.plumb.connect({source:t.from,target:t.to,anchor:c.flowConfig.jsPlumbConfig.anchor.default,connector:[t.cls.linkType,{gap:5,cornerRadius:8,alwaysRespectStubs:!0}],paintStyle:{stroke:t.cls.linkColor,strokeWidth:t.cls.linkThickness}});""!==t.label&&a.setLabel({label:t.label,cssClass:"linkLabel"})}));var a={};e.saveCurrentSelect(a),e.currentSelectGroup=[],e.flowData.status=c.flowConfig.flowStatus.MODIFY}))}))},findNodeConfig:function(e,t,a){var n=null;switch(e){case"commonNodes":n=l.commonNodes.filter((function(e){return e.type===t}));break;case"highNodes":n=l.highNodes.filter((function(e){return e.type===t}));break;case"laneNodes":n=l.laneNodes.filter((function(e){return e.type===t}));break}n&&n.length>=0&&(n=n[0]),a(n)},selectTool:function(e){if(this.currentTool.type!==e){var t=l.tools.filter((function(t){return t.type===e}));switch(t&&t.length>=0&&(this.currentTool=t[0]),e){case"drag":this.changeToDrag();break;case"connection":this.changeToConnection();break;case"zoom-in":this.changeToZoomIn();break;case"zoom-out":this.changeToZoomOut();break}}},changeToDrag:function(){var e=this;this.$nextTick((function(){e.flowData.nodes.forEach((function(t){var a=e.plumb.toggleDraggable(t.id);a||e.plumb.toggleDraggable(t.id),"x-lane"!==t.type&&"y-lane"!==t.type&&(e.plumb.unmakeSource(t.id),e.plumb.unmakeTarget(t.id))}))}))},changeToConnection:function(){var e=this;e.flowData.nodes.forEach((function(t){var a=e.plumb.toggleDraggable(t.id);a&&e.plumb.toggleDraggable(t.id),"x-lane"!==t.type&&"y-lane"!==t.type&&(e.plumb.makeSource(t.id,c.flowConfig.jsPlumbConfig.makeSourceConfig),e.plumb.makeTarget(t.id,c.flowConfig.jsPlumbConfig.makeTargetConfig))}));var t={};e.saveCurrentSelect(t),e.currentSelectGroup=[]},changeToZoomIn:function(){},changeToZoomOut:function(){},checkFlow:function(){var e=this,t=e.flowData.nodes;return!(t.length<=0)||(this.$message.error("流程图中无任何节点！"),!1)},saveFlow:function(){var e=this,t=Object.assign({},e.flowData);if(e.checkFlow()){t.status=c.flowConfig.flowStatus.SAVE;var a=JSON.stringify(t);return this.$message.success("保存流程成功！请查看控制台。"),a}},downLoadFlowPicture:function(){var e=this,t=document.createElement("a"),a="alink-"+d.ZFSN.getId();t.id=a,t.ref=a,t.href=e.flowPicture.url,t.download="流程设计图_"+e.flowData.attr.id+".png",t.click()},cancelDownLoadFlowPicture:function(){this.flowPicture.url="",this.flowPicture.modalVisible=!1},computeCanvasSize:function(){var e=this,t=Object.assign([],e.flowData.nodes),a=t[0].left,n=t[0].top,r=t[0].left+t[0].width,i=t[0].top+t[0].height;t.forEach((function(e){a=Math.min(a,e.left),n=Math.min(n,e.top),r=Math.max(r,e.left+e.width),i=Math.max(i,e.top+e.height)}));var o=r-a,s=i-n;return{width:o,height:s,minX:a,minY:n,maxX:r,maxY:i}},clear:function(){var e=this;e.flowData.nodes.forEach((function(t){e.plumb.remove(t.id)}));var t={};e.saveCurrentSelect(t),e.currentSelectGroup=[],e.flowData.nodes=[],e.flowData.lines=[],e.flowData.remarks=[],this.isShowPopover=!1},toggleShowGrid:function(){var e=this.flowData.config.showGrid;e?(this.flowData.config.showGrid=!1,this.flowData.config.showGridText="显示网格",this.flowData.config.showGridIcon="el-icon-view"):(this.flowData.config.showGrid=!0,this.flowData.config.showGridText="隐藏网格",this.flowData.config.showGridIcon="el-icon-view")},setting:function(){this.$refs.settingModal.open()},shortcutHelper:function(){this.$refs.shortcutModal.open(),this.isShowPopover=!1},usingDoc:function(){window.open(this.info.gitee),this.isShowPopover=!1},exit:function(){alert("退出流程设计器...")},showLinkContextMenu:function(e){var t=window.event||e;t.preventDefault(),t.stopPropagation();var a=t.clientX,n=t.clientY;this.linkContextMenuData.axis={x:a,y:n}},deleteLink:function(){var e=this,t=e.currentSelect.from,a=e.currentSelect.to;e.plumb.deleteConnection(e.plumb.getConnections({source:t,target:a})[0]);var n=e.flowData.lines;n.splice(n.findIndex((function(e){return e.from===t&&e.to===a})),1),e.flowData.lines=Object.assign([],n);var r={};e.saveCurrentSelect(r)},loseShortcut:function(){this.activeShortcut=!1},getShortcut:function(){this.activeShortcut=!0},openTest:function(){var e=this,t=Object.assign({},e.flowData);e.$refs.testModal.flowData=t,e.$refs.testModal.testVisible=!0},moveNode:function(e){var t=this,a=c.flowConfig.defaultStyle.movePx,n=!0;switch(e){case"left":a=-a;break;case"up":a=-a,n=!1;break;case"right":break;case"down":n=!1}if(t.currentSelectGroup.length>0)t.currentSelectGroup.forEach((function(e){n?e.left+=a:e.top+=a})),t.plumb.repaintEverything();else if(t.currentSelect.id){var i=(0,r.default)({},t.currentSelect);n?i.left+=a:i.top+=a,t.saveCurrentSelect(i),t.plumb.repaintEverything()}}})}},a46f:function(e,t,a){},a8038:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-input",{attrs:{readonly:!0},nativeOn:{click:function(t){e.dialogVisible=!0}},model:{value:e.comNames,callback:function(t){e.comNames=t},expression:"comNames"}}),a("el-dialog",{staticClass:"cs-dialog",attrs:{visible:e.dialogVisible,"destroy-on-close":!0,title:"审核者",width:"60%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[e.dialogVisible?a("select-tab-com",{ref:"tabs",attrs:{show:e.dialogVisible,"user-names":e.userNamesText,"group-names":e.groupNamesText,"role-names":e.roleNamesText,"department-names":e.departmentNamesText,users:e.selectUsers,group:e.selectGroup,roles:e.selectRoles,departments:e.selectDepartments},on:{"update:show":function(t){e.dialogVisible=t},"update:userNames":function(t){e.userNamesText=t},"update:user-names":function(t){e.userNamesText=t},"update:groupNames":function(t){e.groupNamesText=t},"update:group-names":function(t){e.groupNamesText=t},"update:roleNames":function(t){e.roleNamesText=t},"update:role-names":function(t){e.roleNamesText=t},"update:departmentNames":function(t){e.departmentNamesText=t},"update:department-names":function(t){e.departmentNamesText=t},"update:users":function(t){e.selectUsers=t},"update:group":function(t){e.selectGroup=t},"update:roles":function(t){e.selectRoles=t},"update:departments":function(t){e.selectDepartments=t}}}):e._e(),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)},r=[]},a807:function(e,t,a){"use strict";a.r(t);var n=a("142f5"),r=a("3c22");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("a18c4");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"46892db2",null);t["default"]=s.exports},a8a1:function(e,t,a){"use strict";a.r(t);var n=a("2a91"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},aca9:function(e,t,a){"use strict";a.r(t);var n=a("929d"),r=a("0e6e");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("06dc");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},af7c:function(e,t,a){},b13e:function(e,t,a){"use strict";a("baea")},b163:function(e,t,a){"use strict";a.r(t);var n=a("fb34");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);var i,o,s=a("2877"),l=Object(s["a"])(n["default"],i,o,!1,null,null,null);t["default"]=l.exports},b17b:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseJsonRange=t.default=t.css2json=t.FormatedCellText=void 0,a("4de4"),a("a15b"),a("d81d"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("498a"),a("159b");var n=a("6f23");function r(e){var t={};return e.forEach((function(e){var a=e.indexOf(":"),n=e.substring(0,a).trim(),r=e.substring(a+1).trim();t[n]=r})),t}var i=t.css2json=function(e){var t,a;while(-1!==(t=e.indexOf("/*"))&&-1!==(a=e.indexOf("*/")))e=e.substring(0,t)+e.substring(a+2);var n={};while(e.length>0){var i=e.split(";").map((function(e){return e.trim()})).filter((function(e){return e.length>0}));return i=r(i),i}return n},o=t.FormatedCellText={props:{isFormatter:{type:Boolean,default:!1},formatter:{type:String,default:""},type:{type:String,default:"字符型"},text:{type:String|Boolean|Number,default:""},cusStyle:{type:void 0|String,default:""},range:{type:void 0|String,default:""},showTooltip:{type:Boolean,default:!1},toolTipText:{type:String|Boolean|Number,default:""}},render:function(e){var t,a,r,o="",l={},c=s(this.range);if(this.cusStyle)try{l=i(this.cusStyle)}catch(y){}switch(this.type){case"boolean":r=e("el-tag",{attrs:{effect:"dark",size:"mini",type:this.text?"success":"danger"},style:{borderRadius:"12px",padding:"0 10px"}},this.text?(null===(t=c[0])||void 0===t?void 0:t.label)||"是":(null===(a=c[1])||void 0===a?void 0:a.label)||"否");break;case"date":var d,u;if(this.formatter)o=(0,n.formatDate)(this.text,this.formatter);else o=null!==(d=null===(u=this.text)||void 0===u?void 0:u.toString())&&void 0!==d?d:"";r=e("span",{style:l},o);break;case"daterange":var f,p,h;if("[object Array]"===Object.prototype.toString.call(this.text))o=this.text.join("~");else o=(null===(f=this.text)||void 0===f?void 0:f.toString())||"";if(this.formatter&&this.text)o=(0,n.formatDate)(o.split("~")[0],this.formatter)+"~"+(0,n.formatDate)(o.split("~")[1],this.formatter);else o=null!==(p=null===(h=o)||void 0===h?void 0:h.toString())&&void 0!==p?p:"";r=e("span",{style:l},o);break;case"text":default:var m,_,g,v;if(this.formatter)o=null!==(m=null===(_=this.text)||void 0===_?void 0:_.toString())&&void 0!==m?m:"-";else o=null!==(g=null===(v=this.text)||void 0===v?void 0:v.toString())&&void 0!==g?g:"-";r=e("span",{style:l},o);break}return this.showTooltip,r}},s=t.parseJsonRange=function(e){if(!e)return[];try{var t=JSON.parse(e);return t}catch(a){}return[]};t.default={name:"GTableCell",components:{FormatedCellText:o},props:{editable:{type:Boolean,default:!1},rowIndex:{type:Number,default:0},row:{type:Object,default:function(){}},column:{type:Object,default:function(){}},options:{type:Array,default:function(){return[]}},editMode:{type:String,default:""},cellEditorBlurSaveModel:{type:Boolean,default:!0},showTooltip:{type:Boolean,default:!1}},data:function(){return{type:"",oValue:""}},created:function(){this.type=this.column.Type,this.oValue=this.row[this.column.Code]},methods:{setEditMode:function(e){var t=this;this.$emit("changeRowEditMode",{index:this.rowIndex,mode:e}),this.$nextTick((function(){var e;null===(e=t.$refs.control)||void 0===e||e.focus()}))},valueChanged:function(e){this.$emit("inlineEdited",{index:this.rowIndex,row:this.row,key:this.column.Code,value:e})},blurHandler:function(){this.cellEditorBlurSaveModel}}}},b26b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9c4");var r=n(a("5530"));t.default={name:"SearchForm",props:{value:{type:Object,default:function(){return{}}},formItems:{type:Array,default:function(){return[]}},rules:{type:Object,default:function(){return{}}},defaultLabelWidth:{type:String,default:"100px"},searchButton:{type:Boolean,default:!0},textAlign:{type:String,default:"left"}},data:function(){return{form:(0,r.default)({},this.value)}},watch:{value:{handler:function(e){JSON.stringify(e)!==JSON.stringify(this.form)&&(this.form=(0,r.default)({},e))},deep:!0},form:{handler:function(e){this.$emit("input",(0,r.default)({},e)),this.$emit("change",(0,r.default)({},e))},deep:!0}},methods:{handleSearch:function(){this.$emit("search",this.form)},handleReset:function(){var e=this;this.reset(),setTimeout((function(){e.handleSearch()}),100)},reset:function(){this.$refs.form.resetFields()}}}},b309:function(e,t,a){"use strict";a("85c9")},b3a3:function(e,t,a){"use strict";a.r(t);var n=a("2e18"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},b407:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("a434"),a("b0c0");var r=n(a("5530")),i=n(a("30bf")),o=a("2f62");t.default={components:{SelectUsers:i.default},props:["plumb","flowData","formTemplate"],data:function(){return{currentSelect:"",Compares:"",flag:!1,formItemLayout:{labelCol:{span:6},wrapperCol:{span:16}},NodeConfluenceTypes:[{id:"all",name:"全部通过"},{id:"one",name:"至少一个通过"}],NodeDesignates:[{id:"SPECIAL_USER",name:"审核者"},{id:"ALL_USER",name:"所有人"},{id:"SUBMIT_DEPARTMENT",name:"发起者部门领导"}],NodeRejectTypes:[{id:"0",name:"前一步"},{id:"1",name:"第一步"},{id:"2",name:"某一步"},{id:"3",name:"用户指定"},{id:"4",name:"不处理"}],rules:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}]},activeKey:"flow-attr"}},computed:(0,r.default)({},(0,o.mapGetters)({currentSelectData:"currentSelect"})),mounted:function(){this.currentSelect=Object.assign({},this.currentSelectData),this.Compares=this.currentSelect.Compares},methods:(0,r.default)((0,r.default)({},(0,o.mapActions)({saveCurrentSelect:"saveCurrentSelect"})),{},{nodeNameChange:function(e){this.currentSelect.name=e.target.value},btnAddCompare:function(){this.Compares.push({FieldName:"",Operation:"",Value:""})},btnDelCompare:function(e){this.Compares.splice(e,1)},linkLabelChange:function(e){var t=this,a=e;t.currentSelect.label=a;var n=t.plumb.getConnections({source:t.currentSelect.from,target:t.currentSelect.to})[0];if(""!==a)n.setLabel({label:a,cssClass:"linkLabel"});else{var r=n.getLabelOverlay();r&&n.removeOverlay(r.id)}},groupChange:function(e,t){this.currentSelect.setInfo.NodeDesignateData[e]=t},usersChange:function(e,t){this.currentSelect.setInfo.NodeDesignateData[e]=t},rolesChange:function(e,t){this.currentSelect.setInfo.NodeDesignateData[e]=t},departmentsChange:function(e,t){this.currentSelect.setInfo.NodeDesignateData[e]=t},handleChangeRoles:function(e){this.currentSelect.setInfo.NodeDesignateData.Texts="",this.currentSelect.setInfo.NodeDesignateData.roles=[],this.currentSelect.setInfo.NodeDesignateData.group=[],this.currentSelect.setInfo.NodeDesignateData.users=[],this.currentSelect.setInfo.NodeDesignateData.departments=[],this.currentSelect.setInfo.NodeDesignateData.departmentsText="",this.currentSelect.setInfo.NodeDesignateData.groupText="",this.currentSelect.setInfo.NodeDesignateData.usersText="",this.currentSelect.setInfo.NodeDesignateData.rolesText=""}}),watch:{currentSelectData:{deep:!0,handler:function(e){this.flag?this.flag=!1:(this.currentSelect=Object.assign({},(0,r.default)({},e)),"sl"===this.currentSelectData.type?this.activeKey="link-attr":this.currentSelectData.type?this.activeKey="node-attr":this.activeKey="flow-attr")}},currentSelect:{handler:function(e){this.Compares=e.Compares,this.saveCurrentSelect(e),this.flag=!0},deep:!0}}}},b5b0:function(e,t,a){"use strict";a.r(t);var n=a("2cb4"),r=a("9096");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},b62c:function(e,t,a){},b6b8:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ZFSN=void 0,a("cb29"),a("a15b"),a("fb6a"),a("a9e3"),a("d3b7"),a("ac1f"),a("25f0"),a("5319");var n=a("def8");t.ZFSN={seqNo:1,consoleLog:function(e){for(var t=0,a=e.length;t<a;t++)e[t]+"\n"},getId:function(){var e=n.flowConfig.idType;if("string"===typeof e){if("uuid"===e)return this.getUUID();if("time_stamp"===e)return this.getTimeStamp()}else if(e instanceof Array){if("time_stamp_and_sequence"===e[0])return this.getSequence(e[1]);if("time_stamp_and_sequence"===e[0])return this.getTimeStampAndSequence(e[1]);if("custom"===e[0])return e[1]()}},getUUID:function(){for(var e=[],t="0123456789abcdef",a=0;a<36;a++)e[a]=t.substr(Math.floor(16*Math.random()),1);e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-";var n=e.join("");return n.replace(/-/g,"")},getTimeStamp:function(){return(new Date).getTime()},getSequence:function(e){var t=new Array(e).fill("0").join("");return(t+this.seqNo++).slice(-e)},getTimeStampAndSequence:function(e){return this.getTimeStamp()+this.getSequence(e)},add:function(e,t){var a,n;try{a=e.toString().split(".")[1].length}catch(i){a=0}try{n=t.toString().split(".")[1].length}catch(i){n=0}var r=Math.pow(10,Math.max(a,n));return(this.mul(e,r)+this.mul(t,r))/r},sub:function(e,t){var a,n;try{a=e.toString().split(".")[1].length}catch(i){a=0}try{n=t.toString().split(".")[1].length}catch(i){n=0}var r=Math.pow(10,Math.max(a,n));return(this.mul(e,r)-this.mul(t,r))/r},mul:function(e,t){var a=0,n=e.toString(),r=t.toString();try{a+=n.split(".")[1].length}catch(i){}try{a+=r.split(".")[1].length}catch(i){}return Number(n.replace(".",""))*Number(r.replace(".",""))/Math.pow(10,a)},div:function(e,t){var a=0,n=0;try{a=e.toString().split(".")[1].length}catch(o){}try{n=t.toString().split(".")[1].length}catch(o){}var r=Number(e.toString().replace(".","")),i=Number(t.toString().replace(".",""));return this.mul(r/i,Math.pow(10,n-a))}}},b71b:function(e,t,a){"use strict";a("7b38")},b799:function(e,t,a){"use strict";a("4678f")},b804:function(e,t,a){"use strict";a.r(t);var n=a("359a"),r=a("2c5c3");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},b834:function(e,t,a){"use strict";a.r(t);var n=a("cc03"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},b85f:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{ref:"flowContainer",staticStyle:{width:"100%",height:"100%",overflow:"hidden",position:"relative"}},[e.isShowContent?a("div",{staticClass:"states-box"},e._l(e.states,(function(t){return a("span",{key:t.type,staticClass:"state-item"},[a("span",{class:t.className}),a("p",[e._v(e._s(t.name))])])})),0):e._e(),a("draggable",e._b({staticClass:"flow-container",class:{zoomIn:"zoom-in"==e.currentTool.type,zoomOut:"zoom-out"==e.currentTool.type,canScale:e.container.scaleFlag,canDrag:e.container.dragFlag,canMultiple:e.rectangleMultiple.flag},staticStyle:{cursor:"pointer"},style:{top:e.container.pos.top+"px",left:e.container.pos.left+"px",transform:"scale("+e.container.scale+")",transformOrigin:e.container.scaleOrigin.left+"px "+e.container.scaleOrigin.top+"px","z-index":e.isDrag?11:-2},attrs:{id:"toDraggable",tag:"div"},on:{add:e.handleAddFormItem,end:e.handleMoveEnd},model:{value:e.flowData.nodes,callback:function(t){e.$set(e.flowData,"nodes",t)},expression:"flowData.nodes"}},"draggable",{group:"flow",animation:200},!1)),a("div",{ref:"flowContainers",staticClass:"flow-container",class:{grid:e.flowData.config.showGrid,zoomIn:"zoom-in"==e.currentTool.type,zoomOut:"zoom-out"==e.currentTool.type,canScale:e.container.scaleFlag,canDrag:e.container.dragFlag,canMultiple:e.rectangleMultiple.flag},staticStyle:{cursor:"pointer"},style:{top:e.container.pos.top+"px",left:e.container.pos.left+"px",transform:"scale("+e.container.scale+")",transformOrigin:e.container.scaleOrigin.left+"px "+e.container.scaleOrigin.top+"px"},attrs:{id:"flowContainer"},on:{DOMMouseScroll:e.scaleContainer,contextmenu:e.showContainerContextMenu,mousedown:e.mousedownHandler,mousemove:e.mousemoveHandler,mouseup:e.mouseupHandler,mousewheel:e.scaleContainer,click:function(t){return t.stopPropagation(),e.containerHandler(t)}}},[e._l(e.flowData.nodes,(function(t){return[t&&t.id?a("flow-node",{key:t.id,attrs:{"current-tool":e.currentTool,"is-show-content":e.isShowContent,node:t,plumb:e.plumb,select:e.currentSelect,"select-group":e.currentSelectGroup},on:{"update:select":function(t){e.currentSelect=t},"update:selectGroup":function(t){e.currentSelectGroup=t},"update:select-group":function(t){e.currentSelectGroup=t},alignForLine:e.alignForLine,hideAlignLine:e.hideAlignLine,isMultiple:e.isMultiple,showNodeContextMenu:e.showNodeContextMenu,updateNodePos:e.updateNodePos}}):e._e()]}))],2),a("vue-context-menu",{attrs:{"context-menu-data":e.containerContextMenuData},on:{levelCenter:e.levelCenter,levelDown:e.levelDown,levelUp:e.levelUp,paste:e.paste,saveFlow:e.saveFlow,selectAll:e.selectAll,verticaLeft:e.verticaLeft,verticalCenter:e.verticalCenter,verticalRight:e.verticalRight}}),a("vue-context-menu",{attrs:{"context-menu-data":e.nodeContextMenuData},on:{copyNode:e.copyNode,deleteNode:e.deleteNode}})],1)},r=[]},b89a:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:e.multiple},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)},r=[]},b926:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("c740"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("7d54"),a("d3b7"),a("18a5"),a("4c53"),a("159b");var r=n(a("2909")),i=n(a("5530")),o=n(a("b76a")),s=a("def8"),l=a("b6b8"),c=n(a("2f53")),d=a("2f62");t.default={components:{draggable:o.default,FlowNode:c.default},props:["browserType","flowData","plumb","select","selectGroup","currentTool","isShowContent","isDrag"],data:function(){return{ctx:null,currentSelectGroup:this.selectGroup,states:[{type:"4",name:"审核中",className:"node-bg"},{type:"1",name:"通过",className:"node-pass-bg"},{type:"2",name:"不通过",className:"node-not-bg"},{type:"0",name:"驳回",className:"node-back-bg"}],container:{pos:{top:-3e3,left:-3e3},startPos:{startMove:!1,x:0,y:0},endPos:{x:0,y:0},dragFlag:!1,draging:!1,scale:s.flowConfig.defaultStyle.containerScale.init,scaleFlag:!1,scaleOrigin:{left:0,top:0},scaleShow:l.ZFSN.mul(s.flowConfig.defaultStyle.containerScale.init,100),auxiliaryLine:{isOpen:s.flowConfig.defaultStyle.isOpenAuxiliaryLine,isShowXLine:!1,isShowYLine:!1,controlFnTimesFlag:!0}},auxiliaryLinePos:{left:0,top:0},mouse:{position:{left:0,top:0},tempPos:{left:0,top:0}},rectangleMultiple:{flag:!1,multipling:!1,position:{top:0,left:0},height:0,width:0},containerContextMenuData:s.flowConfig.contextMenu.container,nodeContextMenuData:s.flowConfig.contextMenu.node,tempLinkId:"",clipboard:[]}},computed:(0,i.default)({},(0,d.mapGetters)({currentSelect:"currentSelect"})),mounted:function(){},methods:(0,i.default)((0,i.default)({},(0,d.mapActions)({saveCurrentSelect:"saveCurrentSelect"})),{},{handleMoveEnd:function(e){e.newIndex,e.oldIndex},handleAddFormItem:function(e){var t=this,a=e.originalEvent.target.className.indexOf("common-x-lane-node")>=0||e.originalEvent.target.className.indexOf("common-y-lane-node")>=0;this.mouse.position.left=a?e.originalEvent.offsetX+e.originalEvent.target.offsetLeft:e.originalEvent.offsetX,this.mouse.position.top=a?e.originalEvent.offsetY+e.originalEvent.target.offsetTop:e.originalEvent.offsetY,this.$nextTick((function(){var a=e.newIndex,n=t.flowData.nodes[a].type,r=t.flowData.nodes[a].belongto;t.$emit("findNodeConfig",r,n,(function(e){e?t.addNewNode(e,a):t.$message.error("未知的节点类型！")}))}))},addNewNode:function(e,t){var a=this,n=a.mouse.position.left,r=a.mouse.position.top,o=a.computeNodePos(n,r);n=o.left,r=o.top;var s=Object.assign({},e);s.id=s.type+"-"+l.ZFSN.getId(),s.height=50,"start round mix"===s.type||"end round"===s.type||"event"===s.type||"gateway"===s.type?(s.left=n-25,s.width=50):(s.left=n-60,s.width=120),s.top=r-25,"x-lane"===s.type?(s.height=200,s.width=600):"y-lane"===s.type&&(s.height=600,s.width=200),this.$set(this.flowData.nodes,t,(0,i.default)({},s)),this.$emit("selectTool","drag")},computeNodePos:function(e,t){var a=s.flowConfig.defaultStyle.alignGridPX[0],n=s.flowConfig.defaultStyle.alignGridPX[1];return e%a&&(e=a-e%a+e),t%n&&(t=n-t%n+t),{left:e,top:t}},mousedownHandler:function(e){var t=this,a=window.event||e;t.container.startPos={startMove:!0,x:a.clientX,y:a.clientY,l:this.$refs.flowContainers.offsetLeft,t:this.$refs.flowContainers.offsetTop},0===a.button&&(t.container.dragFlag&&(t.mouse.tempPos=t.mouse.position,t.container.draging=!0),t.currentSelectGroup=[],t.rectangleMultiple.flag&&(t.mouse.tempPos=t.mouse.position,t.rectangleMultiple.multipling=!0))},mousemoveHandler:function(e){var t=this,a=window.event||e;if(t.container.startPos.startMove){t.container.endPos={x:a.clientX,y:a.clientY};var n=t.container.endPos.y-(t.container.startPos.y-t.container.startPos.t),r=t.container.endPos.x-(t.container.startPos.x-t.container.startPos.l),i=Math.abs(t.container.endPos.y-t.container.startPos.y)>=20,o=Math.abs(t.container.endPos.x-t.container.startPos.x)>=20;t.container.pos={top:i?n:t.container.pos.top,left:o?r:t.container.pos.left}}if("flowContainer"===a.target.id)t.mouse.position={left:a.offsetX,top:a.offsetY};else{var s=a.target.className,l=a.target.tagName;"lane-text"!==s&&"lane-text-div"!==s&&"svg"!==l&&"path"!==l&&"I"!==l&&(t.mouse.position.left=a.target.offsetLeft+a.offsetX,t.mouse.position.top=a.target.offsetTop+a.offsetY)}if(t.container.draging){var c=t.container.pos.top+(t.mouse.position.top-t.mouse.tempPos.top),d=t.container.pos.left+(t.mouse.position.left-t.mouse.tempPos.left);c>=0&&(c=0),d>=0&&(d=0),t.container.pos={top:c,left:d}}if(t.rectangleMultiple.multipling){var u=t.mouse.position.top-t.mouse.tempPos.top,f=t.mouse.position.left-t.mouse.tempPos.left,p=t.mouse.tempPos.top,h=t.mouse.tempPos.left;u>=0&&f<0?(f=-f,h-=f):u<0&&f>=0?(u=-u,p-=u):u<0&&f<0&&(u=-u,f=-f,p-=u,h-=f),t.rectangleMultiple.height=u,t.rectangleMultiple.width=f,t.rectangleMultiple.position.top=p,t.rectangleMultiple.position.left=h}},mouseupHandler:function(){var e=this;e.container.startPos.startMove=!1,e.container.draging&&(e.container.draging=!1),e.rectangleMultiple.multipling&&(e.judgeSelectedNode(),e.rectangleMultiple.multipling=!1,e.rectangleMultiple.width=0,e.rectangleMultiple.height=0)},judgeSelectedNode:function(){var e=this,t=e.rectangleMultiple.position.top,a=e.rectangleMultiple.position.left,n=t+e.rectangleMultiple.height,r=a+e.rectangleMultiple.width,i=e.flowData.nodes;i.forEach((function(i){i.top>=t&&i.left>=a&&i.top<=n&&i.left<=r&&(e.plumb.addToDragSelection(i.id),e.currentSelectGroup.push(i))}))},scaleContainer:function(e){e.preventDefault();var t=this,a=window.event||e;t.container.scaleFlag?2===t.browserType?a.detail<0?t.enlargeContainer():t.narrowContainer():a.deltaY<0?t.enlargeContainer():t.container.scale&&t.narrowContainer():a.wheelDelta>0?t.enlargeContainer():t.narrowContainer()},enlargeContainer:function(){var e=this;e.container.scaleOrigin.left=e.mouse.position.left,e.container.scaleOrigin.top=e.mouse.position.top;var t=l.ZFSN.add(e.container.scale,s.flowConfig.defaultStyle.containerScale.onceEnlarge);t<=s.flowConfig.defaultStyle.containerScale.max&&(e.container.scale=t,e.container.scaleShow=l.ZFSN.mul(e.container.scale,100),e.plumb.setZoom(e.container.scale))},narrowContainer:function(){var e=this;e.container.scaleOrigin.left=e.mouse.position.left,e.container.scaleOrigin.top=e.mouse.position.top;var t=l.ZFSN.sub(e.container.scale,s.flowConfig.defaultStyle.containerScale.onceNarrow);t>=s.flowConfig.defaultStyle.containerScale.min&&(e.container.scale=t,e.container.scaleShow=l.ZFSN.mul(e.container.scale,100),e.plumb.setZoom(e.container.scale))},showContainerContextMenu:function(e){var t=window.event||e;t.preventDefault(),this.selectContainer();var a=t.clientX,n=t.clientY;this.containerContextMenuData.axis={x:a,y:n}},showNodeContextMenu:function(e){var t=window.event||e;t.preventDefault();var a=t.clientX,n=t.clientY;this.nodeContextMenuData.axis={x:a,y:n}},flowInfo:function(){var e=this,t=e.flowData.nodes,a=e.flowData.lines;alert("当前流程图中有 "+t.length+" 个节点，有 "+a.length+" 条连线。")},paste:function(){var e=this,t=0;e.clipboard.forEach((function(a){var n=Object.assign({},a);n.id=n.type+"-"+l.ZFSN.getId();var r=e.computeNodePos(e.mouse.position.left+t,e.mouse.position.top+t);n.left=r.left,n.top=r.top,t+=20,e.flowData.nodes.push(n)}))},selectAll:function(){var e=this;e.flowData.nodes.forEach((function(t){e.plumb.addToDragSelection(t.id),e.currentSelectGroup.push(t)}))},saveFlow:function(){this.$emit("saveFlow")},checkAlign:function(){return!(this.currentSelectGroup.length<2)||(this.$message.error("请选择至少两个节点！"),!1)},verticaLeft:function(){var e=this;if(e.checkAlign())for(var t=e.flowData.nodes,a=e.currentSelectGroup,n=a[0].left,r=a[0].top,i=function(i){r=r+a[i-1].height+s.flowConfig.defaultStyle.alignSpacing.vertical;var o=t.filter((function(e){return e.id===a[i].id}))[0];o.tx=n,o.ty=r,e.plumb.animate(a[i].id,{top:r,left:n},{duration:s.flowConfig.defaultStyle.alignDuration,complete:function(){o.left=o.tx,o.top=o.ty}})},o=1;o<a.length;o++)i(o)},verticalCenter:function(){var e=this;if(e.checkAlign())for(var t=e.flowData.nodes,a=e.currentSelectGroup,n=a[0].left,r=a[0].top,i=n,o=function(o){r=r+a[o-1].height+s.flowConfig.defaultStyle.alignSpacing.vertical,n=i+l.ZFSN.div(a[0].width,2)-l.ZFSN.div(a[o].width,2);var c=t.filter((function(e){return e.id===a[o].id}))[0];c.tx=n,c.ty=r,e.plumb.animate(a[o].id,{top:r,left:n},{duration:s.flowConfig.defaultStyle.alignDuration,complete:function(){c.left=c.tx,c.top=c.ty}})},c=1;c<a.length;c++)o(c)},verticalRight:function(){var e=this;if(e.checkAlign())for(var t=e.flowData.nodes,a=e.currentSelectGroup,n=a[0].left,r=a[0].top,i=n,o=function(o){r=r+a[o-1].height+s.flowConfig.defaultStyle.alignSpacing.vertical,n=i+a[0].width-a[o].width;var l=t.filter((function(e){return e.id===a[o].id}))[0];l.tx=n,l.ty=r,e.plumb.animate(a[o].id,{top:r,left:n},{duration:s.flowConfig.defaultStyle.alignDuration,complete:function(){l.left=l.tx,l.top=l.ty}})},l=1;l<a.length;l++)o(l)},levelUp:function(){var e=this;if(e.checkAlign())for(var t=e.flowData.nodes,a=e.currentSelectGroup,n=a[0].left,r=a[0].top,i=function(i){n=n+a[i-1].width+s.flowConfig.defaultStyle.alignSpacing.level;var o=t.filter((function(e){return e.id===a[i].id}))[0];o.tx=n,o.ty=r,e.plumb.animate(a[i].id,{top:r,left:n},{duration:s.flowConfig.defaultStyle.alignDuration,complete:function(){o.left=o.tx,o.top=o.ty}})},o=1;o<a.length;o++)i(o)},levelCenter:function(){var e=this;if(e.checkAlign())for(var t=e.flowData.nodes,a=e.currentSelectGroup,n=a[0].left,r=a[0].top,i=r,o=function(o){r=i+l.ZFSN.div(a[0].height,2)-l.ZFSN.div(a[o].height,2),n=n+a[o-1].width+s.flowConfig.defaultStyle.alignSpacing.level;var c=t.filter((function(e){return e.id===a[o].id}))[0];c.tx=n,c.ty=r,e.plumb.animate(a[o].id,{top:r,left:n},{duration:s.flowConfig.defaultStyle.alignDuration,complete:function(){c.left=c.tx,c.top=c.ty}})},c=1;c<a.length;c++)o(c)},levelDown:function(){var e=this;if(e.checkAlign())for(var t=e.flowData.nodes,a=e.currentSelectGroup,n=a[0].left,r=a[0].top,i=r,o=function(o){r=i+a[0].height-a[o].height,n=n+a[o-1].width+s.flowConfig.defaultStyle.alignSpacing.level;var l=t.filter((function(e){return e.id===a[o].id}))[0];l.tx=n,l.ty=r,e.plumb.animate(a[o].id,{top:r,left:n},{duration:s.flowConfig.defaultStyle.alignDuration,complete:function(){l.left=l.tx,l.top=l.ty}})},l=1;l<a.length;l++)o(l)},addRemark:function(){},copyNode:function(){var e=this;e.clipboard=[],e.currentSelectGroup.length>0?e.clipboard=Object.assign([],e.currentSelectGroup):e.currentSelect.id&&e.clipboard.push(e.currentSelect)},getConnectionsByNodeId:function(e){var t=this,a=t.plumb.getConnections({source:e}),n=t.plumb.getConnections({target:e});return a.concat(n)},deleteNode:function(){var e=this,t=this,a=t.flowData.nodes,n=t.flowData.lines;t.flowData.lines=n.filter((function(t){return t.from!==e.currentSelect.id&&t.to!==e.currentSelect.id})),t.plumb.deleteConnectionsForElement(this.currentSelect.id);var r=a.findIndex((function(t){return t.id===e.currentSelect.id}));a.splice(r,1),t.selectContainer()},handleConnect:function(){var e=this;e.$nextTick((function(){var t=(0,r.default)(e.flowData.lines);t.forEach((function(t){var a=e.plumb.connect({source:t.from,target:t.to,anchor:s.flowConfig.jsPlumbConfig.anchor.default,connector:[t.cls.linkType,{gap:5,cornerRadius:8,alwaysRespectStubs:!0}],paintStyle:{stroke:t.cls.linkColor,strokeWidth:t.cls.linkThickness}});""!==t.label&&a.setLabel({label:t.label,cssClass:"linkLabel"})}))}))},containerHandler:function(){var e=this;e.selectContainer();var t=e.currentTool.type;"zoom-in"===t?e.enlargeContainer():"zoom-out"===t&&e.narrowContainer()},selectContainer:function(){var e={};this.saveCurrentSelect(e),this.$emit("getShortcut")},isMultiple:function(e){e(this.rectangleMultiple.flag)},updateNodePos:function(){var e=this,t=e.flowData.nodes;e.currentSelectGroup.forEach((function(e){var a=parseInt(document.getElementById(e.id).style.left),n=parseInt(document.getElementById(e.id).style.top),r=t.filter((function(t){return t.id===e.id}))[0];r.left=a,r.top=n}))},alignForLine:function(e){var t=this;if(!(t.selectGroup.length>1)&&t.container.auxiliaryLine.controlFnTimesFlag){var a=e.el.id,n=t.flowData.nodes;n.forEach((function(n){if(a!==n.id){var r=s.flowConfig.defaultStyle.showAuxiliaryLineDistance,i=e.pos,o=e.el.offsetHeight,l=e.el.offsetWidth,c=i[0]-n.left,d=i[1]-n.top;if(c>=-r&&c<=r||c+l>=-r&&c+l<=r){t.container.auxiliaryLine.isShowYLine=!0,t.auxiliaryLinePos.left=n.left+t.container.pos.left;var u=n.left+n.width/2;u===i[0]+l/2&&(t.auxiliaryLinePos.left=u+t.container.pos.left)}if(d>=-r&&d<=r||d+o>=-r&&d+o<=r){t.container.auxiliaryLine.isShowXLine=!0,t.auxiliaryLinePos.top=n.top+t.container.pos.top;var f=n.top+n.height/2;f===i[1]+o/2&&(t.auxiliaryLinePos.top=f+t.container.pos.left)}}})),t.container.auxiliaryLine.controlFnTimesFlag=!1,setTimeout((function(){t.container.auxiliaryLine.controlFnTimesFlag=!0}),200)}},hideAlignLine:function(){this.container.auxiliaryLine.isOpen&&(this.container.auxiliaryLine.isShowXLine=!1,this.container.auxiliaryLine.isShowYLine=!1)}}),watch:{currentSelect:function(){""!==this.tempLinkId&&(document.getElementById(this.tempLinkId)&&document.getElementById(this.tempLinkId).classList&&document.getElementById(this.tempLinkId).classList.remove("link-active"),this.tempLinkId=""),"sl"===this.currentSelect.type&&(this.tempLinkId=this.currentSelect.id,document.getElementById(this.currentSelect.id).classList.add("link-active"))},selectGroup:function(e){this.currentSelectGroup=e,this.currentSelectGroup.length<=0&&this.plumb.clearDragSelection()},currentSelectGroup:{handler:function(e){this.$emit("update:selectGroup",e)},deep:!0}}}},b956:function(e,t,a){"use strict";a.r(t);var n=a("2348"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},b97b:function(e,t,a){"use strict";a.r(t);var n=a("3c2d"),r=a("c295");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("d48d");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"2fb14234",null);t["default"]=s.exports},ba7d:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("93aa");t.default={name:"SelectPartType",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},isRaw:{type:Boolean,default:!0}},data:function(){return{list:[],treeParamsDepart:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}}}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.value),this.$emit("change",this.value)},getList:function(){var e=this,t=this.isRaw?n.GetCategoryTreeList:n.GetAuxCategoryTreeList;t({isAll:!0}).then((function(t){if(t.IsSucceed){var a=t.Data;e.$nextTick((function(t){var n;null===(n=e.$refs)||void 0===n||null===(n=n.treeSelectDepart)||void 0===n||n.treeDataUpdateFun(a)}))}else e.$message({message:t.Message,type:"error"})}))}}}},bad9:function(e,t,a){"use strict";a.r(t);var n=a("1c89"),r=a("f60e");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},bae6:function(e,t,a){"use strict";a.r(t);var n=a("19d9"),r=a("fcd6");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("4de7");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"157fa37e",null);t["default"]=s.exports},baea:function(e,t,a){},bb2b:function(e,t,a){"use strict";function n(){var e="https://export.dhtmlx.com/gantt",t=["leftside_text","rightside_text","task_text","progress_text","task_class"];function a(e,t,a){gantt.env.isIE?(gantt.env.isIE=!1,gantt.ajax.post(e,t,a),gantt.env.isIE=!0):gantt.ajax.post(e,t,a)}function n(e,t){for(var a in t)e[a]||(e[a]=t[a]);return e}function r(e){var t=e.config.columns;if(t)for(var a=0;a<t.length;a++)t[a].template&&(t[a].$template=!0)}function i(i){var o={};function s(e){if(!t){var t=document.createElement("DIV");t.style.cssText="position:absolute; display:none;",document.body.appendChild(t)}return o[e]?o[e]:(t.className=e,o[e]=h(t,"color")+";"+h(t,"backgroundColor"))}function l(e){var t=[];return e.forEach((function(e){t.push(e.startMinute),t.push(e.endMinute)})),t}function c(e,t){return function(a,n,r){n=n||this.config.root_id,r=r||this;var i=this.getChildren(n);if(i)for(var o=0;o<i.length;o++){var s=this._pull[i[o]];(!e||s.end_date>e)&&(!t||s.start_date<t)&&a.call(r,s),this.hasChild(s.id)&&this.eachTask(a,s.id,r)}}}function d(t){var a=t.server||e,n=t.store||0,r=t.data,i=t.callback;r.append("type","excel-parse"),r.append("data",JSON.stringify({sheet:t.sheet||0})),n&&r.append("store",n);var o=new XMLHttpRequest;o.onreadystatechange=function(e){4==o.readyState&&0==o.status&&i&&i(null)},o.onload=function(){var e=o.status>400,t=null;if(!e)try{t=JSON.parse(o.responseText)}catch(a){}i&&i(t)},o.open("POST",a,!0),o.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.send(r)}i._getWorktimeSettings=function(){var e,t={hours:[0,24],minutes:null,dates:{0:!0,1:!0,2:!0,3:!0,4:!0,5:!0,6:!0}};if(i.config.work_time){var a=i._working_time_helper;if(a&&a.get_calendar)e=a.get_calendar();else if(a)e={hours:a.hours,minutes:null,dates:a.dates};else if(i.config.worktimes&&i.config.worktimes.global){var n=i.config.worktimes.global;if(n.parsed){var r=l(n.parsed.hours);for(var o in e={hours:null,minutes:r,dates:{}},n.parsed.dates)Array.isArray(n.parsed.dates[o])?e.dates[o]=l(n.parsed.dates[o]):e.dates[o]=n.parsed.dates[o]}else e={hours:n.hours,minutes:null,dates:n.dates}}else e=t}else e=t;return e},i.exportToPDF=function(e){e&&e.raw?e=n(e,{name:"gantt.pdf",data:this._serialize_html()}):(e=n(e||{},{name:"gantt.pdf",data:this._serialize_all(),config:this.config}),b(i,e.config.columns)),e.version=this.version,this._send_to_export(e,"pdf")},i.exportToPNG=function(e){e&&e.raw?e=n(e,{name:"gantt.png",data:this._serialize_html()}):(e=n(e||{},{name:"gantt.png",data:this._serialize_all(),config:this.config}),b(i,e.config.columns)),e.version=this.version,this._send_to_export(e,"png")},i.exportToICal=function(e){e=n(e||{},{name:"gantt.ical",data:this._serialize_plain().data,version:this.version}),this._send_to_export(e,"ical")},i.exportToExcel=function(e){var t,a,r,i;if(e=e||{},e.start||e.end){r=this.getState(),a=[this.config.start_date,this.config.end_date],i=this.getScrollState();var o=this.date.str_to_date(this.config.date_format);t=this.eachTask,e.start&&(this.config.start_date=o(e.start)),e.end&&(this.config.end_date=o(e.end)),this.render(),this.eachTask=c(this.config.start_date,this.config.end_date)}this._no_progress_colors="base-colors"===e.visual,e=n(e,{name:"gantt.xlsx",title:"Tasks",data:this._serialize_table(e).data,columns:this._serialize_columns({rawDates:!0}),version:this.version}),e.visual&&(e.scales=this._serialize_scales(e)),this._send_to_export(e,"excel"),(e.start||e.end)&&(this.config.start_date=r.min_date,this.config.end_date=r.max_date,this.eachTask=t,this.render(),this.scrollTo(i.x,i.y),this.config.start_date=a[0],this.config.end_date=a[1])},i.exportToJSON=function(e){e=n(e||{},{name:"gantt.json",data:this._serialize_all(),config:this.config,columns:this._serialize_columns(),worktime:i._getWorktimeSettings(),version:this.version}),this._send_to_export(e,"json")},i.importFromExcel=function(e){var t=e.data;if(t instanceof FormData);else if(t instanceof File){var a=new FormData;a.append("file",t),e.data=a}d(e)},i._msp_config=function(e){if(e.project)for(var t in e.project)e._custom_data||(e._custom_data={}),e._custom_data[t]=e.project[t](this.config);if(e.tasks)for(var a=0;a<e.data.length;a++){var n=this.getTask(e.data[a].id);for(var t in n._custom_data||(n._custom_data={}),e.tasks)n._custom_data[t]=e.tasks[t](n,this.config)}delete e.project,delete e.tasks,e.time=i._getWorktimeSettings();var r=this.getSubtaskDates(),o=this.date.date_to_str("%d-%m-%Y %H:%i:%s");e.start_end={start_date:o(r.start_date),end_date:o(r.end_date)}},i._msp_data=function(){var e=this.templates.xml_format,t=this.templates.format_date;this.templates.xml_format=this.date.date_to_str("%d-%m-%Y %H:%i:%s"),this.templates.format_date=this.date.date_to_str("%d-%m-%Y %H:%i:%s");var a=this._serialize_all();return this.templates.xml_format=e,this.templates.format_date=t,a},i._ajax_to_export=function(t,n,r){delete t.callback;var i=t.server||e,o="type="+n+"&store=1&data="+encodeURIComponent(JSON.stringify(t)),s=function(e){var t=e.xmlDoc||e,a=t.status>400,n=null;if(!a)try{n=JSON.parse(t.responseText)}catch(i){}r(n)};a(i,o,s)},i._send_to_export=function(t,a){var n=this.date.date_to_str(this.config.date_format||this.config.xml_date);if(t.config&&(t.config=this.copy(t.config),r(t,a),t.config.start_date&&t.config.end_date&&(t.config.start_date instanceof Date&&(t.config.start_date=n(t.config.start_date)),t.config.end_date instanceof Date&&(t.config.end_date=n(t.config.end_date)))),t.callback)return i._ajax_to_export(t,a,t.callback);var o=this._create_hidden_form();o.firstChild.action=t.server||e,o.firstChild.childNodes[0].value=JSON.stringify(t),o.firstChild.childNodes[1].value=a,o.firstChild.submit()},i._create_hidden_form=function(){if(!this._hidden_export_form){var e=this._hidden_export_form=document.createElement("div");e.style.display="none",e.innerHTML="<form method='POST' target='_blank'><textarea name='data' style='width:0px; height:0px;' readonly='true'></textarea><input type='hidden' name='type' value=''></form>",document.body.appendChild(e)}return this._hidden_export_form};var u=i.json._copyObject;function f(e){var t={};for(var a in e)"$"!=a.charAt(0)&&(t[a]=e[a]);var n=i.templates.xml_format||i.templates.format_date;return t.start_date=n(t.start_date),t.end_date&&(t.end_date=n(t.end_date)),t}function p(e){var t=i.templates.task_text(e.start_date,e.end_date,e),a=f(e);return a.text=t||a.text,a}function h(e,t){var a=e.currentStyle?e.currentStyle[t]:getComputedStyle(e,null)[t],n=a.replace(/\s/g,"").match(/^rgba?\((\d+),(\d+),(\d+)/i);return(n&&4===n.length?("0"+parseInt(n[1],10).toString(16)).slice(-2)+("0"+parseInt(n[2],10).toString(16)).slice(-2)+("0"+parseInt(n[3],10).toString(16)).slice(-2):a).replace("#","")}var m=i.date.date_to_str("%Y-%m-%dT%H:%i:%s.000Z");function _(e){var t=v(e,p(e));t.start_date&&(t.start_date=m(e.start_date)),t.end_date&&(t.end_date=m(e.end_date));var a=i._day_index_by_date?i._day_index_by_date:i.columnIndexByDate;t.$start=a.call(i,e.start_date),t.$end=a.call(i,e.end_date),t.$level=e.$level,t.$type=e.$rendered_type;var n=i.templates;return t.$text=n.task_text(e.start,e.end_date,e),t.$left=n.leftside_text?n.leftside_text(e.start,e.end_date,e):"",t.$right=n.rightside_text?n.rightside_text(e.start,e.end_date,e):"",t}function g(e){var t=_(e),a=i.getTaskNode(e.id);if(a&&a.firstChild){var n=h(i._no_progress_colors?a:a.firstChild,"backgroundColor");"363636"==n&&(n=h(a,"backgroundColor")),t.$color=n}else e.color&&(t.$color=e.color);return t}function v(e,t){for(var a=0;a<i.config.columns.length;a++){var n=i.config.columns[a].template;if(n){var r=n(e);r instanceof Date&&(r=i.templates.date_grid(r,e)),t["_"+a]=r}}return t}function y(e){for(var a=f(e),n=0;n<t.length;n++){var r=i.templates[t[n]];r&&(a["$"+n]=r(e.start_date,e.end_date,e))}return v(e,a),a.open=e.$open,a}function b(e,t){for(var a=0;a<t.length;a++)t[a].label=t[a].label||e.locale.labels["column_"+t[a].name],"string"===typeof t[a].width&&(t[a].width=1*t[a].width)}function D(){if(i._scale_helpers){var e=i._get_scales(),t=i.config.min_column_width,a=i._get_resize_options().x?Math.max(i.config.autosize_min_width,0):config.$task.offsetWidth,n=config.config.scale_height-1;return i._scale_helpers.prepareConfigs(e,t,a,n)}var r=i.$ui.getView("timeline");if(r){var o=r.$config.width;"x"!=i.config.autosize&&"xy"!=i.config.autosize||(o=Math.max(i.config.autosize_min_width,0));var s=i.getState(),l=(e=r._getScales(),t=i.config.min_column_width,n=i.config.scale_height-1,i.config.rtl);return r.$scaleHelper.prepareConfigs(e,t,o,n,s.min_date,s.max_date,l)}}function S(){i.exportMode=!0;var e=i.templates.xml_format,t=i.templates.format_date;i.templates.xml_format=i.templates.format_date=i.date.date_to_str(i.config.date_format||i.config.xml_date);var a=i.serialize();return i.templates.xml_format=e,i.templates.format_date=t,i.exportMode=!1,a}i._serialize_html=function(){var e=i.config.smart_scales,t=i.config.smart_rendering;(e||t)&&(i.config.smart_rendering=!1,i.config.smart_scales=!1,i.render());var a=this.$container.parentNode.innerHTML;return(e||t)&&(i.config.smart_scales=e,i.config.smart_rendering=t,i.render()),a},i._serialize_all=function(){i.json._copyObject=y;var e=S();return i.json._copyObject=u,e},i._serialize_plain=function(){var e=i.templates.xml_format,t=i.templates.format_date;i.templates.xml_format=i.date.date_to_str("%Y%m%dT%H%i%s",!0),i.templates.format_date=i.date.date_to_str("%Y%m%dT%H%i%s",!0),i.json._copyObject=p;var a=S();return i.templates.xml_format=e,i.templates.format_date=t,i.json._copyObject=u,delete a.links,a},i._serialize_table=function(e){i.json._copyObject=e.visual?g:_;var t=S();if(i.json._copyObject=u,delete t.links,e.cellColors){var a=this.templates.timeline_cell_class||this.templates.task_cell_class;if(a){for(var n=D(),r=n[0].trace_x,o=1;o<n.length;o++)n[o].trace_x.length>r.length&&(r=n[o].trace_x);for(o=0;o<t.data.length;o++){t.data[o].styles=[];for(var l=this.getTask(t.data[o].id),c=0;c<r.length;c++){var d=r[c],f=a(l,d);f&&t.data[o].styles.push({index:c,styles:s(f)})}}}}return t},i._serialize_scales=function(e){for(var t=[],a=D(),n=1/0,r=0,o=0;o<a.length;o++)n=Math.min(n,a[o].col_width);for(o=0;o<a.length;o++){var l=0,c=0,d=[];t.push(d);var u=a[o];r=Math.max(r,u.trace_x.length);for(var f=u.format||u.template||(u.date?i.date.date_to_str(u.date):i.config.date_scale),p=0;p<u.trace_x.length;p++){var h=u.trace_x[p];c=l+Math.round(u.width[p]/n);var m={text:f(h),start:l,end:c};if(e.cellColors){var _=u.css||this.templates.scale_cell_class;if(_){var g=_(h);g&&(m.styles=s(g))}}d.push(m),l=c}}return{width:r,height:t.length,data:t}},i._serialize_columns=function(e){i.exportMode=!0;for(var t=[],a=i.config.columns,n=0,r=0;r<a.length;r++)"add"!=a[r].name&&"buttons"!=a[r].name&&(t[n]={id:a[r].template?"_"+r:a[r].name,header:a[r].label||i.locale.labels["column_"+a[r].name],width:a[r].width?Math.floor(a[r].width/4):""},"duration"==a[r].name&&(t[n].type="number"),"start_date"!=a[r].name&&"end_date"!=a[r].name||(t[n].type="date",e&&e.rawDates&&(t[n].id=a[r].name)),n++);return i.exportMode=!1,t}}gantt.ajax||(window.dhtmlxAjax?gantt.ajax=window.dhtmlxAjax:window.dhx4&&(gantt.ajax=window.dhx4.ajax)),i(gantt),window.Gantt&&Gantt.plugin&&Gantt.plugin(i)}function r(){var e="https://export.dhtmlx.com/gantt";function t(e){for(var t=0;t<e.length;t++){0==e[t].parent&&(e[t]._lvl=1);for(var a=t+1;a<e.length;a++)e[t].id==e[a].parent&&(e[a]._lvl=e[t]._lvl+1)}}function a(e){for(var t=0;t<e.length;t++)delete e[t]._lvl}function n(e){t(e.data);for(var n={},i=0;i<e.data.length;i++)n[e.data[i].id]=e.data[i];var l={};for(i=0;i<e.links.length;i++){var c=e.links[i];gantt.isTaskExists(c.source)&&gantt.isTaskExists(c.target)&&n[c.source]&&n[c.target]&&(l[c.id]=c)}for(i in l)o(l[i],n);var d={};for(i in n)r(n[i],l,n,{},d,null);for(i in l)s(l,n);for(i=0;i<e.links.length;i++)l[e.links[i].id]||(e.links.splice(i,1),i--);a(e.data)}function r(e,t,a,n,o,s){var l=e.$_source;if(l){n[e.id]&&i(s,t,n,o),n[e.id]=!0;for(var c={},d=0;d<l.length;d++)if(!o[l[d]]){var u=t[l[d]],f=a[u._target];c[f.id]&&i(u,t,n,o),c[f.id]=!0,r(f,t,a,n,o,u)}n[e.id]=!1}}function i(e,t,a,n){e&&(gantt.callEvent("onExportCircularDependency",[e.id,e])&&delete t[e.id],delete a[e._source],delete a[e._target],n[e.id]=!0)}function o(e,t){var a,n,r={target:t[e.target],source:t[e.source]};if(r.target._lvl!=r.source._lvl){r.target._lvl<r.source._lvl?(a="source",n=r.target._lvl):(a="target",n=r.source._lvl);do{var i=t[r[a].parent];if(!i)break;r[a]=i}while(r[a]._lvl<n);var o=t[r.source.parent],s=t[r.target.parent];while(o&&s&&o.id!=s.id)r.source=o,r.target=s,o=t[r.source.parent],s=t[r.target.parent]}e._target=r.target.id,e._source=r.source.id,r.target.$_target||(r.target.$_target=[]),r.target.$_target.push(e.id),r.source.$_source||(r.source.$_source=[]),r.source.$_source.push(e.id)}function s(e,t){for(var a in e)delete e[a]._target,delete e[a]._source;for(var n in t)delete t[n].$_source,delete t[n].$_target}function l(e,t){if(t&&t.project){for(var a in t.project)gantt.config.$custom_data||(gantt.config.$custom_data={}),gantt.config.$custom_data[a]="function"===typeof t.project[a]?t.project[a](gantt.config):t.project[a];delete t.project}}function c(e,t){t&&t.tasks&&(e.data.forEach((function(e){for(var a in t.tasks)e.$custom_data||(e.$custom_data={}),e.$custom_data[a]="function"===typeof t.tasks[a]?t.tasks[a](e,gantt.config):t.tasks[a]})),delete t.tasks)}function d(e,t){var a=t.name||"gantt.xml";delete t.name,gantt.config.custom=t;var n=gantt._getWorktimeSettings(),r=gantt.getSubtaskDates();if(r.start_date&&r.end_date){var i=gantt.templates.format_date||gantt.templates.xml_format;gantt.config.start_end={start_date:i(r.start_date),end_date:i(r.end_date)}}var o=void 0!==t.auto_scheduling&&!!t.auto_scheduling,s={callback:t.callback||null,config:gantt.config,data:e,manual:o,name:a,worktime:n};for(var l in t)s[l]=t[l];return s}function u(t){function a(t){var a=t.server||e,n=t.store||0,r=t.data,i=t.callback,o={};t.durationUnit&&(o.durationUnit=t.durationUnit),t.projectProperties&&(o.projectProperties=t.projectProperties),t.taskProperties&&(o.taskProperties=t.taskProperties),r.append("type",t.type||"msproject-parse"),r.append("data",JSON.stringify(o)),n&&r.append("store",n);var s=new XMLHttpRequest;s.onreadystatechange=function(e){4==s.readyState&&0==s.status&&i&&i(null)},s.onload=function(){var e=s.status>400,t=null;if(!e)try{t=JSON.parse(s.responseText)}catch(a){}i&&i(t)},s.open("POST",a,!0),s.setRequestHeader("X-Requested-With","XMLHttpRequest"),s.send(r)}t._ms_export={},t.exportToMSProject=function(e){e=e||{},e.skip_circular_links=void 0===e.skip_circular_links||!!e.skip_circular_links;var t=this.templates.xml_format,a=this.templates.format_date,r=this.config.xml_date,i=this.config.date_format,o="%d-%m-%Y %H:%i:%s";this.config.xml_date=o,this.config.date_format=o,this.templates.xml_format=this.date.date_to_str(o),this.templates.format_date=this.date.date_to_str(o);var s=this._serialize_all();l(s,e),c(s,e),e.skip_circular_links&&n(s),e=d(s,e),this._send_to_export(e,e.type||"msproject"),this.config.xml_date=r,this.config.date_format=i,this.templates.xml_format=t,this.templates.format_date=a,this.config.$custom_data=null,this.config.custom=null},t.exportToPrimaveraP6=function(e){return e.type="primaveraP6",t.exportToMSProject(e)},t.importFromMSProject=function(e){var t=e.data;if(t instanceof FormData);else if(t instanceof File){var n=new FormData;n.append("file",t),e.data=n}a(e)},t.importFromPrimaveraP6=function(e){return e.type="primaveraP6-parse",t.importFromMSProject(e)}}u(gantt),window.Gantt&&Gantt.plugin&&Gantt.plugin(u)}Object.defineProperty(t,"__esModule",{value:!0}),t.ep1=n,t.ep2=r,a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("7d54"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("466d"),a("5319"),a("159b")},bb48:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909"));a("99af"),a("4de4"),a("e9f5"),a("910d"),a("7d54"),a("a732"),a("d3b7"),a("159b");var i=a("6186"),o=n(a("1463"));t.default={components:{TreeDetail:o.default},props:{userNames:{type:String,default:""},users:{type:Array,default:function(){return[]}},show:{type:Boolean,default:!1}},data:function(){return{treeLoading:!1,expandedKey:"",treeData:[],tableData:[],multipleSelection:[],tbLoading:!1,selectUsers:[]}},watch:{multipleSelection:function(){var e=this;this.selectUsers=this.selectUsers.filter((function(t){return!e.multipleSelection.some((function(e){return e.Id===t.Id}))}))}},mounted:function(){var e=this;this.$nextTick((function(t){e.selectUsers=(0,r.default)(e.users),e.fetchTreeData()}))},methods:{groupData:function(){this.selectUsers=[].concat((0,r.default)(this.selectUsers),(0,r.default)(this.multipleSelection))},handleSelectionUser:function(e){this.multipleSelection=e},fetchTreeData:function(){var e=this;this.treeLoading=!0,(0,i.GetDepartmentTree)().then((function(t){e.treeData=t.Data,e.treeLoading=!1;var a=e.treeData&&e.treeData.length>0&&e.treeData[0],n=a.Id;e.expandedKey=n,e.fetchUserList(n)}))},fetchUserList:function(e){var t=this;this.groupData(),this.tbLoading=!0,(0,i.GetUserList)({departmentId:e}).then((function(e){t.tableData=e.Data,t.tbLoading=!1,t.$nextTick((function(e){t.multipleSelection=t.tableData.filter((function(e){return t.selectUsers.some((function(t){return t.Id===e.Id}))})),t.setSelectTable()}))}))},handleNodeClick:function(e){this.fetchUserList(e.Id)},handleSelectionChange:function(e){this.multipleSelection=e},getCheckedData:function(){this.groupData(),this.$emit("update:users",this.selectUsers),this.$emit("handleUpdate",1)},rowClick:function(e){},setSelectTable:function(){var e=this;this.multipleSelection.forEach((function(t){e.$refs.multipleTable.toggleRowSelection(t)}))}}}},bcc6:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n=a("09f4");t.default={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e,type:"limit"}),this.autoScroll&&(0,n.scrollTo)(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize,type:"page"}),this.autoScroll&&(0,n.scrollTo)(0,800)}}}},bf95:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-tooltip",{attrs:{disabled:e.isDisabled(),content:"点击关闭 tooltip 功能"}},[e.contentHtml?a("div",{attrs:{slot:"content"},slot:"content"},[a("div",{domProps:{innerHTML:e._s(e.contentHtml)}})]):e._e(),"start round mix"==e.node.type?a("div",{staticClass:"common-circle-node node-start-bg node-start",class:{active:e.isActive()},style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("i",{class:"iconfont icon-StartOP"}),e._v(" "+e._s(e.node.name)+" ")]):"end round"==e.node.type?a("div",{staticClass:"common-circle-node node-end",class:{active:e.isActive()},style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("i",{class:"iconfont icon-End"}),e._v(" "+e._s(e.node.name)+" ")]):"node"===e.node.type?a("div",{staticClass:"common-rectangle-node flex-row",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[e.node.addInfo?a("el-icon",{class:e.tagStatusClass(e.node.addInfo)}):a("el-icon",{class:e.node.icon||e.node.defaultIcon||"el-icon-s-tools"}),a("span",{staticClass:"flex-item"},[e._v(e._s(e.node.name))])],1):"fork"===e.node.type?a("div",{staticClass:"common-rectangle-node flex-row",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("el-icon",{class:e.node.icon||e.node.defaultIcon||"iconfont icon-fork"}),a("span",{staticClass:"flex-item"},[e._v(e._s(e.node.name))])],1):"join"===e.node.type?a("div",{staticClass:"common-rectangle-node flex-row",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("el-icon",{class:e.node.icon||e.node.defaultIcon||"iconfont icon-gaibanxianxingtubiao-"}),a("span",{staticClass:"flex-item"},[e._v(e._s(e.node.name))])],1):"common"==e.node.type?a("div",{staticClass:"common-rectangle-node flex-row",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("el-icon",{class:e.node.icon||e.node.defaultIcon}),a("span",{staticClass:"flex-item"},[e._v(e._s(e.node.name))])],1):"freedom"==e.node.type?a("div",{staticClass:"common-rectangle-node",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("i",{staticClass:"el-icon-refresh"}),e._v(" "+e._s(e.node.name)+" ")]):"event"==e.node.type?a("div",{staticClass:"common-circle-node",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[e._v(" "+e._s(e.node.name)+" ")]):"gateway"==e.node.type?a("div",{staticClass:"common-diamond-node",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}}):"child-flow"==e.node.type?a("div",{staticClass:"common-rectangle-node",class:e.stateClass(),style:{top:e.node.top+"px",left:e.node.left+"px",cursor:"drag"==e.currentTool.type?"move":"connection"==e.currentTool.type?"crosshair":"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("a-icon",{staticClass:"node-icon",attrs:{type:"api"}}),e._v(" "+e._s(e.node.name)+" ")],1):"x-lane"==e.node.type?a("vdr",{staticClass:"common-x-lane-node",class:{laneActive:e.isActive()},style:{top:e.node.top+"px",left:e.node.left+"px",height:e.node.height+"px",width:e.node.width+"px",cursor:"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id,h:e.node.height,parent:!0,w:e.node.width},on:{resizing:e.onResize}},[a("div",{staticClass:"lane-text-div",style:{top:0,left:0,cursor:"drag"==e.currentTool.type?"move":"default"},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("span",{staticClass:"lane-text"},[e._v(e._s(e.node.name))])])]):"y-lane"==e.node.type?a("vdr",{staticClass:"common-y-lane-node",class:{laneActive:e.isActive()},style:{top:e.node.top+"px",left:e.node.left+"px",height:e.node.height+"px",width:e.node.width+"px",cursor:"zoom-in"==e.currentTool.type?"zoom-in":"zoom-out"==e.currentTool.type?"zoom-out":"default"},attrs:{id:e.node.id,h:e.node.height,w:e.node.width},on:{resizing:e.onResize}},[a("div",{staticClass:"lane-text-div",style:{cursor:"drag"==e.currentTool.type?"move":"default"},on:{click:function(t){return t.stopPropagation(),e.selectNode(t)},contextmenu:function(t){return t.stopPropagation(),e.showNodeContextMenu(t)}}},[a("span",{staticClass:"lane-text"},[e._v(e._s(e.node.name))])])]):a("div")],1)},r=[]},c120:function(e,t,a){"use strict";a.r(t);var n=a("16fe"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},c13a:function(e,t,a){"use strict";a.r(t);var n=a("eb18"),r=a("f7bf");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},c145:function(e,t,a){},c1e6:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"c-upload-container"},[a("el-upload",{ref:"upload",attrs:{drag:"",limit:e.limit,accept:e.accept,"file-list":e.fileList,"before-upload":e.importData,"auto-upload":e.autoUpload,"on-change":e.handleChange,"on-exceed":e.handleExceed,action:"string"}},[a("svg-icon",{attrs:{"icon-class":"upload-icon","class-name":"cs--icon-upload"}}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("支持格式： "+e._s(e.accept)+"，"+e._s(e.tip))])])],1)],1)},r=[]},c295:function(e,t,a){"use strict";a.r(t);var n=a("f349"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},c330:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b0c0");var r=n(a("401f"));r.default.install=function(e){e.component(r.default.name,r.default)};t.default=r.default},c643:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");t.default={name:"ExpandableSection",props:{width:{type:[Number,String],default:"300px"},value:{type:Boolean,default:!0},toolTipDisabled:{type:Boolean,default:!1}},computed:{isExpanded:{get:function(){return this.value},set:function(e){this.$emit("input",e)}},computedWidth:function(){return"number"===typeof this.width?"".concat(this.width,"px"):this.width}},methods:{toggleContent:function(){this.isExpanded=!this.isExpanded}}}},c670:function(e,t,a){},c7ad:function(e,t,a){},c7f0:function(e,t,a){"use strict";a.r(t);var n=a("ee2b"),r=a("f35d");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"8c64a652",null);t["default"]=s.exports},c95f:function(e,t,a){"use strict";a.r(t);var n=a("bb48"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},c9d8:function(e,t,a){"use strict";a.r(t);var n=a("926d0"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},cc03:function(e,t,a){"use strict";var n=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9");var r=a("472f"),i=n(a("c1df"));t.default={name:"PlanHistory",props:{plan:{type:Object,default:function(){return{}}},begin_date:{type:String,default:""},end_date:{type:String,default:""}},data:function(){return{histories:[]}},created:function(){var e=this;(0,r.GetPlanUpdateList)(this.plan.Id).then((function(t){t.IsSucceed&&(e.histories=t.Data)}))},methods:{moment:function(e){return i(e)},view:function(e){this.$router.push({path:"/plan/history/".concat(null===e||void 0===e?void 0:e.Id)})}}}},cf1f:function(e,t,a){"use strict";a("f6cf")},cf7c:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");t.default={props:{autoUpload:{type:Boolean,default:!1},limit:{type:Number,default:1},accept:{type:String,default:".xls, .xlsx"},tip:{type:String,default:"最大5M"},fileList:{type:Array,default:function(){return[]}},beforeUpload:Function,onChange:Function},data:function(){return{loading:!1}},methods:{importData:function(e){return this.beforeUpload&&this.beforeUpload(e),!1},handleSubmit:function(){this.$refs.upload.submit()},handleChange:function(e,t){this.onChange&&this.onChange(e,t)},handleExceed:function(){this.$message({type:"warning",message:"文件数量超过数量限制"})}}}},cfd3:function(e,t,a){"use strict";var n=a("4ea4").default,r=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("a15b"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("159b");var i=r(a("32fd")),o=n(a("8215")),s=r(a("c1df"));t.default={name:"PlanAddDialog",components:{TypeTreeItem:o.default},props:{plan:{type:Object,default:function(){return null}},typetree:{type:Array,default:function(){return[]}},members:{type:Array,default:function(){return[]}},editMode:{type:Boolean,default:!0},type:{type:String,default:""}},data:function(){var e=this;return{visible:!1,form:{Type_Id:"",Code:"",Id:"",Name:"",Resources:1,Plan_Oauth:"0",Plan_Start_Date:null,Plan_End_Date:null,Plan_Duration:0,Admin:[],Observer:[],Remark:""},editmode:!1,rules:{Type_Id:[{required:!0,message:"选择计划类别",trigger:"change"}],Code:[{required:!0,message:"请填写编号",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],Name:[{required:!0,message:"请填写名称",trigger:"blur"},{min:3,max:50,message:"长度在 3 到 50 个字符",trigger:"blur"}],Plan_Start_Date:[{type:"date",required:!0,message:"请选择计划开始时间",trigger:"change"}],Plan_End_Date:[{type:"date",required:!0,message:"请选择计划完成时间",trigger:"change"},{validator:function(t,a,n){if(a<e.form.Plan_Start_Date)return n("完成时间不能早于开始时间");n()},trigger:"change"}],Admin:[{type:"array",required:!0,message:"选择所有者",trigger:"change"}],Resources:[{required:!0,message:"请选择活动资源",trigger:"change"}]}}},computed:{role:function(){var e;return new i.PlanAuth(null===(e=this.plan)||void 0===e?void 0:e.Plan_Auth)}},watch:{"form.Plan_Start_Date":function(e,t){e!==t&&i.calcPlanDurations(this.form)},"form.Plan_End_Date":function(e,t){e!==t&&i.calcPlanDurations(this.form)}},created:function(){var e=this;this.editmode=this.editMode,this.plan?(this.editmode&&!this.canEdit()&&(this.editmode=!1),Object.keys(this.form).forEach((function(t){void 0!==e.plan[t]&&(e.form[t]=e.plan[t],"Resources"!==t&&"Plan_Duration"!==t||(e.form[t]=Number(e.form[t])))}))):(this.form=i.createEmptyPlan(),this.form.Plan_Oauth="1",Object.keys(this.form).forEach((function(t){"Resources"!==t&&"Plan_Duration"!==t||(e.form[t]=Number(e.form[t]))})),this.form.Plan_Auth="负责",this.form.Plan_Data={data:[],links:[]},this.form.Type_Id=this.type||"0"),this.form.Admin||(this.form.Admin=this.$store.state.user.userId?[this.$store.state.user.userId]:[]),this.form.Plan_Start_Date||(this.form.Plan_Start_Date=s(new Date).startOf("date").toDate())},methods:{onSubmit:function(){var e=this;if(!this.editmode)return this.$emit("dialogCancel");this.$refs["form"].validate((function(t){if(!t)return!1;e.$emit("dialogFormSubmitSuccess",{type:"setPlanBase",data:e.form})}))},handleSelectType:function(e,t){this.form.Type_Id=e,this.visible=!1},getPlanType:function(e,t){var a,n=t.find((function(t){return t.Id===e}));if(n)a=n;else for(var r=0;r<t.length;r++)if(t[r].Children&&t[r].Children.length>0&&(n=this.getPlanType(e,t[r].Children),n&&"{}"!==JSON.stringify(n))){a=n;break}return null!==a&&void 0!==a?a:{}},toDateStr:function(e){return e?s(e).startOf("date").format("YYYY-MM-DD"):""},getUsers:function(e){var t=this.members.filter((function(t){return e.indexOf(t.User_Id)>-1}));return t.map((function(e){return e.UserName})).join(",")},canEdit:function(){return!!this.role.check(i.PlanAuth.ACCESSES.WBS|i.PlanAuth.ACCESSES.WBS)}}}},d0d7:function(e,t,a){"use strict";a.r(t);var n=a("36af"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},d0fe:function(e,t,a){},d1a0:function(e,t,a){"use strict";a("d38f")},d2db:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"TypeTreeItem",props:{data:{type:Array,default:function(){return[]}}},created:function(){}}},d331:function(e,t,a){"use strict";a.r(t);var n=a("bcc6"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},d34e:function(e,t,a){"use strict";a.r(t);var n=a("24286"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},d38f:function(e,t,a){},d48d:function(e,t,a){"use strict";a("9b4c1")},d50e:function(e,t,a){"use strict";a.r(t);var n=a("6971"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},d68c:function(e,t,a){"use strict";var n=a("dbce").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c1df"));t.default={name:"UpdateSet",props:{plan:{type:Object,default:function(){return{}}},editMode:{type:Boolean,default:!1}},data:function(){return{form:{},editmode:!1}},created:function(){this.editmode=this.editMode},methods:{submit:function(){if(!this.editmode)return this.$emit("dialogCancel");this.$emit("dialogFormSubmitSuccess",{type:"setUpdatePlan",data:this.form})},moment:function(e){return r(e)}}}},d89c:function(e,t,a){"use strict";a.r(t);var n=a("1f58"),r=a("d34e");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("b309");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"6b34f160",null);t["default"]=s.exports},d9818:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"-16px"}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"过滤条件"}},[a("el-checkbox-group",{staticStyle:{display:"flex","flex-direction":"column"},model:{value:e.form.checkList,callback:function(t){e.$set(e.form,"checkList",t)},expression:"form.checkList"}},[a("el-checkbox",{attrs:{label:"task"},on:{change:function(t){return e.changeCheckList("task",t)}}},[e._v("一般作业")]),a("el-checkbox",{attrs:{label:"milestone"},on:{change:function(t){return e.changeCheckList("milestone",t)}}},[e._v("里程碑作业")]),a("el-checkbox",{attrs:{label:"project"},on:{change:function(t){return e.changeCheckList("project",t)}}},[e._v("WBS")]),a("el-checkbox",{attrs:{label:"critical"},on:{change:function(t){return e.changeCheckList("critical",t)}}},[e._v("关键作业")]),e._e(),a("el-checkbox",{attrs:{label:"threeweeks"},on:{change:function(t){return e.changeCheckList("threeweeks",t)}}},[e._v("三周滚动时间")]),a("el-checkbox",{attrs:{label:"daterange"},on:{change:function(t){return e.changeCheckList("daterange",t)}}},[e._v("时间周期")]),e.form.checkList.indexOf("daterange")>-1?a("div",{staticStyle:{background:"#EEE",padding:"12px","border-radius":"4px","margin-top":"10px","padding-left":"20px"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.dates,callback:function(t){e.$set(e.form,"dates",t)},expression:"form.dates"}})],1):e._e()],1)],1),a("el-form-item",{attrs:{label:"匹配方式"}},[a("el-radio-group",{staticStyle:{display:"flex","flex-direction":"column"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[a("el-radio",{staticStyle:{margin:"8px 0 16px 0"},attrs:{label:"ONE"}},[e._v("满足任意一条选择的条件（并集）")]),a("el-radio",{attrs:{label:"ALL"}},[e._v("满足所有选择的条件（交集）")])],1)],1),a("el-form-item",{attrs:{align:"right"}},[a("el-button",{on:{click:function(t){return e.$emit("dialogCancel")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确定")])],1)],1)],1)},r=[]},d9e9:function(e,t,a){"use strict";a.r(t);var n=a("e0ed"),r=a("e545");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},da20:function(e,t,a){"use strict";a.r(t);var n=a("3df3"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},db05:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("d3b7");var r=n(a("c14f")),i=n(a("1da1")),o=a("cf45"),s=a("ed08");t.default={name:"SelectDict",props:{value:{type:String,default:""},code:{type:String,required:!0},valueField:{type:String,default:"Value"}},data:function(){return{list:[],selectedValue:""}},watch:{value:function(){this.selectedValue=Array.isArray(this.value)?(0,s.deepClone)(this.value):this.value}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:(0,o.getDictionary)(e.code).then((function(t){e.list=t.filter((function(e){return e.Is_Enabled}))}));case 1:return t.a(2)}}),t)})))()}}}},dc01:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b0c0"),a("a9e3"),a("ac1f"),a("00b4");t.default={props:{autoUpload:{type:Boolean,default:!1},beforeUpload:Function,limits:{type:Number,default:999},formData:{type:Object,default:function(){}},name:{type:String,default:"files"},list:{type:Array,default:function(){return[]}},showLimit:{type:Boolean,default:!1},showList:{type:Boolean,default:!0},accept:{type:String,default:".xls, .xlsx"},canPreview:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},action:{type:String,default:"string"},btnType:{type:String,default:"text"},btnIcon:{type:String,default:""},btnSize:{type:String,default:"small"},btnText:{type:String,default:"上传文件"},fileList:{type:Array,default:function(){return[]}}},data:function(){return{loading:!1}},computed:{btnLabel:function(){return this.loading?"上传中":this.btnText}},methods:{importData:function(e){return this.loading=!0,this.beforeUpload(e),this.setLoadingFalse(),!1},handleSubmit:function(){this.$refs.upload.submit()},handleRemove:function(e,t){e&&"success"===e.status&&this.$emit("handleRemove",e,t)},beforeRemove:function(e,t){this.$emit("beforeRemove",e,t)},onSuccess:function(){this.setLoadingFalse()},handleExceed:function(){},handlePreview:function(e){this.$emit("handlePreview",e)},setLoadingFalse:function(){this.loading=!1},isExcel:function(e){return/\.(xlsx|xls|csv)$/.test(e.name)}}}},de99:function(e,t,a){"use strict";a.r(t);var n=a("ff4c"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},def8:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.flowConfig=void 0;t.flowConfig={jsPlumbInsConfig:{Connector:["Flowchart",{gap:5,cornerRadius:8,alwaysRespectStubs:!0}],ConnectionOverlays:[["Arrow",{width:10,length:10,location:1}]],PaintStyle:{stroke:"#2a2929",strokeWidth:2},HoverPaintStyle:{stroke:"#409EFF",strokeWidth:3},EndpointStyle:{fill:"#456",stroke:"#2a2929",strokeWidth:1,radius:3},EndpointHoverStyle:{fill:"pink"}},jsPlumbConfig:{anchor:{default:["Bottom","Right","Top","Left"]},conn:{isDetachable:!1},makeSourceConfig:{filter:"a",filterExclude:!0,maxConnections:-1,endpoint:["Dot",{radius:7}],anchor:["Bottom","Right","Top","Left"]},makeTargetConfig:{filter:"a",filterExclude:!0,maxConnections:-1,endpoint:["Dot",{radius:7}],anchor:["Bottom","Right","Top","Left"]}},defaultStyle:{dragOpacity:.7,alignGridPX:[5,5],alignSpacing:{level:100,vertical:100},alignDuration:300,containerScale:{init:1,min:.5,max:3,onceNarrow:.1,onceEnlarge:.1},isOpenAuxiliaryLine:!0,showAuxiliaryLineDistance:20,movePx:5,photoBlankDistance:200},idType:"uuid",flowStatus:{CREATE:"0",SAVE:"1",MODIFY:"2",LOADING:"3"},shortcut:{multiple:{code:17,codeName:"CTRL",shortcutName:"多选"},dragContainer:{code:32,codeName:"SPACE",shortcutName:"画布拖拽"},scaleContainer:{code:18,codeName:"ALT(firefox下为SHIFT)",shortcutName:"画布缩放"},dragTool:{code:68,codeName:"D",shortcutName:"拖拽工具"},connTool:{code:76,codeName:"L",shortcutName:"连线工具"},zoomInTool:{code:190,codeName:"<",shortcutName:"放大工具"},zoomOutTool:{code:188,codeName:">",shortcutName:"缩小工具"},leftMove:{code:37,codeName:"←",shortcutName:"左移"},upMove:{code:38,codeName:"↑",shortcutName:"上移"},rightMove:{code:39,codeName:"→",shortcutName:"右移"},downMove:{code:40,codeName:"↓",shortcutName:"下移"},settingModal:{code:83,codeName:"CTRL+ALT+S",shortcutName:"打开设置页面"},testModal:{code:84,codeName:"CTRL+ALT+T",shortcutName:"打开测试页面"}},contextMenu:{container:{menuName:"flow-menu",axis:{left:null,top:null},menulists:[{fnHandler:"paste",icoName:"edit",btnName:"粘贴"},{fnHandler:"selectAll",icoName:"edit",btnName:"全选"},{icoName:"edit",btnName:"对齐方式",children:[{icoName:"edit",fnHandler:"verticaLeft",btnName:"垂直左对齐"},{icoName:"edit",fnHandler:"verticalCenter",btnName:"垂直居中"},{icoName:"edit",fnHandler:"verticalRight",btnName:"垂直右对齐"},{icoName:"edit",fnHandler:"levelUp",btnName:"水平上对齐"},{icoName:"edit",fnHandler:"levelCenter",btnName:"水平居中"},{icoName:"edit",fnHandler:"levelDown",btnName:"水平下对齐"}]}]},node:{menuName:"node-menu",axis:{left:null,top:null},menulists:[{fnHandler:"copyNode",icoName:"edit",btnName:"复制节点"},{fnHandler:"deleteNode",icoName:"edit",btnName:"删除节点"}]},sl:{menuName:"link-menu",axis:{left:null,top:null},menulists:[{fnHandler:"deleteLink",icoName:"edit",btnName:"删除连线"}]}}}},e0ed:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:""},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.FullName,value:e.Id}})})),1)},r=[]},e0f6:function(e,t,a){"use strict";a("af7c")},e100:function(e,t,a){"use strict";a.r(t);var n=a("83b8e"),r=a("7c48");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("90bd");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"78e7e55a",null);t["default"]=s.exports},e242:function(e,t,a){"use strict";a.r(t);var n=a("e8213"),r=a("9e22");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"2d3fd775",null);t["default"]=s.exports},e37f:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9c4"),a("b64b");var r=n(a("0b09")),i=n(a("65b1")),o=a("1099");t.default={components:{CreatedFlow:r.default,bimdialog:i.default},data:function(){return{dialogVisible:!1,isadd:!1,title:"",moduleType:"FlowModuleNote",histories:[],schemeContent:"",formData:{}}},methods:{opendialog:function(e,t){this.dialogVisible=!0,this.rowId=e,this.isadd=t,this.getHistoryOptions(),this.getFlowInstances()},getHistoryOptions:function(){var e=this;(0,o.QueryHistories)({FlowInstanceId:this.rowId||""}).then((function(t){t.IsSucceed&&(e.histories=t.Data)}))},getFlowInstances:function(){var e=this;this.isadd?(0,o.FlowSchemesGet)({id:this.rowId}).then((function(t){e.schemeContent=JSON.parse(JSON.stringify(t.Data.Scheme_Content))})):(0,o.FlowInstancesGet)({Id:this.rowId}).then((function(t){e.schemeContent=JSON.parse(JSON.stringify(t.Data.Scheme_Content)),e.formData=JSON.parse(t.Data.Frm_Data),e.title=t.Data.Custom_Name}))},handleSubmit:function(){},handleClose:function(){this.dialogVisible=!1}}}},e3b0:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var r=n(a("8fea")),i=a("ed08");t.default={name:"SelectDepartment",props:{value:{type:[Array,Number,String],default:function(){return""}},type:{type:String,default:"RawInStoreType"},multiple:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{list:[],selectedValue:Array.isArray(this.value)?(0,i.deepClone)(this.value):this.value}},watch:{type:{handler:function(e){this.list=r.default[e]},immediate:!0}},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)}}}},e545:function(e,t,a){"use strict";a.r(t);var n=a("0cf3"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},e59e:function(e,t){e.exports="https://integ-plat-produce-test.bimtk.com/static/img/shang.ae888606.png"},e6ad:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("fb6a"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("a732"),a("d3b7"),a("ac1f"),a("00b4"),a("5319"),a("498a");t.default={name:"ElUploadDrag",props:{disabled:Boolean},inject:{uploader:{default:""}},data:function(){return{dragover:!1}},methods:{onDragover:function(){this.disabled||(this.dragover=!0)},onDrop:function(e){if(!this.disabled&&this.uploader){var t=this.uploader.accept;this.dragover=!1,t?this.$emit("file",[].slice.call(e.dataTransfer.files).filter((function(e){var a=e.type,n=e.name,r=n.indexOf(".")>-1?".".concat(n.split(".").pop()):"",i=a.replace(/\/.*$/,"");return t.split(",").map((function(e){return e.trim()})).filter((function(e){return e})).some((function(e){return/\..+$/.test(e)?r===e:/\/\*$/.test(e)?i===e.replace(/\/\*$/,""):!!/^[^\/]+\/[^\/]+$/.test(e)&&a===e}))}))):this.$emit("file",e.dataTransfer.files)}}}}},e6d3:function(e,t,a){"use strict";a.r(t);var n=a("cf7c"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},e720:function(e,t,a){"use strict";a.r(t);var n=a("261d"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},e7cc:function(e,t,a){"use strict";a.r(t);var n=a("3e51"),r=a("d0d7");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},e7ed:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dep-container"},[a("el-card",{staticClass:"box-card dep-left",attrs:{shadow:"none"}},[a("tree-detail",{ref:"tree",attrs:{"expand-on-click-node":!1,"expanded-key":e.expandedKey,loading:e.treeLoading,"tree-data":e.treeData,icon:"icon-users","same-icon":"","show-detail":""},on:{handleNodeClick:e.handleNodeClick}})],1),a("div",{staticClass:"dep-right",staticStyle:{height:"100%",overflow:"auto"}},[a("div",{staticStyle:{height:"100%",overflow:"auto"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"100%"},on:{"row-click":e.rowClick,select:e.handleSelectionUser,"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"55"}}),a("el-table-column",{attrs:{align:"center",label:"账户名",prop:"Login_Account",width:"120"}}),a("el-table-column",{attrs:{align:"center",label:"姓名",prop:"Display_Name",width:"120"}}),a("el-table-column",{attrs:{align:"center",label:"手机号",prop:"Mobile"}}),a("el-table-column",{attrs:{align:"center",label:"邮箱",prop:"Email"}}),a("el-table-column",{attrs:{align:"center",label:"部门",prop:"DepartmentName",width:"120"}}),a("el-table-column",{attrs:{align:"center",label:"状态",prop:"UserStatusName",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!==t.row.Lock_Reason?a("el-tooltip",{staticClass:"item",attrs:{content:t.row.Lock_Reason,effect:"dark",placement:"top"}},[a("span",{staticStyle:{cursor:"pointer"},style:"停用"===t.row.UserStatusName?"color:#FB6B7F":"锁定"===t.row.UserStatusName?"color:#F5C15A":""},[e._v(e._s(t.row.UserStatusName))])]):a("span",{staticStyle:{cursor:"pointer"},style:"停用"===t.row.UserStatusName?"color:#FB6B7F":"锁定"===t.row.UserStatusName?"color:#F5C15A":""},[e._v(e._s(t.row.UserStatusName))])]}}])})],1)],1)])],1)},r=[]},e8213:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("tree-detail",{ref:"tree",attrs:{"expand-on-click-node":!1,"show-checkbox":"","check-strictly":"",icon:"icon-users",loading:e.treeLoading,"tree-data":e.treeData,"default-checked-keys":e.defaultCheckedKeys},on:{getCheckedNodes:e.getCheckedNodes}})],1)},r=[]},e989:function(e,t,a){"use strict";a.r(t);var n=a("2e8b"),r=a("b3a3");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},ea05:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"task-detail"},[a("el-link",{staticClass:"closeme",attrs:{underline:!1,icon:"el-icon-close"},on:{click:e.closeMe}}),a("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":e.handleClick},model:{value:e.tabName,callback:function(t){e.tabName=t},expression:"tabName"}},[a("el-tab-pane",{attrs:{label:("project"===e.form.type?"WBS":"milestone"===e.form.type?"里程碑":"作业")+"状态",name:"profile"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:9}},[a("div",{staticClass:"col-block"},[a("div",{staticClass:"head"},[e._v("基本信息")]),a("el-form",{ref:"form1",staticStyle:{width:"100%"},attrs:{model:e.form,"label-width":"90px"}},[a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:("project"===e.form.type?"WBS":"milestone"===e.form.type?"里程碑":"作业")+"名称",size:"mini"}},[e.editMode&&e.canEditTask?a("el-input",{on:{change:function(t){return e.formChange("text",t)}},model:{value:e.form.text,callback:function(t){e.$set(e.form,"text",t)},expression:"form.text"}}):a("span",[e._v(" "+e._s(e.form.text)+" ")])],1)],1)],1),a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"责任人",size:"mini"}},[e.editMode&&e.canEditTask||e.editMode&&"1"!==e.plan.Status&&"2"!==e.plan.Status&&e.canEditWBS?a("el-select",{attrs:{placeholder:"请选择",filterable:""},on:{change:function(t){return e.formChange("Responsible_User",t)}},model:{value:e.form.Responsible_User,callback:function(t){e.$set(e.form,"Responsible_User",t)},expression:"form.Responsible_User"}},[a("el-option",{attrs:{label:"选择负责人",value:null}}),e._l(e.members,(function(e){return a("el-option",{key:e.User_Id,attrs:{label:e.UserName,value:e.User_Id}})}))],2):a("span",[e._v(" "+e._s(e.getUser(e.form.Responsible_User).UserName)+" ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"完成进度",size:"mini"}},[e.editMode&&"project"!==e.form.type&&e.canUpdateProgress&&"milestone"!==e.form.type?[a("el-input-number",{staticStyle:{width:"68px"},attrs:{controls:!1,precision:1,type:"number",min:0,max:100,disabled:!e.form.Actual_Start_Date||Boolean(e.form.Actual_End_Date)},on:{change:function(t){return e.formChange("tempProgress",t)}},model:{value:e.tempProgress,callback:function(t){e.tempProgress=t},expression:"tempProgress"}}),e._v(" % ")]:a("span",[e._v(e._s(e.tempProgress.toFixed(2))+"%")])],2)],1)],1),"task"===e.form.type||"milestone"===e.form.type?a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"作业类型",size:"mini"}},[e.editMode&&e.canEditTask?[a("el-radio",{attrs:{disabled:"",label:"project"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[e._v("WBS")]),a("el-radio",{attrs:{label:"task",disabled:Boolean(e.form.Actual_Start_Date)},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[e._v("作业")]),a("el-radio",{attrs:{label:"milestone",disabled:Boolean(e.form.Actual_Start_Date)},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[e._v("里程碑")])]:a("span",[e._v(" "+e._s("project"===e.form.type?"WBS":"milestone"===e.form.type?"里程碑":"作业")+" ")])],2)],1)],1):e._e(),a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},["project"===e.form.type?a("el-form-item",{attrs:{label:"运营计划开始","label-width":"100px",size:"mini"}},[e._v(" "+e._s(e.toDateStr(e.form.Control_Plan_Start_Date))+" ")]):e._e(),"task"===e.form.type||"milestone"===e.form.type?a("el-form-item",{attrs:{label:"限制条件",size:"mini"}},[e.editMode&&e.canEditTask?a("el-select",{attrs:{placeholder:"请选择",disabled:"milestone"===e.form.type},on:{change:function(t){return e.formChange("constraint_type",t)}},model:{value:e.form.constraint_type,callback:function(t){e.$set(e.form,"constraint_type",t)},expression:"form.constraint_type"}},e._l(e.constraints,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):a("span",[e._v(" "+e._s(e.getConstraintType(e.form.constraint_type).label)+" ")])],1):e._e()],1),a("el-col",{attrs:{span:12}},["project"===e.form.type?a("el-form-item",{attrs:{label:"运营计划结束","label-width":"100px",size:"mini"}},[e._v(" "+e._s(e.toDateStr(e.form.Control_Plan_End_Date))+" ")]):e._e(),"task"===e.form.type||"milestone"===e.form.type?a("el-form-item",{attrs:{label:"限制日期",size:"mini"}},[e.editMode&&e.canEditTask?a("el-date-picker",{staticClass:"simon-date-picker",staticStyle:{width:"100%"},attrs:{disabled:["asap","alap"].indexOf(e.form.constraint_type)>-1||"milestone"===e.form.type,type:"date",placeholder:"选择日期"},on:{change:function(t){return e.formChange("constraint_date",t)}},model:{value:e.form.constraint_date,callback:function(t){e.$set(e.form,"constraint_date",t)},expression:"form.constraint_date"}}):a("span",[e._v(" "+e._s(e.toDateStr(e.form.constraint_date))+" ")])],1):e._e()],1)],1),"project"===e.form.type?a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划开始",size:"mini"}},[e._v(" "+e._s(e.toDateStr(e.form.Plan_Start_Date))+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划结束",size:"mini"}},[e._v(" "+e._s(e.toDateStr(e.form.Plan_End_Date))+" ")])],1)],1):e._e()],1)],1)]),a("el-col",{attrs:{span:10}},["project"===e.form.type?a("div",{staticClass:"col-block"},[a("el-form",{ref:"form2",staticStyle:{width:"100%"},attrs:{model:e.form,"label-width":"90px"}},[a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际开始",size:"mini"}},[a("span",[e._v(" "+e._s(e.toDateStr(e.form.Actual_Start_Date))+" ")])])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际结束",size:"mini"}},[a("span",[e._v(" "+e._s(e.toDateStr(e.form.Actual_End_Date))+" ")])])],1)],1),a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"存在问题",size:"mini"}},[e.editMode&&e.canUpdateProgress?a("el-input",{attrs:{type:"textarea",rows:"2"},on:{change:function(t){return e.formChange("Existing_Problems",t)}},model:{value:e.form.Existing_Problems,callback:function(t){e.$set(e.form,"Existing_Problems",t)},expression:"form.Existing_Problems"}}):a("span",[e._v(" "+e._s(e.form.Existing_Problems)+" ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"解决措施",size:"mini"}},[e.editMode&&e.canUpdateProgress?a("el-input",{attrs:{type:"textarea",rows:"2"},on:{change:function(t){return e.formChange("Solutions",t)}},model:{value:e.form.Solutions,callback:function(t){e.$set(e.form,"Solutions",t)},expression:"form.Solutions"}}):a("span",[e._v(" "+e._s(e.form.Solutions)+" ")])],1)],1)],1),a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协调事项",size:"mini"}},[e.editMode&&e.canUpdateProgress?a("el-input",{attrs:{type:"textarea",rows:"2"},on:{change:function(t){return e.formChange("Need_Coordinate",t)}},model:{value:e.form.Need_Coordinate,callback:function(t){e.$set(e.form,"Need_Coordinate",t)},expression:"form.Need_Coordinate"}}):a("span",[e._v(" "+e._s(e.form.Need_Coordinate)+" ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协调部门",size:"mini"}},[e.editMode&&e.canUpdateProgress?a("el-input",{attrs:{type:"textarea",rows:"2"},on:{change:function(t){return e.formChange("Coordinate_Department",t)}},model:{value:e.form.Coordinate_Department,callback:function(t){e.$set(e.form,"Coordinate_Department",t)},expression:"form.Coordinate_Department"}}):a("span",[e._v(" "+e._s(e.form.Coordinate_Department)+" ")])],1)],1)],1)],1)],1):e._e(),"task"===e.form.type||"milestone"===e.form.type?a("div",{staticClass:"col-block"},[a("div",{staticClass:"head"},[e._v("时间参数")]),a("el-form",{ref:"form2",staticStyle:{width:"100%"},attrs:{model:e.form,"label-width":"90px"}},[a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划开始",size:"mini"}},[e.editMode&&e.canEditTask&&"milestone"!==e.form.type?a("el-date-picker",{staticClass:"simon-date-picker",staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},on:{change:function(t){return e.formChange("Plan_Start_Date",t)}},model:{value:e.form.Plan_Start_Date,callback:function(t){e.$set(e.form,"Plan_Start_Date",t)},expression:"form.Plan_Start_Date"}}):a("span",[e._v(" "+e._s("milestone"===e.form.type?"-":e.toDateStr(e.form.Plan_Start_Date))+" ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际开始",size:"mini"}},[e.editMode&&e.canUpdateProgress&&"milestone"!==e.form.type?a("el-date-picker",{staticClass:"simon-date-picker",staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},on:{change:function(t){return e.formChange("Actual_Start_Date",t)}},model:{value:e.form.Actual_Start_Date,callback:function(t){e.$set(e.form,"Actual_Start_Date",t)},expression:"form.Actual_Start_Date"}}):a("span",[e._v(" "+e._s(e.toDateStr(e.form.Actual_Start_Date))+" ")])],1)],1)],1),a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划完成",size:"mini"}},[e.editMode&&e.canEditTask?a("el-date-picker",{staticClass:"simon-date-picker",staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期"},on:{change:function(t){return e.formChange("Plan_End_Date",t)}},model:{value:e.form.Plan_End_Date,callback:function(t){e.$set(e.form,"Plan_End_Date",t)},expression:"form.Plan_End_Date"}}):a("span",[e._v(" "+e._s(e.toDateStr(e.form.Plan_End_Date))+" ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际完成",size:"mini"}},[e.editMode&&e.canUpdateProgress?a("el-date-picker",{staticClass:"simon-date-picker",staticStyle:{width:"100%"},attrs:{type:"date",disabled:!e.form.Actual_Start_Date&&"milestone"!==e.form.type,placeholder:"选择日期"},on:{change:function(t){return e.formChange("Actual_End_Date",t)}},model:{value:e.form.Actual_End_Date,callback:function(t){e.$set(e.form,"Actual_End_Date",t)},expression:"form.Actual_End_Date"}}):a("span",[e._v(" "+e._s(e.toDateStr(e.form.Actual_End_Date))+" ")])],1)],1)],1),a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划工期",size:"mini"}},[e.editMode&&e.canEditTask?a("el-input",{attrs:{type:"number",min:"0",disabled:"milestone"===e.form.type},on:{change:function(t){return e.formChange("Plan_Duration",t)}},model:{value:e.form.Plan_Duration,callback:function(t){e.$set(e.form,"Plan_Duration",t)},expression:"form.Plan_Duration"}}):a("span",[e._v(" "+e._s(e.form.Plan_Duration)+"(天) ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际工期",size:"mini"}},[a("span",[e._v(e._s(e.form.Actual_Duration)+"(天)")])])],1)],1),a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划人工",size:"mini"}},[e.editMode&&e.canEditTask?a("el-input",{attrs:{type:"number",min:"0",disabled:"milestone"===e.form.type},on:{change:function(t){return e.formChange("Plan_Resources",t)}},model:{value:e.form.Plan_Resources,callback:function(t){e.$set(e.form,"Plan_Resources",t)},expression:"form.Plan_Resources"}}):a("span",[e._v(" "+e._s(e.form.Plan_Resources)+" ")])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际人工",size:"mini"}},[e.editMode&&e.canUpdateProgress&&"milestone"!==e.form.type?a("el-input",{attrs:{type:"number",min:"0",disabled:"milestone"===e.form.type},on:{change:function(t){return e.formChange("Actual_Resources",t)}},model:{value:e.form.Actual_Resources,callback:function(t){e.$set(e.form,"Actual_Resources",t)},expression:"form.Actual_Resources"}}):a("span",[e._v(" "+e._s(e.form.Actual_Resources)+" ")])],1)],1)],1)],1)],1):e._e()]),a("el-col",{attrs:{span:5}},[a("div",{staticClass:"col-block"},[a("div",{staticClass:"head"},[e._v(" "+e._s(("project"===e.form.type?"wbs":"milestone"===e.form.type?"里程碑":"作业")+"备注")+" ")]),e.editMode&&e.canUpdateProgress?a("el-input",{staticStyle:{"margin-left":"20px",height:"100%"},attrs:{type:"textarea"},on:{change:function(t){return e.formChange("Remark",t)}},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}}):a("span",[e._v(" "+e._s(e.form.Remark)+" ")])],1)])],1)],1),"task"===e.form.type?a("el-tab-pane",{attrs:{label:"逻辑关系",name:"relation"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"col-block"},[a("div",{staticClass:"head"},[e._v("前置作业")]),a("div",{staticClass:"rlist"},[a("el-table",{attrs:{height:"190",stripe:"",data:e.preTasks,"cell-style":{border:"none"}}},[a("el-table-column",{attrs:{label:"作业",prop:"task",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a["task"].text)+" ")]}}],null,!1,4204009641)}),a("el-table-column",{attrs:{label:"关系",prop:"link",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e.getLinkType(a["link"].type).label)+" ")]}}],null,!1,2351293446)}),a("el-table-column",{attrs:{label:"延迟(天)",prop:"link",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.editMode&&e.canEditTask&&!Boolean(e.form.Actual_Start_Date)?[a("el-input",{attrs:{size:"mini",type:"number",placeholder:""},on:{change:function(t){return e.updateLagDays(n["link"],t)}},model:{value:n["link"].lag,callback:function(t){e.$set(n["link"],"lag",t)},expression:"row['link'].lag"}})]:[e._v(" "+e._s(n["link"].lag)+" ")]]}}],null,!1,3131213577)})],1)],1),e._e()])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"col-block"},[a("div",{staticClass:"head"},[e._v("后置作业")]),a("div",{staticClass:"rlist"},[a("el-table",{attrs:{height:"190",stripe:"",data:e.pastTasks,"cell-style":{border:"none"}}},[a("el-table-column",{attrs:{label:"作业",prop:"task",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a["task"].text)+" ")]}}],null,!1,4204009641)}),a("el-table-column",{attrs:{label:"关系",prop:"link",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e.getLinkType(a["link"].type).label)+" ")]}}],null,!1,2351293446)}),a("el-table-column",{attrs:{label:"延迟(天)",prop:"link",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.editMode&&e.canEditTask&&!Boolean(e.form.Actual_Start_Date)?[a("el-input",{attrs:{size:"mini",type:"number",placeholder:""},on:{change:function(t){return e.updateLagDays(n["link"],t)}},model:{value:n["link"].lag,callback:function(t){e.$set(n["link"],"lag",t)},expression:"row['link'].lag"}})]:[e._v(" "+e._s(n["link"].lag)+" ")]]}}],null,!1,3131213577)})],1)],1),e._e()])])],1)],1):e._e(),"task"===e.form.type?a("el-tab-pane",{attrs:{label:"填报历史",name:"history"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"col-block"},[a("div",{staticClass:"head"},[e._v("填报历史")]),a("div",{staticClass:"rlist"},[a("el-table",{attrs:{height:"190",stripe:"",data:e.history,"cell-style":{border:"none"}}},[a("el-table-column",{attrs:{label:"填报进度",prop:"Actual_Progress",align:"center",width:150},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(e._s((100*Number(a["Actual_Progress"])).toFixed(2))+"%")]}}],null,!1,398441853)}),a("el-table-column",{attrs:{label:"填报人",prop:"Create_UserName",align:"center",width:160}}),a("el-table-column",{attrs:{label:"填报日期",prop:"Create_Date",align:"center",width:240},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(e._s(e.toDateStr(a["Create_Date"])))]}}],null,!1,3785054531)}),a("el-table-column",{attrs:{label:"备注",prop:"Remark",align:"center"}})],1)],1)])])],1)],1):e._e(),e.extend?a("el-tab-pane",{attrs:{label:"扩展属性",name:"extend"}},[a("el-form",{attrs:{"label-width":"100px"}},[a("div",{staticClass:"flex-form-item-wrapper"},[e._l(e.extendfields.filter((function(e){return"longtext"!==e.Type})),(function(t){return[a("el-form-item",{key:t.Code,attrs:{label:t.Label}},[e.editMode&&e.canEditTask?["text"===t.Type?a("el-input",{attrs:{autocomplete:"off"},on:{change:function(a){return e.formChange(t.Code,a)}},model:{value:e.form[t.Code],callback:function(a){e.$set(e.form,t.Code,a)},expression:"form[attr.Code]"}}):e._e(),"number"===t.Type?a("el-input",{attrs:{type:"number"},on:{change:function(a){return e.formChange(t.Code,a)}},model:{value:e.form[t.Code],callback:function(a){e.$set(e.form,t.Code,a)},expression:"form[attr.Code]"}}):e._e(),"date"===t.Type?a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},on:{change:function(a){return e.formChange(t.Code,a)}},model:{value:e.form[t.Code],callback:function(a){e.$set(e.form,t.Code,a)},expression:"form[attr.Code]"}}):e._e()]:a("span",[e._v(" "+e._s(e.form[t.Code])+" ")])],2)]}))],2),a("div",{staticClass:"flex-form-item-wrapper"},[e._l(e.extendfields.filter((function(e){return"longtext"==e.Type})),(function(t){return[a("el-form-item",{key:t.Code,attrs:{label:t.Label}},[e.editMode&&e.canEditTask?[a("el-input",{attrs:{type:"textarea",rows:2},on:{change:function(a){return e.formChange(t.Code,a)}},model:{value:e.form[t.Code],callback:function(a){e.$set(e.form,t.Code,a)},expression:"form[attr.Code]"}})]:a("span",[e._v(" "+e._s(e.form[t.Code])+" ")])],2)]}))],2)])],1):e._e()],1)],1)},r=[]},ea81:function(e,t,a){"use strict";a.r(t);var n=a("382d"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},ead9:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:""},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t[e.valueField]}})})),1)},r=[]},eafb:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("el-container",{staticClass:"container"},[e.isShowContent?e._e():a("el-aside",{staticClass:"select-area",attrs:{theme:"light",width:"300px"}},[a("el-row",[a("el-checkbox-button",{staticClass:"cs-tag",model:{value:e.tag.checked0,callback:function(t){e.$set(e.tag,"checked0",t)},expression:"tag.checked0"}},[e._v("工具")]),e.tag.checked0?a("div",{attrs:{align:"center"}},[a("el-button-group",e._l(e.field.tools,(function(t,n){return a("el-button",{key:n,staticStyle:{"font-size":"16px",width:"100px"},attrs:{icon:t.icon,type:e.currentTool.type==t.type?"primary":"default",size:"mini"},on:{click:function(a){return e.selectTool(t.type)}}})})),1)],1):e._e()],1),a("el-row",[a("el-checkbox-button",{staticClass:"cs-tag",model:{value:e.tag.checked1,callback:function(t){e.$set(e.tag,"checked1",t)},expression:"tag.checked1"}},[e._v("基础节点")]),e.tag.checked1?a("div",{attrs:{align:"center"}},[a("draggable",e._b({attrs:{list:e.field.commonNodes,move:e.handleMove,tag:"el-row"},on:{end:e.handleMoveEnd,start:e.handleMoveStart}},"draggable",{group:{name:"flow",pull:"clone",put:!1},sort:!1},!1),[e._l(e.field.commonNodes,(function(t,n){return[a("el-col",{key:n,attrs:{span:12}},[a("div",{staticClass:"node-item",attrs:{type:t.type,belongto:"commonNodes"}},[a("i",{class:t.icon,staticStyle:{"font-size":"16px"}}),e._v(" "+e._s(t.name)+" ")])])]}))],2)],1):e._e()],1),a("el-row",[a("el-checkbox-button",{staticClass:"cs-tag",model:{value:e.tag.checked3,callback:function(t){e.$set(e.tag,"checked3",t)},expression:"tag.checked3"}},[e._v("泳道节点")]),a("div",{attrs:{align:"center"}},[e.tag.checked3?a("draggable",e._b({attrs:{grid:{gutter:8,column:2},list:e.field.laneNodes,move:e.handleMove,tag:"el-row"},on:{end:e.handleMoveEnd,start:e.handleMoveStart}},"draggable",{group:{name:"flow",pull:"clone",put:!1},sort:!1},!1),e._l(e.field.laneNodes,(function(t,n){return a("el-col",{key:n,attrs:{span:12}},[a("div",{staticClass:"node-item",attrs:{type:t.type,belongto:"laneNodes"}},[a("i",{class:t.icon,staticStyle:{"font-size":"16px"}}),e._v(" "+e._s(t.name)+" ")])])})),1):e._e()],1)],1)],1),a("el-main",{staticStyle:{padding:"0"}},[a("el-container",{staticStyle:{height:"100%"}},[e.isShowContent?e._e():a("el-header",{staticClass:"header-option",staticStyle:{height:"auto"}},[a("el-popover",{attrs:{placement:"bottom",title:"确认要重新绘制吗？"},model:{value:e.isShowPopover,callback:function(t){e.isShowPopover=t},expression:"isShowPopover"}},[a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{size:"small"},on:{click:function(t){e.isShowPopover=!1}}},[e._v("取消")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.clear}},[e._v("确认")])],1),a("el-tooltip",{attrs:{slot:"reference",content:"重新绘制",placement:"bottom"},slot:"reference"},[a("el-button",{staticClass:"header-option-button",attrs:{icon:"el-icon-refresh-left",size:"small"}})],1)],1)],1),a("el-main",{staticClass:"content",staticStyle:{padding:"0"}},[e.loadFlowArea&&e.flowData?a("flow-area",{ref:"flowArea",attrs:{"browser-type":e.browserType,"current-tool":e.currentTool,"flow-data":e.flowData,"is-drag":e.isDrag,"is-show-content":e.isShowContent,plumb:e.plumb,select:e.currentSelect,"select-group":e.currentSelectGroup},on:{"update:select":function(t){e.currentSelect=t},"update:selectGroup":function(t){e.currentSelectGroup=t},"update:select-group":function(t){e.currentSelectGroup=t},findNodeConfig:e.findNodeConfig,getShortcut:e.getShortcut,saveFlow:e.saveFlow,selectTool:e.selectTool}}):e._e(),a("vue-context-menu",{attrs:{"context-menu-data":e.linkContextMenuData},on:{deleteLink:e.deleteLink}})],1)],1)],1),e.isShowContent?e._e():a("el-aside",{staticClass:"attr-area",attrs:{theme:"light",width:"350px"},on:{mousedown:function(t){return t.stopPropagation(),e.loseShortcut(t)}}},[a("flow-attr",{attrs:{"flow-data":e.flowData,"form-template":e.formTemplate,plumb:e.plumb}})],1)],1),a("el-dialog",{attrs:{closable:e.flowPicture.closable,"mask-closable":e.flowPicture.maskClosable,title:"流程设计图_"+e.flowData.attr.id+".png",visible:e.flowPicture.modalVisible,"cancel-text":"取消",centered:"","ok-text":"下载到本地",width:"90%"},on:{cancel:e.cancelDownLoadFlowPicture,ok:e.downLoadFlowPicture}},[a("div",{attrs:{align:"center"}},[a("img",{attrs:{src:e.flowPicture.url}})])])],1)},r=[]},eb0c:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-tabs",{attrs:{"tab-position":"left"},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"用户群组",name:"group"}},[a("user-group",{ref:"group",attrs:{"group-names":e.groupNames,group:e.selectGroupList},on:{"update:groupNames":function(t){e.groupNames=t},"update:group-names":function(t){e.groupNames=t},"update:group":function(t){e.selectGroupList=t},handleUpdate:e.handleUpdate}})],1),a("el-tab-pane",{attrs:{label:"角色",name:"roles"}},[a("roles",{ref:"roles",attrs:{show:e.showDialog,"role-names":e.roleNames,roles:e.selectRoleList},on:{"update:show":function(t){e.showDialog=t},"update:roleNames":function(t){e.roleNames=t},"update:role-names":function(t){e.roleNames=t},"update:roles":function(t){e.selectRoleList=t},handleUpdate:e.handleUpdate}})],1),a("el-tab-pane",{attrs:{label:"用户",name:"users"}},[a("users",{ref:"users",attrs:{status:e.status[1],show:e.showDialog,"user-names":e.userNames,users:e.selectUserList},on:{"update:status":function(t){return e.$set(e.status,1,t)},"update:show":function(t){e.showDialog=t},"update:userNames":function(t){e.userNames=t},"update:user-names":function(t){e.userNames=t},"update:users":function(t){e.selectUserList=t},handleUpdate:e.handleUpdate}})],1),a("el-tab-pane",{attrs:{name:"departments",label:"部门"}},[a("department",{ref:"departments",attrs:{show:e.showDialog,status:e.status[2],"department-names":e.departmentNames,departments:e.selectDepartmentList},on:{"update:show":function(t){e.showDialog=t},"update:status":function(t){return e.$set(e.status,2,t)},"update:departmentNames":function(t){e.departmentNames=t},"update:department-names":function(t){e.departmentNames=t},"update:departments":function(t){e.selectDepartmentList=t},handleUpdate:e.handleUpdate}})],1)],1)},r=[]},eb18:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:!e.departmentId&&!e.nullIsFetch||e.disabled},on:{change:e.handleChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)},r=[]},ebc3:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"plan-history"},[a("div",{staticClass:"scroll"},[a("ul",[e._l(e.histories,(function(t,n){return[a("li",{key:t.Id,on:{click:function(a){return e.view(t)}}},[a("span",[e._v(e._s(n+1))]),a("div",[e._v(e._s(e.moment(t.Data_Date).format("YYYY-MM-DD")))]),a("i",{staticClass:"el-icon-arrow-right"})])]}))],2),e.histories.length<=0?a("div",{staticStyle:{"text-align":"center",border:"1px solid #F2F2F2",color:"#999",padding:"8px"}},[e._v(" 暂无数据 ")]):e._e()])])},r=[]},ec75:function(e,t,a){"use strict";a("a0a3")},ee2b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{disabled:e.loading||e.disabled,"on-success":e.onSuccess,"on-remove":e.handleRemove,"before-remove":e.beforeRemove,"on-preview":e.handlePreview,"before-upload":e.importData,limit:e.limits,"on-exceed":e.handleExceed,"file-list":e.fileList,accept:e.accept,"show-file-list":e.showList,action:e.action,name:e.name,data:e.formData}},[a("el-button",{attrs:{icon:e.btnIcon,loading:e.loading,size:e.btnSize,type:e.btnType}},[e._v(e._s(e.btnLabel))]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)},r=[]},ee30:function(e,t,a){},ef66:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"wl-transfer transfer",style:{width:e.width,height:e.height}},["transfer"==e.mode?[n("div",{staticClass:"transfer-left"},[n("h3",{staticClass:"transfer-title"},[n("el-checkbox",{attrs:{indeterminate:e.from_is_indeterminate},on:{change:e.fromAllBoxChange},model:{value:e.from_check_all,callback:function(t){e.from_check_all=t},expression:"from_check_all"}}),n("span",[e._v(e._s(e.fromTitle))]),e._t("title-left")],2),n("div",{staticClass:"transfer-main"},[e._t("from"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{clearable:"",placeholder:e.placeholder,size:"small"},model:{value:e.filterFrom,callback:function(t){e.filterFrom=t},expression:"filterFrom"}}):e._e(),n("el-tree",{ref:"from-tree",attrs:{"show-checkbox":"",lazy:e.lazy,indent:e.indent,draggable:e.draggable,"allow-drag":e.allowDrag,"allow-drop":e.allowDrop,"icon-class":e.iconClass,"node-key":e.node_key,load:e.leftloadNode,props:e.defaultProps,data:e.self_from_data,accordion:e.accordion,"default-expand-all":e.openAll,"highlight-current":e.highLight,"check-strictly":e.checkStrictly,"render-content":e.renderContentLeft,"filter-node-method":e.filterNodeFrom,"default-checked-keys":e.defaultCheckedKeys,"default-expanded-keys":e.from_expanded_keys},on:{check:e.fromTreeChecked,"node-drag-start":e.nodeDragStartLeft,"node-drag-enter":e.nodeDragEnterLeft,"node-drag-leave":e.nodeDragLeaveLeft,"node-drag-over":e.nodeDragOverLeft,"node-drag-end":e.nodeDragEndLeft,"node-drop":e.nodeDropLeft},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,r=t.data;return n("span",{staticClass:"custom-tree-node"},[e._t("content-left",[n("span",[e._v(e._s(a.label))])],{node:a,data:r})],2)}}],null,!0)}),e._t("left-footer")],2)]),n("div",{staticClass:"transfer-center"},[e.button_text?[n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.from_disabled},on:{click:function(t){return e.addToAims(!0)}}},[e._v(" "+e._s(e.fromButton||"添加")+" "),n("i",{staticClass:"el-icon-arrow-right"})])],1),n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.to_disabled,icon:"el-icon-arrow-left"},on:{click:e.removeToSource}},[e._v(e._s(e.toButton||"移除"))])],1)]:[n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.from_disabled},on:{click:function(t){return e.addToAims(!0)}}},[e._v("添加 "),n("i",{staticClass:"el-icon-arrow-right"})])],1),n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.to_disabled,icon:"el-icon-arrow-left"},on:{click:e.removeToSource}},[e._v("移除")])],1)]],2),n("div",{staticClass:"transfer-right"},[n("h3",{staticClass:"transfer-title"},[n("el-checkbox",{attrs:{indeterminate:e.to_is_indeterminate},on:{change:e.toAllBoxChange},model:{value:e.to_check_all,callback:function(t){e.to_check_all=t},expression:"to_check_all"}}),n("span",[e._v(e._s(e.toTitle))]),e._t("title-right")],2),n("div",{staticClass:"transfer-main"},[e._t("to"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{clearable:"",placeholder:e.placeholder,size:"small"},model:{value:e.filterTo,callback:function(t){e.filterTo=t},expression:"filterTo"}}):e._e(),n("el-tree",{ref:"to-tree",attrs:{slot:"to","show-checkbox":"",lazy:e.lazyRight,data:e.self_to_data,"node-key":e.node_key,props:e.defaultProps,load:e.rightloadNode,indent:e.indent,draggable:e.draggable,"allow-drag":e.allowDrag,"allow-drop":e.allowDrop,"icon-class":e.iconClass,"default-expand-all":e.openAll,"highlight-current":e.highLight,"check-strictly":e.checkStrictly,"render-content":e.renderContentRight,"filter-node-method":e.filterNodeTo,"default-expanded-keys":e.to_expanded_keys},on:{check:e.toTreeChecked,"node-drag-start":e.nodeDragStartRight,"node-drag-enter":e.nodeDragEnterRight,"node-drag-leave":e.nodeDragLeaveRight,"node-drag-over":e.nodeDragOverRight,"node-drag-end":e.nodeDragEndRight,"node-drop":e.nodeDropRight},slot:"to",scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,r=t.data;return n("span",{staticClass:"custom-tree-node"},[e._t("content-left",[n("span",[e._v(e._s(a.label))])],{node:a,data:r})],2)}}],null,!0)}),e._t("right-footer")],2)])]:"transferSort"==e.mode?[n("div",{staticClass:"transfer-left"},[n("h3",{staticClass:"transfer-title"},[n("el-checkbox",{attrs:{indeterminate:e.from_is_indeterminate},on:{change:e.fromAllBoxChange},model:{value:e.from_check_all,callback:function(t){e.from_check_all=t},expression:"from_check_all"}}),n("span",[e._v(e._s(e.fromTitle))]),e._t("title-left")],2),n("div",{staticClass:"transfer-main"},[e._t("from"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{clearable:"",placeholder:e.placeholder,size:"small"},model:{value:e.filterFrom,callback:function(t){e.filterFrom=t},expression:"filterFrom"}}):e._e(),n("el-tree",{ref:"from-tree",attrs:{"show-checkbox":"",lazy:e.lazy,indent:e.indent,draggable:e.draggable,"allow-drag":e.allowDrag,"allow-drop":e.allowDrop,"icon-class":e.iconClass,"node-key":e.node_key,load:e.leftloadNode,props:e.defaultProps,data:e.self_from_data,accordion:e.accordion,"default-expand-all":e.openAll,"highlight-current":e.highLight,"check-strictly":e.checkStrictly,"render-content":e.renderContentLeft,"filter-node-method":e.filterNodeFrom,"default-checked-keys":e.defaultCheckedKeys,"default-expanded-keys":e.from_expanded_keys},on:{check:e.fromTreeChecked,"node-drag-start":e.nodeDragStartLeft,"node-drag-enter":e.nodeDragEnterLeft,"node-drag-leave":e.nodeDragLeaveLeft,"node-drag-over":e.nodeDragOverLeft,"node-drag-end":e.nodeDragEndLeft,"node-drop":e.nodeDropLeft},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,r=t.data;return n("span",{staticClass:"custom-tree-node"},[e._t("content-left",[n("span",[e._v(e._s(a.label))])],{node:a,data:r})],2)}}],null,!0)}),e._t("left-footer")],2)]),n("div",{staticClass:"transfer-center"},[e.button_text?[n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.from_disabled},on:{click:function(t){return e.addToAimsSort(!0)}}},[e._v(" "+e._s(e.fromButton||"添加")+" "),n("i",{staticClass:"el-icon-arrow-right"})])],1),n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.to_disabled,icon:"el-icon-arrow-left"},on:{click:e.removeToSourceSort}},[e._v(e._s(e.toButton||"移除"))])],1)]:[n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.from_disabled},on:{click:function(t){return e.addToAimsSort(!0)}}},[e._v("添加 "),n("i",{staticClass:"el-icon-arrow-right"})])],1),n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.to_disabled,icon:"el-icon-arrow-left"},on:{click:e.removeToSourceSort}},[e._v("移除")])],1)]],2),n("div",{staticClass:"transfer-right"},[n("h3",{staticClass:"transfer-title"},[n("el-checkbox",{attrs:{indeterminate:e.to_is_indeterminate},on:{change:e.toAllBoxChange},model:{value:e.to_check_all,callback:function(t){e.to_check_all=t},expression:"to_check_all"}}),n("span",[e._v(e._s(e.toTitle))]),e._t("title-right")],2),n("div",{staticClass:"transfer-main"},[e._t("to"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{clearable:"",placeholder:e.placeholder,size:"small"},model:{value:e.filterTo,callback:function(t){e.filterTo=t},expression:"filterTo"}}):e._e(),n("el-tree",{ref:"to-tree",attrs:{slot:"to","show-checkbox":"",lazy:e.lazyRight,data:e.self_to_data,"node-key":e.node_key,props:e.defaultProps,load:e.rightloadNode,indent:e.indent,draggable:e.draggable,"allow-drag":e.allowDrag,"allow-drop":e.allowDrop,"icon-class":e.iconClass,"default-expand-all":e.openAll,"highlight-current":e.highLight,"check-strictly":e.checkStrictly,"render-content":e.renderContentRight,"filter-node-method":e.filterNodeTo,"default-expanded-keys":e.to_expanded_keys},on:{check:e.toTreeChecked,"node-drag-start":e.nodeDragStartRight,"node-drag-enter":e.nodeDragEnterRight,"node-drag-leave":e.nodeDragLeaveRight,"node-drag-over":e.nodeDragOverRight,"node-drag-end":e.nodeDragEndRight,"node-drop":e.nodeDropRight},slot:"to",scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,r=t.data;return n("span",{staticClass:"custom-tree-node"},[e._t("content-left",[n("span",[e._v(e._s(a.label))])],{node:a,data:r})],2)}}],null,!0)}),e._t("right-footer")],2),1==e.upDownDisable?n("div",{staticStyle:{position:"absolute",top:"45%",left:"85%",width:"15%",transform:"translateY(-50%)","text-align":"center"}},[n("el-button",{attrs:{icon:"el-icon-arrow-up",circle:""},on:{click:e.upData}})],1):e._e(),1==e.upDownDisable?n("div",{staticStyle:{position:"absolute",top:"55%",left:"85%",width:"15%",transform:"translateY(-50%)","text-align":"center"}},[n("el-button",{attrs:{icon:"el-icon-arrow-down",circle:""},on:{click:e.downData}})],1):e._e()])]:"addressList"==e.mode?[n("div",{staticClass:"transfer-left"},[n("h3",{staticClass:"transfer-title"},[n("el-checkbox",{attrs:{indeterminate:e.from_is_indeterminate},on:{change:e.fromAllBoxChange},model:{value:e.from_check_all,callback:function(t){e.from_check_all=t},expression:"from_check_all"}}),n("span",[e._v(e._s(e.fromTitle))])],1),n("div",{staticClass:"transfer-main"},[e._t("from"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{clearable:"",placeholder:e.placeholder,size:"small"},model:{value:e.filterFrom,callback:function(t){e.filterFrom=t},expression:"filterFrom"}}):e._e(),n("el-tree",{ref:"from-tree",attrs:{"show-checkbox":"",indent:e.indent,draggable:e.draggable,"allow-drag":e.allowDrag,"allow-drop":e.allowDrop,"icon-class":e.iconClass,"node-key":e.node_key,props:e.defaultProps,data:e.self_from_data,"default-expand-all":e.openAll,"highlight-current":e.highLight,"render-content":e.renderContentLeft,"filter-node-method":e.filterNodeFrom,"default-expanded-keys":e.from_expanded_keys},on:{check:e.fromTreeChecked,"node-drag-start":e.nodeDragStartLeft,"node-drag-enter":e.nodeDragEnterLeft,"node-drag-leave":e.nodeDragLeaveLeft,"node-drag-over":e.nodeDragOverLeft,"node-drag-end":e.nodeDragEndLeft,"node-drop":e.nodeDropLeft}})],2)]),n("div",{staticClass:"transfer-center address-list-center"},[n("p",{directives:[{name:"show",rawName:"v-show",value:!e.move_up,expression:"!move_up"}],staticClass:"transfer-center-item",class:{"address-only-item":1===e.addressOptions.num}},[n("el-button",{staticClass:"address-first-btn",attrs:{type:"primary",icon:"el-icon-arrow-right",circle:"",disabled:e.from_disabled},on:{click:function(t){return e.addressListTransfer(0)}}})],1),e.addressOptions.num>1?n("p",{staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.from_disabled,icon:"el-icon-arrow-right",circle:""},on:{click:function(t){return e.addressListTransfer(1)}}})],1):e._e(),n("p",{directives:[{name:"show",rawName:"v-show",value:e.move_up,expression:"move_up"}],staticClass:"transfer-center-item"},[n("el-button",{attrs:{type:"primary",disabled:e.from_disabled,icon:"el-icon-arrow-right",circle:""},on:{click:function(t){return e.addressListTransfer(2)}}})],1)]),n("div",{staticClass:"transfer-right"},[n("div",{staticClass:"transfer-right-item",class:{"transfer-right-small":e.move_up,"transfer-right-only":1===e.addressOptions.num}},[n("h3",{staticClass:"transfer-title"},[n("span",[e._v(e._s(e.toTitle))]),e.move_up?n("img",{staticClass:"move_up_img move_down_img",attrs:{src:a("e59e"),alt:""},on:{click:function(t){return e.moveUp("down")}}}):n("span",{staticClass:"u-clear",on:{click:function(t){return e.clearList(0,"all")}}},[e._v("清空")])]),e.move_up?e._e():n("div",{staticClass:"transfer-main"},[e._t("to"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{placeholder:e.placeholder,size:"small"},model:{value:e.filterListFirst,callback:function(t){e.filterListFirst=t},expression:"filterListFirst"}}):e._e(),n("ul",{staticClass:"address-list-ul"},e._l(e.addressee,(function(t){return n("li",{key:t[e.node_key],staticClass:"address-list-li"},[n("label",[e._v(" "+e._s(t[e.defaultProps.label])+" "+e._s(e.addressOptions.connector)+" "+e._s(t[e.addressOptions.suffix])+" ")]),n("i",{staticClass:"address-list-del el-icon-delete",on:{click:function(a){return e.clearList(0,t[e.node_key])}}})])})),0)],2)]),e.addressOptions.num>=2?n("div",{staticClass:"transfer-right-item"},[n("h3",{staticClass:"transfer-title"},[n("span",[e._v(e._s(e.toTitleSecond||"抄送人"))]),n("span",{staticClass:"u-clear",on:{click:function(t){return e.clearList(1,"all")}}},[e._v("清空")])]),n("div",{staticClass:"transfer-main"},[e._t("to"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{placeholder:e.placeholder,size:"small"},model:{value:e.filterListSecond,callback:function(t){e.filterListSecond=t},expression:"filterListSecond"}}):e._e(),n("ul",{staticClass:"address-list-ul"},e._l(e.Cc,(function(t){return n("li",{key:t[e.node_key],staticClass:"address-list-li"},[n("label",[e._v(" "+e._s(t[e.defaultProps.label])+" "+e._s(e.addressOptions.connector)+" "+e._s(t[e.addressOptions.suffix])+" ")]),n("i",{staticClass:"address-list-del el-icon-delete",on:{click:function(a){return e.clearList(1,t[e.node_key])}}})])})),0)],2)]):e._e(),3===e.addressOptions.num?n("div",{staticClass:"transfer-right-item",class:{"transfer-right-small":!e.move_up}},[n("h3",{staticClass:"transfer-title"},[n("span",[e._v(e._s(e.toTitleThird||"密送人"))]),e.move_up?n("span",{staticClass:"u-clear",on:{click:function(t){return e.clearList(2,"all")}}},[e._v("清空")]):n("img",{staticClass:"move_up_img",attrs:{src:a("e59e"),alt:""},on:{click:function(t){return e.moveUp("up")}}})]),e.move_up?n("div",{staticClass:"transfer-main"},[e._t("to"),e.filter?n("el-input",{staticClass:"filter-tree",attrs:{placeholder:e.placeholder,size:"small"},model:{value:e.filterListThird,callback:function(t){e.filterListThird=t},expression:"filterListThird"}}):e._e(),n("ul",{staticClass:"address-list-ul"},e._l(e.secret_receiver,(function(t){return n("li",{key:t[e.node_key],staticClass:"address-list-li"},[n("label",[e._v(" "+e._s(t[e.defaultProps.label])+" "+e._s(e.addressOptions.connector)+" "+e._s(t[e.addressOptions.suffix])+" ")]),n("i",{staticClass:"address-list-del el-icon-delete",on:{click:function(a){return e.clearList(2,t[e.node_key])}}})])})),0)],2):e._e()]):e._e()])]:e._e()],2),e.showbutton?n("span",{staticClass:"dialog-footer",staticStyle:{margin:"20px auto 0",display:"block","text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.cancel}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1):e._e()])},r=[]},efc3:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3"),a("2c3e");t.default={name:"Sticky",props:{stickyTop:{type:Number,default:0},zIndex:{type:Number,default:1},className:{type:String,default:""}},data:function(){return{active:!1,position:"",width:void 0,height:void 0,isSticky:!1}},mounted:function(){this.height=this.$el.getBoundingClientRect().height,window.addEventListener("scroll",this.handleScroll),window.addEventListener("resize",this.handleResize)},activated:function(){this.handleScroll()},destroyed:function(){window.removeEventListener("scroll",this.handleScroll),window.removeEventListener("resize",this.handleResize)},methods:{sticky:function(){this.active||(this.position="fixed",this.active=!0,this.width=this.width+"px",this.isSticky=!0)},handleReset:function(){this.active&&this.reset()},reset:function(){this.position="",this.width="auto",this.active=!1,this.isSticky=!1},handleScroll:function(){var e=this.$el.getBoundingClientRect().width;this.width=e||"auto";var t=this.$el.getBoundingClientRect().top;t<this.stickyTop?this.sticky():this.handleReset()},handleResize:function(){this.isSticky&&(this.width=this.$el.getBoundingClientRect().width+"px")}}}},f0d4:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:e.multiple,disabled:e.disabled},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Value,attrs:{label:e.Display_Name,value:e.Value}})})),1)},r=[]},f349:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{title:{type:String,default:""},list:{type:Array,default:function(){return[]}}},data:function(){return{}}}},f35d:function(e,t,a){"use strict";a.r(t);var n=a("dc01"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},f5dd:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("b64b"),a("d3b7"),a("159b");n(a("b775"));var r=n(a("4360")),i=a("5f87");t.default={name:"PlanImport",props:{action:{type:String,default:""},exts:{type:Array,default:function(){return[]}},multiple:{type:Boolean,default:!1},filesize:{type:Number,default:5}},data:function(){return{fileList:[],progresses:{},reqHeader:null,parsedData:null,loading:!1}},created:function(){this.reqHeader={},r.default.getters.token&&(this.reqHeader["Authorization"]=(0,i.getToken)()),r.default.getters.Last_Working_Object_Id&&(this.reqHeader.Last_Working_Object_Id=r.default.getters.Last_Working_Object_Id)},methods:{beforeUpload:function(e){var t=e.name.split(".").pop(),a=!0;this.exts.length>0&&(a=this.exts.indexOf(t)>-1);var n=!0;return n=e.size/1024/1024<this.filesize,a||this.$message.error("上传文件格式错误!"),n||this.$message.error("上传文件大小不能超过 "+this.filesize+"MB!"),this.loading=!1,a&&n},uploadProgressChange:function(e,t,a){var n=this;100===e.percent&&setTimeout((function(){n.progresses[t.name]=0,n.progresses=Object.assign({},n.progresses)}),600)},uploadStatusChange:function(e,t){"ready"===e.status?this.fileList.push(e):"fail"===e.status&&(this.fileList=this.fileList.concat([]));var a=!0;this.loading=!1,this.fileList.forEach((function(e){"success"!==e.status&&(a=!1)})),a&&this.cancel()},uploadError:function(e,t,a){this.loading=!1},uploadSuccess:function(e,t,a){e.IsSucceed?this.$emit("dialogFormSubmitSuccess",{type:"onMppImported",data:JSON.parse(e.Data)}):(t.status="fail",this.$message.error(e.Message))},cancel:function(){this.$refs.upload.clearFiles(),this.fileList=[],this.$emit("dialogCancel"),this.loading=!1},beginUpload:function(){this.fileList=this.fileList.map((function(e){return e.status="ready",e})),this.loading=!0,this.$refs.upload.submit()},reUpload:function(e){e.status="ready",this.$refs.upload.submit()},removeFile:function(e){this.fileList=this.fileList.filter((function(t){return t.name!==e.name}))},extIcon:function(e){var t="document_unknown_icon";switch(e.name.split(".").pop()){case"xls":case"xlsx":t="document_form_icon";break;case"txt":t="document_txt_icon";break;case"doc":case"docx":t="document_word_icon";break;case"zip":case"7z":case"rar":t="document_zip_icon";break;case"png":case"jpg":case"jpeg":case"gif":case"bmp":t="multimedia_image_icon";break;case"ppt":case"pptx":t="document_ppt_icon";break;case"pdf":t="document_pdf_icon";break}return t}}}},f60e:function(e,t,a){"use strict";a.r(t);var n=a("79ed"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},f67b:function(e,t,a){"use strict";a.r(t);var n=a("b85f"),r=a("1dfa");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("7f6a");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"9776399c",null);t["default"]=s.exports},f6cf:function(e,t,a){},f7bf:function(e,t,a){"use strict";a.r(t);var n=a("8d0e"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},f7c3:function(e,t,a){"use strict";a.r(t);var n=a("d9818"),r=a("301cf");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},f8e1:function(e,t,a){"use strict";a.r(t);var n=a("0e29"),r=a("7cf8");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("37fd");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"727b72b2",null);t["default"]=s.exports},f917:function(e,t,a){"use strict";a.r(t);var n=a("eb0c"),r=a("a8a1");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"681ffb70",null);t["default"]=s.exports},fb34:function(e,t,a){"use strict";a.r(t);var n=a("3db1"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},fb6a9:function(e,t,a){"use strict";a.r(t);var n=a("2bd2"),r=a("de99");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"364e08de",null);t["default"]=s.exports},fbcb:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2638"));a("4de4"),a("7db0"),a("c740"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var i=n(a("aca9")),o=n(a("b163")),s=n(a("f82c")),l=n(a("6725"));function c(){}t.default={name:"ElUpload",components:{ElProgress:s.default,UploadList:i.default,Upload:o.default},mixins:[l.default],provide:function(){return{uploader:this}},inject:{elForm:{default:""}},props:{action:{type:String,required:!0},headers:{type:Object,default:function(){return{}}},data:Object,multiple:Boolean,name:{type:String,default:"file"},drag:Boolean,dragger:Boolean,withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:String,type:{type:String,default:"select"},beforeUpload:Function,beforeRemove:Function,onRemove:{type:Function,default:c},onChange:{type:Function,default:c},onPreview:{type:Function},onSuccess:{type:Function,default:c},onProgress:{type:Function,default:c},onError:{type:Function,default:c},fileList:{type:Array,default:function(){return[]}},autoUpload:{type:Boolean,default:!0},listType:{type:String,default:"text"},httpRequest:Function,disabled:Boolean,limit:Number,onExceed:{type:Function,default:c},hiddenSuccessFile:Boolean,hiddenFailedFile:{type:Boolean,default:!0}},data:function(){return{uploadFiles:[],errorList:[],dragOver:!1,draging:!1,tempIndex:1}},computed:{uploadDisabled:function(){return this.disabled||(this.elForm||{}).disabled}},watch:{listType:function(e){"picture-card"!==e&&"picture"!==e||(this.uploadFiles=this.uploadFiles.map((function(e){if(!e.url&&e.raw)try{e.url=URL.createObjectURL(e.raw)}catch(t){}return e})))},fileList:{immediate:!0,handler:function(e){var t=this;this.uploadFiles=e.map((function(e){return e.uid=e.uid||Date.now()+t.tempIndex++,e.status=e.status||"success",e}))}}},beforeDestroy:function(){this.uploadFiles.forEach((function(e){e.url&&0===e.url.indexOf("blob:")&&URL.revokeObjectURL(e.url)}))},methods:{reUpload:function(e){if(this.errorList.length){var t=e.pointFile,a=e.file;this.$refs["upload-inner"].post(a,t)}},handleStart:function(e){e.uid=Date.now()+this.tempIndex++;var t={status:"ready",name:e.name,size:e.size,percentage:0,uid:e.uid,raw:e};if("picture-card"===this.listType||"picture"===this.listType)try{t.url=URL.createObjectURL(e)}catch(a){return}this.uploadFiles.push(t),this.onChange(t,this.uploadFiles)},handleProgress:function(e,t){var a=this.getFile(t);this.onProgress(e,a,this.uploadFiles),a.status="uploading",a.percentage=e.percent||0},handleSuccess:function(e,t){var a=this.getFile(t);if(a){a.status="success",a.response=e;var n=this.uploadFiles;this.hiddenSuccessFile&&n.splice(n.indexOf(a),1);var r=this.errorList.findIndex((function(e){return e.uid===a.uid}));-1!==r&&this.errorList.splice(r,1),this.onSuccess(e,a,this.uploadFiles),this.onChange(a,this.uploadFiles)}},handleError:function(e,t,a){var n=this.getFile(t),r=this.uploadFiles;n.status="fail",this.hiddenFailedFile&&r.splice(r.indexOf(n),1);var i=this.errorList.find((function(e){return e.uid===t.uid}));i||this.errorList.push({uid:t.uid,pointFile:a,file:t}),this.onError(e,n,this.uploadFiles,this.errorList),this.onChange(n,this.uploadFiles)},handleRemove:function(e,t){var a=this;t&&(e=this.getFile(t));var n=function(){a.abort(e);var t=a.uploadFiles;t.splice(t.indexOf(e),1),a.errorList.splice(a.errorList.findIndex((function(t){return t.uid===e.uid})),1),a.onRemove(e,t)};if(this.beforeRemove){if("function"===typeof this.beforeRemove){var r=this.beforeRemove(e,this.uploadFiles);r&&r.then?r.then((function(){n()}),c):!1!==r&&n()}}else n()},getFile:function(e){var t,a=this.uploadFiles;return a.every((function(a){return t=e.uid===a.uid?a:null,!t})),t},abort:function(e){this.$refs["upload-inner"].abort(e)},clearFiles:function(){this.uploadFiles=[]},submit:function(){var e=this;this.uploadFiles.filter((function(e){return"ready"===e.status})).forEach((function(t){e.$refs["upload-inner"].upload(t.raw)}))},getMigratingConfig:function(){return{props:{"default-file-list":"default-file-list is renamed to file-list.","show-upload-list":"show-upload-list is renamed to show-file-list.","thumbnail-mode":"thumbnail-mode has been deprecated, you can implement the same effect according to this case: http://element.eleme.io/#/zh-CN/component/upload#yong-hu-tou-xiang-shang-chuan"}}}},render:function(e){var t,a=this;this.showFileList&&(t=e(i.default,{attrs:{disabled:this.uploadDisabled,listType:this.listType,files:this.uploadFiles,handlePreview:this.onPreview},on:{remove:this.handleRemove}},[function(e){if(a.$scopedSlots.file)return a.$scopedSlots.file({file:e.file})}]));var n={props:{type:this.type,drag:this.drag,action:this.action,multiple:this.multiple,"before-upload":this.beforeUpload,"with-credentials":this.withCredentials,headers:this.headers,name:this.name,data:this.data,accept:this.accept,fileList:this.uploadFiles,autoUpload:this.autoUpload,listType:this.listType,disabled:this.uploadDisabled,limit:this.limit,"on-exceed":this.onExceed,"on-start":this.handleStart,"on-progress":this.handleProgress,"on-success":this.handleSuccess,"on-error":this.handleError,"on-preview":this.onPreview,"on-remove":this.handleRemove,"http-request":this.httpRequest},ref:"upload-inner"},o=this.$slots.trigger||this.$slots.default,s=e("upload",(0,r.default)([{},n]),[o]);return e("div",["picture-card"===this.listType?t:"",this.$slots.trigger?[s,this.$slots.default]:s,this.$slots.tip,"picture-card"!==this.listType?t:""])}}},fbd1:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var r=a("ea13"),i=n(a("a807")),o=a("f382");t.default={name:"UserGroup",components:{treeDetail:i.default},props:{group:{type:Array,default:function(){return[]}},show:{type:Boolean,default:!1}},data:function(){return{treeData:[],treeLoading:!1,defaultCheckedKeys:[],expandedKey:""}},mounted:function(){this.fetchTreeData()},methods:{fetchTreeData:function(){var e=this;(0,r.GetGroupTree)().then((function(t){t.IsSucceed&&((0,o.disableDirectory)(t.Data),e.treeData=t.Data,e.treeLoading=!1,e.$nextTick((function(t){e.defaultCheckedKeys=e.group.map((function(e){return e.Id}))})))}))},getCheckedData:function(){var e=this;this.$nextTick((function(t){e.$refs.tree.getCheckedNodes()}))},getCheckedNodes:function(e){this.$emit("update:group",e),this.$emit("handleUpdate",3)},check:function(e){e.dataArray,e.data}}}},fcd6:function(e,t,a){"use strict";a.r(t);var n=a("c643"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},fe99:function(e,t,a){"use strict";a.r(t);var n=a("8666"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},ff4c:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=n(a("a807")),i=a("0187");t.default={components:{TreeDetail:r.default},props:{roles:{type:Array,default:function(){return[]}},show:{type:Boolean,default:!1}},data:function(){return{expandedKey:"",treeLoading:!1,treeData:[],checkedNodes:[],defaultCheckedKeys:[]}},mounted:function(){this.fetchTreeData()},methods:{fetchTreeData:function(){var e=this;this.treeLoading=!0,(0,i.GetRoleTree)().then((function(t){e.treeData=t.Data,e.treeLoading=!1,e.$nextTick((function(t){e.defaultCheckedKeys=e.roles.map((function(e){return e.Id}))}))}))},getCheckedData:function(){this.$refs.tree.getCheckedNodes(!0)},getCheckedNodes:function(e){this.checkedNodes=e.filter((function(e){return!e.Is_Directory})),this.$emit("update:roles",this.checkedNodes),this.$emit("handleUpdate",0)}}}}}]);