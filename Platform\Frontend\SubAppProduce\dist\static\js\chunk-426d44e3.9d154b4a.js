(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-426d44e3"],{"17f3":function(e,t,a){"use strict";a.r(t);var n=a("b354"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"1da18":function(e,t,a){"use strict";a.r(t);var n=a("eab9"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"3f06":function(e,t,a){"use strict";a.r(t);var n=a("4604"),i=a("17f3");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("ab21");var s=a("2877"),o=Object(s["a"])(i["default"],n["a"],n["b"],!1,null,"99e2a88c",null);t["default"]=o.exports},4604:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100"},[a("div",{staticClass:"app-container h100"},[a("div",{staticClass:"top-btn",on:{click:e.toBack}},[a("el-button",[e._v("返回")])],1),a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-main"},[a("div",{staticClass:"table_warrap",staticStyle:{height:"100%"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleAdd()}}},[e._v("新增类型")]),a("el-button",{attrs:{disabled:0==e.selectList.length},on:{click:function(t){return e.handleDel()}}},[e._v("批量删除")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"op",fn:function(t){var n=t.row,i=t.index;return[a("div",[a("el-button",{attrs:{index:i,type:"text"},on:{click:function(t){return e.handleAdd(n)}}},[e._v("编辑")]),a("el-button",{attrs:{index:i,type:"text"},on:{click:function(t){return e.handleDel(n.Id)}}},[e._v("删除")])],1)]}}])})],1)],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增类型",visible:e.dialogVisible,width:"576px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("add-Dialog",{ref:"add",on:{close:e.handleClose,refresh:e.fetchData}})],1)],1)])])])},i=[]},"46b19":function(e,t,a){},"644f":function(e,t,a){},"80d7":function(e,t,a){"use strict";a("46b19")},ab21:function(e,t,a){"use strict";a("644f")},b354:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var i=n(a("c14f")),r=n(a("1da1")),s=n(a("0f97")),o=(a("d51a"),a("fd31"),n(a("641e"))),l=a("ed08"),c=n(a("db2b2")),d=a("ecb3");t.default={components:{DynamicDataTable:s.default,addDialog:c.default},mixins:[o.default],data:function(){return{loading:!1,dialogVisible:!1,form:{Name1:"",Name2:"",Name3:"",Name4:"",dateRange2:["",""],PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[]}},created:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getFactoryTypeOption("pro_type_maintenance_list");case 1:return t.a(2)}}),t)})))()},mounted:function(){},methods:{fetchData:function(){var e=this;this.loading=!0,(0,d.GetChangeType)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e})),e.total=t.Data.TotalCount,e.loading=!1):e.$message({message:t.Message,type:"error"})}))},datePickerwrapper:function(){},handleSearch:function(){},multiSelectedChange:function(e){this.selectList=e},handleAdd:function(e){var t=this;this.dialogVisible=!0,e&&this.$nextTick((function(){t.$refs.add.init(e)}))},handleDel:function(e){var t=this;this.$confirm("删除选中类型, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="";e||t.selectList.forEach((function(e){a+=e.Id+","})),(0,d.DeleteChangeType)({ids:e||a}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleClose:function(){this.$refs.add.resetForm(),this.dialogVisible=!1},handleType:function(){this.$router.push({name:"PROTypeMaintenance",query:{pg_redirect:"PROChangeManagement"}})},toBack:function(){(0,l.closeTagView)(this.$store,this.$route)}}}},db2b2:function(e,t,a){"use strict";a.r(t);var n=a("efa7"),i=a("1da18");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("80d7");var s=a("2877"),o=Object(s["a"])(i["default"],n["a"],n["b"],!1,null,"0f571050",null);t["default"]=o.exports},eab9:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("5530")),r=a("ecb3");a("ed08"),t.default={data:function(){return{form:{Id:"",Display_Name:""},btnLoading:!1}},mounted:function(){},methods:{init:function(e){this.form.Id=e.Id,this.form.Display_Name=e.Display_Name},submitForm:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;e.btnLoading=!0;var a=(0,i.default)({},e.form);(0,r.SaveChangeType)(a).then((function(t){t.IsSucceed?(e.$refs.form.resetFields(),e.$message({type:"success",message:"添加成功"}),e.btnLoading=!1,e.resetForm(),e.$emit("close"),e.$emit("refresh")):(e.$message({type:"warning",message:t.Message}),e.btnLoading=!1,e.resetForm())}))}))},resetForm:function(){this.form.Id="",this.form.Display_Name=""}}}},efa7:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"类型名称",prop:"Display_Name",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"360px"},attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1),a("el-form-item",[a("div",{staticClass:"btn-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submitForm()}}},[e._v("提交")])],1)])],1)],1)},i=[]}}]);