(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-608ad220"],{2918:function(e,t,r){"use strict";r("979a")},4648:function(e,t,r){"use strict";r.r(t);var a=r("a5d7"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},"979a":function(e,t,r){},a5d7:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(r("c14f")),n=a(r("1da1"));r("99af"),r("4de4"),r("7db0"),r("caad"),r("d81d"),r("fb6a"),r("e9f5"),r("910d"),r("f665"),r("ab43"),r("a9e3"),r("b680"),r("dca8"),r("d3b7"),r("2532");var i=r("be22"),s=r("cf45"),c=r("7757"),l=r("8cdf"),u=r("ed08");t.default={name:"PROPegistrationIncomeAdd",components:{},props:{},data:function(){var e=this;return{form:{Factory_Id:"",Receipt_No:"",Income_Type:"",Reference_Price:"",Accounting_Date:new Date,Sys_Project_Id:"无项目",Depart_Id:"",Coefficient:"",Number:"",Output_Tax_Rate:"",Payer:"",Abstract:"",Invoice_Type:0,ContractNo:"",Company_Id:localStorage.getItem("Last_Working_Object_Id")},backendDate:null,pickerOptions:{disabledDate:function(t){return t.getTime()<=new Date(e.backendDate).getTime()}},IncomeType:[],ProjectList:[],departmentlist:[],factoryOption:[],type:"",obj:{},factoryDisabled:!1,disabledDepart:!1,isDisabled:!0,tableData:[{Pay_Date:"",Pay_Price:"",Payee:"",Pay_Depart_Id:"",Remark:"2"}],rules:{Factory_Id:[{required:!0,message:"请选择归属基地",trigger:"change"}],Income_Type:[{required:!0,message:"请选择收入类别",trigger:"change"}],Reference_Price:[{required:!0,message:"请输入参考价",trigger:"blur"}],Accounting_Date:[{required:!0,message:"请选择日期",trigger:"change"}],Depart_Id:[{required:!0,message:"请选择归属部门",trigger:"change"}],Sys_Project_Id:[{required:!0,message:"请选择归属项目",trigger:"change"}],Coefficient:[{required:!0,message:"请输入系数",trigger:"blur"}],Number:[{required:!0,message:"请输入数量",trigger:"blur"}],Invoice_Type:[{required:!0,message:"请选择发票类型",trigger:"change"}],Output_Tax_Rate:[{required:!0,message:"请输入税率",trigger:"blur"}],Payer:[{required:!0,message:"请输入付款方",trigger:"blur"}]}}},computed:{getPrice:function(){return this.form.Unit_Price=Number((Number(this.form.Reference_Price)*Number(this.form.Coefficient)).toFixed(2))||0},getAccountingAmount:function(){return this.form.Accounting_Price=Number((Number(this.getPrice)*Number(this.form.Number)).toFixed(2))||0}},watch:{},mounted:function(){var e=this;return(0,n.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.obj=e.$route.query,t.n=1,e.getPermission();case 1:return t.n=2,(0,s.getDictionary)("IncomeType").then((function(t){var r;e.IncomeType=t,null!==(r=e.IncomeType)&&void 0!==r&&r.length&&"add"==e.obj.type&&(e.form.Income_Type=e.IncomeType[0].Id)}));case 2:return t.n=3,e.getOptions();case 3:"view"!=e.obj.type&&"edit"!=e.obj.type||(e.getIncomeDetails(e.obj.Id),e.changeValue(e.form.Factory_Id),e.getTimeData(e.form.Factory_Id)),"view"==e.obj.type?e.isDisabled=!0:e.isDisabled=!1;case 4:return t.a(2)}}),t)})))()},methods:{getTimeData:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,c.GetOMALatestAccountingDate)({FactoryId:e});case 1:a=r.v,a.IsSucceed?t.backendDate=a.Data||"":t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getPermission:function(){var e=this;return new Promise((function(t,r){e.permissionList=[],(0,i.GetUserDepartTypePermission)({}).then((function(a){a.IsSucceed?(e.permissionList=a.Data,t()):(e.$message({message:a.Message,type:"error"}),r())}))}))},changeValue:function(e){this.getFirstLevelDepartsUnderFactory(e),this.getFactoryProjectList(e)},changeType:function(e){0==e&&(this.form.Output_Tax_Rate="")},getOptions:function(){var e,t=this;e="view"==this.obj.type?c.GetQueryNonExternalFactory:c.GetNonExternalFactory,e({}).then((function(e){if(e.IsSucceed){t.factoryOption=Object.freeze(e.Data||[]);var r=t.factoryOption.find((function(e){return e.Is_Cur_User_Factory}));r&&t.factoryOption.length&&"add"==t.obj.type&&(t.form.Factory_Id=r.Id,t.changeValue(t.form.Factory_Id),t.getTimeData(t.form.Factory_Id)),t.factoryDisabled=r&&0===t.permissionList.length}else t.$message({message:e.Message,type:"error"})}))},onInput:function(e){if(e.includes(".")){var t=e.split(".");t[1].length>6&&(e="".concat(t[0],".").concat(t[1].slice(0,6)),this.form.Number=parseFloat(e))}},getProLabel:function(e){return"".concat(e.Short_Name,"（").concat(e.Status,"/").concat(e.FeedingMethod,"）")},getFirstLevelDepartsUnderFactory:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a,n,i,s;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:if(e){r.n=1;break}return r.a(2);case 1:return r.n=2,(0,l.GetFirstLevelDepartsUnderFactory)({FactoryId:e});case 2:a=r.v,a.IsSucceed?(n=a.Data||[],t.permissionList.length?(i=n.filter((function(e){return t.permissionList.includes(e.Type)||e.Is_Cur_User_Depart})),t.departmentlist=i):t.departmentlist=n,s=t.departmentlist.find((function(e){return e.Is_Cur_User_Depart})),s?t.factoryOption.length&&"add"===t.obj.type&&(t.form.Depart_Id=s.Id):t.factoryOption.length&&"add"===t.obj.type&&(t.form.Depart_Id=""),t.disabledDepart=s&&0===t.permissionList.length):t.message.error(a.Mesaage);case 3:return r.a(2)}}),r)})))()},getIncomeDetails:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,l.IncomeDetails)({Id:e});case 1:a=r.v,a.IsSucceed?(t.form=a.Data||[],t.changeValue(t.form.Factory_Id),t.getTimeData(t.form.Factory_Id)):t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getFactoryProjectList:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:if(e){r.n=1;break}return r.a(2);case 1:return r.n=2,(0,l.GetFactoryProjectList)({FactoryId:e});case 2:a=r.v,a.IsSucceed?t.ProjectList=Object.freeze(a.Data.map((function(e){return e.Status||e.Type?e.label="".concat(e.Short_Name,"(").concat(e.Status&&e.Type?"".concat(e.Status,"/").concat(e.Type):"".concat(e.Status||e.Type),")"):e.label=e.Short_Name,e}))):t.message.error(a.Mesaage);case 3:return r.a(2)}}),r)})))()},getFactory:function(){var e=this;(0,c.GetNonExternalFactory)({}).then((function(t){t.IsSucceed?e.factoryOption=t.Data||[]:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;"add"==e.obj.type?(0,l.SaveIncome)(e.form).then((function(t){t.IsSucceed?(e.$message.success("提交成功"),e.toBack()):e.$message({message:t.Message,type:"error"})})):(e.form.Id=e.obj.Id,(0,l.EditIncome)(e.form).then((function(t){t.IsSucceed?(e.$message.success("编辑成功"),e.toBack()):e.$message({message:t.Message,type:"error"})})))}))},toBack:function(){(0,u.closeTagView)(this.$store,this.$route)}}}},af5e:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[r("div",{staticClass:"cs-z-page-main-content"},[r("div",{staticClass:"top"},[e._v("营业外收入基础信息")]),r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[r("el-row",{staticClass:"row-bg"},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属基地:",prop:"Factory_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",disabled:e.isDisabled||e.factoryDisabled},on:{change:e.changeValue},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e,t){return r("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"收入类别:",prop:"Income_Type"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled},model:{value:e.form.Income_Type,callback:function(t){e.$set(e.form,"Income_Type",t)},expression:"form.Income_Type"}},e._l(e.IncomeType,(function(e,t){return r("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"参考价:",prop:"Reference_Price"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Reference_Price,callback:function(t){e.$set(e.form,"Reference_Price",t)},expression:"form.Reference_Price"}},[r("template",{slot:"append"},[e._v("元")])],2)],1),r("el-form-item",{attrs:{label:"单价:",prop:"",required:""}},[r("el-input",{attrs:{type:"number",step:"any",disabled:""},model:{value:e.getPrice,callback:function(t){e.getPrice=t},expression:"getPrice"}},[r("template",{slot:"append"},[e._v("元")])],2)],1),r("el-form-item",{attrs:{label:"核算金额:",prop:"",required:""}},[r("el-input",{attrs:{step:"any",type:"number",disabled:""},model:{value:e.getAccountingAmount,callback:function(t){e.getAccountingAmount=t},expression:"getAccountingAmount"}},[r("template",{slot:"append"},[e._v("元")])],2)],1),r("el-form-item",{attrs:{label:"核算开始日期:",prop:"Accounting_Date"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期","value-format":"yyyy-MM-dd",disabled:e.isDisabled,"picker-options":e.pickerOptions},model:{value:e.form.Accounting_Date,callback:function(t){e.$set(e.form,"Accounting_Date",t)},expression:"form.Accounting_Date"}})],1),r("el-form-item",{attrs:{label:"合同编号:"}},[r("el-input",{attrs:{disabled:e.isDisabled},model:{value:e.form.ContractNo,callback:function(t){e.$set(e.form,"ContractNo",t)},expression:"form.ContractNo"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"单据编号",prop:"Receipt_No"}},[r("el-input",{attrs:{disabled:"",placeholder:""},model:{value:e.form.Receipt_No,callback:function(t){e.$set(e.form,"Receipt_No",t)},expression:"form.Receipt_No"}})],1),r("el-form-item",{attrs:{label:"登记部门:",prop:"Depart_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",disabled:e.isDisabled||"view"==e.obj.type||!e.form.Factory_Id||e.disabledDepart},model:{value:e.form.Depart_Id,callback:function(t){e.$set(e.form,"Depart_Id",t)},expression:"form.Depart_Id"}},e._l(e.departmentlist,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"归属项目:",prop:"Sys_Project_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled||!e.form.Factory_Id},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},[r("el-option",{attrs:{label:"无项目",value:"无项目"}}),e._l(e.ProjectList,(function(t){return r("el-option",{key:t.Sys_Project_Id,attrs:{label:e.getProLabel(t),value:t.Sys_Project_Id}})}))],2)],1),r("el-form-item",{attrs:{label:"系数:",prop:"Coefficient"}},[r("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{disabled:e.isDisabled,precision:4,min:0},model:{value:e.form.Coefficient,callback:function(t){e.$set(e.form,"Coefficient",t)},expression:"form.Coefficient"}})],1),r("el-form-item",{attrs:{label:"数量:",prop:"Number"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},on:{input:e.onInput},model:{value:e.form.Number,callback:function(t){e.$set(e.form,"Number",e._n(t))},expression:"form.Number"}})],1),r("el-form-item",{attrs:{label:"票据类型:",prop:"Invoice_Type"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled},on:{change:e.changeType},model:{value:e.form.Invoice_Type,callback:function(t){e.$set(e.form,"Invoice_Type",t)},expression:"form.Invoice_Type"}},[r("el-option",{attrs:{label:"无发票",value:0}}),r("el-option",{attrs:{label:"增值税普通发票",value:2}}),r("el-option",{attrs:{label:"增值税专用发票",value:1}})],1)],1),0!=e.form.Invoice_Type?r("el-form-item",{attrs:{label:"税率:",prop:"Output_Tax_Rate"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Output_Tax_Rate,callback:function(t){e.$set(e.form,"Output_Tax_Rate",t)},expression:"form.Output_Tax_Rate"}},[r("template",{slot:"append"},[e._v("%")])],2)],1):e._e(),r("el-form-item",{attrs:{label:"付款方:",prop:"Payer"}},[r("el-input",{attrs:{disabled:e.isDisabled},model:{value:e.form.Payer,callback:function(t){e.$set(e.form,"Payer",t)},expression:"form.Payer"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"摘要:",prop:"Abstract"}},[r("el-input",{attrs:{type:"textarea",rows:4,disabled:e.isDisabled},model:{value:e.form.Abstract,callback:function(t){e.$set(e.form,"Abstract",t)},expression:"form.Abstract"}})],1)],1)],1)],1),r("el-row",[r("footer",[e.isDisabled?e._e():r("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("提交")]),r("el-button",{on:{click:e.toBack}},[e._v("取消")])],1)])],1)])},o=[]},dca8:function(e,t,r){"use strict";var a=r("23e7"),o=r("bb2f"),n=r("d039"),i=r("861d"),s=r("f183").onFreeze,c=Object.freeze,l=n((function(){c(1)}));a({target:"Object",stat:!0,forced:l,sham:!o},{freeze:function(e){return c&&i(e)?c(s(e)):e}})},ee153:function(e,t,r){"use strict";r.r(t);var a=r("af5e"),o=r("4648");for(var n in o)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return o[e]}))}(n);r("2918");var i=r("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"df875a78",null);t["default"]=s.exports}}]);