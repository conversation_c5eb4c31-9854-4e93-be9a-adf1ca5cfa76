(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-c88c20a8"],{"0058":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("15fd"));a("7db0"),a("4e82"),a("b0c0"),a("e9f5"),a("f665"),a("dca8"),a("d3b7");var i=a("8378"),o=(a("1b69"),a("93aa")),l=["Id"];e.default={data:function(){return{versionList:[],projectList:[],activeName:"",form:{Name:"",Is_System:!1,Sort:void 0},btnAddLoading:!1,btnDelLoading:!1,btnLoading:!1,rules:{Name:[{required:!0,message:"请输入",trigger:"blur"}]}}},mounted:function(){this.getVersionList(),this.getProject()},methods:{getVersionList:function(){var t=this;(0,i.GetList)({}).then((function(e){if(e.IsSucceed){t.versionList=e.Data.sort((function(t,e){return t.Sort-e.Sort}));var a=t.versionList.find((function(t){return t.Is_System}));a&&(t.versionCode=a.Id)}else t.$message({message:e.Message,type:"error"})}))},getProject:function(){var t=this;(0,o.GetProjectListForTenant)({}).then((function(e){e.IsSucceed&&(t.projectList=Object.freeze(e.Data))}))},handleClick:function(t,e){this.setForm(t.name)},setForm:function(t){var e=this.versionList.find((function(e){return e.Id===t}));e&&(this.form.Name=e.Name,this.form.Sort=e.Sort,this.form.Is_System=e.Is_System,this.form.Id=e.Id)},handleCreate:function(){this.handleAdd(null,!0)},handleAdd:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.$refs["form"].validate((function(t){if(!t)return!1;if(!a&&e.form.Id)e.btnLoading=!0,(0,i.UpdateVersion)(e.form).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.getVersionList()):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}));else{e.btnAddLoading=!0;var r=e.form,o=(r.Id,(0,n.default)(r,l));(0,i.CreateVersion)(o).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.getVersionList(),e.setForm(e.activeName)):e.$message({message:t.Message,type:"error"}),e.btnAddLoading=!1}))}}))},handleDelete:function(){var t=this;this.$confirm("是否删除该版本?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.btnDelLoading=!0,(0,i.DeleteVersion)({Id:t.activeName}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.getVersionList(),t.activeName="0",t.$refs["form"].resetFields()):t.$message({message:e.Message,type:"error"}),t.btnDelLoading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))}}}},"01f6":function(t,e,a){},"020c":function(t,e,a){"use strict";a.r(e);var r=a("a4b9"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},"08201":function(t,e,a){"use strict";a.r(e);var r=a("db78"),n=a("a1cc");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("dc22");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"35c953a4",null);e["default"]=l.exports},"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=o,Math.easeInOutQuad=function(t,e,a,r){return t/=r/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=i(),l=t-o,s=20,u=0;e="undefined"===typeof e?500:e;var c=function(){u+=s;var t=Math.easeInOutQuad(u,o,l,e);n(t),u<e?r(c):a&&"function"===typeof a&&a()};c()}},"12da":function(t,e,a){"use strict";a.r(e);var r=a("27be"),n=a("3ba2");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("b5f7");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"42f01121",null);e["default"]=l.exports},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186"),n=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,r.GetGridByCode)({code:t,IsAll:a}).then((function(t){var r=t.IsSucceed,o=t.Data,l=t.Message;if(r){if(!o)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,o.Grid),s=a?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+o.Grid.Row_Number||n.tablePageSize[0]),i(e.columns)}else e.$message({message:l,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,r=t.type;this.queryInfo.Page="limit"===r?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===e){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"15fd":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=i,a("a4d3");var r=n(a("ccb5"));function n(t){return t&&t.__esModule?t:{default:t}}function i(t,e){if(null==t)return{};var a,n,i=(0,r.default)(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)a=o[n],-1===e.indexOf(a)&&{}.propertyIsEnumerable.call(t,a)&&(i[a]=t[a])}return i}},1679:function(t,e,a){},"1cc7":function(t,e,a){},"27be":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"cs-wrapper"},[a("div",{staticClass:"form-x"},[a("el-form",{ref:"form",attrs:{inline:"",model:t.form,"label-width":"180px"}},[a("el-row",{attrs:{type:"flex",justify:"center"}},[a("el-col",{staticStyle:{"text-align":"center"},attrs:{span:24}},t._l(t.fac,(function(e,r){return a("el-form-item",{key:r,attrs:{label:e.factoryName}},[a("el-select",{staticStyle:{"margin-right":"10px"},attrs:{filterable:"",placeholder:"请选择仓库",clearable:""},on:{change:function(a){return t.getLocation(a,e)}},model:{value:e.wValue,callback:function(a){t.$set(e,"wValue",a)},expression:"f.wValue"}},t._l(e.warehouse,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1),a("el-select",{attrs:{filterable:"",placeholder:"请选择库位",clearable:""},model:{value:e.lValue,callback:function(a){t.$set(e,"lValue",a)},expression:"f.lValue"}},t._l(e.vlocation,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)})),1)],1)],1)],1),a("footer",[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.bloading},on:{click:t.submit}},[t._v("确 认")])],1)])},n=[]},"35b9":function(t,e,a){"use strict";a.r(e);var r=a("b8fe"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},"3ba2":function(t,e,a){"use strict";a.r(e);var r=a("5b31"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},4166:function(t,e,a){"use strict";a.r(e);var r=a("baba"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},"418d":function(t,e,a){"use strict";a("01f6")},"48ba":function(t,e,a){},"4e82":function(t,e,a){"use strict";var r=a("23e7"),n=a("e330"),i=a("59ed"),o=a("7b0b"),l=a("07fa"),s=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),p=a("99f4"),h=a("1212"),g=a("ea83"),b=[],v=n(b.sort),S=n(b.push),y=c((function(){b.sort(void 0)})),P=c((function(){b.sort(null)})),I=f("sort"),w=!c((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var t,e,a,r,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(r=0;r<47;r++)b.push({k:e+r,v:a})}for(b.sort((function(t,e){return e.v-t.v})),r=0;r<b.length;r++)e=b[r].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),R=y||!P||!I||!w,x=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:u(e)>u(a)?1:-1}};r({target:"Array",proto:!0,forced:R},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(w)return void 0===t?v(e):v(e,t);var a,r,n=[],u=l(e);for(r=0;r<u;r++)r in e&&S(n,e[r]);d(n,x(t)),a=l(n),r=0;while(r<a)e[r]=n[r++];while(r<u)s(e,r++);return e}})},"51a8":function(t,e,a){},"5b31":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("2909"));a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var i=a("f887");e.default={data:function(){return{form:{value1:"",value2:""},loading:!1,bloading:!1,fac:[]}},mounted:function(){},methods:{init:function(t,e){var a=this;this.list=t,this.loading=!0,(0,i.FindTenantLocationList)({}).then((function(t){t.IsSucceed?(a.fac=t.Data.map((function(t){var e=[],a=[];return t.Warehouse_Location.forEach((function(t){t.Location.forEach((function(e){e.pid=t.Warehouse.Id})),e.push(t.Warehouse),a.push.apply(a,(0,n.default)(t.Location))})),{factoryName:t.Factory.Name,factoryId:t.Factory.Id,warehouse:e,location:a,vlocation:[],vwarehouse:[],wValue:"",lValue:""}})),1===e&&a.getInfo()):a.$message({message:t.Message,type:"error"}),a.loading=!1}))},getLocation:function(t,e){e.lValue="",e.vlocation=t?e.location.filter((function(e){return e.pid===t})):[]},getInfo:function(){var t=this;(0,i.FindAuxLocationList)({auxId:this.list[0].Id}).then((function(e){if(e.IsSucceed){var a=e.Data||[];a.forEach((function(e,a){var r=e.Factory_Id,n=e.Location_Id,i=e.Warehouse_Id,o=t.fac.find((function(t){return t.factoryId===r}));o&&(t.getLocation(i,o),o.wValue=i,o.lValue=n)}))}else t.$message({message:e.Message,type:"error"})}))},submit:function(){var t=this;this.bloading=!0;var e=[];this.fac.forEach((function(t){t.lValue&&e.push({Factory_Id:t.factoryId,Warehouse_Id:t.wValue,Location_Id:t.lValue})})),(0,i.SaveAuxLocation)({Aux_Id:this.list.map((function(t){return t.Id})),FactoryLocation:e}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.$emit("close")):t.$message({message:e.Message,type:"error"}),t.bloading=!1}))}}}},"5c0f":function(t,e,a){"use strict";a.r(e);var r=a("f7d1"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},"66fc":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:t.getTemplate}},[t._v("点击此处下载导入模板")])],1),a("upload",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},n=[]},7727:function(t,e,a){"use strict";a("48ba")},"7a2b":function(t,e,a){},8378:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CreateVersion=L,e.DelAuxCategoryEntity=S,e.DelAuxEntity=I,e.DelCategoryEntity=s,e.DelRawEntity=d,e.DeleteVersion=k,e.EditAuxEnabled=P,e.EditRawEnabled=c,e.ExportAuxForProject=q,e.ExportAuxList=C,e.ExportFindRawInAndOut=W,e.ExportInOutStoreReport=X,e.ExportPicking=N,e.ExportRawList=_,e.ExportRecSendProjectMaterialReport=Q,e.ExportRecSendProjectReport=J,e.ExportReceiving=A,e.ExportStagnationInventory=K,e.ExportStoreReport=H,e.FindInAndOutPageList=F,e.FindPickingNewPageList=E,e.FindPickingPageList=T,e.FindReceivingNewPageList=$,e.FindReceivingPageList=G,e.GetAuxCategoryDetail=v,e.GetAuxCategoryTreeList=g,e.GetAuxDetail=R,e.GetAuxFilterDataSummary=z,e.GetAuxForProjectDetail=B,e.GetAuxForProjectPageList=U,e.GetAuxPageList=w,e.GetAuxTemplate=x,e.GetAuxWHSummaryList=j,e.GetCategoryDetail=l,e.GetCategoryTreeList=i,e.GetCycleDate=V,e.GetList=D,e.GetRawDetail=m,e.GetRawPageList=f,e.GetRawTemplate=p,e.ImportAuxList=M,e.ImportRawList=h,e.SaveAuxCategoryEntity=b,e.SaveAuxEntity=y,e.SaveCategoryEntity=o,e.SaveRawEntity=u,e.UpdateVersion=O;var n=r(a("b775"));function i(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function o(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:t})}function u(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:t})}function c(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:t})}function d(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:t})}function f(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function m(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:t})}function p(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:t})}function h(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:t,timeout:12e5})}function g(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function b(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:t})}function v(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:t})}function S(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:t})}function y(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:t})}function I(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:t})}function w(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function R(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:t})}function x(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:t})}function M(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:t,timeout:12e5})}function _(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:t})}function C(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:t})}function D(t){return(0,n.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:t})}function L(t){return(0,n.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:t})}function O(t){return(0,n.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:t})}function k(t){return(0,n.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:t})}function A(t){return(0,n.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:t})}function N(t){return(0,n.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:t})}function G(t){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:t})}function $(t){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:t})}function T(t){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:t})}function E(t){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:t})}function F(t){return(0,n.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:t})}function V(t){return(0,n.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:t})}function W(t){return(0,n.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:t})}function j(t){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:t})}function U(t){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:t})}function B(t){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:t})}function q(t){return(0,n.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:t})}function z(t){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:t})}function K(t){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:t})}function H(t){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:t})}function J(t){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:t})}function Q(t){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:t})}function X(t){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:t})}},"867a":function(t,e,a){"use strict";var r=Math.log,n=Math.LOG10E;t.exports=Math.log10||function(t){return r(t)*n}},"86de":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container abs100"},[a("div",{staticClass:"cs-tabs"},[a("el-tabs",{on:{"tab-click":t.handleTabClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"原材料",name:t.statusMap.rawMaterial}}),a("el-tab-pane",{attrs:{label:"辅料",name:t.statusMap.auxiliaryMaterial}})],1),a("span",{staticClass:"cs-tab active",on:{click:t.setVersion}},[t._v("版本管理")])],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"left"},[a("LeftCard",{ref:"left",attrs:{"type-code":t.activeName},on:{nodeClick:t.nodeClick,refresh:t.refreshCategory,add:t.categoryAdd,getFirstTreeInitData:t.getFirstTreeInitData}})],1),a("div",{staticClass:"right"},[a("div",{staticClass:"right-top"},[a("div",{staticClass:"btn-x"},[a("b",{staticClass:"mr-10",staticStyle:{"white-space":"nowrap"}},[t._v(t._s(t.isRawMaterial?"原料库":"辅料库"))])]),a("div",{staticClass:"search-form"},[a("el-form",{ref:"searchForm",attrs:{model:t.searchForm,inline:"","label-width":"80px"}},[a("el-form-item",[t.isRawMaterial?t._e():a("el-button",{attrs:{disabled:!t.multipleSelection.length,type:"primary"},on:{click:function(e){return t.openCk(t.multipleSelection,2)}}},[t._v("编辑仓库")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新增")]),a("el-button",{attrs:{disabled:!t.multipleSelection.length},on:{click:t.handleChangeStatus}},[t._v("启用/停用")]),a("el-button",{attrs:{type:"danger",disabled:!t.multipleSelection.length},on:{click:function(e){return t.handleDelete(t.multipleSelection)}}},[t._v("删除")]),a("el-button",{on:{click:t.handleImport}},[t._v("导入")]),a("el-button",{attrs:{type:"success"},on:{click:t.handleExport}},[t._v("导出")])],1),a("el-form-item",{attrs:{label:"唯一编码",prop:"Materiel_Code"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"唯一编码",clearable:""},model:{value:t.searchForm.Materiel_Code,callback:function(e){t.$set(t.searchForm,"Materiel_Code",e)},expression:"searchForm.Materiel_Code"}})],1),a("el-form-item",{attrs:{label:t.isRawMaterial?"原料名称":"辅料名称",prop:"Materiel_Name"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:t.isRawMaterial?"原料名称":"辅料名称",clearable:""},model:{value:t.searchForm.Materiel_Name,callback:function(e){t.$set(t.searchForm,"Materiel_Name",e)},expression:"searchForm.Materiel_Name"}})],1),t.isRawMaterial?a("el-form-item",{attrs:{label:"材质",prop:"Material","label-width":"50px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"材质",clearable:""},model:{value:t.searchForm.Material,callback:function(e){t.$set(t.searchForm,"Material",e)},expression:"searchForm.Material"}})],1):t._e(),a("el-form-item",{attrs:{label:"规格",prop:"Spec","label-width":"50px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"规格",clearable:""},model:{value:t.searchForm.Spec,callback:function(e){t.$set(t.searchForm,"Spec",e)},expression:"searchForm.Spec"}})],1),a("el-form-item",{staticClass:"search-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleDepSearch}},[t._v("精准搜索")]),a("el-button",{on:{click:function(e){t.$refs["searchForm"].resetFields(),t.handleSearch()}}},[t._v("重置")])],1)],1)],1)]),a("div",{staticClass:"table-w"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","show-overflow":"",stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"45"}}),t._l(t.currentColumns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,align:e.Align,sortable:"","min-width":e.Width},scopedSlots:t._u(["SpecificationUsage"===e.Code?{key:"default",fn:function(a){var r=a.row;return[t._v(" "+t._s(0===r[e.Code]?"按需使用":1===r[e.Code]?"使用标准规格":2===r[e.Code]?"不使用标准规格":"")+" ")]}}:"Is_Enabled"===e.Code?{key:"default",fn:function(r){var n=r.row;return[a("span",[n[e.Code]?a("el-tag",{attrs:{type:"success"}},[t._v("是")]):a("el-tag",{attrs:{type:"danger"}},[t._v("否")])],1)]}}:"Dull_Date"===e.Code?{key:"default",fn:function(e){var r=e.row;return[a("span",[t._v(t._s(r.Dull_Date?r.Dull_Date+"天":""))])]}}:"Warning"===e.Code?{key:"default",fn:function(e){var r=e.row;return[r.Warning||r.Warning_Max?a("span",["板材"===r.BigTypeDescription||"型材"===r.BigTypeDescription||"钢卷"===r.BigTypeDescription?a("span",[t._v(" "+t._s(parseFloat((r.Warning/1e3).toFixed(3)))+" - "+t._s(parseFloat((r.Warning_Max/1e3).toFixed(3)))+" ")]):a("span",[t._v(t._s(r.Warning)+" - "+t._s(r.Warning_Max))])]):a("span",[t._v("-")])]}}:{key:"default",fn:function(a){var r=a.row;return[t._v(" "+t._s(t._f("displayValue")(r[e.Code]))+" ")]}}],null,!0)})]})),t.isRawMaterial?t._e():a("vxe-column",{attrs:{title:"默认仓库",align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openCk([r],1)}}},[t._v("编辑")])]}}],null,!1,1143057299)}),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"96"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleEdit(r)}}},[t._v("编辑")]),a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(e){return t.handleDelete([r])}}},[t._v("删除")])]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[t._v("已选"+t._s(t.multipleSelection.length)+"条数据 ")])],1),a("Pagination",{attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)])])]),a("transition",{attrs:{name:"dialog-fade"}},[t.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.width,top:"10vh"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a(t.currentComponent,{ref:"content",tag:"component",attrs:{"version-id":t.versionCode,type:t.isRawMaterial?0:1,"catalog-id":t.catalogId,"catalog-detail":t.catalogDetail},on:{close:t.handleClose,refreshCategory:t.refreshCategory,refreshPage:t.refreshPage,refresh:function(e){return t.fetchData(1)}}})],1):t._e()],1)],1)},n=[]},"88b0":function(t,e,a){"use strict";a("51a8")},93847:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"left-card"},[a("div",{staticClass:"left-card-title"},[a("div",{staticClass:"card-title"},[t._v(t._s(t.typeName))]),t.btnDisabled?t._e():a("div",{staticClass:"cs-btn-x"},[a("el-button",{attrs:{type:"primary"},on:{click:t.add}},[t._v("新增")])],1)]),a("div",{staticClass:"tree-x"},[a("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:t.treeLoading,expression:"treeLoading"}],ref:"tree",attrs:{"element-loading-text":"加载中...",props:{label:"Label",children:"Children"},data:t.treeData,"highlight-current":"","default-expand-all":"","current-node-key":t.currentKey,"empty-text":"暂无数据","node-key":"Id","expand-on-click-node":!1},on:{"node-click":t.handleNodeClick},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.node,n=e.data;return a("span",{staticClass:"custom-tree-node"},[a("svg-icon",{attrs:{"icon-class":r.expanded?"icon-folder-open":"icon-folder","class-name":"class-icon"}}),a("span",{class:["cs-label",{"is-active":t.currentKey===n.Id}],attrs:{title:r.label}},[t._v(t._s(r.label))]),t.currentKey!==n.Id||t.btnDisabled?t._e():a("span",{staticClass:"tree-btn-x"},[1===r.level?a("i",{staticClass:"cs-btn el-icon-plus",attrs:{title:"新增"},on:{click:function(e){return t.handleAdd(n)}}}):t._e(),a("i",{staticClass:"cs-btn el-icon-edit",staticStyle:{color:"#298DFF"},attrs:{title:"编辑"},on:{click:function(e){return t.handleEdit(n,r)}}}),Boolean(["板材","型材","钢卷","花纹板","结构辅料","维护辅料"].includes(n.Label))?t._e():a("i",{staticClass:"cs-btn el-icon-delete",staticStyle:{color:"red"},attrs:{title:"删除"},on:{click:function(e){return t.handleDelete(n)}}})])],1)}}])})],1)])},n=[]},"93aa":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=E,e.AuxInStoreExport=Q,e.AuxReturnByReceipt=Y,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=j,e.DeleteInStore=v,e.DeletePicking=Ct,e.ExportCheckReceipt=ft,e.ExportInstoreReceipt=dt,e.ExportMoneyAdjustOrder=Rt,e.ExportPicking=Mt,e.ExportProcess=Vt,e.ExportTestDetail=It,e.FindAuxPageList=U,e.FindRawPageList=A,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=X,e.GetAuxImportTemplate=T,e.GetAuxPageList=at,e.GetAuxPickOutStoreSubList=V,e.GetAuxProcurementDetails=rt,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=P,e.GetImportTemplate=L,e.GetInstoreDetail=b,e.GetMoneyAdjustDetailPageList=wt,e.GetOMALatestStatisticTime=D,e.GetOrderDetail=ot,e.GetPartyAs=y,e.GetPickLockStoreToChuku=Tt,e.GetPickPlate=Et,e.GetPickSelectPageList=Gt,e.GetPickSelectSubList=$t,e.GetPickingDetail=Ot,e.GetPickingTypeSettingDetail=kt,e.GetProjectListForTenant=nt,e.GetRawDetailByReceipt=lt,e.GetRawOrderList=it,e.GetRawPageList=I,e.GetRawPickOutStoreSubList=$,e.GetRawProcurementDetails=w,e.GetRawSurplusReturnStoreDetail=G,e.GetReturnPlate=Ft,e.GetStoreSelectPage=Dt,e.GetSuppliers=S,e.GetTestDetail=St,e.GetTestInStoreOrderList=bt,e.Import=O,e.ImportCheckReceipt=pt,e.ImportInstoreReceipt=mt,e.InStoreListSummary=ut,e.LockPicking=At,e.ManualAuxInStoreDetail=z,e.ManualInStoreDetail=h,e.MaterielAuxInStoreList=F,e.MaterielAuxManualInStore=J,e.MaterielAuxPurchaseInStore=K,e.MaterielAuxSubmitInStore=W,e.MaterielPartyAInStorel=H,e.MaterielRawInStoreList=i,e.MaterielRawInStoreListInSumNew=l,e.MaterielRawInStoreListNew=o,e.MaterielRawManualInStore=M,e.MaterielRawPartyAInStore=x,e.MaterielRawPurchaseInStore=R,e.MaterielRawSubmitInStore=s,e.MaterielRawSurplusInStore=_,e.OutStoreListSummary=ct,e.PartAInStoreDetail=p,e.PartyAInInStoreDetail=q,e.PurchaseAuxInStoreDetail=B,e.PurchaseInStoreDetail=m,e.RawInStoreExport=k,e.RawReturnByReceipt=st,e.RawSurplusReturnStore=N,e.SaveInStore=C,e.SavePicking=Lt,e.SetQualified=yt,e.SetTestDetail=Pt,e.StoreMoneyAdjust=gt,e.SubmitApproval=f,e.SubmitAuxApproval=d,e.SubmitInStore=ht,e.SubmitPicking=_t,e.SurplusInStoreDetail=g,e.UnLockPicking=Nt,e.UpdateInvoiceInfo=xt,e.Withdraw=u,e.WithdrawAux=c,e.WithdrawChecked=vt;var n=r(a("b775"));function i(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function o(t){return(0,n.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function l(t){return(0,n.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function u(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function c(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function d(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function f(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function m(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function p(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function h(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function g(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function b(t){return(0,n.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function v(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function S(t){return(0,n.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function y(t){return(0,n.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function I(t){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function w(t){return(0,n.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function R(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function x(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function M(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function _(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function C(t){return(0,n.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function D(t){return(0,n.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function L(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function O(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function k(t){return(0,n.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function A(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function N(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function G(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function $(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function T(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function E(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function F(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function V(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function W(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function j(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function U(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function B(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function q(t){return(0,n.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function z(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function K(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function H(t){return(0,n.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function J(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function Q(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function X(t){return(0,n.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function Y(t){return(0,n.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function at(t){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function rt(t){return(0,n.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function nt(t){return(0,n.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function it(t){return(0,n.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function ot(t){return(0,n.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function lt(t){return(0,n.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function st(t){return(0,n.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function ut(t){return(0,n.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function ct(t){return(0,n.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function dt(t){return(0,n.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function ft(t){return(0,n.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function mt(t){return(0,n.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function pt(t){return(0,n.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function ht(t){return(0,n.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function gt(t){return(0,n.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function bt(t){return(0,n.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function vt(t){return(0,n.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function St(t){return(0,n.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function yt(t){return(0,n.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function Pt(t){return(0,n.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function It(t){return(0,n.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function wt(t){return(0,n.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function Rt(t){return(0,n.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function xt(t){return(0,n.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function Mt(t){return(0,n.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function _t(t){return(0,n.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function Ct(t){return(0,n.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function Dt(t){return(0,n.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function Lt(t){return(0,n.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function Ot(t){return(0,n.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function kt(t){return(0,n.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function At(t){return(0,n.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function Nt(t){return(0,n.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function Gt(t){return(0,n.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function $t(t){return(0,n.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function Tt(t){return(0,n.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function Et(t){return(0,n.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function Ft(t){return(0,n.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function Vt(t){return(0,n.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},9460:function(t,e,a){"use strict";a("1679")},a1cc:function(t,e,a){"use strict";a.r(e);var r=a("dea0"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},a3a2e:function(t,e,a){"use strict";var r=a("23e7"),n=a("e330"),i=a("5926"),o=a("408a"),l=a("1148"),s=a("867a"),u=a("d039"),c=RangeError,d=String,f=isFinite,m=Math.abs,p=Math.floor,h=Math.pow,g=Math.round,b=n(1.1.toExponential),v=n(l),S=n("".slice),y="-6.9000e-11"===b(-69e-12,4)&&"1.25e+0"===b(1.255,2)&&"1.235e+4"===b(12345,3)&&"3e+1"===b(25,0),P=function(){return u((function(){b(1,1/0)}))&&u((function(){b(1,-1/0)}))},I=function(){return!u((function(){b(1/0,1/0),b(NaN,1/0)}))},w=!y||!P()||!I();r({target:"Number",proto:!0,forced:w},{toExponential:function(t){var e=o(this);if(void 0===t)return b(e);var a=i(t);if(!f(e))return String(e);if(a<0||a>20)throw new c("Incorrect fraction digits");if(y)return b(e,a);var r,n,l,u,P="";if(e<0&&(P="-",e=-e),0===e)n=0,r=v("0",a+1);else{var I=s(e);n=p(I);var w=h(10,n-a),R=g(e/w);2*e>=(2*R+1)*w&&(R+=1),R>=h(10,a+1)&&(R/=10,n+=1),r=d(R)}return 0!==a&&(r=S(r,0,1)+"."+S(r,1)),0===n?(l="+",u="0"):(l=n>0?"+":"-",u=d(m(n))),r+="e"+l+u,P+r}})},a4b9:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("15fd"));a("99af"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var i=r(a("c14f")),o=r(a("1da1")),l=a("8378"),s=["Children"];e.default={props:{typeCode:{type:String,default:""}},data:function(){return{filterText:"",treeLoading:!1,treeData:[],currentKey:""}},computed:{typeName:function(){return this.isRawMaterial?"原料分类":"辅料分类"},isRawMaterial:function(){return"0"===this.typeCode},btnDisabled:function(){return 1===parseInt(this.$route.query.status)}},watch:{typeCode:function(){this.getTreeList()}},created:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTreeList();case 1:return e.a(2)}}),e)})))()},methods:{getTreeList:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){var a;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return t.treeLoading=!0,a=t.isRawMaterial?l.GetCategoryTreeList:l.GetAuxCategoryTreeList,e.n=1,a({}).then((function(e){e.IsSucceed?(t.treeData=[{Label:"全部",Id:"all",Data:{}}].concat(e.Data),t.treeData.length&&(t.currentKey=t.treeData[0].Id,t.$nextTick((function(e){t.$refs["tree"].setCurrentKey(t.currentKey);var a=t.$refs["tree"].getNode(t.currentKey);t.$emit("nodeClick",{data:t.treeData[0].Data.Category||t.treeData[0].Data,node:a}),t.$emit("getFirstTreeInitData",t.getFirstTreeData())})))):t.$message({message:e.Message,type:"error"}),t.treeLoading=!1}));case 1:return e.a(2)}}),e)})))()},add:function(){this.$emit("add",{})},handleEdit:function(t,e){this.$emit("add",{data:t,isEdit:!0,node:e})},handleAdd:function(t){this.$emit("add",{data:t,isFromTree:!0})},handleDelete:function(t){var e=this;this.$confirm("此操作将删除该分类, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a;a=e.isRawMaterial?l.DelCategoryEntity:l.DelAuxCategoryEntity,a({Id:t.Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleNodeButtonDelete:function(){},handleOpenAddEdit:function(){},handleNodeClick:function(t,e){var a=t.Data.Category||t.Data;this.currentKey=t.Id,this.$emit("nodeClick",{data:a,node:e})},getFirstTreeData:function(){return this.treeData.map((function(t){t.Children;var e=(0,n.default)(t,s);return e}))}}}},a60f:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"唯一编码",prop:"InputId"}},[a("el-input",{attrs:{clearable:"",maxlength:"30","show-word-limit":""},model:{value:t.form.InputId,callback:function(e){t.$set(t.form,"InputId","string"===typeof e?e.trim():e)},expression:"form.InputId"}})],1),t.PShow?a("el-form-item",{attrs:{rules:0===t.type?[{required:!0,message:"请选择",trigger:"change"}]:[],label:"上级",prop:"PId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",disabled:t.PDisabled,clearable:"",placeholder:"请选择"},on:{change:t.PChange},model:{value:t.form.PId,callback:function(e){t.$set(t.form,"PId",e)},expression:"form.PId"}},t._l(t.options,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Label,value:t.Id,disabled:t.disabled}})})),1)],1):t._e(),a("el-form-item",{attrs:{label:"分类名称",prop:"Name"}},[a("el-input",{attrs:{maxlength:"20","show-word-limit":"",clearable:"",disabled:t.NameDisabled},model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name","string"===typeof e?e.trim():e)},expression:"form.Name"}})],1),t.SectionParametersShow?a("el-form-item",{attrs:{label:"截面参数",prop:"IsSectionParameters"}},[a("el-switch",{attrs:{disabled:t.SectionParametersDisabled},model:{value:t.form.IsSectionParameters,callback:function(e){t.$set(t.form,"IsSectionParameters",e)},expression:"form.IsSectionParameters"}})],1):t._e()],1),t.SectionParametersShow&&t.form.IsSectionParameters?a("div",[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-button",{attrs:{type:"primary",size:"mini",disabled:t.SectionParametersDisabled},on:{click:t.handleAddParam}},[t._v("添加")]),a("el-button",{attrs:{type:"danger",size:"mini",plain:"",disabled:t.SectionParametersDisabled||0===t.multipleSelection.length},on:{click:t.deleteSelectedRows}},[t._v("删除")])],1),a("el-col",[a("el-table",{staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:t.tableData},on:{"selection-change":t.handleTableData1Change}},[t._v(" > "),a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{align:"center",prop:"Name",label:"*参数名称"},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.row;return[a("el-input",{attrs:{maxlength:"20",placeholder:"请输入",disabled:t.SectionParametersDisabled},model:{value:r.Name,callback:function(e){t.$set(r,"Name","string"===typeof e?e.trim():e)},expression:"row.Name"}},[a("i",{staticClass:"el-input__icon el-icon-edit",attrs:{slot:"suffix"},slot:"suffix"})])]}}],null,!1,3468139950)}),a("el-table-column",{attrs:{align:"center",prop:"Unit",label:"单位"}})],1)],1)],1)],1):t._e(),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:t.handleClose}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},n=[]},a7b3:function(t,e,a){"use strict";a("1cc7")},b173:function(t,e,a){"use strict";a.r(e);var r=a("c1ba"),n=a("baa8");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("418d");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"55e1f5c6",null);e["default"]=l.exports},b5f7:function(t,e,a){"use strict";a("7a2b")},b8fe:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("5530"));a("4de4"),a("7db0"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("4e82"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var i=a("8378");e.default={props:{type:{type:Number,default:0}},data:function(){return{form:{Id:"",Name:"",PId:"",PName:"",InputId:"",IsSectionParameters:!1},rules:{InputId:[{required:!0,message:"请输入",trigger:"blur"}],Name:[{required:!0,message:"请输入",trigger:"blur"}],IsSectionParameters:[{required:!0,message:"请输入",trigger:"change"}]},options:[],btnLoading:!1,PDisabled:!1,PShow:!1,NameDisabled:!1,SectionParametersShow:!1,SectionParametersDisabled:!1,IsHavaMateriel:!1,tableData:[{Name:"厚度",Unit:"mm",Sort:1},{Name:"宽度",Unit:"mm",Sort:2}],multipleSelection:[]}},mounted:function(){this.options=this.$parent.$parent.getFirstTreeData().filter((function(t){return"all"!==t.Id}))},methods:{handleAddParam:function(){this.tableData.length>=5?this.$message({message:"截面参数最多配置5个",type:"warning"}):this.tableData.push({Name:"",Unit:"mm",Sort:this.tableData.length+1})},deleteSelectedRows:function(t){var e=this;if(this.tableData.length-this.multipleSelection.length<1)this.$message({message:"截面参数最少配置1个",type:"warning"});else for(var a=function(){var t=e.multipleSelection[r],a=e.tableData.findIndex((function(e){return t.Sort===e.Sort}));-1!==a&&e.tableData.splice(a,1)},r=this.multipleSelection.length-1;r>=0;r--)a()},handleTableData1Change:function(t){this.multipleSelection=t},handleClose:function(){this.$emit("close")},handleSubmit:function(){var t=this;this.$refs["form"].validate((function(e){if(!e)return!1;t.tableData.map((function(e){e.Name||t.$message({message:"参数名称不能为空",type:"warning"})}));var a,r=t.form,o=r.Id,l=r.Name,s=r.PId,u=r.PName,c=r.InputId,d=r.IsSectionParameters,f={Id:o,Name:l,Code:c,Pid:s,Is_Open_Section_Parameter:d,Big_Type:"板材"===l||"板材"===u||"结构辅料"===l||"结构辅料"===u?1:"型材"===l||"型材"===u||"维护辅料"===l||"维护辅料"===u?2:"钢卷"===l||"钢卷"===u?3:99,Is_System:!("板材"!==l&&"型材"!==l&&"钢卷"!==l&&"结构辅料"!==l&&"维护辅料"!==l)},m=[];d&&(m=t.tableData.map((function(t,e){return{Id:t.Id||"",Category_Id:t.Category_Id||o,Name:t.Name,Unit:t.Unit,Sort:t.Sort}}))),0===t.type?a=i.SaveCategoryEntity:(a=i.SaveAuxCategoryEntity,delete f.Is_Open_Section_Parameter),a(0===t.type?{Category:f,Params:m}:(0,n.default)({},f)).then((function(e){e.IsSucceed?(t.$message({message:"".concat(t.isEdit?"修改":"保存","成功"),type:"success"}),t.$emit("refreshCategory")):t.$message({message:e.Message,type:"error"}),t.handleClose()}))}))},setCategory:function(t,e,a,r){if(this.isEdit=e,e){var n,i=t.Data.Category||t.Data,o=i.Code,l=i.Name,s=i.Pid,u=i.Id,c=i.Is_Open_Section_Parameter;r&&1===r.level&&(this.options=this.options.filter((function(t){return t.Id!==u})));var d=s?null===(n=this.options.find((function(t){return t.Id===s})))||void 0===n?void 0:n.Label:"";this.form.Name=l,this.form.InputId=o,this.form.PId=s,this.form.PName=d,this.form.Id=u,this.form.IsSectionParameters=c,this.options=this.options.map((function(t){return"板材"===t.Label||"型材"===t.Label||"钢卷"===t.Label||"结构辅料"===t.Label||"维护辅料"===t.Label?t.disabled=!0:t.disabled=!1,t})),"板材"!==this.form.Name&&"型材"!==this.form.Name&&"钢卷"!==this.form.Name&&"结构辅料"!==this.form.Name&&"维护辅料"!==this.form.Name&&(this.PShow=!0),"板材"!==d&&"型材"!==d&&"钢卷"!==d&&"结构辅料"!==d&&"维护辅料"!==d||(this.PDisabled=!0),t.Children.length>0&&(this.PDisabled=!0),["板材","型材","钢卷","花纹板","结构辅料","维护辅料"].includes(this.form.Name)&&(this.NameDisabled=!0),"型材"!==this.form.Name&&"型材"!==d||(this.SectionParametersShow=!0),this.form.IsSectionParameters&&this.getCategoryDetail(),this.SectionParametersDisabled=t.Data.Is_Hava_Materiel}else a&&(this.form.PId=t.Id,this.form.PName=t.Label,a&&(this.PDisabled=!0),"型材"!==this.form.Name&&"型材"!==this.form.PName||(this.SectionParametersShow=!0)),this.PShow=!0},getCategoryDetail:function(){var t=this;(0,i.GetCategoryDetail)({categoryId:this.form.Id}).then((function(e){e.IsSucceed?t.tableData=e.Data.Params.sort((function(t,e){return t.Sort-e.Sort})):t.$message({message:e.Message,type:"error"})}))},PChange:function(t){var e,a=t?null===(e=this.options.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Label:"";this.form.PName=a,this.SectionParametersShow="型材"===a}}}},b98d:function(t,e,a){"use strict";a.r(e);var r=a("a60f"),n=a("35b9");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("9460");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"3c415d10",null);e["default"]=l.exports},baa8:function(t,e,a){"use strict";a.r(e);var r=a("0058"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},baba:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=r(a("5530")),i=r(a("c14f")),o=r(a("1da1")),l=r(a("bee27")),s=r(a("15ac")),u=a("c685"),c=r(a("333d")),d=r(a("b98d")),f=r(a("08201")),m=r(a("df18")),p=a("8378"),h=r(a("12da")),g=a("ed08"),b=r(a("b173"));e.default={name:"PROMaterialWarehouse",components:{Version:b.default,CK:h.default,LeftCard:l.default,AddCategory:d.default,AddRawForm:f.default,ImportFile:m.default,Pagination:c.default},mixins:[s.default],data:function(){return{activeName:"0",statusMap:{rawMaterial:"0",auxiliaryMaterial:"1"},tablePageSize:u.tablePageSize,tbLoading:!1,dialogVisible:!1,versionCode:"",title:"",content:"",searchForm:{Materiel_Code:"",Materiel_Name:"",Material:"",Spec:""},catalogId:"",catalogDetail:"",width:"30%",currentComponent:"",columns:[],currentColumns:[],tbData:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1,PageSize:u.tablePageSize[0]},total:0,options:[]}},computed:{isRawMaterial:function(){return this.statusMap.rawMaterial===this.activeName},isSteelPlate:function(){return 1===this.catalogDetail.Big_Type&&this.isRawMaterial}},watch:{isSteelPlate:function(){this.isRawMaterial&&this.getColumn()}},provide:function(){var t=this;return{material:function(){return t.isRawMaterial}}},methods:{getColumn:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig(t.isRawMaterial?"pro_raw_material_list":"pro_auxiliary_material_list");case 1:t.currentColumns=t.columns.filter((function(e){return t.isSteelPlate?"Spec"!==e.Code:"Thick"!==e.Code}));case 2:return e.a(2)}}),e)})))()},refreshPage:function(){this.getVersionList()},handleVersion:function(t){this.versionCode=t.Id},setVersion:function(){this.currentComponent="Version",this.width="40%",this.title="版本管理",this.dialogVisible=!0,this.$nextTick((function(t){}))},handleSearch:function(){this.Is_Accurate_Search=!1,this.fetchData(1)},handleDepSearch:function(){this.Is_Accurate_Search=!0,this.fetchData(1)},fetchData:function(t){var e,a=this;e=this.isRawMaterial?p.GetRawPageList:p.GetAuxPageList,t&&(this.queryInfo.Page=t),this.tbLoading=!0,e((0,n.default)((0,n.default)((0,n.default)({Version_Id:this.versionCode,Is_Accurate_Search:this.Is_Accurate_Search},this.queryInfo),this.searchForm),{},{Category_Id:this.catalogId})).then((function(t){t.IsSucceed?(a.tbData=t.Data.Data.map((function(t){return t})),a.total=t.Data.TotalCount,a.multipleSelection=[]):a.$message({message:t.Message,type:"error"})})).finally((function(){a.tbLoading=!1}))},handleAdd:function(){this.addRawForm(!1)},handleEdit:function(t){this.addRawForm(!0,t)},addRawForm:function(t,e){var a=this;t?(this.catalogDetail.Name=null===e||void 0===e?void 0:e.BigTypeDescription,this.catalogDetail.Big_Type=null===e||void 0===e?void 0:e.Big_Type):this.catalogDetail.Id||(this.catalogDetail.Name=null,this.catalogDetail.Big_Type=null),this.currentComponent="AddRawForm",this.width="60%",this.isRawMaterial?this.title=t?"编辑原料":"新增原料":this.title=t?"编辑辅料":"新增辅料",this.dialogVisible=!0,this.$nextTick((function(r){a.$refs["content"].init(t,e)}))},openCk:function(t,e){var a=this;this.currentComponent="CK",this.width="60%",this.title="默认仓库",this.dialogVisible=!0,this.$nextTick((function(r){a.$refs["content"].init(t,e)}))},handleImport:function(){this.currentComponent="ImportFile",this.width="40%",this.title=this.isRawMaterial?"原料导入":"辅料导入",this.dialogVisible=!0},handleExport:function(){var t=this,e=this.isRawMaterial?p.ExportRawList:p.ExportAuxList;e({}).then((function(e){e.IsSucceed?window.open((0,g.combineURL)(t.$baseUrl,e.Data),"_blank"):t.$message({message:e.Message,type:"error"})}))},handleChangeStatus:function(){var t=this;this.$confirm("是否启用/停用所选".concat((this.isRawMaterial,"原料"),"?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=t.isRawMaterial?p.EditRawEnabled:p.EditAuxEnabled;e({ids:t.multipleSelection.map((function(t){return t.Id}))}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"操作成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},tbSelectChange:function(t){this.multipleSelection=t.records},handleDelete:function(t){var e=this;this.$confirm("是否删除选中数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a=t.map((function(t){return t.Id})),r=e.isRawMaterial?p.DelRawEntity:p.DelAuxEntity;r({ids:a}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},nodeClick:function(t){var e=t.data;t.node;this.catalogId=e.Id,this.catalogDetail=e,this.$refs["searchForm"].resetFields(),this.fetchData(1)},refreshCategory:function(){this.$refs["left"].getTreeList()},categoryAdd:function(t){var e=this,a=t.data,r=void 0===a?null:a,n=t.isEdit,i=void 0!==n&&n,o=t.isFromTree,l=void 0!==o&&o,s=t.node;this.currentComponent="AddCategory",this.width="30%",this.title=i?"编辑分类":"新增分类",this.dialogVisible=!0,this.$nextTick((function(t){e.$refs["content"].setCategory(r,i,l,s)}))},handleClose:function(){this.dialogVisible=!1},handleTabClick:function(t){var e=this;return(0,o.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(t.name===e.activeName){a.n=2;break}return a.n=1,e.getColumn();case 1:e.$refs["searchForm"].resetFields(),e.fetchData(1);case 2:return a.a(2)}}),a)})))()},getFirstTreeInitData:function(t){this.options=t,this.getColumn()},getFirstTreeData:function(){return this.$refs["left"].getFirstTreeData()}}}},bee27:function(t,e,a){"use strict";a.r(e);var r=a("93847"),n=a("020c");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("a7b3");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"9a1306e2",null);e["default"]=l.exports},c1ba:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cs-wrapper"},[a("el-tabs",{staticStyle:{height:"400px"},attrs:{"tab-position":"left"},on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.versionList,(function(t){return a("el-tab-pane",{key:t.Id,attrs:{name:t.Id,label:t.Name}})})),1),a("div",{staticClass:"cs-main"},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"版本名称",prop:"Name"}},[a("el-input",{model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name","string"===typeof e?e.trim():e)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"是否默认项目",prop:"Is_System"}},[a("el-radio",{attrs:{label:!0},model:{value:t.form.Is_System,callback:function(e){t.$set(t.form,"Is_System",e)},expression:"form.Is_System"}},[t._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:t.form.Is_System,callback:function(e){t.$set(t.form,"Is_System",e)},expression:"form.Is_System"}},[t._v("否")])],1),a("el-form-item",{attrs:{label:"序号",prop:"Sort"}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{min:0},model:{value:t.form.Sort,callback:function(e){t.$set(t.form,"Sort",e)},expression:"form.Sort"}})],1)],1),a("footer",[a("el-button",{attrs:{loading:t.btnAddLoading},on:{click:t.handleCreate}},[t._v("新增")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleAdd}},[t._v("保存")]),"0"!==t.activeName?a("el-button",{attrs:{loading:t.btnDelLoading,type:"danger"},on:{click:t.handleDelete}},[t._v("删除")]):t._e()],1)],1)],1)},n=[]},c96b:function(t,e,a){},ccb5:function(t,e,a){"use strict";function r(t,e){if(null==t)return{};var a={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;a[r]=t[r]}return a}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r},db78:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"140px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"唯一编码",prop:"Code",rules:[{required:"true"!==t.MaterielAutoGenCode,message:"请输入",trigger:"blur"}]}},[a("el-input",{attrs:{maxlength:"30",placeholder:"true"===t.MaterielAutoGenCode?"自动生成":"请输入",disabled:"true"===t.MaterielAutoGenCode},model:{value:t.form.Code,callback:function(e){t.$set(t.form,"Code","string"===typeof e?e.trim():e)},expression:"form.Code"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:0===t.type?"原料名称":"辅料名称",prop:"Name"}},[a("el-input",{attrs:{maxlength:"40",placeholder:"请输入",disabled:t.isEdit&&0===t.type},on:{input:t.nameChange},model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name","string"===typeof e?e.trim():e)},expression:"form.Name"}})],1)],1),0===t.type?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{disabled:t.isEdit,maxlength:"30",placeholder:"请输入"},model:{value:t.form.Material,callback:function(e){t.$set(t.form,"Material","string"===typeof e?e.trim():e)},expression:"form.Material"}})],1)],1):t._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所属分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"cs-select",attrs:{disabled:"","tree-params":t.treeParams},model:{value:t.form.Category_Id,callback:function(e){t.$set(t.form,"Category_Id",e)},expression:"form.Category_Id"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计量单位",prop:"Measure_Unit"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:t.form.Measure_Unit,callback:function(e){t.$set(t.form,"Measure_Unit",e)},expression:"form.Measure_Unit"}},t._l(t.unitList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Display_Name}})})),1)],1)],1),0===t.type?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"核算单位",prop:"Accounting_Unit"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:t.form.Accounting_Unit,callback:function(e){t.$set(t.form,"Accounting_Unit",e)},expression:"form.Accounting_Unit"}},t._l(t.valuationUnitList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Display_Name}})})),1)],1)],1):t._e(),t.ThickShow?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"规格(厚度mm)",prop:"Thick"}},[a("el-input",{staticClass:"input-number",attrs:{placeholder:"请输入",disabled:t.isEdit,type:"number",min:"0",step:"any"},on:{change:function(e){return t.numChange(e,"Thick")}},model:{value:t.form.Thick,callback:function(e){t.$set(t.form,"Thick","string"===typeof e?e.trim():e)},expression:"form.Thick"}})],1)],1):t._e(),t.SectionParametersShow||t.ThickShow?t._e():a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{attrs:{maxlength:"30",placeholder:"请输入",disabled:t.isEdit&&0===t.type},model:{value:t.form.Spec,callback:function(e){t.$set(t.form,"Spec","string"===typeof e?e.trim():e)},expression:"form.Spec"}})],1)],1),t.SpecificGravityShow&&!t.versionList.length?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"比重",prop:"Specific_Gravity"}},[a("el-input",{staticClass:"input-number",style:t.SpecificGravityUnitShow?"width: 80%":"width: 100%",attrs:{placeholder:"请输入",type:"number",step:"any",min:"0"},on:{change:function(e){return t.numChange(e,"Specific_Gravity")}},model:{value:t.form.Specific_Gravity,callback:function(e){t.$set(t.form,"Specific_Gravity","string"===typeof e?e.trim():e)},expression:"form.Specific_Gravity"}}),t.SpecificGravityUnitShow?a("span",{staticClass:"cs-unit"},[t._v("kg/mm")]):t._e()],1)],1):t._e(),a("el-col",{staticClass:"cs-warn-form",attrs:{span:12}},[t.SpecificGravityShow?[a("el-form-item",{attrs:{label:"预警值",prop:"Warning"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{max:t.WarnMaxNumber,precision:3,placeholder:"最小值"},model:{value:t.WarnNumber,callback:function(e){t.WarnNumber=e},expression:"WarnNumber"}}),a("span",{staticClass:"cs-unit"},[t._v("t")]),a("span",{staticClass:"middle"},[t._v(" - ")]),a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:t.WarnNumber,precision:3,placeholder:"最大值"},model:{value:t.WarnMaxNumber,callback:function(e){t.WarnMaxNumber=e},expression:"WarnMaxNumber"}}),a("span",{staticClass:"cs-unit"},[t._v("t")])],1)]:[a("el-form-item",{attrs:{label:"预警值",prop:"Warning"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{max:t.WarnMaxNumber,precision:0,placeholder:"最小值"},model:{value:t.WarnNumber,callback:function(e){t.WarnNumber=e},expression:"WarnNumber"}}),a("span",{staticClass:"middle"},[t._v(" - ")]),a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:t.WarnNumber,precision:0,placeholder:"最大值"},model:{value:t.WarnMaxNumber,callback:function(e){t.WarnMaxNumber=e},expression:"WarnMaxNumber"}})],1)]],2),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"呆滞时间提醒",prop:"Dull_Date"}},[a("el-input",{staticClass:"cs-input",staticStyle:{width:"80%"},attrs:{min:0,placeholder:"请输入",oninput:"value=value.replace(/[^\\d]+/g,'')"},model:{value:t.form.Dull_Date,callback:function(e){t.$set(t.form,"Dull_Date",e)},expression:"form.Dull_Date"}}),a("span",{staticClass:"cs-unit"},[t._v("天")])],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:100,type:"textarea"},model:{value:t.form.Remark,callback:function(e){t.$set(t.form,"Remark",e)},expression:"form.Remark"}})],1)],1),a("el-col",{attrs:{span:24}},[t.SectionParametersShow?a("el-form-item",{attrs:{label:"规格"}},[a("el-row",[a("el-col",[a("el-table",{staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:t.tableDataBox}},t._l(t.tableData,(function(e,r){return a("el-table-column",{key:r,attrs:{label:e.Name,align:"center","min-width":160}},[[a("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"100%"},attrs:{min:0,disabled:t.IsHaveStore,maxlength:"20",placeholder:"请输入",clearable:""},model:{value:e.Value,callback:function(a){t.$set(e,"Value",a)},expression:"item.Value"}},[a("i",{staticClass:"el-input__icon el-icon-edit",attrs:{slot:"suffix"},slot:"suffix"})])]],2)})),1)],1)],1)],1):t._e()],1),t.SpecificGravityShow?a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"cs-strong",attrs:{label:"比重维护"}})],1):t._e(),t.SpecificGravityShow?t._l(t.versionList,(function(e,r){return a("el-col",{key:r,attrs:{span:8}},[a("el-form-item",{attrs:{label:e.Name+(e.Is_System?"(默认)":"")}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{placeholder:"请输入",clearable:""},model:{value:e.Specific_Gravity,callback:function(a){t.$set(e,"Specific_Gravity",a)},expression:"items.Specific_Gravity"}})],1)],1)})):t._e(),a("el-col",{attrs:{span:24}},[a("el-form-item",[a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)])],1)],2)],1)},n=[]},dc22:function(t,e,a){"use strict";a("c96b")},dca8:function(t,e,a){"use strict";var r=a("23e7"),n=a("bb2f"),i=a("d039"),o=a("861d"),l=a("f183").onFreeze,s=Object.freeze,u=i((function(){s(1)}));r({target:"Object",stat:!0,forced:u,sham:!n},{freeze:function(t){return s&&o(t)?s(l(t)):t}})},dea0:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("5530")),i=r(a("15fd")),o=r(a("c14f")),l=r(a("1da1"));a("d9e2"),a("7db0"),a("d81d"),a("14d9"),a("13d5"),a("4e82"),a("e9f5"),a("f665"),a("ab43"),a("9485"),a("a9e3"),a("a3a2e"),a("b680"),a("d3b7"),a("ac1f"),a("466d"),a("5319");var s=a("6186"),u=a("4744"),c=a("8378"),d=["Specific_Gravity_String"];e.default={props:{catalogDetail:{type:Object,default:function(){}},type:{type:Number,default:0},versionId:{type:String,default:""}},data:function(){return{versionList:[],form:{Code:"",Name:"",Material:"",Measure_Unit:"",Accounting_Unit:"",Thick:"",Spec:"",Specific_Gravity:"",Big_Type:this.catalogDetail.Big_Type,Category_Id:this.catalogDetail.Id,Warning:null,Warning_Max:null,Remark:"",Dull_Date:"",Spec_Type:this.catalogDetail.Is_Open_Section_Parameter?1:2},tableDataBox:[{}],tableData:[{Name:"厚度",Unit:"mm",Sort:1,Value:""},{Name:"宽度",Unit:"mm",Sort:2,Value:""}],tableDataTemp:[],multipleSelection1:[],multipleSelection2:[],btnLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},rules:{Warning:[{validator:this.checkValues,trigger:"change"}],Name:[{required:!0,message:"请输入",trigger:"blur"}],Measure_Unit:[{required:!0,message:"请选择",trigger:"change"}],Thick:[{required:!0,message:"请输入",trigger:"blur"}],Spec:[{required:!0,message:"请输入",trigger:"blur"}]},unitList:[],valuationUnitList:[],options:[],isEdit:!1,IsHaveStore:!1,MaterielAutoGenCode:"false"}},computed:{SectionParametersShow:function(){return this.catalogDetail.Is_Open_Section_Parameter},SpecificGravityShow:function(){var t=this;if("板材"===this.catalogDetail.Name||"型材"===this.catalogDetail.Name||"钢卷"===this.catalogDetail.Name)return!0;var e,a=this.catalogDetail.Pid?null===(e=this.options.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";return"板材"===a||"型材"===a||"钢卷"===a},SpecificGravityUnitShow:function(){var t=this;if("板材"===this.catalogDetail.Name||"型材"===this.catalogDetail.Name)return!0;var e,a=this.catalogDetail.Pid?null===(e=this.options.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";return"板材"===a||"型材"===a},ThickShow:function(){var t=this;if("板材"===this.catalogDetail.Name)return!0;var e,a=this.catalogDetail.Pid?null===(e=this.options.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";return"板材"===a},WarnNumber:{get:function(){if(void 0!==this.WarnMaxNumber)return void 0!==this.form.Warning&&null!==this.form.Warning?this.SpecificGravityShow?parseFloat((this.form.Warning/1e3).toFixed(3)):this.form.Warning:this.WarnMaxNumber?0:void 0},set:function(t){var e=this;this.form.Warning=void 0!==t&&null!==t?t*(this.SpecificGravityShow?1e3:1):null,this.$nextTick((function(){e.validateFields()}))}},WarnMaxNumber:{get:function(){return void 0!==this.form.Warning_Max&&null!==this.form.Warning_Max?this.SpecificGravityShow?parseFloat((this.form.Warning_Max/1e3).toFixed(3)):this.form.Warning_Max:void 0},set:function(t){var e=this;this.form.Warning_Max=void 0!==t&&null!==t?t*(this.SpecificGravityShow?1e3:1):null,this.$nextTick((function(){e.validateFields()}))}}},watch:{},created:function(){this.getBaseData()},mounted:function(){this.getCategoryOption(),this.getVersionList(),this.options=this.$parent.$parent.getFirstTreeData()},methods:{checkValues:function(t,e,a){var r=this.form,n=r.Warning,i=r.Warning_Max;!n&&0!==n||i?a():a(new Error("请输入预警值最大值"))},validateFields:function(){this.$refs.form.validateField("Warning"),this.$refs.form.validateField("Warning_Max")},nameChange:function(t){this.form.Name=t.replace(/\s*/g,"")},getBaseData:function(){var t=this;(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"unit"}).then((function(e){e.IsSucceed?t.unitList=e.Data:t.$message({message:e.Message,type:"error"})})),(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"ValuationUnit"}).then((function(e){e.IsSucceed?t.valuationUnitList=e.Data:t.$message({message:e.Message,type:"error"})})),(0,u.GetPreferenceSettingValue)({code:"Materiel_AutoGen_Code"}).then((function(e){e.IsSucceed?t.MaterielAutoGenCode=e.Data:t.$message({message:e.Message,type:"error"})}))},numChange:function(t,e){if(t&&t<0)switch(e){case"Thick":this.form.Thick="";break;case"Specific_Gravity":this.form.Specific_Gravity="";break;case"Warning":this.form.Warning="";break}},init:function(t,e){var a=this;return(0,l.default)((0,o.default)().m((function r(){return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:if(a.isEdit=t,!a.SectionParametersShow){r.n=1;break}return r.n=1,(0,c.GetCategoryDetail)({categoryId:a.catalogDetail.Id}).then((function(t){t.IsSucceed?a.tableDataTemp=t.Data.Params.sort((function(t,e){return t.Sort-e.Sort})):a.$message({message:t.Message,type:"error"})}));case 1:!t&&(a.tableData=a.tableDataTemp),t&&a.setInfo(e);case 2:return r.a(2)}}),r)})))()},getVersionList:function(){var t=this;(0,c.GetList)({}).then((function(e){e.IsSucceed?t.versionList=e.Data.map((function(t){return t.Specific_Gravity=void 0,t})):t.$message({message:e.Message,type:"error"})}))},setInfo:function(t){var e,a=this,r=t.Id;this.isEdit=!0,e=0===this.type?c.GetRawDetail:c.GetAuxDetail,e({id:r}).then((function(t){if(t.IsSucceed){var e=t.Data,r=e.Specific_Gravity_String,n=(0,i.default)(e,d);if(a.SectionParametersShow){var o=n.Spec?n.Spec.split("*"):[];a.tableData=a.tableDataTemp.map((function(t,e){return a.$set(t,"Value",o[e]),t}))}n.Specific_Gravity=r,a.versionList=((null===n||void 0===n?void 0:n.Versions)||[]).sort((function(t,e){return t.Sort-e.Sort})).map((function(t){return null===t.Specific_Gravity?(t.Specific_Gravity=void 0,t):t})),a.form=Object.assign(a.form,n),a.IsHaveStore=n.Is_Have_Store}else a.$message({message:t.Message,type:"error"})}))},toNonExponential:function(t){var e=t.toExponential().match(/\d(?:.(\d*))?e([+-]\d+)/);return t.toFixed(Math.max(0,(e[1]||"").length-e[2]))||t},setTb:function(t){return t.reduce((function(t,e,a){var r=e.RolNum;return t[r]||(t[r]=[]),t[r].push(e),t}),[])},getCategoryOption:function(){var t=this,e=this.$parent.$parent.$refs.left.treeData;this.$nextTick((function(a){t.$refs.treeSelect.treeDataUpdateFun(e)}))},handleSubmit:function(){var t=this;this.$refs["form"].validate((function(e){if(e)if(t.form.Category_Id){var a,r=(0,n.default)({},t.form);if(t.SectionParametersShow){r.Spec="";var i=t.tableData.find((function(t){return!t.Value}));if(i)return void t.$message({message:"规格值不能为空",type:"warning"});t.tableData.map((function(t,e){r.Spec?r.Spec=r.Spec+"*"+t.Value:r.Spec=t.Value}))}a=0===t.type?c.SaveRawEntity:c.SaveAuxEntity,t.btnLoading=!0,a((0,n.default)((0,n.default)({},r),{},{Versions:t.versionList})).then((function(e){e.IsSucceed?(t.$message({message:t.isEdit?"修改成功":"保存成功",type:"success"}),t.$emit("refresh"),t.$emit("close")):t.$message({message:e.Message,type:"error"})})).finally((function(){t.btnLoading=!1}))}else t.$message.error("请选择所属分类")}))}}}},df18:function(t,e,a){"use strict";a.r(e);var r=a("66fc"),n=a("5c0f");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("7727");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"58fee846",null);e["default"]=l.exports},f52b:function(t,e,a){"use strict";a.r(e);var r=a("86de"),n=a("4166");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("88b0");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"5da418de",null);e["default"]=l.exports},f7d1:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a("3796")),i=a("ed08"),o=a("8378");e.default={components:{Upload:n.default},props:{versionId:{type:String,default:""}},data:function(){return{btnLoading:!1,schdulingPlanId:""}},inject:["material"],mounted:function(){},methods:{getTemplate:function(){var t=this,e=this.material()?o.GetRawTemplate:o.GetAuxTemplate;e().then((function(e){window.open((0,i.combineURL)(t.$baseUrl,e.Data))}))},beforeUpload:function(t){var e,a=this,r=new FormData;r.append("files",t),r.append("Version_Id",this.versionId),this.btnLoading=!0,e=this.material()?o.ImportRawList:o.ImportAuxList,e(r).then((function(t){t.IsSucceed?(a.$message({type:"success",message:"导入成功"}),a.$emit("refresh"),a.$emit("close")):a.$message({type:"error",message:t.Message}),a.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(t){this.schdulingPlanId=t.Schduling_Id}}}},f887:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteLocation=u,e.FindAuxLocationList=f,e.FindTenantLocationList=d,e.GetLocationList=o,e.GetLocationPageList=l,e.SaveAuxLocation=m,e.SaveLocation=s;var n=r(a("b775")),i=r(a("4328"));function o(t){return(0,n.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/Location/GetLocationPageList",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/Location/SaveLocation",method:"post",data:t})}function u(t){return(0,n.default)({url:"/PRO/Location/DeleteLocation",method:"post",data:i.default.stringify(t)})}var c="";function d(t){return(0,n.default)({url:c+"/PRO/Location/FindTenantLocationList",method:"post",data:t})}function f(t){return(0,n.default)({url:c+"/PRO/MaterielAuxConfig/FindAuxLocationList",method:"get",params:t})}function m(t){return(0,n.default)({url:c+"/PRO/MaterielAuxConfig/SaveAuxLocation",method:"post",data:t})}}}]);