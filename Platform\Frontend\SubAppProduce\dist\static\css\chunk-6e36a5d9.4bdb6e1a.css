.baseinfo[data-v-68597526]{background:#f6f6f6;padding:16px;position:relative}.baseinfo .flag[data-v-68597526]{display:inline-block;padding:4px 12px;background:#298dff;position:absolute;top:4px;right:0;color:#fff}.baseinfo>dl[data-v-68597526]{padding:0}.baseinfo>dl dt[data-v-68597526]{font-size:1.8em;margin-bottom:12px}.baseinfo>dl dd[data-v-68597526]{padding:2px 0;margin:2px 0}.baseinfo>dl dd.lay-2[data-v-68597526]{margin-top:10px}.baseinfo>dl dd.lay-2[data-v-68597526]:first-child{margin-top:20px}.p-form[data-v-68597526]{margin-top:24px}.plan-process{padding:16px;background:#fff}.plan-process span.match{color:#222;background:#ff0}.plan-process .empty-tb-header .el-table__body-wrapper,.plan-process .empty-tb-header .el-table__empty-block{height:0!important}.plan-process>.el-card{height:100%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-ms-flex-flow:column;flex-flow:column}.plan-process>.el-card>.el-card__header{padding:20px 20px 0}.plan-process>.el-card>.el-card__header .el-table{margin-bottom:0!important}.plan-process>.el-card>.el-card__body{height:0;-webkit-box-flex:1;-ms-flex:1;flex:1;overflow:auto}.plan-process ::-webkit-scrollbar{width:8px;height:8px}.plan-process ::-webkit-scrollbar-thumb{border-radius:4px;background:#ddd}.plan-process ::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 2px rgba(0,0,0,.1);box-shadow:inset 0 0 2px rgba(0,0,0,.1);border-radius:4px;background:#ededed}.plan-process .p-header{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:distribute;justify-content:space-around;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.plan-process .p-header .square-icon{display:inline-block;padding:2px;border:1px solid #ddd;margin-right:10px}.plan-process .p-header h3{-webkit-box-flex:1;-ms-flex:1;flex:1}.plan-process .plan-table-wrap{margin-bottom:2px}.plan-process .plan-table-wrap>header{height:48px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:distribute;justify-content:space-around;background:rgba(41,141,255,.06666666666666667);color:#298dff;padding:4px 12px;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-top:1px solid #ddd}.plan-process .plan-table-wrap>header h3{-webkit-box-flex:1;-ms-flex:1;flex:1;margin-left:10px}