(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b3eaf4bc"],{"0151":function(t,e,n){"use strict";n.r(e);var a=n("8197"),r=n("e4e0");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("8096");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"5743da14",null);e["default"]=l.exports},"0972":function(t,e,n){"use strict";n.r(e);var a=n("eb5c"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=o,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,n){var o=i(),l=t-o,u=20,s=0;e="undefined"===typeof e?500:e;var d=function(){s+=u;var t=Math.easeInOutQuad(s,o,l,e);r(t),s<e?a(d):n&&"function"===typeof n&&n()};d()}},"0eda":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7");var r=n("7f9d"),i=a(n("3796"));e.default={components:{UploadExcel:i.default},data:function(){return{btnLoading:!1}},methods:{beforeUpload:function(t){var e,n=this;if(t){var a,i=new FormData;if(i.append("files",t),i.append("importModel",this.importType),null!==(e=this.selectRow)&&void 0!==e&&e.Schduling_Id)i.append("schdulingPlanId",null===(a=this.selectRow)||void 0===a?void 0:a.Schduling_Id);this.btnLoading=!0,(0,r.ImportSchduling)(i).then((function(t){t.IsSucceed?(n.$message({message:"导入成功",type:"success"}),n.$emit("close"),n.$emit("refresh")):n.$message({message:t.Message,type:"error"})})).finally((function(t){n.btnLoading=!1}))}},setRow:function(t,e){this.selectRow=t,this.selectRow?this.importType=t.Schduling_Model:this.importType=e||void 0},handleSubmit:function(){this.$refs.upload.handleSubmit()}}}},1083:function(t,e,n){"use strict";n.r(e);var a=n("1b61"),r=n("426e");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("cc66");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"0e05aa14",null);e["default"]=l.exports},"10ec":function(t,e,n){"use strict";n.r(e);var a=n("a270"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},"13a8":function(t,e,n){"use strict";n("86f7")},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),r=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,o=t.Data,l=t.Message;if(a){if(!o)return void e.$message({message:"表格配置不存在",type:"error"});var u=[];e.tbConfig=Object.assign({},e.tbConfig,o.Grid),u=n?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=u.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+o.Grid.Row_Number||r.tablePageSize[0]),i(e.columns)}else e.$message({message:l,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var r=this.columns[a];if(r.Code===e){n.Type=r.Type,n.Filter_Type=r.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1b61":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"wrapper"},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"tb-wrapper",attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","checkbox-config":{checkField:"checked"},height:"auto",align:"left",stripe:"","row-config":{isCurrent:!0,isHover:!0},size:"medium","edit-config":{trigger:"click",mode:"cell",activeMethod:t.activeCellMethod},data:t.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),t._l(t.columns,(function(e){return["Is_Component"===e.Code?n("vxe-column",{key:e.Code,attrs:{align:e.Align,field:e.Code,title:e.Display_Name,sortable:"","min-width":e.Width},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("el-tag",{attrs:{type:a.Is_Component?"danger":"success"}},[t._v(t._s(a.Is_Component?"否":"是"))])]}}],null,!0)}):"Cancel_Count"===e.Code?n("vxe-column",{key:e.Code,attrs:{align:e.Align,"edit-render":{},field:e.Code,title:e.Display_Name,sortable:"",fixed:"right","min-width":e.Width},scopedSlots:t._u([{key:"edit",fn:function(e){var a=e.row;return[n("vxe-input",{attrs:{type:"integer",min:1,max:a.Can_Cancel_Count},model:{value:a.Cancel_Count,callback:function(e){t.$set(a,"Cancel_Count",t._n(e))},expression:"row.Cancel_Count"}})]}}],null,!0)}):n("vxe-column",{key:e.Id,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:e.Code,title:e.Display_Name,"min-width":e.Width,align:e.Align}})]}))],2)],1),n("footer",[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",disabled:!t.multipleSelection.length,loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)])},r=[]},"1bbc":function(t,e,n){},"1f31":function(t,e,n){"use strict";n.r(e);var a=n("314c"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},"210d":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CancelCompSchduling=c,e.CancelPartSchduling=d,e.DelPartPlan=g,e.GetCanSchdulingParts=o,e.GetCanSchdulingUnits=l,e.GetPartEntity=u,e.GetPartList=i,e.GetPartSchdulingPageList=s,e.ImportPartSchduling=p,e.ImportPartSchdulingNew=v,e.ImportUnitSchdulingNew=b,e.SavePartRelease=m,e.SavePartTask=h,e.SavePartTaskDraft=f;var r=a(n("b775"));function i(t){return(0,r.default)({url:"/PRO/ProductionTask/GetPartList",method:"post",data:t})}function o(t){return(0,r.default)({url:"/PRO/ProductionTask/GetCanSchdulingParts",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/ProductionTask/GetCanSchdulingUnits",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/ProductionTask/GetPartEntity",method:"post",data:t,timeout:12e5})}function s(t){return(0,r.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingPageList",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/ProductionSchduling/CancelPartSchduling",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/ProductionSchduling/CancelCompSchduling",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraft",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/ProductionTask/SavePartTask",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/ProductionTask/SavePartRelease",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/ProductionSchduling/ImportPartSchduling",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlan",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/ProductionSchduling/ImportPartSchdulingNew",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/ProductionSchduling/ImportUnitSchdulingNew",method:"post",data:t})}},2201:function(t,e,n){"use strict";n.r(e);var a=n("d257"),r=n("5b88");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("f479");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"2a5b0fe6",null);e["default"]=l.exports},"2ee5d":function(t,e,n){},"314c":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7");var a=n("7f9d");e.default={data:function(){return{tableData:[],tbLoading:!1}},computed:{isCom:function(){return"com"===this.pageType}},inject:["pageType"],methods:{init:function(t){this.row=t,this.fetchData()},fetchData:function(){var t=this;this.tbLoading=!0;var e=null;e=this.isCom?a.GetSchdulingCancelHistory:a.GetPartSchdulingCancelHistory,e({schdulingCode:this.row.Schduling_Code}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})})).finally((function(e){t.tbLoading=!1}))}}}},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=h,e.DeleteProject=d,e.GeAreaTrees=C,e.GetFileSync=O,e.GetInstallUnitIdNameList=I,e.GetNoBindProjectList=m,e.GetPartDeepenFileList=S,e.GetProjectAreaTreeList=_,e.GetProjectEntity=u,e.GetProjectList=l,e.GetProjectPageList=o,e.GetProjectTemplate=p,e.GetPushProjectPageList=y,e.GetSchedulingPartList=w,e.IsEnableProjectMonomer=c,e.SaveProject=s,e.UpdateProjectTemplateBase=g,e.UpdateProjectTemplateContacts=v,e.UpdateProjectTemplateContract=b,e.UpdateProjectTemplateOther=P;var r=a(n("b775")),i=a(n("4328"));function o(t){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:i.default.stringify(t)})}function s(t){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:i.default.stringify(t)})}function c(t){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function w(t){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function O(t){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"3e73":function(t,e,n){"use strict";n.r(e);var a=n("48f7"),r=n("10ec");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"8e8dfb1c",null);e["default"]=l.exports},"426e":function(t,e,n){"use strict";n.r(e);var a=n("f3cd"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},"48f7":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("home")},r=[]},"4e82":function(t,e,n){"use strict";var a=n("23e7"),r=n("e330"),i=n("59ed"),o=n("7b0b"),l=n("07fa"),u=n("083a"),s=n("577e"),d=n("d039"),c=n("addb"),f=n("a640"),h=n("3f7e"),m=n("99f4"),p=n("1212"),g=n("ea83"),v=[],b=r(v.sort),P=r(v.push),y=d((function(){v.sort(void 0)})),_=d((function(){v.sort(null)})),I=f("sort"),C=!d((function(){if(p)return p<70;if(!(h&&h>3)){if(m)return!0;if(g)return g<603;var t,e,n,a,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)v.push({k:e+a,v:n})}for(v.sort((function(t,e){return e.v-t.v})),a=0;a<v.length;a++)e=v[a].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),S=y||!_||!I||!C,w=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:s(e)>s(n)?1:-1}};a({target:"Array",proto:!0,forced:S},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(C)return void 0===t?b(e):b(e,t);var n,a,r=[],s=l(e);for(a=0;a<s;a++)a in e&&P(r,e[a]);c(r,w(t)),n=l(r),a=0;while(a<n)e[a]=r[a++];while(a<s)u(e,a++);return e}})},"59d9":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"wrapper"},[n("div",{staticClass:"h100"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"","row-config":{isCurrent:!0,isHover:!0},loading:t.tbLoading,align:"left",stripe:"",data:t.tableData,resizable:"","tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"150",field:t.isCom?"Comp_Code":"Part_Code",title:(t.isCom?"构件":"部件")+"名称"}}),t.isCom?t._e():n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Comp_Code",title:"所属构件"}}),n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Spec",title:"规格"}}),n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Texture",title:"材质"}}),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"120",field:"Length",title:"长度"}}),t.isCom?n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Type",title:"构件类型"}}):t._e(),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"80",field:"Cancel_Count",title:"数量"}}),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"120",field:"Weight",title:"单重(kg)"}}),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"150",field:"Cancel_Date",title:"撤回时间"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Cancel_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])})],1)],1),n("footer",[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")])],1)])},r=[]},"5b88":function(t,e,n){"use strict";n.r(e);var a=n("0eda"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},"62d5":function(t,e,n){},"662b":function(t,e,n){"use strict";n.r(e);var a=n("80fc"),r=n("c39f");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("dc83");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"362b79f2",null);e["default"]=l.exports},"702d":function(t,e,n){"use strict";n("62d5")},7196:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteWorkshop=s,e.GetFactoryPeoplelist=i,e.GetWorkshopEntity=u,e.GetWorkshopPageList=l,e.SaveEntity=o;var r=a(n("b775"));function i(t){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function o(t){return(0,r.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:t})}},"7fbd":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("caad"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7"),n("ac1f"),n("2532"),n("841c");var r=a(n("c14f")),i=a(n("1da1")),o=a(n("5530")),l=n("ed08"),u=a(n("9b4d")),s=a(n("0f97")),d=a(n("15ac")),c=n("ccf8"),f=n("7f9d"),h=a(n("2201")),m=a(n("1083")),p=a(n("0151")),g=a(n("8b31")),v=a(n("c1df")),b=a(n("fdbd")),P=n("8975"),y=n("2f62"),_=n("7196"),I=a(n("333d")),C=n("c685"),S=n("c24f");e.default={inject:["pageType"],components:{Pagination:I.default,WithdrawHistory:b.default,AddSchedule:u.default,DynamicDataTable:s.default,Withdraw:m.default,ComImport:h.default,PartImport:p.default},mixins:[d.default,g.default],data:function(){return{statusMap:{finish:"9",unOrdered:"0",ordered:"1"},scheduleType:{comp:1,part:2,comp_part:3},activeName:"1",pgLoading:!1,dialogVisible:!1,currentComponent:"",title:"",dWidth:"40%",queryForm:{Finish_Date_Begin:"",Finish_Date_End:"",Status:0,Workshop_Id:"",Schduling_Code:""},tablePageSize:C.tablePageSize,queryInfo:{Page:1,PageSize:C.tablePageSize[0]},workShopOption:[],columns:[],tbData:[],total:0,search:function(){return{}},roleList:[]}},computed:(0,o.default)({isCom:function(){return"com"===this.pageType},finishTime:{get:function(){return[(0,P.timeFormat)(this.queryForm.Finish_Date_Begin),(0,P.timeFormat)(this.queryForm.Finish_Date_End)]},set:function(t){if(t){var e=t[0],n=t[1];this.queryForm.Finish_Date_Begin=(0,P.timeFormat)(e),this.queryForm.Finish_Date_End=(0,P.timeFormat)(n)}else this.queryForm.Finish_Date_Begin="",this.queryForm.Finish_Date_End=""}}},(0,y.mapGetters)("factoryInfo",["workshopEnabled"])),watch:{activeName:function(t,e){this.queryForm.Status=+t,this.pgLoading=!0,this.getPageInfo()}},activated:function(){!this.isUpdate&&this.fetchData(1)},mounted:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.isUpdate=!0,t.getRoleAuthorization(),e.n=1,t.getFactoryInfo();case 1:return t.workshopEnabled&&t.getWorkshop(),t.search=(0,l.debounce)(t.fetchData,800,!0),e.n=2,t.getPageInfo();case 2:return e.a(2)}}),e)})))()},methods:{getPageInfo:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){var n;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return n="0"===t.activeName?"PROScheduleIsUnorderUnitPart":"1"===t.activeName?"PROScheduleIsOrderUnitParting":"PROScheduleIsOrderPartUnitFinish",e.n=1,t.getTableConfig(n);case 1:t.workshopEnabled||(t.columns=t.columns.filter((function(t){return"Workshop_Name"!==t.Code}))),t.fetchData();case 2:return e.a(2)}}),e)})))()},handleClick:function(){},getFactoryInfo:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.$store.dispatch("factoryInfo/getWorkshop");case 1:return e.a(2)}}),e)})))()},canEditBtn:function(t){var e=t.Status,n=t.Schduling_Model;return n!==this.scheduleType.comp_part&&e===+this.statusMap.unOrdered},canImportBtn:function(t){var e=t.Status,n=t.Schduling_Model,a=t.Area_Id;return!(n===this.scheduleType.comp_part&&!this.isCom)&&(!(a&&"string"===typeof a&&a.split(",").length>1)&&e===+this.statusMap.unOrdered)},canOrderBtn:function(t){var e=t.Status,n=t.Schduling_Model;return!(n===this.scheduleType.comp_part&&!this.isCom)&&e===+this.statusMap.unOrdered},canWithdrawBtn:function(t){t.Generate_Source;var e=t.Status,n=t.Schduling_Model;return!(n===this.scheduleType.comp_part&&!this.isCom)&&e===+this.statusMap.ordered},canWithdrawDraftBtn:function(t){var e=t.Generate_Source,n=t.Status,a=t.Schduling_Model,r=t.Receive_Count,i=t.Cancel_Count,o=t.Total_Change_Count;return 1!==e&&(!(a===this.scheduleType.comp_part&&!this.isCom||r>0||i>0||o>0)&&n===+this.statusMap.ordered)},canDeleteBtn:function(t){var e=t.Status,n=t.Schduling_Model;return!(n===this.scheduleType.comp_part&&!this.isCom)&&e===+this.statusMap.unOrdered},handleAdd:function(){var t=(0,c.getDraftQuery)("PRO2PartScheduleDraftNewPartUnit","add","unitPart",{},this.$route);this.$router.push((0,o.default)({},t))},handleRowImport:function(t){var e=this;this.dWidth="40%",this.currentComponent="PartImport",this.title="导入",this.dialogVisible=!0,this.$nextTick((function(n){e.$refs["content"].setRow(t)}))},handlePartImport:function(){this.dWidth="40%",this.currentComponent="PartImport",this.title="导入",this.dialogVisible=!0},fetchData:function(t){var e=this;this.pgLoading=!0,t&&(this.queryInfo.Page=t);var n=this.queryForm,a=n.projectId,r=n.areaId,i=n.install,l=(n.Status,n.Schduling_Code),u=n.Workshop_Id,s=n.Finish_Date_Begin,d=n.Finish_Date_End,c=(0,o.default)((0,o.default)({},this.queryInfo),{},{Project_Id:a,Area_Id:r,InstallUnit_Id:i,Status:+this.activeName,Schduling_Code:l,Workshop_Id:u,Finish_Date_Begin:s,Finish_Date_End:d,Is_New_Schduling:!0});(0,f.GetUnitSchdulingPageList)(c).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):(e.$message({message:t.Message,type:"error"}),e.tbData=[],e.total=0)})).finally((function(t){e.isUpdate=!1,e.pgLoading=!1}))},handleDelete:function(t){var e=this;this.$confirm("是否删除该排产单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DelSchdulingPlanById)({schdulingPlanId:t.Schduling_Id}).then((function(t){t.IsSucceed?(e.$message({message:"删除成功",type:"success"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleSave:function(t){var e=this;this.$confirm("是否下达该任务?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pgLoading=!0,(0,f.SaveSchdulingTaskById)({schdulingPlanId:t.Schduling_Id}).then((function(t){t.IsSucceed?(e.fetchData(1),e.$message({message:"下达成功",type:"success"}),e.pgLoading=!1):(e.$message({message:t.Message,type:"error"}),e.pgLoading=!1)}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handleCanCelDetail:function(t){var e=this;this.dWidth="80%",this.currentComponent="WithdrawHistory",this.title="撤回历史",this.dialogVisible=!0,this.$nextTick((function(n){e.$refs["content"].init(t)}))},handleEdit:function(t){var e="PRO2PartScheduleDraftNewPartUnit",n=(0,c.getDraftQuery)(e,"edit",this.pageType,{pid:t.Schduling_Id,areaId:t.Area_Id,install:t.InstallUnit_Id,model:t.Schduling_Model},this.$route);this.$router.push((0,o.default)({},n))},handleView:function(t){var e="PRO2PartScheduleDraftNewPartUnit",n=(0,c.getDraftQuery)(e,"view",this.pageType,{pid:t.Schduling_Id,areaId:t.Area_Id,install:t.InstallUnit_Id,type:1===t.Generate_Source?"1":void 0},this.$route);this.$router.push((0,o.default)({},n))},handleWithdraw:function(t,e){var n=this;this.dWidth="80%",this.currentComponent="Withdraw",this.title="撤回",this.dialogVisible=!0,this.$nextTick((function(e){n.$refs["content"].init(t)}))},handleWithdrawAll:function(t){var e=this;this.$confirm("是否撤销排产单回草稿?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.pgLoading=!0,(0,f.WithdrawScheduling)({schedulingId:t.Schduling_Id}).then((function(t){t.IsSucceed?(e.$message({message:"撤回草稿成功",type:"success"}),e.fetchData(1)):(e.$message({message:t.Message,type:"error"}),e.pgLoading=!1)})).catch((function(t){e.pgLoading=!1}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handleClose:function(){this.dialogVisible=!1},handleReset:function(){this.$refs["form"].resetFields(),this.finishTime="",this.search(1)},moment:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD";return""==(null!==t&&void 0!==t?t:"")?"":(0,v.default)(t).format(e)},getWorkshop:function(){var t=this;(0,_.GetWorkshopPageList)({Page:1,PageSize:-1}).then((function(e){var n;e.IsSucceed?(null!==e&&void 0!==e&&null!==(n=e.Data)&&void 0!==n&&n.Data||(t.workShopOption=[]),t.workShopOption=e.Data.Data.map((function(t){return{Id:t.Id,Display_Name:t.Display_Name}}))):t.$message({message:e.Message,type:"error"})}))},getRoles:function(t){return this.roleList.includes(t)},getRoleAuthorization:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){var n;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,S.RoleAuthorization)({roleType:3,menuType:1,menuId:t.$route.meta.Id});case 1:n=e.v,n.IsSucceed?t.roleList=n.Data.map((function(t){return t.Code})):t.$message({type:"warning",message:n.Message});case 2:return e.a(2)}}),e)})))()}}}},8096:function(t,e,n){"use strict";n("99214")},"80fc":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"container abs100"},[n("div",{staticClass:"cs-tabs"},[n("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"进行中",name:t.statusMap.ordered}}),n("el-tab-pane",{attrs:{label:"已完成",name:t.statusMap.finish}}),n("el-tab-pane",{attrs:{label:"未下达",name:t.statusMap.unOrdered}})],1)],1),n("div",{staticClass:"search-wrapper"},[n("el-form",{ref:"form",attrs:{model:t.queryForm,inline:"","label-width":"100px"}},[n("el-form-item",[n("div",{staticClass:"btn-wrapper"},[t.getRoles("PartUnitAddScheduleNew")?n("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新增排产单")]):t._e(),t.getRoles("ProUnitAddSchedule")?n("el-button",{on:{click:t.handlePartImport}},[t._v("导入部件排产")]):t._e()],1)]),n("el-form-item",{attrs:{"label-width":"70px",label:"项目名称",prop:"projectId"}},[n("el-select",{staticStyle:{width:"150px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.queryForm.projectId,callback:function(e){t.$set(t.queryForm,"projectId",e)},expression:"queryForm.projectId"}},t._l(t.projectOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),t.workshopEnabled?n("el-form-item",{attrs:{label:"所属车间",prop:"Workshop_Id","label-width":"70px"}},[n("el-select",{staticStyle:{width:"150px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.queryForm.Workshop_Id,callback:function(e){t.$set(t.queryForm,"Workshop_Id",e)},expression:"queryForm.Workshop_Id"}},t._l(t.workShopOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1):t._e(),n("el-form-item",{directives:[{name:"show",rawName:"v-show",value:t.activeName!==t.statusMap.unOrdered,expression:"activeName!==statusMap.unOrdered"}],attrs:{"label-width":"70px ",label:"排产单号",prop:"Schduling_Code"}},[n("el-input",{staticStyle:{width:"150px"},attrs:{clearable:"",type:"text"},model:{value:t.queryForm.Schduling_Code,callback:function(e){t.$set(t.queryForm,"Schduling_Code",e)},expression:"queryForm.Schduling_Code"}})],1),n("el-form-item",{attrs:{label:"要求完成时间",prop:"finishTime"}},[n("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.finishTime,callback:function(e){t.finishTime=e},expression:"finishTime"}})],1),n("el-form-item",[n("el-button",{on:{click:t.handleReset}},[t._v("重置")]),n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.search(1)}}},[t._v("搜索")])],1)],1)],1),n("div",{staticClass:"main-wrapper"},[n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:t.pgLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[n("vxe-column",{key:e.Code,attrs:{"min-width":e.Width,fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,field:e.Code,title:e.Display_Name},scopedSlots:t._u(["Schduling_Code"===e.Code?{key:"default",fn:function(e){var a=e.row;return[n("el-link",{attrs:{type:"primary"},on:{click:function(e){return t.handleView(a)}}},[t._v(t._s(a.Schduling_Code))])]}}:["Finish_Date","Operator_Date","Order_Date"].includes(e.Code)?{key:"default",fn:function(n){var a=n.row;return[t._v(" "+t._s(t.moment(a[e.Code]))+" ")]}}:"Status"===e.Code?{key:"default",fn:function(e){var n=e.row;return[t._v(" "+t._s(0===n.Status?"草稿":"已下达")+" ")]}}:"Cancel_Count"===e.Code?{key:"default",fn:function(e){var a=e.row;return[a.Cancel_Count?n("el-link",{attrs:{type:"primary"},on:{click:function(e){return t.handleCanCelDetail(a)}}},[t._v(t._s(a.Cancel_Count))]):n("span",[t._v(t._s(a.Cancel_Count))])]}}:null],null,!0)})]})),t.statusMap.finish!==t.activeName?n("vxe-column",{attrs:{fixed:"right",title:"操作",width:t.activeName===t.statusMap.ordered?170:220,"min-width":t.activeName===t.statusMap.ordered?170:220,"show-overflow":""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t.canEditBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleEdit(a)}}},[t._v("修改 ")]):t._e(),t.canOrderBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleSave(a)}}},[t._v("下达 ")]):t._e(),t.statusMap.unOrdered===t.activeName?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleView(a)}}},[t._v("查看")]):t._e(),t.canWithdrawBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleWithdraw(a)}}},[t._v("撤销排产 ")]):t._e(),t.canWithdrawDraftBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleWithdrawAll(a)}}},[t._v("撤回草稿 ")]):t._e(),t.canDeleteBtn(a)?n("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(e){return t.handleDelete(a)}}},[t._v("删除 ")]):t._e()]}}],null,!1,3628944113)}):t._e()],2)],1),n("div",{staticClass:"data-info"},[n("Pagination",{attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)]),t.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.dWidth},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:function(e){return t.fetchData(1)}}})],1):t._e()],1)},r=[]},8197:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-form",{ref:"form",attrs:{rules:t.rules,model:t.queryForm,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"项目名称",prop:"projectId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:t.disabledProject,filterable:"",clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.queryForm.projectId,callback:function(e){t.$set(t.queryForm,"projectId",e)},expression:"queryForm.projectId"}},t._l(t.projectOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域名称",prop:"areaId"}},[n("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{disabled:!t.queryForm.projectId||t.disabledArea,"select-params":{clearable:!0},"tree-params":t.treeParams},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.queryForm.areaId,callback:function(e){t.$set(t.queryForm,"areaId",e)},expression:"queryForm.areaId"}})],1)],1),n("upload",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},r=[]},"849e":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("d3b7");var r=a(n("3796")),i=n("210d"),o=a(n("8b31"));e.default={components:{Upload:r.default},mixins:[o.default],data:function(){return{disabledProject:!1,disabledArea:!1,btnLoading:!1,schdulingPlanId:"",rules:{projectId:[{required:!0,message:"请选择",trigger:"change"}],areaId:[{required:!0,message:"请选择",trigger:"change"}]}}},created:function(){},mounted:function(){},methods:{beforeUpload:function(t){var e,n,a=this,r=new FormData,o=null===(e=this.projectOption.find((function(t){return t.Id===a.queryForm.projectId})))||void 0===e?void 0:e.Sys_Project_Id;r.append("files",t),r.append("sysProjectId",o),r.append("areaId",this.queryForm.areaId),""!==(null!==(n=this.schdulingPlanId)&&void 0!==n?n:"")&&r.append("schdulingPlanId",this.schdulingPlanId),this.btnLoading=!0,(0,i.ImportUnitSchdulingNew)(r).then((function(t){t.IsSucceed?(a.$message({type:"success",message:"导入成功"}),a.$emit("refresh"),a.$emit("close")):a.$message({type:"error",message:t.Message}),a.btnLoading=!1}))},handleSubmit:function(){var t=this;this.$refs.form.validate((function(e){e&&t.$refs.upload.handleSubmit()}))},setRow:function(t){this.queryForm.projectId=t.Project_Id,this.queryForm.areaId=t.Area_Id,this.schdulingPlanId=t.Schduling_Id,this.disabledProject=!0,this.disabledArea=!0,this.queryForm.projectId&&this.getAreaList()}}}},"86f7":function(t,e,n){},"8b31":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("e9f5"),n("910d"),n("7d54"),n("d3b7"),n("159b");var a=n("3166"),r=n("f2f6"),i=n("8975");e.default={data:function(){return{queryForm:{projectId:"",install:"",areaId:""},projectOption:[],areaList:[],installOption:[],treeParams:{data:[],filterable:!1,clickParent:!0,props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}}}},mounted:function(){this.getProjectOption()},methods:{getAreaList:function(){var t=this;(0,a.GeAreaTrees)({projectId:this.queryForm.projectId,Area_Id:this.queryForm.install}).then((function(e){if(e.IsSucceed){var n=e.Data;t.setDisabledTree(n),t.treeParams.data=n,t.$nextTick((function(e){t.$refs.treeSelect.treeDataUpdateFun(n)}))}else t.$message({message:e.Message,type:"error"})}))},getInstall:function(){var t=this;(0,r.GetInstallUnitPageList)({Area_Id:this.queryForm.areaId,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.installOption=e.Data.Data.filter((function(t){return t.Id})):t.$message({message:e.Message,type:"error"})}))},projectChange:function(){this.queryForm.areaId="",this.queryForm.install="",this.queryForm.projectId&&this.getAreaList()},areaChange:function(t){if(this.areaIsImport=!1,this.queryForm.install="",this.queryForm.areaId&&!this.areaIsImport){this.getInstall();var e=null===t||void 0===t?void 0:t.Data,n=e.Demand_Begin_Date,a=e.Demand_End_Date;this.getAreaTime((0,i.timeFormat)(n),(0,i.timeFormat)(a))}},installChange:function(){this.getInstall()},areaClear:function(){this.queryForm.install=""},getProjectOption:function(){var t=this;(0,a.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.projectOption=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var n=t.Children;n&&n.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(n))}))},getAreaTime:function(){}}}},99214:function(t,e,n){},"9b4d":function(t,e,n){"use strict";n.r(e);var a=n("fd26"),r=n("0972");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("13a8");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"280e0670",null);e["default"]=l.exports},a270:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7"),n("3ca3"),n("ddb0");var r=a(n("662b")),i=a(n("2082"));e.default={name:"PRO2UnitPartScheduleNew",components:{Home:r.default},provide:{pageType:"unitPart"},mixins:[i.default],data:function(){return{addPageArray:[{path:this.$route.path+"/draft",hidden:!0,component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-2d0c91c4"),n.e("chunk-05a1e5cf"),n.e("chunk-c0405280")]).then(n.bind(null,"b82e"))},name:"PRO2PartScheduleDraftNewPartUnit",meta:{title:"草稿",Id:this.$route.meta.Id}}]}}}},aa75:function(t,e,n){},c39f:function(t,e,n){"use strict";n.r(e);var a=n("7fbd"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},c3c6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.uniqueCode=e.getDraftQuery=e.FIX_COLUMN=void 0,n("b0c0");var r=a(n("5530"));e.getDraftQuery=function(t,e,n,a,i){return{name:t,query:(0,r.default)({status:e,pg_type:n,pg_redirect:i.name},a)}},e.uniqueCode=function(t){return"uuid"},e.FIX_COLUMN=["Comp_Code","Project_Name","Area_Name","Part_Code","InstallUnit_Name"]},cc66:function(t,e,n){"use strict";n("aa75")},ccf8:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.uniqueCode=e.getUnique=e.getDraftQuery=e.FIX_COLUMN=void 0,n("b0c0");var r=a(n("5530"));e.getDraftQuery=function(t,e,n,a,i){return{name:t,query:(0,r.default)({status:e,pg_type:n,pg_redirect:i.name},a)}},e.uniqueCode=function(t){return"uuid"},e.FIX_COLUMN=["Comp_Code","Part_Code"],e.getUnique=function(t,e){return e.InstallUnit_Id+e.Part_Code+e.Part_Aggregate_Id}},d257:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},r=[]},dc83:function(t,e,n){"use strict";n("2ee5d")},e4e0:function(t,e,n){"use strict";n.r(e);var a=n("849e"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a},eb5c:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(n("5530"));n("14d9"),n("d3b7");var i=n("c3c6"),o=a(n("8b31")),l=n("7f9d");e.default={mixins:[o.default],data:function(){return{btnLoading:!1,rules:{projectId:[{required:!0,message:"请选择",trigger:"change"}],areaId:[{required:!0,message:"请选择",trigger:"change"}]}}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType}},methods:{getAreaTime:function(t,e){this.rangDate=[t,e]},submit:function(){var t=this;this.$refs["queryForm"].validate((function(e){e&&(t.btnLoading=!0,(0,l.CheckSchduling)({projectId:t.queryForm.projectId,areaId:t.queryForm.areaId,installId:t.queryForm.install,schdulingModel:t.isCom?1:2}).then((function(e){if(e.IsSucceed){t.$emit("close");var n=(0,i.getDraftQuery)("PRO2PartScheduleDraftNewPartUnit","add",t.pageType,(0,r.default)({},t.queryForm));t.$router.push((0,r.default)({},n))}else t.$message({message:e.Message,type:"error"})})).finally((function(e){t.btnLoading=!1})))}))}}}},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=u,e.CheckPlanTime=s,e.DeleteInstallUnit=h,e.GetCompletePercent=b,e.GetEntity=y,e.GetInstallUnitAllInfo=c,e.GetInstallUnitComponentPageList=v,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=d,e.GetInstallUnitList=l,e.GetInstallUnitPageList=o,e.GetProjectInstallUnitList=P,e.ImportInstallUnit=p,e.InstallUnitInfoTemplate=m,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=_;var r=a(n("b775")),i=a(n("4328"));function o(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function s(t){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function d(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function c(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function h(t){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function m(t){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function p(t){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function y(t){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:i.default.stringify(t)})}function _(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},f3cd:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("caad"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7"),n("2532"),n("c7cd");var r=a(n("c14f")),i=a(n("1da1")),o=a(n("5530")),l=n("6186"),u=n("7f9d"),s=n("210d"),d=n("c3c6"),c=n("2f62");e.default={data:function(){return{btnLoading:!1,tbLoading:!1,total:0,columns:[],tbData:[],tbConfig:{},TotalCount:0,multipleSelection:[]}},computed:(0,o.default)({isCom:function(){return"com"===this.pageType}},(0,c.mapGetters)("factoryInfo",["workshopEnabled"])),inject:["pageType"],mounted:function(){},methods:{getConfig:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig("PROUnitPartWithdraw_new");case 1:t.workshopEnabled||(t.columns=t.columns.filter((function(t){return"Workshop_Name"!==t.Code})));case 2:return e.a(2)}}),e)})))()},init:function(t){this.row=t,this.getConfig(),this.fetchData()},fetchData:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){var n,a,i;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:if(t.tbLoading=!0,n=null,!t.isCom){e.n=2;break}return e.n=1,t.getComPageList(null===(a=t.row)||void 0===a?void 0:a.Schduling_Id);case 1:n=e.v,e.n=4;break;case 2:return e.n=3,t.getPartPageList(null===(i=t.row)||void 0===i?void 0:i.Schduling_Id);case 3:n=e.v;case 4:t.initData(n),t.tbLoading=!1;case 5:return e.a(2)}}),e)})))()},initData:function(t){this.tbData=t.filter((function(t){return t.Can_Cancel_Count})).map((function(t){return t.Cancel_Count=t.Can_Cancel_Count,t.checked=!1,t}))},getComPageList:function(t){var e=this;return new Promise((function(n,a){(0,u.GetPageSchdulingComps)({Schduling_Plan_Id:t}).then((function(t){if(t.IsSucceed){var r=t.Data,i=r.Schduling_Comps,o=r.Schduling_Plan;e.info=o,n(i||[])}else e.$message({message:t.Message,type:"error"}),a()}))}))},getPartPageList:function(t){var e=this;return new Promise((function(n,a){(0,s.GetPartEntity)({Schduling_Plan_Id:t}).then((function(t){if(t.IsSucceed){var r=t.Data,i=r.SarePartsModel,o=r.Schduling_Plan;e.info=o,n(i||[])}else e.$message({message:t.Message,type:"error"}),a()}))}))},handleSubmit:function(){var t=this;this.btnLoading=!0;var e={Schduling_Plan:this.info};Object.assign(e,{SarePartsModel:this.multipleSelection.map((function(t){return t.Cancel_Count=+t.Cancel_Count,t})).filter((function(t){return t.Cancel_Count}))}),(0,u.CancelUnitSchduling)(e).then((function(e){e.IsSucceed?(t.$message({message:"撤回成功",type:"success"}),t.$emit("close"),t.$emit("refresh")):t.$message({message:e.Message,type:"error"}),t.btnLoading=!1}))},tbSelectChange:function(t){this.multipleSelection=t.records},activeCellMethod:function(t){t.row;var e=t.column;t.columnIndex;return"Cancel_Count"===e.field},getTableConfig:function(t){var e=this;return(0,i.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,l.GetGridByCode)({code:t}).then((function(t){var n=t.IsSucceed,a=t.Data,r=t.Message;if(n){e.tbConfig=Object.assign({},e.tbConfig,a.Grid);var i=a.ColumnList||[];e.columns=i.filter((function(t){return t.Is_Display})).map((function(t){return d.FIX_COLUMN.includes(t.Code)&&(t.fixed="left"),t})),e.columns.push({Display_Name:"可撤回数量",Code:"Can_Cancel_Count",Width:"120px"}),e.columns.push({Display_Name:"撤回数量",Code:"Cancel_Count",Width:"120px"})}else e.$message({message:r,type:"error"})}));case 1:return n.a(2)}}),n)})))()}}}},f479:function(t,e,n){"use strict";n("1bbc")},fd26:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-form",{ref:"queryForm",attrs:{model:t.queryForm,rules:t.rules,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"项目名称",prop:"projectId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.queryForm.projectId,callback:function(e){t.$set(t.queryForm,"projectId",e)},expression:"queryForm.projectId"}},t._l(t.projectOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域名称",prop:"areaId"}},[n("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{"tree-params":t.treeParams,"select-params":{clearable:!1}},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.queryForm.areaId,callback:function(e){t.$set(t.queryForm,"areaId",e)},expression:"queryForm.areaId"}})],1),n("el-form-item",{attrs:{label:"批次",prop:"install",rules:[{required:t.installOption.length,message:"请选择",trigger:"change"}]}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",disabled:!t.installOption.length,placeholder:"请选择"},on:{change:t.installChange},model:{value:t.queryForm.install,callback:function(e){t.$set(t.queryForm,"install",e)},expression:"queryForm.install"}},t._l(t.installOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.submit}},[t._v("确定")])],1)],1)},r=[]},fdbd:function(t,e,n){"use strict";n.r(e);var a=n("59d9"),r=n("1f31");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("702d");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"53de41e7",null);e["default"]=l.exports}}]);