(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-1b78e2f8"],{"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,n){var i=r(),s=t-i,u=20,c=0;e="undefined"===typeof e?500:e;var l=function(){c+=u;var t=Math.easeInOutQuad(c,i,s,e);o(t),c<e?a(l):n&&"function"===typeof n&&n()};l()}},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,i=t.Data,s=t.Message;if(a){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var u=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),u=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=u.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(e.columns)}else e.$message({message:s,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===e){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=d,e.CancelBindBimProject=m,e.DeleteProject=l,e.GeAreaTrees=j,e.GetFileSync=S,e.GetInstallUnitIdNameList=C,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=I,e.GetProjectAreaTreeList=y,e.GetProjectEntity=u,e.GetProjectList=s,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=v,e.GetSchedulingPartList=w,e.IsEnableProjectMonomer=f,e.SaveProject=c,e.UpdateProjectTemplateBase=g,e.UpdateProjectTemplateContacts=P,e.UpdateProjectTemplateContract=_,e.UpdateProjectTemplateOther=b;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function c(t){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function f(t){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function S(t){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},3533:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7"),n("ac1f"),n("25f0"),n("5319");e.default={props:{info:{type:Object,default:function(){return{title:"",icon:"",num1:0,num2:0}}},showArr:{type:Boolean,default:!0}},methods:{formatNumberWithCommas:function(t){if(t)return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}}}},"534f":function(t,e,n){"use strict";n.r(e);var a=n("3533"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"53e7":function(t,e,n){"use strict";n.r(e);var a=n("f1a8"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},6898:function(t,e,n){"use strict";n.r(e);var a=n("7634"),o=n("53e7");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("a34f");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"6f388e57",null);e["default"]=s.exports},7634:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[n("div",{staticClass:"cs-z-page-main-content",staticStyle:{padding:"0"}},[n("div",{staticClass:"cs-z-tb-wrapper fff"},[n("div",{staticClass:"item-x"},t._l(t.itemInfos,(function(t,e){return n("ItemInfo",{key:t.title,attrs:{info:t,"show-arr":6!==e,cp:t.cp}})})),1),n("vxe-toolbar",{ref:"xToolbar1",scopedSlots:t._u([{key:"buttons",fn:function(){return[n("el-button",{attrs:{loading:t.exporting,type:"success"},on:{click:t.handleExport}},[t._v("导 出")])]},proxy:!0},{key:"tools",fn:function(){return[n("el-form",{ref:"form",staticClass:"cs-form",attrs:{model:t.form,"label-width":"80px",inline:""}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Short_Name"}},[n("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择..."},model:{value:t.form.Short_Name,callback:function(e){t.$set(t.form,"Short_Name",e)},expression:"form.Short_Name"}},[t._l(t.ProjectNameData,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Short_Name}})}))],2)],1)],1),n("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜 索")]),n("el-button",{on:{click:function(e){return t.resetForm()}}},[t._v("重 置 ")]),n("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":t.gridCode},on:{updateColumn:t.changeColumn}})]},proxy:!0}])}),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{key:t.tbKey,ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:t.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"auto",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[n("vxe-column",{key:e.Code,attrs:{"min-width":e.Width,fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:e.Code,title:e.Display_Name},scopedSlots:t._u(["Plan_Time_Range"===e.Code?{key:"default",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Plan_Start_Time))+" - "+t._s(t._f("timeFormat")(n.Plan_Over_Time))+" ")]}}:"Act_Time_Range"===e.Code?{key:"default",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Act_Start_Time))+" - "+t._s(t._f("timeFormat")(n.Act_End_Time))+" ")]}}:["Materiel_Prepare_Rate","Drawing_Completion_Rate","Producing_Rate","Component_In_Rate","Out_Rate"].includes(e.Code)?{key:"default",fn:function(n){var a=n.row;return[t._v(" "+t._s(null===a[e.Code]?"-":a[e.Code]+"%")+" ")]}}:["Project_Progress"].includes(e.Code)?{key:"default",fn:function(a){var o=a.row;return[n("el-tag",{attrs:{type:"正常"===o.Project_Progress?"success":"danger"}},[t._v(" "+t._s(t._f("displayValue")(o[e.Code]))+" ")])]}}:{key:"default",fn:function(a){var o=a.row;return[n("span",[t._v(t._s(null===o[e.Code]?"-":o[e.Code]))])]}}],null,!0)})]}))],2)],1),n("Pagination",{attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageCustomChange}})],1)])])},o=[]},"7b30":function(t,e,n){},"7d6e":function(t,e,n){"use strict";n("7b30")},a34f:function(t,e,n){"use strict";n("e24c")},a9272:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"item-wrapper"},[n("div",{staticClass:"title-x"},[n("svg-icon",{attrs:{"class-name":"svg-icons","icon-class":t.info.icon}}),n("span",{staticClass:"cs-title"},[t._v(t._s(t.info.title))]),t.showArr?[n("span",{staticClass:"cs-right-icon"}),n("div",{staticClass:"arr-icons-x"},[n("svg-icon",{attrs:{"class-name":"arr-icons","icon-class":"arrow-r"}}),n("span",{staticClass:"cs-right-line"})],1)]:t._e()],2),n("span"),n("div",{staticClass:"cs-info"},[n("span",{staticClass:"cs-point",class:t.info.cp}),n("div",[n("span",{staticClass:"info-label"},[t._v("数量")]),n("span",{staticClass:"cs-blue"},[t._v(t._s(t.formatNumberWithCommas(t.info.num1)||0))])]),n("div",[n("span",{staticClass:"info-label"},[t._v("重量")]),n("span",{staticClass:"cs-green"},[t._v(t._s(t.formatNumberWithCommas(t.info.num2)||0))])])]),n("svg",{staticStyle:{position:"absolute",left:"-999px"},attrs:{width:"300",height:"300",viewBox:"0 0 20 20"}},[n("filter",{attrs:{id:"inset-shadow"}},[n("feOffset",{attrs:{dx:"0",dy:"0"}}),n("feGaussianBlur",{attrs:{stdDeviation:"6",result:"offset-blur"}}),n("feComposite",{attrs:{operator:"out",in:"SourceGraphic",in2:"offset-blur",result:"inverse"}}),n("feFlood",{attrs:{"flood-color":"black","flood-opacity":".35",result:"color"}}),n("feComposite",{attrs:{operator:"in",in:"color",in2:"inverse",result:"shadow"}}),n("feComposite",{attrs:{operator:"over",in:"shadow",in2:"SourceGraphic"}})],1)])])},o=[]},c8aa:function(t,e,n){"use strict";n.r(e);var a=n("a9272"),o=n("534f");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("7d6e");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"3eb942bf",null);e["default"]=s.exports},e24c:function(t,e,n){},f1a8:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("fb6a"),n("e9f5"),n("910d"),n("d3b7");var o=a(n("c14f")),r=a(n("1da1")),i=a(n("c8aa")),s=a(n("333d")),u=n("c685"),c=n("3166"),l=a(n("15ac")),f=n("586a"),d=n("ed08"),m=a(n("a657"));e.default={name:"PROProgressTracking",components:{DynamicTableFields:m.default,Pagination:s.default,ItemInfo:i.default},mixins:[l.default],data:function(){return{gridCode:"",exporting:!1,tbLoading:!1,ProjectNameData:[],tbData:[],columns:[],tablePageSize:u.tablePageSize,queryInfo:{Page:1,PageSize:u.tablePageSize[0]},form:{Short_Name:""},total:0,itemInfos:[{title:"要货",icon:"icon-track1",num1:0,num2:0,cp:"c-blue"},{title:"深化",icon:"icon-track2",num1:0,num2:0,cp:"c-green"},{title:"采购",icon:"icon-track3",num1:0,num2:0,cp:"c-purple"},{title:"排产",icon:"icon-track3",num1:0,num2:0,cp:"c-purple"},{title:"在制品",icon:"icon-track4",num1:0,num2:0,cp:"c-yellow"},{title:"已入库",icon:"icon-track7",num1:0,num2:0,cp:"c-dy"},{title:"已发货",icon:"icon-track6",num1:0,num2:0,cp:"c-pk"}],tbKey:100}},mounted:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return t.gridCode="ProjectTraceNew",e.n=1,t.getTableConfig(t.gridCode);case 1:t.getProjectOption(),t.getTotalInfo(),t.fetchData(1);case 2:return e.a(2)}}),e)})))()},methods:{changeColumn:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig(t.gridCode);case 1:t.tbKey++;case 2:return e.a(2)}}),e)})))()},pageCustomChange:function(t){var e=t.page,n=void 0===e?1:e,a=t.limit,o=void 0===a?this.tablePageSize[0]:a;1===n?(this.tbData=this.list.slice(0,o),this.queryInfo.Page=1):this.tbData=this.list.slice((n-1)*o,n*o)},resetForm:function(){this.$refs["form"].resetFields(),this.fetchData(1)},initTb:function(){this.tbData=(0,d.deepClone)(this.list),this.total=this.tbData.length},handleSearch:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.fetchData(1);case 1:t.form.Short_Name?(t.tbData=t.list.filter((function(e){return e.Project_Name===t.form.Short_Name})),t.total=t.tbData.length,t.queryInfo.Page=1,t.queryInfo.PageSize=t.tablePageSize[0]):t.initTb();case 2:return e.a(2)}}),e)})))()},getTotalInfo:function(){var t=this;(0,f.GetProjectAreaProgressSummary)({Is_Compute_Purchase:!0}).then((function(e){var n=e.Data,a=n.Estimate_Amount,o=n.Estimate_Weight,r=n.Deepen_Count,i=n.Deepen_Weight,s=n.Schduled_Count,u=n.Schduled_Weight,c=n.Producing_Count,l=n.Producing_Weight,f=n.Stock_In_Count,d=n.Stock_In_Weight,m=n.Out_Count,p=n.Out_Weight,h=n.Purchase_Count,g=n.Purchase_Weight;t.itemInfos[0].num1=a,t.itemInfos[0].num2=o,t.itemInfos[1].num1=r,t.itemInfos[1].num2=i,t.itemInfos[2].num1=h,t.itemInfos[2].num2=g,t.itemInfos[3].num1=s,t.itemInfos[3].num2=u,t.itemInfos[4].num1=c,t.itemInfos[4].num2=l,t.itemInfos[5].num1=f,t.itemInfos[5].num2=d,t.itemInfos[6].num1=m,t.itemInfos[6].num2=p}))},handleExport:function(){this.$refs.xTable1.exportData({filename:"项目库存统计",type:"xlsx",data:this.tbData})},fetchData:function(){var t=arguments,e=this;return(0,r.default)((0,o.default)().m((function n(){var a;return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return a=t.length>0&&void 0!==t[0]?t[0]:1,e.list=[],a&&(e.queryInfo.Page=a),e.tbLoading=!0,n.n=1,(0,f.GetProjectLifeCycleProgress)({}).then((function(t){t.IsSucceed?(e.list=t.Data||[],e.initTb(),e.pageCustomChange({page:a,limit:e.queryInfo.PageSize})):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));case 1:return n.a(2)}}),n)})))()},getProjectOption:function(){var t=this;(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))}}}}}]);