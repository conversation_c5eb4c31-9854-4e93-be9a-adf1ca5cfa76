(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-55983c44"],{"43af":function(t,e,a){"use strict";a.r(e);var r=a("946ca"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},"4f39":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=i,e.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var n=r(a("53ca"));function i(t,e){if(0===arguments.length||!t)return null;var a,r=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,n.default)(t)?a=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),a=new Date(t));var i={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=r.replace(/{([ymdhisa])+}/g,(function(t,e){var a=i[e];return"a"===e?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var a=t.split("~"),r=i(new Date(a[0]),e)+" ~ "+i(new Date(a[1]),e);return r}return t&&t.length>0?i(new Date(t),e):void 0}},"946ca":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var n=r(a("c14f")),i=r(a("1da1")),o=r(a("ac03")),c=a("cf45"),s=a("4f39"),l=r(a("3502")),u=a("d7ff"),f=a("7757"),d=a("8ff5"),m=a("05e0");e.default={name:"OMAProductionFeeDetailWorker",components:{VTable:l.default},mixins:[o.default],data:function(){return{form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],showExport:!1}},computed:{curTitle:function(){return"".concat((0,s.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")}},beforeCreate:function(){this.curModuleKey=d.curModuleKey},mounted:function(){var t=this;return(0,i.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.showExport=t.getRoles("OMAProProcessDetailExport"),t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,e.n=1,t.getProcess();case 1:return t.fetchData(),e.n=2,(0,c.getDictionary)("Engineering Status");case 2:t.projectOption=e.v;case 3:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate()&&(this.loading=!0,(0,u.GetProcessFeesList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=((null===e||void 0===e?void 0:e.Data)||[]).map((function(t){return t.Process_Fees.forEach((function(e,a){var r=e.Code,n=e.Value;t[r]=n})),t}));var a=t.setTotalData(t.tableData,t.generateColumn()),r=a.column;t.columns=r,t.$refs["tb"].setColumns(r)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},getProcess:function(){var t=this;return new Promise((function(e,a){(0,f.GetFactoryProcessLibs)({FactoryId:t.form.FactoryId,Is_Include_UnEnabled:!0}).then((function(a){a.IsSucceed?(t.processOption=a.Data.filter((function(t){return!t.Is_External})),e()):t.$message({message:a.Message,type:"error"})}))}))},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var t=180,e=[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:3},field:"ProjectAbbreviation",minWidth:m.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:m.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:m.ProjectStatusW}]}]},{title:"劳资-生产工人",children:[]}];if(this.processOption.length){var a=[];this.processOption.forEach((function(e,r){var n=[{title:e.Name+"报工量(T)",children:[{minWidth:t,field:e.Code,title:0,isTotal:!0}]},{title:e.Name+"单价(元)",children:[{minWidth:t,field:e.Code+"_unit_price",title:0,isTotal:!0}]}];a.push.apply(a,n)})),a.push({title:"劳资-生产调差",children:[{minWidth:t,field:"Production_Adjust",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:t,field:"Process_Sub_Total",title:0,isTotal:!0}]}),e[e.length-1].children=a}return e}}}},bfcc:function(t,e,a){"use strict";a("ff67")},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,a("d3b7");var r=a("6186");function n(t){return new Promise((function(e,a){(0,r.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},cff68:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("label",[t._v("数据列表")]),a("div",{staticClass:"btn-x"},[t.showExport?a("el-button",{attrs:{disabled:t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e()],1)]),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},n=[]},db47:function(t,e,a){"use strict";a.r(e);var r=a("cff68"),n=a("43af");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("bfcc");var o=a("2877"),c=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"0347c053",null);e["default"]=c.exports},ff67:function(t,e,a){}}]);