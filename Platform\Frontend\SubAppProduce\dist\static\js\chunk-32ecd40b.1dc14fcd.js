(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-32ecd40b"],{"09f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,n,a){return e/=a/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,n){var i=r(),u=e-i,d=20,l=0;t="undefined"===typeof t?500:t;var s=function(){l+=d;var e=Math.easeInOutQuad(l,i,u,t);o(e),l<t?a(s):n&&"function"===typeof n&&n()};s()}},"15ac":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");t.default={methods:{getTableConfig:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:e,IsAll:n}).then((function(e){var a=e.IsSucceed,i=e.Data,u=e.Message;if(a){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var d=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),d=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=d.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:u,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit,a=e.type;this.queryInfo.Page="limit"===a?1:t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===t){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},3166:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=g,t.DeleteProject=s,t.GeAreaTrees=v,t.GetFileSync=G,t.GetInstallUnitIdNameList=y,t.GetNoBindProjectList=h,t.GetPartDeepenFileList=R,t.GetProjectAreaTreeList=b,t.GetProjectEntity=d,t.GetProjectList=u,t.GetProjectPageList=i,t.GetProjectTemplate=m,t.GetPushProjectPageList=O,t.GetSchedulingPartList=S,t.IsEnableProjectMonomer=c,t.SaveProject=l,t.UpdateProjectTemplateBase=p,t.UpdateProjectTemplateContacts=C,t.UpdateProjectTemplateContract=P,t.UpdateProjectTemplateOther=_;var o=a(n("b775")),r=a(n("4328"));function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function G(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"7015f":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=y,t.AgainSubmitChangeOrder=O,t.BatchReuseEngineeringContactChangedComponentPart=ie,t.BatchReuseEngineeringContactMocComponentPart=ue,t.CancelChangeOrder=_,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=A,t.DeleteChangeOrder=c,t.DeleteChangeOrderV2=w,t.DeleteChangeReason=m,t.DeleteChangeType=u,t.DeleteEngineeringContactChangeOrder=J,t.DeleteMocOrder=Q,t.DeleteMocType=Pe,t.ExportEngineeringContactChangedAddComponentPart=se,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=oe,t.ExportMocAddComponentPart=ce,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=R,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=l,t.GetChangeOrderTaskInfo=I,t.GetChangeOrderTaskPageList=T,t.GetChangeOrderV2=F,t.GetChangeOrderV2PageList=E,t.GetChangeReason=g,t.GetChangeType=r,t.GetChangedComponentPartPageList=U,t.GetChangedComponentPartProductionList=W,t.GetCompAndPartSchdulingPageList=j,t.GetCompAndPartTaskList=M,t.GetCompanyUserPageList=D,t.GetEngineeringContactChangeOrder=H,t.GetEngineeringContactChangeOrderPageList=q,t.GetEngineeringContactChangedAddComponentPartPageList=de,t.GetEngineeringContactChangedAddComponentPartSummary=le,t.GetEngineeringContactChangedComponentPartPageList=Y,t.GetEngineeringContactChangedSummary=X,t.GetEngineeringContactFileInfo=V,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=K,t.GetEngineeringContactMocSummary=ae,t.GetFactoryChangeTypeListV2=z,t.GetFactoryPeoplelist=d,t.GetMocAddComponentPartPageList=ge,t.GetMocModelList=Oe,t.GetMocOrderInfo=pe,t.GetMocOrderPageList=he,t.GetMocOrderTypeList=Ce,t.GetMyChangeOrderPageList=P,t.GetProjectAreaChangeTreeList=B,t.GetProjectChangeOrderList=G,t.GetTypeReason=p,t.ImportChangFile=me,t.ImportChangeDeependFile=C,t.QueryHistories=v,t.ReuseEngineeringContactChangedComponentPart=ne,t.ReuseEngineeringContactMocComponentPart=re,t.SaveChangeOrder=s,t.SaveChangeOrderTask=L,t.SaveChangeOrderV2=k,t.SaveChangeReason=h,t.SaveChangeType=i,t.SaveEngineeringContactChangeOrder=$,t.SaveMocOrder=be,t.SaveMocOrderType=_e,t.SubmitChangeOrder=b,t.SubmitChangeOrderV2=N,t.SubmitMocOrder=x,t.Verification=S;var o=a(n("b775"));a(n("4328"));function r(e){return(0,o.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function v(e){return(0,o.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function R(e){return(0,o.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function D(e){return(0,o.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,o.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,o.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,o.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ne(e){return(0,o.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ae(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function oe(e){return(0,o.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ue(e){return(0,o.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function de(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function le(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function ce(e){return(0,o.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function ge(e){return(0,o.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function he(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function me(e){return(0,o.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function pe(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Ce(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Pe(e){return(0,o.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function _e(e){return(0,o.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function Oe(e){return(0,o.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function be(e){return(0,o.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},"91b5":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"page-container abs100"},[n("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"项目汇总",name:"1"}}),n("el-tab-pane",{attrs:{label:"构件",name:"构件"}}),n("el-tab-pane",{attrs:{label:"部件",name:"部件"}}),n("el-tab-pane",{attrs:{label:"零件",name:"零件"}})],1),"1"!==e.activeName?n("el-form",{ref:"form",staticClass:"cs-form",attrs:{inline:"",model:e.form,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return n("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),n("el-form-item",{attrs:{label:"当前状态",prop:"Production_Status"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Production_Status,callback:function(t){e.$set(e.form,"Production_Status",t)},expression:"form.Production_Status"}},[n("el-option",{attrs:{label:"未生产",value:"未生产"}}),n("el-option",{attrs:{label:"生产中",value:"生产中"}}),e.isPart?n("el-option",{attrs:{label:"生产完成",value:"生产完成"}}):[n("el-option",{attrs:{label:"已入库",value:"已入库"}}),n("el-option",{attrs:{label:"已发货",value:"已发货"}})]],2)],1),n("el-form-item",{attrs:{label:e.activeName+"名称"}},[n("el-input",{staticClass:"input-with-select",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.names,callback:function(t){e.names=t},expression:"names"}},[n("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.nameMode,callback:function(t){e.nameMode=t},expression:"nameMode"}},[n("el-option",{attrs:{label:"模糊搜索",value:1}}),n("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1),n("el-form-item",{attrs:{label:"变更联系单号",prop:"Bill_No"}},[n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No",t)},expression:"form.Bill_No"}})],1),n("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.mocType,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.fetchData(1)}}},[e._v("搜索")]),n("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),n("div",{staticClass:"cs-main"},["1"===e.activeName?n("div",{staticClass:"cs-button-box"},[n("el-form",{attrs:{inline:"","label-width":"80px"}},[n("el-form-item",{attrs:{label:"项目名称"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return n("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.fetchData(1)}}},[e._v("搜索")]),n("el-button",{on:{click:e.handleReset}},[e._v("重置")]),n("el-button",{attrs:{type:"success",loading:e.exportLoading},on:{click:e.handleExport}},[e._v("导出明细")])],1)],1)],1):n("div",{staticClass:"cs-button-box"},[n("el-button",{attrs:{type:"success",loading:e.exportLoading},on:{click:e.handleExport}},[e._v("导出明细")])],1),n("div",{staticClass:"info-box"},[n("div",{staticClass:"cs-col"},[n("span",[n("span",{staticClass:"info-label mb-8"},[e._v("新增总计数量/重量")]),n("i",[e._v(e._s(e.info.New_Count)+" / "+e._s(e._f("filterNum")(e.info.New_Weight))+"t")])]),n("span",[n("span",{staticClass:"info-label"},[e._v("生产中数量/重量")]),n("i",[e._v(e._s(e.info.Producing_Count)+" / "+e._s(e._f("filterNum")(e.info.Producing_Weight))+"t")])])]),n("div",{staticClass:"cs-col"},[n("span",[n("span",{staticClass:"info-label mb-8"},[e._v("清单总计数量/重量")]),n("i",[e._v(e._s(e.info.Deepen_Count)+" / "+e._s(e._f("filterNum")(e.info.Deepen_Weight))+"t")])]),n("span",[n("span",{staticClass:"info-label"},[e._v("未生产数量/重量")]),n("i",[e._v(e._s(e.info.UnProduce_Count)+" / "+e._s(e._f("filterNum")(e.info.UnProduce_Weight))+"t")])])]),e.isPart?e._e():n("div",{staticClass:"cs-col"},[n("span",[n("span",{staticClass:"info-label mb-8"},[e._v("已入库数量/重量")]),n("i",[e._v(e._s(e.info.In_Count)+" / "+e._s(e._f("filterNum")(e.info.In_Weight))+"t")])]),n("span",[n("span",{staticClass:"info-label mb-8"},[e._v("已发货数量/重量")]),n("i",[e._v(e._s(e.info.Out_Count)+" / "+e._s(e._f("filterNum")(e.info.Out_Weight))+"t")])])])]),n("div",{staticClass:"fff tb-x"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"加载中","empty-text":"暂无数据",height:"auto",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,"show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"title-prefix":["New_Count","Deepen_Count","Change_Count","Production_Status"].includes(t.Code)?{content:e.getContent(t.Code)}:null},scopedSlots:e._u(["Change_Date"===t.Code?{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("timeFormat")(n.Change_Date))+" ")]}}:{key:"default",fn:function(a){var o=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})]}))],2)],1),n("div",{staticClass:"data-info"},[n("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)])],1)},o=[]},"96b2":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("ac1f"),n("5319");var o=a(n("5530")),r=a(n("15ac")),i=n("7015f"),u=n("c685"),d=n("3166"),l=a(n("333d")),s=a(n("6612")),c=n("ed08");t.default={name:"PROComponentTracking",components:{Pagination:l.default},filters:{filterNum:function(e){return(0,s.default)(e).divide(1e3).format("0.[00]")}},mixins:[r.default],data:function(){return{nameMode:1,names:"",form:{Production_Status:"",Sys_Project_Id:"",Code_Like:"",Codes:"",Bill_No:"",Moc_Type_Id:""},info:{Deepen_Count:0,Deepen_Weight:0,In_Count:0,In_Weight:0,New_Count:0,New_Weight:0,Out_Count:0,Out_Weight:0,Producing_Count:0,Producing_Weight:0,UnProduce_Count:0,UnProduce_Weight:0},multipleSelection:[],activeName:"1",projectName:"",mocType:[],projectList:[],exportLoading:!1,tbLoading:!1,tbData:[],columns:[],tablePageSize:u.tablePageSize,queryInfo:{Page:1,PageSize:u.tablePageSize[0]},total:0}},computed:{subObj:function(){return(0,o.default)((0,o.default)({},this.form),{},{Type:this.curType})},curType:function(){return"1"===this.activeName?null:this.activeName},isPart:function(){return"零件"===this.curType}},watch:{names:function(e,t){this.changeMode()},nameMode:function(e,t){this.changeMode()}},mounted:function(){this.getTableConfig("PRoProjectTrackingConfig"),this.getBasicData(),this.fetchData(1),this.getSettingInfo()},methods:{changeMode:function(e){1===this.nameMode?(this.form.Code_Like=this.names,this.form.Codes=""):(this.form.Code_Like="",this.form.Codes=this.names.replace(/\s+/g,"\n"))},getSettingInfo:function(){var e=this;(0,i.GetMocOrderTypeList)({}).then((function(t){t.IsSucceed?e.mocType=t.Data:e.$message({message:t.Message,type:"error"})}))},handleClick:function(e,t){"1"===this.activeName?this.getTableConfig("PRoProjectTrackingConfig"):"构件"===this.activeName?this.getTableConfig("PRoComTrackingConfig"):"部件"===this.activeName?this.getTableConfig("PRoUnitPartTrackingConfig"):this.getTableConfig("PRoPartTrackingConfig"),this.handleReset()},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0,(0,i.GetMocAddComponentPartPageList)((0,o.default)((0,o.default)({},this.subObj),this.queryInfo)).then((function(e){var n;e.IsSucceed?(t.tbData=(null===e||void 0===e||null===(n=e.Data)||void 0===n?void 0:n.Data)||[],t.total=e.Data.TotalCount,t.getTotalInfo()):t.$message({message:e.Message,type:"error"});t.tbLoading=!1}))},getTotalInfo:function(){var e=this;(0,i.GetEngineeringContactMocAddComponentPartSummary)(this.subObj).then((function(t){if(t.IsSucceed){var n=t.Data,a=n.Deepen_Count,o=n.Deepen_Weight,r=n.In_Count,i=n.In_Weight,u=n.New_Count,d=n.New_Weight,l=n.Out_Count,s=n.Out_Weight,c=n.Producing_Count,f=n.Producing_Weight,g=n.UnProduce_Count,h=n.UnProduce_Weight;Object.assign(e.info,{Deepen_Count:a,Deepen_Weight:o,In_Count:r,In_Weight:i,New_Count:u,New_Weight:d,Out_Count:l,Out_Weight:s,Producing_Count:c,Producing_Weight:f,UnProduce_Count:g,UnProduce_Weight:h})}else e.$message({message:t.Message,type:"error"})}))},getBasicData:function(){var e=this;(0,d.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)}))},handleReset:function(){var e;null===(e=this.$refs["form"])||void 0===e||e.resetFields(),this.names="",this.form.Sys_Project_Id="",this.form.Codes="",this.form.Code_Like="",this.fetchData(1)},handleExport:function(){var e=this;this.exportLoading=!0,(0,i.ExportMocAddComponentPart)(this.subObj).then((function(t){t.IsSucceed?window.open((0,c.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"}),e.exportLoading=!1}))},tbSelectChange:function(e){this.multipleSelection=e.records},getContent:function(e){return"Deepen_Count"===e?this.curType?"该".concat(this.isPart?"零":"构","件在深化清单中的总数量"):"该项目下涉及到变更新增零构件的清单总数":"Change_Count"===e?"该".concat(this.isPart?"零":"构","件本次变更新增的总数"):"Production_Status"===e?"针对该".concat(this.isPart?"零":"构","件在清单内所有数量的状态显示"):"New_Count"===e?"该项目下涉及到变更新增零构件的新增总数":void 0}}}},ac13:function(e,t,n){"use strict";n.r(t);var a=n("96b2"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},aea0:function(e,t,n){},cdd9:function(e,t,n){"use strict";n.r(t);var a=n("91b5"),o=n("ac13");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("d2f4");var i=n("2877"),u=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"72eee131",null);t["default"]=u.exports},d2f4:function(e,t,n){"use strict";n("aea0")}}]);