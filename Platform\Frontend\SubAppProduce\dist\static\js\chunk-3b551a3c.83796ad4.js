(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3b551a3c"],{"00df":function(e,t,n){"use strict";n.r(t);var a=n("d87a"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},"082f":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("d3b7");var r=n("7f9d"),i=a(n("3796")),o=a(n("1e99"));t.default={components:{UploadExcel:i.default},mixins:[o.default],data:function(){return{disabledProject:!1,disabledArea:!1,btnLoading:!1,rules:{projectId:[{required:!0,message:"请选择",trigger:"change"}],areaId:[{required:!0,message:"请选择",trigger:"change"}]}}},methods:{beforeUpload:function(e){var t,n,a=this;if(e){var i,o=new FormData,l=null===(t=this.projectOption.find((function(e){return e.Id===a.queryForm.projectId})))||void 0===t?void 0:t.Sys_Project_Id;if(o.append("files",e),o.append("importModel",this.importType),o.append("sysProjectId",l),o.append("areaId",this.queryForm.areaId),null!==(n=this.selectRow)&&void 0!==n&&n.Schduling_Id)o.append("schdulingPlanId",null===(i=this.selectRow)||void 0===i?void 0:i.Schduling_Id);this.btnLoading=!0,(0,r.ImportSchduling)(o).then((function(e){e.IsSucceed?(a.$message({message:"导入成功",type:"success"}),a.$emit("close"),a.$emit("refresh")):a.$message({message:e.Message,type:"error"})})).finally((function(e){a.btnLoading=!1}))}},setRow:function(e,t){this.selectRow=e,this.selectRow?(this.importType=e.Schduling_Model,this.queryForm.projectId=e.Project_Id,this.queryForm.areaId=e.Area_Id,this.queryForm.projectId&&this.getAreaList(),this.disabledProject=!0,this.disabledArea=!0):this.importType=t||void 0},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){t&&e.$refs.upload.handleSubmit()}))}}}},"09f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=o,Math.easeInOutQuad=function(e,t,n,a){return e/=a/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,n){var o=i(),l=e-o,u=20,s=0;t="undefined"===typeof t?500:t;var c=function(){s+=u;var e=Math.easeInOutQuad(s,o,l,t);r(e),s<t?a(c):n&&"function"===typeof n&&n()};c()}},"0b2c":function(e,t,n){},1478:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("home")},r=[]},"15ac":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),r=n("c685");t.default={methods:{getTableConfig:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,a.GetGridByCode)({code:e,IsAll:n}).then((function(e){var a=e.IsSucceed,o=e.Data,l=e.Message;if(a){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});var u=[];t.tbConfig=Object.assign({},t.tbConfig,o.Grid),u=n?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=u.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+o.Grid.Row_Number||r.tablePageSize[0]),i(t.columns)}else t.$message({message:l,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit,a=e.type;this.queryInfo.Page="limit"===a?1:t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var r=this.columns[a];if(r.Code===t){n.Type=r.Type,n.Filter_Type=r.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},1816:function(e,t,n){"use strict";n.r(t);var a=n("39e9"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},1874:function(e,t,n){"use strict";n("de49")},"1e99":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("e9f5"),n("910d"),n("7d54"),n("d3b7"),n("159b");var a=n("3166"),r=n("f2f6"),i=n("8975");t.default={data:function(){return{queryForm:{projectId:"",install:"",areaId:""},projectOption:[],areaList:[],installOption:[],treeParams:{data:[],filterable:!1,clickParent:!0,props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}}}},mounted:function(){this.getProjectOption()},methods:{getAreaList:function(){var e=this;(0,a.GeAreaTrees)({projectId:this.queryForm.projectId,Area_Id:this.queryForm.install}).then((function(t){if(t.IsSucceed){var n=t.Data;e.setDisabledTree(n),e.treeParams.data=n,e.$nextTick((function(t){e.$refs.treeSelect.treeDataUpdateFun(n)}))}else e.$message({message:t.Message,type:"error"})}))},getInstall:function(){var e=this;(0,r.GetInstallUnitPageList)({Area_Id:this.queryForm.areaId,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.installOption=t.Data.Data.filter((function(e){return e.Id})):e.$message({message:t.Message,type:"error"})}))},projectChange:function(){this.queryForm.areaId="",this.queryForm.install="",this.queryForm.projectId&&this.getAreaList()},areaChange:function(e){if(this.areaIsImport=!1,this.queryForm.install="",this.queryForm.areaId&&!this.areaIsImport){this.getInstall();var t=null===e||void 0===e?void 0:e.Data,n=t.Demand_Begin_Date,a=t.Demand_End_Date;this.getAreaTime((0,i.timeFormat)(n),(0,i.timeFormat)(a))}},installChange:function(){this.getInstall()},areaClear:function(){this.queryForm.install=""},getProjectOption:function(){var e=this;(0,a.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.projectOption=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var n=e.Children;n&&n.length?e.disabled=!0:(e.disabled=!1,t.setDisabledTree(n))}))},getAreaTime:function(){}}}},"210d":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CancelCompSchduling=d,t.CancelPartSchduling=c,t.DelPartPlan=g,t.GetCanSchdulingParts=o,t.GetCanSchdulingUnits=l,t.GetPartEntity=u,t.GetPartList=i,t.GetPartSchdulingPageList=s,t.ImportPartSchduling=p,t.ImportPartSchdulingNew=v,t.ImportUnitSchdulingNew=b,t.SavePartRelease=m,t.SavePartTask=h,t.SavePartTaskDraft=f;var r=a(n("b775"));function i(e){return(0,r.default)({url:"/PRO/ProductionTask/GetPartList",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCanSchdulingParts",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCanSchdulingUnits",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ProductionTask/GetPartEntity",method:"post",data:e,timeout:12e5})}function s(e){return(0,r.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingPageList",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/ProductionSchduling/CancelPartSchduling",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/ProductionSchduling/CancelCompSchduling",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraft",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ProductionTask/SavePartTask",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProductionTask/SavePartRelease",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProductionSchduling/ImportPartSchduling",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlan",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/ProductionSchduling/ImportPartSchdulingNew",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/ProductionSchduling/ImportUnitSchdulingNew",method:"post",data:e})}},2546:function(e,t,n){},2604:function(e,t,n){"use strict";n("2546")},"2aa8":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d3b7"),n("3ca3"),n("ddb0");var r=a(n("736d6")),i=a(n("2082"));t.default={name:"PRO2ComScheduleNew",components:{Home:r.default},provide:{pageType:"com"},mixins:[i.default],data:function(){return{addPageArray:[{path:this.$route.path+"/draft",hidden:!0,component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-05a1e5cf"),n.e("chunk-ad623fea")]).then(n.bind(null,"d174"))},name:"PRO2ComScheduleDraftNew",meta:{title:"草稿",Id:this.$route.meta.Id}}]}}}},"2ec6":function(e,t,n){},3166:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=h,t.DeleteProject=c,t.GeAreaTrees=I,t.GetFileSync=O,t.GetInstallUnitIdNameList=C,t.GetNoBindProjectList=m,t.GetPartDeepenFileList=S,t.GetProjectAreaTreeList=_,t.GetProjectEntity=u,t.GetProjectList=l,t.GetProjectPageList=o,t.GetProjectTemplate=p,t.GetPushProjectPageList=P,t.GetSchedulingPartList=w,t.IsEnableProjectMonomer=d,t.SaveProject=s,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=v,t.UpdateProjectTemplateContract=b,t.UpdateProjectTemplateOther=y;var r=a(n("b775")),i=a(n("4328"));function o(e){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:i.default.stringify(e)})}function s(e){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:i.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function O(e){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"31b4":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{rules:e.rules,model:e.queryForm,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"项目名称",prop:"projectId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.disabledProject,filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.queryForm.projectId,callback:function(t){e.$set(e.queryForm,"projectId",t)},expression:"queryForm.projectId"}},e._l(e.projectOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域名称",prop:"areaId"}},[n("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{disabled:!e.queryForm.projectId||e.disabledArea,"select-params":{clearable:!0},"tree-params":e.treeParams},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.queryForm.areaId,callback:function(t){e.$set(e.queryForm,"areaId",t)},expression:"queryForm.areaId"}})],1)],1),n("upload-excel",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},r=[]},"382f":function(e,t,n){"use strict";n.r(t);var a=n("7930"),r=n("ae8b");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("2604");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"750502a0",null);t["default"]=l.exports},"39e9":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("caad"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7"),n("2532"),n("c7cd");var r=a(n("c14f")),i=a(n("1da1")),o=a(n("5530")),l=n("6186"),u=n("7f9d"),s=n("210d"),c=n("c3c6"),d=n("2f62");t.default={data:function(){return{btnLoading:!1,tbLoading:!1,total:0,columns:[],tbData:[],tbConfig:{},TotalCount:0,multipleSelection:[]}},computed:(0,o.default)({isCom:function(){return"com"===this.pageType}},(0,d.mapGetters)("factoryInfo",["workshopEnabled"])),inject:["pageType"],mounted:function(){},methods:{getConfig:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.isCom?"PROComWithdraw":"PROPartWithdraw_new");case 1:e.workshopEnabled||(e.columns=e.columns.filter((function(e){return"Workshop_Name"!==e.Code})));case 2:return t.a(2)}}),t)})))()},init:function(e){this.row=e,this.getConfig(),this.fetchData()},fetchData:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var n,a,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.tbLoading=!0,n=null,!e.isCom){t.n=2;break}return t.n=1,e.getComPageList(null===(a=e.row)||void 0===a?void 0:a.Schduling_Id);case 1:n=t.v,t.n=4;break;case 2:return t.n=3,e.getPartPageList(null===(i=e.row)||void 0===i?void 0:i.Schduling_Id);case 3:n=t.v;case 4:e.initData(n),e.tbLoading=!1;case 5:return t.a(2)}}),t)})))()},initData:function(e){this.tbData=e.filter((function(e){return e.Can_Cancel_Count})).map((function(e){return e.Cancel_Count=e.Can_Cancel_Count,e.checked=!1,e}))},getComPageList:function(e){var t=this;return new Promise((function(n,a){(0,u.GetPageSchdulingComps)({Schduling_Plan_Id:e}).then((function(e){if(e.IsSucceed){var r=e.Data,i=r.Schduling_Comps,o=r.Schduling_Plan;t.info=o,n(i||[])}else t.$message({message:e.Message,type:"error"}),a()}))}))},getPartPageList:function(e){var t=this;return new Promise((function(n,a){(0,s.GetPartEntity)({Schduling_Plan_Id:e}).then((function(e){if(e.IsSucceed){var r=e.Data,i=r.SarePartsModel,o=r.Schduling_Plan;t.info=o,n(i||[])}else t.$message({message:e.Message,type:"error"}),a()}))}))},handleSubmit:function(){var e=this;this.btnLoading=!0;var t=null,n={Schduling_Plan:this.info};this.isCom?(t=s.CancelCompSchduling,Object.assign(n,{Schduling_Comps:this.multipleSelection.map((function(e){return e.Cancel_Count=+e.Cancel_Count,e})).filter((function(e){return e.Cancel_Count}))})):(t=s.CancelPartSchduling,Object.assign(n,{SarePartsModel:this.multipleSelection.map((function(e){return e.Cancel_Count=+e.Cancel_Count,e})).filter((function(e){return e.Cancel_Count}))})),t(n).then((function(t){t.IsSucceed?(e.$message({message:"撤回成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))},tbSelectChange:function(e){this.multipleSelection=e.records},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Cancel_Count"===t.field},getTableConfig:function(e){var t=this;return(0,i.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,l.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,a=e.Data,r=e.Message;if(n){t.tbConfig=Object.assign({},t.tbConfig,a.Grid);var i=a.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return c.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),e})),t.columns.push({Display_Name:"可撤回数量",Code:"Can_Cancel_Count",Width:"120px"}),t.columns.push({Display_Name:"撤回数量",Code:"Cancel_Count",Width:"120px"})}else t.$message({message:r,type:"error"})}));case 1:return n.a(2)}}),n)})))()}}}},"3cb2":function(e,t,n){"use strict";n("0b2c")},"470d":function(e,t,n){"use strict";n.r(t);var a=n("7353"),r=n("7a5d");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("3cb2");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"0330f5c8",null);t["default"]=l.exports},"47ba":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d3b7");var a=n("7f9d");t.default={data:function(){return{tableData:[],tbLoading:!1}},computed:{isCom:function(){return"com"===this.pageType}},inject:["pageType"],methods:{init:function(e){this.row=e,this.fetchData()},fetchData:function(){var e=this;this.tbLoading=!0;var t=null;t=this.isCom?a.GetSchdulingCancelHistory:a.GetPartSchdulingCancelHistory,t({schdulingCode:this.row.Schduling_Code}).then((function(t){t.IsSucceed?e.tableData=t.Data:e.$message({message:t.Message,type:"error"})})).finally((function(t){e.tbLoading=!1}))}}}},"4e82":function(e,t,n){"use strict";var a=n("23e7"),r=n("e330"),i=n("59ed"),o=n("7b0b"),l=n("07fa"),u=n("083a"),s=n("577e"),c=n("d039"),d=n("addb"),f=n("a640"),h=n("3f7e"),m=n("99f4"),p=n("1212"),g=n("ea83"),v=[],b=r(v.sort),y=r(v.push),P=c((function(){v.sort(void 0)})),_=c((function(){v.sort(null)})),C=f("sort"),I=!c((function(){if(p)return p<70;if(!(h&&h>3)){if(m)return!0;if(g)return g<603;var e,t,n,a,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)v.push({k:t+a,v:n})}for(v.sort((function(e,t){return t.v-e.v})),a=0;a<v.length;a++)t=v[a].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),S=P||!_||!C||!I,w=function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:s(t)>s(n)?1:-1}};a({target:"Array",proto:!0,forced:S},{sort:function(e){void 0!==e&&i(e);var t=o(this);if(I)return void 0===e?b(t):b(t,e);var n,a,r=[],s=l(t);for(a=0;a<s;a++)a in t&&y(r,t[a]);d(r,w(e)),n=l(r),a=0;while(a<n)t[a]=r[a++];while(a<s)u(t,a++);return t}})},"500e":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("caad"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7"),n("ac1f"),n("2532"),n("841c");var r=a(n("c14f")),i=a(n("1da1")),o=a(n("5530")),l=n("ed08"),u=a(n("87e7c")),s=a(n("0f97")),c=a(n("15ac")),d=n("96a3"),f=n("7f9d"),h=n("210d"),m=a(n("d7ea")),p=a(n("ea7e")),g=a(n("382f")),v=a(n("1e99")),b=a(n("c1df")),y=a(n("470d")),P=n("8975"),_=n("2f62"),C=n("7196"),I=a(n("333d")),S=n("c685"),w=n("c24f");t.default={inject:["pageType"],components:{Pagination:I.default,WithdrawHistory:y.default,AddSchedule:u.default,DynamicDataTable:s.default,Withdraw:p.default,ComImport:m.default,PartImport:g.default},mixins:[c.default,v.default],data:function(){return{statusMap:{finish:"9",unOrdered:"0",ordered:"1"},scheduleType:{comp:1,part:2,comp_part:3},activeName:"1",pgLoading:!1,dialogVisible:!1,currentComponent:"",title:"",dWidth:"40%",queryForm:{Finish_Date_Begin:"",Finish_Date_End:"",Status:0,Workshop_Id:"",Schduling_Code:""},tablePageSize:S.tablePageSize,queryInfo:{Page:1,PageSize:S.tablePageSize[0]},workShopOption:[],columns:[],tbData:[],total:0,search:function(){return{}},roleList:[]}},computed:(0,o.default)({isCom:function(){return"com"===this.pageType},finishTime:{get:function(){return[(0,P.timeFormat)(this.queryForm.Finish_Date_Begin),(0,P.timeFormat)(this.queryForm.Finish_Date_End)]},set:function(e){if(e){var t=e[0],n=e[1];this.queryForm.Finish_Date_Begin=(0,P.timeFormat)(t),this.queryForm.Finish_Date_End=(0,P.timeFormat)(n)}else this.queryForm.Finish_Date_Begin="",this.queryForm.Finish_Date_End=""}}},(0,_.mapGetters)("factoryInfo",["workshopEnabled"])),watch:{activeName:function(e,t){this.queryForm.Status=+e,this.pgLoading=!0,this.getPageInfo()}},activated:function(){!this.isUpdate&&this.fetchData(1)},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.isUpdate=!0,e.getRoleAuthorization(),t.n=1,e.getFactoryInfo();case 1:return e.workshopEnabled&&e.getWorkshop(),e.search=(0,l.debounce)(e.fetchData,800,!0),t.n=2,e.getPageInfo();case 2:return t.a(2)}}),t)})))()},methods:{getPageInfo:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n="0"===e.activeName?"PROScheduleUnOrder":"1"===e.activeName?"PROScheduleIsOrder":"PROScheduleFinish",t.n=1,e.getTableConfig(n);case 1:e.workshopEnabled||(e.columns=e.columns.filter((function(e){return"Workshop_Name"!==e.Code}))),e.fetchData();case 2:return t.a(2)}}),t)})))()},handleClick:function(){},getFactoryInfo:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("factoryInfo/getWorkshop");case 1:return t.a(2)}}),t)})))()},canEditBtn:function(e){var t=e.Status,n=e.Schduling_Model;return n!==this.scheduleType.comp_part&&t===+this.statusMap.unOrdered},canImportBtn:function(e){var t=e.Status,n=e.Schduling_Model;return!(n===this.scheduleType.comp_part&&!this.isCom)&&t===+this.statusMap.unOrdered},canOrderBtn:function(e){var t=e.Status,n=e.Schduling_Model;return!(n===this.scheduleType.comp_part&&!this.isCom)&&t===+this.statusMap.unOrdered},canWithdrawBtn:function(e){e.Generate_Source;var t=e.Status,n=e.Schduling_Model;return!(n===this.scheduleType.comp_part&&!this.isCom)&&t===+this.statusMap.ordered},canWithdrawDraftBtn:function(e){var t=e.Generate_Source,n=e.Status,a=e.Schduling_Model,r=e.Receive_Count,i=e.Cancel_Count,o=e.Total_Change_Count;return 1!==t&&(!(a===this.scheduleType.comp_part&&!this.isCom||r>0||i>0||o>0)&&n===+this.statusMap.ordered)},canDeleteBtn:function(e){var t=e.Status,n=e.Schduling_Model;return!(n===this.scheduleType.comp_part&&!this.isCom)&&t===+this.statusMap.unOrdered},handleAdd:function(){var e=(0,d.getDraftQuery)("com"===this.pageType?"PRO2ComScheduleDraftNew":"PRO2PartScheduleDraftNew","add",this.isCom?"com":"part",{},this.$route);this.$router.push((0,o.default)({},e))},handleRowImport:function(e){var t=this;this.isCom?this.handleComImport(e):(this.dWidth="40%",this.currentComponent="PartImport",this.title="导入",this.dialogVisible=!0,this.$nextTick((function(n){t.$refs["content"].setRow(e)})))},handleComImportNew:function(e,t){this.handleComImport(e,t)},handleComImport:function(e,t){var n=this;this.dWidth="40%",this.currentComponent="ComImport",this.title="导入",this.dialogVisible=!0,this.$nextTick((function(a){e?n.$refs["content"].setRow(e):n.$refs["content"].setRow(null,t)}))},handlePartImport:function(){this.dWidth="40%",this.currentComponent="PartImport",this.title="导入",this.dialogVisible=!0},fetchData:function(e){var t=this;this.pgLoading=!0,e&&(this.queryInfo.Page=e);var n=null,a=this.queryForm,r=a.projectId,i=a.areaId,l=a.install,u=(a.Status,a.Schduling_Code),s=a.Workshop_Id,c=a.Finish_Date_Begin,d=a.Finish_Date_End,m=(0,o.default)((0,o.default)({},this.queryInfo),{},{Project_Id:r,Area_Id:i,InstallUnit_Id:l,Status:+this.activeName,Schduling_Code:u,Workshop_Id:s,Finish_Date_Begin:c,Finish_Date_End:d,Is_New_Schduling:!0});n=this.isCom?f.GetCompSchdulingPageList:h.GetPartSchdulingPageList,n(m).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):(t.$message({message:e.Message,type:"error"}),t.tbData=[],t.total=0)})).finally((function(e){t.isUpdate=!1,t.pgLoading=!1}))},handleDelete:function(e){var t=this;this.$confirm("是否删除该排产单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DelSchdulingPlanById)({schdulingPlanId:e.Schduling_Id}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleSave:function(e){var t=this;this.$confirm("是否下达该任务?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pgLoading=!0,(0,f.SaveSchdulingTaskById)({schdulingPlanId:e.Schduling_Id}).then((function(e){e.IsSucceed?(t.fetchData(1),t.$message({message:"下达成功",type:"success"}),t.pgLoading=!1):(t.$message({message:e.Message,type:"error"}),t.pgLoading=!1)}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleCanCelDetail:function(e){var t=this;this.dWidth="80%",this.currentComponent="WithdrawHistory",this.title="撤回历史",this.dialogVisible=!0,this.$nextTick((function(n){t.$refs["content"].init(e)}))},handleEdit:function(e){var t=this.isCom?"PRO2ComScheduleDraftNew":"PRO2PartScheduleDraftNew",n=(0,d.getDraftQuery)(t,"edit",this.pageType,{pid:e.Schduling_Id,areaId:e.Area_Id,install:e.InstallUnit_Id,model:e.Schduling_Model},this.$route);this.$router.push((0,o.default)({},n))},handleView:function(e){var t=this.isCom?"PRO2ComScheduleDraftNew":"PRO2PartScheduleDraftNew",n=(0,d.getDraftQuery)(t,"view",this.pageType,{pid:e.Schduling_Id,areaId:e.Area_Id,install:e.InstallUnit_Id,type:1===e.Generate_Source?"1":void 0},this.$route);this.$router.push((0,o.default)({},n))},handleWithdraw:function(e,t){var n=this;this.dWidth="80%",this.currentComponent="Withdraw",this.title="撤回",this.dialogVisible=!0,this.$nextTick((function(t){n.$refs["content"].init(e)}))},handleWithdrawAll:function(e){var t=this;this.$confirm("是否撤销排产单回草稿?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pgLoading=!0,(0,f.WithdrawScheduling)({schedulingId:e.Schduling_Id}).then((function(e){e.IsSucceed?(t.$message({message:"撤回草稿成功",type:"success"}),t.fetchData(1)):(t.$message({message:e.Message,type:"error"}),t.pgLoading=!1)})).catch((function(e){t.pgLoading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleClose:function(){this.dialogVisible=!1},handleReset:function(){this.$refs["form"].resetFields(),this.finishTime="",this.search(1)},moment:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YYYY-MM-DD";return""==(null!==e&&void 0!==e?e:"")?"":(0,b.default)(e).format(t)},getWorkshop:function(){var e=this;(0,C.GetWorkshopPageList)({Page:1,PageSize:-1}).then((function(t){var n;t.IsSucceed?(null!==t&&void 0!==t&&null!==(n=t.Data)&&void 0!==n&&n.Data||(e.workShopOption=[]),e.workShopOption=t.Data.Data.map((function(e){return{Id:e.Id,Display_Name:e.Display_Name}}))):e.$message({message:t.Message,type:"error"})}))},getRoles:function(e){return this.roleList.includes(e)},getRoleAuthorization:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,w.RoleAuthorization)({roleType:3,menuType:1,menuId:e.$route.meta.Id});case 1:n=t.v,n.IsSucceed?e.roleList=n.Data.map((function(e){return e.Code})):e.$message({type:"warning",message:n.Message});case 2:return t.a(2)}}),t)})))()}}}},"56d9":function(e,t,n){"use strict";n.r(t);var a=n("1478"),r=n("d59c");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"20b0e73a",null);t["default"]=l.exports},"67ad":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("3796")),i=n("210d");t.default={components:{Upload:r.default},data:function(){return{btnLoading:!1,schdulingPlanId:""}},created:function(){},mounted:function(){},methods:{beforeUpload:function(e){var t,n=this,a=new FormData;a.append("files",e),""!=(null!==(t=this.schdulingPlanId)&&void 0!==t?t:"")&&a.append("schdulingPlanId",this.schdulingPlanId),this.btnLoading=!0,(0,i.ImportPartSchduling)(a).then((function(e){e.IsSucceed?(n.$message({type:"success",message:"导入成功"}),n.$emit("refresh"),n.$emit("close")):n.$message({type:"error",message:e.Message}),n.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},"6c56":function(e,t,n){"use strict";n.r(t);var a=n("082f"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},7196:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteWorkshop=s,t.GetFactoryPeoplelist=i,t.GetWorkshopEntity=u,t.GetWorkshopPageList=l,t.SaveEntity=o;var r=a(n("b775"));function i(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:e})}},7353:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"wrapper"},[n("div",{staticClass:"h100"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"","row-config":{isCurrent:!0,isHover:!0},loading:e.tbLoading,align:"left",stripe:"",data:e.tableData,resizable:"","tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"150",field:e.isCom?"Comp_Code":"Part_Code",title:(e.isCom?"构件":"零件")+"名称"}}),e.isCom?e._e():n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Comp_Code",title:"所属构件"}}),n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Spec",title:"规格"}}),n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Texture",title:"材质"}}),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"120",field:"Length",title:"长度"}}),e.isCom?n("vxe-column",{attrs:{align:"left",sortable:"","min-width":"120",field:"Type",title:"构件类型"}}):e._e(),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"80",field:"Cancel_Count",title:"数量"}}),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"120",field:"Weight",title:"单重(kg)"}}),n("vxe-column",{attrs:{align:"center",sortable:"","min-width":"150",field:"Cancel_Date",title:"撤回时间"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("timeFormat")(n.Cancel_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])})],1)],1),n("footer",[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")])],1)])},r=[]},"736d6":function(e,t,n){"use strict";n.r(t);var a=n("b668"),r=n("8602");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("94006");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"1bea0407",null);t["default"]=l.exports},7930:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},r=[]},"7a5d":function(e,t,n){"use strict";n.r(t);var a=n("47ba"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},8602:function(e,t,n){"use strict";n.r(t);var a=n("500e"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},"87e7c":function(e,t,n){"use strict";n.r(t);var a=n("d778"),r=n("00df");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("a685");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"43cdf929",null);t["default"]=l.exports},94006:function(e,t,n){"use strict";n("2ec6")},"96a3":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniqueCode=t.getDraftQuery=t.FIX_COLUMN=void 0,n("b0c0");var r=a(n("5530"));t.getDraftQuery=function(e,t,n,a,i){return{name:e,query:(0,r.default)({status:t,pg_type:n,pg_redirect:i.name},a)}},t.uniqueCode=function(e){return"uuid"},t.FIX_COLUMN=["Comp_Code","Part_Code"]},a685:function(e,t,n){"use strict";n("e13f")},a7c71:function(e,t,n){"use strict";n("e271")},ae8b:function(e,t,n){"use strict";n.r(t);var a=n("67ad"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},b668:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"container abs100"},[n("div",{staticClass:"cs-tabs"},[n("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"进行中",name:e.statusMap.ordered}}),n("el-tab-pane",{attrs:{label:"已完成",name:e.statusMap.finish}}),n("el-tab-pane",{attrs:{label:"未下达",name:e.statusMap.unOrdered}})],1)],1),n("div",{staticClass:"search-wrapper"},[n("el-form",{ref:"form",attrs:{model:e.queryForm,inline:"","label-width":"100px"}},[n("el-form-item",[n("div",{staticClass:"btn-wrapper"},[e.getRoles(e.isCom?"ComAddSchedule":"PartAddScheduleNew")?n("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增排产单")]):e._e(),e.isCom?[e.getRoles("ImportComUnitPartsSchedule")?n("el-button",{on:{click:function(t){return e.handleComImportNew(null,3)}}},[e._v("导入构/部/零件排产")]):e._e(),e.getRoles("ImportComScheduleNew")?n("el-button",{on:{click:function(t){return e.handleComImportNew(null,1)}}},[e._v("导入构件排产")]):e._e()]:[e.getRoles("ImportPartSchedule")?n("el-button",{on:{click:e.handlePartImport}},[e._v("导入零件排产")]):e._e()]],2)]),n("el-form-item",{attrs:{label:"项目名称",prop:"projectId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.queryForm.projectId,callback:function(t){e.$set(e.queryForm,"projectId",t)},expression:"queryForm.projectId"}},e._l(e.projectOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域名称",prop:"areaId"}},[n("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{disabled:!e.queryForm.projectId,"select-params":{clearable:!0},"tree-params":e.treeParams},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.queryForm.areaId,callback:function(t){e.$set(e.queryForm,"areaId",t)},expression:"queryForm.areaId"}})],1),n("el-form-item",{attrs:{label:"批次",prop:"install"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.queryForm.areaId,clearable:"",placeholder:"请选择"},model:{value:e.queryForm.install,callback:function(t){e.$set(e.queryForm,"install",t)},expression:"queryForm.install"}},e._l(e.installOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),e.workshopEnabled?n("el-form-item",{attrs:{label:"所属车间",prop:"Workshop_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.queryForm.Workshop_Id,callback:function(t){e.$set(e.queryForm,"Workshop_Id",t)},expression:"queryForm.Workshop_Id"}},e._l(e.workShopOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1):e._e(),n("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.activeName!==e.statusMap.unOrdered,expression:"activeName!==statusMap.unOrdered"}],attrs:{label:"排产单号",prop:"Schduling_Code"}},[n("el-input",{attrs:{clearable:"",type:"text"},model:{value:e.queryForm.Schduling_Code,callback:function(t){e.$set(e.queryForm,"Schduling_Code",t)},expression:"queryForm.Schduling_Code"}})],1),n("el-form-item",{attrs:{label:"要求完成时间",prop:"finishTime"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.finishTime,callback:function(t){e.finishTime=t},expression:"finishTime"}})],1),n("el-form-item",[n("el-button",{on:{click:e.handleReset}},[e._v("重置")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("搜索")])],1)],1)],1),n("div",{staticClass:"main-wrapper"},[n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.pgLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,"row-config":{isCurrent:!0,isHover:!0},resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["Schduling_Code"===t.Code?{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleView(a)}}},[e._v(e._s(a.Schduling_Code))])]}}:["Finish_Date","Operator_Date","Order_Date"].includes(t.Code)?{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e.moment(a[t.Code]))+" ")]}}:"Status"===t.Code?{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(0===n.Status?"草稿":"已下达")+" ")]}}:"Cancel_Count"===t.Code?{key:"default",fn:function(t){var a=t.row;return[a.Cancel_Count?n("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleCanCelDetail(a)}}},[e._v(e._s(a.Cancel_Count))]):n("span",[e._v(e._s(a.Cancel_Count))])]}}:null],null,!0)})]})),e.statusMap.finish!==e.activeName?n("vxe-column",{attrs:{fixed:"right",title:"操作",width:e.activeName===e.statusMap.ordered?170:220,"min-width":e.activeName===e.statusMap.ordered?170:220,"show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e.canEditBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(a)}}},[e._v("修改 ")]):e._e(),e.canOrderBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleSave(a)}}},[e._v("下达 ")]):e._e(),e.statusMap.unOrdered===e.activeName?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(a)}}},[e._v("查看")]):e._e(),e.canWithdrawBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleWithdraw(a)}}},[e._v("撤销排产 ")]):e._e(),e.canWithdrawDraftBtn(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleWithdrawAll(a)}}},[e._v("撤回草稿 ")]):e._e(),e.canDeleteBtn(a)?n("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除 ")]):e._e()]}}],null,!1,3628944113)}):e._e()],2)],1),n("div",{staticClass:"data-info"},[n("Pagination",{attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)]),e.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",on:{close:e.handleClose,refresh:function(t){return e.fetchData(1)}}})],1):e._e()],1)},r=[]},c3c6:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniqueCode=t.getDraftQuery=t.FIX_COLUMN=void 0,n("b0c0");var r=a(n("5530"));t.getDraftQuery=function(e,t,n,a,i){return{name:e,query:(0,r.default)({status:t,pg_type:n,pg_redirect:i.name},a)}},t.uniqueCode=function(e){return"uuid"},t.FIX_COLUMN=["Comp_Code","Project_Name","Area_Name","Part_Code","InstallUnit_Name"]},d59c:function(e,t,n){"use strict";n.r(t);var a=n("2aa8"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},d778:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-form",{ref:"queryForm",attrs:{model:e.queryForm,rules:e.rules,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"项目名称",prop:"projectId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.queryForm.projectId,callback:function(t){e.$set(e.queryForm,"projectId",t)},expression:"queryForm.projectId"}},e._l(e.projectOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域名称",prop:"areaId"}},[n("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{"tree-params":e.treeParams,"select-params":{clearable:!1}},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.queryForm.areaId,callback:function(t){e.$set(e.queryForm,"areaId",t)},expression:"queryForm.areaId"}})],1),n("el-form-item",{attrs:{label:"批次",prop:"install",rules:[{required:e.installOption.length,message:"请选择",trigger:"change"}]}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",disabled:!e.installOption.length,placeholder:"请选择"},on:{change:e.installChange},model:{value:e.queryForm.install,callback:function(t){e.$set(e.queryForm,"install",t)},expression:"queryForm.install"}},e._l(e.installOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确定")])],1)],1)},r=[]},d7ea:function(e,t,n){"use strict";n.r(t);var a=n("31b4"),r=n("6c56");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("a7c71");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"0008802f",null);t["default"]=l.exports},d87a:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("5530"));n("14d9"),n("d3b7");var i=n("c3c6"),o=a(n("1e99")),l=n("7f9d");t.default={mixins:[o.default],data:function(){return{btnLoading:!1,rules:{projectId:[{required:!0,message:"请选择",trigger:"change"}],areaId:[{required:!0,message:"请选择",trigger:"change"}]}}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType}},methods:{getAreaTime:function(e,t){this.rangDate=[e,t]},submit:function(){var e=this;this.$refs["queryForm"].validate((function(t){t&&(e.btnLoading=!0,(0,l.CheckSchduling)({projectId:e.queryForm.projectId,areaId:e.queryForm.areaId,installId:e.queryForm.install,schdulingModel:e.isCom?1:2}).then((function(t){if(t.IsSucceed){e.$emit("close");var n=(0,i.getDraftQuery)("com"===e.pageType?"PRO2ComScheduleDraftNew":"PRO2PartScheduleDraftNew","add",e.pageType,(0,r.default)({},e.queryForm),e.$route);e.$router.push((0,r.default)({},n))}else e.$message({message:t.Message,type:"error"})})).finally((function(t){e.btnLoading=!1})))}))}}}},de49:function(e,t,n){},e13f:function(e,t,n){},e271:function(e,t,n){},ea7e:function(e,t,n){"use strict";n.r(t);var a=n("f45d"),r=n("1816");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("1874");var o=n("2877"),l=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"2c81c628",null);t["default"]=l.exports},f2f6:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=u,t.CheckPlanTime=s,t.DeleteInstallUnit=h,t.GetCompletePercent=b,t.GetEntity=P,t.GetInstallUnitAllInfo=d,t.GetInstallUnitComponentPageList=v,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=c,t.GetInstallUnitList=l,t.GetInstallUnitPageList=o,t.GetProjectInstallUnitList=y,t.ImportInstallUnit=p,t.InstallUnitInfoTemplate=m,t.SaveInstallUnit=g,t.SaveOhterSourceInstallUnit=_;var r=a(n("b775")),i=a(n("4328"));function o(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function s(e){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function c(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function d(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function h(e){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function m(e){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function p(e){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function P(e){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:i.default.stringify(e)})}function _(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},f45d:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"wrapper"},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-wrapper",attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","checkbox-config":{checkField:"checked"},height:"auto",align:"left",stripe:"","row-config":{isCurrent:!0,isHover:!0},size:"medium","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Is_Component"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-tag",{attrs:{type:a.Is_Component?"danger":"success"}},[e._v(e._s(a.Is_Component?"否":"是"))])]}}],null,!0)}):"Cancel_Count"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,"edit-render":{},field:t.Code,title:t.Display_Name,sortable:"",fixed:"right","min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"integer",min:1,max:a.Can_Cancel_Count},model:{value:a.Cancel_Count,callback:function(t){e.$set(a,"Cancel_Count",e._n(t))},expression:"row.Cancel_Count"}})]}}],null,!0)}):n("vxe-column",{key:t.Id,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)],1),n("footer",[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)])},r=[]}}]);