(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b088e49e"],{"2a7f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeletePartType=d,t.GetConsumingProcessAllList=h,t.GetFactoryPartTypeIndentifySetting=c,t.GetPartTypeEntity=m,t.GetPartTypeList=s,t.GetPartTypePageList=i,t.GetPartTypeTree=p,t.SaveConsumingProcessAllList=g,t.SavePartType=u,t.SavePartTypeIdentifySetting=f,t.SettingDefault=l;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/PartType/GetPartTypePageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartType/GetPartTypeList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartType/SettingDefault",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/PartType/SavePartType",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/PartType/DeletePartType",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/PartType/GetFactoryPartTypeIndentifySetting",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/PartType/SavePartTypeIdentifySetting",method:"post",data:e})}function p(e){return(0,o.default)({url:"/pro/parttype/GetPartTypeTree",method:"post",data:r.default.stringify(e)})}function m(e){return(0,o.default)({url:"/pro/parttype/GetPartTypeEntity",method:"post",data:r.default.stringify(e)})}function h(e){return(0,o.default)({url:"/PRO/PartType/GetConsumingProcessAllList",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/PartType/SaveConsumingProcessAllList",method:"post",data:e})}},"456d":function(e,t,a){"use strict";a.r(t);var n=a("b1fb"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"462c":function(e,t,a){"use strict";a.r(t);var n=a("cff3"),o=a("fa67");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("becb");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"2328841b",null);t["default"]=s.exports},"5ddf":function(e,t,a){"use strict";a.r(t);var n=a("e4ce"),o=a("456d");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"68b4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("2909")),r=n(a("5530")),i=n(a("c14f")),s=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("caad"),a("a15b"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("a9e3"),a("b680"),a("d3b7"),a("25f0"),a("159b");var l=a("6186"),u=n(a("1463")),d=n(a("a888")),c=n(a("bc3a")),f=n(a("bae6")),p=a("7f9d"),m=n(a("bad9")),h=n(a("3339")),g=n(a("5ddf")),P=a("e6cd"),y=a("2ef0"),b=n(a("a657")),v=a("ed08");t.default={directives:{elDragDialog:d.default},components:{DynamicTableFields:b.default,SelectProject:m.default,ExpandableSection:f.default,TreeDetail:u.default,SelectDict:h.default,SelectPartType:g.default},data:function(){return{showExpand:!0,treeData:[],treeLoading:!0,expandedKey:"",projectName:"",statusType:"",tbData:[],total:0,tbLoading:!1,pgLoading:!1,searchParams:{ProjectName:"",BatchNo:"",PartNo:"",NestCode:"",MachineId:"",NestProgress:"",PartType:""},exportParams:{deviceType:"",printType:"0",importModel:"",FileName:""},dialogVisible:!1,selectList:[],columns:[],columnsOption:[],title:"",width:"60%",dialogVisibleMachine:!1,assignMachineParams:{},assignMachineTbData:[],machineColumns:[],selectListIds:[],machineLoading:!1,nestingMachine:[],batchEditMachineId:"",submitLoading:!1,selectedMachineIndexs:[],sumData:{},checkedTree:[],exportType:""}},created:function(){this.getTableConfig("NestingBillTable","columns"),this.getDict("nesting_machine","nestingMachine"),this.fetchTreeData()},methods:{getSumData:function(){var e=this,t=(0,y.groupBy)(this.assignMachineTbData,"MachineId"),a={"未分配":0};this.nestingMachine.forEach((function(e){a[e.Value]=0}));var n=function(n){var o=e.nestingMachine.find((function(e){return e.Id===n}));o?a[o.Value]=t[n]:a["未分配"]=t[n]};for(var o in t)n(o);this.sumData=(0,y.mapValues)(a,(function(e){return(0,y.sumBy)(e,"PartArea").toFixed(2)/1}))},footerMethod:function(e){var t=this,a=e.columns,n=e.data,o=[a.map((function(e,a){return["PartArea","PartCount"].includes(e.field)?t.sumNum(n,e.field,2):1===a?"合计":null}))];return o},sumNum:function(e,t,a){for(var n=0,o=0;o<e.length;o++)n+=Number(e[o][t])||0;return n.toFixed(a)/1},getDict:function(e,t){var a=this;(0,P.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){a[t]=e.Data}))},fetchTreeData:function(){var e=this;this.treeLoading=!0,(0,p.GetNestingBillTreeList)({model:{Keyword:this.projectName,Status:this.statusType},PageInfo:{Page:1,PageSize:100}}).then((function(t){e.treeData=t.Data.Data,e.treeData.length>0||(e.currentNode={},e.tbData=[])})).finally((function(){return[e.treeLoading=!1]}))},handleNodeClick:function(e){var t=e.data,a=e.dataArray;this.selectListIds=[],this.currentNode=t,this.expandedKey=t.TaskCode,this.checkedTree=a,this.pgLoading=!0,this.handleSearch(!0,!0)},handleSearch:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e&&this.$refs.searchParams.resetFields(),t&&this.fetchData()},getTableConfig:function(e,t){var a=this;return(0,s.default)((0,i.default)().m((function n(){return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,l.GetGridByCode)({code:e}).then((function(e){a.$set(a,t,e.Data.ColumnList)}));case 1:return n.a(2)}}),n)})))()},updateColumns:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,e.getTableConfig("NestingBillTable","columns");case 1:e.tbLoading=!1;case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,(0,p.GetNestingBillDetailList)((0,r.default)({TaskCode:e.checkedTree.checkedKeys.join(",")},e.searchParams));case 1:a=t.v,e.tbData=a.Data,e.tbLoading=!1,e.pgLoading=!1;case 2:return t.a(2)}}),t)})))()},tbSelectChange:function(e){this.selectListIds=e.records.map((function(e){return e.Id}))},machineTbSelectChange:function(e){this.selectedMachineIndexs=e.records.map((function(e,t){return t}))},getTbData:function(e){var t=e.CountInfo;this.tipLabel=t},getFile:function(e){return new Promise((function(t,a){(0,c.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))},handleOpenExportDialog:function(e,t){this.title=t,this.exportType=e,this.width="40%";var a=this.checkedTree.checkedNodes[0],n=a.ProjectName,o=a.TaskCode;this.exportParams.FileName="".concat(n,"-").concat(o),this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.dialogVisibleMachine=!1,this.batchEditMachineId="",this.selectedMachineIndexs=[]},assignTheMachine:function(){var e=this;this.title="分配机床",this.width="60%",this.dialogVisibleMachine=!0,this.machineLoading=!0,this.getTableConfig("assignMachine","machineColumns"),(0,p.GetDetailSummaryList)({TaskDetailIds:this.selectListIds}).then((function(t){e.assignMachineTbData=t.Data,e.getSumData()})).finally((function(){e.machineLoading=!1}))},batchEdit:function(){var e=this;this.selectedMachineIndexs.forEach((function(t){e.assignMachineTbData[t].MachineId=e.batchEditMachineId})),this.getSumData()},submitAssignMachine:function(){var e=this;this.submitLoading=!0,(0,p.UpdateMachineName)({TaskCode:this.expandedKey,details:this.assignMachineTbData}).then((function(t){t.IsSucceed?(e.$message.success("分配成功"),e.handleClose(),e.submitLoading=!1):e.$message.error(t.Message)}))},customFilterFun:function(e,t,a){var n=e.split(","),r=n[0],i=n[1];if(!e)return!0;var s=a.parent,l=[a.label],u=[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"],d=1;while(d<a.level)l=[].concat((0,o.default)(l),[s.label]),u=[].concat((0,o.default)(u),[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"]),s=s.parent,d++;l=l.filter((function(e){return!!e})),u=u.filter((function(e){return!!e}));var c=!0,f=!0;return this.statusType&&(f=u.some((function(e){return-1!==e.indexOf(i)}))),this.projectName&&(c=l.some((function(e){return-1!==e.indexOf(r)}))),c&&f},submitExport:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$refs.machineForm.validate();case 1:a={Sigma:p.SigmaWOLExport,Lentak:p.LentakExport,Profiles:p.ProfilesExport,Bochu:p.BochuAddTask},n=a[e.exportType],n({Machine:e.exportParams.deviceType,ImportMode:e.exportParams.importModel,TagType:e.exportParams.printType,TaskDetailIdList:e.selectListIds,TaskCode:e.currentNode.TaskCode,FileName:e.exportParams.FileName}).then((function(t){t.IsSucceed?window.open((0,v.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)})),e.handleClose();case 2:return t.a(2)}}),t)})))()}}}},a024:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=E,t.AddTechnology=l,t.AddWorkingProcess=s,t.DelLib=F,t.DeleteProcess=D,t.DeleteProcessFlow=T,t.DeleteTechnology=L,t.DeleteWorkingTeams=_,t.GetAllProcessList=f,t.GetCheckGroupList=I,t.GetChildComponentTypeList=N,t.GetFactoryAllProcessList=p,t.GetFactoryPeoplelist=O,t.GetFactoryWorkingTeam=P,t.GetGroupItemsList=v,t.GetLibList=i,t.GetLibListType=A,t.GetProcessFlow=m,t.GetProcessFlowListWithTechnology=h,t.GetProcessList=d,t.GetProcessListBase=c,t.GetProcessListTeamBase=C,t.GetProcessListWithUserBase=w,t.GetProcessWorkingTeamBase=R,t.GetTeamListByUser=j,t.GetTeamProcessList=b,t.GetWorkingTeam=y,t.GetWorkingTeamBase=k,t.GetWorkingTeamInfo=M,t.GetWorkingTeams=x,t.GetWorkingTeamsPageList=G,t.SaveWorkingTeams=S,t.UpdateProcessTeam=g;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function p(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function h(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function P(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function b(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function v(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function T(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function L(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function D(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function k(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function C(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function w(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a888:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("d565")),r=function(e){e.directive("el-drag-dialog",o.default)};window.Vue&&(window["el-drag-dialog"]=o.default,Vue.use(r)),o.default.install=r;t.default=o.default},b1fb:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1"));a("a9e3");a("a024");var i=a("ed08"),s=a("2a7f");t.default={name:"SelectPartType",props:{value:{type:[Array,Number,String],default:""},multiple:{type:Boolean,default:!1}},data:function(){return{list:[],selectedValue:Array.isArray(this.value)?(0,i.deepClone)(this.value):this.value}},watch:{value:{handler:function(e){this.selectedValue=Array.isArray(e)?(0,i.deepClone)(e):e},immediate:!0}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:(0,s.GetPartTypeList)({}).then((function(t){e.list=t.Data}));case 1:return t.a(2)}}),t)})))()}}}},becb:function(e,t,a){"use strict";a("d894")},cf45:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=o,a("d3b7");var n=a("6186");function o(e){return new Promise((function(t,a){(0,n.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},cff3:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"h100 app-wrapper",attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff ",attrs:{width:300},model:{value:e.showExpand,callback:function(t){e.showExpand=t},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[a("div",{staticClass:"tree-search"},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"套料进度选择"},on:{change:e.fetchTreeData},model:{value:e.statusType,callback:function(t){e.statusType=t},expression:"statusType"}},[a("el-option",{attrs:{label:"未完成",value:"未完成"}}),a("el-option",{attrs:{label:"已完成",value:"已完成"}})],1),a("el-input",{attrs:{placeholder:"工序任务单号",size:"small",clearable:"","suffix-icon":"el-icon-search"},on:{blur:e.fetchTreeData,clear:e.fetchTreeData},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fetchTreeData(t)}},model:{value:e.projectName,callback:function(t){e.projectName="string"===typeof t?t.trim():t},expression:"projectName"}})],1),a("el-divider",{staticClass:"cs-divider"}),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":e.customFilterFun,loading:e.treeLoading,"tree-data":e.treeData,"node-key":"TaskCode","expanded-key":e.expandedKey,"show-checkbox":"","check-on-click-node":!0},on:{check:e.handleNodeClick},scopedSlots:e._u([{key:"csLabel",fn:function(t){var n=t.data;return[[a("span",{staticClass:"cs-blue"}),e._v(e._s(n.TaskCode)+" "),a("span",{class:["cs-tag","已完成"==n.Status?"greenBg":"orangeBg"]},[a("i",{class:["已完成"==n.Status?"fourGreen":"fourOrange"]},[e._v(e._s("已完成"==n.Status?"已完成":"未完成"))])]),a("span",{class:["cs-tag",n.IsMatched?"greenBg":"orangeBg"]},[a("i",{class:[n.IsMatched?"fourGreen":"fourOrange"]},[e._v(e._s(n.IsMatched?"已关联":"未关联"))])])]]}}])})],1)],1)]),a("div",{staticClass:"cs-right"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"searchParams",staticClass:"demo-form-inline",attrs:{model:e.searchParams,inline:""}},[a("el-form-item",{attrs:{label:"项目名称",prop:"ProjectName"}},[a("SelectProject",{model:{value:e.searchParams.ProjectName,callback:function(t){e.$set(e.searchParams,"ProjectName",t)},expression:"searchParams.ProjectName"}})],1),a("el-form-item",{attrs:{label:"批次",prop:"BatchNo"}},[a("el-input",{attrs:{placeholder:"请输入"},model:{value:e.searchParams.BatchNo,callback:function(t){e.$set(e.searchParams,"BatchNo",t)},expression:"searchParams.BatchNo"}})],1),a("el-form-item",{attrs:{label:"零件号",prop:"PartNo"}},[a("el-input",{attrs:{placeholder:"请输入"},model:{value:e.searchParams.PartNo,callback:function(t){e.$set(e.searchParams,"PartNo",t)},expression:"searchParams.PartNo"}})],1),a("el-form-item",{attrs:{label:"套料任务单号",prop:"NestCode"}},[a("el-input",{attrs:{placeholder:"请输入"},model:{value:e.searchParams.NestCode,callback:function(t){e.$set(e.searchParams,"NestCode",t)},expression:"searchParams.NestCode"}})],1),a("el-form-item",{attrs:{label:"机床",prop:"MachineId"}},[a("SelectDict",{attrs:{code:"nesting_machine"},model:{value:e.searchParams.MachineId,callback:function(t){e.$set(e.searchParams,"MachineId",t)},expression:"searchParams.MachineId"}})],1),a("el-form-item",{attrs:{label:"套料进度",prop:"NestProgress"}},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"请选择"},model:{value:e.searchParams.NestProgress,callback:function(t){e.$set(e.searchParams,"NestProgress",t)},expression:"searchParams.NestProgress"}},[a("el-option",{attrs:{label:"未完成",value:"未完成"}}),a("el-option",{attrs:{label:"已完成",value:"已完成"}})],1)],1),a("el-form-item",{attrs:{label:"零件类型",prop:"PartType"}},[a("SelectPartType",{model:{value:e.searchParams.PartType,callback:function(t){e.$set(e.searchParams,"PartType",t)},expression:"searchParams.PartType"}})],1),a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSearch(!1,!0)}}},[e._v("搜索 ")]),a("el-button",{on:{click:function(t){return e.handleSearch(!0)}}},[e._v("重置")])],1)],1)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},[a("el-button",{attrs:{type:"primary",disabled:!e.selectListIds.length},on:{click:function(t){return e.handleOpenExportDialog("Sigma","导出Sigma文件")}}},[e._v("导出Sigma文件 ")]),a("el-button",{attrs:{type:"primary",disabled:!e.selectListIds.length},on:{click:function(t){return e.handleOpenExportDialog("Lentak","导出Lentak文件")}}},[e._v("导出Lentak文件 ")]),a("el-button",{attrs:{type:"primary",disabled:!e.selectListIds.length},on:{click:function(t){return e.handleOpenExportDialog("Profiles","导出型材套料文件")}}},[e._v("导出型材套料文件 ")]),a("el-button",{attrs:{type:"primary",disabled:!e.selectListIds.length},on:{click:function(t){return e.handleOpenExportDialog("Bochu","推送柏楚套料")}}},[e._v("推送柏楚套料 ")]),a("el-button",{attrs:{type:"primary",disabled:!e.selectListIds.length},on:{click:e.assignTheMachine}},[e._v("分配机床 ")]),a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":"NestingBillTable"},on:{updateColumn:e.updateColumns}})],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[e.tbLoading?e._e():a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","empty-render":{name:"NotData"},height:"auto","auto-resize":"",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{"show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width?t.Width:120,fixed:t.Is_Frozen?t.Frozen_Dirction:""},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return[a("span",[e._v(e._s(o[t.Code]||"-"))])]}}],null,!0)})}))],2)],1)])])],1),e.dialogVisibleMachine?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.title,visible:e.dialogVisibleMachine,width:e.width},on:{"update:visible":function(t){e.dialogVisibleMachine=t},close:e.handleClose}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("span",[e._v("机床：")]),a("el-select",{attrs:{clearable:"",filterable:""},model:{value:e.batchEditMachineId,callback:function(t){e.batchEditMachineId=t},expression:"batchEditMachineId"}},e._l(e.nestingMachine,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Value}})})),1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.batchEdit}},[e._v("批量修改")]),a("div",{staticStyle:{"margin-left":"auto",color:"#298DFF"}},e._l(e.sumData,(function(t,n){return a("span",{key:n,staticStyle:{"margin-left":"10px"}},[e._v(e._s(n)+"："+e._s(t))])})),0)],1),a("div",{staticStyle:{height:"calc(100vh - 500px)","margin-top":"16px"}},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.machineLoading,expression:"machineLoading"}],staticClass:"cs-vxe-table",attrs:{"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"auto","auto-resize":"",align:"left",stripe:"",data:e.assignMachineTbData,resizable:"","tooltip-config":{enterable:!0},"edit-config":{trigger:"click",mode:"cell",showIcon:!0},"show-footer":"","footer-method":e.footerMethod},on:{"checkbox-all":e.machineTbSelectChange,"checkbox-change":e.machineTbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.machineColumns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.fixed,"show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width?t.Width:120},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return["MachineId"===t.Code?[a("el-select",{attrs:{clearable:"",filterable:""},on:{change:e.getSumData},model:{value:o[t.Code],callback:function(a){e.$set(o,t.Code,a)},expression:"row[item.Code]"}},e._l(e.nestingMachine,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Value}})})),1)]:[a("span",[e._v(e._s(o[t.Code]||"-"))])]]}}],null,!0)})}))],2)],1),a("div",{staticStyle:{"text-align":"right","margin-top":"16px"}},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submitAssignMachine}},[e._v("确 定")])],1)]):e._e(),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-form",{ref:"machineForm",attrs:{"label-width":"80px",model:e.exportParams}},["Sigma"===e.exportType?[a("el-form-item",{attrs:{label:"设备类型",prop:"deviceType",rules:[{required:!0,message:"请选择",trigger:"blur"}]}},[a("SelectDict",{attrs:{code:"nesting_sigma_device"},model:{value:e.exportParams.deviceType,callback:function(t){e.$set(e.exportParams,"deviceType",t)},expression:"exportParams.deviceType"}})],1),a("el-form-item",{attrs:{label:"打印类型",prop:"printType",rules:[{required:!0,message:"请选择",trigger:"blur"}]}},[a("el-radio-group",{model:{value:e.exportParams.printType,callback:function(t){e.$set(e.exportParams,"printType",t)},expression:"exportParams.printType"}},[a("el-radio",{attrs:{label:"0"}},[e._v("打印零件序号")]),a("el-radio",{attrs:{label:"1"}},[e._v("打印零件号")])],1)],1),a("el-form-item",{attrs:{label:"导入模式",prop:"importModel",rules:[{required:!0,message:"请选择",trigger:"blur"}]}},[a("SelectDict",{attrs:{code:"nesting_import_model"},model:{value:e.exportParams.importModel,callback:function(t){e.$set(e.exportParams,"importModel",t)},expression:"exportParams.importModel"}})],1)]:e._e(),a("el-form-item",{attrs:{label:"文件名称",prop:"FileName",rules:[{required:!0,message:"请选择",trigger:"blur"}]}},[a("el-input",{model:{value:e.exportParams.FileName,callback:function(t){e.$set(e.exportParams,"FileName",t)},expression:"exportParams.FileName"}})],1)],2),a("div",{staticStyle:{"text-align":"right","margin-top":"16px"}},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submitExport}},[e._v("确 定")])],1)],1):e._e()],1)},o=[]},d565:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319");t.default={bind:function(e,t,a){var n=e.querySelector(".el-dialog__header"),o=e.querySelector(".el-dialog");n.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var r=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();n.onmousedown=function(e){var t=e.clientX-n.offsetLeft,i=e.clientY-n.offsetTop,s=o.offsetWidth,l=o.offsetHeight,u=document.body.clientWidth,d=document.body.clientHeight,c=o.offsetLeft,f=u-o.offsetLeft-s,p=o.offsetTop,m=d-o.offsetTop-l,h=r(o,"left"),g=r(o,"top");h.includes("%")?(h=+document.body.clientWidth*(+h.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(h=+h.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var n=e.clientX-t,r=e.clientY-i;-n>c?n=-c:n>f&&(n=f),-r>p?r=-p:r>m&&(r=m),o.style.cssText+=";left:".concat(n+h,"px;top:").concat(r+g,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}},d894:function(e,t,a){},e41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=d,t.GetPartsList=s,t.GetProjectAreaTreeList=r,t.ImportParts=u,t.SaveProjectAreaSort=i;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},e4ce:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:e.multiple},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)},o=[]},e6cd:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCompanySteam=v,t.AddDeviceTypeProperty=$,t.AddExaminations=q,t.AddFlow=w,t.AddPreliminaryRules=u,t.AddSysDeviceType=B,t.DelDelaytasks=c,t.DeleteDeviceTypeProperty=z,t.DeleteExaminations=H,t.DeleteFlow=I,t.DeleteSysDeviceType=V,t.EditDeviceTypeProperty=Y,t.EditExaminations=K,t.EditFlow=O,t.EditPreliminaryRules=d,t.EditSysDeviceType=W,t.ExportExaminationAtten=X,t.GetAllEntities=i,t.GetAttachmentAdd=F,t.GetAttachmentDelete=A,t.GetCompanyList=P,t.GetConfigTemplateList=ee,t.GetDelaytasksInfo=f,t.GetDictionaryDetailListByCode=s,t.GetEntity=p,t.GetExaminationDetail=J,t.GetExaminationEntities=U,t.GetFileAdd=b,t.GetFileDelete=L,t.GetFileEdit=T,t.GetFileInfo=y,t.GetFileList=h,t.GetFlowList=N,t.GetGroupTree=m,t.GetPreliminaryRules=l,t.GetProfessionalAdd=x,t.GetProfessionalDelete=D,t.GetProfessionalEdit=G,t.GetProfessionalInfo=S,t.GetProjectDeviceTypeTree=R,t.GetProjectsNodeAdd=M,t.GetProjectsNodeDelete=_,t.GetProjectsNodeEdit=k,t.GetProjectsNodeInfo=C,t.GetPushMessagePageList=Q,t.GetStemList=g,t.GetSysDeviceTypeTree=j,t.GetWorking_ObjectList=r,t.InfoFlow=E,t.ResendPushMessage=Z,t.SaveModifyChanges=te;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PLM/Plm_Projects/GetWorking_ObjectList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PLM/Plm_Professional_Type/GetAllEntities",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PLM/Plm_Delay_Tasks/GetEntities",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PLM/Plm_Delay_Tasks/Add",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PLM/Plm_Delay_Tasks/Edit",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PLM/Plm_Delay_Tasks/Delete",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PLM/Plm_Delay_Tasks/GetEntity",method:"post",data:e})}function p(e){return(0,o.default)({url:"/SYS/UserGroup/GetEntity",method:"post",data:e})}function m(e){return(0,o.default)({url:"/SYS/UserGroup/GetGroupTree",method:"post",data:e})}function h(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:e})}function g(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetStemList",method:"post",data:e})}function P(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetCompanyList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:e})}function b(e){return(0,o.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:e})}function v(e){return(0,o.default)({url:"/SYS/Sys_FileType/AddCompanySteam",method:"post",data:e})}function T(e){return(0,o.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:e})}function L(e){return(0,o.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PLM/Plm_Professional_Type/Delete",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PLM/Plm_Professional_Type/Add",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PLM/Plm_Professional_Type/Edit",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PLM/Plm_Professional_Type/GetEntity",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PLM/Plm_Projects_Node/Delete",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PLM/Plm_Projects_Node/Add",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PLM/Plm_Projects_Node/Edit",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PLM/Plm_Projects_Node/GetEntity",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PLM/FlowManagement/Add",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PLM/FlowManagement/Edit",method:"post",data:e})}function I(e){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/Delete",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PLM/FlowManagement/GetProjectsflowmanagementEntity",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PLM/FlowManagement/GetProjectsflowmanagements",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PLM/Attachment/Add",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PLM/Attachment/Delete",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PLM/ProjectDevice/GetSysDeviceTypeTree",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PLM/ProjectDevice/GetSysDeviceTypeTree",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PLM/ProjectDevice/AddSysDeviceType",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PLM/ProjectDevice/EditSysDeviceType",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PLM/ProjectDevice/DeleteSysDeviceType",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PLM/ProjectDevice/AddDeviceTypeProperty",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PLM/ProjectDevice/EditDeviceTypeProperty",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PLM/ProjectDevice/DeleteDeviceTypeProperty",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PLM/ProcessReport/GetExaminationEntities",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PLM/ProcessReport/AddExaminations",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PLM/ProcessReport/EditExaminations",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PLM/ProcessReport/DeleteExaminations",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PLM/ProcessReport/GetExaminationDetail",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PLM/ProcessReport/ExportExaminationAtten",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PLM/PushMessage/GetPushMessagePageList",method:"post",data:e})}function Z(e){return(0,o.default)({url:"/PLM/PushMessage/ResendPushMessage",method:"post",data:e})}function ee(e){return(0,o.default)({url:"/SYS/ColumnConfiguration/GetConfigTemplateList",method:"post",data:e})}function te(e){return(0,o.default)({url:"/SYS/ColumnConfiguration/SaveModifyChanges",method:"post",data:e})}},fa67:function(e,t,a){"use strict";a.r(t);var n=a("68b4"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a}}]);