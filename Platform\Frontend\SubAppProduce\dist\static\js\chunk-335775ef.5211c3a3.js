(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-335775ef"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,r){return e/=r/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=n(),l=e-i,u=20,s=0;t="undefined"===typeof t?500:t;var d=function(){s+=u;var e=Math.easeInOutQuad(s,i,l,t);o(e),s<t?r(d):a&&"function"===typeof a&&a()};d()}},"1382b":function(e,t,a){"use strict";a("458a")},"458a":function(e,t,a){},"4f7b":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var o=r(a("c14f")),n=r(a("1da1")),i=a("8975"),l=a("9002"),u=a("3c4a"),s=a("209b"),d=(a("8378"),r(a("6612")));t.default={data:function(){return{form:{Begin_Date:null,Category_Id:null,End_Date:null,LT_Id:null,Mat_Name:null,Operator_Id:null,Store_Type:null,Sys_Project_Id:null,WH_Id:null},categoryOptions:{"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},projectNameData:[],warehouses:[],locations:[],columns:[],tbData:[],LTDisabled:!1,tbLoading:!1,btnLoading:!1}},inject:["projectData","isRaw","category"],computed:{planTime:{get:function(){return[(0,i.timeFormat)(this.form.Begin_Date),(0,i.timeFormat)(this.form.End_Date)]},set:function(e){if(e){var t=e[0],a=e[1];this.form.Begin_Date=(0,i.timeFormat)(t),this.form.End_Date=(0,i.timeFormat)(a)}else this.form.Begin_Date="",this.form.End_Date=""}}},mounted:function(){var e=this;return(0,n.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)(e.isRaw?"PROInventoryMoveStore":"PROInventoryAuxMoveStore");case 1:e.columns=t.v,e.getWarehouseList(),e.projectNameData=e.projectData(),e.getCategoryList(),e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{filterFun2:function(e){this.$refs.treeSelectArea.$refs.tree.filter(e)},getCategoryList:function(){var e=this,t=this.category();this.categoryOptions.data=t,this.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t)}))},resetForm:function(e){this.planTime="",this.$refs[e].resetFields()},fetchData:function(){var e=this;this.tbLoading=!0;var t=this.isRaw?u.FindRawFlowList:u.FindAuxFlowList;t(this.form).then((function(t){t.IsSucceed?e.tbData=t.Data.map((function(e){return e.Transfer_Weight=(0,d.default)(e.Transfer_Weight||0).divide(1e3).format("0.[000]"),e.Tax_Unit_Price=1e3*e.Tax_Unit_Price,e})):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},getWarehouseList:function(){var e=this;(0,s.GetWarehouseListOfCurFactory)({type:this.isRaw?"原材料仓库":"辅料仓库"}).then((function(t){t.IsSucceed&&(e.warehouses=t.Data)}))},wareChange:function(e){var t=this;this.form.LT_Id="",e&&(0,s.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))}}}},5264:function(e,t,a){"use strict";a.r(t);var r=a("8dfe"),o=a("cb32");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("1382b");var i=a("2877"),l=Object(i["a"])(o["default"],r["a"],r["b"],!1,null,"d13566a8",null);t["default"]=l.exports},"52c7":function(e,t,a){"use strict";a.r(t);var r=a("fe1e"),o=a("8af9");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);var i=a("2877"),l=Object(i["a"])(o["default"],r["a"],r["b"],!1,null,"7caf5844",null);t["default"]=l.exports},"5f52":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("dca8"),a("d3b7");var o=r(a("c14f")),n=r(a("1da1")),i=a("6186"),l=a("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,n.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getTypeList();case 1:return a.n=2,t.getTable(e);case 2:return a.a(2)}}),a)})))()},getTypeList:function(){var e=this;return(0,n.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var r,o=null===(r=a[0])||void 0===r?void 0:r.Id;e.Code=a.find((function(e){return e.Id===o})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(a){(0,i.GetGridByCode)({code:e+","+t.Code}).then((function(e){var r=e.IsSucceed,o=e.Data,n=e.Message;if(r){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,o.Grid),t.columns=(o.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),o.Grid.Is_Page&&(t.queryInfo.PageSize=+o.Grid.Row_Number),a(t.columns)}else t.$message({message:n,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var o=this.columns[r];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},8378:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CreateVersion=M,t.DelAuxCategoryEntity=v,t.DelAuxEntity=w,t.DelCategoryEntity=u,t.DelRawEntity=f,t.DeleteVersion=D,t.EditAuxEnabled=R,t.EditRawEnabled=d,t.ExportAuxForProject=z,t.ExportAuxList=L,t.ExportFindRawInAndOut=$,t.ExportInOutStoreReport=X,t.ExportPicking=E,t.ExportRawList=O,t.ExportRecSendProjectMaterialReport=K,t.ExportRecSendProjectReport=Q,t.ExportReceiving=A,t.ExportStagnationInventory=U,t.ExportStoreReport=J,t.FindInAndOutPageList=j,t.FindPickingNewPageList=N,t.FindPickingPageList=G,t.FindReceivingNewPageList=k,t.FindReceivingPageList=F,t.GetAuxCategoryDetail=b,t.GetAuxCategoryTreeList=g,t.GetAuxDetail=I,t.GetAuxFilterDataSummary=B,t.GetAuxForProjectDetail=q,t.GetAuxForProjectPageList=V,t.GetAuxPageList=P,t.GetAuxTemplate=x,t.GetAuxWHSummaryList=H,t.GetCategoryDetail=l,t.GetCategoryTreeList=n,t.GetCycleDate=W,t.GetList=S,t.GetRawDetail=m,t.GetRawPageList=c,t.GetRawTemplate=p,t.ImportAuxList=C,t.ImportRawList=h,t.SaveAuxCategoryEntity=y,t.SaveAuxEntity=_,t.SaveCategoryEntity=i,t.SaveRawEntity=s,t.UpdateVersion=T;var o=r(a("b775"));function n(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:e,timeout:12e5})}function g(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:e,timeout:12e5})}function O(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:e})}function M(e){return(0,o.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:e})}},"8af9":function(e,t,a){"use strict";a.r(t);var r=a("f534"),o=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=o.a},"8dfe":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return o}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"100px"}},[a("el-form-item",{attrs:{label:(e.isRaw?"原料":"辅料")+"名称",prop:"Mat_Name"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Mat_Name,callback:function(t){e.$set(e.form,"Mat_Name",t)},expression:"form.Mat_Name"}})],1),a("el-form-item",{attrs:{label:"操作日期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.planTime,callback:function(t){e.planTime=t},expression:"planTime"}})],1),a("el-form-item",{attrs:{label:(e.isRaw?"原料":"辅料")+"分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0,filterable:!0},"tree-params":e.categoryOptions},on:{searchFun:e.filterFun2},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1),a("el-form-item",{attrs:{label:"仓库",prop:"WH_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择仓库",clearable:""},on:{change:e.wareChange},model:{value:e.form.WH_Id,callback:function(t){e.$set(e.form,"WH_Id",t)},expression:"form.WH_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"LT_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.WH_Id,filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.LT_Id,callback:function(t){e.$set(e.form,"LT_Id",t)},expression:"form.LT_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"操作人",prop:"Operator_Id"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Operator_Id,callback:function(t){e.$set(e.form,"Operator_Id",t)},expression:"form.Operator_Id"}})],1),a("el-form-item",{attrs:{label:"项目",prop:"Sys_Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"库存类型",prop:"Store_Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Store_Type,callback:function(t){e.$set(e.form,"Store_Type",t)},expression:"form.Store_Type"}},[a("el-option",{attrs:{label:"公共",value:1}}),a("el-option",{attrs:{label:"锁定",value:2}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("搜索")]),a("el-button",{on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")])],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","checkbox-config":{checkField:"checked"},height:"500",align:"left",stripe:"","row-config":{isCurrent:!0,isHover:!0},size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return["Mat_Type"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-tag",{attrs:{type:2===r.Store_Type?"primary":"success"}},[e._v(e._s(r.Is_Component?"锁定库存":"公共库存")+" ")])]}}],null,!0)}):"Operate_Time"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Operate_Time))+" ")]}}],null,!0)}):a("vxe-column",{key:t.Id,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,width:t.Width}})]}))],2)],1)],1)},o=[]},9002:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTableConfig=void 0,a("d3b7");var r=a("6186"),o=void 0;t.getTableConfig=function(e){return new Promise((function(t,a){(0,r.GetGridByCode)({code:e}).then((function(e){var a=e.IsSucceed,r=e.Data,n=e.Message;if(a){var i=r.ColumnList||[];t(i)}else o.$message({message:n,type:"error"})}))}))}},cb32:function(e,t,a){"use strict";a.r(t);var r=a("4f7b"),o=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=o.a},f534:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(a("5530")),n=r(a("c14f")),i=r(a("1da1"));a("a9e3");var l=a("209b"),u=a("3c4a");t.default={props:{visible:{type:Boolean,default:!1},id:{type:String,default:""},curType:{type:Number,default:1}},data:function(){return{warehouses:[],locations:[],loading:!1,form:{WH_Id:"",LT_Id:"",Count:void 0,Remark:""},rules:{WH_Id:[{required:!0,message:"请输入",trigger:"blur"}],LT_Id:[{required:!0,message:"请输入",trigger:"blur"}],Count:[{required:!0,message:"请输入",trigger:"blur"}]}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},mounted:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.getWarehouseList();case 1:return t.a(2)}}),t)})))()},methods:{getWarehouseList:function(){var e=this;(0,l.GetWarehouseListOfCurFactory)({type:1===this.curType?"原材料仓库":"辅料仓库"}).then((function(t){t.IsSucceed&&(e.warehouses=t.Data)}))},wareChange:function(e){var t=this;this.form.LT_Id="",e&&(0,l.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.loading=!0;var a=1===e.curType?u.SetRawLT:u.SetAuxLT;a((0,o.default)((0,o.default)({},e.form),{},{Store_Sub_Id:e.id})).then((function(t){t.IsSucceed?(e.dialogVisible=!1,e.$emit("updateInfo"),e.$message({message:"移库成功",type:"success"})):e.$message({message:t.Message,type:"error"}),e.loading=!1}))}))}}}},fe1e:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return o}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"移库","append-to-body":"",visible:e.dialogVisible,width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"仓库",prop:"WH_Id"}},[a("el-select",{ref:"WarehouseRef",attrs:{clearable:"",filterable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.WH_Id,callback:function(t){e.$set(e.form,"WH_Id",t)},expression:"form.WH_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"LT_Id"}},[a("el-select",{ref:"LocationRef",attrs:{clearable:"",filterable:"",placeholder:"请选择库位",disabled:!e.form.WH_Id},model:{value:e.form.LT_Id,callback:function(t){e.$set(e.form,"LT_Id",t)},expression:"form.LT_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"数量",prop:"Count"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0},model:{value:e.form.Count,callback:function(t){e.$set(e.form,"Count",t)},expression:"form.Count"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)]):e._e()},o=[]}}]);