(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3b1ad40a"],{"0187":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteRole=i,t.GetRoleMenusObj=f,t.GetRoleTree=r,t.GetRoleWorkingObjListByUser=l,t.GetUserListByRole=d,t.GetUserRoleTreeWithoutObject=h,t.GetWorkingObjTree=p,t.SaveDepartmentObject=m,t.SaveRole=u,t.SaveRoleMenu=c,t.SaveUserAuthorize=s,t.SaveUserObject=g;var o=a(n("b775"));function r(){return(0,o.default)({url:"/SYS/Role/GetRoleTree",method:"post"})}function u(e){return(0,o.default)({url:"/SYS/Role/SaveRole",method:"post",data:e})}function i(e){return(0,o.default)({url:"/SYS/Role/DeleteRole",method:"post",data:e})}function d(e){return(0,o.default)({url:"/SYS/Role/GetUserListByRole",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/Role/SaveUserAuthorize",method:"post",data:e})}function l(e){return(0,o.default)({url:"/SYS/Role/GetRoleWorkingObjListByUser",method:"post",data:e})}function c(e){return(0,o.default)({url:"/SYS/Role/SaveRoleMenu",method:"post",data:e})}function f(e){return(0,o.default)({url:"/SYS/Role/GetRoleMenusObj",method:"post",data:e})}function h(e){return(0,o.default)({url:"/SYS/User/GetUserRoleTreeWithoutObject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/SYS/User/GetWorkingObjTree",method:"post",data:e})}function g(e){return(0,o.default)({url:"/SYS/User/SaveUserObject",method:"post",data:e})}function m(e){return(0,o.default)({url:"/SYS/User/SaveDepartmentObject",method:"post",data:e})}},"09f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=u,Math.easeInOutQuad=function(e,t,n,a){return e/=a/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function u(e,t,n){var u=r(),i=e-u,d=20,s=0;t="undefined"===typeof t?500:t;var l=function(){s+=d;var e=Math.easeInOutQuad(s,u,i,t);o(e),s<t?a(l):n&&"function"===typeof n&&n()};l()}},"15ac":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");t.default={methods:{getTableConfig:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:e,IsAll:n}).then((function(e){var a=e.IsSucceed,u=e.Data,i=e.Message;if(a){if(!u)return void t.$message({message:"表格配置不存在",type:"error"});var d=[];t.tbConfig=Object.assign({},t.tbConfig,u.Grid),d=n?(null===u||void 0===u?void 0:u.ColumnList)||[]:(null===u||void 0===u?void 0:u.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=d.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+u.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:i,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit,a=e.type;this.queryInfo.Page="limit"===a?1:t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===t){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"15fd":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("a4d3");var a=o(n("ccb5"));function o(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(null==e)return{};var n,o,r=(0,a.default)(e,t);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(e);for(o=0;o<u.length;o++)n=u[o],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}},"1b5d":function(e,t,n){"use strict";n.r(t);var a=n("4651"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"1c94":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("div",{staticClass:"cs-box mb10"},[n("el-row",[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择",filterable:""},on:{change:function(t){return e.projectChange(e.form.Sys_Project_Id)}},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return n("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[n("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.Sys_Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"单据状态",prop:"Status"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},[n("el-option",{attrs:{label:"草稿",value:1}}),n("el-option",{attrs:{label:"审核中",value:2}}),n("el-option",{attrs:{label:"审核未通过",value:-2}}),n("el-option",{attrs:{label:"审核通过",value:3}})],1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.mocType,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"执行情况",prop:"Exec_Status"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Exec_Status,callback:function(t){e.$set(e.form,"Exec_Status",t)},expression:"form.Exec_Status"}},[n("el-option",{attrs:{label:"未开始",value:1}}),n("el-option",{attrs:{label:"执行中",value:2}}),n("el-option",{attrs:{label:"已完成",value:3}})],1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"变更日期",prop:"Change_Date"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.Change_Date,callback:function(t){e.$set(e.form,"Change_Date",t)},expression:"form.Change_Date"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"紧急程度",prop:"Urgency"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Urgency,callback:function(t){e.$set(e.form,"Urgency",t)},expression:"form.Urgency"}},[n("el-option",{attrs:{label:"普通",value:1}}),n("el-option",{attrs:{label:"紧急",value:2}})],1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{"label-width":"20px"}},[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.fetchData(1)}}},[e._v("搜索")]),n("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1)],1)],1),n("div",{staticClass:"cs-box cs-main"},[n("vxe-toolbar",{ref:"xToolbar1",staticClass:"cs-toolBar",scopedSlots:e._u([{key:"buttons",fn:function(){return[n("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新建联系单")])]},proxy:!0},{key:"tools",fn:function(){return[n("el-button",{attrs:{type:"primary"},on:{click:e.handleSetting}},[e._v("变更类型配置")]),n("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.changeColumn}})]},proxy:!0}])}),n("div",{staticClass:"cs-bottom-wapper"},[n("div",{staticClass:"fff tb-x"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0},"checkbox-config":{checkField:"checked"}},on:{"checkbox-all":e.multiSelectedChange,"checkbox-change":e.multiSelectedChange}},[e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,"show-overflow":"tooltip",sortable:"",align:"center",field:t.Code,title:t.Display_Name,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":""},scopedSlots:e._u([["Change_Date","Demand_Date","Create_Date","Change_End"].includes(t.Code)?{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e._f("timeFormat")(a[t.Code]))+" ")]}}:"Exec_Status"===t.Code?{key:"default",fn:function(a){var o=a.row;return[n("span",{class:["cs-tags",1===o[t.Code]?"cs-red":2===o[t.Code]?"cs-blue":"cs-green"]},[e._v(e._s(1===o[t.Code]?"未开始":2===o[t.Code]?"执行中":3===o[t.Code]?"已完成":""))])]}}:"Urgency"===t.Code?{key:"default",fn:function(t){var a=t.row;return[1==a.Urgency?n("el-tag",{attrs:{type:"primary"}},[e._v("普通")]):2==a.Urgency?n("el-tag",{attrs:{type:"danger"}},[e._v("紧急")]):n("span",[e._v("-")])]}}:{key:"default",fn:function(a){var o=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})]})),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._l(e.getButtonsByStatus(a.Status,a),(function(t){return[t.checkKey(t.key,a)?n("el-button",{key:t.text,class:{"txt-red":t.isRed},attrs:{type:"text"},on:{click:function(e){return t.handler(a)}}},[e._v(e._s(t.text))]):e._e()]}))]}}])})],2)],1),n("div",{staticClass:"data-info"},[n("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)])],1),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog cs-dialog",attrs:{title:"变更类型",visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t},close:function(t){e.dialogVisible=!1}}},[n("div",[n("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[n("vxe-button",{attrs:{status:"primary",content:"添加"},on:{click:function(t){return e.handleAddSetting()}}})]},proxy:!0}])}),n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",border:"",stripe:"",resizable:"","show-overflow":"",loading:e.settingLoading,data:e.dialogTable,"edit-config":{beforeEditMethod:e.activeRowMethod,trigger:"click",showStatus:!0,mode:"row"}},on:{"edit-closed":e.editClosedEvent,"edit-disabled":e.editDisabledEvent}},[n("vxe-column",{attrs:{align:"left",field:"Display_Name",title:"类型名称","min-width":"180","edit-render":{autofocus:".vxe-input--inner"}},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.Display_Name,callback:function(t){e.$set(a,"Display_Name",t)},expression:"row.Display_Name"}})]}}])}),n("vxe-column",{attrs:{align:"left",field:"Is_Deepen_Change",title:"是否变更清单","min-width":"180","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[n("el-radio",{attrs:{label:!0},model:{value:a.Is_Deepen_Change,callback:function(t){e.$set(a,"Is_Deepen_Change",t)},expression:"row.Is_Deepen_Change"}},[e._v("是")]),n("el-radio",{attrs:{label:!1},model:{value:a.Is_Deepen_Change,callback:function(t){e.$set(a,"Is_Deepen_Change",t)},expression:"row.Is_Deepen_Change"}},[e._v("否")])]}},{key:"default",fn:function(t){var a=t.row;return[a.Is_Deepen_Change?n("el-tag",{attrs:{type:"success"}},[e._v("是")]):n("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}])}),n("vxe-column",{attrs:{align:"left",title:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{staticClass:"txt-red",attrs:{type:"text"},on:{click:function(t){return e.removeRowEvent(a)}}},[e._v("删除")])]}}])})],1)],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("关 闭")])],1)]),n("Monitor",{ref:"monitor"})],1)},o=[]},3166:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=h,t.DeleteProject=l,t.GeAreaTrees=b,t.GetFileSync=_,t.GetInstallUnitIdNameList=v,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=R,t.GetProjectAreaTreeList=y,t.GetProjectEntity=d,t.GetProjectList=i,t.GetProjectPageList=u,t.GetProjectTemplate=g,t.GetPushProjectPageList=O,t.GetSchedulingPartList=G,t.IsEnableProjectMonomer=c,t.SaveProject=s,t.UpdateProjectTemplateBase=m,t.UpdateProjectTemplateContacts=P,t.UpdateProjectTemplateContract=C,t.UpdateProjectTemplateOther=S;var o=a(n("b775")),r=a(n("4328"));function u(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function s(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function _(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},4651:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("2909")),r=a(n("c14f")),u=a(n("1da1")),i=a(n("5530")),d=a(n("15fd"));n("99af"),n("14d9"),n("b0c0"),n("e9f5"),n("7d54"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0");var s=n("c685"),l=a(n("15ac")),c=a(n("333d")),f=n("7015f"),h=a(n("2082")),p=n("3166"),g=a(n("7962")),m=n("9643"),P=a(n("a657")),C=["Change_Date"];t.default={name:"PROEngineeringChangeOrder",components:{DynamicTableFields:P.default,Monitor:g.default,Pagination:c.default},mixins:[l.default,h.default],data:function(){return{addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([n.e("chunk-2d0c91c4"),n.e("chunk-0079a193")]).then(n.bind(null,"9a6ff"))},name:"PROEngineeringChangeOrderAdd",meta:{title:"新增"}},{path:this.$route.path+"/edit",hidden:!0,component:function(){return Promise.all([n.e("chunk-2d0c91c4"),n.e("chunk-0079a193")]).then(n.bind(null,"9a6ff"))},name:"PROEngineeringChangeOrderEdit",meta:{title:"编辑"}},{path:this.$route.path+"/view",hidden:!0,component:function(){return Promise.all([n.e("chunk-2d0c91c4"),n.e("chunk-0079a193")]).then(n.bind(null,"9a6ff"))},name:"PROEngineeringChangeOrderView",meta:{title:"查看"}}],form:{Sys_Project_Id:"",Status:"",Exec_Status:"",Change_Date:"",Moc_Type_Id:"",Area_Id:"",Urgency:""},activeName:"second",dialogVisible:!1,tbLoading:!1,settingLoading:!1,tbData:[],projectList:[],dialogTable:[],mocType:[],installUnitList:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},tbConfig:{},multipleSelection:[],columns:[],gridCode:"PROEngChangeOrder",tablePageSize:s.tablePageSize,queryInfo:{Page:1,PageSize:s.tablePageSize[0]},total:0,buttonConfigs:{draft:[{text:"提交审核",handler:this.handleSubmitAudit,checkKey:this.checkKey},{text:"编辑",handler:this.handleEdit,checkKey:this.checkKey},{text:"删除",isRed:!0,handler:this.handleDelete,checkKey:this.checkKey}],reviewing:[{text:"查看",handler:this.handleView,checkKey:this.checkKey},{text:"监控",key:"monitor",handler:this.handleMonitor,checkKey:this.checkKey},{text:"回收",handler:this.handleRecycle,checkKey:this.checkKey}],approved:[{text:"查看",handler:this.handleView,checkKey:this.checkKey},{text:"监控",key:"monitor",handler:this.handleMonitor,checkKey:this.checkKey}],finish:[{text:"完成",handler:this.handleComplete,checkKey:this.checkKey}]}}},mounted:function(){this.getTableConfig(this.gridCode),this.fetchData(1),this.getBasicData(),this.getSettingInfo()},methods:{fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e);var n=this.form,a=n.Change_Date,o=(0,d.default)(n,C),r=a[0],u=a[1];(0,f.GetMocOrderPageList)((0,i.default)((0,i.default)({},o),{},{Change_Begin:r,Change_End:u},this.queryInfo)).then((function(e){var n;e.IsSucceed?(t.tbData=(null===e||void 0===e||null===(n=e.Data)||void 0===n?void 0:n.Data)||[],t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})}))},handleSetting:function(){this.dialogVisible=!0,this.getSettingInfo()},handleAddSetting:function(e){var t=this;return(0,u.default)((0,r.default)().m((function n(){var a,o,u,i;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return a=t.$refs.xTable,o={Display_Name:"",Is_Deepen_Change:!1},n.n=1,a.insertAt(o,e);case 1:return u=n.v,i=u.row,n.n=2,a.setEditCell(i,"name");case 2:return n.a(2)}}),n)})))()},removeRowEvent:function(e){var t=this;e.Id?this.$confirm(" 是否删除该类型?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DeleteMocType)({ids:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.getSettingInfo()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})})):this.$refs.xTable.remove(e)},getSettingInfo:function(){var e=this;(0,f.GetMocOrderTypeList)({}).then((function(t){t.IsSucceed?(e.dialogTable=t.Data,e.mocType=t.Data):e.$message({message:t.Message,type:"error"})}))},changeColumn:function(){var e=this;return(0,u.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:return t.a(2)}}),t)})))()},editClosedEvent:function(e){var t=this,n=e.row,a=e.column;if(n.Display_Name){var o=this.$refs.xTable,r=a.field,u=!1;if((o.isUpdateByRow(n,r)&&n.Id||!n.Id)&&(u=!0),u){var i={Display_Name:n.Display_Name,Is_Deepen_Change:n.Is_Deepen_Change};n.Id&&(i.Id=n.Id),(0,f.SaveMocOrderType)(i).then((function(e){e.IsSucceed?(t.$message({message:"保存成功",type:"success"}),o.reloadRow(n,null,r),t.getSettingInfo()):t.$message({message:e.Message,type:"error"})}))}}else this.$message({message:"名称不能为空",type:"warning"})},getBasicData:function(){var e=this;(0,p.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)}))},getAreaList:function(e){var t=this;(0,p.GeAreaTrees)({sysProjectId:e}).then((function(e){if(e.IsSucceed){var n=e.Data;t.setDisabledTree(n),t.treeParamsArea.data=e.Data,t.$nextTick((function(n){t.$refs.treeSelectArea.treeDataUpdateFun(e.Data)}))}else t.$message({message:e.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var n=e.Children;n&&n.length?e.disabled=!0:(e.disabled=!1,t.setDisabledTree(n))}))},projectChange:function(e){var t=this,n=e;this.form.Area_Id="",this.treeParamsArea.data=[],this.$nextTick((function(e){t.$refs.treeSelectArea.treeDataUpdateFun([])})),e&&this.getAreaList(n)},areaChange:function(){},areaClear:function(){this.form.Area_Id=""},handleAdd:function(e,t){this.$router.push({name:"PROEngineeringChangeOrderAdd",query:{pg_redirect:this.$route.name}})},handleSearch:function(e,t){},multiSelectedChange:function(e){this.multipleSelection=e.records},handleReset:function(){this.$refs["form"].resetFields(),this.fetchData(1)},getButtonsByStatus:function(e,t){switch(e){case-1:case 1:case-2:return this.buttonConfigs.draft;case 2:return this.buttonConfigs.reviewing;case 3:return 2===t.Exec_Status?[].concat((0,o.default)(this.buttonConfigs.approved),(0,o.default)(this.buttonConfigs.finish)):this.buttonConfigs.approved;default:return[]}},handleSubmitAudit:function(e){var t=this;this.$confirm("是否提交审核?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.tbLoading=!0,(0,f.SubmitMocOrder)({Id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"提交成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleEdit:function(e){this.$router.push({name:"PROEngineeringChangeOrderEdit",query:{pg_redirect:this.$route.name,type:1,id:e.Id}})},handleDelete:function(e){var t=this;this.$confirm("是否删除该数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.tbLoading=!0,(0,f.DeleteMocOrder)({Id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleView:function(e){this.$router.push({name:"PROEngineeringChangeOrderView",query:{pg_redirect:this.$route.name,id:e.Id,type:2}})},handleMonitor:function(e){this.$refs["monitor"].opendialog(e.Instance_Id,!1)},handleRecycle:function(e){var t=this;this.$confirm("是否回收?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.tbLoading=!0,(0,m.CancelFlow)({instanceId:e.Instance_Id}).then((function(e){e.IsSucceed?(t.$message({message:"回收成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},editDisabledEvent:function(e){e.row,e.column},activeRowMethod:function(e){var t=e.row;e.rowIndex;return!t.Id},handleComplete:function(e){var t=this;this.$confirm("是否完成?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.tbLoading=!0,(0,f.ChangeMocOrderStatus)({Id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"操作成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},checkKey:function(e,t){return!e||("monitor"!==e||!!t["Instance_Id"])}}}},"7015f":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=v,t.AgainSubmitChangeOrder=O,t.BatchReuseEngineeringContactChangedComponentPart=ue,t.BatchReuseEngineeringContactMocComponentPart=ie,t.CancelChangeOrder=S,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=U,t.DeleteChangeOrder=c,t.DeleteChangeOrderV2=E,t.DeleteChangeReason=g,t.DeleteChangeType=i,t.DeleteEngineeringContactChangeOrder=K,t.DeleteMocOrder=H,t.DeleteMocType=Ce,t.ExportEngineeringContactChangedAddComponentPart=le,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=oe,t.ExportMocAddComponentPart=ce,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=R,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=s,t.GetChangeOrderTaskInfo=T,t.GetChangeOrderTaskPageList=I,t.GetChangeOrderV2=$,t.GetChangeOrderV2PageList=w,t.GetChangeReason=h,t.GetChangeType=r,t.GetChangedComponentPartPageList=F,t.GetChangedComponentPartProductionList=Y,t.GetCompAndPartSchdulingPageList=k,t.GetCompAndPartTaskList=D,t.GetCompanyUserPageList=j,t.GetEngineeringContactChangeOrder=z,t.GetEngineeringContactChangeOrderPageList=N,t.GetEngineeringContactChangedAddComponentPartPageList=de,t.GetEngineeringContactChangedAddComponentPartSummary=se,t.GetEngineeringContactChangedComponentPartPageList=J,t.GetEngineeringContactChangedSummary=X,t.GetEngineeringContactFileInfo=W,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=Q,t.GetEngineeringContactMocSummary=ae,t.GetFactoryChangeTypeListV2=V,t.GetFactoryPeoplelist=d,t.GetMocAddComponentPartPageList=he,t.GetMocModelList=Oe,t.GetMocOrderInfo=me,t.GetMocOrderPageList=pe,t.GetMocOrderTypeList=Pe,t.GetMyChangeOrderPageList=C,t.GetProjectAreaChangeTreeList=B,t.GetProjectChangeOrderList=_,t.GetTypeReason=m,t.ImportChangFile=ge,t.ImportChangeDeependFile=P,t.QueryHistories=b,t.ReuseEngineeringContactChangedComponentPart=ne,t.ReuseEngineeringContactMocComponentPart=re,t.SaveChangeOrder=l,t.SaveChangeOrderTask=L,t.SaveChangeOrderV2=x,t.SaveChangeReason=p,t.SaveChangeType=u,t.SaveEngineeringContactChangeOrder=q,t.SaveMocOrder=ye,t.SaveMocOrderType=Se,t.SubmitChangeOrder=y,t.SubmitChangeOrderV2=M,t.SubmitMocOrder=A,t.Verification=G;var o=a(n("b775"));a(n("4328"));function r(e){return(0,o.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function b(e){return(0,o.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function R(e){return(0,o.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function j(e){return(0,o.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,o.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,o.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,o.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ne(e){return(0,o.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ae(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function oe(e){return(0,o.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ue(e){return(0,o.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function de(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function le(e){return(0,o.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function ce(e){return(0,o.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function he(e){return(0,o.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function pe(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function ge(e){return(0,o.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function me(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Pe(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Ce(e){return(0,o.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function Se(e){return(0,o.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function Oe(e){return(0,o.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function ye(e){return(0,o.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},"7f6c":function(e,t,n){},9643:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProjectSendingInfo=v,t.CancelFlow=W,t.DeleteProjectSendingInfo=d,t.EditProjectSendingInfo=c,t.ExportComponentStockOutInfo=_,t.ExportInvoiceList=N,t.ExportSendSteel=f,t.ExportSendingDetailInfoList=h,t.GetLocationList=w,t.GetProduceCompentEntity=S,t.GetProducedPartToSendPageList=E,t.GetProjectAcceptInfoPagelist=A,t.GetProjectSendingAllCount=m,t.GetProjectSendingInfoAndItemPagelist=k,t.GetProjectSendingInfoLogPagelist=x,t.GetProjectSendingInfoPagelist=i,t.GetProjectsendinginEntity=l,t.GetReadyForDeliverSummary=I,t.GetReadyForDeliveryComponentPageList=G,t.GetReadyForDeliveryPageList=R,t.GetReturnHistoryPageList=U,t.GetSendToReturnPageList=L,t.GetStockOutBillInfoPageList=T,t.GetStockOutDetailList=P,t.GetStockOutDetailPageList=D,t.GetStockOutDocEntity=b,t.GetStockOutDocPageList=u,t.GetWaitingStockOutPageList=C,t.GetWarehouseListOfCurFactory=j,t.GetWeighingReviewList=$,t.SaveStockOut=y,t.SubmitApproval=V,t.SubmitProjectSending=s,t.SubmitReturnToStockIn=O,t.SubmitWeighingForPC=F,t.Transforms=p,t.TransformsByType=M,t.TransformsWithoutWeight=g,t.WeighingReviewSubmit=B,t.WithdrawDraft=Y;var o=a(n("b775")),r=a(n("4328"));function u(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDocPageList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoPagelist",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/ComponentStockOut/DeleteProjectSendingInfo",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitProjectSending",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectsendinginEntity",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ComponentStockOut/EditProjectSendingInfo",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportSendSteel",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportSendingDetailInfoList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ComponentStockOut/Transforms",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/ComponentStockOut/TransformsWithoutWeight",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingAllCount",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailList",method:"post",data:r.default.stringify(e)})}function C(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetWaitingStockOutPageList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProduceCompentEntity",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/ComponentReturn/SubmitReturnToStockIn",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SaveStockOut",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/ComponentStockOut/AddProjectSendingInfo",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDocEntity",method:"post",data:r.default.stringify(e)})}function R(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryPageList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryComponentPageList",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportComponentStockOutInfo",method:"post",data:r.default.stringify(e)})}function I(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliverSummary",method:"post",data:r.default.stringify(e)})}function k(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoAndItemPagelist",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/ComponentReturn/GetSendToReturnPageList",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailPageList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutBillInfoPageList",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoLogPagelist",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProducedPartToSendPageList",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/ComponentStockOut/TransformsByType",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectAcceptInfoPagelist",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/ComponentReturn/GetReturnHistoryPageList",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetWeighingReviewList",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/ComponentStockOut/WeighingReviewSubmit",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitWeighingForPC",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PRO/ComponentStockOut/WithdrawDraft",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportInvoiceList",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitApproval",method:"post",data:e})}function W(e){return(0,o.default)({url:"/Sys/FlowInstances/CancelFlow",method:"post",data:e})}},af5a:function(e,t,n){"use strict";n("7f6c")},ccb5:function(e,t,n){"use strict";function a(e,t){if(null==e)return{};var n={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;n[a]=e[a]}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=a},d461:function(e,t,n){"use strict";n.r(t);var a=n("1c94"),o=n("1b5d");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("af5a");var u=n("2877"),i=Object(u["a"])(o["default"],a["a"],a["b"],!1,null,"07b7942e",null);t["default"]=i.exports},e41b:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=d,t.GetPartsImportTemplate=l,t.GetPartsList=i,t.GetProjectAreaTreeList=r,t.ImportParts=s,t.SaveProjectAreaSort=u;var o=a(n("b775"));function r(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},ea13:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteGroup=s,t.DeleteGroupRole=g,t.DeleteGroupUser=c,t.DeleteUserRole=v,t.GetGroupEntity=i,t.GetGroupList=r,t.GetGroupRole=h,t.GetGroupTree=u,t.GetGroupUser=f,t.GetGroupUserByRole=S,t.GetRoleListCanAdd=p,t.GetShuttleUserList=O,t.GetWorkingObjTreeListByGroupId=C,t.SaveGroup=d,t.SaveGroupObject=P,t.SaveGroupRole=m,t.SaveGroupUser=l,t.SaveUserRoles=y;var o=a(n("b775"));function r(){return(0,o.default)({url:"/SYS/UserGroup/GetGroupList",method:"post"})}function u(e){return(0,o.default)({url:"/SYS/UserGroup/GetGroupTree",method:"post",data:e})}function i(e){return(0,o.default)({url:"/SYS/UserGroup/GetGroupEntity",method:"post",data:e})}function d(e){return(0,o.default)({url:"/SYS/UserGroup/SaveGroup",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/UserGroup/DeleteGroup",method:"post",data:e})}function l(e){return(0,o.default)({url:"/SYS/UserGroup/SaveGroupUser",method:"post",data:e})}function c(e){return(0,o.default)({url:"/SYS/UserGroup/DeleteGroupUser",method:"post",data:e})}function f(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetGroupUser",data:e})}function h(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetGroupRole",data:e})}function p(e){return(0,o.default)({method:"post",url:"sys/UserGroup/GetRoleListCanAdd",data:e})}function g(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/DeleteGroupRole",data:e})}function m(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/SaveGroupRole",data:e})}function P(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/SaveGroupObject",data:e})}function C(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetWorkingObjTreeListByGroupId",data:e})}function S(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetGroupUserByRole",data:e})}function O(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetShuttleUserList",data:e})}function y(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/SaveUserRoles",data:e})}function v(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/DeleteUserRole",data:e})}},f382:function(e,t,n){"use strict";function a(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=a(e.Children)),!0)}))}function o(e){e.map((function(e){if(e.Is_Directory||!e.Children)return o(e.Children);delete e.Children}))}function r(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function a(e,t,o){for(var r=0;r<e.length;r++){var u=e[r];if(u.Id===t)return n&&o.push(u),o;if(u.Children&&u.Children.length){if(o.push(u),a(u.Children,t,o).length)return o;o.pop()}}return[]}return a(e,t,[])}function u(e){return e.Children&&e.Children.length>0?u(e.Children[0]):e}function i(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&i(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=o,t.disableDirectory=i,t.findAllParentNode=r,t.findFirstNode=u,t.getDirectoryTree=a,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7")}}]);