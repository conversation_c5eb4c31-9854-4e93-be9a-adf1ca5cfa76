(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6536a7fb"],{"0ce7":function(t,e,n){"use strict";var o=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("c14f")),r=o(n("1da1"));n("b0c0"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7");var i=o(n("9b15")),u=n("5c96"),s=n("21c4"),l=n("6186"),c=function(){(0,l.SecurityToken)().then((function(t){sessionStorage.setItem("ossToken",JSON.stringify(t.Data))}))};c(),setInterval((function(){c()}),114e4);e.default={name:"OSSUpload",mixins:[u.Upload],props:{ossOnSuccess:{type:Function,default:Function},piecesize:{type:Number,default:2},httpRequest:{type:Function,default:function(t){var e=this,n=JSON.parse(sessionStorage.getItem("ossToken"));this.$nextTick((function(){var o,u=null!==(o=e.data)&&void 0!==o&&o.piecesize?1*e.data.piecesize:2,c=new i.default({region:"oss-"+n.regionId,secure:!0,accessKeyId:n.AccessKeyId,accessKeySecret:n.AccessKeySecret,stsToken:n.SecurityToken,bucket:n.bucket,refreshSTSToken:function(){var t=(0,r.default)((0,a.default)().m((function t(){var n;return(0,a.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.securityToken();case 1:return n=t.v,t.a(2,{accessKeyId:n.AccessKeyId,accessKeySecret:n.AccessKeySecret,stsToken:n.SecurityToken})}}),t)})));function n(){return t.apply(this,arguments)}return n}(),refreshSTSTokenInterval:9e5}),d=t.file,f=new Date;c.multipartUpload((0,s.getTenantId)()+"/"+f.getFullYear()+"/"+(1*f.getMonth()+1)+"/"+f.getDate()+"/"+f.getMinutes()+"_"+f.getSeconds()+"_"+f.getMilliseconds()+"/"+d.name,d,{progress:function(e,n){this.process=n,t.onProgress({percent:Math.floor(100*e)})},parallel:4,partSize:1048576*u,meta:{}}).then((function(e){if(200===e.res.statusCode){var n,o=e.res.requestUrls[0]&&e.res.requestUrls[0].split("?")[0];!1===(null===t||void 0===t||null===(n=t.data)||void 0===n?void 0:n.callback)?t.onSuccess({Data:o+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name}):(0,l.GetOssUrl)({url:o}).then((function(e){t.onSuccess({Data:o+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name,encryptionUrl:e.Data})}))}}),(function(e){t.onError(e)}))}))}}},data:function(){return{process:null}},watch:{process:function(t){this.$emit("getprocess",t)}},mounted:function(){},methods:{handleFn:function(t){},securityToken:function(){return new Promise((function(t,e){(0,l.SecurityToken)({}).then((function(e){t(e.Data)})).catch((function(t){e(t)}))}))}}}},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var o=n("6186"),a=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,o.GetGridByCode)({code:t,IsAll:n}).then((function(t){var o=t.IsSucceed,i=t.Data,u=t.Message;if(o){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),s=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||a.tablePageSize[0]),r(e.columns)}else e.$message({message:u,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,o=t.type;this.queryInfo.Page="limit"===o?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var o=0;o<this.columns.length;o++){var a=this.columns[o];if(a.Code===e){n.Type=a.Type,n.Filter_Type=a.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1db8d":function(t,e,n){"use strict";n("f509")},4088:function(t,e,n){"use strict";var o=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("caad"),n("14d9"),n("e9f5"),n("910d"),n("7d54"),n("e9c4"),n("b64b"),n("d3b7"),n("25f0"),n("2532"),n("159b");var a=o(n("5530")),r=o(n("c14f")),i=o(n("1da1")),u=o(n("15ac")),s=n("9643"),l=n("ed08"),c=o(n("6612")),d=o(n("c7ab")),f=n("6186");e.default={name:"PROWeighingVerification",components:{OSSUpload:d.default},mixins:[u.default],data:function(){return{fileListData:[],carNumber:"",dialogLoading:!1,dialogVisible:!1,tbLoading:!1,columns:[],tbData:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1,PageSize:-1},total:0,form:{Pound_Remark:"",Tare_Weight:void 0,Pound_Weight:void 0,Reason_Weight:void 0},rules:{Tare_Weight:[{required:!0,message:"请输入",trigger:"blur"}],Pound_Weight:[{required:!0,message:"请输入",trigger:"blur"}]}}},computed:{netWeight:function(){return this.form.Pound_Weight&&this.form.Tare_Weight?this.getNum(this.form.Pound_Weight,this.form.Tare_Weight):0},showRed:function(t){var e,n,o=t.netWeight;return!!this.rowData&&(Math.abs(o-(null===(e=this.rowData)||void 0===e?void 0:e.Reason_Weight)||0)>=(null===(n=this.rowData)||void 0===n?void 0:n.Weigh_Warning_Threshold)||0)}},mounted:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.originTb=[],e.n=1,t.getTableConfig("PROWeighingReviewList");case 1:t.fetchData();case 2:return e.a(2)}}),e)})))()},methods:{getNum:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(0,c.default)(t||0).subtract(e||0).format("0.[000]")},handleSearch:function(){var t=this;this.carNumber?this.tbData=this.originTb.filter((function(e){return e.Car_License.includes(t.carNumber)})):this.fetchData()},submitDialog:function(){var t=this;this.$refs["form"].validate((function(e){if(e){t.dialogLoading=!0;var n=[];Array.isArray(t.fileListArr)&&t.fileListArr.length>0&&t.fileListArr.forEach((function(t){n.push(t.response&&t.response.encryptionUrl?t.response.encryptionUrl:t.encryptionUrl)})),(0,s.WeighingReviewSubmit)((0,a.default)((0,a.default)({},t.form),{},{Attachment_Weight:n.toString()})).then((function(e){e.IsSucceed?(t.fetchData(),t.dialogVisible=!1):t.$message({message:e.Message,type:"error"}),t.dialogLoading=!1}))}}))},handleClose:function(){},fetchData:function(){var t=this;(0,s.GetWeighingReviewList)({Car_License:this.carNumber}).then((function(e){e.IsSucceed?(t.tbData=e.Data,t.originTb=(0,l.deepClone)(t.tbData)):t.$message({message:e.Message,type:"error"})}))},handleOpen:function(t){this.rowData=t,this.form=Object.assign(this.form,t),this.dialogVisible=!0},uploadSuccess:function(t,e,n){this.fileListArr=JSON.parse(JSON.stringify(n))},uploadRemove:function(t,e){this.fileListArr=JSON.parse(JSON.stringify(e))},handlePreview:function(t){return(0,i.default)((0,r.default)().m((function e(){var n;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:if(n="",!t.response||!t.response.encryptionUrl){e.n=1;break}n=t.response.encryptionUrl,e.n=3;break;case 1:return e.n=2,(0,f.GetOssUrl)({url:t.encryptionUrl});case 2:n=e.v,n=n.Data;case 3:window.open(n);case 4:return e.a(2)}}),e)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过10个"})}}}},"556c":function(t,e,n){"use strict";n.r(e);var o=n("4088"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);e["default"]=a.a},"7a5e":function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return a}));var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container abs100"},[n("div",{staticClass:"cs-wrapper"},[n("div",{staticClass:"search-x"},[n("span",{staticClass:"cs-label"},[t._v("车牌号：")]),n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:t.carNumber,callback:function(e){t.carNumber=e},expression:"carNumber"}}),n("el-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")])],1),n("div",{staticClass:"tb-x",staticStyle:{"margin-top":"16px"}},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:t.tbLoading,stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[n("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,"min-width":e.Width,align:e.Align,sortable:"",align:"left","show-overflow":"tooltip"},scopedSlots:t._u(["EntryDate"===e.Code?{key:"default",fn:function(n){var o=n.row;return[t._v(" "+t._s(t._f("timeFormat")(o[e.Code],"{y}-{m}-{d}"))+" ")]}}:null],null,!0)})]})),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleOpen(o)}}},[t._v("过磅录入")])]}}])})],2)],1)]),t.dialogVisible?n("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"plm-custom-dialog",attrs:{title:"过磅录入",visible:t.dialogVisible,width:"50%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n("div",[n("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"皮重",prop:"Tare_Weight"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0,clearble:""},model:{value:t.form.Tare_Weight,callback:function(e){t.$set(t.form,"Tare_Weight",t._n(e))},expression:"form.Tare_Weight"}}),n("span",{staticClass:"cs-unit"},[t._v("kg")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"磅重",prop:"Pound_Weight"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0,clearble:""},model:{value:t.form.Pound_Weight,callback:function(e){t.$set(t.form,"Pound_Weight",t._n(e))},expression:"form.Pound_Weight"}}),n("span",{staticClass:"cs-unit"},[t._v("kg")])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"理重",prop:"Reason_Weight"}},[t._v(" "+t._s(t.form.Reason_Weight||0)+" kg ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"净重",prop:"region"}},[n("span",{class:{"cs-red":t.showRed}},[t._v(t._s(t.netWeight)+" kg "),t.showRed?n("span",[t._v("（"+t._s(t.getNum(t.netWeight,t.form.Reason_Weight)>0?"高于":"低于")+"理重"+t._s(Math.abs(+t.getNum(t.netWeight,t.form.Reason_Weight)))+"kg）")]):t._e()])])],1),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"过磅备注",prop:"Pound_Remark"}},[n("el-input",{staticClass:"w100",attrs:{type:"textarea",maxlength:"50","show-word-limit":"",autosize:{minRows:3,maxRows:4},placeholder:"请输入"},model:{value:t.form.Pound_Remark,callback:function(e){t.$set(t.form,"Pound_Remark",e)},expression:"form.Pound_Remark"}})],1)],1),n("el-col",[n("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[n("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:10,multiple:!0,"on-success":function(e,n,o){t.uploadSuccess(e,n,o)},"on-remove":t.uploadRemove,"on-preview":t.handlePreview,"on-exceed":t.handleExceed,"file-list":t.fileListData,"show-file-list":!0,disabled:!1}},[n("el-button",{attrs:{type:"primary",disabled:!1}},[t._v("上传文件")])],1)],1)],1)],1)],1)],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.dialogLoading},on:{click:t.submitDialog}},[t._v("确 定")])],1)]):t._e()],1)},a=[]},"8bb5":function(t,e,n){"use strict";n.r(e);var o=n("7a5e"),a=n("556c");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("1db8d");var i=n("2877"),u=Object(i["a"])(a["default"],o["a"],o["b"],!1,null,"5856583e",null);e["default"]=u.exports},9643:function(t,e,n){"use strict";var o=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProjectSendingInfo=y,e.CancelFlow=J,e.DeleteProjectSendingInfo=s,e.EditProjectSendingInfo=d,e.ExportComponentStockOutInfo=_,e.ExportInvoiceList=M,e.ExportSendSteel=f,e.ExportSendingDetailInfoList=m,e.GetLocationList=G,e.GetProduceCompentEntity=v,e.GetProducedPartToSendPageList=j,e.GetProjectAcceptInfoPagelist=N,e.GetProjectSendingAllCount=h,e.GetProjectSendingInfoAndItemPagelist=I,e.GetProjectSendingInfoLogPagelist=x,e.GetProjectSendingInfoPagelist=u,e.GetProjectsendinginEntity=c,e.GetReadyForDeliverSummary=w,e.GetReadyForDeliveryComponentPageList=R,e.GetReadyForDeliveryPageList=C,e.GetReturnHistoryPageList=E,e.GetSendToReturnPageList=D,e.GetStockOutBillInfoPageList=T,e.GetStockOutDetailList=S,e.GetStockOutDetailPageList=L,e.GetStockOutDocEntity=k,e.GetStockOutDocPageList=i,e.GetWaitingStockOutPageList=b,e.GetWarehouseListOfCurFactory=W,e.GetWeighingReviewList=F,e.SaveStockOut=O,e.SubmitApproval=$,e.SubmitProjectSending=l,e.SubmitReturnToStockIn=P,e.SubmitWeighingForPC=U,e.Transforms=p,e.TransformsByType=A,e.TransformsWithoutWeight=g,e.WeighingReviewSubmit=z,e.WithdrawDraft=q;var a=o(n("b775")),r=o(n("4328"));function i(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDocPageList",method:"post",data:t})}function u(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoPagelist",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/ComponentStockOut/DeleteProjectSendingInfo",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitProjectSending",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectsendinginEntity",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/ComponentStockOut/EditProjectSendingInfo",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportSendSteel",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportSendingDetailInfoList",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/ComponentStockOut/Transforms",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/ComponentStockOut/TransformsWithoutWeight",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingAllCount",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailList",method:"post",data:r.default.stringify(t)})}function b(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetWaitingStockOutPageList",method:"post",data:t})}function v(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProduceCompentEntity",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/ComponentReturn/SubmitReturnToStockIn",method:"post",data:t})}function O(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SaveStockOut",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/ComponentStockOut/AddProjectSendingInfo",method:"post",data:t})}function k(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDocEntity",method:"post",data:r.default.stringify(t)})}function C(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryPageList",method:"post",data:t})}function R(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryComponentPageList",method:"post",data:t})}function _(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportComponentStockOutInfo",method:"post",data:r.default.stringify(t)})}function w(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliverSummary",method:"post",data:r.default.stringify(t)})}function I(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoAndItemPagelist",method:"post",data:t})}function D(t){return(0,a.default)({url:"/PRO/ComponentReturn/GetSendToReturnPageList",method:"post",data:t})}function L(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailPageList",method:"post",data:t})}function T(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutBillInfoPageList",method:"post",data:t})}function W(t){return(0,a.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function G(t){return(0,a.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function x(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoLogPagelist",method:"post",data:t})}function j(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProducedPartToSendPageList",method:"post",data:t})}function A(t){return(0,a.default)({url:"/PRO/ComponentStockOut/TransformsByType",method:"post",data:t})}function N(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectAcceptInfoPagelist",method:"post",data:t})}function E(t){return(0,a.default)({url:"/PRO/ComponentReturn/GetReturnHistoryPageList",method:"post",data:t})}function F(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetWeighingReviewList",method:"post",data:t})}function z(t){return(0,a.default)({url:"/PRO/ComponentStockOut/WeighingReviewSubmit",method:"post",data:t})}function U(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitWeighingForPC",method:"post",data:t})}function q(t){return(0,a.default)({url:"/PRO/ComponentStockOut/WithdrawDraft",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportInvoiceList",method:"post",data:t})}function $(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitApproval",method:"post",data:t})}function J(t){return(0,a.default)({url:"/Sys/FlowInstances/CancelFlow",method:"post",data:t})}},c7ab:function(t,e,n){"use strict";n.r(e);var o=n("f68a");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);var r,i,u=n("2877"),s=Object(u["a"])(o["default"],r,i,!1,null,null,null);e["default"]=s.exports},f509:function(t,e,n){},f68a:function(t,e,n){"use strict";n.r(e);var o=n("0ce7"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);e["default"]=a.a}}]);