(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4f226f7a"],{"0a26":function(t,e,n){"use strict";n("359c")},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),i=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,r=t.Data,l=t.Message;if(a){if(!r)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,r.Grid),s=n?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+r.Grid.Row_Number||i.tablePageSize[0]),o(e.columns)}else e.$message({message:l,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var i=this.columns[a];if(i.Code===e){n.Type=i.Type,n.Filter_Type=i.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1ae7":function(t,e,n){"use strict";n("929e")},"1d89":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{staticClass:"drag-dialog",attrs:{modal:!1,visible:t.dialogVisible,"close-on-click-modal":!1,title:"各工序任务统计",width:"380px"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n("ul",t._l(t.list,(function(e,a){return n("li",{key:a},[n("div",{staticClass:"li-name"},[t._v(t._s(e.name))]),e.total_amount>=0?n("div",[t._v(t._s(e.producing_amount))]):t._e(),n("div",{staticStyle:{width:"50px"}},[n("div",{staticClass:"process",style:{width:t.getPercentNum(e.total_amount)}})])])})),0)])},i=[]},"2b44":function(t,e,n){"use strict";n("7eba")},"2cf5":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("5530"));n("99af"),n("a630"),n("d81d"),n("e9f5"),n("ab43"),n("d3b7"),n("25f0"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("3ca3"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494");var o=n("6f48c"),r=n("ed08"),l=a(n("4f2b"));e.default={props:{selectList:{type:Array,default:function(){return[]}}},data:function(){return{checkImgIndex:void 0,currentItem:{},btnLoading:!1,dialogVisible1:!1,list:[],total:0,queryInfo:{Page:1,PageSize:8}}},computed:{checkList:function(){return this.selectList.map((function(t){return t.Id})).toString()},filterProjectId:function(){var t=this.selectList.map((function(t){return t.Project_Id}));return Array.from(new Set(t)).toString()}},mounted:function(){(0,l.default)(),this.fetchList()},methods:{fetchList:function(){var t=this;(0,o.GetPrintTemplateInfoPageList)((0,i.default)((0,i.default)({},this.queryInfo),{},{Project_Id:this.filterProjectId})).then((function(e){e.IsSucceed&&(t.queryInfo.Page=e.Data.Page,t.total=e.Data.TotalCount,t.list=e.Data.Data)}))},handleSubmit:function(){var t=this;this.currentItem?(0,o.GenerateReport)({id:this.currentItem.Id,type:"pdf",report:this.currentItem.File_Name.substring(0,this.currentItem.File_Name.length-4),open:"inline",keyValues:this.checkList}).then((function(e){var n=window.URL.createObjectURL(new Blob([e],{type:"application/pdf"}));window.open(n,"_blank"),t.btnLoading=!1})):this.$message({message:"请选择模板",type:"warning"})},handleH5:function(){var t=this;this.currentItem?(this.dialogVisible1=!0,this.btnLoading=!0,this.$nextTick((function(e){t.showPrintModel(t.currentItem)}))):this.$message({message:"请选择模板",type:"warning"})},showPrintModel:function(t){rubylong.grhtml5.barcodeURL=this.getUrl("/PRO/PrintTemplate/GetBarcode");var e,n=this.getUrl("/PRO/PrintTemplate/GetTemplteData?id="+t.Id),a=this.getUrl("/PRO/PrintTemplate/GetReportData?templateName=".concat(t.Display_Name,"&keyValue=").concat(this.checkList,"&type=main"));e=rubylong.grhtml5.insertReportViewer("report_print",n,a),this.btnLoading=!1,e.start()},handlePrint:function(){this.$print(this.$refs.print)},getUrl:function(t){return(0,r.combineURL)(this.$baseUrl,t)},currentChange:function(t){this.queryInfo.Page=t,this.fetchList()},handleImgClick:function(t,e){this.checkImgIndex=e,this.currentItem=t}}}},"359c":function(t,e,n){},"3a27":function(t,e,n){"use strict";n.r(e);var a=n("65ed7"),i=n("dfab");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("6ec1");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"6de3aa64",null);e["default"]=l.exports},"3e26":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"upload-box"},[t._m(0),n("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{attrs:{disabled:t.btnLoading},on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit()}}},[t._v("确 定")])],1)],1)])},i=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"margin-bottom":"20px"}},[n("strong",{staticStyle:{"font-size":"16px",color:"#222834"}},[t._v("操作方法")]),n("br"),t._v(" 1. 选择要排产的构件；"),n("br"),t._v(" 2. 点击右上角“导出”按钮；"),n("br"),t._v(" 3. 在下载后的excel表格中编辑“"),n("span",{staticClass:"txt-blue"},[t._v("工艺")]),t._v("”、“"),n("span",{staticClass:"txt-blue"},[t._v("排产日期")]),t._v("”字段后，重新上传。 （"),n("span",{staticClass:"txt-red"},[t._v("其它字段禁止修改")]),t._v("）"),n("br"),t._v(" 4. 编辑好后，在此重新上传导入。 5. 如需在Excel表中指定工序责任班组，在表格后边增加两列填写“工序1、班组1”，表头需要填写不重复的内容！ ")])}]},4179:function(t,e,n){"use strict";n.r(e);var a=n("962a"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"424c":function(t,e,n){"use strict";n.r(e);var a=n("d697"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"45fd":function(t,e,n){"use strict";n("dd0a")},4600:function(t,e,n){},"4b56":function(t,e,n){"use strict";n.r(e);var a=n("6ff0"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"4fa5c":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100"},[n("top-header",{scopedSlots:t._u([{key:"left",fn:function(){return[n("div",[n("span",{class:["z-card",{active:!t.isFinish}],on:{click:function(e){return t.changePage(!1)}}},[t._v("未排产")]),n("span",{class:["z-card",{active:t.isFinish}],on:{click:function(e){return t.changePage(!0)}}},[t._v("已排产")])])]},proxy:!0},{key:"right",fn:function(){return[n("div",[t.isFinish?[n("el-button",{attrs:{disabled:!t.selectList.length,icon:"iconfont icon-print",size:"large"},on:{click:function(e){return t.handlePrint(t.selectList)}}},[t._v("打印二维码 ")])]:[n("el-button",{attrs:{size:"large",type:"warning"},on:{click:t.handleCheckInfo}},[t._v("任务查看")]),n("el-divider",{attrs:{direction:"vertical"}}),t._e(),n("el-button",{attrs:{disabled:!t.selectList.length,size:"large",type:"success"},on:{click:t.handleExport}},[t._v("导出")])]],2)]},proxy:!0}])}),n("main",{staticClass:"main cs-z-shadow"},[n("dynamic-data-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],key:t.isFinish?1:2,ref:"dyTable",attrs:{"element-loading-text":"拼命加载中","element-loading-spinner":"el-icon-loading",columns:t.columns,config:t.tbConfig,data:t.tbData,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,multiSelectedChange:t.multiSelectedChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"hsearch_Project_Name",fn:function(e){var a=e.column;return[n("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},on:{change:t.projectChange},model:{value:t.$refs.dyTable.searchedField[a.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,a.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.projects,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"hsearch_InstallUnit_Name",fn:function(e){e.column;return[n("el-select",{attrs:{disabled:!t.$refs.dyTable.searchedField["Project_Name"],placeholder:"请选择",clearable:""},on:{change:t.installNameChange},model:{value:t.installName,callback:function(e){t.installName=e},expression:"installName"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.installOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"hsearch_InstallUnit_Priority",fn:function(e){var a=e.column;return[n("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:t.showSearchBtn},model:{value:t.$refs.dyTable.searchedField[a.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,a.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.priorityOption,(function(e){return n("el-option",{key:e.key,attrs:{label:e.label,value:e.value}},[n("span",{staticClass:"color-box",style:{textAlign:"center",backgroundColor:t.getColorBox(e.key).b,color:t.getColorBox(e.key).c}},[t._v(" "+t._s(e.label)+" ")])])}))],2)]}},{key:"InstallUnit_Priority",fn:function(e){var a=e.row;return[n("span",{staticClass:"color-box",style:{backgroundColor:t.getColorBox(a.InstallUnit_Priority).b,color:t.getColorBox(a.InstallUnit_Priority).c}},[t._v(" "+t._s(a.InstallUnit_Priority)+" "),void 0!==a.InstallUnit_Priority?n("span",[t._v("%")]):t._e()])]}},{key:"op",fn:function(e){var a=e.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleInfo(a)}}},[t._v("查看")]),n("el-divider",{attrs:{direction:"vertical"}}),t.isFinish?[n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleCancelPart(a)}}},[t._v("部分操作")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleCancel([a])}}},[t._v("全部撤销排产")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handlePrint([a])}}},[t._v("打印二维码")])]:[n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handlePartSchedule(a)}}},[t._v("部分排产")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleAllSchedule([a])}}},[t._v("全部排产")])]]}}])})],1),n("el-dialog",{staticClass:"cs-dialog",attrs:{top:"7vh",title:t.dialogInfo.title,visible:t.dialogInfo.dialogVisible,width:t.dialogInfo.width},on:{"update:visible":function(e){return t.$set(t.dialogInfo,"dialogVisible",e)},close:t.handleClose}},[t.dialogInfo.dialogVisible?n(t.dialogInfo.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":t.currentSelect},on:{close:t.handleClose,refresh:t.fetchData}}):t._e()],1),t.isTaskInfo?n("task-info",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"taskInfo"}):t._e(),n("el-drawer",{attrs:{"before-close":t.handleCloseDrawer,title:t.drawerTitle,visible:t.drawer,direction:"rtl",size:"85%"},on:{"update:visible":function(e){t.drawer=e}}},[t.drawer?n(t.drawerComponent,{ref:"drawerContent",tag:"component",attrs:{"install-list":t.currentSelect,"is-details":t.props.isDetails,"is-scheduled":t.isFinish,"project-name":t.props.projectName,"unit-name":t.props.unitName},on:{refresh:t.fetchData}}):t._e()],1)],1)},i=[]},"5f23":function(t,e,n){},"65ed7":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:t.form,inline:"",rules:t.rules,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"排产时间：",prop:"planTime"}},[n("el-date-picker",{staticStyle:{width:"150px"},attrs:{"value-format":"yyyy-MM-dd",type:"date"},model:{value:t.form.planTime,callback:function(e){t.$set(t.form,"planTime",e)},expression:"form.planTime"}})],1),n("el-form-item",{attrs:{label:"工艺：",prop:"process"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:t.getTeamList},model:{value:t.form.process,callback:function(e){t.$set(t.form,"process",e)},expression:"form.process"}},t._l(t.processOptions,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)],1),n("div",{staticClass:"cs-line"}),n("div",{staticClass:"tip-x"},[n("div",[t._v(" 所选工艺中，以下工序需要分配班组： ")]),n("div",{staticStyle:{color:"#298DFF"}},[t._v(" 显示全部工序 "),n("svg-icon",{attrs:{"icon-class":t.eysIcon,"class-name":"cs-icon"},nativeOn:{click:function(e){return t.showAllProcess(e)}}})],1)]),n("div",{staticClass:"timeline-x"},[n("el-timeline",[t._l(t.list,(function(e){return[n("el-timeline-item",{directives:[{name:"show",rawName:"v-show",value:t.showEyes||!t.showEyes&&!e.checkboxDisabled,expression:"showEyes||(!showEyes&&!item.checkboxDisabled)"}],key:e.Id,attrs:{timestamp:e.Name,color:"#298DFF",placement:"top"}},[n("div",{staticClass:"tag-x clearfix"},[n("el-checkbox-group",{attrs:{disabled:e.checkboxDisabled,max:1},model:{value:e.checkboxGroup,callback:function(n){t.$set(e,"checkboxGroup",n)},expression:"item.checkboxGroup"}},t._l(e.Teams,(function(t){return n("el-checkbox",{key:t.Id,attrs:{label:t.Name,border:""}})})),1)],1)])]}))],2)],1),n("div",{staticClass:"remark"},[t._v(" 注：您可以在“构件分配”页面进行重新调整责任班组。 ")]),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{loading:t.btnLoading,type:"primary"},on:{click:t.handleSchedule}},[t._v("确 定")])],1)],1)},i=[]},"6613a":function(t,e,n){"use strict";n.r(e);var a=n("4fa5c"),i=n("424c");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("0a26");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"ae39c296",null);e["default"]=l.exports},"6ec1":function(t,e,n){"use strict";n("5f23")},"6f48c":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AllotProject=f,e.DeleteTemplate=u,e.GenerateReport=h,e.GetPrintTemplateInfoPageList=r,e.GetPrintTemplatePageList=l,e.GetProjectShuttleList=d,e.GetTemplatePageList=s,e.SaveTemplate=c;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/PrintTemplate/GetPrintTemplateInfoPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/PrintTemplate/GetPrintTemplatePageList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/PrintTemplate/GetTemplatePageList",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/PrintTemplate/SaveTemplate",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/PrintTemplate/DeleteTemplate",method:"post",data:o.default.stringify(t)})}function d(t){return(0,i.default)({url:"/PRO/PrintTemplate/GetProjectShuttleList",method:"post",data:o.default.stringify(t)})}function f(t){return(0,i.default)({url:"/PRO/PrintTemplate/AllotProject",method:"post",data:o.default.stringify(t)})}function h(t){return(0,i.default)({url:"/PRO/PrintTemplate/GenerateReport",method:"post",data:o.default.stringify(t),responseType:"blob"})}},"6ff0":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7"),n("25f0");var i=a(n("5530")),o=a(n("c14f")),r=a(n("1da1")),l=a(n("3a27")),s=a(n("0f97")),c=a(n("15ac")),u=n("586a"),d=n("ed08"),f=a(n("cf995")),h=a(n("d639")),p=a(n("eab3")),m=a(n("34e9"));e.default={components:{DynamicDataTable:s.default,Schedule:l.default,ProCancel:h.default,ImportExcel:p.default,TopHeader:m.default,Print:f.default},mixins:[c.default],props:{isDetails:{type:Boolean,default:!0},isScheduled:{type:Boolean,default:!0},installList:{type:Array,default:function(){return[]}},projectName:{type:String,default:""},unitName:{type:String,default:""}},data:function(){return{queryInfo:{Page:1,PageSize:30,ParameterJson:[],codes:""},title:"",currentComponent:"",dialogVisible:!1,tbLoading:!1,selectList:[],currentList:[],columns:[],tbData:[],tbConfig:{Pager_Align:"center",Op_Width:220},total:0}},mounted:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:if(!t.isScheduled){e.n=2;break}return e.n=1,t.getTableConfig("comp_scheduled_details");case 1:e.n=3;break;case 2:return e.n=3,t.getTableConfig("comp_scheduling_details");case 3:t.isDetails&&(t.tbConfig.Is_Select=!1),t.fetchData();case 4:return e.a(2)}}),e)})))()},methods:{fetchData:function(t){var e=this;t&&(this.queryInfo.Page=t),this.tbLoading=!0;var n=this.installList.map((function(t){return t.InstallUnit_Id}));(0,u.GetSchedulingComponentByInstallPageList)((0,i.default)((0,i.default)({InstallUnit_Ids:n,Is_Scheduled:this.isScheduled},this.queryInfo),{},{Comp_Codes:this.queryInfo.codes.length>0?this.queryInfo.codes.split("\n"):[]})).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.selectList=[],e.currentList=[]):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},multiSelectedChange:function(t){this.selectList=t,this.currentList=t},refresh:function(){this.fetchData(),this.$emit("refresh")},handlePrint:function(){this.currentList=this.selectList.map((function(t){return{Id:t.Id,Project_Id:t.Project_Id}})),this.title="标签打印",this.currentComponent="Print",this.dialogVisible=!0},handleSchedule:function(){this.title="排产",this.currentComponent="Schedule",this.currentList=[{InstallUnit_Id:this.selectList[0].InstallUnit_Id,Comp_Ids:this.selectList.map((function(t){return t.Id}))}],this.dialogVisible=!0},close:function(){this.dialogVisible=!1},handleCancelSchedule:function(){this.title="撤销排产",this.currentComponent="ProCancel",this.dialogVisible=!0,this.currentList=this.selectList.map((function(t){return t.Id}))},handleImport:function(){this.title="导入排产",this.currentComponent="ImportExcel",this.dialogVisible=!0},handleExport:function(){var t=this,e=this.selectList.map((function(t){return t.Id}));(0,u.ExportToScheduleComponentInfo)({ids:e.toString()}).then((function(e){if(e.IsSucceed){var n=(0,d.combineURL)(t.$baseUrl,e.Data);window.open(n,"_blank")}else t.$message({message:e.Message,type:"error"})}))},getStatueInfo:function(t){switch(t){case 1:return{color:"#EBF1F6",label:"待排产"};case 2:return{color:"#91D5FF",label:"待生产"};case 3:return{color:"#FAC414",label:"生产中"};case 4:return{color:"#52C41A",label:"已完成"};default:return{}}}}}},7704:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"header"},[n("div",[n("span",[t._v("已选构件：")]),n("strong",[t._v(t._s(t.selectList.length))])]),n("div",{staticStyle:{"margin-left":"40px"}},[n("span",[t._v("标签模板：")]),n("strong",[t._v(t._s(t.currentItem.Display_Name))])])]),n("div",{staticClass:"card-box"},t._l(t.list,(function(e,a){return n("img",{key:a,class:{active:a===t.checkImgIndex},attrs:{src:t.getUrl(e.Thumbnail_Url),alt:""},on:{click:function(n){return t.handleImgClick(e,a)}}})})),0),n("div",{staticClass:"page-box"},[t.total>8?n("el-pagination",{attrs:{"page-size":t.queryInfo.PageSize,total:t.total,layout:"prev, pager, next"},on:{"current-change":t.currentChange}}):t._e()],1),n("footer",{staticClass:"cs-footer"},[n("el-button",{attrs:{loading:t.btnLoading,disabled:void 0===t.checkImgIndex,type:"success"},on:{click:t.handleH5}},[t._v("打 印 ")])],1),n("el-dialog",{attrs:{visible:t.dialogVisible1,"append-to-body":"",title:"提示",width:"50%"},on:{"update:visible":function(e){t.dialogVisible1=e}}},[n("div",{ref:"print",staticClass:"qrcode-box print-result result",attrs:{id:"report_print"}}),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogVisible1=!1}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:t.handlePrint}},[t._v("确 定")])],1)])],1)},i=[]},"7eba":function(t,e,n){},"7f5b":function(t,e,n){"use strict";n("4600")},8820:function(t,e,n){"use strict";n.r(e);var a=n("ae15"),i=n("4b56");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("1ae7");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"44b0df42",null);e["default"]=l.exports},"8d037":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dialog-content"},[n("span",{staticClass:"info"},[t._v(" 请确认是否撤销所选构件排产计划。 ")]),n("div",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{loading:t.btnLoading,type:"danger"},on:{click:t.handleSubmit}},[t._v("确 定")])],1)])},i=[]},"91c7":function(t,e,n){"use strict";n.r(e);var a=n("97d9"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"929e":function(t,e,n){},"962a":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7"),n("25f0");var a=n("586a");e.default={props:{selectList:{type:Array,default:function(){return[]}}},data:function(){return{btnLoading:!1}},methods:{handleSubmit:function(){var t=this;this.btnLoading=!0,(0,a.CancelSchedulingBase)({ids:this.selectList.toString()}).then((function(e){e.IsSucceed?(t.$message({title:"成功",dangerouslyUseHTMLString:!0,message:"撤销成功，共 ".concat(e.Data," 个构件"),type:"success"}),t.$emit("refresh")):t.$message({message:e.Message,type:"error"}),t.$emit("close"),t.btnLoading=!1}))}}}},9645:function(t,e,n){"use strict";n.r(e);var a=n("2cf5"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"97d9":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7");var i=a(n("2909")),o=n("586a");e.default={data:function(){return{dialogVisible:!1,list:[]}},methods:{fetchData:function(){var t=this;(0,o.GetWorkingProcedureSummary)({Page:1,PageSize:10}).then((function(e){e.IsSucceed?(t.max=Math.max.apply(Math,(0,i.default)(e.Data.map((function(t){return t.total_amount})))),t.list=e.Data):t.$message({message:e.Message,type:"error"})}))},handleOpen:function(){this.dialogVisible=!0,this.fetchData()},handleClose:function(){this.dialogVisible=!1},getPercentNum:function(t){return t/this.max*50+"px"}}}},a024:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProcessFlow=c,e.AddProessLib=R,e.AddTechnology=s,e.AddWorkingProcess=l,e.DelLib=j,e.DeleteProcess=L,e.DeleteProcessFlow=I,e.DeleteTechnology=_,e.DeleteWorkingTeams=k,e.GetAllProcessList=f,e.GetCheckGroupList=U,e.GetChildComponentTypeList=$,e.GetFactoryAllProcessList=h,e.GetFactoryPeoplelist=D,e.GetFactoryWorkingTeam=b,e.GetGroupItemsList=P,e.GetLibList=r,e.GetLibListType=N,e.GetProcessFlow=p,e.GetProcessFlowListWithTechnology=m,e.GetProcessList=u,e.GetProcessListBase=d,e.GetProcessListTeamBase=w,e.GetProcessListWithUserBase=G,e.GetProcessWorkingTeamBase=F,e.GetTeamListByUser=A,e.GetTeamProcessList=y,e.GetWorkingTeam=v,e.GetWorkingTeamBase=O,e.GetWorkingTeamInfo=x,e.GetWorkingTeams=T,e.GetWorkingTeamsPageList=C,e.SaveWorkingTeams=S,e.UpdateProcessTeam=g;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:o.default.stringify(t)})}function s(t){return(0,i.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:o.default.stringify(t)})}function c(t){return(0,i.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:o.default.stringify(t)})}function u(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:o.default.stringify(t)})}function d(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:o.default.stringify(t)})}function f(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:o.default.stringify(t)})}function h(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:o.default.stringify(t)})}function m(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:o.default.stringify(t)})}function b(){return(0,i.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function v(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:o.default.stringify(t)})}function y(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:o.default.stringify(t)})}function P(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:o.default.stringify(t)})}function I(t){return(0,i.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:o.default.stringify(t)})}function _(t){return(0,i.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:o.default.stringify(t)})}function L(t){return(0,i.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:t})}function T(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}function C(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:t})}function S(t){return(0,i.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:t})}function k(t){return(0,i.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:t})}function x(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:o.default.stringify(t)})}function O(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:o.default.stringify(t)})}function w(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:o.default.stringify(t)})}function G(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:t})}function D(t){return(0,i.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function U(t){return(0,i.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:t})}function R(t){return(0,i.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:t})}function $(t){return(0,i.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:t})}function j(t){return(0,i.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:t})}function N(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:t})}function F(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:t})}function A(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}},a888:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("d565")),o=function(t){t.directive("el-drag-dialog",i.default)};window.Vue&&(window["el-drag-dialog"]=i.default,Vue.use(o)),i.default.install=o;e.default=i.default},ae15:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"container"},[n("top-header",{attrs:{padding:"20px 40px"},scopedSlots:t._u([{key:"left",fn:function(){return[n("span",{staticStyle:{color:"#222834"}},[t._v(t._s(t.projectName)+" / "+t._s(t.unitName))])]},proxy:!0},{key:"right",fn:function(){return[n("div",{staticClass:"right-x"},[n("span",{staticClass:"input-label"},[t._v("构件编号：")]),n("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"构件编码，换行查找多个",autosize:{minRows:4,maxRows:4},resize:"none",type:"textarea"},model:{value:t.queryInfo.codes,callback:function(e){t.$set(t.queryInfo,"codes",e)},expression:"queryInfo.codes"}}),n("el-button",{staticStyle:{margin:"0 20px"},attrs:{type:"primary"},on:{click:function(e){return t.fetchData(1)}}},[t._v("搜索")]),t.isDetails?t._e():[t.isScheduled?[n("el-button",{attrs:{disabled:!t.selectList.length,plain:""},on:{click:t.handlePrint}},[t._v("标签打印")]),n("el-button",{attrs:{disabled:!t.selectList.length,type:"danger"},on:{click:t.handleCancelSchedule}},[t._v("撤销排产")])]:[n("el-button",{attrs:{type:"success"},on:{click:t.handleImport}},[t._v("导入排产")]),n("el-button",{attrs:{disabled:!t.selectList.length,plain:""},on:{click:t.handleExport}},[t._v("导 出")]),n("el-button",{attrs:{disabled:!t.selectList.length,type:"primary"},on:{click:t.handleSchedule}},[t._v("排 产 ")])]]],2)]},proxy:!0}])}),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"tb-x"},[n("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,config:t.tbConfig,data:t.tbData,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,multiSelectedChange:t.multiSelectedChange,tableSearch:t.tableSearch,columnSearchChange:t.columnSearchChange},scopedSlots:t._u([{key:"Production_Status_Name",fn:function(e){var a=e.row;return[n("span",{staticClass:"cs-status-circle",style:{background:t.getStatueInfo(a.Production_Status).color}}),t._v(" "+t._s(a.Production_Status_Name)+" ")]}}])})],1),n("el-dialog",{staticClass:"cs-dialog",attrs:{"append-to-body":!0,"modal-append-to-body":!1,visible:t.dialogVisible,title:t.title,top:"7vh",width:"'60%'"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[t.dialogVisible?n(t.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":t.currentList,"is-part-schedule":""},on:{close:t.close,refresh:t.refresh}}):t._e()],1)],1)},i=[]},cf995:function(t,e,n){"use strict";n.r(e);var a=n("7704"),i=n("9645");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("7f5b");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"0d955564",null);e["default"]=l.exports},d565:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("caad"),n("ac1f"),n("2532"),n("5319");e.default={bind:function(t,e,n){var a=t.querySelector(".el-dialog__header"),i=t.querySelector(".el-dialog");a.style.cssText+=";cursor:move;",i.style.cssText+=";top:0px;";var o=function(){return window.document.currentStyle?function(t,e){return t.currentStyle[e]}:function(t,e){return getComputedStyle(t,!1)[e]}}();a.onmousedown=function(t){var e=t.clientX-a.offsetLeft,r=t.clientY-a.offsetTop,l=i.offsetWidth,s=i.offsetHeight,c=document.body.clientWidth,u=document.body.clientHeight,d=i.offsetLeft,f=c-i.offsetLeft-l,h=i.offsetTop,p=u-i.offsetTop-s,m=o(i,"left"),g=o(i,"top");m.includes("%")?(m=+document.body.clientWidth*(+m.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(m=+m.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(t){var a=t.clientX-e,o=t.clientY-r;-a>d?a=-d:a>f&&(a=f),-o>h?o=-h:o>p&&(o=p),i.style.cssText+=";left:".concat(a+m,"px;top:").concat(o+g,"px;"),n.child.$emit("dragDialog")},document.onmouseup=function(t){document.onmousemove=null,document.onmouseup=null}}}}},d56f:function(t,e,n){"use strict";n.r(e);var a=n("1d89"),i=n("91c7");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("45fd");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"8f40e008",null);e["default"]=l.exports},d639:function(t,e,n){"use strict";n.r(e);var a=n("8d037"),i=n("4179");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("df3e");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"4a6c9b90",null);e["default"]=l.exports},d65e:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("d81d"),n("e9f5"),n("f665"),n("ab43"),n("d3b7");var a=n("a024"),i=n("586a");e.default={props:{selectList:{type:Array,default:function(){return[]}},isPartSchedule:{default:!1,type:Boolean}},data:function(){return{list:[],form:{planTime:"",process:""},showEyes:!1,btnLoading:!1,processOptions:[],dialogFormVisible:!1,rules:{planTime:[{required:!0,message:"请选择",trigger:"change"}],process:[{required:!0,message:"请选择",trigger:"change"}]}}},computed:{eysIcon:function(){return this.showEyes?"eye-open":"eye"}},mounted:function(){this.init()},methods:{init:function(){this.getLibList()},handleSchedule:function(){var t=this;this.$refs["form"].validate((function(e){if(e){var n=t.list.map((function(t){var e;return{Working_Process_Id:t.Id,Working_Team_Id:null===(e=t.Teams.find((function(e){return e.Name===t.checkboxGroup[0]})))||void 0===e?void 0:e.Id}})),a={Schedule_Date:t.form.planTime,Technology_Id:t.form.process,Allocation_Compids:t.selectList,Allocation_Teams:n};t.btnLoading=!0,(0,i.ScheduleComponentAndAllocation)(a).then((function(e){e.IsSucceed?(t.$message({message:"排产成功 ".concat(e.Data," 个"),type:"success"}),t.$emit("refresh"),t.$emit("close")):t.$message({message:e.Message,type:"error"}),t.btnLoading=!1}))}}))},getLibList:function(){var t=this;(0,a.GetLibList)({type:1}).then((function(e){e.IsSucceed?t.processOptions=e.Data:t.$message({message:e.Message,type:"error"})}))},getTeamList:function(){var t=this;(0,a.GetProcessListTeamBase)({technologyId:this.form.process}).then((function(e){e.IsSucceed?t.list=e.Data.map((function(e){return 1===e.Teams.length?(t.$set(e,"checkboxGroup",[e.Teams[0].Name]),t.$set(e,"checkboxDisabled",!0)):(t.$set(e,"checkboxGroup",[]),t.$set(e,"checkboxDisabled",!1)),e})):t.$message({message:e.Message,type:"error"})}))},showAllProcess:function(){this.showEyes=!this.showEyes}}}},d697:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("f665"),n("7d54"),n("ab43"),n("dca8"),n("d3b7"),n("25f0"),n("159b");var i=a(n("2909")),o=a(n("5530")),r=a(n("c14f")),l=a(n("1da1")),s=a(n("34e9")),c=a(n("0f97")),u=a(n("a888")),d=a(n("15ac")),f=n("586a"),h=a(n("3a27")),p=a(n("8820")),m=a(n("d56f")),g=n("ed08"),b=a(n("d639")),v=a(n("cf995")),y=a(n("eab3")),P=n("1b69"),I=n("f2f6");e.default={name:"PROProductionSchedule",components:{TopHeader:s.default,ImportExcel:y.default,Print:v.default,ProCancel:b.default,DynamicDataTable:c.default,Schedule:h.default,TaskInfo:m.default,RowDetail:p.default},directives:{elDragDialog:u.default},mixins:[d.default],data:function(){return{currentSelect:[],drawerTitle:"",drawerComponent:"",drawer:!1,isTaskInfo:!1,tbLoading:!1,isFinish:!1,queryInfo:{Page:1,PageSize:10,ParameterJson:[]},selectList:[],columns:[],tbData:[],tbConfig:{Pager_Align:"center",Op_Width:220},total:0,dialogInfo:{title:"",currentComponent:"",dialogVisible:!1,width:"40%"},props:{isDetails:!0,projectName:"",unitName:""},projects:[],installOption:[],installName:"",priorityOption:[]}},watch:{isFinish:function(t){this.tbConfig.Op_Width=t?340:220}},mounted:function(){this.initData()},methods:{initData:function(){var t=this;return(0,l.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:if(!t.isFinish){e.n=2;break}return e.n=1,t.getTableConfig("comp_scheduled_install_list");case 1:e.n=3;break;case 2:return e.n=3,t.getTableConfig("comp_un_scheduling_install_list");case 3:t.fetchData(),t.getSearchOption();case 4:return e.a(2)}}),e)})))()},fetchData:function(){var t=this;return new Promise((function(e){t.tbLoading=!0,(0,f.GetSchedulingComponentInstallPageList)((0,o.default)((0,o.default)({},t.queryInfo),{},{Is_Scheduled:t.isFinish})).then((function(n){n.IsSucceed?(t.tbData=n.Data.Data,t.total=n.Data.TotalCount,t.selectList=[],t.currentList=[]):t.$message({message:n.Message,type:"error"}),t.tbLoading=!1,e()}))}))},changePage:function(t){this.queryInfo.Page=1,this.isFinish=t,this.initData(),this.queryInfo.ParameterJson=[]},handleInfo:function(t){this.currentSelect=[t],this.drawerTitle="查看详情",this.drawerComponent="RowDetail",this.props.isDetails=!0,this.props.projectName=t.Project_Name,this.props.unitName=t.InstallUnit_Name,this.drawer=!0},handleCheckInfo:function(){var t=this;this.isTaskInfo=!0,this.$nextTick((function(e){t.$refs["taskInfo"].handleOpen()}))},handleCancelPart:function(t){this.currentSelect=[t],this.drawerTitle="部分操作",this.drawerComponent="RowDetail",this.props.isDetails=!1,this.props.projectName=t.Project_Name,this.props.unitName=t.InstallUnit_Name,this.drawer=!0},handleAllSchedule:function(t){this.currentSelect=t.map((function(t){return{InstallUnit_Id:t.InstallUnit_Id,Comp_Ids:t.Unscheduling_Ids}})),this.dialogInfo.title="排产",this.dialogInfo.currentComponent="Schedule",this.dialogInfo.dialogVisible=!0},handleImport:function(){this.dialogInfo.title="导入排产",this.dialogInfo.currentComponent="ImportExcel",this.dialogInfo.dialogVisible=!0},handleCloseDrawer:function(t){t()},handleCancel:function(t){var e=[];t.forEach((function(t){e.push.apply(e,(0,i.default)(t.Scheduling_Ids))})),this.currentSelect=e,this.dialogInfo.title="撤销排产",this.dialogInfo.currentComponent="ProCancel",this.dialogInfo.dialogVisible=!0},handleExport:function(){var t=this,e=[];this.selectList.forEach((function(t){e.push.apply(e,(0,i.default)(t.Unscheduling_Ids))})),(0,f.ExportToScheduleComponentInfo)({ids:e.toString()}).then((function(e){if(e.IsSucceed){var n=(0,g.combineURL)(t.$baseUrl,e.Data);window.open(n,"_blank")}else t.$message({message:e.Message,type:"error"})}))},multiSelectedChange:function(t){this.selectList=t,this.currentSelect=t},handleClose:function(){this.dialogInfo.dialogVisible=!1},handleSchedule:function(){this.dialogInfo.title="排产",this.dialogInfo.currentComponent="Schedule",this.dialogInfo.dialogVisible=!0,this.currentSelect=this.selectList.map((function(t){return{InstallUnit_Id:t.InstallUnit_Id,Comp_Ids:t.Unscheduling_Ids}}))},handlePartSchedule:function(t){this.currentSelect=[t],this.drawerTitle="部分排产",this.drawerComponent="RowDetail",this.props.projectName=t.Project_Name,this.props.unitName=t.InstallUnit_Name,this.props.isDetails=!1,this.drawer=!0},handlePrint:function(t){var e=[];t.forEach((function(t){var n=t.Scheduling_Ids&&t.Scheduling_Ids.map((function(e){return{Id:e,Project_Id:t.Project_Id}}));e.push.apply(e,(0,i.default)(n))})),this.dialogInfo.title="标签打印",this.dialogInfo.currentComponent="Print",this.currentSelect=e,this.dialogInfo.dialogVisible=!0},getColorBox:function(t){return t>=100?{b:"#3E0606",c:"#ffffff"}:t>=67&&t<100?{b:"#F13333",c:"#ffffff"}:t>=33&&t<67?{b:"#FFD736",c:"#9C6800"}:t>=0&&t<33?{b:"#57DB7A",c:"#008123"}:{b:"#BCE4FF",c:"#44AAEF"}},getSearchOption:function(){this.getSearchProjectList(),this.priorityOption=[{key:100,value:[99.99,null],label:"已经延迟"},{key:70,value:[66.99,100],label:"紧急"},{key:35,value:[32.99,67],label:"刚好"},{key:24,value:[-.01,33],label:"不急"},{key:-10,value:[null,0],label:"太早"}]},getSearchProjectList:function(){var t=this;(0,P.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=Object.freeze(e.Data))}))},projectChange:function(t){if(this.installName="",this.installOption=[],t){var e=this.projects.find((function(e){return e.Name===t}));this.getUnitList(e.Id)}else this.$refs.dyTable.searchedField["InstallUnit_Name"]="";this.showSearchBtn()},getUnitList:function(t){var e=this;(0,I.GetInstallUnitList)({Project_Id:t}).then((function(t){t.IsSucceed&&(e.installOption=t.Data)}))},installNameChange:function(t){this.$refs.dyTable.searchedField["InstallUnit_Name"]=t,this.showSearchBtn()}}}},dca8:function(t,e,n){"use strict";var a=n("23e7"),i=n("bb2f"),o=n("d039"),r=n("861d"),l=n("f183").onFreeze,s=Object.freeze,c=o((function(){s(1)}));a({target:"Object",stat:!0,forced:c,sham:!i},{freeze:function(t){return s&&r(t)?s(l(t)):t}})},dd0a:function(t,e,n){},dd3c:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("3796")),o=n("586a"),r=n("ed08");e.default={components:{UploadExcel:i.default},data:function(){return{btnLoading:!1}},mounted:function(){},methods:{beforeUpload:function(t){var e=this,n=new FormData;n.append("files",t),this.btnLoading=!0,(0,o.ImportSchedulingInfoWithAlloct)(n).then((function(t){if(t.IsSucceed)e.$message({message:"导入成功",type:"success"}),e.$emit("refresh");else if(e.$message({message:t.Message,type:"error"}),t.Data){var n=(0,r.combineURL)(e.$baseUrl,t.Data);window.open(n,"_blank")}e.$emit("close"),e.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()}}}},df3e:function(t,e,n){"use strict";n("fa76")},dfab:function(t,e,n){"use strict";n.r(e);var a=n("d65e"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},eab3:function(t,e,n){"use strict";n.r(e);var a=n("3e26"),i=n("f456");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("2b44");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"099d1ade",null);e["default"]=l.exports},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=s,e.CheckPlanTime=c,e.DeleteInstallUnit=h,e.GetCompletePercent=v,e.GetEntity=P,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=b,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=u,e.GetInstallUnitList=l,e.GetInstallUnitPageList=r,e.GetProjectInstallUnitList=y,e.ImportInstallUnit=m,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=I;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function y(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function P(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(t)})}function I(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},f456:function(t,e,n){"use strict";n.r(e);var a=n("dd3c"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},fa76:function(t,e,n){}}]);