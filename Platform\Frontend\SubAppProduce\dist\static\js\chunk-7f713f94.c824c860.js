(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7f713f94"],{"0d58":function(e,t,a){"use strict";a.r(t);var i=a("56e3"),r=a.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(l);t["default"]=r.a},"136b":function(e,t,a){},"1c4a":function(e,t,a){"use strict";a.r(t);var i=a("2503"),r=a("c1e2");for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);a("85de");var n=a("2877"),o=Object(n["a"])(r["default"],i["a"],i["b"],!1,null,"22cdbf10",null);t["default"]=o.exports},"1eb6":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"新增下级",width:"30%",visible:e.dialogVisible,"before-close":e.onClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"ruleForm",attrs:{model:e.formData,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名",prop:"Display_Name"}},[a("el-input",{style:{width:"100%"},attrs:{placeholder:"请输入项目名",clearable:""},model:{value:e.formData.Display_Name,callback:function(t){e.$set(e.formData,"Display_Name",t)},expression:"formData.Display_Name"}})],1),a("el-form-item",{attrs:{label:"项目值",prop:"Value"}},[a("el-input",{style:{width:"100%"},attrs:{placeholder:"请输入项目值",clearable:""},model:{value:e.formData.Value,callback:function(t){e.$set(e.formData,"Value",t)},expression:"formData.Value"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"Sort"}},[a("el-input",{style:{width:"100%"},attrs:{placeholder:"请输入排序",clearable:""},model:{value:e.formData.Sort,callback:function(t){e.$set(e.formData,"Sort",e._n(t))},expression:"formData.Sort"}})],1),a("el-form-item",{attrs:{prop:"Is_Default",label:"是否默认"}},[a("el-checkbox",{model:{value:e.formData.Is_Default,callback:function(t){e.$set(e.formData,"Is_Default",t)},expression:"formData.Is_Default"}})],1),a("el-form-item",{attrs:{label:"是否有效",prop:"Is_Enabled"}},[a("el-checkbox",{model:{value:e.formData.Is_Enabled,callback:function(t){e.$set(e.formData,"Is_Enabled",t)},expression:"formData.Is_Enabled"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{style:{width:"100%"},attrs:{type:"textarea",placeholder:"请输入备注",autosize:{minRows:4,maxRows:4}},model:{value:e.formData.Remark,callback:function(t){e.$set(e.formData,"Remark",t)},expression:"formData.Remark"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.onClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},"24e0":function(e,t,a){},2503:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticClass:"h100 cs-fill-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"cs-header"},[a("span",{staticClass:"cs-title"},[e._v(e._s(e.tableTopTitle))]),e.showAddBtn?a("el-button",{attrs:{type:"success"},on:{click:function(t){return e.handleOpenDialog("add")}}},[e._v("新增")]):e._e()],1)]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-custom-table",attrs:{stripe:"",border:"","max-height":"690",data:e.tableData,size:"middle","row-key":"Id","tree-props":{children:"Children",hasChildren:"hasChildren"}}},[e._l(e.tableTitle,(function(t){return a("el-table-column",{key:t.id,attrs:{label:t.name,align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){var r=i.row;return["Is_Enabled"===t.en||"is_Default"===t.en?a("span",["Is_Enabled"===t.en?a("el-switch",{attrs:{"active-color":"#13ce66"},on:{change:function(t){return e.handleChangeSwitch(t,r)}},model:{value:r.Data.Is_Enabled,callback:function(t){e.$set(r.Data,"Is_Enabled",t)},expression:"row.Data.Is_Enabled"}}):a("el-switch",{attrs:{"active-color":"#13ce66"},on:{change:function(t){return e.handleChangeDefault(r)}},model:{value:r.Data.Is_Default,callback:function(t){e.$set(r.Data,"Is_Default",t)},expression:"row.Data.Is_Default"}})],1):a("span",[e._v(" "+e._s(r.Data[t.en])+" ")])]}}],null,!0)})})),a("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("el-button",{attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleOpenDialog("edit",i.Data)}}},[e._v("编辑 ")]),a("el-button",{attrs:{icon:"el-icon-search",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleChildrenOpenDialog(i.Data)}}},[e._v("下级配置 ")])]}}])})],2),a("Dialog",{ref:"dialog",on:{update:e.handleUpdate}}),a("DChildren",{ref:"cdialog",on:{Subordinate:function(t){return e.$emit("updateTable")}}})],1)},r=[]},3126:function(e,t,a){"use strict";a.r(t);var i=a("8f8e"),r=a.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(l);t["default"]=r.a},"36a16":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,width:"30%",visible:e.dialogVisible,"before-close":e.onClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"ruleForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名",prop:"Display_Name"}},[a("el-input",{style:{width:"100%"},attrs:{placeholder:"请输入项目名",clearable:""},model:{value:e.formData.Display_Name,callback:function(t){e.$set(e.formData,"Display_Name",t)},expression:"formData.Display_Name"}})],1),a("el-form-item",{attrs:{label:"项目值",prop:"Value"}},[a("el-input",{style:{width:"100%"},attrs:{placeholder:"请输入项目值",clearable:""},model:{value:e.formData.Value,callback:function(t){e.$set(e.formData,"Value",t)},expression:"formData.Value"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"Sort"}},[a("el-input",{style:{width:"100%"},attrs:{placeholder:"请输入排序",clearable:""},model:{value:e.formData.Sort,callback:function(t){e.$set(e.formData,"Sort",e._n(t))},expression:"formData.Sort"}})],1),a("el-form-item",{attrs:{prop:"Is_Default",label:"是否默认"}},[a("el-checkbox",{model:{value:e.formData.Is_Default,callback:function(t){e.$set(e.formData,"Is_Default",t)},expression:"formData.Is_Default"}})],1),a("el-form-item",{attrs:{label:"是否有效",prop:"Is_Enabled"}},[a("el-checkbox",{model:{value:e.formData.Is_Enabled,callback:function(t){e.$set(e.formData,"Is_Enabled",t)},expression:"formData.Is_Enabled"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{style:{width:"100%"},attrs:{type:"textarea",placeholder:"请输入备注",autosize:{minRows:4,maxRows:4}},model:{value:e.formData.Remark,callback:function(t){e.$set(e.formData,"Remark",t)},expression:"formData.Remark"}})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.onClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handelConfirm}},[e._v("确定")])],1)],1)},r=[]},"39ad1":function(e,t,a){},"41c4":function(e,t,a){"use strict";a("39ad1")},"4ac5":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var i=a("6186");t.default={name:"children",props:{mode:{type:Number,default:0},unit:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:!1,formData:{Parent_Id:"",Dictionary_Id:"",Display_Name:"",Value:"",Sort:0,Is_Enabled:!0,Is_Default:!1,Remark:""},rules:{Display_Name:[{required:!0,message:"请输入项目名",trigger:"blur"}],Value:[{required:!0,message:"请输入项目值",trigger:"blur"}],Sort:[{required:!0,type:"number",message:"排序必须为数字值",trigger:"blur"}]}}},created:function(){},methods:{onClose:function(){this.dialogVisible=!1,this.$refs.ruleForm.resetFields(),this.formData={Parent_Id:"",Dictionary_Id:"",Display_Name:"",Value:"",Sort:0,Is_Enabled:!0,Is_Default:!1,Remark:""}},onOpen:function(e){this.dialogVisible=!0,this.formData.Parent_Id=e.Id},submit:function(){var e=this;this.$refs.ruleForm.validate((function(t){t&&(0,i.SaveDictionaryDetail)(e.formData).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"添加成功"}),e.$emit("Subordinate"),e.onClose()):e.$message({type:"error",message:t.Message})}))}))}}}},"56e3":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a("6186");t.default={data:function(){return{title:"",dialogVisible:!1,btnLoading:!1,formData:{Dictionary_Id:"",Display_Name:"",Value:"",Sort:0,Is_Enabled:!0,Is_Default:!1,Remark:""},rules:{Display_Name:[{required:!0,message:"请输入项目名",trigger:"blur"}],Value:[{required:!0,message:"请输入项目值",trigger:"blur"}],Sort:[{required:!0,type:"number",message:"排序必须为数字值",trigger:"blur"}]}}},mounted:function(){},methods:{onOpen:function(e,t){var a=this;this.formData.Dictionary_Id=this.$route.query.n,this.dialogVisible=!0,"edit"===e?(this.title="编辑",this.$nextTick((function(e){a.initEdit(t)}))):(this.$delete(this.formData,"Id"),this.title="新增")},initEdit:function(e){this.formData=Object.assign({},e)},onClose:function(){this.$refs["ruleForm"].resetFields(),this.dialogVisible=!1},handelConfirm:function(){var e=this;this.$refs["ruleForm"].validate((function(t){t&&(e.btnLoading=!0,(0,i.SaveDictionaryDetail)(e.formData).then((function(t){t.IsSucceed?(e.$refs["ruleForm"].resetFields(),e.dialogVisible=!1,e.$emit("update"),e.$message({message:"保存成功",type:"success"})):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1})))}))}}}},"5b1d":function(e,t,a){"use strict";a("24e0")},"5d66":function(e,t,a){"use strict";a.r(t);var i=a("36a16"),r=a("0d58");for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);var n=a("2877"),o=Object(n["a"])(r["default"],i["a"],i["b"],!1,null,null,null);t["default"]=o.exports},"63e0":function(e,t,a){"use strict";a.r(t);var i=a("4ac5"),r=a.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(l);t["default"]=r.a},"85de":function(e,t,a){"use strict";a("136b")},"8f8e":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7");var r=i(a("1c4a")),l=i(a("1463")),n=a("6186");t.default={name:"SysDictionary",components:{zTable:r.default,TreeDetail:l.default},data:function(){return{treeLoading:!1,isEdit:!1,dialogVisible:!1,tbLoading:!1,tableTopTitle:"",treeData:[],tableData:[],firstId:"",expandedKey:"",showTb:!0,DictionaryEntity:{Code:"",Display_Name:"",Parent_Id:"",Is_Project_Modify:!1},rules:{Code:[{required:!0,message:"请输入编号",trigger:"blur"}],Display_Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Parent_Id:[{required:!0,message:"请选择父级节点",trigger:"blur"}]},selectParams:{clearable:!0,placeholder:"请输入内容"},treeParams:{clickParent:!1,filterable:!0,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},treeParamsDialog:{clickParent:!0,filterable:!0,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}}}},mounted:function(){this.fetchTreeData()},methods:{fetchTreeData:function(){var e=this;this.treeLoading=!0,(0,n.GetTreeDictionary)().then((function(t){t.Data[0].Children[0]&&t.Data[0].Children[0].Id?e.firstId=t.Data[0].Children[0].Id:e.firstId=t.Data[0].Id,e.expandedKey=e.$route.query.n||e.firstId,!e.$route.query.n&&e.$router.push({name:"SysDictionary",query:{n:e.firstId}}),e.treeData=t.Data,t.Data[0].Children[0]&&t.Data[0].Children[0].Label?e.tableTopTitle=t.Data[0].Children[0].Label:e.tableTopTitle=t.Data[0].Label,e.treeLoading=!1}))},fetchTableData:function(e){var t=this;e||(e=this.$route.query.n),this.tbLoading=!0,(0,n.GetDictionaryTreeDetailList)({dictionaryId:e}).then((function(e){t.tableData=e.Data,t.tbLoading=!1}))},handleNodeClick:function(e){this.showTb=!0,this.tableTopTitle=e.Label+"("+e.Data.Code+")",this.$router.push({name:"SysDictionary",query:{n:e.Id}}),this.fetchTableData(e.Id)},handleClick:function(){var e=this;this.isEdit=!1,this.dialogVisible=!0,this.$nextTick((function(){e.$refs.treeSelect.treeDataUpdateFun(e.treeData)}))},_searchFun:function(e){this.$refs.treeSelect.filterFun(e)},handleNodeButtonEdit:function(e){var t=this;this.dialogVisible=!0,this.$nextTick((function(){t.$refs.treeSelect.treeDataUpdateFun(t.treeData)})),this.isEdit=!0,this.DictionaryEntity.Parent_Id=e.Id,(0,n.GetDictionaryEntity)({Id:e.Id}).then((function(e){t.DictionaryEntity=JSON.parse(JSON.stringify(e.Data))}))},handleClose:function(){this.dialogVisible=!1,this.$refs["ruleForm"].resetFields(),this.DictionaryEntity={Code:"",Display_Name:"",Parent_Id:"",Is_Project_Modify:!1}},saveDictionary:function(){var e=this;(0,n.SaveDictionary)(this.DictionaryEntity).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"操作成功"}),e.dialogVisible=!1,e.fetchTreeData()):e.$message({type:"error",message:t.Message})}))},getTableTopTitle:function(){},handleEdit:function(e){},handleNodeClickFalse:function(){this.showTb=!1},changedata:function(e){var t=this;0!==e.length&&e.map((function(e){e.Label=e.Label+"("+e.Code+")",e.Data.Display_Name=e.Data.Display_Name+"("+e.Data.Code+")",t.changedata(e.Children)}))}}}},9163:function(e,t,a){"use strict";a.r(t);var i=a("1eb6"),r=a("63e0");for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);a("b33d"),a("41c4");var n=a("2877"),o=Object(n["a"])(r["default"],i["a"],i["b"],!1,null,"0dd3530c",null);t["default"]=o.exports},b33d:function(e,t,a){"use strict";a("c14c")},c14c:function(e,t,a){},c1e2:function(e,t,a){"use strict";a.r(t);var i=a("faed"),r=a.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(l);t["default"]=r.a},d4f7:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-row",{staticClass:"h100",attrs:{type:"flex",gutter:15}},[a("el-col",{attrs:{span:5}},[a("el-card",{staticClass:"h100 cs-scroll"},[a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:e.handleClick}},[e._v("新增")])],1),a("tree-detail",{attrs:{"expand-all":"","show-detail":!0,"show-icon":!1,"can-node-click":!1,"tree-loading":e.treeLoading,"tree-data":e.treeData,"button-type-array":["edit"],"expanded-key":e.expandedKey},on:{handleNodeClickFalse:e.handleNodeClickFalse,currentNodeLabel:e.getTableTopTitle,handleNodeClick:e.handleNodeClick,handleNodeButtonEdit:e.handleNodeButtonEdit}})],1)],1),a("el-col",{attrs:{span:19}},[a("z-table",{directives:[{name:"show",rawName:"v-show",value:e.showTb,expression:"showTb"}],ref:"table",attrs:{"table-data":e.tableData,"table-top-title":e.tableTopTitle,"first-id":e.firstId},on:{updateTable:e.fetchTableData,rowData:e.handleEdit}})],1)],1),a("el-dialog",{attrs:{title:e.isEdit?"编辑":"新增",visible:e.dialogVisible,width:"20%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"ruleForm",attrs:{model:e.DictionaryEntity,rules:e.rules,"label-width":"90px"}},[a("el-form-item",{attrs:{label:"Code",prop:"Code"}},[a("el-input",{attrs:{disabled:e.isEdit,placeholder:"请输入"},model:{value:e.DictionaryEntity.Code,callback:function(t){e.$set(e.DictionaryEntity,"Code",t)},expression:"DictionaryEntity.Code"}})],1),a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{attrs:{placeholder:"请输入"},model:{value:e.DictionaryEntity.Display_Name,callback:function(t){e.$set(e.DictionaryEntity,"Display_Name",t)},expression:"DictionaryEntity.Display_Name"}})],1),a("el-form-item",{attrs:{label:"父级节点",prop:"Parent_Id"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"treeselect",staticStyle:{width:"100%"},attrs:{"select-params":e.selectParams,"tree-params":e.treeParamsDialog},on:{searchFun:e._searchFun},model:{value:e.DictionaryEntity.Parent_Id,callback:function(t){e.$set(e.DictionaryEntity,"Parent_Id",t)},expression:"DictionaryEntity.Parent_Id"}})],1),a("el-form-item",{attrs:{label:"是否可修改",prop:"Is_Project_Modify"}},[a("el-switch",{model:{value:e.DictionaryEntity.Is_Project_Modify,callback:function(t){e.$set(e.DictionaryEntity,"Is_Project_Modify",t)},expression:"DictionaryEntity.Is_Project_Modify"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.saveDictionary}},[e._v("确 定")])],1)],1)],1)},r=[]},e41b:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=s,t.GetPartsImportTemplate=u,t.GetPartsList=o,t.GetProjectAreaTreeList=l,t.ImportParts=d,t.SaveProjectAreaSort=n;var r=i(a("b775"));function l(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function n(e){return(0,r.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},f604:function(e,t,a){"use strict";a.r(t);var i=a("d4f7"),r=a("3126");for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);a("5b1d");var n=a("2877"),o=Object(n["a"])(r["default"],i["a"],i["b"],!1,null,"7e6682b8",null);t["default"]=o.exports},faed:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a("6186"),l=i(a("9163")),n=i(a("5d66"));t.default={components:{Dialog:n.default,DChildren:l.default},props:{tbLoading:{type:Boolean,default:!1},tableTopTitle:{type:String,default:""},tableData:{type:Array,default:function(){return[]}},firstId:{type:String,default:""}},data:function(){return{pages:{title:0,page:"1",pageSize:"10"},value:"",options:[{value:"选项1",label:"编号"},{value:"选项2",label:"名称"},{value:"选项3",label:"地址"}],tableTitle:[{name:"项目名",id:1,en:"Display_Name"},{name:"项目值",id:2,en:"Value"},{name:"排序",id:4,en:"Sort"},{name:"默认",id:5,en:"is_Default"},{name:"备注",id:6,en:"Remark"},{name:"有效",id:0,en:"Is_Enabled"}]}},computed:{showAddBtn:function(){return!!this.$route.query.n}},watch:{firstId:function(e,t){this.handleUpdate(e)}},methods:{handleUpdate:function(e){this.$emit("updateTable",e)},handleOpenDialog:function(e,t){this.$refs.dialog.onOpen(e,t)},handleChangeSwitch:function(e,t){var a=this;(0,r.UpdateDictionaryDetailIsValid)({is_valid:e,id:t.Id}).then((function(e){a.$emit("updateTable"),a.$message({message:"修改成功",type:"success"})}))},handleChildrenOpenDialog:function(e){this.$refs.cdialog.onOpen(e)}}}}}]);