(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4497ba0e"],{"069f":function(e,t,a){},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=l,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(e,t,a){var l=r(),o=e-l,s=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=s;var e=Math.easeInOutQuad(u,l,o,t);i(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},1345:function(e,t,a){"use strict";a.r(t);var n=a("af34"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},1364:function(e,t,a){"use strict";a.r(t);var n=a("6c72"),i=a("eab5");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("891d");var l=a("2877"),o=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"088e5182",null);t["default"]=o.exports},2644:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{item:{type:Object,default:function(){}}},methods:{handleProject:function(e){this.$emit("info",e)},handleDelete:function(e){this.$emit("delete",e)},handlePrint:function(e){this.$emit("print",e)}}}},"26df":function(e,t,a){"use strict";a.r(t);var n=a("4e1f"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},28610:function(e,t,a){},"29af":function(e,t,a){"use strict";a.r(t);var n=a("2644"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"2ad9":function(e,t,a){},"2c01":function(e,t,a){"use strict";a("069f")},"3d08":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-z-flex-pd16-wrap x-container"},[a("div",{staticClass:"ml-8"},[a("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("div",{staticClass:"cs-main"},e._l(e.list,(function(t,n){return a("card",{key:n,class:[{"mr-8":!n%4}],attrs:{item:t},on:{delete:e.handleDelete,info:e.handleInfo,print:e.handlePrint}})})),1),a("Pagination",{attrs:{limit:e.listQuery.PageSize,page:e.listQuery.Page,total:e.total},on:{"update:limit":function(t){return e.$set(e.listQuery,"PageSize",t)},"update:page":function(t){return e.$set(e.listQuery,"Page",t)},pagination:e.fetchData}}),e.dialogVisible?a("el-dialog",{class:{"cs-dialog":"SelectProject"===e.currentComponent},attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e()],1)},i=[]},4103:function(e,t,a){"use strict";a("28610")},"430c":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a("ed08"),r=n(a("7b1d"));t.default={directives:{print:r.default},data:function(){return{src:"",printObj:{id:"printMe",extraHead:'<meta http-equiv="Content-Language" content="zh-cn"/>'}}},methods:{init:function(e){this.src=(0,i.combineURL)(this.$baseUrl,e.Thumbnail_Url)}}}},"4e1f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("b8cf")),r=n(a("a62b")),l=n(a("1364")),o=a("6f48c"),s=n(a("333d")),u=n(a("e4e0d"));t.default={name:"PROBasicLabelPrinting",components:{Card:i.default,SelectProject:r.default,Print:l.default,Pagination:s.default,Add:u.default},data:function(){return{dialogVisible:!1,currentComponent:"",total:0,listQuery:{PageSize:10,Page:1},value:"",title:"",list:[],width:"40%",h60:!1}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var e=this;(0,o.GetTemplatePageList)(this.listQuery).then((function(t){t.IsSucceed?(e.list=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})}))},handleAdd:function(){this.currentComponent="Add",this.title="新增",this.width="60%",this.dialogVisible=!0},handleInfo:function(e){var t=this;this.currentComponent="SelectProject",this.title="选择项目",this.width="50%",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].init(e)}))},handlePrint:function(e){var t=this;this.currentComponent="Print",this.title="库位标签打印",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleClose:function(){this.dialogVisible=!1},handleDelete:function(e){var t=this;this.$confirm("是否删除该标签?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.DeleteTemplate)({ids:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))}}}},6645:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"报表名称",prop:"Display_Name"}},[a("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"报表类型",prop:"Type"}},[a("el-input",{model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"报表模板",prop:"File_Name"}},[a("el-input",{model:{value:e.form.File_Name,callback:function(t){e.$set(e.form,"File_Name",t)},expression:"form.File_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"选择文件",prop:"templates"}},[a("upload",{ref:"file",attrs:{"before-upload":e.beforeUploadFile,"btn-text":e.fileName,limit:1,accept:".grf","btn-icon":"el-icon-upload","btn-size":"middle"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"条件字段",prop:"Key_Column"}},[a("el-input",{model:{value:e.form.Key_Column,callback:function(t){e.$set(e.form,"Key_Column",t)},expression:"form.Key_Column"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"缩略图",prop:"thumbnails"}},[a("upload",{ref:"img",attrs:{"before-upload":e.beforeUploadImg,"btn-text":e.imgName,limit:1,accept:".png,jpeg,jpg","btn-icon":"el-icon-upload","btn-size":"middle"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"是否默认",prop:"Is_Default"}},[a("el-checkbox",{model:{value:e.form.Is_Default,callback:function(t){e.$set(e.form,"Is_Default",t)},expression:"form.Is_Default"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"报表说明",prop:"Remark"}},[a("el-input",{model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"主报表数据源",prop:"Data_Url"}},[a("el-input",{attrs:{autosize:{minRows:4,maxRows:8},type:"textarea"},model:{value:e.form.Data_Url,callback:function(t){e.$set(e.form,"Data_Url",t)},expression:"form.Data_Url"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"子报表数据源",prop:"Data_Url_Ex01"}},[a("el-input",{attrs:{autosize:{minRows:4,maxRows:8},type:"textarea"},model:{value:e.form.Data_Url_Ex01,callback:function(t){e.$set(e.form,"Data_Url_Ex01",t)},expression:"form.Data_Url_Ex01"}})],1)],1)],1),a("el-form-item",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确定")])],1)],1)],1)},i=[]},"6c72":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"center"},[a("div",{staticClass:"img-x",attrs:{id:"printMe"}},[a("img",{staticClass:"cs-img",attrs:{src:e.src,alt:""}})]),a("el-button",{directives:[{name:"print",rawName:"v-print",value:e.printObj,expression:"printObj"}],attrs:{type:"primary"}},[e._v("打 印")])],1)},i=[]},"6f48c":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AllotProject=d,t.DeleteTemplate=c,t.GenerateReport=m,t.GetPrintTemplateInfoPageList=l,t.GetPrintTemplatePageList=o,t.GetProjectShuttleList=f,t.GetTemplatePageList=s,t.SaveTemplate=u;var i=n(a("b775")),r=n(a("4328"));function l(e){return(0,i.default)({url:"/PRO/PrintTemplate/GetPrintTemplateInfoPageList",method:"post",data:e})}function o(e){return(0,i.default)({url:"/PRO/PrintTemplate/GetPrintTemplatePageList",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/PrintTemplate/GetTemplatePageList",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/PrintTemplate/SaveTemplate",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/PrintTemplate/DeleteTemplate",method:"post",data:r.default.stringify(e)})}function f(e){return(0,i.default)({url:"/PRO/PrintTemplate/GetProjectShuttleList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,i.default)({url:"/PRO/PrintTemplate/AllotProject",method:"post",data:r.default.stringify(e)})}function m(e){return(0,i.default)({url:"/PRO/PrintTemplate/GenerateReport",method:"post",data:r.default.stringify(e),responseType:"blob"})}},"78c9":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"h100"},[a("div",{staticClass:"cx-wrapper"},[a("el-transfer",{staticClass:"cs-trans",attrs:{filterable:"",titles:["全部","已选项目"],"button-texts":["到左边","到右边"],props:{key:"Id",label:"Name"},format:{noChecked:"${total}",hasChecked:"${checked}/${total}"},data:e.data},on:{change:e.handleChange},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}})],1),a("div",{staticClass:"text-center"},[a("el-button",{staticClass:"cs-btn",on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{staticClass:"cs-btn",attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)])},i=[]},"891d":function(e,t,a){"use strict";a("2ad9")},"94ae":function(e,t,a){"use strict";a("fa9b")},a45e:function(e,t,a){"use strict";a.r(t);var n=a("3d08"),i=a("26df");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("94ae");var l=a("2877"),o=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"c6211414",null);t["default"]=o.exports},a62b:function(e,t,a){"use strict";a.r(t);var n=a("78c9"),i=a("d16f");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("2c01");var l=a("2877"),o=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"7f0718c6",null);t["default"]=o.exports},abea:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"card-wrapper"},[a("div",{staticClass:"top"},[a("i",{staticClass:"iconfont icon-qrcode txt-blue"}),a("strong",[e._v(e._s(e.item.Display_Name))])]),a("div",{staticClass:"btn"},[a("span",[a("el-button",{attrs:{icon:"el-icon-printer",size:"mini"},on:{click:function(t){return e.handlePrint(e.item)}}},[e._v("标签预览")]),a("el-button",{attrs:{disabled:"初始模板"===e.item.Display_Name,size:"mini"},on:{click:function(t){return e.handleProject(e.item)}}},[e._v("适用项目 "+e._s("初始模板"===e.item.Display_Name?"所有":e.item.Project_Count))])],1),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.handleDelete(e.item)}}},[e._v("删 除")])],1)])},i=[]},af34:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b0c0");var i=n(a("c7f0")),r=a("6f48c");t.default={components:{Upload:i.default},data:function(){return{form:{Display_Name:"",File_Name:"",Remark:"",Key_Column:"",Data_Url:"",Is_Default:!1,Data_Url_Ex01:"",Type:""},fileName:"选择文件",imgName:"选择图片",rules:{Display_Name:[{required:!0,message:"请输入",trigger:"blur"}],Type:[{required:!0,message:"请输入",trigger:"blur"}],File_Name:[{required:!0,message:"请输入",trigger:"blur"}]}}},created:function(){this.fileFormData=new FormData},methods:{beforeUploadFile:function(e){this.fileName=e.name,this.form.File_Name=e.name,this.fileFormData.set("templates",e),this.$refs.file.onSuccess()},beforeUploadImg:function(e){this.imgName=e.name,this.fileFormData.set("thumbnails",e),this.$refs.img.onSuccess()},handleSubmit:function(){var e=this;this.fileFormData.get("templates")&&this.fileFormData.get("thumbnails")?this.$refs["form"].validate((function(t){if(t){for(var a in e.form)e.fileFormData.append(a,e.form[a]);(0,r.SaveTemplate)(e.fileFormData).then((function(t){t.IsSucceed?(e.$refs.file.onSuccess(),e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})}))}})):this.$message({message:"请选择文件",type:"warning"})}}}},b8cf:function(e,t,a){"use strict";a.r(t);var n=a("abea"),i=a("29af");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("4103");var l=a("2877"),o=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"f450579e",null);t["default"]=o.exports},bbd6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("25f0");var i=n(a("2909")),r=a("6f48c");t.default={data:function(){this.$createElement;return{data:[],value:[],templateName:"",renderFunc:function(e,t){return e("span",[t.Display_Name])}}},methods:{init:function(e){var t=this;this.templateName=e.Display_Name,(0,r.GetProjectShuttleList)({templateName:this.templateName,search:""}).then((function(e){e.IsSucceed?(t.data=[].concat((0,i.default)(e.Data.ToAddList),(0,i.default)(e.Data.AddedList)),t.value=e.Data.AddedList.map((function(e){return e.Id}))):t.$message({message:e.Message,type:"error"})}))},handleSubmit:function(){var e=this;(0,r.AllotProject)({templateName:this.templateName,projectIds:this.value.toString()}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})}))},handleChange:function(e,t,a){}}}},d16f:function(e,t,a){"use strict";a.r(t);var n=a("bbd6"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},e4e0d:function(e,t,a){"use strict";a.r(t);var n=a("6645"),i=a("1345");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var l=a("2877"),o=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"555feb34",null);t["default"]=o.exports},eab5:function(e,t,a){"use strict";a.r(t);var n=a("430c"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},fa9b:function(e,t,a){}}]);