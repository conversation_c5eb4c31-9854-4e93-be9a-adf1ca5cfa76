(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-d3134992"],{"01a3":function(t,e,a){"use strict";a.r(e);var n=a("5f05"),r=a("7a2d");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("42d6"),a("62aa");var l=a("2877"),o=Object(l["a"])(r["default"],n["a"],n["b"],!1,null,"6f439183",null);e["default"]=o.exports},"03c3":function(t,e,a){},"056e":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-container",[a("el-header",{staticStyle:{height:"32px"}},[a("h3",{staticClass:"psd"},[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))])]),a("el-main",{staticStyle:{"padding-top":"12px"}},[a("el-table",{attrs:{border:"",stripe:"",data:t.data}},[a("el-table-column",{attrs:{prop:"create_username",label:"人员"}}),a("el-table-column",{attrs:{prop:"comp_count",label:"构件件数"}}),a("el-table-column",{attrs:{prop:"schduling_count",label:"已排产构件数"}})],1)],1),a("el-footer",{staticStyle:{height:"36px"},attrs:{align:"center"}},[a("el-button",{attrs:{size:"mini"},on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.confirmArrange}},[t._v("确定")])],1)],1)},r=[]},1375:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a15b"),a("d81d"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("498a"),a("159b"),a("ddb0");var r=a("ed08"),i=n(a("0f97")),l=n(a("7667d")),o=n(a("01a3")),s=a("6186"),c=a("1b69"),u=n(a("b775")),d=a("6f23"),f=a("2dd9"),p=n(a("4328")),b=window.open;e.default={components:{DynamicDataTable:i.default,PdScheDetail:l.default,QiTaoDetail:o.default},data:function(){return{apis:{GetCompletePercent:"/PRO/InstallUnit/GetCompletePercent",ExportPriorityAnalyse:"/PRO/OperationPlan/ExportPriorityAnalyse"},loading:!0,gridCode:"pro_priority_list",tbConfig:{},columns:[],data:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},projects:[],checkedRows:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0,SortName:"",SortOrder:""},tipDetails:[],priorityOption:[{key:100,value:[100,null],label:"已经延迟"},{key:70,value:[67,99],label:"紧急"},{key:35,value:[33,66],label:"刚好"},{key:24,value:[0,32],label:"不急"},{key:-10,value:[null,-1],label:"太早"}]}},created:function(){var t=this;Promise.all([(0,c.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))]).then((function(){(0,s.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData().then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1}))}))}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:160,Op_Label:"安装单元状态"}),this.filterData.PageSize=this.tbConfig.Row_Number,this.filterData.SortName=this.tbConfig.Sort_Column,this.filterData.SortOrder=this.tbConfig.Sort_Type},setCols:function(t){var e=this;t.forEach((function(t){"Project_Name"===t.Code&&(t.Range=JSON.stringify(e.projects.map((function(t){return{label:t.Name,value:t.Name}}))))})),this.columns=t},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},getTableData:function(){return this.tbConfig.Data_Url?(0,u.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData)}):Promise.reject("invalid data api...")},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},getPlanDetails:function(t,e){var a=this,n=2;"Demand_Content"===e.Code&&(n=2),"External_Details"===e.Code&&(n=1),(0,u.default)({url:this.apis.GetCompletePercent,method:"post",params:{installunitId:t.Id,type:n}}).then((function(t){t.IsSucceed?a.tipDetails=t.Data:a.$message.warning(String(t.Message||t.IsSucceed))}))},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.Page=1,this.filterData.PageSize=e,this.filterChange()},multiSelectedChange:function(t){this.checkedRows=t},filterChange:function(t){var e=this;this.filterData=Object.assign({},this.filterData,{Page:Number(t||1)}),this.filterData.ParameterJson=(0,f.setParameterJson)(Object.assign({},this.fiterArrObj,{Priority:this.fiterArrObj["Priority"]?this.fiterArrObj["Priority"].split(",").map((function(t){var e=t.trim()?Number(t.trim()):null;return isNaN(e)&&(e=null),e})):null}),this.columns),this.loading=!0,this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)})).finally((function(){e.loading=!1}))},exportExcel:function(){var t=this;(0,u.default)({url:this.apis.ExportPriorityAnalyse,method:"post",paramsSerializer:function(t){return p.default.stringify(t,{arrayFormat:"repeat"})},params:{ids:this.checkedRows.map((function(t){return t.Id}))}}).then((function(e){if(e.IsSucceed&&e.Data){var a=(0,r.combineURL)(t.$baseUrl,e.Data.split("/").map((function(t){return encodeURIComponent(t)})).join("/"));b(a)}}))},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=this,a=t.type;t.data;switch(a){case"reload":this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)}));break}this.dialogCancel()},customColorMethod:function(t){return t=Number(t),t<0?{b:"#BCE4FF",f:"#44AAEF"}:t>=0&&t<33?{b:"#57DB7A",f:"#008123"}:t>=33&&t<67?{b:"#FFD736",f:"#9C6800"}:t>=67&&t<100?{b:"#F13333",f:"#ffffff"}:t>=100?{b:"#3E0606",f:"#ffffff"}:{}},formatDate:function(t){return(0,d.formatDate)(t,"yyyy-M-d")}}}},"2dd9":function(t,e,a){"use strict";function n(t,e){t||(t={}),e||(e=[]);var a=[],n=function(n){var r;r="[object Array]"===Object.prototype.toString.call(t[n])?t[n]:[t[n]];var i=r.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(i.filter((function(t){return null!==t})).length<=0&&(i=null),i){var l={Key:n,Value:i,Type:"",Filter_Type:""},o=e.find((function(t){return t.Code===n}));l.Type=null===o||void 0===o?void 0:o.Type,l.Filter_Type=null===o||void 0===o?void 0:o.Filter_Type,a.push(l)}};for(var r in t)n(r);return a}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=n,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("25f0")},"330c":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var r=n(a("b775")),i=a("6f23"),l=(n(a("4328")),a("477a"));e.default={name:"QiTaoDetail",components:{},props:{unit:{type:Object,default:function(){return{}}}},data:function(){return{apis:{GetPrepare:"/PRO/OperationPlan/GetPrepare",GetMaterialPrepareDetail:"/PRO/OperationPlan/GetMaterialPrepareDetail",GetBillPrepareDetail:"/PRO/OperationPlan/GetBillPrepareDetail"},data:[],expanded:{}}},created:function(){this.getPrepare()},methods:{cancel:function(){this.$emit("dialogCancel")},confirmPrepare:function(){this.$emit("dialogFormSubmitSuccess",{type:"reload"})},getPrepare:function(){var t=this;(0,r.default)({url:this.apis.GetPrepare,method:"post",params:{installId:this.unit.Id}}).then((function(e){e.IsSucceed&&(t.data=e.Data)}))},expandChange:function(t,e){var a=e.find((function(e){return e.Id===t.Id}));a?10===t.Item?this.getFormList(t.InstallUnit_Id,t.Id):this.loadDetail(t.InstallUnit_Id,t.Id):(delete this.expanded[t.Id],this.expanded=Object.assign({},this.expanded))},loadDetail:function(t,e){var a=this;(0,r.default)({url:this.apis.GetMaterialPrepareDetail,method:"post",params:{installId:t}}).then((function(t){t.IsSucceed?a.expanded[e]=t.Data.Details:a.expanded[e]=[],a.expanded=Object.assign({},a.expanded)}))},getFormList:function(t,e){var a=this;(0,l.GetBillPrepareDetail)({installId:t}).then((function(t){t.IsSucceed?a.expanded[e]=t.Data.Details:a.expanded[e]=[],a.expanded=Object.assign({},a.expanded)}))},formatDate:function(t){return(0,i.formatDate)(t,"yyyy-M-d")}}}},"3dbb":function(t,e,a){"use strict";a.r(e);var n=a("1375"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"42d6":function(t,e,a){"use strict";a("92434")},"44fb":function(t,e,a){"use strict";a.r(e);var n=a("7e6f"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"477a":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetBillPrepareDetail=l,e.GetFactoryAlloctCount=o;var r=n(a("b775")),i=n(a("4328"));function l(t){return(0,r.default)({url:"/PRO/OperationPlan/GetBillPrepareDetail",method:"post",data:i.default.stringify(t)})}function o(t){return(0,r.default)({url:"/PRO/OperationPlan/GetFactoryAlloctCount",method:"post",data:i.default.stringify(t)})}},"4a0c":function(t,e,a){"use strict";a.r(e);var n=a("d9f83"),r=a("3dbb");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("517b"),a("e428");var l=a("2877"),o=Object(l["a"])(r["default"],n["a"],n["b"],!1,null,"07ac2c47",null);e["default"]=o.exports},"4e82":function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),i=a("59ed"),l=a("7b0b"),o=a("07fa"),s=a("083a"),c=a("577e"),u=a("d039"),d=a("addb"),f=a("a640"),p=a("3f7e"),b=a("99f4"),h=a("1212"),m=a("ea83"),g=[],y=r(g.sort),v=r(g.push),_=u((function(){g.sort(void 0)})),C=u((function(){g.sort(null)})),D=f("sort"),P=!u((function(){if(h)return h<70;if(!(p&&p>3)){if(b)return!0;if(m)return m<603;var t,e,a,n,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)g.push({k:e+n,v:a})}for(g.sort((function(t,e){return e.v-t.v})),n=0;n<g.length;n++)e=g[n].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),S=_||!C||!D||!P,x=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:S},{sort:function(t){void 0!==t&&i(t);var e=l(this);if(P)return void 0===t?y(e):y(e,t);var a,n,r=[],c=o(e);for(n=0;n<c;n++)n in e&&v(r,e[n]);d(r,x(t)),a=o(r),n=0;while(n<a)e[n]=r[n++];while(n<c)s(e,n++);return e}})},"517b":function(t,e,a){"use strict";a("f359")},"5f05":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-container",{staticClass:"qitao-detail"},[a("el-header",{staticStyle:{height:"32px"}},[a("h3",{staticClass:"psd"},[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))])]),a("el-main",{staticStyle:{"padding-top":"12px"}},[a("el-table",{attrs:{stripe:"",expand:"",data:t.data},on:{"expand-change":t.expandChange}},[a("el-table-column",{attrs:{type:"expand"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.expanded[e.row.Id]&&11===e.row.Item?a("el-table",{key:e.row.Id,attrs:{data:t.expanded[e.row.Id],"header-cell-style":{border:"none"},"cell-style":{border:"none"}}},[a("el-table-column",{attrs:{prop:"Is_Ready",label:"是否齐套"},scopedSlots:t._u([{key:"default",fn:function(t){return[(t.row.Is_Ready,a("el-link",{attrs:{type:"success",underline:!1}},[a("i",{staticClass:"el-icon-check",attrs:{type:"success"}})]))]}}],null,!0)}),a("el-table-column",{attrs:{prop:"name",label:"物料名称"}}),a("el-table-column",{attrs:{prop:"spec",label:"规格"}}),a("el-table-column",{attrs:{prop:"model",label:"材质/颜色/强度"}}),a("el-table-column",{attrs:{prop:"unit",label:"单位"}}),a("el-table-column",{attrs:{prop:"Number",label:"数量"}}),a("el-table-column",{attrs:{prop:"remark",label:"备注"}})],1):t._e(),t.expanded[e.row.Id]&&10===e.row.Item?a("el-table",{key:e.row.Id,attrs:{data:t.expanded[e.row.Id],"header-cell-style":{border:"none"},"cell-style":{border:"none"}}},[a("el-table-column",{attrs:{prop:"Is_Ready",label:"是否齐套"},scopedSlots:t._u([{key:"default",fn:function(t){return[(t.row.Is_Ready,a("el-link",{attrs:{type:"success",underline:!1}},[a("i",{staticClass:"el-icon-check",attrs:{type:"success"}})]))]}}],null,!0)}),a("el-table-column",{attrs:{prop:"File_Name",label:"文件名称"}}),a("el-table-column",{attrs:{prop:"Count",label:"构件数"}}),a("el-table-column",{attrs:{prop:"NetWeight",label:"重量"}}),a("el-table-column",{attrs:{prop:"Volume",label:"体积"}}),a("el-table-column",{attrs:{prop:"Create_Username",label:"上传人"}}),a("el-table-column",{attrs:{prop:"Create_Date",label:"上传时间"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Create_Date))+" ")]}}],null,!0)})],1):t._e()]}}])}),a("el-table-column",{attrs:{prop:"Item_Name",label:"齐套元素"}}),a("el-table-column",{attrs:{prop:"Percentage",label:"齐套状态"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.Percentage||0)+"%")])]}}])}),a("el-table-column",{attrs:{prop:"Finish_Time",label:"齐套时间"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[n["Is_Ready"]?a("span",[t._v(" "+t._s(t.formatDate(n.Finish_Time))+" ")]):a("span",[t._v("-")])]}}])})],1)],1),a("el-footer",{staticStyle:{height:"36px"},attrs:{align:"center"}},[a("el-button",{attrs:{size:"mini"},on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.confirmPrepare}},[t._v("确定")])],1)],1)},r=[]},"5fef":function(t,e,a){},"62aa":function(t,e,a){"use strict";a("5fef")},"7667d":function(t,e,a){"use strict";a.r(e);var n=a("056e"),r=a("44fb");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("a23b");var l=a("2877"),o=Object(l["a"])(r["default"],n["a"],n["b"],!1,null,"a12187a8",null);e["default"]=o.exports},"7a2d":function(t,e,a){"use strict";a.r(e);var n=a("330c"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"7e6f":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("b775"));n(a("4328")),e.default={name:"PdScheDetail",props:{unit:{type:Object,default:function(){return{}}}},data:function(){return{apis:{GetArrangeProdCount:"/PRO/OperationPlan/GetArrangeProdCount"},data:[]}},created:function(){this.getArrangeProdCount()},methods:{getArrangeProdCount:function(){var t=this;(0,r.default)({url:this.apis.GetArrangeProdCount,method:"post",params:{installId:this.unit.Id}}).then((function(e){e.IsSucceed&&(t.data=e.Data)}))},cancel:function(){this.$emit("dialogCancel")},confirmArrange:function(){this.$emit("dialogCancel")}}}},92434:function(t,e,a){},9373:function(t,e,a){},a23b:function(t,e,a){"use strict";a("03c3")},d9f83:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 flex-pd16-wrap"},[a("div",{staticClass:"page-main-content cs-z-shadow"},[a("el-container",{staticStyle:{height:"100%"}},[a("el-header",{staticStyle:{height:"24px","margin-bottom":"16px"}},[a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{disabled:t.checkedRows.length<=0,type:"success",size:"mini"},on:{click:t.exportExcel}},[t._v("导出")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{padding:"0"}},[t._e(),a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{multiSelectedChange:t.multiSelectedChange,tableSearch:t.tableSearch,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange},scopedSlots:t._u([{key:"hsearch_Priority",fn:function(e){var n=e.column;return[a("el-select",{key:n.Code,on:{focus:t.$refs.table.showSearch},model:{value:t.fiterArrObj[n.Code],callback:function(e){t.$set(t.fiterArrObj,n.Code,e)},expression:"fiterArrObj[column.Code]"}},[a("el-option",{attrs:{label:"所有",value:""}},[a("div",{staticClass:"tag-select-item"},[a("el-tag",{staticStyle:{"border-radius":"0",border:"none","text-align":"center",height:"28px","line-height":"28px"}},[t._v("所有")])],1)]),t._l(t.priorityOption,(function(e,n){return a("el-option",{key:n,attrs:{label:e.label,value:e.value.join(",")}},[a("div",{staticClass:"tag-select-item"},[a("el-tag",{style:{color:t.customColorMethod(e.key)["f"],borderRadius:"0",height:"28px",border:"none",textAlign:"center",lineHeight:"28px"},attrs:{hit:!1,color:t.customColorMethod(e.key)["b"]}},[t._v(t._s(e.label))])],1)])}))],2)]}},{key:"Priority",fn:function(e){var n=e.row,r=e.column;return[a("el-tag",{style:{width:"92%",color:t.customColorMethod(n[r.Code])["f"],borderRadius:"0",height:"28px",border:"none",lineHeight:"28px"},attrs:{color:t.customColorMethod(n[r.Code])["b"]}},[t._v(t._s(n[r.Code]||0)+"%")]),t._e()]}},t._l(["Demand_Content","Complete_Content","External_Details"],(function(e){return{key:"innertip-"+e,fn:function(n){var r=n.row,i=n.column,l=n.$index;return[r[i.Code]?a("el-popover",{key:e+"_"+l,attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.getPlanDetails(r,i)}}},[a("div",{staticClass:"tooltip-content"},[a("el-table",{attrs:{"header-cell-style":{backgroundColor:"#14234E",color:"#FFF"},data:t.tipDetails,"cell-style":{border:"none"}}},[a("el-table-column",{attrs:{prop:"component_type_name",label:"类别",width:"60"}}),a("el-table-column",{attrs:{prop:"total",label:"需求量",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row[e.column.property])+t._s(e.row["unit"])+" ")]}}],null,!0)}),a("el-table-column",{attrs:{prop:"complete_amount",label:"已完成",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row[e.column.property])+t._s(e.row["unit"])+" ")]}}],null,!0)}),a("el-table-column",{attrs:{prop:"percent",label:"完成率",width:"180"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("el-progress",{staticStyle:{"text-direction":"rtl"},attrs:{percentage:t.row[t.column.property]}})]}}],null,!0)})],1)],1),a("el-link",{staticStyle:{"margin-left":"2px"},attrs:{slot:"reference",type:"primary",underline:!1},slot:"reference"},[a("i",{staticClass:"el-icon-chat-line-square"})])],1):t._e()]}}}))],null,!0)})],1)],1)],1),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",attrs:{name:t.dialogCfgs.title},on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},r=[]},e428:function(t,e,a){"use strict";a("9373")},f359:function(t,e,a){}}]);