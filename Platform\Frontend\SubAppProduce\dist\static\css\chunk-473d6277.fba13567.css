.plan.common-wrapper{height:calc(100vh - 90px);padding:12px;display:-webkit-box;display:-ms-flexbox;display:flex}.plan.common-wrapper .square-icon{display:inline-block;width:16px;height:16px;overflow:hidden;text-align:center;line-height:16px;color:#fff;background:#666;margin-top:2px;border-radius:2px}.plan.common-wrapper .square-icon i{font-size:12px}.plan.common-wrapper .square-icon.cyan{background:#3ecc93}.plan.common-wrapper .square-icon.orange{background:#f5c15a}.plan.common-wrapper .square-icon.none{background:none!important;margin-bottom:-3px}.plan.common-wrapper .square-icon.none i{font-size:16px}.plan.common-wrapper .square-icon.none.orange{color:#ff6f30}.plan.common-wrapper .square-icon.none.green{color:#73d279}.plan.common-wrapper .square-icon.none.cyan{color:#61c9e9}.plan.common-wrapper .custom-tree-node{font-size:14px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;-webkit-box-flex:1;-ms-flex:auto;flex:auto;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.plan.common-wrapper .custom-tree-node .svg-icon{height:16px!important;width:16px!important}.plan.common-wrapper .custom-tree-node .ctx{-webkit-box-flex:1;-ms-flex:auto;flex:auto;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;flex-direction:row;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.plan.common-wrapper .custom-tree-node .ctx,.plan.common-wrapper .custom-tree-node .ctx .ops{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row}.plan.common-wrapper .custom-tree-node .ctx .ops{flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;margin-right:16px}.plan.common-wrapper .custom-tree-node .ctx .ops i{margin-left:6px}.plan.common-wrapper .state-dot{display:inline-block;width:6px;height:6px;border-radius:3px;background-color:grey;margin-right:6px;margin-top:-4px;font-size:0}.plan.common-wrapper>.el-card{width:100%;-webkit-box-shadow:none;box-shadow:none;border:none;background:none}.plan.common-wrapper>.el-card>.el-card__body{padding:16px}.plan.common-wrapper>.el-card>.el-card__body>.el-container{height:100%}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-header{padding:0}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-main{padding:0;-webkit-box-shadow:2px 2px 12px #eee;box-shadow:2px 2px 12px #eee;background:#fff;margin-left:20px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-ms-flex-flow:column;flex-flow:column}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-main .main-head{-ms-flex-negative:0;flex-shrink:0;overflow:hidden;margin:16px}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-main .main-table{height:0;-webkit-box-flex:1;-ms-flex:1;flex:1;margin:16px}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-main .main-table .lead{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;white-space:nowrap;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-main .main-table .lead .flag{width:24px;-ms-flex-negative:0;flex-shrink:0}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-aside{-webkit-box-shadow:2px 2px 12px #eee;box-shadow:2px 2px 12px #eee;background:#fff;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-ms-flex-flow:column;flex-flow:column}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-aside header{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;margin:16px;font-weight:700;-ms-flex-negative:0;flex-shrink:0}.plan.common-wrapper>.el-card>.el-card__body>.el-container>.el-aside .tree-scroll{height:0;-webkit-box-flex:1;-ms-flex:1;flex:1;overflow-y:auto}