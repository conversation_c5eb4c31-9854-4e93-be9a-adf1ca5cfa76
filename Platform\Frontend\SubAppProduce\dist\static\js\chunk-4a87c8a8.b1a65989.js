(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4a87c8a8"],{"17c1":function(t,e,n){"use strict";n.r(e);var a=n("f400"),o=n("6160");for(var c in o)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(c);var u=n("2877"),r=Object(u["a"])(o["default"],a["a"],a["b"],!1,null,"0e5b32d6",null);e["default"]=r.exports},6160:function(t,e,n){"use strict";n.r(e);var a=n("cffe"),o=n.n(a);for(var c in a)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(c);e["default"]=o.a},abfb:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportCancelStockInfo=m,e.GetInStockPageList=d,e.GetStockCancelDetailList=i,e.GetStockCancelDetailPageList=r,e.GetStockCancelDocEntity=l,e.GetStockCancelDocPageList=u,e.RemoveDetail=s,e.RemoveMain=p,e.SaveComponentStockCancel=f;var o=a(n("b775")),c=a(n("4328"));function u(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDocPageList",method:"post",data:t})}function r(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDetailPageList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDetailList",method:"post",data:c.default.stringify(t)})}function l(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDocEntity",method:"post",data:c.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetInStockPageList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/SaveComponentStockCancel",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/RemoveDetail",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/RemoveMain",method:"post",data:c.default.stringify(t)})}function m(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/ExportCancelStockInfo",method:"post",data:c.default.stringify(t)})}},cffe:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var o=a(n("8a9e")),c=n("82a3");e.default={name:"PROReturnGoodsEdit",components:{AddPage:o.default},data:function(){return{tbData:[],total:0}},computed:{name:function(){return this.$route.name}},methods:{fetchData:function(t){var e=this;(0,c.GetReturnDetailList)({billId:this.$route.query.id}).then((function(t){var n=t.IsSucceed,a=t.Data,o=t.Message;n?(e.tbData=a,e.total=a.length,e.$refs.main.showTable()):e.$message({message:o,type:"error"})}))}}}},f400:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("add-page",{ref:"main",attrs:{"is-edit":"","type-page":"goods",name:t.name,"tb-config-code":"pro_return_detail_list","tb-data":t.tbData,total:t.total},on:{getTbList:t.fetchData}})},o=[]}}]);