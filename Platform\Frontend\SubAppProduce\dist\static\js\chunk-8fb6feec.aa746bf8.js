(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-8fb6feec"],{"09f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,n,a){return e/=a/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,n){var i=r(),s=e-i,l=20,u=0;t="undefined"===typeof t?500:t;var d=function(){u+=l;var e=Math.easeInOutQuad(u,i,s,t);o(e),u<t?a(d):n&&"function"===typeof n&&n()};d()}},"15ac":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");t.default={methods:{getTableConfig:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:e,IsAll:n}).then((function(e){var a=e.IsSucceed,i=e.Data,s=e.Message;if(a){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit,a=e.type;this.queryInfo.Page="limit"===a?1:t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===t){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},16322:function(e,t,n){"use strict";n("6869")},"1b44":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("c14f")),r=a(n("1da1")),i=n("7015f");t.default={data:function(){return{title:"查看",dialogVisible:!1,tbLoading:!1,id:"",codes:"",type:"",Production_Finished_List:[],Production_UnFinished_List:[],tbData:[],columns:[{Code:"Working_Process_Name",Display_Name:"状态",Sort:10,Width:0,Align:"center",Is_Display:!0,fixed:null},{Code:"Num",Display_Name:"数量",Sort:20,Width:0,Align:"center",Is_Display:!0,fixed:null},{Code:"Total_Weight",Display_Name:"总重（kg）",Sort:30,Width:0,Align:"center",Is_Display:!0,fixed:null}]}},computed:{},watch:{},created:function(){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{handleOpen:function(e,t){this.id=e.Moc_Id_After,this.changeId=e.Change_Id,this.type=t,e.Component_Code&&(this.codes=e.Component_Code),e.Unit_Code&&(this.codes=e.Unit_Code),e.Part_Code&&(this.codes=e.Part_Code),this.dialogVisible=!0,this.tbLoading=!0,this.fetchData()},fetchData:function(){var e=this;(0,i.GetChangedComponentPartProductionList)({Id:this.id,Type:this.type,Change_Id:this.changeId}).then((function(t){t.IsSucceed?(e.Production_Finished_List=t.Data.Production_Finished_List,e.Production_UnFinished_List=t.Data.Production_UnFinished_List,e.tbLoading=!1):e.$message({message:t.Message,type:"error"})}))},handleClose:function(){this.dialogVisible=!1,this.$emit("close")}}}},3166:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=g,t.DeleteProject=d,t.GeAreaTrees=S,t.GetFileSync=I,t.GetInstallUnitIdNameList=v,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=O,t.GetProjectAreaTreeList=b,t.GetProjectEntity=l,t.GetProjectList=s,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=_,t.GetSchedulingPartList=T,t.IsEnableProjectMonomer=c,t.SaveProject=u,t.UpdateProjectTemplateBase=m,t.UpdateProjectTemplateContacts=C,t.UpdateProjectTemplateContract=P,t.UpdateProjectTemplateOther=y;var o=a(n("b775")),r=a(n("4328"));function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function I(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},6869:function(e,t,n){},"6d41":function(e,t,n){"use strict";n.r(t);var a=n("a88c"),o=n("9532");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("16322");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"d659d312",null);t["default"]=s.exports},"7015f":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=v,t.AgainSubmitChangeOrder=_,t.BatchReuseEngineeringContactChangedComponentPart=ie,t.BatchReuseEngineeringContactMocComponentPart=se,t.CancelChangeOrder=y,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=A,t.DeleteChangeOrder=c,t.DeleteChangeOrderV2=w,t.DeleteChangeReason=h,t.DeleteChangeType=s,t.DeleteEngineeringContactChangeOrder=J,t.DeleteMocOrder=Q,t.DeleteMocType=Pe,t.ExportEngineeringContactChangedAddComponentPart=de,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=oe,t.ExportMocAddComponentPart=ce,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=O,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=u,t.GetChangeOrderTaskInfo=G,t.GetChangeOrderTaskPageList=R,t.GetChangeOrderV2=E,t.GetChangeOrderV2PageList=N,t.GetChangeReason=g,t.GetChangeType=r,t.GetChangedComponentPartPageList=W,t.GetChangedComponentPartProductionList=V,t.GetCompAndPartSchdulingPageList=M,t.GetCompAndPartTaskList=L,t.GetCompanyUserPageList=D,t.GetEngineeringContactChangeOrder=H,t.GetEngineeringContactChangeOrderPageList=U,t.GetEngineeringContactChangedAddComponentPartPageList=le,t.GetEngineeringContactChangedAddComponentPartSummary=ue,t.GetEngineeringContactChangedComponentPartPageList=Y,t.GetEngineeringContactChangedSummary=X,t.GetEngineeringContactFileInfo=z,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=K,t.GetEngineeringContactMocSummary=ae,t.GetFactoryChangeTypeListV2=$,t.GetFactoryPeoplelist=l,t.GetMocAddComponentPartPageList=ge,t.GetMocModelList=_e,t.GetMocOrderInfo=me,t.GetMocOrderPageList=pe,t.GetMocOrderTypeList=Ce,t.GetMyChangeOrderPageList=P,t.GetProjectAreaChangeTreeList=B,t.GetProjectChangeOrderList=I,t.GetTypeReason=m,t.ImportChangFile=he,t.ImportChangeDeependFile=C,t.QueryHistories=S,t.ReuseEngineeringContactChangedComponentPart=ne,t.ReuseEngineeringContactMocComponentPart=re,t.SaveChangeOrder=d,t.SaveChangeOrderTask=F,t.SaveChangeOrderV2=j,t.SaveChangeReason=p,t.SaveChangeType=i,t.SaveEngineeringContactChangeOrder=q,t.SaveMocOrder=be,t.SaveMocOrderType=ye,t.SubmitChangeOrder=b,t.SubmitChangeOrderV2=x,t.SubmitMocOrder=k,t.Verification=T;var o=a(n("b775"));a(n("4328"));function r(e){return(0,o.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function S(e){return(0,o.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function O(e){return(0,o.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function D(e){return(0,o.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,o.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,o.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,o.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ne(e){return(0,o.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ae(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function oe(e){return(0,o.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function le(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function ue(e){return(0,o.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function de(e){return(0,o.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function ce(e){return(0,o.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,o.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function ge(e){return(0,o.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function pe(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function he(e){return(0,o.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function me(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Ce(e){return(0,o.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Pe(e){return(0,o.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function ye(e){return(0,o.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function _e(e){return(0,o.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function be(e){return(0,o.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},"946d":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.prefix=t.newPrefix=t.getFieldDisplayName=t.getCoreFieldsCode=t.getCoreFields=t.getAllCodesByType=t.generateAllCodes=t.filterByCodeType=t.defaultPrefix=t.changeTypeReverse=t.changeType=t.allCodes=t.addUnitPartContentCode=t.addPartContentCode=t.addComContentCode=void 0;var o=a(n("5530"));n("99af"),n("4de4"),n("7db0"),n("caad"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("d3b7"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("159b"),n("ddb0");var r={common:{SteelName:{Field_Type:"string",IsCoreField:!0,isMustInput:!0},SteelCode:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},SteelNumber:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},ComponentName:{Field_Type:"string",IsCoreField:!0,isMustInput:!0},PartName:{Field_Type:"string",IsCoreField:!0,isMustInput:!0},SteelSpec:{Field_Type:"string",IsCoreField:!0,isMustInput:!0},SteelLength:{Field_Type:"number",IsCoreField:!0,isMustInput:!0},SteelMaterial:{Field_Type:"string",IsCoreField:!0,isMustInput:!0},SteelAmount:{Field_Type:"number",precision:0,IsCoreField:!0,isMustInput:!0},GrossWeight:{Field_Type:"number",IsCoreField:!0,isMustInput:!1},SteelWeight:{Field_Type:"number",IsCoreField:!0,isMustInput:!0},Superficial_Area:{Field_Type:"number",IsCoreField:!1,isMustInput:!1},Area:{Field_Type:"number",IsCoreField:!1,isMustInput:!1},SteelType:{Field_Type:"select",IsCoreField:!1,isMustInput:!1},Remark:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Comp_Description:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},PartForm:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Paint_Code:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},PayCode:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Demand_Drawing_Length:{Field_Type:"number",IsCoreField:!1,isMustInput:!1},Technology_Code:{Field_Type:"select",IsCoreField:!1,isMustInput:!1},Hole_Number:{Field_Type:"number",precision:0,IsCoreField:!1,isMustInput:!1},Aperture:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Technology_Remark:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Texture_Replacement:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Spec_Replacement:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Margin:{Field_Type:"number",IsCoreField:!1,isMustInput:!1},EA_Number:{Field_Type:"number",IsCoreField:!1,isMustInput:!1},dxf_Url:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},ABM:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Layer:{Field_Type:"string",IsCoreField:!1,isMustInput:!1},Thick:{Field_Type:"number",IsCoreField:!1,isMustInput:!1},Width:{Field_Type:"number",IsCoreField:!1,isMustInput:!1},IsHeteroideus:{Field_Type:"string",IsCoreField:!1,isMustInput:!1}},typeSpecific:{1:{SteelAmount:{Display_Name:"深化数量"},Comp_Description:{CodeType:1}},2:{SteelAmount:{Display_Name:"单数"},ComponentName:{CodeType:2}},3:{SteelAmount:{Display_Name:"单数"},PartName:{CodeType:3}}}},i={1:["SteelName","SteelCode","SteelNumber","SteelSpec","SteelLength","SteelMaterial","SteelAmount","SteelWeight","GrossWeight","Superficial_Area","Area","Remark","Comp_Description","Paint_Code","PayCode","Demand_Drawing_Length","Technology_Code"],2:["ComponentName","SteelLength","SteelSpec","SteelMaterial","SteelAmount","GrossWeight","SteelWeight","Superficial_Area","EA_Number","Demand_Drawing_Length","Area","Remark","Paint_Code","PayCode","Technology_Code"],3:["PartName","SteelSpec","SteelLength","SteelMaterial","SteelAmount","GrossWeight","SteelWeight","Superficial_Area","Demand_Drawing_Length","Area","Remark","Paint_Code","PayCode","Technology_Code","Hole_Number","Aperture","Technology_Remark","Texture_Replacement","Spec_Replacement","Margin","PartForm","EA_Number","dxf_Url","ABM","Layer","Thick","Width","IsHeteroideus"]},s=t.allCodes=[],l=(t.generateAllCodes=function(e){var n=[],a=new Set;return[1,2,3].forEach((function(t){var s=i[t];s.forEach((function(i){var s=e.find((function(e){return e.Code===i}));if(s){var l="".concat(i,"_").concat(t);if(!a.has(l)){var u,d=r.common[i]||{},c=(null===(u=r.typeSpecific[t])||void 0===u?void 0:u[i])||{},f=(0,o.default)((0,o.default)({},c.CodeType&&{CodeType:c.CodeType}),{},{Code:i,CodeType:t,precision:"number"===typeof d.precision?d.precision:2,Display_Name:c.Display_Name||s.Display_Name,Field_Type:d.Field_Type||"string",IsCoreField:d.IsCoreField||!1,isMustInput:d.isMustInput||!1});n.push(f),a.add(l)}}}))})),t.allCodes=s=n,n},t.getCoreFields=function(e){var t=e.filter((function(e){return e.IsCoreField}));return t}),u=(t.getCoreFieldsCode=function(){var e=l(s);return e.map((function(e){return e.Code}))},t.getFieldDisplayName=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a={};return e.forEach((function(e){"SteelAmount"===e.Code?a[e.Code]=0===n?"深化数量":1===n||2===n?"单数":e.Display_Name:a[e.Code]=e.Display_Name})),"SteelAmount"===t?0===n?"深化数量":1===n||2===n?"单数":"数量":a[t]||t},t.addComContentCode=["SetupPosition","SteelName","SteelSpec","SteelMaterial","SteelLength","SteelWeight","SteelAllWeight","SteelAmount"],t.addUnitPartContentCode=["InstallUnit_Name","Code","Spec","Texture","Length","Weight","Total_Weight","Num"],t.addPartContentCode=["InstallUnit_Name","Code","Spec","Texture","Length","Weight","Total_Weight","Num"],t.changeType={isAdd:"新增",isIncrease:"数量增加",isDecrease:"数量减少",isDelete:"删除",isAdjust:"信息调整",isNoChange:"无变更"},t.changeTypeReverse={"新增":"isAdd","数量增加":"isIncrease","数量减少":"isDecrease","删除":"isDelete","信息调整":"isAdjust",isNoChange:"isNoChange"},t.getAllCodesByType=function(e){var t=s.filter((function(t){return!t.CodeType||t.CodeType===e}));return t},t.filterByCodeType=function(e){return s.filter((function(t){var n;return void 0!==t.CodeType?t.CodeType===e:null===(n=i[e])||void 0===n?void 0:n.includes(t.Code)}))},t.newPrefix=function(e){return"ynew"+e},t.prefix="ydefault");t.defaultPrefix=function(e){return u+e}},9532:function(e,t,n){"use strict";n.r(t);var a=n("e17a"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},a88c:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"page-container abs100"},[n("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"项目汇总",name:"1"}}),n("el-tab-pane",{attrs:{label:"构件",name:"构件"}}),n("el-tab-pane",{attrs:{label:"部件",name:"部件"}}),n("el-tab-pane",{attrs:{label:"零件",name:"零件"}})],1),"1"!==e.activeName?n("el-form",{ref:"form",staticClass:"cs-form",attrs:{inline:"",model:e.form,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return n("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),n("el-form-item",{attrs:{label:"当前状态",prop:"Reuse_Status"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Reuse_Status,callback:function(t){e.$set(e.form,"Reuse_Status",t)},expression:"form.Reuse_Status"}},[n("el-option",{attrs:{label:"复用",value:1}}),n("el-option",{attrs:{label:"无状态",value:0}})],1)],1),n("el-form-item",{attrs:{label:e.activeName+"名称"}},[n("el-input",{staticClass:"input-with-select",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.names,callback:function(t){e.names=t},expression:"names"}},[n("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.nameMode,callback:function(t){e.nameMode=t},expression:"nameMode"}},[n("el-option",{attrs:{label:"模糊搜索",value:1}}),n("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1),n("el-form-item",{attrs:{label:"变更联系单号",prop:"Bill_No"}},[n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No",t)},expression:"form.Bill_No"}})],1),n("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.mocType,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.fetchData(1)}}},[e._v("搜索")]),n("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1):e._e(),n("div",{staticClass:"cs-main"},["1"===e.activeName?n("div",{staticClass:"cs-button-box"},[n("el-form",{attrs:{inline:"","label-width":"80px"}},[n("el-form-item",{attrs:{label:"项目名称"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return n("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.fetchData(1)}}},[e._v("搜索")]),n("el-button",{on:{click:e.handleReset}},[e._v("重置")]),n("el-button",{attrs:{type:"success",loading:e.exportLoading},on:{click:e.handleExport}},[e._v("导出明细")])],1)],1)],1):n("div",{staticClass:"cs-button-box"},[n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:e.handAllReuse}},[e._v("批量复用")]),n("el-button",{attrs:{type:"success",loading:e.exportLoading},on:{click:e.handleExport}},[e._v("导出明细")])],1),n("div",{staticClass:"info-box"},[n("div",{staticClass:"cs-col"},[n("span",[n("span",{staticClass:"info-label"},[e._v("复用数量/重量")]),n("i",[e._v(e._s(e.info.ReuseCount)+" / "+e._s(e._f("filterNum")(e.info.ReuseWeight))+"t ")])]),n("span",[n("span",{staticClass:"info-label"},[e._v("总计数量/重量")]),n("i",[e._v(e._s(e.info.ChangeCount)+" / "+e._s(e._f("filterNum")(e.info.ChangeWeight))+"t ")])])])]),n("div",{staticClass:"fff tb-x"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"加载中","empty-text":"暂无数据",height:"auto",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.curType?n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}):e._e(),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,"show-overflow":"tooltip",sortable:"",align:"center",field:t.Code,title:t.Display_Name},scopedSlots:e._u(["ProductionStatus"===t.Code?{key:"default",fn:function(t){var a=t.row;return[n("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleOpen(a)}}},[e._v("查看")])]}}:"Change_Date"===t.Code?{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("timeFormat")(n.Change_Date))+" ")]}}:"Change_Type"===t.Code?{key:"default",fn:function(t){var a=t.row;return[n("span",{class:e.getChangeStyle(a.Change_Type)},[e._v(" "+e._s(a.Change_Type)+" ")])]}}:{key:"default",fn:function(a){var o=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})]})),e.curType?n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"100","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return["复用"!==a.Reuse_Status?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.showDialog(a)}}},[e._v("复用")]):e._e()]}}],null,!1,3480147197)}):e._e()],2)],1),n("div",{staticClass:"data-info"},[n("div",[e.curType?n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.multipleSelection.length)+" 条数据 ")]):e._e()],1),n("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)]),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"提示",visible:e.dialogVisible,width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t},close:function(t){e.dialogVisible=!1}}},[n("div",[n("span",[e._v("复用数量：")]),n("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"80%"},model:{value:e.reuseNum,callback:function(t){e.reuseNum=t},expression:"reuseNum"}})],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.reuseLoading},on:{click:e.handleReuse}},[e._v("确 定")])],1)]),n("StatusDialog",{ref:"statusDialog"})],1)},o=[]},b507:function(e,t,n){"use strict";n.r(t);var a=n("1b44"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},cd36:function(e,t,n){"use strict";n.r(t);var a=n("f6a9"),o=n("b507");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"72b60e32",null);t["default"]=s.exports},e17a:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("caad"),n("d81d"),n("14d9"),n("e9f5"),n("ab43"),n("d3b7"),n("ac1f"),n("2532"),n("5319");var o=a(n("5530")),r=a(n("15ac")),i=n("7015f"),s=n("c685"),l=n("3166"),u=a(n("333d")),d=a(n("6612")),c=n("ed08"),f=a(n("cd36")),g=n("946d");t.default={name:"PROComponentHandling",components:{StatusDialog:f.default,Pagination:u.default},filters:{filterNum:function(e){return(0,d.default)(e).divide(1e3).format("0.[00]")}},mixins:[r.default],data:function(){return{nameMode:1,names:"",show:!1,reuseLoading:!1,form:{Sys_Project_Id:"",Code_Like:"",Codes:"",Bill_No:"",Moc_Type_Id:"",Reuse_Status:""},info:{ReuseCount:0,ReuseWeight:0,ChangeCount:0,ChangeWeight:0},multipleSelection:[],reuseNum:void 0,activeName:"1",projectName:"",mocType:[],projectList:[],dialogVisible:!1,exportLoading:!1,tbLoading:!1,tbData:[],columns:[],tablePageSize:s.tablePageSize,queryInfo:{Page:1,PageSize:s.tablePageSize[0]},total:0}},computed:{subObj:function(){return(0,o.default)((0,o.default)({},this.form),{},{Type:this.curType})},curType:function(){return"1"===this.activeName?null:this.activeName}},watch:{names:function(e,t){this.changeMode()},nameMode:function(e,t){this.changeMode()}},mounted:function(){this.getTableConfig("PRoComponentTrackingConfig"),this.getBasicData(),this.fetchData(1),this.getSettingInfo()},methods:{changeMode:function(e){1===this.nameMode?(this.form.Code_Like=this.names,this.form.Codes=""):(this.form.Code_Like="",this.form.Codes=this.names.replace(/\s+/g,"\n"))},getSettingInfo:function(){var e=this;(0,i.GetMocOrderTypeList)({}).then((function(t){t.IsSucceed?e.mocType=t.Data:e.$message({message:t.Message,type:"error"})}))},handleClick:function(e,t){"1"===this.activeName?this.getTableConfig("PRoComponentTrackingConfig"):"构件"===this.activeName?this.getTableConfig("PRoComponentTrackingComConfig"):"部件"===this.activeName?this.getTableConfig("PRoCompTrackingUnitPartConfig"):"零件"===this.activeName&&this.getTableConfig("PRoComponentTrackingPartConfig"),this.handleReset()},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0,(0,i.GetEngineeringContactMocComponentPartPageList)((0,o.default)((0,o.default)({},this.subObj),this.queryInfo)).then((function(e){var n;e.IsSucceed?(t.tbData=(null===e||void 0===e||null===(n=e.Data)||void 0===n?void 0:n.Data)||[],t.total=e.Data.TotalCount,t.getTotalInfo()):t.$message({message:e.Message,type:"error"});t.tbLoading=!1}))},getTotalInfo:function(){var e=this;(0,i.GetEngineeringContactMocSummary)(this.subObj).then((function(t){if(t.IsSucceed){var n=t.Data,a=n.ReuseWeight,o=n.ChangeWeight,r=n.ReuseCount,i=n.ChangeCount;Object.assign(e.info,{ReuseCount:r,ReuseWeight:a,ChangeCount:i,ChangeWeight:o})}else e.$message({message:t.Message,type:"error"})}))},getBasicData:function(){var e=this;(0,l.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)}))},getChangeStyle:function(e){var t=["cs-c-box"];if(!e)return t;var n=e.split(",");return n.includes(g.changeType.isAdd)?t.push("cs-change-green"):n.includes(g.changeType.isAdjust)?t.push("cs-change-yellow"):n.includes(g.changeType.isDecrease)||n.includes(g.changeType.isIncrease)?t.push("cs-change-blue"):n.includes(g.changeType.isDelete)?t.push("cs-change-red"):t.push("cs-default"),t},handleReset:function(){var e;null===(e=this.$refs["form"])||void 0===e||e.resetFields(),this.names="",this.form.Sys_Project_Id="",this.form.Codes="",this.form.Code_Like="",this.fetchData(1)},handleExport:function(){var e=this;this.exportLoading=!0,(0,i.ExportEngineeringContactMocComponentPartPageList)(this.subObj).then((function(t){t.IsSucceed?window.open((0,c.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"}),e.exportLoading=!1}))},handleOpen:function(e){this.$refs["statusDialog"].handleOpen(e,this.activeName)},handleReuse:function(){var e=this;this.reuseNum?(this.reuseLoading=!0,(0,i.ReuseEngineeringContactMocComponentPart)({Id:this.curRow.Id,Num:this.reuseNum,Type:this.curType,Change_Order_Id:this.curRow.Change_Id}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"}),e.dialogVisible=!1,e.reuseLoading=!1}))):this.$message({message:"请选择数量",type:"warning"})},showDialog:function(e){this.curRow=e,this.reuseNum=void 0,this.dialogVisible=!0},handAllReuse:function(){var e=this;this.$confirm("是否确认全部复用?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=e.multipleSelection.map((function(t){return{Id:t.Id,Type:e.curType}}));(0,i.BatchReuseEngineeringContactMocComponentPart)(t).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},tbSelectChange:function(e){this.multipleSelection=e.records}}}},f6a9:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"bim-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"680px",top:"5vh"},on:{"update:visible":function(t){e.dialogVisible=t},cancelbtn:e.handleClose,handleClose:e.handleClose}},[n("div",{key:e.id,staticClass:"tb-container"},[n("div",{staticStyle:{"margin-bottom":"10px",color:"#333333"}},[e._v(e._s(e.codes)+"生产情况")]),n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","auto-resize":"",align:"left",stripe:"",data:e.Production_Finished_List,resizable:"","tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t,a){return n("vxe-column",{key:a,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,width:t.Width},scopedSlots:e._u([{key:"default",fn:function(a){var o=a.row;return[n("div",[n("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])])]}}],null,!0)})})),1),n("div",{staticStyle:{"margin-top":"30px","margin-bottom":"10px",color:"#333333"}},[e._v(e._s(e.codes)+"当前位置")]),n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","auto-resize":"",align:"left",stripe:"",data:e.Production_UnFinished_List,resizable:"","tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t,a){return n("vxe-column",{key:a,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,width:t.Width},scopedSlots:e._u([{key:"default",fn:function(a){var o=a.row;return[n("div",[n("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])])]}}],null,!0)})})),1)],1)])},o=[]}}]);