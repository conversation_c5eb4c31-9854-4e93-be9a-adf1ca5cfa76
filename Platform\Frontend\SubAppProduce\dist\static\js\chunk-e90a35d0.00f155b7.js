(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-e90a35d0"],{"13a1":function(e,t,a){"use strict";a.r(t);var n=a("8c28"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,s=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var u=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),u=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=u.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"17c4":function(e,t,a){"use strict";a.r(t);var n=a("4835"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"1dcf":function(e,t,a){"use strict";a("4101")},"2a703":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper h100",attrs:{"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中"}},[e.tbLoading?e._e():a("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1)},o=[]},4101:function(e,t,a){},"437f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("841c");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=a("a675"),u=n(a("15ac")),l=n(a("0f97"));t.default={components:{DynamicDataTable:l.default},mixins:[u.default],props:{pageInfo:{type:Object,default:function(){return{processId:"",teamId:"",currentType:void 0}}},search:{type:Array,default:function(){return[]}}},data:function(){return{tbLoading:!1,columns:[],tbData:[],tbConfig:{},total:0,queryInfo:{Page:1,PageSize:20},list:[]}},mounted:function(){this.init()},methods:{handleDetail:function(e){},init:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("comp_task_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,o.default)((0,o.default)({Process_Id:this.pageInfo.processId,Working_Team_Id:this.pageInfo.teamId,Comp_Codes:this.search},this.queryInfo),{},{Is_Page:!0});(0,s.GetReadyCompPageList)(t).then((function(t){var a=t.IsSucceed,n=t.Message,o=t.Data;a?(e.tbData=o.Data,e.total=o.TotalCount):e.$message({message:n,type:"error"}),e.tbLoading=!1}))}}}},4835:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("841c");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=a("a675"),u=n(a("15ac")),l=n(a("0f97"));t.default={components:{DynamicDataTable:l.default},mixins:[u.default],props:{pageInfo:{type:Object,default:function(){return{processId:"",teamId:"",currentType:void 0}}},search:{type:Array,default:function(){return[]}}},data:function(){return{tbLoading:!1,columns:[],tbData:[],tbConfig:{},total:0,queryInfo:{Page:1,PageSize:20},list:[]}},mounted:function(){this.init()},methods:{init:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("comp_task_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,o.default)((0,o.default)({Process_Id:this.pageInfo.processId,Working_Team_Id:this.pageInfo.teamId,Comp_Codes:this.search,InstallUnit_Id:""},this.queryInfo),{},{Status:[2],Check_Sign:[0,1]});(0,s.GetProductionCompPageList)(t).then((function(t){var a=t.IsSucceed,n=t.Message,o=t.Data;a?(e.tbData=null===o||void 0===o?void 0:o.Data,e.total=o.TotalCount):e.$message({message:n,type:"error"}),e.tbLoading=!1}))}}}},"4bd1":function(e,t,a){"use strict";a.r(t);var n=a("ac41"),o=a("13a1");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"31c4d84d",null);t["default"]=s.exports},"4e82":function(e,t,a){"use strict";var n=a("23e7"),o=a("e330"),r=a("59ed"),i=a("7b0b"),s=a("07fa"),u=a("083a"),l=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),h=a("3f7e"),g=a("99f4"),p=a("1212"),m=a("ea83"),b=[],P=o(b.sort),y=o(b.push),v=c((function(){b.sort(void 0)})),T=c((function(){b.sort(null)})),L=f("sort"),I=!c((function(){if(p)return p<70;if(!(h&&h>3)){if(g)return!0;if(m)return m<603;var e,t,a,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)b.push({k:t+n,v:a})}for(b.sort((function(e,t){return t.v-e.v})),n=0;n<b.length;n++)t=b[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),C=v||!T||!L||!I,G=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:l(t)>l(a)?1:-1}};n({target:"Array",proto:!0,forced:C},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(I)return void 0===e?P(t):P(t,e);var a,n,o=[],l=s(t);for(n=0;n<l;n++)n in t&&y(o,t[n]);d(o,G(e)),a=s(o),n=0;while(n<a)t[n]=o[n++];while(n<l)u(t,n++);return t}})},"776e":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper h100",attrs:{"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中"}},[e.tbLoading?e._e():a("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1)},o=[]},"7cef":function(e,t,a){"use strict";a.r(t);var n=a("d147"),o=a("a75e");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("1dcf");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"58406949",null);t["default"]=s.exports},"7ed7":function(e,t,a){"use strict";a.r(t);var n=a("437f"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"8c28":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("841c");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=a("a675"),u=n(a("15ac")),l=n(a("0f97"));t.default={components:{DynamicDataTable:l.default},mixins:[u.default],props:{pageInfo:{type:Object,default:function(){return{processId:"",teamId:"",currentType:void 0}}},search:{type:Array,default:function(){return[]}}},data:function(){return{tbLoading:!1,columns:[],tbData:[],tbConfig:{},total:0,queryInfo:{Page:1,PageSize:20},list:[]}},mounted:function(){this.init()},methods:{init:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("comp_task_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,o.default)((0,o.default)({},this.queryInfo),{},{Process_Id:this.pageInfo.processId,Working_Team_Id:this.pageInfo.teamId,Comp_Codes:this.search,InstallUnit_Id:"",Status:[2],Check_Sign:[-1,2]});(0,s.GetProductionCompPageList)(t).then((function(t){var a=t.IsSucceed,n=t.Message,o=t.Data;a?(e.tbData=null===o||void 0===o?void 0:o.Data,e.total=o.TotalCount):e.$message({message:n,type:"error"}),e.tbLoading=!1}))}}}},a024:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=l,t.AddProessLib=x,t.AddTechnology=u,t.AddWorkingProcess=s,t.DelLib=N,t.DeleteProcess=I,t.DeleteProcessFlow=T,t.DeleteTechnology=L,t.DeleteWorkingTeams=O,t.GetAllProcessList=f,t.GetCheckGroupList=A,t.GetChildComponentTypeList=W,t.GetFactoryAllProcessList=h,t.GetFactoryPeoplelist=S,t.GetFactoryWorkingTeam=b,t.GetGroupItemsList=v,t.GetLibList=i,t.GetLibListType=z,t.GetProcessFlow=g,t.GetProcessFlowListWithTechnology=p,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=k,t.GetProcessListWithUserBase=w,t.GetProcessWorkingTeamBase=q,t.GetTeamListByUser=j,t.GetTeamProcessList=y,t.GetWorkingTeam=P,t.GetWorkingTeamBase=R,t.GetWorkingTeamInfo=D,t.GetWorkingTeams=C,t.GetWorkingTeamsPageList=G,t.SaveWorkingTeams=_,t.UpdateProcessTeam=m;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function h(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function p(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function b(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function P(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function y(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function v(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function T(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function L(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function I(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function R(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function k(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function w(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a2642:function(e,t,a){"use strict";a.r(t);var n=a("776e"),o=a("17c4");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"ec30b8b6",null);t["default"]=s.exports},a675:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetAllocationAppMessage=i,t.GetAllocationLogPageList=r,t.GetProductionCompPageList=l,t.GetProductionCompProjectInstallPageList=s,t.GetProductionPartPageList=c,t.GetProductionProjectInstallPageList=u,t.GetReadyCompPageList=d;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PRO/ProductionPDA/GetAllocationLogPageList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/ProductionPDA/GetAllocationAppMessage",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionCompProjectInstallPageList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionProjectInstallPageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionCompPageList",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionPartPageList",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/ProductionPDA/GetReadyCompPageList",method:"post",data:e})}},a75e:function(e,t,a){"use strict";a.r(t);var n=a("e35b"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},ac41:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper h100",attrs:{"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中"}},[e.tbLoading?e._e():a("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1)},o=[]},b48c:function(e,t,a){"use strict";a.r(t);var n=a("2a703"),o=a("7ed7");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"2331ade4",null);t["default"]=s.exports},d147:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100  cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"工序班组"}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:e.optionsChange},model:{value:e.processTeam,callback:function(t){e.processTeam=t},expression:"processTeam"}},e._l(e.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"构件名称"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{resize:"vertical",placeholder:"换行查找多个",autosize:{minRows:3,maxRows:3},type:"textarea"},model:{value:e.SteelNames,callback:function(t){e.SteelNames=t},expression:"SteelNames"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")])],1)],1),a("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{lazy:"",label:"待生产",name:"second"}},["second"===e.activeName?a("pro",{ref:"second",attrs:{search:e.search,"page-info":e.pageInfo}}):e._e()],1),a("el-tab-pane",{attrs:{lazy:"",label:"待质检",name:"five"}},["five"===e.activeName?a("quality",{ref:"five",attrs:{search:e.search,"page-info":e.pageInfo}}):e._e()],1),a("el-tab-pane",{attrs:{lazy:"",label:"待转移",name:"third"}},["third"===e.activeName?a("un-transfer",{ref:"third",attrs:{search:e.search,"page-info":e.pageInfo}}):e._e()],1)],1)],1)])},o=[]},e35b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("ab43"),a("d3b7");var o=a("a024"),r=n(a("4bd1")),i=n(a("a2642")),s=n(a("b48c"));t.default={name:"PROTaskBoard",components:{unTransfer:r.default,pro:s.default,quality:i.default},data:function(){return{activeName:"second",options:[],processTeam:"",SteelNames:"",pageInfo:{processId:"",teamId:"",currentType:void 0}}},computed:{search:function(){return this.SteelNames.length>0?this.SteelNames.split("\n"):[]}},mounted:function(){this.getProcessTeam()},methods:{handleSearch:function(){this.$refs[this.activeName].fetchData()},setOption:function(e){var t=this.options.find((function(t){return t.value===e}));t&&(this.pageInfo.currentType=null===t||void 0===t?void 0:t.Type,this.pageInfo.processId=null===t||void 0===t?void 0:t.processId,this.pageInfo.teamId=null===t||void 0===t?void 0:t.teamId)},optionsChange:function(e){var t=this;this.setOption(e),this.$nextTick((function(e){t.$refs[t.activeName].fetchData()}))},getProcessTeam:function(){var e=this;(0,o.GetProcessListWithUserBase)({type:0}).then((function(t){e.options=t.Data.map((function(e){return{processId:e.Id,value:e.Id+e.Working_Team_Id,label:e.Name+"/"+e.Working_Team_Name,Type:e.Type,teamId:e.Working_Team_Id}})),e.options.length>0&&(e.processTeam=e.options[0].value,e.optionsChange(e.processTeam))}))},handleClick:function(e,t){}}}}}]);