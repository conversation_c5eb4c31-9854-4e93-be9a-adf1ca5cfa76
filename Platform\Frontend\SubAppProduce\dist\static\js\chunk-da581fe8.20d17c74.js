(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-da581fe8"],{"184d":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header"},[a("el-button",{on:{click:e.toBack}},[e._v("返回")])],1)]},proxy:!0},e.readonly?null:{key:"right",fn:function(){return[a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.handleSubmit(1)}}},[e._v("保存草稿")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.handleSubmit(2)}}},[e._v("提交")])]},proxy:!0}],null,!0)}),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"110px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货计划单号",prop:"Code"}},[a("el-input",{attrs:{disabled:!0,placeholder:"自动生成"},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"项目名称",prop:"projectName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.projectName,callback:function(t){e.$set(e.form,"projectName",t)},expression:"form.projectName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"装车人",prop:"Loader_UserId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.readonly,placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Loader_UserId,callback:function(t){e.$set(e.form,"Loader_UserId",t)},expression:"form.Loader_UserId"}},e._l(e.allUsers,(function(e){return a("el-option",{key:e.id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货人",prop:"Shipper_UserId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.readonly,placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Shipper_UserId,callback:function(t){e.$set(e.form,"Shipper_UserId",t)},expression:"form.Shipper_UserId"}},e._l(e.allUsers,(function(e){return a("el-option",{key:e.id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"计划发货日期",prop:"Plan_Date"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.readonly,placeholder:"请选择",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.Plan_Date,callback:function(t){e.$set(e.form,"Plan_Date",t)},expression:"form.Plan_Date"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"计划编制人",prop:"Plan_UserId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.readonly,placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Plan_UserId,callback:function(t){e.$set(e.form,"Plan_UserId",t)},expression:"form.Plan_UserId"}},e._l(e.allUsers,(function(e){return a("el-option",{key:e.id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{disabled:e.readonly,autosize:{minRows:2,maxRows:2},maxlength:500,"show-word-limit":"",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1)],1),a("div",{staticClass:"header"},[a("el-form",{attrs:{inline:"","label-width":"70px"}},[a("el-form-item",{attrs:{label:"构件名称"}},[a("el-input",{attrs:{clearable:""},model:{value:e.query.code,callback:function(t){e.$set(e.query,"code",t)},expression:"query.code"}})],1),a("el-form-item",{attrs:{label:"区域"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"treeselect",attrs:{"select-params":e.selectParams,"tree-params":e.treeParamsArea},on:{searchFun:function(t){return e.filterFun(t,"treeSelectArea")},"select-clear":e.areaClear},model:{value:e.query.area,callback:function(t){e.$set(e.query,"area",t)},expression:"query.area"}})],1),a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{attrs:{clearable:""},model:{value:e.query.status,callback:function(t){e.$set(e.query,"status",t)},expression:"query.status"}},[a("el-option",{attrs:{label:"未完成",value:"未完成"}}),a("el-option",{attrs:{label:"已完成",value:"已完成"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("搜索")])],1)],1),a("div",{staticClass:"right"},[a("div",{staticStyle:{"margin-right":"10px"}},[e._v("理论总重："+e._s(e.sumWeight)+"t")]),e.readonly?e._e():[a("el-button",{attrs:{disabled:!e.selectList.length,size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除")]),a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("添加")])]],2)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("vxe-table",{ref:"vxeTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.filterData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-change":e.checkboxChange,"checkbox-all":e.selectAllCheckboxChange}},[e.readonly?e._e():a("vxe-column",{attrs:{type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Plan_Count"!==t.Code||e.readonly?"Is_Direct"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",width:t.Width,align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(1==a.Is_Direct?"是":"否")+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]?r[t.Code]:"-")+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{field:t.Code,align:t.Align,title:t.Display_Name,sortable:"","edit-render":{},"min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var r=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1},on:{change:function(t){return e.sumItem(r)}},model:{value:r.Plan_Count,callback:function(t){e.$set(r,"Plan_Count",t)},expression:"row.Plan_Count"}})]}},{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Plan_Count)+" ")]}}],null,!0)})]}))],2)],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width,top:e.topDialog},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible,"project-id":e.projectId,"sys-project-id":e.form.Sys_Project_Id,"checked-data":e.tbData},on:{close:e.close,reCount:e.getTotal,selectList:e.addSelectList}})],1):e._e(),a("check-info",{ref:"info"})],1)])},n=[]},"1b65":function(e,t,a){"use strict";a.r(t);var r=a("95fd"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},"26e9":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),o=r(a("c14f")),i=r(a("1da1"));a("4de4"),a("7db0"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("ac1f"),a("2532"),a("841c"),a("159b");var l=r(a("7c56")),s=r(a("0f97")),c=r(a("34e9")),u=r(a("4b32")),d=a("ed08"),f=a("1b69"),m=a("6186"),p=r(a("6612")),h=r(a("f7f0")),g=a("eecc"),b=a("3166");t.default={name:"ShipPlanDetail",components:{TitleInfo:l.default,TopHeader:c.default,DynamicDataTable:s.default,CheckInfo:u.default,AddDialog:h.default},data:function(){return{query:{code:"",area:"",status:""},width:"80vw",currentComponent:"",title:"",dialogVisible:!1,topDialog:"5vh",sendNumber:"",totalNum:"",totalWeight:"",form:{Sys_Project_Id:"",Remark:"",Loader_UserId:"",Shipper_UserId:"",Plan_UserId:localStorage.getItem("UserId"),Plan_Date:"",Status:"",projectName:""},rules:{projectId:[{required:!0,message:"请选择项目",trigger:"change"}],Loader_UserId:[{required:!0,message:"请选择装车人",trigger:"change"}],Shipper_UserId:[{required:!0,message:"请选择发货人",trigger:"change"}],Plan_Date:[{required:!0,message:"请选计划发货日期",trigger:"change"}],Plan_UserId:[{required:!0,message:"请选计划编制人",trigger:"change"}]},PageInfo:{ParameterJson:[],Page:1,PageSize:20},plm_ProjectSendingInfo:{},Itemdetail:[],projects:"",Id:"",projectId:"",tbConfig:{Pager_Align:"center"},columns:[],tbData:[],total:0,tbLoading:!1,selectList:[],sums:[],allUsers:[],loading:!1,filterData:[],type:"view",selectParams:{placeholder:"请选择",clearable:!0},treeParamsArea:{"check-strictly":!0,"expand-on-click-node":!1,"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Label"}}}},computed:{sumWeight:function(){var e=0;return this.filterData.forEach((function(t){e+=Number(t.Total_Weight)})),(e/1e3).toFixed(5)},readonly:function(){return"view"===this.type}},watch:{},created:function(){this.getAllUsers(),this.getTableConfig()},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,r,n,i,l,s;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.type=e.$route.query.type||"add","add"===e.type?(a=JSON.parse(decodeURIComponent(e.$route.query.p)),r=a.Name,n=a.Id,i=a.Sys_Project_Id,e.projectId=n,e.form.projectName=r,e.form.Sys_Project_Id=i,e.getProjectEntity(e.projectId),e.getLastUser()):(l=JSON.parse(decodeURIComponent(e.$route.query.p)),s=l.Name,e.form.projectName=s,e.getInfo());case 1:return t.a(2)}}),t)})))()},methods:{sumItem:function(e){e.Total_Weight=(e.SteelWeight*e.Plan_Count).toFixed(2)},checkboxChange:function(){this.selectList=this.$refs.vxeTable.getCheckboxRecords()},selectAllCheckboxChange:function(){this.selectList=this.$refs.vxeTable.getCheckboxRecords()},filterFun:function(e,t){this.$refs[t].filterFun(e)},areaClear:function(){this.query.Area_Id=""},getAreaList:function(){var e=this;(0,b.GeAreaTrees)({sysProjectId:this.form.Sys_Project_Id}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},search:function(){var e=this;this.filterData=this.tbData.filter((function(t){return(t.Component_Code||"").includes(e.query.code)&&(t.FullAreaposition||"").includes(e.query.area)&&(t.Status||"").includes(e.query.status)}))},getLastUser:function(){var e=this;(0,g.GetLastUser)({Sys_Project_Id:this.form.Sys_Project_Id}).then((function(t){t.IsSucceed&&(e.form.Loader_UserId=t.Data.Loader_UserId,e.form.Shipper_UserId=t.Data.Shipper_UserId)}))},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Plan_Count"===t.field},handleSubmit:function(e){var t=this;return(0,i.default)((0,o.default)().m((function a(){var r,i,l;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.$refs["form"].validate();case 1:if(t.tbData&&t.tbData.length){a.n=2;break}return t.$message.error("请添加明细"),a.a(2);case 2:if(r=!1,t.tbData.forEach((function(e){e.Plan_Count<1&&(r=!0)})),!r){a.n=3;break}return t.$message.error("应发数量不能小于1"),a.a(2);case 3:return t.isClicked=!0,i={Main:(0,n.default)((0,n.default)({},t.form),{},{Status:e}),Details:t.tbData},a.n=4,(0,g.SaveOutPlan)(i);case 4:l=a.v,l.IsSucceed?(t.$message.success("保存成功"),t.toBack()):t.$message.error(l.Message);case 5:return a.a(2)}}),a)})))()},getNum:function(e,t){return(0,p.default)(e).subtract(t).format("0.[000]")},getProjectEntity:function(e){var t=this;(0,f.GetProjectEntity)({Id:e}).then((function(e){if(e.IsSucceed){var a=e.Data.Contacts.find((function(e){return"Consignee"==e.Type}));t.form.receiveName=null===a||void 0===a?void 0:a.Name,t.form.Receiver_Tel=null===a||void 0===a?void 0:a.Tel}}))},getProjectPageList:function(){var e=this;(0,f.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projects=t.Data.Data)}))},toBack:function(){(0,d.closeTagView)(this.$store,this.$route)},getInfo:function(){var e=this;(0,g.GetOutPlanEntity)({id:this.$route.query.id}).then((function(t){if(t.IsSucceed){var a=e.form.projectName;e.form=t.Data.Main,e.form.projectName=a,e.form.projectId=e.form.Sys_Project_Id,e.tbData=t.Data.Details.map((function(e){return e.Status=e.Accept_Count-e.Plan_Count>=0&&e.Accept_Count?"已完成":"未完成",e})),e.search(),e.getAreaList()}else e.$message({message:t.Message,type:"error"})}))},addSelectList:function(e){var t=this;e.forEach((function(e){e.Status="未完成",t.tbData.push(e)})),this.total=this.tbData.length,this.search()},getTotal:function(){},handleAdd:function(){this.currentComponent="AddDialog",this.dialogVisible=!0,this.title="新增"},handleDelete:function(){var e=this;this.$confirm("删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.selectList.forEach((function(t){var a=e.tbData.findIndex((function(e){return e.Component_Id===t.Component_Id}));-1!==a&&e.tbData.splice(a,1)})),e.search(),e.$message({type:"success",message:"删除成功!"})})).catch((function(){}))},close:function(){this.dialogVisible=!1},handleInfo:function(e){this.$refs.info.handleOpen(e)},getTableConfig:function(){var e=this;return new Promise((function(t){(0,m.GetGridByCode)({code:"PROShipPlanDetail"}).then((function(a){var r=a.IsSucceed,n=a.Data,o=a.Message;if(r){if(!n)return void e.$message({message:"表格配置不存在",type:"error"});e.tbConfig=Object.assign({},e.tbConfig,n.Grid),e.columns=n.ColumnList.map((function(e){return e.Is_Resizable=!0,e})),e.PageInfo.PageSize=+n.Grid.Row_Number,t(e.columns)}else e.$message({message:o,type:"error"})}))}))},getAllUsers:function(){var e=this;(0,m.GetAllUserPage)().then((function(t){e.allUsers=t.Data.Data}))}}}},"302e":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"70px"}},[a("el-form-item",{attrs:{label:"构件名称",prop:"Component_Code"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Component_Code,callback:function(t){e.$set(e.form,"Component_Code",t)},expression:"form.Component_Code"}})],1),a("el-form-item",{attrs:{label:"区域","label-width":"45px",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"treeselect",attrs:{"select-params":e.selectParams,"tree-params":e.treeParamsArea},on:{searchFun:function(t){return e.filterFun(t,"treeSelectArea")},"select-clear":e.areaClear},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(){e.form.Page=1,e.fetchData()}}},[e._v("查询 ")]),a("el-button",{on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")])],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticStyle:{height:"600px",width:"100%"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.form.Page,"sum-values":e.sums,"select-width":70,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.multiSelectedChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange}})],1),a("span",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("添 加")])],1)],1)},n=[]},3166:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=u,t.GeAreaTrees=I,t.GetFileSync=D,t.GetInstallUnitIdNameList=C,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=k,t.GetProjectAreaTreeList=y,t.GetProjectEntity=s,t.GetProjectList=l,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=_,t.GetSchedulingPartList=j,t.IsEnableProjectMonomer=d,t.SaveProject=c,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=b,t.UpdateProjectTemplateContract=P,t.UpdateProjectTemplateOther=v;var n=r(a("b775")),o=r(a("4328"));function i(e){return(0,n.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function D(e){return(0,n.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"3f35":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportPackingList=f,t.GeneratePackCode=i,t.GetPacking2ndEntity=s,t.GetPacking2ndPageList=l,t.GetPackingGroupByDirectDetailList=o,t.GetWaitPack2ndPageList=c,t.SavePacking2nd=d,t.UnzipPacking2nd=u;var n=r(a("b775"));r(a("4328"));function o(e){return(0,n.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:e})}function i(){return(0,n.default)({url:"/PRO/Packing/GeneratePackCode",method:"post"})}function l(e){return(0,n.default)({url:"/PRO/Packing/GetPacking2ndPageList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Packing/GetPacking2ndEntity",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Packing/GetWaitPack2ndPageList",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Packing/UnzipPacking2nd",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Packing/SavePacking2nd",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Packing/ExportPackingList",method:"post",data:e})}},"40d1":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a("3f35");t.default={data:function(){return{innerTableData:[],dialogVisible:!1}},methods:{handleInfo:function(e){var t=this;(0,r.GetPackingGroupByDirectDetailList)({Unique_Code:e.Unique_Code}).then((function(e){e.IsSucceed?t.innerTableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleOpen:function(e){this.dialogVisible=!0,this.handleInfo(e)},handleClose:function(){}}}},"4b32":function(e,t,a){"use strict";a.r(t);var r=a("a729"),n=a("939e");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"784abcb7",null);t["default"]=l.exports},"50e57":function(e,t,a){"use strict";a("5e92")},"5e92":function(e,t,a){},"70de":function(e,t,a){"use strict";a.r(t);var r=a("d520"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},"7c56":function(e,t,a){"use strict";a.r(t);var r=a("abe7"),n=a("1b65");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("f5f7");var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"3eff9b38",null);t["default"]=l.exports},"82a1":function(e,t,a){"use strict";a("a4d6")},"939e":function(e,t,a){"use strict";a.r(t);var r=a("40d1"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},"95fd":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{title:{type:String,default:""}},data:function(){return{}}}},a4d6:function(e,t,a){},a729:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"提示","append-to-body":"",visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-table",{staticClass:"inner-tb",staticStyle:{width:"100%"},attrs:{data:e.innerTableData}},[a("el-table-column",{attrs:{align:"center",prop:"InstallUnit_Name",label:"生产单元"}}),a("el-table-column",{attrs:{align:"center",prop:"Component_Code",label:"编号",width:"180"}}),a("el-table-column",{attrs:{align:"center",prop:"Unique_Code",label:"唯一码"}}),a("el-table-column",{attrs:{align:"center",prop:"Spec",label:"规格型号"}}),a("el-table-column",{attrs:{align:"center",prop:"Num",label:"数量"}}),a("el-table-column",{attrs:{align:"center",prop:"NetWeight",label:"总重（kg）"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)],1)},n=[]},a79b:function(e,t,a){"use strict";a.r(t);var r=a("184d"),n=a("e6a5");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("82a1");var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"f0a0943a",null);t["default"]=l.exports},abe7:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"title-box"},[a("div",{staticClass:"title"},[e._v(" "+e._s(e.title)+" ")]),a("div",[e._t("default")],2)])},n=[]},b46e:function(e,t,a){},d520:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var n=r(a("5530")),o=a("eecc"),i=r(a("0f97")),l=a("6186"),s=a("3166");t.default={name:"Add",components:{DynamicDataTable:i.default},props:{sysProjectId:{type:String,default:""},checkedData:{type:String,default:function(){return[]}}},data:function(){return{tbLoading:!1,columns:[],tbConfig:{Pager_Align:"center"},tbData:[],form:{Component_Code:"",Area_Id:"",Page:1,PageSize:20},sums:[],total:0,selectList:[],selectParams:{placeholder:"请选择",clearable:!0},treeParamsArea:{"check-strictly":!0,"expand-on-click-node":!1,"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}}}},mounted:function(){this.getAreaList(),this.getTableConfig(),this.fetchData()},methods:{handleSubmit:function(){this.$emit("selectList",this.selectList),this.$emit("close"),this.$emit("reCount")},filterFun:function(e,t){this.$refs[t].filterFun(e)},getAreaList:function(){var e=this;(0,s.GeAreaTrees)({sysProjectId:this.sysProjectId}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},areaClear:function(){this.form.Area_Id=""},fetchData:function(){var e=this;this.tbLoading=!0,(0,o.GetWaitingOutPlanComponentPageList)((0,n.default)((0,n.default)({},this.form),{},{Sys_Project_Id:this.sysProjectId,Component_Id:this.checkedData.map((function(e){return e.Component_Id}))})).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.tbLoading=!1):e.$message({message:t.Message,type:"error"})}))},getTableConfig:function(){var e=this;return new Promise((function(t){(0,l.GetGridByCode)({code:"PROShipPlanDocuments"}).then((function(a){var r=a.IsSucceed,n=a.Data,o=a.Message;if(r){if(!n)return void e.$message({message:"表格配置不存在",type:"error"});e.tbConfig=Object.assign({},e.tbConfig,n.Grid),e.columns=n.ColumnList.map((function(e){return e.Is_Resizable=!0,e})),e.form.PageSize=+n.Grid.Row_Number,t(e.columns)}else e.$message({message:o,type:"error"})}))}))},multiSelectedChange:function(e){this.selectList=e},resetForm:function(e){this.form.Page=1,this.$refs[e].resetFields(),this.fetchData()},handlePageChange:function(e){this.pageInfo?this.pageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.pageInfo?(this.pageInfo.Page=1,this.pageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},e6a5:function(e,t,a){"use strict";a.r(t);var r=a("26e9"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},f5f7:function(e,t,a){"use strict";a("b46e")},f7f0:function(e,t,a){"use strict";a.r(t);var r=a("302e"),n=a("70de");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("50e57");var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"6d0f4388",null);t["default"]=l.exports}}]);