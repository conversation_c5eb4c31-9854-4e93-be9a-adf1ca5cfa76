(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-21c1f738"],{"04a6":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAblJREFUOE+tlV1PwyAUhk8tFYQuqNuVyZzO6ZXGRP//v9ArpybqjcboWNFCV1ZzlnZZallnIneFw9Pz+RJA8woAYI8x1iGEdMIwpGjmnLN5nifGmCkATACgqF/Hi/Ul4zjuVxDPDxdwrfUTACB8uVaBAWPskFLa80Ga9rMse0vT9Lk6WwIZY4O/wipIlmWvaZq+4HcF7Eopj/7iWd1WKTUGAIXAQAhxQQiJ1gHn8/lXkiRjxlifUrpft3XOGa31LQJ7UsrBOpgxJrHWogc7QohTQkjYZI9eBpTSEWNM+oDYIiVMCCFGPhjet9a+Y7iXhBDSBLTWfhpjHgGgwzk/iaJoa10kzrnvQEp53WRkjJlYa+8BQHLOh20wZOR57gLO+VUURb8avCgKNZ1OMW8bA2ezWRHEcXzumwpjjCq9bM1fNZo4HUNK6d6aomCFMfTtssLe9rLWfmCo+1LK45Zkp1rrOwDYEkKcEUK2PW3zsHFjl2KAjX3QFFF5flMVo9XLtrFUSmFaJv8lDkvFqcsXKk63zZvVc698rRjtCiH6vsRXdqUYoMAmPoFd3ccRWzwBYRjiM7Co6iZPwA9WatnjrtOKmgAAAABJRU5ErkJggg=="},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=o,Math.easeInOutQuad=function(e,t,a,r){return e/=r/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=i(),l=e-o,s=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=s;var e=Math.easeInOutQuad(u,o,l,t);n(e),u<t?r(c):a&&"function"===typeof a&&a()};c()}},"11a3":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog-wapper"},[a("div",{staticStyle:{"padding-bottom":"10px",color:"#333"}},[e._v(e._s(e.row.Raw_Name))]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{height:"0",flex:"1"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.currentColumns,data:e.tbData,border:"",stripe:""},scopedSlots:e._u([{key:"Tax_All_Price",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Tax_All_Price||0===r.Tax_All_Price?r.Tax_All_Price.toFixed(2):"-"))])]}},{key:"Furnace_Batch_No",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Furnace_Batch_No||"-"))])]}},{key:"op",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleMove(r)}}},[e._v("移库")])]}},{key:"SurplusImgUrl",fn:function(t){var r=t.row;return[r.SurplusImgUrl?a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.download(r.SurplusImgUrl)}}},[e._v("下载")]):a("span",[e._v("-")])]}}])})],1),e.dialogVisible?a("yiku",{attrs:{id:e.rowId,"cur-type":1,visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t},updateInfo:e.updateInfo}}):e._e()],1)},n=[]},"15a2":function(e,t,a){"use strict";a.r(t);var r=a("e821"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186"),n=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,r.GetGridByCode)({code:e,IsAll:a}).then((function(e){var r=e.IsSucceed,o=e.Data,l=e.Message;if(r){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});var s=[];t.tbConfig=Object.assign({},t.tbConfig,o.Grid),s=a?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=s.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+o.Grid.Row_Number||n.tablePageSize[0]),i(t.columns)}else t.$message({message:l,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,r=e.type;this.queryInfo.Page="limit"===r?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1b14":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9f5"),a("7d54"),a("d3b7"),a("159b");var n=a("3c4a"),i=r(a("bc29")),o=r(a("40c9")),l=r(a("74fe")),s=r(a("d51d"));t.default={name:"PROMaterialInventory",components:{StatisticsTb:s.default,inventory:l.default},mixins:[i.default,o.default],data:function(){return{activeName:"1",summaryList:[],summtypeId:"",activeTab:"stock"}},computed:{stockTab:function(){return"stock"===this.activeTab}},mounted:function(){this.SummaryList()},methods:{SummaryList:function(){var e=this;(0,n.GetRawWHSummaryList)({}).then((function(t){if(t.IsSucceed){var a=0,r=0;t.Data.forEach((function(e){a+=e.Weight,r+=e.Money})),t.Data.unshift({Warehouse_Id:"",Warehouse_Name:"工厂总库存",Weight:a,Money:r}),e.summaryList=t.Data}else e.$message.error(t.Message)}))},handleClick:function(e){this.summtypeId=e.Warehouse_Id,this.$refs.inventoryRef.warehoust(e.Warehouse_Id)},scrollLeft:function(){document.querySelector(".containerto").scrollBy({left:-250,behavior:"smooth"})},scrollRight:function(){document.querySelector(".containerto").scrollBy({left:250,behavior:"smooth"})}}}},"2bb7":function(e,t,a){},"34c4":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1")),o=a("4744"),l=a("cf45"),s=a("ed08");t.default={name:"SelectProjectStatus",props:{value:{type:[Array,String],default:""},multiple:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1}},data:function(){return{list:[],selectedValue:""}},watch:{value:function(){this.selectedValue=Array.isArray(this.value)?(0,s.deepClone)(this.value):this.value}},created:function(){this.getList()},methods:{handleChange:function(){this.$emit("input",this.selectedValue),this.$emit("change",this.selectedValue)},getList:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:(0,o.GetPreferenceSettingValue)({code:"Productweight"}).then((function(t){(0,l.getDictionary)("true"===t.Data?"Engineering Status":"project_status").then((function(t){e.list=t}))}));case 1:return t.a(2)}}),t)})))()}}}},4065:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1")),o=r(a("5530"));a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("a9e3"),a("b680"),a("d3b7");var l=a("c685"),s=r(a("15ac")),u=r(a("333d")),c=a("8378"),d=r(a("a657")),f=a("ed08"),m=r(a("bad9"));a("90d1"),t.default={components:{SelectProject:m.default,DynamicTableFields:d.default,Pagination:u.default},mixins:[s.default],props:{otherQuery:{type:Object,default:function(){return{}}},showSearch:{type:Boolean,default:!1}},data:function(){return{tbLoading:!1,tbData:[],columns:[],tablePageSize:l.tablePageSize,totalNum:0,queryInfo:{Page:1,PageSize:l.tablePageSize[0]},form:{DateRange:[],MaterielName:"",MaterielFullName:"",Spec:"",SysProjectId:""},gridCode:"PRORawStatisticsTb",showTable:!0,materialType:0,loading1:!1,loading2:!1,loading3:!1}},watch:{"form.DateRange":function(e){this.form.BeginDate=e&&e.length&&e[0],this.form.EndDate=e&&e.length&&e[1]}},mounted:function(){this.getTableConfig(this.gridCode),this.fetchData()},methods:{footerMethod:function(e){var t=this,a=e.columns,r=e.data,n=[a.map((function(e,a){return["InQuantity","BeginStoreAmount","InWeight","InAmount","OutQuantity","OutAmount","OutWeight","EndStoreAmount","EndStore","EndStoreWeight"].includes(e.field)?t.sumNum(r,e.field,5):1===a?"当页合计":null}))];return n},sumNum:function(e,t,a){for(var r=0,n=0;n<e.length;n++)r+=Number(e[n][t])||0;return r.toFixed(a)/1},exportExcel:function(){var e=this;this.loading1=!0,(0,c.ExportFindRawInAndOut)((0,o.default)((0,o.default)({Type:1},this.otherQuery),this.form)).then((function(t){window.open((0,f.combineURL)(e.$baseUrl,t.Data))})).finally((function(){e.loading1=!1}))},exportRecSendProjectReport:function(){var e=this;this.loading2=!0,(0,c.ExportRecSendProjectReport)((0,o.default)((0,o.default)({materialType:this.materialType},this.otherQuery),this.form)).then((function(t){window.open((0,f.combineURL)(e.$baseUrl,t.Data))})).finally((function(){e.loading2=!1}))},exportExportRecSendProjectMaterialReport:function(){var e=this;this.loading3=!0,(0,c.ExportRecSendProjectMaterialReport)((0,o.default)((0,o.default)({materialType:this.materialType},this.otherQuery),this.form)).then((function(t){window.open((0,f.combineURL)(e.$baseUrl,t.Data))})).finally((function(){e.loading3=!1}))},updateColumn:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.showTable=!1,t.n=1,e.getTableConfig(e.gridCode);case 1:e.showTable=!0;case 2:return t.a(2)}}),t)})))()},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0,(0,c.FindInAndOutPageList)((0,o.default)((0,o.default)((0,o.default)({},this.queryInfo),{},{Type:1},this.otherQuery),this.form)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.totalNum=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))}}}},4120:function(e,t,a){"use strict";a.r(t);var r=a("34c4"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},4753:function(e,t,a){},"524d":function(e,t,a){"use strict";a("91ed")},"52b3":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAWpJREFUOE+tlLFOwlAUhv/TEsOACYODiQ7wBo6u7m5iERcdJUUjLe7MSotRGhh1Eam4ubv6FjJo4sBAIgMxtMeUiFBCWyjc9fz3u985ufcSlrxoFl6p9LDm5AqFw3ZQPhCo3Tb2IKA2ANk4UU/Tz35QT6BjJUQjVRBSLgCjaff6WS/bqUC90jgC6BqE+FQbRgfgcyWXvp+su4B61dxgCzUi7AbNyqkz44VEnChZ6XOY/wcGWnmdMGFL81p5cv9s6dIwt8i24qIgvs7SplfGsq0dFsTOqGXD5EWAiiwNWNOBjA8Q+r4HMCIgbA4zvkD6sZL5fKblByyX6wleEd/nAuqVRh2gdTeYv5RcOhMOaJiOQWLCtKXIUjIUULt53LaJo+NAgamnnh28hQIufYaa8XQF8ODrGi1qq/L+RShDfZEZaoZ5PLT4BppFWeqWDDNFQGzcj4FuQZaaRcOMrWL0tamydOe62Iu8kvG9v7s0zfh1+IqBAAAAAElFTkSuQmCC"},"52c7":function(e,t,a){"use strict";a.r(t);var r=a("fe1e"),n=a("8af9");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"7caf5844",null);t["default"]=l.exports},"540e":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("b680"),a("d3b7");var n=r(a("c14f")),i=r(a("1da1")),o=r(a("5f52")),l=r(a("0f97")),s=a("3c4a"),u=a("ed08"),c=r(a("52c7")),d=a("6186");t.default={components:{DynamicDataTable:l.default,yiku:c.default},mixins:[o.default],data:function(){return{dialogVisible:!1,pgLoading:!1,tbConfig:{},columns:[],currentColumns:[],tbData:[],flag:1,ProjectId:"",rowId:"",row:{}}},methods:{download:function(e){(0,d.GetOssUrl)({url:e}).then((function(e){window.open(e.Data)}))},handleMove:function(e){this.rowId=e.Store_Sub_Id,this.dialogVisible=!0},updateInfo:function(){this.fetchData(this.row),this.$emit("refresh")},init:function(e){var t=this;return(0,i.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return t.row=e,a.n=1,t.getTableConfig("pro_Instock_quantity");case 1:return t.currentColumns=t.columns.map((function(e){return["AdjustAmount","Tax_All_Price","PickingLockCount","In_Store_Count","In_Store_Weight"].includes(e.Code)&&(e.SummaryMethod="sum"),e})),a.n=2,t.fetchData(e);case 2:return a.a(2)}}),a)})))()},fetchData:function(e){var t=this;return(0,i.default)((0,n.default)().m((function a(){var r;return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return r={Store_Id:e.Store_Id,Sys_Project_Id:e.Sys_Project_Id},a.n=1,(0,s.GetRawForProjectDetail)(r).then((function(e){e.IsSucceed?t.tbData=e.Data.map((function(e){return e.In_Store_Date=e.In_Store_Date?(0,u.parseTime)(new Date(e.In_Store_Date),"{y}-{m}-{d}"):"",e.In_Store_Weight=e.In_Store_Weight||0===e.In_Store_Weight?(e.In_Store_Weight/1e3).toFixed(5)/1:"-",e.Tax_Unit_Price=e.Tax_Unit_Price||0===e.Tax_Unit_Price?(1e3*e.Tax_Unit_Price).toFixed(2)/1:"-",e.NoTax_Unit_Price=e.NoTax_Unit_Price||0===e.NoTax_Unit_Price?(1e3*e.NoTax_Unit_Price).toFixed(2)/1:"-",e.In_Store_Count=e.In_Store_Count||0===e.In_Store_Count?e.In_Store_Count/1:"-",e})):t.$message.error(e.Message)}));case 1:return a.a(2)}}),a)})))()}}}},"5f52":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("dca8"),a("d3b7");var n=r(a("c14f")),i=r(a("1da1")),o=a("6186"),l=a("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,i.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getTypeList();case 1:return a.n=2,t.getTable(e);case 2:return a.a(2)}}),a)})))()},getTypeList:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var r,n=null===(r=a[0])||void 0===r?void 0:r.Id;e.Code=a.find((function(e){return e.Id===n})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(a){(0,o.GetGridByCode)({code:e+","+t.Code}).then((function(e){var r=e.IsSucceed,n=e.Data,i=e.Message;if(r){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),n.Grid.Is_Page&&(t.queryInfo.PageSize=+n.Grid.Row_Number),a(t.columns)}else t.$message({message:i,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"74fe":function(e,t,a){"use strict";a.r(t);var r=a("b44bd"),n=a("d76d");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("9ce4");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"4d884754",null);t["default"]=l.exports},"77b5":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAJCAYAAAAo/ezGAAAAAXNSR0IArs4c6QAAAJpJREFUOE9jdFj+P4GRiWEyAwMDDwN1wZf//xhyGUFm2q34r8nMxLCa4T+DNlXsYGS4+vcfQ+ihCMbrYAtAQG/Rf25hNobJDIwMiRRZ8p9h/ttfDLmX4hi/gsyBWwAzlIIgAwfJgUjGBcgOxLCArCBDChJ032O1gKQgQwsSoi0gIsiwBgnJFmANMjxBQpYFKEHGwMCAnEoIpTgADbVKUN9upmAAAAAASUVORK5CYII="},8378:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CreateVersion=L,t.DelAuxCategoryEntity=_,t.DelAuxEntity=I,t.DelCategoryEntity=s,t.DelRawEntity=d,t.DeleteVersion=M,t.EditAuxEnabled=w,t.EditRawEnabled=c,t.ExportAuxForProject=B,t.ExportAuxList=R,t.ExportFindRawInAndOut=G,t.ExportInOutStoreReport=K,t.ExportPicking=N,t.ExportRawList=x,t.ExportRecSendProjectMaterialReport=Y,t.ExportRecSendProjectReport=H,t.ExportReceiving=O,t.ExportStagnationInventory=Q,t.ExportStoreReport=J,t.FindInAndOutPageList=W,t.FindPickingNewPageList=j,t.FindPickingPageList=E,t.FindReceivingNewPageList=F,t.FindReceivingPageList=k,t.GetAuxCategoryDetail=v,t.GetAuxCategoryTreeList=g,t.GetAuxDetail=S,t.GetAuxFilterDataSummary=V,t.GetAuxForProjectDetail=q,t.GetAuxForProjectPageList=z,t.GetAuxPageList=C,t.GetAuxTemplate=A,t.GetAuxWHSummaryList=$,t.GetCategoryDetail=l,t.GetCategoryTreeList=i,t.GetCycleDate=U,t.GetList=D,t.GetRawDetail=m,t.GetRawPageList=f,t.GetRawTemplate=h,t.ImportAuxList=P,t.ImportRawList=p,t.SaveAuxCategoryEntity=b,t.SaveAuxEntity=y,t.SaveCategoryEntity=o,t.SaveRawEntity=u,t.UpdateVersion=T;var n=r(a("b775"));function i(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function o(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:e,timeout:12e5})}function g(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:e,timeout:12e5})}function x(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:e})}function L(e){return(0,n.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:e})}function U(e){return(0,n.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:e})}function z(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:e})}function q(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:e})}function B(e){return(0,n.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:e})}function V(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:e})}function Q(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:e})}function J(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:e})}function H(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:e})}function Y(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:e})}function K(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:e})}},"8af9":function(e,t,a){"use strict";a.r(t);var r=a("f534"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"8b72":function(e,t,a){"use strict";a("2bb7")},"8bad":function(e,t,a){"use strict";a.r(t);var r=a("1b14"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"8db5":function(e,t,a){"use strict";a("d970")},9002:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTableConfig=void 0,a("d3b7");var r=a("6186"),n=void 0;t.getTableConfig=function(e){return new Promise((function(t,a){(0,r.GetGridByCode)({code:e}).then((function(e){var a=e.IsSucceed,r=e.Data,i=e.Message;if(a){var o=r.ColumnList||[];t(o)}else n.$message({message:i,type:"error"})}))}))}},"90d1":function(e,t){e.exports={WEIGHT_DECIMAL:5,INBOUND_DETAIL_UNIT_PRICE_DECIMAL:6,DETAIL_TOTAL_PRICE_DECIMAL:2,COUNT_DECIMAL:2,UNIT_WEIGHT_DECIMAL:100,TAX_MODE:0,SUMMARY_FIELDS:["Theory_Weight","InStoreWeight","Voucher_Weight","InStoreCount","TaxTotalPrice","NoTaxTotalPrice","TaxPrice"],INBOUND_DETAIL_HIDE_FIELDS:{isPurchase:["PartyUnitName"],isCustomer:["PurchaseNo","SupplierName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"],isManual:["PurchaseNo","PartyUnitName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"]},INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS:["SupplierName","ProjectName","Material","Tax_Rate","NoTaxUnitPrice","TaxUnitPrice"],INBOUND_DETAIL_SUMMARY_FIELDS:["InStoreCount","InStoreWeight","Voucher_Weight","NoTaxAllPrice","Tax_All_Price","Adjust_Amount","Tax","Theory_Weight"],OutBOUND_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","AvailableCount","AvailableWeight","NoTaxAllPrice","Tax_All_Price","Out_Store_Weight","Out_Store_Count","Returned_Weight","Returned_Count","InStoreCount","InStoreWeight"],Return_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","NoTaxAllPrice","Tax_All_Price","AvailableCount","AvailableWeight","Voucher_Weight"]}},"91ed":function(e,t,a){},93230:function(e,t,a){"use strict";a.r(t);var r=a("4065"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"9ce4":function(e,t,a){"use strict";a("b1db")},a559:function(e,t,a){"use strict";a("4753")},b1db:function(e,t,a){},b44bd:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-wapper"},[e._m(0),a("div",{staticClass:"btn-wrapper"},[a("div",{staticClass:"table-search"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("div",{staticClass:"search-from",style:e.showSearch?"grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr":"grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 200px;"},[a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"项目",prop:"Sys_Project_Id"}},[a("el-select",{staticClass:"cs-select",attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[a("el-select",{ref:"WarehouseRef",staticClass:"cs-select",attrs:{disabled:""!==e.summtypeId,clearable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{ref:"LocationRef",staticClass:"cs-select",attrs:{clearable:"",placeholder:"请选择库位",disabled:!e.form.Warehouse_Id},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{staticClass:"cs-select",attrs:{placeholder:"通配符%",clearable:""},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName",t)},expression:"form.Raw_FullName"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectState"}},[a("SelectProjectStatus",{model:{value:e.form.ProjectState,callback:function(t){e.$set(e.form,"ProjectState",t)},expression:"form.ProjectState"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"项目编码",prop:"ProjectCode"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.ProjectCode,callback:function(t){e.$set(e.form,"ProjectCode",t)},expression:"form.ProjectCode"}})],1)],1),e.showSearch?e._e():a("div",{staticClass:"search-item"},[a("el-button",{on:{click:function(t){e.showSearch=!e.showSearch}}},[e._v(e._s(e.showSearch?"收起":"展开"))]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)]),a("el-row",[a("transition",{attrs:{name:"fade"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"search-from",staticStyle:{"grid-template-columns":"1fr 1fr 1fr 1fr 1fr 1fr"}},[a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{type:"text",clearable:"",placeholder:"材质"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"所属分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0,filterable:!0},"tree-params":e.categoryOptions,styles:{width:"100%"}},on:{searchFun:e.filterFun2},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"供应商",prop:"SupplierName"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.SupplierName,callback:function(t){e.$set(e.form,"SupplierName",t)},expression:"form.SupplierName"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnitName"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.PartyUnitName,callback:function(t){e.$set(e.form,"PartyUnitName",t)},expression:"form.PartyUnitName"}})],1)],1),a("div",{staticClass:"search-item"},[a("el-form-item",{attrs:{label:"原料属性",prop:"Raw_Property"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择"},model:{value:e.form.Raw_Property,callback:function(t){e.$set(e.form,"Raw_Property",t)},expression:"form.Raw_Property"}},[a("el-option",{attrs:{label:"自采",value:1}}),a("el-option",{attrs:{label:"甲供",value:2}}),a("el-option",{attrs:{label:"代购",value:3}}),a("el-option",{attrs:{label:"余料",value:4}})],1)],1)],1),e.showSearch?a("div",{staticStyle:{"margin-left":"10px"}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-button",{on:{click:function(t){e.showSearch=!e.showSearch}}},[e._v(e._s(e.showSearch?"收起":"展开"))]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1):e._e()])])],1)],1)],1)]),a("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[e.getRoles("ExportRawNew")?a("el-button",{attrs:{loading:e.btnLoading},on:{click:e.handelExport}},[e._v("导出")]):e._e(),a("el-button",{attrs:{loading:e.btnLoading2},on:{click:e.exportStagnationInventory}},[e._v("导出库龄呆滞分析表")]),a("el-button",{attrs:{loading:e.btnLoading3},on:{click:e.exportStoreReport}},[e._v("导出库存状况表")]),a("el-button",{attrs:{type:"success"},on:{click:e.stockStream}},[e._v("移库流水")]),a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":"pro_material_requisitionNew,Steel"},on:{updateColumn:e.getGridConfig}})],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:!e.showTable,expression:"!showTable"}],staticClass:"tb-container"},[e.showTable?a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0},"row-config":{isCurrent:!0,isHover:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t,r){return a("vxe-column",{key:r,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,width:t.Width,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(r){var n=r.row;return["Raw_Name"==t.Code?a("div",[n.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),n.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),n.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(n.Raw_Name)+" ")],1):"Material"==t.Code?a("div",[e._v(" "+e._s(n.Material||"-")+" ")]):"Dull_Date"==t.Code?a("div",[a("span",{style:!0===n.Is_Over_Dull_Date?"color:red":""},[e._v(" "+e._s(n.Dull_Date||"-"))])]):"Warning_Status"==t.Code?a("div",[a("span",{style:"正常"===n.Warning_Status?"":"color:red"},[e._v(" "+e._s(n.Warning_Status||"-"))])]):"In_Store_Weight"==t.Code?a("div",[e._v(" "+e._s(n.In_Store_Weight||0===n.In_Store_Weight?(n.In_Store_Weight/1e3).toFixed(5):"-")+" ")]):"In_Store_Weight"==t.Code?a("div",[e._v(" "+e._s(n.In_Store_Weight||"-")+" ")]):"PartyUnitName"==t.Code?a("div",[e._v(" "+e._s(n.PartyUnitName||"-")+" ")]):"In_Store_Count"==t.Code?a("div",[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleView(n)}}},[e._v(e._s(n.In_Store_Count||0))])],1):a("div",[a("span",[e._v(e._s(n[t.Code]||"-"))])])]}}],null,!0)})}))],2):e._e()],1),a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"cs-component-num"},[a("div",{staticClass:"cs-col"},[a("span",[e._v("当前：")]),a("span",[e._v("物料在库量 "),a("i",[e._v(" "+e._s(e.filterData.Quantity||0===e.filterData.Quantity?(e.filterData.Quantity/1e3).toFixed(5):"-")+" t，")])]),a("span",[e._v("在库金额"),a("i",[e._v(" "+e._s(e.filterData.Money||0===e.filterData.Money?e.filterData.Money.toFixed(2):"-")+" 元")])])])]),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",on:{refresh:function(t){return e.fetchData(1)},close:e.handleClose}})],1):e._e()],1)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"0px"}},[a("div")])}]},b549:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"100px"}},[a("el-form-item",{attrs:{label:(e.isRaw?"原料":"辅料")+"全名",prop:"Mat_Name_Full"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%"},model:{value:e.form.Mat_Name_Full,callback:function(t){e.$set(e.form,"Mat_Name_Full",t)},expression:"form.Mat_Name_Full"}})],1),a("el-form-item",{attrs:{label:(e.isRaw?"原料":"辅料")+"分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0,filterable:!0},"tree-params":e.categoryOptions},on:{searchFun:e.filterFun2},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1),a("el-form-item",{attrs:{label:(e.isRaw?"原料":"辅料")+"名称",prop:"Mat_Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Mat_Name,callback:function(t){e.$set(e.form,"Mat_Name",t)},expression:"form.Mat_Name"}})],1),a("el-form-item",{attrs:{label:"转移仓库",prop:"WH_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择",clearable:""},on:{change:e.wareChange},model:{value:e.form.WH_Id,callback:function(t){e.$set(e.form,"WH_Id",t)},expression:"form.WH_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"转移库位",prop:"LT_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.WH_Id,filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.LT_Id,callback:function(t){e.$set(e.form,"LT_Id",t)},expression:"form.LT_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"操作人",prop:"Operator_Id"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Operator_Id,callback:function(t){e.$set(e.form,"Operator_Id",t)},expression:"form.Operator_Id"}})],1),a("el-form-item",{attrs:{label:"项目",prop:"Sys_Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"操作时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.planTime,callback:function(t){e.planTime=t},expression:"planTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("搜索")]),a("el-button",{on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")])],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","checkbox-config":{checkField:"checked"},height:"500",align:"left",stripe:"","row-config":{isCurrent:!0,isHover:!0},size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return["Mat_Type"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-tag",{attrs:{type:2===r.Store_Type?"primary":"success"}},[e._v(e._s(r.Is_Component?"锁定库存":"公共库存")+" ")])]}}],null,!0)}):"Operate_Time"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Operate_Time))+" ")]}}],null,!0)}):a("vxe-column",{key:t.Id,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,width:t.Width}})]}))],2)],1)],1)},n=[]},c603:function(e,t,a){"use strict";a.r(t);var r=a("b549"),n=a("15a2");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("8db5");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"12a99bd2",null);t["default"]=l.exports},cf45:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=n,a("d3b7");var r=a("6186");function n(e){return new Promise((function(t,a){(0,r.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},d14e:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("2532"),a("3ca3"),a("159b"),a("ddb0");var n=r(a("5530")),i=r(a("c14f")),o=r(a("1da1")),l=a("c685"),s=r(a("333d")),u=r(a("5f52")),c=r(a("0f97")),d=r(a("d612")),f=a("ed08"),m=a("3c4a"),h=a("209b"),p=a("c24f"),g=r(a("c603")),b=r(a("bc29")),v=r(a("40c9")),_=a("8378"),y=r(a("d63e4")),w=r(a("a657"));t.default={components:{DynamicTableFields:w.default,SelectProjectStatus:y.default,Stream:g.default,DynamicDataTable:c.default,stockDialog:d.default,Pagination:s.default},mixins:[u.default,b.default,v.default],props:{searchDetail:{type:Object,default:function(){return{}}}},data:function(){return{tablePageSize:l.tablePageSize,showSearch:!1,categoryOptions:{"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},treeList:[],roleList:[],pgLoading:!1,btnLoading:!1,btnLoading2:!1,btnLoading3:!1,queryInfo:{Page:1,PageSize:20},columns:[],currentColumns:[],tbData:[],tbConfig:{},total:0,dialogVisible:!1,dialogTitle:"",width:0,currentComponent:"",form:{Raw_FullName:"",Warehouse_Id:"",Sys_Project_Id:"",Location_Id:"",Raw_Name:"",Spec:"",Material:"",Length:"",Width:"",Category_Id:"",SupplierName:"",PartyUnitName:"",Raw_Property:[],ProjectState:"",ProjectCode:""},activeName:"1",CommonStore:[],LockStore:[],summaryList:{},Pitchs:[],filterData:[],summtypeId:"",spanWidth:4,showTable:!0}},watch:{activeName:function(e){}},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getRoleAuthorization();case 1:return t.n=2,e.getGridConfig();case 2:return t.n=3,e.fetchData();case 3:e.getCategoryList();case 4:return t.a(2)}}),t)})))()},provide:function(){return{projectData:this.getProjectInfo,isRaw:!0,category:this.getTreeList}},methods:{ExportStagnationInventory:_.ExportStagnationInventory,getGridConfig:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.showTable=!1,t.n=1,e.getTableConfig("pro_material_requisitionNew");case 1:e.currentColumns=e.columns,e.showTable=!0;case 2:return t.a(2)}}),t)})))()},stockStream:function(){this.dialogTitle="移库流水",this.currentComponent="Stream",this.width="80%",this.dialogVisible=!0},SummaryList:function(){var e=this;(0,m.GetRawWHSummaryList)({}).then((function(t){t.IsSucceed?e.summaryList=t.Data:e.$message.error(t.Message)}))},changePage:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),Promise.all([e.fetchData()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},FilterData:function(){var e=this,t=(0,n.default)((0,n.default)({},this.form),{},{Pitchs:this.Pitchs});(0,m.GetRawFilterDataSummary)((0,n.default)({},t)).then((function(t){t.IsSucceed?e.filterData=t.Data:e.$message.error(t.Message)}))},tbSelectChange:function(e){var t=this;this.Pitchs=[],e.records.forEach((function(e){t.Pitchs.push({Store_Id:e.Store_Id,Sys_Project_Id:e.Sys_Project_Id})})),this.FilterData()},filterFun2:function(e){this.$refs.treeSelectArea.$refs.tree.filter(e)},getProjectInfo:function(){return this.ProjectNameData},getTreeList:function(){return this.treeList},handleSearch:function(){this.form.Warehouse_Id=this.summtypeId||this.form.Warehouse_Id,this.fetchData(1)},getCategoryList:function(){var e=this;(0,_.GetCategoryTreeList)({}).then((function(t){if(t.IsSucceed){e.treeList=t.Data;var a=t.Data;e.categoryOptions.data=a,e.$nextTick((function(t){e.$refs.treeSelectArea.treeDataUpdateFun(a)}))}else e.$message.error(t.Message)}))},getRoles:function(e){return this.roleList.includes(e)},getRoleAuthorization:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,p.RoleAuthorization)({roleType:3,menuType:1,menuId:e.$route.meta.Id});case 1:a=t.v,a.IsSucceed?e.roleList=a.Data.map((function(e){return e.Code})):e.$message({type:"warning",message:a.Message});case 2:return t.a(2)}}),t)})))()},warehoust:function(e){this.summtypeId=e,this.form.Warehouse_Id=e,this.fetchData(1),this.wareChange(this.form.Warehouse_Id)},wareChange:function(e){var t=this;this.form.Location_Id="",(0,h.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.FilterData(),this.pgLoading=!0,(0,m.GetRawForProjectPageList)({Model:this.form,PageInfo:this.queryInfo}).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.map((function(e){return e})),t.total=e.Data.TotalCount):t.$message.error(e.Message)})).finally((function(e){t.pgLoading=!1}))},generateComponent:function(){this.dialogTitle="在库量",this.currentComponent="stockDialog",this.width="60%",this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},handleView:function(e){var t=this;this.generateComponent(),this.$nextTick((function(a){t.$refs.content.init(e)}))},handelExport:function(){var e=this;this.btnLoading=!0;var t=(0,n.default)((0,n.default)({},this.form),{},{Pitchs:this.Pitchs});(0,m.ExportRawForProject)((0,n.default)({},t)).then((function(t){t.IsSucceed?(t.Message&&e.$alert(t.Message,"导出通知",{confirmButtonText:"我知道了"}),window.open((0,f.combineURL)(e.$baseUrl,t.Data))):e.$message.error(t.Message)})).finally((function(t){e.btnLoading=!1}))},exportStagnationInventory:function(){var e=this;this.btnLoading2=!0,(0,_.ExportStagnationInventory)({Model:(0,n.default)({},this.form),PageInfo:{}}).then((function(t){t.IsSucceed?window.open((0,f.combineURL)(e.$baseUrl,t.Data)):e.$message.error(t.Message)})).finally((function(){e.btnLoading2=!1}))},exportStoreReport:function(){var e=this;this.btnLoading3=!0,(0,_.ExportStoreReport)({Model:(0,n.default)({},this.form),PageInfo:{}}).then((function(t){t.IsSucceed?window.open((0,f.combineURL)(e.$baseUrl,t.Data)):e.$message.error(t.Message)})).finally((function(){e.btnLoading3=!1}))}}}},d51d:function(e,t,a){"use strict";a.r(t);var r=a("d628"),n=a("93230");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("a559");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"870e0192",null);t["default"]=l.exports},d612:function(e,t,a){"use strict";a.r(t);var r=a("11a3"),n=a("e9e72");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("8b72");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"4f298afc",null);t["default"]=l.exports},d628:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-z-tb-wrapper"},[e.showSearch?a("el-form",{ref:"form",staticClass:"search-header",attrs:{inline:"",model:e.form}},[a("el-form-item",{attrs:{label:"选择时间",prop:"DateRange"}},[a("el-date-picker",{staticStyle:{width:"250px"},attrs:{type:"daterange","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.DateRange,callback:function(t){e.$set(e.form,"DateRange",t)},expression:"form.DateRange"}})],1),a("el-form-item",{attrs:{label:"项目名称",prop:"SysProjectId"}},[a("SelectProject",{model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}})],1),a("el-form-item",{attrs:{label:"原料全名",prop:"MaterielFullName"}},[a("el-input",{attrs:{placeholder:"通配符%",clearable:""},model:{value:e.form.MaterielFullName,callback:function(t){e.$set(e.form,"MaterielFullName",t)},expression:"form.MaterielFullName"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"MaterielName"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.MaterielName,callback:function(t){e.$set(e.form,"MaterielName",t)},expression:"form.MaterielName"}})],1),a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.fetchData(1)}}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.fetchData(1)}}},[e._v("重置")])],1)],1):e._e(),a("div",{staticClass:"tb-header"},[a("el-button",{attrs:{loading:e.loading1},on:{click:e.exportExcel}},[e._v("导出")]),a("el-button",{attrs:{loading:e.loading2},on:{click:e.exportRecSendProjectReport}},[e._v("收发存汇总表导出(工程号)")]),a("el-button",{attrs:{loading:e.loading3},on:{click:e.exportExportRecSendProjectMaterialReport}},[e._v("收发存汇总表导出(工程号+存货编码)")]),a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.updateColumn}})],1),a("div",{staticClass:"tb-wrapper"},[e.showTable?a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"element-loading-spinner":"el-icon-loading","element-loading-text":"加载中","empty-text":"暂无数据",height:"100%",data:e.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0},"row-config":{isCurrent:!0,isHover:!0,keyField:"Id"}}},[a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",align:"center",fixed:"left"}}),e._l(e.columns,(function(t,r){return a("vxe-column",{key:r,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["Semi_Type"===t.Code?{key:"default",fn:function(e){e.row}}:{key:"default",fn:function(r){var n=r.row;return[a("span",[e._v(e._s(n[t.Code]||"-"))])]}}],null,!0)})}))],2):e._e()],1),a("Pagination",{attrs:{total:e.totalNum,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)},n=[]},d63e4:function(e,t,a){"use strict";a.r(t);var r=a("ef5c"),n=a("4120");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,null,null);t["default"]=l.exports},d7268:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAR9JREFUOE+1lKFOA0EURe9NEAgEooKkCPoHyFo8DkxVkeBq+hUYHEhQmK2rr+1fsIImiIqKCgTJZaYMm9kJ09nOlknW7Lw9c97bN4/Y82ITnqSOjSO5TMUngZKuADw50C3JyTZoFOisHgFcB4ACwF3M9k+gpCGAB/McR2xWAEYkX8L9GlBS16V3maqV258CsGVY/MZXwAZWsTNqtsywioF/bCWdu1rNGqYZC7swdV35KasNkOSGFQO+A/hKHHAA4LT6GQlgj2S5DSjpDMDbTkBJr6aNTgLwB8lBLtAaWBN/laahe7nAPoDDAPhJcp4F/I8a3gPYjC5vLUmOswzNdWxVwxvPoiC5lmRH11FguCZZSLLvq9FG8rnW2G1uif/tN+wGm1obWqtPAAAAAElFTkSuQmCC"},d76d:function(e,t,a){"use strict";a.r(t);var r=a("d14e"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},d7a6:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"container abs100"},[r("div",{staticClass:"tabs"},[r("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[r("el-tab-pane",{attrs:{label:"原料库存",name:"stock"}}),r("el-tab-pane",{attrs:{label:"出入库统计",name:"detail"}})],1)],1),e.stockTab?[r("div",{ref:"searchDom",staticClass:"header_wrapper"},[r("div",{staticClass:"search-wrapper"},[r("div",{staticClass:"scroll-left",on:{click:function(t){return e.scrollLeft()}}},[r("img",{staticStyle:{width:"32px",height:"32px"},attrs:{src:a("04a6"),alt:""}})]),r("div",{staticClass:"containerto"},e._l(e.summaryList,(function(t,n){return r("div",{key:n,class:e.summtypeId===t.Warehouse_Id?"itemlast":"item",on:{click:function(a){return e.handleClick(t)}}},[r("div",{staticClass:"item-a"},[r("div",{staticClass:"icon-a"},[e.summtypeId===t.Warehouse_Id?r("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:a("d7268"),alt:"",srcset:""}}):r("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:a("52b3"),alt:"",srcset:""}})]),r("div",{staticClass:"title-a"},[e._v(e._s(t.Warehouse_Name))])]),r("div",{staticClass:"item-b title-b"},[r("span",[e._v("在库量(t)")]),r("span",{staticClass:"title-b-num"},[e._v(" "+e._s(t.Weight||0===t.Weight?(t.Weight/1e3).toFixed(5):"-")+" ")])]),r("div",{staticClass:"item-b title-b"},[r("span",[e._v("在库金额(元)")]),r("span",{staticClass:"title-b-num"},[e._v(" "+e._s(t.Money||0===t.Money?t.Money.toFixed(2):"-")+" ")])]),e._m(0,!0)])})),0),r("div",{staticClass:"scroll-left",on:{click:function(t){return e.scrollRight()}}},[r("img",{staticStyle:{width:"32px",height:"32px"},attrs:{src:a("f679"),alt:""}})])])]),r("div",{staticClass:"main-wrapper"},[r("inventory",{ref:"inventoryRef"})],1)]:r("div",{staticStyle:{padding:"0 16px",background:"#ffffff",height:"100%"}},[r("StatisticsTb",{ref:"statisticsTbRef",attrs:{"show-search":""}})],1)],2)},n=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"item-c"},[r("img",{attrs:{src:a("77b5"),alt:"",srcset:""}})])}]},d970:function(e,t,a){},dad0:function(e,t,a){"use strict";a.r(t);var r=a("d7a6"),n=a("8bad");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("524d");var o=a("2877"),l=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"03e5f975",null);t["default"]=l.exports},e821:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=r(a("c14f")),i=r(a("1da1")),o=a("8975"),l=a("9002"),s=a("3c4a"),u=a("209b"),c=(a("8378"),r(a("6612")));t.default={data:function(){return{form:{Begin_Date:null,Category_Id:null,End_Date:null,LT_Id:null,Mat_Name:null,Operator_Id:null,Store_Type:null,Sys_Project_Id:null,WH_Id:null,Mat_Name_Full:null},categoryOptions:{"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},projectNameData:[],warehouses:[],locations:[],columns:[],tbData:[],LTDisabled:!1,tbLoading:!1,btnLoading:!1}},inject:["projectData","isRaw","category"],computed:{planTime:{get:function(){return[(0,o.timeFormat)(this.form.Begin_Date),(0,o.timeFormat)(this.form.End_Date)]},set:function(e){if(e){var t=e[0],a=e[1];this.form.Begin_Date=(0,o.timeFormat)(t),this.form.End_Date=(0,o.timeFormat)(a)}else this.form.Begin_Date="",this.form.End_Date=""}}},mounted:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)(e.isRaw?"PROInventoryMoveStore":"PROInventoryAuxMoveStore");case 1:e.columns=t.v,e.getWarehouseList(),e.projectNameData=e.projectData(),e.getCategoryList(),e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{filterFun2:function(e){this.$refs.treeSelectArea.$refs.tree.filter(e)},getCategoryList:function(){var e=this,t=this.category();this.categoryOptions.data=t,this.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t)}))},resetForm:function(e){this.planTime="",this.$refs[e].resetFields(),this.fetchData()},fetchData:function(){var e=this;this.tbLoading=!0;var t=this.isRaw?s.FindRawFlowList:s.FindAuxFlowList;t(this.form).then((function(t){t.IsSucceed?e.tbData=t.Data.map((function(e){return e.Transfer_Weight=(0,c.default)(e.Transfer_Weight||0).divide(1e3).format("0.[000]"),e.Tax_Unit_Price=1e3*e.Tax_Unit_Price,e})):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},getWarehouseList:function(){var e=this;(0,u.GetWarehouseListOfCurFactory)({type:this.isRaw?"原材料仓库":"辅料仓库"}).then((function(t){t.IsSucceed&&(e.warehouses=t.Data)}))},wareChange:function(e){var t=this;this.form.LT_Id="",e&&(0,u.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))}}}},e9e72:function(e,t,a){"use strict";a.r(t);var r=a("540e"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},ef5c:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:e.clearable,multiple:e.multiple},on:{change:e.handleChange},model:{value:e.selectedValue,callback:function(t){e.selectedValue=t},expression:"selectedValue"}},e._l(e.list,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)},n=[]},f534:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),i=r(a("c14f")),o=r(a("1da1"));a("a9e3");var l=a("209b"),s=a("3c4a");t.default={props:{visible:{type:Boolean,default:!1},id:{type:String,default:""},curType:{type:Number,default:1}},data:function(){return{warehouses:[],locations:[],loading:!1,form:{WH_Id:"",LT_Id:"",Count:void 0,Remark:""},rules:{WH_Id:[{required:!0,message:"请输入",trigger:"blur"}],LT_Id:[{required:!0,message:"请输入",trigger:"blur"}],Count:[{required:!0,message:"请输入",trigger:"blur"}]}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.getWarehouseList();case 1:return t.a(2)}}),t)})))()},methods:{getWarehouseList:function(){var e=this;(0,l.GetWarehouseListOfCurFactory)({type:1===this.curType?"原材料仓库":"辅料仓库"}).then((function(t){t.IsSucceed&&(e.warehouses=t.Data)}))},wareChange:function(e){var t=this;this.form.LT_Id="",e&&(0,l.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.loading=!0;var a=1===e.curType?s.SetRawLT:s.SetAuxLT;a((0,n.default)((0,n.default)({},e.form),{},{Store_Sub_Id:e.id})).then((function(t){t.IsSucceed?(e.dialogVisible=!1,e.$emit("updateInfo"),e.$message({message:"移库成功",type:"success"})):e.$message({message:t.Message,type:"error"}),e.loading=!1}))}))}}}},f679:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAatJREFUOE+tlctSwjAUhk9MMJEGAsJWUcCdjjP6/m+hGy8slIU6ylDoJekFnOO0TK0NlBmza/r3y7nlL4HqRQCgK4RoM8YkpZSjLE1TkyTJUmu9AIA5AKzLn+OH5dVxHOeEMXZoOexnG+Ge570AAMI3qwgkQohTznl/G6j8LoqijzAMX/P9DVAIMdgXlkOiKHoPw3CKzzmwp5Q62yeystZ13ScAcBFIHMe5Yow1yiJjzJfWeiqlvKCUNnfUVHued4/AvlJqUCVOkiT1ff8RAELO+VgI0doGxShJJlQ2YQbFdALO+QhHyaY1xnxiuteMMbbt5DiOV0EQPAPAknM+FEJ0qvRpmgZEKXVbpxkZdIKFb7fbY0LIn6wwG9JsNm8ajUbVgP86pxDlwgaM43hNpJSX+dWqWUdMubLmeHvwdgw5590tsDjrdJQ1xdppY8wMUz1WSp1bxibyff8BAFbZLB7tGJvJrsGeaa3fpJSjXWXJzOIub4Y1yjoTgBrXdXGs5v9lDhvHKdsXOk6vblSos9pXAVLXYNEM0GCXNoMt7h9kv4AWpbSVu3edX8A3PCnh47VeoOAAAAAASUVORK5CYII="},fe1e:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"移库","append-to-body":"",visible:e.dialogVisible,width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"仓库",prop:"WH_Id"}},[a("el-select",{ref:"WarehouseRef",attrs:{clearable:"",filterable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.WH_Id,callback:function(t){e.$set(e.form,"WH_Id",t)},expression:"form.WH_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"LT_Id"}},[a("el-select",{ref:"LocationRef",attrs:{clearable:"",filterable:"",placeholder:"请选择库位",disabled:!e.form.WH_Id},model:{value:e.form.LT_Id,callback:function(t){e.$set(e.form,"LT_Id",t)},expression:"form.LT_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"数量",prop:"Count"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0},model:{value:e.form.Count,callback:function(t){e.$set(e.form,"Count",t)},expression:"form.Count"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)]):e._e()},n=[]}}]);