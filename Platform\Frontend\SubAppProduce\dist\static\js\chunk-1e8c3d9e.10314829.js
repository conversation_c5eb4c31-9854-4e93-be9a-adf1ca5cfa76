(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-1e8c3d9e","chunk-2d0e2790"],{"01920":function(t,e,n){"use strict";n("3fc3")},"0a2af":function(t,e,n){"use strict";n.r(e);var r=n("7da0"),a=n("2011b");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("28acb");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"05d82446",null);e["default"]=u.exports},"0b19":function(t,e,n){"use strict";n.r(e);var r=n("4cc4"),a=n("7fd3");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("3ecb");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"0085ff7d",null);e["default"]=u.exports},1381:function(t,e,n){"use strict";n.r(e);var r=n("5aef"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var r=n("6186"),a=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,r.GetGridByCode)({code:t,IsAll:n}).then((function(t){var r=t.IsSucceed,i=t.Data,u=t.Message;if(r){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),s=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||a.tablePageSize[0]),o(e.columns)}else e.$message({message:u,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,r=t.type;this.queryInfo.Page="limit"===r?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var r=0;r<this.columns.length;r++){var a=this.columns[r];if(a.Code===e){n.Type=a.Type,n.Filter_Type=a.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"15fd":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=o,n("a4d3");var r=a(n("ccb5"));function a(t){return t&&t.__esModule?t:{default:t}}function o(t,e){if(null==t)return{};var n,a,o=(0,r.default)(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(a=0;a<i.length;a++)n=i[a],-1===e.indexOf(n)&&{}.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}},"1b82":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"cs-box"},[n("div",{staticClass:"box-container"},[n("div",{staticStyle:{"margin-top":"24px"}},[n("strong",{staticClass:"title"},[t._v(t._s(t.item.working_team_name))])]),n("div",{staticStyle:{"margin-top":"20px"}},[n("span",[t._v(t._s(t.item.count)+"个；"+t._s(t.item.sum)+"（kg）")])])])])},a=[]},"2011b":function(t,e,n){"use strict";n.r(e);var r=n("9015"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},2150:function(t,e,n){"use strict";n.r(e);var r=n("7658"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},"27c6":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=r(n("5530")),o=r(n("15fd")),i=r(n("c14f")),u=r(n("1da1"));n("4de4"),n("7db0"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("d3b7"),n("159b");var s=r(n("34e9")),d=r(n("e601")),l=r(n("d87c")),c=r(n("ffa7")),f=r(n("0a2af")),p=r(n("a7e9")),m=r(n("0f97")),h=r(n("15ac")),g=r(n("a888")),P=r(n("1463")),v=n("7f9d"),_=r(n("3dcb")),b=n("1b69"),C=n("f2f6"),T=n("ed08"),k=["Comp_Codes"];e.default={name:"PROComAllocation",components:{Support:l.default,TopHeader:s.default,ImportDialog:d.default,cancel:c.default,bitchEdit:p.default,zSelect:f.default,DynamicDataTable:m.default,BtnGroup:_.default,TreeDetail:P.default},directives:{elDragDialog:g.default},mixins:[h.default],data:function(){return{statusOption:{unPro:{value:"1",label:"待生产"},inPro:{value:"2",label:"生产中"},finish:{value:"3",label:"已完成"},transfer:{value:"4",label:"转移中"}},dWidth:"30%",value:"",processId:"",title:"",dialogVisible:!1,drawer:!1,currentComponent:"ImportDialog",processOptions:[],options:[],tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,Alloction_Status:1,ParameterJson:[],Comp_Codes:""},columns:[],tbData:[],total:0,selectArray:[],tbLoading:!1,detailPageIndex:0,btnOptions:[{label:"待分配",value:1},{label:"已分配",value:2}],installName:"",projects:[],installOption:[],groupValue:"",groupInput:"",treeLoading:!1,treeData:[]}},computed:{currentTreeData:function(){var t=this;return this.treeData.filter((function(e){return-1!==e.working_team_name.indexOf(t.groupInput)}))},isFinishPage:function(){return 2===this.queryInfo.Alloction_Status}},mounted:function(){var t=this;return(0,u.default)((0,i.default)().m((function e(){var n;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig("AllocationComponent");case 1:return e.n=2,t.getProcessList();case 2:if(n=t.isFinishPage,!n){e.n=3;break}return e.n=3,t.getGroupList();case 3:t.fetchData(),t.getComponentType();case 4:return e.a(2)}}),e)})))()},methods:{getProcessList:function(){var t=this;return new Promise((function(e){(0,v.GetCoordinateProcess)({type:1}).then((function(n){var r,a=n.IsSucceed,o=n.Message,i=n.Data;a?(t.processOptions=i,t.processId=null===(r=t.processOptions[0])||void 0===r?void 0:r.Id):t.$message({message:o,type:"error"});e()}))}))},getGroupList:function(){var t=this;return new Promise((function(e,n){(0,v.GetWorkingTeamComponentCountBase)({processId:t.processId}).then((function(n){var r,a=n.IsSucceed,o=n.Message,i=n.Data;a?(i.forEach((function(e){t.$set(e,"Label",e.working_team_name),t.$set(e,"Id",e.working_team_id)})),t.treeData=i,t.groupValue=null===(r=t.treeData[0])||void 0===r?void 0:r.working_team_id):t.$message({message:o,type:"error"});e()}))}))},fetchData:function(t){var e=this;if(t&&(this.queryInfo.Page=t),this.selectArray=[],this.tbLoading=!0,!this.processId)return this.tbLoading=!1,this.tbData=[],void(this.total=0);var n=this.queryInfo,r=n.Comp_Codes,i=(0,o.default)(n,k),u=(0,a.default)((0,a.default)({},i),{},{Process_Id:this.processId});this.isFinishPage&&(u.Working_Team_Id=this.groupValue),r.length&&(u.Comp_Codes=r.split("\n").filter((function(t){return!!t}))),(0,v.GetProcessAllocationComponentBasePageList)(u).then((function(t){var n=t.IsSucceed,r=t.Message,a=t.Data;n?(e.tbData=null===a||void 0===a?void 0:a.Data,e.total=null===a||void 0===a?void 0:a.TotalCount):e.$message({message:r,type:"error"}),e.tbLoading=!1}))},btnChange:function(){var t=this;return(0,u.default)((0,i.default)().m((function e(){var n;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:if(n=t.isFinishPage,!n){e.n=1;break}return e.n=1,t.getGroupList();case 1:t.fetchData();case 2:return e.a(2)}}),e)})))()},processChange:function(){var t=this;return(0,u.default)((0,i.default)().m((function e(){var n;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:if(n=t.isFinishPage,!n){e.n=1;break}return e.n=1,t.getGroupList();case 1:t.fetchData();case 2:return e.a(2)}}),e)})))()},handleNodeClick:function(t){this.groupValue=t.Id,this.fetchData()},getComponentType:function(){var t=this;(0,b.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))},installNameChange:function(t){this.$refs.dyTable.searchedField["Installunit_Name"]=t,this.showSearchBtn()},projectChange:function(t){if(this.installName="",this.installOption=[],t){var e=this.projects.find((function(e){return e.Name===t}));this.getUnitList(e.Id)}else this.$refs.dyTable.searchedField["Installunit_Name"]="";this.showSearchBtn()},getUnitList:function(t){var e=this;(0,C.GetInstallUnitList)({Project_Id:t}).then((function(t){t.IsSucceed&&(e.installOption=t.Data)}))},handleExport:function(){var t=this;(0,v.ExportAllocationComponent)({processId:this.processId,uncodes:this.selectArray.map((function(t){return t.Unique_Code}))}).then((function(e){var n=e.IsSucceed,r=e.Message,a=e.Data;n?window.open((0,T.combineURL)(t.$baseUrl,a)):t.$message({message:r,type:"error"})}))},tbSelectChange:function(t){this.selectArray=t},handleImport:function(){this.dWidth="30%",this.currentComponent="ImportDialog",this.title="导入工艺",this.dialogVisible=!0},handleSupport:function(){this.$refs.support.handleOpen(this.processId)},handleSelect:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.dWidth="42%",this.currentComponent="zSelect",this.title=this.isFinishPage?"调整分配":"选择分配",this.dialogVisible=!0,this.$nextTick((function(r){n?e.$refs.content.handleSelect(t,n,!e.isFinishPage):e.$refs.content.handleSelect([t],n,!e.isFinishPage)}))},handleBitchEdit:function(){var t=this;this.drawer=!0,this.$nextTick((function(e){t.$refs.drawer.handleSelect(t.selectArray)}))},getBatchList:function(t){this.handleSelect(t,!0)},handleClose:function(){this.dialogVisible=!1,this.drawer=!1},handleDrawerClose:function(){this.drawerVisible=!1}}}},"27ed":function(t,e,n){"use strict";n.r(e);var r=n("b2ab"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},"28acb":function(t,e,n){"use strict";n("e59c")},"2e0e":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{staticClass:"cs-dialog",attrs:{title:"批量编辑",visible:t.dialogVisible,width:"55%"},on:{"update:visible":function(e){t.dialogVisible=e},close:function(e){t.dialogVisible=!1}}},[n("el-row",[n("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{"label-width":"200px"}},t._l(t.list,(function(e,r){return n("el-col",{key:r,attrs:{span:12}},[n("el-form-item",{attrs:{label:e.Comp_Code+"："}},[n("el-input-number",{attrs:{min:1,max:e.Comp_Sum},model:{value:e.num,callback:function(n){t.$set(e,"num",n)},expression:"item.num"}})],1)],1)})),1)],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},a=[]},"31ba":function(t,e,n){},"3b8f":function(t,e,n){"use strict";n.r(e);var r=n("aa74");n.d(e,"version",(function(){return r["cb"]})),n.d(e,"dependencies",(function(){return r["l"]})),n.d(e,"PRIORITY",(function(){return r["g"]})),n.d(e,"init",(function(){return r["B"]})),n.d(e,"connect",(function(){return r["j"]})),n.d(e,"disconnect",(function(){return r["n"]})),n.d(e,"disConnect",(function(){return r["m"]})),n.d(e,"dispose",(function(){return r["o"]})),n.d(e,"getInstanceByDom",(function(){return r["w"]})),n.d(e,"getInstanceById",(function(){return r["x"]})),n.d(e,"registerTheme",(function(){return r["R"]})),n.d(e,"registerPreprocessor",(function(){return r["P"]})),n.d(e,"registerProcessor",(function(){return r["Q"]})),n.d(e,"registerPostInit",(function(){return r["N"]})),n.d(e,"registerPostUpdate",(function(){return r["O"]})),n.d(e,"registerUpdateLifecycle",(function(){return r["T"]})),n.d(e,"registerAction",(function(){return r["H"]})),n.d(e,"registerCoordinateSystem",(function(){return r["I"]})),n.d(e,"getCoordinateSystemDimensions",(function(){return r["v"]})),n.d(e,"registerLocale",(function(){return r["L"]})),n.d(e,"registerLayout",(function(){return r["J"]})),n.d(e,"registerVisual",(function(){return r["U"]})),n.d(e,"registerLoading",(function(){return r["K"]})),n.d(e,"setCanvasCreator",(function(){return r["V"]})),n.d(e,"registerMap",(function(){return r["M"]})),n.d(e,"getMap",(function(){return r["y"]})),n.d(e,"registerTransform",(function(){return r["S"]})),n.d(e,"dataTool",(function(){return r["k"]})),n.d(e,"zrender",(function(){return r["eb"]})),n.d(e,"matrix",(function(){return r["D"]})),n.d(e,"vector",(function(){return r["bb"]})),n.d(e,"zrUtil",(function(){return r["db"]})),n.d(e,"color",(function(){return r["i"]})),n.d(e,"throttle",(function(){return r["X"]})),n.d(e,"helper",(function(){return r["A"]})),n.d(e,"use",(function(){return r["Z"]})),n.d(e,"setPlatformAPI",(function(){return r["W"]})),n.d(e,"parseGeoJSON",(function(){return r["F"]})),n.d(e,"parseGeoJson",(function(){return r["G"]})),n.d(e,"number",(function(){return r["E"]})),n.d(e,"time",(function(){return r["Y"]})),n.d(e,"graphic",(function(){return r["z"]})),n.d(e,"format",(function(){return r["u"]})),n.d(e,"util",(function(){return r["ab"]})),n.d(e,"env",(function(){return r["p"]})),n.d(e,"List",(function(){return r["e"]})),n.d(e,"Model",(function(){return r["f"]})),n.d(e,"Axis",(function(){return r["a"]})),n.d(e,"ComponentModel",(function(){return r["c"]})),n.d(e,"ComponentView",(function(){return r["d"]})),n.d(e,"SeriesModel",(function(){return r["h"]})),n.d(e,"ChartView",(function(){return r["b"]})),n.d(e,"innerDrawElementOnCanvas",(function(){return r["C"]})),n.d(e,"extendComponentModel",(function(){return r["r"]})),n.d(e,"extendComponentView",(function(){return r["s"]})),n.d(e,"extendSeriesModel",(function(){return r["t"]})),n.d(e,"extendChartView",(function(){return r["q"]}))},"3ecb":function(t,e,n){"use strict";n("31ba")},"3fc3":function(t,e,n){},4686:function(t,e,n){"use strict";n.r(e);var r=n("1b82"),a=n("2150");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("723f");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"2287eba7",null);e["default"]=u.exports},"49ed":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=r(n("3796")),o=n("7f9d"),i=n("ed08");e.default={components:{UploadExcel:a.default},props:{processId:{type:String,default:""}},data:function(){return{dialogVisible:!1}},mounted:function(){},methods:{getTemplate:function(){},beforeUpload:function(t){var e=this,n=new FormData;n.append("processId",this.processId),n.append("files",t),(0,o.ImportUpdateComponentWorkingTeam)(n).then((function(t){if(t.IsSucceed)e.$message({message:"导入成功",type:"success"}),e.$emit("close"),e.$emit("refresh");else if(e.$message({message:t.Message,type:"error"}),t.Data){var n=(0,i.combineURL)(e.$baseUrl,t.Data);window.open(n,"_blank")}}))},handleSubmit:function(){this.$refs.upload.handleSubmit()}}}},"4cc4":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100  cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1100px"}},[n("div",{class:["cs-z-page-main-content",t.isFinishPage?"cs-finish":""]},[n("div",{staticClass:"cs-top"},[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:""}},[n("el-form-item",[n("btn-group",{attrs:{options:t.btnOptions},on:{change:t.btnChange},model:{value:t.queryInfo.Alloction_Status,callback:function(e){t.$set(t.queryInfo,"Alloction_Status",e)},expression:"queryInfo.Alloction_Status"}})],1),n("el-form-item",{attrs:{label:"工序"}},[n("el-select",{attrs:{placeholder:"请选择"},on:{change:t.processChange},model:{value:t.processId,callback:function(e){t.processId=e},expression:"processId"}},t._l(t.processOptions,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"构件编号"}},[n("el-input",{attrs:{resize:"none",placeholder:"换行查找多个",autosize:{minRows:4,maxRows:4},type:"textarea"},model:{value:t.queryInfo.Comp_Codes,callback:function(e){t.$set(t.queryInfo,"Comp_Codes",e)},expression:"queryInfo.Comp_Codes"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.fetchData(1)}}},[t._v("搜 索")])],1)],1),n("div",[t.isFinishPage?n("el-button",{attrs:{disabled:!t.selectArray.length,type:"primary"},on:{click:t.handleBitchEdit}},[t._v("批量调整分配")]):[n("el-button",{attrs:{type:"warning"},on:{click:t.handleSupport}},[t._v("分配辅助")]),n("el-button",{attrs:{disabled:!t.selectArray.length,type:"primary"},on:{click:t.handleBitchEdit}},[t._v("批量操作")])]],2)],1),t.isFinishPage?n("div",{staticClass:"cs-divider"}):t._e(),n("div",{class:["cs-main-container",t.isFinishPage?"cs-single":""]},[t.isFinishPage?n("div",{staticClass:"main-group"},[n("el-input",{staticStyle:{"margin-bottom":"20px"},attrs:{placeholder:"搜索","suffix-icon":"el-icon-search"},model:{value:t.groupInput,callback:function(e){t.groupInput=e},expression:"groupInput"}}),n("div",{staticClass:"cs-scroll"},[n("tree-detail",{attrs:{loading:t.treeLoading,icon:"icon-folder-open","tree-data":t.currentTreeData,"expanded-key":t.groupValue},on:{handleNodeClick:t.handleNodeClick}})],1)],1):t._e(),n("div",{class:[t.isFinishPage?"table-info-x":"cs-wh"]},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper h100",class:{"main-table":t.isFinishPage},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中"}},[t.tbLoading?t._e():n("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,config:t.tbConfig,data:t.tbData,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,multiSelectedChange:t.tbSelectChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"hsearch_Project_Name",fn:function(e){var r=e.column;return[n("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},on:{change:t.projectChange},model:{value:t.$refs.dyTable.searchedField[r.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,r.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.projects,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"hsearch_Installunit_Name",fn:function(e){e.column;return[n("el-select",{attrs:{disabled:!t.$refs.dyTable.searchedField["Project_Name"],clearable:"",placeholder:"请选择"},on:{change:t.installNameChange},model:{value:t.installName,callback:function(e){t.installName=e},expression:"installName"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.installOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"Process_Status",fn:function(e){var r=e.row;return[r.Status===t.statusOption.unPro.value?n("span",{staticClass:"z-radio-blue"},[t._v(t._s(r.Process_Status))]):t._e(),r.Status===t.statusOption.inPro.value?n("span",{staticClass:"z-radio-yellow"},[t._v(t._s(r.Process_Status))]):t._e(),r.Status===t.statusOption.transfer.value?n("span",{staticClass:"z-radio-y2"},[t._v(t._s(r.Process_Status))]):t._e(),r.Status===t.statusOption.finish.value?n("span",{staticClass:"z-radio-gray"},[t._v(t._s(r.Process_Status))]):t._e()]}},{key:"op",fn:function(e){var r=e.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleSelect(r,!1)}}},[t._v(t._s(t.isFinishPage?"调整分配":"选择分配")+" ")])]}}],null,!1,2699949231)})],1)])]),t.dialogVisible?n("el-dialog",{staticClass:"cs-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.dWidth},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n(t.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":t.queryInfo.Alloction_Status,"process-id":t.processId},on:{close:t.handleClose,refresh:t.fetchData}})],1):t._e()],1),n("support",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"support"}),n("bitch-edit",{ref:"drawer",attrs:{show:t.drawer},on:{"update:show":function(e){t.drawer=e},getList:t.getBatchList}})],1)},a=[]},"4e82":function(t,e,n){"use strict";var r=n("23e7"),a=n("e330"),o=n("59ed"),i=n("7b0b"),u=n("07fa"),s=n("083a"),d=n("577e"),l=n("d039"),c=n("addb"),f=n("a640"),p=n("3f7e"),m=n("99f4"),h=n("1212"),g=n("ea83"),P=[],v=a(P.sort),_=a(P.push),b=l((function(){P.sort(void 0)})),C=l((function(){P.sort(null)})),T=f("sort"),k=!l((function(){if(h)return h<70;if(!(p&&p>3)){if(m)return!0;if(g)return g<603;var t,e,n,r,a="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)P.push({k:e+r,v:n})}for(P.sort((function(t,e){return e.v-t.v})),r=0;r<P.length;r++)e=P[r].k.charAt(0),a.charAt(a.length-1)!==e&&(a+=e);return"DGBEFHACIJK"!==a}})),O=b||!C||!T||!k,y=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:d(e)>d(n)?1:-1}};r({target:"Array",proto:!0,forced:O},{sort:function(t){void 0!==t&&o(t);var e=i(this);if(k)return void 0===t?v(e):v(e,t);var n,r,a=[],d=u(e);for(r=0;r<d;r++)r in e&&_(a,e[r]);c(a,y(t)),n=u(a),r=0;while(r<n)e[r]=a[r++];while(r<d)s(e,r++);return e}})},"56a1":function(t,e,n){"use strict";n.r(e);var r=n("b168"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},"56a2":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{staticClass:"drag-dialog",attrs:{"close-on-click-modal":!1,modal:!1,visible:t.dialogVisible,"custom-class":"cs-dialog",title:"各班组任务统计",width:t.width},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n("v-chart",{ref:"bar",staticStyle:{width:"100%"},attrs:{option:t.option,autoresize:""}})],1)},a=[]},"5aef":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={}},"5c7f":function(t,e,n){"use strict";n.r(e),n.d(e,"INIT_OPTIONS_KEY",(function(){return M})),n.d(e,"LOADING_OPTIONS_KEY",(function(){return D})),n.d(e,"THEME_KEY",(function(){return $})),n.d(e,"UPDATE_OPTIONS_KEY",(function(){return F})),n.d(e,"default",(function(){return q}));var r=n("2b0e"),a=n("ed09");function o(t){t=t||r["default"],t&&!t["__composition_api_installed__"]&&t.use(a["default"])}o(r["default"]);var i=r["default"],u=(r["default"].version,n("1be7")),s=n("88b3"),d=null;function l(t){return d||(d=(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){return setTimeout(t,16)}).bind(window)),d(t)}var c=null;function f(t){c||(c=(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(t){clearTimeout(t)}).bind(window)),c(t)}function p(t){var e=document.createElement("style");return e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t)),(document.querySelector("head")||document.body).appendChild(e),e}function m(t,e){void 0===e&&(e={});var n=document.createElement(t);return Object.keys(e).forEach((function(t){n[t]=e[t]})),n}function h(t,e,n){var r=window.getComputedStyle(t,n||null)||{display:"none"};return r[e]}function g(t){if(!document.documentElement.contains(t))return{detached:!0,rendered:!1};var e=t;while(e!==document){if("none"===h(e,"display"))return{detached:!1,rendered:!1};e=e.parentNode}return{detached:!1,rendered:!0}}var P='.resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',v=0,_=null;function b(t,e){t.__resize_mutation_handler__||(t.__resize_mutation_handler__=k.bind(t));var n=t.__resize_listeners__;if(!n)if(t.__resize_listeners__=[],window.ResizeObserver){var r=t.offsetWidth,a=t.offsetHeight,o=new ResizeObserver((function(){(t.__resize_observer_triggered__||(t.__resize_observer_triggered__=!0,t.offsetWidth!==r||t.offsetHeight!==a))&&y(t)})),i=g(t),u=i.detached,s=i.rendered;t.__resize_observer_triggered__=!1===u&&!1===s,t.__resize_observer__=o,o.observe(t)}else if(t.attachEvent&&t.addEventListener)t.__resize_legacy_resize_handler__=function(){y(t)},t.attachEvent("onresize",t.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",t.__resize_mutation_handler__);else if(v||(_=p(P)),S(t),t.__resize_rendered__=g(t).rendered,window.MutationObserver){var d=new MutationObserver(t.__resize_mutation_handler__);d.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),t.__resize_mutation_observer__=d}t.__resize_listeners__.push(e),v++}function C(t,e){var n=t.__resize_listeners__;if(n){if(e&&n.splice(n.indexOf(e),1),!n.length||!e){if(t.detachEvent&&t.removeEventListener)return t.detachEvent("onresize",t.__resize_legacy_resize_handler__),void document.removeEventListener("DOMSubtreeModified",t.__resize_mutation_handler__);t.__resize_observer__?(t.__resize_observer__.unobserve(t),t.__resize_observer__.disconnect(),t.__resize_observer__=null):(t.__resize_mutation_observer__&&(t.__resize_mutation_observer__.disconnect(),t.__resize_mutation_observer__=null),t.removeEventListener("scroll",O),t.removeChild(t.__resize_triggers__.triggers),t.__resize_triggers__=null),t.__resize_listeners__=null}!--v&&_&&_.parentNode.removeChild(_)}}function T(t){var e=t.__resize_last__,n=e.width,r=e.height,a=t.offsetWidth,o=t.offsetHeight;return a!==n||o!==r?{width:a,height:o}:null}function k(){var t=g(this),e=t.rendered,n=t.detached;e!==this.__resize_rendered__&&(!n&&this.__resize_triggers__&&(R(this),this.addEventListener("scroll",O,!0)),this.__resize_rendered__=e,y(this))}function O(){var t=this;R(this),this.__resize_raf__&&f(this.__resize_raf__),this.__resize_raf__=l((function(){var e=T(t);e&&(t.__resize_last__=e,y(t))}))}function y(t){t&&t.__resize_listeners__&&t.__resize_listeners__.forEach((function(e){e.call(t,t)}))}function S(t){var e=h(t,"position");e&&"static"!==e||(t.style.position="relative"),t.__resize_old_position__=e,t.__resize_last__={};var n=m("div",{className:"resize-triggers"}),r=m("div",{className:"resize-expand-trigger"}),a=m("div"),o=m("div",{className:"resize-contract-trigger"});r.appendChild(a),n.appendChild(r),n.appendChild(o),t.appendChild(n),t.__resize_triggers__={triggers:n,expand:r,expandChild:a,contract:o},R(t),t.addEventListener("scroll",O,!0),t.__resize_last__={width:t.offsetWidth,height:t.offsetHeight}}function R(t){var e=t.__resize_triggers__,n=e.expand,r=e.expandChild,a=e.contract,o=a.scrollWidth,i=a.scrollHeight,u=n.offsetWidth,s=n.offsetHeight,d=n.scrollWidth,l=n.scrollHeight;a.scrollLeft=o,a.scrollTop=i,r.style.width=u+1+"px",r.style.height=s+1+"px",n.scrollLeft=d,n.scrollTop=l}var I=function(){return I=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},I.apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var G=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function w(t){return e=Object.create(null),G.forEach((function(n){e[n]=function(e){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(!t.value)throw new Error("ECharts is not initialized yet.");return t.value[e].apply(t.value,n)}}(n)})),e;var e}var L={autoresize:[Boolean,Object]},x=/^on[^a-z]/,A=function(t){return x.test(t)};function B(t,e){var n=Object(a["isRef"])(t)?Object(a["unref"])(t):t;return n&&"object"==typeof n&&"value"in n?n.value||e:n||e}var D="ecLoadingOptions",j={loading:Boolean,loadingOptions:Object},U=null,z="x-vue-echarts",N=[],E=[];!function(t,e){if(t&&"undefined"!=typeof document){var n,r=!0===e.prepend?"prepend":"append",a=!0===e.singleTag,o="string"==typeof e.container?document.querySelector(e.container):document.getElementsByTagName("head")[0];if(a){var i=N.indexOf(o);-1===i&&(i=N.push(o)-1,E[i]={}),n=E[i]&&E[i][r]?E[i][r]:E[i][r]=u()}else n=u();65279===t.charCodeAt(0)&&(t=t.substring(1)),n.styleSheet?n.styleSheet.cssText+=t:n.appendChild(document.createTextNode(t))}function u(){var t=document.createElement("style");if(t.setAttribute("type","text/css"),e.attributes)for(var n=Object.keys(e.attributes),a=0;a<n.length;a++)t.setAttribute(n[a],e.attributes[n[a]]);var i="prepend"===r?"afterbegin":"beforeend";return o.insertAdjacentElement(i,t),t}}("x-vue-echarts{display:flex;flex-direction:column;width:100%;height:100%;min-width:0}\n.vue-echarts-inner{flex-grow:1;min-width:0;width:auto!important;height:auto!important}\n",{});var W=function(){if(null!=U)return U;if("undefined"==typeof HTMLElement||"undefined"==typeof customElements)return U=!1;try{new Function("tag","class EChartsElement extends HTMLElement {\n  __dispose = null;\n\n  disconnectedCallback() {\n    if (this.__dispose) {\n      this.__dispose();\n      this.__dispose = null;\n    }\n  }\n}\n\nif (customElements.get(tag) == null) {\n  customElements.define(tag, EChartsElement);\n}\n")(z)}catch(t){return U=!1}return U=!0}();i&&i.config.ignoredElements.push(z);var $="ecTheme",M="ecInitOptions",F="ecUpdateOptions",V=/(^&?~?!?)native:/,q=Object(a["defineComponent"])({name:"echarts",props:I(I({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},L),j),emits:{},inheritAttrs:!1,setup:function(t,e){var n=e.attrs,r=Object(a["shallowRef"])(),o=Object(a["shallowRef"])(),i=Object(a["shallowRef"])(),d=Object(a["shallowRef"])(),l=Object(a["inject"])($,null),c=Object(a["inject"])(M,null),f=Object(a["inject"])(F,null),p=Object(a["toRefs"])(t),m=p.autoresize,h=p.manualUpdate,g=p.loading,P=p.loadingOptions,v=Object(a["computed"])((function(){return d.value||t.option||null})),_=Object(a["computed"])((function(){return t.theme||B(l,{})})),T=Object(a["computed"])((function(){return t.initOptions||B(c,{})})),k=Object(a["computed"])((function(){return t.updateOptions||B(f,{})})),O=Object(a["computed"])((function(){return function(t){var e={};for(var n in t)A(n)||(e[n]=t[n]);return e}(n)})),y={},S=Object(a["getCurrentInstance"])().proxy.$listeners,R={};function G(e){if(o.value){var n=i.value=Object(u["l"])(o.value,_.value,T.value);t.group&&(n.group=t.group),Object.keys(R).forEach((function(t){var e=R[t];if(e){var r=t.toLowerCase();"~"===r.charAt(0)&&(r=r.substring(1),e.__once__=!0);var a=n;if(0===r.indexOf("zr:")&&(a=n.getZr(),r=r.substring(3)),e.__once__){delete e.__once__;var o=e;e=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];o.apply(void 0,t),a.off(r,e)}}a.on(r,e)}})),m.value?Object(a["nextTick"])((function(){n&&!n.isDisposed()&&n.resize(),r()})):r()}function r(){var t=e||v.value;t&&n.setOption(t,k.value)}}function L(){i.value&&(i.value.dispose(),i.value=void 0)}S?Object.keys(S).forEach((function(t){V.test(t)?y[t.replace(V,"$1")]=S[t]:R[t]=S[t]})):Object.keys(n).filter((function(t){return A(t)})).forEach((function(t){var e=t.charAt(2).toLowerCase()+t.slice(3);if(0!==e.indexOf("native:"))"Once"===e.substring(e.length-4)&&(e="~".concat(e.substring(0,e.length-4))),R[e]=n[t];else{var r="on".concat(e.charAt(7).toUpperCase()).concat(e.slice(8));y[r]=n[t]}}));var x=null;Object(a["watch"])(h,(function(e){"function"==typeof x&&(x(),x=null),e||(x=Object(a["watch"])((function(){return t.option}),(function(t,e){t&&(i.value?i.value.setOption(t,I({notMerge:t!==e},k.value)):G())}),{deep:!0}))}),{immediate:!0}),Object(a["watch"])([_,T],(function(){L(),G()}),{deep:!0}),Object(a["watchEffect"])((function(){t.group&&i.value&&(i.value.group=t.group)}));var j=w(i);return function(t,e,n){var r=Object(a["inject"])(D,{}),o=Object(a["computed"])((function(){return I(I({},B(r,{})),null==n?void 0:n.value)}));Object(a["watchEffect"])((function(){var n=t.value;n&&(e.value?n.showLoading(o.value):n.hideLoading())}))}(i,g,P),function(t,e,n){var r=null;Object(a["watch"])([n,t,e],(function(t,e,n){var a=t[0],o=t[1],i=t[2];if(a&&o&&i){var u=!0===i?{}:i,d=u.throttle,l=void 0===d?100:d,c=u.onResize,f=function(){o.resize(),null==c||c()};r=l?Object(s["c"])(f,l):f,b(a,r)}n((function(){a&&r&&C(a,r)}))}))}(i,m,o),Object(a["onMounted"])((function(){G()})),Object(a["onBeforeUnmount"])((function(){W&&r.value?r.value.__dispose=L:L()})),I({chart:i,root:r,inner:o,setOption:function(e,n){t.manualUpdate&&(d.value=e),i.value?i.value.setOption(e,n||{}):G(e)},nonEventAttrs:O,nativeListeners:y},j)},render:function(){var t=i?{attrs:this.nonEventAttrs,on:this.nativeListeners}:I(I({},this.nonEventAttrs),this.nativeListeners);return t.ref="root",t.class=t.class?["echarts"].concat(t.class):"echarts",Object(a["h"])(z,t,[Object(a["h"])("div",{ref:"inner",class:"vue-echarts-inner"})])}})},"6c0d":function(t,e,n){"use strict";n.r(e);var r=n("49ed"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},"723f":function(t,e,n){"use strict";n("e615")},7658:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={props:{item:{type:Object,default:function(){return{}}}}}},"7a5dd":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"upload-box"},[t._m(0),n("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"success"},on:{click:function(e){return t.handleSubmit()}}},[t._v("导 入")])],1)],1)},a=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"margin-bottom":"20px"}},[n("strong",{staticStyle:{"font-size":"16px",color:"#222834"}},[t._v("操作方法")]),n("br"),t._v(" 1. 选择要排产的构件；"),n("br"),t._v(" 2. 点击右上角“导出”按钮；"),n("br"),t._v(" 3. 在下载后的excel表格中编辑“"),n("span",{staticClass:"txt-blue"},[t._v("制造班组")]),t._v("”字段后，重新上传。 （"),n("span",{staticClass:"txt-red"},[t._v("其它字段禁止修改")]),t._v("）"),n("br"),t._v(" 4. 编辑好后，在此重新上传导入。 ")])}]},"7d16":function(t,e,n){"use strict";var r=n("97ac");n.d(e,"b",(function(){return r["a"]}));var a=n("f95e");n.d(e,"a",(function(){return a["a"]}))},"7da0":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"s-container"},[t.isBatch?t._e():n("div",{staticClass:"text-center"},[n("span",{staticClass:"cs-title"},[t._v("选择数量")]),n("div",{staticStyle:{"margin-bottom":"20px"}},[t._v("最大可操作数量：")]),n("el-input-number",{attrs:{max:t.maxNum,min:1},model:{value:t.num,callback:function(e){t.num=e},expression:"num"}})],1),n("span",{staticClass:"cs-title"},[t._v("选择班组")]),n("div",{staticClass:"card-container"},t._l(t.list,(function(e){return n("select-box",{key:e.Id,class:{active:e.isCheck},attrs:{item:e},nativeOn:{click:function(n){return n.stopPropagation(),t.itemClick(e)}}})})),1),n("div",{staticClass:"footer"},[n("el-button",{on:{click:t.handleCancel}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",disabled:!t.selectItem,loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)])},a=[]},"7f9d":function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPartTeamProcessAllocation=Et,e.AdjustSubAssemblyTeamProcessAllocation=At,e.AdjustTeamProcessAllocation=xt,e.ApplyCheck=qt,e.BatchReceiveTaskFromStock=Ge,e.BatchReceiveTransferTask=zt,e.BatchWithdrawSimplifiedProcessingHistory=he,e.BeginProcess=M,e.BochuAddTask=Ve,e.CancelNestingBill=Y,e.CancelSchduling=_t,e.CancelTransferTask=Wt,e.CancelUnitSchduling=ne,e.CheckSchduling=Mt,e.ComponentAllocationWorkingTeam=f,e.ComponentAllocationWorkingTeamBase=p,e.CreateCompTransferBill=F,e.CreateCompTransferByTransferCode=Q,e.CreatePartTransferByPartCodes=Z,e.CreatePartTransferByTaskBill=V,e.CreatePartTransferByTransferCode=K,e.DelSchdulingPlan=bt,e.DelSchdulingPlanById=Ct,e.DeleteNestingResult=_e,e.DownNestingTaskTemplate=nt,e.ExportAllocationComponent=k,e.ExportSimplifiedProcessingHistory=je,e.ExportTaskCodeDetails=yt,e.ExportTransferCodeDetail=jt,e.GetAllWorkingTeamComponentCount=h,e.GetAllWorkingTeamComponentCountBase=P,e.GetAllWorkingTeamPartCount=R,e.GetAllWorkingTeamPartCountBase=g,e.GetBuildReturnRecordList=de,e.GetCanSchdulingComps=st,e.GetCanSchdulingPartList=lt,e.GetCheckUserRankList=Le,e.GetCheckingItemList=Ae,e.GetCheckingQuestionList=Be,e.GetCompSchdulingInfoDetail=dt,e.GetCompSchdulingPageList=mt,e.GetCompTaskPageList=ce,e.GetCompTaskPartCompletionStock=Ue,e.GetComponentPartComplexity=v,e.GetCoordinateProcess=C,e.GetDetailSummaryList=Ee,e.GetDwg=ge,e.GetMonthlyFullCheckProducedData=De,e.GetNestingBillBoardPageList=W,e.GetNestingBillDetailList=Ne,e.GetNestingBillPageList=w,e.GetNestingBillTreeList=ze,e.GetNestingFiles=be,e.GetNestingMaterialWithPart=Ye,e.GetNestingPartList=Te,e.GetNestingResultPageList=Pe,e.GetNestingSurplusList=ve,e.GetNestingTaskInfoDetail=U,e.GetPageProcessTransferDetailBase=it,e.GetPageSchdulingComps=ut,e.GetPartPrepareList=Xt,e.GetPartSchdulingCancelHistory=Ut,e.GetPartSchdulingInfoDetail=Kt,e.GetPartSchdulingPageList=ct,e.GetPartTaskBoard=E,e.GetPartWithParentPageList=Je,e.GetProcessAllocationComponentBasePageList=T,e.GetProcessAllocationComponentPageList=u,e.GetProcessAllocationPartBasePageList=y,e.GetProcessAllocationPartPageList=O,e.GetProcessPartTransferDetail=tt,e.GetProcessTransferDetail=X,e.GetProcessTransferPageList=$,e.GetSchdulingCancelHistory=Gt,e.GetSchdulingPageList=pt,e.GetSchdulingPartUsePageList=Ft,e.GetSchdulingWorkingTeams=gt,e.GetSemiFinishedStock=ke,e.GetSimplifiedProcessingHistory=pe,e.GetSimplifiedProcessingSummary=me,e.GetSourceFinishedList=Oe,e.GetStopList=Ze,e.GetTargetReceiveList=ye,e.GetTaskPartPrepareList=te,e.GetTeamCompHistory=at,e.GetTeamPartUseList=Vt,e.GetTeamProcessAllocation=Lt,e.GetTeamStockPageList=ot,e.GetTeamTaskAllocationPageList=wt,e.GetTeamTaskBoardPageList=z,e.GetTeamTaskDetails=Ot,e.GetTeamTaskPageList=kt,e.GetTenantFactoryType=rt,e.GetToReceiveTaskDetailList=Re,e.GetToReceiveTaskList=Se,e.GetTransferCancelDetails=$t,e.GetTransferDetail=Dt,e.GetTransferHistory=Bt,e.GetTransferPageList=N,e.GetUnitSchdulingInfoDetail=Qt,e.GetUnitSchdulingPageList=ht,e.GetWorkingTeamCheckingList=xe,e.GetWorkingTeamComponentCount=m,e.GetWorkingTeamComponentCountBase=d,e.GetWorkingTeamLoadRealTime=le,e.GetWorkingTeamPartCount=I,e.GetWorkingTeamPartCountBase=G,e.GetWorkingTeamsPageList=ue,e.GetYearlyFullCheckProducedData=we,e.ImportNestingFiles=Ce,e.ImportNestingInfo=_,e.ImportSchduling=Pt,e.ImportThumbnail=qe,e.ImportUpdateComponentWorkingTeam=i,e.ImportUpdatePartWorkingTeam=S,e.LentakExport=Me,e.PartsAllocationWorkingTeamBase=x,e.PartsAllocationWrokingTeam=L,e.PartsBatchAllocationWorkingTeamBase=A,e.ProAddQuest=et,e.ProfilesExport=Fe,e.ReceiveTaskFromStock=Ie,e.ReceiveTransferBill=H,e.ReceiveTransferTask=Nt,e.RevocationComponentAllocation=s,e.SaveChangeZeroComponentRecoil=se,e.SaveCompSchdulingDraft=Ht,e.SaveCompTransferBill=q,e.SaveComponentSchedulingWorkshop=re,e.SaveNestingPartInfo=j,e.SavePartSchdulingDraft=Jt,e.SavePartSchdulingDraftNew=Yt,e.SavePartSchedulingWorkshop=ae,e.SavePartSchedulingWorkshopNew=oe,e.SavePartTransferBill=J,e.SaveSchdulingDraft=ft,e.SaveSchdulingTask=vt,e.SaveSchdulingTaskById=Tt,e.SaveUnitSchdulingDraftNew=Zt,e.SaveUnitSchedulingWorkshopNew=ie,e.SigmaWOLExport=$e,e.SimplifiedProcessing=fe,e.TeamProcessingByTaskCode=Rt,e.TeamTaskProcessing=It,e.TeamTaskTransfer=St,e.UpdateBatchCompAllocationWorkingTeamBase=c,e.UpdateBatchPartsAllocationWrokingTeamBase=D,e.UpdateComponentAllocationWorkingTeamBase=l,e.UpdateMachineName=We,e.UpdatePartsAllocationWorkingTeamBase=B,e.UploadNestingFiles=b,e.WithdrawPicking=He,e.WithdrawScheduling=ee;var a=r(n("b775")),o=r(n("4328"));function i(t){return(0,a.default)({url:"/PRO/ProductionTask/ImportUpdateComponentWorkingTeam",method:"post",data:t})}function u(t){return(0,a.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentPageList",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/ProductionTask/RevocationComponentAllocation",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCountBase",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/ProductionTask/UpdateComponentAllocationWorkingTeamBase",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/ProductionTask/UpdateBatchCompAllocationWorkingTeamBase",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeam",method:"post",data:o.default.stringify(t)})}function p(t){return(0,a.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeamBase",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCount",method:"post",data:o.default.stringify(t)})}function h(t){return(0,a.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCount",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCountBase",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCountBase",method:"post",data:t})}function v(t){return(0,a.default)({url:"/PRO/ProductionTask/GetComponentPartComplexity",method:"post",data:o.default.stringify(t)})}function _(t){return(0,a.default)({url:"/PRO/ProductionTask/ImportNestingInfo",method:"post",data:t})}function b(t){return(0,a.default)({url:"/PRO/ProductionTask/UploadNestingFiles",method:"post",data:t})}function C(t){return(0,a.default)({url:"/PRO/ProductionTask/GetCoordinateProcess",method:"post",data:o.default.stringify(t)})}function T(t){return(0,a.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentBasePageList",method:"post",data:t})}function k(t){return(0,a.default)({url:"/PRO/ProductionTask/ExportAllocationComponent",method:"post",data:t})}function O(t){return(0,a.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartPageList",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartBasePageList",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/ProductionTask/ImportUpdatePartWorkingTeam",method:"post",data:t})}function R(t){return(0,a.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCount",method:"post",data:t})}function I(t){return(0,a.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCount",method:"post",data:o.default.stringify(t)})}function G(t){return(0,a.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCountBase",method:"post",data:o.default.stringify(t)})}function w(t){return(0,a.default)({url:"/PRO/ProductionTask/GetNestingBillPageList",method:"post",data:t})}function L(t){return(0,a.default)({url:"/PRO/ProductionTask/PartsAllocationWrokingTeam",method:"post",data:o.default.stringify(t)})}function x(t){return(0,a.default)({url:"/PRO/ProductionTask/PartsAllocationWorkingTeamBase",method:"post",data:o.default.stringify(t)})}function A(t){return(0,a.default)({url:"/PRO/ProductionTask/PartsBatchAllocationWorkingTeamBase",method:"post",data:t})}function B(t){return(0,a.default)({url:"/PRO/ProductionTask/UpdatePartsAllocationWorkingTeamBase",method:"post",data:t})}function D(t){return(0,a.default)({url:"/PRO/ProductionTask/UpdateBatchPartsAllocationWrokingTeamBase",method:"post",data:t})}function j(t){return(0,a.default)({url:"/PRO/ProductionTask/SaveNestingPartInfo",method:"post",data:o.default.stringify(t)})}function U(t){return(0,a.default)({url:"/PRO/ProductionTask/GetNestingTaskInfoDetail",method:"post",data:o.default.stringify(t)})}function z(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamTaskBoardPageList",method:"post",data:t})}function N(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTransferPageList",method:"post",data:t})}function E(t){return(0,a.default)({url:"/PRO/ProductionTask/GetPartTaskBoard",method:"post",data:t})}function W(t){return(0,a.default)({url:"/PRO/ProductionTask/GetNestingBillBoardPageList",method:"post",data:t})}function $(t){return(0,a.default)({url:"/PRO/ProductionTask/GetProcessTransferPageList",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/ProductionTask/BeginProcess",method:"post",data:o.default.stringify(t)})}function F(t){return(0,a.default)({url:"/PRO/ProductionTask/CreateCompTransferBill",method:"post",data:t})}function V(t){return(0,a.default)({url:"/PRO/ProductionTask/CreatePartTransferByTaskBill",method:"post",data:t})}function q(t){return(0,a.default)({url:"/PRO/ProductionTask/SaveCompTransferBill",method:"post",data:t})}function H(t){return(0,a.default)({url:"/PRO/ProductionTask/ReceiveTransferBill",method:"post",data:o.default.stringify(t)})}function J(t){return(0,a.default)({url:"/PRO/ProductionTask/SavePartTransferBill",method:"post",data:t})}function Y(t){return(0,a.default)({url:"/PRO/ProductionTask/CancelNestingBill",method:"post",data:o.default.stringify(t)})}function Z(t){return(0,a.default)({url:"/PRO/ProductionTask/CreatePartTransferByPartCodes",method:"post",data:t})}function K(t){return(0,a.default)({url:"/PRO/ProductionTask/CreatePartTransferByTransferCode",method:"post",data:o.default.stringify(t)})}function Q(t){return(0,a.default)({url:"/PRO/ProductionTask/CreateCompTransferByTransferCode",method:"post",data:t})}function X(t){return(0,a.default)({url:"/PRO/ProductionTask/GetProcessTransferDetail",method:"post",data:o.default.stringify(t)})}function tt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetProcessPartTransferDetail",method:"post",data:o.default.stringify(t)})}function et(t){return(0,a.default)({url:"/PRO/ProductionTask/ProAddQuest",method:"post",data:o.default.stringify(t)})}function nt(t){return(0,a.default)({url:"/PRO/ProductionTask/DownNestingTaskTemplate",method:"post",data:t})}function rt(){return(0,a.default)({url:"/PRO/ProductionTask/GetTenantFactoryType",method:"post"})}function at(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamCompHistory",method:"post",data:t})}function ot(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamStockPageList",method:"post",data:t})}function it(t){return(0,a.default)({url:"/PRO/ProductionTask/GetPageProcessTransferDetailBase",method:"post",data:t})}function ut(t){return(0,a.default)({url:"/PRO/ProductionTask/GetPageSchdulingComps",method:"post",data:t})}function st(t){return(0,a.default)({url:"/PRO/ProductionTask/GetCanSchdulingComps",method:"post",data:t})}function dt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingInfoDetail",method:"post",data:t})}function lt(t){return(0,a.default)({url:"/PRO/nesting/GetCanSchdulingPartList",method:"post",data:t})}function ct(t){return(0,a.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingPageList",method:"post",data:t})}function ft(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SaveSchdulingDraft",method:"post",data:t})}function pt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetSchdulingPageList",method:"post",data:t})}function mt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingPageList",method:"post",data:t})}function ht(t){return(0,a.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingPageList",method:"post",data:t})}function gt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetSchdulingWorkingTeams",method:"post",data:t})}function Pt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/ImportCompSchduling",method:"post",timeout:12e5,data:t})}function vt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTask",method:"post",data:t})}function _t(t){return(0,a.default)({url:"/PRO/ProductionTask/CancelSchduling",method:"post",data:t})}function bt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlan",method:"post",data:t})}function Ct(t){return(0,a.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlanById",method:"post",data:t})}function Tt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTaskById",method:"post",data:t,timeout:12e5})}function kt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamTaskPageList",method:"post",data:t})}function Ot(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamTaskDetails",method:"post",data:t})}function yt(t){return(0,a.default)({url:"/PRO/ProductionTask/ExportTaskCodeDetails",method:"post",data:t})}function St(t,e){return(0,a.default)({url:"/PRO/ProductionTask/TeamTaskTransfer",method:"post",data:t,params:e})}function Rt(t){return(0,a.default)({url:"/PRO/ProductionTask/TeamProcessingByTaskCode",method:"post",data:t})}function It(t){return(0,a.default)({url:"/PRO/ProductionTask/TeamTaskProcessing",method:"post",data:t})}function Gt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetSchdulingCancelHistory",method:"post",data:t})}function wt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamTaskAllocationPageList",method:"post",data:t})}function Lt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamProcessAllocation",method:"post",data:t})}function xt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/AdjustCompTeamProcessAllocation",method:"post",data:t})}function At(t){return(0,a.default)({url:"/PRO/ProductionSchduling/AdjustSubAssemblyTeamProcessAllocation",method:"post",data:t})}function Bt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTransferHistory",method:"post",data:t})}function Dt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTransferDetail",method:"post",data:t})}function jt(t){return(0,a.default)({url:"/PRO/ProductionTask/ExportTransferCodeDetail",method:"post",data:t})}function Ut(t){return(0,a.default)({url:"/PRO/ProductionTask/GetPartSchdulingCancelHistory",method:"post",data:t})}function zt(t){return(0,a.default)({url:"/PRO/ProductionTask/BatchReceiveTransferTask",method:"post",data:o.default.stringify(t)})}function Nt(t){return(0,a.default)({url:"/PRO/ProductionTask/ReceiveTransferTask",method:"post",data:t})}function Et(t){return(0,a.default)({url:"/PRO/ProductionSchduling/AdjustPartTeamProcessAllocation",method:"post",data:t})}function Wt(t){return(0,a.default)({url:"/PRO/ProductionTask/CancelTransferTask",method:"post",data:t})}function $t(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTransferCancelDetails",method:"post",data:t})}function Mt(t){return(0,a.default)({url:"/PRO/ProductionTask/CheckSchduling",method:"post",data:o.default.stringify(t)})}function Ft(t){return(0,a.default)({url:"/PRO/ProductionTask/GetSchdulingPartUsePageList",method:"post",data:t})}function Vt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetTeamPartUseList",method:"post",data:t})}function qt(t){return(0,a.default)({url:"/PRO/ProductionTask/ApplyCheck",method:"post",data:t})}function Ht(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SaveCompSchdulingDraft",method:"post",data:t,timeout:12e5})}function Jt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraft",method:"post",data:t,timeout:12e5})}function Yt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraftNew",method:"post",data:t,timeout:12e5})}function Zt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SaveUnitSchdulingDraftNew",method:"post",data:t,timeout:12e5})}function Kt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingInfoDetail",method:"post",data:t,timeout:12e5})}function Qt(t){return(0,a.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingInfoDetail",method:"post",data:t,timeout:12e5})}function Xt(t){return(0,a.default)({url:"/PRO/ProductionTask/GetPartPrepareList",method:"post",data:t})}function te(t){return(0,a.default)({url:"/pro/productiontask/GetTaskPartPrepareList",method:"post",data:t})}function ee(t){return(0,a.default)({url:"/PRO/ProductionSchduling/WithdrawScheduling",method:"post",data:t})}function ne(t){return(0,a.default)({url:"/PRO/ProductionSchduling/CancelUnitSchduling",method:"post",data:t})}function re(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SaveComponentSchedulingWorkshop",method:"post",data:t})}function ae(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshop",method:"post",data:t})}function oe(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshopNew",method:"post",data:t})}function ie(t){return(0,a.default)({url:"/PRO/ProductionSchduling/SaveUnitSchedulingWorkshopNew",method:"post",data:t})}function ue(t){return(0,a.default)({url:"/PRO/ZeroComponentRecoil/GetWorkingTeamsPageList",method:"post",data:t})}function se(t){return(0,a.default)({url:"/PRO/ZeroComponentRecoil/SaveChangeZeroComponentRecoil",method:"post",data:t})}function de(t){return(0,a.default)({url:"/PRO/ZeroComponentRecoil/GetBuildReturnRecordList",method:"post",data:t})}function le(t){return(0,a.default)({url:"/PRO/ProductionTask/GetWorkingTeamLoadRealTime",method:"post",data:t})}function ce(t){return(0,a.default)({url:"/PRO/ProductionTask/GetCompTaskPageList",method:"post",data:t})}function fe(t){return(0,a.default)({url:"/PRO/ProductionTask/SimplifiedProcessing",method:"post",data:t})}function pe(t){return(0,a.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingHistory",method:"post",data:t})}function me(t){return(0,a.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingSummary",method:"post",data:t})}function he(t){return(0,a.default)({url:"/PRO/ProductionTask/BatchWithdrawSimplifiedProcessingHistory",method:"post",data:t})}function ge(t){return(0,a.default)({url:"/PRO/ProductionTask/GetDwg",method:"post",data:t})}function Pe(t){return(0,a.default)({url:"/PRO/nesting/GetNestingResultPageList",method:"post",data:t})}function ve(t){return(0,a.default)({url:"/PRO/nesting/GetNestingSurplusList",method:"post",data:t})}function _e(t){return(0,a.default)({url:"/PRO/nesting/DeleteNestingResult",method:"post",data:t})}function be(t){return(0,a.default)({url:"/PRO/nesting/GetNestingFiles",method:"post",data:t})}function Ce(t){return(0,a.default)({url:"/PRO/nesting/Import",method:"post",data:t})}function Te(t){return(0,a.default)({url:"/PRO/nesting/GetNestingPartList",method:"post",data:t})}function ke(t){return(0,a.default)({url:"/PRO/productiontask/GetSemiFinishedStock",method:"post",data:t})}function Oe(t){return(0,a.default)({url:"/PRO/productiontask/GetSourceFinishedList",method:"post",data:t})}function ye(t){return(0,a.default)({url:"/PRO/productiontask/GetTargetReceiveList",method:"post",data:t})}function Se(t){return(0,a.default)({url:"/PRO/productiontask/GetToReceiveTaskList",method:"post",data:t})}function Re(t){return(0,a.default)({url:"/PRO/productiontask/GetToReceiveTaskDetailList",method:"post",data:t})}function Ie(t){return(0,a.default)({url:"/PRO/productiontask/ReceiveTaskFromStock",method:"post",data:t})}function Ge(t){return(0,a.default)({url:"/PRO/productiontask/BatchReceiveTaskFromStock",method:"post",data:t})}function we(t){return(0,a.default)({url:"/PRO/InspectionAnalysis/GetYearlyFullCheckProducedData",method:"post",data:t})}function Le(t){return(0,a.default)({url:"/PRO/InspectionAnalysis/GetCheckUserRankList",method:"post",data:t})}function xe(t){return(0,a.default)({url:"/PRO/InspectionAnalysis/GetWorkingTeamCheckingList",method:"post",data:t})}function Ae(t){return(0,a.default)({url:"/PRO/InspectionAnalysis/GetCheckingItemList",method:"post",data:t})}function Be(t){return(0,a.default)({url:"/PRO/InspectionAnalysis/GetCheckingQuestionList",method:"post",data:t})}function De(t){return(0,a.default)({url:"/PRO/InspectionAnalysis/GetMonthlyFullCheckProducedData",method:"post",data:t})}function je(t){return(0,a.default)({url:"/PRO/productiontask/ExportSimplifiedProcessingHistory",method:"post",data:t})}function Ue(t){return(0,a.default)({url:"/PRO/productiontask/GetCompTaskPartCompletionStock",method:"post",data:t})}function ze(t){return(0,a.default)({url:"/Pro/NestingBill/GetPageList",method:"post",data:t})}function Ne(t){return(0,a.default)({url:"/Pro/NestingBill/GetDetailList",method:"post",data:t})}function Ee(t){return(0,a.default)({url:"/Pro/NestingBill/GetDetailSummaryList",method:"post",data:t})}function We(t){return(0,a.default)({url:"/Pro/NestingBill/UpdateMachineName",method:"post",data:t})}function $e(t){return(0,a.default)({url:"/PRO/CustomUssl/SigmaWOLExport",method:"post",data:t})}function Me(t){return(0,a.default)({url:"/PRO/CustomUssl/LentakExport",method:"post",data:t})}function Fe(t){return(0,a.default)({url:"/PRO/CustomUssl/ProfilesExport",method:"post",data:t})}function Ve(t){return(0,a.default)({url:"/PRO/CustomUssl/BochuAddTask",method:"post",data:t})}function qe(t){return(0,a.default)({url:"/Pro/Nesting/ImportThumbnail",method:"post",data:t})}function He(t){return(0,a.default)({url:"/Pro/MaterielPicking/WithdrawPicking",method:"post",data:t})}function Je(t){return(0,a.default)({url:"/PRO/productiontask/GetPartWithParentPageList",method:"post",data:t})}function Ye(t){return(0,a.default)({url:"/PRO/productiontask/GetNestingMaterialWithPart",method:"post",data:t})}function Ze(t){return(0,a.default)({url:"/PRO/MOC/GetStopList",method:"post",data:t})}},"7fd3":function(t,e,n){"use strict";n.r(e);var r=n("27c6"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},9015:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("14d9"),n("e9f5"),n("7d54"),n("a9e3"),n("d3b7"),n("159b");var a=r(n("4686")),o=n("7f9d");e.default={components:{SelectBox:a.default},props:{processId:{type:String,default:""},pageType:{type:Number,default:void 0}},data:function(){return{btnLoading:!1,isBatch:!1,list:[],selectItem:null,num:void 0,maxNum:void 0,rowArray:[],selectedValue:""}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;(0,o.GetWorkingTeamComponentCountBase)({processId:this.processId}).then((function(e){var n=e.IsSucceed,r=e.Message,a=e.Data;n?(t.list=a,t.list.forEach((function(e){t.selectedValue&&t.selectedValue===e.working_team_id?t.$set(e,"isCheck",!0):t.$set(e,"isCheck",!1)}))):t.$message({message:r,type:"error"})}))},handleSelect:function(t,e){var n,r,a;(this.rowArray=t,this.isBatch=e,this.maxNum=null===(n=t[0])||void 0===n?void 0:n.Comp_Sum,this.selectedValue=null===(r=t[0])||void 0===r?void 0:r.Working_Team_Id,e)||(this.num=null===(a=t[0])||void 0===a?void 0:a.Comp_Sum)},itemClick:function(t){this.list.forEach((function(e){e.isCheck=e===t})),this.selectItem=t},handleCancel:function(){this.$emit("close")},handleClear:function(){var t=this;this.$confirm("是否清除当前分配?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.RevocationComponentAllocation)({processId:t.processId,comps:[t.rowArray.Component_Id]}).then((function(e){var n=e.IsSucceed,r=e.Message;n?(t.$message({message:"清除分配成功",type:"success"}),t.$emit("close"),t.$emit("refresh")):t.$message({message:r,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleSubmit:function(){var t=this;if(this.selectItem.id){this.btnLoading=!0;var e,n,r=[];1===this.pageType?this.rowArray.forEach((function(e,n){var a={Installunit_Id:e.Installunit_Id,Process_Id:t.processId,Team_Id:t.selectItem.id,Technology_Id:e.Technology_Id,Comp_Code:e.Comp_Code};t.isBatch?a.Count=e.num:a.Count=t.num,r.push(a)})):this.rowArray.forEach((function(e,n){var a={Installunit_Id:null===e||void 0===e?void 0:e.Installunit_Id,Process_Id:t.processId,Team_Id:t.selectItem.id,Comp_Code:null===e||void 0===e?void 0:e.Comp_Code,Source_Team_Id:null===e||void 0===e?void 0:e.Working_Team_Id};t.isBatch?a.Count=e.num:a.Count=t.num,r.push(a)})),1===this.pageType?(e=o.ComponentAllocationWorkingTeamBase,n=r):this.isBatch?(n=r,e=o.UpdateBatchCompAllocationWorkingTeamBase):(n=r[0],e=o.UpdateComponentAllocationWorkingTeamBase),e(n).then((function(e){var n=e.IsSucceed,r=e.Message;n?(t.$message({message:"调整成功",type:"success"}),t.btnLoading=!1,t.$emit("close"),t.$emit("refresh")):t.$message({message:r,type:"error"})}))}else this.$message({message:"请选择班组",type:"warning"})}}}},a7e9:function(t,e,n){"use strict";n.r(e);var r=n("2e0e"),a=n("56a1");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("01920");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"1f18e0df",null);e["default"]=u.exports},a888:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=r(n("d565")),o=function(t){t.directive("el-drag-dialog",a.default)};window.Vue&&(window["el-drag-dialog"]=a.default,Vue.use(o)),a.default.install=o;e.default=a.default},aa04:function(t,e,n){},ae9d:function(t,e,n){"use strict";n.r(e);var r=n("7d16");n.d(e,"SVGRenderer",(function(){return r["b"]})),n.d(e,"CanvasRenderer",(function(){return r["a"]}))},b168:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("e9f5"),n("7d54"),n("e9c4"),n("b64b"),n("d3b7"),n("159b");e.default={props:{show:{type:Boolean,default:!1}},data:function(){return{input:"",list:[],btnLoading:!1}},computed:{dialogVisible:{get:function(){return this.show},set:function(t){this.$emit("update:show",t)}}},methods:{handleSelect:function(t){var e=this;this.list=JSON.parse(JSON.stringify(t)),this.list.forEach((function(t){e.$set(t,"num",t.Comp_Sum)}))},handleSubmit:function(){this.$emit("getList",this.list)}}}},b2ab:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var a=n("3b8f"),o=n("ae9d"),i=n("e824"),u=n("f235"),s=r(n("5c7f")),d=n("7f9d");(0,a.use)([i.BarChart,u.GridComponent,u.TooltipComponent,u.LegendComponent,u.DatasetComponent,o.CanvasRenderer]);e.default={name:"Support",components:{VChart:s.default},data:function(){return{width:"500px",option:{color:["#298DFF","#3ECC93"],tooltip:{trigger:"axis",formatter:function(t){var e,n=null===(e=t[0])||void 0===e?void 0:e.data,r=n.count,a=n.weight,o=n.pending_count,i=n.pending_weight;return"<span>正在加工  ".concat(o," 个/").concat(i,"Kg</span></br>\n                    <span>等待加工  ").concat(r," 个/").concat(a,"Kg</span>\n                    ")}},grid:{bottom:45},legend:{type:"plain",right:30,textStyle:{color:"#ffffff"}},dataset:{dimensions:["working_team_name","pending_count","count"],source:[]},xAxis:{type:"category",axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#ffffff"}},axisLabel:{}},yAxis:{splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!0,lineStyle:{color:"#ffffff"}}},series:[{name:"正在加工",type:"bar",stack:"正在加工 等待加工",barWidth:16},{name:"等待加工",type:"bar",stack:"正在加工 等待加工",barWidth:16,barGap:"-100%"}]},dialogVisible:!1}},mounted:function(){this.newline(this.option,4,"xAxis")},methods:{fetchData:function(t){var e=this;(0,d.GetAllWorkingTeamComponentCountBase)({processId:t}).then((function(t){var n=t.IsSucceed,r=t.Message,a=t.Data;if(n){e.option.dataset.source=a;var o=88*a.length+190;e.width=(o>500?o:500)+"px",e.$nextTick((function(t){e.dialogVisible=!0}))}else e.$message({message:r,type:"error"})}))},handleOpen:function(t){this.fetchData(t)},handleClose:function(){},getPercentNum:function(t){return t/100*50+"px"},newline:function(t,e,n){return t[n].axisLabel={interval:"auto",margin:10,formatter:function(t){var n="",r=t.length,a=e,o=Math.ceil(r/a);if(r>a)for(var i=0;i<o;i++){var u="",s=i*a,d=s+a;u=i==o-1?t.substring(s,r):t.substring(s,d)+"\n",n+=u}else n=t;return n}},t}}}},b7b0:function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[t._v(" 1 ")])},a=[]},c1ae:function(t,e,n){"use strict";n("aa04")},c6ee:function(t,e,n){"use strict";var r=n("3620");n.d(e,"j",(function(){return r["a"]}));var a=n("4cb5");n.d(e,"a",(function(){return a["a"]}));var o=n("49bba");n.d(e,"o",(function(){return o["a"]}));var i=n("acf6");n.d(e,"r",(function(){return i["a"]}));var u=n("e8e6");n.d(e,"p",(function(){return u["a"]}));var s=n("b37b");n.d(e,"l",(function(){return s["a"]}));var d=n("54ca");n.d(e,"u",(function(){return d["a"]}));var l=n("128d");n.d(e,"v",(function(){return l["a"]}));var c=n("efb0");n.d(e,"h",(function(){return c["a"]}));var f=n("9be8");n.d(e,"g",(function(){return f["a"]}));var p=n("e275");n.d(e,"f",(function(){return p["a"]}));var m=n("7b72");n.d(e,"m",(function(){return m["a"]}));var h=n("10e8e");n.d(e,"q",(function(){return h["a"]}));var g=n("0d95");n.d(e,"b",(function(){return g["a"]}));var P=n("b489");n.d(e,"c",(function(){return P["a"]}));var v=n("2564");n.d(e,"e",(function(){return v["a"]}));var _=n("14bf");n.d(e,"k",(function(){return _["a"]}));var b=n("0eed");n.d(e,"i",(function(){return b["a"]}));var C=n("583f");n.d(e,"n",(function(){return C["a"]}));var T=n("c835b");n.d(e,"t",(function(){return T["a"]}));var k=n("8acb");n.d(e,"s",(function(){return k["a"]}));var O=n("052f");n.d(e,"d",(function(){return O["a"]}))},ccb5:function(t,e,n){"use strict";function r(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;n[r]=t[r]}return n}Object.defineProperty(e,"__esModule",{value:!0}),e.default=r},cfbf:function(t,e,n){},d565:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("caad"),n("ac1f"),n("2532"),n("5319");e.default={bind:function(t,e,n){var r=t.querySelector(".el-dialog__header"),a=t.querySelector(".el-dialog");r.style.cssText+=";cursor:move;",a.style.cssText+=";top:0px;";var o=function(){return window.document.currentStyle?function(t,e){return t.currentStyle[e]}:function(t,e){return getComputedStyle(t,!1)[e]}}();r.onmousedown=function(t){var e=t.clientX-r.offsetLeft,i=t.clientY-r.offsetTop,u=a.offsetWidth,s=a.offsetHeight,d=document.body.clientWidth,l=document.body.clientHeight,c=a.offsetLeft,f=d-a.offsetLeft-u,p=a.offsetTop,m=l-a.offsetTop-s,h=o(a,"left"),g=o(a,"top");h.includes("%")?(h=+document.body.clientWidth*(+h.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(h=+h.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(t){var r=t.clientX-e,o=t.clientY-i;-r>c?r=-c:r>f&&(r=f),-o>p?o=-p:o>m&&(o=m),a.style.cssText+=";left:".concat(r+h,"px;top:").concat(o+g,"px;"),n.child.$emit("dragDialog")},document.onmouseup=function(t){document.onmousemove=null,document.onmouseup=null}}}}},d87c:function(t,e,n){"use strict";n.r(e);var r=n("56a2"),a=n("27ed");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("dbfc");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"2938b115",null);e["default"]=u.exports},da16:function(t,e,n){"use strict";var r=n("8702");n.d(e,"l",(function(){return r["a"]}));var a=n("4b2a");n.d(e,"k",(function(){return a["a"]}));var o=n("bb6f");n.d(e,"t",(function(){return o["a"]}));var i=n("80a9");n.d(e,"u",(function(){return i["a"]}));var u=n("b25d");n.d(e,"i",(function(){return u["a"]}));var s=n("5334");n.d(e,"v",(function(){return s["a"]}));var d=n("4bd9");n.d(e,"s",(function(){return d["a"]}));var l=n("b899");n.d(e,"d",(function(){return l["a"]}));var c=n("5a72");n.d(e,"j",(function(){return c["a"]}));var f=n("3094");n.d(e,"y",(function(){return f["a"]}));var p=n("2da7");n.d(e,"z",(function(){return p["a"]}));var m=n("af5c");n.d(e,"b",(function(){return m["a"]}));var h=n("b22b");n.d(e,"c",(function(){return h["a"]}));var g=n("9394");n.d(e,"x",(function(){return g["a"]}));var P=n("541a");n.d(e,"w",(function(){return P["a"]}));var v=n("a0c6");n.d(e,"r",(function(){return v["a"]}));var _=n("9502");n.d(e,"q",(function(){return _["a"]}));var b=n("4231");n.d(e,"p",(function(){return b["a"]}));var C=n("ff32");n.d(e,"m",(function(){return C["a"]}));var T=n("a6f0");n.d(e,"o",(function(){return T["a"]}));var k=n("ebf2");n.d(e,"n",(function(){return k["a"]}));var O=n("104d");n.d(e,"e",(function(){return O["a"]}));var y=n("e1ff");n.d(e,"f",(function(){return y["a"]}));var S=n("ac12");n.d(e,"g",(function(){return S["a"]}));var R=n("abd2");n.d(e,"B",(function(){return R["a"]}));var I=n("7c0d");n.d(e,"C",(function(){return I["a"]}));var G=n("c436");n.d(e,"D",(function(){return G["a"]}));var w=n("47e7");n.d(e,"a",(function(){return w["a"]}));var L=n("e600");n.d(e,"A",(function(){return L["a"]}));var x=n("5e81");n.d(e,"h",(function(){return x["a"]}))},dbfc:function(t,e,n){"use strict";n("cfbf")},e41b:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteByIds=s,e.GetPartsImportTemplate=l,e.GetPartsList=u,e.GetProjectAreaTreeList=o,e.ImportParts=d,e.SaveProjectAreaSort=i;var a=r(n("b775"));function o(t){return(0,a.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:t})}function u(t){return(0,a.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:t})}},e59c:function(t,e,n){},e601:function(t,e,n){"use strict";n.r(e);var r=n("7a5dd"),a=n("6c0d");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("c1ae");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"07b10dc6",null);e["default"]=u.exports},e615:function(t,e,n){},e824:function(t,e,n){"use strict";n.r(e);var r=n("c6ee");n.d(e,"LineChart",(function(){return r["j"]})),n.d(e,"BarChart",(function(){return r["a"]})),n.d(e,"PieChart",(function(){return r["o"]})),n.d(e,"ScatterChart",(function(){return r["r"]})),n.d(e,"RadarChart",(function(){return r["p"]})),n.d(e,"MapChart",(function(){return r["l"]})),n.d(e,"TreeChart",(function(){return r["u"]})),n.d(e,"TreemapChart",(function(){return r["v"]})),n.d(e,"GraphChart",(function(){return r["h"]})),n.d(e,"GaugeChart",(function(){return r["g"]})),n.d(e,"FunnelChart",(function(){return r["f"]})),n.d(e,"ParallelChart",(function(){return r["m"]})),n.d(e,"SankeyChart",(function(){return r["q"]})),n.d(e,"BoxplotChart",(function(){return r["b"]})),n.d(e,"CandlestickChart",(function(){return r["c"]})),n.d(e,"EffectScatterChart",(function(){return r["e"]})),n.d(e,"LinesChart",(function(){return r["k"]})),n.d(e,"HeatmapChart",(function(){return r["i"]})),n.d(e,"PictorialBarChart",(function(){return r["n"]})),n.d(e,"ThemeRiverChart",(function(){return r["t"]})),n.d(e,"SunburstChart",(function(){return r["s"]})),n.d(e,"CustomChart",(function(){return r["d"]}))},f235:function(t,e,n){"use strict";n.r(e);var r=n("da16");n.d(e,"GridSimpleComponent",(function(){return r["l"]})),n.d(e,"GridComponent",(function(){return r["k"]})),n.d(e,"PolarComponent",(function(){return r["t"]})),n.d(e,"RadarComponent",(function(){return r["u"]})),n.d(e,"GeoComponent",(function(){return r["i"]})),n.d(e,"SingleAxisComponent",(function(){return r["v"]})),n.d(e,"ParallelComponent",(function(){return r["s"]})),n.d(e,"CalendarComponent",(function(){return r["d"]})),n.d(e,"GraphicComponent",(function(){return r["j"]})),n.d(e,"ToolboxComponent",(function(){return r["y"]})),n.d(e,"TooltipComponent",(function(){return r["z"]})),n.d(e,"AxisPointerComponent",(function(){return r["b"]})),n.d(e,"BrushComponent",(function(){return r["c"]})),n.d(e,"TitleComponent",(function(){return r["x"]})),n.d(e,"TimelineComponent",(function(){return r["w"]})),n.d(e,"MarkPointComponent",(function(){return r["r"]})),n.d(e,"MarkLineComponent",(function(){return r["q"]})),n.d(e,"MarkAreaComponent",(function(){return r["p"]})),n.d(e,"LegendComponent",(function(){return r["m"]})),n.d(e,"LegendScrollComponent",(function(){return r["o"]})),n.d(e,"LegendPlainComponent",(function(){return r["n"]})),n.d(e,"DataZoomComponent",(function(){return r["e"]})),n.d(e,"DataZoomInsideComponent",(function(){return r["f"]})),n.d(e,"DataZoomSliderComponent",(function(){return r["g"]})),n.d(e,"VisualMapComponent",(function(){return r["B"]})),n.d(e,"VisualMapContinuousComponent",(function(){return r["C"]})),n.d(e,"VisualMapPiecewiseComponent",(function(){return r["D"]})),n.d(e,"AriaComponent",(function(){return r["a"]})),n.d(e,"TransformComponent",(function(){return r["A"]})),n.d(e,"DatasetComponent",(function(){return r["h"]}))},f2f6:function(t,e,n){"use strict";var r=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=s,e.CheckPlanTime=d,e.DeleteInstallUnit=p,e.GetCompletePercent=v,e.GetEntity=b,e.GetInstallUnitAllInfo=c,e.GetInstallUnitComponentPageList=P,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=l,e.GetInstallUnitList=u,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=_,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=m,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=C;var a=r(n("b775")),o=r(n("4328"));function i(t){return(0,a.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function u(t){return(0,a.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function d(t){return(0,a.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function l(t){return(0,a.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function c(t){return(0,a.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,a.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function p(t){return(0,a.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function m(t){return(0,a.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,a.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,a.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function _(t){return(0,a.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function b(t){return(0,a.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(t)})}function C(t){return(0,a.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},ffa7:function(t,e,n){"use strict";n.r(e);var r=n("b7b0"),a=n("1381");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"d3ac116a",null);e["default"]=u.exports}}]);