(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-c7c11a8a"],{1894:function(e,t,a){},"282a":function(e,t,a){"use strict";a.r(t);var n=a("e8e3"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"2c89":function(e,t,a){"use strict";a.r(t);var n=a("3095"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"2fb0":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"header_tab"},[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"日报",name:"日报"}}),a("el-tab-pane",{attrs:{label:"月报",name:"月报"}})],1)],1),"日报"==e.activeName?a("day-salary"):e._e(),"月报"==e.activeName?a("month-salary"):e._e()],1)])},r=[]},3095:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var r=n(a("5530")),i=n(a("0f97")),o=(a("d51a"),a("fd31"),n(a("641e"))),l=a("ed08"),s=n(a("ced7")),c=a("6186"),d=a("f4e9");t.default={components:{DynamicDataTable:i.default,addDialog:s.default},mixins:[o.default],data:function(){return{dialogVisible:!1,loading:!1,form:{Name:"",Create_Userid:"",dateRange1:"",dateRange2:"",Type:1,PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},userList:[]}},created:function(){this.getBasicData(),this.getFactoryTypeOption("pro_salary_day_list")},mounted:function(){},methods:{getBasicData:function(){var e=this;(0,d.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},fetchData:function(){var e=this;this.loading=!0;var t=(0,r.default)({},this.form);delete t["dateRange1"],delete t["dateRange2"],delete t["PageInfo"],t.File_Begin=this.form.dateRange1?(0,l.parseTime)(this.form.dateRange1[0],"{y}-{m}-{d}"):"",t.File_End=this.form.dateRange1?(0,l.parseTime)(this.form.dateRange1[1],"{y}-{m}-{d}"):"",t.Create_Begin=this.form.dateRange2?(0,l.parseTime)(this.form.dateRange2[0],"{y}-{m}-{d}"):"",t.Create_End=this.form.dateRange2?(0,l.parseTime)(this.form.dateRange2[1],"{y}-{m}-{d}"):"",(0,d.GetFactorySalaryPageList)((0,r.default)((0,r.default)({},t),this.form.PageInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e.Salary_File_Date=e.Salary_File_Date?(0,l.parseTime)(new Date(e.Salary_File_Date),"{y}-{m}-{d}"):e.Salary_File_Date,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).catch(console.error).finally((function(){e.loading=!1}))},datePickerwrapper:function(){},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},handleClose:function(){this.$refs.add.resetForm(),this.dialogVisible=!1},handleInfo:function(e){e&&this.openGetOssUrl(e)},openGetOssUrl:function(e){(0,c.GetOssUrl)({url:e,day:30}).then((function(e){window.open(e.Data)}))},handleImp:function(e,t){var a=this;this.dialogVisible=!0,this.$nextTick((function(){a.$refs.add.init(e,t)}))},handleDel:function(e){var t=this;this.$confirm("删除该工资单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.DeleteSalary)({ids:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))}}}},4512:function(e,t,a){"use strict";a.r(t);var n=a("6d59"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"4e82":function(e,t,a){"use strict";var n=a("23e7"),r=a("e330"),i=a("59ed"),o=a("7b0b"),l=a("07fa"),s=a("083a"),c=a("577e"),d=a("d039"),u=a("addb"),f=a("a640"),m=a("3f7e"),p=a("99f4"),h=a("1212"),g=a("ea83"),y=[],v=r(y.sort),b=r(y.push),_=d((function(){y.sort(void 0)})),S=d((function(){y.sort(null)})),D=f("sort"),P=!d((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var e,t,a,n,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:t+n,v:a})}for(y.sort((function(e,t){return t.v-e.v})),n=0;n<y.length;n++)t=y[n].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),C=_||!S||!D||!P,I=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:c(t)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:C},{sort:function(e){void 0!==e&&i(e);var t=o(this);if(P)return void 0===e?v(t):v(t,e);var a,n,r=[],c=l(t);for(n=0;n<c;n++)n in t&&b(r,t[n]);u(r,I(e)),a=l(r),n=0;while(n<a)t[n]=r[n++];while(n<c)s(t,n++);return t}})},"4f97":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"84px"}},[a("el-form-item",{attrs:{label:"工资单名称",prop:"Name"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"月报时间",prop:"dateRange1"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"monthrange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始月份","end-placeholder":"结束月份","picker-options":e.pickerOptions2},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange1,callback:function(t){e.$set(e.form,"dateRange1",t)},expression:"form.dateRange1"}})],1),a("el-form-item",{attrs:{label:"操作时间",prop:"dateRange2"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange2,callback:function(t){e.$set(e.form,"dateRange2",t)},expression:"form.dateRange2"}})],1),a("el-form-item",{attrs:{label:"操作人名称",prop:"Create_Userid"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Create_Userid,callback:function(t){e.$set(e.form,"Create_Userid",t)},expression:"form.Create_Userid"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleImp(2,"")}}},[e._v("导入工资单")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"op",fn:function(t){var n=t.row,r=t.index;return[a("div",[a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleInfo(n.File_Url)}}},[e._v("查看")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleImp(2,n.Id)}}},[e._v("导入")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleDel(n.Id)}}},[e._v("删除")])],1)]}}])})],1)],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"导入工资单",visible:e.dialogVisible,width:"576px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("add-Dialog",{ref:"add",on:{close:e.handleClose,refresh:e.fetchData}})],1)],1)},r=[]},"5dfc":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"标题",prop:"File_Name",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"360px"},attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.File_Name,callback:function(t){e.$set(e.form,"File_Name",t)},expression:"form.File_Name"}})],1),1==e.form.Type?a("el-form-item",{attrs:{label:"日报日期",prop:"Salary_File_Date",rules:{required:!0,message:"请选择",trigger:"change"}}},[a("el-date-picker",{staticStyle:{width:"360px"},attrs:{type:"date",placeholder:"选择日期"},model:{value:e.form.Salary_File_Date,callback:function(t){e.$set(e.form,"Salary_File_Date",t)},expression:"form.Salary_File_Date"}})],1):e._e(),2==e.form.Type?a("el-form-item",{attrs:{label:"月报月份",prop:"Salary_File_Date",rules:{required:!0,message:"请选择",trigger:"change"}}},[a("el-date-picker",{staticStyle:{width:"360px"},attrs:{type:"month",placeholder:"选择日期"},model:{value:e.form.Salary_File_Date,callback:function(t){e.$set(e.form,"Salary_File_Date",t)},expression:"form.Salary_File_Date"}})],1):e._e(),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:e.allowFile,"file-list":e.fileList,limit:1,"on-success":function(t,a,n){e.uploadSuccess(t,a,n)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.uploadExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("el-form-item",[a("div",{staticClass:"btn-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submitForm()}}},[e._v("提交")])],1)])],1)],1)},r=[]},"641e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,r.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(a){a.IsSucceed?(t.ProfessionalType=a.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:a.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,n.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,r=e.Data,i=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var o=t.tbConfig.Code.split(",");"plm_component_page_list"!==o[0]&&"plm_parts_page_list"!==o[0]||(t.tbConfig.Is_Page=!0),t.columns=(r.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+r.Grid.Row_Number:t.form.PageSize=+r.Grid.Row_Number,a(t.columns),t.fetchData()}else t.$message({message:i,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},"6d59":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var r=n(a("5530")),i=n(a("0f97")),o=(a("d51a"),a("fd31"),n(a("641e"))),l=a("ed08"),s=n(a("ced7")),c=a("6186"),d=a("f4e9");t.default={components:{DynamicDataTable:i.default,addDialog:s.default},mixins:[o.default],data:function(){return{dialogVisible:!1,loading:!1,form:{Name:"",Create_Userid:"",dateRange1:"",dateRange2:"",Type:2,PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},pickerOptions2:{shortcuts:[{text:"本月",onClick:function(e){e.$emit("pick",[new Date,new Date])}},{text:"今年至今",onClick:function(e){var t=new Date,a=new Date((new Date).getFullYear(),0);e.$emit("pick",[a,t])}},{text:"最近六个月",onClick:function(e){var t=new Date,a=new Date;a.setMonth(a.getMonth()-6),e.$emit("pick",[a,t])}}]},userList:[]}},created:function(){this.getBasicData(),this.getFactoryTypeOption("pro_salary_month_list"),this.fetchData()},mounted:function(){},methods:{getBasicData:function(){var e=this;(0,d.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},fetchData:function(){var e=this,t=(0,r.default)({},this.form);delete t["dateRange1"],delete t["dateRange2"],delete t["PageInfo"],t.File_Begin=this.form.dateRange1?(0,l.parseTime)(this.form.dateRange1[0],"{y}-{m}"):"",t.File_End=this.form.dateRange1?(0,l.parseTime)(this.form.dateRange1[1],"{y}-{m}"):"",t.Create_Begin=this.form.dateRange2?(0,l.parseTime)(this.form.dateRange2[0],"{y}-{m}-{d}"):"",t.Create_End=this.form.dateRange2?(0,l.parseTime)(this.form.dateRange2[1],"{y}-{m}-{d}"):"",(0,d.GetFactorySalaryPageList)((0,r.default)((0,r.default)({},t),this.form.PageInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e.Salary_File_Date=e.Salary_File_Date?(0,l.parseTime)(new Date(e.Salary_File_Date),"{y}-{m}"):e.Salary_File_Date,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).catch(console.error).finally((function(){e.loading=!1}))},datePickerwrapper:function(){},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},handleClose:function(){this.$refs.add.resetForm(),this.dialogVisible=!1},handleInfo:function(e){e&&this.openGetOssUrl(e)},openGetOssUrl:function(e){(0,c.GetOssUrl)({url:e,day:30}).then((function(e){window.open(e.Data)}))},handleImp:function(e,t){var a=this;this.dialogVisible=!0,this.$nextTick((function(){a.$refs.add.init(e,t)}))},handleDel:function(e){var t=this;this.$confirm("删除该工资单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.DeleteSalary)({ids:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))}}}},"74e83":function(e,t,a){"use strict";a("1894")},"8fb4":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"84px"}},[a("el-form-item",{attrs:{label:"工资单名称",prop:"Name"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"日报时间",prop:"dateRange1"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange1,callback:function(t){e.$set(e.form,"dateRange1",t)},expression:"form.dateRange1"}})],1),a("el-form-item",{attrs:{label:"操作时间",prop:"dateRange2"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange2,callback:function(t){e.$set(e.form,"dateRange2",t)},expression:"form.dateRange2"}})],1),a("el-form-item",{attrs:{label:"操作人名称",prop:"Create_Userid"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Create_Userid,callback:function(t){e.$set(e.form,"Create_Userid",t)},expression:"form.Create_Userid"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleImp(1,"")}}},[e._v("导入工资单")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"op",fn:function(t){var n=t.row,r=t.index;return[a("div",[a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleInfo(n.File_Url)}}},[e._v("查看")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleImp(1,n.Id)}}},[e._v("导入")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleDel(n.Id)}}},[e._v("删除")])],1)]}}])})],1)],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"导入工资单",visible:e.dialogVisible,width:"576px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("add-Dialog",{ref:"add",on:{close:e.handleClose,refresh:e.fetchData}})],1)],1)},r=[]},a20d:function(e,t,a){},b2d2:function(e,t,a){"use strict";a("a20d")},b46b:function(e,t,a){"use strict";a.r(t);var n=a("2fb0"),r=a("282a");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("c7e30");var o=a("2877"),l=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"3053c520",null);t["default"]=l.exports},b6af:function(e,t,a){"use strict";a("fe13")},bc2b:function(e,t,a){"use strict";a.r(t);var n=a("4f97"),r=a("4512");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("b2d2");var o=a("2877"),l=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"60b0d16f",null);t["default"]=l.exports},c7e30:function(e,t,a){"use strict";a("dcb7")},cb8f:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("ab43"),a("d3b7");var r=a("f4e9"),i=a("ed08"),o=n(a("bbc2")),l=a("6186");t.default={components:{OSSUpload:o.default},data:function(){return{form:{Type:"",Id:"",File_Name:"",Salary_File_Date:new Date},userList:[],allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],Attachments:[],btnLoading:!1}},mounted:function(){},methods:{init:function(e,t){this.form.Type=e,this.form.Id=t},uploadSuccess:function(e,t,a){var n=this;this.Attachments=[],a.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl.split("?")[0]:t.File_Url=e.url,n.Attachments.push(t)})),this.form.File_Name=this.Attachments[0].File_Name},uploadExceed:function(e,t){this.$message({type:"warning",message:"已超过文件上传最大数量"})},uploadRemove:function(e,t){var a=this;this.Attachments=[],t.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl.split("?")[0]:t.File_Url=e.url,a.Attachments.push(t)})),this.form.File_Name=""},handlePreview:function(e){var t=null;t=e.hasOwnProperty("response")?e.response.encryptionUrl.split("?")[0]:e.url,this.openGetOssUrl(t)},openGetOssUrl:function(e){(0,l.GetOssUrl)({url:e,day:30}).then((function(e){window.open(e.Data)}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;if(e.btnLoading=!0,0==e.Attachments.length)return e.$message({type:"warning",message:"请上传工资单文件"}),void(e.btnLoading=!1);var a={Id:e.form.Id||"",Type:e.form.Type||"",Salary_File_Date:e.form.Salary_File_Date?(0,i.parseTime)(new Date(e.form.Salary_File_Date),"{y}-{m}-{d}"):"",File_Name:e.form.File_Name,File_Url:e.Attachments[0].File_Url};2==e.form.Type&&(a.Salary_File_Date=e.form.Salary_File_Date?(0,i.parseTime)(new Date(e.form.Salary_File_Date),"{y}-{m}"):""),e.form.Id?(0,r.UpdateImportSalaryFile)(a).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"提交成功"}),e.$emit("close"),e.$emit("refresh")):e.$message({type:"warning",message:t.Message})})).catch(console.error).finally((function(){e.btnLoading=!1})):(0,r.ImportSalaryFiles)([a]).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"提交成功"}),e.$emit("close"),e.$emit("refresh")):e.$message({type:"warning",message:t.Message})})).catch(console.error).finally((function(){e.btnLoading=!1}))}))},resetForm:function(){this.form.Id="",this.form.Type="",this.form.File_Name="",this.form.Salary_File_Date=new Date,this.fileList=[],this.Attachments=[]}}}},ced7:function(e,t,a){"use strict";a.r(t);var n=a("5dfc"),r=a("e647");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("74e83");var o=a("2877"),l=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"11e21ba7",null);t["default"]=l.exports},d51a:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddLanch=l,t.BatchManageSaveCheck=v,t.DelLanch=y,t.DeleteToleranceSetting=O,t.EntityQualityManagement=u,t.ExportQISummary=k,t.GetCheckingEntity=_,t.GetCompPartForSpotCheckPageList=C,t.GetCompQISummary=w,t.GetDictionaryDetailListByCode=s,t.GetEditById=b,t.GetFactoryPeoplelist=m,t.GetNodeList=c,t.GetPageFeedBack=S,t.GetPageQualityManagement=i,t.GetPartAndSteelBacrode=d,t.GetSheetDwg=p,t.GetSpotCheckingEntity=I,t.GetToleranceSettingList=T,t.ImportQISummary=F,t.ManageAdd=o,t.RectificationRecord=P,t.SaveFeedBack=D,t.SavePass=h,t.SaveQIReportData=x,t.SaveTesting=f,t.SaveToleranceSetting=R,t.SubmitLanch=g;var r=n(a("b775"));function i(e){return(0,r.default)({url:"/PRO/Inspection/GetPageQualityManagement",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/Inspection/ManageAdd",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:e})}function s(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Inspection/EntityQualityManagement",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Inspection/SaveTesting",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Inspection/GetSheetDwg",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Inspection/SavePass",method:"post",data:e,timeout:12e5})}function g(e){return(0,r.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Inspection/BatchManageSaveCheck",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckingEntity",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Inspection/GetPageFeedBack",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/Inspection/SaveFeedBack",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Inspection/GetCompPartForSpotCheckPageList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Inspection/GetSpotCheckingEntity",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Inspection/GetCompQISummary",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/Inspection/ExportQISummary",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Inspection/ImportQISummary",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/Inspection/SaveQIReportData",method:"post",data:e})}function R(e){return(0,r.default)({url:"/pro/Inspection/SaveToleranceSetting",method:"post",data:e})}function O(e){return(0,r.default)({url:"/pro/Inspection/DeleteToleranceSetting",method:"post",data:e})}function T(e){return(0,r.default)({url:"/pro/Inspection/GetToleranceSettingList",method:"post",data:e})}},d5e95:function(e,t,a){"use strict";a.r(t);var n=a("8fb4"),r=a("2c89");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("b6af");var o=a("2877"),l=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"0b551a72",null);t["default"]=l.exports},dcb7:function(e,t,a){},e647:function(e,t,a){"use strict";a.r(t);var n=a("cb8f"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},e8e3:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("d5e95")),i=n(a("bc2b"));t.default={name:"PROStartInspect",components:{daySalary:r.default,monthSalary:i.default},data:function(){return{activeName:"日报"}}}},f4e9:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteQuotaList=u,t.DeleteSalary=l,t.ExportPlanSalary=g,t.GetFactoryPeoplelist=c,t.GetFactorySalaryPageList=s,t.GetPlanSalaryDetailList=h,t.GetPlanSalaryPageList=p,t.GetQuotaDetail=m,t.GetQuotaPageList=f,t.ImportSalaryFiles=i,t.SaveQuotaEntity=d,t.UpdateImportSalaryFile=o;var r=n(a("b775"));n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/Factory/ImportSalaryFiles",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/Factory/UpdateImportSalaryFile",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Factory/DeleteSalary",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Factory/GetFactorySalaryPageList",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/ProductionSalary/SaveQuotaEntity",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ProductionSalary/DeleteQuotaList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetQuotaPageList",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetQuotaDetail",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetPlanSalaryPageList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetPlanSalaryDetailList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProductionSalary/ExportPlanSalary",method:"post",data:e})}},fe13:function(e,t,a){}}]);