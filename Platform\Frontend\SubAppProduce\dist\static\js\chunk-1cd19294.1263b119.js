(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-1cd19294"],{"28d76":function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return o}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"abs100 cs-z-flex-pd16-wrap",attrs:{"element-loading-text":"打印数据生成中"}},[n("div",{staticClass:"top-btn",on:{click:t.toBack}},[n("el-button",[t._v("返回")])],1),n("div",{staticClass:"top-btn-print",on:{click:t.printEvent}},[n("el-button",{attrs:{type:"primary"}},[t._v("打印")])],1),n("div",{staticClass:"cs-z-page-main-content"},[n("el-form",{ref:"form",attrs:{inline:"","label-width":"140px"}},[n("el-row",[n("el-col",{attrs:{span:20}},[n("el-form-item",{attrs:{label:"项目名称/区域："}},[t._v(" "+t._s(t.info.Project_Name)+"/"+t._s(t.info.Area_Name)+" ")]),n("el-form-item",{attrs:{label:"排产单号："}},[t._v(" "+t._s(t.info.Schduling_Code)+" ")]),n("el-form-item",{attrs:{label:"加工班组："}},[t._v(" "+t._s(t.info.Working_Team_Name)+" ")]),n("el-form-item",{attrs:{label:"任务下达时间："}},[t._v(" "+t._s(t.info.Order_Date)+" ")]),n("el-form-item",{attrs:{label:"任务单号："}},[t._v(" "+t._s(t.info.Task_Code)+" ")]),n("el-form-item",{attrs:{label:"工序要求完成时间："}},[t._v(" "+t._s(t.info.Process_Finish_Date)+" ")])],1),n("el-col",{attrs:{span:4}},[n("qrcode-vue",{ref:"qrcodeRef",attrs:{size:79,value:"T="+t.info.Task_Code+"&C="+t.Tenant_Code,"class-name":"qrcode",level:"H"}})],1)],1)],1),n("div",{staticClass:"tb-x"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","print-config":t.printConfig,"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:t.tbLoading,stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0},"cell-class-name":t.cellClassName},on:{"cell-click":t.cellClickEvent}},[t._l(t.columns,(function(t){return[n("vxe-column",{key:t.Id,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)],1)],1)])},o=[]},"4b76":function(t,e,n){"use strict";n("e85c")},"4ec9":function(t,e,n){"use strict";n("6f48")},"6f48":function(t,e,n){"use strict";var i=n("6d61"),o=n("6566");i("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),o)},"753e":function(t,e,n){"use strict";n.r(e);var i=n("28d76"),o=n("a231");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("4b76");var r=n("2877"),s=Object(r["a"])(o["default"],i["a"],i["b"],!1,null,"784054f2",null);e["default"]=s.exports},a231:function(t,e,n){"use strict";n.r(e);var i=n("e8d6"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},e85c:function(t,e,n){},e8d6:function(t,e,n){"use strict";var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("4de4"),n("a630"),n("a15b"),n("d81d"),n("e9f5"),n("910d"),n("ab43"),n("4ec9"),n("b64b"),n("d3b7"),n("3ca3"),n("ddb0");var o=i(n("b85c")),a=i(n("c14f")),r=i(n("1da1")),s=i(n("5530")),l=i(n("15ac")),c=n("7f9d"),d=n("ed08"),u=i(n("d7b0")),f=n("2f62"),m="\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-row-first {\n          margin-bottom: 10px;\n        }\n        .my-list-col {\n          width:30%;\n          display: inline-block;\n          float: left;\n          margin-right: 1%;\n          word-wrap:break-word;\n          word-break:normal;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        .cs-img{\n          position:relative;\n          right:30px\n        }\n        ";e.default={name:"PROTaskListDetailPrint",components:{QrcodeVue:u.default},mixins:[l.default],data:function(){var t=this;return{printData:[],tbConfig:{Op_Width:120},tbLoading:!1,tbData:[],columns:[],pageType:"",command:"",printColumns:[],info:{Task_Code:"",Project_Name:"",Area_Name:"",InstallUnit_Name:"",Schduling_Code:"",Task_Finish_Date:"",Finish_Date2:"",Order_Date:"",Working_Team_Name:"",Working_Process_Name:""},printConfig:{sheetName:"任务单详情",style:m,beforePrintMethod:function(e){var n=e.content;return t.topHtml+n}},Tenant_Code:localStorage.getItem("tenant")}},computed:(0,s.default)((0,s.default)({},(0,f.mapGetters)("tenant",["isVersionFour"])),{},{isCom:function(){return"com"===this.pageType},isUnitPart:function(){return"unitPart"===this.pageType},isPart:function(){return"part"===this.pageType}}),mounted:function(){var t=this;return(0,r.default)((0,a.default)().m((function e(){var n;return(0,a.default)().w((function(e){while(1)switch(e.n){case 0:return t.pageType=t.$route.query.type,t.command=t.$route.query.command,t.info=JSON.parse(decodeURIComponent(t.$route.query.other)),e.n=1,t.getTableConfig(t.isCom?"PROComTaskListDetail":t.isUnitPart?"PROUnitPartTaskListDetail":"PROPartTaskListDetail");case 1:return t.isCom&&(t.columns=t.columns.filter((function(t){return"Part_Code"!==t.Code})),t.printColumns=t.columns.filter((function(t){return"Part_Code"!==t.Code&&"Comp_Description"!==t.Code})),"code"===t.command&&(t.columns=t.columns.filter((function(t){return"Comp_Code"!==t.Code&&"Part_Code"!==t.Code})),t.printColumns=t.columns.filter((function(t){return"Comp_Code"!==t.Code&&"Comp_Description"!==t.Code&&"Part_Code"!==t.Code})))),t.isUnitPart&&(t.printColumns=t.columns.filter((function(t){return"Project_Name"!==t.Code&&"Area_Name"!==t.Code&&"Finish_Count"!==t.Code&&"Finish_Weight"!==t.Code&&"Comp_Description"!==t.Code}))),t.isPart&&(t.printColumns=t.columns.filter((function(t){return"Project_Name"!==t.Code&&"Area_Name"!==t.Code&&"Finish_Count"!==t.Code&&"Finish_Weight"!==t.Code&&"Comp_Description"!==t.Code}))),e.n=2,t.fetchData(t.info.Task_Code,t.info.Working_Team_Id);case 2:n=e.v,t.isCom&&"code"===t.command?t.tbData=t.mergeSimilarItems(n):t.tbData=n,t.getHtml(),t.printEvent();case 3:return e.a(2)}}),e)})))()},methods:{fetchData:function(t,e,n){var i=this;return(0,r.default)((0,a.default)().m((function n(){var o,r;return(0,a.default)().w((function(n){while(1)switch(n.n){case 0:return i.tbLoading=!0,n.n=1,(0,c.GetTeamTaskDetails)({Page:-1,PageSize:-1,Process_Type:i.isCom?2:i.isPart?1:3,Working_Team_Id:e,Task_Code:t,Next_Team_Id:"",Next_Process_Id:""});case 1:if(o=n.v,!o.IsSucceed){n.n=2;break}return r=o.Data.Data.filter((function(t){return 0!==t.Allocation_Count})),i.tbLoading=!1,n.a(2,r);case 2:i.tbLoading=!1;case 3:return n.a(2)}}),n)})))()},mergeSimilarItems:function(t){var e,n=new Map,i=(0,o.default)(t);try{for(i.s();!(e=i.n()).done;){var a=e.value;if(null!=a.Steel_Code&&""!==a.Steel_Code){var r=[a.Steel_Code,a.Spec,a.Weight,a.Main_Part,a.AttachedBoardsNumber,a.Length,a.Texture,a.Next_Process_Name,a.Next_Working_Team_Names,a.InstallUnit_Name].join("|");if(n.has(r)){var l=n.get(r);l.Allocation_Weight+=a.Allocation_Weight||0,l.Finish_Weight+=a.Finish_Weight||0,l.Allocation_Count+=a.Allocation_Count||0,l.Finish_Count+=a.Finish_Count||0}else{var c=(0,s.default)((0,s.default)({},a),{},{Allocation_Weight:a.Allocation_Weight||0,Finish_Weight:a.Finish_Weight||0,Allocation_Count:a.Allocation_Count||0,Finish_Count:a.Finish_Count||0});n.set(r,c)}}else{var d=(0,s.default)((0,s.default)({},a),{},{Allocation_Weight:a.Allocation_Weight||0,Finish_Weight:a.Finish_Weight||0,Allocation_Count:a.Allocation_Count||0,Finish_Count:a.Finish_Count||0});n.set("null_".concat(n.size),d)}}}catch(u){i.e(u)}finally{i.f()}return Array.from(n.values())},handleReset:function(){this.$refs["form"].resetFields()},getHtml:function(){var t=this,e=this.$refs["qrcodeRef"];return new Promise((function(n,i){t.$nextTick((function(i){var o,a=e.$refs["qrcode-vue"],r=a.toDataURL("image/png");t.topHtml='\n        <h1 class="title">#'.concat(t.info.Working_Process_Name||"",'# 加工任务单</h1>\n        <div class="my-top">\n          <div class="left">\n            <div class="my-list-row my-list-row-first">\n              <div class="my-list-col">项目名称/区域：').concat(t.info.Project_Name||"","/").concat(t.info.Area_Name||"",'</div>\n              <div class="my-list-col">排产单号：').concat(t.info.Schduling_Code||"",'</div>\n              <div class="my-list-col">加工班组：').concat(t.info.Working_Team_Name||"",'</div>\n            </div>\n            <div class="my-list-row">\n              <div class="my-list-col">任务下单时间：').concat(t.info.Order_Date||"",'</div>\n              <div class="my-list-col">任务单号：').concat((null===(o=t.info)||void 0===o?void 0:o.Task_Code)||"",'</div>\n              <div class="my-list-col">工序要求完成时间：').concat(t.info.Process_Finish_Date||"",'</div>\n            </div>\n          </div>\n          <div class="right">\n           <img class="cs-img" src="').concat(r,'" alt="">\n          </div>\n        </div>\n        '),n()}))}))},printEvent:function(){var t=this;this.getHtml().then((function(e){t.$refs.xTable.print({sheetName:t.printConfig.sheetName,style:m,mode:"current",columns:t.printColumns.map((function(t){return{field:t.Code}})),beforePrintMethod:function(e){var n=e.content,i=t.topHtml+n;return i}})}))},handleView:function(t){},toBack:function(){(0,d.closeTagView)(this.$store,this.$route)},cellClickEvent:function(t){t.row,t.rowIndex,t.column,t.columnIndex},cellClassName:function(t){t.row,t.rowIndex,t.column,t.columnIndex}}}}}]);