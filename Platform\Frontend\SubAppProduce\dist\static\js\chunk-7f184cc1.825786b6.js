(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7f184cc1"],{"459e":function(e,t,a){},"4e82":function(e,t,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),i=a("7b0b"),c=a("07fa"),l=a("083a"),d=a("577e"),s=a("d039"),u=a("addb"),f=a("a640"),p=a("3f7e"),h=a("99f4"),m=a("1212"),g=a("ea83"),y=[],P=r(y.sort),v=r(y.push),_=s((function(){y.sort(void 0)})),C=s((function(){y.sort(null)})),b=f("sort"),I=!s((function(){if(m)return m<70;if(!(p&&p>3)){if(h)return!0;if(g)return g<603;var e,t,a,n,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:t+n,v:a})}for(y.sort((function(e,t){return t.v-e.v})),n=0;n<y.length;n++)t=y[n].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),S=_||!C||!b||!I,D=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:d(t)>d(a)?1:-1}};n({target:"Array",proto:!0,forced:S},{sort:function(e){void 0!==e&&o(e);var t=i(this);if(I)return void 0===e?P(t):P(t,e);var a,n,r=[],d=c(t);for(n=0;n<d;n++)n in t&&v(r,t[n]);u(r,D(e)),a=c(r),n=0;while(n<a)t[n]=r[n++];while(n<d)l(t,n++);return t}})},"5a55":function(e,t,a){"use strict";a("459e")},"641e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,r.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(a){a.IsSucceed?(t.ProfessionalType=a.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:a.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,n.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=t.tbConfig.Code.split(",");"plm_component_page_list"!==i[0]&&"plm_parts_page_list"!==i[0]||(t.tbConfig.Is_Page=!0),t.columns=(r.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+r.Grid.Row_Number:t.form.PageSize=+r.Grid.Row_Number,a(t.columns),t.fetchData()}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},8639:function(e,t,a){"use strict";a.r(t);var n=a("dda9"),r=a("956c");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("5a55");var i=a("2877"),c=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"b7ba6982",null);t["default"]=c.exports},"956c":function(e,t,a){"use strict";a.r(t);var n=a("ce64"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},ce64:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530"));a("d81d"),a("14d9"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var o=n(a("0f97")),i=(a("d51a"),a("fd31"),n(a("641e"))),c=a("ed08"),l=n(a("2082")),d=a("ecb3"),s=a("1b69");t.default={components:{DynamicDataTable:o.default},mixins:[i.default,l.default],data:function(){return{loading:!1,form:{Bill_No:"",Moc_Type_Id:"",Sys_Project_Id:"",Handler_Userid:"",Create_Userid:"",dateRange:"",PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},addPageArray:[{path:this.$route.path+"/addChange",hidden:!0,component:function(){return a.e("chunk-8eb1768c").then(a.bind(null,"d6e9"))},name:"PROAddChange",meta:{title:"变更单详情"}},{path:this.$route.path+"/typeMaintenance",hidden:!0,component:function(){return a.e("chunk-426d44e3").then(a.bind(null,"3f06"))},name:"PROTypeMaintenance",meta:{title:"类型维护"}}],typeList:[],projectList:[],userList:[]}},created:function(){this.getFactoryTypeOption("pro_change_management_list"),this.getBasicData()},mounted:function(){},methods:{getBasicData:function(){var e=this;(0,d.GetChangeType)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.typeList=t.Data.Data)})),(0,s.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)})),(0,d.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},fetchData:function(){var e=this;this.loading=!0;var t=(0,r.default)({},this.form);delete t["dateRange"],delete t["PageInfo"],t.Create_Begin=this.form.dateRange?(0,c.parseTime)(this.form.dateRange[0],"{y}-{m}-{d}"):"",t.Create_End=this.form.dateRange?(0,c.parseTime)(this.form.dateRange[1],"{y}-{m}-{d}"):"",(0,d.GetChangeOrderPageList)((0,r.default)((0,r.default)({},t),this.form.PageInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Create_Date=e.Create_Date?(0,c.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e})),e.total=t.Data.TotalCount,e.loading=!1):e.$message({message:t.Message,type:"error"})}))},datePickerwrapper:function(){},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},handleChange:function(e,t){var a={Id:e,type:t};this.$router.push({name:"PROAddChange",query:{pg_redirect:"PROChangeManagement",data:a}})},handleType:function(){this.$router.push({name:"PROTypeMaintenance",query:{pg_redirect:"PROChangeManagement"}})},handleInfo:function(e){},handleDel:function(e){var t=this;this.$confirm("删除选中类型, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="";e||t.selectList.forEach((function(e){a+=e.Id+","})),(0,d.DeleteChangeOrder)({ids:e||a}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},multiSelectedChange:function(e){this.selectList=e}}}},d51a:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddLanch=c,t.BatchManageSaveCheck=P,t.DelLanch=y,t.DeleteToleranceSetting=w,t.EntityQualityManagement=u,t.ExportQISummary=R,t.GetCheckingEntity=_,t.GetCompPartForSpotCheckPageList=S,t.GetCompQISummary=O,t.GetDictionaryDetailListByCode=l,t.GetEditById=v,t.GetFactoryPeoplelist=p,t.GetNodeList=d,t.GetPageFeedBack=C,t.GetPageQualityManagement=o,t.GetPartAndSteelBacrode=s,t.GetSheetDwg=h,t.GetSpotCheckingEntity=D,t.GetToleranceSettingList=L,t.ImportQISummary=k,t.ManageAdd=i,t.RectificationRecord=I,t.SaveFeedBack=b,t.SavePass=m,t.SaveQIReportData=G,t.SaveTesting=f,t.SaveToleranceSetting=T,t.SubmitLanch=g;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/PRO/Inspection/GetPageQualityManagement",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Inspection/ManageAdd",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:e})}function l(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Inspection/EntityQualityManagement",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Inspection/SaveTesting",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Inspection/GetSheetDwg",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Inspection/SavePass",method:"post",data:e,timeout:12e5})}function g(e){return(0,r.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Inspection/BatchManageSaveCheck",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckingEntity",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Inspection/GetPageFeedBack",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Inspection/SaveFeedBack",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Inspection/GetCompPartForSpotCheckPageList",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/Inspection/GetSpotCheckingEntity",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Inspection/GetCompQISummary",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/Inspection/ExportQISummary",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/Inspection/ImportQISummary",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/Inspection/SaveQIReportData",method:"post",data:e})}function T(e){return(0,r.default)({url:"/pro/Inspection/SaveToleranceSetting",method:"post",data:e})}function w(e){return(0,r.default)({url:"/pro/Inspection/DeleteToleranceSetting",method:"post",data:e})}function L(e){return(0,r.default)({url:"/pro/Inspection/GetToleranceSettingList",method:"post",data:e})}},dda9:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"84px"}},[a("el-form-item",{attrs:{label:"变更单号",prop:"Bill_No"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No",t)},expression:"form.Bill_No"}})],1),a("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.typeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"处理人",prop:"Handler_Userid"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Handler_Userid,callback:function(t){e.$set(e.form,"Handler_Userid",t)},expression:"form.Handler_Userid"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"操作时间",prop:"dateRange"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange,callback:function(t){e.$set(e.form,"dateRange",t)},expression:"form.dateRange"}})],1),a("el-form-item",{attrs:{label:"操作人",prop:"Create_Userid"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Create_Userid,callback:function(t){e.$set(e.form,"Create_Userid",t)},expression:"form.Create_Userid"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleChange("",0)}}},[e._v("发起变更")]),a("el-button",{on:{click:function(t){return e.handleType()}}},[e._v("类型维护")]),a("el-button",{attrs:{disabled:0==e.selectList.length},on:{click:function(t){return e.handleDel()}}},[e._v("批量删除")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Handler_User",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Handler_User||"-"))])]}},{key:"Cc_Name",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Cc_Name||"-"))])]}},{key:"op",fn:function(t){var n=t.row,r=t.index;return[a("div",[a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleChange(n.Id,1)}}},[e._v("查看")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleChange(n.Id,2)}}},[e._v("编辑")]),a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleDel(n.Id)}}},[e._v("删除")])],1)]}}])})],1)],1)])])])])},r=[]},ecb3:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteChangeOrder=u,t.DeleteChangeType=c,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=d,t.GetChangeType=o,t.GetFactoryPeoplelist=l,t.SaveChangeOrder=s,t.SaveChangeType=i;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}}}]);