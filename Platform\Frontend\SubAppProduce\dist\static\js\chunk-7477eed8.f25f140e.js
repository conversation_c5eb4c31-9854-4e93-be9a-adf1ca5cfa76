(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7477eed8"],{"0284":function(t,e,a){},1162:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i=a("7757"),n=a("4f39");e.default={props:{statisticalDate:{type:String,default:""},factoryId:{type:String,required:!0},reportType:{type:Number,required:!0}},data:function(){return{showTip:!1,time:"",curDate:""}},watch:{statisticalDate:function(t){this.curDate=t,this.curDate&&this.getUpdate()}},methods:{getUpdate:function(){var t=this;(0,i.GetBusinessLastUpdateDate)({StatisticalDate:this.curDate,FactoryId:this.factoryId,ReportType:this.reportType}).then((function(e){if(e.IsSucceed){var a=e.Data,i=a.Business_Last_Date,r=a.Account_Generate_Date,o=new Date(i),l=new Date(r);o>l?(t.showTip=!0,t.time=(0,n.parseTime)(o)):(t.showTip=!1,t.time="")}else t.$message({message:e.Message,type:"error"})}))}}}},"1b7e":function(t,e,a){"use strict";a.r(e);var i=a("1162"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"4f39":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=r,e.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var n=i(a("53ca"));function r(t,e){if(0===arguments.length||!t)return null;var a,i=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,n.default)(t)?a=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),a=new Date(t));var r={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=i.replace(/{([ymdhisa])+}/g,(function(t,e){var a=r[e];return"a"===e?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var a=t.split("~"),i=r(new Date(a[0]),e)+" ~ "+r(new Date(a[1]),e);return i}return t&&t.length>0?r(new Date(t),e):void 0}},"52fe":function(t,e,a){"use strict";a("0284")},"7acec":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:t.loading},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("UpdateDate",{ref:"updateDate",attrs:{"statistical-date":t.form.StatisticalDate,"factory-id":t.form.FactoryId,"report-type":1}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"btn-x"},[t.isView&&t.unCheck&&t.getRoles("OMAMCUpdate")?a("el-button",{attrs:{disabled:t.loading,loading:t.updateBtn},on:{click:t.handleUpdate}},[t._v("更新数据")]):t._e(),t.getRoles("OMAMCDetailExport")?a("el-button",{attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e(),t.unCheck&&t.getRoles("OMAMCSubmit")?a("el-button",{attrs:{disabled:t.loading,type:"primary"},on:{click:t.handleSubmit}},[t._v("提交核算")]):t._e(),t.toBeConfirmed&&t.getRoles("OMAMCCheck")?[a("el-button",{attrs:{type:"danger",disabled:t.loading},on:{click:function(e){return t.handleCheck(!1)}}},[t._v("审核不通过")]),a("el-button",{attrs:{type:"success",disabled:t.loading},on:{click:function(e){return t.handleCheck(!0)}}},[t._v("审核通过")])]:t._e()],2)],1),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},n=[]},"90f5":function(t,e,a){"use strict";a.r(e);var i=a("b5756"),n=a("1b7e");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("e209");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"79bd74a2",null);e["default"]=l.exports},b5756:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",[a("span",{staticClass:"cs-label"},[t._v("数据列表")]),t.time?a("span",{staticClass:"cs-time"},[t._v("数据更新时间："+t._s(t.time))]):t._e(),t.showTip?a("span",{staticClass:"cs-tip"},[t._v("有数据变更请点击更新数据")]):t._e()])},n=[]},bb8b:function(t,e,a){"use strict";a.r(e);var i=a("7acec"),n=a("e359");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("52fe");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"a1b5740e",null);e["default"]=l.exports},cbdc:function(t,e,a){},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,a("d3b7");var i=a("6186");function n(t){return new Promise((function(e,a){(0,i.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},e209:function(t,e,a){"use strict";a("cbdc")},e359:function(t,e,a){"use strict";a.r(e);var i=a("ede8"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},ede8:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("14d9"),a("b0c0"),a("d3b7");var n=i(a("c14f")),r=i(a("1da1")),o=i(a("ac03")),l=a("dc62"),s=a("cf45"),c=a("4f39"),u=i(a("3502")),d=i(a("90f5")),f=a("bda6"),h=a("db0a"),m=a("05e0");e.default={name:"PROMaterialControlReportDetail",components:{UpdateDate:d.default,VTable:u.default},mixins:[o.default],data:function(){return{addPageArray:[],updateBtn:!1,showBtn:!1,isView:!1,unCheck:!1,toBeConfirmed:!1,form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[]}},computed:{curTitle:function(){return"".concat((0,c.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计金额（元）")}},beforeCreate:function(){this.curModuleKey=f.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.loading=!0,t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,t.toBeConfirmed="check"===t.$route.query.type,t.isView="view"===t.$route.query.type,t.fetchData(),e.n=1,(0,s.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate()&&(this.loading=!0,(0,l.GetStockControlSummaryDetail)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[],t.tableData.length?(t.toBeConfirmed=2===t.tableData[0].AccountingStatus,t.unCheck=1===t.tableData[0].AccountingStatus,t.isView=!t.toBeConfirmed,t.showBtn=!0):t.showBtn=!1;var a=t.setTotalData(t.tableData,t.generateColumn()),i=a.column;t.columns=i,t.$refs["tb"].setColumns(i)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleUpdate:function(){var t=this;this.$confirm("是否更新数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.updateBtn=!0,(0,h.DailyBatch)({Factory_Id:t.form.FactoryId,Date:t.form.StatisticalDate}).then((function(e){e.IsSucceed?(t.fetchData(),t.$refs["updateDate"].getUpdate(),t.$message({message:"更新成功",type:"success"})):t.$message({message:e.Message,type:"error"}),t.updateBtn=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleSubmit:function(){var t=this;this.$confirm("是否提交核算?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,l.SubmitAccounting)({StatisticalDate:t.form.StatisticalDate,FactoryId:t.form.FactoryId}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleMaterialInfo:function(){this.$router.push({name:"PROMaterialControlDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleMaterialCostInfo:function(){this.$router.push({name:"PROMaterialCostDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleMaterialFeeInfo:function(){this.$router.push({name:"PROMaterialFeeDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleMaterialBalanceInfo:function(){this.$router.push({name:"PROMaterialBalanceDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},handleCheck:function(t){var e=this;this.$confirm("是否确定审核".concat(t?"通过":"不通过","?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,l.ReviewStockControlSummary)({FactoryId:e.form.FactoryId,Is_Approved:t,StatisticalDate:e.form.StatisticalDate}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},generateColumn:function(){var t=this,e=this.$createElement,a=140;return[{fixed:"left",title:"项目简称",params:{rowSpan:2},children:[{params:{none:"none"},children:[{title:this.curTitle,params:{colSpan:3},minWidth:m.ProjectAbbreviationW,field:"ProjectAbbreviation"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目编号",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:m.ProjectNumberW,field:"ProjectNumber"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目状态",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:m.ProjectStatusW,field:"ProjectStatus"}]}]},{params:{bg:"bg-green"},slots:{header:function(){var a=[e("span",["物控产值(元) "])];return t.getRoles("OMAMCDetailOutPut")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMaterialInfo()}}},["查看详情"])),a}},children:[{title:"主材-生产(元)",children:[{minWidth:a,field:"MainMaterialProduction",title:0,isTotal:!0}]},{title:"主材-营销(元)",children:[{minWidth:a,field:"MainMaterialMarketing",title:0,isTotal:!0}]},{title:"主材-其他(元)",children:[{minWidth:a,field:"MainMaterialOther",title:0,isTotal:!0}]},{title:"项目辅材(元)",children:[{minWidth:a,field:"ProjectAuxOutput",title:0,isTotal:!0}]},{title:"公共辅材(元)",children:[{minWidth:a,field:"PublicAuxOutput",title:0,isTotal:!0}]},{title:"小计(元)",children:[{isSubTotal:!0,minWidth:a,field:"MaterialSubtotal",title:0}]}]},{params:{bg:"bg-cyan"},title:"营业外收入",children:[{title:"小计(元)",children:[{minWidth:a,field:"NonbusinessSubtotal",title:0,isTotal:!0}]}]},{params:{bg:"bg-blue"},slots:{header:function(){var a=[e("span",["物控成本(元) "])];return t.getRoles("OMAMCDetailCost")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMaterialCostInfo()}}},["查看详情"])),a}},children:[{title:"主材-采购(元)",children:[{minWidth:a,field:"MainMaterialPurchase",title:0,isTotal:!0}]},{title:"主材-甲供(元)",children:[{minWidth:a,field:"MainMaterialDirectSupply",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"MaterialControlCostSubtotal",title:0,isSubTotal:!0}]}]},{title:"资产折旧(元)",params:{bg:"bg-purple"},children:[{title:"折旧费用(元)",children:[{minWidth:a,field:"DepreciationExpense",title:0,isTotal:!0}]}]},{params:{bg:"bg-yellow"},slots:{header:function(){var a=[e("span",["物控费用(元) "])];return t.getRoles("OMAMCDetailFee")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMaterialFeeInfo()}}},["查看详情"])),a}},children:[{title:"项目辅材(元)",children:[{minWidth:a,field:"MaterialControlProjectAux",title:0,isTotal:!0}]},{title:"公共辅材(元)",children:[{minWidth:a,field:"MaterialControlCostPublicAux",title:0,isTotal:!0}]},{title:"管理费用(元)",children:[{minWidth:a,field:"ManagementCost",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"ManagementCostSubtotal",title:0,isSubTotal:!0}]}]},{title:"物控结存(元)",params:{bg:"bg-orange"},slots:{header:function(){var a=[e("span",["物控结存(元) "])];return t.getRoles("OMAMCDetailBa")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMaterialBalanceInfo()}}},["查看详情"])),a}},children:[{title:"自购主材结存(元)",children:[{minWidth:a,field:"SelfPurchasedMaterialInventory",title:0,isTotal:!0}]},{title:"甲供主材结存(元)",children:[{minWidth:a,field:"OwnerSuppliedMaterialInventory",title:0,isTotal:!0}]},{title:"项目辅材结存(元)",children:[{minWidth:a,field:"ProjectAuxiliaryMaterialInventory",title:0,isTotal:!0}]},{title:"公共辅材结存(元)",children:[{minWidth:a,field:"PublicAuxiliaryMaterialInventory",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"MaterialControlSubtotalInventory",title:0,isSubTotal:!0}]}]},{title:"物控利润(元)",params:{bg:"bg-pink"},children:[{title:"产值+营业外收入-成本-资产折旧-费用+结存差",children:[{minWidth:380,field:"MaterialControlProfit",title:0,isTotal:!0}]}]}]}}}}}]);