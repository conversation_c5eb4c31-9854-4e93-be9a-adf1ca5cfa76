(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-9546f868"],{"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,n){var i=o(),l=t-i,s=20,u=0;e="undefined"===typeof e?500:e;var c=function(){u+=s;var t=Math.easeInOutQuad(u,i,l,e);r(t),u<e?a(c):n&&"function"===typeof n&&n()};c()}},"192c":function(t,e,n){"use strict";n.r(e);var a=n("9908"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},"1bee":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var r=n("73aa"),o=a(n("333d")),i=a(n("dee1"));e.default={name:"SysPreWarning",components:{Pagination:o.default,Dialog:i.default},filters:{filterStatus:function(t){switch(t){case 0:return"停止";case 1:return"正在运行"}},statusFilter:function(t){var e=0===t,n={false:"color-success",true:"color-danger"};return n[e]}},data:function(){return{loading:!1,btnLoading:!1,tableData:[],multipleSelection:[],pageInfo:{page:1,pageSize:20,totalCount:0}}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var t=this;this.loading=!0,(0,r.GetWarningSettingPageList)({Platform:"0",pageInfo:this.pageInfo}).then((function(e){if(e.IsSucceed){var n=e.Data,a=n.Data,r=n.Page,o=n.PageSize,i=n.TotalCount;t.tableData=a,t.pageInfo.pageSize=o,t.pageInfo.page=r,t.pageInfo.totalCount=i}t.loading=!1}))},handleEdit:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.$refs.dialog.handleOpen(t,e)},handleModifyStatus:function(t){var e=this,n=0===t.Status?1:0;this.$confirm("确认".concat(0===n?"停止":"启动",'定时任务"').concat(t.Display_Name,'"?'),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:function(a,o,i){"confirm"===a?(o.confirmButtonLoading=!0,o.confirmButtonText="执行中...",(0,r.ChangeScheduleStatus)({Id:t.Id,Status:n}).then((function(){e.$message.success('"'.concat(t.Display_Name,'"').concat(0===n?"已停止":"已启动")),o.confirmButtonLoading=!1,o.confirmButtonText="确定",t.Status=n,i()})).catch((function(){o.confirmButtonLoading=!1,o.confirmButtonText="确定"}))):i()}})}}}},"209d":function(t,e,n){"use strict";n("279c")},"279c":function(t,e,n){},"3d39":function(t,e,n){},6338:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{title:t.title,visible:t.dialogVisible,width:"40%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"预警名称",prop:"Display_Name"}},[n("el-input",{model:{value:t.form.Display_Name,callback:function(e){t.$set(t.form,"Display_Name",e)},expression:"form.Display_Name"}})],1),n("el-form-item",{attrs:{label:"预警类型",prop:"Call"}},[n("el-select",{staticClass:"filter-item",attrs:{placeholder:"选择预警类型"},on:{change:t.currentCallChange},model:{value:t.form.Call,callback:function(e){t.$set(t.form,"Call",e)},expression:"form.Call"}},t._l(t.jobCallList,(function(t){return n("el-option",{key:t.FullName,attrs:{label:t.Description,value:t.FullName}})})),1)],1),n("el-form-item",{attrs:{label:"预警说明",prop:"Explanation"}},[n("el-input",{attrs:{disabled:!0,"show-word-limit":"",maxlength:"500",type:"textarea"},model:{value:t.form.Explanation,callback:function(e){t.$set(t.form,"Explanation",e)},expression:"form.Explanation"}})],1),n("el-form-item",{attrs:{label:"预警阈值"}},[n("el-table",{ref:"singleTable",staticStyle:{width:"100%"},attrs:{data:t.form.ThresholdList,border:""}},[n("el-table-column",{attrs:{label:"编号","min-width":"25%",property:"ThresholdCode",align:"center"}}),n("el-table-column",{attrs:{label:"阈值","min-width":"50%",property:"ThresholdValue",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-input",{attrs:{placeholder:"请输入..."},model:{value:e.row.ThresholdValue,callback:function(n){t.$set(e.row,"ThresholdValue",n)},expression:"scope.row.ThresholdValue"}})]}}])}),n("el-table-column",{attrs:{label:"单位","min-width":"25%",property:"ThresholdUnit",align:"center"}})],1)],1),n("el-form-item",{attrs:{label:"预警周期",prop:"Cron"}},[n("cron-input",{model:{value:t.form.Cron,callback:function(e){t.$set(t.form,"Cron",e)},expression:"form.Cron"}})],1),n("el-form-item",{attrs:{label:"预警用户",prop:"Warning_UserIds"}},[n("el-select",{attrs:{multiple:"",filterable:"",placeholder:"请选择"},model:{value:t.form.Warning_UserIds,callback:function(e){t.$set(t.form,"Warning_UserIds",e)},expression:"form.Warning_UserIds"}},t._l(t.userIdsOptions,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"预警角色",prop:"Warning_RoleIds"}},[n("el-select",{attrs:{multiple:"",filterable:"",placeholder:"请选择"},model:{value:t.form.Warning_RoleIds,callback:function(e){t.$set(t.form,"Warning_RoleIds",e)},expression:"form.Warning_RoleIds"}},t._l(t.roleIdsOptions,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[n("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"300",type:"textarea"},model:{value:t.form.Remark,callback:function(e){t.$set(t.form,"Remark",e)},expression:"form.Remark"}})],1)],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:t.handleClose}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit("form")}}},[t._v("确 定")])],1)],1)},r=[]},"73aa":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ChangeScheduleStatus=l,e.DeleteSchedule=s,e.GetWarningObjectList=c,e.GetWarningSettingEntity=i,e.GetWarningSettingPageList=o,e.QueryLocalPreWarningList=d,e.SaveWarningSettingEntity=u;var r=a(n("b775"));function o(t){return(0,r.default)({url:"/SYS/WarningSetting/GetWarningSettingPageList",method:"post",data:t})}function i(t){return(0,r.default)({url:"/SYS/WarningSetting/GetWarningSettingEntity",method:"post",data:t})}function l(t){return(0,r.default)({url:"/sys/WarningSetting/ChangeWarningStatus",method:"post",data:t})}function s(t){return(0,r.default)({url:"/sys/WarningSetting/DeleteWarningSetting",method:"post",data:t})}function u(t){return(0,r.default)({url:"/SYS/WarningSetting/SaveWarningSettingEntity",method:"post",data:t})}function c(t){return(0,r.default)({url:"/SYS/WarningObject/GetWarningObjectList",method:"post",data:t})}function d(t){return(0,r.default)({url:"/SYS/WarningSetting/QueryLocalPreWarningList",method:"post",data:t})}},"8ec6":function(t,e,n){"use strict";n.r(e);var a=n("ec44"),r=n("e2b8");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("209d");var i=n("2877"),l=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"6c9838e7",null);e["default"]=l.exports},9908:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("e9f5"),n("910d"),n("e9c4"),n("b64b"),n("d3b7"),n("25f0");var r=n("73aa"),o=a(n("0a20"));e.default={components:{CronInput:o.default},data:function(){return{title:"",dialogVisible:!1,btnLoading:!1,form:{Id:"",Display_Name:"",ThresholdList:[],Cron:"",Call:"",Description:"",Status:0,Warning_UserIds:[],Warning_RoleIds:[],Explanation:"",Remark:"",Platform:"",WorkingObjectId:""},userIdsOptions:[],roleIdsOptions:[],jobCallList:[],unitOptions:[{label:"年",value:"year"},{label:"月",value:"month"},{label:"日",value:"day"},{label:"小时",value:"house"},{label:"分钟",value:"minute"},{label:"秒",value:"second"}],rules:{Display_Name:[{required:!0,message:"请输入预警名称",trigger:"blur"}],ThresholdValue:[{required:!0,message:"请输入预警阈值",trigger:"change"}],Cron:[{required:!0,message:"规则不能为空",trigger:"change"}],Call:[{required:!0,message:"任务地址不能为空",trigger:"blur"}]}}},created:function(){var t=this;(0,r.QueryLocalPreWarningList)({}).then((function(e){t.jobCallList=e.Data}))},methods:{currentCallChange:function(t){var e=this.jobCallList.filter((function(e){return e.FullName===t}));this.form.Explanation=e[0].Explanation,this.form.ThresholdList=e[0].ThresholdList,this.form.Platform=e[0].Platform},handleOpen:function(t,e){if(this.dialogVisible=!0,"add"===t)this.title="添加",this.fetchRoleAndUser("0"),this.fetchRoleAndUser("1");else{this.title="编辑";var n=e.Id,a=e.Display_Name,r=(e.ThresholdList,e.Status),o=e.Explanation,i=e.Remark,l=e.Cron,s=e.Call,u=e.Platform,c=e.WorkingObjectId;this.form={Id:n,Display_Name:a,ThresholdList:[],Status:r,Explanation:o,Remark:i,Cron:l,Call:s,Warning_UserIds:[],Warning_RoleIds:[],Platform:u,WorkingObjectId:c},this.fetchDataDetail(n),this.fetchRoleAndUser("0",n),this.fetchRoleAndUser("1",n)}},handleClose:function(){this.resetForm("form")},fetchDataDetail:function(t){var e=this;(0,r.GetWarningSettingEntity)({id:t}).then((function(t){if(t.IsSucceed){var n=t.Data,a=n.Warning_RoleIds,r=n.Warning_UserIds;e.form.ThresholdList=JSON.parse(t.Data.ThresholdList),e.form.Warning_RoleIds=a&&a.split(",")||[],e.form.Warning_UserIds=r&&r.split(",")||[]}}))},fetchRoleAndUser:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";(0,r.GetWarningObjectList)({warningId:n,type:t}).then((function(n){"0"===t?e.userIdsOptions=n.Data:e.roleIdsOptions=n.Data}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.btnLoading=!0;var n=Object.assign({},e.form);n.Platform=e.form.Platform,n.WorkingObjectId=e.form.WorkingObjectId,n.Warning_UserIds=n.Warning_UserIds.toString(),n.Warning_RoleIds=n.Warning_RoleIds.toString(),n.ThresholdList=JSON.stringify(n.ThresholdList);var a=e.jobCallList.filter((function(t){return t.FullName===n.Call}));n.Description=a[0].Description,(0,r.SaveWarningSettingEntity)(n).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.resetForm("form"),e.$emit("handleUpdate")):e.$message.warning(t.Message)})),e.btnLoading=!1}))},resetForm:function(t){this.$refs[t].resetFields(),this.form.ThresholdList=[],this.dialogVisible=!1}}}},d7b35:function(t,e,n){"use strict";n("3d39")},dee1:function(t,e,n){"use strict";n.r(e);var a=n("6338"),r=n("192c");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("d7b35");var i=n("2877"),l=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"9aed881c",null);e["default"]=l.exports},e2b8:function(t,e,n){"use strict";n.r(e);var a=n("1bee"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},ec44:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-button",{staticStyle:{"margin-bottom":"10px",float:"right"},attrs:{icon:"el-icon-plus"},on:{click:function(e){return t.handleEdit("add")}}},[t._v("新增")]),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{stripe:"","row-key":"Id",size:"large",data:t.tableData}},[n("el-table-column",{attrs:{align:"center",type:"index",width:"50"}}),n("el-table-column",{attrs:{align:"center",prop:"Display_Name",label:"预警名称"}}),n("el-table-column",{attrs:{align:"center",prop:"Description",label:"预警类型"}}),n("el-table-column",{attrs:{align:"center",prop:"Explanation",label:"预警说明"}}),n("el-table-column",{attrs:{align:"center",prop:"Run_Count",label:"运行状态"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{class:t._f("statusFilter")(e.row.Status)},[t._v(t._s(t._f("filterStatus")(e.row.Status)))])]}}])}),n("el-table-column",{attrs:{align:"center",prop:"Remark",label:"备注"}}),n("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{disabled:1===e.row.Status},on:{click:function(n){return t.handleEdit("edit",e.row)}}},[t._v("编辑")]),n("el-button",{attrs:{size:"mini",type:0===e.row.Status?"success":"danger"},on:{click:function(n){return t.handleModifyStatus(e.row)}}},[t._v(t._s(0===e.row.Status?"启用":"停止"))])]}}])})],1),t.pageInfo.totalCount>0?n("pagination",{attrs:{total:t.pageInfo.totalCount,page:t.pageInfo.page,limit:t.pageInfo.pageSize},on:{"update:page":function(e){return t.$set(t.pageInfo,"page",e)},"update:limit":function(e){return t.$set(t.pageInfo,"pageSize",e)},pagination:t.fetchData}}):t._e(),n("Dialog",{ref:"dialog",on:{handleUpdate:t.fetchData}})],1)},r=[]}}]);