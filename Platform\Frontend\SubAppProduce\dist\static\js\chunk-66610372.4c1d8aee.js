(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-66610372"],{"1c2c":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("ab43"),a("d3b7");var n=o(a("5530")),r=o(a("c14f")),i=o(a("1da1")),u=a("c24f"),s=a("aa50"),l=o(a("5f52")),c=o(a("0f97")),d=o(a("8e3a"));t.default={name:"PROMaterialRequisition",components:{DynamicDataTable:c.default},mixins:[l.default,d.default],inject:["Type"],data:function(){return{dialogVisible:!1,form:{UserId:"",Time:[],CreateTimeStart:null,CreateTimeEnd:null,GroupId:"",ProjectId:"",Status:0},tbConfig:{Op_Width:250},total:0,pgLoading:!1,btnloading:!1,queryInfo:{Page:1,PageSize:20},columns:[],tbData:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},dialogForm:{PickNo:"",Pass:null,ApproveRemarks:""},validatePass:{required:!1,message:"请输入审核意见",trigger:"blur"},dialogRules:{Pass:{required:!0,message:"请选择审核结果",trigger:"change"}},roleList:[]}},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getRoleAuthorization();case 1:return t.n=2,e.getPeopleList();case 2:return t.n=3,e.getTableConfig("PRORequisitionOrder");case 3:return t.n=4,e.fetchData();case 4:return t.a(2)}}),t)})))()},activated:function(){!this.isUpdate&&this.fetchData(1)},mounted:function(){this.isUpdate=!0},methods:{fetchData:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,o,i,u,l,c,d,f,p;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return""===e.form.Status&&(e.form.Status=0),a=e.form,o=a.CreateTimeEnd,i=a.CreateTimeStart,u=a.GroupId,l=a.ProjectId,c=a.Status,d=a.UserId,f={},f.CreateTimeEnd=o,f.CreateTimeStart=i,f.Status=c,f.ProjectId=l,f.GroupId=u,f.UserId=d,e.pgLoading=!0,p=1==e.Type?s.GetRawPickList:s.GetAuxPickList,t.n=1,p((0,n.default)((0,n.default)({},f),e.queryInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.tbData.map((function(e){return 3===e.Status&&(e.StatusToolTip=e.ApproveInfo),e.CreateTime=e.CreateTime.substr(0,10),e})),e.total=t.Data.TotalCount):e.$message.error(t.Message)})).finally((function(t){e.pgLoading=!1,e.isUpdate=!1}));case 1:return t.a(2)}}),t)})))()},addOrder:function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";1===t?this.$router.push({path:this.$route.path+"/order",query:{pg_redirect:1===this.Type?"PROMaterialRequisition":"PROAccessoriesUse",info:a,type:t,PageType:this.Type}}):2===t?this.$router.push({path:this.$route.path+"/view",query:{pg_redirect:1===this.Type?"PROMaterialRequisition":"PROAccessoriesUse",info:a,type:t,PageType:this.Type}}):this.$router.push({path:this.$route.path+"/edit",query:{pg_redirect:1===this.Type?"PROMaterialRequisition":"PROAccessoriesUse",info:a,type:t,PageType:this.Type}})},handelView:function(e){this.$router.push({path:this.$route.path+"/tread_Order",query:{pg_redirect:1===this.Type?"PROMaterialRequisition":"PROAccessoriesUse",info:e.ReceiptNumber,PageType:this.Type}})},changeTime:function(e){this.form.CreateTimeStart=e?e[0]:null,this.form.CreateTimeEnd=e?e[1]:null},handelSubmit:function(e){var t=this;this.$confirm("请确定当前领用单提交, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.RawStockSubmit)({pickNo:e.ReceiptNumber}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"提交成功!"}),t.fetchData()):t.$message({type:"warning",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消提交"})}))},handelDelete:function(e){var t=this;this.$confirm("请确定删除当前领用单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.RawPickDelete)({pickNo:e.ReceiptNumber}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData()):t.$message({type:"warning",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handelExamine:function(e){this.dialogVisible=!0,this.dialogForm.PickNo=e.ReceiptNumber},radioInput:function(e){this.validatePass.required=!e},dialogConfirm:function(e){var t=this;this.$refs[e].validate((function(a){if(!a)return!1;(0,s.Approve)((0,n.default)({},t.dialogForm)).then((function(a){a.IsSucceed?(t.$message({type:"success",message:"审核成功!"}),t.dialogCancel(e),t.fetchData()):t.$message({type:"warning",message:a.Message})}))}))},dialogCancel:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1},closeDialog:function(){this.dialogForm={PickNo:"",Pass:null,ApproveRemarks:""}},getRoleAuthorization:function(){var e=this;(0,u.RoleAuthorization)({roleType:3,menuType:1,menuId:this.$route.meta.Id}).then((function(t){t.IsSucceed?e.roleList=t.Data:e.$message({type:"warning",message:t.Message})}))}}}},"209b":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CheckPackCode=c,t.ExportComponentStockInInfo=p,t.ExportPackingInInfo=m,t.ExportWaitingStockIn2ndList=G,t.FinishCollect=I,t.From_Stock_Status_TYPES=void 0,t.GetComponentStockInEntity=s,t.GetLocationList=i,t.GetPackingDetailList=f,t.GetPackingEntity=y,t.GetPackingGroupByDirectDetailList=d,t.GetStockInDetailList=l,t.GetStockMoveDetailList=v,t.GetWarehouseListOfCurFactory=r,t.HandleInventoryItem=R,t.PackingTypes=t.PackingStatus=t.InventoryComponentTypes=t.InventoryCheckStatus=t.InventoryCheckExceptions=void 0,t.RemoveMain=h,t.SaveComponentScrap=T,t.SaveInventory=b,t.SavePacking=P,t.SaveStockIn=u,t.SaveStockMove=k,t.StockInTypes=void 0,t.UnzipPacking=g,t.UpdateBillReady=L,t.UpdateMaterialReady=O;var n=o(a("b775"));o(a("4328")),t.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],t.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],t.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],t.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],t.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],t.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],t.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function r(e){return(0,n.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Location/GetLocationList",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:e}})}function l(e,t){return(0,n.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:e,isEdit:t}})}function c(e,t){return(0,n.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:e,id:t}})}function d(e){return(0,n.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:e}})}function m(e){return(0,n.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:e}})}function h(e){return(0,n.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:e}})}function P(e){return(0,n.default)({url:"/PRO/Packing/SavePacking",method:"post",data:e})}function g(e){var t=e.id,a=e.locationId;return(0,n.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:t,locationId:a}})}function y(e){var t=e.id,a=e.code;return(0,n.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:t,code:a}})}function v(e){return(0,n.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:e}})}function k(e){return(0,n.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:e})}function R(e){var t=e.id,a=e.type,o=e.value;return(0,n.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:t,type:a,value:o}})}function T(e){return(0,n.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:e}})}function L(e){var t=e.installId,a=e.isReady;return(0,n.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:t,isReady:a}})}function O(e){return(0,n.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:e})}},"25dc":function(e,t,a){"use strict";a("c01e")},3166:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=p,t.DeleteProject=c,t.GeAreaTrees=T,t.GetFileSync=O,t.GetInstallUnitIdNameList=R,t.GetNoBindProjectList=m,t.GetPartDeepenFileList=I,t.GetProjectAreaTreeList=b,t.GetProjectEntity=s,t.GetProjectList=u,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=k,t.GetSchedulingPartList=L,t.IsEnableProjectMonomer=d,t.SaveProject=l,t.UpdateProjectTemplateBase=P,t.UpdateProjectTemplateContacts=g,t.UpdateProjectTemplateContract=y,t.UpdateProjectTemplateOther=v;var n=o(a("b775")),r=o(a("4328"));function i(e){return(0,n.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function l(e){return(0,n.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function O(e){return(0,n.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"4e82":function(e,t,a){"use strict";var o=a("23e7"),n=a("e330"),r=a("59ed"),i=a("7b0b"),u=a("07fa"),s=a("083a"),l=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),p=a("3f7e"),m=a("99f4"),h=a("1212"),P=a("ea83"),g=[],y=n(g.sort),v=n(g.push),k=c((function(){g.sort(void 0)})),b=c((function(){g.sort(null)})),R=f("sort"),T=!c((function(){if(h)return h<70;if(!(p&&p>3)){if(m)return!0;if(P)return P<603;var e,t,a,o,n="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(o=0;o<47;o++)g.push({k:t+o,v:a})}for(g.sort((function(e,t){return t.v-e.v})),o=0;o<g.length;o++)t=g[o].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}})),I=k||!b||!R||!T,L=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:l(t)>l(a)?1:-1}};o({target:"Array",proto:!0,forced:I},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(T)return void 0===e?y(t):y(t,e);var a,o,n=[],l=u(t);for(o=0;o<l;o++)o in t&&v(n,t[o]);d(n,L(e)),a=u(n),o=0;while(o<a)t[o]=n[o++];while(o<l)s(t,o++);return t}})},"5f52":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("dca8"),a("d3b7");var n=o(a("c14f")),r=o(a("1da1")),i=a("6186"),u=a("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,r.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getTypeList();case 1:return a.n=2,t.getTable(e);case 2:return a.a(2)}}),a)})))()},getTypeList:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var o,n=null===(o=a[0])||void 0===o?void 0:o.Id;e.Code=a.find((function(e){return e.Id===n})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(a){(0,i.GetGridByCode)({code:e+","+t.Code}).then((function(e){var o=e.IsSucceed,n=e.Data,r=e.Message;if(o){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),n.Grid.Is_Page&&(t.queryInfo.PageSize=+n.Grid.Row_Number),a(t.columns)}else t.$message({message:r,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var o=0;o<this.columns.length;o++){var n=this.columns[o];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},6543:function(e,t,a){"use strict";a.r(t);var o=a("eb753"),n=a("c7c4");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("25dc");var i=a("2877"),u=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"04f5dbca",null);t["default"]=u.exports},"7de9":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=p,t.AddCheckItemCombination=g,t.AddCheckType=s,t.DelNode=O,t.DelQualityList=T,t.DeleteCheckItem=f,t.DeleteCheckType=l,t.EntityCheckItem=m,t.EntityCheckType=i,t.EntityQualityList=R,t.ExportInspsectionSummaryInfo=w,t.GetCheckGroupList=P,t.GetCheckItemList=d,t.GetCheckTypeList=u,t.GetCompTypeTree=C,t.GetDictionaryDetailListByCode=r,t.GetEntityNode=L,t.GetFactoryPeoplelist=v,t.GetFactoryProfessionalByCode=S,t.GetMaterialType=D,t.GetNodeList=I,t.GetProEntities=y,t.GetProcessCodeList=k,t.QualityList=b,t.SaveCheckItem=h,t.SaveCheckType=c,t.SaveNode=G;var n=o(a("b775"));function r(e){return(0,n.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},"8e3a":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("dca8"),a("d3b7");var n=o(a("c14f")),r=o(a("1da1")),i=a("3166"),u=a("7de9"),s=a("a024"),l=a("fd31"),c=a("209b");t.default={data:function(){return{warehouses:[],locations:[],workingTeamsList:[],peopleList:[],ProjectNameData:[],statusOptions:[{Code:0,Display_Name:"全部"},{Code:1,Display_Name:"草稿"},{Code:2,Display_Name:"待审核"},{Code:3,Display_Name:"审核未通过"},{Code:4,Display_Name:"待发放"},{Code:5,Display_Name:"部分发放"},{Code:6,Display_Name:"已完成"}],Code:""}},mounted:function(){this.getWarehouseList(),this.getGetWorkingTeamsList(),this.getProjectOption()},methods:{getWarehouseList:function(){var e=this;(0,c.GetWarehouseListOfCurFactory)({}).then((function(t){t.IsSucceed&&(e.warehouses=t.Data)}))},wareChange:function(e){var t=this;this.form.Location_Id="",this.$nextTick((function(){t.form.Warehouse_Name=t.$refs["WarehouseRef"].selected.currentLabel})),(0,c.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))},locationChange:function(e){var t=this;this.$nextTick((function(){t.form.Location_Name=t.$refs["LocationRef"].selected.currentLabel}))},getPeopleList:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryPeoplelist)().then((function(t){t.IsSucceed?e.peopleList=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getGetWorkingTeamsList:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetWorkingTeams)({}).then((function(t){t.IsSucceed?e.workingTeamsList=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getProjectOption:function(){var e=this;(0,i.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getTypeList:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var o,n=null===(o=a[0])||void 0===o?void 0:o.Id;e.Code=a.find((function(e){return e.Id===n})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()}}}},a024:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=l,t.AddProessLib=x,t.AddTechnology=s,t.AddWorkingProcess=u,t.DelLib=F,t.DeleteProcess=T,t.DeleteProcessFlow=b,t.DeleteTechnology=R,t.DeleteWorkingTeams=G,t.GetAllProcessList=f,t.GetCheckGroupList=j,t.GetChildComponentTypeList=A,t.GetFactoryAllProcessList=p,t.GetFactoryPeoplelist=_,t.GetFactoryWorkingTeam=g,t.GetGroupItemsList=k,t.GetLibList=i,t.GetLibListType=$,t.GetProcessFlow=m,t.GetProcessFlowListWithTechnology=h,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=D,t.GetProcessListWithUserBase=w,t.GetProcessWorkingTeamBase=M,t.GetTeamListByUser=N,t.GetTeamProcessList=v,t.GetWorkingTeam=y,t.GetWorkingTeamBase=S,t.GetWorkingTeamInfo=C,t.GetWorkingTeams=I,t.GetWorkingTeamsPageList=L,t.SaveWorkingTeams=O,t.UpdateProcessTeam=P;var n=o(a("b775")),r=o(a("4328"));function i(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function s(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function l(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function p(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function h(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function g(){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function v(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function k(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function b(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function R(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function T(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function S(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function D(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function w(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},aa50:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Approve=d,t.AuxPickDelivery=v,t.DeliveryRawStock=m,t.GetAuxDeliveryList=b,t.GetAuxPickDetail=P,t.GetAuxPickList=h,t.GetDeliveryAuxStock=k,t.GetDeliveryRawStock=p,t.GetPickAuxStock=y,t.GetPickRawStock=c,t.GetRawDeliveryList=f,t.GetRawPickDetail=s,t.GetRawPickList=r,t.RawPickDelete=u,t.RawStockSubmit=i,t.SaveAuxPick=g,t.SaveRawPick=l;var n=o(a("b775"));o(a("4328"));function r(e){return(0,n.default)({url:"/PRO/RawPicking/GetRawPickList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/RawPicking/Submit",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/RawPicking/Delete",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/RawPicking/GetRawPickDetail",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/RawPicking/SaveRawPick",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/RawPicking/GetPickRawStock",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/RawPicking/Approve",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/RawPicking/GetDeliveryList",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/RawPicking/GetDeliveryRawStock",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/RawPicking/Delivery",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/AuxPicking/GetAuxPickList",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/AuxPicking/GetAuxPickDetail",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/AuxPicking/SaveAuxPick",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/AuxPicking/GetPickAuxStock",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/AuxPicking/Delivery",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/AuxPicking/GetDeliveryAuxStock",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/AuxPicking/GetDeliveryList",method:"post",data:e})}},c01e:function(e,t,a){},c7c4:function(e,t,a){"use strict";a.r(t);var o=a("1c2c"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);t["default"]=n.a},dca8:function(e,t,a){"use strict";var o=a("23e7"),n=a("bb2f"),r=a("d039"),i=a("861d"),u=a("f183").onFreeze,s=Object.freeze,l=r((function(){s(1)}));o({target:"Object",stat:!0,forced:l,sham:!n},{freeze:function(e){return s&&i(e)?s(u(e)):e}})},eb753:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"领料人",prop:"UserId"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.UserId,callback:function(t){e.$set(e.form,"UserId",t)},expression:"form.UserId"}},e._l(e.peopleList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"领料日期",prop:"Time"}},[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","picker-options":e.pickerOptions},on:{change:e.changeTime},model:{value:e.form.Time,callback:function(t){e.$set(e.form,"Time",t)},expression:"form.Time"}})],1),a("el-form-item",{attrs:{label:"领料班组",prop:"GroupId"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.GroupId,callback:function(t){e.$set(e.form,"GroupId",t)},expression:"form.GroupId"}},e._l(e.workingTeamsList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"归属项目",prop:"ProjectId"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"单据状态",prop:"Status"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.fetchData()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{type:"primary",loading:e.btnloading},on:{click:function(t){return e.addOrder("新建领用单",1)}}},[e._v("新建领用单")])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{height:"0",flex:"1"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange},scopedSlots:e._u([{key:"op",fn:function(t){var o=t.row;return[2===o.Status||4===o.Status||6===o.Status||5===o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.addOrder("查看领料单",2,o.ReceiptNumber)}}},[e._v("查看")]):e._e(),1===o.Status||3===o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.addOrder("编辑领料单",3,o.ReceiptNumber)}}},[e._v("编辑")]):e._e(),-1===e.roleList.findIndex((function(e){return"Material_Issuance"===e.Code}))||4!==o.Status&&5!==o.Status?e._e():a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handelView(o)}}},[e._v("发料")]),1===o.Status||3===o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handelSubmit(o)}}},[e._v("提交")]):e._e(),1===o.Status||3===o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handelDelete(o)}}},[e._v("删除")]):e._e(),-1!==e.roleList.findIndex((function(e){return"Material_approve"===e.Code}))&&2===o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handelExamine(o)}}},[e._v("审核")]):e._e()]}}])})],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"审核",visible:e.dialogVisible,width:"500px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.closeDialog}},[a("el-form",{ref:"dialogForm",attrs:{model:e.dialogForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审核结果",prop:"Pass",rules:{required:!0,message:"请选择审核结果",trigger:"change"}}},[a("el-radio-group",{on:{input:e.radioInput},model:{value:e.dialogForm.Pass,callback:function(t){e.$set(e.dialogForm,"Pass",t)},expression:"dialogForm.Pass"}},[a("el-radio",{attrs:{label:!0}},[e._v("通过")]),a("el-radio",{attrs:{label:!1}},[e._v("不通过")])],1)],1),a("el-form-item",{attrs:{label:"审核意见",prop:"ApproveRemarks",rules:e.validatePass}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.dialogForm.ApproveRemarks,callback:function(t){e.$set(e.dialogForm,"ApproveRemarks",t)},expression:"dialogForm.ApproveRemarks"}})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.dialogCancel("dialogForm")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dialogConfirm("dialogForm")}}},[e._v("确 定")])],1)],1)],1)},n=[]}}]);