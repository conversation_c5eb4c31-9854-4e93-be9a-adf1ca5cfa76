(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-44aa0a2e"],{7009:function(e,t,r){"use strict";r.r(t);var a=r("913e"),s=r("d17e");for(var o in s)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return s[e]}))}(o);r("ac0c");var l=r("2877"),i=Object(l["a"])(s["default"],a["a"],a["b"],!1,null,"140b197b",null);t["default"]=i.exports},8611:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r("0e4d");t.default={name:"SysCompanyInfo",data:function(){return{ruleForm:{Name:"",Short_Name:"",Address:"",Icon:""},rules:{Name:[{required:!0,message:"请输入公司名称",trigger:"blur"}],Short_Name:[{required:!0,message:"请输入公司简称",trigger:"blur"}]},imageUrl:"",allowFile:"image/*",fileList:[]}},created:function(){this.fetchData()},methods:{fetchData:function(){var e=this;(0,a.GetEntity)({}).then((function(t){Object.assign(e.ruleForm,t.Data),e.imageUrl=e.ruleForm.Icon}))},companySuccess:function(e){this.ruleForm.Icon=e.Data.split("*")[0],this.imageUrl=e.Data.split("*")[0]},submitForm:function(){var e=this;(0,a.Save)(this.ruleForm).then((function(t){t.IsSucceed?e.$message({type:"success",message:"保存成功"}):e.$message({type:"error",message:t.Message})}))}}}},"913e":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return s}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-card",{staticClass:"box-card"},[r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"公司名称",prop:"Name"}},[r("el-input",{model:{value:e.ruleForm.Name,callback:function(t){e.$set(e.ruleForm,"Name",t)},expression:"ruleForm.Name"}})],1),r("el-form-item",{attrs:{label:"公司简称",prop:"Short_Name"}},[r("el-input",{model:{value:e.ruleForm.Short_Name,callback:function(t){e.$set(e.ruleForm,"Short_Name",t)},expression:"ruleForm.Short_Name"}})],1),r("el-form-item",{attrs:{label:"公司地址",prop:"Address"}},[r("el-input",{attrs:{type:"textarea",maxlength:"100",autosize:{minRows:4,maxRows:6},"show-word-limit":""},model:{value:e.ruleForm.Address,callback:function(t){e.$set(e.ruleForm,"Address",t)},expression:"ruleForm.Address"}})],1),r("el-form-item",{attrs:{label:"公司LOGO",prop:"Icon"}},[r("el-upload",{staticClass:"avatar-uploader",attrs:{accept:e.allowFile,action:e.$store.state.uploadUrl,"on-success":e.companySuccess,"show-file-list":!1}},[e.imageUrl?r("img",{staticClass:"avatar",attrs:{src:e.imageUrl}}):r("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")])],1)],1)],1)],1)},s=[]},9661:function(e,t,r){},ac0c:function(e,t,r){"use strict";r("9661")},d17e:function(e,t,r){"use strict";r.r(t);var a=r("8611"),s=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=s.a}}]);