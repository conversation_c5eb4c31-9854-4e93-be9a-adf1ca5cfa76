(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3410f962"],{"06ab":function(t,e,n){"use strict";n("6637")},"2e8a":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteComponentType=d,e.GetCompTypeTree=f,e.GetComponentTypeEntity=l,e.GetComponentTypeList=r,e.GetFactoryCompTypeIndentifySetting=h,e.GetTableSettingList=p,e.GetTypePageList=u,e.RestoreTemplateType=b,e.SavDeepenTemplateSetting=C,e.SaveCompTypeIdentifySetting=y,e.SaveComponentType=s,e.SaveProBimComponentType=c,e.UpdateColumnSetting=g,e.UpdateComponentPartTableSetting=m;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:o.default.stringify(t)})}function d(t){return(0,i.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:o.default.stringify(t)})}function f(t){return(0,i.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:o.default.stringify(t)})}function c(t){return(0,i.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:t})}function m(t){return(0,i.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:t})}function h(t){return(0,i.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:t})}function y(t){return(0,i.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:t})}function C(t){return(0,i.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:t})}function b(t){return(0,i.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:t})}},6637:function(t,e,n){},"9a4e":function(t,e,n){"use strict";n.r(e);var a=n("a1ed"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"9b10":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[n("div",{staticClass:"sch-detail"},[n("header",[n("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),n("span",[t._v(t._s(t.unitFullInfo.Project_Name||"")+" / "+t._s(t.unitFullInfo.Name||""))])],1),n("div",{staticClass:"twrap"},[n("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,border:"",columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page},on:{columnSearchChange:t.columnSearchChange,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange,tableSearch:t.tableSearch}})],1)])])},i=[]},a1ed:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("7db0"),n("d81d"),n("e9f5"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("a9e3"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0");var i=a(n("b775")),o=a(n("0f97")),r=n("6186"),u=n("2e8a"),s=n("2dd9"),l=n("1b69"),d=n("ed08");e.default={name:"SheduleDetail",components:{DynamicDataTable:o.default},beforeRouteEnter:function(t,e,n){n((function(t){t.InstallUnit_Id=t.filterData.InstallUnit_Id=t.$route.query.id,t.getInstallUnitInfo()}))},data:function(){return{apis:{GetInstallUnitEntity:"/PRO/InstallUnit/GetInstallUnitEntity",GetInstallUnitComponentPageList:"/PRO/InstallUnit/GetInstallUnitComponentPageList"},gridCode:"pro_installunit_component_list",columns:[],tbConfig:{},data:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},factoryies:[],loading:!0,unitFullInfo:{},InstallUnit_Id:"",cmptTypes_1:[],cmptTypes_2:[]}},created:function(){var t=this;Promise.all([(0,u.GetComponentTypeList)({Level:1}).then((function(e){e.IsSucceed&&(t.cmptTypes_1=e.Data)})),(0,l.GetFactoryList)({}).then((function(e){t.factoryies=null===e||void 0===e?void 0:e.Data}))]).then((function(){(0,r.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){return t.getTableData()})).then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)}))}))},methods:{getInstallUnitInfo:function(){var t=this;(0,i.default)({url:this.apis.GetInstallUnitEntity,method:"post",params:{id:this.InstallUnit_Id}}).then((function(e){if(!e.IsSucceed)return t.$message.warning(e.Message);t.unitFullInfo=e.Data}))},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center"}),this.filterData.PageSize=this.tbConfig.Row_Number},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.Page=1,this.filterData.PageSize=e,this.filterChange()},setCols:function(t){var e=this;t.forEach((function(t){"scheduling_date"!==t.Code&&"actual_begin_date"!==t.Code&&"actual_finish_date"!==t.Code||(t.Type="date",t.Formatter="yyyy-M-d"),"type_name"===t.Code&&(t.Range=JSON.stringify(e.cmptTypes_1.map((function(t){return{label:t.Name,value:t.Name}})))),"sub_type_name"===t.Code&&(t.Range=JSON.stringify(e.cmptTypes_2.map((function(t){return{label:t.Name,value:t.Name}})))),"factory_name"===t.Code&&(t.Range=JSON.stringify(e.factoryies.map((function(t){return{label:t.Name,value:t.Name}}))))})),this.columns=t.concat([])},getTableData:function(){return(0,i.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData)})},setGridData:function(t){this.filterData.TotalCount=t.TotalCount,this.data=t.Data},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange()},filterChange:function(t){var e=this;this.filterData=Object.assign({},this.filterData,{Page:Number(t||1)}),this.filterData.ParameterJson=(0,s.setParameterJson)(this.fiterArrObj,this.columns),this.loading=!0,this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)})).finally((function(){e.loading=!1}))},tagBack:function(){(0,d.closeTagView)(this.$store,this.$route)},columnSearchChange:function(t){var e=this,n=t.column,a=t.value;if("type_name"===n.Code){var i=this.cmptTypes_1.find((function(t){return t.Name===a}));if(!i)return;(0,u.GetComponentTypeList)({Level:2,Parent_Id:i.Id}).then((function(t){t.IsSucceed&&(e.cmptTypes_2=t.Data,e.setCols(e.columns),e.$refs.table.searchedField=Object.assign({},e.$refs.table.searchedField,{Sub_Type_Name:""}))}))}}}}},b8e7:function(t,e,n){"use strict";n.r(e);var a=n("9b10"),i=n("9a4e");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("06ab");var r=n("2877"),u=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"283f344a",null);e["default"]=u.exports}}]);