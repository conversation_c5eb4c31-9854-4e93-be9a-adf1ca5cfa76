<template>
  <div>
    <el-row v-for="(info, index) in list" :key="info.id" class="item-x">
      <div class="item">
        <label>
          属性名称
          <el-select
            v-model="info.key"
            style="width: calc(100% - 65px)"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in filterOption(info.key)"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
        </label>
      </div>
      <div class="item" style="line-height: 32px">
        <label>请输入值
          <el-input-number
            v-if="checkType(info.key, 'number')"
            v-model="info.val"
            :min="0"
            class="cs-number-btn-hidden"
          />
          <el-input v-if="checkType(info.key, 'string')" v-model="info.val" />
          <el-select
            v-if="checkType(info.key, 'array') && info.key === 'Is_Heteroideus'"
            v-model="info.val"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in Is_Heteroideus_Data"
              :key="item.Id"
              :label="item.Name"
              :value="item.Name"
            />
          </el-select>
        </label>
      </div>
      <span v-if="index === 0" class="item-span">
        <i class="el-icon-circle-plus-outline" @click="handleAdd" />
      </span>
      <span v-else class="item-span">
        <i class="el-icon-circle-plus-outline" @click="handleAdd" />
        <i
          class="el-icon-remove-outline txt-red"
          @click="handleDelete(index)"
        />
      </span>
    </el-row>
    <div style="text-align: right; width: 100%; padding: 20px 2% 0 0">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="onSubmit">确定</el-button>
    </div>
  </div>
</template>

<script>
import { BatchUpdatePartProcessInfo } from '@/api/PRO/component'
import { v4 as uuidv4 } from 'uuid'
import { GetGridByCode } from '@/api/sys'
import { GetUserableAttr } from '@/api/PRO/professionalType'
export default {
  props: {
    typeEntity: {
      type: Object,
      default: () => {}
    },
    areaId: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      btnLoading: false,
      treeParams: {
        'default-expand-all': true,
        filterable: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      value: '',
      options: [
        {
          key: 'Thick',
          label: '厚度',
          type: 'number'
        },
        {
          key: 'Paint_Code',
          label: '油漆代码',
          type: 'string'
        },
        {
          key: 'PayCode',
          label: 'paycode',
          type: 'string'
        },
        {
          key: 'Demand_Drawing_Length',
          label: '要求图纸长度',
          type: 'string'
        },
        {
          key: 'Technology_Remark',
          label: '工艺注释',
          type: 'string'
        },
        {
          key: 'Technology_Code',
          label: '工艺代码',
          type: 'string'
        },
        {
          key: 'Texture_Replacement',
          label: '材质替换',
          type: 'string'
        },
        {
          key: 'Spec_Replacement',
          label: '规格替换',
          type: 'string'
        },
        {
          key: 'Layer',
          label: '涂层',
          type: 'string'
        },
        {
          key: 'Hole_Number',
          label: '孔数',
          type: 'number'
        },
        {
          key: 'Aperture',
          label: '孔径',
          type: 'number'
        },
        {
          key: 'Margin',
          label: '余量',
          type: 'number'
        },
        {
          key: 'EA_Number',
          label: 'EA数量',
          type: 'number'
        },
        {
          key: 'ABM',
          label: 'ABM',
          type: 'number'
        },
        {
          key: 'Is_Heteroideus',
          label: '异形',
          type: 'array'
        },
        {
          key: 'Dxf_Url',
          label: 'dxf地址路径',
          type: 'string'
        },
        {
          key: 'Remark',
          label: '备注',
          type: 'string'
        }
      ],
      list: [
        {
          id: uuidv4(),
          val: undefined,
          key: ''
        }
      ],
      Is_Heteroideus_Data: [{ Name: '是', Id: true }, { Name: '否', Id: false }]
    }
  },
  async mounted() {
    // await this.getUserableAttr()
    const codeArr = this.options.filter((item, index) => index).map(i => i.key)
    const columns = await this.convertCode(
      this.typeEntity.Code,
      codeArr,
      'PartTechnologyList'
    )
    this.options = this.options.map((item, index) => {
      if (index) {
        item.label = columns.filter((v) => v.Is_Display).find((i) => i.Code === item.key)?.Display_Name
      }
      return item
    })

    this.options = [...this.options]
  },
  methods: {
    // 获取拓展字段
    async getUserableAttr() {
      await GetUserableAttr({
        IsComponent: false
      }).then(res => {
        if (res.IsSucceed) {
          const resData = res.Data
          const expandData = []
          resData.forEach(item => {
            const expandJson = {}
            expandJson.key = item.Code
            expandJson.lable = item.Display_Name
            expandJson.type = 'string'
            expandData.push(expandJson)
          })
          this.options = this.options.concat(expandData)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    init(list, columnsOption) {
      this.selectList = list
      console.log(list)
      const arr = list.filter(item => item.Component_Code !== null && item.Component_Code !== '')
      console.log(arr)
      this.options = arr.length > 0 ? this.options.filter(v => v.key !== 'Num') : this.options
    },
    handleAdd() {
      this.list.push({
        id: uuidv4(),
        val: undefined,
        key: ''
      })
    },
    handleDelete(index) {
      this.list.splice(index, 1)
    },
    async onSubmit() {
      this.btnLoading = true
      const Keysmodel = []
      for (let i = 0; i < this.list.length; i++) {
        console.log(this.list)
        const obj = {}
        const element = this.list[i]
        // if (!element.val) {
        //   if (element.key === 'Thick' || element.key === 'Hole_Number' || element.key === 'Aperture' || element.key === 'Margin' || element.key === 'EA_Number') {
        //     element.val === 0 ? this.$message({ message: '值不能为0', type: 'warning' }) : this.$message({ message: '值不能为空', type: 'warning' })
        //   } else {
        //     this.$message({
        //       message: '值不能为空',
        //       type: 'warning'
        //     })
        //   }
        //   this.btnLoading = false
        //   return
        // }
        obj.code = element.key
        obj.value = element.val
        Keysmodel.push(obj)
        console.log(Keysmodel)
      }
      await BatchUpdatePartProcessInfo({
        Ids: this.selectList.map((v) => v.Part_Aggregate_Id).toString(),
        Keysmodel,
        Area_Id: this.areaId,
        Project_Id: this.projectId
      }).then((res) => {
        if (res.IsSucceed) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.$emit('close')
          this.$emit('refresh')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.btnLoading = false
      })
    },
    filterOption(currentValue) {
      console.log(currentValue)
      return this.options.filter((k) => {
        return (
          (!this.list.map((v) => v.key).includes(k.key) ||
            k.key === currentValue) &&
          k.label
        )
      })
    },

    checkType(key, type) {
      if (!key) return false
      return this.options.find((v) => v.key === key).type === type
    },

    // 获取配置数据
    async getColumnConfiguration(code, mainType = 'PartTechnologyList') {
      const res = await GetGridByCode({ code: mainType + ',' + code })
      return res.Data.ColumnList
    },

    // 根据Code（数据）获取名称
    async convertCode(typeCode, propsArr = [], mainType) {
      const props = await this.getColumnConfiguration(typeCode, mainType)
      const columns = props.filter(i => {
        const arr = propsArr.map(i => i.toLowerCase())
        return arr.includes(i.Code.toLowerCase())
      })
      return columns
    }
  }
}
</script>

<style scoped lang="scss">
[class^="el-icon"] {
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  margin-left: 15px;
}

.item-x {
  display: flex;
  margin-bottom: 20px;
  flex: 0 1 50%;
  justify-content: space-between;

  .item {
    width: 45%;
    white-space: nowrap;
    &:not(:first-of-type) {
      margin-left: 20px;
      .cs-number-btn-hidden,
      .el-input,
      .el-select {
        width: 80%;
      }
    }
  }

  .item-span {
    width: 90px;
    padding-top: 5px;
  }
}
::v-deep {
  .el-tree-select-input {
    width: 80% !important;
  }
}
</style>
