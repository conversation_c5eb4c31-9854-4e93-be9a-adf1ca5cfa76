(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3a9f51dc"],{"0308":function(t,e,n){},"281c":function(t,e,n){"use strict";var a=n("dbce").default,o=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("d81d"),n("b0c0"),n("e9f5"),n("ab43"),n("b680"),n("d3b7");var i=o(n("34e9")),r=o(n("3dcb")),c=o(n("b804")),s=n("3b8f"),u=n("ae9d"),f=n("f235"),d=o(n("5c7f")),l=n("e824"),p=n("6b87"),v=a(n("313e"));(0,s.use)([<PERSON><PERSON>,<PERSON><PERSON>,f.<PERSON>,f.<PERSON>nent,f.DatasetComponent,u.CanvasRenderer]);e.default={name:"PROCockpitFirstView",components:{TopHeader:i.default,BtnGroup:r.default,VChart:d.default,Sticky:c.default},data:function(){return{lovingVue:"钢构",totalWeight:0,btnOptions:[{label:"钢结构",value:"钢构"},{label:"PC",value:"PC"}],option:{tooltip:{trigger:"axis",formatter:function(t){var e="";return t.map((function(t){e="".concat(t.name,"<br/>产量 <b>").concat(t.value.Total_Weight," T</b>")})),e}},grid:{left:"2%",right:"5%",top:20,bottom:"15%",containLabel:!0},dataset:{dimensions:["Actual_Finish_Date","Total_Weight"],source:[]},xAxis:{type:"category",boundaryGap:!1},yAxis:{type:"value"},series:[{type:"line",smooth:!0,itemStyle:{color:"#FB6F83"},areaStyle:{color:new v.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"#FDC7CF"},{offset:1,color:"#FFFFFF"}])}}]}}},watch:{lovingVue:function(t,e){}},created:function(){this.fetchData()},methods:{fetchData:function(){var t=this;(0,p.GetComponentProducedByDays)({}).then((function(e){t.option.dataset.source=e.Data;var n=0;e.Data.map((function(t){n=t.Total_Weight+n})),t.totalWeight=(n/30).toFixed(2)}))},btnClick:function(){}}}},"51d1":function(t,e,n){"use strict";n.r(e);var a=n("281c"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},a634:function(t,e,n){"use strict";n.r(e);var a=n("fe88"),o=n("51d1");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("e1f1");var r=n("2877"),c=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"566a7023",null);e["default"]=c.exports},e1f1:function(t,e,n){"use strict";n("0308")},fe88:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pd16 cs-container"},[n("div",{staticClass:"cs-main"},[n("div",{staticClass:"cs-card"},[n("top-header",{attrs:{padding:"0"},scopedSlots:t._u([{key:"left",fn:function(){return[n("strong",{staticClass:"cs-title cs-radio"},[t._v(" 近30天日均产量 ")]),n("span",{staticClass:"cs-sub-title"},[t._v("（T）"),n("b",[t._v(t._s(t.totalWeight)+" 吨")])])]},proxy:!0},{key:"right",fn:function(){return[t._e()]},proxy:!0}])}),n("v-chart",{ref:"line",staticClass:"v-chart",attrs:{option:t.option,autoresize:""}})],1)])])},o=[]}}]);