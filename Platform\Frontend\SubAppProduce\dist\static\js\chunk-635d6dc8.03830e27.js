(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-635d6dc8","chunk-2d0b8e66"],{"0187":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteRole=u,t.GetRoleMenusObj=f,t.GetRoleTree=o,t.GetRoleWorkingObjListByUser=c,t.GetUserListByRole=s,t.GetUserRoleTreeWithoutObject=p,t.GetWorkingObjTree=h,t.SaveDepartmentObject=m,t.SaveRole=i,t.SaveRoleMenu=l,t.SaveUserAuthorize=d,t.SaveUserObject=v;var a=r(n("b775"));function o(){return(0,a.default)({url:"/SYS/Role/GetRoleTree",method:"post"})}function i(e){return(0,a.default)({url:"/SYS/Role/SaveRole",method:"post",data:e})}function u(e){return(0,a.default)({url:"/SYS/Role/DeleteRole",method:"post",data:e})}function s(e){return(0,a.default)({url:"/SYS/Role/GetUserListByRole",method:"post",data:e})}function d(e){return(0,a.default)({url:"/SYS/Role/SaveUserAuthorize",method:"post",data:e})}function c(e){return(0,a.default)({url:"/SYS/Role/GetRoleWorkingObjListByUser",method:"post",data:e})}function l(e){return(0,a.default)({url:"/SYS/Role/SaveRoleMenu",method:"post",data:e})}function f(e){return(0,a.default)({url:"/SYS/Role/GetRoleMenusObj",method:"post",data:e})}function p(e){return(0,a.default)({url:"/SYS/User/GetUserRoleTreeWithoutObject",method:"post",data:e})}function h(e){return(0,a.default)({url:"/SYS/User/GetWorkingObjTree",method:"post",data:e})}function v(e){return(0,a.default)({url:"/SYS/User/SaveUserObject",method:"post",data:e})}function m(e){return(0,a.default)({url:"/SYS/User/SaveDepartmentObject",method:"post",data:e})}},"0404":function(e,t,n){"use strict";n.r(t);var r=n("eca2"),a=n("b0ab");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("e6ca");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"0c8fcc3a",null);t["default"]=u.exports},"11e1":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"my-full-page"},[e.plan.Id?n("BimGantt",{ref:"gantt",attrs:{plan:e.plan,editmode:!1,extend:e.plan.HasExtention,extendFields:e.plan.ExtentionColumns}}):e._e()],1)},a=[]},"13d57":function(e,t,n){"use strict";n.r(t);var r=n("8759"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},"16c0":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("b0c0"),n("498a");var r=n("eb4a");t.default={name:"FlowModuleNote",props:{isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{User_Name:this.$store.getters.name,Days:"",Request_Comment:""},btnLoading:!1,rules:{Request_Comment:[{required:!0,message:"原因不能为空",trigger:"blur"}],Days:[{required:!0,message:"天数不能为空"},{type:"number",message:"天数必须为数字值"}]}}},computed:{btnDisabled:{get:function(){return!this.form.Request_Comment.trim().length},set:function(e){this.btnDisabled=this.form.Request_Comment.trim().length}}},watch:{datas:function(e,t){this.isEdit||(this.form=Object.assign(this.form,e))}},methods:{handleSubmit:function(){var e=this;this.$refs.ruleForm.validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,r.SaveBusinessData)({webfromId:e.$route.name,model:e.form}).then((function(t){t.IsSucceed?(e.$refs.ruleForm.resetFields(),e.$message({message:"保存成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))}}}},"18d8":function(e,t,n){"use strict";n.r(t);var r=n("4750"),a=n("6eab");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"5133467a",null);t["default"]=u.exports},"1cb4":function(e,t,n){"use strict";var r=n("dbce").default,a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("74a4")),i=r(n("32fd")),u=n("472f");t.default={name:"PlanView",components:{BimGantt:o.default},data:function(){return{plan:{}}},beforeRouteUpdate:function(e,t,n){this.loadPlanData(e.params.id||e.query.id),n()},created:function(){this.loadPlanData(this.$route.params.id||this.$route.query.id)},methods:{loadPlanData:function(e){var t=this;(0,u.GetPlanEntity)(e).then((function(e){e.IsSucceed&&(t.plan=i.parseServerPlanEntity(e.Data),setTimeout((function(){t.$refs.gantt.zoomToFit()}),300))}))}}}},"1f3d":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dropdown",{attrs:{trigger:"click","show-timeout":100}},[n("el-button",{attrs:{plain:""}},[e._v(e._s(e.getTxt())+" "),n("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),n("el-dropdown-menu",{staticClass:"no-padding",attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",[n("el-radio-group",{staticStyle:{padding:"10px"},model:{value:e.comment_disabled,callback:function(t){e.comment_disabled=t},expression:"comment_disabled"}},[n("el-radio",{attrs:{label:1}},[e._v("同意")]),n("el-radio",{attrs:{label:2}},[e._v("不同意")]),n("el-radio",{attrs:{label:3}},[e._v("驳回")])],1)],1)],1)],1)},a=[]},"1f88":function(e,t,n){},2601:function(e,t,n){"use strict";n.r(t);var r=n("eaae"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},"262c":function(e,t){e.exports="https://integ-plat-produce-test.bimtk.com/static/img/gantt-chartbg-2.2f514b8d.png"},"313e":function(e,t,n){"use strict";n.r(t),n.d(t,"version",(function(){return a["cb"]})),n.d(t,"dependencies",(function(){return a["l"]})),n.d(t,"PRIORITY",(function(){return a["g"]})),n.d(t,"init",(function(){return a["B"]})),n.d(t,"connect",(function(){return a["j"]})),n.d(t,"disconnect",(function(){return a["n"]})),n.d(t,"disConnect",(function(){return a["m"]})),n.d(t,"dispose",(function(){return a["o"]})),n.d(t,"getInstanceByDom",(function(){return a["w"]})),n.d(t,"getInstanceById",(function(){return a["x"]})),n.d(t,"registerTheme",(function(){return a["R"]})),n.d(t,"registerPreprocessor",(function(){return a["P"]})),n.d(t,"registerProcessor",(function(){return a["Q"]})),n.d(t,"registerPostInit",(function(){return a["N"]})),n.d(t,"registerPostUpdate",(function(){return a["O"]})),n.d(t,"registerUpdateLifecycle",(function(){return a["T"]})),n.d(t,"registerAction",(function(){return a["H"]})),n.d(t,"registerCoordinateSystem",(function(){return a["I"]})),n.d(t,"getCoordinateSystemDimensions",(function(){return a["v"]})),n.d(t,"registerLocale",(function(){return a["L"]})),n.d(t,"registerLayout",(function(){return a["J"]})),n.d(t,"registerVisual",(function(){return a["U"]})),n.d(t,"registerLoading",(function(){return a["K"]})),n.d(t,"setCanvasCreator",(function(){return a["V"]})),n.d(t,"registerMap",(function(){return a["M"]})),n.d(t,"getMap",(function(){return a["y"]})),n.d(t,"registerTransform",(function(){return a["S"]})),n.d(t,"dataTool",(function(){return a["k"]})),n.d(t,"zrender",(function(){return a["eb"]})),n.d(t,"matrix",(function(){return a["D"]})),n.d(t,"vector",(function(){return a["bb"]})),n.d(t,"zrUtil",(function(){return a["db"]})),n.d(t,"color",(function(){return a["i"]})),n.d(t,"throttle",(function(){return a["X"]})),n.d(t,"helper",(function(){return a["A"]})),n.d(t,"use",(function(){return a["Z"]})),n.d(t,"setPlatformAPI",(function(){return a["W"]})),n.d(t,"parseGeoJSON",(function(){return a["F"]})),n.d(t,"parseGeoJson",(function(){return a["G"]})),n.d(t,"number",(function(){return a["E"]})),n.d(t,"time",(function(){return a["Y"]})),n.d(t,"graphic",(function(){return a["z"]})),n.d(t,"format",(function(){return a["u"]})),n.d(t,"util",(function(){return a["ab"]})),n.d(t,"env",(function(){return a["p"]})),n.d(t,"List",(function(){return a["e"]})),n.d(t,"Model",(function(){return a["f"]})),n.d(t,"Axis",(function(){return a["a"]})),n.d(t,"ComponentModel",(function(){return a["c"]})),n.d(t,"ComponentView",(function(){return a["d"]})),n.d(t,"SeriesModel",(function(){return a["h"]})),n.d(t,"ChartView",(function(){return a["b"]})),n.d(t,"innerDrawElementOnCanvas",(function(){return a["C"]})),n.d(t,"extendComponentModel",(function(){return a["r"]})),n.d(t,"extendComponentView",(function(){return a["s"]})),n.d(t,"extendSeriesModel",(function(){return a["t"]})),n.d(t,"extendChartView",(function(){return a["q"]}));var r=n("22b4"),a=n("aa74"),o=n("f95e"),i=n("97ac"),u=n("3620"),s=n("4cb5"),d=n("49bba"),c=n("acf6"),l=n("e8e6"),f=n("b37b"),p=n("54ca"),h=n("128d"),v=n("efb0"),m=n("9be8"),b=n("e275"),g=n("7b72"),O=n("10e8e"),y=n("0d95"),j=n("b489"),S=n("2564"),I=n("14bf"),_=n("0eed"),G=n("583f"),x=n("c835b"),M=n("8acb"),w=n("052f"),C=n("4b2a"),T=n("bb6f"),D=n("b25d"),P=n("5334"),U=n("4bd9"),R=n("b899"),k=n("5a72"),F=n("3094"),L=n("2da7"),N=n("af5c"),E=n("b22b"),A=n("9394"),$=n("541a"),Y=n("a0c6"),B=n("9502"),z=n("4231"),q=n("ff32"),V=n("104d"),J=n("e1ff"),H=n("ac12"),W=n("abd2"),Z=n("7c0d"),Q=n("c436"),K=n("47e7"),X=n("e600"),ee=n("5e81"),te=n("4f85"),ne=n("6d8b"),re=n("4a3f"),ae=n("cbe5"),oe=n("401b"),ie=n("342d"),ue=n("8582"),se=n("e263"),de=n("9850"),ce=n("dce8"),le=n("87b1"),fe=n("c7a2"),pe=n("4aa2"),he=n("20c8"),ve=he["a"].CMD;function me(e,t){return Math.abs(e-t)<1e-5}function be(e){var t,n,r,a,o,i=e.data,u=e.len(),s=[],d=0,c=0,l=0,f=0;function p(e,n){t&&t.length>2&&s.push(t),t=[e,n]}function h(e,n,r,a){me(e,r)&&me(n,a)||t.push(e,n,r,a,r,a)}function v(e,n,r,a,o,i){var u=Math.abs(n-e),s=4*Math.tan(u/4)/3,d=n<e?-1:1,c=Math.cos(e),l=Math.sin(e),f=Math.cos(n),p=Math.sin(n),h=c*o+r,v=l*i+a,m=f*o+r,b=p*i+a,g=o*s*d,O=i*s*d;t.push(h-g*l,v+O*c,m+g*p,b-O*f,m,b)}for(var m=0;m<u;){var b=i[m++],g=1===m;switch(g&&(d=i[m],c=i[m+1],l=d,f=c,b!==ve.L&&b!==ve.C&&b!==ve.Q||(t=[l,f])),b){case ve.M:d=l=i[m++],c=f=i[m++],p(l,f);break;case ve.L:n=i[m++],r=i[m++],h(d,c,n,r),d=n,c=r;break;case ve.C:t.push(i[m++],i[m++],i[m++],i[m++],d=i[m++],c=i[m++]);break;case ve.Q:n=i[m++],r=i[m++],a=i[m++],o=i[m++],t.push(d+2/3*(n-d),c+2/3*(r-c),a+2/3*(n-a),o+2/3*(r-o),a,o),d=a,c=o;break;case ve.A:var O=i[m++],y=i[m++],j=i[m++],S=i[m++],I=i[m++],_=i[m++]+I;m+=1;var G=!i[m++];n=Math.cos(I)*j+O,r=Math.sin(I)*S+y,g?(l=n,f=r,p(l,f)):h(d,c,n,r),d=Math.cos(_)*j+O,c=Math.sin(_)*S+y;for(var x=(G?-1:1)*Math.PI/2,M=I;G?M>_:M<_;M+=x){var w=G?Math.max(M+x,_):Math.min(M+x,_);v(M,w,O,y,j,S)}break;case ve.R:l=d=i[m++],f=c=i[m++],n=l+i[m++],r=f+i[m++],p(n,f),h(n,f,n,r),h(n,r,l,r),h(l,r,l,f),h(l,f,n,f);break;case ve.Z:t&&h(d,c,l,f),d=l,c=f;break}}return t&&t.length>2&&s.push(t),s}function ge(e,t,n,r,a,o,i,u,s,d){if(me(e,n)&&me(t,r)&&me(a,i)&&me(o,u))s.push(i,u);else{var c=2/d,l=c*c,f=i-e,p=u-t,h=Math.sqrt(f*f+p*p);f/=h,p/=h;var v=n-e,m=r-t,b=a-i,g=o-u,O=v*v+m*m,y=b*b+g*g;if(O<l&&y<l)s.push(i,u);else{var j=f*v+p*m,S=-f*b-p*g,I=O-j*j,_=y-S*S;if(I<l&&j>=0&&_<l&&S>=0)s.push(i,u);else{var G=[],x=[];Object(re["g"])(e,n,a,i,.5,G),Object(re["g"])(t,r,o,u,.5,x),ge(G[0],x[0],G[1],x[1],G[2],x[2],G[3],x[3],s,d),ge(G[4],x[4],G[5],x[5],G[6],x[6],G[7],x[7],s,d)}}}}function Oe(e,t){var n=be(e),r=[];t=t||1;for(var a=0;a<n.length;a++){var o=n[a],i=[],u=o[0],s=o[1];i.push(u,s);for(var d=2;d<o.length;){var c=o[d++],l=o[d++],f=o[d++],p=o[d++],h=o[d++],v=o[d++];ge(u,s,c,l,f,p,h,v,i,t),u=h,s=v}r.push(i)}return r}function ye(e,t,n){var r=e[t],a=e[1-t],o=Math.abs(r/a),i=Math.ceil(Math.sqrt(o*n)),u=Math.floor(n/i);0===u&&(u=1,i=n);for(var s=[],d=0;d<i;d++)s.push(u);var c=i*u,l=n-c;if(l>0)for(d=0;d<l;d++)s[d%i]+=1;return s}function je(e,t,n){for(var r=e.r0,a=e.r,o=e.startAngle,i=e.endAngle,u=Math.abs(i-o),s=u*a,d=a-r,c=s>Math.abs(d),l=ye([s,d],c?0:1,t),f=(c?u:d)/l.length,p=0;p<l.length;p++)for(var h=(c?d:u)/l[p],v=0;v<l[p];v++){var m={};c?(m.startAngle=o+f*p,m.endAngle=o+f*(p+1),m.r0=r+h*v,m.r=r+h*(v+1)):(m.startAngle=o+h*v,m.endAngle=o+h*(v+1),m.r0=r+f*p,m.r=r+f*(p+1)),m.clockwise=e.clockwise,m.cx=e.cx,m.cy=e.cy,n.push(m)}}function Se(e,t,n){for(var r=e.width,a=e.height,o=r>a,i=ye([r,a],o?0:1,t),u=o?"width":"height",s=o?"height":"width",d=o?"x":"y",c=o?"y":"x",l=e[u]/i.length,f=0;f<i.length;f++)for(var p=e[s]/i[f],h=0;h<i[f];h++){var v={};v[d]=f*l,v[c]=h*p,v[u]=l,v[s]=p,v.x+=e.x,v.y+=e.y,n.push(v)}}function Ie(e,t,n,r){return e*r-n*t}function _e(e,t,n,r,a,o,i,u){var s=n-e,d=r-t,c=i-a,l=u-o,f=Ie(c,l,s,d);if(Math.abs(f)<1e-6)return null;var p=e-a,h=t-o,v=Ie(p,h,c,l)/f;return v<0||v>1?null:new ce["a"](v*s+e,v*d+t)}function Ge(e,t,n){var r=new ce["a"];ce["a"].sub(r,n,t),r.normalize();var a=new ce["a"];ce["a"].sub(a,e,t);var o=a.dot(r);return o}function xe(e,t){var n=e[e.length-1];n&&n[0]===t[0]&&n[1]===t[1]||e.push(t)}function Me(e,t,n){for(var r=e.length,a=[],o=0;o<r;o++){var i=e[o],u=e[(o+1)%r],s=_e(i[0],i[1],u[0],u[1],t.x,t.y,n.x,n.y);s&&a.push({projPt:Ge(s,t,n),pt:s,idx:o})}if(a.length<2)return[{points:e},{points:e}];a.sort((function(e,t){return e.projPt-t.projPt}));var d=a[0],c=a[a.length-1];if(c.idx<d.idx){var l=d;d=c,c=l}var f=[d.pt.x,d.pt.y],p=[c.pt.x,c.pt.y],h=[f],v=[p];for(o=d.idx+1;o<=c.idx;o++)xe(h,e[o].slice());xe(h,p),xe(h,f);for(o=c.idx+1;o<=d.idx+r;o++)xe(v,e[o%r].slice());return xe(v,f),xe(v,p),[{points:h},{points:v}]}function we(e){var t=e.points,n=[],r=[];Object(se["d"])(t,n,r);var a=new de["a"](n[0],n[1],r[0]-n[0],r[1]-n[1]),o=a.width,i=a.height,u=a.x,s=a.y,d=new ce["a"],c=new ce["a"];return o>i?(d.x=c.x=u+o/2,d.y=s,c.y=s+i):(d.y=c.y=s+i/2,d.x=u,c.x=u+o),Me(t,d,c)}function Ce(e,t,n,r){if(1===n)r.push(t);else{var a=Math.floor(n/2),o=e(t);Ce(e,o[0],a,r),Ce(e,o[1],n-a,r)}return r}function Te(e,t){for(var n=[],r=0;r<t;r++)n.push(Object(ie["a"])(e));return n}function De(e,t){t.setStyle(e.style),t.z=e.z,t.z2=e.z2,t.zlevel=e.zlevel}function Pe(e){for(var t=[],n=0;n<e.length;)t.push([e[n++],e[n++]]);return t}function Ue(e,t){var n,r=[],a=e.shape;switch(e.type){case"rect":Se(a,t,r),n=fe["a"];break;case"sector":je(a,t,r),n=pe["a"];break;case"circle":je({r0:0,r:a.r,startAngle:0,endAngle:2*Math.PI,cx:a.cx,cy:a.cy},t,r),n=pe["a"];break;default:var o=e.getComputedTransform(),i=o?Math.sqrt(Math.max(o[0]*o[0]+o[1]*o[1],o[2]*o[2]+o[3]*o[3])):1,u=Object(ne["map"])(Oe(e.getUpdatedPathProxy(),i),(function(e){return Pe(e)})),s=u.length;if(0===s)Ce(we,{points:u[0]},t,r);else if(s===t)for(var d=0;d<s;d++)r.push({points:u[d]});else{var c=0,l=Object(ne["map"])(u,(function(e){var t=[],n=[];Object(se["d"])(e,t,n);var r=(n[1]-t[1])*(n[0]-t[0]);return c+=r,{poly:e,area:r}}));l.sort((function(e,t){return t.area-e.area}));var f=t;for(d=0;d<s;d++){var p=l[d];if(f<=0)break;var h=d===s-1?f:Math.ceil(p.area/c*t);h<0||(Ce(we,{points:p.poly},h,r),f-=h)}}n=le["a"];break}if(!n)return Te(e,t);var v=[];for(d=0;d<r.length;d++){var m=new n;m.setShape(r[d]),De(e,m),v.push(m)}return v}function Re(e,t){var n=e.length,r=t.length;if(n===r)return[e,t];for(var a=[],o=[],i=n<r?e:t,u=Math.min(n,r),s=Math.abs(r-n)/6,d=(u-2)/6,c=Math.ceil(s/d)+1,l=[i[0],i[1]],f=s,p=2;p<u;){var h=i[p-2],v=i[p-1],m=i[p++],b=i[p++],g=i[p++],O=i[p++],y=i[p++],j=i[p++];if(f<=0)l.push(m,b,g,O,y,j);else{for(var S=Math.min(f,c-1)+1,I=1;I<=S;I++){var _=I/S;Object(re["g"])(h,m,g,y,_,a),Object(re["g"])(v,b,O,j,_,o),h=a[3],v=o[3],l.push(a[1],o[1],a[2],o[2],h,v),m=a[5],b=o[5],g=a[6],O=o[6]}f-=S-1}}return i===e?[l,t]:[e,l]}function ke(e,t){for(var n=e.length,r=e[n-2],a=e[n-1],o=[],i=0;i<t.length;)o[i++]=r,o[i++]=a;return o}function Fe(e,t){for(var n,r,a,o=[],i=[],u=0;u<Math.max(e.length,t.length);u++){var s=e[u],d=t[u],c=void 0,l=void 0;s?d?(n=Re(s,d),c=n[0],l=n[1],r=c,a=l):(l=ke(a||s,s),c=s):(c=ke(r||d,d),l=d),o.push(c),i.push(l)}return[o,i]}function Le(e){for(var t=0,n=0,r=0,a=e.length,o=0,i=a-2;o<a;i=o,o+=2){var u=e[i],s=e[i+1],d=e[o],c=e[o+1],l=u*c-d*s;t+=l,n+=(u+d)*l,r+=(s+c)*l}return 0===t?[e[0]||0,e[1]||0]:[n/t/3,r/t/3,t]}function Ne(e,t,n,r){for(var a=(e.length-2)/6,o=1/0,i=0,u=e.length,s=u-2,d=0;d<a;d++){for(var c=6*d,l=0,f=0;f<u;f+=2){var p=0===f?c:(c+f-2)%s+2,h=e[p]-n[0],v=e[p+1]-n[1],m=t[f]-r[0],b=t[f+1]-r[1],g=m-h,O=b-v;l+=g*g+O*O}l<o&&(o=l,i=d)}return i}function Ee(e){for(var t=[],n=e.length,r=0;r<n;r+=2)t[r]=e[n-r-2],t[r+1]=e[n-r-1];return t}function Ae(e,t,n,r){for(var a,o=[],i=0;i<e.length;i++){var u=e[i],s=t[i],d=Le(u),c=Le(s);null==a&&(a=d[2]<0!==c[2]<0);var l=[],f=[],p=0,h=1/0,v=[],m=u.length;a&&(u=Ee(u));for(var b=6*Ne(u,s,d,c),g=m-2,O=0;O<g;O+=2){var y=(b+O)%g+2;l[O+2]=u[y]-d[0],l[O+3]=u[y+1]-d[1]}if(l[0]=u[b]-d[0],l[1]=u[b+1]-d[1],n>0)for(var j=r/n,S=-r/2;S<=r/2;S+=j){var I=Math.sin(S),_=Math.cos(S),G=0;for(O=0;O<u.length;O+=2){var x=l[O],M=l[O+1],w=s[O]-c[0],C=s[O+1]-c[1],T=w*_-C*I,D=w*I+C*_;v[O]=T,v[O+1]=D;var P=T-x,U=D-M;G+=P*P+U*U}if(G<h){h=G,p=S;for(var R=0;R<v.length;R++)f[R]=v[R]}}else for(var k=0;k<m;k+=2)f[k]=s[k]-c[0],f[k+1]=s[k+1]-c[1];o.push({from:l,to:f,fromCp:d,toCp:c,rotation:-p})}return o}function $e(e){return e.__isCombineMorphing}var Ye="__mOriginal_";function Be(e,t,n){var r=Ye+t,a=e[r]||e[t];e[r]||(e[r]=e[t]);var o=n.replace,i=n.after,u=n.before;e[t]=function(){var e,t=arguments;return u&&u.apply(this,t),e=o?o.apply(this,t):a.apply(this,t),i&&i.apply(this,t),e}}function ze(e,t){var n=Ye+t;e[n]&&(e[t]=e[n],e[n]=null)}function qe(e,t){for(var n=0;n<e.length;n++)for(var r=e[n],a=0;a<r.length;){var o=r[a],i=r[a+1];r[a++]=t[0]*o+t[2]*i+t[4],r[a++]=t[1]*o+t[3]*i+t[5]}}function Ve(e,t){var n=e.getUpdatedPathProxy(),r=t.getUpdatedPathProxy(),a=Fe(be(n),be(r)),o=a[0],i=a[1],u=e.getComputedTransform(),s=t.getComputedTransform();function d(){this.transform=null}u&&qe(o,u),s&&qe(i,s),Be(t,"updateTransform",{replace:d}),t.transform=null;var c=Ae(o,i,10,Math.PI),l=[];Be(t,"buildPath",{replace:function(e){for(var n=t.__morphT,r=1-n,a=[],o=0;o<c.length;o++){var i=c[o],u=i.from,s=i.to,d=i.rotation*n,f=i.fromCp,p=i.toCp,h=Math.sin(d),v=Math.cos(d);Object(oe["lerp"])(a,f,p,n);for(var m=0;m<u.length;m+=2){var b=u[m],g=u[m+1],O=s[m],y=s[m+1],j=b*r+O*n,S=g*r+y*n;l[m]=j*v-S*h+a[0],l[m+1]=j*h+S*v+a[1]}var I=l[0],_=l[1];e.moveTo(I,_);for(m=2;m<u.length;){O=l[m++],y=l[m++];var G=l[m++],x=l[m++],M=l[m++],w=l[m++];I===O&&_===y&&G===M&&x===w?e.lineTo(M,w):e.bezierCurveTo(O,y,G,x,M,w),I=M,_=w}}}})}function Je(e,t,n){if(!e||!t)return t;var r=n.done,a=n.during;function o(){ze(t,"buildPath"),ze(t,"updateTransform"),t.__morphT=-1,t.createPathProxy(),t.dirtyShape()}return Ve(e,t),t.__morphT=0,t.animateTo({__morphT:1},Object(ne["defaults"])({during:function(e){t.dirtyShape(),a&&a(e)},done:function(){o(),r&&r()}},n)),t}function He(e,t,n,r,a,o){var i=16;e=a===n?0:Math.round(32767*(e-n)/(a-n)),t=o===r?0:Math.round(32767*(t-r)/(o-r));for(var u,s=0,d=(1<<i)/2;d>0;d/=2){var c=0,l=0;(e&d)>0&&(c=1),(t&d)>0&&(l=1),s+=d*d*(3*c^l),0===l&&(1===c&&(e=d-1-e,t=d-1-t),u=e,e=t,t=u)}return s}function We(e){var t=1/0,n=1/0,r=-1/0,a=-1/0,o=Object(ne["map"])(e,(function(e){var o=e.getBoundingRect(),i=e.getComputedTransform(),u=o.x+o.width/2+(i?i[4]:0),s=o.y+o.height/2+(i?i[5]:0);return t=Math.min(u,t),n=Math.min(s,n),r=Math.max(u,r),a=Math.max(s,a),[u,s]})),i=Object(ne["map"])(o,(function(o,i){return{cp:o,z:He(o[0],o[1],t,n,r,a),path:e[i]}}));return i.sort((function(e,t){return e.z-t.z})).map((function(e){return e.path}))}function Ze(e){return Ue(e.path,e.count)}function Qe(){return{fromIndividuals:[],toIndividuals:[],count:0}}function Ke(e,t,n){var r=[];function a(e){for(var t=0;t<e.length;t++){var n=e[t];$e(n)?a(n.childrenRef()):n instanceof ae["b"]&&r.push(n)}}a(e);var o=r.length;if(!o)return Qe();var i=n.dividePath||Ze,u=i({path:t,count:o});if(u.length!==o)return Qe();r=We(r),u=We(u);for(var s=n.done,d=n.during,c=n.individualDelay,l=new ue["c"],f=0;f<o;f++){var p=r[f],h=u[f];h.parent=t,h.copyTransform(l),c||Ve(p,h)}function v(e){for(var t=0;t<u.length;t++)u[t].addSelfToZr(e)}function m(){t.__isCombineMorphing=!1,t.__morphT=-1,t.childrenRef=null,ze(t,"addSelfToZr"),ze(t,"removeSelfFromZr")}t.__isCombineMorphing=!0,t.childrenRef=function(){return u},Be(t,"addSelfToZr",{after:function(e){v(e)}}),Be(t,"removeSelfFromZr",{after:function(e){for(var t=0;t<u.length;t++)u[t].removeSelfFromZr(e)}});var b=u.length;if(c){var g=b,O=function(){g--,0===g&&(m(),s&&s())};for(f=0;f<b;f++){var y=c?Object(ne["defaults"])({delay:(n.delay||0)+c(f,b,r[f],u[f]),done:O},n):n;Je(r[f],u[f],y)}}else t.__morphT=0,t.animateTo({__morphT:1},Object(ne["defaults"])({during:function(e){for(var n=0;n<b;n++){var r=u[n];r.__morphT=t.__morphT,r.dirtyShape()}d&&d(e)},done:function(){m();for(var t=0;t<e.length;t++)ze(e[t],"updateTransform");s&&s()}},n));return t.__zr&&v(t.__zr),{fromIndividuals:r,toIndividuals:u,count:b}}function Xe(e,t,n){var r=t.length,a=[],o=n.dividePath||Ze;function i(e){for(var t=0;t<e.length;t++){var n=e[t];$e(n)?i(n.childrenRef()):n instanceof ae["b"]&&a.push(n)}}if($e(e)){i(e.childrenRef());var u=a.length;if(u<r)for(var s=0,d=u;d<r;d++)a.push(Object(ie["a"])(a[s++%u]));a.length=r}else{a=o({path:e,count:r});var c=e.getComputedTransform();for(d=0;d<a.length;d++)a[d].setLocalTransform(c);if(a.length!==r)return Qe()}a=We(a),t=We(t);var l=n.individualDelay;for(d=0;d<r;d++){var f=l?Object(ne["defaults"])({delay:(n.delay||0)+l(d,r,a[d],t[d])},n):n;Je(a[d],t[d],f)}return{fromIndividuals:a,toIndividuals:t,count:t.length}}var et=n("deca");function tt(e){return Object(ne["isArray"])(e[0])}function nt(e,t){for(var n=[],r=e.length,a=0;a<r;a++)n.push({one:e[a],many:[]});for(a=0;a<t.length;a++){var o=t[a].length,i=void 0;for(i=0;i<o;i++)n[i%r].many.push(t[a][i])}var u=0;for(a=r-1;a>=0;a--)if(!n[a].many.length){var s=n[u].many;if(s.length<=1){if(!u)return n;u=0}o=s.length;var d=Math.ceil(o/2);n[a].many=s.slice(d,o),n[u].many=s.slice(0,d),u++}return n}var rt={clone:function(e){for(var t=[],n=1-Math.pow(1-e.path.style.opacity,1/e.count),r=0;r<e.count;r++){var a=Object(ie["a"])(e.path);a.setStyle("opacity",n),t.push(a)}return t},split:null};function at(e,t,n,r,a,o){if(e.length&&t.length){var i=Object(et["a"])("update",r,a);if(i&&i.duration>0){var u,s,d=r.getModel("universalTransition").get("delay"),c=Object.assign({setToFinal:!0},i);tt(e)&&(u=e,s=t),tt(t)&&(u=t,s=e);for(var l=u?u===e:e.length>t.length,f=u?nt(s,u):nt(l?t:e,[l?e:t]),p=0,h=0;h<f.length;h++)p+=f[h].many.length;var v=0;for(h=0;h<f.length;h++)m(f[h],l,v,p),v+=f[h].many.length}}function m(e,t,r,a,i){var u=e.many,s=e.one;if(1!==u.length||i)for(var l=Object(ne["defaults"])({dividePath:rt[n],individualDelay:d&&function(e,t,n,o){return d(e+r,a)}},c),f=t?Ke(u,s,l):Xe(s,u,l),p=f.fromIndividuals,h=f.toIndividuals,v=p.length,b=0;b<v;b++){y=d?Object(ne["defaults"])({delay:d(b,v)},c):c;o(p[b],h[b],t?u[b]:e.one,t?e.one:u[b],y)}else{var g=t?u[0]:s,O=t?s:u[0];if($e(g))m({many:[g],one:O},!0,r,a,!0);else{var y=d?Object(ne["defaults"])({delay:d(r,a)},c):c;Je(g,O,y),o(g,O,g,O,y)}}}}function ot(e){if(!e)return[];if(Object(ne["isArray"])(e)){for(var t=[],n=0;n<e.length;n++)t.push(ot(e[n]));return t}var r=[];return e.traverse((function(e){e instanceof ae["b"]&&!e.disableMorphing&&!e.invisible&&!e.ignore&&r.push(e)})),r}var it=n("80f0"),ut=n("e0d3"),st=(n("edae"),n("19ebf")),dt=1e4,ct=0,lt=1,ft=2,pt=Object(ut["o"])();function ht(e,t){for(var n=e.dimensions,r=0;r<n.length;r++){var a=e.getDimensionInfo(n[r]);if(a&&0===a.otherDims[t])return n[r]}}function vt(e,t,n){var r=e.getDimensionInfo(n),a=r&&r.ordinalMeta;if(r){var o=e.get(r.name,t);return a&&a.categories[o]||o+""}}function mt(e,t,n,r){var a=r?"itemChildGroupId":"itemGroupId",o=ht(e,a);if(o){var i=vt(e,t,o);return i}var u=e.getRawDataItem(t),s=r?"childGroupId":"groupId";return u&&u[s]?u[s]+"":r?void 0:n||e.getId(t)}function bt(e){var t=[];return Object(ne["each"])(e,(function(e){var n=e.data,r=e.dataGroupId;if(!(n.count()>dt))for(var a=n.getIndices(),o=0;o<a.length;o++)t.push({data:n,groupId:mt(n,o,r,!1),childGroupId:mt(n,o,r,!0),divide:e.divide,dataIndex:o})})),t}function gt(e,t,n){e.traverse((function(e){e instanceof ae["b"]&&Object(et["c"])(e,{style:{opacity:0}},t,{dataIndex:n,isFrom:!0})}))}function Ot(e){if(e.parent){var t=e.getComputedTransform();e.setLocalTransform(t),e.parent.remove(e)}}function yt(e){e.stopAnimation(),e.isGroup&&e.traverse((function(e){e.stopAnimation()}))}function jt(e,t,n){var r=Object(et["a"])("update",n,t);r&&e.traverse((function(e){if(e instanceof st["c"]){var t=Object(et["b"])(e);t&&e.animateFrom({style:t},r)}}))}function St(e,t){var n=e.length;if(n!==t.length)return!1;for(var r=0;r<n;r++){var a=e[r],o=t[r];if(a.data.getId(a.dataIndex)!==o.data.getId(o.dataIndex))return!1}return!0}function It(e,t,n){var r=bt(e),a=bt(t);function o(e,t,n,r,a){(n||e)&&t.animateFrom({style:n&&n!==e?Object(ne["extend"])(Object(ne["extend"])({},n.style),e.style):e.style},a)}var i=!1,u=ct,s=Object(ne["createHashMap"])(),d=Object(ne["createHashMap"])();r.forEach((function(e){e.groupId&&s.set(e.groupId,!0),e.childGroupId&&d.set(e.childGroupId,!0)}));for(var c=0;c<a.length;c++){var l=a[c].groupId;if(d.get(l)){u=lt;break}var f=a[c].childGroupId;if(f&&s.get(f)){u=ft;break}}function p(e,t){return function(n){var r=n.data,a=n.dataIndex;return t?r.getId(a):e?u===lt?n.childGroupId:n.groupId:u===ft?n.childGroupId:n.groupId}}var h=St(r,a),v={};if(!h)for(c=0;c<a.length;c++){var m=a[c],b=m.data.getItemGraphicEl(m.dataIndex);b&&(v[b.id]=!0)}function g(e,t){var n=r[t],u=a[e],s=u.data.hostModel,d=n.data.getItemGraphicEl(n.dataIndex),c=u.data.getItemGraphicEl(u.dataIndex);d!==c?d&&v[d.id]||c&&(yt(c),d?(yt(d),Ot(d),i=!0,at(ot(d),ot(c),u.divide,s,e,o)):gt(c,s,e)):c&&jt(c,u.dataIndex,s)}new it["a"](r,a,p(!0,h),p(!1,h),null,"multiple").update(g).updateManyToOne((function(e,t){var n=a[e],u=n.data,s=u.hostModel,d=u.getItemGraphicEl(n.dataIndex),c=Object(ne["filter"])(Object(ne["map"])(t,(function(e){return r[e].data.getItemGraphicEl(r[e].dataIndex)})),(function(e){return e&&e!==d&&!v[e.id]}));d&&(yt(d),c.length?(Object(ne["each"])(c,(function(e){yt(e),Ot(e)})),i=!0,at(ot(c),ot(d),n.divide,s,e,o)):gt(d,s,n.dataIndex))})).updateOneToMany((function(e,t){var n=r[t],u=n.data.getItemGraphicEl(n.dataIndex);if(!u||!v[u.id]){var s=Object(ne["filter"])(Object(ne["map"])(e,(function(e){return a[e].data.getItemGraphicEl(a[e].dataIndex)})),(function(e){return e&&e!==u})),d=a[e[0]].data.hostModel;s.length&&(Object(ne["each"])(s,(function(e){return yt(e)})),u?(yt(u),Ot(u),i=!0,at(ot(u),ot(s),n.divide,d,e[0],o)):Object(ne["each"])(s,(function(t){return gt(t,d,e[0])})))}})).updateManyToMany((function(e,t){new it["a"](t,e,(function(e){return r[e].data.getId(r[e].dataIndex)}),(function(e){return a[e].data.getId(a[e].dataIndex)})).update((function(n,r){g(e[n],t[r])})).execute()})).execute(),i&&Object(ne["each"])(t,(function(e){var t=e.data,r=t.hostModel,a=r&&n.getViewOfSeriesModel(r),o=Object(et["a"])("update",r,0);a&&r.isAnimationEnabled()&&o&&o.duration>0&&a.group.traverse((function(e){e instanceof ae["b"]&&!e.animators.length&&e.animateFrom({style:{opacity:0}},o)}))}))}function _t(e){var t=e.getModel("universalTransition").get("seriesKey");return t||e.id}function Gt(e){return Object(ne["isArray"])(e)?e.sort().join(","):e}function xt(e){if(e.hostModel)return e.hostModel.getModel("universalTransition").get("divideShape")}function Mt(e,t){var n=Object(ne["createHashMap"])(),r=Object(ne["createHashMap"])(),a=Object(ne["createHashMap"])();return Object(ne["each"])(e.oldSeries,(function(t,n){var o=e.oldDataGroupIds[n],i=e.oldData[n],u=_t(t),s=Gt(u);r.set(s,{dataGroupId:o,data:i}),Object(ne["isArray"])(u)&&Object(ne["each"])(u,(function(e){a.set(e,{key:s,dataGroupId:o,data:i})}))})),Object(ne["each"])(t.updatedSeries,(function(e){if(e.isUniversalTransitionEnabled()&&e.isAnimationEnabled()){var t=e.get("dataGroupId"),o=e.getData(),i=_t(e),u=Gt(i),s=r.get(u);if(s)n.set(u,{oldSeries:[{dataGroupId:s.dataGroupId,divide:xt(s.data),data:s.data}],newSeries:[{dataGroupId:t,divide:xt(o),data:o}]});else if(Object(ne["isArray"])(i)){0;var d=[];Object(ne["each"])(i,(function(e){var t=r.get(e);t.data&&d.push({dataGroupId:t.dataGroupId,divide:xt(t.data),data:t.data})})),d.length&&n.set(u,{oldSeries:d,newSeries:[{dataGroupId:t,data:o,divide:xt(o)}]})}else{var c=a.get(i);if(c){var l=n.get(c.key);l||(l={oldSeries:[{dataGroupId:c.dataGroupId,data:c.data,divide:xt(c.data)}],newSeries:[]},n.set(c.key,l)),l.newSeries.push({dataGroupId:t,data:o,divide:xt(o)})}}}})),n}function wt(e,t){for(var n=0;n<e.length;n++){var r=null!=t.seriesIndex&&t.seriesIndex===e[n].seriesIndex||null!=t.seriesId&&t.seriesId===e[n].id;if(r)return n}}function Ct(e,t,n,r){var a=[],o=[];Object(ne["each"])(Object(ut["r"])(e.from),(function(e){var n=wt(t.oldSeries,e);n>=0&&a.push({dataGroupId:t.oldDataGroupIds[n],data:t.oldData[n],divide:xt(t.oldData[n]),groupIdDim:e.dimension})})),Object(ne["each"])(Object(ut["r"])(e.to),(function(e){var r=wt(n.updatedSeries,e);if(r>=0){var a=n.updatedSeries[r].getData();o.push({dataGroupId:t.oldDataGroupIds[r],data:a,divide:xt(a),groupIdDim:e.dimension})}})),a.length>0&&o.length>0&&It(a,o,r)}function Tt(e){e.registerUpdateLifecycle("series:beforeupdate",(function(e,t,n){Object(ne["each"])(Object(ut["r"])(n.seriesTransition),(function(e){Object(ne["each"])(Object(ut["r"])(e.to),(function(e){for(var t=n.updatedSeries,r=0;r<t.length;r++)(null!=e.seriesIndex&&e.seriesIndex===t[r].seriesIndex||null!=e.seriesId&&e.seriesId===t[r].id)&&(t[r][te["a"]]=!0)}))}))})),e.registerUpdateLifecycle("series:transition",(function(e,t,n){var r=pt(t);if(r.oldSeries&&n.updatedSeries&&n.optionChanged){var a=n.seriesTransition;if(a)Object(ne["each"])(Object(ut["r"])(a),(function(e){Ct(e,r,n,t)}));else{var o=Mt(r,n);Object(ne["each"])(o.keys(),(function(e){var n=o.get(e);It(n.oldSeries,n.newSeries,t)}))}Object(ne["each"])(n.updatedSeries,(function(e){e[te["a"]]&&(e[te["a"]]=!1)}))}for(var i=e.getSeries(),u=r.oldSeries=[],s=r.oldDataGroupIds=[],d=r.oldData=[],c=0;c<i.length;c++){var l=i[c].getData();l.count()<dt&&(u.push(i[c]),s.push(i[c].get("dataGroupId")),d.push(l))}}))}var Dt=n("ee29");Object(r["a"])([o["a"]]),Object(r["a"])([i["a"]]),Object(r["a"])([u["a"],s["a"],d["a"],c["a"],l["a"],f["a"],p["a"],h["a"],v["a"],m["a"],b["a"],g["a"],O["a"],y["a"],j["a"],S["a"],I["a"],_["a"],G["a"],x["a"],M["a"],w["a"]]),Object(r["a"])(C["a"]),Object(r["a"])(T["a"]),Object(r["a"])(D["a"]),Object(r["a"])(P["a"]),Object(r["a"])(U["a"]),Object(r["a"])(R["a"]),Object(r["a"])(k["a"]),Object(r["a"])(F["a"]),Object(r["a"])(L["a"]),Object(r["a"])(N["a"]),Object(r["a"])(E["a"]),Object(r["a"])(A["a"]),Object(r["a"])($["a"]),Object(r["a"])(Y["a"]),Object(r["a"])(B["a"]),Object(r["a"])(z["a"]),Object(r["a"])(q["a"]),Object(r["a"])(V["a"]),Object(r["a"])(J["a"]),Object(r["a"])(H["a"]),Object(r["a"])(W["a"]),Object(r["a"])(Z["a"]),Object(r["a"])(Q["a"]),Object(r["a"])(K["a"]),Object(r["a"])(X["a"]),Object(r["a"])(ee["a"]),Object(r["a"])(Tt),Object(r["a"])(Dt["a"])},"3d40":function(e,t,n){"use strict";n.r(t);var r=n("d9db"),a=n("86a3");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"61375c71",null);t["default"]=u.exports},4725:function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("5539")),o=r(n("97ff"));t.default={name:"FlowPendingFlowVerify",components:{verify:a.default},mixins:[o.default],data:function(){return{}},methods:{handleSubmit:function(){this.tagBack()}}}},4750:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("verify",{on:{handleSubmit:e.handleSubmit}})},a=[]},"4d8e":function(e,t,n){"use strict";n("75a6")},"4f1a":function(e,t){e.exports="https://integ-plat-produce-test.bimtk.com/static/img/gantt-chartbg-3.47933cf2.png"},5539:function(e,t,n){"use strict";n.r(t);var r=n("afff"),a=n("2601");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("4d8e");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"863c55f2",null);t["default"]=u.exports},"61ce":function(e,t){e.exports="https://integ-plat-produce-test.bimtk.com/static/img/gantt-chartbg-4.9fec9601.png"},"6eab":function(e,t,n){"use strict";n.r(t);var r=n("4725"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},"75a6":function(e,t,n){},"86a3":function(e,t,n){"use strict";n.r(t);var r=n("8e39"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},8759:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:["value"],computed:{comment_disabled:{get:function(){return this.value},set:function(e){this.$emit("input",e)}}},methods:{getTxt:function(){switch(this.comment_disabled){case 1:return"同意";case 2:return"不同意";case 3:return"驳回";default:break}}}}},"89b7":function(e,t,n){},"8e39":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("5530")),o=r(n("0404")),i=r(n("c0f5")),u=r(n("c093"));t.default={components:(0,a.default)({FlowModuleNote:o.default,PlanView:i.default},u.default),props:{code:{type:String,default:""},datas:{type:Object,default:function(){return{}}}},data:function(){return{currentComponent:""}},mounted:function(){var e;this.currentComponent=null!==(e=this.$options["components"][this.code])&&void 0!==e?e:null}}},"97ff":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("14d9"),n("fb6a");t.default={methods:{tagBack:function(){var e=this,t=this.$route;this.$store.dispatch("tagsView/delView",t).then((function(n){var r=n.visitedViews;if(t.path===e.$route.path){var a=r.slice(-1)[0];e.$router.push(a.fullPath)}}))}}}},9911:function(e,t,n){"use strict";var r=n("23e7"),a=n("857a"),o=n("af03");r({target:"String",proto:!0,forced:o("link")},{link:function(e){return a(this,"a","href",e)}})},afff:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{height:"calc(100vh - 130px)","overflow-y":"scroll"}},[n("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[e._v(" "+e._s(e.title)+" ")]},proxy:!0},e.readable?null:{key:"right",fn:function(){return[n("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入审核意见"},model:{value:e.postObj.verificationOpinion,callback:function(t){e.$set(e.postObj,"verificationOpinion",t)},expression:"postObj.verificationOpinion"}}),n("verify-status",{staticStyle:{"margin-right":"5px"},model:{value:e.postObj.verificationFinally,callback:function(t){e.$set(e.postObj,"verificationFinally",t)},expression:"postObj.verificationFinally"}}),1===e.postObj.verificationFinally&&e.showTag?n("el-card",{staticClass:"cs-card",attrs:{type:"box-card"}},[n("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.postObj,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"是否加签：",prop:"isEdit"}},[n("el-radio",{attrs:{label:1},model:{value:e.postObj.isEdit,callback:function(t){e.$set(e.postObj,"isEdit",t)},expression:"postObj.isEdit"}},[e._v("是")]),n("el-radio",{attrs:{disabled:e.radioDisableF,label:0},model:{value:e.postObj.isEdit,callback:function(t){e.$set(e.postObj,"isEdit",t)},expression:"postObj.isEdit"}},[e._v("否")])],1),e.postObj.isEdit?n("div",[n("el-form-item",{attrs:{label:"指定用户：",prop:"User_Id"}},[n("el-select",{attrs:{clearable:"",filterable:"",placeholder:"请选择"},model:{value:e.postObj.User_Id,callback:function(t){e.$set(e.postObj,"User_Id",t)},expression:"postObj.User_Id"}},e._l(e.userOptions,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"名称：",prop:"Node_Name"}},[n("el-input",{model:{value:e.postObj.Node_Name,callback:function(t){e.$set(e.postObj,"Node_Name",t)},expression:"postObj.Node_Name"}})],1)],1):e._e()],1)],1):e._e(),"2"===e.postObj.NodeRejectType&&3===e.postObj.verificationFinally?n("el-select",{attrs:{placeholder:""},model:{value:e.postObj.NodeRejectStep,callback:function(t){e.$set(e.postObj,"NodeRejectStep",t)},expression:"postObj.NodeRejectStep"}},e._l(e.FlowNodes,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.name,value:e.Id}})})),1):e._e(),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{loading:e.btnLoading,type:"success"},on:{click:e.submitForm}},[e._v("提交 ")])]},proxy:!0}],null,!0)}),n("div",{staticClass:"createPost-container"},[e.moduleType?n("page-modules",{ref:"PageModule",attrs:{code:e.moduleType,datas:e.formData,"is-edit":!1}}):e._e(),n("el-timeline",{attrs:{reverse:!0}},e._l(e.histories,(function(t,r){return n("el-timeline-item",{key:r,attrs:{timestamp:e._f("timeFormat")(t.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}")}},[e._v(" "+e._s(t.Content)+" "),n("div",[n("span",{},[e._v(e._s(t.Create_UserName))]),t.Next_Maker_List?n("span",[n("el-divider",{attrs:{direction:"vertical"}}),e._v(" 下一步： ")],1):e._e(),n("span",[e._v(e._s(t.Next_Maker_List))])])])})),1),n("created-flow",{attrs:{"form-template":null,"is-edit":!0,"is-show-content":!0,"scheme-content":e.schemeContent}})],1)],1)},a=[]},b0ab:function(e,t,n){"use strict";n.r(t);var r=n("16c0"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},bac8:function(e,t,n){"use strict";n.r(t);var r=n("1f3d"),a=n("13d57");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,null,null);t["default"]=u.exports},bfd4:function(e,t,n){"use strict";n.r(t);var r=n("1cb4"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},c093:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d3b7"),n("3ca3"),n("ddb0");t.default={GeneralContract:function(){return n.e("chunk-076993c0").then(n.bind(null,"2e5a"))},AllographContract:function(){return n.e("chunk-136d1f4f").then(n.bind(null,"b788"))},PriceFlow:function(){return n.e("chunk-6b154538").then(n.bind(null,"7391"))},ProcurementFlow:function(){return n.e("chunk-5f508548").then(n.bind(null,"55cc"))},SealFlow:function(){return n.e("chunk-3307be8c").then(n.bind(null,"e35d"))},TechSolution:function(){return n.e("chunk-d6f396d6").then(n.bind(null,"6868"))}}},c0f5:function(e,t,n){"use strict";n.r(t);var r=n("11e1"),a=n("bfd4");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("e54f");var i=n("2877"),u=Object(i["a"])(a["default"],r["a"],r["b"],!1,null,"9d523f34",null);t["default"]=u.exports},d682:function(e,t){e.exports="https://integ-plat-produce-test.bimtk.com/static/img/gantt-chartbg-1.8c6c8667.png"},d9db:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{position:"relative","min-height":"500px"}},[n(e.currentComponent,{ref:"FrameModule",tag:"component",attrs:{datas:e.datas,"is-edit":!1}})],1)},a=[]},e41b:function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=s,t.GetPartsImportTemplate=c,t.GetPartsList=u,t.GetProjectAreaTreeList=o,t.ImportParts=d,t.SaveProjectAreaSort=i;var a=r(n("b775"));function o(e){return(0,a.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,a.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function u(e){return(0,a.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function s(e){return(0,a.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function d(e){return(0,a.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function c(e){return(0,a.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},e54f:function(e,t,n){"use strict";n("1f88")},e6ca:function(e,t,n){"use strict";n("89b7")},ea13:function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteGroup=d,t.DeleteGroupRole=v,t.DeleteGroupUser=l,t.DeleteUserRole=S,t.GetGroupEntity=u,t.GetGroupList=o,t.GetGroupRole=p,t.GetGroupTree=i,t.GetGroupUser=f,t.GetGroupUserByRole=O,t.GetRoleListCanAdd=h,t.GetShuttleUserList=y,t.GetWorkingObjTreeListByGroupId=g,t.SaveGroup=s,t.SaveGroupObject=b,t.SaveGroupRole=m,t.SaveGroupUser=c,t.SaveUserRoles=j;var a=r(n("b775"));function o(){return(0,a.default)({url:"/SYS/UserGroup/GetGroupList",method:"post"})}function i(e){return(0,a.default)({url:"/SYS/UserGroup/GetGroupTree",method:"post",data:e})}function u(e){return(0,a.default)({url:"/SYS/UserGroup/GetGroupEntity",method:"post",data:e})}function s(e){return(0,a.default)({url:"/SYS/UserGroup/SaveGroup",method:"post",data:e})}function d(e){return(0,a.default)({url:"/SYS/UserGroup/DeleteGroup",method:"post",data:e})}function c(e){return(0,a.default)({url:"/SYS/UserGroup/SaveGroupUser",method:"post",data:e})}function l(e){return(0,a.default)({url:"/SYS/UserGroup/DeleteGroupUser",method:"post",data:e})}function f(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetGroupUser",data:e})}function p(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetGroupRole",data:e})}function h(e){return(0,a.default)({method:"post",url:"sys/UserGroup/GetRoleListCanAdd",data:e})}function v(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/DeleteGroupRole",data:e})}function m(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/SaveGroupRole",data:e})}function b(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/SaveGroupObject",data:e})}function g(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetWorkingObjTreeListByGroupId",data:e})}function O(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetGroupUserByRole",data:e})}function y(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetShuttleUserList",data:e})}function j(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/SaveUserRoles",data:e})}function S(e){return(0,a.default)({method:"post",url:"/SYS/UserGroup/DeleteUserRole",data:e})}},eaae:function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("e9f5"),n("910d"),n("e9c4"),n("b64b"),n("d3b7"),n("ac1f"),n("466d");var a=n("eb4a"),o=r(n("34e9")),i=r(n("0b09")),u=r(n("bac8")),s=n("6186"),d=r(n("3d40"));t.default={name:"MyFlowVerify2",components:{TopHeader:o.default,CreatedFlow:i.default,verifyStatus:u.default,pageModules:d.default},props:{isEdit:{type:Boolean,default:!1},readable:{type:Boolean,default:!1}},data:function(){return{form:{},histories:[],userOptions:[],showTag:!1,postObj:{isEdit:0,User_Id:"",Node_Name:"",Callback_Url:"",flowInstanceId:"",verificationOpinion:"",verificationFinally:1,NodeRejectStep:"",NodeRejectType:"0"},options:[{value:"0",label:"上一步"},{value:"1",label:"第一步"},{value:"2",label:"指定步骤"}],FlowNodes:[],title:"",flowSchemesOptionList:[],btnLoading:!1,radioDisableF:!1,formData:{},currentSchemeId:"",moduleType:"",Activity_Id:"",schemeContent:""}},mounted:function(){this.postObj.flowInstanceId=this.$route.query.id||"",this.getHistoryOptions(),this.getFlowInstances(),this.getUserList()},provide:function(){return{changeFlowInstanceData:this.changeFlowInstanceData}},methods:{changeFlowInstanceData:function(e){this.postObj.FormDataJson=JSON.stringify(e)},getModuleCode:function(){var e=this;setTimeout((function(t){e.moduleType="FlowModuleNote"}),100)},getFlowInstances:function(){var e=this;(0,a.FlowInstancesGet)({Id:this.postObj.flowInstanceId}).then((function(t){if(t.IsSucceed){e.schemeContent=JSON.parse(JSON.stringify(t.Data.Scheme_Content)),e.formData=JSON.parse(t.Data.Frm_Data),e.Activity_Id=t.Data.Activity_Id,e.title=t.Data.Custom_Name,e.FlowNodes=JSON.parse(t.Data.Scheme_Content).nodes,e.moduleType=t.Data.Web_Id;var n=e.findCurrentNode();n.length&&n[0].isadd&&(e.postObj.isEdit=0,e.radioDisableF=!1,e.showTag=!0),n.length&&n[0].mustadd&&(e.postObj.isEdit=1,e.radioDisableF=!0,e.showTag=!0),e.isAddTag()&&(e.showTag=!1)}}))},isAddTag:function(){return 5===this.Activity_Id.match(/-/g).length},findCurrentNode:function(){var e=this;return this.FlowNodes.filter((function(t){return t.id===e.Activity_Id}))},getUserList:function(){var e=this;(0,s.GetUserList)({}).then((function(t){e.userOptions=t.Data}))},getHistoryOptions:function(){var e=this;(0,a.QueryHistories)({FlowInstanceId:this.$route.query.id||""}).then((function(t){t.IsSucceed&&(e.histories=t.Data)}))},submitFn:function(e){var t=this;(0,a.Verification)(e).then((function(e){e.IsSucceed?(t.$message({message:"审批成功",type:"success"}),t.$emit("handleSubmit")):t.$message({message:"提交失败",type:"error"}),t.btnLoading=!1}))},submitForm:function(){if(1!==this.postObj.isEdit||this.postObj.User_Id.length){var e=Object.assign(this.postObj);if(1===e.verificationFinally&&e.isEdit&&this.showTag||(delete e.User_Id,delete e.Node_Name,delete e.Callback_Url),delete e.isEdit,this.btnLoading=!0,"businessmonthlyassessment"===this.moduleType||"managementmonthlyassessment"===this.moduleType||"managementyearlyassessment"===this.moduleType){var t=JSON.parse(localStorage.getItem("epcBusinessAssessment"));t.Details&&t.Details.length>0?(e.FormDataJson=JSON.stringify(t),this.submitFn(e)):(this.$message({type:"error",message:"请填完评分内容在提交!"}),this.btnLoading=!1)}else this.submitFn(e)}else this.$message({message:"请指定用户",type:"warning"})}}}},eca2:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-card",{staticClass:"box-card",attrs:{shadow:"never"}},[n("el-form",{ref:"ruleForm",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"姓名",prop:"User_Name"}},[n("el-input",{attrs:{disabled:!0},model:{value:e.form.User_Name,callback:function(t){e.$set(e.form,"User_Name",t)},expression:"form.User_Name"}})],1),n("el-form-item",{attrs:{label:"天数",prop:"Days"}},[n("el-input-number",{attrs:{disabled:!e.isEdit,min:1},model:{value:e.form.Days,callback:function(t){e.$set(e.form,"Days",t)},expression:"form.Days"}})],1),n("el-form-item",{attrs:{label:"原因",prop:"Request_Comment"}},[n("el-input",{attrs:{autosize:{minRows:3,maxRows:5},disabled:!e.isEdit,maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Request_Comment,callback:function(t){e.$set(e.form,"Request_Comment",t)},expression:"form.Request_Comment"}})],1)],1)],1),e.isEdit?n("el-button",{staticClass:"cs-btn",attrs:{disabled:e.btnDisabled,loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("保存 ")]):e._e()],1)},a=[]},f382:function(e,t,n){"use strict";function r(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=r(e.Children)),!0)}))}function a(e){e.map((function(e){if(e.Is_Directory||!e.Children)return a(e.Children);delete e.Children}))}function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function r(e,t,a){for(var o=0;o<e.length;o++){var i=e[o];if(i.Id===t)return n&&a.push(i),a;if(i.Children&&i.Children.length){if(a.push(i),r(i.Children,t,a).length)return a;a.pop()}}return[]}return r(e,t,[])}function i(e){return e.Children&&e.Children.length>0?i(e.Children[0]):e}function u(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&u(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=a,t.disableDirectory=u,t.findAllParentNode=o,t.findFirstNode=i,t.getDirectoryTree=r,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7")},f44c:function(e,t,n){}}]);