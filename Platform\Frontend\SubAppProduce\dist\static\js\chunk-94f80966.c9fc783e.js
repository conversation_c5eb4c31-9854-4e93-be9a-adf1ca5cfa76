(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-94f80966"],{"01b6":function(e,t,n){"use strict";var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n("94ac"));t.default={name:"PROTransferReceiveView",components:{Detail:a.default},data:function(){return{}}}},3920:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("detail",{attrs:{"is-view":""}})},a=[]},"879f9":function(e,t,n){"use strict";n.r(t);var i=n("01b6"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},"93fe":function(e,t,n){"use strict";n.r(t);var i=n("d251"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},"94ac":function(e,t,n){"use strict";n.r(t);var i=n("e7c7"),a=n("93fe");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("edd1");var o=n("2877"),s=Object(o["a"])(a["default"],i["a"],i["b"],!1,null,"e94813c8",null);t["default"]=s.exports},"985a":function(e,t,n){"use strict";n.r(t);var i=n("3920"),a=n("879f9");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);var o=n("2877"),s=Object(o["a"])(a["default"],i["a"],i["b"],!1,null,"6806f527",null);t["default"]=s.exports},d251:function(e,t,n){"use strict";var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("c740"),n("d81d"),n("14d9"),n("a434"),n("e9f5"),n("7d54"),n("ab43"),n("b64b"),n("d3b7"),n("159b");var a=i(n("5530")),r=i(n("c14f")),o=i(n("1da1")),s=i(n("d7b0")),l=i(n("15ac")),c=n("7f9d"),u=i(n("d055")),f=n("8975"),d=n("ed08"),m=i(n("6612")),p="$_$",v="Receiveing_Count",h="Team_Refused_Count",_="\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-col {\n          height: 28px;\n          line-height: 28px;\n          min-width:30%;\n          display: inline-block;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        ";t.default={components:{QrcodeVue:s.default},filters:{filterNum:function(e){return e?(0,m.default)(e).divide(1e3).format("0.[00]"):0}},mixins:[l.default],data:function(){var e=this;return{printConfig:{sheetName:"转移单接收",style:_,beforePrintMethod:function(t){var n=t.content;return e.topHtml+n}},finishList:[],tbLoading:!1,queryInfo:{Page:1,PageSize:-1},tbData:[],columns:[],multipleSelection:[],inputList:[{code:v,name:"接收"},{code:h,name:"拒收"}],formInline:{},Tenant_Code:localStorage.getItem("tenant")}},computed:{isCom:function(){return"com"===this.pageType},isView:function(){var e;return"view"===(null===(e=this.$route.query)||void 0===e?void 0:e.type)}},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var n,i,a,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:t.p=0,i=JSON.parse(decodeURIComponent(null===(n=e.$route.query)||void 0===n?void 0:n.other)),e.formInline=Object.assign({},e.formInline,i),e.pageType=e.$route.query.pg_type,t.n=2;break;case 1:return t.p=1,t.v,e.$message({message:"参数错误",type:"error"}),t.a(2);case 2:return t.n=3,e.getTableConfig("PROComTransferReceiveDetail");case 3:e.isCom?(a=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==a&&e.columns.splice(a,1)):(o=e.columns.findIndex((function(e){return"Comp_Code"===e.Code})),-1!==o&&e.columns.splice(o,1)),e.queryInfo.PageSize=-1,e.fetchData();case 4:return t.a(2)}}),t,null,[[0,1]])})))()},methods:{fetchData:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,e.fetchList();case 1:n=t.v,e.isView?e.tbData=n:e.initTable(n),e.multipleSelection=[],e.tbLoading=!1;case 2:return t.a(2)}}),t)})))()},fetchList:function(){var e=this,t=c.GetTransferDetail;return new Promise((function(n,i){t((0,a.default)({Transfer_Code:e.formInline.Transfer_Code,Process_Type:e.isCom?2:1},e.queryInfo)).then((function(e){e.IsSucceed?n(e.Data.map((function(e){return e.checked=!1,e}))):i(e.Message)}))})).catch((function(t){e.$message({message:t,type:"error"})}))},initTable:function(e){var t=this;this.tbData=e.map((function(e){return t.inputList.forEach((function(n){var i=t.getRowUniqueMax(n.code);n.code===v?e[i]=e.Can_Receive_Count||0:e[i]=(e.Can_Receive_Count||0)-(e[v]||0)})),e}))},inputChange:function(e,t,n){if(n===v){var i=this.getRowUniqueMax(h);t[i]=t.Can_Receive_Count-e.value}else if(n===h){var a=this.getRowUniqueMax(v);t[a]=t.Can_Receive_Count-e.value}},handleReceive:function(e){var t=this,n=e?"是否默认接收当前全部数据?":"是否接收当前数据?";this.$confirm(n,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n=null;n=e?t.multipleSelection.map((function(e){return e.Receiveing_Count=e.Can_Receive_Count,e})):t.multipleSelection,(0,c.ReceiveTransferTask)(n).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},getInnerTable:function(e,t){},getHtml:function(){var e=this;return new Promise((function(t,n){u.default.toDataURL("T=".concat(e.formInline.Transfer_Code,"&C=").concat(e.Tenant_Code)).then((function(n){var i;e.topHtml='\n        <h1 class="title">'.concat(e.printConfig.sheetName,'</h1>\n        <div class="my-top">\n          <div class="left">\n            <div class="my-list-row">\n              <div class="my-list-col">转移单编号：').concat((null===(i=e.formInline)||void 0===i?void 0:i.Transfer_Code)||"",'</div>\n              <div class="my-list-col">转移时间：').concat((0,f.timeFormat)(e.formInline.Transfer_Time||""),'</div>\n              <div class="my-list-col">转出班组：').concat(e.formInline.Source_Team_Name||"",'</div>\n            </div>\n            <div class="my-list-row">\n              <div class="my-list-col">转入数量：').concat(e.formInline.Transfer_Count||"",'</div>\n              <div class="my-list-col">转入重量：').concat(e.formInline.Transfer_Weight||"",'</div>\n              <div class="my-list-col">接收工序：').concat(e.formInline.Target_Process_Name||"",'</div>\n              <div class="my-list-col">接收班组：').concat(e.formInline.Target_Team_Name||"",'</div>\n            </div>\n          </div>\n          <div class="right">\n            <div class="qrcode">\n                <img src="').concat(n,'">\n            </div>\n          </div>\n        </div>\n        '),t()}))}))},getRowUniqueMax:function(e){return"".concat(e).concat(p,"max")},printEvent:function(){var e=this;this.getHtml().then((function(t){e.$refs.xTable.print({sheetName:e.printConfig.sheetName,style:_,mode:"selected",columns:e.columns.map((function(e){return{field:e.Code}})),beforePrintMethod:function(t){var n=t.content;return e.topHtml+n}})}))},handleExport:function(){var e=this;(0,c.ExportTransferCodeDetail)({Transfer_Code:this.formInline.Transfer_Code,Process_Type:this.isCom?2:1}).then((function(t){t.IsSucceed?(e.$message({message:"导出成功",type:"success"}),window.open((0,d.combineURL)(e.$baseUrl,t.Data),"_blank")):e.$message({message:t.Message,type:"error"})}))},tbSelectChange:function(e){this.multipleSelection=e.records},closeView:function(){(0,d.closeTagView)(this.$store,this.$route)}}}},e7c7:function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return a}));var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-card",{staticClass:"box-card h100"},[n("h4",{staticClass:"topTitle"},[n("span"),e._v("基本信息")]),n("el-form",{ref:"formInline",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline}},[n("el-row",[n("el-col",{attrs:{span:20}},[n("el-row",[n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转移单编号:",prop:"Transfer_Code"}},[n("span",[e._v(e._s(e.formInline.Transfer_Code))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转移时间:",prop:"Transfer_Time"}},[n("span",[e._v(e._s(e._f("timeFormat")(e.formInline.Transfer_Time)))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转出班组:",prop:"Source_Team_Name"}},[n("span",[e._v(e._s(e.formInline.Source_Team_Name))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转入数量:",prop:"Transfer_Count"}},[n("span",[e._v(e._s(e.formInline.Transfer_Count))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转入重量:",prop:"Transfer_Weight"}},[n("span",[e._v(e._s(e._f("filterNum")(e.formInline.Transfer_Weight))+"(t)")])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"接收工序:",prop:"Target_Process_Name"}},[n("span",[e._v(e._s(e.formInline.Target_Process_Name))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"接收班组:",prop:"Target_Team_Name "}},[n("span",[e._v(e._s(e.formInline.Target_Team_Name))])])],1)],1)],1),n("el-col",{attrs:{span:4}},[n("qrcode-vue",{attrs:{"class-name":"qrcode",size:79,value:"T="+e.formInline.Transfer_Code+"&C="+e.Tenant_Code,level:"H"}})],1)],1)],1),n("el-divider",{staticClass:"elDivder"}),n("vxe-toolbar",{ref:"xToolbar",scopedSlots:e._u([{key:"buttons",fn:function(){return[e.isView?e._e():n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleReceive(!0)}}},[e._v("全部接收")]),n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:e.printEvent}},[e._v("打印表格")]),n("el-button",{attrs:{type:"success"},on:{click:e.handleExport}},[e._v("导出")])]},proxy:!0}])}),n("div",{staticClass:"tb-x"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"print-config":e.printConfig,"checkbox-config":{checkField:"checked"},align:"left",height:"100%","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange},scopedSlots:e._u([{key:"Back_Count",fn:function(t){var i=t.row;return[n("el-popover",{attrs:{placement:"bottom",trigger:"click",width:"400"},on:{show:function(t){return e.getInnerTable(i)}}},[n("div",{staticClass:"tooltip-content"},[n("el-table",{staticClass:"cs-custom-table",attrs:{"highlight-current-row":"",height:"300px",data:e.finishList}},[n("el-table-column",{attrs:{align:"center",prop:e.isCom?"Comp_Code":"Part_Code",label:e.isCom?"构件名称":"零件名称"}}),n("el-table-column",{attrs:{align:"center",prop:"num",label:"拒收数量"}}),n("el-table-column",{attrs:{align:"center",prop:"date",label:"拒收时间"}})],1)],1),n("el-link",{attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v(e._s(i.Back_Count))])],1)]}}])},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(e){return[n("vxe-column",{key:e.Id,attrs:{fixed:["Comp_Code","Part_Code"].includes(e.Code)?"left":"","show-overflow":"tooltip",sortable:"",field:e.Code,title:e.Display_Name,"min-width":e.Width,align:e.Align,width:"auto"}})]})),e.isView?e._e():e._l(e.inputList,(function(t,i){return n("vxe-column",{key:i,attrs:{fixed:"right","edit-render":{},field:t.code,title:t.name,sortable:"","min-width":"170"},scopedSlots:e._u([{key:"edit",fn:function(i){var a=i.row;return[n("vxe-input",{attrs:{type:"integer",min:0,max:a[e.getRowUniqueMax(t.code)]},on:{change:function(n){return e.inputChange(n,a,t.code)}},model:{value:a[t.code],callback:function(n){e.$set(a,t.code,e._n(n))},expression:"row[element.code]"}})]}},{key:"default",fn:function(n){var i=n.row;return[e._v(" "+e._s(i[t.code])+" ")]}}],null,!0)})}))],2)],1),e.isView?e._e():n("div",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:e.closeView}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleReceive(!1)}}},[e._v("确 定")])],1)],1)],1)},a=[]},edd1:function(e,t,n){"use strict";n("f5cf")},f5cf:function(e,t,n){}}]);