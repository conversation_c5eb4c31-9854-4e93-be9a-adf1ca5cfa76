(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-71bbf3e5"],{"06cb":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n("586a");e.default={props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""},isCom:{type:Boolean,default:!0}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},tbData:[],rowId:0}},methods:{init:function(t){var e=this;this.rowId=t.Id,this.tbLoading=!0;var n=this.isCom?a.GetSchedulingProcessingCompList:a.GetSchedulingProcessingPartList;n({id:t.Id}).then((function(t){t.IsSucceed?e.tbData=t.Data:e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))}}}},"127a":function(t,e,n){"use strict";n("d151")},2736:function(t,e,n){"use strict";n.r(e);var a=n("308e"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},"2e8a":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteComponentType=l,e.GetCompTypeTree=d,e.GetComponentTypeEntity=u,e.GetComponentTypeList=r,e.GetFactoryCompTypeIndentifySetting=h,e.GetTableSettingList=m,e.GetTypePageList=s,e.RestoreTemplateType=C,e.SavDeepenTemplateSetting=y,e.SaveCompTypeIdentifySetting=b,e.SaveComponentType=c,e.SaveProBimComponentType=f,e.UpdateColumnSetting=g,e.UpdateComponentPartTableSetting=p;var o=a(n("b775")),i=a(n("4328"));function r(t){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:i.default.stringify(t)})}function l(t){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:i.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:i.default.stringify(t)})}function f(t){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:t})}function h(t){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:t})}function b(t){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:t})}function y(t){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:t})}function C(t){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:t})}},"308e":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("ade3"));n("99af"),n("4de4"),n("caad"),n("d81d"),n("13d5"),n("e9f5"),n("910d"),n("7d54"),n("ab43"),n("9485"),n("a9e3"),n("b64b"),n("d3b7"),n("ac1f"),n("25f0"),n("2532"),n("5319"),n("498a"),n("c7cd"),n("159b");var i=n("6186"),r=n("2e8a"),s=a(n("0f97")),c=a(n("5f51")),u=n("3f35"),l=n("5641"),d=n("ed08");e.default=(0,o.default)((0,o.default)({name:"PROComponentDetail",components:{DynamicDataTable:s.default,RelationFormDialog:c.default},data:function(){return{tbLoading:!1,btnLoading:!1,isClicked:!1,confirmed:!1,warehouses:[],locations:[],tbConfig:{},columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:-1,PageSizes:[20,40,60,80,100]},form:{Codes:"",Codes_Format:"",ComponentType:""},Stock_Status_Data:[{Name:"工厂打包",Id:0},{Name:"仓库打包",Id:3},{Name:"仓库打包",Id:4}],TypeData:[],Details:[],multiSelected:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"50%"},gridCode:"pro_packing_detail_list",Proportion:0,Unit:"",totalAmount:0,totalWeight:0,Id:""}},computed:{mode:function(){return this.$router.currentRoute.query.mode}},mounted:function(){var t=this;this.Id=this.$router.currentRoute.query.Id,(0,l.getFactoryProfessional)().then((function(e){t.Proportion=e[0].Proportion,t.Unit=e[0].Unit,t.getComponentTypeList()}))},created:function(){},beforeRouteEnter:function(t,e,n){n((function(t){t.initRouterParams()}))},beforeRouteUpdate:function(t,e,n){this.initRouterParams(),n()},beforeRouteLeave:function(t,e,n){this.confirmed?n():this.$confirm("此操作不会保存已修改但尚未保存的数据，是否离开?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n()}))}},"created",(function(){this.initRouterParams(),this.getGridByCode()})),"methods",{getComponentTypeList:function(){var t=this;(0,r.GetComponentTypeList)({Category_Id:this.ProfessionalId,Factory_Id:localStorage.getItem("CurReferenceId")}).then((function(e){e.IsSucceed&&(t.TypeData=e.Data)}))},getGridByCode:function(){var t=this;this.tbLoading=!0,(0,i.GetGridByCode)({Code:this.gridCode}).then((function(e){var n=e.IsSucceed,a=e.Data;e.Message;if(n){t.tbConfig=Object.assign({},t.tbConfig,a.Grid);var o=a.ColumnList||[];t.columns=o.filter((function(t){return t.Is_Display})).map((function(t){return 0==t.Width?t.minWidth=120:t.minWidth=t.Width,l.FIX_COLUMN.includes(t.Code)&&(t.fixed="left"),t})),t.tbLoading=!1,1==t.mode&&t.Id&&t.fetchData()}}))},fetchData:function(){var t=this;this.tbLoading=!0,(0,u.GetPacking2ndEntity)({id:this.Id}).then((function(e){e.IsSucceed&&t.setGridData(e.Data)})).catch(console.error).finally((function(){t.tbLoading=!1}))},tagBack:function(){var t=this;this.$confirm("此操作不会保存数据，是否离开?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.confirmed=!0,(0,d.closeTagView)(t.$store,t.$route)})).catch((function(){t.$message({type:"info",message:"已取消"})}))},subWeight:function(){var t=0,e=0;this.tbData.length<=0?(t=0,e=0):(t=this.tbData.map((function(e){return e.SteelAmount+=t})).reduce((function(t,e){return Number(t)+Number(e)})),e=this.tbData.map((function(t){return t.SteelWeight*t.SteelAmount})).reduce((function(t,e){return Number(t)+Number(e)}))),this.totalAmount=t,this.totalWeight=Math.round(e/this.Proportion*1e3)/1e3},multiSelectedChange:function(t){t.checked;var e=this.$refs.xTable.getCheckboxRecords();this.multiSelected=e},activeCellMethod:function(t){t.row;var e=t.column;t.columnIndex;return"SteelAmount"===e.field},initRouterParams:function(){if(1!=this.mode){var t=JSON.parse(sessionStorage.getItem("PackageParams"));this.form=Object.assign({},this.form,t)}},setGridData:function(t){var e=this;this.form=t.Entity,this.tbData=t.Details,this.TypeData.forEach((function(t){t.Code==e.form.Type&&(e.form.Type_Name=t.Name)})),this.subWeight()},handleSearch:function(){var t=this.form.Codes_Format.trim();t=t.replace(/\s+/g,"\n"),this.form.Codes=t},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.type,n=t.data;switch(this.dialogCancel(),e){case"merge":this.mergeEntity(n);break}},mergeEntity:function(t){t.map((function(t){return t.SteelAmount=t.Wait_Pack_Num,t})),this.Details=t,this.tbData=this.tbData.concat(t),this.pageInfo.TotalCount=this.tbData.length,this.subWeight()},gridPageChange:function(t){var e=t.page;this.pageInfo.Page=Number(e),this.fetchData()},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=Number(e),this.pageInfo.PageSize=Number(e),this.pageInfo.Page=1,this.fetchData()},handlePageChange:function(t){var e=t.currentPage,n=t.pageSize;this.pageInfo.Page=e,this.pageInfo.PageSize=n,this.fetchData()},deleteRows:function(){var t=this;this.multiSelected.forEach((function(e){var n=e.Component_Id;t.tbData=t.tbData.filter((function(t){return n!==t.Component_Id}))})),this.subWeight()},releaseComponent:function(){this.multiSelected.length==this.tbData.length?this.releasePackage():(this.deleteRows(),this.save())},releasePackage:function(){var t=this;(0,u.UnzipPacking2nd)({Ids:this.Id}).then((function(e){e.IsSucceed?(t.confirmed=!0,""===e.Message?t.$message.success("批量释放成功"):t.$message.success(e.Message),(0,d.closeTagView)(t.$store,t.$route)):t.$message.warning(e.Message||"")}))},save:function(){var t=this;this.isClicked=!0,this.btnLoading=!0,this.form.ProjectID=this.form.Sys_Project_Id,(0,u.SavePacking2nd)({Entity:this.form,Details:this.tbData}).then((function(e){e.IsSucceed?(t.confirmed=!0,t.$message.success(e.Message||""),(0,d.closeTagView)(t.$store,t.$route)):(t.isClicked=!1,t.btnLoading=!1,t.$message.warning(e.Message||""))})).catch((function(){t.$message({type:"info",message:res.Message}),t.btnLoading=!1}))},formatSaveData:function(){var t=this,e={entity:{},details:[]};return Object.keys(this.entity).forEach((function(n){"details"!==n&&(e.entity[n]=t.entity[n])})),e.details=this.entity.details,e}})},"30ca":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"content"},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[n("el-table",{staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:t.tbData,stripe:"",size:"medium"}},[n("el-table-column",{attrs:{label:"工程名称",align:"left",prop:"short_name"}}),n("el-table-column",{attrs:{label:"排产单号",align:"left",prop:"schduling_code"}}),n("el-table-column",{attrs:{label:"当前工序",align:"left",prop:"working_process_name"}}),n("el-table-column",{attrs:{label:"加工班组",align:"left",prop:"working_team_name"}}),n("el-table-column",{attrs:{label:"加工中数量",align:"left",prop:"processed_count"}})],1)],1),n("footer",[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("关闭")])],1)])},o=[]},"3f35":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportPackingList=f,e.GeneratePackCode=r,e.GetPacking2ndEntity=c,e.GetPacking2ndPageList=s,e.GetPackingGroupByDirectDetailList=i,e.GetWaitPack2ndPageList=u,e.SavePacking2nd=d,e.UnzipPacking2nd=l;var o=a(n("b775"));a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function r(){return(0,o.default)({url:"/PRO/Packing/GeneratePackCode",method:"post"})}function s(t){return(0,o.default)({url:"/PRO/Packing/GetPacking2ndPageList",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Packing/GetPacking2ndEntity",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Packing/GetWaitPack2ndPageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Packing/UnzipPacking2nd",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/Packing/SavePacking2nd",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Packing/ExportPackingList",method:"post",data:t})}},4403:function(t,e,n){"use strict";n.r(e);var a=n("06cb"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},5641:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.FIX_COLUMN=void 0,e.getFactoryProfessional=s;var o=a(n("c14f")),i=a(n("1da1"));n("d3b7");var r=n("fd31");function s(){return new Promise((function(t,e){(0,r.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then(function(){var n=(0,i.default)((0,o.default)().m((function n(a){return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:a.IsSucceed?t(a.Data):e("error");case 1:return n.a(2)}}),n)})));return function(t){return n.apply(this,arguments)}}()).catch((function(t){e("error")}))}))}e.FIX_COLUMN=["SteelName"]},"5f51":function(t,e,n){"use strict";n.r(e);var a=n("30ca"),o=n("4403");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("cbdb");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"9de1ce84",null);e["default"]=s.exports},7195:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[n("el-button",{staticStyle:{"margin-bottom":"16px"},on:{click:t.tagBack}},[t._v("返回")]),n("div",{staticClass:"sch-detail"},[n("div",{staticClass:"form-search"},[n("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:t.form,"label-width":"105px"}},[[n("el-form-item",{attrs:{label:"构件名称",prop:"Codes_Format"}},[n("el-input",{attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text"},model:{value:t.form.Codes_Format,callback:function(e){t.$set(t.form,"Codes_Format",e)},expression:"form.Codes_Format"}})],1)],[n("el-form-item",{attrs:{label:"构件类型",prop:"ComponentType"}},[n("el-select",{ref:"SteelTypeRef",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},on:{change:t.steelTypeChange},model:{value:t.form.ComponentType,callback:function(e){t.$set(t.form,"ComponentType",e)},expression:"form.ComponentType"}},t._l(t.TypeData,(function(t){return n("el-option",{key:t.Code,attrs:{label:t.Name,value:t.Code}})})),1)],1)],n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),n("el-button",{on:{click:t.resetSearch}},[t._v("重置")])],1)],2)],1),n("div",{staticClass:"twrap"},[n("div",{staticStyle:{height:"100%"}},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:t.tbLoading,align:"left",stripe:"",data:t.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:t.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":t.multiSelectedChange,"checkbox-change":t.multiSelectedChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),t._l(t.columns,(function(e){return["SteelAmount"===e.Code?n("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,sortable:"","edit-render":{},"min-width":"120"},scopedSlots:t._u([{key:"edit",fn:function(e){var a=e.row;return[n("vxe-input",{attrs:{type:"integer",min:1,max:a.Wait_Pack_Num,disabled:t.form.Stock_Status>3},model:{value:a.SteelAmount,callback:function(e){t.$set(a,"SteelAmount",e)},expression:"row.SteelAmount"}})]}},{key:"default",fn:function(e){var n=e.row;return[t._v(" "+t._s(n.SteelAmount)+" ")]}}],null,!0)}):"Is_Component_Name"===e.Code?n("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,sortable:"",width:e.Width,"min-width":e.minWidth},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[t._v(" "+t._s("直发件"==n.Is_Component_Name?"是":"否")+" ")]}}],null,!0)}):n("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:e.Code,title:e.Display_Name,width:e.Width,"min-width":e.minWidth},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.row;return[t._v(" "+t._s(a[e.Code]?a[e.Code]:"-")+" ")]}}],null,!0)})]}))],2)],1)])]),n("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[n("keep-alive",[t.dialogShow?n(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},o=[]},"882a":function(t,e,n){},ba61:function(t,e,n){"use strict";n.r(e);var a=n("7195"),o=n("2736");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("127a");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"5e1cbdf8",null);e["default"]=s.exports},cbdb:function(t,e,n){"use strict";n("882a")},d151:function(t,e,n){}}]);