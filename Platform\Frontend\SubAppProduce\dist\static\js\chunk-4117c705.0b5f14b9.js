(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4117c705"],{"0a45":function(e,t,a){"use strict";a("c277")},"0ce7":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),o=n(a("1da1"));a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var r=n(a("9b15")),l=a("5c96"),s=a("21c4"),c=a("6186"),d=function(){(0,c.SecurityToken)().then((function(e){sessionStorage.setItem("ossToken",JSON.stringify(e.Data))}))};d(),setInterval((function(){d()}),114e4);t.default={name:"OSSUpload",mixins:[l.Upload],props:{ossOnSuccess:{type:Function,default:Function},piecesize:{type:Number,default:2},httpRequest:{type:Function,default:function(e){var t=this,a=JSON.parse(sessionStorage.getItem("ossToken"));this.$nextTick((function(){var n,l=null!==(n=t.data)&&void 0!==n&&n.piecesize?1*t.data.piecesize:2,d=new r.default({region:"oss-"+a.regionId,secure:!0,accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken,bucket:a.bucket,refreshSTSToken:function(){var e=(0,o.default)((0,i.default)().m((function e(){var a;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.securityToken();case 1:return a=e.v,e.a(2,{accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken})}}),e)})));function a(){return e.apply(this,arguments)}return a}(),refreshSTSTokenInterval:9e5}),u=e.file,f=new Date;d.multipartUpload((0,s.getTenantId)()+"/"+f.getFullYear()+"/"+(1*f.getMonth()+1)+"/"+f.getDate()+"/"+f.getMinutes()+"_"+f.getSeconds()+"_"+f.getMilliseconds()+"/"+u.name,u,{progress:function(t,a){this.process=a,e.onProgress({percent:Math.floor(100*t)})},parallel:4,partSize:1048576*l,meta:{}}).then((function(t){if(200===t.res.statusCode){var a,n=t.res.requestUrls[0]&&t.res.requestUrls[0].split("?")[0];!1===(null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.callback)?e.onSuccess({Data:n+"*"+u.size+"*"+u.name.substr(u.name.lastIndexOf("."))+"*"+u.name}):(0,c.GetOssUrl)({url:n}).then((function(t){e.onSuccess({Data:n+"*"+u.size+"*"+u.name.substr(u.name.lastIndexOf("."))+"*"+u.name,encryptionUrl:t.Data})}))}}),(function(t){e.onError(t)}))}))}}},data:function(){return{process:null}},watch:{process:function(e){this.$emit("getprocess",e)}},mounted:function(){},methods:{handleFn:function(e){},securityToken:function(){return new Promise((function(e,t){(0,c.SecurityToken)({}).then((function(t){e(t.Data)})).catch((function(e){t(e)}))}))}}}},"0ff9":function(e,t,a){"use strict";a.r(t);var n=a("324a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"12dd":function(e,t,a){},"1a6d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-row",{staticStyle:{height:"100%",overflow:"auto","box-sizing":"border-box"},attrs:{gutter:12,type:"flex"}},[n("el-card",{staticClass:"cs-fill-card h100",staticStyle:{width:"304px"},attrs:{id:"sliderLeft",shadow:"none"}},["factory"===e.mode?n("el-row",{attrs:{type:"flex"}},[n("div",{staticStyle:{width:"100px",height:"32px","line-height":"32px"}},[e._v(" 当前项目： ")]),n("el-select",{staticStyle:{"margin-bottom":"10px"},attrs:{placeholder:"请选择",filterable:""},on:{change:e.projectChange},model:{value:e.projectId,callback:function(t){e.projectId=t},expression:"projectId"}},e._l(e.projectList,(function(e){return n("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1):e._e(),n("el-row",{staticClass:"dep-top-bar",attrs:{justify:"space-between",type:"flex"}},[n("el-col",[n("span",{staticClass:"dep-tree-title"},[e._v(e._s(e.LChange("文件类别")))])]),n("el-col",{staticStyle:{"text-align":"right"}},[n("el-button",{directives:[{name:"authed",rawName:"v-authed",value:e.AuthButtons,expression:"AuthButtons"}],attrs:{code:"addFolder",icon:"el-icon-plus"},on:{click:function(t){return e.handleOpenAddEdit("add",e.data)}}},[e._v(e._s(e.LChange("新增")))])],1)],1),n("tree-detail",{ref:"tree",attrs:{"expand-on-click-node":!1,"button-type-array":e.treeOperate?["delete","edit"]:[],loading:e.treeLoading,"tree-data":e.treeData,"same-icon":"","show-detail":"","expanded-key":e.expandedKey,icon:"icon-folder"},on:{handleNodeButtonDelete:e.handleNodeButtonDelete,handleNodeButtonEdit:function(t){return e.handleOpenAddEdit("edit",t)},setCurrentNode:e.setCurrentNode,handleNodeClick:e.handleNodeClick}})],1),n("div",{directives:[{name:"resize-width",rawName:"v-resize-width"}],staticClass:"drag-bar"}),e.isGCLXD?[n("iframe",{staticClass:"cs-fill-card h100",staticStyle:{flex:"1","margin-left":"6px"},attrs:{frameborder:"0",src:"http://work.jgsteel.cn:8010/WorkFlow/qsm/BimInstanceList?adtag=app&typeid=1&projectname="+e.projectName}})]:e.isHTPSJL?[n("iframe",{staticClass:"cs-fill-card h100",staticStyle:{flex:"1","margin-left":"6px"},attrs:{frameborder:"0",src:"http://work.jgsteel.cn:8010/WorkFlow/qsm/BimInstanceList?adtag=app&typeid=2&projectname="+e.projectName}})]:[n("el-card",{staticClass:"cs-fill-card cs-fill-card2 h100",staticStyle:{flex:"1","margin-left":"6px"}},[n("div",{staticStyle:{"padding-bottom":"20px",display:"flex","flex-wrap":"wrap"}},[n("el-button",{directives:[{name:"authed",rawName:"v-authed",value:e.AuthButtons,expression:"AuthButtons"}],attrs:{type:"primary",code:"addFile"},on:{click:function(t){return e.Add()}}},[e._v(e._s(e.LChange("新增")))]),n("el-button",{directives:[{name:"authed",rawName:"v-authed",value:e.AuthButtons,expression:"AuthButtons"}],attrs:{type:"danger",code:"deleteFile"},on:{click:e.Delete}},[e._v(e._s(e.LChange("删除")))]),"net"!==e.viewBy||e.selecting?e._e():n("el-button",{on:{click:function(t){e.selecting=!0}}},[e._v(e._s(e.LChange("选择")))]),"net"===e.viewBy&&e.selecting?n("el-button",{on:{click:function(t){e.selecting=!1,e.ids=[]}}},[e._v(e._s(e.LChange("取消选择")))]):e._e(),"net"===e.viewBy&&e.selecting&&!e.isCheckedAll?n("el-button",{on:{click:e.checkAll}},[e._v(e._s(e.LChange("全选")))]):e._e(),"net"===e.viewBy&&e.selecting&&e.isCheckedAll?n("el-button",{on:{click:e.cancelCheckAll}},[e._v(e._s(e.LChange("取消全选")))]):e._e(),"list"===e.viewBy||e.selecting?[n("el-button",{directives:[{name:"authed",rawName:"v-authed",value:e.AuthButtons,expression:"AuthButtons"}],attrs:{code:"moveFile"},on:{click:e.chooseMove}},[e._v(e._s(e.LChange("移动")))]),n("el-button",{directives:[{name:"authed",rawName:"v-authed",value:e.AuthButtons,expression:"AuthButtons"}],attrs:{code:"downloadFile"},on:{click:function(t){return e.Download()}}},[e._v(e._s(e.LChange("下载")))]),n("el-button",{on:{click:e.viewHistory}},[e._v(e._s(e.LChange("历史记录")))]),e.showComponentsButton?n("el-button",{on:{click:e.generateComponents}},[e._v(e._s(e.LChange("生成构件")))]):e._e(),"PLMEngineeringFiles"===e.type?n("el-button",{on:{click:e.scan}},[e._v(e._s(e.LChange("文件扫描")))]):e._e()]:e._e(),e.BIMFiles?["bimviz"===e.modelApp?[n("el-button",{attrs:{type:"success"},on:{click:e.createScene}},[e._v(e._s(e.LChange("生成场景")))])]:e._e(),e.modelStatus?n("el-button",{attrs:{readonly:""}},[e._v(e._s(e.modelStatus))]):e._e()]:e._e(),n("el-button",{attrs:{type:"success"},on:{click:e.shareFiles}},[e._v(e._s(e.LChange("分享")))]),e.PLMDeepenFiles||e.CadFiles?[n("el-button",{on:{click:e.caituMethod}},[e._v(e._s(e.LChange("拆图")))]),n("el-button",{on:{click:e.openCaituDialog}},[e._v(e._s(e.LChange("拆图队列")))])]:e._e(),n("div",{staticStyle:{"margin-left":"auto",display:"flex","align-items":"center"}},[n("el-date-picker",{staticStyle:{"margin-right":"10px",width:"300px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":e.LChange("至"),"start-placeholder":e.LChange("开始日期"),"end-placeholder":e.LChange("结束日期"),"picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}}),n("el-input",{staticStyle:{width:"200px","margin-right":"20px"},attrs:{placeholder:e.LChange("请输入内容")},model:{value:e.fileModel.Title,callback:function(t){e.$set(e.fileModel,"Title",t)},expression:"fileModel.Title"}},[n("el-button",{attrs:{slot:"append",icon:"el-icon-search"},slot:"append"})],1),n("div",[n("el-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v(" "+e._s(e.LChange("搜索"))+" ")]),n("el-button",{on:{click:e.resetSearch}},[e._v(" "+e._s(e.LChange("重置"))+" ")])],1)],1)],2),n("el-row",{staticClass:"plm-bimtable"},["list"===e.viewBy?n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"100%",stripe:""},on:{"selection-change":e.selectionChange},scopedSlots:e._u([{key:"empty",fn:function(){return[n("ElTableEmpty")]},proxy:!0}],null,!1,1319441631)},[n("el-table-column",{attrs:{type:"selection",width:"44"}}),n("el-table-column",{attrs:{prop:"Doc_Title",label:e.LChange("标题"),"show-overflow-tooltip":"","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(a){return e.View(t.row)}}},[e._v(e._s(t.row.Doc_Title||t.row.File_Name))])]}}],null,!1,1580829186)}),n("el-table-column",{attrs:{prop:"Doc_Content",label:e.LChange("简要描述"),"show-overflow-tooltip":"","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.PLMDeepenFiles&&4==t.row.IsCaitu?n("span",{staticStyle:{color:"#f00"}},[e._v(e._s(t.row.Doc_Content))]):n("span",[e._v(e._s(t.row.Doc_Content))])]}}],null,!1,1912390584)}),n("el-table-column",{attrs:{prop:"Doc_File",label:e.LChange("附件信息"),"show-overflow-tooltip":"","min-width":"150"}}),e.PLMDeepenFiles||e.CadFiles?n("el-table-column",{attrs:{label:e.LChange("拆图状态"),width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("caituFilter")(t.row.IsCaitu))+" ")]}}],null,!1,1987726142)}):e._e(),e.BIMFiles?[n("el-table-column",{attrs:{label:e.LChange("默认加载"),align:"center",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{size:"mini "},on:{change:function(a){return e.changeLoad(a,t.row.Id)}},model:{value:t.row.Is_Load,callback:function(a){e.$set(t.row,"Is_Load",a)},expression:"scope.row.Is_Load"}})]}}],null,!1,3382333453)})]:[n("el-table-column",{attrs:{label:e.LChange("变更文件"),align:"center",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{disabled:"",size:"mini "},model:{value:t.row.IsChanged,callback:function(a){e.$set(t.row,"IsChanged",a)},expression:"scope.row.IsChanged"}})]}}],null,!1,219204479)})],e.CadFiles||e.isGlendale?[n("el-table-column",{attrs:{label:e.LChange("编译状态"),align:"center","min-width":"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[100==t.row.CompilationStatus?n("div",{staticClass:"by-dot by-dot-success"},[e._v(" 编译成功 ")]):e._e(),0==t.row.CompilationStatus?n("div",{staticClass:"by-dot by-dot-info"},[e._v(" 等待编译 ")]):e._e(),t.row.CompilationStatus>0&&t.row.CompilationStatus<100?n("div",{staticClass:"by-dot by-dot-primary"},[e._v(" 正在编译 ")]):e._e(),-100==t.row.CompilationStatus?n("div",{staticClass:"by-dot"},[e._v(" 已取消 ")]):e._e(),t.row.CompilationStatus>-100&&t.row.CompilationStatus<0?n("div",{staticClass:"by-dot"},[e._v(" 编译失败 ")]):e._e()]}}],null,!1,3397225076)})]:e._e(),e.isGlendale?[n("el-table-column",{attrs:{label:e.LChange("全部绑定"),align:"center",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.DBSyncStatus?n("el-tag",{attrs:{type:"danger"}},[e._v("否")]):n("el-tag",{attrs:{type:"success"}},[e._v("是")])]}}],null,!1,1954607992)}),n("el-table-column",{attrs:{label:e.LChange("内嵌图纸"),align:"center",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.Is_Drawing?n("el-tag",{attrs:{type:"success"}},[e._v("是")]):n("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}],null,!1,3138451592)})]:e._e(),e.showComponentsButton?n("el-table-column",{attrs:{label:e.LChange("自动生成构件清单"),width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-tag",{attrs:{type:t.row.IsCaitu?"success":"danger"}},[e._v(" "+e._s(e.LChange(t.row.IsCaitu?"已生成":"未生成"))+" ")])]}}],null,!1,911027523)}):e._e(),e.isSHXT?n("el-table-column",{attrs:{label:e.LChange("对应构件"),width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-tag",{attrs:{type:t.row.IsDraft?"success":"danger"}},[e._v(" "+e._s(e.LChange(t.row.IsDraft?"有":"无"))+" ")])]}}],null,!1,1431654642)}):e._e(),n("el-table-column",{attrs:{prop:"Create_UserName",label:e.LChange("编辑人"),align:"center",width:"110"}}),n("el-table-column",{attrs:{prop:"Create_Date",label:e.LChange("编辑时间"),align:"center",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("timeFormat")(t.row.Create_Date,"{y}-{m}-{d} {h}:{i}"))+" ")]}}],null,!1,3484480220)}),n("el-table-column",{attrs:{label:e.LChange("操作"),width:"165",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isEdit?[n("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editRow(t.row)}}},[e._v(e._s(e.LChange("完成")))]),n("el-button",{attrs:{type:"text"},on:{click:function(e){t.row.isEdit=!1}}},[e._v(e._s(e.LChange("取消")))])]:[e.editAuth?n("el-button",{attrs:{type:"text",code:"editFile"},on:{click:function(a){return e.Edit(t.row)}}},[e._v(e._s(e.LChange("编辑")))]):e._e(),e.BIMFiles?e._e():n("el-button",{attrs:{type:"text"},on:{click:function(a){return e.View(t.row)}}},[e._v(e._s(e.LChange("查看")))]),e.isGlendale&&100==t.row.CompilationStatus&&t.row.DBSyncStatus&&e.Is_Integration?n("el-button",{attrs:{type:"text",loading:t.row.loading},on:{click:function(a){return e.syncModelInfo(t.row)}}},[e._v(" "+e._s(e.LChange("绑定"))+" ")]):e._e()],e.isGlendale&&100==t.row.CompilationStatus&&e.Is_Integration?n("el-button",{attrs:{type:"text"},on:{click:function(a){return e.compareModel(t.row.BimId,t.row.Id,t.row)}}},[e._v("模型对比")]):e._e()]}}],null,!1,3141351897)})],2):n("div",{staticClass:"imageList"},[n("el-row",[n("el-checkbox-group",{model:{value:e.ids,callback:function(t){e.ids=t},expression:"ids"}},e._l(e.tableData,(function(t,i){return n("el-col",{key:t.Id,attrs:{span:6}},[n("div",{staticClass:"item",on:{click:function(a){return e.previewVideo(t.Id)}}},[e.selecting?n("el-checkbox",{staticStyle:{"box-sizing":"border-box",width:"100%"},attrs:{label:t.Id}},[e.videoType.includes(t.File_Type)?n("div",{staticClass:"picture"},[n("video",{ref:"videoView",refInFor:!0,attrs:{src:t.File_Url,height:"130px",width:"100%"}})]):n("el-image",{staticClass:"picture",attrs:{src:t.File_Url}},[n("div",{staticClass:"error-slot",attrs:{slot:"error"},slot:"error"},[n("svg-icon",{attrs:{"icon-class":"default-picture","class-name":"picture"}})],1)])],1):[e.videoType.includes(t.File_Type)?n("div",{staticClass:"picture"},[n("div",{staticClass:"videoBtn"},[n("img",{attrs:{src:a("7ec3"),alt:""}})]),n("video",{ref:"videoView",refInFor:!0,attrs:{id:t.Id,src:t.File_Url,height:"130px",width:"100%"},on:{fullscreenchange:e.fullscreenchange}})]):n("el-image",{staticClass:"picture",attrs:{src:t.File_Url,"preview-src-list":e.srcList},on:{click:function(a){return e.previewImage(t,i)}}},[n("div",{staticClass:"error-slot",attrs:{slot:"error"},slot:"error"},[n("svg-icon",{attrs:{"icon-class":"default-picture","class-name":"picture"}})],1)])],n("footer",{staticClass:"footer"},[n("div",{staticClass:"title-date"},[n("div",{staticClass:"title"},[e._v(" "+e._s(t.Doc_Title||t.File_Name)+" ")]),n("div",{staticClass:"date"},[e._v(" "+e._s(e._f("timeFormat")(t.Create_Date,"{y}-{m}-{d} {h}:{i}"))+" ")])]),n("div",{staticClass:"content"},[e._v(" "+e._s(t.Doc_Content)+" ")])])],2)])})),1)],1)],1),n("div",{staticClass:"page"},[n("el-pagination",{attrs:{"current-page":e.pageInfo.Page,"page-sizes":[15,50,100,1e3],"page-size":e.pageInfo.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pageInfo.TotalCount},on:{"update:currentPage":function(t){return e.$set(e.pageInfo,"Page",t)},"update:current-page":function(t){return e.$set(e.pageInfo,"Page",t)},"size-change":e.handlePageSizeChange,"current-change":e.getData}})],1)],1)],1)]],2),n("bimdialog",{ref:"dialog",attrs:{"bimviz-id":e.bimvizId},on:{getData:e.getData}}),n("add-edit-dep",{ref:"AddEditDep",attrs:{"tree-data":e.treeData},on:{changeData:e.fetchTreeData}}),n("el-dialog",{attrs:{"dialog-title":e.LChange("移动到"),"dialog-width":"500px",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:e.Move,cancelbtn:function(t){e.dialogVisible=!1},handleClose:function(t){e.dialogVisible=!1}}},[n("tree-detail",{ref:"tree",attrs:{"expand-on-click-node":!1,loading:e.treeLoading,"tree-data":e.treeData,"same-icon":"","expanded-key":e.expandedKey,icon:"icon-folder"},on:{handleNodeClick:e.chooseMoveFolder}})],1),n("el-dialog",{attrs:{"dialog-title":e.LChange("拆图队列"),"dialog-width":"500px",hidebtn:"",visible:e.CaituVisible},on:{"update:visible":function(t){e.CaituVisible=t},handleClose:function(t){e.CaituVisible=!1}}},[n("el-table",{attrs:{data:e.caituTable}},[n("el-table-column",{attrs:{label:"文件名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.Doc_Title||t.row.File_Name)+" ")]}}])}),n("el-table-column",{attrs:{label:"操作人",prop:"Create_UserName"}}),n("el-table-column",{attrs:{label:"操作时间",prop:"Create_Date"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("timeFormat")(t.row.Create_Date,"{y}-{m}-{d} {h}:{i}"))+" ")]}}])})],1)],1),n("el-dialog",{attrs:{"dialog-title":e.LChange("文件分享"),"dialog-width":"600px",visible:e.shareVisible,hidebtn:""},on:{"update:visible":function(t){e.shareVisible=t},handleClose:function(t){e.shareVisible=!1}}},[n("el-form",[n("el-form-item",{attrs:{label:e.LChange("分享链接")}},[e._v(" "+e._s(e.shareUrl)+" "),n("el-button",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:e.shareUrl,expression:"shareUrl",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:e.clipboardSuccess,expression:"clipboardSuccess",arg:"success"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary"}},[e._v(e._s(e.LChange("复制")))])],1),n("el-form-item",{attrs:{label:e.LChange("有效天数")}},[n("el-input-number",{attrs:{min:1,max:1e3,label:"描述文字"},on:{change:e.buildShareUrl},model:{value:e.shareTime,callback:function(t){e.shareTime=t},expression:"shareTime"}})],1),n("el-form-item",{attrs:{label:"二维码"}},[n("qrcode-vue",{attrs:{size:124,value:e.shareUrl,"class-name":"qrcode",level:"H"}})],1)],1)],1),n("history",{ref:"history",on:{preview:e.View,download:e.Download}}),n("scan",{ref:"scan",attrs:{typeid:e.fileModel.typeId},on:{resetData:e.getData}}),n("model-compare-dialog",{ref:"modelCompare"}),e.currentLoading?n("i",{staticClass:"el-icon-close close-btn",on:{click:function(t){e.currentLoading.close(),e.currentLoading=null}}}):e._e()],1)},i=[]},"1c1e":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("9518"));t.default={name:"PLMBimFiles",components:{commonFiles:i.default}}},"1d3d":function(e,t,a){"use strict";a.r(t);var n=a("3aea"),i=a("af5d");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("4291");var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"36340743",null);t["default"]=l.exports},"248b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetGlModelSqlitData=h,t.SetCompilationStatus=p,t.fileAuthSave=c,t.fileSubscribeSave=f,t.freezeHolder=s,t.getFileAuthedList=d,t.getFileSubscribeList=m,t.getFileUserAuth=u,t.getHolderVersionDetailList=r,t.getHolderVersionList=o,t.rollbackFolder=l;var i=n(a("b775"));function o(e){return(0,i.default)({url:"sys/Sys_File_Type_Version/GetEntities",method:"post",data:e})}function r(e){return(0,i.default)({url:"sys/Sys_File_Type_Version/GetEntity",method:"post",data:e})}function l(e){return(0,i.default)({url:"sys/Sys_File_Type_Version/GetChange",method:"post",data:e})}function s(e){return(0,i.default)({url:"Sys/Sys_FileType/Disabled",method:"post",data:e})}function c(e){return(0,i.default)({url:"sys/Sys_File_Type_Power/Add",method:"post",data:e})}function d(e){return(0,i.default)({url:"sys/Sys_File_Type_Power/GetEntities",method:"post",data:e})}function u(e){return(0,i.default)({url:"sys/Sys_File_Type_Power/GetEntity",method:"post",data:e})}function f(e){return(0,i.default)({url:"sys/Sys_File_Type_SMS/Add",method:"post",data:e})}function m(e){return(0,i.default)({url:"sys/Sys_File_Type_SMS/GetEntities",method:"post",data:e})}function p(e){return(0,i.default)({url:"SYS/Sys_File/SetCompilationStatus",method:"post",data:e})}function h(e){return(0,i.default)({url:"PLM/GlModel/GetGlModelSqlitData",method:"post",data:e})}},"2e00":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetEpcEntity=o,t.GetUserListByObjId=l,t.SaveEpcProject=r;var i=n(a("b775"));function o(e){return(0,i.default)({url:"/EPC/Project/GetEpcEntity",method:"post",data:e})}function r(e){return(0,i.default)({url:"/EPC/Project/SaveEpcProject",method:"post",data:e})}function l(e){return(0,i.default)({url:"/EPC/Project/GetUserListByObjId",method:"post",data:e})}},3085:function(e,t,a){},"324a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("5530")),o=n(a("c14f")),r=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("d3b7"),a("ac1f"),a("25f0"),a("2532"),a("3ca3"),a("5319"),a("841c"),a("159b"),a("ddb0");var l=n(a("1463")),s=n(a("335b9")),c=n(a("6fc7")),d=n(a("a7c7")),u=n(a("e770")),f=n(a("1d3d")),m=n(a("bc3a")),p=a("21a6"),h=n(a("c4e3")),g=a("0e9a"),b=a("d363"),_=a("361f"),v=a("0d9a"),y=a("248b"),I=a("2e00"),C=a("21c4"),S=n(a("8325")),w=n(a("d7b0")),D=n(a("2b0e")),P=a("dc02"),F=(a("8975"),a("c24f")),k=n(a("6347")),M=a("6186"),L=(a("4744"),a("f4f2")),x=a("b28f"),T=n(a("8627")),B=a("1b69"),j=n(a("21f5"));D.default.use(P.VueJsonp);var G,E=null;t.default={name:"ProjectFiles",directives:{clipboard:S.default},components:{ElTableEmpty:j.default,TreeDetail:l.default,AddEditDep:s.default,bimdialog:c.default,"el-dialog":d.default,history:f.default,QrcodeVue:w.default,scan:u.default,ModelCompareDialog:T.default},filters:{caituFilter:function(e){return 1===e||3===e?"已拆图":5===e?"拆图队列中":4===e?"拆图失败":"无需拆图"}},mixins:[k.default],props:{viewBy:{type:String,default:"list"}},data:function(){return{isPlay:!1,tenantCode:"",checkedItems:[],selecting:!1,pageInfo:{Page:1,PageSize:15,TotalCount:0},fileModel:{typeId:"",doc_Catelog:"",Title:"",startTime:"",endTime:""},type:"",data:{Id:"",Catalog_Code:"",name:""},activeName:"first",filterText:"",treeData:[],tableData:[],treeLoading:!1,DepartmentName:"",DepartmentId:"",expandedKey:"",elsebuttons:[],attachments:[],imageType:[".png",".jpg",".jpeg",".gif"],dialogVisible:!1,moveToFolderId:"",ids:[],videoType:[".mp4",".mv",".avi",".3gp"],bimvizId:null,modelStatus:"",selectNames:[],shareUrl:"",shareVisible:!1,CaituVisible:!1,caituTable:[],shareTime:30,selectList:[],tableLoading:!1,dateRange:[],projectName:"",currentLoading:null,pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},currImageIndex:0,notify:"",dwgUrl:"http://dwgv1.bimtk.com:5432?CadUrl=",modelApp:"bimviz",treeOperate:!1,mode:"",projectList:[],projectId:"",Is_Integration:!0}},computed:{isCheckedAll:function(){return this.tableData.length===this.ids.length},srcList:function(){return this.tableData.map((function(e){return e.File_Url}))},PLMBIMFiles:function(){return"PLMBimFiles"===this.$route.name||"PLMDesignModel"===this.$route.name||"PROBimFiles"===this.$route.name},PLMDeepenFiles:function(){return"PLMDeepenFiles"===this.$route.name&&"深化清单"!==this.data.name},EPCBIMFiles:function(){return"EPCBimFiles"===this.$route.name},BIMFiles:function(){return"EPCBimFiles"===this.$route.name||"PLMBimFiles"===this.$route.name||"PLMDesignModel"===this.$route.name||"PROBimFiles"===this.$route.name},PLMStandard:function(){return"PLMStandard"===this.$route.name||"PLMTechnologyFiles"===this.$route.name},CadFiles:function(){return"CadManager"===this.$route.name||"PLMDeepenFiles"===this.$route.name},showComponentsButton:function(){return"深化清单"===this.data.name},isSHXT:function(){return"深化详图"===this.data.name},isGCLXD:function(){return"工程联系单"===this.data.name},isHTPSJL:function(){return"合同评审记录"===this.data.name},isBimviz:function(){return this.BIMFiles&&"bimviz"===this.modelApp},isGlendale:function(){return this.BIMFiles&&"glendale"===this.modelApp},treeButtonAuth:function(){var e=[];return this.AuthButtons.buttons.find((function(e){return"deleteFolder"===e.Code}))&&e.push("delete"),this.AuthButtons.buttons.find((function(e){return"editFolder"===e.Code}))&&e.push("edit"),e},editAuth:function(){return!!this.AuthButtons.buttons.find((function(e){return"editFile"===e.Code}))}},watch:{ids:{handler:function(){var e=this;this.selectList=this.tableData.filter((function(t){return e.ids.includes(t.Id)}))},deep:!0},dateRange:function(e){try{this.fileModel.startTime=e[0],this.fileModel.endTime=e[1]}catch(t){this.fileModel.startTime=null,this.fileModel.endTime=null}},"fileModel.typeId":function(){var e=this;this.$nextTick((function(){e.$refs.table.doLayout()}))}},created:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:e.Is_Integration=t.v,e.tenantCode=localStorage.getItem("tenant"),e.type=e.$route.name,e.fileModel.doc_Catelog="PLMBimFiles",e.data.Catalog_Code=e.$route.name,e.projectName=localStorage.getItem("ProjectName"),e.getDwgUrl();case 2:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(a=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),"2"!==a){t.n=2;break}return e.mode="factory",t.n=1,e.getProjectList();case 1:e.projectList.length&&e.fetchTreeData(!0),t.n=3;break;case 2:e.fetchTreeData(!0);case 3:e.getData(),e.BIMFiles&&e.getModelStatus();case 4:return t.a(2)}}),t)})))()},destroyed:function(){E=null,clearInterval(G)},methods:{resetSearch:function(){this.dateRange="",this.search()},projectChange:function(e){sessionStorage.setItem("bimFileProjectId",this.projectId),this.fetchTreeData(),this.getData()},getProjectList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,e.tableLoading=!0,t.n=1,(0,B.GetProjectPageList)({PageSize:-1});case 1:a=t.v,e.projectList=a.Data.Data,a.Data.Data.length?e.projectId=sessionStorage.getItem("bimFileProjectId")?sessionStorage.getItem("bimFileProjectId"):a.Data.Data[0].Sys_Project_Id:(e.$message.error("暂无项目"),e.treeLoading=!1,e.tableLoading=!1);case 2:return t.a(2)}}),t)})))()},previewVideo:function(e){var t=document.getElementById(e);t.requestFullscreen&&(t.requestFullscreen(),t.play(),document.addEventListener("fullscreenchange",(function(e){document.fullscreenElement||(t.pause(),e.target.currentTime=0)})))},compareModel:function(e,t,a){this.$refs.modelCompare.handleOpen(e,this.treeData,t,a,this.projectId)},fullscreenchange:function(e){},caituMethod:function(){var e=this;if(1===this.selectList.length){if(1===this.selectList[0].IsCaitu||3===this.selectList[0].IsCaitu)return void this.$message.warning("该图纸已拆图");if(5===this.selectList[0].IsCaitu)return void this.$message.warning("该图纸已在拆图队列中，请勿重复添加")}else{if(!(this.selectList.length>1))return void this.$message.warning("请选择要拆图的文件");if(this.selectList.some((function(e){return 1===e.IsCaitu||3===e.IsCaitu})))return void this.$message.warning("您选择的文件中有已拆图文件，请重新选择");if(this.selectList.some((function(e){return 5===e.IsCaitu})))return void this.$message.warning("您选择的文件中有已在拆图队列中的文件，请重新选择")}(0,v.SetUnCaiTuFile)({fileIds:this.ids,caitu:5}).then((function(t){t.IsSucceed?e.getData():e.$message.error(t.Message)}))},openCaituDialog:function(){var e=this;this.CaituVisible=!0,(0,v.GetFilesByType)({fileModel:(0,i.default)((0,i.default)({},this.fileModel),{},{typeId:"",IsCaitu:5}),pageInfo:{Page:1,PageSize:1e3}}).then((function(t){e.caituTable=t.Data.Data}))},syncModelInfo:function(e){var t=this;this.$set(e,"loading",!0),(0,y.GetGlModelSqlitData)({bimId:e.BimId,sysProjectId:this.projectId}).then((function(a){e.loading=!1,a.IsSucceed?(t.$message.success("绑定成功"),t.getData()):t.$message.error(a.Message)}))},getDwgUrl:function(){var e=this;(0,M.GetPreferencessettingsByKey)({key:"DwgUrl"}).then((function(t){t.Data&&(e.dwgUrl=t.Data+"?CadUrl=")}))},authButtonUpdated:function(){this.AuthButtons.buttons&&this.AuthButtons.buttons.length||(this.AuthButtons.buttons=[{Code:"addFolder"},{Code:"editFolder"},{Code:"deleteFolder"},{Code:"addFile"},{Code:"editFile"},{Code:"deleteFile"},{Code:"moveFile"},{Code:"downloadFile"}])},getModelStatus:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getProjectData();case 1:e.modelApp="glendale";case 2:return t.a(2)}}),t)})))()},getProjectData:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(E){t.n=5;break}if(!e.PLMBIMFiles){t.n=2;break}return t.n=1,(0,_.GetPlmProjectGetEntity)({id:e.projectId});case 1:a=t.v;case 2:if(!e.EPCBIMFiles){t.n=4;break}return t.n=3,(0,I.GetEpcEntity)({id:localStorage.getItem("CurReferenceId")});case 3:a=t.v;case 4:E=a.Data;case 5:return t.a(2)}}),t)})))()},toBimViz:function(){var e=this;this.getBimvizStatus(),G=setInterval((function(){e.getBimvizStatus()}),1e4)},getBimvizStatus:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getBimVizId();case 1:a=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,restport:BIM_API_CONFIG.MODEL_PORT,key:BIM_API_CONFIG.KEY}),n=a.getProjectBuildManager(),n.getRebuildSceneState(BIM_API_CONFIG.USER_NAME,e.bimvizId,(function(t){0===t.State?e.modelStatus="正在编译":-1===t.State?e.modelStatus="无模型":2===t.State?e.modelStatus="编译失败":1===t.State&&(e.modelStatus="编译成功")}));case 2:return t.a(2)}}),t)})))()},generateComponents:function(){var e=this,t=this.selectList,a=t.map((function(e){return~e.Doc_File.indexOf(".")?e.Doc_File:e.Doc_File+e.File_Type}));(0,v.ImportDeependToSteel)({fileList:t.map((function(e){return e.File_Url})).join("|||"),nameList:a.join("|||"),idList:this.ids.join("|||")}).then((function(t){t.IsSucceed?(e.getData(),e.$message.success(t.Message)):e.$message.error(t.Message)}))},documents:function(){var e=this;this.data.Id?(0,v.GetFileSync)({Id:this.data.Id}).then((function(t){t.IsSucceed?e.$message.success("同步成功"):(e.$message.error(t.Message),e.getData())})):this.$message.warning("请选择要同步的文件夹")},scan:function(){this.$refs.scan.handleOpen()},shareFiles:function(){var e=this.BIMFiles?this.selectNames:this.ids;e.length?!this.BIMFiles&&e.length>1?this.$message.warning("只能选择一个文件"):this.buildShareUrl():this.$message.warning("请选择文件")},buildShareUrl:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n,i,r,l,s,c,d,u,f;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(a=e.ids,n="http://cloud.bimtk.com/",i="",i=e.selectList[0].File_Type,r=e.selectList[0].Pdf_Url,l=e.selectList[0].File_Url,".xls"!==i&&".xlsx"!==i&&".doc"!==i&&".docx"!==i){t.n=3;break}if(i=".pdf",r){t.n=2;break}return s=e.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),e.currentLoading=s,t.n=1,(0,g.fileToPdf2)(l);case 1:r=t.v,s.close(),e.currentLoading=null;case 2:a=[r],t.n=7;break;case 3:if(".pdf"!==i){t.n=4;break}i=".pdf",a=[l],t.n=7;break;case 4:if(".dwg"!==i){t.n=5;break}i=".dwg",a=[l],t.n=7;break;case 5:if(".png"!==i&&".jpg"!==i&&".jpeg"!==i&&".gif"!==i){t.n=6;break}i=".image",a=[l],t.n=7;break;case 6:return e.$message("该格式的文件暂不支持分享"),t.a(2);case 7:return t.n=8,(0,M.GetOssUrl)({url:a[0].split("?")[0],day:e.shareTime});case 8:c=t.v,a=c.Data,d=localStorage.getItem("CurReferenceId"),u="".concat(n,"h5/plm-projectFiles-share-index.html?tenantID=").concat((0,C.getTenantId)(),"&projectId=").concat(d,"&fileType=").concat(i,"&fileIds=").concat(escape(encodeURI(a))),f={strUrl:u},(0,v.GetShortUrl)(f).then((function(t){e.shareUrl="".concat(n,"/h5/a-b.html?t=").concat((0,C.getTenantId)(),"&u=").concat(t.Data,"&h=").concat((0,L.baseUrl)()),e.shareVisible=!0}));case 9:return t.a(2)}}),t)})))()},clipboardSuccess:function(){this.$message.success("复制成功")},changeLoad:function(e,t){var a=this;return(0,r.default)((0,o.default)().m((function n(){var i,r;return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return i=a.ids.length?a.ids:[t],n.n=1,(0,v.ChangeLoad)({ids:i,load:e});case 1:return a.getData(),n.n=2,(0,v.GetLoadingFiles)();case 2:r=n.v,a.isBimviz&&a.updateLoadingFiles(r.Data);case 3:return n.a(2)}}),n)})))()},updateLoadingFiles:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n,i,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getBimVizId();case 1:n=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,key:BIM_API_CONFIG.KEY}),i=n.getModelProjectManager(),r={SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,LoadFiles:e},i.updateSceneSettings(BIM_API_CONFIG.USER_NAME,t.bimvizId,r,(function(e){e.IsSuccess?this.$message.success(e.Message):this.$message.error(e.Message)}));case 2:return a.a(2)}}),a)})))()},handlePageSizeChange:function(e){this.pageInfo.PageSize=e,this.getData()},search:function(){this.pageInfo.Page=1,this.getData()},getData:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,F.getConfigure)({code:"glendale_token"});case 1:return x.Glendale.token=t.v.Data,t.n=2,(0,F.getConfigure)({code:"glendale_baseurl"});case 2:x.Glendale.baseUrl=t.v.Data,e.PLMStandard&&(e.fileModel.IsProject=!0),e.fileModel.Projectid=e.projectId,e.tableLoading=!0,(0,v.GetFilesByType)({fileModel:e.fileModel,pageInfo:e.pageInfo}).then((function(t){var a=t.Data,n=a.Data,i=a.Page,o=a.PageSize,r=a.TotalCount;e.tableData=n||[],e.pageInfo={Page:i,PageSize:o,TotalCount:r},e.getCompilationStatus(),e.tableLoading=!1,e.getAuth()}));case 3:return t.a(2)}}),t)})))()},getCompilationStatus:function(){var e=this,t=!1;this.tableData=this.tableData.map((function(a){return a.checked=!1,a.BimId&&(100!=a.CompilationStatus||a.CompilationStatus<0)&&(t=!0,m.default.post(x.Glendale.baseUrl+"/api/app/model/query-model-info?LightweightName="+a.BimId,{},{headers:{Token:x.Glendale.token}}).then((function(t){var n=t.data.datas[0].status;e.$set(a,"CompilationStatus",n),(0,y.SetCompilationStatus)({ids:[a.Id],status:n})}))),a})),this.$refs.table.doLayout(),t&&setTimeout(this.getCompilationStatus,1e4)},handleImport:function(){},handleNodeClick:function(e){"factory"===this.mode||"Systerm"===e.Data.Create_UserName||"System"===e.Data.Create_UserName?this.treeOperate=e.Data.Is_Disabled:this.treeOperate=!0,this.fileModel.typeId=e.Id,this.data.Id=e.Id,this.data.name=e.Label,this.data.Catalog_Code=e.Code,this.getData()},fetchUserList:function(e){var t=this;(0,M.GetFileType)(e).then((function(e){t.treeData=e.Data}))},handleOpenAddEdit:function(e,t){this.$refs.AddEditDep.handleOpen(e,t,this.treeData,"factory"===this.mode,"factory"===this.mode?this.projectId:null)},handleTabClick:function(e,t){},View:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n,i,r,l,s,c;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:if(!t.CadFiles||".glzip"!==e.File_Type){a.n=4;break}n="",a.n=1;break;case 1:return a.n=2,(0,F.getConfigure)({code:"glendale_url"});case 2:n=a.v.Data;case 3:return n+="/#/cadView?modelId=".concat(e.BimId,"&baseUrl=").concat((0,L.baseUrl)(),"&projectId=").concat(localStorage.getItem("CurReferenceId"),"&token=").concat(t.$store.state.user.token,"&auth_id=").concat(t.$store.state.user.Last_Working_Object_Id),window.open(n),a.a(2);case 4:return a.n=5,(0,v.AttachmentGetEntities)({recordId:e.Id});case 5:if(i=a.v,t.attachments=i.Data,!(t.attachments.length>0)){a.n=10;break}if(r=t.attachments[0].File_Url,l=t.attachments[0].Pdf_Url,s=t.attachments[0].File_Type,".xls"!==s&&".xlsx"!==s&&".doc"!==s&&".docx"!==s){a.n=9;break}if(!l){a.n=6;break}window.open(l+"#toolbar=0","_blank"),a.n=8;break;case 6:return c=t.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),t.currentLoading=c,a.n=7,(0,g.fileToPdf)(r,!1);case 7:c.close(),t.currentLoading=null;case 8:a.n=10;break;case 9:".dwg"===s?window.open(t.dwgUrl+(0,g.parseOssUrl)(r),"_blank"):(".pdf"===s&&(r+="#toolbar=0"),window.open(r,"_blank"));case 10:return a.a(2)}}),a)})))()},Edit:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:if(n="",!t.isBimviz){a.n=2;break}return a.n=1,t.getBimVizId();case 1:n=a.v;case 2:t.$refs.dialog.handleOpen("edit",e,n,t.modelApp,E,t.projectId);case 3:return a.a(2)}}),a)})))()},selectionChange:function(e){this.selectNames=e.map((function(e){return e.Doc_File})),this.ids=e.map((function(e){return e.Id})),this.selectList=e},checkAll:function(){this.ids=this.tableData.map((function(e){return e.Id}))},cancelCheckAll:function(){this.ids=[]},Delete:function(){var e=this;0!==this.ids.length?this.$confirm(" 确认删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((0,r.default)((0,o.default)().m((function t(){var a,n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if((0,v.FileDelete)({ids:e.ids}).then((function(t){!0===t.IsSucceed&&(e.$message({message:"删除成功",type:"success"}),e.getData(e.fileModel.typeId))})),!e.isBimviz){t.n=2;break}return t.n=1,e.getBimVizId();case 1:a=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,restport:BIM_API_CONFIG.MODEL_PORT,key:BIM_API_CONFIG.KEY}),n=a.getModelProjectManager(),e.selectNames.forEach((function(t){n.removeProjectFile(BIM_API_CONFIG.USER_NAME,e.bimvizId,t,(function(e){}))}));case 2:return t.a(2)}}),t)})))).catch((function(){e.$message({type:"info",message:"已取消删除"})})):this.$message({message:"请先选择记录",type:"warning"})},chooseMove:function(){0!==this.ids.length?(this.moveToFolderId="",this.dialogVisible=!0):this.$message({message:"请先选择记录",type:"warning"})},chooseMoveFolder:function(e){this.moveToFolderId=e.Id},Move:function(){var e=this;this.moveToFolderId?(0,v.FileMove)({ids:this.ids,doc_Type:this.moveToFolderId}).then((function(t){t.IsSucceed&&(e.$message.success("移动成功"),e.dialogVisible=!1,e.getData())})):this.$message.warning("请选择要移动到的文件夹")},Download:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.ids;t&&t.length?(this.notify=this.$notify.info({title:"提示",message:"文件下载中，请稍后...",duration:0}),(0,v.GetEntitiesByRecordId)({ids:t}).then((function(a){if(e.attachments=a.Data,e.attachments.length>0){var n=e.attachments,i=e.attachments[0].File_Url;if(1===t.length){var o=e.attachments.map((function(e){return e.File_Name})).join(",");(0,g.downloadfile)(i,o,e.attachments[0].File_Type,e.notify)}else{var r="".concat(localStorage.getItem("ProjectName"),"-").concat(e.data.name,"-").concat((new Date).toLocaleDateString());e.handleBatchDownload(n,r)}}}))):this.$message.warning("请选择要下载的文件")},handleBatchDownload:function(e,t){var a=this;return(0,r.default)((0,o.default)().m((function n(){var i,r,l,s;return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return i=e,r=new h.default,l={},s=[],n.n=1,i.forEach((function(e){var t=a.getFile(e.File_Url).then((function(t){var a=e.File_Name.replace(/[\\/:*?"<>|]/g,"_");r.file(a,t,{binary:!0}),l[a]=t}));s.push(t)}));case 1:Promise.all(s).then((function(){r.generateAsync({type:"blob"}).then((function(e){(0,p.saveAs)(e,t+".zip"),a.loading=!1,a.notify&&a.notify.close()})).catch((function(e){a.loading=!1,a.$message.error("网络出现了一点小问题，请稍后重试")}))}));case 2:return n.a(2)}}),n)})))()},previewImage:function(e,t){var a=this;this.$nextTick((function(){var e=a,n=document.getElementsByClassName("el-image-viewer__canvas")[0],i=document.createElement("div");function o(){i.innerHTML='\n          <div style="color: #ffffff;position: fixed;top: 20px;left: 20px;height: 50px;width: 310px;font-size: 23px;">'.concat(e.tableData[e.currImageIndex].Doc_Content,"</div>\n        "),n.appendChild(i)}a.currImageIndex=t,o(),document.getElementsByClassName("el-image-viewer__next")[0].addEventListener("click",(function(){a.currImageIndex++,o()})),document.getElementsByClassName("el-image-viewer__prev")[0].addEventListener("click",(function(){a.currImageIndex--,o()})),document.onkeydown=function(e){switch(e.keyCode){case 37:a.currImageIndex--,o();break;case 39:a.currImageIndex++,o();break;default:}}}))},getFile:function(e){return new Promise((function(t,a){(0,m.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))},viewHistory:function(){this.ids.length?this.$refs.history.handleOpen(this.ids[0]):this.$message.warning("请先选择记录")},handleNodeButtonDelete:function(e){var t=this,a="确认删除本类别？",n={confirmButtonText:"删除",type:"warning"};this.$confirm(a,"提示",n).then((function(){(0,v.FileTypeDelete)({id:e.Id}).then(function(){var e=(0,r.default)((0,o.default)().m((function e(a){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:if(!0!==a.IsSucceed){e.n=2;break}return t.$message({type:"success",message:"删除成功!"}),e.n=1,t.fetchTreeData(!0);case 1:t.getData(),e.n=3;break;case 2:t.$message.error(a.Data);case 3:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},fetchTreeData:function(e){var t=this;this.treeLoading=!0;var a={catalogCode:"PLMBimFiles"};"factory"===this.mode?a.proSysProjectId=this.projectId:a.bimProjectId=localStorage.getItem("CurReferenceId"),(0,M.GetFileType)(a).then((function(n){t.treeData=n.Data,t.treeLoading=!1,e&&t.fetchUserList(a)}))},setCurrentNode:function(e){},Add:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(!e.data.Id){t.n=3;break}if(a="",!e.isBimviz){t.n=2;break}return t.n=1,e.getBimVizId();case 1:a=t.v;case 2:e.$refs.dialog.handleOpen("add",e.data,a,e.modelApp,E,e.projectId),t.n=4;break;case 3:return e.$message({message:"请先选择类别",type:"warning"}),t.a(2);case 4:return t.a(2)}}),t)})))()},buildBimVizSpace:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n,i,l,s,c,d,u;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.$refs.dialog.loading=!0,t.n=1,e.getTenantInfo();case 1:a=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,restport:BIM_API_CONFIG.MODEL_PORT,key:BIM_API_CONFIG.KEY}),n=E.Name,i=E.Code,l=new BIMVIZ.ModelProject(i,n,0,""),s=BIMVIZ.SceneDomain.Architectural,BIM_API_CONFIG.SceneDomain&&"Rabar"==BIM_API_CONFIG.SceneDomain&&(s=BIMVIZ.SceneDomain.Rabar),c=new BIMVIZ.ProjectSettings({BackgroundStyle:BIMVIZ.SceneBackgroundStyle.White,SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,ThemeStyle:BIMVIZ.SceneThemeStyle.ModelSelt,GroundStyle:BIMVIZ.SceneGroundStyle.None,SceneDomain:s,MaxMemory:8e3,SceneLoadMode:2}),d=new BIMVIZ.ProjectInfo(l,c),u=a.getModelProjectManager(),u.addProject(BIM_API_CONFIG.USER_NAME,d,function(){var t=(0,r.default)((0,o.default)().m((function t(a){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(!a.IsSuccess){t.n=3;break}if(e.bimvizId=a.ProjectId,E.Bim_Viz_Id=e.bimvizId,E.Custom=[],!e.PLMBIMFiles){t.n=1;break}return t.n=1,(0,_.GetPlmProjectEdit)({plm_Projects:E,source:3});case 1:if(!e.EPCBIMFiles){t.n=2;break}return t.n=2,(0,b.SaveEpcBimVizId)({bimVizId:e.bimvizId,projectId:E.Project_Id});case 2:return e.$refs.dialog.loading=!1,t.a(2,e.bimvizId);case 3:return e.$message.error("场景生成失败"),t.a(2,e.bimvizId);case 4:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),(function(){window.reload()}));case 2:return t.a(2)}}),t)})))()},createScene:function(){var e=this;this.$confirm("您确认要重新生成模型场景吗","提示",{type:"warning"}).then((0,r.default)((0,o.default)().m((function t(){var a,n,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getBimVizId();case 1:return t.n=2,e.getTenantInfo();case 2:return t.n=3,(0,m.default)({method:"get",url:"http://116.62.156.119:7004/api/user/token?devkey=".concat(BIM_API_CONFIG.KEY),timeout:5e3});case 3:return a=t.v,n=a.data.token,t.n=4,(0,m.default)({method:"get",timeout:5e3,headers:{Authorization:"Bearer "+n},url:"http://116.62.156.119:7004/api/projectbuild/RebuildScene?username=".concat(BIM_API_CONFIG.USER_NAME,"&projid=").concat(e.bimvizId,"&cmd=RebuildAll")});case 4:i=t.v,i.data.IsSuccess?e.$message.success(i.data.Message):e.$message.error(i.data.Message);case 5:return t.a(2)}}),t)}))))},getBimVizId:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(e.bimvizId){t.n=9;break}if(!e.PLMBIMFiles){t.n=2;break}return t.n=1,(0,_.GetPlmProjectGetEntity)({id:localStorage.getItem("CurReferenceId")});case 1:a=t.v;case 2:if(!e.EPCBIMFiles){t.n=4;break}return t.n=3,(0,I.GetEpcEntity)({id:localStorage.getItem("CurReferenceId")});case 3:a=t.v;case 4:if(!a.IsSucceed||!a.Data){t.n=7;break}if(E=a.Data,!a.Data.Bim_Viz_Id){t.n=5;break}e.bimvizId=a.Data.Bim_Viz_Id,t.n=6;break;case 5:return t.n=6,e.buildBimVizSpace();case 6:t.n=8;break;case 7:e.$message.error("获取项目信息失败");case 8:return t.a(2,e.bimvizId);case 9:return t.a(2,e.bimvizId);case 10:return t.a(2)}}),t)})))()},getTenantInfo:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,b.GetTenantModelExtend)();case 1:if(a=t.v,a.Data){t.n=2;break}return e.$confirm("请先在系统偏好设置中，设置BIM模型的配置信息","提示"),t.a(2);case 2:n=a.Data.split(",").map((function(e){return e.split(":")})),BIM_API_CONFIG.KEY=n.filter((function(e){return"key"===e[0]}))[0][1],BIM_API_CONFIG.USER_NAME=n.filter((function(e){return"username"===e[0]}))[0][1];try{BIM_API_CONFIG.SceneDomain=n.filter((function(e){return"scenedomain"===e[0]}))[0][1]}catch(i){}return t.a(2,BIM_API_CONFIG)}}),t)})))()}}}},3285:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{attrs:{"dialog-title":"文件扫描","dialog-width":"1000px",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit()},cancelbtn:e.handleClose,handleClose:e.handleClose}},[a("div",{staticStyle:{width:"100%",height:"850px",margin:"0 12px 16px 16px",background:"#fff","padding-right":"16px"}},[a("iframe",{staticClass:"pc iframe",staticStyle:{width:"100%",height:"850px"},attrs:{id:"iframeId",src:"/static/axbauche/axbauche.html",frameborder:"0",scrolling:"no"}})])])},i=[]},"335b9":function(e,t,a){"use strict";a.r(t);var n=a("3808"),i=a("8df2");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("543d");var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"75652c3a",null);t["default"]=l.exports},3808:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{attrs:{"dialog-title":e.title,visible:e.dialogVisible,"dialog-width":"32%"},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:e.handleSubmit,cancelbtn:e.handleCancel,handleClose:e.handleCancel}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"类别名称",prop:"Document_Name"}},[a("el-input",{model:{value:e.form.Document_Name,callback:function(t){e.$set(e.form,"Document_Name",t)},expression:"form.Document_Name"}})],1),a("el-form-item",{attrs:{label:"类别编码",prop:"English_Name"}},[a("el-input",{model:{value:e.form.English_Name,callback:function(t){e.$set(e.form,"English_Name",t)},expression:"form.English_Name"}})],1),a("el-form-item",{attrs:{label:"上级目录",prop:"Parent_Id"}},[a("el-tree-select",{ref:"treeSelect",attrs:{"tree-params":e.treeParams},model:{value:e.form.Parent_Id,callback:function(t){e.$set(e.form,"Parent_Id",t)},expression:"form.Parent_Id"}})],1),a("el-form-item",{attrs:{label:"排序号",prop:"Sort"}},[a("el-input",{model:{value:e.form.Sort,callback:function(t){e.$set(e.form,"Sort",e._n(t))},expression:"form.Sort"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleCancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit()}}},[e._v("确 定")])],1)],1)},i=[]},"3aea":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{"dialog-title":"历史记录","dialog-width":"1200px",visible:e.historyVisible,hidebtn:""},on:{"update:visible":function(t){e.historyVisible=t},handleClose:function(t){e.historyVisible=!1}}},[a("el-row",{attrs:{type:"flex",justify:"space-between"}},[a("el-button",{on:{click:e.download}},[a("i",{staticClass:"el-icon-download"}),e._v(" 下载")]),a("el-input",{staticClass:"input-with-select",attrs:{placeholder:"请输入内容"},model:{value:e.filter,callback:function(t){e.filter=t},expression:"filter"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.search},slot:"append"})],1)],1),a("div",{staticClass:"plm-bimtable"},[a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.tablaData},on:{"selection-change":e.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"Doc_Title",label:"标题"}}),a("el-table-column",{attrs:{prop:"Doc_Content",label:"简要描述"}}),a("el-table-column",{attrs:{prop:"Doc_File",label:"附件信息"}}),a("el-table-column",{attrs:{prop:"Create_UserName",label:"变更人",width:"90px"}}),a("el-table-column",{attrs:{prop:"Create_Date",label:"变更时间",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("timeFormat")(t.row.Create_Date,"{y}-{m}-{d} {h}:{i}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.preview(t.row)}}},[e._v("查看")])]}}])})],1),a("div",{staticClass:"sum"},[e._v(" 已选"+e._s(e.ids.length)+"条数据 ")])],1)],1)},i=[]},"3b20":function(e,t,a){"use strict";a.r(t);var n=a("cfa8"),i=a("e7ff");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"3e787977",null);t["default"]=l.exports},4291:function(e,t,a){"use strict";a("7726")},"50d9":function(e,t,a){"use strict";a("ecba")},"543d":function(e,t,a){"use strict";a("12dd")},5862:function(e,t,a){"use strict";a.r(t);var n=a("ca42"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},5972:function(e,t,a){"use strict";a.r(t);var n=a("fbe5"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"66f9":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddArea=ae,t.AddDeepFile=X,t.AddMonomer=H,t.AppendImportPartList=le,t.AreaDelete=ie,t.AreaGetEntity=ee,t.AttachmentGetEntities=A,t.ChangeLoad=v,t.CommonImportDeependToComp=W,t.ContactsAdd=N,t.ContactsDelete=G,t.ContactsEdit=E,t.ContactsGetEntities=B,t.ContactsGetEntity=T,t.ContactsGetTreeList=j,t.DeleteMonomer=J,t.DepartmentAdd=P,t.DepartmentDelete=w,t.DepartmentEdit=D,t.DepartmentGetEntities=S,t.DepartmentGetEntity=C,t.DepartmentGetList=F,t.EditArea=ne,t.EditMonomer=Z,t.FileAdd=g,t.FileAddType=f,t.FileDelete=h,t.FileEdit=b,t.FileGetEntity=p,t.FileHistory=y,t.FileMove=_,t.FileTypeAdd=u,t.FileTypeDelete=d,t.FileTypeEdit=m,t.FileTypeGetEntities=o,t.FileTypeGetEntity=r,t.GeAreaTrees=te,t.GetAreaTreeList=Q,t.GetDictionaryDetailListByCode=x,t.GetEntitiesByRecordId=$,t.GetEntitiesProject=s,t.GetFileCatalog=l,t.GetFilesByType=c,t.GetGetMonomerList=Y,t.GetLoadingFiles=I,t.GetMonomerEntity=q,t.GetProMonomerList=K,t.GetProjectEntity=oe,t.GetProjectsflowmanagementAdd=O,t.GetProjectsflowmanagementEdit=U,t.GetProjectsflowmanagementInfo=z,t.GetShortUrl=V,t.ImportDeependToSteel=R,t.ImportPartList=re,t.SysuserGetUserEntity=M,t.SysuserGetUserList=k,t.UserGroupTree=L;var i=n(a("b775"));function o(e){return(0,i.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:e})}function r(e){return(0,i.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:e})}function l(e){return(0,i.default)({url:"/SYS/Sys_FileType/GetFileCatalog",method:"post",data:e})}function s(e){return(0,i.default)({url:"/SYS/Sys_FileType/GetEntitiesProject",method:"post",data:e})}function c(e){return(0,i.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:e})}function d(e){return(0,i.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:e})}function u(e){return(0,i.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:e})}function f(e){return(0,i.default)({url:"/SYS/Sys_FileType/AddType",method:"post",data:e})}function m(e){return(0,i.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:e})}function p(e){return(0,i.default)({url:"/SYS/Sys_File/GetEntity",method:"post",data:e})}function h(e){return(0,i.default)({url:"/SYS/Sys_File/Delete",method:"post",data:e})}function g(e){return(0,i.default)({url:"/SYS/Sys_File/Add",method:"post",data:e})}function b(e){return(0,i.default)({url:"/SYS/Sys_File/Edit",method:"post",data:e})}function _(e){return(0,i.default)({url:"/SYS/Sys_File/Move",method:"post",data:e})}function v(e){return(0,i.default)({url:"/SYS/Sys_File/IsLoad",method:"post",data:e})}function y(e){return(0,i.default)({url:"/SYS/Sys_File/OldFile",method:"post",data:e})}function I(e){return(0,i.default)({url:"/SYS/Sys_File/GetBIMList",method:"post",data:e})}function C(e){return(0,i.default)({url:"/SYS/Sys_Contacts_Department/GetEntity",method:"post",data:e})}function S(e){return(0,i.default)({url:"/SYS/Sys_Contacts_Department/GetEntities",method:"post",data:e})}function w(e){return(0,i.default)({url:"/SYS/Sys_Contacts_Department/Delete",method:"post",data:e})}function D(e){return(0,i.default)({url:"/SYS/Sys_Contacts_Department/Edit",method:"post",data:e})}function P(e){return(0,i.default)({url:"/SYS/Sys_Contacts_Department/Add",method:"post",data:e})}function F(e){return(0,i.default)({url:"/SYS/Sys_Contacts_Department/GetList",method:"post",data:e})}function k(e){return(0,i.default)({url:"/SYS/User/GetUserList",method:"post",data:e})}function M(e){return(0,i.default)({url:"/SYS/User/GetUserEntity",method:"post",data:e})}function L(e){return(0,i.default)({url:"/SYS/UserGroup/GetChildGroupTree",method:"post",data:e})}function x(e){return(0,i.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function T(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/GetEntity",method:"post",data:e})}function B(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:e})}function j(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:e})}function G(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/Delete",method:"post",data:e})}function E(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/Edit",method:"post",data:e})}function N(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/Add",method:"post",data:e})}function A(e){return(0,i.default)({url:"/SYS/Sys_File/GetAttachmentEntities",method:"post",data:e})}function $(e){return(0,i.default)({url:"/SYS/Sys_File/GetEntitiesByRecordId",method:"post",data:e})}function O(e){return(0,i.default)({url:"/SYS/Sys_Projectsflowmanagement/Add",method:"post",data:e})}function U(e){return(0,i.default)({url:"/SYS/Sys_Projectsflowmanagement/Edit",method:"post",data:e})}function z(e){return(0,i.default)({url:"/SYS/Sys_Projectsflowmanagement/GetEntity",method:"post",data:e})}function V(e){return(0,i.default)({url:"/PLM/XModel/GetShortUrl",method:"post",data:e})}function R(e){return(0,i.default)({url:"/plm/component/ImportDeependToSteel",method:"get",params:e})}function Y(e){return(0,i.default)({url:"/PLM/Plm_Project_Areas/GetGetMonomerList",method:"post",data:e})}function q(e){return(0,i.default)({url:"/PLM/Plm_Project_Areas/GetMonomerEntity",method:"post",data:e})}function K(e){return(0,i.default)({url:"/PLM/Plm_Project_Areas/GetProMonomerList",method:"post",data:e})}function H(e){return(0,i.default)({url:"/PLM/Plm_Project_Areas/AddMonomer",method:"post",data:e})}function Z(e){return(0,i.default)({url:"/PLM/Plm_Project_Areas/EditMonomer",method:"post",data:e})}function J(e){return(0,i.default)({url:"/PLM/Plm_Project_Areas/DeleteMonomer",method:"post",data:e})}function W(e){return(0,i.default)({url:"/plm/MaterialInfo/CommonImportDeependToComp",method:"get",params:e})}function X(e){return(0,i.default)({url:"/PLM/Component/AddDeepFile",method:"post",data:e,timeout:18e5})}function Q(e){return(0,i.default)({url:"/PLM/Plm_Project_Areas/GetAreaTreeList",method:"post",data:e})}function ee(e){return(0,i.default)({url:"/PRO/Project/GetArea",method:"post",data:e})}function te(e){return(0,i.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function ae(e){return(0,i.default)({url:"/PRO/Project/AddArea",method:"post",data:e})}function ne(e){return(0,i.default)({url:"/PRO/Project/EditArea",method:"post",data:e})}function ie(e){return(0,i.default)({url:"/PRO/Project/Delete",method:"post",data:e})}function oe(e){return(0,i.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:e})}function re(e){return(0,i.default)({url:"/PRO/Part/ImportPartList",method:"post",data:e})}function le(e){return(0,i.default)({url:"/PRO/Part/AppendImportPartList",method:"post",data:e})}},"6c88":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a("0d9a"),o=n(a("a7c7"));t.default={components:{bimdialog:o.default},props:{treeData:{type:Array,default:function(){return[]}}},data:function(){return{title:"文件类别",type:"",dialogVisible:!1,form:{Document_Name:"",English_Name:"",Parent_Id:"",Catalog_Code:"",Sort:"",Is_System:!1},treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},rules:{Document_Name:[{required:!0,message:"请输入类别名称",trigger:"blur"}],Sort:[{required:!0,message:"排序不能为空"},{type:"number",message:"排序必须为数字值"}]}}},created:function(){},methods:{handleOpen:function(e,t,a,n,o){var r=this;this.type=e,this.dialogVisible=!0,this.$nextTick((function(){r.$refs.treeSelect.treeDataUpdateFun(a)})),"add"===e?(this.title="添加",this.form={Document_Name:"",English_Name:"",Parent_Id:t.Id,Catalog_Code:"PLMBimFiles",Sort:"",Is_System:!1,Project_Id:o||localStorage.getItem("CurReferenceId"),Is_Disabled:n}):(this.title="编辑",(0,i.FileTypeGetEntity)({id:t.Id}).then((function(e){1==e.IsSucceed&&(r.form=e.Data)})))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;"add"===e.type?(0,i.FileAddType)({sys_File_Type:e.form}).then((function(t){!0===t.IsSucceed&&(e.$message({type:"success",message:e.title+"成功"}),e.$emit("changeData"),e.form={Document_Name:"",English_Name:"",Parent_Id:"",Catalog_Code:"",Sort:"",Is_System:!1},e.dialogVisible=!1)})):(0,i.FileTypeEdit)({sys_File_Type:e.form}).then((function(t){!0===t.IsSucceed&&(e.$message({type:"success",message:e.title+"成功"}),e.$emit("changeData"),e.form={Document_Name:"",English_Name:"",Parent_Id:"",Catalog_Code:"",Sort:"",Is_System:!1},e.dialogVisible=!1)}))}))},handleCancel:function(){this.resetForm("form")},resetForm:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1}}}},"6fc7":function(e,t,a){"use strict";a.r(t);var n=a("b217"),i=a("5862");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"1def3b45",null);t["default"]=l.exports},7726:function(e,t,a){},"7a4d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("e9c4"),a("d3b7");var i=n(a("a7c7")),o=a("0d9a"),r=a("ed08");t.default={name:"History",components:{"el-dialog":i.default},data:function(){return{historyVisible:!1,filter:"",tablaData:[],ids:[]}},methods:{handleOpen:function(e){var t=this;this.tablaData=[],this.historyVisible=!0,(0,o.FileHistory)({id:e}).then((function(e){t.tablaData=e.Data.Data,t.list=(0,r.deepClone)(t.tablaData)}))},search:function(){var e=this;this.tablaData=this.list.filter((function(t){var a=JSON.stringify(t);return-1!==a.indexOf(e.filter)}))},preview:function(e){this.$emit("preview",e)},selectionChange:function(e){this.ids=e.map((function(e){return e.Id}))},download:function(){this.ids.length?this.$emit("download",this.ids):this.$message.warning("请先选择记录")}}}},"7c57":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block"}},[e.label?a("span",{staticClass:"text",style:"width:"+e.labelWidth},[e._v(e._s(e.label)+" ")]):e._e(),a("el-select",{staticClass:"select",style:"width:"+e.width,attrs:{loading:e.loading,filterable:e.filterable,clearable:e.clearable,disabled:e.disabled,readonly:e.readonly,multiple:e.multiple,placeholder:e.placeholder,"multiple-limit":e.multipleLimit,"popper-append-to-body":e.popperAppendToBody},on:{blur:e.blur,focus:e.focus,change:e.change},model:{value:e.tmpvalue,callback:function(t){e.tmpvalue=t},expression:"tmpvalue"}},["projectlist"===e.thistype?e._l(e.options,(function(t){return a("el-option",{key:t.Sys_Project_Id,attrs:{label:"projectlist"===e.type?t.Name:t.Short_Name,value:t.Sys_Project_Id}})})):e._e(),"managerlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.ProjectManagerId,attrs:{label:e.ProjectManagerName,value:e.ProjectManagerId}})})):e._e(),"majorlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"userlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"authuserlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"factorylist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"arealist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"contacts"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.User_Id,attrs:{label:e.Employor_Name,value:e.User_Id}})})):e._e(),"dictionary"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e()],2)],1)},i=[]},"7ec3":function(e,t){e.exports="https://integ-plat-produce-test.bimtk.com/static/img/videoBtn.75484b45.png"},8627:function(e,t,a){"use strict";a.r(t);var n=a("f672"),i=a("5972");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("0a45");var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"42bd7804",null);t["default"]=l.exports},"8a0a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),o=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var r=a("a5f2");t.default={props:{type:{type:String,default:""},label:{type:String,default:""},labelWidth:{type:String,default:""},value:{type:String|Array,default:""},filterable:{type:Boolean,default:!0},clearable:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},multipleLimit:{type:Number,default:0},placeholder:{type:String,default:"请选择"},width:{type:String,default:"200px"},popperAppendToBody:{type:Boolean,default:!0},customparam:{type:Object,default:function(){}},defaultfirst:{type:Boolean,default:!1},projectId:{type:String,default:""}},data:function(){return{loading:!1,options:[],thistype:"",tmpvalue:this.value,tmpkey:"Id"}},watch:{tmpvalue:function(e){this.$emit("input",e)},value:function(e){this.tmpvalue=e}},mounted:function(){this.getdata()},methods:{change:function(){var e=this;if(this.multiple){var t=[];this.tmpvalue.map((function(a){t.push(e.options.find((function(t){return t[e.tmpkey]===a})))})),this.$emit("change",t)}else this.$emit("change",this.options.find((function(t){return t[e.tmpkey]===e.tmpvalue})))},blur:function(e){this.$emit("blur",e)},focus:function(e){this.$emit("focus",e)},getdata:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.loading=!0,e.thistype=e.type,a=e.type,t.n="projectlist"===a?1:"projectshortlist"===a?3:"managerlist"===a?5:"projectmajorlist"===a?7:"sysmajorlist"===a?9:"userlist"===a?11:"authuserlist"===a?13:"factorylist"===a?15:"arealist"===a?17:"contacts"===a?19:21;break;case 1:return t.n=2,e.getproject();case 2:return e.tmpkey="Sys_Project_Id",t.a(3,23);case 3:return e.thistype="projectlist",e.tmpkey="Sys_Project_Id",t.n=4,e.getproject();case 4:return t.a(3,23);case 5:return e.tmpkey="ProjectManagerId",t.n=6,e.getmanager();case 6:return t.a(3,23);case 7:return e.thistype="majorlist",e.tmpkey="Id",t.n=8,e.getmajor(!1);case 8:return t.a(3,23);case 9:return e.thistype="majorlist",e.tmpkey="Id",t.n=10,e.getmajor(!0);case 10:return t.a(3,23);case 11:return e.tmpkey="Id",t.n=12,e.getuser();case 12:return t.a(3,23);case 13:return e.tmpkey="Id",t.n=14,e.getAuthuser();case 14:return t.a(3,23);case 15:return e.tmpkey="Id",t.n=16,e.getfactory();case 16:return t.a(3,23);case 17:return e.tmpkey="Id",t.n=18,e.getarea();case 18:return t.a(3,23);case 19:return e.tmpkey="Id",t.n=20,e.getContacts();case 20:return t.a(3,23);case 21:return e.thistype="dictionary",e.tmpkey="Id",t.n=22,e.getdictionary(e.type);case 22:return t.a(3,23);case 23:e.defaultfirst&&!e.tmpvalue&&(e.tmpvalue=e.options[0][e.tmpkey]),e.loading=!1;case 24:return t.a(2)}}),t)})))()},getContacts:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,r.GetProjectContacts)({model:{ProjectId:e.projectId,IsSysUser:!0,Platform:~~localStorage.getItem("CurPlatform")}});case 1:a=t.v,a.IsSucceed&&(e.options=a.Data);case 2:return t.a(2)}}),t)})))()}}}},"8df2":function(e,t,a){"use strict";a.r(t);var n=a("6c88"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},9518:function(e,t,a){"use strict";a.r(t);var n=a("1a6d"),i=a("0ff9");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("963a");var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"187429b6",null);t["default"]=l.exports},"963a":function(e,t,a){"use strict";a("3085")},a5f2:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetProjectContacts=o;var i=n(a("b775"));function o(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/GetProjectList",method:"post",data:e})}},af5d:function(e,t,a){"use strict";a.r(t);var n=a("7a4d"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},b217:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"plmdialog",attrs:{"dialog-title":e.title,visible:e.dialogVisible,"dialog-width":"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),a("el-form-item",{attrs:{label:"是否变更",prop:"IsChanged"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("否")])],1),e.PLMDeepenFiles||e.CadFiles?a("el-form-item",{attrs:{label:"是否自动拆图"}},[a("el-radio",{attrs:{label:5},model:{value:e.form.IsCaitu,callback:function(t){e.$set(e.form,"IsCaitu",t)},expression:"form.IsCaitu"}},[e._v("是")]),a("el-radio",{attrs:{label:0},model:{value:e.form.IsCaitu,callback:function(t){e.$set(e.form,"IsCaitu",t)},expression:"form.IsCaitu"}},[e._v("否")])],1):e._e(),a("el-form-item",{attrs:{label:"是否将重复文件移入历史文件库",prop:"IsChanged","label-width":"260px"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("否")])],1),e.BIMFiles&&e.isGlendale?a("el-form-item",{attrs:{label:"是否内嵌图纸",prop:"Is_Load"}},[a("el-radio",{attrs:{disabled:"add"!==e.type,label:!0},model:{value:e.form.Is_Drawing,callback:function(t){e.$set(e.form,"Is_Drawing",t)},expression:"form.Is_Drawing"}},[e._v("是")]),a("el-radio",{attrs:{disabled:"add"!==e.type,label:!1},model:{value:e.form.Is_Drawing,callback:function(t){e.$set(e.form,"Is_Drawing",t)},expression:"form.Is_Drawing"}},[e._v("否")])],1):e._e(),e.BIMFiles?a("el-form-item",{attrs:{label:"是否加载",prop:"Is_Load"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("否")])],1):e._e(),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",data:{callback:!1},action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:"edit"===e.type?1:999,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:"",accept:e.allowFile,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("el-form-item",{attrs:{label:"是否通知"}},[a("el-radio",{attrs:{label:!0},on:{input:e.handleRadioChange},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("否")])],1),e.isNotify?[a("el-form-item",{attrs:{label:"通知标题",prop:"model.Title",rules:{required:!0,message:"通知标题不能为空",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.model.Title,callback:function(t){e.$set(e.form.model,"Title",t)},expression:"form.model.Title"}})],1),a("el-form-item",{attrs:{label:"通知人员",prop:"model.UserIds",rules:[{required:!0,message:"请选择通知人员",trigger:"change"}]}},[a("form-item",{attrs:{"project-id":e.projectId,type:"contacts",filterable:"",multiple:"",width:"360px"},model:{value:e.form.model.UserIds,callback:function(t){e.$set(e.form.model,"UserIds",t)},expression:"form.model.UserIds"}})],1),a("el-form-item",{attrs:{label:"通知内容"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{type:"textarea"},model:{value:e.form.model.Content,callback:function(t){e.$set(e.form.model,"Content",t)},expression:"form.model.Content"}})],1)]:e._e()],2)],1)},i=[]},b28f:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Glendale=void 0;var n=a("21c4");t.Glendale={baseUrl:"https://glendale-api.bimtk.com",uploadUrl:"/api/app/model/upload-file",externalUploadUrl:"/api/app/model/transcode-file",token:"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",options:{InitiatingUser:"admin",UniqueCode:"20228_29",Priority:"202",ModelUploadUrl:"".concat((0,n.getTenantId)()),OtherInfo:"",ConfigJson:{style:1,zGrid:1,viewStyle:0,drawing:0,accuracy:3,parametric:1,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:1,unitRatio:.001,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:1,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:1}},optionsCad:{Name:"图纸12月30",InitiatingUser:"admin",Priority:"202",UniqueCode:"test001",IsCAD:!0,ModelUploadUrl:"".concat((0,n.getTenantId)()),ConfigJson:{style:1,zGrid:0,viewStyle:0,drawing:0,accuracy:5,parametric:0,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:0,unitRatio:1,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:0,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:0,faceNumLimit:1e6}}}},be3a:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("a7c7"));t.default={components:{bimdialog:i.default},props:{typeid:{type:String,default:""}},data:function(){return{dialogVisible:!1}},mounted:function(){},methods:{handleSubmit:function(){this.$emit("resetData"),this.dialogVisible=!1},handleClose:function(){this.dialogVisible=!1},handleOpen:function(){var e=this;this.dialogVisible=!0,this.$nextTick((function(){var t=document.getElementById("iframeId");t.contentWindow.postMessage({data:e.typeid},"*")}))}}}},bf4b:function(e,t,a){"use strict";a.r(t);var n=a("be3a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},bf8b:function(e,t,a){"use strict";a.r(t);var n=a("7c57"),i=a("cf8f");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("50d9");var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,null,null);t["default"]=l.exports},c277:function(e,t,a){},c7ab:function(e,t,a){"use strict";a.r(t);var n=a("f68a");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var o,r,l=a("2877"),s=Object(l["a"])(n["default"],o,r,!1,null,null,null);t["default"]=s.exports},ca42:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("c740"),a("a15b"),a("d81d"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("d3b7"),a("159b");var i=n(a("c14f")),o=n(a("1da1")),r=n(a("5530")),l=a("66f9"),s=n(a("a7c7")),c=n(a("bc3a")),d=n(a("c7ab")),u=a("b28f"),f=a("c24f"),m=a("361f"),p=a("21c4"),h=a("2e00"),g=n(a("bf8b")),b=a("e144"),_={Id:"",Doc_Catelog:"",Doc_Type:"",Project_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",IsChanged:!1,Is_Load:!1,Is_pic:!1,Doc_File:"",ishistory:!0,BimId:"",IsCaitu:5,Is_Drawing:!1,model:{UserIds:[],Title:"",Content:""}};t.default={components:{"el-dialog":s.default,OSSUpload:d.default,formItem:g.default},data:function(){return{isNotify:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,form:(0,r.default)({},_),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}]},treeParams:{clickParent:!0,filterable:!1,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},selectParams:{multiple:!1,clearable:!0,placeholder:"请输入内容"},fileType:"",curFile:"",bimvizId:"",modelApp:"",projectInfo:{},projectId:""}},computed:{PLMBIMFiles:function(){return"PLMBimFiles"===this.$route.name||"PLMDesignModel"===this.$route.name||"PROBimFiles"===this.$route.name},EPCBIMFiles:function(){return"EPCBimFiles"===this.$route.name},BIMFiles:function(){return"EPCBimFiles"===this.$route.name||"PLMBimFiles"===this.$route.name||"PLMDesignModel"===this.$route.name||"PROBimFiles"===this.$route.name},CadFiles:function(){return"CadManager"===this.$route.name||"PLMDeepenFiles"===this.$route.name},PLMDeepenFiles:function(){return"PLMDeepenFiles"===this.$route.name},isBimviz:function(){return this.BIMFiles&&"bimviz"===this.modelApp},isGlendale:function(){return this.BIMFiles&&"glendale"===this.modelApp}},watch:{modelApp:function(e){"bimviz"===e&&this.BIMFiles&&(this.allowFile=".ifc,.bzip2,.glzip"),"glendale"===e&&this.BIMFiles&&(this.allowFile=".glzip")}},created:function(){this.fileType=this.$route.name,"PLMPicVideoFiles"===this.fileType&&(this.allowFile="image/*"),this.BIMFiles&&(this.allowFile=".ifc,.bzip,.bzip2,.glzip")},methods:{handleRadioChange:function(e){e&&"edit"===this.type&&this.form.Doc_Title&&(this.form.model.Title="资料变更通知："+this.form.Doc_Title)},handleChange:function(){},onExceed:function(){"edit"===this.type?this.$message.error("最多只能上传1个文件"):this.$message.error("最多只能上传999个文件")},beforeUpload:function(e){var t=e.name.substring(e.name.lastIndexOf("."));if(".glzip"!==t)return this.$message.error("格式错误，上传失败"),!1;this.curFile=e,this.loading=!0},beforeRemove:function(e){if("ready"!==e.status)return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=0;this.fileList.filter((function(t,n){t.name===e.name&&(a=n)}));var n=this.fileList.findIndex((function(t){return t.uid===e.uid}));n>-1&&this.fileList.splice(a,1),this.attachments.splice(a,1);var i="",o="";t.forEach((function(e){i=i+","+e.name.substring(0,e.name.lastIndexOf(".")),o=o+","+e.name})),this.form.Doc_Title=i.substring(1),this.form.Doc_Content=i.substring(1),this.form.Doc_File=o.substring(1),this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.loading=!t.every((function(e){return"success"===e.status}))},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){this.fileList=a,this.loading=!a.every((function(e){return"success"===e.status})),this.loading||(this.form.Doc_Title=this.form.Doc_Content=this.fileList.map((function(e){return e.name.substring(0,e.name.lastIndexOf("."))})).join(","),this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.form.Doc_File=this.fileList.map((function(e){return e.name})).join(","),this.attachments=this.fileList.map((function(e){var t=e.response.Data;return{File_Url:t.split("*")[0],File_Size:t.split("*")[1],File_Type:t.split("*")[2],File_Name:t.split("*")[3]}})))},handleOpen:function(e,t,a,n,i,o){var s=this;this.isNotify=!1,this.projectId=o,this.form=(0,r.default)((0,r.default)({},_),{},{model:{UserIds:[],Title:"",Content:""}}),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.form.Project_Id=o,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.modelApp=n,this.projectInfo=i||{},"add"===this.type?(this.fileList=[],this.title="新增文件",this.form.Id=""):(this.title="编辑文件",(0,l.AttachmentGetEntities)({recordId:t.Id}).then((function(e){s.attachments=e.Data.map((function(e){return delete e.Id,e})),s.fileList=e.Data.map((function(e){return e.name=e.File_Name,e}))})),(0,l.FileGetEntity)({id:t.Id}).then((function(e){e.IsSucceed&&(s.form=(0,r.default)((0,r.default)({},e.Data),{},{model:{UserIds:[],Title:"",Content:""}}))})))},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.$refs["form"].validate(function(){var t=(0,o.default)((0,i.default)().m((function t(a){var n,l,s;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!a){t.n=10;break}if(e.loading=!0,"edit"!==e.type){t.n=1;break}return e.updateInfo(),t.a(2);case 1:if(!e.isBimviz){t.n=2;break}n=new FormData,e.fileList.forEach((function(e){n.append("file",e.raw)})),l=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,key:BIM_API_CONFIG.KEY}),s=l.getModelProjectManager(),s.uploadProjectFiles(BIM_API_CONFIG.USER_NAME,e.bimvizId,n,(function(t){e.updateInfo(),e.getAllLoadingFiles()}),(function(e,t){})),t.n=9;break;case 2:if(!e.isGlendale||"add"!==e.type){t.n=5;break}return t.n=3,(0,f.getConfigure)({code:"glendale_token"});case 3:return u.Glendale.token=t.v.Data,t.n=4,(0,f.getConfigure)({code:"glendale_baseurl"});case 4:u.Glendale.baseUrl=t.v.Data,c.default.all(e.fileList.map(function(){var t=(0,o.default)((0,i.default)().m((function t(a,n){var o,l,s,d,m;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return o=new FormData,o.append("file",a.raw),u.Glendale.options.ConfigJson.drawing=e.form.Is_Drawing?1:0,l=(0,r.default)((0,r.default)({},u.Glendale.options),{},{UniqueCode:"".concat((0,p.getTenantId)(),"__").concat((0,b.v4)()),Name:encodeURIComponent(a.name)}),t.n=1,(0,f.getConfigure)({code:"cloudModel"});case 1:if(s=t.v.Data,!s||"1"!==s.split(";")[0]){t.n=2;break}return d=s.split(";")[1],m=s.split(";")[3],t.n=2,c.default.post("".concat(d).concat(u.Glendale.uploadUrl,"?input=").concat(JSON.stringify(l)),o,{headers:{Token:m,"Content-Type":"multipart/form-data"}});case 2:return t.a(2,c.default.post("".concat(u.Glendale.baseUrl).concat(u.Glendale.uploadUrl,"?input=").concat(JSON.stringify(l)),o,{headers:{Token:u.Glendale.token,"Content-Type":"multipart/form-data"}}).then((function(e){a.BimId=e.data.datas.lightweightName})))}}),t)})));return function(e,a){return t.apply(this,arguments)}}())).then((function(t){e.form.BimId=e.fileList.map((function(e){return e.BimId})).join(","),e.updateInfo()})),t.n=9;break;case 5:if(!e.CadFiles){t.n=8;break}return t.n=6,(0,f.getConfigure)({code:"glendale_token"});case 6:return u.Glendale.token=t.v.Data,t.n=7,(0,f.getConfigure)({code:"glendale_baseurl"});case 7:u.Glendale.baseUrl=t.v.Data,c.default.all(e.fileList.map((function(e,t){var a=e.raw.name.split("."),n=a[a.length-1];if("glzip"===n){var i=new FormData;i.append("file",e.raw);var o=(0,r.default)((0,r.default)({},u.Glendale.optionsCad),{},{UniqueCode:"".concat((0,p.getTenantId)(),"__").concat((0,b.v4)()),Name:encodeURIComponent(e.name)});return c.default.post("".concat(u.Glendale.baseUrl).concat(u.Glendale.uploadUrl,"?input=").concat(JSON.stringify(o)),i,{headers:{Token:u.Glendale.token,"Content-Type":"multipart/form-data"}}).then((function(t){e.BimId=t.data.datas.lightweightName}))}}))).then((function(t){e.form.BimId=e.fileList.map((function(e){return e.BimId})).join(","),e.updateInfo(!1)})),t.n=9;break;case 8:e.updateInfo();case 9:t.n=11;break;case 10:return e.$message({message:"请将表单填写完整",type:"warning"}),t.a(2,!1);case 11:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}());case 1:return t.a(2)}}),t)})))()},updateInfo:function(){var e=arguments,t=this;return(0,o.default)((0,i.default)().m((function a(){var n,o,r;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(n=!(e.length>0&&void 0!==e[0])||e[0],r={},r=t.isNotify?t.form.model:{UserIds:[],Title:"",Content:""},"add"!==t.type){a.n=2;break}return a.n=1,(0,l.FileAdd)({file:t.form,model:r,attachmentList:t.attachments});case 1:o=a.v,a.n=4;break;case 2:return a.n=3,(0,l.FileEdit)({file:t.form,model:r,attachmentList:t.attachments});case 3:o=a.v;case 4:n&&t.BIMFiles&&(t.projectInfo.Bim_Type="glendale"===t.modelApp?2:1,t.projectInfo.Custom_Fields=[],"1"===(0,p.getCurPlatform)()&&(0,h.SaveEpcProject)({epc_Project:t.projectInfo}),"3"===(0,p.getCurPlatform)()&&(0,m.GetPlmProjectEdit)({plm_Projects:t.projectInfo,source:3})),o.IsSucceed?(t.$message({message:"保存成功",type:"success"}),t.$emit("getData",t.form.Doc_Type),t.loading=!1,t.handleClose()):(t.loading=!1,t.$message.error(o.Message));case 5:return a.a(2)}}),a)})))()},getAllLoadingFiles:function(){var e=this;(0,l.GetLoadingFiles)().then((function(t){e.updateLoadingFiles(t.Data)}))},updateLoadingFiles:function(e){var t=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,key:BIM_API_CONFIG.KEY}),a=t.getModelProjectManager(),n={SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,LoadFiles:e};a.updateSceneSettings(BIM_API_CONFIG.USER_NAME,this.bimvizId,n,(function(e){}))}}}},cf8f:function(e,t,a){"use strict";a.r(t);var n=a("8a0a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},cfa8:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("common-files")],1)},i=[]},d363:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetCurPlmProjectGetEntity=o,t.GetTenantModelExtend=r,t.SaveEpcBimVizId=l;var i=n(a("b775"));function o(e){return(0,i.default)({url:"PLM/XModel/GetProjectInfo",method:"post",data:e})}function r(){return(0,i.default)({url:"SYS/PreferenceSetting/GetPreferenceSettingValue",method:"post",data:{code:"BimModel"}})}function l(e){return(0,i.default)({url:"/EPC/Project/SaveEpcBimVizId",method:"post",data:e})}},dc02:function(e,t,a){"use strict";function n(e,t,a){void 0===a&&(a="[]"),e=e.replace(/=/g,"");var i=[];if(null==t)return i;switch(t.constructor){case String:case Number:case Boolean:i.push(encodeURIComponent(e)+"="+encodeURIComponent(t));break;case Array:t.forEach((function(t){i=i.concat(n(""+e+a+"=",t,a))}));break;case Object:Object.keys(t).forEach((function(o){var r=t[o];i=i.concat(n(e+"["+o+"]",r,a))}))}return i}function i(e){var t=[];return e.forEach((function(e){"string"==typeof e?t.push(e):t=t.concat(i(e))})),t}
/**
 * Vue Jsonp.
 * # Carry Your World #
 *
 * @author: LancerComet
 * @license: MIT
 */a.r(t),a.d(t,"VueJsonp",(function(){return o})),a.d(t,"jsonp",(function(){return r}));var o={install:function(e){e.prototype.$jsonp=r}};function r(e,t,a){var o;if(void 0===t&&(t={}),"string"!=typeof e)throw new Error('[Vue-jsonp] Type of param "url" is not string.');if("object"!=typeof t||!t)throw new Error("[Vue-jsonp] Invalid params, should be an object.");var r="number"==typeof a?a:null!==(o=null==a?void 0:a.timeout)&&void 0!==o?o:5e3,l="[]";if("object"==typeof a){var s=a.arrayIndicator;"string"==typeof s&&(l=s)}return new Promise((function(a,o){var s="string"==typeof t.callbackQuery?t.callbackQuery:"callback",c="string"==typeof t.callbackName?t.callbackName:"jsonp_"+(Math.floor(1e5*Math.random())*Date.now()).toString(16);t[s]=c,delete t.callbackQuery,delete t.callbackName;var d=[];Object.keys(t).forEach((function(e){d=d.concat(n(e,t[e],l))}));var u=i(d).join("&"),f=function(){m(),clearTimeout(h),o({status:400,statusText:"Bad Request"})},m=function(){g.removeEventListener("error",f)},p=function(){document.body.removeChild(g),delete window[c]},h=null;r>-1&&(h=setTimeout((function(){m(),p(),o({statusText:"Request Timeout",status:408})}),r)),window[c]=function(e){clearTimeout(h),m(),p(),a(e)};var g=document.createElement("script");g.addEventListener("error",f),g.src=e+(/\?/.test(e)?"&":"?")+u,document.body.appendChild(g)}))}},e770:function(e,t,a){"use strict";a.r(t);var n=a("3285"),i=a("bf4b");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"171e6fd6",null);t["default"]=l.exports},e7ff:function(e,t,a){"use strict";a.r(t);var n=a("1c1e"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},ecba:function(e,t,a){},f672:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{attrs:{"dialog-title":e.title,"dialog-width":"800px",visible:e.dialogVisible,hidebtn:""},on:{"update:visible":function(t){e.dialogVisible=t},handleClose:function(t){e.dialogVisible=!1}}},[a("div",{staticClass:"item"},[a("span",[e._v("对比方式")]),a("div",[a("el-radio",{attrs:{label:"version"},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v("版本对比")]),a("el-radio",{attrs:{label:"any"},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v("自选文件对比")])],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:"version"===e.radio,expression:"radio==='version'"}]},[a("div",{staticClass:"plm-bimtable"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table",attrs:{data:e.tableData,height:"300px",stripe:""}},[a("el-table-column",{attrs:{prop:"Create_Date",label:"历史版本"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("timeFormat")(t.row.Create_Date,"{y}-{m}-{d} {h}:{i}"))+" ")]}}])}),a("el-table-column",{attrs:{prop:"Create_UserName",label:"操作人"}}),a("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",disabled:t.row.BimId===e.mainModelId},on:{click:function(a){return e.compareModel(t.row)}}},[e._v("对比")])]}}])})],1)],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:"any"===e.radio,expression:"radio==='any'"}]},[a("div",{staticClass:"item"},[a("span",[e._v("目标文件夹")]),a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.typeId,callback:function(t){e.typeId=t},expression:"typeId"}})],1),a("div",{staticClass:"item"},[a("span",[e._v("目标文件")]),a("div",{staticClass:"plm-bimtable"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table",attrs:{data:e.tableData2,height:"300px",stripe:""}},[a("el-table-column",{attrs:{prop:"Doc_Title",label:e.LChange("标题"),"show-overflow-tooltip":"","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.Doc_Title||t.row.File_Name))])]}}])}),a("el-table-column",{attrs:{prop:"Doc_Content",label:e.LChange("描述"),"show-overflow-tooltip":"","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.Doc_Content))])]}}])}),a("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",disabled:t.row.BimId===e.mainModelId},on:{click:function(a){return e.compareModel(t.row)}}},[e._v("对比")])]}}])})],1),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.pageInfo.Page,"page-sizes":[15,50,100,1e3],"page-size":e.pageInfo.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pageInfo.TotalCount},on:{"update:currentPage":function(t){return e.$set(e.pageInfo,"Page",t)},"update:current-page":function(t){return e.$set(e.pageInfo,"Page",t)},"size-change":e.handlePageSizeChange,"current-change":e.getData}})],1)],1)])])])},i=[]},f68a:function(e,t,a){"use strict";a.r(t);var n=a("0ce7"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},fbe5:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("d3b7"),a("3ca3"),a("ddb0");var i=n(a("a7c7")),o=a("0d9a"),r=n(a("2082")),l=(a("ed08"),a("8975"));t.default={name:"ModelCompareDialog",components:{bimdialog:i.default},mixins:[r.default],data:function(){return{addPageArray:[{path:"/modelCompare",hidden:!0,component:function(){return a.e("chunk-c91647a6").then(a.bind(null,"855f"))},name:"ModelCompare",meta:{title:"模型对比"}}],title:"模型对比",radio:"version",dialogVisible:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},tableLoading:!1,tableData:[],tableData2:[],pageInfo:{Page:1,PageSize:15,TotalCount:0},typeId:"",mainModelId:"",fileObj:{},projectId:""}},watch:{typeId:function(){this.pageInfo.Page=1,this.getData()}},mounted:function(){this.getData()},methods:{getHistory:function(){var e=this;(0,o.FileHistory)({id:this.fileId}).then((function(t){e.tableData=t.Data.Data}))},handleOpen:function(e,t,a,n,i){var o=this;this.mainModelId=e,this.dialogVisible=!0,this.fileId=a,this.fileObj=n,this.projectId=i,this.getHistory(),this.$nextTick((function(){o.$refs.treeSelect.treeDataUpdateFun(t)}))},handleClose:function(){this.dialogVisible=!1},getData:function(){var e=this;(0,o.GetFilesByType)({fileModel:{typeId:this.typeId,doc_Catelog:"PLMBimFiles",Projectid:this.projectId},pageInfo:this.pageInfo}).then((function(t){var a=t.Data,n=a.Data,i=a.Page,o=a.PageSize,r=a.TotalCount;e.tableData2=n,e.pageInfo={Page:i,PageSize:o,TotalCount:r}}))},handlePageSizeChange:function(e){this.pageInfo.PageSize=e,this.getData()},compareModel:function(e){this.dialogVisible=!1,this.$router.push({name:"ModelCompare",query:{mainModelId:this.mainModelId,compareModelId:e.BimId,mainFileId:this.fileId,compareFileId:e.Id,type:"version"===this.radio?"版本对比":"自选文件对比",name1:"version"===this.radio?(0,l.timeFormat)(this.fileObj.Create_Date):this.fileObj.File_Name,name2:"version"===this.radio?(0,l.timeFormat)(e.Create_Date):e.File_Name}})}}}}}]);