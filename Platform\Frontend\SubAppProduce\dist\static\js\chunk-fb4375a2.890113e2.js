(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-fb4375a2"],{"2d71c":function(e,t,n){"use strict";n("9bcc")},"2e8a":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=u,t.GetCompTypeTree=d,t.GetComponentTypeEntity=l,t.GetComponentTypeList=i,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=p,t.GetTypePageList=s,t.RestoreTemplateType=_,t.SavDeepenTemplateSetting=y,t.SaveCompTypeIdentifySetting=b,t.SaveComponentType=c,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=m;var o=a(n("b775")),r=a(n("4328"));function i(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function b(e){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function y(e){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function _(e){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"37d9":function(e,t,n){"use strict";n.r(t);var a=n("4ba4"),o=n("cc61");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("2d71c");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"3f6a2afa",null);t["default"]=s.exports},"43d0":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("2909")),r=a(n("c14f")),i=a(n("1da1"));n("7db0"),n("c740"),n("caad"),n("d81d"),n("14d9"),n("13d5"),n("a434"),n("e9f5"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("a9e3"),n("d3b7"),n("2532"),n("159b");var s=a(n("34e9")),c=a(n("37d9")),l=a(n("0f97")),u=a(n("15ac")),d=n("1b69"),f=n("ed08"),p=n("abfb"),m=n("82a3"),h=a(n("4b32")),g=n("cf45");t.default={components:{TopHeader:s.default,Add:c.default,DynamicDataTable:l.default,CheckInfo:h.default},mixins:[u.default],props:{typePage:{type:String,default:""},isEdit:{type:Boolean,default:!1},tbData:{type:Array,default:function(){return[]}},tbConfigCode:{type:String,default:""},total:{type:Number,default:0}},data:function(){return{form:{Id:"",Project_Id:"",Remark:"",Project_Name:"",Project_Code:"",Cancel_Date:""},rules:{Project_Id:[{required:!0,message:"请选择项目",trigger:"change"}]},btnLoading:!1,loading:!1,isClicked:!1,currentComponent:"",title:"",dialogVisible:!1,tbConfig:{Pager_Align:"center",Tree_Key:"Component_Id"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],tbLoading:!1,projectOptions:[],checkedList:[],pageInfo:{Project_Code:"",Project_Id:"",Remark:"",Project_Name:""},sums:[],returnOptions:[]}},computed:{tableData:{get:function(){return this.tbData},set:function(e){this.$emit("update:tbData",e)}}},watch:{"tbData.length":{handler:function(){this.getTotal()}}},mounted:function(){this.isEdit?this.init():this.getTableConfig(this.tbConfigCode),this.getReturnType(),this.getProjectList()},methods:{init:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,e.getTableConfig(e.tbConfigCode);case 1:e.isEdit&&(e.getInfo(),e.$emit("getTbList"));case 2:return t.a(2)}}),t)})))()},getReturnType:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,g.getDictionary)("return_type");case 1:e.returnOptions=t.v;case 2:return t.a(2)}}),t)})))()},getTotal:function(){var e=this,t=this.$refs.dyTable.$refs.dtable.columns;t.forEach((function(t,n){if(0===n)e.sums[0]="合计";else if("NetWeight"===t.property){var a=e.tbData.reduce((function(n,a){return e.highPrecisionAdd(n,a[t.property])}),0);e.$set(e.sums,n,a)}else e.$set(e.sums,n,"")}))},toMain:function(){"stocks"===this.typePage?this.$router.push({name:"PROReturnStocks"}):this.$router.push({name:"PROReturnGoods"})},getInfo:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var n,a,o,i,s,c,l,u,d;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(n=null,"stocks"!==e.typePage){t.n=2;break}return t.n=1,e.getReturnStocksEntity();case 1:n=t.v,t.n=4;break;case 2:return t.n=3,e.getReturnGoodsEntity();case 3:n=t.v;case 4:n.IsSucceed?(a=n.Data,o=a.Id,i=a.Project_Id,s=a.Remark,c=a.Project_Name,l=a.Project_Code,u=a.Cancel_Date,d=a.Return_Date,e.form.Project_Id=i,e.form.Remark=s,e.form.Project_Name=c,e.form.Project_Code=l,e.form.Cancel_Date=u,e.form.Return_Date=d,e.form.Id=o):e.$message({message:n.Message,type:"error"});case 5:return t.a(2)}}),t)})))()},getReturnStocksEntity:function(){var e=this;return new Promise((function(t,n){(0,p.GetStockCancelDocEntity)({id:e.$route.query.id}).then((function(e){t(e)}))}))},getReturnGoodsEntity:function(){var e=this;return new Promise((function(t,n){(0,m.GetReturnDocEntity)({id:e.$route.query.id}).then((function(e){t(e)}))}))},getProjectList:function(){var e=this;(0,d.GetProjectList)({}).then((function(t){t.IsSucceed?e.projectOptions=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=(0,i.default)((0,r.default)().m((function t(n){var a,o,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(n){t.n=1;break}return t.a(2);case 1:if(e.isClicked=!0,e.isEdit||(a=e.projectOptions.find((function(t){return t.Id===e.form.Project_Id})),e.form.Cancel_Date=(0,f.parseTime)(new Date,"{y}-{m}-{d} {h}:{i}:{s}"),e.form.Return_Date=(0,f.parseTime)(new Date,"{y}-{m}-{d} {h}:{i}:{s}"),e.form.Project_Name=a.Name,e.form.Project_Code=a.Code),o={entity:{Project_Id:e.form.Project_Id,Project_Name:e.form.Project_Name,Project_Code:e.form.Project_Code,Cancel_Date:e.form.Cancel_Date,Return_Date:e.form.Return_Date,Remark:e.form.Remark},details:e.tableData},"stocks"===e.typePage?delete o.entity.Return_Date:delete o.entity.Cancel_Date,e.isEdit&&(o.entity.Id=e.$route.query.id),i=null,e.btnLoading=!0,"stocks"!==e.typePage){t.n=3;break}return t.n=2,e.submitStocks(o);case 2:i=t.v,t.n=5;break;case 3:return t.n=4,e.submitGoods(o);case 4:i=t.v;case 5:e.btnLoading=!1,i.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.toMain()):(e.isClicked=!1,e.$message({message:i.Message,type:"error"}));case 6:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},submitStocks:function(e){return new Promise((function(t,n){(0,p.SaveComponentStockCancel)(e).then((function(e){t(e)}))}))},submitGoods:function(e){return new Promise((function(t,n){(0,m.SaveStockReturn)(e).then((function(e){t(e)}))}))},selectList:function(e){var t,n=this.tableData.map((function(e){return e.Component_Id})),a=e.reduce((function(e,t){return!n.includes(t.Component_Id)&&e.push(t),e}),[]);(t=this.tableData).push.apply(t,(0,o.default)(a))},showTable:function(){var e=this;this.$nextTick((function(t){e.tbLoading=!1}))},multiSelectedChange:function(e){this.checkedList=e},handleDelete:function(){var e=this;this.checkedList.forEach((function(t,n){var a=e.tableData.findIndex((function(e){return e.Component_Id===t.Component_Id}));-1!==a&&e.tableData.splice(a,1)}))},selectChange:function(e){this.tableData=[]},handleAdd:function(){var e=this;this.currentComponent="Add",this.title="添加明细",this.dialogVisible=!0,this.$nextTick((function(t){e.$refs.content.init(e.tbData)}))},handleClose:function(){this.dialogVisible=!1},handleInfo:function(e){this.$refs.info.handleOpen(e)}}}},4677:function(e,t,n){"use strict";n("809c")},"482a":function(e,t,n){"use strict";n.r(t);var a=n("43d0"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"4ba4":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticStyle:{display:"flex","margin-bottom":"10px"}},[n("span",{staticClass:"ys"},[e._v(" "+e._s("stocks"===e.typePage?"退库":"退货")+"原因")]),n("el-input",{attrs:{placeholder:"请输入内容",clearable:""},model:{value:e.input,callback:function(t){e.input=t},expression:"input"}})],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[n("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,"sum-values":e.sums,"select-width":70,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"hsearch_InstallUnit_Name",fn:function(t){var a=t.column;return[n("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.installNameChange},model:{value:e.$refs.dyTable.searchedField[a.Code],callback:function(t){e.$set(e.$refs.dyTable.searchedField,a.Code,t)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),e._l(e.installOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})}))],2)]}},{key:"hsearch_Component_Type_Name",fn:function(t){var a=t.column;return[n("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.fPartChange},model:{value:e.$refs.dyTable.searchedField[a.Code],callback:function(t){e.$set(e.$refs.dyTable.searchedField,a.Code,t)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),e._l(e.cmptTypes_1,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})}))],2)]}},{key:"hsearch_Component_Sub_Type_Name",fn:function(t){t.column;return[n("el-select",{attrs:{disabled:!e.$refs.dyTable.searchedField["Component_Type_Name"],placeholder:"请选择",clearable:""},on:{change:e.showSearchBtn},model:{value:e.subTypeName,callback:function(t){e.subTypeName=t},expression:"subTypeName"}},[n("el-option",{attrs:{label:"全部",value:""}}),e._l(e.cmptTypes_2,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})}))],2)]}},{key:"op",fn:function(t){var a=t.row,o=t.index;return["打包件"===a.C_Type?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleInfo(a)}}},[e._v("查看")]):e._e()]}}])})],1),n("div",{staticClass:"footer"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{disabled:!e.select.length,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1),n("check-info",{ref:"info"})],1)},o=[]},"4c10":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("7db0"),n("d81d"),n("13d5"),n("e9f5"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("d3b7"),n("159b");var o=a(n("5530")),r=a(n("c14f")),i=a(n("1da1")),s=a(n("0f97")),c=n("abfb"),l=a(n("15ac")),u=n("82a3"),d=a(n("4b32")),f=n("2e8a"),p=n("f2f6");t.default={components:{DynamicDataTable:s.default,CheckInfo:d.default},mixins:[l.default],props:{typePage:{type:String,default:""},tableData:{type:Array,default:function(){return[]}},projectId:{type:String,default:""}},data:function(){return{input:"",subTypeName:"",tbConfig:{Pager_Align:"center",Tree_Key:"Component_Id",Is_Lazy:!0,Children_Field:"children",hasChildren:"Is_Pack"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],tbData:[],total:0,tbLoading:!1,select:[],parentArray:[],sums:[],cmptTypes_1:[],projects:[],cmptTypes_2:[],installOption:[]}},watch:{"tbData.length":{handler:function(){this.getTotal()}}},methods:{init:function(e){var t=this;return(0,i.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return t.parentArray=e,n.n=1,t.getTableConfig("pro_waiting_cancel_stock_list");case 1:t.fetchData(),t.getSearchType();case 2:return n.a(2)}}),n)})))()},fetchData:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.tbLoading=!0,n=null,"stocks"!==e.typePage){t.n=2;break}return t.n=1,e.getStockList();case 1:n=t.v,t.n=4;break;case 2:return t.n=3,e.getGoodsList();case 3:n=t.v;case 4:n.IsSucceed?(e.tbData=n.Data.Data,e.total=n.Data.TotalCount,e.toggleSelection(e.parentArray)):e.$message({message:n.Message,type:"error"}),e.tbLoading=!1;case 5:return t.a(2)}}),t)})))()},installNameChange:function(e){this.$refs.dyTable.searchedField["InstallUnit_Name"]=e,this.showSearchBtn()},getSearchType:function(){var e=this;(0,f.GetComponentTypeList)({Level:1}).then((function(t){t.IsSucceed&&(e.cmptTypes_1=t.Data)})),(0,p.GetInstallUnitList)({Project_Id:this.projectId}).then((function(t){t.IsSucceed&&(e.installOption=t.Data)}))},fPartChange:function(e){this.showSearchBtn(),this.subTypeName="",this.cmptTypes_2=[],e&&this.getLitterPartChange(e)},getLitterPartChange:function(e){var t=this,n=this.cmptTypes_1.find((function(t){return t.Name===e}));(0,f.GetComponentTypeList)({Level:2,Parent_Id:n.Id}).then((function(e){e.IsSucceed&&(t.cmptTypes_2=e.Data)}))},getStockList:function(){var e=this;return new Promise((function(t,n){(0,c.GetInStockPageList)((0,o.default)((0,o.default)({},e.queryInfo),{},{Project_Id:e.projectId})).then((function(e){t(e)}))}))},getGoodsList:function(){var e=this;return new Promise((function(t,n){(0,u.GetStockOutPageList)((0,o.default)((0,o.default)({},e.queryInfo),{},{Project_Id:e.projectId})).then((function(e){t(e)}))}))},handleInfo:function(e){this.$refs.info.handleOpen(e)},getTotal:function(){var e=this,t=this.$refs.dyTable.$refs.dtable.columns;t.forEach((function(t,n){if(0===n)e.sums[0]="合计";else if("NetWeight"===t.property){var a=e.tbData.reduce((function(n,a){return e.highPrecisionAdd(n,a[t.property])}),0);e.$set(e.sums,n,a)}else e.$set(e.sums,n,"")}))},handleSubmit:function(){var e=this;this.select.forEach((function(t){e.$set(t,"Reason",e.input),e.$set(t,"Remark",e.input)})),this.$emit("selectList",this.select),this.$emit("close")},toggleSelection:function(e){var t=this,n=e.map((function(e){return e.Component_Id}));this.$nextTick((function(e){t.tbData.forEach((function(e){n.find((function(t){return t===e.Component_Id}))&&t.$refs.dyTable.$refs.dtable.toggleRowSelection(e)}))}))},multiSelectedChange:function(e){this.select=e}}}},"809c":function(e,t,n){},"8a9e":function(e,t,n){"use strict";n.r(t);var a=n("e0ae3"),o=n("482a");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("4677");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"33abe7ea",null);t["default"]=s.exports},"9bcc":function(e,t,n){},cc61:function(e,t,n){"use strict";n.r(t);var a=n("4c10"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},cf45:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=o,n("d3b7");var a=n("6186");function o(e){return new Promise((function(t,n){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},e0ae3:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[n("div",{staticClass:" cs-z-page-main-content"},[n("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[n("div",{staticClass:"cs-header"},[n("router-link",{attrs:{to:{name:e.$route.query.pg_redirect}}},[n("el-button",{attrs:{circle:"",icon:"el-icon-arrow-left",size:"mini"}})],1),n("strong",{staticClass:"title"},[e._v(e._s(!0===e.isEdit?"编辑":"新增")+e._s("stocks"===e.typePage?"成品退库单":"成品退货单"))])],1)]},proxy:!0},{key:"right",fn:function(){return[n("el-button",{attrs:{loading:e.btnLoading,disabled:e.isClicked,type:"primary"},on:{click:e.handleSubmit}},[e._v("保 存")])]},proxy:!0}])}),n("el-row",[n("el-form",{ref:"form",staticStyle:{width:"100%","margin-top":"10px"},attrs:{model:e.form,rules:e.rules,inline:"","label-width":"100px"}},[n("el-col",{attrs:{span:7}},[n("el-form-item",{attrs:{label:"项目名称：",prop:"Project_Id"}},[n("el-select",{attrs:{disabled:e.isEdit,placeholder:"请选择",filterable:""},on:{change:e.selectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projectOptions,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),n("el-col",{attrs:{span:17}},[n("el-form-item",{staticClass:"cs-itm",attrs:{label:"stocks"===e.typePage?"退库说明：":"退货说明："}},[n("el-input",{attrs:{clearable:""},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1)],1),n("div",{staticStyle:{"margin-bottom":"10px"}},[n("el-button",{attrs:{type:"primary",disabled:!e.form.Project_Id},on:{click:e.handleAdd}},[e._v("添加明细")]),n("el-button",{attrs:{disabled:!e.checkedList.length,type:"danger"},on:{click:e.handleDelete}},[e._v("删除明细")])],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[e.tableData?n("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,config:e.tbConfig,data:e.tableData,page:e.queryInfo.Page,total:e.total,"sum-values":e.sums,"select-width":70,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Type",fn:function(t){var a=t.row;return"goods"===e.typePage?[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:a.Type,callback:function(t){e.$set(a,"Type",t)},expression:"row.Type"}},e._l(e.returnOptions,(function(e){return n("el-option",{key:e.Display_Name,attrs:{label:e.Display_Name,value:e.Display_Name}})})),1)]:void 0}},{key:"op",fn:function(t){var a=t.row,o=t.index;return["打包件"===a.C_Type?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleInfo(a)}}},[e._v("查看")]):e._e()]}}],null,!0)}):e._e()],1),n("check-info",{ref:"info"}),e.dialogVisible?n("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"60%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"type-page":e.typePage,"project-id":e.form.Project_Id,"dialog-visible":e.dialogVisible,"table-data":e.tableData},on:{"update:tableData":function(t){e.tableData=t},"update:table-data":function(t){e.tableData=t},selectList:e.selectList,close:e.handleClose}})],1):e._e()],1)])},o=[]}}]);