(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-da677b98"],{"0373":function(e,t,a){"use strict";a.r(t);var l=a("5e46"),r=a("0c24");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("a866");var n=a("2877"),i=Object(n["a"])(r["default"],l["a"],l["b"],!1,null,"6cb85148",null);t["default"]=i.exports},"05ce":function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a("f289"),o=l(a("506b"));t.default={name:"PROSysReceiptNumber",components:{Dialog:o.default},data:function(){return{tableData:[]}},created:function(){var e=this;(0,r.GetBillSetupList)().then((function(t){e.tableData=t.Data}))},methods:{handleClick:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.$refs.dialog.dialogOpen(e,t)},changeTable:function(){var e=this;(0,r.GetBillSetupList)().then((function(t){e.tableData=t.Data}))},recoverClick:function(e){var t=this;this.$confirm("此操作将恢复初始配置, 是否继续吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,r.RecoveryBillSetup)({id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"恢复成功!"}),t.changeTable()):t.$message({type:"info",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))}}}},"0c24":function(e,t,a){"use strict";a.r(t);var l=a("05ce"),r=a.n(l);for(var o in l)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(o);t["default"]=r.a},"1ef8":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return r}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"add"===e.title?"新增":"编辑",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"140px"}},[a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{attrs:{readonly:"",disabled:""},model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1),a("el-form-item",{attrs:{label:"编号",prop:"Code"}},[a("el-input",{attrs:{readonly:"",disabled:""},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),a("el-form-item",{attrs:{label:"单据前缀",prop:"Prefix"}},[a("el-input",{model:{value:e.form.Prefix,callback:function(t){e.$set(e.form,"Prefix",t)},expression:"form.Prefix"}})],1),a("el-form-item",{attrs:{label:"是否包含年份",prop:"Is_Year"}},[a("el-switch",{on:{change:e.changeYearType},model:{value:e.form.Is_Year,callback:function(t){e.$set(e.form,"Is_Year",t)},expression:"form.Is_Year"}})],1),a("el-form-item",{attrs:{label:"是否包含月份",prop:"Is_Month"}},[a("el-switch",{model:{value:e.form.Is_Month,callback:function(t){e.$set(e.form,"Is_Month",t)},expression:"form.Is_Month"}})],1),a("el-form-item",{attrs:{label:"是否包含日期",prop:"Is_Day"}},[a("el-switch",{model:{value:e.form.Is_Day,callback:function(t){e.$set(e.form,"Is_Day",t)},expression:"form.Is_Day"}})],1),e.form.Is_Year?a("el-form-item",{attrs:{label:"年份位数",prop:"Year_Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Year_Type,callback:function(t){e.$set(e.form,"Year_Type",t)},expression:"form.Year_Type"}},[a("el-option",{attrs:{label:"4",value:"4"}}),a("el-option",{attrs:{label:"2",value:"2"}})],1)],1):e._e(),a("el-form-item",{attrs:{label:"流水单号长度",prop:"Sequence_Length"}},[a("el-input",{model:{value:e.form.Sequence_Length,callback:function(t){e.$set(e.form,"Sequence_Length",e._n(t))},expression:"form.Sequence_Length"}})],1),a("el-form-item",{attrs:{label:"生成编号规则唯一",prop:"Is_Working_Object_Unique"}},[a("el-switch",{model:{value:e.form.Is_Working_Object_Unique,callback:function(t){e.$set(e.form,"Is_Working_Object_Unique",t)},expression:"form.Is_Working_Object_Unique"}})],1),a("el-form-item",{attrs:{label:"说明",prop:"Remark"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:4,maxRows:6}},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.dialogSubmit}},[e._v("确 定")])],1)],1)},r=[]},"31e6":function(e,t,a){},"506b":function(e,t,a){"use strict";a.r(t);var l=a("1ef8"),r=a("ef35");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var n=a("2877"),i=Object(n["a"])(r["default"],l["a"],l["b"],!1,null,"8c9a4924",null);t["default"]=i.exports},"5e46":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return r}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100 cs-z-shadow"},[a("div",{staticClass:"container"},[a("el-table",{staticClass:"cs-custom-table",staticStyle:{width:"100%","margin-top":"20px"},attrs:{height:"calc(100% - 50px)",data:e.tableData,border:"",stripe:""}},[a("el-table-column",{attrs:{prop:"Display_Name",label:"名称",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Code",label:"编号",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Prefix",label:"单据前缀",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Is_Year",label:"是否包含年份",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{value:e.row.Is_Year,disabled:""}})]}}])}),a("el-table-column",{attrs:{prop:"Is_Month",label:"是否包含月份",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{value:e.row.Is_Month,disabled:""}})]}}])}),a("el-table-column",{attrs:{prop:"Is_Day",label:"是否包含日期",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{value:e.row.Is_Day,disabled:""}})]}}])}),a("el-table-column",{attrs:{prop:"Year_Type",label:"年份位数",align:"center","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{prop:"Sequence_Length",label:"流水号长度",align:"center","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{prop:"Is_Working_Object_Unique",label:"当前工厂单独生成",align:"center",width:"120"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{value:e.row.Is_Working_Object_Unique,disabled:""}})]}}])}),a("el-table-column",{attrs:{prop:"Remark",label:"说明","show-overflow-tooltip":"",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.recoverClick(t.row)}}},[e._v("恢复初始配置")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleClick("edit",t.row)}}},[e._v("编辑")])]}}])})],1)],1),a("Dialog",{ref:"dialog",on:{changeTable:e.changeTable}})],1)},r=[]},a866:function(e,t,a){"use strict";a("31e6")},d1d4:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=a("f289");t.default={data:function(){return{dialogVisible:!1,title:"",form:{Display_Name:"",Code:"",Prefix:"",Is_Year:!0,Is_Month:!0,Is_Day:!0,Year_Type:"4",Sequence_Length:"",Remark:"",Is_Working_Object_Unique:!0},rules:{Display_Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Code:[{required:!0,message:"请输入编号",trigger:"blur"}],Prefix:[{required:!0,message:"请输入单号前缀",trigger:"blur"}],Sequence_Length:[{required:!0,message:"请输入流水号长度",trigger:"blur"}]}}},methods:{dialogOpen:function(e,t){this.dialogVisible=!0,this.title=e,"edit"===e&&(this.form=Object.assign({},t))},restFrom:function(){this.form={Display_Name:"",Code:"",Prefix:"",Is_Year:!0,Is_Month:!0,Is_Day:!0,Year_Type:"4",Sequence_Length:"",Remark:""}},handleClose:function(){this.restFrom(),this.dialogVisible=!1},changeYearType:function(){this.$set(this.form,"Year_Type","4")},dialogSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return e.$message({type:"error",message:"请填完表单在提交"}),!1;!1===e.form.Is_Year&&(e.form.Year_Type=0),(0,l.SaveBillSetup)(e.form).then((function(t){!0===t.IsSucceed?(e.$message({type:"success",message:"编辑成功"}),e.$emit("changeTable"),e.restFrom(),e.dialogVisible=!1):e.$message({type:"error",message:t.Message})}))}))}}}},ef35:function(e,t,a){"use strict";a.r(t);var l=a("d1d4"),r=a.n(l);for(var o in l)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(o);t["default"]=r.a},f289:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetBillSetupEntity=n,t.GetBillSetupList=s,t.GetBillSetupPageList=i,t.RecoveryBillSetup=u,t.SaveBillSetup=o;var r=l(a("b775"));function o(e){return(0,r.default)({method:"post",url:"/SYS/BillSetup/SaveBillSetup",data:e})}function n(e){return(0,r.default)({method:"post",url:"/SYS/BillSetup/GetBillSetupEntity",data:e})}function i(e){return(0,r.default)({method:"post",url:"/SYS​/BillSetup​/GetBillSetupPageList",data:e})}function s(e){return(0,r.default)({method:"post",url:"/SYS/BillSetup/GetBillSetupList",data:e})}function u(e){return(0,r.default)({method:"post",url:"/SYS/BillSetup/RecoveryBillSetup",data:e})}}}]);