(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-f400f8b8"],{"2a2c":function(e,t,o){"use strict";o.d(t,"a",(function(){return n})),o.d(t,"b",(function(){return s}));var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container abs100"},[o("el-card",{staticClass:"box-card h100"},[o("div",{staticClass:"topButton"},[o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleOpen()}}},[e._v("新增")])],1),o("el-table",{staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{stripe:"",data:e.tableData,border:""},scopedSlots:e._u([{key:"empty",fn:function(){return[o("ElTableEmpty")]},proxy:!0}])},[o("el-table-column",{attrs:{sortable:"",prop:"Name",label:"工艺名称"}}),o("el-table-column",{attrs:{sortable:"",prop:"Code",label:"工艺代号"}}),o("el-table-column",{attrs:{sortable:"",prop:"WorkName",label:"工序流程"}}),o("el-table-column",{attrs:{sortable:"",prop:"Component_type",label:"构件类型"}}),o("el-table-column",{attrs:{sortable:"",prop:"Component_Type_Codes",label:"构件类型代号"}}),o("el-table-column",{attrs:{sortable:"",prop:"address",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{type:"text"},on:{click:function(o){return e.handleOpen("edit",t.row)}}},[e._v("编辑")]),o("el-button",{staticClass:"btn-del txt-red",attrs:{type:"text",size:"small"},on:{click:function(o){return e.handleDel(t.row.Id)}}},[e._v("删除")])]}}])})],1)],1),o("Dialog",{ref:"Dialog",on:{refresh:e.fetchData}})],1)},s=[]},"3e2f":function(e,t,o){"use strict";o.r(t);var n=o("ce22"),s=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(r);t["default"]=s.a},"549d":function(e,t,o){},"5c40":function(e,t,o){"use strict";o("549d")},7257:function(e,t,o){"use strict";o.r(t);var n=o("d6d1"),s=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(r);t["default"]=s.a},9979:function(e,t,o){"use strict";o.r(t);var n=o("a312"),s=o("3e2f");for(var r in s)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return s[e]}))}(r);var a=o("2877"),l=Object(a["a"])(s["default"],n["a"],n["b"],!1,null,"25510d4a",null);t["default"]=l.exports},a024:function(e,t,o){"use strict";var n=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=x,t.AddTechnology=i,t.AddWorkingProcess=l,t.DelLib=S,t.DeleteProcess=L,t.DeleteProcessFlow=C,t.DeleteTechnology=_,t.DeleteWorkingTeams=G,t.GetAllProcessList=f,t.GetCheckGroupList=W,t.GetChildComponentTypeList=$,t.GetFactoryAllProcessList=p,t.GetFactoryPeoplelist=D,t.GetFactoryWorkingTeam=g,t.GetGroupItemsList=P,t.GetLibList=a,t.GetLibListType=B,t.GetProcessFlow=m,t.GetProcessFlowListWithTechnology=h,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=I,t.GetProcessListWithUserBase=R,t.GetProcessWorkingTeamBase=N,t.GetTeamListByUser=A,t.GetTeamProcessList=T,t.GetWorkingTeam=y,t.GetWorkingTeamBase=k,t.GetWorkingTeamInfo=O,t.GetWorkingTeams=v,t.GetWorkingTeamsPageList=F,t.SaveWorkingTeams=w,t.UpdateProcessTeam=b;var s=n(o("b775")),r=n(o("4328"));function a(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function l(e){return(0,s.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function i(e){return(0,s.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,s.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function p(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function m(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function h(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function b(e){return(0,s.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function g(){return(0,s.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function T(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function P(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function C(e){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function _(e){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function L(e){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function v(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function F(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function w(e){return(0,s.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function G(e){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function O(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function k(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function I(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function R(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function D(e){return(0,s.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function W(e){return(0,s.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function x(e){return(0,s.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function $(e){return(0,s.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function S(e){return(0,s.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function B(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function N(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function A(e){return(0,s.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a312:function(e,t,o){"use strict";o.d(t,"a",(function(){return n})),o.d(t,"b",(function(){return s}));var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-dialog",{attrs:{title:"add"==e.type?"新增":"编辑",visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[o("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px"}},[o("el-form-item",{attrs:{label:"工艺名称",prop:"Name"}},[o("el-input",{model:{value:e.ruleForm.Name,callback:function(t){e.$set(e.ruleForm,"Name",t)},expression:"ruleForm.Name"}})],1),o("el-form-item",{attrs:{label:"工艺代号",prop:"Code"}},[o("el-input",{model:{value:e.ruleForm.Code,callback:function(t){e.$set(e.ruleForm,"Code",t)},expression:"ruleForm.Code"}})],1),o("el-form-item",{attrs:{prop:""}},[o("template",{slot:"label"},[o("span",{staticStyle:{color:"#fb6b7f","margin-right":"4px"}},[e._v("*")]),e._v("工艺流程 ")]),o("el-button",{attrs:{disabled:e.ProcessFlow.length>=e.options.length,type:"primary"},on:{click:e.addTableData}},[e._v("新增")])],2),o("el-form-item",{attrs:{label:"",prop:""}},[o("el-table",{staticStyle:{width:"100%"},attrs:{data:e.ProcessFlow,border:""}},[o("el-table-column",{attrs:{prop:"",label:"工序流程",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},on:{change:e.processChange},model:{value:t.row.Process_Id,callback:function(o){e.$set(t.row,"Process_Id",o)},expression:"scope.row.Process_Id"}},e._l(e.options,(function(e,t){return o("el-option",{key:t,attrs:{label:e.Name,value:e.Id,disabled:e.disabled}})})),1)]}}])}),o("el-table-column",{attrs:{prop:"address",label:"操作",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,s=t.$index;return[o("el-button",{attrs:{type:"text",icon:"el-icon-top",disabled:0==s},on:{click:function(t){return e.moveUpward(n,s)}}}),o("el-button",{attrs:{type:"text",icon:"el-icon-bottom",disabled:s==e.ProcessFlow.length-1},on:{click:function(t){return e.moveDown(n,s)}}}),o("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",icon:"el-icon-delete"},nativeOn:{click:function(t){return t.preventDefault(),e.deleteRow(s,e.ProcessFlow)}}})]}}])})],1)],1),o("el-form-item",{attrs:{label:"构件类型",prop:"Component_Type_Ids"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择构件类型"},on:{change:e.ctChange},model:{value:e.ruleForm.Component_Type_Ids,callback:function(t){e.$set(e.ruleForm,"Component_Type_Ids",t)},expression:"ruleForm.Component_Type_Ids"}},e._l(e.ChildComponentList,(function(e,t){return o("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1),o("el-form-item",{attrs:{label:"构件类型代号",prop:"Component_Type_Codes"}},[o("el-input",{attrs:{disabled:""},model:{value:e.ruleForm.Component_Type_Codes,callback:function(t){e.$set(e.ruleForm,"Component_Type_Codes",t)},expression:"ruleForm.Component_Type_Codes"}})],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},s=[]},ce22:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("4de4"),o("caad"),o("d81d"),o("14d9"),o("a434"),o("e9f5"),o("910d"),o("7d54"),o("ab43"),o("e9c4"),o("b64b"),o("d3b7"),o("2532"),o("159b");var n=o("a024");t.default={components:{},data:function(){return{dialogVisible:!1,ProcessFlow:[],options:[],ChildComponentList:[],type:"add",ruleForm:{Name:"",Code:"",Component_Type_Codes:"",Component_Type_Ids:[]},rules:{Name:[{required:!0,message:"请输入工艺名称",trigger:"blur"}],Code:[{required:!0,message:"请输入工艺代号",trigger:"blur"}],Component_Type_Ids:[{required:!0,message:"请选择构件类型",trigger:"change"}]}}},created:function(){},mounted:function(){},methods:{handleOpen:function(e,t){var o=this;this.type=e,this.dialogVisible=!0,(0,n.GetProcessListBase)({Type:1}).then((function(e){if(e.IsSucceed){var t=e.Data&&e.Data.map((function(e){return e.disabled=!1,e}));o.$set(o,"options",t)}})),(0,n.GetChildComponentTypeList)({IsTechnology:!0,TechnologyId:null===t||void 0===t?void 0:t.Id}).then((function(e){e.IsSucceed&&o.$set(o,"ChildComponentList",e.Data)})),"edit"==e&&(this.ruleForm=Object.assign({},t),(0,n.GetProcessFlow)({technologyId:t.Id}).then((function(e){e.IsSucceed&&(o.ProcessFlow=e.Data)})))},processChange:function(e){var t=this.ProcessFlow.map((function(e){return e.Process_Id}));this.options.forEach((function(e,o){e.disabled=t.includes(e.Id)}))},submit:function(){var e=this,t=this.ruleForm,o=t.Name,s=t.Code,r=t.Id,a=t.Component_Type_Codes,l=t.Component_Type_Ids,i=a.split(";").filter((function(e){return""!=e})),u=!1,c=!1,d={Id:r,Name:o,Code:s,Component_Type_Codes:i,Component_Type_Ids:l,Type:1};if(0!=this.ProcessFlow.length){var f=JSON.parse(JSON.stringify(this.ProcessFlow));f.map((function(e,t){e.Step=t+1,""==e.Process_Id&&(u=!0),f.length>1&&t>0&&e.Process_Id==f[t-1].Process_Id&&(c=!0)})),this.$refs.ruleForm.validate((function(t){if(!t)return!1;u?e.$message.error("请选择工艺流程后再提交"):c?e.$message.error("不能添加连续的相同工艺流程"):(0,n.AddProessLib)({TechnologyLib:d,ProcessFlow:f}).then((function(t){t.IsSucceed?(e.$message.success("保存成功"),e.$emit("refresh"),e.handleClose()):e.$message.error(t.Message)}))}))}else this.$message.error("请添加工艺流程后再提交")},handleClose:function(){this.ProcessFlow=[],this.ruleForm={Name:"",Code:"",Component_Type_Codes:"",Component_Type_Ids:[]},this.$refs.ruleForm.resetFields(),this.dialogVisible=!1},addTableData:function(){this.ProcessFlow.push({Process_Id:""})},deleteRow:function(e,t){t.splice(e,1),this.processChange()},moveUpward:function(e,t){var o=this.ProcessFlow[t-1];this.ProcessFlow.splice(t-1,1),this.ProcessFlow.splice(t,0,o)},moveDown:function(e,t){var o=this.ProcessFlow[t+1];this.ProcessFlow.splice(t+1,1),this.ProcessFlow.splice(t,0,o)},ctChange:function(){var e=this;this.ruleForm.Component_Type_Codes="",this.ruleForm.Component_Type_Ids.map((function(t){e.ChildComponentList.map((function(o){t==o.Id&&(e.ruleForm.Component_Type_Codes+="".concat(o.Code,";"))}))}))}}}},d6d1:function(e,t,o){"use strict";var n=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("d81d"),o("e9f5"),o("ab43"),o("d3b7"),o("25f0");var s=n(o("9979")),r=o("a024"),a=n(o("21f5"));t.default={components:{Dialog:s.default,ElTableEmpty:a.default},data:function(){return{tableData:[]}},created:function(){this.fetchData()},mounted:function(){},methods:{handleOpen:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"add",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.$refs.Dialog.handleOpen(e,t)},fetchData:function(){var e=this;(0,r.GetLibList)({}).then((function(t){t.IsSucceed?(e.tableData=t.Data,e.tableData.map((function(e){e.Component_Type_Codes=e.Component_Type_Codes.toString()}))):e.$message({message:t.Message,type:"error"})}))},handleDel:function(e){var t=this;this.$confirm("此操作将删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,r.DelLib)({Id:e}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData()):t.$message({type:"error",message:e.Message})}))})).catch((function(){}))}}}},dbe4:function(e,t,o){"use strict";o.r(t);var n=o("2a2c"),s=o("7257");for(var r in s)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return s[e]}))}(r);o("5c40");var a=o("2877"),l=Object(a["a"])(s["default"],n["a"],n["b"],!1,null,"2ce728e2",null);t["default"]=l.exports}}]);