(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-677a155c"],{"15fd":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,a("a4d3");var r=i(a("ccb5"));function i(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(null==e)return{};var a,i,n=(0,r.default)(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],-1===t.indexOf(a)&&{}.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}},"337b":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530")),n=r(a("15fd")),o=r(a("c14f")),s=r(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("13d5"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("dca8"),a("d3b7"),a("ac1f"),a("00b4"),a("2532"),a("159b");var l=a("be22"),c=a("ed08"),u=a("7757"),d=a("cf45"),f=r(a("6612")),p=r(a("c1df")),_=a("64b9"),m=["PaymentList"],g=["_X_ROW_KEY","Remark"],y=["isOut","Input_Tax_Rate","Accounting_Price"];t.default={name:"PROExpenseAccountingDetail",data:function(){var e=this;return{backendDate:null,pickerOptions:{disabledDate:function(t){return t.getTime()<=new Date(e.backendDate).getTime()}},pageType:"",disabledDepart:!1,factoryDisabled:!1,pageLoading:!1,btnLoading:!1,factoryOption:[],departsOption:[],departsOptionNoFilter:[],belongDepartsOption:[],feeTypeOption:[],projectOption:[],tableData:[],totalPrice:0,form:{isOut:!1,Factory_Id:"",Receipt_No:"",Register_Depart_Id:"",Fee_Type:"",Sys_Project_Id:"",Is_Pay_On_Behalf:!1,Accounting_Price:void 0,Belongs_Depart_Id:"",Invoice_Type:null,Amortization_Period:null,Accounting_Date:"",Abstract:"",Amortization_Days_Count:void 0,Input_Tax_Rate:void 0,Belongs_Fee_Type:"",Contract_No:"",Payee:""},showBelongsType:!1,rules:{Factory_Id:[{required:!0,message:"请选择",trigger:"change"}],Register_Depart_Id:[{required:!0,message:"请选择",trigger:"change"}],Fee_Type:[{required:!0,message:"请选择",trigger:"change"}],Is_Pay_On_Behalf:[{required:!0,message:"请选择",trigger:"change"}],Accounting_Price:[{required:!0,message:"请输入正确数字",trigger:"blur"}],Belongs_Depart_Id:[{required:!0,message:"请选择",trigger:"change"}],isOut:[{required:!0,message:"请选择",trigger:"change"}],Amortization_Days_Count:[{required:!0,message:"请输入正确数字",trigger:"blur"}],Input_Tax_Rate:[{required:!0,message:"请输入正确数字",trigger:"blur"}],Payee:[{required:!0,message:"请选择",trigger:"blur"}]},validRules:{Pay_Date:[{required:!0,message:"支付日期必须填写"}],Pay_Price:[{required:!0,message:"支付金额必须填写"}],Pay_Depart_Id:[{required:!0,message:"录入部门必须填写"}]}}},computed:{isView:function(){return["view",""].includes(this.$route.query.type)},isEdit:function(){return!this.$route.query.c&&"edit"===this.$route.query.type},isAdd:function(){return"add"===this.$route.query.type},isConfirm:function(){return!!this.$route.query.c||"confirm"===this.$route.query.type},isRegistration:function(){return"reg"===this.$route.query.type},entryOption:function(){var e=this;return this.departsOption.filter((function(t){return[e.form.Register_Depart_Id,e.form.Belongs_Depart_Id].includes(t.Id)}))},curTaxRate:{get:function(){return this.form.Input_Tax_Rate},set:function(e){/00+\./g.test(e)&&(e="0"),/\.0*$/g.test(e)?this.form.Input_Tax_Rate=e:this.form.Input_Tax_Rate=Number(e)>100?100:+(0,f.default)(e).format("0.[000000]")}},curDaysCount:{get:function(){return+(0,f.default)(this.form.Amortization_Days_Count).format("0")},set:function(e){this.form.Amortization_Days_Count=e}},accountDate:{get:function(){return this.form.Accounting_Date},set:function(e){this.form.Accounting_Date=e}}},watch:{"form.Belongs_Depart_Id":function(e,t){this.belongChange(e)}},mounted:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getPermission();case 1:e.tb=[],e.getOptions(),e.isAdd||e.getFeeInfo(),e.isConfirm&&(e.rules.Belongs_Fee_Type=[{required:!0,message:"请选择",trigger:"change"}]);case 2:return t.a(2)}}),t)})))()},methods:{getTimeData:function(e){var t=this;return(0,s.default)((0,o.default)().m((function a(){var r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,u.GetOMALatestAccountingDate)({FactoryId:e});case 1:r=a.v,r.IsSucceed?t.backendDate=r.Data||"":t.message.error(r.Mesaage);case 2:return a.a(2)}}),a)})))()},getPermission:function(){var e=this;return new Promise((function(t,a){e.permissionList=[],(0,l.GetUserDepartTypePermission)({}).then((function(r){r.IsSucceed?(e.permissionList=r.Data,t()):(e.$message({message:r.Message,type:"error"}),a())}))}))},getFeeInfo:function(){var e=this;this.pageLoading=!0,(0,l.GetFeeEntity)({Id:this.$route.query.id}).then((function(t){if(t.IsSucceed){var a=t.Data,r=a.PaymentList,i=(0,n.default)(a,m);for(var o in i.Belongs_Fee_Type?i.Belongs_Fee_Type=parseInt(i.Belongs_Fee_Type):i.Belongs_Fee_Type="",e.form)e.form[o]=i[o];e.form.Id=i.Id,r.forEach((function(t,a){var r=t.Pay_Date,i=t.Pay_Price,n=t.Pay_Depart_Id,o=t.Remark,s=t.Id;e.tableData.push({Pay_Date:r,Pay_Price:i,Pay_Depart_Id:n,Remark:o,Id:s}),e.tb.push({Pay_Date:r,Pay_Price:i,Pay_Depart_Id:n,Remark:o,Id:s})}));var s=e.tableData.reduce((function(e,t){return e+t.Pay_Price}),0);e.totalPrice=(0,f.default)(s).format("0,0.[00]"),e.form.isOut=e.form.Belongs_Depart_Id!==e.form.Register_Depart_Id,e.factoryChange(e.form.Factory_Id),e.getTimeData(e.form.Factory_Id)}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.pageLoading=!1}))},getOptions:function(){var e,t=this;e=this.isView?u.GetQueryNonExternalFactory:u.GetNonExternalFactory,e({}).then((function(e){if(e.IsSucceed){t.factoryOption=Object.freeze(e.Data||[]);var a=t.factoryOption.find((function(e){return e.Is_Cur_User_Factory}));a&&t.factoryOption.length&&t.isAdd&&(t.form.Factory_Id=a.Id,t.factoryChange(t.form.Factory_Id),t.getTimeData(t.form.Factory_Id)),t.factoryDisabled=(t.isAdd||t.isEdit)&&a&&0===t.permissionList.length}else t.$message({message:e.Message,type:"error"})})),(0,d.getDictionary)("FeeType").then((function(e){var a;t.feeTypeOption=e,null!==(a=t.feeTypeOption)&&void 0!==a&&a.length&&t.isAdd&&(t.form.Fee_Type=t.feeTypeOption[0].Id)})),this.isAdd&&(this.form.Accounting_Date=(0,p.default)().format("YYYY-MM-DD"))},getDeparts:function(e){var t=this;(0,u.GetFirstLevelDepartsUnderFactory)({FactoryId:e}).then((function(e){if(e.IsSucceed){var a=e.Data;if(t.departsOptionNoFilter=a,t.permissionList.length){var r=a.filter((function(e){return t.permissionList.includes(e.Type)||e.Is_Cur_User_Depart}));t.isView||t.isConfirm?t.departsOption=a:t.departsOption=r}else t.departsOption=a;var i=t.departsOption.find((function(e){return e.Is_Cur_User_Depart}));i?t.isAdd&&(t.form.Register_Depart_Id=i.Id):t.isAdd&&(t.form.Register_Depart_Id=""),t.disabledDepart=(t.isAdd||t.isEdit)&&i&&0===t.permissionList.length,t.changeRegisterId()}else t.$message({message:e.Message,type:"error"})})),(0,u.GetFactoryProjectList)({FactoryId:e}).then((function(e){e.IsSucceed?t.projectOption=Object.freeze(e.Data.map((function(e){return e.Status||e.Type?e.label="".concat(e.Short_Name,"(").concat(e.Status&&e.Type?"".concat(e.Status,"/").concat(e.Type):"".concat(e.Status||e.Type),")"):e.label=e.Short_Name,e}))):t.$message({message:e.Message,type:"error"})}))},factoryChange:function(e){this.getDeparts(e)},priceChange:function(){var e=this.tableData.reduce((function(e,t){return e+t.Pay_Price}),0);this.totalPrice=(0,f.default)(e).format("0,0.[00]")},changeRegisterId:function(){var e=this;this.belongDepartsOption=this.departsOptionNoFilter.map((function(t){return t.disabled=t.Id===e.form.Register_Depart_Id,t})),this.form.isOut?this.form.Belongs_Depart_Id===this.form.Register_Depart_Id&&(this.form.Belongs_Depart_Id=""):this.outChange(!1),this.belongChange(this.form.Belongs_Depart_Id),this.tableData.forEach((function(t){t.Pay_Depart_Id=e.form.Register_Depart_Id}))},outChange:function(e){var t=this;if(e)this.form.Belongs_Depart_Id===this.form.Register_Depart_Id&&(this.form.Belongs_Depart_Id=""),this.form.Accounting_Date="";else{var a=this.belongDepartsOption.find((function(e){return e.Id===t.form.Register_Depart_Id}));a&&(this.form.Belongs_Depart_Id=a.Id)}},belongChange:function(e){var t;if(this.departsOption.length){var a="营销核算"===(null===(t=this.departsOption.find((function(t){return t.Id===e})))||void 0===t?void 0:t.Type);a?this.showBelongsType=!0:(this.showBelongsType=!1,this.form.Belongs_Fee_Type="")}},handleSubmit:function(){var e=this,t=function(e){for(var t in e)return""===e[t]};this.$refs["form"].validate((function(a){if(!a)return!1;for(var r=(0,c.deepClone)(e.tableData),o=0;o<r.length;o++){var s,u=r[o],d=(u._X_ROW_KEY,u.Remark,(0,n.default)(u,g));null===(s=r[o])||void 0===s||delete s._X_ROW_KEY;var f=t(d);if(f)return e.$message({message:"付款信息有必填项未填写，请完善后再次提交",type:"warning"}),!1}var p=e.form,m=(p.isOut,p.Input_Tax_Rate),b=p.Accounting_Price,h=(0,n.default)(p,y),v=(0,i.default)((0,i.default)({},h),{},{Accounting_Price:Number(b),Input_Tax_Rate:Number(m),PaymentList:r});e.btnLoading=!0;var D=null;e.isAdd?D=l.SaveNewFee:e.isConfirm?D=l.ConfirmFee:e.isEdit?D=l.UpdateFee:e.isRegistration&&(D=l.UpdatePaymentList),D(v).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),_.EventBus.$emit("refresh"),e.toBack(!1)):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))}))},editRowEvent:function(e){var t=this.$refs.xTable;t.setActiveRow(e)},handleDeleteRow:function(e){var t=this;this.$confirm("是否删除该条数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.tableData.splice(e,1),t.tb.splice(e,1),t.priceChange()})).catch((function(){t.$message({type:"info",message:"已取消"})}))},insertEvent:function(){var e=this;this.tableData.push({Pay_Date:"",Pay_Price:"",Pay_Depart_Id:this.form.Register_Depart_Id,Remark:""}),this.tb.push({Pay_Date:"",Pay_Price:"",Pay_Depart_Id:this.form.Register_Depart_Id,Remark:""}),this.$nextTick((function(t){e.$refs["btn".concat(e.tableData.length-1)].$el.click()}))},saveRowEvent:function(e,t){var a=this,r=this.$refs.xTable;r.clearActived().then((function(){a.loading=!0,setTimeout((function(){Object.assign(a.tb[t],a.tableData[t]),a.loading=!1}),300)}))},cancelRowEvent:function(e,t){Object.assign(this.tableData[t],this.tb[t]);var a=this.$refs.xTable;a.clearActived()},toBack:function(e){var t=this;this.isView?(0,c.closeTagView)(this.$store,this.$route):e?this.$confirm("此操作不会保存编辑数据，是否退出？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.closeTagView)(t.$store,t.$route)})).catch((function(){t.$message({type:"info",message:"已取消"})})):(0,c.closeTagView)(this.$store,this.$route)},getProLabel:function(e){return"".concat(e.Short_Name,"（").concat(e.Status,"/").concat(e.FeedingMethod,"）")},getPayDepart:function(e,t){var a=this.entryOption.find((function(e){return e.Id===t}));return a?a.Display_Name:(e.Pay_Depart_Id="","")}}}},"7b78":function(e,t,a){},9689:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"cs-z-page-main-content"},[a("div",{staticClass:"btn-x"},[a("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:function(t){return e.toBack(!0)}}},[e._v("返 回 ")])],1),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"单据编号",prop:"Receipt_No"}},[a("el-input",{attrs:{disabled:"",placeholder:""},model:{value:e.form.Receipt_No,callback:function(t){e.$set(e.form,"Receipt_No",t)},expression:"form.Receipt_No"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"归属基地",prop:"Factory_Id"}},[a("el-select",{staticClass:"w100",attrs:{disabled:e.isView||e.isConfirm||e.isRegistration||e.factoryDisabled,placeholder:"请选择"},on:{change:e.factoryChange},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"登记部门",prop:"Register_Depart_Id"}},[a("el-select",{staticClass:"w100",attrs:{filterable:"",disabled:!e.form.Factory_Id||e.isView||e.isConfirm||e.isRegistration||e.disabledDepart,placeholder:"请选择"},on:{change:e.changeRegisterId},model:{value:e.form.Register_Depart_Id,callback:function(t){e.$set(e.form,"Register_Depart_Id",t)},expression:"form.Register_Depart_Id"}},e._l(e.departsOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"费用科目",prop:"Fee_Type"}},[a("el-select",{staticClass:"w100",attrs:{filterable:"",disabled:e.isView||e.isConfirm||e.isRegistration,placeholder:"请选择"},model:{value:e.form.Fee_Type,callback:function(t){e.$set(e.form,"Fee_Type",t)},expression:"form.Fee_Type"}},e._l(e.feeTypeOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"归属项目",prop:"Sys_Project_Id"}},[a("el-select",{staticClass:"w100",attrs:{filterable:"",disabled:!e.form.Factory_Id||e.isView||e.isConfirm||e.isRegistration,placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},[a("el-option",{attrs:{label:"无项目",value:""}}),e._l(e.projectOption,(function(t){return a("el-option",{key:t.Sys_Project_Id,attrs:{label:e.getProLabel(t),value:t.Sys_Project_Id}})}))],2)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否代付费用",prop:"Is_Pay_On_Behalf"}},[a("el-select",{staticClass:"w100",staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isConfirm||e.isRegistration,placeholder:"请选择"},model:{value:e.form.Is_Pay_On_Behalf,callback:function(t){e.$set(e.form,"Is_Pay_On_Behalf",t)},expression:"form.Is_Pay_On_Behalf"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1)],1),a("el-col",{staticClass:"cs-col",attrs:{span:12}},[a("el-form-item",{attrs:{label:"核算金额(元)",prop:"Accounting_Price"}},[[a("el-input",{attrs:{step:"any",type:"number",disabled:e.isView||e.isConfirm||e.isRegistration},model:{value:e.form.Accounting_Price,callback:function(t){e.$set(e.form,"Accounting_Price",t)},expression:"form.Accounting_Price"}},[a("template",{slot:"append"},[e._v("元")])],2)]],2)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否调出"}},[a("el-select",{staticClass:"w100",attrs:{disabled:e.isView||e.isConfirm||e.isRegistration,placeholder:"请选择"},on:{change:e.outChange},model:{value:e.form.isOut,callback:function(t){e.$set(e.form,"isOut",t)},expression:"form.isOut"}},[a("el-option",{attrs:{label:"否",value:!1}}),a("el-option",{attrs:{label:"是",value:!0}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"归属部门",prop:"Belongs_Depart_Id"}},[a("el-select",{staticClass:"w100",attrs:{filterable:"",disabled:!e.form.Factory_Id||e.isView||!e.form.isOut||e.isConfirm||e.isRegistration,placeholder:"请选择"},model:{value:e.form.Belongs_Depart_Id,callback:function(t){e.$set(e.form,"Belongs_Depart_Id",t)},expression:"form.Belongs_Depart_Id"}},e._l(e.belongDepartsOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,disabled:e.disabled,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"票据类型",prop:"Invoice_Type"}},[a("el-select",{staticClass:"w100",attrs:{disabled:e.isView||e.isRegistration||e.isConfirm,placeholder:"请选择"},model:{value:e.form.Invoice_Type,callback:function(t){e.$set(e.form,"Invoice_Type",t)},expression:"form.Invoice_Type"}},[a("el-option",{attrs:{label:"无发票",value:null}}),a("el-option",{attrs:{label:"增值税专用发票",value:1}}),a("el-option",{attrs:{label:"增值税普通发票",value:2}})],1)],1)],1),1===e.form.Invoice_Type?a("el-col",{staticClass:"cs-col",attrs:{span:12}},[a("el-form-item",{attrs:{label:"税率",prop:"Input_Tax_Rate"}},[a("el-input",{attrs:{step:"any",min:0,max:100,type:"number",disabled:e.isView||e.isRegistration||e.isConfirm},model:{value:e.curTaxRate,callback:function(t){e.curTaxRate=t},expression:"curTaxRate"}},[a("template",{slot:"append"},[e._v("%")])],2)],1)],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否按周期摊销",prop:"Amortization_Period"}},[a("el-select",{staticClass:"w100",attrs:{disabled:e.isView||e.isRegistration,placeholder:"请选择"},model:{value:e.form.Amortization_Period,callback:function(t){e.$set(e.form,"Amortization_Period",t)},expression:"form.Amortization_Period"}},[a("el-option",{attrs:{label:"否",value:null}}),a("el-option",{attrs:{label:"自然月摊销",value:1}}),a("el-option",{attrs:{label:"自定义天数",value:2}})],1)],1)],1),2===e.form.Amortization_Period?a("el-col",{staticClass:"cs-col",attrs:{span:12}},[a("el-form-item",{attrs:{label:"摊销天数",prop:"Amortization_Days_Count"}},[a("el-input",{attrs:{min:0,step:"any",type:"number",disabled:e.isView||e.isRegistration},model:{value:e.curDaysCount,callback:function(t){e.curDaysCount=e._n(t)},expression:"curDaysCount"}},[a("template",{slot:"append"},[e._v("天")])],2)],1)],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"核算开始日期",prop:"Accounting_Date"}},[a("el-date-picker",{staticClass:"w100",attrs:{disabled:e.isView||e.form.isOut&&!e.isConfirm||e.isRegistration,"value-format":"yyyy-MM-dd",clearable:!1,"picker-options":e.pickerOptions,type:"date"},model:{value:e.accountDate,callback:function(t){e.accountDate=t},expression:"accountDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"摘要说明",prop:"Abstract"}},[a("el-input",{attrs:{type:"textarea","show-word-limit":"",maxlength:200,disabled:e.isView||e.isRegistration},model:{value:e.form.Abstract,callback:function(t){e.$set(e.form,"Abstract",t)},expression:"form.Abstract"}})],1)],1),a("el-col",{attrs:{span:12}},[e.showBelongsType?a("el-form-item",{attrs:{label:"归属成本/费用",prop:"Belongs_Fee_Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.form.isOut&&!e.isConfirm||e.isRegistration,placeholder:""},model:{value:e.form.Belongs_Fee_Type,callback:function(t){e.$set(e.form,"Belongs_Fee_Type",t)},expression:"form.Belongs_Fee_Type"}},[a("el-option",{attrs:{label:"管理费用",value:1}}),a("el-option",{attrs:{label:"安装劳务成本",value:2}})],1)],1):e._e()],1)],1),a("el-divider"),a("div",{staticClass:"info-x"},[e._v(" 付款信息：累计付款"),a("strong",{staticClass:"red"},[e._v(" "+e._s(e.totalPrice)+" ")]),e._v("元 ")]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同编号",prop:"Contract_No"}},[a("el-input",{attrs:{disabled:e.isView||e.isConfirm},model:{value:e.form.Contract_No,callback:function(t){e.$set(e.form,"Contract_No",t)},expression:"form.Contract_No"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"收款方",prop:"Payee"}},[a("el-input",{attrs:{disabled:e.isView||e.isConfirm},model:{value:e.form.Payee,callback:function(t){e.$set(e.form,"Payee",t)},expression:"form.Payee"}})],1)],1)],1)],1),e.isAdd||e.isEdit||e.isRegistration?a("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:e.insertEvent}},[e._v("新增付款信息")])]},proxy:!0}],null,!1,3034553341)}):e._e(),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",stripe:"",align:"left",height:"300",resizable:"","show-overflow":"","edit-rules":e.validRules,data:e.tableData,"edit-config":{trigger:"manual",mode:"row"}}},[a("vxe-column",{attrs:{field:"Pay_Date",title:"支付日期","edit-render":{}},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(e._f("timeFormat")(r.Pay_Date)))])]}},{key:"edit",fn:function(t){var r=t.row;return[a("vxe-input",{attrs:{type:"date",placeholder:"请选择",transfer:""},model:{value:r.Pay_Date,callback:function(t){e.$set(r,"Pay_Date",t)},expression:"row.Pay_Date"}})]}}])}),a("vxe-column",{attrs:{field:"Pay_Price",title:"支付金额","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var r=t.row;return[a("vxe-input",{attrs:{type:"float",placeholder:"请输入"},on:{change:e.priceChange},model:{value:r.Pay_Price,callback:function(t){e.$set(r,"Pay_Price",t)},expression:"row.Pay_Price"}})]}}])}),a("vxe-column",{attrs:{field:"Pay_Depart_Id",title:"录入部门","edit-render":{}},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(e.getPayDepart(r,r.Pay_Depart_Id)))])]}},{key:"edit",fn:function(t){var r=t.row;return[a("vxe-select",{attrs:{transfer:"",placeholder:"请选择",disabled:e.form.Belongs_Depart_Id===e.form.Register_Depart_Id},model:{value:r.Pay_Depart_Id,callback:function(t){e.$set(r,"Pay_Depart_Id",t)},expression:"row.Pay_Depart_Id"}},e._l(e.entryOption,(function(e,t){return a("vxe-option",{key:t,attrs:{value:e.Id,label:e.Display_Name}})})),1)]}}])}),a("vxe-column",{attrs:{field:"Remark",title:"备注","edit-render":{},width:"400"},scopedSlots:e._u([{key:"edit",fn:function(t){var r=t.row;return[a("vxe-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:r.Remark,callback:function(t){e.$set(r,"Remark","string"===typeof t?t.trim():t)},expression:"row.Remark"}})]}}])}),e.isAdd||e.isEdit||e.isRegistration?a("vxe-column",{attrs:{title:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row,i=t.rowIndex;return[e.$refs.xTable.isActiveByRow(r)?[a("vxe-button",{attrs:{type:"text",status:"primary"},on:{click:function(t){return e.saveRowEvent(r,i)}}},[e._v("保存")]),a("vxe-button",{attrs:{type:"text",status:"danger"},on:{click:function(t){return e.cancelRowEvent(r,i)}}},[e._v("取消")])]:[a("vxe-button",{ref:"btn"+i,attrs:{type:"text",status:"primary"},on:{click:function(t){return e.editRowEvent(r,i)}}},[e._v("编辑")]),a("vxe-button",{attrs:{type:"text",status:"danger"},on:{click:function(t){return e.handleDeleteRow(i)}}},[e._v("删除")])]]}}],null,!1,919067965)}):e._e()],1)],1),e.isView?e._e():a("footer",[a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("提交")]),a("el-button",{on:{click:function(t){return e.toBack(!0)}}},[e._v("取消")])],1)],1)])},i=[]},a8be:function(e,t,a){"use strict";a("7b78")},ccb5:function(e,t,a){"use strict";function r(e,t){if(null==e)return{};var a={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;a[r]=e[r]}return a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},d6ff:function(e,t,a){"use strict";a.r(t);var r=a("337b"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},dda1:function(e,t,a){"use strict";a.r(t);var r=a("9689"),i=a("d6ff");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("a8be");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"7ef502dc",null);t["default"]=s.exports}}]);