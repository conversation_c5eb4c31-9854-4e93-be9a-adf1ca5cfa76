(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b6b6e3a0"],{"0283":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"c-upload",staticStyle:{"margin-top":"-16px"}},[n("div",{staticClass:"tbox",staticStyle:{"flex-direction":"column"}},[n("el-row",{staticStyle:{width:"100%"},attrs:{gutter:20}},[n("el-col",{attrs:{span:8}},[t._v(" 1.选择项目 ")]),n("el-col",{attrs:{span:16}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},on:{change:t.projSelectHandle},model:{value:t.formData.Project_Id,callback:function(e){t.$set(t.formData,"Project_Id",e)},expression:"formData.Project_Id"}},[n("el-option",{attrs:{label:"选择项目",value:""}}),t._l(t.projs,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})}))],2)],1)],1),n("el-row",{staticStyle:{width:"100%","margin-top":"16px"},attrs:{gutter:20}},[n("el-col",{attrs:{span:8}},[t._v(" 安装单元名称 ")]),n("el-col",{attrs:{span:16}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},model:{value:t.formData.InstallUnit_Id,callback:function(e){t.$set(t.formData,"InstallUnit_Id",e)},expression:"formData.InstallUnit_Id"}},[n("el-option",{attrs:{label:"选择安装单元",value:""}}),t._l(t.units,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})}))],2)],1)],1)],1),n("div",{staticClass:"tbox"},[n("p",[t._v("2.下载模板")]),n("el-button",{staticStyle:{border:"1px solid #D9DBE2",padding:"8px 20px"},attrs:{icon:"el-icon-document",type:"text"},on:{click:t.downTmpl}},[t._v(t._s(t.tmpl.title))])],1),n("div",{staticStyle:{background:"#F7F8F9",padding:"12px 20px",border:"1px solid #D9DBE2"}},[n("div",{staticStyle:{"margin-bottom":"16px"}},[t._v(" 3.上传文件 ")]),n("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:"",limit:1,action:t.action,headers:t.reqHeader,multiple:t.multiple,accept:t.exts.map((function(t){return"."+t})).join(","),"file-list":t.fileList,"auto-upload":!1,"before-upload":t.beforeUpload,"on-error":t.uploadError,"on-exceed":t.uploadExceed,"on-success":t.uploadSuccess,name:"files",data:t.formData},scopedSlots:t._u([{key:"file",fn:function(e){var a=e.file;return n("div",{},[n("div",{class:{"up-item":!0,error:"fail"===a.status}},[t.progresses[a.name]>0?n("div",{staticClass:"percent",style:{width:t.progresses[a.name]+"%"}}):t._e(),n("div",{staticClass:"bar"},[n("div",{staticClass:"title"},[n("svg-icon",{staticStyle:{height:"30px",width:"30px"},attrs:{name:t.extIcon(a),"icon-class":t.extIcon(a)}}),t._v(" "+t._s(a.name)+" ")],1),n("div",{staticClass:"remove"},[n("i",{staticClass:"el-icon-close",attrs:{title:"移除文件"},on:{click:function(e){return t.removeFile(a)}}})])])])])}}])},[n("svg-icon",{staticClass:"icon-svg",attrs:{name:"upload-icon","icon-class":"upload-icon",width:"200",height:"200"}}),n("div",{staticClass:"el-upload__text"},[t._v(" 将文件拖到此处，或"),n("em",[t._v("点击选择")]),n("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 支持格式："+t._s(t.exts.join("、"))+"，最大文件限制："+t._s(t.filesize)+"M ")])])],1)],1),n("div",{staticClass:"tbox",staticStyle:{"margin-top":"12px","flex-direction":"column","align-items":"flex-start"}},[n("div",{staticStyle:{"margin-bottom":"12px",wdith:"100%"}},[t._v(" 4.更新方式 ")]),n("el-row",{staticStyle:{width:"100%"},attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.formData.Import_Type,callback:function(e){t.$set(t.formData,"Import_Type",e)},expression:"formData.Import_Type"}},[n("el-option",{attrs:{label:"选择更新方式",value:""}}),n("el-option",{attrs:{label:"更新",value:2}}),n("el-option",{attrs:{label:"覆盖",value:1}})],1)],1),2===t.formData.Import_Type?[n("el-col",{attrs:{span:6}},[n("el-checkbox",{model:{value:t.formData.Is_Supplement,callback:function(e){t.$set(t.formData,"Is_Supplement",e)},expression:"formData.Is_Supplement"}},[t._v("补充")])],1),n("el-col",{attrs:{span:6}},[n("el-checkbox",{model:{value:t.formData.Is_Changed,callback:function(e){t.$set(t.formData,"Is_Changed",e)},expression:"formData.Is_Changed"}},[t._v("变更")])],1)]:t._e()],2)],1),t._e(),n("div",{staticStyle:{"text-align":"right","margin-top":"12px"}},[n("el-button",{attrs:{size:"mini",disabled:t.btnLoading},on:{click:t.cancel}},[t._v("取消")]),n("el-button",{attrs:{type:"success",size:"mini",loading:t.btnLoading},on:{click:t.beginUpload}},[t._v("导入")])],1)])},i=[]},"0a15":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("3796")),o=n("586a"),s=n("ed08");e.default={components:{UploadExcel:i.default},props:{handleClose:Function},data:function(){return{btnLoading:!1}},mounted:function(){},methods:{beforeUpload:function(t){var e=this,n=new FormData;n.append("files",t),this.btnLoading=!0,(0,o.ImportModelInfo)(n).then((function(t){if(t.IsSucceed)e.$message({message:"导入成功",type:"success"}),e.$emit("dialogFormSubmitSuccess",{type:"reload"});else if(e.$message({message:t.Message,type:"error"}),t.Data){var n=(0,s.combineURL)(e.$baseUrl,t.Data);window.open(n,"_blank")}e.handleClose(),e.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()}}}},"0de1":function(t,e,n){},1471:function(t,e,n){"use strict";n("22da")},1963:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100 flex-pd16-wrap"},[n("div",{staticClass:"page-main-content cs-z-shadow"},[n("el-container",{staticStyle:{height:"100%"}},[n("el-header",{staticClass:"art-header"},[n("el-button",{attrs:{type:"danger",size:"mini",disabled:t.checkedRows.length<=0},on:{click:function(e){return t.deleteRows(t.checkedRows)}}},[t._v("删除 ")]),n("div",[n("el-button",{attrs:{type:"success",size:"mini",loading:t.uploading},on:{click:function(e){return t.openDialog({title:"导入",width:"480px",component:"CUpload",props:{exts:["xls","xlsx"],tmpl:{title:"构件清单模板"},action:t.apis.ImportComponent,multiple:!1,size:1}})}}},[t._v(t._s(t.uploading?"导入中...":"导入")+" ")]),n("el-button",{attrs:{type:"primary",size:"mini",disabled:!t.checkedRows.length},on:{click:t.handleExport}},[t._v("导出 ")])],1)],1),n("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"art-main"},[n("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:"","cell-editor-blur-save-model":!1},on:{multiSelectedChange:t.multiSelectedChange,tableSearch:t.tableSearch,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange,cellEditorChanged:t.cellEditorChanged,rowEditModeChanged:t.rowEditModeChanged,columnSearchChange:t.columnSearchChange},scopedSlots:t._u([{key:"hsearch_Project_Name",fn:function(e){e.column;return[n("el-select",{staticStyle:{width:"96%"},attrs:{filterable:"",placeholder:"请选择",clearable:""},on:{focus:t.showSearch,change:function(e){return t.columnSearchChange({column:"Project_Name",value:e})}},model:{value:t.fiterArrObj["Project_Name"],callback:function(e){t.$set(t.fiterArrObj,"Project_Name",e)},expression:"fiterArrObj['Project_Name']"}},t._l(t.projects,(function(t,e){return n("el-option",{key:e,attrs:{label:t.Name,value:t.Name}})})),1)]}},{key:"hsearch_InstallUnit_Name",fn:function(e){e.column;return[n("el-select",{attrs:{disabled:!t.fiterArrObj["Project_Name"],clearable:"",placeholder:"请选择"},on:{change:t.installNameChange},model:{value:t.installName,callback:function(e){t.installName=e},expression:"installName"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.installOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"hsearch_Type_Name",fn:function(e){e.column;return[n("el-select",{staticStyle:{width:"96%"},attrs:{filterable:"",placeholder:"请选择",clearable:""},on:{focus:t.showSearch,change:function(e){return t.columnSearchChange({column:"Type_Name",value:e})}},model:{value:t.fiterArrObj["Type_Name"],callback:function(e){t.$set(t.fiterArrObj,"Type_Name",e)},expression:"fiterArrObj['Type_Name']"}},t._l(t.cmptTypes_1,(function(t,e){return n("el-option",{key:e,attrs:{label:t.Name,value:t.Name}})})),1)]}},{key:"op",fn:function(e){var a=e.row,i=e.$index;return[t.editingRows[i]?[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(e){return t.saveRow(a,i,t.editingFields)}}},[t._v("确定 ")]),t._v(" | "),n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(e){return t.cancelRowEdit(a,i,t.editingFields)}}},[t._v("取消 ")])]:[t._e(),n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(e){return t.handleBathEdit(a)}}},[t._v("编辑 ")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-link",{attrs:{type:"danger",underline:!1},on:{click:function(e){return t.deleteRows([a])}}},[t._v("删除 ")])]]}}])})],1)],1)],1),n("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[n("keep-alive",[t.dialogShow?n(t.dialogCfgs.component,t._b({ref:"content",tag:"component",attrs:{name:t.dialogCfgs.title},on:{dialogCancel:t.dialogCancel,startCheck:t.startCheck,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},i=[]},"22da":function(t,e,n){},"22f4":function(t,e,n){},2427:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n("586a");e.default={data:function(){return{btnLoading:!1,form:{Texture:"",Netweight:void 0,Length:void 0,Spec:"",Code:""}}},methods:{init:function(t){Object.assign(this.form,t)},onSubmit:function(){var t=this,e={Texture:this.form.Texture,Netweight:this.form.Netweight,Width:this.form.Width,Length:this.form.Length,Spec:this.form.Spec,InstallUnit_Id:this.form.InstallUnit_Id,Code:this.form.Code};this.btnLoading=!0,(0,a.BatchUpateCompProperty)(e).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.$emit("dialogFormSubmitSuccess",{type:"reload"}),t.$emit("dialogCancel")):t.$message({message:e.Message,type:"error"}),t.btnLoading=!1}))}}}},"2dd9":function(t,e,n){"use strict";function a(t,e){t||(t={}),e||(e=[]);var n=[],a=function(a){var i;i="[object Array]"===Object.prototype.toString.call(t[a])?t[a]:[t[a]];var o=i.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(o.filter((function(t){return null!==t})).length<=0&&(o=null),o){var s={Key:a,Value:o,Type:"",Filter_Type:""},l=e.find((function(t){return t.Code===a}));s.Type=null===l||void 0===l?void 0:l.Type,s.Filter_Type=null===l||void 0===l?void 0:l.Filter_Type,n.push(s)}};for(var i in t)a(i);return n}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=a,n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("d3b7"),n("25f0")},"2e8a":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteComponentType=u,e.GetCompTypeTree=d,e.GetComponentTypeEntity=c,e.GetComponentTypeList=s,e.GetFactoryCompTypeIndentifySetting=g,e.GetTableSettingList=p,e.GetTypePageList=l,e.RestoreTemplateType=_,e.SavDeepenTemplateSetting=v,e.SaveCompTypeIdentifySetting=b,e.SaveComponentType=r,e.SaveProBimComponentType=f,e.UpdateColumnSetting=h,e.UpdateComponentPartTableSetting=m;var i=a(n("b775")),o=a(n("4328"));function s(t){return(0,i.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:t})}function r(t){return(0,i.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:o.default.stringify(t)})}function u(t){return(0,i.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:o.default.stringify(t)})}function d(t){return(0,i.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:o.default.stringify(t)})}function f(t){return(0,i.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:t})}function m(t){return(0,i.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:t})}function h(t){return(0,i.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:t})}function g(t){return(0,i.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:t})}function b(t){return(0,i.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:t})}function v(t){return(0,i.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:t})}function _(t){return(0,i.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:t})}},"4e82":function(t,e,n){"use strict";var a=n("23e7"),i=n("e330"),o=n("59ed"),s=n("7b0b"),l=n("07fa"),r=n("083a"),c=n("577e"),u=n("d039"),d=n("addb"),f=n("a640"),p=n("3f7e"),m=n("99f4"),h=n("1212"),g=n("ea83"),b=[],v=i(b.sort),_=i(b.push),y=u((function(){b.sort(void 0)})),C=u((function(){b.sort(null)})),I=f("sort"),S=!u((function(){if(h)return h<70;if(!(p&&p>3)){if(m)return!0;if(g)return g<603;var t,e,n,a,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)b.push({k:e+a,v:n})}for(b.sort((function(t,e){return e.v-t.v})),a=0;a<b.length;a++)e=b[a].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),T=y||!C||!I||!S,O=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:c(e)>c(n)?1:-1}};a({target:"Array",proto:!0,forced:T},{sort:function(t){void 0!==t&&o(t);var e=s(this);if(S)return void 0===t?v(e):v(e,t);var n,a,i=[],c=l(e);for(a=0;a<c;a++)a in e&&_(i,e[a]);d(i,O(t)),n=l(i),a=0;while(a<n)e[a]=i[a++];while(a<c)r(e,a++);return e}})},"58b6":function(t,e,n){"use strict";n.r(e);var a=n("0a15"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"5f1ae":function(t,e,n){"use strict";n.r(e);var a=n("cc9e"),i=n("58b6");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("6eb2");var s=n("2877"),l=Object(s["a"])(i["default"],a["a"],a["b"],!1,null,"4e1d83d6",null);e["default"]=l.exports},"6b79":function(t,e,n){"use strict";n.r(e);var a=n("9ed6a"),i=n("a9f5");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var s=n("2877"),l=Object(s["a"])(i["default"],a["a"],a["b"],!1,null,"0594ecb2",null);e["default"]=l.exports},"6eb2":function(t,e,n){"use strict";n("e82c")},"8f8f":function(t,e,n){"use strict";n.r(e);var a=n("ece2"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"99d6":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("c14f")),o=a(n("1da1"));n("4de4"),n("a15b"),n("d81d"),n("b0c0"),n("e9f5"),n("910d"),n("ab43"),n("a9e3"),n("d3b7");var s=a(n("b775")),l=(a(n("4328")),a(n("4360"))),r=n("5f87"),c=n("ed08"),u=n("586a");e.default={name:"CUpload",components:{},props:{action:{type:String,default:""},exts:{type:Array,default:function(){return[]}},tmpl:{type:Object,default:function(){return{}}},multiple:{type:Boolean,default:!1},filesize:{type:Number,default:5}},data:function(){return{apis:{GetProjectList:"/PRO/Project/GetProjectList",GetInstallUnitList:"/PRO/InstallUnit/GetInstallUnitList",ProductionDataTemplate:"/PRO/Component/ProductionDataTemplate"},templateUrl:"",updateType:"Is_Supplement",completed:!0,projs:[],units:[],formData:{Project_Id:"",InstallUnit_Id:"",Is_Changed:!1,Is_Supplement:!0,Import_Type:2,Type:0},fileList:[],progresses:{},reqHeader:null,btnLoading:!1}},watch:{"formData.Import_Type":function(t){(2===t||1===t)&&(this.formData["Is_Changed"]=!1,this.formData["Is_Supplement"]=!1)}},created:function(){this.tmpl.templateUrl&&(this.templateUrl=this.tmpl.templateUrl),this.getTemplate(),this.loadProjects(),this.reqHeader={},l.default.getters.token&&(this.reqHeader["Authorization"]=(0,r.getToken)()),l.default.getters.Last_Working_Object_Id&&(this.reqHeader.Last_Working_Object_Id=l.default.getters.Last_Working_Object_Id)},methods:{getTemplate:function(){var t=this;(0,s.default)({url:this.apis.ProductionDataTemplate,method:"post",data:{}}).then((function(e){e.IsSucceed&&(t.templateUrl=(0,c.combineURL)(t.$baseUrl,e.Data.split("/").map((function(t){return encodeURIComponent(t)})).join("/")))}))},downTmpl:function(){this.templateUrl&&window.open(this.templateUrl,"_blank")},cancel:function(){this.$refs.upload.clearFiles(),this.fileList=[],this.$emit("dialogCancel")},confirmImport:function(){this.$emit("dialogFormSubmitSuccess",{type:"reload"})},beginUpload:function(){return this.formData.Project_Id?this.formData.InstallUnit_Id?this.formData.Import_Type?this.$refs["upload"].uploadFiles.length?(this.btnLoading=!0,void this.submit()):this.$message.warning("请选择文件"):this.$message.warning("选择更新方式"):this.$message.warning("选择安装单元"):this.$message.warning("选择项目")},uploadExceed:function(){this.$message({message:"每次只能上传一个文件，如果需要上传新的文件，请删除原有的文件后再次上传",type:"warning"})},beforeUpload:function(t){var e=t.name.split(".").pop(),n=!0;this.exts.length>0&&(n=this.exts.indexOf(e)>-1);var a=!0;return a=t.size/1024/1024<this.filesize,n||this.$message.error("上传文件格式错误!"),a||this.$message.error("上传文件大小不能超过 "+this.filesize+"MB!"),n&&a},uploadError:function(t,e,n){},uploadSuccess:function(t,e,n){},addUploadStatus:function(){var t=this;return new Promise((function(e){(0,u.AddDelayed)({operSource:1,installId:t.formData.InstallUnit_Id}).then((function(n){n.IsSucceed?(t.$emit("startCheck"),t.$notify.info({message:"正在处理中，请稍后查看...",duration:4e3}),e(!0)):(t.$message({message:n.Message,type:"error"}),e(!1))}))}))},submit:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return t.$refs.upload.submit(),e.n=1,t.addUploadStatus();case 1:t.$emit("dialogCancel");case 2:return e.a(2)}}),e)})))()},removeFile:function(t){this.fileList=this.fileList.filter((function(e){return e.name!==t.name}))},loadProjects:function(){var t=this;(0,s.default)({url:this.apis.GetProjectList,method:"post",data:{}}).then((function(e){e.IsSucceed&&(t.projs=e.Data)}))},projSelectHandle:function(t){this.formData.InstallUnit_Id="",t?this.loadInstallUnitsList():this.units=[]},loadInstallUnitsList:function(){var t=this;(0,s.default)({url:this.apis.GetInstallUnitList,method:"post",data:{Project_Id:this.formData.Project_Id}}).then((function(e){e.IsSucceed&&e.IsSucceed&&(t.units=e.Data)}))},extIcon:function(t){var e="document_unknown_icon";switch(t.name.split(".").pop()){case"xls":case"xlsx":e="document_form_icon";break;case"txt":e="document_txt_icon";break;case"doc":case"docx":e="document_word_icon";break;case"zip":case"7z":case"rar":e="document_zip_icon";break;case"png":case"jpg":case"jpeg":case"gif":case"bmp":e="multimedia_image_icon";break;case"ppt":case"pptx":e="document_ppt_icon";break;case"pdf":e="document_pdf_icon";break}return e}}}},"9ed6a":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:t.form,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"构件编号",prop:"Texture"}},[n("strong",[t._v(" "+t._s(t.form.Code))])]),n("el-form-item",{attrs:{label:"材质",prop:"Texture"}},[n("el-input",{model:{value:t.form.Texture,callback:function(e){t.$set(t.form,"Texture",e)},expression:"form.Texture"}})],1),n("el-form-item",{attrs:{label:"重量",prop:"Netweight"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"100%"},model:{value:t.form.Netweight,callback:function(e){t.$set(t.form,"Netweight",e)},expression:"form.Netweight"}})],1),n("el-form-item",{attrs:{label:"长度",prop:"Length"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"100%"},model:{value:t.form.Length,callback:function(e){t.$set(t.form,"Length",e)},expression:"form.Length"}})],1),n("el-form-item",{attrs:{label:"规格型号",prop:"Spec"}},[n("el-input",{model:{value:t.form.Spec,callback:function(e){t.$set(t.form,"Spec",e)},expression:"form.Spec"}})],1),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(e){return t.$emit("dialogCancel")}}},[t._v("取消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.onSubmit}},[t._v("确 定")])],1)],1)],1)},i=[]},a9f5:function(t,e,n){"use strict";n.r(e);var a=n("2427"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},ca31:function(t,e,n){},cc9e:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"upload-box"},[t._m(0),n("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{attrs:{disabled:t.btnLoading},on:{click:t.handleClose}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit()}}},[t._v("确 定")])],1)],1)])},i=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"margin-bottom":"20px"}},[n("strong",{staticStyle:{"font-size":"16px",color:"#222834"}},[t._v("操作方法")]),n("br"),t._v(" 1. 选择要调整的构件；"),n("br"),t._v(" 2. 点击右上角“导出”按钮；"),n("br"),t._v(" 3. 在下载后的excel表格中编辑“"),n("span",{staticClass:"txt-blue"},[t._v("模型ID")]),t._v("”、“"),n("span",{staticClass:"txt-blue"},[t._v("轴网信息")]),t._v("”字段后，重新上传。 （"),n("span",{staticClass:"txt-red"},[t._v("其它字段禁止修改")]),t._v("）"),n("br"),t._v(" 4. 编辑好后，在此重新上传导入。 ")])}]},d0df:function(t,e,n){"use strict";n.r(e);var a=n("0283"),i=n("ddfe");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("dcdc7"),n("ebe20");var s=n("2877"),l=Object(s["a"])(i["default"],a["a"],a["b"],!1,null,"ff1812e4",null);e["default"]=l.exports},dcdc7:function(t,e,n){"use strict";n("22f4")},ddfe:function(t,e,n){"use strict";n.r(e);var a=n("99d6"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},e6af:function(t,e,n){"use strict";n.r(e);var a=n("1963"),i=n("8f8f");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("f4c0"),n("1471");var s=n("2877"),l=Object(s["a"])(i["default"],a["a"],a["b"],!1,null,"67ac40d4",null);e["default"]=l.exports},e82c:function(t,e,n){},ebe20:function(t,e,n){"use strict";n("0de1")},ece2:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("4de4"),n("7db0"),n("a15b"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7"),n("07ac"),n("25f0"),n("159b");var i=a(n("0f97")),o=a(n("d0df")),s=a(n("5f1ae")),l=n("6186"),r=a(n("b775")),c=n("2e8a"),u=n("1b69"),d=n("2dd9"),f=n("586a"),p=n("ed08"),m=a(n("6b79")),h=n("f2f6");e.default={components:{DynamicDataTable:i.default,CUpload:o.default,ModuleUpload:s.default,BatchEdit:m.default},data:function(){return{loading:!0,gridCode:"pro_component_list",apis:{ImportComponent:(0,p.combineURL)(this.$baseUrl,"/PRO/Component/ImportComponent"),AdjustComponentNum:"/PRO/Component/AdjustComponentNum",UpdateComponent:"/PRO/Component/UpdateComponent",DeleteComponent:"/PRO/Component/DeleteInstallComponent"},tbConfig:{Data_Url:"/PRO/Component/GetInstallUnitCompPageList"},columns:[],data:[],resData:null,projects:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},checkedRows:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:15},editingRows:{},editingFields:[],dynTblOptBak:null,cmptTypes_1:[],cmptTypes_2:[],installName:"",installOption:[],uploading:!1}},watch:{resData:function(t){var e=this;setTimeout((function(){e.setGridData(t)}),0)}},created:function(){var t=this;this.getTableData().then((function(e){e.IsSucceed&&(t.loading=!1,t.resData=e.Data)})).catch(console.error).finally((function(){})),(0,l.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})),(0,c.GetComponentTypeList)({Level:1}).then((function(e){e.IsSucceed&&(t.cmptTypes_1=e.Data)})),(0,u.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)})),this.getUploadStatus()},beforeDestroy:function(){this.removeInterval()},methods:{startCheck:function(){this.listenUploadStatue()},listenUploadStatue:function(){var t=this;this.uploading=!0,this.timer||(this.timer=setInterval((function(){t.getUploadStatus()}),5e3))},getUploadStatus:function(){var t=this;(0,f.GetDelayed)({operSource:1}).then((function(e){if(e.IsSucceed){var n=e.Data[0];if(n){if(1===n.Status)return void t.startCheck();var a=JSON.parse(n.Oper_Result);if(a.IsSucceed)t.$notify.success({title:"操作",message:"导入成功",duration:0}),t.dialogFormSubmitSuccess({type:"reload"});else if(t.$notify.error({message:a.Message,title:"导入失败",duration:0}),a.Data){var i=(0,p.combineURL)(t.$baseUrl,a.Data.split("/").map((function(t){return encodeURIComponent(t)})).join("/"));window.open(i,"_blank")}t.uploading=!1,t.removeInterval(),t.delStatus(n.Id)}}else t.removeInterval(),t.uploading=!1}))},removeInterval:function(){clearInterval(this.timer),this.timer=null},delStatus:function(t){var e=this;(0,f.DelDelayed)({ids:[t]}).then((function(t){t.IsSucceed||e.$message({message:t.Message,type:"error"})}))},showSearch:function(){this.$refs.table.showSearch()},handleExport:function(){var t=this,e=this.checkedRows.map((function(t){return{Installunit_Id:t.Installunit_Id,Code:t.Code}}));(0,f.ExportComponentModelInfoByInstall)(e).then((function(e){if(e.Data){var n=(0,p.combineURL)(t.$baseUrl,e.Data);window.open(n,"_blank")}}))},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:150}),this.filterData.PageSize=this.tbConfig.Row_Number},setCols:function(t){var e=this;t.forEach((function(t){"Type_Name"===t.Code&&(t.Range=JSON.stringify(e.cmptTypes_1.map((function(t){return{label:t.Name,value:t.Name}})))),"Sub_Type_Name"===t.Code&&(t.Range=JSON.stringify(e.cmptTypes_2.map((function(t){return{label:t.Name,value:t.Name}})))),"Project_Name"===t.Code&&(t.Range=JSON.stringify(e.projects.map((function(t){return{label:t.Name,value:t.Name}}))))})),this.columns=t.concat([])},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},backupDynTblOpts:function(){this.dynTblOptBak||(this.dynTblOptBak={},this.dynTblOptBak.tbConfig=Object.assign({},this.tbConfig),this.dynTblOptBak.columns=this.columns.map((function(t){return Object.assign({},t)})),this.dynTblOptBak.data=this.data.map((function(t){return Object.assign({},t)})))},resetDynTblOpts:function(t,e){var n=this,a=!1;if(a=Object.values(this.editingRows).filter((function(t){return Boolean(t)})).length<=0,this.dynTblOptBak){var i=this.tbConfig.Is_Edit;this.tbConfig=Object.assign({},this.dynTblOptBak.tbConfig,{Row_Number:this.filterData.PageSize}),a||void 0===e||(this.tbConfig.Is_Edit=i),this.tbConfig=Object.assign({},this.tbConfig),a&&(this.columns=this.dynTblOptBak.columns.map((function(t){return Object.assign({},t)}))),t&&(this.data=this.dynTblOptBak.data.map((function(t,a){return void 0===e||e===a?Object.assign({},t):Object.assign({},n.data[a])}))),a&&(this.dynTblOptBak=null)}},getTableData:function(){return this.dynTblOptBak&&this.resetDynTblOpts(),this.tbConfig.Data_Url?(0,r.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData)}):Promise.reject("invalid data api...")},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=this,n=t.type;t.data;switch(n){case"reload":this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)}));break}},multiSelectedChange:function(t){this.checkedRows=t},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.PageSize=e,this.filterData.Page=1,this.filterChange()},filterChange:function(t){var e=this;this.filterData=Object.assign({},this.filterData,{Page:Number(t||1)}),this.filterData.ParameterJson=(0,d.setParameterJson)(this.fiterArrObj,this.columns),this.loading=!0,this.getTableData().then((function(t){t.IsSucceed&&e.setGridData(t.Data)})).finally((function(){e.loading=!1}))},editFields:function(t,e){this.backupDynTblOpts(),this.editingFields=t,this.tbConfig=Object.assign({},this.tbConfig,{Is_Edit:!0}),this.columns.forEach((function(e){t.indexOf(e.Code)>-1?e.Is_Edit=!0:e.Is_Edit=!1})),this.columns=this.columns.concat([]),this.$refs.table.changeRowEditMode({index:e,mode:"ready"}),this.editingRows=this.$refs.table.editingRowStatus},cancelRowEdit:function(t,e,n){this.$refs.table.changeRowEditMode({index:e,mode:null}),this.editingFields=[],this.editingRows=this.$refs.table.editingRowStatus,this.resetDynTblOpts(!0,e)},saveRow:function(t,e,n){var a=this,i={url:"",method:"post"};if("component_count"===n[0]&&1===n.length&&(i.url=this.apis.AdjustComponentNum,i.params={code:t.code,installunitId:t.installunit_id,adjustCount:t[n[0]]}),n.length>1&&(i.url=this.apis.UpdateComponent,i.data={},n.forEach((function(e){i.data[e]=t[e]}))),!i.url)return this.$message.warning("无效操作");(0,r.default)(i).then((function(t){t.IsSucceed&&(a.$refs.table.changeRowEditMode({index:e,mode:null}),a.editingFields=[],a.editingRows=a.$refs.table.editingRowStatus,a.resetDynTblOpts(!1,e))}))},handleBathEdit:function(t){var e=this;this.dialogCfgs.component="BatchEdit",this.dialogCfgs.title="编辑",this.dialogCfgs.width="40%",this.dialogShow=!0,this.$nextTick((function(n){e.$refs["content"].init(t)}))},deleteRows:function(t){var e=this;t.length<=0||this.$confirm("确认删除该构件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n=t.map((function(t){return{Installunit_Id:t.Installunit_Id,Code:t.Code}}));(0,r.default)({url:e.apis.DeleteComponent,method:"post",data:n}).then((function(t){t.IsSucceed?(e.$message.success(t.Message||"删除成功"),e.filterChange(e.filterChange.Page)):(e.$message.warning(t.Message||"删除失败"),e.filterChange(e.filterChange.Page))}))})).catch((function(){e.$message.warning("已取消")}))},cellEditorChanged:function(t){t.index,t.row,t.key,t.value},rowEditModeChanged:function(t){this.editingRows=t},columnSearchChange:function(t){var e=this,n=t.column,a=t.value;if("Type_Name"===n){var i=this.cmptTypes_1.find((function(t){return t.Name===a}));if(!i)return;(0,c.GetComponentTypeList)({Level:2,Parent_Id:i.Id}).then((function(t){t.IsSucceed&&(e.cmptTypes_2=t.Data,e.setCols(e.columns),e.$refs.table.searchedField=Object.assign({},e.$refs.table.searchedField,{Sub_Type_Name:""}))}))}if("Project_Name"===n)if(this.installName="",this.installOption=[],a){var o=this.projects.find((function(t){return t.Name===a}));this.getUnitList(o.Id)}else this.$refs.table.searchedField["InstallUnit_Name"]="",this.$refs.table.showSearch()},installNameChange:function(t){this.$refs.table.searchedField["InstallUnit_Name"]=t,this.$refs.table.showSearch()},getUnitList:function(t){var e=this;(0,h.GetInstallUnitList)({Project_Id:t}).then((function(t){t.IsSucceed&&(e.installOption=t.Data)}))}}}},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=r,e.CheckPlanTime=c,e.DeleteInstallUnit=p,e.GetCompletePercent=v,e.GetEntity=y,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=b,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=u,e.GetInstallUnitList=l,e.GetInstallUnitPageList=s,e.GetProjectInstallUnitList=_,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=m,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=C;var i=a(n("b775")),o=a(n("4328"));function s(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function r(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function _(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function y(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(t)})}function C(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},f4c0:function(t,e,n){"use strict";n("ca31")}}]);