(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2c0ca8de"],{"1acb":function(e,t,a){},"26cb":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"page-container"},[a("el-row",{staticStyle:{"margin-bottom":"16px"},attrs:{type:"flex",justify:"space-between"}},[a("el-col",{attrs:{span:4}},[a("el-button",{attrs:{type:"primary"},on:{click:e.addProfession}},[e._v("新增专业")])],1),a("el-col",{attrs:{span:20}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入关键字",clearable:""},model:{value:e.searchValue,callback:function(t){e.searchValue=t},expression:"searchValue"}}),a("div",[a("el-button",{staticStyle:{"margin-left":"16px"},attrs:{type:"primary"},on:{click:e.getData}},[e._v("搜索")])],1)],1)],1)],1),a("div",{staticClass:"list-wrapper"},[a("el-table",{ref:"table",attrs:{tablecode:"plm_professionaltype_list_pro","custom-param":{is_System:!1,typeid:"",name:e.searchValue,companyId:e.companyId}},on:{"get-selection-data":e.getSelectionData,getbutton:e.getClick}})],1)],1),a("bimdialog",{ref:"dialog",on:{getData:e.getData}}),a("nodeList",{ref:"nodeList",on:{getData:e.getData}})],1)},n=[]},"363d":function(e,t,a){"use strict";a.r(t);var i=a("5822"),n=a("82fc");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,null,null);t["default"]=s.exports},"4ae8":function(e,t,a){},5822:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{staticClass:"plmdialog",attrs:{"dialog-title":e.title,"append-to-body":!0,"dialog-width":"400px",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t},handleClose:e.handleClose,cancelbtn:e.handleClose,submitbtn:e.handleSubmit}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"代号",prop:"Code"}},[a("el-input",{staticStyle:{width:"90%"},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),a("el-form-item",{attrs:{label:"专业名称",prop:"Name"}},[a("el-input",{staticStyle:{width:"90%"},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"统计单位",prop:"Unit"}},[a("el-select",{staticStyle:{width:"90%"},attrs:{filterable:"",clearable:"",placeholder:"选择统计单位"},on:{change:e.changeselect},model:{value:e.Unittmp.Id,callback:function(t){e.$set(e.Unittmp,"Id",t)},expression:"Unittmp.Id"}},e._l(e.Unitlist,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"单位换算",prop:"Proportion"}},[a("el-row",[a("el-col",{attrs:{span:14}},[a("el-input",{staticStyle:{width:"95%"},attrs:{disabled:""},model:{value:e.Unittmp.Proportion,callback:function(t){e.$set(e.Unittmp,"Proportion",t)},expression:"Unittmp.Proportion"}})],1),a("el-col",{attrs:{span:9}},[a("el-input",{staticStyle:{width:"85%"},attrs:{disabled:""},model:{value:e.Unittmp.ProportionUnit,callback:function(t){e.$set(e.Unittmp,"ProportionUnit",t)},expression:"Unittmp.ProportionUnit"}})],1)],1)],1),a("el-form-item",{attrs:{label:"排序号",prop:"Sort"}},[a("el-input",{staticStyle:{width:"90%"},attrs:{oninput:"value=value.replace(/[^0-9]/g,'')"},model:{value:e.form.Sort,callback:function(t){e.$set(e.form,"Sort",t)},expression:"form.Sort"}})],1)],1)],1)},n=[]},"58fa":function(e,t,a){"use strict";a.r(t);var i=a("f8e2"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},"5efa":function(e,t,a){"use strict";a.r(t);var i=a("26cb"),n=a("e725");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("f5c5");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"2b094918",null);t["default"]=s.exports},"74e9":function(e,t,a){"use strict";a("1acb")},"7aaa":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;a("20ff");var n=a("8899"),r=i(a("65b1"));t.default={components:{bimdialog:r.default},data:function(){return{dialogVisible:!1,title:"",Type_Id:"",btnLoading:!1,form:{Id:"",Code:"",Name:"",Sort:"",Color:"",Is_System:!0,Is_Factory_Check:!1,Use_Platform:"",Show_Platform:[]},currentType:0,prop2List:[],rules:{Code:[{required:!0,message:"请输入节点编号",trigger:"blur"}],Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Sort:[{required:!0,message:"请输入排序号",trigger:"blur"}],Use_Platform:[{required:!0,message:"请选择操作系统",trigger:"change"}],Show_Platform:[{required:!0,message:"请至少选择一个显示系统",trigger:"change"}]}}},methods:{getCurrentType:function(){var e=this;(0,n.PlatIntegration)({}).then((function(t){t.IsSucceed?e.currentType=t.Data:e.$message({message:t.Message,type:"error"})}))},handleOpen:function(e,t){var a=this;this.getCurrentType(),this.dialogVisible=!0,this.type=e,"add"===this.type?(this.title="添加",this.Type_Id=t.id):(this.title="编辑",(0,n.GetProjectsNodeInfo)({id:t.id}).then((function(e){e.IsSucceed&&(a.form=e.Data)})))},handleClose:function(){this.$refs["form"].resetFields(),this.dialogVisible=!1},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return e.$message({message:"请将表单填写完整",type:"warning"}),!1;"add"===e.type?(e.form.Type_Id=e.Type_Id,(0,n.GetProjectsNodeAdd)({plm_Projects_Node:e.form}).then((function(t){t.IsSucceed?(e.$emit("getData"),e.handleClose()):e.$message({message:t.Message,type:"warning"})}))):(0,n.GetProjectsNodeEdit)({plm_Projects_Node:e.form}).then((function(t){t.IsSucceed&&(e.$emit("getData"),e.handleClose())}))}))}}}},"82fc":function(e,t,a){"use strict";a.r(t);var i=a("b340"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},"918e":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{staticClass:"plmdialog",attrs:{"dialog-title":e.title,"append-to-body":!0,"dialog-width":"400px",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t},handleClose:e.handleClose,cancelbtn:e.handleClose,submitbtn:e.handleSubmit}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"编号",prop:"Code"}},[a("el-input",{model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),a("el-form-item",{attrs:{label:"名称",prop:"Name"}},[a("el-input",{model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"排序号",prop:"Sort"}},[a("el-input",{attrs:{oninput:"value=value.replace(/[^0-9]/g,'')"},model:{value:e.form.Sort,callback:function(t){e.$set(e.form,"Sort",t)},expression:"form.Sort"}})],1),0===e.currentType?a("el-form-item",{attrs:{label:"操作系统",prop:"Use_Platform"}},[a("el-radio-group",{model:{value:e.form.Use_Platform,callback:function(t){e.$set(e.form,"Use_Platform",e._n(t))},expression:"form.Use_Platform"}},[a("el-radio",{attrs:{label:3}},[e._v("BIM+项目管理系统")]),a("el-radio",{attrs:{label:2}},[e._v("生产管理工具")])],1)],1):e._e(),0===e.currentType?a("el-form-item",{attrs:{label:"显示系统",prop:"Show_Platform"}},[a("el-checkbox-group",{model:{value:e.form.Show_Platform,callback:function(t){e.$set(e.form,"Show_Platform",t)},expression:"form.Show_Platform"}},[a("el-checkbox",{attrs:{label:"3"}},[e._v("BIM+项目管理系统")]),a("el-checkbox",{attrs:{label:"2"}},[e._v("生产管理工具")])],1)],1):e._e(),a("el-form-item",{attrs:{id:"colorpick",label:"颜色",prop:"Color"}},[a("el-input",{model:{value:e.form.Color,callback:function(t){e.$set(e.form,"Color",t)},expression:"form.Color"}},[a("template",{slot:"append"},[a("el-color-picker",{attrs:{size:"small"},model:{value:e.form.Color,callback:function(t){e.$set(e.form,"Color",t)},expression:"form.Color"}})],1)],2)],1),a("el-form-item",{attrs:{label:"是否质检",prop:"Is_Factory_Check"}},[a("el-switch",{model:{value:e.form.Is_Factory_Check,callback:function(t){e.$set(e.form,"Is_Factory_Check",t)},expression:"form.Is_Factory_Check"}})],1)],1)],1)},n=[]},b08a:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{staticClass:"plmdialog",attrs:{"dialog-title":e.title,"dialog-width":"1169px",visible:e.dialogVisible,hidebtn:""},on:{"update:visible":function(t){e.dialogVisible=t},handleClose:e.handleClose}},[a("div",{staticClass:"page-container"},[a("el-row",{staticStyle:{"margin-bottom":"16px"},attrs:{type:"flex",justify:"space-between"}},[a("el-col",{attrs:{span:4}},[a("el-button",{attrs:{type:"primary"},on:{click:e.addNode}},[e._v("新增节点")])],1),a("el-col",{attrs:{span:20}},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入关键字",clearable:""},model:{value:e.searchValue,callback:function(t){e.searchValue=t},expression:"searchValue"}}),a("div",[a("el-button",{staticStyle:{"margin-left":"16px"},attrs:{type:"primary"},on:{click:e.getLogisticsData}},[e._v("搜索")])],1)],1)],1)],1),a("div",{staticClass:"list-wrapper"},[a("el-table",{ref:"table",attrs:{tablecode:"plm_Projects_Node_list1","custom-param":{is_System:!0,typeid:e.typeId,name:e.searchValue}},on:{"get-selection-data":e.getSelectionData,getbutton:e.getClick}})],1)],1),a("addlogistics",{ref:"addlogistics",on:{getData:e.getLogisticsData}})],1)},n=[]},b340:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var n=i(a("c14f")),r=i(a("1da1")),o=(a("20ff"),a("8899")),s=i(a("65b1"));t.default={components:{bimdialog:s.default},data:function(){return{dialogVisible:!1,title:"",type:"add",btnLoading:!1,form:{Id:"",Code:"",Name:"",Unit:"",Steel_Unit:"",Proportion:"",Total_Weight:"",Sort:"",MaterialName:"",MaterialCode:"",Is_System:!0,Is_Grid:!1},prop2List:[],rules:{Code:[{required:!0,message:"请输入编号",trigger:"blur"}],Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Sort:[{required:!0,message:"请输入排序号",trigger:"blur"}],Unit:[{required:!0,message:"请选择统计单位",trigger:"blur"}]},Unitlist:[],Unittmp:{}}},methods:{getUnit:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){var a;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.Unittmp={},t.n=1,(0,o.GetDictionaryDetailListByCode)({dictionaryCode:"UnitConversion"});case 1:a=t.v,a.IsSucceed?e.Unitlist=a.Data:e.$message({type:"error",message:a.Message});case 2:return t.a(2)}}),t)})))()},handleOpen:function(e,t){var a=this;return(0,r.default)((0,n.default)().m((function i(){return(0,n.default)().w((function(i){while(1)switch(i.n){case 0:return a.dialogVisible=!0,a.type=e,i.n=1,a.getUnit();case 1:"add"===a.type?(a.title="添加",a.form={Is_System:!0}):(a.title="编辑",(0,o.GetProfessionalInfo)({id:t.id}).then((function(e){if(e.IsSucceed){a.form=e.Data;var t=a.Unitlist.find((function(e){return e.Display_Name===a.form.Unit}));t&&(a.Unittmp={Id:t.Id,Unit:t.Display_Name,Proportion:t.Value.split("/")[1],ProportionUnit:t.Value.split("/")[0]})}})));case 2:return i.a(2)}}),i)})))()},handleClose:function(){this.$refs["form"].resetFields(),this.dialogVisible=!1},changeselect:function(){var e=this,t=this.Unitlist.find((function(t){return t.Id===e.Unittmp.Id}));this.Unittmp={Id:t.Id,Unit:t.Display_Name,Proportion:t.Value.split("/")[1],ProportionUnit:t.Value.split("/")[0]},this.form.Unit=this.Unittmp.Unit,this.form.Proportion=this.Unittmp.Proportion,this.form.Steel_Unit=this.Unittmp.ProportionUnit},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return e.$message({message:"请将表单填写完整",type:"warning"}),!1;"add"===e.type?(0,o.GetProfessionalAdd)({plm_Professional_Type:e.form}).then((function(t){t.IsSucceed?(e.$message({message:"新增成功",type:"success"}),e.$emit("getData"),e.handleClose()):e.$message({message:t.Message,type:"error"})})):(0,o.GetProfessionalEdit)({plm_Professional_Type:e.form}).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("getData"),e.handleClose()):e.$message({message:t.Message,type:"error"})}))}))}}}},be21:function(e,t,a){"use strict";a.r(t);var i=a("918e"),n=a("e91a");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("ded6");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,null,null);t["default"]=s.exports},c2ea:function(e,t,a){},d981:function(e,t,a){"use strict";a.r(t);var i=a("b08a"),n=a("58fa");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("74e9");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"543524b2",null);t["default"]=s.exports},d9f5a:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("b0c0"),a("d3b7"),a("3ca3"),a("ddb0");var n=i(a("1d42")),r=i(a("363d")),o=i(a("d981")),s=a("8899"),l=i(a("2082"));t.default={name:"ProfessionalCategoryList",components:{"el-table":n.default,bimdialog:r.default,nodeList:o.default},mixins:[l.default],data:function(){return{companyId:localStorage.getItem("Last_Working_Object_Id"),searchValue:"",addPageArray:[{path:"unit-template-setting",hidden:!0,component:function(){return a.e("chunk-1ee5b71e").then(a.bind(null,"868b"))},name:"SYSUnitPartTemp",meta:{title:"专用模板配置"}},{path:"template-setting",hidden:!0,component:function(){return a.e("chunk-f6c11df8").then(a.bind(null,"5357"))},name:"TemplateSetting",meta:{title:"专用模板配置"}},{path:"template-setting-lj",hidden:!0,component:function(){return a.e("chunk-71021594").then(a.bind(null,"8478"))},name:"TemplateSettingLj",meta:{title:"零件模板配置"}},{path:this.$route.path+"/category",hidden:!0,component:function(){return a.e("chunk-3fa3429a").then(a.bind(null,"b26b7"))},name:"ProfessionalCategoryListInfo",meta:{title:"零构件类型"}}]}},methods:{getSelectionData:function(){},getData:function(){this.$refs.table.refresh()},addProfession:function(){this.$refs.dialog.handleOpen("add")},edit:function(e,t){if(!0===t.row.is_system)return this.$message({type:"warning",message:"该类别属于系统级别，不可操作"}),!1;this.$refs.dialog.handleOpen("edit",t.row)},delete:function(e,t){var a=this;if(!0===t.row.is_system)return this.$message({type:"warning",message:"该类别属于系统级别，不可操作"}),!1;this.$confirm(" 确认删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.GetProfessionalDelete)({id:t.row.id}).then((function(e){!0===e.IsSucceed&&(a.$message({type:"success",message:"删除成功"}),a.getData())}))})).catch((function(){a.$message({type:"info",message:"已取消删除"})}))},getClick:function(e,t){switch(e){case"btnedit":this.edit(e,t);break;case"btndelete":this.delete(e,t);break;case"jdedit":this.$refs.nodeList.handleOpen(!0,t.row);break;case"unitPartCode":this.$router.push({name:"SYSUnitPartTemp",query:{pg_redirect:this.$route.name,name:t.row.name,unit:t.row.unit,steel_unit:t.row.steel_unit}});break;case"mbedit":this.$router.push({name:"TemplateSetting",query:{pg_redirect:this.$route.name,typeCode:t.row.code,materialCode:t.row.materialcode,name:t.row.name,unit:t.row.unit,steel_unit:t.row.steel_unit}});break;case"ljedit":this.$router.push({name:"TemplateSettingLj",query:{pg_redirect:this.$route.name,typeCode:t.row.code,materialCode:t.row.materialcode,name:t.row.name,unit:t.row.unit,steel_unit:t.row.steel_unit}});break;case"gjedit":this.$router.push({name:"ProfessionalCategoryListInfo",query:{pg_redirect:this.$route.name,typeCode:t.row.code,materialCode:t.row.materialcode,name:t.row.name,unit:t.row.unit,steel_unit:t.row.steel_unit,typeId:t.row.id}});break}}}}},ded6:function(e,t,a){"use strict";a("4ae8")},e725:function(e,t,a){"use strict";a.r(t);var i=a("d9f5a"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},e91a:function(e,t,a){"use strict";a.r(t);var i=a("7aaa"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},f5c5:function(e,t,a){"use strict";a("c2ea")},f8e2:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("be21")),r=i(a("65b1")),o=i(a("1d42")),s=a("8899");t.default={name:"NodeList",components:{addlogistics:n.default,bimdialog:r.default,"el-table":o.default},data:function(){return{searchValue:"",dialogVisible:!1,title:"配置节点",typeId:""}},methods:{handleClose:function(){this.dialogVisible=!1},getLogisticsData:function(){this.$refs.table.refresh()},handleOpen:function(e,t){var a=this;this.dialogVisible=!0,this.typeId=t.id,this.$nextTick((function(){a.$refs.table.refresh()}))},addNode:function(){this.$refs.addlogistics.handleOpen("add",{id:this.typeId})},getSelectionData:function(){},logisticsEdit:function(e,t){this.$refs.addlogistics.handleOpen("edit",t.row)},logisticsdelete:function(e,t){var a=this;this.$confirm(" 确认删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.GetProjectsNodeDelete)({id:t.row.id}).then((function(e){e.IsSucceed&&(a.$message({type:"success",message:"删除成功"}),a.getLogisticsData())}))}))},getClick:function(e,t){switch(e){case"logisticsedit":this.logisticsEdit(e,t);break;case"logisticsdelete":this.logisticsdelete(e,t);break}}}}}}]);