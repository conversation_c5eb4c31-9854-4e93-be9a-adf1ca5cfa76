(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2e29fd60"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,a){var i=o(),s=t-i,l=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=l;var t=Math.easeInOutQuad(c,i,s,e);r(t),c<e?n(u):a&&"function"===typeof a&&a()};u()}},3166:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=u,e.GeAreaTrees=j,e.GetFileSync=C,e.GetInstallUnitIdNameList=y,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=R,e.GetProjectAreaTreeList=b,e.GetProjectEntity=l,e.GetProjectList=s,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=_,e.GetSchedulingPartList=S,e.IsEnableProjectMonomer=d,e.SaveProject=c,e.UpdateProjectTemplateBase=P,e.UpdateProjectTemplateContacts=g,e.UpdateProjectTemplateContract=v,e.UpdateProjectTemplateOther=I;var r=n(a("b775")),o=n(a("4328"));function i(t){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(t)})}function c(t){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(t)})}function d(t){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function C(t){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"3be7":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container abs100"},[a("div",{staticClass:"search-wrapper"},[a("el-tabs",{on:{"tab-click":t.handleTabsClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"待加工",name:"待加工"}}),a("el-tab-pane",{attrs:{label:"待确认",name:"待确认"}}),a("el-tab-pane",{attrs:{label:"已完成",name:"已完成"}})],1),a("el-form",{ref:"form",attrs:{model:t.form,inline:"","label-width":"100px"}},[[a("el-form-item",{attrs:{label:"零件名称",prop:"Codes_Format"}},[a("el-input",{attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text"},model:{value:t.form.Codes_Format,callback:function(e){t.$set(t.form,"Codes_Format",e)},expression:"form.Codes_Format"}})],1)],a("el-form-item",{attrs:{label:"补换单号",prop:"Replace_Code"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text"},model:{value:t.form.Replace_Code,callback:function(e){t.$set(t.form,"Replace_Code",e)},expression:"form.Replace_Code"}})],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},t._l(t.ProjectNameData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),[a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!t.form.Project_Id,"select-params":{clearable:!0},"tree-params":t.treeParamsArea},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1)],[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",attrs:{disabled:!t.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:t.setupPositionChange},model:{value:t.form.InstallUnit_Id,callback:function(e){t.$set(t.form,"InstallUnit_Id",e)},expression:"form.InstallUnit_Id"}},t._l(t.SetupPositionData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)],a("el-form-item",{attrs:{label:"要求完成日期",prop:"Finish_Date"}},[a("el-date-picker",{attrs:{type:"daterange",clearable:!0,align:"right","value-format":"yyyy-MM-dd","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.Finish_Date,callback:function(e){t.Finish_Date=e},expression:"Finish_Date"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),a("el-button",{on:{click:t.resetSearch}},[t._v("重置")])],1)],2)],1),a("div",{staticClass:"main-wrapper"},[a("div",{directives:[{name:"show",rawName:"v-show",value:"待加工"===t.activeName,expression:"activeName==='待加工'"}],staticClass:"assistant-wrapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{size:"small",type:"primary",disabled:0===t.selectList.length},on:{click:function(e){return t.handleApprove(t.selectList)}}},[t._v("批量加工 ")])],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),t._l(t.columns,(function(e,n){return a("vxe-column",{key:n,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,field:e.Code,title:e.Display_Name,"min-width":e.minWidth,width:e.Width},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.row;return["Process_Status"==e.Code?a("div",[1===r[e.Code]?a("span",{staticStyle:{color:"#67c23a"}},[t._v("已完成")]):2===r[e.Code]?a("span",{staticStyle:{color:"#e6a23c"}},[t._v("待确认")]):a("span",{staticStyle:{color:"#f56c6c"}},[t._v("待加工")])]):"Reality_Finish_Date"==e.Code?a("div",[a("span",[t._v(t._s("01-01-01"===r.Reality_Finish_Date?"-":r.Reality_Finish_Date))])]):a("div",[a("span",[t._v(t._s(r[e.Code]||"-"))])])]}}],null,!0)})})),"待加工"===t.activeName?a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"150","show-overflow":""},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleApprove([n])}}},[t._v("加工")])]}}],null,!1,1227101685)}):t._e()],2)],1),a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"cs-component-num"}),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.changePage}})],1)])])])},r=[]},"4e82":function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),i=a("7b0b"),s=a("07fa"),l=a("083a"),c=a("577e"),u=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),p=a("99f4"),h=a("1212"),P=a("ea83"),g=[],v=r(g.sort),I=r(g.push),_=u((function(){g.sort(void 0)})),b=u((function(){g.sort(null)})),y=f("sort"),j=!u((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(P)return P<603;var t,e,a,n,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)g.push({k:e+n,v:a})}for(g.sort((function(t,e){return e.v-t.v})),n=0;n<g.length;n++)e=g[n].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),R=_||!b||!y||!j,S=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:R},{sort:function(t){void 0!==t&&o(t);var e=i(this);if(j)return void 0===t?v(e):v(e,t);var a,n,r=[],c=s(e);for(n=0;n<c;n++)n in e&&I(r,e[n]);d(r,S(t)),a=s(r),n=0;while(n<a)e[n]=r[n++];while(n<c)l(e,n++);return e}})},"55ed":function(t,e,a){},"83b4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("7d54"),a("d3b7"),a("159b");var n=a("3166"),r=a("f2f6");e.default={data:function(){return{ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},SetupPositionData:[]}},mounted:function(){this.getProjectOption()},methods:{getProjectOption:function(){var t=this;(0,n.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getAreaList:function(){var t=this,e=this.form.Project_Id?this.form.Project_Id:this.form.ProjectId;(0,n.GeAreaTrees)({projectId:e}).then((function(e){if(e.IsSucceed){var a=e.Data;t.setDisabledTree(a),t.treeParamsArea.data=a,t.$nextTick((function(e){var n;null===(n=t.$refs.treeSelectArea)||void 0===n||n.treeDataUpdateFun(a)}))}else t.$message({message:e.Message,type:"error"})}))},getInstall:function(){var t=this;(0,r.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.SetupPositionData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},projectChangeSingle:function(t){var e,a=this;this.$nextTick((function(){a.form.ProjectName=a.$refs["ProjectName"].selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.getProjectEntity(t)},projectChange:function(t){var e,a=this;this.$nextTick((function(){var t;a.form.ProjectName=null===(t=a.$refs["ProjectName"])||void 0===t?void 0:t.selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.form.Area_Id="",this.form.AreaPosition="",this.treeParamsArea.data=[],this.$nextTick((function(t){var e;null===(e=a.$refs.treeSelectArea)||void 0===e||e.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",t&&this.getAreaList()},areaChange:function(t){this.form.AreaPosition=t.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.AreaPosition="",this.form.InstallUnit_Id="",this.form.SetupPosition=""},setupPositionChange:function(){var t=this;this.$nextTick((function(){t.form.SetupPosition=t.$refs["SetupPosition"].selected.currentLabel}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var a=t.Children;a&&a.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(a))}))},dateChange:function(t){},getProjectEntity:function(t){var e=this;(0,n.GetProjectEntity)({id:t}).then((function(t){if(t.IsSucceed){var a="",n=t.Data.Contacts;n.forEach((function(t){"Consignee"===t.Type&&(a=t.Name)})),e.consigneeName=a}else e.$message({message:t.Message,type:"error"})}))}}}},"86e2":function(t,e,a){"use strict";a.r(e);var n=a("91e25"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"87c9":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AssignReplacementTask=m,e.EditReplacementApply=c,e.FindPartReplaceApplyById=s,e.GetPartReplaceApplyPageList=i,e.GetReplaceApprovePageList=d,e.GetReplacementTaskPageList=f,e.GetReplacementTaskProcessPageList=p,e.GetReplacementTaskTracePageList=P,e.GetTeamListByUser=o,e.GetWorkingTeams=g,e.SavePartReplaceApply=l,e.SavePartReplaceComfirm=u,e.UpdateProcessTaskStatus=h;var r=n(a("b775"));n(a("4328"));function o(t){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/Replacement/GetPartReplaceApplyPageList",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/Replacement/FindPartReplaceApplyById",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Replacement/SavePartReplaceApply",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/Replacement/EditReplacementApply",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Replacement/SavePartReplaceComfirm",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/Replacement/GetReplaceApprovePageList",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskPageList",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Replacement/AssignReplacementTask",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskProcessPageList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Replacement/UpdateProcessTaskStatus",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskTracePageList",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}},"91e25":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("4e82"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("5319"),a("498a"),a("c7cd"),a("ddb0");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),s=a("6186"),l=a("fd31"),c=a("87c9"),u=n(a("83b4")),d=a("c685"),f=n(a("333d")),m=a("8975");e.default={name:"PROPartReplaceProcess",components:{Pagination:f.default},mixins:[u.default],data:function(){return{tbConfig:{},columns:[],tbData:[],selectList:[],tablePageSize:d.tablePageSize,queryInfo:{Page:1,PageSize:20},total:0,tbLoading:!1,gridCode:"part_replace_process_page_list",form:{InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Area_Id:"",AreaPosition:"",Start_Finish_Date:"",End_Finish_Date:"",Codes_Format:"",Codes:"",Process_Status:3,Replace_Code:""},pickerOptions:{shortcuts:[{text:"今天",onClick:function(t){var e=new Date,a=new Date;t.$emit("pick",[a,e])}},{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}}]},activeName:"待加工",Unit:"",ProfessionalCode:"",ProfessionalId:""}},computed:{Finish_Date:{get:function(){return[(0,m.timeFormat)(this.form.Start_Finish_Date),(0,m.timeFormat)(this.form.End_Finish_Date)]},set:function(t){if(t){var e=t[0],a=t[1];this.form.Start_Finish_Date=(0,m.timeFormat)(e),this.form.End_Finish_Date=(0,m.timeFormat)(a)}else this.form.Start_Finish_Date="",this.form.End_Finish_Date=""}}},created:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTypeList();case 1:return e.n=2,t.fetchData();case 2:return e.a(2)}}),e)})))()},mounted:function(){},methods:{getTypeList:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){var a,n,r,i;return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=e.v,n=a.Data,a.IsSucceed?(t.typeOption=Object.freeze(n),t.typeOption.length>0&&(t.Proportion=n[0].Proportion,t.Unit=n[0].Unit,t.form.TypeId=null===(r=t.typeOption[0])||void 0===r?void 0:r.Id,t.form.Type_Name=null===(i=t.typeOption[0])||void 0===i?void 0:i.Name)):t.$message({message:a.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},fetchData:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig(t.gridCode);case 1:t.tbLoading=!0,Promise.all([t.fetchList()]).then((function(e){t.tbLoading=!1}));case 2:return e.a(2)}}),e)})))()},getTableConfig:function(t){var e=this;return new Promise((function(a){(0,s.GetGridByCode)({code:t+","+e.typeOption.find((function(t){return t.Id===e.form.TypeId})).Code}).then((function(t){var n=t.IsSucceed,r=t.Data,o=t.Message;if(n){if(!r)return e.$message.error("当前专业没有配置相对应表格"),void(e.tbLoading=!0);e.tbConfig=Object.assign({},e.tbConfig,r.Grid);var i=r.ColumnList||[],s=i.sort((function(t,e){return t.Sort-e.Sort}));e.columns=s.filter((function(t){return t.Is_Display})).map((function(t){return"Replace_Code"===t.Code&&(t.fixed="left"),t})),e.columns.map((function(t){if("Process_Status"===t.Code)return t.minWidth="160",t.Width="auto",t})),e.queryInfo.PageSize=+r.Grid.Row_Number||20,a(e.columns);var l=JSON.parse(JSON.stringify(e.columns));e.columnsOption=l}else e.$message({message:o,type:"error"})}))}))},fetchList:function(){var t=this;return new Promise((function(e){t.form.Audit_Results_Name=t.form.Audit_Results_Id,(0,c.GetReplacementTaskProcessPageList)((0,r.default)((0,r.default)({},t.queryInfo),t.form)).then((function(a){a.IsSucceed?(t.queryInfo.PageSize=a.Data.PageSize,t.total=a.Data.TotalCount,t.tbData=a.Data.Data.map((function(t){return t.Demand_Finish_Date=(0,m.timeFormat)(t.Demand_Finish_Date,"{y}-{m}-{d}"),t.Reality_Finish_Date=(0,m.timeFormat)(t.Reality_Finish_Date,"{y}-{m}-{d}"),t})),t.selectList=[]):t.$message({message:a.Message,type:"error"}),e()})).finally((function(e){t.isUpdate=!1}))}))},handleApprove:function(t){var e=this,a=t.map((function(t){return t.Id}));this.$confirm("确认加工该零件吗","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.UpdateProcessTaskStatus)({Ids:a,Operation_Type:3}).then((function(t){t.IsSucceed&&(e.$message({message:"加工成功",type:"success"}),e.fetchData())}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},changePage:function(t){var e=this;return(0,i.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:n=t.page,r=t.limit,e.tbLoading=!0,e.queryInfo.PageSize=r,e.queryInfo.Page=n,Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 1:return a.a(2)}}),a)})))()},tbSelectChange:function(t){this.selectList=t.records},resetSearch:function(){var t=this;this.$nextTick((function(){t.$refs["form"].resetFields(),t.Finish_Date="",t.handleSearch()}))},handleSearch:function(){var t=this.form.Codes_Format.trim();t=t.replace(/\s+/g,"\n"),this.form.Codes=t,this.queryInfo.Page=1,this.fetchData()},handleTabsClick:function(t,e){"待加工"===t.name?this.form.Process_Status=3:"待确认"===t.name?this.form.Process_Status=2:this.form.Process_Status=1,this.fetchList()}}}},a085:function(t,e,a){"use strict";a.r(e);var n=a("3be7"),r=a("86e2");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("af3d");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"16dd346e",null);e["default"]=s.exports},af3d:function(t,e,a){"use strict";a("55ed")},dca8:function(t,e,a){"use strict";var n=a("23e7"),r=a("bb2f"),o=a("d039"),i=a("861d"),s=a("f183").onFreeze,l=Object.freeze,c=o((function(){l(1)}));n({target:"Object",stat:!0,forced:c,sham:!r},{freeze:function(t){return l&&i(t)?l(s(t)):t}})},f2f6:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=l,e.CheckPlanTime=c,e.DeleteInstallUnit=m,e.GetCompletePercent=v,e.GetEntity=_,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=g,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=u,e.GetInstallUnitList=s,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=I,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=P,e.SaveOhterSourceInstallUnit=b;var r=n(a("b775")),o=n(a("4328"));function i(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function c(t){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function u(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function _(t){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(t)})}function b(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);