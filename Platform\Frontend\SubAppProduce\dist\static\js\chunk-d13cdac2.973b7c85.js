(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-d13cdac2"],{"05e0":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.StatisticalDateW=e.ProjectStatusW=e.ProjectNumberW=e.ProjectAbbreviationW=void 0;e.ProjectAbbreviationW=140,e.ProjectNumberW=120,e.ProjectStatusW=100,e.StatisticalDateW=120},"0825":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n=i(a("6612"));e.default={filters:{formatNum:function(t){return(0,n.default)(t).format("0,0.[00]")}},props:{amount:{type:[String,Number],default:0},label:{type:String,default:""},bg:{type:String,default:""}}}},"0c22":function(t,e,a){"use strict";a("941d")},"3a87":function(t,e,a){},"487f":function(t,e,a){"use strict";a.r(e);var i=a("ef7b"),n=a("a5ee");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("0c22");var r=a("2877"),u=Object(r["a"])(n["default"],i["a"],i["b"],!1,null,"70bb946d",null);e["default"]=u.exports},8386:function(t,e,a){"use strict";a("3a87")},"8ff5":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.curModuleKey=void 0;e.curModuleKey="production"},9216:function(t,e,a){"use strict";a.r(e);var i=a("0825"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"941d":function(t,e,a){},"963e":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tag-x"},[a("span",[t._v(t._s(t.label)+"：")]),a("span",{staticClass:"cs-num"},[t._v(t._s(t._f("formatNum")(t.amount)))]),a("span",[t._v(" 元")])])},n=[]},a5ee:function(t,e,a){"use strict";a.r(e);var i=a("cbf1"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},ab88:function(t,e,a){"use strict";a.r(e);var i=a("963e"),n=a("9216");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("8386");var r=a("2877"),u=Object(r["a"])(n["default"],i["a"],i["b"],!1,null,"83134542",null);e["default"]=u.exports},cbf1:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("14d9"),a("13d5"),a("b0c0"),a("e9f5"),a("910d"),a("9485"),a("d3b7"),a("3ca3"),a("ddb0");var n=i(a("5530")),o=i(a("c14f")),r=i(a("1da1")),u=i(a("ac03")),l=i(a("3502")),s=i(a("ab88")),c=i(a("2082")),d=a("d7ff"),f=a("2f62"),h=a("7757"),m=a("8ff5"),p=a("05e0");e.default={name:"PROProductionReport",components:{Tags:s.default,VTable:l.default},mixins:[u.default,c.default],data:function(){return{addPageArray:[],showExport:!1,totalInfo:{Output_SubTotal:0,NonBusiness_Income:0,Cost_SubTotal:0,Assets_Depreciation_Fee:0,Fees_SubTotal:0,Stock_SubTotal:0,Profit:0},factoryOption:[],staticTime:"",activeName:""}},beforeCreate:function(){this.curModuleKey=m.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return t.loading=!0,t.$store.commit("oma/INIT",t.curModuleKey),t.$store.commit("oma/SAVE_KEY",t.curModuleKey),e.n=1,t.saveRoles({key:t.curModuleKey,value:t.$route.meta.Id});case 1:return t.initRoute(),e.n=2,t.init();case 2:t.fetchData();case 3:return e.a(2)}}),e)})))()},methods:(0,n.default)((0,n.default)({},(0,f.mapActions)({saveRoles:"oma/saveRoles",saveFactoryId:"oma/saveFactoryId",saveOriginDate:"oma/saveOriginDate"})),{},{init:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getFactory();case 1:return t.factoryOption=e.v,t.factoryOption.length&&(t.activeName=t.factoryOption[0].Id,t.saveFactoryId({key:t.curModuleKey,value:t.activeName})),e.n=2,t.getDate();case 2:t.staticTime=e.v,t.saveOriginDate({key:t.curModuleKey,value:t.staticTime}),t.showExport=t.getRoles("OMAProExport");case 3:return e.a(2)}}),e)})))()},initRoute:function(){(this.getRoles("OMAProView")||this.getRoles("OMAProCheck"))&&this.addPageArray.push({path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-b4941a9e").then(a.bind(null,"af23c"))},name:"OMAProductionReportDetail",meta:{title:"生产汇总详情",noCache:!0}});var t=[{path:this.$route.path+"/mInfo",hidden:!0,component:function(){return a.e("chunk-825fb472").then(a.bind(null,"5c02"))},name:"OMAProductionOutputInfo",meta:{title:"产值明细",noCache:!0}},{path:this.$route.path+"/costInfo",hidden:!0,component:function(){return a.e("chunk-1b0793be").then(a.bind(null,"bd44"))},name:"OMAProductionCostDetailInfo",meta:{title:"成本明细",activeMenu:"/pro/production_report",noCache:!0}},{path:this.$route.path+"/feeInfo",hidden:!0,component:function(){return a.e("chunk-8e9c5f82").then(a.bind(null,"3d5b"))},name:"OMAProductionFeeDetailInfo",meta:{title:"费用明细",activeMenu:"/pro/production_report",noCache:!0}},{path:this.$route.path+"/balanceInfo",hidden:!0,component:function(){return a.e("chunk-a345eb26").then(a.bind(null,"aeea"))},name:"OMAProductionBalanceDetailInfo",meta:{title:"结存明细",activeMenu:"/pro/production_report",noCache:!0}}];this.getRoles("OMAProOutputDetail")&&this.addPageArray.push(t[0]),this.getRoles("OMAProCostDetail")&&this.addPageArray.push(t[1]),this.getRoles("OMAProFeeDetail")&&this.addPageArray.push(t[2]),this.getRoles("OMAProBaDetail")&&this.addPageArray.push(t[3]),this.getRoles("OMAProBaSeDetail")&&this.addPageArray.push({path:this.$route.path+"/Semi",hidden:!0,component:function(){return a.e("chunk-09b0907e").then(a.bind(null,"91dd"))},name:"OMAProductionBalanceDetailSemiInfo",meta:{title:"半成品结存明细",activeMenu:"/pro/production_report",noCache:!0}});var e=[{path:this.$route.path+"/workDetail",hidden:!0,component:function(){return a.e("chunk-55983c44").then(a.bind(null,"db47"))},name:"OMAProductionFeeDetailWorker",meta:{title:"劳资生产人员",activeMenu:"/pro/production_report",noCache:!0}},{path:this.$route.path+"/outsourcing",hidden:!0,component:function(){return a.e("chunk-2c7bacd8").then(a.bind(null,"9f8f"))},name:"OMAOProductionFeeDetailOutsourcing",meta:{title:"劳资工序外包",activeMenu:"/pro/production_report",noCache:!0}}];this.getRoles("OMAProPeoDetail")&&this.addPageArray.push(e[0]),this.getRoles("OMAProProcessDetail")&&this.addPageArray.push(e[1]),this.initPage(this.$route.name)},fetchData:function(){var t=this;this.checkDate(this.staticTime)&&(this.loading=!0,(0,d.ProductionSummaryList)({StatisticalDate:this.staticTime,FactoryId:this.activeName}).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[];try{var a=t.setTotalData(t.tableData,t.generateColumn(),!0),i=a.column;t.columns=i,t.$refs["tb"].setColumns(i),t.getTotalInfos()}catch(n){}}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},changeTab:function(t){this.saveFactoryId({key:this.curModuleKey,value:t.name}),this.fetchData()},getTotalInfos:function(){var t=this,e=function(t){return 3===t.AccountingStatus},a=function(a){return t.tableData.filter(e).reduce((function(t,e){return t+e[a]}),0)};this.totalInfo.Output_SubTotal=a("Output_SubTotal"),this.totalInfo.NonBusiness_Income=a("NonBusiness_Income"),this.totalInfo.Cost_SubTotal=a("Cost_SubTotal"),this.totalInfo.Assets_Depreciation_Fee=a("Assets_Depreciation_Fee"),this.totalInfo.Fees_SubTotal=a("Fees_SubTotal"),this.totalInfo.Stock_SubTotal=a("Stock_SubTotal"),this.totalInfo.Profit=a("Profit")},handleCheck:function(t){this.$router.push({name:"OMAProductionReportDetail",query:{pg_redirect:this.$route.name,type:"check",d:t.Statics_Date}})},getDate:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){var a;return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,h.GetReportLastDate)({FactoryId:t.activeName});case 1:if(a=e.v,!a.IsSucceed){e.n=2;break}return e.a(2,a.Data);case 2:t.$message({message:a.Message,type:"error"});case 3:return e.a(2)}}),e)})))()},handleView:function(t){this.$router.push({name:"OMAProductionReportDetail",query:{pg_redirect:this.$route.name,type:"view",d:t.Statics_Date}})},generateColumn:function(){var t=this,e=this.$createElement,a=140;return[{title:"统计日期",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{field:"Statics_Date",minWidth:p.StatisticalDateW,title:"当月合计",formatter:["formatDateTime","yyyy-MM-dd"]}]}]},{title:"生产产值(元)",params:{bg:"bg-green"},children:[{title:"成品产值(元)",children:[{minWidth:a,field:"Product_Output",title:0,isTotal:!0}]},{title:"修正值(元)",children:[{minWidth:a,field:"Product_Output_Fix",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Output_SubTotal",title:0,isTotal:!0}]}]},{params:{bg:"bg-cyan"},title:"营业外收入",children:[{title:"小计(元)",children:[{minWidth:a,field:"NonBusiness_Income",title:0,isTotal:!0}]}]},{title:"生产成本(元)",params:{bg:"bg-blue"},children:[{title:"主材成本(元)-采购(元)",children:[{minWidth:170,field:"Raw_Purchase_Cost_Production",title:0,isTotal:!0}]},{title:"主材成本-甲供(元)",children:[{minWidth:150,field:"Raw_Self_Supplying_Cost_Production",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Cost_SubTotal",title:0,isTotal:!0}]}]},{title:"资产折旧(元)",params:{bg:"bg-purple"},children:[{title:"折旧费用(元)",children:[{minWidth:a,field:"Assets_Depreciation_Fee",title:0,isTotal:!0}]}]},{title:"生产费用(元)",params:{bg:"bg-yellow"},children:[{title:"项目辅材(元)",children:[{minWidth:a,field:"Project_Aux_Fee",title:0,isTotal:!0}]},{title:"公共辅材(元)",children:[{minWidth:a,field:"Public_Aux_Fee",title:0,isTotal:!0}]},{title:"劳资-生产工人",children:[{minWidth:a,field:"Labor_Salary",title:0,isTotal:!0}]},{title:"劳资-工序外包",children:[{minWidth:a,field:"External_Labor_Salary",title:0,isTotal:!0}]},{title:"管理费用(元)",children:[{minWidth:a,field:"Manage_Fee",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Fees_SubTotal",title:0,isTotal:!0}]}]},{title:"生产结存(元)",params:{bg:"bg-orange"},children:[{title:"半成品主材结存(元)",children:[{minWidth:160,field:"Semi_Finished_Stock",title:0,isTotal:!0}]},{title:"项目辅材结存(元)",children:[{minWidth:160,field:"Project_Aux_Stock",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Stock_SubTotal",title:0,isTotal:!0}]}]},{title:"生产利润(元)",params:{bg:"bg-pink"},children:[{title:"产值+营业外收入-成本-资产折旧-费用+结存差",children:[{minWidth:370,field:"Profit",title:0,isTotal:!0}]}]},{field:"Status",title:"核算状态(元)",minWidth:a,slots:{default:function(t){var a=t.row;return[e("el-tag",{attrs:{type:1===a.AccountingStatus?"danger":2===a.AccountingStatus?"warning":3===a.AccountingStatus?"success":""}},[1===a.AccountingStatus?"未核算":2===a.AccountingStatus?"待确认":3===a.AccountingStatus?"已核算":""])]}}},{title:"操作",params:{empty:!0},visible:!0,fixed:"right",minWidth:a,slots:{default:function(a){var i=a.row,n=a.column,o=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleView(i)}}},["查看"]),r=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleCheck(i)}}},["审核"]),u=[];return t.getRoles("OMAProView")&&u.push(o),2===i.AccountingStatus&&t.getRoles("OMAProCheck")&&u.push(r),u.length&&(n.params.empty=!1),u}}}]}})}},d7ff:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetExtenalProcessFeesList=m,e.GetFixRecord=v,e.GetProcessFeesList=h,e.GetSCCostDailyDetailList=c,e.GetSCFeesDailyDetailList=f,e.GetSCOutputDailyDetailList=d,e.GetSCProjectSummaryList=u,e.GetSCStockDailyDetailList=p,e.GetSemiProductDailyDetailList=b,e.ImportSCFixRecord=_,e.ProductionSummaryList=r,e.ReviewStockControlSummary=s,e.SubmitAccounting=l,e.SubmitFixValue=g;var n=i(a("b775")),o="";function r(t){return(0,n.default)({url:o+"/oma/screport/ProductionSummaryList",method:"post",data:t})}function u(t){return(0,n.default)({url:o+"/oma/screport/GetSCProjectSummaryList",method:"post",data:t})}function l(t){return(0,n.default)({url:o+"/oma/screport/SubmitAccounting",method:"post",data:t})}function s(t){return(0,n.default)({url:o+"/oma/screport/ReviewStockControlSummary",method:"post",data:t})}function c(t){return(0,n.default)({url:o+"/oma/screport/GetSCCostDailyDetailList",method:"post",data:t})}function d(t){return(0,n.default)({url:o+"/oma/screport/GetSCOutputDailyDetailList",method:"post",data:t})}function f(t){return(0,n.default)({url:o+"/oma/screport/GetSCFeesDailyDetailList",method:"post",data:t})}function h(t){return(0,n.default)({url:o+"/oma/screport/GetProcessFeesList",method:"post",data:t})}function m(t){return(0,n.default)({url:o+"/oma/screport/GetExtenalProcessFeesList",method:"post",data:t})}function p(t){return(0,n.default)({url:o+"/oma/screport/GetSCStockDailyDetailList",method:"post",data:t})}function b(t){return(0,n.default)({url:o+"/oma/screport/GetSemiProductDailyDetailList",method:"post",data:t})}function g(t){return(0,n.default)({url:o+"/oma/screport/SubmitFixValue",method:"post",data:t})}function v(t){return(0,n.default)({url:o+"/oma/screport/GetFixRecord",method:"post",data:t})}function _(t){return(0,n.default)({url:o+"/oma/screport/ImportSCFixRecord",method:"post",data:t})}},ef7b:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-tabs",{on:{"tab-click":t.changeTab},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.factoryOption,(function(t){return a("el-tab-pane",{key:t.Id,attrs:{label:t.Short_Name,name:t.Id}})})),1),a("div",{staticClass:"search-x"},[a("label",[t._v("统计时间： "),a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"month","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.staticTime,callback:function(e){t.staticTime=e},expression:"staticTime"}})],1),a("el-button",{attrs:{disabled:t.loading,type:"primary"},on:{click:function(e){return t.fetchData()}}},[t._v("搜 索")])],1),a("el-divider"),a("div",{staticClass:"toolbar"},[a("Tags",{staticClass:"bg-green",attrs:{label:"生产产值",amount:t.totalInfo.Output_SubTotal}}),a("Tags",{staticClass:"bg-cyan",attrs:{label:"营业外收入",amount:t.totalInfo.NonBusiness_Income}}),a("Tags",{staticClass:"bg-blue",attrs:{label:"生产成本",amount:t.totalInfo.Cost_SubTotal}}),a("Tags",{staticClass:"bg-purple",attrs:{label:"资产折旧",amount:t.totalInfo.Assets_Depreciation_Fee}}),a("Tags",{staticClass:"bg-yellow",attrs:{label:"生产费用",amount:t.totalInfo.Fees_SubTotal}}),a("Tags",{staticClass:"bg-orange",attrs:{label:"生产结存",amount:t.totalInfo.Stock_SubTotal}}),a("Tags",{staticClass:"bg-pink",attrs:{label:"生产利润",amount:t.totalInfo.Profit}})],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("label",[t._v("数据列表")]),t.showExport?a("el-button",{staticClass:"mr-10",attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.staticTime+"生产汇总表")}}},[t._v("导出报表")]):t._e()],1),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},n=[]}}]);