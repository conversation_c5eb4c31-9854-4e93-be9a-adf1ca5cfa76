(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3aac15aa"],{"036c":function(e,t,a){},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=r(),s=e-i,l=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=l;var e=Math.easeInOutQuad(u,i,s,t);o(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},"0ebc":function(e,t,a){"use strict";a.r(t);var n=a("7882"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},1216:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.show?a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,"label-width":"120px"}},[a("el-row",[e.getLabel("SteelName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelName")+":",prop:"SteelName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1)],1):e._e(),e.getLabel("SteelSpec")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelSpec")+":",prop:"SteelSpec"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelSpec,callback:function(t){e.$set(e.form,"SteelSpec",t)},expression:"form.SteelSpec"}})],1)],1):e._e(),e.getLabel("SteelWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelWeight")+":",prop:"SteelWeight"}},[a("el-input",{attrs:{type:"number",step:"any",disabled:!0},model:{value:e.form.SteelWeight,callback:function(t){e.$set(e.form,"SteelWeight",t)},expression:"form.SteelWeight"}})],1)],1):e._e(),e.getLabel("SteelAllWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelAllWeight")+":",prop:"SteelAllWeight"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelAllWeight,callback:function(t){e.$set(e.form,"SteelAllWeight",t)},expression:"form.SteelAllWeight"}})],1)],1):e._e(),e.getLabel("SteelLength")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelLength")+":",prop:"SteelLength"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelLength,callback:function(t){e.$set(e.form,"SteelLength",t)},expression:"form.SteelLength"}})],1)],1):e._e(),e.getLabel("SteelType")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelType")+":",prop:"SteelType"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelType,callback:function(t){e.$set(e.form,"SteelType",t)},expression:"form.SteelType"}})],1)],1):e._e(),e.getLabel("SteelAmount")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelAmount")+":",prop:"SteelAmount"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelAmount,callback:function(t){e.$set(e.form,"SteelAmount",t)},expression:"form.SteelAmount"}})],1)],1):e._e(),e.getLabel("SchedulingNum")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SchedulingNum")+":",prop:"SchedulingNum"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SchedulingNum,callback:function(t){e.$set(e.form,"SchedulingNum",t)},expression:"form.SchedulingNum"}})],1)],1):e._e(),e.getLabel("ProjectName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("ProjectName")+":",prop:"ProjectName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.ProjectName,callback:function(t){e.$set(e.form,"ProjectName",t)},expression:"form.ProjectName"}})],1)],1):e._e(),e.getLabel("AreaPosition")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("AreaPosition")+":",prop:"AreaPosition"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.AreaPosition,callback:function(t){e.$set(e.form,"AreaPosition",t)},expression:"form.AreaPosition"}})],1)],1):e._e(),e.getLabel("SetupPosition")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SetupPosition")+":",prop:"SetupPosition"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SetupPosition,callback:function(t){e.$set(e.form,"SetupPosition",t)},expression:"form.SetupPosition"}})],1)],1):e._e(),e.getLabel("Create_UserName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Create_UserName")+":",prop:"Create_UserName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Create_UserName,callback:function(t){e.$set(e.form,"Create_UserName",t)},expression:"form.Create_UserName"}})],1)],1):e._e(),e.getLabel("Create_Date")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Create_Date")+":",prop:"Create_Date"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Create_Date,callback:function(t){e.$set(e.form,"Create_Date",t)},expression:"form.Create_Date"}})],1)],1):e._e(),e.getLabel("Remark")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Remark")+":",prop:"Remark"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1):e._e(),e._l(e.extendField,(function(t){return a("el-col",{key:t.Code,attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel(t.Code)+":",prop:t.Code}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form[t.Code],callback:function(a){e.$set(e.form,t.Code,a)},expression:"form[item.Code]"}})],1)],1)})),a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")])],1)],1)],2)],1):e._e()],1)},o=[]},"13fd":function(e,t,a){"use strict";a.r(t);var n=a("dda5"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,s=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"25a2":function(e,t,a){"use strict";a("036c")},"26dd0":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""},scopedSlots:e._u([{key:"Url",fn:function(t){var n=t.row;return[a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n.Url)}}},[e._v("查看")])],1)]}}])})],1)])},o=[]},"27ae":function(e,t,a){"use strict";a.r(t);var n=a("26dd0"),o=a("cba3");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"69b45a73",null);t["default"]=s.exports},"2e8a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=c,t.GetCompTypeTree=d,t.GetComponentTypeEntity=u,t.GetComponentTypeList=i,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=m,t.GetTypePageList=s,t.RestoreTemplateType=b,t.SavDeepenTemplateSetting=_,t.SaveCompTypeIdentifySetting=y,t.SaveComponentType=l,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=p;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function y(e){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function _(e){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function b(e){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},3154:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""},scopedSlots:e._u([{key:"Operation_Time",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(n.Operation_Time)+" ")])]}}])})],1)])},o=[]},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=c,t.GeAreaTrees=C,t.GetFileSync=T,t.GetInstallUnitIdNameList=P,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=I,t.GetProjectAreaTreeList=S,t.GetProjectEntity=l,t.GetProjectList=s,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=v,t.GetSchedulingPartList=D,t.IsEnableProjectMonomer=d,t.SaveProject=u,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=y,t.UpdateProjectTemplateContract=_,t.UpdateProjectTemplateOther=b;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function T(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},3592:function(e,t,a){"use strict";a.r(t);var n=a("6903"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},3677:function(e,t,a){"use strict";a.r(t);var n=a("54aa"),o=a("9ae1");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"8fa1ee02",null);t["default"]=s.exports},"46e3":function(e,t,a){},"4a5f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("7d54"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=a("6186"),l=a("8975");t.default={props:{typeEntity:{type:Object,default:function(){return{}}},paramsSteel:{type:Array,default:function(){return[]}}},data:function(){return{SteelName_Old:"",SteelAmount_Old:"",Project_Id_Old:"",Area_Id_Old:"",InstallUnit_Id_Old:"",isReadOnly:!1,btnLoading:!1,form:{Id:"",SteelName:"",SteelSpec:"",SteelMaterial:"",SteelLength:"",SteelWeight:"",SteelAmount:"",SchedulingNum:"",SteelAllWeight:"",SteelType:"",InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Area_Id:"",AreaPosition:"",Is_Component:"",Create_UserName:"",Create_Date:"",Remark:"",RKPackCount:""},extendField:[],show:!1}},created:function(){this.getFormProps()},mounted:function(){},methods:{init:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return t.isReadOnly=e.isReadOnly,a.n=1,(0,i.GetComponentImportEntity)({id:e.Id}).then((function(a){if(a.IsSucceed){t.SteelName_Old=a.Data.ImportDetail.SteelName,t.SteelAmount_Old=a.Data.ImportDetail.SteelAmount,t.Project_Id_Old=a.Data.ImportDetail.Project_Id,t.Area_Id_Old=a.Data.ImportDetail.Area_Id,t.InstallUnit_Id_Old=a.Data.ImportDetail.InstallUnit_Id,t.form=a.Data.ImportDetail;var n=a.Data.ImportExtend;n.length>0&&(t.propsList=t.propsList.concat(n));var o=JSON.parse(JSON.stringify(t.form));n.forEach((function(e){o[e.Code]=e.Value})),t.form=Object.assign({},o),t.extendField=n,t.form.RKPackCount=e.RKPackCount,t.form.SchedulingNum=e.SchedulingNum,t.form.Create_UserName=e.Create_UserName,t.form.Create_Date=(0,l.timeFormat)(e.Create_Date,"{y}-{m}-{d}"),t.form.SteelAllWeight=Math.round(t.form.SteelWeight*t.form.SteelAmount*1e3)/1e3}else t.$message({message:a.Message,type:"error"})}));case 1:return a.a(2)}}),a)})))()},getFormProps:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getColumnConfiguration(e.typeEntity.Code);case 1:a=t.v,e.propsList=a,e.show=!0;case 2:return t.a(2)}}),t)})))()},getLabel:function(e){var t=this.getPropsName(e);if(!t)try{this.rules[e].required=!1}catch(a){}return t},calculationAllWeight:function(){this.form.SteelAllWeight=Math.round(this.form.SteelWeight*this.form.SteelAmount*1e3)/1e3},getColumnConfiguration:function(e){var t=arguments;return(0,r.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"pro_component_query_list",a.n=1,(0,s.GetGridByCode)({code:n+","+e});case 1:return r=a.v,a.a(2,r.Data.ColumnList)}}),a)})))()},getPropsName:function(e){var t;return null===(t=this.propsList.find((function(t){return t.Code.toLowerCase()===e.toLowerCase()})))||void 0===t?void 0:t.Display_Name},steelTypeChange:function(e){var t=JSON.parse(e.Code);this.form.Is_Component=1!=t}}}},"4e82":function(e,t,a){"use strict";var n=a("23e7"),o=a("e330"),r=a("59ed"),i=a("7b0b"),s=a("07fa"),l=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),p=a("99f4"),h=a("1212"),g=a("ea83"),y=[],_=o(y.sort),b=o(y.push),v=c((function(){y.sort(void 0)})),S=c((function(){y.sort(null)})),P=f("sort"),C=!c((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var e,t,a,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:t+n,v:a})}for(y.sort((function(e,t){return t.v-e.v})),n=0;n<y.length;n++)t=y[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),I=v||!S||!P||!C,D=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:I},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(C)return void 0===e?_(t):_(t,e);var a,n,o=[],u=s(t);for(n=0;n<u;n++)n in t&&b(o,t[n]);d(o,D(e)),a=s(o),n=0;while(n<a)t[n]=o[n++];while(n<u)l(t,n++);return t}})},"532d":function(e,t,a){"use strict";a("46e3")},"53cd":function(e,t,a){},"54aa":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""},scopedSlots:e._u([{key:"Operation_Time",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(n.Operation_Time)+" ")])]}}])})],1)])},o=[]},"57a7":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("span"),e._v("预计交付时间："+e._s(e.Plan_Date)),a("span",[e._v("是否超时："),a("span",[e._v(e._s(1==e.Is_Over_Time?"是":"否"))])])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,border:"",stripe:""}})],1)])},o=[]},"5e09":function(e,t,a){"use strict";a.r(t);var n=a("1216"),o=a("acec");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("25a2");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"161a078c",null);t["default"]=s.exports},6180:function(e,t,a){"use strict";a.r(t);var n=a("57a7"),o=a("3592");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("d8f0");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"204c08e7",null);t["default"]=s.exports},"61ee":function(e,t,a){"use strict";a.r(t);var n=a("a059e"),o=a("13fd");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"34fc95b6",null);t["default"]=s.exports},6791:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var o=a("586a"),r=n(a("15ac")),i=n(a("0f97")),s=a("8975");t.default={components:{DynamicDataTable:i.default},mixins:[r.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Com_Code",Display_Name:"构件名称",Is_Display:!0,Sort:1},{Code:"Number",Display_Name:"数量",Is_Display:!0,Sort:2},{Code:"Weight",Display_Name:"重量",Is_Display:!0,Sort:3},{Code:"Operation_Time",Display_Name:"操作时间",Align:"right",Is_Display:!0,Sort:4}],total:0,tbData:[],rowId:0,meansType:0}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id;var a=o.EntityComponentQueryInfo;this.tbLoading=!0,a({Com_Warehoused_Id:e.Id}).then((function(e){var a=e.IsSucceed,n=(e.Data,e.Message);a?t.tbData=e.Data.map((function(e){return e.Operation_Time=(0,s.timeFormat)(e.Operation_Time,"{y}-{m}-{d}"),e})):t.$message({message:n,type:"error"}),t.tbLoading=!1})).catch((function(){t.tbLoading=!1,t.$message({message:Message,type:"error"})}))}}}},6903:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=a("6f23"),u=n(a("0f97"));t.default={components:{DynamicDataTable:u.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},columns:[{Code:"Scheduling_Status",Display_Name:"构件状态",Align:"center",Is_Display:!0,Sort:1},{Code:"Component_Count",Display_Name:"数量（件）",Align:"center",Is_Display:!0,Sort:2}],tbData:[],Plan_Date:"",Is_Over_Time:!1,rowId:0}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id,this.tbLoading=!0,(0,i.GetSchedulingList)({id:e.Id}).then((function(e){e.IsSucceed?(t.Plan_Date=(0,l.formatDate)(e.Data.Plan_Date,"yyyy-MM-dd"),t.Is_Over_Time=e.Data.Is_Over_Time,t.tbData=e.Data.Scheduling_List):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleView:function(e){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()}}}},7882:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var o=a("586a"),r=n(a("15ac")),i=n(a("0f97")),s=a("8975");t.default={components:{DynamicDataTable:i.default},mixins:[r.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Com_Code",Display_Name:"构件名称",Is_Display:!0,Sort:1},{Code:"Number",Display_Name:"数量",Is_Display:!0,Sort:2},{Code:"Weight",Display_Name:"重量",Is_Display:!0,Sort:3},{Code:"Operation_Time",Display_Name:"操作时间",Align:"right",Is_Display:!0,Sort:4}],total:0,tbData:[],rowId:0}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id;var a=o.EntityComponentQueryInfo;this.tbLoading=!0,a({Com_Plan_Id:e.Id}).then((function(e){var a=e.IsSucceed,n=(e.Data,e.Message);a?t.tbData=e.Data.map((function(e){return e.Operation_Time=(0,s.timeFormat)(e.Operation_Time,"{y}-{m}-{d}"),e})):t.$message({message:n,type:"error"}),t.tbLoading=!1})).catch((function(){t.tbLoading=!1,t.$message({message:Message,type:"error"})}))}}}},"8ef4":function(e,t,a){"use strict";a.r(t);var n=a("934b"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"91e0":function(e,t,a){"use strict";a.r(t);var n=a("6791"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"934b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("a732"),a("dca8"),a("d3b7"),a("ac1f"),a("25f0"),a("3ca3"),a("5319"),a("498a"),a("c7cd"),a("ddb0");var s=a("6186"),l=a("fd31"),u=a("586a"),c=a("3166"),d=a("2e8a"),f=n(a("1463")),m=n(a("5e09")),p=n(a("27ae")),h=n(a("61ee")),g=n(a("6180")),y=n(a("b4b4")),_=n(a("3677")),b=n(a("99bc")),v=n(a("a888")),S=n(a("333d")),P=a("8975"),C=n(a("bc3a")),I=n(a("f151")),D=a("c685");t.default={name:"PROComponentQuery",directives:{elDragDialog:v.default,sysUseType:I.default},components:{TreeDetail:f.default,Pagination:S.default,Edit:m.default,SteelMeans:p.default,ModelComponentCode:h.default,ProductionDetails:g.default,SteelOrder:y.default,SteelStock:_.default,SteelScan:b.default},data:function(){return{tablePageSize:D.tablePageSize,treeData:[],treeLoading:!0,expandedKey:"",projectName:"",searchHeight:0,searchStatus:!0,tbData:[],total:0,tbLoading:!1,queryInfo:{Page:1,PageSize:10,ParameterJson:[]},customPageSize:[10,20,50,100],installUnitIdNameList:[],customParams:{SteelNames:"",TypeId:"",Fuzzy_Search_Col:"SteelName",Fuzzy_Search:"",Fuzzy_Search_Col2:"SteelAmount",Fuzzy_Search2:"",Fuzzy_Search_Col3:"SchedulingNum",Fuzzy_Search3:"",Fuzzy_Search_Col4:"Stock_Count",Fuzzy_Search4:"",InstallUnit_Id:"",SteelNamesFormat:"",Sys_Project_Id:"",Project_Id:"",Area_Id:""},Unit:"",Proportion:0,customDialogParams:{},dialogVisible:!1,currentComponent:"",selectList:[],typeOption:[],treeParamsSteel:[],columns:[],columnsOption:[{Display_Name:"构件名称",Code:"SteelName"},{Display_Name:"构件类型",Code:"SteelType"},{Display_Name:"是否直发件",Code:"Is_Component_Status"},{Display_Name:"深化数量",Code:"SteelAmount"},{Display_Name:"深化重量",Code:"SteelAllWeight"},{Display_Name:"排产数量",Code:"SchedulingNum"},{Display_Name:"排产重量",Code:"SchedulingWeight"},{Display_Name:"生产中数量",Code:"Producing_Count"},{Display_Name:"待入库数量",Code:"WarehousedCount"},{Display_Name:"待入库重量",Code:"WarehousedWeight"},{Display_Name:"入库数量",Code:"Stock_Count"},{Display_Name:"入库重量",Code:"Stock_Weight"},{Display_Name:"发货重量",Code:"ScanWeight"},{Display_Name:"发货数量",Code:"ScanNumber"},{Display_Name:"要货结束时间",Code:"Demand_End_Date"}],title:"",width:"60%",sysUseType:void 0,InProdNum:0,InProdWeight:0,StockNum:0,StockWeight:0,ScanNum:0,ScanWeight:0,SteelType_Count:0}},computed:{typeEntity:function(){var e=this;return this.typeOption.find((function(t){return t.Id===e.customParams.TypeId}))}},watch:{"customParams.TypeId":function(e,t){t&&"0"!==t&&this.fetchData()}},mounted:function(){this.searchHeight=this.$refs.searchDom.offsetHeight+327},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTypeList();case 1:e.fetchTreeData();case 2:return t.a(2)}}),t)})))()},methods:{fetchTreeData:function(){var e=this;(0,c.GetProjectAreaTreeList)({MenuId:this.$route.meta.Id,projectName:this.projectName}).then((function(t){var a=t.Data;a.map((function(e){return 0==e.Children.length?e.Data.Is_Imported=!1:(e.Data.Is_Imported=e.Children.some((function(e){return 1==e.Data.Is_Imported})),e.Is_Directory=!0,e.Children.map((function(e){e.Children.length>0&&(e.Is_Directory=!0)}))),e})),e.expandedKey=a[0].Id,e.treeData=a,e.handleNodeClick(a[0]),e.treeLoading=!1}))},handleNodeClick:function(e){null===e.ParentNodes?(this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id,this.customParams.Project_Id=e.Data.Id,this.customParams.Area_Id=""):(this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id,this.customParams.Project_Id=e.Data.Project_Id,this.customParams.Area_Id=e.Data.Id);var t=-1==e.Id?"":e.Id;this.$refs.customParams.resetFields("InstallUnit_Id"),this.getInstallUnitIdNameList(t),this.fetchData(),this.getComponentQueryInfo()},getInstallUnitIdNameList:function(e){var t=this;""==e?this.installUnitIdNameList=[]:(0,c.GetInstallUnitIdNameList)({Area_Id:e}).then((function(e){t.installUnitIdNameList=e.Data}))},handleSearch:function(e){this.searchStatus=!1,e&&(this.$refs.customParams.resetFields(),this.customParams.Fuzzy_Search_Col="SteelName",this.customParams.Fuzzy_Search_Col2="SchedulingNum",this.customParams.Fuzzy_Search_Col3="SchedulingNum",this.customParams.Fuzzy_Search_Col4="Stock_Count",this.searchStatus=!0);var t=this.customParams.SteelNamesFormat.trim();t=t.replace(/\s+/g,"\n"),this.customParams.SteelNames=t,this.fetchData()},getComponentQueryInfo:function(){var e=this;(0,u.GetComponentQueryInfo)((0,o.default)({},this.customParams)).then((function(t){t.IsSucceed?(e.InProdNum=Math.round(1e3*t.Data.InProdNum)/1e3,e.InProdWeight=Math.round(1e3*t.Data.InProdWeight)/1e3,e.StockNum=Math.round(1e3*t.Data.StockNum)/1e3,e.StockWeight=Math.round(1e3*t.Data.StockWeight)/1e3,e.ScanNum=Math.round(1e3*t.Data.ScanNum)/1e3,e.ScanWeight=Math.round(1e3*t.Data.ScanWeight)/1e3,e.SteelType_Count=Math.round(1e3*t.Data.SteelType_Count)/1e3):e.$message({message:t.Message,type:"error"})})).catch((function(){}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,s.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.customParams.TypeId})).Code}).then((function(e){var n=e.IsSucceed,o=e.Data,r=e.Message;if(n){if(!o)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,o.Grid);var i=o.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return"SteelName"===e.Code&&(e.fixed="left"),e})),t.queryInfo.PageSize=+o.Grid.Row_Number||20,a(t.columns)}else t.$message({message:r,type:"error"})}))}))},getComponentQueryPageInfo:function(){var e=this;return new Promise((function(t){var a=u.GetComponentQueryPageInfo;a((0,o.default)((0,o.default)({},e.queryInfo),e.customParams)).then((function(a){a.IsSucceed?(e.tbData=a.Data.Data.map((function(e){return e.Finish_Date=(0,P.timeFormat)(e.Finish_Date,"{y}-{m}-{d}"),e.Demand_End_Date=(0,P.timeFormat)(e.Demand_End_Date,"{y}-{m}-{d}"),e})),e.queryInfo.PageSize=a.Data.PageSize,e.total=a.Data.TotalCount,e.selectList=[]):e.$message({message:a.Message,type:"error"}),t()}))}))},fetchData:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("pro_component_query_list");case 1:e.tbLoading=!0,Promise.all([e.getComponentQueryPageInfo()]).then((function(t){e.tbLoading=!1}));case 2:return t.a(2)}}),t)})))()},changePage:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),Promise.all([e.getComponentQueryPageInfo()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},getTypeList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.customParams.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id),e.getCompTypeTree(e.typeOption[0].Code)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getCompTypeTree:function(e){var t=this;this.loading=!0,(0,d.GetCompTypeTree)({professional:e}).then((function(e){e.IsSucceed?t.treeParamsSteel=e.Data:(t.$message({message:e.Message,type:"error"}),t.treeData=[])})).finally((function(e){t.loading=!1}))},handleView:function(e){var t=this;this.width="45%",this.generateComponent("查看构件","Edit"),this.$nextTick((function(a){e.isReadOnly=!0,t.$refs["content"].init(e)}))},handleViewSH:function(e,t){var a=this;this.width="40%",this.generateComponent("查看深化资料","SteelMeans"),this.$nextTick((function(n){a.$refs["content"].init(e,t)}))},handleViewOrder:function(e){var t=this;this.width="40%",this.generateComponent("排产详情","SteelOrder"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleViewStock:function(e){var t=this;this.width="40%",this.generateComponent("入库详情","SteelStock"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleViewScan:function(e){var t=this;this.width="40%",this.generateComponent("发货详情","SteelScan"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleSteelMeans:function(e){this.handleViewSH(e,1)},handleViewModel:function(e){var t=this;this.width="40%",this.generateComponent("模型构件唯一码列表","ModelComponentCode"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleViewScheduling:function(e){var t=this;this.width="30%",this.generateComponent("生产详情","ProductionDetails"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},getFile:function(e){return new Promise((function(t,a){(0,C.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))},handleClose:function(){this.dialogVisible=!1},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0}}}},"99bc":function(e,t,a){"use strict";a.r(t);var n=a("cad3"),o=a("91e0");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"2098aaf4",null);t["default"]=s.exports},"9ae1":function(e,t,a){"use strict";a.r(t);var n=a("a7f3"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},a059e:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""}})],1)])},o=[]},a7f3:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var o=a("586a"),r=n(a("15ac")),i=n(a("0f97")),s=a("8975");t.default={components:{DynamicDataTable:i.default},mixins:[r.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Com_Code",Display_Name:"构件名称",Is_Display:!0,Sort:1},{Code:"Number",Display_Name:"数量",Is_Display:!0,Sort:2},{Code:"Weight",Display_Name:"重量",Is_Display:!0,Sort:3},{Code:"Warehouse_Name",Display_Name:"仓库",Is_Display:!0,Sort:4},{Code:"Location_Name",Display_Name:"库位",Is_Display:!0,Sort:5},{Code:"Operation_Time",Display_Name:"操作时间",Align:"right",Is_Display:!0,Sort:6}],total:0,tbData:[],rowId:0,meansType:0}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id;var a=o.EntityComponentQueryInfo;this.tbLoading=!0,a({Com_Stock_Id:e.Id}).then((function(e){var a=e.IsSucceed,n=(e.Data,e.Message);a?t.tbData=e.Data.map((function(e){return e.Operation_Time=(0,s.timeFormat)(e.Operation_Time,"{y}-{m}-{d}"),e})):t.$message({message:n,type:"error"}),t.tbLoading=!1})).catch((function(){t.tbLoading=!1,t.$message({message:Message,type:"error"})}))}}}},a888:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("d565")),r=function(e){e.directive("el-drag-dialog",o.default)};window.Vue&&(window["el-drag-dialog"]=o.default,Vue.use(r)),o.default.install=r;t.default=o.default},acec:function(e,t,a){"use strict";a.r(t);var n=a("4a5f"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},b4b4:function(e,t,a){"use strict";a.r(t);var n=a("3154"),o=a("0ebc");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"c99d7f76",null);t["default"]=s.exports},b4fa:function(e,t,a){"use strict";a.r(t);var n=a("d36d"),o=a("8ef4");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("532d");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"19ef6320",null);t["default"]=s.exports},b9eb:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2"),a("d3b7"),a("25f0");var o=n(a("4360"));function r(e,t){var a=t.value,n=o.default.getters&&o.default.getters.sysUseType;if("[object Number]"!==Object.prototype.toString.call(a)||"number"!==typeof n)throw new Error('need sysUseType! Like v-sys-use-type="123"');a!==n&&e.parentNode&&e.parentNode.removeChild(e)}t.default={inserted:function(e,t){r(e,t)},update:function(e,t){r(e,t)}}},cad3:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""},scopedSlots:e._u([{key:"Operation_Time",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(n.Operation_Time)+" ")])]}}])})],1)])},o=[]},cba3:function(e,t,a){"use strict";a.r(t);var n=a("ccaf"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},ccaf:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b");var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=n(a("0f97")),u=a("0e9a");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Type",Display_Name:"文件类型",Is_Display:!0,Sort:1},{Code:"Title",Display_Name:"文件名称",Is_Display:!0,Sort:2},{Code:"Url",Display_Name:"操作",Align:"right",Is_Display:!0,Sort:3}],total:0,tbData:[],rowId:0,meansType:0}},mounted:function(){},methods:{init:function(e,t){var a=this;this.rowId=e.Id,this.meansType=t;var n=0==t?i.GetComponentDeepenFileList:i.GetPartDeepenFileList;this.tbLoading=!0,n({id:e.Id}).then((function(e){e.IsSucceed?a.tbData=e.Data:a.$message({message:e.Message,type:"error"}),a.tbLoading=!1}))},handleView:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n,r,i,s,l;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:if(n=e.lastIndexOf("."),r=e.lastIndexOf("?"),i=e.substring(n,r),".pdf"!==i){a.n=1;break}window.open(e+"#toolbar=0","_blank"),a.n=4;break;case 1:if(".xls"!==i&&".xlsx"!==i&&".doc"!==i&&".docx"!==i){a.n=3;break}return s=t.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),a.n=2,(0,u.fileToPdf)(e,!1);case 2:s.close(),a.n=4;break;case 3:".dwg"===i?(l=e.split(".com/"),l[1]=encodeURIComponent(l[1]),e=l.join(".com/"),window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+e,"_blank")):(".pdf"===i&&(e+="#toolbar=0"),window.open(e,"_blank"));case 4:return a.a(2)}}),a)})))()}}}},d36d:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-row",{staticClass:"h100",attrs:{type:"flex",gutter:15}},[a("el-col",{staticStyle:{"padding-left":"0"},attrs:{span:4}},[a("div",{staticClass:"fff h100 cs-scroll",staticStyle:{padding:"16px 10px 16px 16px","border-radius":"4px"}},[a("div",[a("el-input",{attrs:{placeholder:"请输入项目名称"},model:{value:e.projectName,callback:function(t){e.projectName=t},expression:"projectName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.fetchTreeData},slot:"append"})],1)],1),a("tree-detail",{ref:"tree",staticStyle:{"margin-top":"20px",height:"calc(100% - 36px)"},attrs:{icon:"icon-folder",loading:e.treeLoading,"tree-data":e.treeData,"show-status":"","show-detail":"","expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick}})],1)]),a("el-col",{staticStyle:{"padding-right":"0"},attrs:{span:20}},[a("div",{staticClass:"container"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.customParams,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"查询条件1",prop:"Fuzzy_Search"}},[a("el-input",{staticClass:"input-with-select",attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.customParams.Fuzzy_Search,callback:function(t){e.$set(e.customParams,"Fuzzy_Search",t)},expression:"customParams.Fuzzy_Search"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择",clearable:""},slot:"prepend",model:{value:e.customParams.Fuzzy_Search_Col,callback:function(t){e.$set(e.customParams,"Fuzzy_Search_Col",t)},expression:"customParams.Fuzzy_Search_Col"}},e._l(e.columnsOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1),a("el-form-item",{attrs:{label:"查询条件2",prop:"Fuzzy_Search2"}},[a("el-input",{staticClass:"input-with-select",attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.customParams.Fuzzy_Search2,callback:function(t){e.$set(e.customParams,"Fuzzy_Search2",t)},expression:"customParams.Fuzzy_Search2"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择",clearable:""},slot:"prepend",model:{value:e.customParams.Fuzzy_Search_Col2,callback:function(t){e.$set(e.customParams,"Fuzzy_Search_Col2",t)},expression:"customParams.Fuzzy_Search_Col2"}},e._l(e.columnsOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1),a("el-form-item",{attrs:{label:"查询条件3",prop:"Fuzzy_Search3"}},[a("el-input",{staticClass:"input-with-select",attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.customParams.Fuzzy_Search3,callback:function(t){e.$set(e.customParams,"Fuzzy_Search3",t)},expression:"customParams.Fuzzy_Search3"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择",clearable:""},slot:"prepend",model:{value:e.customParams.Fuzzy_Search_Col3,callback:function(t){e.$set(e.customParams,"Fuzzy_Search_Col3",t)},expression:"customParams.Fuzzy_Search_Col3"}},e._l(e.columnsOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1),a("el-form-item",{attrs:{label:"查询条件4",prop:"Fuzzy_Search4"}},[a("el-input",{staticClass:"input-with-select",attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.customParams.Fuzzy_Search4,callback:function(t){e.$set(e.customParams,"Fuzzy_Search4",t)},expression:"customParams.Fuzzy_Search4"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择",clearable:""},slot:"prepend",model:{value:e.customParams.Fuzzy_Search_Col4,callback:function(t){e.$set(e.customParams,"Fuzzy_Search_Col4",t)},expression:"customParams.Fuzzy_Search_Col4"}},e._l(e.columnsOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1),a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"250px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.customParams.InstallUnit_Id,callback:function(t){e.$set(e.customParams,"InstallUnit_Id",t)},expression:"customParams.InstallUnit_Id"}},e._l(e.installUnitIdNameList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"构件名称",prop:"SteelNamesFormat"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请输入（空格间隔筛选多个）"},model:{value:e.customParams.SteelNamesFormat,callback:function(t){e.$set(e.customParams,"SteelNamesFormat",t)},expression:"customParams.SteelNamesFormat"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"8px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSearch()}}},[e._v("搜索")]),a("el-button",{on:{click:function(t){return e.handleSearch("reset")}}},[e._v("重置")])],1)],1)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-component-num"},[a("span",[e._v("当前生产中构件："),a("span",[e._v("总数量"),a("i",[e._v(e._s(e.InProdNum))])]),e._v("总重量"),a("i",[e._v(e._s(e.InProdWeight))])]),e._v(" "),a("span",[e._v("当前库存中构件："),a("span",[e._v("总数量"),a("i",[e._v(e._s(e.StockNum))])]),e._v("总重量"),a("i",[e._v(e._s(e.StockWeight))])])]),a("div",{staticClass:"cs-component-num"},[a("span",[e._v("已发运构件："),a("span",[e._v("总数量"),a("i",[e._v(e._s(e.ScanNum))])]),e._v("总重量"),a("i",[e._v(e._s(e.ScanWeight))])]),e._v(" "),a("span",[e._v("构件类型总数："),a("span",[e._v("总数量"),a("i",[e._v(e._s(e.SteelType_Count))])])])]),a("div",{staticClass:"tb-container",style:{height:"calc(100vh - "+e.searchHeight+"px)"}},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,width:t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return["SteelAmount"==t.Code?a("div",[1==o.Is_Component_Status?a("span",{staticStyle:{color:"#298DFF"}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" 件")]):a("span",{staticStyle:{color:"#298DFF",cursor:"pointer"},on:{click:function(t){return e.handleViewModel(o)}}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" 件")])]):"SchedulingNum"==t.Code?a("div",[o[t.Code]?a("span",{staticStyle:{color:"#298DFF",cursor:"pointer"},on:{click:function(t){return e.handleViewScheduling(o)}}},[e._v(e._s(o[t.Code]+" 件"))]):a("span",[e._v("-")])]):"Order_Date"==t.Code?a("div",[o["SchedulingNum"]?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleViewOrder(o)}}},[e._v("查看")]):a("span",[e._v("-")])],1):"Stock_In_Date"==t.Code?a("div",[o["Stock_Count"]?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleViewStock(o)}}},[e._v("查看")]):a("span",[e._v("-")])],1):"ScanDate"==t.Code?a("div",[o["ScanNumber"]?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleViewScan(o)}}},[e._v("查看")]):a("span",[e._v("-")])],1):"SH"==t.Code?a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleViewSH(o,0)}}},[e._v("查看")])],1):"Finish_Date"==t.Code||"Demand_End_Date"==t.Code?a("div",[a("span",[e._v(e._s("01-01-01"==o[t.Code]?"-":o[t.Code]))])]):a("div",[a("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])])]}}],null,!0)})})),a("vxe-column",{attrs:{fixed:"right",align:"right",title:"操作",width:"100","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("查看详情")])]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"data-info"}),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)])])])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":e.selectList,"custom-params":e.customDialogParams,"type-id":e.customParams.TypeId,"type-entity":e.typeEntity,"params-steel":e.treeParamsSteel,"project-id":e.customParams.Project_Id,"sys-project-id":e.customParams.Sys_Project_Id},on:{close:e.handleClose,refresh:e.fetchData,checkSteelMeans:e.handleSteelMeans}})],1):e._e()],1)},o=[]},d565:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319");t.default={bind:function(e,t,a){var n=e.querySelector(".el-dialog__header"),o=e.querySelector(".el-dialog");n.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var r=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();n.onmousedown=function(e){var t=e.clientX-n.offsetLeft,i=e.clientY-n.offsetTop,s=o.offsetWidth,l=o.offsetHeight,u=document.body.clientWidth,c=document.body.clientHeight,d=o.offsetLeft,f=u-o.offsetLeft-s,m=o.offsetTop,p=c-o.offsetTop-l,h=r(o,"left"),g=r(o,"top");h.includes("%")?(h=+document.body.clientWidth*(+h.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(h=+h.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var n=e.clientX-t,r=e.clientY-i;-n>d?n=-d:n>f&&(n=f),-r>m?r=-m:r>p&&(r=p),o.style.cssText+=";left:".concat(n+h,"px;top:").concat(r+g,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}},d8f0:function(e,t,a){"use strict";a("53cd")},dca8:function(e,t,a){"use strict";var n=a("23e7"),o=a("bb2f"),r=a("d039"),i=a("861d"),s=a("f183").onFreeze,l=Object.freeze,u=r((function(){l(1)}));n({target:"Object",stat:!0,forced:u,sham:!o},{freeze:function(e){return l&&i(e)?l(s(e)):e}})},dda5:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=n(a("0f97"));t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"ExtensionName",Display_Name:"模型唯一ID",Align:"center",Is_Display:!0,Sort:1},{Code:"SerialNumber",Display_Name:"管理编号",Align:"center",Is_Display:!0,Sort:2},{Code:"TopHeight",Display_Name:"顶标高",Align:"center",Is_Display:!0,Sort:3},{Code:"BottomHeight",Display_Name:"底标高",Align:"center",Is_Display:!0,Sort:4},{Code:"Axis",Display_Name:"位置轴线",Align:"center",Is_Display:!0,Sort:5}],total:0,tbData:[],rowId:0}},mounted:function(){},methods:{init:function(e,t){var a=this;this.rowId=e.Id,this.tbLoading=!0,(0,i.GetComponentModelList)({id:e.Id}).then((function(e){e.IsSucceed?a.tbData=e.Data:a.$message({message:e.Message,type:"error"}),a.tbLoading=!1}))},handleView:function(e){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()}}}},e41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=c,t.GetPartsList=s,t.GetProjectAreaTreeList=r,t.ImportParts=u,t.SaveProjectAreaSort=i;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},f151:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("b9eb")),r=function(e){e.directive("permission",o.default)};window.Vue&&(window["sysUseType"]=o.default,Vue.use(r)),o.default.install=r;t.default=o.default}}]);