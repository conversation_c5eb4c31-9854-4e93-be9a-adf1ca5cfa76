(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-30ef07d4"],{1242:function(e,t,a){"use strict";a("3602")},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,o=e.Data,s=e.Message;if(n){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,o.Grid),l=a?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+o.Grid.Row_Number||r.tablePageSize[0]),i(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var r=this.columns[n];if(r.Code===t){a.Type=r.Type,a.Filter_Type=r.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1a87":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b0c0");var r=n(a("c14f")),i=n(a("1da1")),o=n(a("5530")),s=n(a("5fda")),l=a("2f62");t.default={name:"PROTaskList",components:{MainPage:s.default},data:function(){return{pgLoading:!1,activeName:"com"}},computed:(0,o.default)((0,o.default)({isCom:function(){return"com"===this.activeName}},(0,l.mapGetters)("tenant",["isVersionFour"])),{},{hasUnitPart:function(){return this.isVersionFour}}),methods:{handleClick:function(e,t){},changeTab:function(e){var t=this;return(0,i.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return t.pgLoading=!0,t.activeName=e.name,a.n=1,t.getTbConfigInfo();case 1:t.fetchData(1);case 2:return a.a(2)}}),a)})))()},getTbConfigInfo:function(){},fetchData:function(){}}}},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=c,t.GeAreaTrees=T,t.GetFileSync=j,t.GetInstallUnitIdNameList=I,t.GetNoBindProjectList=h,t.GetPartDeepenFileList=C,t.GetProjectAreaTreeList=v,t.GetProjectEntity=l,t.GetProjectList=s,t.GetProjectPageList=o,t.GetProjectTemplate=p,t.GetPushProjectPageList=b,t.GetSchedulingPartList=k,t.IsEnableProjectMonomer=d,t.SaveProject=u,t.UpdateProjectTemplateBase=_,t.UpdateProjectTemplateContacts=P,t.UpdateProjectTemplateContract=g,t.UpdateProjectTemplateOther=y;var r=n(a("b775")),i=n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:i.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:i.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function j(e){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},3602:function(e,t,a){},"4e82":function(e,t,a){"use strict";var n=a("23e7"),r=a("e330"),i=a("59ed"),o=a("7b0b"),s=a("07fa"),l=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),h=a("99f4"),p=a("1212"),_=a("ea83"),P=[],g=r(P.sort),y=r(P.push),b=c((function(){P.sort(void 0)})),v=c((function(){P.sort(null)})),I=f("sort"),T=!c((function(){if(p)return p<70;if(!(m&&m>3)){if(h)return!0;if(_)return _<603;var e,t,a,n,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)P.push({k:t+n,v:a})}for(P.sort((function(e,t){return t.v-e.v})),n=0;n<P.length;n++)t=P[n].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),C=b||!v||!I||!T,k=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:C},{sort:function(e){void 0!==e&&i(e);var t=o(this);if(T)return void 0===e?g(t):g(t,e);var a,n,r=[],u=s(t);for(n=0;n<u;n++)n in t&&y(r,t[n]);d(r,k(e)),a=s(r),n=0;while(n<a)t[n]=r[n++];while(n<u)l(t,n++);return t}})},"4ee0":function(e,t,a){},"5fda":function(e,t,a){"use strict";a.r(t);var n=a("6f16"),r=a("7e46");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("1242");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"067e3e6f",null);t["default"]=s.exports},"6f16":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.Loading,expression:"Loading"}],staticClass:"h100",attrs:{"element-loading-text":"数据生成中"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",attrs:{model:e.queryForm,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"任务单号",prop:"Task_Code"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.queryForm.Task_Code,callback:function(t){e.$set(e.queryForm,"Task_Code",t)},expression:"queryForm.Task_Code"}})],1),a("el-form-item",{attrs:{label:"排产单号",prop:"Schduling_Code"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.queryForm.Schduling_Code,callback:function(t){e.$set(e.queryForm,"Schduling_Code",t)},expression:"queryForm.Schduling_Code"}})],1),a("el-form-item",{attrs:{label:"项目名称",prop:"projectId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.queryForm.projectId,callback:function(t){e.$set(e.queryForm,"projectId",t)},expression:"queryForm.projectId"}},e._l(e.projectOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),!e.isPart||e.isPart&&e.hasUnitPart?a("el-form-item",{attrs:{label:"区域名称",prop:"areaId"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{disabled:!e.queryForm.projectId,"select-params":{clearable:!0},"tree-params":e.treeParams},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.queryForm.areaId,callback:function(t){e.$set(e.queryForm,"areaId",t)},expression:"queryForm.areaId"}})],1):e._e(),!e.isPart||e.isPart&&e.hasUnitPart?a("el-form-item",{attrs:{label:"批次",prop:"install"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.queryForm.areaId,clearable:"",placeholder:"请选择"},model:{value:e.queryForm.install,callback:function(t){e.$set(e.queryForm,"install",t)},expression:"queryForm.install"}},e._l(e.installOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"状态",prop:"Task_Status"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.queryForm.Task_Status,callback:function(t){e.$set(e.queryForm,"Task_Status",t)},expression:"queryForm.Task_Status"}},[a("el-option",{attrs:{label:"已完成",value:1}}),a("el-option",{attrs:{label:"未完成",value:0}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{type:"primary",disabled:!e.selectArray.length},on:{click:e.handleExport}},[e._v("导出任务单列表")]),e.isCom?[a("el-dropdown",{attrs:{trigger:"click",placement:"bottom-start"},on:{command:function(t){return e.handleCommand(t,1)}}},[a("el-button",{attrs:{type:"primary",disabled:!e.selectArray.length}},[e._v("导出任务单 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"name"}},[e._v("构件名称导出")]),a("el-dropdown-item",{attrs:{command:"code"}},[e._v("构件号合并导出")])],1)],1)]:e._e(),e.isUnitPart?[a("el-button",{attrs:{disabled:!e.selectArray.length,type:"success"},on:{click:function(t){return e.handleCommand("name")}}},[e._v("导出任务单")])]:e._e(),e.isPart?[a("el-button",{attrs:{disabled:!e.selectArray.length,type:"success"},on:{click:function(t){return e.handleCommand("name")}}},[e._v("导出任务单")])]:e._e()],2),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper",attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.getSelectVal,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange},scopedSlots:e._u([{key:"Task_Code",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleView(n)}}},[e._v(e._s(n.Task_Code))])]}},{key:"Task_Status",fn:function(t){var n=t.row;return[0===n.Task_Status?a("el-tag",{attrs:{type:"danger"}},[e._v("未完成")]):a("el-tag",{attrs:{type:"success"}},[e._v("已完成")])]}},{key:"op",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("查看 ")]),e.isCom?[a("el-dropdown",{staticStyle:{"margin-left":"12px"},attrs:{trigger:"click",placement:"bottom-start"},on:{command:function(t){return e.printSelected(t,n)}}},[a("el-button",[e._v("打印任务单 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"name"}},[e._v("构件名称打印")]),a("el-dropdown-item",{attrs:{command:"code"}},[e._v("构件号合并打印")])],1)],1)]:e._e(),e.isUnitPart?[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.printSelected("name",n)}}},[e._v("打印任务单")])]:e._e(),e.isPart?[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.printSelected("name",n)}}},[e._v("打印任务单")])]:e._e()]}}])})],1)],1)])},r=[]},"7e46":function(e,t,a){"use strict";a.r(t);var n=a("f368"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"83b8":function(e,t,a){"use strict";a.r(t);var n=a("1a87"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"9b4f":function(e,t,a){"use strict";a.r(t);var n=a("e08a"),r=a("83b8");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("a861");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"d0e35804",null);t["default"]=s.exports},a861:function(e,t,a){"use strict";a("4ee0")},aec3:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("7d54"),a("d3b7"),a("159b");var n=a("3166"),r=a("f2f6"),i=a("8975");t.default={data:function(){return{queryForm:{projectId:"",install:"",areaId:""},projectOption:[],areaList:[],installOption:[],treeParams:{data:[],filterable:!1,clickParent:!0,props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}}}},mounted:function(){this.getProjectOption()},methods:{getAreaList:function(){var e=this;(0,n.GeAreaTrees)({projectId:this.queryForm.projectId,Area_Id:this.queryForm.install}).then((function(t){if(t.IsSucceed){var a=t.Data;e.setDisabledTree(a),e.treeParams.data=a,e.$nextTick((function(t){e.$refs.treeSelect.treeDataUpdateFun(a)}))}else e.$message({message:t.Message,type:"error"})}))},getInstall:function(){var e=this;(0,r.GetInstallUnitPageList)({Area_Id:this.queryForm.areaId,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.installOption=t.Data.Data.filter((function(e){return e.Id})):e.$message({message:t.Message,type:"error"})}))},projectChange:function(){this.queryForm.areaId="",this.queryForm.install="",this.queryForm.projectId&&this.getAreaList()},areaChange:function(e){if(this.areaIsImport=!1,this.queryForm.install="",this.queryForm.areaId&&!this.areaIsImport){this.getInstall();var t=null===e||void 0===e?void 0:e.Data,a=t.Demand_Begin_Date,n=t.Demand_End_Date;this.getAreaTime((0,i.timeFormat)(a),(0,i.timeFormat)(n))}},installChange:function(){this.getInstall()},areaClear:function(){this.queryForm.install=""},getProjectOption:function(){var e=this;(0,n.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.projectOption=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var a=e.Children;a&&a.length?e.disabled=!0:(e.disabled=!1,t.setDisabledTree(a))}))},getAreaTime:function(){}}}},e08a:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("el-tabs",{on:{"tab-click":e.changeTab},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"构件任务单",name:"com"}}),a("el-tab-pane",{attrs:{label:"部件任务单",name:"unitPart"}}),a("el-tab-pane",{attrs:{label:"零件任务单",name:"part"}})],1),a("div",{staticClass:"main-wrapper"},[a("MainPage",{key:e.activeName,tag:"component",attrs:{"has-unit-part":e.hasUnitPart,"page-type":e.activeName}})],1)],1)},r=[]},f2f6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=l,t.CheckPlanTime=u,t.DeleteInstallUnit=m,t.GetCompletePercent=g,t.GetEntity=b,t.GetInstallUnitAllInfo=d,t.GetInstallUnitComponentPageList=P,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=c,t.GetInstallUnitList=s,t.GetInstallUnitPageList=o,t.GetProjectInstallUnitList=y,t.ImportInstallUnit=p,t.InstallUnitInfoTemplate=h,t.SaveInstallUnit=_,t.SaveOhterSourceInstallUnit=v;var r=n(a("b775")),i=n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function u(e){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function c(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function d(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function m(e){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function h(e){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function p(e){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:i.default.stringify(e)})}function v(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},f368:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),i=n(a("c14f")),o=n(a("1da1"));a("c740"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("159b"),a("ddb0");var s=n(a("aec3")),l=n(a("15ac")),u=n(a("0f97")),c=a("7f9d"),d=n(a("2082")),f=a("8975"),m=a("ed08");a("2f62"),t.default={components:{DynamicDataTable:u.default},mixins:[s.default,l.default,d.default],props:{pageType:{type:String,default:"com"},hasUnitPart:{type:Boolean,default:!1}},data:function(){return{loading:!1,addPageArray:[{path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-04f2d649").then(a.bind(null,"4bae"))},name:"PROTaskListDetail",meta:{title:"任务单详情"}},{path:this.$route.path+"/detailPrint",hidden:!0,component:function(){return a.e("chunk-1cd19294").then(a.bind(null,"753e"))},name:"PROTaskListDetailPrint",meta:{title:"打印任务单详情"}}],queryForm:{Task_Status:void 0,Task_Code:void 0,Schduling_Code:void 0},queryInfo:{Page:1,PageSize:20},tbConfig:{Op_Width:200,IS_ToolTip:!0},Loading:!1,tbLoading:!1,columns:[],tbData:[],selectArray:[],total:0,search:function(){return{}}}},computed:{isPart:function(){return"part"===this.pageType},isCom:function(){return"com"===this.pageType},isUnitPart:function(){return"unitPart"===this.pageType}},created:function(){this.tbConfig.Op_Width=this.isCom?200:140},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.search=(0,m.debounce)(e.fetchData,800,!0),t.n=1,e.getTableConfig(e.isCom||e.isUnitPart?"PROComTaskList":"PROPartTaskList");case 1:e.hasUnitPart||(a=["Project_Name","Area_Name","InstallUnit_Name"],a.forEach((function(t){var a=e.columns.findIndex((function(e){return e.Code===t}));-1!==a&&e.columns.splice(a,1)}))),e.isPart&&(n=e.columns.findIndex((function(e){return"Process_Finish_Date"===e.Code})),-1!==n&&e.columns.splice(n,1)),e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{getSelectVal:function(e){this.selectArray=e},handleExport:function(){var e=this,t=this.columns.map((function(e){return e.Code})),n=i(t,this.selectArray),r=this.columns.map((function(e){return e.Display_Name}));function i(e,t){return t.map((function(t){return e.map((function(e){return"Order_Date"===e?(0,f.timeFormat)(t[e]):"Task_Status"===e?0===t[e]?"未完成":"已完成":t[e]}))}))}a.e("chunk-2d0cc0b6").then(a.t.bind(null,"4bf8",7)).then((function(t){t.export_json_to_excel({header:r,data:n,filename:"".concat(e.isCom?"构件任务单":e.isUnitPart?"部件任务单":"零件任务单"),autoWidth:!0,bookType:"xlsx"})}))},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0,(0,c.GetTeamTaskPageList)((0,r.default)((0,r.default)({},this.queryInfo),{},{Task_Code:this.queryForm.Task_Code,Schduling_Code:this.queryForm.Schduling_Code,Project_Id:this.queryForm.projectId,Task_Status:this.queryForm.Task_Status,Area_Id:this.queryForm.areaId,InstallUnit_Id:this.queryForm.install,Process_Type:this.isCom?2:this.isPart?1:3})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.map((function(e){return e.Finish_Date=e.Finish_Date?(0,m.parseTime)(new Date(e.Finish_Date),"{y}-{m}-{d}"):e.Finish_Date,e.Order_Date=e.Order_Date?(0,m.parseTime)(new Date(e.Order_Date),"{y}-{m}-{d}"):e.Order_Date,e.Task_Finish_Date=e.Task_Finish_Date?(0,m.parseTime)(new Date(e.Task_Finish_Date),"{y}-{m}-{d}"):e.Task_Finish_Date,e.Process_Finish_Date=e.Process_Finish_Date?(0,m.parseTime)(new Date(e.Process_Finish_Date),"{y}-{m}-{d}"):e.Process_Finish_Date,e})),t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.tbLoading=!1}))},handleReset:function(){this.$refs["form"].resetFields(),this.search(1)},handleCommand:function(e,t){var a=this,n=this.selectArray.map((function(e){return{Task_Code:e.Task_Code,Working_Team_Id:e.Working_Team_Id}})),r=1===this.selectArray.length?this.selectArray[0].Working_Team_Id:"",i=1===this.selectArray.length?this.selectArray[0].Task_Code:"";this.Loading=!0,(0,c.ExportTaskCodeDetails)({Process_Type:this.isCom?2:this.isPart?1:3,Working_Team_Id:r,Task_Code:i,Is_Merge:"code"===e,ExportTeamTaskModel:n}).then((function(e){e.IsSucceed?(a.$message({message:"导出成功",type:"success"}),window.open((0,m.combineURL)(a.$baseUrl,e.Data),"_blank")):a.$message({message:e.Message,type:"error"})})).finally((function(e){a.Loading=!1}))},printSelected:function(e,t){var a={Task_Code:t.Task_Code,Project_Name:t.Project_Name,Area_Name:t.Area_Name,InstallUnit_Name:t.InstallUnit_Name,Schduling_Code:t.Schduling_Code,Finish_Date:t.Finish_Date,Order_Date:t.Order_Date,Task_Finish_Date:t.Task_Finish_Date,Process_Finish_Date:t.Process_Finish_Date,Working_Team_Name:t.Working_Team_Name,Working_Process_Name:t.Working_Process_Name,Working_Team_Id:t.Working_Team_Id};this.$router.push({name:"PROTaskListDetailPrint",query:{type:this.pageType,command:e,pg_redirect:this.$route.name,other:encodeURIComponent(JSON.stringify(a))}})},handleView:function(e){var t=e.Task_Code,a=e.Project_Name,n=e.Area_Name,r=e.InstallUnit_Name,i=e.Schduling_Code,o=e.Finish_Date,s=e.Order_Date,l=e.Task_Finish_Date,u=e.Process_Finish_Date,c=e.Working_Team_Name,d=e.Working_Process_Name;this.$router.push({name:"PROTaskListDetail",query:{id:e.Task_Code,type:this.pageType,tid:e.Working_Team_Id,pg_redirect:this.$route.name,other:encodeURIComponent(JSON.stringify({Task_Code:t,Project_Name:a,Area_Name:n,InstallUnit_Name:r,Schduling_Code:i,Finish_Date:o,Process_Finish_Date:u,Order_Date:s,Finish_Date2:l,Working_Team_Name:c,Working_Process_Name:d}))}})}}}}}]);