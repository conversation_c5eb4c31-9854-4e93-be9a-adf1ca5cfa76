(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-67e5b337"],{"527d":function(e,n,t){},5739:function(e,n,t){"use strict";t.d(n,"a",(function(){return a})),t.d(n,"b",(function(){return r}));var a=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("div",{staticClass:"container abs100"},[t("History")],1)},r=[]},"5a3a":function(e,n,t){"use strict";t.r(n);var a=t("5ce7"),r=t.n(a);for(var u in a)["default"].indexOf(u)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(u);n["default"]=r.a},"5ce7":function(e,n,t){"use strict";var a=t("4ea4").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=a(t("ec1a"));n.default={name:"PROComTransferHistory",components:{History:r.default},provide:{pageType:"com"}}},"6a67":function(e,n,t){"use strict";t.r(n);var a=t("5739"),r=t("5a3a");for(var u in r)["default"].indexOf(u)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(u);t("8bda");var c=t("2877"),i=Object(c["a"])(r["default"],a["a"],a["b"],!1,null,"01655cc8",null);n["default"]=i.exports},"8bda":function(e,n,t){"use strict";t("527d")}}]);