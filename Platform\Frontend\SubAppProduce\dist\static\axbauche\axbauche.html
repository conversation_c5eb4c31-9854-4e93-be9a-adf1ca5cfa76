﻿<html>

<head>
	<!--<meta http-equiv="Content-Type" content="text/html; charset=gb2312">-->
	<meta http-equiv="X-UA-Compatible" content="IE=10"/>
	<title>SDK 演示</title>
	<link href="./css/test.css" rel="stylesheet" >

	<script src="../bimviz/third/jquery/jquery-2.1.1.min.js"></script>
	<script src="../config/index.js"></script>
	<script src="../bimviz/third/jquery.cookie.js"></script>
	<script src="../bimviz/third/jquery-ui.min.js"></script>
	<script src="../bimviz/third/layer-v3.5.1/layer/layer.js"></script>
	<script src="../bimviz/third/framework-ui.js"></script>
	<script src="../bimviz/services/baas.js"></script>
	<script src="js/WsUtil.js" type="text/javascript" charset="utf-8"></script>
	<script src="js/OcxUtil.js" type="text/javascript" charset="utf-8"></script>
	<script src="js/axCam_Ocx.js" type="text/javascript" charset="utf-8" for="axCam_Ocx" event="MessageCallback(type,str)"></script>
	<script src="js/axCam_Ocx2.js" type="text/javascript" charset="utf-8" for="axCam_Ocx2" event="MessageCallback(type,str)"></script>
	<!--  <script type="text/javascript" for="axCam_Ocx" event="MessageCallback(type,str)"> -->
		<script >

    var isIE = true;        //是否IE
    var isSecondDev = false; //是否有两个canvas显示视频
    var camidMain = 0;    //主头ID
    var camidSub = 0;     //副头ID
    var typeid='';

  //页面关闭时,停止摄像头,停止身份证读卡
  window.onbeforeunload = function(event) { 
  	StopICWork();
  	CloseFinger();
  	CloseCam(); 
  	FaceCheckUninit();
  	if(isSecondDev){
  	CloseCam2(); 
  	FaceCheckUninitSecond();
  }

  } 
  window.addEventListener('message', (data)=>{
	  console.log('接收到的数据', data.data.data)
	  typeid=data.data.data
	})

//加载控件
function loadActiveX() {
	//if (navigator.userAgent.indexOf('MSIE') >= 0) {
	if (!!window.ActiveXObject || "ActiveXObject" in window){
		isIE = true;
         //IE浏览器加载控件
         document.getElementById("ActiveXDivOne").innerHTML = "<OBJECT id=\"axCam_Ocx\"  classid=\"clsid:D5BD5B4A-4FC0-4869-880B-27EE9869D706\" width=\"600px\" height=\"360px\" ></OBJECT>";
         //document.getElementById("ActiveXDivTwo").innerHTML = "<OBJECT id=\"axCam_Ocx2\"  classid=\"clsid:341014BA-CD4A-4918-A11F-8A33B152915A\" width=\"500px\" height=\"400px\" ></OBJECT>";
         OcxInit();
     }
     else {
     	isIE = false;
     	if (!window.WebSocket) { 
     		alert("WebSocket not supported by this browser!"); 
     	} 
        //其他浏览器加载控件
        document.getElementById("ActiveXDivOne").innerHTML =" <canvas id=\"cameraCanvas\" width=\"600px\" height=\"360px\" style=\"border:1px solid #d3d3d3;\">";
       // document.getElementById("ActiveXDivTwo").innerHTML =" <canvas id=\"cameraCanvasSecond\" width=\"500px\" height=\"400px\" style=\"border:1px solid #d3d3d3;\">";
        WsInit(600,360,600,360,true);     
    }

      if(document.addEventListener){ 
    	document.addEventListener('DOMMouseScroll',scrollFunc,false); 
	}//W3C 
	window.onmousewheel=document.onmousewheel=scrollFunc
}



 //必需重写---获取设备名称(num为当前摄像头数量,strUsbNamr为摄像头名字数组)
 function GetDevName(num,strUsbNamr){
 	var obj = document.getElementById("DeviceName");
 	obj.options.length = 0;
 	for(var i=0;i<num;i++){
 		var objOption = document.createElement("option");
 		objOption.text = strUsbNamr[i];
 		objOption.value = i;
 		obj.options.add(objOption);
 	}
 	if(num>0){
	if(isSecondDev){
 		obj.options[camidMain].selected = true;
 		var obj2 = document.getElementById("DeviceName2");
 		obj2.options.length = 0;
 		if(num>1){
 			

 				for(var i=0;i<num;i++){
 					var objOption = document.createElement("option");
 					objOption.text = strUsbNamr[i];
 					objOption.value = i;
 					obj2.options.add(objOption);
 				}
 				obj2.options[camidSub].selected = true;
 				OcxGetDeviceResolutionSecond();
 			//}
 		}
 	}
 	}

 }

//必需重写---获取分辨率(data为分辨率数组,每2个为一组,宽高)
function GetDeviceResolution(data) {
	var obj = document.getElementById("Resolution");
	var max = 0;
	var maxIndex = 0;
	obj.options.length = 0;
	if(data.length>0){
		for(var i=0;i<data.length/2;i++){
			var objOption = document.createElement("option");
			var ww = data[i*2];
			var hh = data[i*2+1];
			objOption.text = ""+ww+"*"+hh;
			objOption.value = i;
			obj.options.add(objOption);
			if(max<ww){
				max = ww;
				maxIndex = i;
			}
		}
		obj.options[maxIndex].selected = true;

		
	}
} 

//必需重写---获取分辨率副头(data为分辨率数组,每2个为一组,宽高)
function GetDeviceResolutionSecond(data) {
	var obj = document.getElementById("Resolution2");
	var max = 0;
	var maxIndex = 0;
	obj.options.length = 0;
	for(var i=0;i<data.length/2-1;i++){
		var objOption = document.createElement("option");
		var ww = data[i*2];
		var hh = data[i*2+1];
		objOption.text = ""+ww+"*"+hh;
		objOption.value = i;
		obj.options.add(objOption);
		if(max<ww){
			max = ww;
			maxIndex = i;
		}
	}
	obj.options[maxIndex].selected = true;

} 

//设备1初始化完成,可以进行相关操作
function LoadOver(){
  StartVideo();
}

//设备2初始化完成,可以进行相关操作
function LoadOver2(){
  StartVideo2();
}



//开启摄像头
function StartVideo() 
{
	var obj = document.getElementById("Resolution");
	var restr = obj[obj.selectedIndex].text;
	var pos = restr.lastIndexOf("*");
	var width = parseInt(restr.substring(0, pos));
	var height =parseInt(restr.substring(pos + 1, restr.length));
	SetImagebase64(1);
StartCam(camidMain,width,height);
	//StartCam(camidSub,1600,1200);

}

//关闭摄像头
function CloseVideo() 
{
	CloseCam(); 
}

//抓图拍照
function Capture() {

	var type = 0;
	if(document.getElementById("Radio1").checked){
      //不裁边
      type = 0;
  }else if(document.getElementById("Radio2").checked){
      //自动裁边
      type = 1;
  }else if(document.getElementById("Radio3").checked){
      //手动裁边
      type = 2;
  }
  CaptureImage(type);
}

//切换摄像头
function ChangeDevice() 
{
	CloseCam();
	var devObj = document.getElementById("DeviceName");
	camidMain = devObj.selectedIndex;
	ChangeCamDevice(camidMain);
}

//切换分辨率
function ChangeResolution() {
	CloseCam();
	var obj = document.getElementById("Resolution");
	var restr = obj[obj.selectedIndex].text;
	var pos = restr.lastIndexOf("*");
	var width = parseInt(restr.substring(0, pos));
	var height = parseInt(restr.substring(pos + 1, restr.length));
	StartCam(camidMain,width,height);

}

//设置图片类型
function SetFileType() 
{
	var type = document.getElementById("FileType").selectedIndex;
	SetImageType(type);
}

//设置图片颜色格式
function SetImageColorMode() 
{
	var type = document.getElementById("ColourMode").selectedIndex;
	SetColorMode(type);
}

//图像设置窗口
function ShowImageSettingWindow()
{
	ShowSettingWindow();
}

//设置是否裁剪
function SetCutType() {
	var type = 0;

	if (document.getElementById("Radio1").checked) {
		type = 0;
	}
	if (document.getElementById("Radio2").checked) {
		type = 1;
	}
	if (document.getElementById("Radio3").checked) {
		type = 2;
	}
	SetCamCutType(type);
	SetCamCutType2(type);
}

//设置智能连拍模式
function SetCaptureModel() {
	if (document.getElementById("Radio10").checked) {
		SetWiseCapture(0);
	}
	if (document.getElementById("Radio11").checked) {
		SetWiseCapture(1);
	}
	if (document.getElementById("Radio12").checked) {
		SetTimeCapture(1, 5000);
	}
}

//设置保存的文件路径
function SetFilePath(){
	//var path = "C:\\Users\\<USER>\\Desktop"
	var path = "E:\\img"
	SetImagePath(path);
	SetImagePath2(path);

}

//设置拍照时是否只返回base64
function funSetImagebase64(){
	var isCheck = document.getElementById("checkboxBase").checked;
	if(isCheck){
		SetImagebase64(1);
	}else {
		SetImagebase64(0);
	}

}

//启动身份证模块
function funStartIC(){
	StartIC();
}

//启动身份证自动读卡
function funStartICWork(){
	StartICWork();
}

//关闭身份证自动读卡
function funStopICWork(){
	StopICWork();
}

function funGetOneIC(){
	GetOneIC();
}

function funGetICValues(){
	GetICValues();
}

//条码识别
function funReadBarCode(type){
	if(type==0){
	var imgPath = "D:\\barcode.jpg";
	ReadBarCode(imgPath);
	}else if(type==1){
		ReadBarQrcodeD(1);
	}
}

//二维码识别
function funReadQrCode(type){
	if(type==0){
	var imgPath = "D:\\qrcode.jpg";
	ReadQrCode(imgPath);
	}else if(type==1){
		ReadBarQrcodeD(2);
	}
}

//合并PDF
function funConvertToPDF(){
	var pat1 = "D:\\add1.jpg";
	var pat2 = "D:\\add2.jpg";
	var pat3 = "D:\\add3.jpg";
	var pdfpath = "D:\\Convert.pdf";
	AddPDFImageFile(pat1);
	AddPDFImageFile(pat2);
	AddPDFImageFile(pat3);
	SavePDF(pdfpath);

}

//图片合并
function funCombineTwoImage(){
   var dir = 1;  // 1->垂直合并  2->水平合并
   if (document.getElementById("Radio4").checked) { dir = 1;}
   if (document.getElementById("Radio5").checked) { dir = 2;}

   var combinePath = "C:\\MyIMG\\CombineImg.jpg";
   var ImgPath1 = "C:\\MyIMG\\add1.jpg";
   var ImgPath2 = "C:\\MyIMG\\add2.jpg";
   CombineTwoImage(combinePath, ImgPath1, ImgPath2, dir);

}

//上传图片
function funUpdataFile(){
	UpdataFile("127.0.0.1", 8080, "/FileStreamDemo/servlet/FileSteamUpload?","D:\\add1.jpg");
}

//上传Base64(js自带功能)
function funUpdataBase64(){
	var strImgBase64 = document.getElementById("img1").src.split(',')[1];
	var $Blob= getBlobBydataURI(strImgBase64,'image/jpeg');       
  var formData = new FormData();       
  var name;
  var type = document.getElementById("FileType").value;
  switch (type){
	case "0":
		type='.jpg';
		break;
	case "1":
		type='.png';
		break;
	case "2":
		type='.bmp';
		break;
	case "3":
		type='.git';
		break;
	case "4":
		type='.tif';
		break;
	default:
		break;
  }
  
  if (document.getElementById("Radio19").checked) {
	  name=document.getElementById("Radio21").value
  }
  else{
	  name="file_"+Date.parse(new Date())
  }
  formData.append("files", $Blob ,name+type);     
  console.log('13',formData)  
      //组建XMLHttpRequest 上传文件       
      var request = new XMLHttpRequest();        
       //上传连接地址
       request.open("POST", " http://cloud.bimtk.com:928/File/FileUpload/UploadFiles");           
       request.onreadystatechange=function()      
       {          if (request.readyState==4)        
       	{            if(request.status==200){            
			   console.log("上传成功" );
			var file=   JSON.parse(request.responseText).Data;
			var params = {
				attachmentList: [{
					File_Name:name+type,
					File_Size:"0",
					File_Type:type,
					File_Url:file.split('*')[0],
				}],
				file:{
					Doc_Catelog: "PLMEngineeringFiles",
					Doc_Content: name+type,
					Doc_File: name+type,
					Doc_Title:name,
					Doc_Type:typeid,
					Id: "",
					IsChanged: false,
					Is_Load: false,
					Project_Id: "",
					ishistory: true
				}
        }
			Baas().sendRequest({
        "action": "SYS/Sys_File/Add",
        "async": false,
        "params": params,
        "success": function (data) {
          // console.log(data);
          if (data.IsSucceed) {
			
		  }
		  else{
			  console.log(data.Message)
		  }
        }
      });
	 //   File_Url: file.split('*')[0],
    //    File_Size: file.split('*')[1],
     //   File_Type: file.split('*')[2],
     //   File_Name: file.split('*')[3]
       	}else{             
       		console.log("上传失败,检查上传地址是否正确");  
       		            
       	}          
       }       
   }       
   request.send(formData);     
}

//刷新设备
function RefreshDev(){
	CloseCam(); 
	CloseCam2();
	RefreshDevice();
}

//根据文件路径获取base64
function fungetBase64(){
	
	var strpath = "D:\\1.jpg";
	GetBase64FromFile(strpath);
}

//设置水印
function SetWaterType(){
	if (document.getElementById("Radio6").checked) {
		CloseWaterMark();
	}
	if (document.getElementById("Radio7").checked) {
		var waterinfo = "我的水印";
		var FontSize = 50;
		var FontSytle = 0;
		var xOff = 100;
		var yOff = 100;
		OpenWaterMark(waterinfo, FontSize, FontSytle, 255, 0, 0, xOff, xOff);
	}
}

//设置自动裁边单图与多图
function funsetJiubianModel(){
	if (document.getElementById("Radio13").checked) {
		SetJiubianModel(0);
       // axCam_Ocx.CusCrop(0);
   }else if (document.getElementById("Radio14").checked) {
   	SetJiubianModel(1);
   }
}

//设置四周补白
function funsetBuBai(){
	var isCheck = document.getElementById("checkbox2").checked;
	if(isCheck){
		SetBuBai(1);
	}else  SetBuBai(0);
}

//设置去灰
function funsetQudise(){
	var isCheck = document.getElementById("checkbox1").checked;
	if(isCheck){
		SetQudise(1);    
	}else SetQudise(0);
}

function funSetFileNameModel(){
	if (document.getElementById("Radio19").checked) {
  //自定义
  var num = 1;
  var name = "IMGAAA";
  SetFileNameCustom(name,num);

  var num2 = 10;
  var name2 = "IMGAAAsub";
  SetFileNameCustom2(name2,num2);
}else if (document.getElementById("Radio20").checked) {
  //时间
  SetFileNameTime();
  SetFileNameTime2();
}
}

function funSetAutoExposure(){
	if (document.getElementById("RadioExopen").checked) {
		SetAutoExposure(1);
   }else if (document.getElementById("RadioExclose").checked) {
   		SetAutoExposure(0);
   }
}

function funGetCamParameter(){
	GetCamParameter();
}

function funSetExposure(){
	SetExposure(0);
}

function funSetBrightness(){
	SetBrightness(0);
}

function funGetCamNum(){
	GetCamNum();
}

function funDeleteFile(){
	var filename = "D:\\1.jpg";
	DeleteFile(filename);
}

function funSetJpgQuanlity(){

	SetJpgQuanlity(100);
}

function funGetICPictureAll(){

	GetICPictureAll();
}

function funSetCusCropPlace(){

	var cutX = 800;
	var cutY=600;
	var cutW = 800;
	var cutH = 600;


	SetCusCropPlace(cutX,cutY,cutW,cutH);


}

function funSetCusCropPlace2(){

	var cutX = 100;
	var cutY=100;
	var cutW = 400;
	var cutH = 300;

	SetCusCropPlace2(cutX,cutY,cutW,cutH);


}

function funBeginVideo(){
	var filename = "D:\\videoOne.avi";
	BeginVideo(filename,0,1);
}

function funStopVideo(){
	StopVideo();
}

function funBeginVideo2(){
	var filename = "D:\\videoTwo.avi";
	BeginVideoSecond(filename,0,1);
}

function funStopVideo2(){
	StopVideoSecond();
}

function funGetAudioName(){
	GetAudioName();
}

function funMakeDir(){
	var filename = "D:\\ceshi";
	MakeDir(filename);
}

function funAutoFoucs(){
	AutoFoucs();
}


var hide_show = 0;
function ShowHide() {

	if (hide_show == 0) {
		hide_show = 1;
		if(isIE){
			document.getElementById("axCam_Ocx").style.visibility = "hidden";
		}else  document.getElementById("cameraCanvas").style.visibility = "hidden";
	}
	else {
		hide_show = 0;
		if(isIE){
			document.getElementById("axCam_Ocx").style.visibility = "visible";
		}else  document.getElementById("cameraCanvas").style.visibility = "visible";
	}

}

function ShowInfo(op) {
	var obj = document.getElementById("TextArea1");
	obj.value = op+obj.value;
}
/*****************************副摄像头操作部分*******************************************

*****************************************************************************************/


//打开副摄像头
function StartVideo2() 
{
	if(camidSub >= 0){
	var obj = document.getElementById("Resolution2");
	var restr = obj[obj.selectedIndex].text;
	var pos = restr.lastIndexOf("*");
	var width = parseInt(restr.substring(0, pos));
	var height =parseInt(restr.substring(pos + 1, restr.length));
	StartCam2(camidSub,width,height);
}

}

//关闭副摄像头
function CloseVideo2() {
	CloseCam2();
}

//切换摄像头
function ChangeDevice2() 
{
	CloseCam2();
	var devObj = document.getElementById("DeviceName2");
	camidSub = devObj.selectedIndex;
	ChangeCamDevice2(camidSub);
}

//切换分辨率
function ChangeResolution2() {
	CloseCam2();
	var obj = document.getElementById("Resolution2");
	var restr = obj[obj.selectedIndex].text;
	var pos = restr.lastIndexOf("*");
	var width = parseInt(restr.substring(0, pos));
	var height = parseInt(restr.substring(pos + 1, restr.length));
	StartCam2(camidSub,width,height);

}


//副头拍照
function Capture2() {
	var type = 0;
	if(document.getElementById("Radio1").checked){
      //不裁边
      type = 0;
  }else if(document.getElementById("Radio2").checked){
      //自动裁边
      type = 1;
  }else if(document.getElementById("Radio3").checked){
      //手动裁边
      type = 2;
  }
	CaptureImage2(type);

}



function InfoCallback(op) {

	var text = "";
	if(op == 0){
		text = "连接成功\r\n";
	}else if(op == 0x01){
		text = "断开成功\r\n";
	}else if(op == 0x02){
		text = "设备已经连接\r\n";
	}else if(op == 0x03){
		text = "设备已经关闭\r\n";
	}else if(op == 0x04){
		text = "拍照成功\r\n";
	}else if(op == 0x05){
		text = "pdf添加文件成功\r\n";
	}else if(op == 0x06){
		text = "pdf保存成功\r\n";
	}else if(op == 0x07){
		text = "图片合并成功\r\n";
	}else if(op == 0x08){
		text = "智能连拍启动\r\n";
	}else if(op == 0x09){
		text = "定时连拍启动\r\n";
	}else if(op == 0x10){
		text = "定时连拍成功\r\n";
	}else if(op == 0x11){
		text = "定时连拍关闭\r\n";
	}else if(op == 0x12){
		text = "文件上传服务器成功\r\n";
	}else if(op == 0x13){
		text = "水印开启\r\n";
	}else if(op == 0x14){
		text = "水印关闭\r\n";
	}else if(op == 0x15){
		text = "此设备属于本公司\r\n";
	}else if(op == 0x16){
		text = "此设备不属于本公司\r\n";
	}else if(op == 0x17){
		text = "自动曝光启动\r\n";
	}else if(op == 0x18){
		text = "自动曝光关闭\r\n";
	}else if(op == 0x19){
		text = "身份证功能启动成功\r\n";
	}else if(op == 0x1a){
		text = "身份证功能启动失败\r\n";
	}else if(op == 0x1b){
		text = "身份证读卡成功\r\n";
	}else if(op == 0x1c){
		text = "身份证读卡失败\r\n";
	}else if(op == 0x1d){
		text = "重新操作\r\n";
	}else if(op == 0x1e){
		text = "未发现模块\r\n";
	}else if(op == 0x1f){
		text = "未启动身份证功能\r\n";
	}else if(op == 0x20){
		text = "启动身份证自动读卡\r\n";
	}else if(op == 0x21){
		text = "关闭身份证自动读卡\r\n";
	}else if(op == 0x22){
		text = "启动拍照只生成base64\r\n";
	}else if(op == 0x23){
		text = "关闭拍照只生成base64\r\n";
	}else if(op == 0x25){
		text = "初始化指纹模块成功\r\n";
	}else if(op == 0x26){
		text = "初始化指纹模块失败\r\n";
	}else if(op == 0x27){
		text = "未初始化指纹模块\r\n";
	}else if(op == 0x28){
		text = "身份证没有指纹数据\r\n";
	}else if(op == 0x29){
		text = "指纹认证成功\r\n";
	}else if(op == 0x30){
		text = "开始指纹认证\r\n";
	}else if(op == 0x31){
		text = "正在指纹认证\r\n";
	}else if(op == 0x32){
		text = "停止指纹认证\r\n";
	}else if(op == 0x33){
		text = "指纹认证失败\r\n";
	}else if(op == 0x34){
		text = "开始录像\r\n";
	}else if(op == 0x35){
		text = "停止录像\r\n";
	}else if(op == 0x36){
		text = "正在录像中\r\n";
	}else if(op == 0x37){
		text = "开始录像副头\r\n";
	}else if(op == 0x38){
		text = "停止录像副头\r\n";
	}else if(op == 0x39){
		text = "正在录像中副头\r\n";
	}else if(op == 0x44){
		text = "建立文件成功\r\n";
	}else if(op == 0x45){
		text = "建立文件失败\r\n";
	}else if(op == 0x46){
		text = "人脸识别初始化成功\r\n";
	}else if(op == 0x47){
		text = "启动人脸对比\r\n";
	}else if(op == 0x48){
		text = "人脸识别初始化成功\r\n";
	}else if(op == 0x49){
		text = "主头正在连接中\r\n";
	}else if(op == 0x4a){
		text = "主头等待连接\r\n";
	}else if(op == 0x4b){
		text = "副头正在连接中\r\n";
	}else if(op == 0x4c){
		text = "副头等待连接\r\n";
	}


	else if(op == 0xa0){
		text = "没有对应分辨率\r\n";     
	}else if(op == 0xa1){
		text = "pdf没有添加任何文件\r\n";     
	}else if(op == 0xa2){
		text = "文件不存在\r\n";     
	}else if(op == 0xa3){
		text = "意外断开\r\n";     
	}else if(op == 0xa4){
		text = "连接不上\r\n";     
	}else if(op == 0xa5){
		text = "pdf添加文件不是jpg格式\r\n";     
	}else if(op == 0xa6){
		text = "没有发现摄像头\r\n";     
	}else if(op == 0xa7){
		text = "camid无效\r\n";     
	}else if(op == 0xa8){
		text = "图片尺寸太小\r\n";     
	}else if(op == 0xa9){
		text = "文件上传服务器失败\r\n";     
	}else if(op == 0xaa){
		text = "该设备没有副头\r\n";     
	}else if(op == 0xab){
		text = "条码识别失败\r\n";     
	}else if(op == 0xac){
		text = "二维码识别失败\r\n";     
	}else if(op == 0xad){
		text = "图片合并失败\r\n";     
	}else if(op == 0xae){
		text = "设置路径失败,路径不存在\r\n";    
	}else if(op == 0xaf){
		text = "摄像头切换太频繁\r\n";     
	}else if(op == 0xb1){
		text = "未发现指纹模块\r\n";     
	}else if(op == 0xb2){
		text = "录像分辨率不能高于1600*1200\r\n";     
	}else if(op == 0xb3){
		text = "副头录像分辨率不能高于1600*1200\r\n";     
	}else if(op == 0xb4){
		text = "没发现麦克风\r\n";     
	}else if(op == 0xb8){
		text = "人脸识别初始化失败\r\n";     
	}else if(op == 0xb9){
		text = "请读取身份证信息\r\n";     
	}else if(op == 0xba){
		text = "请先初始化人脸识别\r\n";     
	}else if(op == 0xbb){
		text = "没有发现合适的人脸\r\n";     
	}

	var obj = document.getElementById("TextArea1");
	obj.value = text+ obj.value;
}

function InfoTextCallback(type,str){

	var text = "";
	if(type == 0){
		text = "图片路径:"+str+"\r\n";
	}else if(type == 1){
		text = "默认路径:    "+str+"\r\n";

	}else if(type == 2){
		text = "条码:    "+str+"\r\n";

	}else if(type == 3){
		text = "文件上传服务器成功:"+str+"\r\n";

	}else if(type == 4){
		text = "文件上传服务器失败:"+str+"\r\n";

	}else if(type == 5){
		text = "base64编码成功,请自行处理str\r\n";
// text ="data:;base64," +str+"\r\n";
 var imgobj1 = document.getElementById("img1");
 	imgobj1.src = "data:;base64," + str;
}else if(type == 6){
	text = "删除文件成功:"+str+"\r\n";
}else if(type == 7){
	text = "二维码:"+str+"\r\n";
}else if(type == 8){
	text = "拍照失败:"+str+"\r\n";
}

else if(type == 9){
	text = "身份证名字:"+str+"\r\n";
}else if(type == 10){
	text = "身份证号码:"+str+"\r\n";
}else if(type == 11){
	text = "身份证性别:"+str+"\r\n";
}else if(type == 12){
	text = "身份证民族:"+str+"\r\n";
}else if(type == 13){
	text = "身份证生日:"+str+"\r\n";
}else if(type == 14){
	text = "身份证地址:"+str+"\r\n";
}else if(type == 15){
	text = "身份证签发机关:"+str+"\r\n";
}else if(type == 16){
	text = "身份证有效开始日期:"+str+"\r\n";
}else if(type == 17){
	text = "身份证有效截至日期:"+str+"\r\n";
}else if(type == 18){
	text = "安全模块号:"+str+"\r\n";
}else if(type == 19){
 	//身份证头像
 	text = "身份证头像base64\r\n";
 	var imgobj1 = document.getElementById("img1");
 	imgobj1.src = "data:;base64," + str;
 }else if(type == 21){
 	text = "base64编码成功,请自行处理str(副头)\r\n";
 //text ="data:;base64," +str+"\r\n";
 var imgobj1 = document.getElementById("img2");
 	imgobj1.src = "data:;base64," + str;
}else if(type == 22){
	text = "曝光范围:"+str+"\r\n";
}else if(type == 23){
	text = "亮度范围:"+str+"\r\n";
}else if(type == 24){
	text = "上传服务器返回:"+str+"\r\n";
}else if(type == 25){
	var imgobj1 = document.getElementById("img1");
 	imgobj1.src = "data:;base64," + str;
	text = "身份证复印件"+"\r\n";
}else if(type == 26){
	text = "当前设备数量:"+str+"\r\n";
}else if(type == 27){
	text = "麦克风:"+str+"\r\n";
}else if(type == 28){
		text = "人脸抓拍base64编码成功,请自行处理str\r\n";
// text ="data:;base64," +str+"\r\n";
 var imgobj1 = document.getElementById("img1");
 	imgobj1.src = "data:;base64," + str;
}

var obj = document.getElementById("TextArea1");
obj.value = text+ obj.value;
}


</script>

</head>

<body onload ="loadActiveX();">


	<div style="width:600px; height: 850px;  background:white;  float:left">
		<div  width="100%" height="360px" id='ActiveXDivOne' ></div>
		<div style="width:600px; height: 450px; border: 1px solid #CECECE; background:white;  float:left;">
			<table style="padding-top:20px ;display: flex;flex-direction: row;justify-content: space-around;">
				<tr>
					<td>
						<input class="btn" type = "button" value = " 放大 " onclick = "ZoomOut(1);" />
						<input class="btn" id="Button2" type="button" value="打开" onclick = "StartVideo();"/>
						<input class="btn" type = "button" value = " 左旋 " onclick = "RoateL(1);" />	
						<input class="btn" type = "button" value = " 适合大小 " onclick = "BestSize(1);" />
						<input class="btn" id="Button2" type="button" value="参数设置" onclick = "ShowImageSettingWindow();"/>
						<input class="btn" id="Button2" type="button" value="拍照" onclick = "Capture();"/>
					</td>
				</tr>				
				<tr>
					<td>
						<input class="btn" type = "button" value = " 缩小 " onclick = "ZoomIn(1);" />
						<input class="btn" id="Button2" type="button" value="关闭" onclick = "CloseVideo();"/>
						<input class="btn" type = "button" value = " 右旋 " onclick = "RoateR(1);" />
						<input class="btn" type = "button" value = " 实际大小 " onclick = "TrueSize(1);" />	
						<input class="btn" id="Button2" type="button" value="自动对焦" onclick = "funAutoFoucs();"/>
						<input class="btn" id="Button7" type="button" value="上传" onclick = "funUpdataBase64();"/>
					</td>
				</tr>
			</table>
					<div style="width:600px;height:133px;">
						<div style="width:240px;height:133px;background:white; border: 1px solid #D0D3DB; border-radius: 6px;float:left;margin-left: 16px;margin-top: 13px; ">
							<table>
								<tr>
									<td style="font-size: medium" class="word">图像格式</td>
								</tr>
							<tr>
									<td style="font-size: medium" class="row" >保存格式</td>
									<td  class="row">
										<select id = "FileType" style="width: 120px;" onchange = "SetFileType()">
											<option value="0">jpg</option>
											<option value="1">png</option>
											<option value="2">bmp</option>
											<option value="3">gif</option>
											<option value="4">tif</option>
	
										</select>
									</td>
								</tr>
								<tr>
									<td style="font-size: medium" class="row">颜色格式</td>
									<td class="row" >
											<select id = "ColourMode" style="width: 120px;" onchange = "SetImageColorMode()">
												<option value="0">彩色</option>
												<option value="1">灰度</option>
												<option value="2">黑白</option>
											</select>
									</td>
								</tr>
							</table>
						</div>	
						<div style="width:310px;height:133px;background:white; border: 1px solid #D0D3DB;border-radius: 6px; float:left;margin-left: 16px;margin-top: 13px;">
						<table>
							<tr>
								<td style="font-size: medium" class="word">设备信息</td>
							</tr>
								<tr >
									<td style="font-size: medium" class="row">设备</td>
									<td class="row">
										<select  id="DeviceName" name="D1"onchange = "ChangeDevice()"></select>
									</td>
								</tr>
								<tr>
									<td  style="font-size: medium" class="row">分辨率</td>
									<td class="row">
										<select  id="Resolution" name="D2"onchange = "ChangeResolution()"></select>
									</td>
								</tr>
							</table>
						</div>
					</div>
					<div style="width:600px;height:133px;">
						<div style="width:240px;height:133px;background:white; border: 1px solid #D0D3DB;border-radius: 6px; float:left;margin-left: 16px; margin-top: 13px;">
			                <table>
								<tr>
									<td style="font-size: medium" class="word">图像格式</td>
									<tr>
										<td class="row">
											<input id="Radio1"   checked="checked" name="R1" type="radio" value="V1" onclick="SetCutType()"/>不裁切
										</td>
									</tr>
									<tr>
									
										<td class="row">
											
											<input id="Radio2"  name="R1" type="radio" value="V2" onclick="SetCutType()"/>自动裁切
											<input id="Radio3"  name="R1" type="radio" value="V3" onclick="SetCutType()"/>手动裁切
										</td>
									</tr>
									<tr>
										<td class="row"> 
											<input id="Radio13"  checked="checked" name="R11" type="radio" value="V1" onclick="funsetJiubianModel()"/>单图	
											<input id="Radio14"  name="R11" type="radio" value="V2" onclick="funsetJiubianModel()"/>多图
											<input id="checkbox2"  type="checkbox" value="黑边补白" onclick = "funsetBuBai();">补白<br>
										</td>
									</tr>
								</tr>
							</table>
								
							
						</div>
						<div style="width:310px;height:133px;background:white; border: 1px solid #D0D3DB; border-radius: 6px;float:left; margin-left: 16px;margin-top: 13px;">
							<table>
								<tr>
									<td style="font-size: medium;" class="word">
										文件命名:</td>
								</tr>
								<tr>
									<td class="row">
										<input id="Radio19"  name="R7" type="radio" value="V1" onclick="funSetFileNameModel()"/>自定义
										<input id="Radio21"  name="R7" type="text" />
									</td>
								</tr>
								<tr>
									<td class="row">
										<input id="Radio20"  checked="checked" name="R7" type="radio" value="V2" onclick="funSetFileNameModel()"/>时间	
									</td>
																	
								</tr>
							</table>
									
							
						</div>
					</div>
							
					
				</div>
	
	</div>

	<div style="width:280px; height: 810px;margin-left: 13px; background:white; border: 1px solid black;float:left">
		<div style="width: 100%;height: 30%;">
			<img id="img1"  style="width: 100%;height: 100%" />
		</div> 
		<div style="width: 100%;height: 70%;">
			<textarea id="TextArea1" cols="20" rows="2" style="width: 100%;height:100%;">
			</textarea> 
		</div>
	</div>        

	
			


				<!-- 		 <table style="width: 100%;">
									<tr>
										<td style="font-size: medium" class="style13">
										连拍模式:</td>

										<td>
											<input id="Radio10" checked="checked" name="R10" type="radio" value="V1" onclick="SetCaptureModel()"/>无
										</td>
										<td>
											<input id="Radio11"  name="R10" type="radio" value="V2" onclick="SetCaptureModel()"/>智能连拍
										</td>
										<td>
											<input id="Radio12"  name="R10" type="radio" value="V3" onclick="SetCaptureModel()"/>定时连拍
										</td> 


										<td style="font-size: medium" >
										</td> 


									<td >
											<input type = "button" value = " 合并PDF " onclick = "funConvertToPDF();" 
											style="width: 69px" />
										</td>
									

										<td>
											<input id="Button7" type="button" value="上传(本地图片)" onclick = "funUpdataFile();"/>
										</td>

										<td>
											<input id="Button7" type="button" value="上传(base64)" onclick = "funUpdataBase64();"/>
										</td>

										<td>
											<input id="ButtonPath" type="button" value="文件路径" onclick = "SetFilePath();"/>
										</td>

										<td>
											<input type = "button" value = " 图片合并 " onclick = "funCombineTwoImage();" />
										</td>
										<td>
											方向:  
											<input id="Radio4" checked="checked" name="R2" type="radio" value="V1" />垂直   
											<input id="Radio5"  name="R2" type="radio" value="V2" />水平        
										</td> 
										<td>

										</td>

										<td >
											<input id="checkbox1" type="checkbox" value="去底灰" onclick = "funsetQudise();">去底灰
												<input id="Button7" type="button" value="上传" onclick = "funUpdataBase64();"/>
											</td>
											 <td>
												<input id="checkboxBase" type="checkbox" value="拍照base64" onclick = "funSetImagebase64();">拍照base64</td> 
											</tr>
										</table>   -->

								

											<!-- <table style="width: 100%;">
												<tr>
													<td >

														<input  type="button" value="获取当期摄像头数量" onclick = "funGetCamNum();"/>

														<input  type="button" value="建立文件夹" onclick = "funMakeDir();"/>

														<input  type="button" value="删除文件" onclick = "funDeleteFile();"/>
											
														<input  type="button" value="设置JPG图像质量" onclick = "funSetJpgQuanlity();"/>

														<input  type="button" value="设置手动裁边坐标" onclick = "funSetCusCropPlace();"/>

														<input  type="button" value="设置手动裁边坐标2" onclick = "funSetCusCropPlace2();"/>

														<input  type="button" value="条码识别(路径)" onclick = "funReadBarCode(0);"/>

														<input  type="button" value="条码识别(动态)" onclick = "funReadBarCode(1);"/>

														<input  type="button" value="二维码识别(路径)" onclick = "funReadQrCode(0);"/>

														<input  type="button" value="二维码识别(动态)" onclick = "funReadQrCode(1);"/>

													</td>



												</tr>



											</table>	

											<table style="width: 100%;">
												<tr>
													<td style="font-size: medium" class="style33">
													身份证功能:</td>

													<td >
														<input id="ButtonStartID" type="button" value="启动身份证模块" onclick = "funStartIC();"/>



														<input id="ButtonStartID" type="button" value="自动读卡" onclick = "funStartICWork();"/>

														<input id="ButtonStartID" type="button" value=" 关闭自动读卡" onclick = "funStopICWork();"/>

														<input id="ButtonStartID" type="button" value="手动读卡" onclick = "funGetOneIC();"/>

														<input id="ButtonStartID" type="button" value="获取身份证信息" onclick = "funGetICValues();"/>

														<input id="ButtonStartID" type="button" value="获取身份证复印件" onclick = "funGetICPictureAll();"/>

													</td>


									

												</tr>

											</table>

												<table style="width: 100%;">
												<tr>
													<td style="font-size: medium" class="style33">
													指纹功能:</td>

													<td >
														<input id="ButtonStartID" type="button" value="初始化指纹功能" onclick = "InitFinger();"/>

														<input id="ButtonStartID" type="button" value="启动指纹功能" onclick = "StartFinger();"/>

														<input id="ButtonStartID" type="button" value="停止指纹功能" onclick = "CloseFinger();"/>
													</td>

													<td style="font-size: medium" class="style33">
													录像功能:</td>

													<td >
														<input id="ButtonStartID" type="button" value="启动录像主头" onclick = "funBeginVideo();"/>

														<input id="ButtonStartID" type="button" value="停止录像主头" onclick = "funStopVideo();"/>

														<input id="ButtonStartID" type="button" value="启动录像副头" onclick = "funBeginVideo2();"/>

														<input id="ButtonStartID" type="button" value="停止录像副头" onclick = "funStopVideo2();"/>

														<input id="ButtonStartID" type="button" value="获取声卡名字" onclick = "funGetAudioName();"/>
													</td>



											</table> -->
<!-- 
											<table style="width: 100%;">
												<tr>
													<td style="font-size: medium" class="style33">
													人脸识别功能:</td>

													<td >
														<input id="ButtonStartID" type="button" value="初始化人脸识别" onclick = "FaceCheckInit();"/>

														<input id="ButtonStartID" type="button" value="启动人脸对比" onclick = "StartFaceCheck();"/>

														<input id="ButtonStartID" type="button" value="停止人脸对比" onclick = "StopFaceCheck();"/>

														<input id="ButtonStartID" type="button" value="关闭人脸识别" onclick = "FaceCheckUninit();"/>
													</td>

													<td >
														<input id="ButtonStartID" type="button" value="初始化人脸识别" onclick = "FaceCheckInitSecond();"/>

														<input id="ButtonStartID" type="button" value="启动人脸对比" onclick = "StartFaceCheckSecond();"/>

														<input id="ButtonStartID" type="button" value="停止人脸对比" onclick = "StopFaceCheckSecond();"/>

														<input id="ButtonStartID" type="button" value="关闭人脸识别" onclick = "FaceCheckUninitSecond();"/>
													</td>



											</table> -->







									



										<div id="image_container" style="clear:both;">

											<div class="flex">

											</div>
										</div>



									</body>

									</html>