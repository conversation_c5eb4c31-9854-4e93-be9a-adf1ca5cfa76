(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-d6f396d6"],{"0775":function(e,t,r){"use strict";r.r(t);var a=r("ad3a"),n=r("22e9");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);r("2df4");var o=r("2877"),u=Object(o["a"])(n["default"],a["a"],a["b"],!1,null,"e7cac7d6",null);t["default"]=u.exports},"22e9":function(e,t,r){"use strict";r.r(t);var a=r("de2d"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a},"262a":function(e,t,r){"use strict";r("8651")},"2df4":function(e,t,r){"use strict";r("a0bb")},6868:function(e,t,r){"use strict";r.r(t);var a=r("de7c"),n=r("751a");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);r("262a");var o=r("2877"),u=Object(o["a"])(n["default"],a["a"],a["b"],!1,null,"557c9c02",null);t["default"]=u.exports},"751a":function(e,t,r){"use strict";r.r(t);var a=r("83b3"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a},"83b3":function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(r("0775"));t.default={name:"Seal",components:{TechSolution:n.default},props:{isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}}}},8651:function(e,t,r){},a0bb:function(e,t,r){},ad3a:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"108px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"项目名称",required:"",prop:"Project_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择项目",filterable:"",disabled:Boolean(e.$store.state.user.CurReferenceId)},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projs,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Project_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"方案类别",required:"",prop:"Scheme_Type"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选方案类别"},model:{value:e.form.Scheme_Type,callback:function(t){e.$set(e.form,"Scheme_Type",t)},expression:"form.Scheme_Type"}},e._l(e.Types,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):r("span",[e._v(e._s(e.form.Scheme_Type))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"方案名称",required:"",prop:"Scheme_Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Scheme_Name,callback:function(t){e.$set(e.form,"Scheme_Name",t)},expression:"form.Scheme_Name"}}):r("span",[e._v(e._s(e.form.Scheme_Name))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},n=[]},de2d:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("5530"));t.default={name:"TechSolution",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{Scheme_Type:"",Project_Id:"",Scheme_Name:"",Content_Overview:"",Attachments:[],Remark:""},rules:{Project_Id:[{required:!0,message:"请选择项目",trigger:"blur"}],Scheme_Type:[{required:!0,message:"请选择方案类别",trigger:"blur"}],Scheme_Name:[{required:!0,message:"请输入方案名称",trigger:"blur"}],Content_Overview:[{required:!0,message:"请填写方案概述",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]},Types:[{label:"A类",value:"A类"},{label:"B类",value:"B类"}]}},watch:{entity:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.entity),this.form.Remark=this.form.Content_Overview},datas:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.datas),this.form.Remark=this.form.Content_Overview}},created:function(){this.form=(0,n.default)((0,n.default)((0,n.default)({},this.form),this.entity),this.datas),this.form.Remark=this.form.Content_Overview,this.isEdit&&!this.form.Project_Id&&(this.form.Project_Id=this.$store.state.user.CurReferenceId)},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,n.default)({},this.form);return t.Project_Name=this.projs.find((function(t){return t.Id===e.form.Project_Id}))?this.projs.find((function(t){return t.Id===e.form.Project_Id})).Name:"",t.Content_Overview=t.Remark,t},attachOpen:function(e){window.open(e,"_blank")}}}},de7c:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("h3",[e._v("技术方案审批")]),r("tech-solution",{attrs:{"is-edit":e.isEdit,datas:e.datas}})],1)},n=[]}}]);