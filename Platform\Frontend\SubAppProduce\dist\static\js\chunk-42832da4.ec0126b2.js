(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-42832da4"],{"0e08":function(e,t,r){"use strict";r.r(t);var a=r("44e5a"),o=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},1497:function(e,t,r){"use strict";r.r(t);var a=r("1c10"),o=r("0e08");for(var i in o)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return o[e]}))}(i);r("1fdc");var n=r("2877"),s=Object(n["a"])(o["default"],a["a"],a["b"],!1,null,"414a9f98",null);t["default"]=s.exports},"1c10":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[r("div",{staticClass:"cs-z-page-main-content"},[r("div",{staticClass:"top"},[e._v("劳务产值基础信息")]),r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[r("el-row",{staticClass:"row-bg"},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属基地:",prop:"Factory_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",disabled:e.isDisabled||e.factoryDisabled},on:{change:e.changeValue},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e,t){return r("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"归属项目:",prop:"Sys_Project_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled||!e.form.Factory_Id},on:{change:e.selectOrderId},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.ProjectList,(function(t){return r("el-option",{key:t.Sys_Project_Id,attrs:{label:e.getProLabel(t),value:t.Sys_Project_Id}})})),1)],1),r("el-form-item",{attrs:{label:"合同编号:",prop:"",required:""}},[r("el-input",{attrs:{disabled:""},model:{value:e.form.Contract_No,callback:function(t){e.$set(e.form,"Contract_No",t)},expression:"form.Contract_No"}})],1),r("el-form-item",{attrs:{label:"本次报量:",prop:"Current_Dose"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Current_Dose,callback:function(t){e.$set(e.form,"Current_Dose",e._n(t))},expression:"form.Current_Dose"}},[r("template",{slot:"append"},[e._v("T")])],2)],1),r("el-form-item",{attrs:{label:"单价:",prop:"Unit_Price"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Unit_Price,callback:function(t){e.$set(e.form,"Unit_Price",t)},expression:"form.Unit_Price"}},[r("template",{slot:"append"},[e._v("元")])],2)],1),r("el-row",{staticStyle:{width:"100%"}},[r("el-col",{attrs:{span:0==e.form.Invoice_Type?24:12}},[r("el-form-item",{attrs:{label:"票据类型:",prop:"Invoice_Type"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled},on:{change:e.changeType},model:{value:e.form.Invoice_Type,callback:function(t){e.$set(e.form,"Invoice_Type",t)},expression:"form.Invoice_Type"}},[r("el-option",{attrs:{label:"无发票",value:0}}),r("el-option",{attrs:{label:"增值税普通发票",value:1}}),r("el-option",{attrs:{label:"增值税专用发票",value:2}})],1)],1)],1),r("el-col",{attrs:{span:12}},[0!=e.form.Invoice_Type?r("el-form-item",{attrs:{label:"税率:",prop:"Output_Tax_Rate"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Output_Tax_Rate,callback:function(t){e.$set(e.form,"Output_Tax_Rate",t)},expression:"form.Output_Tax_Rate"}},[r("template",{slot:"append"},[e._v("%")])],2)],1):e._e()],1)],1),r("el-form-item",{attrs:{label:"核算开始日期:",prop:"Accounting_Date"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",disabled:e.isDisabled,"value-format":"yyyy-MM-dd","picker-options":e.pickerOptions},model:{value:e.form.Accounting_Date,callback:function(t){e.$set(e.form,"Accounting_Date",t)},expression:"form.Accounting_Date"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"单据编号",prop:"Receipt_No"}},[r("el-input",{attrs:{disabled:"",placeholder:""},model:{value:e.form.Receipt_No,callback:function(t){e.$set(e.form,"Receipt_No",t)},expression:"form.Receipt_No"}})],1),r("el-form-item",{attrs:{label:"登记部门:",prop:"Depart_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",disabled:e.isDisabled||!e.form.Factory_Id||e.disabledDepart},model:{value:e.form.Depart_Id,callback:function(t){e.$set(e.form,"Depart_Id",t)},expression:"form.Depart_Id"}},e._l(e.departmentlist,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"供料方式:",prop:"",required:""}},[r("el-input",{attrs:{disabled:""},model:{value:e.form.Feeding_Mode_Name,callback:function(t){e.$set(e.form,"Feeding_Mode_Name",t)},expression:"form.Feeding_Mode_Name"}})],1),r("el-form-item",{attrs:{label:"发包单位:",prop:"",required:""}},[r("el-input",{attrs:{disabled:""},model:{value:e.form.Contract_Unit,callback:function(t){e.$set(e.form,"Contract_Unit",t)},expression:"form.Contract_Unit"}})],1),r("el-form-item",{attrs:{label:"累计报量:",prop:"",required:""}},[r("el-input",{attrs:{step:"any",type:"number",disabled:""},model:{value:e.form.Accumulative_Dose,callback:function(t){e.$set(e.form,"Accumulative_Dose",t)},expression:"form.Accumulative_Dose"}},[r("template",{slot:"append"},[e._v("T")])],2)],1),r("el-form-item",{attrs:{label:"核算金额:",prop:"",required:""}},[r("el-input",{attrs:{step:"any",type:"number",disabled:""},model:{value:e.getAccountingAmount,callback:function(t){e.getAccountingAmount=t},expression:"getAccountingAmount"}},[r("template",{slot:"append"},[e._v("元")])],2)],1),r("el-form-item",{attrs:{label:"累计金额:",prop:"Accumulative_Price"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:""},model:{value:e.form.Accumulative_Price,callback:function(t){e.$set(e.form,"Accumulative_Price",t)},expression:"form.Accumulative_Price"}},[r("template",{slot:"append"},[e._v("元")])],2)],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"备注:",prop:"Remarks"}},[r("el-input",{attrs:{type:"textarea",rows:4,disabled:e.isDisabled},model:{value:e.form.Remarks,callback:function(t){e.$set(e.form,"Remarks",t)},expression:"form.Remarks"}})],1)],1)],1)],1),r("el-row",[r("footer",[e.isDisabled?e._e():r("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("提交")]),r("el-button",{on:{click:e.toBack}},[e._v("取消")])],1)])],1)])},o=[]},"1fdc":function(e,t,r){"use strict";r("ffbe")},"44e5a":function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(r("c14f")),i=a(r("1da1"));r("99af"),r("4de4"),r("7db0"),r("caad"),r("d81d"),r("e9f5"),r("910d"),r("f665"),r("ab43"),r("a9e3"),r("dca8"),r("d3b7"),r("2532");var n=r("be22"),s=r("cf45"),c=r("7757"),l=r("8cdf"),u=r("699e"),d=r("ed08");t.default={name:"PROServiceRegisterAdd",components:{},props:{},data:function(){var e=this;return{form:{Receipt_No:"",Invoice_Type:0,Accounting_Date:new Date,Company_Id:localStorage.getItem("Last_Working_Object_Id"),Accumulative_Price:"",Accumulative_Dose:"",Contract_Unit:"",Output_Tax_Rate:"",Factory_Id:"",Unit_Price:"",Depart_Id:"",Feeding_Mode_Name:"",Feeding_Mode:"",Sys_Project_Id:""},factoryDisabled:!1,disabledDepart:!1,IncomeType:[],ProjectList:[],departmentlist:[],factoryOption:[],type:"",obj:{},isDisabled:!0,isDisabled1:!0,backendDate:null,pickerOptions:{disabledDate:function(t){return t.getTime()<=new Date(e.backendDate).getTime()}},tableData:[{Pay_Date:"",Pay_Price:"",Payee:"",Pay_Depart_Id:"",Remark:"2"}],rules:{Factory_Id:[{required:!0,message:"请选择归属基地",trigger:"change"}],Current_Dose:[{required:!0,message:"请输入本次报量",trigger:"blur"}],Unit_Price:[{required:!0,message:"请输入单价",trigger:"blur"}],Accounting_Date:[{required:!0,message:"请选择日期",trigger:"change"}],Depart_Id:[{required:!0,message:"请选择登记部门",trigger:"change"}],Sys_Project_Id:[{required:!0,message:"请选择归属项目",trigger:"change"}],Coefficient:[{required:!0,message:"请输入系数",trigger:"blur"}],Invoice_Type:[{required:!0,message:"请选择票据类型",trigger:"change"}],Output_Tax_Rate:[{required:!0,message:"请输入税率",trigger:"blur"}],Payer:[{required:!0,message:"请输入付款方",trigger:"blur"}]}}},computed:{getAccountingAmount:function(){return this.form.Accounting_Price=Number(this.form.Unit_Price)*Number(this.form.Current_Dose)||0}},watch:{},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.obj=e.$route.query,t.n=1,e.getPermission();case 1:return t.n=2,e.getOptions();case 2:return t.n=3,(0,s.getDictionary)("IncomeType");case 3:e.IncomeType=t.v,"view"!=e.obj.type&&"edit"!=e.obj.type||(e.getLabourRegistratioDetails(e.obj.Id),e.changeValue(e.form.Factory_Id),e.getTimeData(e.form.Factory_Id)),"view"==e.obj.type?e.isDisabled=!0:e.isDisabled=!1;case 4:return t.a(2)}}),t)})))()},methods:{getTimeData:function(e){var t=this;return(0,i.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,c.GetOMALatestAccountingDate)({FactoryId:e});case 1:a=r.v,a.IsSucceed?t.backendDate=a.Data||"":t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getPermission:function(){var e=this;return new Promise((function(t,r){e.permissionList=[],(0,n.GetUserDepartTypePermission)({}).then((function(a){a.IsSucceed?(e.permissionList=a.Data,t()):(e.$message({message:a.Message,type:"error"}),r())}))}))},getProLabel:function(e){return"".concat(e.Short_Name,"（").concat(e.Status,"）")},changeType:function(e){0==e&&(this.form.Output_Tax_Rate="")},changeValue:function(e){this.getFirstLevelDepartsUnderFactory(e),this.getFactoryProjectList(e)},getOptions:function(){var e,t=this;e="view"==this.obj.type?c.GetQueryNonExternalFactory:c.GetNonExternalFactory,e({}).then((function(e){if(e.IsSucceed){t.factoryOption=Object.freeze(e.Data||[]);var r=t.factoryOption.find((function(e){return e.Is_Cur_User_Factory}));r&&t.factoryOption.length&&"add"==t.obj.type&&(t.form.Factory_Id=r.Id,t.changeValue(t.form.Factory_Id),t.getTimeData(t.form.Factory_Id)),t.factoryDisabled=r&&0===t.permissionList.length}else t.$message({message:e.Message,type:"error"})}))},selectOrderId:function(){var e=this,t=this.ProjectList.find((function(t){return t.Sys_Project_Id===e.form.Sys_Project_Id}));this.form.Feeding_Mode_Name=t.FeedingMethod,this.form.Feeding_Mode=t.FeedingMethodID,this.form.Contract_Unit=t.FBDW_Name,this.form.Contract_No=t.Contract_No,this.form.Unit_Price=t.Unit_Price,this.form.Accumulative_Dose=t.Accumulative_Dose,this.form.Accumulative_Price=t.Accumulative_Price},getFirstLevelDepartsUnderFactory:function(e){var t=this;return(0,i.default)((0,o.default)().m((function r(){var a,i,n,s;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,l.GetFirstLevelDepartsUnderFactory)({FactoryId:e});case 1:a=r.v,a.IsSucceed?(i=a.Data||[],t.permissionList.length?(n=i.filter((function(e){return t.permissionList.includes(e.Type)||e.Is_Cur_User_Depart})),t.departmentlist=n):t.departmentlist=i,s=t.departmentlist.find((function(e){return e.Is_Cur_User_Depart})),s?t.factoryOption.length&&"add"===t.obj.type&&(t.form.Depart_Id=s.Id):t.factoryOption.length&&"add"===t.obj.type&&(t.form.Depart_Id=""),t.disabledDepart=s&&0===t.permissionList.length):t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getLabourRegistratioDetails:function(e){var t=this;return(0,i.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,u.LabourRegistratioDetails)({Id:e});case 1:a=r.v,a.IsSucceed?(t.form=a.Data||[],t.changeValue(t.form.Factory_Id),t.getTimeData(t.form.Factory_Id)):t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getFactoryProjectList:function(e){var t=this;return(0,i.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,l.GetFactoryProjectList)({FactoryId:e});case 1:a=r.v,a.IsSucceed?t.ProjectList=Object.freeze(a.Data.map((function(e){return e.Status||e.Type?e.label="".concat(e.Short_Name,"(").concat(e.Status&&e.Type?"".concat(e.Status,"/").concat(e.Type):"".concat(e.Status||e.Type),")"):e.label=e.Short_Name,e}))):t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getFactory:function(){var e=this;(0,c.GetNonExternalFactory)({}).then((function(t){t.IsSucceed?e.factoryOption=t.Data||[]:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;"add"==e.obj.type?(0,u.SaveLabourRegistration)(e.form).then((function(t){t.IsSucceed?(e.$message.success("提交成功"),e.toBack()):e.$message({message:t.Message,type:"error"})})):(e.form.Id=e.obj.Id,(0,u.EditLabourRegistration)(e.form).then((function(t){t.IsSucceed?(e.$message.success("编辑成功"),e.toBack()):e.$message({message:t.Message,type:"error"})})))}))},toBack:function(){(0,d.closeTagView)(this.$store,this.$route)}}}},dca8:function(e,t,r){"use strict";var a=r("23e7"),o=r("bb2f"),i=r("d039"),n=r("861d"),s=r("f183").onFreeze,c=Object.freeze,l=i((function(){c(1)}));a({target:"Object",stat:!0,forced:l,sham:!o},{freeze:function(e){return c&&n(e)?c(s(e)):e}})},ffbe:function(e,t,r){}}]);