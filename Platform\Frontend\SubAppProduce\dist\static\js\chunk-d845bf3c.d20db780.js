(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-d845bf3c"],{"38cd":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("5530"));n("4de4"),n("d81d"),n("e9f5"),n("910d"),n("ab43"),n("a9e3"),n("b680"),n("d3b7"),n("3ca3"),n("ddb0");var l=a(n("34e9")),r=a(n("c1df")),i=n("5e99"),u=n("6b87"),s=n("8975"),c=n("f2f6"),d=n("1b69");e.default={name:"PRODayReport",components:{Header:l.default},data:function(){return{form:{finishDate:"",factoryId:"",project:"",unit:""},calendarTime:"",factoryName:"",tableData:[],projectOptions:[],unitOptions:[],options:[],Weight:0,Num:0}},created:function(){var t=this;this.form.finishDate=(0,r.default)().format("YYYY-MM-DD"),this.calendarTime=(0,r.default)().format("YYYY年MM月DD日"),Promise.all([(0,i.GetFactoryList)({}).then((function(e){e.IsSucceed?(t.options=e.Data,t.form.factoryId=e.Data[0].Id,t.factoryName=e.Data[0].Name):t.$message({type:"error",message:e.Message})}))]).then((function(){t.fetchData(),t.getProject()}))},methods:{fetchData:function(){var t=this;this.calendarTime=(0,r.default)(this.form.finishDate).format("YYYY年MM月DD日"),this.factoryName=this.options.filter((function(e){if(e.Id===t.form.factoryId)return e}))[0].Name,(0,u.GetComponentProducedDay)((0,o.default)({},this.form)).then((function(e){if(e.IsSucceed){t.tableData=e.Data;var n=0;t.tableData.map((function(t){return t.Scheduling_Date=(0,s.timeFormat)(t.Scheduling_Date),t.Actual_Finish_Date=(0,s.timeFormat)(t.Actual_Finish_Date),n+=Number(t.Netweight),t})),t.Weight=n.toFixed(2),t.Num=t.tableData.length}else t.$message({type:"error",message:e.Message})}))},handleExport:function(){var t=this,e=["项目","生产单元","构件大类","构件小类","构件编号","唯一码","规格","重量(kg)","排产日期","完成日期"],a=["Project_Name","InstallUnit_Name","Type_Name","Sub_Type_Name","Code","Unique_Code","Spec","Netweight","Scheduling_Date","Actual_Finish_Date"],o=this.formatJson(a,this.tableData);n.e("chunk-2d0cc0b6").then(n.t.bind(null,"4bf8",7)).then((function(n){n.export_json_to_excel({header:e,data:o,filename:"".concat(t.calendarTime," 生产日报"),autoWidth:!0,bookType:"xlsx"})}))},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},getProject:function(){var t=this;(0,d.GetProjectList)({}).then((function(e){e.IsSucceed?t.projectOptions=e.Data:t.$message({message:e.Message,type:"error"})}))},getUnitList:function(t){var e=this;(0,c.GetInstallUnitList)({Project_Id:t}).then((function(t){t.IsSucceed?e.unitOptions=t.Data:e.$message({message:t.Message,type:"error"})}))},projectChange:function(t){this.form.unit="",this.getUnitList(t),this.fetchData()},unitChange:function(t){this.fetchData()}}}},4132:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"box-card"},[n("Header",{attrs:{padding:"0"},scopedSlots:t._u([{key:"left",fn:function(){return[n("el-form",{attrs:{"label-width":"80px",inline:!0}},[t._e(),n("el-form-item",{attrs:{label:"日期"}},[n("el-date-picker",{attrs:{type:"date",clearable:!1,"value-format":"yyyy-MM-dd",placeholder:"选择日期"},on:{change:t.fetchData},model:{value:t.form.finishDate,callback:function(e){t.$set(t.form,"finishDate",e)},expression:"form.finishDate"}})],1),n("el-form-item",{attrs:{label:"项目"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:t.projectChange},model:{value:t.form.project,callback:function(e){t.$set(t.form,"project",e)},expression:"form.project"}},t._l(t.projectOptions,(function(t){return n("el-option",{key:t.value,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"安装单元"}},[n("el-select",{attrs:{disabled:!t.form.project,placeholder:"请选择",clearable:""},on:{change:t.unitChange},model:{value:t.form.unit,callback:function(e){t.$set(t.form,"unit",e)},expression:"form.unit"}},t._l(t.unitOptions,(function(t){return n("el-option",{key:t.value,attrs:{label:t.Name,value:t.Id}})})),1)],1)],1)]},proxy:!0},{key:"right",fn:function(){return[n("el-button",{attrs:{type:"success"},on:{click:t.handleExport}},[t._v("导出")])]},proxy:!0}])}),n("h4",{staticStyle:{"text-align":"center"}},[t._v(" "+t._s(t.calendarTime)+"生产日报 ")]),n("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"660",border:""},scopedSlots:t._u([{key:"append",fn:function(){return[n("div",{staticClass:"sum"},[t._v(" 今日完成："+t._s((t.Weight/1e3).toFixed(2))+" 吨；共 "+t._s(t.Num)+" 件 ")])]},proxy:!0}])},[n("el-table-column",{attrs:{prop:"Project_Name","show-overflow-tooltip":"",label:"项目"}}),n("el-table-column",{attrs:{prop:"InstallUnit_Name","show-overflow-tooltip":"",label:"生产单元"}}),n("el-table-column",{attrs:{prop:"Type_Name","show-overflow-tooltip":"",label:"构件大类"}}),n("el-table-column",{attrs:{prop:"Sub_Type_Name","show-overflow-tooltip":"",label:"构件大类"}}),n("el-table-column",{attrs:{prop:"Code","show-overflow-tooltip":"",label:"构件编号"}}),n("el-table-column",{attrs:{prop:"Unique_Code","show-overflow-tooltip":"",label:"唯一码"}}),n("el-table-column",{attrs:{prop:"Spec","show-overflow-tooltip":"",label:"规格"}}),n("el-table-column",{attrs:{prop:"Netweight","show-overflow-tooltip":"",label:"重量(kg)"}}),n("el-table-column",{attrs:{prop:"Scheduling_Date","show-overflow-tooltip":"",label:"排产日期"}}),n("el-table-column",{attrs:{prop:"Actual_Finish_Date","show-overflow-tooltip":"",label:"完成日期"}})],1)],1)],1)},o=[]},"4a20":function(t,e,n){"use strict";n.r(e);var a=n("4132"),o=n("61ac");for(var l in o)["default"].indexOf(l)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(l);n("5fefa");var r=n("2877"),i=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"79a0ea75",null);e["default"]=i.exports},"5fefa":function(t,e,n){"use strict";n("e88e")},"61ac":function(t,e,n){"use strict";n.r(e);var a=n("38cd"),o=n.n(a);for(var l in a)["default"].indexOf(l)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(l);e["default"]=o.a},"6b87":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetComponentCountByMonth=c,e.GetComponentInfoPageList=p,e.GetComponentNoProduceDetailPageList=i,e.GetComponentPageList=f,e.GetComponentProducedByDays=l,e.GetComponentProducedDay=s,e.GetFactoryComponentYield=P,e.GetFactorySchdulingPlanYield=b,e.GetFactoryTeamYield=h,e.GetFactoryTeamYieldForDay=m,e.GetInstallUnitProducedCount=r,e.GetProducedDetailPageList=u,e.GetTeamProducedCountByDate=d;var o=a(n("b775"));a(n("4328"));function l(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentProducedByDays",method:"post",data:t})}function r(t){return(0,o.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentNoProduceDetailPageList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ProductionCount/GetProducedDetailPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentProducedDay",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentCountByMonth",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/ProductionCount/GetTeamProducedCountByDate",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Component/GetComponentPageList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Component/GetComponentInfoPageList",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/ProductionCount/GetFactoryTeamYieldForDay",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/ProductionCount/GetFactoryTeamYield",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/ProductionCount/GetFactoryComponentYield",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/Component/GetFactorySchdulingPlanYield",method:"post",data:t})}},e88e:function(t,e,n){},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=u,e.CheckPlanTime=s,e.DeleteInstallUnit=p,e.GetCompletePercent=I,e.GetEntity=g,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=b,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=i,e.GetInstallUnitPageList=r,e.GetProjectInstallUnitList=y,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=m,e.SaveInstallUnit=P,e.SaveOhterSourceInstallUnit=D;var o=a(n("b775")),l=a(n("4328"));function r(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function s(t){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function p(t){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function m(t){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function g(t){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:l.default.stringify(t)})}function D(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);