(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-30a601f0","chunk-2d0b8e66","chunk-2ca64e87"],{"0775":function(e,t,r){"use strict";r.r(t);var a=r("ad3a"),n=r("22e9");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);r("2df4");var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,"e7cac7d6",null);t["default"]=s.exports},"0ccb7":function(e,t,r){"use strict";r.r(t);var a=r("5fb5"),n=r("b306");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,null,null);t["default"]=s.exports},"0ea9":function(e,t,r){"use strict";r("99d0")},"0f54":function(e,t,r){"use strict";r.r(t);var a=r("594c"),n=r("3f7ed");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);r("0ea9");var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,"28ade4d2",null);t["default"]=s.exports},1180:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteQualifiedProvider=w,t.DeleteSubcontractor=_,t.DeleteSupplier=f,t.ExportProviderList=N,t.ExportQualifiedProviderInfoList=k,t.GenerateNew=i,t.GenerateNewSubcontractor=h,t.GetProviderList=I,t.GetProviderPageList=A,t.GetProviderStatus=u,t.GetQualifiedEntity=j,t.GetQualifiedProviderInfoPageList=x,t.GetQualifiedProviderPageList=P,t.GetSubcontractorEntity=v,t.GetSubcontractorPageList=p,t.GetSupplierEntity=l,t.GetSupplierPageList=o,t.SaveQualifiedProvider=C,t.SaveSubcontractor=g,t.SaveSupplier=s,t.SubmitSubcontractorInfo=y,t.SubmitSupplierInfo=c,t.SupplySubcontractor=S,t.SupplySupplier=m,t.UpdateSubcontractorStatus=b,t.UpdateSupplierStatus=d;var n=a(r("b775"));function o(e){return(0,n.default)({url:"/EPC/Supplier/GetSupplierPageList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/EPC/Supplier/GenerateNew",method:"post",data:e})}function s(e){return(0,n.default)({url:"/EPC/Supplier/SaveSupplier",method:"post",data:e})}function l(e){return(0,n.default)({url:"/EPC/Supplier/GetSupplierEntity",method:"post",data:e})}function u(e){return(0,n.default)({url:"/EPC/Supplier/GetProviderStatus",method:"post",data:e})}function c(e){return(0,n.default)({url:"/EPC/Supplier/SubmitSupplierInfo",method:"post",data:e})}function d(e){return(0,n.default)({url:"/EPC/Supplier/UpdateSupplierStatus",method:"post",data:e})}function f(e){return(0,n.default)({url:"/EPC/Supplier/DeleteSupplier",method:"post",data:e})}function m(e){return(0,n.default)({url:"/EPC/Supplier/SupplySupplier",method:"post",data:e})}function p(e){return(0,n.default)({url:"/EPC/Subcontractor/GetSubcontractorPageList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/EPC/Subcontractor/GenerateNew",method:"post",data:e})}function _(e){return(0,n.default)({url:"/EPC/Subcontractor/DeleteSubcontractor",method:"post",data:e})}function v(e){return(0,n.default)({url:"/EPC/Subcontractor/GetSubcontractorEntity",method:"post",data:e})}function g(e){return(0,n.default)({url:"/EPC/Subcontractor/SaveSubcontractor",method:"post",data:e})}function b(e){return(0,n.default)({url:"/EPC/Subcontractor/UpdateSubcontractorStatus",method:"post",data:e})}function y(e){return(0,n.default)({url:"/EPC/Subcontractor/SubmitSubcontractorInfo",method:"post",data:e})}function S(e){return(0,n.default)({url:"/EPC/Subcontractor/SupplySubcontractor",method:"post",data:e})}function P(e){return(0,n.default)({url:"/EPC/QualifiedProvider/GetQualifiedProviderPageList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/EPC/QualifiedProvider/GetProviderList",method:"post",data:e})}function C(e){return(0,n.default)({url:"/EPC/QualifiedProvider/SaveQualifiedProvider",method:"post",data:e})}function w(e){return(0,n.default)({url:"/EPC/QualifiedProvider/DeleteQualifiedProvider",method:"post",data:e})}function j(e){return(0,n.default)({url:"/EPC/QualifiedProvider/GetEntity",method:"post",data:e})}function x(e){return(0,n.default)({url:"/EPC/QualifiedProvider/GetQualifiedProviderInfoPageList",method:"post",data:e})}function k(e){return(0,n.default)({url:"/EPC/QualifiedProvider/ExportQualifiedProviderInfoList",method:"post",data:e})}function A(e){return(0,n.default)({url:"/EPC/QualifiedProvider/GetProviderPageList",method:"post",data:e})}function N(e){return(0,n.default)({url:"/EPC/QualifiedProvider/ExportProviderList",method:"post",data:e})}},"159c":function(e,t,r){"use strict";r.r(t);var a=r("698b"),n=r("a518");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);r("e543");var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,"ab01ab64",null);t["default"]=s.exports},"1b93":function(e,t,r){},"222a":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"108px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属项目",required:"",prop:"Project_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择所属项目",filterable:""},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projs,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Project_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"工期",required:"",prop:"Schedule"}},[e.isEdit?r("el-input",{model:{value:e.form.Schedule,callback:function(t){e.$set(e.form,"Schedule",t)},expression:"form.Schedule"}}):r("span",[e._v(e._s(e.form.Schedule))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"特定内容",required:"",prop:"Content"}},[e.isEdit?r("el-input",{model:{value:e.form.Content,callback:function(t){e.$set(e.form,"Content",t)},expression:"form.Content"}}):r("span",[e._v(e._s(e.form.Content))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合作形式",required:"",prop:"Project_Category"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选合作形式"},model:{value:e.form.Project_Category,callback:function(t){e.$set(e.form,"Project_Category",t)},expression:"form.Project_Category"}},e._l(e.cooperations,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Project_Category_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同编号",required:"",prop:"Contract_Code"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Code,callback:function(t){e.$set(e.form,"Contract_Code",t)},expression:"form.Contract_Code"}}):r("span",[e._v(e._s(e.form.Contract_Code))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同名称",required:"",prop:"Contract_Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Name,callback:function(t){e.$set(e.form,"Contract_Name",t)},expression:"form.Contract_Name"}}):r("span",[e._v(e._s(e.form.Contract_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"承揽单位",required:"",prop:"Provider_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选承揽单位",filterable:""},model:{value:e.form.Provider_Id,callback:function(t){e.$set(e.form,"Provider_Id",t)},expression:"form.Provider_Id"}},e._l(e.providers,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Supplier_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同价格",required:"",prop:"Price"}},[e.isEdit?r("el-input-number",{attrs:{min:0},model:{value:e.form.Price,callback:function(t){e.$set(e.form,"Price",e._n(t))},expression:"form.Price"}}):r("span",[e._v(e._s(e.form.Price))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"特定采购形式",required:"",prop:"Given_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Given_Style,callback:function(t){e.$set(e.form,"Given_Style",t)},expression:"form.Given_Style"}}):r("span",[e._v(e._s(e.form.Given_Style))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},n=[]},"22e9":function(e,t,r){"use strict";r.r(t);var a=r("de2d"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},"2c6b":function(e,t,r){},"2df4":function(e,t,r){"use strict";r("a0bb")},"2e74":function(e,t,r){"use strict";var a=r("dbce").default,n=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("4de4"),r("7db0"),r("d81d"),r("14d9"),r("b0c0"),r("e9f5"),r("910d"),r("f665"),r("7d54"),r("ab43"),r("a9e3"),r("d3b7"),r("18a5"),r("159b");var o=n(r("5530")),i=r("2f62"),s=a(r("313e")),l=r("e193"),u=r("def8"),c=r("b6b8"),d=n(r("f67b"));t.default={name:"FlowNodeSet",components:{FlowArea:d.default},props:{flow:{type:Object,default:null},fnodes:{type:Array,default:function(){return[]}}},data:function(){return{nodes:[],chart:null,currentSelectGroup:[],flowData:{nodes:[],lines:[],attr:{id:""},config:{showGrid:!0,showGridText:"隐藏网格",showGridIcon:"el-icon-view"},status:u.flowConfig.flowStatus.CREATE,remarks:[]}}},computed:(0,o.default)({},(0,i.mapGetters)({currentSelect:"currentSelect"})),watch:{currentSelect:function(e){e&&e.id&&["end round","start round mix"].indexOf(e.type)<0&&this.$emit("openDrawer",e)}},created:function(){u.flowConfig.contextMenu.node={},u.flowConfig.contextMenu.container={},this.flowData=this.groupSchemeContent(),this.initJsPlumb(),this.loadFlow()},mounted:function(){},methods:{resetFlow:function(){var e=this;this.chart=s.init(this.$refs.chart);var t={title:{text:""},tooltip:{},animationDurationUpdate:1500,animationEasingUpdate:"quinticInOut",series:[{type:"graph",layout:"none",symbolSize:50,roam:!0,label:{show:!0},edgeSymbol:["circle","arrow"],edgeSymbolSize:[4,10],edgeLabel:{fontSize:20},data:[],links:[{source:0,target:1,symbolSize:[5,20],label:{show:!0},lineStyle:{width:5,curveness:.2}},{source:"Node 2",target:"Node 1",label:{show:!0},lineStyle:{curveness:.2}},{source:"Node 1",target:"Node 3"},{source:"Node 2",target:"Node 3"},{source:"Node 2",target:"Node 4"},{source:"Node 1",target:"Node 4"}],lineStyle:{opacity:.9,width:2,curveness:0}}]},r=200;t.series[0].data=this.flow.nodes.map((function(e,t){return{name:e.name,x:r+e.width*t,y:r+e.height*t}})),t.series[0].links=this.flow.lines.map((function(t){return{source:e.flow.nodes.find((function(e){return e.id===t.from}))?e.flow.nodes.find((function(e){return e.id===t.from})).name:"",target:e.flow.nodes.find((function(e){return e.id===t.to}))?e.flow.nodes.find((function(e){return e.id===t.to})).name:""}})),this.chart.setOption(t)},validate:function(){var e=this;return new Promise((function(t,r){e.fnodes.find((function(e){return!e.User_Ids}))?r("必须选择节点审批人员"):t(!0)}))},getData:function(){return this.fnodes},groupSchemeContent:function(){var e=Object.assign({},this.flow);if(!e.attr||!e.attr.id){var t=e.lines,r=e.nodes;r.length>0&&r.forEach((function(e){e.top=e.top<3e3?3e3+Number(e.top):e.top,e.left=e.left<3e3?3e3+Number(e.left):e.left,e.setInfo=e.setInfo||{NodeRejectType:0,NodeConfluenceType:"",NodeDesignate:"",ThirdPartyUrl:"",NodeDesignateData:{users:[],roles:[],departments:[],usersText:"",rolesText:"",departmentsText:"",Texts:""}}})),t.length>0&&t.forEach((function(e){e.label=e.label||e.name,e.cls={linkType:"Flowchart",linkColor:"#2a2929",linkThickness:2}})),e.attr={id:e.id||""},e.config={showGrid:!0,showGridText:"隐藏网格",showGridIcon:"el-icon-view"}}return e},initJsPlumb:function(){var e=this;e.plumb=l.jsPlumb.getInstance(u.flowConfig.jsPlumbInsConfig),this.loadFlowArea=!0,this.plumb.ready((function(){e.plumb.bind("beforeDrop",(function(t){var r=t.sourceId,a=t.targetId;if(r===a)return!1;var n=e.flowData.lines.filter((function(e){return e.from===r&&e.to===a}));return!(n.length>0)||(e.$message.error("同方向的两节点连线只能有一条！"),!1)}));var t=0;e.plumb.bind("connection",(function(r){var a,n=r.connection.canvas,o={},i="";if(e.flowData.status===u.flowConfig.flowStatus.CREATE||e.flowData.status===u.flowConfig.flowStatus.MODIFY)i="link-"+c.ZFSN.getId(),a="";else if(e.flowData.status===u.flowConfig.flowStatus.LOADING){var s=e.flowData.lines[t];i=s.id,a=s.label,t++}n.id=i,o.type="sl",o.id=i,o.from=r.sourceId,o.to=r.targetId,o.label=a,o.cls={linkType:u.flowConfig.jsPlumbInsConfig.Connector[0],linkColor:u.flowConfig.jsPlumbInsConfig.PaintStyle.stroke,linkThickness:u.flowConfig.jsPlumbInsConfig.PaintStyle.strokeWidth},document.getElementById(i).addEventListener("click",(function(t){var r=window.event||t;r.stopPropagation();var a=e.flowData.lines.filter((function(e){return e.id===i}))[0],n=[{FieldName:"",Operation:"",Value:""}];a.Compares=a.Compares||n})),e.flowData.status!==u.flowConfig.flowStatus.LOADING&&e.flowData.lines.push(o)})),e.plumb.importDefaults({ConnectionsDetachable:u.flowConfig.jsPlumbConfig.conn.isDetachable})}))},loadFlow:function(){var e=this;e.flowData.status=u.flowConfig.flowStatus.LOADING,this.plumb.ready((function(){var t=Object.assign([],e.flowData.lines);e.$nextTick((function(){t.forEach((function(t){var r=e.plumb.connect({source:t.from,target:t.to,anchor:u.flowConfig.jsPlumbConfig.anchor.default,connector:[t.cls.linkType,{gap:5,cornerRadius:8,alwaysRespectStubs:!0}],paintStyle:{stroke:t.cls.linkColor,strokeWidth:t.cls.linkThickness}});""!==t.label&&r.setLabel({label:t.label,cssClass:"linkLabel"})}));e.currentSelectGroup=[],e.flowData.status=u.flowConfig.flowStatus.MODIFY}))}))}}}},"313e":function(e,t,r){"use strict";r.r(t),r.d(t,"version",(function(){return n["cb"]})),r.d(t,"dependencies",(function(){return n["l"]})),r.d(t,"PRIORITY",(function(){return n["g"]})),r.d(t,"init",(function(){return n["B"]})),r.d(t,"connect",(function(){return n["j"]})),r.d(t,"disconnect",(function(){return n["n"]})),r.d(t,"disConnect",(function(){return n["m"]})),r.d(t,"dispose",(function(){return n["o"]})),r.d(t,"getInstanceByDom",(function(){return n["w"]})),r.d(t,"getInstanceById",(function(){return n["x"]})),r.d(t,"registerTheme",(function(){return n["R"]})),r.d(t,"registerPreprocessor",(function(){return n["P"]})),r.d(t,"registerProcessor",(function(){return n["Q"]})),r.d(t,"registerPostInit",(function(){return n["N"]})),r.d(t,"registerPostUpdate",(function(){return n["O"]})),r.d(t,"registerUpdateLifecycle",(function(){return n["T"]})),r.d(t,"registerAction",(function(){return n["H"]})),r.d(t,"registerCoordinateSystem",(function(){return n["I"]})),r.d(t,"getCoordinateSystemDimensions",(function(){return n["v"]})),r.d(t,"registerLocale",(function(){return n["L"]})),r.d(t,"registerLayout",(function(){return n["J"]})),r.d(t,"registerVisual",(function(){return n["U"]})),r.d(t,"registerLoading",(function(){return n["K"]})),r.d(t,"setCanvasCreator",(function(){return n["V"]})),r.d(t,"registerMap",(function(){return n["M"]})),r.d(t,"getMap",(function(){return n["y"]})),r.d(t,"registerTransform",(function(){return n["S"]})),r.d(t,"dataTool",(function(){return n["k"]})),r.d(t,"zrender",(function(){return n["eb"]})),r.d(t,"matrix",(function(){return n["D"]})),r.d(t,"vector",(function(){return n["bb"]})),r.d(t,"zrUtil",(function(){return n["db"]})),r.d(t,"color",(function(){return n["i"]})),r.d(t,"throttle",(function(){return n["X"]})),r.d(t,"helper",(function(){return n["A"]})),r.d(t,"use",(function(){return n["Z"]})),r.d(t,"setPlatformAPI",(function(){return n["W"]})),r.d(t,"parseGeoJSON",(function(){return n["F"]})),r.d(t,"parseGeoJson",(function(){return n["G"]})),r.d(t,"number",(function(){return n["E"]})),r.d(t,"time",(function(){return n["Y"]})),r.d(t,"graphic",(function(){return n["z"]})),r.d(t,"format",(function(){return n["u"]})),r.d(t,"util",(function(){return n["ab"]})),r.d(t,"env",(function(){return n["p"]})),r.d(t,"List",(function(){return n["e"]})),r.d(t,"Model",(function(){return n["f"]})),r.d(t,"Axis",(function(){return n["a"]})),r.d(t,"ComponentModel",(function(){return n["c"]})),r.d(t,"ComponentView",(function(){return n["d"]})),r.d(t,"SeriesModel",(function(){return n["h"]})),r.d(t,"ChartView",(function(){return n["b"]})),r.d(t,"innerDrawElementOnCanvas",(function(){return n["C"]})),r.d(t,"extendComponentModel",(function(){return n["r"]})),r.d(t,"extendComponentView",(function(){return n["s"]})),r.d(t,"extendSeriesModel",(function(){return n["t"]})),r.d(t,"extendChartView",(function(){return n["q"]}));var a=r("22b4"),n=r("aa74"),o=r("f95e"),i=r("97ac"),s=r("3620"),l=r("4cb5"),u=r("49bba"),c=r("acf6"),d=r("e8e6"),f=r("b37b"),m=r("54ca"),p=r("128d"),h=r("efb0"),_=r("9be8"),v=r("e275"),g=r("7b72"),b=r("10e8e"),y=r("0d95"),S=r("b489"),P=r("2564"),I=r("14bf"),C=r("0eed"),w=r("583f"),j=r("c835b"),x=r("8acb"),k=r("052f"),A=r("4b2a"),N=r("bb6f"),O=r("b25d"),E=r("5334"),D=r("4bd9"),G=r("b899"),q=r("5a72"),T=r("3094"),$=r("2da7"),M=r("af5c"),F=r("b22b"),R=r("9394"),V=r("541a"),L=r("a0c6"),U=r("9502"),B=r("4231"),z=r("ff32"),Q=r("104d"),Y=r("e1ff"),H=r("ac12"),J=r("abd2"),Z=r("7c0d"),W=r("c436"),K=r("47e7"),X=r("e600"),ee=r("5e81"),te=r("4f85"),re=r("6d8b"),ae=r("4a3f"),ne=r("cbe5"),oe=r("401b"),ie=r("342d"),se=r("8582"),le=r("e263"),ue=r("9850"),ce=r("dce8"),de=r("87b1"),fe=r("c7a2"),me=r("4aa2"),pe=r("20c8"),he=pe["a"].CMD;function _e(e,t){return Math.abs(e-t)<1e-5}function ve(e){var t,r,a,n,o,i=e.data,s=e.len(),l=[],u=0,c=0,d=0,f=0;function m(e,r){t&&t.length>2&&l.push(t),t=[e,r]}function p(e,r,a,n){_e(e,a)&&_e(r,n)||t.push(e,r,a,n,a,n)}function h(e,r,a,n,o,i){var s=Math.abs(r-e),l=4*Math.tan(s/4)/3,u=r<e?-1:1,c=Math.cos(e),d=Math.sin(e),f=Math.cos(r),m=Math.sin(r),p=c*o+a,h=d*i+n,_=f*o+a,v=m*i+n,g=o*l*u,b=i*l*u;t.push(p-g*d,h+b*c,_+g*m,v-b*f,_,v)}for(var _=0;_<s;){var v=i[_++],g=1===_;switch(g&&(u=i[_],c=i[_+1],d=u,f=c,v!==he.L&&v!==he.C&&v!==he.Q||(t=[d,f])),v){case he.M:u=d=i[_++],c=f=i[_++],m(d,f);break;case he.L:r=i[_++],a=i[_++],p(u,c,r,a),u=r,c=a;break;case he.C:t.push(i[_++],i[_++],i[_++],i[_++],u=i[_++],c=i[_++]);break;case he.Q:r=i[_++],a=i[_++],n=i[_++],o=i[_++],t.push(u+2/3*(r-u),c+2/3*(a-c),n+2/3*(r-n),o+2/3*(a-o),n,o),u=n,c=o;break;case he.A:var b=i[_++],y=i[_++],S=i[_++],P=i[_++],I=i[_++],C=i[_++]+I;_+=1;var w=!i[_++];r=Math.cos(I)*S+b,a=Math.sin(I)*P+y,g?(d=r,f=a,m(d,f)):p(u,c,r,a),u=Math.cos(C)*S+b,c=Math.sin(C)*P+y;for(var j=(w?-1:1)*Math.PI/2,x=I;w?x>C:x<C;x+=j){var k=w?Math.max(x+j,C):Math.min(x+j,C);h(x,k,b,y,S,P)}break;case he.R:d=u=i[_++],f=c=i[_++],r=d+i[_++],a=f+i[_++],m(r,f),p(r,f,r,a),p(r,a,d,a),p(d,a,d,f),p(d,f,r,f);break;case he.Z:t&&p(u,c,d,f),u=d,c=f;break}}return t&&t.length>2&&l.push(t),l}function ge(e,t,r,a,n,o,i,s,l,u){if(_e(e,r)&&_e(t,a)&&_e(n,i)&&_e(o,s))l.push(i,s);else{var c=2/u,d=c*c,f=i-e,m=s-t,p=Math.sqrt(f*f+m*m);f/=p,m/=p;var h=r-e,_=a-t,v=n-i,g=o-s,b=h*h+_*_,y=v*v+g*g;if(b<d&&y<d)l.push(i,s);else{var S=f*h+m*_,P=-f*v-m*g,I=b-S*S,C=y-P*P;if(I<d&&S>=0&&C<d&&P>=0)l.push(i,s);else{var w=[],j=[];Object(ae["g"])(e,r,n,i,.5,w),Object(ae["g"])(t,a,o,s,.5,j),ge(w[0],j[0],w[1],j[1],w[2],j[2],w[3],j[3],l,u),ge(w[4],j[4],w[5],j[5],w[6],j[6],w[7],j[7],l,u)}}}}function be(e,t){var r=ve(e),a=[];t=t||1;for(var n=0;n<r.length;n++){var o=r[n],i=[],s=o[0],l=o[1];i.push(s,l);for(var u=2;u<o.length;){var c=o[u++],d=o[u++],f=o[u++],m=o[u++],p=o[u++],h=o[u++];ge(s,l,c,d,f,m,p,h,i,t),s=p,l=h}a.push(i)}return a}function ye(e,t,r){var a=e[t],n=e[1-t],o=Math.abs(a/n),i=Math.ceil(Math.sqrt(o*r)),s=Math.floor(r/i);0===s&&(s=1,i=r);for(var l=[],u=0;u<i;u++)l.push(s);var c=i*s,d=r-c;if(d>0)for(u=0;u<d;u++)l[u%i]+=1;return l}function Se(e,t,r){for(var a=e.r0,n=e.r,o=e.startAngle,i=e.endAngle,s=Math.abs(i-o),l=s*n,u=n-a,c=l>Math.abs(u),d=ye([l,u],c?0:1,t),f=(c?s:u)/d.length,m=0;m<d.length;m++)for(var p=(c?u:s)/d[m],h=0;h<d[m];h++){var _={};c?(_.startAngle=o+f*m,_.endAngle=o+f*(m+1),_.r0=a+p*h,_.r=a+p*(h+1)):(_.startAngle=o+p*h,_.endAngle=o+p*(h+1),_.r0=a+f*m,_.r=a+f*(m+1)),_.clockwise=e.clockwise,_.cx=e.cx,_.cy=e.cy,r.push(_)}}function Pe(e,t,r){for(var a=e.width,n=e.height,o=a>n,i=ye([a,n],o?0:1,t),s=o?"width":"height",l=o?"height":"width",u=o?"x":"y",c=o?"y":"x",d=e[s]/i.length,f=0;f<i.length;f++)for(var m=e[l]/i[f],p=0;p<i[f];p++){var h={};h[u]=f*d,h[c]=p*m,h[s]=d,h[l]=m,h.x+=e.x,h.y+=e.y,r.push(h)}}function Ie(e,t,r,a){return e*a-r*t}function Ce(e,t,r,a,n,o,i,s){var l=r-e,u=a-t,c=i-n,d=s-o,f=Ie(c,d,l,u);if(Math.abs(f)<1e-6)return null;var m=e-n,p=t-o,h=Ie(m,p,c,d)/f;return h<0||h>1?null:new ce["a"](h*l+e,h*u+t)}function we(e,t,r){var a=new ce["a"];ce["a"].sub(a,r,t),a.normalize();var n=new ce["a"];ce["a"].sub(n,e,t);var o=n.dot(a);return o}function je(e,t){var r=e[e.length-1];r&&r[0]===t[0]&&r[1]===t[1]||e.push(t)}function xe(e,t,r){for(var a=e.length,n=[],o=0;o<a;o++){var i=e[o],s=e[(o+1)%a],l=Ce(i[0],i[1],s[0],s[1],t.x,t.y,r.x,r.y);l&&n.push({projPt:we(l,t,r),pt:l,idx:o})}if(n.length<2)return[{points:e},{points:e}];n.sort((function(e,t){return e.projPt-t.projPt}));var u=n[0],c=n[n.length-1];if(c.idx<u.idx){var d=u;u=c,c=d}var f=[u.pt.x,u.pt.y],m=[c.pt.x,c.pt.y],p=[f],h=[m];for(o=u.idx+1;o<=c.idx;o++)je(p,e[o].slice());je(p,m),je(p,f);for(o=c.idx+1;o<=u.idx+a;o++)je(h,e[o%a].slice());return je(h,f),je(h,m),[{points:p},{points:h}]}function ke(e){var t=e.points,r=[],a=[];Object(le["d"])(t,r,a);var n=new ue["a"](r[0],r[1],a[0]-r[0],a[1]-r[1]),o=n.width,i=n.height,s=n.x,l=n.y,u=new ce["a"],c=new ce["a"];return o>i?(u.x=c.x=s+o/2,u.y=l,c.y=l+i):(u.y=c.y=l+i/2,u.x=s,c.x=s+o),xe(t,u,c)}function Ae(e,t,r,a){if(1===r)a.push(t);else{var n=Math.floor(r/2),o=e(t);Ae(e,o[0],n,a),Ae(e,o[1],r-n,a)}return a}function Ne(e,t){for(var r=[],a=0;a<t;a++)r.push(Object(ie["a"])(e));return r}function Oe(e,t){t.setStyle(e.style),t.z=e.z,t.z2=e.z2,t.zlevel=e.zlevel}function Ee(e){for(var t=[],r=0;r<e.length;)t.push([e[r++],e[r++]]);return t}function De(e,t){var r,a=[],n=e.shape;switch(e.type){case"rect":Pe(n,t,a),r=fe["a"];break;case"sector":Se(n,t,a),r=me["a"];break;case"circle":Se({r0:0,r:n.r,startAngle:0,endAngle:2*Math.PI,cx:n.cx,cy:n.cy},t,a),r=me["a"];break;default:var o=e.getComputedTransform(),i=o?Math.sqrt(Math.max(o[0]*o[0]+o[1]*o[1],o[2]*o[2]+o[3]*o[3])):1,s=Object(re["map"])(be(e.getUpdatedPathProxy(),i),(function(e){return Ee(e)})),l=s.length;if(0===l)Ae(ke,{points:s[0]},t,a);else if(l===t)for(var u=0;u<l;u++)a.push({points:s[u]});else{var c=0,d=Object(re["map"])(s,(function(e){var t=[],r=[];Object(le["d"])(e,t,r);var a=(r[1]-t[1])*(r[0]-t[0]);return c+=a,{poly:e,area:a}}));d.sort((function(e,t){return t.area-e.area}));var f=t;for(u=0;u<l;u++){var m=d[u];if(f<=0)break;var p=u===l-1?f:Math.ceil(m.area/c*t);p<0||(Ae(ke,{points:m.poly},p,a),f-=p)}}r=de["a"];break}if(!r)return Ne(e,t);var h=[];for(u=0;u<a.length;u++){var _=new r;_.setShape(a[u]),Oe(e,_),h.push(_)}return h}function Ge(e,t){var r=e.length,a=t.length;if(r===a)return[e,t];for(var n=[],o=[],i=r<a?e:t,s=Math.min(r,a),l=Math.abs(a-r)/6,u=(s-2)/6,c=Math.ceil(l/u)+1,d=[i[0],i[1]],f=l,m=2;m<s;){var p=i[m-2],h=i[m-1],_=i[m++],v=i[m++],g=i[m++],b=i[m++],y=i[m++],S=i[m++];if(f<=0)d.push(_,v,g,b,y,S);else{for(var P=Math.min(f,c-1)+1,I=1;I<=P;I++){var C=I/P;Object(ae["g"])(p,_,g,y,C,n),Object(ae["g"])(h,v,b,S,C,o),p=n[3],h=o[3],d.push(n[1],o[1],n[2],o[2],p,h),_=n[5],v=o[5],g=n[6],b=o[6]}f-=P-1}}return i===e?[d,t]:[e,d]}function qe(e,t){for(var r=e.length,a=e[r-2],n=e[r-1],o=[],i=0;i<t.length;)o[i++]=a,o[i++]=n;return o}function Te(e,t){for(var r,a,n,o=[],i=[],s=0;s<Math.max(e.length,t.length);s++){var l=e[s],u=t[s],c=void 0,d=void 0;l?u?(r=Ge(l,u),c=r[0],d=r[1],a=c,n=d):(d=qe(n||l,l),c=l):(c=qe(a||u,u),d=u),o.push(c),i.push(d)}return[o,i]}function $e(e){for(var t=0,r=0,a=0,n=e.length,o=0,i=n-2;o<n;i=o,o+=2){var s=e[i],l=e[i+1],u=e[o],c=e[o+1],d=s*c-u*l;t+=d,r+=(s+u)*d,a+=(l+c)*d}return 0===t?[e[0]||0,e[1]||0]:[r/t/3,a/t/3,t]}function Me(e,t,r,a){for(var n=(e.length-2)/6,o=1/0,i=0,s=e.length,l=s-2,u=0;u<n;u++){for(var c=6*u,d=0,f=0;f<s;f+=2){var m=0===f?c:(c+f-2)%l+2,p=e[m]-r[0],h=e[m+1]-r[1],_=t[f]-a[0],v=t[f+1]-a[1],g=_-p,b=v-h;d+=g*g+b*b}d<o&&(o=d,i=u)}return i}function Fe(e){for(var t=[],r=e.length,a=0;a<r;a+=2)t[a]=e[r-a-2],t[a+1]=e[r-a-1];return t}function Re(e,t,r,a){for(var n,o=[],i=0;i<e.length;i++){var s=e[i],l=t[i],u=$e(s),c=$e(l);null==n&&(n=u[2]<0!==c[2]<0);var d=[],f=[],m=0,p=1/0,h=[],_=s.length;n&&(s=Fe(s));for(var v=6*Me(s,l,u,c),g=_-2,b=0;b<g;b+=2){var y=(v+b)%g+2;d[b+2]=s[y]-u[0],d[b+3]=s[y+1]-u[1]}if(d[0]=s[v]-u[0],d[1]=s[v+1]-u[1],r>0)for(var S=a/r,P=-a/2;P<=a/2;P+=S){var I=Math.sin(P),C=Math.cos(P),w=0;for(b=0;b<s.length;b+=2){var j=d[b],x=d[b+1],k=l[b]-c[0],A=l[b+1]-c[1],N=k*C-A*I,O=k*I+A*C;h[b]=N,h[b+1]=O;var E=N-j,D=O-x;w+=E*E+D*D}if(w<p){p=w,m=P;for(var G=0;G<h.length;G++)f[G]=h[G]}}else for(var q=0;q<_;q+=2)f[q]=l[q]-c[0],f[q+1]=l[q+1]-c[1];o.push({from:d,to:f,fromCp:u,toCp:c,rotation:-m})}return o}function Ve(e){return e.__isCombineMorphing}var Le="__mOriginal_";function Ue(e,t,r){var a=Le+t,n=e[a]||e[t];e[a]||(e[a]=e[t]);var o=r.replace,i=r.after,s=r.before;e[t]=function(){var e,t=arguments;return s&&s.apply(this,t),e=o?o.apply(this,t):n.apply(this,t),i&&i.apply(this,t),e}}function Be(e,t){var r=Le+t;e[r]&&(e[t]=e[r],e[r]=null)}function ze(e,t){for(var r=0;r<e.length;r++)for(var a=e[r],n=0;n<a.length;){var o=a[n],i=a[n+1];a[n++]=t[0]*o+t[2]*i+t[4],a[n++]=t[1]*o+t[3]*i+t[5]}}function Qe(e,t){var r=e.getUpdatedPathProxy(),a=t.getUpdatedPathProxy(),n=Te(ve(r),ve(a)),o=n[0],i=n[1],s=e.getComputedTransform(),l=t.getComputedTransform();function u(){this.transform=null}s&&ze(o,s),l&&ze(i,l),Ue(t,"updateTransform",{replace:u}),t.transform=null;var c=Re(o,i,10,Math.PI),d=[];Ue(t,"buildPath",{replace:function(e){for(var r=t.__morphT,a=1-r,n=[],o=0;o<c.length;o++){var i=c[o],s=i.from,l=i.to,u=i.rotation*r,f=i.fromCp,m=i.toCp,p=Math.sin(u),h=Math.cos(u);Object(oe["lerp"])(n,f,m,r);for(var _=0;_<s.length;_+=2){var v=s[_],g=s[_+1],b=l[_],y=l[_+1],S=v*a+b*r,P=g*a+y*r;d[_]=S*h-P*p+n[0],d[_+1]=S*p+P*h+n[1]}var I=d[0],C=d[1];e.moveTo(I,C);for(_=2;_<s.length;){b=d[_++],y=d[_++];var w=d[_++],j=d[_++],x=d[_++],k=d[_++];I===b&&C===y&&w===x&&j===k?e.lineTo(x,k):e.bezierCurveTo(b,y,w,j,x,k),I=x,C=k}}}})}function Ye(e,t,r){if(!e||!t)return t;var a=r.done,n=r.during;function o(){Be(t,"buildPath"),Be(t,"updateTransform"),t.__morphT=-1,t.createPathProxy(),t.dirtyShape()}return Qe(e,t),t.__morphT=0,t.animateTo({__morphT:1},Object(re["defaults"])({during:function(e){t.dirtyShape(),n&&n(e)},done:function(){o(),a&&a()}},r)),t}function He(e,t,r,a,n,o){var i=16;e=n===r?0:Math.round(32767*(e-r)/(n-r)),t=o===a?0:Math.round(32767*(t-a)/(o-a));for(var s,l=0,u=(1<<i)/2;u>0;u/=2){var c=0,d=0;(e&u)>0&&(c=1),(t&u)>0&&(d=1),l+=u*u*(3*c^d),0===d&&(1===c&&(e=u-1-e,t=u-1-t),s=e,e=t,t=s)}return l}function Je(e){var t=1/0,r=1/0,a=-1/0,n=-1/0,o=Object(re["map"])(e,(function(e){var o=e.getBoundingRect(),i=e.getComputedTransform(),s=o.x+o.width/2+(i?i[4]:0),l=o.y+o.height/2+(i?i[5]:0);return t=Math.min(s,t),r=Math.min(l,r),a=Math.max(s,a),n=Math.max(l,n),[s,l]})),i=Object(re["map"])(o,(function(o,i){return{cp:o,z:He(o[0],o[1],t,r,a,n),path:e[i]}}));return i.sort((function(e,t){return e.z-t.z})).map((function(e){return e.path}))}function Ze(e){return De(e.path,e.count)}function We(){return{fromIndividuals:[],toIndividuals:[],count:0}}function Ke(e,t,r){var a=[];function n(e){for(var t=0;t<e.length;t++){var r=e[t];Ve(r)?n(r.childrenRef()):r instanceof ne["b"]&&a.push(r)}}n(e);var o=a.length;if(!o)return We();var i=r.dividePath||Ze,s=i({path:t,count:o});if(s.length!==o)return We();a=Je(a),s=Je(s);for(var l=r.done,u=r.during,c=r.individualDelay,d=new se["c"],f=0;f<o;f++){var m=a[f],p=s[f];p.parent=t,p.copyTransform(d),c||Qe(m,p)}function h(e){for(var t=0;t<s.length;t++)s[t].addSelfToZr(e)}function _(){t.__isCombineMorphing=!1,t.__morphT=-1,t.childrenRef=null,Be(t,"addSelfToZr"),Be(t,"removeSelfFromZr")}t.__isCombineMorphing=!0,t.childrenRef=function(){return s},Ue(t,"addSelfToZr",{after:function(e){h(e)}}),Ue(t,"removeSelfFromZr",{after:function(e){for(var t=0;t<s.length;t++)s[t].removeSelfFromZr(e)}});var v=s.length;if(c){var g=v,b=function(){g--,0===g&&(_(),l&&l())};for(f=0;f<v;f++){var y=c?Object(re["defaults"])({delay:(r.delay||0)+c(f,v,a[f],s[f]),done:b},r):r;Ye(a[f],s[f],y)}}else t.__morphT=0,t.animateTo({__morphT:1},Object(re["defaults"])({during:function(e){for(var r=0;r<v;r++){var a=s[r];a.__morphT=t.__morphT,a.dirtyShape()}u&&u(e)},done:function(){_();for(var t=0;t<e.length;t++)Be(e[t],"updateTransform");l&&l()}},r));return t.__zr&&h(t.__zr),{fromIndividuals:a,toIndividuals:s,count:v}}function Xe(e,t,r){var a=t.length,n=[],o=r.dividePath||Ze;function i(e){for(var t=0;t<e.length;t++){var r=e[t];Ve(r)?i(r.childrenRef()):r instanceof ne["b"]&&n.push(r)}}if(Ve(e)){i(e.childrenRef());var s=n.length;if(s<a)for(var l=0,u=s;u<a;u++)n.push(Object(ie["a"])(n[l++%s]));n.length=a}else{n=o({path:e,count:a});var c=e.getComputedTransform();for(u=0;u<n.length;u++)n[u].setLocalTransform(c);if(n.length!==a)return We()}n=Je(n),t=Je(t);var d=r.individualDelay;for(u=0;u<a;u++){var f=d?Object(re["defaults"])({delay:(r.delay||0)+d(u,a,n[u],t[u])},r):r;Ye(n[u],t[u],f)}return{fromIndividuals:n,toIndividuals:t,count:t.length}}var et=r("deca");function tt(e){return Object(re["isArray"])(e[0])}function rt(e,t){for(var r=[],a=e.length,n=0;n<a;n++)r.push({one:e[n],many:[]});for(n=0;n<t.length;n++){var o=t[n].length,i=void 0;for(i=0;i<o;i++)r[i%a].many.push(t[n][i])}var s=0;for(n=a-1;n>=0;n--)if(!r[n].many.length){var l=r[s].many;if(l.length<=1){if(!s)return r;s=0}o=l.length;var u=Math.ceil(o/2);r[n].many=l.slice(u,o),r[s].many=l.slice(0,u),s++}return r}var at={clone:function(e){for(var t=[],r=1-Math.pow(1-e.path.style.opacity,1/e.count),a=0;a<e.count;a++){var n=Object(ie["a"])(e.path);n.setStyle("opacity",r),t.push(n)}return t},split:null};function nt(e,t,r,a,n,o){if(e.length&&t.length){var i=Object(et["a"])("update",a,n);if(i&&i.duration>0){var s,l,u=a.getModel("universalTransition").get("delay"),c=Object.assign({setToFinal:!0},i);tt(e)&&(s=e,l=t),tt(t)&&(s=t,l=e);for(var d=s?s===e:e.length>t.length,f=s?rt(l,s):rt(d?t:e,[d?e:t]),m=0,p=0;p<f.length;p++)m+=f[p].many.length;var h=0;for(p=0;p<f.length;p++)_(f[p],d,h,m),h+=f[p].many.length}}function _(e,t,a,n,i){var s=e.many,l=e.one;if(1!==s.length||i)for(var d=Object(re["defaults"])({dividePath:at[r],individualDelay:u&&function(e,t,r,o){return u(e+a,n)}},c),f=t?Ke(s,l,d):Xe(l,s,d),m=f.fromIndividuals,p=f.toIndividuals,h=m.length,v=0;v<h;v++){y=u?Object(re["defaults"])({delay:u(v,h)},c):c;o(m[v],p[v],t?s[v]:e.one,t?e.one:s[v],y)}else{var g=t?s[0]:l,b=t?l:s[0];if(Ve(g))_({many:[g],one:b},!0,a,n,!0);else{var y=u?Object(re["defaults"])({delay:u(a,n)},c):c;Ye(g,b,y),o(g,b,g,b,y)}}}}function ot(e){if(!e)return[];if(Object(re["isArray"])(e)){for(var t=[],r=0;r<e.length;r++)t.push(ot(e[r]));return t}var a=[];return e.traverse((function(e){e instanceof ne["b"]&&!e.disableMorphing&&!e.invisible&&!e.ignore&&a.push(e)})),a}var it=r("80f0"),st=r("e0d3"),lt=(r("edae"),r("19ebf")),ut=1e4,ct=0,dt=1,ft=2,mt=Object(st["o"])();function pt(e,t){for(var r=e.dimensions,a=0;a<r.length;a++){var n=e.getDimensionInfo(r[a]);if(n&&0===n.otherDims[t])return r[a]}}function ht(e,t,r){var a=e.getDimensionInfo(r),n=a&&a.ordinalMeta;if(a){var o=e.get(a.name,t);return n&&n.categories[o]||o+""}}function _t(e,t,r,a){var n=a?"itemChildGroupId":"itemGroupId",o=pt(e,n);if(o){var i=ht(e,t,o);return i}var s=e.getRawDataItem(t),l=a?"childGroupId":"groupId";return s&&s[l]?s[l]+"":a?void 0:r||e.getId(t)}function vt(e){var t=[];return Object(re["each"])(e,(function(e){var r=e.data,a=e.dataGroupId;if(!(r.count()>ut))for(var n=r.getIndices(),o=0;o<n.length;o++)t.push({data:r,groupId:_t(r,o,a,!1),childGroupId:_t(r,o,a,!0),divide:e.divide,dataIndex:o})})),t}function gt(e,t,r){e.traverse((function(e){e instanceof ne["b"]&&Object(et["c"])(e,{style:{opacity:0}},t,{dataIndex:r,isFrom:!0})}))}function bt(e){if(e.parent){var t=e.getComputedTransform();e.setLocalTransform(t),e.parent.remove(e)}}function yt(e){e.stopAnimation(),e.isGroup&&e.traverse((function(e){e.stopAnimation()}))}function St(e,t,r){var a=Object(et["a"])("update",r,t);a&&e.traverse((function(e){if(e instanceof lt["c"]){var t=Object(et["b"])(e);t&&e.animateFrom({style:t},a)}}))}function Pt(e,t){var r=e.length;if(r!==t.length)return!1;for(var a=0;a<r;a++){var n=e[a],o=t[a];if(n.data.getId(n.dataIndex)!==o.data.getId(o.dataIndex))return!1}return!0}function It(e,t,r){var a=vt(e),n=vt(t);function o(e,t,r,a,n){(r||e)&&t.animateFrom({style:r&&r!==e?Object(re["extend"])(Object(re["extend"])({},r.style),e.style):e.style},n)}var i=!1,s=ct,l=Object(re["createHashMap"])(),u=Object(re["createHashMap"])();a.forEach((function(e){e.groupId&&l.set(e.groupId,!0),e.childGroupId&&u.set(e.childGroupId,!0)}));for(var c=0;c<n.length;c++){var d=n[c].groupId;if(u.get(d)){s=dt;break}var f=n[c].childGroupId;if(f&&l.get(f)){s=ft;break}}function m(e,t){return function(r){var a=r.data,n=r.dataIndex;return t?a.getId(n):e?s===dt?r.childGroupId:r.groupId:s===ft?r.childGroupId:r.groupId}}var p=Pt(a,n),h={};if(!p)for(c=0;c<n.length;c++){var _=n[c],v=_.data.getItemGraphicEl(_.dataIndex);v&&(h[v.id]=!0)}function g(e,t){var r=a[t],s=n[e],l=s.data.hostModel,u=r.data.getItemGraphicEl(r.dataIndex),c=s.data.getItemGraphicEl(s.dataIndex);u!==c?u&&h[u.id]||c&&(yt(c),u?(yt(u),bt(u),i=!0,nt(ot(u),ot(c),s.divide,l,e,o)):gt(c,l,e)):c&&St(c,s.dataIndex,l)}new it["a"](a,n,m(!0,p),m(!1,p),null,"multiple").update(g).updateManyToOne((function(e,t){var r=n[e],s=r.data,l=s.hostModel,u=s.getItemGraphicEl(r.dataIndex),c=Object(re["filter"])(Object(re["map"])(t,(function(e){return a[e].data.getItemGraphicEl(a[e].dataIndex)})),(function(e){return e&&e!==u&&!h[e.id]}));u&&(yt(u),c.length?(Object(re["each"])(c,(function(e){yt(e),bt(e)})),i=!0,nt(ot(c),ot(u),r.divide,l,e,o)):gt(u,l,r.dataIndex))})).updateOneToMany((function(e,t){var r=a[t],s=r.data.getItemGraphicEl(r.dataIndex);if(!s||!h[s.id]){var l=Object(re["filter"])(Object(re["map"])(e,(function(e){return n[e].data.getItemGraphicEl(n[e].dataIndex)})),(function(e){return e&&e!==s})),u=n[e[0]].data.hostModel;l.length&&(Object(re["each"])(l,(function(e){return yt(e)})),s?(yt(s),bt(s),i=!0,nt(ot(s),ot(l),r.divide,u,e[0],o)):Object(re["each"])(l,(function(t){return gt(t,u,e[0])})))}})).updateManyToMany((function(e,t){new it["a"](t,e,(function(e){return a[e].data.getId(a[e].dataIndex)}),(function(e){return n[e].data.getId(n[e].dataIndex)})).update((function(r,a){g(e[r],t[a])})).execute()})).execute(),i&&Object(re["each"])(t,(function(e){var t=e.data,a=t.hostModel,n=a&&r.getViewOfSeriesModel(a),o=Object(et["a"])("update",a,0);n&&a.isAnimationEnabled()&&o&&o.duration>0&&n.group.traverse((function(e){e instanceof ne["b"]&&!e.animators.length&&e.animateFrom({style:{opacity:0}},o)}))}))}function Ct(e){var t=e.getModel("universalTransition").get("seriesKey");return t||e.id}function wt(e){return Object(re["isArray"])(e)?e.sort().join(","):e}function jt(e){if(e.hostModel)return e.hostModel.getModel("universalTransition").get("divideShape")}function xt(e,t){var r=Object(re["createHashMap"])(),a=Object(re["createHashMap"])(),n=Object(re["createHashMap"])();return Object(re["each"])(e.oldSeries,(function(t,r){var o=e.oldDataGroupIds[r],i=e.oldData[r],s=Ct(t),l=wt(s);a.set(l,{dataGroupId:o,data:i}),Object(re["isArray"])(s)&&Object(re["each"])(s,(function(e){n.set(e,{key:l,dataGroupId:o,data:i})}))})),Object(re["each"])(t.updatedSeries,(function(e){if(e.isUniversalTransitionEnabled()&&e.isAnimationEnabled()){var t=e.get("dataGroupId"),o=e.getData(),i=Ct(e),s=wt(i),l=a.get(s);if(l)r.set(s,{oldSeries:[{dataGroupId:l.dataGroupId,divide:jt(l.data),data:l.data}],newSeries:[{dataGroupId:t,divide:jt(o),data:o}]});else if(Object(re["isArray"])(i)){0;var u=[];Object(re["each"])(i,(function(e){var t=a.get(e);t.data&&u.push({dataGroupId:t.dataGroupId,divide:jt(t.data),data:t.data})})),u.length&&r.set(s,{oldSeries:u,newSeries:[{dataGroupId:t,data:o,divide:jt(o)}]})}else{var c=n.get(i);if(c){var d=r.get(c.key);d||(d={oldSeries:[{dataGroupId:c.dataGroupId,data:c.data,divide:jt(c.data)}],newSeries:[]},r.set(c.key,d)),d.newSeries.push({dataGroupId:t,data:o,divide:jt(o)})}}}})),r}function kt(e,t){for(var r=0;r<e.length;r++){var a=null!=t.seriesIndex&&t.seriesIndex===e[r].seriesIndex||null!=t.seriesId&&t.seriesId===e[r].id;if(a)return r}}function At(e,t,r,a){var n=[],o=[];Object(re["each"])(Object(st["r"])(e.from),(function(e){var r=kt(t.oldSeries,e);r>=0&&n.push({dataGroupId:t.oldDataGroupIds[r],data:t.oldData[r],divide:jt(t.oldData[r]),groupIdDim:e.dimension})})),Object(re["each"])(Object(st["r"])(e.to),(function(e){var a=kt(r.updatedSeries,e);if(a>=0){var n=r.updatedSeries[a].getData();o.push({dataGroupId:t.oldDataGroupIds[a],data:n,divide:jt(n),groupIdDim:e.dimension})}})),n.length>0&&o.length>0&&It(n,o,a)}function Nt(e){e.registerUpdateLifecycle("series:beforeupdate",(function(e,t,r){Object(re["each"])(Object(st["r"])(r.seriesTransition),(function(e){Object(re["each"])(Object(st["r"])(e.to),(function(e){for(var t=r.updatedSeries,a=0;a<t.length;a++)(null!=e.seriesIndex&&e.seriesIndex===t[a].seriesIndex||null!=e.seriesId&&e.seriesId===t[a].id)&&(t[a][te["a"]]=!0)}))}))})),e.registerUpdateLifecycle("series:transition",(function(e,t,r){var a=mt(t);if(a.oldSeries&&r.updatedSeries&&r.optionChanged){var n=r.seriesTransition;if(n)Object(re["each"])(Object(st["r"])(n),(function(e){At(e,a,r,t)}));else{var o=xt(a,r);Object(re["each"])(o.keys(),(function(e){var r=o.get(e);It(r.oldSeries,r.newSeries,t)}))}Object(re["each"])(r.updatedSeries,(function(e){e[te["a"]]&&(e[te["a"]]=!1)}))}for(var i=e.getSeries(),s=a.oldSeries=[],l=a.oldDataGroupIds=[],u=a.oldData=[],c=0;c<i.length;c++){var d=i[c].getData();d.count()<ut&&(s.push(i[c]),l.push(i[c].get("dataGroupId")),u.push(d))}}))}var Ot=r("ee29");Object(a["a"])([o["a"]]),Object(a["a"])([i["a"]]),Object(a["a"])([s["a"],l["a"],u["a"],c["a"],d["a"],f["a"],m["a"],p["a"],h["a"],_["a"],v["a"],g["a"],b["a"],y["a"],S["a"],P["a"],I["a"],C["a"],w["a"],j["a"],x["a"],k["a"]]),Object(a["a"])(A["a"]),Object(a["a"])(N["a"]),Object(a["a"])(O["a"]),Object(a["a"])(E["a"]),Object(a["a"])(D["a"]),Object(a["a"])(G["a"]),Object(a["a"])(q["a"]),Object(a["a"])(T["a"]),Object(a["a"])($["a"]),Object(a["a"])(M["a"]),Object(a["a"])(F["a"]),Object(a["a"])(R["a"]),Object(a["a"])(V["a"]),Object(a["a"])(L["a"]),Object(a["a"])(U["a"]),Object(a["a"])(B["a"]),Object(a["a"])(z["a"]),Object(a["a"])(Q["a"]),Object(a["a"])(Y["a"]),Object(a["a"])(H["a"]),Object(a["a"])(J["a"]),Object(a["a"])(Z["a"]),Object(a["a"])(W["a"]),Object(a["a"])(K["a"]),Object(a["a"])(X["a"]),Object(a["a"])(ee["a"]),Object(a["a"])(Nt),Object(a["a"])(Ot["a"])},"3e64":function(e,t,r){"use strict";r.r(t);var a=r("e22d"),n=r("a06b");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,null,null);t["default"]=s.exports},"3f7ed":function(e,t,r){"use strict";r.r(t);var a=r("b323"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},"41c6":function(e,t,r){"use strict";r("2c6b")},"46b8":function(e,t,r){"use strict";r.r(t);var a=r("7bc9"),n=r("b729");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);r("7a21");var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,"76d94424",null);t["default"]=s.exports},"4b2a7":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[e.cmpt?r(e.cmpt,e._b({ref:"form",tag:"v-component",scopedSlots:e._u([{key:"attachment",fn:function(){return[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("el-input",{attrs:{type:"textarea",rows:3},on:{change:function(t){return e.formChange("Remark",t)}},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("el-upload",{staticClass:"upload-demo",attrs:{action:e.$store.state.uploadUrl,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-remove":e.beforeRemove,drag:"",multiple:"","auto-upload":!0,"file-list":e.fileList,"on-success":e.handleChange,accept:".png,.jpeg,.doc,.docx,.pdf,.xls,.xlsx"}},[r("svg-icon",{staticClass:"icon-svg",staticStyle:{width:"96px",height:"96px"},attrs:{name:"upload-icon","icon-class":"upload-icon"}}),r("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),r("em",[e._v("点击上传")]),r("div",{staticClass:"el-upload__tip"},[e._v(" 只能上传.png,.jpeg,.doc,.docx,.pdf,.xls,.xlsx文件 ")])])],1)],1)]},proxy:!0}],null,!1,**********)},"v-component",{projs:e.projs,providers:e.providers,channels:e.channels,cooperations:e.cooperations,invoices:e.invoices,rates:e.rates,seals:e.seals,entity:e.form},!1)):e._e(),e.flowContent?r("flow-node-set",{ref:"nodes",attrs:{flow:e.flowContent,fnodes:e.nodes},on:{openDrawer:e.openDrawer}}):e._e(),r("el-drawer",{attrs:{title:e.drawer.title,visible:e.drawerShow,direction:"rtl",modal:!1,"modal-append-to-body":!1},on:{"update:visible":function(t){e.drawerShow=t}}},[r("div",{staticStyle:{padding:"20px"}},[r("p",{staticStyle:{"font-size":"1.2em"}},[r("strong",[e._v("["+e._s(e.drawer.name)+"]")]),e._v("审批人 ")]),r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:function(t){return e.selectUserIdChange(e.drawer.nid,t)}},model:{value:e.selectUserId,callback:function(t){e.selectUserId=t},expression:"selectUserId"}},e._l(e.drawer.users,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)])],1)},n=[]},"4d85":function(e,t,r){"use strict";r.r(t);var a=r("c495"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},"594c":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"abs100 pd-16"},[r("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"full-h-flex"},[r("el-card",{staticStyle:{"flex-shrink":"0"},attrs:{shadow:"always"}},[r("div",{staticClass:"crd-head"},[r("div",{staticClass:"title"},[r("h2",[e._v("发起"+e._s(e.scheme.Scheme_Name))])]),r("div",{staticClass:"acts"},[r("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(t){return e.saveClickHandle(!0)}}},[e._v("发 起")]),r("el-button",{attrs:{size:"medium"},on:{click:function(t){return e.saveClickHandle(!1)}}},[e._v("暂存草稿")]),r("el-button",{attrs:{size:"medium"},on:{click:e.cancelClickHandle}},[e._v("取 消")])],1)])]),r("el-card",{staticClass:"auto-h",staticStyle:{"margin-top":"16px"},attrs:{shadow:"always"}},[r("div",{staticClass:"full-h-flex"},[r("div",{staticClass:"flex-scroll-box-v"},[r("div",{staticStyle:{padding:"0px 36px"}},[r("GeneralFlowForm",e._b({ref:"flow-form",attrs:{type:e.webFromId},on:{selectUserIdChange:e.selectUserIdChange,updateNodes:function(t){return e.type=t},tagBack:e.tagBack}},"GeneralFlowForm",{projs:e.projs,providers:e.providers,channels:e.channels,cooperations:e.cooperations,invoices:e.invoices,rates:e.rates,seals:e.seals,scheme:e.scheme,nodes:e.type},!1))],1)])])])],1)])},n=[]},"5a2d":function(e,t,r){"use strict";r.r(t);var a=r("222a"),n=r("4d85");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,null,null);t["default"]=s.exports},"5fb5":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"108px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属项目",required:"",prop:"Project_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择所属项目",filterable:""},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projs,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Project_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"发包内容",required:"",prop:"Contract_Award"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Award,callback:function(t){e.$set(e.form,"Contract_Award",t)},expression:"form.Contract_Award"}}):r("span",[e._v(e._s(e.form.Contract_Award))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同编号",required:"",prop:"Contract_Code"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Code,callback:function(t){e.$set(e.form,"Contract_Code",t)},expression:"form.Contract_Code"}}):r("span",[e._v(e._s(e.form.Contract_Code))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同名称",required:"",prop:"Contract_Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Name,callback:function(t){e.$set(e.form,"Contract_Name",t)},expression:"form.Contract_Name"}}):r("span",[e._v(e._s(e.form.Contract_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"承揽单位",required:"",prop:"Provider_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选承揽单位",filterable:""},model:{value:e.form.Provider_Id,callback:function(t){e.$set(e.form,"Provider_Id",t)},expression:"form.Provider_Id"}},e._l(e.providers,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Supplier_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同价格",required:"",prop:"Price"}},[e.isEdit?r("el-input-number",{attrs:{min:0},model:{value:e.form.Price,callback:function(t){e.$set(e.form,"Price",e._n(t))},expression:"form.Price"}}):r("span",[e._v(e._s(e.form.Price))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合作形式",required:"",prop:"Project_Category"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选合作形式"},model:{value:e.form.Project_Category,callback:function(t){e.$set(e.form,"Project_Category",t)},expression:"form.Project_Category"}},e._l(e.cooperations,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Project_Category_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"增值税税率",required:"",prop:"VAT_Rate"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选税率"},model:{value:e.form.VAT_Rate,callback:function(t){e.$set(e.form,"VAT_Rate",t)},expression:"form.VAT_Rate"}},e._l(e.rates,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.VAT_Rate_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"结算方式",required:"",prop:"Settlement_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Settlement_Style,callback:function(t){e.$set(e.form,"Settlement_Style",t)},expression:"form.Settlement_Style"}}):r("span",[e._v(e._s(e.form.Settlement_Style))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"开票形式",required:"",prop:"Invoicing_Style"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选开票形式"},model:{value:e.form.Invoicing_Style,callback:function(t){e.$set(e.form,"Invoicing_Style",t)},expression:"form.Invoicing_Style"}},e._l(e.invoices,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Invoicing_Style_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"付款方式",required:"",prop:"Pay_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Pay_Style,callback:function(t){e.$set(e.form,"Pay_Style",t)},expression:"form.Pay_Style"}}):r("span",[e._v(e._s(e.form.Pay_Style))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},n=[]},"698b":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("fieldset",{staticStyle:{"margin-top":"28px"}},[e._m(0),r("div",{staticClass:"block",staticStyle:{padding:"16px"}},[r("div",{staticStyle:{width:"100%",height:"375px",border:"1px dotted #D2D2D2","margin-bottom":"0px"}},[e._e(),r("FlowArea",{attrs:{"browser-type":3,"current-tool":{type:"drag",icon:"drag",name:"拖拽"},"flow-data":e.flowData,"is-drag":!1,"is-show-content":!1,"select-group":e.currentSelectGroup,select:e.currentSelect,plumb:e.plumb},on:{"update:selectGroup":function(t){e.currentSelectGroup=t},"update:select-group":function(t){e.currentSelectGroup=t},"update:select":function(t){e.currentSelect=t}}})],1),e._e()],1)])},n=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("legend",[r("strong",{staticStyle:{padding:"6px","font-size":"1.2em"}},[e._v("审批节点设置")]),r("span",{staticStyle:{"font-size":".85em"}},[e._v("(点击节点选择审批人)")])])}]},"6e67":function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteProject=l,t.GetEntity=s,t.GetProjectList=o,t.SaveProject=i,t.UpdateProjectStatus=u;var n=a(r("b775"));function o(e){return(0,n.default)({url:"/EPC/Project/GetProjectList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/EPC/Project/SaveProject",method:"post",data:e})}function s(e){return(0,n.default)({url:"/EPC/Project/GetEntity",method:"post",data:e})}function l(e){return(0,n.default)({url:"/EPC/Project/DeleteProject",method:"post",data:e})}function u(e){return(0,n.default)({url:"/EPC/Project/UpdateProjectStatus",method:"post",data:e})}},"7a21":function(e,t,r){"use strict";r("9aa7")},"7bc9":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"108px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"申请部门",required:"",prop:"Department_Id"}},[e.isEdit?r("el-popover",{attrs:{placement:"bottom-start",width:"450",trigger:"click"}},[r("div",{staticStyle:{height:"450px","overflow-y":"auto"}},[e.departments?r("el-tree",{ref:"tree",attrs:{props:e.treeProps,"show-checkbox":!1,"node-key":"Id",data:e.departments,"expand-on-click-node":!1},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,n=t.data;return r("span",{staticClass:"custom-tree-node"},[n.Is_Directory?r("span",[e._v(e._s(a.label))]):r("el-radio",{attrs:{label:n.Id},model:{value:e.form.Department_Id,callback:function(t){e.$set(e.form,"Department_Id",t)},expression:"form.Department_Id"}},[e._v(e._s(a.label))])],1)}}],null,!1,3806635514)}):e._e()],1),r("div",{ref:"fakeInput",staticClass:"fake-input",attrs:{slot:"reference"},slot:"reference"},[r("div",{staticClass:"inner-div"},[r("div",{staticClass:"tags"},[e.form.Department_Id?r("el-tag",{staticClass:"tag",attrs:{closable:"",type:"info"},on:{close:e.tagClose}},[e._v(" "+e._s(e.getDptName(e.form.Department_Id))+" ")]):e._e()],1),r("div",{staticClass:"append"},[r("i",{staticClass:"el-icon-arrow-down"})])])])]):r("span",[e._v(e._s(e.form.Department_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"申请人",required:"",prop:"Apply_UserId"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择申请人",filterable:""},model:{value:e.form.Apply_UserId,callback:function(t){e.$set(e.form,"Apply_UserId",t)},expression:"form.Apply_UserId"}},e._l(e.users,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Apply_UserName))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"印鉴名称",required:"",prop:"Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}}):r("span",[e._v(e._s(e.form.Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"类别",required:"",prop:"Category"}},[e.isEdit?r("el-input",{model:{value:e.form.Category,callback:function(t){e.$set(e.form,"Category",t)},expression:"form.Category"}}):r("span",[e._v(e._s(e.form.Category))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"选择印章",required:"",prop:"Type"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选印章"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},e._l(e.seals,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Type))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},n=[]},"833e":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"108px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属项目",required:"",prop:"Project_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择所属项目",filterable:""},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projs,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Project_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"发包内容",required:"",prop:"Contract_Award"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Award,callback:function(t){e.$set(e.form,"Contract_Award",t)},expression:"form.Contract_Award"}}):r("span",[e._v(e._s(e.form.Contract_Award))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同编号",required:"",prop:"Contract_Code"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Code,callback:function(t){e.$set(e.form,"Contract_Code",t)},expression:"form.Contract_Code"}}):r("span",[e._v(e._s(e.form.Contract_Code))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同名称",required:"",prop:"Contract_Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Name,callback:function(t){e.$set(e.form,"Contract_Name",t)},expression:"form.Contract_Name"}}):r("span",[e._v(e._s(e.form.Contract_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"代签单位",required:"",prop:"Signed_Provider_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选代签单位",filterable:""},model:{value:e.form.Signed_Provider_Id,callback:function(t){e.$set(e.form,"Signed_Provider_Id",t)},expression:"form.Signed_Provider_Id"}},e._l(e.providers,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Signed_Provider_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"委托单位",required:"",prop:"Entrust_Provider_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选委托单位",filterable:""},model:{value:e.form.Entrust_Provider_Id,callback:function(t){e.$set(e.form,"Entrust_Provider_Id",t)},expression:"form.Entrust_Provider_Id"}},e._l(e.providers,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Entrust_Provider_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"承包单位",required:"",prop:"Contract_Provider_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选承包单位",filterable:""},model:{value:e.form.Contract_Provider_Id,callback:function(t){e.$set(e.form,"Contract_Provider_Id",t)},expression:"form.Contract_Provider_Id"}},e._l(e.providers,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Contract_Provider_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"承包范围",required:"",prop:"Contract_Scope"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Scope,callback:function(t){e.$set(e.form,"Contract_Scope",t)},expression:"form.Contract_Scope"}}):r("span",[e._v(e._s(e.form.Contract_Scope))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同价格",required:"",prop:"Price"}},[e.isEdit?r("el-input-number",{attrs:{min:0},model:{value:e.form.Price,callback:function(t){e.$set(e.form,"Price",e._n(t))},expression:"form.Price"}}):r("span",[e._v(e._s(e.form.Price))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"税票缴纳形式",required:"",prop:"Tax_Receipt_Pay_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Tax_Receipt_Pay_Style,callback:function(t){e.$set(e.form,"Tax_Receipt_Pay_Style",t)},expression:"form.Tax_Receipt_Pay_Style"}}):r("span",[e._v(e._s(e.form.Tax_Receipt_Pay_Style))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"增值税税率",required:"",prop:"VAT_Rate"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选税率"},model:{value:e.form.VAT_Rate,callback:function(t){e.$set(e.form,"VAT_Rate",t)},expression:"form.VAT_Rate"}},e._l(e.rates,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.VAT_Rate_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"结算方式",required:"",prop:"Settlement_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Settlement_Style,callback:function(t){e.$set(e.form,"Settlement_Style",t)},expression:"form.Settlement_Style"}}):r("span",[e._v(e._s(e.form.Settlement_Style))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"开票形式",required:"",prop:"Invoicing_Style"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选开票形式"},model:{value:e.form.Invoicing_Style,callback:function(t){e.$set(e.form,"Invoicing_Style",t)},expression:"form.Invoicing_Style"}},e._l(e.invoices,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Invoicing_Style_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"付款方式",required:"",prop:"Pay_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Pay_Style,callback:function(t){e.$set(e.form,"Pay_Style",t)},expression:"form.Pay_Style"}}):r("span",[e._v(e._s(e.form.Pay_Style))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},n=[]},9182:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("5530"));t.default={name:"PriceFlow",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{Project_Id:"",Contract_Award:"",Contract_Code:"",Contract_Name:"",Provider_Id:"",Price:"",Project_Category:"",VAT_Rate:"",Settlement_Style:"",Invoicing_Style:"",Pay_Style:"",Remark:"",Attachments:[],Project_Name:"",Supplier_Name:"",Project_Category_Name:"",Invoicing_Style_Name:"",VAT_Rate_Name:""},rules:{Project_Id:[{required:!0,message:"请选择所属项目",trigger:"blur"}],Contract_Award:[{required:!0,message:"请输入发包内容",trigger:"blur"}],Contract_Code:[{required:!0,message:"请输入合同编号",trigger:"blur"}],Contract_Name:[{required:!0,message:"请输入合同名称",trigger:"blur"}],Provider_Id:[{required:!0,message:"请选择承揽单位",trigger:"blur"}],Project_Category:[{required:!0,message:"请选择合作形式",trigger:"blur"}],Price:[{required:!0,message:"请输入合同价格",trigger:"blur"}],Tax_Receipt_Pay_Style:[{required:!0,message:"请输入税票缴纳形式",trigger:"blur"}],VAT_Rate:[{required:!0,message:"请选择增值税税率",trigger:"blur"}],Settlement_Style:[{required:!0,message:"请输入结算方式",trigger:"blur"}],Invoicing_Style:[{required:!0,message:"请选择开票形式",trigger:"blur"}],Pay_Style:[{required:!0,message:"请输入付款方式",trigger:"blur"}],Remark:[{required:!0,message:"请填写说明",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]}}},watch:{entity:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.entity)},datas:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.datas)}},created:function(){this.form=(0,n.default)((0,n.default)((0,n.default)({},this.form),this.entity),this.datas)},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,n.default)({},this.form);return t.Project_Name=this.projs.find((function(t){return t.Id===e.form.Project_Id}))?this.projs.find((function(t){return t.Id===e.form.Project_Id})).Name:"",t.Supplier_Name=this.providers.find((function(t){return t.Id===e.form.Provider_Id}))?this.providers.find((function(t){return t.Id===e.form.Provider_Id})).Name:"",t.Project_Category_Name=this.cooperations.find((function(t){return t.Value===e.form.Project_Category}))?this.cooperations.find((function(t){return t.Value===e.form.Project_Category})).Display_Name:"",t.Invoicing_Style_Name=this.invoices.find((function(t){return t.Value===e.form.Invoicing_Style}))?this.invoices.find((function(t){return t.Value===e.form.Invoicing_Style})).Display_Name:"",t.VAT_Rate_Name=this.rates.find((function(t){return t.Value===e.form.VAT_Rate}))?this.rates.find((function(t){return t.Value===e.form.VAT_Rate})).Display_Name:"",t},attachOpen:function(e){window.open(e,"_blank")}}}},9283:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(r("2909")),o=a(r("5530"));r("4de4"),r("7db0"),r("d81d"),r("14d9"),r("b0c0"),r("e9f5"),r("910d"),r("f665"),r("ab43"),r("b64b"),r("d3b7"),r("3ca3"),r("ddb0");var i=a(r("159c")),s=a(r("3e64")),l=a(r("b8ed")),u=a(r("0ccb7")),c=a(r("5a2d")),d=a(r("46b8")),f=a(r("0775")),m=r("eb4a");t.default={name:"GeneralFlowForm",components:{FlowNodeSet:i.default,GeneralContract:s.default,AllographContract:l.default,PriceFlow:u.default,ProcurementFlow:c.default,SealFlow:d.default,TechSolution:f.default},props:{type:{type:String,default:""},projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},scheme:{type:Object,default:function(){return{}}},nodes:{type:Array,default:function(){return[]}}},data:function(){return{form:{Attachments:[],Remark:""},drawer:{nid:"",title:"",users:[]},selectUserId:"",fileList:[],flowContent:null,drawerShow:!1}},computed:{cmpt:function(){var e;return null!==(e=this.$options["components"][this.$route.params.type])&&void 0!==e?e:null},id:function(){return this.$route.params.id},apiAddProcess:function(){return"GeneralContract"===this.type?m.AddConventionalContractProcess:"AllographContract"===this.type?m.AddOtherSignedContractProcess:"PriceFlow"===this.type?m.AddContractPriceProcess:"ProcurementFlow"===this.type?m.AddSpecialPurchaseProcess:"SealFlow"===this.type?m.AddSealProcess:"TechSolution"===this.type?m.AddTechSolutionProcess:null},apiGetEntity:function(){return m.FlowInstancesGet}},watch:{form:function(e){},scheme:function(){try{this.flowContent=JSON.parse(this.scheme.Scheme_Content)}catch(e){}}},created:function(){this.id&&this.getEntity(this.id)},methods:{getEntity:function(e){var t=this;return this.apiGetEntity({id:e}).then((function(e){if(e.IsSucceed)try{var r=JSON.parse(e.Data.Frm_Data);t.form=(0,o.default)({},r),t.$emit("updateNodes",r.Nodes),t.fileList=r.Attachments.map((function(e){return{url:e,name:e.split("/")[e.split("/").length-1]}})),t.form.Content_Overview&&(t.form.Remark=t.form.Content_Overview)}catch(a){}else t.$message.warning(e.Message)}))},openDrawer:function(e){var t=this.nodes.find((function(t){return t.Id===e.id}));this.drawerShow=!0,this.drawer.title="设置节点",this.drawer.name=e.name,t&&(this.drawer.users=t.Users,this.selectUserId=t.User_Ids,this.drawer.nid=t.Id)},formChange:function(e,t){this.$refs.form&&(this.$refs.form.form[e]=t)},handlePreview:function(e){e.url?window.open(e.url,"_blank"):e.response&&e.response.Data&&window.open(e.response.Data.split("*")[0],"_blank")},handleRemove:function(e,t){var r=e.url?e.url:e.response&&e.response.Data?e.response.Data.split("*")[0]:"";this.form.Attachments=this.form.Attachments.filter((function(e){return e!==r})),this.formChange("Attachments",(0,n.default)(this.form.Attachments))},beforeRemove:function(e,t){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleChange:function(e,t,r){this.form.Attachments.push(t.response.Data.split("*")[0]),this.formChange("Attachments",(0,n.default)(this.form.Attachments))},selectUserIdChange:function(e,t){this.$emit("selectUserIdChange",e,t)},submit:function(e){var t,r,a,n,i,s,l=this,u=null!==(t=null===(r=this.$refs["form"])||void 0===r?void 0:r.getData())&&void 0!==t?t:{},c=null!==(a=null===(n=this.$refs["nodes"])||void 0===n?void 0:n.getData())&&void 0!==a?a:[],d=(0,o.default)((0,o.default)({webfromId:this.type},u),{},{Nodes:c,IsApprove:e});this.$refs["form"]&&this.$refs["nodes"]?Promise.all([null===(i=this.$refs["form"])||void 0===i?void 0:i.validate(),null===(s=this.$refs["nodes"])||void 0===s?void 0:s.validate()]).then((function(t){l.apiAddProcess(d).then((function(t){t.IsSucceed?e&&l.$emit("tagBack"):l.$message.warning(t.Message)})).catch((function(e){l.$message.warning(e)}))})).catch((function(e){l.$message.warning("表单验证失败，检查是否遗漏必填项或者未设置审批人")})):this.$message.warning("流程表单异常")}}}},"97ff":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("14d9"),r("fb6a");t.default={methods:{tagBack:function(){var e=this,t=this.$route;this.$store.dispatch("tagsView/delView",t).then((function(r){var a=r.visitedViews;if(t.path===e.$route.path){var n=a.slice(-1)[0];e.$router.push(n.fullPath)}}))}}}},"99d0":function(e,t,r){},"9aa7":function(e,t,r){},"9ea2":function(e,t,r){"use strict";r.r(t);var a=r("ecb8"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},a06b:function(e,t,r){"use strict";r.r(t);var a=r("f966"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},a0bb:function(e,t,r){},a518:function(e,t,r){"use strict";r.r(t);var a=r("2e74"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},aa8a:function(e,t,r){"use strict";r.r(t);var a=r("4b2a7"),n=r("d67e");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);r("41c6");var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,"7f190d49",null);t["default"]=s.exports},ad3a:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"108px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"项目名称",required:"",prop:"Project_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择项目",filterable:"",disabled:Boolean(e.$store.state.user.CurReferenceId)},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projs,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Project_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"方案类别",required:"",prop:"Scheme_Type"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选方案类别"},model:{value:e.form.Scheme_Type,callback:function(t){e.$set(e.form,"Scheme_Type",t)},expression:"form.Scheme_Type"}},e._l(e.Types,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):r("span",[e._v(e._s(e.form.Scheme_Type))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"方案名称",required:"",prop:"Scheme_Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Scheme_Name,callback:function(t){e.$set(e.form,"Scheme_Name",t)},expression:"form.Scheme_Name"}}):r("span",[e._v(e._s(e.form.Scheme_Name))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},n=[]},b306:function(e,t,r){"use strict";r.r(t);var a=r("9182"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},b323:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("97ff")),o=r("6e67"),i=r("1180"),s=r("6186"),l=a(r("aa8a")),u=r("eb4a");t.default={name:"GeneralFlowCreate",components:{GeneralFlowForm:l.default},mixins:[n.default],data:function(){return{loading:!1,type:[],scheme:{},projs:[],providers:[],channels:[],cooperations:[],invoices:[],rates:[],seals:[]}},computed:{webFromId:function(){return this.$route.params.type||""},id:function(){return this.$route.params.id}},created:function(){this.getFormModel(),this.getFlowScheme(this.webFromId),this.getProjectList(),this.getProviderList(),this.getDicts()},methods:{selectUserIdChange:function(e,t){var r=this.type.find((function(t){return t.Id===e}));r&&(r.User_Ids=t)},getFormModel:function(){var e=this;return(0,u.GetFlowSchemeNodeByFromId)({webfromId:this.webFromId,applyType:"1"}).then((function(t){t.IsSucceed&&(e.type=t.Data)}))},getFlowScheme:function(e){var t=this;return(0,u.GetFlowSchemeByFromId)({webfromId:e,applyType:"1"}).then((function(e){if(e.IsSucceed)try{t.scheme=e.Data}catch(r){}else t.$message.warning(e.Message)}))},getProjectList:function(){var e=this;(0,o.GetProjectList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.projs=t.Data.Data)}))},getProviderList:function(){var e=this;(0,i.GetProviderList)({excludeIsQualifiedStatus:!1}).then((function(t){t.IsSucceed&&(e.providers=t.Data)}))},getDicts:function(){var e=this;(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"SubcontractorChannel"}).then((function(t){t.IsSucceed&&(e.channels=t.Data)})),(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"Cooperate_Style"}).then((function(t){t.IsSucceed&&(e.cooperations=t.Data)})),(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"Invoice_Type"}).then((function(t){t.IsSucceed&&(e.invoices=t.Data)})),(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"Invoice_Rate"}).then((function(t){t.IsSucceed&&(e.rates=t.Data)})),(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"Seal_Type"}).then((function(t){t.IsSucceed&&(e.seals=t.Data)}))},saveClickHandle:function(e){var t,r=this;this.loading=!0,null===(t=this.$refs["flow-form"])||void 0===t||t.submit(e),setTimeout((function(){r.loading=!1}),600)},cancelClickHandle:function(){this.tagBack()}}}},b729:function(e,t,r){"use strict";r.r(t);var a=r("dac80"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},b8ed:function(e,t,r){"use strict";r.r(t);var a=r("833e"),n=r("9ea2");for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);var i=r("2877"),s=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,null,null);t["default"]=s.exports},c495:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("5530"));t.default={name:"ProcurementFlow",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{Project_Id:"",Schedule:"",Content:"",Project_Category:"",Contract_Code:"",Contract_Name:"",Provider_Id:"",Price:"",Given_Style:"",Remark:"",Attachments:[],Projece_Name:"",Supplier_Name:"",Project_Category_Name:""},rules:{Project_Id:[{required:!0,message:"请选择所属项目",trigger:"blur"}],Schedule:[{required:!0,message:"请输入工期",trigger:"blur"}],Content:[{required:!0,message:"请输入特定内容",trigger:"blur"}],Project_Category:[{required:!0,message:"请选择合作形式",trigger:"blur"}],Contract_Code:[{required:!0,message:"请输入合同编号",trigger:"blur"}],Contract_Name:[{required:!0,message:"请输入合同名称",trigger:"blur"}],Provider_Id:[{required:!0,message:"请选择承揽单位",trigger:"blur"}],Price:[{required:!0,message:"请输入合同价格",trigger:"blur"}],Given_Style:[{required:!0,message:"请输入特定采购形式",trigger:"blur"}],Remark:[{required:!0,message:"请填写说明",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]}}},watch:{entity:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.entity)},datas:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.datas)}},created:function(){this.form=(0,n.default)((0,n.default)((0,n.default)({},this.form),this.entity),this.datas)},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,n.default)({},this.form);return t.Project_Name=this.projs.find((function(t){return t.Id===e.form.Project_Id}))?this.projs.find((function(t){return t.Id===e.form.Project_Id})).Name:"",t.Supplier_Name=this.providers.find((function(t){return t.Id===e.form.Provider_Id}))?this.providers.find((function(t){return t.Id===e.form.Provider_Id})).Name:"",t.Project_Category_Name=this.cooperations.find((function(t){return t.Value===e.form.Project_Category}))?this.cooperations.find((function(t){return t.Value===e.form.Project_Category})).Display_Name:"",t},attachOpen:function(e){window.open(e,"_blank")}}}},d67e:function(e,t,r){"use strict";r.r(t);var a=r("9283"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},dac80:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("5530")),o=r("6186");t.default={name:"SealFlow",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{treeProps:{label:"Label",children:"Children"},departments:null,users:[],form:{Department_Id:"",Apply_UserId:"",Name:"",Category:"",Type:"",Remark:"",Attachments:[],Apply_UserName:"",Department_Name:""},rules:{Department_Id:[{required:!0,message:"请选择部门",trigger:"blur"}],Apply_UserId:[{required:!0,message:"请选择申请人",trigger:"blur"}],Name:[{required:!0,message:"请输入印鉴名称",trigger:"blur"}],Category:[{required:!0,message:"请输入类别",trigger:"blur"}],Type:[{required:!0,message:"请选择印章",trigger:"blur"}],Remark:[{required:!0,message:"请填写用印说明",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]}}},watch:{"form.Department_Id":function(e,t){this.form.Nodes||(this.form.Apply_UserId=""),delete this.form["Nodes"],e?this.getDeptUsers():this.users=[]},entity:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.entity)},datas:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.datas)}},created:function(){var e=this;this.form=(0,n.default)((0,n.default)((0,n.default)({},this.form),this.entity),this.datas),(0,o.GetDepartmentTree)({}).then((function(t){t.IsSucceed&&(e.departments=t.Data,e.form.Department_Id&&e.getDeptUsers())}))},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,n.default)({},this.form);return t.Apply_UserName=this.users.find((function(t){return t.Id===e.form.Apply_UserId}))?this.users.find((function(t){return t.Id===e.form.Apply_UserId})).Display_Name:"",t.Department_Name=this.getDptName(this.form.Department_Id),t},getDptName:function(e){return this.$refs.tree?this.$refs.tree.getNode(e).label:""},getDeptUsers:function(){var e=this;(0,o.GetUserList)({DepartmentId:this.form.Department_Id}).then((function(t){t.IsSucceed&&(e.users=t.Data)}))},tagClose:function(){this.form.Department_Id=""},attachOpen:function(e){window.open(e,"_blank")}}}},de2d:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("5530"));t.default={name:"TechSolution",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{Scheme_Type:"",Project_Id:"",Scheme_Name:"",Content_Overview:"",Attachments:[],Remark:""},rules:{Project_Id:[{required:!0,message:"请选择项目",trigger:"blur"}],Scheme_Type:[{required:!0,message:"请选择方案类别",trigger:"blur"}],Scheme_Name:[{required:!0,message:"请输入方案名称",trigger:"blur"}],Content_Overview:[{required:!0,message:"请填写方案概述",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]},Types:[{label:"A类",value:"A类"},{label:"B类",value:"B类"}]}},watch:{entity:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.entity),this.form.Remark=this.form.Content_Overview},datas:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.datas),this.form.Remark=this.form.Content_Overview}},created:function(){this.form=(0,n.default)((0,n.default)((0,n.default)({},this.form),this.entity),this.datas),this.form.Remark=this.form.Content_Overview,this.isEdit&&!this.form.Project_Id&&(this.form.Project_Id=this.$store.state.user.CurReferenceId)},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,n.default)({},this.form);return t.Project_Name=this.projs.find((function(t){return t.Id===e.form.Project_Id}))?this.projs.find((function(t){return t.Id===e.form.Project_Id})).Name:"",t.Content_Overview=t.Remark,t},attachOpen:function(e){window.open(e,"_blank")}}}},e22d:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"110px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属项目",required:"",prop:"Project_Id"}},[e.isEdit?r("el-select",{attrs:{filterable:"",placeholder:"请选择所属项目"},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projs,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Project_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"发包内容",required:"",prop:"Contract_Award"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Award,callback:function(t){e.$set(e.form,"Contract_Award",t)},expression:"form.Contract_Award"}}):r("span",[e._v(e._s(e.form.Contract_Award))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同编号",required:"",prop:"Contract_Code"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Code,callback:function(t){e.$set(e.form,"Contract_Code",t)},expression:"form.Contract_Code"}}):r("span",[e._v(e._s(e.form.Contract_Code))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同名称",required:"",prop:"Contract_Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Name,callback:function(t){e.$set(e.form,"Contract_Name",t)},expression:"form.Contract_Name"}}):r("span",[e._v(e._s(e.form.Contract_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"承揽单位",required:"",prop:"Provider_Id"}},[e.isEdit?r("el-select",{attrs:{filterable:"",placeholder:"请选承揽单位"},model:{value:e.form.Provider_Id,callback:function(t){e.$set(e.form,"Provider_Id",t)},expression:"form.Provider_Id"}},e._l(e.providers,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Supplier_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同价格",required:"",prop:"Price"}},[e.isEdit?r("el-input-number",{attrs:{min:0},model:{value:e.form.Price,callback:function(t){e.$set(e.form,"Price",e._n(t))},expression:"form.Price"}}):r("span",[e._v(e._s(e.form.Price))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合作形式",required:"",prop:"Project_Category"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选合作形式"},model:{value:e.form.Project_Category,callback:function(t){e.$set(e.form,"Project_Category",t)},expression:"form.Project_Category"}},e._l(e.cooperations,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Project_Category_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"分包单位选择",required:"",prop:"Subcontractor_Source"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.form.Subcontractor_Source,callback:function(t){e.$set(e.form,"Subcontractor_Source",t)},expression:"form.Subcontractor_Source"}},e._l(e.channels,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Subcontractor_Source_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"增值税税率",required:"",prop:"VAT_Rate"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选税率"},model:{value:e.form.VAT_Rate,callback:function(t){e.$set(e.form,"VAT_Rate",t)},expression:"form.VAT_Rate"}},e._l(e.rates,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.VAT_Rate_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"结算方式",required:"",prop:"Settlement_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Settlement_Style,callback:function(t){e.$set(e.form,"Settlement_Style",t)},expression:"form.Settlement_Style"}}):r("span",[e._v(e._s(e.form.Settlement_Style))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"开票形式",required:"",prop:"Invoicing_Style"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选开票形式"},model:{value:e.form.Invoicing_Style,callback:function(t){e.$set(e.form,"Invoicing_Style",t)},expression:"form.Invoicing_Style"}},e._l(e.invoices,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Invoicing_Style_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"付款方式",required:"",prop:"Pay_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Pay_Style,callback:function(t){e.$set(e.form,"Pay_Style",t)},expression:"form.Pay_Style"}}):r("span",[e._v(e._s(e.form.Pay_Style))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},n=[]},e543:function(e,t,r){"use strict";r("1b93")},eb4a:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddContractPriceProcess=I,t.AddConventionalContractProcess=S,t.AddOtherSignedContractProcess=P,t.AddSealProcess=w,t.AddSpecialPurchaseProcess=C,t.AddTechSolutionProcess=j,t.CheckBusinessVerification=y,t.CheckExistFlow=f,t.FlowInstancesCancel=c,t.FlowInstancesDelete=h,t.FlowInstancesGet=_,t.FlowInstancesLoad=m,t.FlowSchemesAdd=s,t.FlowSchemesDelete=u,t.FlowSchemesGet=i,t.FlowSchemesLoad=d,t.FlowSchemesUpdate=l,t.GetContractPriceEntity=O,t.GetConventionalContractEntity=A,t.GetFileInfo=$,t.GetFlowSchemeByFromId=k,t.GetFlowSchemeNodeByFromId=x,t.GetLeavePageList=b,t.GetListEntitiesProject=T,t.GetOneEntitiesProject=q,t.GetOtherSignedContractEntity=N,t.GetSchemeObjectIds=M,t.GetSealEntity=D,t.GetSpecialPurchaseEntity=E,t.QueryHistories=g,t.SaveBusinessData=p,t.SaveDesignFlow=G,t.Verification=v;var n=a(r("b775")),o=a(r("4328"));function i(e){return(0,n.default)({method:"get",url:"/SYS/FlowSchemes/Get",params:e})}function s(e){return(0,n.default)({method:"post",url:"/SYS/FlowSchemes/Add",data:e})}function l(e){return(0,n.default)({method:"post",url:"/SYS/FlowSchemes/Update",data:e})}function u(e){return(0,n.default)({method:"post",url:"/SYS/FlowSchemes/Delete",data:e})}function c(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/CancelFlow",data:e})}function d(e){return(0,n.default)({method:"post",url:"/SYS/FlowSchemes/Load",data:e})}function f(e){return(0,n.default)({method:"post",url:"/SYS/FlowSchemes/CheckExistFlow",data:e})}function m(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/Load",data:e})}function p(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/SaveBusinessData",data:e})}function h(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/Delete",data:e})}function _(e){return(0,n.default)({method:"get",url:"/SYS/FlowInstances/Get",params:e})}function v(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/Verification",data:e})}function g(e){return(0,n.default)({method:"get",url:"/SYS/FlowInstances/QueryHistories",params:e})}function b(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/GetLeavePageList",data:o.default.stringify(e)})}function y(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/CheckBusinessVerification",data:o.default.stringify(e)})}function S(e){return(0,n.default)({method:"post",url:"/EPC/ConventionalContract/AddProcess",data:e})}function P(e){return(0,n.default)({method:"post",url:"/EPC/OtherSignedContract/AddProcess",data:e})}function I(e){return(0,n.default)({method:"post",url:"/EPC/ContractPrice/AddProcess",data:e})}function C(e){return(0,n.default)({method:"post",url:"/EPC/SpecialPurchase/AddProcess",data:e})}function w(e){return(0,n.default)({method:"post",url:"/EPC/Seal/AddProcess",data:e})}function j(e){return(0,n.default)({method:"post",url:"/EPC/TechnicalScheme/AddProcess",data:e})}function x(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/GetFlowSchemeNodeByFromId",data:e})}function k(e){return(0,n.default)({method:"post",url:"/SYS/FlowInstances/GetFlowSchemeByFromId",data:e})}function A(e){return(0,n.default)({method:"post",url:"/EPC/ConventionalContract​/GetEntity",data:e})}function N(e){return(0,n.default)({method:"post",url:"/EPC/OtherSignedContract/GetEntity",data:e})}function O(e){return(0,n.default)({method:"post",url:"/EPC/ContractPrice/GetEntity",data:e})}function E(e){return(0,n.default)({method:"post",url:"/EPC/SpecialPurchase/GetEntity",data:e})}function D(e){return(0,n.default)({method:"post",url:"/EPC/Seal/GetEntity",data:e})}function G(e){return(0,n.default)({method:"post",url:"/SYS/Sys_File/SaveDesignFlow",data:e})}function q(e){return(0,n.default)({method:"post",url:"/Sys/Sys_FileType/GetOneEntitiesProject",data:e})}function T(e){return(0,n.default)({method:"post",url:"/Sys/Sys_FileType/GetListEntitiesProject",data:e})}function $(e){return(0,n.default)({method:"post",url:"/Sys/Sys_FileType/GetEntity",data:e})}function M(e){return(0,n.default)({method:"post",url:"/Sys/FlowSchemes/GetSchemeObjectIds",data:e})}},ecb8:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("5530"));t.default={name:"AllographContract",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{Project_Id:"",Contract_Award:"",Contract_Code:"",Contract_Name:"",Signed_Provider_Id:"",Entrust_Provider_Id:"",Contract_Provider_Id:"",Contract_Scope:"",Price:"",Tax_Receipt_Pay_Style:"",VAT_Rate:"",Settlement_Style:"",Invoicing_Style:"",Pay_Style:"",Remark:"",Attachments:[],Project_Name:"",Signed_Provider_Name:"",Entrust_Provider_Name:"",Contract_Provider_Name:"",Invoicing_Style_Name:"",VAT_Rate_Name:""},rules:{Project_Id:[{required:!0,message:"请选择所属项目",trigger:"blur"}],Contract_Award:[{required:!0,message:"请输入发包内容",trigger:"blur"}],Contract_Code:[{required:!0,message:"请输入合同编号",trigger:"blur"}],Contract_Name:[{required:!0,message:"请输入合同名称",trigger:"blur"}],Signed_Provider_Id:[{required:!0,message:"请选择代签单位",trigger:"blur"}],Entrust_Provider_Id:[{required:!0,message:"请选择委托单位",trigger:"blur"}],Contract_Provider_Id:[{required:!0,message:"请选择承包单位",trigger:"blur"}],Contract_Scope:[{required:!0,message:"请选择承包范围",trigger:"blur"}],Price:[{required:!0,message:"请输入合同价格",trigger:"blur"}],Tax_Receipt_Pay_Style:[{required:!0,message:"请输入税票缴纳形式",trigger:"blur"}],VAT_Rate:[{required:!0,message:"请选择增值税税率",trigger:"blur"}],Settlement_Style:[{required:!0,message:"请输入结算方式",trigger:"blur"}],Invoicing_Style:[{required:!0,message:"请选择开票形式",trigger:"blur"}],Pay_Style:[{required:!0,message:"请输入付款方式",trigger:"blur"}],Remark:[{required:!0,message:"请填写说明",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]}}},watch:{entity:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.entity)},datas:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.datas)}},created:function(){this.form=(0,n.default)((0,n.default)((0,n.default)({},this.form),this.entity),this.datas)},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,n.default)({},this.form);return t.Project_Name=this.projs.find((function(t){return t.Id===e.form.Project_Id}))?this.projs.find((function(t){return t.Id===e.form.Project_Id})).Name:"",t.Signed_Provider_Name=this.providers.find((function(t){return t.Id===e.form.Signed_Provider_Id}))?this.providers.find((function(t){return t.Id===e.form.Signed_Provider_Id})).Name:"",t.Entrust_Provider_Name=this.providers.find((function(t){return t.Id===e.form.Entrust_Provider_Id}))?this.providers.find((function(t){return t.Id===e.form.Entrust_Provider_Id})).Name:"",t.Contract_Provider_Name=this.providers.find((function(t){return t.Id===e.form.Contract_Provider_Id}))?this.providers.find((function(t){return t.Id===e.form.Contract_Provider_Id})).Name:"",t.Invoicing_Style_Name=this.invoices.find((function(t){return t.Value===e.form.Invoicing_Style}))?this.invoices.find((function(t){return t.Value===e.form.Invoicing_Style})).Display_Name:"",t.VAT_Rate_Name=this.rates.find((function(t){return t.Value===e.form.VAT_Rate}))?this.rates.find((function(t){return t.Value===e.form.VAT_Rate})).Display_Name:"",t},attachOpen:function(e){window.open(e,"_blank")}}}},f966:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var n=a(r("5530"));t.default={name:"GeneralContract",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{Project_Id:"",Contract_Award:"",Contract_Code:"",Contract_Name:"",Provider_Id:"",Price:"",Project_Category:"",Subcontractor_Source:"",VAT_Rate:"",Settlement_Style:"",Invoicing_Style:"",Pay_Style:"",Remark:"",Attachments:[],Project_Name:"",Supplier_Name:"",Project_Category_Name:"",Subcontractor_Source_Name:"",Invoicing_Style_Name:"",VAT_Rate_Name:""},rules:{Project_Id:[{required:!0,message:"请选择所属项目",trigger:"blur"}],Contract_Award:[{required:!0,message:"请输入发包内容",trigger:"blur"}],Contract_Code:[{required:!0,message:"请输入合同编号",trigger:"blur"}],Contract_Name:[{required:!0,message:"请输入合同名称",trigger:"blur"}],Provider_Id:[{required:!0,message:"请选择承揽单位",trigger:"blur"}],Price:[{required:!0,message:"请输入合同价格",trigger:"blur"}],Project_Category:[{required:!0,message:"请选择合作形式",trigger:"blur"}],Subcontractor_Source:[{required:!0,message:"请选择分包单位",trigger:"blur"}],VAT_Rate:[{required:!0,message:"请选择增值税税率",trigger:"blur"}],Settlement_Style:[{required:!0,message:"请输入结算方式",trigger:"blur"}],Invoicing_Style:[{required:!0,message:"请选择开票形式",trigger:"blur"}],Pay_Style:[{required:!0,message:"请输入付款方式",trigger:"blur"}],Remark:[{required:!0,message:"请填写说明",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]}}},watch:{entity:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.entity)},datas:function(e){this.form=(0,n.default)((0,n.default)({},this.form),this.datas)}},created:function(){this.form=(0,n.default)((0,n.default)((0,n.default)({},this.form),this.entity),this.datas)},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,n.default)({},this.form);return t.Project_Name=this.projs.find((function(t){return t.Id===e.form.Project_Id}))?this.projs.find((function(t){return t.Id===e.form.Project_Id})).Name:"",t.Supplier_Name=this.providers.find((function(t){return t.Id===e.form.Provider_Id}))?this.providers.find((function(t){return t.Id===e.form.Provider_Id})).Name:"",t.Project_Category_Name=this.cooperations.find((function(t){return t.Value===e.form.Project_Category}))?this.cooperations.find((function(t){return t.Value===e.form.Project_Category})).Display_Name:"",t.Subcontractor_Source_Name=this.channels.find((function(t){return t.Value===e.form.Subcontractor_Source}))?this.channels.find((function(t){return t.Value===e.form.Subcontractor_Source})).Display_Name:"",t.Invoicing_Style_Name=this.invoices.find((function(t){return t.Value===e.form.Invoicing_Style}))?this.invoices.find((function(t){return t.Value===e.form.Invoicing_Style})).Display_Name:"",t.VAT_Rate_Name=this.rates.find((function(t){return t.Value===e.form.VAT_Rate}))?this.rates.find((function(t){return t.Value===e.form.VAT_Rate})).Display_Name:"",t},attachOpen:function(e){window.open(e,"_blank")}}}}}]);