(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-254f2670"],{"0349":function(e,t,a){"use strict";a.r(t);var n=a("a87b"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"0732":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"采购单号:",prop:"PurchaseCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.PurchaseCode,callback:function(t){e.$set(e.form,"PurchaseCode","string"===typeof t?t.trim():t)},expression:"form.PurchaseCode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"原料分类:",prop:"Type"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"原料名称:",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name","string"===typeof t?t.trim():t)},expression:"form.Name"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"供应商:",prop:"VendorName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.VendorName,callback:function(t){e.$set(e.form,"VendorName","string"===typeof t?t.trim():t)},expression:"form.VendorName"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["IsExpire"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("el-tag",{attrs:{type:n.IsExpire?"success":"danger"}},[e._v(e._s(n.IsExpire?"是":"否"))])]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("displayValue")(n[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},"0afb":function(e,t,a){"use strict";a("5552")},"0da6":function(e,t,a){"use strict";a.r(t);var n=a("beac"),i=a("9a4d");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"f6768e28",null);t["default"]=l.exports},"13a16":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card h100"},[a("h3",[e._v("入库单信息")]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"入库单号",prop:"ReceiptNumber"}},[e._v(" "+e._s(e.form.ReceiptNumber)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"入库类型",prop:"EntryType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView,placeholder:"请选择"},on:{change:e.typeChange},model:{value:e.form.EntryType,callback:function(t){e.$set(e.form,"EntryType",t)},expression:"form.EntryType"}},[a("el-option",{attrs:{label:"手动入库",value:0}}),a("el-option",{attrs:{label:"退料入库",value:1}}),a("el-option",{attrs:{label:"采购入库",value:2}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"入库日期",prop:"EntryDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{readonly:e.isView,"value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.EntryDate,callback:function(t){e.$set(e.form,"EntryDate",t)},expression:"form.EntryDate"}})],1)],1),0===e.form.EntryType?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"ProjectId"}},[a("el-select",{attrs:{placeholder:"所属项目",clearable:"",filterable:"",disabled:e.isView},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.projectOptions,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"Remarks"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{readonly:e.isView,autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:100,type:"textarea"},model:{value:e.form.Remarks,callback:function(t){e.$set(e.form,"Remarks",t)},expression:"form.Remarks"}})],1)],1)],1)],1),a("el-divider",{staticClass:"elDivder"}),a("h4",[e._v("入库明细")]),e.isView?e._e():a("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),a("el-button",{attrs:{type:"danger",disabled:!e.multipleSelection.length},on:{click:e.handleDelete}},[e._v("删除")]),e.isPurchase?e._e():a("el-button",{on:{click:e.handleImport}},[e._v("导入")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleWarehouse(!1)}}},[e._v("批量选择仓库/库位")])]},proxy:!0}],null,!1,3984637662)}),a("div",{staticClass:"tb-x"},[a(e.currentTbComponent,{ref:"table",tag:"component",attrs:{"is-view":e.isView},on:{changeStandard:e.changeStandard,changeWarehouse:e.changeWarehouse,changeRawName:e.openAddDialog,select:e.setSelectRow}})],1),e.isView?e._e():a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("footer",[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("div",[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{loading:e.saveLoading},on:{click:function(t){return e.saveDraft(0)}}},[e._v("保存草稿 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("提交入库")])],1)])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":0},on:{close:e.handleClose,warehouse:e.getWarehouse,importData:e.importData,standard:e.getStandard,refresh:e.fetchData}})],1):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.isPurchase?"新增采购原料":"选择原料",visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[e.isPurchase?a("add-purchase-list",{ref:"draft",attrs:{"is-single":e.isSingle},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}}):a("add-list",{ref:"draft",attrs:{"is-single":e.isSingle},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2)],1)},i=[]},"1caa":function(e,t,a){"use strict";a.r(t);var n=a("0732"),i=a("0349");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("bd48");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"2d692948",null);t["default"]=l.exports},"248d":function(e,t,a){},"24ee":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[2===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"非标规格",name:"1"}},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},e._l(e.list,(function(t){return a("el-form-item",{key:t,attrs:{label:t}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:e.form[t],callback:function(a){e.$set(e.form,t,a)},expression:"form[item]"}})],1)})),1)],1):e._e(),1===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"标准规格",name:"2"}},[a("el-table",{ref:"multipleTable",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(a){return a.stopPropagation(),function(a){return e.handleRadioChange(a,t.row)}(a)}},model:{value:e.radioSelect,callback:function(t){e.radioSelect=t},expression:"radioSelect"}})]}}],null,!1,3152109164)}),a("el-table-column",{attrs:{align:"center",prop:"StandardDesc",label:"规格"}})],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},i=[]},"39ea":function(e,t,a){"use strict";a("ff0c")},"52b92":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var i=n(a("2909")),r=n(a("c14f")),o=n(a("1da1")),l=a("9002"),s=a("cf45"),c=n(a("6612"));t.default={props:{isView:{type:Boolean,default:!1}},data:function(){return{tbLoading:!1,multipleSelection:[],textureOption:[],columns:[],tbData:[],texture:{}}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTexture();case 1:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PRORawReceiptAddList1");case 1:e.columns=t.v;case 2:return t.a(2)}}),t)})))()},methods:{tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleChange:function(e){this.$emit("changeRawName",e)},handleStandard:function(e){this.$emit("changeStandard",e)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,a=e.map((function(e){return e.Count=1,e}));(t=this.tbData).push.apply(t,(0,i.default)(a))},importData:function(e){var t,a=this;e.forEach((function(e,t){e.Name=e.RawName,a.countChange({value:e.Count},e)})),(t=this.tbData).push.apply(t,(0,i.default)(e))},countChange:function(e,t){var a=e.value;t.PerWtg&&(t.TotalPerWtg=(0,c.default)(a*t.PerWtg).format("0.[00]")),t.PerLen&&(t.TotalPerLen=(0,c.default)(a*t.PerLen).format("0.[00]"))},getTextureType:function(e){var t=this.textureOption.find((function(t){return t.Id===e}));return t&&t.Display_Name},lenChange:function(e,t){var a=e.value;t.PerLen&&(t.TotalPerLen=a*t.Count)},wtgChange:function(e,t){var a=e.value;t.PerWtg&&(t.TotalPerWtg=a*t.Count)},getTexture:function(){var e=this;(0,s.getDictionary)("Texture").then((function(t){e.textureOption=t,e.textureOption.forEach((function(t,a){e.$set(e.texture,t.Value,t.Display_Name)}))}))}}}},5552:function(e,t,a){},"577f":function(e,t,a){"use strict";a.r(t);var n=a("13a16"),i=a("9fec");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("39ea");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"7cd943fe",null);t["default"]=l.exports},"643c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],a("el-col",{attrs:{span:2}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(0===n[t.Code]?"按需使用":1===n[t.Code]?"使用标准规格":2===n[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("displayValue")(n[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},"78c2":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("5530")),r=n(a("c14f")),o=n(a("1da1"));a("99af"),a("7db0"),a("c740"),a("caad"),a("d81d"),a("fb6a"),a("a434"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("ac1f"),a("841c"),a("159b");var l=a("ed08"),s=n(a("0da6")),c=n(a("ca50")),u=n(a("c0ce")),d=n(a("1caa")),f=n(a("2de0")),h=n(a("2ae8")),m=n(a("79ed3")),p=a("2245"),v=n(a("6612")),g=a("3166");t.default={components:{RawManualTb:s.default,AddPurchaseList:d.default,PurchaseTb:c.default,ImportFile:f.default,Warehouse:h.default,Standard:m.default,AddList:u.default},props:{pageType:{type:Number,default:void 0}},data:function(){return{tbLoading:!1,projectOptions:[],multipleSelection:[],form:{ReceiptNumber:"",Remarks:"",EntryType:0,ProjectId:"",EntryDate:this.getDate()},currentComponent:"",title:"",dWidth:"60%",isSingle:!1,saveLoading:!1,search:function(){return{}},openAddList:!1,dialogVisible:!1,rules:{EntryType:[{required:!0,message:"请选择",trigger:"change"}],EntryDate:[{required:!0,message:"请选择日期",trigger:"change"}]}}},computed:{isView:function(){return 3===this.pageType},isAdd:function(){return 1===this.pageType},isEdit:function(){return 2===this.pageType},isManual:function(){return 0===this.form.EntryType},isPurchase:function(){return 2===this.form.EntryType},currentTbComponent:function(e){var t=e.isPurchase;return t?c.default:s.default}},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.search=(0,l.debounce)(e.fetchData,800,!0),e.getProject(),e.isAdd||e.getInfo();case 1:return t.a(2)}}),t)})))()},methods:{fetchData:function(){},getInfo:function(){var e=this;(0,p.GetRawWarehouseReceiptEntity)({id:this.$route.params.id}).then((function(t){if(t.IsSucceed){var a=t.Data,n=a.EntryDate,i=a.EntryType,r=a.RawRcptDetail,o=a.ReceiptNumber,l=a.ProjectId,s=a.Remarks;e.form.EntryDate=new Date(n),e.form.EntryType=i,e.form.ReceiptNumber=o,e.form.Remarks=s,e.form.ProjectId=l,e.form.Id=e.$route.params.id,e.$nextTick((function(t){e.$refs["table"].tbData=r.map((function(e){return e.Name=e.RawName,e.TotalPerWtg=(0,v.default)(e.Count*e.PerWtg).format("0.[00]"),e.TotalPerLen=(0,v.default)(e.Count*e.PerLen).format("0.[00]"),e}))}))}else e.$message({message:t.Message,type:"error"})}))},getProject:function(){var e=this;(0,g.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.projectOptions=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},changeWarehouse:function(e){this.currentRow=e,this.handleWarehouse(!0)},getWarehouse:function(e){var t=this,a=e.warehouse,n=e.location;this.currentRow?(this.currentRow.WarehouseId=a.Id,this.$set(this.currentRow,"WarehouseName",a.Display_Name),this.$set(this.currentRow,"LocationName",n.Display_Name),this.currentRow.LocationId=n.Id):this.multipleSelection.forEach((function(e,i){t.$set(e,"WarehouseName",a.Display_Name),t.$set(e,"LocationName",n.Display_Name),e.LocationId=n.Id,e.WarehouseId=a.Id}))},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,a=e.val;1===t?this.$set(this.currentRow,"StandardDesc",a):(this.$set(this.currentRow,"StandardDesc",a.StandardDesc),this.currentRow.StandardId=a.StandardId)},typeChange:function(e){0!==e&&(this.form.ProjectId="")},handleReset:function(){this.$refs["form"].resetFields(),this.search(1)},getAddList:function(e){this.$refs["table"].addData(e)},importData:function(e){this.$refs["table"].importData(e)},getRowName:function(e){var t=e.Name,a=e.Id;this.currentRow.Name=t,this.currentRow.RawId=a,this.currentRow.StandardDesc=""},handleWarehouse:function(e){this.currentComponent="Warehouse",this.dWidth="40%",this.title="批量选择仓库/库位",!e&&(this.currentRow=null),this.dialogVisible=!0},handleImport:function(){this.currentComponent="ImportFile",this.dWidth="40%",this.title="原料导入",this.dialogVisible=!0},handleDelete:function(e){var t=this;this.multipleSelection.forEach((function(e,a){var n=t.$refs.table.tbData,i=n.findIndex((function(t){return t.RawId===e.RawId}));n.splice(i,1)})),this.multipleSelection=[]},handleClose:function(e){this.openAddList=!1,this.dialogVisible=!1},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},checkValidate:function(){var e=(0,l.deepClone)(this.$refs["table"].tbData);if(!e.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var t=this.checkTb(e),a=t.status,n=t.msg;return a?{data:e,status:!0}:(this.$message({message:"".concat(n||"必填字段","不能为空"),type:"warning"}),{status:!1})},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,a=this.checkValidate(),n=a.data,r=a.status;r&&this.$refs["form"].validate((function(a){if(!a)return!1;e.saveLoading=!0,(0,p.SaveRawWarehouseReceipt)((0,i.default)((0,i.default)({},e.form),{},{Status:0===t?0:1,RawRcptDetails:n})).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"}),e.saveLoading=!1}))}))},checkTb:function(e){var t=this,a=[];a=this.isPurchase?["Count","LocationId"]:["Name","MaterialType","StandardDesc","PerLen","PerWtg","Count","LocationId"];for(var n=0;n<e.length;n++){for(var i,r=e[n],o=function(){var e=a[l];if(["",null,void 0].includes(r[e])){var n=t.$refs.table.columns,i=n.find((function(t){return t.Code===e}));return{v:{status:!1,msg:null===i||void 0===i?void 0:i.Display_Name}}}},l=0;l<a.length;l++)if(i=o(),i)return i.v;delete r._X_ROW_KEY,delete r.WarehouseName,delete r.LocationName}return{status:!0,msg:""}},openAddDialog:function(e){var t=this;this.currentRow=e,this.openAddList=!0,e?(this.isSingle=!0,this.$nextTick((function(a){t.$refs["draft"].setRow(e)}))):this.isSingle=!1},closeView:function(){(0,l.closeTagView)(this.$store,this.$route)},setSelectRow:function(e){this.multipleSelection=e},getDate:function(){var e=new Date,t=e.getFullYear(),a=("0"+(e.getMonth()+1)).slice(-2),n=("0"+e.getDate()).slice(-2);return"".concat(t,"-").concat(a,"-").concat(n)},handleSubmit:function(e){var t=this;this.$confirm("确认提交入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.saveDraft(1)})).catch((function(){t.$message({type:"info",message:"已取消"})}))}}}},"79ed3":function(e,t,a){"use strict";a.r(t);var n=a("24ee"),i=a("c270");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("0afb");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"10bbff52",null);t["default"]=l.exports},"7eca":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("2532"),a("c7cd"),a("159b");var i=n(a("2909")),r=n(a("c14f")),o=n(a("1da1")),l=a("9002"),s=a("cf45"),c=n(a("6612"));t.default={props:{isView:{type:Boolean,default:!1}},data:function(){return{tbLoading:!1,multipleSelection:[],textureOption:[],columns:[],tbData:[],texture:{}}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTexture();case 1:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a,n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PRORawPurchaseAddList");case 1:a=t.v,n=["FurnaceBatchNo","LocationId","Remarks","Count","NotReceivedCount"],e.columns=a.map((function(e){return n.includes(e.Code)&&(e.fixed="right"),e}));case 2:return t.a(2)}}),t)})))()},methods:{tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,a=this,n=e.map((function(e){return e.Count=1,a.countChange({value:1},e),e}));(t=this.tbData).push.apply(t,(0,i.default)(n))},countChange:function(e,t){var a=e.value;t.PerWtg&&(t.TotalPerWtg=(0,c.default)(a*t.PerWtg).format("0.[00]")),t.PerLen&&(t.TotalPerLen=(0,c.default)(a*t.PerLen).format("0.[00]"))},getTextureType:function(e){var t=this.textureOption.find((function(t){return t.Id===e}));return t&&t.Display_Name},getTexture:function(){var e=this;(0,s.getDictionary)("Texture").then((function(t){e.textureOption=t,e.textureOption.forEach((function(t,a){e.$set(e.texture,t.Value,t.Display_Name)}))}))}}}},"9a4d":function(e,t,a){"use strict";a.r(t);var n=a("52b92"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"9fec":function(e,t,a){"use strict";a.r(t);var n=a("78c2"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},a87b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("b64b"),a("d3b7"),a("25f0"),a("2532"),a("159b");var i=n(a("c14f")),r=n(a("1da1")),o=a("9002"),l=a("2245");t.default={data:function(){return{form:{PurchaseCode:"",Type:"",Name:"",VendorName:""},selectRow:null,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Label"}},columns:[],fTable:[],tbConfig:{},multipleSelection:[]}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getList(),this.getConfig()},methods:{getConfig:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,o.getTableConfig)("PROAddRawPurchase");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},getList:function(){var e=this;(0,l.GetMaterialCategoryList)({Type:0}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelect.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){var e=this,t=Object.keys(this.form);this.fTable=this.originalData.filter((function(a){var n=!0;return t.forEach((function(t,i){(a[t]||"").toString().includes(e.form[t])||(n=!1)})),n}))},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetPurchaseDetailList)({Type:1}).then((function(t){t.IsSucceed?(e.fTable=t.Data,e.originalData=t.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},b6225:function(e,t,a){"use strict";a.r(t);var n=a("7eca"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},ba9f:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),r=n(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("841c");var o=a("ed08"),l=a("9002"),s=a("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,s.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},bd48:function(e,t,a){"use strict";a("248d")},beac:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Name"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.Name)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleChange(n)}}})]}},{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.Name))])]}}],null,!0)}):"MaterialType"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(e.texture[n.MaterialType]))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-select",{attrs:{filterable:"",transfer:""},model:{value:n.MaterialType,callback:function(t){e.$set(n,"MaterialType",t)},expression:"row.MaterialType"}},e._l(e.textureOption,(function(e){return a("vxe-option",{key:e.Id,attrs:{value:e.Value,label:e.Display_Name}})})),1)]}}],null,!0)}):"StandardDesc"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[a("el-tag",{attrs:{type:"info"}},[e._v(e._s(n.Code)+" ")]),e._v(" "+e._s(n.StandardDesc))],1)]}},{key:"edit",fn:function(t){var n=t.row;return[a("el-tag",{attrs:{type:"info"}},[e._v(e._s(n.Code)+" ")]),e._v(" "+e._s(n.StandardDesc)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleStandard(n)}}})]}}],null,!0)}):"PerLen"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.PerLen))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.lenChange(t,n)}},model:{value:n.PerLen,callback:function(t){e.$set(n,"PerLen",e._n(t))},expression:"row.PerLen"}})]}}],null,!0)}):"PerWtg"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.PerWtg))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.wtgChange(t,n)}},model:{value:n.PerWtg,callback:function(t){e.$set(n,"PerWtg",e._n(t))},expression:"row.PerWtg"}})]}}],null,!0)}):"RealWtg"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.RealWtg))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{min:0,type:"number"},model:{value:n.RealWtg,callback:function(t){e.$set(n,"RealWtg",e._n(t))},expression:"row.RealWtg"}})]}}],null,!0)}):"Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.Count))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countChange(t,n)}},model:{value:n.Count,callback:function(t){e.$set(n,"Count",e._n(t))},expression:"row.Count"}})]}}],null,!0)}):"LocationId"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.WarehouseName)+"/"+e._s(n.LocationName)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(n)}}})]}},{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.WarehouseName)+"/"+e._s(a.LocationName)+" ")]}}],null,!0)}):"Brand"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.Brand))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"text"},model:{value:n.Brand,callback:function(t){e.$set(n,"Brand",t)},expression:"row.Brand"}})]}}],null,!0)}):"FurnaceBatchNo"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.FurnaceBatchNo))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"text"},model:{value:n.FurnaceBatchNo,callback:function(t){e.$set(n,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})]}}],null,!0)}):"Remarks"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.Remarks))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"text"},model:{value:n.Remarks,callback:function(t){e.$set(n,"Remarks",t)},expression:"row.Remarks"}})]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)},i=[]},c0ce:function(e,t,a){"use strict";a.r(t);var n=a("643c"),i=a("c67f");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("d54b");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"7e99cf87",null);t["default"]=l.exports},c270:function(e,t,a){"use strict";a.r(t);var n=a("ebcf"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},c67f:function(e,t,a){"use strict";a.r(t);var n=a("ba9f"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},ca50:function(e,t,a){"use strict";a.r(t);var n=a("e3a1"),i=a("b6225");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"0af96d8a",null);t["default"]=l.exports},d54b:function(e,t,a){"use strict";a("f3afb")},e3a1:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.Count))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countChange(t,n)}},model:{value:n.Count,callback:function(t){e.$set(n,"Count",e._n(t))},expression:"row.Count"}})]}}],null,!0)}):"LocationId"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction:"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.WarehouseName)+"/"+e._s(n.LocationName)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(n)}}})]}},{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.WarehouseName)+"/"+e._s(a.LocationName)+" ")]}}],null,!0)}):"FurnaceBatchNo"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction:"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.FurnaceBatchNo))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"text"},model:{value:n.FurnaceBatchNo,callback:function(t){e.$set(n,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})]}}],null,!0)}):"Remarks"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction:"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.Remarks))])]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"text"},model:{value:n.Remarks,callback:function(t){e.$set(n,"Remarks",t)},expression:"row.Remarks"}})]}}],null,!0)}):"StandardDesc"===t.Code?a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-tag",{attrs:{type:"info"}},[e._v(e._s(n.Code))]),e._v(e._s(n.StandardDesc)+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)},i=[]},ebcf:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b"),a("14d9"),a("13d5"),a("e9f5"),a("9485"),a("d3b7");var n=a("2245");t.default={data:function(){return{activeName:"1",radioSelect:null,btnLoading:!1,form:{},list:[],tableData:[],specificationUsage:0}},watch:{specificationUsage:function(e,t){1===e&&(this.activeName="2")}},methods:{getOption:function(e){var t=this;this.specificationUsage=e.SpecificationUsage,e&&(this.list=e.RcptRawParams.split("*")),(0,n.GetRawStandardsList)({rawId:e.RawId}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleRadioChange:function(e,t){e.stopPropagation(),this.currentRow=Object.assign({},t)},submit:function(){var e=this;if("1"===this.activeName){var t=!0,a=this.list.reduce((function(a,n){if(e.form[n])return a.push(e.form[n]),a;t=!1}),[]);if(!t)return void this.$message({message:"输入数据不能为0",type:"warning"});this.$emit("standard",{type:1,val:a.join("*")})}else{if(!this.currentRow)return void this.$message({message:"请选择规格",type:"warning"});this.$emit("standard",{type:2,val:this.currentRow})}this.handleClose()},handleClose:function(){this.$emit("close")}}}},f3afb:function(e,t,a){},ff0c:function(e,t,a){}}]);