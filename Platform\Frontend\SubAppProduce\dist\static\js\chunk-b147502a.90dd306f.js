(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b147502a"],{"04ab":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"数据是否调整",prop:"IsAdjust"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.IsAdjust,callback:function(e){t.$set(t.form,"IsAdjust",e)},expression:"form.IsAdjust"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"有调整",value:!0}}),a("el-option",{attrs:{label:"未调整",value:!1}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:t.loading},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("UpdateDate",{ref:"updateDate",attrs:{"statistical-date":t.form.StatisticalDate,"factory-id":t.form.FactoryId,"report-type":5}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"btn-x"},[t.isView&&t.unCheck&&t.getRoles("OMAPurchaseDetailUpdate")?a("el-button",{attrs:{loading:t.updateBtn,disabled:t.loading},on:{click:t.handleUpdate}},[t._v("更新数据")]):t._e(),t.getRoles("OMAPurchaseDetailExport")?a("el-button",{attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e(),t.unCheck&&t.getRoles("OMAPurchaseDetailSubmit")?a("el-button",{attrs:{disabled:t.loading,type:"primary"},on:{click:t.handleSubmit}},[t._v("提交核算")]):t._e(),t.getRoles("OMAPurchaseDetailRecord")?a("el-button",{on:{click:t.handleRecord}},[t._v(" 修正记录")]):t._e(),t.toBeConfirmed&&t.getRoles("OMAPurchaseCheck")?[a("el-button",{attrs:{type:"danger",disabled:t.loading},on:{click:function(e){return t.handleCheck(!1)}}},[t._v("审核不通过")]),a("el-button",{attrs:{type:"success",disabled:t.loading},on:{click:function(e){return t.handleCheck(!0)}}},[t._v("审核通过")])]:t._e()],2)],1),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1),t.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.width},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:t.fetchData}})],1):t._e()],1)])},n=[]},1162:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i=a("7757"),n=a("4f39");e.default={props:{statisticalDate:{type:String,default:""},factoryId:{type:String,required:!0},reportType:{type:Number,required:!0}},data:function(){return{showTip:!1,time:"",curDate:""}},watch:{statisticalDate:function(t){this.curDate=t,this.curDate&&this.getUpdate()}},methods:{getUpdate:function(){var t=this;(0,i.GetBusinessLastUpdateDate)({StatisticalDate:this.curDate,FactoryId:this.factoryId,ReportType:this.reportType}).then((function(e){if(e.IsSucceed){var a=e.Data,i=a.Business_Last_Date,r=a.Account_Generate_Date,o=new Date(i),s=new Date(r);o>s?(t.showTip=!0,t.time=(0,n.parseTime)(o)):(t.showTip=!1,t.time="")}else t.$message({message:e.Message,type:"error"})}))}}}},"1b7e":function(t,e,a){"use strict";a.r(e);var i=a("1162"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"230a":function(t,e,a){"use strict";a.r(e);var i=a("7ced"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"4f39":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=r,e.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var n=i(a("53ca"));function r(t,e){if(0===arguments.length||!t)return null;var a,i=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,n.default)(t)?a=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),a=new Date(t));var r={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=i.replace(/{([ymdhisa])+}/g,(function(t,e){var a=r[e];return"a"===e?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var a=t.split("~"),i=r(new Date(a[0]),e)+" ~ "+r(new Date(a[1]),e);return i}return t&&t.length>0?r(new Date(t),e):void 0}},"5de8":function(t,e,a){"use strict";a.r(e);var i=a("ad04"),n=a("e7e1");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("efda");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"04d4e93f",null);e["default"]=s.exports},"60c3":function(t,e,a){},"664d":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",data:t.tableData,stripe:"",loading:t.loading,align:"left",height:"500",resizable:"","empty-text":"暂无数据"}},[a("vxe-column",{attrs:{fixed:"left","min-width":"200",field:"ProjectAbbreviation",title:"项目简称"}}),a("vxe-column",{attrs:{fixed:"left","min-width":"200",field:"ProjectNumber",title:"项目编号"}}),a("vxe-column",{attrs:{"min-width":"200",field:"Fix_Value",title:"产值修正值"}}),a("vxe-column",{attrs:{"min-width":"200",field:"Remark",title:"备注说明"}}),a("vxe-column",{attrs:{"min-width":"150",field:"Create_UserName",title:"操作人"}}),a("vxe-column",{attrs:{"min-width":"200",field:"Create_Date",title:"操作时间"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])})],1),a("div",{staticClass:"cs-footer"},[a("el-button",{on:{click:t.handleClose}},[t._v("取 消")])],1)],1)},n=[]},"7b48":function(t,e,a){},"7ced":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("14d9"),a("b0c0"),a("d3b7");var n=i(a("c14f")),r=i(a("1da1")),o=i(a("ac03")),s=a("cf45"),c=a("4f39"),l=i(a("3502")),u=i(a("5de8")),d=i(a("e560")),f=i(a("90f5")),m=a("9a77"),h=a("b60f"),p=a("db0a"),g=a("05e0"),b=i(a("6612"));e.default={name:"OMAPurchaseDetail",components:{VTable:l.default,CorrectDialog:u.default,RecordDialog:d.default,UpdateDate:f.default},mixins:[o.default],data:function(){return{updateBtn:!1,showBtn:!1,isView:!1,toBeConfirmed:!1,form:{StatisticalDate:"",IsAdjust:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],currentComponent:"",title:"",width:"",dialogVisible:!1,unCheck:!1}},computed:{curTitle:function(){return"".concat((0,c.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计金额（元）")}},beforeCreate:function(){this.curModuleKey=m.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,t.toBeConfirmed="check"===t.$route.query.type,t.isView="view"===t.$route.query.type,t.fetchData(),e.n=1,(0,s.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate(this.form.StatisticalDate)&&(this.loading=!0,(0,h.GetDDProjectSummaryList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[],t.tableData.length?(t.toBeConfirmed=2===t.tableData[0].AccountingStatus,t.unCheck=1===t.tableData[0].AccountingStatus,t.isView=!t.toBeConfirmed,t.showBtn=!0):t.showBtn=!1;var a=t.setTotalData(t.tableData,t.generateColumn()),i=a.column;t.columns=i,t.$refs["tb"].setColumns(i)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleEdit:function(t,e){var a=this;this.currentComponent="CorrectDialog",this.title=e?"查看修正值":"编辑修正值",this.width="40%",this.dialogVisible=!0,this.$nextTick((function(i){a.$refs["content"].initData({isDetail:e,Statics_Date:a.form.StatisticalDate,Factory_Id:a.form.FactoryId,Sys_Project_Id:t.Sys_Project_Id,ProjectAbbreviation:t.ProjectAbbreviation,ProjectNumber:t.ProjectNumber,ProjectStatus:t.ProjectStatus})}))},handleUpdate:function(){var t=this;this.$confirm("是否更新数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.updateBtn=!0,(0,p.DailyBatch)({Factory_Id:t.form.FactoryId,Date:t.form.StatisticalDate}).then((function(e){e.IsSucceed?(t.fetchData(),t.$refs["updateDate"].getUpdate(),t.$message({message:"更新成功",type:"success"})):t.$message({message:e.Message,type:"error"}),t.updateBtn=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleClose:function(){this.dialogVisible=!1},handleSubmit:function(){var t=this;this.$confirm("是否提交核算?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,h.SubmitDDAccounting)({StatisticalDate:t.form.StatisticalDate,FactoryId:t.form.FactoryId}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handlePurchaseOutputInfo:function(){this.$router.push({name:"OMAPurchaseOutputDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handlePurchaseCostInfo:function(){this.$router.push({name:"OMAPurchaseCostDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handlePurchaseFeeInfo:function(){this.$router.push({name:"OMAPurchaseFeeDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey="",this.form.IsAdjust=""},handleCheck:function(t){var e=this;this.$confirm("是否确定审核".concat(t?"通过":"不通过","?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,h.ReviewStockControlSummary)({FactoryId:e.form.FactoryId,Is_Approved:t,StatisticalDate:e.form.StatisticalDate}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handleRecord:function(){var t=this;this.currentComponent="RecordDialog",this.title="修正记录",this.width="70%",this.dialogVisible=!0,this.$nextTick((function(e){t.$refs["content"].fetchData({StatisticalDate:t.form.StatisticalDate,FactoryId:t.form.FactoryId})}))},showRed:function(t,e){var a=+(0,b.default)(t).format("0.[00]"),i=+(0,b.default)(e).format("0.[00]");return a>i},generateColumn:function(){var t=this,e=this.$createElement,a=160;return[{fixed:"left",title:"项目简称",params:{rowSpan:2},children:[{params:{none:"none"},children:[{title:this.curTitle,params:{colSpan:4},minWidth:g.ProjectAbbreviationW,field:"ProjectAbbreviation"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目编号",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:g.ProjectNumberW,field:"ProjectNumber"}]}]},{fixed:"left",params:{rowSpan:2},title:"单位名称",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:a,field:"FBDW_Name"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目状态",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:g.ProjectStatusW,field:"ProjectStatus"}]}]},{params:{bg:"bg-green"},slots:{header:function(){var a=[e("span",["产值(元) "])];return t.getRoles("OMAPurchaseDetailOutput")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handlePurchaseOutputInfo()}}},["查看详情"])),a}},children:[{title:"代购主辅材产值(元)",children:[{minWidth:a,field:"Material_Output",title:0,isTotal:!0}]},{title:"代付费用产值(元)",children:[{minWidth:a,field:"Fee_Output",title:0,isTotal:!0}]},{title:"修正值(元)",children:[{minWidth:a,field:"Fix_Value",title:0,isTotal:!0}]},{title:"小计(元)",children:[{isTotal:!0,minWidth:a,field:"Output_SubTotal",title:0}]}]},{params:{bg:"bg-cyan"},slots:{header:function(){var a=[e("span",["成本(元) "])];return t.getRoles("OMAPurchaseDetailCost")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handlePurchaseCostInfo()}}},["查看详情"])),a}},children:[{title:"主材成本(元)",children:[{minWidth:a,field:"Material_Cost",title:0,isTotal:!0}]}]},{slots:{header:function(){var a=[e("span",["费用(元) "])];return t.getRoles("OMAPurchaseDetailFee")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handlePurchaseFeeInfo()}}},["查看详情"])),a}},params:{bg:"bg-blue"},children:[{title:"项目辅材(元)",children:[{minWidth:a,field:"Aux_Fee",title:0,isTotal:!0}]},{title:"代付费用(元)",children:[{minWidth:a,field:"Pay_For_Other_Fee",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Fee_SubTotal",title:0,isTotal:!0}]}]},{title:"利润(元)",params:{bg:"bg-purple"},children:[{title:"产值-成本-费用",children:[{minWidth:a,field:"Profit",title:0,isTotal:!0}]}]},{title:"代收代付",params:{bg:"bg-yellow"},children:[{title:"代收款(元)",children:[{minWidth:a,field:"Agency_Fund",title:0,isTotal:!0}]},{title:"代开票(元)",children:[{minWidth:a,field:"Agency_Invoicing",title:0,isTotal:!0}]},{title:"代付款(元)",children:[{minWidth:a,field:"Agency_Payment",title:0,isTotal:!0,slots:{default:function(a){var i=a.row,n="";return n=t.showRed(i.Agency_Payment,i.Agency_Fund)?e("span",{class:"txt-red"},[i.Agency_Payment]):e("span",[i.Agency_Payment]),[n]}}}]},{title:"代收票(元)",children:[{minWidth:a,field:"Agency_Ticket",title:0,isTotal:!0}]}]},{title:"操作",params:{empty:!0},visible:!0,fixed:"right",minWidth:a,slots:{default:function(a){var i=a.row,n=a.column,r=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleEdit(i)}}},["编辑"]);return t.unCheck&&t.getRoles("OMAPurchaseEdit")?(n.params.empty=!1,[r]):[]}}}]}}}},"90f5":function(t,e,a){"use strict";a.r(e);var i=a("b5756"),n=a("1b7e");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("e209");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"79bd74a2",null);e["default"]=s.exports},9475:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7");var i=a("d7ff");e.default={data:function(){return{form:{ProjectAbbreviation:"",ProjectNumber:"",ProjectStatus:"",Fix_Value:void 0,Remark:""},btnLoading:!1,isDetail:!1,loading:!1,rules:{Fix_Value:[{required:!0,message:"请输入",trigger:"blur"}]}}},methods:{initData:function(t){this.otherData=t;var e=t.ProjectStatus,a=t.Statics_Date,i=t.Factory_Id,n=t.Sys_Project_Id,r=t.ProjectNumber,o=t.ProjectAbbreviation,s=t.isDetail;this.isDetail=s,this.form.ProjectAbbreviation=o,this.form.ProjectNumber=r,this.form.ProjectStatus=e,this.getInfo({StatisticalDate:a,FactoryId:i,Fix_Business_Type:4,Sys_Project_Id:n,Is_Lastest:!0})},getInfo:function(t){var e=this;this.loading=!0,(0,i.GetFixRecord)(t).then((function(t){if(t.IsSucceed){var a=t.Data;if(a&&a.length){var i=a[0];e.form.Remark=i.Remark,e.form.Fix_Value=i.Fix_Value}else e.form.Remark="",e.form.Fix_Value=""}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1}))},handleSubmit:function(){var t=this;this.$refs["form"].validate((function(e){if(e){var a=t.otherData,n=a.Statics_Date,r=a.Factory_Id,o=a.Sys_Project_Id;t.btnLoading=!0,(0,i.SubmitFixValue)({Statics_Date:n,Factory_Id:r,Sys_Project_Id:o,Fix_Value:t.form.Fix_Value,Remark:t.form.Remark,Fix_Business_Type:4}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.$emit("refresh"),t.handleClose()):t.$message({message:e.Message,type:"error"})})).finally((function(){t.btnLoading=!1}))}}))},handleClose:function(){this.$emit("close")}}}},a62a:function(t,e,a){"use strict";a.r(e);var i=a("04ab"),n=a("230a");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("aa78");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"24164e2e",null);e["default"]=s.exports},aa78:function(t,e,a){"use strict";a("60c3")},ad04:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"150px"}},[a("el-form-item",{attrs:{label:"项目简称"}},[t._v(" "+t._s(t.form.ProjectAbbreviation)+" ")]),a("el-form-item",{attrs:{label:"项目编号",prop:"ProjectNumber"}},[t._v(" "+t._s(t.form.ProjectNumber)+" ")]),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[t._v(" "+t._s(t.form.ProjectStatus)+" ")]),a("el-form-item",{attrs:{label:"产值修正值",prop:"Fix_Value"}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",model:{value:t.form.Fix_Value,callback:function(e){t.$set(t.form,"Fix_Value",e)},expression:"form.Fix_Value"}})],1),a("el-form-item",{attrs:{label:"备注说明",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"200",type:"textarea"},model:{value:t.form.Remark,callback:function(e){t.$set(t.form,"Remark",e)},expression:"form.Remark"}})],1),a("el-form-item",{staticClass:"cs-footer"},[a("el-button",{on:{click:t.handleClose}},[t._v("取 消")]),a("el-button",{attrs:{loading:t.btnLoading,type:"primary"},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},n=[]},ad85:function(t,e,a){"use strict";a("ba4e")},b5756:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",[a("span",{staticClass:"cs-label"},[t._v("数据列表")]),t.time?a("span",{staticClass:"cs-time"},[t._v("数据更新时间："+t._s(t.time))]):t._e(),t.showTip?a("span",{staticClass:"cs-tip"},[t._v("有数据变更请点击更新数据")]):t._e()])},n=[]},ba4e:function(t,e,a){},bf25:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var n=a("d7ff"),r=i(a("6612"));e.default={data:function(){return{tableData:[],loading:!1}},methods:{fetchData:function(t){var e=this;this.loading=!0;var a=t.StatisticalDate,i=t.FactoryId;(0,n.GetFixRecord)({StatisticalDate:a,Fix_Business_Type:4,FactoryId:i}).then((function(t){t.IsSucceed?e.tableData=((null===t||void 0===t?void 0:t.Data)||[]).map((function(t){return t.Fix_Value=(0,r.default)(t.Fix_Value).format("0,0.[00]"),t})):e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1}))},handleClose:function(){this.$emit("close")}}}},c7a0:function(t,e,a){"use strict";a.r(e);var i=a("bf25"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},cbdc:function(t,e,a){},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,a("d3b7");var i=a("6186");function n(t){return new Promise((function(e,a){(0,i.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},d7ff:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetExtenalProcessFeesList=h,e.GetFixRecord=v,e.GetProcessFeesList=m,e.GetSCCostDailyDetailList=u,e.GetSCFeesDailyDetailList=f,e.GetSCOutputDailyDetailList=d,e.GetSCProjectSummaryList=s,e.GetSCStockDailyDetailList=p,e.GetSemiProductDailyDetailList=g,e.ImportSCFixRecord=y,e.ProductionSummaryList=o,e.ReviewStockControlSummary=l,e.SubmitAccounting=c,e.SubmitFixValue=b;var n=i(a("b775")),r="";function o(t){return(0,n.default)({url:r+"/oma/screport/ProductionSummaryList",method:"post",data:t})}function s(t){return(0,n.default)({url:r+"/oma/screport/GetSCProjectSummaryList",method:"post",data:t})}function c(t){return(0,n.default)({url:r+"/oma/screport/SubmitAccounting",method:"post",data:t})}function l(t){return(0,n.default)({url:r+"/oma/screport/ReviewStockControlSummary",method:"post",data:t})}function u(t){return(0,n.default)({url:r+"/oma/screport/GetSCCostDailyDetailList",method:"post",data:t})}function d(t){return(0,n.default)({url:r+"/oma/screport/GetSCOutputDailyDetailList",method:"post",data:t})}function f(t){return(0,n.default)({url:r+"/oma/screport/GetSCFeesDailyDetailList",method:"post",data:t})}function m(t){return(0,n.default)({url:r+"/oma/screport/GetProcessFeesList",method:"post",data:t})}function h(t){return(0,n.default)({url:r+"/oma/screport/GetExtenalProcessFeesList",method:"post",data:t})}function p(t){return(0,n.default)({url:r+"/oma/screport/GetSCStockDailyDetailList",method:"post",data:t})}function g(t){return(0,n.default)({url:r+"/oma/screport/GetSemiProductDailyDetailList",method:"post",data:t})}function b(t){return(0,n.default)({url:r+"/oma/screport/SubmitFixValue",method:"post",data:t})}function v(t){return(0,n.default)({url:r+"/oma/screport/GetFixRecord",method:"post",data:t})}function y(t){return(0,n.default)({url:r+"/oma/screport/ImportSCFixRecord",method:"post",data:t})}},db0a:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DailyBatch=u,e.GetAccountingDefaultSetting=o,e.GetProjectAccountingSettingDetail=s,e.GetProjectListForAccounting=r,e.SaveDefaultAccountingSetting=l,e.SaveProjectAccountingSetting=c;var n=i(a("b775"));function r(t){return(0,n.default)({url:"/oma/AccountingSetting/GetProjectListForAccounting",method:"post",data:t})}function o(t){return(0,n.default)({url:"/oma/AccountingSetting/GetAccountingDefaultSetting",method:"post",data:t})}function s(t){return(0,n.default)({url:"/oma/AccountingSetting/GetProjectAccountingSettingDetail",method:"post",data:t})}function c(t){return(0,n.default)({url:"/oma/AccountingSetting/SaveProjectAccountingSetting",method:"post",data:t})}function l(t){return(0,n.default)({url:"/oma/AccountingSetting/SaveDefaultAccountingSetting",method:"post",data:t})}function u(t){return(0,n.default)({url:"/oma/AccountingSetting/DailyBatch",method:"post",data:t})}},e209:function(t,e,a){"use strict";a("cbdc")},e560:function(t,e,a){"use strict";a.r(e);var i=a("664d"),n=a("c7a0");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("ad85");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"facba216",null);e["default"]=s.exports},e7e1:function(t,e,a){"use strict";a.r(e);var i=a("9475"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},efda:function(t,e,a){"use strict";a("7b48")}}]);