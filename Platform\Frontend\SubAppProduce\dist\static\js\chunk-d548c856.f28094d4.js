(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-d548c856"],{"1cba":function(t,e,a){"use strict";a.r(e);var n=a("f0f5"),c=a("fa5c");for(var o in c)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return c[t]}))}(o);a("6bc5");var r=a("2877"),u=Object(r["a"])(c["default"],n["a"],n["b"],!1,null,"5b309498",null);e["default"]=u.exports},"36a1":function(t,e,a){},"6bc5":function(t,e,a){"use strict";a("36a1")},"777a":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a("d7ed");e.default={name:"SysCache",data:function(){return{tableData:[]}},created:function(){var t=this;(0,n.GetCacheList)().then((function(e){t.tableData=e.Data}))},methods:{delClick:function(t){var e=this;this.$confirm("是否删除缓存?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,n.RemoveCache)({keys:t}).then((function(t){!0===t.IsSucceed&&(e.$message({type:"success",message:"删除成功"}),(0,n.GetCacheList)().then((function(t){e.tableData=t.Data})))}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))}}}},d7ed:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetCacheList=o,e.RemoveCache=r;var c=n(a("b775"));function o(t){return(0,c.default)({method:"post",url:"/SYS/Queue/GetCacheList",data:t})}function r(t){return(0,c.default)({method:"post",url:"/SYS/Queue/RemoveCache",data:t})}},f0f5:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return c}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"container"},[a("el-table",{staticStyle:{width:"100%",height:"calc(100vh - 130px)"},attrs:{data:t.tableData,border:""}},[a("el-table-column",{attrs:{align:"center",label:"缓存对象",prop:"CacheObject","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"缓存类型",prop:"CacheType","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"到期时间",prop:"Expire","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticStyle:{color:"#fb6b7f"},attrs:{size:"small",type:"text"},on:{click:function(a){return t.delClick(e.row.Key)}}},[t._v("删除 ")])]}}])})],1)],1)])},c=[]},fa5c:function(t,e,a){"use strict";a.r(e);var n=a("777a"),c=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=c.a}}]);