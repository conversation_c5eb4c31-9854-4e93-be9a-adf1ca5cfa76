(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2fb54f50"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=r(),l=e-i,s=20,u=0;t="undefined"===typeof t?500:t;var d=function(){u+=s;var e=Math.easeInOutQuad(u,i,l,t);o(e),u<t?n(d):a&&"function"===typeof a&&a()};d()}},"0c47":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("fb6a"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("2532");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),l=a("586a"),s=n(a("333d")),u=a("c685");t.default={name:"UnPartDetail",components:{Pagination:s.default},data:function(){return{names:"",nameMode:1,searchForm:{Working_Team_Id:"",Comp_Id:"",Working_Process_Id:""},tableData:[],originData:[],loading:!1,currentPage:1,pageSize:u.tablePageSize[0],total:0,tablePageSize:u.tablePageSize}},methods:{handleOpen:function(e,t){this.originData=[],this.searchForm.Working_Process_Id=e.Working_Process_Id,this.searchForm.Working_Team_Id=t||"",this.searchForm.Comp_Id=e.Comp_Id,this.fetchData()},fetchData:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.loading=!0,a={Working_Team_Id:e.searchForm.Working_Team_Id,Comp_Id:e.searchForm.Comp_Id,Working_Process_Id:e.searchForm.Working_Process_Id},(0,l.GetUnPreparedList)(a).then((function(t){t.IsSucceed?(e.originData=t.Data||[],e.currentPage=1,e.filterTableData()):(e.originData=[],e.tableData=[],e.total=0,e.$message({message:t.Message,type:"error"}))})).finally((function(){e.loading=!1}));case 1:return t.a(2)}}),t)})))()},filterTableData:function(){var e=this,t=[];t=this.names?1===this.nameMode?this.originData.filter((function(t){return t.Code&&t.Code.includes(e.names)})):this.originData.filter((function(t){return t.Code&&t.Code===e.names})):this.originData,t=t.map((function(e){return(0,o.default)((0,o.default)({},e),{},{Lack_Count:(e.Need_Count||0)-(e.Stock_Count||0)})})),this.total=t.length;var a=(this.currentPage-1)*this.pageSize;this.tableData=t.slice(a,a+this.pageSize)},handleSearch:function(){this.currentPage=1,this.filterTableData()},handleReset:function(){this.names="",this.nameMode=1,this.currentPage=1,this.filterTableData()},handlePageChange:function(e){var t=e.page,a=e.limit;this.currentPage=t,this.pageSize=a,this.filterTableData()}}}},"0cac":function(e,t,a){"use strict";a("3240")},"1ab0":function(e,t,a){"use strict";a.r(t);var n=a("1d73"),o=a("5d5c");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("0cac");var i=a("2877"),l=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"2ba175a7",null);t["default"]=l.exports},"1d73":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},on:{change:function(t){return e.projectChange(e.form.Sys_Project_Id)}},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.Sys_Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{attrs:{disabled:!e.form.Area_Id,clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.installUnitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"构件类型",prop:"Type_Names"}},[a("el-tree-select",{ref:"treeSelectType",staticClass:"cs-tree-x",attrs:{"tree-params":e.treeParamsType,"value-key":"Id"},on:{searchFun:e._searchFun},model:{value:e.form.Type_Names,callback:function(t){e.$set(e.form,"Type_Names",t)},expression:"form.Type_Names"}})],1),a("el-form-item",{attrs:{label:"领用工序",prop:"Comp_Process_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Comp_Process_Id,callback:function(t){e.$set(e.form,"Comp_Process_Id",t)},expression:"form.Comp_Process_Id"}},e._l(e.processList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"生产班组",prop:"Woking_Team_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Woking_Team_Id,callback:function(t){e.$set(e.form,"Woking_Team_Id",t)},expression:"form.Woking_Team_Id"}},e._l(e.teamList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"构件名称",prop:"SteelNames"}},[a("el-input",{staticStyle:{width:"220px"},attrs:{type:"text",placeholder:"请输入内容",clearable:"",size:"small"},model:{value:e.searchName,callback:function(t){e.searchName=t},expression:"searchName"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择",size:"small"},slot:"prepend",model:{value:e.searchNameMode,callback:function(t){e.searchNameMode=t},expression:"searchNameMode"}},[a("el-option",{attrs:{label:"模糊搜索",value:1}}),a("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1),a("el-form-item",{attrs:{label:"齐套状态",prop:"Is_Kitting"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Is_Kitting,callback:function(t){e.$set(e.form,"Is_Kitting",t)},expression:"form.Is_Kitting"}},[a("el-option",{attrs:{value:!0,label:"已齐套"}}),a("el-option",{attrs:{value:!1,label:"未齐套"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"success",loading:e.btnLoading,disabled:0===e.tableData.length},on:{click:e.handleLeadingOut}},[e._v("导出")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("div",{staticStyle:{height:"calc(100% - 50px)"}},[a("vxe-grid",{staticClass:"cs-vxe-table",attrs:{resizable:"",height:"100%","empty-text":"暂无数据",stripe:"",loading:e.tableLoading,columns:e.tableColumn,data:e.tableData,"show-overflow":"tooltip"},scopedSlots:e._u([{key:"UnProcess_Amount_default",fn:function(t){var n=t.row;return[[a("div",[e._v(e._s(n.UnProcess_Amount+"/"+n.UnProcess_Weight||"-"))])]]}},{key:"Prepare_Count_default",fn:function(t){var a=t.row;return[[e._v(" "+e._s(a.Prepare_Count)+" ")]]}}])})],1),e.loading?e._e():a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"cs-component-num"}),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.pageSizeTotal,"max-height":"100%","page-sizes":e.tablePageSize,page:e.form.PageInfo.Page,limit:e.form.PageInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.form.PageInfo,"Page",t)},"update:limit":function(t){return e.$set(e.form.PageInfo,"PageSize",t)},pagination:e.changePage}})],1)])],1)])])]),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"UnPartDetail"===e.currentComponent?"未齐套量":"零件齐套",visible:e.dialogVisible,width:"80%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"currentComponent",tag:"component",on:{close:e.handleClose}})],1):e._e()],1)},o=[]},"1dd6":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("7f9d");t.default={components:{},props:{},data:function(){return{tableData:[],CompData:"",Columns:[{title:"零件名称",width:"200",field:"Part_Code"},{title:"是否主零件",width:"200",field:"Is_Main"},{title:"单数",width:"200",field:"Times"},{title:"需求数",width:"200",field:"Need_Count"},{title:"排产数量",width:"200",field:"Schduling_Count"},{title:"生产中数量",width:"200",field:"Producing_Count"},{title:"生产完成数量",width:"200",field:"Stock_Count"},{title:"已消耗数量",width:"200",field:"Use_Count"}],tbLoading:!1}},created:function(){},mounted:function(){},methods:{handleOpen:function(e){var t=this;this.tbLoading=!0,this.CompData=e,(0,n.GetPartPrepareList)({CompId:e.Comp_Id,ProcessId:e.Working_Process_Id}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))}}}},"2ac1":function(e,t,a){"use strict";a.r(t);var n=a("66cf"),o=a("2dab");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("f1d7");var i=a("2877"),l=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"600f9ce8",null);t["default"]=l.exports},"2dab":function(e,t,a){"use strict";a.r(t);var n=a("0c47"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=d,t.GeAreaTrees=T,t.GetFileSync=G,t.GetInstallUnitIdNameList=v,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=C,t.GetProjectAreaTreeList=I,t.GetProjectEntity=s,t.GetProjectList=l,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=y,t.GetSchedulingPartList=L,t.IsEnableProjectMonomer=c,t.SaveProject=u,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=P,t.UpdateProjectTemplateContract=b,t.UpdateProjectTemplateOther=_;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function G(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},3240:function(e,t,a){},"5d5c":function(e,t,a){"use strict";a.r(t);var n=a("6818"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"612b4":function(e,t,a){},"641e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,o.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(a){a.IsSucceed?(t.ProfessionalType=a.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:a.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,n.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,o=e.Data,r=e.Message;if(n){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,o.Grid);var i=t.tbConfig.Code.split(",");"plm_component_page_list"!==i[0]&&"plm_parts_page_list"!==i[0]||(t.tbConfig.Is_Page=!0),t.columns=(o.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+o.Grid.Row_Number:t.form.PageSize=+o.Grid.Row_Number,a(t.columns),t.fetchData()}else t.$message({message:r,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},"66cf":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"un-part-detail"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,model:e.searchForm}},[a("el-form-item",{attrs:{label:"部件/零件名称:"}},[a("el-input",{staticClass:"input-with-select",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.names,callback:function(t){e.names=t},expression:"names"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.nameMode,callback:function(t){e.nameMode=t},expression:"nameMode"}},[a("el-option",{attrs:{label:"模糊搜索",value:1}}),a("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{data:e.tableData,"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"center","show-overflow":"",stripe:"",height:"100%",loading:e.loading,resizable:"","empty-text":"暂无数据","show-footer":"",size:"medium","tooltip-config":{enterable:!0}}},[a("vxe-column",{attrs:{field:"Code",title:"部件/零件名称"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[1===n.Part_Grade?a("el-tag",[e._v("部")]):a("el-tag",{attrs:{type:"success"}},[e._v("零")]),e._v(" "+e._s(n.Code)+" ")]}}])}),a("vxe-column",{attrs:{field:"Need_Count",title:"需求量"}}),a("vxe-column",{attrs:{field:"Stock_Count",title:"库存数"}}),a("vxe-column",{attrs:{field:"Lack_Count",title:"缺件量"}})],1)],1),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.currentPage,limit:e.pageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){e.currentPage=t},"update:limit":function(t){e.pageSize=t},pagination:e.handlePageChange}})],1)},o=[]},6818:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("a15b"),a("d81d"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("b680"),a("d3b7"),a("ac1f"),a("3ca3"),a("5319"),a("5b81"),a("498a"),a("c7cd"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),l=a("586a"),s=a("f2f6"),u=a("fd31"),d=a("3166"),c=a("a024"),f=n(a("333d")),m=n(a("641e")),p=a("c685"),h=a("f4f2"),g=n(a("6612")),P=n(a("a6f7")),b=n(a("2ac1"));t.default={components:{Pagination:f.default,CompleteSet:P.default,UnPartDetail:b.default},mixins:[m.default],data:function(){return{currentComponent:"",dialogVisible:!1,loading:!1,btnLoading:!1,form:{Sys_Project_Id:"",Area_Id:"",InstallUnit_Id:"",Is_Kitting:"",Type_Names:[],Comp_Process_Id:"",Woking_Team_Id:"",SteelNames:"",PageInfo:{Page:1,PageSize:20}},tableLoading:!1,tableColumn:[],tableData:[],tablePageSize:p.tablePageSize,pageSizeTotal:0,multipleSelection:[],projectList:[],installUnitList:[],processList:[],teamList:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},treeParamsType:{"check-strictly":!0,"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],searchName:"",searchNameMode:1}},watch:{ProfessionalType:function(e){0!==this.ProfessionalType.length&&this.getBasicData()}},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.PageInfo.PageSize=e.tablePageSize[0],t.n=1,e.getFactoryTypeOption("pro_inventory_list");case 1:return t.a(2)}}),t)})))()},mounted:function(){},methods:{getBasicData:function(){var e=this;(0,d.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)})),(0,u.GetCompTypeTree)({professional:this.ProfessionalType[0].Code}).then((function(t){t.IsSucceed?(e.treeParamsType.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectType.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})})),(0,c.GetProcessListBase)({}).then((function(t){t.IsSucceed?e.processList=t.Data:e.$message({message:t.Message,type:"error"})})),(0,c.GetWorkingTeams)({}).then((function(t){t.IsSucceed?e.teamList=t.Data:e.$message({message:t.Message,type:"error"})}))},getAreaList:function(e){var t=this;(0,d.GeAreaTrees)({sysProjectId:e}).then((function(e){if(e.IsSucceed){var a=e.Data;t.setDisabledTree(a),t.treeParamsArea.data=e.Data,t.$nextTick((function(a){t.$refs.treeSelectArea.treeDataUpdateFun(e.Data)}))}else t.$message({message:e.Message,type:"error"})}))},getInstallUnitPageList:function(){var e=this;(0,s.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.installUnitList=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var a=e.Children;a&&a.length?e.disabled=!0:(e.disabled=!1,t.setDisabledTree(a))}))},fetchData:function(){var e=this;this.loading=!0;var t=(0,o.default)({},this.form);delete t["PageInfo"],1===this.searchNameMode?(t.Code=this.searchName.trim(),t.SteelNames=""):(t.Code="",t.SteelNames=this.searchName.split(" ").filter(Boolean).join("\n")),this.setColumnList(),(0,l.GetProducingKittingPageList)((0,o.default)((0,o.default)({},t),this.form.PageInfo)).then((function(t){t.IsSucceed?(e.tableData=t.Data.Data.map((function(e){var t=e.UnProcess_Amount.split("/");return e.UnProcess_Weight=Number(t[1]).toFixed(2),e.UnProcess_Amount=Number(t[0]),e.Prepare_Weight=(0,g.default)(e.Weight).multiply(e.Prepare_Count).format("0.[000]"),e.UnPrepare_Count=(0,g.default)(e.UnProcess_Count).subtract(e.Prepare_Count).format("0.[000]"),e.UnPrepare_Weight=(0,g.default)(e.UnPrepare_Count).multiply(e.Weight).format("0.[000]"),e.TotalWeight=(0,g.default)(e.Amount).multiply(e.Weight).format("0.[000]"),e})),e.pageSizeTotal=t.Data.TotalCount,e.loading=!1):e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1}))},setColumnList:function(){var e=this,t=this.$createElement;this.tableColumn=this.columns.filter((function(a){return a.field=a.Code,a.title=a.Display_Name,a.minWidth=160,a.align=a.Align||"center","Project_Name"===a.Code||"Area_Name"===a.Code||"Installunit_Name"===a.Code?a.fixed="left":"UnProcess_Amount"===a.Code?(a.slots={default:"".concat(a.Code,"_default")},a.titleHelp={message:"已排产但当前工序还未加工的量",align:"right"}):"Prepare_Count"===a.Code?(a.slots={default:"".concat(a.Code,"_default")},a.titleHelp={message:"当前生产完成零件可加工构件工序的数量"}):"UnPrepare_Count"!==a.Code&&"UnPrepare_Weight"!==a.Code||(a.slots={default:function(n){var o=n.row;return t("el-link",{attrs:{type:"primary"},on:{click:function(){return e.openUnPartDetail(o)}}},[o[a.Code]])}}),a.Is_Display})),this.tableColumn.unshift({type:"seq",width:60,fixed:"left",align:"center"})},projectChange:function(e){var t=this,a=e;this.form.Area_Id="",this.form.InstallUnit_Id="",this.treeParamsArea.data=[],this.$nextTick((function(e){t.$refs.treeSelectArea.treeDataUpdateFun([])})),e&&this.getAreaList(a)},areaChange:function(){this.form.InstallUnit_Id="",this.getInstallUnitPageList()},areaClear:function(){this.form.Area_Id="",this.form.InstallUnit_Id=""},removeTagFn:function(e,t){},_searchFun:function(e){this.$refs.treeSelectType.filterFun(e)},handleLeadingOut:function(){var e=this,t=(0,o.default)({},this.form);delete t["PageInfo"],t.SteelNames=t.SteelNames.trim().replaceAll(" ","\n"),this.btnLoading=!0,(0,l.ExportProducingKittingPageList)((0,o.default)({},t)).then((function(t){if(t.IsSucceed){var a=new URL(t.Data,(0,h.baseUrl)());window.open(a.href)}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},openPartDetail:function(e){var t=this;this.dialogVisible=!0,this.$nextTick((function(){t.$refs["CompleteSet"].handleOpen(e)}))},openUnPartDetail:function(e){var t=this;this.currentComponent="UnPartDetail",this.dialogVisible=!0,this.$nextTick((function(){t.$refs.currentComponent.handleOpen(e,t.form.Woking_Team_Id)}))},handleClose:function(){this.dialogVisible=!1},changePage:function(){var e=this;this.tableLoading=!0,("number"!==typeof this.form.PageInfo.PageSize||this.form.PageInfo.PageSize<1)&&(this.form.PageInfo.PageSize=20),Promise.all([this.fetchData()]).then((function(t){e.tableLoading=!1}))},handleReset:function(){this.$refs["form"].resetFields(),this.searchName="",this.searchNameMode=1,this.handleSearch()}}}},a024:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=R,t.AddTechnology=s,t.AddWorkingProcess=l,t.DelLib=A,t.DeleteProcess=T,t.DeleteProcessFlow=I,t.DeleteTechnology=v,t.DeleteWorkingTeams=S,t.GetAllProcessList=f,t.GetCheckGroupList=j,t.GetChildComponentTypeList=x,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=w,t.GetFactoryWorkingTeam=P,t.GetGroupItemsList=y,t.GetLibList=i,t.GetLibListType=W,t.GetProcessFlow=p,t.GetProcessFlowListWithTechnology=h,t.GetProcessList=d,t.GetProcessListBase=c,t.GetProcessListTeamBase=U,t.GetProcessListWithUserBase=k,t.GetProcessWorkingTeamBase=N,t.GetTeamListByUser=z,t.GetTeamProcessList=_,t.GetWorkingTeam=b,t.GetWorkingTeamBase=D,t.GetWorkingTeamInfo=O,t.GetWorkingTeams=C,t.GetWorkingTeamsPageList=L,t.SaveWorkingTeams=G,t.UpdateProcessTeam=g;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function s(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function m(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function h(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function P(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function b(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function _(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function y(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function I(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function v(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function T(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function D(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function U(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function k(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a264:function(e,t,a){"use strict";a.r(t);var n=a("1dd6"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},a6f7:function(e,t,a){"use strict";a.r(t);var n=a("ef9d"),o=a("a264");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("d3af");var i=a("2877"),l=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"5d62feda",null);t["default"]=l.exports},d3af:function(e,t,a){"use strict";a("612b4")},ef9d:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("h2",[e._v(e._s("构件名称："+e.CompData.Code))]),a("h2",[e._v(e._s("领用工序："+e.CompData.Comp_Process_Code))]),a("h2",[e._v(e._s("生产班组："+e.CompData.Working_Team_Names))]),a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",data:e.tableData,border:"",resizable:"",stripe:"",loading:e.tbLoading,"show-overflow":"","row-config":{isHover:!0}}},[e._l(e.Columns,(function(t,n){return["是否主零件"==t.title?a("vxe-column",{key:n,attrs:{sortable:"",align:"left",title:t.title,width:t.width,"min-width":t.width,field:t.field},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Is_Main?"是":"否")+" ")]}}],null,!0)}):a("vxe-column",{key:n,attrs:{align:"left",sortable:"",title:t.title,width:t.width,"min-width":t.width,field:t.field}})]}))],2)],1)},o=[]},f1d7:function(e,t,a){"use strict";a("f78a")},f2f6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=s,t.CheckPlanTime=u,t.DeleteInstallUnit=m,t.GetCompletePercent=b,t.GetEntity=y,t.GetInstallUnitAllInfo=c,t.GetInstallUnitComponentPageList=P,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=d,t.GetInstallUnitList=l,t.GetInstallUnitPageList=i,t.GetProjectInstallUnitList=_,t.ImportInstallUnit=h,t.InstallUnitInfoTemplate=p,t.SaveInstallUnit=g,t.SaveOhterSourceInstallUnit=I;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function u(e){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function d(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function c(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function m(e){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function p(e){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function h(e){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(e)})}function I(e){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},f78a:function(e,t,a){}}]);