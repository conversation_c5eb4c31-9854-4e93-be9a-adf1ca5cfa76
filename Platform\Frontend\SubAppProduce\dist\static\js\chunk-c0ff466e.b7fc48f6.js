(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-c0ff466e"],{"06b4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),i=n(a("c14f")),o=n(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var s=a("9002"),l=a("93aa"),u=n(a("6612")),c=a("ed08");t.default={props:{bigTypeData:{type:Number,default:1},formData:{type:Object,default:function(){}},projectList:{type:Array,required:!0,default:function(){return[]}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{OrderCode:"",CategoryId:"",RawName:"",Supplier:"",SupplierName:"",SysProjectId:"",Thick:"",Width:"",Length:"",Material:"",Raw_FullName:"",IsFinished:"0"},selectRow:null,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[],BigType:1}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.PurchaseSubId===t.PurchaseSubId}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)},bigTypeData:{handler:function(e,t){this.BigType=e},immediate:!0}},created:function(){this.getCategoryTreeList()},mounted:function(){this.getConfig();var e=this.formData,t=e.Supplier,a=e.SysProjectId;t&&a&&(this.form.Supplier=t,this.form.SysProjectId=a)},methods:{getConfig:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PROAddRawPurchase");case 1:e.columns=t.v,e.columnsOption(1),e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChange:function(e){var t=this;return(0,o.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return t.BigType=e,t.resetForm("form"),a.n=1,t.columnsOption();case 1:return a.a(2)}}),a)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.BigType,e.currentColumns=JSON.parse(JSON.stringify(e.columns));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;(0,l.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$nextTick((function(a){var n;null===(n=e.$refs)||void 0===n||n.treeSelect.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawProcurementDetails)((0,r.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.InStoreCount=e.AvailableCount,e.WareWeight=(0,u.default)(e.WareWeight||0).divide(1e3).format("0.[00000]"),e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},addJoinedIds:function(){},addToList:function(){var e=(0,c.deepClone)(this.multipleSelection);this.addJoinedIds(),this.$emit("getAddList",e),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e}))),this.addJoinedIds(),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},"07ab":function(e,t,a){"use strict";a.r(t);var n=a("d3de"),r=a("2acc");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("7ae6");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"3e506e20",null);t["default"]=s.exports},"0820":function(e,t,a){},"0ce70":function(e,t,a){"use strict";a.r(t);var n=a("9a6f"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"0ec8":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.renderComponent?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checCheckboxkMethod}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60",title:""}}),e.isView?e._e():a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,r=t.rowIndex;return[a("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&n.Sub_Id)},on:{click:function(t){return e.handleCopy(n,r)}}},[e._v("复制")])]}}],null,!1,2613506362)}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(n){var r=n.row;return[a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(n){var r=n.row;return["Width"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.widthChange(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Length"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.lengthChange(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Material"===t.Code?a("div",[a("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.tableEditData(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"InStoreCount"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.checkWeight(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"InStoreWeight"===t.Code?a("div",[r.Specific_Gravity&&3!==r.BigType?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"InStoreWeight")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Pound_Weight"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Pound_Weight")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Voucher_Weight"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Voucher_Weight")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("div",[e._v(" "+e._s(r.WarehouseName)+"/"+e._s(r.LocationName)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(r)}}})])]):"FurnaceBatchNo"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r.FurnaceBatchNo,callback:function(t){e.$set(r,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})],1):"ProjectName"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-select",{attrs:{transfer:""},on:{change:function(t){return e.projectChange(r)}},model:{value:r.SysProjectId,callback:function(t){e.$set(r,"SysProjectId",t)},expression:"row.SysProjectId"}},e._l(e.projectList,(function(e){return a("vxe-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Short_Name}})})),1)],1):"SupplierName"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-select",{attrs:{transfer:""},on:{change:function(t){return e.supplierChange(r)}},model:{value:r.Supplier,callback:function(t){e.$set(r,"Supplier",t)},expression:"row.Supplier"}},e._l(e.supplierList,(function(e){return a("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):"TaxUnitPrice"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"TaxUnitPrice")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},r=[]},"13da0":function(e,t,a){"use strict";a.r(t);var n=a("ef52"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"161f":function(e,t,a){},"1c3b":function(e,t,a){"use strict";a.r(t);var n=a("0ec8"),r=a("37a64");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"1dbdf76e",null);t["default"]=s.exports},2094:function(e,t,a){"use strict";a("63ec")},"22de":function(e,t,a){"use strict";a.r(t);var n=a("6e51"),r=a("0ce70");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"726754b2",null);t["default"]=s.exports},"27ad2":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("2532");var r=n(a("2909")),i=n(a("c14f")),o=n(a("1da1")),s=a("e144");t.default={props:{isReplacePurchase:{type:Boolean,default:!1},isReceive:{type:Boolean,default:!1},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}},checkTypeList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},Is_Component:"",value:"",options:[],list:[{id:(0,s.v4)(),val:void 0,key:""}],SupplierName:"",PartyUnitName:"",ProjectName:""}},mounted:function(){return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{optionChange:function(e,t){var a;if("supplierArray"===t)this.SupplierName=null===(a=this.supplierList.find((function(t){return t.Id===e})))||void 0===a?void 0:a.Name;else if("partyUnitArray"===t){var n;this.PartyUnitName=null===(n=this.partyUnitList.find((function(t){return t.Id===e})))||void 0===n?void 0:n.Name}else if("projectArray"===t){var r;this.ProjectName=null===(r=this.projectList.find((function(t){return t.Sys_Project_Id===e})))||void 0===r?void 0:r.Short_Name}},handleAdd:function(){this.list.push({id:(0,s.v4)(),val:void 0,key:""})},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a,n,r;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:for(e.btnLoading=!0,a={},n=0;n<e.list.length;n++)r=e.list[n],a[r.key]=r.val;e.list.map((function(t){"Supplier"===t.key?t.name=e.SupplierName:"PartyUnit"===t.key?t.name=e.PartyUnitName:"SysProjectId"===t.key&&(t.name=e.ProjectName)})),e.$emit("batchEditor",e.list),e.btnLoading=!1;case 1:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(a){return(!t.list.map((function(e){return e.key})).includes(a.key)||a.key===e)&&a.label}))},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},init:function(e,t,a){var n,i;if(this.selectList=e,this.isReceive)this.options=[{key:"Width",label:"退库宽度",type:"number"},{key:"Length",label:"退库长度",type:"number"},{key:"InStoreCount",label:"退库数量",type:"number"},{key:"Is_Surplus",label:"是否余料",type:"Is_Surplus"}],(n=this.options).push.apply(n,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList));else if(1===t)this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(i=this.options).push.apply(i,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"Width",label:"宽度(mm)",type:"number"},{key:"Length",label:"长度(mm)",type:"number"},{key:"InStoreCount",label:"入库数量",type:"number"},{key:"Pound_Weight",label:"磅重(t)",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}));else if(2===t){var o;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(o=this.options).push.apply(o,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"Length",label:"长度(mm)",type:"number"},{key:"InStoreCount",label:"入库数量",type:"number"},{key:"Pound_Weight",label:"磅重(t)",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}else if(3===t){var s;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(s=this.options).push.apply(s,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"InStoreCount",label:"入库数量",type:"number"},{key:"Pound_Weight",label:"磅重(t)",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}else{var l;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(l=this.options).push.apply(l,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"InStoreCount",label:"入库数量",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}}}}},"28b0":function(e,t,a){"use strict";a.r(t);var n=a("bbd2"),r=a("edce");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("7fc7");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"370349c8",null);t["default"]=s.exports},"2acc":function(e,t,a){"use strict";a.r(t);var n=a("3eae"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"2b0f":function(e,t,a){"use strict";a.r(t);var n=a("e9fc"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"2e097":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[2===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"非标规格",name:"1"}},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},e._l(e.list,(function(t){return a("el-form-item",{key:t,attrs:{label:t}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:e.form[t],callback:function(a){e.$set(e.form,t,a)},expression:"form[item]"}})],1)})),1)],1):e._e(),1===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"标准规格",name:"2"}},[a("el-table",{ref:"multipleTable",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(a){return a.stopPropagation(),function(a){return e.handleRadioChange(a,t.row)}(a)}},model:{value:e.radioSelect,callback:function(t){e.radioSelect=t},expression:"radioSelect"}})]}}],null,!1,3152109164)}),a("el-table-column",{attrs:{align:"center",prop:"StandardDesc",label:"规格"}})],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},3340:function(e,t,a){"use strict";a("d56b")},3500:function(e,t,a){"use strict";a.r(t);var n=a("5a9a"),r=a("90d5");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("ddaa");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"7c3b4364",null);t["default"]=s.exports},"353d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("caad"),a("2532");n(a("6612")),t.default={props:{columns:{type:Array,default:function(){return[]}}},data:function(){return{resultArray:[],indexes:[],loading:!1}},methods:{setLoading:function(e){this.loading=e},submit:function(){this.loading=!0,this.$emit("submit",1)},setTbData:function(e,t){this.resultArray=e,this.indexes=t},rowStyle:function(e){var t=e.rowIndex;if(this.indexes.includes(t))return{backgroundColor:"#ED8591",color:"#ffffff"}}}}},"37a64":function(e,t,a){"use strict";a.r(t);var n=a("a6d8"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"3eae":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909")),i=n(a("c14f")),o=n(a("1da1")),s=n(a("5530"));a("99af"),a("4de4"),a("7db0"),a("c740"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("13d5"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("9485"),a("e9c4"),a("4ec9"),a("a9e3"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("2532"),a("3ca3"),a("38cf"),a("841c"),a("498a"),a("159b"),a("ddb0");var l=a("ed08"),u=n(a("22de")),c=n(a("1c3b")),d=n(a("c618")),f=n(a("ccb1")),p=n(a("3500")),h=n(a("d450")),m=n(a("879a")),y=n(a("28b0")),g=n(a("cf2c")),b=n(a("c7ab")),v=n(a("b76a")),S=a("4f19"),_=a("93aa"),C=a("6186"),w=a("3166"),P=a("5480"),k=a("2f62"),I=n(a("5167")),x=a("30c8"),L=a("e144"),D=n(a("525f")),R=n(a("8c02")),N=n(a("c13a")),T=n(a("a657")),O=n(a("29d9")),F=n(a("b5b0"));t.default={components:{SelectLocation:F.default,SelectWarehouse:O.default,DynamicTableFields:T.default,SelectDepartmentUser:N.default,SelectDepartment:R.default,AddPurchaseList:D.default,Repeat:I.default,MaterialTb:c.default,AddList2:p.default,ReceiveTb:u.default,BatchEdit:d.default,ImportFile:m.default,Warehouse:y.default,Standard:g.default,AddList:f.default,AddRawMaterialList:h.default,OSSUpload:b.default,draggable:v.default},props:{pageType:{type:Number,default:void 0}},data:function(){return{isRetract:!1,tbLoading:!1,filterVisible:!1,statisticTime:"",dataSourceType:"",multipleSelection:[],ProjectList:[],PartyUnitList:[],SupplierList:[],formStatus:+this.$route.query.status||"",form:{InStoreNo:this.$route.query.id||"",InStoreType:parseInt(this.$route.query.type)||4,InStoreDate:this.getDate(),Is_Replace_Purchase:!1,Remark:"",Attachment:"",Status:null,Return_Dept_Id:"",Return_Person_Id:"",Car_Pound_Weight:""},rules:{InStoreDate:[{required:!0,message:"请选择日期",trigger:"change"}]},searchForm:{Raw_FullName:"",RawName:"",Thick:"",Spec:"",Material:"",SysProjectId:"",CategoryId:"",Length:"",Width:"",Supplier:"",PartyUnit:"",WarehouseId:"",LocationId:""},dialogRepeatVisible:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},currentComponent:"",title:"",dWidth:"60%",saveLoading:!1,search:function(){return{}},openAddList:!1,dialogVisible:!1,BigType:1,fileListData:[],fileListArr:[],searchNum:1,rootTableData:[],tableData:[],typeNumber1:0,typeNumber2:0,typeNumber3:0,typeNumber4:0,popoverVisible:!1,rootColumns:[],checkTypeList:[{BigType:1,checkList:[],checkSameList:[],remarkList:[]},{BigType:2,checkList:[],checkSameList:[],remarkList:[]},{BigType:3,checkList:[],checkSameList:[],remarkList:[]},{BigType:99,checkList:[],checkSameList:[],remarkList:[]}],isNesting:1==this.$route.query.isNesting}},computed:(0,s.default)({isView:function(){return 3===this.pageType},isAdd:function(){return 1===this.pageType},isEdit:function(){return 2===this.pageType},isMaterial:function(){return"false"===this.dataSourceType},isReceive:function(){return"true"===this.dataSourceType},currentTbComponent:function(e){var t=e.isReceive;return t?u.default:c.default},gridCode:function(){return this.isReceive?"PRORawStockReturnReceiveList":"PRORawStockReturnMaterialList"}},(0,k.mapGetters)("factoryInfo",["checkDuplicate"])),provide:function(){return{checkDuplicate:this.getDuplicate}},created:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getFactoryInfo();case 1:return t.n=2,e.getOMALatestStatisticTime();case 2:return t.n=3,e.getPreferenceSettingValue();case 3:return t.n=4,e.getSuppliers();case 4:return t.n=5,e.getPartyAs();case 5:return t.n=6,e.getCategoryTreeList();case 6:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.search=(0,l.debounce)(e.fetchData,800,!0),t.n=1,e.getProject();case 1:if(e.isAdd){t.n=2;break}return t.n=2,e.getInfo();case 2:return t.a(2)}}),t)})))()},methods:{getDuplicate:function(){return this.checkDuplicate},getFactoryInfo:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("factoryInfo/getWorkshop");case 1:return t.a(2)}}),t)})))()},handleRetract:function(){this.isRetract=!this.isRetract},handleSearch:function(){var e=this;this.tableData=JSON.parse(JSON.stringify(this.rootTableData)),this.searchForm.RawName&&(this.tableData=this.tableData.filter((function(t){return t.RawName===e.searchForm.RawName}))),this.searchForm.Thick&&(this.tableData=this.tableData.filter((function(t){return Number(t.Thick)===Number(e.searchForm.Thick)}))),this.searchForm.Spec&&(this.tableData=this.tableData.filter((function(t){return t.Spec+""===e.searchForm.Spec+""}))),this.searchForm.Material&&(this.tableData=this.tableData.filter((function(t){return t.Material===e.searchForm.Material}))),this.searchForm.SysProjectId&&(this.tableData=this.tableData.filter((function(t){return t.SysProjectId===e.searchForm.SysProjectId}))),this.searchForm.CategoryId&&(this.tableData=this.tableData.filter((function(t){return t.CategoryId===e.searchForm.CategoryId}))),this.searchForm.Length&&(this.tableData=this.tableData.filter((function(t){return Number(t.Length)===Number(e.searchForm.Length)}))),this.searchForm.Width&&(this.tableData=this.tableData.filter((function(t){return Number(t.Width)===Number(e.searchForm.Width)}))),this.searchForm.Supplier&&(this.tableData=this.tableData.filter((function(t){return t.Supplier===e.searchForm.Supplier}))),this.searchForm.PartyUnit&&(this.tableData=this.tableData.filter((function(t){return t.PartyUnit===e.searchForm.PartyUnit}))),this.searchForm.Raw_FullName&&(this.tableData=this.tableData.filter((function(t){return(0,x.isMaterialInclude)(t.Raw_FullName,e.searchForm.Raw_FullName)}))),this.searchForm.WarehouseId&&(this.tableData=this.tableData.filter((function(t){return t.WarehouseId===e.searchForm.WarehouseId}))),this.searchForm.LocationId&&(this.tableData=this.tableData.filter((function(t){return t.LocationId===e.searchForm.LocationId}))),this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.tableData)),this.setTabData(),this.filterVisible=!1},handleReset:function(){this.searchForm.Raw_FullName="",this.searchForm.RawName="",this.searchForm.Thick="",this.searchForm.Spec="",this.searchForm.Material="",this.searchForm.SysProjectId="",this.searchForm.CategoryId="",this.searchForm.Length="",this.searchForm.Width="",this.searchForm.Supplier="",this.searchForm.PartyUnit="",this.searchForm.WarehouseId="",this.searchForm.LocationId="",this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.rootTableData)),this.tableData=JSON.parse(JSON.stringify(this.rootTableData)),this.setTabData()},getTableColumns:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,P.getTableConfig)(e.gridCode);case 1:e.rootColumns=t.v,e.$refs["table"].init(e.rootColumns),e.$refs["table"].forceRerender(e.rootColumns);case 2:return t.a(2)}}),t)})))()},handleSaveTbSet:function(){},radioChange:function(e){this.BigType=e,this.multipleSelection=[],this.handleReset()},uploadSuccess:function(e,t,a){this.fileListArr=JSON.parse(JSON.stringify(a))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},handlePreview:function(e){return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(a="",!e.response||!e.response.encryptionUrl){t.n=1;break}a=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,C.GetOssUrl)({url:e.encryptionUrl});case 2:a=t.v,a=a.Data;case 3:window.open(a);case 4:return t.a(2)}}),t)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})},fetchData:function(){},getInfo:function(){var e=this;(0,_.GetRawSurplusReturnStoreDetail)({returnStoreNo:this.$route.query.id}).then((function(t){if(t.IsSucceed){var a=t.Data.Receipt,n=t.Data.Sub,r=a.InStoreDate,i=a.Remark,o=a.Attachment,s=a.Status,l=a.Return_Dept_Id,u=a.Return_Person_Id;if(e.form.InStoreDate=e.getDate(new Date(r)),e.form.Remark=i,e.form.Status=s,e.form.Return_Dept_Id=l,e.form.Return_Person_Id=u,o){e.form.Attachment=o;var c=o.split(",");c.forEach((function(t){var a=t.indexOf("?Expires=")>-1?t.substring(0,t.lastIndexOf("?Expires=")):t,n=decodeURI(a.substring(a.lastIndexOf("/")+1)),r={};r.name=n,r.url=a,r.encryptionUrl=a,e.fileListData.push(r),e.fileListArr.push(r)}))}var d=n.map((function(e,t){return e.index=(0,L.v4)(),e.Warehouse_Location=e.WarehouseName?e.WarehouseName+"/"+e.LocationName:"",e}));e.$nextTick((function(t){e.$refs["table"].tbData=JSON.parse(JSON.stringify(d)),e.tableData=JSON.parse(JSON.stringify(d)),e.rootTableData=JSON.parse(JSON.stringify(d)),e.setTabData()}))}else e.$message({message:t.Message,type:"error"})}))},getOMALatestStatisticTime:function(){var e=this;(0,_.GetOMALatestStatisticTime)().then((function(t){t.IsSucceed&&(e.statisticTime=t.Data)}))},getPreferenceSettingValue:function(){var e=this;(0,S.GetPreferenceSettingValue)({code:"DataSourceType"}).then((function(t){t.IsSucceed&&(e.dataSourceType=t.Data,e.getTableColumns())}))},getSuppliers:function(){var e=this;(0,_.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var n in t.Data){var r={Id:n,Name:t.Data[n]};a.push(r)}e.SupplierList=a}else e.$message({message:t.Message,type:"error"})}))},getPartyAs:function(){var e=this;(0,_.GetPartyAs)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var n in t.Data){var r={Id:n,Name:t.Data[n]};a.push(r)}e.PartyUnitList=a}else e.$message({message:t.Message,type:"error"})}))},getProject:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,w.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectList=t.Data.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,_.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$refs.treeSelect.treeDataUpdateFun(t.Data)):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},changeWarehouse:function(e){this.currentRow=e,this.handleWarehouse(!0)},getWarehouse:function(e){var t=this,a=e.warehouse,n=e.location;this.currentRow?(this.currentRow.WarehouseId=a.Id,this.currentRow.LocationId=n.Id,this.$set(this.currentRow,"WarehouseName",a.Display_Name),this.$set(this.currentRow,"LocationName",n.Display_Name),this.$set(this.currentRow,"Warehouse_Location",a.Display_Name+"/"+n.Display_Name)):this.multipleSelection.forEach((function(e,r){t.$set(e,"WarehouseName",a.Display_Name),t.$set(e,"LocationName",n.Display_Name),t.$set(e,"Warehouse_Location",a.Display_Name+"/"+n.Display_Name),e.LocationId=n.Id,e.WarehouseId=a.Id})),this.handleUpdateRow()},batchEditorFn:function(e){var t=this;this.multipleSelection.forEach((function(a,n){e.forEach((function(e){if(t.$set(a,e.key,e.val),"Supplier"===e.key)t.$set(a,"SupplierName",e.name);else if("PartyUnit"===e.key)t.$set(a,"PartyUnitName",e.name);else if("SysProjectId"===e.key){var n,r=t.ProjectList.find((function(t){return t.Sys_Project_Id===e.val})),i=a.Versions.find((function(e){return e.Id===r.Version_Id}));if(i)a.Specific_Gravity=null===i||void 0===i?void 0:i.Specific_Gravity,a.Version_Id=null===i||void 0===i?void 0:i.Id;else{var o=a.Versions.find((function(e){return e.Is_System}));a.Specific_Gravity=null===o||void 0===o?void 0:o.Specific_Gravity,a.Version_Id=null===o||void 0===o?void 0:o.Id}t.$set(a,"ProjectName",e.name),t.$set(a,"Project_Code",null===(n=t.ProjectList.find((function(t){return t.Sys_Project_Id===e.val})))||void 0===n?void 0:n.Code)}}))})),this.multipleSelection.forEach((function(a,n){e.forEach((function(e){["SysProjectId","Width","Length","InStoreCount"].includes(e.key)?t.$refs["table"].checkWeight(a):["Pound_Weight","TaxUnitPrice"].includes(e.key)&&t.$refs["table"].countTaxUnitPrice(a,"item.key")}))})),this.handleUpdateRow(),this.handleClose()},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,a=e.val;1===t?this.$set(this.currentRow,"StandardDesc",a):(this.$set(this.currentRow,"StandardDesc",a.StandardDesc),this.currentRow.StandardId=a.StandardId)},openAddDialog:function(e){var t=this;this.currentRow=e,this.openAddList=!0,e?(this.isSingle=!0,this.$nextTick((function(a){t.$refs["draft"].setRow(e)}))):this.isSingle=!1},closeView:function(){(0,l.closeTagView)(this.$store,this.$route)},getAddList:function(e){var t=this;e[0].BigType&&(this.BigType=e[0].BigType),this.isReceive&&e.map((function(e){e.Is_Surplus=!0})),2!==this.form.InStoreType&&3!==this.form.InStoreType||e.map((function(e){var a;e.ProjectName=t.form.ProjectName,e.Project_Code=null===(a=t.ProjectList.find((function(e){return e.Sys_Project_Id===t.form.SysProjectId})))||void 0===a?void 0:a.Code,e.SysProjectId=t.form.SysProjectId,e.SupplierName=t.form.SupplierName,e.Supplier=t.form.Supplier,e.PartyUnitName=t.form.PartyUnitName,e.PartyUnit=t.form.PartyUnit})),this.handleUpdateTb(e,"add")},setTabData:function(){this.typeNumber1=this.tableData.filter((function(e){return 1===e.BigType})).length,this.typeNumber2=this.tableData.filter((function(e){return 2===e.BigType})).length,this.typeNumber3=this.tableData.filter((function(e){return 3===e.BigType})).length,this.typeNumber4=this.tableData.filter((function(e){return 99===e.BigType})).length},handleUpdateTb:function(e,t){var a,n;e.map((function(e,t){e.index=(0,L.v4)()}));var i=JSON.parse(JSON.stringify(e));"add"===t&&this.$refs["table"].addData(i),(a=this.tableData).push.apply(a,(0,r.default)(i)),(n=this.rootTableData).push.apply(n,(0,r.default)(i)),this.setTabData()},handleUpdateRow:function(){var e=JSON.parse(JSON.stringify(this.$refs.table.tbData));this.tableData=JSON.parse(JSON.stringify(e)),this.rootTableData.map((function(t){var a=e.find((function(e){return e.index===t.index}));a&&Object.assign(t,a)}))},importData:function(e){this.$refs["table"].importData(e)},getRowName:function(e){var t=e.Name,a=e.Id;this.currentRow.Name=t,this.currentRow.RawId=a,this.currentRow.StandardDesc=""},handleBatchEdit:function(){var e=this;this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.multipleSelection,e.BigType,e.form.InStoreType)}))},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handleWarehouse:function(e){this.currentComponent="Warehouse",this.dWidth="40%",this.title="批量选择仓库/库位",!e&&(this.currentRow=null),this.dialogVisible=!0},handleImport:function(){this.currentComponent="ImportFile",this.dWidth="40%",this.title="原料导入",this.dialogVisible=!0},handleDelete:function(e){var t=JSON.parse(JSON.stringify(this.$refs.table.tbData)),a=JSON.parse(JSON.stringify(this.rootTableData));this.multipleSelection.forEach((function(e,n){var r=t.findIndex((function(t){return t.index===e.index}));t.splice(r,1);var i=a.findIndex((function(t){return t.index===e.index}));a.splice(i,1)})),this.$refs.table.tbData=JSON.parse(JSON.stringify(t)),this.tableData=JSON.parse(JSON.stringify(t)),this.rootTableData=JSON.parse(JSON.stringify(a)),this.multipleSelection.length=0,this.setTabData()},handleClose:function(e){this.openAddList=!1,this.dialogVisible=!1,this.dialogRepeatVisible=!1,this.saveLoading=!1},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},checkValidate:function(){var e=(0,l.deepClone)(this.$refs["table"].tbData.filter((function(e){return e.InStoreCount&&"0"!==e.InStoreCount})));if(!e.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var t={status:!0,type:"",msg:""},a=e;if(a.length&&t.status&&(t=this.checkTb(a)),!t.status)return this.$message({message:"".concat(t.type+t.msg||"必填字段","不能为空"),type:"warning"}),{status:!1};var n={status:!0,type:"",msg:""};return n.status?{data:e,status:!0}:(this.$message({message:"".concat(n.type,"存在重复数据"),type:"warning"}),{status:!1})},checkTb:function(e){for(var t=this,a=this.checkTypeList.find((function(t){return t.BigType===e[0].BigType})).checkList,n=0;n<e.length;n++){for(var r,i=e[n],o=function(){var e=a[s];if(["",null,void 0].includes(i[e])){var n=t.$refs.table.rootColumns,r=n.find((function(t){return t.Code===e}));return{v:{status:!1,msg:null===r||void 0===r?void 0:r.Display_Name,type:1===i.BigType?"板材":2===i.BigType?"型材":3===i.BigType?"钢卷":"其他"}}}},s=0;s<a.length;s++)if(r=o(),r)return r.v;delete i._X_ROW_KEY,delete i.WarehouseName,delete i.LocationName}return{status:!0,msg:"",type:""}},checkSameTb:function(e){for(var t,a=this.checkTypeList.find((function(t){return t.BigType===e[0].BigType})).checkSameList,n=new Map,r=function(){var t=e[i],r=a.map((function(e){return t[e]})).join("-");if(n.has(r))return{v:{status:!1,msg:"",type:1===t.BigType?"板材":2===t.BigType?"型材":3===t.BigType?"钢卷":"其他"}};n.set(r,!0)},i=0;i<e.length;i++)if(t=r(),t)return t.v;return{status:!0,msg:"",type:""}},handleSaveDraft:function(){this.isDraft=!0,this.saveDraft(1,!1)},handleSubmit:function(e){this.isDraft=!1,this.saveDraft(3,!1)},submitInfo:function(){var e=this;this.isDraft?this.saveDraft(1,!0):this.$confirm("确认提交退库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveDraft(3,!0)})).catch((function(){var t;e.$message({type:"info",message:"已取消"}),null===(t=e.$refs["repeat"])||void 0===t||t.setLoading(!1)}))},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.checkValidate(),r=n.data,i=n.status;i&&this.$refs["form"].validate((function(n){if(!n)return!1;e.saveLoading=!0;var i=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){i.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)})),e.form.Attachment=i.join(","),e.form.Status=1===t?1:3,e.form.InStoreNo=e.$route.query.id;var o=e.checkSameInfo(),l=o.show,u=o.resultArray,c=o.indexes;!a&&l&&e.checkDuplicate?(e.dialogRepeatVisible=!0,e.$nextTick((function(t){e.$refs["repeat"].setTbData(u,c)}))):(e.saveLoading=!0,(0,_.RawSurplusReturnStore)({Receipt:(0,s.default)((0,s.default)({},e.form),{},{GenerateSource:e.isNesting?1:0}),Sub:r}).then((function(t){var a;t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"}),e.saveLoading=!1,null===(a=e.$refs["repeat"])||void 0===a||a.setLoading(!1)})))}))},setSelectRow:function(e){this.multipleSelection=e},getDate:function(e){var t=e||new Date,a=t.getFullYear(),n=("0"+(t.getMonth()+1)).slice(-2),r=("0"+t.getDate()).slice(-2);return"".concat(a,"-").concat(n,"-").concat(r)},checkSameInfo:function(){var e=this,t=JSON.parse(JSON.stringify(this.$refs.table.tbData));t.forEach((function(t){var a=e.checkTypeList.find((function(e){return e.BigType===t.BigType})),n=a.checkSameList.filter((function(e){return!!e}));t.currentKey=n.reduce((function(e,a){return e+"-"+(t[a]||"").toString().trim()}),"")}));var a=t,n=a.reduce((function(e,t){return t.currentKey&&(e[t.currentKey]=(e[t.currentKey]||0)+1),e}),{}),r=Object.keys(n).filter((function(e){return n[e]>1})),i=a.filter((function(e){return r.includes(e.currentKey)})),o=i.reduce((function(e,t){return e[t.currentKey]||(e[t.currentKey]=[]),e[t.currentKey].push(t),e}),{}),s=Object.keys(o).reduce((function(e,t){return e=e.concat(o[t]),e}),[]),l=s.reduce((function(e,t,a){return 0!==a&&t.currentKey===s[a-1].currentKey||e.push(a),e}),[]);return{show:s.length>0,resultArray:s,indexes:l}}}}},4952:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("div",{staticStyle:{display:"flex","justify-content":"flex-start"}},[a("div",{staticStyle:{width:"300px",border:"1px solid #E8E8E8","border-radius":"5px","padding-top":"15px","margin-right":"20px"}},[a("div",{staticStyle:{"font-size":"16px","padding-left":"10px","font-weight":"600"}},[e._v("物料类别")]),a("div",[a("tree-detail",{ref:"tree",staticStyle:{"margin-top":"20px",height:"50vh"},attrs:{icon:"icon-folder",loading:e.treeLoading,"tree-data":e.treeData,"show-detail":"","expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick}})],1)]),a("div",{staticStyle:{flex:"1"}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"0"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入关键字搜索",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:e.searchReset}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:e.Code,title:e.Display_Name,"min-width":e.Width}})]}))],2)],1),a("div",{staticStyle:{"margin-top":"10px",display:"flex","justify-content":"space-between"}},[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)],1)]),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)])},r=[]},"4ec9":function(e,t,a){"use strict";a("6f48")},"4f19":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Add=De,t.AddArea=$e,t.AddBaseplanprocess=z,t.AddCementOrder=G,t.AddChildDeliveryArea=ee,t.AddContactChangemoney=J,t.AddContactList=U,t.AddDeliveryPackage=re,t.AddMainContractComments=A,t.AddMainContractConference=j,t.AddMainContractManagercomments=W,t.AddNew=be,t.AddPlanprocessBySteel=q,t.AddProcessdatas=Z,t.AddReview=O,t.AddSteelsByPackage=ie,t.Addmilestone=ce,t.Delete=Re,t.DeleteBasePlanProcess=le,t.DeleteDeliveryPackages=oe,t.DeleteNew=Se,t.DeletePackagesSteels=se,t.Deletemilestone=fe,t.Edit=Ne,t.EditBaseplanprocess=Y,t.EditCementOrder=M,t.EditContactList=E,t.EditNew=ve,t.EditPlanprocessBySteel=K,t.EditReview=F,t.Editmilestone=de,t.FlowInstancesGet=_,t.FlowSchemesGet=S,t.GetBaseplanprocess=H,t.GetCementOrder=B,t.GetComponentTypeList=d,t.GetContactEntities=x,t.GetContactEntitiesbyproject=L,t.GetContactList=V,t.GetDepartmentTree=P,t.GetDetailListDictionaryByCode=o,t.GetDictionaryDetailListByCode=i,t.GetDictionaryWithChildrenByCode=s,t.GetEntity=Le,t.GetFactoryList=f,t.GetFactoryProfessionalList=xe,t.GetFeedData=Q,t.GetFlowInstanceTotal=me,t.GetFlowInstances=g,t.GetImport=pe,t.GetLeaderEntity=u,t.GetList=ke,t.GetLookPlanprocessBySteelEntity=ae,t.GetMonthEntityByRptDate=N,t.GetNewEntity=_e,t.GetPackageList=ne,t.GetPreferenceSettingValue=y,t.GetProcessSet=X,t.GetProfessionalTypeEntities=I,t.GetProject=h,t.GetProjectFlowInstances=Pe,t.GetProjectbyAuthority=m,t.GetProjects=p,t.GetProjectsflowmanagementEntity=he,t.GetProjectsflowmanagements=ye,t.GetReviewEntity=$,t.GetSteelData=te,t.GetStockScanWeight=T,t.GetTenantList=Oe,t.GetTreeList=ge,t.GetTypeList=Ie,t.GetUserApplicationList=Fe,t.GetUserEntity=c,t.GetUserList=l,t.Load=we,t.QueryHistories=C,t.QueryHistories2=w,t.SendMessage=Ce,t.ShortNote=R,t.SubmitFlowInstance=v,t.UpdateActiveType=b,t.UpdateInstallUnitListPlanUltimateTime=ue,t.UpdateNextNode=k,t.UpdateState=Te,t.Verification=D;var r=n(a("b775"));function i(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function o(e){return(0,r.default)({url:"/SYS/Dictionary/GetDetailListDictionaryByCode",method:"post",data:e})}function s(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryWithChildrenByCode",method:"post",data:e})}function l(e){return(0,r.default)({url:"/SYS/User/GetUserList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/sys/user/GetLeaderEntity",method:"post",data:e})}function c(e){return(0,r.default)({url:"/SYS/User/GetUserEntity",method:"post",data:e})}function d(e){return(0,r.default)({url:"/pro/componenttype/GetComponentTypeList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PLM/Plm_Projects/GetEntities",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PLM/Plm_Projects/GetEntity",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PLM/Plm_Projects/GetWorking_ObjectList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/SYS/PreferenceSetting/GetPreferenceSettingValue",method:"post",data:e})}function g(e){return(0,r.default)({url:"/SYS/FlowInstances/Get?"+e,method:"get"})}function b(e){return(0,r.default)({url:"/SYS/FlowInstances/UpdateActiveType",method:"post",params:e})}function v(e){return(0,r.default)({url:"/PLM/BaseReview/SubmitFlowInstance",method:"post",params:e})}function S(e){return(0,r.default)({url:"/SYS/FlowSchemes/Get",method:"get",params:e})}function _(e){return(0,r.default)({method:"get",url:"/SYS/FlowInstances/Get",params:e})}function C(e){return(0,r.default)({method:"get",url:"/SYS/FlowInstances/QueryHistories",params:e})}function w(e){return(0,r.default)({method:"get",url:"/SYS/FlowInstances/QueryHistories2",params:e})}function P(e){return(0,r.default)({method:"post",url:"/SYS/Department/GetDepartmentTree",params:e})}function k(e){return(0,r.default)({url:"/SYS/FlowInstances/UpdateNextNode",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function x(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:e})}function L(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetList",method:"post",data:e})}function D(e){return(0,r.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function R(e){return(0,r.default)({url:"/SYS/FlowDispose/ShortNote",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PLM/ProcessReport/GetMonthEntityByRptDate",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PLM/ProcessReport/GetStockScanWeight",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PLM/BaseReview/AddReview",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PLM/BaseReview/EditReview",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PLM/BaseReview/GetReviewEntity",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PLM/BaseReview/AddMainContractManagercomments",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PLM/BaseReview/AddMainContractComments",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PLM/BaseReview/AddMainContractConference",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PLM/BaseReview/AddCementOrder",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PLM/BaseReview/EditCementOrder",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PLM/BaseReview/GetCementOrder",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PLM/BaseReview/AddContactList",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PLM/BaseReview/EditContactList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PLM/BaseReview/GetContactList",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PLM/BaseReview/AddContactChangemoney",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PLM/BaseReview/AddBaseplanprocess",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PLM/BaseReview/AddPlanprocessBySteel",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PLM/BaseReview/EditPlanprocessBySteel",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PLM/BaseReview/EditBaseplanprocess",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PLM/BaseReview/GetBaseplanprocess",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PLM/BaseReview/GetFeedData",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PLM/BaseReview/GetProcessSet",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PLM/BaseReview/AddProcessdatas",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PLM/BaseReview/AddChildDeliveryArea",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PLM/BaseReview/GetSteelData",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PLM/BaseReview/GetLookPlanprocessBySteelEntity",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PLM/BaseReview/GetPackageList",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PLM/BaseReview/AddDeliveryPackage",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PLM/BaseReview/AddSteelsByPackage",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PLM/BaseReview/DeleteDeliveryPackages",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PLM/BaseReview/DeletePackagesSteels",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PLM/BaseReview/DeleteBasePlanProcess",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/OperationPlan/UpdateInstallUnitListPlanUltimateTime",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PLM/FlowManagement/Add",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PLM/FlowManagement/Edit",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PLM/FlowManagement/Delete",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PLM/Plm_Projects/GetImport",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PLM/FlowManagement/GetProjectsflowmanagementEntity",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PLM/BaseReview/GetFlowInstanceTotal",method:"post",data:e})}function ye(e){return(0,r.default)({url:"/PLM/FlowManagement/GetProjectsflowmanagements",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:e})}function be(e){return(0,r.default)({url:"/PLM/DeviceConfig/AddNew",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PLM/DeviceConfig/EditNew",method:"post",data:e})}function Se(e){return(0,r.default)({url:"/PLM/DeviceConfig/DeleteNew",method:"post",data:e})}function _e(e){return(0,r.default)({url:"/PLM/DeviceConfig/GetNewEntity",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PLM/DeviceConfig/SendMessage",method:"post",data:e})}function we(e){return(0,r.default)({url:"/SYS/FlowSchemes/Load",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PLM/BaseReview/GetProjectFlowInstances",method:"post",data:e})}function ke(e){return(0,r.default)({url:"/PLM/Plm_Professional_Type/GetList",method:"post",data:e})}function Ie(e){return(0,r.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function xe(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalList",method:"post",data:e})}function Le(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetEntity",method:"post",data:e})}function De(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/Add",method:"post",data:e})}function Re(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/Delete",method:"post",data:e})}function Ne(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/Edit",method:"post",data:e})}function Te(e){return(0,r.default)({url:"/SYS/UserApplication/UpdateState",method:"post",data:e})}function Oe(e){return(0,r.default)({url:"/EPC/Customer_Information/GetTenantList",method:"post",data:e})}function Fe(e){return(0,r.default)({method:"post",url:"/SYS/UserApplication/GetUserApplicationList",params:e})}function $e(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/AddArea",method:"post",data:e})}},5167:function(e,t,a){"use strict";a.r(t);var n=a("fee8"),r=a("7924");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("2094");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"3648fb89",null);t["default"]=s.exports},"51e7":function(e,t,a){},"525f":function(e,t,a){"use strict";a.r(t);var n=a("82d9"),r=a("fdfa");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("d3d1");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"52d847c6",null);t["default"]=s.exports},"52ad":function(e,t,a){"use strict";a.r(t);var n=a("ec55"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"5a9a":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"出库单号:",prop:"Out_Store_No"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Out_Store_No,callback:function(t){e.$set(e.form,"Out_Store_No","string"===typeof t?t.trim():t)},expression:"form.Out_Store_No"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料名称:",prop:"Raw_Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Raw_Name,callback:function(t){e.$set(e.form,"Raw_Name","string"===typeof t?t.trim():t)},expression:"form.Raw_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商",prop:"Supplier_Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"供应商",type:"text"},model:{value:e.form.Supplier_Name,callback:function(t){e.$set(e.form,"Supplier_Name","string"===typeof t?t.trim():t)},expression:"form.Supplier_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"甲方单位",prop:"PartA_Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"甲方单位",type:"text"},model:{value:e.form.PartA_Name,callback:function(t){e.$set(e.form,"PartA_Name","string"===typeof t?t.trim():t)},expression:"form.PartA_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"Sys_Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"领用人:",prop:"Pick_Person_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Pick_Person_Id,callback:function(t){e.$set(e.form,"Pick_Person_Id",t)},expression:"form.Pick_Person_Id"}},e._l(e.factoryPeoplelist,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"理论厚度:",prop:"Thick"}},[a("el-input",{staticClass:"input-number",attrs:{step:"any",clearable:"",placeholder:"请输入",type:"number"},model:{value:e.form.Thick,callback:function(t){e.$set(e.form,"Thick","string"===typeof t?t.trim():t)},expression:"form.Thick"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格:",prop:"Spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{type:"text",clearable:"",placeholder:"材质"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"0"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",data:e.fTable,resizable:"","tooltip-config":{enterable:!0},"row-style":e.rowStyle},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["DeliveryTime"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d}"))+" ")]}}:"Out_Store_Date"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d}"))+" ")]}}:"Out_Store_Type"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(1===n.Out_Store_Type?"领用出库":3===n.Out_Store_Type?"委外出库":2===n.Out_Store_Type?"甲供退货":"自采退货")+" ")])]}}:"RawName"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("div",[n.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),n.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),n.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(n.RawName)+" ")],1)]}}:"ReturnCount"===t.Code?{key:"default",fn:function(n){var r=n.row;return[a("vxe-input",{attrs:{min:0,max:r.AvailableCount,type:"number"},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(n[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},r=[]},"63ec":function(e,t,a){},"67a3":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[1!==e.formData.InStoreType?a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("点击此处下载导入模板")])],1):e._e(),a("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},r=[]},"6de4":function(e,t,a){},"6e51":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.renderComponent?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checCheckboxkMethod},"row-style":e.rowStyle,"show-footer":"","footer-method":e.footerMethod},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60",title:""}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([t.Style.tips?{key:"header",fn:function(){return[a("span",[e._v(e._s(t.Display_Name))]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(t.Style.tips)},slot:"content"}),a("i",{staticClass:"el-icon-question",staticStyle:{cursor:"pointer","font-size":"16px"}})])]},proxy:!0}:null,{key:"default",fn:function(n){var r=n.row;return["Out_Store_Weight"===t.Code?a("span",[e._v(e._s(r.Out_Store_Weight||0==r.Out_Store_Weight?r.Out_Store_Weight:"-"))]):"Out_Store_Date"===t.Code?a("span",[e._v(e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}")))]):"Out_Store_Type"===t.Code?a("span",[e._v(e._s(1===r.Out_Store_Type?"领用出库":3===r.Out_Store_Type?"委外出库":2===r.Out_Store_Type?"甲供退货":"自采退货"))]):"TaxUnitPrice"===t.Code||"Tax_All_Price"===t.Code?a("span",[e._v(e._s(r[t.Code]||0==r[t.Code]?r[t.Code].toFixed(2):"-"))]):"Returned_Weight"===t.Code?a("span",[e._v(e._s(r[t.Code]||0==r[t.Code]?r[t.Code]:"-"))]):"Is_Surplus"===t.Code?a("span",[e._v(e._s(r[t.Code]?"是":"否"))]):"SurplusImgUrl"===t.Code?a("span",[r[t.Code]?a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return e.download(r[t.Code])}}},[e._v("下载")]):a("span",[e._v("-")])],1):a("span",[e._v(e._s(r[t.Code]||"-"))])]}},t.Is_Edit?{key:"edit",fn:function(n){var r=n.row;return["Actual_Thick"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"InStoreCount"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.inStoreChange(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"InStoreWeight"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"InStoreWeight")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("div",[e._v(" "+e._s(r.WarehouseName)+"/"+e._s(r.LocationName)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(r)}}})])]):"FurnaceBatchNo"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r.FurnaceBatchNo,callback:function(t){e.$set(r,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})],1):"TaxUnitPrice"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"TaxUnitPrice")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Is_Surplus"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(e._s(r[t.Code]?"是":"否"))]):a("vxe-select",{attrs:{filterable:"",transfer:""},model:{value:r.Is_Surplus,callback:function(t){e.$set(r,"Is_Surplus",t)},expression:"row.Is_Surplus"}},[a("vxe-option",{attrs:{value:!0,label:"是"}}),a("vxe-option",{attrs:{value:!1,label:"否"}})],1)],1):a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]})),e.isView?e._e():a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,r=t.rowIndex;return[a("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&n.Sub_Id)},on:{click:function(t){return e.handleCopy(n,r)}}},[e._v("复制")])]}}],null,!1,2613506362)})],2):e._e()},r=[]},"6f48":function(e,t,a){"use strict";var n=a("6d61"),r=a("6566");n("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),r)},7759:function(e,t,a){"use strict";a.r(t);var n=a("27ad2"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"77f0":function(e,t,a){},7924:function(e,t,a){"use strict";a.r(t);var n=a("353d"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"7ae6":function(e,t,a){"use strict";a("0820")},"7fc7":function(e,t,a){"use strict";a("c93b")},"82d9":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-tabs",{on:{"tab-click":e.handleSearch},model:{value:e.form.IsFinished,callback:function(t){e.$set(e.form,"IsFinished",t)},expression:"form.IsFinished"}},[a("el-tab-pane",{attrs:{label:"未完成",name:"0"}}),a("el-tab-pane",{attrs:{label:"已完成",name:"1"}})],1),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"采购单号",prop:"OrderCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"采购单号",type:"text"},model:{value:e.form.OrderCode,callback:function(t){e.$set(e.form,"OrderCode","string"===typeof t?t.trim():t)},expression:"form.OrderCode"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料分类",prop:"CategoryId"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",staticStyle:{width:"100%"},attrs:{"tree-params":e.treeParams},model:{value:e.form.CategoryId,callback:function(t){e.$set(e.form,"CategoryId",t)},expression:"form.CategoryId"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{attrs:{clearable:"",placeholder:"原料名称",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商",prop:"SupplierName"}},[a("el-input",{attrs:{clearable:"",placeholder:"供应商",type:"text"},model:{value:e.form.SupplierName,callback:function(t){e.$set(e.form,"SupplierName","string"===typeof t?t.trim():t)},expression:"form.SupplierName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"厚度",prop:"Thick"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"厚度"},model:{value:e.form.Thick,callback:function(t){e.$set(e.form,"Thick","string"===typeof t?t.trim():t)},expression:"form.Thick"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{type:"text",clearable:"",placeholder:"材质"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"0"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["WareCount"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("span",{style:{color:n.WareCount>n.PurchaseCount?"red":""}},[e._v(" "+e._s(n.WareCount)+" ")])]}}:"PurchaseWeight"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(Number((a.PurchaseWeight/1e3).toFixed(5)))+" ")]}}:"Actual_Spec"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Actual_Spec||"-")+" ")]}}:"DeliveryTime"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d}"))+" ")]}}:"PurchaseTime"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d}"))+" ")]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(n[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},r=[]},"879a":function(e,t,a){"use strict";a.r(t);var n=a("67a3"),r=a("52ad");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("3340");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"65c0f006",null);t["default"]=s.exports},"88ad":function(e,t,a){},"90d5":function(e,t,a){"use strict";a.r(t);var n=a("ace45"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},9643:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProjectSendingInfo=C,t.CancelFlow=J,t.DeleteProjectSendingInfo=l,t.EditProjectSendingInfo=d,t.ExportComponentStockOutInfo=I,t.ExportInvoiceList=E,t.ExportSendSteel=f,t.ExportSendingDetailInfoList=p,t.GetLocationList=O,t.GetProduceCompentEntity=v,t.GetProducedPartToSendPageList=$,t.GetProjectAcceptInfoPagelist=A,t.GetProjectSendingAllCount=y,t.GetProjectSendingInfoAndItemPagelist=L,t.GetProjectSendingInfoLogPagelist=F,t.GetProjectSendingInfoPagelist=s,t.GetProjectsendinginEntity=c,t.GetReadyForDeliverSummary=x,t.GetReadyForDeliveryComponentPageList=k,t.GetReadyForDeliveryPageList=P,t.GetReturnHistoryPageList=j,t.GetSendToReturnPageList=D,t.GetStockOutBillInfoPageList=N,t.GetStockOutDetailList=g,t.GetStockOutDetailPageList=R,t.GetStockOutDocEntity=w,t.GetStockOutDocPageList=o,t.GetWaitingStockOutPageList=b,t.GetWarehouseListOfCurFactory=T,t.GetWeighingReviewList=G,t.SaveStockOut=_,t.SubmitApproval=V,t.SubmitProjectSending=u,t.SubmitReturnToStockIn=S,t.SubmitWeighingForPC=B,t.Transforms=h,t.TransformsByType=W,t.TransformsWithoutWeight=m,t.WeighingReviewSubmit=M,t.WithdrawDraft=U;var r=n(a("b775")),i=n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetStockOutDocPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoPagelist",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/ComponentStockOut/DeleteProjectSendingInfo",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ComponentStockOut/SubmitProjectSending",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProjectsendinginEntity",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/ComponentStockOut/EditProjectSendingInfo",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ComponentStockOut/ExportSendSteel",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ComponentStockOut/ExportSendingDetailInfoList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ComponentStockOut/Transforms",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ComponentStockOut/TransformsWithoutWeight",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProjectSendingAllCount",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailList",method:"post",data:i.default.stringify(e)})}function b(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetWaitingStockOutPageList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProduceCompentEntity",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/ComponentReturn/SubmitReturnToStockIn",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/ComponentStockOut/SaveStockOut",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/ComponentStockOut/AddProjectSendingInfo",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetStockOutDocEntity",method:"post",data:i.default.stringify(e)})}function P(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryPageList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryComponentPageList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/ComponentStockOut/ExportComponentStockOutInfo",method:"post",data:i.default.stringify(e)})}function x(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliverSummary",method:"post",data:i.default.stringify(e)})}function L(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoAndItemPagelist",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/ComponentReturn/GetSendToReturnPageList",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailPageList",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetStockOutBillInfoPageList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Location/GetLocationList",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoLogPagelist",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProducedPartToSendPageList",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/ComponentStockOut/TransformsByType",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetProjectAcceptInfoPagelist",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/ComponentReturn/GetReturnHistoryPageList",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/ComponentStockOut/GetWeighingReviewList",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/ComponentStockOut/WeighingReviewSubmit",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/ComponentStockOut/SubmitWeighingForPC",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/ComponentStockOut/WithdrawDraft",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/ComponentStockOut/ExportInvoiceList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/ComponentStockOut/SubmitApproval",method:"post",data:e})}function J(e){return(0,r.default)({url:"/Sys/FlowInstances/CancelFlow",method:"post",data:e})}},"9a6f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909")),i=n(a("c14f")),o=n(a("1da1"));a("4de4"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("2532"),a("159b");var s=a("30c8"),l=a("90d1"),u=a("93aa"),c=a("6186");t.default={props:{isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1},isReplacePurchase:{type:Boolean,default:!1},formStatus:{type:Number,default:0},checkTypeList:{type:Array,required:!0,default:function(){return[]}},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{tbLoading:!1,multipleSelection:[],rootColumns:[],columns:[],tbData:[],BigType:1,renderComponent:!0,num:1,isNesting:1==this.$route.query.isNesting}},watch:{bigTypeData:{handler:function(e,t){this.BigType=e,this.columnsOption()},immediate:!1},"tbData.length":{handler:function(e,t){this.updateIsWarning(),this.$emit("updateRow")},immediate:!0}},created:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.$route.query.OutStoreNo&&e.getDetail(),e.isNesting&&e.getReturnPlate();case 1:return t.a(2)}}),t)})))()},methods:{download:function(e){(0,c.GetOssUrl)({url:e}).then((function(e){window.open(e.Data)}))},getReturnPlate:function(){var e=this;(0,u.GetReturnPlate)({ids:Array.isArray(this.$route.query.ids)?this.$route.query.ids:[this.$route.query.ids]}).then((function(t){if(t.IsSucceed){var a=t.Data.map((function(t){return t.Actual_Spec=t.Actual_Spec||(1===t.BigType?t.Actual_Thick:"/"),t.Raw_FullName=(0,s.getMaterialName)(t),t.InStoreCount=t.AvailableCount,t.InStoreWeight=t.AvailableWeight,t.Is_Surplus=!0,e.inStoreChange(t),t.rowIndex="",t}));e.$emit("updateTb",a,"add")}else e.$message({message:t.Message,type:"error"})}))},getDetail:function(){var e=this;this.tbLoading=!0,(0,u.GetRawPickOutStoreSubList)({Out_Store_No:this.$route.query.OutStoreNo}).then((function(t){if(t.IsSucceed){var a=t.Data.map((function(t){return t.Actual_Spec=t.Actual_Spec||(1===t.BigType?t.Actual_Thick:"/"),t.Raw_FullName=(0,s.getMaterialName)(t),t.InStoreCount=t.AvailableCount,t.InStoreWeight=t.AvailableWeight,e.inStoreChange(t),t.rowIndex="",t}));e.$emit("updateTb",a,"add")}else e.$message({message:t.Message,type:"error"})})),this.tbLoading=!1},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},rowStyle:function(e){var t=e.row;if(t.isWarning)return{backgroundColor:"#fb6b7f",color:"#ffffff"}},init:function(e){this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.columns=JSON.parse(JSON.stringify(e.rootColumns)).map((function(t){return t.Style=t.Style?JSON.parse(t.Style):"",["InStoreWeight","InStoreCount","Length","Width","Is_Surplus"].includes(t.Code)&&e.isNesting&&(t.Is_Edit=!1),e.isView&&"AvailableCount"===t.Code&&(t.Is_Display=!1),t}));case 1:return t.a(2)}}),t)})))()},getCheckList:function(e,t){this.checkTypeList.map((function(a){a.BigType===e&&(a.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),a.checkList=a.checkList.map((function(e){return e.Code})),a.checkSameList=t.filter((function(e){return e.Is_Edit&&e.Is_Display&&"InStoreCount"!==e.Code&&"InStoreWeight"!==e.Code&&-1===e.Code.indexOf("Remark")})),a.checkSameList=a.checkSameList.map((function(e){return e.Code})),a.checkSameList.unshift("Out_Store_Sub_Id"),a.remarkList=t.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),a.remarkList=a.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}})))}))},getAllColumnsOption:function(){var e=this,t=[1,2,3];t.forEach((function(t){var a=JSON.parse(JSON.stringify(e.rootColumns));if(1===t);else if(2===t){var n=a.findIndex((function(e){return"Width"===e.Code}));-1!==n&&a.splice(n,1)}else if(3===t){var r=a.findIndex((function(e){return"Width"===e.Code}));-1!==r&&a.splice(r,1);var i=a.findIndex((function(e){return"Length"===e.Code}));-1!==i&&a.splice(i,1)}e.getCheckList(t,a)}))},tbfetchData:function(){this.tbData=[]},handleCopy:function(e,t){var a=JSON.parse(JSON.stringify(e));a.Sub_Id&&(a.Sub_Id=""),delete a._X_ROW_KEY,this.$emit("updateTb",[a],"copy"),this.tbData.splice(t+1,0,a)},updateIsWarning:function(){var e=this,t={};this.tbData.forEach((function(e){t[e.Out_Store_Sub_Id]||(t[e.Out_Store_Sub_Id]={sum:0}),99!==e.BigType?t[e.Out_Store_Sub_Id].sum+=Number(e.InStoreWeight):t[e.Out_Store_Sub_Id].sum+=Number(e.InStoreCount)})),this.tbData.forEach((function(e,a){var n=t[e.Out_Store_Sub_Id].sum;99!==e.BigType?e.isWarning=Number(e.Other_Returned_Weight)>Number(e.Out_Store_Weight)||n>Number(e.Out_Store_Weight)-Number(e.Other_Returned_Weight):e.isWarning=Number(e.Other_Returned_Count)>Number(e.Out_Store_Count)||n>Number(e.Out_Store_Count)-Number(e.Other_Returned_Count)})),this.$nextTick((function(){e.$refs.xTable.updateData(e.tbData)}))},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,a=this,n=e.map((function(e){return e.Actual_Spec=e.Actual_Spec||(1===e.BigType?e.Actual_Thick:"/"),e.Raw_FullName=(0,s.getMaterialName)(e),e.InStoreCount=e.AvailableCount,e.InStoreWeight=e.AvailableWeight,a.inStoreChange(e),e.rowIndex="",e}));(t=this.tbData).push.apply(t,(0,r.default)(n))},setRowFullName:function(e){e.Raw_FullName=(0,s.getMaterialName)(e)},checkWeight:function(e){e.InStoreWeight=(e.Unit_Weight*(e.InStoreCount||0)).toFixed(l.WEIGHT_DECIMAL)/1,this.countTaxUnitPrice(e,"InStoreCount_Width_Length")},inStoreChange:function(e){this.checkWeight(e)},countTaxUnitPrice:function(e,t){this.updateIsWarning(),e.Raw_FullName=(0,s.getMaterialName)(e),this.$emit("updateRow")},footerMethod:function(e){var t=this,a=e.columns,n=e.data,r=[a.map((function(e,a){return l.OutBOUND_DETAIL_SUMMARY_FIELDS.includes(e.field)?t.sumNum(n,e.field,5):0===a?"合计":null}))];return r},sumNum:function(e,t,a){for(var n=0,r=0;r<e.length;r++)n+=Number(e[r][t])||0;return n.toFixed(a)/1}}}},a470:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],a("el-col",{attrs:{span:2}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(0===n[t.Code]?"按需使用":1===n[t.Code]?"使用标准规格":2===n[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("displayValue")(n[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},r=[]},a6d8:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909")),i=n(a("c14f")),o=n(a("1da1"));a("4de4"),a("7db0"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7");var s=a("30c8");t.default={props:{isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1},isReplacePurchase:{type:Boolean,default:!1},formStatus:{type:Number,default:0},checkTypeList:{type:Array,required:!0,default:function(){return[]}},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{tbLoading:!1,multipleSelection:[],rootColumns:[],columns:[],tbData:[],originalData:[],BigType:1,renderComponent:!0,num:1}},watch:{bigTypeData:{handler:function(e,t){this.BigType=e,this.columnsOption()},immediate:!1},isReplacePurchase:{handler:function(e,t){e?(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"SupplierName"!==e.Code||(e.Is_Edit=!1,e.Is_Must_Input=!1)})),this.init(this.rootColumns),this.forceRerender()):(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"SupplierName"!==e.Code||(e.Is_Edit=!0)})),this.init(this.rootColumns),this.forceRerender())},immediate:!1}},inject:["checkDuplicate"],created:function(){return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},mounted:function(){},methods:{forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},init:function(e){this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.columns=JSON.parse(JSON.stringify(e.rootColumns));case 1:return t.a(2)}}),t)})))()},getCheckList:function(e,t){var a=this,n=["Raw_FullName","ProjectName","Project_Code","SupplierName","CategoryName","RawName","RawCode","Material","Spec","Actual_Spec","TaxUnitPrice","Tax_Rate","Warehouse_Location","FurnaceBatchNo"],r="Width",i="Length";this.checkTypeList.map((function(o){o.BigType===e&&(o.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),o.checkList=o.checkList.map((function(e){return e.Code})),o.checkSameList=t.filter((function(e){return n.includes(e.Code)})),o.checkSameList=o.checkSameList.map((function(e){return e.Code})),a.checkDuplicate()&&(o.checkSameList.unshift("RawCode"),2===e?o.checkSameList.push(i):1===e&&(o.checkSameList.push(r),o.checkSameList.push(i))),o.remarkList=t.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),o.remarkList=o.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}})))}))},getAllColumnsOption:function(){var e=this,t=[1,2,3];t.forEach((function(t){var a=JSON.parse(JSON.stringify(e.rootColumns));if(1===t);else if(2===t){var n=a.findIndex((function(e){return"Width"===e.Code}));-1!==n&&a.splice(n,1)}else if(3===t){var r=a.findIndex((function(e){return"Width"===e.Code}));-1!==r&&a.splice(r,1);var i=a.findIndex((function(e){return"Length"===e.Code}));-1!==i&&a.splice(i,1)}e.getCheckList(t,a)}))},tbfetchData:function(){this.tbData=[]},handleCopy:function(e,t){var a=JSON.parse(JSON.stringify(e));a.Sub_Id&&(a.Sub_Id=""),delete a._X_ROW_KEY,this.$emit("updateTb",[a],"copy"),this.tbData.splice(t+1,0,a)},projectChange:function(e){var t=this.projectList.find((function(t){return t.Sys_Project_Id===e.SysProjectId}));if(t){e.ProjectName=t.Short_Name,e.Project_Code=t.Code;var a=e.Versions.find((function(e){return e.Id===t.Version_Id}));e.Specific_Gravity=null===a||void 0===a?void 0:a.Specific_Gravity,e.Version_Id=null===t||void 0===t?void 0:t.Version_Id}this.checkWeight(e),this.$emit("updateRow")},supplierChange:function(e){e.SupplierName=this.supplierList.find((function(t){return t.Id===e.Supplier})).Name,this.$emit("updateRow")},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,a=this,n=e.map((function(e){return e.Actual_Spec=e.Actual_Spec||(1===e.BigType?e.Thick:e.Spec),e.Raw_FullName=(0,s.getMaterialName)(e),a.checkWeight(e),e.rowIndex="",e}));(t=this.tbData).push.apply(t,(0,r.default)(n)),this.tbData=JSON.parse(JSON.stringify(this.tbData))},setRowFullName:function(e){e.Raw_FullName=(0,s.getMaterialName)(e)},lengthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},widthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},checkWeight:function(e){0!==e.Specific_Gravity&&!e.Specific_Gravity||3===e.BigType||(1!==e.BigType&&2!==e.BigType||0!==e.Specific_Gravity&&0!==e.Length?1===e.BigType?"花纹板"===e.CategoryName?0===e.Length||0===e.Width?e.InStoreWeightKG=0:e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(2)):e.InStoreWeightKG="":0===e.Thick||0===e.Width?e.InStoreWeightKG=0:e.Thick&&e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Thick*e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(2)):e.InStoreWeightKG="":2===e.BigType&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(2)):e.InStoreWeightKG="":e.InStoreWeightKG=0,e.InStoreWeight=e.InStoreWeightKG||0===e.InStoreWeightKG?Number((e.InStoreWeightKG/1e3).toFixed(5)):""),this.countTaxUnitPrice(e,"InStoreCount_Width_Length")},countTaxUnitPrice:function(e,t){"InStoreWeight"===t?e.InStoreWeightKG=e.InStoreWeight||0===e.InStoreWeight?Number((1e3*e.InStoreWeight).toFixed(2)):"":"Pound_Weight"===t?e.Pound_Weight_KG=e.Pound_Weight||0===e.Pound_Weight?Number((1e3*e.Pound_Weight).toFixed(2)):"":"Voucher_Weight"===t&&(e.Voucher_Weight_KG=e.Voucher_Weight||0===e.Voucher_Weight?Number((1e3*e.Voucher_Weight).toFixed(2)):""),"TaxUnitPrice"===t&&(e.TaxUnitPriceKG=Number((e.TaxUnitPrice/1e3).toFixed(2))),"TaxUnitPrice"!==t&&"InStoreWeight"!==t&&"InStoreCount_Width_Length"!==t||(99===e.BigType?0===e.InStoreCount||0===e.TaxUnitPrice?e.Tax_All_Price=0:e.InStoreCount&&e.TaxUnitPrice?e.Tax_All_Price=Number((e.InStoreCount*e.TaxUnitPrice).toFixed(2)):e.Tax_All_Price="":0===e.InStoreWeightKG||0===e.TaxUnitPriceKG?e.Tax_All_Price=0:e.InStoreWeightKG&&e.TaxUnitPriceKG?e.Tax_All_Price=Number((e.InStoreWeightKG*e.TaxUnitPriceKG).toFixed(2)):e.Tax_All_Price=""),e.NoTaxAllPrice=(e.Tax_All_Price/(1+e.Tax_Rate/100)).toFixed(3)/1||0,this.$emit("updateRow")},tableEditData:function(e){e.Raw_FullName=(0,s.getMaterialName)(v),this.$emit("updateRow")}}}},ace45:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),i=n(a("c14f")),o=n(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var s=a("9002"),l=a("93aa"),u=a("3c4a"),c=a("ed08");t.default={props:{bigTypeData:{type:Number,default:1},formData:{type:Object,default:function(){}},projectList:{type:Array,required:!0,default:function(){return[]}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{Out_Store_No:"",Pick_Person_Id:"",Raw_Name:"",Raw_FullName:"",Thick:"",Spec:"",Sys_Project_Id:"",In_Store_No:"",Category_Id:"",Supplier_Name:"",PartA_Name:"",Width:"",Length:"",Material:""},selectRow:null,tbLoading:!1,saveLoading:!1,factoryPeoplelist:[],treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[],BigType:1}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.Out_Store_Sub_Id===t.Out_Store_Sub_Id}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)},bigTypeData:{handler:function(e,t){this.BigType=e},immediate:!0}},mounted:function(){this.getCategoryTreeList(),this.getConfig(),this.getCurUserCompanyId();var e=this.formData,t=e.SysProjectId,a=e.InStoreNo;t&&(this.form.Sys_Project_Id=t),this.form.In_Store_No=a},methods:{getCurUserCompanyId:function(){var e=this;(0,u.GetCurUserCompanyId)({}).then((function(t){t.IsSucceed?(e.DepartmentId=t.Data,e.getUserPageList()):e.$message({message:t.Message,type:"error"})}))},getUserPageList:function(){var e=this;(0,u.GetUserPage)({PageSize:-1,DepartmentId:this.DepartmentId}).then((function(t){t.IsSucceed?e.factoryPeoplelist=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},rowStyle:function(e){var t=e.row;if(99!==t.BigType){if(Number(t.Returned_Weight)>Number(t.Out_Store_Weight))return{backgroundColor:"#fb6b7f",color:"#ffffff"}}else if(Number(t.Returned_Count)>Number(t.Out_Store_Count))return{backgroundColor:"#fb6b7f",color:"#ffffff"}},getConfig:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PRORawStockReturnAddList");case 1:e.columns=t.v,e.columnsOption(1),e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChange:function(e){var t=this;return(0,o.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return t.BigType=e,t.resetForm("form"),a.n=1,t.columnsOption();case 1:return a.a(2)}}),a)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.currentColumns=JSON.parse(JSON.stringify(e.columns));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;(0,l.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$refs.treeSelect.treeDataUpdateFun(t.Data)):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawPickOutStoreSubList)((0,r.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.ReturnCount=e.AvailableCount,e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},addToList:function(){var e=(0,c.deepClone)(this.multipleSelection);this.$emit("getAddList",e),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},ad97:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("a9e3"),a("d3b7");var n=a("9643");t.default={props:{pageType:{type:Number,default:void 0}},data:function(){return{warehouses:[],locations:[],form:{Warehouse_Id:"",Location_Id:""},btnLoading:!1,rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){this.getWarehouseListOfCurFactory()},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=e.warehouses.find((function(t){return t.Id===e.form.Warehouse_Id})),n=e.locations.find((function(t){return t.Id===e.form.Location_Id}));e.$emit("warehouse",{warehouse:a,location:n}),e.btnLoading=!1,e.handleClose()}))},getWarehouseListOfCurFactory:function(){var e=this;(0,n.GetWarehouseListOfCurFactory)({type:0===this.pageType?"原材料仓库":"辅料仓库"}).then((function(t){if(t.IsSucceed){if(e.warehouses=t.Data,e.warehouses.length){var a=e.warehouses.find((function(e){return e.Is_Default}));a&&(e.form.Warehouse_Id=a.Id,e.getLocationList(!0))}}else e.$message({message:t.Message,type:"error"})}))},wareChange:function(e){this.form.Location_Id="",e&&this.getLocationList(!0)},getLocationList:function(e){var t=this;(0,n.GetLocationList)({Warehouse_Id:this.form.Warehouse_Id}).then((function(a){if(a.IsSucceed){if(t.locations=a.Data,t.locations.length&&e){var n=t.locations.find((function(e){return e.Is_Default}));n&&(t.form.Location_Id=n.Id)}}else t.$message({message:a.Message,type:"error"})}))},handleClose:function(){this.$emit("close")}}}},add3:function(e,t,a){"use strict";a("88ad")},bbd2:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},bcec:function(e,t,a){"use strict";a.r(t);var n=a("ff37"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},c618:function(e,t,a){"use strict";a.r(t);var n=a("f8b1a"),r=a("7759");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("e20d");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"b1203aa4",null);t["default"]=s.exports},c93b:function(e,t,a){},ccb1:function(e,t,a){"use strict";a.r(t);var n=a("a470"),r=a("2b0f");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("d57c");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"323fe1cf",null);t["default"]=s.exports},cf2c:function(e,t,a){"use strict";a.r(t);var n=a("2e097"),r=a("bcec");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("add3");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"1ab8b1e5",null);t["default"]=s.exports},d3d1:function(e,t,a){"use strict";a("6de4")},d3de:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card",style:e.isRetract?"height: 110px; overflow: hidden;":""},[a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"10px","padding-bottom":"10","border-bottom":"1px solid #D0D3DB"}},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("退库单信息")]),a("div",{staticClass:"retract-container",on:{click:e.handleRetract}},[a("el-button",{attrs:{type:"text"}},[e._v(e._s(e.isRetract?"展开":"收起"))]),a("el-button",{attrs:{type:"text",icon:e.isRetract?"el-icon- arrow-down":"el-icon-arrow-up"}})],1)]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退库日期",prop:"InStoreDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus,"picker-options":{disabledDate:function(t){return t.getTime()<new Date(e.statisticTime).getTime()}},"value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.InStoreDate,callback:function(t){e.$set(e.form,"InStoreDate",t)},expression:"form.InStoreDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus,rows:1,"show-word-limit":"",maxlength:100,placeholder:"备注",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退库部门",prop:"Return_Dept_Id"}},[a("SelectDepartment",{attrs:{disabled:e.isView||3===e.formStatus},model:{value:e.form.Return_Dept_Id,callback:function(t){e.$set(e.form,"Return_Dept_Id",t)},expression:"form.Return_Dept_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退库人",prop:"Return_Person_Id "}},[a("SelectDepartmentUser",{attrs:{"department-id":e.form.Return_Dept_Id},model:{value:e.form.Return_Person_Id,callback:function(t){e.$set(e.form,"Return_Person_Id",t)},expression:"form.Return_Person_Id"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:5,multiple:!0,"on-success":function(t,a,n){e.uploadSuccess(t,a,n)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"file-list":e.fileListData,"show-file-list":!0,disabled:e.isView||3===e.formStatus}},[e.isView||3===e.formStatus?e._e():a("el-button",{attrs:{type:"primary"}},[e._v("上传文件")])],1)],1)],1)],1)],1)],1),a("el-card",{staticClass:"box-card box-card-tb"},[a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("退库单明细")])]),a("el-divider",{staticClass:"elDivder"}),a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"0px"}},[a("div"),a("el-form",{ref:"searchForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm,"label-width":"80px"}},[e.filterVisible?e._e():[a("el-form-item",{staticClass:"cs-item",attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{placeholder:"通配符%",clearable:""},model:{value:e.searchForm.Raw_FullName,callback:function(t){e.$set(e.searchForm,"Raw_FullName",t)},expression:"searchForm.Raw_FullName"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Material","label-width":"50px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Material,callback:function(t){e.$set(e.searchForm,"Material",t)},expression:"searchForm.Material"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.SysProjectId,callback:function(t){e.$set(e.searchForm,"SysProjectId",t)},expression:"searchForm.SysProjectId"}},e._l(e.ProjectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],a("el-form-item",{staticClass:"last-btn",style:e.filterVisible?"width: 82px":""},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.filterVisible,expression:"!filterVisible"}],attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.filterVisible,expression:"!filterVisible"}],staticClass:"reset-btn",on:{click:e.handleReset}},[e._v("重置")]),a("el-popover",{attrs:{placement:"bottom",width:"1130"},model:{value:e.filterVisible,callback:function(t){e.filterVisible=t},expression:"filterVisible"}},[a("el-form",{ref:"searchAllForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm,"label-width":"120px"}},[a("el-form-item",{staticClass:"cs-item",attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{placeholder:"通配符%",clearable:""},model:{value:e.searchForm.Raw_FullName,callback:function(t){e.$set(e.searchForm,"Raw_FullName",t)},expression:"searchForm.Raw_FullName"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Material,callback:function(t){e.$set(e.searchForm,"Material",t)},expression:"searchForm.Material"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.SysProjectId,callback:function(t){e.$set(e.searchForm,"SysProjectId",t)},expression:"searchForm.SysProjectId"}},e._l(e.ProjectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"原料分类",prop:"CategoryId"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.searchForm.CategoryId,callback:function(t){e.$set(e.searchForm,"CategoryId",t)},expression:"searchForm.CategoryId"}})],1),1===e.BigType?a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Width,callback:function(t){e.$set(e.searchForm,"Width",t)},expression:"searchForm.Width"}})],1):e._e(),1===e.BigType||2===e.BigType?a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Length,callback:function(t){e.$set(e.searchForm,"Length",t)},expression:"searchForm.Length"}})],1):e._e(),a("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商",clearable:"",filterable:""},model:{value:e.searchForm.Supplier,callback:function(t){e.$set(e.searchForm,"Supplier",t)},expression:"searchForm.Supplier"}},e._l(e.SupplierList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnit"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"甲方单位",clearable:"",filterable:""},model:{value:e.searchForm.PartyUnit,callback:function(t){e.$set(e.searchForm,"PartyUnit",t)},expression:"searchForm.PartyUnit"}},e._l(e.PartyUnitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"仓库",prop:"WarehouseId"}},[a("SelectWarehouse",{model:{value:e.searchForm.WarehouseId,callback:function(t){e.$set(e.searchForm,"WarehouseId",t)},expression:"searchForm.WarehouseId"}})],1),a("el-form-item",{attrs:{label:"库位",prop:"LocationId"}},[a("SelectLocation",{attrs:{"warehouse-id":e.searchForm.WarehouseId},model:{value:e.searchForm.LocationId,callback:function(t){e.$set(e.searchForm,"LocationId",t)},expression:"searchForm.LocationId"}})],1),a("el-form-item",{staticClass:"last-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),a("el-button",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(e.filterVisible?"收起筛选":"展开筛选"))])],1)],1)],2)],1),a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"8px"}},[a("vxe-toolbar",{staticStyle:{display:"flex",width:"100%"},scopedSlots:e._u([{key:"buttons",fn:function(){return[e.isNesting?e._e():[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"primary",plain:""},on:{click:e.handleBatchEdit}},[e._v("批量编辑 ")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleWarehouse(!1)}}},[e._v("批量选择仓库/库位 ")])],a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.getTableColumns}})]},proxy:!0}],null,!1,2343798004)})],1),a("div",{staticClass:"tb-x"},[a(e.currentTbComponent,{ref:"table",tag:"component",attrs:{"is-view":e.isView,"big-type-data":e.BigType,"check-type-list":e.checkTypeList,"is-replace-purchase":e.form.Is_Replace_Purchase,"project-list":e.ProjectList,"supplier-list":e.SupplierList,"party-unit-list":e.PartyUnitList,"form-status":e.formStatus||0},on:{changeStandard:e.changeStandard,changeWarehouse:e.changeWarehouse,select:e.setSelectRow,updateTb:e.handleUpdateTb,updateRow:e.handleUpdateRow}})],1),e.isView?e._e():a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("footer",[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("div",[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),3===e.formStatus||e.isNesting?e._e():a("el-button",{attrs:{loading:e.saveLoading},on:{click:function(t){return e.handleSaveDraft()}}},[e._v("保存草稿 ")]),a("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:e.handleSubmit}},[e._v("提交退库")])],1)])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":0,"project-list":e.ProjectList,"supplier-list":e.SupplierList,"party-unit-list":e.PartyUnitList,"is-replace-purchase":e.form.Is_Replace_Purchase,"is-receive":e.isReceive,"check-type-list":e.checkTypeList},on:{close:e.handleClose,warehouse:e.getWarehouse,batchEditor:e.batchEditorFn,importData:e.importData,standard:e.getStandard,refresh:e.fetchData}})],1):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增原料",visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[e.isReceive?a("AddList2",{ref:"draft",attrs:{"is-single":e.isSingle,"big-type-data":e.BigType,"form-data":e.form,"project-list":e.ProjectList,"joined-items":e.rootTableData},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}}):a("AddRawMaterialList",{ref:"draft",attrs:{"is-single":e.isSingle},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2),e.dialogRepeatVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"检查重复",visible:e.dialogRepeatVisible,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.dialogRepeatVisible=t},close:e.handleClose}},[a("Repeat",{ref:"repeat",attrs:{columns:e.rootColumns},on:{submit:function(t){return e.submitInfo(1,!0)},close:e.handleClose}})],1):e._e()],1)},r=[]},d450:function(e,t,a){"use strict";a.r(t);var n=a("4952"),r=a("13da0");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("e854");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"df666d9a",null);t["default"]=s.exports},d56b:function(e,t,a){},d57c:function(e,t,a){"use strict";a("ef352")},ddaa:function(e,t,a){"use strict";a("161f")},e20d:function(e,t,a){"use strict";a("51e7")},e41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=c,t.GetPartsList=s,t.GetProjectAreaTreeList=i,t.ImportParts=u,t.SaveProjectAreaSort=o;var r=n(a("b775"));function i(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},e854:function(e,t,a){"use strict";a("77f0")},e9fc:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("841c");var o=a("ed08"),s=a("5480"),l=a("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},ec55:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("d3b7");var r=n(a("3796")),i=a("ed08"),o=a("93aa");t.default={components:{Upload:r.default},data:function(){return{btnLoading:!1,schdulingPlanId:""}},inject:["formData"],mounted:function(){},methods:{getTemplate:function(){var e=this;(0,o.GetImportTemplate)({inStoreType:this.formData.InStoreType}).then((function(t){window.open((0,i.combineURL)(e.$baseUrl,t.Data))}))},beforeUpload:function(e){var t=this,a={In_Store_Type:this.formData.InStoreType,Is_Replace_Purchase:this.formData.Is_Replace_Purchase,SysProjectId:this.formData.SysProjectId,PartyUnit:this.formData.PartyUnit,Supplier:this.formData.Supplier},n=new FormData;n.append("Receipt",JSON.stringify(a)),n.append("Files",e),this.btnLoading=!0,(0,o.Import)(n).then((function(e){if(e.IsSucceed){var a=e.Data?e.Data.map((function(e){return e.TaxUnitPrice=Number((e.TaxUnitPrice/1e3).toFixed(3)),e.TaxUnitPriceKG=e.TaxUnitPrice,e})):[];t.$emit("getAddList",a),t.$message({type:"success",message:"导入成功"}),t.$emit("close")}else t.$message({type:"error",message:e.Message}),e.Data&&window.open((0,i.combineURL)(t.$baseUrl,e.Data));t.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},edce:function(e,t,a){"use strict";a.r(t);var n=a("ad97"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},ef352:function(e,t,a){},ef52:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),i=n(a("c14f")),o=n(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("ddb0");var s=a("ed08"),l=a("5480"),u=a("93aa"),c=n(a("1463")),d=n(a("333d")),f=a("c685");t.default={components:{TreeDetail:c.default,Pagination:d.default},props:{isSingle:{type:Boolean,default:!1}},data:function(){return{tablePageSize:f.tablePageSize,treeLoading:!1,expandedKey:"",treeData:[],Category_Id:"",catalogDetail:{},form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,queryInfo:{Page:1,PageSize:20,ParameterJson:[]},total:0,columns:[],currentColumns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{isSteelPlate:function(){var e,t=this,a=this.catalogDetail.Pid?null===(e=this.treeData.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";return"板材"===this.catalogDetail.Name||"板材"===a}},watch:{isSteelPlate:function(){var e,t=this,a=this.catalogDetail.Pid?null===(e=this.treeData.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";this.currentColumns=this.columns.filter((function(e){return"板材"===t.catalogDetail.Name||"板材"===a?"Spec"!==e.Code:"Thick"!==e.Code}))},showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getConfig();case 1:return t.n=2,e.getTreeList();case 2:e.search=(0,s.debounce)(e.fetchData,800,!0);case 3:return t.a(2)}}),t)})))()},methods:{getTreeList:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,t.n=1,(0,u.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeData=t.Data,e.expandedKey=t.Data[0].Id,e.Category_Id=t.Data[0].Id,e.catalogDetail=t.Data[0].Data.Category,e.fetchData()):e.$message({message:t.Message,type:"error"}),e.treeLoading=!1})).catch((function(){e.treeLoading=!1}));case 1:return t.a(2)}}),t)})))()},handleNodeClick:function(e){this.Category_Id=e.Id,this.catalogDetail=e.Data.Category,this.queryInfo.Page=1,this.fetchData()},setRow:function(e){},getConfig:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PRORawList");case 1:e.columns=t.v;case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},searchReset:function(){this.form.Name="",this.fetchData()},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,u.GetRawPageList)((0,r.default)({Category_Id:e.Category_Id,Param:e.form.Name},e.queryInfo)).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data.filter((function(e){return e.Is_Enabled})),e.total=t.Data.TotalCount,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){var t=e.Versions.find((function(e){return e.Is_System}));return e.Version_Id=t.Id,e.Specific_Gravity=t.Specific_Gravity,e.BigType=e.Big_Type,e.RawCode=e.Code,e.CategoryId=e.Category_Id,e.RawName=e.Name,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")},changePage:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=20),Promise.all([e.fetchData()]).then((function(e){}));case 1:return t.a(2)}}),t)})))()}}}},f8b1a:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.list,(function(t,n){return a("el-row",{key:t.id,staticClass:"item-x"},[a("div",{staticClass:"item"},[a("label",[e._v(" 属性名称 "),a("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.key,callback:function(a){e.$set(t,"key",a)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),a("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[a("label",[e._v("请输入值 "),e.checkType(t.key,"number")?a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?a("el-input",{model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"supplierArray")?a("el-select",{attrs:{placeholder:"请选择供应商",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"supplierArray")}},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.supplierList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"partyUnitArray")?a("el-select",{attrs:{placeholder:"请选择甲方单位",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"partyUnitArray")}},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.partyUnitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"projectArray")?a("el-select",{attrs:{placeholder:"请选择所属项目",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"projectArray")}},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1):e._e(),e.checkType(t.key,"Is_Surplus")?a("el-select",{attrs:{placeholder:"请选择是否余料",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"Is_Surplus")}},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1):e._e()],1)]),a("span",{staticClass:"item-span"},0===n?[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),a("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(n)}}})])])})),a("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},r=[]},fdfa:function(e,t,a){"use strict";a.r(t);var n=a("06b4"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},fee8:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("vxe-table",{key:2,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"500","show-overflow":"","empty-text":"无重复项！","row-style":e.rowStyle,"auto-resize":!0,size:"medium",data:e.resultArray,resizable:"","scroll-y":{enabled:!0},"tooltip-config":{enterable:!0}}},[a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var r=n.row;return[a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]}}],null,!0)})]}))],2),a("div",{staticClass:"footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("不合并手动处理")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.submit}},[e._v("合并后提交")])],1)],1)},r=[]},ff37:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b"),a("14d9"),a("13d5"),a("e9f5"),a("9485"),a("d3b7");var n=a("2245");t.default={data:function(){return{activeName:"1",radioSelect:null,btnLoading:!1,form:{},list:[],tableData:[],specificationUsage:0}},watch:{specificationUsage:function(e,t){1===e&&(this.activeName="2")}},methods:{getOption:function(e){var t=this;this.specificationUsage=e.SpecificationUsage,e&&(this.list=e.RcptRawParams.split("*")),(0,n.GetRawStandardsList)({rawId:e.RawId}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleRadioChange:function(e,t){e.stopPropagation(),this.currentRow=Object.assign({},t)},submit:function(){var e=this;if("1"===this.activeName){var t=!0,a=this.list.reduce((function(a,n){if(e.form[n])return a.push(e.form[n]),a;t=!1}),[]);if(!t)return void this.$message({message:"输入数据不能为0",type:"warning"});this.$emit("standard",{type:1,val:a.join("*")})}else{if(!this.currentRow)return void this.$message({message:"请选择规格",type:"warning"});this.$emit("standard",{type:2,val:this.currentRow})}this.handleClose()},handleClose:function(){this.$emit("close")}}}}}]);