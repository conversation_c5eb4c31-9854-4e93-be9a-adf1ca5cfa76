(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-262c9545"],{"0d9a":function(t,e,r){"use strict";var n=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddArea=ot,e.AddDeepFile=tt,e.AddMonomer=Q,e.AreaDelete=at,e.AreaGetEntity=rt,e.AttachmentGetEntities=U,e.ChangeLoad=b,e.CommonImportDeependToComp=$,e.ContactsAdd=k,e.ContactsDelete=I,e.ContactsEdit=R,e.ContactsGetEntities=F,e.ContactsGetEntity=Y,e.ContactsGetTreeList=x,e.DeleteMonomer=Z,e.DepartmentAdd=T,e.DepartmentDelete=L,e.DepartmentEdit=A,e.DepartmentGetEntities=j,e.DepartmentGetEntity=E,e.DepartmentGetList=M,e.EditArea=ut,e.EditMonomer=W,e.FileAdd=S,e.FileAddType=f,e.FileDelete=y,e.FileEdit=v,e.FileGetEntity=m,e.FileHistory=G,e.FileMove=_,e.FileTypeAdd=s,e.FileTypeDelete=d,e.FileTypeEdit=p,e.FileTypeGetEntities=u,e.FileTypeGetEntity=a,e.GeAreaTrees=nt,e.GetAreaTreeList=et,e.GetDictionaryDetailListByCode=O,e.GetEntitiesByRecordId=B,e.GetEntitiesProject=c,e.GetFileCatalog=i,e.GetFilesByType=l,e.GetGetMonomerList=V,e.GetLoadingFiles=g,e.GetMonomerEntity=X,e.GetPartCodeList=P,e.GetProMonomerList=K,e.GetProjectComponentCodeList=h,e.GetProjectEntity=it,e.GetProjectsflowmanagementAdd=N,e.GetProjectsflowmanagementEdit=z,e.GetProjectsflowmanagementInfo=H,e.GetShortUrl=q,e.ImportDeependToSteel=J,e.ImportPartList=ct,e.SysuserGetUserEntity=w,e.SysuserGetUserList=C,e.UserGroupTree=D;var o=n(r("b775"));function u(t){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:t})}function a(t){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:t})}function i(t){return(0,o.default)({url:"/SYS/Sys_FileType/GetFileCatalog",method:"post",data:t})}function c(t){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntitiesProject",method:"post",data:t})}function l(t){return(0,o.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:t})}function d(t){return(0,o.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:t})}function s(t){return(0,o.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:t})}function f(t){return(0,o.default)({url:"/SYS/Sys_FileType/AddType",method:"post",data:t})}function p(t){return(0,o.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:t})}function m(t){return(0,o.default)({url:"/SYS/Sys_File/GetEntity",method:"post",data:t})}function y(t){return(0,o.default)({url:"/SYS/Sys_File/Delete",method:"post",data:t})}function h(t){return(0,o.default)({url:"/pro/component/GetProjectComponentCodeList",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Part/GetPartCodeList",method:"post",data:t})}function S(t){return(0,o.default)({url:"/SYS/Sys_File/Add",method:"post",data:t})}function v(t){return(0,o.default)({url:"/SYS/Sys_File/Edit",method:"post",data:t})}function _(t){return(0,o.default)({url:"/SYS/Sys_File/Move",method:"post",data:t})}function b(t){return(0,o.default)({url:"/SYS/Sys_File/IsLoad",method:"post",data:t})}function G(t){return(0,o.default)({url:"/SYS/Sys_File/OldFile",method:"post",data:t})}function g(t){return(0,o.default)({url:"/SYS/Sys_File/GetBIMList",method:"post",data:t})}function E(t){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetEntity",method:"post",data:t})}function j(t){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetEntities",method:"post",data:t})}function L(t){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Delete",method:"post",data:t})}function A(t){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Edit",method:"post",data:t})}function T(t){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Add",method:"post",data:t})}function M(t){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/SYS/User/GetUserList",method:"post",data:t})}function w(t){return(0,o.default)({url:"/SYS/User/GetUserEntity",method:"post",data:t})}function D(t){return(0,o.default)({url:"/SYS/UserGroup/GetChildGroupTree",method:"post",data:t})}function O(t){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function Y(t){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetEntity",method:"post",data:t})}function F(t){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:t})}function x(t){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:t})}function I(t){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Delete",method:"post",data:t})}function R(t){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Edit",method:"post",data:t})}function k(t){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Add",method:"post",data:t})}function U(t){return(0,o.default)({url:"/SYS/Sys_File/GetAttachmentEntities",method:"post",data:t})}function B(t){return(0,o.default)({url:"/SYS/Sys_File/GetEntitiesByRecordId",method:"post",data:t})}function N(t){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/Add",method:"post",data:t})}function z(t){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/Edit",method:"post",data:t})}function H(t){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/GetEntity",method:"post",data:t})}function q(t){return(0,o.default)({url:"/PLM/XModel/GetShortUrl",method:"post",data:t})}function J(t){return(0,o.default)({url:"/plm/component/ImportDeependToSteel",method:"get",params:t})}function V(t){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetGetMonomerList",method:"post",data:t})}function X(t){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetMonomerEntity",method:"post",data:t})}function K(t){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetProMonomerList",method:"post",data:t})}function Q(t){return(0,o.default)({url:"/PLM/Plm_Project_Areas/AddMonomer",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PLM/Plm_Project_Areas/EditMonomer",method:"post",data:t})}function Z(t){return(0,o.default)({url:"/PLM/Plm_Project_Areas/DeleteMonomer",method:"post",data:t})}function $(t){return(0,o.default)({url:"/plm/MaterialInfo/CommonImportDeependToComp",method:"get",params:t})}function tt(t){return(0,o.default)({url:"/PLM/Component/AddDeepFile",method:"post",data:t,timeout:18e5})}function et(t){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetAreaTreeList",method:"post",data:t})}function rt(t){return(0,o.default)({url:"/PRO/Project/GetArea",method:"post",data:t})}function nt(t){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function ot(t){return(0,o.default)({url:"/PRO/Project/AddArea",method:"post",data:t})}function ut(t){return(0,o.default)({url:"/PRO/Project/EditArea",method:"post",data:t})}function at(t){return(0,o.default)({url:"/PRO/Project/Delete",method:"post",data:t})}function it(t){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:t})}function ct(t){return(0,o.default)({url:"/PRO/Part/ImportPartList",method:"post",data:t})}},"361f":function(t,e,r){"use strict";var n=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ContactsAdd=b,e.GetDictionaryDetailListByCode=a,e.GetEPCProjectList=u,e.GetOrganizationalInfo=g,e.GetOrganizationalList=G,e.GetPlmProjectAdd=p,e.GetPlmProjectAreaAdd=i,e.GetPlmProjectAreaDelete=d,e.GetPlmProjectAreaEdit=l,e.GetPlmProjectAreaGetEntities=f,e.GetPlmProjectAreaGetEntity=s,e.GetPlmProjectDelete=y,e.GetPlmProjectEdit=m,e.GetPlmProjectGetEntities=P,e.GetPlmProjectGetEntity=h,e.GetProjectChecking=v,e.GroupList=S,e.ProjectImport=c,e.getUserList=_;var o=n(r("b775"));function u(t){return(0,o.default)({url:"/EPC/Project/GetProjectList",method:"post",data:t})}function a(t){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PLM/Plm_Project_Area/Add",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PLM/Plm_Projects/ProjectImport",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PLM/Plm_Project_Area/Edit",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PLM/Plm_Project_Area/Delete",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PLM/Plm_Project_Area/GetEntity",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PLM/Plm_Project_Area/GetEntities",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PLM/Plm_Projects/Add",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PLM/Plm_Projects/Edit",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PLM/Plm_Projects/Delete",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PLM/Plm_Projects/GetInfo",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PLM/Plm_Projects/GetEntities",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PLM/Plm_Projects/GroupList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PLM/Plm_Projects/GetProjectChecking",method:"post",data:t})}function _(t){return(0,o.default)({url:"/SYS/User/GetUserList",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PLM/Plm_Projects/ProjectContactsAdd",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PLM/Plm_Projects/GetOrganizationalList",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PLM/Plm_Projects/GetOrganizationalInfo",method:"post",data:t})}},8325:function(t,e,r){"use strict";var n=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(r("fc6f")),u=function(t){t.directive("Clipboard",o.default)};window.Vue&&(window.clipboard=o.default,Vue.use(u)),o.default.install=u;e.default=o.default},b311:function(t,e,r){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
(function(e,r){t.exports=r()})(0,(function(){return function(){var t={686:function(t,e,r){"use strict";r.d(e,{default:function(){return D}});var n=r(279),o=r.n(n),u=r(370),a=r.n(u),i=r(817),c=r.n(i);function l(t){try{return document.execCommand(t)}catch(e){return!1}}var d=function(t){var e=c()(t);return l("cut"),e},s=d;function f(t){var e="rtl"===document.documentElement.getAttribute("dir"),r=document.createElement("textarea");r.style.fontSize="12pt",r.style.border="0",r.style.padding="0",r.style.margin="0",r.style.position="absolute",r.style[e?"right":"left"]="-9999px";var n=window.pageYOffset||document.documentElement.scrollTop;return r.style.top="".concat(n,"px"),r.setAttribute("readonly",""),r.value=t,r}var p=function(t,e){var r=f(t);e.container.appendChild(r);var n=c()(r);return l("copy"),r.remove(),n},m=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},r="";return"string"===typeof t?r=p(t,e):t instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null===t||void 0===t?void 0:t.type)?r=p(t.value,e):(r=c()(t),l("copy")),r},y=m;function h(t){return h="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}var P=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.action,r=void 0===e?"copy":e,n=t.container,o=t.target,u=t.text;if("copy"!==r&&"cut"!==r)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==h(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===r&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===r&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return u?y(u,{container:n}):o?"cut"===r?s(o):y(o,{container:n}):void 0},S=P;function v(t){return v="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function _(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function b(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function G(t,e,r){return e&&b(t.prototype,e),r&&b(t,r),t}function g(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&E(t,e)}function E(t,e){return E=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},E(t,e)}function j(t){var e=T();return function(){var r,n=M(t);if(e){var o=M(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return L(this,r)}}function L(t,e){return!e||"object"!==v(e)&&"function"!==typeof e?A(t):e}function A(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function T(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function M(t){return M=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},M(t)}function C(t,e){var r="data-clipboard-".concat(t);if(e.hasAttribute(r))return e.getAttribute(r)}var w=function(t){g(r,t);var e=j(r);function r(t,n){var o;return _(this,r),o=e.call(this),o.resolveOptions(n),o.listenClick(t),o}return G(r,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof t.action?t.action:this.defaultAction,this.target="function"===typeof t.target?t.target:this.defaultTarget,this.text="function"===typeof t.text?t.text:this.defaultText,this.container="object"===v(t.container)?t.container:document.body}},{key:"listenClick",value:function(t){var e=this;this.listener=a()(t,"click",(function(t){return e.onClick(t)}))}},{key:"onClick",value:function(t){var e=t.delegateTarget||t.currentTarget,r=this.action(e)||"copy",n=S({action:r,container:this.container,target:this.target(e),text:this.text(e)});this.emit(n?"success":"error",{action:r,text:n,trigger:e,clearSelection:function(){e&&e.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(t){return C("action",t)}},{key:"defaultTarget",value:function(t){var e=C("target",t);if(e)return document.querySelector(e)}},{key:"defaultText",value:function(t){return C("text",t)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return y(t,e)}},{key:"cut",value:function(t){return s(t)}},{key:"isSupported",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],e="string"===typeof t?[t]:t,r=!!document.queryCommandSupported;return e.forEach((function(t){r=r&&!!document.queryCommandSupported(t)})),r}}]),r}(o()),D=w},828:function(t){var e=9;if("undefined"!==typeof Element&&!Element.prototype.matches){var r=Element.prototype;r.matches=r.matchesSelector||r.mozMatchesSelector||r.msMatchesSelector||r.oMatchesSelector||r.webkitMatchesSelector}function n(t,r){while(t&&t.nodeType!==e){if("function"===typeof t.matches&&t.matches(r))return t;t=t.parentNode}}t.exports=n},438:function(t,e,r){var n=r(828);function o(t,e,r,n,o){var u=a.apply(this,arguments);return t.addEventListener(r,u,o),{destroy:function(){t.removeEventListener(r,u,o)}}}function u(t,e,r,n,u){return"function"===typeof t.addEventListener?o.apply(null,arguments):"function"===typeof r?o.bind(null,document).apply(null,arguments):("string"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return o(t,e,r,n,u)})))}function a(t,e,r,o){return function(r){r.delegateTarget=n(r.target,e),r.delegateTarget&&o.call(t,r)}}t.exports=u},879:function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var r=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===r||"[object HTMLCollection]"===r)&&"length"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return"string"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e}},370:function(t,e,r){var n=r(879),o=r(438);function u(t,e,r){if(!t&&!e&&!r)throw new Error("Missing required arguments");if(!n.string(e))throw new TypeError("Second argument must be a String");if(!n.fn(r))throw new TypeError("Third argument must be a Function");if(n.node(t))return a(t,e,r);if(n.nodeList(t))return i(t,e,r);if(n.string(t))return c(t,e,r);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function a(t,e,r){return t.addEventListener(e,r),{destroy:function(){t.removeEventListener(e,r)}}}function i(t,e,r){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,r)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,r)}))}}}function c(t,e,r){return o(document.body,t,e,r)}t.exports=u},817:function(t){function e(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var r=t.hasAttribute("readonly");r||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),r||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var n=window.getSelection(),o=document.createRange();o.selectNodeContents(t),n.removeAllRanges(),n.addRange(o),e=n.toString()}return e}t.exports=e},279:function(t){function e(){}e.prototype={on:function(t,e,r){var n=this.e||(this.e={});return(n[t]||(n[t]=[])).push({fn:e,ctx:r}),this},once:function(t,e,r){var n=this;function o(){n.off(t,o),e.apply(r,arguments)}return o._=e,this.on(t,o,r)},emit:function(t){var e=[].slice.call(arguments,1),r=((this.e||(this.e={}))[t]||[]).slice(),n=0,o=r.length;for(n;n<o;n++)r[n].fn.apply(r[n].ctx,e);return this},off:function(t,e){var r=this.e||(this.e={}),n=r[t],o=[];if(n&&e)for(var u=0,a=n.length;u<a;u++)n[u].fn!==e&&n[u].fn._!==e&&o.push(n[u]);return o.length?r[t]=o:delete r[t],this}},t.exports=e,t.exports.TinyEmitter=e}},e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={exports:{}};return t[n](o,o.exports,r),o.exports}return function(){r.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return r.d(e,{a:e}),e}}(),function(){r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}}(),function(){r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),r(686)}().default}))},e41b:function(t,e,r){"use strict";var n=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteByIds=c,e.GetPartsImportTemplate=d,e.GetPartsList=i,e.GetProjectAreaTreeList=u,e.ImportParts=l,e.SaveProjectAreaSort=a;var o=n(r("b775"));function u(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function a(t){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:t})}},fc6f:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("d9e2");var n=r("b311");if(!n)throw new Error("you should npm install `clipboard` --save at first ");e.default={bind:function(t,e){if("success"===e.arg)t._v_clipboard_success=e.value;else if("error"===e.arg)t._v_clipboard_error=e.value;else{var r=new n(t,{text:function(){return e.value},action:function(){return"cut"===e.arg?"cut":"copy"}});r.on("success",(function(e){var r=t._v_clipboard_success;r&&r(e)})),r.on("error",(function(e){var r=t._v_clipboard_error;r&&r(e)})),t._v_clipboard=r}},update:function(t,e){"success"===e.arg?t._v_clipboard_success=e.value:"error"===e.arg?t._v_clipboard_error=e.value:(t._v_clipboard.text=function(){return e.value},t._v_clipboard.action=function(){return"cut"===e.arg?"cut":"copy"})},unbind:function(t,e){"success"===e.arg?delete t._v_clipboard_success:"error"===e.arg?delete t._v_clipboard_error:(t._v_clipboard.destroy(),delete t._v_clipboard)}}}}]);