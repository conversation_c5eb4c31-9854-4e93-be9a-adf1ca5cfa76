(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-d171a19a"],{"059e":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("h2",[e._v(e._s(e.Comp_Code))]),a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",data:e.tableData,border:"",stripe:"",resizable:"",size:"medium",loading:e.tbLoading,"show-overflow":"","row-config":{isHover:!0}}},[e._l(e.Columns,(function(t,n){return["是否主零件"==t.title?a("vxe-column",{key:n,attrs:{title:t.title,align:"left",sortable:"",width:"auto","min-width":t.width,field:t.field},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Is_Main?"是":"否")+" ")]}}],null,!0)}):a("vxe-column",{key:n,attrs:{sortable:"",align:"left",title:t.title,"min-width":t.width,field:t.field}})]}))],2)],1)},i=[]},"114a":function(e,t,a){},"14aa":function(e,t,a){"use strict";a.r(t);var n=a("66af"),i=a("fcd1");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("4e8e");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"98b2b856",null);t["default"]=l.exports},"14cd":function(e,t,a){"use strict";a.r(t);var n=a("4bb2"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"15fd":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,a("a4d3");var n=i(a("ccb5"));function i(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(null==e)return{};var a,i,r=(0,n.default)(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],-1===t.indexOf(a)&&{}.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}},"1ef4c":function(e,t,a){"use strict";a("c6a4")},"2f83":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("3166"),i=a("f2f6");t.default={data:function(){return{ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,"check-strictly":!1,filterable:!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},SetupPositionData:[]}},mounted:function(){this.getProjectOption()},methods:{getProjectOption:function(){var e=this;(0,n.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getAreaList:function(){var e=this;(0,n.GeAreaTrees)({projectId:this.form.Project_Id}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},getInstall:function(){var e=this;(0,i.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.SetupPositionData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},projectChange:function(e){var t=this;this.form.Area_Id="",this.treeParamsArea.data=[],this.$nextTick((function(e){t.$refs.treeSelectArea.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",e&&this.getAreaList()},areaChange:function(e){this.SetupPositionData=[],this.form.InstallUnit_Id="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.InstallUnit_Id=""}}}},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=u,t.GeAreaTrees=I,t.GetFileSync=k,t.GetInstallUnitIdNameList=y,t.GetNoBindProjectList=h,t.GetPartDeepenFileList=T,t.GetProjectAreaTreeList=P,t.GetProjectEntity=s,t.GetProjectList=l,t.GetProjectPageList=o,t.GetProjectTemplate=p,t.GetPushProjectPageList=b,t.GetSchedulingPartList=x,t.IsEnableProjectMonomer=d,t.SaveProject=c,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=_,t.UpdateProjectTemplateContract=v,t.UpdateProjectTemplateOther=C;var i=n(a("b775")),r=n(a("4328"));function o(e){return(0,i.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function c(e){return(0,i.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function d(e){return(0,i.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,i.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function h(e){return(0,i.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function p(e){return(0,i.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function _(e){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function C(e){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function b(e){return(0,i.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function P(e){return(0,i.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function y(e){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function I(e){return(0,i.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function T(e){return(0,i.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function x(e){return(0,i.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function k(e){return(0,i.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"3bb1":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2"),a("99af"),a("4de4"),a("7db0"),a("c740"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var i=n(a("c14f")),r=n(a("1da1")),o=n(a("15ac")),l=a("7f9d"),s=n(a("6612"));a("c3c6"),t.default={mixins:[o.default],props:{tbData:{type:Array,default:function(){return[]}}},data:function(){return{queryInfo:{Page:1,PageSize:-1},totalNum:0,totalWeight:0,tbLoading:!1,loading:!1,multipleSelection:[],columns:[],tableData:[],form:{}}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType}},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,e.getTableConfig("PROStartTransfer");case 1:e.isCom&&(a=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==a&&e.columns.splice(a,1)),e.tableData=JSON.parse(JSON.stringify(e.tbData)).filter((function(e){return e.Ready_Transfer_Count})).map((function(e){if(e.NextTeamAllocations&&e.NextTeamAllocations.length)if(e.inputMax=0,e.teamList=e.NextTeamAllocations.map((function(e,t){return{label:"".concat(e.Next_Team_Name,"(").concat(e.Can_Receive_Count,"件)"),value:e.Next_Team_Id,receiveCount:e.Can_Receive_Count}})).filter((function(e){return!!e.value})),1===e.teamList.length)e.Target_Team_Id=e.teamList[0].value,e.Team_Transfering_Count=e.inputMax=Math.min(e.teamList[0].receiveCount,e.Ready_Transfer_Count);else{var t=e.teamList.filter((function(e){return e.receiveCount>0}));1===t.length&&(e.Team_Transfering_Count=Math.min(t[0].receiveCount,e.Ready_Transfer_Count),e.Target_Team_Id=t[0].value)}else e.Team_Transfering_Count=e.inputMax=e.Ready_Transfer_Count,e.teamList=[];return e.checked=!1,e})),e.inputCHagne(),e.tbLoading=!1;case 2:return t.a(2)}}),t)})))()},methods:{tbSelectChange:function(e){this.multipleSelection=e.records},teamChange:function(e,t){var a=t.NextTeamAllocations.findIndex((function(t){return t.Next_Team_Id===e.value}));-1!==a&&(t.Team_Transfering_Count=t.inputMax=Math.min(t.NextTeamAllocations[a].Can_Receive_Count,t.Ready_Transfer_Count)),this.inputCHagne()},inputCHagne:function(){var e=this;try{this.totalNum=0;var t=0;this.tableData.forEach((function(a,n){a.Team_Transfering_Count||(a.Team_Transfering_Count=1);var i=a.Team_Transfering_Count;if(e.totalNum+=i,!a.Weight)throw new Error("重量不存在，统计失败");t=(0,s.default)(i).multiply(a.Weight).divide(1e3).add(t).value()})),this.totalWeight=(0,s.default)(t).format("0.[00]")}catch(a){this.$message({message:a,type:"error"})}},activeCellMethod:function(e){var t=e.row,a=e.column;e.columnIndex;return"Team_Transfering_Count"===a.field||"Next_Working_Team_Names"===a.field&&t.Next_Process_Code},handleRemove:function(e,t){this.tableData.splice(t,1),this.inputCHagne()},getLabel:function(e){if(!e.Target_Team_Id&&e.teamList.length>1)return"请选择";var t=e.teamList.find((function(t){return t.value===e.Target_Team_Id}));return(null===t||void 0===t?void 0:t.label)||""},save:function(){for(var e=this,t=0;t<this.tableData.length;t++){var a=this.tableData[t];if(!a.Target_Team_Id&&a.Next_Process_Code)return void this.$message({message:"班组不能为空",type:"error"})}this.$confirm("是否提交当前数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0,(0,l.TeamTaskTransfer)(e.tableData,{transferDate:e.form.transferDate}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh"),e.multipleSelection=[]):(e.$message({message:t.Message,type:"error"}),e.loading=!1)}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}},"4bb2":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),r=n(a("1da1")),o=n(a("ec1a")),l=n(a("a212"));t.default={components:{History:o.default,Transfer:l.default},inject:["pageType"],data:function(){return{pgLoading:!1,activeName:"Transfer",tabsActiveName:"1",styleObject:{transform:"translateX(".concat(0,")")}}},computed:{isTransfer:function(){return"Transfer"===this.activeName}},methods:{handleClick:function(e,t){var a=this;this.$nextTick((function(){a.$refs.content.setDataStatus(a.tabsActiveName)}))},changeTab:function(e){var t=this;return(0,r.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return t.pgLoading=!0,t.activeName=e,t.styleObject.transform="Transfer"===e?"translateX(".concat(0,")"):"translateX(".concat(140,"px)"),a.n=1,t.getTbConfigInfo();case 1:t.fetchData(1);case 2:return a.a(2)}}),a)})))()},getTbConfigInfo:function(){},fetchData:function(){}}}},"4cdc":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"inner-container"},[a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",{class:[e.activeCss?"active-input":"search-input "]},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:e.isCom?"构件名称":"零件名称"}},[a("el-input",{ref:"selectRef",attrs:{clearable:"",placeholder:"请输入(空格区分/多个搜索)"},model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{staticClass:"w100",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x w100",attrs:{disabled:!e.form.Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticClass:"w100",attrs:{disabled:!e.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:e.changeInstall},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"我的班组",prop:"Working_Team_Id"}},[a("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Working_Team_Id,callback:function(t){e.$set(e.form,"Working_Team_Id",t)},expression:"form.Working_Team_Id"}},e._l(e.teamOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"排产单号",prop:"Schduling_Code"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Schduling_Code,callback:function(t){e.$set(e.form,"Schduling_Code",t)},expression:"form.Schduling_Code"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1)],1),2==e.dataStatus?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"下道工序",prop:"Target_Process_Code"}},[a("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",clearable:""},on:{change:e.processChange},model:{value:e.form.Target_Process_Code,callback:function(t){e.$set(e.form,"Target_Process_Code",t)},expression:"form.Target_Process_Code"}},e._l(e.processOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Code}})})),1)],1)],1):e._e(),2==e.dataStatus?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"制造班组",prop:"Next_Team_Id"}},[a("el-select",{staticClass:"w100",attrs:{disabled:!e.form.Target_Process_Code,clearable:"",placeholder:"请选择"},model:{value:e.form.Next_Team_Id,callback:function(t){e.$set(e.form,"Next_Team_Id",t)},expression:"form.Next_Team_Id"}},e._l(e.groupOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),a("el-col",{attrs:{span:6}},[a("el-form-item",{key:2,attrs:{"label-width":"80px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1)],1)],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"btn-wrapper"},[1==e.dataStatus?a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:e.handleStart}},[e._v("开始加工")]):e._e(),3==e.dataStatus?a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:e.handleCheck}},[e._v("报检")]):e._e(),2==e.dataStatus?a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:e.handleTransfer}},[e._v("报工转移")]):e._e()],1),a("div",{staticClass:"fff tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",height:"100%","show-overflow":"",loading:e.tbLoading,"checkbox-config":{checkField:"checked"},"row-config":{isCurrent:!0,isHover:!0},stripe:"",border:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.filterColumns,(function(t){return["Prepare_Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"title-help":{message:"当前生产完成零件可加工该构件该工序的数量",icon:"vxe-icon-question-circle-fill"},"min-width":t.Width,align:t.Align,sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.openPartDetail(n)}}},[e._v(" "+e._s(n.Prepare_Count)+" ")])]}}],null,!0)}):"Create_Date"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,sortable:"",align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}],null,!0)}):"Comp_Code"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,align:t.Align,title:e.isCom?t.Display_Name:"所属构件","min-width":t.Width,sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Comp_Code)+" ")]}}],null,!0)}):"Finish_Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:4==e.dataStatus?"转移完成(件)":"已转移","min-width":t.Width,sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("displayValue")(a.Finish_Count))+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:["Task_Code","Comp_Code","Part_Code"].includes(t.Code)?"left":"",field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,"min-width":t.Width}})]}))],2)],1),a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据")]),e.tipLabel?a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v(e._s(e.tipLabel))]):e._e()],1),a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)]),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"80%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"tb-data":e.multipleSelection},on:{close:e.handleClose,refresh:function(t){return e.fetchData(1)}}})],1):e._e()],1)},i=[]},"4e8e":function(e,t,a){"use strict";a("a436")},"58e9":function(e,t,a){"use strict";a.r(t);var n=a("b6c1"),i=a("ac4e");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("65d2");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"c138203a",null);t["default"]=l.exports},"64b2":function(e,t,a){"use strict";a("e815")},"65d2":function(e,t,a){"use strict";a("9913")},"66af":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"container h100"},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"转出数量:"}},[e._v(" "+e._s(e.totalNum)+" ")]),a("el-form-item",{attrs:{label:"转出重量:"}},[e._v(" "+e._s(e.totalWeight)+"(t) ")]),a("el-form-item",{attrs:{label:"转移时间:"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"date","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.transferDate,callback:function(t){e.$set(e.form,"transferDate",t)},expression:"form.transferDate"}})],1)],1),a("div",{staticClass:"tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"checkbox-config":{checkField:"checked"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tableData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e._l(e.columns,(function(t){return["Team_Transfering_Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto",fixed:"right","edit-render":{},"min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.inputMax},on:{change:e.inputCHagne},model:{value:n.Team_Transfering_Count,callback:function(t){e.$set(n,"Team_Transfering_Count",e._n(t))},expression:"row.Team_Transfering_Count"}})]}},{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("displayValue")(a.Team_Transfering_Count))+" ")]}}],null,!0)}):"Next_Working_Team_Names"===t.Code?a("vxe-column",{key:t.Code,attrs:{fixed:"right",field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto","edit-render":{},"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[n.Next_Process_Code?a("span",{style:{color:"请选择"===e.getLabel(n)?"red":""}},[e._v(e._s(e.getLabel(n)))]):e._e()]}},{key:"edit",fn:function(t){var n=t.row;return[a("vxe-select",{on:{change:function(t){return e.teamChange(t,n)}},model:{value:n.Target_Team_Id,callback:function(t){e.$set(n,"Target_Team_Id",t)},expression:"row.Target_Team_Id"}},e._l(n.teamList,(function(e){return a("vxe-option",{key:e.value,attrs:{value:e.value,label:e.label}})})),1)]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto",fixed:"Part_Code"===t.Code||"Comp_Code"===t.Code?"left":"","min-width":t.Width}})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"120","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,i=t.$rowIndex;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleRemove(n,i)}}},[e._v("移除")])]}}])})],2)],1),a("footer",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.tableData.length,loading:e.loading},on:{click:e.save}},[e._v("确定")])],1)],1)},i=[]},7808:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2"),a("4de4"),a("c740"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var i=n(a("c14f")),r=n(a("1da1")),o=n(a("15ac")),l=a("7f9d"),s=n(a("6612"));t.default={mixins:[o.default],props:{tbData:{type:Array,default:function(){return[]}}},data:function(){return{queryInfo:{Page:1,PageSize:-1},total:0,tbLoading:!1,multipleSelection:[],columns:[],tableData:[],totalNum:void 0,totalWeight:void 0,loading:!1}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType}},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,e.getTableConfig("PROStartProcess");case 1:e.isCom&&(a=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==a&&e.columns.splice(a,1)),e.tableData=JSON.parse(JSON.stringify(e.tbData)),e.setData(),e.tbLoading=!1;case 2:return t.a(2)}}),t)})))()},methods:{setData:function(){this.tableData=this.tableData.filter((function(e){return e.Ready_Process_Count})).map((function(e){return e.Processing_Count=e.Ready_Process_Count,e.checked=!1,e})),this.inputChange()},inputChange:function(){var e=this;try{this.totalNum=0;var t=0;this.tableData.forEach((function(a,n){a.Processing_Count||(a.Processing_Count=1);var i=a.Processing_Count;if(e.totalNum+=i,!a.Weight)throw new Error("重量不存在，统计失败");t=(0,s.default)(i).multiply(a.Weight).divide(1e3).add(t).value()})),this.totalWeight=(0,s.default)(t).format("0.[00]")}catch(a){this.$message({message:a,type:"error"})}},tbSelectChange:function(e){this.multipleSelection=e.records},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Processing_Count"===t.field},save:function(){var e=this;this.$confirm("是否提交当前数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0,(0,l.TeamTaskProcessing)(e.tableData).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):(e.$message({message:t.Message,type:"error"}),e.loading=!1)}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}},7939:function(e,t,a){"use strict";a.r(t);var n=a("d14b"),i=a("14cd");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("1ef4c");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"a16c42fc",null);t["default"]=l.exports},"79f9":function(e,t,a){"use strict";a("114a")},"7e994":function(e,t,a){"use strict";a.r(t);var n=a("cfa9"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"8c31":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"container h100"},[a("el-form",{ref:"form",attrs:{inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"加工数量:"}},[e._v(" "+e._s(e.totalNum)+" ")]),a("el-form-item",{attrs:{label:"加工重量:"}},[e._v(" "+e._s(e.totalWeight)+"(t) ")])],1),a("div",{staticClass:"tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"checkbox-config":{checkField:"checked"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tableData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e._l(e.columns,(function(t){return["Processing_Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"","edit-render":{},fixed:"right",align:t.Align,width:"auto","min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.Ready_Process_Count},on:{change:e.inputChange},model:{value:n.Processing_Count,callback:function(t){e.$set(n,"Processing_Count",e._n(t))},expression:"row.Processing_Count"}})]}},{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("displayValue")(a.Processing_Count))+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto",fixed:"Part_Code"===t.Code||"Comp_Code"===t.Code?"left":"","min-width":t.Width}})]}))],2)],1),a("footer",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.tableData.length,loading:e.loading},on:{click:e.save}},[e._v("确定")])],1)],1)},i=[]},9913:function(e,t,a){},"996a":function(e,t,a){"use strict";a.r(t);var n=a("8c31"),i=a("ecd0");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("79f9");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"7272f6ae",null);t["default"]=l.exports},"9d6d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2"),a("4de4"),a("c740"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var i=n(a("c14f")),r=n(a("1da1")),o=n(a("15ac")),l=a("7f9d"),s=n(a("6612"));t.default={mixins:[o.default],props:{tbData:{type:Array,default:function(){return[]}}},data:function(){return{queryInfo:{Page:1,PageSize:-1},total:0,tbLoading:!1,loading:!1,multipleSelection:[],columns:[],tableData:[],totalNum:void 0,totalWeight:void 0}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType}},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,e.getTableConfig("PROCheckList");case 1:e.isCom&&(a=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==a&&e.columns.splice(a,1)),e.tableData=JSON.parse(JSON.stringify(e.tbData)),e.setData(),e.tbLoading=!1;case 2:return t.a(2)}}),t)})))()},methods:{setData:function(){this.tableData=this.tableData.filter((function(e){return e.Ready_Check_Count})).map((function(e){return e.Team_Check_Count=e.Ready_Check_Count,e.checked=!1,e})),this.inputChange()},inputChange:function(){var e=this;try{this.totalNum=0;var t=0;this.tableData.forEach((function(a,n){a.Team_Check_Count||(a.Team_Check_Count=1);var i=a.Team_Check_Count;if(e.totalNum+=i,!a.Weight)throw new Error("重量不存在，统计失败");t=(0,s.default)(i).multiply(a.Weight).divide(1e3).add(t).value()})),this.totalWeight=(0,s.default)(t).format("0.[00]")}catch(a){this.$message({message:a,type:"error"})}},tbSelectChange:function(e){this.multipleSelection=e.records},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Team_Check_Count"===t.field},save:function(){var e=this;this.$confirm("是否提交当前数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0,(0,l.ApplyCheck)(e.tableData).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):(e.$message({message:t.Message,type:"error"}),e.loading=!1)}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}},a212:function(e,t,a){"use strict";a.r(t);var n=a("4cdc"),i=a("df3f");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("64b2");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"b53d562e",null);t["default"]=l.exports},a2bb:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("5530")),r=n(a("15fd")),o=n(a("c14f")),l=n(a("1da1"));a("4de4"),a("7db0"),a("c740"),a("caad"),a("a15b"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("a9e3"),a("d3b7"),a("ac1f"),a("841c");var s=a("7f9d"),c=n(a("15ac")),u=a("ed08"),d=n(a("333d")),f=n(a("996a")),m=n(a("14aa")),h=n(a("a8e2")),p=n(a("58e9")),g=n(a("2f83")),_=a("a024"),v=a("c685"),C=["Comp_Codes","Part_Code"];t.default={components:{Pagination:d.default,Transfer:m.default,CheckDialog:p.default,Process:f.default,CompleteSet:h.default},mixins:[c.default,g.default],data:function(){return{tablePageSize:v.tablePageSize,gridData:[{date:"2016-05-03",name:"王小虎",address:"上海市普"},{date:"2016-05-03",name:"王小虎",address:"上海市普"},{date:"2016-05-03",name:"王小虎",address:"上海市普"},{date:"2016-05-03",name:"王小虎",address:"上海市普"}],tbLoading:!1,dialogVisible:!1,currentComponent:"",tipLabel:"",title:"",processOption:[],teamOptions:[],groupOption:[],columns:[],multipleSelection:[],tbData:[],tbConfig:{},queryInfo:{Page:1,PageSize:20},total:0,form:{Spec:"",Task_Code:"",Schduling_Code:"",Target_Process_Code:"",Comp_Codes:"",Part_Code:"",Working_Team_Id:"",Next_Team_Id:""},rules:{},search:function(){return{}},selectHight:0,activeCss:!1,dataStatus:1}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType},searchInput:{get:function(){return this.isCom?this.form.Comp_Codes:this.form.Part_Code},set:function(e){this.isCom?this.form.Comp_Codes=e:this.form.Part_Code=e}},filterColumns:function(){return 1==this.dataStatus?this.columns.filter((function(e){return!["待质检","质检中","已合格","待转移数(件)","转移中(件)","转移完成(件)","下道工序","下道班组","已转移"].includes(e.Display_Name)})):2==this.dataStatus?this.columns.filter((function(e){return!["已接收(件)","待加工(件)","待质检","质检中","已合格","零件齐套数","已加工","变更数量"].includes(e.Display_Name)})):3==this.dataStatus?this.columns.filter((function(e){return!["待加工(件)","待转移数(件)","转移中(件)","转移完成(件)","已接收(件)","零件齐套数","下道工序","下道班组","已转移","变更数量"].includes(e.Display_Name)})):this.columns.filter((function(e){return!["待加工(件)","待转移数(件)","转移中(件)","已接收(件)","零件齐套数","待质检","质检中","已合格","已加工","变更数量"].includes(e.Display_Name)}))}},mounted:function(){var e=this;return(0,l.default)((0,o.default)().m((function t(){var a,n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("PROStartProcessList");case 1:return e.isCom?(a=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==a&&e.columns.splice(a,1)):(n=e.columns.findIndex((function(e){return"Prepare_Count"===e.Code})),-1!==n&&e.columns.splice(n,1)),e.search=(0,u.debounce)(e.fetchData,800,!0),t.n=2,e.getProcessTeam();case 2:e.fetchData(),e.getProcessOption();case 3:return t.a(2)}}),t)})))()},methods:{fetchData:function(e){var t=this;this.tbLoading=!0,e&&(this.queryInfo.Page=e);var a=this.form,n=a.Comp_Codes,o=a.Part_Code,l=(0,r.default)(a,C),c=(0,i.default)((0,i.default)((0,i.default)({Working_Team_Id:"",Process_Type:this.isCom?2:1},l),this.queryInfo),{},{Data_Status:this.dataStatus});this.form.Working_Team_Id||(c.Working_Team_Id=this.teamOptions.map((function(e){return e.value})).filter((function(e){return!!e})).join(","));var u=this.isCom?n.toUpperCase():o.toUpperCase(),d=u.split(" ").filter((function(e){return!!e}));this.isCom?c.Comp_Codes=d:c.Part_Code=d,(0,s.GetTeamTaskDetails)(c).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.map((function(e){return e.checked=!1,e})),t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.multipleSelection=[],t.tbLoading=!1}))},getProcessTeam:function(){var e=this;return(0,l.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,_.GetTeamListByUser)({type:e.isCom?1:2}).then((function(t){e.teamOptions=t.Data.map((function(e){return{value:e.Id,label:e.Name}})),e.teamOptions.unshift({value:"",label:"全部"}),e.teamOptions.length}));case 1:return t.a(2)}}),t)})))()},handleStart:function(){this.title="开始加工",this.currentComponent="Process",this.dialogVisible=!0},handleTransfer:function(){this.title="报工转移",this.currentComponent="Transfer",this.dialogVisible=!0},getProcessOption:function(){var e=this;(0,_.GetProcessList)({type:this.isCom?1:2}).then((function(t){t.IsSucceed?e.processOption=t.Data:e.$message({message:t.Message,type:"error"})}))},processChange:function(e){this.form.Next_Team_Id="",e&&this.getTeamOption()},getTeamOption:function(){var e=this,t="",a=this.processOption.find((function(t){return t.Code===e.form.Target_Process_Code}));a&&(t=a.Id),(0,_.GetWorkingTeamBase)({processId:t}).then((function(t){t.IsSucceed?e.groupOption=t.Data:e.$message({message:t.Message,type:"error"})}))},handleView:function(){},handleReset:function(){this.$refs["form"].resetFields(),this.searchInput="",this.search(1)},tbSelectChange:function(e){this.multipleSelection=e.records},handleClose:function(){this.dialogVisible=!1},handleCheck:function(){this.title="报检",this.currentComponent="CheckDialog",this.dialogVisible=!0},handelselect:function(){this.Isdisplay=!this.Isdisplay,this.activeCss=!this.activeCss},changeInstall:function(){this.$forceUpdate()},setDataStatus:function(e){this.dataStatus=Number(e),this.activeCss=!1,this.form={Task_Code:"",Spec:"",Schduling_Code:"",Target_Process_Code:"",Comp_Codes:"",Part_Code:"",Working_Team_Id:"",Next_Team_Id:""},this.fetchData()},openPartDetail:function(e){var t=this;this.title="零件齐套",this.currentComponent="CompleteSet",this.dialogVisible=!0,this.$nextTick((function(){t.$refs["content"].handleOpen(e)}))}}}},a436:function(e,t,a){},a8e2:function(e,t,a){"use strict";a.r(t);var n=a("059e"),i=a("7e994");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"4576720c",null);t["default"]=l.exports},ac4e:function(e,t,a){"use strict";a.r(t);var n=a("9d6d"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},b6c1:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"container h100"},[a("el-form",{ref:"form",attrs:{inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"报检数量:"}},[e._v(" "+e._s(e.totalNum)+" ")]),a("el-form-item",{attrs:{label:"报检重量:"}},[e._v(" "+e._s(e.totalWeight)+"(t) ")])],1),a("div",{staticClass:"tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"checkbox-config":{checkField:"checked"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tableData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e._l(e.columns,(function(t){return["Team_Check_Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto","edit-render":{},fixed:"right","min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.Ready_Check_Count},on:{change:e.inputChange},model:{value:n.Team_Check_Count,callback:function(t){e.$set(n,"Team_Check_Count",e._n(t))},expression:"row.Team_Check_Count"}})]}},{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("displayValue")(a.Team_Check_Count))+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto",fixed:"Part_Code"===t.Code||"Comp_Code"===t.Code?"left":"","min-width":t.Width}})]}))],2)],1),a("footer",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.tableData.length,loading:e.loading},on:{click:e.save}},[e._v("确定")])],1)],1)},i=[]},c3c6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniqueCode=t.getDraftQuery=t.FIX_COLUMN=void 0,a("b0c0");var i=n(a("5530"));t.getDraftQuery=function(e,t,a,n,r){return{name:e,query:(0,i.default)({status:t,pg_type:a,pg_redirect:r.name},n)}},t.uniqueCode=function(e){return"uuid"},t.FIX_COLUMN=["Comp_Code","Project_Name","Area_Name","Part_Code","InstallUnit_Name"]},c6a4:function(e,t,a){},ccb5:function(e,t,a){"use strict";function n(e,t){if(null==e)return{};var a={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;a[n]=e[n]}return a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=n},cfa9:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("7f9d");t.default={components:{},props:{},data:function(){return{tableData:[],Comp_Code:"",Columns:[{title:"零件名称",width:"200",field:"Part_Code"},{title:"是否主零件",width:"200",field:"Is_Main"},{title:"单数",width:"200",field:"Times"},{title:"需求数",width:"200",field:"Need_Count"},{title:"排产数量",width:"200",field:"Schduling_count"},{title:"生产中数量",width:"200",field:"Producing_count"},{title:"生产完成数量",width:"200",field:"Stock_count"},{title:"已消耗数量",width:"200",field:"Use_count"}],tbLoading:!1}},created:function(){},mounted:function(){},methods:{handleOpen:function(e){var t=this;this.tbLoading=!0,this.Comp_Code=e.Comp_Code,(0,n.GetPartPrepareList)({CompId:e.Comp_Id,ProcessId:e.Process_Id}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))}}}},d14b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{staticClass:"top-x"},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.tabsActiveName,callback:function(t){e.tabsActiveName=t},expression:"tabsActiveName"}},[a("el-tab-pane",{attrs:{label:"待加工",name:"1"}}),a("el-tab-pane",{attrs:{label:"待报检",name:"3"}}),a("el-tab-pane",{attrs:{label:"待转移",name:"2"}}),a("el-tab-pane",{attrs:{label:"已完成",name:"4"}})],1)],1),a("div",{staticClass:"main-wrapper"},[a("keep-alive",[a(e.activeName,{ref:"content",tag:"component",attrs:{"data-status":e.tabsActiveName}})],1)],1)])},i=[]},df3f:function(e,t,a){"use strict";a.r(t);var n=a("a2bb"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},e815:function(e,t,a){},ecd0:function(e,t,a){"use strict";a.r(t);var n=a("7808"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},f2f6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=s,t.CheckPlanTime=c,t.DeleteInstallUnit=m,t.GetCompletePercent=v,t.GetEntity=b,t.GetInstallUnitAllInfo=d,t.GetInstallUnitComponentPageList=_,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=u,t.GetInstallUnitList=l,t.GetInstallUnitPageList=o,t.GetProjectInstallUnitList=C,t.ImportInstallUnit=p,t.InstallUnitInfoTemplate=h,t.SaveInstallUnit=g,t.SaveOhterSourceInstallUnit=P;var i=n(a("b775")),r=n(a("4328"));function o(e){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function c(e){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function u(e){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function d(e){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function m(e){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function h(e){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function p(e){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function g(e){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function _(e){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function C(e){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function b(e){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(e)})}function P(e){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},fcd1:function(e,t,a){"use strict";a.r(t);var n=a("3bb1"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a}}]);