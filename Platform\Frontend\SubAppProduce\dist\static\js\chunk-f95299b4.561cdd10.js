(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-f95299b4"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=s,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,a){var s=r(),i=t-s,u=20,l=0;e="undefined"===typeof e?500:e;var c=function(){l+=u;var t=Math.easeInOutQuad(l,s,i,e);o(t),l<e?n(c):a&&"function"===typeof a&&a()};c()}},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:t,IsAll:a}).then((function(t){var n=t.IsSucceed,s=t.Data,i=t.Message;if(n){if(!s)return void e.$message({message:"表格配置不存在",type:"error"});var u=[];e.tbConfig=Object.assign({},e.tbConfig,s.Grid),u=a?(null===s||void 0===s?void 0:s.ColumnList)||[]:(null===s||void 0===s?void 0:s.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=u.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+s.Grid.Row_Number||o.tablePageSize[0]),r(e.columns)}else e.$message({message:i,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,n=t.type;this.queryInfo.Page="limit"===n?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===e){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},1746:function(t,e,a){},"251c":function(t,e,a){"use strict";a.r(e);var n=a("44da"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"255c":function(t,e,a){"use strict";a.r(e);var n=a("9dc3"),o=a("251c");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("7a416");var s=a("2877"),i=Object(s["a"])(o["default"],n["a"],n["b"],!1,null,"70df0774",null);e["default"]=i.exports},"27f1":function(t,e,a){"use strict";a.r(e);var n=a("da28"),o=a("8b1d");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);var s=a("2877"),i=Object(s["a"])(o["default"],n["a"],n["b"],!1,null,"f7404300",null);e["default"]=i.exports},"44da":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("5530")),r=n(a("c14f")),s=n(a("1da1"));a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("ab43"),a("dca8"),a("d3b7"),a("ac1f"),a("2532"),a("3ca3"),a("841c"),a("ddb0");var i=n(a("3796")),u=n(a("65b1")),l=a("ed08"),c=n(a("27f1")),d=(a("8975"),n(a("15ac"))),f=n(a("333d")),m=a("ec21"),p=a("be22"),h=a("c685"),g=a("c24f"),y=a("8cdf"),_=a("7757"),b=n(a("2082"));e.default={name:"PROAssetaccount",components:{Pagination:f.default,bimdialog:c.default,mDialog:u.default,Upload:i.default},mixins:[b.default,d.default],data:function(){return{hanLoading:!1,lonloading:!1,dialogShow:!1,addPageArray:[{path:this.$route.path+"/view",hidden:!0,component:function(){return a.e("chunk-f935813e").then(a.bind(null,"6d68"))},name:"PROAssetaccountAdd",meta:{title:"资产台账登记"}}],selectloading:!1,disabled:!0,btnloading:!1,departmentlist:[],factoryOption:[],form:{FactoryId:"",DepartId:"",RegisterDepartId:"",Status:"",FeeType:"",IsToProject:"",Keywords:"",IsPayOnBehalf:"",StartDate:"",EndDate:"",IsOutput:""},columns:[],roleList:[],permissionList:[],curUserDep:"",tbLoading:!1,search:function(){return{}},tbData:[],total:0,tablePageSize:h.tablePageSize,queryInfo:{Page:1,PageSize:h.tablePageSize[0]}}},computed:{},mounted:function(){var t=this;return(0,s.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.tbLoading=!0,e.n=1,t.getUserDepartTypePermission();case 1:return e.n=2,t.getTableConfig("PROAssetaccountPageList");case 2:return e.n=3,t.getRoleAuthorization();case 3:return t.getFactory(),t.search=(0,l.debounce)(t.fetchData,800,!0),e.n=4,t.fetchData();case 4:return e.a(2)}}),e)})))()},methods:{handleImport:function(){this.dialogShow=!0},handleClosebatch:function(){this.dialogShow=!1},handleSubmit:function(){this.$refs.upload.handleSubmit()},beforeUpload:function(t){var e=this,a=new FormData;a.append("Files",t),this.hanLoading=!0,(0,m.AssetsRegistrationImport)(a).then((function(t){t.IsSucceed?(e.fetchData(1),e.$message({type:"success",message:"导入成功"}),e.dialogShow=!1):(e.$message({type:"error",message:t.Message}),t.Data&&window.open((0,l.combineURL)(e.$baseUrl,t.Data))),e.hanLoading=!1}))},exportclick:function(){var t=(0,l.combineURL)(this.$baseUrl,"/Template/OMA/资产台账登记导入模板.xlsx");window.open(t,"_blank")},changeValue:function(t){this.form.DepartId&&(this.form.DepartId=""),this.getFirstLevelDepartsUnderFactory(t)},getRoleAuthorization:function(){var t=this;return(0,s.default)((0,r.default)().m((function e(){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,g.RoleAuthorization)({roleType:3,menuType:1,menuId:t.$route.meta.Id});case 1:a=e.v,a.IsSucceed?t.roleList=a.Data.map((function(t){return t.Code})):t.$message({type:"warning",message:a.Message});case 2:return e.a(2)}}),e)})))()},getFirstLevelDepartsUnderFactory:function(t){var e=this;return(0,s.default)((0,r.default)().m((function a(){var n;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,y.GetFirstLevelDepartsUnderFactory)({FactoryId:t});case 1:n=a.v,n.IsSucceed?(e.departmentlist=n.Data||[],e.disabled=!1):e.$message.error(n.Mesaage);case 2:return a.a(2)}}),a)})))()},getUserDepartTypePermission:function(){var t=this;return(0,s.default)((0,r.default)().m((function e(){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,p.GetUserDepartTypePermission)({});case 1:a=e.v,a.IsSucceed?t.permissionList=a.Data||[]:t.$message.error(a.Mesaage);case 2:return e.a(2)}}),e)})))()},getData:function(){},fetchData:function(t){var e=this;this.tbLoading=!0,this.form.Accounting_StartDate=this.form.InStoreDate?(0,l.parseTime)(this.form.InStoreDate[0],"{y}-{m}-{d}"):null,this.form.Accounting_EndDate=this.form.InStoreDate?(0,l.parseTime)(this.form.InStoreDate[1],"{y}-{m}-{d}"):null,this.form.Trade_StartDate=this.form.PurchaseDate?(0,l.parseTime)(this.form.PurchaseDate[0],"{y}-{m}-{d}"):null,this.form.Trade_EndDate=this.form.PurchaseDate?(0,l.parseTime)(this.form.PurchaseDate[1],"{y}-{m}-{d}"):null,t&&(this.queryInfo.Page=t),(0,m.AssetAccountPageList)((0,o.default)((0,o.default)({},this.queryInfo),this.form)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data||[],e.total=t.Data.TotalCount,e.curUserDep=t.Data.Cur_User_Depart,e.tbLoading=!1):e.$message({message:t.Message,type:"error"})}))},exportReport:function(){var t=this;this.lonloading=!0,(0,m.ExportList)((0,o.default)({},this.form)).then((function(e){e.IsSucceed?(window.open((0,l.combineURL)(t.$baseUrl,e.Data),"_blank"),e.Message&&t.$alert(e.Message,"导出通知",{confirmButtonText:"我知道了"})):t.$message.error(e.Message)})).finally((function(e){t.lonloading=!1}))},pageChange:function(){this.fetchData()},getRoles:function(t){return this.roleList.includes(t)},getButtonEdit:function(t){if((this.curUserDep===t.Department_name||this.$store.getters.userId===t.Create_UserId||this.permissionList.includes(t.Department_Type))&&"未核算"==t.Accounting_Status_Name&&this.roleList.includes("AssetAccountEdit"))return!0},getButtonPaymentRegist:function(t){if((this.curUserDep===t.Department_name||this.$store.getters.userId===t.Create_UserId||this.permissionList.includes(t.Department_Type))&&("核算中"==t.Accounting_Status_Name||"已核算"==t.Accounting_Status_Name)&&this.roleList.includes("PaymentRegistration"))return!0},getButtonAssetAccountDelete:function(t){if((this.curUserDep===t.Department_name||this.$store.getters.userId===t.Create_UserId||this.permissionList.includes(t.Department_Type))&&"未核算"==t.Accounting_Status_Name&&this.roleList.includes("AssetAccountDelete"))return!0},getButtonAssetAccountScrap:function(t){if((this.curUserDep===t.Department_name||this.$store.getters.userId===t.Create_UserId||this.permissionList.includes(t.Department_Type))&&("核算中"==t.Accounting_Status_Name||"已核算"==t.Accounting_Status_Name)&&("摊销中"==t.Amortization_Status_Name||"摊销完毕"==t.Amortization_Status_Name)&&this.roleList.includes("AssetAccountScrap"))return!0},resetForm:function(){this.form={},this.disabled=!0},handleScrap:function(t){this.$refs.dialog.handleOpen(t)},handleOpen:function(t,e){e="add"==t?{}:e,this.$router.push({name:"PROAssetaccountAdd",query:{pg_redirect:"PROAssetaccount",type:t,Id:e.Id}})},handleDelete:function(t){var e=this;this.$confirm("此操作将永久删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,m.DeleteAssetAccount)({Id:t.Id}).then((function(t){t.IsSucceed?(e.fetchData(),e.$message.success("删除成功")):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},getFactory:function(){var t=this;this.selectloading=!0,(0,_.GetQueryNonExternalFactory)({}).then((function(e){e.IsSucceed?(t.selectloading=!1,t.factoryOption=Object.freeze(e.Data)):t.$message({message:e.Message,type:"error"})}))}}}},"6a93":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),s=a("ec21"),i=a("cf45"),u=n(a("65b1"));e.default={props:{},components:{bimdialog:u.default},name:"",data:function(){return{title:"提示",dialogVisible:!1,form:{Id:"",Scrap_Date:""},AssetTypeList:[]}},mounted:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,i.getDictionary)("AssetType");case 1:t.AssetTypeList=e.v;case 2:return e.a(2)}}),e)})))()},methods:{handleSubmit:function(){var t=this;if(!this.form.Scrap_Date)return this.$message({message:"报废日期为填写",type:"error"}),!1;(0,s.ScrapAssetsRegistr)({Id:this.form.Id,Scrap_Date:this.form.Scrap_Date}).then((function(e){e.IsSucceed?t.$emit("getData"):t.$message({message:e.Message,type:"error"})})),this.dialogVisible=!1},handleOpen:function(t){this.form=t,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1}}}},7757:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.FeeRegistrationImport=f,e.GetBusinessLastUpdateDate=c,e.GetFactoryProcessLibs=l,e.GetFactoryProjectList=i,e.GetFirstLevelDepartsUnderFactory=s,e.GetNonExternalFactory=r,e.GetOMALatestAccountingDate=m,e.GetQueryNonExternalFactory=d,e.GetReportLastDate=u;var o=n(a("b775"));function r(t){return(0,o.default)({url:"/oma/Common/GetNonExternalFactory",method:"post",data:t})}function s(t){return(0,o.default)({url:"/oma/Common/GetFirstLevelDepartsUnderFactory",method:"post",data:t})}function i(t){return(0,o.default)({url:"/oma/Common/GetFactoryProjectList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/oma/Common/GetReportLastDate",method:"post",data:t})}function l(t){return(0,o.default)({url:"/oma/Common/GetFactoryProcessLibs",method:"post",data:t})}function c(t){return(0,o.default)({url:"/oma/Common/GetBusinessLastUpdateDate",method:"post",data:t})}function d(t){return(0,o.default)({url:"/oma/Common/GetQueryNonExternalFactory",method:"post",data:t})}function f(t){return(0,o.default)({url:"/oma/FeeRegistration/Import",method:"post",data:t})}function m(t){return(0,o.default)({url:"/oma/common/GetOMALatestAccountingDate",method:"post",data:t})}},"7a416":function(t,e,a){"use strict";a("1746")},"8b1d":function(t,e,a){"use strict";a.r(e);var n=a("6a93"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"8cdf":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BusinessPageList=r,e.DeleteIncome=c,e.EditIncome=d,e.ExportList=f,e.GetFactoryProjectList=i,e.GetFirstLevelDepartsUnderFactory=s,e.IncomeDetails=l,e.IncomeRegistrationImport=m,e.SaveIncome=u;var o=n(a("b775"));function r(t){return(0,o.default)({url:"/OMA/IncomeRegistration/PageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/oma/common/GetFirstLevelDepartsUnderFactory",method:"post",data:t})}function i(t){return(0,o.default)({url:"oma/common/GetFactoryProjectList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/OMA/IncomeRegistration/Save",method:"post",data:t})}function l(t){return(0,o.default)({url:"/OMA/IncomeRegistration/Get",method:"post",data:t})}function c(t){return(0,o.default)({url:"/OMA/IncomeRegistration/Delete",method:"post",data:t})}function d(t){return(0,o.default)({url:"/OMA/IncomeRegistration/Update",method:"post",data:t})}function f(t){return(0,o.default)({url:"/OMA/IncomeRegistration/Export",method:"post",data:t})}function m(t){return(0,o.default)({url:"/oma/IncomeRegistration/Import",method:"post",data:t})}},"9dc3":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",attrs:{inline:"",model:t.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"归属基地:",props:"FactoryId"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",loading:t.selectloading},on:{change:t.changeValue},model:{value:t.form.FactoryId,callback:function(e){t.$set(t.form,"FactoryId",e)},expression:"form.FactoryId"}},t._l(t.factoryOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"摊销部门:",placeholder:"请选择"}},[a("el-select",{attrs:{clearable:"",disabled:!t.form.FactoryId},model:{value:t.form.DepartId,callback:function(e){t.$set(t.form,"DepartId",e)},expression:"form.DepartId"}},t._l(t.departmentlist,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"摊销状态:",prop:"amortizationStatus"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Amortization_Status,callback:function(e){t.$set(t.form,"Amortization_Status",e)},expression:"form.Amortization_Status"}},[a("el-option",{attrs:{label:"摊销中",value:1}}),a("el-option",{attrs:{label:"摊销完毕",value:2}}),a("el-option",{attrs:{label:"已报废",value:3}})],1)],1),a("el-form-item",{attrs:{label:"资产名称:",prop:"assetName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"输入资产名称"},model:{value:t.form.Asset_Name,callback:function(e){t.$set(t.form,"Asset_Name",e)},expression:"form.Asset_Name"}})],1),a("el-form-item",{attrs:{label:"供应商:",prop:"supplier"}},[a("el-input",{attrs:{placeholder:"输入供应商"},model:{value:t.form.Supplier_Name,callback:function(e){t.$set(t.form,"Supplier_Name",e)},expression:"form.Supplier_Name"}})],1),a("el-form-item",{attrs:{label:"付款状态:",prop:"Payment_Status"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Payment_Status,callback:function(e){t.$set(t.form,"Payment_Status",e)},expression:"form.Payment_Status"}},[a("el-option",{attrs:{label:"未付清",value:1}}),a("el-option",{attrs:{label:"已付清",value:2}}),a("el-option",{attrs:{label:"超额支付",value:3}})],1)],1),a("el-form-item",{attrs:{label:"核算状态:",prop:"Status_Name"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Status,callback:function(e){t.$set(t.form,"Status",e)},expression:"form.Status"}},[a("el-option",{attrs:{label:"未核算",value:1}}),a("el-option",{attrs:{label:"核算中",value:2}}),a("el-option",{attrs:{label:"已核算",value:3}})],1)],1),a("el-form-item",{attrs:{label:"核算日期:",prop:"StartDate"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.form.InStoreDate,callback:function(e){t.$set(t.form,"InStoreDate",e)},expression:"form.InStoreDate"}})],1),a("el-form-item",{attrs:{label:"采购日期:",prop:"dateOfPurchase"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.form.PurchaseDate,callback:function(e){t.$set(t.form,"PurchaseDate",e)},expression:"form.PurchaseDate"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.fetchData(1)}}},[t._v("查询")]),a("el-button",{on:{click:t.resetForm}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"info-wrapper"},[a("h4",[t._v("数据列表")]),a("div",{staticClass:"btn-x"},[t.roleList.includes("AssetAccountAdd")?a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.handleOpen("add")}}},[t._v("登记资产")]):t._e(),t.roleList.includes("AssetAccountExport")?a("el-button",{attrs:{loading:t.lonloading},on:{click:t.exportReport}},[t._v("导出报表")]):t._e(),t.roleList.includes("AssetAccountImport")?a("el-button",{on:{click:t.handleImport}},[t._v("导入报表")]):t._e()],1)]),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:t.tbLoading,stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,"min-width":e.Width},scopedSlots:t._u(["Accounting_Date"===e.Code?{key:"default",fn:function(a){var n=a.row;return[t._v(" "+t._s(t._f("timeFormat")(n[e.Code],"{y}-{m}-{d}"))+" ")]}}:"Last_Pay_Date"===e.Code?{key:"default",fn:function(a){var n=a.row;return[t._v(" "+t._s(t._f("timeFormat")(n[e.Code],"{y}-{m}-{d}"))+" ")]}}:"Trade_Date"===e.Code?{key:"default",fn:function(a){var n=a.row;return[t._v(" "+t._s(t._f("timeFormat")(n[e.Code],"{y}-{m}-{d}"))+" ")]}}:"Create_Date"===e.Code?{key:"default",fn:function(a){var n=a.row;return[t._v(" "+t._s(t._f("timeFormat")(n[e.Code],"{y}-{m}-{d} {h}:{i}:{s} "))+" ")]}}:"Accounting_Status_Name"===e.Code?{key:"default",fn:function(e){var n=e.row;return["未核算"===n.Accounting_Status_Name?a("el-tag",{attrs:{type:"danger"}},[t._v("未核算")]):t._e(),"核算中"===n.Accounting_Status_Name?a("el-tag",{attrs:{type:"warning"}},[t._v("核算中")]):t._e(),"已核算"===n.Accounting_Status_Name?a("el-tag",{attrs:{type:"success"}},[t._v("已核算")]):t._e()]}}:"Payment_Status_Name"===e.Code?{key:"default",fn:function(e){var n=e.row;return["超额支付"===n.Payment_Status_Name?a("el-tag",{attrs:{type:"danger"}},[t._v("超额支付")]):t._e(),"未付清"===n.Payment_Status_Name?a("el-tag",{attrs:{type:"warning"}},[t._v("未付清")]):t._e(),"已付清"===n.Payment_Status_Name?a("el-tag",{attrs:{type:"success"}},[t._v("已付清")]):t._e()]}}:"Amortization_Status_Name"===e.Code?{key:"default",fn:function(e){var n=e.row;return["已报废"===n.Amortization_Status_Name?a("el-tag",{attrs:{type:"danger"}},[t._v("已报废")]):t._e(),"摊销中"===n.Amortization_Status_Name?a("el-tag",{attrs:{type:"warning"}},[t._v("摊销中")]):t._e(),"摊销完毕"===n.Amortization_Status_Name?a("el-tag",{attrs:{type:"success"}},[t._v("摊销完毕")]):t._e()]}}:{key:"default",fn:function(a){var n=a.row;return[t._v(" "+t._s(t._f("displayValue")(n[e.Code]))+" ")]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"260"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[t.roleList.includes("AssetAccountView")&&t.permissionList.includes(n.Department_Type)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleOpen("view",n)}}},[t._v("查看")]):t._e(),t.getButtonEdit(n)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleOpen("edit",n)}}},[t._v("编辑")]):t._e(),t.getButtonPaymentRegist(n)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleOpen("paymentRegist",n)}}},[t._v("付款登记")]):t._e(),t.getButtonAssetAccountDelete(n)?a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(e){return t.handleDelete(n)}}},[t._v(" 删除 ")]):t._e(),t.getButtonAssetAccountScrap(n)?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleScrap(n)}}},[t._v(" 报废盈亏 ")]):t._e()]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)])],1),a("bimdialog",{ref:"dialog",on:{getData:t.fetchData}}),a("m-dialog",{attrs:{"dialog-title":"资产台账导入",visible:t.dialogShow,"dialog-width":"680px",top:"20vh",hidebtn:""},on:{"update:visible":function(e){t.dialogShow=e},handleClose:t.handleClosebatch}},[a("div",{staticStyle:{padding:"5px"}},[a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:t.exportclick}},[t._v("点击此处下载导入模板")])],1),a("upload",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(e){return t.handleClosebatch()}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.hanLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)])],1)},o=[]},be22:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ConfirmFee=d,e.CostAccountingRegistrationPageList=i,e.DeleteCostAccountingRegistration=c,e.ExportCostAccountingRegistration=f,e.GetFeeEntity=u,e.GetUserDepartTypePermission=m,e.SaveNewFee=s,e.UpdateFee=l,e.UpdatePaymentList=r;var o=n(a("b775"));function r(t){return(0,o.default)({url:"/oma/FeeRegistration/UpdatePaymentList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/oma/FeeRegistration/SaveNewFee",method:"post",data:t})}function i(t){return(0,o.default)({url:"/oma/FeeRegistration/CostAccountingRegistrationPageList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/oma/FeeRegistration/GetFeeEntity",method:"post",data:t})}function l(t){return(0,o.default)({url:"/oma/FeeRegistration/UpdateFee",method:"post",data:t})}function c(t){return(0,o.default)({url:"/oma/FeeRegistration/DeleteCostAccountingRegistration",method:"post",data:t})}function d(t){return(0,o.default)({url:"/oma/FeeRegistration/ConfirmFee",method:"post",data:t})}function f(t){return(0,o.default)({url:"/oma/FeeRegistration/ExportCostAccountingRegistration",method:"post",data:t})}function m(t){return(0,o.default)({url:"/oma/FeeRegistration/GetUserDepartTypePermission",method:"post",data:t})}},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=o,a("d3b7");var n=a("6186");function o(t){return new Promise((function(e,a){(0,n.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},da28:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("bimdialog",{staticClass:"plmdialog",attrs:{"dialog-title":t.title,"append-to-body":!0,"dialog-width":"450px",visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e},handleClose:t.handleClose,cancelbtn:t.handleClose,submitbtn:t.handleSubmit}},[a("div",[a("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center","margin-bottom":"10px"}},[a("span",{staticStyle:{color:"red","font-size":"25px"}},[a("i",{staticClass:"el-icon-warning"})]),t._v("是否确认报废该资产！ ")]),a("div",[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"资产类型:",prop:"Asset_Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:""},model:{value:t.form.Asset_Type,callback:function(e){t.$set(t.form,"Asset_Type",e)},expression:"form.Asset_Type"}},t._l(t.AssetTypeList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"资产名称"}},[a("el-input",{attrs:{disabled:""},model:{value:t.form.Asset_Name,callback:function(e){t.$set(t.form,"Asset_Name",e)},expression:"form.Asset_Name"}})],1),a("el-form-item",{attrs:{label:"资产编号"}},[a("el-input",{attrs:{disabled:""},model:{value:t.form.Asset_No,callback:function(e){t.$set(t.form,"Asset_No",e)},expression:"form.Asset_No"}})],1),a("el-form-item",{attrs:{label:"残值"}},[a("el-input",{attrs:{disabled:""},model:{value:t.form.Residual_Price,callback:function(e){t.$set(t.form,"Residual_Price",e)},expression:"form.Residual_Price"}})],1),a("el-form-item",{attrs:{label:"采购日期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",disabled:""},model:{value:t.form.Trade_Date,callback:function(e){t.$set(t.form,"Trade_Date",e)},expression:"form.Trade_Date"}})],1),a("el-form-item",{attrs:{label:"报废日期",required:""}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:t.form.Scrap_Date,callback:function(e){t.$set(t.form,"Scrap_Date",e)},expression:"form.Scrap_Date"}})],1)],1)],1)])])],1)},o=[]},dca8:function(t,e,a){"use strict";var n=a("23e7"),o=a("bb2f"),r=a("d039"),s=a("861d"),i=a("f183").onFreeze,u=Object.freeze,l=r((function(){u(1)}));n({target:"Object",stat:!0,forced:l,sham:!o},{freeze:function(t){return u&&s(t)?u(i(t)):t}})},ec21:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AssetAccountPageList=r,e.AssetsRegistDetails=l,e.AssetsRegistrationImport=h,e.DeleteAssetAccount=d,e.EditAssetsRegist=c,e.ExportList=f,e.GetFactoryProjectList=u,e.GetFirstLevelDepartsUnderFactory=i,e.SaveAssetsRegistration=s,e.ScrapAssetsRegistr=m,e.UpdatePaymentList=p;var o=n(a("b775"));function r(t){return(0,o.default)({url:"/OMA/AssetsRegistration/PageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/OMA/AssetsRegistration/Save",method:"post",data:t})}function i(t){return(0,o.default)({url:"/oma/common/GetFirstLevelDepartsUnderFactory",method:"post",data:t})}function u(t){return(0,o.default)({url:"oma/common/GetFactoryProjectList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/OMA/AssetsRegistration/Get",method:"post",data:t})}function c(t){return(0,o.default)({url:"/OMA/AssetsRegistration/Update",method:"post",data:t})}function d(t){return(0,o.default)({url:"/OMA/AssetsRegistration/Delete",method:"post",data:t})}function f(t){return(0,o.default)({url:"/OMA/AssetsRegistration/Export",method:"post",data:t})}function m(t){return(0,o.default)({url:"/OMA/AssetsRegistration/Scrap",method:"post",data:t})}function p(t){return(0,o.default)({url:"/OMA/AssetsRegistration/UpdatePaymentList",method:"post",data:t})}function h(t){return(0,o.default)({url:"/oma/AssetsRegistration/Import",method:"post",data:t})}}}]);