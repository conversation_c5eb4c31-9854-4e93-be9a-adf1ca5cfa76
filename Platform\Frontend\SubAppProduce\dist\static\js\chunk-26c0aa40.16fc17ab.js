(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-26c0aa40"],{"0d41":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("b0c0"),a("e9f5"),a("910d"),a("d3b7");var n=r(a("bc29")),o=r(a("40c9")),i=r(a("74fd1")),l=a("8378");t.default={name:"PROMaterialAllocation",components:{inventory:i.default},mixins:[n.default,o.default],data:function(){return{showSearch:!1,activeName:"1",form:{RawNameFull:"",Spec:"",Thick:"",Width:"",Length:"",Material:"",Raw_Name:"",Category_Id:"",WH_Id:"",Location_Id:"",Sys_Project_Id:"",Supplier:"",Party_Unit:"",Raw_Property:[]},categoryOptions:{"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},treeList:[]}},computed:{materialName:function(){return"PRORawMaterialAllocation"===this.$route.name?"原料":"辅料"}},mounted:function(){this.getCategoryList()},methods:{filterFun:function(e){this.$refs.treeSelectArea.$refs.tree.filter(e)},handleTap:function(e){var t=this;this.activeName=e,this.form={Spec:"",Thick:"",Width:"",Length:"",Material:"",Raw_Name:"",Category_Id:"",WH_Id:"",Location_Id:"",Sys_Project_Id:"",Supplier:"",Party_Unit:"",Raw_Property:[]};var a=[];switch(this.activeName){case"1":a=this.treeList.filter((function(e){return"板材"===e.Label})),this.$refs.treeSelectArea.treeDataUpdateFun(a);break;case"2":a=this.treeList.filter((function(e){return"型材"===e.Label})),this.$refs.treeSelectArea.treeDataUpdateFun(a);break;case"3":a=this.treeList.filter((function(e){return"钢卷"===e.Label})),this.$refs.treeSelectArea.treeDataUpdateFun(a);break;case"99":a=this.treeList.filter((function(e){return"板材"!==e.Label&&"型材"!==e.Label&&"钢卷"!==e.Label})),this.$refs.treeSelectArea.treeDataUpdateFun(a);break;default:}this.$nextTick((function(e){t.$refs.inventoryRef.fetchData(1)}))},handleSearch:function(){this.$refs.inventoryRef.fetchData(1)},getCategoryList:function(){var e=this;(0,l.GetCategoryTreeList)({}).then((function(t){if(t.IsSucceed){e.treeList=t.Data;var a=t.Data;e.categoryOptions.data=a,e.$nextTick((function(t){e.$refs.treeSelectArea.treeDataUpdateFun(a)}))}else e.$message.error(t.Message)}))}}}},"0f0f":function(e,t,a){"use strict";a("1790")},"13cc":function(e,t,a){},1790:function(e,t,a){},"1d11":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-wapper"},[a("div",{staticClass:"toolbar-container"},[a("el-button",{attrs:{type:"primary"},on:{click:e.viewTransformLog}},[e._v("转工程记录")])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{height:"0",flex:"1"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.currentColumns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,multiSelectedChange:e.tbSelectChange},scopedSlots:e._u([{key:"Raw_Name",fn:function(t){var r=t.row;return[a("div",[r.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),r.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),r.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(r.Raw_Name)+" ")],1)]}},{key:"Supplier",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Supplier||"-"))])]}},{key:"Party_Unit",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Party_Unit||"-"))])]}},{key:"Total_Count",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(r,1)}}},[e._v(e._s(r.Total_Count||0))])]}},{key:"Total_Weight",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(r,1)}}},[e._v(e._s(r.Total_Weight?(r.Total_Weight/1e3).toFixed(5):0))])]}},{key:"Total_Lock_Count",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(r,2)}}},[e._v(e._s(r.Total_Lock_Count||0))])]}},{key:"Total_Lock_Weight",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(r,2)}}},[e._v(e._s(r.Total_Lock_Weight?(r.Total_Lock_Weight/1e3).toFixed(5):0))])]}},{key:"op",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handelLock(r,3)}}},[e._v("转工程")])]}}])})],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"material-type":0},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e()],1)},n=[]},"2ee2":function(e,t,a){"use strict";a("95aa")},3520:function(e,t,a){"use strict";a.r(t);var r=a("5705"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},"362c":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("25f0");r(a("53ca"));var n=r(a("c14f")),o=r(a("1da1")),i=r(a("5f52")),l=r(a("0f97")),u=a("3c4a"),c=a("ed08"),s=r(a("bc29")),f=a("90d1");t.default={components:{DynamicDataTable:l.default},mixins:[i.default,s.default],data:function(){return{pgLoading:!1,saveLoading:!1,columns:[],tbConfig:{},currentColumns:[],tbData:[],flag:1,ProjectId:"",rowDetail:{},activeName:""}},mounted:function(){return(0,o.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{batchUnlock:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,t.pgLoading=!0,t.isBatchUnlock=!0,t.flag=4,a.n=1,t.getTableConfig("pro_public_allocation");case 1:t.currentColumns=t.columns.filter((function(e){return["Unlock_Count","Tax_Unit_Price_New"].includes(e.Code)&&(e.Is_Edit=!1),"Lock_Count"!==e.Code&&"Lock_Weight"!==e.Code&&"Affiliation_Project_Name"!==e.Code&&"Purchase_Ready_Lock_Count"!==e.Code})),t.fetNewDetail(e),a.n=3;break;case 2:a.p=2,a.v,t.pgLoading=!1;case 3:return a.a(2)}}),a,null,[[0,2]])})))()},fetNewDetail:function(e){var t=this,a=e.map((function(e){return e.Store_Id})).toString();(0,u.GetRawBatchInventoryDetailList)({Store_Id:a}).then((function(e){e.IsSucceed?t.tbData=e.Data.map((function(e){return e.Unlock_Count=e.In_Store_Count,e.Tax_Unit_Price_New=1e3*(e.Tax_Unit_Price||0),e.Tax_Unit_Price_New_KG=e.Tax_Unit_Price||"",e.Unlock_Weight=e.In_Store_Weight||"",e.Tax_All_Price_New=e.Tax_All_Price||"",e.NewNoTaxAllPrice=(e.Tax_All_Price_New/(1+e.Tax_Rate/100)).toFixed(3)/1||0,e})):t.$message({message:e.Message,type:"error"}),t.pgLoading=!1}))},init:function(e,t,a,r){var i=this;return(0,o.default)((0,n.default)().m((function o(){return(0,n.default)().w((function(n){while(1)switch(n.n){case 0:return i.rowDetail=e,i.ProjectId=a,i.flag=t,i.activeName=r,n.n=1,i.getTableConfig("pro_public_allocation");case 1:return i.currentColumns=i.columns,1===t?i.currentColumns=i.currentColumns.filter((function(e){return"Project_Name"!==e.Code&&"Unlock_Count"!==e.Code&&"Unlock_Weight"!==e.Code&&"Lock_Count"!==e.Code&&"Lock_Weight"!==e.Code&&"Affiliation_Project_Name"!==e.Code&&"Tax_Unit_Price_New"!==e.Code&&"Tax_All_Price_New"!==e.Code&&"NewNoTaxAllPrice"!==e.Code})):2===t?i.currentColumns=i.currentColumns.filter((function(e){return"Unlock_Count"!==e.Code&&"Unlock_Weight"!==e.Code&&"Lock_Count"!==e.Code&&"Lock_Weight"!==e.Code&&"Affiliation_Project_Name"!==e.Code&&"Tax_Unit_Price_New"!==e.Code&&"Tax_All_Price_New"!==e.Code&&"Purchase_Ready_Lock_Count"!==e.Code&&"NewNoTaxAllPrice"!==e.Code})):0===t&&(i.currentColumns=i.currentColumns.filter((function(e){return"Unlock_Count"!==e.Code&&"Unlock_Weight"!==e.Code&&"Project_Name"!==e.Code}))),"99"===r?i.currentColumns=i.currentColumns.filter((function(e){return"In_Store_Weight"!==e.Code&&"Furnace_Batch_No"!==e.Code&&"Lock_Weight"!==e.Code&&"Unlock_Weight"!==e.Code&&"Actual_Thick"!==e.Code})):1!==r&&(i.currentColumns=i.currentColumns.filter((function(e){return"Actual_Thick"!==e.Code}))),i.currentColumns=i.currentColumns.filter((function(e){return"Measure_Unit"!==e.Code})),n.n=2,i.fetchData(e);case 2:return n.a(2)}}),o)})))()},fetchData:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){var r;return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return r={Inventory_Type:t.flag,Store_Id:e.Store_Id,Big_Type:e.Big_Type||1},a.n=1,(0,u.GetRawInventoryDetailList)(r).then((function(a){a.IsSucceed?t.tbData=a.Data.map((function(t){return t.In_Store_Date=t.In_Store_Date?(0,c.parseTime)(new Date(t.In_Store_Date),"{y}-{m}-{d}"):"",t.Tax_Rate_New=t.Tax_Rate,t.Unlock_Count="",t.Unlock_Weight="",t.Lock_Count="",t.Lock_Weight="",t.Affiliation_Project_Name="",t.Affiliation_Project_Id="",t.Tax_Unit_Price_New_KG=t.Tax_Unit_Price||"",t.Tax_Unit_Price_New=t.Tax_Unit_Price||0===t.Tax_Unit_Price?(1e3*t.Tax_Unit_Price).toFixed(3)/1:"",t.Store_Id=e.Store_Id,t.Tax_All_Price_New=(t.Unlock_Weight*t.Tax_Unit_Price_New).toFixed(3)/1||0,t})):t.$message.error(a.Message)}));case 1:return a.a(2)}}),a)})))()},changeUnlockCount:function(e,t){Number(e)>0&&Number(e)<=Number(t.In_Store_Count)?(t.Unlock_Weight=this.checkWeight(t),this.countTaxTotalPrice(t)):(t.Unlock_Count="",t.Unlock_Weight="",t.Tax_All_Price_New="")},projectChange:function(e){this.changeLockCount(e.Lock_Count,e)},changeLockCount:function(e,t){Number(e)>0&&Number(e)<=Number(t.In_Store_Count-t.Purchase_Ready_Lock_Count)?(t.Lock_Weight=this.checkWeight(t),this.countTaxTotalPrice(t)):(t.Lock_Count="",t.Lock_Weight="",t.Affiliation_Project_Id="",t.Tax_All_Price_New="")},checkWeight:function(e){return(e.Lock_Count*e.UnitWeight).toFixed(f.WEIGHT_DECIMAL)/1},changeTaxUnitPrice:function(e,t){t.NewNoTaxAllPrice=(t.Tax_All_Price_New/(1+t.Tax_Rate/100)).toFixed(3)/1||0,this.countTaxTotalPrice(t)},countTaxTotalPrice:function(e){var t=this,a=JSON.parse(JSON.stringify(this.tbData)),r=a.map((function(a){return a.Store_Sub_Id===e.Store_Sub_Id&&Number(a.Tax_Unit_Price_New)>0&&(a.Tax_Unit_Price_New_KG=Number((a.Tax_Unit_Price_New/1e3).toFixed(2)),0===t.flag&&(0===a.Lock_Count||0===a.Tax_Unit_Price_New?a.Tax_All_Price_New=0:a.Lock_Count&&a.Tax_Unit_Price_New?a.Tax_All_Price_New=Number((a.Lock_Count*a.Tax_Unit_Price_New).toFixed(2)):a.Tax_All_Price_New="")),a.NewNoTaxAllPrice=(a.Tax_All_Price_New/(1+a.Tax_Rate/100)).toFixed(3)/1||0,a}));this.$nextTick((function(){t.tbData=r}))},handleSave:function(){var e=this,t=(0,c.deepClone)(this.tbData),a=t.filter((function(e){return e.Lock_Count}));if(0!==a.length){var r=!1;0===this.flag&&a.map((function(e){e.Affiliation_Project_Id||(r=!0),e.Sys_Project_Id=e.Affiliation_Project_Id})),r?this.$message.warning("有锁定数量,所属项目不能为空"):(a=a.map((function(e){return{MaterialType:0,Store_Id:e.Store_Id,Store_Sub_Id:e.Store_Sub_Id,In_Store_Sub_Id:e.In_Store_Sub_Id,To_Sys_Project_Id:e.Sys_Project_Id,Transfer_Count:e.Lock_Count}})),this.saveLoading=!0,(0,u.TransferRawLock)(a).then((function(t){t.IsSucceed?(e.$message.success("保存成功"),e.$emit("refresh"),e.$emit("close")):e.$message.error(t.Message)})).finally((function(t){e.saveLoading=!1})))}else this.$message.warning("转工程数据最少为一条")},handleClose:function(){this.$emit("close")}}}},3673:function(e,t,a){"use strict";a("13cc")},"3cc6":function(e,t,a){},5705:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),o=r(a("1da1"));a("a9e3"),a("ac1f"),a("841c");var i=r(a("bad9")),l=r(a("7387"));t.default={components:{bimtable:l.default,SelectProject:i.default},props:{materialType:{type:[String,Number],default:0}},data:function(){return{pgLoading:!1,form:{MaterialType:"",Material_Name_All:"",Region_Project_Id:"",In_Project_Id:"",To_Project_Id:"",Transfer_Date_Begin:"",Transfer_Date_End:"",Transfer_Date:[]},gridCode:""}},watch:{"form.Transfer_Date":{handler:function(){this.form.Transfer_Date_Begin=this.form.Transfer_Date?this.form.Transfer_Date[0]:"",this.form.Transfer_Date_End=this.form.Transfer_Date?this.form.Transfer_Date[1]:""},immediate:!0},materialType:{handler:function(){this.form.MaterialType=this.materialType,this.gridCode=0==this.materialType?"materialTransformLog":"materialAuxTransformLog",this.search()},immediate:!0}},mounted:function(){return(0,o.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{search:function(){var e;null===(e=this.$refs.table)||void 0===e||e.refresh()},reset:function(){this.form={MaterialType:this.form.MaterialType,Material_Name_All:"",Region_Project_Id:"",In_Project_Id:"",To_Project_Id:"",Transfer_Date_Begin:"",Transfer_Date_End:"",Transfer_Date:[]},this.search()}}}},"5bd3":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{ref:"searchDom",staticClass:"header_wrapper"},[a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:e.materialName+"全名",prop:"RawNameFull"}},[a("el-input",{attrs:{placeholder:"通配符%",clearable:""},model:{value:e.form.RawNameFull,callback:function(t){e.$set(e.form,"RawNameFull",t)},expression:"form.RawNameFull"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:e.materialName+"名称",prop:"Raw_Name"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Raw_Name,callback:function(t){e.$set(e.form,"Raw_Name",t)},expression:"form.Raw_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1)],1),a("transition",{attrs:{name:"fade"}},[e.showSearch?a("span",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{type:"text",clearable:"",placeholder:"材质"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0},"tree-params":e.categoryOptions},on:{searchFun:e.filterFun},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"仓库",prop:"WH_Id"}},[a("el-select",{ref:"WarehouseRef",attrs:{clearable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.WH_Id,callback:function(t){e.$set(e.form,"WH_Id",t)},expression:"form.WH_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{ref:"LocationRef",attrs:{clearable:"",placeholder:"请选择库位",disabled:!e.form.WH_Id},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"项目",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"甲方单位",prop:"Party_Unit"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Party_Unit,callback:function(t){e.$set(e.form,"Party_Unit",t)},expression:"form.Party_Unit"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:e.materialName+"属性",prop:"Raw_Property"}},[a("el-select",{attrs:{multiple:"",placeholder:"请选择"},model:{value:e.form.Raw_Property,callback:function(t){e.$set(e.form,"Raw_Property",t)},expression:"form.Raw_Property"}},[a("el-option",{attrs:{label:"自采",value:1}}),a("el-option",{attrs:{label:"甲供",value:2}}),a("el-option",{attrs:{label:"代购",value:3}}),a("el-option",{attrs:{label:"余料",value:4}})],1)],1)],1)],1):e._e()]),a("el-col",{attrs:{span:6}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){e.showSearch=!e.showSearch}}},[e._v(e._s(e.showSearch?"收起":"展开"))]),e.showSearch?a("i",{staticClass:"el-icon-caret-top",staticStyle:{color:"#409EFF"}}):a("i",{staticClass:"el-icon-caret-bottom",staticStyle:{color:"#409EFF"}})],1)],1)],1)],1)],1)]),a("div",{staticClass:"main-wrapper"},[a("inventory",{ref:"inventoryRef",attrs:{"search-detail":e.form,"active-name":e.activeName},on:{handleTap:e.handleTap}})],1)])},n=[]},"5f52":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("dca8"),a("d3b7");var n=r(a("c14f")),o=r(a("1da1")),i=a("6186"),l=a("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getTypeList();case 1:return a.n=2,t.getTable(e);case 2:return a.a(2)}}),a)})))()},getTypeList:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var r,n=null===(r=a[0])||void 0===r?void 0:r.Id;e.Code=a.find((function(e){return e.Id===n})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(a){(0,i.GetGridByCode)({code:e+","+t.Code}).then((function(e){var r=e.IsSucceed,n=e.Data,o=e.Message;if(r){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),n.Grid.Is_Page&&(t.queryInfo.PageSize=+n.Grid.Row_Number),a(t.columns)}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"64cd":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),o=r(a("c14f")),i=r(a("1da1"));a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var l=r(a("f16f")),u=r(a("0f97")),c=r(a("819f")),s=r(a("67b3")),f=a("3c4a");t.default={components:{DynamicDataTable:u.default,stockDialog:c.default,logDialog:s.default},mixins:[l.default],props:{searchDetail:{type:Object,default:function(){return{}}}},data:function(){return{pgLoading:!1,btnloading:!1,queryInfo:{Page:1,PageSize:20},columns:[],currentColumns:[],selectArray:[],tbData:[],tbConfig:{Op_Width:76},total:0,dialogVisible:!1,dialogTitle:"",width:0,currentComponent:"",form:{RawNameFull:"",Spec:"",Thick:"",Width:"",Length:"",Material:"",Raw_Name:"",Category_Id:"",WH_Id:"",Location_Id:"",Sys_Project_Id:"",Supplier:"",Party_Unit:"",Raw_Property:[],Big_Type:1},activeName:"1"}},watch:{activeName:function(e){switch(e){case"1":this.currentColumns=this.columns.filter((function(e){return"Spec"!==e.Code}));break;case"2":this.currentColumns=this.columns.filter((function(e){return"Thick"!==e.Code&&"Width"!==e.Code}));break;case"3":this.currentColumns=this.columns.filter((function(e){return"Thick"!==e.Code&&"Width"!==e.Code&&"Length"!==e.Code}));break;case"99":this.currentColumns=this.columns.filter((function(e){return"Thick"!==e.Code&&"Width"!==e.Code&&"Length"!==e.Code&&"Total_Count_Weight"!==e.Code&&"Total_Lock_Count_Weight"!==e.Code}));break;default:}}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("PRORawMaterialAllocation");case 1:return e.currentColumns=e.columns,t.n=2,e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{radioChange:function(e){this.activeName=e,this.queryInfo={Page:1,PageSize:20},this.$emit("handleTap",e)},tbSelectChange:function(e){this.selectArray=e},fetchData:function(e){var t=this,a=this.searchDetail,r=a.RawNameFull,o=a.Raw_Name,i=a.Spec,l=a.Thick,u=a.Width,c=a.Length,s=a.Material,d=a.Sys_Project_Id,_=a.Category_Id,m=a.WH_Id,h=a.Location_Id,p=a.Supplier,g=a.Party_Unit,P=a.Raw_Property;this.form={},this.form.RawNameFull=r,this.form.Raw_Name=o,this.form.WH_Id=m,this.form.Sys_Project_Id=d,this.form.Category_Id=_,this.form.Location_Id=h,this.form.Supplier=p,this.form.Party_Unit=g,this.form.Raw_Property=P,this.form.Spec=i,this.form.Thick=l,this.form.Width=u,this.form.Length=c,this.form.Material=s,e&&(this.queryInfo.Page=e),this.pgLoading=!0,(0,f.GetRawInventoryPageList)((0,n.default)((0,n.default)({},this.form),this.queryInfo)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.map((function(e){return e.Spec=1===e.Big_Type?e.Thick:e.Spec,e})),t.total=e.Data.TotalCount):t.$message.error(e.Message)})).finally((function(e){t.pgLoading=!1}))},generateComponent:function(e,t,a){this.dialogTitle=e,this.currentComponent=t,this.width=a,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},handleView:function(e,t){var a=this;this.generateComponent("".concat(1===t?"公共库存":"锁定库存"),"stockDialog","60%"),this.$nextTick((function(r){a.$refs.content.init(e,t,a.form.ProjectId,e.Big_Type)}))},handelLock:function(e){var t=this;this.generateComponent("转工程","stockDialog","60%"),this.$nextTick((function(a){t.$refs.content.init(e,0,t.form.ProjectId,e.Big_Type)}))},viewTransformLog:function(){this.generateComponent("转工程记录","logDialog","90%"),this.$nextTick((function(e){}))}}}},"67b3":function(e,t,a){"use strict";a.r(t);var r=a("e361"),n=a("3520");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("2ee2");var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"c0f17488",null);t["default"]=l.exports},"74fd1":function(e,t,a){"use strict";a.r(t);var r=a("1d11"),n=a("d77f");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("7974");var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"ee60db16",null);t["default"]=l.exports},7958:function(e,t,a){"use strict";a.r(t);var r=a("5bd3"),n=a("cc00");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("3673");var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"951700e4",null);t["default"]=l.exports},7974:function(e,t,a){"use strict";a("3cc6")},"819f":function(e,t,a){"use strict";a.r(t);var r=a("b06b"),n=a("dd80");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("0f0f");var i=a("2877"),l=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"b2abae7e",null);t["default"]=l.exports},8378:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CreateVersion=L,t.DelAuxCategoryEntity=C,t.DelAuxEntity=v,t.DelCategoryEntity=u,t.DelRawEntity=f,t.DeleteVersion=A,t.EditAuxEnabled=b,t.EditRawEnabled=s,t.ExportAuxForProject=q,t.ExportAuxList=k,t.ExportFindRawInAndOut=$,t.ExportInOutStoreReport=X,t.ExportPicking=M,t.ExportRawList=S,t.ExportRecSendProjectMaterialReport=Y,t.ExportRecSendProjectReport=K,t.ExportReceiving=D,t.ExportStagnationInventory=H,t.ExportStoreReport=J,t.FindInAndOutPageList=W,t.FindPickingNewPageList=j,t.FindPickingPageList=E,t.FindReceivingNewPageList=U,t.FindReceivingPageList=O,t.GetAuxCategoryDetail=P,t.GetAuxCategoryTreeList=p,t.GetAuxDetail=x,t.GetAuxFilterDataSummary=z,t.GetAuxForProjectDetail=V,t.GetAuxForProjectPageList=B,t.GetAuxPageList=w,t.GetAuxTemplate=T,t.GetAuxWHSummaryList=G,t.GetCategoryDetail=l,t.GetCategoryTreeList=o,t.GetCycleDate=F,t.GetList=R,t.GetRawDetail=_,t.GetRawPageList=d,t.GetRawTemplate=m,t.ImportAuxList=I,t.ImportRawList=h,t.SaveAuxCategoryEntity=g,t.SaveAuxEntity=y,t.SaveCategoryEntity=i,t.SaveRawEntity=c,t.UpdateVersion=N;var n=r(a("b775"));function o(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:e,timeout:12e5})}function p(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:e,timeout:12e5})}function S(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:e})}function L(e){return(0,n.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:e})}function U(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:e})}function B(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:e})}function V(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:e})}function q(e){return(0,n.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:e})}function z(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:e})}function H(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:e})}function J(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:e})}function K(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:e})}function Y(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:e})}function X(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:e})}},"90d1":function(e,t){e.exports={WEIGHT_DECIMAL:5,INBOUND_DETAIL_UNIT_PRICE_DECIMAL:6,DETAIL_TOTAL_PRICE_DECIMAL:2,COUNT_DECIMAL:2,UNIT_WEIGHT_DECIMAL:100,TAX_MODE:0,SUMMARY_FIELDS:["Theory_Weight","InStoreWeight","Voucher_Weight","InStoreCount","TaxTotalPrice","NoTaxTotalPrice","TaxPrice"],INBOUND_DETAIL_HIDE_FIELDS:{isPurchase:["PartyUnitName"],isCustomer:["PurchaseNo","SupplierName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"],isManual:["PurchaseNo","PartyUnitName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"]},INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS:["SupplierName","ProjectName","Material","Tax_Rate","NoTaxUnitPrice","TaxUnitPrice"],INBOUND_DETAIL_SUMMARY_FIELDS:["InStoreCount","InStoreWeight","Voucher_Weight","NoTaxAllPrice","Tax_All_Price","Adjust_Amount","Tax","Theory_Weight"],OutBOUND_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","AvailableCount","AvailableWeight","NoTaxAllPrice","Tax_All_Price","Out_Store_Weight","Out_Store_Count","Returned_Weight","Returned_Count","InStoreCount","InStoreWeight"],Return_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","NoTaxAllPrice","Tax_All_Price","AvailableCount","AvailableWeight","Voucher_Weight"]}},"95aa":function(e,t,a){},b06b:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog-wapper"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{flex:"1",height:"100%"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.currentColumns,config:e.tbConfig,data:e.tbData,border:"",stripe:"",height:"100%"},scopedSlots:e._u([{key:"Furnace_Batch_No",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Furnace_Batch_No||"-"))])]}},{key:"Purchase_Ready_Lock_Count",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Purchase_Ready_Lock_Count||0===r.Purchase_Ready_Lock_Count?r.Purchase_Ready_Lock_Count:"-"))])]}},{key:"In_Store_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.In_Store_Weight||0===r.In_Store_Weight?(r.In_Store_Weight/1e3).toFixed(5)/1:"-"))])]}},{key:"Unlock_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Unlock_Weight||0===r.Unlock_Weight?(r.Unlock_Weight/1e3).toFixed(5)/1:"-"))])]}},{key:"Lock_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Lock_Weight||0===r.Lock_Weight?(r.Lock_Weight/1e3).toFixed(5)/1:"-"))])]}},{key:"Tax_Unit_Price",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Tax_Unit_Price||0===r.Tax_Unit_Price?(1e3*r.Tax_Unit_Price).toFixed(2)/1:"-"))])]}},{key:"Tax_All_Price",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Tax_All_Price||0===r.Tax_All_Price?r.Tax_All_Price.toFixed(2)/1:"-"))])]}},{key:"Tax_All_Price_New",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Tax_All_Price_New||0===r.Tax_All_Price_New?r.Tax_All_Price_New.toFixed(2)/1:"-"))])]}},{key:"Unlock_Count",fn:function(t){var r=t.row,n=t.column;return[a("div",[n.Is_Edit?a("el-input",{staticClass:"input-number",attrs:{type:"number",placeholder:"请输入",step:"any",min:0,max:r.In_Store_Count?r.In_Store_Count:""},on:{input:function(t){return e.changeUnlockCount(t,r)}},model:{value:r.Unlock_Count,callback:function(t){e.$set(r,"Unlock_Count",e._n(t))},expression:"row.Unlock_Count"}}):a("span",[e._v(e._s(r.Unlock_Count))])],1)]}},{key:"Lock_Count",fn:function(t){var r=t.row;return[a("div",[a("el-input",{staticClass:"input-number",attrs:{step:"any",type:"number",placeholder:"请输入",min:0,max:r.In_Store_Count?r.In_Store_Count:""},on:{input:function(t){return e.changeLockCount(t,r)}},model:{value:r.Lock_Count,callback:function(t){e.$set(r,"Lock_Count",e._n(t))},expression:"row.Lock_Count"}})],1)]}},{key:"Tax_Unit_Price_New",fn:function(t){var r=t.row,n=t.column;return[a("div",[n.Is_Edit?a("el-input",{staticClass:"input-number",attrs:{type:"number",step:"any",placeholder:"请输入",min:0},on:{input:function(t){return e.changeTaxUnitPrice(t,r)}},model:{value:r.Tax_Unit_Price_New,callback:function(t){e.$set(r,"Tax_Unit_Price_New",e._n(t))},expression:"row.Tax_Unit_Price_New"}}):a("span",[e._v(e._s(r.Tax_Unit_Price_New))])],1)]}},{key:"Affiliation_Project_Name",fn:function(t){var r=t.row;return[a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:!Boolean(r.Lock_Count)},on:{change:function(t){return e.projectChange(r)}},model:{value:r.Affiliation_Project_Id,callback:function(t){e.$set(r,"Affiliation_Project_Id",t)},expression:"row.Affiliation_Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)]}}])})],1),0===e.flag?a("div",{staticClass:"footer-wrapper"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:e.handleSave}},[e._v("确定 ")])],1):e._e()])},n=[]},cc00:function(e,t,a){"use strict";a.r(t);var r=a("0d41"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},d77f:function(e,t,a){"use strict";a.r(t);var r=a("64cd"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},dd80:function(e,t,a){"use strict";a.r(t);var r=a("362c"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},e361:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog-wapper"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{flex:"1",height:"100%"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("el-form",{attrs:{inline:""}},[0==e.materialType?a("el-form-item",{attrs:{label:"原料全名"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Material_Name_All,callback:function(t){e.$set(e.form,"Material_Name_All",t)},expression:"form.Material_Name_All"}})],1):a("el-form-item",{attrs:{label:"辅料名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Material_Name_All,callback:function(t){e.$set(e.form,"Material_Name_All",t)},expression:"form.Material_Name_All"}})],1),a("el-form-item",{attrs:{label:"初始项目"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.Region_Project_Id,callback:function(t){e.$set(e.form,"Region_Project_Id",t)},expression:"form.Region_Project_Id"}})],1),a("el-form-item",{attrs:{label:"转入项目"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.To_Project_Id,callback:function(t){e.$set(e.form,"To_Project_Id",t)},expression:"form.To_Project_Id"}})],1),a("el-form-item",{attrs:{label:"转出项目"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.In_Project_Id,callback:function(t){e.$set(e.form,"In_Project_Id",t)},expression:"form.In_Project_Id"}})],1),a("el-form-item",{attrs:{label:"分配日期"}},[a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.Transfer_Date,callback:function(t){e.$set(e.form,"Transfer_Date",t)},expression:"form.Transfer_Date"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("搜索")]),a("el-button",{attrs:{type:"default"},on:{click:e.reset}},[e._v("重置")])],1)],1),a("bimtable",{ref:"table",attrs:{tablecode:e.gridCode,"custom-param":e.form,"case-conversion":!1}})],1)])},n=[]},f16f:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186");t.default={methods:{getTableConfig:function(e){var t=this;return new Promise((function(a){(0,r.GetGridByCode)({code:e}).then((function(e){var r=e.IsSucceed,n=e.Data,o=e.Message;if(r){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),n.Grid.Is_Page&&(t.queryInfo.PageSize=+n.Grid.Row_Number),a(t.columns)}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}}}]);