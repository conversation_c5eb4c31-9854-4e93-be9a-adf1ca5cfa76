(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-1e2ba770"],{1513:function(t,e,r){"use strict";r.d(e,"a",(function(){return o})),r.d(e,"b",(function(){return a}));var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ViewPage",{attrs:{"page-type":3}})},a=[]},"2bb4":function(t,e,r){"use strict";r.r(e);var o=r("2d28"),a=r.n(o);for(var u in o)["default"].indexOf(u)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(u);e["default"]=a.a},"2d28":function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(r("49b3"));e.default={name:"PRORawGoodsReturnView",components:{ViewPage:a.default},data:function(){return{}}}},5480:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getTableConfig=e.getRoleInfo=void 0,r("4de4"),r("d81d"),r("e9f5"),r("910d"),r("ab43"),r("d3b7");var o=r("6186"),a=r("c24f"),u=void 0;e.getTableConfig=function(t,e){return new Promise((function(r,a){(0,o.GetGridByCode)({code:t,businessType:e}).then((function(t){var e=t.IsSucceed,o=t.Data,a=t.Message;if(e){var n=(o.ColumnList||[]).filter((function(t){return t.Is_Display}));r(n)}else u.$message({message:a,type:"error"})}))}))},e.getRoleInfo=function(t){return new Promise((function(e,r){(0,a.RoleAuthorization)({roleType:3,menuType:1,menuId:t}).then((function(t){if(t.IsSucceed){var o=((null===t||void 0===t?void 0:t.Data)||[]).map((function(t){return t.Code}));e(o)}else u.$message({message:t.Message,type:"error"}),r(t.message)}))}))}},"8fea":function(t,e){t.exports={WEIGHT_CONVERSION:1e3,WEIGHT_DECIMAL:5,WEIGHT_KG_DECIMAL:2,COUNT_DECIMAL:2,UNIT_PRICE_DECIMAL:6,UNIT_PRICE_KG_DECIMAL:9,RawInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],RawReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],RawAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],RawOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:3}],RawAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}],AuxInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],AuxReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],AuxAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],AuxOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:2}],AuxAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}]}},"93aa":function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=j,e.AuxInStoreExport=q,e.AuxReturnByReceipt=Y,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=B,e.DeleteInStore=O,e.DeletePicking=At,e.ExportCheckReceipt=ft,e.ExportInstoreReceipt=ct,e.ExportMoneyAdjustOrder=yt,e.ExportPicking=Dt,e.ExportProcess=Wt,e.ExportTestDetail=Mt,e.FindAuxPageList=U,e.FindRawPageList=_,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=X,e.GetAuxImportTemplate=E,e.GetAuxPageList=rt,e.GetAuxPickOutStoreSubList=W,e.GetAuxProcurementDetails=ot,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=g,e.GetImportTemplate=C,e.GetInstoreDetail=R,e.GetMoneyAdjustDetailPageList=Gt,e.GetOMALatestStatisticTime=L,e.GetOrderDetail=nt,e.GetPartyAs=I,e.GetPickLockStoreToChuku=Et,e.GetPickPlate=jt,e.GetPickSelectPageList=bt,e.GetPickSelectSubList=Nt,e.GetPickingDetail=xt,e.GetPickingTypeSettingDetail=Tt,e.GetProjectListForTenant=at,e.GetRawDetailByReceipt=it,e.GetRawOrderList=ut,e.GetRawPageList=M,e.GetRawPickOutStoreSubList=N,e.GetRawProcurementDetails=G,e.GetRawSurplusReturnStoreDetail=b,e.GetReturnPlate=Vt,e.GetStoreSelectPage=Lt,e.GetSuppliers=h,e.GetTestDetail=ht,e.GetTestInStoreOrderList=Rt,e.Import=x,e.ImportCheckReceipt=mt,e.ImportInstoreReceipt=pt,e.InStoreListSummary=dt,e.LockPicking=_t,e.ManualAuxInStoreDetail=J,e.ManualInStoreDetail=S,e.MaterielAuxInStoreList=V,e.MaterielAuxManualInStore=z,e.MaterielAuxPurchaseInStore=K,e.MaterielAuxSubmitInStore=F,e.MaterielPartyAInStorel=Q,e.MaterielRawInStoreList=u,e.MaterielRawInStoreListInSumNew=i,e.MaterielRawInStoreListNew=n,e.MaterielRawManualInStore=D,e.MaterielRawPartyAInStore=k,e.MaterielRawPurchaseInStore=y,e.MaterielRawSubmitInStore=l,e.MaterielRawSurplusInStore=w,e.OutStoreListSummary=st,e.PartAInStoreDetail=m,e.PartyAInInStoreDetail=$,e.PurchaseAuxInStoreDetail=H,e.PurchaseInStoreDetail=p,e.RawInStoreExport=T,e.RawReturnByReceipt=lt,e.RawSurplusReturnStore=v,e.SaveInStore=A,e.SavePicking=Ct,e.SetQualified=It,e.SetTestDetail=gt,e.StoreMoneyAdjust=Pt,e.SubmitApproval=f,e.SubmitAuxApproval=c,e.SubmitInStore=St,e.SubmitPicking=wt,e.SurplusInStoreDetail=P,e.UnLockPicking=vt,e.UpdateInvoiceInfo=kt,e.Withdraw=d,e.WithdrawAux=s,e.WithdrawChecked=Ot;var a=o(r("b775"));function u(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function n(t){return(0,a.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function R(t){return(0,a.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function O(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function I(t){return(0,a.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function G(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function k(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function D(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function w(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function A(t){return(0,a.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function L(t){return(0,a.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function C(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function x(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function T(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function _(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function v(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function b(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function N(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function E(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function j(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function V(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function W(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function F(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function B(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function U(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function H(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function $(t){return(0,a.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function J(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function K(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function Q(t){return(0,a.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function z(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function q(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function X(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function Y(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,a.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function rt(t){return(0,a.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function ot(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function at(t){return(0,a.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function ut(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function nt(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function it(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function lt(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function dt(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function st(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function ct(t){return(0,a.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function ft(t){return(0,a.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function pt(t){return(0,a.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function mt(t){return(0,a.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function St(t){return(0,a.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function Pt(t){return(0,a.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function Rt(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function Ot(t){return(0,a.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function ht(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function It(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function gt(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function Mt(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function Gt(t){return(0,a.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function yt(t){return(0,a.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function kt(t){return(0,a.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function Dt(t){return(0,a.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function wt(t){return(0,a.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function At(t){return(0,a.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function Lt(t){return(0,a.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function Ct(t){return(0,a.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function xt(t){return(0,a.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function Tt(t){return(0,a.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function _t(t){return(0,a.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function vt(t){return(0,a.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function bt(t){return(0,a.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function Nt(t){return(0,a.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function Et(t){return(0,a.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function jt(t){return(0,a.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function Vt(t){return(0,a.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function Wt(t){return(0,a.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},9643:function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProjectSendingInfo=M,e.CancelFlow=$,e.DeleteProjectSendingInfo=l,e.EditProjectSendingInfo=c,e.ExportComponentStockOutInfo=D,e.ExportInvoiceList=U,e.ExportSendSteel=f,e.ExportSendingDetailInfoList=p,e.GetLocationList=_,e.GetProduceCompentEntity=h,e.GetProducedPartToSendPageList=b,e.GetProjectAcceptInfoPagelist=E,e.GetProjectSendingAllCount=P,e.GetProjectSendingInfoAndItemPagelist=A,e.GetProjectSendingInfoLogPagelist=v,e.GetProjectSendingInfoPagelist=i,e.GetProjectsendinginEntity=s,e.GetReadyForDeliverSummary=w,e.GetReadyForDeliveryComponentPageList=k,e.GetReadyForDeliveryPageList=y,e.GetReturnHistoryPageList=j,e.GetSendToReturnPageList=L,e.GetStockOutBillInfoPageList=x,e.GetStockOutDetailList=R,e.GetStockOutDetailPageList=C,e.GetStockOutDocEntity=G,e.GetStockOutDocPageList=n,e.GetWaitingStockOutPageList=O,e.GetWarehouseListOfCurFactory=T,e.GetWeighingReviewList=V,e.SaveStockOut=g,e.SubmitApproval=H,e.SubmitProjectSending=d,e.SubmitReturnToStockIn=I,e.SubmitWeighingForPC=F,e.Transforms=m,e.TransformsByType=N,e.TransformsWithoutWeight=S,e.WeighingReviewSubmit=W,e.WithdrawDraft=B;var a=o(r("b775")),u=o(r("4328"));function n(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDocPageList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoPagelist",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/ComponentStockOut/DeleteProjectSendingInfo",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitProjectSending",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectsendinginEntity",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/ComponentStockOut/EditProjectSendingInfo",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportSendSteel",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportSendingDetailInfoList",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/ComponentStockOut/Transforms",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/ComponentStockOut/TransformsWithoutWeight",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingAllCount",method:"post",data:t})}function R(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailList",method:"post",data:u.default.stringify(t)})}function O(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetWaitingStockOutPageList",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProduceCompentEntity",method:"post",data:t})}function I(t){return(0,a.default)({url:"/PRO/ComponentReturn/SubmitReturnToStockIn",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SaveStockOut",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/ComponentStockOut/AddProjectSendingInfo",method:"post",data:t})}function G(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDocEntity",method:"post",data:u.default.stringify(t)})}function y(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryPageList",method:"post",data:t})}function k(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryComponentPageList",method:"post",data:t})}function D(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportComponentStockOutInfo",method:"post",data:u.default.stringify(t)})}function w(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliverSummary",method:"post",data:u.default.stringify(t)})}function A(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoAndItemPagelist",method:"post",data:t})}function L(t){return(0,a.default)({url:"/PRO/ComponentReturn/GetSendToReturnPageList",method:"post",data:t})}function C(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailPageList",method:"post",data:t})}function x(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutBillInfoPageList",method:"post",data:t})}function T(t){return(0,a.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function _(t){return(0,a.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function v(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoLogPagelist",method:"post",data:t})}function b(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProducedPartToSendPageList",method:"post",data:t})}function N(t){return(0,a.default)({url:"/PRO/ComponentStockOut/TransformsByType",method:"post",data:t})}function E(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectAcceptInfoPagelist",method:"post",data:t})}function j(t){return(0,a.default)({url:"/PRO/ComponentReturn/GetReturnHistoryPageList",method:"post",data:t})}function V(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetWeighingReviewList",method:"post",data:t})}function W(t){return(0,a.default)({url:"/PRO/ComponentStockOut/WeighingReviewSubmit",method:"post",data:t})}function F(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitWeighingForPC",method:"post",data:t})}function B(t){return(0,a.default)({url:"/PRO/ComponentStockOut/WithdrawDraft",method:"post",data:t})}function U(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportInvoiceList",method:"post",data:t})}function H(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitApproval",method:"post",data:t})}function $(t){return(0,a.default)({url:"/Sys/FlowInstances/CancelFlow",method:"post",data:t})}},c704:function(t,e,r){"use strict";r.r(e);var o=r("1513"),a=r("2bb4");for(var u in a)["default"].indexOf(u)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(u);var n=r("2877"),i=Object(n["a"])(a["default"],o["a"],o["b"],!1,null,"3b622da0",null);e["default"]=i.exports}}]);