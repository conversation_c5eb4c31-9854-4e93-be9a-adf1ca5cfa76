(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7930a295"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=r(),s=e-i,u=20,l=0;t="undefined"===typeof t?500:t;var c=function(){l+=u;var e=Math.easeInOutQuad(l,i,s,t);o(e),l<t?n(c):a&&"function"===typeof a&&a()};c()}},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,s=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var u=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),u=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=u.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"2fc4":function(e,t,a){"use strict";a.r(t);var n=a("6993"),o=a("a4f8");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("6f5d");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"3ba765e3",null);t["default"]=s.exports},"5ec6":function(e,t,a){},6993:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"归属基地:"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",loading:e.selectloading},on:{change:e.changeValue},model:{value:e.form.FactoryId,callback:function(t){e.$set(e.form,"FactoryId",t)},expression:"form.FactoryId"}},e._l(e.factoryOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"登记部门:",placeholder:"请选择"}},[a("el-select",{attrs:{clearable:"",disabled:!e.form.FactoryId},model:{value:e.form.DepartId,callback:function(t){e.$set(e.form,"DepartId",t)},expression:"form.DepartId"}},e._l(e.departmentlist,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"项目搜索:"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:e.form.Keywords,callback:function(t){e.$set(e.form,"Keywords",t)},expression:"form.Keywords"}})],1),a("el-form-item",{attrs:{label:"核算状态:"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},[a("el-option",{attrs:{label:"未核算",value:1}}),a("el-option",{attrs:{label:"核算中",value:2}}),a("el-option",{attrs:{label:"已核算",value:3}})],1)],1),a("el-form-item",{attrs:{label:"发包单位:"}},[a("el-input",{attrs:{placeholder:"输入发包单位"},model:{value:e.form.Contract_Unit,callback:function(t){e.$set(e.form,"Contract_Unit",t)},expression:"form.Contract_Unit"}})],1),a("el-form-item",{attrs:{label:"供料方式:"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Feeding_Mode,callback:function(t){e.$set(e.form,"Feeding_Mode",t)},expression:"form.Feeding_Mode"}},e._l(e.FeedingMode,(function(e,t){return a("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"核算日期:",prop:"StartDate"}},[a("el-date-picker",{attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.InStoreDate,callback:function(t){e.$set(e.form,"InStoreDate",t)},expression:"form.InStoreDate"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.fetchData(1)}}},[e._v("查询")]),a("el-button",{on:{click:e.resetForm}},[e._v("重置")])],1)],1),a("el-row",{attrs:{type:"flex",justify:"space-between"}},[a("el-col",{staticClass:"green info",attrs:{span:5}},[a("span",[e._v("累计报量-已核算:"),a("span",{staticClass:"red"},[e._v(e._s(e._f("filterNum")(e.Accounting_Dose)))]),e._v("T")])]),a("el-col",{staticClass:"green info",attrs:{span:5}},[a("span",[e._v("累计金额-已核算:"),a("span",{staticClass:"red"},[e._v(e._s(e._f("filterNum")(e.Accounting_Price)))]),e._v("元")])]),a("el-col",{staticClass:"bule info",attrs:{span:5}},[a("span",[e._v("累计报量-未核算:"),a("span",{staticClass:"red"},[e._v(e._s(e._f("filterNum")(e.Unaccounted_Dose)))]),e._v("T")])]),a("el-col",{staticClass:"bule info",attrs:{span:5}},[a("span",[e._v("累计金额-未核算:"),a("span",{staticClass:"red"},[e._v(e._s(e._f("filterNum")(e.Unaccounted_Price)))]),e._v("元")])])],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"info-wrapper"},[a("h4",[e._v("数据列表")]),a("div",{staticClass:"btn-x"},[e.roleList.includes("ServiceRegisterAdd")?a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleOpen("add",null)}}},[e._v("劳务产值报量登记")]):e._e(),e.roleList.includes("ServiceRegisterExprot")?a("el-button",{attrs:{loading:e.lonloading},on:{click:e.exportReport}},[e._v("导出报表")]):e._e(),e.roleList.includes("ServiceRegisterImport")?a("el-button",{on:{click:e.handleImport}},[e._v("导入报表")]):e._e()],1)]),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["Accounting_Date"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d}"))+" ")]}}:"Create_Date"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d} {h}:{i}:{s} "))+" ")]}}:"Status"===t.Code?{key:"default",fn:function(t){var n=t.row;return[1===n.Status?a("el-tag",{attrs:{type:"danger"}},[e._v("未核算")]):e._e(),2===n.Status?a("el-tag",{attrs:{type:"warning"}},[e._v("核算中")]):e._e(),3===n.Status?a("el-tag",{attrs:{type:"success"}},[e._v("已核算")]):e._e()]}}:"Invoice_Type"===t.Code?{key:"default",fn:function(t){var n=t.row;return[1===n.Invoice_Type?a("span",[e._v("增值税普通发票")]):e._e(),2===n.Invoice_Type?a("span",[e._v("增值税专用发票")]):e._e(),0===n.Invoice_Type?a("span",[e._v("无发票")]):e._e()]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("displayValue")(n[t.Code]))+" ")]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e.roleList.includes("ServiceRegisterView")&&e.permissionList.includes(n.Department_Type)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleOpen("view",n)}}},[e._v("查看")]):e._e(),e.getButtonEdit(n)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleOpen("edit",n)}}},[e._v("编辑")]):e._e(),e.getButtonDelete(n)?a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(n)}}},[e._v(" 删除 ")]):e._e()]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)])],1),a("m-dialog",{attrs:{"dialog-title":"劳务产值导入",visible:e.dialogShow,"dialog-width":"680px",top:"20vh",hidebtn:""},on:{"update:visible":function(t){e.dialogShow=t},handleClose:e.handleClosebatch}},[a("div",{staticStyle:{padding:"5px"}},[a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:e.exportclick}},[e._v("点击此处下载导入模板")])],1),a("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(t){return e.handleClosebatch()}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.hanLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)])],1)},o=[]},"699e":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteLabourRegistration=s,t.EditLabourRegistration=l,t.ExportLabourRegistrationlist=c,t.LabourRegistratioDetails=u,t.LabourRegistrationImport=d,t.LabourRegistrationPageList=r,t.SaveLabourRegistration=i;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/OMA/LabourRegistration/PageList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/OMA/LabourRegistration/Save",method:"post",data:e})}function s(e){return(0,o.default)({url:"/OMA/LabourRegistration/Delete",method:"post",data:e})}function u(e){return(0,o.default)({url:"/OMA/LabourRegistration/Get",method:"post",data:e})}function l(e){return(0,o.default)({url:"/OMA/LabourRegistration/Update",method:"post",data:e})}function c(e){return(0,o.default)({url:"/OMA/LabourRegistration/Export",method:"post",data:e})}function d(e){return(0,o.default)({url:"/oma/LabourRegistration/Import",method:"post",data:e})}},"6f5d":function(e,t,a){"use strict";a("5ec6")},7757:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.FeeRegistrationImport=f,t.GetBusinessLastUpdateDate=c,t.GetFactoryProcessLibs=l,t.GetFactoryProjectList=s,t.GetFirstLevelDepartsUnderFactory=i,t.GetNonExternalFactory=r,t.GetOMALatestAccountingDate=m,t.GetQueryNonExternalFactory=d,t.GetReportLastDate=u;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/oma/Common/GetNonExternalFactory",method:"post",data:e})}function i(e){return(0,o.default)({url:"/oma/Common/GetFirstLevelDepartsUnderFactory",method:"post",data:e})}function s(e){return(0,o.default)({url:"/oma/Common/GetFactoryProjectList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/oma/Common/GetReportLastDate",method:"post",data:e})}function l(e){return(0,o.default)({url:"/oma/Common/GetFactoryProcessLibs",method:"post",data:e})}function c(e){return(0,o.default)({url:"/oma/Common/GetBusinessLastUpdateDate",method:"post",data:e})}function d(e){return(0,o.default)({url:"/oma/Common/GetQueryNonExternalFactory",method:"post",data:e})}function f(e){return(0,o.default)({url:"/oma/FeeRegistration/Import",method:"post",data:e})}function m(e){return(0,o.default)({url:"/oma/common/GetOMALatestAccountingDate",method:"post",data:e})}},"8cdf":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BusinessPageList=r,t.DeleteIncome=c,t.EditIncome=d,t.ExportList=f,t.GetFactoryProjectList=s,t.GetFirstLevelDepartsUnderFactory=i,t.IncomeDetails=l,t.IncomeRegistrationImport=m,t.SaveIncome=u;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/OMA/IncomeRegistration/PageList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/oma/common/GetFirstLevelDepartsUnderFactory",method:"post",data:e})}function s(e){return(0,o.default)({url:"oma/common/GetFactoryProjectList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/OMA/IncomeRegistration/Save",method:"post",data:e})}function l(e){return(0,o.default)({url:"/OMA/IncomeRegistration/Get",method:"post",data:e})}function c(e){return(0,o.default)({url:"/OMA/IncomeRegistration/Delete",method:"post",data:e})}function d(e){return(0,o.default)({url:"/OMA/IncomeRegistration/Update",method:"post",data:e})}function f(e){return(0,o.default)({url:"/OMA/IncomeRegistration/Export",method:"post",data:e})}function m(e){return(0,o.default)({url:"/oma/IncomeRegistration/Import",method:"post",data:e})}},a4f8:function(e,t,a){"use strict";a.r(t);var n=a("ca85"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},be22:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ConfirmFee=d,t.CostAccountingRegistrationPageList=s,t.DeleteCostAccountingRegistration=c,t.ExportCostAccountingRegistration=f,t.GetFeeEntity=u,t.GetUserDepartTypePermission=m,t.SaveNewFee=i,t.UpdateFee=l,t.UpdatePaymentList=r;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/oma/FeeRegistration/UpdatePaymentList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/oma/FeeRegistration/SaveNewFee",method:"post",data:e})}function s(e){return(0,o.default)({url:"/oma/FeeRegistration/CostAccountingRegistrationPageList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/oma/FeeRegistration/GetFeeEntity",method:"post",data:e})}function l(e){return(0,o.default)({url:"/oma/FeeRegistration/UpdateFee",method:"post",data:e})}function c(e){return(0,o.default)({url:"/oma/FeeRegistration/DeleteCostAccountingRegistration",method:"post",data:e})}function d(e){return(0,o.default)({url:"/oma/FeeRegistration/ConfirmFee",method:"post",data:e})}function f(e){return(0,o.default)({url:"/oma/FeeRegistration/ExportCostAccountingRegistration",method:"post",data:e})}function m(e){return(0,o.default)({url:"/oma/FeeRegistration/GetUserDepartTypePermission",method:"post",data:e})}},ca85:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1"));a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("2532"),a("3ca3"),a("841c"),a("ddb0");var s=n(a("3796")),u=n(a("65b1")),l=a("cf45"),c=(a("8975"),a("ed08")),d=n(a("6612")),f=n(a("15ac")),m=n(a("333d")),p=a("c685"),g=a("8cdf"),h=a("699e"),v=a("be22"),y=a("c24f"),b=a("7757"),_=n(a("2082"));t.default={name:"PROServiceRegister",components:{Pagination:m.default,mDialog:u.default,Upload:s.default},filters:{filterNum:function(e){return(0,d.default)(e).format("0,0.[00]")}},mixins:[_.default,f.default],data:function(){return{hanLoading:!1,lonloading:!1,dialogShow:!1,addPageArray:[{path:this.$route.path+"/view",hidden:!0,component:function(){return a.e("chunk-42832da4").then(a.bind(null,"1497"))},name:"PROServiceRegisterAdd",meta:{title:"劳务产值登记"}}],selectloading:!1,btnloading:!1,disabled:!0,curUserDep:"",tbLoading:!1,factoryOption:[],columns:[],departmentlist:[],FeedingMode:[],roleList:[],permissionList:[],Unaccounted_Dose:0,Accounting_Dose:0,Unaccounted_Price:0,Accounting_Price:0,form:{FactoryId:"",DepartId:"",Keywords:"",Payer:"",IncomeType:"",Status:"",InStoreDate:[]},search:function(){return{}},tbData:[],tablePageSize:p.tablePageSize,queryInfo:{Page:1,PageSize:p.tablePageSize[0]},total:0}},computed:{},activated:function(){this.fetchData()},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.fetchData();case 1:return e.getFactory(),t.n=2,e.getUserDepartTypePermission();case 2:return t.n=3,e.getTableConfig("PROServiceRegisterPageList");case 3:return t.n=4,e.getRoleAuthorization();case 4:return e.search=(0,c.debounce)(e.fetchData,800,!0),t.n=5,(0,l.getDictionary)("Materialsupplymethod");case 5:e.FeedingMode=t.v;case 6:return t.a(2)}}),t)})))()},methods:{handleImport:function(){this.dialogShow=!0},handleClosebatch:function(){this.dialogShow=!1},handleSubmit:function(){this.$refs.upload.handleSubmit()},beforeUpload:function(e){var t=this,a=new FormData;a.append("Files",e),this.hanLoading=!0,(0,h.LabourRegistrationImport)(a).then((function(e){e.IsSucceed?(t.fetchData(1),t.$message({type:"success",message:"导入成功"}),t.dialogShow=!1):(t.$message({type:"error",message:e.Message}),e.Data&&window.open((0,c.combineURL)(t.$baseUrl,e.Data))),t.hanLoading=!1}))},exportclick:function(){var e=(0,c.combineURL)(this.$baseUrl,"/Template/OMA/劳务产值登记导入模板.xlsx");window.open(e,"_blank")},getUserDepartTypePermission:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,v.GetUserDepartTypePermission)({});case 1:a=t.v,a.IsSucceed?e.permissionList=a.Data||[]:e.$message.error(a.Mesaage);case 2:return t.a(2)}}),t)})))()},getButtonEdit:function(e){if((this.curUserDep===e.Department_name||this.$store.getters.userId===e.Create_UserId||this.permissionList.includes(e.Department_Type))&&"未核算"==e.Accounting_Status_Name&&this.roleList.includes("ServiceRegisterEdit"))return!0},getButtonDelete:function(e){if((this.curUserDep===e.Department_Name||this.$store.getters.userId===e.Create_UserId||this.permissionList.includes(e.Department_Type))&&"未核算"==e.Accounting_Status_Name&&this.roleList.includes("ServiceRegisterDelete"))return!0},changeValue:function(e){this.form.DepartId="",this.getFirstLevelDepartsUnderFactory(e)},getFirstLevelDepartsUnderFactory:function(e){var t=this;return(0,i.default)((0,r.default)().m((function a(){var n;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,g.GetFirstLevelDepartsUnderFactory)({FactoryId:e});case 1:n=a.v,n.IsSucceed?(t.departmentlist=n.Data||[],t.disabled=!1):t.message.error(n.Mesaage);case 2:return a.a(2)}}),a)})))()},fetchData:function(e){var t=this;this.tbLoading=!0,this.form.StartDate=this.form.InStoreDate?(0,c.parseTime)(this.form.InStoreDate[0],"{y}-{m}-{d}"):null,this.form.EndDate=this.form.InStoreDate?(0,c.parseTime)(this.form.InStoreDate[1],"{y}-{m}-{d}"):null,e&&(this.queryInfo.Page=e),(0,h.LabourRegistrationPageList)((0,o.default)((0,o.default)({},this.form),this.queryInfo)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.DataList||[],t.total=e.Data.TotalCount,t.curUserDep=e.Data.Cur_User_Depart,t.Unaccounted_Dose=e.Data.Data.Unaccounted_Dose,t.Accounting_Dose=e.Data.Data.Accounting_Dose,t.Unaccounted_Price=e.Data.Data.Unaccounted_Price,t.Accounting_Price=e.Data.Data.Accounting_Price,t.tbLoading=!1):t.$message({message:e.Message,type:"error"})}))},getRoleAuthorization:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,y.RoleAuthorization)({roleType:3,menuType:1,menuId:e.$route.meta.Id});case 1:a=t.v,a.IsSucceed?e.roleList=a.Data.map((function(e){return e.Code})):e.$message({type:"warning",message:a.Message});case 2:return t.a(2)}}),t)})))()},exportReport:function(){var e=this;this.lonloading=!0,(0,h.ExportLabourRegistrationlist)((0,o.default)({},this.form)).then((function(t){t.IsSucceed?(window.open((0,c.combineURL)(e.$baseUrl,t.Data),"_blank"),t.Message&&e.$alert(t.Message,"导出通知",{confirmButtonText:"我知道了"})):e.$message.error(t.Message)})).finally((function(t){e.lonloading=!1}))},pageChange:function(){this.fetchData()},resetForm:function(){this.form={},this.disabled=!0},handleOpen:function(e,t){t="add"==e?{}:t,this.$router.push({name:"PROServiceRegisterAdd",query:{pg_redirect:"PROServiceRegister",type:e,Id:t.Id}})},handleDelete:function(e){var t=this;this.$confirm("此操作将永久删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,h.DeleteLabourRegistration)({id:e.Id}).then((function(e){e.IsSucceed?(t.fetchData(),t.$message.success("删除成功")):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},getFactory:function(){var e=this;this.selectloading=!0,(0,b.GetQueryNonExternalFactory)({}).then((function(t){t.IsSucceed?(e.selectloading=!1,e.factoryOption=t.Data||[]):e.$message({message:t.Message,type:"error"})}))}}}},cf45:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=o,a("d3b7");var n=a("6186");function o(e){return new Promise((function(t,a){(0,n.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}}}]);