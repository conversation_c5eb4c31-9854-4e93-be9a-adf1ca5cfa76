(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2622d1da"],{"155b":function(e,t,a){"use strict";a.r(t);var s=a("f0cb"),o=a.n(s);for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(n);t["default"]=o.a},"2f43":function(e,t,a){"use strict";var s=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetItemList=r,t.GetRuleList=i,t.UpdateSetting=u;var o=s(a("b775")),n=s(a("4328"));function i(){return(0,o.default)({url:"/PRO/Setting/GetRuleList",method:"post"})}function r(e){return(0,o.default)({url:"/PRO/Setting/GetItemList",method:"post",data:n.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/Setting/UpdateSetting",method:"post",data:e})}},"326d":function(e,t,a){"use strict";a.r(t);var s=a("c3506"),o=a.n(s);for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(n);t["default"]=o.a},"33a0":function(e,t,a){"use strict";a.r(t);var s=a("bcaf"),o=a("41ee");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("8329");var i=a("2877"),r=Object(i["a"])(o["default"],s["a"],s["b"],!1,null,"3f83c2d4",null);t["default"]=r.exports},"41ee":function(e,t,a){"use strict";a.r(t);var s=a("b23c"),o=a.n(s);for(var n in s)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(n);t["default"]=o.a},"5a82":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return o}));var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-container"},[a("div",{staticClass:"inner-x"},[e.hiddenPart?e._e():a("div",[a("div",{staticClass:"dfx"},[a("div",[a("i",{staticClass:"iconfont icon-material-filled cs-blue"}),a("strong",[e._v(e._s(e.partName))])]),a("div",[a("el-switch",{attrs:{"inner-text":""},model:{value:e.partIsTrue,callback:function(t){e.partIsTrue=t},expression:"partIsTrue"}})],1)]),a("div",{staticStyle:{"text-align":"right"}},[a("el-select",{attrs:{clearable:"",placeholder:"选择工艺"},model:{value:e.processId,callback:function(t){e.processId=t},expression:"processId"}},e._l(e.processOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)]),e.hiddenPart?e._e():a("el-divider"),a("div",{staticClass:"dfx"},[a("div",[a("i",{staticClass:"iconfont icon-steel cs-blue"}),a("strong",[e._v(e._s(e.comName))])]),a("div",[a("el-switch",{attrs:{"inner-text":""},model:{value:e.comIsTrue,callback:function(t){e.comIsTrue=t},expression:"comIsTrue"}})],1),a("div",{staticStyle:{"text-align":"right"}},[a("el-select",{attrs:{clearable:"",placeholder:"选择工艺"},model:{value:e.processComId,callback:function(t){e.processComId=t},expression:"processComId"}},e._l(e.processComOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)]),a("el-divider"),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:e.fetchData}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保 存")])],1)],1)])},o=[]},8329:function(e,t,a){"use strict";a("c795")},"851f":function(e,t,a){"use strict";a.r(t);var s=a("9c7f"),o=a("326d");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("8a8c");var i=a("2877"),r=Object(i["a"])(o["default"],s["a"],s["b"],!1,null,"3550b621",null);t["default"]=r.exports},"8a8c":function(e,t,a){"use strict";a("caa7")},"9c7f":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return o}));var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 dfx cs-z-flex-pd16-wrap"},[a("div",{staticClass:"fff left  cs-z-shadow"},[e._m(0),a("main",{staticClass:"cs-left-main"},[a("ul",{on:{click:e.handleClick}},e._l(e.option,(function(t){return a("li",{key:t,class:{active:e.selectValue===t}},[e._v(e._s(t))])})),0)])]),a("div",{staticClass:"fff right cs-z-shadow"},[a("header",{staticClass:"cs-header"},[a("strong",[e._v(e._s(e.selectValue))]),a("main",{staticClass:"cs-main"},[e.selectValue===e.option[0]?a("process",{attrs:{title:e.selectValue}}):e._e(),e.selectValue===e.option[1]?a("manage",{attrs:{title:e.selectValue}}):e._e()],1)])])])},o=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("header",{staticClass:"cs-header"},[a("strong",[e._v("规则设置")])])}]},a024:function(e,t,a){"use strict";var s=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=l,t.AddProessLib=w,t.AddTechnology=u,t.AddWorkingProcess=r,t.DelLib=F,t.DeleteProcess=L,t.DeleteProcessFlow=I,t.DeleteTechnology=P,t.DeleteWorkingTeams=R,t.GetAllProcessList=f,t.GetCheckGroupList=W,t.GetChildComponentTypeList=N,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=S,t.GetFactoryWorkingTeam=v,t.GetGroupItemsList=T,t.GetLibList=i,t.GetLibListType=V,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=p,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=C,t.GetProcessListWithUserBase=x,t.GetProcessWorkingTeamBase=$,t.GetTeamListByUser=M,t.GetTeamProcessList=b,t.GetWorkingTeam=y,t.GetWorkingTeamBase=k,t.GetWorkingTeamInfo=D,t.GetWorkingTeams=_,t.GetWorkingTeamsPageList=G,t.SaveWorkingTeams=O,t.UpdateProcessTeam=g;var o=s(a("b775")),n=s(a("4328"));function i(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function r(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:n.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:n.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:n.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:n.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:n.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:n.default.stringify(e)})}function m(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:n.default.stringify(e)})}function p(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:n.default.stringify(e)})}function v(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:n.default.stringify(e)})}function b(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:n.default.stringify(e)})}function T(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:n.default.stringify(e)})}function I(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:n.default.stringify(e)})}function P(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:n.default.stringify(e)})}function L(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:n.default.stringify(e)})}function k(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:n.default.stringify(e)})}function C(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:n.default.stringify(e)})}function x(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a5c4:function(e,t,a){"use strict";a("c7ad5")},b23c:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=a("2f43");t.default={props:{title:{type:String,default:""}},data:function(){return{isTrue:!1,Rule_Name:""}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var e=this;(0,s.GetItemList)({ruleName:this.title}).then((function(t){var a,s,o;t.IsSucceed?(e.Rule_Name=null===(a=t.Data[0])||void 0===a?void 0:a.Rule_Name,e.isTrue=null===(s=t.Data[0])||void 0===s?void 0:s.Is_Enabled,e.Id=null===(o=t.Data[0])||void 0===o?void 0:o.Id):e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(e){var t=this,a=[{Id:this.Id,Is_Enabled:this.isTrue}];(0,s.UpdateSetting)(a).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))}}}},bcaf:function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return o}));var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-container"},[a("div",{staticClass:"inner-x"},[a("div",{staticClass:"dfx"},[a("div",[a("i",{staticClass:"iconfont icon-pre-zfj cs-blue"}),a("strong",[e._v(e._s(e.Rule_Name))])]),a("div",[a("el-switch",{attrs:{"inner-text":""},on:{change:e.handleSubmit},model:{value:e.isTrue,callback:function(t){e.isTrue=t},expression:"isTrue"}})],1)])])])},o=[]},c251:function(e,t,a){"use strict";a.r(t);var s=a("5a82"),o=a("155b");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("a5c4");var i=a("2877"),r=Object(i["a"])(o["default"],s["a"],s["b"],!1,null,"65452e69",null);t["default"]=r.exports},c3506:function(e,t,a){"use strict";var s=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=s(a("c251")),n=s(a("33a0")),i=a("2f43");t.default={name:"PROBasicRules",components:{Process:o.default,Manage:n.default},data:function(){return{selectValue:"",option:[]}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var e=this;(0,i.GetRuleList)({}).then((function(t){t.IsSucceed?(e.option=t.Data,e.selectValue=e.option[0]):e.$message({message:t.Message,type:"error"})}))},handleClick:function(e){this.selectValue=e.target.innerText}}}},c795:function(e,t,a){},c7ad5:function(e,t,a){},caa7:function(e,t,a){},f0cb:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=a("2f43"),o=a("a024"),n=a("7f9d");t.default={props:{title:{type:String,default:""}},data:function(){return{processId:"",processComId:"",comIsTrue:"",partIsTrue:"",processOption:[],processComOption:[],partName:"",comName:"",hiddenPart:""}},mounted:function(){this.checkUserFactory(),this.fetchData(),this.init()},methods:{fetchData:function(){var e=this;(0,s.GetItemList)({ruleName:this.title}).then((function(t){var a,s,o,n,i,r,u,l,c,d,f,m,h,p,g,v,y;t.IsSucceed?"零件默认工艺"==(null===(a=t.Data[0])||void 0===a?void 0:a.Item_Name)?(e.processId=null===(s=t.Data[0])||void 0===s?void 0:s.Item_Value,e.partId=null===(o=t.Data[0])||void 0===o?void 0:o.Id,e.partName=null===(n=t.Data[0])||void 0===n?void 0:n.Item_Name,e.partIsTrue=null===(i=t.Data[0])||void 0===i?void 0:i.Is_Enabled,e.processComId=null===(r=t.Data[1])||void 0===r?void 0:r.Item_Value,e.comIsTrue=null===(u=t.Data[1])||void 0===u?void 0:u.Is_Enabled,e.comName=null===(l=t.Data[1])||void 0===l?void 0:l.Item_Name,e.comId=null===(c=t.Data[1])||void 0===c?void 0:c.Id):(e.processId=null===(d=t.Data[1])||void 0===d?void 0:d.Item_Value,e.partId=null===(f=t.Data[1])||void 0===f?void 0:f.Id,e.partName=null===(m=t.Data[1])||void 0===m?void 0:m.Item_Name,e.partIsTrue=null===(h=t.Data[1])||void 0===h?void 0:h.Is_Enabled,e.processComId=null===(p=t.Data[0])||void 0===p?void 0:p.Item_Value,e.comIsTrue=null===(g=t.Data[0])||void 0===g?void 0:g.Is_Enabled,e.comName=null===(v=t.Data[0])||void 0===v?void 0:v.Item_Name,e.comId=null===(y=t.Data[0])||void 0===y?void 0:y.Id):e.$message({message:t.Message,type:"error"})}))},checkUserFactory:function(){var e=this;(0,n.GetTenantFactoryType)({}).then((function(t){t.IsSucceed?"2"===t.Data&&(e.hiddenPart=!0):e.$message({message:t.Message,type:"error"})}))},init:function(){var e=this;(0,o.GetLibList)({type:2}).then((function(t){t.IsSucceed?e.processOption=t.Data:e.$message({message:t.Message,type:"error"})})),(0,o.GetLibList)({type:1}).then((function(t){t.IsSucceed?e.processComOption=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;if(!this.hiddenPart&&this.partIsTrue&&!this.processId||this.comIsTrue&&!this.processComId)this.$message({message:"请选择工艺",type:"warning"});else{var t=[{Id:this.comId,Item_Value:this.processComId,Is_Enabled:this.comIsTrue}];this.hiddenPart||t.unshift({Id:this.partId,Item_Value:this.processId,Is_Enabled:this.partIsTrue}),(0,s.UpdateSetting)(t).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))}}}}}}]);