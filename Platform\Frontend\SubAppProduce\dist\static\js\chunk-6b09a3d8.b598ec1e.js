(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6b09a3d8"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=r(),s=e-i,l=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=l;var e=Math.easeInOutQuad(u,i,s,t);o(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},1276:function(e,t,a){"use strict";var n=a("c65b"),o=a("e330"),r=a("d784"),i=a("825a"),s=a("861d"),l=a("1d80"),u=a("4840"),c=a("8aa5"),d=a("50c4"),f=a("577e"),m=a("dc4a"),h=a("14c3"),p=a("9f7f"),g=a("d039"),P=p.UNSUPPORTED_Y,b=4294967295,v=Math.min,_=o([].push),y=o("".slice),T=!g((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var a="ab".split(e);return 2!==a.length||"a"!==a[0]||"b"!==a[1]})),L="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;r("split",(function(e,t,a){var o="0".split(void 0,0).length?function(e,a){return void 0===e&&0===a?[]:n(t,this,e,a)}:t;return[function(t,a){var r=l(this),i=s(t)?m(t,e):void 0;return i?n(i,t,r,a):n(o,f(r),t,a)},function(e,n){var r=i(this),s=f(e);if(!L){var l=a(o,r,s,n,o!==t);if(l.done)return l.value}var m=u(r,RegExp),p=r.unicode,g=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(P?"g":"y"),T=new m(P?"^(?:"+r.source+")":r,g),C=void 0===n?b:n>>>0;if(0===C)return[];if(0===s.length)return null===h(T,s)?[s]:[];var k=0,I=0,w=[];while(I<s.length){T.lastIndex=P?0:I;var x,D=h(T,P?y(s,I):s);if(null===D||(x=v(d(T.lastIndex+(P?I:0)),s.length))===k)I=c(s,I,p);else{if(_(w,y(s,k,I)),w.length===C)return w;for(var G=1;G<=D.length-1;G++)if(_(w,D[G]),w.length===C)return w;I=k=x}}return _(w,y(s,k)),w}]}),L||!T,P)},1344:function(e,t,a){"use strict";a("677d")},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,s=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"2c7e":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a732"),a("b64b"),a("d3b7"),a("ac1f"),a("1276"),a("159b");var o=n(a("2909")),r=n(a("5530")),i=n(a("c14f")),s=n(a("1da1")),l=n(a("bae6")),u=n(a("1463")),c=n(a("333d")),d=n(a("53b2")),f=n(a("b519")),m=a("c685"),h=a("3166"),p=a("a024"),g=a("8ab4"),P=n(a("15ac")),b=a("7f9d"),v=a("0e9a"),_=n(a("e8ae")),y=a("21a6"),T="$_$";t.default={name:"PROPartsWarehouse",components:{ExpandableSection:l.default,TreeDetail:u.default,Pagination:c.default,Consume:d.default,Record:f.default},mixins:[P.default],data:function(){return{showExpand:!0,treeData:[],treeLoading:!1,expandedKey:"",projectName:"",statusType:"",currentNode:{},customParams:{Sys_Project_Id:"",Project_Id:"",Area_Id:"",Working_Team_Id:"",Working_Process_Id:"",Code_Like:"",Component:"",InstallUnit_Id:"",Waiting_For_Shipment:null},installUnitIdNameList:[],processList:[],teamList:[],activeName:"first",columns:[],secondColumns:[],firstColumns:[],tbData:[],total:0,rightLoading:!1,pgLoading:!1,tbLoading:!1,tablePageSize:m.tablePageSize,queryInfo:{Page:1,PageSize:10},dialogVisible:!1,currentComponent:"",title:"",width:"300",selectList:[],drawer:!1,iframeKey:"",fullscreenid:"",iframeUrl:"",fullbimid:"",fileBim:"",btnloading:!1,WaitingForShipmentData:[{label:"是",value:!0},{label:"否",value:!1}]}},computed:{filterText:function(){return this.projectName+T+this.statusType},nowColumns:function(){return"first"===this.activeName?this.firstColumns:this.secondColumns},Component_Codes:function(){return this.customParams.Component?this.customParams.Component.split("\n"):[]}},mounted:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.pgLoading=!0,t.n=1,e.getTableConfig("first"===e.activeName?"PROProducedPartPool":"PROPartDelivery");case 1:return t.n=2,e.fetchTreeData();case 2:return t.a(2)}}),t)})))()},methods:{fetchTreeData:function(){var e=this;this.treeLoading=!0,(0,h.GetProjectAreaTreeList)({MenuId:this.$route.meta.Id,type:5}).then((function(t){if(t.IsSucceed){if(0===t.Data.length)return void(e.treeLoading=!1);var a=t.Data;a.map((function(e){return 0===e.Children.length?e.Data.Is_Imported=!1:(e.Data.Is_Imported=e.Children.some((function(e){return!0===e.Data.Is_Imported})),e.Is_Directory=!0,e.Children.map((function(e){e.Children.length>0&&(e.Is_Directory=!0)}))),e})),e.treeData=a,0===Object.keys(e.currentNode).length?e.setKey():e.handleNodeClick(e.currentNode)}})).finally((function(){e.treeLoading=!1}))},setKey:function(){var e=this,t=function(a){for(var n=0;n<a.length;n++){var o=a[n],r=o.Data,i=o.Children;if(r.ParentId&&(null===i||void 0===i||!i.length))return e.currentNode=r,void e.handleNodeClick(o);if(i&&i.length>0)return t(i)}};return t(this.treeData)},handleNodeClick:function(e){this.currentNode=e,this.expandedKey=e.Id,this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id,this.customParams.Project_Id=e.Data.Project_Id,this.customParams.Area_Id=e.Data.Id,this.customParams.Area_Id&&(this.pgLoading=!0,this.getInstallUnitIdNameList(this.customParams.Area_Id,e)),this.handleClick()},fetchData:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a="first"===e.activeName?g.GetProducedPartPool:g.GetPartDeliveryPageList,e.tbLoading=!0,t.n=1,a((0,r.default)((0,r.default)((0,r.default)({},e.customParams),e.queryInfo),{},{Component_Codes:e.Component_Codes})).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message.error(t.Message)})).finally((function(){e.tbLoading=!1,e.pgLoading=!1,e.rightLoading=!1}));case 1:return t.a(2)}}),t)})))()},handleClick:function(e,t){var a=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:if(a.rightLoading=!0,a.selectList=[],a.$nextTick((function(){a.$refs.customParams.resetFields()})),"second"!==a.activeName){e.n=3;break}return e.n=1,a.getProcessListBase();case 1:return e.n=2,a.getWorkingTeams(a.customParams.Working_Process_Id);case 2:a.setDefaultTeam();case 3:return e.n=4,a.getTableConfig("first"===a.activeName?"PROProducedPartPool":"PROPartDelivery");case 4:return e.n=5,a.fetchData();case 5:return e.a(2)}}),e)})))()},handelsearch:function(e){var t=this;return(0,s.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(!e){a.n=3;break}if(t.$refs.customParams.resetFields(),t.teamList=[],"second"!==t.activeName){a.n=3;break}return a.n=1,t.getProcessListBase();case 1:return a.n=2,t.getWorkingTeams(t.customParams.Working_Process_Id);case 2:t.setDefaultTeam();case 3:return t.selectList=[],t.queryInfo.Page=1,a.n=4,t.fetchData();case 4:return a.a(2)}}),a)})))()},setDefaultTeam:function(){this.customParams.Working_Team_Id=this.teamList[0].Id},getInstallUnitIdNameList:function(e,t){var a=this;""===e||t.Children.length>0?this.installUnitIdNameList=[]:(0,h.GetInstallUnitIdNameList)({Area_Id:e}).then((function(e){a.installUnitIdNameList=e.Data}))},getProcessListBase:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,p.GetProcessList)({type:1}).then((function(t){if(t.IsSucceed){var a,n={Name:"未分配",Id:"未分配"};e.processList=t.Data,e.processList.push(n),e.customParams.Working_Process_Id=null===(a=t.Data[0])||void 0===a?void 0:a.Id}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},handleProcessChange:function(e){var t=this;return(0,s.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(t.customParams.Working_Team_Id="",!e){a.n=2;break}return a.n=1,t.getWorkingTeams(e);case 1:t.setDefaultTeam(),a.n=3;break;case 2:t.teamList=[];case 3:return a.a(2)}}),a)})))()},getWorkingTeams:function(e){var t=this;return(0,s.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,p.GetWorkingTeamBase)({processId:e}).then((function(e){e.IsSucceed?t.teamList=e.Data:t.$message({message:e.Message,type:"error"})}));case 1:return a.a(2)}}),a)})))()},handleClose:function(){this.dialogVisible=!1},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handelConsume:function(){var e=this;this.generateComponent("消耗",d.default),this.$nextTick((function(){e.$refs["content"].init(e.selectList)}))},handelRecord:function(e,t){var a=this;this.generateComponent(1===e?"转入记录":"消耗记录",f.default),this.$nextTick((function(){a.$refs["content"].init(e,t)}))},tbSelectChange:function(e){this.selectList=e.records},handleExport:function(){var e=this;this.btnloading=!0;var t=this.$refs.xTable.getCheckboxRecords();this.$refs.xTable.exportData({filename:"零件配送表",type:"xlsx",data:t,columnFilterMethod:function(e){var t=e.column;return!!t.field}}).then((function(){e.btnloading=!1}))},exportDataToExcel:function(e,t,a){return(0,s.default)((0,i.default)().m((function n(){var o,r,s;return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return o=new _.default.Workbook,r=o.addWorksheet("Sheet 1"),r.columns=t.map((function(e){return{header:e.Display_Name,key:e.Code,width:e.Width/10}})),e.forEach((function(e){var a=t.map((function(t){return e[t.Code]}));r.addRow(a)})),n.n=1,o.xlsx.writeBuffer();case 1:s=n.v,(0,y.saveAs)(new Blob([s]),"".concat(a,".xlsx"));case 2:return n.a(2)}}),n)})))()},handleDwg:function(e){var t=this;(0,b.GetDwg)({Part_Id:e.Part_Aggregate_Id}).then((function(e){if(e.IsSucceed){var a,n=(null===e||void 0===e||null===(a=e.Data)||void 0===a?void 0:a.length)&&e.Data[0].File_Url;window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+(0,v.parseOssUrl)(n),"_blank")}else t.$message({message:e.Message,type:"error"})}))},checCheckboxkMethod3:function(e){var t=e.row;return"first"!==this.activeName||t.Can_Allocate_Count>0},customFilterFun:function(e,t,a){var n=e.split(T),r=n[0],i=n[1];if(!e)return!0;var s=a.parent,l=[a.label],u=[t.Data.Part_Use_Status],c=1;while(c<a.level)l=[].concat((0,o.default)(l),[s.label]),u=[].concat((0,o.default)(u),[t.Data.Part_Use_Status]),s=s.parent,c++;l=l.filter((function(e){return!!e})),u=u.filter((function(e){return!!e}));var d=!0,f=!0;return this.statusType&&(f=u.some((function(e){return-1!==e.indexOf(i)}))),this.projectName&&(d=l.some((function(e){return-1!==e.indexOf(r)}))),d&&f}}}},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=c,t.GeAreaTrees=L,t.GetFileSync=I,t.GetInstallUnitIdNameList=T,t.GetNoBindProjectList=h,t.GetPartDeepenFileList=C,t.GetProjectAreaTreeList=y,t.GetProjectEntity=l,t.GetProjectList=s,t.GetProjectPageList=i,t.GetProjectTemplate=p,t.GetPushProjectPageList=_,t.GetSchedulingPartList=k,t.IsEnableProjectMonomer=d,t.SaveProject=u,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=P,t.UpdateProjectTemplateContract=b,t.UpdateProjectTemplateOther=v;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function I(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},3512:function(e,t,a){"use strict";a("f702")},"46c1":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("13d5"),a("e9f5"),a("910d"),a("9485"),a("a9e3"),a("b680"),a("d3b7");var o=a("8ab4"),r=n(a("15ac"));t.default={mixins:[r.default],data:function(){return{tbLoading:!1,tbData:[],columns:[],queryInfo:{PageSize:10},partName:"",totalNum:0,totalWeight:0,type:1,Waiting_For_Shipment:""}},mounted:function(){},methods:{init:function(e,t){var a=this;this.partName=t.Part_Code,this.Waiting_For_Shipment=t.Waiting_For_Shipment,this.type=e,1===e?this.getTableConfig("PROTransferRecord"):this.getTableConfig("PROConsumptionRecord").then((function(e){"是"===t.Waiting_For_Shipment?a.columns=a.columns.filter((function(e){return"Comp_Code"!==e.Code})):"否"===t.Waiting_For_Shipment&&(a.columns=a.columns.filter((function(e){return"Send_Code"!==e.Code})))})),this.fetchData(e,t.Part_Aggregate_Id)},fetchData:function(e,t){var a=this,n=1===e?o.GetAggregatePartProducedHistory:o.GetAggregatePartUseRecordHistory;this.tbLoading=!0;var r=1===e?"Actual_Transfer_Count":"Use_Count";n({Part_Aggregate_Id:t}).then((function(e){a.tbData=e.Data,a.totalNum=e.Data.reduce((function(e,t){return e+Number(t[r])}),0);var t=e.Data.reduce((function(e,t){return e+t[r]*t.Weight}),0);a.totalWeight=parseFloat((t/1e3).toFixed(3))})).finally((function(){a.tbLoading=!1}))}}}},"4dc4":function(e,t,a){"use strict";a.r(t);var n=a("2c7e"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"53b2":function(e,t,a){"use strict";a.r(t);var n=a("ed1e"),o=a("fa6d");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("3512");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"3be76080",null);t["default"]=s.exports},"677d":function(e,t,a){},8447:function(e,t,a){"use strict";a.r(t);var n=a("c598"),o=a("4dc4");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("b17be");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"14b8b286",null);t["default"]=s.exports},"87f1":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("2909"));a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("159b"),a("ddb0");var r=n(a("15ac")),i=a("a024"),s=a("8ab4");t.default={name:"Consume",mixins:[r.default],props:{processList:{type:Array,default:function(){return[]}}},data:function(){return{tbLoading:!1,tbData:[],columns:[{Code:"Part_Code",Display_Name:"零件名称"},{Code:"Working_Team_Id",Display_Name:"消耗班组"},{Code:"Stock_Count",Display_Name:"可消耗数量"},{Code:"Use_Count",Display_Name:"消耗数量"}],queryInfo:{PageSize:-1},workTeamsOption:[],Working_Team_Id:"",btnLoading:!1}},computed:{},created:function(){this.getWorkingTeams()},methods:{init:function(e){var t=this;this.tbData=e.map((function(e){return t.$set(e,"Working_Team_Id",""),t.$set(e,"Use_Count",0),e}))},changeAllWorkingTeam:function(e){this.tbData.forEach((function(t){t.Working_Team_Id=e}))},changeSingleWorkingTeam:function(e){var t=(0,o.default)(new Set(this.tbData.map((function(e){return e.Working_Team_Id}))));1===t.length?this.Working_Team_Id=t[0]:this.Working_Team_Id=""},getWorkingTeams:function(){var e=this;(0,i.GetWorkingTeams)({type:1}).then((function(t){t.IsSucceed?e.workTeamsOption=t.Data:e.$message({message:t.Message,type:"error"})}))},confirm:function(){var e=this,t=this.tbData.map((function(e){var t={Part_Aggregate_Id:e.Part_Aggregate_Id,Working_Team_Id:e.Working_Team_Id,Use_Count:e.Use_Count};return t})).filter((function(e){return e.Use_Count>0&&e.Working_Team_Id}));0!==t.length?(this.btnLoading=!0,(0,s.AggregatePartUse)(t).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"领用成功"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))):this.$message({type:"info",message:"请至少填写完成一条数据"})},cancel:function(){this.$emit("close")}}}},"88a3":function(e,t,a){"use strict";a.r(t);var n=a("46c1"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"8ab4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AggregatePartUse=l,t.GetAggregatePartProducedHistory=i,t.GetAggregatePartUseRecordHistory=s,t.GetPartDeliveryPageList=u,t.GetProducedPartPool=r;var o=n(a("b775"));n(a("4328"));function r(e){return(0,o.default)({url:"/PRO/Productiontask/GetProducedPartPool",method:"post",data:e})}function i(e){return(0,o.default)({url:"/pro/productiontask/GetAggregatePartProducedHistory",method:"post",data:e})}function s(e){return(0,o.default)({url:"/pro/productiontask/GetAggregatePartUseRecordHistory",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Productiontask/AggregatePartUse",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Productiontask/GetPartDeliveryPageList",method:"post",data:e})}},a024:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=S,t.AddTechnology=l,t.AddWorkingProcess=s,t.DelLib=N,t.DeleteProcess=L,t.DeleteProcessFlow=y,t.DeleteTechnology=T,t.DeleteWorkingTeams=w,t.GetAllProcessList=f,t.GetCheckGroupList=R,t.GetChildComponentTypeList=j,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=W,t.GetFactoryWorkingTeam=P,t.GetGroupItemsList=_,t.GetLibList=i,t.GetLibListType=A,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=p,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=G,t.GetProcessListWithUserBase=O,t.GetProcessWorkingTeamBase=F,t.GetTeamListByUser=U,t.GetTeamProcessList=v,t.GetWorkingTeam=b,t.GetWorkingTeamBase=D,t.GetWorkingTeamInfo=x,t.GetWorkingTeams=C,t.GetWorkingTeamsPageList=k,t.SaveWorkingTeams=I,t.UpdateProcessTeam=g;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function m(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function p(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function P(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function b(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function v(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function _(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function y(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function T(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function L(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function D(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function G(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function O(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},b17be:function(e,t,a){"use strict";a("f9e6")},b519:function(e,t,a){"use strict";a.r(t);var n=a("d9a2"),o=a("88a3");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("1344");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"48be507a",null);t["default"]=s.exports},c598:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"h100 app-wrapper",attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff",attrs:{width:300},model:{value:e.showExpand,callback:function(t){e.showExpand=t},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[a("div",{staticClass:"tree-search"},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"请选择"},model:{value:e.statusType,callback:function(t){e.statusType=t},expression:"statusType"}},[a("el-option",{attrs:{label:"未消耗完成",value:"未消耗完成"}}),a("el-option",{attrs:{label:"已消耗完成",value:"已消耗完成"}}),a("el-option",{attrs:{label:"零件未生产",value:"零件未生产"}})],1),a("el-input",{attrs:{placeholder:"搜索...",size:"small",clearable:"","suffix-icon":"el-icon-search"},model:{value:e.projectName,callback:function(t){e.projectName="string"===typeof t?t.trim():t},expression:"projectName"}})],1),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":e.customFilterFun,loading:e.treeLoading,"tree-data":e.treeData,"show-status":"","show-detail":"","can-node-click":!1,"filter-text":e.filterText,"expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick},scopedSlots:e._u([{key:"csLabel",fn:function(t){var n=t.showStatus,o=t.data;return[o.ParentNodes?e._e():a("span",{staticClass:"cs-blue"},[e._v("("+e._s(o.Code)+")")]),e._v(e._s(o.Label)+" "),n?[o.Data["Part_Use_Status"]?a("i",{class:["已消耗完成"==o.Data["Part_Use_Status"]?"fourGreen":"未消耗完成"==o.Data["Part_Use_Status"]?"fourOrange":"零件未生产"==o.Data["Part_Use_Status"]?"fourRed":""]},[a("span",[e._v("("+e._s(o.Data["Part_Use_Status"])+")")])]):e._e()]:e._e()]}}])})],1)])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.rightLoading,expression:"rightLoading"}],staticClass:"cs-right",attrs:{"element-loading-text":"加载中"}},[a("div",{staticClass:"container"},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"零件池",name:"first"}}),a("el-tab-pane",{attrs:{label:"零件配送表",name:"second"}})],1),a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.customParams,"label-width":"100px"}},["first"===e.activeName?[a("el-form-item",{attrs:{label:"所属构件名",prop:"Component"}},[a("el-input",{attrs:{type:"textarea",placeholder:"换行多个查询"},model:{value:e.customParams.Component,callback:function(t){e.$set(e.customParams,"Component",t)},expression:"customParams.Component"}})],1),a("el-form-item",{attrs:{label:"零件名称",prop:"Code_Like"}},[a("el-input",{attrs:{placeholder:"请输入零件名称"},model:{value:e.customParams.Code_Like,callback:function(t){e.$set(e.customParams,"Code_Like",t)},expression:"customParams.Code_Like"}})],1),a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"250px"},attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!Boolean(e.customParams.Area_Id)},model:{value:e.customParams.InstallUnit_Id,callback:function(t){e.$set(e.customParams,"InstallUnit_Id",t)},expression:"customParams.InstallUnit_Id"}},e._l(e.installUnitIdNameList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"是否直发",prop:"Waiting_For_Shipment"}},[a("el-select",{staticStyle:{width:"250px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.customParams.Waiting_For_Shipment,callback:function(t){e.$set(e.customParams,"Waiting_For_Shipment",t)},expression:"customParams.Waiting_For_Shipment"}},e._l(e.WaitingForShipmentData,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]:[a("el-form-item",{attrs:{label:"配送工序",prop:"Working_Process_Id"}},[a("el-select",{attrs:{placeholder:"请选择",filterable:""},on:{change:e.handleProcessChange},model:{value:e.customParams.Working_Process_Id,callback:function(t){e.$set(e.customParams,"Working_Process_Id",t)},expression:"customParams.Working_Process_Id"}},e._l(e.processList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"配送班组",prop:"Working_Team_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:"",disabled:!Boolean(e.customParams.Working_Process_Id)||"未分配"===e.customParams.Working_Process_Id},model:{value:e.customParams.Working_Team_Id,callback:function(t){e.$set(e.customParams,"Working_Team_Id",t)},expression:"customParams.Working_Team_Id"}},e._l(e.teamList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"所属构件名",prop:"Component"}},[a("el-input",{attrs:{type:"textarea",placeholder:"换行多个查询"},model:{value:e.customParams.Component,callback:function(t){e.$set(e.customParams,"Component",t)},expression:"customParams.Component"}})],1)],a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handelsearch()}}},[e._v("搜索")]),a("el-button",{on:{click:function(t){return e.handelsearch("reset")}}},[e._v("重置")])],1)],2)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},["first"===e.activeName?a("el-button",{attrs:{type:"primary",disabled:0===e.selectList.length},on:{click:e.handelConsume}},[e._v("消耗")]):a("el-button",{attrs:{type:"success",disabled:0===e.selectList.length,loading:e.btnloading},on:{click:e.handleExport}},[e._v("导出")])],1),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0},"checkbox-config":{checkField:"checked",trigger:"row",checkMethod:e.checCheckboxkMethod3}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:t.Column_Id,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width,width:"auto"},scopedSlots:e._u(["Part_Code"===t.Code?{key:"default",fn:function(n){var o=n.row;return[a("div",[a("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[a("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])])]}}],null,!0)})})),"first"===e.activeName?a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"180","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return t.stopPropagation(),e.handelRecord(1,n)}}},[e._v("转入记录")]),a("el-divider",{attrs:{direction:"vertical"}}),a("el-button",{attrs:{type:"text"},on:{click:function(t){return t.stopPropagation(),e.handelRecord(2,n)}}},[e._v("消耗记录")])]}}],null,!1,2356300104)}):e._e()],2)],1),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)],1)])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"process-list":e.processList},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e()],1)},o=[]},d9a2:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"inner-wapper"},[a("div",{staticClass:"info"},[a("span",[e._v("零件名称: "),a("i",[e._v(e._s(e.partName))])]),a("span",[e._v(" 共"+e._s(1===e.type?"转入":"消耗")+": "),a("i",[e._v(e._s(e.totalNum)+"件")])]),a("span",[e._v(" 总重: "),a("i",[e._v(e._s(e.totalWeight)+"t")])])]),a("div",{staticClass:"table-wapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t,n){return a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width,width:"auto"},scopedSlots:e._u(["Type"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("div",[a("span",[e._v(e._s(1===n.Type?"齐套自动消耗":2===n.Type?"手动消耗":4===n.Type?"发货消耗":"系统自动消耗"))])])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[a("span",[e._v(e._s(e._f("displayValue")(o[t.Code])))])])]}}],null,!0)})})),1)],1)])},o=[]},e41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=c,t.GetPartsList=s,t.GetProjectAreaTreeList=r,t.ImportParts=u,t.SaveProjectAreaSort=i;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},ed1e:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"inner-wapper"},[a("div",{staticClass:"info"},[a("span",{staticClass:"title"},[e._v("批量选择消耗班组")]),a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},on:{change:e.changeAllWorkingTeam},model:{value:e.Working_Team_Id,callback:function(t){e.Working_Team_Id=t},expression:"Working_Team_Id"}},e._l(e.workTeamsOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("div",{staticClass:"table-wapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width,width:"auto"},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return["Working_Team_Id"===t.Code?a("div",[a("vxe-select",{attrs:{transfer:""},on:{change:e.changeSingleWorkingTeam},model:{value:o.Working_Team_Id,callback:function(t){e.$set(o,"Working_Team_Id",t)},expression:"row.Working_Team_Id"}},e._l(e.workTeamsOption,(function(e){return a("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):"Use_Count"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,max:o.Stock_Count,type:"number"},model:{value:o[t.Code],callback:function(a){e.$set(o,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):a("div",[a("span",[e._v(e._s(e._f("displayValue")(o[t.Code])))])])]}}],null,!0)})})),1)],1),a("div",{staticClass:"btn-wapper"},[a("el-button",{on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.confirm}},[e._v("领用")])],1)])},o=[]},f702:function(e,t,a){},f9e6:function(e,t,a){},fa6d:function(e,t,a){"use strict";a.r(t);var n=a("87f1"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a}}]);