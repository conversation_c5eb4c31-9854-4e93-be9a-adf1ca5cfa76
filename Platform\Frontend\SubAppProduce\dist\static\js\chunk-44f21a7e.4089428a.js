(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-44f21a7e"],{"00da":function(e,t,a){"use strict";a("6389")},"06e8":function(e,t,a){"use strict";a.r(t);var r=a("183046"),n=a("4756");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("00da");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"2d50a999",null);t["default"]=s.exports},"0d79":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3"),a("b680");var r=a("ed08");t.default={data:function(){return{tbLoading:!1,tbData:[],rootColumns:[]}},methods:{init:function(e){this.rootColumns=e},setData:function(e){var t=(0,r.deepClone)(e);this.tbData=t},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},checkOutWeight:function(e){var t=e.ReturnCount,a=e.ReturnWidth,r=e.ReturnLength,n=e.Specific_Gravity,i=e.Spec,o=1;1===e.BigType&&(o="花纹板"===e.CategoryName?t*a*r*n:t*a*r*n*i),2===e.BigType&&(o=t*r*n),e.ReturnStoreWeight=Number(o).toFixed(2)/1}}}},183046:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"辅料名称:",prop:"RawName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格:",prop:"Spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质:",prop:"Material"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商:",prop:"SupplierName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.SupplierName,callback:function(t){e.$set(e.form,"SupplierName","string"===typeof t?t.trim():t)},expression:"form.SupplierName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"甲方单位:",prop:"PartyUnitName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.PartyUnitName,callback:function(t){e.$set(e.form,"PartyUnitName","string"===typeof t?t.trim():t)},expression:"form.PartyUnitName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"辅料属性",prop:"Raw_Property"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择"},model:{value:e.form.Raw_Property,callback:function(t){e.$set(e.form,"Raw_Property",t)},expression:"form.Raw_Property"}},[a("el-option",{attrs:{label:"自采",value:1}}),a("el-option",{attrs:{label:"甲供",value:2}}),a("el-option",{attrs:{label:"代购",value:3}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("SelectProject",{model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse"}},[a("SelectWarehouse",{attrs:{type:1},model:{value:e.form.Warehouse,callback:function(t){e.$set(e.form,"Warehouse",t)},expression:"form.Warehouse"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"库位",prop:"Location"}},[a("SelectLocation",{attrs:{"warehouse-id":e.form.Warehouse},model:{value:e.form.Location,callback:function(t){e.$set(e.form,"Location",t)},expression:"form.Location"}})],1)],1),a("el-col",{attrs:{span:6}},[a("div",{staticStyle:{padding:"0 0 10px 0"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)])],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["AvailableCount"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(0===r[t.Code]?"0":r[t.Code]||"-")+" ")]}}:"AvailableWeight"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(0===r[t.Code]?"0":r[t.Code]||"-")+" ")]}}:"InStoreDate"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]}}:"RawName"===t.Code?{key:"default",fn:function(t){var r=t.row;return[a("div",[r.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),e._v(" "),r.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),e._v(e._s(r.RawName)+" ")],1)]}}:"OutStoreCount"===t.Code?{key:"default",fn:function(r){var n=r.row;return[a("vxe-input",{attrs:{min:0,max:n.AvailableCount,type:"number"},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,e._n(a))},expression:"row[item.Code]"}})]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},n=[]},2245:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ActiveAuxMaterial=R,t.ActiveRawMaterial=g,t.DeleteAuxMaterial=C,t.DeleteMaterialCategory=d,t.DeleteMaterials=c,t.DeleteRawMaterial=b,t.DeleteWarehouseReceipt=k,t.ExportPurchaseDetail=j,t.GetAuxMaterialEntity=y,t.GetAuxMaterialPageList=v,t.GetAuxStandardsList=N,t.GetAuxWarehouseReceiptEntity=T,t.GetMaterialCategoryList=u,t.GetMaterialImportPageList=o,t.GetPurchaseDetail=W,t.GetPurchaseDetailList=A,t.GetRawMaterialEntity=h,t.GetRawMaterialPageList=p,t.GetRawStandardsList=P,t.GetRawWarehouseReceiptEntity=O,t.GetWarehouseReceiptPageList=I,t.ImportMatAux=S,t.ImportMatAuxRcpt=F,t.ImportMatRaw=w,t.ImportMatRawRcpt=$,t.ImportMaterial=l,t.MaterialDataTemplate=s,t.SaveAuxMaterialEntity=D,t.SaveAuxWarehouseReceipt=M,t.SaveMaterialCategory=f,t.SaveRawMaterialEntity=m,t.SaveRawWarehouseReceipt=x,t.SubmitWarehouseReceipt=L,t.TemplateDownload=_;var n=r(a("b775")),i=r(a("4328"));function o(e){return(0,n.default)({url:"/PRO/Material/GetMaterialImportPageList",method:"post",data:e})}function s(){return(0,n.default)({url:"/PRO/Material/MaterialDataTemplate",method:"post"})}function l(e){return(0,n.default)({url:"/PRO/Material/ImportMaterial",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Material/DeleteMaterials",method:"post",data:i.default.stringify(e)})}function u(e){return(0,n.default)({url:"/PRO/Material/GetMaterialCategoryList",method:"post",params:e})}function d(e){return(0,n.default)({url:"/PRO/Material/DeleteMaterialCategory",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Material/SaveMaterialCategory",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Material/SaveRawMaterialEntity",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Material/GetRawMaterialEntity",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Material/GetRawMaterialPageList",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Material/ActiveRawMaterial",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Material/DeleteRawMaterial",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialPageList",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialEntity",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Material/TemplateDownload",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/Material/ImportMatRaw",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Material/ImportMatAux",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Material/SaveAuxMaterialEntity",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Material/ActiveAuxMaterial",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/Material/DeleteAuxMaterial",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Material/GetWarehouseReceiptPageList",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Material/GetRawStandardsList",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/Material/SaveRawWarehouseReceipt",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Material/DeleteWarehouseReceipt",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/Material/GetRawWarehouseReceiptEntity",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/Material/SubmitWarehouseReceipt",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Material/GetAuxWarehouseReceiptEntity",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/Material/SaveAuxWarehouseReceipt",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/Material/GetAuxStandardsList",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/Material/ImportMatRawRcpt",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/Material/ImportMatAuxRcpt",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetail",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/Material/ExportPurchaseDetail",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetailList",method:"post",data:e})}},2325:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("点击此处下载导入模板")])],1),a("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},n=[]},"28b0":function(e,t,a){"use strict";a.r(t);var r=a("bbd2"),n=a("edce");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("7fc7");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"370349c8",null);t["default"]=s.exports},"32ed":function(e,t,a){"use strict";a("73ed")},3973:function(e,t,a){},4558:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("841c");var o=a("ed08"),s=a("9002"),l=a("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},4756:function(e,t,a){"use strict";a.r(t);var r=a("8bb9"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"53c4":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],a("el-col",{attrs:{span:2}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(0===r[t.Code]?"按需使用":1===r[t.Code]?"使用标准规格":2===r[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},n=[]},"59cf":function(e,t,a){"use strict";a.r(t);var r=a("e571"),n=a("cdc6");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"4681fc5e",null);t["default"]=s.exports},"5c3e":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("2909")),i=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var s=a("9002"),l=a("3166");t.default={props:{isReturn:{type:Boolean,default:!1},isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1}},data:function(){return{tbLoading:!1,multipleSelection:[],rootColumns:[],columns:[],tbData:[],texture:{},BigType:1,projectOptions:[]}},watch:{"tbData.length":{handler:function(e,t){this.tbData.map((function(e,t){return e.index=t,e}))},immediate:!0}},created:function(){return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.getProject(),t.n=1,e.columnsOption();case 1:return t.a(2)}}),t)})))()},methods:{rowProjectChange:function(e,t,a){var r;a.Pick_Project_Name=null===(r=this.projectOptions.find((function(t){return t.Sys_Project_Id===e.value})))||void 0===r?void 0:r.Short_Name,this.$set(this.tbData,t,a)},getProject:function(){var e=this;(0,l.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.projectOptions=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("pro_aux_material_outbound_detail_list,Steel");case 1:a=t.v,e.rootColumns=a,e.columns=a.filter((function(e){return"PartyUnitName"!==e.Code}));case 2:return t.a(2)}}),t)})))()},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,a=this,r=e.map((function(e){return a.checkNum(e),e}));(t=this.tbData).push.apply(t,(0,n.default)(r))},checkNum:function(e){Number(e.OutStoreCount)>0||(e.OutStoreCount=e.AvailableCount)},countTaxUnitPrice:function(e){},checkCount:function(e){(e.OutStoreCount||0===e.OutStoreCount)&&e.OutStoreCount>e.AvailableCount&&(e.OutStoreCount=e.AvailableCount)}}}},"5cc7e":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[2===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"非标规格",name:"1"}},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},e._l(e.list,(function(t){return a("el-form-item",{key:t,attrs:{label:t}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:e.form[t],callback:function(a){e.$set(e.form,t,a)},expression:"form[item]"}})],1)})),1)],1):e._e(),1===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"标准规格",name:"2"}},[a("el-table",{ref:"multipleTable",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(a){return a.stopPropagation(),function(a){return e.handleRadioChange(a,t.row)}(a)}},model:{value:e.radioSelect,callback:function(t){e.radioSelect=t},expression:"radioSelect"}})]}}],null,!1,3152109164)}),a("el-table-column",{attrs:{align:"center",prop:"StandardDesc",label:"规格"}})],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},n=[]},"5dab":function(e,t,a){"use strict";a.r(t);var r=a("8b1a6"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},6389:function(e,t,a){},"6ede":function(e,t,a){"use strict";a.r(t);var r=a("b31f"),n=a("a990");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("32ed");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"0dfcd1eb",null);t["default"]=s.exports},"719d":function(e,t,a){},"72f6":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n=r(a("3796")),i=a("2245"),o=a("ed08");t.default={components:{Upload:n.default},props:{pageType:{type:Number,default:void 0}},data:function(){return{btnLoading:!1,schdulingPlanId:""}},methods:{getTemplate:function(){var e=this;(0,i.TemplateDownload)({templateType:0===this.pageType?"YLRK":"FLRK"}).then((function(t){window.open((0,o.combineURL)(e.$baseUrl,t.Data))}))},beforeUpload:function(e){var t,a=this,r=new FormData;r.append("files",e),this.btnLoading=!0,t=0===this.pageType?i.ImportMatRawRcpt:i.ImportMatAuxRcpt,t(r).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"导入成功"}),a.$emit("importData",e.Data),a.$emit("close")):a.$message({type:"error",message:e.Message}),a.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},"73ed":function(e,t,a){},"7a9a":function(e,t,a){"use strict";a("ad3d")},"7fc7":function(e,t,a){"use strict";a("c93b")},"82bf":function(e,t,a){"use strict";a.r(t);var r=a("0d79"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"8b1a6":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b"),a("14d9"),a("13d5"),a("e9f5"),a("9485"),a("d3b7");var r=a("2245");t.default={data:function(){return{activeName:"1",radioSelect:null,btnLoading:!1,form:{},list:[],tableData:[],specificationUsage:0}},watch:{specificationUsage:function(e,t){1===e&&(this.activeName="2")}},methods:{getOption:function(e){var t=this;this.specificationUsage=e.SpecificationUsage,e&&(this.list=e.RcptRawParams.split("*")),(0,r.GetRawStandardsList)({rawId:e.RawId}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleRadioChange:function(e,t){e.stopPropagation(),this.currentRow=Object.assign({},t)},submit:function(){var e=this;if("1"===this.activeName){var t=!0,a=this.list.reduce((function(a,r){if(e.form[r])return a.push(e.form[r]),a;t=!1}),[]);if(!t)return void this.$message({message:"输入数据不能为0",type:"warning"});this.$emit("standard",{type:1,val:a.join("*")})}else{if(!this.currentRow)return void this.$message({message:"请选择规格",type:"warning"});this.$emit("standard",{type:2,val:this.currentRow})}this.handleClose()},handleClose:function(){this.$emit("close")}}}},"8bb9":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),i=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("7db0"),a("caad"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7");var s=a("9002"),l=a("3c4a"),c=a("ed08"),u=r(a("bad9")),d=r(a("29d9")),f=r(a("b5b0"));t.default={components:{SelectLocation:f.default,SelectWarehouse:d.default,SelectProject:u.default},props:{pForm:{type:Object,default:function(){}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{RawName:"",Spec:"",Material:"",SupplierName:"",SysProjectId:"",Warehouse:"",Location:"",PartyUnitName:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[]}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.Store_Sub_Id===t.Store_Sub_Id}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig()},methods:{getConfig:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("pro_aux_material_outbound_detail_list,Steel");case 1:e.columns=t.v,e.columns=e.columns.filter((function(e){return!["Remark","Pick_Project_Name","OutStoreCount"].includes(e.Code)})),e.columnsOption(),e.fetchData();case 2:return t.a(2)}}),t)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.currentColumns=JSON.parse(JSON.stringify(e.columns)).filter((function(e){return"OutStoreCount"!==e.Code}));case 1:return t.a(2)}}),t)})))()},getList:function(){},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.FindStoreDetail)((0,n.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.OutStoreCount=e.AvailableCount,e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e.PurchaseCount=e.OutStoreCount,e}))),this.$emit("close")},addToList:function(){var e=(0,c.deepClone)(this.multipleSelection);this.$emit("getAddList",e.map((function(e){return e.PurchaseCount=e.OutStoreCount,e}))),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleClose:function(){this.$emit("close")}}}},"8cdf":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BusinessPageList=i,t.DeleteIncome=u,t.EditIncome=d,t.ExportList=f,t.GetFactoryProjectList=s,t.GetFirstLevelDepartsUnderFactory=o,t.IncomeDetails=c,t.IncomeRegistrationImport=m,t.SaveIncome=l;var n=r(a("b775"));function i(e){return(0,n.default)({url:"/OMA/IncomeRegistration/PageList",method:"post",data:e})}function o(e){return(0,n.default)({url:"/oma/common/GetFirstLevelDepartsUnderFactory",method:"post",data:e})}function s(e){return(0,n.default)({url:"oma/common/GetFactoryProjectList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/OMA/IncomeRegistration/Save",method:"post",data:e})}function c(e){return(0,n.default)({url:"/OMA/IncomeRegistration/Get",method:"post",data:e})}function u(e){return(0,n.default)({url:"/OMA/IncomeRegistration/Delete",method:"post",data:e})}function d(e){return(0,n.default)({url:"/OMA/IncomeRegistration/Update",method:"post",data:e})}function f(e){return(0,n.default)({url:"/OMA/IncomeRegistration/Export",method:"post",data:e})}function m(e){return(0,n.default)({url:"/oma/IncomeRegistration/Import",method:"post",data:e})}},"9adf":function(e,t,a){"use strict";a.r(t);var r=a("2325"),n=a("d852");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("bd95");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"4a50d31e",null);t["default"]=s.exports},a333:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),i=r(a("c14f")),o=r(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("c740"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("841c"),a("159b");var s=a("ed08"),l=r(a("59cf")),c=r(a("fd10")),u=r(a("06e8")),d=r(a("9adf")),f=r(a("eaf62")),m=a("3166"),h=r(a("c7ab")),p=r(a("d7a3")),g=a("8cdf"),b=a("3c4a"),v=a("a024"),y=a("cf45"),_=a("4744"),w=a("93aa"),S=a("6186"),D=r(a("c7c1")),R=r(a("f16f")),C=r(a("28b0")),I=a("e144"),P=r(a("5d4b")),x=r(a("a657")),k=r(a("8c02")),O=r(a("c13a")),L=r(a("98b2"));t.default={components:{PickSelect:L.default,SelectDepartmentUser:O.default,SelectDepartment:k.default,DynamicTableFields:x.default,SelectMaterialStoreType:P.default,AddPurchaseList:u.default,PurchaseTb:l.default,ReturnTb:D.default,ImportFile:d.default,Warehouse:C.default,Standard:f.default,AddList:c.default,OSSUpload:h.default},mixins:[p.default,R.default],props:{pageType:{type:Number,default:void 0}},data:function(){var e=this;return{isRetract:!1,returning:!1,tbLoading:!1,projectOptions:[],multipleSelection:[],factoryPeoplelist:[],departmentlist:[],ProcessList:[],AuxOutboundTypeList:[],WorkingTeamList:[],searchForm:{RawName:"",Spec:""},form:{OutStoreNo:"",OutStoreType:1,OutStoreDate:this.getDate(),Use_Processing_Id:"",Pick_Department_Id:"",SysProjectId:"",Pick_Project_Name:"",ReceiveUserId:"",WorkingTeamId:"",Remark:"",Attachment:""},rules:{OutStoreType:[{required:!0,message:"请选择",trigger:"change"}],OutStoreDate:[{required:!0,message:"请选择",trigger:"change"}],Pick_Department_Id:[{required:!0,message:"请选择",trigger:"change"}],ReceiveUserId:[{required:!0,message:"请选择",trigger:"change"}]},treeParamsDepart:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},backendDate:null,pickerOptions:{disabledDate:function(t){return t.getTime()<new Date(e.backendDate).getTime()}},currentComponent:"",title:"",dWidth:"60%",saveLoading:!1,search:function(){return{}},openAddList:!1,dialogVisible:!1,BigType:1,isProductweight:null,fileListData:[],fileListArr:[],searchNum:1,rootTableData:[],tableData:[],typeNumber1:0,typeNumber2:0,typeNumber3:0,typeNumber4:0,currentTbComponent:"",batchDialogVisible:!1,batchProjectId:""}},computed:{isAdd:function(){return 1===this.pageType},isEdit:function(){return 2===this.pageType},isView:function(){return 3===this.pageType},isPurchase:function(){return 1==this.form.OutStoreType},isReturn:function(){return 8===this.pageType},gridCode:function(){return this.isReturn?"pro_aux_material_outbound_detail_list_return":"pro_aux_material_outbound_detail_list,Steel"}},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!e.isReturn){t.n=2;break}return e.currentTbComponent=D.default,t.n=1,e.getTableConfig(e.gridCode);case 1:a=t.v,e.$nextTick((function(t){e.$refs["table"].init(a)})),t.n=3;break;case 2:e.currentTbComponent=l.default;case 3:return t.n=4,e.getCurFactory();case 4:return t.n=5,e.getOMALatestStatisticTime();case 5:return e.getPreferenceSettingValue(),t.n=6,(0,y.getDictionary)("AuxOutboundType");case 6:return e.AuxOutboundTypeList=t.v,t.n=7,e.getFirstLevelDepartsUnderFactory();case 7:e.search=(0,s.debounce)(e.fetchData,800,!0),e.getProject(),e.getProcessListBase(),e.getWorkingTeams(),e.isAdd||e.getInfo();case 8:return t.a(2)}}),t)})))()},methods:{userChange:function(e){var t=this;(0,b.GetTeamListByUserForMateriel)({id:e}).then((function(e){var a;1===(null===(a=e.Data)||void 0===a?void 0:a.length)?t.$set(t.form,"WorkingTeamId",e.Data[0].Id):t.$set(t.form,"WorkingTeamId","")}))},closeBatchDialog:function(){this.batchProjectId="",this.batchDialogVisible=!1},batchChangeProject:function(){var e=this,t=this.$refs.table.tbData;this.multipleSelection.forEach((function(a,r){var n=t.find((function(e){return e.index===a.index})),i=t.findIndex((function(e){return e.index===a.index}));n.Pick_Sys_Project_Id=e.batchProjectId,e.$set(t,i,n)})),this.closeBatchDialog()},changeColumn:function(){var e=this,t=this.currentTbComponent;this.currentTbComponent="",this.$nextTick((0,o.default)((0,i.default)().m((function a(){var r;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(e.currentTbComponent=t,!e.isReturn){a.n=2;break}return a.n=1,e.getTableConfig(e.gridCode);case 1:r=a.v,e.$nextTick((function(t){e.$refs["table"].init(r)}));case 2:e.$refs["table"].setData(e.tableData);case 3:return a.a(2)}}),a)}))))},uploadSuccess:function(e,t,a){this.fileListArr=JSON.parse(JSON.stringify(a))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},handlePreview:function(e){return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(a="",!e.response||!e.response.encryptionUrl){t.n=1;break}a=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,S.GetOssUrl)({url:e.encryptionUrl});case 2:a=t.v,a=a.Data;case 3:window.open(a);case 4:return t.a(2)}}),t)})))()},getOMALatestStatisticTime:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,w.GetOMALatestStatisticTime)({});case 1:a=t.v,a.IsSucceed?e.backendDate=a.Data||"":e.message.error(a.Mesaage);case 2:return t.a(2)}}),t)})))()},handleSearch:function(){if(this.tableData=JSON.parse(JSON.stringify(this.rootTableData)),this.searchForm.RawName){var e=new RegExp(this.searchForm.RawName,"i");this.tableData=this.tableData.filter((function(t){return e.test(t.RawName)}))}if(this.searchForm.Spec){var t=new RegExp(this.searchForm.Spec,"i");this.tableData=this.tableData.filter((function(e){return t.test(e.Spec)}))}if(this.searchForm.SysProjectId){var a=new RegExp(this.searchForm.SysProjectId,"i");this.tableData=this.tableData.filter((function(e){return a.test(e.Sys_Project_Id)}))}this.isReturn?this.$refs["table"].setData(this.tableData):this.$refs["table"].tbData=this.tableData},handleUpdateTb:function(){this.rootTableData=this.$refs["table"].tbData,this.tableData=JSON.parse(JSON.stringify(this.rootTableData))},getPreferenceSettingValue:function(){var e=this;(0,_.GetPreferenceSettingValue)({code:"Productweight"}).then((function(t){t.IsSucceed?(e.isProductweight=t.Data,"true"!==e.isProductweight&&e.getDepartmentTree()):e.$message({message:t.Message,type:"error"})}))},getDepartmentTree:function(){var e=this;(0,S.GetCompanyDepartTree)({isAll:!0}).then((function(t){if(t.IsSucceed){var a=t.Data;e.setDisabledTree(a),e.treeParamsDepart.data=a,e.$nextTick((function(t){e.$refs.treeSelectDepart.treeDataUpdateFun(a)}))}else e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var a=e.Children;!0===e.Data.Is_Company||"1"===e.Data.Type?e.disabled=!0:e.disabled=!1,a.length>0&&t.setDisabledTree(a)}))},handleReset:function(){this.searchNum=1,this.searchForm.RawName="",this.searchForm.Spec="",this.$refs["table"].tbData=this.rootTableData,this.tableData=JSON.parse(JSON.stringify(this.rootTableData))},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})},handleRetract:function(){this.isRetract=!this.isRetract},fetchData:function(){},getInfo:function(){var e,t,a=this;this.form.OutStoreNo=this.$route.query.OutStoreNo,this.form.OutStoreType=+this.$route.query.OutStoreType,this.isReturn?(e=b.GetAuxDetailByReceipt,t={Id:this.$route.query.id}):(e=b.GetPickOutDetail,t={outStoreNo:this.$route.query.OutStoreNo}),e(t).then((function(e){if(e.IsSucceed){var t=e.Data.Receipt,r=e.Data.Sub,n=t.OutStoreDate,i=t.Remark,o=t.SysProjectId,s=t.Pick_Department_Id,l=t.Use_Processing_Id,c=t.WorkingTeamId,u=t.ReceiveUserId,d=t.Attachment,f=t.Status;a.form.OutStoreDate=a.getDate(new Date(n)),a.form.Remark=i,a.form.SysProjectId=o,a.form.Use_Processing_Id=l,a.form.Pick_Department_Id=s,a.form.Pick_Department_Id&&a.departmentChange(),a.form.Use_Processing_Id=l,a.form.WorkingTeamId=c,a.form.ReceiveUserId=u,a.form.Status=f;var m=r.map((function(e,t){return e.index=(0,I.v4)(),e.Warehouse_Location=e.WarehouseName?e.WarehouseName+"/"+e.LocationName:"",e}));if(a.$nextTick((function(e){a.$refs["table"].tbData=JSON.parse(JSON.stringify(m)),a.tableData=JSON.parse(JSON.stringify(m)),a.rootTableData=JSON.parse(JSON.stringify(m))})),d){a.form.Attachment=d;var h=d.split(",");h.forEach((function(e){var t=e.indexOf("?Expires=")>-1?e.substring(0,e.lastIndexOf("?Expires=")):e,r=decodeURI(t.substring(t.lastIndexOf("/")+1)),n={};n.name=r,n.url=t,n.encryptionUrl=t,a.fileListData.push(n),a.fileListArr.push(n)}))}}else a.$message({message:e.Message,type:"error"})}))},getProcessListBase:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,v.GetProcessListBase)({});case 1:a=t.v,a.IsSucceed?e.ProcessList=a.Data||[]:e.message.error(a.Mesaage);case 2:return t.a(2)}}),t)})))()},departmentChange:function(){this.form.ReceiveUserId="",this.form.Pick_Department_Id&&this.getUserPageList(this.form.Pick_Department_Id)},getUserPageList:function(e){var t=this;(0,b.GetUserPage)({PageSize:-1,DepartmentId:e}).then((function(e){e.IsSucceed?t.factoryPeoplelist=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getFirstLevelDepartsUnderFactory:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,g.GetFirstLevelDepartsUnderFactory)({FactoryId:e.FactoryDetailData.Id});case 1:a=t.v,a.IsSucceed?(e.departmentlist=a.Data||[],e.form.Pick_Department_Id=a.Data.find((function(e){return e.Is_Cur_User_Depart}))?a.Data.find((function(e){return e.Is_Cur_User_Depart})).Id:"",e.departmentChange()):e.message.error(a.Mesaage);case 2:return t.a(2)}}),t)})))()},getProject:function(){var e=this;(0,m.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.projectOptions=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getWorkingTeams:function(){var e=this;(0,v.GetWorkingTeams)().then((function(t){t.IsSucceed?e.WorkingTeamList=t.Data:e.$message({message:t.Message,type:"error"})}))},changeWarehouse:function(e){this.currentRow=e,this.handleWarehouse(!0)},handleWarehouse:function(e){this.currentComponent="Warehouse",this.dWidth="40%",this.title="选择仓库/库位",!e&&(this.currentRow=null),this.dialogVisible=!0},getWarehouse:function(e){var t=e.warehouse,a=e.location;this.currentRow&&(this.currentRow.ReturnWarehoueseId=t.Id,this.currentRow.ReturnLocationId=a.Id,this.$set(this.currentRow,"ReturnWarehoueseName",t.Display_Name),this.$set(this.currentRow,"ReturnLocationName",a.Display_Name),this.$set(this.currentRow,"Warehouse_Location_Return",t.Display_Name+"/"+a.Display_Name))},batchEditorFn:function(e){var t=this;this.currentRow?e.forEach((function(e){t.$set(t.currentRow,e.key,e.val)})):this.multipleSelection.forEach((function(a,r){e.forEach((function(e){t.$set(a,e.key,e.val)}))})),this.handleClose()},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,a=e.val;1===t?this.$set(this.currentRow,"StandardDesc",a):(this.$set(this.currentRow,"StandardDesc",a.StandardDesc),this.currentRow.StandardId=a.StandardId)},typeChange:function(e){0!==e&&(this.BigType=1,this.typeNumber1=0,this.typeNumber2=0,this.typeNumber3=0,this.typeNumber4=0,this.tableData=[],this.$refs["table"].tbData=[])},getAddList:function(e,t){this.BigType=e[0].BigType,e.map((function(e,t){e.index=(0,I.v4)(),e.Pick_Project_Name=e.Project_Name})),this.$refs["table"].addData(e);var a=this.$refs.table.tbData;this.tableData=JSON.parse(JSON.stringify(a)),this.rootTableData=JSON.parse(JSON.stringify(a)),t&&(this.form.WorkingTeamId=t.Pick_Team_Id,this.form.Use_Processing_Id=t.Pick_Process_Id)},importData:function(e){this.$refs["table"].importData(e)},getRowName:function(e){var t=e.Name,a=e.Id;this.currentRow.Name=t,this.currentRow.RawId=a,this.currentRow.StandardDesc=""},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handleDelete:function(){var e,t=this.$refs.table.tbData;this.multipleSelection.forEach((function(e,a){var r=t.findIndex((function(t){return t.index===e.index}));t.splice(r,1)})),this.tableData=JSON.parse(JSON.stringify(t)),this.rootTableData=JSON.parse(JSON.stringify(t)),this.multipleSelection=[],null===(e=this.$refs.table)||void 0===e||null===(e=e.$refs)||void 0===e||e.xTable.clearCheckboxRow()},handleClose:function(e){this.openAddList=!1,this.dialogVisible=!1},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},checkValidate:function(){var e=(0,s.deepClone)(this.$refs["table"].tbData);if(!e.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var t=this.checkTb(e),a=t.status,r=t.msg;return a?{data:e,status:!0}:(this.$message({message:"".concat(r||"必填字段","不能为空"),type:"warning"}),{status:!1})},handleReturn:function(){var e=this;this.$confirm("是否提交退库信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveDraft(3)})).catch((function(t){e.$message({type:"info",message:"已取消"})}))},handleSubmit:function(e){var t=this;this.$confirm("确认提交出库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.saveDraft(3)})).catch((function(e){t.$message({type:"info",message:"已取消"})}))},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=this.checkValidate(),r=a.data,i=a.status;i&&this.$refs["form"].validate((function(a){if(!a)return!1;e.saveLoading=!0;var i=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){i.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)})),e.form.Attachment=i.join(","),e.form.Status=1==t?1:3;var o,s=(0,n.default)({},e.form);o=e.isReturn?b.AuxReturnByReceipt:b.PickOutStore,o({Receipt:s,Sub:r}).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"}),e.saveLoading=!1}))}))},checkTb:function(e){var t=this,a=null,r=["",null,void 0];this.isReturn?(r.push(0),a=["ReturnCount","Warehouse_Location_Return"]):a=["OutStoreCount"];for(var n=0;n<e.length;n++){for(var i,o=e[n],s=function(){var e=a[l];if(r.includes(o[e])){var n=t.$refs.table.rootColumns,i=n.find((function(t){return t.Code===e}));return{v:{status:!1,msg:null===i||void 0===i?void 0:i.Display_Name}}}},l=0;l<a.length;l++)if(i=s(),i)return i.v;delete o._X_ROW_KEY,delete o.WarehouseName,delete o.LocationName}return{status:!0,msg:""}},openAddDialog:function(e){this.openAddList=!0},closeView:function(){(0,s.closeTagView)(this.$store,this.$route)},setSelectRow:function(e){this.multipleSelection=e},getDate:function(e){var t=e||new Date,a=t.getFullYear(),r=("0"+(t.getMonth()+1)).slice(-2),n=("0"+t.getDate()).slice(-2);return"".concat(a,"-").concat(r,"-").concat(n)}}}},a990:function(e,t,a){"use strict";a.r(t);var r=a("a333"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},ad3d:function(e,t,a){},ad97:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("a9e3"),a("d3b7");var r=a("9643");t.default={props:{pageType:{type:Number,default:void 0}},data:function(){return{warehouses:[],locations:[],form:{Warehouse_Id:"",Location_Id:""},btnLoading:!1,rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){this.getWarehouseListOfCurFactory()},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=e.warehouses.find((function(t){return t.Id===e.form.Warehouse_Id})),r=e.locations.find((function(t){return t.Id===e.form.Location_Id}));e.$emit("warehouse",{warehouse:a,location:r}),e.btnLoading=!1,e.handleClose()}))},getWarehouseListOfCurFactory:function(){var e=this;(0,r.GetWarehouseListOfCurFactory)({type:0===this.pageType?"原材料仓库":"辅料仓库"}).then((function(t){if(t.IsSucceed){if(e.warehouses=t.Data,e.warehouses.length){var a=e.warehouses.find((function(e){return e.Is_Default}));a&&(e.form.Warehouse_Id=a.Id,e.getLocationList(!0))}}else e.$message({message:t.Message,type:"error"})}))},wareChange:function(e){this.form.Location_Id="",e&&this.getLocationList(!0)},getLocationList:function(e){var t=this;(0,r.GetLocationList)({Warehouse_Id:this.form.Warehouse_Id}).then((function(a){if(a.IsSucceed){if(t.locations=a.Data,t.locations.length&&e){var r=t.locations.find((function(e){return e.Is_Default}));r&&(t.form.Location_Id=r.Id)}}else t.$message({message:a.Message,type:"error"})}))},handleClose:function(){this.$emit("close")}}}},b31f:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card",style:e.isRetract?"height: 110px; overflow: hidden;":""},[a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"10px","padding-bottom":"10","border-bottom":"1px solid #d0d3db"}},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("出库单信息")]),a("div",{staticClass:"retract-container",on:{click:e.handleRetract}},[a("el-button",{attrs:{type:"text"}},[e._v(e._s(e.isRetract?"展开":"收起"))]),a("el-button",{attrs:{type:"text",icon:e.isRetract?"el-icon-arrow-down":"el-icon-arrow-up"}})],1)]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"出库类型",prop:"OutStoreType"}},[a("SelectMaterialStoreType",{attrs:{type:"RawOutStoreType"},model:{value:e.form.OutStoreType,callback:function(t){e.$set(e.form,"OutStoreType",t)},expression:"form.OutStoreType"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"出库日期",prop:"OutStoreDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,"picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.OutStoreDate,callback:function(t){e.$set(e.form,"OutStoreDate",t)},expression:"form.OutStoreDate"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"领料部门",prop:"Pick_Department_Id"}},["true"===e.isProductweight?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.isView||e.isReturn},on:{change:e.departmentChange},model:{value:e.form.Pick_Department_Id,callback:function(t){e.$set(e.form,"Pick_Department_Id",t)},expression:"form.Pick_Department_Id"}},e._l(e.departmentlist,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1):a("el-tree-select",{ref:"treeSelectDepart",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0,disabled:e.isReturn},"tree-params":e.treeParamsDepart},on:{"select-clear":e.departmentChange,"node-click":e.departmentChange},model:{value:e.form.Pick_Department_Id,callback:function(t){e.$set(e.form,"Pick_Department_Id",t)},expression:"form.Pick_Department_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"领料人",prop:"ReceiveUserId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.isView||!e.form.Pick_Department_Id||e.isReturn},on:{change:e.userChange},model:{value:e.form.ReceiveUserId,callback:function(t){e.$set(e.form,"ReceiveUserId",t)},expression:"form.ReceiveUserId"}},e._l(e.factoryPeoplelist,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),e.isPurchase?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"领料班组",prop:"WorkingTeamId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.isView},model:{value:e.form.WorkingTeamId,callback:function(t){e.$set(e.form,"WorkingTeamId",t)},expression:"form.WorkingTeamId"}},e._l(e.WorkingTeamList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.isPurchase?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"使用工序",prop:"Use_Processing_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.isView||e.isReturn},model:{value:e.form.Use_Processing_Id,callback:function(t){e.$set(e.form,"Use_Processing_Id",t)},expression:"form.Use_Processing_Id"}},e._l(e.ProcessList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,"show-word-limit":"",rows:1,maxlength:100,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),a("el-row",[e.isReturn?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:5,multiple:!0,"on-success":function(t,a,r){e.uploadSuccess(t,a,r)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"show-file-list":!0,"file-list":e.fileListData,disabled:e.isView||e.isReturn}},[e.isView||e.isReturn?e._e():a("el-button",{attrs:{type:"primary",disabled:e.isView||e.isReturn}},[e._v("上传文件")])],1)],1)],1),e.isReturn?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退库部门",prop:"Return_Dept_Id"}},[a("SelectDepartment",{attrs:{disabled:e.isView},model:{value:e.form.Return_Dept_Id,callback:function(t){e.$set(e.form,"Return_Dept_Id",t)},expression:"form.Return_Dept_Id"}})],1)],1):e._e(),e.isReturn?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退库人",prop:"Return_Person_Id "}},[a("SelectDepartmentUser",{attrs:{"department-id":e.form.Return_Dept_Id},model:{value:e.form.Return_Person_Id,callback:function(t){e.$set(e.form,"Return_Person_Id",t)},expression:"form.Return_Person_Id"}})],1)],1):e._e()],1)],1)],1),a("el-card",{staticClass:"box-card box-card-tb"},[a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("出库单明细")])]),a("el-divider",{staticClass:"elDivder"}),a("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[e.isView||e.isReturn?e._e():a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),a("el-button",{attrs:{type:"danger",disabled:!e.multipleSelection.length},on:{click:e.handleDelete}},[e._v("删除")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){e.batchDialogVisible=!0}}},[e._v("批量编辑领用项目")]),a("PickSelect",{staticStyle:{"margin-left":"10px"},attrs:{"selected-list":e.rootTableData,"material-type":1},on:{addList:e.getAddList}})],1),a("div",{staticStyle:{"margin-left":"auto"}},[a("el-form",{ref:"searchForm",attrs:{inline:"",model:e.searchForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"辅料名称",prop:"RawName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{staticClass:"input",attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.SysProjectId,callback:function(t){e.$set(e.searchForm,"SysProjectId",t)},expression:"searchForm.SysProjectId"}},e._l(e.projectOptions,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1),a("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":e.gridCode,"manual-hide-columns":[{Code:"PartyUnitName"}]},on:{updateColumn:e.changeColumn}})],1)],1)]),a("div",{staticClass:"tb-x"},[e.currentTbComponent?a(e.currentTbComponent,{ref:"table",tag:"component",attrs:{"is-return":e.isReturn,"is-view":e.isView,"big-type-data":e.BigType},on:{changeStandard:e.changeStandard,updateTb:e.handleUpdateTb,changeWarehouse:e.changeWarehouse,select:e.setSelectRow}}):e._e()],1),e.isView?e._e():a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("footer",[a("div",{staticClass:"data-info"},[e.isReturn?e._e():a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("div",[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),e.isReturn?a("el-button",{attrs:{loading:e.returning,type:"primary"},on:{click:e.handleReturn}},[e._v("确认退库")]):[a("el-button",{attrs:{loading:e.saveLoading},on:{click:function(t){return e.saveDraft(1)}}},[e._v("保存草稿 ")]),a("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:e.handleSubmit}},[e._v("提交出库")])]],2)])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":1},on:{close:e.handleClose,warehouse:e.getWarehouse,batchEditor:e.batchEditorFn,importData:e.importData,standard:e.getStandard,refresh:e.fetchData}})],1):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增",visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[a("add-purchase-list",{ref:"draft",attrs:{"big-type-data":e.BigType,"p-form":e.form,"joined-items":e.rootTableData},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"批量编辑领用项目",visible:e.batchDialogVisible,top:"10vh",width:"350px"},on:{"update:visible":function(t){e.batchDialogVisible=t},close:e.closeBatchDialog}},[a("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.batchProjectId,callback:function(t){e.batchProjectId=t},expression:"batchProjectId"}},e._l(e.projectOptions,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1),a("p",{staticStyle:{margin:"20px"}},[a("i",[e._v("注：仅能批量编辑公共库存的领用项目")])]),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:e.closeBatchDialog}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.batchChangeProject}},[e._v("确定")])],1)],1)],1)},n=[]},bbd2:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},n=[]},bd95:function(e,t,a){"use strict";a("3973")},bed3:function(e,t,a){"use strict";a.r(t);var r=a("4558"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},c7c1:function(e,t,a){"use strict";a.r(t);var r=a("fb8c"),n=a("82bf");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"9e318b78",null);t["default"]=s.exports},c93b:function(e,t,a){},cdc6:function(e,t,a){"use strict";a.r(t);var r=a("5c3e"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},cf5d:function(e,t,a){"use strict";a("719d")},d7a3:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1")),o=a("5e99"),s=a("fd31"),l=a("7196");t.default={data:function(){return{FactoryDetailData:{},ProfessionalType:[],factoryPeoplelist:[]}},methods:{getCurFactory:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,o.GetCurFactory)({}).then((function(t){t.IsSucceed?e.FactoryDetailData=t.Data[0]:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryTypeOption:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryPeoplelist:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryPeoplelist)({}).then((function(t){t.IsSucceed?e.factoryPeoplelist=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getManageCycle:function(){if(this.FactoryDetailData.Manage_Cycle_Enabled){var e,t,a,r,n,i,o=(new Date).getFullYear(),s=(new Date).getMonth()+1;1===this.FactoryDetailData.Manage_Cycle_Begin_Type?s-1===0?(e=o-1,t=12):(e=o,t=s-1):2===this.FactoryDetailData.Manage_Cycle_Begin_Type?(e=o,t=s):3===this.FactoryDetailData.Manage_Cycle_Begin_Type&&(s+1===13?(e=o+1,t=1):(e=o,t=s+1)),a=this.checkDate(e,t,this.FactoryDetailData.Manage_Cycle_Begin_Date),1===this.FactoryDetailData.Manage_Cycle_End_Type?s-1===0?(r=o-1,n=12):(r=o,n=s-1):2===this.FactoryDetailData.Manage_Cycle_End_Type?(r=o,n=s):3===this.FactoryDetailData.Manage_Cycle_End_Type&&(s+1===13?(r=o+1,n=1):(r=o,n=s+1)),i=this.checkDate(r,n,this.FactoryDetailData.Manage_Cycle_End_Date);var l=e+"-"+this.zeroFill(t)+"-"+this.zeroFill(a),c=r+"-"+this.zeroFill(n)+"-"+this.zeroFill(i);return[l,c]}return!1},checkDate:function(e,t,a){var r;return r=e%4===0?2===t?a>29?29:a:(4===t||6===t||9===t||11===t)&&a>30?30:a:2===t?a>28?28:a:(4===t||6===t||9===t||11===t)&&a>30?30:a,r},zeroFill:function(e){return e<10?"0"+e:e}}}},d852:function(e,t,a){"use strict";a.r(t);var r=a("72f6"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},e571:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{field:t.Code,"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"",title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.row;return["InStoreDate"===t.Code?[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]:[e._v(" "+e._s(r[t.Code]||"-")+" ")]]}},t.Is_Edit?{key:"edit",fn:function(r){var n=r.row;return["OutStoreCount"===t.Code?a("div",[a("el-input",{attrs:{min:0,disabled:!!n.PickSubId,max:n.AvailableCount},on:{change:function(t){return e.checkCount(n)}},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,a)},expression:"row[item.Code]"}})],1):"Pick_Project_Name"===t.Code?[a("vxe-select",{staticStyle:{width:"100%"},attrs:{disabled:!!n.PickSubId,placeholder:"请选择",transfer:"",clearable:"",filterable:""},on:{change:function(t){return e.rowProjectChange(t,e.rowIndex,n)}},model:{value:n.Pick_Sys_Project_Id,callback:function(t){e.$set(n,"Pick_Sys_Project_Id",t)},expression:"row.Pick_Sys_Project_Id"}},e._l(e.projectOptions,(function(e){return a("vxe-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)]:a("div",[a("el-input",{attrs:{type:"text"},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2)},n=[]},eaf62:function(e,t,a){"use strict";a.r(t);var r=a("5cc7e"),n=a("5dab");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("7a9a");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"197ae706",null);t["default"]=s.exports},edce:function(e,t,a){"use strict";a.r(t);var r=a("ad97"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},f16f:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186");t.default={methods:{getTableConfig:function(e){var t=this;return new Promise((function(a){(0,r.GetGridByCode)({code:e}).then((function(e){var r=e.IsSucceed,n=e.Data,i=e.Message;if(r){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),n.Grid.Is_Page&&(t.queryInfo.PageSize=+n.Grid.Row_Number),a(t.columns)}else t.$message({message:i,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},fb8c:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell"},"tooltip-config":{enterable:!0}}},[e._l(e.rootColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(r){var n=r.row;return["InStoreDate"===t.Code?a("span",[e._v(" "+e._s(e._f("timeFormat")(n.InStoreDate))+" ")]):a("span",[e._v(e._s(n[t.Code]||0==n[t.Code]?n[t.Code]:t.Is_Edit?"":"-"))])]}},t.Is_Edit?{key:"edit",fn:function(r){var n=r.row;return["ReturnCount"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,max:n.OutStoreCount,type:"number"},on:{change:function(t){return e.checkOutWeight(n)}},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Warehouse_Location_Return"===t.Code?a("span",[e._v(" "+e._s(n.ReturnWarehoueseName)+"/"+e._s(n.ReturnLocationName)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(n)}}})]):a("div",[a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2)},n=[]},fd10:function(e,t,a){"use strict";a.r(t);var r=a("53c4"),n=a("bed3");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("cf5d");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"cdbeb890",null);t["default"]=s.exports}}]);