(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-afb6c49e"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=o,Math.easeInOutQuad=function(e,t,a,r){return e/=r/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function u(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=u(),i=e-o,l=20,s=0;t="undefined"===typeof t?500:t;var d=function(){s+=l;var e=Math.easeInOutQuad(s,o,i,t);n(e),s<t?r(d):a&&"function"===typeof a&&a()};d()}},"0e06":function(e,t,a){"use strict";a("5ba2")},1237:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-wapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handelAdd}},[e._v("新建出库单")]),e.showReturnBtn?a("el-button",{attrs:{type:"danger"},on:{click:e.handleAddReturn}},[e._v("新增退库单")]):e._e(),a("el-button",{attrs:{loading:e.btnloading,disabled:0===e.multipleSelection.length},on:{click:e.handelExport}},[e._v("导出")]),a("div",{staticStyle:{"margin-left":"auto"}},[2==e.searchDetail.ReleaseStatus?a("span",{staticStyle:{color:"#409EFF","font-size":"14px"}},[a("span",[e._v("出库数量："+e._s(e.sum.ChukuQuantity))]),a("span",{staticStyle:{"margin-left":"10px"}},[e._v("退库数量："+e._s(e.sum.TuikuQuantity))])]):e._e(),a("DynamicTableFields",{staticStyle:{"margin-left":"20px"},attrs:{title:"表格配置","table-config-code":"pro_aux_material_outbound_list,Steel"},on:{updateColumn:e.changeColumn}})],1)],1),a("div",{staticClass:"table-wrapper"},[e.showTable?a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"","checkbox-config":{checkField:"checked"},loading:e.pgLoading,"row-config":{isCurrent:!0,isHover:!0},align:"left",stripe:"",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell"},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"45"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width,align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":""},scopedSlots:e._u([{key:"default",fn:function(r){var n=r.row;return["OutStoreType"===t.Code?a("span",[e._v(" "+e._s(1===n.OutStoreType?"领用出库":3===n.OutStoreType?"委外出库":"-")+" ")]):"StatusDescription"===t.Code?a("span",["通过"===n.StatusDescription?a("span",{staticClass:"by-dot by-dot-success"},[e._v(" "+e._s(n.StatusDescription)+" ")]):a("span",{staticClass:"by-dot by-dot-info"},[e._v(" "+e._s(n.StatusDescription)+" ")])]):"OutStoreNo"===t.Code?a("span",[a("el-link",{style:{color:1==n.Type?"#67C23A":"#298DFF"},attrs:{type:"primary"},on:{click:function(t){return e.handelView(n)}}},[e._v(" "+e._s(e._f("displayValue")(n[t.Code])))])],1):"Status"===t.Code?a("span",[e._v(" "+e._s(1===n.Status?"草稿":2===n.Status?"审核中":3===n.Status?"通过":5===n.Status?"已核算":"退回")+" ")]):a("span",[e._v(" "+e._s(e._f("displayValue")(n[t.Code]))+" ")])]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",align:"center",title:"操作",width:"136"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[1===r.Status||4===r.Status?[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handelEdit(r)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handelSub(r)}}},[e._v("提交")]),a("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.handelDel(r)}}},[e._v("删除")])]:[3===r.Status&&0==r.ckType?a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleReturn(r)}}},[e._v("退库")]):e._e(),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handelView(r)}}},[e._v("查看")])]]}}],null,!1,1392642249)})],2):e._e()],1),a("footer",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.multipleSelection.length)+" 条数据 ")]),a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)])},n=[]},"12bc":function(e,t,a){"use strict";a.r(t);var r=a("1237"),n=a("81fb");for(var u in n)["default"].indexOf(u)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(u);a("0e06");var o=a("2877"),i=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"f3700170",null);t["default"]=i.exports},"2f17":function(e,t,a){},3999:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),u=r(a("1da1")),o=r(a("bc29")),i=r(a("40c9")),l=r(a("12bc")),s=a("cf45"),d=r(a("bad9")),c=r(a("5d4b")),f=r(a("c13a")),p=r(a("8c02"));t.default={name:"PROMaterialInventory",components:{SelectDepartment:p.default,SelectDepartmentUser:f.default,SelectMaterialStoreType:c.default,SelectProject:d.default,inventory:l.default},mixins:[o.default,i.default],data:function(){return{activeName:"1",form:{Status:"",OutStoreNo:"",Pick_Department_Id:"",SysProjectId:"",DateRange:"",ReleaseStatus:"2",ReceiveUserId:"",OutStoreTypeList:[],Use_Processing_Id:"",ReceiverUserId:""},AuxOutboundTypeList:[]}},mounted:function(){var e=this;return(0,u.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getDictionary)("AuxOutboundType");case 1:e.AuxOutboundTypeList=t.v;case 2:return t.a(2)}}),t)})))()},methods:{handleSearch:function(){this.$refs.inventoryRef.fetchData(1)}}}},5480:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTableConfig=t.getRoleInfo=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186"),n=a("c24f"),u=void 0;t.getTableConfig=function(e,t){return new Promise((function(a,n){(0,r.GetGridByCode)({code:e,businessType:t}).then((function(e){var t=e.IsSucceed,r=e.Data,n=e.Message;if(t){var o=(r.ColumnList||[]).filter((function(e){return e.Is_Display}));a(o)}else u.$message({message:n,type:"error"})}))}))},t.getRoleInfo=function(e){return new Promise((function(t,a){(0,n.RoleAuthorization)({roleType:3,menuType:1,menuId:e}).then((function(e){if(e.IsSucceed){var r=((null===e||void 0===e?void 0:e.Data)||[]).map((function(e){return e.Code}));t(r)}else u.$message({message:e.Message,type:"error"}),a(e.message)}))}))}},"5ba2":function(e,t,a){},"5f52":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("dca8"),a("d3b7");var n=r(a("c14f")),u=r(a("1da1")),o=a("6186"),i=a("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,u.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getTypeList();case 1:return a.n=2,t.getTable(e);case 2:return a.a(2)}}),a)})))()},getTypeList:function(){var e=this;return(0,u.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,i.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var r,n=null===(r=a[0])||void 0===r?void 0:r.Id;e.Code=a.find((function(e){return e.Id===n})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(a){(0,o.GetGridByCode)({code:e+","+t.Code}).then((function(e){var r=e.IsSucceed,n=e.Data,u=e.Message;if(r){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),n.Grid.Is_Page&&(t.queryInfo.PageSize=+n.Grid.Row_Number),a(t.columns)}else t.$message({message:u,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},7196:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteWorkshop=s,t.GetFactoryPeoplelist=u,t.GetWorkshopEntity=l,t.GetWorkshopPageList=i,t.SaveEntity=o;var n=r(a("b775"));function u(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function o(e){return(0,n.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:e})}},"720a":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),u=r(a("c14f")),o=r(a("1da1"));a("caad"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("ab43"),a("d3b7"),a("2532"),a("3ca3"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var i=r(a("5f52")),l=r(a("0f97")),s=a("3c4a"),d=r(a("2082")),c=a("ed08"),f=a("f4f2"),p=a("5480"),m=r(a("a657")),h=a("93aa"),S=r(a("bad9")),y=r(a("333d")),P=a("c685");t.default={components:{Pagination:y.default,SelectProject:S.default,DynamicTableFields:m.default,DynamicDataTable:l.default},mixins:[i.default,d.default],props:{searchDetail:{type:Object,default:function(){return{}}}},data:function(){return{tablePageSize:P.tablePageSize,showReturnBtn:!1,pgLoading:!1,btnloading:!1,queryInfo:{Page:1,PageSize:20},columns:[],currentColumns:[],tbData:[],multipleSelection:[],tbConfig:{Op_Width:180,Is_Reserve:!0,Is_Auto_Width:!1},total:0,dialogVisible:!1,dialogTitle:"",width:0,currentComponent:"",form:{},showTable:!0,addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-05015e88"),a.e("chunk-44f21a7e"),a.e("chunk-c5c0f968")]).then(a.bind(null,"50a6"))},name:"PROAuxMaterialOutboundAdd",meta:{title:"新建出库单"}},{path:this.$route.path+"/edit",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-05015e88"),a.e("chunk-44f21a7e"),a.e("chunk-fb90cd5a")]).then(a.bind(null,"c03f"))},name:"PROAuxMaterialOutboundEdit",meta:{title:"编辑出库单"}},{path:this.$route.path+"/view",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-05015e88"),a.e("chunk-44f21a7e"),a.e("chunk-01164d01")]).then(a.bind(null,"ba19"))},name:"PROAuxMaterialOutboundView",meta:{title:"查看出库单"}},{path:this.$route.path+"/add-return",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-5836e6ec"),a.e("chunk-18b1cc82"),a.e("chunk-2996c6d5")]).then(a.bind(null,"b12c"))},name:"PROAuxMaterialReceiptReturnAddReturn",meta:{title:"新建退库单"}},{path:this.$route.path+"/edit/:id/:type",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-5836e6ec"),a.e("chunk-18b1cc82"),a.e("chunk-43c6d828")]).then(a.bind(null,"7778"))},name:"PROAuxMaterialReceiptReturnEdit",meta:{title:"编辑退库单"}},{path:this.$route.path+"/view/:id/:type",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-5836e6ec"),a.e("chunk-18b1cc82"),a.e("chunk-15f5bcf0")]).then(a.bind(null,"3cf1"))},name:"PROAuxMaterialReceiptReturnView",meta:{title:"查看退库单"}}],sum:{}}},watch:{},mounted:function(){var e=this;return(0,o.default)((0,u.default)().m((function t(){var a;return(0,u.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("pro_aux_material_outbound_list");case 1:return e.currentColumns=e.columns,t.n=2,e.fetchData(1);case 2:return t.n=3,(0,p.getRoleInfo)(e.$route.meta.Id);case 3:a=t.v,Array.isArray(a)&&a.includes("AuxOutReturnBtn")&&(e.showReturnBtn=!0);case 4:return t.a(2)}}),t)})))()},methods:{changeColumn:function(){var e=this;return(0,o.default)((0,u.default)().m((function t(){return(0,u.default)().w((function(t){while(1)switch(t.n){case 0:return e.showTable=!1,t.n=1,e.getTableConfig("pro_aux_material_outbound_list");case 1:e.currentColumns=e.columns,e.showTable=!0;case 2:return t.a(2)}}),t)})))()},fetchData:function(e){var t=this;this.multipleSelection=[];var a=this.searchDetail,r=a.OutStoreNo,u=a.Pick_Department_Id,o=a.SysProjectId,i=a.DateRange,l=a.Out_Store_Type,d=a.Status,f=a.ReleaseStatus,p=a.OutStoreTypeList,m=a.ReceiverUserId;this.form={},this.form.Pick_Department_Id=u,this.form.OutStoreNo=r,this.form.Out_Store_Type=l,this.form.SysProjectId=o,this.form.Status=d,this.form.ReleaseStatus=f,this.form.Beg=i?i[0]:"",this.form.End=i?i[1]:"",this.form.OutStoreTypeList=p,this.form.ReceiverUserId=m,e&&(this.queryInfo.Page=e),this.pgLoading=!0,(0,s.FindPageList)((0,n.default)((0,n.default)({},this.form),this.queryInfo)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.map((function(e){return e.OutStoreDate=e.OutStoreDate?(0,c.parseTime)(new Date(e.OutStoreDate),"{y}-{m}-{d}"):"",e.CreateDate=e.CreateDate?(0,c.parseTime)(new Date(e.CreateDate),"{y}-{m}-{d}"):"",e})),t.total=e.Data.TotalCount):t.$message.error(e.Message)})).finally((function(e){t.pgLoading=!1})),(0,h.OutStoreListSummary)((0,n.default)({},this.form)).then((function(e){t.sum=e.Data}))},handleReturn:function(e){this.$router.push({name:"PROAuxMaterialReceiptReturnAddReturn",query:{pg_redirect:this.$route.name,OutStoreNo:e.OutStoreNo,OutStoreType:e.OutStoreType}})},handelAdd:function(){this.$router.push({name:"PROAuxMaterialOutboundAdd",query:{pg_redirect:this.$route.name}})},handelEdit:function(e){0===e.ckType?this.$router.push({name:"PROAuxMaterialOutboundEdit",query:{pg_redirect:this.$route.name,OutStoreNo:e.OutStoreNo,OutStoreType:e.OutStoreType}}):this.$router.push({name:"PROAuxMaterialReceiptReturnEdit",query:{pg_redirect:this.$route.name,id:e.OutStoreNo,type:e.OutStoreType,status:e.Status},params:{id:e.OutStoreNo,type:e.OutStoreType,status:e.Status}})},handelView:function(e){0===e.ckType?this.$router.push({name:"PROAuxMaterialOutboundView",query:{pg_redirect:this.$route.name,OutStoreNo:e.OutStoreNo,OutStoreType:e.OutStoreType}}):this.$router.push({name:"PROAuxMaterialReceiptReturnView",query:{pg_redirect:this.$route.name,id:e.OutStoreNo,type:e.OutStoreType,status:e.Status},params:{id:e.OutStoreNo,type:e.OutStoreType,status:e.Status}})},handelDel:function(e){var t=this;this.$confirm("是否删除选中数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){0===e.ckType?(0,s.EditAuxOutStatus)({OutStoreNo:e.OutStoreNo,Status:1,Is_Deleted:!0}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})})):(0,h.DeleteAuxInStore)({inStoreNo:e.OutStoreNo,status:e.Status}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handelSub:function(e){var t=this;this.$confirm("是否提交选中数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){0==e.ckType?(0,s.EditAuxOutStatus)({OutStoreNo:e.OutStoreNo,Status:3,Is_Deleted:!1}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"提交成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})})):(0,h.MaterielAuxSubmitInStore)({inStoreNo:e.OutStoreNo}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消提交"})}))},handelExport:function(){var e=this,t=this.multipleSelection.map((function(e){return e.Id}));this.btnLoading=!0,(0,s.AuxOutExport)({request:t}).then((function(t){if(t.IsSucceed){e.$message.success("导出成功");var a=new URL(t.Data,(0,f.baseUrl)());window.open(a.href)}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))},tbSelectChange:function(e){this.multipleSelection=e.records},handleAddReturn:function(){this.$router.push({name:"PROAuxMaterialReceiptReturnAddReturn",query:{pg_redirect:this.$route.name}})}}}},"81fb":function(e,t,a){"use strict";a.r(t);var r=a("720a"),n=a.n(r);for(var u in r)["default"].indexOf(u)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(u);t["default"]=n.a},"85e0":function(e,t,a){"use strict";a.r(t);var r=a("ddd2"),n=a("ed43");for(var u in n)["default"].indexOf(u)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(u);a("96431");var o=a("2877"),i=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"1c81ec9a",null);t["default"]=i.exports},"8fea":function(e,t){e.exports={WEIGHT_CONVERSION:1e3,WEIGHT_DECIMAL:5,WEIGHT_KG_DECIMAL:2,COUNT_DECIMAL:2,UNIT_PRICE_DECIMAL:6,UNIT_PRICE_KG_DECIMAL:9,RawInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],RawReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],RawAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],RawOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:3}],RawAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}],AuxInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],AuxReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],AuxAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],AuxOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:2}],AuxAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}]}},"93aa":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AuxImport=E,t.AuxInStoreExport=K,t.AuxReturnByReceipt=Y,t.AuxSurplusReturnStore=ee,t.DeleteAuxInStore=q,t.DeleteInStore=P,t.DeletePicking=ve,t.ExportCheckReceipt=fe,t.ExportInstoreReceipt=ce,t.ExportMoneyAdjustOrder=we,t.ExportPicking=_e,t.ExportProcess=je,t.ExportTestDetail=Oe,t.FindAuxPageList=z,t.FindRawPageList=C,t.GetAuxCategoryTreeList=Z,t.GetAuxDetailByReceipt=X,t.GetAuxImportTemplate=$,t.GetAuxPageList=ae,t.GetAuxPickOutStoreSubList=j,t.GetAuxProcurementDetails=re,t.GetAuxSurplusReturnStoreDetail=te,t.GetCategoryTreeList=I,t.GetImportTemplate=k,t.GetInstoreDetail=y,t.GetMoneyAdjustDetailPageList=be,t.GetOMALatestStatisticTime=x,t.GetOrderDetail=oe,t.GetPartyAs=g,t.GetPickLockStoreToChuku=$e,t.GetPickPlate=Ee,t.GetPickSelectPageList=Ne,t.GetPickSelectSubList=Le,t.GetPickingDetail=Ae,t.GetPickingTypeSettingDetail=Te,t.GetProjectListForTenant=ne,t.GetRawDetailByReceipt=ie,t.GetRawOrderList=ue,t.GetRawPageList=O,t.GetRawPickOutStoreSubList=L,t.GetRawProcurementDetails=b,t.GetRawSurplusReturnStoreDetail=N,t.GetReturnPlate=Ve,t.GetStoreSelectPage=xe,t.GetSuppliers=R,t.GetTestDetail=Re,t.GetTestInStoreOrderList=ye,t.Import=A,t.ImportCheckReceipt=me,t.ImportInstoreReceipt=pe,t.InStoreListSummary=se,t.LockPicking=Ce,t.ManualAuxInStoreDetail=W,t.ManualInStoreDetail=h,t.MaterielAuxInStoreList=V,t.MaterielAuxManualInStore=J,t.MaterielAuxPurchaseInStore=Q,t.MaterielAuxSubmitInStore=F,t.MaterielPartyAInStorel=H,t.MaterielRawInStoreList=u,t.MaterielRawInStoreListInSumNew=i,t.MaterielRawInStoreListNew=o,t.MaterielRawManualInStore=_,t.MaterielRawPartyAInStore=D,t.MaterielRawPurchaseInStore=w,t.MaterielRawSubmitInStore=l,t.MaterielRawSurplusInStore=M,t.OutStoreListSummary=de,t.PartAInStoreDetail=m,t.PartyAInInStoreDetail=B,t.PurchaseAuxInStoreDetail=U,t.PurchaseInStoreDetail=p,t.RawInStoreExport=T,t.RawReturnByReceipt=le,t.RawSurplusReturnStore=G,t.SaveInStore=v,t.SavePicking=ke,t.SetQualified=ge,t.SetTestDetail=Ie,t.StoreMoneyAdjust=Se,t.SubmitApproval=f,t.SubmitAuxApproval=c,t.SubmitInStore=he,t.SubmitPicking=Me,t.SurplusInStoreDetail=S,t.UnLockPicking=Ge,t.UpdateInvoiceInfo=De,t.Withdraw=s,t.WithdrawAux=d,t.WithdrawChecked=Pe;var n=r(a("b775"));function u(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:e})}function o(e){return(0,n.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:e})}function y(e){return(0,n.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:e})}function V(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:e})}function q(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:e})}function z(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:e})}function U(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:e})}function B(e){return(0,n.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:e})}function Q(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:e})}function H(e){return(0,n.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:e})}function J(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:e})}function K(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:e})}function X(e){return(0,n.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:e})}function Y(e){return(0,n.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:e})}function Z(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function ee(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:e})}function te(e){return(0,n.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:e})}function ae(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function re(e){return(0,n.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:e})}function ne(e){return(0,n.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:e})}function ue(e){return(0,n.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:e})}function oe(e){return(0,n.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:e})}function ie(e){return(0,n.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:e})}function le(e){return(0,n.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:e})}function se(e){return(0,n.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:e})}function de(e){return(0,n.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:e})}function ce(e){return(0,n.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:e})}function fe(e){return(0,n.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:e})}function pe(e){return(0,n.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:e})}function me(e){return(0,n.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:e})}function he(e){return(0,n.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:e})}function Se(e){return(0,n.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:e})}function ye(e){return(0,n.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:e})}function Pe(e){return(0,n.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:e})}function Re(e){return(0,n.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:e})}function ge(e){return(0,n.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:e})}function Ie(e){return(0,n.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:e})}function Oe(e){return(0,n.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:e})}function be(e){return(0,n.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:e})}function we(e){return(0,n.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:e})}function De(e){return(0,n.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:e})}function _e(e){return(0,n.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:e})}function Me(e){return(0,n.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:e})}function ve(e){return(0,n.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:e})}function xe(e){return(0,n.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:e})}function ke(e){return(0,n.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:e})}function Ae(e){return(0,n.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:e})}function Te(e){return(0,n.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:e})}function Ce(e){return(0,n.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:e})}function Ge(e){return(0,n.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:e})}function Ne(e){return(0,n.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:e})}function Le(e){return(0,n.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:e})}function $e(e){return(0,n.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:e})}function Ee(e){return(0,n.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:e})}function Ve(e){return(0,n.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:e})}function je(e){return(0,n.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:e})}},96431:function(e,t,a){"use strict";a("2f17")},cf45:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=n,a("d3b7");var r=a("6186");function n(e){return new Promise((function(t,a){(0,r.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},ddd2:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{ref:"searchDom",staticClass:"header_wrapper"},[a("div",{staticClass:"search-wrapper"},[a("el-tabs",{on:{"tab-click":function(t){e.$refs["form"].resetFields(),e.handleSearch()}},model:{value:e.form.ReleaseStatus,callback:function(t){e.$set(e.form,"ReleaseStatus",t)},expression:"form.ReleaseStatus"}},[a("el-tab-pane",{attrs:{label:"已下达",name:"2"}}),a("el-tab-pane",{attrs:{label:"未下达",name:"1"}})],1),a("el-form",{ref:"form",attrs:{model:e.form,inline:""}},[a("el-form-item",{attrs:{label:"单号",prop:"OutStoreNo"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"text",placeholder:"请输入"},model:{value:e.form.OutStoreNo,callback:function(t){e.$set(e.form,"OutStoreNo",t)},expression:"form.OutStoreNo"}})],1),a("el-form-item",{attrs:{label:"类型",prop:"OutStoreTypeList"}},[a("SelectMaterialStoreType",{staticStyle:{width:"220px"},attrs:{type:"RawAllOutStoreType",multiple:""},model:{value:e.form.OutStoreTypeList,callback:function(t){e.$set(e.form,"OutStoreTypeList",t)},expression:"form.OutStoreTypeList"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}})],1),a("el-form-item",{attrs:{label:"领用/退库部门",prop:"Pick_Department_Id"}},[a("SelectDepartment",{model:{value:e.form.Pick_Department_Id,callback:function(t){e.$set(e.form,"Pick_Department_Id",t)},expression:"form.Pick_Department_Id"}})],1),a("el-form-item",{attrs:{label:"领用人/退库人",prop:"ReceiverUserId"}},[a("SelectDepartmentUser",{attrs:{"department-id":e.form.Pick_Department_Id},model:{value:e.form.ReceiverUserId,callback:function(t){e.$set(e.form,"ReceiverUserId",t)},expression:"form.ReceiverUserId"}})],1),a("el-form-item",{attrs:{label:"日期",prop:"DateRange"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.form.DateRange,callback:function(t){e.$set(e.form,"DateRange",t)},expression:"form.DateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1)]),a("div",{staticClass:"main-wrapper"},[a("inventory",{ref:"inventoryRef",attrs:{"search-detail":e.form}})],1)])},n=[]},ed43:function(e,t,a){"use strict";a.r(t);var r=a("3999"),n=a.n(r);for(var u in r)["default"].indexOf(u)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(u);t["default"]=n.a}}]);