(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-55c46df2"],{"05eb":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog_wapper"},[e.shawDialog?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.shawDialog,width:"60%"},on:{"update:visible":function(t){e.shawDialog=t},close:e.handleClose}},[a("div",{staticClass:"select_Wapper"},[a("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:e.disable},on:{change:e.changeObject,clear:e.qualityListClear},model:{value:e.form.Check_Object_Type,callback:function(t){e.$set(e.form,"Check_Object_Type",t)},expression:"form.Check_Object_Type"}},e._l(e.CheckObjectData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!e.form.Check_Object_Type||e.disable},on:{change:e.changeCheckNode,clear:e.checkNodeClear},model:{value:e.form.Check_Node_Id,callback:function(t){e.$set(e.form,"Check_Node_Id",t)},expression:"form.Check_Node_Id"}},e._l(e.CheckNodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-select",{attrs:{placeholder:"请选择",disabled:!e.form.Check_Node_Id||e.disable||"-1"!=e.form.checkTypeId},model:{value:e.form.Check_Type,callback:function(t){e.$set(e.form,"Check_Type",t)},expression:"form.Check_Type"}},e._l(e.CheckTypeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1)],1),a("div",{staticStyle:{margin:"20px 0"}},[a("el-button",{attrs:{type:"primary",disabled:!e.form.Check_Object_Type},on:{click:e.addInfo}},[e._v("添 加")]),a("el-button",{attrs:{type:"danger",disabled:0==e.selectList.length},on:{click:e.handeldelete}},[e._v("删 除 ")])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fff cs-z-tb-wrapper"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","checkbox-config":{checkField:"checked"},"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","show-overflow":"",loading:e.loading,stripe:"",size:"medium",data:e.tbData,"radio-config":{highlight:!0},resizable:"","tooltip-config":{enterable:!0}},on:{"radio-change":e.radioChangeEvent,"checkbox-all":e.multiSelectedChange,"checkbox-change":e.multiSelectedChange}},[e.isFull&&!e.isBatchAdd?a("vxe-column",{attrs:{type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectList.length},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,1316604102)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(i){var n=i.row;return["Inspction_Count"==t.Code?a("div",[a("el-input",{staticStyle:{width:"50px",border:"1px solid #eee","border-radius":"4px"},attrs:{type:"text",readonly:1==n.SteelAmount},on:{blur:function(t){e.inputBlur(t,n.Inspction_Count,n)}},model:{value:n.Inspction_Count,callback:function(t){e.$set(n,"Inspction_Count",t)},expression:"row.Inspction_Count"}})],1):a("div",[e._v(" "+e._s(e._f("displayValue")(n[t.Code]))+" ")])]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(t){return e.handleClose()}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading1},on:{click:function(t){return e.AddSave("form",!1)}}},[e._v("保 存")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading2},on:{click:function(t){return e.AddSave("form",!0)}}},[e._v("提 交")])],1)]):e._e()],1)},n=[]},"0618":function(e,t,a){},"0a00":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("d3ce")),o=i(a("e8b8"));t.default={name:"PROStartInspect",components:{fullCheck:n.default,spotCheck:o.default},data:function(){return{activeName:"全检"}},provide:function(){return{getIsFull:this.getType}},methods:{getType:function(){return"全检"===this.activeName}}}},"0a40":function(e,t,a){"use strict";a("0b39")},"0b39":function(e,t,a){},"0d09":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form.Feedmodel,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"单据状态",prop:"Status"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Status,callback:function(t){e.$set(e.form.Feedmodel,"Status",t)},expression:"form.Feedmodel.Status"}},[a("el-option",{attrs:{label:"草稿",value:0}}),a("el-option",{attrs:{label:"待整改",value:1}}),a("el-option",{attrs:{label:"待复核",value:2}}),a("el-option",{attrs:{label:"待质检",value:4}}),a("el-option",{attrs:{label:"已完成",value:3}})],1)],1),a("el-form-item",{attrs:{label:"质检结果",prop:"Sheet_result"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Sheet_result,callback:function(t){e.$set(e.form.Feedmodel,"Sheet_result",t)},expression:"form.Feedmodel.Sheet_result"}},[a("el-option",{attrs:{label:"合格",value:"合格"}}),a("el-option",{attrs:{label:"不合格",value:"不合格"}})],1)],1),a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.qualityListChange,clear:e.qualityListClear},model:{value:e.form.Feedmodel.Check_Object_Type_Id,callback:function(t){e.$set(e.form.Feedmodel,"Check_Object_Type_Id",t)},expression:"form.Feedmodel.Check_Object_Type_Id"}},e._l(e.qualityList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!Boolean(e.form.Feedmodel.Check_Object_Type_Id)},on:{change:e.nodeChange},model:{value:e.form.Feedmodel.Check_Node_Id,callback:function(t){e.$set(e.form.Feedmodel,"Check_Node_Id",t)},expression:"form.Feedmodel.Check_Node_Id"}},e._l(e.nodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"来源",prop:"Source_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Source_Type,callback:function(t){e.$set(e.form.Feedmodel,"Source_Type",t)},expression:"form.Feedmodel.Source_Type"}},[a("el-option",{attrs:{label:"手动创建",value:"手动创建"}}),a("el-option",{attrs:{label:"业务推送",value:"业务推送"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addCheck()}}},[e._v("新增")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Number",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Number||"-"))])]}},{key:"Steel_Count",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Steel_Count||"-"))])]}},{key:"Qualified_Count",fn:function(t){var i=t.row;return[0==i.Qualified_Count?a("div",[e._v(" "+e._s(i.Qualified_Count)+" ")]):a("div",[e._v(e._s(i.Qualified_Count||"-"))])]}},{key:"Unqualified_Count",fn:function(t){var i=t.row;return[0==i.Unqualified_Count?a("div",[e._v(" "+e._s(i.Unqualified_Count)+" ")]):a("div",[e._v(e._s(i.Unqualified_Count||"-"))])]}},{key:"Check_Result",fn:function(t){var i=t.row;return[i.Check_Result?["合格"===i.Check_Result?a("el-tag",{attrs:{type:"success"}},[e._v(e._s(i.Check_Result))]):a("el-tag",{attrs:{type:"danger"}},[e._v(e._s(i.Check_Result))])]:a("span",[e._v("-")])]}},{key:"Status",fn:function(t){var i=t.row;return["已完成"===i.Status?a("span",{staticClass:"by-dot by-dot-success"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待复核"===i.Status||"待整改"===i.Status?a("span",{staticClass:"by-dot by-dot-primary"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待质检"===i.Status||"草稿"===i.Status?a("span",{staticClass:"by-dot by-dot-info"},[e._v(" "+e._s(i.Status||"-")+" ")]):a("span",[e._v(" "+e._s(i.Status||"-")+" ")])]}},{key:"Check_UserName",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Check_UserName||"-"))])]}},{key:"Pick_Date",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Pick_Date||"-"))])]}},{key:"Code",fn:function(t){var i=t.row;return[a("div",[i.Code?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.rectificationInfo(i)}}},[e._v(e._s(i.Code))]):a("div",[e._v("-")])],1)]}},{key:"op",fn:function(t){var i=t.row,n=t.index;return["草稿"===i.Status?a("div",[a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.addCheck(i)}}},[e._v("编辑")]),a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleSub(i.SheetId)}}},[e._v("提交")]),a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleDel(i.SheetId)}}},[e._v("删除")])],1):"待质检"===i.Status?a("div",[a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleCheck(i)}}},[e._v("质检")])],1):a("div",[a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleInfo(i)}}},[e._v("查看")]),i.Code?a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleRecord(i)}}},[e._v("操作记录")]):e._e()],1)]}}])})],1)],1)]),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"480px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-row":e.selectRow},on:{openDialog:e.openDialog,close:e.handleClose,refresh:e.fetchData,qualityItemChange:e.qualityItemChange}})],1):e._e(),e.dialogVisible2?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content2",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle2,visible:e.dialogVisible2,width:"66%"},on:{"update:visible":function(t){e.dialogVisible2=t},close:e.handleClose2}},[a(e.currentComponent2,{ref:"content2",tag:"component",attrs:{"quality-item":e.qualityItem},on:{close:e.handleClose2,getSelectRow:e.getSelectRow,bitchaddCom:e.bitchaddCom}})],1):e._e(),a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content3",staticClass:"plm-custom-dialog",attrs:{title:"整改记录",visible:e.dialogVisible3,width:"50%"},on:{"update:visible":function(t){e.dialogVisible3=t},close:e.handleClose3}},[a(e.currentComponent3,{ref:"content3",tag:"component",on:{close:e.handleClose3}})],1),a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content4",staticClass:"plm-custom-dialog",attrs:{title:"开具整改单",visible:e.dialogVisible4,width:"30%"},on:{"update:visible":function(t){e.dialogVisible4=t},close:e.handleClose4}},[a("rectificationorder-Dialog",{attrs:{"submit-spot-obj":e.submitSpotObj},on:{close:e.handleClose4,refresh:e.fetchData}})],1),a("selected-Dialog2",{ref:"selectRef",on:{BitchopenDialog:e.BitchopenDialog,refresh:e.fetchData,qualityItemChange:e.qualityItemChange,rectificationOrderInfo:e.rectificationOrderInfo}})],1)},n=[]},"0eaf":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"header_tab"},[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"全检",name:"全检"}}),a("el-tab-pane",{attrs:{label:"抽检",name:"抽检"}})],1)],1),"全检"==e.activeName?a("full-check"):e._e(),"抽检"==e.activeName?a("spot-check"):e._e()],1)])},n=[]},"16ac":function(e,t,a){},1710:function(e,t,a){"use strict";a.r(t);var i=a("64d28"),n=a("aab8");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("267c");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"07d7c3b8",null);t["default"]=c.exports},1997:function(e,t,a){},"1db0e":function(e,t,a){},"20cc":function(e,t,a){},"229d":function(e,t,a){"use strict";a.r(t);var i=a("68ee7"),n=a("e7db");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("468df");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"250edd58",null);t["default"]=c.exports},"267c":function(e,t,a){"use strict";a("1db0e")},"27a1":function(e,t,a){"use strict";a.r(t);var i=a("422d"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},"2bfe":function(e,t,a){"use strict";a.r(t);var i=a("c7bd"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},3254:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var i=a("221f"),n=a("8975");a("0e9a"),t.default={data:function(){return{list:[],srcList:[]}},methods:{init:function(e){this.getrectificationRecord(e.SheetId)},getrectificationRecord:function(e){var t=this;this.srcList=[],(0,i.RectificationRecord)({sheetid:e}).then((function(e){e.IsSucceed?0!=e.Data.length?t.list=e.Data.map((function(e){switch(e.Reply.Type){case 1:e.Reply.Type="整改";break;case 2:e.Reply.Type="复核";break;case 3:e.Reply.Type="评论";break;case 0:e.Reply.Type="移交";break;case-1:e.Reply.Type="初次整改";break}return e.Reply.ActionTime=(0,n.timeFormat)(e.Reply.ActionTime,"{y}-{m}-{d} {h}:{i}:{s}"),e.Attachments.forEach((function(e){t.srcList.push(e.File_Url)})),e})):t.$message({type:"error",message:"暂无操作记录"}):t.$message({type:"error",message:e.Message})}))}}}},"33c2":function(e,t,a){"use strict";a("826b")},"34bd":function(e,t,a){"use strict";a.r(t);var i=a("0eaf"),n=a("a799");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("d9bf");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"0084a04c",null);t["default"]=c.exports},3680:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var n=i(a("0f97")),o=i(a("a888")),l=a("d51a");t.default={directives:{elDragDialog:o.default},components:{DynamicDataTable:n.default},props:{selectRow:{type:Object,default:function(){}}},data:function(){return{btnLoading1:!1,btnLoading2:!1,form:{checkObjectId:"",checkObjectType:"",nodeId:"",SteelName:"",SteelId:"",checkType:"",Check_Type:"",Check_Style:1},qualityList:[],qualityItem:"",nodeList:[],Data:[],rules:{checkObjectId:[{required:!0,message:"请选择",trigger:"change"}],nodeId:[{required:!0,message:"请选择",trigger:"change"}],SteelName:[{required:!0,message:"请选择",trigger:"change"}],checkType:[{required:!0,message:"请选择",trigger:"change"}]},currentComponent:"",title:"",dialogVisible:!1,dialogTitle:"",width:"60%",type:"",addComTitle:"添加构件",CheckTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],CheckNodeList:[],CheckObjectData:[]}},watch:{selectRow:function(){"构件"===this.qualityItem.Display_Name?(this.form.SteelName=this.selectRow.SteelName,this.form.SteelId=this.selectRow.Id):(this.qualityItem.Display_Name,this.form.SteelName=this.selectRow.Code,this.form.SteelId=this.selectRow.Id)}},mounted:function(){this.getDictionaryDetailListByCode()},methods:{init:function(e){this.type=e||""},getDictionaryDetailListByCode:function(){var e=this;(0,l.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.qualityList=t.Data:e.$message({message:t.Message,type:"error"})}))},qualityListChange:function(e){e&&(this.form.nodeId="",this.form.SteelName="",this.form.SteelId="",this.form.checkType="",this.form.Check_Type="",this.getNodeList(),this.qualityItem=this.qualityList.find((function(t){return t.Id==e})),"构件"===this.qualityItem.Display_Name?this.form.checkObjectType=0:"零件"===this.qualityItem.Display_Name?this.form.checkObjectType=1:this.form.checkObjectType=3,this.$emit("qualityItemChange",this.qualityItem))},qualityListClear:function(e){this.nodeList=[],this.$refs.form.resetFields()},getNodeList:function(){var e=this;(0,l.GetNodeList)({check_object_id:this.form.checkObjectId,Check_Style:1}).then((function(t){t.IsSucceed?e.nodeList=t.Data:e.$message({message:t.Message,type:"error"})}))},nodeChange:function(e){var t=this;this.form.checkType="",e&&this.nodeList.find((function(a){return a.Id===e&&(-1==a.Check_Type?(t.form.Check_Type=a.Check_Type,t.form.checkType=""):(t.form.checkType=a.Check_Type,t.form.Check_Type="")),a.Id===e}))},nodeClear:function(e){this.form.checkType="",this.form.Check_Type=""},getCheckType:function(){var e=this;(0,l.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:"res.Message"})})).catch((function(){}))},chooseComponent:function(){this.$store.dispatch("qualityCheck/changeRadio",!0),this.$emit("openDialog")},handleClose:function(){this.dialogVisible=!1},submitForm:function(e){var t=this;e?this.btnLoading2=!0:this.btnLoading1=!0,this.$refs.form.validate((function(a){if(!a)return e?t.btnLoading2=!1:t.btnLoading1=!1,!1;var i=t.form,n=i.checkObjectType,o=i.checkObjectId,c=i.nodeId,s=i.SteelName,r=i.SteelId,d=i.checkType,u=i.Check_Style,f={SheetModel:{Check_Object_Type:n,Check_Object_Type_Id:o,Check_Node_Id:c,SteelName:s,Check_Object_Id:r,Check_Type:d,Check_Style:u},sumbimt:e};(0,l.ManageAdd)(f).then((function(a){a.IsSucceed?(t.$message({message:e?"提交成功":"保存成功",type:"success"}),t.$emit("close"),t.$emit("refresh"),e&&t.$emit("handleCheck",a.Data)):t.$message({message:a.Message,type:"error"}),e?t.btnLoading2=!1:t.btnLoading1=!1}))}))}}}},3805:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("4ec9"),a("b64b"),a("d3b7"),a("ac1f"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("5319"),a("5b81"),a("498a"),a("159b"),a("ddb0");var n=i(a("5530")),o=i(a("c14f")),l=i(a("1da1")),c=a("4d7a"),s=a("d51a"),r=i(a("db46")),d=i(a("333d")),u=a("c685"),f=i(a("15ac")),h=a("fd31"),m=a("7f9d");t.default={components:{Pagination:d.default},mixins:[f.default,r.default],props:{Code:{type:String,default:""},qualityItem:{type:Object,default:function(){}}},data:function(){return{treeSelectParams:{placeholder:"请选择",clearable:!0},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},tablePageSize:u.tablePageSize,loading:!1,pgLoading:!1,ProfessionalId:"",form:{Check_Object_Type:0,ProjectId:"",Area_Id:"",InstallUnit_Id:"",SearchCode:"",Steeltype:"",Code:"",PageInfo:{ParameterJson:[],Page:1,PageSize:20}},total:0,tbConfig:{},columns:[],tbData:[],ProfessionalType:[],selectList:[],selectRow:[],queryInfo:{Page:1,PageSize:10},gridCode:"pro_steel_page_list"}},computed:{isFull:function(){return this.getIsFull()},isRadio:function(){return this.$store.getters["qualityCheck/getIsRadioTb"]}},inject:["getIsFull"],created:function(){"构件"===this.qualityItem.Display_Name?(this.form.Check_Object_Type=0,this.getFactoryTypeOption("pro_steel_waiting_check_list")):"零件"===this.qualityItem.Display_Name?(this.form.Check_Object_Type=1,this.getFactoryTypeOption("pro_spare_waiting_check_list")):(this.form.Check_Object_Type=3,this.getFactoryTypeOption("pro_spare_waiting_check_list")),this.fetchData()},mounted:function(){var e=this;(0,c.getFactoryProfessional)().then((function(t){e.ProfessionalId=t[0].Id,0==e.form.Check_Object_Type&&e.getComponentTypeList()}))},methods:{checkMethod:function(e){var t=e.row;return!t.stopFlag},getFactoryTypeOption:function(e){var t=this;(0,h.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then(function(){var a=(0,l.default)((0,o.default)().m((function a(i){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:if(!i.IsSucceed){a.n=2;break}return t.ProfessionalType=i.Data,a.n=1,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code));case 1:3===t.form.Check_Object_Type&&t.columns.map((function(e){return"Code"===e.Code&&(e.Display_Name="部件名称"),e})),a.n=3;break;case 2:t.$message({message:i.Message,type:"error"});case 3:return a.a(2)}}),a)})));return function(e){return a.apply(this,arguments)}}())},init:function(e,t,a){this.type=t},fetchData:function(){var e=this;this.loading=!0;var t=this.form.Code.trim().replaceAll(" ","\n");Object.assign(this.form.PageInfo,this.queryInfo);var a=this.isFull?s.GetPartAndSteelBacrode:s.GetCompPartForSpotCheckPageList;a((0,n.default)((0,n.default)({},this.form),{},{Code:t})).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,"构件"===e.qualityItem.Display_Name?(e.tbData=t.Data.Data.map((function(e){return e["Inspction_Count"]=e["SteelAmount"],e["Steel_Count"]=0,e["Unqualified_Count"]=0,e["Qualified_Count"]=0,e["SetupPosition"]=e.Installunit_Name,e})),e.tbData=JSON.parse(JSON.stringify(e.tbData))):(e.qualityItem.Display_Name,e.tbData=t.Data.Data.map((function(e){return e["Inspction_Count"]=e["Num"],e["Steel_Count"]=0,e["Unqualified_Count"]=0,e["Qualified_Count"]=0,e["InstallUnit_Name"]=e.Installunit_Name,e["SteelWeight"]=e.Weight,e})),e.tbData=JSON.parse(JSON.stringify(e.tbData))),e.selectList=[],e.total=t.Data.TotalCount,e.getStopList(e.tbData)):e.$message({message:t.Message,type:"error"}),e.loading=!1}))},getStopList:function(e){var t=this;return(0,l.default)((0,o.default)().m((function a(){var i,n;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return i=t.isFull?"SteelUnique":"Id",n=e.map((function(e){return{Id:e[i],Type:"构件"===t.qualityItem.Display_Name?2:"零件"===t.qualityItem.Display_Name?1:3}})),a.n=1,(0,m.GetStopList)(n).then((function(a){if(a.IsSucceed){var n={};a.Data.forEach((function(e){n[e.Id]=!!e.Is_Stop})),e.forEach((function(e){n[e[i]]&&t.$set(e,"stopFlag",n[e[i]])}))}}));case 1:return a.a(2)}}),a)})))()},getComponentTypeList:function(){var e=this;(0,h.GetCompTypeTree)({professional:"Steel"}).then((function(t){if(t.IsSucceed){if(!t.Data)return;e.ObjectTypeList.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectObjectType.treeDataUpdateFun(t.Data)}))}}))},handleSearch:function(){this.queryInfo.Page=1,this.fetchData()},steelTypeChange:function(){},submitCheck:function(){var e=this,t=new Map,a=[];this.selectList.forEach((function(i){var n="";n="构件"===e.qualityItem.Display_Name?"".concat(i.ProjectId,"|").concat(i.Area_Id,"|").concat(i.Installunit_id,"|").concat(i.SteelName):"".concat(i.Project_Name,"-").concat(i.Area_Name,"-").concat(i.InstallUnit_Name,"-").concat(i.Code),t.has(n)?(a.push(i),t.set(n,t.get(n)+1)):t.set(n,1)})),a.length>0?this.$confirm("同项目、区域、批次下选中的有重复数据，确定后重复数据将会合并, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submit(a.length)})).catch((function(){e.$message({type:"info",message:"已取消提交"})})):this.submit(a.length)},submit:function(e){var t=this;this.pgLoading=!0,setTimeout((function(){if("批量"!==t.type)t.$emit("getSelectRow",t.selectRow);else if(e){var a=new Set,i=t.selectList.filter((function(e){var i="";return i="构件"===t.qualityItem.Display_Name?"".concat(e.ProjectId,"|").concat(e.Area_Id,"|").concat(e.Installunit_id,"|").concat(e.SteelName):"".concat(e.Project_Name,"-").concat(e.Area_Name,"-").concat(e.InstallUnit_Name,"-").concat(e.Code),!a.has(i)&&(a.add(i),!0)}));t.$emit("bitchaddCom",i)}else t.$emit("bitchaddCom",t.selectList);t.$emit("close")}),200)},multiSelectedChange:function(e){this.selectList=e.records},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},selectChange:function(e){var t=e.selection,a=e.row;"批量"!==this.type&&(this.$refs.table.$refs.dtable.clearSelection(),0!==t.length?this.selectRow=a:this.selectRow="",t.length>1&&t.shift(),this.$refs.table.$refs.dtable.toggleRowSelection(a,!!t.length))},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable.clearRadioRow()}}}},"3c28":function(e,t,a){"use strict";a.r(t);var i=a("e70c"),n=a("4eb0");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("ab0f");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"33d2e271",null);t["default"]=c.exports},"3f07":function(e,t,a){"use strict";a.r(t);var i=a("3805"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},"422d":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("c14f")),o=i(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");a("6186");var l=i(a("a888")),c=a("7de9"),s=a("221f"),r=a("d51a"),d=i(a("15ac"));t.default={components:{},directives:{elDragDialog:l.default},mixins:[d.default],data:function(){return{btnLoading1:!1,btnLoading2:!1,Check_Style:1,chooseTitle:"",Code:"",loading:!1,tbConfig:{},columns:[],tbData:[],queryInfo:{Page:1,TotalCount:0,PageSize:10},selectList:[],gridCode:"pro_bitch_steel_list",form:{Check_Object_Type:"",Check_Node_Id:"",Check_Type:"",checkTypeId:""},CheckTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],CheckNodeList:[],CheckObjectData:[],shawDialog:!1,isBatchAdd:!1,rules:{Check_Object_Type:[{required:!0,message:"请填写完整表单",trigger:"blur"}],Check_Node_Id:[{required:!0,message:"请填写完整表单",trigger:"blur"}],Check_Type:[{required:!0,message:"请填写完整表单",trigger:"blur"}]},title:"新建质检单",disable:!1,plm_Factory_Sheets:[]}},inject:["getIsFull"],computed:{isFull:function(){return this.getIsFull()}},mounted:function(){this.getCheckType()},methods:{handelInfo:function(e,t){var a=this;e.forEach((function(e){a.tbData.push(e)})),this.Check_Style=t,this.$nextTick((function(e){a.$refs.xTable.clearCheckboxRow()}))},handleClose:function(){this.shawDialog=!1,this.tbData=[],this.form={Check_Object_Type:"",Check_Node_Id:"",Check_Type:"",checkTypeId:""},this.disable=!1},init:function(e,t){this.detail=t,t||(this.title="新建质检单",this.isBatchAdd=!0),this.Code=e,this.shawDialog=!0,this.getGridByCode()},EditInfo:function(e){var t=this;(0,s.GetEditById)({Sheetid:e.Sheetid}).then((function(a){a.IsSucceed?(t.plm_Factory_Sheets=a.Data.plm_Factory_Sheets[0],t.form.Check_Object_Type=t.plm_Factory_Sheets.Check_Object_Type_Id,t.changeObject(t.form.Check_Object_Type),t.form.Check_Node_Id=t.plm_Factory_Sheets.Check_Node_Id,t.form.Check_Type=t.plm_Factory_Sheets.Check_Type,"构件"==e.Check_Object_Type?t.tbData=a.Data.Reads:("零件"==e.Check_Object_Type||"部件"==e.Check_Object_Type)&&(t.tbData=a.Data.Parts)):t.$message({type:"error",message:a.Message})}))},getNode:function(e){this.CheckObjectData.find((function(t){return t.Display_Name==e}))},getCheckType:function(){var e=this;(0,c.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:t.Message})})).catch((function(){}))},changeObject:function(e){var t;this.form.Check_Node_Id="",this.form.Check_Type="",this.tbData=[];var a=null===(t=this.CheckObjectData.find((function(t){return t.Id==e})))||void 0===t?void 0:t.Display_Name;switch(this.chooseTitle=a,a){case"构件":this.check_object_id="0",this.gridCode="pro_bitch_steel_list";break;case"零件":this.check_object_id="1",this.gridCode="pro_bitch_part_list";break;case"物料":this.check_object_id="2";break;case"部件":this.check_object_id="3",this.gridCode="pro_bitch_part_list";break;default:this.check_object_id="0"}this.getGridByCode(),this.getNodeList(e),this.qualityItem=this.CheckObjectData.find((function(t){return t.Id==e})),this.$emit("qualityItemChange",this.qualityItem)},qualityListClear:function(e){this.CheckNodeList=[],this.$refs.form.resetFields()},getNodeList:function(e){var t=this;(0,c.GetNodeList)({check_object_id:e,Check_Style:1}).then((function(e){e.IsSucceed?t.$nextTick((function(){t.CheckNodeList=e.Data})):t.$message({type:"error",message:"res.Message"})}))},changeCheckNode:function(e){this.form.Check_Type="",this.CheckTypeList=[];var t=this.CheckNodeList.find((function(t){return t.Id===e})).Check_Type;this.form.checkTypeId=t,"-1"==t?this.CheckTypeList=[{Name:"质量",Id:"1"},{Name:"探伤",Id:"2"}]:"1"==t?(this.CheckTypeList=[{Name:"质量",Id:"1"}],this.form.Check_Type=t):"2"==t&&(this.CheckTypeList=[{Name:"探伤",Id:"2"}],this.form.Check_Type=t)},checkNodeClear:function(){this.form.Check_Type=""},getGridByCode:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.loading=!0,t.n=1,e.getTableConfig(e.gridCode+","+e.Code);case 1:"3"===e.check_object_id&&e.columns.map((function(e){return"Code"===e.Code&&(e.Display_Name="部件名称"),e})),e.loading=!1;case 2:return t.a(2)}}),t)})))()},setCols:function(e){},multiSelectedChange:function(e){this.selectList=e.records},radioChangeEvent:function(e){var t=e.row;this.selectList=[t]},clearRadioRowEevnt:function(){this.selectList=[],this.$refs.xTable.clearRadioRow()},inputBlur:function(e,t,a){var i=Number(t);i<1||i>Number(a.SteelAmount)?a.Inspction_Count=a.SteelAmount:a.Inspction_Count=i},addInfo:function(){var e=[];this.tbData.forEach((function(t){e.push(t.Id)})),this.$emit("BitchopenDialog",this.check_object_id,this.chooseTitle,e)},handeldelete:function(){var e=this;this.selectList.forEach((function(t){var a=e.tbData.find((function(e){return e.Id===t.Id})),i=e.tbData.indexOf(a);e.tbData.splice(i,1)}))},AddSave:function(e,t){var a=this;t?this.btnLoading2=!0:this.btnLoading1=!0;var i=[];this.tbData.forEach((function(e){var t={};t.Check_Object_Type=a.check_object_id,t.Check_Node_Id=a.form.Check_Node_Id,t.Check_Type=a.form.Check_Type,t.SteelName=e.SteelName,t.Inspction_Count=e.Inspction_Count,t.Check_Object_Id=e.Id,t.Check_Object_Type_Id=a.form.Check_Object_Type,t.Check_Style=a.Check_Style,"零件"==a.chooseTitle&&(t.SteelName=e.Code),i.push(t)})),this.$refs[e].validate((function(e){if(!e)return t?a.btnLoading2=!1:a.btnLoading1=!1,!1;i.length>0?(0,s.AddLanch)({model:i,sumbimt:t,IsFromQualityInspector:!0}).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"保存成功"}),a.handleClose(),a.$emit("refresh"),a.tbData=[]):a.$message({type:"warning",message:e.Message}),t?a.btnLoading2=!1:a.btnLoading1=!1})):(t?a.btnLoading2=!1:a.btnLoading1=!1,a.$message({type:"warning",message:"保存数据不能为空"}))}))},batchManageSaveCheck:function(e,t){var a=this,i=[];this.tbData.forEach((function(e){var t={};t.Check_Object_Type=a.check_object_id,t.Check_Node_Id=a.form.Check_Node_Id,t.Check_Type=a.form.Check_Type,t.SteelName=e.SteelName,t.Inspction_Count=e.Inspction_Count,t.Check_Object_Id=e.Id,t.Check_Object_Type_Id=a.form.Check_Object_Type,t.Check_Style=a.Check_Style,t.InstallUnit_Id=e.InstallUnit_Id,"零件"==a.chooseTitle&&(t.SteelName=e.Code),i.push(t)}));var n={IsSubmit:t,Is_Rectification:!0,ZG_Time_Limit:0,ZG_UserId:"string"};this.$refs[e].validate((function(e){if(!e)return!1;i.length>0?(0,r.BatchManageSaveCheck)({CheckList:i,SubmitInfo:n}).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"保存成功"}),a.handleClose(),a.$emit("refresh"),a.tbData=[]):a.$message({type:"warning",message:e.Message})})):a.$message({type:"warning",message:"保存数据不能为空"})}))}}}},"468df":function(e,t,a){"use strict";a("16ac")},"4b4d":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"add-com-wapper",attrs:{"element-loading-text":"正在处理"}},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"100px"}},[a("el-form-item",{attrs:{label:"项目名称",prop:"ProjectId"}},[a("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.ProjectId,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",attrs:{disabled:!e.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:e.setupPositionChange},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"名称",prop:"Code"}},[a("el-input",{attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text",clearable:""},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),a("el-form-item",{attrs:{label:"名称(模糊)",prop:"SearchCode"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text",clearable:""},model:{value:e.form.SearchCode,callback:function(t){e.$set(e.form,"SearchCode",t)},expression:"form.SearchCode"}})],1),0==e.form.Check_Object_Type?a("el-form-item",{attrs:{label:"构件类型",prop:"Steeltype"}},[a("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",staticStyle:{width:"100%"},attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},model:{value:e.form.Steeltype,callback:function(t){e.$set(e.form,"Steeltype",t)},expression:"form.Steeltype"}})],1):e._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","checkbox-config":{checkField:"checked",checkMethod:e.checkMethod},"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","show-overflow":"",loading:e.loading,stripe:"",size:"medium",data:e.tbData,"radio-config":{highlight:!0,checkMethod:e.checkMethod},resizable:"","tooltip-config":{enterable:!0}},on:{"radio-change":e.radioChangeEvent,"checkbox-all":e.multiSelectedChange,"checkbox-change":e.multiSelectedChange}},[e.isRadio?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox"}}),e._l(e.columns,(function(t){return[["SteelName","Code"].includes(t.Code)?a("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(i){var n=i.row;return[n.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),a("span",[e._v(e._s(n[t.Code]))])]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width}})]}))],2)],1),a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.selectList.length)+"条数据")])],1),a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",disabled:e.isRadio&&!e.selectRow||!e.isRadio&&!e.selectList.length},on:{click:function(t){return e.submitCheck()}}},[e._v("保 存")])],1)],1)])},n=[]},"4eb0":function(e,t,a){"use strict";a.r(t);var i=a("3680"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},"4ec9":function(e,t,a){"use strict";a("6f48")},5083:function(e,t,a){"use strict";a("0618")},"586ae":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form.Feedmodel,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"单据状态",prop:"Status"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Status,callback:function(t){e.$set(e.form.Feedmodel,"Status",t)},expression:"form.Feedmodel.Status"}},[a("el-option",{attrs:{label:"草稿",value:0}}),a("el-option",{attrs:{label:"待整改",value:1}}),a("el-option",{attrs:{label:"待复核",value:2}}),a("el-option",{attrs:{label:"待质检",value:4}}),a("el-option",{attrs:{label:"已完成",value:3}})],1)],1)],1),a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"质检结果",prop:"Sheet_result"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Sheet_result,callback:function(t){e.$set(e.form.Feedmodel,"Sheet_result",t)},expression:"form.Feedmodel.Sheet_result"}},[a("el-option",{attrs:{label:"合格",value:"合格"}}),a("el-option",{attrs:{label:"不合格",value:"不合格"}})],1)],1)],1),a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.qualityListChange,clear:e.qualityListClear},model:{value:e.form.Feedmodel.Check_Object_Type_Id,callback:function(t){e.$set(e.form.Feedmodel,"Check_Object_Type_Id",t)},expression:"form.Feedmodel.Check_Object_Type_Id"}},e._l(e.qualityList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!Boolean(e.form.Feedmodel.Check_Object_Type_Id)},on:{change:e.nodeChange},model:{value:e.form.Feedmodel.Check_Node_Id,callback:function(t){e.$set(e.form.Feedmodel,"Check_Node_Id",t)},expression:"form.Feedmodel.Check_Node_Id"}},e._l(e.nodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Project_Id,callback:function(t){e.$set(e.form.Feedmodel,"Project_Id",t)},expression:"form.Feedmodel.Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:6}},[a("el-form-item",{attrs:{label:"报检时间",prop:"Check_Time"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.form.Feedmodel.Check_Time,callback:function(t){e.$set(e.form.Feedmodel,"Check_Time",t)},expression:"form.Feedmodel.Check_Time"}})],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:6}},[a("el-form-item",{attrs:{label:"名称",prop:"SteelName"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入（空格间隔筛选多个）",clearable:""},model:{value:e.form.Feedmodel.SteelName,callback:function(t){e.$set(e.form.Feedmodel,"SteelName",t)},expression:"form.Feedmodel.SteelName"}})],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:3}},[a("el-form-item",{attrs:{label:"来源",prop:"Source_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Source_Type,callback:function(t){e.$set(e.form.Feedmodel,"Source_Type",t)},expression:"form.Feedmodel.Source_Type"}},[a("el-option",{attrs:{label:"手动创建",value:"手动创建"}}),a("el-option",{attrs:{label:"业务推送",value:"业务推送"}})],1)],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:3}},[a("el-form-item",{attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addCheck("add")}}},[e._v("新增")]),a("el-button",{on:{click:function(t){return e.addCheck()}}},[e._v("批量新增")]),a("el-button",{attrs:{disabled:0==e.selectList.length},on:{click:function(t){return e.allPass()}}},[e._v("批量合格")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding cs-main",staticStyle:{padding:"0"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Number",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Number||"-"))])]}},{key:"Rectify_Date",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Rectify_Date||"-"))])]}},{key:"Rectifier_name",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Rectify_Date?i.Rectifier_name:"-"))])]}},{key:"Partcipant_name",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Partcipant_name||"-"))])]}},{key:"SteelName",fn:function(t){var i=t.row;return[i.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),a("span",[e._v(e._s(i.SteelName||"-"))])]}},{key:"Check_Result",fn:function(t){var i=t.row;return[i.Check_Result?["合格"===i.Check_Result?a("el-tag",{attrs:{type:"success"}},[e._v(e._s(i.Check_Result))]):a("el-tag",{attrs:{type:"danger"}},[e._v(e._s(i.Check_Result))])]:a("span",[e._v("-")])]}},{key:"Status",fn:function(t){var i=t.row;return["已完成"===i.Status?a("span",{staticClass:"by-dot by-dot-success"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待复核"===i.Status||"待整改"===i.Status?a("span",{staticClass:"by-dot by-dot-primary"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待质检"===i.Status||"草稿"===i.Status?a("span",{staticClass:"by-dot by-dot-info"},[e._v(" "+e._s(i.Status||"-")+" ")]):a("span",[e._v(" "+e._s(i.Status||"-")+" ")])]}},{key:"Pick_Date",fn:function(t){var i=t.row;return[a("div",[e._v(e._s(i.Pick_Date||"-"))])]}},{key:"Code",fn:function(t){var i=t.row;return[a("div",[i.Code?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleRecord(i)}}},[e._v("查看")]):a("div",[e._v("-")])],1)]}},{key:"op",fn:function(t){var i=t.row,n=t.index;return["草稿"===i.Status?a("div",[a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleSub(i.SheetId)}}},[e._v("提交")]),a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleDel(i.SheetId)}}},[e._v("删除")])],1):"待质检"===i.Status?a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleCheck(i.SheetId)}}},[e._v("质检")]):a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleInfo(i.SheetId)}}},[e._v("查看")])]}}])})],1)],1)]),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"480px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-row":e.selectRow},on:{openDialog:e.openDialog,close:e.handleClose,refresh:e.fetchData,qualityItemChange:e.qualityItemChange,handleCheck:e.handleCheck}})],1):e._e(),e.dialogVisible2?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content2",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle2,visible:e.dialogVisible2,width:"66%"},on:{"update:visible":function(t){e.dialogVisible2=t},close:e.handleClose2}},[a(e.currentComponent2,{ref:"content2",tag:"component",attrs:{"quality-item":e.qualityItem},on:{close:e.handleClose2,getSelectRow:e.getSelectRow,bitchaddCom:e.bitchaddCom}})],1):e._e(),a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content3",staticClass:"plm-custom-dialog",attrs:{title:"操作记录",visible:e.dialogVisible3,width:"50%"},on:{"update:visible":function(t){e.dialogVisible3=t},close:e.handleClose3}},[a(e.currentComponent3,{ref:"content3",tag:"component",on:{close:e.handleClose3}})],1),a("selected-Dialog",{ref:"selectRef",on:{BitchopenDialog:e.BitchopenDialog,refresh:e.fetchData,qualityItemChange:e.qualityItemChange}})],1)},n=[]},"64d28":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog_wapper"},[e.showDialog?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",staticStyle:{"margin-top":"-5vh"},attrs:{title:e.title,visible:e.showDialog,width:"60%"},on:{"update:visible":function(t){e.showDialog=t},close:e.handleClose}},[a("div",{staticClass:"select_Wapper"},[e.rectificationOrder?a("el-form",{ref:"formOrder",attrs:{model:e.plm_Factory_Sheets,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"整改单号",prop:"Code"}},[a("el-input",{attrs:{value:e.plm_Factory_Sheets.Code,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"整改人",prop:"Rectifier_name"}},[a("el-input",{attrs:{value:e.plm_Factory_Sheets.Rectifier_name,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"整改时限",prop:"Rectify_Date"}},[a("el-input",{attrs:{value:e.plm_Factory_Sheets.Rectify_Date,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"状态",prop:"Status"}},[a("el-input",{attrs:{value:e.plm_Factory_Sheets.Status,type:"text",disabled:""}})],1)],1):a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:e.disable},on:{change:e.changeObject,clear:e.qualityListClear},model:{value:e.form.Check_Object_Type,callback:function(t){e.$set(e.form,"Check_Object_Type",t)},expression:"form.Check_Object_Type"}},e._l(e.CheckObjectData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!e.form.Check_Object_Type||e.disable},on:{change:e.changeCheckNode,clear:e.checkNodeClear},model:{value:e.form.Check_Node_Id,callback:function(t){e.$set(e.form,"Check_Node_Id",t)},expression:"form.Check_Node_Id"}},e._l(e.CheckNodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-select",{attrs:{placeholder:"请选择",disabled:!e.form.Check_Node_Id||e.disable||"-1"!=e.form.checkTypeId},model:{value:e.form.Check_Type,callback:function(t){e.$set(e.form,"Check_Type",t)},expression:"form.Check_Type"}},e._l(e.CheckTypeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检结果",prop:"Sheet_Result"}},[a("el-select",{attrs:{placeholder:"请选择",disabled:e.isSee},on:{change:e.getCount},model:{value:e.form.Sheet_Result,callback:function(t){e.$set(e.form,"Sheet_Result",t)},expression:"form.Sheet_Result"}},[a("el-option",{attrs:{label:"合格",value:"合格"}}),a("el-option",{attrs:{label:"不合格",value:"不合格"}})],1)],1)],1)],1),e.isCheck?e._e():a("div",{staticStyle:{margin:"20px 0"}},[a("el-button",{attrs:{type:"primary",disabled:!e.form.Check_Object_Type},on:{click:e.addInfo}},[e._v("添 加")]),a("el-button",{attrs:{type:"danger",disabled:!e.selectList.length},on:{click:e.handeldelete}},[e._v("删 除 ")])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fff cs-z-tb-wrapper"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","checkbox-config":{checkField:"checked",checkMethod:e.checkMethod},"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","show-overflow":"",loading:e.loading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.multiSelectedChange,"checkbox-change":e.multiSelectedChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox"}}),e._l(e.columns,(function(t){return[["SteelName","Code"].includes(t.Code)?a("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(i){var n=i.row;return[n.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),a("span",[e._v(e._s(n[t.Code]))])]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(i){var n=i.row;return["Inspction_Count"==t.Code?a("div",[a("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"50px",border:"1px solid #eee","border-radius":"4px"},attrs:{type:"text",readonly:1==n.SteelAmount||1==n.Num||"业务推送"==n.Source_Type,disabled:!0},on:{blur:function(t){e.inputBlur1(t,n.Inspction_Count,n)}},model:{value:n.Inspction_Count,callback:function(t){e.$set(n,"Inspction_Count",t)},expression:"row.Inspction_Count"}})],1):"Steel_Count"==t.Code?a("div",[a("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"50px",border:"1px solid #eee","border-radius":"4px"},attrs:{type:"text",readonly:1==n.SteelAmount||1==n.Num,disabled:e.isSee||e.rectificationOrder},on:{blur:function(t){e.inputBlur2(t,n.Steel_Count,n)}},model:{value:n.Steel_Count,callback:function(t){e.$set(n,"Steel_Count",t)},expression:"row.Steel_Count"}})],1):"Unqualified_Count"==t.Code?a("div",[a("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"50px",border:"1px solid #eee","border-radius":"4px"},attrs:{type:"text",disabled:e.isSee||e.rectificationOrder},on:{blur:function(t){e.inputBlur3(t,n.Unqualified_Count,n)}},model:{value:n.Unqualified_Count,callback:function(t){e.$set(n,"Unqualified_Count",t)},expression:"row.Unqualified_Count"}})],1):a("div",[e._v(" "+e._s(e._f("displayValue")(n[t.Code]))+" ")])]}}],null,!0)})]}))],2)],1),e.rectificationOrder?e._e():a("div",{staticClass:"input_Wapper"},[a("el-form",{ref:"form2",attrs:{model:e.form2,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"报检数",prop:"value1"}},[a("el-input",{attrs:{value:e.form2.value1,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"总不合格数量",prop:"value2"}},[a("el-input",{attrs:{value:e.form2.value2,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"总合格数量",prop:"value3"}},[a("el-input",{attrs:{value:e.form2.value3,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"合格率",prop:"value4"}},[a("el-input",{attrs:{value:e.form2.value4,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"抽检率",prop:"value5"}},[a("el-input",{attrs:{value:e.form2.value5,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"要求合格率",prop:"value6"}},[e._v(" "+e._s(e.form2.value6)+" ")])],1)],1),e.isSee?e._e():a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(t){return e.handleClose()}}},[e._v("取 消")]),e.isCheck?e._e():a("el-button",{attrs:{type:"primary",loading:e.btnLoading1},on:{click:function(t){return e.batchManageSaveCheck("form",!1)}}},[e._v("保 存")]),e.isCheck?e._e():a("el-button",{attrs:{type:"primary",loading:e.btnLoading2},on:{click:function(t){return e.batchManageSaveCheck("form",!0)}}},[e._v("提交并质检")]),e.isCheck?a("el-button",{attrs:{type:"primary",loading:e.btnLoading2},on:{click:function(t){return e.batchManageSaveCheck("form",!0)}}},[e._v("质检")]):e._e()],1)]):e._e()],1)},n=[]},"68ee7":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper"},[a("div",{staticClass:"wrapper-main"},e._l(e.list,(function(t,i){return a("ul",{key:i},[a("li",{staticClass:"top"},[a("span",{staticClass:"left-title"},[a("i"),e._v(e._s(t.Reply.Create_UserName))]),a("span",{staticStyle:{color:"#298dff","margin-right":"10px"}},[e._v(e._s(t.Reply.Type))]),a("span",[e._v(e._s(t.Reply.ActionTime))])]),"复核"==t.Reply.Type?a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Reply.Type)+"状态")]),a("span",{staticStyle:{color:"#00c361"}},[e._v(e._s(t.Reply.IsPass?"合格":"不合格"))])]):e._e(),a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Reply.Type)+"内容")]),a("span",[e._v(e._s(t.Reply.Suggestion))])]),a("li",[a("div",{staticClass:"left-title"},[e._v("图片")]),e._l(t.Attachments,(function(t,i){return a("div",{key:i,staticClass:"img_Wrapper"},[a("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:t.File_Url,"preview-src-list":e.srcList}})],1)}))],2)])})),0)])},n=[]},"6f48":function(e,t,a){"use strict";var i=a("6d61"),n=a("6566");i("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n)},"704e":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("5530")),o=i(a("c14f")),l=i(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var c=i(a("762f")),s=i(a("229d")),r=i(a("7d62")),d=(a("6186"),a("221f"),i(a("0f97"))),u=i(a("3c28")),f=i(a("a888")),h=a("d51a"),m=a("fd31"),p=a("1b69"),_=i(a("5cc7")),g=i(a("2082")),b=i(a("1710")),C=a("ed08");t.default={directives:{elDragDialog:f.default},components:{DynamicDataTable:d.default,addDialog:u.default,addComponent:c.default,rectificationDialog:s.default,rectificationorderDialog:r.default,selectedDialog2:b.default},mixins:[g.default,_.default],data:function(){return{code:"",TypeId:"",typeOption:"",dialogVisible:!1,dialogVisible2:!1,dialogVisible3:!1,dialogVisible4:!1,loading:!1,dialogTitle:"",dialogTitle2:"",Ismodal:!0,dialogData:{},currentComponent:"",currentComponent2:"",currentComponent3:"rectificationDialog",tbConfig:{Op_Width:120},Data:[],form:{Feedmodel:{Status:"",Sheet_result:"",Check_Object_Type_Id:"",Check_Node_Id:"",Project_Id:"",Source_Type:"",Check_Style:0},PageInfo:{Page:1,PageSize:20}},selectList:[],qualityList:[],qualityItem:{},selectRow:{},nodeList:[],projectList:[],Date_Time:"",columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:20},total:0,gridCode:"pro_start_inspect",searchHeight:0,pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},addPageArray:[{path:this.$route.path+"/check",hidden:!0,component:function(){return a.e("chunk-9bbd8072").then(a.bind(null,"fc08"))},name:"PROQualityTesting",meta:{title:"质检"}}],submitSpotObj:"",sourceType:0,rectifier_id:""}},provide:function(){var e=this;return{sourceType:function(){return e.sourceType},rectifierId:function(){return e.rectifier_id}}},created:function(){this.getFactoryTypeOption(),this.getDictionaryDetailListByCode(),this.getProjectPageList()},mounted:function(){this.searchHeight=this.$refs.searchDom.offsetHeight+200},methods:{getFactoryTypeOption:function(){var e=this;return(0,l.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,m.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?(e.ProfessionalType=t.Data,e.code=e.ProfessionalType[0].Code):e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_spot_order_list,".concat(e.ProfessionalType[0].Code));case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;this.loading=!0;var t=(0,n.default)({},this.form);(0,h.GetPageQualityManagement)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Rectify_Date=e.Rectify_Date?(0,C.parseTime)(new Date(e.Rectify_Date),"{y}-{m}-{d}"):e.Rectify_Date,e.Pick_Date=e.Pick_Date?(0,C.parseTime)(new Date(e.Pick_Date),"{y}-{m}-{d}"):e.Pick_Date,e.Id=e.SheetId,e})),e.total=t.Data.TotalCount,e.loading=!1):e.$message({message:t.Message,type:"error"})}))},getDictionaryDetailListByCode:function(){var e=this;(0,h.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.qualityList=t.Data:e.$message({message:t.Message,type:"error"})}))},qualityListChange:function(e){e&&(this.form.Feedmodel.Check_Node_Id="",this.getNodeList())},qualityListClear:function(e){this.form.Feedmodel.Check_Node_Id="",this.nodeList=[]},getNodeList:function(){var e=this;(0,h.GetNodeList)({check_object_id:this.form.Feedmodel.Check_Object_Type_Id,Check_Style:0}).then((function(t){t.IsSucceed?e.nodeList=t.Data:e.$message({message:t.Message,type:"error"})}))},nodeChange:function(e){var t=this;e&&this.nodeList.find((function(a){a.Id===e&&(t.form.Feedmodel.checkType=a.Check_Type)}))},getProjectPageList:function(){var e=this;(0,p.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)}))},rectificationInfo:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].init(t.code,e,!0,!0,!0)}))},handleRecord:function(e){this.getrectificationRecord(e)},getrectificationRecord:function(e){var t=this;(0,h.RectificationRecord)({sheetid:e.SheetId}).then((function(a){a.IsSucceed?0===a.Data.length?t.$message({type:"error",message:"暂无操作记录"}):(t.dialogVisible3=!0,t.$nextTick((function(a){t.$refs["content3"].init(e)}))):t.$message({type:"error",message:a.Message})}))},rectificationOrderInfo:function(e){this.dialogVisible4=!0,this.submitSpotObj=e},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},changesearchDate:function(e){},allPass:function(){var e=this,t=[];this.selectList.forEach((function(e){t.push(e.Id)})),(0,h.SavePass)({ids:t}).then((function(t){t.IsSucceed?e.$message({message:"质检成功",type:"success"}):e.$message({message:t.Message,type:"error"})}))},bitchaddCom:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].handelInfo(e,0)}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{}),this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){this.columns=e},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount,this.TotalAmount=e.TotalAmount,this.TotalWeight=e.TotalWeight},multiSelectedChange:function(e){this.selectList=e},qualityItemChange:function(e){this.qualityItem=e},getSelectRow:function(e){this.selectRow=e},generateComponent:function(e,t,a){a&&2==a?(this.dialogTitle2=e,this.currentComponent2=t,this.dialogVisible2=!0):(this.dialogTitle=e,this.currentComponent=t,this.dialogVisible=!0)},openDialog:function(){var e=this;"构件"===this.qualityItem.Display_Name?this.generateComponent("添加构件","addComponent",2):this.generateComponent("添加零件","addComponent",2),this.$nextTick((function(t){e.$refs["content2"].init()}))},BitchopenDialog:function(e,t,a){var i=this;this.generateComponent("添加".concat(t),"addComponent",2),this.$nextTick((function(t){i.$refs["content2"].init(e,"批量",a)}))},addCheck:function(e){var t=this;this.sourceType=0,this.$store.dispatch("qualityCheck/changeRadio",!1),e?this.$nextTick((function(a){t.$refs["selectRef"].init(t.code,e,!1)})):this.$nextTick((function(e){t.$refs["selectRef"].init(t.code,"",!1)}))},handleCheck:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].init(t.code,e,!0)})),"手动创建"==e.Source_Type||"自建"==e.Source_Type?this.sourceType=0:(this.sourceType=1,this.rectifier_id=e.Rectifier_Id)},handleInfo:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].init(t.code,e,!0,!0)}))},handleEdit:function(e){},handleSub:function(e){var t=this;this.$confirm("提交该草稿, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,h.SubmitLanch)({sheetid:e}).then((function(e){e.IsSucceed?(t.$message({message:"提交成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleDel:function(e){var t=this;this.$confirm("删除该草稿, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,h.DelLanch)({sheetids:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleClose:function(){this.dialogVisible=!1},handleClose2:function(){this.dialogVisible2=!1},handleClose3:function(){this.dialogVisible3=!1},handleClose4:function(){this.dialogVisible4=!1}}}},"762f":function(e,t,a){"use strict";a.r(t);var i=a("4b4d"),n=a("3f07");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("5083");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"872db9d2",null);t["default"]=c.exports},"7a9c2":function(e,t,a){},"7b92":function(e,t,a){"use strict";a.r(t);var i=a("85267"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},"7d62":function(e,t,a){"use strict";a.r(t);var i=a("928c"),n=a("2bfe");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("7dd3");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"c39ef9b0",null);t["default"]=c.exports},"7dd3":function(e,t,a){"use strict";a("20cc")},"826b":function(e,t,a){},85267:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("c14f")),o=i(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("5319"),a("5b81"),a("498a"),a("159b"),a("ddb0");var l=i(a("762f")),c=i(a("229d")),s=(a("6186"),a("221f"),i(a("0f97"))),r=i(a("3c28")),d=i(a("a888")),u=a("d51a"),f=a("fd31"),h=a("1b69"),m=i(a("5cc7")),p=i(a("2082")),_=i(a("941db")),g=a("ed08"),b=(a("8975"),a("5c96"),a("7f9d"));t.default={directives:{elDragDialog:d.default},components:{DynamicDataTable:s.default,addDialog:r.default,addComponent:l.default,rectificationDialog:c.default,selectedDialog:_.default},mixins:[p.default,m.default],data:function(){return{code:"",TypeId:"",typeOption:"",dialogVisible:!1,dialogVisible2:!1,dialogVisible3:!1,loading:!1,dialogTitle:"",dialogTitle2:"",Ismodal:!0,dialogData:{},currentComponent:"",currentComponent2:"",currentComponent3:"rectificationDialog",tbConfig:{Op_Width:100},Data:[],form:{Feedmodel:{Status:"",Sheet_result:"",Check_Object_Type_Id:"",Check_Node_Id:"",Project_Id:"",Source_Type:"",SteelName:"",Check_Style:1,Check_Time:[],BeginDate:null,EndDate:null},PageInfo:{Page:1,PageSize:20}},selectList:[],qualityList:[],qualityItem:{},selectRow:{},nodeList:[],projectList:[],Date_Time:"",columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:20},total:0,gridCode:"pro_start_inspect",searchHeight:0,pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}],code:""},addPageArray:[{path:this.$route.path+"/check",hidden:!0,component:function(){return a.e("chunk-9bbd8072").then(a.bind(null,"fc08"))},name:"PROQualityTesting",meta:{title:"质检"}},{path:this.$route.path+"/check",hidden:!0,component:function(){return a.e("chunk-9bbd8072").then(a.bind(null,"fc08"))},name:"PROQualityInfo",meta:{title:"查看"}}]}},created:function(){this.getFactoryTypeOption(),this.getDictionaryDetailListByCode(),this.getProjectPageList()},mounted:function(){this.searchHeight=this.$refs.searchDom.offsetHeight+200},methods:{getFactoryTypeOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?(e.ProfessionalType=t.Data,e.code=e.ProfessionalType[0].Code):e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_check_order_list,".concat(e.ProfessionalType[0].Code));case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;this.loading=!0;var t=JSON.parse(JSON.stringify(this.form));t.Feedmodel.SteelName&&(t.Feedmodel.SteelName=t.Feedmodel.SteelName.trim().replaceAll(" ","\n")),t.Feedmodel.Check_Time&&2===t.Feedmodel.Check_Time.length?(t.Feedmodel.BeginDate=t.Feedmodel.Check_Time[0],t.Feedmodel.EndDate=t.Feedmodel.Check_Time[1]):(t.Feedmodel.BeginDate=null,t.Feedmodel.EndDate=null),(0,u.GetPageQualityManagement)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Rectify_Date=e.Rectify_Date?(0,g.parseTime)(new Date(e.Rectify_Date),"{y}-{m}-{d}"):e.Rectify_Date,e.Pick_Date=e.Pick_Date?(0,g.parseTime)(new Date(e.Pick_Date),"{y}-{m}-{d}"):e.Pick_Date,e.Id=e.SheetId,e})),e.total=t.Data.TotalCount,e.loading=!1,e.getStopList(e.tbData)):e.$message({message:t.Message,type:"error"})}))},getStopList:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){var i,o;return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return i="Check_Object_Id",o=e.map((function(e){return{Id:e[i],Type:"构件"===e.Check_Object_Type?2:"零件"===e.Check_Object_Type?3:1}})),a.n=1,(0,b.GetStopList)(o).then((function(a){if(a.IsSucceed){var n={};a.Data.forEach((function(e){n[e.Id]=!!e.Is_Stop})),e.forEach((function(e){n[e[i]]&&t.$set(e,"stopFlag",n[e[i]])}))}}));case 1:return a.a(2)}}),a)})))()},getDictionaryDetailListByCode:function(){var e=this;(0,u.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.qualityList=t.Data:e.$message({message:t.Message,type:"error"})}))},qualityListChange:function(e){e&&(this.form.Feedmodel.Check_Node_Id="",this.getNodeList())},qualityListClear:function(e){this.form.Feedmodel.Check_Node_Id="",this.nodeList=[]},getNodeList:function(){var e=this;(0,u.GetNodeList)({check_object_id:this.form.Feedmodel.Check_Object_Type_Id,Check_Style:1}).then((function(t){t.IsSucceed?e.nodeList=t.Data:e.$message({message:t.Message,type:"error"})}))},nodeChange:function(e){var t=this;e&&this.nodeList.find((function(a){a.Id===e&&(t.form.Feedmodel.checkType=a.Check_Type)}))},getProjectPageList:function(){var e=this;(0,h.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)}))},handleRecord:function(e){this.getrectificationRecord(e)},getrectificationRecord:function(e){var t=this;(0,u.RectificationRecord)({sheetid:e.SheetId}).then((function(a){a.IsSucceed?0===a.Data.length?t.$message({type:"error",message:"暂无操作记录"}):(t.dialogVisible3=!0,t.$nextTick((function(a){t.$refs["content3"].init(e)}))):t.$message({type:"error",message:a.Message})}))},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},changesearchDate:function(e){},allPass:function(){var e=this,t=[];this.selectList.forEach((function(e){t.push(e.SheetId)})),this.$confirm("质检该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.SavePass)({ids:t}).then((function(t){t.IsSucceed?(e.$message({message:"质检成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){}))},bitchaddCom:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].handelInfo(e,1)}))},multiSelectedChange:function(e){this.selectList=e},qualityItemChange:function(e){this.qualityItem=e},getSelectRow:function(e){this.selectRow=e},generateComponent:function(e,t,a){a&&2==a?(this.dialogTitle2=e,this.currentComponent2=t,this.dialogVisible2=!0):(this.dialogTitle=e,this.currentComponent=t,this.dialogVisible=!0)},openDialog:function(){var e=this;"构件"===this.qualityItem.Display_Name?this.generateComponent("添加构件","addComponent",2):"零件"===this.qualityItem.Display_Name?this.generateComponent("添加零件","addComponent",2):this.generateComponent("添加部件","addComponent",2),this.$nextTick((function(t){e.$refs["content2"].init()}))},BitchopenDialog:function(e,t,a){var i=this;this.generateComponent("添加".concat(t),"addComponent",2),this.$nextTick((function(t){i.$refs["content2"].init(e,"批量",a)}))},addCheck:function(e){var t=this;"add"===e?(this.$store.dispatch("qualityCheck/changeRadio",!0),this.generateComponent("新增","addDialog"),this.$nextTick((function(e){t.$refs["content"].init("新增")}))):(this.$store.dispatch("qualityCheck/changeRadio",!1),this.$nextTick((function(e){t.$refs["selectRef"].init(t.code)})))},handleClose:function(){this.dialogVisible=!1},handleClose2:function(){this.dialogVisible2=!1},handleClose3:function(){this.dialogVisible3=!1},handleEdit:function(e){},handleSub:function(e){var t=this;this.$confirm("提交该草稿, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.SubmitLanch)({sheetid:e}).then((function(e){e.IsSucceed?(t.$message({message:"提交成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleDel:function(e){var t=this;this.$confirm("删除该草稿, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.DelLanch)({sheetids:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleCheck:function(e){this.$router.push({name:"PROQualityTesting",query:{pg_redirect:"PROQualityManagement",sheetId:e,isSee:!1}})},handleInfo:function(e){this.$router.push({name:"PROQualityInfo",query:{pg_redirect:"PROQualityManagement",sheetId:e,isSee:!0}})}}}},"8dd3":function(e,t,a){"use strict";a.r(t);var i=a("704e"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},92838:function(e,t,a){"use strict";a("7a9c2")},"928c":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"是否生成整改单",prop:"Is_Rectification",rules:{required:!0,message:"请选择",trigger:"change"}}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Is_Rectification,callback:function(t){e.$set(e.form,"Is_Rectification",t)},expression:"form.Is_Rectification"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),a("el-form-item",{attrs:{label:"整改人",prop:"ZG_UserId",rules:{required:!!e.form.Is_Rectification,message:"请选择",trigger:"change"}}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",disabled:0==e.form.Is_Rectification},model:{value:e.form.ZG_UserId,callback:function(t){e.$set(e.form,"ZG_UserId",t)},expression:"form.ZG_UserId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"整改时限",prop:"ZG_Time_Limit"}},[a("el-date-picker",{attrs:{align:"right",type:"date",placeholder:"选择日期",disabled:0==e.form.Is_Rectification},model:{value:e.form.ZG_Time_Limit,callback:function(t){e.$set(e.form,"ZG_Time_Limit",t)},expression:"form.ZG_Time_Limit"}})],1),a("el-form-item",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm()}}},[e._v("提交")])],1)],1)],1)},n=[]},"941db":function(e,t,a){"use strict";a.r(t);var i=a("05eb"),n=a("27a1");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("0a40");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"446292bd",null);t["default"]=c.exports},a799:function(e,t,a){"use strict";a.r(t);var i=a("0a00"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},aab8:function(e,t,a){"use strict";a.r(t);var i=a("cd9b"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},ab0f:function(e,t,a){"use strict";a("f8fd")},c7bd:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("0f97")),o=i(a("a888")),l=a("d51a"),c=a("ed08");t.default={directives:{elDragDialog:o.default},components:{DynamicDataTable:n.default},props:{selectRow:{type:Object,default:function(){}},submitSpotObj:{type:Object,default:function(){}}},inject:["sourceType","rectifierId"],data:function(){return{form:{Is_Rectification:"",ZG_Time_Limit:"",ZG_UserId:""},userList:[]}},computed:{computedSourceType:function(){return this.sourceType()},computedRectifierId:function(){return this.rectifierId()}},mounted:function(){1==this.computedSourceType&&(this.form.ZG_UserId=this.computedRectifierId),this.getFactoryPeoplelist()},methods:{init:function(e){this.type=e||""},getFactoryPeoplelist:function(){var e=this;(0,l.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;e.submitSpotObj.SubmitInfo.Is_Rectification=e.form.Is_Rectification,e.submitSpotObj.SubmitInfo.ZG_Time_Limit=e.submitSpotObj.SubmitInfo.ZG_Time_Limit||e.form.ZG_Time_Limit?(0,c.parseTime)(e.form.ZG_Time_Limit,"{y}-{m}-{d}"):"",e.submitSpotObj.SubmitInfo.ZG_UserId=e.form.ZG_UserId,(0,l.BatchManageSaveCheck)(e.submitSpotObj).then((function(t){t.IsSucceed?(e.$refs.form.resetFields(),e.$message({type:"success",message:"质检成功"}),e.$emit("close"),e.$emit("refresh")):e.$message({type:"warning",message:t.Message})}))}))}}}},cd9b:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("c14f")),o=i(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");var l=a("6186"),c=i(a("a888")),s=i(a("6612")),r=a("7de9"),d=(a("221f"),i(a("0f97"))),u=a("d51a"),f=a("ed08"),h=a("7f9d");t.default={components:{DynamicDataTable:d.default},directives:{elDragDialog:c.default},data:function(){return{btnLoading1:!1,btnLoading2:!1,Check_Style:0,isCheck:!1,isSee:!1,rectificationOrder:!1,chooseTitle:"",Code:"",loading:!1,tbConfig:{},columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:10},selectList:[],gridCode:"pro2_bitch_steel_list",form:{Check_Object_Type:"",Check_Node_Id:"",Check_Type:null,checkTypeId:"",Sheet_Result:"合格",SheetId:""},form2:{value1:0,value2:0,value3:0,value4:"",value5:"",value6:""},CheckTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],CheckNodeList:[],CheckObjectData:[],showDialog:!1,rules:{Check_Object_Type:[{required:!0,message:"请选择",trigger:"blur"}],Check_Node_Id:[{required:!0,message:"请选择",trigger:"blur"}],Check_Type:[{required:!0,message:"请选择",trigger:"blur"}]},title:"新建质检单",disable:!1,plm_Factory_Sheets:{}}},mounted:function(){this.getCheckType()},methods:{checkMethod:function(e){var t=e.row;return!t.stopFlag},handelInfo:function(e,t){var a=this;e.forEach((function(e){e.tempId=e.Id,e.Id=e.SheetDetailId||e.Id,a.tbData.push(e)})),this.getCount(),this.Check_Style=t,this.$nextTick((function(e){a.$refs.xTable.clearCheckboxRow()}))},handleClose:function(){this.showDialog=!1,this.tbData=[],this.form={Check_Object_Type:"",Check_Node_Id:"",Check_Type:null,checkTypeId:"",Sheet_Result:"",SheetId:""},this.disable=!1},init:function(e,t,a,i,n){this.detail=t,this.isCheck=a,this.isSee=i,this.rectificationOrder=n,this.Code=e,t?(this.disable=!0,"构件"===t.Check_Object_Type?(this.title="质检单(构件)",this.gridCode="pro2_bitch_steel_list"):"零件"===t.Check_Object_Type?(this.title="质检单(零件)",this.gridCode="pro2_bitch_part_list"):"部件"===t.Check_Object_Type&&(this.title="质检单(部件)",this.gridCode="pro2_bitch_part_list"),this.getCheckingEntity(t)):(this.title="质检单",this.getGridByCode()),this.rectificationOrder&&(this.title="整改单")},getCheckingEntity:function(e){var t=this;(0,u.GetSpotCheckingEntity)({sheetId:e.SheetId}).then((function(a){var i;a.IsSucceed?(t.plm_Factory_Sheets=a.Data[0],t.form2.value6=((null===(i=t.plm_Factory_Sheets)||void 0===i?void 0:i.Demand_Spot_Check_Rate)||0)+"%",t.plm_Factory_Sheets.Rectify_Date=t.plm_Factory_Sheets.Rectify_Date?(0,f.parseTime)(new Date(t.plm_Factory_Sheets.Rectify_Date),"{y}-{m}-{d}"):"",t.form.Check_Object_Type=t.plm_Factory_Sheets.Check_Object_Type_Id,t.form.Sheet_Result=t.plm_Factory_Sheets.Check_Result||"合格",t.changeObject(t.form.Check_Object_Type),t.form.Check_Node_Id=t.plm_Factory_Sheets.Check_Node_Id,t.form.SheetId=t.plm_Factory_Sheets.SheetId,"质量"===t.plm_Factory_Sheets.Check_Type?t.form.Check_Type=1:t.form.Check_Type=2,"构件"===e.Check_Object_Type?t.tbData=a.Data.map((function(e){return e.Installunit_Name=e.SetupPosition,e.tempId=e.Id,e.Id=e.SheetDetailId||e.Id,e})):("零件"===e.Check_Object_Type||"部件"===e.Check_Object_Type)&&(t.tbData=a.Data.map((function(e){return e.Project_Name=e.ProjectName,e.Area_Name=e.AreaPosition,e.Installunit_Name=e.SetupPosition,e.Code=e.SteelName,e.Spec=e.SteelSpec,e.Length=e.SteelLength,e.Num=e.SteelAmount,e.tempId=e.Id,e.Id=e.SheetDetailId||e.Id,e}))),t.tbData.map((function(e){(!e.Steel_Count&&0!==e.Steel_Count||!e.Unqualified_Count&&0!==e.Unqualified_Count||!e.Qualified_Count&&0!==e.Qualified_Count)&&(e.Steel_Count=0,e.Unqualified_Count=0,e.Qualified_Count=e.Inspction_Count)})),t.rectificationOrder&&(t.tbData=t.tbData.filter((function(e){return e.Unqualified_Count}))),t.getCount(),t.getStopList(t.tbData)):t.$message({type:"error",message:a.Message})}))},getStopList:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){var i,o;return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return i="tempId",o=e.map((function(e){return{Id:e[i],Type:"构件"===t.qualityItem.Display_Name?2:"零件"===t.qualityItem.Display_Name?1:3}})),a.n=1,(0,h.GetStopList)(o).then((function(a){if(a.IsSucceed){var n={};a.Data.forEach((function(e){n[e.Id]=!!e.Is_Stop})),e.forEach((function(e){n[e[i]]&&t.$set(e,"stopFlag",n[e[i]])}))}}));case 1:return a.a(2)}}),a)})))()},getNode:function(e){this.CheckObjectData.find((function(t){return t.Display_Name==e}))},getCheckType:function(){var e=this;(0,r.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:t.Message})})).catch((function(){}))},changeObject:function(e){var t;this.form.Check_Node_Id="",this.form.Check_Type=null,this.tbData=[];var a=null===(t=this.CheckObjectData.find((function(t){return t.Id===e})))||void 0===t?void 0:t.Display_Name;switch(this.chooseTitle=a,a){case"构件":this.check_object_id="0",this.gridCode="pro2_bitch_steel_list";break;case"零件":this.check_object_id="1",this.gridCode="pro2_bitch_part_list";break;case"物料":this.check_object_id="2";break;case"部件":this.check_object_id="3",this.gridCode="pro2_bitch_part_list";break;default:this.check_object_id="0"}this.getGridByCode(),this.getNodeList(e),this.qualityItem=this.CheckObjectData.find((function(t){return t.Id===e})),this.$emit("qualityItemChange",this.qualityItem)},qualityListClear:function(e){this.CheckNodeList=[],this.$refs.form.resetFields()},getNodeList:function(e){var t=this;(0,r.GetNodeList)({check_object_id:e,Check_Style:0}).then((function(e){e.IsSucceed?t.$nextTick((function(){t.CheckNodeList=e.Data})):t.$message({type:"error",message:"res.Message"})}))},changeCheckNode:function(e){this.form.Check_Type=null,this.CheckTypeList=[];var t=this.CheckNodeList.find((function(t){return t.Id===e})).Check_Type,a=this.CheckNodeList.find((function(t){return t.Id===e})).Demand_Spot_Check_Rate;this.form2.value6=(a||0)+"%",this.form.checkTypeId=t,-1===t?this.CheckTypeList=[{Name:"质量",Id:1},{Name:"探伤",Id:2}]:1===t?(this.CheckTypeList=[{Name:"质量",Id:1}],this.form.Check_Type=t):2===t&&(this.CheckTypeList=[{Name:"探伤",Id:2}],this.form.Check_Type=t)},checkNodeClear:function(){this.form.Check_Type=""},getGridByCode:function(){var e=this;this.loading=!0,(0,l.GetGridByCode)({Code:this.gridCode+","+this.Code}).then((function(t){t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList),e.loading=!1,e.showDialog=!0)}))},setGrid:function(e){this.tbConfig={};var t=Object.assign({},e,{});(this.isCheck||this.isSee||this.rectificationOrder)&&(t.Is_Select=!1,t.Is_Row_Number=!0),this.tbConfig=t,this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){this.columns=[],"3"===this.check_object_id&&e.map((function(e){return"Code"===e.Code&&(e.Display_Name="部件名称"),e})),this.columns=e},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount,this.TotalAmount=e.TotalAmount,this.TotalWeight=e.TotalWeight},gridPageChange:function(e){var t=e.page;this.pageInfo.Page=Number(t),this.fetchData()},gridSizeChange:function(e){var t=e.size;this.tbConfig.Row_Number=Number(t),this.pageInfo.PageSize=Number(t),this.pageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e.records},inputBlur1:function(e,t,a){var i=Number(t);"构件"===this.chooseTitle?i<1||i>Number(a.SteelAmount)?a.Inspction_Count=a.SteelAmount:a.Inspction_Count=i:("零件"===this.chooseTitle||"部件"===this.chooseTitle)&&(i<1||i>Number(a.Num)?a.Inspction_Count=a.Num:a.Inspction_Count=i),a.Inspction_Count<a.Steel_Count&&(a.Steel_Count=a.Inspction_Count),a.Qualified_Count=a.Steel_Count-a.Unqualified_Count,this.getCount()},inputBlur2:function(e,t,a){var i=Number(t);i>Number(a.Inspction_Count)?a.Steel_Count=a.Inspction_Count:a.Steel_Count=i,a.Steel_Count<a.Unqualified_Count&&(a.Unqualified_Count=a.Steel_Count),a.Qualified_Count=a.Steel_Count-a.Unqualified_Count,this.getCount()},inputBlur3:function(e,t,a){var i=Number(t);i<0||i>Number(a.Steel_Count)?a.Unqualified_Count=a.Steel_Count:a.Unqualified_Count=i,a.Qualified_Count=a.Steel_Count-a.Unqualified_Count,this.getCount()},getCount:function(){var e=this;this.form2.value1=0,this.form2.value2=0,this.form2.value3=0,this.form2.value4="",this.form2.value5="";var t=0,a=0,i=0,n=0;this.tbData.forEach((function(o){"合格"===e.form.Sheet_Result?(0===o.Steel_Count?n+=o.Inspction_Count:n+=o.Inspction_Count-o.Steel_Count+(o.Steel_Count-o.Unqualified_Count),e.form2.value2+=o.Unqualified_Count):(n+=o.Steel_Count-o.Unqualified_Count,e.form2.value2+=o.Inspction_Count-o.Steel_Count+o.Unqualified_Count),o.Qualified_Count=o.Steel_Count-o.Unqualified_Count,i+=o.Steel_Count-o.Unqualified_Count,a+=o.Steel_Count,e.form2.value1+=o.Inspction_Count,t+=o.Inspction_Count})),this.form2.value3=n,0===a?"合格"===this.form.Sheet_Result?this.form2.value4="100%":this.form2.value4="0%":this.form2.value4=(0,s.default)(i).divide(a).format("0.[00]%"),this.form2.value5=(0,s.default)(a).divide(t).format("0.[00]%")},addInfo:function(){var e=[];this.tbData.forEach((function(t){e.push(t.tempId)})),this.$emit("BitchopenDialog",this.check_object_id,this.chooseTitle,e)},handeldelete:function(){var e=this;this.selectList.forEach((function(t){var a=e.tbData.find((function(e){return e.tempId===t.Id})),i=e.tbData.indexOf(a);e.tbData.splice(i,1)}))},batchManageSaveCheck:function(e,t){var a=this;t?this.btnLoading2=!0:this.btnLoading1=!0,this.$refs[e].validate((function(e){if(!e)return t?a.btnLoading2=!1:a.btnLoading1=!1,!1;if(t&&!a.form.Sheet_Result)return a.$message({type:"warning",message:"提交请填写质检结果"}),void(t?a.btnLoading2=!1:a.btnLoading1=!1);var i=[];a.tbData.forEach((function(e){var t={};t.Check_Object_Type=a.check_object_id,t.Check_Node_Id=a.form.Check_Node_Id,t.Check_Type=a.form.Check_Type,t.SteelName=e.SteelName,t.Inspction_Count=e.Inspction_Count,t.Steel_Count=e.Steel_Count,t.Unqualified_Count=e.Unqualified_Count,t.Qualified_Count=e.Qualified_Count,t.Check_Object_Id=e.tempId||e.Id,t.Check_Object_Type_Id=a.form.Check_Object_Type,t.Check_Style=a.Check_Style,t.Check_Result=a.form.Sheet_Result,t.SheetId=a.form.SheetId||"",t.SheetDetailId=e.SheetDetailId||"",t.Area_Id=e.Area_Id,t.InstallUnit_Id=e.InstallUnit_Id,"零件"===a.chooseTitle&&(t.SteelName=e.Code),"部件"===a.chooseTitle&&(t.SteelName=e.Code),i.push(t)}));var n={IsSubmit:t,Is_Rectification:!1};if(i.length>0)if("合格"===a.form.Sheet_Result&&0!==a.form2.value2&&t){var o={CheckList:i,SubmitInfo:n};t?a.btnLoading2=!1:a.btnLoading1=!1,a.handleClose(),a.$emit("rectificationOrderInfo",o)}else(0,u.BatchManageSaveCheck)({CheckList:i,SubmitInfo:n}).then((function(e){e.IsSucceed?(a.$message({type:"success",message:t?"质检成功":"保存成功"}),a.tbData=[],a.handleClose(),a.$emit("refresh")):a.$message({type:"warning",message:e.Message}),t?a.btnLoading2=!1:a.btnLoading1=!1}));else t?a.btnLoading2=!1:a.btnLoading1=!1,a.$message({type:"warning",message:"保存数据不能为空"})}))}}}},d3ce:function(e,t,a){"use strict";a.r(t);var i=a("586ae"),n=a("7b92");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("92838");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"0ce5a126",null);t["default"]=c.exports},d9bf:function(e,t,a){"use strict";a("1997")},e70c:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",rules:e.rules}},[a("el-form-item",{attrs:{label:"质检对象",prop:"checkObjectId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:e.qualityListChange,clear:e.qualityListClear},model:{value:e.form.checkObjectId,callback:function(t){e.$set(e.form,"checkObjectId",t)},expression:"form.checkObjectId"}},e._l(e.qualityList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检节点",prop:"nodeId"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",disabled:!Boolean(e.form.checkObjectId)},on:{change:e.nodeChange,clear:e.nodeClear},model:{value:e.form.nodeId,callback:function(t){e.$set(e.form,"nodeId",t)},expression:"form.nodeId"}},e._l(e.nodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"名称",prop:"SteelName"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",disabled:!Boolean(e.form.checkObjectId)},on:{click:e.chooseComponent},slot:"append"})],1)],1),a("el-form-item",{attrs:{label:"质检类型",prop:"checkType"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",disabled:-1!=e.form.Check_Type},model:{value:e.form.checkType,callback:function(t){e.$set(e.form,"checkType",t)},expression:"form.checkType"}},[a("el-option",{attrs:{label:"质量",value:1}}),a("el-option",{attrs:{label:"探伤",value:2}})],1)],1),"新增"==e.type?a("el-form-item",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading1},on:{click:function(t){return e.submitForm(!1)}}},[e._v("保存")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading2},on:{click:function(t){return e.submitForm(!0)}}},[e._v("提交去质检")])],1):e._e()],1)],1)},n=[]},e7db:function(e,t,a){"use strict";a.r(t);var i=a("3254"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},e8b8:function(e,t,a){"use strict";a.r(t);var i=a("0d09"),n=a("8dd3");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("33c2");var l=a("2877"),c=Object(l["a"])(n["default"],i["a"],i["b"],!1,null,"f8bacea6",null);t["default"]=c.exports},f8fd:function(e,t,a){}}]);