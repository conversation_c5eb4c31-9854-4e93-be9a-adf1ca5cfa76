(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4f9eface"],{"009d":function(e,t,n){"use strict";n.r(t);var a=n("884a"),o=n("2d18");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("29fe");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"96e0f26c",null);t["default"]=s.exports},"04a5":function(e,t,n){"use strict";n("5405")},"12a3":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("2909"));n("4de4"),n("7db0"),n("14d9"),n("13d5"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("9485"),n("d3b7"),n("159b");var i=n("a024");t.default={props:{pageType:{type:String,default:void 0},partTypeOption:{type:Array,default:function(){return[]}}},data:function(){return{itemOption:[{key:"",value:""}],form:{},btnLoading:!1,OwnerOption:[],rules:{}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.list.forEach((function(t){var n=e.itemOption.find((function(e){return e.code===t.Type}));n&&(t.Part_Used_Process=n.value)})),e.btnLoading=!1,e.handleClose()}))},setOption:function(e,t){this.list=t;var n="领用工序",a={};if(this.itemOption=this.list.reduce((function(e,t){return a[t.Type]||"Direct"===t.Type||e.push({code:t.Type,label:t.Type_Name+n,value:""}),a[t.Type]=!0,e}),[]),this.fetchData(),e&&t.length){var o=t[0],i=this.itemOption.find((function(e){return e.code===o.Type}));i&&(i.value=o.Part_Used_Process)}},getComOption:function(){var e=this,t=[];this.list.forEach((function(n){n.Component_Technology_Path||(n.Component_Technology_Path="");var a=n.Component_Technology_Path.split("/");if(a.length){var i=a.reduce((function(t,n){var a=e.option.find((function(e){return e.Code===n}));return a?t.push(a):t.push.apply(t,(0,o.default)(e.option)),t}),[]);t.push(i)}else t.push.apply(t,(0,o.default)(e.option))})),this.OwnerOption=t.reduce((function(e,t){return e.filter((function(e){return-1!==t.indexOf(e)}))}))},fetchData:function(e,t){var n=this;(0,i.GetProcessListBase)({type:1}).then((function(e){e.IsSucceed?(n.option=e.Data,"part"===n.pageType&&n.getComOption()):n.$message({message:e.Message,type:"error"})}))},handleClose:function(){this.$emit("close")}}}},"1ef1":function(e,t,n){"use strict";n.r(t);var a=n("d6b6d"),o=n("858a");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("b9b70");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"23cf7f36",null);t["default"]=s.exports},"29fe":function(e,t,n){"use strict";n("5673")},"2d18":function(e,t,n){"use strict";n.r(t);var a=n("7412"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},"32e8":function(e,t,n){"use strict";n.r(t);var a=n("f7f2"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},5341:function(e,t,n){"use strict";n.r(t);var a=n("63c9"),o=n("32e8");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("6c49");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"9a3b0470",null);t["default"]=s.exports},5405:function(e,t,n){},5673:function(e,t,n){},5941:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"contentBox"},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"90px"}},[n("el-row",[e.isCom?[n("el-col",{attrs:{span:10}},[n("el-form-item",{attrs:{label:"构件编号",prop:"Comp_Codes"}},[n("el-input",{staticStyle:{width:"45%"},attrs:{clearable:"",placeholder:"请输入(空格区分/多个搜索)",type:"text"},model:{value:e.form.Comp_Code,callback:function(t){e.$set(e.form,"Comp_Code",t)},expression:"form.Comp_Code"}}),n("el-input",{staticStyle:{width:"45%","margin-left":"16px"},attrs:{clearable:"",placeholder:"模糊查找(请输入关键字)",type:"text"},model:{value:e.form.Comp_CodeBlur,callback:function(t){e.$set(e.form,"Comp_CodeBlur",t)},expression:"form.Comp_CodeBlur"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"构件类型",prop:"Type"}},[n("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",staticStyle:{width:"100%"},attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}})],1)],1),n("el-col",{attrs:{span:5}},[n("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1)]:[n("el-col",{attrs:{span:7}},[n("el-form-item",{attrs:{label:"所属构件",prop:"Comp_Code"}},[n("el-input",{staticStyle:{width:"45%"},attrs:{placeholder:"请输入(空格区分/多个搜索)",clearable:""},model:{value:e.form.Comp_Code,callback:function(t){e.$set(e.form,"Comp_Code",t)},expression:"form.Comp_Code"}}),n("el-input",{staticStyle:{width:"45%","margin-left":"16px"},attrs:{clearable:"",placeholder:"模糊查找(请输入关键字)",type:"text"},model:{value:e.form.Comp_CodeBlur,callback:function(t){e.$set(e.form,"Comp_CodeBlur",t)},expression:"form.Comp_CodeBlur"}})],1)],1),n("el-col",{attrs:{span:7}},[n("el-form-item",{attrs:{label:"零件名称",prop:"Part_Code"}},[n("el-input",{staticStyle:{width:"45%"},attrs:{placeholder:"请输入(空格区分/多个搜索)",clearable:""},model:{value:e.form.Part_Code,callback:function(t){e.$set(e.form,"Part_Code",t)},expression:"form.Part_Code"}}),n("el-input",{staticStyle:{width:"45%","margin-left":"16px"},attrs:{clearable:"",placeholder:"模糊查找(请输入关键字)",type:"text"},model:{value:e.form.Part_CodeBlur,callback:function(t){e.$set(e.form,"Part_CodeBlur",t)},expression:"form.Part_CodeBlur"}})],1)],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"零件种类",prop:"Type_Name"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}},e._l(e.typeOption,(function(e){return n("el-option",{key:e.Code,attrs:{label:e.Name,value:e.Name}})})),1)],1)],1)],n("el-col",{attrs:{span:2}},[n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.handleSearch()}}},[e._v("查询")])],1)],2)],1),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"","checkbox-config":{checkField:"checked"},loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},align:"left",stripe:"",data:e.fTable,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["customCountColumn"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","edit-render":{},"min-width":"120"},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"integer",min:1,max:a.maxCount},model:{value:a.count,callback:function(t){e.$set(a,"count",e._n(t))},expression:"row.count"}})]}},{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("displayValue")(n.count))+" ")]}}],null,!0)}):"Is_Component"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-tag",{attrs:{type:a.Is_Component?"danger":"success"}},[e._v(e._s(a.Is_Component?"否":"是"))])]}}],null,!0)}):n("vxe-column",{key:t.Code,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)],1),n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.totalSelection.length)+" 条数据 ")]),n("vxe-pager",{attrs:{border:"",background:"",loading:e.tbLoading,"current-page":e.pageInfo.page,"page-size":e.pageInfo.pageSize,"page-sizes":e.pageInfo.pageSizes,total:e.pageInfo.total,layouts:["PrevPage","JumpNumber","NextPage","FullJump","Sizes","Total"],size:"small"},on:{"update:currentPage":function(t){return e.$set(e.pageInfo,"page",t)},"update:current-page":function(t){return e.$set(e.pageInfo,"page",t)},"update:pageSize":function(t){return e.$set(e.pageInfo,"pageSize",t)},"update:page-size":function(t){return e.$set(e.pageInfo,"pageSize",t)},"page-change":e.handlePageChange}})],1),n("div",{staticClass:"button"},[n("el-button",{on:{click:e.handleClose}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",disabled:!e.totalSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存")])],1)],1)},o=[]},"63c9":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"车间",prop:"workShop"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.workShop,callback:function(t){e.$set(e.form,"workShop",t)},expression:"form.workShop"}},e._l(e.workShopOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},o=[]},"6c49":function(e,t,n){"use strict";n("d27f")},7412:function(e,t,n){"use strict";var a=n("dbce").default,o=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("4de4"),n("7db0"),n("c740"),n("a630"),n("caad"),n("d81d"),n("14d9"),n("13d5"),n("4e82"),n("a434"),n("e9f5"),n("d866"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("a732"),n("e9c4"),n("b64b"),n("d3b7"),n("ac1f"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("8a79"),n("2532"),n("3ca3"),n("5319"),n("841c"),n("1276"),n("2ca0"),n("c7cd"),n("159b"),n("ddb0");var i=o(n("2909")),r=o(n("c14f")),s=o(n("1da1")),l=o(n("5530")),c=n("ed08"),u=o(n("1ef1")),d=n("7f9d"),f=o(n("a1ce")),h=o(n("f58c")),p=o(n("5341")),m=n("6186"),g=n("c3c6"),_=n("e144"),v=o(n("6612")),b=n("a024"),C=n("66f9"),y=n("2f62"),w=n("2a7f"),k=a(n("c1df")),S="$_$";t.default={components:{BatchProcessAdjust:u.default,AddDraft:f.default,Workshop:p.default,OwnerProcess:h.default},data:function(){return{pickerOptions:{disabledDate:function(e){}},searchType:"",formInline:{Schduling_Code:"",Create_UserName:"",Finish_Date:"",Remark:""},total:0,columns:[],tbData:[],tbConfig:{},TotalCount:0,multipleSelection:[],pgLoading:!1,deleteLoading:!1,workShopIsOpen:!1,isOwnerNull:!1,dialogVisible:!1,openAddDraft:!1,saveLoading:!1,tbLoading:!1,isCheckAll:!1,currentComponent:"",dWidth:"25%",title:"",search:function(){return{}},pageType:void 0,tipLabel:"",technologyOption:[],typeOption:[],workingTeam:[],pageStatus:void 0,scheduleId:"",partComOwnerColumn:null}},watch:{"tbData.length":{handler:function(e,t){this.checkOwner()},immediate:!1}},computed:(0,l.default)((0,l.default)({isCom:function(){return"com"===this.pageType},isView:function(){return"view"===this.pageStatus},isEdit:function(){return"edit"===this.pageStatus},isAdd:function(){return"add"===this.pageStatus}},(0,y.mapGetters)("factoryInfo",["workshopEnabled"])),(0,y.mapGetters)("schedule",["processList"])),beforeRouteEnter:function(e,t,n){"view"===e.query.status?e.meta.title="查看":e.meta.title="草稿",n()},mounted:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.initProcessList(),e.tbDataMap={},e.pageType=e.$route.query.pg_type,e.pageStatus=e.$route.query.status,e.model=e.$route.query.model,e.scheduleId=e.$route.query.pid||"",e.formInline.Create_UserName=localStorage.getItem("UserAccount"),e.isCom||e.getType(),e.unique=(0,g.uniqueCode)(),e.checkWorkshopIsOpen(),e.getAreaInfo(),e.search=(0,c.debounce)(e.fetchData,800,!0),t.n=1,e.mergeConfig();case 1:(e.isView||e.isEdit)&&e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:(0,l.default)((0,l.default)({},(0,y.mapActions)("schedule",["changeProcessList","initProcessList"])),{},{checkOwner:function(){if(!this.isCom){this.isOwnerNull=this.tbData.every((function(e){return!e.Comp_Import_Detail_Id}));var e=this.columns.findIndex((function(e){return"Part_Used_Process"===e.Code}));if(this.isOwnerNull)-1!==e&&this.columns.splice(e,1);else if(-1===e){if(!this.ownerColumn)return void this.$message({message:"列表配置字段缺少零件领用工序字段",type:"success"});this.columns.push(this.ownerColumn)}}},mergeConfig:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getConfig();case 1:return t.n=2,e.getWorkTeam();case 2:return t.a(2)}}),t)})))()},getConfig:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n=e.isCom?e.isView?"PROComViewPageTbConfig":"PROComDraftPageTbConfig":e.isView?"PROPartViewPageTbConfig":"PROPartDraftPageTbConfig",t.n=1,e.getTableConfig(n);case 1:e.workshopEnabled||(e.columns=e.columns.filter((function(e){return"Workshop_Name"!==e.Code}))),e.checkOwner();case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.tbLoading=!0,n=null,!e.isCom){t.n=2;break}return t.n=1,e.getComPageList();case 1:n=t.v,t.n=4;break;case 2:return t.n=3,e.getPartPageList();case 3:n=t.v;case 4:e.initTbData(n),e.tbLoading=!1;case 5:return t.a(2)}}),t)})))()},closeView:function(){(0,c.closeTagView)(this.$store,this.$route)},checkWorkshopIsOpen:function(){this.workShopIsOpen=!0},tbSelectChange:function(e){this.multipleSelection=e.records},getAreaInfo:function(){var e=this;(0,C.AreaGetEntity)({id:this.$route.query.areaId}).then((function(t){if(t.IsSucceed){var n,a;if(!t.Data)return[];var o=k(null===(n=t.Data)||void 0===n?void 0:n.Demand_Begin_Date),i=k(null===(a=t.Data)||void 0===a?void 0:a.Demand_End_Date);e.pickerOptions.disabledDate=function(e){return e.getTime()<o||e.getTime()>i}}else e.$message({message:t.Message,type:"error"})}))},handleClose:function(){this.dialogVisible=!1,this.openAddDraft=!1},getComPageList:function(){var e=this;return new Promise((function(t,n){var a=e.$route.query.pid;(0,d.GetCompSchdulingInfoDetail)({Schduling_Plan_Id:a}).then((function(a){if(a.IsSucceed){var o=a.Data,i=o.Schduling_Plan,r=o.Schduling_Comps,s=o.Process_List;e.formInline=Object.assign(e.formInline,i),s.forEach((function(t){var n={key:t.Process_Code,value:t};e.changeProcessList(n)})),t(r||[])}else e.$message({message:a.Message,type:"error"}),n()}))}))},getPartPageList:function(){var e=this;return new Promise((function(t,n){var a=e.$route.query.pid;(0,d.GetPartSchdulingInfoDetail)({Schduling_Plan_Id:a}).then((function(a){if(a.IsSucceed){var o,i,r=null===(o=a.Data)||void 0===o?void 0:o.SarePartsModel.map((function(e){return e.Scheduled_Used_Process&&(e.Part_Used_Process=e.Scheduled_Used_Process),e}));e.formInline=Object.assign(e.formInline,null===(i=a.Data)||void 0===i?void 0:i.Schduling_Plan),t(r||[])}else e.$message({message:a.Message,type:"error"}),n()}))}))},initTbData:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Allocation_Teams";this.tbData=e.map((function(e){var a,o=(null===(a=e.Technology_Path)||void 0===a?void 0:a.split("/"))||[];e.uuid=(0,_.v4)(),t.addElementToTbData(e);var i=e[n].filter((function(e){return-1!==o.findIndex((function(t){return e.Process_Code===t}))}));return i.forEach((function(n,a){var o=t.getRowUnique(e.uuid,n.Process_Code,n.Working_Team_Id),i=t.getRowUniqueMax(e.uuid,n.Process_Code,n.Working_Team_Id);e[o]=n.Count,e[i]=0})),t.setInputMax(e),e}))},mergeSelectList:function(e){var t=this;e.forEach((function(e,n){var a=t.getMergeUniqueRow(e);if(!a)return e.puuid=e.uuid,e.Schduled_Count=e.chooseCount,e.Schduled_Weight=(0,v.default)(e.chooseCount*e.Weight).format("0.[00]"),t.tbData.push(e),void t.addElementToTbData(e);a.puuid=e.uuid,a.Schduled_Count+=e.chooseCount,a.Schduled_Weight=(0,v.default)(a.Schduled_Weight).add(e.chooseCount*e.Weight).format("0.[00]"),a.Technology_Path&&t.setInputMax(a)})),this.tbData.sort((function(e,t){return e.initRowIndex-t.initRowIndex}))},addElementToTbData:function(e){var t,n=this.isCom?e.Comp_Code:(null!==(t=e.Component_Code)&&void 0!==t?t:"")+e.Part_Code;this.tbDataMap[n]=e},getMergeUniqueRow:function(e){var t,n=this.isCom?e.Comp_Code:(null!==(t=e.Component_Code)&&void 0!==t?t:"")+e.Part_Code;return this.tbDataMap[n]},checkForm:function(){var e=!0;return this.$refs["formInline"].validate((function(t){t||(e=!1)})),e},saveDraft:function(){var e=arguments,t=this;return(0,s.default)((0,r.default)().m((function n(){var a,o,i,s,l,c,u;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:if(o=e.length>0&&void 0!==e[0]&&e[0],i=t.checkForm(),i){n.n=1;break}return n.a(2,!1);case 1:if(s=t.getSubmitTbInfo(),l=s.tableData,c=s.status,c){n.n=2;break}return n.a(2,!1);case 2:return o||(t.saveLoading=!0),n.n=3,t.handleSaveDraft(l,o);case 3:if(u=n.v,u){n.n=4;break}return n.a(2,!1);case 4:if(!o){n.n=5;break}return n.a(2,u);case 5:null===(a=t.$refs["draft"])||void 0===a||a.fetchData(),t.saveLoading=!1;case 6:return n.a(2)}}),n)})))()},saveWorkShop:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){var n,a,o,i,s,c,u;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(n=e.checkForm(),n){t.n=1;break}return t.a(2,!1);case 1:if(a={},e.tbData.length){t.n=2;break}return e.$message({message:"数据不能为空",type:"success"}),t.a(2);case 2:e.isCom?a.Schduling_Comps=e.tbData:a.SarePartsModel=e.tbData,e.isEdit?a.Schduling_Plan=e.formInline:(o=e.$route.query,i=o.install,s=o.projectId,c=o.areaId,a.Schduling_Plan=(0,l.default)((0,l.default)({},e.formInline),{},{Project_Id:s,InstallUnit_Id:i,Area_Id:c,Schduling_Model:e.model})),e.pgLoading=!0,u=e.isCom?d.SaveComponentSchedulingWorkshop:d.SavePartSchedulingWorkshop,u(a).then((function(t){t.IsSucceed?(e.pgLoading=!1,e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"})}));case 3:return t.a(2)}}),t)})))()},getSubmitTbInfo:function(){for(var e,t=this,n=JSON.parse(JSON.stringify(this.tbData)),a=function(){var e=n[o],a=[];if(!e.Technology_Path)return t.$message({message:"工序不能为空",type:"warning"}),{v:{status:!1}};if(e.Scheduled_Technology_Path&&e.Scheduled_Technology_Path!==e.Technology_Path)return t.$message({message:"请和该区域批次下已排产同".concat(t.isCom?"构件":"零件","保持工序一致"),type:"warning"}),{v:{status:!1}};if(e.Scheduled_Used_Process&&e.Scheduled_Used_Process!==e.Part_Used_Process)return t.$message({message:"请和该区域批次下已排产同零件领用工序保持一致",type:"warning"}),{v:{status:!1}};var r=Array.from(new Set(e.Technology_Path.split("/")));r.forEach((function(n){var o=t.workingTeam.filter((function(e){return e.Process_Code===n})),r=o.map((function(a){var o=t.getRowUnique(e.uuid,n,a.Working_Team_Id),i=t.getRowUniqueMax(e.uuid,n,a.Working_Team_Id),r={Team_Task_Id:e.Team_Task_Id,Comp_Code:e.Comp_Code,Again_Count:+e[o]||0,Part_Code:t.isCom?null:"",Process_Code:n,Technology_Path:e.Technology_Path,Working_Team_Id:a.Working_Team_Id,Working_Team_Name:a.Working_Team_Name};return delete e[o],delete e[i],r}));a.push.apply(a,(0,i.default)(r))}));var s=Object.keys(e).filter((function(t){return t.startsWith(e["uuid"])}));s.forEach((function(t){delete e[t]})),delete e["uuid"],delete e["_X_ROW_KEY"],delete e["puuid"],e.Allocation_Teams=a},o=0;o<n.length;o++)if(e=a(),e)return e.v;return{tableData:n,status:!0}},handleSaveDraft:function(e,t){var n=this;return(0,s.default)((0,r.default)().m((function a(){var o,i,s,c,u,f,h,p,m;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:if(o=n.isCom?d.SaveCompSchdulingDraft:d.SavePartSchdulingDraft,i={},n.isCom){for(c in i.Schduling_Comps=e,s=[],n.processList)n.processList.hasOwnProperty(c)&&s.push(n.processList[c]);i.Process_List=s}else i.SarePartsModel=e;return n.isEdit?i.Schduling_Plan=n.formInline:(u=n.$route.query,f=u.install,h=u.projectId,p=u.areaId,i.Schduling_Plan=(0,l.default)((0,l.default)({},n.formInline),{},{Project_Id:h,InstallUnit_Id:f,Area_Id:p,Schduling_Model:n.model})),m=!1,a.n=1,o(i).then((function(e){e.IsSucceed?t?(n.templateScheduleCode=e.Data,m=!0):(n.pgLoading=!1,n.$message({message:"保存成功",type:"success"}),n.closeView()):(n.saveLoading=!1,n.pgLoading=!1,n.$message({message:e.Message,type:"error"}))}));case 1:return a.a(2,m)}}),a)})))()},handleDelete:function(){var e=this;this.deleteLoading=!0,setTimeout((function(){var t=new Set(e.multipleSelection.map((function(e){return e.uuid})));e.tbData=e.tbData.filter((function(n){var a=t.has(n.uuid);if(a){var o,i=e.isCom?n.Comp_Code:(null!==(o=n.Component_Code)&&void 0!==o?o:"")+n.Part_Code;delete e.tbDataMap[i]}return!a})),e.$nextTick((function(t){var n;null===(n=e.$refs["draft"])||void 0===n||n.mergeData(e.multipleSelection),e.multipleSelection=[]})),e.deleteLoading=!1}),0)},getWorkTeam:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,d.GetSchdulingWorkingTeams)({type:e.isCom?1:2}).then((function(t){t.IsSucceed?e.workingTeam=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},handleSubmit:function(){var e=this;this.$refs["formInline"].validate((function(t){if(t){var n=e.getSubmitTbInfo(),a=n.tableData,o=n.status;o&&e.$confirm("是否提交当前数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveDraftDoSubmit(a)})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}))},saveDraftDoSubmit:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){var n,a,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.pgLoading=!0,null===(n=e.formInline)||void 0===n||!n.Schduling_Code){t.n=2;break}return t.n=1,e.saveDraft(!0);case 1:a=t.v,a&&e.doSubmit(e.formInline.Id),t.n=4;break;case 2:return t.n=3,e.saveDraft(!0);case 3:o=t.v,o&&e.doSubmit(e.templateScheduleCode);case 4:return t.a(2)}}),t)})))()},doSubmit:function(e){var t=this;(0,d.SaveSchdulingTaskById)({schdulingPlanId:e}).then((function(e){e.IsSucceed?(t.$message({message:"下达成功",type:"success"}),t.closeView()):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.pgLoading=!1})).catch((function(e){t.pgLoading=!1}))},getWorkShop:function(e){var t,n=this,a=e.origin,o=e.row,i=e.workShop,r=i.Id,s=i.Display_Name;2===a?null!==(t=e.workShop)&&void 0!==t&&t.Id?(o.Workshop_Name=s,o.Workshop_Id=r,this.setPath(o,r)):(o.Workshop_Name="",o.Workshop_Id=""):this.multipleSelection.forEach((function(t){var a;null!==(a=e.workShop)&&void 0!==a&&a.Id?(t.Workshop_Name=s,t.Workshop_Id=r,n.setPath(t,r)):(t.Workshop_Name="",t.Workshop_Id="")}))},setPath:function(e,t){null!==e&&void 0!==e&&e.Scheduled_Workshop_Id?e.Scheduled_Workshop_Id!==t&&(e.Technology_Path=""):e.Technology_Path=""},handleBatchWorkshop:function(e,t){var n=this;this.title=1===e?"批量分配车间":"分配车间",this.currentComponent="Workshop",this.dWidth="30%",this.dialogVisible=!0,this.$nextTick((function(a){n.$refs["content"].fetchData(e,t)}))},handleAutoDeal:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.$confirm("是否将选中数据按构件类型自动分配","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(e.workshopEnabled){var t=e.multipleSelection.map((function(e){return{uniqueType:"".concat(e.Type,"$_$").concat(e.Workshop_Id)}})),n=Array.from(new Set(t.map((function(e){return e.uniqueType})))),a={};Promise.all(n.map((function(t){var n=t.split("$_$");return e.setLibType(n[0],n[1])}))).then((function(t){var o=t.some((function(e){return void 0==e}));o&&e.$message({message:"所选车间内工序班组与构件类型工序不匹配，请手动分配工序",type:"warning"}),t.forEach((function(e,t){a[n[t]]=e})),e.multipleSelection.forEach((function(t){t.Technology_Path=a["".concat(t.Type,"$_$").concat(t.Workshop_Id)],e.resetWorkTeamMax(t,t.Technology_Path)}))}))}else{var o=e.multipleSelection.map((function(e){return e.Type})),i=Array.from(new Set(o)),r={};Promise.all(i.map((function(t){return e.setLibType(t)}))).then((function(t){t.forEach((function(e,t){r[i[t]]=e})),e.multipleSelection.forEach((function(t){t.Technology_Path=r[t.Type],e.resetWorkTeamMax(t,t.Technology_Path)}))}))}})).catch((function(){e.$message({type:"info",message:"已取消"})}));case 1:return t.a(2)}}),t)})))()},getProcessOption:function(e){var t=this;return new Promise((function(n,a){(0,b.GetProcessListBase)({workshopId:e,type:t.isCom?1:2}).then((function(e){if(e.IsSucceed){var a=e.Data.map((function(e){return e.Code}));n(a)}else t.$message({message:e.Message,type:"error"})}))}))},setLibType:function(e,t){var n=this;return new Promise((function(a){var o={Component_type:e,type:1};n.workshopEnabled&&(o.workshopId=t),(0,b.GetLibListType)(o).then((function(e){if(e.IsSucceed)if(e.Data.Data&&e.Data.Data.length){var t=e.Data.Data[0],o=t.WorkCode&&t.WorkCode.replace(/\\/g,"/");a(o)}else a(void 0);else n.$message({message:e.Message,type:"error"})}))}))},inputChange:function(e){this.setInputMax(e)},setInputMax:function(e){var t=Object.keys(e).filter((function(t){return!t.endsWith("max")&&t.startsWith(e.uuid)&&t.length>e.uuid.length}));t.forEach((function(n){var a=n.split(S)[1],o=t.filter((function(e){var t=e.split(S)[1];return e!==n&&t===a})).reduce((function(t,n){return t+(0,v.default)(e[n]).value()}),0);e[n+S+"max"]=e.Schduled_Count-o}))},sendProcess:function(e){for(var t=e.arr,n=e.str,a=!0,o=0;o<t.length;o++){var i=t[o];if(i.originalPath&&i.originalPath!==n){a=!1;break}i.Technology_Path=n}a||this.$message({message:"请和该区域批次下已排产同构件保持工序一致",type:"warning"})},resetWorkTeamMax:function(e,t){var n,a=this;t?e.Technology_Path=t:t=e.Technology_Path;var o=(null===(n=t)||void 0===n?void 0:n.split("/"))||[];this.workingTeam.forEach((function(t,n){var i=o.some((function(e){return e===t.Process_Code})),r=a.getRowUnique(e.uuid,t.Process_Code,t.Working_Team_Id),s=a.getRowUniqueMax(e.uuid,t.Process_Code,t.Working_Team_Id);i?e[r]||(a.$set(e,r,0),a.$set(e,s,e.Schduled_Count)):(a.$delete(e,r),a.$delete(e,s))}))},checkPermissionTeam:function(e,t){if(!e)return!1;var n=(null===e||void 0===e?void 0:e.split("/"))||[];return!!n.some((function(e){return e===t}))},getTableConfig:function(e){var t=this;return(0,s.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,m.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,a=e.Data,o=e.Message;if(n){t.tbConfig=Object.assign({},t.tbConfig,a.Grid);var i=a.ColumnList||[];t.ownerColumn=i.find((function(e){return"Part_Used_Process"===e.Code})),t.ownerColumn2=i.find((function(e){return"Is_Main_Part"===e.Code})),t.columns=t.setColumnDisplay(i)}else t.$message({message:o,type:"error"})}));case 1:return n.a(2)}}),n)})))()},setColumnDisplay:function(e){return e.filter((function(e){return e.Is_Display})).map((function(e){return g.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),e}))},activeCellMethod:function(e){var t,n=e.row,a=e.column;e.columnIndex;if(this.isView)return!1;var o=null===(t=a.field)||void 0===t?void 0:t.split("$_$")[1];return this.checkPermissionTeam(n.Technology_Path,o)},openBPADialog:function(e,t){var n=this;if(this.workshopEnabled&&1===e){var a=this.checkIsUniqueWorkshop();if(!a)return}this.title=2===e?"工序调整":"批量工序调整",this.currentComponent="BatchProcessAdjust",this.dWidth=this.isCom?"60%":"30%",this.dialogVisible=!0,this.$nextTick((function(a){n.$refs["content"].setData(2===e?[t]:n.multipleSelection,2===e?t.Technology_Path:"")}))},checkIsUniqueWorkshop:function(){for(var e=!0,t=this.multipleSelection[0].Workshop_Name,n=1;n<this.multipleSelection.length;n++){var a=this.multipleSelection[n];if(a.Workshop_Name!==t){e=!1;break}}return e||this.$message({message:"批量分配工序时只有相同车间下的才可一起批量分配",type:"warning"}),e},checkHasWorkShop:function(e,t){for(var n=!0,a=0;a<t.length;a++){var o=t[a];if(!o.Workshop_Name){n=!1;break}}return n||this.$message({message:"请先选择车间后再进行工序分配",type:"warning"}),n},handleAddDialog:function(){var e=this;this.isCom?this.title="构件排产":this.title="添加零件",this.currentComponent="AddDraft",this.dWidth="80%",this.openAddDraft=!0,this.$nextTick((function(t){e.$refs["draft"].setPageData()}))},getRowUnique:function(e,t,n){return"".concat(e).concat(S).concat(t).concat(S).concat(n)},getRowUniqueMax:function(e,t,n){return this.getRowUnique(e,t,n)+"".concat(S,"max")},handleSelectMenu:function(e){"process"===e?this.openBPADialog(1):"deal"===e&&this.handleAutoDeal(1)},handleBatchOwner:function(e,t){var n=this;this.title="批量分配领用工序",this.currentComponent="OwnerProcess",this.dWidth="30%",this.dialogVisible=!0,this.$nextTick((function(a){n.$refs["content"].setOption(2===e,2===e?[t]:n.multipleSelection)}))},handleReverse:function(){var e=[];this.tbData.forEach((function(t,n){t.checked=!t.checked,t.checked&&e.push(t)})),this.multipleSelection=e,this.multipleSelection.length===this.tbData.length&&this.$refs["xTable"].setAllCheckboxRow(!0),0===this.multipleSelection.length&&this.$refs["xTable"].setAllCheckboxRow(!1)},tbFilterChange:function(){var e,t=this,n=this.$refs.xTable,a=n.getColumnByField("Type_Name");null!==a&&void 0!==a&&null!==(e=a.filters)&&void 0!==e&&e.length&&(a.filters.forEach((function(e){e.checked=e.value===t.searchType})),n.updateData())},getType:function(){var e=this;(0,w.GetPartTypeList)({}).then((function(t){t.IsSucceed?e.typeOption=t.Data.map((function(e){return{label:e.Name,value:e.Name,code:e.Code}})):e.$message({message:t.Message,type:"error"})}))},setProcessList:function(e){this.changeProcessList(e)}})}},"858a":function(e,t,n){"use strict";n.r(t);var a=n("e3d8"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},"884a":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"box-card h100",attrs:{"element-loading-text":"正在处理..."}},[n("h4",{staticClass:"topTitle"},[n("span"),e._v("基本信息")]),n("el-form",{ref:"formInline",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline}},[e.isAdd?e._e():n("el-form-item",{attrs:{label:"排产单号",prop:"Schduling_Code"}},[e.isView?n("span",[e._v(e._s(0===e.formInline.Status?"":e.formInline.Schduling_Code))]):n("el-input",{attrs:{disabled:""},model:{value:e.formInline.Schduling_Code,callback:function(t){e.$set(e.formInline,"Schduling_Code",t)},expression:"formInline.Schduling_Code"}})],1),n("el-form-item",{attrs:{label:"计划员",prop:"Create_UserName"}},[e.isView?n("span",[e._v(e._s(e.formInline.Create_UserName))]):n("el-input",{attrs:{disabled:""},model:{value:e.formInline.Create_UserName,callback:function(t){e.$set(e.formInline,"Create_UserName",t)},expression:"formInline.Create_UserName"}})],1),n("el-form-item",{attrs:{label:"要求完成时间",prop:"Finish_Date",rules:{required:!0,message:"请选择",trigger:"change"}}},[e.isView?n("span",[e._v(e._s(e._f("timeFormat")(e.formInline.Finish_Date)))]):n("el-date-picker",{attrs:{"picker-options":e.pickerOptions,disabled:e.isView,"value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:e.formInline.Finish_Date,callback:function(t){e.$set(e.formInline,"Finish_Date",t)},expression:"formInline.Finish_Date"}})],1),n("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[e.isView?n("span",[e._v(e._s(e.formInline.Remark))]):n("el-input",{staticStyle:{width:"320px"},attrs:{disabled:e.isView,placeholder:"请输入"},model:{value:e.formInline.Remark,callback:function(t){e.$set(e.formInline,"Remark",t)},expression:"formInline.Remark"}})],1)],1),n("el-divider",{staticClass:"elDivder"}),n("div",{staticClass:"btn-x"},[e.isView?e._e():[n("div",[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleAddDialog()}}},[e._v("添加")]),e.workshopEnabled?n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleBatchWorkshop(1)}}},[e._v("批量分配车间 ")]):e._e(),e.isCom?e._e():n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleSelectMenu("process")}}},[e._v("批量分配工序 ")]),e.isCom?n("el-dropdown",{staticStyle:{margin:"0 10px"},on:{command:e.handleSelectMenu}},[n("el-button",{staticStyle:{width:"140px"},attrs:{disabled:!e.multipleSelection.length,type:"primary",plain:""}},[e._v(" 分配工序"),n("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{attrs:{command:"process"}},[e._v("批量分配工序 ")]),e.isCom?n("el-dropdown-item",{attrs:{command:"deal"}},[e._v("构件类型自动分配 ")]):e._e()],1)],1):e._e(),e.isCom||e.isOwnerNull?e._e():n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleBatchOwner(1)}}},[e._v("批量分配领用工序 ")]),n("el-button",{attrs:{plain:"",disabled:!e.tbData.length,loading:!1},on:{click:e.handleReverse}},[e._v("反选 ")]),n("el-button",{attrs:{type:"danger",plain:"",loading:e.deleteLoading,disabled:!e.multipleSelection.length},on:{click:e.handleDelete}},[e._v("删除 ")])],1),e.isCom?e._e():n("div",[n("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.tbFilterChange},model:{value:e.searchType,callback:function(t){e.searchType=t},expression:"searchType"}},e._l(e.typeOption,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)]],2),n("div",{staticClass:"tb-x"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","checkbox-config":{checkField:"checked"},"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","filter-config":{showIcon:!1},"show-overflow":"",loading:e.tbLoading,stripe:"","scroll-y":{enabled:!0,gt:20},size:"medium","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView,activeMethod:e.activeCellMethod},data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Is_Component"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-tag",{attrs:{type:a.Is_Component?"danger":"success"}},[e._v(e._s(a.Is_Component?"否":"是")+" ")])]}}],null,!0)}):"Type_Name"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,filters:e.typeOption,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("displayValue")(n.Type_Name))+" ")]}}],null,!0)}):"Technology_Path"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("displayValue")(a.Technology_Path))+" "),e.isView?e._e():n("i",{staticClass:"el-icon-edit",on:{click:function(t){return e.openBPADialog(2,a)}}})]}}],null,!0)}):"Part_Used_Process"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("displayValue")(a.Part_Used_Process))+" "),e.isView?e._e():n("i",{staticClass:"el-icon-edit",on:{click:function(t){return e.handleBatchOwner(2,a)}}})]}}],null,!0)}):"Workshop_Name"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("displayValue")(a.Workshop_Name))+" "),e.isView?e._e():n("i",{staticClass:"el-icon-edit",on:{click:function(t){return e.handleBatchWorkshop(2,a)}}})]}}],null,!0)}):n("vxe-column",{key:t.Id,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)],1),e.isView?e._e():n("el-divider",{staticClass:"elDivder"}),e.isView?e._e():n("footer",[n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.multipleSelection.length)+" 条数据 ")]),e.tipLabel?n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v(e._s(e.tipLabel)+" ")]):e._e()],1),n("div",[e.workshopEnabled?n("el-button",{attrs:{type:"primary"},on:{click:e.saveWorkShop}},[e._v("保存车间分配")]):e._e(),n("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:function(t){return e.saveDraft(!1)}}},[e._v("保存草稿 ")]),n("el-button",{attrs:{disabled:e.deleteLoading},on:{click:e.handleSubmit}},[e._v("下发任务")])],1)])],1),e.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"process-list":e.processList,"page-type":e.pageType,"part-type-option":e.typeOption},on:{close:e.handleClose,sendProcess:e.sendProcess,workShop:e.getWorkShop,refresh:e.fetchData,setProcessList:e.setProcessList}})],1):e._e(),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.openAddDraft,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.openAddDraft=t},close:e.handleClose}},[n("keep-alive",[n("add-draft",{ref:"draft",attrs:{"schedule-id":e.scheduleId,"show-dialog":e.openAddDraft,"page-type":e.pageType},on:{sendSelectList:e.mergeSelectList,close:e.handleClose}})],1)],1)],1)},o=[]},"91b3":function(e,t,n){"use strict";n("a6f5d")},a1ce:function(e,t,n){"use strict";n.r(t);var a=n("5941"),o=n("d007");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("91b3");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"99fce074",null);t["default"]=s.exports},a6f5d:function(e,t,n){},a91a:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},e._l(e.itemOption,(function(t){return n("el-form-item",{key:t.code,attrs:{label:t.label,prop:"ownerProcess"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择"},model:{value:t.value,callback:function(n){e.$set(t,"value",n)},expression:"element.value"}},e._l(e.OwnerOption,(function(e){return n("el-option",{key:e.Code,attrs:{label:e.Name,value:e.Code}})})),1)],1)})),1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},o=[]},b9b70:function(e,t,n){"use strict";n("d692")},d007:function(e,t,n){"use strict";n.r(t);var a=n("f9b0"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},d27f:function(e,t,n){},d692:function(e,t,n){},d6b6d:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-container"},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[n("draggable",{attrs:{handle:".icon-drag"},on:{change:e.changeDraggable},model:{value:e.list,callback:function(t){e.list=t},expression:"list"}},[n("transition-group",e._l(e.list,(function(t,a){return n("el-row",{key:a},[n("el-col",{attrs:{span:1}},[n("i",{staticClass:"iconfont icon-drag cs-drag"})]),n("el-col",{attrs:{span:e.isCom?10:20}},[n("el-form-item",{attrs:{label:"排产工序"+(a+1)}},[n("el-select",{attrs:{disabled:t.isPart,placeholder:"请选择",clearable:""},on:{change:function(n){return e.selectChange(n,t)}},model:{value:t.value,callback:function(n){e.$set(t,"value",n)},expression:"element.value"}},e._l(e.options,(function(e){return n("el-option",{key:e.Code,attrs:{label:e.Name,disabled:e.disabled,value:e.Code}})})),1)],1)],1),e.isCom?n("el-col",{attrs:{span:10}},[n("el-form-item",{attrs:{label:"要求完成时间"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},on:{change:function(n){return e.dateChange(n,t)}},model:{value:t.date,callback:function(n){e.$set(t,"date",n)},expression:"element.date"}})],1)],1):e._e(),n("el-col",{attrs:{span:3}},[n("span",{staticClass:"btn-x"},[0===a&&e.list.length<e.options.length?n("el-button",{attrs:{type:"primary",icon:"el-icon-plus",circle:""},on:{click:e.handleAdd}}):e._e(),0===a||t.isPart?e._e():n("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(n){return e.handleDelete(t)}}})],1)])],1)})),1)],1)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),e.list.length?n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")]):e._e()],1)],1)},o=[]},e3d8:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("7db0"),n("c740"),n("0481"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("a434"),n("4069"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("d3b7"),n("2532"),n("159b");var o=a(n("c14f")),i=a(n("1da1")),r=a(n("5530")),s=n("a024"),l=a(n("b76a")),c=n("ed08"),u=n("2f62");t.default={components:{Draggable:l.default},props:{pageType:{type:String,default:void 0},processList:{type:Object,default:function(){return{}}}},data:function(){return{list:[],options:[],btnLoading:!1,pgLoading:!1,form:{}}},computed:{isCom:function(){return"com"===this.pageType}},methods:(0,r.default)((0,r.default)({},(0,u.mapActions)("schedule",["initProcessList"])),{},{getProcessOption:function(e){var t=this;return new Promise((function(n,a){t.pgLoading=!0,(0,s.GetProcessListBase)({workshopId:e,type:t.isCom?1:2}).then((function(e){e.IsSucceed?t.options=e.Data.map((function(e){return t.$set(e,"disabled",!1),e})):t.$message({message:e.Message,type:"error"}),n()})).finally((function(e){t.pgLoading=!1}))}))},selectChange:function(e,t){var n,a=this.list.map((function(e){return e.value}));(this.options.forEach((function(e,t){e.disabled=a.includes(e.Code)})),this.isCom)&&(t&&(t.date=null===(n=this.processList[e])||void 0===n?void 0:n.Finish_Date))},dateChange:function(e,t){var n,a=this.options.find((function(e){return e.Code===t.value})),o={};a&&(o={Schduling_Id:null===(n=this.formInline)||void 0===n?void 0:n.Schduling_Code,Process_Id:a.Id,Process_Code:a.Code,Finish_Date:e});this.$emit("setProcessList",{key:t.value,value:o})},handleAdd:function(e){var t=this.list.map((function(e){return e.value}));this.options.forEach((function(e){t.includes(e.Code)&&(e.disabled=!0)})),this.list.push({value:"",date:""})},handleDelete:function(e){var t=this.list.findIndex((function(t){return t.value===e.value}));-1!==t&&(this.list.splice(t,1),this.selectChange())},setData:function(e,t){var n=this;return(0,i.default)((0,o.default)().m((function a(){var i,r,s,l,c;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return i=e[0].Workshop_Id,a.n=1,n.getProcessOption(i);case 1:if(n.arr=e||null,n.list=[],r=[],!n.isCom){a.n=3;break}if(s=e.map((function(e){return((null===e||void 0===e?void 0:e.Part_Used_Process)||"").split(",")})),r=n.getUnique(s.flat()).filter((function(e){return!!e})),!r.length){a.n=3;break}if(l=r.filter((function(e){return!!n.options.find((function(t){return t.Code===e}))})),!(l.length<r.length)){a.n=2;break}return n.$message({message:"当前构件生产所属车间内没有该构件所属零件领用工序，请至车间管理内关联相关工序班组",type:"warning"}),a.a(2);case 2:r.forEach((function(e,t){var a={value:e,isPart:!0};n.list.push(a)}));case 3:t&&(c=t.split("/").map((function(e){var t;return{value:e,date:null===(t=n.processList[e])||void 0===t?void 0:t.Finish_Date}})),c.forEach((function(e,t){r.includes(e.value)||n.list.push(e)}))),n.list.length||n.list.push({value:"",date:""}),n.selectChange();case 4:return a.a(2)}}),a)})))()},getUnique:function(e){return(0,c.uniqueArr)(e)},submit:function(){var e=this.list.map((function(e){return e.value})).filter((function(e){return!!e})),t=this.checkCode(e);if(t)if(e.length){this.btnLoading=!0;var n=e.join("/");this.$emit("sendProcess",{arr:this.arr,str:n}),this.btnLoading=!1,this.handleClose()}else this.$message({message:"工序不能全为空",type:"warning"});else this.$message({message:"相邻工序不能相同",type:"warning"})},handleClose:function(){this.$emit("close")},checkCode:function(e){for(var t=!0,n=0;n<e.length;n++)if(n!==e.length-1&&e[n]===e[n+1]){t=!1;break}return t},changeDraggable:function(){this.list.forEach((function(e){e.date=""})),this.initProcessList()}})}},f58c:function(e,t,n){"use strict";n.r(t);var a=n("a91a"),o=n("f8b1");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("04a5");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"85c6e794",null);t["default"]=s.exports},f7f2:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("7db0"),n("d81d"),n("e9f5"),n("f665"),n("ab43"),n("d3b7");var a=n("7196");t.default={data:function(){return{form:{workShop:""},btnLoading:!1,workShopOption:[],rules:{}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;if(e.form.workShop){var n=e.workShopOption.find((function(t){return t.Id===e.form.workShop}));e.$emit("workShop",{workShop:n,origin:e.origin,row:e.row}),e.btnLoading=!1,e.handleClose()}else e.$emit("workShop",{workShop:{},origin:e.origin,row:e.row}),e.btnLoading=!1,e.handleClose()}))},fetchData:function(e,t){var n=this;this.origin=e,this.row=t,(0,a.GetWorkshopPageList)({Page:1,PageSize:-1}).then((function(e){var t;e.IsSucceed?(null!==e&&void 0!==e&&null!==(t=e.Data)&&void 0!==t&&t.Data||(n.workShopOption=[]),n.workShopOption=e.Data.Data.map((function(e){return{Id:e.Id,Display_Name:e.Display_Name}}))):n.$message({message:e.Message,type:"error"})})),2===e&&(this.form.workShop=t.Workshop_Id)},handleClose:function(){this.$emit("close")}}}},f8b1:function(e,t,n){"use strict";n.r(t);var a=n("12a3"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},f9b0:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("5530")),i=a(n("15fd")),r=a(n("c14f")),s=a(n("1da1"));n("4de4"),n("c740"),n("caad"),n("d81d"),n("14d9"),n("fb6a"),n("4e82"),n("a434"),n("e9f5"),n("d866"),n("910d"),n("7d54"),n("ab43"),n("a732"),n("a9e3"),n("d3b7"),n("ac1f"),n("25f0"),n("2532"),n("841c"),n("498a"),n("c7cd"),n("159b");var l=n("6186"),c=n("7f9d"),u=n("210d"),d=n("e144"),f=n("c3c6"),h=n("ed08"),p=n("c685"),m=n("fd31"),g=n("2a7f"),_=["Comp_Codes"];t.default={props:{scheduleId:{type:String,default:""},pageType:{type:String,default:"com"},showDialog:{type:Boolean,default:!1}},data:function(){return{pageInfo:{page:1,pageSize:500,pageSizes:p.tablePageSize,total:0},form:{Comp_Code:"",Comp_CodeBlur:"",Part_CodeBlur:"",Part_Code:"",Type_Name:"",Spec:"",Type:""},isOwnerNull:!0,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},TotalCount:0,Page:0,multipleSelection:[],totalSelection:[],search:function(){return{}},treeSelectParams:{placeholder:"请选择",clearable:!0},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},typeOption:[]}},computed:{isCom:function(){return"com"===this.pageType}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.isCom?this.getObjectTypeList():this.getType(),this.search=(0,h.debounce)(this.fetchData,800,!0)},methods:{getConfig:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n="",n=e.isCom?"PROComDraftEditTbConfig":"PROPartDraftEditTbConfig",t.n=1,e.getTableConfig(n);case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},filterData:function(e){var t=this,n=[];for(var a in this.form)(this.form[a]||!1===this.form[a])&&n.push(a);if(!n.length)return this.setPage(),!e&&(this.pageInfo.page=1),void(this.pageInfo.total=this.tbData.length);var o=this.tbData.filter((function(e){return e.checked=!1,!(t.form.Comp_Code.trim()&&!t.form["Comp_Code"].split(" ").includes(e["Comp_Code"]))&&(!(t.form.Comp_CodeBlur.trim()&&!e.Comp_Code.includes(t.form.Comp_CodeBlur))&&((!t.form.Type||e.Type===t.form.Type)&&(!(t.form.Part_CodeBlur.trim()&&!e.Part_Code.includes(t.form.Part_CodeBlur))&&(!(t.form.Part_Code.trim()&&!t.form["Part_Code"].split(" ").includes(e["Part_Code"]))&&((""===t.form.Type_Name||e.Type_Name===t.form.Type_Name)&&!(""!==t.form.Spec.trim()&&!e.Spec.includes(t.form.Spec)))))))}));!e&&(this.pageInfo.page=1),this.pageInfo.total=o.length,this.setPage(o)},handleSearch:function(){var e;this.totalSelection=[],this.clearSelect(),null!==(e=this.tbData)&&void 0!==e&&e.length&&(this.tbData.forEach((function(e){return e.checked=!1})),this.filterData())},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.totalSelection=this.tbData.filter((function(e){return e.checked}))},clearSelect:function(){this.$refs.xTable1.clearCheckboxRow(),this.totalSelection=[]},fetchData:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.tbLoading=!0,!e.isCom){t.n=2;break}return t.n=1,e.getComTbData();case 1:t.n=3;break;case 2:return t.n=3,e.getPartTbData();case 3:e.initTbData(),e.filterData(),e.tbLoading=!1;case 4:return t.a(2)}}),t)})))()},setPageData:function(){var e;null!==(e=this.tbData)&&void 0!==e&&e.length&&(this.pageInfo.page=1,this.tbData=this.tbData.filter((function(e){return e.Can_Schduling_Count>0})),this.filterData())},handleSave:function(){var e=this;this.saveLoading=!0,setTimeout((function(){e.totalSelection.forEach((function(e){var t=parseInt(e.count);e.Schduled_Count+=t,e.Can_Schduling_Count-=t,e.Can_Schduling_Weight=e.Can_Schduling_Count*e.Weight,e.maxCount=e.Can_Schduling_Count,e.chooseCount=t,e.count=e.Can_Schduling_Count,e.checked=!1}));var t=(0,h.deepClone)(e.totalSelection);e.$emit("sendSelectList",t),e.$emit("close"),e.clearSelect(),e.setPage()}),0)},initTbData:function(){var e,t=this;if(null===(e=this.tbData)||void 0===e||!e.length)return this.tbData=[],void(this.backendTb=[]);var n={};this.tbData.forEach((function(e){t.$set(e,"count",e.Can_Schduling_Count),t.$set(e,"maxCount",e.Can_Schduling_Count),e.uuid=(0,d.v4)(),n[e.Type]=!0})),this.backendTb=(0,h.deepClone)(this.tbData)},getComTbData:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){var n,a,s,l,u,d,f;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n=e.$route.query,a=n.install,s=n.areaId,l=e.form,u=l.Comp_Codes,d=(0,i.default)(l,_),f=[],"[object String]"===Object.prototype.toString.call(u)&&(f=u&&u.split(" ").filter((function(e){return!!e}))),t.n=1,(0,c.GetCanSchdulingComps)((0,o.default)((0,o.default)({},d),{},{Schduling_Plan_Id:e.scheduleId,Comp_Codes:f,InstallUnit_Id:a,Area_Id:s})).then((function(t){t.IsSucceed?(e.pageInfo.total=t.Data.length,e.tbData=t.Data.map((function(e,t){return e.originalPath=e.Scheduled_Technology_Path?e.Scheduled_Technology_Path:"",e.Workshop_Id=e.Scheduled_Workshop_Id,e.Workshop_Name=e.Scheduled_Workshop_Name,e.Technology_Path=e.Scheduled_Technology_Path||e.Technology_Path,e.originalPath&&(e.isDisabled=!0),e.checked=!1,e.initRowIndex=t,e})),e.setPage()):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},handlePageChange:function(e){var t=e.currentPage,n=e.pageSize;this.tbLoading||(this.pageInfo.page=t,this.pageInfo.pageSize=n,this.setPage(),this.filterData(t))},setPage:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.tbData;this.fTable=e.slice((this.pageInfo.page-1)*this.pageInfo.pageSize,this.pageInfo.page*this.pageInfo.pageSize)},getPartTbData:function(){var e=this;return(0,s.default)((0,r.default)().m((function t(){var n,a,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n=e.$route.query,a=n.install,i=n.areaId,t.n=1,(0,u.GetPartList)((0,o.default)((0,o.default)({},e.form),{},{Schduling_Plan_Id:e.scheduleId,InstallUnit_Id:a,Area_Id:i})).then((function(t){t.IsSucceed?(e.pageInfo.total=t.Data.length,e.tbData=t.Data.map((function(e,t){if(e.Component_Technology_Path){var n=e.Component_Technology_Path.split("/");n.length&&n.some((function(t){return t===e.Part_Used_Process}))?e.originalUsedProcess=e.Part_Used_Process:(e.originalUsedProcess="",e.Part_Used_Process="")}return e.originalPath=e.Scheduled_Technology_Path?e.Scheduled_Technology_Path:"",e.Workshop_Id=e.Scheduled_Workshop_Id,e.Workshop_Name=e.Scheduled_Workshop_Name,e.Part_Used_Process=e.Scheduled_Used_Process||e.Part_Used_Process,e.Technology_Path=e.Scheduled_Technology_Path||e.Technology_Path,e.isDisabled=!!e.originalPath,e.checked=!1,e.initRowIndex=t,e})),e.setPartColumn(),e.setPage()):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},setPartColumn:function(){if(this.isOwnerNull=this.tbData.every((function(e){return!e.Comp_Import_Detail_Id})),this.isOwnerNull){var e=this.columns.findIndex((function(e){return"Component_Code"===e.Code}));-1!==e&&this.columns.splice(e,1)}},mergeData:function(e){var t=this;e.forEach((function(e){var n=t.backendTb.findIndex((function(t){return e.puuid&&t.uuid===e.puuid}));-1!==n&&t.tbData.splice(n,0,(0,h.deepClone)(t.backendTb[n]))})),this.tbData.sort((function(e,t){return e.initRowIndex-t.initRowIndex})),this.filterData()},handleClose:function(){this.$emit("close")},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"customCountColumn"===t.field},getTableConfig:function(e){var t=this;return(0,s.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,l.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,a=e.Data,o=e.Message;if(n){t.tbConfig=Object.assign({},t.tbConfig,a.Grid),t.pageInfo.pageSize=Number(t.tbConfig.Row_Number);var i=a.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return f.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),e})),t.columns.push({Display_Name:"排产数量",Code:"customCountColumn"})}else t.$message({message:o,type:"error"})}));case 1:return n.a(2)}}),n)})))()},getObjectTypeList:function(){var e=this;(0,m.GetCompTypeTree)({professional:"Steel"}).then((function(t){t.IsSucceed?(e.ObjectTypeList.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelectObjectType.treeDataUpdateFun(t.Data)}))):e.$message({type:"error",message:t.Message})}))},getType:function(){var e=this;(0,g.GetPartTypeList)({}).then((function(t){t.IsSucceed?e.typeOption=t.Data:e.$message({message:t.Message,type:"error"})}))}}}}}]);