(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-1fdd249b"],{"06f0":function(t,e,n){"use strict";n.r(e);var a=n("b240"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"111f":function(t,e,n){"use strict";n("7aae")},2349:function(t,e,n){"use strict";n("5a17")},"2dd9":function(t,e,n){"use strict";function a(t,e){t||(t={}),e||(e=[]);var n=[],a=function(a){var i;i="[object Array]"===Object.prototype.toString.call(t[a])?t[a]:[t[a]];var r=i.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(r.filter((function(t){return null!==t})).length<=0&&(r=null),r){var o={Key:a,Value:r,Type:"",Filter_Type:""},l=e.find((function(t){return t.Code===a}));o.Type=null===l||void 0===l?void 0:l.Type,o.Filter_Type=null===l||void 0===l?void 0:l.Filter_Type,n.push(o)}};for(var i in t)a(i);return n}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=a,n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("d3b7"),n("25f0")},"4e82":function(t,e,n){"use strict";var a=n("23e7"),i=n("e330"),r=n("59ed"),o=n("7b0b"),l=n("07fa"),s=n("083a"),u=n("577e"),c=n("d039"),d=n("addb"),f=n("a640"),h=n("3f7e"),p=n("99f4"),m=n("1212"),g=n("ea83"),b=[],v=i(b.sort),P=i(b.push),I=c((function(){b.sort(void 0)})),y=c((function(){b.sort(null)})),C=f("sort"),D=!c((function(){if(m)return m<70;if(!(h&&h>3)){if(p)return!0;if(g)return g<603;var t,e,n,a,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)b.push({k:e+a,v:n})}for(b.sort((function(t,e){return e.v-t.v})),a=0;a<b.length;a++)e=b[a].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),_=I||!y||!C||!D,U=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:u(e)>u(n)?1:-1}};a({target:"Array",proto:!0,forced:_},{sort:function(t){void 0!==t&&r(t);var e=o(this);if(D)return void 0===t?v(e):v(e,t);var n,a,i=[],u=l(e);for(a=0;a<u;a++)a in e&&P(i,e[a]);d(i,U(t)),n=l(i),a=0;while(a<n)e[a]=i[a++];while(a<u)s(e,a++);return e}})},"5a17":function(t,e,n){},"7aae":function(t,e,n){},"903e":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100 flex-pd16-wrap"},[n("div",{staticClass:"page-main-content cs-z-shadow"},[n("el-container",{staticStyle:{height:"100%"}},[n("el-header",{staticClass:"art-header"},[n("el-select",{staticStyle:{width:"120px"},attrs:{value:"",placeholder:"选择齐套状态",filterable:""},on:{change:function(e){return t.filterChange(1)}},model:{value:t.filterData.Ready_Status,callback:function(e){t.$set(t.filterData,"Ready_Status",e)},expression:"filterData.Ready_Status"}},[n("el-option",{attrs:{label:"全部",value:0}}),n("el-option",{attrs:{label:"已齐套",value:1}}),n("el-option",{attrs:{label:"未齐套",value:-1}})],1),n("el-button",{on:{click:t.select7Days}},[t._v("7日内待齐套")])],1),n("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"art-main"},[n("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{tableSearch:t.tableSearch,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange},scopedSlots:t._u([{key:"Delivery_Date",fn:function(e){e.column;var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Delivery_Date))+" ")]}},{key:"Is_Produced_Finish",fn:function(e){var a=e.column,i=e.row;return["是"==i[a.Code]?n("el-button",{attrs:{type:"success",round:"",size:"mini"},on:{click:function(e){return t.openPageTab("ProduceDetail",i["Id"],{row:i})}}},[t._v("是")]):n("el-tag",{staticStyle:{width:"92%",cursor:"pointer"},attrs:{type:Number(i[a.Code])<100?"":"success"},on:{click:function(e){return t.openPageTab("ProduceDetail",i["Id"],{row:i})}}},[t._v(t._s(i[a.Code])+"%")])]}},{key:"Is_In_Finish",fn:function(e){var a=e.column,i=e.row;return["是"==i[a.Code]?n("el-button",{attrs:{type:"success",round:"",size:"mini"},on:{click:function(e){return t.openPageTab("WarehousingDetail",i["Id"],{row:i,direct:0})}}},[t._v("是")]):n("el-tag",{staticStyle:{width:"92%",cursor:"pointer"},attrs:{type:Number(i[a.Code])<100?"":"success"},on:{click:function(e){return t.openPageTab("WarehousingDetail",i["Id"],{row:i,direct:0})}}},[t._v(t._s(i[a.Code])+"%")])]}},{key:"Is_Direct_Finish",fn:function(e){var a=e.column,i=e.row;return["是"==i[a.Code]?n("el-button",{attrs:{type:"success",round:"",size:"mini"},on:{click:function(e){return t.openPageTab("WarehousingDetail",i["Id"],{row:i,direct:1})}}},[t._v("是")]):n("el-tag",{staticStyle:{width:"92%",cursor:"pointer"},attrs:{type:Number(i[a.Code])<100?"":"success"},on:{click:function(e){return t.openPageTab("WarehousingDetail",i["Id"],{row:i,direct:1})}}},[t._v(t._s(i[a.Code])+"%")])]}}])})],1)],1)],1)])},i=[]},b240:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("14d9"),n("b0c0"),n("e9f5"),n("7d54"),n("ab43"),n("e9c4"),n("a9e3"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0");var i=a(n("0f97")),r=n("1b69"),o=n("f2f6"),l=n("6186"),s=a(n("2082")),u=a(n("b775")),c=n("2dd9");n("6f23"),window.open,e.default={name:"PrepareDelivery",components:{DynamicDataTable:i.default},mixins:[s.default],data:function(){return{loading:!0,gridCode:"GetDeliveryPreparePageList",addPageArray:[{path:this.$route.path+"/produce",hidden:!0,component:function(){return n.e("chunk-dd7d8504").then(n.bind(null,"9fbce"))},name:"ProduceDetail",meta:{title:"生产详情"}},{path:this.$route.path+"/warehousing",hidden:!0,component:function(){return n.e("chunk-4e9fa6b6").then(n.bind(null,"64fe"))},name:"WarehousingDetail",meta:{title:"入库详情"}}],projects:[],units:[],apis:{},tbConfig:{},columns:[],data:[],Ready_Status:0,fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0,Ready_Status:0,Days:0},ParameterJson:[]}},created:function(){var t=this;Promise.all([(0,r.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)})),(0,o.GetInstallUnitList)({}).then((function(e){e.IsSucceed&&(t.units=e.Data)}))]).then((function(){(0,l.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData()}))}))},methods:{statusChange:function(){},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:80}),this.filterData.PageSize=Number(this.tbConfig.Row_Number)},getTableData:function(){var t=this;return this.dynTblOptBak&&this.resetDynTblOpts(),this.tbConfig.Data_Url?(0,u.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{ParameterJson:(0,c.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(e){if(e.IsSucceed)return t.filterData.Days=0,t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1})):Promise.reject("invalid data api...")},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},setCols:function(t){var e=this;t.forEach((function(t){"Project_Name"==t.Code&&(t.Range=JSON.stringify(e.projects.map((function(t){return{label:t.Name,value:t.Name}})))),"Name"===t.Code&&(t.Range=JSON.stringify(e.units.map((function(t){return{label:t.Name,value:t.Name}})))),"Code"===t.Code&&(t.Range=JSON.stringify(e.units.map((function(t){return{label:t.Name,value:t.Code}}))))})),this.columns=t},filterChange:function(t){this.IsSevenDays||delete this.fiterArrObj.days,this.filterData.Page=null!==t&&void 0!==t?t:1,this.getTableData()},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.PageSize=e,this.filterData.Page=1,this.filterChange()},openPageTab:function(t,e,n){this.$router.push({name:t,query:{id:e,pg_redirect:this.$route.name},params:null!==n&&void 0!==n?n:{}})},select7Days:function(){this.filterData.Days=7,this.filterChange(1)}}}},ec45:function(t,e,n){"use strict";n.r(e);var a=n("903e"),i=n("06f0");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("2349"),n("111f");var o=n("2877"),l=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"3fd8a4a7",null);e["default"]=l.exports},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=s,e.CheckPlanTime=u,e.DeleteInstallUnit=h,e.GetCompletePercent=v,e.GetEntity=I,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=b,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=l,e.GetInstallUnitPageList=o,e.GetProjectInstallUnitList=P,e.ImportInstallUnit=m,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=y;var i=a(n("b775")),r=a(n("4328"));function o(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function I(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function y(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);