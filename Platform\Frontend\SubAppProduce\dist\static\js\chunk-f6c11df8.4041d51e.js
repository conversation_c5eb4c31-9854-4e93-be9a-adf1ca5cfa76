(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-f6c11df8"],{1398:function(e,t,a){"use strict";a.r(t);var n=a("895d"),i=a.n(n);for(var l in n)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(l);t["default"]=i.a},"2e8a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=p,t.GetCompTypeTree=u,t.GetComponentTypeEntity=c,t.GetComponentTypeList=s,t.GetFactoryCompTypeIndentifySetting=y,t.GetTableSettingList=d,t.GetTypePageList=o,t.RestoreTemplateType=b,t.SavDeepenTemplateSetting=_,t.SaveCompTypeIdentifySetting=h,t.SaveComponentType=r,t.SaveProBimComponentType=m,t.UpdateColumnSetting=v,t.UpdateComponentPartTableSetting=f;var i=n(a("b775")),l=n(a("4328"));function s(e){return(0,i.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function o(e){return(0,i.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function r(e){return(0,i.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:l.default.stringify(e)})}function p(e){return(0,i.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:l.default.stringify(e)})}function u(e){return(0,i.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:l.default.stringify(e)})}function m(e){return(0,i.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function y(e){return(0,i.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function h(e){return(0,i.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function _(e){return(0,i.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function b(e){return(0,i.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},5357:function(e,t,a){"use strict";a.r(t);var n=a("e255"),i=a("1398");for(var l in i)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(l);a("56730");var s=a("2877"),o=Object(s["a"])(i["default"],n["a"],n["b"],!1,null,"6f4daea2",null);t["default"]=o.exports},56730:function(e,t,a){"use strict";a("adb5")},"895d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),l=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("f665"),a("ab43"),a("a732"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("25f0"),a("841c");var s=a("2e8a"),o=a("ed08");t.default={name:"TemplateSetting",components:{},data:function(){return{activeNameApi:"plm_component_field_page_list",activeName:"plm_component_field_page_list",currentCode:"plm_component_page_list",typeCode:"",materialCode:"",currentFinalTypeCode:"",tabPosition:"left",tabList:[{label:"构件字段维护",value:"plm_component_field_page_list"},{label:"构件管理列表",value:"plm_component_page_list"},{label:"构件深化清单",value:"plm_component_detailImport"},{label:"构件模型清单",value:"plm_component_modelImport"},{label:"生产详情列表",value:"plm_component_produceDetail"},{label:"打包模板",value:"plm_component_packageTemplate"},{label:"模型字段对照表",value:"plm_component_modelField"}],searchVal:"",majorName:"",unit:"",steelUnit:"",templateList:[],templateListNew:[],loading:!1,systemField:!1,expandField:!1,businessField:!1}},computed:{},created:function(){this.typeCode=this.$route.query.typeCode||"",this.materialCode=this.$route.query.materialCode||"",this.currentFinalTypeCode=this.typeCode},mounted:function(){this.majorName=this.$route.query.name||"",this.unit=this.$route.query.unit||"",this.steelUnit=this.$route.query.steel_unit||"",this.GetTableSettingListFn()},methods:{changeStatus:function(e,t){var a=this.templateList.find((function(e){return e.Id==t})).Display_Name;""==a&&1==e&&(this.$message({type:"error",message:"请先填写字段名"}),this.templateList.map((function(e){return e.Id==t&&(e.Is_Enabled=!1),e})))},handleClick:function(e,t){this.currentCode=e.name,this.GetTableSettingListFn()},backPage:function(){(0,o.closeTagView)(this.$store,this.$route)},searchValue:function(){var e=this;if(this.searchVal){var t=[];this.templateList.map((function(a){-1!==a.Display_Name.search(new RegExp(e.searchVal,"ig"))&&t.push(a)})),this.templateListNew=t}else this.templateListNew=this.templateList},saveModifyChangesFn:function(){this.activeName==this.activeNameApi?this.UpdateComponentPartTableSetting():this.UpdateColumnSetting()},UpdateColumnSetting:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.loading=!0,t.n=1,(0,s.UpdateColumnSetting)(e.templateList);case 1:a=t.v,e.loading=!1,a.IsSucceed?e.$message({type:"success",message:"保存成功"}):e.$message({type:"error",message:a.Message});case 2:return t.a(2)}}),t)})))()},UpdateComponentPartTableSetting:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.loading=!0,t.n=1,(0,s.UpdateComponentPartTableSetting)(e.templateList);case 1:a=t.v,e.loading=!1,a.IsSucceed?e.$message({type:"success",message:"保存成功"}):e.$message({type:"error",message:a.Message});case 2:return t.a(2)}}),t)})))()},GetTableSettingListFn:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a={},a=e.activeName==e.activeNameApi?{IsComponent:!0,ProfessionalCode:e.currentFinalTypeCode}:{IsComponent:!0,ProfessionalCode:e.currentFinalTypeCode,TypeCode:e.currentCode+","+e.currentFinalTypeCode},t.n=1,(0,s.GetTableSettingList)(a);case 1:n=t.v,n.IsSucceed?(e.templateList=n.Data||[],e.templateListNew=e.templateList,e.systemField=e.templateList.some((function(e){return 0==e.Column_Type})),e.expandField=e.templateList.some((function(e){return 1==e.Column_Type})),e.businessField=e.templateList.some((function(e){return 2==e.Column_Type}))):e.$message({type:"error",message:n.Message});case 2:return t.a(2)}}),t)})))()}}}},adb5:function(e,t,a){},e255:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"page-container"},[a("el-button",{staticStyle:{"margin-bottom":"16px"},on:{click:e.backPage}},[e._v("返回")]),a("div",{staticClass:"top-wrapper"},[a("div",{staticClass:"info"},[e.majorName?[a("div",{staticClass:"title"},[e._v("当前专业：")]),a("div",{staticClass:"value"},[e._v(e._s(e.majorName))])]:e._e(),e.unit?[a("div",{staticClass:"title"},[e._v("统计单位：")]),a("div",{staticClass:"value"},[e._v(e._s(e.unit))])]:e._e(),e.steelUnit?[a("div",{staticClass:"title"},[e._v("构件单位：")]),a("div",{staticClass:"value"},[e._v(e._s(e.steelUnit))])]:e._e(),[a("div",{staticClass:"title"},[e._v("单位统计字段：")]),e._l(e.templateListNew,(function(t,n){return a("div",{directives:[{name:"show",rawName:"v-show",value:"SteelAmount"===t.Code,expression:"item.Code==='SteelAmount'"}],key:n,staticStyle:{display:"flex","flex-direction":"row"}},[e._v(" "+e._s(t.Display_Name)+" ")])})),e._l(e.templateListNew,(function(t,n){return a("div",{directives:[{name:"show",rawName:"v-show",value:"SteelWeight"===t.Code,expression:"item.Code==='SteelWeight'"}],key:n+999,staticStyle:{display:"flex","flex-direction":"row"}},[e._v(" *"+e._s(t.Display_Name)+" ")])}))]],2),a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},e._l(e.tabList,(function(e,t){return a("el-tab-pane",{key:t,attrs:{label:e.label,name:e.value}})})),1)],1),a("div",{staticClass:"content-wrapper",staticStyle:{"min-height":"calc(100vh - 340px)"}},[a("div",{staticClass:"right-c"},[a("el-row",{attrs:{type:"flex",justify:"space-between"}},[a("div",{staticClass:"right-c-title"},[a("div",{staticClass:"setting-title"},[e._v(e._s(1==e.systemField?"系统字段":1==e.businessField?"业务字段":1==e.expandField?"拓展字段":""))])]),a("div",{staticStyle:{display:"flex","flex-direction":"row"}},[a("span",{staticStyle:{width:"140px","font-size":"12px",height:"32px","line-height":"32px",display:"inline-block"}},[e._v("字段名称：")]),a("el-input",{attrs:{placeholder:"请输入字段名称",clearable:""},model:{value:e.searchVal,callback:function(t){e.searchVal=t},expression:"searchVal"}}),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.searchValue}},[e._v("查询")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.saveModifyChangesFn}},[e._v("保存设置")])],1)]),a("el-form",{staticStyle:{"margin-top":"24px"},attrs:{"label-width":"120px"}},[e._l(e.templateListNew,(function(t,n){return[0==t.Column_Type?a("el-row",{key:n},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:t.Code,"label-width":"150px"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{disabled:!0},model:{value:t.Display_Name,callback:function(a){e.$set(t,"Display_Name",a)},expression:"item.Display_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"备注说明"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:t.Remark,callback:function(a){e.$set(t,"Remark",a)},expression:"item.Remark"}})],1)],1),e.activeName!=e.activeNameApi?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input",{staticStyle:{width:"100px"},model:{value:t.Sort,callback:function(a){e.$set(t,"Sort",a)},expression:"item.Sort"}})],1)],1):e._e(),e.activeName!=e.activeNameApi?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"是否显示"}},[a("el-switch",{attrs:{"active-color":"#388CFF","inactive-color":"#EEEEEE"},on:{change:function(a){return e.changeStatus(a,t.Id)}},model:{value:t.Is_Enabled,callback:function(a){e.$set(t,"Is_Enabled",a)},expression:"item.Is_Enabled"}})],1)],1):e._e()],1):e._e()]})),a("div",{directives:[{name:"show",rawName:"v-show",value:1==e.businessField&&1==e.systemField,expression:"businessField==true && systemField==true"}],staticClass:"setting-title"},[e._v("业务字段")]),e._l(e.templateListNew,(function(t,n){return[2==t.Column_Type?a("el-row",{key:n},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:t.Code,"label-width":"150px"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:t.Display_Name,callback:function(a){e.$set(t,"Display_Name",a)},expression:"item.Display_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"备注说明"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:t.Remark,callback:function(a){e.$set(t,"Remark",a)},expression:"item.Remark"}})],1)],1),e.activeName!=e.activeNameApi?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input",{staticStyle:{width:"100px"},model:{value:t.Sort,callback:function(a){e.$set(t,"Sort",a)},expression:"item.Sort"}})],1)],1):e._e(),e.activeName==e.activeNameApi?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"是否启用"}},[a("el-switch",{attrs:{"active-color":"#388CFF","inactive-color":"#EEEEEE"},model:{value:t.Is_Enabled,callback:function(a){e.$set(t,"Is_Enabled",a)},expression:"item.Is_Enabled"}})],1)],1):a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"是否显示"}},[a("el-switch",{attrs:{"active-color":"#388CFF","inactive-color":"#EEEEEE"},on:{change:function(a){return e.changeStatus(a,t.Id)}},model:{value:t.Is_Enabled,callback:function(a){e.$set(t,"Is_Enabled",a)},expression:"item.Is_Enabled"}})],1)],1)],1):e._e()]})),a("div",{directives:[{name:"show",rawName:"v-show",value:1==e.expandField,expression:"expandField==true"}],staticClass:"setting-title"},[e._v("拓展字段")]),e._l(e.templateListNew,(function(t,n){return[1==t.Column_Type?a("el-row",{key:n},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:t.Code,"label-width":"150px"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:t.Display_Name,callback:function(a){e.$set(t,"Display_Name",a)},expression:"item.Display_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"备注说明"}},[a("el-input",{staticStyle:{width:"200px"},model:{value:t.Remark,callback:function(a){e.$set(t,"Remark",a)},expression:"item.Remark"}})],1)],1),e.activeName!=e.activeNameApi?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input",{staticStyle:{width:"100px"},model:{value:t.Sort,callback:function(a){e.$set(t,"Sort",a)},expression:"item.Sort"}})],1)],1):e._e(),e.activeName==e.activeNameApi?a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"是否启用"}},[a("el-switch",{attrs:{"active-color":"#388CFF","inactive-color":"#EEEEEE"},on:{change:function(a){return e.changeStatus(a,t.Id)}},model:{value:t.Is_Enabled,callback:function(a){e.$set(t,"Is_Enabled",a)},expression:"item.Is_Enabled"}})],1)],1):a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"是否显示"}},[a("el-switch",{attrs:{"active-color":"#388CFF","inactive-color":"#EEEEEE"},on:{change:function(a){return e.changeStatus(a,t.Id)}},model:{value:t.Is_Enabled,callback:function(a){e.$set(t,"Is_Enabled",a)},expression:"item.Is_Enabled"}})],1)],1)],1):e._e()]}))],2)],1)])],1)])},i=[]}}]);