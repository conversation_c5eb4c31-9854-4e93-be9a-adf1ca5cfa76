(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-57f2ea36"],{"087f":function(e,t,a){"use strict";a.r(t);var n=a("495f"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"2f7c":function(e,t,a){"use strict";a.r(t);var n=a("f351"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"368c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"选择抄送人",prop:"CC_UserId",rules:{required:!0,message:"请选择",trigger:"change"}}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:""},model:{value:e.form.CC_UserId,callback:function(t){e.$set(e.form,"CC_UserId",t)},expression:"form.CC_UserId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1),a("el-form-item",[a("div",{staticClass:"btn-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submitForm()}}},[e._v("提交")])],1)])],1)],1)},r=[]},"495f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530"));a("d81d"),a("14d9"),a("e9f5"),a("ab43"),a("d3b7"),a("3ca3"),a("ddb0");var o=n(a("0f97")),i=n(a("641e")),d=a("ed08"),u=n(a("c23a")),l=a("f4e9"),s=a("1b69"),c=a("7015f"),f=n(a("2082"));a("8975"),t.default={components:{DynamicDataTable:o.default,addCc:u.default},mixins:[i.default,f.default],data:function(){return{dialogVisible:!1,dialogVisible2:!1,loading:!1,form:{Bill_No:"",Moc_Type_Id:"",Sys_Project_Id:"",Create_Userid:"",dateRange:"",Approve_State:"",Approve_Result:"",Is_Change_Model:"",PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},addPageArray:[{path:this.$route.path+"/addChangeOrder4",hidden:!0,component:function(){return Promise.all([a.e("chunk-2d0c91c4"),a.e("chunk-70fdb730")]).then(a.bind(null,"f180"))},name:"PROAddChangeOrder4",meta:{title:"变更单详情"}}],typeList:[],projectList:[],userList:[]}},watch:{tbConfig:function(e){e.Is_Select=!0,e.Is_Row_Number=!1}},created:function(){this.getBasicData(),this.getFactoryTypeOption("pro_change_approval_tobeApproved_list")},mounted:function(){},methods:{getBasicData:function(){var e=this;(0,c.GetChangeType)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.typeList=t.Data.Data)})),(0,s.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)})),(0,l.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},fetchData:function(){var e=this;this.loading=!0;var t=(0,r.default)({},this.form);delete t["dateRange"],delete t["PageInfo"],t.Create_Begin=this.form.dateRange?(0,d.parseTime)(this.form.dateRange[0],"{y}-{m}-{d}"):"",t.Create_End=this.form.dateRange?(0,d.parseTime)(this.form.dateRange[1],"{y}-{m}-{d}"):"",(0,c.GetProjectChangeOrderList)((0,r.default)((0,r.default)({},t),this.form.PageInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Launch_Date=e.Launch_Date?(0,d.parseTime)(new Date(e.Launch_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Launch_Date,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).catch(console.error).finally((function(){e.loading=!1}))},datePickerwrapper:function(){},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},handleClose:function(){this.$refs.addCc.resetForm(),this.dialogVisible=!1},handleChange:function(e,t){var a={Id:e,type:t};this.$router.push({name:"PROAddChangeOrder4",query:{pg_redirect:"PROchangeAccount",data:a}})},handleExp:function(){var e=this;this.$confirm("导出选中数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=e.columns.map((function(e){return e.Code})),n=o(t,e.selectList),r=e.columns.map((function(e){return e.Display_Name}));function o(e,t){return t.map((function(t){return e.map((function(e){return"Is_Change_Model"===e?t[e]?"是":"否":t[e]}))}))}a.e("chunk-2d0cc0b6").then(a.t.bind(null,"4bf8",7)).then((function(e){e.export_json_to_excel({header:r,data:n,filename:"变更台账批量导出",autoWidth:!0,bookType:"xlsx"})}))})).catch((function(){}))}}}},"4e82":function(e,t,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),i=a("7b0b"),d=a("07fa"),u=a("083a"),l=a("577e"),s=a("d039"),c=a("addb"),f=a("a640"),g=a("3f7e"),h=a("99f4"),m=a("1212"),p=a("ea83"),C=[],P=r(C.sort),O=r(C.push),y=s((function(){C.sort(void 0)})),v=s((function(){C.sort(null)})),_=f("sort"),b=!s((function(){if(m)return m<70;if(!(g&&g>3)){if(h)return!0;if(p)return p<603;var e,t,a,n,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)C.push({k:t+n,v:a})}for(C.sort((function(e,t){return t.v-e.v})),n=0;n<C.length;n++)t=C[n].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),R=y||!v||!_||!b,S=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:l(t)>l(a)?1:-1}};n({target:"Array",proto:!0,forced:R},{sort:function(e){void 0!==e&&o(e);var t=i(this);if(b)return void 0===e?P(t):P(t,e);var a,n,r=[],l=d(t);for(n=0;n<l;n++)n in t&&O(r,t[n]);c(r,S(e)),a=d(r),n=0;while(n<a)t[n]=r[n++];while(n<l)u(t,n++);return t}})},5579:function(e,t,a){},"641e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,r.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(a){a.IsSucceed?(t.ProfessionalType=a.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:a.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,n.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=t.tbConfig.Code.split(",");"plm_component_page_list"!==i[0]&&"plm_parts_page_list"!==i[0]||(t.tbConfig.Is_Page=!0),t.columns=(r.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+r.Grid.Row_Number:t.form.PageSize=+r.Grid.Row_Number,a(t.columns),t.fetchData()}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},"7015f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=_,t.AgainSubmitChangeOrder=y,t.BatchReuseEngineeringContactChangedComponentPart=ie,t.BatchReuseEngineeringContactMocComponentPart=de,t.CancelChangeOrder=O,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=j,t.DeleteChangeOrder=c,t.DeleteChangeOrderV2=w,t.DeleteChangeReason=m,t.DeleteChangeType=d,t.DeleteEngineeringContactChangeOrder=q,t.DeleteMocOrder=Y,t.DeleteMocType=Pe,t.ExportEngineeringContactChangedAddComponentPart=se,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=re,t.ExportMocAddComponentPart=ce,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=R,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=l,t.GetChangeOrderTaskInfo=k,t.GetChangeOrderTaskPageList=I,t.GetChangeOrderV2=$,t.GetChangeOrderV2PageList=T,t.GetChangeReason=g,t.GetChangeType=o,t.GetChangedComponentPartPageList=V,t.GetChangedComponentPartProductionList=U,t.GetCompAndPartSchdulingPageList=L,t.GetCompAndPartTaskList=M,t.GetCompanyUserPageList=E,t.GetEngineeringContactChangeOrder=J,t.GetEngineeringContactChangeOrderPageList=N,t.GetEngineeringContactChangedAddComponentPartPageList=ue,t.GetEngineeringContactChangedAddComponentPartSummary=le,t.GetEngineeringContactChangedComponentPartPageList=K,t.GetEngineeringContactChangedSummary=X,t.GetEngineeringContactFileInfo=Q,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=W,t.GetEngineeringContactMocSummary=ne,t.GetFactoryChangeTypeListV2=z,t.GetFactoryPeoplelist=u,t.GetMocAddComponentPartPageList=ge,t.GetMocModelList=ye,t.GetMocOrderInfo=pe,t.GetMocOrderPageList=he,t.GetMocOrderTypeList=Ce,t.GetMyChangeOrderPageList=P,t.GetProjectAreaChangeTreeList=B,t.GetProjectChangeOrderList=G,t.GetTypeReason=p,t.ImportChangFile=me,t.ImportChangeDeependFile=C,t.QueryHistories=b,t.ReuseEngineeringContactChangedComponentPart=ae,t.ReuseEngineeringContactMocComponentPart=oe,t.SaveChangeOrder=s,t.SaveChangeOrderTask=D,t.SaveChangeOrderV2=x,t.SaveChangeReason=h,t.SaveChangeType=i,t.SaveEngineeringContactChangeOrder=H,t.SaveMocOrder=ve,t.SaveMocOrderType=Oe,t.SubmitChangeOrder=v,t.SubmitChangeOrderV2=F,t.SubmitMocOrder=A,t.Verification=S;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function b(e){return(0,r.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function R(e){return(0,r.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function E(e){return(0,r.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function Oe(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function ye(e){return(0,r.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},"768c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"110px"}},[a("el-form-item",{attrs:{label:"变更单号",prop:"Bill_No"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No",t)},expression:"form.Bill_No"}})],1),a("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.typeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"发起人",prop:"Create_Userid"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Create_Userid,callback:function(t){e.$set(e.form,"Create_Userid",t)},expression:"form.Create_Userid"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"发起时间",prop:"dateRange"}},[a("el-date-picker",{staticStyle:{width:"220px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange,callback:function(t){e.$set(e.form,"dateRange",t)},expression:"form.dateRange"}})],1),a("el-form-item",{attrs:{label:"是否有变更任务",prop:"Is_Change_Model"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Is_Change_Model,callback:function(t){e.$set(e.form,"Is_Change_Model",t)},expression:"form.Is_Change_Model"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",{staticStyle:{width:"100%"}},[a("el-button",{attrs:{type:"primary",disabled:0===e.selectList.length},on:{click:function(t){return e.handleExp()}}},[e._v("批量导出")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0",height:"calc(100% - 46px)"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Is_Change_Model",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Is_Change_Model?"是":"否"))])]}},{key:"op",fn:function(t){var n=t.row,r=t.index;return[a("div",[a("el-button",{attrs:{index:r,type:"text"},on:{click:function(t){return e.handleChange(n.Id,1)}}},[e._v("查看")])],1)]}}])})],1)],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"选择抄送人",visible:e.dialogVisible,width:"576px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("add-Cc",{ref:"addCc",on:{close:e.handleClose,refresh:e.fetchData}})],1)],1)])])},r=[]},"7ac1":function(e,t,a){},c23a:function(e,t,a){"use strict";a.r(t);var n=a("368c"),r=a("2f7c");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("d928");var i=a("2877"),d=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"1e65206d",null);t["default"]=d.exports},c520:function(e,t,a){"use strict";a("7ac1")},c6f2:function(e,t,a){"use strict";a.r(t);var n=a("768c"),r=a("087f");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("c520");var i=a("2877"),d=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"13e5133e",null);t["default"]=d.exports},d928:function(e,t,a){"use strict";a("5579")},f351:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b");var r=n(a("5530")),o=a("7015f");t.default={data:function(){return{form:{Id:"",CC_UserId:[]},btnLoading:!1,userList:[]}},created:function(){this.getBasicData()},mounted:function(){},methods:{init:function(e){this.form.Id=e},getBasicData:function(){var e=this;(0,o.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;var a=(0,r.default)({},e.form),n="";0!==a.CC_UserId.length&&(n=a.CC_UserId.join(",")),(0,o.AddChangeCopyHistory)({changeId:e.form.Id,userIds:n}).then((function(t){t.IsSucceed?(e.$message({message:"抄送成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"})}))}))},resetForm:function(){this.$refs.form.resetFields(),this.form.Id="",this.form.CC_UserId=[],this.btnLoading=!1}}}},f4e9:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteQuotaList=c,t.DeleteSalary=d,t.ExportPlanSalary=p,t.GetFactoryPeoplelist=l,t.GetFactorySalaryPageList=u,t.GetPlanSalaryDetailList=m,t.GetPlanSalaryPageList=h,t.GetQuotaDetail=g,t.GetQuotaPageList=f,t.ImportSalaryFiles=o,t.SaveQuotaEntity=s,t.UpdateImportSalaryFile=i;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Factory/ImportSalaryFiles",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Factory/UpdateImportSalaryFile",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Factory/DeleteSalary",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Factory/GetFactorySalaryPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ProductionSalary/SaveQuotaEntity",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/ProductionSalary/DeleteQuotaList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetQuotaPageList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetQuotaDetail",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetPlanSalaryPageList",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProductionSalary/GetPlanSalaryDetailList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProductionSalary/ExportPlanSalary",method:"post",data:e})}}}]);