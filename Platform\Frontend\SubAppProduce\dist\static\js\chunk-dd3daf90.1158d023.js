(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-dd3daf90"],{1162:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n=a("7757"),r=a("4f39");e.default={props:{statisticalDate:{type:String,default:""},factoryId:{type:String,required:!0},reportType:{type:Number,required:!0}},data:function(){return{showTip:!1,time:"",curDate:""}},watch:{statisticalDate:function(t){this.curDate=t,this.curDate&&this.getUpdate()}},methods:{getUpdate:function(){var t=this;(0,n.GetBusinessLastUpdateDate)({StatisticalDate:this.curDate,FactoryId:this.factoryId,ReportType:this.reportType}).then((function(e){if(e.IsSucceed){var a=e.Data,n=a.Business_Last_Date,i=a.Account_Generate_Date,o=new Date(n),c=new Date(i);o>c?(t.showTip=!0,t.time=(0,r.parseTime)(o)):(t.showTip=!1,t.time="")}else t.$message({message:e.Message,type:"error"})}))}}}},"1b7e":function(t,e,a){"use strict";a.r(e);var n=a("1162"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"41a7":function(t,e,a){"use strict";a.r(e);var n=a("dd68"),r=a("9fa9");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("bbfb");var o=a("2877"),c=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"424c07da",null);e["default"]=c.exports},"4f39":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=i,e.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var r=n(a("53ca"));function i(t,e){if(0===arguments.length||!t)return null;var a,n=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,r.default)(t)?a=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),a=new Date(t));var i={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=n.replace(/{([ymdhisa])+}/g,(function(t,e){var a=i[e];return"a"===e?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var a=t.split("~"),n=i(new Date(a[0]),e)+" ~ "+i(new Date(a[1]),e);return n}return t&&t.length>0?i(new Date(t),e):void 0}},8926:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("14d9"),a("b0c0"),a("e9f5"),a("7d54"),a("d3b7"),a("159b");var r=n(a("c14f")),i=n(a("1da1")),o=n(a("ac03")),c=a("cf45"),s=a("4f39"),u=n(a("3502")),l=n(a("90f5")),d=a("bc1b"),f=a("596c"),h=a("db0a"),m=a("05e0");e.default={name:"PROOtherReportDetail",components:{VTable:u.default,UpdateDate:l.default},mixins:[o.default],data:function(){return{updateBtn:!1,showBtn:!1,isView:!1,toBeConfirmed:!1,form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],unCheck:!1}},computed:{curTitle:function(){return"".concat((0,s.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计金额（元）")}},beforeCreate:function(){this.curModuleKey=f.curModuleKey},mounted:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,t.toBeConfirmed="check"===t.$route.query.type,t.isView="view"===t.$route.query.type,t.fetchData(),e.n=1,(0,c.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate()&&(this.loading=!0,(0,d.GetQTProjectSummaryList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[],t.tableData.length?(t.toBeConfirmed=2===t.tableData[0].AccountingStatus,t.unCheck=1===t.tableData[0].AccountingStatus,t.isView=!t.toBeConfirmed,t.showBtn=!0):t.showBtn=!1;var a=t.setTotalData(t.tableData,t.generateColumn()),n=a.column;t.columns=n,t.$refs["tb"].setColumns(n)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleUpdate:function(){var t=this;this.$confirm("是否更新数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.updateBtn=!0,(0,h.DailyBatch)({Factory_Id:t.form.FactoryId,Date:t.form.StatisticalDate}).then((function(e){e.IsSucceed?(t.$refs["updateDate"].getUpdate(),t.fetchData(),t.$message({message:"更新成功",type:"success"})):t.$message({message:e.Message,type:"error"}),t.updateBtn=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleSubmit:function(){var t=this;this.$confirm("是否提交核算?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.SubmitQTAccounting)({StatisticalDate:t.form.StatisticalDate,FactoryId:t.form.FactoryId}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleMarkingInfo:function(){this.$router.push({name:"OMAOtherOutputDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleMarkingCostInfo:function(){this.$router.push({name:"OMAOtherCostDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleMarkingFeeInfo:function(){this.$router.push({name:"OMAOtherFeeDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleMarkingBalanceInfo:function(){this.$router.push({name:"OMAOtherOtherFeeDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},handleCheck:function(t){var e=this;this.$confirm("是否确定审核".concat(t?"通过":"不通过","?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.ReviewStockControlSummary)({FactoryId:e.form.FactoryId,Is_Approved:t,StatisticalDate:e.form.StatisticalDate}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},generateColumn:function(){var t=this,e=this.$createElement,a=140,n=[{fixed:"left",title:"项目简称",params:{rowSpan:2},children:[{params:{none:"none"},children:[{title:this.curTitle,params:{colSpan:3},minWidth:m.ProjectAbbreviationW,field:"ProjectAbbreviation"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目编号",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:m.ProjectNumberW,field:"ProjectNumber"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目状态",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:m.ProjectStatusW,field:"ProjectStatus"}]}]},{params:{bg:"bg-cyan"},slots:{header:function(){var a=[e("span",["营业外收入(元) "])];return t.getRoles("OMAOtherDetailOutDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMarkingInfo()}}},["查看详情"])),a}},children:[],cKey:"1"},{params:{bg:"bg-blue"},slots:{header:function(){var a=[e("span",["主材成本(元) "])];return t.getRoles("OMAOtherDetailCostDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMarkingCostInfo()}}},["查看详情"])),a}},children:[],cKey:"2"},{params:{bg:"bg-orange"},slots:{header:function(){var a=[e("span",["辅料费用(元) "])];return t.getRoles("OMAOtherDetailFeeDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMarkingFeeInfo()}}},["查看详情"])),a}},children:[],cKey:"3"},{title:"资产折旧(元)",params:{bg:"bg-purple"},children:[],cKey:"5"},{params:{bg:"bg-yellow"},children:[],slots:{header:function(){var a=[e("span",["其他费用(元) "])];return t.getRoles("OMAOtherDetailOtherDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleMarkingBalanceInfo()}}},["查看详情"])),a}},cKey:"4"}];return n.forEach((function(e){if(e.cKey){var n=[];t.department.forEach((function(t,r){var i={title:t.Display_Name,children:[{minWidth:a,field:"".concat(e.cKey,"|").concat(t.Id),title:0,isTotal:!0}]};n.push(i)})),n.push({title:"小计",children:[{minWidth:a,formatter:["formatNum",2],field:"".concat(e.cKey,"|subtotal"),title:0,isTotal:!0}]}),e.children=n}})),n}}}},"90f5":function(t,e,a){"use strict";a.r(e);var n=a("b5756"),r=a("1b7e");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("e209");var o=a("2877"),c=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"79bd74a2",null);e["default"]=c.exports},"9fa9":function(t,e,a){"use strict";a.r(e);var n=a("8926"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},b5756:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",[a("span",{staticClass:"cs-label"},[t._v("数据列表")]),t.time?a("span",{staticClass:"cs-time"},[t._v("数据更新时间："+t._s(t.time))]):t._e(),t.showTip?a("span",{staticClass:"cs-tip"},[t._v("有数据变更请点击更新数据")]):t._e()])},r=[]},bbfb:function(t,e,a){"use strict";a("ebbc")},cbdc:function(t,e,a){},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=r,a("d3b7");var n=a("6186");function r(t){return new Promise((function(e,a){(0,n.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},db0a:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DailyBatch=l,e.GetAccountingDefaultSetting=o,e.GetProjectAccountingSettingDetail=c,e.GetProjectListForAccounting=i,e.SaveDefaultAccountingSetting=u,e.SaveProjectAccountingSetting=s;var r=n(a("b775"));function i(t){return(0,r.default)({url:"/oma/AccountingSetting/GetProjectListForAccounting",method:"post",data:t})}function o(t){return(0,r.default)({url:"/oma/AccountingSetting/GetAccountingDefaultSetting",method:"post",data:t})}function c(t){return(0,r.default)({url:"/oma/AccountingSetting/GetProjectAccountingSettingDetail",method:"post",data:t})}function s(t){return(0,r.default)({url:"/oma/AccountingSetting/SaveProjectAccountingSetting",method:"post",data:t})}function u(t){return(0,r.default)({url:"/oma/AccountingSetting/SaveDefaultAccountingSetting",method:"post",data:t})}function l(t){return(0,r.default)({url:"/oma/AccountingSetting/DailyBatch",method:"post",data:t})}},dd68:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:t.loading},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("UpdateDate",{ref:"updateDate",attrs:{"statistical-date":t.form.StatisticalDate,"factory-id":t.form.FactoryId,"report-type":4}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"btn-x"},[t.isView&&t.unCheck&&t.getRoles("OMAOtherDetailUpdate")?a("el-button",{attrs:{disabled:t.loading,loading:t.updateBtn},on:{click:t.handleUpdate}},[t._v("更新数据")]):t._e(),t.getRoles("OMAOtherDetailExport")?a("el-button",{attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e(),t.unCheck&&t.getRoles("OMAOtherDetailSubmit")?a("el-button",{attrs:{disabled:t.loading,type:"primary"},on:{click:t.handleSubmit}},[t._v("提交核算")]):t._e(),t.toBeConfirmed&&t.getRoles("OMAOtherCheck")?[a("el-button",{attrs:{type:"danger",disabled:t.loading},on:{click:function(e){return t.handleCheck(!1)}}},[t._v("审核不通过")]),a("el-button",{attrs:{type:"success",disabled:t.loading},on:{click:function(e){return t.handleCheck(!0)}}},[t._v("审核通过")])]:t._e()],2)],1),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},r=[]},e209:function(t,e,a){"use strict";a("cbdc")},ebbc:function(t,e,a){}}]);