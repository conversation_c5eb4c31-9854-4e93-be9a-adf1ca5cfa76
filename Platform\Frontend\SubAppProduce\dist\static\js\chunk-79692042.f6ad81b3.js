(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-79692042"],{2131:function(t,n,e){"use strict";e.r(n);var u=e("688c"),r=e.n(u);for(var c in u)["default"].indexOf(c)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(c);n["default"]=r.a},"64cc0":function(t,n,e){"use strict";e.d(n,"a",(function(){return u})),e.d(n,"b",(function(){return r}));var u=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("detail",{attrs:{"is-edit":!0}})},r=[]},"688c":function(t,n,e){"use strict";var u=e("4ea4").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=u(e("b156b"));n.default={components:{detail:r.default},data:function(){return{}}}},"733c":function(t,n,e){"use strict";e.r(n);var u=e("64cc0"),r=e("2131");for(var c in r)["default"].indexOf(c)<0&&function(t){e.d(n,t,(function(){return r[t]}))}(c);var a=e("2877"),i=Object(a["a"])(r["default"],u["a"],u["b"],!1,null,"50d119c9",null);n["default"]=i.exports}}]);