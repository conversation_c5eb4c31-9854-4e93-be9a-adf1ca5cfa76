(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4fa978a0"],{"0457":function(t,e,a){"use strict";a.r(e);var i=a("a134"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"15fd":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,a("a4d3");var i=n(a("ccb5"));function n(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(null==t)return{};var a,n,r=(0,i.default)(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(n=0;n<s.length;n++)a=s[n],-1===e.indexOf(a)&&{}.propertyIsEnumerable.call(t,a)&&(r[a]=t[a])}return r}},"175a":function(t,e,a){},"28ac":function(t,e,a){"use strict";a.r(e);var i=a("b09f"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"2fcc":function(t,e,a){},"32ba":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cs-empty"},[t._v(" "+t._s(t.text)+" ")])},n=[]},"5f91":function(t,e,a){},6268:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form",{ref:"form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"项目搜索"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号",clearable:""},model:{value:t.form.Keywords,callback:function(e){t.$set(t.form,"Keywords",e)},expression:"form.Keywords"}})],1),a("el-form-item",{attrs:{label:"项目状态"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.statusOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"项目创建时间",prop:"accountingDate"}},[a("el-date-picker",{staticStyle:{width:"251px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.accountingDate,callback:function(e){t.accountingDate=e},expression:"accountingDate"}})],1),a("el-form-item",{attrs:{label:"配置项",prop:"IsConfigured"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.IsConfigured,callback:function(e){t.$set(t.form,"IsConfigured",e)},expression:"form.IsConfigured"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"待完善",value:!1}}),a("el-option",{attrs:{label:"已完善",value:!0}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.getProjectList()}}},[t._v("查询")]),a("el-button",{on:{click:t.resetForm}},[t._v("重置")])],1)],1)],1),a("el-col",{staticClass:"text-right",attrs:{span:4}},[a("el-button",{on:{click:t.handleDefaultSetting}},[t._v("默认参数设置")])],1)],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"left"},[a("div",{staticClass:"cs-title bt"},[t._v("项目列表")]),a("div",{staticClass:"left-list"},t._l(t.projectList,(function(e){return a("div",{key:e.Project_Code,class:["p-wrapper",{"p-active":e.Sys_Project_Id===t.curProject}],on:{click:function(a){return t.handleProjectClick(e)}}},[a("div",{staticClass:"p-x"},[a("span",{staticClass:"p-title"},[t._v(t._s(e.Short_Name))]),a("span",{staticClass:"p-sub"},[t._v(t._s(e.Project_Code))])]),a("el-tag",{attrs:{type:e.Is_Configured?"info":"danger"}},[t._v(t._s(e.Is_Configured?"已完善":"待完善"))])],1)})),0)]),t.curProject?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"right"},[a("div",{staticClass:"right-top"},[a("div",{staticClass:"cs-title"},[t._v("运营核算配置项:"+t._s(t.curProjectName))]),a("div",{staticClass:"btn-x"},[a("el-button",{attrs:{disabled:!t.processFactoryList.length},on:{click:t.fetchDefaultData}},[t._v("恢复默认值")]),a("el-button",{attrs:{disabled:!t.processFactoryList.length,type:"primary"},on:{click:t.handleSave}},[t._v("保存数据")])],1)]),a("div",{staticClass:"right-main"},[a("el-tabs",{attrs:{type:"card"},on:{"tab-click":t.handleTabClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.factoryList,(function(t){return a("el-tab-pane",{key:t.Id,attrs:{label:t.Short_Name,name:t.Id}})})),1),t.processFactoryList.includes(t.activeName)?[a("h4",[t._v("基础配置项")]),a("div",{staticClass:"items-x"},t._l(t.baseKeyList[t.activeName],(function(e){return a("item-info",{key:e.label,attrs:{code:e.code,value:e.value,width:"33%","is-default":e.isDefault,disabled:e.disabled,label:e.label},on:{"update:value":function(a){return t.$set(e,"value",a)},change:t.inputChange},scopedSlots:t._u([e.isCustom?{key:"content",fn:function(){return[e.isContract?a("div",{staticClass:"cs-row"},[a("span",[t._v(t._s(e.value))]),a("span",{staticClass:"cs-red",attrs:{title:"（项目基础信息-执行单价）"}},[t._v("（项目基础信息-执行单价）")])]):a("div",{staticClass:"cs-content"},[e.rule?a("el-popover",{attrs:{placement:"bottom",title:"",trigger:"click",content:e.rule}},[a("span",{staticClass:"blue",attrs:{slot:"reference"},slot:"reference"},[t._v("查看公式")])]):t._e(),t._v(" "+t._s(e.value)+" ")],1)]},proxy:!0}:null],null,!0)})})),1)]:a("empty",{attrs:{type:t.activeName?2:1}}),t.activeName?[t.curProcessList.length?a("div",{staticClass:"p-box"},[a("h5",{staticClass:"p-box-title"},[t._v("生产配置项")]),t._l(t.curProcessList,(function(e){return a("div",{key:e.key,staticClass:"plist"},[a("item-info",{attrs:{disabled:"",width:"50%",label:e.label1,value:e.value1},on:{"update:value":function(a){return t.$set(e,"value1",a)}}}),a("item-info",{attrs:{"is-default":e.isDefault,width:"50%",label:e.label2,value:e.value2},on:{"update:value":function(a){return t.$set(e,"value2",a)}}})],1)}))],2):t._e(),t.curProcessOutList.length?a("div",{staticClass:"p-box"},[a("h5",{staticClass:"p-box-title"},[t._v("外协工序配置项")]),t._l(t.curProcessOutList,(function(e){return a("div",{key:e.key,staticClass:"plist"},[a("item-info",{attrs:{width:"50%",disabled:"",label:e.label1,value:e.value1},on:{"update:value":function(a){return t.$set(e,"value1",a)}}}),a("item-info",{attrs:{"is-default":e.isDefault,width:"50%",label:e.label2,value:e.value2},on:{"update:value":function(a){return t.$set(e,"value2",a)}}})],1)}))],2):t._e()]:t._e(),t.outFactoryList.length?a("el-divider"):t._e(),t.outFactoryList.length?a("div",{staticClass:"p-box"},[a("h4",{staticClass:"p-box-title"},[t._v("外协工厂配置项")]),t._l(t.outFactoryList,(function(e){return a("div",{key:e.key,staticClass:"plist"},[a("item-info",{attrs:{disabled:"",width:"33%",label:e.label1,value:e.value1},on:{"update:value":function(a){return t.$set(e,"value1",a)}}}),a("item-info",{attrs:{width:"33%","is-default":e.isDefault1,label:e.label2,value:e.value2},on:{"update:value":function(a){return t.$set(e,"value2",a)}}}),a("item-info",{attrs:{width:"33%","is-default":e.isDefault2,label:e.label3,value:e.value3},on:{"update:value":function(a){return t.$set(e,"value3",a)}}})],1)}))],2):t._e()],2)]):t._e()])],1)])},n=[]},7757:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.FeeRegistrationImport=d,e.GetBusinessLastUpdateDate=l,e.GetFactoryProcessLibs=u,e.GetFactoryProjectList=c,e.GetFirstLevelDepartsUnderFactory=s,e.GetNonExternalFactory=r,e.GetOMALatestAccountingDate=_,e.GetQueryNonExternalFactory=f,e.GetReportLastDate=o;var n=i(a("b775"));function r(t){return(0,n.default)({url:"/oma/Common/GetNonExternalFactory",method:"post",data:t})}function s(t){return(0,n.default)({url:"/oma/Common/GetFirstLevelDepartsUnderFactory",method:"post",data:t})}function c(t){return(0,n.default)({url:"/oma/Common/GetFactoryProjectList",method:"post",data:t})}function o(t){return(0,n.default)({url:"/oma/Common/GetReportLastDate",method:"post",data:t})}function u(t){return(0,n.default)({url:"/oma/Common/GetFactoryProcessLibs",method:"post",data:t})}function l(t){return(0,n.default)({url:"/oma/Common/GetBusinessLastUpdateDate",method:"post",data:t})}function f(t){return(0,n.default)({url:"/oma/Common/GetQueryNonExternalFactory",method:"post",data:t})}function d(t){return(0,n.default)({url:"/oma/FeeRegistration/Import",method:"post",data:t})}function _(t){return(0,n.default)({url:"/oma/common/GetOMALatestAccountingDate",method:"post",data:t})}},"7bdd":function(t,e,a){"use strict";a("2fcc")},"7fae":function(t,e,a){"use strict";a("5f91")},"80fb":function(t,e,a){"use strict";a.r(e);var i=a("9dfc"),n=a("9e28");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("7fae");var s=a("2877"),c=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"05594e98",null);e["default"]=c.exports},"89cc":function(t,e,a){"use strict";a.r(e);var i=a("32ba"),n=a("28ac");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("d194");var s=a("2877"),c=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"3896f262",null);e["default"]=c.exports},"9dfc":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"inner-item-x",style:{width:t.width}},[a("div",{staticClass:"item-left",style:{minWidth:t.labelWidth}},[t._v(" "+t._s(t.label)+" ")]),a("div",{class:["item-right",{disabled:t.disabled}],on:{click:t.showInput}},[t.editing?a("el-input-number",{ref:"input",staticClass:"cs-number-btn-hidden",staticStyle:{width:"100%"},attrs:{min:0,size:"mini",precision:2,step:.1},on:{blur:t.saveInput},model:{value:t.inputValue,callback:function(e){t.inputValue=e},expression:"inputValue"}}):a("div",{staticClass:"w100"},[t._t("content",[a("span",{class:{"is-red":t.isDefault}},[t._v(t._s(t.curVal))])])],2)],1)])},n=[]},"9e28":function(t,e,a){"use strict";a.r(e);var i=a("a70c8"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},a134:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("5530")),r=i(a("2909")),s=i(a("15fd")),c=i(a("c14f")),o=i(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("2532"),a("3ca3"),a("159b"),a("ddb0");var u=a("8975"),l=a("cf45"),f=a("db0a"),d=i(a("80fb")),_=i(a("89cc")),v=i(a("2082")),p=i(a("6612")),m=a("7757"),h=a("ed08"),g=["isDefault"],y=["isDefault1","isDefault2"];e.default={name:"PROProjectAccountingSetting",components:{ItemInfo:d.default,Empty:_.default},mixins:[v.default],data:function(){return{addPageArray:[{path:this.$route.path+"/defaultSetting",hidden:!0,component:function(){return a.e("chunk-3441760e").then(a.bind(null,"256b"))},name:"PROProjectAccountingDefaultSetting",meta:{title:"默认参数设置"}}],isResetDefault:!1,fc:[],curProject:"",curProjectName:"",activeName:"",project_status:"",form:{Keywords:"",ProjectStatus:"",StartDate:"",EndDate:"",IsConfigured:""},processFactoryList:[],factoryList:[],outFactoryList:[],processList:[],curProcessList:[],curProcessOutList:[],processOutList:[],projectList:[],statusOption:[],loading:!1,flag:!0,baseKeyList:{}}},computed:{accountingDate:{get:function(){return[(0,u.timeFormat)(this.form.StartDate),(0,u.timeFormat)(this.form.EndDate)]},set:function(t){if(t){var e=t[0],a=t[1];this.form.StartDate=(0,u.timeFormat)(e),this.form.EndDate=(0,u.timeFormat)(a)}else this.form.StartDate="",this.form.EndDate=""}}},beforeRouteLeave:function(t,e,a){var i=this;return(0,o.default)((0,c.default)().m((function t(){return(0,c.default)().w((function(t){while(1)switch(t.n){case 0:i.isResetDefault?i.checkDefaultChange()?(i.isResetDefault=!1,a()):a(!1):a();case 1:return t.a(2)}}),t)})))()},activated:function(){this.flag?(this.getUnitInfo(),this.getProjectList(),this.flag=!1):this.fetchData()},methods:{getUnitInfo:function(){var t=this;return(0,o.default)((0,c.default)().m((function e(){return(0,c.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,l.getDictionary)("Engineering Status");case 1:t.statusOption=e.v;case 2:return e.a(2)}}),e)})))()},fetchData:function(){var t=arguments,e=this;return(0,o.default)((0,c.default)().m((function a(){return(0,c.default)().w((function(a){while(1)switch(a.n){case 0:t.length>0&&void 0!==t[0]&&t[0],e.loading=!0,(0,f.GetProjectAccountingSettingDetail)({sysProjectId:e.curProject}).then((function(t){if(t.IsSucceed){var a,i;e.factoryList=[];var n=e.mergeData(null===(a=t.Data)||void 0===a?void 0:a.Default_Setting,null===(i=t.Data)||void 0===i?void 0:i.Project_Setting),r=n.resultBase,s=n.pFactory,c=n.pExtrnal;e.Base_Setting=r,e.initTb({Base_Setting:r,Factory_Process_Setting_List:s,External_Factory_List:c})}else e.$message({message:t.Message,type:"error"});e.loading=!1})).finally((function(){}));case 1:return a.a(2)}}),a)})))()},fetchDefaultData:function(){var t=this;this.$confirm("是否恢复默认值?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.GetAccountingDefaultSetting)({}).then((function(e){if(e.IsSucceed){var a=e.Data,i=a.Base_Setting,n=(a.Factory_List,a.Factory_Process_Setting_List),r=a.External_Factory_List,s=t.factoryList.map((function(t){return t.Id})),c=n.filter((function(t){return s.includes(t.Factory_Id)}));t.initTb({Base_Setting:i,Factory_Process_Setting_List:c,External_Factory_List:r}),t.isResetDefault=!0}else t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},changeFcId:function(t){this.fcId=t,this.fetchData()},calcProducePrice:function(t){var e=t.Material_Allocate_Unit_Price,a=t.Loss_Rate,i=t.Process_Allocate_Unit_Price,n=0;try{n=+(0,p.default)(e||0).multiply((0,p.default)(a||0).divide(100).add(1).value()).add(i||0).format("0.[00]")}catch(r){n=0}return n},initTb:function(t){var e=this,a=t.Base_Setting,i=t.Factory_Process_Setting_List,n=t.External_Factory_List;this.outFactoryList=[],this.processList=[],this.processOutList=[];var r=[];i.forEach((function(t,a){var i=t.isDefault,n=(0,s.default)(t,g),c={value1:t.Process_Name,label1:"工序名称",label2:"人工单价(元)",value2:t.Unit_Price,key:"v"+a,factoryId:t.Factory_Id,curItem:n,isDefault:i};r.push(t.Factory_Id),t.Is_External?(c.label1="外协工序名称",c.label2="外协加工单价(元)",e.processOutList.push(c)):e.processList.push(c)})),this.processFactoryList=(0,h.uniqueArr)(r),this.factoryList.length&&(this.activeName=this.factoryList[0].Id,this.filterProcess(),this.filterOutProcess()),n.forEach((function(t,a){var i=t.isDefault1,n=t.isDefault2,r=(0,s.default)(t,y);e.outFactoryList.push({value1:t.Factory_Name,label1:"外协工厂名称",value2:t.Material_Unit_Price,label2:"外协材料单价(元)",value3:t.Labor_Unit_Price,label3:"外协人工单价(元)",key:a,curItem:r,isDefault1:i,isDefault2:n})})),a.forEach((function(t,a){e.setBase(t)}))},setBase:function(t){var e=(0,p.default)(t["Loss_Rate"]||0).multiply(100).value(),a=this.calcProducePrice({Material_Allocate_Unit_Price:t["Material_Allocate_Unit_Price"]||0,Loss_Rate:e,Process_Allocate_Unit_Price:t["Process_Allocate_Unit_Price"]||0}),i=[{value:(0,p.default)(t["Product_Output_Tax_Rate"]||0).multiply(100).value(),label:"产品产值税率(%)",code:"Product_Output_Tax_Rate",isDefault:t["isProduct_Output_Tax_RateDefault"]},{value:(0,p.default)(t["Labor_Output_Tax_Rate"]||0).multiply(100).value(),label:"劳务产值税率(%)",code:"Labor_Output_Tax_Rate",isDefault:t["isLabor_Output_Tax_RateDefault"]},{value:e,label:"损耗率(%)",code:"Loss_Rate",isDefault:t["isLoss_RateDefault"]},{value:t["Material_Allocate_Unit_Price"],label:"物控调拨单价(元)",code:"Material_Allocate_Unit_Price",isDefault:t["isMaterial_Allocate_Unit_PriceDefault"]},{value:t["Management_Fee_Self_Supplying"],label:"主材管理费(元)",code:"Management_Fee_Self_Supplying",isDefault:t["isManagement_Fee_Self_SupplyingDefault"]},{value:t["Process_Allocate_Unit_Price"],label:"加工调拨单价(元)",code:"Process_Allocate_Unit_Price",isDefault:t["isProcess_Allocate_Unit_PriceDefault"]},{value:a,label:"生产调拨单价",disabled:!0,isCustom:!0,rule:"物控调拨单价*（1+损耗率）+加工调拨单价",code:"Produce_Allocate_Unit_Price"},{value:t["Difficulty_Coefficient"],label:"该项目难度系数",code:"Difficulty_Coefficient",isDefault:t["isDifficulty_CoefficientDefault"]},{value:t["Cost_Amorization_Coefficient"],label:"公共成本摊销系数",disabled:!0,isCustom:!0,rule:"单项目焊工完工量*单项目难度系数/∑在建项目焊工完工量*单项目难度系数",code:"Cost_Amorization_Coefficient"},{value:t["Stock_Amorization_Coefficient"],label:"公共结存摊销系数",disabled:!0,isCustom:!0,rule:"单项目合同用钢量/∑在建项目合同用钢量",code:"Stock_Amorization_Coefficient"},{value:t["Contract_Unit_Price"],label:"合同综合单价(元)",disabled:!0,isCustom:!0,isContract:!0,code:"Contract_Unit_Price"},{value:t["Manage_Fee_Coefficient"],label:"项目管理费系数",code:"Manage_Fee_Coefficient",isDefault:t["isManage_Fee_CoefficientDefault"]}];this.$set(this.baseKeyList,t.Factory_Id,i)},filterProcess:function(){var t=this;this.curProcessList=this.processList.filter((function(e){return e.factoryId===t.activeName}))},filterOutProcess:function(){var t=this;this.curProcessOutList=this.processOutList.filter((function(e){return e.factoryId===t.activeName}))},filterBaseSetting:function(){},isEmpty:function(t){return[null,void 0,""].includes(t)},mergeData:function(t,e){var a=this;t.Factory_List.forEach((function(t,e){t.Is_External||a.factoryList.push(t)}));var i=[],n=t.Base_Setting,r=t.External_Factory_List,s=t.Factory_Process_Setting_List,c=e.Base_Setting,o=e.External_Factory_List,u=e.Factory_Process_Setting_List,l=["Product_Output_Tax_Rate","Labor_Output_Tax_Rate","Contract_Unit_Price","Loss_Rate","Material_Allocate_Unit_Price","Management_Fee_Self_Supplying","Process_Allocate_Unit_Price","Difficulty_Coefficient","Manage_Fee_Coefficient","Cost_Amorization_Coefficient","Stock_Amorization_Coefficient"];return n.forEach((function(t,e){var r=t.Factory_Id,s={};l.forEach((function(t){var e=c.find((function(t){return t.Factory_Id===r})),i=n.find((function(t){return t.Factory_Id===r}));!e||a.isEmpty(e[t])?(s[t]=i[t],s["is".concat(t,"Default")]=!0):s[t]=e[t]})),s.Factory_Id=r,i.push(s)})),u.forEach((function(t){if(a.isEmpty(t.Unit_Price)){var e=s.find((function(e){return e.Process_Id===t.Process_Id}));e&&(t.Unit_Price=e.Unit_Price,t["isDefault"]=!0)}})),o.forEach((function(t,e){if(a.isEmpty(t.Material_Unit_Price)){var i=r.find((function(e){return e.Factory_Id===t.Factory_Id}));i&&(t.Material_Unit_Price=i.Material_Unit_Price,t["isDefault1"]=!0)}if(a.isEmpty(t.Labor_Unit_Price)){var n=r.find((function(e){return e.Factory_Id===t.Factory_Id}));n&&(t.Labor_Unit_Price=n.Labor_Unit_Price,t["isDefault2"]=!0)}})),{resultBase:i,pFactory:u,pExtrnal:o}},resetForm:function(){var t={Keywords:"",ProjectStatus:"",StartDate:"",EndDate:"",IsConfigured:""};Object.assign(this.form,t)},checkDefaultChange:function(){return window.confirm("恢复默认值后未保存, 是否放弃当前修改?")},handleProjectClick:function(t){var e=this;return(0,o.default)((0,c.default)().m((function a(){return(0,c.default)().w((function(a){while(1)switch(a.n){case 0:if(!e.isResetDefault){a.n=2;break}if(!e.checkDefaultChange()){a.n=1;break}e.isResetDefault=!1,a.n=2;break;case 1:return a.a(2);case 2:return e.isResetDefault=!1,e.curProject=t.Sys_Project_Id,e.curProjectName=t.Short_Name,e.activeName="",a.n=3,e.fetchData(!0);case 3:return a.a(2)}}),a)})))()},handleTabClick:function(t){this.filterProcess(),this.filterOutProcess(),this.filterBaseSetting()},inputChange:function(t){if(["Material_Allocate_Unit_Price","Loss_Rate","Process_Allocate_Unit_Price"].includes(t.code)){var e=this.baseKeyList[this.activeName],a=e.findIndex((function(t){return"Produce_Allocate_Unit_Price"===t.code}));if(-1!==a){var i=e[a],n=e.find((function(t){return"Material_Allocate_Unit_Price"===t.code})).value,r=e.find((function(t){return"Loss_Rate"===t.code})).value,s=e.find((function(t){return"Process_Allocate_Unit_Price"===t.code})).value;i.value=this.calcProducePrice({Material_Allocate_Unit_Price:n,Loss_Rate:r,Process_Allocate_Unit_Price:s}),this.$set(e,a,i)}}},getProjectList:function(){var t=this;if(this.isResetDefault){if(!this.checkDefaultChange())return;this.isResetDefault=!1}(0,f.GetProjectListForAccounting)(this.form).then((function(e){if(e.IsSucceed){if(t.projectList=e.Data,t.isSave)return void(t.isSave=!1);if(!t.projectList.length)return t.activeName="",void(t.curProject="");var a=t.projectList.find((function(t){return!t.Is_Configured}));if(a)t.curProject=a.Sys_Project_Id,t.curProjectName=a.Short_Name;else{var i=t.projectList[0];t.curProject=i.Sys_Project_Id,t.curProjectName=i.Short_Name}t.activeName="",t.fetchData(!0)}else t.$message({message:e.Message,type:"error"})}))},handleDefaultSetting:function(){this.$router.push({name:"PROProjectAccountingDefaultSetting"})},getFactory:function(){var t=this;return(0,o.default)((0,c.default)().m((function e(){var a,i;return(0,c.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,m.GetQueryNonExternalFactory)({});case 1:a=e.v,a.IsSucceed?(i=a.Data,i?t.fc=a.Data:(t.$message({message:"没有工厂权限",type:"warning"}),t.fc=[])):t.$message({message:a.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},handleSave:function(){var t=this;this.$confirm("是否保存当前数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=[],a=!0;if(t.factoryList.forEach((function(i,n){if(t.processFactoryList.includes(i.Id)){var r={Factory_Id:i.Id},s=t.baseKeyList[i.Id];s.forEach((function(e){["Product_Output_Tax_Rate","Labor_Output_Tax_Rate","Loss_Rate"].includes(e.code)?r[e.code]=(0,p.default)(e.value||0).divide(100).value():r[e.code]=e.value,t.isEmpty(r[e.code])&&!["Produce_Allocate_Unit_Price","Cost_Amorization_Coefficient","Stock_Amorization_Coefficient","Contract_Unit_Price"].includes(e.code)&&(a=!1)})),e.push(r)}})),a){var i=[].concat((0,r.default)(t.processOutList),(0,r.default)(t.processList)).map((function(e){var i=e.curItem,r=e.value2;return t.isEmpty(r)&&(a=!1),(0,n.default)((0,n.default)({},i),{},{Unit_Price:r})}));if(a){var s=t.outFactoryList.map((function(e){var i=e.curItem,r=e.value2,s=e.value3;return(t.isEmpty(r)||t.isEmpty(s))&&(a=!1),(0,n.default)((0,n.default)({},i),{},{Material_Unit_Price:r,Labor_Unit_Price:s})}));if(a){var c={Base_Setting:e,Factory_Process_Setting_List:i,External_Factory_List:s,Sys_Project_Id:t.curProject};(0,f.SaveProjectAccountingSetting)(c).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.isResetDefault=!1,t.isSave=!0,t.getProjectList()):t.$message({message:e.Message,type:"error"})}))}else t.$message({message:"请将外协工厂配置项填写完整!",type:"warning"})}else t.$message({message:"请将生产配置项填写完整!",type:"warning"})}else t.$message({message:"请将基础配置项填写完整!",type:"warning"})})).catch((function(){t.$message({type:"info",message:"已取消"})}))}}}},a70c8:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");e.default={props:{width:{type:String,default:"30%"},labelWidth:{type:String,default:"180px"},label:{type:String,default:""},code:{type:String,default:""},value:{type:[String,Number],default:""},disabled:{type:Boolean,default:!1},isDefault:{type:Boolean,default:!1}},data:function(){return{editing:!1,inputValue:""}},computed:{curVal:{get:function(){return this.value},set:function(t){this.$emit("update:value",t)}}},methods:{showInput:function(){var t=this;this.disabled||(this.editing=!0,this.inputValue=this.value,this.$nextTick((function(){t.$refs.input.focus()})))},saveInput:function(){void 0===this.inputValue&&(this.inputValue=0),this.editing=!1,this.curVal=this.inputValue,this.$emit("change",{code:this.code,label:this.label,value:this.value})}}}},b09f:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");e.default={props:{type:{type:Number,default:1}},computed:{text:function(){return 1===this.type?"该项目暂未完善加工工厂信息，请先在项目基础信息中添加加工工厂！":"该工厂暂未完善工序信息，请先工厂中添加工序信息！"}}}},b5cb:function(t,e,a){"use strict";a.r(e);var i=a("6268"),n=a("0457");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("7bdd");var s=a("2877"),c=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"d82fdf8e",null);e["default"]=c.exports},ccb5:function(t,e,a){"use strict";function i(t,e){if(null==t)return{};var a={};for(var i in t)if({}.hasOwnProperty.call(t,i)){if(-1!==e.indexOf(i))continue;a[i]=t[i]}return a}Object.defineProperty(e,"__esModule",{value:!0}),e.default=i},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,a("d3b7");var i=a("6186");function n(t){return new Promise((function(e,a){(0,i.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},d194:function(t,e,a){"use strict";a("175a")},db0a:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DailyBatch=l,e.GetAccountingDefaultSetting=s,e.GetProjectAccountingSettingDetail=c,e.GetProjectListForAccounting=r,e.SaveDefaultAccountingSetting=u,e.SaveProjectAccountingSetting=o;var n=i(a("b775"));function r(t){return(0,n.default)({url:"/oma/AccountingSetting/GetProjectListForAccounting",method:"post",data:t})}function s(t){return(0,n.default)({url:"/oma/AccountingSetting/GetAccountingDefaultSetting",method:"post",data:t})}function c(t){return(0,n.default)({url:"/oma/AccountingSetting/GetProjectAccountingSettingDetail",method:"post",data:t})}function o(t){return(0,n.default)({url:"/oma/AccountingSetting/SaveProjectAccountingSetting",method:"post",data:t})}function u(t){return(0,n.default)({url:"/oma/AccountingSetting/SaveDefaultAccountingSetting",method:"post",data:t})}function l(t){return(0,n.default)({url:"/oma/AccountingSetting/DailyBatch",method:"post",data:t})}}}]);