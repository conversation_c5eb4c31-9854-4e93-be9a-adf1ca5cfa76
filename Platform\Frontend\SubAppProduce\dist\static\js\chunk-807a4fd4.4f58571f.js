(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-807a4fd4"],{"1fd1":function(e,t,a){"use strict";a("b594")},"4d7a6":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("5530")),o=n(a("c14f")),r=n(a("1da1"));a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");var u=a("aa50"),s=n(a("5f52"));t.default={components:{},mixins:[s.default],data:function(){return{RowName:"",TotalBeNum:0,dialogVisible:!1,formInline:{},queryInfo:{Page:1,PageSize:20},total:0,tbLoading:!1,tbData:[],columns:[],rowItem:[],rowIndex:0,RawPickNo:"",BeDeliveryCount:0,PageType:1}},computed:{TotalNum:function(){if(this.tbData.length>0){var e=0;return this.tbData.forEach((function(t){e+=Number(t.Num)})),e}return 0},comList:function(){return 2==this.PageType?this.columns.filter((function(e){return"Brand"!==e.Code&&"FumaceBatchNo"!==e.Code})):this.columns}},created:function(){},mounted:function(){},methods:{changeInput:function(e){e.Num=""==e.Num?0:e.Num},handleOpen:function(e,t,a){var n=this;return(0,r.default)((0,o.default)().m((function i(){return(0,o.default)().w((function(i){while(1)switch(i.n){case 0:return n.RawPickNo=t.Id,n.RowName=t.RawName,n.BeDeliveryCount=t.BeDeliveryCount,i.n=1,n.getTableConfig("PROMaterialOutbound");case 1:if(n.PageType=a,1!==e){i.n=3;break}return i.n=2,n.fetchData(t.Id);case 2:i.n=4;break;case 3:n.tbData=t.Stock;case 4:n.dialogVisible=!0;case 5:return i.a(2)}}),i)})))()},fetchData:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return n=1==t.PageType?{RawPickNo:e}:{AuxPickNo:e},r=1==t.PageType?u.GetDeliveryRawStock:u.GetDeliveryAuxStock,a.n=1,r((0,i.default)({},n)).then((function(e){e.IsSucceed?t.tbData=e.Data.Data.map((function(e){return e.Num=0,e})):t.$message.error(e.Message)})).finally((function(e){t.pgLoading=!1}));case 1:return a.a(2)}}),a)})))()},submit:function(){this.TotalNum>this.BeDeliveryCount?this.$message.error("合计发放数量不能大于大发放数量"):(this.tbData.map((function(e){return e.Num=parseInt(e.Num),e})),this.$emit("selectList",this.tbData,this.RawPickNo,this.TotalNum),this.dialogVisible=!1)},handleClose:function(){this.dialogVisible=!1}}}},5591:function(e,t,a){"use strict";a.r(t);var n=a("4d7a6"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"62af":function(e,t,a){},"7f16":function(e,t,a){"use strict";a.r(t);var n=a("8d18"),i=a("a6b0");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("a59f");var r=a("2877"),u=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"00700dab",null);t["default"]=u.exports},"8d18":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card h100"},[a("h3",[e._v("待发料明细")]),a("div",{staticClass:"main_wapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","max-height":"100%",align:"left",stripe:"",data:e.tbData,resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u([{key:"default",fn:function(n){var i=n.row;return[0!=i[t.Code]?a("span",[e._v(e._s(i[t.Code]||"-"))]):a("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}}],null,!0)})})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"150","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDialog(n)}}},[e._v(e._s(1===e.PageType?"原料":"辅料")+"发放")])]}}])})],2)],1),a("div",{staticClass:"buttonGroup"},[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handelSubmit()}}},[e._v("提交")])],1)]),a("outDialog",{ref:"outDialog",on:{selectList:e.selectList}})],1)},i=[]},"9da0":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:1==e.PageType?"原料出库":"辅料出库",visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"60%","destroy-on-close":"","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{staticClass:"title_wapper"},[a("h3",[e._v(e._s(e.RowName))]),a("h3",[e._v("待发放数量："+e._s(e.BeDeliveryCount))])]),a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"edit-config":{trigger:"click",mode:"cell"},align:"left",height:"350","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.comList,(function(t,n){return["Num"===t.Code?a("vxe-column",{key:n,attrs:{field:t.Code,title:t.Display_Name,"edit-render":{},"min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",max:e.BeDeliveryCount,min:0},on:{change:function(t){return e.changeInput(n)}},model:{value:n.Num,callback:function(t){e.$set(n,"Num",t)},expression:"row.Num"}})]}},{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.Num)+" "),a("i",{staticClass:"el-icon-edit"})]}}],null,!0)}):a("vxe-column",{key:n,attrs:{field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width}})]}))],2),a("div",{staticClass:"total_wapper"},[a("h2",[e._v("合计发放数量:"+e._s(e.TotalNum))])]),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},i=[]},a59f:function(e,t,a){"use strict";a("62af")},a6b0:function(e,t,a){"use strict";a.r(t);var n=a("c0e21"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},b594:function(e,t,a){},c0e21:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var i=n(a("c14f")),o=n(a("1da1")),r=a("aa50"),u=n(a("f7cd")),s=n(a("5f52")),l=a("ed08");t.default={components:{outDialog:u.default},mixins:[s.default],data:function(){return{pgLoading:!1,tbData:[],columns:[],PickNo:"",PageType:1}},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.PickNo=e.$route.query.info,e.PageType=Number(e.$route.query.PageType),t.n=1,e.getTableConfig(1==e.PageType?"PROTreadMaterialsDetail":"PROTreadAuxDetail");case 1:return t.n=2,e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{closeView:function(){(0,l.closeTagView)(this.$store,this.$route)},handleDialog:function(e){0===e.BeDeliveryCount?this.$message.error("待发放数量不足"):0===e.Stock.length?this.$refs.outDialog.handleOpen(1,e,this.PageType):this.$refs.outDialog.handleOpen(2,e,this.PageType)},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a=1==e.PageType?r.GetRawDeliveryList:r.GetAuxDeliveryList,t.n=1,a({PickNo:e.PickNo}).then((function(t){t.IsSucceed?e.tbData=t.Data.Data.map((function(e){return e.BeDeliveryCount=e.PickingCount-e.DeliveryCount,e.CurrentCount=0,e.Stock=[],e})):e.$message.error(t.Message)})).finally((function(t){e.pgLoading=!1}));case 1:return t.a(2)}}),t)})))()},selectList:function(e,t,a){this.tbData.map((function(n){if(n.Id===t)return n.Stock=e,n.CurrentCount=a,n}))},handelSubmit:function(){var e=this,t=1==this.PageType?r.DeliveryRawStock:r.AuxPickDelivery,a=JSON.parse(JSON.stringify(this.tbData));a.map((function(e){return e.RawPickNo=e.Id,e.Num=e.CurrentCount,e})),t(a).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"提交成功!"}),e.closeView()):e.$message.error(t.Message)}))}}}},f7cd:function(e,t,a){"use strict";a.r(t);var n=a("9da0"),i=a("5591");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("1fd1");var r=a("2877"),u=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"c3ed31e2",null);t["default"]=u.exports}}]);