(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-587dcd01"],{"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,n){var i=r(),l=t-i,s=20,u=0;e="undefined"===typeof e?500:e;var d=function(){u+=s;var t=Math.easeInOutQuad(u,i,l,e);o(t),u<e?a(d):n&&"function"===typeof n&&n()};d()}},"11d8":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",[n("span",{staticClass:"cs-title"},[t._v(" 可撤回数量："+t._s(t.count)+" 可撤回重量： "+t._s(t.weight)+"（t） ")])]),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:t.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.fTable,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[n("vxe-column",{key:e.Code,attrs:{"min-width":e.Width,width:"auto",fixed:"Task_Code"===e.Code?"left":"","show-overflow":"tooltip",sortable:"",align:e.Align,field:e.Code,title:e.Display_Name},scopedSlots:t._u(["Count"===e.Code?{key:"default",fn:function(e){var a=e.row;return[n("vxe-input",{attrs:{min:"0",max:a.Operate_Count,placeholder:"请输入",type:"integer"},on:{change:t.change},model:{value:a.Count,callback:function(e){t.$set(a,"Count",t._n(e))},expression:"row.Count"}})]}}:"Comp_Code"===e.Code?{key:"default",fn:function(a){var o=a.row;return[o.DwgCount>0?n("el-link",{attrs:{type:"primary"},on:{click:function(e){return e.stopPropagation(),t.handleDwg(o)}}},[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))]):n("span",[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))])]}}:"Part_Code"===e.Code?{key:"default",fn:function(a){var o=a.row;return[o.DwgCount>0?n("el-link",{attrs:{type:"primary"},on:{click:function(e){return e.stopPropagation(),t.handleDwg(o)}}},[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))]):n("span",[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))])]}}:{key:"default",fn:function(a){var o=a.row;return[n("span",[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))])]}}],null,!0)})]}))],2)],1),n("Pagination",{attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.handlePageChange}}),n("footer",[n("el-button",{on:{click:t.onCancel}},[t._v("取消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading,disabled:!t.fTable.length||0===t.count},on:{click:t.submit}},[t._v("确定")])],1)],1)},o=[]},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,i=t.Data,l=t.Message;if(a){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),s=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(e.columns)}else e.$message({message:l,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===e){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=d,e.GeAreaTrees=T,e.GetFileSync=O,e.GetInstallUnitIdNameList=I,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=C,e.GetProjectAreaTreeList=v,e.GetProjectEntity=s,e.GetProjectList=l,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=_,e.GetSchedulingPartList=L,e.IsEnableProjectMonomer=c,e.SaveProject=u,e.UpdateProjectTemplateBase=g,e.UpdateProjectTemplateContacts=P,e.UpdateProjectTemplateContract=y,e.UpdateProjectTemplateOther=b;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function u(t){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function c(t){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function O(t){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},3359:function(t,e,n){},5816:function(t,e,n){"use strict";n.r(e);var a=n("11d8"),o=n("a06a");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("ebb2");var i=n("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"16df4396",null);e["default"]=l.exports},"69c3":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("fb6a"),n("e9f5"),n("910d"),n("7d54"),n("ab43"),n("b680"),n("d3b7"),n("159b");var o=a(n("c14f")),r=a(n("1da1")),i=a(n("333d")),l=n("c685"),s=n("6186"),u=n("7f9d");e.default={components:{Pagination:i.default},inject:["pageType"],data:function(){return{queryInfo:{Page:1,PageSize:l.tablePageSize[0]},tablePageSize:l.tablePageSize,btnLoading:!1,tbLoading:!1,fTable:[],total:0,count:0,weight:0,columns:[]}},mounted:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig("PROReportHistoryWithdraw");case 1:return e.a(2)}}),e)})))()},methods:{handlePageChange:function(t){var e=t.page,n=t.limit;this.tbLoading||(this.queryInfo.Page=e,this.queryInfo.PageSize=n,this.setPage())},setPage:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.tbData;this.fTable=t.slice((this.queryInfo.Page-1)*this.queryInfo.PageSize,this.queryInfo.Page*this.queryInfo.PageSize)},onCancel:function(){this.$emit("close")},init:function(t){this.tbData=t.map((function(t){return t.Count=t.Operate_Count||0,t})),this.total=t.length,this.change(),this.setPage()},getTableConfig:function(t){var e=this;return new Promise((function(n){(0,s.GetGridByCode)({code:t}).then((function(t){var a=t.IsSucceed,o=t.Data,r=t.Message;if(a){if(!o)return void e.$message({message:"表格配置不存在",type:"error"});e.tbConfig=Object.assign({},e.tbConfig,o.Grid),e.columns=(o.ColumnList.filter((function(t){return t.Is_Display}))||[]).map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t})),e.columns="com"===e.pageType?e.columns.filter((function(t){return"Part_Code"!==t.Code})):e.columns.filter((function(t){return"Comp_Code"!==t.Code})),e.queryInfo.PageSize=+o.Grid.Row_Number||l.tablePageSize[0],n(e.columns)}else e.$message({message:r,type:"error"})}))}))},change:function(){var t=0,e=0;this.tbData.forEach((function(n,a){t+=n.Count,e+=(n.Count||0)*((null===n||void 0===n?void 0:n.Weight)||0)/1e3})),this.count=t,this.weight=e.toFixed(5)/1},submit:function(){var t=this;this.$confirm("是否撤回以上数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.btnLoading=!0;var e=t.tbData.map((function(t){return{Apply_Detail_Id:t.Id,Count:t.Count}})).filter((function(t){return t.Count}));(0,u.BatchWithdrawSimplifiedProcessingHistory)(e).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.$emit("refresh"),t.$emit("close")):t.$message({message:e.Message,type:"error"}),t.btnLoading=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleDwg:function(t){var e=this;(0,u.GetDwg)({Task_Id:t.Pre_Task_Id}).then((function(t){if(t.IsSucceed){var n,a=(null===t||void 0===t||null===(n=t.Data)||void 0===n?void 0:n.length)&&t.Data[0].File_Url;window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+parseOssUrl(a),"_blank")}else e.$message({message:t.Message,type:"error"})}))}}}},"6b42":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("7db0"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("b64b"),n("d3b7"),n("25f0"),n("159b");var o=a(n("5530")),r=a(n("c14f")),i=a(n("1da1")),l=n("c685"),s=a(n("15ac")),u=a(n("333d")),d=n("3166"),c=n("f2f6"),f=n("a024"),m=n("7f9d"),p=n("8975"),h=a(n("5816")),g=n("0e9a"),P=n("ed08");e.default={name:"PROReportHistoryHome",components:{Pagination:u.default,Withdraw:h.default},mixins:[s.default],inject:["pageType"],data:function(){return{form:{Code_Like:"",Working_Team_Id:"",Sys_Project_Id:"",Area_Id:"",InstallUnit_Id:"",End_Date:"",Begin_Date:"",Type:2},tablePageSize:l.tablePageSize,queryInfo:{Page:1,PageSize:l.tablePageSize[0]},teamOptions:[],tbData:[],columns:[],projectOption:[],installOption:[],exportLoading:!1,tbLoading:!1,tbConfig:{},total:0,treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},dialogVisible:!1,title:"",dWidth:"80%",currentComponent:"",multipleSelection:[],summaryInfo:{Actual_Transfer_Weight:0,Actual_Transfer_Count:0}}},computed:{isCom:function(){return"com"===this.pageType},planTime:{get:function(){return[(0,p.timeFormat)(this.form.Begin_Date),(0,p.timeFormat)(this.form.End_Date)]},set:function(t){if(t){var e=t[0],n=t[1];this.form.Begin_Date=(0,p.timeFormat)(e),this.form.End_Date=(0,p.timeFormat)(n)}else this.form.Begin_Date="",this.form.End_Date=""}}},mounted:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig(t.isCom?"PROReportHistoryConfig":"PROReportHistoryPartConfig");case 1:return e.n=2,t.getProcessTeam();case 2:t.fetchData(1),t.getSummary(),t.getProjectOption();case 3:return e.a(2)}}),e)})))()},methods:{refresh:function(){this.getSummary(),this.fetchData(1)},multiSelectedChange:function(t){this.multipleSelection=t.records},getProcessedForm:function(){var t=(0,o.default)((0,o.default)({},this.form),this.queryInfo);return"all"===t.Working_Team_Id&&(t.Working_Team_Id=this.teamOptions.filter((function(t){return"all"!==t.value})).map((function(t){return t.value})).toString()),t.Type=this.isCom?2:1,t},getSummary:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){var n;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,n=t.getProcessedForm(),e.n=1,(0,m.GetSimplifiedProcessingSummary)(n).then((function(e){Object.assign(t.summaryInfo,(null===e||void 0===e?void 0:e.Data)||{})}));case 1:e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}}),e,null,[[0,2]])})))()},getStopList:function(t){var e=this;return(0,i.default)((0,r.default)().m((function n(){var a,o;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return a=e.isCom?"Comp_Id":"Comp_Part_Id",o=t.map((function(t){return{Id:t[a],Type:e.isCom?2:1}})),n.n=1,(0,m.GetStopList)(o).then((function(n){if(n.IsSucceed){var o={};n.Data.forEach((function(t){o[t.Id]=!!t.Is_Stop})),t.forEach((function(t){o[t[a]]&&e.$set(t,"stopFlag",o[t[a]])}))}}));case 1:return n.a(2)}}),n)})))()},fetchData:function(t){var e=this;return(0,i.default)((0,r.default)().m((function n(){var a,i,l;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return t&&(e.queryInfo.Page=t),a=e.getProcessedForm(),e.tbLoading=!0,n.p=1,n.n=2,(0,m.GetSimplifiedProcessingHistory)(a);case 2:i=n.v,i.IsSucceed?(e.tbData=((null===i||void 0===i||null===(l=i.Data)||void 0===l?void 0:l.Data)||[]).map((function(t){return(0,o.default)((0,o.default)({},t),{},{checked:!1})})),e.total=i.Data.TotalCount,e.getStopList(e.tbData)):e.$message({message:i.Message,type:"error"}),n.n=4;break;case 3:n.p=3,n.v;case 4:return n.p=4,e.tbLoading=!1,n.f(4);case 5:return n.a(2)}}),n,null,[[1,3,4,5]])})))()},checCheckboxkMethod3:function(t){var e=t.row;return!e.stopFlag},projectChange:function(){this.form.Area_Id="",this.form.InstallUnit_Id="",this.form.Sys_Project_Id&&this.getAreaList()},areaClear:function(){this.form.InstallUnit_Id=""},getProcessTeam:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,f.GetTeamListByUser)({type:t.isCom?1:2}).then((function(e){t.teamOptions=e.Data.map((function(t){return{value:t.Id,label:t.Name}})),t.teamOptions.unshift({value:"all",label:"全部"}),t.teamOptions.length&&(t.form.Working_Team_Id=t.teamOptions[0].value)}));case 1:return e.a(2)}}),e)})))()},getAreaList:function(){var t=this,e=this.projectOption.find((function(e){return e.Sys_Project_Id===t.form.Sys_Project_Id}));(0,d.GeAreaTrees)({projectId:e.Id,Area_Id:this.form.Area_Id}).then((function(e){if(e.IsSucceed){var n=e.Data;t.treeParams.data=n,t.$nextTick((function(e){t.$refs.treeSelect.treeDataUpdateFun(n)}))}else t.$message({message:e.Message,type:"error"})}))},areaChange:function(t){this.form.InstallUnit_Id="",this.getInstall()},getInstall:function(){var t=this;(0,c.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.installOption=e.Data.Data.filter((function(t){return t.Id})):t.$message({message:e.Message,type:"error"})}))},handleClose:function(){this.dialogVisible=!1},handleDwg:function(t){var e=this;(0,m.GetDwg)({Task_Id:t.Pre_Task_Id}).then((function(t){if(t.IsSucceed){var n,a=(null===t||void 0===t||null===(n=t.Data)||void 0===n?void 0:n.length)&&t.Data[0].File_Url;window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+(0,g.parseOssUrl)(a),"_blank")}else e.$message({message:t.Message,type:"error"})}))},getProjectOption:function(){var t=this;(0,d.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.projectOption=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},handleReset:function(){this.$refs["form"].resetFields(),this.planTime="",this.teamOptions.length&&(this.form.Working_Team_Id=this.teamOptions[0].value),this.fetchData(1),this.getSummary()},handleWithdraw:function(){var t=this;this.title="撤回",this.currentComponent="Withdraw",this.dialogVisible=!0,this.$nextTick((function(e){var n=t.multipleSelection.filter((function(t){return t.Operate_Count>0}));t.$refs["content"].init(JSON.parse(JSON.stringify(n)))}))},handleExport:function(){var t=this;this.exportLoading=!0,this.form.Type=this.isCom?2:1;var e=(0,o.default)((0,o.default)({},this.form),this.queryInfo);if("all"===e.Working_Team_Id){var n=this.teamOptions.map((function(t){return t.value})).filter((function(t){return"all"!==t})).toString();e.Working_Team_Id=n}this.multipleSelection.length&&(e.Ids=this.multipleSelection.map((function(t){return t.Id})).toString()),(0,m.ExportSimplifiedProcessingHistory)(e).then((function(e){e.IsSucceed?window.open((0,P.combineURL)(t.$baseUrl,e.Data),"_blank"):t.$message({message:e.Message,type:"error"}),t.exportLoading=!1}))}}}},"751c":function(t,e,n){"use strict";n.r(e);var a=n("e84b"),o=n("76d0");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("9aa7f");var i=n("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"16afc746",null);e["default"]=l.exports},"76d0":function(t,e,n){"use strict";n.r(e);var a=n("6b42"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"9aa7f":function(t,e,n){"use strict";n("3359")},a024:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProcessFlow=u,e.AddProessLib=U,e.AddTechnology=s,e.AddWorkingProcess=l,e.DelLib=W,e.DeleteProcess=T,e.DeleteProcessFlow=v,e.DeleteTechnology=I,e.DeleteWorkingTeams=w,e.GetAllProcessList=f,e.GetCheckGroupList=D,e.GetChildComponentTypeList=x,e.GetFactoryAllProcessList=m,e.GetFactoryPeoplelist=R,e.GetFactoryWorkingTeam=P,e.GetGroupItemsList=_,e.GetLibList=i,e.GetLibListType=A,e.GetProcessFlow=p,e.GetProcessFlowListWithTechnology=h,e.GetProcessList=d,e.GetProcessListBase=c,e.GetProcessListTeamBase=k,e.GetProcessListWithUserBase=j,e.GetProcessWorkingTeamBase=F,e.GetTeamListByUser=z,e.GetTeamProcessList=b,e.GetWorkingTeam=y,e.GetWorkingTeamBase=G,e.GetWorkingTeamInfo=S,e.GetWorkingTeams=C,e.GetWorkingTeamsPageList=L,e.SaveWorkingTeams=O,e.UpdateProcessTeam=g;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(t)})}function s(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(t)})}function u(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(t)})}function c(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(t)})}function f(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(t)})}function m(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(t)})}function h(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(t)})}function P(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(t)})}function b(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(t)})}function _(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(t)})}function v(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(t)})}function I(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(t)})}function T(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(t)})}function G(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(t)})}function k(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(t)})}function j(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:t})}function A(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:t})}function z(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}},a06a:function(t,e,n){"use strict";n.r(e);var a=n("69c3"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},e84b:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[n("div",{staticClass:"cs-z-page-main-content",staticStyle:{padding:"0"}},[n("div",{staticClass:"form-x"},[n("el-form",{ref:"form",attrs:{inline:"",model:t.form,"label-width":"80px"}},[n("el-form-item",{attrs:{label:t.isCom?"构件名称":"零件名称",prop:"Code_Like"}},[n("el-input",{staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"请输入"},model:{value:t.form.Code_Like,callback:function(e){t.$set(t.form,"Code_Like",e)},expression:"form.Code_Like"}})],1),n("el-form-item",{attrs:{label:"项目",prop:"Sys_Project_Id","label-width":"50px"}},[n("el-select",{staticStyle:{width:"250px"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}},t._l(t.projectOption,(function(t){return n("el-option",{key:t.Sys_Project_Id,attrs:{label:t.Short_Name,value:t.Sys_Project_Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域",prop:"Area_Id","label-width":"50px"}},[n("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",staticStyle:{width:"250px"},attrs:{disabled:!t.form.Sys_Project_Id,"select-params":{clearable:!0},"tree-params":t.treeParams},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1),n("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id","label-width":"50px"}},[n("el-select",{staticStyle:{width:"250px"},attrs:{disabled:!t.form.Area_Id,clearable:"",placeholder:"请选择"},model:{value:t.form.InstallUnit_Id,callback:function(e){t.$set(t.form,"InstallUnit_Id",e)},expression:"form.InstallUnit_Id"}},t._l(t.installOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"我的班组",prop:"Working_Team_Id"}},[n("el-select",{staticStyle:{width:"250px"},attrs:{placeholder:"请选择"},model:{value:t.form.Working_Team_Id,callback:function(e){t.$set(t.form,"Working_Team_Id",e)},expression:"form.Working_Team_Id"}},t._l(t.teamOptions,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),n("el-form-item",{attrs:{label:"报工时间"}},[n("el-date-picker",{staticStyle:{width:"315px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.planTime,callback:function(e){t.planTime=e},expression:"planTime"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.fetchData(1),t.getSummary()}}},[t._v("搜索")]),n("el-button",{attrs:{type:""},on:{click:t.handleReset}},[t._v("重置")])],1)],1)],1),n("div",{staticClass:"cs-main"},[n("div",{staticClass:"btn-x"},[n("div",[n("el-button",{attrs:{disabled:!t.multipleSelection.length,type:"primary"},on:{click:t.handleWithdraw}},[t._v("撤回")]),n("el-button",{attrs:{loading:t.exportLoading,type:"success"},on:{click:t.handleExport}},[t._v("导出")])],1),n("div",{staticClass:"summary-info"},[n("span",[t._v("实际报工总重(t)：")]),n("span",[t._v(t._s(t.summaryInfo.Actual_Transfer_Weight))])])]),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:t.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","checkbox-config":{trigger:"row",checkMethod:t.checCheckboxkMethod3},"tooltip-config":{enterable:!0}},on:{"checkbox-all":t.multiSelectedChange,"checkbox-change":t.multiSelectedChange}},[t._v(" > "),n("vxe-column",{attrs:{fixed:"left",type:"checkbox"}}),t._l(t.columns,(function(e){return[n("vxe-column",{key:e.Code,attrs:{"min-width":e.Width,width:"auto",fixed:"Task_Code"===e.Code?"left":"","show-overflow":"tooltip",sortable:"",align:e.Align,field:e.Code,title:e.Display_Name},scopedSlots:t._u(["Comp_Code"===e.Code||"Part_Code"===e.Code?{key:"default",fn:function(a){var o=a.row;return[o.stopFlag?n("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[t._v("停")]):t._e(),o.DwgCount>0?n("el-link",{attrs:{type:"primary"},on:{click:function(e){return e.stopPropagation(),t.handleDwg(o)}}},[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))]):n("span",[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))])]}}:null],null,!0)})]}))],2)],1),n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[t._v("已选 "+t._s(t.multipleSelection.length)+" 条数据 ")]),n("Pagination",{attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)]),t.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.dWidth,top:"10vh"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:t.refresh}})],1):t._e()],1)])},o=[]},e985:function(t,e,n){},ebb2:function(t,e,n){"use strict";n("e985")},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=s,e.CheckPlanTime=u,e.DeleteInstallUnit=m,e.GetCompletePercent=y,e.GetEntity=_,e.GetInstallUnitAllInfo=c,e.GetInstallUnitComponentPageList=P,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=d,e.GetInstallUnitList=l,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=b,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=v;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function u(t){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function d(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function c(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function _(t){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function v(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);