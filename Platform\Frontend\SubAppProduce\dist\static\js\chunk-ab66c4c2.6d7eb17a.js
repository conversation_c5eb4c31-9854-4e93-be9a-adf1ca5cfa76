(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ab66c4c2"],{"064d":function(e,t,a){"use strict";a.r(t);var n=a("63f1"),i=a("45a1");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("da7c");var o=a("2877"),u=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"0d70e04d",null);t["default"]=u.exports},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=o,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=r(),u=e-o,l=20,c=0;t="undefined"===typeof t?500:t;var s=function(){c+=l;var e=Math.easeInOutQuad(c,o,u,t);i(e),c<t?n(s):a&&"function"===typeof a&&a()};s()}},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),i=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,o=e.Data,u=e.Message;if(n){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,o.Grid),l=a?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+o.Grid.Row_Number||i.tablePageSize[0]),r(t.columns)}else t.$message({message:u,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var i=this.columns[n];if(i.Code===t){a.Type=i.Type,a.Filter_Type=i.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"45a1":function(e,t,a){"use strict";a.r(t);var n=a("adfc"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"63f1":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("el-card",{staticClass:"box-card h100"},[a("div",{staticClass:"search-wrapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{type:"primary",loading:e.btnloading},on:{click:function(t){return e.addMaterialReg()}}},[e._v("新增")])],1),a("div",{staticClass:"form-wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"登记日期",prop:"Register_Date"}},[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","picker-options":e.pickerOptions},model:{value:e.form.Register_Date,callback:function(t){e.$set(e.form,"Register_Date",t)},expression:"form.Register_Date"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.fetchData()}}},[e._v("重置")])],1)],1)],1)]),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"",align:t.Align},scopedSlots:e._u(["Register_Date"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d}"))+" ")]}}:"Create_Date"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}:"Modify_Date"===t.Code?{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(n[t.Code]||"-")+" ")]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{disabled:e.isCalculate(n.Register_Date),type:"text"},on:{click:function(t){return e.handleEdit(n)}}},[e._v("编辑")]),a("el-button",{style:{color:e.isCalculate(n.Register_Date)?"#C0C4CC":"red"},attrs:{disabled:e.isCalculate(n.Register_Date),type:"text"},on:{click:function(t){return e.handleDelete(n)}}},[e._v("删除")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleCopyAdd(n)}}},[e._v("复制新增")])]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增物料登记",visible:e.dialogVisible,width:"960px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.closeDialog}},[a("el-form",{ref:"dialogForm",attrs:{model:e.dialogForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"登记日期",prop:"Register_Date",rules:{required:!0,message:"请选择登记日期",trigger:"change"}}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期","picker-options":e.registerDatePickerOptions},model:{value:e.dialogForm.Register_Date,callback:function(t){e.$set(e.dialogForm,"Register_Date",t)},expression:"dialogForm.Register_Date"}})],1)],1),a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleAdd()}}},[e._v("新增")]),a("el-button",{attrs:{type:"danger",disabled:0==e.selectList.length},on:{click:function(t){return e.handleBatchDelete()}}},[e._v("批量删除")])],1),a("div",{staticStyle:{width:"100%",height:"300px"}},[a("vxe-table",{ref:"xTableDialog",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",height:"auto","show-overflow":"",loading:e.dialogTbLoading,stripe:"",size:"medium",data:e.dialogData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.selectAllEvent,"checkbox-change":e.selectChangeEvent}},[a("vxe-column",{attrs:{type:"checkbox",width:"60",fixed:"left",align:"center"}}),a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),a("vxe-column",{attrs:{field:"Materiel_Aux_Name",title:"辅料名称","min-width":"240",align:"center","edit-render":{}},scopedSlots:e._u([{key:"header",fn:function(){return[a("span",[a("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v("辅料名称")])]},proxy:!0},{key:"default",fn:function(t){var n=t.row;return[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},on:{change:function(t){return e.materielAuxChange(t,n)}},model:{value:n.Materiel_Aux_Id,callback:function(t){e.$set(n,"Materiel_Aux_Id",t)},expression:"row.Materiel_Aux_Id"}},e._l(e.materielAuxData,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Name+"+"+e.Spec}})})),1)]}}])}),e._l(e.dialogColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"",align:t.Align},scopedSlots:e._u(["Balance_Amount"===t.Code?{key:"header",fn:function(){return[a("span",[a("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v("盘存量")])]},proxy:!0}:"Unit_Price"===t.Code?{key:"header",fn:function(){return[a("span",[a("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v("含税单价（元）")])]},proxy:!0}:null,"Balance_Amount"===t.Code?{key:"default",fn:function(n){var i=n.row;return[a("vxe-input",{attrs:{type:"text"},on:{input:function(t){return e.inputChange(t,i,"Balance_Amount")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})]}}:"Unit_Price"===t.Code?{key:"default",fn:function(n){var i=n.row;return[a("vxe-input",{attrs:{type:"text"},on:{input:function(t){return e.inputChange(t,i,"Unit_Price")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})]}}:{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(n[t.Code]||0===parseInt(n[t.Code])?n[t.Code]:"-")+" ")]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleBatchDelete(n)}}},[e._v("删除")])]}}])})],2)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.dialogCancel("dialogForm")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dialogConfirm("dialogForm")}}},[e._v("确 定")])],1)],1)],1)},i=[]},a45f3:function(e,t,a){},aa50:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Approve=d,t.AuxPickDelivery=D,t.DeliveryRawStock=m,t.GetAuxDeliveryList=b,t.GetAuxPickDetail=h,t.GetAuxPickList=p,t.GetDeliveryAuxStock=_,t.GetDeliveryRawStock=g,t.GetPickAuxStock=y,t.GetPickRawStock=s,t.GetRawDeliveryList=f,t.GetRawPickDetail=l,t.GetRawPickList=r,t.RawPickDelete=u,t.RawStockSubmit=o,t.SaveAuxPick=v,t.SaveRawPick=c;var i=n(a("b775"));n(a("4328"));function r(e){return(0,i.default)({url:"/PRO/RawPicking/GetRawPickList",method:"post",data:e})}function o(e){return(0,i.default)({url:"/PRO/RawPicking/Submit",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/RawPicking/Delete",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/RawPicking/GetRawPickDetail",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/RawPicking/SaveRawPick",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/RawPicking/GetPickRawStock",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/RawPicking/Approve",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PRO/RawPicking/GetDeliveryList",method:"post",data:e})}function g(e){return(0,i.default)({url:"/PRO/RawPicking/GetDeliveryRawStock",method:"post",data:e})}function m(e){return(0,i.default)({url:"/PRO/RawPicking/Delivery",method:"post",data:e})}function p(e){return(0,i.default)({url:"/PRO/AuxPicking/GetAuxPickList",method:"post",data:e})}function h(e){return(0,i.default)({url:"/PRO/AuxPicking/GetAuxPickDetail",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PRO/AuxPicking/SaveAuxPick",method:"post",data:e})}function y(e){return(0,i.default)({url:"/PRO/AuxPicking/GetPickAuxStock",method:"post",data:e})}function D(e){return(0,i.default)({url:"/PRO/AuxPicking/Delivery",method:"post",data:e})}function _(e){return(0,i.default)({url:"/PRO/AuxPicking/GetDeliveryAuxStock",method:"post",data:e})}function b(e){return(0,i.default)({url:"/PRO/AuxPicking/GetDeliveryList",method:"post",data:e})}},adfc:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("b680"),a("b64b"),a("d3b7"),a("ac1f"),a("2532"),a("5319"),a("159b");var i=n(a("5530")),r=n(a("c14f")),o=n(a("1da1")),u=a("ed08"),l=n(a("15ac")),c=n(a("333d")),s=a("c685"),d=a("e144"),f=a("aa50"),g=a("b76ac");t.default={name:"PROMaterialRegisterWorkshop",components:{Pagination:c.default},mixins:[l.default],data:function(){var e=this;return{dialogVisible:!1,form:{Register_Date:"",StartDate:null,EndDate:null},gridCode:"pro_material_register_workshop_list,Steel",tbLoading:!0,tablePageSize:s.tablePageSize,columns:[],tbData:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1,PageSize:s.tablePageSize[0]},total:0,btnloading:!1,pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},dialogTbLoading:!1,Register_Date_Min:"",registerDatePickerOptions:{disabledDate:function(t){var a=new Date(e.Register_Date_Min);return t.getTime()<a.getTime()}},dialogForm:{Register_Date:new Date},dialogData:[],dialogColumns:[{Code:"Balance_Amount",Display_Name:"盘存量",Align:"center",Width:140},{Code:"Unit_Price",Display_Name:"含税单价（元）",Align:"center",Width:160},{Code:"Total_Price",Display_Name:"含税总价（元）",Align:"center",Width:140,isView:!0}],selectList:[],materielAuxData:[],Id:""}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:return t.n=2,e.fetchData();case 2:return t.n=3,e.getOMALatestStatisticTime();case 3:return t.n=4,e.getAuxPageList();case 4:return t.a(2)}}),t)})))()},activated:function(){},mounted:function(){},methods:{fetchData:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.StartDate=e.form.Register_Date?(0,u.parseTime)(e.form.Register_Date[0],"{y}-{m}-{d}"):null,e.form.EndDate=e.form.Register_Date?(0,u.parseTime)(e.form.Register_Date[1],"{y}-{m}-{d}"):null,t.n=1,(0,g.GetMaterielAuxDailyBalanceList)((0,i.default)((0,i.default)({},e.form),e.queryInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message.error(t.Message),e.tbLoading=!1})).finally((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},addMaterialReg:function(){this.Id="",this.dialogVisible=!0},handleEdit:function(e){var t=this,a=e.Id;this.Id=a,(0,g.MaterielAuxDailyBalanceDetail)({Id:a}).then((function(e){e.IsSucceed&&(t.dialogData=e.Data.DetailList.map((function(e){return e.uuid=(0,d.v4)(),e})),t.dialogForm.Register_Date=e.Data.Register_Date,t.dialogVisible=!0)}))},handleDelete:function(e){var t=this,a=e.Id;this.$confirm("是否删除选中数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,g.MaterielAuxDailyBalanceDelete)({Id:a}).then((function(e){e.IsSucceed&&(t.$message({type:"success",message:"删除成功!"}),t.fetchData())}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleCopyAdd:function(e){var t=this,a=e.Id;this.Id="",(0,g.MaterielAuxDailyBalanceDetail)({Id:a}).then((function(e){e.IsSucceed&&(t.dialogData=e.Data.DetailList.map((function(e){return e.uuid=(0,d.v4)(),delete e.Id,e})),t.dialogForm.Register_Date=e.Data.Register_Date,t.dialogVisible=!0)}))},handleAdd:function(){var e={uuid:(0,d.v4)(),Materiel_Aux_Id:"",Id:"",Code:"",Materiel_Aux_Name:"",Balance_Amount:"",Unit_Price:"",Total_Price:"",Materiel_Aux_Spec:""};this.dialogData.push(e)},handleBatchDelete:function(e){var t=this;this.$confirm("是否删除选中数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(e)t.dialogData=t.dialogData.filter((function(t){return!e.uuid.includes(t.uuid)}));else{var a=[];t.selectList&&t.selectList.forEach((function(e){a.push(e.uuid)})),t.dialogData=t.dialogData.filter((function(e){return!a.includes(e.uuid)}))}t.selectList=[]})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},selectAllEvent:function(e){e.checked;var t=this.$refs.xTableDialog.getCheckboxRecords();this.selectList=t},selectChangeEvent:function(e){e.checked;var t=this.$refs.xTableDialog.getCheckboxRecords();this.selectList=t},getOMALatestStatisticTime:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:(0,g.GetOMALatestStatisticTime)({}).then((function(t){t.IsSucceed&&("0001-01-01 00:00:00"===t.Data?e.Register_Date_Min="2000-01-01":e.Register_Date_Min=t.Data.substring(0,10))}));case 1:return t.a(2)}}),t)})))()},getAuxPageList:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,g.GetAuxPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.materielAuxData=t.Data.Data)}));case 1:return t.a(2)}}),t)})))()},materielAuxChange:function(e,t){var a=JSON.parse(JSON.stringify(this.materielAuxData)),n=a.filter((function(t){return t.Id===e}));t.Materiel_Aux_Name=n[0].Name,t.Materiel_Aux_Spec=n[0].Spec,t.Materiel_Aux_Id=n[0].Id},inputChange:function(e,t,a){var n=this;if("Balance_Amount"===a){var i=this.setdecimals(e.value,6),r=JSON.parse(JSON.stringify(this.dialogData)),o=r.map((function(e){return e.uuid===t.uuid&&(e[a]=i,!e.Unit_Price&&0!==parseInt(e.Unit_Price)||!i&&0!==parseInt(i)?e.Total_Price="":e.Total_Price=(i*e.Unit_Price).toFixed(2)),e}));this.$nextTick((function(){n.dialogData=o}))}if("Unit_Price"===a){var u=this.setdecimals(e.value,2),l=JSON.parse(JSON.stringify(this.dialogData)),c=l.map((function(e){return e.uuid===t.uuid&&(e[a]=u,!e.Balance_Amount&&0!==parseInt(e.Balance_Amount)||!u&&0!==parseInt(u)?e.Total_Price="":e.Total_Price=(u*e.Balance_Amount).toFixed(2)),e}));this.$nextTick((function(){n.dialogData=c}))}},setdecimals:function(e,t){e=e.replace(/[^\d.]/g,""),e=e.replace(/(^|-)0+(?=\d)/g,"$1"),e=e.replace(/^\./g,""),e=e.replace(/\.{2,}/g,".");var a=!1;switch("-"===e.substring(0,1)&&(a=!0),e=e.replace(/-/g,""),e=e.replace(".","$#$").replace(/\./g,"").replace("$#$","."),t){case 1:e=e.replace(/^(\\-)*(\d+)\.(\d).*$/,"$1$2.$3");break;case 2:e=e.replace(/^(\\-)*(\d+)\.(\d\d).*$/,"$1$2.$3"),a&&(e="-"+e);break;case 3:e=e.replace(/^(\\-)*(\d+)\.(\d\d\d).*$/,"$1$2.$3");break;case 4:e=e.replace(/^(\\-)*(\d+)\.(\d\d\d\d).*$/,"$1$2.$3");break;case 6:e=e.replace(/^(\\-)*(\d+)\.(\d\d\d\d\d\d).*$/,"$1$2.$3");break;default:e=e.replace(/^(\\-)*(\d+)\.(\d\d).*$/,"$1$2.$3");break}return e},handelDelete:function(e){var t=this;this.$confirm("请确定删除当前领用单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.RawPickDelete)({pickNo:e.ReceiptNumber}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData()):t.$message({type:"warning",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},dialogConfirm:function(e){var t=this;this.$refs[e].validate((function(a){if(!a)return!1;if(!t.dialogData.length)return t.$message({type:"warning",message:"物料登记列表至少要有一条数据"}),!1;var n=JSON.parse(JSON.stringify(t.dialogData)),r=!1;if(n.forEach((function(e){""!==e.Materiel_Aux_Id&&""!==e.Balance_Amount&&""!==e.Unit_Price||(r=!0)})),r)return t.$message({type:"warning",message:"请输入必填字段"}),!1;var o=t.dialogData,l=t.Id?g.MaterielAuxDailyBalanceUpdate:g.GetMaterielAuxDailyBalanceSave,c=t.Id?{Id:t.Id}:{},s=(0,u.parseTime)(t.dialogForm.Register_Date);l((0,i.default)({Register_Date:s,DetailList:o},c)).then((function(a){a.IsSucceed?(t.$message({type:"success",message:"成功提交!"}),t.dialogCancel(e),t.fetchData()):t.$message({type:"warning",message:a.Message})}))}))},dialogCancel:function(e){this.$refs[e].resetFields(),this.dialogData=[],this.dialogVisible=!1},closeDialog:function(){this.dialogForm={Register_Date:new Date},this.dialogData=[]},isCalculate:function(e){var t=(0,u.getDaysBetween)(this.Register_Date_Min,(0,u.parseTime)(e,"{y}-{m}-{d}"));return 0===t||1===t}}}},b76ac:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetAuxPageList=o,t.GetMaterielAuxDailyBalanceList=r,t.GetMaterielAuxDailyBalanceSave=u,t.GetOMALatestStatisticTime=d,t.MaterielAuxDailyBalanceDelete=s,t.MaterielAuxDailyBalanceDetail=l,t.MaterielAuxDailyBalanceUpdate=c;var i=n(a("b775"));n(a("4328"));function r(e){return(0,i.default)({url:"/PRO/MaterielAuxDailyBalance/PageList",method:"post",data:e})}function o(e){return(0,i.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/MaterielAuxDailyBalance/Save",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/MaterielAuxDailyBalance/Get",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/MaterielAuxDailyBalance/Update",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/MaterielAuxDailyBalance/Delete",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:e})}},da7c:function(e,t,a){"use strict";a("a45f3")},e144:function(e,t,a){"use strict";a.r(t),a.d(t,"v1",(function(){return s})),a.d(t,"v3",(function(){return M})),a.d(t,"v4",(function(){return T["a"]})),a.d(t,"v5",(function(){return F})),a.d(t,"NIL",(function(){return U})),a.d(t,"version",(function(){return q})),a.d(t,"validate",(function(){return d["a"]})),a.d(t,"stringify",(function(){return o["a"]})),a.d(t,"parse",(function(){return g}));var n,i,r=a("d8f8"),o=a("58cf"),u=0,l=0;function c(e,t,a){var c=t&&a||0,s=t||new Array(16);e=e||{};var d=e.node||n,f=void 0!==e.clockseq?e.clockseq:i;if(null==d||null==f){var g=e.random||(e.rng||r["a"])();null==d&&(d=n=[1|g[0],g[1],g[2],g[3],g[4],g[5]]),null==f&&(f=i=16383&(g[6]<<8|g[7]))}var m=void 0!==e.msecs?e.msecs:Date.now(),p=void 0!==e.nsecs?e.nsecs:l+1,h=m-u+(p-l)/1e4;if(h<0&&void 0===e.clockseq&&(f=f+1&16383),(h<0||m>u)&&void 0===e.nsecs&&(p=0),p>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");u=m,l=p,i=f,m+=122192928e5;var v=(1e4*(268435455&m)+p)%4294967296;s[c++]=v>>>24&255,s[c++]=v>>>16&255,s[c++]=v>>>8&255,s[c++]=255&v;var y=m/4294967296*1e4&268435455;s[c++]=y>>>8&255,s[c++]=255&y,s[c++]=y>>>24&15|16,s[c++]=y>>>16&255,s[c++]=f>>>8|128,s[c++]=255&f;for(var D=0;D<6;++D)s[c+D]=d[D];return t||Object(o["a"])(s)}var s=c,d=a("06e4");function f(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");var t,a=new Uint8Array(16);return a[0]=(t=parseInt(e.slice(0,8),16))>>>24,a[1]=t>>>16&255,a[2]=t>>>8&255,a[3]=255&t,a[4]=(t=parseInt(e.slice(9,13),16))>>>8,a[5]=255&t,a[6]=(t=parseInt(e.slice(14,18),16))>>>8,a[7]=255&t,a[8]=(t=parseInt(e.slice(19,23),16))>>>8,a[9]=255&t,a[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,a[11]=t/4294967296&255,a[12]=t>>>24&255,a[13]=t>>>16&255,a[14]=t>>>8&255,a[15]=255&t,a}var g=f;function m(e){e=unescape(encodeURIComponent(e));for(var t=[],a=0;a<e.length;++a)t.push(e.charCodeAt(a));return t}var p="6ba7b810-9dad-11d1-80b4-00c04fd430c8",h="6ba7b811-9dad-11d1-80b4-00c04fd430c8",v=function(e,t,a){function n(e,n,i,r){if("string"===typeof e&&(e=m(e)),"string"===typeof n&&(n=g(n)),16!==n.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var u=new Uint8Array(16+e.length);if(u.set(n),u.set(e,n.length),u=a(u),u[6]=15&u[6]|t,u[8]=63&u[8]|128,i){r=r||0;for(var l=0;l<16;++l)i[r+l]=u[l];return i}return Object(o["a"])(u)}try{n.name=e}catch(i){}return n.DNS=p,n.URL=h,n};function y(e){if("string"===typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var a=0;a<t.length;++a)e[a]=t.charCodeAt(a)}return D(b(w(e),8*e.length))}function D(e){for(var t=[],a=32*e.length,n="0123456789abcdef",i=0;i<a;i+=8){var r=e[i>>5]>>>i%32&255,o=parseInt(n.charAt(r>>>4&15)+n.charAt(15&r),16);t.push(o)}return t}function _(e){return 14+(e+64>>>9<<4)+1}function b(e,t){e[t>>5]|=128<<t%32,e[_(t)-1]=t;for(var a=1732584193,n=-271733879,i=-1732584194,r=271733878,o=0;o<e.length;o+=16){var u=a,l=n,c=i,s=r;a=A(a,n,i,r,e[o],7,-680876936),r=A(r,a,n,i,e[o+1],12,-389564586),i=A(i,r,a,n,e[o+2],17,606105819),n=A(n,i,r,a,e[o+3],22,-1044525330),a=A(a,n,i,r,e[o+4],7,-176418897),r=A(r,a,n,i,e[o+5],12,1200080426),i=A(i,r,a,n,e[o+6],17,-1473231341),n=A(n,i,r,a,e[o+7],22,-45705983),a=A(a,n,i,r,e[o+8],7,1770035416),r=A(r,a,n,i,e[o+9],12,-1958414417),i=A(i,r,a,n,e[o+10],17,-42063),n=A(n,i,r,a,e[o+11],22,-1990404162),a=A(a,n,i,r,e[o+12],7,1804603682),r=A(r,a,n,i,e[o+13],12,-40341101),i=A(i,r,a,n,e[o+14],17,-1502002290),n=A(n,i,r,a,e[o+15],22,1236535329),a=C(a,n,i,r,e[o+1],5,-165796510),r=C(r,a,n,i,e[o+6],9,-1069501632),i=C(i,r,a,n,e[o+11],14,643717713),n=C(n,i,r,a,e[o],20,-373897302),a=C(a,n,i,r,e[o+5],5,-701558691),r=C(r,a,n,i,e[o+10],9,38016083),i=C(i,r,a,n,e[o+15],14,-660478335),n=C(n,i,r,a,e[o+4],20,-405537848),a=C(a,n,i,r,e[o+9],5,568446438),r=C(r,a,n,i,e[o+14],9,-1019803690),i=C(i,r,a,n,e[o+3],14,-187363961),n=C(n,i,r,a,e[o+8],20,1163531501),a=C(a,n,i,r,e[o+13],5,-1444681467),r=C(r,a,n,i,e[o+2],9,-51403784),i=C(i,r,a,n,e[o+7],14,1735328473),n=C(n,i,r,a,e[o+12],20,-1926607734),a=R(a,n,i,r,e[o+5],4,-378558),r=R(r,a,n,i,e[o+8],11,-2022574463),i=R(i,r,a,n,e[o+11],16,1839030562),n=R(n,i,r,a,e[o+14],23,-35309556),a=R(a,n,i,r,e[o+1],4,-1530992060),r=R(r,a,n,i,e[o+4],11,1272893353),i=R(i,r,a,n,e[o+7],16,-155497632),n=R(n,i,r,a,e[o+10],23,-1094730640),a=R(a,n,i,r,e[o+13],4,681279174),r=R(r,a,n,i,e[o],11,-358537222),i=R(i,r,a,n,e[o+3],16,-722521979),n=R(n,i,r,a,e[o+6],23,76029189),a=R(a,n,i,r,e[o+9],4,-640364487),r=R(r,a,n,i,e[o+12],11,-421815835),i=R(i,r,a,n,e[o+15],16,530742520),n=R(n,i,r,a,e[o+2],23,-995338651),a=S(a,n,i,r,e[o],6,-198630844),r=S(r,a,n,i,e[o+7],10,1126891415),i=S(i,r,a,n,e[o+14],15,-1416354905),n=S(n,i,r,a,e[o+5],21,-57434055),a=S(a,n,i,r,e[o+12],6,1700485571),r=S(r,a,n,i,e[o+3],10,-1894986606),i=S(i,r,a,n,e[o+10],15,-1051523),n=S(n,i,r,a,e[o+1],21,-2054922799),a=S(a,n,i,r,e[o+8],6,1873313359),r=S(r,a,n,i,e[o+15],10,-30611744),i=S(i,r,a,n,e[o+6],15,-1560198380),n=S(n,i,r,a,e[o+13],21,1309151649),a=S(a,n,i,r,e[o+4],6,-145523070),r=S(r,a,n,i,e[o+11],10,-1120210379),i=S(i,r,a,n,e[o+2],15,718787259),n=S(n,i,r,a,e[o+9],21,-343485551),a=x(a,u),n=x(n,l),i=x(i,c),r=x(r,s)}return[a,n,i,r]}function w(e){if(0===e.length)return[];for(var t=8*e.length,a=new Uint32Array(_(t)),n=0;n<t;n+=8)a[n>>5]|=(255&e[n/8])<<n%32;return a}function x(e,t){var a=(65535&e)+(65535&t),n=(e>>16)+(t>>16)+(a>>16);return n<<16|65535&a}function k(e,t){return e<<t|e>>>32-t}function P(e,t,a,n,i,r){return x(k(x(x(t,e),x(n,r)),i),a)}function A(e,t,a,n,i,r,o){return P(t&a|~t&n,e,t,i,r,o)}function C(e,t,a,n,i,r,o){return P(t&n|a&~n,e,t,i,r,o)}function R(e,t,a,n,i,r,o){return P(t^a^n,e,t,i,r,o)}function S(e,t,a,n,i,r,o){return P(a^(t|~n),e,t,i,r,o)}var I=y,$=v("v3",48,I),M=$,T=a("ec26");function O(e,t,a,n){switch(e){case 0:return t&a^~t&n;case 1:return t^a^n;case 2:return t&a^t&n^a&n;case 3:return t^a^n}}function L(e,t){return e<<t|e>>>32-t}function B(e){var t=[1518500249,1859775393,2400959708,3395469782],a=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"===typeof e){var n=unescape(encodeURIComponent(e));e=[];for(var i=0;i<n.length;++i)e.push(n.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var r=e.length/4+2,o=Math.ceil(r/16),u=new Array(o),l=0;l<o;++l){for(var c=new Uint32Array(16),s=0;s<16;++s)c[s]=e[64*l+4*s]<<24|e[64*l+4*s+1]<<16|e[64*l+4*s+2]<<8|e[64*l+4*s+3];u[l]=c}u[o-1][14]=8*(e.length-1)/Math.pow(2,32),u[o-1][14]=Math.floor(u[o-1][14]),u[o-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<o;++d){for(var f=new Uint32Array(80),g=0;g<16;++g)f[g]=u[d][g];for(var m=16;m<80;++m)f[m]=L(f[m-3]^f[m-8]^f[m-14]^f[m-16],1);for(var p=a[0],h=a[1],v=a[2],y=a[3],D=a[4],_=0;_<80;++_){var b=Math.floor(_/20),w=L(p,5)+O(b,h,v,y)+D+t[b]+f[_]>>>0;D=y,y=v,v=L(h,30)>>>0,h=p,p=w}a[0]=a[0]+p>>>0,a[1]=a[1]+h>>>0,a[2]=a[2]+v>>>0,a[3]=a[3]+y>>>0,a[4]=a[4]+D>>>0}return[a[0]>>24&255,a[0]>>16&255,a[0]>>8&255,255&a[0],a[1]>>24&255,a[1]>>16&255,a[1]>>8&255,255&a[1],a[2]>>24&255,a[2]>>16&255,a[2]>>8&255,255&a[2],a[3]>>24&255,a[3]>>16&255,a[3]>>8&255,255&a[3],a[4]>>24&255,a[4]>>16&255,a[4]>>8&255,255&a[4]]}var G=B,N=v("v5",80,G),F=N,U="00000000-0000-0000-0000-000000000000";function z(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}var q=z}}]);