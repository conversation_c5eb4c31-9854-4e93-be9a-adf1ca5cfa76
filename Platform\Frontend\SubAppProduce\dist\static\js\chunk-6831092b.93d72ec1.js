(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6831092b"],{14092:function(e,t,a){"use strict";a.r(t);var n=a("d7b8"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},25205:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("el-button",{staticStyle:{"margin-bottom":"16px"},on:{click:e.tagBack}},[e._v("返回")]),a("div",{staticClass:"sch-detail"},[e._m(0),a("div",{staticClass:"form-search"},[a("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,"label-width":"85px"}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.isView,expression:"isView"}],attrs:{label:"补换单号:",prop:"Remark"}},[a("el-input",{staticStyle:{width:"260px"},attrs:{disabled:e.isView,placeholder:"请输入"},model:{value:e.Replace_Code,callback:function(t){e.Replace_Code=t},expression:"Replace_Code"}})],1),a("el-form-item",{attrs:{label:"项目名称:"}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",filterable:"",disabled:!0},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projectOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域:"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{"tree-params":e.treeParams,"select-params":{clearable:!1},disabled:!0},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"批次:"}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",filterable:"",disabled:!0},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.installOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{required:"",label:"补换班组:",prop:"Replace_Teams_Default_Id",rules:{required:!0,message:"请选择",trigger:"change"}}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",disabled:e.isView,filterable:""},model:{value:e.form.Replace_Teams_Default_Id,callback:function(t){e.$set(e.form,"Replace_Teams_Default_Id",t)},expression:"form.Replace_Teams_Default_Id"}},e._l(e.replace_Teams,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"申请日期:",prop:"Quest_Date"}},[a("el-date-picker",{staticStyle:{width:"260px"},attrs:{disabled:e.isView,"value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:e.form.Quest_Date,callback:function(t){e.$set(e.form,"Quest_Date",t)},expression:"form.Quest_Date"}})],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.isView,expression:"isView"}],attrs:{label:"审批结果:",prop:"Audit_Results_Id"}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",disabled:e.isView,filterable:""},model:{value:e.Audit_Results_Id,callback:function(t){e.Audit_Results_Id=t},expression:"Audit_Results_Id"}},e._l(e.Audit_Results,(function(e){return a("el-option",{key:e.Value,attrs:{label:e.Lable,value:e.Value}})})),1)],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.isView,expression:"isView"}],attrs:{label:"单据状态:",prop:"Replace_Status_Id"}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",disabled:e.isView,filterable:""},model:{value:e.Replace_Status_Id,callback:function(t){e.Replace_Status_Id=t},expression:"Replace_Status_Id"}},e._l(e.Replace_Status,(function(e){return a("el-option",{key:e.Value,attrs:{label:e.Lable,value:e.Value}})})),1)],1),a("el-form-item",{attrs:{label:"备注:",prop:"Remark"}},[a("el-input",{staticStyle:{width:"260px"},attrs:{disabled:e.isView,placeholder:e.isView?"":"请输入"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"view"!==e.status,expression:"status!=='view'"}],staticClass:"assistant-wrapper"},[a("div",[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{disabled:e.form.Stock_Status>3,type:"primary"},on:{click:e.openAddPart}},[e._v("选择零件")]),a("el-button",{attrs:{disabled:e.selectList.length<=0},on:{click:e.deleteRows}},[e._v("删除零件")])],1)])]),a("div",{staticClass:"twrap"},[a("div",{staticStyle:{height:"100%"}},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Amount"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"edit-render":{},"min-width":t.minWidth,width:t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.Num,disabled:"view"===e.status},model:{value:n.Amount,callback:function(t){e.$set(n,"Amount",e._n(t))},expression:"row.Amount"}})]}},{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Amount)+" ")]}}],null,!0)}):a("vxe-column",{key:t.Id,attrs:{field:t.Code,title:t.Display_Name,sortable:"","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(n[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:"add"!==e.status&&e.queryHistoriesList.length>0,expression:"status!=='add' && queryHistoriesList.length>0"}],staticClass:"flow-info"},[a("div",{staticClass:"flow-info_title"},[e._v("流转信息")]),e._l(e.queryHistoriesList,(function(t,n){return a("div",{key:n},[0==t.Verification_Status?[a("div",{staticClass:"flow-info_user"},[e._v("发起人")]),a("div",{staticClass:"flow-info_options"},[a("span",[e._v(e._s(t.Create_UserName))]),a("span"),a("span",[e._v(e._s(t.Create_Date))])])]:[a("div",{staticClass:"flow-info_user"},[e._v("审批人")]),a("div",{staticClass:"flow-info_options"},[a("span",[e._v(e._s(t.Create_UserName))]),a("span",[e._v(e._s(1===t.Verification_Status?"通过":"退回"))]),a("span",[e._v(e._s(t.Create_Date))]),a("div",{staticClass:"remark"},[e._v(" 处理意见："+e._s(t.Verification_Opinion)+" ")])])]],2)}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:"view"!==e.status,expression:"status!=='view'"}],staticClass:"header-btns"},[a("el-button",{on:{click:e.tagBack}},[e._v("取消")]),a("el-button",{attrs:{loading:e.btnLoading,type:"primary",disabled:e.tbData.length<=0||"view"===e.status},on:{click:e.save}},[e._v("提交")])],1)]),a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:e.dialogCfgs.title,visible:e.dialogShow,width:e.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(t){e.dialogShow=t}}},[a("keep-alive",[e.dialogShow?a(e.dialogCfgs.component,e._b({tag:"component",on:{dialogCancel:e.dialogCancel,dialogFormSubmitSuccess:e.dialogFormSubmitSuccess}},"component",e.dialogCfgs.props,!1)):e._e()],1)],1)],1)},r=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-custom-header"},[a("div",{staticClass:"header-text"},[e._v("基本信息")])])}]},"2c61":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCarNum=g,t.Delete=h,t.DeleteScanSteel=C,t.EditWithParts=i,t.GetCadUrlBySteelName=f,t.GetDetaileEntities=l,t.GetForgetScanWeight=d,t.GetNode=p,t.GetPageEntities=s,t.GetPageEntitiesForExproNew=m,t.GetPageStorageBySearch=P,t.GetPartPageList=_,t.GetProjectsNodeList=o,t.GetSteelHistory=c,t.GetTotalWeight=u,t.GetUserNodeList=v;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/PLM/Plm_Projects_Node/GetNodeList",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PLM/Trace/EditWithParts",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PLM/Trace/GetPageEntities",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PLM/Trace/GetTotalWeight",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PLM/Trace/GetForgetScanWeight",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PLM/Trace/GetDetaileEntities",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PLM/Trace/GetSteelHistory",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PLM/Trace/GetCadUrlBySteelName",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PLM/Trace/GetPageEntitiesForExproNew",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PLM/Trace/AddCarNum",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PLM/Trace/Delete",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PLM/Trace/GetNode",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PLM/Trace/DeleteScanSteel",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PLM/Component/GetPageStorageBySearch",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PLM/AppScan/GetUserNodeList",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Part/GetPartPageList",method:"post",data:e})}},"50a6b":function(e,t,a){"use strict";a.r(t);var n=a("25205"),r=a("14092");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("51b3");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"1ba8990c",null);t["default"]=s.exports},"51b3":function(e,t,a){"use strict";a("af3b")},"7015f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=y,t.AgainSubmitChangeOrder=_,t.BatchReuseEngineeringContactChangedComponentPart=ie,t.BatchReuseEngineeringContactMocComponentPart=se,t.CancelChangeOrder=v,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=$,t.DeleteChangeOrder=c,t.DeleteChangeOrderV2=E,t.DeleteChangeReason=h,t.DeleteChangeType=s,t.DeleteEngineeringContactChangeOrder=W,t.DeleteMocOrder=J,t.DeleteMocType=Pe,t.ExportEngineeringContactChangedAddComponentPart=le,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=re,t.ExportMocAddComponentPart=ce,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=S,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=d,t.GetChangeOrderTaskInfo=x,t.GetChangeOrderTaskPageList=w,t.GetChangeOrderV2=j,t.GetChangeOrderV2PageList=T,t.GetChangeReason=m,t.GetChangeType=o,t.GetChangedComponentPartPageList=F,t.GetChangedComponentPartProductionList=q,t.GetCompAndPartSchdulingPageList=L,t.GetCompAndPartTaskList=D,t.GetCompanyUserPageList=M,t.GetEngineeringContactChangeOrder=Q,t.GetEngineeringContactChangeOrderPageList=z,t.GetEngineeringContactChangedAddComponentPartPageList=ue,t.GetEngineeringContactChangedAddComponentPartSummary=de,t.GetEngineeringContactChangedComponentPartPageList=Y,t.GetEngineeringContactChangedSummary=X,t.GetEngineeringContactFileInfo=B,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=K,t.GetEngineeringContactMocSummary=ne,t.GetFactoryChangeTypeListV2=U,t.GetFactoryPeoplelist=u,t.GetMocAddComponentPartPageList=me,t.GetMocModelList=_e,t.GetMocOrderInfo=pe,t.GetMocOrderPageList=ge,t.GetMocOrderTypeList=Ce,t.GetMyChangeOrderPageList=P,t.GetProjectAreaChangeTreeList=V,t.GetProjectChangeOrderList=I,t.GetTypeReason=p,t.ImportChangFile=he,t.ImportChangeDeependFile=C,t.QueryHistories=O,t.ReuseEngineeringContactChangedComponentPart=ae,t.ReuseEngineeringContactMocComponentPart=oe,t.SaveChangeOrder=l,t.SaveChangeOrderTask=G,t.SaveChangeOrderV2=k,t.SaveChangeReason=g,t.SaveChangeType=i,t.SaveEngineeringContactChangeOrder=H,t.SaveMocOrder=be,t.SaveMocOrderType=ve,t.SubmitChangeOrder=b,t.SubmitChangeOrderV2=A,t.SubmitMocOrder=N,t.Verification=R;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function O(e){return(0,r.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function S(e){return(0,r.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function R(e){return(0,r.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function M(e){return(0,r.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function _e(e){return(0,r.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function be(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},"9f6a":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"stockin-details"},[a("div",{staticClass:"search-form"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"零件名称",prop:"Code_Format"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入（空格间隔筛选多个）",clearable:""},model:{value:e.form.Code_Format,callback:function(t){e.$set(e.form,"Code_Format",t)},expression:"form.Code_Format"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{on:{click:function(t){return e.resetSearch()}}},[e._v("重置")])],1)],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,width:t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var r=n.row;return[a("div",[a("span",[e._v(e._s(r[t.Code]||"-"))])])]}}],null,!0)})}))],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1),a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1)])},r=[]},a6b9:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("5319"),a("5b81"),a("498a"),a("c7cd"),a("ddb0");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),s=a("6186"),u=a("2c61"),d=a("fd31"),l=n(a("333d")),c=a("c685");t.default={name:"PROPartReplacePartList",components:{Pagination:l.default},props:{formParams:{type:Object,default:function(){return{}}},partIds:{type:String,default:""}},data:function(){return{tablePageSize:c.tablePageSize,form:{Code_Format:"",Code:"",TypeId:"",Type_Name:""},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},tbLoading:!1,loading:!1,typeOption:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],Details:[],gridCode:"pro_parts_replace_page_list"}},computed:{},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTypeList();case 1:return t.n=2,e.fetchData();case 2:return t.a(2)}}),t)})))()},mounted:function(){},methods:{getTypeList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,n,r,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,d.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.form.TypeId=null===(r=e.typeOption[0])||void 0===r?void 0:r.Id,e.form.Type_Name=null===(i=e.typeOption[0])||void 0===i?void 0:i.Name)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:e.tbLoading=!0,Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 2:return t.a(2)}}),t)})))()},changePage:function(e,t){var a=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:a.tbLoading=!0,("number"!==typeof a.queryInfo.PageSize||a.queryInfo.PageSize<1)&&(a.queryInfo.PageSize=20),a.setGrid((0,r.default)((0,r.default)({},a.tbConfig),{},{Row_Number:e.limit})),a.queryInfo.PageSize=e.limit,a.queryInfo.Page=e.page,Promise.all([a.fetchList()]).then((function(e){a.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},fetchList:function(){var e=this;return new Promise((function(t){(0,u.GetPartPageList)((0,r.default)((0,r.default)((0,r.default)((0,r.default)({},e.queryInfo),e.form),e.formParams),{},{Code:e.form.Code.trim().replaceAll(" ","\n"),Ids:e.partIds})).then((function(a){a.IsSucceed?(e.queryInfo.PageSize=a.Data.PageSize,e.total=a.Data.TotalCount,e.tbData=a.Data.Data,e.selectList=[]):e.$message({message:a.Message,type:"error"}),t()}))}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,s.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.form.TypeId})).Code}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=r.ColumnList||[],s=i.sort((function(e,t){return e.Sort-t.Sort}));t.columns=s.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),e})),t.queryInfo.PageSize=+r.Grid.Row_Number||20,a(t.columns);var u=JSON.parse(JSON.stringify(t.columns));t.columnsOption=u}else t.$message({message:o,type:"error"})}))}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{Pager_Align:"center",Height:360}),this.queryInfo.PageSize=e.Row_Number},handleSearch:function(){var e=this.form.Code_Format.trim();e=e.replace(/\s+/g,"\n"),this.form.Code=e,this.fetchList()},resetSearch:function(){this.$refs["form"].resetFields(),this.handleSearch()},cancel:function(){this.$emit("dialogCancel")},tbSelectChange:function(e){this.selectList=e.records},multiSelectedChange:function(e){this.selectList=e},save:function(){if(this.selectList.length<=0)return this.$message.warning("请选择需要补换的零件");this.Details=this.selectList.map((function(e){return Object.assign({},e,{})})),this.$emit("dialogFormSubmitSuccess",{type:"merge",data:this.Details})}}}},af3b:function(e,t,a){},d7b8:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("25f0"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),s=a("6186"),u=a("fd31"),d=a("87c9"),l=a("7015f"),c=n(a("f9ed")),f=a("4799"),m=a("8975"),g=n(a("dd30")),h=a("ed08");t.default={name:"PackingAdd",components:{PartList:c.default},mixins:[g.default],data:function(){return{tbLoading:!1,btnLoading:!1,confirmed:!1,Id:"",tbConfig:{},columns:[],tbData:[],Part_Header:{},Details:[],queryHistoriesList:[],queryInfo:{Page:1,TotalCount:0,PageSize:-1,PageSizes:[20,40,60,80,100]},form:{Project_Id:"",Sys_Project_Id:"",Area_Id:"",InstallUnit_Id:"",Replace_Teams_Id:"",Quest_Date:"",Remark:""},Replace_Code:"",Audit_Results_Id:1,Replace_Status_Id:1,Audit_Results:[{Value:1,Lable:"已审批"},{Value:2,Lable:"待审批"},{Value:3,Lable:"已退回"}],Replace_Status:[{Value:1,Lable:"已完成"},{Value:2,Lable:"待确认"},{Value:3,Lable:"待加工"}],selectList:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"50%"},gridCode:"pro_parts_replace_page_res",typeOption:[],Proportion:0,Unit:"",Instance_Id:""}},computed:{status:function(){return this.$router.currentRoute.query.status},isView:function(){return"view"===this.$router.currentRoute.query.status},dateDefault:function(){var e=new Date,t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate();return t}},beforeRouteEnter:function(e,t,a){"view"===e.query.status?e.meta.title="查看补换申请单":"edit"===e.query.status?e.meta.title="编辑补换申请单":e.meta.title="新增补换申请单",a()},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.initRouterParams();case 1:return t.n=2,e.getTypeList();case 2:return t.n=3,e.fetchData();case 3:return t.a(2)}}),t)})))()},mounted:function(){var e=this;this.form.Project_Id=this.$router.currentRoute.query.projectId,this.queryForm.projectId=this.$router.currentRoute.query.projectId,this.form.Area_Id=this.$router.currentRoute.query.areaId,this.queryForm.areaId=this.$router.currentRoute.query.areaId,this.form.InstallUnit_Id=this.$router.currentRoute.query.install,this.queryForm.install=this.$router.currentRoute.query.install,this.Id=this.$router.currentRoute.query.Id,this.getAreaList(),this.getInstall(),"add"===this.status&&(this.form.Quest_Date=this.dateDefault),(0,f.getFactoryProfessional)().then((function(t){e.Proportion=t[0].Proportion,e.Unit=t[0].Unit}))},methods:{getTypeList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,n,r,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.form.TypeId=null===(r=e.typeOption[0])||void 0===r?void 0:r.Id,e.form.Type_Name=null===(i=e.typeOption[0])||void 0===i?void 0:i.Name)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:if(e.tbLoading=!0,e.Id){t.n=2;break}return e.tbLoading=!1,t.a(2);case 2:Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 3:return t.a(2)}}),t)})))()},fetchList:function(){var e=this;return new Promise((function(t){(0,d.FindPartReplaceApplyById)({Id:e.Id}).then((function(a){if(a.IsSucceed){var n=a.Data.Part_Header;e.tbData=a.Data.Part_List,e.Part_Header=n,e.Replace_Code=n.Replace_Code,e.form.Replace_Teams_Id=n.Replace_Teams_Id,e.form.Quest_Date=n.Quest_Date,e.Audit_Results_Id=n.Audit_Results_Name||2,e.Replace_Status_Id=-1===n.Replace_Status_Name?null:n.Replace_Status_Name,e.form.Remark=n.Remark,e.Instance_Id=n.Instance_Id,e.selectList=[],e.getQueryHistories()}else e.$message({message:a.Message,type:"error"});t()}))}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,s.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.form.TypeId})).Code}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=r.ColumnList||[],s=i.sort((function(e,t){return e.Sort-t.Sort}));t.columns=s.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),e})),t.columns.push({Display_Name:"补换数量",Code:"Amount"}),t.columns.map((function(e){if("Amount"===e.Code)return e.minWidth="160",e.Width="auto",e})),a(t.columns);var u=JSON.parse(JSON.stringify(t.columns));t.columnsOption=u}else t.$message({message:o,type:"error"})}))}))},openAddPart:function(){var e=this;this.form.Sys_Project_Id=this.projectOption.find((function(t){return t.Id===e.form.Project_Id})).Sys_Project_Id,this.openDialog({title:"添加",component:"PartList",width:"920px",props:{formParams:this.form,partIds:this.tbData.map((function(e){return e.Id})).toString()}})},getQueryHistories:function(){var e=this;(0,l.QueryHistories)("FlowInstanceId=".concat(this.Instance_Id)).then((function(t){if(t.IsSucceed){var a=t.Data.reverse();e.queryHistoriesList=a.map((function(e){return e.Create_Date=e.Create_Date?(0,m.timeFormat)(e.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"):e.Create_Date,e}))}}))},tagBack:function(){var e=this;this.$confirm("此操作不会保存数据，是否离开?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.confirmed=!0,(0,h.closeTagView)(e.$store,e.$route)})).catch((function(){e.$message({type:"info",message:"已取消"})}))},tbSelectChange:function(e){e.checked;var t=this.$refs.xTable.getCheckboxRecords();this.selectList=t},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Amount"===t.field},initRouterParams:function(){if("add"!==this.status){var e=JSON.parse(sessionStorage.getItem("PackageParams"));this.form=Object.assign({},this.form,e)}},openDialog:function(e){e&&"[object Object]"===Object.prototype.toString.call(e)||(e={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,e,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(e){var t=e.type,a=e.data;switch(this.dialogCancel(),t){case"merge":this.mergeEntity(a);break}},mergeEntity:function(e){e.map((function(e){return e.Amount=1,e})),this.Details=e,this.tbData=this.tbData.concat(e),this.queryInfo.TotalCount=this.tbData.length},deleteRows:function(){var e=this;this.$confirm("此操作将删除选择数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.selectList.forEach((function(t){var a=t.Id;e.tbData=e.tbData.filter((function(e){return a!==e.Id}))}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},save:function(){var e=this;if(this.btnLoading=!0,this.form.Replace_Teams_Id=this.form.Replace_Teams_Default_Id,!this.form.Replace_Teams_Id)return this.$message({type:"error",message:"请选择补换班组"}),void(this.btnLoading=!1);this.form.Sys_Project_Id=this.projectOption.find((function(t){return t.Id===e.form.Project_Id})).Sys_Project_Id;var t=[];this.tbData.forEach((function(e){var a={};a.Part_Id=e.Id,a.Amount=e.Amount,t.push(a)})),this.form.Part_List=t,"edit"===this.status&&(this.form.Replace_Code=this.Replace_Code);var a="add"===this.status?d.SavePartReplaceApply:d.EditReplacementApply;a((0,r.default)({},this.form)).then((function(t){t.IsSucceed?(e.$message.success(t.Message||"补换申请单创建成功"),(0,h.closeTagView)(e.$store,e.$route)):(e.$message({message:t.Message,type:"error"}),e.btnLoading=!1)}))},formatSaveData:function(){var e=this,t={entity:{},details:[]};return Object.keys(this.entity).forEach((function(a){"details"!==a&&(t.entity[a]=e.entity[a])})),t.details=this.entity.details,t}}}},ec39:function(e,t,a){"use strict";a.r(t);var n=a("a6b9"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},f023:function(e,t,a){},f9ed:function(e,t,a){"use strict";a.r(t);var n=a("9f6a"),r=a("ec39");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("ffa90");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"a78988ac",null);t["default"]=s.exports},ffa90:function(e,t,a){"use strict";a("f023")}}]);