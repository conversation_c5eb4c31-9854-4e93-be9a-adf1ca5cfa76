(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b72471d2"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,a){var i=o(),u=t-i,l=20,s=0;e="undefined"===typeof e?500:e;var d=function(){s+=l;var t=Math.easeInOutQuad(s,i,u,e);r(t),s<e?n(d):a&&"function"===typeof a&&a()};d()}},"0ad1":function(t,e,a){"use strict";a.r(e);var n=a("3b7d"),r=a("986d");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("72d0");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"dc7e510e",null);e["default"]=u.exports},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,n.GetGridByCode)({code:t,IsAll:a}).then((function(t){var n=t.IsSucceed,i=t.Data,u=t.Message;if(n){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||r.tablePageSize[0]),o(e.columns)}else e.$message({message:u,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,n=t.type;this.queryInfo.Page="limit"===n?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var n=0;n<this.columns.length;n++){var r=this.columns[n];if(r.Code===e){a.Type=r.Type,a.Filter_Type=r.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"27b0":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tb-wrapper"},[a("el-form",{ref:"form",attrs:{inline:"",model:t.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}},t._l(t.projectList,(function(t){return a("el-option",{key:t.Sys_Project_Id,attrs:{label:t.Short_Name,value:t.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域名称",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!t.form.Sys_Project_Id,"select-params":t.selectParams,"tree-params":t.treeParamsArea},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"零件名称",prop:"Part_Name"}},[a("el-input",{attrs:{clearable:""},model:{value:t.form.Part_Name,callback:function(e){t.$set(t.form,"Part_Name",e)},expression:"form.Part_Name"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"500","show-overflow":"",loading:t.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left"}}),t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",align:e.Align,field:e.Code,visible:e.Is_Display,title:e.Display_Name,"min-width":e.Width},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.row;return["Type"===e.Code?a("span",[1===r[e.Code]?a("el-tag",{attrs:{effect:"plain",type:"success"}},[t._v("手动导入")]):a("el-tag",{attrs:{effect:"plain",type:"warning"}},[t._v("自动推送")])],1):"Part_Amount"===e.Code?a("span",[a("el-link",{attrs:{type:"primary",underline:!1}},[t._v(" "+t._s(t._f("displayValue")(r[e.Code])))])],1):a("span",[t._v(" "+t._s(t._f("displayValue")(r[e.Code])))])]}}],null,!0)})]}))],2)],1),a("footer",[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("关 闭")])],1)],1)},r=[]},"28ef":function(t,e,a){"use strict";a("a2d1")},3166:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=p,e.DeleteProject=d,e.GeAreaTrees=I,e.GetFileSync=x,e.GetInstallUnitIdNameList=y,e.GetNoBindProjectList=m,e.GetPartDeepenFileList=w,e.GetProjectAreaTreeList=v,e.GetProjectEntity=l,e.GetProjectList=u,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=R,e.GetSchedulingPartList=_,e.IsEnableProjectMonomer=c,e.SaveProject=s,e.UpdateProjectTemplateBase=P,e.UpdateProjectTemplateContacts=g,e.UpdateProjectTemplateContract=b,e.UpdateProjectTemplateOther=S;var r=n(a("b775")),o=n(a("4328"));function i(t){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(t)})}function s(t){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(t)})}function c(t){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function w(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function x(t){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"3b7d":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-alert",{attrs:{type:"warning",closable:!1}},[[a("span",{staticClass:"cs-label"},[t._v("注意：请先 "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:t.handleExport}},[t._v("点击下载模板")])],1)]],2),a("div",{staticClass:"cs-upload-x"},[a("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}})],1),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定 ")])],1)],1)},r=[]},"46b1":function(t,e,a){"use strict";a.r(e);var n=a("d210"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},4779:function(t,e,a){"use strict";a.r(e);var n=a("9040"),r=a("46b1");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("d7d8");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"cf48cccc",null);e["default"]=u.exports},"69fe":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("c14f")),o=n(a("1da1")),i=n(a("5530"));a("4de4"),a("caad"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("a732"),a("d3b7"),a("25f0"),a("2532"),a("3ca3"),a("ddb0");var u=n(a("333d")),l=n(a("15ac")),s=a("c685"),d=n(a("2082")),c=a("7f9d"),f=n(a("4779")),p=n(a("0ad1")),m=n(a("f175")),h=n(a("cc8d")),P=a("2f62"),g=n(a("a657")),b=a("93aa"),S=a("ed08"),R=n(a("bbc2")),v=a("5f87");e.default={name:"PRONestingManagement",components:{OSSUpload:R.default,DynamicTableFields:g.default,Pagination:u.default,SurplusRaw:f.default,NestReport:p.default,PartsLayout:h.default,NestThumbs:m.default},mixins:[l.default,d.default],data:function(){return{addPageArray:[{path:this.$route.path+"/material/pick/add",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-7f87bd2b")]).then(a.bind(null,"4fac"))},name:"AddMaterialPickList",meta:{title:"新增领料单"},query:{pg_redirect:this.$route.name}},{path:this.$route.path+"/requisition",hidden:!0,component:function(){return a.e("chunk-b6a50f1a").then(a.bind(null,"8d03"))},name:"ModelCompare",meta:{title:"领料单"}},{path:this.$route.path+"/schedule",hidden:!0,component:function(){return a.e("chunk-ebe23692").then(a.bind(null,"c2de"))},name:"PRONestingSchedule",meta:{title:"下发排产任务"}},{path:this.$route.path+"/draft",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-2d0c91c4"),a.e("chunk-05a1e5cf"),a.e("chunk-7f16bee4"),a.e("chunk-12a38700")]).then(a.bind(null,"6217"))},name:"PRO2PartScheduleDraftNestNew",meta:{title:"草稿"}},{path:this.$route.path+"/add-return",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-5836e6ec"),a.e("chunk-c0ff466e"),a.e("chunk-53706990")]).then(a.bind(null,"555b"))},name:"PRORawMaterialStockReturnAddReturn",meta:{title:"新建退库单"}}],form:{Nesting_Result_Name:"",Raw_Name:"",Thickness:void 0,Texture:"",Cut_Status:"",Type:void 0},dialogVisible:!1,width:"70%",title:"",currentComponent:"",tbLoading:!1,options:[],columns:[],tbData:[],multipleSelection:[],tablePageSize:s.tablePageSize,total:0,queryInfo:{Page:1,PageSize:20},downloadLoading:!1,headers:{Authorization:(0,v.getToken)(),Last_Working_Object_Id:localStorage.getItem("Last_Working_Object_Id")}}},mounted:function(){this.getTbConfig(),this.fetchData(1)},methods:(0,i.default)((0,i.default)({getTbConfig:function(){var t=this;return(0,o.default)((0,r.default)().m((function e(){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.tbLoading=!0,e.n=1,t.getTableConfig("PRONestingManagementIndex");case 1:t.tbLoading=!1,a=["Picking_Bills","Out_Bills","Machining_Files","Thumbnail"],t.columns=t.columns.filter((function(t){return!a.includes(t.Code)}));case 2:return e.a(2)}}),e)})))()}},(0,P.mapActions)("schedule",["changeNestIds"])),{},{fetchData:function(t){var e=this;t&&(this.queryInfo.Page=t),this.tbLoading=!0,(0,c.GetNestingResultPageList)((0,i.default)((0,i.default)({},this.queryInfo),this.form)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleSearch:function(){},handleImport:function(){this.currentComponent="NestReport",this.width="30%",this.title="导入套料模板",this.dialogVisible=!0,this.$nextTick((function(t){}))},handleImportThumbs:function(){this.currentComponent="NestThumbs",this.width="30%",this.title="导入缩略图",this.dialogVisible=!0},handleDownload:function(){var t=this;this.downloadLoading=!0,(0,b.ExportProcess)({ids:this.multipleSelection.map((function(t){return t.Id}))}).then((function(e){e.IsSucceed?window.open((0,S.combineURL)(t.$baseUrl,e.Data)):t.$message({message:e.Message,type:"error"})})).finally((function(){t.downloadLoading=!1}))},handleDelete:function(){var t=this;this.$confirm(" 是否删除该数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.DeleteNestingResult)({ids:t.multipleSelection.map((function(t){return t.Id})).toString()}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleSubmit:function(){},tbSelectChange:function(t){this.multipleSelection=t.records},handleRaw:function(t){var e=this;this.width="70%",this.currentComponent="SurplusRaw",this.title="余料信息",this.dialogVisible=!0,this.$nextTick((function(a){e.$refs["content"].getData(t.Id)}))},handleAmount:function(t){var e=this;this.width="70%",this.currentComponent="PartsLayout",this.title="排版零件",this.dialogVisible=!0,this.$nextTick((function(a){e.$refs["content"].getData(t.Id)}))},handleSchedule:function(){var t=this.multipleSelection.filter((function(t){return 1===t.Type})).map((function(t){return t.Id}));t&&(this.changeNestIds(t),this.$router.push({name:"PRO2PartScheduleDraftNestNew",query:{status:"edit",pg_type:"part",pg_redirect:this.$route.name,type:"1"}}))},handleClose:function(){this.dialogVisible=!1},handleReset:function(){this.$refs["form"].resetFields(),this.fetchData(1)},toCreatePickList:function(){this.multipleSelection.some((function(t){return t.PickNo}))?this.$message.error("已有内调单号的不能重复生成"):this.$router.push({name:"AddMaterialPickList",query:{pg_redirect:this.$route.name,type:0,pickType:1,ids:this.multipleSelection.map((function(t){return t.Id}))}})},toCreateReturnList:function(){this.$router.push({name:"PRORawMaterialStockReturnAddReturn",query:{pg_redirect:this.$route.name,isNesting:1,ids:this.multipleSelection.map((function(t){return t.Id}))}})}})}},"72d0":function(t,e,a){"use strict";a("a155")},"77e94":function(t,e,a){"use strict";a.r(e);var n=a("95a2"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"783c":function(t,e,a){},7876:function(t,e,a){},"7f73":function(t,e,a){"use strict";a("7876")},"807a":function(t,e,a){"use strict";a.r(e);var n=a("8eb5"),r=a("cc4f5");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("7f73");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"fc0a0ac8",null);e["default"]=u.exports},"8eb5":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cs-z-flex-pd16-wrap abs100"},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:t.form,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"排版名称",prop:"Nesting_Result_Name"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:"",type:"text"},model:{value:t.form.Nesting_Result_Name,callback:function(e){t.$set(t.form,"Nesting_Result_Name",e)},expression:"form.Nesting_Result_Name"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"Raw_Name"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text",clearable:""},model:{value:t.form.Raw_Name,callback:function(e){t.$set(t.form,"Raw_Name",e)},expression:"form.Raw_Name"}})],1),a("el-form-item",{attrs:{label:"厚度",prop:"Thickness","label-width":"50px"}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{min:0,max:1e6,placeholder:"请输入",clearable:""},model:{value:t.form.Thickness,callback:function(e){t.$set(t.form,"Thickness",e)},expression:"form.Thickness"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Texture","label-width":"50px"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text",clearable:""},model:{value:t.form.Texture,callback:function(e){t.$set(t.form,"Texture",e)},expression:"form.Texture"}})],1),a("el-form-item",{attrs:{label:"切割状态",prop:"Cut_Status"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.Cut_Status,callback:function(e){t.$set(t.form,"Cut_Status",e)},expression:"form.Cut_Status"}},[a("el-option",{attrs:{label:"待切割",value:"待切割"}}),a("el-option",{attrs:{label:"已切割",value:"已切割"}})],1)],1),a("el-form-item",{attrs:{label:"类型",prop:"Type","label-width":"50px"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.Type,callback:function(e){t.$set(t.form,"Type",e)},expression:"form.Type"}},[a("el-option",{attrs:{label:"手动导入",value:1}}),a("el-option",{attrs:{label:"系统推送",value:2}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.fetchData(1)}}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("vxe-toolbar",{ref:"xToolbar",scopedSlots:t._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary",disabled:!t.multipleSelection.length},on:{click:t.toCreatePickList}},[t._v("生成内调单")]),a("el-button",{attrs:{type:"primary",disabled:!t.multipleSelection.length},on:{click:t.toCreateReturnList}},[t._v("生成余料退库单")]),a("el-button",{attrs:{type:"success"},on:{click:t.handleImport}},[t._v("导入套料报告")]),a("el-button",{attrs:{type:"success"},on:{click:t.handleImportThumbs}},[t._v("导入缩略图")]),a("el-button",{attrs:{type:"danger",disabled:!t.multipleSelection.length},on:{click:t.handleDelete}},[t._v("删除")]),a("el-button",{attrs:{type:"primary",disabled:!t.multipleSelection.length},on:{click:t.handleSchedule}},[t._v("下发排产任务")]),a("el-button",{attrs:{type:"default",disabled:!t.multipleSelection.length,loading:t.downloadLoading},on:{click:t.handleDownload}},[t._v("导出加工信息")]),a("el-upload",{staticStyle:{margin:"0 10px"},attrs:{action:t.$baseUrl+"Pro/Nesting/ImportProcess","show-file-list":!1,headers:t.headers,accept:".xlsx,.xls","on-success":function(){return t.fetchData(1)}}},[a("el-button",{attrs:{type:"default"}},[t._v("导入加工信息")])],1),a("el-upload",{staticStyle:{margin:"0 10px"},attrs:{action:t.$baseUrl+"Pro/Nesting/ImportProfiles","show-file-list":!1,"on-success":function(){return t.fetchData(1)},headers:t.headers,accept:".xlsx,.xls"}},[a("el-button",{attrs:{type:"default"}},[t._v("导入型材")])],1),a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":"PRONestingManagementIndex"},on:{updateColumn:t.getTbConfig}})]},proxy:!0}])}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"tb-x"},[t.tbLoading?t._e():a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"checkbox-config":{checkField:"checked"},"empty-render":{name:"NotData"},"row-config":{isCurrent:!0,isHover:!0},align:"center",height:"auto","show-overflow":"","auto-resize":!0,stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox"}}),t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:e.Align,field:e.Code,visible:e.Is_Display,title:e.Display_Name,"min-width":e.Width,"edit-render":e.Is_Edit?{}:null},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.row;return["Type"===e.Code?a("span",[1===r[e.Code]?a("el-tag",{attrs:{effect:"plain",type:"success"}},[t._v("手动导入")]):a("el-tag",{attrs:{effect:"plain",type:"warning"}},[t._v("自动推送")])],1):"Surplus_Raw"===e.Code?a("span",[r.Surplus_Count>0?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleRaw(r)}}},[t._v("查看")]):t._e()],1):"Part_Amount"===e.Code?a("span",[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(e){return t.handleAmount(r)}}},[t._v(t._s(r[e.Code]))])],1):"Utilization"===e.Code?a("span",[t._v(" "+t._s(r[e.Code])),r[e.Code]?a("span",[t._v("%")]):t._e()]):a("span",[t._v(t._s(r[e.Code]))])]}}],null,!0)})]}))],2)],1),a("footer",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[t._v("已选 "+t._s(t.multipleSelection.length)+" 条数据 ")]),a("Pagination",{attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1),t.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.width},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:function(e){return t.fetchData(1)}}})],1):t._e()],1)])},r=[]},9040:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tb-wrapper"},[a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"row-config":{isCurrent:!0,isHover:!0},align:"center",height:"500","show-overflow":"",loading:t.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{fixed:e.fixed,"show-overflow":"tooltip",align:e.Align,field:e.Code,visible:e.Is_Display,title:e.Display_Name,"min-width":e.Width},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.row;return["Type"===e.Code?a("span",[1===r[e.Code]?a("el-tag",{attrs:{effect:"plain",type:"success"}},[t._v("手动导入")]):a("el-tag",{attrs:{effect:"plain",type:"warning"}},[t._v("自动推送")])],1):"Part_Amount"===e.Code?a("span",[a("el-link",{attrs:{type:"primary",underline:!1}},[t._v(t._s(r[e.Code]))])],1):"Img_Url"===e.Code?a("span",[r[e.Code]?a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return t.download(r[e.Code])}}},[t._v("下载")]):a("span",[t._v("-")])],1):a("span",[t._v(t._s(r[e.Code]))])]}}],null,!0)})]}))],2)],1),a("footer",[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("关 闭")])],1)])},r=[]},9259:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a("ed08"),o=a("7f9d"),i=n(a("3796"));e.default={components:{UploadExcel:i.default},data:function(){return{btnLoading:!1}},methods:{beforeUpload:function(t){var e=this;this.btnLoading=!0;var a=new FormData;a.append("Files",t),(0,o.ImportNestingFiles)(a).then((function(t){t.IsSucceed?(e.$message({message:"导入成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):(e.$message({message:t.Message,type:"error"}),t.Data&&window.open((0,r.combineURL)(e.$baseUrl,t.Data))),e.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},handleExport:function(){var t=this;(0,o.GetNestingFiles)({}).then((function(e){e.IsSucceed?window.open((0,r.combineURL)(t.$baseUrl,e.Data)):t.$message({message:e.Message,type:"error"})}))}}}},"93aa":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=$,e.AuxInStoreExport=K,e.AuxReturnByReceipt=X,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=E,e.DeleteInStore=b,e.DeletePicking=Mt,e.ExportCheckReceipt=ft,e.ExportInstoreReceipt=ct,e.ExportMoneyAdjustOrder=wt,e.ExportPicking=xt,e.ExportProcess=Ft,e.ExportTestDetail=yt,e.FindAuxPageList=B,e.FindRawPageList=A,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=Y,e.GetAuxImportTemplate=N,e.GetAuxPageList=at,e.GetAuxPickOutStoreSubList=F,e.GetAuxProcurementDetails=nt,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=v,e.GetImportTemplate=C,e.GetInstoreDetail=g,e.GetMoneyAdjustDetailPageList=It,e.GetOMALatestStatisticTime=k,e.GetOrderDetail=it,e.GetPartyAs=R,e.GetPickLockStoreToChuku=Nt,e.GetPickPlate=$t,e.GetPickSelectPageList=Gt,e.GetPickSelectSubList=jt,e.GetPickingDetail=Lt,e.GetPickingTypeSettingDetail=Dt,e.GetProjectListForTenant=rt,e.GetRawDetailByReceipt=ut,e.GetRawOrderList=ot,e.GetRawPageList=y,e.GetRawPickOutStoreSubList=j,e.GetRawProcurementDetails=I,e.GetRawSurplusReturnStoreDetail=G,e.GetReturnPlate=Ut,e.GetStoreSelectPage=kt,e.GetSuppliers=S,e.GetTestDetail=St,e.GetTestInStoreOrderList=gt,e.Import=L,e.ImportCheckReceipt=mt,e.ImportInstoreReceipt=pt,e.InStoreListSummary=st,e.LockPicking=At,e.ManualAuxInStoreDetail=W,e.ManualInStoreDetail=h,e.MaterielAuxInStoreList=U,e.MaterielAuxManualInStore=H,e.MaterielAuxPurchaseInStore=J,e.MaterielAuxSubmitInStore=z,e.MaterielPartyAInStorel=Q,e.MaterielRawInStoreList=o,e.MaterielRawInStoreListInSumNew=u,e.MaterielRawInStoreListNew=i,e.MaterielRawManualInStore=x,e.MaterielRawPartyAInStore=_,e.MaterielRawPurchaseInStore=w,e.MaterielRawSubmitInStore=l,e.MaterielRawSurplusInStore=O,e.OutStoreListSummary=dt,e.PartAInStoreDetail=m,e.PartyAInInStoreDetail=V,e.PurchaseAuxInStoreDetail=q,e.PurchaseInStoreDetail=p,e.RawInStoreExport=D,e.RawReturnByReceipt=lt,e.RawSurplusReturnStore=T,e.SaveInStore=M,e.SavePicking=Ct,e.SetQualified=Rt,e.SetTestDetail=vt,e.StoreMoneyAdjust=Pt,e.SubmitApproval=f,e.SubmitAuxApproval=c,e.SubmitInStore=ht,e.SubmitPicking=Ot,e.SurplusInStoreDetail=P,e.UnLockPicking=Tt,e.UpdateInvoiceInfo=_t,e.Withdraw=s,e.WithdrawAux=d,e.WithdrawChecked=bt;var r=n(a("b775"));function o(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function i(t){return(0,r.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function g(t){return(0,r.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function w(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function x(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function O(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function M(t){return(0,r.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function k(t){return(0,r.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function L(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function A(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function T(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function G(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function N(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function $(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function U(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function F(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function z(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function E(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function B(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function q(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function V(t){return(0,r.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function W(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function J(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function Q(t){return(0,r.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function H(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function K(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function Y(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function X(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function at(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function nt(t){return(0,r.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function rt(t){return(0,r.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function ot(t){return(0,r.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function it(t){return(0,r.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function ut(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function lt(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function st(t){return(0,r.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function dt(t){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function ct(t){return(0,r.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function ft(t){return(0,r.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function pt(t){return(0,r.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function mt(t){return(0,r.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function ht(t){return(0,r.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function Pt(t){return(0,r.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function gt(t){return(0,r.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function bt(t){return(0,r.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function St(t){return(0,r.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function Rt(t){return(0,r.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function vt(t){return(0,r.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function yt(t){return(0,r.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function It(t){return(0,r.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function wt(t){return(0,r.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function _t(t){return(0,r.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function xt(t){return(0,r.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function Ot(t){return(0,r.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function Mt(t){return(0,r.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function kt(t){return(0,r.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function Ct(t){return(0,r.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function Lt(t){return(0,r.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function Dt(t){return(0,r.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function At(t){return(0,r.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function Tt(t){return(0,r.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function Gt(t){return(0,r.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function jt(t){return(0,r.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function Nt(t){return(0,r.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function $t(t){return(0,r.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function Ut(t){return(0,r.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function Ft(t){return(0,r.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},"95a2":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("14d9"),a("fb6a"),a("e9f5"),a("ab43"),a("d3b7");var r=n(a("bbc2")),o=a("7f9d");e.default={components:{OSSUpload:r.default},data:function(){return{btnLoading:!1,uploadIcon:a("2124"),attachmentList:[]}},methods:{handleUploadSuccess:function(t){this.attachmentList.push({ImgUrl:t.Data.split("*")[0],FileName:this.removeFileExtension(t.Data.split("*")[3])})},removeFileExtension:function(t){var e=t.lastIndexOf(".");return-1===e?t:t.slice(0,e)},handleRemove:function(t,e){var a=this;this.attachmentList=e.map((function(t){return{ImgUrl:t.response.Data.split("*")[0],FileName:a.removeFileExtension(t.response.Data.split("*")[3])}}))},handleSubmit:function(){var t=this;this.btnLoading=!0,(0,o.ImportThumbnail)(this.attachmentList).then((function(e){e.IsSucceed?(t.$message.success("导入成功"),t.$emit("close")):t.$message.error(e.Message)})).finally((function(){t.btnLoading=!1}))}}}},"986d":function(t,e,a){"use strict";a.r(e);var n=a("9259"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},a155:function(t,e,a){},a1dc:function(t,e,a){"use strict";a.r(e);var n=a("bd84"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},a2d1:function(t,e,a){},ae56:function(t,e,a){"use strict";a("783c")},bd84:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("ab43"),a("d3b7");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),u=n(a("15ac")),l=a("7f9d"),s=a("3166");e.default={mixins:[u.default],data:function(){return{form:{Sys_Project_Id:"",Area_Id:"",Part_Name:""},selectParams:{filterable:!0,clearable:!0},treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},tbLoading:!1,projectList:[],tbData:[],columns:[]}},methods:{getData:function(t){var e=this;return(0,i.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return e.tbLoading=!0,e.id=t,a.n=1,e.getTableConfig("PRONestingPartLayout");case 1:return a.n=2,e.getProjectOption();case 2:e.fetchData();case 3:return a.a(2)}}),a)})))()},fetchData:function(){var t=this;this.tbLoading=!0,(0,l.GetNestingPartList)((0,r.default)({Id:this.id},this.form)).then((function(e){e.IsSucceed?t.tbData=e.Data:t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleSubmit:function(){},handleReset:function(){this.$refs["form"].resetFields(),this.fetchData()},getProjectOption:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,s.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){if(e.IsSucceed){var a,n=(null===(a=e.Data)||void 0===a?void 0:a.Data)||[];n.length&&(t.projectList=n,t.getAreaList())}else t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},projectChange:function(){this.form.Area_Id="",this.getAreaList()},getAreaList:function(){var t=this,e=this.form.Sys_Project_Id;if(e){var a=this.projectList.find((function(t){return t.Sys_Project_Id===e}));(0,s.GeAreaTrees)({projectId:a.Id}).then((function(e){e.IsSucceed?(t.treeParamsArea.data=e.Data.map((function(t){return t.Children.length&&(t.disabled=!0),t})),t.$nextTick((function(a){t.$refs.treeSelectArea.treeDataUpdateFun(e.Data)}))):t.$message({message:e.Message,type:"error"})}))}}}}},cc4f5:function(t,e,a){"use strict";a.r(e);var n=a("69fe"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},cc8d:function(t,e,a){"use strict";a.r(e);var n=a("27b0"),r=a("a1dc");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("ae56");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"245331c8",null);e["default"]=u.exports},d210:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("c14f")),o=n(a("1da1")),i=n(a("15ac")),u=a("7f9d"),l=a("6186");e.default={mixins:[i.default],data:function(){return{tbLoading:!1,tbData:[],columns:[]}},methods:{download:function(t){(0,l.GetOssUrl)({url:t}).then((function(t){window.open(t.Data)}))},getData:function(t){var e=this;return(0,o.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return e.tbLoading=!0,e.id=t,a.n=1,e.getTableConfig("PRONestingBalance");case 1:e.fetchData();case 2:return a.a(2)}}),a)})))()},fetchData:function(){var t=this;(0,u.GetNestingSurplusList)({Id:this.id}).then((function(e){e.IsSucceed?t.tbData=e.Data:t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleSubmit:function(){}}}},d418:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"cs-upload-x"},[a("OSSUpload",{staticClass:"textarea",attrs:{drag:"",action:t.$store.state.uploadUrl,"on-success":t.handleUploadSuccess,"on-remove":t.handleRemove,multiple:""}},[[a("el-image",{staticStyle:{width:"96px"},attrs:{src:t.uploadIcon}}),a("div",{staticClass:"el-upload__text"},[a("div",{staticStyle:{color:"#222834","font-size":"14px"}},[t._v("将文件拖到此处，或"),a("em",[t._v("点击上传")])])])]],2)],1),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定 ")])],1)])},r=[]},d7d8:function(t,e,a){"use strict";a("e058")},e058:function(t,e,a){},f175:function(t,e,a){"use strict";a.r(e);var n=a("d418"),r=a("77e94");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("28ef");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"2e656318",null);e["default"]=u.exports}}]);