(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-47a9f792"],{"02e7":function(e,t,a){"use strict";a.r(t);var r=a("732e"),o=a("f7ba");for(var l in o)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(l);var n=a("2877"),s=Object(n["a"])(o["default"],r["a"],r["b"],!1,null,"3fe1f650",null);t["default"]=s.exports},"3c12":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9");var o=r(a("c14f")),l=r(a("1da1")),n=a("3166"),s=a("cf45"),i=a("8975");t.default={props:{isEdit:{type:Boolean,default:!1}},data:function(){return{activeName:"first",activeName2:"first",submitBtnLoading:!1,unitOption:[],form:{Name:"",Code:"",Level:"",Status:"",EPIC_UserName_Bak:"",Contact_UserName_Bak:"",SPIC_UserName_Bak:"",Floor_Num:void 0,Contract_Price:void 0,Contract_Amount:void 0,Plan_Begin_Date:"",Plan_End_Date:"",Actual_Begin_Date:"",Actual_End_Date:"",Survey:"",Construction_Scope:"",Structure_Type:"",Designer:"",Supervisor:"",Contractor:"",Constructor:"",Awards:"",Contract_Scope:"",Contract_Unit_Value:"",Source:2},rules:{Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Code:[{required:!0,message:"请输入编号",trigger:"blur"}],Status:[{required:!0,message:"请选择项目状态",trigger:"change"}]}}},computed:{planTime:{get:function(){return[(0,i.timeFormat)(this.form.Plan_Begin_Date),(0,i.timeFormat)(this.form.Plan_End_Date)]},set:function(e){if(e){var t=e[0],a=e[1];this.form.Plan_Begin_Date=(0,i.timeFormat)(t),this.form.Plan_End_Date=(0,i.timeFormat)(a)}else this.form.Plan_Begin_Date="",this.form.Plan_End_Date=""}},actualTime:{get:function(){return[(0,i.timeFormat)(this.form.Actual_Begin_Date),(0,i.timeFormat)(this.form.Actual_End_Date)]},set:function(e){if(e){var t=e[0],a=e[1];this.form.Actual_Begin_Date=(0,i.timeFormat)(t),this.form.Actual_End_Date=(0,i.timeFormat)(a)}else this.form.Actual_Begin_Date="",this.form.Actual_End_Date=""}}},mounted:function(){var e=this;return(0,l.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.isEdit&&e.fetchData(),t.n=1,(0,s.getDictionary)("unit");case 1:e.unitOption=t.v;case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;(0,n.GetProjectEntity)({id:this.$route.query.id}).then((function(t){t.IsSucceed?Object.assign(e.form,t.Data):e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.submitBtnLoading=!0,(0,n.SaveProject)(e.form).then((function(t){t.IsSucceed?(e.$message({message:e.isEdit?"修改成功":"添加成功",type:"success"}),e.$router.push({name:"PROProjectManagement"})):e.$message({message:t.Message,type:"error"})})),e.submitBtnLoading=!1)}))},handleCancel:function(){this.$router.push({name:"PROProjectManagement"})}}}},"420d":function(e,t,a){"use strict";a.r(t);var r=a("3c12"),o=a.n(r);for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);t["default"]=o.a},"54f6":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return o}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container fff"},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"基本信息",name:"first"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"项目名称：",prop:"Name"}},[a("el-input",{attrs:{maxlength:50,"show-word-limit":""},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目编号：",prop:"Code"}},[a("el-input",{attrs:{maxlength:50,"show-word-limit":""},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目等级：",prop:"Level"}},[a("el-input",{attrs:{maxlength:50,"show-word-limit":""},model:{value:e.form.Level,callback:function(t){e.$set(e.form,"Level",t)},expression:"form.Level"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目状态：",prop:"Status"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择活动区域"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},[a("el-option",{attrs:{label:"未开始",value:"0"}}),a("el-option",{attrs:{label:"进行中",value:"1"}}),a("el-option",{attrs:{label:"已完成",value:"2"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"外协负责人：",prop:"EPIC_UserName_Bak"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.EPIC_UserName_Bak,callback:function(t){e.$set(e.form,"EPIC_UserName_Bak",t)},expression:"form.EPIC_UserName_Bak"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目联络人：",prop:"Contact_UserName_Bak"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Contact_UserName_Bak,callback:function(t){e.$set(e.form,"Contact_UserName_Bak",t)},expression:"form.Contact_UserName_Bak"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目现场负责人：",prop:"SPIC_UserName_Bak"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.SPIC_UserName_Bak,callback:function(t){e.$set(e.form,"SPIC_UserName_Bak",t)},expression:"form.SPIC_UserName_Bak"}})],1)],1)],1)],1)],1),a("el-tabs",{model:{value:e.activeName2,callback:function(t){e.activeName2=t},expression:"activeName2"}},[a("el-tab-pane",{attrs:{label:"详细信息",name:"first"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"层数：",prop:"Floor_Num"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",model:{value:e.form.Floor_Num,callback:function(t){e.$set(e.form,"Floor_Num",t)},expression:"form.Floor_Num"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同总价：",prop:"Contract_Price"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",model:{value:e.form.Contract_Price,callback:function(t){e.$set(e.form,"Contract_Price",t)},expression:"form.Contract_Price"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划时间："}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange",align:"right","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.planTime,callback:function(t){e.planTime=t},expression:"planTime"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合同量：",prop:"Contract_Amount"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{placeholder:"请输入内容"},model:{value:e.form.Contract_Amount,callback:function(t){e.$set(e.form,"Contract_Amount",t)},expression:"form.Contract_Amount"}}),a("el-select",{staticStyle:{width:"91px","margin-left":"10px"},attrs:{placeholder:"请选择"},model:{value:e.form.Contract_Unit_Value,callback:function(t){e.$set(e.form,"Contract_Unit_Value",t)},expression:"form.Contract_Unit_Value"}},e._l(e.unitOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1)],1)])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际时间："}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange",align:"right","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.actualTime,callback:function(t){e.actualTime=t},expression:"actualTime"}})],1)],1)],1)],1)],1),a("el-divider"),a("el-row",{staticStyle:{width:"95%"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"项目概况：",prop:"Survey"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"200","show-word-limit":"",type:"textarea"},model:{value:e.form.Survey,callback:function(t){e.$set(e.form,"Survey",t)},expression:"form.Survey"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"建设范围：",prop:"Construction_Scope"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Construction_Scope,callback:function(t){e.$set(e.form,"Construction_Scope",t)},expression:"form.Construction_Scope"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"结构类型：",prop:"Structure_Type"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Structure_Type,callback:function(t){e.$set(e.form,"Structure_Type",t)},expression:"form.Structure_Type"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"设计单位：",prop:"Designer"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Designer,callback:function(t){e.$set(e.form,"Designer",t)},expression:"form.Designer"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"监理单位：",prop:"Supervisor"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Supervisor,callback:function(t){e.$set(e.form,"Supervisor",t)},expression:"form.Supervisor"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"承包单位：",prop:"Contractor"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Contractor,callback:function(t){e.$set(e.form,"Contractor",t)},expression:"form.Contractor"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"建设单位：",prop:"Constructor"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Constructor,callback:function(t){e.$set(e.form,"Constructor",t)},expression:"form.Constructor"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"获奖情况：",prop:"Awards"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Awards,callback:function(t){e.$set(e.form,"Awards",t)},expression:"form.Awards"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"承包范围：",prop:"Contract_Scope"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:e.form.Contract_Scope,callback:function(t){e.$set(e.form,"Contract_Scope",t)},expression:"form.Contract_Scope"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{type:"primary",loading:e.submitBtnLoading},on:{click:e.handleSubmit}},[e._v("保 存")]),a("el-button",{on:{click:e.handleCancel}},[e._v("取 消")])],1)],1)],1)],1)],1)},o=[]},"732e":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return o}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("detail")],1)},o=[]},"9f66":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(a("b41b"));t.default={name:"PROProjectManagementAdd",components:{detail:o.default},data:function(){return{}}}},b41b:function(e,t,a){"use strict";a.r(t);var r=a("54f6"),o=a("420d");for(var l in o)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(l);a("cf23");var n=a("2877"),s=Object(n["a"])(o["default"],r["a"],r["b"],!1,null,"40460753",null);t["default"]=s.exports},cf23:function(e,t,a){"use strict";a("eacc")},cf45:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=o,a("d3b7");var r=a("6186");function o(e){return new Promise((function(t,a){(0,r.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},eacc:function(e,t,a){},f7ba:function(e,t,a){"use strict";a.r(t);var r=a("9f66"),o=a.n(r);for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);t["default"]=o.a}}]);