(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2e5d6fef","chunk-2d0e2790"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,a,o){return t/=o/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,a){var i=r(),s=t-i,l=20,u=0;e="undefined"===typeof e?500:e;var d=function(){u+=l;var t=Math.easeInOutQuad(u,i,s,e);n(t),u<e?o(d):a&&"function"===typeof a&&a()};d()}},"0cc6":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.isVersionFour?a("v4"):a("v3")],1)},n=[]},"13b3d":function(t,e,a){"use strict";var o=a("dbce").default,n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("5530")),i=n(a("c14f")),s=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("e9c4"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("ddb0");var l=a("ed08"),u=a("7f9d"),d=a("a024"),c=a("7196"),f=a("5e99"),m=n(a("15ac")),p=n(a("2082")),h=n(a("83b4")),P=o(a("313e")),g=n(a("333d")),T=a("c685");e.default={components:{Pagination:g.default},mixins:[m.default,p.default,h.default],data:function(){return{tablePageSize:T.tablePageSize,addPageArray:[{path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-32462350").then(a.bind(null,"c390"))},name:"PROTaskAllocationInfo",meta:{title:"调整分配"}},{path:this.$route.path+"/view",hidden:!0,component:function(){return a.e("chunk-32462350").then(a.bind(null,"c390"))},name:"PROTaskAllocationView",meta:{title:"查看分配"}}],activeName:"2",dialogVisible:!1,pgLoading:!1,tipLabel:"",title:"",currentComponent:"",dWidth:"40%",form:{InstallUnit_Id:"",Project_Id:"",Area_Id:"",Workshop_Id:"",Schduling_Code:"",Process_Code:"",Working_Team_Id:"",Allocate_Status:[1,2]},queryInfo:{Page:1,PageSize:T.tablePageSize[0]},tbConfig:{Op_Width:180},columns:[],tbData:[],processOption:[],groupOption:[],total:0,search:function(){return{}},workShopOption:[],Is_Workshop_Enabled:!1,drawer:!1,myChart:null}},computed:{isCom:function(){return"2"===this.activeName}},activated:function(){this.fetchData()},mounted:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getIsWorkShop();case 1:return e.n=2,t.getTableConfig(t.isCom?"PROTaskAllocationList":"");case 2:t.columns=e.v,t.columns.map((function(e,a){return a===t.columns.length-1&&(e.Min_Width=140),"Workshop_Name"===e.Code&&(e.Is_Display=t.Is_Workshop_Enabled),e})),t.search=(0,l.debounce)(t.fetchData,800,!0),t.getProcessOption(),t.getWorkshopOption();case 3:return e.a(2)}}),e)})))()},methods:{fetchData:function(t){var e=this;this.form.Process_Type=parseInt(this.activeName),t&&(this.queryInfo.Page=t),this.pgLoading=!0,(0,u.GetTeamTaskAllocationPageList)((0,r.default)((0,r.default)({},this.queryInfo),this.form)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.pgLoading=!1}))},getProcessOption:function(){var t=this;(0,d.GetProcessList)({type:2===parseInt(this.activeName)?1:2}).then((function(e){e.IsSucceed?t.processOption=e.Data:t.$message({message:e.Message,type:"error"})}))},taskChange:function(){this.form.Process_Code="",this.form.Working_Team_Id="",this.getProcessOption()},getTeamOption:function(){var t=this,e="",a=this.processOption.find((function(e){return e.Code===t.form.Process_Code}));a&&(e=a.Id),(0,d.GetWorkingTeamBase)({processId:e}).then((function(e){e.IsSucceed?t.groupOption=e.Data:t.$message({message:e.Message,type:"error"})}))},getWorkshopOption:function(){var t=this;(0,c.GetWorkshopPageList)({page:1,pagesize:-1}).then((function(e){e.IsSucceed?t.workShopOption=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getIsWorkShop:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,f.GetCurFactory)({}).then((function(e){e.IsSucceed?t.Is_Workshop_Enabled=e.Data[0].Is_Workshop_Enabled:t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},processChange:function(t){this.form.Working_Team_Id="",t&&this.getTeamOption()},tbSelectChange:function(t){},handleClose:function(){this.dialogVisible=!1},handleReset:function(){this.$refs["form"].resetFields(),this.search(1)},handleDetail:function(t){this.$router.push({name:"PROTaskAllocationInfo",query:{pg_type:this.isCom?"com":"part",pg_redirect:this.isCom?"PROTaskAllocationList":"",Is_Workshop_Enabled:this.Is_Workshop_Enabled,other:encodeURIComponent(JSON.stringify(t))}})},handleView:function(t){this.$router.push({name:"PROTaskAllocationView",query:{type:"view",pg_type:this.isCom?"com":"part",pg_redirect:this.isCom?"PROTaskAllocationList":"",Is_Workshop_Enabled:this.Is_Workshop_Enabled,other:encodeURIComponent(JSON.stringify(t))}})},handleTabsClick:function(t,e){this.form={InstallUnit_Id:"",Project_Id:"",Area_Id:"",Allocate_Status:[1,2],Schduling_Code:"",Process_Code:"",Working_Team_Id:""},this.getProcessOption(),this.fetchData()},drawerOpen:function(){var t=this;this.drawer=!0;var e=[],a=[],o=[];(0,u.GetWorkingTeamLoadRealTime)({type:this.activeName}).then((function(n){n.IsSucceed&&n.Data.length>0&&n.Data.map((function(n){var r,i;e.push(n.Name),a.push(null!==(r=n.Load)&&void 0!==r?r:0),o.push(null!==(i=n.Real_Time_Load)&&void 0!==i?i:0),t.$nextTick((function(){var n=t.$refs.chartDom;null==t.myChart&&(t.myChart=P.init(n));var r={itemStyle:{shadowBlur:10,shadowColor:"rgba(0,0,0,0.3)"}},i={title:{text:"班组负荷实时情况",textStyle:{fontSize:16,color:"#222834"}},tooltip:{show:!0,trigger:"axis"},legend:{icon:"rect",itemWidth:8,itemHeight:4,data:[],textStyle:{fontSize:12,color:"#999999 "}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{data:e,axisLine:{onZero:!0},splitLine:{show:!1},splitArea:{show:!1}},yAxis:{},series:[{name:"负荷提醒线",type:"bar",barGap:"-100%",emphasis:r,data:a,itemStyle:{color:"#91cc75"}},{name:"当前负荷",type:"bar",barGap:"-100%",emphasis:r,data:o,itemStyle:{color:"#5470C6"}}]};t.myChart.setOption(i)}))}))}))}}}},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var o=a("6186"),n=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,o.GetGridByCode)({code:t,IsAll:a}).then((function(t){var o=t.IsSucceed,i=t.Data,s=t.Message;if(o){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||n.tablePageSize[0]),r(e.columns)}else e.$message({message:s,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,o=t.type;this.queryInfo.Page="limit"===o?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var o=0;o<this.columns.length;o++){var n=this.columns[o];if(n.Code===e){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},3166:function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=d,e.GeAreaTrees=v,e.GetFileSync=S,e.GetInstallUnitIdNameList=C,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=_,e.GetProjectAreaTreeList=y,e.GetProjectEntity=l,e.GetProjectList=s,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=b,e.GetSchedulingPartList=O,e.IsEnableProjectMonomer=c,e.SaveProject=u,e.UpdateProjectTemplateBase=P,e.UpdateProjectTemplateContacts=g,e.UpdateProjectTemplateContract=T,e.UpdateProjectTemplateOther=k;var n=o(a("b775")),r=o(a("4328"));function i(t){return(0,n.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function u(t){return(0,n.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function d(t){return(0,n.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function c(t){return(0,n.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,n.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,n.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,n.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,n.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function g(t){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function T(t){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function k(t){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function b(t){return(0,n.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function y(t){return(0,n.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function C(t){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function v(t){return(0,n.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function _(t){return(0,n.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function O(t){return(0,n.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function S(t){return(0,n.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"319a":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pgLoading,expression:"pgLoading"}],staticClass:"container abs100"},[a("el-tabs",{on:{"tab-click":t.handleTabsClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"构件",name:"2"}}),a("el-tab-pane",{attrs:{label:"部件",name:"3"}}),a("el-tab-pane",{attrs:{label:"零件",name:"1"}})],1),a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",attrs:{model:t.form,inline:"","label-width":"80px"}},[a("el-button",{attrs:{type:"primary"},on:{click:t.drawerOpen}},[t._v("班组负荷")]),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},t._l(t.ProjectNameData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),[a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!t.form.Project_Id,"select-params":{clearable:!0},"tree-params":t.treeParamsArea},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1)],[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",attrs:{disabled:!t.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:t.setupPositionChange},model:{value:t.form.InstallUnit_Id,callback:function(e){t.$set(t.form,"InstallUnit_Id",e)},expression:"form.InstallUnit_Id"}},t._l(t.SetupPositionData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)],a("el-form-item",{attrs:{label:"任务工序",prop:"Process_Code"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},on:{change:t.processChange},model:{value:t.form.Process_Code,callback:function(e){t.$set(t.form,"Process_Code",e)},expression:"form.Process_Code"}},t._l(t.processOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Code}})})),1)],1),a("el-form-item",{attrs:{label:"加工班组",prop:"Working_Team_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!t.form.Process_Code,clearable:"",placeholder:"请选择"},model:{value:t.form.Working_Team_Id,callback:function(e){t.$set(t.form,"Working_Team_Id",e)},expression:"form.Working_Team_Id"}},t._l(t.groupOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),t.Is_Workshop_Enabled?a("el-form-item",{attrs:{label:"所属车间",prop:"Workshop_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Workshop_Id,callback:function(e){t.$set(t.form,"Workshop_Id",e)},expression:"form.Workshop_Id"}},t._l(t.workShopOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1):t._e(),a("el-form-item",{attrs:{label:"排产单号",prop:"Schduling_Code"}},[a("el-input",{attrs:{type:"text"},model:{value:t.form.Schduling_Code,callback:function(e){t.$set(t.form,"Schduling_Code",e)},expression:"form.Schduling_Code"}})],1),a("el-form-item",{attrs:{label:"是否分配",prop:"Allocate_Status"}},[a("el-select",{attrs:{multiple:"",placeholder:"请选择",clearable:""},model:{value:t.form.Allocate_Status,callback:function(e){t.$set(t.form,"Allocate_Status",e)},expression:"form.Allocate_Status"}},[a("el-option",{attrs:{label:"未分配",value:1}}),a("el-option",{attrs:{label:"已分配",value:2}}),a("el-option",{attrs:{label:"分配完成",value:3}})],1)],1),a("el-form-item",[a("el-button",{on:{click:t.handleReset}},[t._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.search(1)}}},[t._v("搜索")])],1)],2)],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"tb-x"},[a("vxe-table",{key:t.activeName,staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"","row-config":{isCurrent:!0,isHover:!0},loading:t.pgLoading,align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e,o){return a("vxe-column",{key:e.Code,attrs:{align:e.Align,fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:e.Code,title:e.Display_Name,"min-width":e.Width||120,visible:e.visible},scopedSlots:t._u(["Total_Allocation_Count"===e.Code?{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.Total_Allocation_Count-a.Total_Receive_Count===a.Can_Allocation_Count?"分配完成":a.Total_Allocation_Count>0?"已分配":"未分配")+" ")]}}:"Finish_Date"===e.Code||"Order_Date"===e.Code?{key:"default",fn:function(a){var o=a.row;return[t._v(" "+t._s(t._f("timeFormat")(o[e.Code]))+" ")]}}:{key:"default",fn:function(o){var n=o.row;return[a("span",[t._v(t._s(t._f("displayValue")(0===n[e.Code]?0:n[e.Code])))])]}}],null,!0)})})),a("vxe-column",{attrs:{title:"操作",fixed:"right",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;e.rowIndex;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleView(o)}}},[t._v("查看")]),0!==o.Can_Allocation_Count?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleDetail(o)}}},[t._v("任务分配 ")]):t._e()]}}])})],2)],1),a("Pagination",{attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1),t.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.dWidth},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:function(e){return t.fetchData(1)}}})],1):t._e(),a("el-drawer",{attrs:{size:"60%","custom-class":"drawerBox",visible:t.drawer,direction:"btt","with-header":!1,"append-to-body":"","wrapper-closable":""},on:{"update:visible":function(e){t.drawer=e}}},[a("div",{staticClass:"chartWrapper"},[a("div",{ref:"chartDom",staticStyle:{width:"100%",height:"100%"}})])])],1)},n=[]},"41eb":function(t,e,a){"use strict";var o=a("dbce").default,n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("5530")),i=n(a("c14f")),s=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("e9c4"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("ddb0");var l=a("ed08"),u=a("7f9d"),d=a("a024"),c=a("7196"),f=a("5e99"),m=n(a("15ac")),p=n(a("2082")),h=n(a("83b4")),P=o(a("313e")),g=n(a("333d")),T=a("c685");e.default={name:"PROTaskAllocationList",components:{Pagination:g.default},mixins:[m.default,p.default,h.default],data:function(){return{tablePageSize:T.tablePageSize,addPageArray:[{path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-259f97b9").then(a.bind(null,"f9a8"))},name:"PROTaskAllocationInfo",meta:{title:"调整分配"}},{path:this.$route.path+"/view",hidden:!0,component:function(){return a.e("chunk-259f97b9").then(a.bind(null,"f9a8"))},name:"PROTaskAllocationView",meta:{title:"查看分配"}}],activeName:"2",dialogVisible:!1,pgLoading:!1,tipLabel:"",title:"",currentComponent:"",dWidth:"40%",form:{InstallUnit_Id:"",Project_Id:"",Area_Id:"",Workshop_Id:"",Schduling_Code:"",Process_Code:"",Working_Team_Id:"",Allocate_Status:[1,2]},queryInfo:{Page:1,PageSize:T.tablePageSize[0]},tbConfig:{Op_Width:180},columns:[],tbData:[],processOption:[],groupOption:[],total:0,search:function(){return{}},workShopOption:[],Is_Workshop_Enabled:!1,drawer:!1,myChart:null}},computed:{isCom:function(){return"2"===this.activeName},isUnitPart:function(){return"3"===this.activeName}},activated:function(){this.fetchData()},mounted:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getIsWorkShop();case 1:return e.n=2,t.getCurColumns();case 2:t.search=(0,l.debounce)(t.fetchData,800,!0),t.getProcessOption(),t.getWorkshopOption();case 3:return e.a(2)}}),e)})))()},methods:{getCurColumns:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig("3"===t.activeName?"PROTaskUnitAllocationList":"PROTaskAllocationList");case 1:t.columns=e.v,t.columns=t.columns.map((function(e,a){return a===t.columns.length-1&&(e.Min_Width=140),"Workshop_Name"===e.Code&&(e.Is_Display=t.Is_Workshop_Enabled),e}));case 2:return e.a(2)}}),e)})))()},fetchData:function(t){var e=this;this.form.Process_Type=parseInt(this.activeName),t&&(this.queryInfo.Page=t),this.pgLoading=!0,(0,u.GetTeamTaskAllocationPageList)((0,r.default)((0,r.default)({},this.queryInfo),this.form)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.pgLoading=!1}))},getProcessOption:function(){var t=this,e={1:2,2:1,3:3},a=e[+this.activeName];(0,d.GetProcessList)({type:a}).then((function(e){e.IsSucceed?t.processOption=e.Data:t.$message({message:e.Message,type:"error"})}))},taskChange:function(){this.form.Process_Code="",this.form.Working_Team_Id="",this.getProcessOption()},getTeamOption:function(){var t=this,e="",a=this.processOption.find((function(e){return e.Code===t.form.Process_Code}));a&&(e=a.Id),(0,d.GetWorkingTeamBase)({processId:e}).then((function(e){e.IsSucceed?t.groupOption=e.Data:t.$message({message:e.Message,type:"error"})}))},getWorkshopOption:function(){var t=this;(0,c.GetWorkshopPageList)({page:1,pagesize:-1}).then((function(e){e.IsSucceed?t.workShopOption=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getIsWorkShop:function(){var t=this;return(0,s.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,f.GetCurFactory)({}).then((function(e){e.IsSucceed?t.Is_Workshop_Enabled=e.Data[0].Is_Workshop_Enabled:t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},processChange:function(t){this.form.Working_Team_Id="",t&&this.getTeamOption()},tbSelectChange:function(t){},handleClose:function(){this.dialogVisible=!1},handleReset:function(){this.$refs["form"].resetFields(),this.search(1)},handleDetail:function(t){this.$router.push({name:"PROTaskAllocationInfo",query:{pg_type:this.isCom?"com":this.isUnitPart?"unitPart":"part",pg_redirect:"PROTaskAllocationList",Is_Workshop_Enabled:this.Is_Workshop_Enabled,other:encodeURIComponent(JSON.stringify(t))}})},handleView:function(t){this.$router.push({name:"PROTaskAllocationView",query:{type:"view",pg_type:this.isCom?"com":this.isUnitPart?"unitPart":"part",pg_redirect:"PROTaskAllocationList",Is_Workshop_Enabled:this.Is_Workshop_Enabled,other:encodeURIComponent(JSON.stringify(t))}})},handleTabsClick:function(t,e){this.form={InstallUnit_Id:"",Project_Id:"",Area_Id:"",Allocate_Status:[1,2],Schduling_Code:"",Process_Code:"",Working_Team_Id:""},this.getCurColumns(),this.getProcessOption(),this.fetchData()},drawerOpen:function(){var t=this;this.drawer=!0;var e=[],a=[],o=[];(0,u.GetWorkingTeamLoadRealTime)({type:this.activeName}).then((function(n){n.IsSucceed&&n.Data&&n.Data.length>0&&n.Data.map((function(n){var r,i;e.push(n.Name),a.push(null!==(r=n.Load)&&void 0!==r?r:0),o.push(null!==(i=n.Real_Time_Load)&&void 0!==i?i:0),t.$nextTick((function(){var n=t.$refs.chartDom;null==t.myChart&&(t.myChart=P.init(n));var r={itemStyle:{shadowBlur:10,shadowColor:"rgba(0,0,0,0.3)"}},i={title:{text:"班组负荷实时情况",textStyle:{fontSize:16,color:"#222834"}},tooltip:{show:!0,trigger:"axis"},legend:{icon:"rect",itemWidth:8,itemHeight:4,data:[],textStyle:{fontSize:12,color:"#999999 "}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{data:e,axisLine:{onZero:!0},splitLine:{show:!1},splitArea:{show:!1}},yAxis:{},series:[{name:"负荷提醒线",type:"bar",barGap:"-100%",emphasis:r,data:a,itemStyle:{color:"#91cc75"}},{name:"当前负荷",type:"bar",barGap:"-100%",emphasis:r,data:o,itemStyle:{color:"#5470C6"}}]};t.myChart.setOption(i)}))}))}))}}}},"43bb":function(t,e,a){"use strict";a.r(e);var o=a("cfe3"),n=a("7540");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("ed6d");var i=a("2877"),s=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"5d21c88a",null);e["default"]=s.exports},5106:function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(a("5530")),r=a("2f62"),i=o(a("43bb")),s=o(a("b579"));e.default={name:"PROTaskAllocationList",components:{v3:i.default,v4:s.default},computed:(0,n.default)({},(0,r.mapGetters)("tenant",["isVersionFour"]))}},7196:function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteWorkshop=u,e.GetFactoryPeoplelist=r,e.GetWorkshopEntity=l,e.GetWorkshopPageList=s,e.SaveEntity=i;var n=o(a("b775"));function r(t){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function i(t){return(0,n.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:t})}function u(t){return(0,n.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:t})}},7540:function(t,e,a){"use strict";a.r(e);var o=a("13b3d"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);e["default"]=n.a},"7f9d":function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPartTeamProcessAllocation=$t,e.AdjustSubAssemblyTeamProcessAllocation=Dt,e.AdjustTeamProcessAllocation=Wt,e.ApplyCheck=Vt,e.BatchReceiveTaskFromStock=Ge,e.BatchReceiveTransferTask=Nt,e.BatchWithdrawSimplifiedProcessingHistory=he,e.BeginProcess=z,e.BochuAddTask=qe,e.CancelNestingBill=Z,e.CancelSchduling=kt,e.CancelTransferTask=Ft,e.CancelUnitSchduling=ae,e.CheckSchduling=zt,e.ComponentAllocationWorkingTeam=f,e.ComponentAllocationWorkingTeamBase=m,e.CreateCompTransferBill=M,e.CreateCompTransferByTransferCode=K,e.CreatePartTransferByPartCodes=Q,e.CreatePartTransferByTaskBill=q,e.CreatePartTransferByTransferCode=Y,e.DelSchdulingPlan=bt,e.DelSchdulingPlanById=yt,e.DeleteNestingResult=ke,e.DownNestingTaskTemplate=at,e.ExportAllocationComponent=v,e.ExportSimplifiedProcessingHistory=je,e.ExportTaskCodeDetails=Ot,e.ExportTransferCodeDetail=jt,e.GetAllWorkingTeamComponentCount=h,e.GetAllWorkingTeamComponentCountBase=g,e.GetAllWorkingTeamPartCount=I,e.GetAllWorkingTeamPartCountBase=P,e.GetBuildReturnRecordList=ue,e.GetCanSchdulingComps=lt,e.GetCanSchdulingPartList=dt,e.GetCheckUserRankList=Ae,e.GetCheckingItemList=De,e.GetCheckingQuestionList=we,e.GetCompSchdulingInfoDetail=ut,e.GetCompSchdulingPageList=pt,e.GetCompTaskPageList=ce,e.GetCompTaskPartCompletionStock=Ue,e.GetComponentPartComplexity=T,e.GetCoordinateProcess=y,e.GetDetailSummaryList=$e,e.GetDwg=Pe,e.GetMonthlyFullCheckProducedData=Be,e.GetNestingBillBoardPageList=F,e.GetNestingBillDetailList=xe,e.GetNestingBillPageList=L,e.GetNestingBillTreeList=Ne,e.GetNestingFiles=be,e.GetNestingMaterialWithPart=Ze,e.GetNestingPartList=Ce,e.GetNestingResultPageList=ge,e.GetNestingSurplusList=Te,e.GetNestingTaskInfoDetail=U,e.GetPageProcessTransferDetailBase=it,e.GetPageSchdulingComps=st,e.GetPartPrepareList=Xt,e.GetPartSchdulingCancelHistory=Ut,e.GetPartSchdulingInfoDetail=Yt,e.GetPartSchdulingPageList=ct,e.GetPartTaskBoard=$,e.GetPartWithParentPageList=Je,e.GetProcessAllocationComponentBasePageList=C,e.GetProcessAllocationComponentPageList=s,e.GetProcessAllocationPartBasePageList=O,e.GetProcessAllocationPartPageList=_,e.GetProcessPartTransferDetail=tt,e.GetProcessTransferDetail=X,e.GetProcessTransferPageList=E,e.GetSchdulingCancelHistory=Gt,e.GetSchdulingPageList=mt,e.GetSchdulingPartUsePageList=Mt,e.GetSchdulingWorkingTeams=Pt,e.GetSemiFinishedStock=ve,e.GetSimplifiedProcessingHistory=me,e.GetSimplifiedProcessingSummary=pe,e.GetSourceFinishedList=_e,e.GetStopList=Qe,e.GetTargetReceiveList=Oe,e.GetTaskPartPrepareList=te,e.GetTeamCompHistory=nt,e.GetTeamPartUseList=qt,e.GetTeamProcessAllocation=At,e.GetTeamStockPageList=rt,e.GetTeamTaskAllocationPageList=Lt,e.GetTeamTaskBoardPageList=N,e.GetTeamTaskDetails=_t,e.GetTeamTaskPageList=vt,e.GetTenantFactoryType=ot,e.GetToReceiveTaskDetailList=Ie,e.GetToReceiveTaskList=Se,e.GetTransferCancelDetails=Et,e.GetTransferDetail=Bt,e.GetTransferHistory=wt,e.GetTransferPageList=x,e.GetUnitSchdulingInfoDetail=Kt,e.GetUnitSchdulingPageList=ht,e.GetWorkingTeamCheckingList=We,e.GetWorkingTeamComponentCount=p,e.GetWorkingTeamComponentCountBase=u,e.GetWorkingTeamLoadRealTime=de,e.GetWorkingTeamPartCount=R,e.GetWorkingTeamPartCountBase=G,e.GetWorkingTeamsPageList=se,e.GetYearlyFullCheckProducedData=Le,e.ImportNestingFiles=ye,e.ImportNestingInfo=k,e.ImportSchduling=gt,e.ImportThumbnail=Ve,e.ImportUpdateComponentWorkingTeam=i,e.ImportUpdatePartWorkingTeam=S,e.LentakExport=ze,e.PartsAllocationWorkingTeamBase=W,e.PartsAllocationWrokingTeam=A,e.PartsBatchAllocationWorkingTeamBase=D,e.ProAddQuest=et,e.ProfilesExport=Me,e.ReceiveTaskFromStock=Re,e.ReceiveTransferBill=H,e.ReceiveTransferTask=xt,e.RevocationComponentAllocation=l,e.SaveChangeZeroComponentRecoil=le,e.SaveCompSchdulingDraft=Ht,e.SaveCompTransferBill=V,e.SaveComponentSchedulingWorkshop=oe,e.SaveNestingPartInfo=j,e.SavePartSchdulingDraft=Jt,e.SavePartSchdulingDraftNew=Zt,e.SavePartSchedulingWorkshop=ne,e.SavePartSchedulingWorkshopNew=re,e.SavePartTransferBill=J,e.SaveSchdulingDraft=ft,e.SaveSchdulingTask=Tt,e.SaveSchdulingTaskById=Ct,e.SaveUnitSchdulingDraftNew=Qt,e.SaveUnitSchedulingWorkshopNew=ie,e.SigmaWOLExport=Ee,e.SimplifiedProcessing=fe,e.TeamProcessingByTaskCode=It,e.TeamTaskProcessing=Rt,e.TeamTaskTransfer=St,e.UpdateBatchCompAllocationWorkingTeamBase=c,e.UpdateBatchPartsAllocationWrokingTeamBase=B,e.UpdateComponentAllocationWorkingTeamBase=d,e.UpdateMachineName=Fe,e.UpdatePartsAllocationWorkingTeamBase=w,e.UploadNestingFiles=b,e.WithdrawPicking=He,e.WithdrawScheduling=ee;var n=o(a("b775")),r=o(a("4328"));function i(t){return(0,n.default)({url:"/PRO/ProductionTask/ImportUpdateComponentWorkingTeam",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentPageList",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/ProductionTask/RevocationComponentAllocation",method:"post",data:t})}function u(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCountBase",method:"post",data:t})}function d(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdateComponentAllocationWorkingTeamBase",method:"post",data:t})}function c(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdateBatchCompAllocationWorkingTeamBase",method:"post",data:t})}function f(t){return(0,n.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeam",method:"post",data:r.default.stringify(t)})}function m(t){return(0,n.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeamBase",method:"post",data:t})}function p(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCount",method:"post",data:r.default.stringify(t)})}function h(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCount",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCountBase",method:"post",data:t})}function g(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCountBase",method:"post",data:t})}function T(t){return(0,n.default)({url:"/PRO/ProductionTask/GetComponentPartComplexity",method:"post",data:r.default.stringify(t)})}function k(t){return(0,n.default)({url:"/PRO/ProductionTask/ImportNestingInfo",method:"post",data:t})}function b(t){return(0,n.default)({url:"/PRO/ProductionTask/UploadNestingFiles",method:"post",data:t})}function y(t){return(0,n.default)({url:"/PRO/ProductionTask/GetCoordinateProcess",method:"post",data:r.default.stringify(t)})}function C(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentBasePageList",method:"post",data:t})}function v(t){return(0,n.default)({url:"/PRO/ProductionTask/ExportAllocationComponent",method:"post",data:t})}function _(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartPageList",method:"post",data:t})}function O(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartBasePageList",method:"post",data:t})}function S(t){return(0,n.default)({url:"/PRO/ProductionTask/ImportUpdatePartWorkingTeam",method:"post",data:t})}function I(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCount",method:"post",data:t})}function R(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCount",method:"post",data:r.default.stringify(t)})}function G(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCountBase",method:"post",data:r.default.stringify(t)})}function L(t){return(0,n.default)({url:"/PRO/ProductionTask/GetNestingBillPageList",method:"post",data:t})}function A(t){return(0,n.default)({url:"/PRO/ProductionTask/PartsAllocationWrokingTeam",method:"post",data:r.default.stringify(t)})}function W(t){return(0,n.default)({url:"/PRO/ProductionTask/PartsAllocationWorkingTeamBase",method:"post",data:r.default.stringify(t)})}function D(t){return(0,n.default)({url:"/PRO/ProductionTask/PartsBatchAllocationWorkingTeamBase",method:"post",data:t})}function w(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdatePartsAllocationWorkingTeamBase",method:"post",data:t})}function B(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdateBatchPartsAllocationWrokingTeamBase",method:"post",data:t})}function j(t){return(0,n.default)({url:"/PRO/ProductionTask/SaveNestingPartInfo",method:"post",data:r.default.stringify(t)})}function U(t){return(0,n.default)({url:"/PRO/ProductionTask/GetNestingTaskInfoDetail",method:"post",data:r.default.stringify(t)})}function N(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskBoardPageList",method:"post",data:t})}function x(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferPageList",method:"post",data:t})}function $(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPartTaskBoard",method:"post",data:t})}function F(t){return(0,n.default)({url:"/PRO/ProductionTask/GetNestingBillBoardPageList",method:"post",data:t})}function E(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessTransferPageList",method:"post",data:t})}function z(t){return(0,n.default)({url:"/PRO/ProductionTask/BeginProcess",method:"post",data:r.default.stringify(t)})}function M(t){return(0,n.default)({url:"/PRO/ProductionTask/CreateCompTransferBill",method:"post",data:t})}function q(t){return(0,n.default)({url:"/PRO/ProductionTask/CreatePartTransferByTaskBill",method:"post",data:t})}function V(t){return(0,n.default)({url:"/PRO/ProductionTask/SaveCompTransferBill",method:"post",data:t})}function H(t){return(0,n.default)({url:"/PRO/ProductionTask/ReceiveTransferBill",method:"post",data:r.default.stringify(t)})}function J(t){return(0,n.default)({url:"/PRO/ProductionTask/SavePartTransferBill",method:"post",data:t})}function Z(t){return(0,n.default)({url:"/PRO/ProductionTask/CancelNestingBill",method:"post",data:r.default.stringify(t)})}function Q(t){return(0,n.default)({url:"/PRO/ProductionTask/CreatePartTransferByPartCodes",method:"post",data:t})}function Y(t){return(0,n.default)({url:"/PRO/ProductionTask/CreatePartTransferByTransferCode",method:"post",data:r.default.stringify(t)})}function K(t){return(0,n.default)({url:"/PRO/ProductionTask/CreateCompTransferByTransferCode",method:"post",data:t})}function X(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessTransferDetail",method:"post",data:r.default.stringify(t)})}function tt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessPartTransferDetail",method:"post",data:r.default.stringify(t)})}function et(t){return(0,n.default)({url:"/PRO/ProductionTask/ProAddQuest",method:"post",data:r.default.stringify(t)})}function at(t){return(0,n.default)({url:"/PRO/ProductionTask/DownNestingTaskTemplate",method:"post",data:t})}function ot(){return(0,n.default)({url:"/PRO/ProductionTask/GetTenantFactoryType",method:"post"})}function nt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamCompHistory",method:"post",data:t})}function rt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamStockPageList",method:"post",data:t})}function it(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPageProcessTransferDetailBase",method:"post",data:t})}function st(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPageSchdulingComps",method:"post",data:t})}function lt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetCanSchdulingComps",method:"post",data:t})}function ut(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingInfoDetail",method:"post",data:t})}function dt(t){return(0,n.default)({url:"/PRO/nesting/GetCanSchdulingPartList",method:"post",data:t})}function ct(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingPageList",method:"post",data:t})}function ft(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveSchdulingDraft",method:"post",data:t})}function mt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingPageList",method:"post",data:t})}function pt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingPageList",method:"post",data:t})}function ht(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingPageList",method:"post",data:t})}function Pt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingWorkingTeams",method:"post",data:t})}function gt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/ImportCompSchduling",method:"post",timeout:12e5,data:t})}function Tt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTask",method:"post",data:t})}function kt(t){return(0,n.default)({url:"/PRO/ProductionTask/CancelSchduling",method:"post",data:t})}function bt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlan",method:"post",data:t})}function yt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlanById",method:"post",data:t})}function Ct(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTaskById",method:"post",data:t,timeout:12e5})}function vt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskPageList",method:"post",data:t})}function _t(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskDetails",method:"post",data:t})}function Ot(t){return(0,n.default)({url:"/PRO/ProductionTask/ExportTaskCodeDetails",method:"post",data:t})}function St(t,e){return(0,n.default)({url:"/PRO/ProductionTask/TeamTaskTransfer",method:"post",data:t,params:e})}function It(t){return(0,n.default)({url:"/PRO/ProductionTask/TeamProcessingByTaskCode",method:"post",data:t})}function Rt(t){return(0,n.default)({url:"/PRO/ProductionTask/TeamTaskProcessing",method:"post",data:t})}function Gt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingCancelHistory",method:"post",data:t})}function Lt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskAllocationPageList",method:"post",data:t})}function At(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamProcessAllocation",method:"post",data:t})}function Wt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/AdjustCompTeamProcessAllocation",method:"post",data:t})}function Dt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/AdjustSubAssemblyTeamProcessAllocation",method:"post",data:t})}function wt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferHistory",method:"post",data:t})}function Bt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferDetail",method:"post",data:t})}function jt(t){return(0,n.default)({url:"/PRO/ProductionTask/ExportTransferCodeDetail",method:"post",data:t})}function Ut(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPartSchdulingCancelHistory",method:"post",data:t})}function Nt(t){return(0,n.default)({url:"/PRO/ProductionTask/BatchReceiveTransferTask",method:"post",data:r.default.stringify(t)})}function xt(t){return(0,n.default)({url:"/PRO/ProductionTask/ReceiveTransferTask",method:"post",data:t})}function $t(t){return(0,n.default)({url:"/PRO/ProductionSchduling/AdjustPartTeamProcessAllocation",method:"post",data:t})}function Ft(t){return(0,n.default)({url:"/PRO/ProductionTask/CancelTransferTask",method:"post",data:t})}function Et(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferCancelDetails",method:"post",data:t})}function zt(t){return(0,n.default)({url:"/PRO/ProductionTask/CheckSchduling",method:"post",data:r.default.stringify(t)})}function Mt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingPartUsePageList",method:"post",data:t})}function qt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamPartUseList",method:"post",data:t})}function Vt(t){return(0,n.default)({url:"/PRO/ProductionTask/ApplyCheck",method:"post",data:t})}function Ht(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveCompSchdulingDraft",method:"post",data:t,timeout:12e5})}function Jt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraft",method:"post",data:t,timeout:12e5})}function Zt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraftNew",method:"post",data:t,timeout:12e5})}function Qt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveUnitSchdulingDraftNew",method:"post",data:t,timeout:12e5})}function Yt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingInfoDetail",method:"post",data:t,timeout:12e5})}function Kt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingInfoDetail",method:"post",data:t,timeout:12e5})}function Xt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPartPrepareList",method:"post",data:t})}function te(t){return(0,n.default)({url:"/pro/productiontask/GetTaskPartPrepareList",method:"post",data:t})}function ee(t){return(0,n.default)({url:"/PRO/ProductionSchduling/WithdrawScheduling",method:"post",data:t})}function ae(t){return(0,n.default)({url:"/PRO/ProductionSchduling/CancelUnitSchduling",method:"post",data:t})}function oe(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveComponentSchedulingWorkshop",method:"post",data:t})}function ne(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshop",method:"post",data:t})}function re(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshopNew",method:"post",data:t})}function ie(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveUnitSchedulingWorkshopNew",method:"post",data:t})}function se(t){return(0,n.default)({url:"/PRO/ZeroComponentRecoil/GetWorkingTeamsPageList",method:"post",data:t})}function le(t){return(0,n.default)({url:"/PRO/ZeroComponentRecoil/SaveChangeZeroComponentRecoil",method:"post",data:t})}function ue(t){return(0,n.default)({url:"/PRO/ZeroComponentRecoil/GetBuildReturnRecordList",method:"post",data:t})}function de(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamLoadRealTime",method:"post",data:t})}function ce(t){return(0,n.default)({url:"/PRO/ProductionTask/GetCompTaskPageList",method:"post",data:t})}function fe(t){return(0,n.default)({url:"/PRO/ProductionTask/SimplifiedProcessing",method:"post",data:t})}function me(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingHistory",method:"post",data:t})}function pe(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingSummary",method:"post",data:t})}function he(t){return(0,n.default)({url:"/PRO/ProductionTask/BatchWithdrawSimplifiedProcessingHistory",method:"post",data:t})}function Pe(t){return(0,n.default)({url:"/PRO/ProductionTask/GetDwg",method:"post",data:t})}function ge(t){return(0,n.default)({url:"/PRO/nesting/GetNestingResultPageList",method:"post",data:t})}function Te(t){return(0,n.default)({url:"/PRO/nesting/GetNestingSurplusList",method:"post",data:t})}function ke(t){return(0,n.default)({url:"/PRO/nesting/DeleteNestingResult",method:"post",data:t})}function be(t){return(0,n.default)({url:"/PRO/nesting/GetNestingFiles",method:"post",data:t})}function ye(t){return(0,n.default)({url:"/PRO/nesting/Import",method:"post",data:t})}function Ce(t){return(0,n.default)({url:"/PRO/nesting/GetNestingPartList",method:"post",data:t})}function ve(t){return(0,n.default)({url:"/PRO/productiontask/GetSemiFinishedStock",method:"post",data:t})}function _e(t){return(0,n.default)({url:"/PRO/productiontask/GetSourceFinishedList",method:"post",data:t})}function Oe(t){return(0,n.default)({url:"/PRO/productiontask/GetTargetReceiveList",method:"post",data:t})}function Se(t){return(0,n.default)({url:"/PRO/productiontask/GetToReceiveTaskList",method:"post",data:t})}function Ie(t){return(0,n.default)({url:"/PRO/productiontask/GetToReceiveTaskDetailList",method:"post",data:t})}function Re(t){return(0,n.default)({url:"/PRO/productiontask/ReceiveTaskFromStock",method:"post",data:t})}function Ge(t){return(0,n.default)({url:"/PRO/productiontask/BatchReceiveTaskFromStock",method:"post",data:t})}function Le(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetYearlyFullCheckProducedData",method:"post",data:t})}function Ae(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetCheckUserRankList",method:"post",data:t})}function We(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetWorkingTeamCheckingList",method:"post",data:t})}function De(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetCheckingItemList",method:"post",data:t})}function we(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetCheckingQuestionList",method:"post",data:t})}function Be(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetMonthlyFullCheckProducedData",method:"post",data:t})}function je(t){return(0,n.default)({url:"/PRO/productiontask/ExportSimplifiedProcessingHistory",method:"post",data:t})}function Ue(t){return(0,n.default)({url:"/PRO/productiontask/GetCompTaskPartCompletionStock",method:"post",data:t})}function Ne(t){return(0,n.default)({url:"/Pro/NestingBill/GetPageList",method:"post",data:t})}function xe(t){return(0,n.default)({url:"/Pro/NestingBill/GetDetailList",method:"post",data:t})}function $e(t){return(0,n.default)({url:"/Pro/NestingBill/GetDetailSummaryList",method:"post",data:t})}function Fe(t){return(0,n.default)({url:"/Pro/NestingBill/UpdateMachineName",method:"post",data:t})}function Ee(t){return(0,n.default)({url:"/PRO/CustomUssl/SigmaWOLExport",method:"post",data:t})}function ze(t){return(0,n.default)({url:"/PRO/CustomUssl/LentakExport",method:"post",data:t})}function Me(t){return(0,n.default)({url:"/PRO/CustomUssl/ProfilesExport",method:"post",data:t})}function qe(t){return(0,n.default)({url:"/PRO/CustomUssl/BochuAddTask",method:"post",data:t})}function Ve(t){return(0,n.default)({url:"/Pro/Nesting/ImportThumbnail",method:"post",data:t})}function He(t){return(0,n.default)({url:"/Pro/MaterielPicking/WithdrawPicking",method:"post",data:t})}function Je(t){return(0,n.default)({url:"/PRO/productiontask/GetPartWithParentPageList",method:"post",data:t})}function Ze(t){return(0,n.default)({url:"/PRO/productiontask/GetNestingMaterialWithPart",method:"post",data:t})}function Qe(t){return(0,n.default)({url:"/PRO/MOC/GetStopList",method:"post",data:t})}},"7fc5":function(t,e,a){},"83b4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("7d54"),a("d3b7"),a("159b");var o=a("3166"),n=a("f2f6");e.default={data:function(){return{ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},SetupPositionData:[]}},mounted:function(){this.getProjectOption()},methods:{getProjectOption:function(){var t=this;(0,o.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getAreaList:function(){var t=this,e=this.form.Project_Id?this.form.Project_Id:this.form.ProjectId;(0,o.GeAreaTrees)({projectId:e}).then((function(e){if(e.IsSucceed){var a=e.Data;t.setDisabledTree(a),t.treeParamsArea.data=a,t.$nextTick((function(e){var o;null===(o=t.$refs.treeSelectArea)||void 0===o||o.treeDataUpdateFun(a)}))}else t.$message({message:e.Message,type:"error"})}))},getInstall:function(){var t=this;(0,n.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.SetupPositionData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},projectChangeSingle:function(t){var e,a=this;this.$nextTick((function(){a.form.ProjectName=a.$refs["ProjectName"].selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.getProjectEntity(t)},projectChange:function(t){var e,a=this;this.$nextTick((function(){var t;a.form.ProjectName=null===(t=a.$refs["ProjectName"])||void 0===t?void 0:t.selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.form.Area_Id="",this.form.AreaPosition="",this.treeParamsArea.data=[],this.$nextTick((function(t){var e;null===(e=a.$refs.treeSelectArea)||void 0===e||e.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",t&&this.getAreaList()},areaChange:function(t){this.form.AreaPosition=t.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.AreaPosition="",this.form.InstallUnit_Id="",this.form.SetupPosition=""},setupPositionChange:function(){var t=this;this.$nextTick((function(){t.form.SetupPosition=t.$refs["SetupPosition"].selected.currentLabel}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var a=t.Children;a&&a.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(a))}))},dateChange:function(t){},getProjectEntity:function(t){var e=this;(0,o.GetProjectEntity)({id:t}).then((function(t){if(t.IsSucceed){var a="",o=t.Data.Contacts;o.forEach((function(t){"Consignee"===t.Type&&(a=t.Name)})),e.consigneeName=a}else e.$message({message:t.Message,type:"error"})}))}}}},"8cae":function(t,e,a){},"93ec":function(t,e,a){"use strict";a("8cae")},a024:function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProcessFlow=u,e.AddProessLib=w,e.AddTechnology=l,e.AddWorkingProcess=s,e.DelLib=j,e.DeleteProcess=v,e.DeleteProcessFlow=y,e.DeleteTechnology=C,e.DeleteWorkingTeams=I,e.GetAllProcessList=f,e.GetCheckGroupList=D,e.GetChildComponentTypeList=B,e.GetFactoryAllProcessList=m,e.GetFactoryPeoplelist=W,e.GetFactoryWorkingTeam=g,e.GetGroupItemsList=b,e.GetLibList=i,e.GetLibListType=U,e.GetProcessFlow=p,e.GetProcessFlowListWithTechnology=h,e.GetProcessList=d,e.GetProcessListBase=c,e.GetProcessListTeamBase=L,e.GetProcessListWithUserBase=A,e.GetProcessWorkingTeamBase=N,e.GetTeamListByUser=x,e.GetTeamProcessList=k,e.GetWorkingTeam=T,e.GetWorkingTeamBase=G,e.GetWorkingTeamInfo=R,e.GetWorkingTeams=_,e.GetWorkingTeamsPageList=O,e.SaveWorkingTeams=S,e.UpdateProcessTeam=P;var n=o(a("b775")),r=o(a("4328"));function i(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(t)})}function l(t){return(0,n.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(t)})}function u(t){return(0,n.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(t)})}function d(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(t)})}function c(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(t)})}function f(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(t)})}function m(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:t})}function p(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(t)})}function h(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(t)})}function g(){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function T(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(t)})}function k(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(t)})}function b(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(t)})}function y(t){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(t)})}function C(t){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(t)})}function v(t){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:t})}function _(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}function O(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:t})}function S(t){return(0,n.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:t})}function I(t){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:t})}function R(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(t)})}function G(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(t)})}function L(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(t)})}function A(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:t})}function W(t){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function D(t){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:t})}function w(t){return(0,n.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:t})}function B(t){return(0,n.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:t})}function j(t){return(0,n.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:t})}function U(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:t})}function N(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:t})}function x(t){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}},b328:function(t,e,a){"use strict";a.r(e);var o=a("0cc6"),n=a("f6c3c");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var i=a("2877"),s=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"87531b6e",null);e["default"]=s.exports},b579:function(t,e,a){"use strict";a.r(e);var o=a("319a"),n=a("dbe2");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("93ec");var i=a("2877"),s=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"8d300cbe",null);e["default"]=s.exports},cfe3:function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container abs100"},[a("div",{staticClass:"search-wrapper"},[a("el-tabs",{on:{"tab-click":t.handleTabsClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"构件",name:"2"}}),a("el-tab-pane",{attrs:{label:"零件",name:"1"}})],1),a("el-form",{ref:"form",attrs:{model:t.form,inline:"","label-width":"80px"}},[a("el-button",{attrs:{type:"primary"},on:{click:t.drawerOpen}},[t._v("班组负荷")]),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},t._l(t.ProjectNameData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),[a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!t.form.Project_Id,"select-params":{clearable:!0},"tree-params":t.treeParamsArea},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1)],[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",attrs:{disabled:!t.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:t.setupPositionChange},model:{value:t.form.InstallUnit_Id,callback:function(e){t.$set(t.form,"InstallUnit_Id",e)},expression:"form.InstallUnit_Id"}},t._l(t.SetupPositionData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)],a("el-form-item",{attrs:{label:"任务工序",prop:"Process_Code"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},on:{change:t.processChange},model:{value:t.form.Process_Code,callback:function(e){t.$set(t.form,"Process_Code",e)},expression:"form.Process_Code"}},t._l(t.processOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Code}})})),1)],1),a("el-form-item",{attrs:{label:"加工班组",prop:"Working_Team_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!t.form.Process_Code,clearable:"",placeholder:"请选择"},model:{value:t.form.Working_Team_Id,callback:function(e){t.$set(t.form,"Working_Team_Id",e)},expression:"form.Working_Team_Id"}},t._l(t.groupOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),t.Is_Workshop_Enabled?a("el-form-item",{attrs:{label:"所属车间",prop:"Workshop_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Workshop_Id,callback:function(e){t.$set(t.form,"Workshop_Id",e)},expression:"form.Workshop_Id"}},t._l(t.workShopOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1):t._e(),a("el-form-item",{attrs:{label:"排产单号",prop:"Schduling_Code"}},[a("el-input",{attrs:{type:"text"},model:{value:t.form.Schduling_Code,callback:function(e){t.$set(t.form,"Schduling_Code",e)},expression:"form.Schduling_Code"}})],1),a("el-form-item",{attrs:{label:"是否分配",prop:"Allocate_Status"}},[a("el-select",{attrs:{multiple:"",placeholder:"请选择",clearable:""},model:{value:t.form.Allocate_Status,callback:function(e){t.$set(t.form,"Allocate_Status",e)},expression:"form.Allocate_Status"}},[a("el-option",{attrs:{label:"未分配",value:1}}),a("el-option",{attrs:{label:"已分配",value:2}}),a("el-option",{attrs:{label:"分配完成",value:3}})],1)],1),a("el-form-item",[a("el-button",{on:{click:t.handleReset}},[t._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.search(1)}}},[t._v("搜索")])],1)],2)],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"","row-config":{isCurrent:!0,isHover:!0},loading:t.pgLoading,align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e,o){return a("vxe-column",{key:o,attrs:{align:e.Align,fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:e.Code,title:e.Display_Name,"min-width":e.Width||120,visible:e.visible},scopedSlots:t._u(["Total_Allocation_Count"===e.Code?{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(a.Total_Allocation_Count-a.Total_Receive_Count===a.Can_Allocation_Count?"分配完成":a.Total_Allocation_Count>0?"已分配":"未分配")+" ")]}}:{key:"default",fn:function(o){var n=o.row;return[a("span",[t._v(t._s(n[e.Code]||"-"))])]}}],null,!0)})})),a("vxe-column",{attrs:{title:"操作",fixed:"right",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;e.rowIndex;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleView(o)}}},[t._v("查看")]),0!==o.Can_Allocation_Count?a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleDetail(o)}}},[t._v("任务分配 ")]):t._e()]}}])})],2)],1),a("Pagination",{attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:function(e){return t.fetchData(1)}}})],1),t.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.dWidth},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:t.pageChange}})],1):t._e(),a("el-drawer",{attrs:{size:"60%","custom-class":"drawerBox",visible:t.drawer,direction:"btt","with-header":!1,"append-to-body":"","wrapper-closable":""},on:{"update:visible":function(e){t.drawer=e}}},[a("div",{staticClass:"chartWrapper"},[a("div",{ref:"chartDom",staticStyle:{width:"100%",height:"100%"}})])])],1)},n=[]},dbe2:function(t,e,a){"use strict";a.r(e);var o=a("41eb"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);e["default"]=n.a},ed6d:function(t,e,a){"use strict";a("7fc5")},f2f6:function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=l,e.CheckPlanTime=u,e.DeleteInstallUnit=m,e.GetCompletePercent=T,e.GetEntity=b,e.GetInstallUnitAllInfo=c,e.GetInstallUnitComponentPageList=g,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=d,e.GetInstallUnitList=s,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=k,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=P,e.SaveOhterSourceInstallUnit=y;var n=o(a("b775")),r=o(a("4328"));function i(t){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function u(t){return(0,n.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function d(t){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function c(t){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,n.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,n.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,n.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function g(t){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function T(t){return(0,n.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function k(t){return(0,n.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function b(t){return(0,n.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function y(t){return(0,n.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},f6c3c:function(t,e,a){"use strict";a.r(e);var o=a("5106"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);e["default"]=n.a}}]);