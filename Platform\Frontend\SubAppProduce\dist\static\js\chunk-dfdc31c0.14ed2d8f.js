(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-dfdc31c0"],{"09f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,n,o){return e/=o/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function a(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,n){var i=r(),s=e-i,l=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=l;var e=Math.easeInOutQuad(u,i,s,t);a(e),u<t?o(c):n&&"function"===typeof n&&n()};c()}},"15ac":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var o=n("6186"),a=n("c685");t.default={methods:{getTableConfig:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,o.GetGridByCode)({code:e,IsAll:n}).then((function(e){var o=e.IsSucceed,i=e.Data,s=e.Message;if(o){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||a.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit,o=e.type;this.queryInfo.Page="limit"===o?1:t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var o=0;o<this.columns.length;o++){var a=this.columns[o];if(a.Code===t){n.Type=a.Type,n.Filter_Type=a.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"15fd":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("a4d3");var o=a(n("ccb5"));function a(e){return e&&e.__esModule?e:{default:e}}function r(e,t){if(null==e)return{};var n,a,r=(0,o.default)(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}},"1ca8":function(e,t,n){},2627:function(e,t,n){"use strict";n.r(t);var o=n("af81"),a=n("7fe0");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("2783");var i=n("2877"),s=Object(i["a"])(a["default"],o["a"],o["b"],!1,null,"ed02034a",null);t["default"]=s.exports},2783:function(e,t,n){"use strict";n("1ca8")},"43a4":function(e,t,n){"use strict";var o=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("c740"),n("d81d"),n("a434"),n("e9f5"),n("910d"),n("ab43"),n("e9c4"),n("b64b"),n("d3b7");var a=o(n("c14f")),r=o(n("1da1")),i=o(n("15ac")),s=n("7f9d");t.default={mixins:[i.default],props:{tbData:{type:Array,default:function(){return[]}}},data:function(){return{queryInfo:{Page:1,PageSize:-1},total:0,tbLoading:!1,multipleSelection:[],columns:[],tableData:[]}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType}},mounted:function(){var e=this;return(0,r.default)((0,a.default)().m((function t(){var n,o;return(0,a.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("PROTransferRevokeBath");case 1:e.isCom?(n=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==n&&e.columns.splice(n,1)):(o=e.columns.findIndex((function(e){return"Comp_Code"===e.Code})),-1!==o&&e.columns.splice(o,1)),e.tableData=JSON.parse(JSON.stringify(e.tbData)),e.tableData=e.tableData.filter((function(e){return e.Transfering_Count})).map((function(e){return e.Team_Cancel_Count=e.Transfering_Count,e}));case 2:return t.a(2)}}),t)})))()},methods:{inputChange:function(e){e.Team_Cancel_Count||(e.Team_Cancel_Count=1)},tbSelectChange:function(e){this.multipleSelection=e.records},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Team_Cancel_Count"===t.field},save:function(){var e=this;this.$confirm("是否提交当前数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.CancelTransferTask)(e.tableData).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}},"7fe0":function(e,t,n){"use strict";n.r(t);var o=n("f612"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},a024:function(e,t,n){"use strict";var o=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=W,t.AddTechnology=l,t.AddWorkingProcess=s,t.DelLib=B,t.DeleteProcess=P,t.DeleteProcessFlow=v,t.DeleteTechnology=_,t.DeleteWorkingTeams=I,t.GetAllProcessList=f,t.GetCheckGroupList=R,t.GetChildComponentTypeList=A,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=x,t.GetFactoryWorkingTeam=b,t.GetGroupItemsList=T,t.GetLibList=i,t.GetLibListType=F,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=p,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=S,t.GetProcessListWithUserBase=D,t.GetProcessWorkingTeamBase=$,t.GetTeamListByUser=q,t.GetTeamProcessList=C,t.GetWorkingTeam=y,t.GetWorkingTeamBase=G,t.GetWorkingTeamInfo=k,t.GetWorkingTeams=L,t.GetWorkingTeamsPageList=w,t.SaveWorkingTeams=O,t.UpdateProcessTeam=g;var a=o(n("b775")),r=o(n("4328"));function i(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function l(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function m(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function p(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,a.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function b(){return(0,a.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function C(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function T(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function v(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function _(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function P(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function L(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function w(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function O(e){return(0,a.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function I(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function k(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function G(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function S(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function D(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function x(e){return(0,a.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function R(e){return(0,a.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function W(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function A(e){return(0,a.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function B(e){return(0,a.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function F(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function $(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function q(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},af81:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a}));var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-card",{staticClass:"box-card h100"},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:(e.isCom?"构件":"零件")+"编号"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.searchInput,callback:function(t){e.searchInput=t},expression:"searchInput"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"我的班组",prop:"Source_Team_Id"}},[n("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Source_Team_Id,callback:function(t){e.$set(e.form,"Source_Team_Id",t)},expression:"form.Source_Team_Id"}},e._l(e.teamOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"接收工序",prop:"Process_Code"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},on:{change:e.processChange},model:{value:e.form.Process_Code,callback:function(t){e.$set(e.form,"Process_Code",t)},expression:"form.Process_Code"}},e._l(e.processOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Code}})})),1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"接收班组",prop:"Source_Team_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Process_Code,clearable:"",placeholder:"请选择"},model:{value:e.form.Target_Team_Id,callback:function(t){e.$set(e.form,"Target_Team_Id",t)},expression:"form.Target_Team_Id"}},e._l(e.groupOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),n("el-col",{attrs:{span:4}},[n("el-button",{staticStyle:{"margin-left":"10px"},on:{click:e.handleReset}},[e._v("重置")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("搜索")])],1)],1)],1),n("el-divider",{staticClass:"elDivder"}),n("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:e.handleBatch}},[e._v("批量撤回")])]},proxy:!0}])}),n("div",{staticClass:"tb-x"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",height:"100%","show-overflow":"",loading:e.tbLoading,stripe:"","row-config":{isCurrent:!0,isHover:!0},size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(e){return[n("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,sortable:"",align:e.Align,"min-width":e.Width,width:"auto"}})]})),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[o.Transfering_Count?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleRevoke(o)}}},[e._v("撤回")]):e._e()]}}])})],2)],1),n("div",{staticClass:"cs-bottom"},[n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据")])],1),n("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)],1),e.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"80%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"tb-data":e.currentData},on:{close:e.handleClose,refresh:function(t){return e.fetchData(1)}}})],1):e._e()],1)},a=[]},cb3d:function(e,t,n){"use strict";n.r(t);var o=n("43a4"),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},ccb5:function(e,t,n){"use strict";function o(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(-1!==t.indexOf(o))continue;n[o]=e[o]}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=o},d936:function(e,t,n){"use strict";n("f339e")},e5c8:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a}));var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"tb-x"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"","row-config":{isCurrent:!0,isHover:!0},size:"medium",data:e.tableData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e._l(e.columns,(function(t){return["Team_Cancel_Count"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto","edit-render":{},"min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var o=t.row;return[n("vxe-input",{attrs:{type:"integer",min:1,max:o.Transfering_Count},on:{change:function(t){return e.inputChange(o)}},model:{value:o.Team_Cancel_Count,callback:function(t){e.$set(o,"Team_Cancel_Count",e._n(t))},expression:"row.Team_Cancel_Count"}})]}},{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("displayValue")(n.Team_Cancel_Count))+" ")]}}],null,!0)}):n("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",align:t.Align,width:"auto","min-width":t.Width}})]}))],2)],1),n("footer",[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),n("el-button",{attrs:{disabled:!e.tableData.length,type:"primary"},on:{click:e.save}},[e._v("确定")])],1)])},a=[]},f339e:function(e,t,n){},f612:function(e,t,n){"use strict";var o=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n("5530")),r=o(n("15fd"));n("4de4"),n("7db0"),n("c740"),n("d81d"),n("a434"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("d3b7"),n("ac1f"),n("25f0"),n("841c");var i=o(n("c14f")),s=o(n("1da1")),l=n("ed08"),u=o(n("333d")),c=o(n("15ac")),d=n("7f9d"),f=n("a024"),m=o(n("f94b")),h=n("c685"),p=["Comp_Codes","Part_Code"];t.default={inject:["pageType"],components:{Pagination:u.default,Withdraw:m.default},mixins:[c.default],data:function(){return{tablePageSize:h.tablePageSize,title:"",tipLabel:"",tbLoading:!1,dialogVisible:!1,currentComponent:"",columns:[],teamOptions:[],tbData:[],processOption:[],currentData:[],groupOption:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1},total:0,form:{Source_Team_Id:"",Process_Code:"",Target_Team_Id:"",Comp_Codes:"",Part_Code:"",searchInput:""},search:function(){return{}},rules:{}}},computed:{isCom:function(){return"com"===this.pageType},searchInput:{get:function(){return this.isCom?this.form.Comp_Codes:this.form.Part_Code},set:function(e){this.isCom?this.form.Comp_Codes=e:this.form.Part_Code=e}}},mounted:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var n,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("PROComTransferRevokeList");case 1:return e.isCom?(n=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==n&&e.columns.splice(n,1)):(o=e.columns.findIndex((function(e){return"Comp_Code"===e.Code})),-1!==o&&e.columns.splice(o,1)),e.search=(0,l.debounce)(e.fetchData,800,!0),e.getProcessOption(),t.n=2,e.getProcessTeam();case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},methods:{fetchData:function(e){var t=this;this.tbLoading=!0,e&&(this.queryInfo.Page=e);var n=this.form,o=n.Comp_Codes,i=n.Part_Code,s=(0,r.default)(n,p),l="";l=""===this.form.Source_Team_Id?this.teamOptions.map((function(e){return e.value})).filter((function(e){return!!e})).toString():this.form.Source_Team_Id;var u=(0,a.default)((0,a.default)((0,a.default)({},this.queryInfo),s),{},{Process_Type:this.isCom?2:1,Source_Team_Id:l}),c=this.isCom?o:i,f=c.split(" ").filter((function(e){return!!e}));this.isCom?u.Comp_Codes=f:u.Part_Code=f,(0,d.GetTransferCancelDetails)(u).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount,t.multipleSelection=[]):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.tbLoading=!1}))},handleView:function(){},getTbData:function(e){var t=e.TotalWeight,n=e.TotalCount;this.tipLabel="",n&&(this.tipLabel+="总数".concat(n,"件")),t&&(this.tipLabel+=",总重".concat(t,"t。"))},handleClose:function(){this.dialogVisible=!1},getProcessTeam:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetTeamListByUser)({type:e.isCom?1:2}).then((function(t){e.teamOptions=t.Data.map((function(e){return{value:e.Id,label:e.Name}})),e.teamOptions.unshift({value:"",label:"全部"})}));case 1:return t.a(2)}}),t)})))()},handleBatch:function(){this.title="转移撤销",this.currentComponent="Withdraw",this.currentData=this.multipleSelection,this.dialogVisible=!0},getTeamOption:function(){var e=this,t="",n=this.processOption.find((function(t){return t.Code===e.form.Process_Code}));n&&(t=n.Id),(0,f.GetWorkingTeamBase)({processId:t}).then((function(t){t.IsSucceed?e.groupOption=t.Data:e.$message({message:t.Message,type:"error"})}))},processChange:function(e){this.form.Target_Team_Id="",e&&this.getTeamOption()},getProcessOption:function(){var e=this;(0,f.GetProcessList)({type:this.isCom?1:2}).then((function(t){t.IsSucceed?e.processOption=t.Data:e.$message({message:t.Message,type:"error"})}))},tbSelectChange:function(e){this.multipleSelection=e.records},handleRevoke:function(e){this.currentData=[e],this.title="转移撤销",this.currentComponent="Withdraw",this.dialogVisible=!0},handleReset:function(){this.$refs["form"].resetFields(),this.searchInput="",this.search(1)}}}},f94b:function(e,t,n){"use strict";n.r(t);var o=n("e5c8"),a=n("cb3d");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("d936");var i=n("2877"),s=Object(i["a"])(a["default"],o["a"],o["b"],!1,null,"a8b3ff94",null);t["default"]=s.exports}}]);