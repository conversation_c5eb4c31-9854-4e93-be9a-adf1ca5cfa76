(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7720064d"],{"3f51":function(n,t,e){"use strict";var u=e("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=u(e("b156b"));t.default={components:{detail:r.default},data:function(){return{}}}},"5a77":function(n,t,e){"use strict";e.r(t);var u=e("3f51"),r=e.n(u);for(var a in u)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(a);t["default"]=r.a},d634:function(n,t,e){"use strict";e.r(t);var u=e("f64d8"),r=e("5a77");for(var a in r)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(a);var f=e("2877"),d=Object(f["a"])(r["default"],u["a"],u["b"],!1,null,"87ea5484",null);t["default"]=d.exports},f64d8:function(n,t,e){"use strict";e.d(t,"a",(function(){return u})),e.d(t,"b",(function(){return r}));var u=function(){var n=this,t=n.$createElement,e=n._self._c||t;return e("detail")},r=[]}}]);