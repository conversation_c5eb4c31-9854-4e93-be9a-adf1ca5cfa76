(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-a26a4a60"],{"33c6":function(t,e,a){"use strict";a.r(e);var i=a("4369"),r=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},4369:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var r=i(a("5530")),o=i(a("c14f")),n=i(a("1da1")),l=a("c685"),s=a("8378"),u=i(a("15ac")),d=i(a("333d")),c=a("8975"),f=i(a("807d")),m=a("9d7e2"),p=a("b4f1");e.default={name:"PROWarMaterialCostDetail",components:{Pagination:d.default},mixins:[u.default,f.default],data:function(){return{total:0,queryInfo:{Page:1,PageSize:20},btnLoading:!1,tbLoading:!1,tablePageSize:l.tablePageSize,tbData:[],columns:[],activeName:"first",form:{Purchase_Contract_No:"",Sys_Project_Id:"",ReceivingOrPicking_TypeList:"",Pick_Department_Id:"",WorkingTeamId:"",ReceiveUserId:"",Use_Processing_Id:"",Supplier:"",RawNameFull:"",Store_Date_Begin:"",Store_Date_End:""},sum:{}}},computed:{isReceive:function(){return"first"===this.activeName},gridCode:function(){return this.isReceive?"PRORawMaterialCostDetailReceiving":"PRORawMaterialCostDetailPicking"},dateRange:{get:function(){return[(0,c.timeFormat)(this.form.Store_Date_Begin),(0,c.timeFormat)(this.form.Store_Date_End)]},set:function(t){if(t){var e=t[0],a=t[1];this.form.Store_Date_Begin=(0,c.timeFormat)(e),this.form.Store_Date_End=(0,c.timeFormat)(a)}else this.form.Store_Date_Begin="",this.form.Store_Date_End=""}}},mounted:function(){this.changeColumn(),this.fetchData()},methods:{changeColumn:function(){var t=this;return(0,n.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return t.showTable=!1,e.n=1,t.getTableConfig(t.gridCode);case 1:t.showTable=!0;case 2:return e.a(2)}}),e)})))()},handleClick:function(){this.handleReset("form1"),this.changeColumn(),this.fetchData(1)},fetchData:function(t){var e=this;t&&(this.queryInfo.Page=t),this.tbLoading=!0;var a=this.isReceive?s.FindReceivingNewPageList:s.FindPickingNewPageList,i=(0,r.default)((0,r.default)({Mat_Type:1},this.form),this.queryInfo);a(i).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(t){return t.Tax_All_Price=t.Tax_All_Price?(0,p.comdify)(t.Tax_All_Price+""):"-",t.NoTaxAllPrice=t.NoTaxAllPrice?(0,p.comdify)(t.NoTaxAllPrice+""):"-",t.VoucherWeightTaxPrice=t.VoucherWeightTaxPrice?(0,p.comdify)(t.VoucherWeightTaxPrice+""):"-",t.VoucherWeightNoTaxPrice=t.VoucherWeightNoTaxPrice?(0,p.comdify)(t.VoucherWeightNoTaxPrice+""):"-",t})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));var o=this.isReceive?m.FindReceivingNewSum:m.FindPickingNewSum;o(i).then((function(t){t.IsSucceed?e.sum=t.Data:e.$message({message:t.Message,type:"error"})}))},handleReset:function(t){this.dateRange="",this.form={Purchase_Contract_No:"",Sys_Project_Id:"",ReceivingOrPicking_TypeList:"",Pick_Department_Id:"",WorkingTeamId:"",ReceiveUserId:"",Use_Processing_Id:"",Supplier:"",RawNameFull:"",Store_Date_Begin:"",Store_Date_End:""},this.$refs[t].resetFields(),this.fetchData(1)}}}},7434:function(t,e,a){"use strict";a("f0e5")},8378:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CreateVersion=M,e.DelAuxCategoryEntity=R,e.DelAuxEntity=y,e.DelCategoryEntity=s,e.DelRawEntity=c,e.DeleteVersion=k,e.EditAuxEnabled=x,e.EditRawEnabled=d,e.ExportAuxForProject=q,e.ExportAuxList=I,e.ExportFindRawInAndOut=$,e.ExportInOutStoreReport=X,e.ExportPicking=E,e.ExportRawList=O,e.ExportRecSendProjectMaterialReport=Q,e.ExportRecSendProjectReport=K,e.ExportReceiving=A,e.ExportStagnationInventory=H,e.ExportStoreReport=J,e.FindInAndOutPageList=j,e.FindPickingNewPageList=G,e.FindPickingPageList=F,e.FindReceivingNewPageList=N,e.FindReceivingPageList=L,e.GetAuxCategoryDetail=P,e.GetAuxCategoryTreeList=v,e.GetAuxDetail=w,e.GetAuxFilterDataSummary=B,e.GetAuxForProjectDetail=z,e.GetAuxForProjectPageList=U,e.GetAuxPageList=C,e.GetAuxTemplate=b,e.GetAuxWHSummaryList=W,e.GetCategoryDetail=l,e.GetCategoryTreeList=o,e.GetCycleDate=V,e.GetList=T,e.GetRawDetail=m,e.GetRawPageList=f,e.GetRawTemplate=p,e.ImportAuxList=S,e.ImportRawList=_,e.SaveAuxCategoryEntity=g,e.SaveAuxEntity=h,e.SaveCategoryEntity=n,e.SaveRawEntity=u,e.UpdateVersion=D;var r=i(a("b775"));function o(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function n(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:t,timeout:12e5})}function v(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:t})}function x(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function w(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:t,timeout:12e5})}function O(t){return(0,r.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:t})}function T(t){return(0,r.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:t})}function M(t){return(0,r.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:t})}function k(t){return(0,r.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:t})}function A(t){return(0,r.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:t})}function E(t){return(0,r.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:t})}function L(t){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:t})}function N(t){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:t})}function F(t){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:t})}function G(t){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:t})}function V(t){return(0,r.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:t})}function $(t){return(0,r.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:t})}function W(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:t})}function U(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:t})}function z(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:t})}function q(t){return(0,r.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:t})}function B(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:t})}function H(t){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:t})}function J(t){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:t})}function K(t){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:t})}function Q(t){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:t})}function X(t){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:t})}},"83b9a":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return r}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap flex",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"search-wrapper fff"},[a("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"采购成本",name:"first"}}),a("el-tab-pane",{attrs:{label:"生产成本",name:"second"}})],1),a("el-form",{ref:"form1",staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{model:t.form,inline:""}},[t.isReceive?[a("el-form-item",{attrs:{label:"采购单号",prop:"Purchase_Contract_No"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:t.form.Purchase_Contract_No,callback:function(e){t.$set(t.form,"Purchase_Contract_No",e)},expression:"form.Purchase_Contract_No"}})],1),a("el-form-item",{attrs:{label:"供应商/甲方",prop:"Supplier"}},[a("SelectExternal",{model:{value:t.form.Supplier,callback:function(e){t.$set(t.form,"Supplier",e)},expression:"form.Supplier"}})],1),a("el-form-item",{attrs:{label:"操作时间",prop:"Store_Date_End"}},[a("el-date-picker",{staticClass:"date-picker",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"原料全名",prop:"RawNameFull"}},[a("el-input",{attrs:{type:"text",placeholder:"通配符%",clearable:""},model:{value:t.form.RawNameFull,callback:function(e){t.$set(t.form,"RawNameFull",e)},expression:"form.RawNameFull"}})],1),a("el-form-item",{attrs:{label:"类型",prop:"ReceivingOrPicking_TypeList"}},[a("SelectMaterialStoreType",{attrs:{multiple:"",type:"RawAllInStoreType"},model:{value:t.form.ReceivingOrPicking_TypeList,callback:function(e){t.$set(t.form,"ReceivingOrPicking_TypeList",e)},expression:"form.ReceivingOrPicking_TypeList"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"Sys_Project_Id"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}})],1)]:[a("el-form-item",{attrs:{label:"所属项目",prop:"Sys_Project_Id"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}})],1),a("el-form-item",{attrs:{label:"操作时间",prop:"Store_Date_End"}},[a("el-date-picker",{staticClass:"date-picker",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"领料/退库部门",prop:"Pick_Department_Id"}},[a("SelectDepartment",{model:{value:t.form.Pick_Department_Id,callback:function(e){t.$set(t.form,"Pick_Department_Id",e)},expression:"form.Pick_Department_Id"}})],1),a("el-form-item",{attrs:{label:"领料班组",prop:"WorkingTeamId"}},[a("SelectTeam",{model:{value:t.form.WorkingTeamId,callback:function(e){t.$set(t.form,"WorkingTeamId",e)},expression:"form.WorkingTeamId"}})],1),a("el-form-item",{attrs:{label:"领料/退库人",prop:"ReceiveUserId"}},[a("SelectDepartmentUser",{attrs:{"department-id":t.form.DepartmentId},model:{value:t.form.ReceiveUserId,callback:function(e){t.$set(t.form,"ReceiveUserId",e)},expression:"form.ReceiveUserId"}})],1),a("el-form-item",{attrs:{label:"工序",prop:"Use_Processing_Id"}},[a("SelectProcess",{model:{value:t.form.Use_Processing_Id,callback:function(e){t.$set(t.form,"Use_Processing_Id",e)},expression:"form.Use_Processing_Id"}})],1),a("el-form-item",{attrs:{label:"供应商/甲方",prop:"Supplier"}},[a("SelectExternal",{model:{value:t.form.Supplier,callback:function(e){t.$set(t.form,"Supplier",e)},expression:"form.Supplier"}})],1),a("el-form-item",{attrs:{label:"类型",prop:"ReceivingOrPicking_TypeList"}},[a("SelectMaterialStoreType",{attrs:{multiple:"",type:"RawAllOutStoreType"},model:{value:t.form.ReceivingOrPicking_TypeList,callback:function(e){t.$set(t.form,"ReceivingOrPicking_TypeList",e)},expression:"form.ReceivingOrPicking_TypeList"}})],1),a("el-form-item",{attrs:{label:"原料全名",prop:"RawNameFull"}},[a("el-input",{attrs:{type:"text",placeholder:"通配符%",clearable:""},model:{value:t.form.RawNameFull,callback:function(e){t.$set(t.form,"RawNameFull",e)},expression:"form.RawNameFull"}})],1)],a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:t.tbLoading||t.btnLoading},on:{click:function(e){return t.fetchData(1)}}},[t._v("搜索")]),a("el-button",{attrs:{disabled:t.tbLoading||t.btnLoading},on:{click:function(e){return t.handleReset("form1")}}},[t._v("重置 ")])],1),a("div",{staticStyle:{"margin-left":"auto"}},[a("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":t.gridCode},on:{updateColumn:t.changeColumn}})],1)],2)],1),a("main",{staticClass:"main-wrapper fff"},[t.isReceive?[a("div",{staticClass:"total-container"},[a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-item"},[a("div",[t._v("理重入库金额")]),a("div",[t._v(t._s(t.sum.Tax_In_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("理重退货金额")]),a("div",[t._v(t._s(t.sum.Tax_Out_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("合计")]),a("div",{staticClass:"font-weight"},[t._v(t._s(t.sum.Tax_All)+"元")])]),a("div",{staticClass:"tag"},[t._v("含税")])]),a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-item"},[a("div",[t._v("凭证重入库金额")]),a("div",[t._v(t._s(t.sum.Tax_In_Voucher_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("凭证重退货金额")]),a("div",[t._v(t._s(t.sum.Tax_Out_Voucher_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("合计")]),a("div",{staticClass:"font-weight"},[t._v(t._s(t.sum.Tax_Voucher_All)+"元")])]),a("div",{staticClass:"tag"},[t._v("含税")])]),a("div",{staticClass:"total-wrapper orange"},[a("div",{staticClass:"total-item"},[a("div",[t._v("理重入库金额")]),a("div",[t._v(t._s(t.sum.No_Tax_In_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("理重退货金额")]),a("div",[t._v(t._s(t.sum.No_Tax_Out_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("合计")]),a("div",{staticClass:"font-weight"},[t._v(t._s(t.sum.No_Tax_All)+"元")])]),a("div",{staticClass:"tag"},[t._v("不含税")])]),a("div",{staticClass:"total-wrapper orange"},[a("div",{staticClass:"total-item"},[a("div",[t._v("凭证重入库金额")]),a("div",[t._v(t._s(t.sum.No_Tax_In_Voucher_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("凭证重退货金额")]),a("div",[t._v(t._s(t.sum.No_Tax_Out_Voucher_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("合计")]),a("div",{staticClass:"font-weight"},[t._v(t._s(t.sum.No_Tax_Voucher_All)+"元")])]),a("div",{staticClass:"tag"},[t._v("不含税")])])])]:[a("div",{staticClass:"total-container"},[a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-item"},[a("div",[t._v("理重出库金额")]),a("div",[t._v(t._s(t.sum.Tax_Out_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("理重退库金额")]),a("div",[t._v(t._s(t.sum.Tax_In_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("合计")]),a("div",{staticClass:"font-weight"},[t._v(t._s(t.sum.Tax_All)+"元")])]),a("div",{staticClass:"tag"},[t._v("含税")])]),a("div",{staticClass:"total-wrapper orange"},[a("div",{staticClass:"total-item"},[a("div",[t._v("理重出库金额")]),a("div",[t._v(t._s(t.sum.No_Tax_Out_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("理重退库金额")]),a("div",[t._v(t._s(t.sum.No_Tax_In_Price)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[t._v("合计")]),a("div",{staticClass:"font-weight"},[t._v(t._s(t.sum.No_Tax_All)+"元")])]),a("div",{staticClass:"tag"},[t._v("不含税")])])])],a("div",{directives:[{name:"loading",rawName:"v-loading",value:!t.showTable,expression:"!showTable"}],staticClass:"tb-x"},[t.showTable?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto",loading:t.tbLoading,"show-overflow":"",stripe:"",size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Column_Id,attrs:{field:e.Code,title:e.Display_Name,"min-width":e.Width,align:e.Align,fixed:e.Is_Frozen?e.Frozen_Dirction||"left":""},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.row;return[t._v(" "+t._s(i[e.Code]||"-")+" ")]}}],null,!0)})]}))],2):t._e()],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)],2)])},r=[]},bf1b:function(t,e,a){"use strict";a.r(e);var i=a("83b9a"),r=a("33c6");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("7434");var n=a("2877"),l=Object(n["a"])(r["default"],i["a"],i["b"],!1,null,"5c9d956d",null);e["default"]=l.exports},f0e5:function(t,e,a){}}]);