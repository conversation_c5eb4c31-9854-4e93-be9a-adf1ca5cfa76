(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-a0e2e152"],{"0b513":function(e,t,a){"use strict";a.r(t);var i=a("346d"),r=a("d111");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("501a");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"069062c4",null);t["default"]=s.exports},"0d62":function(e,t,a){"use strict";a.r(t);var i=a("ba13"),r=a("0ed2");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("3ef5");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"d4117b8e",null);t["default"]=s.exports},"0ed2":function(e,t,a){"use strict";a.r(t);var i=a("e5b4"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"13e8e":function(e,t,a){"use strict";a("719c")},"149b":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[2===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"非标规格",name:"1"}},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},e._l(e.list,(function(t){return a("el-form-item",{key:t,attrs:{label:t}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:e.form[t],callback:function(a){e.$set(e.form,t,a)},expression:"form[item]"}})],1)})),1)],1):e._e(),1===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"标准规格",name:"2"}},[a("el-table",{ref:"multipleTable",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(a){return a.stopPropagation(),function(a){return e.handleRadioChange(a,t.row)}(a)}},model:{value:e.radioSelect,callback:function(t){e.radioSelect=t},expression:"radioSelect"}})]}}],null,!1,3152109164)}),a("el-table-column",{attrs:{align:"center",prop:"StandardDesc",label:"规格"}})],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},"20e4":function(e,t,a){},"227d":function(e,t,a){"use strict";a("e4fd")},2444:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[e.isAdjustAmount?a("el-card",{staticClass:"box-card"},[a("el-form",{attrs:{inline:"","label-width":"100px"}},[a("el-form-item",{attrs:{label:"单据日期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"date",placeholder:"单据日期"},model:{value:e.adjustAmountForm.MoneyAdjustDate,callback:function(t){e.$set(e.adjustAmountForm,"MoneyAdjustDate",t)},expression:"adjustAmountForm.MoneyAdjustDate"}})],1),a("el-form-item",{attrs:{label:"单据编号"}},[a("span",[e._v(e._s(e.$route.query.id))])]),a("el-form-item",{attrs:{label:"合同号"}},[a("span",[e._v(e._s(e.form.Purchase_Contract_No||"无"))])]),a("el-form-item",{attrs:{label:"发票号码"}},[a("span",[e._v(e._s(e.form.Invoice_No||"无"))])])],1),a("el-form",{attrs:{"label-width":"100px"}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:3},model:{value:e.adjustAmountForm.Remark,callback:function(t){e.$set(e.adjustAmountForm,"Remark",t)},expression:"adjustAmountForm.Remark"}})],1)],1)],1):a("el-card",{staticClass:"box-card",style:e.isRetract?"height: 110px; overflow: hidden;":""},[a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"10px","padding-bottom":"10px","border-bottom":"1px solid #d0d3db"}},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("入库单信息")]),a("div",{staticClass:"retract-container",on:{click:e.handleRetract}},[a("el-button",{attrs:{type:"text"}},[e._v(e._s(e.isRetract?"展开":"收起"))]),a("el-button",{attrs:{type:"text",icon:e.isRetract?"el-icon-arrow-down":"el-icon-arrow-up"}})],1)]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"入库类型",prop:"InStoreType"}},[a("SelectMaterialStoreType",{attrs:{disabled:e.isView||e.isEdit||e.isReturn,type:"RawInStoreType"},on:{change:e.typeChange},model:{value:e.form.InStoreType,callback:function(t){e.$set(e.form,"InStoreType",t)},expression:"form.InStoreType"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"入库日期",prop:"InStoreDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus||e.isReturn,"picker-options":{disabledDate:function(t){return t.getTime()<new Date(e.statisticTime).getTime()}},"value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.InStoreDate,callback:function(t){e.$set(e.form,"InStoreDate",t)},expression:"form.InStoreDate"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"送货单编号",prop:"Delivery_No"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"送货单编号",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("Delivery_No")}},model:{value:e.form.Delivery_No,callback:function(t){e.$set(e.form,"Delivery_No",t)},expression:"form.Delivery_No"}})],1)],1),1==e.form.InStoreType?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"采购合同编号",prop:"Purchase_Contract_No"}},[a("el-input",{ref:"elInput",staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus||e.isReturn,placeholder:"采购合同编号",type:"text",clearable:""},model:{value:e.form.Purchase_Contract_No,callback:function(t){e.$set(e.form,"Purchase_Contract_No",t)},expression:"form.Purchase_Contract_No"}})],1)],1):e._e(),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"是否代购",prop:"Is_Replace_Purchase"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus||e.isReturn,placeholder:"请选择"},on:{change:e.isReplacePurchaseChange},model:{value:e.form.Is_Replace_Purchase,callback:function(t){e.$set(e.form,"Is_Replace_Purchase",t)},expression:"form.Is_Replace_Purchase"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1)],1),e.form.Is_Replace_Purchase?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"ProjectId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus||e.isReturn},on:{change:e.projectChange},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1):e._e(),1!=e.form.InStoreType&&3!=e.form.InStoreType||!e.form.Is_Replace_Purchase?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus||e.isReturn},on:{change:e.supplierChange},model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}},e._l(e.SupplierList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),2==e.form.InStoreType&&e.form.Is_Replace_Purchase?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnit"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"甲方单位",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus||e.isReturn},on:{change:e.partyUnitChange},model:{value:e.form.PartyUnit,callback:function(t){e.$set(e.form,"PartyUnit",t)},expression:"form.PartyUnit"}},e._l(e.PartyUnitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.isRaw?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"计量方式",prop:"Weight_Method"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn},on:{change:function(t){return e.setFormLocal("Weight_Method",t)}},model:{value:e.form.Weight_Method,callback:function(t){e.$set(e.form,"Weight_Method",t)},expression:"form.Weight_Method"}},[a("el-option",{attrs:{label:"理重",value:0}}),a("el-option",{attrs:{label:"凭证重",value:1}})],1)],1)],1):e._e(),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"车牌号",prop:"CarNumber"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"车牌号",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("CarNumber")}},model:{value:e.form.CarNumber,callback:function(t){e.$set(e.form,"CarNumber",t)},expression:"form.CarNumber"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"司机信息",prop:"Driver"}},[a("el-input",{staticStyle:{width:"35%","margin-right":"10px"},attrs:{disabled:e.isView||e.isReturn,placeholder:"姓名",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("Driver")}},model:{value:e.form.Driver,callback:function(t){e.$set(e.form,"Driver",t)},expression:"form.Driver"}}),a("el-input",{staticStyle:{width:"calc(100% - 35% - 10px)"},attrs:{disabled:e.isView||e.isReturn,placeholder:"手机号",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("DriverMobile")}},model:{value:e.form.DriverMobile,callback:function(t){e.$set(e.form,"DriverMobile",t)},expression:"form.DriverMobile"}})],1)],1),e.isRaw?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"整车磅重(t)",prop:"Car_Pound_Weight"}},[a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:e.weightDecimal,min:0},expression:"{ toFixed: weightDecimal, min: 0 }"}],staticClass:"input-number",staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"整车磅重"},model:{value:e.form.Car_Pound_Weight,callback:function(t){e.$set(e.form,"Car_Pound_Weight",t)},expression:"form.Car_Pound_Weight"}})],1)],1):e._e(),e.isRaw?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"整车凭证重(t)",prop:"Car_Voucher_Weight"}},[a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:e.weightDecimal,min:0},expression:"{ toFixed: weightDecimal, min: 0 }"}],staticClass:"input-number",staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"整车凭证重",clearable:""},model:{value:e.form.Car_Voucher_Weight,callback:function(t){e.$set(e.form,"Car_Voucher_Weight",t)},expression:"form.Car_Voucher_Weight"}})],1)],1):e._e(),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"是否开票",prop:"Has_Invoice"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn},model:{value:e.form.Has_Invoice,callback:function(t){e.$set(e.form,"Has_Invoice",t)},expression:"form.Has_Invoice"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1)],1),e.form.Has_Invoice?[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发票号码",prop:"Invoice_No",rules:[{required:!0,message:"请输入",trigger:"blur"}]}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:(e.isView||e.isReturn)&&!e.isInvoices,placeholder:"发票号码",clearable:""},model:{value:e.form.Invoice_No,callback:function(t){e.$set(e.form,"Invoice_No",t)},expression:"form.Invoice_No"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"开票日期",prop:"Invoice_Make_Time",rules:[{required:!0,message:"请选择",trigger:"blur"}]}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:(e.isView||e.isReturn)&&!e.isInvoices,"value-format":"yyyy-MM-dd",type:"date",placeholder:"开票日期"},model:{value:e.form.Invoice_Make_Time,callback:function(t){e.$set(e.form,"Invoice_Make_Time",t)},expression:"form.Invoice_Make_Time"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"收票日期",prop:"Invoice_Receive_Time",rules:[{required:!0,message:"请选择",trigger:"blur"}]}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:(e.isView||e.isReturn)&&!e.isInvoices,"value-format":"yyyy-MM-dd",type:"date",placeholder:"收票日期"},model:{value:e.form.Invoice_Receive_Time,callback:function(t){e.$set(e.form,"Invoice_Receive_Time",t)},expression:"form.Invoice_Receive_Time"}})],1)],1)]:e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,rows:1,"show-word-limit":"",maxlength:100,placeholder:"备注",type:"textarea",clearable:""},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],2),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:5,multiple:!0,"on-success":function(t,a,i){e.uploadSuccess(t,a,i)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"file-list":e.fileListData,"show-file-list":!0,disabled:e.isView||3===e.formStatus||e.isReturn}},[e.isView||3===e.formStatus||e.isReturn?e._e():a("el-button",{attrs:{type:"primary"}},[e._v("上传文件")])],1)],1)],1)],1)],1)],1),a("el-card",{staticClass:"box-card box-card-tb"},[a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("入库明细")])]),a("el-divider",{staticClass:"elDivder"}),a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"0px"}},[a("el-form",{ref:"searchForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm}},[e.isRaw?a("el-form-item",{attrs:{label:e.materialTypeName+"全名",prop:"RawNameFull"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"通配符%",clearable:""},model:{value:e.searchForm.RawNameFull,callback:function(t){e.$set(e.searchForm,"RawNameFull",t)},expression:"searchForm.RawNameFull"}})],1):e._e(),a("el-form-item",{attrs:{label:e.materialTypeName+"名称",prop:"RawName"}},[a("el-input",{staticStyle:{"min-width":"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),e.isRaw?a("el-form-item",{attrs:{label:"材质",prop:"Material","label-width":"50px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Material,callback:function(t){e.$set(e.searchForm,"Material",t)},expression:"searchForm.Material"}})],1):e._e(),a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.SysProjectId,callback:function(t){e.$set(e.searchForm,"SysProjectId",t)},expression:"searchForm.SysProjectId"}},e._l(e.ProjectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),e.filterVisible?[a("el-form-item",{attrs:{label:e.materialTypeName+"分类",prop:"CategoryId"}},[a("SelectMaterialType",{attrs:{"is-raw":e.isRaw},model:{value:e.searchForm.CategoryId,callback:function(t){e.$set(e.searchForm,"CategoryId",t)},expression:"searchForm.CategoryId"}})],1),e.isRaw?a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Width,callback:function(t){e.$set(e.searchForm,"Width",t)},expression:"searchForm.Width"}})],1):e._e(),e.isRaw?a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Length,callback:function(t){e.$set(e.searchForm,"Length",t)},expression:"searchForm.Length"}})],1):e._e(),a("el-form-item",{attrs:{label:"仓库",prop:"WarehouseId"}},[a("SelectWarehouse",{attrs:{type:e.materialType},model:{value:e.searchForm.WarehouseId,callback:function(t){e.$set(e.searchForm,"WarehouseId",t)},expression:"searchForm.WarehouseId"}})],1),a("el-form-item",{attrs:{label:"库位",prop:"LocationId"}},[a("SelectLocation",{attrs:{"warehouse-id":e.searchForm.WarehouseId},model:{value:e.searchForm.LocationId,callback:function(t){e.$set(e.searchForm,"LocationId",t)},expression:"searchForm.LocationId"}})],1),1==e.form.InStoreType||3==e.form.InStoreType?a("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商",clearable:"",filterable:""},model:{value:e.searchForm.Supplier,callback:function(t){e.$set(e.searchForm,"Supplier",t)},expression:"searchForm.Supplier"}},e._l(e.SupplierList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),2==e.form.InStoreType?a("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnit"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"甲方单位",clearable:"",filterable:""},model:{value:e.searchForm.PartyUnit,callback:function(t){e.$set(e.searchForm,"PartyUnit",t)},expression:"searchForm.PartyUnit"}},e._l(e.PartyUnitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e()]:e._e(),a("el-form-item",{staticClass:"last-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSearch()}}},[e._v("搜索")]),a("el-button",{staticClass:"reset-btn",on:{click:e.handleReset}},[e._v("重置")]),a("el-button",{on:{click:e.toggleFilter}},[e._v(e._s(e.filterVisible?"收起筛选":"展开筛选"))])],1)],2)],1),a("el-divider",{staticClass:"elDivder"}),a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"8px"}},[e.isView||e.isReturn?e._e():a("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"primary",plain:""},on:{click:e.handleBatchEdit}},[e._v("批量编辑 ")]),a("el-button",{on:{click:e.syncWeight}},[e._v("理重同步至凭证重")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleWarehouse(!1)}}},[e._v("批量选择仓库/库位 ")]),a("el-button",{on:{click:e.handleImport}},[e._v("导入")]),a("USSLInboundDetailImport",{attrs:{"material-type":e.materialType},on:{getAddList:e.addListReset}})]},proxy:!0}],null,!1,2789684786)}),a("div",{staticStyle:{"margin-left":"auto"}}),a("DynamicCustomFieldFilter",{attrs:{columns:e.columns},on:{search:e.searchCustomField}}),e.columns?a("DynamicTableFields",{attrs:{title:"表格配置","table-columns":e.columns,"table-config-code":e.gridCode,"manual-hide-columns":e.manualHideTableColumns,type:e.BigType},on:{updateColumn:e.getTableColumns}}):e._e()],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:!e.showTable,expression:"!showTable"}],staticClass:"tb-x"},[e.showTable?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tableData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checCheckboxkMethod},"row-style":e.rowStyle,"show-footer":"","footer-method":e.footerMethod},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e.isView||e.isReturn?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"45",title:""}}),e.isView||e.isReturn?e._e():a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row,r=t.rowIndex;return[a("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&i.Sub_Id)},on:{click:function(t){return e.handleCopy(i,r)}}},[e._v("复制")])]}}],null,!1,2613506362)}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([t.Style.tips?{key:"header",fn:function(){return[a("span",[e._v(e._s(t.Display_Name))]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(t.Style.tips)},slot:"content"}),a("i",{staticClass:"el-icon-question",staticStyle:{cursor:"pointer","font-size":"16px"}})])]},proxy:!0}:null,{key:"default",fn:function(i){var r=i.row;return["Weight_Method"===t.Code?[e._v(" "+e._s(["理重","凭证重"][e.form.Weight_Method])+" ")]:"InStoreWeight"===t.Code?[e._v(" "+e._s(0==e.form.Weight_Method?r.Theory_Weight:r.Voucher_Weight)+" ")]:"InvoiceId"===t.Code?[e._v(" "+e._s(r[t.Code]?"已核销":"未核销")+" ")]:[a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]]}},t.Is_Edit?{key:"edit",fn:function(i){var r=i.row;return["Actual_Spec"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"Width"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType||2===r.BigType?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.widthChange(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"Length"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.lengthChange(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"InStoreCount"===t.Code?a("div",[a("el-input",{ref:"numberInput",refInFor:!0,attrs:{min:0,controls:!1,type:"number"},on:{change:function(t){return e.checkWeight(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"Theory_Weight"===t.Code?a("div",[r.Specific_Gravity&&3!==r.BigType?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:e.weightDecimal,min:0},expression:"{ toFixed: weightDecimal, min: 0 }"}],on:{change:function(t){return e.countTaxUnitPrice(r,"Theory_Weight")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"PurchaseWeight"===t.Code?a("div",[r.Specific_Gravity&&3!==r.BigType?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:e.weightDecimal,min:0},expression:"{ toFixed: weightDecimal, min: 0 }"}],on:{change:function(t){return e.countTaxUnitPrice(r,"PurchaseWeight")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):t.Code.includes("Remark")?a("div",[a("el-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"Voucher_Weight"===t.Code?a("div",[a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:e.weightDecimal,min:0},expression:"{ toFixed: weightDecimal, min: 0 }"}],attrs:{min:0},on:{change:function(t){return e.countTaxUnitPrice(r,"Voucher_Weight")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("div",[e._v(" "+e._s(r.WarehouseName)+"/"+e._s(r.LocationName)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(r)}}})])]):"FurnaceBatchNo"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r.FurnaceBatchNo,callback:function(t){e.$set(r,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})],1):["NoTaxUnitPrice","Tax_Rate","TaxUnitPrice"].includes(t.Code)?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code]||0)))]):a("el-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"TaxUnitPrice")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"ReturnPoundWeight"===t.Code?a("div",[a("el-input",{model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"Delivery_No"===t.Code?a("div",[a("el-input",{on:{change:function(a){return e.changeKeyInfo("Delivery_No",r[t.Code])}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"CarNumber"===t.Code?a("div",[a("el-input",{on:{change:function(a){return e.changeKeyInfo("CarNumber",r[t.Code])}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"Driver"===t.Code?a("div",[a("el-input",{on:{change:function(a){return e.changeKeyInfo("Driver",r[t.Code])}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"DriverMobile"===t.Code?a("div",[a("el-input",{on:{change:function(a){return e.changeKeyInfo("DriverMobile",r[t.Code])}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"OutStoreCount"===t.Code?a("div",[3===e.formStatus&&r.OutStoreCount?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{attrs:{max:r.AvailableCount,min:0,type:"number"},on:{change:function(t){return e.outStoreChange(r)}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):["Adjust_Amount_In","Adjust_Amount_Out"].includes(t.Code)?a("div",[a("el-input",{attrs:{type:"number"},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1):"ProjectName"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-select",{attrs:{filterable:"",transfer:""},on:{change:function(t){return e.itemProjectChange(r)}},model:{value:r.SysProjectId,callback:function(t){e.$set(r,"SysProjectId",t)},expression:"row.SysProjectId"}},e._l(e.ProjectList,(function(e){return a("vxe-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Short_Name}})})),1)],1):"SupplierName"===t.Code?a("div",[a("vxe-select",{attrs:{transfer:"",filterable:"",clearable:""},on:{change:function(t){return e.itemSupplierChange(r)}},model:{value:r.Supplier,callback:function(t){e.$set(r,"Supplier",t)},expression:"row.Supplier"}},e._l(e.SupplierList,(function(e){return a("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):"PartyUnitName"===t.Code?a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("vxe-select",{attrs:{filterable:"",transfer:""},on:{change:function(t){return e.itemPartyUnitChange(r)}},model:{value:r.PartyUnit,callback:function(t){e.$set(r,"PartyUnit",t)},expression:"row.PartyUnit"}},e._l(e.PartyUnitList,(function(e){return a("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):a("div",[3===e.formStatus&&r.Sub_Id?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):a("el-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(a){e.$set(r,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()],1),e.isView?e._e():a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("footer",[a("div",{staticClass:"data-info"},[e.isReturn?e._e():a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("div",[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),e.isReturn?a("el-button",{attrs:{loading:e.returning,type:"primary"},on:{click:e.handleReturn}},[e._v("确认退货")]):[3!==e.formStatus?a("el-button",{attrs:{loading:e.saveLoading,disabled:e.submitLoading},on:{click:e.handleSaveDraft}},[e._v("保存草稿 ")]):e._e(),a("el-button",{attrs:{type:"primary",disabled:e.saveLoading,loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v("提交入库")])]],2)]),e.isAdjustAmount?a("footer",[a("div",{staticStyle:{"margin-left":"auto"}},[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.saveLoading,loading:e.submitLoading},on:{click:e.handleSubmitAdjustAmount}},[e._v("提交")])],1)]):e._e(),e.isInvoices?a("footer",[a("div",{staticStyle:{"margin-left":"auto"}},[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.saveLoading,loading:e.submitLoading},on:{click:e.handleSubmitInvoice}},[e._v("提交")])],1)]):e._e()],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":0,"project-list":e.ProjectList,"supplier-list":e.SupplierList,"party-unit-list":e.PartyUnitList,"is-replace-purchase":e.form.Is_Replace_Purchase,"check-type-list":e.checkTypeList,"is-raw":e.isRaw},on:{close:e.handleClose,warehouse:e.getWarehouse,batchEditor:e.batchEditorFn,standard:e.getStandard,refresh:e.fetchData,getAddList:function(t){return e.getAddList(t,!0)}}})],1):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增"+e.materialTypeName,visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[e.isPurchase?a("add-purchase-list",{ref:"draft",attrs:{"is-single":e.isSingle,"big-type-data":e.BigType,"form-data":e.form,"project-list":e.ProjectList,"joined-items":e.rootTableData},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}}):a("add-raw-material-list",{ref:"draft",attrs:{"is-purchasing":e.form.Is_Replace_Purchase,"is-single":e.isSingle,"is-manual":e.isManual,"is-purchase":e.isPurchase,"is-customer":e.isCustomer,"project-id":e.form.ProjectId,"project-list":e.ProjectList},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2),e.dialogRepeatVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"检查重复",visible:e.dialogRepeatVisible,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.dialogRepeatVisible=t},close:e.handleClose}},[a("Repeat",{ref:"repeat",attrs:{columns:e.columns},on:{submit:function(t){return e.submitInfo(1,!0)},close:e.handleClose}})],1):e._e()],1)},r=[]},28668:function(e,t,a){"use strict";a.r(t);var i=a("4c24"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"2a870":function(e,t,a){"use strict";a("4c35")},"2ee5":function(e,t,a){"use strict";a.r(t);var i=a("308e1"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"308e1":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("5530")),n=i(a("c14f")),o=i(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("ddb0");var s=a("ed08"),l=a("5480"),u=a("93aa"),c=i(a("1463")),d=i(a("333d")),f=a("c685"),h=a("8378");t.default={components:{TreeDetail:c.default,Pagination:d.default},props:{isSingle:{type:Boolean,default:!1},isPurchasing:{type:Boolean,default:!0},isCustomer:{type:Boolean,default:!0},isPurchase:{type:Boolean,default:!0},isManual:{type:Boolean,default:!0},projectId:{type:String,default:""},projectList:{type:Array,default:function(){return[]}}},data:function(){return{tablePageSize:f.tablePageSize,treeLoading:!1,expandedKey:"",versionCode:"",treeData:[],Category_Id:"",catalogDetail:{},form:{Materiel_Name:"",Materiel_Code:"",Spec:"",Material:"",Raw_FullName:""},selectRow:null,tbLoading:!1,saveLoading:!1,queryInfo:{Page:1,PageSize:20,ParameterJson:[]},total:0,versionList:[],columns:[],currentColumns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{showVersion:function(){return!this.isPurchasing},isSteelPlate:function(){var e,t=this,a=this.catalogDetail.Pid?null===(e=this.treeData.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";return"板材"===this.catalogDetail.Name||"板材"===a},isRaw:function(){return"0"==this.$route.params.type}},watch:{isSteelPlate:function(){this.getCurrentColumn()},showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getVersionList();case 1:return t.n=2,e.getConfig();case 2:return t.n=3,e.getTreeList();case 3:e.getCurrentColumn(),e.search=(0,s.debounce)(e.fetchData,800,!0);case 4:return t.a(2)}}),t)})))()},methods:{getCurrentColumn:function(){var e,t=this,a=this.catalogDetail.Pid?null===(e=this.treeData.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";this.currentColumns=this.columns.filter((function(e){return"板材"===t.catalogDetail.Name||"板材"===a?"Spec"!==e.Code:"Thick"!==e.Code}))},versionChange:function(){this.fetchData(1)},getVersionList:function(){var e=this,t=this.projectList.find((function(t){return t.Id===e.projectId})),a="";return t&&(a=t.Sys_Project_Id),new Promise((function(t,i){(0,h.GetList)({sysProjectId:a,inStoryType:e.isManual?3:2}).then((function(a){if(a.IsSucceed){e.versionList=a.Data;var i=e.versionList.find((function(e){return e.Is_System}));i&&(e.versionCode=i.Id)}else e.$message({message:a.Message,type:"error"});t()}))}))},getTreeList:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){var a;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,a=e.isRaw?u.GetCategoryTreeList:u.GetAuxCategoryTreeList,t.n=1,a({}).then((function(t){t.IsSucceed?(e.treeData=[{Label:"全部",Id:"all",Data:{Category:{Name:"",Pid:""}}}].concat(t.Data),e.expandedKey=e.treeData[0].Id,e.Category_Id=e.treeData[0].Id,e.catalogDetail=e.treeData[0].Data.Category,e.fetchData(1)):e.$message({message:t.Message,type:"error"}),e.treeLoading=!1})).catch((function(){e.treeLoading=!1}));case 1:return t.a(2)}}),t)})))()},handleNodeClick:function(e){this.Category_Id=e.Id,this.catalogDetail=e.Data.Category,this.queryInfo.Page=1,this.fetchData()},setRow:function(e){},getConfig:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)(e.isRaw?"PRORawList":"PROAddManualAuxList");case 1:e.columns=t.v;case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},searchReset:function(){this.$refs["form"].resetFields(),this.fetchData(1)},fetchData:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){var i;return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:e&&(t.queryInfo.Page=e),t.tbLoading=!0,i=t.isRaw?u.GetRawPageList:u.GetAuxPageList,i((0,r.default)((0,r.default)({Category_Id:"all"===t.Category_Id?"":t.Category_Id},t.form),t.queryInfo)).then((function(e){e.IsSucceed?(t.fTable=e.Data.Data.filter((function(e){return e.Is_Enabled})),t.total=e.Data.TotalCount,t.multipleSelection=[]):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}));case 1:return a.a(2)}}),a)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.BigType=e.Big_Type,e.RawCode=e.Code,e.CategoryId=e.Category_Id,e.RawName=e.Name,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")},changePage:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=20),Promise.all([e.fetchData()]).then((function(e){}));case 1:return t.a(2)}}),t)})))()}}}},"346d":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("vxe-table",{key:2,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"500","show-overflow":"","empty-text":"无重复项！","row-style":e.rowStyle,"auto-resize":!0,size:"medium",data:e.resultArray,resizable:"","scroll-y":{enabled:!0},"tooltip-config":{enterable:!0}}},[a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(i){var r=i.row;return[a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]}}],null,!0)})]}))],2),a("div",{staticClass:"footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("不合并手动处理")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.submit}},[e._v("合并后提交")])],1)],1)},r=[]},"3a56e":function(e,t,a){},"3ef2":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9c4");var r=i(a("3796")),n=a("ed08"),o=a("93aa");t.default={components:{Upload:r.default},props:{isRaw:{type:Boolean,default:!0}},data:function(){return{btnLoading:!1,schdulingPlanId:""}},inject:["formData"],mounted:function(){},methods:{getTemplate:function(){var e=this,t=this.isRaw?o.GetImportTemplate:o.GetAuxImportTemplate;t({inStoreType:this.formData.InStoreType}).then((function(t){window.open((0,n.combineURL)(e.$baseUrl,t.Data))}))},beforeUpload:function(e){var t=this,a={In_Store_Type:this.formData.InStoreType,Is_Replace_Purchase:this.formData.Is_Replace_Purchase,SysProjectId:this.formData.SysProjectId,PartyUnit:this.formData.PartyUnit,Supplier:this.formData.Supplier},i=new FormData;i.append("Receipt",JSON.stringify(a)),i.append("Files",e),this.btnLoading=!0;var r=this.isRaw?o.Import:o.AuxImport;r(i).then((function(e){if(e.IsSucceed){var a=e.Data||[];t.$emit("getAddList",a),t.$message({type:"success",message:"导入成功"}),t.$emit("close")}else t.$message({type:"error",message:e.Message}),e.Data&&window.open((0,n.combineURL)(t.$baseUrl,e.Data));t.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},"3ef5":function(e,t,a){"use strict";a("7fc2")},"46c2":function(e,t,a){"use strict";a("b92d")},"49e6":function(e,t,a){"use strict";a.r(t);var i=a("3ef2"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"4c24":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("c14f")),n=i(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("841c");var o=a("ed08"),s=a("5480"),l=a("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,n.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,n.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},"4c35":function(e,t,a){},"4fa5":function(e,t,a){"use strict";a("3a56e")},"501a":function(e,t,a){"use strict";a("ace8")},5098:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b"),a("14d9"),a("13d5"),a("e9f5"),a("9485"),a("d3b7");var i=a("2245");t.default={data:function(){return{activeName:"1",radioSelect:null,btnLoading:!1,form:{},list:[],tableData:[],specificationUsage:0}},watch:{specificationUsage:function(e,t){1===e&&(this.activeName="2")}},methods:{getOption:function(e){var t=this;this.specificationUsage=e.SpecificationUsage,e&&(this.list=e.RcptRawParams.split("*")),(0,i.GetRawStandardsList)({rawId:e.RawId}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleRadioChange:function(e,t){e.stopPropagation(),this.currentRow=Object.assign({},t)},submit:function(){var e=this;if("1"===this.activeName){var t=!0,a=this.list.reduce((function(a,i){if(e.form[i])return a.push(e.form[i]),a;t=!1}),[]);if(!t)return void this.$message({message:"输入数据不能为0",type:"warning"});this.$emit("standard",{type:1,val:a.join("*")})}else{if(!this.currentRow)return void this.$message({message:"请选择规格",type:"warning"});this.$emit("standard",{type:2,val:this.currentRow})}this.handleClose()},handleClose:function(){this.$emit("close")}}}},"5a1e":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("5530")),n=i(a("c14f")),o=i(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var s=a("9002"),l=a("93aa"),u=a("ed08");t.default={props:{bigTypeData:{type:Number,default:1},formData:{type:Object,default:function(){}},projectList:{type:Array,required:!0,default:function(){return[]}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{OrderCode:"",CategoryId:"",RawName:"",Supplier:"",SupplierName:"",SysProjectId:"",Thick:"",Width:"",Length:"",Material:"",Raw_FullName:"",IsFinished:"0"},selectRow:null,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[],BigType:1}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.PurchaseSubId===t.PurchaseSubId}))}))},isRaw:function(){return"0"==this.$route.params.type},materialType:function(){return this.$route.params.type},materialTypeName:function(){return"0"==this.$route.params.type?"原料":"辅料"},gridCode:function(){return this.isRaw?"PROAddRawPurchase":"PROAddPurchaseAuxList"}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)},bigTypeData:{handler:function(e,t){this.BigType=e},immediate:!0}},created:function(){this.getCategoryTreeList()},mounted:function(){this.getConfig();var e=this.formData,t=e.Supplier,a=e.SysProjectId;t&&a&&(this.form.Supplier=t,this.form.SysProjectId=a)},methods:{getConfig:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)(e.gridCode);case 1:e.columns=t.v,e.columnsOption(1),e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChange:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return t.BigType=e,t.resetForm("form"),a.n=1,t.columnsOption();case 1:return a.a(2)}}),a)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.currentColumns=JSON.parse(JSON.stringify(e.columns));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this,t=this.isRaw?l.GetCategoryTreeList:l.GetAuxCategoryTreeList;t({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$nextTick((function(a){var i;null===(i=e.$refs)||void 0===i||i.treeSelect.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){var a;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,a=e.isRaw?l.GetRawProcurementDetails:l.GetAuxProcurementDetails,a((0,r.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.InStoreCount=e.AvailableCount,e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},addToList:function(){var e=(0,u.deepClone)(this.multipleSelection);this.$emit("getAddList",e),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},"6d57":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var i=a("9643");t.default={props:{isRaw:{type:Boolean,default:!0}},data:function(){return{warehouses:[],locations:[],form:{Warehouse_Id:"",Location_Id:""},btnLoading:!1,rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){this.getWarehouseListOfCurFactory()},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=e.warehouses.find((function(t){return t.Id===e.form.Warehouse_Id})),i=e.locations.find((function(t){return t.Id===e.form.Location_Id}));e.$emit("warehouse",{warehouse:a,location:i}),e.btnLoading=!1,e.handleClose()}))},getWarehouseListOfCurFactory:function(){var e=this;(0,i.GetWarehouseListOfCurFactory)({type:this.isRaw?"原材料仓库":"辅料仓库"}).then((function(t){if(t.IsSucceed){if(e.warehouses=t.Data,e.warehouses.length){var a=e.warehouses.find((function(e){return e.Is_Default}));a&&(e.form.Warehouse_Id=a.Id,e.getLocationList(!0))}}else e.$message({message:t.Message,type:"error"})}))},wareChange:function(e){this.form.Location_Id="",e&&this.getLocationList(!0)},getLocationList:function(e){var t=this;(0,i.GetLocationList)({Warehouse_Id:this.form.Warehouse_Id}).then((function(a){if(a.IsSucceed){if(t.locations=a.Data,t.locations.length&&e){var i=t.locations.find((function(e){return e.Is_Default}));i&&(t.form.Location_Id=i.Id)}}else t.$message({message:a.Message,type:"error"})}))},handleClose:function(){this.$emit("close")}}}},"6dcb":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("2532");var r=i(a("6612"));t.default={props:{columns:{type:Array,default:function(){return[]}}},data:function(){return{resultArray:[],indexes:[],loading:!1}},methods:{setLoading:function(e){this.loading=e},submit:function(){this.loading=!0,this.$emit("submit",1)},setTbData:function(e,t){this.resultArray=e.map((function(e){return e.InStoreWeight=(0,r.default)(e.InStoreWeightKG).divide(1e3).format("0.[00000]"),e.Pound_Weight=(0,r.default)(e.Pound_Weight_KG).divide(1e3).format("0.[000]"),e.Voucher_Weight=(0,r.default)(e.Voucher_Weight_KG).divide(1e3).format("0.[000]"),e.TaxUnitPrice=(0,r.default)(e.TaxUnitPriceKG).multiply(1e3).format("0.[00]"),e})),this.indexes=t},rowStyle:function(e){var t=e.rowIndex;if(this.indexes.includes(t))return{backgroundColor:"#ED8591",color:"#ffffff"}}}}},"719c":function(e,t,a){},7692:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("2909")),n=i(a("c14f")),o=i(a("1da1")),s=i(a("5530"));a("99af"),a("4de4"),a("7db0"),a("c740"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("13d5"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("9485"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("2532"),a("38cf"),a("841c"),a("498a"),a("159b");var l=a("ed08"),u=i(a("0d62")),c=i(a("8e05")),d=i(a("da27")),f=i(a("aabc")),h=i(a("a747")),m=i(a("9764")),p=i(a("bbb2")),y=i(a("c7ab")),g=i(a("a657")),b=i(a("b76a")),v=a("93aa"),_=a("6186"),S=a("3166"),I=a("5480"),w=a("cf45"),C=i(a("0b513")),x=a("2f62"),P=a("30c8"),R=a("e144"),k=i(a("5d4b")),T=i(a("29d9")),D=i(a("b5b0")),L=a("90d1"),N=i(a("b132")),A=i(a("fe16")),F=i(a("d9e9")),M=i(a("9925")),$=i(a("cb17"));t.default={components:{SelectMaterialType:M.default,SelectExternal:F.default,DynamicCustomFieldFilter:A.default,USSLInboundDetailImport:N.default,SelectLocation:D.default,SelectWarehouse:T.default,SelectMaterialStoreType:k.default,Repeat:C.default,AddPurchaseList:d.default,BatchEdit:u.default,ImportFile:h.default,Warehouse:m.default,Standard:p.default,AddList:c.default,AddRawMaterialList:f.default,OSSUpload:y.default,DynamicTableFields:g.default,draggable:b.default},mixins:[$.default],props:{pageType:{type:Number,default:void 0}},data:function(){return{columns:[],num:1,returning:!1,isDraft:!0,isRetract:!1,tbLoading:!1,filterVisible:!1,statisticTime:"",multipleSelection:[],ProjectList:[],PartyUnitList:[],SupplierList:[],formStatus:+this.$route.query.status||"",form:{InStoreNo:"",InStoreType:+this.$route.query.type||1,InStoreDate:this.getDate(),Delivery_No:"",Purchase_Contract_No:"",CarNumber:"",Is_Replace_Purchase:!1,Driver:"",DriverMobile:"",Car_Pound_Weight:null,Car_Voucher_Weight:null,Remark:"",ProjectName:"",ProjectId:"",SysProjectId:"",PartyUnitName:"",PartyUnit:"",SupplierName:"",Supplier:"",Attachment:"",Status:null,Weight_Method:0,Has_Invoice:!1,Invoice_No:"",Invoice_Make_Time:"",Invoice_Receive_Time:""},rules:{InStoreType:[{required:!0,message:"请选择",trigger:"change"}],InStoreDate:[{required:!0,message:"请选择日期",trigger:"change"}],PartyUnit:[{required:!0,message:"请选择",trigger:"change"}],Is_Replace_Purchase:[{required:!0,message:"请选择",trigger:"change"}],ProjectId:[{required:!0,message:"请选择",trigger:"change"}],Supplier:[{required:!0,message:"请选择",trigger:"change"}]},searchForm:{RawNameFull:"",RawName:"",Thick:"",Spec:"",Material:"",SysProjectId:"",CategoryId:"",Length:"",Width:"",Supplier:"",PartyUnit:"",WarehouseId:"",Location_Id:""},currentComponent:"",title:"",dWidth:"60%",isSingle:!1,submitLoading:!1,saveLoading:!1,search:function(){return{}},openAddList:!1,dialogVisible:!1,dialogRepeatVisible:!1,BigType:1,fileListData:[],fileListArr:[],searchNum:1,rootTableData:[],tableData:[],popoverVisible:!1,manualHideTableColumns:[],RawReceiptTypeList:[],checkTypeList:[{BigType:1,checkList:[],checkSameList:[],remarkList:[]},{BigType:2,checkList:[],checkSameList:[],remarkList:[]},{BigType:3,checkList:[],checkSameList:[],remarkList:[]},{BigType:99,checkList:[],checkSameList:[],remarkList:[]}],showTable:!1,adjustAmountForm:{MoneyAdjustDate:"",Remark:""}}},computed:(0,s.default)({isRaw:function(){return"0"==this.$route.params.type},isAux:function(){return"1"==this.$route.params.type},materialType:function(){return this.$route.params.type},materialTypeName:function(){return"0"==this.$route.params.type?"原料":"辅料"},isView:function(){return!["InboundAddRaw","InboundAddAux","InboundEditRaw","InboundEditAux"].includes(this.$route.name)},isInvoices:function(){return"addInvoices"===this.$route.name},isAdd:function(){return["InboundAddRaw","InboundAddAux"].includes(this.$route.name)},isEdit:function(){return["InboundEditRaw","InboundEditAux"].includes(this.$route.name)},isPurchase:function(){return 1==this.form.InStoreType},isCustomer:function(){return 2==this.form.InStoreType},isManual:function(){return 3==this.form.InStoreType},isReturn:function(){return 88===this.formStatus},isAdjustAmount:function(){return"InboundAdjustAmount"===this.$route.name||"OutboundAdjustAmount"===this.$route.name},isInboundAdjustAmount:function(){return"InboundAdjustAmount"===this.$route.name},isOutboundAdjustAmount:function(){return"OutboundAdjustAmount"===this.$route.name},gridCode:function(){return this.isRaw?"PRORawPurchaseList":"PROPurchaseAuxListNew"}},(0,x.mapGetters)("factoryInfo",["checkDuplicate"])),mounted:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.search=(0,l.debounce)(e.fetchData,800,!0),t.n=1,e.getProject();case 1:if(e.isAdd){t.n=2;break}return t.n=2,e.getInfo();case 2:return t.a(2)}}),t)})))()},provide:function(){return{formData:this.form,checkDuplicate:this.getDuplicate}},methods:{initCreated:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.getFormLocal(),t.n=1,e.getFactoryInfo();case 1:return t.n=2,e.getOMALatestStatisticTime();case 2:return t.n=3,e.getTableColumns();case 3:return t.n=4,e.getRawReceiptTypeList();case 4:return t.n=5,e.getSuppliers();case 5:return t.n=6,e.getPartyAs();case 6:return t.a(2)}}),t)})))()},rowStyle:function(e){var t=e.row;if(t.WaitInStoreWeight<0)return{"background-color":"#F2AAB3!important"}},changeKeyInfo:function(e,t){var a=this.tableData.map((function(t){return t[e]})),i=(0,l.uniqueArr)(a);1===i.length?this.form[e]=t:this.form[e]=""},changeFormKey:function(e){var t=this;this.tableData.forEach((function(a){t.$set(a,e,t.form[e])}))},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},init:function(e){var t=this;e=e.filter((function(e){return e.Style=e.Style?JSON.parse(e.Style):"","ReturnSupplierName"===e.Code&&(e.Is_Edit=!1),t.isPurchase?(L.INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS.includes(e.Code)&&(e.Is_Edit=!1),"Tax_All_Price"===e.Code&&t.isRaw&&(e.Style={tips:"入库凭证重*订单含税单价"}),"NoTaxAllPrice"===e.Code&&t.isRaw&&(e.Style={tips:"入库凭证重*订单不含税单价"}),!L.INBOUND_DETAIL_HIDE_FIELDS.isPurchase.includes(e.Code)):t.isCustomer?(["ProjectName","PartyUnitName"].includes(e.Code)&&(e.Is_Must_Input=!0),!L.INBOUND_DETAIL_HIDE_FIELDS.isCustomer.includes(e.Code)):t.isManual?!L.INBOUND_DETAIL_HIDE_FIELDS.isManual.includes(e.Code):void 0})),this.columns=JSON.parse(JSON.stringify(e)),this.getAllColumnsOption()},getCheckList:function(e,t){var a=this,i=[];i=this.checkDuplicate?["PurchaseNo","ProjectName","Project_Code","SupplierName","RawNameFull","CategoryName","RawName","RawCode","Material","Spec","Actual_Spec","TaxUnitPrice","Tax_Rate","Warehouse_Location","FurnaceBatchNo"]:[];var r="Width",n="Length";this.checkTypeList.map((function(o){o.BigType===e&&(o.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),o.checkList=o.checkList.map((function(e){return e.Code})),o.checkSameList=t.filter((function(e){return e.Is_Display&&i.includes(e.Code)})),o.checkSameList=o.checkSameList.map((function(e){return e.Code})),a.checkDuplicate&&(o.checkSameList.unshift("PurchaseSubId"),2===e?o.checkSameList.push(n):1===e&&(o.checkSameList.push(r),o.checkSameList.push(n))),o.remarkList=t.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),o.remarkList=o.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}})))}))},getAllColumnsOption:function(){var e=this,t=[1,2,3],a=JSON.parse(JSON.stringify(this.columns));t.forEach((function(t){e.getCheckList(t,a)}))},tbfetchData:function(){this.tableData=[]},handleCopy:function(e,t){var a=JSON.parse(JSON.stringify(e));a.Sub_Id&&(a.Sub_Id=""),delete a._X_ROW_KEY,delete a.index,this.$emit("updateTb",[a],"copy"),this.tableData.splice(t+1,0,a)},setRowFullName:function(e){this.isRaw&&(e.Raw_FullName=(0,P.getMaterialName)(e))},lengthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},widthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},tbSelectChange:function(e){this.multipleSelection=e.records},addData:function(e){var t,a=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.map((function(e){return e.Spec=1===e.BigType&&a.isRaw?e.Thick:e.Spec,e.Actual_Spec=e.Actual_Spec||(1===e.BigType?e.Thick:e.Spec),a.isRaw&&(e.RawNameFull=(0,P.getMaterialName)(e)),a.isPurchase&&a.isRaw&&(a.$set(e,"OrderTaxUnitPrice",(e.OrderTaxUnitPrice||e.TaxUnitPrice).toFixed(6)/1),a.$set(e,"OrderNoTaxUnitPrice",((e.OrderTaxUnitPrice||e.TaxUnitPrice)/(1+e.Tax_Rate/100)).toFixed(6)/1)),"number"!==typeof e.Tax_Rate&&1!=a.form.InStoreType&&(e.Tax_Rate=13),e.Specific_Gravity&&3!==e.BigType||!e.PurchaseCount||(e.Theory_Weight=e.PurchaseWeight||0),i&&1==a.form.InStoreType&&(e.InStoreCount=e.PurchaseCount||0,e.Theory_Weight=e.PurchaseWeight||0),a.checkWeight(e),e.rowIndex="",e}));(t=this.rootTableData).push.apply(t,(0,r.default)(n)),this.handleSearch()},checkWeight:function(e){if(this.isAux)this.countTaxUnitPrice(e,"InStoreCount");else if(0!==e.Specific_Gravity&&!e.Specific_Gravity||3===e.BigType)this.countTaxUnitPrice(e,"InStoreCount_Width_Length");else{if(1!==e.BigType&&2!==e.BigType||0!==e.Specific_Gravity&&0!==e.Length)if(1===e.BigType)if(0===e.Thick||0===e.Width)e.Theory_Weight=0;else if(e.Thick&&e.Width&&e.Length&&e.Specific_Gravity){var t=(("花纹板"===e.CategoryName?1:e.Thick)*e.Width*e.Length*e.Specific_Gravity/1e3).toFixed(this.unitWeightDecimal);e.Theory_Weight=(t*e.InStoreCount).toFixed(this.weightDecimal)/1}else e.Theory_Weight="";else if(2===e.BigType)if(e.Length&&e.Specific_Gravity){var a=(e.Length*e.Specific_Gravity/1e3).toFixed(this.unitWeightDecimal);e.Theory_Weight=(a*e.InStoreCount).toFixed(this.weightDecimal)/1}else e.Theory_Weight="";else e.Theory_Weight="";else e.Theory_Weight=0;this.countTaxUnitPrice(e,"InStoreCount_Width_Length")}},countTaxUnitPrice:function(e,t){"TaxUnitPrice"===t&&(this.taxMode?this.$set(e,"NoTaxUnitPrice",(Number(e.TaxUnitPrice||0)/(1+(e.Tax_Rate||0)/100)).toFixed(L.INBOUND_DETAIL_UNIT_PRICE_DECIMAL)/1):this.$set(e,"TaxUnitPrice",(Number(e.NoTaxUnitPrice||0)*(1+(e.Tax_Rate||0)/100)).toFixed(L.INBOUND_DETAIL_UNIT_PRICE_DECIMAL)/1));var a=0==this.form.Weight_Method?e.Theory_Weight:e.Voucher_Weight;this.isPurchase&&this.isRaw?(e.Tax_All_Price=((e.OrderTaxUnitPrice||0)*(e.Voucher_Weight||0)).toFixed(L.DETAIL_TOTAL_PRICE_DECIMAL)/1,e.TaxUnitPrice=(e.Tax_All_Price/a).toFixed(L.INBOUND_DETAIL_UNIT_PRICE_DECIMAL)/1):this.isRaw?"TaxUnitPrice"!==t&&"Theory_Weight"!==t&&"Voucher_Weight"!==t&&"InStoreCount_Width_Length"!==t||(0===a||0===e.TaxUnitPrice?e.Tax_All_Price=0:a&&e.TaxUnitPrice?e.Tax_All_Price=Number(((a||0)*(e.TaxUnitPrice||0)).toFixed(L.DETAIL_TOTAL_PRICE_DECIMAL))/1:e.Tax_All_Price=""):e.Tax_All_Price=Number(((e.InStoreCount||0)*(e.TaxUnitPrice||0)).toFixed(L.DETAIL_TOTAL_PRICE_DECIMAL))/1,this.taxAllPriceChange(e),this.$emit("updateRow")},taxAllPriceChange:function(e){var t,a=0==this.form.Weight_Method?e.Theory_Weight:e.Voucher_Weight;this.isPurchase&&this.isRaw?(e.NoTaxAllPrice=((e.OrderNoTaxUnitPrice||0)*(e.Voucher_Weight||0)).toFixed(L.DETAIL_TOTAL_PRICE_DECIMAL)/1,e.NoTaxUnitPrice=(e.NoTaxAllPrice/a).toFixed(L.INBOUND_DETAIL_UNIT_PRICE_DECIMAL)/1):this.isRaw?0==a||0==e.NoTaxUnitPrice?e.NoTaxAllPrice="":e.NoTaxAllPrice=Number(((a||0)*(e.NoTaxUnitPrice||0)).toFixed(L.DETAIL_TOTAL_PRICE_DECIMAL))/1:e.NoTaxAllPrice=Number((e.InStoreCount||0)*(e.NoTaxUnitPrice||0)).toFixed(L.DETAIL_TOTAL_PRICE_DECIMAL)/1,e.Tax=((e.Tax_All_Price||0)-(e.NoTaxAllPrice||0)).toFixed(L.DETAIL_TOTAL_PRICE_DECIMAL)/1,null===(t=this.$refs)||void 0===t||null===(t=t.xTable)||void 0===t||t.updateFooter()},outStoreChange:function(e){var t=e.Specific_Gravity;t?1===e.BigType?Number(e.Width*e.Length*Number("花纹板"===e.CategoryName?1:e.Spec)*t*e.OutStoreCount):2===e.BigType?Number(e.Length*t*e.OutStoreCount):Number(e.PerWeight*e.OutStoreCount):(e.PerWeight,e.OutStoreCount),e.ReturnVoucherWeight=(e.PerVoucherWeight*e.OutStoreCount).toFixed(this.weightDecimal)/1,this.$emit("updateRow")},getDuplicate:function(){return this.checkDuplicate},getFactoryInfo:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("factoryInfo/getWorkshop");case 1:return t.a(2)}}),t)})))()},handleRetract:function(){this.isRetract=!this.isRetract},handleSearch:function(e){var t=this;this.tableData=this.rootTableData,e&&(this.tableData=this.tableData.filter((function(t){var a=!0;return e&&e.forEach((function(e){var i;!e.Value||null!==(i=t[e.Code])&&void 0!==i&&i.includes(e.Value)||(a=!1)})),a}))),this.searchForm.RawNameFull&&(this.tableData=this.tableData.filter((function(e){return(0,P.isMaterialInclude)(e.Raw_FullName,t.searchForm.RawNameFull)}))),this.searchForm.RawName&&(this.tableData=this.tableData.filter((function(e){return e.RawName.includes(t.searchForm.RawName)}))),this.searchForm.Thick&&(this.tableData=this.tableData.filter((function(e){return e.Thick.includes(t.searchForm.Thick)}))),this.searchForm.Spec&&(this.tableData=this.tableData.filter((function(e){return e.Spec.includes(t.searchForm.Spec)}))),this.searchForm.Material&&(this.tableData=this.tableData.filter((function(e){return e.Material.includes(t.searchForm.Material)}))),this.searchForm.SysProjectId&&(this.tableData=this.tableData.filter((function(e){return e.SysProjectId===t.searchForm.SysProjectId}))),this.searchForm.CategoryId&&(this.tableData=this.tableData.filter((function(e){return e.CategoryId===t.searchForm.CategoryId}))),this.searchForm.Length&&(this.tableData=this.tableData.filter((function(e){return Number(e.Length)===Number(t.searchForm.Length)}))),this.searchForm.Width&&(this.tableData=this.tableData.filter((function(e){return Number(e.Width)===Number(t.searchForm.Width)}))),this.searchForm.Supplier&&(this.tableData=this.tableData.filter((function(e){return e.Supplier===t.searchForm.Supplier}))),this.searchForm.PartyUnit&&(this.tableData=this.tableData.filter((function(e){return e.PartyUnit===t.searchForm.PartyUnit}))),this.searchForm.WarehouseId&&(this.tableData=this.tableData.filter((function(e){return e.WarehouseId===t.searchForm.WarehouseId}))),this.searchForm.LocationId&&(this.tableData=this.tableData.filter((function(e){return e.LocationId===t.searchForm.LocationId})))},handleReset:function(){this.searchForm.RawNameFull="",this.searchForm.RawName="",this.searchForm.Thick="",this.searchForm.Spec="",this.searchForm.Material="",this.searchForm.SysProjectId="",this.searchForm.CategoryId="",this.searchForm.Length="",this.searchForm.Width="",this.searchForm.Supplier="",this.searchForm.PartyUnit="",this.searchForm.WarehouseId="",this.searchForm.LocationId="",this.tableData=this.rootTableData},getTableColumns:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){var a,i,r,o;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.showTable=!1,t.n=1,(0,I.getTableConfig)(e.gridCode,e.BigType);case 1:e.columns=t.v,e.isReturn&&(i=[{Code:"AvailableCount",Display_Name:"可退数量",Width:150,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"OutStoreCount",Display_Name:"退货数量",Width:120,Is_Edit:!0,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"OutStoreWeight",Display_Name:"退货理重",Width:120,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"ReturnSupplierName",Display_Name:"退货供应商",Width:120,Is_Edit:!0,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"ReturnVoucherWeight",Display_Name:"退货凭证重",Width:120,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"ReturnPoundWeight",Display_Name:"退货磅重",Width:120,Frozen_Dirction:"right",Is_Edit:!0,Is_Frozen:!0}],e.columns.forEach((function(e){e.Is_Edit=!1})),(a=e.columns).push.apply(a,i)),e.isAdjustAmount&&(o=[{Code:"Adjust_Amount_In",Display_Name:"入库调整金额",Width:150,Frozen_Dirction:"right",Is_Frozen:!0,Is_Edit:e.isInboundAdjustAmount},{Code:"Adjust_Amount_Out",Display_Name:"出库调整金额",Width:120,Frozen_Dirction:"right",Is_Frozen:!0,Is_Edit:e.isOutboundAdjustAmount}],e.columns.forEach((function(e){e.Is_Edit=!1})),(r=e.columns).push.apply(r,o)),e.init(e.columns),e.showTable=!0;case 2:return t.a(2)}}),t)})))()},getRawReceiptTypeList:function(){var e=this;(0,w.getDictionary)("RawReceiptType").then((function(t){e.RawReceiptTypeList=t.filter((function(e){return e.Is_Enabled}))}))},uploadSuccess:function(e,t,a){this.fileListArr=JSON.parse(JSON.stringify(a))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},handlePreview:function(e){return(0,o.default)((0,n.default)().m((function t(){var a;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:if(a="",!e.response||!e.response.encryptionUrl){t.n=1;break}a=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,_.GetOssUrl)({url:e.encryptionUrl});case 2:a=t.v,a=a.Data;case 3:window.open(a);case 4:return t.a(2)}}),t)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})},fetchData:function(){},getInfo:function(){var e=this;(0,v.GetInstoreDetail)({inStoreNo:this.$route.query.id,materialType:this.materialType,inStoreType:this.$route.query.type}).then((function(t){if(t.IsSucceed){var a=t.Data.Receipt,i=t.Data.Sub,r=a.InStoreDate,n=a.CarNumber,o=a.Driver,s=a.DriverMobile,l=a.Car_Pound_Weight,u=a.Car_Voucher_Weight,c=a.Remark,d=a.ProjectId,f=a.SysProjectId,h=a.PartyUnit,m=a.Supplier,p=a.Attachment,y=a.Status,g=a.Delivery_No,b=a.Purchase_Contract_No,v=a.Is_Replace_Purchase,_=a.Invoice_Make_Time,S=a.Invoice_No,I=a.Invoice_Receive_Time,w=a.Has_Invoice,C=a.Weight_Method,x=a.Id;if(e.form.InStoreDate=e.getDate(new Date(r)),e.form.CarNumber=n,e.form.Driver=o,e.form.DriverMobile=s,e.form.Car_Pound_Weight=l?Number(l.toFixed(3)):l,e.form.Car_Voucher_Weight=u?Number(u.toFixed(3)):u,e.form.Remark=c,e.form.ProjectId=d,e.form.SysProjectId=f,e.form.ProjectName=f?e.ProjectList.find((function(e){return e.Sys_Project_Id===f})).Short_Name:"",e.form.PartyUnit=h,e.form.PartyUnitName=h?e.PartyUnitList.find((function(e){return e.Id===h})).Name:"",e.form.Supplier=m,e.form.SupplierName=m?e.SupplierList.find((function(e){return e.Id===m})).Name:"",e.form.Status=y,e.form.Delivery_No=g,e.form.Purchase_Contract_No=b,e.form.Is_Replace_Purchase=v,e.form.Invoice_Make_Time=_,e.form.Invoice_No=S,e.form.Invoice_Receive_Time=I,e.form.Has_Invoice=!!e.isInvoices||w,e.form.Weight_Method=C,e.form.Id=x,p){e.form.Attachment=p;var P=p.split(",");P.forEach((function(t){var a=t.indexOf("?Expires=")>-1?t.substring(0,t.lastIndexOf("?Expires=")):t,i=decodeURI(a.substring(a.lastIndexOf("/")+1)),r={};r.name=i,r.url=a,r.encryptionUrl=a,e.fileListData.push(r),e.fileListArr.push(r)}))}var k=i.map((function(t,a){return t.index=(0,R.v4)(),t.Warehouse_Location=t.WarehouseName?t.WarehouseName+"/"+t.LocationName:"",t.Width=3===t.BigType||2===t.BigType?0:t.Width,t.Length=3===t.BigType?0:t.Length,t.NoTaxUnitPrice=(t.TaxUnitPrice/(1+t.Tax_Rate/100)).toFixed(L.INBOUND_DETAIL_UNIT_PRICE_DECIMAL)/1,e.isInboundAdjustAmount&&(t.Adjust_Amount_In=""),e.isOutboundAdjustAmount&&(t.Adjust_Amount_Out=""),e.taxAllPriceChange(t),t}));e.$nextTick((function(t){e.tableData=e.rootTableData=k}))}else e.$message({message:t.Message,type:"error"})}))},getOMALatestStatisticTime:function(){var e=this;(0,v.GetOMALatestStatisticTime)().then((function(t){t.IsSucceed&&(e.statisticTime=t.Data)}))},getSuppliers:function(){var e=this;(0,v.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var i in t.Data){var r={Id:i,Name:t.Data[i]};a.push(r)}e.SupplierList=a}else e.$message({message:t.Message,type:"error"})}))},getPartyAs:function(){var e=this;(0,v.GetPartyAs)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var i in t.Data){var r={Id:i,Name:t.Data[i]};a.push(r)}e.PartyUnitList=a}else e.$message({message:t.Message,type:"error"})}))},getProject:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,S.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectList=t.Data.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getWarehouse:function(e){var t=this,a=e.warehouse,i=e.location;this.currentRow?(this.currentRow.WarehouseId=a.Id,this.currentRow.LocationId=i.Id,this.$set(this.currentRow,"WarehouseName",a.Display_Name),this.$set(this.currentRow,"LocationName",i.Display_Name),this.$set(this.currentRow,"Warehouse_Location",a.Display_Name+"/"+i.Display_Name)):this.multipleSelection.forEach((function(e,r){t.$set(e,"WarehouseName",a.Display_Name),t.$set(e,"LocationName",i.Display_Name),t.$set(e,"Warehouse_Location",a.Display_Name+"/"+i.Display_Name),e.LocationId=i.Id,e.WarehouseId=a.Id})),this.handleUpdateRow()},batchEditorFn:function(e){var t=this;this.multipleSelection.forEach((function(a,i){e.forEach((function(e){if(t.$set(a,e.key,e.val),"Supplier"===e.key)t.$set(a,"SupplierName",e.name);else if("PartyUnit"===e.key)t.$set(a,"PartyUnitName",e.name);else if("SysProjectId"===e.key){var i=t.ProjectList.find((function(t){return t.Sys_Project_Id===e.val}));if(t.isRaw){var r=1===i.Big_Type?i.Version_Id:2===i.Big_Type?i.Version_Id2:i.Version_Id3,n=a.Versions.find((function(e){return e.Id===r}));if(n)a.Specific_Gravity=null===n||void 0===n?void 0:n.Specific_Gravity,a.Version_Id=null===n||void 0===n?void 0:n.Id;else{var o=a.Versions.find((function(e){return e.Is_System}));a.Specific_Gravity=null===o||void 0===o?void 0:o.Specific_Gravity,a.Version_Id=null===o||void 0===o?void 0:o.Id}}t.$set(a,"ProjectName",e.name),t.$set(a,"Project_Code",null===i||void 0===i?void 0:i.Code)}else"NoTaxUnitPrice"!==e.key&&"Tax_Rate"!==e.key||t.countTaxUnitPrice(a,"TaxUnitPrice")}))})),this.multipleSelection.forEach((function(a,i){e.forEach((function(e){["SysProjectId","Width","Length","InStoreCount"].includes(e.key)?t.checkWeight(a):["TaxUnitPrice"].includes(e.key)&&t.countTaxUnitPrice(a,e.key)}))})),this.handleUpdateRow(),this.handleClose()},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,a=e.val;1===t?this.$set(this.currentRow,"StandardDesc",a):(this.$set(this.currentRow,"StandardDesc",a.StandardDesc),this.currentRow.StandardId=a.StandardId)},typeChange:function(e){0!==e&&(this.fileListData=[],this.form.DriverMobile="",this.$refs["form"].resetFields(),this.form.ProjectName="",this.form.SysProjectId="",this.form.SupplierName="",this.form.Supplier="",this.form.PartyUnitName="",this.form.PartyUnit="",this.form.Is_Replace_Purchase=!1,this.form.InStoreType=e,this.multipleSelection=[],this.rootTableData=[],this.handleReset(),this.getTableColumns())},isReplacePurchaseChange:function(e){e?(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset()):(this.form.ProjectId="",this.form.SysProjectId="",this.form.Supplier="")},projectChange:function(e){this.form.ProjectName=this.ProjectList.find((function(t){return t.Id===e})).Short_Name,this.form.SysProjectId=this.ProjectList.find((function(t){return t.Id===e})).Sys_Project_Id,e&&(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},supplierChange:function(e){this.form.SupplierName=this.SupplierList.find((function(t){return t.Id===e})).Name,e&&(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},partyUnitChange:function(e){this.form.PartyUnitName=this.PartyUnitList.find((function(t){return t.Id===e})).Name,e&&(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},itemSupplierChange:function(e){e.SupplierName=this.SupplierList.find((function(t){return t.Id===e.Supplier})).Name},itemPartyUnitChange:function(e){e.PartyUnitName=this.PartyUnitList.find((function(t){return t.Id===e.PartyUnit})).Name},itemProjectChange:function(e){var t=this.ProjectList.find((function(t){return t.Sys_Project_Id===e.SysProjectId}));if(t){e.ProjectName=t.Short_Name,e.Project_Code=t.Code;var a=1===e.Big_Type?"Version_Id":2===e.Big_Type?"Version_Id2":"Version_Id3",i=e.Versions.find((function(e){return e.Id===t[a]}));i&&(e.Specific_Gravity=i.Specific_Gravity,e.Version_Id=t[a])}this.checkWeight(e)},openAddDialog:function(e){var t=this;if(this.form.Is_Replace_Purchase){if(!(1!=this.form.InStoreType&&3!=this.form.InStoreType||this.form.ProjectId&&this.form.Supplier))return void this.$message({message:"请先选择所属项目和供应商",type:"warning"});if(2==this.form.InStoreType&&(!this.form.ProjectId||!this.form.PartyUnit))return void this.$message({message:"请先选择所属项目和甲方单位",type:"warning"})}this.currentRow=e,this.openAddList=!0,e?(this.isSingle=!0,this.$nextTick((function(a){t.$refs["draft"].setRow(e)}))):this.isSingle=!1},closeView:function(){(0,l.closeTagView)(this.$store,this.$route)},handleImport:function(){this.currentComponent="ImportFile",this.dWidth="40%",this.title=this.materialTypeName+"导入",this.dialogVisible=!0},addListReset:function(e){this.rootTableData=[],this.tableData=[],this.getAddList(e,!1)},getAddList:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e.map((function(e){return t.form.Delivery_No&&(e.Delivery_No=t.form.Delivery_No),t.form.CarNumber&&(e.CarNumber=t.form.CarNumber),t.form.Driver&&(e.Driver=t.form.Driver),t.form.DriverMobile&&(e.DriverMobile=t.form.DriverMobile),e.Width=3===e.BigType||2===e.BigType?0:e.Width,e.Length=3===e.BigType?0:e.Length,t.isRaw&&(e.Raw_FullName=(0,P.getMaterialName)({BigType:e.BigType,RawName:e.RawName,Material:e.Material,Thick:e.Thick,Spec:e.Spec,Width:e.Width,Length:e.Length})),e})),!this.form.Is_Replace_Purchase||2!=this.form.InStoreType&&3!=this.form.InStoreType)a||this.form.Is_Replace_Purchase||2!=this.form.InStoreType&&3!=this.form.InStoreType||e.forEach((function(e,a){if(e.ProjectName&&e.Version_Id||!t.isRaw);else{var i,r,n=null===(i=e.Versions.find((function(e){return e.Is_System})))||void 0===i?void 0:i.Specific_Gravity;e.Specific_Gravity=null!==n&&void 0!==n?n:null,e.Version_Id=null===(r=e.Versions.find((function(e){return e.Is_System})))||void 0===r?void 0:r.Id}}));else{var i=this.ProjectList.find((function(e){return e.Sys_Project_Id===t.form.SysProjectId}));e.forEach((function(e){var r;if(e.ProjectName=t.form.ProjectName,e.Project_Code=null===(r=t.ProjectList.find((function(e){return e.Sys_Project_Id===t.form.SysProjectId})))||void 0===r?void 0:r.Code,e.SysProjectId=t.form.SysProjectId,e.SupplierName=t.form.SupplierName,e.Supplier=t.form.Supplier,e.PartyUnitName=t.form.PartyUnitName,e.PartyUnit=t.form.PartyUnit,i&&!a&&t.isRaw){var n=e.Versions.find((function(e){return e.Is_System})),o=1===e.Big_Type?"Version_Id":2===e.Big_Type?"Version_Id2":"Version_Id3",s=e.Versions.find((function(e){return e.Id===i[o]}));e.Specific_Gravity=(null===s||void 0===s?void 0:s.Specific_Gravity)||n.Specific_Gravity,e.Version_Id=(null===s||void 0===s?void 0:s.Id)||n.Id}}))}this.handleUpdateTb(e,"add",a)},handleUpdateTb:function(e,t,a){e.map((function(e,t){e.index=(0,R.v4)()}));var i=JSON.parse(JSON.stringify(e));"add"===t&&this.addData(i,a)},handleUpdateRow:function(){var e=this;this.rootTableData.map((function(t){var a=e.tableData.find((function(e){return e.index===t.index}));a&&Object.assign(t,a)}))},getRowName:function(e){var t=e.Name,a=e.Id;this.currentRow.Name=t,this.currentRow.RawId=a,this.currentRow.StandardDesc=""},handleBatchEdit:function(){var e=this;this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.multipleSelection,e.BigType,e.form.InStoreType,e.taxMode)}))},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handleWarehouse:function(e){this.currentRow=e,this.currentComponent="Warehouse",this.dWidth="40%",this.title="批量选择仓库/库位",!e&&(this.currentRow=null),this.dialogVisible=!0},handleDelete:function(e){var t,a=this;this.multipleSelection.forEach((function(e,t){var i=a.rootTableData.findIndex((function(t){return t.index===e.index}));a.rootTableData.splice(i,1),a.handleSearch()})),this.multipleSelection=[],null===(t=this.$refs)||void 0===t||t.xTable.clearCheckboxRow()},handleClose:function(){this.openAddList=!1,this.dialogVisible=!1,this.dialogRepeatVisible=!1},handleDetail:function(e){},checkValidate:function(e){this.handleUpdateRow();var t=(0,l.deepClone)(this.rootTableData);if(!t.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var a={status:!0,type:"",msg:""},i=t;if(i.length&&a.status&&(a=this.checkTb(i,e)),!a.status)return this.$message({message:"".concat(a.type+a.msg||"必填字段","不能为空"),type:"warning"}),{status:!1};var r={status:!0,type:"",msg:""};return r.status?{data:t,status:!0}:(this.$message({message:"".concat(r.type,"存在重复数据"),type:"warning"}),{status:!1})},checkTb:function(e,t){for(var a,i=this,r=function(a){var r,n=(null===(r=i.checkTypeList.find((function(t){return t.BigType===e[a].BigType})))||void 0===r?void 0:r.checkList)||[];1===t&&(n=n.filter((function(e){return"Voucher_Weight"!==e&&"InStoreWeight"!==e})));for(var o,s=e[a],l=function(){var e=n[u];if(["",null,void 0].includes(s[e])){var t=i.columns,a=t.find((function(t){return t.Code===e}));return{v:{v:{status:!1,msg:null===a||void 0===a?void 0:a.Display_Name,type:1===s.BigType?"板材":2===s.BigType?"型材":3===s.BigType?"钢卷":"其他"}}}}},u=0;u<n.length;u++)if(o=l(),o)return o.v;delete s._X_ROW_KEY,delete s.WarehouseName,delete s.LocationName},n=0;n<e.length;n++)if(a=r(n),a)return a.v;return{status:!0,msg:"",type:""}},handleSaveDraft:function(){this.isDraft=!0,this.saveDraft(1,!1)},submitInfo:function(){var e=this;this.isDraft?this.saveDraft(1,!0):this.$confirm("确认提交入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveDraft(3,!0)})).catch((function(){var t;e.$message({type:"info",message:"已取消"}),null===(t=e.$refs["repeat"])||void 0===t||t.setLoading(!1)}))},handleSubmit:function(e){this.isDraft=!1,this.saveDraft(3,!1)},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.isRaw&&(this.rootTableData=this.rootTableData.map((function(t){return t.InStoreWeight=0==e.form.Weight_Method?t.Theory_Weight:t.Voucher_Weight,t.Weight_Method=e.form.Weight_Method,t}))),this.handleUpdateRow();var i=this.checkValidate(t),r=i.data,n=i.status;n&&this.$refs["form"].validate((function(i){if(!i)return!1;var n=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){n.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)}));var o=(0,s.default)({},e.form);if(o.Attachment=n.join(","),o.Status=1===t?1:3,o.InStoreNo=e.$route.query.id,e.isRaw){var l=e.checkSameInfo(),u=l.show,c=l.resultArray,d=l.indexes;!a&&u&&e.checkDuplicate&&(e.dialogRepeatVisible=!0,e.$nextTick((function(t){e.$refs["repeat"].setTbData(c,d)})))}1===t?e.saveLoading=!0:e.submitLoading=!0,e.submitDraft(o,r)}))},submitDraft:function(e,t){var a=this;(0,v.SaveInStore)({Receipt:e,Sub:t,MaterialType:this.materialType,InStoreType:e.InStoreType}).then((function(e){var t;e.IsSucceed?(a.$message({message:"保存成功",type:"success"}),a.closeView()):a.$message({message:e.Message,type:"error"}),a.saveLoading=!1,a.submitLoading=!1,null===(t=a.$refs["repeat"])||void 0===t||t.setLoading(!1)}))},setSelectRow:function(e){this.multipleSelection=e},getDate:function(e){var t=e||new Date,a=t.getFullYear(),i=("0"+(t.getMonth()+1)).slice(-2),r=("0"+t.getDate()).slice(-2);return"".concat(a,"-").concat(i,"-").concat(r)},checkSameInfo:function(){var e=this,t=JSON.parse(JSON.stringify(this.tableData));t.forEach((function(t){var a=e.checkTypeList.find((function(e){return e.BigType===t.BigType})),i=a.checkSameList.filter((function(e){return!!e}));t.currentKey=i.reduce((function(e,a){return e+"-"+(t[a]||"").toString().trim()}),"")}));var a=t,i=a.reduce((function(e,t){return t.currentKey&&(e[t.currentKey]=(e[t.currentKey]||0)+1),e}),{}),r=Object.keys(i).filter((function(e){return i[e]>1})),n=a.filter((function(e){return r.includes(e.currentKey)})),o=n.reduce((function(e,t){return e[t.currentKey]||(e[t.currentKey]=[]),e[t.currentKey].push(t),e}),{}),s=Object.keys(o).reduce((function(e,t){return e=e.concat(o[t]),e}),[]),l=s.reduce((function(e,t,a){return 0!==a&&t.currentKey===s[a-1].currentKey||e.push(a),e}),[]);return{show:s.length>0,resultArray:s,indexes:l}},toggleFilter:function(){this.filterVisible=!this.filterVisible},handleReturn:function(){var e=this,t=this.checkValidate(),a=t.data,i=t.status;i&&this.$refs["form"].validate((function(t){if(!t)return!1;var i=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){i.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)}));var r="",n=(0,s.default)({},e.form);n.Attachment=i.join(","),n.InStoreNo=e.$route.query.id;var o=a.filter((function(e){return(e.OutStoreWeight>=e.AvailableWeight&&e.OutStoreCount!=e.AvailableCount||e.OutStoreWeight!=e.AvailableWeight&&e.OutStoreCount>=e.AvailableCount)&&(r="明细数据中数量或重量有一个值为库存最大值但另一个值非最大值，非最大值将自动被改为最大值",e.OutStoreCount=e.AvailableCount,e.OutStoreWeight=e.AvailableWeight),e.OutStoreCount>0}));r&&e.$message.info(r),e.returning=!0,(0,v.RawReturnByReceipt)({Receipt:n,Sub:o}).then((function(t){t.IsSucceed?(e.$message({message:"退货成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"}),e.returning=!1}))}))},setFormLocal:function(e,t){var a=this,i=localStorage.getItem("materialInboundDetailForm");i=i?JSON.parse(i):{},i[e]=t,localStorage.setItem("materialInboundDetailForm",JSON.stringify(i)),"Weight_Method"===e&&(this.$refs.xTable.updateFooter(),this.tableData.forEach((function(e){a.countTaxUnitPrice(e,"Theory_Weight")})))},getFormLocal:function(){var e=localStorage.getItem("materialInboundDetailForm");if(e)for(var t in e=JSON.parse(e),e)this.form[t]=e[t]},handleSubmitAdjustAmount:function(){var e=this,t=this.tableData.map((function(t){return{MoneyAdjustId:e.form.Id,StoreSubId:t.Sub_Id,Code:t.RawCode,Name:t.RawName,SysProjectId:t.SysProjectId,ProjectId:t.ProjectId,AdjustMoney:e.isInboundAdjustAmount?t.Adjust_Amount_In:t.Adjust_Amount_Out}})).filter((function(e){return e.AdjustMoney&&"0"!==e.AdjustMoney}));if(t.length){this.submitLoading=!0;var a={StoreType:this.isInboundAdjustAmount?1:0,MoneyAdjustDate:this.adjustAmountForm.MoneyAdjustDate,StoreId:this.form.Id,StoreNo:this.$route.query.id,MaterielType:this.materialType,Remark:this.adjustAmountForm.Remark,PurchaseContractNo:this.form.Purchase_Contract_No,Detail:t};(0,v.StoreMoneyAdjust)({model:a}).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"})})).finally((function(){e.submitLoading=!1}))}else this.$message.error("请输入调整金额")},handleSubmitInvoice:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$refs["form"].validate();case 1:(0,v.UpdateInvoiceInfo)({MaterialType:e.materialType,InStoreId:e.form.Id,HasInvoice:e.form.Has_Invoice,InvoiceNo:e.form.Invoice_No,InvoiceMakeTime:e.form.Invoice_Make_Time,InvoiceReceiveTime:e.form.Invoice_Receive_Time}).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({type:"error",message:t.Message})}));case 2:return t.a(2)}}),t)})))()},footerMethod:function(e){var t=this,a=e.columns,i=e.data,r=[a.map((function(e,a){return"InStoreWeight"===e.field?t.sumNum(i,0==t.form.Weight_Method?"Theory_Weight":"Voucher_Weight",5):L.INBOUND_DETAIL_SUMMARY_FIELDS.includes(e.field)?t.sumNum(i,e.field,5):0===a?"合计":null}))];return r},sumNum:function(e,t,a){for(var i=0,r=0;r<e.length;r++)i+=Number(e[r][t])||0;return i.toFixed(a)/1},searchCustomField:function(e){this.handleSearch(e)},syncWeight:function(){var e=this;this.tableData.forEach((function(t){e.$set(t,"Voucher_Weight",t.Theory_Weight),e.countTaxUnitPrice(t,"Voucher_Weight")}))}}}},"771c":function(e,t,a){"use strict";a.r(t);var i=a("c727"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"7eb7":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7");var r=i(a("3796")),n=a("93aa"),o=a("ed08");t.default={name:"USSLInboundDetailImport",components:{Upload:r.default},props:{materialType:{type:[Number,String],default:0}},data:function(){return{show:!1,dialogVisible:!1,btnLoading:!1}},inject:["formData"],created:function(){this.show=["scyth_pz","scyth_test","ussl","scyth_v4","ehss"].includes(localStorage.getItem("TenantId"))},methods:{handleClick:function(){this.dialogVisible=!0},handleSubmit:function(){this.$refs.upload.handleSubmit()},beforeUpload:function(e){var t=this,a={In_Store_Type:this.formData.InStoreType,Is_Replace_Purchase:this.formData.Is_Replace_Purchase,SysProjectId:this.formData.SysProjectId,PartyUnit:this.formData.PartyUnit,Supplier:this.formData.Supplier},i=new FormData;i.append("Receipt",JSON.stringify(a)),i.append("Files",e),i.append("MaterialType",this.materialType),this.btnLoading=!0,(0,n.ImportInstoreReceipt)(i).then((function(e){if(e.IsSucceed){var a=e.Data?e.Data.map((function(e){return e.InStoreCount=e.AvailableCount,e})):[];t.$emit("getAddList",a),t.$message({type:"success",message:"导入成功"}),t.dialogVisible=!1}else t.$message({type:"error",message:e.Message}),e.Data&&window.open((0,o.combineURL)(t.$baseUrl,e.Data));t.btnLoading=!1}))}}}},"7fc2":function(e,t,a){},8378:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CreateVersion=D,t.DelAuxCategoryEntity=v,t.DelAuxEntity=I,t.DelCategoryEntity=l,t.DelRawEntity=d,t.DeleteVersion=N,t.EditAuxEnabled=S,t.EditRawEnabled=c,t.ExportAuxForProject=z,t.ExportAuxList=k,t.ExportFindRawInAndOut=E,t.ExportInOutStoreReport=X,t.ExportPicking=F,t.ExportRawList=R,t.ExportRecSendProjectMaterialReport=Y,t.ExportRecSendProjectReport=J,t.ExportReceiving=A,t.ExportStagnationInventory=K,t.ExportStoreReport=H,t.FindInAndOutPageList=W,t.FindPickingNewPageList=O,t.FindPickingPageList=j,t.FindReceivingNewPageList=$,t.FindReceivingPageList=M,t.GetAuxCategoryDetail=b,t.GetAuxCategoryTreeList=y,t.GetAuxDetail=C,t.GetAuxFilterDataSummary=q,t.GetAuxForProjectDetail=G,t.GetAuxForProjectPageList=B,t.GetAuxPageList=w,t.GetAuxTemplate=x,t.GetAuxWHSummaryList=V,t.GetCategoryDetail=s,t.GetCategoryTreeList=n,t.GetCycleDate=U,t.GetList=T,t.GetRawDetail=h,t.GetRawPageList=f,t.GetRawTemplate=m,t.ImportAuxList=P,t.ImportRawList=p,t.SaveAuxCategoryEntity=g,t.SaveAuxEntity=_,t.SaveCategoryEntity=o,t.SaveRawEntity=u,t.UpdateVersion=L;var r=i(a("b775"));function n(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:e,timeout:12e5})}function y(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:e,timeout:12e5})}function R(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:e})}function D(e){return(0,r.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:e})}},"87cb":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[1!==e.formData.InStoreType?a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("点击此处下载导入模板")])],1):e._e(),a("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},r=[]},"8e05":function(e,t,a){"use strict";a.r(t);var i=a("f79a"),r=a("28668");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("2a870");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"03052914",null);t["default"]=s.exports},"92b2":function(e,t,a){},"94f0":function(e,t,a){"use strict";a.r(t);var i=a("7692"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"969fa":function(e,t,a){"use strict";a.r(t);var i=a("7eb7"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},"96cb":function(e,t,a){"use strict";a.r(t);var i=a("5098"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},9764:function(e,t,a){"use strict";a.r(t);var i=a("cd50"),r=a("d30d");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("e14a");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"ba9f89c8",null);t["default"]=s.exports},"9d7e2":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.FindPickingNewSum=o,t.FindReceivingNewSum=s,t.GetAuxCostReport=l,t.GetAuxCostReportSummary=u,t.GetListForContractSetting=n,t.GetListForContractSettingForMateriel=c;var r=i(a("b775"));function n(e){return(0,r.default)({url:"/SYS/ExternalCompany/GetListForContractSetting",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingNewSum",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingNewSum",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/MaterielReport/GetAuxCostReport",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/MaterielReport/GetAuxCostReportSummary",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/MaterielRawInStore/GetListForContractSettingForMateriel",method:"post",data:e})}},a1cd:function(e,t,a){"use strict";a("92b2")},a747:function(e,t,a){"use strict";a.r(t);var i=a("87cb"),r=a("49e6");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("a1cd");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"0801c7ae",null);t["default"]=s.exports},aabc:function(e,t,a){"use strict";a.r(t);var i=a("ff45"),r=a("2ee5");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("13e8e");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"4899f5e0",null);t["default"]=s.exports},ace8:function(e,t,a){},b132:function(e,t,a){"use strict";a.r(t);var i=a("d0ac"),r=a("969fa");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("d0b6");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"06c49ace",null);t["default"]=s.exports},b533:function(e,t,a){"use strict";a.r(t);var i=a("5a1e"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},b92d:function(e,t,a){},ba13:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.list,(function(t,i){return a("el-row",{key:t.id,staticClass:"item-x"},[a("div",{staticClass:"item"},[a("label",[e._v(" 属性名称 "),a("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},model:{value:t.key,callback:function(a){e.$set(t,"key",a)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),a("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[a("label",[e._v("请输入值 "),e.checkType(t.key,"number")?a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?a("el-input",{model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"supplierArray")?a("el-select",{attrs:{placeholder:"请选择供应商",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"supplierArray")}},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.supplierList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"partyUnitArray")?a("el-select",{attrs:{placeholder:"请选择甲方单位",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"partyUnitArray")}},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.partyUnitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"projectArray")?a("el-select",{attrs:{placeholder:"请选择所属项目",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"projectArray")}},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1):e._e()],1)]),a("span",{staticClass:"item-span"},0===i?[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),a("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(i)}}})])])})),a("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},r=[]},bbb2:function(e,t,a){"use strict";a.r(t);var i=a("149b"),r=a("96cb");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("4fa5");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"cae0a858",null);t["default"]=s.exports},c727:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(a("5530"));a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");t.default={name:"DynamicCustomFieldFilter",props:{title:{type:String,default:"扩展查询"},columns:{type:Array,default:function(){return[]}}},data:function(){return{show:!1,fields:[]}},watch:{columns:{handler:function(){this.getFields()},deep:!0}},mounted:function(){this.getFields()},methods:{getFields:function(){this.fields=this.columns.filter((function(e){return e.Is_Filter&&e.Is_Display&&-1!==e.Code.indexOf("Remark")})).map((function(e){return(0,r.default)((0,r.default)({},e),{},{Value:""})}))},handleSearch:function(){this.$emit("search",this.fields),this.show=!1},handleReset:function(){this.getFields(),this.handleSearch()}}}},cb17:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b64b");var r=i(a("c14f")),n=i(a("1da1")),o=i(a("05e5")),s=a("90d1"),l=a("1099");t.default={components:{ImportExcel:o.default},data:function(){return{unitWeightDecimal:s.UNIT_WEIGHT_DECIMAL,weightDecimal:s.WEIGHT_DECIMAL,TAX_MODE:0,taxMode:s.TAX_MODE}},created:function(){var e=this;return(0,n.default)((0,r.default)().m((function t(){var a,i,n,o,s,u,c,d;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetPreferenceSettingValue)({code:"materialConfig"});case 1:if(c=a=t.v,u=null===c,u){t.n=2;break}u=void 0===a;case 2:if(!u){t.n=3;break}d=void 0,t.n=4;break;case 3:d=a.Data;case 4:i=d,i&&(i=JSON.parse(i),e.unitWeightDecimal=(null===(n=i)||void 0===n?void 0:n.unitWeightDecimal)||e.unitWeightDecimal,e.weightDecimal=(null===(o=i)||void 0===o?void 0:o.weightDecimal)||e.weightDecimal,e.taxMode=(null===(s=i)||void 0===s?void 0:s.taxMode)||e.taxMode),e.initCreated();case 5:return t.a(2)}}),t)})))()}}},cd50:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},d025:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.fields&&e.fields.length?a("div",{staticStyle:{display:"inline-block"}},[a("el-popover",{ref:"popover",attrs:{placement:"bottom-end",width:"300",trigger:"click"},model:{value:e.show,callback:function(t){e.show=t},expression:"show"}},[a("div",{staticStyle:{"text-align":"right",margin:"0"}},[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:e.handleReset}},[e._v("重置")]),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.handleSearch}},[e._v("搜索")])],1),a("el-form",{staticStyle:{"margin-top":"12px"}},e._l(e.fields,(function(t){return a("el-form-item",{key:t.Id,attrs:{label:t.Display_Name}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:t.Value,callback:function(a){e.$set(t,"Value",a)},expression:"item.Value"}})],1)})),1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(e.title))])],1)],1):e._e()},r=[]},d0ac:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.show&&0==e.materialType?a("div",{staticStyle:{"margin-left":"10px"}},[a("el-button",{on:{click:e.handleClick}},[e._v("导入接收单")]),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"导入接收单",visible:e.dialogVisible,width:"40%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:function(t){e.dialogVisible=!1}}},[a("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1):e._e()],1):e._e()},r=[]},d0b6:function(e,t,a){"use strict";a("f656")},d111:function(e,t,a){"use strict";a.r(t);var i=a("6dcb"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},d172:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-tabs",{on:{"tab-click":e.handleSearch},model:{value:e.form.IsFinished,callback:function(t){e.$set(e.form,"IsFinished",t)},expression:"form.IsFinished"}},[a("el-tab-pane",{attrs:{label:"未完成",name:"0"}}),a("el-tab-pane",{attrs:{label:"已完成",name:"1"}})],1),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"采购单号",prop:"OrderCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"采购单号",type:"text"},model:{value:e.form.OrderCode,callback:function(t){e.$set(e.form,"OrderCode","string"===typeof t?t.trim():t)},expression:"form.OrderCode"}})],1)],1),e.isRaw?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1)],1):e._e(),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:e.materialTypeName+"分类",prop:"CategoryId"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",staticStyle:{width:"100%"},attrs:{"tree-params":e.treeParams},model:{value:e.form.CategoryId,callback:function(t){e.$set(e.form,"CategoryId",t)},expression:"form.CategoryId"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:e.materialTypeName+"名称",prop:"RawName"}},[a("el-input",{attrs:{clearable:"",placeholder:e.materialTypeName+"名称",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商",prop:"SupplierName"}},[a("el-input",{attrs:{clearable:"",placeholder:"供应商",type:"text"},model:{value:e.form.SupplierName,callback:function(t){e.$set(e.form,"SupplierName","string"===typeof t?t.trim():t)},expression:"form.SupplierName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),e.isRaw?[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"厚度",prop:"Thick"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"厚度"},model:{value:e.form.Thick,callback:function(t){e.$set(e.form,"Thick","string"===typeof t?t.trim():t)},expression:"form.Thick"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{type:"text",clearable:"",placeholder:"材质"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1)]:e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"0"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],2)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["WareCount"===t.Code?{key:"default",fn:function(t){var i=t.row;return[a("span",{style:{color:i.WareCount>i.PurchaseCount?"red":""}},[e._v(" "+e._s(i.WareCount)+" ")])]}}:"PurchaseWeight"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(Number(a.PurchaseWeight.toFixed(5)))+" ")]}}:"Actual_Spec"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Actual_Spec||"-")+" ")]}}:"DeliveryTime"===t.Code?{key:"default",fn:function(a){var i=a.row;return[e._v(" "+e._s(e._f("timeFormat")(i[t.Code],"{y}-{m}-{d}"))+" ")]}}:"PurchaseTime"===t.Code?{key:"default",fn:function(a){var i=a.row;return[e._v(" "+e._s(e._f("timeFormat")(i[t.Code],"{y}-{m}-{d}"))+" ")]}}:{key:"default",fn:function(a){var i=a.row;return[e._v(" "+e._s(i[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},r=[]},d30d:function(e,t,a){"use strict";a.r(t);var i=a("6d57"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=r.a},da27:function(e,t,a){"use strict";a.r(t);var i=a("d172"),r=a("b533");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("227d");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"0319921c",null);t["default"]=s.exports},e14a:function(e,t,a){"use strict";a("e9fe")},e4fd:function(e,t,a){},e5b4:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("2532");var r=i(a("2909")),n=i(a("c14f")),o=i(a("1da1")),s=a("e144");t.default={props:{isReplacePurchase:{type:Boolean,default:!1},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}},checkTypeList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}},isRaw:{type:Boolean,default:!0}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},Is_Component:"",value:"",options:[],list:[{id:(0,s.v4)(),val:void 0,key:""}],SupplierName:"",PartyUnitName:"",ProjectName:""}},mounted:function(){return(0,o.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{optionChange:function(e,t){var a;if("supplierArray"===t)this.SupplierName=null===(a=this.supplierList.find((function(t){return t.Id===e})))||void 0===a?void 0:a.Name;else if("partyUnitArray"===t){var i;this.PartyUnitName=null===(i=this.partyUnitList.find((function(t){return t.Id===e})))||void 0===i?void 0:i.Name}else if("projectArray"===t){var r;this.ProjectName=null===(r=this.projectList.find((function(t){return t.Sys_Project_Id===e})))||void 0===r?void 0:r.Short_Name}},handleAdd:function(){this.list.push({id:(0,s.v4)(),val:void 0,key:""})},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){var a,i,r,o;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.btnLoading=!0,a={},i=0;case 1:if(!(i<e.list.length)){t.n=4;break}if(r=e.list[i],r.val){t.n=2;break}if(o=!0,"Width"===r.key||"Length"===r.key||"InStoreCount"===r.key||"InStoreWeight"===r.key?0===r.val?e.$message({message:"值不能为0",type:"warning"}):e.$message({message:"值不能为空",type:"warning"}):"Tax_Rate"===r.key||"NoTaxUnitPrice"===r.key||"TaxUnitPrice"===r.key?o=!1:e.$message({message:"值不能为空",type:"warning"}),e.btnLoading=!1,!o){t.n=2;break}return t.a(2);case 2:a[r.key]=r.val;case 3:i++,t.n=1;break;case 4:e.list.map((function(t){"Supplier"===t.key?t.name=e.SupplierName:"PartyUnit"===t.key?t.name=e.PartyUnitName:"SysProjectId"===t.key&&(t.name=e.ProjectName)})),e.$emit("batchEditor",e.list),e.btnLoading=!1;case 5:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(a){return(!t.list.map((function(e){return e.key})).includes(a.key)||a.key===e)&&a.label}))},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},init:function(e,t,a){var i,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if(this.selectList=e,!this.isRaw){var o,s=[];return 2===a&&s.push({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"}),3===a&&s.push({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}),s.push({key:"InStoreCount",label:"入库数量",type:"number"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),void(o=this.options).push.apply(o,s.concat((0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)))}if(1===t)this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(i=this.options).push.apply(i,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"Width",label:"宽度(mm)",type:"number"},{key:"Length",label:"长度(mm)",type:"number"},{key:"InStoreCount",label:"入库数量",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}));else if(2===t){var l;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(l=this.options).push.apply(l,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"Length",label:"长度(mm)",type:"number"},{key:"InStoreCount",label:"入库数量",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}else if(3===t){var u;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(u=this.options).push.apply(u,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"InStoreCount",label:"入库数量",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}else{var c;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(c=this.options).push.apply(c,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===a?this.options.push({key:"InStoreCount",label:"入库数量",type:"number"}):2===a?(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==a&&4!==a||(this.options.push({key:"Material",label:"材质",type:"string"},n?{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"}:{key:"NoTaxUnitPrice",label:"不含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}}}}},e9fe:function(e,t,a){},eef2:function(e,t,a){"use strict";a.r(t);var i=a("2444"),r=a("94f0");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("fa6c8");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"4334058f",null);t["default"]=s.exports},f656:function(e,t,a){},f79a:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],a("el-col",{attrs:{span:2}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(a){var i=a.row;return[e._v(" "+e._s(0===i[t.Code]?"按需使用":1===i[t.Code]?"使用标准规格":2===i[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(a){var i=a.row;return[e._v(" "+e._s(e._f("displayValue")(i[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},r=[]},fa6c8:function(e,t,a){"use strict";a("20e4")},fe16:function(e,t,a){"use strict";a.r(t);var i=a("d025"),r=a("771c");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("46c2");var o=a("2877"),s=Object(o["a"])(r["default"],i["a"],i["b"],!1,null,"da626940",null);t["default"]=s.exports},ff45:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return r}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("div",{staticClass:"cs-main"},[a("div",{staticClass:"cs-left"},[a("div",{staticClass:"cs-left__text"},[e._v("物料类别")]),a("div",{staticClass:"cs-left__tree"},[a("tree-detail",{ref:"tree",staticStyle:{"margin-top":"20px"},attrs:{icon:"icon-folder",loading:e.treeLoading,"tree-data":e.treeData,"show-detail":"","expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick}})],1)]),a("div",{staticClass:"cs-right"},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80"}},[e.isRaw?a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1):e._e(),a("el-form-item",{attrs:{label:this.isRaw?"原料名称":"辅料名称",prop:"Materiel_Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Materiel_Name,callback:function(t){e.$set(e.form,"Materiel_Name",t)},expression:"form.Materiel_Name"}})],1),a("el-form-item",{attrs:{label:"唯一编码",prop:"Materiel_Code"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Materiel_Code,callback:function(t){e.$set(e.form,"Materiel_Code",t)},expression:"form.Materiel_Code"}})],1),a("el-form-item",{attrs:{label:e.isRaw?"规格(厚度mm)":"规格",prop:"Spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material",t)},expression:"form.Material"}})],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:e.searchReset}},[e._v("重置")])],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:e.Code,title:e.Display_Name,"min-width":e.Width}})]}))],2)],1),a("div",{staticClass:"cs-footer"},[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)],1)]),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)])},r=[]}}]);