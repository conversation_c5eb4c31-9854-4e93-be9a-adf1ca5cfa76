(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-473d6277"],{1403:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"plan common-wrapper"},[a("el-card",{attrs:{shadow:"no"}},[a("el-container",[a("el-aside",[a("header",[e._v(" 计划类别 "),a("el-button",{attrs:{icon:"el-icon-plus",disabled:e.treeEditing},on:{click:e.newType}},[e._v("新增类别")])],1),a("div",{staticClass:"tree-scroll"},[a("el-tree",{ref:"tree",attrs:{data:e.treedata,"node-key":"Id",props:e.defaultProps,"default-expand-all":!0,"expand-on-click-node":!1,"icon-class":"el-icon-arrow-right",draggable:!e.treeEditing},on:{"node-click":e.handleNodeClick,"node-drag-over":e.handleDragOver,"node-drop":e.handleDrop},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.node,o=t.data;return a("div",{staticClass:"custom-tree-node"},[a("svg-icon",{staticStyle:{height:"30px",width:"30px","margin-right":"4px"},attrs:{name:e.getNodeIcon(n),"icon-class":e.getNodeIcon(n)}}),a("div",{staticClass:"ctx"},[n.isCurrent&&e.treeEditing?[a("el-input",{attrs:{size:"mini",type:"text"},model:{value:o.Label,callback:function(t){e.$set(o,"Label",t)},expression:"data.Label"}})]:[a("span",[e._v(e._s(n.label))])],n.isCurrent&&"0"!=o.Id&&!e.treeEditing?a("div",{staticClass:"ops"},[a("i",{staticClass:"el-icon-delete",staticStyle:{color:"#fb6b7f"},on:{click:function(t){return t.stopPropagation(),e.deleteType(o)}}}),a("i",{staticClass:"el-icon-plus",staticStyle:{color:"#298dff"},on:{click:function(t){return t.stopPropagation(),e.newType(t)}}}),a("i",{staticClass:"el-icon-edit",staticStyle:{color:"#298dff"},on:{click:function(t){t.stopPropagation(),e.treeEditing=!0,e.nodeBack=o.Label}}})]):e._e(),n.isCurrent&&e.treeEditing?a("div",{staticClass:"ops"},[a("i",{staticClass:"el-icon-close",staticStyle:{color:"#fb6b7f"},on:{click:function(t){return t.stopPropagation(),e.cancelEditNode(o)}}}),a("i",{staticClass:"el-icon-check",staticStyle:{color:"#298dff"},on:{click:function(t){return t.stopPropagation(),e.saveType(o)}}})]):e._e()],2)],1)}}])})],1)]),a("el-main",[a("div",{staticClass:"main-head"},[a("el-select",{staticStyle:{"margin-right":"12px"},attrs:{placeholder:"选择权限"},on:{change:function(t){e.query.Page=1,e.loadPlanList()}},model:{value:e.query.Plan_Auth,callback:function(t){e.$set(e.query,"Plan_Auth",t)},expression:"query.Plan_Auth"}},[a("el-option",{attrs:{label:"全部权限",value:""}}),a("el-option",{attrs:{label:"负责",value:"负责"}}),a("el-option",{attrs:{label:"参与",value:"参与"}}),a("el-option",{attrs:{label:"查看",value:"查看"}})],1),a("el-select",{attrs:{placeholder:"选择状态"},on:{change:function(t){e.query.Page=1,e.loadPlanList()}},model:{value:e.query.Status,callback:function(t){e.$set(e.query,"Status",t)},expression:"query.Status"}},[a("el-option",{attrs:{label:"全部状态",value:""}}),a("el-option",{attrs:{label:"待发布",value:"0"}}),a("el-option",{attrs:{label:"审核中",value:"1"}}),a("el-option",{attrs:{label:"已发布",value:"2"}})],1),a("div",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.addPlan}},[e._v("新建")])],1)],1),a("div",{staticClass:"main-table"},[a("DynamicDataTable",{attrs:{data:e.data,config:e.tbConfig,columns:e.columns,border:""},scopedSlots:e._u([{key:"Code",fn:function(t){var n=t.column,o=t.row;return[a("el-tooltip",{attrs:{disabled:""===o.Reference_Plan_Name,placement:"top",effect:"light"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(e._s(o.Reference_Plan_Name))]),a("div",{staticClass:"lead"},[a("div",{staticClass:"flag"},[o["Is_Main_Plan"]?a("span",{staticClass:"square-icon cyan"},[a("i",{staticClass:"iconfont icon-home3-filled"})]):e._e(),o["Is_Target_Plan"]?a("span",{staticClass:"square-icon orange"},[a("i",{staticClass:"iconfont icon-star-filled"})]):e._e()]),a("div",[e._v(e._s(o[n.Code]))])])])]}},{key:"Plan_Auth",fn:function(t){var n=t.column,o=t.row;return[a("span",{class:["square-icon","none",e.authIcon(o[n.Code])[1]]},[a("i",{class:["iconfont",e.authIcon(o[n.Code])[0]]})]),e._v(" "+e._s(o[n.Code])+" ")]}},{key:"Percent_Complete",fn:function(t){var a=t.column,n=t.row;return[e._v(" "+e._s((100*n[a.Code]).toFixed(2)+"%")+" ")]}},{key:"Status",fn:function(t){var n=t.column,o=t.row;return["1"===o[n.Code]||"2"===o[n.Code]||"3"===o[n.Code]?a("el-popover",{attrs:{placement:"right",width:"260",trigger:"click"},on:{show:function(t){return e.showMess(o.Id)}}},[a("el-table",{attrs:{data:e.approveList,"show-header":!1,"show-overflow-tooltip":""}},[a("el-table-column",{attrs:{width:"85",property:"Create_UserName",label:"姓名"}}),a("el-table-column",{attrs:{width:"145",property:"Verification_Opinion",label:"状态"}})],1),a("span",{attrs:{slot:"reference"},slot:"reference"},[a("span",{staticClass:"state-dot",style:{background:e.getStatusColor(o[n.Code])}}),e._v(e._s(e.getStatusName(o[n.Code])))])],1):e._e(),"0"===o.Status?a("span",[a("span",{staticClass:"state-dot",style:{background:e.getStatusColor(o[n.Code])}}),e._v(" "+e._s(e.getStatusName(o[n.Code]))+" ")]):e._e()]}},{key:"op",fn:function(t){var n=t.row;return[a("el-dropdown",{on:{command:function(t){return e.handleCommand(n,t)}}},[a("span",{staticClass:"el-dropdown-link"},[a("i",{staticClass:"el-icon-s-operation"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e._l(e.authedCommands(n),(function(t){return[a("el-dropdown-item",{key:t.value,attrs:{command:t.value}},[e._v(e._s(t.label))])]}))],2)],1)]}}])})],1)])],1)],1),a("el-dialog",{attrs:{title:e.dialogCfgs.title,visible:e.dialogShow,width:e.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(t){e.dialogShow=t}}},[a("keep-alive",[e.dialogShow?a(e.dialogCfgs.component,e._b({tag:"component",attrs:{name:e.dialogCfgs.title},on:{dialogCancel:e.dialogCancel,dialogFormSubmitSuccess:e.dialogFormSubmitSuccess}},"component",e.dialogCfgs.props,!1)):e._e()],1)],1)],1)},o=[]},"2dd9":function(e,t,a){"use strict";function n(e,t){e||(e={}),t||(t=[]);var a=[],n=function(n){var o;o="[object Array]"===Object.prototype.toString.call(e[n])?e[n]:[e[n]];var l=o.map((function(e){var t=Object.prototype.toString.call(e);return["[object Boolean]","[object Number]"].indexOf(t)>-1||e?e:null}));if(l.filter((function(e){return null!==e})).length<=0&&(l=null),l){var r={Key:n,Value:l,Type:"",Filter_Type:""},s=t.find((function(e){return e.Code===n}));r.Type=null===s||void 0===s?void 0:s.Type,r.Filter_Type=null===s||void 0===s?void 0:s.Filter_Type,a.push(r)}};for(var o in e)n(o);return a}Object.defineProperty(t,"__esModule",{value:!0}),t.setParameterJson=n,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("25f0")},"30e0":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("d3b7"),a("25f0"),a("3ca3"),a("ddb0"),a("a9b5");var o=n(a("0f97")),l=n(a("1d04")),r=a("2dd9"),s=a("eb4a"),i=a("472f"),u=a("3543"),d=a("affc"),c=n(a("2082"));t.default={name:"PlanList",components:{DynamicDataTable:o.default,PlanAddDialog:l.default},mixins:[c.default],data:function(){return{addPageArray:[{path:"approve-plan",component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-de86d6a0"),a.e("chunk-54fa4a98"),a.e("chunk-3a3c0f1b"),a.e("chunk-635d6dc8")]).then(a.bind(null,"18d8"))},name:"FlowPendingFlowVerify",hidden:!0,meta:{title:"审核"}}],treedata:[],approveList:[],members:[],defaultProps:{children:"Children",label:"Label"},data:[],columns:[{Is_Display:!0,Display_Name:"计划编号",Code:"Code"},{Is_Display:!0,Display_Name:"计划权限",Code:"Plan_Auth"},{Is_Display:!0,Display_Name:"计划名称",Code:"Name"},{Is_Display:!0,Display_Name:"开始日期",Code:"Dynamic_Start_Date"},{Is_Display:!0,Display_Name:"完成日期",Code:"Dynamic_End_Date"},{Is_Display:!0,Display_Name:"工期",Code:"Dynamic_Duration"},{Is_Display:!0,Display_Name:"完成度(%)",Code:"Percent_Complete"},{Is_Display:!0,Display_Name:"创建人",Code:"Create_UserName"},{Is_Display:!0,Display_Name:"状态",Code:"Status",Align:"center"}],tbConfig:{Is_Page:!1,Height:0},query:{Search:"",Page:1,PageSize:200,SortName:"",SortOrder:"",TotalCount:0,Plan_Type_Id:"",Status:"",Plan_Auth:""},filter:{permission:"",status:""},dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},treeEditing:!1,nodeBack:"",tempNodeIdCount:0,dragBackInfo:{Id:"",Parent_Id:"",Sort_Number:""}}},created:function(){var e=this;(0,i.GetUserListByObjId)(this.$store.getters.Last_Working_Object_Id,{Page:1,PageSize:1e3}).then((function(t){t.IsSucceed&&(e.members=t.Data.Data)})),Promise.all([this.LoadTree()]).then((function(){e.loadPlanList()}))},watch:{$route:function(){this.loadPlanList()}},methods:{showMess:function(e){var t=this;(0,s.QueryHistories)({FlowInstanceId:e||"",SortOrder:"ASC"}).then((function(e){e.IsSucceed&&(t.approveList=e.Data)}))},loadPlanList:function(){var e=this,t=Object.assign({},this.query,{ParameterJson:(0,r.setParameterJson)(this.filter,this.columns)});(0,i.GetPlanList)(t).then((function(t){t.IsSucceed&&e.setGridData(t)}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{Pager_Align:"center",Op_Width:100})},setCols:function(e){this.columns=e},setGridData:function(e){this.query.TotalCount=e.TotalCount,this.data=e.Data},handleNodeClick:function(e){var t=e.Id;this.query.Plan_Type_Id=t,this.loadPlanList()},getNodeIcon:function(e){return e.expanded?"icon-folder-open":"icon-folder"},handleCommand:function(e,t){var a;switch(String(t)){case"0":a="view";break;case"1":a="edit";break;case"2":this.copyPlan(e);break;case"3":this.deletePlan(e);break;case"4":this.ApprovePlan(e);break;case"5":this.changePlanStatus(e,0);break}a&&this.$router.push({path:"/plan/".concat(a,"/").concat(e.Id)})},addPlan:function(){this.openDialog({title:"新建计划",width:"50%",component:"PlanAddDialog",props:{typetree:this.treedata,members:this.members,type:this.query.Plan_Type_Id||"0"}})},deletePlan:function(e){var t=this;this.$confirm("确认要删除该计划吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,i.DeletePlan)(e.Id).then((function(e){e.IsSucceed?t.loadPlanList():t.$message({type:"warning",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},copyPlan:function(e){var t=this;this.$confirm("确认要复制当前计划吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,i.CopyPlan)(e.Id).then((function(e){e.IsSucceed&&t.loadPlanList()}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},changePlanStatus:function(e,t){var a=this;this.$confirm("请确认是否要撤销当前已发布的进度计划?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,i.ChangePlanStatus)(e.Id,t).then((function(a){a.IsSucceed&&(e.Status=String(t))}))})).catch((function(){a.$message({type:"info",message:"已取消"})}))},ApprovePlan:function(e){this.$router.push({name:"FlowPendingFlowVerify",query:{id:e.Id,pg_redirect:this.$route.name}})},getStatusColor:function(e){var t="";switch(e){case"2":t="#3ECC93";break;case"0":t="#298DFF";break;case"1":t="#F5C15A";break;case"3":t="#298DFF";break}return t},getStatusName:function(e){var t="";switch(e){case"0":t="待发布";break;case"1":t="审核中";break;case"2":t="已发布";break;case"3":t="已驳回";break}return t},openDialog:function(e){e&&"[object Object]"===Object.prototype.toString.call(e)||(e={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,e,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(e){var t=e.type,a=e.data;switch(this.dialogCancel(),t){case"setPlanBase":this.newPlanPage(a);break}},newPlanPage:function(e){this.$router.push({name:"PlanAdd",query:Object.assign({},{pg_redirect:this.$route.name}),params:{plan:e}})},handleDrop:function(e,t,a,n){var o=this;(0,d.ChangeSort)(e.data.Id,"before"==a?"up":"after"==a?"down":"inner"==a?"child":"",t.data.Id).then((function(e){e.IsSucceed?o.$message.success(e.Message):o.$message.warning(e.Message),o.LoadTree(),o.dragBackInfo={}}))},handleDragOver:function(e,t,a){e!==t?(this.dragBackInfo.Id=e.data.Id,this.dragBackInfo.Parent_Id=e.data.ParentNodes,this.dragBackInfo.Sort_Number=e.data.Data.Sort_Number):(this.dragBackInfo.Id="",this.dragBackInfo.Parent_Id="",this.dragBackInfo.Sort_Number="")},saveType:function(e){var t=this;(0,d.PlanTypeSave)({Id:e.Id.indexOf("temp_")>-1?"":e.Id,Name:e.Label,Parent_Id:e.ParentNodes}).then((function(a){a.IsSucceed?(t.treeEditing=!1,t.nodeBack="",t.LoadTree()):(t.treeEditing=!1,e.Label=t.nodeBack,t.nodeBack="",t.$message.warning(a.Message))}))},LoadTree:function(){var e=this;return(0,d.GetPlanTypeTree)(this.$store.state.user.Last_Working_Object_Id).then((function(t){t.IsSucceed&&(e.treedata=t.Data)}))},newType:function(){var e,t,a,n,o={Label:"新增类别",ParentNodes:null!==(e=null===(t=this.$refs.tree.getCurrentNode())||void 0===t?void 0:t.Id)&&void 0!==e?e:"",Id:"temp_"+(this.tempNodeIdCount+1)};this.tempNodeIdCount++,this.$refs.tree.append(o,null!==(a=null===(n=this.$refs.tree.getCurrentNode())||void 0===n?void 0:n.Id)&&void 0!==a?a:"");this.$refs.tree.getNode(o);this.treeEditing=!0,this.nodeBack=o.Label,this.$refs.tree.setCurrentKey(o.Id)},cancelEditNode:function(e){this.treeEditing=!1,e.Label=this.nodeBack,this.nodeBack="",e.Id.indexOf("temp_")>-1&&this.$refs.tree.remove(e)},deleteType:function(e){var t=this;this.$confirm("确认要删除该分类吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.PlanTypeDelete)(e.Id).then((function(a){var n;a.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.$refs.tree.remove(e)):t.$message({type:"warning",message:null!==(n=a.Message)&&void 0!==n?n:""})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},getAuth:function(e){return new u.PlanAuth(e)},authedCommands:function(e){var t=[{label:"查看",value:0},{label:"编辑",value:1},{label:"复制",value:2},{label:"删除",value:3},{label:"审核",value:4},{label:"撤销",value:5}];e.Is_Target_Plan&&(t=t.filter((function(e){return 0===e.value}))),e.CanApprove||(t=t.filter((function(e){return 4!==e.value}))),"0"!==e.Status.toString()&&"3"!==e.Status.toString()||(t=t.filter((function(e){return 5!==e.value&&4!==e.value}))),"1"===e.Status.toString()&&(t=t.filter((function(e){return[5,2,3,1].indexOf(e.value)<0}))),"2"===e.Status.toString()&&(t=t.filter((function(e){return[4,3].indexOf(e.value)<0})));var a=[],n=this.getAuth(e.Plan_Auth);return 1===n.getValue()?(a=t.filter((function(e){return 0===e.value||4===e.value})),a):63===n.getValue()?(a=t.concat([]),a):(a=t.filter((function(e){return[0,1].indexOf(e.value)>-1})),a)},authIcon:function(e){switch(e){case"负责":return["icon-auction-filled","orange"];case"参与":return["icon-users-filled","green"];case"查看":return["icon-eye-filled","cyan"]}return["",""]}}}},"472f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CONSTRAINT_TYPES=void 0,t.ChangePlanStatus=h,t.CheckNeedApprovePlan=C,t.CopyPlan=p,t.DeletePlan=f,t.GetExtention=y,t.GetPlanEntity=u,t.GetPlanEntityReadOnly=_,t.GetPlanFieldSetting=d,t.GetPlanList=l,t.GetPlanResouseReport=b,t.GetPlanUpdateData=S,t.GetPlanUpdateList=g,t.GetTaskInputList=m,t.GetTaskProgressList=i,t.GetUserListByObjId=r,t.LINK_TYPES=void 0,t.SavePlan=s,t.SavePlanFieldSetting=c,t.SaveTaskInput=P,t.SendTaskInputMessage=v;var o=n(a("b775"));t.CONSTRAINT_TYPES=[{value:"asap",label:"越早越好"},{value:"alap",label:"越晚越好"},{value:"mso",label:"必须开始于..."},{value:"mfo",label:"必须完成于..."},{value:"snet",label:"不得早于...开始"},{value:"snlt",label:"不得晚于...开始"},{value:"fnet",label:"不得早于...完成"},{value:"fnlt",label:"不得晚于...完成"}],t.LINK_TYPES=[{label:"FS",value:0},{label:"SS",value:1},{label:"FF",value:2},{label:"SF",value:3}];function l(e){return(0,o.default)({url:"/Plan/Plan/GetPlanList",method:"post",data:e})}function r(e,t){return(0,o.default)({url:"/EPC/Project/GetUserListByObjId?workObjId="+e,method:"post",data:t})}function s(e,t){return(0,o.default)({url:"/Plan/Plan/SavePlan",method:"post",params:e,data:t})}function i(e){return(0,o.default)({url:"/Plan/Task/GetTaskProgressList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/Plan/Plan/GetPlanEntity",method:"post",params:{plan_id:e}})}function d(e,t){return(0,o.default)({url:"/Plan/Plan/GetPlanFieldSetting",method:"post",params:{plan_id:e,is_default:t}})}function c(e){return(0,o.default)({url:"/Plan/Plan/SavePlanFieldSetting",method:"post",data:e})}function f(e){return(0,o.default)({url:"/Plan/Plan/DeletePlan",method:"post",params:{plan_id:e}})}function p(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return(0,o.default)({url:"/Plan/Plan/CopyPlan",method:"post",params:{plan_id:e,newPlanName:t,newPlanCode:a}})}function h(e,t){return(0,o.default)({url:"/Plan/Plan/ChangePlanStatus",method:"post",params:{plan_id:e,status:t}})}function m(){return(0,o.default)({url:"/Plan/Task/GetTaskInputList",method:"post",data:{}})}function P(e){return(0,o.default)({url:"/Plan/Task/SaveTaskInput",method:"post",data:e})}function g(e,t,a){return(0,o.default)({url:"/Plan/Plan/GetPlanUpdateList",method:"post",params:{plan_id:e,begin_date:t,end_date:a}})}function S(e){return(0,o.default)({url:"/Plan/Plan/GetPlanUpdateData",method:"post",params:{history_id:e}})}function v(e){return(0,o.default)({url:"/Plan/Plan/SendTaskInputMessage",method:"post",data:e})}function y(){return(0,o.default)({url:"/Plan/Plan/GetExtention",method:"post"})}function b(e,t){return(0,o.default)({url:"/Plan/Plan/GetPlanResouseReport",method:"post",params:{plan_id:e,type:t}})}function C(){return(0,o.default)({url:"/Plan/Plan/CheckNeedApprovePlan",method:"post"})}function _(e,t){return(0,o.default)({url:"/Plan/Plan/GetPlanEntityReadOnly",method:"post",params:{plan_id:e,access_token:t}})}},8062:function(e,t,a){"use strict";a.r(t);var n=a("1403"),o=a("9a78");for(var l in o)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(l);var r=a("2877"),s=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"9a78":function(e,t,a){"use strict";a.r(t);var n=a("30e0"),o=a.n(n);for(var l in n)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(l);t["default"]=o.a},a9b5:function(e,t,a){},affc:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ChangeSort=i,t.GetPlanTypeTree=l,t.PlanTypeDelete=s,t.PlanTypeSave=r;var o=n(a("b775"));function l(){return(0,o.default)({url:"/Plan/PlanType/GetPlanTypeTree",method:"post",params:{}})}function r(e){return(0,o.default)({url:"/Plan/PlanType/Save",method:"post",data:e})}function s(e){return(0,o.default)({url:"/Plan/PlanType/Delete",method:"post",params:{id:e}})}function i(e,t,a){return(0,o.default)({url:"/Plan/PlanType/ChangeSort",method:"post",params:{id:e,moveType:t,referenceId:a}})}},eb4a:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddContractPriceProcess=_,t.AddConventionalContractProcess=b,t.AddOtherSignedContractProcess=C,t.AddSealProcess=w,t.AddSpecialPurchaseProcess=I,t.AddTechSolutionProcess=k,t.CheckBusinessVerification=y,t.CheckExistFlow=f,t.FlowInstancesCancel=d,t.FlowInstancesDelete=m,t.FlowInstancesGet=P,t.FlowInstancesLoad=p,t.FlowSchemesAdd=s,t.FlowSchemesDelete=u,t.FlowSchemesGet=r,t.FlowSchemesLoad=c,t.FlowSchemesUpdate=i,t.GetContractPriceEntity=E,t.GetConventionalContractEntity=D,t.GetFileInfo=x,t.GetFlowSchemeByFromId=F,t.GetFlowSchemeNodeByFromId=T,t.GetLeavePageList=v,t.GetListEntitiesProject=B,t.GetOneEntitiesProject=A,t.GetOtherSignedContractEntity=G,t.GetSchemeObjectIds=$,t.GetSealEntity=N,t.GetSpecialPurchaseEntity=L,t.QueryHistories=S,t.SaveBusinessData=h,t.SaveDesignFlow=O,t.Verification=g;var o=n(a("b775")),l=n(a("4328"));function r(e){return(0,o.default)({method:"get",url:"/SYS/FlowSchemes/Get",params:e})}function s(e){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Add",data:e})}function i(e){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Update",data:e})}function u(e){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Delete",data:e})}function d(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/CancelFlow",data:e})}function c(e){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Load",data:e})}function f(e){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/CheckExistFlow",data:e})}function p(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/Load",data:e})}function h(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/SaveBusinessData",data:e})}function m(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/Delete",data:e})}function P(e){return(0,o.default)({method:"get",url:"/SYS/FlowInstances/Get",params:e})}function g(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/Verification",data:e})}function S(e){return(0,o.default)({method:"get",url:"/SYS/FlowInstances/QueryHistories",params:e})}function v(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/GetLeavePageList",data:l.default.stringify(e)})}function y(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/CheckBusinessVerification",data:l.default.stringify(e)})}function b(e){return(0,o.default)({method:"post",url:"/EPC/ConventionalContract/AddProcess",data:e})}function C(e){return(0,o.default)({method:"post",url:"/EPC/OtherSignedContract/AddProcess",data:e})}function _(e){return(0,o.default)({method:"post",url:"/EPC/ContractPrice/AddProcess",data:e})}function I(e){return(0,o.default)({method:"post",url:"/EPC/SpecialPurchase/AddProcess",data:e})}function w(e){return(0,o.default)({method:"post",url:"/EPC/Seal/AddProcess",data:e})}function k(e){return(0,o.default)({method:"post",url:"/EPC/TechnicalScheme/AddProcess",data:e})}function T(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/GetFlowSchemeNodeByFromId",data:e})}function F(e){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/GetFlowSchemeByFromId",data:e})}function D(e){return(0,o.default)({method:"post",url:"/EPC/ConventionalContract​/GetEntity",data:e})}function G(e){return(0,o.default)({method:"post",url:"/EPC/OtherSignedContract/GetEntity",data:e})}function E(e){return(0,o.default)({method:"post",url:"/EPC/ContractPrice/GetEntity",data:e})}function L(e){return(0,o.default)({method:"post",url:"/EPC/SpecialPurchase/GetEntity",data:e})}function N(e){return(0,o.default)({method:"post",url:"/EPC/Seal/GetEntity",data:e})}function O(e){return(0,o.default)({method:"post",url:"/SYS/Sys_File/SaveDesignFlow",data:e})}function A(e){return(0,o.default)({method:"post",url:"/Sys/Sys_FileType/GetOneEntitiesProject",data:e})}function B(e){return(0,o.default)({method:"post",url:"/Sys/Sys_FileType/GetListEntitiesProject",data:e})}function x(e){return(0,o.default)({method:"post",url:"/Sys/Sys_FileType/GetEntity",data:e})}function $(e){return(0,o.default)({method:"post",url:"/Sys/FlowSchemes/GetSchemeObjectIds",data:e})}}}]);