(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7c89aa30"],{"00dd":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b0c0"),a("ac1f"),a("5319"),a("5b81"),a("498a");var o=n(a("c14f")),i=n(a("1da1")),r=a("2e8a"),l=n(a("83b4")),s=n(a("2ea6")),c=n(a("3571")),u=n(a("ec57")),d=a("4d7a"),f=a("2c08");t.default={name:"PROStockInquiry",components:{StockCheck:c.default,SemiFinishedCheck:u.default},mixins:[l.default,s.default],data:function(){return{warehouseType:"成品仓库",activeName:"成品库",loading:!1,drawer:!1,Is_Component_Data:[{Name:"是",Id:!1},{Name:"否",Id:!0}],form:{InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Sys_Project_Id:"",Area_Id:"",Warehouse_Id:"",Location_Id:"",Is_Component:"",SteelName:"",Model_Id:"",Pack_Code:"",Code:"",SearchCode:"",Type:"",Code_Like:"",Semi_Type:null},tbConfig:{},columns:[],currentType:"全部",data:[],comTypeOptions:[],projects:[],installOption:[],installName:"",searchHeight:0,WarehouseList:[{Name:"出库",Code:"CK"},{Name:"入库",Code:"RK"}],TypeData:[],SemiTypeData:[{Name:"构件半成品",Code:2},{Name:"零件半成品",Code:1}],ProfessionalId:""}},watch:{activeName:{handler:function(e,t){this.form={InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Sys_Project_Id:"",Area_Id:"",Warehouse_Id:"",Location_Id:"",Is_Component:"",SteelName:"",Model_Id:"",Pack_Code:"",Code:"",SearchCode:"",Type:"",Code_Like:"",Semi_Type:null}},deep:!0}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.getProfessional(),t.n=1,e.getProjectNan();case 1:e.getProjectOption(),e.$nextTick((function(t){e.$refs["stockCheckref"].getTypeList()}));case 2:return t.a(2)}}),t)})))()},methods:{getProjectNan:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.getQueryParam)(["Project_Id","Sys_Project_Id"]);case 1:a=t.v,e.form.Project_Id=a.Project_Id,e.form.Sys_Project_Id=a.Sys_Project_Id,e.projectChange(a.Project_Id);case 2:return t.a(2)}}),t)})))()},getProfessional:function(){var e=this;(0,d.getFactoryProfessional)().then((function(t){e.ProfessionalId=t[0].Id,e.getComponentTypeList()}))},getComponentTypeList:function(){var e=this;(0,r.GetComponentTypeList)({Level:1,Category_Id:this.ProfessionalId,Factory_Id:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed&&(e.TypeData=t.Data)}))},handleTap:function(e){var t=this;return(0,i.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return t.warehouseType="成品库"===e.name?"成品仓库":"半成品仓库",Object.assign(t.form,{InstallUnit_Id:"",SetupPosition:"",Area_Id:"",Warehouse_Id:"",Location_Id:"",Is_Component:"",SteelName:"",Model_Id:"",Pack_Code:"",Code:"",SearchCode:"",Type:"",Code_Like:"",Semi_Type:null}),t.getWarehouseList(),a.n=1,t.getProjectNan();case 1:"成品库"===t.activeName?t.$nextTick((function(e){t.$refs["stockCheckref"].getTypeList()})):t.$nextTick((function(e){t.$refs["semiFinishedCheckref"].getTypeList()}));case 2:return a.a(2)}}),a)})))()},handleSearch:function(e){this.form.Code=this.form.SearchCode.trim().replaceAll(" ","\n"),e&&"reset"===e&&(this.form.ProjectName="",this.form.Sys_Project_Id=""),"成品库"===this.activeName?this.$refs.stockCheckref.getTypeList():"半成品库"===this.activeName&&this.$refs.semiFinishedCheckref.getTypeList(),(0,f.addSearchLog)(this.form)}}}},"0583":function(e,t,a){},"10eb":function(e,t,a){"use strict";a.r(t);var n=a("1a65"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,r=e.Data,l=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});var s=[];t.tbConfig=Object.assign({},t.tbConfig,r.Grid),s=a?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=s.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+r.Grid.Row_Number||o.tablePageSize[0]),i(t.columns)}else t.$message({message:l,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1a65":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("dca8"),a("d3b7"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0");var o=n(a("5530")),i=n(a("c14f")),r=n(a("1da1")),l=a("6186"),s=n(a("333d")),c=a("fd31"),u=a("abf9"),d=a("8975"),f=a("ed08"),p=a("c685");t.default={components:{Pagination:s.default},props:{searchDetail:{type:Object,default:function(){}}},data:function(){return{tablePageSize:p.tablePageSize,Unit:"",TypeId:"",Proportion:0,typeOption:"",Date_Time:[],searchDate:{StartTime:"",EndTime:""},columns:[],tbData:[],dialog:!1,tbLoading:!0,total:0,queryInfo:{Page:1,PageSize:20},customPageSize:[10,20,50,100],pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},KC_Count:0,KC_Weith_Count:0,Select_KC_Count:0,Select_KC_Weith_Count:0,selectList:[]}},mounted:function(){this.getTypeList()},methods:{getTypeList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id,e.getTableConfig("PROSemiFinishedCheck"),e.fetchList())):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getTableConfig:function(e){var t=this;(0,l.GetGridByCode)({code:e+","+this.typeOption.find((function(e){return e.Id===t.TypeId})).Code}).then((function(e){var a=e.IsSucceed,n=e.Data,o=e.Message;if(a){if(!n)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbLoading=!1;var i=n.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),"Stock_Count"===e.Code&&(e.minWidth="200",e.Width="auto"),e}))}else t.$message({message:o,type:"error"})}))},changePage:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},fetchList:function(){var e=this;(0,u.GetListComponent)((0,o.default)((0,o.default)((0,o.default)((0,o.default)({},this.searchDetail),this.searchDate),this.queryInfo),{},{Query_Type:3})).then((function(t){e.tbData=t.Data.Data.map((function(e){return e.In_Date=(0,d.timeFormat)(e.In_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.total=t.Data.TotalCount}))},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.Select_KC_Count=0,this.Select_KC_Weith_Count=0;var a=0;this.selectList.length>0&&(this.selectList.forEach((function(e){t.Select_KC_Count+=e.Stock_Count,a+=Number(e.ToWeight)})),this.Select_KC_Weith_Count=Math.round(a/this.Proportion*1e3)/1e3)},changesearchDate:function(e){e?(this.searchDate.StartTime=e[0],this.searchDate.EndTime=e[1]):(this.searchDate.StartTime="",this.searchDate.EndTime=""),this.fetchList()},stockinFn:function(){var e=this,t=this.selectList.map((function(e){return{Import_Detail_Id:e.Import_Detail_Id,Location_Id:e.Location_Id,Model_Id:e.Model_Id,Pack_Code:e.Pack_Code}}));(0,u.InventorySummaryExport)({Selected:t}).then((function(t){t.IsSucceed?window.open((0,f.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)}))},handleInfo:function(e){this.dialog=!0,this.getList(e)},getList:function(e){var t=this;(0,u.GetStockComponentListByImportDetailId)({Import_Detail_Id:e.Import_Detail_Id,Location_Id:e.Location_Id,Pack_Code:e.Pack_Code,Model_Id:e.Model_Id}).then((function(e){e.IsSucceed?t.$refs["dialog"].fetchData((null===e||void 0===e?void 0:e.Data)||[]):t.$message({message:e.Message,type:"error"})}))}}}},"1c17":function(e,t,a){"use strict";a("0583")},"2e8a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=u,t.GetCompTypeTree=d,t.GetComponentTypeEntity=c,t.GetComponentTypeList=r,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=p,t.GetTypePageList=l,t.RestoreTemplateType=y,t.SavDeepenTemplateSetting=v,t.SaveCompTypeIdentifySetting=_,t.SaveComponentType=s,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=m;var o=n(a("b775")),i=n(a("4328"));function r(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:i.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:i.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:i.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function _(e){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function v(e){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function y(e){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},3571:function(e,t,a){"use strict";a.r(t);var n=a("6fa1"),o=a("9117");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("1c17");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"bd9827cc",null);t["default"]=l.exports},"3ff84":function(e,t,a){"use strict";a("407b")},"407b":function(e,t,a){},4136:function(e,t,a){"use strict";a.r(t);var n=a("00dd"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"4d7a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.FIX_COLUMN=void 0,t.getFactoryProfessional=l;var o=n(a("c14f")),i=n(a("1da1"));a("d3b7");var r=a("fd31");function l(){return new Promise((function(e,t){(0,r.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then(function(){var a=(0,i.default)((0,o.default)().m((function a(n){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:n.IsSucceed?e(n.Data):t("error");case 1:return a.a(2)}}),a)})));return function(e){return a.apply(this,arguments)}}()).catch((function(e){t("error")}))}))}t.FIX_COLUMN=["SteelName"]},"54e5":function(e,t,a){},"67de":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("dca8"),a("b64b"),a("d3b7"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0");var o=n(a("5530")),i=n(a("c14f")),r=n(a("1da1")),l=a("6186"),s=n(a("333d")),c=a("fd31"),u=a("abf9"),d=a("8975"),f=a("ed08"),p=a("c685"),m=n(a("89e5")),h=(a("2c08"),n(a("6612")));t.default={components:{Pagination:s.default,DialogInfo:m.default},props:{searchDetail:{type:Object,default:function(){}}},data:function(){return{dialogInnerColumn:[{title:"管理编号",field:"SerialNumber",Align:"left"},{title:"模型ID",field:"Model_Id",Align:"left"},{title:"入库时间",field:"In_Date",Align:"center",isTime:!0},{title:"所属包号",field:"Pack_Code",Align:"left"}],tablePageSize:p.tablePageSize,Unit:"",TypeId:"",Proportion:0,typeOption:"",Date_Time:[],searchDate:{StartTime:"",EndTime:""},columns:[],tbData:[],dialog:!1,tbLoading:!0,total:0,queryInfo:{Page:1,PageSize:20},pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},KC_Count:0,KC_Weith_Count:0,Select_KC_Count:0,Select_KC_Weith_Count:0,selectList:[]}},mounted:function(){},methods:{getTypeList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id,e.getTableConfig("PROStockCheck"),e.fetchList(),e.getComponentCount())):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getTableConfig:function(e){var t=this;(0,l.GetGridByCode)({code:e+","+this.typeOption.find((function(e){return e.Id===t.TypeId})).Code}).then((function(e){var a=e.IsSucceed,n=e.Data,o=e.Message;if(a){if(!n)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbLoading=!1;var i=n.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return"CheckName"===e.Code&&(e.fixed="left"),"LocationName"===e.Code&&(e.minWidth="200",e.Width="auto"),e}))}else t.$message({message:o,type:"error"})}))},changePage:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},fetchList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:(0,u.GetListComponent)((0,o.default)((0,o.default)((0,o.default)((0,o.default)({},e.searchDetail),e.searchDate),e.queryInfo),{},{Query_Type:2})).then((function(t){e.tbData=t.Data.Data.map((function(e){return e.In_Date=(0,d.timeFormat)(e.In_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.total=t.Data.TotalCount}));case 1:return t.a(2)}}),t)})))()},getComponentCount:function(){var e=this;(0,u.GetComponentCount)((0,o.default)((0,o.default)({},this.searchDetail),this.searchDate)).then((function(t){if(t.IsSucceed){var a;e.KC_Count=Math.round(1e3*t.Data.KC_Count)/1e3;var n=(null===(a=t.Data)||void 0===a?void 0:a.KC_Weith_Count)||0;e.KC_Weith_Count=(0,h.default)(n).divide(1e3).format("0.[00]")}else e.$message({message:t.Message,type:"error"})}))},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.Select_KC_Count=0,this.Select_KC_Weith_Count=0;var a=0;this.selectList.length>0&&(this.selectList.forEach((function(e){t.Select_KC_Count+=e.Stock_Count,a+=Number(e.ToWeight)})),this.Select_KC_Weith_Count=(0,h.default)(a).format("0.[00]"))},changesearchDate:function(e){e?(this.searchDate.StartTime=e[0],this.searchDate.EndTime=e[1]):(this.searchDate.StartTime="",this.searchDate.EndTime=""),this.fetchList(),this.getComponentCount()},stockinFn:function(){var e=this,t=this.selectList.map((function(e){return{Import_Detail_Id:e.Import_Detail_Id,Location_Id:e.Location_Id,Model_Id:e.Model_Id,Pack_Code:e.Pack_Code}}));(0,u.InventorySummaryExport)({Selected:t}).then((function(t){t.IsSucceed?window.open((0,f.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)}))},handleInfo:function(e){this.dialog=!0,this.getList(e)},getList:function(e){var t=this;(0,u.GetStockComponentListByImportDetailId)({Import_Detail_Id:e.Import_Detail_Id,Location_Id:e.Location_Id,Pack_Code:e.Pack_Code,Model_Id:e.Model_Id}).then((function(e){e.IsSucceed?t.$refs["dialog"].fetchData((null===e||void 0===e?void 0:e.Data)||[]):t.$message({message:e.Message,type:"error"})}))}}}},"6fa1":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("div",{staticClass:"assistant-wrapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{size:"small",type:"success",disabled:e.selectList.length<=0},on:{click:e.stockinFn}},[e._v("导出库存明细")]),a("div",{staticClass:"change-total-wrapper"},[a("span",[e._v("选中总数 "+e._s(e.Select_KC_Count)+" 件")]),a("span",[e._v("选中构件总量 "+e._s(e.Select_KC_Weith_Count)+" "+e._s(e.Unit))])])],1),a("div",{staticClass:"total-wrapper"},[a("span",[e._v("构件总数 "+e._s(e.KC_Count)+" 件")]),a("span",[e._v("构件总量 "+e._s(e.KC_Weith_Count)+" "+e._s(e.Unit))])]),a("div",{staticClass:"date-picker-wrapper"},[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd"},on:{change:e.changesearchDate},model:{value:e.Date_Time,callback:function(t){e.Date_Time=t},expression:"Date_Time"}})],1)]),a("div",{staticClass:"table-wrapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",data:e.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.minWidth,width:t.Width},scopedSlots:e._u(["Stock_Count"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleInfo(n)}}},[e._v(" "+e._s(n.Stock_Count?n.Stock_Count:"-")+" ")])]}}:"Is_Component"===t.Code?{key:"default",fn:function(t){var n=t.row;return[n.Is_Component?a("el-tag",{attrs:{type:"danger"}},[e._v("否")]):a("el-tag",{attrs:{type:"success"}},[e._v("是")])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(e._s(o[t.Code]||"-"))])]}}],null,!0)})}))],2)],1),a("div",{staticStyle:{"margin-top":"10px"}},[a("div",{staticStyle:{display:"inline-block"}},[a("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[e._v("已选"+e._s(e.selectList.length)+"条数据")])],1),a("Pagination",{staticClass:"cs-table-pagination",staticStyle:{display:"inline-block",float:"right"},attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}}),a("dialog-info",{ref:"dialog",attrs:{title:"库存数量",columns:e.dialogInnerColumn,visible:e.dialog},on:{"update:visible":function(t){e.dialog=t}}})],1)])},o=[]},"89e5":function(e,t,a){"use strict";a.r(t);var n=a("8e11"),o=a("b520");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("968d");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"96fc6d92",null);t["default"]=l.exports},"8e11":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog cs-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tableData,resizable:"","tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t){return a("vxe-column",{key:t.field,attrs:{title:t.title,sortable:"","min-width":t.minWidth,align:t.Align},scopedSlots:e._u([t.isTime?{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(o[t.field]&&e.formatTime(o[t.field])||"-"))])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(o[t.field]||"-"))])]}}],null,!0)})})),1),a("Pagination",{attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}}),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)],1)},o=[]},9117:function(e,t,a){"use strict";a.r(t);var n=a("67de"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"91c0":function(e,t,a){},"968d":function(e,t,a){"use strict";a("c8a7")},a14b:function(e,t,a){"use strict";a("91c0")},a7c2:function(e,t,a){"use strict";a("54e5")},b520:function(e,t,a){"use strict";a.r(t);var n=a("be26"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},bb52:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("div",{staticClass:"table-wrapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",data:e.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.minWidth,width:t.Width},scopedSlots:e._u(["Type"===t.Code?{key:"default",fn:function(t){var n=t.row;return[1===n.Type?a("div",[e._v("零件半成品")]):2===n.Type?a("div",[e._v("构件半成品")]):a("div",[e._v("-")])]}}:"Source"===t.Code?{key:"default",fn:function(t){var n=t.row;return[1===n.Source?a("div",[e._v("零件退返")]):2===n.Source?a("div",[e._v("构件退返")]):3===n.Source?a("div",[e._v("现场退货")]):a("div",[e._v("-")])]}}:"Is_Component"===t.Code?{key:"default",fn:function(t){var n=t.row;return[n.Is_Component?a("el-tag",{attrs:{type:"danger"}},[e._v("否")]):a("el-tag",{attrs:{type:"success"}},[e._v("是")])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(e._s(o[t.Code]||"-"))])]}}],null,!0)})}))],2)],1),a("div",{staticStyle:{"margin-top":"10px"}},[a("div",{staticStyle:{display:"inline-block"}},[a("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[e._v("已选"+e._s(e.selectList.length)+"条数据")])],1),a("Pagination",{staticClass:"cs-table-pagination",staticStyle:{display:"inline-block",float:"right"},attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)])},o=[]},be26:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("fb6a");var o=n(a("15ac")),i=(n(a("c695")),a("8975")),r=n(a("333d")),l=a("c685");t.default={components:{Pagination:r.default},mixins:[o.default],props:{title:{type:String,default:""},visible:{type:Boolean,default:!1},columns:{type:Array,default:function(){return[]}}},data:function(){return{tablePageSize:l.tablePageSize,tbLoading:!1,tableData:[],queryInfo:{Page:1,PageSize:l.tablePageSize[0]},total:0}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},methods:{handleOpen:function(){this.dialogVisible=!0},fetchData:function(e){this.list=e,this.total=e.length,this.pageChange({page:1,limit:l.tablePageSize[0]})},handleClose:function(){},formatTime:function(e){return(0,i.timeFormat)(e,"{y}-{m}-{d} {h}:{i}:{s}")},pageChange:function(e){var t=e.page,a=e.limit;this.tableData=1===t?this.list.slice(0,a):this.list.slice((t-1)*a,t*a)}}}},c8a7:function(e,t,a){},e840:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{ref:"searchDom",staticClass:"header_wrapper"},[a("el-tabs",{staticClass:"tab_header search-wrapper",on:{"tab-click":e.handleTap},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"成品库",name:"成品库"}}),a("el-tab-pane",{attrs:{label:"半成品库",name:"半成品库"}})],1),a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"90px"}},[a("el-row",["成品库"==e.activeName?[a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"构件编号",prop:"SearchCode"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入（空格间隔筛选多个）"},model:{value:e.form.SearchCode,callback:function(t){e.$set(e.form,"SearchCode",t)},expression:"form.SearchCode"}})],1)],1),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"模型ID",prop:"Model_Id"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:e.form.Model_Id,callback:function(t){e.$set(e.form,"Model_Id",t)},expression:"form.Model_Id"}})],1)],1),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"构件类型",prop:"Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},e._l(e.TypeData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Code}})})),1)],1)],1)]:e._e(),"半成品库"==e.activeName?[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"半成品名称",prop:"Code_Like"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:e.form.Code_Like,callback:function(t){e.$set(e.form,"Code_Like",t)},expression:"form.Code_Like"}})],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"半成品类型",prop:"Semi_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Semi_Type,callback:function(t){e.$set(e.form,"Semi_Type",t)},expression:"form.Semi_Type"}},e._l(e.SemiTypeData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Code}})})),1)],1)],1)]:e._e(),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"成品库"==e.activeName?"项目名称":"来源项目",prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"成品库"==e.activeName?"区域":"来源区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1),"成品库"==e.activeName?[a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",attrs:{disabled:!e.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:e.setupPositionChange},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"直发件",prop:"Is_Component"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Component,callback:function(t){e.$set(e.form,"Is_Component",t)},expression:"form.Is_Component"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1)]:e._e(),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"仓库名称",prop:"Warehouse_Id"}},[a("el-select",{ref:"WarehouseRef",attrs:{clearable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"库位名称",prop:"Location_Id"}},[a("el-select",{ref:"LocationRef",attrs:{clearable:"",placeholder:"请选择库位",disabled:!e.form.Warehouse_Id},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),"成品库"==e.activeName?[a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"包编号",prop:"Pack_Code"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:e.form.Pack_Code,callback:function(t){e.$set(e.form,"Pack_Code",t)},expression:"form.Pack_Code"}})],1)],1)]:e._e(),a("el-col",{attrs:{span:5,lg:4,xl:4}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch("reset")}}},[e._v("重置")])],1)],1)],2)],1)],1)],1),a("div",{staticClass:"main-wrapper"},["成品库"==e.activeName?a("StockCheck",{ref:"stockCheckref",attrs:{"search-detail":e.form}}):a("SemiFinishedCheck",{ref:"semiFinishedCheckref",attrs:{"search-detail":e.form}})],1)])},o=[]},ec57:function(e,t,a){"use strict";a.r(t);var n=a("bb52"),o=a("10eb");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("a7c2");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"cd7b4300",null);t["default"]=l.exports},f6c8:function(e,t,a){"use strict";a.r(t);var n=a("e840"),o=a("4136");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("3ff84"),a("a14b");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"63437edd",null);t["default"]=l.exports}}]);