(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-67617cd8"],{"035d":function(t,e,a){},"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,a){var i=o(),u=t-i,l=20,s=0;e="undefined"===typeof e?500:e;var c=function(){s+=l;var t=Math.easeInOutQuad(s,i,u,e);r(t),s<e?n(c):a&&"function"===typeof a&&a()};c()}},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,n.GetGridByCode)({code:t,IsAll:a}).then((function(t){var n=t.IsSucceed,i=t.Data,u=t.Message;if(n){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||r.tablePageSize[0]),o(e.columns)}else e.$message({message:u,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,n=t.type;this.queryInfo.Page="limit"===n?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var n=0;n<this.columns.length;n++){var r=this.columns[n];if(r.Code===e){a.Type=r.Type,a.Filter_Type=r.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},3166:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=c,e.GeAreaTrees=S,e.GetFileSync=j,e.GetInstallUnitIdNameList=v,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=O,e.GetProjectAreaTreeList=_,e.GetProjectEntity=l,e.GetProjectList=u,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=b,e.GetSchedulingPartList=w,e.IsEnableProjectMonomer=d,e.SaveProject=s,e.UpdateProjectTemplateBase=P,e.UpdateProjectTemplateContacts=g,e.UpdateProjectTemplateContract=y,e.UpdateProjectTemplateOther=R;var r=n(a("b775")),o=n(a("4328"));function i(t){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(t)})}function s(t){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(t)})}function d(t){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function O(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function w(t){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function j(t){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"3c4a":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxOutExport=q,e.AuxReturnByReceipt=ct,e.EditAuxOutStatus=W,e.EditOutStatus=I,e.ExportFlow=P,e.ExportProjectRawAnalyse=mt,e.ExportRawForProject=Pt,e.FindAuxFlowList=it,e.FindAuxPageList=G,e.FindPageList=k,e.FindProjectRawAnalyse=Z,e.FindRawFlowList=rt,e.FindRawPageList=H,e.FindReturnStoreNewPageList=R,e.FindReturnStoreNewSum=b,e.FindStoreDetail=E,e.GetAuxBatchInventoryDetailList=ft,e.GetAuxDetailByReceipt=st,e.GetAuxInventoryDetailList=c,e.GetAuxInventoryPageList=s,e.GetAuxSummary=N,e.GetAuxWarningPageList=d,e.GetCurUserCompanyId=M,e.GetFirstLevelDepartsUnderFactory=C,e.GetInPageList=f,e.GetInPageListSum=h,e.GetMaterielRawOutStoreList=y,e.GetOutFromSourceData=j,e.GetOutPageList=m,e.GetOutSum=p,e.GetPickOutDetail=z,e.GetProjectRawAnalyseByProject=ot,e.GetProjectRawAnalyseDetail=tt,e.GetProjectRawAnalyseSum=et,e.GetRawBatchInventoryDetailList=dt,e.GetRawDetailByReceipt=lt,e.GetRawFilterDataSummary=yt,e.GetRawForProjectDetail=gt,e.GetRawForProjectPageList=ht,e.GetRawInventoryDetailList=i,e.GetRawInventoryPageList=o,e.GetRawSummary=l,e.GetRawWHSummaryList=pt,e.GetRawWarningPageList=u,e.GetTeamListByUserForMateriel=Rt,e.GetUserPage=x,e.List=g,e.ListDetail=w,e.OutSourcingOutStore=v,e.OutSourcingOutStoreDetail=O,e.PartyAAuxOutStore=L,e.PartyAAuxOutStoreDetail=F,e.PartyAOutStore=Q,e.PartyAOutStoreDetail=Y,e.PickOutStore=B,e.PickUpOutStore=_,e.PickUpOutStoreDetail=S,e.RawOutExport=D,e.RawReturnByReceipt=ut,e.SelfAuxReturnOutStore=T,e.SelfAuxReturnOutStoreDetail=A,e.SelfReturnOutStore=K,e.SelfReturnOutStoreDetail=X,e.SetAuxLT=nt,e.SetAuxLock=V,e.SetAuxUnlock=J,e.SetRawLT=at,e.SetRawLock=U,e.SetRawUnlock=$,e.TransferRawLock=bt;var r=n(a("b775"));function o(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawInventoryPageList",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawInventoryDetailList",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawWarningPageList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawSummary",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxInventoryPageList",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxInventoryDetailList",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxWarningPageList",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/MaterielFlow/GetInPageList",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/MaterielFlow/GetOutPageList",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/MaterielFlow/GetOutSum",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/MaterielFlow/GetInPageListSum",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/MaterielFlow/ExportFlow",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/List",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/List",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/FindReturnStoreNewPageList",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/MaterielReturnStore/FindReturnStoreNewSum",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/PickUpOutStore",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/OutSourcingOutStore",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/PickUpOutStoreDetail",method:"post",data:t})}function O(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/OutSourcingOutStoreDetail",method:"post",data:t})}function w(t){return(0,r.default)({url:"/PRO/MaterielRawInStore/ListDetail",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/MaterielRawOutStoreNew/GetOutFromSourceData",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/EditOutStatus",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PRO/MaterielRawOutStore/Export",method:"post",data:t})}function C(t){return(0,r.default)({url:"/OMA/Common/GetFirstLevelDepartsUnderFactory",method:"post",data:t})}function x(t){return(0,r.default)({url:"/SYS/User/GetUserPage",method:"post",data:t})}function M(t){return(0,r.default)({url:"/PRO/Communal/GetCurUserCompanyId",method:"post",data:t})}function G(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/FindAuxPageList",method:"post",data:t})}function T(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfAuxReturnOutStore",method:"post",data:t})}function L(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAAuxOutStore",method:"post",data:t})}function A(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfAuxReturnOutStoreDetail",method:"post",data:t})}function F(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAAuxOutStoreDetail",method:"post",data:t})}function k(t){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/FindPageList",method:"post",data:t})}function N(t){return(0,r.default)({url:"PRO/MaterielInventory/GetAuxSummary",method:"post",data:t})}function B(t){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/PickOutStore",method:"post",data:t})}function z(t){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/GetPickOutDetail",method:"post",data:t})}function W(t){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/EditOutStatus",method:"post",data:t})}function q(t){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/Export",method:"post",data:t})}function E(t){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/GetOutFromSourceData ",method:"post",data:t})}function U(t){return(0,r.default)({url:"/PRO/MaterielAssign/SetRawLock",method:"post",data:t})}function $(t){return(0,r.default)({url:"/PRO/MaterielAssign/SetRawUnlock",method:"post",data:t})}function V(t){return(0,r.default)({url:"/PRO/MaterielAssign/SetAuxLock",method:"post",data:t})}function J(t){return(0,r.default)({url:"/PRO/MaterielAssign/SetAuxUnlock",method:"post",data:t})}function H(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/FindRawPageList",method:"post",data:t})}function K(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfReturnOutStore",method:"post",data:t})}function Q(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAOutStore",method:"post",data:t})}function Y(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAOutStoreDetail",method:"post",data:t})}function X(t){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfReturnOutStoreDetail",method:"post",data:t})}function Z(t){return(0,r.default)({url:"/PRO/MaterielReport/FindProjectRawAnalyse",method:"post",data:t})}function tt(t){return(0,r.default)({url:"/PRO/MaterielReport/GetProjectRawAnalyseDetail",method:"post",data:t})}function et(t){return(0,r.default)({url:"/PRO/MaterielReport/GetProjectRawAnalyseSum",method:"post",data:t})}function at(t){return(0,r.default)({url:"/PRO/MaterielInventory/SetRawLT",method:"post",data:t})}function nt(t){return(0,r.default)({url:"/PRO/MaterielInventory/SetAuxLT",method:"post",data:t})}function rt(t){return(0,r.default)({url:"/PRO/MaterielInventory/FindRawFlowList",method:"post",data:t})}function ot(t){return(0,r.default)({url:"/pro/MaterielReport/GetProjectRawAnalyseByProject",method:"post",data:t})}function it(t){return(0,r.default)({url:"/pro/MaterielInventory/FindAuxFlowList",method:"post",data:t})}function ut(t){return(0,r.default)({url:"/pro/MaterielReturnStore/RawReturnByReceipt",method:"post",data:t})}function lt(t){return(0,r.default)({url:"/pro/MaterielReturnStore/GetRawDetailByReceipt",method:"post",data:t})}function st(t){return(0,r.default)({url:"/pro/MaterielReturnStore/GetAuxDetailByReceipt",method:"post",data:t})}function ct(t){return(0,r.default)({url:"/pro/MaterielReturnStore/AuxReturnByReceipt",method:"post",data:t})}function dt(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawBatchInventoryDetailList",method:"post",data:t})}function ft(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxBatchInventoryDetailList",method:"post",data:t})}function mt(t){return(0,r.default)({url:"/PRO/MaterielReport/ExportProjectRawAnalyse",method:"post",data:t})}function pt(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawWHSummaryList",method:"post",data:t})}function ht(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawForProjectPageList",method:"post",data:t})}function Pt(t){return(0,r.default)({url:"/PRO/MaterielInventory/ExportRawForProject",method:"post",data:t})}function gt(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawForProjectDetail",method:"post",data:t})}function yt(t){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawFilterDataSummary",method:"post",data:t})}function Rt(t){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamListByUserForMateriel",method:"post",data:t})}function bt(t){return(0,r.default)({url:"/Pro/MaterielAssignNew/TransferRawLock",method:"post",data:t})}},"4b1e":function(t,e,a){},"6db6":function(t,e,a){"use strict";a("7417")},7182:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=this,a=e.$createElement,n=e._self._c||a;return n("div",[n("div",{staticClass:"info-box"},[n("b",[e.isRaw?n("el-radio-group",{on:{input:e.radioChange},model:{value:e.BigType,callback:function(t){e.BigType=t},expression:"BigType"}},[n("el-radio-button",{attrs:{label:1}},[e._v("板材（"+e._s(e.typeNumber1)+"）")]),n("el-radio-button",{attrs:{label:2}},[e._v("型材（"+e._s(e.typeNumber2)+"）")]),n("el-radio-button",{attrs:{label:3}},[e._v("钢卷（"+e._s(e.typeNumber3)+"）")])],1):e._e()],1),n("b",{staticClass:"cs-blue"},[n("span",{staticStyle:{"margin-right":"10px"}},[e._v("数量:"+e._s(e.tbSum.Count||0))]),e.isRaw?n("span",[e._v("理重(t):"+e._s(e.tbSum.Weight?(e.tbSum.Weight/1e3).toFixed(3):0))]):e._e()])]),n("div",{staticClass:"cs-z-tb-wrapper"},[n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"400",align:"left",stripe:"",data:e.tbData.filter((function(a){return a.Big_Type===e.BigType||!t.isRaw})),resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["Weight"===t.Code?{key:"default",fn:function(a){var r=a.row;return[n("span",[e._v(e._s(r[t.Code]?(r[t.Code]/1e3).toFixed(3):0))])]}}:{key:"default",fn:function(a){var r=a.row;return[n("span",[e._v(e._s(r[t.Code]||"-"))])]}}],null,!0)})]}))],2)],1)])])},r=[]},7417:function(t,e,a){},"75ba":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content",staticStyle:{padding:"0"}},[a("top-header",{staticStyle:{height:"auto","border-bottom":"16px solid #f3f4f6"},attrs:{padding:"16px 20px 0 20px"},scopedSlots:t._u([{key:"left",fn:function(){return[a("el-form",{ref:"form",staticClass:"cs-form",attrs:{model:t.form,inline:""}},[a("el-form-item",{attrs:{label:"日期选择",prop:"Begin_Date"}},[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.planTime,callback:function(e){t.planTime=e},expression:"planTime"}})],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}},[t._l(t.ProjectNameData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Sys_Project_Id}})}))],2)],1),a("el-form-item",{attrs:{label:"采购计划单",prop:"Purchase_Plan_No"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:t.form.Purchase_Plan_No,callback:function(e){t.$set(t.form,"Purchase_Plan_No",e)},expression:"form.Purchase_Plan_No"}})],1),a("el-form-item",{attrs:{label:"项目编号",prop:"Project_Code"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:t.form.Project_Code,callback:function(e){t.$set(t.form,"Project_Code",e)},expression:"form.Project_Code"}})],1),a("el-form-item",{attrs:{label:"计划类型",prop:"Plan_Category"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Plan_Category,callback:function(e){t.$set(t.form,"Plan_Category",e)},expression:"form.Plan_Category"}},[a("el-option",{attrs:{label:"自采",value:1}}),a("el-option",{attrs:{label:"甲供",value:2}})],1)],1),a("el-form-item",{attrs:{label:"项目状态",prop:"Project_Status"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",multiple:""},model:{value:t.form.Project_Status,callback:function(e){t.$set(t.form,"Project_Status",e)},expression:"form.Project_Status"}},t._l(t.projectStatusOptions,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.getTotalInfo(),t.fetchData(1)}}},[t._v("搜 索")]),a("el-button",{on:{click:function(e){t.resetForm(),t.getTotalInfo(),t.fetchData(1)}}},[t._v("重 置 ")]),a("el-button",{attrs:{type:"success",loading:t.exportIng},on:{click:function(e){return t.handleExport()}}},[t._v("导 出 ")])],1)],1)]},proxy:!0}])}),a("div",{staticClass:"cs-z-tb-wrapper fff"},[a("div",{staticClass:"item-x"},t._l(t.itemInfos,(function(e){return a("ItemInfo",{key:e.title,attrs:{info:e,"is-raw":t.isRaw}})})),1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:t.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[t.tbConfig.Is_Row_Number?a("vxe-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:"left",align:"center"}}):t._e(),t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{"min-width":e.Width,fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",align:e.Align,field:e.Code,title:e.Display_Name},scopedSlots:t._u([["Plan_Weight","Order_Weight","In_Weight","Pick_Weight","Return_Weight","Goods_Weight","Store_Weight"].includes(e.Code)?{key:"default",fn:function(n){var r=n.row;return[t.isRaw?a("el-link",{attrs:{type:"primary"},on:{click:function(a){return a.stopPropagation(),t.showScheduleDialog(e,r)}}},[t._v(t._s(r[e.Code]?(r[e.Code]/1e3).toFixed(3):0))]):a("el-link",{attrs:{type:"primary"},on:{click:function(a){return a.stopPropagation(),t.showScheduleDialog(e,r)}}},[t._v(" "+t._s(t._f("displayValue")(r[e.Code])))])]}}:["Order_Money","In_Money","Pick_Money","Return_Money","Goods_Money","Store_Money"].includes(e.Code)?{key:"default",fn:function(n){var r=n.row;return[a("span",{attrs:{type:"primary"},on:{click:function(a){return a.stopPropagation(),t.showScheduleDialog(e,r)}}},[t._v(t._s(r[e.Code]?r[e.Code].toFixed(2):0))])]}}:"Project_Name"===e.Code?{key:"default",fn:function(n){var r=n.row;return[a("el-link",{attrs:{type:"primary",href:"#"},nativeOn:{click:function(e){return t.handleProject(r)}}},[t._v(" "+t._s(t._f("displayValue")(r[e.Code])))])]}}:{key:"default",fn:function(n){var r=n.row;return[a("span",[t._v(t._s(r[e.Code]||0))])]}}],null,!0)})]}))],2)],1),a("Pagination",{attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1),t.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:t.dialogWidth,top:"10vh"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a(t.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-type":t.dialogType,"dialog-sys-project-id":t.dialogSysProjectId,"form-data":t.form,"is-raw":t.isRaw},on:{close:t.handleClose,refresh:function(e){return t.fetchData(1)}}})],1):t._e()],1)])},r=[]},"7d8e5":function(t,e,a){"use strict";a.r(e);var n=a("ba24"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"88bd":function(t,e,a){"use strict";a.r(e);var n=a("d21e"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"8b56":function(t,e,a){"use strict";a.r(e);var n=a("7182"),r=a("c9ae");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("f549");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"21dc58ee",null);e["default"]=u.exports},9909:function(t,e,a){"use strict";a("035d")},aab9:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"item-wrapper"},[a("div",{staticClass:"cs-x"},[a("svg-icon",{attrs:{"class-name":"icons","icon-class":t.info.icon}}),a("div",{staticClass:"cs-right"},[a("span",{staticClass:"cs-title"},[t._v(t._s(t.info.title))]),a("div",{staticClass:"cs-item"},[a("label",{staticClass:"cs-label"},[t._v("数量")]),a("strong",{staticClass:"cs-num cs-green"},[t._v(t._s(t.info.num1||0))])]),t.isRaw?a("div",{staticClass:"cs-item"},[a("label",{staticClass:"cs-label"},[t._v("重量(t)")]),a("strong",{staticClass:"cs-num cs-blue"},[t._v(t._s(t.info.num2?(t.info.num2/1e3).toFixed(3):0))])]):t._e(),a("div",{staticClass:"cs-item"},[t.info.showMoney?[a("label",{staticClass:"cs-label"},[t._v("金额(元)")]),a("strong",{staticClass:"cs-num cs-orange"},[t._v(t._s(t.info.Money||0))])]:t._e()],2)])],1)])},r=[]},adda:function(t,e,a){"use strict";a.r(e);var n=a("75ba"),r=a("7d8e5");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("6db6");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"7bd89dee",null);e["default"]=u.exports},ba24:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1"));a("4de4"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("ac1f"),a("3ca3"),a("5319"),a("159b"),a("ddb0");var u=n(a("34e9")),l=a("3166"),s=n(a("15ac")),c=a("8975"),d=n(a("333d")),f=a("c685"),m=n(a("f688")),p=n(a("8b56")),h=a("3c4a"),P=n(a("2082")),g=a("ed08"),y=a("cf45"),R=a("4744"),b=a("6186");e.default={name:"PRORawProjectAnalysis",components:{Pagination:d.default,TopHeader:u.default,ItemInfo:m.default,ScheduleDialog:p.default},mixins:[s.default,P.default],data:function(){return{addPageArray:[{path:this.$route.path+"/RawProjectDetail",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-0e394c96")]).then(a.bind(null,"a460e"))},name:"PRORawProjectAnalysisDetail",meta:{title:"单项目原料分析"}},{path:this.$route.path+"/auxProjectDetail",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-0e394c96")]).then(a.bind(null,"a460e"))},name:"PROAuxProjectAnalysisDetail",meta:{title:"单项目辅料分析"}}],exportIng:!1,dialogVisible:!1,currentComponent:"",dialogTitle:"",dialogType:"",dialogWidth:"60%",dialogSysProjectId:"",tablePageSize:f.tablePageSize,itemInfos:[{title:"深化量",icon:"raw-project-analysis1",num1:0,num2:0,Money:0},{title:"采购计划量",icon:"raw-project-analysis1",num1:0,num2:0,Money:0},{title:"采购订单量",icon:"raw-project-analysis2",num1:0,num2:0,Money:0},{title:"入库量",icon:"raw-project-analysis3",num1:0,num2:0,Money:0},{title:"领用量",icon:"raw-project-analysis4",num1:0,num2:0,Money:0},{title:"退库量",icon:"raw-project-analysis5",num1:0,num2:0,Money:0},{title:"退货量",icon:"raw-project-analysis6",num1:0,num2:0,Money:0},{title:"项目锁定在库量",icon:"raw-project-analysis7",num1:0,num2:0,Money:0},{title:"公共在库量",icon:"raw-project-analysis8",num1:0,num2:0,Money:0}],form:{Sys_Project_Id:"",Purchase_Plan_No:"",Plan_Category:"",Begin_Date:"",End_Date:"",Project_Code:"",Project_Status:[],MaterialType:""},queryInfo:{Page:1,PageSize:20},ProjectNameData:[],tbData:[],columns:[],tbLoading:!1,tbConfig:{},total:0,projectStatusOptions:[]}},computed:{planTime:{get:function(){return[(0,c.timeFormat)(this.form.Begin_Date),(0,c.timeFormat)(this.form.End_Date)]},set:function(t){if(t){var e=t[0],a=t[1];this.form.Begin_Date=(0,c.timeFormat)(e),this.form.End_Date=(0,c.timeFormat)(a)}else this.form.Begin_Date="",this.form.End_Date=""}},isRaw:function(){return"PRORawProjectAnalysis"===this.$route.name}},created:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return t.form.MaterialType="PRORawProjectAnalysis"===t.$route.name?0:1,t.isRaw||t.itemInfos.shift(),t.getProjectStatusOptions(),e.n=1,t.getTableConfig("PRORawProjectAnalysisPageList");case 1:return e.n=2,t.getTotalInfo();case 2:return e.n=3,t.fetchData(1);case 3:return e.n=4,t.getProjectOption();case 4:return e.a(2)}}),e)})))()},methods:{getTableConfig:function(t){var e=this;return new Promise((function(a){(0,b.GetGridByCode)({code:t}).then((function(t){var n=t.IsSucceed,r=t.Data,o=t.Message;if(n){if(!r)return void e.$message({message:"表格配置不存在",type:"error"});e.tbConfig=Object.assign({},e.tbConfig,r.Grid),e.columns=(r.ColumnList.filter((function(t){return t.Is_Display}))||[]).map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t})),e.isRaw||(e.columns=e.columns.map((function(t){return t.Display_Name=t.Display_Name.replace("(t)",""),t}))),e.queryInfo&&(e.queryInfo.PageSize=+r.Grid.Row_Number||f.tablePageSize[0]),a(e.columns)}else e.$message({message:o,type:"error"})}))}))},getProjectStatusOptions:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:(0,R.GetPreferenceSettingValue)({code:"Productweight"}).then((function(e){(0,y.getDictionary)("true"===e.Data?"Engineering Status":"project_status").then((function(e){t.projectStatusOptions=e}))}));case 1:return e.a(2)}}),e)})))()},handleProject:function(t){this.$router.push({name:this.isRaw?"PRORawProjectAnalysisDetail":"PROAuxProjectAnalysisDetail",query:{pg_redirect:this.$route.name,id:t.Sys_Project_Id,t:this.form.Plan_Category,Material_Utilization_Rate:t.Material_Utilization_Rate,materialType:this.form.MaterialType}})},getTotalInfo:function(){var t=this;(0,h.GetProjectRawAnalyseSum)((0,r.default)({},this.form)).then((function(e){e.IsSucceed?(t.isRaw||e.Data.shift(),t.itemInfos.forEach((function(t,a){t.num1=e.Data[a].Count,t.num2=e.Data[a].Weight,t.Money=e.Data[a].Money,t.showMoney=a>1}))):t.$message({message:e.Message,type:"error"})}))},handleExport:function(){var t=this;this.exportIng=!0,(0,h.ExportProjectRawAnalyse)((0,r.default)((0,r.default)({},this.queryInfo),this.form)).then((function(e){e.IsSucceed?window.open((0,g.combineURL)(t.$baseUrl,e.Data),"_blank"):t.$message({message:e.Message,type:"error"}),t.exportIng=!1}))},pageChange:function(t){t.page,t.limit;this.fetchData()},fetchData:function(t){var e=this;t&&(this.queryInfo.Page=t),this.tbLoading=!0,(0,h.FindProjectRawAnalyse)((0,r.default)((0,r.default)({},this.queryInfo),this.form)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(t){return t})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},getProjectOption:function(){var t=this;(0,l.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},resetForm:function(){this.$refs["form"].resetFields(),this.planTime=""},handleClose:function(){this.dialogVisible=!1},showScheduleDialog:function(t,e){switch(this.dialogTitle=t.Display_Name,this.dialogSysProjectId=e.Sys_Project_Id,t.Code){case"Plan_Weight":this.dialogType=1;break;case"Order_Weight":this.dialogType=2;break;case"In_Weight":this.dialogType=3;break;case"Pick_Weight":this.dialogType=4;break;case"Return_Weight":this.dialogType=5;break;case"Goods_Weight":this.dialogType=6;break;case"Store_Weight":this.dialogType=7;break}this.currentComponent="ScheduleDialog",this.dialogWidth="60%",this.dialogVisible=!0}}}},c9ae:function(t,e,a){"use strict";a.r(e);var n=a("f07e"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=r,a("d3b7");var n=a("6186");function r(t){return new Promise((function(e,a){(0,n.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},d21e:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7"),a("ac1f"),a("25f0"),a("5319");e.default={props:{info:{type:Object,default:function(){return{title:"",icon:"",num1:0,num2:0}}},isRaw:{type:Boolean,default:!0}},methods:{formatNumberWithCommas:function(t){if(t)return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}}}},f07e:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1"));a("4de4"),a("c740"),a("caad"),a("a434"),a("e9f5"),a("910d"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var u=n(a("f16f")),l=a("3c4a");e.default={mixins:[u.default],props:{dialogType:{type:Number,default:1},dialogSysProjectId:{type:String,default:""},formData:{type:Object,default:function(){}},isRaw:{type:Boolean,default:!0}},data:function(){return{tbLoading:!1,tbData:[],rootColumns:[],columns:[],tbConfig:{},tbSum:{},BigType:1,typeNumber1:0,typeNumber2:0,typeNumber3:0}},created:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig("PRORawProjectAnalysisDetailList");case 1:return t.rootColumns=JSON.parse(JSON.stringify(t.columns)),e.n=2,t.columnsOption();case 2:return e.n=3,t.fetchData();case 3:return e.a(2)}}),e)})))()},methods:{radioChange:function(t){this.BigType=t,this.columnsOption()},columnsOption:function(){var t=this.dialogType;if(this.columns=JSON.parse(JSON.stringify(this.rootColumns)),4!==t){var e=this.columns.findIndex((function(t){return"Pick_Department_Name"===t.Code}));-1!==e&&this.columns.splice(e,1);var a=this.columns.findIndex((function(t){return"Pick_Person_Name"===t.Code}));-1!==a&&this.columns.splice(a,1)}if(this.isRaw){if(1===this.BigType){var n=this.columns.findIndex((function(t){return"Spec"===t.Code}));-1!==n&&this.columns.splice(n,1)}else if(2===this.BigType){var r=this.columns.findIndex((function(t){return"Thick"===t.Code}));-1!==r&&this.columns.splice(r,1);var o=this.columns.findIndex((function(t){return"Width"===t.Code}));-1!==o&&this.columns.splice(o,1)}else if(3===this.BigType){var i=this.columns.findIndex((function(t){return"Thick"===t.Code}));-1!==i&&this.columns.splice(i,1);var u=this.columns.findIndex((function(t){return"Width"===t.Code}));-1!==u&&this.columns.splice(u,1);var l=this.columns.findIndex((function(t){return"Length"===t.Code}));-1!==l&&this.columns.splice(l,1)}}else this.columns=this.columns.filter((function(t){return!["Thick","Width","Length","Weight"].includes(t.Code)}))},fetchData:function(){var t=this;this.tbLoading=!0;var e=(0,r.default)({},this.formData);e.Sys_Project_Id=this.dialogSysProjectId,(0,l.GetProjectRawAnalyseDetail)((0,r.default)((0,r.default)({},e),{},{Type:this.dialogType})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Item_Coll,t.tbSum=e.Data.Sum,t.setTabData()):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},setTabData:function(){this.isRaw?(this.typeNumber1=this.tbData.filter((function(t){return 1===t.Big_Type})).length,this.typeNumber2=this.tbData.filter((function(t){return 2===t.Big_Type})).length,this.typeNumber3=this.tbData.filter((function(t){return 3===t.Big_Type})).length):this.tbData=this.tbData.filter((function(t){return t.Raw_Name}))}}}},f16f:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186");e.default={methods:{getTableConfig:function(t){var e=this;return new Promise((function(a){(0,n.GetGridByCode)({code:t}).then((function(t){var n=t.IsSucceed,r=t.Data,o=t.Message;if(n){if(!r)return void e.$message({message:"表格配置不存在",type:"error"});e.tbConfig=Object.assign({},e.tbConfig,r.Grid),e.columns=(r.ColumnList.filter((function(t){return t.Is_Display}))||[]).map((function(t){return t.Is_Resizable=!0,t})),r.Grid.Is_Page&&(e.queryInfo.PageSize=+r.Grid.Row_Number),a(e.columns)}else e.$message({message:o,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit;this.queryInfo.Page=e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var n=0;n<this.columns.length;n++){var r=this.columns[n];if(r.Code===e){a.Type=r.Type,a.Filter_Type=r.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},f549:function(t,e,a){"use strict";a("4b1e")},f688:function(t,e,a){"use strict";a.r(e);var n=a("aab9"),r=a("88bd");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("9909");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"68b2c91a",null);e["default"]=u.exports}}]);