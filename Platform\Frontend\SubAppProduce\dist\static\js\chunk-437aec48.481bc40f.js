(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-437aec48"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=l,Math.easeInOutQuad=function(e,t,a,o){return e/=o/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(e,t,a){var l=n(),i=e-l,s=20,c=0;t="undefined"===typeof t?500:t;var u=function(){c+=s;var e=Math.easeInOutQuad(c,l,i,t);r(e),c<t?o(u):a&&"function"===typeof a&&a()};u()}},"0a5c":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("b0c0"),a("e9f5"),a("7d54"),a("d3b7"),a("159b");var r=o(a("5530")),n=o(a("c14f")),l=o(a("1da1")),i=a("ac6b"),s=o(a("333d")),c=a("ed08"),u=a("2e8a"),d=a("c685");t.default={components:{Pagination:s.default},data:function(){return{TotalCount:0,TotalWeight:0,form:{Type:"0",FactoryId:"",TeamId:"",ProjectId:"",AreaId:"",FinishStartTime:"",FinishEndTime:""},treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"children",label:"label",value:"Id"}},FactoryNameData:[],ProjectNameData:[],tablePageSize:d.tablePageSize,certs:[],content:{Name:"",SSZT:"",Contract_Type:"",Bim_Name:"",ContractStatus:"",Project_State:"",Project_Type:"",Manager:"",CJDW:"",CompanyId:localStorage.getItem("CurReferenceId")},pageInfoproject:{Page:1,PageSize:9999999},tableData:[],pageInfo:{Page:1,total:0,PageSize:d.tablePageSize[0]},loading:!1,Factorytime:[],groupsNameData:[],treeParamsSteel:[],treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}}}},created:function(){this.getfactory(),this.getProjectOption(),this.getCompTypeTree()},mounted:function(){var e=this;return(0,l.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.fetchData();case 1:return t.a(2)}}),t)})))()},methods:{getCompTypeTree:function(){var e=this;(0,u.GetCompTypeTree)({professional:"Steel"}).then((function(t){e.$refs.treeSelect.treeDataUpdateFun(t.Data)}))},handleClick:function(e,t){this.pageInfo.PageSize=20,this.pageInfo.Page=1,this.pageInfo.total=0,this.form={Type:e.name,FactoryId:"",TeamId:"",ProjectId:"",AreaId:"",FinishStartTime:"",FinishEndTime:""},this.fetchData()},handleExport:function(){var e=this;(0,i.ExportTeamProcessingTask)((0,r.default)((0,r.default)({},this.pageInfoproject),this.form)).then((function(t){t.IsSucceed?window.open((0,c.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"})}))},getData:function(){this.pageInfo.Page=1,this.form.FinishStartTime=this.Factorytime?this.Factorytime[0]:"",this.form.FinishEndTime=this.Factorytime?this.Factorytime[1]:"",this.fetchData()},getfactory:function(){var e=this;(0,i.GetFactoryPageList)({pageInfo:this.pageInfoproject}).then((function(t){t.IsSucceed?e.FactoryNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getGropList:function(){var e=this;this.form.TeamId="",(0,i.GetWorkingTeams)({FactoryId:this.form.FactoryId,Type:this.form.Type}).then((function(t){t.IsSucceed?e.groupsNameData=t.Data:e.$message({message:t.Message,type:"error"})}))},getProjectOption:function(){var e=this;return(0,l.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:(0,i.GetEntities)({content:e.content,pageInfo:e.pageInfoproject}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getAreaList:function(){var e=this;this.form.AreaId="",(0,i.GetGetMonomerList)({projectId:this.form.ProjectId}).then((function(t){t.IsSucceed?e.pickCert(t.Data):e.$message({message:t.Message,type:"error"})}))},pickCert:function(e){var t=this;this.certs=[],e.forEach((function(e){var a=[];(0,i.GetAreaPageList)({pageInfo:{Page:1,PageSize:9999999,MonomerId:e.Id,ProjectId:t.form.ProjectId,AreaName:""},MonomerId:e.Id,ProjectId:t.form.ProjectId,AreaName:""}).then((function(o){if(o.IsSucceed){var r={};r=o.Data.Data,r.forEach((function(e){a.push({label:e.Name,Id:e.Id})})),t.certs.push({disabled:"全部单体"===e.Name,label:e.Name,Id:e.Id,children:a})}}))})),this.treeParamsArea.data=this.certs,this.$nextTick((function(e){t.$refs.treeSelectArea.treeDataUpdateFun(t.certs)}))},fetchData:function(){var e=this;this.tableData=[],this.loading=!0,(0,i.GetTeamProcessingTask)((0,r.default)((0,r.default)({},this.pageInfo),this.form)).then((function(t){e.loading=!1,t.IsSucceed?(e.tableData=t.Data.Data||[],e.pageInfo.PageSize=t.Data.PageSize,e.pageInfo.Page=t.Data.Page,e.pageInfo.total=t.Data.TotalCount,e.$refs.table.doLayout()):e.$message({message:t.Message,type:"error"})})),(0,i.GetSummaryTeamProcessingTask)((0,r.default)((0,r.default)({},this.pageInfo),this.form)).then((function(t){t.IsSucceed?(e.TotalCount=t.Data.TotalCount,e.TotalWeight=t.Data.TotalWeight):e.$message({message:t.Message,type:"error"})}))}}}},"2e8a":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=u,t.GetCompTypeTree=d,t.GetComponentTypeEntity=c,t.GetComponentTypeList=l,t.GetFactoryCompTypeIndentifySetting=h,t.GetTableSettingList=m,t.GetTypePageList=i,t.RestoreTemplateType=b,t.SavDeepenTemplateSetting=P,t.SaveCompTypeIdentifySetting=y,t.SaveComponentType=s,t.SaveProBimComponentType=p,t.UpdateColumnSetting=g,t.UpdateComponentPartTableSetting=f;var r=o(a("b775")),n=o(a("4328"));function l(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:n.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:n.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:n.default.stringify(e)})}function p(e){return(0,r.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function h(e){return(0,r.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function y(e){return(0,r.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function P(e){return(0,r.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function b(e){return(0,r.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"36c2":function(e,t,a){},"464b":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return r}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"box-card h100"},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-tab-pane",{attrs:{label:"构件",name:"0"}}),a("el-tab-pane",{attrs:{label:"零件",name:"1"}})],1),a("div",{staticClass:"total-right"},[a("span",[e._v("总数："+e._s(e.TotalCount)+"件；")]),a("span",[e._v("总重："+e._s(e.TotalWeight)+"t")])]),a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"加工工厂"}},[a("el-select",{attrs:{filterable:"",clearable:""},on:{change:e.getGropList},model:{value:e.form.FactoryId,callback:function(t){e.$set(e.form,"FactoryId",t)},expression:"form.FactoryId"}},e._l(e.FactoryNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"加工班组"}},[a("el-select",{attrs:{filterable:"",clearable:"",disabled:!e.form.FactoryId},model:{value:e.form.TeamId,callback:function(t){e.$set(e.form,"TeamId",t)},expression:"form.TeamId"}},e._l(e.groupsNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:0==e.form.Type,expression:"form.Type==0"}],attrs:{label:"构件类型"}},[a("el-tree-select",{ref:"treeSelect",attrs:{"tree-params":e.treeParams},model:{value:e.form.SteelType_Id,callback:function(t){e.$set(e.form,"SteelType_Id",t)},expression:"form.SteelType_Id"}})],1),a("el-form-item",{attrs:{label:"项目"}},[a("el-select",{attrs:{filterable:"",clearable:""},on:{change:e.getAreaList},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.ProjectId,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},model:{value:e.form.AreaId,callback:function(t){e.$set(e.form,"AreaId",t)},expression:"form.AreaId"}})],1),a("el-form-item",{attrs:{label:"完成时间"}},[a("el-date-picker",{staticStyle:{width:"250px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.Factorytime,callback:function(t){e.Factorytime=t},expression:"Factorytime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.getData}},[e._v("搜索")])],1)],1),a("div",{staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleExport}},[e._v("导出")])],1),a("div",{staticClass:"plm-bimtable"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",staticStyle:{width:"100%"},attrs:{border:"",stripe:"",data:e.tableData,height:"calc(100vh - 400px)"}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"FactoryName",label:"加工工厂",align:"center","show-overflow-tooltip":"",fixed:""}}),a("el-table-column",{attrs:{prop:"TeamName",label:"加工班组",align:"center","show-overflow-tooltip":"",fixed:""}}),a("el-table-column",{attrs:{prop:"ProjectCode",label:"项目编号",align:"center","show-overflow-tooltip":"",fixed:""}}),a("el-table-column",{attrs:{prop:"ProjectShortName",label:"项目名称",align:"center","show-overflow-tooltip":"",fixed:""}}),a("el-table-column",{attrs:{prop:"AreaName",label:"区域",align:"center","show-overflow-tooltip":"",fixed:""}}),a("el-table-column",{attrs:{prop:"CompCode",label:"0"===e.form.Type?"构件名称":"零件名称",align:"center","show-overflow-tooltip":"",fixed:""}}),0==e.form.Type?a("el-table-column",{attrs:{prop:"SteelTypeName",label:"构件类型",align:"center","show-overflow-tooltip":"",fixed:""}}):e._e(),a("el-table-column",{attrs:{prop:"SteelSpec",label:"规格",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"SteelLength",label:"长度",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"SteelWeight",label:"单重(kg)",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"FinishCount",label:"报工完成数量(件)",align:"center","show-overflow-tooltip":"","min-width":"130px"}}),a("el-table-column",{attrs:{prop:"FinishWeight",label:"报工完成量(kg)",align:"center","show-overflow-tooltip":"","min-width":"130px"}}),a("el-table-column",{attrs:{prop:"ProcessName",label:"加工工序",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"NextProcessName",label:"接收工序",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"FinishDateString",label:"完成时间",align:"center","show-overflow-tooltip":"","min-width":"120px"}})],1),a("div",{staticClass:"page"},[a("pagination",{attrs:{total:e.pageInfo.total,page:e.pageInfo.Page,limit:e.pageInfo.PageSize,"page-sizes":e.tablePageSize},on:{"update:page":function(t){return e.$set(e.pageInfo,"Page",t)},"update:limit":function(t){return e.$set(e.pageInfo,"PageSize",t)},pagination:e.fetchData}})],1)],1)],1)])},r=[]},4808:function(e,t,a){"use strict";a.r(t);var o=a("464b"),r=a("d148");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);a("af508");var l=a("2877"),i=Object(l["a"])(r["default"],o["a"],o["b"],!1,null,"739a6544",null);t["default"]=i.exports},ac6b:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportTeamProcessingTask=g,t.FindMatBillSumPageList=h,t.GetAreaPageList=c,t.GetCompanyFactoryPageList=d,t.GetEntities=i,t.GetFactoryPageList=u,t.GetGetMonomerList=s,t.GetMatBillSumSubList=y,t.GetProcessingProgress=n,t.GetProcessingProgressTask=l,t.GetSummaryTeamProcessingTask=f,t.GetTeamProcessingTask=p,t.GetWorkingTeams=m;var r=o(a("b775"));function n(e){return(0,r.default)({url:"/PRO/ProductionReport/GetProcessingProgress",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/ProductionReport/GetProcessingProgressTask",method:"post",data:e})}function i(e){return(0,r.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_projects/GetEntities"),method:"post",data:e})}function s(e){return(0,r.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetGetMonomerList"),method:"post",data:e})}function c(e){return(0,r.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetAreaPageList"),method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPageList",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Factory/GetCompanyFactoryPageList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProductionReport/GetTeamProcessingTask",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProductionReport/GetWorkingTeams",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProductionReport/GetSummaryTeamProcessingTask",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProductionReport/ExportTeamProcessingTask",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ProductionReport/FindMatBillSumPageList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/ProductionReport/GetMatBillSumSubList",method:"post",data:e})}},af508:function(e,t,a){"use strict";a("36c2")},d148:function(e,t,a){"use strict";a.r(t);var o=a("0a5c"),r=a.n(o);for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);t["default"]=r.a}}]);