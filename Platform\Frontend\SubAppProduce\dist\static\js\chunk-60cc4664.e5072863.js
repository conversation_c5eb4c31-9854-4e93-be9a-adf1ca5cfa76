(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-60cc4664"],{"02c5":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("7db0"),a("a15b"),a("fb6a"),a("b0c0"),a("e9f5"),a("f665"),a("a9e3"),a("d3b7"),a("843c");var s=i(a("2b0e")),r=i(a("5c7f")),n=a("3b8f"),o=a("ae9d"),l=a("e824"),c=a("f235");(0,n.use)([<PERSON><PERSON>,<PERSON><PERSON>,c<PERSON>,c<PERSON>,c.<PERSON>]),s.default.component("v-chart",r.default);e.default={props:{dataList:{type:Array,default:function(){return[]}},center:{type:Array,default:function(){return["18%","50%"]}},radius:{type:String,default:"90%"},legendGap:{type:Number,default:10}},data:function(){var t=this;return{chartOption:{title:{text:"",left:"0",top:"0",textStyle:{color:"#333",fontSize:16,fontWeight:"normal"}},tooltip:{trigger:"item",formatter:function(t){return"".concat(t.name,"<br/>数量: ").concat(t.value,"<br/>占比: ").concat(t.data.percentage,"%")}},legend:{orient:"vertical",right:"0",top:"center",itemWidth:6,itemHeight:6,itemGap:this.legendGap,icon:"circle",formatter:function(e){var a=t.chartOption.series[0].data,i=a.find((function(t){return t.name===e})),s=t.setFixedWidth(e,6);return["{a|"+s+"}","{b|"+i.value+"}","{c|"+i.percentage+"%}"].join("  ")},textStyle:{rich:{a:{width:80,align:"left",color:"#333"},b:{width:40,align:"right",color:"#298DFF"},c:{width:40,align:"right",color:"#FF902C"}}}},series:[{name:"",type:"pie",radius:this.radius,center:this.center,avoidLabelOverlap:!1,label:{show:!1},emphasis:{label:{show:!1}},labelLine:{show:!1},data:this.dataList}]}}},watch:{dataList:function(t,e){this.chartOption.series[0].data=t}},methods:{setFixedWidth:function(t,e){return t.length>e?t.slice(0,e-1)+"…":t.padEnd(e,"　")}}}},"0446":function(t,e,a){"use strict";a.r(e);var i=a("6cca"),s=a("c68c");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(r);a("4c14");var n=a("2877"),o=Object(n["a"])(s["default"],i["a"],i["b"],!1,null,"cc87757a",null);e["default"]=o.exports},"0470":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return s}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-wrapper "},[a("div",{staticClass:"inner-container"},[a("div",{staticClass:"search-x"},[a("el-form",{ref:"form",attrs:{model:t.form,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"专业类别",prop:"ProfessionalTypeName"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:"",disabled:""},model:{value:t.form.ProfessionalTypeName,callback:function(e){t.$set(t.form,"ProfessionalTypeName",e)},expression:"form.ProfessionalTypeName"}})],1),a("el-form-item",{attrs:{label:"选择年份",prop:"Year"}},[a("el-date-picker",{attrs:{"value-format":"yyyy",type:"year",clearable:!1,placeholder:"选择年"},on:{change:t.yearChange},model:{value:t.form.Year,callback:function(e){t.$set(t.form,"Year",e)},expression:"form.Year"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),a("el-button",{on:{click:t.reset}},[t._v("重置")])],1)],1)],1),a("div",{staticClass:"cs-row f1"},[a("div",{staticClass:"cs-box cs-w"},[a("div",{staticClass:"title-x"},[a("div",{staticClass:"cs-title mb27"},[t._v("年度成品全检数据")]),a("el-form",{ref:"form",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"质检类型"}},[a("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择"},on:{change:t.getTotalInfo},model:{value:t.checkType,callback:function(e){t.checkType=e},expression:"checkType"}},[a("el-option",{attrs:{label:"质量",value:1}}),a("el-option",{attrs:{label:"探伤",value:2}})],1)],1)],1)],1),a("div",{staticClass:"cs-row1"},[a("div",{staticClass:"cs-col"},[a("div",{staticClass:"inner-item"},[a("div",{staticClass:"item-title"},[t._v("全检重量（t）")]),a("div",{staticClass:"item-num cs-yellow"},[t._v(t._s(t.getNum(t.totalInfo.Check_Weight)))]),a("svg-icon",{attrs:{"icon-class":"p-weight","class-name":"cs-pic"}})],1)]),a("div",{staticClass:"cs-col"},[a("div",{staticClass:"inner-item "},[a("div",{staticClass:"item-title"},[t._v("全检数量")]),a("div",{staticClass:"item-num cs-blue"},[t._v(t._s(t.getNum(t.totalInfo.Check_Count)))]),a("svg-icon",{attrs:{"icon-class":"p-qj","class-name":"cs-pic"}})],1)])]),a("div",{staticClass:"cs-row1 mt8"},[a("div",{staticClass:"cs-col"},[a("div",{staticClass:"inner-item "},[a("div",{staticClass:"item-title"},[t._v("一次合格数量")]),a("div",{staticClass:"item-num cs-blue"},[t._v(t._s(t.getNum(t.totalInfo.First_Pass_Count)))]),a("svg-icon",{attrs:{"icon-class":"p-hg","class-name":"cs-pic"}})],1)]),a("div",{staticClass:"cs-col"},[a("div",{staticClass:"inner-item"},[a("div",{staticClass:"item-title"},[t._v("一次合格率")]),a("div",{staticClass:"item-num cs-green"},[t._v(" "+t._s(t.totalInfo.First_Pass_Rate?t.getNum(t.totalInfo.First_Pass_Rate)+"%":0)+" ")]),a("svg-icon",{attrs:{"icon-class":"p-hgp","class-name":"cs-pic"}})],1)])])]),a("div",{staticClass:"cs-box e-chart-x fd"},[a("div",{staticClass:"chart-top"},[a("div",{staticClass:"cs-title"},[t._v("年度成品全检合格信息汇总")]),a("el-form",{ref:"form",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"质检类型"}},[a("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择"},on:{change:t.getChartInfo},model:{value:t.checkType3,callback:function(e){t.checkType3=e},expression:"checkType3"}},[a("el-option",{attrs:{label:"质量",value:1}}),a("el-option",{attrs:{label:"探伤",value:2}})],1)],1)],1)],1),t.showLine?a("LineBar",{ref:"line"}):a("CustomEmpty",{attrs:{label:"无质检数据"}})],1),a("div",{staticClass:"cs-box cs-rank fd"},[t._m(0),a("el-form",{ref:"form",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"选择时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:!1,"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.getRank},model:{value:t.rankTime,callback:function(e){t.rankTime=e},expression:"rankTime"}})],1)],1),t.rankList.length?a("div",{staticClass:"rank-x fd"},t._l(t.rankList,(function(e,i){return a("div",{key:i,staticClass:"rank-item"},[a("div",{staticClass:"rank-info"},[a("span",{staticClass:"rank-name"},[t._v(t._s(e.Check_UserName))]),a("span",[t._v(t._s(e.Check_Count))])]),a("el-progress",{attrs:{"stroke-width":8,"show-text":!1,"stroke-linecap":"square",percentage:e.percent}})],1)})),0):a("CustomEmpty",{attrs:{label:"无排名记录"}})],1)]),a("div",{staticClass:"cs-row f2"},[a("div",{staticClass:"cs-box group-x fd"},[a("div",{staticClass:"cs-title mb16"},[t._v("班组合格率排名（全检）")]),a("el-form",{ref:"form",staticClass:"cs-form",attrs:{inline:"","label-width":"40px"}},[a("el-form-item",{attrs:{label:"选择时间","label-width":"70px"}},[a("el-date-picker",{staticStyle:{width:"230px"},attrs:{clearable:!1,"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.getTbList},model:{value:t.teamTime,callback:function(e){t.teamTime=e},expression:"teamTime"}})],1),a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{staticStyle:{width:"80px"},attrs:{placeholder:"请选择"},on:{change:t.getTbList},model:{value:t.checkType2,callback:function(e){t.checkType2=e},expression:"checkType2"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),a("el-option",{attrs:{label:"质量",value:1}}),a("el-option",{attrs:{label:"探伤",value:2}})],1)],1),a("el-form-item",{attrs:{label:"对象"}},[a("el-select",{staticStyle:{width:"80px"},attrs:{placeholder:"请选择"},on:{change:t.getTbList},model:{value:t.Check_Object_Type,callback:function(e){t.Check_Object_Type=e},expression:"Check_Object_Type"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),a("el-option",{attrs:{label:"构件",value:0}}),a("el-option",{attrs:{label:"零件",value:1}})],1)],1),a("el-form-item",{staticClass:"cs-form-item",attrs:{"label-width":"40px",label:"车间"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择"},on:{change:t.getTbList},model:{value:t.Workshop_Id,callback:function(e){t.Workshop_Id=e},expression:"Workshop_Id"}},t._l(t.workshopList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{staticClass:"cs-form-item",attrs:{"label-width":"40px",label:"项目"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"请选择"},on:{change:t.getTbList},model:{value:t.Sys_Project_Id,callback:function(e){t.Sys_Project_Id=e},expression:"Sys_Project_Id"}},t._l(t.projectList,(function(t){return a("el-option",{key:t.Sys_Project_Id,attrs:{label:t.Short_Name,value:t.Sys_Project_Id}})})),1)],1)],1),a("div",{staticClass:"tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"center","show-overflow":"",stripe:"",height:"auto",size:"medium",resizable:"","tooltip-config":{enterable:!0},data:t.tbData}},[a("vxe-table-column",{attrs:{"min-width":"80",field:"name",title:"排名",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){e.rowIndex;var i=e.$rowIndex;return[i<3?a("span",{staticClass:"tb-idx-x"},[a("span",{staticClass:"tb-idx"},[t._v(t._s(i+1))]),0===i?a("svg-icon",{attrs:{"icon-class":"rank1","class-name":"cs-rank"}}):1===i?a("svg-icon",{attrs:{"icon-class":"rank2","class-name":"cs-rank"}}):2===i?a("svg-icon",{attrs:{"icon-class":"rank3","class-name":"cs-rank"}}):t._e()],1):a("span",[t._v(t._s(i+1))])]}}])}),a("vxe-table-column",{attrs:{align:"left","min-width":"120",field:"Working_Team_Name",title:"班组名称"}}),a("vxe-table-column",{attrs:{align:"left","min-width":"120",field:"Workshop_Name",title:"所属车间"}}),a("vxe-table-column",{attrs:{"min-width":"120",field:"Inspection_Count",title:"报检量"}}),a("vxe-table-column",{attrs:{"min-width":"120",field:"Check_Count",title:"质检量"}}),a("vxe-table-column",{attrs:{"min-width":"120",field:"Qualified_Count",title:"合格量"}}),a("vxe-table-column",{attrs:{"min-width":"120",field:"First_Pass_Count",title:"一次合格量"}}),a("vxe-table-column",{attrs:{"min-width":"120",field:"First_Pass_Rate",title:"一次合格率"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("span",{class:{"cs-red":i.showRed}},[t._v(t._s(i.First_Pass_Rate))])]}}])}),a("vxe-table-column",{attrs:{"min-width":"120",field:"Rectify_Count",title:"整改完成数量"}})],1)],1)],1),a("div",{staticClass:"cs-box pie-x"},[t._m(1),a("el-form",{ref:"form",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"选择时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:!1,"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.getCheckingItem},model:{value:t.zbTime,callback:function(e){t.zbTime=e},expression:"zbTime"}})],1)],1),t.zl.length?a("div",{staticClass:"pie-container"},[t._m(2),a("div",{staticClass:"pie-wrapper"},[a("Pie",{attrs:{"data-list":t.zl}})],1)]):t._e(),t.ts.length?a("div",{staticClass:"pie-container"},[t._m(3),a("div",{staticClass:"pie-wrapper"},[a("Pie",{attrs:{"data-list":t.ts}})],1)]):t._e(),t.ts.length||t.zl.length?t._e():a("CustomEmpty",{attrs:{label:"无问题记录"}})],1),a("div",{staticClass:"cs-box cs-summary fd"},[t._m(4),a("el-form",{ref:"form",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"选择时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:!1,"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.getCheckingQuestion},model:{value:t.zjTime,callback:function(e){t.zjTime=e},expression:"zjTime"}})],1)],1),t.zjSummary.length?a("div",{staticClass:"pie-title"},[a("div",{staticClass:"title3"},[t._v("占比")]),a("div",{staticClass:"title2"},[t._v("不合格数")])]):t._e(),t.zjSummary.length?a("div",{staticClass:"pie-wrapper"},[a("Pie",{attrs:{"data-list":t.zjSummary,radius:"50","legend-gap":20}})],1):t._e(),t.zjSummary.length?t._e():a("CustomEmpty",{attrs:{label:"无汇总记录"}})],1)])])])},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"title-x"},[a("div",{staticClass:"cs-title"},[t._v("人员月度质量数量排名")]),a("span",{staticClass:"title-info"},[t._v("单位：件")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"title-x"},[a("div",{staticClass:"cs-title"},[t._v("工序质检问题占比")]),a("span",{staticClass:"title-info"},[t._v("单位：个")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pie-title"},[a("div",{staticClass:"title1"},[t._v("质量")]),a("div",{staticClass:"title2"},[t._v("问题数")]),a("div",{staticClass:"title3"},[t._v("占比")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pie-title"},[a("div",{staticClass:"title1"},[t._v("探伤")]),a("div",{staticClass:"title2"},[t._v("问题数")]),a("div",{staticClass:"title3"},[t._v("占比")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"title-x"},[a("div",{staticClass:"cs-title"},[t._v("质检类型数量汇总")]),a("span",{staticClass:"title-info"},[t._v("单位：个")])])}]},"05a0":function(t,e,a){},"15ee":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("cb29"),a("d81d"),a("13d5"),a("4e82"),a("e9f5"),a("7d54"),a("ab43"),a("9485"),a("b680"),a("d3b7"),a("25f0"),a("159b");var s=i(a("5530")),r=i(a("c14f")),n=i(a("1da1")),o=i(a("6612")),l=i(a("2470")),c=i(a("ad9d")),u=a("7f9d"),d=a("fd31"),f=a("7196"),h=a("3166"),m=i(a("0446"));e.default={name:"PROHome",components:{LineBar:l.default,Pie:c.default,CustomEmpty:m.default},data:function(){return{form:{ProfessionalTypeName:"",Year:""},checkType:1,checkType2:void 0,checkType3:1,showLine:!1,Workshop_Id:"",Check_Object_Type:void 0,Sys_Project_Id:"",ProCategoryList:[],workshopList:[],projectList:[],dataList:[],rankList:[],tbData:[],zjSummary:[],zl:[],ts:[],rankTime:"",planTime:"",teamTime:"",zbTime:"",zjTime:"",totalInfo:{Check_Count:0,First_Pass_Count:0,Check_Weight:0,First_Pass_Rate:0}}},mounted:function(){var t=this;return(0,n.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.setYear(),e.n=1,t.getProfessionalTypeList();case 1:t.getTotalInfo(),t.getWorkshop(),t.handleSearch();case 2:return e.a(2)}}),e)})))()},methods:{yearChange:function(){this.setYear(this.form.Year)},handleSearch:function(){this.teamTime=this.planTime,this.rankTime=this.planTime,this.rankTime=this.planTime,this.zbTime=this.planTime,this.zjTime=this.planTime,this.getTotalInfo(),this.getTbList(),this.getChartInfo(),this.getRank(),this.getCheckingItem(),this.getCheckingQuestion()},getTotalInfo:function(){var t=this;(0,u.GetYearlyFullCheckProducedData)({Year:this.form.Year,Check_Type:this.checkType}).then((function(e){var a=e.Data,i=a.Check_Count,s=void 0===i?0:i,r=a.First_Pass_Count,n=void 0===r?0:r,o=a.Check_Weight,l=void 0===o?0:o,c=a.First_Pass_Rate,u=void 0===c?0:c;Object.assign(t.totalInfo,{Check_Count:s,First_Pass_Count:n,Check_Weight:l,First_Pass_Rate:u})}))},getTbList:function(){var t=this;(0,u.GetWorkingTeamCheckingList)({Date_Begin:this.teamTime[0],Date_End:this.teamTime[1],Check_Type:this.checkType2,Check_Object_Type:this.Check_Object_Type,Workshop_Id:this.Workshop_Id,Sys_Project_Id:this.Sys_Project_Id}).then((function(e){e.IsSucceed?t.tbData=e.Data.map((function(t){return t.First_Pass_Rate=(null===t||void 0===t?void 0:t.First_Pass_Rate)||0,t.showRed=t.First_Pass_Rate<.9,t.First_Pass_Rate=(0,o.default)((null===t||void 0===t?void 0:t.First_Pass_Rate)||0).format("0.[00]%"),t})):t.$message({message:e.Message,type:"error"})}))},getCheckingQuestion:function(){var t=this,e=["#298DFF","#00D0EB","#4EBF8B","#FDD603","#FF902C","#FF5E7C"],a=function(t){return t&&"number"===typeof t?t.toFixed(2)/1:0};(0,u.GetCheckingQuestionList)({Date_Begin:this.zjTime[0],Date_End:this.zjTime[1]}).then((function(i){i.IsSucceed?t.zjSummary=i.Data.map((function(t,i){var s=e[i]||"#298DFF";return{value:t.Problem_Count,name:t.Problem_Name,percentage:a(t.Problem_Percent),itemStyle:{color:s}}})):t.$message({message:i.Message,type:"error"})}))},getCheckingItem:function(){var t=this,e=["#5470c6","#fac858","#91cc72","#ee6666"],a=function(t){return t&&"number"===typeof t?t.toFixed(2)/1:0},i=function(t){return t.map((function(t,i){var s=e[i]||"#5470c6";return{value:t.Check_Count,name:t.Check_Name,percentage:a(t.Check_Percent),itemStyle:{color:s}}}))};(0,u.GetCheckingItemList)({Date_Begin:this.zbTime[0],Date_End:this.zbTime[1]}).then((function(e){if(e.IsSucceed){var a=e.Data,s=a.ZL_List,r=a.TC_List;t.zl=i(s),t.ts=i(r)}else t.$message({message:e.Message,type:"error"})}))},getRank:function(){var t,e,a=this;(0,u.GetCheckUserRankList)({Date_Begin:(null===(t=this.rankTime)||void 0===t?void 0:t[0])||"",Date_End:(null===(e=this.rankTime)||void 0===e?void 0:e[1])||""}).then((function(t){if(t.IsSucceed){var e=t.Data,i=e.reduce((function(t,e){return(e.Check_Count||0)>(t.Check_Count||0)?e:t}),{}),r=i.Check_Count;a.rankList=t.Data.map((function(t){return(0,s.default)((0,s.default)({},t),{},{Check_Count:t.Check_Count||0,percent:t.Check_Count/r*100})})).sort((function(t,e){return e.Check_Count-t.Check_Count}))}else a.$message({message:t.Message,type:"error"})}))},getWorkshop:function(){var t=this;(0,f.GetWorkshopPageList)({Page:-1}).then((function(e){var a;e.IsSucceed?(t.workshopList=(null===(a=e.Data)||void 0===a?void 0:a.Data)||[],t.workshopList.length&&t.workshopList.unshift({Id:"",Display_Name:"全部"})):t.$message({message:e.Message,type:"error"})})),(0,h.GetProjectPageList)({PageSize:-1}).then((function(e){var a;e.IsSucceed?(t.projectList=(null===(a=e.Data)||void 0===a?void 0:a.Data)||[],t.projectList.length&&t.projectList.unshift({Sys_Project_Id:"",Short_Name:"全部"})):t.$message({message:e.Message,type:"error"})}))},getChartInfo:function(){var t=this;(0,u.GetMonthlyFullCheckProducedData)({Year:this.form.Year,Check_Type:this.checkType3}).then((function(e){if(e.IsSucceed){var a=new Array(12).fill(0),i=new Array(12).fill(0),s=new Array(12).fill(0),r=!1;e.Data.forEach((function(t){var e=t.Year_Month-1;a[e]=t.Check_Count,i[e]=t.Qualified_Count,s[e]=t.First_Pass_Rate,(t.Check_Count||t.Qualified_Count||t.First_Pass_Rate)&&(r=!0)})),t.showLine=r,t.$nextTick((function(e){t.showLine&&t.$refs["line"].updateChartOption(a,i,s)}))}else t.$message({message:e.Message,type:"error"})}))},getProfessionalTypeList:function(){var t=this;return(0,n.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,d.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(e){e.IsSucceed?t.form.ProfessionalTypeName=e.Data[0].Name:t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},getNum:function(t){return"number"!==typeof t?0:(0,o.default)(t).format("0,0.[000]")},setYear:function(t){this.form.Year=t?new Date(t).getFullYear().toString():(new Date).getFullYear().toString(),this.planTime=["".concat(this.form.Year,"-01-01"),"".concat(this.form.Year,"-12-31")],this.teamTime=this.planTime,this.rankTime=this.planTime,this.rankTime=this.planTime,this.zbTime=this.planTime,this.zjTime=this.planTime},reset:function(){this.checkType3=1,this.checkType=1,this.checkType2=void 0,this.Check_Object_Type=void 0,this.Workshop_Id="",this.Sys_Project_Id="",this.setYear(),this.handleSearch()}}}},"230e":function(t,e,a){"use strict";a("05a0")},2470:function(t,e,a){"use strict";a.r(e);var i=a("a803"),s=a("afc7");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(r);a("6ac7");var n=a("2877"),o=Object(n["a"])(s["default"],i["a"],i["b"],!1,null,"bc873aea",null);e["default"]=o.exports},"2c3a":function(t,e,a){"use strict";a.r(e);var i=a("0470"),s=a("ab31");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(r);a("230e");var n=a("2877"),o=Object(n["a"])(s["default"],i["a"],i["b"],!1,null,"27b4b513",null);e["default"]=o.exports},3166:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=h,e.DeleteProject=u,e.GeAreaTrees=k,e.GetFileSync=x,e.GetInstallUnitIdNameList=P,e.GetNoBindProjectList=m,e.GetPartDeepenFileList=T,e.GetProjectAreaTreeList=g,e.GetProjectEntity=l,e.GetProjectList=o,e.GetProjectPageList=n,e.GetProjectTemplate=p,e.GetPushProjectPageList=y,e.GetSchedulingPartList=j,e.IsEnableProjectMonomer=d,e.SaveProject=c,e.UpdateProjectTemplateBase=v,e.UpdateProjectTemplateContacts=_,e.UpdateProjectTemplateContract=C,e.UpdateProjectTemplateOther=b;var s=i(a("b775")),r=i(a("4328"));function n(t){return(0,s.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function o(t){return(0,s.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,s.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function c(t){return(0,s.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function u(t){return(0,s.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function d(t){return(0,s.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,s.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function h(t){return(0,s.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function m(t){return(0,s.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function p(t){return(0,s.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function v(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function _(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function C(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function b(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function y(t){return(0,s.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function g(t){return(0,s.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function P(t){return(0,s.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function k(t){return(0,s.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function T(t){return(0,s.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function j(t){return(0,s.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function x(t){return(0,s.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"3da7":function(t,e,a){},"4c14":function(t,e,a){"use strict";a("3da7")},"53d7":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={props:{label:{type:String,default:"暂无数据"}}}},"5d4c":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("b0c0"),a("e9f5"),a("7d54"),a("d3b7"),a("159b");var s=a("3b8f"),r=a("ae9d"),n=a("e824"),o=a("f235"),l=i(a("5c7f"));(0,s.use)([r.CanvasRenderer,n.BarChart,n.LineChart,o.GridComponent,o.TitleComponent,o.TooltipComponent,o.LegendComponent]);e.default={components:{VChart:l.default},props:{zjList:{type:Array,default:function(){return[]}},hgList:{type:Array,default:function(){return[]}},pList:{type:Array,default:function(){return[]}}},data:function(){return{chartOption:{tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:function(t){var e="font-weight:bold;",a='<span  style="'.concat(e,'">').concat(t[0].name,"</span>")+"<br/>";return t.forEach((function(t){var i=t.value,s="";"质检量"===t.seriesName||"合格量"===t.seriesName?s="#FF902C":"一次合格率"===t.seriesName&&(s="#4EBF8B",i+="%"),a+="<span>".concat(t.seriesName,': <span style="color:').concat(s,"; ").concat(e,'">').concat(i,"</span> </span><br/>")})),a}},legend:{data:["质检量","合格量","一次合格率"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{axisTick:{show:!1},axisLine:{show:!1},type:"category",data:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"]},yAxis:[{type:"value",name:"质检量/合格量"},{type:"value",show:!1,name:"一次合格率",min:0,max:100}],series:[{name:"质检量",type:"bar",barWidth:10,itemStyle:{color:"#FF902C"},data:this.zjList},{name:"合格量",type:"bar",itemStyle:{color:"#298DFF"},barWidth:10,data:this.hgList},{name:"一次合格率",type:"line",itemStyle:{color:"#00CFAA"},symbolSize:0,label:{show:!0,position:"top",formatter:"{c}%",textStyle:{fontWeight:"bold",fontSize:16,color:"#4EBF8B",textAlign:"center"}},yAxisIndex:1,data:this.pList}]}}},methods:{updateChartOption:function(t,e,a){this.chartOption.series[0].data=t,this.chartOption.series[1].data=e,this.chartOption.series[2].data=a}}}},"6ac7":function(t,e,a){"use strict";a("f28d")},"6cca":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return s}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"emptyBox"},[a("div",{staticClass:"empty"}),a("div",{staticClass:"label"},[t._v(t._s(t.label))])])},s=[]},7196:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteWorkshop=c,e.GetFactoryPeoplelist=r,e.GetWorkshopEntity=l,e.GetWorkshopPageList=o,e.SaveEntity=n;var s=i(a("b775"));function r(t){return(0,s.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function n(t){return(0,s.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:t})}function o(t){return(0,s.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:t})}function l(t){return(0,s.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:t})}function c(t){return(0,s.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:t})}},"8f50":function(t,e,a){"use strict";a.r(e);var i=a("02c5"),s=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=s.a},a527:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return s}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"chart-container"},[a("v-chart",{staticClass:"chart",attrs:{option:t.chartOption}})],1)},s=[]},a803:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return s}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"echart"},[a("v-chart",{attrs:{option:t.chartOption,autoresize:""}})],1)},s=[]},ab31:function(t,e,a){"use strict";a.r(e);var i=a("15ee"),s=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=s.a},ad9d:function(t,e,a){"use strict";a.r(e);var i=a("a527"),s=a("8f50");for(var r in s)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(r);a("eca8");var n=a("2877"),o=Object(n["a"])(s["default"],i["a"],i["b"],!1,null,"4ba131b5",null);e["default"]=o.exports},afc7:function(t,e,a){"use strict";a.r(e);var i=a("5d4c"),s=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=s.a},c68c:function(t,e,a){"use strict";a.r(e);var i=a("53d7"),s=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=s.a},d570:function(t,e,a){},eca8:function(t,e,a){"use strict";a("d570")},f28d:function(t,e,a){}}]);