(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4e9fa6b6"],{"64fe":function(t,e,a){"use strict";a.r(e);var i=a("8216f"),n=a("b632");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("d7ea4");var o=a("2877"),s=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"2a4effc1",null);e["default"]=s.exports},"8216f":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),a("span",[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))]),a("el-button",{style:{marginLeft:"32px",background:t.getPercentInfo(t.preparePercent).color,color:"#FFF"},attrs:{round:"",size:"small"}},[t._v("入库完成率: "+t._s(t.getPercentInfo(t.preparePercent).value.toFixed(2))+"%")]),a("div",{staticClass:"right-fix"},[a("el-select",{staticStyle:{width:"126px"},attrs:{size:"mini",clearable:"",placeholder:"选择入库状态",filterable:""},model:{value:t.fiterArrObj.Is_Accord_With,callback:function(e){t.$set(t.fiterArrObj,"Is_Accord_With",e)},expression:"fiterArrObj.Is_Accord_With"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"待排产",value:"待排产"}}),a("el-option",{attrs:{label:"待生产",value:"待生产"}}),a("el-option",{attrs:{label:"生产中",value:"生产中"}}),a("el-option",{attrs:{label:"待入库",value:"待入库"}}),a("el-option",{attrs:{label:"已入库",value:"已入库"}}),a("el-option",{attrs:{label:"已出库",value:"已出库"}})],1),t._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.filterChange(1)}}},[t._v("查询")])],1)],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange},scopedSlots:t._u([{key:"Is_Ready",fn:function(e){var i=e.column,n=e.row;e.$index;return[n[i.Code]?a("el-button",{attrs:{type:"success",round:"",size:"small"}},[t._v("是")]):a("el-button",{attrs:{type:"danger",round:"",size:"small"}},[t._v("否")])]}}])})],1)])])},n=[]},"98e6":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("d3b7"),a("3ca3"),a("ddb0");var n=i(a("0f97")),r=a("1b69"),o=i(a("b775")),s=a("2dd9"),l=a("6186"),c=a("ed08");e.default={name:"WarehousingDetail",components:{DynamicDataTable:n.default},data:function(){return{apis:{GetStockInDetailPageList:"/PRO/ComponentStockIn/GetComponentStockInDetailPageList"},fiterArrObj:{},gridCode:"pro_installunit_component_in_summary",filterData:{Page:1,PageSize:10},tbConfig:{},columns:[],data:[],projects:[]}},computed:{unit:function(){var t,e;return null!==(t=null===(e=this.$route.params)||void 0===e?void 0:e.row)&&void 0!==t?t:{}},direct:function(){var t,e;return null!==(t=null===(e=this.$route.params)||void 0===e?void 0:e.direct)&&void 0!==t?t:0},preparePercent:function(){var t;return t=this.direct?"Is_Direct_Finish":"Is_In_Finish","是"==this.unit[t]?100:Number(this.unit[t])}},created:function(){var t=this;this.setGrid({Is_Page:!0,Height:0,Row_Number:12,Is_Filter:!0}),Promise.all([(0,r.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))]).then((function(){(0,l.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList),t.filterData.PageSize=e.Data.Grid.Row_Number)})).then((function(){t.getTableData()}))})),this.setGridData({Data:[{Amount:20}]})},methods:{tagBack:function(){(0,c.closeTagView)(this.$store,this.$route)},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120}),this.filterData.PageSize=this.tbConfig.Row_Number},setCols:function(t){this.columns=t},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},getTableData:function(){var t=this;(0,o.default)({url:this.apis.GetStockInDetailPageList,method:"post",data:Object.assign({},this.filterData,{InstallUnit_Id:this.unit.Id,Project_Id:this.unit.Project_Id,C_Type:this.direct?"直发件":"构件",ParameterJson:(0,s.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(e){e.IsSucceed&&(t.filterData.TotalCount=e.Data.TotalCount,t.setGridData(e.Data))}))},filterChange:function(t){this.filterData.Page=t||1,this.getTableData()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.PageSize=e,this.filterData.Page=1,this.filterChange()},getProduceProcess:function(t,e){},getPercentInfo:function(t){var e=Number(t),a={value:e};return e<25?a.color="#000":e<=25&&e<100?a.color="#298DFF":100==e&&(a.color="#3ECC93"),a}}}},b632:function(t,e,a){"use strict";a.r(e);var i=a("98e6"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},b7e9b:function(t,e,a){},d7ea4:function(t,e,a){"use strict";a("b7e9b")}}]);