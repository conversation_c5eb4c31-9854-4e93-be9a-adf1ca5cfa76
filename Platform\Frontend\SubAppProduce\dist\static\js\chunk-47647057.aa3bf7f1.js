(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-47647057"],{"0347":function(t,e,a){},"28bd":function(t,e,a){},"2a3b":function(t,e,a){"use strict";a.r(e);var i=a("78c0"),r=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=r.a},"329b":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return r}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),a("span",[t._v("新增盘库单")]),a("div",{staticClass:"right-fix"},[a("el-button",{attrs:{loading:t.btnLoading,type:"primary",disabled:!t.canSave||t.isClicked},on:{click:t.save}},[t._v("保存")]),a("el-button",{on:{click:t.tagBack}},[t._v("取消")])],1)],1),a("div",{staticClass:"header"},[a("el-form",{staticClass:"h-adv-form",attrs:{inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名称",required:""}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择项目",multiple:"",filterable:"",clearable:""},on:{change:t.filterChange},model:{value:t.fiterArrObj.Project_Id,callback:function(e){t.$set(t.fiterArrObj,"Project_Id",e)},expression:"fiterArrObj.Project_Id"}},t._l(t.projects,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"盘点名称",required:""}},[a("el-input",{staticStyle:{width:"160px"},attrs:{clearable:""},model:{value:t.fiterArrObj.Name,callback:function(e){t.$set(t.fiterArrObj,"Name",e)},expression:"fiterArrObj.Name"}})],1),a("el-form-item",{attrs:{label:"仓库",required:"","label-width":"60px"}},[a("el-select",{attrs:{clearable:"",placeholder:"选择仓库",multiple:"",filterable:""},on:{change:t.filterChange},model:{value:t.fiterArrObj.Warehouse_Id,callback:function(e){t.$set(t.fiterArrObj,"Warehouse_Id",e)},expression:"fiterArrObj.Warehouse_Id"}},t._l(t.wares,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"备注","label-width":"60px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"备注..."},model:{value:t.fiterArrObj.Remark,callback:function(e){t.$set(t.fiterArrObj,"Remark",e)},expression:"fiterArrObj.Remark"}})],1)],1)],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""},on:{gridSizeChange:t.gridSizeChange,gridPageChange:t.gridPageChange}})],1)]),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},r=[]},3598:function(t,e,a){"use strict";a.r(e);var i=a("329b"),r=a("2a3b");for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);a("f975"),a("e3b2");var s=a("2877"),o=Object(s["a"])(r["default"],i["a"],i["b"],!1,null,"000cfed4",null);e["default"]=o.exports},"78c0":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("7db0"),a("d81d"),a("14d9"),a("fb6a"),a("e9f5"),a("f665"),a("7d54"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("159b"),a("ddb0");var r=a("1b69"),n=i(a("0f97")),s=a("6186"),o=i(a("b775")),l=a("209b");e.default={name:"Add",components:{DynamicDataTable:n.default},data:function(){return{gridCode:"pro_stock_inventory_result_list",canSave:!0,isClicked:!1,projects:[],wares:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},tbConfig:{},columns:[],data:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},created:function(){var t=this;(0,s.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})),Promise.all([(0,r.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)})),(0,l.GetWarehouseListOfCurFactory)({}).then((function(e){e.IsSucceed&&(t.wares=e.Data)}))]).then((function(){}))},methods:{tagBack:function(){var t=this,e=this.$route;this.$store.dispatch("tagsView/delView",e).then((function(a){var i=a.visitedViews;if(e.path===t.$route.path){var r=i.slice(-1)[0];t.$router.push(r.fullPath)}}))},multiSelectedChange:function(t){this.checkedRows=t},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120}),this.filterData.PageSize=Number(t.Row_Number)},setCols:function(t){this.columns=t.concat([])},setGridData:function(t){this.data=t.Data},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.type;t.data;switch(e){case"reload":break}},save:function(){var t=this;if(!this.fiterArrObj.Name)return this.$message.warning("盘点名称必填");if(!this.fiterArrObj.Project_Id||this.fiterArrObj.Project_Id.length<=0)return this.$message.warning("必须选择项目");if(!this.fiterArrObj.Warehouse_Id||this.fiterArrObj.Warehouse_Id.length<=0)return this.$message.warning("必须选择仓库");this.isClicked=!0;var e=this.formatPostData();(0,l.SaveInventory)(e).then((function(e){var a;e.IsSucceed?(t.$message.success("保存成功"),t.fiterArrObj.Bill_Id=e.Message,t.canSave=!1,t.fiterArrObj.Bill_Id&&t.getBillDetails()):(t.isClicked=!1,t.$message.warning(null!==(a=e.Message)&&void 0!==a?a:""))}))},formatPostData:function(){var t=this,e={Inventory_Warehouse_Ids:this.fiterArrObj["Warehouse_Id"],Inventory_Project_Ids:this.fiterArrObj["Project_Id"],Remark:this.fiterArrObj["Remark"]||"",Name:this.fiterArrObj["Name"],Inventory_Date:new Date},a=[];e.Inventory_Warehouse_Ids.forEach((function(e){var i=t.wares.find((function(t){return t.Id===e}));a.push(i)}));var i=[];return e.Inventory_Project_Ids.forEach((function(e){var a=t.projects.find((function(t){return t.Id===e}));i.push(a)})),e.Inventory_Project_Names=i.map((function(t){return t.Name})),e.Inventory_Warehouse_Names=a.map((function(t){return t.Display_Name})),e},filterChange:function(t){var e=this;if("all"===t)return!1;(0,o.default)({url:this.tbConfig.Data_Url,method:"post",data:{}}).then((function(t){t.IsSucceed&&e.setGridData(t.Data.Data)}))},getBillDetails:function(){var t=this;(0,o.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{Bill_Id:this.fiterArrObj.Bill_Id})}).then((function(e){e.IsSucceed&&(t.setGridData(e.Data),t.filterData.TotalCount=e.Data.TotalCount)}))},gridSizeChange:function(t){var e=t.size;this.filterData.PageSize=e,this.filterData.Page=1,this.getBillDetails()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.getBillDetails()}}}},e3b2:function(t,e,a){"use strict";a("0347")},f975:function(t,e,a){"use strict";a("28bd")}}]);