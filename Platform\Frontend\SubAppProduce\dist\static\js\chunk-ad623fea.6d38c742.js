(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ad623fea"],{"0714":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"车间",prop:"workShop"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.workShop,callback:function(t){e.$set(e.form,"workShop",t)},expression:"form.workShop"}},e._l(e.workShopOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},"0cdf":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("7db0"),n("c740"),n("0481"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("4e82"),n("a434"),n("4069"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("a732"),n("e9c4"),n("b64b"),n("d3b7"),n("2532"),n("159b");var r=a(n("c14f")),i=a(n("2909")),o=a(n("1da1")),s=a(n("5530")),l=n("a024"),c=a(n("b76a")),u=n("ed08"),d=n("2f62"),f=n("e144");t.default={components:{Draggable:c.default},props:{pageType:{type:String,default:void 0},isNest:{type:Boolean,default:!1},processList:{type:Object,default:function(){return{}}}},data:function(){return{list:[],options:[],btnLoading:!1,pgLoading:!1,form:{}}},computed:{isCom:function(){return"com"===this.pageType}},methods:(0,s.default)((0,s.default)({},(0,d.mapActions)("schedule",["initProcessList"])),{},{getProcessOption:function(e){var t=this;return new Promise((function(n,a){t.pgLoading=!0,(0,l.GetProcessListBase)({workshopId:e,type:1}).then((function(e){e.IsSucceed?t.options=e.Data.map((function(e){return t.$set(e,"disabled",!1),e})):t.$message({message:e.Message,type:"error"}),n()})).finally((function(e){t.pgLoading=!1}))}))},selectChange:function(e,t){var n,a=this.list.map((function(e){return e.value}));(this.options.forEach((function(e,t){e.disabled=a.includes(e.Code)})),this.isCom)&&(t&&(t.date=null===(n=this.processList[e])||void 0===n?void 0:n.Finish_Date))},dateChange:function(e,t){var n,a=this.options.find((function(e){return e.Code===t.value}));a&&(null===(n=this.formInline)||void 0===n||n.Schduling_Code,a.Id,a.Code)},handleAdd:function(e){var t=this.list.map((function(e){return e.value}));this.options.forEach((function(e){t.includes(e.Code)&&(e.disabled=!0)})),this.list.push({key:(0,f.v4)(),value:"",date:""})},handleDelete:function(e){var t=this.list.findIndex((function(t){return t.value===e.value}));-1!==t&&(this.list.splice(t,1),this.selectChange())},setData:function(e,t){var n=this;return(0,o.default)((0,r.default)().m((function a(){var o,s,l,c,u,d,h,p;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return o=[],t&&(o=t.split("/")),s=e[0].Workshop_Id,l=[],e.forEach((function(e){if(e.Part_Used_Process){var t=e.Part_Used_Process.split(",");l.push.apply(l,(0,i.default)(t))}})),a.n=1,n.getProcessOption(s);case 1:n.options=n.options.filter((function(e){var t=!1;return o.length&&o.includes(e.Code)&&(t=!0),l.length&&l.includes(e.Code)&&(t=!0),e.Part_Type_Used_Process&&e.Part_Type_Used_Process===e.Code&&(t=!0),t||(t=!!e.Is_Enable),t})),n.arr=e||[],n.list=[],c=[],n.isCom&&(u=e.map((function(e){return((null===e||void 0===e?void 0:e.Part_Used_Process)||"").split(",")})),c=n.getUnique(u.flat()).filter((function(e){return!!e})),c.length&&(c.filter((function(e){return!!n.options.find((function(t){return t.Code===e}))})),c.forEach((function(e,t){var a,r={value:e,isPart:!0,key:(0,f.v4)(),date:null===(a=n.processList[e])||void 0===a?void 0:a.Finish_Date};n.list.push(r)})))),o.length&&(d=o.map((function(e){var t;return{key:(0,f.v4)(),value:e,date:null===(t=n.processList[e])||void 0===t?void 0:t.Finish_Date}})),d.forEach((function(e,t){c.includes(e.value)||n.list.push(e)}))),n.list.length||(n.list.push({value:"",key:(0,f.v4)(),date:""}),n.isNest&&(h=n.options.filter((function(e){return e.Is_Nest})),1===h.length&&(n.list[0].value=h[0].Code))),p=o.reduce((function(e,t,n){return e[t]=n,e}),{}),n.list.sort((function(e,t){return p[e.value]-p[t.value]})),n.selectChange();case 2:return a.a(2)}}),a)})))()},getUnique:function(e){return(0,u.uniqueArr)(e)},submit:function(){var e=this,t=this.list.map((function(e){return e.value})).filter((function(e){return!!e})),n=this.checkCode(t);if(n)if(t.length){if(this.isNest){var a=this.options.filter((function(e){return e.Is_Nest}));if(a.length){var r=a.some((function(e){return t.includes(e.Code)}));if(!r)return void this.$message({message:"请至少选择一个套料工序！",type:"warning"})}}this.btnLoading=!0;var i=t.join("/");this.list.forEach((function(t,n){var a,r=e.options.find((function(e){return e.Code===t.value})),i={};r&&(i={Schduling_Id:null===(a=e.formInline)||void 0===a?void 0:a.Schduling_Code,Process_Id:r.Id,Process_Code:r.Code,Finish_Date:t.date});e.$emit("setProcessList",{key:t.value,value:i})})),this.$emit("sendProcess",{arr:this.arr,str:i}),this.btnLoading=!1,this.handleClose()}else this.$message({message:"工序不能全为空",type:"warning"});else this.$message({message:"相邻工序不能相同",type:"warning"})},handleClose:function(){this.$emit("close")},checkCode:function(e){for(var t=!0,n=0;n<e.length;n++)if(n!==e.length-1&&e[n]===e[n+1]){t=!1;break}return t},changeDraggable:function(){var e=this;this.list.forEach((function(t){e.$set(t,"date","")})),this.initProcessList()}})}},"10b3":function(e,t,n){"use strict";n("e9f55")},"2ce2":function(e,t,n){"use strict";n.r(t);var a=n("317e"),r=n("ca6a");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("728a");var o=n("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"776deda4",null);t["default"]=s.exports},3056:function(e,t,n){"use strict";function a(e){var t=Object(e),n=[];for(var a in t)n.unshift(a);return function e(){for(;n.length;)if((a=n.pop())in t)return e.value=a,e.done=!1,e;return e.done=!0,e}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=a},"317e":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},e._l(e.itemOption,(function(t){return n("el-form-item",{key:t.code,attrs:{label:t.label,prop:"ownerProcess"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择"},model:{value:t.value,callback:function(n){e.$set(t,"value",n)},expression:"element.value"}},e._l(e.OwnerOption[t.code],(function(e){return n("el-option",{key:e.Code,attrs:{label:e.Name,value:e.Code}})})),1)],1)})),1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},3378:function(e,t,n){"use strict";n("d211")},"45cd":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("7db0"),n("caad"),n("d81d"),n("14d9"),n("13d5"),n("e9f5"),n("d866"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("d3b7"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("159b"),n("ddb0");var r=a(n("2909")),i=a(n("c14f")),o=a(n("1da1")),s=n("7196"),l=n("a024");t.default={props:{isVersionFour:{type:Boolean,default:!1}},data:function(){return{form:{workShop:""},btnLoading:!1,workShopOption:[],rules:{}}},methods:{submit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,o.default)((0,i.default)().m((function t(n){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!n){t.n=1;break}e.btnLoading=!0,e.form.workShop?(a=e.workShopOption.find((function(t){return t.Id===e.form.workShop})),e.$emit("workShop",{workShop:a,origin:e.origin,row:e.row}),e.btnLoading=!1,e.handleClose()):(e.$emit("workShop",{workShop:{},origin:e.origin,row:e.row}),e.btnLoading=!1,e.handleClose()),t.n=2;break;case 1:return t.a(2,!1);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},checkProcess:function(e,t,n){var a=[];return n.forEach((function(n){var r=t[n];if(r){var i=r.every((function(t){return e.includes(t)}));i||a.push(n)}})),a},getFlowList:function(){var e=this,t=[];if(1===this.origin)t=this.gyCodes;else{var n;if(!this.row||null===(n=this.row)||void 0===n||!n.Technology_Code)return[];t=[this.row.Technology_Code]}return new Promise((function(n,a){(0,l.GetProcessFlowListWithTechnology)({TechnologyCodes:t,Type:1}).then((function(t){if(t.IsSucceed){var r=t.Data||[],i=r.reduce((function(e,t){return e[t.Code]=t.Technology_Path,e}),{});n(i)}else e.$message({message:t.Message,type:"error"}),a()}))}))},getProcess:function(e){var t=this;return new Promise((function(n,a){(0,l.GetProcessListBase)({workshopId:e,type:1}).then((function(e){if(e.IsSucceed){var r=(e.Data||[]).map((function(e){return e.Code}));n(r)}else t.$message({message:e.Message,type:"error"}),a()}))}))},fetchData:function(e,t){var n=this,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];this.origin=e,this.selectList=a,this.row=t,this.gyCodes=(0,r.default)(new Set(this.selectList.map((function(e){return e.Technology_Code})).filter((function(e){return!!e})))),(0,s.GetWorkshopPageList)({Page:1,PageSize:-1}).then((function(e){var t;e.IsSucceed?(null!==e&&void 0!==e&&null!==(t=e.Data)&&void 0!==t&&t.Data||(n.workShopOption=[]),n.workShopOption=e.Data.Data.map((function(e){return{Id:e.Id,Display_Name:e.Display_Name}}))):n.$message({message:e.Message,type:"error"})})),2===e&&(this.form.workShop=t.Workshop_Id)},handleClose:function(){this.$emit("close")}}}},5750:function(e,t,n){"use strict";n("d665")},"626d":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100 flex-row"},[e.isAdd?n("div",{staticClass:"cs-left"},[n("ExpandableSection",{model:{value:e.showExpand,callback:function(t){e.showExpand=t},expression:"showExpand"}},[n("div",{staticClass:"cs-tree-wrapper"},[n("div",{staticClass:"tree-search"},[n("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"请选择"},on:{change:e.fetchTreeStatus},model:{value:e.statusType,callback:function(t){e.statusType=t},expression:"statusType"}},[n("el-option",{attrs:{label:"可排产",value:"可排产"}}),n("el-option",{attrs:{label:"排产完成",value:"排产完成"}}),n("el-option",{attrs:{label:"未导入",value:"未导入"}})],1),n("el-input",{attrs:{placeholder:"搜索...",size:"small",clearable:"","suffix-icon":"el-icon-search"},on:{blur:e.fetchTreeDataLocal,clear:e.fetchTreeDataLocal},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fetchTreeDataLocal(t)}},model:{value:e.projectName,callback:function(t){e.projectName="string"===typeof t?t.trim():t},expression:"projectName"}})],1),n("el-divider"),n("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":e.customFilterFun,loading:e.treeLoading,"tree-data":e.treeData,"show-status":"","show-detail":"","filter-text":e.filterText,"expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick},scopedSlots:e._u([{key:"csLabel",fn:function(t){var a=t.showStatus,r=t.data;return[r.ParentNodes?e._e():n("span",{staticClass:"cs-blue"},[e._v("("+e._s(r.Code)+")")]),e._v(e._s(r.Label)+" "),a?[r.Data[e.statusCode]?n("i",{class:["可排产"==r.Data[e.statusCode]?"fourGreen":"排产完成"==r.Data[e.statusCode]?"fourOrange":"未导入"==r.Data[e.statusCode]?"fourRed":""]},[n("span",[e._v("("+e._s(r.Data[e.statusCode])+")")])]):e._e()]:e._e()]}}],null,!1,3288586200)})],1)])],1):e._e(),n("div",{staticClass:"cs-right"},[n("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"box-card h100",attrs:{"element-loading-text":"正在处理..."}},[n("h4",{staticClass:"topTitle"},[n("span"),e._v("基本信息")]),n("el-form",{ref:"formInline",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline}},[e.isAdd||e.isNest?e._e():n("el-form-item",{attrs:{label:"排产单号",prop:"Schduling_Code"}},[e.isView?n("span",[e._v(e._s(0===e.formInline.Status?"":e.formInline.Schduling_Code))]):n("el-input",{attrs:{disabled:""},model:{value:e.formInline.Schduling_Code,callback:function(t){e.$set(e.formInline,"Schduling_Code",t)},expression:"formInline.Schduling_Code"}})],1),n("el-form-item",{attrs:{label:"计划员",prop:"Create_UserName"}},[e.isView?n("span",[e._v(e._s(e.formInline.Create_UserName))]):n("el-input",{attrs:{disabled:""},model:{value:e.formInline.Create_UserName,callback:function(t){e.$set(e.formInline,"Create_UserName",t)},expression:"formInline.Create_UserName"}})],1),n("el-form-item",{attrs:{label:"要求完成时间",prop:"Finish_Date",rules:{required:!0,message:"请选择",trigger:"change"}}},[e.isView?n("span",[e._v(e._s(e._f("timeFormat")(e.formInline.Finish_Date)))]):n("el-date-picker",{attrs:{"picker-options":e.pickerOptions,disabled:e.isView,"value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:e.formInline.Finish_Date,callback:function(t){e.$set(e.formInline,"Finish_Date",t)},expression:"formInline.Finish_Date"}})],1),e.isNest||e.isVersionFour?e._e():n("el-form-item",{attrs:{label:"批次",prop:"Create_UserName"}},[e.isView?n("span",[e._v(e._s(e.installName))]):n("el-select",{attrs:{disabled:!e.isAdd,filterable:"",placeholder:"请选择"},on:{change:e.installChange},model:{value:e.formInline.InstallUnit_Id,callback:function(t){e.$set(e.formInline,"InstallUnit_Id",t)},expression:"formInline.InstallUnit_Id"}},e._l(e.installUnitIdList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[e.isView?n("span",[e._v(e._s(e.formInline.Remark))]):n("el-input",{staticStyle:{width:"320px"},attrs:{disabled:e.isView,placeholder:"请输入"},model:{value:e.formInline.Remark,callback:function(t){e.$set(e.formInline,"Remark",t)},expression:"formInline.Remark"}})],1)],1),n("el-divider",{staticClass:"elDivder"}),n("div",{staticClass:"btn-x"},[e.isView?e._e():n("div",[n("div",{ref:"searchDom",staticClass:"search-container"},[n("el-form",{ref:"searchForm",attrs:{model:e.innerForm,inline:""}},[n("el-form-item",{attrs:{"label-width":"80px",prop:"searchContent",label:e.isCom?"构件名称":"零件名称"}},[n("el-input",{staticClass:"input-with-select",attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.innerForm.searchContent,callback:function(t){e.$set(e.innerForm,"searchContent",t)},expression:"innerForm.searchContent"}},[n("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.curSearch,callback:function(t){e.curSearch=t},expression:"curSearch"}},[n("el-option",{attrs:{label:"精准查询",value:1}}),n("el-option",{attrs:{label:"模糊查询",value:0}})],1)],1)],1),n("el-form-item",{attrs:{"label-width":"80px",label:e.isCom?"构件类型":"零件类型",prop:"searchComTypeSearch"}},["view"!==e.$route.query.status?n("el-tree-select",{ref:"treeSelectComponentType",staticClass:"cs-tree-x",attrs:{placeholder:"请选择","select-params":e.treeSelectParams,"tree-params":e.treeParamsComponentType},on:{searchFun:e.componentTypeFilter},model:{value:e.innerForm.searchComTypeSearch,callback:function(t){e.$set(e.innerForm,"searchComTypeSearch",t)},expression:"innerForm.searchComTypeSearch"}}):e._e()],1),n("el-form-item",{attrs:{label:"规格","label-width":"50px",prop:"searchSpecSearch"}},[n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.innerForm.searchSpecSearch,callback:function(t){e.$set(e.innerForm,"searchSpecSearch",t)},expression:"innerForm.searchSpecSearch"}})],1),e.isCom?n("el-form-item",{attrs:{label:"是否直发件",prop:"searchDirect"}},[n("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.innerForm.searchDirect,callback:function(t){e.$set(e.innerForm,"searchDirect",t)},expression:"innerForm.searchDirect"}},[n("el-option",{attrs:{label:"是",value:!0}}),n("el-option",{attrs:{label:"否",value:!1}})],1)],1):e._e(),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.innerFilter}},[e._v("搜索")]),n("el-button",{on:{click:e.resetInnerForm}},[e._v("重置")])],1)],1)],1)])]),n("vxe-toolbar",{ref:"xToolbar1",scopedSlots:e._u([{key:"buttons",fn:function(){return[e.isView?[n("el-button",{attrs:{disabled:!e.tbData.length},on:{click:e.handleExport}},[e._v("导出")])]:[e.isNest?e._e():n("el-button",{attrs:{type:"primary",disabled:e.disabledAdd},on:{click:function(t){return e.handleAddDialog()}}},[e._v("添加")]),e.workshopEnabled?n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleBatchWorkshop(1)}}},[e._v("分配车间 ")]):e._e(),e.isCom?e._e():n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleSelectMenu("process")}}},[e._v("分配工序 ")]),e.isCom?n("el-dropdown",{staticStyle:{margin:"0 10px"},on:{command:e.handleSelectMenu}},[n("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"primary",plain:""}},[e._v(" 分配工序"),n("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{attrs:{command:"process"}},[e._v("批量分配工序 ")]),e.isCom?n("el-dropdown-item",{attrs:{command:"deal"}},[e._v("构件类型自动分配 ")]):e._e(),e.isVersionFour?n("el-dropdown-item",{attrs:{command:"craft"}},[e._v("工艺代码分配 ")]):e._e()],1)],1):e._e(),e.isCom||e.isOwnerNull?e._e():n("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleBatchOwner(1)}}},[e._v("批量分配领用工序 ")]),n("el-button",{attrs:{plain:"",disabled:!e.tbData.length,loading:!1},on:{click:e.handleReverse}},[e._v("反选 ")]),n("el-button",{attrs:{type:"danger",plain:"",loading:e.deleteLoading,disabled:!e.multipleSelection.length},on:{click:e.handleDelete}},[e._v("删除 ")])]]},proxy:!0},{key:"tools",fn:function(){return[n("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.changeColumn}})]},proxy:!0}])}),n("div",{staticClass:"tb-x"},[n("vxe-table",{key:e.tbKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","checkbox-config":{checkField:"checked"},"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","filter-config":{showIcon:!1},"show-overflow":"",loading:e.tbLoading,stripe:"","scroll-y":{enabled:!0,gt:20},size:"medium","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Is_Component"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"",field:t.Code,title:t.Display_Name,sortable:"",filters:e.isComponentOptions,"filter-method":e.filterComponentMethod,width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-tag",{attrs:{type:a.Is_Component?"danger":"success"}},[e._v(e._s(a.Is_Component?"否":"是")+" ")])]}}],null,!0)}):["Type","Type_Name"].includes(t.Code)?n("vxe-column",{key:t.Code,attrs:{align:t.Align,"filter-method":e.filterTypeMethod,field:t.Code,filters:e.filterTypeOption,title:t.Display_Name,fixed:t.Is_Frozen?t.Frozen_Dirction:"",sortable:"",width:t.Width},scopedSlots:e._u([{key:"filter",fn:function(t){var a=t.$panel,r=t.column;return e._l(r.filters,(function(t,r){return n("input",{directives:[{name:"model",rawName:"v-model",value:t.data,expression:"option.data"}],key:r,attrs:{type:"type"},domProps:{value:t.data},on:{input:[function(n){n.target.composing||e.$set(t,"data",n.target.value)},function(e){return a.changeOption(e,!!t.data,t)}]}})}))}},{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e._f("displayValue")(a[t.Code]))+" ")]}}],null,!0)}):["Comp_Code","Part_Code"].includes(t.Code)?n("vxe-column",{key:t.Code,attrs:{align:t.Align,"filter-method":e.filterCodeMethod,field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction:"",filters:e.filterCodeOption,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"filter",fn:function(t){var a=t.$panel,r=t.column;return e._l(r.filters,(function(t,r){return n("input",{directives:[{name:"model",rawName:"v-model",value:t.data,expression:"option.data"}],key:r,attrs:{type:"type"},domProps:{value:t.data},on:{input:[function(n){n.target.composing||e.$set(t,"data",n.target.value)},function(e){return a.changeOption(e,!!t.data,t)}]}})}))}},{key:"default",fn:function(a){var r=a.row;return[r.Is_Change?n("el-tag",{staticStyle:{margin:"8px"},attrs:{type:"danger"}},[e._v("变")]):e._e(),r.stopFlag?n("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),n("span",[e._v(e._s(e._f("displayValue")(r[t.Code])))])]}}],null,!0)}):"Spec"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction:"",filters:e.specOptions,"filter-method":e.filterSpecMethod,title:t.Display_Name,sortable:"",width:t.Width},scopedSlots:e._u([{key:"filter",fn:function(t){var a=t.$panel,r=t.column;return e._l(r.filters,(function(t,r){return n("input",{directives:[{name:"model",rawName:"v-model",value:t.data,expression:"option.data"}],key:r,attrs:{type:"type"},domProps:{value:t.data},on:{input:[function(n){n.target.composing||e.$set(t,"data",n.target.value)},function(e){return a.changeOption(e,!!t.data,t)}]}})}))}},{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("displayValue")(n.Spec))+" ")]}}],null,!0)}):"Schduled_Weight"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,fixed:t.Is_Frozen?t.Frozen_Dirction:"",sortable:"",width:t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s((n.Schduled_Count*n.Weight).toFixed(2)/1)+" ")]}}],null,!0)}):"Technology_Path"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"",field:t.Code,"show-overflow":!1,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"cs-column-row"},[n("div",{staticClass:"cs-ell"},[n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.Technology_Path,placement:"top"}},[n("span",[e._v(e._s(e._f("displayValue")(a.Technology_Path)))])])],1),e.isView?e._e():n("i",{staticClass:"el-icon-edit",on:{click:function(t){return e.openBPADialog(2,a)}}})])]}}],null,!0)}):"Part_Used_Process"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"",field:t.Code,title:t.Display_Name,sortable:"","show-overflow":!1,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"cs-column-row"},[n("div",{staticClass:"cs-ell"},[n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.Part_Used_Process,placement:"top"}},[n("span",[e._v(e._s(e._f("displayValue")(a.Part_Used_Process)))])])],1),e.showPartUsedProcess(a)?n("i",{staticClass:"el-icon-edit",on:{click:function(t){return e.handleBatchOwner(2,a)}}}):e._e()])]}}],null,!0)}):"Workshop_Name"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,"show-overflow":!1,field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction:"",title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("div",{staticClass:"cs-column-row"},[n("div",{staticClass:"cs-ell"},[n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:a.Workshop_Name,placement:"top"}},[n("span",[e._v(e._s(e._f("displayValue")(a.Workshop_Name)))])])],1),e.isView?e._e():n("i",{staticClass:"el-icon-edit",on:{click:function(t){return e.handleBatchWorkshop(2,a)}}})])]}}],null,!0)}):"Schduled_Count"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"",fixed:t.Is_Frozen?t.Frozen_Dirction:"","edit-render":{},"min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"integer",min:"0",max:a.chooseCount},model:{value:a.Schduled_Count,callback:function(t){e.$set(a,"Schduled_Count",e._n(t))},expression:"row.Schduled_Count"}})]}},{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("displayValue")(n.Schduled_Count))+" ")]}}],null,!0)}):n("vxe-column",{key:t.Id,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)],1),e.isView?e._e():n("el-divider",{staticClass:"elDivder"}),e.isView?e._e():n("footer",[n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.multipleSelection.length)+" 条数据 ")]),e.tipLabel?n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v(e._s(e.tipLabel)+" ")]):e._e()],1),n("div",[e.workshopEnabled&&!e.isNest?n("el-button",{attrs:{type:"primary"},on:{click:e.saveWorkShop}},[e._v("保存车间分配")]):e._e(),e.isNest?e._e():n("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:function(t){return e.saveDraft(!1)}}},[e._v("保存草稿 ")]),n("el-button",{attrs:{disabled:e.deleteLoading||e.tbData.some((function(e){return e.stopFlag}))},on:{click:e.handleSubmit}},[e._v("下发任务")])],1)])],1)],1),e.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"is-nest":e.isNest,"is-version-four":e.isVersionFour,"is-part-prepare":e.isPartPrepare,"process-list":e.processList,"page-type":e.pageType,"part-type-option":e.typeOption},on:{close:e.handleClose,sendProcess:e.sendProcess,workShop:e.getWorkShop,refresh:e.fetchData,setProcessList:e.setProcessList}})],1):e._e(),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],key:e.addDraftKey,staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.openAddDraft,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.openAddDraft=t},close:e.handleClose}},[n("add-draft",{ref:"draft",attrs:{"current-ids":e.currentIds,"is-part-prepare":e.isPartPrepare,"area-id":e.areaId,"install-id":e.formInline.InstallUnit_Id,"schedule-id":e.scheduleId,"show-dialog":e.openAddDraft,"page-type":e.pageType},on:{sendSelectList:e.mergeSelectList,close:e.handleClose}})],1)],1)},r=[]},"635e":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("2909"));n("4de4"),n("7db0"),n("14d9"),n("13d5"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("9485"),n("d3b7"),n("159b");var i=n("a024");t.default={props:{pageType:{type:String,default:void 0},partTypeOption:{type:Array,default:function(){return[]}},isPartPrepare:{type:Boolean,default:!1}},data:function(){return{itemOption:[{key:"",value:""}],form:{},loading:!1,btnLoading:!1,OwnerOption:{},rules:{}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.list.forEach((function(t){var n=e.itemOption.find((function(e){return e.code===t.Type}));n&&(t.Scheduled_Used_Process&&t.Scheduled_Used_Process!==n.value?e.$message({message:"请和该区域批次下已排产同零件领用工序保持一致",type:"warning"}):t.Part_Used_Process=n.value)})),e.btnLoading=!1,e.handleClose()}))},setOption:function(e,t){var n=this;this.list=t||[],this.isInline=e;var a="领用工序",r={};if(this.itemOption=this.list.reduce((function(e,t){return r[t.Type]||"Direct"===t.Type||(e.push({code:t.Type,label:t.Type_Name+a,value:""}),n.$set(n.OwnerOption,t.Type,[])),r[t.Type]=!0,e}),[]),this.fetchData(),e&&t.length){var i=t[0],o=this.itemOption.find((function(e){return e.code===i.Type}));o&&(o.value=i.Part_Used_Process)}},getComOption:function(){var e=this,t={},n=[],a=function(t){return e.option.find((function(e){return e.Code===t}))};this.list.forEach((function(i){var o=i.Type;if(n.push(o),t[o]||(t[o]=[]),i.Component_Technology_Path=i.Component_Technology_Path||"",i.Scheduled_Used_Process){var s=a(i.Scheduled_Used_Process);s&&t[o].push(s)}else{var l,c=i.Component_Technology_Path.split("/").filter((function(e){return!!e}));if(c.length)c.forEach((function(e){var n=a(e);n&&t[o].push(n)}));else(l=t[o]).push.apply(l,(0,r.default)(e.option))}})),this.isInline?this.OwnerOption=t:n.forEach((function(t,n){e.OwnerOption[t]=e.option}))},fetchData:function(e,t){var n=this;this.loading=!0,(0,i.GetProcessListBase)({type:1}).then((function(e){e.IsSucceed?(n.option=e.Data,"part"===n.pageType&&n.getComOption()):n.$message({message:e.Message,type:"error"}),n.loading=!1}))},handleClose:function(){this.$emit("close")}}}},"694d":function(e,t,n){"use strict";n.r(t);var a=n("8e5d"),r=n("fad4");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("10b3");var o=n("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"6c8fede6",null);t["default"]=s.exports},"6f3e":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=i,n("a4d3"),n("e01a"),n("d28b"),n("d9e2"),n("d3b7"),n("3ca3"),n("ddb0");var a=r(n("53ca"));function r(e){return e&&e.__esModule?e:{default:e}}function i(e){if(null!=e){var t=e["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],n=0;if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}}}throw new TypeError((0,a.default)(e)+" is not iterable")}},"728a":function(e,t,n){"use strict";n("d08c")},"7de9":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=h,t.AddCheckItemCombination=_,t.AddCheckType=l,t.DelNode=k,t.DelQualityList=P,t.DeleteCheckItem=f,t.DeleteCheckType=c,t.EntityCheckItem=p,t.EntityCheckType=o,t.EntityQualityList=I,t.ExportInspsectionSummaryInfo=$,t.GetCheckGroupList=g,t.GetCheckItemList=d,t.GetCheckTypeList=s,t.GetCompTypeTree=T,t.GetDictionaryDetailListByCode=i,t.GetEntityNode=w,t.GetFactoryPeoplelist=C,t.GetFactoryProfessionalByCode=x,t.GetMaterialType=L,t.GetNodeList=S,t.GetProEntities=v,t.GetProcessCodeList=y,t.QualityList=b,t.SaveCheckItem=m,t.SaveCheckType=u,t.SaveNode=D;var r=a(n("b775"));function i(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},"7f56":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-container"},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[n("draggable",{attrs:{handle:".icon-drag"},on:{change:e.changeDraggable},model:{value:e.list,callback:function(t){e.list=t},expression:"list"}},[n("transition-group",e._l(e.list,(function(t,a){return n("el-row",{key:t.key},[n("el-col",{attrs:{span:1}},[n("i",{staticClass:"iconfont icon-drag cs-drag"})]),n("el-col",{attrs:{span:e.isCom?10:20}},[n("el-form-item",{attrs:{label:"排产工序"+(a+1)}},[n("el-select",{key:t.key,staticStyle:{width:"90%"},attrs:{disabled:t.isPart,placeholder:"请选择",clearable:""},on:{change:function(n){return e.selectChange(n,t)}},model:{value:t.value,callback:function(n){e.$set(t,"value",n)},expression:"element.value"}},e._l(e.options,(function(t){return n("el-option",{key:t.Code,attrs:{label:t.Name,disabled:t.disabled,value:t.Code}},[n("div",{staticClass:"cs-option"},[n("span",{staticClass:"cs-label"},[e._v(e._s(t.Name))]),t.Is_Nest&&e.isNest?n("span",{staticClass:"cs-tip"},[e._v("(套)")]):e._e()])])})),1)],1)],1),e.isCom?n("el-col",{attrs:{span:10}},[n("el-form-item",{attrs:{label:"要求完成时间"}},[n("el-date-picker",{key:t.key,staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},on:{change:function(n){return e.dateChange(n,t)}},model:{value:t.date,callback:function(n){e.$set(t,"date",n)},expression:"element.date"}})],1)],1):e._e(),n("el-col",{attrs:{span:3}},[n("span",{staticClass:"btn-x"},[0===a&&e.list.length<e.options.length?n("el-button",{attrs:{type:"primary",icon:"el-icon-plus",circle:""},on:{click:e.handleAdd}}):e._e(),0===a||t.isPart?e._e():n("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(n){return e.handleDelete(t)}}})],1)])],1)})),1)],1)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),e.list.length?n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")]):e._e()],1)],1)},r=[]},"82d3":function(e,t,n){"use strict";n("be94")},"8e5d":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"contentBox"},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"90px"}},[n("el-row",[n("el-col",{attrs:{span:7}},[n("el-form-item",{attrs:{label:"构件编号",prop:"Comp_Codes"}},[n("el-input",{staticClass:"input-with-select w100",attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.searchContent,callback:function(t){e.searchContent=t},expression:"searchContent"}},[n("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.curSearch,callback:function(t){e.curSearch=t},expression:"curSearch"}},[n("el-option",{attrs:{label:"精准查询",value:1}}),n("el-option",{attrs:{label:"模糊查询",value:0}})],1)],1)],1)],1),n("el-col",{attrs:{span:5}},[n("el-form-item",{attrs:{label:"构件类型",prop:"Type"}},[n("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",staticStyle:{width:"100%"},attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}})],1)],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"规格",prop:"Spec","label-width":"50px"}},[n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1),e.isVersionFour?n("el-col",{attrs:{span:3}},[n("el-form-item",{attrs:{label:"批次","label-width":"50px",prop:"Create_UserName"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",multiple:"",placeholder:"请选择"},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.installUnitIdList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),n("el-col",{attrs:{span:5}},[n("el-button",{staticStyle:{"margin-left":"10px"},on:{click:e.handleReset}},[e._v("重置")]),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.handleSearch()}}},[e._v("查询")]),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{loading:e.addLoading,type:"primary"},on:{click:function(t){return e.addToList()}}},[e._v("加入列表")])],1)],1)],1),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"","checkbox-config":{checkField:"checked",checkMethod:e.checkCheckboxMethod},loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},align:"left",stripe:"",data:e.fTable,resizable:"","edit-config":{trigger:"click",mode:"cell"},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Is_Component"===t.Code?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-tag",{attrs:{type:a.Is_Component?"danger":"success"}},[e._v(e._s(a.Is_Component?"否":"是"))])]}}],null,!0)}):["Part_Code","Comp_Code"].includes(t.Code)?n("vxe-column",{key:t.Code,attrs:{align:t.Align,field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.row;return[r.Is_Change?n("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("变")]):e._e(),r.stopFlag?n("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]}}],null,!0)}):n("vxe-column",{key:t.Code,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)],1),n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.totalSelection.length)+" 条数据 ")]),n("vxe-pager",{attrs:{border:"",background:"",loading:e.tbLoading,"current-page":e.pageInfo.page,"page-size":e.pageInfo.pageSize,"page-sizes":e.pageInfo.pageSizes,total:e.pageInfo.total,layouts:["PrevPage","JumpNumber","NextPage","FullJump","Sizes","Total"],size:"small"},on:{"update:currentPage":function(t){return e.$set(e.pageInfo,"page",t)},"update:current-page":function(t){return e.$set(e.pageInfo,"page",t)},"update:pageSize":function(t){return e.$set(e.pageInfo,"pageSize",t)},"update:page-size":function(t){return e.$set(e.pageInfo,"pageSize",t)},"page-change":e.handlePageChange}})],1),n("div",{staticClass:"button"},[n("el-button",{on:{click:e.handleClose}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",disabled:!e.totalSelection.length,loading:e.saveLoading},on:{click:function(t){return e.handleSave(2)}}},[e._v("保存")])],1)],1)},r=[]},"9a88":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("15fd"));n("4de4"),n("c740"),n("caad"),n("d81d"),n("14d9"),n("fb6a"),n("4e82"),n("a434"),n("e9f5"),n("d866"),n("910d"),n("7d54"),n("ab43"),n("a732"),n("a9e3"),n("d3b7"),n("ac1f"),n("25f0"),n("2532"),n("5319"),n("841c"),n("498a"),n("c7cd"),n("159b");var i=a(n("c14f")),o=a(n("1da1")),s=a(n("5530")),l=n("6186"),c=n("7f9d"),u=n("210d"),d=n("e144"),f=n("ed08"),h=n("c685"),p=n("fd31"),m=n("2a7f"),g=n("2f62"),_=n("3166"),v=["Comp_Codes"];t.default={props:{scheduleId:{type:String,default:""},pageType:{type:String,default:"com"},showDialog:{type:Boolean,default:!1},areaId:{type:String,default:""},installId:{type:String,default:""},currentIds:{type:String,default:""},isPartPrepare:{type:Boolean,default:!1}},data:function(){return{pageInfo:{page:1,pageSize:500,pageSizes:h.tablePageSize,total:0},form:{Comp_Code:"",Comp_CodeBlur:"",Part_CodeBlur:"",Part_Code:"",Type_Name:"",InstallUnit_Id:"",Spec:"",Type:""},searchContent:"",curSearch:1,isOwnerNull:!0,tbLoading:!1,addLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},TotalCount:0,Page:0,multipleSelection:[],installUnitIdList:[],totalSelection:[],search:function(){return{}},treeSelectParams:{placeholder:"请选择",clearable:!0},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},typeOption:[]}},computed:(0,s.default)({isCom:function(){return"com"===this.pageType}},(0,g.mapGetters)("tenant",["isVersionFour"])),watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.isCom?this.getObjectTypeList():this.getType(),this.search=(0,f.debounce)(this.fetchData,800,!0),this.getInstallUnitIdNameList()},methods:{getConfig:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return n="",n=e.isCom?"PROComDraftEditTbConfig":"PROPartDraftEditTbConfig_new",t.n=1,e.getTableConfig(n);case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},filterData:function(e){var t=this;1===this.curSearch&&(this.form.Comp_Code=this.searchContent,this.form.Comp_CodeBlur=""),0===this.curSearch&&(this.form.Comp_CodeBlur=this.searchContent,this.form.Comp_Code="");var n=[];for(var a in this.form)(this.form[a]||!1===this.form[a])&&n.push(a);if(!n.length)return this.setPage(),!e&&(this.pageInfo.page=1),void(this.pageInfo.total=this.tbData.length);var r=this.tbData.filter((function(e){e.checked=!1;var n=function(e){return e.trim().replace(/\s+/g," ").split(" ")};if(t.form.Comp_Code.trim()){var a=n(t.form.Comp_Code);if(!a.includes(e["Comp_Code"]))return!1}if(t.form.Comp_CodeBlur.trim()){var r=n(t.form.Comp_CodeBlur);if(!r.some((function(t){return e["Comp_Code"].includes(t)})))return!1}if(t.form.Type&&e.Type!==t.form.Type)return!1;if(t.form.Part_CodeBlur.trim()){var i=n(t.form.Part_CodeBlur);if(!i.some((function(t){return e["Part_Code"].includes(t)})))return!1}if(t.form.Part_Code.trim()){var o=n(t.form.Part_Code);if(!o.includes(e["Part_Code"]))return!1}if(t.isVersionFour&&t.form.InstallUnit_Id.length&&!t.form.InstallUnit_Id.includes(e.InstallUnit_Id))return!1;if(""!==t.form.Type_Name&&e.Type_Name!==t.form.Type_Name)return!1;if(""!==t.form.Spec.trim()){var s=n(t.form.Spec);if(!s.some((function(t){return e.Spec.includes(t)})))return!1}return!0}));!e&&(this.pageInfo.page=1),this.pageInfo.total=r.length,this.setPage(r)},handleSearch:function(){var e;this.totalSelection=[],this.clearSelect(),null!==(e=this.tbData)&&void 0!==e&&e.length&&(this.tbData.forEach((function(e){return e.checked=!1})),this.filterData())},handleReset:function(){this.form.Type_Name="",this.form.Comp_Code="",this.form.Comp_CodeBlur="",this.form.Type="",this.form.Spec="",this.searchContent="",this.handleSearch()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.totalSelection=this.tbData.filter((function(e){return e.checked}))},clearSelect:function(){this.$refs.xTable1.clearCheckboxRow(),this.totalSelection=[]},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(e.tbLoading=!0,!e.isCom){t.n=2;break}return t.n=1,e.getComTbData();case 1:t.n=3;break;case 2:return t.n=3,e.getPartTbData();case 3:e.initTbData(),e.filterData(),e.tbLoading=!1;case 4:return t.a(2)}}),t)})))()},setPageData:function(){var e;null!==(e=this.tbData)&&void 0!==e&&e.length&&(this.pageInfo.page=1,this.tbData=this.tbData.filter((function(e){return e.Can_Schduling_Count>0})),this.filterData())},handleSave:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2;1===t?this.addLoading=!0:this.saveLoading=!0,setTimeout((function(){e.totalSelection.forEach((function(e){var t=parseInt(e.count);e.Schduled_Count+=t,e.Can_Schduling_Count-=t,e.Can_Schduling_Weight=e.Can_Schduling_Count*e.Weight,e.maxCount=e.Can_Schduling_Count,e.chooseCount=t,e.count=e.Can_Schduling_Count,e.checked=!1}));var n=(0,f.deepClone)(e.totalSelection);e.$emit("sendSelectList",n),e.addLoading=!1,e.clearSelect(),e.setPageData(),2===t&&e.$emit("close")}),0)},initTbData:function(){var e,t=this,n={};if(null===(e=this.tbData)||void 0===e||!e.length)return this.tbData=[],void(this.backendTb=[]);this.tbData.forEach((function(e){t.$set(e,"count",e.Can_Schduling_Count),t.$set(e,"maxCount",e.Can_Schduling_Count),e.uuid=(0,d.v4)(),n[e.Type]=!0})),this.backendTb=(0,f.deepClone)(this.tbData)},getComTbData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n,a,o,l,u;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return n=e.form,a=n.Comp_Codes,o=(0,r.default)(n,v),l=[],"[object String]"===Object.prototype.toString.call(a)&&(l=a&&a.split(" ").filter((function(e){return!!e}))),t.n=1,(0,c.GetCanSchdulingComps)((0,s.default)((0,s.default)({Ids:e.currentIds},o),{},{Schduling_Plan_Id:e.scheduleId,Comp_Codes:l,InstallUnit_Id:e.installId,Area_Id:e.areaId})).then((function(t){t.IsSucceed?(e.pageInfo.total=t.Data.length,e.tbData=t.Data.map((function(e,t){return e.originalPath=e.Scheduled_Technology_Path?e.Scheduled_Technology_Path:"",e.Workshop_Id=e.Scheduled_Workshop_Id,e.Workshop_Name=e.Scheduled_Workshop_Name,e.Technology_Path=e.Scheduled_Technology_Path||e.Technology_Path,e.checked=!1,e.initRowIndex=t,e})),e.setPage()):e.$message({message:t.Message,type:"error"})}));case 1:return u=e.tbData.map((function(e){return{Id:e.Comp_Import_Detail_Id,Type:2}})),t.n=2,(0,c.GetStopList)(u).then((function(t){if(t.IsSucceed){var n={};t.Data.forEach((function(e){n[e.Id]=!!e.Is_Stop})),e.tbData.forEach((function(t){n.hasOwnProperty(t.Comp_Import_Detail_Id)&&e.$set(t,"stopFlag",n[t.Comp_Import_Detail_Id])}))}}));case 2:return t.a(2)}}),t)})))()},checkCheckboxMethod:function(e){var t=e.row;return!t.stopFlag},handlePageChange:function(e){var t=e.currentPage,n=e.pageSize;this.tbLoading||(this.pageInfo.page=t,this.pageInfo.pageSize=n,this.setPage(),this.filterData(t))},setPage:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.tbData;this.fTable=e.slice((this.pageInfo.page-1)*this.pageInfo.pageSize,this.pageInfo.page*this.pageInfo.pageSize)},getPartTbData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetCanSchdulingParts)((0,s.default)((0,s.default)({Ids:e.currentIds},e.form),{},{Schduling_Plan_Id:e.scheduleId,InstallUnit_Id:e.installId,Area_Id:e.areaId})).then((function(t){t.IsSucceed?(e.pageInfo.total=t.Data.length,e.tbData=t.Data.map((function(t,n){return t.originalPath=t.Scheduled_Technology_Path?t.Scheduled_Technology_Path:"",t.Workshop_Id=t.Scheduled_Workshop_Id,t.Workshop_Name=t.Scheduled_Workshop_Name,t.Comp_Import_Detail_Id&&(t.Part_Used_Process=e.getPartUsedProcess(t)),t.Technology_Path=t.Scheduled_Technology_Path||t.Technology_Path,t.checked=!1,t.initRowIndex=n,e.isPartPrepare||(t.Temp_Part_Used_Process=t.Part_Used_Process),t})),e.setPartColumn(),e.setPage()):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getPartUsedProcess:function(e){if(e.Scheduled_Used_Process)return e.Scheduled_Used_Process;if(e.Component_Technology_Path){var t=e.Component_Technology_Path.split("/");if(t.includes(e.Part_Used_Process))return e.Part_Used_Process;if(t.includes(e.Part_Type_Used_Process))return e.Part_Type_Used_Process}else{if(e.Part_Used_Process)return e.Part_Used_Process;if(e.Part_Type_Used_Process)return e.Part_Type_Used_Process}return""},setPartColumn:function(){if(this.isOwnerNull=this.tbData.every((function(e){return!e.Comp_Import_Detail_Id})),this.isOwnerNull){var e=this.columns.findIndex((function(e){return"Component_Code"===e.Code}));-1!==e&&this.columns.splice(e,1)}},mergeData:function(e){var t=this;e.forEach((function(e){var n=t.backendTb.findIndex((function(t){return e.puuid&&t.uuid===e.puuid}));-1!==n&&t.tbData.splice(n,0,(0,f.deepClone)(t.backendTb[n]))})),this.tbData.sort((function(e,t){return e.initRowIndex-t.initRowIndex})),this.filterData()},handleClose:function(){this.$emit("close")},getTableConfig:function(e){var t=this;return(0,o.default)((0,i.default)().m((function n(){return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,l.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,a=e.Data,r=e.Message;if(n){t.tbConfig=Object.assign({},t.tbConfig,a.Grid),t.pageInfo.pageSize=Number(t.tbConfig.Row_Number);var i=a.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return e.Is_Frozen&&(e.fixed="left"),e}))}else t.$message({message:r,type:"error"})}));case 1:return n.a(2)}}),n)})))()},getObjectTypeList:function(){var e=this;(0,p.GetCompTypeTree)({professional:"Steel"}).then((function(t){t.IsSucceed?(e.ObjectTypeList.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelectObjectType.treeDataUpdateFun(t.Data)}))):e.$message({type:"error",message:t.Message})}))},getType:function(){var e=this;(0,m.GetPartTypeList)({}).then((function(t){t.IsSucceed?e.typeOption=t.Data:e.$message({message:t.Message,type:"error"})}))},addToList:function(){this.totalSelection.length&&this.handleSave(1)},getInstallUnitIdNameList:function(e){var t=this;this.areaId?(0,_.GetInstallUnitIdNameList)({Area_Id:this.areaId}).then((function(e){t.installUnitIdList=e.Data||[]})):this.installUnitIdList=[]}}}},a257:function(e,t,n){"use strict";n.r(t);var a=n("f533"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},a418:function(e,t,n){"use strict";n.r(t);var a=n("7f56"),r=n("d2d7");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("3378");var o=n("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"308a0f14",null);t["default"]=s.exports},adf6:function(e,t,n){"use strict";n.r(t);var a=n("0714"),r=n("b8f8");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("5750");var o=n("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"cceb300c",null);t["default"]=s.exports},b8f8:function(e,t,n){"use strict";n.r(t);var a=n("45cd"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},be94:function(e,t,n){},ca6a:function(e,t,n){"use strict";n.r(t);var a=n("635e"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},d08c:function(e,t,n){},d174:function(e,t,n){"use strict";n.r(t);var a=n("626d"),r=n("a257");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("82d3");var o=n("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"7c391b0a",null);t["default"]=s.exports},d211:function(e,t,n){},d2d7:function(e,t,n){"use strict";n.r(t);var a=n("0cdf"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},d665:function(e,t,n){},e41b:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=u,t.GetPartsList=s,t.GetProjectAreaTreeList=i,t.ImportParts=c,t.SaveProjectAreaSort=o;var r=a(n("b775"));function i(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},e9f55:function(e,t,n){},f533:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("4de4"),n("7db0"),n("c740"),n("a630"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("4e82"),n("a434"),n("e9f5"),n("d866"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("a732"),n("e9c4"),n("b64b"),n("d3b7"),n("ac1f"),n("25f0"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("8a79"),n("2532"),n("3ca3"),n("5319"),n("841c"),n("1276"),n("2ca0"),n("498a"),n("159b"),n("ddb0");var r=a(n("ade3")),i=a(n("6f3e")),o=a(n("3056")),s=a(n("2909")),l=a(n("c14f")),c=a(n("1da1")),u=a(n("5530")),d=n("ed08"),f=a(n("a418")),h=n("7f9d"),p=a(n("694d")),m=a(n("2ce2")),g=a(n("adf6")),_=n("6186"),v=n("96a3"),C=n("e144"),y=a(n("6612")),b=n("a024"),I=n("66f9"),P=n("2f62"),S=n("2a7f"),w=a(n("c1df")),k=a(n("bae6")),D=a(n("1463")),T=n("3166"),x=n("7de9"),L=n("0e9a"),$=a(n("a657")),O="$_$";t.default={components:{DynamicTableFields:$.default,TreeDetail:D.default,ExpandableSection:k.default,BatchProcessAdjust:f.default,AddDraft:p.default,Workshop:g.default,OwnerProcess:m.default},data:function(){return{isComponentOptions:[{label:"是",value:!1},{label:"否",value:!0}],specOptions:[{data:""}],filterTypeOption:[{data:""}],filterCodeOption:[{data:""}],pickerOptions:{disabledDate:function(e){}},innerForm:{searchContent:"",searchComTypeSearch:"",searchSpecSearch:"",searchDirect:""},curSearch:1,searchType:"",formInline:{Schduling_Code:"",Create_UserName:"",Finish_Date:"",InstallUnit_Id:"",Remark:""},total:0,currentIds:"",gridCode:"",columns:[],tbData:[],tbConfig:{},TotalCount:0,multipleSelection:[],showExpand:!0,pgLoading:!1,deleteLoading:!1,workShopIsOpen:!1,isOwnerNull:!1,dialogVisible:!1,openAddDraft:!1,saveLoading:!1,tbLoading:!1,isCheckAll:!1,currentComponent:"",dWidth:"25%",title:"",tbKey:100,search:function(){return{}},pageType:void 0,tipLabel:"",technologyOption:[],typeOption:[],workingTeam:[],pageStatus:void 0,scheduleId:"",partComOwnerColumn:null,installUnitIdList:[],projectId:"",areaId:"",projectName:"",statusType:"可排产",expandedKey:"",treeLoading:!1,treeData:[],treeParamsComponentType:{"default-expand-all":!0,"check-strictly":!0,filterable:!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},treeSelectParams:{placeholder:"请选择",collapseTags:!0,clearable:!0},disabledAdd:!0}},watch:{"tbData.length":{handler:function(e,t){this.checkOwner()},immediate:!1}},computed:(0,u.default)((0,u.default)((0,u.default)({isCom:function(){return"com"===this.pageType},isView:function(){return"view"===this.pageStatus},isEdit:function(){return"edit"===this.pageStatus},isAdd:function(){return"add"===this.pageStatus},addDraftKey:function(){return this.expandedKey+this.formInline.InstallUnit_Id},filterText:function(){return this.projectName+O+this.statusType},statusCode:function(){return this.isCom?"Comp_Schdule_Status":"Part_Schdule_Status"},installName:function(){var e=this,t=this.installUnitIdList.find((function(t){return t.Id===e.formInline.InstallUnit_Id}));return t?t.Name:""},isPartPrepare:function(){return this.getIsPartPrepare&&!this.isCom},isNest:function(){return"1"===this.$route.query.type}},(0,P.mapGetters)("factoryInfo",["workshopEnabled","getIsPartPrepare"])),(0,P.mapGetters)("schedule",["processList","nestIds"])),(0,P.mapGetters)("tenant",["isVersionFour"])),beforeRouteEnter:function(e,t,n){"view"===e.query.status?e.meta.title="查看":e.meta.title="草稿",n()},mounted:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){var n,a,r;return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:return e.initProcessList(),e.tbDataMap={},e.craftCodeMap={},e.pageType=e.$route.query.pg_type,e.pageStatus=e.$route.query.status,e.model=e.$route.query.model,e.scheduleId=e.$route.query.pid||"",e.formInline.Create_UserName=localStorage.getItem("UserAccount"),e.unique=(0,v.uniqueCode)(),e.checkWorkshopIsOpen(),e.search=(0,d.debounce)(e.fetchData,800,!0),t.n=1,e.mergeConfig();case 1:(e.isView||e.isEdit)&&(n=e.$route.query,a=n.areaId,r=n.install,e.areaId=a,e.formInline.InstallUnit_Id=r,e.getInstallUnitIdNameList(),e.fetchData()),e.isAdd&&(e.fetchTreeData(),e.getType()),e.isEdit&&e.getType();case 2:return t.a(2)}}),t)})))()},methods:(0,u.default)((0,u.default)({},(0,P.mapActions)("schedule",["changeProcessList","initProcessList"])),{},{checkOwner:function(){if(!this.isCom){this.isOwnerNull=this.tbData.every((function(e){return!e.Comp_Import_Detail_Id}))&&!this.isNest;var e=this.columns.findIndex((function(e){return"Part_Used_Process"===e.Code}));if(this.isOwnerNull)-1!==e&&this.columns.splice(e,1);else{if(-1===e){if(!this.ownerColumn)return void this.$message({message:"列表配置字段缺少零件领用工序字段",type:"success"});this.columns.push(this.ownerColumn)}this.comPart=!0}}},mergeConfig:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getConfig();case 1:return t.n=2,e.getWorkTeam();case 2:return t.a(2)}}),t)})))()},getConfig:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){var n;return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:return n="",n=e.isNest?e.isView?"PRONestingScheduleDetail":"PRONestingScheduleConfig":e.isView?"PROComViewPageTbConfig":"PROComDraftPageTbConfig",e.gridCode=n,t.n=1,e.getTableConfig(n);case 1:e.workshopEnabled||(e.columns=e.columns.filter((function(e){return"Workshop_Name"!==e.Code}))),e.isVersionFour||(e.columns=e.columns.filter((function(e){return"Technology_Code"!==e.Code}))),e.checkOwner();case 2:return t.a(2)}}),t)})))()},changeColumn:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:e.tbKey++;case 2:return t.a(2)}}),t)})))()},handleNodeClick:function(e){var t,n=this;if(this.expandedKey=e.Id,this.areaId!==e.Id){if(this.disabledAdd=!0,e.ParentNodes&&!((null===(t=e.Children)||void 0===t?void 0:t.length)>0))if("未导入"!==(null===e||void 0===e?void 0:e.Data[this.statusCode])){var a=function(e){var t=e.Data;n.areaId=t.Id,n.projectId=t.Project_Id,n.expandedKey=n.areaId,n.formInline.Finish_Date="",n.formInline.InstallUnit_Id="",n.formInline.Remark="",n.tbData=[],n.getAreaInfo(),n.getInstallUnitIdNameList()};this.tbData.length?this.$confirm("切换区域右侧数据清空，是否确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a(e),n.disabledAdd=!1,n.tbDataMap={}})).catch((function(){n.$message({type:"info",message:"已取消"})})):(this.disabledAdd=!1,a(e))}else this.$message({message:"清单未导入，请联系深化人员导入清单",type:"warning"})}else this.disabledAdd=!1},customFilterFun:function(e,t,n){var a=e.split(O),r=a[0],i=a[1];if(!e)return!0;var o=n.parent,l=[n.label],c=[t.Data[this.statusCode]],u=1;while(u<n.level)l=[].concat((0,s.default)(l),[o.label]),c=[].concat((0,s.default)(c),[t.Data[this.statusCode]]),o=o.parent,u++;l=l.filter((function(e){return!!e})),c=c.filter((function(e){return!!e}));var d=!0,f=!0;return this.statusType&&(f=c.some((function(e){return-1!==e.indexOf(i)}))),this.projectName&&(d=l.some((function(e){return-1!==e.indexOf(r)}))),d&&f},fetchData:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){var n;return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:if(e.tbLoading=!0,n=null,!e.isNest){t.n=5;break}if(!e.isView){t.n=2;break}return t.n=1,e.getPartPageList();case 1:n=t.v,t.n=4;break;case 2:return t.n=3,e.getNestPageList();case 3:n=t.v;case 4:t.n=9;break;case 5:if(!e.isCom){t.n=7;break}return t.n=6,e.getComPageList();case 6:n=t.v,t.n=9;break;case 7:return t.n=8,e.getPartPageList();case 8:n=t.v;case 9:e.initTbData(n),e.tbLoading=!1;case 10:return t.a(2)}}),t)})))()},fetchTreeDataLocal:function(){},fetchTreeStatus:function(){},fetchTreeData:function(){var e=this;this.treeLoading=!0,(0,T.GetProjectAreaTreeList)({MenuId:this.$route.meta.Id,projectName:this.projectName,Type:this.isCom?1:2}).then((function(t){if(!t.IsSucceed)return e.$message({message:t.Message,type:"error"}),e.treeData=[],void(e.treeLoading=!1);if(0===t.Data.length)return e.treeLoading=!1,void(e.treeData=[]);var n=t.Data.map((function(e){return e.Is_Directory=!0,e}));e.treeData=n,e.$nextTick((function(t){e.$refs.tree.filterRef(e.filterText);var n=e.setKey();n||(e.pgLoading=!1)})),e.treeLoading=!1})).catch((function(t){e.treeLoading=!1,e.treeData=[]}))},setKey:function(){var e=this,t=function(a){for(var r=0;r<a.length;r++){var i=a[r],o=i.Data,s=i.Children,l=n(o.Id);if(o.ParentId&&(null===s||void 0===s||!s.length)&&l.visible)return e.handleNodeClick(i),!0;if(null!==s&&void 0!==s&&s.length){var c=t(s);if(c)return!0}}return!1},n=function(t){return e.$refs["tree"].getNodeByKey(t)};return t(this.treeData)},closeView:function(){(0,d.closeTagView)(this.$store,this.$route)},checkWorkshopIsOpen:function(){this.workShopIsOpen=!0},tbSelectChange:function(e){this.multipleSelection=e.records},getAreaInfo:function(){var e=this;this.formInline.Finish_Date="",(0,I.AreaGetEntity)({id:this.areaId}).then((function(t){if(t.IsSucceed){var n,a;if(!t.Data)return[];var r=(0,w.default)(null===(n=t.Data)||void 0===n?void 0:n.Demand_Begin_Date),i=(0,w.default)(null===(a=t.Data)||void 0===a?void 0:a.Demand_End_Date);e.pickerOptions.disabledDate=function(e){return e.getTime()<r||e.getTime()>i}}else e.$message({message:t.Message,type:"error"})}))},handleClose:function(){this.dialogVisible=!1,this.openAddDraft=!1},getNestPageList:function(){var e=this;return new Promise((function(t,n){(0,h.GetCanSchdulingPartList)({Ids:e.nestIds}).then((function(a){if(a.IsSucceed){var r=(null===a||void 0===a?void 0:a.Data)||[],i=r.map((function(e){return e.Scheduled_Used_Process&&(e.Part_Used_Process=e.Scheduled_Used_Process),e.Workshop_Id=e.Scheduled_Workshop_Id,e.Workshop_Name=e.Scheduled_Workshop_Name,e.Technology_Path=e.Scheduled_Technology_Path||e.Technology_Path,e.chooseCount=e.Can_Schduling_Count,e}));t(i)}else e.$message({message:a.Message,type:"error"}),n()}))}))},getComPageList:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){var n,a;return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:return n=e.$route.query.pid,t.n=1,(0,h.GetCompSchdulingInfoDetail)({Schduling_Plan_Id:n}).then((function(t){if(t.IsSucceed){var n=t.Data,a=n.Schduling_Plan,r=n.Schduling_Comps,i=n.Process_List;e.formInline=Object.assign(e.formInline,a),i.forEach((function(t){var n={key:t.Process_Code,value:t};e.changeProcessList(n)}));var o=r.map((function(e){return e.chooseCount=e.Can_Schduling_Count,e}));return e.getStopList(o),o||[]}e.$message({message:t.Message,type:"error"})}));case 1:return a=t.v,t.a(2,a)}}),t)})))()},getStopList:function(e){var t=this;return(0,c.default)((0,l.default)().m((function n(){var a;return(0,l.default)().w((function(n){while(1)switch(n.n){case 0:return a=e.map((function(e){return{Id:e.Comp_Import_Detail_Id,Type:2}})),n.n=1,(0,h.GetStopList)(a).then((function(n){if(n.IsSucceed){var a={};n.Data.forEach((function(e){a[e.Id]=!!e.Is_Stop})),e.forEach((function(e){a[e.Comp_Import_Detail_Id]&&t.$set(e,"stopFlag",a[e.Comp_Import_Detail_Id])}))}}));case 1:return n.a(2)}}),n)})))()},getPartPageList:function(){var e=this;return new Promise((function(t,n){var a=e.$route.query.pid;(0,h.GetPartSchdulingInfoDetail)({Schduling_Plan_Id:a}).then((function(a){if(a.IsSucceed){var r,i,o=null===(r=a.Data)||void 0===r?void 0:r.SarePartsModel.map((function(e){return e.Scheduled_Used_Process&&(e.Part_Used_Process=e.Scheduled_Used_Process),e.chooseCount=e.Can_Schduling_Count,e}));e.formInline=Object.assign(e.formInline,null===(i=a.Data)||void 0===i?void 0:i.Schduling_Plan),t(o||[])}else e.$message({message:a.Message,type:"error"}),n()}))}))},initTbData:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Allocation_Teams";this.tbData=e.map((function(e){var a,r=(null===(a=e.Technology_Path)||void 0===a?void 0:a.split("/"))||[];if(e.uuid=(0,C.v4)(),t.addElementToTbData(e),e[n]){var i=e[n].filter((function(e){return-1!==r.findIndex((function(t){return e.Process_Code===t}))}));i.forEach((function(n,a){var r=t.getRowUnique(e.uuid,n.Process_Code,n.Working_Team_Id),i=t.getRowUniqueMax(e.uuid,n.Process_Code,n.Working_Team_Id);e[r]=n.Count,e[i]=0}))}return t.setInputMax(e),e}));var a="";a=this.isCom?this.tbData.map((function(e){return e.Comp_Import_Detail_Id})).toString():this.tbData.map((function(e){return e.Part_Aggregate_Id})).toString(),this.currentIds=a},mergeSelectList:function(e){var t=this;return(0,c.default)((0,l.default)().m((function n(){var a;return(0,l.default)().w((function(n){while(1)switch(n.n){case 0:if(!t.isVersionFour){n.n=1;break}return n.n=1,t.mergeCraftProcess(e);case 1:a=!0,e.forEach((function(e,n){var r=t.getMergeUniqueRow(e);if(!r){if(e.puuid=e.uuid,e.Schduled_Count=e.chooseCount,e.Schduled_Weight=(0,y.default)(e.chooseCount*e.Weight).format("0.[00]"),t.isVersionFour&&!e.Technology_Path&&t.craftCodeMap[e.Technology_Code]instanceof Array){var i=t.craftCodeMap[e.Technology_Code];if(e.Part_Used_Process){var o=e.Part_Used_Process.split(","),s=o.every((function(e){return i.includes(e)}));s?e.Technology_Path=i.join("/"):a=!1}else e.Technology_Path=i.join("/")}return t.tbData.push(e),void t.addElementToTbData(e)}r.puuid=e.uuid,r.Schduled_Count+=e.chooseCount,r.Schduled_Weight=(0,y.default)(r.Schduled_Weight).add(e.chooseCount*e.Weight).format("0.[00]"),r.Technology_Path&&t.setInputMax(r)})),t.showCraftUsedPartResult(a),t.tbData.sort((function(e,t){return e.initRowIndex-t.initRowIndex}));case 2:return n.a(2)}}),n)})))()},addElementToTbData:function(e){var t=this.getUniKey(e);this.tbDataMap[t]=e},getMergeUniqueRow:function(e){var t=this.getUniKey(e);return this.tbDataMap[t]},getUniKey:function(e){var t,n;return this.isVersionFour?this.isCom?(e.Comp_Code+e.InstallUnit_Name).toString().trim():(null!==(t=e.Component_Code)&&void 0!==t?t:"")+e.Part_Code+e.Part_Aggregate_Id:this.isCom?e.Comp_Code:(null!==(n=e.Component_Code)&&void 0!==n?n:"")+e.Part_Code+e.Part_Aggregate_Id},checkForm:function(){var e=!0;return this.$refs["formInline"].validate((function(t){t||(e=!1)})),e},saveDraft:function(){var e=arguments,t=this;return(0,c.default)((0,l.default)().m((function n(){var a,r,i,o,s,c,u;return(0,l.default)().w((function(n){while(1)switch(n.n){case 0:if(r=e.length>0&&void 0!==e[0]&&e[0],i=t.checkForm(),i){n.n=1;break}return n.a(2,!1);case 1:if(o=t.getSubmitTbInfo(),s=o.tableData,c=o.status,c){n.n=2;break}return n.a(2,!1);case 2:return r||(t.saveLoading=!0),n.n=3,t.handleSaveDraft(s,r);case 3:if(u=n.v,u){n.n=4;break}return n.a(2,!1);case 4:if(!r){n.n=5;break}return n.a(2,u);case 5:null===(a=t.$refs["draft"])||void 0===a||a.fetchData(),t.saveLoading=!1;case 6:return n.a(2)}}),n)})))()},saveWorkShop:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){var n,a,r;return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:if(n=e.checkForm(),n){t.n=1;break}return t.a(2,!1);case 1:if(a={},e.tbData.length){t.n=2;break}return e.$message({message:"数据不能为空",type:"success"}),t.a(2);case 2:e.isCom?a.Schduling_Comps=e.tbData:a.SarePartsModel=e.tbData,e.isEdit?a.Schduling_Plan=e.formInline:a.Schduling_Plan=(0,u.default)((0,u.default)({},e.formInline),{},{Project_Id:e.projectId,Area_Id:e.areaId,Schduling_Model:e.model}),e.pgLoading=!0,r=e.isCom?h.SaveComponentSchedulingWorkshop:h.SavePartSchedulingWorkshopNew,r(a).then((function(t){t.IsSucceed?(e.pgLoading=!1,e.$message({message:"保存成功",type:"success"}),e.closeView()):(e.$message({message:t.Message,type:"error"}),e.pgLoading=!1)}));case 3:return t.a(2)}}),t)})))()},getSubmitTbInfo:function(){var e=this,t=JSON.parse(JSON.stringify(this.tbData));t=t.filter((function(e){return e.Schduled_Count>0}));for(var n,a=function(){var n=t[r],a=[];if(!n.Technology_Path)return e.$message({message:"工序不能为空",type:"warning"}),{v:{status:!1}};if(e.isPartPrepare&&!n.Part_Used_Process&&"Direct"!==n.Type&&e.comPart){var i="领用工序不能为空";if(!e.isNest)return e.$message({message:i,type:"warning"}),{v:{status:!1}};if(n.Comp_Import_Detail_Id)return e.$message({message:i,type:"warning"}),{v:{status:!1}}}if(n.Scheduled_Technology_Path&&n.Scheduled_Technology_Path!==n.Technology_Path)return e.$message({message:"请和该区域批次下已排产同".concat(e.isCom?"构件":"零件","保持工序一致"),type:"warning"}),{v:{status:!1}};if(n.Scheduled_Used_Process&&n.Scheduled_Used_Process!==n.Part_Used_Process)return e.$message({message:"请和该区域批次下已排产同零件领用工序保持一致",type:"warning"}),{v:{status:!1}};for(var o=Array.from(new Set(n.Technology_Path.split("/"))),l=function(){var e=o[c],t=n.Schduled_Count||0,r=[];n.Allocation_Teams&&(r=n.Allocation_Teams.filter((function(t){return t.Process_Code===e})));var i,l=r.reduce((function(e,t){return e+(t.Again_Count||0)}),0);if(l>t)return a=[],1;(i=a).push.apply(i,(0,s.default)(r))},c=0;c<o.length;c++)if(l())break;var u=Object.keys(n).filter((function(e){return e.startsWith(n["uuid"])}));u.forEach((function(e){delete n[e]})),delete n["uuid"],delete n["_X_ROW_KEY"],delete n["puuid"],n.Allocation_Teams=a},r=0;r<t.length;r++)if(n=a(),n)return n.v;return{tableData:t,status:!0}},handleSaveDraft:function(e,t){var n=this;return(0,c.default)((0,l.default)().m((function a(){var r,i,o,s,c;return(0,l.default)().w((function(a){while(1)switch(a.n){case 0:if(r=n.isCom?h.SaveCompSchdulingDraft:h.SavePartSchdulingDraftNew,i={},n.isCom){for(s in i.Schduling_Comps=e,o=[],n.processList)n.processList.hasOwnProperty(s)&&o.push(n.processList[s]);i.Process_List=o}else i.SarePartsModel=e;return n.isEdit?i.Schduling_Plan=n.formInline:i.Schduling_Plan=(0,u.default)((0,u.default)({},n.formInline),{},{Project_Id:n.projectId,Area_Id:n.areaId,Schduling_Model:n.model}),c=!1,a.n=1,r(i).then((function(e){e.IsSucceed?t?(n.templateScheduleCode=e.Data,c=!0):(n.pgLoading=!1,n.$message({message:"保存成功",type:"success"}),n.closeView()):(n.saveLoading=!1,n.pgLoading=!1,n.$message({message:e.Message,type:"error"}))}));case 1:return a.a(2,c)}}),a)})))()},handleDelete:function(){var e=this;this.deleteLoading=!0,setTimeout((function(){var t=new Set(e.multipleSelection.map((function(e){return e.uuid})));e.tbData=e.tbData.filter((function(n){var a=t.has(n.uuid);if(a){var r=e.getUniKey(n);delete e.tbDataMap[r]}return!a})),e.$nextTick((function(t){var n;null===(n=e.$refs["draft"])||void 0===n||n.mergeData(e.multipleSelection),e.multipleSelection=[]})),e.deleteLoading=!1}),0)},getWorkTeam:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,h.GetSchdulingWorkingTeams)({type:e.isCom?1:2}).then((function(t){t.IsSucceed?e.workingTeam=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},handleSubmit:function(){var e=this;this.$refs["formInline"].validate((function(t){if(t){var n=e.getSubmitTbInfo(),a=n.tableData,r=n.status;r&&e.$confirm("是否提交当前数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveDraftDoSubmit(a)})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}))},saveDraftDoSubmit:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){var n,a,r;return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:if(e.pgLoading=!0,null===(n=e.formInline)||void 0===n||!n.Schduling_Code){t.n=2;break}return t.n=1,e.saveDraft(!0);case 1:a=t.v,a&&e.doSubmit(e.formInline.Id),t.n=4;break;case 2:return t.n=3,e.saveDraft(!0);case 3:r=t.v,r&&e.doSubmit(e.templateScheduleCode);case 4:return t.a(2)}}),t)})))()},doSubmit:function(e){var t=this;(0,h.SaveSchdulingTaskById)({schdulingPlanId:e}).then((function(e){e.IsSucceed?(t.$message({message:"下达成功",type:"success"}),t.closeView()):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.pgLoading=!1})).catch((function(e){t.pgLoading=!1}))},getWorkShop:function(e){var t=this;return(0,c.default)((0,l.default)().m((function n(){var a,r,i,o,s,c;return(0,l.default)().w((function(n){while(1)switch(n.n){case 0:a=e.origin,r=e.row,i=e.workShop,o=i.Id,s=i.Display_Name,2===a?null!==(c=e.workShop)&&void 0!==c&&c.Id?(r.Workshop_Name=s,r.Workshop_Id=o,t.setPath(r,o)):(r.Workshop_Name="",r.Workshop_Id=""):t.multipleSelection.forEach((function(n){var a;null!==(a=e.workShop)&&void 0!==a&&a.Id?(n.Workshop_Name=s,n.Workshop_Id=o,t.setPath(n,o)):(n.Workshop_Name="",n.Workshop_Id="")}));case 1:return n.a(2)}}),n)})))()},setPath:function(e,t){null!==e&&void 0!==e&&e.Scheduled_Workshop_Id?e.Scheduled_Workshop_Id!==t&&(e.Technology_Path=""):e.Technology_Path=""},handleBatchWorkshop:function(e,t){var n=this;this.title=1===e?"批量分配车间":"分配车间",this.currentComponent="Workshop",this.dWidth="30%",this.dialogVisible=!0,this.$nextTick((function(a){n.$refs["content"].fetchData(e,t,n.multipleSelection)}))},mergeCraftProcess:function(e){var t=this;return(0,c.default)((0,l.default)().m((function n(){var a,r,c,u,d,f;return(0,l.default)().w((function(n){while(1)switch(n.n){case 0:a=(0,s.default)(new Set(e.map((function(e){return e.Technology_Code})))),r=(0,l.default)().m((function e(n){return(0,l.default)().w((function(e){while(1)switch(e.n){case 0:t.craftCodeMap.hasOwnProperty(n)&&(a=a.filter((function(e){return e!==n})));case 1:return e.a(2)}}),e)})),d=(0,o.default)(t.craftCodeMap);case 1:if((f=d()).done){n.n=3;break}return c=f.value,n.d((0,i.default)(r(c)),2);case 2:n.n=1;break;case 3:return n.n=4,t.getCraftProcess(a);case 4:u=n.v,Object.assign(t.craftCodeMap,u);case 5:return n.a(2)}}),n)})))()},getCraftProcess:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t=t.filter((function(e){return!!e})),t.length?new Promise((function(n,a){(0,b.GetProcessFlowListWithTechnology)({TechnologyCodes:t,Type:1}).then((function(t){if(t.IsSucceed){var r=t.Data||[],i=r.reduce((function(e,t){return e[t.Code]=t.Technology_Path,e}),{});n(i)}else e.$message({message:t.Message,type:"error"}),a()}))})):Promise.resolve({})},handleAutoDeal:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:e.$confirm("是否将选中数据按构件类型自动分配","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(e.workshopEnabled){var t=e.multipleSelection.map((function(e){return{uniqueType:"".concat(e.Type,"$_$").concat(e.Workshop_Id)}})),n=Array.from(new Set(t.map((function(e){return e.uniqueType})))),a={};Promise.all(n.map((function(t){var n=t.split("$_$");return e.setLibType(n[0],n[1])}))).then((function(t){var r=t.some((function(e){return void 0==e}));r&&e.$message({message:"所选车间内工序班组与构件类型工序不匹配，请手动分配工序",type:"warning"}),t.forEach((function(e,t){a[n[t]]=e})),e.multipleSelection.forEach((function(t){t.Technology_Path=a["".concat(t.Type,"$_$").concat(t.Workshop_Id)],e.resetWorkTeamMax(t,t.Technology_Path)}))}))}else{var r=e.multipleSelection.map((function(e){return e.Type})),i=Array.from(new Set(r)),o={};Promise.all(i.map((function(t){return e.setLibType(t)}))).then((function(t){t.forEach((function(e,t){o[i[t]]=e})),e.multipleSelection.forEach((function(t){t.Technology_Path=o[t.Type],e.resetWorkTeamMax(t,t.Technology_Path)}))}))}})).catch((function(){e.$message({type:"info",message:"已取消"})}));case 1:return t.a(2)}}),t)})))()},getProcessOption:function(e){var t=this;return new Promise((function(n,a){(0,b.GetProcessListBase)({workshopId:e,type:1}).then((function(e){if(e.IsSucceed){var a=e.Data.map((function(e){return e.Code}));n(a)}else t.$message({message:e.Message,type:"error"})}))}))},setLibType:function(e,t){var n=this;return new Promise((function(a){var r={Component_type:e,type:1};n.workshopEnabled&&(r.workshopId=t),(0,b.GetLibListType)(r).then((function(e){if(e.IsSucceed)if(e.Data.Data&&e.Data.Data.length){var t=e.Data.Data[0],r=t.WorkCode&&t.WorkCode.replace(/\\/g,"/");a(r)}else a(void 0);else n.$message({message:e.Message,type:"error"})}))}))},inputChange:function(e){this.setInputMax(e)},setInputMax:function(e){var t=Object.keys(e).filter((function(t){return!t.endsWith("max")&&t.startsWith(e.uuid)&&t.length>e.uuid.length}));t.forEach((function(n){var a=n.split(O)[1],r=t.filter((function(e){var t=e.split(O)[1];return e!==n&&t===a})).reduce((function(t,n){return t+(0,y.default)(e[n]).value()}),0);e[n+O+"max"]=e.Schduled_Count-r}))},sendProcess:function(e){for(var t=e.arr,n=e.str,a=!0,r=0;r<t.length;r++){var i=t[r];if(i.originalPath&&i.originalPath!==n){a=!1;break}i.Technology_Path=n}a||this.$message({message:"请和该区域批次下已排产同构件保持工序一致",type:"warning"})},resetWorkTeamMax:function(e,t){var n,a=this;t?e.Technology_Path=t:t=e.Technology_Path;var r=(null===(n=t)||void 0===n?void 0:n.split("/"))||[];this.workingTeam.forEach((function(t,n){var i=r.some((function(e){return e===t.Process_Code})),o=a.getRowUnique(e.uuid,t.Process_Code,t.Working_Team_Id),s=a.getRowUniqueMax(e.uuid,t.Process_Code,t.Working_Team_Id);i?e[o]||(a.$set(e,o,0),a.$set(e,s,e.Schduled_Count)):(a.$delete(e,o),a.$delete(e,s))}))},checkPermissionTeam:function(e,t){if(!e)return!1;var n=(null===e||void 0===e?void 0:e.split("/"))||[];return!!n.some((function(e){return e===t}))},getTableConfig:function(e){var t=this;return(0,c.default)((0,l.default)().m((function n(){return(0,l.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,_.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,a=e.Data,r=e.Message;if(n){t.tbConfig=Object.assign({},t.tbConfig,a.Grid);var i=a.ColumnList||[];t.ownerColumn=i.find((function(e){return"Part_Used_Process"===e.Code})),t.ownerColumn2=i.find((function(e){return"Is_Main_Part"===e.Code})),t.columns=t.setColumnDisplay(i)}else t.$message({message:r,type:"error"})}));case 1:return n.a(2)}}),n)})))()},setColumnDisplay:function(e){return e.filter((function(e){return e.Is_Display}))},activeCellMethod:function(e){var t,n=e.row,a=e.column;e.columnIndex;if(this.isView)return!1;var r=null===(t=a.field)||void 0===t?void 0:t.split("$_$")[1];return this.checkPermissionTeam(n.Technology_Path,r)},openBPADialog:function(e,t){var n=this;if(this.workshopEnabled&&1===e){var a=this.checkIsUniqueWorkshop();if(!a)return}this.title=2===e?"工序调整":"批量工序调整",this.currentComponent="BatchProcessAdjust",this.dWidth=this.isCom?"60%":"35%",this.dialogVisible=!0,this.$nextTick((function(a){n.$refs["content"].setData(2===e?[t]:n.multipleSelection,2===e?t.Technology_Path:"")}))},checkIsUniqueWorkshop:function(){for(var e=!0,t=this.multipleSelection[0].Workshop_Name,n=1;n<this.multipleSelection.length;n++){var a=this.multipleSelection[n];if(a.Workshop_Name!==t){e=!1;break}}return e||this.$message({message:"批量分配工序时只有相同车间下的才可一起批量分配",type:"warning"}),e},checkHasWorkShop:function(e,t){for(var n=!0,a=0;a<t.length;a++){var r=t[a];if(!r.Workshop_Name){n=!1;break}}return n||this.$message({message:"请先选择车间后再进行工序分配",type:"warning"}),n},handleAddDialog:function(){var e=this;this.isCom?this.title="构件排产":this.title="添加零件",this.currentComponent="AddDraft",this.dWidth="80%",this.openAddDraft=!0,this.$nextTick((function(t){e.$refs["draft"].setPageData()}))},getRowUnique:function(e,t,n){return"".concat(e).concat(O).concat(t).concat(O).concat(n)},getRowUniqueMax:function(e,t,n){return this.getRowUnique(e,t,n)+"".concat(O,"max")},handleSelectMenu:function(e){var t=this;return(0,c.default)((0,l.default)().m((function n(){return(0,l.default)().w((function(n){while(1)switch(n.n){case 0:if("process"!==e){n.n=1;break}t.openBPADialog(1),n.n=4;break;case 1:if("deal"!==e){n.n=3;break}return n.n=2,t.handleAutoDeal(1);case 2:n.n=4;break;case 3:if("craft"!==e){n.n=4;break}return n.n=4,t.handleSetCraftProcess();case 4:return n.a(2)}}),n)})))()},handleSetCraftProcess:function(){var e=this;return(0,c.default)((0,l.default)().m((function t(){var n,a,i,o,c,u,d;return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:if(n=function(){e.$message({message:"已分配成功",type:"success"})},a=e.multipleSelection.map((function(e){return e.Technology_Code})).filter((function(e){return!!e})),a.length){t.n=1;break}return e.$message({message:"工艺代码不存在",type:"warning"}),t.a(2);case 1:return t.n=2,e.mergeCraftProcess(e.multipleSelection);case 2:i=Array.from(new Set(e.multipleSelection.map((function(e){return e.Workshop_Id})).filter((function(e){return!!e})))),o=[],i.length?(i.forEach((function(t){o.push(e.getProcessOption(t).then((function(e){return(0,r.default)({},t,e)})))})),c=Promise.all(o).then((function(e){return Object.assign.apply(Object,[{}].concat((0,s.default)(e)))})),c.then((function(t){for(var a=!0,r=!0,i=function(){var n=e.multipleSelection[o],i=t[n.Workshop_Id],s=e.craftCodeMap[n.Technology_Code];if(s){var l=s.every((function(e){return i.includes(e)}));if(!l)return a=!1,1;var c=e.checkHasCraftUsedPart(n,s);c?n.Technology_Path=s.join("/"):r=!1}},o=0;o<e.multipleSelection.length;o++)i();a||setTimeout((function(){e.$alert("所选车间下班组加工工序不包含工艺代码工序请手动排产","提示",{confirmButtonText:"确定"})}),200);var s=e.showCraftUsedPartResult(r);a&&s&&n()}))):(u=!0,e.multipleSelection.forEach((function(t){var n=e.craftCodeMap[t.Technology_Code];if(n){var a=e.checkHasCraftUsedPart(t,n);a?t.Technology_Path=n.join("/"):u=!1}})),d=e.showCraftUsedPartResult(u),d&&n());case 3:return t.a(2)}}),t)})))()},checkHasCraftUsedPart:function(e,t){return!(e.Part_Used_Process&&!t.includes(e.Part_Used_Process))},showCraftUsedPartResult:function(e){var t=this;return!!e||(setTimeout((function(){t.$alert("部分构件工序路径内不包含零件领用工序请手动排产","提示",{confirmButtonText:"确定"})}),200),!1)},handleBatchOwner:function(e,t){var n=this;this.title="批量分配领用工序",this.currentComponent="OwnerProcess",this.dWidth="30%",this.dialogVisible=!0,this.$nextTick((function(a){n.$refs["content"].setOption(2===e,2===e?[t]:n.multipleSelection)}))},handleReverse:function(){var e=[];this.tbData.forEach((function(t,n){t.checked=!t.checked,t.checked&&e.push(t)})),this.multipleSelection=e,this.multipleSelection.length===this.tbData.length&&this.$refs["xTable"].setAllCheckboxRow(!0),0===this.multipleSelection.length&&this.$refs["xTable"].setAllCheckboxRow(!1)},getType:function(){var e=this,t=function(){var t=e.isCom?x.GetCompTypeTree:S.GetPartTypeList;t({}).then((function(t){if(t.IsSucceed){var n=t.Data;e.isCom||(n=n.map((function(e,t){return{Data:e.Name,Label:e.Name}}))),e.treeParamsComponentType.data=n,e.$nextTick((function(t){var a;null===(a=e.$refs.treeSelectComponentType)||void 0===a||a.treeDataUpdateFun(n)}))}else e.$message({message:t.Message,type:"error"})}))};t()},handleDwg:function(e){var t=this,n={};this.isCom?n.Comp_Id=e.Comp_Import_Detail_Id:n.Part_Id=e.Part_Aggregate_Id,(0,h.GetDwg)(n).then((function(e){if(e.IsSucceed){var n,a=(null===e||void 0===e||null===(n=e.Data)||void 0===n?void 0:n.length)&&e.Data[0].File_Url;window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+(0,L.parseOssUrl)(a),"_blank")}else t.$message({message:e.Message,type:"error"})}))},setProcessList:function(e){this.changeProcessList(e)},resetInnerForm:function(){this.$refs["searchForm"].resetFields(),this.$refs.xTable.clearFilter()},innerFilter:function(){var e=this;this.multipleSelection=[];var t=[];this.isCom?t.push("Type","Comp_Code","Spec","Is_Component"):t.push("Part_Code","Spec","Type_Name");var n=this.$refs.xTable;n.clearCheckboxRow(),t.forEach((function(t,a){var r=n.getColumnByField(t);if("Is_Component"===t&&r.filters.forEach((function(t,n){t.checked=n===(e.innerForm.searchDirect?0:1)})),"Spec"===t){var i=r.filters[0];i.data=e.innerForm.searchSpecSearch,i.checked=!0}if("Type"===t||"Type_Name"===t){var o=r.filters[0];o.data=e.innerForm.searchComTypeSearch,o.checked=!0}if("Comp_Code"===t||"Part_Code"===t){var s=r.filters[0];s.data=e.innerForm.searchContent,s.checked=!0}})),n.updateData()},filterComponentMethod:function(e){e.option;var t=e.row;return""===this.innerForm.searchDirect||t.Is_Component===!this.innerForm.searchDirect},filterSpecMethod:function(e){e.option;var t=e.row;if(""===this.innerForm.searchSpecSearch.trim())return!0;var n=function(e){return e.trim().replace(/\s+/g," ").split(" ")},a=n(this.innerForm.searchSpecSearch);return a.some((function(e){return(t.Spec||"").includes(e)}))},filterTypeMethod:function(e){e.option;var t=e.row;if(""===this.innerForm.searchComTypeSearch)return!0;var n=this.isCom?"Type":"Type_Name";return t[n]===this.innerForm.searchComTypeSearch},filterCodeMethod:function(e){e.option;var t=e.row;if(""===this.innerForm.searchContent.trim())return!0;var n=function(e){return e.trim().replace(/\s+/g," ").split(" ")},a=this.isCom?"Comp_Code":"Part_Code",r=n(this.innerForm.searchContent);if(1===this.curSearch)return r.some((function(e){return t[a]===e}));for(var i=0;i<r.length;i++){var o=r[i];if(t[a].includes(o))return!0}return!1},componentTypeFilter:function(e){var t;null===(t=this.$refs)||void 0===t||t.treeSelectComponentType.filterFun(e)},getInstallUnitIdNameList:function(e){var t=this;!this.areaId||this.isVersionFour?(this.installUnitIdList=[],this.disabledAdd=!1):(this.disabledAdd=!0,(0,T.GetInstallUnitIdNameList)({Area_Id:this.areaId}).then((function(e){t.installUnitIdList=e.Data,t.installUnitIdList.length&&(t.formInline.InstallUnit_Id=t.installUnitIdList[0].Id),t.disabledAdd=!1})))},installChange:function(){var e=this;if(!this.tbData.length)return this.$refs["searchForm"].resetFields(),void this.$refs.xTable.clearFilter();this.$confirm("切换区域右侧数据清空, 是否确认?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.tbData=[],e.resetInnerForm()})).catch((function(){e.$message({type:"info",message:"已取消"})}))},showPartUsedProcess:function(e){return this.isNest?!!e.Comp_Import_Detail_Id:!this.isView&&"Direct"!==e.Type},handleExport:function(){if(this.tbData.length){var e=this.tbData[0];this.$refs.xTable.exportData({filename:"构件排产-".concat(e.Project_Name,"-").concat(e.Area_Name,"-").concat(this.formInline.Schduling_Code,"(构件)"),type:"xlsx",data:this.tbData})}else this.$message({message:"暂无数据",type:"warning"})}})}},fad4:function(e,t,n){"use strict";n.r(t);var a=n("9a88"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a}}]);