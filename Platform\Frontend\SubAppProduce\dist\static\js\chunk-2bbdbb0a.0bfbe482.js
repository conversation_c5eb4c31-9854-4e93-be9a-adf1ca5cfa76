(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2bbdbb0a"],{"336a":function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var u=a(r("7485"));e.default={name:"PROAuxGoodsReturnView",components:{ViewPage:u.default},data:function(){return{}}}},5480:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getTableConfig=e.getRoleInfo=void 0,r("4de4"),r("d81d"),r("e9f5"),r("910d"),r("ab43"),r("d3b7");var a=r("6186"),u=r("c24f"),o=void 0;e.getTableConfig=function(t,e){return new Promise((function(r,u){(0,a.GetGridByCode)({code:t,businessType:e}).then((function(t){var e=t.IsSucceed,a=t.Data,u=t.Message;if(e){var n=(a.ColumnList||[]).filter((function(t){return t.Is_Display}));r(n)}else o.$message({message:u,type:"error"})}))}))},e.getRoleInfo=function(t){return new Promise((function(e,r){(0,u.RoleAuthorization)({roleType:3,menuType:1,menuId:t}).then((function(t){if(t.IsSucceed){var a=((null===t||void 0===t?void 0:t.Data)||[]).map((function(t){return t.Code}));e(a)}else o.$message({message:t.Message,type:"error"}),r(t.message)}))}))}},"8fea":function(t,e){t.exports={WEIGHT_CONVERSION:1e3,WEIGHT_DECIMAL:5,WEIGHT_KG_DECIMAL:2,COUNT_DECIMAL:2,UNIT_PRICE_DECIMAL:6,UNIT_PRICE_KG_DECIMAL:9,RawInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],RawReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],RawAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],RawOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:3}],RawAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}],AuxInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],AuxReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],AuxAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],AuxOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:2}],AuxAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}]}},"93aa":function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=E,e.AuxInStoreExport=q,e.AuxReturnByReceipt=Y,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=W,e.DeleteInStore=I,e.DeletePicking=gt,e.ExportCheckReceipt=ct,e.ExportInstoreReceipt=ft,e.ExportMoneyAdjustOrder=yt,e.ExportPicking=Gt,e.ExportProcess=Ut,e.ExportTestDetail=wt,e.FindAuxPageList=F,e.FindRawPageList=T,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=X,e.GetAuxImportTemplate=v,e.GetAuxPageList=rt,e.GetAuxPickOutStoreSubList=U,e.GetAuxProcurementDetails=at,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=O,e.GetImportTemplate=L,e.GetInstoreDetail=R,e.GetMoneyAdjustDetailPageList=At,e.GetOMALatestStatisticTime=k,e.GetOrderDetail=nt,e.GetPartyAs=M,e.GetPickLockStoreToChuku=vt,e.GetPickPlate=Et,e.GetPickSelectPageList=Ct,e.GetPickSelectSubList=Vt,e.GetPickingDetail=_t,e.GetPickingTypeSettingDetail=Nt,e.GetProjectListForTenant=ut,e.GetRawDetailByReceipt=it,e.GetRawOrderList=ot,e.GetRawPageList=w,e.GetRawPickOutStoreSubList=V,e.GetRawProcurementDetails=A,e.GetRawSurplusReturnStoreDetail=C,e.GetReturnPlate=jt,e.GetStoreSelectPage=kt,e.GetSuppliers=h,e.GetTestDetail=ht,e.GetTestInStoreOrderList=Rt,e.Import=_,e.ImportCheckReceipt=mt,e.ImportInstoreReceipt=pt,e.InStoreListSummary=dt,e.LockPicking=Tt,e.ManualAuxInStoreDetail=J,e.ManualInStoreDetail=S,e.MaterielAuxInStoreList=j,e.MaterielAuxManualInStore=z,e.MaterielAuxPurchaseInStore=K,e.MaterielAuxSubmitInStore=B,e.MaterielPartyAInStorel=Q,e.MaterielRawInStoreList=o,e.MaterielRawInStoreListInSumNew=i,e.MaterielRawInStoreListNew=n,e.MaterielRawManualInStore=G,e.MaterielRawPartyAInStore=D,e.MaterielRawPurchaseInStore=y,e.MaterielRawSubmitInStore=l,e.MaterielRawSurplusInStore=x,e.OutStoreListSummary=st,e.PartAInStoreDetail=m,e.PartyAInInStoreDetail=$,e.PurchaseAuxInStoreDetail=H,e.PurchaseInStoreDetail=p,e.RawInStoreExport=N,e.RawReturnByReceipt=lt,e.RawSurplusReturnStore=b,e.SaveInStore=g,e.SavePicking=Lt,e.SetQualified=Mt,e.SetTestDetail=Ot,e.StoreMoneyAdjust=Pt,e.SubmitApproval=c,e.SubmitAuxApproval=f,e.SubmitInStore=St,e.SubmitPicking=xt,e.SurplusInStoreDetail=P,e.UnLockPicking=bt,e.UpdateInvoiceInfo=Dt,e.Withdraw=d,e.WithdrawAux=s,e.WithdrawChecked=It;var u=a(r("b775"));function o(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function n(t){return(0,u.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function i(t){return(0,u.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function l(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function d(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function s(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function f(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function c(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function p(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function m(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function S(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function P(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function R(t){return(0,u.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function I(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function h(t){return(0,u.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function M(t){return(0,u.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function O(t){return(0,u.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function w(t){return(0,u.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function A(t){return(0,u.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function y(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function D(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function G(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function x(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function g(t){return(0,u.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function k(t){return(0,u.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function L(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function _(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function N(t){return(0,u.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function T(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function b(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function C(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function V(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function v(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function E(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function j(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function U(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function B(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function W(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function F(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function H(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function $(t){return(0,u.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function J(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function K(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function Q(t){return(0,u.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function z(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function q(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function X(t){return(0,u.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function Y(t){return(0,u.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,u.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,u.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function rt(t){return(0,u.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function at(t){return(0,u.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function ut(t){return(0,u.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function ot(t){return(0,u.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function nt(t){return(0,u.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function it(t){return(0,u.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function lt(t){return(0,u.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function dt(t){return(0,u.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function st(t){return(0,u.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function ft(t){return(0,u.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function ct(t){return(0,u.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function pt(t){return(0,u.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function mt(t){return(0,u.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function St(t){return(0,u.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function Pt(t){return(0,u.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function Rt(t){return(0,u.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function It(t){return(0,u.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function ht(t){return(0,u.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function Mt(t){return(0,u.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function Ot(t){return(0,u.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function wt(t){return(0,u.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function At(t){return(0,u.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function yt(t){return(0,u.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function Dt(t){return(0,u.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function Gt(t){return(0,u.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function xt(t){return(0,u.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function gt(t){return(0,u.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function kt(t){return(0,u.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function Lt(t){return(0,u.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function _t(t){return(0,u.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function Nt(t){return(0,u.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function Tt(t){return(0,u.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function bt(t){return(0,u.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function Ct(t){return(0,u.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function Vt(t){return(0,u.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function vt(t){return(0,u.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function Et(t){return(0,u.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function jt(t){return(0,u.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function Ut(t){return(0,u.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},"99e6":function(t,e,r){"use strict";r.r(e);var a=r("336a"),u=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(o);e["default"]=u.a},ddad:function(t,e,r){"use strict";r.r(e);var a=r("eb87"),u=r("99e6");for(var o in u)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return u[t]}))}(o);var n=r("2877"),i=Object(n["a"])(u["default"],a["a"],a["b"],!1,null,"94a78530",null);e["default"]=i.exports},eb87:function(t,e,r){"use strict";r.d(e,"a",(function(){return a})),r.d(e,"b",(function(){return u}));var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ViewPage",{attrs:{"page-type":3}})},u=[]}}]);