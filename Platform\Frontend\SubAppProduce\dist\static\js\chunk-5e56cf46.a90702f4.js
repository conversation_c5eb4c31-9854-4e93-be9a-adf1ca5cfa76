(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5e56cf46"],{"06b4":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530")),n=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var l=a("9002"),s=a("93aa"),c=r(a("6612")),u=a("ed08");t.default={props:{bigTypeData:{type:Number,default:1},formData:{type:Object,default:function(){}},projectList:{type:Array,required:!0,default:function(){return[]}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{OrderCode:"",CategoryId:"",RawName:"",Supplier:"",SupplierName:"",SysProjectId:"",Thick:"",Width:"",Length:"",Material:"",Raw_FullName:"",IsFinished:"0"},selectRow:null,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[],BigType:1}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.PurchaseSubId===t.PurchaseSubId}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)},bigTypeData:{handler:function(e,t){this.BigType=e},immediate:!0}},created:function(){this.getCategoryTreeList()},mounted:function(){this.getConfig();var e=this.formData,t=e.Supplier,a=e.SysProjectId;t&&a&&(this.form.Supplier=t,this.form.SysProjectId=a)},methods:{getConfig:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PROAddRawPurchase");case 1:e.columns=t.v,e.columnsOption(1),e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChange:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return t.BigType=e,t.resetForm("form"),a.n=1,t.columnsOption();case 1:return a.a(2)}}),a)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.BigType,e.currentColumns=JSON.parse(JSON.stringify(e.columns));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;(0,s.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$nextTick((function(a){var r;null===(r=e.$refs)||void 0===r||r.treeSelect.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,s.GetRawProcurementDetails)((0,i.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.InStoreCount=e.AvailableCount,e.WareWeight=(0,c.default)(e.WareWeight||0).divide(1e3).format("0.[00000]"),e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},addJoinedIds:function(){},addToList:function(){var e=(0,u.deepClone)(this.multipleSelection);this.addJoinedIds(),this.$emit("getAddList",e),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e}))),this.addJoinedIds(),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},"0e71":function(e,t,a){"use strict";a.r(t);var r=a("bc2a"),i=a("22d5");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);var o=a("2877"),l=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"bcdffb90",null);t["default"]=l.exports},"1db8":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("2909")),n=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("2532");var l=a("ed08"),s=a("90d1"),c=a("3c4a"),u=r(a("cb17"));t.default={mixins:[u.default],props:{isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1},isReplacePurchase:{type:Boolean,default:!1},checkTypeList:{type:Array,required:!0,default:function(){return[]}},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{tbLoading:!1,multipleSelection:[],rootColumns:[],columns:[],tbData:[],originalData:[],BigType:1,renderComponent:!0,num:1}},watch:{bigTypeData:{handler:function(e,t){this.BigType=e,this.columnsOption()},immediate:!1}},mounted:function(){},methods:{initCreated:function(){this.$route.query.InStoreNo&&this.getDetail()},getDetail:function(){var e=this;(0,c.GetOutFromSourceData)({InStoreNo:this.$route.query.InStoreNo}).then((function(t){e.addData(t.Data)}))},formatTime:function(e){return(0,l.parseTime)(new Date(e),"{y}-{m}-{d}")},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},init:function(e){this.isView?this.rootColumns=JSON.parse(JSON.stringify(e)).map((function(e){return e.Is_Edit=!1,e})):this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.columns=JSON.parse(JSON.stringify(e.rootColumns)),e.columns.map((function(e){e.Style=e.Style?JSON.parse(e.Style):""}));case 1:return t.a(2)}}),t)})))()},getCheckList:function(e,t){this.checkTypeList.map((function(a){a.BigType===e&&(a.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),a.checkList=a.checkList.map((function(e){return e.Code})),a.checkSameList=t.filter((function(e){return e.Is_Edit&&e.Is_Display&&"OutStoreCount"!==e.Code&&"OutStoreWeight"!==e.Code&&"Pound_Weight"!==e.Code&&"Voucher_Weight"!==e.Code&&-1===e.Code.indexOf("Remark")})),a.checkSameList=a.checkSameList.map((function(e){return e.Code})))}))},getAllColumnsOption:function(){var e=this,t=[1,2,3];t.forEach((function(t){var a=JSON.parse(JSON.stringify(e.rootColumns));if(1===t);else if(2===t){var r=a.findIndex((function(e){return"Width"===e.Code}));-1!==r&&a.splice(r,1);var i=a.findIndex((function(e){return"Length"===e.Code}));-1!==i&&a.splice(i,1)}else if(3===t){var n=a.findIndex((function(e){return"Width"===e.Code}));-1!==n&&a.splice(n,1);var o=a.findIndex((function(e){return"Length"===e.Code}));-1!==o&&a.splice(o,1)}e.getCheckList(t,a)}))},tbfetchData:function(){this.tbData=[]},handleCopy:function(e,t){var a=this;return(0,o.default)((0,n.default)().m((function r(){return(0,n.default)().w((function(r){while(1)switch(r.n){case 0:a.tbData.splice(t+1,0,JSON.parse(JSON.stringify(e)));case 1:return r.a(2)}}),r)})))()},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},setVoucherWeight:function(e){var t=(e.OutStoreCount*e.PerVoucherWeight).toFixed(this.weightDecimal);e.Voucher_Weight=t},addData:function(e){var t,a=this,r=e.map((function(e){return e.OutStoreCount=e.AvailableCount,a.setVoucherWeight(e),a.checkWeight(e),e}));(t=this.tbData).push.apply(t,(0,i.default)(r))},checkWeight:function(e){e.OutStoreWeight=(e.Unit_Weight*e.OutStoreCount).toFixed(this.weightDecimal)/1,this.setVoucherWeight(e),this.$emit("updateRow")},blurWeight:function(e,t){"OutStoreWeight"===t&&e.OutStoreWeight>=e.AvailableWeight&&(e.OutStoreWeight=e.AvailableWeight,e.OutStoreCount=e.AvailableCount),this.$emit("updateRow")},checkCount:function(e){if((e.OutStoreCount||0===e.OutStoreCount)&&e.OutStoreCount>=e.AvailableCount)return e.OutStoreCount=e.AvailableCount,e.OutStoreWeight=e.AvailableWeight,void this.$emit("updateRow");this.checkWeight(e)},footerMethod:function(e){var t=this,a=e.columns,r=e.data,i=[a.map((function(e,a){return s.Return_DETAIL_SUMMARY_FIELDS.includes(e.field)?t.sumNum(r,e.field,s.DETAIL_TOTAL_PRICE_DECIMAL):0===a?"合计":null}))];return i},sumNum:function(e,t,a){for(var r=0,i=0;i<e.length;i++)r+=Number(e[i][t])||0;return r.toFixed(a)/1}}}},"22d5":function(e,t,a){"use strict";a.r(t);var r=a("1db8"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},2325:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("点击此处下载导入模板")])],1),a("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},i=[]},3110:function(e,t,a){},"33ca":function(e,t,a){"use strict";a.r(t);var r=a("c710"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},3530:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530")),n=r(a("2909")),o=r(a("c14f")),l=r(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("c740"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("4ec9"),a("a9e3"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("159b"),a("ddb0");var s=a("ed08"),c=r(a("0e71")),u=r(a("485ae")),d=r(a("d5e99")),f=r(a("9adf")),m=r(a("fdc8")),h=a("3166"),p=r(a("c7ab")),b=a("6186"),g=a("5480"),v=r(a("d7a3")),y=a("93aa"),S=a("cf45"),w=a("3c4a"),C=a("30c8"),_=a("e144"),x=r(a("525f")),D=r(a("5d4b")),k=r(a("a657")),R=r(a("29d9")),N=r(a("b5b0"));t.default={components:{SelectLocation:N.default,SelectWarehouse:R.default,DynamicTableFields:k.default,SelectMaterialStoreType:D.default,AddPurchaseList:x.default,AddList2:d.default,ReceiveTb:c.default,ImportFile:f.default,Standard:m.default,AddList:u.default,OSSUpload:p.default},mixins:[v.default],props:{pageType:{type:Number,default:void 0}},data:function(){return{isRetract:!1,statisticTime:"",filterVisible:!1,tbLoading:!1,currentTbComponent:c.default,ProjectList:[],PartyUnitList:[],SupplierList:[],multipleSelection:[],form:{OutStoreNo:"",OutStoreType:+this.$route.query.OutStoreType||4,OutStoreDate:this.getDate(),Supplier:"",PartyUnit:"",Remark:"",Attachment:""},rules:{OutStoreType:[{required:!0,message:"请选择",trigger:"change"}],OutStoreDate:[{required:!0,message:"请选择",trigger:"change"}],Supplier:[{required:!0,message:"请选择",trigger:"change"}],PartyUnit:[{required:!0,message:"请选择",trigger:"change"}]},currentComponent:"",title:"",dWidth:"60%",saveLoading:!1,search:function(){return{}},openAddList:!1,dialogVisible:!1,BigType:1,fileListData:[],fileListArr:[],searchNum:1,rootTableData:[],tableData:[],searchForm:{RawNameFull:"",RawName:"",Thick:"",Spec:"",Material:"",Sys_Project_Id:"",CategoryId:"",Length:"",Width:"",Supplier:"",PartyUnit:"",WarehouseId:"",LocationId:""},treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},rootColumns:[],RawGoodsReturnTypeList:[],checkTypeList:[{BigType:1,checkList:[],checkSameList:[]},{BigType:2,checkList:[],checkSameList:[]},{BigType:3,checkList:[],checkSameList:[]},{BigType:99,checkList:[],checkSameList:[]}],gridCode:"PRORawProcurementOutList"}},computed:{isAdd:function(){return 1==this.pageType},isEdit:function(){return 2==this.pageType},isView:function(){return 3==this.pageType},isSelfProcurement:function(){return 4==this.form.OutStoreType},isCustomer:function(){return 2==this.form.OutStoreType}},created:function(){var e=this;return(0,l.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getCurFactory();case 1:return t.n=2,e.getBaseData();case 2:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,l.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.search=(0,s.debounce)(e.fetchData,800,!0),e.isAdd||e.getInfo(),t.n=1,e.getTableColumns();case 1:case 2:return t.a(2)}}),t)})))()},methods:{handleRetract:function(){this.isRetract=!this.isRetract},radioChange:function(e){this.BigType=e,this.multipleSelection=[],this.handleReset()},handleSearch:function(){var e=this;this.tableData=JSON.parse(JSON.stringify(this.rootTableData)),this.searchForm.RawName&&(this.tableData=this.tableData.filter((function(t){return t.RawName==e.searchForm.RawName}))),this.searchForm.Thick&&(this.tableData=this.tableData.filter((function(t){return Number(t.Thick)==Number(e.searchForm.Thick)}))),this.searchForm.Spec&&(this.tableData=this.tableData.filter((function(t){return t.Spec+""==e.searchForm.Spec+""}))),this.searchForm.Material&&(this.tableData=this.tableData.filter((function(t){return t.Material==e.searchForm.Material}))),this.searchForm.Sys_Project_Id&&(this.tableData=this.tableData.filter((function(t){return t.Sys_Project_Id==e.searchForm.Sys_Project_Id}))),this.searchForm.CategoryId&&(this.tableData=this.tableData.filter((function(t){return t.CategoryId==e.searchForm.CategoryId}))),this.searchForm.Length&&(this.tableData=this.tableData.filter((function(t){return Number(t.Length)==Number(e.searchForm.Length)}))),this.searchForm.Width&&(this.tableData=this.tableData.filter((function(t){return Number(t.Width)==Number(e.searchForm.Width)}))),this.searchForm.Supplier&&(this.tableData=this.tableData.filter((function(t){return t.Supplier==e.searchForm.Supplier}))),this.searchForm.PartyUnit&&(this.tableData=this.tableData.filter((function(t){return t.PartyUnit==e.searchForm.PartyUnit}))),this.searchForm.RawNameFull&&(this.tableData=this.tableData.filter((function(t){return(0,C.isMaterialInclude)(t.Raw_FullName,e.searchForm.RawNameFull)}))),this.searchForm.WarehouseId&&(this.tableData=this.tableData.filter((function(t){return t.WarehouseId===e.searchForm.WarehouseId}))),this.searchForm.LocationId&&(this.tableData=this.tableData.filter((function(t){return t.LocationId===e.searchForm.LocationId}))),this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.tableData)),this.filterVisible=!1},handleReset:function(){this.searchForm.RawNameFull="",this.searchForm.RawName="",this.searchForm.Thick="",this.searchForm.Spec="",this.searchForm.Material="",this.searchForm.Sys_Project_Id="",this.searchForm.CategoryId="",this.searchForm.Length="",this.searchForm.Width="",this.searchForm.Supplier="",this.searchForm.PartyUnit="",this.searchForm.WarehouseId="",this.searchForm.LocationId="",this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.rootTableData)),this.tableData=JSON.parse(JSON.stringify(this.rootTableData))},uploadSuccess:function(e,t,a){this.fileListArr=JSON.parse(JSON.stringify(a))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},handlePreview:function(e){return(0,l.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(a="",!e.response||!e.response.encryptionUrl){t.n=1;break}a=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,b.GetOssUrl)({url:e.encryptionUrl});case 2:a=t.v,a=a.Data;case 3:window.open(a);case 4:return t.a(2)}}),t)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})},getInfo:function(){var e=this;this.form.OutStoreNo=this.$route.query.OutStoreNo,this.form.OutStoreType=+this.$route.query.OutStoreType;var t=this.isSelfProcurement?w.SelfReturnOutStoreDetail:w.PartyAOutStoreDetail;t({outStoreNo:this.form.OutStoreNo}).then((function(t){if(t.IsSucceed){var a=t.Data.Receipt,r=t.Data.Sub.map((function(e){return e})),i=a.OutStoreDate,n=a.Remark,o=a.Supplier,l=a.PartyUnit,s=a.Attachment,c=a.Status;e.form.OutStoreDate=e.getDate(new Date(i)),e.form.Remark=n,e.form.Supplier=o,e.form.PartyUnit=l,e.form.Status=c;var u=r.map((function(e,t){return e.RawNameFull=e.Raw_FullName,e.index=(0,_.v4)(),e.Warehouse_Location=e.WarehouseName?e.WarehouseName+"/"+e.LocationName:"",e}));if(e.$nextTick((function(t){e.$refs["table"].tbData=JSON.parse(JSON.stringify(u)),e.tableData=JSON.parse(JSON.stringify(u)),e.rootTableData=JSON.parse(JSON.stringify(u))})),s){e.form.Attachment=s;var d=s.split(",");d.forEach((function(t){var a=t.indexOf("?Expires=")>-1?t.substring(0,t.lastIndexOf("?Expires=")):t,r=decodeURI(a.substring(a.lastIndexOf("/")+1)),i={};i.name=r,i.url=a,i.encryptionUrl=a,e.fileListData.push(i),e.fileListArr.push(i)}))}}else e.$message({message:t.Message,type:"error"})}))},getTableColumns:function(){var e=this;return(0,l.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,g.getTableConfig)(e.gridCode);case 1:e.rootColumns=t.v,e.$refs["table"].init(e.rootColumns),e.$refs["table"].forceRerender();case 2:return t.a(2)}}),t)})))()},getBaseData:function(){var e=this;(0,S.getDictionary)("RawGoodsReturnType").then((function(t){e.RawGoodsReturnTypeList=t.filter((function(e){return e.Is_Enabled}))})),(0,h.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectList=t.Data.Data:e.$message({message:t.Message,type:"error"})})),(0,y.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var r in t.Data){var i={Id:r,Name:t.Data[r]};a.push(i)}e.SupplierList=a}else e.$message({message:t.Message,type:"error"})})),(0,y.GetPartyAs)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var r in t.Data){var i={Id:r,Name:t.Data[r]};a.push(i)}e.PartyUnitList=a}else e.$message({message:t.Message,type:"error"})})),(0,y.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$refs.treeSelect.treeDataUpdateFun(t.Data)):e.$message({message:t.Message,type:"error"})})),(0,y.GetOMALatestStatisticTime)().then((function(t){t.IsSucceed&&(e.statisticTime=t.Data)}))},changeWarehouse:function(e){this.currentRow=e},getWarehouse:function(e){e.warehouse,e.location},batchEditorFn:function(e){},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,a=e.val;1==t?this.$set(this.currentRow,"StandardDesc",a):(this.$set(this.currentRow,"StandardDesc",a.StandardDesc),this.currentRow.StandardId=a.StandardId)},typeChange:function(e){0!==e&&(this.BigType=1,this.fileListData=[],this.$refs["form"].resetFields(),this.form.OutStoreType=e,this.rootTableData=[],this.handleReset(),this.multipleSelection=[])},projectChange:function(){this.BigType=1,this.multipleSelection=[],this.rootTableData=[],this.handleReset()},supplierChange:function(e){this.multipleSelection=[],this.rootTableData=[],this.handleReset()},partyUnitChange:function(e){this.multipleSelection=[],this.rootTableData=[],this.handleReset()},getAddList:function(e){this.BigType=e[0].BigType,this.handleUpdateTb(e)},handleUpdateTb:function(e){var t,a;e.map((function(e,t){e.RawNameFull=e.Raw_FullName,e.index=(0,_.v4)()}));var r=JSON.parse(JSON.stringify(e));this.$refs["table"].addData(r),(t=this.tableData).push.apply(t,(0,n.default)(r)),(a=this.rootTableData).push.apply(a,(0,n.default)(r))},handleUpdateRow:function(){var e=JSON.parse(JSON.stringify(this.$refs.table.tbData));this.tableData=JSON.parse(JSON.stringify(e)),this.rootTableData.map((function(t){var a=e.find((function(e){return e.index==t.index}));a&&Object.assign(t,a)}))},importData:function(e){this.$refs["table"].importData(e)},getRowName:function(e){var t=e.Name,a=e.Id;this.currentRow.Name=t,this.currentRow.RawId=a,this.currentRow.StandardDesc=""},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handleWarehouse:function(e){},handleImport:function(){this.currentComponent="ImportFile",this.dWidth="40%",this.title="原料导入",this.dialogVisible=!0},handleDelete:function(e){var t,a=JSON.parse(JSON.stringify(this.$refs.table.tbData)),r=JSON.parse(JSON.stringify(this.rootTableData));this.multipleSelection.forEach((function(e,t){var i=a.findIndex((function(t){return t.index==e.index}));a.splice(i,1);var n=r.findIndex((function(t){return t.index==e.index}));r.splice(n,1)})),this.$refs.table.tbData=JSON.parse(JSON.stringify(a)),this.tableData=JSON.parse(JSON.stringify(a)),this.rootTableData=JSON.parse(JSON.stringify(r)),this.multipleSelection=[],null===(t=this.$refs.table)||void 0===t||null===(t=t.$refs)||void 0===t||t.xTable.clearCheckboxRow()},handleClose:function(e){this.openAddList=!1,this.dialogVisible=!1},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},checkValidate:function(){var e=(0,s.deepClone)(this.$refs["table"].tbData);if(!e.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var t=e.filter((function(e){return 1==e.BigType})),a=e.filter((function(e){return 2==e.BigType})),r=e.filter((function(e){return 3==e.BigType})),i=e.filter((function(e){return 99==e.BigType})),n={status:!0,type:"",msg:""};if(t.length&&n.status&&(n=this.checkTb(t)),a.length&&n.status&&(n=this.checkTb(a)),r.length&&n.status&&(n=this.checkTb(r)),i.length&&n.status&&(n=this.checkTb(i)),!n.status)return this.$message({message:"".concat(n.type+n.msg||"必填字段","不能为空"),type:"warning"}),{status:!1};var o={status:!0,type:"",msg:""};return t.length&&o.status&&(o=this.checkSameTb(t)),a.length&&o.status&&(o=this.checkSameTb(a)),r.length&&o.status&&(o=this.checkSameTb(r)),i.length&&o.status&&(o=this.checkSameTb(i)),{data:e,status:!0}},checkTb:function(e){for(var t=this,a=this.checkTypeList.find((function(t){return t.BigType==e[0].BigType})).checkList,r=0;r<e.length;r++){for(var i,n=e[r],o=function(){var e=a[l];if(["",null,void 0].includes(n[e])){var r=t.$refs.table.rootColumns,i=r.find((function(t){return t.Code==e}));return{v:{status:!1,msg:null===i||void 0===i?void 0:i.Display_Name,type:1==n.BigType?"板材":2==n.BigType?"型材":3==n.BigType?"钢卷":"其他"}}}},l=0;l<a.length;l++)if(i=o(),i)return i.v;delete n._X_ROW_KEY,delete n.WarehouseName,delete n.LocationName}return{status:!0,msg:"",type:""}},checkSameTb:function(e){for(var t,a=["Store_Sub_Id"],r=new Map,i=function(){var t=e[n],i=a.map((function(e){return t[e]})).join("-");if(r.has(i))return{v:{status:!1,msg:"",type:1==t.BigType?"板材":2==t.BigType?"型材":3==t.BigType?"钢卷":"其他"}};r.set(i,!0)},n=0;n<e.length;n++)if(t=i(),t)return t.v;return{status:!0,msg:"",type:""}},handleSubmit:function(e){var t=this;this.$confirm("确认提交退货单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.saveDraft(3)})).catch((function(){t.$message({type:"info",message:"已取消"})}))},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=this.checkValidate(),r=a.data,n=a.status;n&&this.$refs["form"].validate((function(a){if(!a)return!1;var n=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){n.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)})),e.form.Attachment=n.join(","),e.form.Status=1==t?1:3;var o=(0,i.default)({},e.form),l="";r=r.map((function(e){return(e.OutStoreWeight>=e.AvailableWeight&&e.OutStoreCount!=e.AvailableCount||e.OutStoreWeight!=e.AvailableWeight&&e.OutStoreCount>=e.AvailableCount)&&(l="明细数据中数量或重量有一个值为库存最大值但另一个值非最大值，非最大值将自动被改为最大值",e.OutStoreCount=e.AvailableCount),e})),l&&e.$message.info(l);var s="";4==e.form.OutStoreType?s=w.SelfReturnOutStore:2==e.form.OutStoreType&&(s=w.PartyAOutStore),s({Receipt:o,Sub:r}).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"}),e.saveLoading=!1}))}))},openAddDialog:function(e){this.openAddList=!0},closeView:function(){(0,s.closeTagView)(this.$store,this.$route)},setSelectRow:function(e){this.multipleSelection=e},getDate:function(e){var t=e||new Date,a=t.getFullYear(),r=("0"+(t.getMonth()+1)).slice(-2),i=("0"+t.getDate()).slice(-2);return"".concat(a,"-").concat(r,"-").concat(i)}}}},3821:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],a("el-col",{attrs:{span:2}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(0===r[t.Code]?"按需使用":1===r[t.Code]?"使用标准规格":2===r[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},3973:function(e,t,a){},"485ae":function(e,t,a){"use strict";a.r(t);var r=a("3821"),i=a("85ae");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("d677");var o=a("2877"),l=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"435d622b",null);t["default"]=l.exports},"49b3":function(e,t,a){"use strict";a.r(t);var r=a("6a71"),i=a("d827");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("8ad3");var o=a("2877"),l=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"4059477b",null);t["default"]=l.exports},"4b6a":function(e,t,a){"use strict";a("8cde")},"4c99":function(e,t,a){"use strict";a("3110")},"4ec9":function(e,t,a){"use strict";a("6f48")},"525f":function(e,t,a){"use strict";a.r(t);var r=a("82d9"),i=a("fdfa");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("d3d1");var o=a("2877"),l=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"52d847c6",null);t["default"]=l.exports},"6a71":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card",style:e.isRetract?"height: 110px; overflow: hidden;":""},[a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"10px","padding-bottom":"10","border-bottom":"1px solid #D0D3DB"}},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("退货单信息")]),a("div",{staticClass:"retract-container",on:{click:e.handleRetract}},[a("el-button",{attrs:{type:"text"}},[e._v(e._s(e.isRetract?"展开":"收起"))]),a("el-button",{attrs:{type:"text",icon:e.isRetract?"el-icon-arrow-down":"el-icon-arrow-up"}})],1)]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退货类型",prop:"OutStoreType"}},[a("SelectMaterialStoreType",{attrs:{disabled:e.isView||e.isEdit,type:"RawReturnStoreType"},on:{change:e.typeChange},model:{value:e.form.OutStoreType,callback:function(t){e.$set(e.form,"OutStoreType",t)},expression:"form.OutStoreType"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退货日期",prop:"OutStoreDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView,"picker-options":{disabledDate:function(t){return t.getTime()<new Date(e.statisticTime).getTime()}},"value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.OutStoreDate,callback:function(t){e.$set(e.form,"OutStoreDate",t)},expression:"form.OutStoreDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView,rows:1,"show-word-limit":"",maxlength:100,placeholder:"备注",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:5,multiple:!0,"on-success":function(t,a,r){e.uploadSuccess(t,a,r)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"show-file-list":!0,"file-list":e.fileListData,disabled:e.isView}},[e.isView?e._e():a("el-button",{attrs:{type:"primary"}},[e._v("上传文件")])],1)],1)],1)],1)],1)],1),a("el-card",{staticClass:"box-card box-card-tb"},[a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("退货单明细")])]),a("el-divider",{staticClass:"elDivder"}),a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"0px"}},[a("div"),a("el-form",{ref:"searchForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm,"label-width":"80px"}},[e.filterVisible?e._e():[a("el-form-item",{staticClass:"cs-item",attrs:{label:"原料全名",prop:"RawNameFull"}},[a("el-input",{attrs:{placeholder:"通配符%",clearable:""},model:{value:e.searchForm.RawNameFull,callback:function(t){e.$set(e.searchForm,"RawNameFull","string"===typeof t?t.trim():t)},expression:"searchForm.RawNameFull"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Material","label-width":"50px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Material,callback:function(t){e.$set(e.searchForm,"Material",t)},expression:"searchForm.Material"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"Sys_Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.Sys_Project_Id,callback:function(t){e.$set(e.searchForm,"Sys_Project_Id",t)},expression:"searchForm.Sys_Project_Id"}},e._l(e.ProjectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],a("el-form-item",{staticClass:"last-btn",style:e.filterVisible?"width: 82px":""},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.filterVisible,expression:"!filterVisible"}],attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.filterVisible,expression:"!filterVisible"}],staticClass:"reset-btn",on:{click:e.handleReset}},[e._v("重置")]),a("el-popover",{attrs:{placement:"bottom",width:"1130"},model:{value:e.filterVisible,callback:function(t){e.filterVisible=t},expression:"filterVisible"}},[a("el-form",{ref:"searchAllForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm,"label-width":"120px"}},[a("el-form-item",{staticClass:"cs-item",attrs:{label:"原料全名",prop:"RawNameFull"}},[a("el-input",{attrs:{placeholder:"通配符%",clearable:""},model:{value:e.searchForm.RawNameFull,callback:function(t){e.$set(e.searchForm,"RawNameFull","string"===typeof t?t.trim():t)},expression:"searchForm.RawNameFull"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Material,callback:function(t){e.$set(e.searchForm,"Material",t)},expression:"searchForm.Material"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"Sys_Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.Sys_Project_Id,callback:function(t){e.$set(e.searchForm,"Sys_Project_Id",t)},expression:"searchForm.Sys_Project_Id"}},e._l(e.ProjectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"原料分类",prop:"CategoryId"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.searchForm.CategoryId,callback:function(t){e.$set(e.searchForm,"CategoryId",t)},expression:"searchForm.CategoryId"}})],1),1==e.BigType?a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Width,callback:function(t){e.$set(e.searchForm,"Width",t)},expression:"searchForm.Width"}})],1):e._e(),1==e.BigType||2==e.BigType?a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Length,callback:function(t){e.$set(e.searchForm,"Length",t)},expression:"searchForm.Length"}})],1):e._e(),a("el-form-item",{attrs:{label:"仓库",prop:"WarehouseId"}},[a("SelectWarehouse",{model:{value:e.searchForm.WarehouseId,callback:function(t){e.$set(e.searchForm,"WarehouseId",t)},expression:"searchForm.WarehouseId"}})],1),a("el-form-item",{attrs:{label:"库位",prop:"LocationId"}},[a("SelectLocation",{attrs:{"warehouse-id":e.searchForm.WarehouseId},model:{value:e.searchForm.LocationId,callback:function(t){e.$set(e.searchForm,"LocationId",t)},expression:"searchForm.LocationId"}})],1),a("el-form-item",{staticClass:"last-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),a("el-button",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(e.filterVisible?"收起筛选":"展开筛选"))])],1)],1)],2)],1),a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"8px"}},[a("vxe-toolbar",{staticStyle:{width:"100%"},scopedSlots:e._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")]),a("div",{staticStyle:{"margin-left":"auto"}},[a("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.getTableColumns}})],1)]},proxy:!0}],null,!1,4013722377)})],1),a("div",{staticClass:"tb-x"},[a(e.currentTbComponent,{ref:"table",tag:"component",attrs:{"is-view":e.isView,"big-type-data":e.BigType,"check-type-list":e.checkTypeList,"project-list":e.ProjectList,"supplier-list":e.SupplierList,"party-unit-list":e.PartyUnitList},on:{changeStandard:e.changeStandard,changeWarehouse:e.changeWarehouse,select:e.setSelectRow,updateRow:e.handleUpdateRow}})],1),e.isView?e._e():a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("footer",[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("div",[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{loading:e.saveLoading},on:{click:function(t){return e.saveDraft(1)}}},[e._v("保存草稿 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("提交退货")])],1)])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":0},on:{close:e.handleClose,warehouse:e.getWarehouse,batchEditor:e.batchEditorFn,importData:e.importData,standard:e.getStandard,refresh:e.fetchData}})],1):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增",visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[a("AddList2",{ref:"draft",attrs:{"big-type-data":e.BigType,"form-data":e.form,"project-list":e.ProjectList,"joined-items":e.rootTableData},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2)],1)},i=[]},"6de4":function(e,t,a){},"6f48":function(e,t,a){"use strict";var r=a("6d61"),i=a("6566");r("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},"72f6":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var i=r(a("3796")),n=a("2245"),o=a("ed08");t.default={components:{Upload:i.default},props:{pageType:{type:Number,default:void 0}},data:function(){return{btnLoading:!1,schdulingPlanId:""}},methods:{getTemplate:function(){var e=this;(0,n.TemplateDownload)({templateType:0===this.pageType?"YLRK":"FLRK"}).then((function(t){window.open((0,o.combineURL)(e.$baseUrl,t.Data))}))},beforeUpload:function(e){var t,a=this,r=new FormData;r.append("files",e),this.btnLoading=!0,t=0===this.pageType?n.ImportMatRawRcpt:n.ImportMatAuxRcpt,t(r).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"导入成功"}),a.$emit("importData",e.Data),a.$emit("close")):a.$message({type:"error",message:e.Message}),a.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},"82d9":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-tabs",{on:{"tab-click":e.handleSearch},model:{value:e.form.IsFinished,callback:function(t){e.$set(e.form,"IsFinished",t)},expression:"form.IsFinished"}},[a("el-tab-pane",{attrs:{label:"未完成",name:"0"}}),a("el-tab-pane",{attrs:{label:"已完成",name:"1"}})],1),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"采购单号",prop:"OrderCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"采购单号",type:"text"},model:{value:e.form.OrderCode,callback:function(t){e.$set(e.form,"OrderCode","string"===typeof t?t.trim():t)},expression:"form.OrderCode"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料分类",prop:"CategoryId"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",staticStyle:{width:"100%"},attrs:{"tree-params":e.treeParams},model:{value:e.form.CategoryId,callback:function(t){e.$set(e.form,"CategoryId",t)},expression:"form.CategoryId"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{attrs:{clearable:"",placeholder:"原料名称",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商",prop:"SupplierName"}},[a("el-input",{attrs:{clearable:"",placeholder:"供应商",type:"text"},model:{value:e.form.SupplierName,callback:function(t){e.$set(e.form,"SupplierName","string"===typeof t?t.trim():t)},expression:"form.SupplierName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"厚度",prop:"Thick"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"厚度"},model:{value:e.form.Thick,callback:function(t){e.$set(e.form,"Thick","string"===typeof t?t.trim():t)},expression:"form.Thick"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{type:"text",clearable:"",placeholder:"材质"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"0"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["WareCount"===t.Code?{key:"default",fn:function(t){var r=t.row;return[a("span",{style:{color:r.WareCount>r.PurchaseCount?"red":""}},[e._v(" "+e._s(r.WareCount)+" ")])]}}:"PurchaseWeight"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(Number((a.PurchaseWeight/1e3).toFixed(5)))+" ")]}}:"Actual_Spec"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.Actual_Spec||"-")+" ")]}}:"DeliveryTime"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]}}:"PurchaseTime"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},"85ae":function(e,t,a){"use strict";a.r(t);var r=a("9e26"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},"8a08":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[2===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"非标规格",name:"1"}},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},e._l(e.list,(function(t){return a("el-form-item",{key:t,attrs:{label:t}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:e.form[t],callback:function(a){e.$set(e.form,t,a)},expression:"form[item]"}})],1)})),1)],1):e._e(),1===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"标准规格",name:"2"}},[a("el-table",{ref:"multipleTable",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(a){return a.stopPropagation(),function(a){return e.handleRadioChange(a,t.row)}(a)}},model:{value:e.radioSelect,callback:function(t){e.radioSelect=t},expression:"radioSelect"}})]}}],null,!1,3152109164)}),a("el-table-column",{attrs:{align:"center",prop:"StandardDesc",label:"规格"}})],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},i=[]},"8ad3":function(e,t,a){"use strict";a("b79e")},"8cde":function(e,t,a){},"92ba":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b"),a("14d9"),a("13d5"),a("e9f5"),a("9485"),a("d3b7");var r=a("2245");t.default={data:function(){return{activeName:"1",radioSelect:null,btnLoading:!1,form:{},list:[],tableData:[],specificationUsage:0}},watch:{specificationUsage:function(e,t){1===e&&(this.activeName="2")}},methods:{getOption:function(e){var t=this;this.specificationUsage=e.SpecificationUsage,e&&(this.list=e.RcptRawParams.split("*")),(0,r.GetRawStandardsList)({rawId:e.RawId}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleRadioChange:function(e,t){e.stopPropagation(),this.currentRow=Object.assign({},t)},submit:function(){var e=this;if("1"===this.activeName){var t=!0,a=this.list.reduce((function(a,r){if(e.form[r])return a.push(e.form[r]),a;t=!1}),[]);if(!t)return void this.$message({message:"输入数据不能为0",type:"warning"});this.$emit("standard",{type:1,val:a.join("*")})}else{if(!this.currentRow)return void this.$message({message:"请选择规格",type:"warning"});this.$emit("standard",{type:2,val:this.currentRow})}this.handleClose()},handleClose:function(){this.$emit("close")}}}},"952a":function(e,t,a){"use strict";a.r(t);var r=a("92ba"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},"9adf":function(e,t,a){"use strict";a.r(t);var r=a("2325"),i=a("d852");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("bd95");var o=a("2877"),l=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"4a50d31e",null);t["default"]=l.exports},"9d7e2":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.FindPickingNewSum=o,t.FindReceivingNewSum=l,t.GetAuxCostReport=s,t.GetAuxCostReportSummary=c,t.GetListForContractSetting=n,t.GetListForContractSettingForMateriel=u;var i=r(a("b775"));function n(e){return(0,i.default)({url:"/SYS/ExternalCompany/GetListForContractSetting",method:"post",data:e})}function o(e){return(0,i.default)({url:"/PRO/MaterielFlow/FindPickingNewSum",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/MaterielFlow/FindReceivingNewSum",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/MaterielReport/GetAuxCostReport",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/MaterielReport/GetAuxCostReportSummary",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/MaterielRawInStore/GetListForContractSettingForMateriel",method:"post",data:e})}},"9e26":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("c14f")),n=r(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("841c");var o=a("ed08"),l=a("9002"),s=a("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,s.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},b79e:function(e,t,a){},bc2a:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.renderComponent?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"show-footer":"","footer-method":e.footerMethod},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([t.Style.tips?{key:"header",fn:function(){return[a("span",[e._v(e._s(t.Display_Name))]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(t.Style.tips)},slot:"content"}),a("i",{staticClass:"el-icon-question",staticStyle:{cursor:"pointer","font-size":"16px"}})])]},proxy:!0}:null,{key:"default",fn:function(r){var i=r.row;return["AvailableWeight"===t.Code?a("span",[e._v(" "+e._s(i[t.Code]||0===i[t.Code]?i[t.Code]:"-")+" ")]):"Warehouse_Location"===t.Code?a("span",[e._v(" "+e._s(i.WarehouseName)+"/"+e._s(i.LocationName)+" ")]):"InStoreDate"===t.Code?a("span",[e._v(" "+e._s(i.InStoreDate?e.formatTime(i.InStoreDate):"-")+" ")]):"RawName"===t.Code?[a("div",[i.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),i.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),i.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(i.RawName))],1)]:"TaxUnitPrice"===t.Code||"NoTaxUnitPrice"===t.Code?a("span",[e._v(" "+e._s(i[t.Code]||"-")+" ")]):a("span",[e._v(e._s(i[t.Code]||0==i[t.Code]?i[t.Code]:t.Is_Edit?"":"-"))])]}},t.Is_Edit?{key:"edit",fn:function(r){var i=r.row;return["Actual_Thick"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Width"===t.Code||"Length"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.checkWeight(i)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"OutStoreCount"===t.Code?a("div",[a("vxe-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:2,min:0},expression:"{toFixed:2,min:0}"}],attrs:{min:0,type:"number"},on:{change:function(t){return e.checkCount(i)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"OutStoreWeight"===t.Code?a("div",[i.Specific_Gravity&&3!=i.BigType?a("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(a){return e.blurWeight(i,t.Code)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Pound_Weight"===t.Code||"Voucher_Weight"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(a){return e.blurWeight(i,t.Code)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):a("div",[a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},i=[]},bd95:function(e,t,a){"use strict";a("3973")},c710:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530")),n=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var l=a("9002"),s=a("93aa"),c=a("3c4a"),u=a("ed08"),d=r(a("d9e9")),f=r(a("bad9"));t.default={components:{SelectProject:f.default,SelectExternal:d.default},props:{bigTypeData:{type:Number,default:1},projectList:{type:Array,default:function(){return[]}},formData:{type:Object,default:function(){}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{Raw_FullName:"",RawName:"",Thick:"",Spec:"",Material:"",Supplier:"",PartyUnit:"",SysProjectId:"",Raw_Property:"",Category_Id:"",Width:"",Length:"",InStoreNo:""},selectRow:null,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[],BigType:1}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.Store_Sub_Id===t.Store_Sub_Id}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)},bigTypeData:{handler:function(e,t){this.BigType=e},immediate:!0}},mounted:function(){this.getCategoryTreeList(),this.getConfig();var e=this.formData,t=e.Supplier,a=e.PartyUnit;(t||a)&&(this.form.Supplier=t,this.form.PartyUnit=a)},methods:{getConfig:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PRORawMaterialOutboundReturnAdd");case 1:e.columns=t.v,e.columnsOption(),e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChange:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return t.BigType=e,t.resetForm("form"),a.n=1,t.columnsOption();case 1:return a.a(2)}}),a)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.BigType,e.currentColumns=JSON.parse(JSON.stringify(e.columns));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;(0,s.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$refs.treeSelect.treeDataUpdateFun(t.Data)):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,c.GetOutFromSourceData)((0,i.default)((0,i.default)({},e.form),{},{OutStoreType:e.formData.OutStoreType})).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.OutStoreCount=e.AvailableCount,e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},addToList:function(){var e=(0,u.deepClone)(this.multipleSelection);this.$emit("getAddList",e),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},cb17:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b64b");var i=r(a("c14f")),n=r(a("1da1")),o=r(a("05e5")),l=a("90d1"),s=a("1099");t.default={components:{ImportExcel:o.default},data:function(){return{unitWeightDecimal:l.UNIT_WEIGHT_DECIMAL,weightDecimal:l.WEIGHT_DECIMAL,TAX_MODE:0,taxMode:l.TAX_MODE}},created:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){var a,r,n,o,l,c,u,d;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetPreferenceSettingValue)({code:"materialConfig"});case 1:if(u=a=t.v,c=null===u,c){t.n=2;break}c=void 0===a;case 2:if(!c){t.n=3;break}d=void 0,t.n=4;break;case 3:d=a.Data;case 4:r=d,r&&(r=JSON.parse(r),e.unitWeightDecimal=(null===(n=r)||void 0===n?void 0:n.unitWeightDecimal)||e.unitWeightDecimal,e.weightDecimal=(null===(o=r)||void 0===o?void 0:o.weightDecimal)||e.weightDecimal,e.taxMode=(null===(l=r)||void 0===l?void 0:l.taxMode)||e.taxMode),e.initCreated();case 5:return t.a(2)}}),t)})))()}}},d3d1:function(e,t,a){"use strict";a("6de4")},d5e99:function(e,t,a){"use strict";a.r(t);var r=a("e87f5"),i=a("33ca");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("4b6a");var o=a("2877"),l=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"8bf5ce60",null);t["default"]=l.exports},d677:function(e,t,a){"use strict";a("e29c")},d827:function(e,t,a){"use strict";a.r(t);var r=a("3530"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},d852:function(e,t,a){"use strict";a.r(t);var r=a("72f6"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},e29c:function(e,t,a){},e87f5:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"90px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"入库单号",prop:"InStoreNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.InStoreNo,callback:function(t){e.$set(e.form,"InStoreNo","string"===typeof t?t.trim():t)},expression:"form.InStoreNo"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料属性",prop:"Raw_Property"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择"},model:{value:e.form.Raw_Property,callback:function(t){e.$set(e.form,"Raw_Property",t)},expression:"form.Raw_Property"}},[a("el-option",{attrs:{label:"自采",value:1}}),a("el-option",{attrs:{label:"甲供",value:2}}),a("el-option",{attrs:{label:"代购",value:3}}),a("el-option",{attrs:{label:"余料",value:4}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格/厚度",prop:"Spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商/甲方",prop:"Supplier"}},[a("SelectExternal",{model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"0"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["AvailableCount"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(0===r[t.Code]?"0":r[t.Code]||"-")+" ")]}}:"AvailableWeight"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}:"InStoreDate"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]}}:"Warehouse_Location"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.WarehouseName)+"/"+e._s(a.LocationName)+" ")]}}:"TaxUnitPrice"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}:"Tax_All_Price"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||0===r[t.Code]?r[t.Code].toFixed(2):"-")+" ")]}}:"OutStoreCount"===t.Code?{key:"default",fn:function(r){var i=r.row;return[a("vxe-input",{attrs:{min:0,max:i.AvailableCount,type:"number"},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})]}}:"RawName"===t.Code?{key:"default",fn:function(t){var r=t.row;return[a("div",[r.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),r.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),r.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(r.RawName)+" ")],1)]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},fdc8:function(e,t,a){"use strict";a.r(t);var r=a("8a08"),i=a("952a");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("4c99");var o=a("2877"),l=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"a156059a",null);t["default"]=l.exports},fdfa:function(e,t,a){"use strict";a.r(t);var r=a("06b4"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a}}]);