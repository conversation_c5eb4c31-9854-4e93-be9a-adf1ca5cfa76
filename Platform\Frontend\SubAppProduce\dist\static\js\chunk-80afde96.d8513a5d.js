(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-80afde96"],{"07a9":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{staticClass:"h100 cs-fill-card"},[n("div",{staticClass:"table-top",attrs:{slot:"header"},slot:"header"},[n("div",[n("el-radio-group",{model:{value:e.tableTopTadio,callback:function(t){e.tableTopTadio=t},expression:"tableTopTadio"}},[n("el-radio-button",{attrs:{label:"pc"}},[e._v("PC菜单")]),n("el-radio-button",{attrs:{label:"app"}},[e._v("APP菜单")])],1)],1),n("div",[n("el-input",{staticStyle:{width:"200px","margin-right":"16px"},attrs:{placeholder:"输入关键字进行过滤","suffix-icon":"el-icon-search"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),n("el-button",{attrs:{type:"primary",disabled:"app"===e.tableTopTadio}},[e._v("设为模块首页")]),n("el-button",{attrs:{type:"primary"},on:{click:e.handAdd}},[e._v("新增菜单")])],1)]),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticStyle:{width:"100%"},attrs:{height:"100%",data:e.tableData,size:"middle","row-key":"Id","default-expand-all":"","tree-props":{children:"Children"}}},[n("el-table-column",{attrs:{type:"selection",width:"55"}}),e._l(e.tableTitle,(function(t){return n("el-table-column",{key:t.id,attrs:{align:"center",label:t.name,width:t.width},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return["Icon"===t.en?n("span",[n("i",{class:["iconfont",i[t.en]]})]):"Is_Enabled"===t.en?n("span",[e._v(" "+e._s(i.Is_Enabled?"是":"否")+" ")]):n("span",[e._v(" "+e._s(i[t.en])+" ")])]}}],null,!0)})})),n("el-table-column",{attrs:{label:"操作",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(a)}}},[e._v("查看")]),n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]),n("el-button",{attrs:{type:"text",disabled:""},on:{click:function(t){return e.handleBtnEdit(a)}}},[e._v("按钮")]),n("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")])]}}])})],2),n("btn-dialog",{ref:"btnDialog"})],1)},i=[]},"1c88":function(e,t,n){"use strict";n.r(t);var a=n("688e9"),i=n.n(a);for(var l in a)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(l);t["default"]=i.a},"2d62":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("e9f5"),n("910d"),n("dca8"),n("d3b7");var i=a(n("3ee1")),l=a(n("6b3b")),o=a(n("6347")),r=n("dbde");t.default={name:"SysMenuManage",components:{zTable:i.default,zDialog:l.default},mixins:[o.default],data:function(){return{treeData:[],treeLoading:!0,dialogVisible:!1,rowData:{},tableData:[],tableTopTitle:"",tableLoading:!0,rootId:"",expandedKey:"",filterText:"",defaultProps:{children:"Children",label:"Display_Name"},tableTopTadio:"pc"}},watch:{filterText:function(e){this.$refs.tree.filter(e)}},mounted:function(){this.getTreeList()},methods:{getTreeList:function(){var e=this;this.treeLoading=!0,(0,r.GetModuleList)({PageIndex:-1}).then((function(t){e.treeData=Object.freeze(t.Data.Data),e.rootId=t.Data.Data[0].Id,e.treeLoading=!1,e.getMenuList()}))},getMenuList:function(){var e=this;this.tableLoading=!0,(0,r.GetMenuList)({ModuleId:this.rootId,Page_Type:this.tableTopTadio||"pc"}).then((function(t){e.tableData=t.Data,e.tableLoading=!1}))},handleNodeClick:function(e){this.rootId=e.Id,this.getMenuList()},getTableTopTitle:function(e){this.tableTopTitle=e},handleEdit:function(e){this.rowData=e,this.$refs.dialog.handleOpen(e,this.treeData,"edit")},handleDetail:function(e){this.rowData=e,this.$refs.dialog.handleOpen(e,this.treeData,"view")},handleAdd:function(){this.$refs.dialog.handleOpen({},this.treeData,"add")},filterNode:function(e,t){return!e||-1!==t.Display_Name.indexOf(e)},getableTopTadio:function(e){this.tableTopTadio=e||"pc"}}}},"3ee1":function(e,t,n){"use strict";n.r(t);var a=n("07a9"),i=n("8824");for(var l in i)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(l);n("b6ee");var o=n("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"85693ea0",null);t["default"]=r.exports},"5a21":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"cs-dialog",attrs:{"custom-class":"dialogCustomClass",title:"add"===e.title?"新增":"编辑",visible:e.dialogVisible,width:"40%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",attrs:{"label-width":"120px",inline:!0,rules:e.rules,model:e.formInline,disabled:e.isView}},[n("el-form-item",{attrs:{label:"所属模块",prop:"Parent_Module"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.changeModule,clear:e.clearModule},model:{value:e.formInline.Parent_Module,callback:function(t){e.$set(e.formInline,"Parent_Module",t)},expression:"formInline.Parent_Module"}},e._l(e.moduleList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"上级菜单",prop:"Parent_Id"}},[n("el-tree-select",{ref:"treeSelect",attrs:{"tree-params":e.treeParams,disabled:!Boolean(e.formInline.Parent_Module)},model:{value:e.formInline.Parent_Id,callback:function(t){e.$set(e.formInline,"Parent_Id",t)},expression:"formInline.Parent_Id"}})],1),n("el-form-item",{attrs:{label:"菜单名称",prop:"Display_Name"}},[n("el-input",{attrs:{placeholder:"名称"},model:{value:e.formInline.Display_Name,callback:function(t){e.$set(e.formInline,"Display_Name",t)},expression:"formInline.Display_Name"}})],1),n("el-form-item",{attrs:{label:"菜单编号",prop:"Code"}},[n("el-input",{attrs:{placeholder:"编号"},model:{value:e.formInline.Code,callback:function(t){e.$set(e.formInline,"Code",t)},expression:"formInline.Code"}})],1),n("el-form-item",{attrs:{label:"业务范围",prop:"Business_Scope"}},[n("el-checkbox-group",{model:{value:e.formInline.Business_Scope,callback:function(t){e.$set(e.formInline,"Business_Scope",t)},expression:"formInline.Business_Scope"}},e._l(e.moduleBusinessScope,(function(t){return n("el-checkbox",{key:t.Id,attrs:{label:t}},[e._v(e._s(t))])})),1)],1),n("el-form-item",{attrs:{label:"选择图标",prop:"Icon"}},[n("el-input",{attrs:{placeholder:"图标"},model:{value:e.formInline.Icon,callback:function(t){e.$set(e.formInline,"Icon",t)},expression:"formInline.Icon"}}),n("router-link",{attrs:{target:"_blank",to:{name:"SysIcons"}}},[n("svg-icon",{attrs:{"icon-class":"eye-open","class-name":"eye-open"}})],1)],1),n("el-form-item",{attrs:{label:"菜单类型",prop:"Menu_Type"}},[n("el-radio-group",{model:{value:e.formInline.Menu_Type,callback:function(t){e.$set(e.formInline,"Menu_Type",t)},expression:"formInline.Menu_Type"}},[n("el-radio",{attrs:{label:"业务"}},[e._v("业务")]),n("el-radio",{attrs:{label:"报表"}},[e._v("报表")])],1)],1),n("el-form-item",{attrs:{label:"选项"}},[[n("el-checkbox",{model:{value:e.formInline.Is_Enabled,callback:function(t){e.$set(e.formInline,"Is_Enabled",t)},expression:"formInline.Is_Enabled"}},[e._v("有效")]),n("el-checkbox",{model:{value:e.formInline.Is_Line,callback:function(t){e.$set(e.formInline,"Is_Line",t)},expression:"formInline.Is_Line"}},[e._v("分割线")])]],2),n("el-form-item",{attrs:{label:"菜单排序",prop:"Sort"}},[n("el-input",{attrs:{placeholder:"排序"},model:{value:e.formInline.Sort,callback:function(t){e.$set(e.formInline,"Sort",e._n(t))},expression:"formInline.Sort"}})],1),n("el-form-item",{attrs:{label:"禁用缓存",prop:"No_Cache"}},[n("el-checkbox",{model:{value:e.formInline.No_Cache,callback:function(t){e.$set(e.formInline,"No_Cache",t)},expression:"formInline.No_Cache"}})],1),n("el-form-item",{attrs:{label:"菜单组件",prop:"Component"}},[n("el-input",{attrs:{placeholder:"component"},model:{value:e.formInline.Component,callback:function(t){e.$set(e.formInline,"Component",t)},expression:"formInline.Component"}})],1),n("el-form-item",{attrs:{label:"菜单地址",prop:"Url_Address"}},[n("el-input",{attrs:{placeholder:"地址"},model:{value:e.formInline.Url_Address,callback:function(t){e.$set(e.formInline,"Url_Address",t)},expression:"formInline.Url_Address"}})],1),n("el-form-item",{staticClass:"all-line",attrs:{label:"备注信息",prop:"Remark"}},[n("el-input",{attrs:{rows:3,type:"textarea",placeholder:"请输入"},model:{value:e.formInline.Remark,callback:function(t){e.$set(e.formInline,"Remark",t)},expression:"formInline.Remark"}})],1)],1),e.isView?e._e():n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){return e.handleCancel("ruleForm")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("确 定")])],1)],1)},i=[]},"609b":function(e,t,n){"use strict";n.r(t);var a=n("2d62"),i=n.n(a);for(var l in a)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(l);t["default"]=i.a},"61fb":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-drawer",{attrs:{title:e.title,visible:e.dialogVisible,size:"50%",direction:"rtl"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n("el-button",{staticStyle:{margin:"10px 0 10px 20px"},attrs:{icon:"iconfont icon-plus",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleAddEditClick("add")}}},[e._v(" 新增")]),n("el-table",{staticStyle:{width:"100%"},attrs:{size:"large",data:e.tableData}},[n("el-table-column",{attrs:{type:"index",width:"50"}}),n("el-table-column",{attrs:{prop:"Code",label:"编码",width:"180"}}),n("el-table-column",{attrs:{prop:"Display_Name",label:"名称"}}),n("el-table-column",{attrs:{prop:"Remark",label:"备注"}}),n("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.handleAddEditClick("edit",a)}}},[e._v("编辑")])]}}])})],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)],1),n("add-edit-dialog",{ref:"dialog",on:{update:function(t){return e.fetchDate()}}})],1)},i=[]},"64bed":function(e,t,n){"use strict";n("e9f8")},"688e9":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("5530"));n("d9e2"),n("7db0"),n("a15b"),n("e9f5"),n("f665"),n("a9e3"),n("d3b7");var l=n("6186"),o=(n("f382"),n("c24f"),n("dbde"));t.default={props:{tableTopTadio:{type:String,default:"pc"}},data:function(){return{dialogVisible:!1,btnLoading:!1,formInline:{Parent_Module:"",Page_Type:1,Menu_Type:"",Business_Scope:[],Parent_Id:"",Code:"",No_Cache:!1,Display_Name:"",Icon:"",Sort:"",Remark:"",Url_Address:"",Is_Line:!1,Is_Enabled:!0,Component:""},Option:[],typeOption:["项目级","公司级","工厂级","仓库级"],treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Display_Name",value:"Id"}},targetArray:[{label:"菜单",value:0},{label:"页面",value:1}],value:"",title:"",rules:{Parent_Module:[{required:!0,message:"请选择所属模块",trigger:"change"}],Display_Name:[{required:!0,message:"请输入菜单名称",trigger:"blur"}],Code:[{required:!0,message:"请输入菜单编码",trigger:"blur"}],Business_Scope:[{required:!0,message:"请至少选择一个业务范围",trigger:"change"}],Menu_Type:[{required:!0,message:"请选择菜单类型",trigger:"change"}],Url_Address:[{required:!0,message:"请输入菜单编码",trigger:"blur"}]},productlist:[],moduleList:[],tableData:[],isView:!1,MenuId:"",moduleBusinessScope:["公司","工厂","项目"]}},created:function(){var e=this;(0,l.GetDictionaryDetailListByCode)({dictionaryCode:"platform"}).then((function(t){e.Option=t.Data}))},methods:{changeModule:function(e){var t=this;this.formInline.Parent_Id="",this.getModuleBusinessScope(e),e?this.getMenuList(e):(this.treeParams.data=[],this.$nextTick((function(e){t.$refs.treeSelect.treeDataUpdateFun([])})))},clearModule:function(){},getModuleBusinessScope:function(e){var t=this.moduleList.find((function(t){return t.Id===e}));this.moduleBusinessScope=t.Business_Scope.split("/")},submitForm:function(e){var t=this;this.$refs[e].validate((function(n){if(!n)return!1;var a=(0,i.default)({},t.formInline);delete a["Business_Scope"],a.Business_Scope=t.formInline.Business_Scope.join("/"),a.Sort=t.formInline.Sort?Number(t.formInline.Sort):null,t.btnLoading=!0,t.MenuId?(0,o.UpdateMenu)((0,i.default)((0,i.default)({},a),{},{Id:t.MenuId})).then((function(n){n.IsSucceed?(t.$emit("getMenuList"),t.$refs[e].resetFields(),t.dialogVisible=!1,t.$message({message:"保存成功",type:"success"})):t.$message({message:n.Message,type:"error"}),t.btnLoading=!1})):(0,o.AddMenu)(a).then((function(n){n.IsSucceed?(t.$emit("getMenuList"),t.$refs[e].resetFields(),t.dialogVisible=!1,t.$message({message:"保存成功",type:"success"})):t.$message({message:n.Message,type:"error"}),t.btnLoading=!1}))}))},handleCancel:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1},formData:function(){},handleOpen:function(e,t,n){var a=this;this.moduleList=t,this.formInline.Page_Type="pc"===this.tableTopTadio?1:2,"edit"===n||"view"===n?(this.MenuId=e.Id,(0,o.GetMenuEntity)({MenuId:e.Id}).then((function(e){if(e.IsSucceed){var t=e.Data,n=t.Parent_Module,i=t.Menu_Type,l=t.Business_Scope,o=t.Parent_Id,r=t.Code,s=t.No_Cache,d=t.Display_Name,u=t.Icon,c=t.Sort,f=t.Remark,m=t.Url_Address,p=t.Is_Line,b=t.Is_Enabled,h=t.Component;a.formInline.Parent_Module=n,a.formInline.Menu_Type=i,a.formInline.Business_Scope=l.split("/"),a.formInline.Parent_Id=o,a.formInline.No_Cache=s,a.formInline.Display_Name=d,a.formInline.Icon=u,a.formInline.Sort=c,a.formInline.Remark=f,a.formInline.Code=r,a.formInline.Url_Address=m,a.formInline.Is_Line=p,a.formInline.Is_Enabled=b,a.formInline.Component=h,a.getMenuList(n),a.getModuleBusinessScope(n)}else a.$message({message:e.Message,type:"error"})}))):this.MenuId="",this.isView="view"===n,this.dialogVisible=!0},handleClose:function(e){this.$refs.ruleForm.resetFields(),e()},getMenuList:function(e){var t=this;(0,o.GetMenuList)({ModuleId:e,Page_Type:this.tableTopTadio||"pc"}).then((function(e){t.tableData=e.Data,t.treeParams.data=t.tableData,t.$nextTick((function(e){t.$refs.treeSelect.treeDataUpdateFun(t.tableData)}))}))}}}},"6b3b":function(e,t,n){"use strict";n.r(t);var a=n("5a21"),i=n("1c88");for(var l in i)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(l);n("64bed");var o=n("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"36b291b0",null);t["default"]=r.exports},"6e69":function(e,t,n){},8824:function(e,t,n){"use strict";n.r(t);var a=n("9a56"),i=n.n(a);for(var l in a)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(l);t["default"]=i.a},"8fe4":function(e,t,n){"use strict";n.r(t);var a=n("b3e4"),i=n.n(a);for(var l in a)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(l);t["default"]=i.a},90966:function(e,t,n){"use strict";n.r(t);var a=n("61fb"),i=n("bb49");for(var l in i)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(l);n("a120");var o=n("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"2edbe208",null);t["default"]=r.exports},"94d7":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={inject:{AuthButtons:{default:function(){return{}}}}}},"9a56":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("90966")),l=a(n("94d7")),o=n("dbde");t.default={components:{BtnDialog:i.default},mixins:[l.default],props:{tableTopTitle:{type:String,default:""},tbLoading:{type:Boolean,default:!0},tableData:{type:Array,default:function(){return[]}},rootId:{type:String,default:""}},data:function(){return{pages:{title:0,page:"1",pageSize:"10"},tableTitle:[{name:"菜单名称",id:1,en:"Display_Name"},{name:"菜单编码",id:2,en:"Code"},{name:"排序",id:3,en:"Sort",width:"80px"},{name:"URL地址",id:4,en:"Url_Address"},{name:"菜单类型",id:5,en:"Menu_Type"},{name:"业务范围",id:6,en:"Business_Scope"},{name:"是否有效",id:7,en:"Is_Enabled"},{name:"图标",id:8,en:"Icon",width:"80px"}],authBtns:[],tableTopTadio:"pc",filterText:""}},watch:{tableTopTadio:function(e){this.$emit("getableTopTadio",e),this.$emit("getMenuList")}},methods:{getTableList:function(){this.$emit("getMenuList")},handleDetail:function(e){this.$emit("rowDataView",e)},handleEdit:function(e){this.$emit("rowData",e)},handleDelete:function(e){var t=this;this.$confirm("是否删除该菜单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.DelMenu)({Id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.$emit("getMenuList")):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handAdd:function(){this.$emit("rowDataAdd")},handleBtnEdit:function(e){this.$refs.btnDialog.handleOpen(e)}}}},a120:function(e,t,n){"use strict";n("f7b2")},b399:function(e,t,n){"use strict";n("c004")},b3e4:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n("6186");t.default={data:function(){return{title:"",dialogVisible:!1,form:{Id:"",Menu_Id:"",Code:"",Display_Name:"",Remark:""},submitForm:null,btnLoading:!1,rules:{Code:[{required:!0,message:"编码不能为空",trigger:"blur"}],Display_Name:[{required:!0,message:"名称不能为空",trigger:"blur"}]}}},methods:{handleOpen:function(e,t){var n=this;this.dialogVisible=!0,"add"===e?(this.$delete(this.form,"Id"),this.form.Menu_Id=t.menuId,this.title="添加"):(this.title="编辑",this.$nextTick((function(e){Object.assign(n.form,t)})))},handleClose:function(){this.resetForm("form")},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,a.UpdateButton)(e.form).then((function(t){t.IsSucceed&&(e.$emit("update"),e.$message({message:"编辑成功",type:"success"}),e.dialogVisible=!1),e.btnLoading=!1}))}))},resetForm:function(){this.$refs["form"].resetFields(),this.dialogVisible=!1}}}},b6ee:function(e,t,n){"use strict";n("6e69")},bb49:function(e,t,n){"use strict";n.r(t);var a=n("fb8d"),i=n.n(a);for(var l in a)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(l);t["default"]=i.a},c004:function(e,t,n){},c29f:function(e,t,n){"use strict";n.r(t);var a=n("eb27"),i=n("609b");for(var l in i)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(l);n("b399");var o=n("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"8f446380",null);t["default"]=r.exports},cba2:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"编码",prop:"Code"}},[n("el-input",{model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[n("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1),n("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[n("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"50",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit()}}},[e._v("确 定")])],1)],1)},i=[]},d091:function(e,t,n){"use strict";n.r(t);var a=n("cba2"),i=n("8fe4");for(var l in i)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(l);var o=n("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"469c5f5c",null);t["default"]=r.exports},dbde:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddMenu=u,t.AddModule=l,t.DelMenu=f,t.DelModule=r,t.GetMenuEntity=m,t.GetMenuList=d,t.GetModuleList=o,t.SetModuelIndexPage=p,t.UpdateMenu=c,t.UpdateModule=s;var i=a(n("b775"));function l(e){return(0,i.default)({url:"/Platform/Menu/AddModule",method:"post",data:e})}function o(e){return(0,i.default)({url:"/Platform/Menu/GetModuleList",method:"post",data:e})}function r(e){return(0,i.default)({url:"/Platform/Menu/DelModule",method:"post",data:e})}function s(e){return(0,i.default)({url:"/Platform/Menu/UpdateModule",method:"post",data:e})}function d(e){return(0,i.default)({url:"/Platform/Menu/GetMenuList",method:"post",data:e})}function u(e){return(0,i.default)({url:"/Platform/Menu/AddMenu",method:"post",data:e})}function c(e){return(0,i.default)({url:"/Platform/Menu/UpdateMenu",method:"post",data:e})}function f(e){return(0,i.default)({url:"/Platform/Menu/DelMenu",method:"post",data:e})}function m(e){return(0,i.default)({method:"get",url:"/Platform/Menu/GetMenuEntity",params:e})}function p(e){return(0,i.default)({url:"/Platform/Menu/SetModuelIndexPage",method:"post",data:e})}},dca8:function(e,t,n){"use strict";var a=n("23e7"),i=n("bb2f"),l=n("d039"),o=n("861d"),r=n("f183").onFreeze,s=Object.freeze,d=l((function(){s(1)}));a({target:"Object",stat:!0,forced:d,sham:!i},{freeze:function(e){return s&&o(e)?s(r(e)):e}})},e9f8:function(e,t,n){},eb27:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-row",{staticClass:"h100",attrs:{gutter:15,type:"flex"}},[n("el-col",{attrs:{span:5}},[n("el-card",{staticClass:"box-card h100 cs-scroll"},[n("div",[e._v("模块列表")]),n("el-divider"),n("el-input",{attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),n("el-divider"),n("el-tree",{ref:"tree",attrs:{data:e.treeData,"node-key":"Id","default-expand-all":"","expand-on-click-node":!1,props:e.defaultProps,"filter-node-method":e.filterNode},on:{"current-change":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,i=t.data;return n("span",{staticClass:"custom-tree-node"},[n("span",[e._v(e._s(a.label))]),n("span",[e._v(e._s(i.Business_Scope))])])}}])})],1)],1),n("el-col",{attrs:{span:19}},[n("z-table",{attrs:{"root-id":e.rootId,"table-data":e.tableData,"table-top-title":e.tableTopTitle,"tb-loading":e.tableLoading},on:{getMenuList:e.getMenuList,rowData:e.handleEdit,rowDataAdd:e.handleAdd,rowDataView:e.handleDetail,getableTopTadio:e.getableTopTadio}})],1)],1),n("z-dialog",{ref:"dialog",attrs:{"row-data":e.rowData,"table-top-tadio":e.tableTopTadio},on:{getMenuList:e.getMenuList,updateTable:e.getTreeList}})],1)},i=[]},f382:function(e,t,n){"use strict";function a(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=a(e.Children)),!0)}))}function i(e){e.map((function(e){if(e.Is_Directory||!e.Children)return i(e.Children);delete e.Children}))}function l(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function a(e,t,i){for(var l=0;l<e.length;l++){var o=e[l];if(o.Id===t)return n&&i.push(o),i;if(o.Children&&o.Children.length){if(i.push(o),a(o.Children,t,i).length)return i;i.pop()}}return[]}return a(e,t,[])}function o(e){return e.Children&&e.Children.length>0?o(e.Children[0]):e}function r(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&r(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=i,t.disableDirectory=r,t.findAllParentNode=l,t.findFirstNode=o,t.getDirectoryTree=a,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7")},f7b2:function(e,t,n){},fb8d:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n("6186"),l=a(n("d091"));t.default={components:{AddEditDialog:l.default},data:function(){return{dialogVisible:!1,tableData:[],title:"",menuId:null}},methods:{fetchDate:function(){var e=this;(0,i.GetButtonList)({menuId:this.menuId}).then((function(t){e.tableData=t.Data}))},handleOpen:function(e){this.menuId=e.Id,this.fetchDate(),this.title=e.Display_Name+" | 按钮配置",this.dialogVisible=!0},handleClose:function(){},handleAddEditClick:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"add"===e&&(t.menuId=this.menuId),this.$refs.dialog.handleOpen(e,t)}}}}}]);