(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3441760e"],{"00e9":function(t,e,a){"use strict";a.r(e);var i=a("3066"),s=a.n(i);for(var c in i)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(c);e["default"]=s.a},"256b":function(t,e,a){"use strict";a.r(e);var i=a("637c"),s=a("00e9");for(var c in s)["default"].indexOf(c)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(c);a("991b");var n=a("2877"),l=Object(n["a"])(s["default"],i["a"],i["b"],!1,null,"5b8ba640",null);e["default"]=l.exports},3066:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=i(a("5530")),c=i(a("2909"));a("99af"),a("4de4"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("2532"),a("159b");var n=a("db0a"),l=i(a("80fb")),u=a("ed08"),r=i(a("6612")),o=i(a("89cc"));e.default={components:{Empty:o.default,ItemInfo:l.default},data:function(){return{activeName:"",fcId:"",loading:!1,baseKeyList:{},curProcessList:[],factoryList:[],outFactoryList:[],curProcessOutList:[],processList:[],processFactoryList:[],processOutList:[]}},mounted:function(){this.fetchDefaultData()},methods:{fetchDefaultData:function(){var t=this;this.loading=!0,(0,n.GetAccountingDefaultSetting)({}).then((function(e){if(e.IsSucceed){var a=e.Data,i=a.Base_Setting,s=a.Factory_Process_Setting_List,c=a.External_Factory_List,n=a.Factory_List;t.factoryList=[],n.forEach((function(e,a){e.Is_External||t.factoryList.push(e)})),t.initTb({Base_Setting:i,Factory_Process_Setting_List:s,External_Factory_List:c})}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1}))},initTb:function(t){var e=this,a=t.Base_Setting,i=t.Factory_Process_Setting_List,s=t.External_Factory_List;this.outFactoryList=[],this.processList=[],this.processOutList=[];var c=[];i.forEach((function(t,a){var i={value1:t.Process_Name,label1:"工序名称",label2:"人工单价(元)",value2:t.Unit_Price,key:"v"+a,factoryId:t.Factory_Id,curItem:t};c.push(t.Factory_Id),t.Is_External?e.processOutList.push(i):e.processList.push(i)})),this.processFactoryList=(0,u.uniqueArr)(c),this.factoryList.length&&(this.activeName=this.factoryList[0].Id,this.filterProcess(),this.filterOutProcess()),s.forEach((function(t,a){e.outFactoryList.push({value1:t.Factory_Name,label1:"外协工厂名称",value2:t.Material_Unit_Price,label2:"外协材料单价(元)",value3:t.Labor_Unit_Price,label3:"外协人工单价(元)",key:a,curItem:t})})),this.Base_Setting=a,a.forEach((function(t,a){e.setBase(t)}))},setBase:function(t){if(t){var e=[{value:(0,r.default)(t["Product_Output_Tax_Rate"]).multiply(100).value(),label:"产品产值税率(%)",code:"Product_Output_Tax_Rate"},{value:(0,r.default)(t["Labor_Output_Tax_Rate"]).multiply(100).value(),label:"劳务产值税率(%)",code:"Labor_Output_Tax_Rate"},{value:(0,r.default)(t["Loss_Rate"]).multiply(100).value(),label:"损耗率(%)",code:"Loss_Rate"},{value:t["Material_Allocate_Unit_Price"],label:"物控调拨单价(元)",code:"Material_Allocate_Unit_Price"},{value:t["Management_Fee_Self_Supplying"],label:"主材管理费(元)",code:"Management_Fee_Self_Supplying"},{value:t["Process_Allocate_Unit_Price"],label:"加工调拨单价(元)",code:"Process_Allocate_Unit_Price"},{value:t["Difficulty_Coefficient"],label:"该项目难度系数",code:"Difficulty_Coefficient"},{value:t["Manage_Fee_Coefficient"],label:"项目管理费系数",code:"Manage_Fee_Coefficient"}];this.$set(this.baseKeyList,t.Factory_Id,e)}},filterProcess:function(){var t=this;this.curProcessList=this.processList.filter((function(e){return e.factoryId===t.activeName}))},filterOutProcess:function(){var t=this;this.curProcessOutList=this.processOutList.filter((function(e){return e.factoryId===t.activeName}))},filterBaseSetting:function(){},handleSave:function(){var t=this;this.$confirm("是否提交当前数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=[],a=!0;if(t.factoryList.forEach((function(i,s){if(t.processFactoryList.includes(i.Id)){var c={Factory_Id:i.Id},n=t.baseKeyList[i.Id];n.forEach((function(e){["Product_Output_Tax_Rate","Labor_Output_Tax_Rate","Loss_Rate"].includes(e.code)?c[e.code]=(0,r.default)(e.value).divide(100).value():c[e.code]=e.value,t.isEmpty(c[e.code])&&(a=!1)})),e.push(c)}})),a){var i=[].concat((0,c.default)(t.processOutList),(0,c.default)(t.processList)).map((function(e){var i=e.curItem,c=e.value2;return t.isEmpty(c)&&(a=!1),(0,s.default)((0,s.default)({},i),{},{Unit_Price:c})}));if(a){var l=t.outFactoryList.map((function(e){var i=e.curItem,c=e.value2,n=e.value3;return(t.isEmpty(c)||t.isEmpty(n))&&(a=!1),(0,s.default)((0,s.default)({},i),{},{Material_Unit_Price:c,Labor_Unit_Price:n})}));if(a){var u={Base_Setting:e,Factory_Process_Setting_List:i,External_Factory_List:l,Sys_Project_Id:t.curProject};(0,n.SaveDefaultAccountingSetting)(u).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchDefaultData()):t.$message({message:e.Message,type:"error"})}))}else t.$message({message:"请将外协工厂配置项填写完整!",type:"warning"})}else t.$message({message:"请将生产配置项填写完整!",type:"warning"})}else t.$message({message:"请将基础配置项填写完整!",type:"warning"})})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleTabClick:function(t){this.filterProcess(),this.filterOutProcess()},isEmpty:function(t){return[null,void 0,""].includes(t)},toBack:function(){(0,u.closeTagView)(this.$store,this.$route)}}}},"637c":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return s}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"cs-z-page-main-content"},[a("div",{staticClass:"right-main"},[a("div",{staticClass:"btn-x"},[a("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:t.toBack}},[t._v("返 回 ")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleSave}},[t._v("保 存 ")])],1),a("el-tabs",{attrs:{type:"card"},on:{"tab-click":t.handleTabClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.factoryList,(function(t){return a("el-tab-pane",{key:t.Id,attrs:{label:t.Short_Name,name:t.Id}})})),1),t.processFactoryList.includes(t.activeName)?[a("h4",[t._v("基础配置项")]),a("div",{staticClass:"items-x"},t._l(t.baseKeyList[t.activeName],(function(e){return a("item-info",{key:e.label,attrs:{value:e.value,width:"33%",label:e.label},on:{"update:value":function(a){return t.$set(e,"value",a)}}})})),1)]:a("empty",{attrs:{type:t.activeName?2:1}}),[t.curProcessList.length?a("div",{staticClass:"p-box"},[a("h5",{staticClass:"p-box-title"},[t._v("生产配置项")]),t._l(t.curProcessList,(function(e){return a("div",{key:e.key,staticClass:"plist"},[a("item-info",{attrs:{disabled:"",width:"50%",label:e.label1,value:e.value1},on:{"update:value":function(a){return t.$set(e,"value1",a)}}}),a("item-info",{attrs:{width:"50%",label:e.label2,value:e.value2},on:{"update:value":function(a){return t.$set(e,"value2",a)}}})],1)}))],2):t._e(),t.curProcessOutList.length?a("div",{staticClass:"p-box"},[a("h5",{staticClass:"p-box-title"},[t._v("外协工序配置项")]),t._l(t.curProcessOutList,(function(e){return a("div",{key:e.key,staticClass:"plist"},[a("item-info",{attrs:{width:"50%",disabled:"",label:e.label1,value:e.value1},on:{"update:value":function(a){return t.$set(e,"value1",a)}}}),a("item-info",{attrs:{width:"50%",label:e.label2,value:e.value2},on:{"update:value":function(a){return t.$set(e,"value2",a)}}})],1)}))],2):t._e()],t.outFactoryList.length?a("div",{staticClass:"p-box"},[a("el-divider"),a("h4",{staticClass:"p-box-title"},[t._v("外协工厂配置项")]),t._l(t.outFactoryList,(function(e){return a("div",{key:e.key,staticClass:"plist"},[a("item-info",{attrs:{disabled:"",width:"33%",label:e.label1,value:e.value1},on:{"update:value":function(a){return t.$set(e,"value1",a)}}}),a("item-info",{attrs:{"is-default":e.isDefault1,width:"33%",label:e.label2,value:e.value2},on:{"update:value":function(a){return t.$set(e,"value2",a)}}}),a("item-info",{attrs:{"is-default":e.isDefault2,width:"33%",label:e.label3,value:e.value3},on:{"update:value":function(a){return t.$set(e,"value3",a)}}})],1)}))],2):t._e()],2)])])},s=[]},"991b":function(t,e,a){"use strict";a("a7b8")},a7b8:function(t,e,a){}}]);