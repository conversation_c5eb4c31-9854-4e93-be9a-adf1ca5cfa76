(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-1e424911"],{"0145":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"container",attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[n("div",{staticClass:"info-x"},[e.isProjectList?[n("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,inline:"","label-width":"80px"}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Short_Name"}},[n("el-input",{attrs:{placeholder:"请输入",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.form.Short_Name,callback:function(t){e.$set(e.form,"Short_Name",t)},expression:"form.Short_Name"}})],1),n("el-form-item",{attrs:{label:"类型",prop:"Type"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[n("custom-option",{attrs:{"option-label":"Display_Name","option-value":"Value",options:e.ProjectType}})],1)],1),n("el-form-item",{attrs:{label:"项目状态",prop:"Status"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},[n("custom-option",{attrs:{"option-label":"Display_Name","option-value":"Value",options:e.statusOption}})],1)],1),n("el-form-item",{attrs:{label:"优先级",prop:"Level"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Level,callback:function(t){e.$set(e.form,"Level",t)},expression:"form.Level"}},[n("custom-option",{attrs:{"option-label":"Display_Name","option-value":"Value",options:e.levelOption}})],1)],1),n("el-form-item",{attrs:{label:"项目来源",prop:"Bind_Type"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Bind_Type,callback:function(t){e.$set(e.form,"Bind_Type",t)},expression:"form.Bind_Type"}},[n("el-option",{attrs:{label:"自建",value:"自建"}}),n("el-option",{attrs:{label:"推送绑定",value:"推送绑定"}})],1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),n("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)]:e._e(),n("div",{directives:[{name:"show",rawName:"v-show",value:e.isProjectList,expression:"isProjectList"}],staticClass:"info-inner"},[n("el-button",{attrs:{type:"primary",disabled:e.Is_Integration},on:{click:e.handleAdd}},[e._v("新建")]),e.isProjectList?[n("span",[e._v("项目总数: "+e._s(e.Project_Count.Total_Count))]),n("span",[e._v("待安装项目数: "+e._s(e.Project_Count.Build_Count))]),n("span",[e._v("纯制作项目数: "+e._s(e.Project_Count.Self_Count))])]:e._e()],2),n("div",{key:e.isProjectList?0:1,staticClass:"fff cs-z-tb-wrapper"},[n("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch},scopedSlots:e._u([{key:"Status",fn:function(t){var a=t.row;return[n("div",[e._v(" "+e._s(e.getProjectStatus(a))+" ")])]}},{key:"Type",fn:function(t){var n=t.row;return[e._v(" "+e._s(e.getTypeName(n))+" ")]}},{key:"Plan_Begin_Date",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Plan_Begin_Date))+" "),a.Actual_Begin_Date?n("span",[e._v("~")]):e._e(),e._v(" "+e._s(e._f("timeFormat")(a.Plan_End_Date))+" ")]}},{key:"Actual_Begin_Date",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Actual_Begin_Date))+" "),a.Actual_Begin_Date?n("span",[e._v("~")]):e._e(),e._v(" "+e._s(e._f("timeFormat")(a.Actual_End_Date))+" ")]}},{key:"Bind_Type",fn:function(t){var a=t.row;return[n("div",[e._v(" "+e._s(a.Bind_Type)+" ")])]}},{key:"Contact_UserName_Bak",fn:function(t){var a=t.row;return[n("el-tooltip",{attrs:{effect:"dark",content:a.Contact_UserName_Tel,placement:"bottom"}},[n("div",[e._v(" "+e._s(a.Contact_UserName_Bak)+" ")])])]}},{key:"Plan_End_Date",fn:function(t){var a=t.row;return[n("div",[e._v(" "+e._s(e._f("timeFormat")(a.Plan_End_Date))+" ")])]}},{key:"op",fn:function(t){var a=t.row;return[e.isProjectList?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(a)}}},[e._v("详情")]):e._e(),e.isProjectList?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetailEdit(a)}}},[e._v("编辑")]):e._e(),e.isProjectList?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleUnit(a)}}},[e._v("区域")]):e._e(),e.isProjectList?e._e():n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(a)}}},[e._v("管理")]),e.Is_Integration?e._e():n("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(a.Id)}}},[e._v("删除")]),e.isProjectList?e._e():n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleBind(a)}}},[e._v("绑定 ")]),n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleChangeOrder(a.Sys_Project_Id)}}},[e._v("变更单 ")])]}}])})],1)],2),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"项目绑定",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:function(t){e.dialogVisible=!1}}},[n("label",[e._v("选择项目 "),n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.bandingProject,callback:function(t){e.bandingProject=t},expression:"bandingProject"}},e._l(e.bandingOptions,(function(e){return n("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Name,value:e.Sys_Project_Id}})})),1)],1),n("div",{staticStyle:{"margin-top":"20px","text-align":"right"}},[n("el-button",{on:{click:e.cancel}},[e._v("取消")]),n("el-button",{attrs:{disabled:!e.bandingProject,type:"primary",loading:e.bindLoading},on:{click:e.bindSubmit}},[e._v("确定")])],1)]),n("el-drawer",{attrs:{title:"变更历史",visible:e.drawerVisible,direction:"rtl","wrapper-closable":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.drawerVisible=t}}},[0!==e.changeOrderList.length?n("div",{staticClass:"change-order"},e._l(e.changeOrderList,(function(t){return n("div",{key:t.Id,staticClass:"change-order-item"},[n("div",[n("span",[e._v("项目名称：")]),n("span",[e._v(e._s(t.Project_Name))])]),n("div",[n("span",[e._v("变更单号：")]),n("span",[e._v(e._s(t.Bill_No))])]),n("div",[n("span",[e._v("发起时间：")]),n("span",[e._v(e._s(t.Launch_Date))])]),n("div",[n("span",[e._v("发起人：")]),n("span",[e._v(e._s(t.Create_UserName))])]),n("div",[n("span",[e._v("审批人：")]),n("span",[e._v(e._s(t.Approve_UserName))])]),n("div",[n("span",[e._v("通过时间：")]),n("span",[e._v(e._s(t.Approve_Date))])])])})),0):e._e()])],1)},r=[]},"02c5d":function(e,t,n){"use strict";n.r(t);var a=n("7e45"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},"15ac":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),r=n("c685");t.default={methods:{getTableConfig:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,a.GetGridByCode)({code:e,IsAll:n}).then((function(e){var a=e.IsSucceed,i=e.Data,u=e.Message;if(a){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var d=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),d=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=d.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||r.tablePageSize[0]),o(t.columns)}else t.$message({message:u,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit,a=e.type;this.queryInfo.Page="limit"===a?1:t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var r=this.columns[a];if(r.Code===t){n.Type=r.Type,n.Filter_Type=r.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"2a92":function(e,t,n){"use strict";n.r(t);var a=n("0145"),r=n("02c5d");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("b8f3");var i=n("2877"),u=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"587e395e",null);t["default"]=u.exports},3166:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=h,t.DeleteProject=c,t.GeAreaTrees=b,t.GetFileSync=L,t.GetInstallUnitIdNameList=O,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=j,t.GetProjectAreaTreeList=v,t.GetProjectEntity=d,t.GetProjectList=u,t.GetProjectPageList=i,t.GetProjectTemplate=g,t.GetPushProjectPageList=y,t.GetSchedulingPartList=S,t.IsEnableProjectMonomer=l,t.SaveProject=s,t.UpdateProjectTemplateBase=m,t.UpdateProjectTemplateContacts=P,t.UpdateProjectTemplateContract=C,t.UpdateProjectTemplateOther=_;var r=a(n("b775")),o=a(n("4328"));function i(e){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(e)})}function s(e){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(e)})}function l(e){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function L(e){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"4e82":function(e,t,n){"use strict";var a=n("23e7"),r=n("e330"),o=n("59ed"),i=n("7b0b"),u=n("07fa"),d=n("083a"),s=n("577e"),c=n("d039"),l=n("addb"),f=n("a640"),h=n("3f7e"),p=n("99f4"),g=n("1212"),m=n("ea83"),P=[],C=r(P.sort),_=r(P.push),y=c((function(){P.sort(void 0)})),v=c((function(){P.sort(null)})),O=f("sort"),b=!c((function(){if(g)return g<70;if(!(h&&h>3)){if(p)return!0;if(m)return m<603;var e,t,n,a,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)P.push({k:t+a,v:n})}for(P.sort((function(e,t){return t.v-e.v})),a=0;a<P.length;a++)t=P[a].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),j=y||!v||!O||!b,S=function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:s(t)>s(n)?1:-1}};a({target:"Array",proto:!0,forced:j},{sort:function(e){void 0!==e&&o(e);var t=i(this);if(b)return void 0===e?C(t):C(t,e);var n,a,r=[],s=u(t);for(a=0;a<s;a++)a in t&&_(r,t[a]);l(r,S(e)),n=u(r),a=0;while(a<n)t[a]=r[a++];while(a<s)d(t,a++);return t}})},"7015f":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=O,t.AgainSubmitChangeOrder=y,t.BatchReuseEngineeringContactChangedComponentPart=ie,t.BatchReuseEngineeringContactMocComponentPart=ue,t.CancelChangeOrder=_,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=x,t.DeleteChangeOrder=l,t.DeleteChangeOrderV2=w,t.DeleteChangeReason=g,t.DeleteChangeType=u,t.DeleteEngineeringContactChangeOrder=J,t.DeleteMocOrder=Y,t.DeleteMocType=Ce,t.ExportEngineeringContactChangedAddComponentPart=ce,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=re,t.ExportMocAddComponentPart=le,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=j,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=s,t.GetChangeOrderTaskInfo=I,t.GetChangeOrderTaskPageList=R,t.GetChangeOrderV2=$,t.GetChangeOrderV2PageList=E,t.GetChangeReason=h,t.GetChangeType=o,t.GetChangedComponentPartPageList=F,t.GetChangedComponentPartProductionList=V,t.GetCompAndPartSchdulingPageList=T,t.GetCompAndPartTaskList=D,t.GetCompanyUserPageList=k,t.GetEngineeringContactChangeOrder=K,t.GetEngineeringContactChangeOrderPageList=U,t.GetEngineeringContactChangedAddComponentPartPageList=de,t.GetEngineeringContactChangedAddComponentPartSummary=se,t.GetEngineeringContactChangedComponentPartPageList=X,t.GetEngineeringContactChangedSummary=W,t.GetEngineeringContactFileInfo=z,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=Q,t.GetEngineeringContactMocSummary=ae,t.GetFactoryChangeTypeListV2=q,t.GetFactoryPeoplelist=d,t.GetMocAddComponentPartPageList=he,t.GetMocModelList=ye,t.GetMocOrderInfo=me,t.GetMocOrderPageList=pe,t.GetMocOrderTypeList=Pe,t.GetMyChangeOrderPageList=C,t.GetProjectAreaChangeTreeList=N,t.GetProjectChangeOrderList=L,t.GetTypeReason=m,t.ImportChangFile=ge,t.ImportChangeDeependFile=P,t.QueryHistories=b,t.ReuseEngineeringContactChangedComponentPart=ne,t.ReuseEngineeringContactMocComponentPart=oe,t.SaveChangeOrder=c,t.SaveChangeOrderTask=G,t.SaveChangeOrderV2=M,t.SaveChangeReason=p,t.SaveChangeType=i,t.SaveEngineeringContactChangeOrder=H,t.SaveMocOrder=ve,t.SaveMocOrderType=_e,t.SubmitChangeOrder=v,t.SubmitChangeOrderV2=B,t.SubmitMocOrder=A,t.Verification=S;var r=a(n("b775"));a(n("4328"));function o(e){return(0,r.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function b(e){return(0,r.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function j(e){return(0,r.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function k(e){return(0,r.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function _e(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function ye(e){return(0,r.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},"7e27":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7");t.default={functional:!0,props:{options:{type:Array,default:function(){return[]}},optionValue:{type:String,default:""},optionLabel:{type:String,default:""}},render:function(e,t){var n=t.props;return n.options.map((function(t,a){return e("el-option",{attrs:{value:t[n.optionValue],label:t[n.optionLabel]}})}))}}},"7e45":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("c14f")),o=a(n("1da1"));n("7db0"),n("d81d"),n("14d9"),n("b0c0"),n("e9f5"),n("f665"),n("ab43"),n("d3b7"),n("3ca3"),n("ddb0");var i=a(n("0f97")),u=a(n("15ac")),d=a(n("2082")),s=n("3166"),c=n("de45"),l=n("cf45"),f=a(n("ca67")),h=n("7015f"),p=n("ed08");t.default={name:"PROProjectList",components:{DynamicDataTable:i.default,CustomOption:f.default},mixins:[u.default,d.default],data:function(){return{drawerVisible:!1,changeOrderList:[],isActive:1,form:{Code:"",Short_Name:"",Level:"",Type:"",Status:"",Bind_Type:""},tbConfig:{},bandingProject:"",bandingOptions:[],levelOption:c.PRO_STATUS,pgLoading:!1,dialogVisible:!1,bindLoading:!1,selectList:[],queryInfo:{Page:1,PageSize:10},columns:[],tbData:[],total:0,addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-16ba4d99"),n.e("chunk-226d2b16")]).then(n.bind(null,"e1ae"))},name:"PROProjectListAdd",meta:{title:"新增"}},{path:this.$route.path+"/edit",hidden:!0,component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-16ba4d99"),n.e("chunk-5fd0ed43")]).then(n.bind(null,"5c37"))},name:"PROProjectListEdit",meta:{title:"编辑"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return n.e("chunk-4c17a115").then(n.bind(null,"3118"))},name:"PROProjectListDetail",meta:{title:"详情"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-16ba4d99"),n.e("chunk-5fd0ed43")]).then(n.bind(null,"5c37"))},name:"PROProjectListDetail",meta:{title:"编辑"}},{path:this.$route.path+"/unit",hidden:!0,component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-29736023")]).then(n.bind(null,"674d"))},name:"PROProjectListUnit",meta:{title:"区域"}}],projectName:localStorage.getItem("ProjectName"),styleObject:{transform:"translateX(".concat(0,")")},Project_Count:{Total_Count:0,Build_Count:0,Self_Count:0},ProjectType:[],statusOption:[],ProjectStatus:[],Is_Integration:!1}},computed:{isProjectList:function(){return 1===this.isActive},onlyYzyDo:function(){return"yzy"===this.$store.getters.name&&!1}},activated:function(){this.fetchData(1)},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:e.Is_Integration=t.v;case 2:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,e.tableList={},e.pgLoading=!0,t.n=1,e.getTbConfigInfo();case 1:return e.fetchData(1),t.n=2,(0,l.getDictionary)("ProjectType");case 2:return e.ProjectType=t.v,t.n=3,(0,l.getDictionary)("ProjectStatus");case 3:return e.statusOption=t.v,t.n=4,(0,l.getDictionary)("ProjectLevel");case 4:return e.levelOption=t.v,t.n=5,(0,l.getDictionary)("ProjectStatus");case 5:e.ProjectStatus=t.v,t.n=7;break;case 6:t.p=6,t.v,e.pgLoading=!1;case 7:return t.a(2)}}),t,null,[[0,6]])})))()},methods:{getTbConfigInfo:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var n,a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(n=e.isProjectList?"pro_project_list":"PlmInviteProjecList",a=e.tableList[n],!a){t.n=1;break}e.tbConfig=a.tbConfig,e.columns=a.columns,e.queryInfo.PageSize=a.PageSize,t.n=3;break;case 1:return t.n=2,e.getTableConfig(n,{config:{Is_Filter:!1}});case 2:e.tableList[n]={tbConfig:e.tbConfig,columns:e.columns,PageSize:e.queryInfo.PageSize};case 3:return t.a(2)}}),t)})))()},fetchData:function(e){var t,n=this;this.pgLoading=!0,e&&(this.queryInfo.Page=e),t=this.isProjectList?s.GetProjectPageList:s.GetPushProjectPageList,t(this.queryInfo).then((function(e){var t,a;e.IsSucceed?(n.tbData=e.Data.Data,n.total=null===(t=e.Data)||void 0===t?void 0:t.TotalCount,n.isProjectList&&(n.Project_Count=null===(a=e.Data)||void 0===a?void 0:a.Project_Count)):(n.$message({message:e.Message,type:"error"}),n.tbData=[],n.total=0)})).finally((function(){n.$nextTick((function(e){n.pgLoading=!1}))}))},getTypeName:function(e){var t;return null===(t=this.ProjectType.find((function(t){return t.Value===e.Type})))||void 0===t?void 0:t.Display_Name},getProjectStatus:function(e){var t;return null===(t=this.ProjectStatus.find((function(t){return t.Value===e.Status})))||void 0===t?void 0:t.Display_Name},changeTab:function(e){var t=this;return(0,o.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return t.pgLoading=!0,t.isActive=e,t.styleObject.transform=1===e?"translateX(".concat(0,")"):"translateX(".concat(140,"px)"),n.n=1,t.getTbConfigInfo();case 1:t.fetchData(1);case 2:return n.a(2)}}),n)})))()},handleBind:function(e){var t=this;this.dialogVisible=!0,this.bimId=e.Sys_Project_Id,(0,s.GetNoBindProjectList)({}).then((function(e){e.IsSucceed?t.bandingOptions=e.Data:t.$message({message:e.Message,type:"error"})}))},bindSubmit:function(){var e=this;this.$confirm("该项目下现有的所有数据都将清空。是否要绑定？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.bindLoading=!0,(0,s.BindBimProject)({bim_project_id:e.bimId,pro_project_id:e.bandingProject}).then((function(t){t.IsSucceed?(e.$message({message:"绑定成功",type:"success"}),e.fetchData(1),e.dialogVisible=!1):e.$message({message:t.Message,type:"error"})})).finally((function(t){e.bindLoading=!1,e.bandingProject=""}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handleSearch:function(){var e=[];this.form.Short_Name&&e.push({Key:"Short_Name",Type:"text",Filter_Type:"text",Value:[this.form.Short_Name]}),this.form.Type&&e.push({Key:"Type",Type:"text",Filter_Type:"text",Value:[this.form.Type]}),this.form.Status&&e.push({Key:"Status",Type:"text",Filter_Type:"radio",Value:[this.form.Status]}),this.form.Level&&e.push({Key:"Level",Type:"text",Filter_Type:"radio",Value:[this.form.Level]}),this.form.Bind_Type&&e.push({Key:"Bind_Type",Type:"text",Filter_Type:"radio",Value:[this.form.Bind_Type]}),this.queryInfo.ParameterJson=e,this.fetchData(1)},getColor:function(e){switch(e){case"0":return{color:"#F1B430",label:"未开始"};case"1":return{color:"#298DFF",label:"进行中"};case"2":return{color:"#3ECC93",label:"已完成"}}},handleUnbind:function(e){var t=this;this.$confirm("解绑当前项目后，如果再次邀请项目绑定时，将会清空当前项目数据。是否要解绑？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.CancelBindBimProject)({bim_project_id:e.Bim_Project_Id,pro_project_id:e.Sys_Project_Id}).then((function(e){e.IsSucceed?(t.$message({message:"解绑成功",type:"success"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleAdd:function(){this.$router.push({name:"PROProjectListAdd",query:{pg_redirect:this.$route.name}})},handleEdit:function(e){this.$router.push({name:"PROProjectListEdit",query:{pg_redirect:this.$route.name,id:e.Sys_Project_Id,from:2,bid:e.Sys_Project_Id}})},handleDetail:function(e){this.$router.push({name:"PROProjectListDetail",query:{pg_redirect:this.$route.name,id:e.Id,from:1===e.Bind_Status?3:1}})},handleDetailEdit:function(e){this.$router.push({name:"PROProjectListEdit",query:{pg_redirect:this.$route.name,id:e.Id,from:1===e.Bind_Status?3:1}})},handleUnit:function(e){this.$router.push({name:"PROProjectListUnit",query:{pg_redirect:this.$route.name,bimId:e.Bim_Project_Id,sysId:e.Sys_Project_Id,id:e.Id,name:encodeURIComponent(e.Short_Name),p:encodeURIComponent(e.Professional_Codes),status:e.Bind_Status}})},handleChangeOrder:function(e){var t=this;this.drawerVisible=!0,(0,h.GetProjectChangeOrderList)({Sys_Project_Id:e,Type:"Project",Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.changeOrderList=e.Data.Data.map((function(e){return e.Approve_Date=e.Approve_Date?(0,p.parseTime)(new Date(e.Approve_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Approve_Date,e.Launch_Date=e.Launch_Date?(0,p.parseTime)(new Date(e.Launch_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Launch_Date,e})):t.$message({message:e.Message,type:"error"})})).catch(console.error).finally((function(){}))},cancel:function(){this.bandingProject="",this.dialogVisible=!1},handleDelete:function(e){var t=this;this.$confirm("是否删除该项目?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.DeleteProject)({id:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))}}}},8604:function(e,t,n){},"950d":function(e,t,n){"use strict";n.r(t);var a=n("7e27"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},b8f3:function(e,t,n){"use strict";n("8604")},ca67:function(e,t,n){"use strict";n.r(t);var a=n("950d");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);var o,i,u=n("2877"),d=Object(u["a"])(a["default"],o,i,!1,null,null,null);t["default"]=d.exports},cf45:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=r,n("d3b7");var a=n("6186");function r(e){return new Promise((function(t,n){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},de45:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PRO_STATUS=void 0;t.PRO_STATUS=[{label:"未开始",value:"0"},{label:"进行中",value:"1"},{label:"已完成",value:"2"}]}}]);