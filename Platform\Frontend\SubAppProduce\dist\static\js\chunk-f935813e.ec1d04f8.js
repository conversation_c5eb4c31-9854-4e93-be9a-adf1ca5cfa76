(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-f935813e"],{"63f7":function(e,t,r){},"6d68":function(e,t,r){"use strict";r.r(t);var a=r("8d80"),o=r("7517");for(var n in o)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return o[e]}))}(n);r("7384");var i=r("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"52f4f7c1",null);t["default"]=l.exports},7384:function(e,t,r){"use strict";r("63f7")},7517:function(e,t,r){"use strict";r.r(t);var a=r("802f"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},"802f":function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(r("c14f")),n=a(r("1da1"));r("99af"),r("4de4"),r("7db0"),r("caad"),r("d81d"),r("14d9"),r("13d5"),r("a434"),r("e9f5"),r("910d"),r("f665"),r("7d54"),r("ab43"),r("9485"),r("a9e3"),r("dca8"),r("d3b7"),r("ac1f"),r("00b4"),r("2532"),r("159b");var i=r("be22"),l=r("cf45"),u=r("7757"),s=r("93aa"),d=r("ec21"),c=r("8cdf"),p=a(r("6612")),f=r("ed08");t.default={name:"PROAssetaccountAdd",components:{},props:{},data:function(){var e=this;return{form:{Factory_Id:"",Receipt_No:"",Sys_Project_Id:"无项目",Company_Id:localStorage.getItem("Last_Working_Object_Id"),Accounting_Date:new Date,Trade_Date:new Date,Contract_No:"",Invoice_Type:0,PaymentList:[],Tax_Rate:"",Asset_Type:"",Asset_Name:"",Device_Sn:"",Supplier_Id:"",Depreciation_Period:"",Depart_Id:""},type:"",backendDate:null,pickerOptions:{disabledDate:function(t){return t.getTime()<=new Date(e.backendDate).getTime()}},isDisabled:!0,disabledDepart:!1,factoryDisabled:!1,paymentInfo:0,factoryOption:[],departmentlist:[],AssetTypeList:[],ProjectList:[],SupplierData:[],obj:{},validRules:{Pay_Date:[{required:!0,message:"支付日期必须填写"}],Pay_Price:[{required:!0,message:"支付金额必须填写"}]},rules:{Factory_Id:[{required:!0,message:"请选择归属基地",trigger:"change"}],Asset_Type:[{required:!0,message:"请选择资产类型",trigger:"blur"}],Asset_Name:[{required:!0,message:"请输入资产名称",trigger:"blur"}],Device_Sn:[{required:!0,message:"请输入项目编号",trigger:"blur"}],Supplier_Id:[{required:!0,message:"请选择供应商",trigger:"change"}],Accounting_Date:[{required:!0,message:"请选择日期",trigger:"change"}],Depreciation_Period:[{required:!0,message:"请输入折旧年限",trigger:"blur"}],Trade_Date:[{required:!0,message:"请选择日期",trigger:"change"}],Depart_Id:[{required:!0,message:"请选择归属部门",trigger:"change"}],Sys_Project_Id:[{required:!0,message:"请选择归属项目",trigger:"change"}],Asset_No:[{required:!0,message:"请输入资产编号",trigger:"blur"}],Model:[{required:!0,message:"请输入型号",trigger:"blur"}],Invoice_Type:[{required:!0,message:"请选择票据类型",trigger:"change"}],Tax_Rate:[{required:!0,message:"请输入税率",trigger:"blur"}],Residual_Rate:[{required:!0,message:"请输入残值率",trigger:"blur"}],Accounting_Price:[{required:!0,message:"请输入核算金额",trigger:"blur"}],Payee:[{required:!0,message:"请输入收款方",trigger:"blur"}]}}},computed:{sumOfPayPrice:function(){var e=this.form.PaymentList.reduce((function(e,t){return e+Number(t.Pay_Price)}),0);return(0,p.default)(e).format("0,0.[00]")}},mounted:function(){var e=this;return(0,n.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.obj=e.$route.query,t.n=1,e.getPermission();case 1:return t.n=2,e.getSuppliers();case 2:return t.n=3,e.getOptions();case 3:"view"!=e.obj.type&&"edit"!=e.obj.type&&"paymentRegist"!=e.obj.type||(e.assetsRegistDetails(e.obj.Id),e.changeValue(e.form.Factory_Id),e.getTimeData(e.form.Factory_Id)),"edit"==e.obj.type||"add"==e.obj.type?e.isDisabled=!1:"paymentRegist"!=e.obj.type&&"view"!=e.obj.type||(e.isDisabled=!0);case 4:return t.a(2)}}),t)})))()},methods:{getTimeData:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,u.GetOMALatestAccountingDate)({FactoryId:e});case 1:a=r.v,a.IsSucceed?t.backendDate=a.Data||"":t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getPermission:function(){var e=this;return new Promise((function(t,r){e.permissionList=[],(0,i.GetUserDepartTypePermission)({}).then((function(a){a.IsSucceed?(e.permissionList=a.Data,t()):(e.$message({message:a.Message,type:"error"}),r())}))}))},handleInput:function(){var e=/^[1-9]\d*$/;e.test(this.form.Depreciation_Period)||(this.form.Depreciation_Period="")},changeType:function(e){0!=e&&2!=e||(this.form.Tax_Rate="")},getOptions:function(){var e,t=this;e="view"==this.obj.type?u.GetQueryNonExternalFactory:u.GetNonExternalFactory,e({}).then((function(e){if(e.IsSucceed){t.factoryOption=Object.freeze(e.Data||[]);var r=t.factoryOption.find((function(e){return e.Is_Cur_User_Factory}));r&&t.factoryOption.length&&"add"===t.obj.type&&(t.form.Factory_Id=r.Id,t.changeValue(t.form.Factory_Id),t.getTimeData(t.form.Factory_Id)),t.factoryDisabled=r&&0===t.permissionList.length}else t.$message({message:e.Message,type:"error"})})),(0,l.getDictionary)("AssetType").then((function(e){var r;t.AssetTypeList=e,null!==(r=t.AssetTypeList)&&void 0!==r&&r.length&&"add"==t.obj.type&&(t.form.Asset_Type=t.AssetTypeList[0].Id)}))},getProLabel:function(e){return"".concat(e.Short_Name,"（").concat(e.Status,"/").concat(e.FeedingMethod,"）")},changeValue:function(e){this.getFirstLevelDepartsUnderFactory(e),this.getFactoryProjectList(e)},getSuppliers:function(){var e=this;(0,s.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var r=[];for(var a in t.Data){var o={Id:a,Name:t.Data[a]};r.push(o)}e.SupplierData=r}else e.$message({message:t.Message,type:"error"})}))},assetsRegistDetails:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,d.AssetsRegistDetails)({Id:e});case 1:a=r.v,a.IsSucceed?(t.form=a.Data||[],t.changeValue(t.form.Factory_Id),t.getTimeData(t.form.Factory_Id)):t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getFactoryProjectList:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,c.GetFactoryProjectList)({FactoryId:e});case 1:a=r.v,a.IsSucceed?t.ProjectList=Object.freeze(a.Data.map((function(e){return e.Status||e.Type?e.label="".concat(e.Short_Name,"(").concat(e.Status&&e.Type?"".concat(e.Status,"/").concat(e.Type):"".concat(e.Status||e.Type),")"):e.label=e.Short_Name,e}))):t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},getFirstLevelDepartsUnderFactory:function(e){var t=this;return(0,n.default)((0,o.default)().m((function r(){var a,n,i,l;return(0,o.default)().w((function(r){while(1)switch(r.n){case 0:return r.n=1,(0,c.GetFirstLevelDepartsUnderFactory)({FactoryId:e});case 1:a=r.v,a.IsSucceed?(n=a.Data||[],t.permissionList.length?(i=n.filter((function(e){return t.permissionList.includes(e.Type)||e.Is_Cur_User_Depart})),t.departmentlist=i):t.departmentlist=n,l=t.departmentlist.find((function(e){return e.Is_Cur_User_Depart})),l?t.factoryOption.length&&"add"===t.obj.type&&(t.form.Depart_Id=l.Id):t.factoryOption.length&&"add"===t.obj.type&&(t.form.Depart_Id=""),t.disabledDepart=l&&0===t.permissionList.length):t.message.error(a.Mesaage);case 2:return r.a(2)}}),r)})))()},addPaymentInfo:function(){this.form.PaymentList.push({Pay_Date:"",Pay_Price:"",Remark:""})},handleDeleteRow:function(e){var t=this;this.form.PaymentList.forEach((function(r,a){e._X_ROW_KEY===r._X_ROW_KEY&&t.form.PaymentList.splice(a,1)}))},editRowEvent:function(e){var t=this.$refs.xTable;t.setActiveRow(e)},cancelRowEvent:function(e){var t=this.$refs.xTable;t.clearActived().then((function(){}))},saveRowEvent:function(e){var t=this,r=this.$refs.xTable;r.clearActived().then((function(){t.loading=!0,setTimeout((function(){t.loading=!1}),300)}))},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;if("add"==e.obj.type)(0,d.SaveAssetsRegistration)(e.form).then((function(t){t.IsSucceed?(e.$message.success("提交成功"),e.toBack()):e.$message({message:t.Message,type:"error"})}));else if("edit"==e.obj.type||"paymentRegist"==e.obj.type){e.form.Id=e.obj.Id;var r="paymentRegist"===e.obj.type?d.UpdatePaymentList:d.EditAssetsRegist;r(e.form).then((function(t){t.IsSucceed?(e.$message.success("编辑成功"),e.toBack()):e.$message({message:t.Message,type:"error"})}))}}))},toBack:function(){(0,f.closeTagView)(this.$store,this.$route)}}}},"8d80":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[r("div",{staticClass:"cs-z-page-main-content"},[r("div",{staticClass:"top"},[e._v("资产基础信息")]),r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[r("el-row",{staticClass:"row-bg"},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"单据编号",prop:"Receipt_No"}},[r("el-input",{attrs:{disabled:"",placeholder:""},model:{value:e.form.Receipt_No,callback:function(t){e.$set(e.form,"Receipt_No",t)},expression:"form.Receipt_No"}})],1),r("el-form-item",{attrs:{label:"归属基地:",prop:"Factory_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled||e.factoryDisabled},on:{change:e.changeValue},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e,t){return r("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"资产类型:",prop:"Asset_Type"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled},model:{value:e.form.Asset_Type,callback:function(t){e.$set(e.form,"Asset_Type",t)},expression:"form.Asset_Type"}},e._l(e.AssetTypeList,(function(e,t){return r("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"资产名称:",prop:"Asset_Name"}},[r("el-input",{attrs:{disabled:e.isDisabled},model:{value:e.form.Asset_Name,callback:function(t){e.$set(e.form,"Asset_Name",t)},expression:"form.Asset_Name"}})],1),r("el-form-item",{attrs:{label:"设备编号:",prop:"Device_Sn"}},[r("el-input",{attrs:{disabled:e.isDisabled},model:{value:e.form.Device_Sn,callback:function(t){e.$set(e.form,"Device_Sn",t)},expression:"form.Device_Sn"}})],1),r("el-form-item",{attrs:{label:"供应商:",prop:"Supplier_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled},model:{value:e.form.Supplier_Id,callback:function(t){e.$set(e.form,"Supplier_Id",t)},expression:"form.Supplier_Id"}},e._l(e.SupplierData,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"核算开始日期:",prop:"Accounting_Date"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期",disabled:e.isDisabled,"picker-options":e.pickerOptions},model:{value:e.form.Accounting_Date,callback:function(t){e.$set(e.form,"Accounting_Date",t)},expression:"form.Accounting_Date"}})],1),r("el-form-item",{attrs:{label:"折旧年限:",prop:"Depreciation_Period"}},[r("el-input",{attrs:{min:0,placeholder:"请输入正整数",disabled:e.isDisabled},on:{input:e.handleInput},model:{value:e.form.Depreciation_Period,callback:function(t){e.$set(e.form,"Depreciation_Period",e._n(t))},expression:"form.Depreciation_Period"}},[r("template",{slot:"append"},[e._v("年")])],2)],1),"view"==e.obj.type||"paymentRegist"==e.obj.type?r("el-form-item",{attrs:{label:"已摊销金额:",prop:"Amortization_Price"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Amortization_Price,callback:function(t){e.$set(e.form,"Amortization_Price",t)},expression:"form.Amortization_Price"}},[r("template",{slot:"append"},[e._v("元")])],2)],1):e._e()],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属部门:",prop:"Depart_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled||!e.form.Factory_Id||e.disabledDepart},model:{value:e.form.Depart_Id,callback:function(t){e.$set(e.form,"Depart_Id",t)},expression:"form.Depart_Id"}},e._l(e.departmentlist,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),r("el-form-item",{attrs:{label:"归属项目:",prop:"Sys_Project_Id"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled||!e.form.Factory_Id},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},[r("el-option",{attrs:{label:"无项目",value:"无项目"}}),e._l(e.ProjectList,(function(t){return r("el-option",{key:t.Sys_Project_Id,attrs:{label:e.getProLabel(t),value:t.Sys_Project_Id}})}))],2)],1),r("el-form-item",{attrs:{label:"资产编号:",prop:"Asset_No"}},[r("el-input",{attrs:{disabled:e.isDisabled},model:{value:e.form.Asset_No,callback:function(t){e.$set(e.form,"Asset_No",t)},expression:"form.Asset_No"}})],1),r("el-form-item",{attrs:{label:"型号:",prop:"Model"}},[r("el-input",{attrs:{disabled:e.isDisabled},model:{value:e.form.Model,callback:function(t){e.$set(e.form,"Model",t)},expression:"form.Model"}})],1),r("el-row",{staticStyle:{width:"100%"}},[r("el-col",{attrs:{span:1==e.form.Invoice_Type?12:24}},[r("el-form-item",{attrs:{label:"票据类型:",prop:"Invoice_Type"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:e.isDisabled},on:{change:e.changeType},model:{value:e.form.Invoice_Type,callback:function(t){e.$set(e.form,"Invoice_Type",t)},expression:"form.Invoice_Type"}},[r("el-option",{attrs:{label:"无发票",value:0}}),r("el-option",{attrs:{label:"增值税专用发票",value:1}}),r("el-option",{attrs:{label:"增值税普通发票",value:2}})],1)],1)],1),r("el-col",{attrs:{span:12}},[1==e.form.Invoice_Type?r("el-form-item",{attrs:{label:"税率:",prop:"Tax_Rate"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Tax_Rate,callback:function(t){e.$set(e.form,"Tax_Rate",t)},expression:"form.Tax_Rate"}},[r("template",{slot:"append"},[e._v("%")])],2)],1):e._e()],1)],1),r("el-form-item",{attrs:{label:"核算金额:",prop:"Accounting_Price"}},[r("el-input",{attrs:{type:"number",step:"any",placeholder:"请输入",disabled:e.isDisabled},model:{value:e.form.Accounting_Price,callback:function(t){e.$set(e.form,"Accounting_Price",t)},expression:"form.Accounting_Price"}},[r("template",{slot:"append"},[e._v("元")])],2)],1),"view"==e.obj.type||"paymentRegist"==e.obj.type?r("el-form-item",{attrs:{label:"残值:",prop:"Residual_Price"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Residual_Price,callback:function(t){e.$set(e.form,"Residual_Price",t)},expression:"form.Residual_Price"}},[r("template",{slot:"append"},[e._v("元")])],2)],1):e._e(),r("el-form-item",{attrs:{label:"残值率:",prop:"Residual_Rate"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Residual_Rate,callback:function(t){e.$set(e.form,"Residual_Rate",t)},expression:"form.Residual_Rate"}},[r("template",{slot:"append"},[e._v("%")])],2)],1),"view"==e.obj.type||"paymentRegist"==e.obj.type?r("el-form-item",{attrs:{label:"现值:",prop:"Present_Price"}},[r("el-input",{attrs:{type:"number",step:"any",disabled:e.isDisabled},model:{value:e.form.Present_Price,callback:function(t){e.$set(e.form,"Present_Price",t)},expression:"form.Present_Price"}},[r("template",{slot:"append"},[e._v("元")])],2)],1):e._e(),r("el-form-item",{attrs:{label:"采购日期:",prop:"Trade_Date"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期",disabled:e.isDisabled},model:{value:e.form.Trade_Date,callback:function(t){e.$set(e.form,"Trade_Date",t)},expression:"form.Trade_Date"}})],1)],1)],1),r("div",{staticClass:"top"},[e._v(" 付款信息：累计付款"),r("strong",{staticStyle:{color:"red"}},[e._v(" "+e._s(e.sumOfPayPrice)+" ")]),e._v("元 ")]),r("el-row",[r("el-col",{attrs:{span:24}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同编号:",prop:"Contract_No"}},[r("el-input",{model:{value:e.form.Contract_No,callback:function(t){e.$set(e.form,"Contract_No",t)},expression:"form.Contract_No"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"收款方:",prop:"Payee"}},[r("el-input",{model:{value:e.form.Payee,callback:function(t){e.$set(e.form,"Payee",t)},expression:"form.Payee"}})],1)],1)],1)],1)],1),r("div",[r("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return["view"!=e.obj.type?r("el-button",{attrs:{type:"primary"},on:{click:e.addPaymentInfo}},[e._v("新增付款信息")]):e._e()]},proxy:!0}])}),r("div",{staticClass:"tb-x"},[r("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",stripe:"","edit-rules":e.validRules,align:"left",resizable:"","show-overflow":"",data:e.form.PaymentList,"edit-config":{trigger:"manual",mode:"row"}}},[r("vxe-column",{attrs:{field:"Pay_Date",title:"支付日期","edit-render":{}},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",[e._v(e._s(e._f("timeFormat")(a.Pay_Date)))])]}},{key:"edit",fn:function(t){var a=t.row;return[r("vxe-input",{attrs:{type:"date",placeholder:"请选择",transfer:""},model:{value:a.Pay_Date,callback:function(t){e.$set(a,"Pay_Date",t)},expression:"row.Pay_Date"}})]}}])}),r("vxe-column",{attrs:{field:"Pay_Price",title:"支付金额","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[r("vxe-input",{attrs:{type:"float",placeholder:"请输入"},model:{value:a.Pay_Price,callback:function(t){e.$set(a,"Pay_Price",t)},expression:"row.Pay_Price"}})]}}])}),r("vxe-column",{attrs:{field:"Remark",title:"备注","edit-render":{},width:"400"},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[r("vxe-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:a.Remark,callback:function(t){e.$set(a,"Remark",t)},expression:"row.Remark"}})]}}])}),"view"!=e.obj.type?r("vxe-column",{attrs:{title:"操作",width:"400"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e.$refs.xTable.isActiveByRow(a)?[r("vxe-button",{attrs:{type:"text",status:"primary"},on:{click:function(t){return e.saveRowEvent(a)}}},[e._v("保存")]),r("vxe-button",{attrs:{type:"text",status:"danger"},on:{click:function(t){return e.cancelRowEvent(a)}}},[e._v("取消")])]:[r("vxe-button",{attrs:{type:"text",status:"primary"},on:{click:function(t){return e.editRowEvent(a)}}},[e._v("编辑")]),r("el-popconfirm",{attrs:{title:"是否删除该条数据？"},on:{confirm:function(t){return e.handleDeleteRow(a)}}},[r("vxe-button",{attrs:{slot:"reference",type:"text",status:"danger"},slot:"reference"},[e._v("删除")])],1)]]}}],null,!1,975837217)}):e._e()],1)],1)],1),r("el-row",[r("footer",["view"!=e.obj.type?r("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("提交")]):e._e(),r("el-button",{on:{click:e.toBack}},[e._v("取消")])],1)])],1)])},o=[]},"93aa":function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AuxImport=C,t.AuxInStoreExport=X,t.AuxReturnByReceipt=H,t.AuxSurplusReturnStore=ee,t.DeleteAuxInStore=U,t.DeleteInStore=h,t.DeletePicking=xe,t.ExportCheckReceipt=pe,t.ExportInstoreReceipt=ce,t.ExportMoneyAdjustOrder=ve,t.ExportPicking=we,t.ExportProcess=Ee,t.ExportTestDetail=ge,t.FindAuxPageList=B,t.FindRawPageList=L,t.GetAuxCategoryTreeList=Z,t.GetAuxDetailByReceipt=Y,t.GetAuxImportTemplate=$,t.GetAuxPageList=re,t.GetAuxPickOutStoreSubList=E,t.GetAuxProcurementDetails=ae,t.GetAuxSurplusReturnStoreDetail=te,t.GetCategoryTreeList=b,t.GetImportTemplate=O,t.GetInstoreDetail=S,t.GetMoneyAdjustDetailPageList=Ie,t.GetOMALatestStatisticTime=A,t.GetOrderDetail=ie,t.GetPartyAs=_,t.GetPickLockStoreToChuku=$e,t.GetPickPlate=Ce,t.GetPickSelectPageList=je,t.GetPickSelectSubList=Ne,t.GetPickingDetail=ke,t.GetPickingTypeSettingDetail=Te,t.GetProjectListForTenant=oe,t.GetRawDetailByReceipt=le,t.GetRawOrderList=ne,t.GetRawPageList=g,t.GetRawPickOutStoreSubList=N,t.GetRawProcurementDetails=I,t.GetRawSurplusReturnStoreDetail=j,t.GetReturnPlate=Fe,t.GetStoreSelectPage=Ae,t.GetSuppliers=R,t.GetTestDetail=Re,t.GetTestInStoreOrderList=Se,t.Import=k,t.ImportCheckReceipt=me,t.ImportInstoreReceipt=fe,t.InStoreListSummary=se,t.LockPicking=Le,t.ManualAuxInStoreDetail=V,t.ManualInStoreDetail=P,t.MaterielAuxInStoreList=F,t.MaterielAuxManualInStore=K,t.MaterielAuxPurchaseInStore=Q,t.MaterielAuxSubmitInStore=q,t.MaterielPartyAInStorel=J,t.MaterielRawInStoreList=n,t.MaterielRawInStoreListInSumNew=l,t.MaterielRawInStoreListNew=i,t.MaterielRawManualInStore=w,t.MaterielRawPartyAInStore=D,t.MaterielRawPurchaseInStore=v,t.MaterielRawSubmitInStore=u,t.MaterielRawSurplusInStore=M,t.OutStoreListSummary=de,t.PartAInStoreDetail=m,t.PartyAInInStoreDetail=W,t.PurchaseAuxInStoreDetail=z,t.PurchaseInStoreDetail=f,t.RawInStoreExport=T,t.RawReturnByReceipt=ue,t.RawSurplusReturnStore=G,t.SaveInStore=x,t.SavePicking=Oe,t.SetQualified=_e,t.SetTestDetail=be,t.StoreMoneyAdjust=ye,t.SubmitApproval=p,t.SubmitAuxApproval=c,t.SubmitInStore=Pe,t.SubmitPicking=Me,t.SurplusInStoreDetail=y,t.UnLockPicking=Ge,t.UpdateInvoiceInfo=De,t.Withdraw=s,t.WithdrawAux=d,t.WithdrawChecked=he;var o=a(r("b775"));function n(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:e})}function i(e){return(0,o.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:e})}function S(e){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:e})}function W(e){return(0,o.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:e})}function J(e){return(0,o.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:e})}function Z(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function ee(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:e})}function te(e){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function ae(e){return(0,o.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:e})}function oe(e){return(0,o.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:e})}function ne(e){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:e})}function le(e){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:e})}function ue(e){return(0,o.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:e})}function de(e){return(0,o.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:e})}function ce(e){return(0,o.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:e})}function pe(e){return(0,o.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:e})}function fe(e){return(0,o.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:e})}function me(e){return(0,o.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:e})}function Pe(e){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:e})}function ye(e){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:e})}function Se(e){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:e})}function he(e){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:e})}function Re(e){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:e})}function _e(e){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:e})}function be(e){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:e})}function ge(e){return(0,o.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:e})}function Ie(e){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:e})}function ve(e){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:e})}function De(e){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:e})}function we(e){return(0,o.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:e})}function Me(e){return(0,o.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:e})}function xe(e){return(0,o.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:e})}function Ae(e){return(0,o.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:e})}function Oe(e){return(0,o.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:e})}function ke(e){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:e})}function Te(e){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:e})}function Le(e){return(0,o.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:e})}function Ge(e){return(0,o.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:e})}function je(e){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:e})}function Ne(e){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:e})}function $e(e){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:e})}function Ce(e){return(0,o.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:e})}function Fe(e){return(0,o.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:e})}function Ee(e){return(0,o.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:e})}}}]);