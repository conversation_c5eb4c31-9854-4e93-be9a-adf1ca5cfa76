(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-656a16e8"],{1089:function(t,e,o){"use strict";o.r(e);var a=o("9cd7"),n=o("6486");for(var r in n)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(r);o("ffa0");var i=o("2877"),u=Object(i["a"])(n["default"],a["a"],a["b"],!1,null,"68e63892",null);e["default"]=u.exports},"31ce":function(t,e,o){},6486:function(t,e,o){"use strict";o.r(e);var a=o("9535"),n=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"6b87":function(t,e,o){"use strict";var a=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetComponentCountByMonth=l,e.GetComponentInfoPageList=m,e.GetComponentNoProduceDetailPageList=u,e.GetComponentPageList=f,e.GetComponentProducedByDays=r,e.GetComponentProducedDay=c,e.GetFactoryComponentYield=y,e.GetFactorySchdulingPlanYield=D,e.GetFactoryTeamYield=h,e.GetFactoryTeamYieldForDay=p,e.GetInstallUnitProducedCount=i,e.GetProducedDetailPageList=d,e.GetTeamProducedCountByDate=s;var n=a(o("b775"));a(o("4328"));function r(t){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentProducedByDays",method:"post",data:t})}function i(t){return(0,n.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount",method:"post",data:t})}function u(t){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentNoProduceDetailPageList",method:"post",data:t})}function d(t){return(0,n.default)({url:"/PRO/ProductionCount/GetProducedDetailPageList",method:"post",data:t})}function c(t){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentProducedDay",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentCountByMonth",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/ProductionCount/GetTeamProducedCountByDate",method:"post",data:t})}function f(t){return(0,n.default)({url:"/PRO/Component/GetComponentPageList",method:"post",data:t})}function m(t){return(0,n.default)({url:"/PRO/Component/GetComponentInfoPageList",method:"post",data:t})}function p(t){return(0,n.default)({url:"/PRO/ProductionCount/GetFactoryTeamYieldForDay",method:"post",data:t})}function h(t){return(0,n.default)({url:"/PRO/ProductionCount/GetFactoryTeamYield",method:"post",data:t})}function y(t){return(0,n.default)({url:"/PRO/ProductionCount/GetFactoryComponentYield",method:"post",data:t})}function D(t){return(0,n.default)({url:"/PRO/Component/GetFactorySchdulingPlanYield",method:"post",data:t})}},9535:function(t,e,o){"use strict";var a=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("99af"),o("4de4"),o("a15b"),o("d81d"),o("e9f5"),o("910d"),o("ab43"),o("d3b7"),o("3ca3"),o("ddb0");var n=a(o("34e9")),r=a(o("c1df")),i=o("5e99"),u=o("6b87");o("8975"),e.default={name:"PRODayReport",components:{Header:n.default},data:function(){return{form:{factoryId:"",begin:"",end:""},finishDate:[],factoryName:"",tableData:[],options:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(t){var e=new Date,o=new Date;o.setTime(o.getTime()-6048e5),t.$emit("pick",[o,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,o=new Date;o.setTime(o.getTime()-2592e6),t.$emit("pick",[o,e])}},{text:"最近三个月",onClick:function(t){var e=new Date,o=new Date;o.setTime(o.getTime()-7776e6),t.$emit("pick",[o,e])}}]}}},created:function(){var t=this;this.finishDate=[(0,r.default)().subtract(7,"days").format("yyyy-MM-DD"),(0,r.default)().format("yyyy-MM-DD")],this.form.begin=(0,r.default)().subtract(7,"days").format("yyyy-MM-DD"),this.form.end=(0,r.default)().format("yyyy-MM-DD"),Promise.all([(0,i.GetFactoryList)({}).then((function(e){e.IsSucceed?(t.options=e.Data,t.form.factoryId=e.Data[0].Id,t.factoryName=e.Data[0].Name):t.$message({type:"error",message:e.Message})}))]).then((function(){(0,u.GetTeamProducedCountByDate)(t.form).then((function(e){e.IsSucceed?(t.tableData=e.Data,t.tableData.map((function(e){e.dateRange=t.finishDate.join(" / ")}))):t.$message({type:"error",message:e.Message})}))}))},methods:{fetchData:function(){var t=this;this.form.begin=this.finishDate[0],this.form.end=this.finishDate[1],this.factoryName=this.options.filter((function(e){if(e.Id===t.form.factoryId)return e}))[0].Name,(0,u.GetTeamProducedCountByDate)(this.form).then((function(e){e.IsSucceed?(t.tableData=e.Data,t.tableData.map((function(e){e.dateRange=t.finishDate.join(" / ")}))):t.$message({type:"error",message:e.Message})}))},handleExport:function(){var t=this,e=["统计日期范围","班组","工序","产量(kg)","数量(件)"],a=["dateRange","working_team_name","working_process_name","Total_Weight","count"],n=this.formatJson(a,this.tableData);o.e("chunk-2d0cc0b6").then(o.t.bind(null,"4bf8",7)).then((function(o){o.export_json_to_excel({header:e,data:n,filename:"".concat(t.factoryName," ").concat(t.finishDate[0]," ~ ").concat(t.finishDate[1]," 产量统计"),autoWidth:!0,bookType:"xlsx"})}))},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}}},"9cd7":function(t,e,o){"use strict";o.d(e,"a",(function(){return a})),o.d(e,"b",(function(){return n}));var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("el-card",{staticClass:"box-card"},[o("Header",{attrs:{padding:"0"},scopedSlots:t._u([{key:"left",fn:function(){return[o("el-form",{attrs:{"label-width":"40px",inline:!0}},[t._e(),o("el-form-item",{attrs:{"label-width":"80",label:"统计日期"}},[o("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.finishDate,callback:function(e){t.finishDate=e},expression:"finishDate"}})],1)],1)]},proxy:!0},{key:"right",fn:function(){return[o("el-button",{attrs:{type:"success"},on:{click:t.handleExport}},[t._v("导出")])]},proxy:!0}])}),o("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.tableData,height:"700",border:""}},[o("el-table-column",{attrs:{prop:"dateRange","show-overflow-tooltip":"",label:"统计日期范围"}}),o("el-table-column",{attrs:{prop:"working_team_name","show-overflow-tooltip":"",label:"班组"}}),o("el-table-column",{attrs:{prop:"working_process_name","show-overflow-tooltip":"",label:"工序"}}),o("el-table-column",{attrs:{prop:"Total_Weight","show-overflow-tooltip":"",label:"产量(kg)"}}),o("el-table-column",{attrs:{prop:"count","show-overflow-tooltip":"",label:"数量(件)"}})],1)],1)],1)},n=[]},ffa0:function(t,e,o){"use strict";o("31ce")}}]);