(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3daa35fa"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=n,Math.easeInOutQuad=function(e,t,a,o){return e/=o/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(e,t,a){var n=l(),s=e-n,i=20,c=0;t="undefined"===typeof t?500:t;var d=function(){c+=i;var e=Math.easeInOutQuad(c,n,s,t);r(e),c<t?o(d):a&&"function"===typeof a&&a()};d()}},"36ec":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var r=o(a("5530")),l=o(a("c14f")),n=o(a("1da1")),s=o(a("5c20")),i=o(a("333d")),c=a("ac6b"),d=a("c685");t.default={components:{processdialog:s.default,Pagination:i.default},data:function(){return{tableData:[],pageInfo:{Page:1,PageSize:d.tablePageSize[0],total:0},pageSizes:d.tablePageSize,tbLoading:!1,processingprogress:[{Id:0,Display_Name:"未完成"},{Id:1,Display_Name:"已完成"}],form:{Type:"0",ProjectId:"",FactoryId:"",AreaId:"",ProgressStatus:"",OrderStartTime:"",OrderEndTime:"",SteelName:""},dateofissuance:[],ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"children",label:"label",value:"Id"}},content:{Name:"",SSZT:"",Contract_Type:"",Bim_Name:"",ContractStatus:"",Project_State:"",Project_Type:"",Manager:"",CJDW:"",CompanyId:localStorage.getItem("CurReferenceId")},pageInfoti:{Page:1,PageSize:9999999},certs:[],FactoryNameData:[]}},computed:{isFactory:function(){return"/produce/factory/pro/basic/process"===this.$route.path}},created:function(){this.getProjectOption(),this.getfactory()},mounted:function(){var e=this;return(0,n.default)((0,l.default)().m((function t(){return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.fetchData();case 1:return t.a(2)}}),t)})))()},methods:{getProjectOption:function(){var e=this;return(0,n.default)((0,l.default)().m((function t(){return(0,l.default)().w((function(t){while(1)switch(t.n){case 0:(0,c.GetEntities)({content:e.content,pageInfo:e.pageInfoti}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getAreaList:function(){var e=this;this.form.AreaId="",(0,c.GetGetMonomerList)({projectId:this.form.ProjectId}).then((function(t){t.IsSucceed?e.pickCert1(t.Data):e.$message({message:t.Message,type:"error"})}))},pageChange:function(e){var t=e.page,a=e.limit,o=e.type;this.pageInfo.Page="limit"===o?1:t,this.pageInfo.PageSize=a,this.fetchData()},pickCert1:function(e){var t=this;this.certs=[],e.forEach((function(e){var a=[];(0,c.GetAreaPageList)({pageInfo:{Page:1,PageSize:9999999,MonomerId:e.Id,ProjectId:t.form.ProjectId,AreaName:""},MonomerId:e.Id,ProjectId:t.form.ProjectId,AreaName:""}).then((function(o){if(o.IsSucceed){var r={};r=o.Data.Data,r.forEach((function(e){a.push({label:e.Name,Id:e.Id})})),t.certs.push({disabled:"全部单体"===e.Name,label:e.Name,Id:e.Id,children:a})}}))})),this.treeParamsArea.data=this.certs,this.$nextTick((function(e){t.$refs.treeSelectArea.treeDataUpdateFun(t.certs)}))},getfactory:function(){var e=this;(0,c.GetCompanyFactoryPageList)({pageInfo:this.pageInfoti}).then((function(t){t.IsSucceed?e.FactoryNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},fetchData:function(){var e=this;this.tbLoading=!0,(0,c.GetProcessingProgress)((0,r.default)((0,r.default)((0,r.default)({},this.pageInfo),this.form),{},{InstanceType:this.isFactory?1:0})).then((function(t){t.IsSucceed?(e.tableData=t.Data.Data||[],e.pageInfo.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).finally((function(){e.tbLoading=!1}))},loadContentMethod:function(e){var t=this,a=e.row;return new Promise((function(e){setTimeout((function(){a.childData=a.childData.map((function(e){return(0,r.default)((0,r.default)({},e),{},{Schduling_Code:a.Schduling_Code,Type:t.form.Type})})),e()}),500)}))},handleClick:function(e,t){this.pageInfo.PageSize=this.pageSizes[0],this.pageInfo.Page=1,this.pageInfo.total=0,this.form.Type=e.name,this.form.ProjectId="",this.form.FactoryId="",this.form.AreaId="",this.form.ProgressStatus="",this.form.OrderStartTime="",this.form.OrderEndTime="",this.form.SteelName="",this.fetchData()},getData:function(){this.pageInfo.Page=1,this.form.OrderStartTime=this.dateofissuance[0]||"",this.form.OrderEndTime=this.dateofissuance[1]||"",this.fetchData()},progressChild:function(e){this.$refs.processdialog.handleOpen(e)}}}},"3efb":function(e,t,a){"use strict";a("8a6c")},"5a33":function(e,t,a){"use strict";a.r(t);var o=a("36ec"),r=a.n(o);for(var l in o)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(l);t["default"]=r.a},"5c20":function(e,t,a){"use strict";a.r(t);var o=a("f833"),r=a("f584");for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);a("3efb");var n=a("2877"),s=Object(n["a"])(r["default"],o["a"],o["b"],!1,null,"2f0ac552",null);t["default"]=s.exports},6894:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return r}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-tab-pane",{attrs:{label:"构件",name:"0"}}),a("el-tab-pane",{attrs:{label:"部件",name:"3"}}),a("el-tab-pane",{attrs:{label:"零件",name:"1"}})],1),a("el-form",{attrs:{inline:"","label-width":"180"}},[a("el-form-item",{attrs:{label:"0"===e.form.Type?"构件名称":"1"===e.form.Type?"零件名称":"部件名称"}},[a("el-input",{staticStyle:{width:"170px"},attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1),a("el-form-item",{attrs:{label:"项目"}},[a("el-select",{staticStyle:{width:"170px"},attrs:{filterable:"",clearable:""},on:{change:e.getAreaList},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",staticStyle:{width:"170px"},attrs:{disabled:!e.form.ProjectId,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},model:{value:e.form.AreaId,callback:function(t){e.$set(e.form,"AreaId",t)},expression:"form.AreaId"}})],1),a("el-form-item",{attrs:{label:"加工进度"}},[a("el-select",{staticStyle:{width:"170px"},attrs:{clearable:""},model:{value:e.form.ProgressStatus,callback:function(t){e.$set(e.form,"ProgressStatus",t)},expression:"form.ProgressStatus"}},e._l(e.processingprogress,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Display_Name,label:e.Display_Name}})})),1)],1),e.isFactory?e._e():a("el-form-item",{attrs:{label:"加工工厂"}},[a("el-select",{staticStyle:{width:"170px"},attrs:{filterable:"",clearable:""},model:{value:e.form.FactoryId,callback:function(t){e.$set(e.form,"FactoryId",t)},expression:"form.FactoryId"}},e._l(e.FactoryNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"下达时间"}},[a("el-date-picker",{staticStyle:{width:"250px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.dateofissuance,callback:function(t){e.dateofissuance=t},expression:"dateofissuance"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.getData}},[e._v("搜索")])],1)],1),a("div",{staticClass:"main-wrapper"},[a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",height:"auto",stripe:"",loading:e.tbLoading,resizable:"","tree-config":{transform:!0},"expand-config":{lazy:!0,loadMethod:e.loadContentMethod,iconOpen:"vxe-icon-square-minus",iconClose:"vxe-icon-square-plus"},data:e.tableData}},[a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",type:"expand",width:"40"},scopedSlots:e._u([{key:"content",fn:function(t){var o=t.row;return[a("div",{staticClass:"expand-wrapper"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table cs-nested-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",stripe:"",data:o.childData,align:"left",resizable:""}},[a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"Code",title:"任务单号","show-overflow":"title"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{staticStyle:{color:"#277ff0"}},[e._v(e._s(o.Code))])]}}],null,!0)}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"ProcessName",title:"工序","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"TeamName",title:"加工班组","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"120",sortable:"",field:"Current_Task_Count",title:"任务数量","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"120",sortable:"",field:"Current_Task_Weight",title:"任务重量(kg)","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"120",sortable:"",field:"Finish_Count",title:"完成数量","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"150",sortable:"",field:"Finish_Weight",title:"完成重量(kg)","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"left","min-width":"150",sortable:"",field:"Task_Status",title:"加工进度","show-overflow":"title"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{style:"未完成"===o.Task_Status?{color:"red"}:{color:"#67c23a"}},[e._v(e._s(o.Task_Status))])]}}],null,!0)}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",title:"操作","show-overflow":"title"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("vxe-button",{attrs:{status:"primary",type:"text",round:""},on:{click:function(t){return e.progressChild(o)}}},[e._v("加工进度")])]}}],null,!0)})],1)],1)]}}])}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"Schduling_Code",title:"排产单号","show-overflow":"title"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{staticStyle:{color:"#277ff0"}},[e._v(e._s(o.Schduling_Code))])]}}])}),a("vxe-column",{attrs:{align:"center","min-width":"120",sortable:"",field:"OrderUserName",title:"计划员","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"ProjectName",title:"项目名称","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"AreaName",title:"区域","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"WorkshopName",title:"生产车间","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"120",sortable:"",field:"Schduling_Count",title:"排产数量","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"150",sortable:"",field:"Schduling_Weight",title:"排产重量(kg)","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"150",sortable:"",field:"Finish_Count",title:"完成数量","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"150",sortable:"",field:"Finish_Weight",title:"完成重量(kg)","show-overflow":"title"}}),a("vxe-column",{attrs:{align:"center","min-width":"120",sortable:"",field:"Schduling_Status",title:"加工进度","show-overflow":"title"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("span",{style:"未完成"===o.Schduling_Status?{color:"red"}:{color:"#67c23a"}},[e._v(e._s(o.Schduling_Status))])]}}])}),a("vxe-column",{attrs:{align:"left","min-width":"120",sortable:"",field:"FactoryName",title:"加工工厂","show-overflow":"title"}}),a("vxe-column",{attrs:{"min-width":"120",sortable:"",align:"center",field:"Order_Date",title:"下达时间","show-overflow":"title"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Order_Date,"{y}-{m}-{d}"))+" ")]}}])})],1)],1),a("div",{staticStyle:{"text-align":"right"}},[a("pagination",{attrs:{total:e.pageInfo.total,page:e.pageInfo.Page,"page-sizes":e.pageSizes,limit:e.pageInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.pageInfo,"Page",t)},"update:limit":function(t){return e.$set(e.pageInfo,"PageSize",t)},pagination:e.pageChange}})],1)]),a("processdialog",{ref:"processdialog"})],1)])},r=[]},"8a6c":function(e,t,a){},"90ca":function(e,t,a){"use strict";a.r(t);var o=a("6894"),r=a("5a33");for(var l in r)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(l);a("9cdb");var n=a("2877"),s=Object(n["a"])(r["default"],o["a"],o["b"],!1,null,"5f89c8ba",null);t["default"]=s.exports},"9cdb":function(e,t,a){"use strict";a("bed39")},ac6b:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportTeamProcessingTask=h,t.FindMatBillSumPageList=g,t.GetAreaPageList=c,t.GetCompanyFactoryPageList=u,t.GetEntities=s,t.GetFactoryPageList=d,t.GetGetMonomerList=i,t.GetMatBillSumSubList=b,t.GetProcessingProgress=l,t.GetProcessingProgressTask=n,t.GetSummaryTeamProcessingTask=p,t.GetTeamProcessingTask=f,t.GetWorkingTeams=m;var r=o(a("b775"));function l(e){return(0,r.default)({url:"/PRO/ProductionReport/GetProcessingProgress",method:"post",data:e})}function n(e){return(0,r.default)({url:"/PRO/ProductionReport/GetProcessingProgressTask",method:"post",data:e})}function s(e){return(0,r.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_projects/GetEntities"),method:"post",data:e})}function i(e){return(0,r.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetGetMonomerList"),method:"post",data:e})}function c(e){return(0,r.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetAreaPageList"),method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPageList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Factory/GetCompanyFactoryPageList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProductionReport/GetTeamProcessingTask",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProductionReport/GetWorkingTeams",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProductionReport/GetSummaryTeamProcessingTask",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ProductionReport/ExportTeamProcessingTask",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProductionReport/FindMatBillSumPageList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/ProductionReport/GetMatBillSumSubList",method:"post",data:e})}},bed39:function(e,t,a){},e66a:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("a15b"),a("d81d"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var r=o(a("5530")),l=a("ac6b");t.default={props:{showSearch:{type:Boolean,default:!0}},data:function(){return{dialogVisible:!1,processingprogress:[{Id:11,Display_Name:"未开始"},{Id:1,Display_Name:"待接收"},{Id:2,Display_Name:"待加工"},{Id:3,Display_Name:"待报检"},{Id:4,Display_Name:"待转移"},{Id:5,Display_Name:"质检中"},{Id:6,Display_Name:"待整改"},{Id:7,Display_Name:"待复核"},{Id:8,Display_Name:"下道未接收"},{Id:9,Display_Name:"已完成"},{Id:10,Display_Name:"未完成"}],producedata:{Schduling_Code:"",Code:"",ProcessName:"",TeamName:"",Task_Status:"",Contract_Type:""},modelsearch:{Type:"",Schduling_Id:"",Task_Code:"",Task_Status_Int:"",Comp_Code_Real:"",Comp_Code_Like:"",TeamId:""},tableData:[],pgType:""}},created:function(){},methods:{handleClose:function(){this.dialogVisible=!1},handleOpen:function(e){this.modelsearch.Task_Status_Int="",this.modelsearch.Comp_Code_Real="",this.modelsearch.Comp_Code_Like="",this.dialogVisible=!0,this.producedata=e,this.modelsearch.Task_Code=e.Code,this.modelsearch.Type=e.Type,this.modelsearch.Schduling_Id=e.Schduling_Id,this.modelsearch.TeamId=e.TeamId,this.modelsearch.Task_Id=e.Task_Id,this.getData()},getData:function(){var e=this;(0,l.GetProcessingProgressTask)((0,r.default)({},this.modelsearch)).then((function(t){t.IsSucceed?(null===t||void 0===t||t.Data.forEach((function(e){e.CurrentLocations=e.CurrentLocations.map((function(e){return"".concat(e.ProcessName,":").concat(e.CompCount)})).join("，")})),e.tableData=t.Data||[]):e.$message({message:t.Message,type:"error"})}))}}}},f584:function(e,t,a){"use strict";a.r(t);var o=a("e66a"),r=a.n(o);for(var l in o)["default"].indexOf(l)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(l);t["default"]=r.a},f833:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return r}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"page-container"},[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"add-dialog",attrs:{title:"生产进度",visible:e.dialogVisible,width:"90%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{staticStyle:{float:"left"},attrs:{inline:""}},[a("el-form-item",{attrs:{label:"排产单号："}},[e._v(" "+e._s(e.producedata.Schduling_Code)+" ")]),a("el-form-item",{attrs:{label:"任务单号："}},[e._v(" "+e._s(e.producedata.Code)+" ")]),a("el-form-item",{attrs:{label:"工序："}},[e._v(" "+e._s(e.producedata.ProcessName)+" ")]),a("el-form-item",{attrs:{label:"加工班组："}},[e._v(" "+e._s(e.producedata.TeamName)+" ")])],1),e.showSearch?a("el-form",{staticClass:"right-align",attrs:{inline:""}},[a("el-form-item",{attrs:{label:"加工进度：","label-width":"200px"}},[a("el-select",{attrs:{clearable:""},model:{value:e.modelsearch.Task_Status_Int,callback:function(t){e.$set(e.modelsearch,"Task_Status_Int",t)},expression:"modelsearch.Task_Status_Int"}},e._l(e.processingprogress,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1),e.showSearch?a("el-form-item",{attrs:{label:"0"===e.modelsearch.Type?"构件名称：":"3"===e.modelsearch.Type?"部件名称：":"零件名称："}},[a("el-input",{attrs:{placeholder:"请输入(空格区分/多个搜索)"},model:{value:e.modelsearch.Comp_Code_Real,callback:function(t){e.$set(e.modelsearch,"Comp_Code_Real",t)},expression:"modelsearch.Comp_Code_Real"}})],1):e._e(),e.showSearch?a("el-form-item",[a("el-input",{attrs:{placeholder:"(模糊查询)请输入关键字"},model:{value:e.modelsearch.Comp_Code_Like,callback:function(t){e.$set(e.modelsearch,"Comp_Code_Like",t)},expression:"modelsearch.Comp_Code_Like"}})],1):e._e(),e.showSearch?a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.getData}},[e._v("查询")])],1):e._e()],1):e._e(),a("div",{staticClass:"plm-bimtabler"},[a("el-table",{staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{stripe:"",border:"",data:e.tableData,height:"531px"}},["1"===e.modelsearch.Type?a("el-table-column",{attrs:{prop:"Part_Code",label:"零件名称",align:"center","show-overflow-tooltip":""}}):e._e(),"3"===e.modelsearch.Type?a("el-table-column",{attrs:{prop:"Part_Code",label:"部件名称",align:"center","show-overflow-tooltip":""}}):e._e(),a("el-table-column",{attrs:{prop:"Comp_Code",label:"0"===e.modelsearch.Type?"构件名称":"所属构件",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Current_Task_Count",label:"任务数量",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Current_Task_Weight",label:"任务重量(kg)",width:"100px",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Finish_Count",label:"完成数量",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Finish_Weight",label:"完成重量(kg)",width:"100px",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Task_Status_IntString",label:"加工进度",align:"center","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{style:"已完成"===t.row.Task_Status_IntString?{color:"#67c23a"}:{color:"red"}},[e._v(e._s(t.row.Task_Status_IntString))])]}}])}),a("el-table-column",{attrs:{prop:"CurrentLocations",label:"当前位置",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Project_Name",label:"所属项目",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Area_Name",label:"区域",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"InstallUnit_Name",label:"批次",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Ready_Receive_Count",label:"待接收",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Ready_Process_Count",label:"待加工",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Ready_Check_Count",label:"待报检",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Ready_Transfer_Count",label:"待转移","show-overflow-tooltip":"",align:"center"}}),a("el-table-column",{attrs:{prop:"Uncheck_CountString",label:"质检未完成",align:"center",width:"100px","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Uncheck_Change_CountString",label:"整改未完成",align:"center",width:"100px","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Uncheck_Recharge_CountString",label:"复核未完成",align:"center",width:"100px","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Transfering_Count",label:"下道未接收",align:"center",width:"100px","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"CheckUserName",label:"质检员",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"CheckName",label:"质检方式",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"SteelSpec",label:"规格",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"SteelLength",label:"长度",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"SteelMaterial",label:"材质",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"SteelWeight",label:"单重",align:"center","show-overflow-tooltip":""}})],1)],1)],1)],1)},r=[]}}]);