(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-268c457c"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,a){var i=o(),u=t-i,l=20,d=0;e="undefined"===typeof e?500:e;var s=function(){d+=l;var t=Math.easeInOutQuad(d,i,u,e);r(t),d<e?n(s):a&&"function"===typeof a&&a()};s()}},"1aba":function(t,e,a){"use strict";a.r(e);var n=a("48ce"),r=a("206e");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("c76a");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"7f17a926",null);e["default"]=u.exports},"1bb2":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddApproach=dt,e.AddBound=R,e.AddCheck=Mt,e.AddComponentOperation=et,e.AddLocation=x,e.AddMaterial=M,e.AddMaterialType=s,e.AddSettlement=nt,e.AddVendor=B,e.AddWareHouse=v,e.DeleteApproach=ct,e.DeleteBound=z,e.DeleteCheck=Lt,e.DeleteLocation=k,e.DeleteMaterial=L,e.DeleteMaterialType=f,e.DeleteSettlement=ot,e.DeleteVendor=W,e.DeleteWareHouse=G,e.EditApproach=st,e.EditBound=H,e.EditCheck=gt,e.EditLocation=T,e.EditMaterial=g,e.EditMaterialType=c,e.EditSettlement=rt,e.EditVendor=$,e.EditWareHouse=C,e.ExportApproach=pt,e.ExportCheck=_t,e.ExportInBound=vt,e.ExportOutBound=Ct,e.ExportSettlement=lt,e.GetApproach=ft,e.GetBoundDetailList=q,e.GetBoundEntity=V,e.GetBoundPageList=j,e.GetCheckDetailList=Pt,e.GetComponentLog=K,e.GetDetailEntity=bt,e.GetDictionaryDetailListByCode=Q,e.GetLocationEntity=E,e.GetLocationList=I,e.GetLocationPageList=D,e.GetLocationTree=S,e.GetMaterialInfoEntity=m,e.GetMaterialList=p,e.GetMaterialPageList=h,e.GetMaterialTypeEntity=d,e.GetMaterialTypeList=u,e.GetMaterialTypePageList=l,e.GetMaterialTypeTree=i,e.GetProfessionalType=U,e.GetProjectsNodeList=Y,e.GetProjectsNodebyType=J,e.GetSettlement=it,e.GetStockList=ht,e.GetStockPageList=F,e.GetStorageData=tt,e.GetStorageSteelData=Z,e.GetTraceData=X,e.GetTraceDetail=at,e.GetVendorEntity=w,e.GetVendorList=N,e.GetVendorPageList=O,e.GetWareHouseEntity=y,e.GetWareHouseList=_,e.GetWareHousePageList=b,e.GetWareHouseTree=A,e.GetWorking_ObjectList=o,e.ImportApproach=mt,e.ImportCheck=yt,e.ImportMaterial=P,e.ImportSettlement=ut;var r=n(a("b775"));function o(t){return(0,r.default)({url:"/PLM/Plm_Projects/GetWorking_ObjectList",method:"post",data:t})}function i(){return(0,r.default)({url:"/PLM/MaterialType/GetTree",method:"post"})}function u(t){return(0,r.default)({url:"/PLM/MaterialType/GetList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PLM/MaterialType/GetPageList",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PLM/MaterialType/GetEntity",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PLM/MaterialType/Add",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PLM/MaterialType/Edit",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PLM/MaterialType/Delete",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PLM/MaterialInfo/GetEntity",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PLM/MaterialInfo/GetList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PLM/MaterialInfo/GetPageList",method:"post",data:t})}function M(t){return(0,r.default)({url:"/PLM/MaterialInfo/Add",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PLM/MaterialInfo/Edit",method:"post",data:t})}function L(t){return(0,r.default)({url:"/PLM/MaterialInfo/Delete",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PLM/MaterialInfo/ImportMaterial",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PLM/MaterialWareHouse/GetEntity",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PLM/MaterialWareHouse/GetList",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PLM/MaterialWareHouse/GetPageList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PLM/MaterialWareHouse/Add",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PLM/MaterialWareHouse/Edit",method:"post",data:t})}function G(t){return(0,r.default)({url:"/PLM/MaterialWareHouse/Delete",method:"post",data:t})}function E(t){return(0,r.default)({url:"/PLM/MaterialLocation/GetEntity",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PLM/MaterialLocation/GetPageList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PLM/MaterialLocation/GetList",method:"post",data:t})}function S(t){return(0,r.default)({url:"/PLM/MaterialLocation/GetAppTree",method:"post",data:t})}function x(t){return(0,r.default)({url:"/PLM/MaterialLocation/Add",method:"post",data:t})}function T(t){return(0,r.default)({url:"/PLM/MaterialLocation/Edit",method:"post",data:t})}function k(t){return(0,r.default)({url:"/PLM/MaterialLocation/Delete",method:"post",data:t})}function A(){return(0,r.default)({url:"/PLM/MaterialLocation/GetTree",method:"post"})}function w(t){return(0,r.default)({url:"/PLM/MaterialVendor/GetEntity",method:"post",data:t})}function O(t){return(0,r.default)({url:"/PLM/MaterialVendor/GetPageList",method:"post",data:t})}function N(){return(0,r.default)({url:"/PLM/MaterialVendor/GetList",method:"post"})}function B(t){return(0,r.default)({url:"/PLM/MaterialVendor/Add",method:"post",data:t})}function $(t){return(0,r.default)({url:"/PLM/MaterialVendor/Edit",method:"post",data:t})}function W(t){return(0,r.default)({url:"/PLM/MaterialVendor/Delete",method:"post",data:t})}function V(t){return(0,r.default)({url:"/PLM/MaterialBound/GetEntity",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PLM/MaterialBound/GetPageList",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PLM/MaterialBound/Add",method:"post",data:t})}function H(t){return(0,r.default)({url:"/PLM/MaterialBound/Edit",method:"post",data:t})}function z(t){return(0,r.default)({url:"/PLM/MaterialBound/Delete",method:"post",data:t})}function q(t){return(0,r.default)({url:"/PLM/MaterialBound/GetDetailList",method:"post",data:t})}function F(t){return(0,r.default)({url:"/PLM/MaterialBound/GetStockPageList",method:"post",data:t})}function Q(t){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function U(t){return(0,r.default)({url:"/PLM/Plm_Professional_Type/GetAllEntities",method:"post",data:t})}function J(t){return(0,r.default)({url:"/PLM/Plm_Projects_Node/GetEntities",method:"post",data:t})}function Y(t){return(0,r.default)({url:"/PLM/Plm_Projects_Node/GetNodeList",method:"post",data:t})}function K(t){return(0,r.default)({url:"/PLM/ComponentLog/GetComponentLog",method:"post",data:t})}function X(t){return(0,r.default)({url:"/PLM/ComponentLog/GetTraceData",method:"post",data:t})}function Z(t){return(0,r.default)({url:"/PLM/ComponentLog/GetStorageSteelData",method:"post",data:t})}function tt(t){return(0,r.default)({url:"/PLM/ComponentLog/GetStorageData",method:"post",data:t})}function et(t){return(0,r.default)({url:"/PRO/Component/AddComponentOperation",method:"post",data:t})}function at(t){return(0,r.default)({url:"/PLM/ComponentLog/GetTraceDetail",method:"post",data:t})}function nt(t){return(0,r.default)({url:"/PLM/MaterialRegister/Add",method:"post",data:t})}function rt(t){return(0,r.default)({url:"/PLM/MaterialRegister/Edit",method:"post",data:t})}function ot(t){return(0,r.default)({url:"/PLM/MaterialRegister/Delete",method:"post",data:t})}function it(t){return(0,r.default)({url:"/PLM/MaterialRegister/GetEntity",method:"post",data:t})}function ut(t){return(0,r.default)({url:"/PLM/MaterialRegister/ImportSettlement",method:"post",data:t})}function lt(t){return(0,r.default)({url:"/PLM/MaterialRegister/ExportSettlement",method:"post",data:t})}function dt(t){return(0,r.default)({url:"/PLM/MaterialRegister/AddApproach",method:"post",data:t})}function st(t){return(0,r.default)({url:"/PLM/MaterialRegister/EditApproach",method:"post",data:t})}function ct(t){return(0,r.default)({url:"/PLM/MaterialRegister/DeleteApproach",method:"post",data:t})}function ft(t){return(0,r.default)({url:"/PLM/MaterialRegister/GetApproachEntity",method:"post",data:t})}function mt(t){return(0,r.default)({url:"/PLM/MaterialRegister/ImportApproach",method:"post",data:t})}function pt(t){return(0,r.default)({url:"/PLM/MaterialRegister/ExportApproach",method:"post",data:t})}function ht(t){return(0,r.default)({url:"/PLM/MaterialCheck/GetStockList",method:"post",data:t})}function Mt(t){return(0,r.default)({url:"/PLM/MaterialCheck/AddCheck",method:"post",data:t})}function gt(t){return(0,r.default)({url:"/PLM/MaterialCheck/EditCheck",method:"post",data:t})}function Lt(t){return(0,r.default)({url:"/PLM/MaterialCheck/DeleteCheck",method:"post",data:t})}function Pt(t){return(0,r.default)({url:"/PLM/MaterialCheck/GetCheckDetailList",method:"post",data:t})}function yt(t){return(0,r.default)({url:"/PLM/MaterialCheck/ImportCheck",method:"post",data:t})}function _t(t){return(0,r.default)({url:"/PLM/MaterialCheck/ExportCheck",method:"post",data:t})}function bt(t){return(0,r.default)({url:"/PLM/MaterialBound/GetDetailEntity",method:"post",data:t})}function vt(t){return(0,r.default)({url:"/PLM/MaterialBound/ExportInBound",method:"post",data:t})}function Ct(t){return(0,r.default)({url:"/PLM/MaterialBound/ExportOutBound",method:"post",data:t})}},"206e":function(t,e,a){"use strict";a.r(e);var n=a("a853"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"2a89":function(t,e,a){"use strict";a.r(e);var n=a("e36b"),r=a("6cdf");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("77fb");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"fdf86956",null);e["default"]=u.exports},"2cb1":function(t,e,a){"use strict";a("e822")},"38b5":function(t,e,a){"use strict";a.r(e);var n=a("922b"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"48ce":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"card-wrapper"},[a("div",{staticClass:"top-box"},[a("div",{staticClass:"t-box"},[a("i",{staticClass:"iconfont icon-steel"}),a("strong",{staticClass:"title"},[t._v(t._s(t.item.Short_Name))])]),a("div",{staticClass:"t-box-btn"},[a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete()}}},[t._v("删 除")]),a("el-button",{attrs:{size:"mini"},on:{click:t.handleEdit}},[t._v("编 辑")])],1)]),a("div",{staticClass:"sub-title"},[t._v(" "+t._s(t.item.Name)+" ")]),a("div",{staticClass:"tag-container"},[a("span",{staticClass:"tag-box"},[t._v(t._s(t.item.Category))])])])},r=[]},"4cd4":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("ab43"),a("d3b7");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),u=a("5e99"),l=a("1bb2"),d=a("ed08"),s=a("6186");e.default={props:{isEdit:{type:Boolean,default:!1}},data:function(){return{btnLoading:!1,form:{Manager:"",Short_Name:"",Name:"",Category:"",Professional_Codes:[""],Operate_Begin_Date:"",Capacity_Init:0,Manager_Id:"",Is_External:!1},tableData:[],comType:[],userOptions:[],rules:{Category:[{required:!0,message:"请选择",trigger:"change"}],Short_Name:[{required:!0,message:"请输入",trigger:"blur"}],Name:[{required:!0,message:"请输入",trigger:"blur"}],Is_External:[{required:!0,message:"请输入",trigger:"change"}]}}},created:function(){},mounted:function(){this.init()},methods:{init:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){var a;return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return a=localStorage.getItem("Last_Working_Object_Id"),e.n=1,(0,l.GetProfessionalType)({is_System:!1,pagesize:-1,companyId:a}).then((function(e){t.comType=e.Data.Data}));case 1:t.isEdit||(t.form.Operate_Begin_Date=(0,d.parseTime)(new Date,"{y}-{m}-{d}"));case 2:return e.a(2)}}),e)})))()},factoryChange:function(t){},changeCategory:function(t){var e=this.comType.find((function(e){return e.Name==t}));this.form.Professional_Codes[0]=e.Code},querySearchAsync:function(t,e){var a=this;return(0,i.default)((0,o.default)().m((function n(){var r;return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return r=[],n.n=1,a.getUserList(t);case 1:r=n.v,e(r);case 2:return n.a(2)}}),n)})))()},handleSelect:function(t){this.form.Manager_Id=t.Id,this.form.Manager=t.Display_Name},clear:function(){this.form.Manager_Id="",this.form.Manager="",this.$refs.autocomplete.activated=!0},blur:function(){var t=this,e=this.userOptions.find((function(e){return e.Display_Name==t.form.Manager}));e?(this.form.Manager_Id=e.Id,this.form.Manager=e.Display_Name):(this.form.Manager_Id="",this.form.Manager="")},getUserList:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return new Promise((function(a){(0,s.GetUserPage)({Page:1,pageSize:20,Search:e}).then((function(e){t.userOptions=e.Data.Data.map((function(e){return t.$set(e,"value",e.Display_Name),e})),a(t.userOptions)}))}))},handleEdit:function(t){var e=this;(0,u.GetFactoryEntity)({id:t.Id}).then((function(t){if(t.IsSucceed){var a=t.Data.entity,n=a.Name,r=a.Short_Name,o=a.Category,i=a.Id,u=a.Operate_Begin_Date,l=a.Capacity_Init,d=a.Manager_Id,s=a.Manager,c=a.Professional_Codes,f=a.Is_External;e.form.Short_Name=r,e.form.Name=n,e.form.Category=o,e.form.Id=i,e.form.Operate_Begin_Date=u,e.form.Capacity_Init=l,e.form.Manager_Id=d,e.form.Manager=s,e.form.Professional_Codes=c,e.form.Is_External=f}else e.$message({message:t.Message,type:"error"})}))},handleClose:function(){this.$emit("close")},handleSubmit:function(t){var e=this,a=(0,r.default)({},this.form);delete a["Operate_Begin_Date"],delete a["Capacity_Init"],this.$refs[t].validate((function(t){if(!t)return!1;var n={entity:a};e.btnLoading=!0,(0,u.SaveFactory)(n).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.btnLoading=!1,e.$emit("refresh"),e.$emit("close")):(e.btnLoading=!1,e.$message({message:t.Message,type:"error"}))}))}))}}}},"6c85":function(t,e,a){"use strict";a.r(e);var n=a("73dc"),r=a("38b5");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("2cb1");var i=a("2877"),u=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"94ac8d1e",null);e["default"]=u.exports},"6cdf":function(t,e,a){"use strict";a.r(e);var n=a("4cd4"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=r.a},"73dc":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"cs-z-flex-pd16-wrap x-container"},[a("div",{staticClass:"ml-8"},[a("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:t.handleAdd}},[t._v("新增")])],1),a("div",{staticClass:"cs-main"},t._l(t.list,(function(e,n){return a("card",{key:n,class:[{"mr-8":!n%4}],attrs:{item:e},on:{delete:function(a){return t.handleDelete(e)},edit:function(a){return t.handleEdit(e)}}})})),1),a("Pagination",{attrs:{total:t.total,page:t.listQuery.Page,limit:t.listQuery.PageSize,"page-sizes":[12,24,36,48],"page-size":12},on:{"update:page":function(e){return t.$set(t.listQuery,"Page",e)},"update:limit":function(e){return t.$set(t.listQuery,"PageSize",e)},pagination:t.fetchData}}),t.dialogVisible?a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:"40%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[t.dialogVisible?a(t.currentComponent,{ref:"content",tag:"component",attrs:{"is-edit":t.isEdit},on:{close:t.handleClose,refresh:t.fetchData}}):t._e()],1):t._e()],1)},r=[]},"77fb":function(t,e,a){"use strict";a("b530")},"834f":function(t,e,a){},"922b":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("1aba")),o=n(a("2a89")),i=a("5e99"),u=n(a("333d"));e.default={name:"PROBasicFactory",components:{Card:r.default,AddEdit:o.default,Pagination:u.default},data:function(){return{showDialog:!1,isEdit:!1,total:0,listQuery:{PageSize:12,Page:1},value:"",title:"",list:[],currentComponent:"",dialogVisible:!1}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var t=this;(0,i.GetFactoryPageList)(this.listQuery).then((function(e){e.IsSucceed?(t.list=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})}))},handleAdd:function(){this.openDialog(!1)},handleEdit:function(t){this.openDialog(!0,t)},openDialog:function(t,e){var a=this;this.isEdit=t,this.currentComponent="AddEdit",this.title=t?"编辑":"新增",this.dialogVisible=!0,this.$nextTick((function(n){t&&a.$refs["content"].handleEdit(e)}))},handleClose:function(){this.dialogVisible=!1},handleDelete:function(t){var e=this;this.$confirm("删除后该工厂数据将无法查看，是否确认删除该工厂?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,i.DeleteFactory)({id:t.Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))}}}},a853:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={props:{item:{type:Object,default:function(){}}},computed:{WarehouseList:function(){return this.item.Warehouse_Name&&this.item.Warehouse_Name.length?this.item.Warehouse_Name.split(","):[]}},methods:{handleEdit:function(t){this.$emit("edit",t)},handleDelete:function(t){this.$emit("delete",t)}}}},b530:function(t,e,a){},c76a:function(t,e,a){"use strict";a("834f")},e36b:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"title-box"},[a("strong",[t._v("基本信息")]),t.isEdit?t._e():a("strong",{staticClass:"message"},[t._v("(工厂类型选择后不可更改，请慎重！)")])]),a("el-form",{ref:"form",staticStyle:{"padding-top":"10px"},attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"工厂加工类别",prop:"Category"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:t.isEdit},on:{change:t.changeCategory},model:{value:t.form.Category,callback:function(e){t.$set(t.form,"Category",e)},expression:"form.Category"}},t._l(t.comType,(function(t){return a("el-option",{key:t.Id,attrs:{value:t.Name}})})),1)],1),a("el-form-item",{attrs:{label:"是否外协工厂",prop:"Is_External"}},[a("el-radio-group",{model:{value:t.form.Is_External,callback:function(e){t.$set(t.form,"Is_External",e)},expression:"form.Is_External"}},[a("el-radio",{attrs:{label:!0}},[t._v("是")]),a("el-radio",{attrs:{label:!1}},[t._v("否")])],1)],1),a("el-form-item",{attrs:{label:"工厂名称",prop:"Short_Name"}},[a("el-input",{attrs:{maxlength:"50","show-word-limit":""},model:{value:t.form.Short_Name,callback:function(e){t.$set(t.form,"Short_Name",e)},expression:"form.Short_Name"}})],1),a("el-form-item",{attrs:{label:"工厂全称",prop:"Name"}},[a("el-input",{attrs:{maxlength:"50","show-word-limit":""},model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name",e)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"厂长",prop:"Manager"}},[a("el-autocomplete",{ref:"autocomplete",staticStyle:{width:"100%"},attrs:{"fetch-suggestions":t.querySearchAsync,clearable:"",placeholder:"请选择"},on:{clear:t.clear,select:t.handleSelect,blur:t.blur},model:{value:t.form.Manager,callback:function(e){t.$set(t.form,"Manager",e)},expression:"form.Manager"}})],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:t.handleClose}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit("form")}}},[t._v("确 定")])],1)],1)},r=[]},e822:function(t,e,a){}}]);