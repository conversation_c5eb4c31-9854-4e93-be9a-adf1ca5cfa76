(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-343fe9a4"],{"04b3b":function(t,e,i){"use strict";var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d3b7");var l=a(i("c14f")),r=a(i("1da1")),o=a(i("ac03")),n=i("cf45"),s=i("4f39"),c=a(i("3502")),d=i("bc1b"),f=i("e961"),m=i("05e0"),u={day:1,month:2,year:3};e.default={name:"PROOPReportDetail",components:{VTable:c.default},mixins:[o.default],data:function(){return{reportType:u.day,dateType:u,form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",Type:"",FactoryId:""},projectOption:[],showExport:!1}},computed:{curTitle:function(){var t="";return this.reportType===u.year?t="".concat((0,s.timeFormat)(this.form.StatisticalDate,"{y}年"),"项目合计"):this.reportType===u.month?t="".concat((0,s.timeFormat)(this.form.StatisticalDate,"{y}年{m}月"),"项目合计"):this.reportType===u.day&&(t="".concat((0,s.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")),t}},watch:{reportType:function(){this.formatDate(this.form.StatisticalDate)}},beforeCreate:function(){this.curModuleKey=f.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,l.default)().m((function e(){return(0,l.default)().w((function(e){while(1)switch(e.n){case 0:return t.showExport=t.getRoles("OMAOPDetailExport"),t.form.FactoryId=t.factoryId,t.$route.query.d?(t.staticTime=t.$route.query.d,t.form.StatisticalDate=t.$route.query.d):t.form.StatisticalDate=t.originDate,t.formatDate(t.form.StatisticalDate),t.fetchData(),e.n=1,(0,n.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},methods:{startExport:function(){this.handleExport(this.curTitle+"运营汇总表")},formatDate:function(t){return this.reportType===u.year?t=(0,s.timeFormat)(t,"{y}"):this.reportType===u.month?t=(0,s.timeFormat)(t,"{y}-{m}"):this.reportType===u.day&&(t=(0,s.timeFormat)(t,"{y}-{m}-{d}")),this.setDate(t),t},setDate:function(t){this.form.StatisticalDate=t},fetchData:function(){var t=this;if(this.checkDate()){this.loading=!0;var e={StatisticalDate:this.form.StatisticalDate,Type:this.reportType,SearchKey:this.form.SearchKey,ProjectStatus:this.form.ProjectStatus,FactoryId:this.form.FactoryId};(0,d.GetOperationalSummaryDetail)(e).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[];var i=t.setTotalData(t.tableData,t.generateColumn()),a=i.column;t.columns=a,t.$refs["tb"].setColumns(a)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1}))}},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var t=160;return[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:3},field:"ProjectAbbreviation",minWidth:m.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:m.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:m.ProjectStatusW}]}]},{title:"物控部",params:{bg:"bg-green"},children:[{title:"物控产值(元)",children:[{minWidth:t,field:"WK_Output",title:0,isTotal:!0}]},{title:"营业外收入(元)",children:[{minWidth:t,field:"WK_NonBusiness_Income",title:0,isTotal:!0}]},{title:"物控成本(元)",children:[{minWidth:t,field:"WK_Cost",title:0,isTotal:!0}]},{title:"折旧资产(元)",children:[{minWidth:t,field:"WK_Assets",title:0,isTotal:!0}]},{title:"物控费用(元)",children:[{minWidth:t,field:"WK_Fees",title:0,isTotal:!0}]},{title:"物控结存(元)",children:[{minWidth:t,field:"WK_Stock",title:0,isTotal:!0}]},{title:"物控利润(元)",children:[{minWidth:t,field:"WK_Profit",title:0,isTotal:!0}]}]},{title:"生产部",params:{bg:"bg-blue"},children:[{title:"生产产值(元)",children:[{minWidth:t,field:"SC_Output",title:0,isTotal:!0}]},{title:"营业外收入(元)",children:[{minWidth:t,field:"SC_NonBusiness_Income",title:0,isTotal:!0}]},{title:"生产成本(元)",children:[{minWidth:t,field:"SC_Cost",title:0,isTotal:!0}]},{title:"折旧资产(元)",children:[{minWidth:t,field:"SC_Assets",title:0,isTotal:!0}]},{title:"生产费用(元)",children:[{minWidth:t,field:"SC_Fees",title:0,isTotal:!0}]},{title:"生产结存(元)",children:[{minWidth:t,field:"SC_Stock",title:0,isTotal:!0}]},{title:"生产利润(元)",children:[{minWidth:t,field:"SC_Profit",title:0,isTotal:!0}]}]},{title:"营销部",params:{bg:"bg-orange"},children:[{title:"营销产值(元)",children:[{minWidth:t,field:"YX_Output",title:0,isTotal:!0}]},{title:"营业外收入(元)",children:[{minWidth:t,field:"YX_NonBusiness_Income",title:0,isTotal:!0}]},{title:"营销成本(元)",children:[{minWidth:t,field:"YX_Cost",title:0,isTotal:!0}]},{title:"折旧资产(元)",children:[{minWidth:t,field:"YX_Assets",title:0,isTotal:!0}]},{title:"营销费用(元)",children:[{minWidth:t,field:"YX_Fees",title:0,isTotal:!0}]},{title:"营销结存(元)",children:[{minWidth:t,field:"YX_Stock",title:0,isTotal:!0}]},{title:"营销利润(元)",children:[{minWidth:t,field:"YX_Profit",title:0,isTotal:!0}]}]},{title:"综合部门",children:[{title:"部门费用(元)",children:[{field:"QT_Fees",minWidth:t,title:0,isTotal:!0}]}]},{title:"代购代付利润",children:[{title:"管理利润(元)",children:[{field:"DD_Profit",minWidth:t,title:0,isTotal:!0}]}]}]}}}},"13b3f":function(t,e,i){"use strict";i("9717")},"7c65":function(t,e,i){"use strict";i.r(e);var a=i("f5e4"),l=i("fa0a");for(var r in l)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return l[t]}))}(r);i("13b3f");var o=i("2877"),n=Object(o["a"])(l["default"],a["a"],a["b"],!1,null,"a1baa6ee",null);e["default"]=n.exports},9717:function(t,e,i){},cf45:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=l,i("d3b7");var a=i("6186");function l(t){return new Promise((function(e,i){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},f5e4:function(t,e,i){"use strict";i.d(e,"a",(function(){return a})),i.d(e,"b",(function(){return l}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[i("div",{staticClass:"cs-z-page-main-content"},[i("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[t.reportType===t.dateType.year?i("el-date-picker",{key:1,staticClass:"cs-date",staticStyle:{width:"180px"},attrs:{"value-format":"yyyy",type:"year",clearable:!1,"picker-options":t.pickerOptions,placeholder:"选择年"},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}}):t._e(),t.reportType===t.dateType.month?i("el-date-picker",{key:3,staticClass:"cs-date",staticStyle:{width:"180px"},attrs:{clearable:!1,"value-format":"yyyy-MM",type:"month",placeholder:"选择月"},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}}):t._e(),t.reportType===t.dateType.day?i("el-date-picker",{key:4,staticClass:"cs-date",staticStyle:{width:"180px"},attrs:{clearable:!1,"value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日"},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}}):t._e()],1),i("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[i("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),i("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[i("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),i("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),i("el-divider"),i("div",{staticClass:"tb-info"},[i("label",[t._v("数据列表"+t._s(t.reportType))]),i("div",{staticClass:"btn-x"},[t.showExport?i("el-button",{staticStyle:{"margin-right":"10px"},attrs:{disabled:t.isEmpty},on:{click:t.startExport}},[t._v("导出报表")]):t._e(),t.getRoles("OMAOPDetailDate")?i("el-select",{attrs:{placeholder:"请选择"},model:{value:t.reportType,callback:function(e){t.reportType=e},expression:"reportType"}},[i("el-option",{attrs:{label:"年报表",value:t.dateType.year}}),i("el-option",{attrs:{label:"月报表",value:t.dateType.month}}),i("el-option",{attrs:{label:"日报表",value:t.dateType.day}})],1):t._e()],1)]),i("div",{staticClass:"tb-x"},[i("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},l=[]},fa0a:function(t,e,i){"use strict";i.r(e);var a=i("04b3b"),l=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=l.a}}]);