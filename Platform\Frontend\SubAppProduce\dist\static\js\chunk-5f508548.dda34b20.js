(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5f508548"],{"0cdfa":function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(r("5a2d"));t.default={name:"Procurement",components:{ProcurementFlow:o.default},props:{isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}}}},"222a":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"108px"}},[r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"归属项目",required:"",prop:"Project_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选择所属项目",filterable:""},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projs,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Project_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"工期",required:"",prop:"Schedule"}},[e.isEdit?r("el-input",{model:{value:e.form.Schedule,callback:function(t){e.$set(e.form,"Schedule",t)},expression:"form.Schedule"}}):r("span",[e._v(e._s(e.form.Schedule))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"特定内容",required:"",prop:"Content"}},[e.isEdit?r("el-input",{model:{value:e.form.Content,callback:function(t){e.$set(e.form,"Content",t)},expression:"form.Content"}}):r("span",[e._v(e._s(e.form.Content))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合作形式",required:"",prop:"Project_Category"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选合作形式"},model:{value:e.form.Project_Category,callback:function(t){e.$set(e.form,"Project_Category",t)},expression:"form.Project_Category"}},e._l(e.cooperations,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Value}})})),1):r("span",[e._v(e._s(e.form.Project_Category_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同编号",required:"",prop:"Contract_Code"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Code,callback:function(t){e.$set(e.form,"Contract_Code",t)},expression:"form.Contract_Code"}}):r("span",[e._v(e._s(e.form.Contract_Code))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同名称",required:"",prop:"Contract_Name"}},[e.isEdit?r("el-input",{model:{value:e.form.Contract_Name,callback:function(t){e.$set(e.form,"Contract_Name",t)},expression:"form.Contract_Name"}}):r("span",[e._v(e._s(e.form.Contract_Name))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"承揽单位",required:"",prop:"Provider_Id"}},[e.isEdit?r("el-select",{attrs:{placeholder:"请选承揽单位",filterable:""},model:{value:e.form.Provider_Id,callback:function(t){e.$set(e.form,"Provider_Id",t)},expression:"form.Provider_Id"}},e._l(e.providers,(function(e){return r("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):r("span",[e._v(e._s(e.form.Supplier_Name))])],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"合同价格",required:"",prop:"Price"}},[e.isEdit?r("el-input-number",{attrs:{min:0},model:{value:e.form.Price,callback:function(t){e.$set(e.form,"Price",e._n(t))},expression:"form.Price"}}):r("span",[e._v(e._s(e.form.Price))])],1)],1)],1),r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"特定采购形式",required:"",prop:"Given_Style"}},[e.isEdit?r("el-input",{model:{value:e.form.Given_Style,callback:function(t){e.$set(e.form,"Given_Style",t)},expression:"form.Given_Style"}}):r("span",[e._v(e._s(e.form.Given_Style))])],1)],1)],1),e.isEdit?e._e():r("el-row",{attrs:{gutter:16}},[r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"说明",required:"",prop:"Remark"}},[r("span",[e._v(e._s(e.form.Remark))])])],1)],1),e.isEdit?e._e():r("el-form-item",{attrs:{label:"附件",required:!0,prop:"Attachments"}},[r("ul",{staticClass:"el-upload-list el-upload-list--text",staticStyle:{"margin-top":"-4px"}},e._l(e.form.Attachments,(function(t,a){return r("li",{key:a,staticClass:"el-upload-list__item is-success",attrs:{tabindex:"0"}},[r("a",{staticClass:"el-upload-list__item-name",on:{click:function(r){return e.attachOpen(t)}}},[r("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t?t.split("/")[t.split("/").length-1]:"")+" ")])])})),0)]),e.isEdit?e._t("attachment"):e._e()],2)},o=[]},"2fde":function(e,t,r){"use strict";r("e812")},"4d85":function(e,t,r){"use strict";r.r(t);var a=r("c495"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},"55cc":function(e,t,r){"use strict";r.r(t);var a=r("9fa0"),o=r("f0ce");for(var n in o)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return o[e]}))}(n);r("2fde");var i=r("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"1e0f550e",null);t["default"]=l.exports},"5a2d":function(e,t,r){"use strict";r.r(t);var a=r("222a"),o=r("4d85");for(var n in o)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return o[e]}))}(n);var i=r("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,null,null);t["default"]=l.exports},"9fa0":function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("h3",[e._v("特殊情况采购审批")]),r("procurement-flow",{attrs:{"is-edit":e.isEdit,datas:e.datas}})],1)},o=[]},c495:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("7db0"),r("e9f5"),r("f665"),r("d3b7");var o=a(r("5530"));t.default={name:"ProcurementFlow",props:{projs:{type:Array,default:function(){return[]}},providers:{type:Array,default:function(){return[]}},channels:{type:Array,default:function(){return[]}},cooperations:{type:Array,default:function(){return[]}},invoices:{type:Array,default:function(){return[]}},rates:{type:Array,default:function(){return[]}},seals:{type:Array,default:function(){return[]}},entity:{type:Object,default:function(){return{}}},isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{Project_Id:"",Schedule:"",Content:"",Project_Category:"",Contract_Code:"",Contract_Name:"",Provider_Id:"",Price:"",Given_Style:"",Remark:"",Attachments:[],Projece_Name:"",Supplier_Name:"",Project_Category_Name:""},rules:{Project_Id:[{required:!0,message:"请选择所属项目",trigger:"blur"}],Schedule:[{required:!0,message:"请输入工期",trigger:"blur"}],Content:[{required:!0,message:"请输入特定内容",trigger:"blur"}],Project_Category:[{required:!0,message:"请选择合作形式",trigger:"blur"}],Contract_Code:[{required:!0,message:"请输入合同编号",trigger:"blur"}],Contract_Name:[{required:!0,message:"请输入合同名称",trigger:"blur"}],Provider_Id:[{required:!0,message:"请选择承揽单位",trigger:"blur"}],Price:[{required:!0,message:"请输入合同价格",trigger:"blur"}],Given_Style:[{required:!0,message:"请输入特定采购形式",trigger:"blur"}],Remark:[{required:!0,message:"请填写说明",trigger:"blur"}],Attachments:[{required:!0,type:"array",message:"至少包含一个附件",trigger:"change"}]}}},watch:{entity:function(e){this.form=(0,o.default)((0,o.default)({},this.form),this.entity)},datas:function(e){this.form=(0,o.default)((0,o.default)({},this.form),this.datas)}},created:function(){this.form=(0,o.default)((0,o.default)((0,o.default)({},this.form),this.entity),this.datas)},methods:{validate:function(){var e=this;return new Promise((function(t,r){e.$refs.form.validate((function(e){if(!e)return r(!1),!1;t(!0)}))}))},getData:function(){var e=this,t=(0,o.default)({},this.form);return t.Project_Name=this.projs.find((function(t){return t.Id===e.form.Project_Id}))?this.projs.find((function(t){return t.Id===e.form.Project_Id})).Name:"",t.Supplier_Name=this.providers.find((function(t){return t.Id===e.form.Provider_Id}))?this.providers.find((function(t){return t.Id===e.form.Provider_Id})).Name:"",t.Project_Category_Name=this.cooperations.find((function(t){return t.Value===e.form.Project_Category}))?this.cooperations.find((function(t){return t.Value===e.form.Project_Category})).Display_Name:"",t},attachOpen:function(e){window.open(e,"_blank")}}}},e812:function(e,t,r){},f0ce:function(e,t,r){"use strict";r.r(t);var a=r("0cdfa"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a}}]);