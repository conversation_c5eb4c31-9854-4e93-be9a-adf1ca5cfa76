(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-698e74eb"],{"062b":function(t,e,n){"use strict";n.r(e);var a=n("25aa"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},1078:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"box-card"},[n("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"进行中",name:"first"}}),n("el-tab-pane",{attrs:{label:"已完成",name:"second"}})],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-z-tb-wrapper"},[n("dynamic-data-table",{key:t.activeName,ref:"dyTable",attrs:{columns:t.columns,data:t.tbData,config:t.tbConfig,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{tableSearch:t.tableSearch,gridSizeChange:t.handlePageChange,gridPageChange:t.handlePageChange},scopedSlots:t._u([{key:"hsearch_Project_Name",fn:function(e){var a=e.column;return[n("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},on:{change:t.projectChange},model:{value:t.$refs.dyTable.searchedField[a.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,a.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.projects,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"hsearch_Installunit_Name",fn:function(e){var a=e.column;return[n("el-select",{attrs:{disabled:!t.$refs.dyTable.searchedField["Project_Name"],placeholder:"请选择",clearable:""},on:{change:t.showSearchBtn},model:{value:t.$refs.dyTable.searchedField[a.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,a.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.installOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"Ultimate_Demand_End_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Ultimate_Demand_End_Date))+" ")]}},{key:"Unfinish_Count_Weight",fn:function(e){var a=e.row;return[n("div",{staticClass:"teble-pro",on:{click:function(e){return t.openDialog(a,"生产未完成量明细")}}},[n("i",{staticClass:"el-icon-link"}),t._v(" "+t._s(a.Unfinish_Count_Weight)+" ")])]}},{key:"Finish_Count_Weight",fn:function(e){var a=e.row;return[n("div",{staticClass:"teble-pro",on:{click:function(e){return t.openDialog(a,"生产完成量明细")}}},[n("i",{staticClass:"el-icon-link"}),t._v(" "+t._s(a.Finish_Count_Weight)+" ")])]}},{key:"Finish_Percentage_w",fn:function(e){var a=e.row;return[n("div",{staticClass:"teble-pro"},[0===a.Finish_Percentage_w?n("span",{staticClass:"grey"},[t._v(t._s(a.Finish_Percentage_w)+" %")]):100===a.Finish_Percentage_w?n("span",{staticClass:"green"},[t._v(t._s(a.Finish_Percentage_w)+" %")]):n("span",{staticClass:"blue"},[t._v(t._s(a.Finish_Percentage_w.toFixed(2))+" %")])])]}},{key:"Stock_In_Count_Weight",fn:function(e){var a=e.row;return[n("div",{staticClass:"teble-pro",on:{click:function(e){return t.openDialog(a,"在库量明细")}}},[n("i",{staticClass:"el-icon-link"}),t._v(" "+t._s(a.Stock_In_Count_Weight)+" ")])]}},{key:"Stock_Out_Count_Weight",fn:function(e){var a=e.row;return[n("div",{staticClass:"teble-pro",on:{click:function(e){return t.openDialog(a,"出库量明细")}}},[n("i",{staticClass:"el-icon-link"}),t._v(" "+t._s(a.Stock_Out_Count_Weight)+" ")])]}}])})],1)],1),n("Dialog",{ref:"dialog"})],1)},o=[]},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,r=t.Data,s=t.Message;if(a){if(!r)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,r.Grid),l=n?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+r.Grid.Row_Number||o.tablePageSize[0]),i(e.columns)}else e.$message({message:s,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===e){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1faa":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("d3b7");var o=a(n("c14f")),i=a(n("1da1")),r=a(n("0f97")),s=a(n("34e9")),l=n("6b87"),u=a(n("15ac")),c=n("8975"),d=n("1b69"),f=n("f2f6"),m=a(n("d169"));e.default={name:"PROProducedCount",components:{DynamicDataTable:r.default,TopHeader:s.default,Dialog:m.default},filters:{timeFormat:c.timeFormat},mixins:[u.default],data:function(){return{queryInfo:{Page:1,PageSize:20,ParameterJson:[{Key:"Finish_Percentage_w",Type:"number",Filter_Type:"numberrange",Value:[0,99.99]}]},projects:[],installOption:[],installName:"",activeName:"first",columns:[],tbData:[],total:0,tbConfig:{Pager_Align:"center"},tbLoading:!0}},mounted:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig("ProUnitProducedCount");case 1:t.fetchData(),(0,d.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}));case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.tbLoading=!0,(0,l.GetInstallUnitProducedCount)({model:this.queryInfo}).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleClick:function(){"second"===this.activeName?(this.queryInfo={Page:1,PageSize:20,ParameterJson:[{Key:"Finish_Percentage_w",Type:"number",Filter_Type:"numberrange",Value:[100,null]}]},this.getTableConfig("ProCountFinish")):(this.queryInfo={Page:1,PageSize:20,ParameterJson:[{Key:"Finish_Percentage_w",Type:"number",Filter_Type:"numberrange",Value:[0,99.999]}]},this.getTableConfig("ProUnitProducedCount")),this.fetchData()},getUnitList:function(t){var e=this;(0,f.GetInstallUnitList)({Project_Id:t}).then((function(t){t.IsSucceed&&(e.installOption=t.Data)}))},projectChange:function(t){if(this.installOption=[],t){var e=this.projects.find((function(e){return e.Name===t}));this.getUnitList(e.Id)}this.showSearchBtn()},openDialog:function(t,e){this.$refs.dialog.open(t,e)}}}},"25aa":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7"),n("3ca3"),n("ddb0");var o=a(n("d7b0")),i=n("7f9d"),r=n("586a");e.default={components:{QrcodeVue:o.default},props:{drawer:{type:Boolean,default:!1},isProHistoryCard:{type:Boolean,default:!1}},data:function(){return{list:[],ComCode:"",QrCode:"",showBackIcon:!1,form:{Project_Name:"",Sub_Type_Name:"",Unique_Code:"",Comp_Code:""},receiveStatues:{unPro:1,inPro:2,finish:3,transfer:4},loading:!0,isProHistory:!0}},computed:{title:function(){return this.isProHistory?"生产历史":"构件历史"}},watch:{drawer:function(t){!t&&(this.showBackIcon=!1)}},methods:{fetchData:function(t){this.isProHistory=this.isProHistoryCard,this.isProHistory?this.getProHistory(t):(this.ComCode=t,this.getComHistoryInfo(this.ComCode))},getComHistoryInfo:function(t){var e=this;this.loading=!0;var n=this.getComHistoryList(t),a=this.getComFormInfo(t);Promise.all([n,a]).then((function(t){e.loading=!1}))},getProHistory:function(t){var e=this;this.loading=!0,(0,i.GetTeamCompHistory)({ucode:t}).then((function(t){var n,a;t.IsSucceed?(e.list=t.Data.History_Records||[],e.form=null===(n=t.Data)||void 0===n?void 0:n.Comp,e.QrCode=null===(a=e.form)||void 0===a?void 0:a.Scan_Url):e.$message({message:t.Message,type:"error"});e.loading=!1}))},getComHistoryList:function(t){var e=this;return new Promise((function(n){(0,r.GetComponentLogList)({uniqueCode:t}).then((function(t){t.IsSucceed?(e.list=t.Data||[],e.list=e.list.map((function(t){return"SCZ"!==t.Code&&"SCWC"!==t.Code||e.$set(t,"showBtn",!0),t}))):e.$message({message:t.Message,type:"error"}),n()}))}))},getComFormInfo:function(t){var e=this;return new Promise((function(n){(0,r.GetComponentEntityWithUniqueCode)({uniquecode:t}).then((function(t){if(t.IsSucceed){if(!t.Data)return e.form.Project_Name="",e.form.Sub_Type_Name="",e.form.Comp_Code="",e.form.Unique_Code="",e.QrCode="",void n();var a=t.Data,o=a.Project_Name,i=a.Sub_Type_Name,r=a.Code,s=a.Unique_Code,l=a.Id;e.form.Project_Name=o,e.form.Sub_Type_Name=i,e.form.Comp_Code=r,e.form.Unique_Code=s,e.QrCode=l}else e.$message({message:t.Message,type:"error"});n()}))}))},handleDetail:function(t){this.showBackIcon=!0,this.isProHistory=!0,this.getProHistory(t.Component_UniqueCode)},goBack:function(){this.isProHistory=!1,this.showBackIcon=!1,this.getComHistoryInfo(this.ComCode)},handleClose:function(t){this.$emit("update:drawer",!1)}}}},"37a6":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-drawer",{staticClass:"cs-draw",attrs:{modal:!1,"destroy-on-close":"","modal-append-to-body":!1,size:"432px",visible:t.drawer,direction:"rtl","before-close":t.handleClose},on:{"update:visible":function(e){t.drawer=e}},scopedSlots:t._u([{key:"title",fn:function(){return[n("div",[t.showBackIcon?n("i",{staticClass:"el-icon-d-arrow-left",on:{click:t.goBack}}):t._e(),t._v(" "+t._s(t.title)+" ")])]},proxy:!0}])},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"wrapper",attrs:{"element-loading-text":"拼命加载中"}},[n("div",{staticClass:"detail"},[n("div",{staticClass:"info"},[n("strong",{staticClass:"title"},[t._v(t._s(t.form.Project_Name))]),n("div",{staticClass:"types"},[n("strong",[t._v(t._s(t.form.Sub_Type_Name))]),n("strong",[t._v(t._s(t.form.Comp_Code))])]),n("strong",{staticClass:"code"},[t._v(t._s(t.form.Unique_Code))])]),n("div",{staticClass:"scan-x"},[t.QrCode?n("qrcode-vue",{attrs:{size:90,value:t.QrCode,"class-name":"qrcode",level:"H"}}):t._e()],1)]),t.list.length?n("div",{staticClass:"list-x"},[t.isProHistory?t._l(t.list,(function(e){return n("div",{key:e.Id,class:["list circle",e.Status===t.receiveStatues.unPro?"z-radio-blue-b":e.Status===t.receiveStatues.inPro?"z-radio-yellow-b":e.Status===t.receiveStatues.transfer?"z-radio-y2-b":"z-radio-gray-b"]},[n("div",{staticClass:"title"},[n("label",[t._v("工序： "),n("strong",[t._v(t._s(e.Working_Process_Name))])]),n("el-tag",{class:["cs-tag",e.Status===t.receiveStatues.unPro?"blue":e.Status===t.receiveStatues.inPro?"yellow":e.Status===t.receiveStatues.transfer?"dark_yellow":"gray"],attrs:{size:"medium"}},[t._v(t._s(e.Status_Text)+" ")])],1),n("label",[t._v("班组： "),n("strong",[t._v(t._s(e.Working_Team_Name))])])])})):t._l(t.list,(function(e){return n("div",{key:e.Id,staticClass:"list com-item"},[n("div",{staticClass:"fx"},[n("div",{staticClass:"tag-title-x"},[n("strong",{staticClass:"tag-title"},[t._v(t._s(e.Operation))])]),e.showBtn?n("el-button",{staticStyle:{color:"#298DFF"},attrs:{size:"mini"},on:{click:function(n){return t.handleDetail(e)}}},[t._v("详情")]):t._e()],1),n("div",{staticClass:"fx-c"},[n("label",[t._v("人员： "),n("strong",[t._v(t._s(e.Create_UserName))])]),n("label",[t._v("时间： "),n("strong",[t._v(t._s(t._f("timeFormat")(e.Operation_Time,"{y}-{m}-{d} {h}:{i}:{s}")))])])])])}))],2):t._e()])])},o=[]},"481f":function(t,e,n){},"4e82":function(t,e,n){"use strict";var a=n("23e7"),o=n("e330"),i=n("59ed"),r=n("7b0b"),s=n("07fa"),l=n("083a"),u=n("577e"),c=n("d039"),d=n("addb"),f=n("a640"),m=n("3f7e"),h=n("99f4"),p=n("1212"),g=n("ea83"),_=[],C=o(_.sort),b=o(_.push),v=c((function(){_.sort(void 0)})),P=c((function(){_.sort(null)})),y=f("sort"),I=!c((function(){if(p)return p<70;if(!(m&&m>3)){if(h)return!0;if(g)return g<603;var t,e,n,a,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)_.push({k:e+a,v:n})}for(_.sort((function(t,e){return e.v-t.v})),a=0;a<_.length;a++)e=_[a].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),D=v||!P||!y||!I,w=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:u(e)>u(n)?1:-1}};a({target:"Array",proto:!0,forced:D},{sort:function(t){void 0!==t&&i(t);var e=r(this);if(I)return void 0===t?C(e):C(e,t);var n,a,o=[],u=s(e);for(a=0;a<u;a++)a in e&&b(o,e[a]);d(o,w(t)),n=s(o),a=0;while(a<n)e[a]=o[a++];while(a<u)l(e,a++);return e}})},5575:function(t,e,n){"use strict";n("481f")},"5ff1":function(t,e,n){"use strict";n("e0c4")},"65a7":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("d81d"),n("14d9"),n("e9f5"),n("ab43"),n("d3b7"),n("3ca3"),n("ddb0");var o=a(n("0f97")),i=a(n("34e9")),r=a(n("15ac")),s=n("8975"),l=n("6b87"),u=a(n("b93a"));e.default={components:{DynamicDataTable:o.default,TopHeader:i.default,UniqueCard:u.default},filters:{timeFormat:s.timeFormat},mixins:[r.default],data:function(){return{drawer:!1,dialogVisible:!1,title:"",columns:[],tbData:[],tbConfig:{Pager_Align:"center"},queryInfo:{Page:1},tbLoading:!0,isProHistoryCard:!1,tableTitle:""}},methods:{open:function(t,e){switch(this.tbData=[],this.columns=[],this.dialogVisible=!0,this.title=e,this.tableTitle="".concat(t.Project_Name," / ").concat(t.Installunit_Name),e){case"生产未完成量明细":this.getTableConfig("ProUnFinishProducedCount"),this.fetchData(t.InstallUnit_Id);break;case"生产完成量明细":this.getTableConfig("ProFinishProducedCount"),this.fetchData(t.InstallUnit_Id);break;case"在库量明细":this.getTableConfig("InDetails"),this.fetchData(t.InstallUnit_Id);break;default:this.getTableConfig("OutDetails"),this.fetchData(t.InstallUnit_Id)}},fetchData:function(t){var e=this;this.tbLoading=!0,"生产未完成量明细"===this.title?(0,l.GetComponentNoProduceDetailPageList)({model:{InstallUnit_Id:t,PageSize:-1}}).then((function(t){t.IsSucceed?e.tbData=t.Data.Data:e.$message({message:t.Message,type:"error"}),e.tbLoading=!1})):"生产完成量明细"===this.title?(0,l.GetProducedDetailPageList)({model:{InstallUnit_Id:t,PageSize:-1}}).then((function(t){t.IsSucceed?e.tbData=t.Data.Data:e.$message({message:t.Message,type:"error"}),e.tbLoading=!1})):"在库量明细"===this.title?(0,l.GetComponentInfoPageList)({InstallUnit_Id:t,C_Type:"全部",Page:1,Factory_Id:"0",PageSize:-1,ParameterJson:[{Key:"Whole_Status_Name",Value:["已入库"],Type:"text",Filter_Type:"radio"}]}).then((function(t){t.IsSucceed?e.tbData=t.Data.Data:e.$message({message:t.Message,type:"error"}),e.tbLoading=!1})):(0,l.GetComponentInfoPageList)({InstallUnit_Id:t,C_Type:"全部",Page:1,Factory_Id:"0",PageSize:-1,ParameterJson:[{Key:"Whole_Status_Name",Value:["已出库","退货待入库","退库待入库","进场","安装"],Type:"text",Filter_Type:"radio"}]}).then((function(t){t.IsSucceed?e.tbData=t.Data.Data:e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},close:function(){this.dialogVisible=!1,this.tbData=[],this.columns=[]},openDrawer:function(t){var e=this;this.drawer=!0,this.isProHistoryCard="生产未完成量明细"===this.title,this.$nextTick((function(n){e.$refs["unique"].fetchData(t.Unique_Code)}))},handleExport:function(){var t=this,e=[],a=[];this.columns.map((function(t){e.push(t.Display_Name),a.push(t.Code)}));var o=this.formatJson(a,this.tbData);n.e("chunk-2d0cc0b6").then(n.t.bind(null,"4bf8",7)).then((function(n){n.export_json_to_excel({header:e,data:o,filename:"".concat(t.tableTitle," ").concat(t.title),autoWidth:!0,bookType:"xlsx"})}))},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}}},"6b87":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetComponentCountByMonth=c,e.GetComponentInfoPageList=m,e.GetComponentNoProduceDetailPageList=s,e.GetComponentPageList=f,e.GetComponentProducedByDays=i,e.GetComponentProducedDay=u,e.GetFactoryComponentYield=g,e.GetFactorySchdulingPlanYield=_,e.GetFactoryTeamYield=p,e.GetFactoryTeamYieldForDay=h,e.GetInstallUnitProducedCount=r,e.GetProducedDetailPageList=l,e.GetTeamProducedCountByDate=d;var o=a(n("b775"));a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentProducedByDays",method:"post",data:t})}function r(t){return(0,o.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentNoProduceDetailPageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ProductionCount/GetProducedDetailPageList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentProducedDay",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentCountByMonth",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/ProductionCount/GetTeamProducedCountByDate",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Component/GetComponentPageList",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Component/GetComponentInfoPageList",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/ProductionCount/GetFactoryTeamYieldForDay",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ProductionCount/GetFactoryTeamYield",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/ProductionCount/GetFactoryComponentYield",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/Component/GetFactorySchdulingPlanYield",method:"post",data:t})}},"6ce5b":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{title:t.title,visible:t.dialogVisible,width:"60%","destroy-on-close":""},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[n("TopHeader",{attrs:{padding:"0"},scopedSlots:t._u([{key:"left",fn:function(){return[t._v(t._s(t.tableTitle))]},proxy:!0},{key:"right",fn:function(){return[n("el-button",{attrs:{type:"success"},on:{click:t.handleExport}},[t._v("导出")])]},proxy:!0}])}),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-z-tb-wrapper"},[n("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,data:t.tbData,config:t.tbConfig,border:"",stripe:""},scopedSlots:t._u([{key:"Unique_Code",fn:function(e){var a=e.row;return[n("span",{staticClass:"cs-pointer",on:{click:function(e){return t.openDrawer(a)}}},[t._v(t._s(a.Unique_Code))])]}}])})],1),n("unique-card",{ref:"unique",attrs:{"is-pro-history-card":t.isProHistoryCard,drawer:t.drawer},on:{"update:isProHistoryCard":function(e){t.isProHistoryCard=e},"update:is-pro-history-card":function(e){t.isProHistoryCard=e},"update:drawer":function(e){t.drawer=e}}})],1)},o=[]},"6de1":function(t,e,n){"use strict";n.r(e);var a=n("1078"),o=n("9d60");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("72cb");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"b1a78a1e",null);e["default"]=s.exports},"72cb":function(t,e,n){"use strict";n("c28e")},"812b":function(t,e,n){"use strict";n.r(e);var a=n("65a7"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},"9d60":function(t,e,n){"use strict";n.r(e);var a=n("1faa"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=o.a},b93a:function(t,e,n){"use strict";n.r(e);var a=n("37a6"),o=n("062b");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("5ff1");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"3b59e7d8",null);e["default"]=s.exports},c28e:function(t,e,n){},d169:function(t,e,n){"use strict";n.r(e);var a=n("6ce5b"),o=n("812b");for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);n("5575");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"3d4d30aa",null);e["default"]=s.exports},e0c4:function(t,e,n){},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=l,e.CheckPlanTime=u,e.DeleteInstallUnit=m,e.GetCompletePercent=C,e.GetEntity=v,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=_,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=s,e.GetInstallUnitPageList=r,e.GetProjectInstallUnitList=b,e.ImportInstallUnit=p,e.InstallUnitInfoTemplate=h,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=P;var o=a(n("b775")),i=a(n("4328"));function r(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function u(t){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function h(t){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function p(t){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:i.default.stringify(t)})}function P(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);