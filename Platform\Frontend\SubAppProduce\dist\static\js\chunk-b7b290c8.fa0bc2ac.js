(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b7b290c8"],{"041b":function(e,t,a){"use strict";a.r(t);var l=a("6f34"),n=a("dd74");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("61af");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"0de9dc27",null);t["default"]=o.exports},"0493":function(e,t,a){},"0fe4":function(e,t,a){"use strict";a.r(t);var l=a("9363"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},1428:function(e,t,a){"use strict";a.r(t);var l=a("33f0"),n=a("c391");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("e094");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,null,null);t["default"]=o.exports},"15f4":function(e,t,a){"use strict";a.r(t);var l=a("a835"),n=a("5202");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("69ae");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"640a3ce6",null);t["default"]=o.exports},2533:function(e,t,a){"use strict";a.r(t);var l=a("2ee8"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},"27a5":function(e,t,a){"use strict";a("a408")},"2e92d":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-drawer",{staticClass:"cs-drawer",attrs:{size:"80%",title:e.title,visible:e.drawer,direction:"rtl","before-close":e.handleClose},on:{"update:visible":function(t){e.drawer=t}}},[a("el-row",{staticClass:"cs-row"},[a("el-col",{attrs:{span:20}},[a("el-button",{attrs:{type:"success"},on:{click:function(t){return e.handleDialogOpen("数据表字段导入")}}},[e._v("数据表字段导入")]),a("el-button",{attrs:{type:"success"},on:{click:e.handleExcelOpen}},[e._v("Excel表字段导入")]),a("el-button",{attrs:{type:"success"},on:{click:function(t){return e.handleModelOpen("已配置字段导入")}}},[e._v("已配置字段导入")])],1),a("el-col",{attrs:{span:4}},[a("el-button",{attrs:{type:"success"},on:{click:function(t){return e.handleAdd("")}}},[e._v("新增")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],attrs:{data:e.tableData,size:"middle",height:"calc(100% - 80px)","row-key":"Id","tree-props":{children:"Children"}}},[a("el-table-column",{attrs:{align:"center",type:"index",width:"50"}}),e._l(e.tableTitle,(function(t){return a("el-table-column",{key:t.id,attrs:{width:t.width,align:"center",label:t.name},scopedSlots:e._u([{key:"default",fn:function(l){var n=l.row;return["Sort"===t.en||"Width"===t.en?a("div",[a("el-input",{staticStyle:{width:"50px",border:"1px solid #eee","border-radius":"4px"},attrs:{type:"text"},on:{input:function(t){e.inputChange(t,n.Data)},blur:e.inputBlur},model:{value:n.Data[t.en],callback:function(a){e.$set(n.Data,t.en,a)},expression:"row.Data[item.en]"}})],1):a("div",[e._v(" "+e._s(e._f("filterBoolean")(n.Data[t.en],n.Data[t.en]))+" ")])]}}],null,!0)})})),a("el-table-column",{attrs:{label:"操作",align:"center",fixed:"right",width:e.Is_Multi_Header?"300":"200"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleEdit(l.Data)}}},[e._v("编辑 ")]),a("el-button",{attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleDel(l)}}},[e._v("删除 ")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.Is_Multi_Header,expression:"Is_Multi_Header"}],attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleAdd(l.Id)}}},[e._v("新增子表头 ")])]}}])})],2),a("d-b-dialog",{ref:"dialog",on:{update:e.fetchData}}),a("import-dialog",{ref:"importDialog",on:{update:e.update}}),e.showDialog?a("add-edit-dialog",{ref:"addEditDialog",on:{handleUpdate:e.fetchData}}):e._e()],1)},n=[]},"2ee8":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2");var l=a("6186"),n={Menu_Id:"菜单",Code:"编码",Display_Name:"名称",Row_Number:"每页记录数"};t.default={props:{treeData:{type:Array,default:function(){return[]}}},data:function(){var e=function(e,t,a){0===t.length?a(new Error(n[e.field]+"必须填写")):a()};return{dialogVisible:!1,formInline:{Menu_Id:"",Code:"",Display_Name:"",Is_Select:!1,Is_Row_Number:!1,Is_Page:!1,Is_Filter:!1,Is_Summary:!1,Is_Edit:!1,Is_Auto_Width:!1,Row_Number:0,Is_Sub_Grid:!1,Sort_Column:"",Sort_Type:"",Table_Name:"",Data_Url:"",remark:"",Height:void 0,Width:void 0,Sub_Grid_Code:"",Is_Multi_Header:!1,Click_Url:""},options:[{value:!0,label:"是"},{value:!1,label:"否"}],treeDataValue:"",treeDataLabel:"",btnLoading:!1,rules:{Code:[{validator:e,required:!0}],Display_Name:[{validator:e,required:!0}],Row_Number:[{validator:e,required:!0}]}}},created:function(){},mounted:function(){},methods:{handleCancel:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1},handleOpen:function(e){var t=this;this.dialogVisible=!0,this.reset(),this.$nextTick((function(a){Object.assign(t.formInline,e)}))},handleClose:function(e){this.$refs.ruleForm.resetFields(),e()},reset:function(){this.formInline={Menu_Id:"",Code:"",Display_Name:"",Is_Select:!1,Is_Row_Number:!1,Is_Page:!1,Is_Filter:!1,Is_Summary:!1,Is_Auto_Width:!1,Is_Edit:!1,Row_Number:0,Is_Sub_Grid:!1,Sort_Column:"",Sort_Type:"",Table_Name:"",Data_Url:"",remark:"",Width:0,Height:0,Sub_Grid_Code:"",Is_Multi_Header:!1,Click_Url:""}},submitForm:function(e){var t=this;this.$refs[e].validate((function(a){if(!a)return!1;t.$delete(t.formInline,"treeDataLabel"),t.formInline.isAdd&&t.$delete(t.formInline,"isAdd"),t.btnLoading=!0,(0,l.UpdateGrid)(t.formInline).then((function(a){a.IsSucceed&&(t.$emit("updateTable",t.formInline.Parent_Id),t.$emit("getMenuList",t.$route.query.n),t.$refs[e].resetFields(),t.dialogVisible=!1,t.$message({message:"修改成功",type:"success"})),t.btnLoading=!1}))}))}}}},"2f0a":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-row",{staticClass:"h100",attrs:{type:"flex",gutter:15}},[a("el-col",{attrs:{span:5}},[a("el-card",{staticClass:"h100 cs-scroll"},[a("tree-detail",{attrs:{"show-icon":!1,"expand-all":"","expanded-key":e.expandedKey,"tree-loading":e.treeLoading,"tree-data":e.treeData,"can-node-click":!1},on:{currentNodeLabel:e.getTableTopTitle,handleNodeClick:e.handleNodeClick,handleNodeClickFalse:e.handleNodeClickFalse}})],1)],1),a("el-col",{attrs:{span:19}},[a("z-table",{directives:[{name:"show",rawName:"v-show",value:e.showTb,expression:"showTb"}],attrs:{"tb-loading":e.tableLoading,"table-top-title":e.tableTopTitle,"table-data":e.tableData,"first-id":e.firstId},on:{rowData:e.handleEdit,handleAdd:e.handleAdd,handleDel:e.handleDel,getMenuList:e.getGridList}})],1)],1),a("z-dialog",{ref:"dialog",attrs:{"tree-data":e.treeData,"row-data":e.rowData},on:{getMenuList:e.getGridList,updateTable:e.getTreeList}})],1)},n=[]},"33f0":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("vxe-grid",{ref:"xGrid",staticClass:"custom-vxe-grid",attrs:{loading:e.loading,size:e.vxeConfigComputed.size,border:e.vxeConfigComputed.border,align:e.vxeConfigComputed.align,"show-footer":e.vxeConfigComputed.showFooter,stripe:e.vxeConfigComputed.stripe,resizable:!1,"auto-resize":!0,height:"100%","export-config":{},"row-class-name":e.rowClassName,"cell-style":e.cellStyle,columns:e.replaceData,data:e.tableData},on:{"cell-click":e.cellClickEvent},scopedSlots:e._u([e.vxeConfigComputed.pageShow?{key:"pager",fn:function(){return[a("vxe-pager",{attrs:{background:"",align:e.pageConfig.right,size:e.pageConfig.size,layouts:e.pageConfig.layouts,"current-page":e.pageConfig.currentPage,"page-size":e.pageConfig.pageSize,"page-sizes":e.pageConfig.pageSizes,total:e.pageConfig.total},on:{"update:currentPage":function(t){return e.$set(e.pageConfig,"currentPage",t)},"update:current-page":function(t){return e.$set(e.pageConfig,"currentPage",t)},"update:pageSize":function(t){return e.$set(e.pageConfig,"pageSize",t)},"update:page-size":function(t){return e.$set(e.pageConfig,"pageSize",t)},"update:pageSizes":function(t){return e.$set(e.pageConfig,"pageSizes",t)},"update:page-sizes":function(t){return e.$set(e.pageConfig,"pageSizes",t)},"page-change":e.handlePageChange}})]},proxy:!0}:null],null,!0)})],1)},n=[]},"35d7":function(e,t,a){"use strict";a.r(t);var l=a("3eca"),n=a("968b");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("27a5");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"cdfc46b4",null);t["default"]=o.exports},3721:function(e,t,a){"use strict";a("95ab")},"3de3":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{"before-close":e.handleClose,visible:e.dialogVisible,title:"编辑",width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-row",[a("el-form",{ref:"ruleForm",staticClass:"form-inline",attrs:{model:e.formInline,rules:e.rules,"label-width":"120px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"菜单名称",prop:"Menu_Name"}},[a("el-input",{attrs:{disabled:"",placeholder:""},model:{value:e.formInline.Menu_Name,callback:function(t){e.$set(e.formInline,"Menu_Name",t)},expression:"formInline.Menu_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"编码",prop:"Code"}},[a("el-input",{attrs:{placeholder:"编码"},model:{value:e.formInline.Code,callback:function(t){e.$set(e.formInline,"Code",t)},expression:"formInline.Code"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{attrs:{placeholder:"名称"},model:{value:e.formInline.Display_Name,callback:function(t){e.$set(e.formInline,"Display_Name",t)},expression:"formInline.Display_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示复选框",prop:"Is_Select"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Select,callback:function(t){e.$set(e.formInline,"Is_Select",t)},expression:"formInline.Is_Select"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示行号",prop:"Is_Row_Number"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Row_Number,callback:function(t){e.$set(e.formInline,"Is_Row_Number",t)},expression:"formInline.Is_Row_Number"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示分页",prop:"Is_Page"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Page,callback:function(t){e.$set(e.formInline,"Is_Page",t)},expression:"formInline.Is_Page"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示过滤",prop:"Is_Filter"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Filter,callback:function(t){e.$set(e.formInline,"Is_Filter",t)},expression:"formInline.Is_Filter"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示汇总",prop:"Is_Summary"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Summary,callback:function(t){e.$set(e.formInline,"Is_Summary",t)},expression:"formInline.Is_Summary"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"可否编辑",prop:"Is_Edit"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Edit,callback:function(t){e.$set(e.formInline,"Is_Edit",t)},expression:"formInline.Is_Edit"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"自动列宽",prop:"Is_Auto_Width"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Auto_Width,callback:function(t){e.$set(e.formInline,"Is_Auto_Width",t)},expression:"formInline.Is_Auto_Width"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"表格高度",prop:"Height"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0,label:"表格高度"},model:{value:e.formInline.Height,callback:function(t){e.$set(e.formInline,"Height",t)},expression:"formInline.Height"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"表格宽",prop:"Width"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0,label:"表格宽"},model:{value:e.formInline.Width,callback:function(t){e.$set(e.formInline,"Width",t)},expression:"formInline.Width"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包含子列表",prop:"Is_Sub_Grid"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Sub_Grid,callback:function(t){e.$set(e.formInline,"Is_Sub_Grid",t)},expression:"formInline.Is_Sub_Grid"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"子表编码",prop:"Sub_Grid_Code"}},[a("el-input",{attrs:{placeholder:"子表编码"},model:{value:e.formInline.Sub_Grid_Code,callback:function(t){e.$set(e.formInline,"Sub_Grid_Code",t)},expression:"formInline.Sub_Grid_Code"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序字段",prop:"Sort_Column"}},[a("el-input",{attrs:{placeholder:"排序字段"},model:{value:e.formInline.Sort_Column,callback:function(t){e.$set(e.formInline,"Sort_Column",t)},expression:"formInline.Sort_Column"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序方式",prop:"Sort_Type"}},[a("el-input",{attrs:{placeholder:"排序方式"},model:{value:e.formInline.Sort_Type,callback:function(t){e.$set(e.formInline,"Sort_Type",t)},expression:"formInline.Sort_Type"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"每页记录",prop:"Row_Number"}},[a("el-input",{attrs:{placeholder:"每页记录数"},model:{value:e.formInline.Row_Number,callback:function(t){e.$set(e.formInline,"Row_Number",e._n(t))},expression:"formInline.Row_Number"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"数据库表",prop:"Table_Name"}},[a("el-input",{attrs:{placeholder:"数据库表"},model:{value:e.formInline.Table_Name,callback:function(t){e.$set(e.formInline,"Table_Name",t)},expression:"formInline.Table_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"数据地址",prop:"Data_Url"}},[a("el-input",{attrs:{placeholder:"数据地址"},model:{value:e.formInline.Data_Url,callback:function(t){e.$set(e.formInline,"Data_Url",t)},expression:"formInline.Data_Url"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"配置多级表头",prop:"Is_Multi_Header"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formInline.Is_Multi_Header,callback:function(t){e.$set(e.formInline,"Is_Multi_Header",t)},expression:"formInline.Is_Multi_Header"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"表格穿透数据url",prop:"Click_Url"}},[a("el-input",{attrs:{placeholder:"表格穿透数据url"},model:{value:e.formInline.Click_Url,callback:function(t){e.$set(e.formInline,"Click_Url",t)},expression:"formInline.Click_Url"}})],1)],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.handleCancel("ruleForm")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("确 定")])],1)],1)},n=[]},"3e10":function(e,t,a){"use strict";a.r(t);var l=a("adb68"),n=a("d77a");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("3721");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"31718c69",null);t["default"]=o.exports},"3eca":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-drawer",{staticClass:"cs-drawer",attrs:{"append-to-body":!0,"before-close":e.handleDialogClose,visible:e.dialogVisible,size:"60%",title:e.title},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dbTbLoading,expression:"dbTbLoading"}],staticClass:"db-table",attrs:{data:e.DbData,height:"100%",stripe:""},on:{"row-click":e.handleRowClick}},[a("el-table-column",{attrs:{align:"center"},scopedSlots:e._u([{key:"header",fn:function(t){return[a("div",[e._v("数据库表名")]),a("el-input",{attrs:{size:"mini",placeholder:"输入关键字搜索","suffix-icon":"el-icon-search",clearable:""},on:{change:e.handleSearchClick},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}})]}},{key:"default",fn:function(t){var l=t.row;return[a("div",[e._v(e._s(l.Table_Name))])]}}])})],1)],1),a("el-col",{attrs:{span:16}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],ref:"multipleTable",attrs:{data:e.tableData,height:"100%",stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{align:"center",label:"字段",prop:"Code"}}),a("el-table-column",{attrs:{align:"center",label:"字段名称",prop:"Display_Name"}}),a("el-table-column",{attrs:{align:"center",label:"字段类型",prop:"Type"}})],1)],1)],1),e.multipleSelection.length>0?a("div",{staticClass:"footer"},[a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1):e._e()],1)},n=[]},"4bc3":function(e,t,a){},"4ce4":function(e,t,a){"use strict";a.r(t);var l=a("3de3"),n=a("2533");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("a3ed");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"3553b448",null);t["default"]=o.exports},5202:function(e,t,a){"use strict";a.r(t);var l=a("f1f8"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},"566c":function(e,t,a){},"5b83":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticClass:"h100 cs-fill-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v(e._s(e.tableTopTitle||"全部"))]),a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],attrs:{height:"100%",data:e.tableData,size:"middle"}},[a("el-table-column",{attrs:{align:"center",type:"index",width:"50"}}),e._l(e.tableTitle,(function(t){return a("el-table-column",{key:t.id,attrs:{width:"120px",align:"center",label:t.name},scopedSlots:e._u([{key:"default",fn:function(a){var l=a.row;return[e._v(" "+e._s(e._f("filterBoolean")(l[t.en],l[t.en]))+" ")]}}],null,!0)})})),a("el-table-column",{attrs:{align:"center",fixed:"right",label:"操作",width:"350"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-button",{attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleSet(l)}}},[e._v("配置字段 ")]),a("el-button",{attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleEdit(l)}}},[e._v("编辑 ")]),a("el-button",{attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleDel(l.Id)}}},[e._v("删除 ")]),a("el-button",{attrs:{icon:"el-icon-edit-outline",plain:"",size:"mini",type:"primary"},on:{click:function(t){return e.handleShow(l)}}},[e._v("预览 ")])]}}])})],2),a("list-config",{ref:"listConfig"}),a("list-table",{ref:"listTable"})],1)},n=[]},"5c59":function(e,t,a){"use strict";a.r(t);var l=a("9edd"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},"61af":function(e,t,a){"use strict";a("4bc3")},"69ae":function(e,t,a){"use strict";a("566c")},"6f34":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticStyle:{"margin-top":"-7vh"},attrs:{title:e.dialogName,visible:e.dialogVisible,width:"50%","before-close":e.handleClose,"append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,"status-icon":"",rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"编号",prop:"Code"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Code,callback:function(t){e.$set(e.ruleForm,"Code",t)},expression:"ruleForm.Code"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"名称",prop:"Display_Name"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Display_Name,callback:function(t){e.$set(e.ruleForm,"Display_Name",t)},expression:"ruleForm.Display_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"字段类型",prop:"Type"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.ruleForm.Type,callback:function(t){e.$set(e.ruleForm,"Type",t)},expression:"ruleForm.Type"}},e._l(e.typeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"对齐方向",prop:"Align"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.Align,callback:function(t){e.$set(e.ruleForm,"Align",t)},expression:"ruleForm.Align"}},e._l(e.alignOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"格式化字符串",prop:"Formatter"}},[a("el-input",{attrs:{placeholder:"请输入内容",clearable:""},model:{value:e.ruleForm.Formatter,callback:function(t){e.$set(e.ruleForm,"Formatter",t)},expression:"ruleForm.Formatter"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否显示",prop:"Is_Display"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.Is_Display,callback:function(t){e.$set(e.ruleForm,"Is_Display",t)},expression:"ruleForm.Is_Display"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示宽度",prop:"Width"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Width,callback:function(t){e.$set(e.ruleForm,"Width",e._n(t))},expression:"ruleForm.Width"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序码",prop:"Sort"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Sort,callback:function(t){e.$set(e.ruleForm,"Sort",e._n(t))},expression:"ruleForm.Sort"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"小数位数",prop:"Digit_Number"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Digit_Number,callback:function(t){e.$set(e.ruleForm,"Digit_Number",e._n(t))},expression:"ruleForm.Digit_Number"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否冻结",prop:"Is_Frozen"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.Is_Frozen,callback:function(t){e.$set(e.ruleForm,"Is_Frozen",t)},expression:"ruleForm.Is_Frozen"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否编辑",prop:"Is_Edit"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.Is_Edit,callback:function(t){e.$set(e.ruleForm,"Is_Edit",t)},expression:"ruleForm.Is_Edit"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否必输",prop:"Is_Must_Input"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.Is_Must_Input,callback:function(t){e.$set(e.ruleForm,"Is_Must_Input",t)},expression:"ruleForm.Is_Must_Input"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否格式化",prop:"Is_Formatter"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.Is_Formatter,callback:function(t){e.$set(e.ruleForm,"Is_Formatter",t)},expression:"ruleForm.Is_Formatter"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"显示样式",prop:"Style"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Style,callback:function(t){e.$set(e.ruleForm,"Style",t)},expression:"ruleForm.Style"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"下拉框名称",prop:"Dropdown_Name"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Dropdown_Name,callback:function(t){e.$set(e.ruleForm,"Dropdown_Name",t)},expression:"ruleForm.Dropdown_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"下拉框参数",prop:"Dropdown_Parameter"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Dropdown_Parameter,callback:function(t){e.$set(e.ruleForm,"Dropdown_Parameter",t)},expression:"ruleForm.Dropdown_Parameter"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否排序",prop:"Is_Sort"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.Is_Sort,callback:function(t){e.$set(e.ruleForm,"Is_Sort",t)},expression:"ruleForm.Is_Sort"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"默认排序",prop:"Sort_Type"}},[a("el-input",{attrs:{type:"text",autocomplete:"off"},model:{value:e.ruleForm.Sort_Type,callback:function(t){e.$set(e.ruleForm,"Sort_Type",t)},expression:"ruleForm.Sort_Type"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",autocomplete:"off"},model:{value:e.ruleForm.Remark,callback:function(t){e.$set(e.ruleForm,"Remark",t)},expression:"ruleForm.Remark"}})],1)],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.handleCancel("ruleForm")}}},[e._v("取 消")]),a("el-button",{attrs:{loading:e.submitLoading,type:"primary"},on:{click:function(t){return e.handleSubmit("ruleForm")}}},[e._v("确 定")])],1)],1)},n=[]},"6fb6":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("d3b7"),a("2532"),a("159b");t.default={name:"DynamicVxeTable",props:{vxeConfig:{type:Object,default:function(){return{}}},columnsData:{type:Array,default:function(){return[]}},tableData:{type:Array,default:function(){return[]}},dataConfig:{type:Object,default:function(){return{}}},pageConfig:{type:Object,default:function(){return{}}}},data:function(){return{replaceData:[],loading:!this.vxeConfig.loading||this.vxeConfig.loading,url:this.vxeConfig.url?this.vxeConfig.url:""}},computed:{vxeConfigComputed:function(){var e={};return void 0===this.vxeConfig.size?e.size="medium":e.size=this.vxeConfig.size,void 0===this.vxeConfig.align?e.align="center":e.align=this.vxeConfig.align,void 0===this.vxeConfig.showFooter?e.showFooter=!0:e.showFooter=this.vxeConfig.showFooter,void 0===this.vxeConfig.border?e.border=!0:e.border=this.vxeConfig.border,void 0===this.vxeConfig.stripe?e.stripe=!0:e.stripe=this.vxeConfig.stripe,void 0===this.vxeConfig.pageShow?e.pageShow=!1:e.pageShow=this.vxeConfig.pageShow,e},pageConfig:function(){return Object.assign({align:"right",size:"small",layouts:["Sizes","PrevJump","PrevPage","Number","NextPage","NextJump","FullJump","Total"]},this.pageConfig)}},watch:{columnsData:{handler:function(e,t){this.columnsData=e},immediate:!0,deep:!0},tableData:{handler:function(e,t){this.tableData=e,0!=this.tableData.length&&this.roloadTableData()},deep:!0}},mounted:function(){this.replaceName(this.columnsData),this.roloadTableData()},methods:{roloadTableData:function(){var e=this;this.$refs.xGrid.reloadData(this.tableData).then((function(){e.handleMerge(),e.loading=!1}))},replaceName:function(e){var t=this;e.map((function(a){return a["title"]=a["Display_Name"],a["field"]=a["Code"],a["children"]=a["Children"],a["align"]=a["Align"],delete a["Display_Name"],delete a["Code"],delete a["Children"],delete a["Align"],a.children&&t.replaceName(a.children),t.replaceData=e,e}))},handlePageChange:function(e){var t=e.currentPage,a=e.pageSize;this.pageConfig.currentPage=t,this.pageConfig.pageSize=a;var l={Page:t,pageSize:a};this.$emit("changePageFn",l)},rowClassName:function(e){var t=e.row,a=e.rowIndex;if(this.dataConfig.rowBgColor&&"{}"!=JSON.stringify(this.dataConfig.rowBgColor)){var l=this.dataConfig.rowBgColor;for(var n in l)if(l[n].includes(a))return"row-".concat(n)}if(this.url&&""!=this.url&&""!=t.Click_Param)return"row-pointer"},cellStyle:function(e){e.row,e.column;var t=e.rowIndex,a=(e._columnIndex,{textAlign:"left"});if(this.dataConfig.rowAlignLeft&&this.dataConfig.rowAlignLeft.includes(t))return a},cellClickEvent:function(e){var t=e.row;e.column,e._rowIndex;this.url&&""!=this.url&&""!=t.Click_Param&&window.open("".concat(this.url,"?Click_Param=").concat(t.Click_Param))},handleMerge:function(){var e=this.$refs.xGrid,t=e.getTableData(),a=t.fullData,l=t.visibleData,n=e.getTableColumn(),r=n.fullColumn,i=r.filter((function(e){return 1==e.visible}));if(0!=a.length){var o=[],s=this.dataConfig.mergeCol?this.dataConfig.mergeCol:[];s&&s.length>0&&a.forEach((function(e,t){i.forEach((function(a,n){var r=e[a.property];if(r&&s.includes(a.property)){var i=l[t-1],u=l[t+1];if(i&&i[a.property]===r)o.push({row:t,col:n,rowspan:0,colspan:0});else{var d=1;while(u&&u[a.property]===r)u=l[++d+t];d>1&&o.push({row:t,col:n,rowspan:d,colspan:1})}}}))}));var u=this.dataConfig.mergeRow?this.dataConfig.mergeRow:[];u&&u.length>0&&a.forEach((function(e,t){u.includes(t)&&o.push({row:t,col:0,rowspan:1,colspan:r.length})})),o=o.concat(this.dataConfig.rowMerge?this.dataConfig.rowMerge:[],this.dataConfig.colMerge?this.dataConfig.colMerge:[]),e.setMergeCells(o).then((function(){}))}}}}},"842f":function(e,t,a){},9363:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=l(a("da8a")),r=l(a("3e10"));t.default={components:{ListConfig:n.default,ListTable:r.default},props:{tableTopTitle:{type:String,default:""},tbLoading:{type:Boolean,default:!1},tableData:{type:Array,default:function(){return[]}},firstId:{type:String,default:""}},data:function(){return{pages:{title:0,page:"1",pageSize:"10"},tableTitle:[{name:"菜单名称",id:1,en:"Menu_Name"},{name:"列表名称",id:999,en:"Display_Name"},{name:"列表编码",id:2,en:"Code"},{name:"显示复选框",id:3,en:"Is_Select"},{name:"显示行号",id:4,en:"Is_Row_Number"},{name:"显示分页",id:5,en:"Is_Page"},{name:"显示过滤",id:6,en:"Is_Filter"},{name:"显示汇总",id:7,en:"Is_Summary"},{name:"可否编辑",id:8,en:"Is_Edit"},{name:"排序字段",id:9,en:"Sort_Column"},{name:"排序方式",id:10,en:"Sort_Type"},{name:"每页记录数",id:11,en:"Row_Number"},{name:"所属公司",id:12,en:"Company_Name"}]}},watch:{firstId:function(e){this.getTableList(e)}},methods:{getTableList:function(e){this.$emit("getMenuList",this.$route.query.n||e)},handleEdit:function(e){this.$emit("rowData",e)},handleAdd:function(){this.$emit("handleAdd")},handleSet:function(e){this.$refs.listConfig.handleOpen(e)},handleDel:function(e){this.$emit("handleDel",e)},handleShow:function(e){this.$refs.listTable.handleOpen(e)}}}},"95ab":function(e,t,a){},"968b":function(e,t,a){"use strict";a.r(t);var l=a("c3fd"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},9721:function(e,t,a){},"9dc2":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2");var l=a("6186"),n={Code:"编号",Display_Name:"名称",Width:"宽度",Digit_Number:"小数位数"};t.default={data:function(){var e=function(e,t,a){0===t.length?a(new Error(n[e.field]+"必须填写")):a()};return{dialogVisible:!1,ruleForm:{Grid_Id:"",Code:"",Display_Name:"",Type:"",Align:"center",Range:"",Width:120,Sort:0,Proportion:1,Digit_Number:0,Dropdown_Parameter:"",Dropdown_Name:"",Default_Value:"",Sort_Type:"",Filter_Type:"",Is_Display:!0,Is_Filter:!1,Is_Frozen:!1,Is_Primary_Ke:!0,Is_Must_Input:!0,Is_Edit:!0,Is_Sort:!0,Is_Formatter:!1,Formatter:"",Style:"",Remark:"",Parent_Id:""},submitLoading:!1,dialogName:"添加",typeOptions:[{label:"字符型",value:"text"},{label:"数值型",value:"number"},{label:"布尔型",value:"boolean"},{label:"日期型",value:"date"},{label:"日期范围",value:"daterange"},{label:"时间型",value:"time"}],selectTypeOption:[{label:"字符型",value:"text"},{label:"单选型",value:"radio"},{label:"多选型",value:"multi"},{label:"数值型",value:"number"},{label:"数值范围",value:"numberrange"},{label:"布尔型",value:"boolean"},{label:"日期型",value:"date"},{label:"时间型",value:"time"},{label:"日期范围",value:"daterange"},{label:"时间范围",value:"timerange"},{label:"滑块型",value:"slider"},{label:"开关型",value:"switch"},{label:"颜色型",value:"color"}],alignOptions:[{label:"靠左",value:"left"},{label:"居中",value:"center"},{label:"靠右",value:"right"}],options:[{label:"是",value:!0},{label:"否",value:!1}],rules:{Code:[{validator:e,required:!0}],Display_Name:[{validator:e,required:!0}],Width:[{validator:e,required:!0}],Proportion:[{required:!0,message:"所占列数不能为空"},{type:"number",message:"必须为数字值"}],Digit_Number:[{validator:e,required:!0}],Sort:[{required:!0,message:"排序不能为空"},{type:"number",message:"排序必须为数字值"}]}}},methods:{handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){e&&(t.submitLoading=!0,(0,l.SaveGridColumn)(t.ruleForm).then((function(e){e.IsSucceed?(t.dialogVisible=!1,t.$emit("handleUpdate"),t.$message({message:"新增"===t.dialogName?"保存成功":"修改成功",type:"success"})):t.$message({message:e.Message,type:"error"}),t.submitLoading=!1})))}))},handleCancel:function(e){this.$refs[e].resetFields(),this.submitLoading=!1,this.dialogVisible=!1},handleOpen:function(e,t,a){var l=this;this.dialogVisible=!0,"add"===e?(this.$delete(this.ruleForm,"Id"),this.dialogName="新增",this.ruleForm.Grid_Id=t,""!==a&&(this.ruleForm.Parent_Id=a)):(this.dialogName="编辑",this.$nextTick((function(e){l.$refs.ruleForm.resetFields(),Object.assign(l.ruleForm,t)})))},handleClose:function(e){this.$refs.ruleForm.resetFields(),this.submitLoading=!1,e()}}}},"9edd":function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=l(a("35d7")),r=l(a("041b")),i=l(a("15f4")),o=a("6186");t.default={components:{DBDialog:n.default,AddEditDialog:r.default,ImportDialog:i.default},data:function(){return{drawer:!1,dialogVisible:!1,tbLoading:!1,showDialog:!1,Is_Multi_Header:!1,title:"",rowId:"",tableData:[],tableTitle:[{name:"编号",id:1,width:"120px",en:"Code"},{name:"名称",id:2,width:"120px",en:"Display_Name"},{name:"是否显示",id:7,en:"Is_Display"},{name:"排序码",id:6,en:"Sort"},{name:"字段类型",id:3,en:"Type"},{name:"显示宽度",id:5,en:"Width"},{name:"对齐方向",id:4,en:"Align"},{name:"是否主键",id:8,en:"Is_Primary_Key"},{name:"是否必输",id:9,en:"Is_Must_Input"},{name:"是否可编辑",id:10,width:"120px",en:"Is_Edit"},{name:"是否格式化",width:"120px",id:12,en:"Is_Formatter"},{name:"显示样式",width:"240px",id:13,en:"Style"},{name:"是否冻结",id:14,en:"Is_Frozen"},{name:"下拉框名称",id:15,width:"120px",en:"Dropdown_Name"},{name:"下拉框参数",width:"120px",id:16,en:"Dropdown_Parameter"},{name:"默认排序",id:17,en:"Sort_Type"},{name:"小数位数",id:18,en:"Digit_Number"},{name:"备注",id:20,width:"170px",en:"Remark"}],ruleForm:{Grid_Id:"",Code:"",Display_Name:"",Type:"",Align:"center",Range:"",Width:120,Sort:0,Proportion:1,Digit_Number:0,Dropdown_Parameter:"",Dropdown_Name:"",Default_Value:"",Sort_Type:"",Filter_Type:"",Is_Display:!0,Is_Filter:!1,Is_Frozen:!1,Is_Primary_Ke:!0,Is_Must_Input:!0,Is_Edit:!0,Is_Sort:!0,Is_Formatter:!1,Formatter:"",Style:"",Remark:"",Parent_Id:""}}},mounted:function(){},methods:{inputChange:function(e,t){Object.assign(this.ruleForm,t)},inputBlur:function(e){var t=this;(0,o.SaveGridColumn)(this.ruleForm).then((function(e){e.IsSucceed?t.$message({message:"修改成功",type:"success"}):t.$message({message:e.Message,type:"error"})}))},fetchData:function(){var e=this;this.showDialog=!1,this.tbLoading=!0,(0,o.GetColumnTreeList)({gridId:this.rowId}).then((function(t){e.tableData=t.Data,e.tbLoading=!1}))},handleAdd:function(e){var t=this;this.showDialog=!0,this.$nextTick((function(a){t.$refs.addEditDialog.handleOpen("add",t.rowId,e)}))},handleEdit:function(e){var t=this;this.showDialog=!0,this.$nextTick((function(a){t.$refs.addEditDialog.handleOpen("edit",e)}))},handleDel:function(e){var t=this,a=0!==e.Children.length?"该行存在子数据，确定删除该行?":"确定删除该行?";this.$confirm(a,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.DeleteColumn)({Ids:[e.Id]}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))}))},handleClose:function(e){e()},handleDialogOpen:function(e){this.$refs.dialog.handleDialogOpen(this.rowId,e)},handleExcelOpen:function(){this.$refs.importDialog.open(this.rowId)},handleModelOpen:function(e){this.$refs.dialog.handleDialogOpen(this.rowId,e)},update:function(){this.fetchData()},handleOpen:function(e){this.drawer=!0;var t=e.Id,a=e.Display_Name,l=e.Is_Multi_Header;this.title=a,this.rowId=t,this.Is_Multi_Header=l,this.fetchData()}}}},a3ed:function(e,t,a){"use strict";a("af00")},a408:function(e,t,a){},a835:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"导入",visible:e.dialogVisible,width:"280px","custom-class":"dialogCustomClass","before-close":e.handleClose,"append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-button",{attrs:{type:"success",size:"medium"},on:{click:e.download}},[e._v("模板下载")])],1),a("el-col",{attrs:{span:12}},[a("upload",{ref:"upload",staticClass:"z-upload",attrs:{limits:1,"btn-size":"medium","btn-type":"primary","btn-label":"直接导入","before-upload":e.handleImport}})],1)],1)],1)},n=[]},adb68:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return n}));var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-drawer",{staticClass:"cs-drawer",attrs:{size:"80%",title:e.title,visible:e.drawer,direction:"rtl","before-close":e.handleClose},on:{"update:visible":function(t){e.drawer=t}}},[e.columnsData?a("div",[a("dynamic-vxe-table",{attrs:{vxeConfig:e.vxeConfig,columnsData:e.columnsData}})],1):e._e()])},n=[]},af00:function(e,t,a){},af38:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("b0c0");var n=l(a("c437")),r=l(a("1463")),i=l(a("4ce4")),o=a("f382"),s=a("6186");l(a("4360")),t.default={name:"SysListConfig",components:{zTable:n.default,TreeDetail:r.default,zDialog:i.default},data:function(){return{treeLoading:!0,tableLoading:!1,tableTopTitle:"",tableData:[],treeData:[],rowData:{},expandedKey:"",firstId:"",showTb:!0,node:""}},created:function(){this.testFun()},mounted:function(){this.getTreeList()},methods:{testFun:function(){},getTreeList:function(){var e=this;this.treeLoading=!0,(0,s.GetTreeMenus)().then((function(t){try{var a=(0,o.findFirstNode)(t.Data.length>0&&t.Data[0]);e.firstId=a.Id||"",e.node=a}catch(l){return void e.$message({message:"数据错误",type:"error"})}!e.$route.query.n&&e.$router.push({name:"SysListConfig",query:{n:e.firstId}}),e.expandedKey=e.$route.query.n||e.firstId,e.treeData=t.Data&&t.Data[0].Children,e.treeLoading=!1}))},getGridList:function(e){var t=this;this.tableLoading=!0,(0,s.GetGridList)({meunId:e}).then((function(e){t.tableData=e.Data,t.tableLoading=!1}))},getTableTopTitle:function(e){this.tableTopTitle=e},handleNodeClick:function(e){this.showTb=!0,this.node=e,this.tableTopTitle=e.Label,this.$router.push({name:"SysListConfig",query:{n:e.Id}}),this.getGridList(e.Id)},handleEdit:function(e){this.$refs.dialog.handleOpen(e)},handleAdd:function(){this.$refs.dialog.handleOpen({isAdd:!0,Menu_Id:this.node.Id,Menu_Name:this.node.Label})},handleNodeClickFalse:function(){this.showTb=!1},handleDel:function(e){var t=this;this.$confirm("确定是否删除该行?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.DeleteGrid)({Id:e}).then((function(e){e.IsSucceed&&(t.$message({type:"success",message:"删除成功"}),t.getGridList(t.node.Id))}))}))}}}},b53f:function(e,t,a){"use strict";a("9721")},c177:function(e,t,a){"use strict";a.r(t);var l=a("2f0a"),n=a("ede5");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("fa9c");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"25b04274",null);t["default"]=o.exports},c1b1:function(e,t,a){"use strict";a("0493")},c391:function(e,t,a){"use strict";a.r(t);var l=a("6fb6"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},c3fd:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("d3b7");var l=a("6186");t.default={data:function(){return{dialogVisible:!1,dbTbLoading:!1,tbLoading:!1,btnLoading:!1,grideId:"",DbData:[],DbDataCopy:[],tableData:[],multipleSelection:[],title:"",filterText:""}},methods:{fetchData:function(){var e=this;this.tbLoading=!0,"数据表字段导入"===this.title?(0,l.GetTableList)().then((function(t){e.DbData=t.Data,e.DbDataCopy=t.Data,e.tbLoading=!1})):(0,l.GetModelList)().then((function(t){e.DbData=t.Data,e.DbDataCopy=t.Data,e.tbLoading=!1}))},fetchDataDetail:function(e){var t=this;this.dbTbLoading=!0,"数据表字段导入"===this.title?(0,l.GetColumnFromTable)({table_name:e.Table_Name}).then((function(e){t.tableData=e.Data,t.dbTbLoading=!1})):(0,l.GetColumnFromModel)({region:e.Model_Region,table_name:e.Table_Name}).then((function(e){t.tableData=e.Data,t.dbTbLoading=!1}))},handleSubmit:function(){var e=this;this.btnLoading=!0,(0,l.SaveGridColumnFromTable)({grid_id:this.grideId,sys_Column:this.multipleSelection}).then((function(t){t.IsSucceed&&(e.$refs.multipleTable.clearSelection(),e.$emit("update"),e.$message({message:"添加成功",type:"success"})),e.btnLoading=!1}))},handleSelectionChange:function(e){this.multipleSelection=e},handleRowClick:function(e){this.fetchDataDetail(e)},handleDialogOpen:function(e,t){this.grideId=e,this.dialogVisible=!0,this.title=t,this.fetchData(t)},handleDialogClose:function(e){this.$refs.multipleTable.clearSelection(),e()},handleSearchClick:function(){var e=this;this.DbData=this.DbDataCopy.filter((function(t){if(-1!=t.Table_Name.indexOf(e.filterText))return t.Table_Name}))}}}},c437:function(e,t,a){"use strict";a.r(t);var l=a("5b83"),n=a("0fe4");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("b53f");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"6865dbc4",null);t["default"]=o.exports},c8a3:function(e,t,a){},d77a:function(e,t,a){"use strict";a.r(t);var l=a("f320"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},da8a:function(e,t,a){"use strict";a.r(t);var l=a("2e92d"),n=a("5c59");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("c1b1");var i=a("2877"),o=Object(i["a"])(n["default"],l["a"],l["b"],!1,null,"28eac4a0",null);t["default"]=o.exports},dd74:function(e,t,a){"use strict";a.r(t);var l=a("9dc2"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},e094:function(e,t,a){"use strict";a("c8a3")},e41b:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=s,t.GetPartsImportTemplate=d,t.GetPartsList=o,t.GetProjectAreaTreeList=r,t.ImportParts=u,t.SaveProjectAreaSort=i;var n=l(a("b775"));function r(e){return(0,n.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function o(e){return(0,n.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},ede5:function(e,t,a){"use strict";a.r(t);var l=a("af38"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(r);t["default"]=n.a},f1f8:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=l(a("c7f0")),r=a("6186"),i=a("ed08");t.default={components:{Upload:n.default},data:function(){return{dialogVisible:!1,TypeId:""}},methods:{handleClose:function(e){this.dialogVisible=!1},open:function(e){this.dialogVisible=!0,this.TypeId=e},handleImport:function(e){var t=this,a=new FormData;a.append("files",e),a.append("grid_id",this.TypeId),(0,r.ImportGridColumn)(a).then((function(e){e.IsSucceed?e.Data?(t.$message({message:e.Message,type:"success"}),t.$emit("update")):t.$message({message:e.Data.Message,type:"error"}):t.$message({message:e.Message,type:"error"}),t.handleClose()}))},download:function(){var e=this;(0,r.GridColumnemplate)({}).then((function(t){t.IsSucceed?window.open((0,i.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({type:"error",message:t.Message})}))}}}},f320:function(e,t,a){"use strict";var l=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("6186"),r=l(a("1428"));t.default={components:{DynamicVxeTable:r.default},data:function(){return{drawer:!1,title:"",tableData:[],Code:"",vxeConfig:{loading:!0,size:"small",border:!0,align:"center",showFooter:!1,url:"https://www.bimtk.com/",pageShow:!1},columnsData:null}},mounted:function(){},methods:{fetchData:function(){var e=this;(0,n.GetVXEGridByCode)({code:this.Code}).then((function(t){e.columnsData=t.Data}))},handleClose:function(e){this.columnsData=null,e()},update:function(){this.fetchData()},handleOpen:function(e){this.drawer=!0;var t=e.Display_Name,a=e.Code;this.title=t,this.Code=a,this.fetchData()}}}},f382:function(e,t,a){"use strict";function l(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=l(e.Children)),!0)}))}function n(e){e.map((function(e){if(e.Is_Directory||!e.Children)return n(e.Children);delete e.Children}))}function r(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function l(e,t,n){for(var r=0;r<e.length;r++){var i=e[r];if(i.Id===t)return a&&n.push(i),n;if(i.Children&&i.Children.length){if(n.push(i),l(i.Children,t,n).length)return n;n.pop()}}return[]}return l(e,t,[])}function i(e){return e.Children&&e.Children.length>0?i(e.Children[0]):e}function o(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&o(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=n,t.disableDirectory=o,t.findAllParentNode=r,t.findFirstNode=i,t.getDirectoryTree=l,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7")},fa9c:function(e,t,a){"use strict";a("842f")}}]);