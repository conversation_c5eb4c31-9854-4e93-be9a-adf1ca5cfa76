(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-1ee5b71e"],{"1ac6":function(e,t,n){},26448:function(e,t,n){"use strict";n("1ac6")},"2e8a":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=c,t.GetCompTypeTree=f,t.GetComponentTypeEntity=u,t.GetComponentTypeList=i,t.GetFactoryCompTypeIndentifySetting=h,t.GetTableSettingList=p,t.GetTypePageList=s,t.RestoreTemplateType=b,t.SavDeepenTemplateSetting=g,t.SaveCompTypeIdentifySetting=y,t.SaveComponentType=l,t.SaveProBimComponentType=d,t.UpdateColumnSetting=v,t.UpdateComponentPartTableSetting=m;var r=a(n("b775")),o=a(n("4328"));function i(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:o.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:o.default.stringify(e)})}function f(e){return(0,r.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function h(e){return(0,r.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function y(e){return(0,r.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function g(e){return(0,r.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function b(e){return(0,r.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"60cd":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"page-container"},[n("el-button",{staticStyle:{"margin-bottom":"16px"},on:{click:e.backPage}},[e._v("返回")]),n("div",{staticClass:"top-wrapper"},[n("div",{staticClass:"info"},[e.majorName?[n("div",{staticClass:"title"},[e._v("当前专业：")]),n("div",{staticClass:"value"},[e._v(e._s(e.majorName))])]:e._e(),e.unit?[n("div",{staticClass:"title"},[e._v("统计单位：")]),n("div",{staticClass:"value"},[e._v(e._s(e.unit))])]:e._e(),e.steelUnit?[n("div",{staticClass:"title"},[e._v("构件单位：")]),n("div",{staticClass:"value"},[e._v(e._s(e.steelUnit))])]:e._e(),[n("div",{staticClass:"title"},[e._v("单位统计字段：")]),e._v(" "+e._s(e.unitInfo)+" ")]],2),n("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},e._l(e.tabList,(function(e,t){return n("el-tab-pane",{key:t,attrs:{label:e.label,name:e.value}})})),1)],1),n("div",{staticClass:"cs-content-wrapper"},[n("div",{staticClass:"content-top"},[n("span",{staticClass:"content-title"},[e._v("系统字段")]),n("div",{staticClass:"content-top-right"},[n("label",{staticClass:"cs-label"},[n("span",[e._v("字段名称：")]),n("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchValue,callback:function(t){e.searchValue=t},expression:"searchValue"}})],1),n("div",[n("el-button",{attrs:{type:"primary"},on:{click:e.filterList}},[e._v("查询")]),n("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:e.save}},[e._v("保存设置")]),n("el-button",{attrs:{type:"success",loading:e.restoreLoading1},on:{click:function(t){return e.restore(1)}}},[e._v("恢复默认二级清单")]),n("el-button",{attrs:{type:"success",loading:e.restoreLoading2},on:{click:function(t){return e.restore(2)}}},[e._v("恢复默认三级清单")])],1)])]),n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},e._l(e.list,(function(t){return n("el-row",{key:t.uuid},[n("el-col",{attrs:{span:7}},[n("el-form-item",{attrs:{label:"字段名："}},[n("el-input",{class:["w100",{showRed:t.showRed}],attrs:{clearble:""},model:{value:t.Display_Name,callback:function(n){e.$set(t,"Display_Name",n)},expression:"item.Display_Name"}})],1)],1),n("el-col",{attrs:{span:7}},[n("el-form-item",{attrs:{label:"备注说明："}},[n("el-input",{staticClass:"w100",attrs:{clearble:""},model:{value:t.Remark,callback:function(n){e.$set(t,"Remark",n)},expression:"item.Remark"}})],1)],1),n("el-col",{attrs:{span:7}},[n("el-form-item",{attrs:{label:"排序："}},[n("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{min:0,clearble:""},model:{value:t.Sort,callback:function(n){e.$set(t,"Sort",e._n(n))},expression:"item.Sort"}})],1)],1),t.showRed?e._e():n("el-col",{attrs:{span:3}},[n("el-form-item",{attrs:{label:"是否启用："}},[n("el-switch",{model:{value:t.Is_Enabled,callback:function(n){e.$set(t,"Is_Enabled",n)},expression:"item.Is_Enabled"}})],1)],1)],1)})),1)],1)],1)},r=[]},"868b":function(e,t,n){"use strict";n.r(t);var a=n("60cd"),r=n("fd90");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("26448");var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"6b5b86ae",null);t["default"]=s.exports},e144:function(e,t,n){"use strict";n.r(t),n.d(t,"v1",(function(){return c})),n.d(t,"v3",(function(){return U})),n.d(t,"v4",(function(){return $["a"]})),n.d(t,"v5",(function(){return E})),n.d(t,"NIL",(function(){return M})),n.d(t,"version",(function(){return q})),n.d(t,"validate",(function(){return f["a"]})),n.d(t,"stringify",(function(){return i["a"]})),n.d(t,"parse",(function(){return p}));var a,r,o=n("d8f8"),i=n("58cf"),s=0,l=0;function u(e,t,n){var u=t&&n||0,c=t||new Array(16);e=e||{};var f=e.node||a,d=void 0!==e.clockseq?e.clockseq:r;if(null==f||null==d){var p=e.random||(e.rng||o["a"])();null==f&&(f=a=[1|p[0],p[1],p[2],p[3],p[4],p[5]]),null==d&&(d=r=16383&(p[6]<<8|p[7]))}var m=void 0!==e.msecs?e.msecs:Date.now(),v=void 0!==e.nsecs?e.nsecs:l+1,h=m-s+(v-l)/1e4;if(h<0&&void 0===e.clockseq&&(d=d+1&16383),(h<0||m>s)&&void 0===e.nsecs&&(v=0),v>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");s=m,l=v,r=d,m+=122192928e5;var y=(1e4*(268435455&m)+v)%4294967296;c[u++]=y>>>24&255,c[u++]=y>>>16&255,c[u++]=y>>>8&255,c[u++]=255&y;var g=m/4294967296*1e4&268435455;c[u++]=g>>>8&255,c[u++]=255&g,c[u++]=g>>>24&15|16,c[u++]=g>>>16&255,c[u++]=d>>>8|128,c[u++]=255&d;for(var b=0;b<6;++b)c[u+b]=f[b];return t||Object(i["a"])(c)}var c=u,f=n("06e4");function d(e){if(!Object(f["a"])(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}var p=d;function m(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}var v="6ba7b810-9dad-11d1-80b4-00c04fd430c8",h="6ba7b811-9dad-11d1-80b4-00c04fd430c8",y=function(e,t,n){function a(e,a,r,o){if("string"===typeof e&&(e=m(e)),"string"===typeof a&&(a=p(a)),16!==a.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var s=new Uint8Array(16+e.length);if(s.set(a),s.set(e,a.length),s=n(s),s[6]=15&s[6]|t,s[8]=63&s[8]|128,r){o=o||0;for(var l=0;l<16;++l)r[o+l]=s[l];return r}return Object(i["a"])(s)}try{a.name=e}catch(r){}return a.DNS=v,a.URL=h,a};function g(e){if("string"===typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return b(T(_(e),8*e.length))}function b(e){for(var t=[],n=32*e.length,a="0123456789abcdef",r=0;r<n;r+=8){var o=e[r>>5]>>>r%32&255,i=parseInt(a.charAt(o>>>4&15)+a.charAt(15&o),16);t.push(i)}return t}function C(e){return 14+(e+64>>>9<<4)+1}function T(e,t){e[t>>5]|=128<<t%32,e[C(t)-1]=t;for(var n=1732584193,a=-271733879,r=-1732584194,o=271733878,i=0;i<e.length;i+=16){var s=n,l=a,u=r,c=o;n=I(n,a,r,o,e[i],7,-680876936),o=I(o,n,a,r,e[i+1],12,-389564586),r=I(r,o,n,a,e[i+2],17,606105819),a=I(a,r,o,n,e[i+3],22,-1044525330),n=I(n,a,r,o,e[i+4],7,-176418897),o=I(o,n,a,r,e[i+5],12,1200080426),r=I(r,o,n,a,e[i+6],17,-1473231341),a=I(a,r,o,n,e[i+7],22,-45705983),n=I(n,a,r,o,e[i+8],7,1770035416),o=I(o,n,a,r,e[i+9],12,-1958414417),r=I(r,o,n,a,e[i+10],17,-42063),a=I(a,r,o,n,e[i+11],22,-1990404162),n=I(n,a,r,o,e[i+12],7,1804603682),o=I(o,n,a,r,e[i+13],12,-40341101),r=I(r,o,n,a,e[i+14],17,-1502002290),a=I(a,r,o,n,e[i+15],22,1236535329),n=P(n,a,r,o,e[i+1],5,-165796510),o=P(o,n,a,r,e[i+6],9,-1069501632),r=P(r,o,n,a,e[i+11],14,643717713),a=P(a,r,o,n,e[i],20,-373897302),n=P(n,a,r,o,e[i+5],5,-701558691),o=P(o,n,a,r,e[i+10],9,38016083),r=P(r,o,n,a,e[i+15],14,-660478335),a=P(a,r,o,n,e[i+4],20,-405537848),n=P(n,a,r,o,e[i+9],5,568446438),o=P(o,n,a,r,e[i+14],9,-1019803690),r=P(r,o,n,a,e[i+3],14,-187363961),a=P(a,r,o,n,e[i+8],20,1163531501),n=P(n,a,r,o,e[i+13],5,-1444681467),o=P(o,n,a,r,e[i+2],9,-51403784),r=P(r,o,n,a,e[i+7],14,1735328473),a=P(a,r,o,n,e[i+12],20,-1926607734),n=k(n,a,r,o,e[i+5],4,-378558),o=k(o,n,a,r,e[i+8],11,-2022574463),r=k(r,o,n,a,e[i+11],16,1839030562),a=k(a,r,o,n,e[i+14],23,-35309556),n=k(n,a,r,o,e[i+1],4,-1530992060),o=k(o,n,a,r,e[i+4],11,1272893353),r=k(r,o,n,a,e[i+7],16,-155497632),a=k(a,r,o,n,e[i+10],23,-1094730640),n=k(n,a,r,o,e[i+13],4,681279174),o=k(o,n,a,r,e[i],11,-358537222),r=k(r,o,n,a,e[i+3],16,-722521979),a=k(a,r,o,n,e[i+6],23,76029189),n=k(n,a,r,o,e[i+9],4,-640364487),o=k(o,n,a,r,e[i+12],11,-421815835),r=k(r,o,n,a,e[i+15],16,530742520),a=k(a,r,o,n,e[i+2],23,-995338651),n=D(n,a,r,o,e[i],6,-198630844),o=D(o,n,a,r,e[i+7],10,1126891415),r=D(r,o,n,a,e[i+14],15,-1416354905),a=D(a,r,o,n,e[i+5],21,-57434055),n=D(n,a,r,o,e[i+12],6,1700485571),o=D(o,n,a,r,e[i+3],10,-1894986606),r=D(r,o,n,a,e[i+10],15,-1051523),a=D(a,r,o,n,e[i+1],21,-2054922799),n=D(n,a,r,o,e[i+8],6,1873313359),o=D(o,n,a,r,e[i+15],10,-30611744),r=D(r,o,n,a,e[i+6],15,-1560198380),a=D(a,r,o,n,e[i+13],21,1309151649),n=D(n,a,r,o,e[i+4],6,-145523070),o=D(o,n,a,r,e[i+11],10,-1120210379),r=D(r,o,n,a,e[i+2],15,718787259),a=D(a,r,o,n,e[i+9],21,-343485551),n=S(n,s),a=S(a,l),r=S(r,u),o=S(o,c)}return[n,a,r,o]}function _(e){if(0===e.length)return[];for(var t=8*e.length,n=new Uint32Array(C(t)),a=0;a<t;a+=8)n[a>>5]|=(255&e[a/8])<<a%32;return n}function S(e,t){var n=(65535&e)+(65535&t),a=(e>>16)+(t>>16)+(n>>16);return a<<16|65535&n}function w(e,t){return e<<t|e>>>32-t}function L(e,t,n,a,r,o){return S(w(S(S(t,e),S(a,o)),r),n)}function I(e,t,n,a,r,o,i){return L(t&n|~t&a,e,t,r,o,i)}function P(e,t,n,a,r,o,i){return L(t&a|n&~a,e,t,r,o,i)}function k(e,t,n,a,r,o,i){return L(t^n^a,e,t,r,o,i)}function D(e,t,n,a,r,o,i){return L(n^(t|~a),e,t,r,o,i)}var N=g,R=y("v3",48,N),U=R,$=n("ec26");function O(e,t,n,a){switch(e){case 0:return t&n^~t&a;case 1:return t^n^a;case 2:return t&n^t&a^n&a;case 3:return t^n^a}}function A(e,t){return e<<t|e>>>32-t}function x(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"===typeof e){var a=unescape(encodeURIComponent(e));e=[];for(var r=0;r<a.length;++r)e.push(a.charCodeAt(r))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var o=e.length/4+2,i=Math.ceil(o/16),s=new Array(i),l=0;l<i;++l){for(var u=new Uint32Array(16),c=0;c<16;++c)u[c]=e[64*l+4*c]<<24|e[64*l+4*c+1]<<16|e[64*l+4*c+2]<<8|e[64*l+4*c+3];s[l]=u}s[i-1][14]=8*(e.length-1)/Math.pow(2,32),s[i-1][14]=Math.floor(s[i-1][14]),s[i-1][15]=8*(e.length-1)&4294967295;for(var f=0;f<i;++f){for(var d=new Uint32Array(80),p=0;p<16;++p)d[p]=s[f][p];for(var m=16;m<80;++m)d[m]=A(d[m-3]^d[m-8]^d[m-14]^d[m-16],1);for(var v=n[0],h=n[1],y=n[2],g=n[3],b=n[4],C=0;C<80;++C){var T=Math.floor(C/20),_=A(v,5)+O(T,h,y,g)+b+t[T]+d[C]>>>0;b=g,g=y,y=A(h,30)>>>0,h=v,v=_}n[0]=n[0]+v>>>0,n[1]=n[1]+h>>>0,n[2]=n[2]+y>>>0,n[3]=n[3]+g>>>0,n[4]=n[4]+b>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]}var G=x,j=y("v5",80,G),E=j,M="00000000-0000-0000-0000-000000000000";function V(e){if(!Object(f["a"])(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}var q=V},fd90:function(e,t,n){"use strict";n.r(t);var a=n("ff52c"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},ff52c:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("caad"),n("a15b"),n("d81d"),n("b0c0"),n("e9f5"),n("910d"),n("ab43"),n("a732"),n("d3b7"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("498a"),n("ddb0");var a=n("ed08"),r=n("2e8a"),o=n("e144");t.default={name:"SYSUnitPartTemp",data:function(){return{activeName:"pz",tabList:[{label:"深化清单导入配置",value:"pz"}],searchValue:"",majorName:"",unit:"",steelUnit:"",templateListNew:[],list:[],form:{},pgLoading:!1,saveLoading:!1,restoreLoading1:!1,restoreLoading2:!1,unitInfo:""}},mounted:function(){this.majorName=this.$route.query.name||"",this.unit=this.$route.query.unit||"",this.steelUnit=this.$route.query.steel_unit||"",this.fetchData()},methods:{backPage:function(){(0,a.closeTagView)(this.$store,this.$route)},fetchData:function(){var e=this;this.pgLoading=!0,(0,r.GetTableSettingList)({ProfessionalCode:"Steel"}).then((function(t){if(t.IsSucceed){var n="";e.defaultList=t.Data.map((function(e){return e.uuid=(0,o.v4)(),"SteelAmount"===e.Code&&(n+="".concat(e.Display_Name||"","*")),"SteelWeight"===e.Code&&(n+="".concat(e.Display_Name||"")),e.showRed=0===e.Column_Type,e})),e.list=(0,a.deepClone)(e.defaultList),e.unitInfo=n}else e.$message({message:t.Message,type:"error"});e.pgLoading=!1}))},filterList:function(){var e=this;this.searchValue?this.list=this.defaultList.filter((function(t){return t.Display_Name.includes(e.searchValue)})):this.list=(0,a.deepClone)(this.defaultList)},save:function(){var e=this,t=this.list.some((function(e){return!e.Display_Name||""===e.Display_Name.trim()}));if(t)this.$message.error("字段名不能为空");else{var n=new Set,a=this.list.filter((function(e){return!!n.has(e.Display_Name)||(n.add(e.Display_Name),!1)}));if(a.length>0)return this.$message.error("存在重复的字段名 : ".concat(a.map((function(e){return e.Display_Name})).join(", "))),[];this.$confirm("是否保存当前配置?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveLoading=!0;var t=e.list.map((function(e){return{Professional_Code:"Steel",Is_Component:"",Code:e.Code,Display_Name:e.Display_Name,Column_Type:e.Column_Type,Sort:e.Sort,Remark:e.Remark,Is_Enabled:e.Is_Enabled}}));(0,r.SavDeepenTemplateSetting)(t).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"}),e.saveLoading=!1}))})).catch((function(){e.$message({type:"info",message:"已取消"}),e.saveLoading=!1}))}},restore:function(e){var t=this,n=1===e?"二级":"三级";1===e?this.restoreLoading1=!0:this.restoreLoading2=!0,this.$confirm("此是否恢复默认".concat(n,"清单?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,r.RestoreTemplateType)({ProfessionalCode:"Steel",Type:e}).then((function(e){e.IsSucceed?(t.fetchData(),t.$message({type:"success",message:"恢复成功!"})):t.$message({message:e.Message,type:"error"}),t.restoreLoading1=!1,t.restoreLoading2=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"}),t.restoreLoading1=!1,t.restoreLoading2=!1}))}}}}}]);