(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-34f69ca8"],{"15fd":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=r,n("a4d3");var a=o(n("ccb5"));function o(t){return t&&t.__esModule?t:{default:t}}function r(t,e){if(null==t)return{};var n,o,r=(0,a.default)(t,e);if(Object.getOwnPropertySymbols){var u=Object.getOwnPropertySymbols(t);for(o=0;o<u.length;o++)n=u[o],-1===e.indexOf(n)&&{}.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}},"209b":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CheckPackCode=c,e.ExportComponentStockInInfo=p,e.ExportPackingInInfo=P,e.ExportWaitingStockIn2ndList=_,e.FinishCollect=b,e.From_Stock_Status_TYPES=void 0,e.GetComponentStockInEntity=l,e.GetLocationList=u,e.GetPackingDetailList=f,e.GetPackingEntity=v,e.GetPackingGroupByDirectDetailList=d,e.GetStockInDetailList=s,e.GetStockMoveDetailList=y,e.GetWarehouseListOfCurFactory=r,e.HandleInventoryItem=g,e.PackingTypes=e.PackingStatus=e.InventoryComponentTypes=e.InventoryCheckStatus=e.InventoryCheckExceptions=void 0,e.RemoveMain=m,e.SaveComponentScrap=O,e.SaveInventory=j,e.SavePacking=h,e.SaveStockIn=i,e.SaveStockMove=S,e.StockInTypes=void 0,e.UnzipPacking=I,e.UpdateBillReady=C,e.UpdateMaterialReady=k;var o=a(n("b775"));a(n("4328")),e.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],e.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],e.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],e.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],e.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],e.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],e.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function r(t){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:t}})}function s(t,e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:t,isEdit:e}})}function c(t,e){return(0,o.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:t,id:e}})}function d(t){return(0,o.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:t}})}function P(t){return(0,o.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:t}})}function m(t){return(0,o.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:t}})}function h(t){return(0,o.default)({url:"/PRO/Packing/SavePacking",method:"post",data:t})}function I(t){var e=t.id,n=t.locationId;return(0,o.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:e,locationId:n}})}function v(t){var e=t.id,n=t.code;return(0,o.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:e,code:n}})}function y(t){return(0,o.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:t}})}function S(t){return(0,o.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:t})}function g(t){var e=t.id,n=t.type,a=t.value;return(0,o.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:e,type:n,value:a}})}function O(t){return(0,o.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:t}})}function C(t){var e=t.installId,n=t.isReady;return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:e,isReady:n}})}function k(t){return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:t})}},"2c08":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.getQueryParam=e.addSearchLog=void 0;var o=a(n("5530")),r=a(n("15fd"));n("e9f5"),n("7d54"),n("e9c4"),n("b64b"),n("d3b7"),n("159b");var u=a(n("c14f")),i=a(n("1da1")),l=n("fd31"),s=n("3166"),c=["Project_Id","Sys_Project_Id","ProjectName"],d=(e.getQueryParam=function(){var t=(0,i.default)((0,u.default)().m((function t(e){var n,a,o,r,i,s,c,f,p,P,m,h=arguments;return(0,u.default)().w((function(t){while(1)switch(t.n){case 0:return n=h.length>1&&void 0!==h[1]?h[1]:"warehouse",t.p=1,t.n=2,(0,l.GetCurUserLastQueryParam)({Menu_Id:n});case 2:if(a=t.v,o={Project_Id:"",Sys_Project_Id:"",ProjectName:""},null!==a&&void 0!==a&&a.IsSucceed&&a.Data){t.n=3;break}return t.a(2,o);case 3:t.p=3,r=JSON.parse(a.Data),t.n=5;break;case 4:return t.p=4,t.v,t.a(2,o);case 5:if(i=[],"string"!==typeof e){t.n=6;break}i=[e],t.n=8;break;case 6:if(!Array.isArray(e)){t.n=7;break}i=e,t.n=8;break;case 7:return t.a(2,o);case 8:if(i.length){t.n=9;break}return t.a(2,o);case 9:if(s={},i.forEach((function(t){s[t]=r[t]||""})),c=s.Project_Id,f=s.Sys_Project_Id,p=c||f,!p){t.n=11;break}return t.n=10,d(p);case 10:m=t.v,t.n=12;break;case 11:m=o;case 12:return P=m,t.a(2,Object.assign(s,P));case 13:return t.p=13,t.v,t.a(2,{Project_Id:"",Sys_Project_Id:"",ProjectName:""})}}),t,null,[[3,4],[1,13]])})));return function(e){return t.apply(this,arguments)}}(),function(){var t=(0,i.default)((0,u.default)().m((function t(e){var n,a,o,r,i,l,c;return(0,u.default)().w((function(t){while(1)switch(t.n){case 0:if(n={Project_Id:"",Sys_Project_Id:"",ProjectName:""},e){t.n=1;break}return t.a(2,n);case 1:return t.p=1,t.n=2,(0,s.GetProjectEntity)({id:e});case 2:if(o=t.v,null!==o&&void 0!==o&&o.IsSucceed&&null!==(a=o.Data)&&void 0!==a&&a.Project){t.n=3;break}return t.a(2,n);case 3:return r=o.Data.Project,i=r.Short_Name,l=r.Id,c=r.Sys_Project_Id,t.a(2,{Project_Id:l||"",Sys_Project_Id:c||"",ProjectName:i||""});case 4:return t.p=4,t.v,t.a(2,n)}}),t,null,[[1,4]])})));return function(e){return t.apply(this,arguments)}}());e.addSearchLog=function(){var t=(0,i.default)((0,u.default)().m((function t(e){var n,a,i,s,d,f,p,P=arguments;return(0,u.default)().w((function(t){while(1)switch(t.n){case 0:return n=P.length>1&&void 0!==P[1]?P[1]:"warehouse",a=e.Project_Id,i=e.Sys_Project_Id,s=e.ProjectName,d=(0,r.default)(e,c),f=(0,o.default)({Project_Id:a||"",Sys_Project_Id:i||"",ProjectName:s||""},d),t.n=1,(0,l.AddSearchLog)({Menu_Id:n,Query_Param:JSON.stringify(f)});case 1:if(p=t.v,!p.IsSucceed){t.n=2;break}return t.a(2);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()},"2e8a":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteComponentType=c,e.GetCompTypeTree=d,e.GetComponentTypeEntity=s,e.GetComponentTypeList=u,e.GetFactoryCompTypeIndentifySetting=h,e.GetTableSettingList=p,e.GetTypePageList=i,e.RestoreTemplateType=y,e.SavDeepenTemplateSetting=v,e.SaveCompTypeIdentifySetting=I,e.SaveComponentType=l,e.SaveProBimComponentType=f,e.UpdateColumnSetting=m,e.UpdateComponentPartTableSetting=P;var o=a(n("b775")),r=a(n("4328"));function u(t){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:r.default.stringify(t)})}function c(t){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:r.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:r.default.stringify(t)})}function f(t){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:t})}function h(t){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:t})}function I(t){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:t})}function v(t){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:t})}function y(t){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:t})}},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=p,e.DeleteProject=c,e.GeAreaTrees=O,e.GetFileSync=k,e.GetInstallUnitIdNameList=g,e.GetNoBindProjectList=P,e.GetPartDeepenFileList=b,e.GetProjectAreaTreeList=j,e.GetProjectEntity=l,e.GetProjectList=i,e.GetProjectPageList=u,e.GetProjectTemplate=m,e.GetPushProjectPageList=S,e.GetSchedulingPartList=C,e.IsEnableProjectMonomer=d,e.SaveProject=s,e.UpdateProjectTemplateBase=h,e.UpdateProjectTemplateContacts=I,e.UpdateProjectTemplateContract=v,e.UpdateProjectTemplateOther=y;var o=a(n("b775")),r=a(n("4328"));function u(t){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function s(t){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function k(t){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"33cf":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n("209b");e.default={data:function(){return{warehouses:[],locations:[]}},mounted:function(){this.getWarehouseList()},methods:{getWarehouseList:function(){var t=this;(0,a.GetWarehouseListOfCurFactory)({}).then((function(e){e.IsSucceed&&(t.warehouses=e.Data)}))},wareChange:function(t){var e=this;this.form.Location_Id="",this.$nextTick((function(){e.form.Warehouse_Name=e.$refs["WarehouseRef"].selected.currentLabel})),(0,a.GetLocationList)({Warehouse_Id:t}).then((function(t){t.IsSucceed&&(e.locations=t.Data)}))},locationChange:function(t){var e=this;this.$nextTick((function(){e.form.Location_Name=e.$refs["LocationRef"].selected.currentLabel}))}}}},"4d7a":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.FIX_COLUMN=void 0,e.getFactoryProfessional=i;var o=a(n("c14f")),r=a(n("1da1"));n("d3b7");var u=n("fd31");function i(){return new Promise((function(t,e){(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then(function(){var n=(0,r.default)((0,o.default)().m((function n(a){return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:a.IsSucceed?t(a.Data):e("error");case 1:return n.a(2)}}),n)})));return function(t){return n.apply(this,arguments)}}()).catch((function(t){e("error")}))}))}e.FIX_COLUMN=["SteelName"]},"83b4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("7d54"),n("d3b7"),n("159b");var a=n("3166"),o=n("f2f6");e.default={data:function(){return{ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},SetupPositionData:[]}},mounted:function(){this.getProjectOption()},methods:{getProjectOption:function(){var t=this;(0,a.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getAreaList:function(){var t=this,e=this.form.Project_Id?this.form.Project_Id:this.form.ProjectId;(0,a.GeAreaTrees)({projectId:e}).then((function(e){if(e.IsSucceed){var n=e.Data;t.setDisabledTree(n),t.treeParamsArea.data=n,t.$nextTick((function(e){var a;null===(a=t.$refs.treeSelectArea)||void 0===a||a.treeDataUpdateFun(n)}))}else t.$message({message:e.Message,type:"error"})}))},getInstall:function(){var t=this;(0,o.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.SetupPositionData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},projectChangeSingle:function(t){var e,n=this;this.$nextTick((function(){n.form.ProjectName=n.$refs["ProjectName"].selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.getProjectEntity(t)},projectChange:function(t){var e,n=this;this.$nextTick((function(){var t;n.form.ProjectName=null===(t=n.$refs["ProjectName"])||void 0===t?void 0:t.selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.form.Area_Id="",this.form.AreaPosition="",this.treeParamsArea.data=[],this.$nextTick((function(t){var e;null===(e=n.$refs.treeSelectArea)||void 0===e||e.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",t&&this.getAreaList()},areaChange:function(t){this.form.AreaPosition=t.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.AreaPosition="",this.form.InstallUnit_Id="",this.form.SetupPosition=""},setupPositionChange:function(){var t=this;this.$nextTick((function(){t.form.SetupPosition=t.$refs["SetupPosition"].selected.currentLabel}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var n=t.Children;n&&n.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(n))}))},dateChange:function(t){},getProjectEntity:function(t){var e=this;(0,a.GetProjectEntity)({id:t}).then((function(t){if(t.IsSucceed){var n="",a=t.Data.Contacts;a.forEach((function(t){"Consignee"===t.Type&&(n=t.Name)})),e.consigneeName=n}else e.$message({message:t.Message,type:"error"})}))}}}},ccb5:function(t,e,n){"use strict";function a(t,e){if(null==t)return{};var n={};for(var a in t)if({}.hasOwnProperty.call(t,a)){if(-1!==e.indexOf(a))continue;n[a]=t[a]}return n}Object.defineProperty(e,"__esModule",{value:!0}),e.default=a},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=l,e.CheckPlanTime=s,e.DeleteInstallUnit=p,e.GetCompletePercent=v,e.GetEntity=S,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=I,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=i,e.GetInstallUnitPageList=u,e.GetProjectInstallUnitList=y,e.ImportInstallUnit=m,e.InstallUnitInfoTemplate=P,e.SaveInstallUnit=h,e.SaveOhterSourceInstallUnit=j;var o=a(n("b775")),r=a(n("4328"));function u(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function s(t){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function p(t){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function P(t){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function m(t){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function S(t){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function j(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);