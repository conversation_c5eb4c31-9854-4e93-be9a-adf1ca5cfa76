(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ce4b737e"],{"018c":function(e,t,a){},"01db":function(e,t,a){"use strict";a.r(t);var i=a("e2df"),n=a("9c4e");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("b464");var s=a("2877"),r=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"2040afa6",null);t["default"]=r.exports},"1ce4":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");var n=i(a("ade3")),o=a("6186"),s=i(a("a888")),r=a("7de9"),l=(a("221f"),i(a("0f97"))),c=a("d51a"),d=i(a("bbc2")),u=a("ed08");t.default={components:{DynamicDataTable:l.default,OSSUpload:d.default},directives:{elDragDialog:s.default},data:function(){return(0,n.default)((0,n.default)((0,n.default)((0,n.default)((0,n.default)((0,n.default)((0,n.default)({Check_Style:1,btnLoading:!1,isCheck:!1,isSee:!1,chooseTitle:"",Code:"",loading:!1,tbConfig:{},columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:10},selectList:[],gridCode:"pro2_bitch_steel_list",form:{Check_Object_Type:"",Check_Node_Id:"",Check_Type:null,Sheet_Result:"",Code:"",Number:"",AllUnqualified_Count:0,Rectifier_name:"",Rectify_Date:"",Status:""},CheckTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],CheckNodeList:[],CheckObjectData:[],shawDialog:!1,title:"新建质检单",disable:!1,plm_Factory_Sheets:[]},"isSee",!1),"rectificationState",1),"form2",{Sheet_Id:"",IsPass:"",Suggestion:"",fileList2:[]}),"rules2",{IsPass:[{required:!0,message:"请复核",trigger:"change"}]}),"fileList",[]),"fileList2",[]),"Attachments",[])},mounted:function(){this.getCheckType()},methods:{handelInfo:function(e,t){var a=this;e.forEach((function(e){a.tbData.push(e)})),this.getCount(),this.Check_Style=t},handleClose:function(){this.shawDialog=!1,this.tbData=[],this.form={Check_Object_Type:"",Check_Node_Id:"",Check_Type:null,Sheet_Result:"",Code:"",Number:"",AllUnqualified_Count:0,Rectifier_name:"",Rectify_Date:"",Status:""},this.form2={Sheet_Id:"",IsPass:"",Suggestion:"",fileList2:[]},this.disable=!1},init:function(e,t,a,i,n){this.detail=t,this.isCheck=a,this.isSee=i,this.form2.Sheet_Id=t.SheetId,this.rectificationState=n,t&&(this.disable=!0,this.title="整改单","构件"==t.Check_Object_Type?(this.gridCode="pro2_bitch_steel_list",this.check_object_id="0"):"零件"==t.Check_Object_Type?(this.gridCode="pro2_bitch_part_list",this.check_object_id="1"):"部件"==t.Check_Object_Type&&(this.gridCode="pro2_bitch_part_list",this.check_object_id="3"),this.getCheckingEntity(t)),this.Code=e,this.shawDialog=!0,this.getGridByCode()},getCheckingEntity:function(e){var t=this;(0,c.GetCheckingEntity)({sheetId:e.SheetId}).then((function(a){a.IsSucceed?(t.plm_Factory_Sheets=a.Data[0],t.form.Check_Object_Type=t.plm_Factory_Sheets.Check_Object_Type,t.form.Check_Type=t.plm_Factory_Sheets.Check_Type,t.form.Sheet_Result=t.plm_Factory_Sheets.Check_Result,t.form.Check_Node_Id=t.plm_Factory_Sheets.Check_Node_Id,t.form.SheetId=t.plm_Factory_Sheets.SheetId,t.form.Code=t.plm_Factory_Sheets.Code,t.form.Number=t.plm_Factory_Sheets.Number,t.form.Rectifier_name=t.plm_Factory_Sheets.Rectifier_name,t.form.Rectify_Date=t.plm_Factory_Sheets.Rectify_Date?(0,u.parseTime)(new Date(t.plm_Factory_Sheets.Rectify_Date),"{y}-{m}-{d}"):"",t.form.Status=t.plm_Factory_Sheets.Status,"构件"===e.Check_Object_Type?t.tbData=a.Data:("零件"===e.Check_Object_Type||"部件"===e.Check_Object_Type)&&(t.tbData=a.Data.map((function(e){return e.Project_Name=e.ProjectName,e.Area_Name=e.AreaPosition,e.InstallUnit_Name=e.SetupPosition,e.Code=e.SteelName,e.Spec=e.SteelSpec,e.Length=e.SteelLength,e.Num=e.SteelAmount,e}))),t.tbData=t.tbData.filter((function(e){return e.Unqualified_Count})),t.form.AllUnqualified_Count=0,t.tbData.forEach((function(e){t.form.AllUnqualified_Count+=e.Unqualified_Count}))):t.$message({type:"error",message:a.Message})}))},uploadSuccess:function(e,t,a){var i=this;this.Attachments=[],a.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,i.Attachments.push(t)}))},uploadExceed:function(e,t){this.$message({type:"warning",message:"已超过文件上传最大数量"})},uploadRemove:function(e,t){var a=this;this.Attachments=[],t.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,a.Attachments.push(t)}))},handlePreview2:function(e){var t=null;t=e.hasOwnProperty("response")?e.response.encryptionUrl:e.url,window.open(t,"_blank")},getNode:function(e){this.CheckObjectData.find((function(t){return t.Display_Name==e}))},getCheckType:function(){var e=this;(0,r.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:t.Message})})).catch((function(){}))},changeObject:function(e){var t;this.tbData=[];var a=null===(t=this.CheckObjectData.find((function(t){return t.Id==e})))||void 0===t?void 0:t.Display_Name;switch(this.chooseTitle=a,a){case"构件":this.check_object_id="0",this.gridCode="pro2_bitch_steel_list";break;case"零件":this.check_object_id="1",this.gridCode="pro2_bitch_part_list";break;case"物料":this.check_object_id="2";break;case"部件":this.check_object_id="3",this.gridCode="pro2_bitch_part_list";break;default:this.check_object_id="0"}this.getGridByCode(),this.getNodeList(e),this.qualityItem=this.CheckObjectData.find((function(t){return t.Id==e})),this.$emit("qualityItemChange",this.qualityItem)},getNodeList:function(e){var t=this;(0,r.GetNodeList)({check_object_id:e}).then((function(e){e.IsSucceed?t.$nextTick((function(){t.CheckNodeList=e.Data})):t.$message({type:"error",message:"res.Message"})}))},changeCheckNode:function(e){this.CheckTypeList=[];var t=this.CheckNodeList.find((function(t){return t.Id===e})).Check_Type;"-1"==t?this.CheckTypeList=[{Name:"质量",Id:"1"},{Name:"探伤",Id:"2"}]:"1"==t?this.CheckTypeList=[{Name:"质量",Id:"1"}]:"2"==t&&(this.CheckTypeList=[{Name:"探伤",Id:"2"}])},getGridByCode:function(){var e=this;this.loading=!0,(0,o.GetGridByCode)({Code:this.gridCode+","+this.Code}).then((function(t){t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList))})).then((function(){e.loading=!1}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{}),this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){"3"===this.check_object_id&&e.map((function(e){return"Code"===e.Code&&(e.Display_Name="部件名称"),e})),this.columns=e},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount,this.TotalAmount=e.TotalAmount,this.TotalWeight=e.TotalWeight},gridPageChange:function(e){var t=e.page;this.pageInfo.Page=Number(t),this.fetchData()},gridSizeChange:function(e){var t=e.size;this.tbConfig.Row_Number=Number(t),this.pageInfo.PageSize=Number(t),this.pageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},addInfo:function(){var e=[];this.tbData.forEach((function(t){e.push(t.Id)})),this.$emit("BitchopenDialog",this.check_object_id,this.chooseTitle,e)},handeldelete:function(){var e=this;this.selectList.forEach((function(t){var a=e.tbData.find((function(e){return e.Id===t.Id})),i=e.tbData.indexOf(a);e.tbData.splice(i,1)}))},submit:function(){var e=this,t=this.form2,a=t.Sheet_Id,i=t.IsPass,n=t.Suggestion,o={plm_Sheetreply:{Sheet_Id:a||"",IsPass:i,Suggestion:n||""},_Attachments:this.Attachments};2!==this.rectificationState&&delete o.plm_Sheetreply["IsPass"],this.$refs.form2.validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,c.SaveFeedBack)(o).then((function(t){t.IsSucceed?(e.$message({message:"提交成功",type:"success"}),e.$emit("refresh"),e.handleClose()):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))}}}},"221f":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Add=l,t.AddLanch=r,t.DelLanch=d,t.EditLanch=f,t.EntityLanch=o,t.GetEditById=u,t.GetPageQualitySummary=p,t.GetPartAndSteelBacrode=s,t.RectificationRecord=h,t.SubmitLanch=c;var n=i(a("b775"));function o(e){return(0,n.default)({url:"/PRO/Inspection/EntityLanch",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:e})}function r(e){return(0,n.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Inspection/Add",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Inspection/EditLanch",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Inspection/GetPageQualitySummary",method:"post",data:e})}},"2a58":function(e,t,a){"use strict";a.r(t);var i=a("e0b9"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},3302:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form.Feedmodel,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Check_Object_Type_Id,callback:function(t){e.$set(e.form.Feedmodel,"Check_Object_Type_Id",t)},expression:"form.Feedmodel.Check_Object_Type_Id"}},e._l(e.qualityList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Check_Type,callback:function(t){e.$set(e.form.Feedmodel,"Check_Type",t)},expression:"form.Feedmodel.Check_Type"}},[a("el-option",{attrs:{label:"质量",value:1}}),a("el-option",{attrs:{label:"探伤",value:2}})],1)],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"整改单号",prop:"Code"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Feedmodel.Code,callback:function(t){e.$set(e.form.Feedmodel,"Code",t)},expression:"form.Feedmodel.Code"}})],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"整改人",prop:"Rectifier_Id"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Feedmodel.Rectifier_Id,callback:function(t){e.$set(e.form.Feedmodel,"Rectifier_Id",t)},expression:"form.Feedmodel.Rectifier_Id"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",{attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",[a("el-radio-group",{attrs:{size:"small"},on:{change:e.radioChange},model:{value:e.radioStatus,callback:function(t){e.radioStatus=t},expression:"radioStatus"}},[a("el-radio-button",{attrs:{label:1}},[e._v("待整改")]),a("el-radio-button",{attrs:{label:2}},[e._v("待复核")]),a("el-radio-button",{attrs:{label:-1}},[e._v("我的整改单")])],1)],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding cs-main",staticStyle:{padding:"0"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Status",fn:function(t){var i=t.row;return["已完成"===i.Status?a("span",{staticClass:"by-dot by-dot-success"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待复核"===i.Status||"待整改"===i.Status?a("span",{staticClass:"by-dot by-dot-primary"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待质检"===i.Status||"草稿"===i.Status?a("span",{staticClass:"by-dot by-dot-info"},[e._v(" "+e._s(i.Status||"-")+" ")]):a("span",[e._v(" "+e._s(i.Status||"-")+" ")])]}},{key:"op",fn:function(t){var i=t.row,n=t.index;return[a("div",[1===e.form.Feedmodel.Status?a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleRectification(i.SheetId)}}},[e._v("整改")]):2===e.form.Feedmodel.Status?a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleReview(i.SheetId)}}},[e._v("复核")]):a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleInfo(i.SheetId)}}},[e._v("查看详情")]),a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleRecord(i)}}},[e._v("操作记录")])],1)]}}])})],1)],1)]),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"480px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-row":e.selectRow},on:{openDialog:e.openDialog,close:e.handleClose,refresh:e.fetchData,qualityItemChange:e.qualityItemChange}})],1):e._e(),e.dialogVisible2?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content2",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle2,visible:e.dialogVisible2,width:"66%"},on:{"update:visible":function(t){e.dialogVisible2=t},close:e.handleClose2}},[a(e.currentComponent2,{ref:"content2",tag:"component",attrs:{"quality-item":e.qualityItem},on:{close:e.handleClose2,getSelectRow:e.getSelectRow}})],1):e._e(),a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content3",staticClass:"plm-custom-dialog",attrs:{title:"操作记录",visible:e.dialogVisible3,width:"50%"},on:{"update:visible":function(t){e.dialogVisible3=t},close:e.handleClose3}},[a(e.currentComponent3,{ref:"content3",tag:"component",on:{close:e.handleClose3}})],1)],1)},n=[]},"352a":function(e,t,a){},"3ad9":function(e,t,a){},"3fd5":function(e,t,a){"use strict";a.r(t);var i=a("cb37"),n=a("977d");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("82ce");var s=a("2877"),r=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"c301d8d4",null);t["default"]=r.exports},"4e82":function(e,t,a){"use strict";var i=a("23e7"),n=a("e330"),o=a("59ed"),s=a("7b0b"),r=a("07fa"),l=a("083a"),c=a("577e"),d=a("d039"),u=a("addb"),f=a("a640"),h=a("3f7e"),p=a("99f4"),m=a("1212"),g=a("ea83"),y=[],b=n(y.sort),_=n(y.push),C=d((function(){y.sort(void 0)})),v=d((function(){y.sort(null)})),I=f("sort"),S=!d((function(){if(m)return m<70;if(!(h&&h>3)){if(p)return!0;if(g)return g<603;var e,t,a,i,n="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(i=0;i<47;i++)y.push({k:t+i,v:a})}for(y.sort((function(e,t){return t.v-e.v})),i=0;i<y.length;i++)t=y[i].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}})),k=C||!v||!I||!S,P=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:c(t)>c(a)?1:-1}};i({target:"Array",proto:!0,forced:k},{sort:function(e){void 0!==e&&o(e);var t=s(this);if(S)return void 0===e?b(t):b(t,e);var a,i,n=[],c=r(t);for(i=0;i<c;i++)i in t&&_(n,t[i]);u(n,P(e)),a=r(n),i=0;while(i<a)t[i]=n[i++];while(i<c)l(t,i++);return t}})},"53a0":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var n=i(a("5530")),o=i(a("c14f")),s=i(a("1da1")),r=i(a("3fd5")),l=i(a("0f97")),c=i(a("a888")),d=a("d51a"),u=a("fd31"),f=a("1b69"),h=i(a("5cc7")),p=i(a("01db")),m=a("ed08");t.default={directives:{elDragDialog:c.default},components:{DynamicDataTable:l.default,rectificationDialog:r.default,selectedDialog2:p.default},mixins:[h.default],data:function(){return{recordSheetId:"",radioStatus:1,addradio:"pro_waiting_out_list",Is_Pack:!1,code:"",TypeId:"",typeOption:"",dialogVisible:!1,dialogVisible2:!1,dialogVisible3:!1,loading:!1,dialogTitle:"",dialogTitle2:"",Ismodal:!0,dialogData:{},currentComponent:"",currentComponent2:"",currentComponent3:"rectificationDialog",tbConfig:{Op_Width:150},Data:[],form:{Feedmodel:{Status:1,Check_Object_Type_Id:"",Check_Type:"",Code:"",Rectifier_Id:"",Check_Style:0},PageInfo:{Page:1,PageSize:20}},userList:[],selectList:[],qualityList:[],qualityItem:{},selectRow:{},nodeList:[],projectList:[],Date_Time:"",columns:[],tbData:[],total:0,pageInfo:{Page:1,TotalCount:0,PageSize:20},gridCode:"pro_start_inspect",searchHeight:0,pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}],code:""}}},created:function(){this.getFactoryTypeOption(),this.getDictionaryDetailListByCode(),this.getFactoryPeoplelist()},mounted:function(){this.searchHeight=this.$refs.searchDom.offsetHeight+200},methods:{getFactoryTypeOption:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?(e.ProfessionalType=t.Data,e.code=e.ProfessionalType[0].Code):e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_check_feedback_part_list,".concat(e.ProfessionalType[0].Code));case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},fetchData:function(){var e=this,t=(0,n.default)({},this.form);(0,d.GetPageFeedBack)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Rectify_Date=e.Rectify_Date?(0,m.parseTime)(new Date(e.Rectify_Date),"{y}-{m}-{d}"):e.Rectify_Date,e.Pick_Date=e.Pick_Date?(0,m.parseTime)(new Date(e.Pick_Date),"{y}-{m}-{d}"):e.Pick_Date,e.Id=e.SheetId,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})}))},radioChange:function(e){this.form.Feedmodel.Status=e,this.form.PageInfo.Page=1,this.fetchData()},getFactoryPeoplelist:function(){var e=this;(0,d.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},getDictionaryDetailListByCode:function(){var e=this;(0,d.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.qualityList=t.Data:e.$message({message:t.Message,type:"error"})}))},qualityListChange:function(e){e&&this.getNodeList()},qualityListClear:function(e){this.$refs.form.resetFields()},getNodeList:function(){var e=this;(0,d.GetNodeList)({check_object_id:this.form.Feedmodel.checkObjectId}).then((function(t){t.IsSucceed?e.nodeList=t.Data:e.$message({message:t.Message,type:"error"})}))},nodeChange:function(e){var t=this;e&&this.nodeList.find((function(a){a.Id===e&&(t.form.Feedmodel.checkType=a.Check_Type)}))},handleRectification:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].init(t.code,e,!0,!0,1)}))},handleReview:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].init(t.code,e,!0,!0,2)}))},handleInfo:function(e){var t=this;this.$nextTick((function(a){t.$refs["selectRef"].init(t.code,e,!0,!0,-1)}))},handleRecord:function(e){this.getrectificationRecord(e)},getrectificationRecord:function(e){var t=this;(0,d.RectificationRecord)({sheetid:e.SheetId}).then((function(a){a.IsSucceed?0===a.Data.length?t.$message({type:"error",message:"暂无操作记录"}):(t.dialogVisible3=!0,t.$nextTick((function(a){t.$refs["content3"].init(e)}))):t.$message({type:"error",message:a.Message})}))},getProjectPageList:function(){var e=this;(0,f.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)}))},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},changesearchDate:function(e){},allPass:function(){var e=[];this.selectList.forEach((function(t){e.push(t.Id)}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{}),this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){this.columns=e},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount,this.TotalAmount=e.TotalAmount,this.TotalWeight=e.TotalWeight},multiSelectedChange:function(e){this.selectList=e},qualityItemChange:function(e){this.qualityItem=e},getSelectRow:function(e){this.selectRow=e},generateComponent:function(e,t,a){a&&2==a?(this.dialogTitle2=e,this.currentComponent2=t,this.dialogVisible2=!0):(this.dialogTitle=e,this.currentComponent=t,this.dialogVisible=!0)},openDialog:function(){var e=this;"构件"===this.qualityItem.Display_Name?this.generateComponent("添加构件","addComponent",2):this.generateComponent("添加零件","addComponent",2),this.$nextTick((function(t){e.$refs["content2"].init()}))},handleClose:function(){this.dialogVisible=!1},handleClose2:function(){this.dialogVisible2=!1},handleClose3:function(){this.dialogVisible3=!1}}}},"5cc7":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var i=a("6186"),n=a("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,n.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(a){a.IsSucceed?(t.ProfessionalType=a.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:a.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,i.GetGridByCode)({code:e}).then((function(e){var i=e.IsSucceed,n=e.Data,o=e.Message;if(i){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+n.Grid.Row_Number:t.form.PageSize=+n.Grid.Row_Number,a(t.columns)}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},"60f0":function(e,t,a){"use strict";a("018c")},"63eb":function(e,t,a){},"6a26":function(e,t,a){"use strict";a("70c5")},"70c5":function(e,t,a){},"75c0":function(e,t,a){"use strict";a("3ad9")},"7de9":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=h,t.AddCheckItemCombination=y,t.AddCheckType=l,t.DelNode=D,t.DelQualityList=S,t.DeleteCheckItem=f,t.DeleteCheckType=c,t.EntityCheckItem=p,t.EntityCheckType=s,t.EntityQualityList=I,t.ExportInspsectionSummaryInfo=L,t.GetCheckGroupList=g,t.GetCheckItemList=u,t.GetCheckTypeList=r,t.GetCompTypeTree=T,t.GetDictionaryDetailListByCode=o,t.GetEntityNode=P,t.GetFactoryPeoplelist=_,t.GetFactoryProfessionalByCode=O,t.GetMaterialType=w,t.GetNodeList=k,t.GetProEntities=b,t.GetProcessCodeList=C,t.QualityList=v,t.SaveCheckItem=m,t.SaveCheckType=d,t.SaveNode=R;var n=i(a("b775"));function o(e){return(0,n.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function r(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},"82ce":function(e,t,a){"use strict";a("63eb")},"8f4d":function(e,t,a){"use strict";a.r(t);var i=a("ac3c"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},9396:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"header_tab"},[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"全检",name:"全检"}}),a("el-tab-pane",{attrs:{label:"抽检",name:"抽检"}})],1)],1),"全检"==e.activeName?a("full-check"):e._e(),"抽检"==e.activeName?a("spot-check"):e._e()],1)])},n=[]},"977d":function(e,t,a){"use strict";a.r(t);var i=a("a138"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},"9c4e":function(e,t,a){"use strict";a.r(t);var i=a("1ce4"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},a138:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var i=a("221f"),n=a("8975");a("0e9a"),t.default={data:function(){return{list:[],srcList:[]}},methods:{init:function(e){this.getrectificationRecord(e.SheetId)},getrectificationRecord:function(e){var t=this;this.srcList=[],(0,i.RectificationRecord)({sheetid:e}).then((function(e){e.IsSucceed?0!=e.Data.length?t.list=e.Data.map((function(e){switch(e.Reply.Type){case 1:e.Reply.Type="整改";break;case 2:e.Reply.Type="复核";break;case 3:e.Reply.Type="评论";break;case 0:e.Reply.Type="移交";break;case-1:e.Reply.Type="初次整改";break}return e.Reply.ActionTime=(0,n.timeFormat)(e.Reply.ActionTime,"{y}-{m}-{d} {h}:{i}:{s}"),e.Attachments.forEach((function(e){t.srcList.push(e.File_Url)})),e})):t.$message({type:"error",message:"暂无操作记录"}):t.$message({type:"error",message:e.Message})}))}}}},a888:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("d565")),o=function(e){e.directive("el-drag-dialog",n.default)};window.Vue&&(window["el-drag-dialog"]=n.default,Vue.use(o)),n.default.install=o;t.default=n.default},ac3c:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("fd13")),o=i(a("db5c"));t.default={name:"PROQualityRectification2",components:{fullCheck:n.default,spotCheck:o.default},data:function(){return{activeName:"全检"}},mounted:function(){}}},b464:function(e,t,a){"use strict";a("352a")},c02a:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper-main"},[a("div",{ref:"searchDom",staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form.Feedmodel,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Check_Object_Type_Id,callback:function(t){e.$set(e.form.Feedmodel,"Check_Object_Type_Id",t)},expression:"form.Feedmodel.Check_Object_Type_Id"}},e._l(e.qualityList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Feedmodel.Check_Type,callback:function(t){e.$set(e.form.Feedmodel,"Check_Type",t)},expression:"form.Feedmodel.Check_Type"}},[a("el-option",{attrs:{label:"质量",value:1}}),a("el-option",{attrs:{label:"探伤",value:2}})],1)],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"整改单号",prop:"Code"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Feedmodel.Code,callback:function(t){e.$set(e.form.Feedmodel,"Code",t)},expression:"form.Feedmodel.Code"}})],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"整改人",prop:"Rectifier_Id"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Feedmodel.Rectifier_Id,callback:function(t){e.$set(e.form.Feedmodel,"Rectifier_Id",t)},expression:"form.Feedmodel.Rectifier_Id"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1)],1)],1),a("div",{staticClass:"table_warrap",staticStyle:{flex:"1"}},[a("div",{staticClass:"table_content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","margin-bottom":"16px"}},[a("div",[a("el-radio-group",{attrs:{size:"small"},on:{change:e.radioChange},model:{value:e.radioStatus,callback:function(t){e.radioStatus=t},expression:"radioStatus"}},[a("el-radio-button",{attrs:{label:1}},[e._v("待整改")]),a("el-radio-button",{attrs:{label:2}},[e._v("待复核")]),a("el-radio-button",{attrs:{label:-1}},[e._v("我的整改单")])],1)],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding cs-main",staticStyle:{padding:"0"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Status",fn:function(t){var i=t.row;return["已完成"===i.Status?a("span",{staticClass:"by-dot by-dot-success"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待复核"===i.Status||"待整改"===i.Status?a("span",{staticClass:"by-dot by-dot-primary"},[e._v(" "+e._s(i.Status||"-")+" ")]):"待质检"===i.Status||"草稿"===i.Status?a("span",{staticClass:"by-dot by-dot-info"},[e._v(" "+e._s(i.Status||"-")+" ")]):a("span",[e._v(" "+e._s(i.Status||"-")+" ")])]}},{key:"op",fn:function(t){var i=t.row,n=t.index;return[a("div",[1===e.form.Feedmodel.Status?a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleRectification(i)}}},[e._v("整改")]):2===e.form.Feedmodel.Status?a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleReview(i)}}},[e._v("复核")]):a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleInfo(i)}}},[e._v("查看详情")]),a("el-button",{attrs:{index:n,type:"text"},on:{click:function(t){return e.handleRecord(i)}}},[e._v("操作记录")])],1)]}}])})],1)],1)]),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"480px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-row":e.selectRow},on:{openDialog:e.openDialog,close:e.handleClose,refresh:e.fetchData,qualityItemChange:e.qualityItemChange}})],1):e._e(),e.dialogVisible2?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content2",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle2,visible:e.dialogVisible2,width:"66%"},on:{"update:visible":function(t){e.dialogVisible2=t},close:e.handleClose2}},[a(e.currentComponent2,{ref:"content2",tag:"component",attrs:{"quality-item":e.qualityItem},on:{close:e.handleClose2,getSelectRow:e.getSelectRow}})],1):e._e(),a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content3",staticClass:"plm-custom-dialog",attrs:{title:"操作记录",visible:e.dialogVisible3,width:"50%"},on:{"update:visible":function(t){e.dialogVisible3=t},close:e.handleClose3}},[a(e.currentComponent3,{ref:"content3",tag:"component",on:{close:e.handleClose3}})],1),a("selected-Dialog2",{ref:"selectRef",on:{refresh:e.fetchData}})],1)},n=[]},cb37:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper"},[a("div",{staticClass:"wrapper-main"},e._l(e.list,(function(t,i){return a("ul",{key:i},[a("li",{staticClass:"top"},[a("span",{staticClass:"left-title"},[a("i"),e._v(e._s(t.Reply.Create_UserName))]),a("span",{staticStyle:{color:"#298dff","margin-right":"10px"}},[e._v(e._s(t.Reply.Type))]),a("span",[e._v(e._s(t.Reply.ActionTime))])]),"复核"==t.Reply.Type?a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Reply.Type)+"状态")]),a("span",{staticStyle:{color:"#00c361"}},[e._v(e._s(t.Reply.IsPass?"合格":"不合格"))])]):e._e(),a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Reply.Type)+"内容")]),a("span",[e._v(e._s(t.Reply.Suggestion))])]),a("li",[a("div",{staticClass:"left-title"},[e._v("图片")]),e._l(t.Attachments,(function(t,i){return a("div",{key:i,staticClass:"img_Wrapper"},[a("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:t.File_Url,"preview-src-list":e.srcList}})],1)}))],2)])})),0)])},n=[]},cce0:function(e,t,a){"use strict";a.r(t);var i=a("9396"),n=a("8f4d");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("6a26");var s=a("2877"),r=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"40812bd5",null);t["default"]=r.exports},d51a:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddLanch=r,t.BatchManageSaveCheck=b,t.DelLanch=y,t.DeleteToleranceSetting=L,t.EntityQualityManagement=u,t.ExportQISummary=R,t.GetCheckingEntity=C,t.GetCompPartForSpotCheckPageList=k,t.GetCompQISummary=D,t.GetDictionaryDetailListByCode=l,t.GetEditById=_,t.GetFactoryPeoplelist=h,t.GetNodeList=c,t.GetPageFeedBack=v,t.GetPageQualityManagement=o,t.GetPartAndSteelBacrode=d,t.GetSheetDwg=p,t.GetSpotCheckingEntity=P,t.GetToleranceSettingList=x,t.ImportQISummary=T,t.ManageAdd=s,t.RectificationRecord=S,t.SaveFeedBack=I,t.SavePass=m,t.SaveQIReportData=O,t.SaveTesting=f,t.SaveToleranceSetting=w,t.SubmitLanch=g;var n=i(a("b775"));function o(e){return(0,n.default)({url:"/PRO/Inspection/GetPageQualityManagement",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Inspection/ManageAdd",method:"post",data:e})}function r(e){return(0,n.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:e})}function l(e){return(0,n.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Inspection/EntityQualityManagement",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Inspection/SaveTesting",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Inspection/GetSheetDwg",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Inspection/SavePass",method:"post",data:e,timeout:12e5})}function g(e){return(0,n.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Inspection/BatchManageSaveCheck",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckingEntity",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Inspection/GetPageFeedBack",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Inspection/SaveFeedBack",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Inspection/GetCompPartForSpotCheckPageList",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Inspection/GetSpotCheckingEntity",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Inspection/GetCompQISummary",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Inspection/ExportQISummary",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Inspection/ImportQISummary",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/Inspection/SaveQIReportData",method:"post",data:e})}function w(e){return(0,n.default)({url:"/pro/Inspection/SaveToleranceSetting",method:"post",data:e})}function L(e){return(0,n.default)({url:"/pro/Inspection/DeleteToleranceSetting",method:"post",data:e})}function x(e){return(0,n.default)({url:"/pro/Inspection/GetToleranceSettingList",method:"post",data:e})}},d565:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319");t.default={bind:function(e,t,a){var i=e.querySelector(".el-dialog__header"),n=e.querySelector(".el-dialog");i.style.cssText+=";cursor:move;",n.style.cssText+=";top:0px;";var o=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();i.onmousedown=function(e){var t=e.clientX-i.offsetLeft,s=e.clientY-i.offsetTop,r=n.offsetWidth,l=n.offsetHeight,c=document.body.clientWidth,d=document.body.clientHeight,u=n.offsetLeft,f=c-n.offsetLeft-r,h=n.offsetTop,p=d-n.offsetTop-l,m=o(n,"left"),g=o(n,"top");m.includes("%")?(m=+document.body.clientWidth*(+m.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(m=+m.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var i=e.clientX-t,o=e.clientY-s;-i>u?i=-u:i>f&&(i=f),-o>h?o=-h:o>p&&(o=p),n.style.cssText+=";left:".concat(i+m,"px;top:").concat(o+g,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}},db5c:function(e,t,a){"use strict";a.r(t);var i=a("c02a"),n=a("e1b2");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("75c0");var s=a("2877"),r=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"328526d9",null);t["default"]=r.exports},e0b9:function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("5530")),o=i(a("c14f")),s=i(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var r=i(a("3fd5")),l=i(a("0f97")),c=i(a("a888")),d=a("d51a"),u=a("fd31"),f=a("1b69"),h=i(a("5cc7")),p=i(a("2082")),m=a("ed08");t.default={directives:{elDragDialog:c.default},components:{DynamicDataTable:l.default,rectificationDialog:r.default},mixins:[p.default,h.default],data:function(){return{recordSheetId:"",radioStatus:1,addradio:"pro_waiting_out_list",Is_Pack:!1,code:"",TypeId:"",typeOption:"",dialogVisible:!1,dialogVisible2:!1,dialogVisible3:!1,loading:!1,dialogTitle:"",dialogTitle2:"",Ismodal:!0,dialogData:{},currentComponent:"",currentComponent2:"",currentComponent3:"rectificationDialog",tbConfig:{Op_Width:150},Data:[],form:{Feedmodel:{Status:1,Check_Object_Type_Id:"",Check_Type:"",Code:"",Rectifier_Id:"",Check_Style:1},PageInfo:{Page:1,PageSize:20}},userList:[],selectList:[],qualityList:[],qualityItem:{},selectRow:{},nodeList:[],projectList:[],Date_Time:"",columns:[],tbData:[],total:0,pageInfo:{Page:1,TotalCount:0,PageSize:20},gridCode:"pro_start_inspect",searchHeight:0,pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}],code:""},addPageArray:[{path:this.$route.path+"/check",hidden:!0,component:function(){return a.e("chunk-6bb995da").then(a.bind(null,"8979"))},name:"PRORectificationInfo",meta:{title:"整改"}},{path:this.$route.path+"/check",hidden:!0,component:function(){return a.e("chunk-6bb995da").then(a.bind(null,"8979"))},name:"PROReviewInfo",meta:{title:"复核"}},{path:this.$route.path+"/check",hidden:!0,component:function(){return a.e("chunk-6bb995da").then(a.bind(null,"8979"))},name:"PROCheckInfo",meta:{title:"查看详情"}}]}},created:function(){this.getFactoryTypeOption(),this.getDictionaryDetailListByCode(),this.getFactoryPeoplelist()},mounted:function(){this.searchHeight=this.$refs.searchDom.offsetHeight+200},methods:{getFactoryTypeOption:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?(e.ProfessionalType=t.Data,e.code=e.ProfessionalType[0].Code):e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_check_feedback_all_list,".concat(e.ProfessionalType[0].Code));case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},fetchData:function(){var e=this,t=(0,n.default)({},this.form);(0,d.GetPageFeedBack)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.Rectify_Date=e.Rectify_Date?(0,m.parseTime)(new Date(e.Rectify_Date),"{y}-{m}-{d}"):e.Rectify_Date,e.Pick_Date=e.Pick_Date?(0,m.parseTime)(new Date(e.Pick_Date),"{y}-{m}-{d}"):e.Pick_Date,e.Id=e.SheetId,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})}))},radioChange:function(e){this.form.Feedmodel.Status=e,this.form.PageInfo.Page=1,this.fetchData()},getFactoryPeoplelist:function(){var e=this;(0,d.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},getDictionaryDetailListByCode:function(){var e=this;(0,d.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.qualityList=t.Data:e.$message({message:t.Message,type:"error"})}))},qualityListChange:function(e){e&&this.getNodeList()},qualityListClear:function(e){this.$refs.form.resetFields()},getNodeList:function(){var e=this;(0,d.GetNodeList)({check_object_id:this.form.Feedmodel.checkObjectId}).then((function(t){t.IsSucceed?e.nodeList=t.Data:e.$message({message:t.Message,type:"error"})}))},nodeChange:function(e){var t=this;e&&this.nodeList.find((function(a){a.Id===e&&(t.form.Feedmodel.checkType=a.Check_Type)}))},handleRectification:function(e){this.$router.push({name:"PRORectificationInfo",query:{pg_redirect:"PROQualityFeedback",sheetId:e,isSee:!0,isRectification:!0}})},handleReview:function(e){this.$router.push({name:"PROReviewInfo",query:{pg_redirect:"PROQualityFeedback",sheetId:e,isSee:!0,isReview:!0}})},handleInfo:function(e){this.$router.push({name:"PROCheckInfo",query:{pg_redirect:"PROQualityFeedback",sheetId:e,isSee:!0,isInfo:!0}})},handleRecord:function(e){this.getrectificationRecord(e)},getrectificationRecord:function(e){var t=this;(0,d.RectificationRecord)({sheetid:e.SheetId}).then((function(a){a.IsSucceed?0===a.Data.length?t.$message({type:"error",message:"暂无操作记录"}):(t.dialogVisible3=!0,t.$nextTick((function(a){t.$refs["content3"].init(e)}))):t.$message({type:"error",message:a.Message})}))},getProjectPageList:function(){var e=this;(0,f.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)}))},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()},changesearchDate:function(e){},allPass:function(){var e=[];this.selectList.forEach((function(t){e.push(t.Id)}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{}),this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){this.columns=e},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount,this.TotalAmount=e.TotalAmount,this.TotalWeight=e.TotalWeight},multiSelectedChange:function(e){this.selectList=e},qualityItemChange:function(e){this.qualityItem=e},getSelectRow:function(e){this.selectRow=e},generateComponent:function(e,t,a){a&&2==a?(this.dialogTitle2=e,this.currentComponent2=t,this.dialogVisible2=!0):(this.dialogTitle=e,this.currentComponent=t,this.dialogVisible=!0)},openDialog:function(){var e=this;"构件"===this.qualityItem.Display_Name?this.generateComponent("添加构件","addComponent",2):this.generateComponent("添加零件","addComponent",2),this.$nextTick((function(t){e.$refs["content2"].init()}))},handleClose:function(){this.dialogVisible=!1},handleClose2:function(){this.dialogVisible2=!1},handleClose3:function(){this.dialogVisible3=!1}}}},e1b2:function(e,t,a){"use strict";a.r(t);var i=a("53a0"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},e2df:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog_wapper"},[e.shawDialog?a("el-dialog",{ref:"content",staticClass:"plm-custom-dialog",staticStyle:{"margin-top":"-5vh"},attrs:{title:e.title,visible:e.shawDialog,width:"60%"},on:{"update:visible":function(t){e.shawDialog=t},close:e.handleClose}},[a("div",{staticClass:"select_Wapper"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"整改单号",prop:"Code"}},[a("el-input",{attrs:{value:e.form.Code,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"质检单号",prop:"Number"}},[a("el-input",{attrs:{value:e.form.Number,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-input",{attrs:{value:e.form.Check_Object_Type,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-input",{attrs:{value:e.form.Check_Type,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"总不合格数",prop:"AllUnqualified_Count"}},[a("el-input",{attrs:{value:e.form.AllUnqualified_Count,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"整改人",prop:"Rectifier_name"}},[a("el-input",{attrs:{value:e.form.Rectifier_name,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"整改时限",prop:"Rectify_Date"}},[a("el-input",{attrs:{value:e.form.Rectify_Date,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"整改状态",prop:"Status"}},[a("el-input",{attrs:{value:e.form.Status,type:"text",disabled:""}})],1)],1)],1),e.isCheck?e._e():a("div",{staticStyle:{margin:"20px 0"}},[a("el-button",{attrs:{type:"primary",disabled:!e.form.Check_Object_Type},on:{click:e.addInfo}},[e._v("添 加")]),a("el-button",{attrs:{type:"danger",disabled:0==e.selectList.length},on:{click:e.handeldelete}},[e._v("删 除 ")])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fff cs-z-tb-wrapper"},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.pageInfo.TotalCount,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.gridPageChange,gridSizeChange:e.gridSizeChange,multiSelectedChange:e.multiSelectedChange}})],1),-1!==e.rectificationState?a("div",{staticClass:"rectification-content"},[a("el-form",{ref:"form2",staticClass:"demo-form2",attrs:{model:e.form2,rules:e.rules2,"label-width":"100px"}},[2===e.rectificationState?a("el-form-item",{attrs:{label:"复核状态",prop:"IsPass"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form2.IsPass,callback:function(t){e.$set(e.form2,"IsPass",t)},expression:"form2.IsPass"}},[a("el-option",{attrs:{label:"合格",value:!0}}),a("el-option",{attrs:{label:"不合格",value:!1}})],1)],1):e._e(),a("el-form-item",{attrs:{label:1===e.rectificationState?"整改内容":"复核内容",prop:"Suggestion"}},[a("el-input",{staticStyle:{width:"70%"},attrs:{type:"textarea",rows:4,placeholder:"请输入内容"},model:{value:e.form2.Suggestion,callback:function(t){e.$set(e.form2,"Suggestion",t)},expression:"form2.Suggestion"}})],1),a("el-form-item",{attrs:{label:"上传附件",prop:"fileList2"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:"image/*","file-list":e.fileList2,multiple:"",limit:5,"on-success":function(t,a,i){e.uploadSuccess(t,a,i)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview2,"on-exceed":e.uploadExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("文件上传数量最多为5个")])])],1)],1)],1):e._e(),-1!==e.rectificationState?a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(t){return e.handleClose()}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submit()}}},[e._v("提交")])],1):e._e()]):e._e()],1)},n=[]},fd13:function(e,t,a){"use strict";a.r(t);var i=a("3302"),n=a("2a58");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("60f0");var s=a("2877"),r=Object(s["a"])(n["default"],i["a"],i["b"],!1,null,"914ca19a",null);t["default"]=r.exports}}]);