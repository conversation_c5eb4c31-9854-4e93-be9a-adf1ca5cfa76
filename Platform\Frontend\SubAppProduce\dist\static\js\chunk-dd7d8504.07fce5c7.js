(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-dd7d8504"],{1664:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),a("span",[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))]),a("el-button",{style:{marginLeft:"32px",background:t.getPercentInfo(t.unit.Is_Produced_Finish).color,color:"#FFF"},attrs:{round:"",size:"small"}},[t._v("生产完成率: "+t._s(t.getPercentInfo(t.unit.Is_Produced_Finish).value)+"%")]),a("div",{staticClass:"right-fix"},[a("el-select",{staticStyle:{width:"126px"},attrs:{size:"mini",value:"",clearable:"",placeholder:"选择排产状态",filterable:""},model:{value:t.kStatus,callback:function(e){t.kStatus=e},expression:"kStatus"}},t._l(t.states,(function(t,e){return a("el-option",{key:e,attrs:{label:t.label,value:t.label}})})),1),t._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(1)}}},[t._v("查询")])],1)],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{tableSearch:t.tableSearch,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange},scopedSlots:t._u([{key:"Is_Ready",fn:function(e){var n=e.column,i=e.row;e.$index;return[i[n.Code]?a("el-button",{attrs:{type:"success",round:"",size:"small"}},[t._v("是")]):a("el-button",{attrs:{type:"danger",round:"",size:"small"}},[t._v("否")])]}},{key:"Amount",fn:function(e){var n=e.row,i=e.column,o=e.$index;return[n[i.Code]?a("el-popover",{key:"Amount_"+o,attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.getProduceProcess(n,i)}}},[a("div",{staticClass:"tooltip-content"},[a("el-table",{staticStyle:{width:"100%"},attrs:{"header-cell-style":{backgroundColor:"#14234E",color:"#FFF"},data:t.tipDetails,"cell-style":{border:"none"}}},[a("el-table-column",{attrs:{prop:"component_type_name",label:"类别",width:"60"}}),a("el-table-column",{attrs:{prop:"total",label:"需求量",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row[e.column.property])+t._s(e.row["unit"])+" ")]}}],null,!0)}),a("el-table-column",{attrs:{prop:"complete_amount",label:"已完成",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row[e.column.property])+t._s(e.row["unit"])+" ")]}}],null,!0)}),a("el-table-column",{attrs:{prop:"percent",label:"完成率"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("el-progress",{staticStyle:{"text-direction":"rtl"},attrs:{percentage:t.row[t.column.property]}})]}}],null,!0)})],1)],1),a("el-tag",{staticStyle:{width:"92%",cursor:"pointer"},attrs:{slot:"reference",type:n[i.Code]<100?"":"success"},slot:"reference"},[t._v(t._s(n[i.Code])+"%")])],1):t._e()]}}])})],1)])])},i=[]},3873:function(t,e,a){},"9fbce":function(t,e,a){"use strict";a.r(e);var n=a("1664"),i=a("b529");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("a417");var r=a("2877"),l=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"a2a3126a",null);e["default"]=l.exports},a417:function(t,e,a){"use strict";a("3873")},b529:function(t,e,a){"use strict";a.r(e);var n=a("decd"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},decd:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var i=n(a("0f97")),o=a("6186"),r=(a("2dd9"),a("1b69")),l=n(a("b775")),s=a("ed08");e.default={name:"ProduceDetail",components:{DynamicDataTable:i.default},data:function(){return{gridCode:"pro_installunit_component_list",fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0,InstallUnit_Id:""},kStatus:"",states:[{value:"未排产",label:"未排产"},{value:"待生产",label:"待生产"},{value:"生产中",label:"生产中"},{value:"已完成",label:"已完成"}],tbConfig:{},columns:[],data:[],tipDetails:[]}},computed:{unit:function(){var t,e;return null!==(t=null===(e=this.$route.params)||void 0===e?void 0:e.row)&&void 0!==t?t:{}}},created:function(){var t=this;Promise.all([(0,r.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))]).then((function(){(0,o.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData()}))}))},methods:{tagBack:function(){(0,s.closeTagView)(this.$store,this.$route)},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120}),this.filterData.PageSize=this.tbConfig.Row_Number},setCols:function(t){var e=this;t.forEach((function(t){"project_name"===t.Code&&(t.Range=JSON.stringify(e.projects.map((function(t){return{label:t.Name,value:t.Name}}))))})),this.columns=t},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},getTableData:function(){var t=this;this.filterData.InstallUnit_Id=this.unit.InstallUnit_Id,(0,l.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{Status:this.kStatus})}).then((function(e){e.IsSucceed&&(t.filterData.TotalCount=e.Data.TotalCount,t.setGridData(e.Data))}))},tableSearch:function(t){this.fiterArrObj=t,this.filterData.Page=1,this.getTableData()},search:function(){this.filterData.Page=1,this.getTableData()},filterChange:function(t){this.filterData.Page=null!==t&&void 0!==t?t:1,this.getTableData()},gridPageChange:function(t){var e=t.page;this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig=Object.assign({},this.tbConfig,{Row_Number:e}),this.filterData.PageSize=e,this.filterChange(1)},getProduceProcess:function(t,e){},getPercentInfo:function(t){var e=Number(t),a={value:e};return e<25?a.color="#000":e<=25&&e<100?a.color="#298DFF":100==e&&(a.color="#3ECC93"),a}}}}}]);