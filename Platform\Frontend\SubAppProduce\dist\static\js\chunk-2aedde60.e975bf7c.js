(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2aedde60"],{4614:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-card",{staticClass:"box-card h100"},[n("h4",{staticClass:"topTitle"},[n("span"),e._v("基本信息")]),n("el-form",{ref:"formInline",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline}},[n("el-row",[n("el-col",{attrs:{span:20}},[n("el-row",[n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转移单编号:",prop:"Transfer_Code"}},[n("span",[e._v(e._s(e.formInline.Transfer_Code))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转移时间:",prop:"Transfer_Time"}},[n("span",[e._v(e._s(e._f("timeFormat")(e.formInline.Transfer_Time)))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转出班组:",prop:"Source_Team_Name"}},[n("span",[e._v(e._s(e.formInline.Source_Team_Name))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转出数量:",prop:"Transfer_Count"}},[n("span",[e._v(e._s(e.formInline.Transfer_Count))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"转出重量:",prop:"Transfer_Weight"}},[n("span",[e._v(e._s(e._f("filterNum")(e.formInline.Transfer_Weight))+"(t)")])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"接收工序:",prop:"Target_Process_Name"}},[n("span",[e._v(e._s(e.formInline.Target_Process_Name))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"接收班组:",prop:"Target_Team_Name "}},[n("span",[e._v(e._s(e.formInline.Target_Team_Name))])])],1)],1)],1),n("el-col",{attrs:{span:4}},[n("qrcode-vue",{attrs:{"class-name":"qrcode",size:79,value:"T="+e.formInline.Transfer_Code+"&C="+e.Tenant_Code,level:"H"}})],1)],1)],1),n("el-divider",{staticClass:"elDivder"}),n("vxe-toolbar",{ref:"xToolbar",scopedSlots:e._u([{key:"buttons",fn:function(){return[n("vxe-button",{on:{click:e.printEvent}},[e._v("打印表格")])]},proxy:!0}])}),n("div",{staticClass:"tb-x"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","print-config":e.printConfig,"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Id,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width,width:"auto"},scopedSlots:e._u(["Comp_Code"===t.Code?{key:"default",fn:function(a){var r=a.row;return[r.stopFlag?n("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),e._v(" "+e._s(r[t.Code])+" ")]}}:{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e._f("displayValue")(a[t.Code]))+" ")]}}],null,!0)})]}))],2)],1)],1)],1)},r=[]},"473b":function(e,t,n){},"6c36":function(e,t,n){"use strict";n.r(t);var a=n("4614"),r=n("942c");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("92467");var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"30e4a5de",null);t["default"]=s.exports},92467:function(e,t,n){"use strict";n("473b")},"942c":function(e,t,n){"use strict";n.r(t);var a=n("c011"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},c011:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("d81d"),n("e9f5"),n("7d54"),n("ab43"),n("b64b"),n("d3b7"),n("159b");var r=a(n("5530")),o=a(n("c14f")),i=a(n("1da1")),s=a(n("d7b0")),l=n("7f9d"),c=a(n("15ac")),f=a(n("d055")),d=n("8975"),u=a(n("6612")),m="\n        .title {\n          text-align: center;\n        }\n        .is--print{\n          box-sizing: border-box;\n          width:95% !important;\n          margin:0 auto !important;\n        }\n        .my-list-row {\n          display: inline-block;\n          width: 100%;\n          margin-left:3%;\n        }\n        .my-list-col {\n          height: 28px;\n          line-height: 28px;\n          min-width:30%;\n          display: inline-block;\n        }\n        .left{\n          flex:1;\n        }\n        .my-top {\n          display:flex;\n          font-size: 12px;\n          margin-bottom: 5px;\n        }\n        .qrcode{\n          margin-right:10px\n        }\n        ";t.default={name:"PROTransferHistoryDetail",components:{QrcodeVue:s.default},filters:{filterNum:function(e){return(0,u.default)(e).divide(1e3).format("0.[00]")}},mixins:[c.default],data:function(){var e=this;return{printConfig:{sheetName:"构件转移单",style:m,beforePrintMethod:function(t){var n=t.content;return e.topHtml+n}},topHtml:"",tbLoading:!1,queryInfo:{Page:1,PageSize:-1},tbData:[],columns:[],formInline:{},Tenant_Code:localStorage.getItem("tenant")}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return n=JSON.parse(decodeURIComponent(e.$route.query.other)),t.n=1,e.getTableConfig("PROTransferHistoryDetail");case 1:e.formInline=Object.assign({},e.formInline,n),e.isCom="com"===e.$route.query.pg_type,e.getHtml(),e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;(0,l.GetTransferDetail)((0,r.default)((0,r.default)({Transfer_Code:this.formInline.Transfer_Code},this.queryInfo),{},{Process_Type:this.isCom?2:1})).then((function(t){t.IsSucceed?(e.tbData=t.Data,e.getStopList(e.tbData)):e.$message({message:t.Message,type:"error"})}))},getStopList:function(e){var t=this;return(0,i.default)((0,o.default)().m((function n(){var a,r;return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return a="Comp_Part_Id",r=e.map((function(e){return{Id:e[a],Type:t.isCom?2:1}})),n.n=1,(0,l.GetStopList)(r).then((function(n){if(n.IsSucceed){var r={};n.Data.forEach((function(e){r[e.Id]=!!e.Is_Stop})),e.forEach((function(e){r[e[a]]&&t.$set(e,"stopFlag",r[e[a]])}))}}));case 1:return n.a(2)}}),n)})))()},getHtml:function(){var e=this;return new Promise((function(t,n){f.default.toDataURL("T=".concat(e.formInline.Transfer_Code,"&C=").concat(e.Tenant_Code)).then((function(n){var a;e.topHtml='\n        <h1 class="title">'.concat(e.printConfig.sheetName,'</h1>\n        <div class="my-top">\n          <div class="left">\n            <div class="my-list-row">\n              <div class="my-list-col">转移单编号：').concat((null===(a=e.formInline)||void 0===a?void 0:a.Transfer_Code)||"",'</div>\n              <div class="my-list-col">转移时间：').concat((0,d.timeFormat)(e.formInline.Transfer_Time||""),'</div>\n              <div class="my-list-col">转出班组：').concat(e.formInline.Source_Team_Name||"",'</div>\n            </div>\n            <div class="my-list-row">\n              <div class="my-list-col">转出数量：').concat(e.formInline.Transfer_Count||"",'</div>\n              <div class="my-list-col">转出重量：').concat(e.formInline.Transfer_Weight||"",'</div>\n              <div class="my-list-col">接收工序：').concat(e.formInline.Target_Process_Name||"",'</div>\n              <div class="my-list-col">接收班组：').concat(e.formInline.Target_Team_Name||"",'</div>\n            </div>\n          </div>\n          <div class="right">\n            <div class="qrcode">\n                    <img src="').concat(n,'">\n              </div>\n          </div>\n        </div>\n        '),t()}))}))},printEvent:function(){var e=this;this.getHtml().then((function(t){e.$refs.xTable.print({sheetName:e.printConfig.sheetName,style:m,mode:"selected",columns:e.columns.map((function(e){return{field:e.Code}})),beforePrintMethod:function(t){var n=t.content;return e.topHtml+n}})}))}}}}}]);