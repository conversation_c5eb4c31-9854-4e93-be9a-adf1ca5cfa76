(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-23e60abc"],{1663:function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("caad"),r("b0c0");var o=a(r("5530")),u=r("93aa"),n=(r("0e9a"),a(r("1d42"))),i=a(r("bad9")),l=a(r("d9e9")),d={MoneyAdjustDate:"",MoneyAdjustNo:"",PurchaseContractNo:"",InvoiceNo:"",Supplier:""};e.default={name:"AdjustAmount",components:{SelectExternal:l.default,SelectProject:i.default,bimtable:n.default},data:function(){return{searchForm:{model:(0,o.default)({},d)},tableData:[],columns:[],tableLoading:!1,gridCode:"materialInboundAdjustAmount",customParams:{},dialogVisible:!1,queryForm:{model:{ProjectId:"",Name:""}}}},computed:{materialType:function(){return["AdjustAmountRawOutbound","AdjustAmountRawInbound"].includes(this.$route.name)?"0":"1"},inboundType:function(){return["AdjustAmountRawInbound","AdjustAmountAuxInbound"].includes(this.$route.name)?"1":"0"},inboundName:function(){return["AdjustAmountRawInbound","AdjustAmountAuxInbound"].includes(this.$route.name)?"入库":"出库"}},created:function(){this.handleReset()},methods:{getClick:function(t,e){switch(t){case"view":this.viewDetail(e);break;case"export":this.exportExcel(e);break}},refresh:function(){var t;null===(t=this.$refs.table)||void 0===t||t.refresh()},handleReset:function(){this.searchForm={model:(0,o.default)({},d)},this.searchForm.model.MaterielType=this.materialType,this.searchForm.model.StoreType=this.inboundType,this.refresh()},exportExcel:function(t){var e=this;(0,u.ExportMoneyAdjustOrder)({model:{Id:t.row.Id,MaterielType:this.materialType,StoreType:this.inboundType}}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"导出成功"}),window.open(e.$baseUrl+t.Data,"_blank")):e.$message({type:"error",message:t.Message})}))},viewDetail:function(t){this.queryForm.model.Id=t.row.Id,this.queryForm.model.MaterielType=this.materialType,this.queryForm.model.StoreType=this.inboundType,this.dialogVisible=!0},resetDetailSearch:function(){this.queryForm.model.ProjectId="",this.queryForm.model.Name="",this.refreshDetail()},refreshDetail:function(){this.$refs.table2.refresh()}}}},"1f13":function(t,e,r){},"4e7a":function(t,e,r){"use strict";r.r(e);var a=r("1663"),o=r.n(a);for(var u in a)["default"].indexOf(u)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(u);e["default"]=o.a},"6f0e":function(t,e,r){"use strict";r("1f13")},"93aa":function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=E,e.AuxInStoreExport=H,e.AuxReturnByReceipt=X,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=U,e.DeleteInStore=R,e.DeletePicking=Gt,e.ExportCheckReceipt=mt,e.ExportInstoreReceipt=ct,e.ExportMoneyAdjustOrder=bt,e.ExportPicking=xt,e.ExportProcess=_t,e.ExportTestDetail=At,e.FindAuxPageList=B,e.FindRawPageList=F,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=K,e.GetAuxImportTemplate=N,e.GetAuxPageList=rt,e.GetAuxPickOutStoreSubList=_,e.GetAuxProcurementDetails=at,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=O,e.GetImportTemplate=D,e.GetInstoreDetail=P,e.GetMoneyAdjustDetailPageList=wt,e.GetOMALatestStatisticTime=k,e.GetOrderDetail=nt,e.GetPartyAs=I,e.GetPickLockStoreToChuku=Nt,e.GetPickPlate=Et,e.GetPickSelectPageList=jt,e.GetPickSelectSubList=Tt,e.GetPickingDetail=vt,e.GetPickingTypeSettingDetail=Lt,e.GetProjectListForTenant=ot,e.GetRawDetailByReceipt=it,e.GetRawOrderList=ut,e.GetRawPageList=A,e.GetRawPickOutStoreSubList=T,e.GetRawProcurementDetails=w,e.GetRawSurplusReturnStoreDetail=j,e.GetReturnPlate=$t,e.GetStoreSelectPage=kt,e.GetSuppliers=M,e.GetTestDetail=Mt,e.GetTestInStoreOrderList=Pt,e.Import=v,e.ImportCheckReceipt=pt,e.ImportInstoreReceipt=ft,e.InStoreListSummary=dt,e.LockPicking=Ft,e.ManualAuxInStoreDetail=J,e.ManualInStoreDetail=S,e.MaterielAuxInStoreList=$,e.MaterielAuxManualInStore=Y,e.MaterielAuxPurchaseInStore=Q,e.MaterielAuxSubmitInStore=q,e.MaterielPartyAInStorel=z,e.MaterielRawInStoreList=u,e.MaterielRawInStoreListInSumNew=i,e.MaterielRawInStoreListNew=n,e.MaterielRawManualInStore=x,e.MaterielRawPartyAInStore=y,e.MaterielRawPurchaseInStore=b,e.MaterielRawSubmitInStore=l,e.MaterielRawSurplusInStore=g,e.OutStoreListSummary=st,e.PartAInStoreDetail=p,e.PartyAInInStoreDetail=V,e.PurchaseAuxInStoreDetail=W,e.PurchaseInStoreDetail=f,e.RawInStoreExport=L,e.RawReturnByReceipt=lt,e.RawSurplusReturnStore=C,e.SaveInStore=G,e.SavePicking=Dt,e.SetQualified=It,e.SetTestDetail=Ot,e.StoreMoneyAdjust=ht,e.SubmitApproval=m,e.SubmitAuxApproval=c,e.SubmitInStore=St,e.SubmitPicking=gt,e.SurplusInStoreDetail=h,e.UnLockPicking=Ct,e.UpdateInvoiceInfo=yt,e.Withdraw=d,e.WithdrawAux=s,e.WithdrawChecked=Rt;var o=a(r("b775"));function u(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function n(t){return(0,o.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function P(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function M(t){return(0,o.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function A(t){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function N(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function E(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function $(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function q(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function B(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function V(t){return(0,o.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function J(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function Q(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function z(t){return(0,o.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function Y(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function H(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function K(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function X(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function rt(t){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function at(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function ot(t){return(0,o.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function ut(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function nt(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function it(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function lt(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function dt(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function st(t){return(0,o.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function ct(t){return(0,o.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function mt(t){return(0,o.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function ft(t){return(0,o.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function pt(t){return(0,o.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function St(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function ht(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function Pt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function Rt(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function Mt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function It(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function Ot(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function At(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function wt(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function bt(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function yt(t){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function xt(t){return(0,o.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function gt(t){return(0,o.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function Gt(t){return(0,o.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function kt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function Dt(t){return(0,o.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function vt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function Lt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function Ft(t){return(0,o.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function Ct(t){return(0,o.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function jt(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function Tt(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function Nt(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function Et(t){return(0,o.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function $t(t){return(0,o.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function _t(t){return(0,o.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},"9d7e2":function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.FindPickingNewSum=n,e.FindReceivingNewSum=i,e.GetAuxCostReport=l,e.GetAuxCostReportSummary=d,e.GetListForContractSetting=u,e.GetListForContractSettingForMateriel=s;var o=a(r("b775"));function u(t){return(0,o.default)({url:"/SYS/ExternalCompany/GetListForContractSetting",method:"post",data:t})}function n(t){return(0,o.default)({url:"/PRO/MaterielFlow/FindPickingNewSum",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/MaterielFlow/FindReceivingNewSum",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/MaterielReport/GetAuxCostReport",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/MaterielReport/GetAuxCostReportSummary",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/GetListForContractSettingForMateriel",method:"post",data:t})}},ce26:function(t,e,r){"use strict";r.d(e,"a",(function(){return a})),r.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container abs100"},[r("div",{staticClass:"h100 app-wrapper",attrs:{"element-loading-text":"加载中"}},[r("div",{staticClass:"cs-right"},[r("div",{ref:"searchDom",staticClass:"cs-from"},[r("div",{staticClass:"cs-search"},[r("el-form",{ref:"customParams",attrs:{inline:""}},[r("el-form-item",{attrs:{label:"单据日期",prop:"MoneyAdjustDate"}},[r("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择",clearable:""},model:{value:t.searchForm.model.MoneyAdjustDate,callback:function(e){t.$set(t.searchForm.model,"MoneyAdjustDate",e)},expression:"searchForm.model.MoneyAdjustDate"}})],1),r("el-form-item",{attrs:{label:"单据编号",prop:"MoneyAdjustNo"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:t.searchForm.model.MoneyAdjustNo,callback:function(e){t.$set(t.searchForm.model,"MoneyAdjustNo",e)},expression:"searchForm.model.MoneyAdjustNo"}})],1),r("el-form-item",{attrs:{label:"合同号",prop:"PurchaseContractNo"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:t.searchForm.model.PurchaseContractNo,callback:function(e){t.$set(t.searchForm.model,"PurchaseContractNo",e)},expression:"searchForm.model.PurchaseContractNo"}})],1),r("el-form-item",{attrs:{label:"发票号码",prop:"InvoiceNo"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:t.searchForm.model.InvoiceNo,callback:function(e){t.$set(t.searchForm.model,"InvoiceNo",e)},expression:"searchForm.model.InvoiceNo"}})],1),r("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[r("SelectExternal",{model:{value:t.searchForm.model.Supplier,callback:function(e){t.$set(t.searchForm.model,"Supplier",e)},expression:"searchForm.model.Supplier"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:t.refresh}},[t._v("搜索")]),r("el-button",{staticClass:"reset-btn",on:{click:t.handleReset}},[t._v("重置")])],1)],1)],1)]),r("div",{staticClass:"fff cs-z-tb-wrapper"},[r("div",{staticClass:"tb-container"},[r("bimtable",{ref:"table",attrs:{"case-conversion":!1,tablecode:t.gridCode,"custom-param":t.searchForm},on:{getbutton:t.getClick}})],1)])])]),t.dialogVisible?r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.inboundName+"单调整",visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[r("el-form",{staticStyle:{display:"flex","justify-content":"flex-end"},attrs:{inline:"","label-width":"80px"}},[r("el-form-item",{attrs:{label:"项目名称"}},[r("SelectProject",{model:{value:t.queryForm.model.ProjectId,callback:function(e){t.$set(t.queryForm.model,"ProjectId",e)},expression:"queryForm.model.ProjectId"}})],1),r("el-form-item",{attrs:{label:"物料名称"}},[r("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:t.queryForm.model.Name,callback:function(e){t.$set(t.queryForm.model,"Name",e)},expression:"queryForm.model.Name"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:t.refreshDetail}},[t._v("查询")]),r("el-button",{attrs:{type:"default"},on:{click:t.resetDetailSearch}},[t._v("重置")])],1)],1),r("bimtable",{ref:"table2",attrs:{"case-conversion":!1,tablecode:"adjustAmountDetail","custom-param":t.queryForm}})],1):t._e()],1)},o=[]},d4bb:function(t,e,r){"use strict";r.r(e);var a=r("ce26"),o=r("4e7a");for(var u in o)["default"].indexOf(u)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(u);r("6f0e");var n=r("2877"),i=Object(n["a"])(o["default"],a["a"],a["b"],!1,null,"aebebe42",null);e["default"]=i.exports}}]);