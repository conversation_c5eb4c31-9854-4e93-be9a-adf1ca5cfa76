(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7d793908"],{"01dc":function(e,t,s){"use strict";s("ce7c")},"0852":function(e,t,s){"use strict";s.d(t,"a",(function(){return i})),s.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-card",{staticClass:"detail-container",attrs:{shadow:"never"}},[s("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[s("div",{staticClass:"title-box"},[s("strong",{staticClass:"title"},[e._v(e._s(e.currentStep.Name||e.currentStep.Process_Name))]),s("span",{staticClass:"sub-title"},[e._v(e._s(e.currentStep.Code||e.currentStep.Process_code))])])]},proxy:!0},{key:"right",fn:function(){return[s("el-button",{on:{click:function(t){return e.$emit("refreshStep")}}},[e._v("取 消")]),e.isNewNode?e._e():s("el-button",{attrs:{type:"danger"},on:{click:e.handleDelete}},[e._v("删 除")]),s("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保 存")])]},proxy:!0}])}),s("div",{staticClass:"content"},[s("div",{staticClass:"info"},[s("div",[e._v(" 工序名称： "),s("strong",[e._v(e._s(e.currentStep.Name||e.currentStep.Process_Name))])]),s("div",[e._v(" 工序代码： "),s("strong",[e._v(e._s(e.currentStep.Code||e.currentStep.Process_code))])]),s("div",[e._v(" 协调人： "),s("strong",[e._v(e._s(e.currentStep.Coordinate_UserName))])]),s("div",[e._v(" 工序类别： "),s("strong",[e._v(e._s(e.currentStep.Type_Name||e.currentStep.Technology_Name))])])]),s("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"150px"}},[s("strong",{staticClass:"line-title"},[e._v("工序规则")]),s("el-form-item",{attrs:{label:"接收规则：",prop:"Accept_Type"}},[s("el-radio-group",{model:{value:e.form.Accept_Type,callback:function(t){e.$set(e.form,"Accept_Type",t)},expression:"form.Accept_Type"}},[s("el-radio",{attrs:{label:1}},[e._v("手动接收")])],1)],1),s("el-form-item",{attrs:{label:"分配规则：",prop:"divide"}},[s("el-checkbox-group",{model:{value:e.form.divide,callback:function(t){e.$set(e.form,"divide",t)},expression:"form.divide"}},[s("el-checkbox",{attrs:{disabled:"",label:"允许管理者分配",name:"type"}})],1)],1),e.isComTech?s("div",[s("strong",{staticClass:"line-title"},[e._v("质检要求")]),s("el-form-item",{attrs:{label:"转移前质检要求：",prop:"Quality_Test"}},[s("el-radio-group",{model:{value:e.form.Quality_Test,callback:function(t){e.$set(e.form,"Quality_Test",t)},expression:"form.Quality_Test"}},[s("el-radio",{attrs:{label:!0}},[e._v("必须质检合格")]),s("el-radio",{attrs:{label:!1}},[e._v("不做要求")])],1)],1)],1):e._e()],1),e.isComTech?s("div",[s("strong",{staticClass:"line-title"},[e._v("检查项清单")]),s("el-button",{staticClass:"check-btn",on:{click:e.handleClick}},[e._v("管理")]),e._l(e.form.Group_Items,(function(t){return s("div",{key:t.GroupId,staticClass:"show-box"},[s("i",{staticClass:"iconfont icon-file-check"}),e._v(" "+e._s(t.GroupName)+" ")])}))],2):e._e(),s("strong",{staticClass:"line-title"},[e._v("执行班组")]),e._l(e.groupList,(function(t){return s("span",{key:t.Id,staticClass:"cs-tag"},[e._v(e._s(t.Name))])}))],2)],1)},o=[]},"12fd":function(e,t,s){"use strict";var i=s("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,s("a9e3");var o=i(s("34e9")),n=s("a024");t.default={components:{TopHeader:o.default},props:{isEdit:{type:Boolean,default:!1},pageType:{type:Number,default:void 0}},data:function(){return{btnLoading:!1,form:{Code:"",Name:""},rules:{Code:[{required:!0,message:"请输入代码",trigger:"blur"},{max:30,message:"长度在 30 个字符内",trigger:"blur"}],Name:[{required:!0,message:"请输入名称",trigger:"blur"},{max:30,message:"长度在 30 个字符内",trigger:"blur"}]}}},methods:{init:function(e){var t=e.Code,s=e.Name,i=e.Type,o=e.Id;this.form={Id:o,Code:t,Name:s,Type:i}},handleDelete:function(e){var t=this;this.$confirm("是否删除该工艺, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,n.DeleteTechnology)({technologyId:t.form.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.$emit("refresh")):t.$message({message:e.Message,type:"error"}),t.$emit("close")}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){t&&(e.btnLoading=!0,e.form.Type=e.pageType,(0,n.AddTechnology)(e.form).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1})))}))}}}},"1a78":function(e,t,s){"use strict";s.r(t);var i=s("0852"),o=s("917a");for(var n in o)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return o[e]}))}(n);s("56aa");var r=s("2877"),a=Object(r["a"])(o["default"],i["a"],i["b"],!1,null,"21471b94",null);t["default"]=a.exports},2537:function(e,t,s){"use strict";var i=s("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,s("7db0"),s("e9f5"),s("f665"),s("d3b7");var o=i(s("5530")),n=s("a024"),r=s("2f62");t.default={data:function(){return{options:[],form:{value:""},btnLoading:!1,rules:{value:[{required:!0,message:"请选择工序",trigger:"change"}]}}},computed:(0,o.default)({},(0,r.mapGetters)("proProcessSetting",["currentProcess","stepDetail"])),mounted:function(){this.getList()},methods:{getList:function(){var e=this;(0,n.GetProcessList)({type:this.currentProcess.Type}).then((function(t){t.IsSucceed?e.options=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(t){e.btnLoading=!0;var s=e.options.find((function(t){return t.Id===e.form.value}));s.isNew=!0,e.$store.commit("proProcessSetting/SAVE_CURRENT_STEP",s),e.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"Process_Id",value:e.form.value}),e.$emit("close"),e.$emit("addData",s)}}))}}}},"2934f":function(e,t,s){"use strict";s.d(t,"a",(function(){return i})),s.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[s("div",{staticClass:"pg-container"},[s("process",{ref:"process",attrs:{"page-type":e.pageType},on:{btnCLick:e.processClick,itemCLick:e.processItemCLick}}),e.showStep?s("step",{ref:"step",on:{getDetail:e.getDetail,add:e.stepAdd,hiddenDetail:e.hiddenDetail}}):e._e(),e.showDetail?s("detail",{ref:"detail",on:{refreshStep:e.refreshStep}}):e._e()],1),e.dialogVisible?s("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[s(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":e.pageType,"is-edit":e.isEdit,"dialog-visible":e.dialogVisible},on:{close:e.handleClose,addData:e.addStepData,refresh:e.fetchData,refreshStep:e.refreshStep}})],1):e._e()],1)},o=[]},"2a0a":function(e,t,s){"use strict";s.r(t);var i=s("6e0f"),o=s("3168");for(var n in o)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return o[e]}))}(n);s("9d36");var r=s("2877"),a=Object(r["a"])(o["default"],i["a"],i["b"],!1,null,"ddd02796",null);t["default"]=a.exports},3168:function(e,t,s){"use strict";s.r(t);var i=s("12fd"),o=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},"33fe":function(e,t,s){"use strict";s.r(t);var i=s("e413"),o=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},"34b8":function(e,t,s){"use strict";s("8084")},"44cb":function(e,t,s){"use strict";s.r(t);var i=s("60f67"),o=s("33fe");for(var n in o)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return o[e]}))}(n);s("95480");var r=s("2877"),a=Object(r["a"])(o["default"],i["a"],i["b"],!1,null,"5a7d5e7b",null);t["default"]=a.exports},"56aa":function(e,t,s){"use strict";s("dbca")},"60f67":function(e,t,s){"use strict";s.d(t,"a",(function(){return i})),s.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-card",{staticClass:"box-card",attrs:{shadow:"never"}},[s("div",{staticClass:"title-box"},[s("strong",{staticClass:"title"},[e._v(e._s(1===e.pageType?"构件":"零件")+"工艺")]),s("el-button",{attrs:{icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.handleClick({type:"add",pageType:e.pageType})}}},[e._v("新增")])],1),s("div",{staticClass:"content-box"},[s("el-menu",{staticClass:"el-menu-vertical-demo"},e._l(e.itemList,(function(t,i){return s("el-menu-item",{key:i,staticClass:"c-fx",attrs:{index:i.toString()},on:{click:function(s){return e.itemClick(t)}}},[s("div",{staticClass:"icon-box"},[s("i",{staticClass:"iconfont icon-steel"})]),s("span",{staticClass:"title-content",attrs:{slot:"title"},slot:"title"},[s("div",{staticClass:"content"},[s("div",{staticClass:"content-title"},[e._v(" "+e._s(t.Name)+" ")]),s("div",{staticClass:"sub-title"},[e._v(" "+e._s(t.Code)+" ")])]),s("div",{staticClass:"third"},[s("i",{staticClass:"el-icon-edit-outline",on:{click:function(s){return s.stopPropagation(),e.handleClick({type:"edit",data:t,pageType:e.pageType})}}})])])])})),1)],1)])},o=[]},"68cc":function(e,t,s){},"6a2c":function(e,t,s){"use strict";s.d(t,"a",(function(){return i})),s.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-card",{staticClass:"process-container",attrs:{shadow:"never"}},[s("div",{staticClass:"top-header"},[s("div",[s("strong",{staticClass:"title"},[e._v(e._s(e.currentProcess.Name))]),s("div",{staticClass:"sub-title"},[e._v(" "+e._s(e.currentProcess.Code)+" ")])]),s("div",[s("el-button",{attrs:{disabled:e.disabledAddBtnClick,icon:"el-icon-plus"},on:{click:function(t){return e.handleAdd("insert")}}},[e._v("新增工序")])],1)]),s("div",{staticClass:"content"},[e._l(e.list,(function(t,i){return s("div",{key:i,staticClass:"btn-box "},[s("span",{class:["custom-button bg-blue",{active:t.isChecked,"is-finish":t.Is_Last}],on:{click:function(s){return s.stopPropagation(),e.handleClick(t)}}},[s("div",{staticClass:"btn-title"},[e._v(e._s(t.Process_Name||t.Name))]),s("div",{staticClass:"btn-sub-title"},[e._v(e._s(t.Process_code||t.Code))])])])})),s("div",{staticClass:"btn-add-box",on:{click:function(t){return t.stopPropagation(),e.handleAdd("add")}}},[s("div",{staticClass:"btn-title"},[e._v("新增工序")])])],2)])},o=[]},"6aa3":function(e,t,s){"use strict";var i=s("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(s("5530")),n=s("2f62");t.default={computed:(0,o.default)((0,o.default)({},(0,n.mapGetters)("proProcessSetting",["currentProcess"])),{},{showStep:function(){return!!this.currentProcess.Id}}),beforeDestroy:function(){this.$store.dispatch("proProcessSetting/saveCurrentProcess",{}),this.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL_NULL",{}),this.$store.commit("proProcessSetting/SAVE_CURRENT_STEP",{})},methods:{processItemCLick:function(e){this.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL_NULL",{}),this.$store.dispatch("proProcessSetting/saveCurrentProcess",e)},hiddenDetail:function(){this.showDetail=!1},getDetail:function(){var e=this;this.showDetail=!0,this.$nextTick((function(t){e.$refs.detail.init()}))},fetchData:function(){this.$refs.process.fetchData()},refreshStep:function(){this.$refs.step.fetchData()},addStepData:function(e){this.$refs.step.addList(e)},handleClose:function(){this.dialogVisible=!1},stepAdd:function(){this.currentComponent="AddStep",this.title="新增工序",this.dialogVisible=!0},processClick:function(e){var t=this,s=e.type,i=e.data,o=e.pageType;this.currentComponent="Add",this.isEdit="edit"===s,this.dialogVisible=!0,"add"===s?this.title=1===o?"新增构件工艺":"新增零件工艺":(this.title=1===o?"编辑构件工艺":"编辑零件工艺",this.$nextTick((function(e){t.$refs.content.init(i)})))}}}},"6e0f":function(e,t,s){"use strict";s.d(t,"a",(function(){return i})),s.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[s("el-form-item",{attrs:{label:"名称",prop:"Name"}},[s("el-input",{attrs:{"show-word-limit":"",placeholder:"最多30个字",maxlength:30},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),s("el-form-item",{attrs:{label:"代码",prop:"Code"}},[s("el-input",{attrs:{"show-word-limit":"",maxlength:30,placeholder:"字母+数字，30字符"},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),s("el-form-item",[s("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[e.isEdit?s("el-button",{staticClass:"block btn-del txt-red",attrs:{type:"text"},on:{click:function(t){return e.handleDelete()}}},[e._v("删除工艺")]):e._e()]},proxy:!0},{key:"right",fn:function(){return[s("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),s("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])]},proxy:!0}])})],1)],1)],1)},o=[]},7549:function(e,t,s){"use strict";var i=s("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(s("44cb")),n=i(s("b4d8")),r=i(s("1a78")),a=i(s("2a0a")),c=i(s("cd56")),u=i(s("6aa3"));t.default={name:"PROProcessPartsTech",components:{Step:n.default,Process:o.default,Detail:r.default,AddStep:c.default,Add:a.default},mixins:[u.default],data:function(){return{isEdit:!1,showDetail:!1,pageType:2,width:"40%",processType:"",currentComponent:"",title:"",dialogVisible:!1}}}},"78fe":function(e,t,s){"use strict";var i=s("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,s("d81d"),s("14d9"),s("e9f5"),s("ab43"),s("a732"),s("d3b7");var o=i(s("5530")),n=i(s("34e9")),r=s("2f62"),a=s("a024"),c="按负荷自动分配",u="允许管理者分配";t.default={components:{TopHeader:n.default},props:{isComTech:{type:Boolean,default:!1},formList:{type:Array,default:function(){return[]}}},data:function(){return{groupList:[],isNewNode:!1,form:{Accept_Type:1,Is_Must_Do:void 0,Is_Last:void 0,divide:[u],Quality_Test:!1,Group_Items:[]},rules:{Accept_Type:[{required:!0,message:"请选择接收规则",trigger:"change"}],divide:[{type:"array",required:!0,message:"请至少选择一个分配规则",trigger:"change"}],Quality_Test:[{required:!0,message:"请选择转移前质检要求",trigger:"change"}],Is_Must_Do:[{required:!0,message:"请选择是否必做",trigger:"change"}],Is_Last:[{required:!0,message:"请选择是否是否统计为已完成",trigger:"change"}]}}},watch:{"form.Is_Last":function(e){this.$store.commit("proProcessSetting/CHANGE_CURRENT_STEP",{key:"Is_Last",value:e})},formList:function(e){this.form.Group_Items=e}},computed:(0,o.default)({},(0,r.mapGetters)("proProcessSetting",["currentProcess","currentStep","submitForm","stepDetail"])),methods:{init:function(){this.getGroup();var e=this.currentStep,t=e.Accept_Type,s=e.Automatic_Allocation,i=e.Manager_Allocation,o=e.Is_Must_Do,n=e.Quality_Test,r=e.Is_Last,a=e.Group_Items,l=e.isNew;this.form.Accept_Type=t||this.form.Accept_Type,this.form.Is_Must_Do=o,this.form.Is_Last=r,this.form.Quality_Test=n||this.form.Quality_Test,this.form.Group_Items=a,s&&-1===this.form.divide.indexOf(c)&&this.form.divide.push(c),i&&-1===this.form.divide.indexOf(u)&&this.form.divide.push(u),this.isNewNode=l},handleClick:function(){this.$emit("check",this.form.Group_Items)},getGroup:function(){var e=this;(0,a.GetWorkingTeamBase)({processId:this.stepDetail.Process_Id}).then((function(t){t.IsSucceed?e.groupList=t.Data:e.$message({message:t.Message,type:"error"})}))},handleDelete:function(){var e=this;this.$confirm("是否删除该工序?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,a.DeleteProcessFlow)({flowId:e.stepDetail.Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.$emit("refreshStep")):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;var s=Object.assign({},e.form);s.Automatic_Allocation=s.divide.some((function(e){return e===c})),s.Manager_Allocation=s.divide.some((function(e){return e===u})),s.Technology_Id=e.currentProcess.Id,delete s.divide,delete e.stepDetail.index,!e.stepDetail.Id&&delete e.stepDetail.Id,e.isComTech&&(s.Check_Items=e.formList.map((function(e){return e.GroupId}))),(0,a.AddProcessFlow)((0,o.default)((0,o.default)({},s),e.stepDetail)).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.$emit("refreshStep")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))}}}},"7b15":function(e,t,s){"use strict";s.r(t);var i=s("2934f"),o=s("ecab");for(var n in o)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return o[e]}))}(n);s("34b8");var r=s("2877"),a=Object(r["a"])(o["default"],i["a"],i["b"],!1,null,"026e5b75",null);t["default"]=a.exports},8084:function(e,t,s){},"917a":function(e,t,s){"use strict";s.r(t);var i=s("78fe"),o=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},95480:function(e,t,s){"use strict";s("68cc")},"9d36":function(e,t,s){"use strict";s("add0")},a024:function(e,t,s){"use strict";var i=s("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=O,t.AddTechnology=c,t.AddWorkingProcess=a,t.DelLib=w,t.DeleteProcess=L,t.DeleteProcessFlow=T,t.DeleteTechnology=P,t.DeleteWorkingTeams=G,t.GetAllProcessList=f,t.GetCheckGroupList=E,t.GetChildComponentTypeList=N,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=x,t.GetFactoryWorkingTeam=v,t.GetGroupItemsList=y,t.GetLibList=r,t.GetLibListType=R,t.GetProcessFlow=p,t.GetProcessFlowListWithTechnology=h,t.GetProcessList=l,t.GetProcessListBase=d,t.GetProcessListTeamBase=D,t.GetProcessListWithUserBase=A,t.GetProcessWorkingTeamBase=W,t.GetTeamListByUser=M,t.GetTeamProcessList=b,t.GetWorkingTeam=_,t.GetWorkingTeamBase=I,t.GetWorkingTeamInfo=$,t.GetWorkingTeams=C,t.GetWorkingTeamsPageList=k,t.SaveWorkingTeams=S,t.UpdateProcessTeam=g;var o=i(s("b775")),n=i(s("4328"));function r(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function a(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:n.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:n.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:n.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:n.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:n.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:n.default.stringify(e)})}function m(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:n.default.stringify(e)})}function h(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:n.default.stringify(e)})}function v(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function _(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:n.default.stringify(e)})}function b(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:n.default.stringify(e)})}function y(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:n.default.stringify(e)})}function T(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:n.default.stringify(e)})}function P(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:n.default.stringify(e)})}function L(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:n.default.stringify(e)})}function I(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:n.default.stringify(e)})}function D(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:n.default.stringify(e)})}function A(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a6bc:function(e,t,s){},a806:function(e,t,s){"use strict";s.d(t,"a",(function(){return i})),s.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("div",{staticClass:"container"},[s("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[s("el-form-item",{attrs:{label:"选择工序",prop:"value"}},[s("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.value,callback:function(t){e.$set(e.form,"value",t)},expression:"form.value"}},e._l(e.options,(function(e){return s("el-option",{key:e.Id,style:{color:e.Working_Team_Ids.length?"#333333":"red"},attrs:{label:e.Name,value:e.Id}})})),1)],1)],1)],1),e._m(0),s("div",{staticStyle:{"text-align":"right"}},[s("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),s("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)])},o=[function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"tip"},[e._v("注："),s("span",{staticStyle:{color:"red"}},[e._v("红色")]),e._v("工序表示未关联班组，请先在工序设置中关联班组。")])}]},adb8:function(e,t,s){"use strict";s("a6bc")},add0:function(e,t,s){},b4d8:function(e,t,s){"use strict";s.r(t);var i=s("6a2c"),o=s("f8be");for(var n in o)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return o[e]}))}(n);s("01dc");var r=s("2877"),a=Object(r["a"])(o["default"],i["a"],i["b"],!1,null,"26e0f787",null);t["default"]=a.exports},cd56:function(e,t,s){"use strict";s.r(t);var i=s("a806"),o=s("efdc");for(var n in o)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return o[e]}))}(n);s("adb8");var r=s("2877"),a=Object(r["a"])(o["default"],i["a"],i["b"],!1,null,"4247afcc",null);t["default"]=a.exports},ce7c:function(e,t,s){},dbca:function(e,t,s){},e413:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,s("a9e3");var i=s("a024");t.default={props:{pageType:{type:Number,default:1}},data:function(){return{itemList:[]}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var e=this;(0,i.GetLibList)({type:this.pageType}).then((function(t){t.IsSucceed?e.itemList=t.Data:e.$message({message:t.Message,type:"error"})}))},getIndex:function(e){return e},handleClick:function(e){this.$emit("btnCLick",e)},itemClick:function(e){this.$emit("itemCLick",e)}}}},ecab:function(e,t,s){"use strict";s.r(t);var i=s("7549"),o=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},efdc:function(e,t,s){"use strict";s.r(t);var i=s("2537"),o=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},f86c:function(e,t,s){"use strict";var i=s("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,s("d81d"),s("a434"),s("e9f5"),s("d866"),s("7d54"),s("ab43"),s("a732"),s("d3b7"),s("159b");var o=i(s("5530")),n=s("2f62"),r=s("a024");t.default={data:function(){return{list:[]}},computed:(0,o.default)((0,o.default)({},(0,n.mapGetters)("proProcessSetting",["currentProcess","stepDetail"])),{},{disabledAddBtnClick:function(){return!this.list.some((function(e){return e.isChecked}))}}),watch:{currentProcess:{handler:function(e,t){this.fetchData()},deep:!0,immediate:!0},list:{handler:function(e,t){e.every((function(e){return!e.isChecked}))&&this.$emit("hiddenDetail")},deep:!0}},methods:{fetchData:function(){var e=this;this.currentProcess&&(0,r.GetProcessFlow)({technologyId:this.currentProcess.Id}).then((function(t){t.IsSucceed?(e.list=t.Data.map((function(e,t){return e.isChecked=!1,e})),e.resetData()):e.$message({message:t.Message,type:"error"})}))},handleAdd:function(e){"add"===e&&(this.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"Step",value:this.list.length+1}),this.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"index",value:this.list.length+1})),this.$emit("add",e)},addList:function(e){this.$set(e,"Is_Last",!1),this.list.splice(this.stepDetail.index,0,e),this.handleClick(e)},handleClick:function(e){var t=this;this.list.forEach((function(s,i){s===e?(t.$set(s,"isChecked",!0),t.$store.commit("proProcessSetting/SAVE_CURRENT_STEP",e),t.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"Process_Id",value:e.Process_Id||e.Id}),e.isNew?(t.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"Id",value:null}),t.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"Step",value:i+1})):(t.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"Id",value:e.Id}),t.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"Step",value:e.Step})),t.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL",{key:"index",value:i+1})):t.$set(s,"isChecked",!1)})),this.$emit("getDetail")},resetData:function(){this.$store.commit("proProcessSetting/SAVE_CURRENT_STEP",{}),this.$store.commit("proProcessSetting/CHANGE_STEP_DETAIL_NULL",{})}}}},f8be:function(e,t,s){"use strict";s.r(t);var i=s("f86c"),o=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a}}]);