(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-410063f1"],{"0b1b":function(e,t,a){"use strict";a.r(t);var o=a("dd557"),n=a.n(o);for(var s in o)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(s);t["default"]=n.a},1127:function(e,t,a){},1374:function(e,t,a){},"13e2":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"cs-header"},[a("qrcode-vue",{attrs:{size:e.size,value:e.infos.Bill_Number,"class-name":"qrcode",level:"H"}}),a("el-form",[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"下料任务单编号："}},[a("strong",{staticClass:"cs-blue"},[e._v(e._s(e.infos.Bill_Code))])])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工序："}},[e._v(" "+e._s(e.infos.Working_Process_Name)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务单唯一码： "}},[a("strong",{staticClass:"cs-blue"},[e._v(e._s(e.infos.Bill_Number))])])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"班组："}},[e._v(" "+e._s(e.infos.Working_Team_Name)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"涉及订单："}},[e._v(" "+e._s(e.InstallUnit)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"制表： "}},[e._v(" "+e._s(e.infos.Create_UserName)+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:" 下达日期："}},[e._v(" "+e._s(e._f("timeFormat")(e.infos.Create_Date))+" ")])],1)],1)],1)],1),a("strong",{staticClass:"tb-title"},[e._v("零件列表")]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff mtp20  cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",attrs:{"select-width":70,columns:e.columns,config:e.tbConfig,data:e.infos.Details,border:"","sum-values":e.sums,stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1),a("div",{staticClass:"mtp20"},[a("strong",[e._v("附件下载：")]),e._l(e.infos.File_Infos,(function(t){return a("a",{key:t.Id,staticClass:"mr-20 cs-blue",attrs:{href:e.getUrl(t)}},[e._v(e._s(t.File_Name))])}))],2),a("div",{staticClass:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("打 印")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleClose}},[e._v("关 闭")])],1)])},n=[]},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var o=a("6186"),n=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(s){(0,o.GetGridByCode)({code:e,IsAll:a}).then((function(e){var o=e.IsSucceed,r=e.Data,i=e.Message;if(o){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,r.Grid),l=a?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+r.Grid.Row_Number||n.tablePageSize[0]),s(t.columns)}else t.$message({message:i,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,o=e.type;this.queryInfo.Page="limit"===o?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var o=0;o<this.columns.length;o++){var n=this.columns[o];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"30c0":function(e,t,a){"use strict";a("1127")},"4e82":function(e,t,a){"use strict";var o=a("23e7"),n=a("e330"),s=a("59ed"),r=a("7b0b"),i=a("07fa"),l=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),g=a("3f7e"),h=a("99f4"),m=a("1212"),p=a("ea83"),b=[],y=n(b.sort),P=n(b.push),v=c((function(){b.sort(void 0)})),T=c((function(){b.sort(null)})),L=f("sort"),_=!c((function(){if(m)return m<70;if(!(g&&g>3)){if(h)return!0;if(p)return p<603;var e,t,a,o,n="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(o=0;o<47;o++)b.push({k:t+o,v:a})}for(b.sort((function(e,t){return t.v-e.v})),o=0;o<b.length;o++)t=b[o].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}})),G=v||!T||!L||!_,C=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};o({target:"Array",proto:!0,forced:G},{sort:function(e){void 0!==e&&s(e);var t=r(this);if(_)return void 0===e?y(t):y(t,e);var a,o,n=[],u=i(t);for(o=0;o<u;o++)o in t&&P(n,t[o]);d(n,C(e)),a=i(n),o=0;while(o<a)t[o]=n[o++];while(o<u)l(t,o++);return t}})},"6c7a":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-select",{staticStyle:{width:"120px","margin-right":"10px"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getGroup},model:{value:e.processId,callback:function(t){e.processId=t},expression:"processId"}},e._l(e.processList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1),a("el-select",{staticStyle:{width:"120px"},attrs:{disabled:!e.processId,placeholder:"请选择"},on:{change:e.fetchData},model:{value:e.groupId,callback:function(t){e.groupId=t},expression:"groupId"}},e._l(e.groupOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)]},proxy:!0},{key:"right",fn:function(){return[a("el-button",{attrs:{icon:"el-icon-printer"},on:{click:e.handlePrint}},[e._v("打印")])]},proxy:!0}])}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1),e.dialogVisible?a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"60%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":e.selectList},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e()],1)])},n=[]},"6d8d":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("5530")),s=o(a("c14f")),r=o(a("1da1")),i=o(a("34e9")),l=o(a("99a8")),u=o(a("0f97")),c=o(a("15ac")),d=a("7f9d"),f=a("a024");t.default={name:"PROTaskOrder",components:{TopHeader:i.default,Detail:l.default,DynamicDataTable:u.default},mixins:[c.default],data:function(){return{value:"",title:"",processId:"",options:[],currentComponent:"",selectList:[],dialogVisible:!1,tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],tbData:[],groupId:"",projects:[],processList:[],groupOption:[],total:0,tbLoading:!1,getPartType:void 0}},mounted:function(){var e=this;return(0,r.default)((0,s.default)().m((function t(){return(0,s.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("GetNestingBillBoardPageList");case 1:e.getProcessList();case 2:return t.a(2)}}),t)})))()},methods:{getProcessList:function(){var e=this;(0,f.GetProcessList)().then((function(t){t.IsSucceed?(e.processList=t.Data,e.processList.length>0&&(e.processId=e.processList[0].Id,e.getGroup())):e.$message({message:t.Message,type:"error"})}))},getGroup:function(){var e=this;(0,f.GetWorkingTeamBase)({processId:this.processId}).then((function(t){t.IsSucceed?(e.groupOption=t.Data,e.groupOption.length>0&&(e.groupId=e.groupOption[0].Id,e.fetchData())):e.$message({message:t.Message,type:"error"})}))},fetchData:function(){var e=this;this.tbLoading=!0,(0,d.GetNestingBillBoardPageList)((0,n.default)((0,n.default)({},this.queryInfo),{},{Process_Id:this.processId,Working_Team_Id:this.groupId})).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleDetail:function(e){var t=this;this.currentComponent="Detail",this.title="任务单详情",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs.content.init(e)}))},handlePrint:function(){},handleClose:function(){this.dialogVisible=!1}}}},"99a8":function(e,t,a){"use strict";a.r(t);var o=a("13e2"),n=a("0b1b");for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);a("30c0");var r=a("2877"),i=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"2a77bfed",null);t["default"]=i.exports},"9f93":function(e,t,a){"use strict";a("1374")},a024:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=B,t.AddTechnology=l,t.AddWorkingProcess=i,t.DelLib=F,t.DeleteProcess=_,t.DeleteProcessFlow=T,t.DeleteTechnology=L,t.DeleteWorkingTeams=O,t.GetAllProcessList=f,t.GetCheckGroupList=W,t.GetChildComponentTypeList=A,t.GetFactoryAllProcessList=g,t.GetFactoryPeoplelist=S,t.GetFactoryWorkingTeam=b,t.GetGroupItemsList=v,t.GetLibList=r,t.GetLibListType=x,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=m,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=R,t.GetProcessListWithUserBase=w,t.GetProcessWorkingTeamBase=N,t.GetTeamListByUser=$,t.GetTeamProcessList=P,t.GetWorkingTeam=y,t.GetWorkingTeamBase=D,t.GetWorkingTeamInfo=k,t.GetWorkingTeams=G,t.GetWorkingTeamsPageList=C,t.SaveWorkingTeams=I,t.UpdateProcessTeam=p;var n=o(a("b775")),s=o(a("4328"));function r(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:s.default.stringify(e)})}function l(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:s.default.stringify(e)})}function u(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:s.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:s.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:s.default.stringify(e)})}function f(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:s.default.stringify(e)})}function g(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:s.default.stringify(e)})}function m(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:s.default.stringify(e)})}function b(){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:s.default.stringify(e)})}function P(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:s.default.stringify(e)})}function v(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:s.default.stringify(e)})}function T(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:s.default.stringify(e)})}function L(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:s.default.stringify(e)})}function _(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:s.default.stringify(e)})}function D(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:s.default.stringify(e)})}function R(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:s.default.stringify(e)})}function w(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function B(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a1aa:function(e,t,a){"use strict";a.r(t);var o=a("6c7a"),n=a("d023");for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);a("9f93");var r=a("2877"),i=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"969c32f0",null);t["default"]=i.exports},d023:function(e,t,a){"use strict";a.r(t);var o=a("6d8d"),n=a.n(o);for(var s in o)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(s);t["default"]=n.a},dd557:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("13d5"),a("e9f5"),a("7d54"),a("9485"),a("d3b7"),a("159b");var n=o(a("d7b0")),s=a("7f9d"),r=o(a("0f97")),i=o(a("15ac")),l=a("ed08");t.default={name:"Detail",components:{QrcodeVue:n.default,DynamicDataTable:r.default},mixins:[i.default],data:function(){return{size:100,infos:{},tbLoading:!1,tbConfig:{Pager_Align:"center"},columns:[],tbData:[],queryInfo:{Page:1,PageSize:10,ParameterJson:[]},InstallUnit:"",sums:["计"]}},mounted:function(){this.getTableConfig("GetNestingTaskInfoDetail")},methods:{init:function(e){this.InstallUnit=e.InstallUnit,this.fetchData(e)},fetchData:function(e){var t=this;this.tbLoading=!0,(0,s.GetNestingTaskInfoDetail)({billCode:e.Bill_Code}).then((function(e){e.IsSucceed?(t.infos=e.Data,t.tbLoading=!1,t.$nextTick((function(e){t.getTotal()}))):(t.$message({message:e.Message,type:"error"}),t.tbLoading=!1)}))},handleClose:function(){this.$emit("close")},getUrl:function(e){return(0,l.combineURL)(this.$baseUrl,e.Upload_Url)},getTotal:function(){var e=this,t=this.$refs.dyTable.$refs.dtable.columns;t.forEach((function(t,a){if(0===a)e.sums[0]="合计";else if("count"===t.property){var o=e.infos.Details.reduce((function(a,o){return e.highPrecisionAdd(a,o[t.property])}),0);e.$set(e.sums,a,o)}else e.$set(e.sums,a,"")}))}}}}}]);