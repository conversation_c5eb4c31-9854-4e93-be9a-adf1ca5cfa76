(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3c068ec4"],{"1b6d":function(t,e,n){"use strict";n.r(e);var a=n("4f98"),o=n("210c");for(var c in o)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(c);var u=n("2877"),r=Object(u["a"])(o["default"],a["a"],a["b"],!1,null,"1b11b463",null);e["default"]=r.exports},"210c":function(t,e,n){"use strict";n.r(e);var a=n("40e8"),o=n.n(a);for(var c in a)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(c);e["default"]=o.a},"40e8":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0");var o=a(n("8a9e"));e.default={name:"PROReturnGoodsAdd",components:{AddPage:o.default},data:function(){return{tbData:[],total:0}},computed:{name:function(){return this.$route.name}},methods:{fetchData:function(){}}}},"4f98":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("add-page",{ref:"main",attrs:{name:t.name,"type-page":"goods","tb-config-code":"pro_return_detail_list","tb-data":t.tbData,total:t.total},on:{"update:tbData":function(e){t.tbData=e},"update:tb-data":function(e){t.tbData=e},fetchData:t.fetchData}})},o=[]},abfb:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportCancelStockInfo=m,e.GetInStockPageList=i,e.GetStockCancelDetailList=d,e.GetStockCancelDetailPageList=r,e.GetStockCancelDocEntity=l,e.GetStockCancelDocPageList=u,e.RemoveDetail=s,e.RemoveMain=p,e.SaveComponentStockCancel=f;var o=a(n("b775")),c=a(n("4328"));function u(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDocPageList",method:"post",data:t})}function r(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDetailPageList",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDetailList",method:"post",data:c.default.stringify(t)})}function l(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDocEntity",method:"post",data:c.default.stringify(t)})}function i(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/GetInStockPageList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/SaveComponentStockCancel",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/RemoveDetail",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/RemoveMain",method:"post",data:c.default.stringify(t)})}function m(t){return(0,o.default)({url:"/PRO/ComponentStockCancel/ExportCancelStockInfo",method:"post",data:c.default.stringify(t)})}}}]);