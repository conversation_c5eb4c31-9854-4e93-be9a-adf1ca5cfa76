(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-89b0cc72"],{"05e0":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.StatisticalDateW=e.ProjectStatusW=e.ProjectNumberW=e.ProjectAbbreviationW=void 0;e.ProjectAbbreviationW=140,e.ProjectNumberW=120,e.ProjectStatusW=100,e.StatisticalDateW=120},"060e":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-tabs",{on:{"tab-click":t.changeTab},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.factoryOption,(function(t){return a("el-tab-pane",{key:t.Id,attrs:{label:t.Short_Name,name:t.Id}})})),1),a("div",{staticClass:"search-x"},[a("label",[t._v("统计时间： "),a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"month","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.staticTime,callback:function(e){t.staticTime=e},expression:"staticTime"}})],1),a("el-button",{attrs:{type:"primary",disabled:t.loading},on:{click:function(e){return t.fetchData()}}},[t._v("搜 索")])],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("label",[t._v("数据列表")]),t.showExport?a("el-button",{staticClass:"mr-10",attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.staticTime+"代购代付表")}}},[t._v("导出报表")]):t._e()],1),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},n=[]},"5726a":function(t,e,a){"use strict";a("f2b81")},7098:function(t,e,a){"use strict";a.r(e);var i=a("9fdf"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"9a77":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.curModuleKey=void 0;e.curModuleKey="purchase"},"9fdf":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("14d9"),a("b0c0"),a("d3b7"),a("3ca3"),a("ddb0");var n=i(a("5530")),r=i(a("c14f")),o=i(a("1da1")),u=i(a("ac03")),c=i(a("2082")),l=i(a("3502")),s=a("2f62"),d=a("7757"),h=a("9a77"),f=a("b60f"),m=a("05e0"),p=i(a("6612"));e.default={name:"OMAPurchaseIndex",components:{VTable:l.default},mixins:[u.default,c.default],data:function(){return{addPageArray:[],factoryOption:[],staticTime:"",showExport:!1,activeName:""}},beforeCreate:function(){this.curModuleKey=h.curModuleKey},mounted:function(){var t=this;return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.loading=!0,t.$store.commit("oma/INIT",t.curModuleKey),t.$store.commit("oma/SAVE_KEY",t.curModuleKey),e.n=1,t.saveRoles({key:t.curModuleKey,value:t.$route.meta.Id});case 1:return t.initRoute(),e.n=2,t.init();case 2:t.fetchData();case 3:return e.a(2)}}),e)})))()},methods:(0,n.default)((0,n.default)({},(0,s.mapActions)({saveRoles:"oma/saveRoles",saveFactoryId:"oma/saveFactoryId",saveOriginDate:"oma/saveOriginDate"})),{},{init:function(){var t=this;return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.loading=!0,e.n=1,t.getFactory();case 1:return t.factoryOption=e.v,t.factoryOption.length&&(t.activeName=t.factoryOption[0].Id,t.saveFactoryId({key:t.curModuleKey,value:t.activeName})),e.n=2,t.getDate();case 2:t.staticTime=e.v,t.saveOriginDate({key:t.curModuleKey,value:t.staticTime}),t.showExport=t.getRoles("OMAPurchaseExport");case 3:return e.a(2)}}),e)})))()},initRoute:function(){(this.getRoles("OMAPurchaseView")||this.getRoles("OMAPurchaseCheck"))&&this.addPageArray.push({path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-b147502a").then(a.bind(null,"a62a"))},name:"OMAPurchaseDetail",meta:{title:"项目汇总",noCache:!0}});var t=[{path:this.$route.path+"/mInfo",hidden:!0,component:function(){return a.e("chunk-01369c3d").then(a.bind(null,"6bb8"))},name:"OMAPurchaseOutputDetailInfo",meta:{title:"产值明细",noCache:!0}},{path:this.$route.path+"/costInfo",hidden:!0,component:function(){return a.e("chunk-a000775c").then(a.bind(null,"3361"))},name:"OMAPurchaseCostDetailInfo",meta:{title:"成本明细",noCache:!0}},{path:this.$route.path+"/feeInfo",hidden:!0,component:function(){return a.e("chunk-c012ebd0").then(a.bind(null,"ee9f"))},name:"OMAPurchaseFeeDetailInfo",meta:{title:"费用明细",noCache:!0}}];this.getRoles("OMAPurchaseDetailOutput")&&this.addPageArray.push(t[0]),this.getRoles("OMAPurchaseDetailCost")&&this.addPageArray.push(t[1]),this.getRoles("OMAPurchaseDetailFee")&&this.addPageArray.push(t[2]),this.addPageArray.length&&this.initPage(this.$route.name)},fetchData:function(){var t=this;this.checkDate(this.staticTime)&&(this.loading=!0,(0,f.GetDDSummaryList)({StatisticalDate:this.staticTime,FactoryId:this.activeName}).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[];var a=t.setTotalData(t.tableData,t.generateColumn(),!1),i=a.column;t.columns=i,t.$refs["tb"].setColumns(i)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},getDate:function(){var t=this;return(0,o.default)((0,r.default)().m((function e(){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,d.GetReportLastDate)({FactoryId:t.activeName});case 1:if(a=e.v,!a.IsSucceed){e.n=2;break}return e.a(2,a.Data);case 2:t.$message({message:a.Message,type:"error"});case 3:return e.a(2)}}),e)})))()},changeTab:function(t){this.saveFactoryId({key:this.curModuleKey,value:t.name}),this.fetchData()},handleCheck:function(t){this.$router.push({name:"OMAPurchaseDetail",query:{pg_redirect:this.$route.name,type:"check",d:t.Statics_Date}})},handleView:function(t){this.$router.push({name:"OMAPurchaseDetail",query:{pg_redirect:this.$route.name,type:"view",d:t.Statics_Date}})},showRed:function(t,e){var a=+(0,p.default)(t).format("0.[00]"),i=+(0,p.default)(e).format("0.[00]");return a>i},generateColumn:function(){var t=this,e=this.$createElement,a=200;return[{title:"统计日期",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{field:"Statics_Date",minWidth:m.StatisticalDateW,title:"当月合计",formatter:["formatDateTime","yyyy-MM-dd"]}]}]},{title:"产值(元)",params:{bg:"bg-green"},children:[{title:"代购主辅材产值(元)",children:[{minWidth:a,field:"Material_Output",title:0,isTotal:!0}]},{title:"代付费用产值(元)",children:[{minWidth:a,field:"Fee_Output",title:0,isTotal:!0}]},{title:"修正值(元)",children:[{minWidth:a,field:"Fix_Value",title:0,isTotal:!0}]},{title:"小计(元)",children:[{isTotal:!0,minWidth:a,field:"Output_SubTotal",title:0}]}]},{params:{bg:"bg-cyan"},title:"成本(元)",children:[{title:"主材成本(元)",children:[{minWidth:a,field:"Material_Cost",title:0,isTotal:!0}]}]},{title:"费用(元)",params:{bg:"bg-blue"},children:[{title:"项目辅材(元)",children:[{minWidth:a,field:"Aux_Fee",title:0,isTotal:!0}]},{title:"代付费用(元)",children:[{minWidth:a,field:"Pay_For_Other_Fee",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Fee_SubTotal",title:0,isTotal:!0}]}]},{title:"利润(元)",params:{bg:"bg-purple"},children:[{title:"产值-成本-费用",children:[{minWidth:a,field:"Profit",title:0,isTotal:!0}]}]},{title:"代收代付",params:{bg:"bg-yellow"},children:[{title:"代收款(元)",children:[{minWidth:a,field:"Agency_Fund",title:0,isTotal:!0}]},{title:"代开票(元)",children:[{minWidth:a,field:"Agency_Invoicing",title:0,isTotal:!0}]},{title:"代付款(元)",children:[{minWidth:a,field:"Agency_Payment",title:0,isTotal:!0,slots:{default:function(a){var i=a.row,n="";return n=t.showRed(i.Agency_Payment,i.Agency_Fund)?e("span",{class:"txt-red"},[i.Agency_Payment]):e("span",[i.Agency_Payment]),[n]}}}]},{title:"代收票(元)",children:[{minWidth:a,field:"Agency_Ticket",title:0,isTotal:!0}]}]},{field:"AccountingStatus",title:"核算状态(元)",minWidth:a,slots:{default:function(t){var a=t.row;return[e("el-tag",{attrs:{type:1===a.AccountingStatus?"danger":2===a.AccountingStatus?"warning":3===a.AccountingStatus?"success":""}},[1===a.AccountingStatus?"未核算":2===a.AccountingStatus?"待确认":3===a.AccountingStatus?"已核算":""])]}}},{title:"操作",params:{empty:!0},visible:!0,fixed:"right",minWidth:a,slots:{default:function(a){var i=a.row,n=a.column,r=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleView(i)}}},["查看"]),o=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleCheck(i)}}},["审核"]),u=[];return t.getRoles("OMAPurchaseView")&&u.push(r),2===i.AccountingStatus&&t.getRoles("OMAPurchaseCheck")&&u.push(o),u.length&&(n.params.empty=!1),u}}}]}})}},b60f:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetDDCostDailyDetailList=d,e.GetDDFeesDailyDetailList=h,e.GetDDOutputDailyDetailList=s,e.GetDDProjectSummaryList=l,e.GetDDSummaryList=o,e.ReviewStockControlSummary=c,e.SubmitDDAccounting=u;var n=i(a("b775")),r="";function o(t){return(0,n.default)({url:r+"/oma/ddreport/GetDDSummaryList",method:"post",data:t})}function u(t){return(0,n.default)({url:r+"/oma/ddreport/SubmitDDAccounting",method:"post",data:t})}function c(t){return(0,n.default)({url:r+"/oma/ddreport/ReviewStockControlSummary",method:"post",data:t})}function l(t){return(0,n.default)({url:r+"/oma/ddreport/GetDDProjectSummaryList",method:"post",data:t})}function s(t){return(0,n.default)({url:r+"/oma/ddreport/GetDDOutputDailyDetailList",method:"post",data:t})}function d(t){return(0,n.default)({url:r+"/oma/ddreport/GetDDCostDailyDetailList",method:"post",data:t})}function h(t){return(0,n.default)({url:r+"/oma/ddreport/GetDDFeesDailyDetailList",method:"post",data:t})}},f0fb:function(t,e,a){"use strict";a.r(e);var i=a("060e"),n=a("7098");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("5726a");var o=a("2877"),u=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"22e45488",null);e["default"]=u.exports},f2b81:function(t,e,a){}}]);