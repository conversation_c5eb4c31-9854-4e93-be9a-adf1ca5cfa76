(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6e36a5d9"],{1890:function(t,e,a){"use strict";var l=a("dbce").default,n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("159b");var r=a("472f"),s=n(a("bd80")),o=l(a("32fd"));e.default={name:"PlanProcess",components:{ProcessTask:s.default},data:function(){return{tableData:[],columns:[{prop:"Label",label:"任务名称",width:0,align:"left"},{prop:"Plan_Start_Date",label:"计划开始",width:180},{prop:"Plan_End_Date",label:"计划完成",width:180},{prop:"Actual_Start_Date",label:"实际开始",width:180},{prop:"Actual_End_Date",label:"实际完成",width:180},{prop:"Actual_Progress",label:"完成百分比",width:120}],planOpened:[],keyword:"",filters:[{key:"Label",value:""}],dialogShow:!1,dialogCfgs:{}}},computed:{filteredData:function(){var t=this.filterDataByFilters();return t}},watch:{keyword:function(t){}},created:function(){this.loadTaskList()},methods:{loadTaskList:function(){var t=this;return(0,r.GetTaskInputList)().then((function(e){e.IsSucceed&&(t.tableData=e.Data,t.planOpened=e.Data.map((function(t){return t.Plan.Id})))}))},togglePlan:function(t){this.planOpened.indexOf(t)<0?this.planOpened.push(t):this.planOpened=this.planOpened.filter((function(e){return e!=t}))},findParentWBS:function(t,e){for(var a,l=0;l<e.length;l++){if(e[l].Id===t){a=e[l];break}a=this.findParentWBS(t,e[l].Children)}return a},findParent:function(t,e){for(var a,l=0;l<t.length;l++){if(t[l].Id===e){a=t[l];break}if(t[l].Children&&t[l].Children.length>0&&(a=this.findParent(e,t[l].Children),a))break}return a},processTask:function(t,e){var a,l,n,r=null!==(a=null===(l=this.tableData.find((function(e){return e.Plan.Id===t.Id})))||void 0===l?void 0:l.Tasks)&&void 0!==a?a:[],s=null!==(n=this.findParent(r,e.Parent_Task_Id))&&void 0!==n?n:{};o.openDialog({title:"".concat("project"===e.Task_Type?"WBS":"milestone"===e.Task_Type?"里程碑":"作业","进度填报"),width:"750px",component:"ProcessTask",props:{plan:t,parent:s,task:e}},this)},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.type,a=t.data;this.dialogCancel(),this[e]&&"[object Function]"===Object.prototype.toString.call(this[e])&&this[e](a)},saveProcess:function(t){var e=this;(0,r.SaveTaskInput)(t).then((function(t){t.IsSucceed&&e.loadTaskList()}))},search:function(){this.filters[0].value=this.keyword,this.$nextTick((function(){var t=document.querySelector("span.match");t&&t.scrollIntoViewIfNeeded()}))},filterDataByFilters:function(){return this.tableData},deepFilter:function(t,e){e.forEach((function(t){!t.Children||t.Children.length}))},toDateStr:function(t){return o.toDateStr(t)}}}},"29e3":function(t,e,a){},"327a":function(t,e,a){"use strict";var l=a("dbce").default,n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("5530"));a("e9f5"),a("7d54"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("25f0"),a("159b");var s=l(a("c1df")),o=l(a("32fd"));e.default={name:"ProcessTask",props:{task:{type:Object,default:function(){return{}}},plan:{type:Object,default:function(){return{}}},parent:{type:Object,default:function(){return{}}}},data:function(){return{form:{Data_Date:new Date,Actual_End_Date:""}}},watch:{"form.Actual_End_Date":function(t){Boolean(t)?(this.form.Actual_Progress=100,"milestone"===this.form.Task_Type&&(this.form.Actual_Start_Date=t)):"milestone"===this.form.Task_Type&&(this.form.Actual_Start_Date=null,this.form.Actual_Progress=0),this.$nextTick()},"form.Actual_Progress":function(t){var e;100!==Number(t)||null!==(e=this.form)&&void 0!==e&&e.Actual_End_Date||(this.form.Actual_End_Date=new Date)}},created:function(){this.form=(0,r.default)((0,r.default)((0,r.default)({},this.form),this.task),{},{Actual_Progress:(100*this.task.Actual_Progress).toFixed(2)})},methods:{submit:function(){var t=(0,r.default)((0,r.default)({},this.form),{},{Actual_Progress:this.form.Actual_Progress/100});Object.keys(t).forEach((function(e){o.DATE_FIELDS.indexOf(e)>-1&&t[e]&&(t[e]=s(t[e]).format("YYYY-MM-DD"))})),this.$emit("dialogFormSubmitSuccess",{type:"saveProcess",data:t})},toDateStr:function(t){return t?s(t).startOf("date").format("YYYY-MM-DD"):"-"}}}},4310:function(t,e,a){"use strict";a.d(e,"a",(function(){return l})),a.d(e,"b",(function(){return n}));var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-top":"-12px",padding:"12px"}},[a("div",{staticClass:"baseinfo"},[a("span",{staticClass:"flag"},[t._v(t._s("project"===t.task.Task_Type?"WBS":"milestone"===t.task.Task_Type?"里程碑":"作业"))]),a("dl",[a("dt",[a("i",{staticClass:"el-icon-link",staticStyle:{color:"#37a1e7"}}),t._v(" "+t._s(t.task.Name)+" ")]),a("dd",[t._v("所属计划："+t._s(t.plan.Name))]),a("dd",[t._v("上级节点："+t._s(t.parent?t.parent.Label:""))]),"task"===t.task.Task_Type?[a("dd",{staticClass:"lay-2"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[t._v("计划开始："),a("strong",[t._v(t._s(t.toDateStr(t.task.Plan_Start_Date)))])]),a("el-col",{attrs:{span:12}},[t._v("工期："),a("strong",[t._v(t._s(t.task.Plan_Duration)+"天")])])],1)],1),a("dd",{staticClass:"lay-2"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[t._v("计划完成："),a("strong",[t._v(t._s(t.toDateStr(t.task.Plan_End_Date)))])]),a("el-col",{attrs:{span:12}},[t._v("计划人工资源："),a("strong",[t._v(t._s(t.task.Plan_Resources)+"人/天")])])],1)],1)]:t._e(),"project"===t.task.Task_Type?[a("dd",{staticClass:"lay-2"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[t._v("运营计划开始："),a("strong",[t._v(t._s(t.toDateStr(t.task.Control_Plan_Start_Date)))])]),a("el-col",{attrs:{span:8}},[t._v("计划开始："),a("strong",[t._v(t._s(t.toDateStr(t.task.Plan_Start_Date)))])]),a("el-col",{attrs:{span:8}},[t._v("实际开始："),a("strong",[t._v(t._s(t.toDateStr(t.task.Actual_Start_Date)))])])],1)],1),a("dd",{staticClass:"lay-2"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[t._v("运营计划完成："),a("strong",[t._v(t._s(t.toDateStr(t.task.Control_Plan_End_Date)))])]),a("el-col",{attrs:{span:8}},[t._v("计划完成："),a("strong",[t._v(t._s(t.toDateStr(t.task.Plan_End_Date)))])]),a("el-col",{attrs:{span:8}},[t._v("实际完成："),a("strong",[t._v(t._s(t.toDateStr(t.task.Actual_End_Date)))])])],1)],1)]:t._e(),"milestone"===t.task.Task_Type?[a("dd",{staticClass:"lay-2"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[t._v("计划完成："),a("strong",[t._v(t._s(t.toDateStr(t.task.Plan_End_Date)))])]),a("el-col",{attrs:{span:12}},[t._v("实际完成："),a("strong",[t._v(t._s(t.toDateStr(t.task.Actual_End_Date)))])])],1)],1)]:t._e()],2)]),a("el-form",{staticClass:"p-form",attrs:{"label-width":"100px"}},["task"===t.task.Task_Type?[a("el-row",{attrs:{gutter:0}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际开始"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:t.form.Actual_Start_Date,callback:function(e){t.$set(t.form,"Actual_Start_Date",e)},expression:"form.Actual_Start_Date"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"完成百分比"}},[a("el-input",{attrs:{type:"number",placeholder:"请输入内容"},model:{value:t.form.Actual_Progress,callback:function(e){t.$set(t.form,"Actual_Progress",e)},expression:"form.Actual_Progress"}},[a("template",{slot:"append"},[t._v("%")])],2)],1)],1)],1),a("el-row",{attrs:{gutter:0}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际完成"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:t.form.Actual_End_Date,callback:function(e){t.$set(t.form,"Actual_End_Date",e)},expression:"form.Actual_End_Date"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际人工资源"}},[a("el-input",{attrs:{placeholder:"请输入内容"},model:{value:t.form.Actual_Resources,callback:function(e){t.$set(t.form,"Actual_Resources",e)},expression:"form.Actual_Resources"}},[a("template",{slot:"append"},[t._v("人/天")])],2)],1)],1)],1)]:t._e(),"milestone"===t.task.Task_Type?[a("el-row",{attrs:{gutter:0}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"实际完成"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:t.form.Actual_End_Date,callback:function(e){t.$set(t.form,"Actual_End_Date",e)},expression:"form.Actual_End_Date"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"完成百分比"}},[a("span",[t._v(t._s(t.form.Actual_Progress)+"%")])])],1)],1)]:t._e(),"project"===t.task.Task_Type?[a("el-form-item",{attrs:{label:"存在问题"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"300","show-word-limit":""},model:{value:t.form.Existing_Problems,callback:function(e){t.$set(t.form,"Existing_Problems",e)},expression:"form.Existing_Problems"}})],1),a("el-form-item",{attrs:{label:"解决措施"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"300","show-word-limit":""},model:{value:t.form.Solutions,callback:function(e){t.$set(t.form,"Solutions",e)},expression:"form.Solutions"}})],1),a("el-form-item",{attrs:{label:"需协调事项"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"300","show-word-limit":""},model:{value:t.form.Need_Coordinate,callback:function(e){t.$set(t.form,"Need_Coordinate",e)},expression:"form.Need_Coordinate"}})],1),a("el-form-item",{attrs:{label:"需要协调部门"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"300","show-word-limit":""},model:{value:t.form.Coordinate_Department,callback:function(e){t.$set(t.form,"Coordinate_Department",e)},expression:"form.Coordinate_Department"}})],1)]:t._e(),"task"===t.task.Task_Type?[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",maxlength:"300","show-word-limit":""},model:{value:t.form.Remark,callback:function(e){t.$set(t.form,"Remark",e)},expression:"form.Remark"}})],1)]:t._e(),a("el-form-item",{attrs:{align:"right"}},[a("el-button",{on:{click:function(e){return t.$emit("dialogCancel")}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确定")])],1)],2)],1)},n=[]},"472f":function(t,e,a){"use strict";var l=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CONSTRAINT_TYPES=void 0,e.ChangePlanStatus=_,e.CheckNeedApprovePlan=D,e.CopyPlan=p,e.DeletePlan=f,e.GetExtention=k,e.GetPlanEntity=u,e.GetPlanEntityReadOnly=S,e.GetPlanFieldSetting=d,e.GetPlanList=r,e.GetPlanResouseReport=g,e.GetPlanUpdateData=b,e.GetPlanUpdateList=v,e.GetTaskInputList=m,e.GetTaskProgressList=i,e.GetUserListByObjId=s,e.LINK_TYPES=void 0,e.SavePlan=o,e.SavePlanFieldSetting=c,e.SaveTaskInput=h,e.SendTaskInputMessage=P;var n=l(a("b775"));e.CONSTRAINT_TYPES=[{value:"asap",label:"越早越好"},{value:"alap",label:"越晚越好"},{value:"mso",label:"必须开始于..."},{value:"mfo",label:"必须完成于..."},{value:"snet",label:"不得早于...开始"},{value:"snlt",label:"不得晚于...开始"},{value:"fnet",label:"不得早于...完成"},{value:"fnlt",label:"不得晚于...完成"}],e.LINK_TYPES=[{label:"FS",value:0},{label:"SS",value:1},{label:"FF",value:2},{label:"SF",value:3}];function r(t){return(0,n.default)({url:"/Plan/Plan/GetPlanList",method:"post",data:t})}function s(t,e){return(0,n.default)({url:"/EPC/Project/GetUserListByObjId?workObjId="+t,method:"post",data:e})}function o(t,e){return(0,n.default)({url:"/Plan/Plan/SavePlan",method:"post",params:t,data:e})}function i(t){return(0,n.default)({url:"/Plan/Task/GetTaskProgressList",method:"post",data:t})}function u(t){return(0,n.default)({url:"/Plan/Plan/GetPlanEntity",method:"post",params:{plan_id:t}})}function d(t,e){return(0,n.default)({url:"/Plan/Plan/GetPlanFieldSetting",method:"post",params:{plan_id:t,is_default:e}})}function c(t){return(0,n.default)({url:"/Plan/Plan/SavePlanFieldSetting",method:"post",data:t})}function f(t){return(0,n.default)({url:"/Plan/Plan/DeletePlan",method:"post",params:{plan_id:t}})}function p(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return(0,n.default)({url:"/Plan/Plan/CopyPlan",method:"post",params:{plan_id:t,newPlanName:e,newPlanCode:a}})}function _(t,e){return(0,n.default)({url:"/Plan/Plan/ChangePlanStatus",method:"post",params:{plan_id:t,status:e}})}function m(){return(0,n.default)({url:"/Plan/Task/GetTaskInputList",method:"post",data:{}})}function h(t){return(0,n.default)({url:"/Plan/Task/SaveTaskInput",method:"post",data:t})}function v(t,e,a){return(0,n.default)({url:"/Plan/Plan/GetPlanUpdateList",method:"post",params:{plan_id:t,begin_date:e,end_date:a}})}function b(t){return(0,n.default)({url:"/Plan/Plan/GetPlanUpdateData",method:"post",params:{history_id:t}})}function P(t){return(0,n.default)({url:"/Plan/Plan/SendTaskInputMessage",method:"post",data:t})}function k(){return(0,n.default)({url:"/Plan/Plan/GetExtention",method:"post"})}function g(t,e){return(0,n.default)({url:"/Plan/Plan/GetPlanResouseReport",method:"post",params:{plan_id:t,type:e}})}function D(){return(0,n.default)({url:"/Plan/Plan/CheckNeedApprovePlan",method:"post"})}function S(t,e){return(0,n.default)({url:"/Plan/Plan/GetPlanEntityReadOnly",method:"post",params:{plan_id:t,access_token:e}})}},"6d17":function(t,e,a){},"90d7":function(t,e,a){"use strict";a.r(e);var l=a("e046"),n=a("cefb");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("a5be");var s=a("2877"),o=Object(s["a"])(n["default"],l["a"],l["b"],!1,null,null,null);e["default"]=o.exports},a5be:function(t,e,a){"use strict";a("29e3")},bd80:function(t,e,a){"use strict";a.r(e);var l=a("4310"),n=a("c2e7");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("f845");var s=a("2877"),o=Object(s["a"])(n["default"],l["a"],l["b"],!1,null,"68597526",null);e["default"]=o.exports},c2e7:function(t,e,a){"use strict";a.r(e);var l=a("327a"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return l[t]}))}(r);e["default"]=n.a},cefb:function(t,e,a){"use strict";a.r(e);var l=a("1890"),n=a.n(l);for(var r in l)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return l[t]}))}(r);e["default"]=n.a},e046:function(t,e,a){"use strict";a.d(e,"a",(function(){return l})),a.d(e,"b",(function(){return n}));var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 plan-process"},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("header",{staticClass:"p-header"},[a("div",{staticClass:"square-icon"},[a("i",{staticClass:"iconfont icon-edit-file"})]),a("h3",[a("span",[t._v("进度填报")])]),a("el-input",{staticStyle:{width:"240px","flex-shrink":"0"},attrs:{placeholder:"请输入内容"},on:{blur:t.search},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.search},slot:"append"})],1)],1),a("el-table",{staticClass:"empty-tb-header",staticStyle:{width:"100%","margin-bottom":"20px"},attrs:{data:[]}},[t._l(t.columns,(function(t){return[a("el-table-column",{key:t.prop,attrs:{prop:t.prop,label:t.label,width:t.width,align:t.align?t.align:"center"}})]})),a("el-table-column",{attrs:{label:"操作",width:120,align:"center"}})],2)],1),t._l(t.filteredData,(function(e){return[a("div",{key:e.Plan.Id,staticClass:"plan-table-wrap"},[a("header",[a("i",{staticClass:"iconfont icon-gantt"}),a("h3",[t._v(" "+t._s(e.Plan.Name)+" ")]),a("i",{class:{"el-icon-arrow-down":t.planOpened.indexOf(e.Plan.Id)>-1,"el-icon-arrow-up":t.planOpened.indexOf(e.Plan.Id)<0},staticStyle:{cursor:"pointer"},on:{click:function(a){return t.togglePlan(e.Plan.Id)}}})]),a("transition",{attrs:{name:"el-fade-in-linear"}},[a("el-table",{directives:[{name:"show",rawName:"v-show",value:t.planOpened.indexOf(e.Plan.Id)>-1,expression:"planOpened.indexOf(plan.Plan.Id) > -1"}],ref:"dtable",refInFor:!0,attrs:{data:e.Tasks,"show-header":!1,"row-key":"Id","default-expand-all":"","tree-props":{children:"Children"}}},[t._l(t.columns,(function(e){return[a("el-table-column",{key:e.prop,attrs:{prop:e.prop,label:e.label,width:e.width,align:e.align?e.align:"center"},scopedSlots:t._u([{key:"default",fn:function(l){var n=l.row;return["Label"===e.prop?a("span",{class:{match:n["Label"].indexOf(t.filters[0].value)&&n["Label"].indexOf(t.filters[0].value)>-1},attrs:{id:"anchor-"+n.Id}},[t._v(" "+t._s(n["Label"])+" ")]):t._e(),"Plan_Start_Date"===e.prop?a("span",[t._v(" "+t._s(t.toDateStr(n.Data["Plan_Start_Date"]))+" ")]):t._e(),"Plan_End_Date"===e.prop?a("span",[t._v(" "+t._s(t.toDateStr(n.Data["Plan_End_Date"]))+" ")]):t._e(),"Actual_Start_Date"===e.prop?a("span",[t._v(" "+t._s(t.toDateStr(n.Data["Actual_Start_Date"]))+" ")]):t._e(),"Actual_End_Date"===e.prop?a("span",[t._v(" "+t._s(t.toDateStr(n.Data["Actual_End_Date"]))+" ")]):t._e(),"Actual_Progress"===e.prop?a("span",[t._v(" "+t._s(Number(100*n.Data["Actual_Progress"]).toFixed(2)+"%")+" ")]):t._e()]}}],null,!0)})]})),a("el-table-column",{attrs:{label:"操作",width:120,align:"center"},scopedSlots:t._u([{key:"default",fn:function(l){var n=l.row;return[a("el-button",{attrs:{size:"mini",type:"project"===n.Data.Task_Type?"success":"milestone"===n.Data.Task_Type?"warning":"primary"},on:{click:function(a){return t.processTask(e.Plan,n.Data)}}},[t._v("填报")])]}}],null,!0)})],2)],1)],1)]}))],2),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",attrs:{name:t.dialogCfgs.title},on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},n=[]},f845:function(t,e,a){"use strict";a("6d17")}}]);