(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5a3f2354"],{"15ca":function(t,e,r){"use strict";r.d(e,"a",(function(){return a})),r.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"container-box"},[r("BimForm",{ref:"bimform",attrs:{"form-items":t.formItems,"default-label-width":"auto"},on:{search:t.search,reset:t.search},model:{value:t.form,callback:function(e){t.form=e},expression:"form"}}),r("el-row",{staticStyle:{display:"flex"}},[r("el-button",{attrs:{type:"primary"},on:{click:t.toAdd}},[t._v("新增")]),t.showExport?r("el-button",{attrs:{type:"default",disabled:0===t.ids.length},on:{click:t.handelExport}},[t._v("导出")]):t._e(),r("div",{staticStyle:{"margin-left":"auto"}},[r("span",{attrs:{id:"dynamic-table-fields"}})])],1),r("bimtable",{ref:"table",staticStyle:{"margin-top":"16px"},attrs:{"case-conversion":!1,tablecode:t.gridCode,"custom-param":t.form,"dynamic-table-fields-element-id":"dynamic-table-fields"},on:{getbutton:t.getClick,"get-selection-data":t.changeSelection}})],1)},o=[]},"1a6e":function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("caad"),r("d81d"),r("14d9"),r("b0c0"),r("e9f5"),r("ab43"),r("d3b7"),r("ac1f"),r("3ca3"),r("841c"),r("ddb0");var o=a(r("2082")),n=a(r("1d42")),u=a(r("447e")),i=a(r("bad9")),l=a(r("14a1")),d=a(r("e989")),s=r("93aa"),c=r("7f9d");e.default={name:"MaterialPickList",components:{BimForm:u.default,bimtable:n.default,SelectProject:i.default,SelectProcess:l.default,SelectTeam:d.default},mixins:[o.default],data:function(){return{showExport:["scyth_pz","scyth_test","ussl","scyth_v4"].includes(localStorage.getItem("TenantId")),form:{MaterialType:0,SysProjectId:"",PickingProcessId:"",PickingTeamId:"",PickingNo:"",Status:""},gridCode:"materialPickList",addPageArray:[{path:this.$route.path+"/material/pick/add",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-0f7e2416")]).then(r.bind(null,"4fac"))},name:"AddMaterialPickList",meta:{title:"新增领料单",activeMenu:"/produce/PRO/MaterialPickList"},query:{pg_redirect:this.$route.name}},{path:this.$route.path+"/material/pickAux/add",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-0f7e2416")]).then(r.bind(null,"4fac"))},name:"AddMaterialPickAuxList",meta:{title:"新增领料单",activeMenu:"/produce/PRO/MaterialAuxPickList"},query:{pg_redirect:this.$route.name}},{path:"/material/pick/view/:id",hidden:!0,component:function(){return r.e("chunk-0882172f").then(r.bind(null,"825f"))},name:"ViewMaterialPickList",meta:{title:"查看领料单"},query:{pg_redirect:this.$route.name}},{path:"/material/pick/edit/:id",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-0f7e2416")]).then(r.bind(null,"4fac"))},name:"EditMaterialPickList",meta:{title:"编辑领料单"},query:{pg_redirect:this.$route.name}},{path:"/material/pick/lock/:id",hidden:!0,component:function(){return r.e("chunk-0882172f").then(r.bind(null,"825f"))},name:"LockMaterialPickList",meta:{title:"锁定库存"},query:{pg_redirect:this.$route.name}}],ids:[],formItems:[{label:"项目名称",prop:"SysProjectId",type:"component",component:i.default},{label:"领用工序",prop:"PickingProcessId",type:"component",component:l.default},{label:"领用班组",prop:"PickingTeamId",type:"component",component:d.default},{label:"状态",prop:"Status",type:"select",options:[{label:"草稿",value:0},{label:"未完成",value:1},{label:"已完成",value:2}]},{label:"单号",prop:"PickingNo",type:"input"}]}},computed:{isRaw:function(){return"PROMaterialPickList"===this.$route.name},materialType:function(){return"PROMaterialPickList"===this.$route.name?0:1}},watch:{materialType:{handler:function(t){this.form.MaterialType=t},immediate:!0}},methods:{getClick:function(t,e){var r=this,a=e.row;"view"===t&&this.$router.push({name:"ViewMaterialPickList",params:{id:a.Id},query:{type:this.materialType}}),"edit"===t&&this.$router.push({name:"EditMaterialPickList",params:{id:a.Id},query:{type:this.materialType}}),"lock"===t&&this.$router.push({name:"LockMaterialPickList",params:{id:a.Id},query:{type:this.materialType}}),"back"===t&&(0,c.WithdrawPicking)({PickingId:a.Id}).then((function(t){t.IsSucceed?(r.$message({message:"撤回成功",type:"success"}),r.search()):r.$message({message:t.Message,type:"error"})})),"submit"===t&&(0,s.SubmitPicking)({PickingId:a.Id}).then((function(t){t.IsSucceed?(r.$message({message:"提交成功",type:"success"}),r.search()):r.$message({message:t.Message,type:"error"})})),"delete"===t&&this.$confirm("确定删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.DeletePicking)({PickingId:a.Id}).then((function(t){t.IsSucceed?(r.$message({message:"删除成功",type:"success"}),r.search()):r.$message({message:t.Message,type:"error"})}))}))},changeSelection:function(t){this.ids=t.map((function(t){return t.Id}))},handleReset:function(){this.$refs.bimform.handleReset()},search:function(){this.$refs.table.search()},handelExport:function(){var t=this;(0,s.ExportPicking)({materialType:this.form.MaterialType,sendId:this.ids[0]}).then((function(e){e.IsSucceed?window.open(t.$baseUrl+e.Data):t.$message({message:e.Message,type:"error"})}))},toAdd:function(){this.$router.push({name:this.isRaw?"AddMaterialPickList":"AddMaterialPickAuxList",query:{pg_redirect:this.$route.name,type:this.materialType}})}}}},"836a":function(t,e,r){},"860c":function(t,e,r){"use strict";r.r(e);var a=r("1a6e"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},"93aa":function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=_,e.AuxInStoreExport=K,e.AuxReturnByReceipt=Y,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=U,e.DeleteInStore=S,e.DeletePicking=bt,e.ExportCheckReceipt=ft,e.ExportInstoreReceipt=ct,e.ExportMoneyAdjustOrder=Gt,e.ExportPicking=kt,e.ExportProcess=jt,e.ExportTestDetail=Ot,e.FindAuxPageList=N,e.FindRawPageList=v,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=X,e.GetAuxImportTemplate=F,e.GetAuxPageList=rt,e.GetAuxPickOutStoreSubList=j,e.GetAuxProcurementDetails=at,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=L,e.GetImportTemplate=A,e.GetInstoreDetail=R,e.GetMoneyAdjustDetailPageList=Mt,e.GetOMALatestStatisticTime=w,e.GetOrderDetail=ut,e.GetPartyAs=y,e.GetPickLockStoreToChuku=Ft,e.GetPickPlate=_t,e.GetPickSelectPageList=Wt,e.GetPickSelectSubList=Bt,e.GetPickingDetail=xt,e.GetPickingTypeSettingDetail=Dt,e.GetProjectListForTenant=ot,e.GetRawDetailByReceipt=it,e.GetRawOrderList=nt,e.GetRawPageList=O,e.GetRawPickOutStoreSubList=B,e.GetRawProcurementDetails=M,e.GetRawSurplusReturnStoreDetail=W,e.GetReturnPlate=Et,e.GetStoreSelectPage=wt,e.GetSuppliers=g,e.GetTestDetail=gt,e.GetTestInStoreOrderList=Rt,e.Import=x,e.ImportCheckReceipt=pt,e.ImportInstoreReceipt=mt,e.InStoreListSummary=dt,e.LockPicking=vt,e.ManualAuxInStoreDetail=Q,e.ManualInStoreDetail=P,e.MaterielAuxInStoreList=E,e.MaterielAuxManualInStore=H,e.MaterielAuxPurchaseInStore=V,e.MaterielAuxSubmitInStore=$,e.MaterielPartyAInStorel=z,e.MaterielRawInStoreList=n,e.MaterielRawInStoreListInSumNew=i,e.MaterielRawInStoreListNew=u,e.MaterielRawManualInStore=k,e.MaterielRawPartyAInStore=I,e.MaterielRawPurchaseInStore=G,e.MaterielRawSubmitInStore=l,e.MaterielRawSurplusInStore=T,e.OutStoreListSummary=st,e.PartAInStoreDetail=p,e.PartyAInInStoreDetail=J,e.PurchaseAuxInStoreDetail=q,e.PurchaseInStoreDetail=m,e.RawInStoreExport=D,e.RawReturnByReceipt=lt,e.RawSurplusReturnStore=C,e.SaveInStore=b,e.SavePicking=At,e.SetQualified=yt,e.SetTestDetail=Lt,e.StoreMoneyAdjust=ht,e.SubmitApproval=f,e.SubmitAuxApproval=c,e.SubmitInStore=Pt,e.SubmitPicking=Tt,e.SurplusInStoreDetail=h,e.UnLockPicking=Ct,e.UpdateInvoiceInfo=It,e.Withdraw=d,e.WithdrawAux=s,e.WithdrawChecked=St;var o=a(r("b775"));function n(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function u(t){return(0,o.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function R(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function M(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function A(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function B(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function E(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function $(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function N(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function q(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function J(t){return(0,o.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function Q(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function V(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function z(t){return(0,o.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function H(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function K(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function X(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function Y(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function rt(t){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function at(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function ot(t){return(0,o.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function nt(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function ut(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function it(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function lt(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function dt(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function st(t){return(0,o.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function ct(t){return(0,o.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function ft(t){return(0,o.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function mt(t){return(0,o.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function pt(t){return(0,o.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function Pt(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function ht(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function Rt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function St(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function gt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function yt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function Lt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function Ot(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function Mt(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function Gt(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function It(t){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function kt(t){return(0,o.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function Tt(t){return(0,o.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function bt(t){return(0,o.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function wt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function At(t){return(0,o.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function xt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function Dt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function vt(t){return(0,o.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function Ct(t){return(0,o.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function Wt(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function Bt(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function Ft(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function _t(t){return(0,o.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function Et(t){return(0,o.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function jt(t){return(0,o.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},a024:function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProcessFlow=d,e.AddProessLib=C,e.AddTechnology=l,e.AddWorkingProcess=i,e.DelLib=B,e.DeleteProcess=M,e.DeleteProcessFlow=L,e.DeleteTechnology=O,e.DeleteWorkingTeams=T,e.GetAllProcessList=f,e.GetCheckGroupList=v,e.GetChildComponentTypeList=W,e.GetFactoryAllProcessList=m,e.GetFactoryPeoplelist=D,e.GetFactoryWorkingTeam=R,e.GetGroupItemsList=y,e.GetLibList=u,e.GetLibListType=F,e.GetProcessFlow=p,e.GetProcessFlowListWithTechnology=P,e.GetProcessList=s,e.GetProcessListBase=c,e.GetProcessListTeamBase=A,e.GetProcessListWithUserBase=x,e.GetProcessWorkingTeamBase=_,e.GetTeamListByUser=E,e.GetTeamProcessList=g,e.GetWorkingTeam=S,e.GetWorkingTeamBase=w,e.GetWorkingTeamInfo=b,e.GetWorkingTeams=G,e.GetWorkingTeamsPageList=I,e.SaveWorkingTeams=k,e.UpdateProcessTeam=h;var o=a(r("b775")),n=a(r("4328"));function u(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:n.default.stringify(t)})}function l(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:n.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:n.default.stringify(t)})}function s(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:n.default.stringify(t)})}function c(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:n.default.stringify(t)})}function f(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:n.default.stringify(t)})}function m(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:n.default.stringify(t)})}function P(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:n.default.stringify(t)})}function R(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function S(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:n.default.stringify(t)})}function g(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:n.default.stringify(t)})}function y(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:n.default.stringify(t)})}function L(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:n.default.stringify(t)})}function O(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:n.default.stringify(t)})}function M(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:n.default.stringify(t)})}function w(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:n.default.stringify(t)})}function A(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:n.default.stringify(t)})}function x(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:t})}function B(t){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:t})}function E(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}},ac6b:function(t,e,r){"use strict";var a=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportTeamProcessingTask=P,e.FindMatBillSumPageList=h,e.GetAreaPageList=d,e.GetCompanyFactoryPageList=c,e.GetEntities=i,e.GetFactoryPageList=s,e.GetGetMonomerList=l,e.GetMatBillSumSubList=R,e.GetProcessingProgress=n,e.GetProcessingProgressTask=u,e.GetSummaryTeamProcessingTask=p,e.GetTeamProcessingTask=f,e.GetWorkingTeams=m;var o=a(r("b775"));function n(t){return(0,o.default)({url:"/PRO/ProductionReport/GetProcessingProgress",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ProductionReport/GetProcessingProgressTask",method:"post",data:t})}function i(t){return(0,o.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_projects/GetEntities"),method:"post",data:t})}function l(t){return(0,o.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetGetMonomerList"),method:"post",data:t})}function d(t){return(0,o.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetAreaPageList"),method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Factory/GetFactoryPageList",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Factory/GetCompanyFactoryPageList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/ProductionReport/GetTeamProcessingTask",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/ProductionReport/GetWorkingTeams",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ProductionReport/GetSummaryTeamProcessingTask",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/ProductionReport/ExportTeamProcessingTask",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/ProductionReport/FindMatBillSumPageList",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/ProductionReport/GetMatBillSumSubList",method:"post",data:t})}},cd35:function(t,e,r){"use strict";r.r(e);var a=r("15ca"),o=r("860c");for(var n in o)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(n);r("dd8a");var u=r("2877"),i=Object(u["a"])(o["default"],a["a"],a["b"],!1,null,"0fbc6125",null);e["default"]=i.exports},dd8a:function(t,e,r){"use strict";r("836a")}}]);