(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ebb14442"],{"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var o=a("6186"),n=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,o.GetGridByCode)({code:e,IsAll:a}).then((function(e){var o=e.IsSucceed,i=e.Data,s=e.Message;if(o){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||n.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,o=e.type;this.queryInfo.Page="limit"===o?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var o=0;o<this.columns.length;o++){var n=this.columns[o];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1e48":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("5530")),r=o(a("c14f")),i=o(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var s=o(a("15ac")),l=o(a("0f97")),u=o(a("34e9")),c=o(a("a153")),d=o(a("d7a3")),f=a("ed08"),m=a("f4e9"),h=a("a024"),g=a("f4f2");t.default={name:"PROSalaryPropertyQuery",components:{DynamicDataTable:l.default,TopHeader:u.default,detail:c.default},mixins:[s.default,d.default],data:function(){return{btnLoading:!1,tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10},currentComponent:"",title:"",columns:[],tbData:[],total:0,tbLoading:!1,dialogVisible:!1,selectList:[],keywords:"",rules:{calculateDate:[{required:!0,message:"请选择",trigger:"change"}],Working_Team_List:[{required:!0,message:"请选择",trigger:"change"}]},form:{calculateDate:"",Working_Team_List:[]},workingTeamList:[],Total_Fee:0}},watch:{columns:function(e){this.FactoryDetailData.Is_Workshop_Enabled||e.map((function(e){"Workshop_Name"===e.Code&&(e.Is_Display=!1)}))}},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getCurFactory();case 1:return t.n=2,e.getTableConfig("pro_property_query_list,Steel");case 2:e.getBaseData();case 3:return t.a(2)}}),t)})))()},methods:{getBaseData:function(){var e=this;(0,h.GetWorkingTeams)().then((function(t){t.IsSucceed?e.workingTeamList=t.Data:e.$message({message:t.Message,type:"error"})}))},fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,n.default)({},this.form);delete t["calculateDate"],t.Begin_Date=this.form.calculateDate?(0,f.parseTime)(this.form.calculateDate[0],"{y}-{m}-{d}"):"",t.End_Date=this.form.calculateDate?(0,f.parseTime)(this.form.calculateDate[1],"{y}-{m}-{d}"):"",(0,m.GetPlanSalaryPageList)((0,n.default)((0,n.default)({},t),{},{PageSize:-1})).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.Total_Fee=0,t.Data.Data.map((function(t){e.Total_Fee+=t.Total_Fee}))):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleClose:function(){this.dialogVisible=!1},handleSelectionChange:function(e){this.selectList=e},handleDetail:function(e){var t=this;this.currentComponent="detail",this.title="计产明细",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].initData(e,t.form)}))},handleExport:function(){var e=this,t=(0,n.default)({},this.form);delete t["calculateDate"],t.Begin_Date=this.form.calculateDate?(0,f.parseTime)(this.form.calculateDate[0],"{y}-{m}-{d}"):"",t.End_Date=this.form.calculateDate?(0,f.parseTime)(this.form.calculateDate[1],"{y}-{m}-{d}"):"",(0,m.ExportPlanSalary)((0,n.default)((0,n.default)({},t),{},{Data:this.tbData})).then((function(t){if(t.IsSucceed){var a=new URL(t.Data,(0,g.baseUrl)());window.open(a.href,"_blank"),e.$message({message:"导出成功",type:"success"})}else e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;e.fetchData()}))}}}},"38e1":function(e,t,a){"use strict";a.r(t);var o=a("1e48"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);t["default"]=n.a},"3a5e":function(e,t,a){"use strict";a.r(t);var o=a("fc20"),n=a("38e1");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("aaaa");var i=a("2877"),s=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"8c57464e",null);t["default"]=s.exports},"433b":function(e,t,a){},"4e82":function(e,t,a){"use strict";var o=a("23e7"),n=a("e330"),r=a("59ed"),i=a("7b0b"),s=a("07fa"),l=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),h=a("99f4"),g=a("1212"),y=a("ea83"),p=[],_=n(p.sort),b=n(p.push),v=c((function(){p.sort(void 0)})),P=c((function(){p.sort(null)})),D=f("sort"),T=!c((function(){if(g)return g<70;if(!(m&&m>3)){if(h)return!0;if(y)return y<603;var e,t,a,o,n="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(o=0;o<47;o++)p.push({k:t+o,v:a})}for(p.sort((function(e,t){return t.v-e.v})),o=0;o<p.length;o++)t=p[o].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}})),L=v||!P||!D||!T,C=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};o({target:"Array",proto:!0,forced:L},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(T)return void 0===e?_(t):_(t,e);var a,o,n=[],u=s(t);for(o=0;o<u;o++)o in t&&b(n,t[o]);d(n,C(e)),a=s(n),o=0;while(o<a)t[o]=n[o++];while(o<u)l(t,o++);return t}})},7196:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteWorkshop=u,t.GetFactoryPeoplelist=r,t.GetWorkshopEntity=l,t.GetWorkshopPageList=s,t.SaveEntity=i;var n=o(a("b775"));function r(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:e})}},"77bd":function(e,t,a){"use strict";a("433b")},8044:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("c14f")),r=o(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var i=o(a("15ac")),s=o(a("0f97")),l=a("f4e9"),u=a("ed08");t.default={components:{DynamicDataTable:s.default},mixins:[i.default],data:function(){return{tbConfig:{Pager_Align:"center"},columns:[],tbData:[],total:0,queryInfo:{Page:1,PageSize:10},form:{},detailData:"",detailList:"",activeName:""}},watch:{activeName:function(){1===this.detailData.Task_Type&&this.columns.map((function(e){"Component_Code"===e.Code&&(e.Is_Display=!1)}))}},created:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("pro_property_query_detail,Steel");case 1:e.activeName=1;case 2:return t.a(2)}}),t)})))()},methods:{initData:function(e,t){var a=this;this.detailData=e,(0,l.GetPlanSalaryDetailList)({Working_Team_Id:e.Working_Team_Id,Working_Process_Id:e.Working_Process_Id,Workshop_Id:e.Workshop_Id,Begin_Date:t.calculateDate?(0,u.parseTime)(t.calculateDate[0],"{y}-{m}-{d}"):"",End_Date:t.calculateDate?(0,u.parseTime)(t.calculateDate[1],"{y}-{m}-{d}"):""}).then((function(e){e.IsSucceed?a.detailList=e.Data.map((function(e){var t="";return e.Config.Quota_Detail_List.map((function(e){var a="",o="",n="";a=1===e.Custome_Key?"单重":2===e.Custome_Key?"长度":3===e.Custome_Key?"板厚":"构件类型",o=1===e.Custome_Symbol?"<":2===e.Custome_Symbol?"≤":3===e.Custome_Symbol?">":4===e.Custome_Symbol?"≥":"=",n=1===e.Custome_Key?e.Custome_Value+"kg":2===e.Custome_Key||3===e.Custome_Key?e.Custome_Value+"mm":e.Custome_Value,t=t+a+o+n+","})),e.Config.Quota_Detail=t,e})):a.$message({message:e.Message,type:"error"})}))}}}},a024:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=I,t.AddTechnology=l,t.AddWorkingProcess=s,t.DelLib=B,t.DeleteProcess=T,t.DeleteProcessFlow=P,t.DeleteTechnology=D,t.DeleteWorkingTeams=F,t.GetAllProcessList=f,t.GetCheckGroupList=W,t.GetChildComponentTypeList=A,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=R,t.GetFactoryWorkingTeam=p,t.GetGroupItemsList=v,t.GetLibList=i,t.GetLibListType=M,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=g,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=O,t.GetProcessListWithUserBase=w,t.GetProcessWorkingTeamBase=x,t.GetTeamListByUser=E,t.GetTeamProcessList=b,t.GetWorkingTeam=_,t.GetWorkingTeamBase=S,t.GetWorkingTeamInfo=G,t.GetWorkingTeams=L,t.GetWorkingTeamsPageList=C,t.SaveWorkingTeams=k,t.UpdateProcessTeam=y;var n=o(a("b775")),r=o(a("4328"));function i(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function l(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function m(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function g(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function p(){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function _(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function b(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function v(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function P(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function D(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function T(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function S(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function O(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function w(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function B(e){return(0,n.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a153:function(e,t,a){"use strict";a.r(t);var o=a("adbe"),n=a("e39f");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("77bd");var i=a("2877"),s=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"61659599",null);t["default"]=s.exports},aaaa:function(e,t,a){"use strict";a("eb87a")},adbe:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"form-list"},[a("div",{staticClass:"form-content"},[a("span",[e._v("所属班组")]),a("span",[e._v(e._s(e.detailData.Working_Team_Name))])]),a("div",{staticClass:"form-content"},[a("span",[e._v("工序")]),a("span",[e._v(e._s(e.detailData.Working_Process_Name))])])]),a("div",{staticClass:"variable-list"},[a("div",{staticClass:"variable-container"},e._l(e.detailList,(function(t,o){return a("el-collapse",{key:o,attrs:{accordion:""},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-collapse-item",{attrs:{name:o+1}},[a("template",{slot:"title"},[a("div",{staticClass:"form-content"},[a("span",[e._v("变量")]),a("span",[e._v(e._s(t.Config.Quota_Detail))])]),a("div",{staticClass:"form-content"},[a("span",[e._v("定额")]),a("span",[e._v(e._s(t.Config.Unit_Price)+"元")])]),a("div",{staticClass:"form-content"},[a("span",[e._v("备注")]),a("span",[e._v(e._s(t.Config.Remark||"-"))])])]),a("div",{staticClass:"variable-table"},[a("dynamic-data-table",{ref:"dyTable",refInFor:!0,staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:t.List,total:e.total,page:e.queryInfo.Page,border:"",stripe:""},scopedSlots:e._u([{key:"Fee",fn:function(t){var o=t.row;return[a("div",[e._v(e._s(o.Fee?0==o.Fee?o.Fee:o.Fee.toFixed(3):"-"))])]}}],null,!0)})],1)],2)],1)})),1)])])},n=[]},d7a3:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("c14f")),r=o(a("1da1")),i=a("5e99"),s=a("fd31"),l=a("7196");t.default={data:function(){return{FactoryDetailData:{},ProfessionalType:[],factoryPeoplelist:[]}},methods:{getCurFactory:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,i.GetCurFactory)({}).then((function(t){t.IsSucceed?e.FactoryDetailData=t.Data[0]:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryTypeOption:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryPeoplelist:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryPeoplelist)({}).then((function(t){t.IsSucceed?e.factoryPeoplelist=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getManageCycle:function(){if(this.FactoryDetailData.Manage_Cycle_Enabled){var e,t,a,o,n,r,i=(new Date).getFullYear(),s=(new Date).getMonth()+1;1===this.FactoryDetailData.Manage_Cycle_Begin_Type?s-1===0?(e=i-1,t=12):(e=i,t=s-1):2===this.FactoryDetailData.Manage_Cycle_Begin_Type?(e=i,t=s):3===this.FactoryDetailData.Manage_Cycle_Begin_Type&&(s+1===13?(e=i+1,t=1):(e=i,t=s+1)),a=this.checkDate(e,t,this.FactoryDetailData.Manage_Cycle_Begin_Date),1===this.FactoryDetailData.Manage_Cycle_End_Type?s-1===0?(o=i-1,n=12):(o=i,n=s-1):2===this.FactoryDetailData.Manage_Cycle_End_Type?(o=i,n=s):3===this.FactoryDetailData.Manage_Cycle_End_Type&&(s+1===13?(o=i+1,n=1):(o=i,n=s+1)),r=this.checkDate(o,n,this.FactoryDetailData.Manage_Cycle_End_Date);var l=e+"-"+this.zeroFill(t)+"-"+this.zeroFill(a),u=o+"-"+this.zeroFill(n)+"-"+this.zeroFill(r);return[l,u]}return!1},checkDate:function(e,t,a){var o;return o=e%4===0?2===t?a>29?29:a:(4===t||6===t||9===t||11===t)&&a>30?30:a:2===t?a>28?28:a:(4===t||6===t||9===t||11===t)&&a>30?30:a,o},zeroFill:function(e){return e<10?"0"+e:e}}}},e39f:function(e,t,a){"use strict";a.r(t);var o=a("8044"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);t["default"]=n.a},eb87a:function(e,t,a){},f4e9:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteQuotaList=d,t.DeleteSalary=s,t.ExportPlanSalary=y,t.GetFactoryPeoplelist=u,t.GetFactorySalaryPageList=l,t.GetPlanSalaryDetailList=g,t.GetPlanSalaryPageList=h,t.GetQuotaDetail=m,t.GetQuotaPageList=f,t.ImportSalaryFiles=r,t.SaveQuotaEntity=c,t.UpdateImportSalaryFile=i;var n=o(a("b775"));o(a("4328"));function r(e){return(0,n.default)({url:"/PRO/Factory/ImportSalaryFiles",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Factory/UpdateImportSalaryFile",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Factory/DeleteSalary",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Factory/GetFactorySalaryPageList",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/ProductionSalary/SaveQuotaEntity",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/ProductionSalary/DeleteQuotaList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetQuotaPageList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetQuotaDetail",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetPlanSalaryPageList",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetPlanSalaryDetailList",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/ProductionSalary/ExportPlanSalary",method:"post",data:e})}},fc20:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{attrs:{padding:"0"},scopedSlots:e._u([{key:"left",fn:function(){return[a("el-form",{ref:"form",attrs:{"label-width":"80px",inline:!0,model:e.form,rules:e.rules}},[a("el-form-item",{attrs:{label:"核算时间",prop:"calculateDate"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.calculateDate,callback:function(t){e.$set(e.form,"calculateDate",t)},expression:"form.calculateDate"}})],1),a("el-form-item",{attrs:{label:"核算班组",prop:"Working_Team_List"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择",multiple:""},model:{value:e.form.Working_Team_List,callback:function(t){e.$set(e.form,"Working_Team_List",t)},expression:"form.Working_Team_List"}},e._l(e.workingTeamList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("计产核算")])],1)],1)]},proxy:!0}])}),a("div",{staticClass:"mid-container"},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("div",[a("el-button",{attrs:{type:"success",loading:e.btnLoading,disabled:0===e.tbData.length},on:{click:e.handleExport}},[e._v("导出")])],1),a("div",{staticClass:"total-content"},[a("div",{staticClass:"total-wrapper"},[a("span",[e._v(e._s("总计加工费（元）："+(e.Total_Fee||"-")))])])])])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.handleSelectionChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch},scopedSlots:e._u([{key:"empty",fn:function(t){t.row;return[a("div",[e._v(e._s("暂无数据，可通过上方筛选条件进行查询"))])]}},{key:"Workshop_Name",fn:function(t){var o=t.row;return[a("div",[e._v(e._s(o.Workshop_Name||"-"))])]}},{key:"Valuation_Unit",fn:function(t){var o=t.row;return[a("div",[e._v(e._s(1===o.Valuation_Unit?"重量(t)":2===o.Valuation_Unit?"长度(m)":"数量件"))])]}},{key:"Unit_Amount",fn:function(t){var o=t.row;return[a("div",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.handleDetail(o)}}},[e._v(e._s(o.Unit_Amount?0==o.Unit_Amount?o.Unit_Amount:o.Unit_Amount.toFixed(3):"-"))])]}},{key:"Total_Fee",fn:function(t){var o=t.row;return[a("div",[e._v(e._s(o.Total_Fee?0==o.Total_Fee?o.Total_Fee:o.Total_Fee.toFixed(3):"-"))])]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"cs-dialog",attrs:{title:e.title,visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"60%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible},on:{close:e.handleClose,refresh:e.fetchData}}):e._e()],1)],1)},n=[]}}]);