(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-32c25f97"],{"0bff":function(t,e,a){"use strict";a("4744c")},"15ac":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),i=a("c685");e.default={methods:{getTableConfig:function(t){var e=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:t,IsAll:a}).then((function(t){var n=t.IsSucceed,l=t.Data,o=t.Message;if(n){if(!l)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,l.Grid),s=a?(null===l||void 0===l?void 0:l.ColumnList)||[]:(null===l||void 0===l?void 0:l.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+l.Grid.Row_Number||i.tablePageSize[0]),r(e.columns)}else e.$message({message:o,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,a=t.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:e,this.fetchData()},pageChange:function(t){var e=t.page,a=t.limit,n=t.type;this.queryInfo.Page="limit"===n?1:e,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var a={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?a.Value=t[e]:a.Value=[t[e]];for(var n=0;n<this.columns.length;n++){var i=this.columns[n];if(i.Code===e){a.Type=i.Type,a.Filter_Type=i.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1a6e7":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100  cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content cs-z-shadow"},[a("div",{staticStyle:{"text-align":"right","margin-bottom":"10px"}},[a("el-button",{attrs:{type:"danger",disabled:!t.selectList.length},on:{click:t.handleDelete}},[t._v("删除")]),a("el-button",{attrs:{type:"success"},on:{click:t.handleImport}},[t._v("导入")])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,data:t.tbData,config:t.tbConfig,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{multiSelectedChange:t.multiSelectedChange,tableSearch:t.tableSearch,gridSizeChange:t.handlePageChange,gridPageChange:t.handlePageChange},scopedSlots:t._u([{key:"hsearch_Project_Name",fn:function(e){var n=e.column;return[a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:t.projectChange},model:{value:t.$refs.dyTable.searchedField[n.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,n.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},[a("el-option",{attrs:{label:"全部",value:""}}),t._l(t.projects,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}},{key:"hsearch_InstallUnit_Name",fn:function(e){e.column;return[a("el-select",{attrs:{disabled:!t.$refs.dyTable.searchedField["Project_Name"],placeholder:"请选择",clearable:""},on:{change:t.installNameChange},model:{value:t.installName,callback:function(e){t.installName=e},expression:"installName"}},[a("el-option",{attrs:{label:"全部",value:""}}),t._l(t.installOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}}])})],1),a("import-dialog",{ref:"import"})],1)])},i=[]},2245:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ActiveAuxMaterial=O,e.ActiveRawMaterial=g,e.DeleteAuxMaterial=_,e.DeleteMaterialCategory=c,e.DeleteMaterials=u,e.DeleteRawMaterial=I,e.DeleteWarehouseReceipt=C,e.ExportPurchaseDetail=$,e.GetAuxMaterialEntity=v,e.GetAuxMaterialPageList=b,e.GetAuxStandardsList=j,e.GetAuxWarehouseReceiptEntity=L,e.GetMaterialCategoryList=d,e.GetMaterialImportPageList=l,e.GetPurchaseDetail=k,e.GetPurchaseDetailList=N,e.GetRawMaterialEntity=h,e.GetRawMaterialPageList=m,e.GetRawStandardsList=U,e.GetRawWarehouseReceiptEntity=D,e.GetWarehouseReceiptPageList=S,e.ImportMatAux=y,e.ImportMatAuxRcpt=T,e.ImportMatRaw=R,e.ImportMatRawRcpt=A,e.ImportMaterial=s,e.MaterialDataTemplate=o,e.SaveAuxMaterialEntity=M,e.SaveAuxWarehouseReceipt=x,e.SaveMaterialCategory=f,e.SaveRawMaterialEntity=p,e.SaveRawWarehouseReceipt=w,e.SubmitWarehouseReceipt=G,e.TemplateDownload=P;var i=n(a("b775")),r=n(a("4328"));function l(t){return(0,i.default)({url:"/PRO/Material/GetMaterialImportPageList",method:"post",data:t})}function o(){return(0,i.default)({url:"/PRO/Material/MaterialDataTemplate",method:"post"})}function s(t){return(0,i.default)({url:"/PRO/Material/ImportMaterial",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/Material/DeleteMaterials",method:"post",data:r.default.stringify(t)})}function d(t){return(0,i.default)({url:"/PRO/Material/GetMaterialCategoryList",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/Material/DeleteMaterialCategory",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/Material/SaveMaterialCategory",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/Material/SaveRawMaterialEntity",method:"post",data:t})}function h(t){return(0,i.default)({url:"/PRO/Material/GetRawMaterialEntity",method:"post",data:t})}function m(t){return(0,i.default)({url:"/PRO/Material/GetRawMaterialPageList",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/Material/ActiveRawMaterial",method:"post",data:t})}function I(t){return(0,i.default)({url:"/PRO/Material/DeleteRawMaterial",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/Material/GetAuxMaterialPageList",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/Material/GetAuxMaterialEntity",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/Material/TemplateDownload",method:"post",data:t})}function R(t){return(0,i.default)({url:"/PRO/Material/ImportMatRaw",method:"post",data:t})}function y(t){return(0,i.default)({url:"/PRO/Material/ImportMatAux",method:"post",data:t})}function M(t){return(0,i.default)({url:"/PRO/Material/SaveAuxMaterialEntity",method:"post",data:t})}function O(t){return(0,i.default)({url:"/PRO/Material/ActiveAuxMaterial",method:"post",data:t})}function _(t){return(0,i.default)({url:"/PRO/Material/DeleteAuxMaterial",method:"post",data:t})}function S(t){return(0,i.default)({url:"/PRO/Material/GetWarehouseReceiptPageList",method:"post",data:t})}function U(t){return(0,i.default)({url:"/PRO/Material/GetRawStandardsList",method:"post",data:t})}function w(t){return(0,i.default)({url:"/PRO/Material/SaveRawWarehouseReceipt",method:"post",data:t})}function C(t){return(0,i.default)({url:"/PRO/Material/DeleteWarehouseReceipt",method:"post",data:t})}function D(t){return(0,i.default)({url:"/PRO/Material/GetRawWarehouseReceiptEntity",method:"post",data:t})}function G(t){return(0,i.default)({url:"/PRO/Material/SubmitWarehouseReceipt",method:"post",data:t})}function L(t){return(0,i.default)({url:"/PRO/Material/GetAuxWarehouseReceiptEntity",method:"post",data:t})}function x(t){return(0,i.default)({url:"/PRO/Material/SaveAuxWarehouseReceipt",method:"post",data:t})}function j(t){return(0,i.default)({url:"/PRO/Material/GetAuxStandardsList",method:"post",data:t})}function A(t){return(0,i.default)({url:"/PRO/Material/ImportMatRawRcpt",method:"post",data:t})}function T(t){return(0,i.default)({url:"/PRO/Material/ImportMatAuxRcpt",method:"post",data:t})}function k(t){return(0,i.default)({url:"/PRO/Material/GetPurchaseDetail",method:"post",data:t})}function $(t){return(0,i.default)({url:"/PRO/Material/ExportPurchaseDetail",method:"post",data:t})}function N(t){return(0,i.default)({url:"/PRO/Material/GetPurchaseDetailList",method:"post",data:t})}},"4744c":function(t,e,a){},"4e82":function(t,e,a){"use strict";var n=a("23e7"),i=a("e330"),r=a("59ed"),l=a("7b0b"),o=a("07fa"),s=a("083a"),u=a("577e"),d=a("d039"),c=a("addb"),f=a("a640"),p=a("3f7e"),h=a("99f4"),m=a("1212"),g=a("ea83"),I=[],b=i(I.sort),v=i(I.push),P=d((function(){I.sort(void 0)})),R=d((function(){I.sort(null)})),y=f("sort"),M=!d((function(){if(m)return m<70;if(!(p&&p>3)){if(h)return!0;if(g)return g<603;var t,e,a,n,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)I.push({k:e+n,v:a})}for(I.sort((function(t,e){return e.v-t.v})),n=0;n<I.length;n++)e=I[n].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),O=P||!R||!y||!M,_=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:u(e)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:O},{sort:function(t){void 0!==t&&r(t);var e=l(this);if(M)return void 0===t?b(e):b(e,t);var a,n,i=[],u=o(e);for(n=0;n<u;n++)n in e&&v(i,e[n]);c(i,_(t)),a=o(i),n=0;while(n<a)e[n]=i[n++];while(n<u)s(e,n++);return e}})},"62f6":function(t,e,a){"use strict";a.r(e);var n=a("dc75"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"78f7":function(t,e,a){"use strict";a.r(e);var n=a("1a6e7"),i=a("62f6");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);var l=a("2877"),o=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"57c020a6",null);e["default"]=o.exports},"92a7":function(t,e,a){"use strict";a.r(e);var n=a("bec6"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},a930a:function(t,e,a){"use strict";a.r(e);var n=a("adec"),i=a("92a7");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("0bff");var l=a("2877"),o=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"65f05030",null);e["default"]=o.exports},adec:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:"导入更新",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a("div",{staticClass:"box "},[a("div",{staticClass:"box-item"},[a("span",{staticClass:"item-span"},[t._v("1.选择项目")]),a("el-select",{staticStyle:{width:"70%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.Project_Id,callback:function(e){t.Project_Id=e},expression:"Project_Id"}},t._l(t.projects,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("div",{staticClass:"box-item"},[a("span",{staticClass:"item-span"},[t._v("2.生产单元")]),a("el-select",{staticStyle:{width:"70%"},attrs:{filterable:"",disabled:!t.Project_Id,clearable:"",placeholder:"请选择"},model:{value:t.InstallUnit_Id,callback:function(e){t.InstallUnit_Id=e},expression:"InstallUnit_Id"}},t._l(t.installOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)]),a("div",{staticClass:"box d-x"},[a("span",[t._v("2.下载模板")]),a("el-button",{attrs:{size:"large"},on:{click:t.handleDownload}},[a("svg-icon",{attrs:{"icon-class":"document_form_icon"}}),t._v(" 材料清单模板 ")],1)],1),a("div",{staticClass:"upload-box"},[a("span",{staticStyle:{"margin-bottom":"20px",display:"inline-block"}},[t._v(" 3.上传文件 ")]),a("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}})],1),a("footer",{staticClass:"cs-footer"},[a("el-button",{attrs:{disabled:t.btnLoading},on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit()}}},[t._v("确 定")])],1)])},i=[]},bec6:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("3796")),r=a("c9d9"),l=a("ed08"),o=a("1b69"),s=a("f2f6"),u=a("2245");e.default={components:{UploadExcel:i.default},props:{processId:{type:String,default:""}},data:function(){return{dialogVisible:!1,Project_Id:"",InstallUnit_Id:"",btnLoading:!1,radio:3,proOption:[],installOption:[],projects:[]}},mounted:function(){this.getProject()},methods:{getTemplate:function(){},getProject:function(){var t=this;(0,o.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))},projectChange:function(){this.installOption=[],this.InstallUnit_Id="",this.Project_Id&&this.getUnitList()},getUnitList:function(){var t=this;(0,s.GetInstallUnitList)({Project_Id:this.Project_Id}).then((function(e){e.IsSucceed&&(t.installOption=e.Data)}))},beforeUpload:function(t){var e=this,a=new FormData;a.append("Project_Id",this.Project_Id),a.append("InstallUnit_Id",this.InstallUnit_Id),a.append("Type",4),a.append("files",t),(0,u.ImportMaterial)(a).then((function(t){t.IsSucceed?(e.$message({message:"导入成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):(e.$message({message:t.Message,type:"error"}),t.Data&&window.open((0,l.combineURL)(e.$baseUrl,t.Data),"_blank"))}))},handleDownload:function(){var t=this;(0,u.MaterialDataTemplate)({}).then((function(e){e.IsSucceed?window.open((0,l.combineURL)(r.fileUploadOriginUrl,e.Data)):t.$message({message:e.Message,type:"error"})}))},handleClose:function(){this.dialogVisible=!1},handleOpen:function(){this.dialogVisible=!0},handleSubmit:function(){this.Project_Id?this.InstallUnit_Id?this.$refs.upload.handleSubmit():this.$message({message:"请选择安装单元",type:"warning"}):this.$message({message:"请选择项目",type:"warning"})}}}},dc75:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("ab43"),a("d3b7"),a("25f0");var i=n(a("c14f")),r=n(a("1da1")),l=n(a("a930a")),o=a("2245"),s=n(a("0f97")),u=n(a("15ac")),d=a("1b69"),c=a("f2f6");e.default={name:"PROMaterialsList",components:{importDialog:l.default,DynamicDataTable:s.default},mixins:[u.default],data:function(){return{tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},projects:[],installName:"",columns:[],installOption:[],tbData:[],total:0,tbLoading:!1,selectList:[]}},mounted:function(){var t=this;return(0,r.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig("Pro_Materiel_List");case 1:t.fetchData(),t.getSearchList();case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;(0,o.GetMaterialImportPageList)(this.queryInfo).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})}))},getSearchList:function(){var t=this;(0,d.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))},installNameChange:function(t){this.$refs.dyTable.searchedField["InstallUnit_Name"]=t,this.showSearchBtn()},getUnitList:function(t){var e=this;(0,c.GetInstallUnitList)({Project_Id:t}).then((function(t){t.IsSucceed&&(e.installOption=t.Data)}))},projectChange:function(t){if(this.installName="",this.installOption=[],t){var e=this.projects.find((function(e){return e.Name===t}));this.getUnitList(e.Id)}else this.$refs.dyTable.searchedField["InstallUnit_Name"]="";this.showSearchBtn()},multiSelectedChange:function(t){this.selectList=t},handleDelete:function(){var t=this;this.$confirm("此操作将永久删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.DeleteMaterials)({ids:t.selectList.map((function(t){return t.Id})).toString()}).then((function(e){e.IsSucceed?t.$message({type:"success",message:"删除成功!"}):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleImport:function(){this.$refs["import"].handleOpen()}}}},f2f6:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=s,e.CheckPlanTime=u,e.DeleteInstallUnit=p,e.GetCompletePercent=b,e.GetEntity=P,e.GetInstallUnitAllInfo=c,e.GetInstallUnitComponentPageList=I,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=d,e.GetInstallUnitList=o,e.GetInstallUnitPageList=l,e.GetProjectInstallUnitList=v,e.ImportInstallUnit=m,e.InstallUnitInfoTemplate=h,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=R;var i=n(a("b775")),r=n(a("4328"));function l(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function o(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function I(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function P(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function R(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);