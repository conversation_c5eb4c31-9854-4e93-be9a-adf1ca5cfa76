(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-29736023"],{"037e":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"text-center"},[a("label",[t._v("单体名称 "),a("el-input",{staticStyle:{width:"70%"},attrs:{placeholder:"请输入内容",clearable:""},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1),a("el-divider"),a("div",{staticClass:"btn-x"},[a("el-button",{on:{click:t.close}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!t.value.trim(),loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确定")])],1)],1)},r=[]},"142c":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"left-card"},[a("div",{staticClass:"card-title",attrs:{title:t.projectName}},[t._v(t._s(t.projectName))]),t.btnDisabled?t._e():a("div",{staticClass:"cs-btn-x"},[a("el-button",{attrs:{type:"primary",disabled:t.Is_Integration},on:{click:t.addRegion}},[t._v("新增区域")])],1),a("div",{staticClass:"tree-x"},[a("el-tree",{ref:"tree",attrs:{props:{label:"Label",children:"Children"},data:t.treeData,"highlight-current":"","current-node-key":t.currentKey,"empty-text":"暂无数据","node-key":"Id","expand-on-click-node":!1},on:{"node-click":t.handleNodeClick},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.node,r=e.data;return a("span",{staticClass:"custom-tree-node"},[a("svg-icon",{attrs:{"icon-class":n.expanded?"icon-folder-open":"icon-folder","class-name":"class-icon"}}),a("span",{staticClass:"cs-label",attrs:{title:n.label}},[t._v(t._s(n.label))]),t.currentKey!==r.Id||t.btnDisabled?t._e():a("span",{staticClass:"tree-btn-x"},[t.Is_Integration?t._e():a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){return t.handleEdit(r)}}},[t._v(" 编辑 ")]),1!==n.level||t.Is_Integration?t._e():a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){return t.handleAdd(r)}}},[t._v(" 添加 ")]),t.Is_Integration?t._e():a("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"mini"},on:{click:function(e){return t.handleDelete(r)}}},[t._v(" 删除 ")])],1)],1)}}])})],1)])},r=[]},"25d7":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("14d9"),a("d3b7");var o=a("ed08"),s=n(a("7e39")),l=n(a("cba4")),d=n(a("44c0")),u=n(a("3a16")),c=n(a("edf4")),f=a("8975"),m=n(a("15ac")),p=n(a("0f97")),h=a("f2f6"),g=a("66f9");e.default={name:"PROProjectListUnit",components:{UnitLeftCard:s.default,SingleDialog:l.default,UnitDialog:u.default,ImportDialog:c.default,DynamicDataTable:p.default,RegionDialog:d.default},mixins:[m.default],data:function(){return{form:{Name:"",Plan_Date:[],Ultimate_Demand_Date:[],Code:""},areaInfo:[{code:"Estimate_Count",label:"预估构件总数（件）",width:"130px"},{code:"Discreet_Value",label:"预估构件总重（t）",width:"130px"},{code:"SteelAmount",label:"深化构件总数（件）",width:"130px"},{code:"SteelWeight",label:"深化构件总重（t）",width:"130px"},{code:"To_Stock_In_Count",label:"未入库总数（件）",width:"130px"},{code:"To_Stock_In_Weight",label:"未入库总重（t）",width:"130px"},{code:"Demand_Begin_Date",label:"协调要货使开始时间",width:"130px"},{code:"Demand_End_Date",label:"协调要货使结束时间",width:"130px"},{code:"Axis_Y",label:"轴网（纵）",width:"100px"},{code:"Axis_X",label:"轴网（横）",width:"100px"},{code:"Operator_Name",label:"操作人",width:"100px"},{code:"Operator_Date",label:"操作时间",width:"100px"}],areaForm:{},tbLoading:!1,showAddBtn:!1,currentComponent:"",title:"",treeData:[],tbConfig:{},dialogVisible:!1,isAreaNode:!1,projectId:"",monomerId:"",queryInfo:{Page:1,PageSize:10,ParameterJson:[],Area_Id:""},tbData:[],columns:[],total:0}},mounted:function(){this.projectId=this.$route.query.sysId},methods:{getSelectVal:function(t){this.selectList=t},handleSearch:function(){this.queryInfo.ParameterJson=[],this.form.Name&&this.queryInfo.ParameterJson.push({Key:"Name",Type:"text",Filter_Type:"text",Value:[this.form.Name]}),this.form.Plan_Date&&this.form.Plan_Date.length>0&&this.form.Plan_Date[0]&&this.queryInfo.ParameterJson.push({Key:"Plan_Date",Type:"date",Filter_Type:"daterange",Value:this.form.Plan_Date}),this.form.Ultimate_Demand_Date&&this.form.Ultimate_Demand_Date.length>0&&this.form.Ultimate_Demand_Date[0]&&this.queryInfo.ParameterJson.push({Key:"Ultimate_Demand_Date",Type:"date",Filter_Type:"daterange",Value:this.form.Ultimate_Demand_Date}),this.form.Code&&this.queryInfo.ParameterJson.push({Key:"Code",Type:"text",Filter_Type:"text",Value:[this.form.Code]}),this.getTbList(1)},getTreeData:function(){this.isAreaNode=!1,this.$refs["tree"].refreshNode()},treeNodeClick:function(t){var e,a=t.data,n=t.node;this.node=n,this.showAddBtn=!(null!==(e=a.Data)&&void 0!==e&&e.Is_Imported),this.queryInfo.Area_Id=a.Id,this.getAreaInfo(a),this.getTbList(1),this.isAreaNode=!0},getAreaInfo:function(t){var e=this;return(0,i.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:(0,g.AreaGetEntity)({id:t.Id}).then((function(t){if(t.IsSucceed){if(!t.Data)return[];t.Data.Demand_Begin_Date=(0,f.timeFormat)(t.Data.Demand_Begin_Date),t.Data.Demand_End_Date=(0,f.timeFormat)(t.Data.Demand_End_Date),t.Data.Create_Date=(0,f.timeFormat)(t.Data.Create_Date),t.Data.Operator_Date=(0,f.timeFormat)(t.Data.Operator_Date),e.areaForm=t.Data,e.monomerId=t.Data.MonomerId}else e.$message({message:t.Message,type:"error"})}));case 1:return a.a(2)}}),a)})))()},getTbList:function(t){var e=this;return(0,i.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,e.getTableConfig("pro_project_list_area");case 1:e.fetchData(t);case 2:return a.a(2)}}),a)})))()},fetchData:function(t){var e=this;this.tbLoading=!0,t&&(this.queryInfo.Page=t),(0,h.GetInstallUnitPageList)(this.queryInfo).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).finally((function(t){e.tbLoading=!1}))},handleEdit:function(t){var e=this;this.currentComponent="UnitDialog",this.title="编辑批次",this.dialogVisible=!0,this.$nextTick((function(a){e.$refs["content"].getInfo(t)}))},handleDelete:function(t){var e=this;this.$confirm("是否删除该批次","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,h.DeleteInstallUnit)({id:t.Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.fetchData(1)):e.$message({message:"删除失败",type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleClose:function(){this.dialogVisible=!1},handleAddSingle:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.data,n=void 0===a?"":a,r=e.isEdit,i=void 0!==r&&r;this.currentComponent="SingleDialog",this.title=i?"编辑单体":"新增单体",this.dialogVisible=!0,this.$nextTick((function(e){i&&t.$refs["content"].getInfo(n,i)}))},handleAddRegion:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.data,n=void 0===a?"":a,r=e.isEdit,i=void 0!==r&&r,o=e.isFromTree,s=void 0!==o&&o;this.currentComponent="RegionDialog",this.title=i?"编辑区域":"新增区域",this.dialogVisible=!0,this.$nextTick((function(e){s&&t.$refs["content"].setArea(n,t.node),i&&t.$refs["content"].getInfo(n,t.node),!i&&s&&t.$refs["content"].setRange(n)}))},handleAddUnit:function(){this.currentComponent="UnitDialog",this.title="新建安装单元",this.dialogVisible=!0},handleExport:function(){},handleExportArea:function(){},handleImport:function(){this.currentComponent="ImportDialog",this.title="安装单元信息导入",this.dialogVisible=!0},goBack:function(){var t=this;this.$confirm("退出后不会保留当前页面数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.closeTagView)(t.$store,t.$route)})).catch((function(){}))}}}},"2d0a":function(t,e,a){"use strict";a.r(e);var n=a("d4d4"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"3a16":function(t,e,a){"use strict";a.r(e);var n=a("5724"),r=a("897c");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("f70d");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"397b854d",null);e["default"]=s.exports},4177:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7");var r=n(a("5530")),i=n(a("c14f")),o=n(a("1da1")),s=a("8975"),l=a("f2f6"),d=a("66f9");e.default={props:{monomerId:{type:String,default:""},areaId:{type:String,default:""}},data:function(){return{title:"",btnLoading:!1,form:{Project_Id:"",Name:"",Factory_Id:localStorage.getItem("CurReferenceId"),Ultimate_Demand_Begin_Date:"",Ultimate_Demand_End_Date:""},detailForm:{},rules:{Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Project_Id:[{required:!0,message:"请选择项目",trigger:"change"}],Ultimate_Demand_Begin_Date:[{required:!0,message:"请选择时间",trigger:"change"}]}}},computed:{planTime:{get:function(){return[(0,s.timeFormat)(this.form.Ultimate_Demand_Begin_Date),(0,s.timeFormat)(this.form.Ultimate_Demand_End_Date)]},set:function(t){if(t){var e=t[0],a=t[1];this.form.Ultimate_Demand_Begin_Date=(0,s.timeFormat)(e),this.form.Ultimate_Demand_End_Date=(0,s.timeFormat)(a)}else this.form.Ultimate_Demand_Begin_Date="",this.form.Ultimate_Demand_End_Date=""}}},mounted:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:t.form.Project_Id=t.$route.query.id,t.status=+t.$route.query.status,t.getProjectList();case 1:return e.a(2)}}),e)})))()},methods:{getInfo:function(t){var e=this;(0,l.GetInstallUnitAllInfo)({id:t.Id}).then((function(t){if(t.IsSucceed){var a=t.Data.entity,n=a.Name,r=a.Ultimate_Demand_Begin_Date,i=a.Ultimate_Demand_End_Date,o=a.Id,l={Name:n,Id:o,Ultimate_Demand_Begin_Date:(0,s.timeFormat)(r),Ultimate_Demand_End_Date:(0,s.timeFormat)(i)};Object.assign(e.form,l)}else e.$message({message:t.Message,type:"error"})}))},getProjectList:function(){var t=this;(0,d.GetProjectEntity)({id:this.form.Project_Id}).then((function(e){e.IsSucceed&&(t.projectOption=e.Data)}))},handleSubmit:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;var a,n,i={entity:(0,r.default)((0,r.default)({},e.form),{},{Monomer_Id:e.monomerId,Area_Id:e.areaId}),details:[]};i.entity.Source=2,i.entity.Project_Code=null===(a=e.projectOption)||void 0===a||null===(a=a.Project)||void 0===a?void 0:a.Code,i.entity.Project_Name=null===(n=e.projectOption)||void 0===n||null===(n=n.Project)||void 0===n?void 0:n.Short_Name,e.installSubmit(i)}))},installSubmit:function(t){var e=this;this.btnLoading=!0,(0,l.SaveInstallUnit)(t).then((function(t){t.IsSucceed?e.submitSuccess():e.$message({message:t.Message,type:"error"})})).finally((function(t){e.btnLoading=!1}))},submitSuccess:function(){this.$message({message:"添加成功",type:"success"}),this.resetForm("form"),this.$emit("getTbInfo")},resetForm:function(){this.$emit("close")}}}},"44c0":function(t,e,a){"use strict";a.r(e);var n=a("67d4"),r=a("5d7c");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"422f9c18",null);e["default"]=s.exports},"52f2":function(t,e,a){"use strict";a.r(e);var n=a("e4a7"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"552d":function(t,e,a){"use strict";a.r(e);var n=a("25d7"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},5724:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"安装单元名称：",prop:"Name"}},[a("el-input",{attrs:{clearable:""},model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name",e)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"要货时间：",prop:"Ultimate_Demand_Begin_Date"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.planTime,callback:function(e){t.planTime=e},expression:"planTime"}})],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:t.resetForm}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit("form")}}},[t._v("确 定")])],1)],1)},r=[]},"5d7c":function(t,e,a){"use strict";a.r(e);var n=a("6d7a"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"66f9":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddArea=at,e.AddDeepFile=Q,e.AddMonomer=J,e.AppendImportPartList=st,e.AreaDelete=rt,e.AreaGetEntity=tt,e.AttachmentGetEntities=k,e.ChangeLoad=b,e.CommonImportDeependToComp=H,e.ContactsAdd=O,e.ContactsDelete=$,e.ContactsEdit=F,e.ContactsGetEntities=G,e.ContactsGetEntity=U,e.ContactsGetTreeList=L,e.DeleteMonomer=W,e.DepartmentAdd=x,e.DepartmentDelete=P,e.DepartmentEdit=C,e.DepartmentGetEntities=S,e.DepartmentGetEntity=I,e.DepartmentGetList=A,e.EditArea=nt,e.EditMonomer=X,e.FileAdd=g,e.FileAddType=f,e.FileDelete=h,e.FileEdit=_,e.FileGetEntity=p,e.FileHistory=v,e.FileMove=D,e.FileTypeAdd=c,e.FileTypeDelete=u,e.FileTypeEdit=m,e.FileTypeGetEntities=i,e.FileTypeGetEntity=o,e.GeAreaTrees=et,e.GetAreaTreeList=Z,e.GetDictionaryDetailListByCode=T,e.GetEntitiesByRecordId=M,e.GetEntitiesProject=l,e.GetFileCatalog=s,e.GetFilesByType=d,e.GetGetMonomerList=V,e.GetLoadingFiles=y,e.GetMonomerEntity=K,e.GetProMonomerList=z,e.GetProjectEntity=it,e.GetProjectsflowmanagementAdd=R,e.GetProjectsflowmanagementEdit=N,e.GetProjectsflowmanagementInfo=Y,e.GetShortUrl=B,e.ImportDeependToSteel=q,e.ImportPartList=ot,e.SysuserGetUserEntity=E,e.SysuserGetUserList=j,e.UserGroupTree=w;var r=n(a("b775"));function i(t){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:t})}function o(t){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:t})}function s(t){return(0,r.default)({url:"/SYS/Sys_FileType/GetFileCatalog",method:"post",data:t})}function l(t){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntitiesProject",method:"post",data:t})}function d(t){return(0,r.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:t})}function u(t){return(0,r.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:t})}function c(t){return(0,r.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:t})}function f(t){return(0,r.default)({url:"/SYS/Sys_FileType/AddType",method:"post",data:t})}function m(t){return(0,r.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:t})}function p(t){return(0,r.default)({url:"/SYS/Sys_File/GetEntity",method:"post",data:t})}function h(t){return(0,r.default)({url:"/SYS/Sys_File/Delete",method:"post",data:t})}function g(t){return(0,r.default)({url:"/SYS/Sys_File/Add",method:"post",data:t})}function _(t){return(0,r.default)({url:"/SYS/Sys_File/Edit",method:"post",data:t})}function D(t){return(0,r.default)({url:"/SYS/Sys_File/Move",method:"post",data:t})}function b(t){return(0,r.default)({url:"/SYS/Sys_File/IsLoad",method:"post",data:t})}function v(t){return(0,r.default)({url:"/SYS/Sys_File/OldFile",method:"post",data:t})}function y(t){return(0,r.default)({url:"/SYS/Sys_File/GetBIMList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetEntity",method:"post",data:t})}function S(t){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetEntities",method:"post",data:t})}function P(t){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Delete",method:"post",data:t})}function C(t){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Edit",method:"post",data:t})}function x(t){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Add",method:"post",data:t})}function A(t){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetList",method:"post",data:t})}function j(t){return(0,r.default)({url:"/SYS/User/GetUserList",method:"post",data:t})}function E(t){return(0,r.default)({url:"/SYS/User/GetUserEntity",method:"post",data:t})}function w(t){return(0,r.default)({url:"/SYS/UserGroup/GetChildGroupTree",method:"post",data:t})}function T(t){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function U(t){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetEntity",method:"post",data:t})}function G(t){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:t})}function L(t){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:t})}function $(t){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Delete",method:"post",data:t})}function F(t){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Edit",method:"post",data:t})}function O(t){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Add",method:"post",data:t})}function k(t){return(0,r.default)({url:"/SYS/Sys_File/GetAttachmentEntities",method:"post",data:t})}function M(t){return(0,r.default)({url:"/SYS/Sys_File/GetEntitiesByRecordId",method:"post",data:t})}function R(t){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/Add",method:"post",data:t})}function N(t){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/Edit",method:"post",data:t})}function Y(t){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/GetEntity",method:"post",data:t})}function B(t){return(0,r.default)({url:"/PLM/XModel/GetShortUrl",method:"post",data:t})}function q(t){return(0,r.default)({url:"/plm/component/ImportDeependToSteel",method:"get",params:t})}function V(t){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetGetMonomerList",method:"post",data:t})}function K(t){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetMonomerEntity",method:"post",data:t})}function z(t){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetProMonomerList",method:"post",data:t})}function J(t){return(0,r.default)({url:"/PLM/Plm_Project_Areas/AddMonomer",method:"post",data:t})}function X(t){return(0,r.default)({url:"/PLM/Plm_Project_Areas/EditMonomer",method:"post",data:t})}function W(t){return(0,r.default)({url:"/PLM/Plm_Project_Areas/DeleteMonomer",method:"post",data:t})}function H(t){return(0,r.default)({url:"/plm/MaterialInfo/CommonImportDeependToComp",method:"get",params:t})}function Q(t){return(0,r.default)({url:"/PLM/Component/AddDeepFile",method:"post",data:t,timeout:18e5})}function Z(t){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetAreaTreeList",method:"post",data:t})}function tt(t){return(0,r.default)({url:"/PRO/Project/GetArea",method:"post",data:t})}function et(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function at(t){return(0,r.default)({url:"/PRO/Project/AddArea",method:"post",data:t})}function nt(t){return(0,r.default)({url:"/PRO/Project/EditArea",method:"post",data:t})}function rt(t){return(0,r.default)({url:"/PRO/Project/Delete",method:"post",data:t})}function it(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:t})}function ot(t){return(0,r.default)({url:"/PRO/Part/ImportPartList",method:"post",data:t})}function st(t){return(0,r.default)({url:"/PRO/Part/AppendImportPartList",method:"post",data:t})}},"674d":function(t,e,a){"use strict";a.r(e);var n=a("ca15"),r=a("552d");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("70db");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"0d09e7a0",null);e["default"]=s.exports},"67d4":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:t.form,rules:t.rules,"label-width":"150px"}},[a("el-form-item",{attrs:{label:"区域名称",prop:"Name"}},[a("el-input",{attrs:{placeholder:"请输入","show-word-limit":"",maxlength:50,clearable:""},model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name","string"===typeof e?e.trim():e)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"预估构件总数（件）",prop:"Estimate_Count"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0},model:{value:t.form.Estimate_Count,callback:function(e){t.$set(t.form,"Estimate_Count",t._n(e))},expression:"form.Estimate_Count"}})],1),a("el-form-item",{attrs:{label:"预估构件总重（t）",prop:"Discreet_Value"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0},model:{value:t.form.Discreet_Value,callback:function(e){t.$set(t.form,"Discreet_Value",t._n(e))},expression:"form.Discreet_Value"}})],1),a("el-form-item",{attrs:{label:"要货时间",prop:"Demand_Begin_Date"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"picker-options":t.pickerOptions,"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.pickerChange},model:{value:t.Demand_Date,callback:function(e){t.Demand_Date=e},expression:"Demand_Date"}})],1),a("el-form-item",{attrs:{label:"轴网（横）",prop:"Axis_X"}},[a("el-input",{attrs:{placeholder:"请输入","show-word-limit":"",maxlength:50,clearable:""},model:{value:t.form.Axis_X,callback:function(e){t.$set(t.form,"Axis_X",e)},expression:"form.Axis_X "}})],1),a("el-form-item",{attrs:{label:"轴网（纵）",prop:"Axis_Y"}},[a("el-input",{attrs:{placeholder:"请输入","show-word-limit":"",maxlength:50,clearable:""},model:{value:t.form.Axis_Y,callback:function(e){t.$set(t.form,"Axis_Y",e)},expression:"form.Axis_Y"}})],1),a("el-divider"),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:t.close}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:t.btnDisabled,loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确定")])],1)],1)],1)},r=[]},"6d7a":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("5530"));a("7db0"),a("e9f5"),a("f665"),a("d3b7");var i=a("8975"),o=a("66f9");e.default={props:{treeData:{type:Array,default:function(){return[]}}},data:function(){return{pickerOptions:{disabledDate:function(t){return!1}},form:{Name:"",Estimate_Count:"",Discreet_Value:"",Demand_Begin_Date:"",Demand_End_Date:"",Axis_Y:"",Axis_X:"",Level:1},countMax:void 0,weightMax:void 0,btnDisabled:!1,btnLoading:!1,disableSingle:!1,rules:{Name:[{required:!0,message:"请输入",trigger:"blur"}],Demand_Begin_Date:[{required:!0,message:"请选择时间",trigger:"change"}]}}},computed:{Demand_Date:{get:function(){return[(0,i.timeFormat)(this.form.Demand_Begin_Date),(0,i.timeFormat)(this.form.Demand_End_Date)]},set:function(t){if(t){var e=t[0],a=t[1];this.form.Demand_Begin_Date=(0,i.timeFormat)(e),this.form.Demand_End_Date=(0,i.timeFormat)(a)}else this.form.Demand_Begin_Date="",this.form.Demand_End_Date=""}}},mounted:function(){this.ProjectId=this.$route.query.id},methods:{handleSubmit:function(){var t=this,e=this.$parent.$parent.$refs.tree.treeData;!e.find((function(e){return e.Label===t.form.Name}))||this.form.ParentId||this.isEdit?this.$refs["form"].validate((function(e){var a;e&&(t.btnLoading=!0,a=t.form.Id?o.EditArea:o.AddArea,a((0,r.default)((0,r.default)({},t.form),{},{Project_Id:t.ProjectId,Factory_Id:localStorage.getItem("CurReferenceId")})).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.close(),t.$emit("getTreeList")):t.$message({message:e.Message,type:"error"})})).finally((function(){t.btnLoading=!1})))})):this.$message({message:"一级区域不能重名",type:"warning"})},close:function(){this.$emit("close")},getInfo:function(t,e){if(this.isEdit=!0,1===e.level){var a=this.getFirstAreaRangeDate(t),n=a.startTime,r=a.endTime;this.pickerOptions.disabledDate=function(t){return t.getTime()<new Date(r).valueOf()&&t.getTime()>new Date(n).valueOf()}}else{var i=e.parent.data.Data,o=i.Demand_Begin_Date,s=i.Demand_End_Date;this.setPickRange(o,s)}this.nodeData=t,this.form=Object.assign(this.form,t.Data)},getFirstAreaRangeDate:function(t){var e,a,n=t.Children;if(n.length){e=Date.parse(n[0].Data.Demand_Begin_Date),a=Date.parse(n[0].Data.Demand_End_Date);for(var r=1;r<n.length;r++){var i=n[r].Data,o=Date.parse(i.Demand_Begin_Date),s=Date.parse(i.Demand_End_Date);o<e&&(e=o),a<s&&(a=s)}}return{startTime:e,endTime:a}},pickerChange:function(t){var e=t[0],a=t[1];if(this.isEdit){var n=this.getFirstAreaRangeDate(this.nodeData),r=n.startTime,i=n.endTime;Date.parse(e)>i||Date.parse(a)<r?(this.$message({message:"时间应包含内部区域时间范围",type:"warning"}),this.btnDisabled=!0):this.btnDisabled=!1}},setSingle:function(t){this.form.MonomerId=t,this.disableSingle=!0},setRange:function(t){var e=null===t||void 0===t?void 0:t.Data,a=e.Demand_Begin_Date,n=e.Demand_End_Date;e.Discreet_Value,e.Estimate_Count;this.setPickRange(a,n)},setPickRange:function(t,e){this.pickerOptions.disabledDate=function(a){return a.getTime()<new Date(t).valueOf()||a.getTime()>new Date(e).valueOf()}},setArea:function(t,e){this.nodeData=e,this.form.ParentId=t.Id,this.form.Level=2,this.disableSingle=!0}}}},"70db":function(t,e,a){"use strict";a("d6bf")},"73f4":function(t,e,a){"use strict";a.r(e);var n=a("d7e4"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"7e39":function(t,e,a){"use strict";a.r(e);var n=a("142c"),r=a("2d0a");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("a5bb");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"7f1c8d9f",null);e["default"]=s.exports},"897c":function(t,e,a){"use strict";a.r(e);var n=a("4177"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"8bc2d":function(t,e,a){},a1e45:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"项目名称："}},[t._v(" "+t._s(t.projectName)+" ")]),a("el-form-item",{attrs:{label:"下载模板"}},[a("el-button",{attrs:{type:"success",plain:""},on:{click:t.handleDownload}},[t._v("安装单元模版")])],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("upload",{ref:"file",attrs:{"before-upload":t.beforeUpload,"btn-text":t.fileName,limit:1,accept:".xls","btn-icon":"el-icon-upload","btn-size":"middle"}})],1),a("el-divider"),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确定")])],1)],1)],1)},r=[]},a5bb:function(t,e,a){"use strict";a("8bc2d")},afe06:function(t,e,a){},ca15:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container abs100"},[a("div",{staticClass:"top-x"},[a("el-button",{on:{click:t.goBack}},[t._v("返回")])],1),a("div",{staticClass:"card-x"},[a("unit-left-card",{ref:"tree",on:{refresh:t.getTreeData,nodeClick:t.treeNodeClick,addSingle:t.handleAddSingle,addRegion:t.handleAddRegion}}),t.isAreaNode?a("div",{staticClass:"right-card"},[a("div",{staticClass:"top-tb-x"},[a("h3",[t._v("区域信息")]),a("el-form",[a("el-row",t._l(t.areaInfo,(function(e){return a("el-col",{key:e.code,attrs:{span:12}},[a("el-form-item",{ref:"form",refInFor:!0,attrs:{label:e.label,"label-width":"150px"}},[a("el-input",{attrs:{disabled:"",readonly:""},model:{value:t.areaForm[e.code],callback:function(a){t.$set(t.areaForm,e.code,a)},expression:"areaForm[item.code]"}})],1)],1)})),1)],1)],1),a("h3",{staticStyle:{"padding-left":"16px","margin-top":"0"}},[t._v("批次信息")]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper cs-plm-dy-table",attrs:{"element-loading-text":"拼命加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",attrs:{config:t.tbConfig,"select-width":65,border:"",columns:t.columns,data:t.tbData,page:t.queryInfo.Page,total:t.total,stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,tableSearch:t.tableSearch,multiSelectedChange:t.getSelectVal},scopedSlots:t._u([{key:"Plan_Date",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Plan_Date))+" ")]}},{key:"Ultimate_Demand_Date",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Ultimate_Demand_Date))+" ")]}}],null,!1,4289749545)})],1)]):t._e()],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[t.dialogVisible?a(t.currentComponent,{ref:"content",tag:"component",attrs:{"project-id":t.projectId,"area-id":t.queryInfo.Area_Id,"monomer-id":t.monomerId,"tree-data":t.treeData},on:{close:t.handleClose,getTbInfo:function(e){return t.fetchData(1)},getTreeList:t.getTreeData}}):t._e()],1)],1)},r=[]},cba4:function(t,e,a){"use strict";a.r(e);var n=a("037e"),r=a("73f4");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("d1b1");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"9cf94bec",null);e["default"]=s.exports},d1b1:function(t,e,a){"use strict";a("dffb")},d4d4:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("c14f")),i=n(a("1da1"));a("4de4"),a("d81d"),a("b0c0"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var o=a("66f9"),s=a("a667");e.default={data:function(){return{filterText:"",hasSingle:!1,treeLoading:!1,projectName:"",treeData:[],currentKey:"",Is_Integration:!1}},computed:{btnDisabled:function(){return 1===parseInt(this.$route.query.status)}},watch:{filterText:function(t){this.$refs.tree.filter(t)}},created:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:t.Is_Integration=e.v,t.projectName=decodeURIComponent(t.$route.query.name),t.projectId=t.$route.query.id,t.getNewAreaList();case 2:return e.a(2)}}),e)})))()},methods:{loadMore:function(t,e){var a=this;return(0,i.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:if(0!==t.level){n.n=2;break}return n.n=1,a.getTreeData();case 1:return a.treeNode=t,a.treeResolve=e,n.a(2,e(a.treeData));case 2:if(1!==t.level){n.n=3;break}return a.currentKey=t.data.Id,n.a(2,e(a.areaResult));case 3:if(!(t.level>1)){n.n=4;break}return n.a(2,e([]));case 4:return n.a(2)}}),n)})))()},refreshNode:function(){this.getNewAreaList()},getAreaList:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,s.GetAreaPageList)({PageInfo:{Page:1,PageSize:99999},ProjectId:t.projectId,MonomerId:t.currentKey}).then((function(e){t.areaResult=e.Data.Data.map((function(t){return t.leaf=!0,t}))}));case 1:return e.a(2)}}),e)})))()},getNewAreaList:function(){var t=this;(0,o.GeAreaTrees)({ProjectId:this.projectId,parentAreaId:""}).then((function(e){e.IsSucceed?(t.treeData=e.Data,t.treeData.length&&(t.currentKey=t.treeData[0].Id,t.$nextTick((function(e){t.$refs["tree"].setCurrentKey(t.currentKey);var a=t.$refs["tree"].getNode(t.currentKey);t.$emit("nodeClick",{data:t.treeData[0].Data,node:a})})))):t.$message({message:e.Message,type:"error"})}))},getTreeData:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,o.GetProMonomerList)({projectId:t.projectId,useTree:!0}).then((function(e){if(e.IsSucceed){var a=e.Data;a.Data?(t.treeData=a.Data.map((function(t){return t.leaf=!0,t})),t.hasSingle=!1):(t.treeData=a,t.hasSingle=!0),t.currentKey=""}else t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},addRegion:function(){this.$emit("addRegion")},handleEdit:function(t){this.$emit("addRegion",{data:t,isEdit:!0})},handleAdd:function(t){this.$emit("addRegion",{data:t,isFromTree:!0})},handleDelete:function(t){var e=this;this.$confirm("此操作将删除该选项, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.AreaDelete)({id:t.Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleNodeButtonDelete:function(){},handleOpenAddEdit:function(){},handleNodeClick:function(t,e){this.currentKey=t.Id,this.$emit("nodeClick",{data:t,node:e})}}}},d6bf:function(t,e,a){},d7e4:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7");var n=a("66f9");e.default={props:{projectId:{type:String,default:""}},data:function(){return{value:"",btnLoading:!1}},methods:{getInfo:function(t,e){this.info=t,this.value=t.Name,this.isEdit=e},handleSubmit:function(){var t,e,a=this;this.btnLoading=!0,this.isEdit?(t=n.EditMonomer,e={Id:this.info.Id,Name:this.value,Project_Id:this.projectId}):(t=n.AddMonomer,e={Project_Id:this.projectId,Name:this.value}),t(e).then((function(t){t.IsSucceed?(a.$message({message:a.isEdit?"修改成功":"添加成功",type:"success"}),a.close(),a.$emit("getTreeList")):a.$message({message:t.Message,type:"error"})})).finally((function(t){a.btnLoading=!1}))},close:function(){this.$emit("close")}}}},dffb:function(t,e,a){},e4a7:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("b0c0"),a("d3b7");var r=n(a("c7f0")),i=a("f2f6"),o=a("ed08");e.default={components:{Upload:r.default},props:{areaId:{type:String,default:""}},data:function(){return{form:{ProjectId:""},options:[],btnLoading:!1,rules:{},fileName:"选择文件",projectName:""}},mounted:function(){this.projectName=decodeURIComponent(this.$route.query.name),this.form.ProjectId=this.$route.query.id},methods:{handleSubmit:function(){this.ProjectId?this.$refs.file.handleSubmit():this.$message({message:"请选择项目",type:"info"})},beforeUpload:function(t){var e=this,a=new FormData;a.append("ProjectId",this.form.ProjectId),a.append("areaId",this.areaId),a.append("files",t),this.btnLoading=!0,(0,i.ImportInstallUnit)(a).then((function(t){if(t.IsSucceed)e.$message({message:"导入成功",type:"success"}),e.$emit("getTbInfo");else if(e.$message({message:t.Message,type:"error"}),t.Data){var a=(0,o.combineURL)(e.$baseUrl,t.Data);window.open(a,"_blank")}e.$emit("close")})).finally((function(){e.btnLoading=!1}))},handleDownload:function(){var t=this;(0,i.InstallUnitInfoTemplate)({}).then((function(e){e.IsSucceed?window.open((0,o.combineURL)(t.$baseUrl,e.Data)):t.$message({message:e.Message,type:"error"})}))},handleUpload:function(){}}}},edf4:function(t,e,a){"use strict";a.r(e);var n=a("a1e45"),r=a("52f2");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"15c79a30",null);e["default"]=s.exports},f2f6:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=l,e.CheckPlanTime=d,e.DeleteInstallUnit=m,e.GetCompletePercent=D,e.GetEntity=v,e.GetInstallUnitAllInfo=c,e.GetInstallUnitComponentPageList=_,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=u,e.GetInstallUnitList=s,e.GetInstallUnitPageList=o,e.GetProjectInstallUnitList=b,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=y;var r=n(a("b775")),i=n(a("4328"));function o(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function d(t){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function u(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function c(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:i.default.stringify(t)})}function y(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},f70d:function(t,e,a){"use strict";a("afe06")}}]);