(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-8eb1768c"],{"5cf7":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return s}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100"},[a("div",{staticClass:"app-container h100"},[a("div",{staticClass:"top-btn",on:{click:e.toBack}},[a("el-button",[e._v("返回")])],1),a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-main",style:1==e.typeData.type?"padding-bottom: 0px;":""},[a("div",{staticClass:"basic-information"},[a("header",[e._v("基础信息")]),a("el-form",{ref:"form",staticClass:"demo-form-inline",staticStyle:{"padding-left":"20px"},attrs:{inline:!0,model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"变更单号",prop:"Bill_No"}},[a("el-input",{attrs:{type:"text",disabled:0!=e.typeData.type},on:{input:function(t){e.form.Bill_No=e.form.Bill_No.replace(/[^\w\.\/]/gi,"")}},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No","string"===typeof t?t.trim():t)},expression:"form.Bill_No"}})],1),a("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[0==e.typeData.type?a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.typeList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1):a("el-input",{attrs:{value:e.form.Moc_Type_Name,type:"text",disabled:""}})],1)],1)],1),a("div",{staticClass:"basic-information last-item"},[a("header",[e._v("变更业务")]),a("el-form",{ref:"form2",staticClass:"demo-form-inline",staticStyle:{"padding-left":"20px"},attrs:{inline:!0,model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[1!=e.typeData.type?a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.projectChange},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Short_Name}})})),1):a("el-input",{attrs:{value:e.form.ProjectName,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"项目编号",prop:"Sys_Project_Id"}},[1!=e.typeData.type?a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Code}})})),1):a("el-input",{attrs:{value:e.form.ProjectCode,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"处理人",prop:"Handle_UserId"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:"",disabled:1==e.typeData.type},model:{value:e.form.Handle_UserId,callback:function(t){e.$set(e.form,"Handle_UserId",t)},expression:"form.Handle_UserId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1),a("el-form-item",{attrs:{label:"抄送人",prop:"CC_UserId"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:"",disabled:1==e.typeData.type},model:{value:e.form.CC_UserId,callback:function(t){e.$set(e.form,"CC_UserId",t)},expression:"form.CC_UserId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1)],1)],1),a("div",{staticClass:"change-reason first-item"},[a("header",[e._v("变更原因")]),a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",rows:4,maxlength:"100",placeholder:"请输入内容",disabled:1==e.typeData.type},model:{value:e.form.Reason,callback:function(t){e.$set(e.form,"Reason",t)},expression:"form.Reason"}})],1),a("div",{staticClass:"change-reason"},[a("header",[e._v("变更内容")]),a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",rows:4,maxlength:"100",placeholder:"请输入内容",disabled:1==e.typeData.type},model:{value:e.form.Content,callback:function(t){e.$set(e.form,"Content",t)},expression:"form.Content"}})],1),a("div",{class:1==e.typeData.type?"file-items last-item":"file-items"},[a("header",[e._v("资料附件")]),a("div",{staticClass:"file-up"},[a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:e.allowFile,"file-list":e.fileList,multiple:"",limit:10,"on-success":function(t,a,r){e.uploadSuccess(t,a,r)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.uploadExceed,disabled:1==e.typeData.type}},[a("i",{staticClass:"el-icon-upload"}),1!=e.typeData.type?a("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),a("em",[e._v("点击上传")])]):a("div",{staticClass:"el-upload__text"},[e._v("查看详情，禁止上传")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 文件上传数量最多为10个 ")])])],1)])]),1!=e.typeData.type?a("div",{staticClass:"submit-btn"},[a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("提交")])],1):e._e()])])])},s=[]},"99ee":function(e,t,a){"use strict";a("ac470")},ac470:function(e,t,a){},d25f:function(e,t,a){"use strict";a.r(t);var r=a("f2fe"),s=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=s.a},d6e9:function(e,t,a){"use strict";a.r(t);var r=a("5cf7"),s=a("d25f");for(var o in s)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(o);a("99ee");var l=a("2877"),i=Object(l["a"])(s["default"],r["a"],r["b"],!1,null,"6cfb3429",null);t["default"]=i.exports},f2fe:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var s=r(a("c14f")),o=r(a("1da1")),l=(a("d51a"),r(a("bbc2"))),i=a("ed08"),n=a("ecb3"),c=a("1b69"),d=a("6186");t.default={name:"PROQualityManagement",components:{OSSUpload:l.default},data:function(){return{btnLoading:!1,form:{Bill_No:"",Moc_Type_Id:"",Sys_Project_Id:"",Handle_UserId:"",CC_UserId:"",Reason:"",Content:"",Moc_Type_Name:"",ProjectName:"",ProjectCode:"",Handle_UserName:"",CC_UserName:""},rules:{Bill_No:[{required:!0,message:"请选择",trigger:"blur"}],Moc_Type_Id:[{required:!0,message:"请选择",trigger:"change"}],Sys_Project_Id:[{required:!0,message:"请选择",trigger:"change"}]},fileList:[],Attachments:[],typeList:[],projectList:[],userList:[],allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",typeData:{Id:"",type:0},OrdeDetail:{}}},created:function(){var e=this;return(0,o.default)((0,s.default)().m((function t(){return(0,s.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getBasicData();case 1:e.typeData.Id=e.$route.query.data.Id,e.typeData.type=e.$route.query.data.type,0!=e.typeData.type&&e.getChangeOrdeDetail(e.typeData.Id);case 2:return t.a(2)}}),t)})))()},methods:{getBasicData:function(){var e=this;(0,n.GetChangeType)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.typeList=t.Data.Data)})),(0,c.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)})),(0,n.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)}))},getChangeOrdeDetail:function(e){var t=this;(0,n.GetChangeOrdeDetail)({id:e}).then((function(e){if(e.IsSucceed){t.OrdeDetail=e.Data,t.form.Bill_No=t.OrdeDetail.Bill_No,t.form.Moc_Type_Id=t.OrdeDetail.Moc_Type_Id,t.form.Sys_Project_Id=t.OrdeDetail.Sys_Project_Id,t.form.Handle_UserId=t.OrdeDetail.Handle_UserId.split(","),t.form.Handle_UserId.pop(),t.form.CC_UserId=t.OrdeDetail.CC_UserId.split(","),t.form.CC_UserId.pop(),t.form.Reason=t.OrdeDetail.Reason,t.form.Content=t.OrdeDetail.Content;var a=t.typeList.find((function(e){return e.Id==t.form.Moc_Type_Id}));if(t.form.Moc_Type_Name=a.Display_Name,0!=t.OrdeDetail.AttachmentList.length&&t.OrdeDetail.AttachmentList.map((function(e){var a={name:"",url:""};a.name=e.File_Name,a.url=e.File_Url,t.fileList.push(a)})),1==t.typeData.type){var r=t.projectList.find((function(e){return e.Sys_Project_Id==t.form.Sys_Project_Id}));t.form.ProjectName=r.Short_Name,t.form.ProjectCode=r.Code}}else t.$message({message:e.Message,type:"error"})}))},projectChange:function(e){},uploadSuccess:function(e,t,a){var r=this;this.Attachments=[],a.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,r.Attachments.push(t)}))},uploadExceed:function(e,t){this.$message({type:"warning",message:"已超过文件上传最大数量"})},uploadRemove:function(e,t){var a=this;this.Attachments=[],t.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,a.Attachments.push(t)}))},handlePreview:function(e){var t=null;t=e.hasOwnProperty("response")?e.response.encryptionUrl:e.url,this.openGetOssUrl(t)},openGetOssUrl:function(e){(0,d.GetOssUrl)({url:e,day:30}).then((function(e){window.open(e.Data)}))},submit:function(){var e=this;this.$refs.form.validate((function(t){var a=!0;if(t||(a=t),e.$refs.form2.validate((function(e){e||(a=e)})),a){e.btnLoading=!0;var r=e.form,s=r.Bill_No,o=r.Moc_Type_Id,l=r.Sys_Project_Id,c=r.Handle_UserId,d=r.CC_UserId,p=r.Reason,u=r.Content,f={Bill_No:s,Moc_Type_Id:o,Sys_Project_Id:l,Handle_UserId:c,CC_UserId:d,Reason:p,Content:u,AttachmentList:e.Attachments,Id:e.typeData.Id||""},m="",_="";f.Handle_UserId.length>0&&f.Handle_UserId.forEach((function(e){m+=e+","})),f.CC_UserId.length>0&&f.CC_UserId.forEach((function(e){_+=e+","})),f.Handle_UserId=m,f.CC_UserId=_,(0,n.SaveChangeOrder)(f).then((function(t){t.IsSucceed?(e.$message({message:"提交成功",type:"success"}),(0,i.closeTagView)(e.$store,e.$route)):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))},toBack:function(){var e=this;1==this.typeData.type?(0,i.closeTagView)(this.$store,this.$route):this.$confirm("此操作不会保存编辑数据，是否退出？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,i.closeTagView)(e.$store,e.$route)})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}}}]);