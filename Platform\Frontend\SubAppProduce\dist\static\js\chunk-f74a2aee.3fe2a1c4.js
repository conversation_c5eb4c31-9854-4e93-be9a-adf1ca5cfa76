(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-f74a2aee"],{1976:function(t,e,n){"use strict";n.r(e);var a=n("8492"),r=n("3310");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"5bda5530",null);e["default"]=s.exports},3310:function(t,e,n){"use strict";n.r(e);var a=n("9372"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},"354d":function(t,e,n){},"42d7":function(t,e,n){"use strict";n("354d")},"42f4":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[n("div",{staticClass:"cs-z-page-main-content"},[n("el-card",{attrs:{shadow:"never"}},[n("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{inline:"",model:t.form,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:8}},[n("el-form-item",[n("router-link",{attrs:{to:{name:t.$route.query.pg_redirect}}},[n("el-button",{attrs:{circle:"",icon:"el-icon-arrow-left",size:"mini"}})],1),n("strong",{staticClass:"title"},[t._v(t._s(t.form.Project_Name))])],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"退货单号：",prop:"region"}},[n("strong",[t._v(t._s(t.form.Id))])])],1),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:4}},[n("el-form-item",[n("el-button",{on:{click:function(t){}}},[t._v("打印")]),n("el-button",{attrs:{type:"success"},on:{click:t.handleExport}},[t._v("导出")])],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:7,offset:1}},[n("el-form-item",{attrs:{label:"退货人员："}},[n("strong",[t._v(t._s(t.form.Cancel_UserName||t.form.Return_UserName))])])],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"退货时间："}},[n("strong",[t._v(" "+t._s(t._f("timeFormat")(t.form.Cancel_Date||t.form.Return_Date,"{y}-{m}-{d} {h}:{i}:{s}")))])])],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"备注："}},[t._v(" "+t._s(t.form.Remark)+" ")])],1)],1)],1)],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[t.tableData?n("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,config:t.tbConfig,data:t.tableData,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"op",fn:function(e){var a=e.row,r=e.index;return["打包件"===a.C_Type?n("el-button",{attrs:{index:r,type:"text"},on:{click:function(e){return t.handleInfo(a)}}},[t._v("查看")]):t._e()]}}],null,!1,181305867)}):t._e()],1),n("check-info",{ref:"info"})],1)])},r=[]},"5b7d":function(t,e,n){"use strict";n.r(e);var a=n("5efc"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a},"5efc":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(n("0f97")),o=a(n("15ac")),i=a(n("4b32"));e.default={components:{DynamicDataTable:r.default,CheckInfo:i.default},mixins:[o.default],props:{form:{type:Object,default:function(){}},tableData:{type:Array,default:function(){return[]}},tbConfigCode:{type:String,default:""}},data:function(){return{tbLoading:!0,tbConfig:{Pager_Align:"center",Tree_Key:"Component_Id"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},total:0,columns:[]}},mounted:function(){this.getTableConfig(this.tbConfigCode)},methods:{showTb:function(){this.tbLoading=!1},handleInfo:function(t){this.$refs.info.handleOpen(t)},handleExport:function(){this.$emit("export",this.form.Id)}}}},"691a":function(t,e,n){"use strict";n.r(e);var a=n("42f4"),r=n("5b7d");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("42d7");var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"d5738c82",null);e["default"]=s.exports},8492:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("detail",{ref:"detail",attrs:{form:t.form,"table-data":t.tableData,"tb-config-code":"pro_return_detail_list"},on:{export:t.handleExport}})},r=[]},9372:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(n("691a")),o=n("82a3"),i=n("ed08");e.default={name:"PROReturnGoodsDetail",components:{Detail:r.default},data:function(){return{form:{},tableData:[]}},mounted:function(){this.getReturnGoodsEntity(),this.getTbData()},methods:{getTbData:function(){var t=this;(0,o.GetReturnDetailList)({billId:this.$route.query.id}).then((function(e){e.IsSucceed?(t.tableData=e.Data,t.$refs.detail.showTb()):t.$message({message:e.Message,type:"error"})}))},getReturnGoodsEntity:function(){var t=this;(0,o.GetReturnDocEntity)({id:this.$route.query.id}).then((function(e){e.IsSucceed?t.form=e.Data:t.$message({message:e.Message,type:"error"})}))},handleExport:function(t){var e=this;(0,o.ExportReturnInfo)({Id:t}).then((function(t){if(t.IsSucceed){var n=(0,i.combineURL)(e.$baseUrl,t.Data);window.open(n,"_blank")}else e.$message({message:t.Message,type:"error"})}))}}}}}]);