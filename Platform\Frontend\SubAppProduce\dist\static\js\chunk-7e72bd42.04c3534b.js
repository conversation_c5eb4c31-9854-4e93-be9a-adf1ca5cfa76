(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7e72bd42"],{"13bc":function(t,e,n){"use strict";n("25c2")},"25c2":function(t,e,n){},"2dd9":function(t,e,n){"use strict";function a(t,e){t||(t={}),e||(e=[]);var n=[],a=function(a){var i;i="[object Array]"===Object.prototype.toString.call(t[a])?t[a]:[t[a]];var r=i.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(r.filter((function(t){return null!==t})).length<=0&&(r=null),r){var l={Key:a,Value:r,Type:"",Filter_Type:""},o=e.find((function(t){return t.Code===a}));l.Type=null===o||void 0===o?void 0:o.Type,l.Filter_Type=null===o||void 0===o?void 0:o.Filter_Type,n.push(l)}};for(var i in t)a(i);return n}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=a,n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("d3b7"),n("25f0")},"4e82":function(t,e,n){"use strict";var a=n("23e7"),i=n("e330"),r=n("59ed"),l=n("7b0b"),o=n("07fa"),u=n("083a"),s=n("577e"),c=n("d039"),d=n("addb"),f=n("a640"),h=n("3f7e"),p=n("99f4"),m=n("1212"),b=n("ea83"),g=[],v=i(g.sort),P=i(g.push),C=c((function(){g.sort(void 0)})),I=c((function(){g.sort(null)})),y=f("sort"),D=!c((function(){if(m)return m<70;if(!(h&&h>3)){if(p)return!0;if(b)return b<603;var t,e,n,a,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)g.push({k:e+a,v:n})}for(g.sort((function(t,e){return e.v-t.v})),a=0;a<g.length;a++)e=g[a].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),_=C||!I||!y||!D,U=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:s(e)>s(n)?1:-1}};a({target:"Array",proto:!0,forced:_},{sort:function(t){void 0!==t&&r(t);var e=l(this);if(D)return void 0===t?v(e):v(e,t);var n,a,i=[],s=o(e);for(a=0;a<s;a++)a in e&&P(i,e[a]);d(i,U(t)),n=o(i),a=0;while(a<n)e[a]=i[a++];while(a<s)u(e,a++);return e}})},"5fba":function(t,e,n){"use strict";n.r(e);var a=n("cd3b"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},6041:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100 flex-pd16-wrap"},[n("div",{staticClass:"page-main-content  cs-z-shadow"},[n("el-container",{staticStyle:{height:"100%"}},[n("el-header",{staticClass:"art-header"},[n("el-select",{staticStyle:{width:"200px"},attrs:{filterable:"",placeholder:"选择齐套状态"},on:{change:t.readyChange},model:{value:t.filterData.Ready_Status,callback:function(e){t.$set(t.filterData,"Ready_Status",e)},expression:"filterData.Ready_Status"}},[n("el-option",{attrs:{label:"全部",value:0}}),n("el-option",{attrs:{label:"已齐套",value:1}}),n("el-option",{attrs:{label:"未齐套",value:-1}})],1),n("el-button",{on:{click:t.select7Days}},[t._v("7日内待齐套")])],1),n("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"art-main"},[n("DynamicDataTable",{ref:"table",attrs:{columns:t.columns,config:t.tbConfig,data:t.data,page:t.filterData.Page,total:t.filterData.TotalCount,border:""},on:{gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"Bill_Ready",fn:function(e){var a=e.column,i=e.row;return["是"===i[a.Code]?n("el-button",{attrs:{round:"",size:"mini",type:"success"},on:{click:function(e){return t.openPageTab("PrepareCheck",i["Id"],{row:i})}}},[t._v("是 ")]):n("el-button",{attrs:{round:"",size:"mini",type:"danger"},on:{click:function(e){return t.openPageTab("PrepareCheck",i["Id"],{row:i})}}},[t._v("否 ")])]}},{key:"Percentage",fn:function(e){var a=e.column,i=e.row;return[n("el-tag",{staticStyle:{width:"92%",cursor:"pointer"},attrs:{type:Number(i[a.Code])<100?"":"success"},on:{click:function(e){return t.openPageTab("PrepareDetail",i["Id"],{row:i})}}},[t._v(t._s(Number(i[a.Code]))+"% ")])]}},{key:"Suggest_Produce_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Suggest_Produce_Date))+" ")]}},{key:"Bill_Finish_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Bill_Finish_Date))+" ")]}}])})],1)],1)],1)])},i=[]},"65ca":function(t,e,n){"use strict";n("86db")},7336:function(t,e,n){"use strict";n.r(e);var a=n("6041"),i=n("5fba");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("13bc"),n("65ca");var l=n("2877"),o=Object(l["a"])(i["default"],a["a"],a["b"],!1,null,"371a1868",null);e["default"]=o.exports},"86db":function(t,e,n){},cd3b:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("14d9"),n("b0c0"),n("e9f5"),n("7d54"),n("ab43"),n("e9c4"),n("a9e3"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0");var i=a(n("0f97")),r=n("1b69"),l=n("f2f6"),o=n("6186"),u=a(n("2082")),s=a(n("b775")),c=n("2dd9");window.open,e.default={name:"PrepareProduce",components:{DynamicDataTable:i.default},mixins:[u.default],data:function(){return{loading:!0,gridCode:"ProductionPrepare",addPageArray:[{path:this.$route.path+"/detail",hidden:!0,component:function(){return n.e("chunk-62bd9ccb").then(n.bind(null,"6188"))},name:"PrepareDetail",meta:{title:"物料齐套明细"}},{path:this.$route.path+"/check",hidden:!0,component:function(){return n.e("chunk-5ace52b8").then(n.bind(null,"9dbf"))},name:"PrepareCheck",meta:{title:"清单齐套确认"}}],projects:[],units:[],apis:[],tbConfig:{},columns:[],data:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0,Ready_Status:0,Days:0},ParameterJson:[]}},created:function(){var t=this;Promise.all([(0,r.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)})),(0,l.GetInstallUnitList)({}).then((function(e){e.IsSucceed&&(t.units=e.Data)}))]).then((function(){(0,o.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData()}))}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:80}),this.filterData.PageSize=Number(this.tbConfig.Row_Number)},getTableData:function(){var t=this;return this.dynTblOptBak&&this.resetDynTblOpts(),this.tbConfig.Data_Url?(0,s.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{ParameterJson:(0,c.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(e){if(e.IsSucceed)return t.filterData.Days=0,t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1})):Promise.reject("invalid data api...")},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},setCols:function(t){var e=this;t.forEach((function(t){"Project_Name"==t.Code&&(t.Range=JSON.stringify(e.projects.map((function(t){return{label:t.Name,value:t.Name}})))),"Name"===t.Code&&(t.Range=JSON.stringify(e.units.map((function(t){return{label:t.Name,value:t.Name}})))),"Code"===t.Code&&(t.Range=JSON.stringify(e.units.map((function(t){return{label:t.Name,value:t.Code}}))))})),this.columns=t},filterChange:function(t){this.filterData.Page=null!==t&&void 0!==t?t:1,this.getTableData()},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange(1)},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.PageSize=e,this.filterData.Page=1,this.filterChange(1)},readyChange:function(t){this.filterData.Ready_Status=t,this.filterChange(1)},openPageTab:function(t,e,n){this.$router.push({name:t,query:{id:e,pg_redirect:this.$route.name},params:n})},select7Days:function(){this.filterData.Days=7,this.filterChange(1)}}}},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=u,e.CheckPlanTime=s,e.DeleteInstallUnit=h,e.GetCompletePercent=v,e.GetEntity=C,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=g,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=o,e.GetInstallUnitPageList=l,e.GetProjectInstallUnitList=P,e.ImportInstallUnit=m,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=b,e.SaveOhterSourceInstallUnit=I;var i=a(n("b775")),r=a(n("4328"));function l(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function o(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function s(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function C(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function I(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);