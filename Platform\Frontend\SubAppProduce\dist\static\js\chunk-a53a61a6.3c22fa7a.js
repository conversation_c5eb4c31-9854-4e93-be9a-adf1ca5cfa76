(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-a53a61a6"],{"06a5":function(e,t,a){"use strict";a.r(t);var n=a("3c6c"),i=a("7146");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("b7f2");var o=a("2877"),s=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"65c4cbc2",null);t["default"]=s.exports},"18a7":function(e,t,a){"use strict";a.r(t);var n=a("7ee1"),i=a("75ad");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("cb90");var o=a("2877"),s=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"6be00930",null);t["default"]=s.exports},"1b78":function(e,t,a){"use strict";a("556f")},"22d2":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"title-box"},[a("div",{staticClass:"title"},[e._v(" "+e._s(e.title)+" ")]),a("div",[e._t("default")],2)])},i=[]},"3c6c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pack-container"},[a("div",{staticClass:"statistical-container"},[a("span",[e._v("构件总数 "+e._s(e.packDetail.AllAmount)+" 件")]),a("span",[e._v("构件总重 "+e._s(e.packDetail.AllWeight)+" kg")])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"table-container"},[a("dynamic-data-table",{ref:"table",attrs:{columns:e.columns,config:e.tbConfig,data:e.packDetailList,page:e.form.PageInfo.Page,total:e.total,border:"",stripe:""},scopedSlots:e._u([{key:"Is_Component_Name",fn:function(t){var n=t.row;return[a("div",[n.Is_Component_Name?a("div",["直发件"==n.Is_Component_Name?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])],1):a("div",[e._v(" "+e._s("-")+" ")])])]}}])})],1)])},i=[]},"3f35":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportPackingList=f,t.GeneratePackCode=o,t.GetPacking2ndEntity=l,t.GetPacking2ndPageList=s,t.GetPackingGroupByDirectDetailList=r,t.GetWaitPack2ndPageList=c,t.SavePacking2nd=d,t.UnzipPacking2nd=u;var i=n(a("b775"));n(a("4328"));function r(e){return(0,i.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:e})}function o(){return(0,i.default)({url:"/PRO/Packing/GeneratePackCode",method:"post"})}function s(e){return(0,i.default)({url:"/PRO/Packing/GetPacking2ndPageList",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/Packing/GetPacking2ndEntity",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/Packing/GetWaitPack2ndPageList",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/Packing/UnzipPacking2nd",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/Packing/SavePacking2nd",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PRO/Packing/ExportPackingList",method:"post",data:e})}},"40d1":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("3f35");t.default={data:function(){return{innerTableData:[],dialogVisible:!1}},methods:{handleInfo:function(e){var t=this;(0,n.GetPackingGroupByDirectDetailList)({Unique_Code:e.Unique_Code}).then((function(e){e.IsSucceed?t.innerTableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleOpen:function(e){this.dialogVisible=!0,this.handleInfo(e)},handleClose:function(){}}}},4872:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CarDataTemplate=d,t.DeleteCar=c,t.GetCarList=r,t.GetCarPageList=s,t.GetCurCarPageList=o,t.ImportCar=u,t.SaveCar=l;var i=n(a("b775"));n(a("4328"));function r(e){return(0,i.default)({url:"/PRO/Car/GetCarList",method:"post",data:e})}function o(e){return(0,i.default)({url:"/PRO/Car/GetCurCarPageList",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/Car/GetCarPageList",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/Car/SaveCar",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/Car/DeleteCar",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/Car/ImportCar",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/Car/CarDataTemplate",method:"post",data:e})}},"4b32":function(e,t,a){"use strict";a.r(t);var n=a("a729"),i=a("939e");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("2877"),s=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"784abcb7",null);t["default"]=s.exports},"52fa":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),r=n(a("1da1")),o=(a("d7b0"),n(a("0f97"))),s=n(a("5cc7")),l=a("3f35");t.default={components:{DynamicDataTable:o.default},mixins:[s.default],data:function(){return{tbLoading:!1,ProfessionalType:[],tbConfig:{},columns:[],total:0,form:{PageInfo:{Page:1,PageSize:20}},packDetail:{},packDetailList:[]}},methods:{init:function(e){var t=this;return(0,r.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return t.tbLoading=!0,a.n=1,t.getFactoryTypeOption("pro_waiting_out_detail_package");case 1:t.fetchData(e);case 2:return a.a(2)}}),a)})))()},fetchData:function(e){var t=this;(0,l.GetPacking2ndEntity)({id:e}).then((function(e){e.IsSucceed?(t.packDetail=e.Data.Entity,t.packDetailList=e.Data.Details,t.tbLoading=!1):t.$message({message:e.Message,type:"error"})}))}}}},"556f":function(e,t,a){},"60b4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{title:{type:String,default:""}},data:function(){return{}}}},7146:function(e,t,a){"use strict";a.r(t);var n=a("52fa"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"75ad":function(e,t,a){"use strict";a.r(t);var n=a("c590"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"7ee1":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header"},[a("el-button",{on:{click:e.toBack}},[e._v("返回")])],1)]},proxy:!0},{key:"right",fn:function(){return[a("el-button",{attrs:{type:"primary",loading:e.btnLoading,disabled:e.isClicked},on:{click:e.handlePrint}},[e._v("预览")])]},proxy:!0}])}),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货单号",prop:"receiveNum"}},[a("el-input",{attrs:{disabled:!0,placeholder:"自动生成"},model:{value:e.form.receiveNum,callback:function(t){e.$set(e.form,"receiveNum",t)},expression:"form.receiveNum"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"项目名称",prop:"projectName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.projectName,callback:function(t){e.$set(e.form,"projectName",t)},expression:"form.projectName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"收货人",prop:"receiveName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入内容",disabled:!0},model:{value:e.form.receiveName,callback:function(t){e.$set(e.form,"receiveName",t)},expression:"form.receiveName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"收货人电话",prop:"Receiver_Tel"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",disabled:!0},model:{value:e.form.Receiver_Tel,callback:function(t){e.$set(e.form,"Receiver_Tel",t)},expression:"form.Receiver_Tel"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"车牌号",prop:"LicenseDetail"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.LicenseDetail,callback:function(t){e.$set(e.form,"LicenseDetail",t)},expression:"form.LicenseDetail"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货人",prop:"issueName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.issueName,callback:function(t){e.$set(e.form,"issueName",t)},expression:"form.issueName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货时间",prop:"Out_Date"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Out_Date,callback:function(t){e.$set(e.form,"Out_Date",t)},expression:"form.Out_Date"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"专业",prop:"ProfessionalTypeName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.ProfessionalTypeName,callback:function(t){e.$set(e.form,"ProfessionalTypeName",t)},expression:"form.ProfessionalTypeName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"收货地址",prop:"Address"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Address,callback:function(t){e.$set(e.form,"Address",t)},expression:"form.Address"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"物流费",prop:"Logistics_Fee"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Logistics_Fee,callback:function(t){e.$set(e.form,"Logistics_Fee",t)},expression:"form.Logistics_Fee"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"关联合同",prop:"Contract_Name"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Contract_Name,callback:function(t){e.$set(e.form,"Contract_Name",t)},expression:"form.Contract_Name"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:2,maxRows:2},maxlength:1e3,"show-word-limit":"",type:"textarea",disabled:!0},model:{value:e.form.Remarks,callback:function(t){e.$set(e.form,"Remarks",t)},expression:"form.Remarks"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"附件"}},[e._l(e.fileInfo,(function(t,n){return[a("el-link",{key:n,attrs:{href:t.url,target:"_blank"}},[e._v(e._s(t.name))]),n!==e.fileInfo.length-1?a("el-divider",{key:n,attrs:{direction:"vertical"}}):e._e()]}))],2)],1)],1)],1),a("div",[a("h4",[e._v(" 过磅信息 ")]),a("el-form",{ref:"form",attrs:{inline:"",model:e.weightform,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"皮重"}},[e._v(" "+e._s(e.weightform.Tare_Weight)+"kg ")]),a("el-form-item",{attrs:{label:"理重"}},[e._v(" "+e._s(e.weightform.Reason_Weight)+"kg ")]),a("el-form-item",{attrs:{label:"磅重"}},[e._v(" "+e._s(e.weightform.Pound_Weight)+"kg ")]),a("el-form-item",{attrs:{label:"净重",prop:"region"}},[a("span",{class:{"cs-red":e.showRed}},[e._v(e._s(e.netWeight)+" "),e.showRed?a("span",[e._v("（"+e._s(e.netWeight-e.weightform.Reason_Weight>0?"高于":"低于")+"理重"+e._s(Math.abs(+e.getNum(e.netWeight,e.weightform.Reason_Weight)))+"kg）")]):e._e()])]),a("el-form-item",{attrs:{label:"过磅备注",prop:"region"}},[e._v(" "+e._s(e.plm_ProjectSendingInfo.Pound_Remark)+" ")]),a("el-form-item",{attrs:{label:"附件"}},[e._l(e.weightFileInfo,(function(t,n){return[a("el-link",{key:n,attrs:{href:t.url,target:"_blank"}},[e._v(e._s(t.name))]),n!==e.weightFileInfo.length-1?a("el-divider",{key:n,attrs:{direction:"vertical"}}):e._e()]}))],2)],1)],1),a("top-header",{staticStyle:{"margin-bottom":"10px"},scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header",staticStyle:{"margin-bottom":"20px"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:e.radioChange},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[a("el-radio-button",{attrs:{label:"pro_component_out_detail_list"}},[e._v("构件")]),a("el-radio-button",{attrs:{label:"pro_package_out_detail_list"}},[e._v("打包件")]),a("el-radio-button",{attrs:{label:"PROShipUnitPart"}},[e._v("部件")]),a("el-radio-button",{attrs:{label:"PROShipPart"}},[e._v("零件")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[e.sendNumber?a("div",{staticClass:"statistics-container"},[a("div",{staticClass:"statistics-item",staticStyle:{"margin-right":"0"}},[a("span",{staticClass:"cs-label"},[e._v("发货序号：")]),a("span",{staticClass:"cs-num"},[e._v(e._s(e.sendNumber))]),a("span",{staticClass:"cs-label"},[e._v("发货数量（件）：")]),a("span",{staticClass:"cs-num"},[e._v(e._s(e.totalNum))]),a("span",{staticClass:"cs-label"},[e._v("发货总重（kg）：")]),a("span",{staticClass:"cs-num"},[e._v(e._s(e.totalWeight))])])]):e._e()]},proxy:!0}])}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,config:e.tbConfig,data:2===e.tabTypeCode?e.tbData2:1===e.tabTypeCode?e.tbData:3===e.tabTypeCode?e.tbData3:e.tbData4,page:e.form.PageInfo.Page,"sum-values":e.sums,"select-width":70,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"PackageSn",fn:function(t){var n=t.row;return[a("div",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.handleDetail(n)}}},[e._v(e._s(n.PackageSn))])]}}])})],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width,top:e.topDialog},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible,"project-id":e.projectId,"sys-project-id":e.form.ProjectId,"add-radio":e.addradio,"is-pack":e.Is_Pack},on:{addCarData:e.addCarData,close:e.close,reCount:e.getTotal,refresh:e.fetchData,selectList:e.addSelectList}})],1):e._e(),a("check-info",{ref:"info"})],1)])},i=[]},"939e":function(e,t,a){"use strict";a.r(t);var n=a("40d1"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"946c":function(e,t,a){},97745:function(e,t,a){},a131:function(e,t,a){"use strict";a.r(t);var n=a("22d2"),i=a("c918");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("1b78");var o=a("2877"),s=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"9c596852",null);t["default"]=s.exports},a729:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"提示","append-to-body":"",visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-table",{staticClass:"inner-tb",staticStyle:{width:"100%"},attrs:{data:e.innerTableData}},[a("el-table-column",{attrs:{align:"center",prop:"InstallUnit_Name",label:"生产单元"}}),a("el-table-column",{attrs:{align:"center",prop:"Component_Code",label:"编号",width:"180"}}),a("el-table-column",{attrs:{align:"center",prop:"Unique_Code",label:"唯一码"}}),a("el-table-column",{attrs:{align:"center",prop:"Spec",label:"规格型号"}}),a("el-table-column",{attrs:{align:"center",prop:"Num",label:"数量"}}),a("el-table-column",{attrs:{align:"center",prop:"NetWeight",label:"总重（kg）"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)],1)},i=[]},b7f2:function(e,t,a){"use strict";a("946c")},c590:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var i=n(a("5530")),r=n(a("c14f")),o=n(a("1da1")),s=n(a("a131")),l=a("9643"),c=n(a("0f97")),u=n(a("06a5")),d=a("4872"),f=n(a("34e9")),m=n(a("4b32")),p=a("ed08"),g=a("f4f2"),h=a("1b69"),b=a("6186"),_=a("fd31"),v=a("a667"),P=n(a("6612")),C=a("0e9a"),k={com:1,package:2,unitPart:3,part:4};t.default={components:{TitleInfo:s.default,TopHeader:f.default,DynamicDataTable:c.default,CheckInfo:m.default,packDetail:u.default},data:function(){return{tabTypeCode:1,radio:"pro_component_out_detail_list",addradio:"pro_waiting_out_list",isEdit:!0,Is_Pack:!1,isClicked:!1,width:"40%",btnLoading:!1,currentComponent:"",title:"",dialogVisible:!1,topDialog:"1vh",sendNumber:"",totalNum:"",totalWeight:"",form:{ProjectId:"",Out_Date:"",Remarks:"",Contact_UserName:"",Mobile:"",License:"",LicenseDetail:"",Address:"",receiveName:"",issueName:"",Receiver_Tel:"",Area_Id:"",projectName:"",receiveNum:"",ProfessionalTypeName:"",Logistics_Fee:"",Contract_Name:"",PageInfo:{ParameterJson:[],Page:1,PageSize:20}},plm_ProjectSendingInfo:{},produced_Components:[],projectSendingInfo_Item:[],Itemdetail:[],PackagesList:[],ProfessionalType:[],carOptions:[],projects:"",Id:"",projectId:"",planTime:"",showDialog:!1,tbConfig:{Pager_Align:"center"},columns:[],tbData:[],tbData2:[],tbData3:[],tbData4:[],total:0,fileInfo:[],weightFileInfo:[],tbLoading:!1,selectList:[],sums:[],weightform:{Tare_Weight:0,Reason_Weight:0,Pound_Weight:0,Net_Weight:0,Weigh_Warning_Threshold:0},contractOptions:[]}},computed:{netWeight:function(){return this.weightform.Pound_Weight&&this.weightform.Tare_Weight?this.getNum(this.weightform.Pound_Weight,this.weightform.Tare_Weight):0},showRed:function(e){var t=e.netWeight;return Math.abs(t-this.weightform.Reason_Weight)>=this.weightform.Weigh_Warning_Threshold}},watch:{"tbData.length":{handler:function(){this.getTotal()}}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tabTypeCode=k.com,e.getFactoryTypeOption(),e.getAllCarList();case 1:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a,n,i,o,s,l,c,u;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(!e.isEdit){t.n=2;break}return t.n=1,e.getInfo();case 1:t.n=3;break;case 2:a=JSON.parse(decodeURIComponent(e.$route.query.p)),n=a.Name,i=a.Id,o=a.Code,s=a.Address,a.Receiver,a.Receiver_Tel,a.ProjectId,a.Area_Id,a.InstallUnit_Id,l=a.Sys_Project_Id,a.Receive_UserName,a.ProfessionalType,c=a.Logistics_Fee,u=a.Contract_Name,e.projectId=i,e.Project_Code=o,e.form.projectName=n,e.form.Address=s,e.form.ProjectId=l,e.form.issueName=e.$store.state.user.name,e.form.Logistics_Fee=c,e.form.Contract_Name=u,e.getProjectEntity(e.projectId);case 3:return t.n=4,e.getContractList();case 4:return t.a(2)}}),t)})))()},methods:{getNum:function(e,t){return(0,P.default)(e).subtract(t).format("0.[000]")},getFactoryTypeOption:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,_.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?(e.ProfessionalType=t.Data,e.form.ProfessionalTypeName=e.ProfessionalType[0].Name):e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_component_out_detail_list,".concat(e.ProfessionalType[0].Code));case 2:return t.a(2)}}),t)})))()},getProjectEntity:function(e){var t=this;(0,h.GetProjectEntity)({Id:e}).then((function(e){if(e.IsSucceed){var a=e.Data.Contacts.find((function(e){return"Consignee"==e.Type}));t.form.receiveName=a.Name,t.form.Receiver_Tel=a.Tel}}))},getProjectPageList:function(){var e=this;(0,h.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projects=t.Data.Data)}))},toBack:function(){(0,p.closeTagView)(this.$store,this.$route)},radioChange:function(e){"pro_component_out_detail_list"===e?(this.addradio="pro_waiting_out_list",this.Is_Pack=!1,this.tabTypeCode=k.com,this.getTableConfig("".concat(e,",").concat(this.ProfessionalType[0].Code))):"pro_package_out_detail_list"===e?(this.addradio="pro_waiting_out_list_package",this.Is_Pack=!0,this.tabTypeCode=k.package,this.getTableConfig("".concat(e,",").concat(this.ProfessionalType[0].Code))):"PROShipUnitPart"===e?(this.addradio="PROShipAddUnitPart",this.tabTypeCode=k.unitPart,this.getTableConfig("".concat(e)),this.Is_Pack=!1):"PROShipPart"===e&&(this.addradio="PROShipAddPart",this.Is_Pack=!1,this.tabTypeCode=k.part,this.getTableConfig("".concat(e)))},handleSubmit:function(){},getInfo:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetProjectsendinginEntity)({Id:e.$route.query.id}).then(function(){var t=(0,o.default)((0,r.default)().m((function t(a){var n,s,l,c,u,d,f,m,g,h,b,_,v,k,I,y,D,N,w,L,T,S;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(!a.IsSucceed){t.n=5;break}if(e.plm_ProjectSendingInfo=a.Data.Plm_ProjectSendingInfo,e.Itemdetail=a.Data.Itemdetail,e.PartList=a.Data.PartList,e.PackagesList=a.Data.PackagesList,e.weightform=a.Data.WeightInfo,n=e.plm_ProjectSendingInfo,n.Id,s=n.Code,l=n.ProjectId,c=n.ProjectName,u=n.Consignee,d=n.ConsigneeTel,f=n.MakerName,m=n.VehicleNo,g=n.DriverName,h=n.Telephone,b=n.Address,_=n.Attachment,v=n.SendDate,k=n.Remarks,I=n.Number,y=n.Logistics_Fee,D=n.Contract_Id,e.form.ProjectId=l,e.form.receiveNum=s,e.form.projectName=c,e.form.receiveName=u,e.form.Receiver_Tel=d,e.form.issueName=f,e.form.License=m,e.form.Contact_UserName=g,e.form.Mobile=h,e.form.Address=b,e.form.Out_Date=(0,p.parseTime)(new Date(v),"{y}-{m}-{d} {h}:{i}:{s}"),e.form.Remarks=k,e.form.LicenseDetail="".concat(m,"(").concat(g," ").concat(h,")"),e.form.Logistics_Fee=y,N=e.contractOptions.find((function(e){return e.Id===D})),e.form.Contract_Name=N?N.ContractName:"",e.sendNumber=I,w=0,L=0,e.Itemdetail.forEach((function(t,a){var n=t.Component_Id,i=t.S_Count,r=t.SteelWeight,o=t.AllWeight,s=t.Name,l=t.Spec,c=t.Length,u=t.WarehouseName,d=t.Code,f=t.LocationName,m=t.Area_Name,p=t.Wait_Stock_Count,g=t.SerialNumber,h={Id:n,Area_Name:m,Name:s,Spec:l,Length:c,WarehouseName:u,Code:d,LocationName:f,S_Count:i,Wait_Stock_Count:p,Netweight:r,AllWeight:o,isOld:!0,SerialNumber:g};w+=i||0,L+=o||0,e.tbData.push(h)})),e.PackagesList.forEach((function(t,a){var n=t.PkgNO,i=t.PackageSn,r=t.AllWeight,o=t.Volume,s=t.AllAmount,l=t.WarehouseName,c=t.DIM,u=t.LocationName,d=t.PackageId,f={PkgNO:n,PackageSn:i,AllWeight:r,AllAmount:s,Volume:o,WarehouseName:l,LocationName:u,isOld:!0,DIM:c,PackageId:d};w+=s||0,L+=r||0,e.tbData2.push(f)})),e.PartList.forEach((function(t,a){var n=(0,i.default)({},t);n.S_Count=n.Amount,n.Netweight=n.Weight,n.AllWeight=e.getAllWeight(n),n.Total_Weight=(0,P.default)(n.Stock_Count).multiply(n.Weight).value(),n.Part_Grade>0?e.tbData3.push(n):e.tbData4.push(n)})),e.totalNum=(0,P.default)(w).format("0.[00]"),e.totalWeight=(0,P.default)(L).format("0.[00]"),!_){t.n=2;break}return T=_.split(",").map(function(){var t=(0,o.default)((0,r.default)().m((function t(a){var n,i,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n=a.split("?")[0],t.n=1,e.handleUrl(n);case 1:return i=t.v,o=(0,C.getFileNameFromUrl)(n),t.a(2,{url:i,name:o})}}),t)})));return function(e){return t.apply(this,arguments)}}()),t.n=1,Promise.all(T);case 1:e.fileInfo=t.v;case 2:if(!e.weightform.Attachment_Weight){t.n=4;break}return S=e.weightform.Attachment_Weight.split(",").map(function(){var t=(0,o.default)((0,r.default)().m((function t(a){var n,i,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n=a.split("?")[0],t.n=1,e.handleUrl(n);case 1:return i=t.v,o=(0,C.getFileNameFromUrl)(n),t.a(2,{url:i,name:o})}}),t)})));return function(e){return t.apply(this,arguments)}}()),t.n=3,Promise.all(S);case 3:e.weightFileInfo=t.v;case 4:t.n=6;break;case 5:e.$message({message:a.Message,type:"error"});case 6:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}());case 1:return t.a(2)}}),t)})))()},carChange:function(e){var t=this;if(!e)return this.form.Contact_UserName="",this.form.Mobile="",void(this.form.License="");var a=this.carOptions.find((function(e){return e.License===t.form.License}));this.form.Contact_UserName=a.Contact_UserName,this.form.Mobile=a.Mobile,this.form.License=a.License},projectIdChange:function(e){},projectIdClear:function(e){},getAllWeight:function(e){return Number(e.S_Count*e.Netweight).toFixed(2)/1},addSelectList:function(e){var t=this;!1===this.Is_Pack?(this.tbData=this.tbData.filter((function(e){return e.isOld})),e.forEach((function(e){t.tbData.push(e)})),this.total=this.tbData.length):!0===this.Is_Pack&&(this.tbData2=this.tbData2.filter((function(e){return e.isOld})),e.forEach((function(e){t.tbData2.push(e)})),this.total=this.tbData2.length)},handlePrint:function(){var e=this;(0,l.Transforms)({sendId:this.plm_ProjectSendingInfo.Id}).then((function(t){if(t.IsSucceed){var a=new URL(t.Data,(0,g.baseUrl)());window.open(a.href,"_blank"),e.$message({type:"success",message:"打印成功!"})}else e.$message({message:t.Message,type:"error"})}))},multiSelectedChange:function(e){this.selectList=e},fetchData:function(){},getTotal:function(){},getAllCarList:function(){var e=this;return new Promise((function(t){(0,d.GetCurCarPageList)({}).then((function(a){a.IsSucceed?(e.carOptions=a.Data,e.carOptions.forEach((function(t,a){e.$set(t,"detail","".concat(t.License,"(").concat(t.Contact_UserName," ").concat(t.Mobile,")"))})),t()):e.$message({message:a.Message,type:"error"})}))}))},addCarData:function(e){this.carOptions.push(e)},handleAdd:function(){var e=this;this.currentComponent="AddDialog",this.width="65%",this.dialogVisible=!0,!1===this.Is_Pack?(this.title="添加构件",this.$nextTick((function(t){e.$refs.content.init(e.tbData)}))):!0===this.Is_Pack&&(this.title="添加打包件",this.$nextTick((function(t){e.$refs.content.init(e.tbData2)})))},handleUrl:function(e){return(0,o.default)((0,r.default)().m((function t(){var a,n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,b.GetOssUrl)({url:e});case 1:return a=t.v,n=a.Data,t.a(2,n)}}),t)})))()},handleEditCar:function(){this.currentComponent="CarDialog",this.title="编辑常用车辆",this.dialogVisible=!0},close:function(){this.dialogVisible=!1},handleInfo:function(e){this.$refs.info.handleOpen(e)},handleDetail:function(e){var t=this;this.currentComponent="packDetail",this.width="60%",this.title="打包件详情",this.topDialog="10vh",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs.content.init(e.PackageId||e.Id)}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,b.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,i=e.Data,r=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,i.Grid),t.columns=(i.ColumnList.filter((function(e){return e.Is_Display&&"Wait_Stock_Count"!==e.Code}))||[]).map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e})),t.form.PageInfo.PageSize=+i.Grid.Row_Number,t.tbConfig.Is_Select=!1,t.tbConfig.Is_Row_Number=!0,a(t.columns)}else t.$message({message:r,type:"error"})}))}))},getContractList:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a,n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,a={ContractTypeCode:3,ProjectIds:[]},e.form.ProjectId&&a.ProjectIds.push(e.form.ProjectId),t.n=1,(0,v.GetContractList)(a);case 1:n=t.v,n.IsSucceed?e.contractOptions=n.Data:e.$message({message:n.Message,type:"error"}),t.n=3;break;case 2:t.p=2,t.v;case 3:return t.a(2)}}),t,null,[[0,2]])})))()}}}},c918:function(e,t,a){"use strict";a.r(t);var n=a("60b4"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},cb90:function(e,t,a){"use strict";a("97745")}}]);