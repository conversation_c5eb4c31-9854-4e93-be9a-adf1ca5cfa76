(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-cad5ef3c"],{"0198":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"person-wapper"},[a("div",{staticClass:"search-wapper"},[a("el-form",{ref:"form",attrs:{model:t.form,inline:"","label-width":"70px"}},[a("el-form-item",{attrs:{label:"专业类别",prop:"ProfessionalTypeName"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:"",disabled:""},model:{value:t.form.ProfessionalTypeName,callback:function(e){t.$set(t.form,"ProfessionalTypeName",e)},expression:"form.ProfessionalTypeName"}})],1),a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Check_Object_Type,callback:function(e){t.$set(t.form,"Check_Object_Type",e)},expression:"form.Check_Object_Type"}},[a("el-option",{attrs:{label:"构件",value:0}}),a("el-option",{attrs:{label:"零件",value:1}})],1)],1),a("el-form-item",{attrs:{label:"质检方式",prop:"checkType"}},[a("el-input",{attrs:{type:"text",placeholder:"请选择",clearable:"",disabled:""},model:{value:t.form.checkType,callback:function(e){t.$set(t.form,"checkType",e)},expression:"form.checkType"}})],1),a("el-form-item",{attrs:{label:"选择年月",prop:"Date"}},[a("el-date-picker",{attrs:{type:"month",placeholder:"选择年月","value-format":"yyyy-MM"},on:{change:function(e){return t.changeDate()}},model:{value:t.form.Date,callback:function(e){t.$set(t.form,"Date",e)},expression:"form.Date"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),a("el-button",{on:{click:function(e){t.$refs["form"].resetFields(),t.handleSearch()}}},[t._v("重置")])],1)],1)],1),a("div",{staticClass:"middle-wapper"},[a("el-card",{staticClass:"card-wapper"},[a("InspectDataScreen",{attrs:{"checking-object-type":t.form.Check_Object_Type,"check-node-id":t.form.Check_Node_Id,date:t.form.Date}})],1),a("el-card",{staticClass:"card-wapper mid-card"},[a("YearFactoryDataChart",{attrs:{"checking-object-type":t.form.Check_Object_Type,"check-node-id":t.form.Check_Node_Id,date:t.form.Date}})],1),a("el-card",{staticClass:"card-wapper"},[a("MouthFactoryType",{attrs:{"checking-object-type":t.form.Check_Object_Type,"check-node-id":t.form.Check_Node_Id,date:t.form.Date}})],1),a("el-card",{staticClass:"card-wapper bottom-card"},[a("MouthFactoryData",{attrs:{"checking-object-type":t.form.Check_Object_Type,"check-node-id":t.form.Check_Node_Id,date:t.form.Date}})],1),a("el-card",{staticClass:"card-wapper bottom-card"},[a("MouthFactoryNumber",{attrs:{"checking-object-type":t.form.Check_Object_Type,"check-node-id":t.form.Check_Node_Id,date:t.form.Date}})],1),a("el-card",{staticClass:"card-wapper bottom-card"},[a("MouthFactoryCheckRate",{attrs:{"checking-object-type":t.form.Check_Object_Type,"check-node-id":t.form.Check_Node_Id,date:t.form.Date}})],1),a("el-card",{staticClass:"card-wapper bottom-card"},[a("NodesRejectionRate",{attrs:{"checking-object-type":t.form.Check_Object_Type,"check-node-id":t.form.Check_Node_Id,date:t.form.Date}})],1)],1)])},i=[]},"027e":function(t,e,a){"use strict";a.r(e);var n=a("dbe27"),i=a("5347");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("8aab");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"4d0c920e",null);e["default"]=c.exports},"05381":function(t,e,a){"use strict";a("2146")},"0876":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/be-reviewed.3337a67d.png"},"0d94":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("b680");var n=a("2ab9"),i=a("ed08");e.default={props:{Checking_Object_Type:{type:Number,default:0},Check_Node_Id:{type:String,default:""},Date:{type:String,default:""}},data:function(){return{countList:{}}},watch:{Checking_Object_Type:function(){this.fetchData()},Check_Node_Id:function(){this.fetchData()},Date:function(){this.fetchData()}},created:function(){var t=this;this.fetchData(),setInterval((function(){t.fetchData()}),6e4)},methods:{fetchData:function(){var t=this;(0,n.GetYearlySummary)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,i.parseTime)(new Date(this.Date),"{y}-{m}")}).then((function(e){t.countList=e.Data||[],t.countList.Checking_Percent=t.countList.Checking_Percent.toFixed(2)+"%",t.countList.First_Pass_Percent=t.countList.First_Pass_Percent.toFixed(2)+"%"}))}}}},"0de2":function(t,e,a){"use strict";a.r(e);var n=a("ec69"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},1751:function(t,e,a){"use strict";a("5818")},"1a40":function(t,e,a){"use strict";a.r(e);var n=a("e4677"),i=a("59b2");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("1751");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"495d225a",null);e["default"]=c.exports},"212c":function(t,e,a){},2146:function(t,e,a){},2395:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"quick-menu"},[a("div",{ref:"histogramChart",staticClass:"chart-c"})])},i=[]},"26de":function(t,e,a){"use strict";a.r(e);var n=a("cb8d"),i=a("a341");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("a0a39");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"e39f0340",null);e["default"]=c.exports},"28da":function(t,e,a){"use strict";a.r(e);var n=a("820b"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"2ab9":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetCheckingProblemList=l,e.GetDictionaryDetailListByCode=r,e.GetMonthCheckingSummary=u,e.GetMonthlyCheckingUserList=d,e.GetUnQualifiedCheckNodeList=s,e.GetYearlySummary=o,e.GetYearlyTrend=c;var i=n(a("b775"));function r(t){return(0,i.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function o(t){return(0,i.default)({url:"/PRO/InspectionAnalysis/GetYearlySummary",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/InspectionAnalysis/GetYearlyTrend",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/InspectionAnalysis/GetUnQualifiedCheckNodeList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/InspectionAnalysis/GetMonthCheckingSummary",method:"post",data:t})}function d(t){return(0,i.default)({url:"/PRO/InspectionAnalysis/GetMonthlyCheckingUserList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/InspectionAnalysis/GetCheckingProblemList",method:"post",data:t})}},"335a":function(t,e,a){"use strict";var n=a("dbce").default,i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(a("ade3"));a("d81d"),a("e9f5"),a("ab43"),a("a9e3"),a("d3b7");var o,c=a("2ab9"),s=n(a("313e")),u=a("ed08");e.default={name:"FactoryDataChart",props:{Checking_Object_Type:{type:Number,default:0},Check_Node_Id:{type:String,default:""},Date:{type:String,default:""}},data:function(){return{list:[]}},watch:{Checking_Object_Type:function(){this.fetchData()},Check_Node_Id:function(){this.fetchData()},Date:function(){this.fetchData()}},created:function(){var t=this;this.fetchData(),setInterval((function(){t.fetchData()}),6e4)},methods:{fetchData:function(){var t=this;(0,c.GetYearlyTrend)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,u.parseTime)(new Date(this.Date),"{y}-{m}")}).then((function(e){t.list=e.Data||[];t.list.map((function(t){return t.Month}));var a=t.list.map((function(t){return t.Checking_Count?t.Checking_Count:0})),n=t.list.map((function(t){return t.First_Pass_Percent?t.First_Pass_Percent/100:0})),i={title:{text:"质检量年度趋势",textStyle:{color:"#222834",fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",axisPointer:{type:"cross",crossStyle:{color:"#999"}}},legend:{data:["质检量(件)","一次合格率"],left:"center",top:"0",itemWidth:8,itemHeight:4},grid:{left:40,bottom:30},xAxis:[{type:"category",data:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],axisPointer:{type:"shadow"},axisLabel:{interval:0,textStyle:{color:"#333333"}},axisLine:{lineStyle:{color:"#E2E4E9"}}}],yAxis:[(0,r.default)({name:"质检量",nameLocation:"end",nameTextStyle:{align:"left"},type:"value",splitLine:{show:!1},axisLabel:{textStyle:{color:"rgba(34, 40, 52, 0.4)"}}},"nameTextStyle",{color:"rgba(34, 40, 52, 0.4)"}),{name:"一次合格率",nameLocation:"end",type:"value",splitLine:{show:!1},axisLabel:{textStyle:{color:"rgba(34, 40, 52, 0.4)"}},nameTextStyle:{color:"rgba(34, 40, 52, 0.4)"}}],series:[{name:"质检量(件)",type:"bar",data:a,barWidth:10,itemStyle:{normal:{color:new s.graphic.LinearGradient(0,0,0,1,[{offset:1,color:"#298DFF"},{offset:0,color:"#29D3FF"}])}}},{name:"一次合格率",type:"line",yAxisIndex:1,data:n,symbol:"none",itemStyle:{normal:{color:"#00CFAA",lineStyle:{color:"#00CFAA",width:2,shadowColor:"rgba(241, 180, 48, 0.4)",shadowBlur:4,shadowOffsetY:2}}}}]},c=t.$refs.histogramChart;o=s.init(c),o.setOption(i)}))}}}},"33dd":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7"),a("25f0");var i=n(a("e4e4")),r=n(a("38c5")),o=n(a("a6ed")),c=n(a("027e")),s=n(a("26de")),u=n(a("1a40")),d=n(a("ab58")),l=a("fd31"),f=a("2ab9"),h=a("ed08");e.default={components:{InspectDataScreen:i.default,YearFactoryDataChart:r.default,NodesRejectionRate:o.default,MouthFactoryData:c.default,MouthFactoryNumber:s.default,MouthFactoryCheckRate:u.default,MouthFactoryType:d.default},data:function(){return{dialogVisible:!1,loading:!1,form:{ProfessionalTypeName:"",checkType:"全检",Check_Object_Type:0,Check_Node_Id:"",Date:(0,h.parseTime)(new Date,"{y}-{m}"),PageInfo:{Page:1,PageSize:20}},ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],typeList:[],projectList:[],userList:[],qualityList:[],cateList:[],factoryList:[],categroyName:"",category:"",factoryId:localStorage.getItem("CurReferenceId"),date:new Date,year:(new Date).getFullYear().toString(),ymonth:(0,h.parseTime)(new Date,"{y}-{m}")}},created:function(){this.getBasicData()},methods:{getBasicData:function(){var t=this;(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(e){e.IsSucceed?(t.ProfessionalType=e.Data,t.form.ProfessionalTypeName=t.ProfessionalType[0].Name):t.$message({message:e.Message,type:"error"})})),(0,f.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(e){e.IsSucceed&&(t.qualityList=e.Data)}))},fetchData:function(){},changeDate:function(){},handleSearch:function(){this.form.PageInfo.Page=1,this.fetchData()}}}},"34fe":function(t,e,a){},35508:function(t,e,a){"use strict";a.r(e);var n=a("5a52"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"38c5":function(t,e,a){"use strict";a.r(e);var n=a("2395"),i=a("c31d");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("7df2");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"d6690014",null);e["default"]=c.exports},"3e08":function(t,e,a){"use strict";a.r(e);var n=a("33dd"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"423a":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"title"},t._l(t.title,(function(e,n){return a("span",{key:n,class:t.spanCurr==n?"clickindex":"index",staticStyle:{cursor:"pointer"},on:{click:function(a){return t.handelIndex(n,e)}}},[t._v(t._s(e.Display_Name))])})),0),a("div",{staticClass:"content-wapper"},[a("factory")],1)])])},i=[]},4825:function(t,e,a){},5034:function(t,e,a){"use strict";a("851b")},5347:function(t,e,a){"use strict";a.r(e);var n=a("6266"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"568a":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-row",{staticClass:"quick-menu"},[a("div",{ref:"mouthNumberChart1",staticClass:"chart-c"}),a("div",{ref:"mouthNumberChart2",staticClass:"chart-c"})])},i=[]},5801:function(t,e,a){},5818:function(t,e,a){},"59b2":function(t,e,a){"use strict";a.r(e);var n=a("b63d"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"5a52":function(t,e,a){"use strict";var n=a("dbce").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("ab43"),a("a9e3"),a("b680"),a("d3b7");var i,r,o=a("2ab9"),c=n(a("313e")),s=a("ed08");e.default={name:"QuickMenu",props:{Checking_Object_Type:{type:Number,default:0},Check_Node_Id:{type:String,default:""},Date:{type:String,default:""}},data:function(){return{list:[],list2:[],dataContent:""}},watch:{Checking_Object_Type:function(){this.fetchData()},Check_Node_Id:function(){this.fetchData()},Date:function(){this.fetchData()}},created:function(){var t=this;this.fetchData(),setInterval((function(){t.fetchData()}),6e4)},methods:{fetchData:function(){var t=this;(0,o.GetUnQualifiedCheckNodeList)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,s.parseTime)(new Date(this.Date),"{y}-{m}"),Query_Type:0}).then((function(e){t.list=e.Data||[];var a=t.list.map((function(t){return t.Check_Node_Name?t.Check_Node_Name:"未分类"})),n=t.list.map((function(t){return{name:t.Check_Node_Name?t.Check_Node_Name:"未分类",value:t.Unqualified_Percent.toFixed(2)}})),r={title:[{text:"节点不合格占比",left:0,textStyle:{color:"#222834",fontSize:16,fontWeight:"bold"}}],tooltip:{trigger:"item"},legend:{width:"80%",itemWidth:8,itemHeight:4,bottom:"10%",left:"10%",data:a,textStyle:{color:"#333",fontSize:12,rich:{a:{lineHeight:12}}},formatter:["{a|{name}}"].join("\n")},series:[{type:"pie",radius:["30%","50%"],center:["50%","40%"],label:{show:!0,position:"center",formatter:"年",fontSize:"14",fontWeight:"bold"},labelLine:{show:!1},data:n,tooltip:{position:"right",formatter:function(t){return t.name+t.value+"%"}}}]},o=t.$refs.mouthNumberChart1;i=c.init(o),i.setOption(r)})),(0,o.GetUnQualifiedCheckNodeList)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,s.parseTime)(new Date(this.Date),"{y}-{m}"),Query_Type:1}).then((function(e){t.list2=e.Data||[];var a=t.list2.map((function(t){return t.Check_Node_Name?t.Check_Node_Name:"未分类"})),n=t.list2.map((function(t){return{name:t.Check_Node_Name?t.Check_Node_Name:"未分类",value:t.Unqualified_Percent.toFixed(2)}})),i={tooltip:{trigger:"item"},legend:{width:"80%",itemWidth:8,itemHeight:4,bottom:"10%",left:"10%",data:a,textStyle:{color:"#333",fontSize:12,rich:{a:{lineHeight:12}}}},series:[{type:"pie",radius:["30%","50%"],center:["50%","40%"],label:{show:!0,position:"center",formatter:"月",fontSize:"14",fontWeight:"bold"},labelLine:{show:!1},data:n,tooltip:{position:"left",formatter:function(t){return t.name+t.value+"%"}}}]},o=t.$refs.mouthNumberChart2;r=c.init(o),r.setOption(i)}))}}}},6021:function(t,e,a){"use strict";a.r(e);var n=a("0198"),i=a("3e08");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("5034");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"0d65098e",null);e["default"]=c.exports},"60a6":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"quick-menu"},[n("div",{staticClass:"title-wapper"},[t._v("年度质检数据")]),n("div",{staticClass:"data-wappper"},[n("div",{staticClass:"inspect-wapper"},[n("img",{attrs:{src:a("7080"),alt:""}}),n("div",[n("span",{staticClass:"inspect-item"},[t._v("质检重量(t)")]),n("span",{staticClass:"number"},[t._v(t._s(t.countList.Total_Yield))])])]),n("div",{staticClass:"inspect-wapper"},[n("img",{attrs:{src:a("9136"),alt:""}}),n("div",[n("span",{staticClass:"inspect-item"},[t._v("质检数量")]),n("span",{staticClass:"number"},[t._v(t._s(t.countList.Checking_Count))])])]),n("div",{staticClass:"inspect-wapper"},[n("img",{attrs:{src:a("0876"),alt:""}}),n("div",[n("span",{staticClass:"inspect-item"},[t._v("质检率")]),n("span",{staticClass:"number"},[t._v(t._s(t.countList.Checking_Percent))])])]),n("div",{staticClass:"inspect-wapper"},[n("img",{attrs:{src:a("b3c2"),alt:""}}),n("div",[n("span",{staticClass:"inspect-item"},[t._v("一次通过率")]),n("span",{staticClass:"number"},[t._v(t._s(t.countList.First_Pass_Percent))])])])])])},i=[]},6266:function(t,e,a){"use strict";var n=a("dbce").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("a9e3"),a("d3b7");a("ac26");var i,r=a("8975"),o=a("2ab9"),c=n(a("313e")),s=a("ed08");e.default={name:"QuickMenu",props:{Checking_Object_Type:{type:Number,default:0},Check_Node_Id:{type:String,default:""},Date:{type:String,default:""}},data:function(){return{list:[],dataContent:"",all:0}},watch:{Checking_Object_Type:function(){this.fetchData()},Check_Node_Id:function(){this.fetchData()},Date:function(){this.fetchData()}},created:function(){var t=this;this.fetchData(),setInterval((function(){t.fetchData()}),6e4)},methods:{filterUnit:function(t,e,a,n){return(0,r.numberUnitFormatter)(t,e,a,n)},fetchData:function(){var t=this;(0,o.GetMonthCheckingSummary)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,s.parseTime)(new Date(this.Date),"{y}-{m}")}).then((function(e){t.list=e.Data||[];var a=t.list.map((function(t){return t.Date?(0,s.parseTime)(new Date(t.Date),"{m}-{d}"):""})),n=t.list.map((function(t){return t.Yield||0}));t.all=0,t.list.map((function(e){t.all+=e.Yield}));var r={tooltip:{backgroundColor:"#FFFFFF",borderColor:"#FFFFFF",extraCssText:"box-shadow:inset 0 0 3px #FFFFFF; border-radius:0;",borderWidth:.5,textStyle:{color:"#333"},trigger:"axis"},grid:{top:30,left:60,bottom:30},xAxis:{type:"category",data:a,axisPointer:{type:"shadow"},axisLabel:{textStyle:{color:"#333333"}},axisLine:{lineStyle:{color:"#E2E4E9"}}},yAxis:{type:"value",show:!0,offset:15,splitLine:{show:!0,lineStyle:{color:"#f2f2f2",width:1}},axisLine:{lineStyle:{color:"#1E376D",width:1}},axisLabel:{color:"#888888",formatter:function(t){return t},textStyle:{color:"rgba(34, 40, 52, 0.4)"}}},series:[{data:n,type:"line",symbol:"none",lineStyle:{color:"#FF8B11"}}]},o=t.$refs.mouthNumberChart;i=c.init(o),i.setOption(r)}))}}}},"62b3":function(t,e,a){"use strict";a.r(e);var n=a("0d94"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"644a":function(t,e,a){},7080:function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/be-inspect.826f5b3a.png"},"7df2":function(t,e,a){"use strict";a("5801")},"802c":function(t,e,a){},8134:function(t,e,a){"use strict";var n=a("dbce").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("a9e3"),a("d3b7");var i,r=n(a("313e")),o=a("2ab9"),c=a("ed08");e.default={name:"QuickMenu",props:{Checking_Object_Type:{type:Number,default:0},Check_Node_Id:{type:String,default:""},Date:{type:String,default:""}},data:function(){return{list:[],dataContent:"",all:0}},watch:{Checking_Object_Type:function(){this.fetchData()},Check_Node_Id:function(){this.fetchData()},Date:function(){this.fetchData()}},created:function(){var t=this;this.fetchData(),setInterval((function(){t.fetchData()}),6e4)},methods:{fetchData:function(){var t=this;(0,o.GetMonthCheckingSummary)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,c.parseTime)(new Date(this.Date),"{y}-{m}")}).then((function(e){t.list=e.Data||[];var a=t.list.map((function(t){return t.Date?(0,c.parseTime)(new Date(t.Date),"{m}-{d}"):""})),n=t.list.map((function(t){return t.Amount||0}));t.all=0,t.list.map((function(e){t.all+=e.Amount}));var o={tooltip:{backgroundColor:"#FFFFFF",borderColor:"#FFFFFF",extraCssText:"box-shadow:inset 0 0 3px #FFFFFF; border-radius:0;",borderWidth:.5,textStyle:{color:"#333"},trigger:"axis"},grid:{top:30,left:50,bottom:30},xAxis:{type:"category",data:a,axisPointer:{type:"shadow"},axisLabel:{textStyle:{color:"#333333"}},axisLine:{lineStyle:{color:"#E2E4E9"}},axisTick:{}},yAxis:{type:"value",offset:15,axisLabel:{color:"#888888",formatter:function(t){return t},textStyle:{color:"rgba(34, 40, 52, 0.4)"}}},series:[{data:n,type:"bar",itemStyle:{normal:{color:new r.graphic.LinearGradient(0,0,0,1,[{offset:1,color:"#298DFF"},{offset:0,color:"#29D3FF"}])}}}]},s=t.$refs.mouthNumberChart;i=r.init(s),i.setOption(o)}))}}}},"820b":function(t,e,a){"use strict";var n=a("dbce").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a15b"),a("d81d"),a("b0c0"),a("e9f5"),a("ab43"),a("a9e3"),a("b680"),a("d3b7");var i,r,o=a("2ab9"),c=n(a("313e")),s=a("ed08");e.default={name:"QuickMenu",props:{Checking_Object_Type:{type:Number,default:0},Check_Node_Id:{type:String,default:""},Date:{type:String,default:""}},data:function(){return{list:[],list2:[],dataContent:""}},watch:{Checking_Object_Type:function(){this.fetchData()},Check_Node_Id:function(){this.fetchData()},Date:function(){this.fetchData()}},created:function(){var t=this;this.fetchData(),setInterval((function(){t.fetchData()}),6e4)},methods:{fetchData:function(){var t=this;(0,o.GetCheckingProblemList)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,s.parseTime)(new Date(this.Date),"{y}-{m}"),Query_Type:0}).then((function(e){t.list=e.Data||[];var a=t.list.map((function(t){return t.Problem_Name?t.Problem_Name:"未分类"})),n=t.list.map((function(t){return{name:t.Problem_Name?t.Problem_Name:"未分类",value:t.Problem_Percent.toFixed(2)}})),r={title:[{text:"问题类型占比",left:0,textStyle:{color:"#222834",fontSize:16,fontWeight:"bold"}}],tooltip:{trigger:"item"},legend:{width:"80%",itemWidth:8,itemHeight:4,bottom:"10%",left:"10%",data:a,textStyle:{color:"#333",fontSize:12,rich:{a:{lineHeight:12}}},formatter:["{a|{name}}"].join("\n")},series:[{type:"pie",radius:["30%","50%"],center:["50%","40%"],label:{show:!0,position:"center",formatter:"年",fontSize:"14",fontWeight:"bold"},labelLine:{show:!1},data:n,tooltip:{position:"right",formatter:function(t){return t.name+t.value+"%"}}}]},o=t.$refs.mouthNumberChart1;i=c.init(o),i.setOption(r)})),(0,o.GetCheckingProblemList)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,s.parseTime)(new Date(this.Date),"{y}-{m}"),Query_Type:1}).then((function(e){t.list2=e.Data||[];var a=t.list2.map((function(t){return t.Problem_Name?t.Problem_Name:"未分类"})),n=t.list2.map((function(t){return{name:t.Problem_Name?t.Problem_Name:"未分类",value:t.Problem_Percent.toFixed(2)}})),i={tooltip:{trigger:"item"},legend:{width:"80%",itemWidth:8,itemHeight:4,bottom:"10%",left:"10%",data:a,textStyle:{color:"#333",fontSize:12,rich:{a:{lineHeight:12}}}},series:[{type:"pie",radius:["30%","50%"],center:["50%","40%"],label:{show:!0,position:"center",formatter:"月",fontSize:"14",fontWeight:"bold"},labelLine:{show:!1},data:n,tooltip:{position:"left",formatter:function(t){return t.name+t.value+"%"}}}]},o=t.$refs.mouthNumberChart2;r=c.init(o),r.setOption(i)}))}}}},"851b":function(t,e,a){},"8aab":function(t,e,a){"use strict";a("212c")},"8c38":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-row",{staticClass:"quick-menu"},[a("div",{ref:"mouthNumberChart1",staticClass:"chart-c"}),a("div",{ref:"mouthNumberChart2",staticClass:"chart-c"})])},i=[]},9136:function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/be-rectified.1d761622.png"},"9adc0":function(t,e,a){"use strict";a("34fe")},a0a39:function(t,e,a){"use strict";a("644a")},a341:function(t,e,a){"use strict";a.r(e);var n=a("8134"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},a44f:function(t,e,a){"use strict";a.r(e);var n=a("423a"),i=a("0de2");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("05381");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"0506a495",null);e["default"]=c.exports},a6ed:function(t,e,a){"use strict";a.r(e);var n=a("568a"),i=a("35508");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("c6ca");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"64180952",null);e["default"]=c.exports},ab58:function(t,e,a){"use strict";a.r(e);var n=a("8c38"),i=a("28da");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("b2dd");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"5439b520",null);e["default"]=c.exports},ac26:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetFactoryEntity=et,e.GetFactoryFileEntities=W,e.GetFactoryOutlineList=A,e.GetLately30CheckCount=U,e.GetLately30CheckRate=z,e.GetLately30CheckWeight=Q,e.GetMonthWeightData=B,e.GetPersonalCheckCountRank=H,e.GetProcessListBase=Z,e.GetProjectsList=I,e.GetQuestionRank=Y,e.GetSysProfessionalByCode=X,e.GetTeamGroupRank=q,e.GetTypeList=K,e.GetWarehouseCapacity=nt,e.GetWorkingTeamBase=tt,e.GetYearTotalData=$,e.SearchComponentPageList=at,e.addClassification=f,e.addFactoryCheck=L,e.addFactorySettingGroup=s,e.deleteClassification=p,e.deleteFactoryCheck=x,e.deleteFactorySettingGroup=d,e.editClassification=h,e.editFactoryCheck=S,e.editFactorySettingGroup=u,e.exportManageList=V,e.getAllProcessList=R,e.getCategoryList=o,e.getClassificationEntity=m,e.getClassificationList=C,e.getComponentTypeList=c,e.getDwgPaint=P,e.getFactoryAreaEntities=j,e.getFactoryCheckEntity=F,e.getFactoryList=r,e.getFactorySteelList=G,e.getFactoryWorkingTeam=E,e.getGroupEntity=l,e.getGroupItemsList=g,e.getProjectsByPlatform=M,e.getProjectsNodeList=w,e.getRelatedUser=_,e.getSheetDwg=v,e.getSheetDwgById=D,e.getSheetDwgByName=k,e.getSheetDwgList=J,e.getUserList=y,e.gtComponentList=b,e.multiAddFactoryCheck=O,e.replyFactoryCheck=N,e.submitFactoryCheck=T;var i=n(a("b775"));function r(t){return(0,i.default)({url:"/PLM/FactorySetting/GetAuthFactoryList",method:"post",data:t})}function o(t){return(0,i.default)({url:"/PLM/FactorySetting/GetCategoryList",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PLM/FactorySetting/AddGroup",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PLM/FactorySetting/EditGroup",method:"post",data:t})}function d(t){return(0,i.default)({url:"/PLM/FactorySetting/DeleteGroup",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PLM/FactorySetting/GetGroupEntity",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PLM/FactorySetting/AddClassification",method:"post",data:t})}function h(t){return(0,i.default)({url:"/PLM/FactorySetting/EditClassification",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PLM/FactorySetting/DeleteClassification",method:"post",data:t})}function m(t){return(0,i.default)({url:"/PLM/FactorySetting/GetClassificationEntity",method:"post",data:t})}function y(t){return(0,i.default)({url:"/SYS/User/GetUserList",method:"post",data:t})}function _(t){return(0,i.default)({url:"/PLM/FactorySetting/GetRelatedUser",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/Component/GetComponentList",method:"post",data:t})}function C(t){return(0,i.default)({url:"/PLM/FactorySetting/GetClassificationList",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PLM/FactorySetting/GetGroupItemsList",method:"post",data:t})}function v(t){return(0,i.default)({url:"PLM/FactoryCheck/GetSheetDwg",method:"post",data:t})}function k(t){return(0,i.default)({url:"/PLM/FactoryCheck/GetSheetDwgByName",method:"post",data:t})}function D(t){return(0,i.default)({url:"/PLM/FactoryCheck/GetSheetDwg",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PLM/FactoryCheck/GetDwgPaint",method:"post",data:t})}function F(t){return(0,i.default)({url:"/PLM/FactoryCheck/GetEntity",method:"post",data:t})}function L(t){return(0,i.default)({url:"/PLM/FactoryCheck/Add",method:"post",data:t})}function S(t){return(0,i.default)({url:"/PLM/FactoryCheck/Edit",method:"post",data:t})}function x(t){return(0,i.default)({url:"/PLM/FactoryCheck/Delete",method:"post",data:t})}function T(t){return(0,i.default)({url:"/PLM/FactoryCheck/Submit",method:"post",data:t})}function N(t){return(0,i.default)({url:"/PLM/FactoryCheck/Reply",method:"post",data:t})}function O(t){return(0,i.default)({url:"/PLM/FactoryCheck/MultiAdd",method:"post",data:t})}function G(t){return(0,i.default)({url:"/PLM/FactorySetting/GetSteelList",method:"post",data:t})}function w(t){return(0,i.default)({url:"/plm/plm_projects_node/GetEntities",method:"post",data:t})}function j(t){return(0,i.default)({url:"/PLM/FactorySetting/GetAreaEntities",method:"post",data:t})}function M(t){return(0,i.default)({url:"/PLM/QSCheck/GetProjectsByPlatform",method:"post",data:t})}function I(t){return(0,i.default)({url:"/PLM/FactorySetting/GetProjectsList",method:"post",data:t})}function R(t){return(0,i.default)({url:"/PLM/FactorySetting/GetProcessList",method:"post",data:t})}function E(t){return(0,i.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:t})}function A(t){return(0,i.default)({url:"/PLM/FactoryReport/GetOutlineList",method:"post",data:t})}function W(t){return(0,i.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:t})}function $(t){return(0,i.default)({url:"/PLM/FactoryReport/GetYearTotalData",method:"post",data:t})}function B(t){return(0,i.default)({url:"/PLM/FactoryReport/GetMonthWeightData",method:"post",data:t})}function Y(t){return(0,i.default)({url:"/PLM/FactoryReport/GetQuestionRank",method:"post",data:t})}function Q(t){return(0,i.default)({url:"/PLM/FactoryReport/GetLately30CheckWeight",method:"post",data:t})}function U(t){return(0,i.default)({url:"/PLM/FactoryReport/GetLately30CheckCount",method:"post",data:t})}function z(t){return(0,i.default)({url:"/PLM/FactoryReport/GetLately30CheckRate",method:"post",data:t})}function q(t){return(0,i.default)({url:"/PLM/FactoryReport/GetTeamGroupRank",method:"post",data:t})}function H(t){return(0,i.default)({url:"/PLM/FactoryReport/GetPersonalCheckCountRank",method:"post",data:t})}function J(t){return(0,i.default)({url:"/PLM/FactoryCheck/GetSheetDwg",method:"post",data:t})}function V(t){return(0,i.default)({url:"/PLM/FactoryReport/ExportManageList",method:"post",data:t})}function K(t){return(0,i.default)({url:"/PLM/Plm_Professional_Type/GetTypeList",method:"post",data:t})}function X(t){return(0,i.default)({url:"/Plm/Plm_Professional_Type/GetSysProfessionalByCode",method:"post",data:t})}function Z(t){return(0,i.default)({url:"/Pro/TechnologyLib/GetProcessListBase",method:"post",data:t})}function tt(t){return(0,i.default)({url:"/Pro/TechnologyLib/GetWorkingTeamBase",method:"post",data:t})}function et(t){return(0,i.default)({url:"/Pro/Factory/GetFactoryEntity",method:"post",data:t})}function at(t){return(0,i.default)({url:"/PRO/Component/SearchCheckComponentPageList",method:"post",data:t})}function nt(t){return(0,i.default)({url:"/PRO/Component/GetWarehouseCapacity",method:"post",data:t})}},b2dd:function(t,e,a){"use strict";a("802c")},b3c2:function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/participate.1dee0371.png"},b63d:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("e9f5"),a("7d54"),a("a9e3"),a("d3b7"),a("159b");var n=a("2ab9"),i=a("ed08");e.default={name:"YearQtAmountTrend",props:{Checking_Object_Type:{type:Number,default:0},Check_Node_Id:{type:String,default:""},Date:{type:String,default:""}},data:function(){return{total:0,data:[]}},watch:{Checking_Object_Type:function(){this.fetchData()},Check_Node_Id:function(){this.fetchData()},Date:function(){this.fetchData()}},created:function(){var t=this;this.fetchData(),setInterval((function(){t.fetchData()}),6e4)},mounted:function(){},methods:{fetchData:function(){var t=this;(0,n.GetMonthlyCheckingUserList)({Checking_Object_Type:this.Checking_Object_Type,Check_Node_Id:this.Check_Node_Id,Date:(0,i.parseTime)(new Date(this.Date),"{y}-{m}")}).then((function(e){e.IsSucceed?(t.data=e.Data,t.data.forEach((function(e){t.total+=e.Amount}))):Toast.loading({type:"warning",duration:2e3,message:e.Message})}))}}}},c31d:function(t,e,a){"use strict";a.r(e);var n=a("335a"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},c6ca:function(t,e,a){"use strict";a("4825")},cb8d:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-row",{staticClass:"quick-menu"},[a("div",{staticClass:"cs-title-x"},[a("span",{staticClass:"cs-title"},[t._v("月度质检数量")]),a("span",[a("span",{staticClass:"cs-label"},[t._v("总量 ")]),a("span",{staticClass:"cs-num"},[t._v(" "+t._s(t.all||0)+"t")])])]),a("div",{ref:"mouthNumberChart",staticClass:"chart-c"})])},i=[]},dbe27:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-row",{staticClass:"quick-menu"},[a("div",{staticClass:"cs-title-x"},[a("span",{staticClass:"cs-title"},[t._v(" 月度质检量")]),a("span",[a("span",{staticClass:"cs-label"},[t._v("总量 ")]),a("span",{staticClass:"cs-num"},[t._v(" "+t._s(t.all||0)+"t")])])]),a("div",{ref:"mouthNumberChart",staticClass:"chart-c"})])},i=[]},e4677:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"working-board-echarts"},[t._m(0),a("div",{staticClass:"echarts-content"},[a("div",{staticClass:"echarts-content-list"},t._l(t.data,(function(e,n){return a("div",{key:n,staticClass:"line-box"},[a("div",{staticClass:"line-name-value"},[a("span",{staticClass:"line-name"},[t._v(t._s(e.UserName))]),a("span",{staticClass:"line-value"},[t._v(t._s(e.Amount))])]),a("div",{staticClass:"line-bj"},[a("div",{staticClass:"line-bj-value",style:"width:"+(e.Amount/t.total*100).toFixed(2)+"%"})])])})),0)])])},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"echarts-top"},[a("div",{staticClass:"echarts-top-title"},[t._v("人员质量数量排名")]),a("div",{staticClass:"echarts-top-total"},[a("span",[t._v("单位：")]),a("span",[t._v("件")])])])}]},e4e4:function(t,e,a){"use strict";a.r(e);var n=a("60a6"),i=a("62b3");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("9adc0");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"d6361f1c",null);e["default"]=c.exports},ec69:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("6021"));e.default={name:"PROHome",components:{factory:i.default},data:function(){return{spanCurr:0,title:[{Display_Name:"工厂",Id:2}],Type:2}},methods:{handelIndex:function(t,e){this.Type=e.Id,this.spanCurr=t}}}}}]);