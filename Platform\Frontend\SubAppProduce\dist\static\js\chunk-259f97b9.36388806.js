(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-259f97b9"],{1276:function(e,t,n){"use strict";var o=n("c65b"),r=n("e330"),a=n("d784"),i=n("825a"),l=n("861d"),s=n("1d80"),u=n("4840"),c=n("8aa5"),d=n("50c4"),f=n("577e"),m=n("dc4a"),h=n("14c3"),p=n("9f7f"),_=n("d039"),g=p.UNSUPPORTED_Y,v=4294967295,C=Math.min,b=r([].push),T=r("".slice),y=!_((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),k="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;a("split",(function(e,t,n){var r="0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:o(t,this,e,n)}:t;return[function(t,n){var a=s(this),i=l(t)?m(t,e):void 0;return i?o(i,t,a,n):o(r,f(a),t,n)},function(e,o){var a=i(this),l=f(e);if(!k){var s=n(r,a,l,o,r!==t);if(s.done)return s.value}var m=u(a,RegExp),p=a.unicode,_=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(g?"g":"y"),y=new m(g?"^(?:"+a.source+")":a,_),w=void 0===o?v:o>>>0;if(0===w)return[];if(0===l.length)return null===h(y,l)?[l]:[];var I=0,x=0,S=[];while(x<l.length){y.lastIndex=g?0:x;var P,A=h(y,g?T(l,x):l);if(null===A||(P=C(d(y.lastIndex+(g?x:0)),l.length))===I)x=c(l,x,p);else{if(b(S,T(l,I,x)),S.length===w)return S;for(var q=1;q<=A.length-1;q++)if(b(S,A[q]),S.length===w)return S;x=I=P}}return b(S,T(l,I)),S}]}),k||!y,g)},8780:function(e,t,n){"use strict";var o=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("4de4"),n("7db0"),n("c740"),n("a630"),n("caad"),n("d81d"),n("14d9"),n("13d5"),n("a434"),n("b0c0"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("a732"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7"),n("ac1f"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("8a79"),n("2532"),n("3ca3"),n("1276"),n("2ca0"),n("498a"),n("c7cd"),n("159b"),n("ddb0");var r=o(n("2909")),a=o(n("c14f")),i=o(n("1da1")),l=n("6186"),s=n("c3c6"),u=n("7f9d"),c=o(n("d7b0")),d=n("e144"),f=o(n("6612")),m=n("ed08"),h=n("fd31"),p="$_$";t.default={components:{QrcodeVue:c.default},filters:{filterNum:function(e){return(0,f.default)(e).divide(1e3).format("0.[00]")}},data:function(){return{treeSelectParams:{placeholder:"请选择",clearable:!0},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},tbLoading:!1,loading:!1,activeName:"first",tipLabel:"",tbData:[],filterTbData:[],multipleSelection:[],columns:[],workingTeam:[],workingTeamColumn:[],formInline:{},pg_type:"",searchType:"",type:"",queryForm:{Comp_Codes:"",Part_Code:"",Spec:"",Comp_Codes_Vague:"",Part_Code_Vague:""},dialogVisible:!1,form:{TeamGroup:""},rules:{TeamGroup:[{required:!0,message:"请输入班组名称",trigger:"change"}]},Is_Workshop_Enabled:!1}},computed:{isView:function(){return"view"===this.type},isCom:function(){return"com"===this.pg_type},isUnitPart:function(){return"unitPart"===this.pg_type}},mounted:function(){var e=this;return(0,i.default)((0,a.default)().m((function t(){var n,o,r,i,l,s;return(0,a.default)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,n=JSON.parse(decodeURIComponent(e.$route.query.other)),e.formInline=Object.assign({},e.formInline,n),e.pg_type=e.$route.query.pg_type,e.type=e.$route.query.type,e.Is_Workshop_Enabled=e.$route.query.Is_Workshop_Enabled,t.n=1,e.getTableConfig(e.isUnitPart?"PROTaskUnitAllocationChange":"PROTaskAllocationChange");case 1:e.isCom?(o=e.columns.findIndex((function(e){return"Part_Code"===e.Code})),-1!==o&&e.columns.splice(o,1),r=e.columns.findIndex((function(e){return"Component_Code"===e.Code})),-1!==r&&e.columns.splice(r,1)):(i=e.columns.findIndex((function(e){return"Comp_Code"===e.Code})),-1!==i&&e.columns.splice(i,1),l=e.columns.findIndex((function(e){return"Type"===e.Code})),-1!==l&&e.columns.splice(l,1)),e.Is_Workshop_Enabled||(s=e.columns.findIndex((function(e){return"Workshop_Name"===e.Code})),-1!==s&&e.columns.splice(s,1)),e.getObjectTypeList(),e.fetchData(),t.n=3;break;case 2:t.p=2,t.v,e.$message({message:"参数错误,请重新操作",type:"error"});case 3:return t.a(2)}}),t,null,[[0,2]])})))()},methods:{getRowCCode:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if("first"===this.activeName)return"allocatedTask"+n;var o=this.activeName.split(p),r=this.workingTeam.find((function(e){return e.Working_Team_Name===o[0]})),a=this.getRowUnique(e.uuid,e.Process_Code,r.Working_Team_Id);return"Max"===n?a:t+a+n},handleClick:function(e){var t=this;if("first"===e.name){var n=this.$refs.xTable.getColumnByField("Schduled_Count"),o=this.$refs.xTable.getColumnByField("Can_Allocation_Count");n.visible=!1,o.visible=!0}else{var r=this.$refs.xTable.getColumnByField("Schduled_Count"),a=this.$refs.xTable.getColumnByField("Can_Allocation_Count");r.visible=!0,a.visible=!1}this.workingTeam.forEach((function(n,o){var r="".concat(n.Working_Team_Name,"$_$").concat(n.Process_Code),a=t.$refs.xTable.getColumnByField(r);a.visible=r===e.name})),this.$refs.xTable.refreshColumn(),this.filterTbData=this.tbData.filter((function(e){return t.filterZero(e)})),this.multipleSelection=[],this.$refs.xTable.clearCheckboxRow()},filterZero:function(e){if("first"===this.activeName)return e.allocatedTask>0;var t=this.activeName.split(p),n=this.workingTeam.find((function(e){return e.Working_Team_Name===t[0]})),o=this.getRowUnique(e.uuid,n.Process_Code,n.Working_Team_Id);return e[o]>0},fetchData:function(){var e=this,t=this.queryForm.Comp_Codes?this.queryForm.Comp_Codes.trim().split(" "):[],n=this.queryForm.Part_Code?this.queryForm.Part_Code.trim().split(" "):[],o=2;o=this.isCom?2:this.isUnitPart?3:1,this.tbLoading=!0,(0,u.GetTeamProcessAllocation)({Page:1,PageSize:-1,Step:this.formInline.Step,Process_Type:o,Schduling_Code:this.formInline.Schduling_Code,Process_Code:this.formInline.Process_Code,Workshop_Name:this.formInline.Workshop_Name,Area_Id:this.formInline.Area_Id,InstallUnit_Id:this.formInline.InstallUnit_Id,Comp_Codes:t,Part_Code:n}).then(function(){var t=(0,i.default)((0,a.default)().m((function t(n){var o,r,i,l;return(0,a.default)().w((function(t){while(1)switch(t.n){case 0:if(!n.IsSucceed){t.n=2;break}return o=n.Data,r=o.Schduling_Plan,i=o.Schduling_Comps,e.planInfoTemp=r,t.n=1,e.getStopList(i);case 1:e.initTbData(i),i.length&&(e.workingTeam=i[0].Allocation_Teams,l=e.workingTeam.map((function(e){return e.visible=!1,e})),e.workingTeamColumn=(0,m.deepClone)(l)),e.filterData(),t.n=3;break;case 2:e.$message({message:n.Message,type:"error"});case 3:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()).finally((function(t){e.tbLoading=!1}))},checkMethod:function(e){var t=e.row;return!t.stopFlag},getStopList:function(e){var t=this;return(0,i.default)((0,a.default)().m((function n(){var o,r;return(0,a.default)().w((function(n){while(1)switch(n.n){case 0:return o="Id",r=e.map((function(e){return{Id:e[o],Type:t.isCom?2:t.isUnitPart?3:1}})),n.n=1,(0,u.GetStopList)(r).then((function(n){if(n.IsSucceed){var r={};n.Data.forEach((function(e){r[e.Id]=!!e.Is_Stop})),e.forEach((function(e){r[e[o]]&&t.$set(e,"stopFlag",r[e[o]])}))}}));case 1:return n.a(2)}}),n)})))()},filterData:function(){var e=this;this.multipleSelection=[],this.$refs.xTable.clearCheckboxRow();var t=this.isCom?"Comp_Code":"Part_Code",n=this.isCom?"Comp_Codes":"Part_Code",o=this.queryForm[n].split(" ").filter((function(e){return!!e})),r=this.isCom?"Comp_Codes_Vague":"Part_Code_Vague",a=this.queryForm[r],i=this.tbData.filter((function(e){return!o.length&&""===a||o.includes(e[t])})),l=i.length>0&&""===a||0===i.length&&""===a?i:this.tbData,s=l.filter((function(e){return""===a&&!o.length||e[t].includes(a)})),u=i.concat(s),c=u.reduce((function(e,t){var n=e.find((function(e){return e.Schduling_Detail_Id===t.Schduling_Detail_Id}));return n||e.push(t),e}),[]);this.filterTbData=c.filter((function(t){return!e.searchType||e.searchType===t.Type})).filter((function(t){return!e.queryForm.Spec||(t.Spec||"").includes(e.queryForm.Spec)})).filter((function(t){return e.filterZero(t)}))},initTbData:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Allocation_Teams";this.tbData=e.map((function(e){var o,r=(null===(o=e.Technology_Path)||void 0===o?void 0:o.split("/"))||[];e.uuid=(0,d.v4)();var a=e[n].filter((function(e){return-1!==r.findIndex((function(t){return e.Process_Code===t}))}));e.defaultCan_Allocation_Count=e.Can_Allocation_Count;var i=0;return a.forEach((function(n,o){var r=t.getRowUnique(e.uuid,n.Process_Code,n.Working_Team_Id),a=t.getRowUniqueMax(e.uuid,n.Process_Code,n.Working_Team_Id);e[r]=n.Count,t.$set(e,"alCount"+r,n.Count),e[a]=0,i+=n.Count,t.$set(e,"totalTask"+n.Working_Team_Name,n.Total_Receive_Count+n.Count)})),t.$set(e,"allocatedTask",e.defaultCan_Allocation_Count-i),e.Can_Allocation_Count=e.allocatedTask,t.$set(e,"allocatedTaskMax",e.Can_Allocation_Count),t.setInputMax(e),e.checked=!1,e}))},inputChange:function(e){this.setInputMax(e)},setInputMax:function(e){var t=Object.keys(e).filter((function(t){return!t.endsWith("max")&&t.startsWith(e.uuid)&&t.length>e.uuid.length}));t.forEach((function(n){var o=n.split(p)[1],r=t.filter((function(e){var t=e.split(p)[1];return e!==n&&t===o})).reduce((function(t,n){return t+(0,f.default)(e[n]).value()}),0);e[n+p+"max"]=e.Schduled_Count-r,+e[n]}))},checkPermissionTeam:function(e,t){if(!e)return!1;var n=(null===e||void 0===e?void 0:e.split("/"))||[];return!!n.some((function(e){return e===t}))},getSubmitTbInfo:function(){for(var e,t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.tbData,o=JSON.parse(JSON.stringify(n)),a=function(){var e=o[i],n=[];if(!e.Technology_Path)return t.$message({message:"工序不能为空",type:"warning"}),{v:{status:!1}};var a=Array.from(new Set(e.Technology_Path.split("/")));a.forEach((function(o){var a=t.workingTeam.filter((function(e){return e.Process_Code===o})),i=a.map((function(n,r){var a=t.getRowUnique(e.uuid,o,n.Working_Team_Id),i=t.getRowUniqueMax(e.uuid,o,n.Working_Team_Id),l={Comp_Code:e.Comp_Code,Again_Count:+e[a],Part_Code:t.isCom?null:e.Part_Code,Process_Code:o,Technology_Path:e.Technology_Path,Working_Team_Id:n.Working_Team_Id,Working_Team_Name:n.Working_Team_Name,Team_Task_Id:e.Allocation_Teams.find((function(e){return e.Working_Team_Id===n.Working_Team_Id})).Team_Task_Id};return delete e["alCount"+a],delete e["allocatedTask"],delete e["allocatedTaskMax"],delete e[a],delete e[i],l}));n.push.apply(n,(0,r.default)(i))})),delete e["uuid"],delete e["puuid"],e.Allocation_Teams=n},i=0;i<o.length;i++)if(e=a(),e)return e.v;return{tableData:o,status:!0}},handleSubmit:function(){var e=this,t=this.getSubmitTbInfo(),n=t.tableData,o=t.status;o&&this.$confirm("是否提交当前数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.loading=!0;var t={Schduling_Plan:e.planInfoTemp,Schduling_Comps:n},o={Schduling_Plan:e.planInfoTemp,SarePartsModel:n},r=e.isCom?u.AdjustTeamProcessAllocation:e.isUnitPart?u.AdjustSubAssemblyTeamProcessAllocation:u.AdjustPartTeamProcessAllocation;r(e.isCom?t:o).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.handleClose()):e.$message({message:t.Message,type:"error"}),e.loading=!1}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handleClose:function(){this.closeView()},closeView:function(){(0,m.closeTagView)(this.$store,this.$route)},tbSelectChange:function(e){this.multipleSelection=e.records},getTaskCode:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?"totalTask"+e:"totalTask"+this.activeName.split(p)[0]},reverseSelection:function(){var e=this.$refs.xTable.getCheckboxRecords().filter((function(e){return!e.stopFlag})),t=this.filterTbData.filter((function(t){return!e.includes(t)&&!t.stopFlag}));this.$refs.xTable.setCheckboxRow(e,!1),this.$refs.xTable.setCheckboxRow(t,!0),this.multipleSelection=this.$refs.xTable.getCheckboxRecords()},getTableConfig:function(e){var t=this;return(0,i.default)((0,a.default)().m((function n(){return(0,a.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,l.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,o=e.Data,r=e.Message;if(n){t.tbConfig=Object.assign({},t.tbConfig,o.Grid);var a=o.ColumnList||[];t.columns=a.filter((function(e){return e.Is_Display})).map((function(e){return s.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),"Schduled_Count"===e.Code&&(e.visible=!1),e}))}else t.$message({message:r,type:"error"})}));case 1:return n.a(2)}}),n)})))()},activeCellMethod:function(e){var t,n=e.row,o=e.column;e.columnIndex;if(this.isView)return!1;if("AllocatedCount"===o.field)return!0;var r=null===(t=o.field)||void 0===t?void 0:t.split("$_$")[1];return this.checkPermissionTeam(n.Technology_Path,r)},getRowUnique:function(e,t,n){return"".concat(e).concat(p).concat(t).concat(p).concat(n)},getRowUniqueMax:function(e,t,n){return this.getRowUnique(e,t,n)+"".concat(p,"max")},Batchallocation:function(){this.dialogVisible=!0},handleDialog:function(){this.dialogVisible=!1},handelData:function(){var e=this;this.multipleSelection.forEach((function(t){return t.Allocation_Teams.forEach((function(n){return n.Working_Team_Id===e.form.TeamGroup?n.Count=t.Can_Allocation_Count:n.Count=0}))}));for(var t=function(){var t=e.multipleSelection[n],o=Array.from(new Set(t.Technology_Path.split("/")));o.forEach((function(n){var o=e.workingTeam.filter((function(e){return e.Process_Code===n}));o.forEach((function(o){var r=e.getRowUnique(t.uuid,n,e.form.TeamGroup),a=e.getRowUnique(t.uuid,n,o.Working_Team_Id),i=e.getRowUniqueMax(t.uuid,n,e.form.TeamGroup),l=e.getRowUniqueMax(t.uuid,n,o.Working_Team_Id);r===a&&i===l?(t[a]=t["Can_Allocation_Count"],t[l]=t["Can_Allocation_Count"]):(t[a]=0,t[l]=0)}))}))},n=0;n<this.multipleSelection.length;n++)t();for(var o=function(t){for(var n=function(n){e.tbData[t].uuid===e.multipleSelection[n].uuid&&e.$nextTick((function(o){e.tbData[t]=e.multipleSelection[n]}))},o=0;o<e.multipleSelection.length;o++)n(o)},r=0;r<this.tbData.length;r++)o(r)},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.doAllocation(),t.handleDialog(),t.multipleSelection=[],t.$refs.xTable.clearCheckboxRow()}))},doAllocation:function(){var e=this;"first"===this.activeName?this.multipleSelection.forEach((function(t,n){if(t.Can_Allocation_Count){var o=t.Allocation_Teams.find((function(t){return t.Working_Team_Id===e.form.TeamGroup})),r=e.getRowUnique(t.uuid,o.Process_Code,o.Working_Team_Id);t[r]=Number(t[r])+t.allocatedTask,t.Can_Allocation_Count=t.Can_Allocation_Count-t.allocatedTask,t[e.getTaskCode(o.Working_Team_Name)]+=t.allocatedTask,t[e.getTaskCode()]-=t.allocatedTask,t.allocatedTaskMax=t.Can_Allocation_Count,t.allocatedTask=t.Can_Allocation_Count,t["alCount"+r]=t[r]}})):this.multipleSelection.forEach((function(t,n){var o=t.Allocation_Teams.find((function(t){return t.Working_Team_Id===e.form.TeamGroup})),r=e.getRowUnique(t.uuid,o.Process_Code,o.Working_Team_Id),a=Math.min(t[e.getRowCCode(t,"alCount")],t[e.getRowCCode(t)]);t[e.getTaskCode(o.Working_Team_Name)]+=a,t[e.getTaskCode()]-=a,t[r]=Number(t[r])+a,t[e.getRowCCode(t)]-=a,t[e.getRowCCode(t,"alCount")]-=a,t["alCount"+r]=t[r]})),this.filterTbData=this.tbData.filter((function(t){return e.filterZero(t)}))},resetForm:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1},filterTypeMethod:function(e){var t=e.option,n=e.row;return n.Type.includes(t.data)},filterTypeRecoverMethod:function(e){var t=e.option;t.data=""},filterCodeMethod:function(e){var t=e.option,n=e.row;return n[this.isCom?"Comp_Code":"Part_Code"].includes(t.data)},filterCodeRecoverMethod:function(e){var t=e.option;t.data=""},getObjectTypeList:function(){var e=this;(0,h.GetCompTypeTree)({professional:"Steel"}).then((function(t){t.IsSucceed?(e.ObjectTypeList.data=t.Data,e.$nextTick((function(n){var o;null===(o=e.$refs)||void 0===o||null===(o=o.treeSelectObjectType)||void 0===o||o.treeDataUpdateFun(t.Data)}))):e.$message({type:"error",message:t.Message})}))}}}},"8a79":function(e,t,n){"use strict";var o=n("23e7"),r=n("4625"),a=n("06cf").f,i=n("50c4"),l=n("577e"),s=n("5a34"),u=n("1d80"),c=n("ab13"),d=n("c430"),f=r("".slice),m=Math.min,h=c("endsWith"),p=!d&&!h&&!!function(){var e=a(String.prototype,"endsWith");return e&&!e.writable}();o({target:"String",proto:!0,forced:!p&&!h},{endsWith:function(e){var t=l(u(this));s(e);var n=arguments.length>1?arguments[1]:void 0,o=t.length,r=void 0===n?o:m(i(n),o),a=l(e);return f(t,r-a.length,r)===a}})},c3c6:function(e,t,n){"use strict";var o=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniqueCode=t.getDraftQuery=t.FIX_COLUMN=void 0,n("b0c0");var r=o(n("5530"));t.getDraftQuery=function(e,t,n,o,a){return{name:e,query:(0,r.default)({status:t,pg_type:n,pg_redirect:a.name},o)}},t.uniqueCode=function(e){return"uuid"},t.FIX_COLUMN=["Comp_Code","Project_Name","Area_Name","Part_Code","InstallUnit_Name"]},d301:function(e,t,n){},e144:function(e,t,n){"use strict";n.r(t),n.d(t,"v1",(function(){return c})),n.d(t,"v3",(function(){return F})),n.d(t,"v4",(function(){return $["a"]})),n.d(t,"v5",(function(){return O})),n.d(t,"NIL",(function(){return V})),n.d(t,"version",(function(){return E})),n.d(t,"validate",(function(){return d["a"]})),n.d(t,"stringify",(function(){return i["a"]})),n.d(t,"parse",(function(){return m}));var o,r,a=n("d8f8"),i=n("58cf"),l=0,s=0;function u(e,t,n){var u=t&&n||0,c=t||new Array(16);e=e||{};var d=e.node||o,f=void 0!==e.clockseq?e.clockseq:r;if(null==d||null==f){var m=e.random||(e.rng||a["a"])();null==d&&(d=o=[1|m[0],m[1],m[2],m[3],m[4],m[5]]),null==f&&(f=r=16383&(m[6]<<8|m[7]))}var h=void 0!==e.msecs?e.msecs:Date.now(),p=void 0!==e.nsecs?e.nsecs:s+1,_=h-l+(p-s)/1e4;if(_<0&&void 0===e.clockseq&&(f=f+1&16383),(_<0||h>l)&&void 0===e.nsecs&&(p=0),p>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");l=h,s=p,r=f,h+=122192928e5;var g=(1e4*(268435455&h)+p)%4294967296;c[u++]=g>>>24&255,c[u++]=g>>>16&255,c[u++]=g>>>8&255,c[u++]=255&g;var v=h/4294967296*1e4&268435455;c[u++]=v>>>8&255,c[u++]=255&v,c[u++]=v>>>24&15|16,c[u++]=v>>>16&255,c[u++]=f>>>8|128,c[u++]=255&f;for(var C=0;C<6;++C)c[u+C]=d[C];return t||Object(i["a"])(c)}var c=u,d=n("06e4");function f(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}var m=f;function h(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}var p="6ba7b810-9dad-11d1-80b4-00c04fd430c8",_="6ba7b811-9dad-11d1-80b4-00c04fd430c8",g=function(e,t,n){function o(e,o,r,a){if("string"===typeof e&&(e=h(e)),"string"===typeof o&&(o=m(o)),16!==o.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var l=new Uint8Array(16+e.length);if(l.set(o),l.set(e,o.length),l=n(l),l[6]=15&l[6]|t,l[8]=63&l[8]|128,r){a=a||0;for(var s=0;s<16;++s)r[a+s]=l[s];return r}return Object(i["a"])(l)}try{o.name=e}catch(r){}return o.DNS=p,o.URL=_,o};function v(e){if("string"===typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return C(T(y(e),8*e.length))}function C(e){for(var t=[],n=32*e.length,o="0123456789abcdef",r=0;r<n;r+=8){var a=e[r>>5]>>>r%32&255,i=parseInt(o.charAt(a>>>4&15)+o.charAt(15&a),16);t.push(i)}return t}function b(e){return 14+(e+64>>>9<<4)+1}function T(e,t){e[t>>5]|=128<<t%32,e[b(t)-1]=t;for(var n=1732584193,o=-271733879,r=-1732584194,a=271733878,i=0;i<e.length;i+=16){var l=n,s=o,u=r,c=a;n=x(n,o,r,a,e[i],7,-680876936),a=x(a,n,o,r,e[i+1],12,-389564586),r=x(r,a,n,o,e[i+2],17,606105819),o=x(o,r,a,n,e[i+3],22,-1044525330),n=x(n,o,r,a,e[i+4],7,-176418897),a=x(a,n,o,r,e[i+5],12,1200080426),r=x(r,a,n,o,e[i+6],17,-1473231341),o=x(o,r,a,n,e[i+7],22,-45705983),n=x(n,o,r,a,e[i+8],7,1770035416),a=x(a,n,o,r,e[i+9],12,-1958414417),r=x(r,a,n,o,e[i+10],17,-42063),o=x(o,r,a,n,e[i+11],22,-1990404162),n=x(n,o,r,a,e[i+12],7,1804603682),a=x(a,n,o,r,e[i+13],12,-40341101),r=x(r,a,n,o,e[i+14],17,-1502002290),o=x(o,r,a,n,e[i+15],22,1236535329),n=S(n,o,r,a,e[i+1],5,-165796510),a=S(a,n,o,r,e[i+6],9,-1069501632),r=S(r,a,n,o,e[i+11],14,643717713),o=S(o,r,a,n,e[i],20,-373897302),n=S(n,o,r,a,e[i+5],5,-701558691),a=S(a,n,o,r,e[i+10],9,38016083),r=S(r,a,n,o,e[i+15],14,-660478335),o=S(o,r,a,n,e[i+4],20,-405537848),n=S(n,o,r,a,e[i+9],5,568446438),a=S(a,n,o,r,e[i+14],9,-1019803690),r=S(r,a,n,o,e[i+3],14,-187363961),o=S(o,r,a,n,e[i+8],20,1163531501),n=S(n,o,r,a,e[i+13],5,-1444681467),a=S(a,n,o,r,e[i+2],9,-51403784),r=S(r,a,n,o,e[i+7],14,1735328473),o=S(o,r,a,n,e[i+12],20,-1926607734),n=P(n,o,r,a,e[i+5],4,-378558),a=P(a,n,o,r,e[i+8],11,-2022574463),r=P(r,a,n,o,e[i+11],16,1839030562),o=P(o,r,a,n,e[i+14],23,-35309556),n=P(n,o,r,a,e[i+1],4,-1530992060),a=P(a,n,o,r,e[i+4],11,1272893353),r=P(r,a,n,o,e[i+7],16,-155497632),o=P(o,r,a,n,e[i+10],23,-1094730640),n=P(n,o,r,a,e[i+13],4,681279174),a=P(a,n,o,r,e[i],11,-358537222),r=P(r,a,n,o,e[i+3],16,-722521979),o=P(o,r,a,n,e[i+6],23,76029189),n=P(n,o,r,a,e[i+9],4,-640364487),a=P(a,n,o,r,e[i+12],11,-421815835),r=P(r,a,n,o,e[i+15],16,530742520),o=P(o,r,a,n,e[i+2],23,-995338651),n=A(n,o,r,a,e[i],6,-198630844),a=A(a,n,o,r,e[i+7],10,1126891415),r=A(r,a,n,o,e[i+14],15,-1416354905),o=A(o,r,a,n,e[i+5],21,-57434055),n=A(n,o,r,a,e[i+12],6,1700485571),a=A(a,n,o,r,e[i+3],10,-1894986606),r=A(r,a,n,o,e[i+10],15,-1051523),o=A(o,r,a,n,e[i+1],21,-2054922799),n=A(n,o,r,a,e[i+8],6,1873313359),a=A(a,n,o,r,e[i+15],10,-30611744),r=A(r,a,n,o,e[i+6],15,-1560198380),o=A(o,r,a,n,e[i+13],21,1309151649),n=A(n,o,r,a,e[i+4],6,-145523070),a=A(a,n,o,r,e[i+11],10,-1120210379),r=A(r,a,n,o,e[i+2],15,718787259),o=A(o,r,a,n,e[i+9],21,-343485551),n=k(n,l),o=k(o,s),r=k(r,u),a=k(a,c)}return[n,o,r,a]}function y(e){if(0===e.length)return[];for(var t=8*e.length,n=new Uint32Array(b(t)),o=0;o<t;o+=8)n[o>>5]|=(255&e[o/8])<<o%32;return n}function k(e,t){var n=(65535&e)+(65535&t),o=(e>>16)+(t>>16)+(n>>16);return o<<16|65535&n}function w(e,t){return e<<t|e>>>32-t}function I(e,t,n,o,r,a){return k(w(k(k(t,e),k(o,a)),r),n)}function x(e,t,n,o,r,a,i){return I(t&n|~t&o,e,t,r,a,i)}function S(e,t,n,o,r,a,i){return I(t&o|n&~o,e,t,r,a,i)}function P(e,t,n,o,r,a,i){return I(t^n^o,e,t,r,a,i)}function A(e,t,n,o,r,a,i){return I(n^(t|~o),e,t,r,a,i)}var q=v,N=g("v3",48,q),F=N,$=n("ec26");function D(e,t,n,o){switch(e){case 0:return t&n^~t&o;case 1:return t^n^o;case 2:return t&n^t&o^n&o;case 3:return t^n^o}}function W(e,t){return e<<t|e>>>32-t}function U(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"===typeof e){var o=unescape(encodeURIComponent(e));e=[];for(var r=0;r<o.length;++r)e.push(o.charCodeAt(r))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var a=e.length/4+2,i=Math.ceil(a/16),l=new Array(i),s=0;s<i;++s){for(var u=new Uint32Array(16),c=0;c<16;++c)u[c]=e[64*s+4*c]<<24|e[64*s+4*c+1]<<16|e[64*s+4*c+2]<<8|e[64*s+4*c+3];l[s]=u}l[i-1][14]=8*(e.length-1)/Math.pow(2,32),l[i-1][14]=Math.floor(l[i-1][14]),l[i-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<i;++d){for(var f=new Uint32Array(80),m=0;m<16;++m)f[m]=l[d][m];for(var h=16;h<80;++h)f[h]=W(f[h-3]^f[h-8]^f[h-14]^f[h-16],1);for(var p=n[0],_=n[1],g=n[2],v=n[3],C=n[4],b=0;b<80;++b){var T=Math.floor(b/20),y=W(p,5)+D(T,_,g,v)+C+t[T]+f[b]>>>0;C=v,v=g,g=W(_,30)>>>0,_=p,p=y}n[0]=n[0]+p>>>0,n[1]=n[1]+_>>>0,n[2]=n[2]+g>>>0,n[3]=n[3]+v>>>0,n[4]=n[4]+C>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]}var R=U,M=g("v5",80,R),O=M,V="00000000-0000-0000-0000-000000000000";function j(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}var E=j},e215:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return r}));var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container abs100"},[n("el-card",{staticClass:"box-card h100"},[n("h4",{staticClass:"topTitle"},[n("span"),e._v("基本信息")]),n("el-form",{ref:"formInline",staticClass:"demo-form-inline",attrs:{"label-position":"right","label-width":"90px",inline:!0,model:e.formInline}},[n("el-row",[n("el-col",{attrs:{span:20}},[n("el-row",[n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"排产单号:","label-width":"75px",prop:"Schduling_Code"}},[n("span",[e._v(e._s(e.formInline.Schduling_Code))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"项目名称:",prop:"Project_Name"}},[n("span",[e._v(e._s(e.formInline.Project_Name))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"区域:",prop:"Area_Name"}},[n("span",[e._v(e._s(e.formInline.Area_Name))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"批次:",prop:"Installunit_Name"}},[n("span",[e._v(e._s(e.formInline.Installunit_Name))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"任务数量:","label-width":"75px",prop:"Allocation_Count"}},[n("span",[e._v(e._s(e.formInline.Allocation_Count))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"任务重量:",prop:"Allocation_Weight"}},[n("span",[e._v(e._s(e._f("filterNum")(e.formInline.Allocation_Weight))+"(t)")])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"已完成数量:",prop:"Finish_Count"}},[n("span",[e._v(e._s(e.formInline.Finish_Count))])])],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"已完成重量:",prop:"Finish_Weight"}},[n("span",[e._v(e._s(e._f("filterNum")(e.formInline.Finish_Weight))+"(t)")])])],1)],1)],1),n("el-col",{attrs:{span:4}},[n("qrcode-vue",{attrs:{size:79,value:e.formInline.Schduling_Code,"class-name":"qrcode",level:"H"}})],1)],1)],1),n("el-divider",{staticClass:"elDivder"}),n("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"待分配",name:"first"}}),e._l(e.workingTeam,(function(e,t){return n("el-tab-pane",{key:t,attrs:{label:e.Working_Team_Name,name:e.Working_Team_Name+"$_$"+e.Process_Code}})}))],2),n("div",{staticClass:"tb-options",style:{"justify-content":e.isView?"end":"space-between"}},[n("div",[e.isView?e._e():n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.Batchallocation()}}},[e._v("批量分配")]),e.isView?e._e():n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.reverseSelection()}}},[e._v("反选")])],1),n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.queryForm}},[n("el-form-item",{attrs:{label:"规格"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.queryForm.Spec,callback:function(t){e.$set(e.queryForm,"Spec","string"===typeof t?t.trim():t)},expression:"queryForm.Spec"}})],1),e.isCom?n("el-form-item",{attrs:{label:e.isCom?"构件类型":e.isUnitPart?"部件类型":"零件类型"}},[n("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",staticStyle:{width:"100%"},attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},model:{value:e.searchType,callback:function(t){e.searchType=t},expression:"searchType"}})],1):e._e(),n("el-form-item",{attrs:{label:e.isCom?"构件编号":e.isUnitPart?"部件名称":"零件名称"}},[e.isCom?n("el-input",{attrs:{clearable:"",placeholder:"请输入(空格区分/多个搜索)"},model:{value:e.queryForm.Comp_Codes,callback:function(t){e.$set(e.queryForm,"Comp_Codes",t)},expression:"queryForm.Comp_Codes"}}):n("el-input",{attrs:{clearable:"",placeholder:"请输入(空格区分/多个搜索)"},model:{value:e.queryForm.Part_Code,callback:function(t){e.$set(e.queryForm,"Part_Code",t)},expression:"queryForm.Part_Code"}}),e.isCom?n("el-input",{staticStyle:{"margin-left":"10px"},attrs:{clearable:"",placeholder:"模糊查找(请输入关键字)"},model:{value:e.queryForm.Comp_Codes_Vague,callback:function(t){e.$set(e.queryForm,"Comp_Codes_Vague","string"===typeof t?t.trim():t)},expression:"queryForm.Comp_Codes_Vague"}}):n("el-input",{staticStyle:{"margin-left":"10px"},attrs:{clearable:"",placeholder:"模糊查找(请输入关键字)"},model:{value:e.queryForm.Part_Code_Vague,callback:function(t){e.$set(e.queryForm,"Part_Code_Vague","string"===typeof t?t.trim():t)},expression:"queryForm.Part_Code_Vague"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.filterData}},[e._v("查询")])],1)],1)],1),n("div",{staticClass:"tb-x"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"100%","show-overflow":"",loading:e.tbLoading,"checkbox-config":{checkField:"checked",checkMethod:e.checkMethod},stripe:"",size:"medium","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView,beforeEditMethod:e.activeCellMethod},data:e.filterTbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[n("vxe-column",{attrs:{type:"checkbox",width:"60",fixed:"left"}}),e._l(e.columns,(function(t){return["Comp_Code"===t.Code||"Part_Code"===t.Code?n("vxe-column",{key:t.Id,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",visible:t.visible,field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(o){var r=o.row;return[r.stopFlag?n("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),n("span",[e._v(e._s(r[t.Code]))])]}}],null,!0)}):"Schduled_Count"===t.Code?n("vxe-column",{key:t.Id,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",visible:t.visible,field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s("first"===e.activeName?"":n[e.getTaskCode()])+" ")]}}],null,!0)}):n("vxe-column",{key:t.Id,attrs:{align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction:"",visible:t.visible,"show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]})),e._l(e.workingTeamColumn,(function(t,o){return n("vxe-column",{key:o,attrs:{align:t.Align,visible:t.visible,fixed:"right",field:t.Working_Team_Name+"$_$"+t.Process_Code,title:"可分配数量",sortable:"","min-width":"170"},scopedSlots:e._u([{key:"edit",fn:function(o){var r=o.row;return[n("vxe-input",{attrs:{type:"integer",min:0,max:r[e.getRowUniqueMax(r.uuid,t.Process_Code,t.Working_Team_Id)]},on:{change:function(n){return e.inputChange(r,t.Process_Code,t.Working_Team_Id)}},model:{value:r[e.getRowUnique(r.uuid,t.Process_Code,t.Working_Team_Id)],callback:function(n){e.$set(r,e.getRowUnique(r.uuid,t.Process_Code,t.Working_Team_Id),e._n(n))},expression:"\n                row[\n                  getRowUnique(\n                    row.uuid,\n                    element.Process_Code,\n                    element.Working_Team_Id\n                  )\n                ]\n              "}})]}},{key:"default",fn:function(n){var o=n.row;return[e.checkPermissionTeam(o.Technology_Path,t.Process_Code)?[e._v(" "+e._s(o[e.getRowUnique(o.uuid,t.Process_Code,t.Working_Team_Id)])+" ")]:[e._v(" -")]]}}],null,!0)})})),n("vxe-column",{key:e.activeName,attrs:{align:"left","edit-render":{},field:"AllocatedCount",title:"分配数量",sortable:"",fixed:"right","min-width":"180"},scopedSlots:e._u([{key:"edit",fn:function(t){var o=t.row;return[n("vxe-input",{key:e.activeName,attrs:{type:"integer",min:0,max:o[e.getRowCCode(o,"","Max")]},model:{value:o[e.getRowCCode(o,"alCount")],callback:function(t){e.$set(o,e.getRowCCode(o,"alCount"),e._n(t))},expression:"row[getRowCCode(row,'alCount')]"}})]}},{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("displayValue")(n[e.getRowCCode(n,"alCount")]))+" ")]}}])})],2)],1),n("footer",[n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据")]),e.tipLabel?n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v(e._s(e.tipLabel))]):e._e()],1),n("div",[n("el-button",{on:{click:e.handleClose}},[e._v("取消 ")]),e.isView?e._e():n("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSubmit}},[e._v("提交")])],1)])],1),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"批量分配",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleDialog}},[n("el-form",{ref:"form",staticClass:"demo-ruleForm",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"选择班组",prop:"TeamGroup"}},[n("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.TeamGroup,callback:function(t){e.$set(e.form,"TeamGroup",t)},expression:"form.TeamGroup"}},e._l(e.workingTeam,(function(e){return n("el-option",{key:e.Working_Team_Id,attrs:{label:e.Working_Team_Name,value:e.Working_Team_Id}})})),1)],1),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.resetForm("form")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){e.submitForm("form"),e.resetForm("form")}}},[e._v("确 定")])],1)],1)],1)],1)},r=[]},ef6e:function(e,t,n){"use strict";n("d301")},f67d:function(e,t,n){"use strict";n.r(t);var o=n("8780"),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},f9a8:function(e,t,n){"use strict";n.r(t);var o=n("e215"),r=n("f67d");for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(a);n("ef6e");var i=n("2877"),l=Object(i["a"])(r["default"],o["a"],o["b"],!1,null,"81736af4",null);t["default"]=l.exports}}]);