(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2e20e39b"],{"09f4":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,o,a){return e/=a/2,e<1?o/2*e*e+t:(e--,-o/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,o){var i=n(),s=e-i,l=20,c=0;t="undefined"===typeof t?500:t;var u=function(){c+=l;var e=Math.easeInOutQuad(c,i,s,t);r(e),c<t?a(u):o&&"function"===typeof o&&o()};u()}},"15fd":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,o("a4d3");var a=r(o("ccb5"));function r(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(null==e)return{};var o,r,n=(0,a.default)(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)o=i[r],-1===t.indexOf(o)&&{}.propertyIsEnumerable.call(e,o)&&(n[o]=e[o])}return n}},"1cd4":function(e,t,o){"use strict";o.r(t);var a=o("879f4"),r=o.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},3166:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=u,t.GeAreaTrees=L,t.GetFileSync=I,t.GetInstallUnitIdNameList=v,t.GetNoBindProjectList=h,t.GetPartDeepenFileList=T,t.GetProjectAreaTreeList=b,t.GetProjectEntity=l,t.GetProjectList=s,t.GetProjectPageList=i,t.GetProjectTemplate=p,t.GetPushProjectPageList=C,t.GetSchedulingPartList=S,t.IsEnableProjectMonomer=d,t.SaveProject=c,t.UpdateProjectTemplateBase=P,t.UpdateProjectTemplateContacts=g,t.UpdateProjectTemplateContract=_,t.UpdateProjectTemplateOther=y;var r=a(o("b775")),n=a(o("4328"));function i(e){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:n.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:n.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function I(e){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"3e53":function(e,t,o){"use strict";o.d(t,"a",(function(){return a})),o.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[e.dialogVisible?o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:"按项目状态导出",visible:e.dialogVisible,width:"600px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[o("span",{staticStyle:{width:"120px"}},[e._v("项目状态：")]),o("el-checkbox-group",{model:{value:e.checkList,callback:function(t){e.checkList=t},expression:"checkList"}},e._l(e.projectStatusOption,(function(t,a){return o("el-col",{key:a,staticStyle:{"margin-bottom":"10px"},attrs:{span:8}},[o("el-checkbox",{attrs:{label:t.Id}},[e._v(e._s(t.Display_Name))])],1)})),1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary",disabled:0===e.checkList.length,loading:e.btnloading},on:{click:e.handelConfirm}},[e._v("确 定")])],1)],1):e._e()],1)},r=[]},"4e82":function(e,t,o){"use strict";var a=o("23e7"),r=o("e330"),n=o("59ed"),i=o("7b0b"),s=o("07fa"),l=o("083a"),c=o("577e"),u=o("d039"),d=o("addb"),f=o("a640"),m=o("3f7e"),h=o("99f4"),p=o("1212"),P=o("ea83"),g=[],_=r(g.sort),y=r(g.push),C=u((function(){g.sort(void 0)})),b=u((function(){g.sort(null)})),v=f("sort"),L=!u((function(){if(p)return p<70;if(!(m&&m>3)){if(h)return!0;if(P)return P<603;var e,t,o,a,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:o=3;break;case 68:case 71:o=4;break;default:o=2}for(a=0;a<47;a++)g.push({k:t+a,v:o})}for(g.sort((function(e,t){return t.v-e.v})),a=0;a<g.length;a++)t=g[a].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),T=C||!b||!v||!L,S=function(e){return function(t,o){return void 0===o?-1:void 0===t?1:void 0!==e?+e(t,o)||0:c(t)>c(o)?1:-1}};a({target:"Array",proto:!0,forced:T},{sort:function(e){void 0!==e&&n(e);var t=i(this);if(L)return void 0===e?_(t):_(t,e);var o,a,r=[],c=s(t);for(a=0;a<c;a++)a in t&&y(r,t[a]);d(r,S(e)),o=s(r),a=0;while(a<o)t[a]=r[a++];while(a<c)l(t,a++);return t}})},"599f":function(e,t,o){"use strict";o("e7ee")},"5b823":function(e,t,o){"use strict";o.r(t);var a=o("9992"),r=o.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"60e0":function(e,t,o){"use strict";o.r(t);var a=o("cb44b"),r=o("5b823");for(var n in r)["default"].indexOf(n)<0&&function(e){o.d(t,e,(function(){return r[e]}))}(n);o("b58d");var i=o("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"2ed4e82a",null);t["default"]=s.exports},"7de9":function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=m,t.AddCheckItemCombination=g,t.AddCheckType=l,t.DelNode=I,t.DelQualityList=L,t.DeleteCheckItem=f,t.DeleteCheckType=c,t.EntityCheckItem=h,t.EntityCheckType=i,t.EntityQualityList=v,t.ExportInspsectionSummaryInfo=R,t.GetCheckGroupList=P,t.GetCheckItemList=d,t.GetCheckTypeList=s,t.GetCompTypeTree=k,t.GetDictionaryDetailListByCode=n,t.GetEntityNode=S,t.GetFactoryPeoplelist=y,t.GetFactoryProfessionalByCode=O,t.GetMaterialType=j,t.GetNodeList=T,t.GetProEntities=_,t.GetProcessCodeList=C,t.QualityList=b,t.SaveCheckItem=p,t.SaveCheckType=u,t.SaveNode=G;var r=a(o("b775"));function n(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},"879f4":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o("4744"),r=o("6186");t.default={data:function(){return{dialogVisible:!1,projectStatusOption:[],checkList:[],btnloading:!1}},mounted:function(){this.getPreferenceSettingValue()},methods:{open:function(){var e=this;this.dialogVisible=!0,this.$nextTick((function(){e.checkList=[],e.btnloading=!1}))},handleClose:function(){this.dialogVisible=!1},getPreferenceSettingValue:function(){var e=this;(0,a.GetPreferenceSettingValue)({code:"Productweight"}).then((function(t){t.IsSucceed?"true"!==t.Data?e.getProjectStatus("project_status"):e.getProjectStatus("Engineering Status"):e.$message({message:t.Message,type:"error"})}))},getProjectStatus:function(e){var t=this;(0,r.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed?t.projectStatusOption=e.Data||[]:t.$message({message:e.Message,type:"error"})}))},handelConfirm:function(){this.btnloading=!0,this.$emit("exportByProject",2,this.checkList)},clearLoading:function(){this.btnloading=!1}}}},"87c9":function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AssignReplacementTask=m,t.EditReplacementApply=c,t.FindPartReplaceApplyById=s,t.GetPartReplaceApplyPageList=i,t.GetReplaceApprovePageList=d,t.GetReplacementTaskPageList=f,t.GetReplacementTaskProcessPageList=h,t.GetReplacementTaskTracePageList=P,t.GetTeamListByUser=n,t.GetWorkingTeams=g,t.SavePartReplaceApply=l,t.SavePartReplaceComfirm=u,t.UpdateProcessTaskStatus=p;var r=a(o("b775"));a(o("4328"));function n(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Replacement/GetPartReplaceApplyPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Replacement/FindPartReplaceApplyById",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Replacement/SavePartReplaceApply",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Replacement/EditReplacementApply",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Replacement/SavePartReplaceComfirm",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Replacement/GetReplaceApprovePageList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskPageList",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Replacement/AssignReplacementTask",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskProcessPageList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Replacement/UpdateProcessTaskStatus",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskTracePageList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}},9992:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(o("15fd")),n=a(o("5530")),i=a(o("c14f")),s=a(o("1da1"));o("99af"),o("4de4"),o("7db0"),o("c740"),o("caad"),o("a15b"),o("d81d"),o("14d9"),o("4e82"),o("a434"),o("e9f5"),o("910d"),o("f665"),o("7d54"),o("ab43"),o("a732"),o("e9c4"),o("a9e3"),o("b680"),o("b64b"),o("d3b7"),o("ac1f"),o("25f0"),o("2532"),o("3ca3"),o("841c"),o("159b"),o("ddb0"),o("2b3d"),o("bf19"),o("9861"),o("88a7"),o("271a"),o("5494");var l=o("ed08"),c=a(o("333d")),u=o("c685"),d=o("3166"),f=o("a024"),m=o("9e96"),h=o("f84a"),p=o("f4f2"),P=o("7de9"),g=a(o("fb35")),_=(o("87c9"),["Working_Team_Ids"]),y=["Working_Team_Ids"];t.default={components:{Pagination:c.default,exportDialog:g.default},data:function(){return{curSearch:1,loading:!1,btnLoading:!1,tableLoading:!1,ProjectList:[],ProcessList:[],CompProcessList:[],PartProcessList:[],search:"",CurUserLastQueryParam:"",totaData:{Deepen_Weight:0,Schduling_Weight:0,Task_Weight:0,Task_Finish_Weight:0,Sending_Weight:0},tableData:[],tableColumn:[],rootTableColumn:[{field:"Area_Name",title:"区域",minWidth:120,fixed:"left"},{field:"Component_Type",title:"构件类型",minWidth:120,fixed:"left",formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:"-"}},{field:"Component_Code",title:"构件编号",minWidth:120,fixed:"left"},{field:"Spec",title:"规格",minWidth:120},{field:"Length",title:"长度",minWidth:120},{field:"Weight",title:"单重(kg)",minWidth:120},{field:"Deepen_Amount",title:"深化数量",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Deepen_Weight",title:"深化总重(kg)",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Schduling_Amount",title:"排产数量",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Schduling_Weight",title:"排产总重(kg)",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Part_Amount",title:"配套零件数量",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Sending_Amount",title:"发货数量",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Sending_Weight",title:"发货重量(kg)",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Stock_Amount",title:"库存数量",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Stock_Weight",title:"库存重量(kg)",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:"Sending_Status",title:"发货状态",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:"-"}},{field:"Sending_Finish_Date",title:"发货完成日期",minWidth:120,formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:"-"}}],pageInfo:{Page:1,PageSize:20},pageSizeTotal:0,tablePageSize:u.tablePageSize,searchForm:{Project_Code:"",Sys_Project_Id:"",Area_Id:"",Working_Team_Ids:"",Component_Type:[],Comp_Process_Ids:[],Part_Process_Ids:[]},treeParamsArea:{"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},treeParamsComponentType:{"default-expand-all":!0,"check-strictly":!0,filterable:!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},treeSelectParams:{placeholder:"构件类型",collapseTags:!0},workTeamOption:[]}},computed:{workTeamName:function(){var e=this,t=[],o=this.searchForm.Working_Team_Ids||[];return o.length&&this.workTeamOption.length&&o.forEach((function(o,a){var r=e.workTeamOption.findIndex((function(e){return e.Id===o}));if(-1!==r){var n=e.workTeamOption[r].Name;t.push(n)}})),t}},created:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.pageInfo.PageSize=e.tablePageSize[0],e.getWorkTeam(),t.n=1,e.getBaseData();case 1:return t.a(2)}}),t)})))()},methods:{getWorkTeam:function(){var e=this;(0,f.GetWorkingTeamsPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.workTeamOption=(t.Data||[]).Data.map((function(e){return{Id:e.Id,Name:e.Name}})):e.$message({message:t.Message,type:"error"})}))},partChange:function(e){e.includes(!1)?(this.searchForm.Part_Process_Ids=[!1],this.PartProcessList.forEach((function(e){return e.disabled=!0}))):this.PartProcessList.forEach((function(e){return e.disabled=!1}))},compChange:function(e){e.includes(!1)?(this.searchForm.Comp_Process_Ids=[!1],this.CompProcessList.forEach((function(e){return e.disabled=!0}))):this.CompProcessList.forEach((function(e){return e.disabled=!1}))},getBaseData:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,d.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectList=t.Data.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,(0,f.GetFactoryAllProcessList)({Type:0}).then((function(t){t.IsSucceed?(e.ProcessList=t.Data,e.CompProcessList=t.Data.filter((function(e){return 1===e.Type})),e.PartProcessList=t.Data.filter((function(e){return 2===e.Type}))):e.$message({message:t.Message,type:"error"})}));case 2:return t.n=3,(0,m.GetCurUserLastQueryParam)({Menu_Id:e.$route.meta.Id}).then((function(t){if(t.IsSucceed){var o;if(e.CurUserLastQueryParam=t.Data,t.Data){var a=JSON.parse(t.Data),r=a.Working_Team_Ids,n=a.curSearch,i=a.search,s=a.Sys_Project_Id,l=a.Area_Id,c=a.Component_Type,u=a.Comp_Process_Ids,d=a.Part_Process_Ids;e.searchForm.Sys_Project_Id=s||e.ProjectList[0].Sys_Project_Id,e.getAreaList(),e.searchForm.Area_Id=l||"",e.searchForm.Working_Team_Ids=r||[],e.searchForm.Component_Type=c||[],e.searchForm.Comp_Process_Ids=u,e.searchForm.Part_Process_Ids=d,e.search=i,e.curSearch=n}else e.searchForm.Sys_Project_Id=e.ProjectList[0].Sys_Project_Id,e.getAreaList();e.searchForm.Project_Code=null===(o=e.ProjectList.find((function(t){return t.Sys_Project_Id===e.searchForm.Sys_Project_Id})))||void 0===o?void 0:o.Code,e.setColumnList(),e.getProjectSchdulingSendingData(),e.fetchData()}else e.$message({message:t.Message,type:"error"})}));case 3:(0,P.GetCompTypeTree)({professional:"Steel"}).then((function(t){t.IsSucceed?(e.treeParamsComponentType.data=t.Data,e.$nextTick((function(o){e.$refs.treeSelectComponentType.treeDataUpdateFun(t.Data)}))):e.$message({type:"error",message:t.Message})}));case 4:return t.a(2)}}),t)})))()},getAreaList:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,d.GeAreaTrees)({sysProjectId:e.searchForm.Sys_Project_Id}).then((function(t){if(t.IsSucceed){var o=t.Data;e.setDisabledTree(o),e.treeParamsArea.data=t.Data,e.$nextTick((function(o){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getProjectSchdulingSendingData:function(){var e=this;(0,h.GetProjectSchdulingSendingData)({Sys_Project_Id:this.searchForm.Sys_Project_Id,Area_Id:this.searchForm.Area_Id,Working_Team_Ids:this.searchForm.Working_Team_Ids.toString()}).then((function(t){t.IsSucceed?(e.totaData.Deepen_Weight=t.Data.Deepen_Weight,e.totaData.Schduling_Weight=t.Data.Schduling_Weight,e.totaData.Sending_Weight=t.Data.Sending_Weight,e.totaData.Task_Weight=t.Data.Task_Weight,e.totaData.Task_Finish_Weight=t.Data.Task_Finish_Weight):e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var o=e.Children;o&&o.length?e.disabled=!0:(e.disabled=!1,t.setDisabledTree(o))}))},projectChange:function(e){var t,o=this,a=document.getElementsByClassName("input-with-select");a[0].children[0].value="";var r=e;this.searchForm.Area_Id="",this.treeParamsArea.data=[],this.searchForm.Project_Code=null===(t=this.ProjectList.find((function(e){return e.Sys_Project_Id===o.searchForm.Sys_Project_Id})))||void 0===t?void 0:t.Code,this.$nextTick((function(e){o.$refs.treeSelectArea.treeDataUpdateFun([])})),e&&this.getAreaList(r)},areaClear:function(){this.searchForm.Area_Id=""},areaFilter:function(e){this.$refs.treeSelectArea.filterFun(e)},componentTypeFilter:function(e){this.$refs.treeSelectComponentType.filterFun(e)},handleSearch:function(){this.pageInfo.Page=1,this.setColumnList(),this.getProjectSchdulingSendingData(),this.fetchData()},handleReset:function(){var e,t=this;this.$refs["searchForm"].resetFields();var o=document.getElementsByClassName("input-with-select");if(o[0].children[0].value="",this.$refs.treeSelectArea.filterFun(),this.CurUserLastQueryParam){var a=JSON.parse(this.CurUserLastQueryParam),r=a.search,n=a.curSearch,i=a.Sys_Project_Id,s=a.Area_Id,l=a.Component_Type,c=a.Comp_Process_Ids,u=a.Working_Team_Ids,d=a.Part_Process_Ids;this.searchForm.Sys_Project_Id=i||this.ProjectList[0].Sys_Project_Id,this.getAreaList(),this.searchForm.Area_Id=s||"",this.searchForm.Working_Team_Ids=u||[],this.searchForm.Component_Type=l||[],this.searchForm.Comp_Process_Ids=c,this.searchForm.Part_Process_Ids=d,this.searchForm.search=r,this.searchForm.curSearch=n}else this.searchForm.Sys_Project_Id=this.ProjectList[0].Sys_Project_Id,this.getAreaList();this.compChange(this.searchForm.Comp_Process_Ids),this.partChange(this.searchForm.Part_Process_Ids),this.searchForm.Project_Code=null===(e=this.ProjectList.find((function(e){return e.Sys_Project_Id===t.searchForm.Sys_Project_Id})))||void 0===e?void 0:e.Code,this.handleSearch()},fetchData:function(){var e=this;this.loading=!0;var t=(0,n.default)({},this.searchForm);1===this.curSearch?(t.Component_Codes=this.search,t.Component_Code_Like=""):(t.Component_Code_Like=this.search,t.Component_Codes="");var o=t.Working_Team_Ids,a=(0,r.default)(t,_);(0,h.GetProjectComponentProductionFlowPageList)((0,n.default)((0,n.default)((0,n.default)({},a),this.pageInfo),{},{Working_Team_Ids:o.toString()})).then((function(t){t.IsSucceed?(e.tableData=t.Data.Data.map((function(e){for(var t in e.Sending_Finish_Date=e.Sending_Finish_Date?(0,l.parseTime)(e.Sending_Finish_Date,"{y}-{m}-{d}"):null,e)["Weight","Deepen_Weight","Schduling_Weight","Sending_Weight","Stock_Weight"].includes(t)&&(e[t]=e[t]?Number(Number(e[t]).toFixed(2)):0);return e})),e.setSameTb(),e.pageSizeTotal=t.Data.TotalCount,e.addSearchLog(),e.tableColumn=e.tableColumn2.filter((function(t){return!t.processCode||!e.workTeamName.length||e.tableData.some((function(o){return e.workTeamName.includes(o[t.processCode+"_bz"])}))}))):e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1}))},addSearchLog:function(){var e=this,t=(0,n.default)((0,n.default)({},this.searchForm),{},{curSearch:this.curSearch,search:this.search});(0,m.AddSearchLog)({Menu_Id:this.$route.meta.Id,Query_Param:JSON.stringify(t)}).then((function(t){t.IsSucceed||e.$message({message:t.Message,type:"error"})}))},setColumnList:function(){var e,t=this;this.tableColumn2=(0,l.deepClone)(this.rootTableColumn);var o=[],a=[],r=[];a=this.searchForm.Comp_Process_Ids.length>0?this.CompProcessList.filter((function(e){return t.searchForm.Comp_Process_Ids.includes(e.Id)})):this.CompProcessList,r=this.searchForm.Part_Process_Ids.length>0?this.PartProcessList.filter((function(e){return t.searchForm.Part_Process_Ids.includes(e.Id)})):this.PartProcessList,o=a.concat(r);var n=[];o.sort((function(e,t){return e.Sort-t.Sort})).forEach((function(e){var t={};t.title=e.Name,t.processCode=e.Code,t.children=[{field:e.Code+"_rwsl",title:"任务数量",minWidth:160,align:"center",formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:e.Code+"_wwcsl",title:"未完成数量",minWidth:160,align:"center",formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:e.Code+"_wcsl",title:"完成数量",minWidth:160,align:"center",formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:0}},{field:e.Code+"_wczl",title:"完成重量(kg)",minWidth:160,align:"center",formatter:function(e){var t=e.cellValue;e.row,e.column;return t?Number(Number(t).toFixed(2)):0}},{field:e.Code+"_bz",title:"班组",minWidth:160,align:"center",formatter:function(e){var t=e.cellValue;e.row,e.column;return null!==t&&void 0!==t?t:"-"}},{field:e.Code+"_wcrq",title:"完成日期",minWidth:160,align:"center",formatter:function(e){var t=e.cellValue;e.row,e.column;return t?(0,l.parseTime)(t,"{y}-{m}-{d}"):"-"}}],n.push(t)})),(e=this.tableColumn2).splice.apply(e,[11,0].concat(n)),this.tableColumn=(0,l.deepClone)(this.tableColumn2)},mergeRowMethod:function(e){var t=e.row,o=e._rowIndex,a=e.column,r=e.visibleData,n=["Area_Name","Component_Type","Component_Code","Spec","Length","Weight","Deepen_Amount","Deepen_Weight","Schduling_Amount","Schduling_Weight","Part_Amount","Sending_Amount","Sending_Weight","Stock_Amount","Stock_Weight","Sending_Status","Sending_Finish_Date"];if(n.includes(a.property)){var i=t["uniqueCode"],s=r[o-1],l=r[o+1];if(s&&s["uniqueCode"]===i)return{rowspan:0,colspan:0};var c=1;while(l&&l["uniqueCode"]===i)l=r[++c+o];if(c>1)return{rowspan:c,colspan:1}}},setSameTb:function(){var e=this,t=["Area_Name","Component_Type","Component_Code","Spec","Length","Weight"];this.tableData.forEach((function(o,a){var r=t.map((function(e){return o[e]})).join("|");e.$set(e.tableData[a],"uniqueCode",r)}))},handleCommand:function(e){this.curSearch=e},handleLeadingOut:function(e){var t=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];1===e&&(this.btnLoading=!0);var a=(0,n.default)({},this.searchForm);1===this.curSearch?(a.Component_Codes=this.search,a.Component_Code_Like=""):(a.Component_Code_Like=this.search,a.Component_Codes="");var i=this.ProjectList.find((function(e){return e.Sys_Project_Id===a.Sys_Project_Id})).Short_Name,s=1===e?h.ExportProjectComponentProductionFlowData:h.ExportAllProjectComponentProductionFlowData,l=a.Working_Team_Ids,c=(0,r.default)(a,y),u=1===e?(0,n.default)((0,n.default)({},c),{},{Project_Name:i,Working_Team_Ids:l.toString()}):{Component_Type:a.Component_Type,Component_Code_Like:a.Component_Code_Like,Component_Codes:a.Component_Codes,Project_State_Id:o};s((0,n.default)({},u)).then((function(o){if(o.IsSucceed){2===e&&t.$refs.exportDialog.handleClose(),t.$message.success("导出成功");var a=new URL(o.Data,(0,p.baseUrl)());window.open(a.href)}else t.$message({message:o.Message,type:"error"})})).finally((function(){t.btnLoading=!1,2===e&&t.$refs.exportDialog.clearLoading()}))},exportByProject:function(){this.$refs.exportDialog.open()},changePage:function(e){var t=this;this.tableLoading=!0,"limit"===(null===e||void 0===e?void 0:e.type)&&(this.pageInfo.Page=1),("number"!==typeof this.pageInfo.PageSize||this.pageInfo.PageSize<1)&&(this.pageInfo.PageSize=20),Promise.all([this.fetchData()]).then((function(e){t.tableLoading=!1}))}}}},"9e96":function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddSearchLog=n,t.GetCurUserLastQueryParam=i;var r=a(o("b775"));function n(e){return(0,r.default)({url:"/PRO/SearchLog/AddSearchLog",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/SearchLog/GetCurUserLastQueryParam",method:"post",data:e})}},a024:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=c,t.AddProessLib=W,t.AddTechnology=l,t.AddWorkingProcess=s,t.DelLib=A,t.DeleteProcess=L,t.DeleteProcessFlow=b,t.DeleteTechnology=v,t.DeleteWorkingTeams=G,t.GetAllProcessList=f,t.GetCheckGroupList=D,t.GetChildComponentTypeList=w,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=F,t.GetFactoryWorkingTeam=g,t.GetGroupItemsList=C,t.GetLibList=i,t.GetLibListType=x,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=p,t.GetProcessList=u,t.GetProcessListBase=d,t.GetProcessListTeamBase=j,t.GetProcessListWithUserBase=R,t.GetProcessWorkingTeamBase=N,t.GetTeamListByUser=B,t.GetTeamProcessList=y,t.GetWorkingTeam=_,t.GetWorkingTeamBase=O,t.GetWorkingTeamInfo=k,t.GetWorkingTeams=T,t.GetWorkingTeamsPageList=S,t.SaveWorkingTeams=I,t.UpdateProcessTeam=P;var r=a(o("b775")),n=a(o("4328"));function i(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:n.default.stringify(e)})}function l(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:n.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:n.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:n.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:n.default.stringify(e)})}function f(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:n.default.stringify(e)})}function m(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:n.default.stringify(e)})}function p(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:n.default.stringify(e)})}function g(){return(0,r.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function _(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:n.default.stringify(e)})}function y(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:n.default.stringify(e)})}function C(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:n.default.stringify(e)})}function b(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:n.default.stringify(e)})}function v(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:n.default.stringify(e)})}function L(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:n.default.stringify(e)})}function O(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:n.default.stringify(e)})}function j(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:n.default.stringify(e)})}function R(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},b58d:function(e,t,o){"use strict";o("c2a9")},c2a9:function(e,t,o){},cb44b:function(e,t,o){"use strict";o.d(t,"a",(function(){return a})),o.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container abs100"},[o("div",{staticClass:"h100 wrapper-c"},[o("div",{ref:"searchDom",staticClass:"search-container"},[o("el-form",{ref:"searchForm",attrs:{model:e.searchForm,inline:"","label-width":"120px"}},[o("el-form-item",{attrs:{label:"构件编号"},scopedSlots:e._u([{key:"label",fn:function(){return[o("div",{staticClass:"cs-gj-x"},[o("span",{staticClass:"cs-gj-label"},[e._v("构件编号")]),o("el-dropdown",{attrs:{trigger:"click"},on:{command:e.handleCommand}},[o("span",{staticClass:"el-dropdown-link"},[e._v(" "+e._s(1===e.curSearch?"精准查询":"模糊查询")),o("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),o("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[o("el-dropdown-item",{attrs:{command:1}},[e._v("精准查询")]),o("el-dropdown-item",{attrs:{command:2}},[e._v("模糊查询")])],1)],1)],1)]},proxy:!0}])},[o("el-input",{attrs:{placeholder:"请输入内容",clearable:""},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}})],1),o("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"项目名称",filterable:""},on:{change:function(t){return e.projectChange(e.searchForm.Sys_Project_Id)}},model:{value:e.searchForm.Sys_Project_Id,callback:function(t){e.$set(e.searchForm,"Sys_Project_Id",t)},expression:"searchForm.Sys_Project_Id"}},e._l(e.ProjectList,(function(e){return o("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),o("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[o("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.searchForm.Sys_Project_Id,"select-params":{clearable:!0,placeholder:"区域"},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,searchFun:e.areaFilter},model:{value:e.searchForm.Area_Id,callback:function(t){e.$set(e.searchForm,"Area_Id",t)},expression:"searchForm.Area_Id"}})],1),o("el-form-item",{attrs:{label:"构件类型",prop:"Component_Type"}},[o("el-tree-select",{ref:"treeSelectComponentType",staticClass:"cs-tree-x",attrs:{"select-params":e.treeSelectParams,"tree-params":e.treeParamsComponentType},on:{searchFun:e.componentTypeFilter},model:{value:e.searchForm.Component_Type,callback:function(t){e.$set(e.searchForm,"Component_Type",t)},expression:"searchForm.Component_Type"}})],1),o("el-form-item",{attrs:{label:"构件工序",prop:"Comp_Process_Ids"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"构件工序",clearable:"",filterable:"",multiple:"","collapse-tags":""},on:{change:e.compChange},model:{value:e.searchForm.Comp_Process_Ids,callback:function(t){e.$set(e.searchForm,"Comp_Process_Ids",t)},expression:"searchForm.Comp_Process_Ids"}},[o("el-option",{attrs:{label:"无",value:!1}}),e._l(e.CompProcessList,(function(e){return o("el-option",{key:e.Id,attrs:{disabled:e.disabled,label:e.Name,value:e.Id}})}))],2)],1),o("el-form-item",{attrs:{label:"零件工序",prop:"Part_Process_Ids"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"零件工序",clearable:"",filterable:"",multiple:"","collapse-tags":""},on:{change:e.partChange},model:{value:e.searchForm.Part_Process_Ids,callback:function(t){e.$set(e.searchForm,"Part_Process_Ids",t)},expression:"searchForm.Part_Process_Ids"}},[o("el-option",{attrs:{label:"无",value:!1}}),e._l(e.PartProcessList,(function(e){return o("el-option",{key:e.Id,attrs:{label:e.Name,disabled:e.disabled,value:e.Id}})}))],2)],1),o("el-form-item",{attrs:{label:"班组名称",prop:"Part_Process_Ids"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:"","collapse-tags":""},model:{value:e.searchForm.Working_Team_Ids,callback:function(t){e.$set(e.searchForm,"Working_Team_Ids",t)},expression:"searchForm.Working_Team_Ids"}},[o("el-option",{attrs:{label:"全部",value:""}}),e._l(e.workTeamOption,(function(e){return o("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})}))],2)],1),o("el-form-item",[o("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),o("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1),o("div",{staticClass:"table-container"},[o("div",{staticClass:"toolbar-container"},[o("div",[o("el-button",{attrs:{type:"success",loading:e.btnLoading,disabled:0===e.tableData.length},on:{click:function(t){return e.handleLeadingOut(1)}}},[e._v("导出 ")]),o("span",{staticClass:"toolbar-text"},[e._v("项目编号: "+e._s(e.searchForm.Project_Code||"-"))]),o("el-button",{attrs:{type:"primary"},on:{click:e.exportByProject}},[e._v("按项目状态导出 ")])],1),o("div",{staticClass:"total-container"},[o("span",[e._v(e._s("深化重量(t): "+(e.totaData.Deepen_Weight?Number(Number(e.totaData.Deepen_Weight).toFixed(3)):0)))]),o("span",[e._v(e._s("排产重量(t): "+(e.totaData.Schduling_Weight?Number(Number(e.totaData.Schduling_Weight).toFixed(3)):0)))]),o("span",[e._v(e._s("发货重量(t): "+(e.totaData.Sending_Weight?Number(Number(e.totaData.Sending_Weight).toFixed(3)):0)))]),e.searchForm.Working_Team_Ids.length?[o("span",[e._v(e._s("任务重量(t): "+(e.totaData.Task_Weight?Number(Number(e.totaData.Task_Weight).toFixed(3)):0)))]),o("span",[e._v(e._s("完成重量(t): "+(e.totaData.Task_Finish_Weight?Number(Number(e.totaData.Task_Finish_Weight).toFixed(3)):0)))])]:e._e()],2)]),o("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{padding:"0","margin-top":"16px",height:"calc(100% - 60px)"}},[o("div",{staticStyle:{height:"calc(100% - 50px)"}},[o("vxe-grid",{staticClass:"custom-vxe-grid",attrs:{border:"",resizable:"",height:"100%","empty-text":"暂无数据",loading:e.tableLoading,columns:e.tableColumn,data:e.tableData,"show-overflow":"tooltip",align:"center","span-method":e.mergeRowMethod}})],1),e.loading?e._e():o("div",{staticClass:"cs-bottom"},[o("div",{staticClass:"cs-component-num"}),o("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.pageSizeTotal,"max-height":"100%","page-sizes":e.tablePageSize,page:e.pageInfo.Page,limit:e.pageInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.pageInfo,"Page",t)},"update:limit":function(t){return e.$set(e.pageInfo,"PageSize",t)},pagination:e.changePage}})],1)])],1)]),o("exportDialog",{ref:"exportDialog",on:{exportByProject:e.handleLeadingOut}})],1)},r=[]},ccb5:function(e,t,o){"use strict";function a(e,t){if(null==e)return{};var o={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;o[a]=e[a]}return o}Object.defineProperty(t,"__esModule",{value:!0}),t.default=a},e7ee:function(e,t,o){},f84a:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportAllProjectComponentProductionFlowData=S,t.ExportProjectAreaProgress=G,t.ExportProjectComponentProductionFlowData=T,t.ExportSchedulingPlanProcess=I,t.GetCompFinishProcess=i,t.GetCompInPercent=c,t.GetCompOutPercent=u,t.GetCompPercent=d,t.GetComponentListByStatus=C,t.GetComponentYieldByStatus=b,t.GetDaysAndWeight=f,t.GetInstallUnitProducedCount1=p,t.GetInstallUnitProducedCount2=P,t.GetNestingFinishPercent=s,t.GetPartFinishPercent=l,t.GetProcessLoad=m,t.GetProjectAreaProgressList=_,t.GetProjectAreaProgressSummary=y,t.GetProjectComponentProductionFlowPageList=v,t.GetProjectSchdulingSendingData=L,t.GetProjectWarehouseDataStatistics=g,t.GetTeamLoad=h;var r=a(o("b775")),n=a(o("4328"));function i(e){return(0,r.default)({url:"/PRO/ProductionCount/GetCompFinishProcess",method:"post",data:n.default.stringify(e)})}function s(e){return(0,r.default)({url:"/PRO/ProductionCount/GetNestingFinishPercent",method:"post",data:n.default.stringify(e)})}function l(e){return(0,r.default)({url:"/PRO/ProductionCount/GetPartFinishPercent",method:"post",data:n.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/ProductionCount/GetCompInPercent",method:"post",data:n.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/ProductionCount/GetCompOutPercent",method:"post",data:n.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/ProductionCount/GetCompPercent",method:"post",data:n.default.stringify(e)})}function f(e){return(0,r.default)({url:"/PRO/ProductionCount/GetDaysAndWeight",method:"post",data:n.default.stringify(e)})}function m(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProcessLoad",method:"post",data:n.default.stringify(e)})}function h(e){return(0,r.default)({url:"/PRO/ProductionCount/GetTeamLoad",method:"post",data:n.default.stringify(e)})}function p(e){return(0,r.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount1",method:"post",data:n.default.stringify(e)})}function P(e){return(0,r.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount2",method:"post",data:n.default.stringify(e)})}function g(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectWarehouseDataStatistics",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressSummary",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentListByStatus",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentYieldByStatus",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectComponentProductionFlowPageList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectSchdulingSendingData",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/ProductionCount/ExportProjectComponentProductionFlowData",method:"post",data:e})}function S(e){return(0,r.default)({url:"/pro/ProductionCount/ExportAllProjectComponentProductionFlowData",method:"post",data:e})}function I(e){return(0,r.default)({url:"/pro/ProductionCount/ExportSchedulingPlanProcess",method:"post",data:e})}function G(e){return(0,r.default)({url:"/pro/ProductionCount/ExportProjectAreaProgress",method:"post",data:e})}},fb35:function(e,t,o){"use strict";o.r(t);var a=o("3e53"),r=o("1cd4");for(var n in r)["default"].indexOf(n)<0&&function(e){o.d(t,e,(function(){return r[e]}))}(n);o("599f");var i=o("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"4546f263",null);t["default"]=s.exports}}]);