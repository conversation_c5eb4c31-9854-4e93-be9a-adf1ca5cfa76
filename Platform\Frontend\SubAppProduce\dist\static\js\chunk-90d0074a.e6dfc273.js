(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-90d0074a"],{"0187":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteRole=o,t.GetRoleMenusObj=f,t.GetRoleTree=i,t.GetRoleWorkingObjListByUser=d,t.GetUserListByRole=l,t.GetUserRoleTreeWithoutObject=p,t.GetWorkingObjTree=m,t.SaveDepartmentObject=b,t.SaveRole=s,t.SaveRoleMenu=u,t.SaveUserAuthorize=c,t.SaveUserObject=h;var r=n(a("b775"));function i(){return(0,r.default)({url:"/SYS/Role/GetRoleTree",method:"post"})}function s(e){return(0,r.default)({url:"/SYS/Role/SaveRole",method:"post",data:e})}function o(e){return(0,r.default)({url:"/SYS/Role/DeleteRole",method:"post",data:e})}function l(e){return(0,r.default)({url:"/SYS/Role/GetUserListByRole",method:"post",data:e})}function c(e){return(0,r.default)({url:"/SYS/Role/SaveUserAuthorize",method:"post",data:e})}function d(e){return(0,r.default)({url:"/SYS/Role/GetRoleWorkingObjListByUser",method:"post",data:e})}function u(e){return(0,r.default)({url:"/SYS/Role/SaveRoleMenu",method:"post",data:e})}function f(e){return(0,r.default)({url:"/SYS/Role/GetRoleMenusObj",method:"post",data:e})}function p(e){return(0,r.default)({url:"/SYS/User/GetUserRoleTreeWithoutObject",method:"post",data:e})}function m(e){return(0,r.default)({url:"/SYS/User/GetWorkingObjTree",method:"post",data:e})}function h(e){return(0,r.default)({url:"/SYS/User/SaveUserObject",method:"post",data:e})}function b(e){return(0,r.default)({url:"/SYS/User/SaveDepartmentObject",method:"post",data:e})}},"01e6":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100"},[a("MultiCompany",{tag:"component"})],1)},r=[]},"04440":function(e,t,a){"use strict";a.r(t);var n=a("9fe7"),r=a("99fb");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("69e6");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"966dbbd0",null);t["default"]=o.exports},"0536":function(e,t,a){"use strict";a("ead4")},"0bd0":function(e,t,a){"use strict";a("e0a3")},"0d16":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,"destroy-on-close":"",width:"70%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.isEdit?e._e():a("el-steps",{attrs:{active:e.active,simple:"","finish-status":"success"}},[a("el-step",{attrs:{title:"步骤 1"}}),a("el-step",{attrs:{title:"步骤 2"}}),a("el-step",{attrs:{title:"步骤 3"}})],1),a("el-row",{directives:[{name:"show",rawName:"v-show",value:0===e.active,expression:"active===0"}]},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账户",prop:"Login_Account"}},[a("el-input",{model:{value:e.form.Login_Account,callback:function(t){e.$set(e.form,"Login_Account","string"===typeof t?t.trim():t)},expression:"form.Login_Account"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名",prop:"Display_Name"}},[a("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name","string"===typeof t?t.trim():t)},expression:"form.Display_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门",prop:"Department_Id"}},[a("el-tree-select",{ref:"treeSelect",attrs:{"tree-params":e.treeParams},model:{value:e.form.Department_Id,callback:function(t){e.$set(e.form,"Department_Id",t)},expression:"form.Department_Id"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"工号",prop:"Job_Number"}},[a("el-input",{model:{value:e.form.Job_Number,callback:function(t){e.$set(e.form,"Job_Number","string"===typeof t?t.trim():t)},expression:"form.Job_Number"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"手机",prop:"Mobile"}},[a("el-input",{model:{value:e.form.Mobile,callback:function(t){e.$set(e.form,"Mobile","string"===typeof t?t.trim():t)},expression:"form.Mobile"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"邮箱",prop:"Email"}},[a("el-input",{model:{value:e.form.Email,callback:function(t){e.$set(e.form,"Email","string"===typeof t?t.trim():t)},expression:"form.Email"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"状态",prop:"Is_Enabled"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Enabled,callback:function(t){e.$set(e.form,"Is_Enabled",t)},expression:"form.Is_Enabled"}},[e._v("启用")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Enabled,callback:function(t){e.$set(e.form,"Is_Enabled",t)},expression:"form.Is_Enabled"}},[e._v("停用")])],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注信息",prop:"Remark"}},[a("el-input",{attrs:{clearable:"",autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"50",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1)],1),e.isEdit?e._e():a("div",{directives:[{name:"show",rawName:"v-show",value:1===e.active,expression:"active===1"}]},[a("div",{staticClass:"item-top"},[a("el-button",{staticStyle:{"margin-right":"16px"},attrs:{disabled:e.addBtnDisabled,type:"primary"},on:{click:e.handleAddClick}},[e._v("添加角色")]),a("el-input",{staticStyle:{width:"30%"},attrs:{placeholder:"请输入内容",clearable:""},on:{clear:e.searchValChange,input:e.searchValChange},model:{value:e.searchVal,callback:function(t){e.searchVal=t},expression:"searchVal"}})],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{staticClass:"cs-custom-table",attrs:{type:"index",width:"50",label:"ID"}}),a("el-table-column",{attrs:{align:"center",prop:"Name",label:"角色名称"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,r=t.$index;return[a("el-select",{attrs:{placeholder:"请选择"},on:{change:function(t){return e.roleChange(t,r)}},model:{value:n.Name,callback:function(t){e.$set(n,"Name",t)},expression:"row.Name"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.Name,value:e.Id,disabled:e.disabled}})})),1)]}}],null,!1,3966468834)}),a("el-table-column",{attrs:{align:"center",prop:"Type",label:"角色分类",width:"180"}}),a("el-table-column",{attrs:{align:"center",prop:"Remark",label:"备注信息"}}),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{color:"red"},attrs:{type:"text",size:"small"},on:{click:function(a){return e.remove(t.$index)}}},[e._v("移除")])]}}],null,!1,2129944735)})],1)],1),e.isEdit?e._e():a("div",{directives:[{name:"show",rawName:"v-show",value:2===e.active,expression:"active===2"}]},[a("company-select",{ref:"company",attrs:{"page-type":"2","user-id":e.userId}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleCancel}},[e._v("取 消")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:0!==e.active,expression:"active!==0"}],attrs:{type:"primary"},on:{click:e.pre}},[e._v("上一步")]),a("el-button",{directives:[{name:"show",rawName:"v-show",value:2!==e.active&&!e.isEdit,expression:"active!==2&&!isEdit"}],attrs:{type:"primary"},on:{click:e.next}},[e._v("下一步")]),2===e.active||e.isEdit?a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:function(t){return e.handleSubmit()}}},[e._v("确 定")]):e._e()],1)],1)},r=[]},1396:function(e,t,a){"use strict";a.r(t);var n=a("6f40"),r=a("449e");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("150f");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"4fdc1ca8",null);t["default"]=o.exports},"150f":function(e,t,a){"use strict";a("5cbc")},"163f":function(e,t,a){},"1aad":function(e,t,a){"use strict";a.r(t);var n=a("9ce1"),r=a("255d");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("0536");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"138786ea",null);t["default"]=o.exports},2062:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("a15b"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var n=a("0187"),r=a("6186");t.default={name:"UserRole",props:{userIds:{type:Array,default:function(){return[]}}},data:function(){return{title:"选择角色",roleLoading:!1,defaultProps:{children:"Children",label:"Label"},dialogVisible:!1,treeData:[],keyword:"",allSelected:!1}},computed:{allNodeIds:function(){return this.getAllTreeNodeIds(this.treeData)}},watch:{keyword:function(e){this.$refs.roleTree.filter(e)}},created:function(){},methods:{getRoleData:function(){var e=this;this.roleLoading=!0;var t=1===this.userIds.length?n.GetUserRoleTreeWithoutObject:n.GetRoleTree,a=1===this.userIds.length?{userId:this.userIds[0]}:{};return t(a).then((function(t){if(t.IsSucceed)if(e.treeData=t.Data,1===e.userIds.length){var a=e.findCheckedNodes(e.treeData);e.$refs.roleTree.setCheckedKeys(a.map((function(e){return e.Id})))}else e.$refs.roleTree.setCheckedKeys([])})).finally((function(){e.roleLoading=!1}))},findCheckedNodes:function(e){var t=this,a=[];return e.forEach((function(e){e.Is_Directory||e.Data.Check_Status&&a.push(e),e.Children&&e.Children.length>0&&(a=a.concat(t.findCheckedNodes(e.Children)))})),a},getAllTreeNodeIds:function(e){var t=this,a=[];return e.forEach((function(e){e.Is_Directory||a.push(e.Id),e.Children&&e.Children.length>0&&(a=a.concat(t.getAllTreeNodeIds(e.Children)))})),a},handleSelectAll:function(){this.allSelected=!this.allSelected,this.allSelected?this.$refs.roleTree.setCheckedKeys(this.allNodeIds):this.$refs.roleTree.setCheckedKeys([])},handleSubmit:function(){var e=this;if(this.userIds.length<=0)this.dialogVisible=!1;else{var t,a,i=this.$refs.roleTree.getCheckedNodes();1===this.userIds.length?(a=n.SaveUserAuthorize,t={userId:this.userIds[0],roles:i.filter((function(e){return!e.Is_Directory})).map((function(t){return{User_Id:e.userIds[0],Role_Id:t.Id}}))}):(a=r.BatchSaveAuthorize,t=i.map((function(t){return{Role_Id:t.Id,User_Id:e.userIds.join(",")}}))),a(t).then((function(t){!0===t.IsSucceed?(e.$message({type:"success",message:"操作成功"}),e.$emit("resetData")):e.$message.warning(t.Message)})).then((function(){e.dialogVisible=!1}))}},filterNode:function(e,t){return!e||-1!==t.Label.indexOf(e)},handleOpen:function(e){1===e.length?this.title="角色权限：".concat(e[0].Display_Name,"  ").concat(e[0].Mobile):this.title="角色权限",this.dialogVisible=!0,this.getRoleData()},handleClose:function(){this.dialogVisible=!1}}}},2098:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");t.default={name:"ItemComponent",props:{index:{type:Number},source:{type:Object,default:function(){return{}}}}}},"225e":function(e,t,a){"use strict";a.r(t);var n=a("613ed"),r=a("df8c");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("65c4");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"77388b50",null);t["default"]=o.exports},"255d":function(e,t,a){"use strict";a.r(t);var n=a("2062"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"2bce":function(e,t,a){"use strict";a.r(t);var n=a("676d"),r=a("fa56");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("dfd0");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"295e213c",null);t["default"]=o.exports},"2d86":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("15fd")),i=n(a("ade3")),s=n(a("c14f")),o=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("caad"),a("14d9"),a("13d5"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("9485"),a("d3b7"),a("2532"),a("159b");var l=a("6186"),c=n(a("89c1")),d=n(a("04440")),u=a("e144"),f=["Children"];t.default={components:{VirtualList:c.default},props:{departmentId:{type:String,default:""},userId:{type:String,default:""},pageType:{type:String,default:"1"}},data:function(){return{itemComponent:d.default,projectItems:[],factoryItems:[],filterCompany:"",filterProject:"",filterFactory:"",companyTree:[],currentNodeKey:"",isAllProject:!1,isAllFactory:!1}},computed:{objKey:function(){return"1"===this.pageType?"DepartmentId":"UserId"},objVal:function(){return"1"===this.pageType?this.departmentId:this.userId}},watch:{departmentId:function(){},filterCompany:function(e){this.$refs.tree.filter(e)},filterProject:function(e){this.handleFilter(e,this.projectItems)},filterFactory:function(e){this.handleFilter(e,this.factoryItems)}},mounted:function(){var e=this;return(0,o.default)((0,s.default)().m((function t(){return(0,s.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.fetchData();case 1:e.getCurrentData(e.objVal);case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;return(0,o.default)((0,s.default)().m((function t(){return(0,s.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetCompanyUnionProjectUnionFactoryObjByUserId)({}).then((function(t){if(t.IsSucceed){var a=t.Data,n=a.Factory_List,r=a.Company_List,i=a.Project_List;e.setData(n,r,i)}}));case 1:return t.a(2)}}),t)})))()},setData:function(e,t,a){var n=this;this.companyTree=t||[],e.forEach((function(e,t){var a={uid:(0,u.v4)(),text:e.Factory_Name,companyObjId:e.Company_Object_Id,factoryId:e.Factory_Id,checked:!1,show:!0,currentNodeKey:"",isCompanyAll:e.Is_All,change:function(e){n.selectPartAllChange(e,n.factoryItems)}};if(a.isCompanyAll){var r=n.findItemById(n.companyTree[0],a.companyObjId);r&&(a.text="工厂权限（".concat(null===r||void 0===r?void 0:r.Label,"）")),a.show=!1}n.factoryItems.push(a)})),a.forEach((function(e,t){var a={uid:(0,u.v4)(),text:e.Project_Name,projectId:e.Project_Id,companyObjId:e.Company_Object_Id,checked:!1,currentNodeKey:"",show:!0,isCompanyAll:e.Is_All,change:function(e){n.selectPartAllChange(e,n.projectItems)}};if(a.isCompanyAll){a.show=!1;var r=n.findItemById(n.companyTree[0],a.companyObjId);r&&(a.text="项目权限（".concat(r.Label,"）"))}n.projectItems.push(a)}))},getCurrentData:function(e){var t,a=this,n={};"1"===this.pageType?(t=l.GetSelectedPermissionByDepartmentId,n.departmentId=e):"2"===this.pageType&&(n.userId=e,t=l.GetSelectedPermissionByUserId),t(n).then((function(e){if(e.IsSucceed){if(!e.Data)return;var t=[];e.Data.forEach((function(e,n){e.CompanyObjectId&&t.push(e.CompanyObjectId),e.ProjectObjectId.forEach((function(e,t){var n=a.projectItems.find((function(t){return t.projectId===e}));n?(n.checked=!0,n.isCompanyAll&&a.projectItems.forEach((function(e){e.companyObjId===n.companyObjId&&(e.checked=!0)}))):"all"===e&&(a.isAllProject=!0,a.projectItems.forEach((function(e,t){e.isCompanyAll?(e.show=!1,e.checked=!0):e.show=!1})))})),e.FactoryObjectId.forEach((function(e,t){var n=a.factoryItems.find((function(t){return t.factoryId===e}));n?(n.checked=!0,n.isCompanyAll&&a.factoryItems.forEach((function(e){e.companyObjId===n.companyObjId&&(e.checked=!0)}))):"all"===e&&(a.isAllFactory=!0,a.factoryItems.forEach((function(e,t){e.isCompanyAll?(e.show=!1,e.checked=!0):e.show=!1})))}))})),a.$refs["tree"].setCheckedKeys(t)}else a.$message({message:e.Message,type:"error"})}))},selectAll:function(e,t){e?1===t?this.projectItems.forEach((function(e){e.isCompanyAll?e.checked=!0:e.show=!1})):this.factoryItems.forEach((function(e){e.isCompanyAll?e.checked=!0:e.show=!1})):1===t?this.projectItems.forEach((function(e){e.isCompanyAll||(e.show=!0),e.checked=!1})):this.factoryItems.forEach((function(e){e.isCompanyAll||(e.show=!0),e.checked=!1}))},handleFilter:function(e,t){var a,n=this.currentNodeKey;a=n?t.find((function(e){return e.isCompanyAll&&e.companyObjId===n})):t.find((function(e){return e.isCompanyAll})),a?a.isCompanyAll&&a.checked?t.forEach((function(e){e.show=a.uid===e.uid})):t.forEach((function(t){t.show=n?n===t.companyObjId&&!!t.text.includes(e):!!t.text.includes(e)&&!t.isCompanyAll})):t.forEach((function(e){e.show=!1}))},selectPartAllChange:function(e,t){e.isCompanyAll&&(e.checked?t.forEach((function(t){t.companyObjId===e.companyObjId&&(t.show=t.uid===e.uid,t.checked=t.uid===e.uid)})):t.forEach((function(t){t.companyObjId===e.companyObjId&&(t.show=!0,t.checked=!1)})))},getResultData:function(){var e=this,t=this.$refs["tree"].getCheckedKeys(),a=this.treeToArray(this.companyTree,t),n=[],r={},s=this.getChecked("project");s.result.forEach((function(t,a){r[t.companyObjId]?r[t.companyObjId].ProjectObjectId.push(t.projectId):r[t.companyObjId]=(0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)({},e.objKey,e.objVal),"ProjectObjectId",[t.projectId]),"FactoryObjectId",[]),"CompanyObjectId",""),"CompanyId",t.companyObjId)}));var o=this.getChecked("factory");o.result.forEach((function(t,a){r[t.companyObjId]?r[t.companyObjId].FactoryObjectId.push(t.factoryId):r[t.companyObjId]=(0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)({},e.objKey,e.objVal),"ProjectObjectId",[]),"FactoryObjectId",[t.factoryId]),"CompanyObjectId",""),"CompanyId",t.companyObjId)}));for(var l=0;l<a.length;l++){var c=a[l];c.checked&&(r[c.Id]?r[c.Id].CompanyObjectId=c.Id:r[c.Id]=(0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)({},this.objKey,this.objVal),"ProjectObjectId",[]),"FactoryObjectId",[]),"CompanyObjectId",c.Id),"CompanyId",c.Id))}for(var d in r)n.push(r[d]);return this.isAllFactory&&(n.length?n[0].FactoryObjectId=["all"]:n.push((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)({},this.objKey,this.objVal),"ProjectObjectId",[]),"FactoryObjectId",["all"]),"CompanyObjectId",""),"CompanyId",""))),this.isAllProject&&(n.length?n[0].ProjectObjectId=["all"]:n.push((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)({},this.objKey,this.objVal),"ProjectObjectId",["all"]),"FactoryObjectId",[]),"CompanyObjectId",""),"CompanyId",""))),n},getChecked:function(e){var t;if("project"===e){if(this.isAllProject)return{result:[]};t=this.projectItems}else{if(this.isAllFactory)return{result:[]};t=this.factoryItems}var a=t.filter((function(e){return!(!e.isCompanyAll||!e.checked)||e.checked}));return{result:a}},filterNode:function(e,t){return!e||-1!==t.Label.indexOf(e)},nodeClick:function(e,t){this.currentNodeKey=e.Id,this.isAllProject=!1,this.isAllFactory=!1,this.currentNodeData=e,this.handleFilter("",this.projectItems),this.handleFilter("",this.factoryItems)},treeToArray:function(e,t){var a=this;return e.reduce((function(e,n){n.checked=!!t.includes(n.Id);var i=n.Children,s=(0,r.default)(n,f);return e.concat(s,i&&i.length?a.treeToArray(i,t):[])}),[])},findItemById:function(e,t){var a=null,n=function(e){e.Id!==t?e.Children.forEach((function(e){return n(e)})):a=e};return n(e),a}}}},3431:function(e,t,a){},3751:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"60%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-row",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门名称",prop:"Display_Name"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"编号",prop:"Code"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"公司类型",prop:"Is_Company"}},[a("el-radio-group",{model:{value:e.form.Is_Company,callback:function(t){e.$set(e.form,"Is_Company",t)},expression:"form.Is_Company"}},[a("el-radio",{attrs:{label:!0}},[e._v("公司")]),a("el-radio",{attrs:{label:!1}},[e._v("非公司")])],1)],1)],1),e.form.Is_Company?e._e():a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"部门类型",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择部门类型"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},e._l(e.Option,(function(e,t){return a("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Value}})})),1)],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上级节点",prop:"Parent_Id"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"w100",attrs:{"select-params":{clearable:!1},"tree-params":e.treeParams},model:{value:e.form.Parent_Id,callback:function(t){e.$set(e.form,"Parent_Id",t)},expression:"form.Parent_Id"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序号",prop:"Sort"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Sort,callback:function(t){e.$set(e.form,"Sort",e._n(t))},expression:"form.Sort"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"负责人",prop:"Leader"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",filterable:"",placeholder:"请选择"},model:{value:e.form.Leader,callback:function(t){e.$set(e.form,"Leader",t)},expression:"form.Leader"}},e._l(e.users,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"独立管理"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Independent_Authorize,callback:function(t){e.$set(e.form,"Is_Independent_Authorize",t)},expression:"form.Is_Independent_Authorize"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Independent_Authorize,callback:function(t){e.$set(e.form,"Is_Independent_Authorize",t)},expression:"form.Is_Independent_Authorize"}},[e._v("否")])],1)],1),a("el-col",{attrs:{span:12}},[e.form.Is_Independent_Authorize?a("el-form-item",{attrs:{label:"管理员",prop:"Department_Administrator"}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"请选择"},model:{value:e.form.Department_Administrator,callback:function(t){e.$set(e.form,"Department_Administrator",t)},expression:"form.Department_Administrator"}},e._l(e.users,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1):e._e()],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{clearable:"",autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"50",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleCancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit()}}},[e._v("确 定")])],1)],1)},r=[]},"3b91":function(e,t,a){"use strict";a.r(t);var n=a("0d16"),r=a("d5ac");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("7bd3");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"663820de",null);t["default"]=o.exports},"3c22d":function(e,t,a){"use strict";a.r(t);var n=a("798f"),r=a("8280");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("8aff");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"18b5c288",null);t["default"]=o.exports},"449e":function(e,t,a){"use strict";a.r(t);var n=a("d188"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"480a":function(e,t,a){"use strict";a.r(t);var n=a("98a8"),r=a("83fe");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("0bd0");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"1fc3ddc7",null);t["default"]=o.exports},5368:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af");var r=n(a("225e")),i=a("8930"),s=a("0187");t.default={components:{CompanySelect:r.default},props:{ids:{type:Array,default:function(){return[]}}},data:function(){return{dialogVisible:!1,btnLoading:!1,title:"",pageType:"1",userId:""}},methods:{handleClose:function(){this.dialogVisible=!1},handleOpen:function(e,t,a){var n,r,i;"用户"===t?(this.pageType="2",1===a.length?(this.title="".concat(t,"授权:").concat((null===(n=a[0])||void 0===n?void 0:n.Display_Name)||"","  ").concat((null===(r=a[0])||void 0===r?void 0:r.Mobile)||""),this.userId=null===(i=a[0])||void 0===i?void 0:i.Id):this.title="".concat(t,"授权")):(this.pageType="1",this.title="".concat(t,"授权:")+(null===e||void 0===e?void 0:e.Label)||"");this.dialogVisible=!0},submit:function(){var e,t=this;e="1"===this.pageType?i.SaveDepartmentObject:s.SaveUserObject;var a=this.$refs["company"].getResultData();this.btnLoading=!0,e(a).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.handleClose()):t.$message({message:e.Message,type:"error"}),t.btnLoading=!1}))}}}},5502:function(e,t,a){"use strict";a.r(t);var n=a("ba56"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"5cbc":function(e,t,a){},"613ed":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"list-wrap"},[a("div",{staticClass:"list-item"},[a("div",{staticClass:"item-top"},[a("span",{staticClass:"item-label"},[e._v("公司")]),a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.filterCompany,callback:function(t){e.filterCompany=t},expression:"filterCompany"}})],1),a("el-divider"),a("div",{staticClass:"item-content"},[a("el-tree",{ref:"tree",attrs:{data:e.companyTree,"current-node-key":e.currentNodeKey,"show-checkbox":"","expand-on-click-node":!1,"node-key":"Id","filter-node-method":e.filterNode,"check-strictly":"","default-expand-all":"","default-checked-keys":[],props:{children:"Children",label:"Label"}},on:{"node-click":e.nodeClick}})],1)],1),a("div",{staticClass:"list-item"},[a("div",{staticClass:"item-top"},[a("span",{staticClass:"item-label"},[e._v("项目")]),a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.filterProject,callback:function(t){e.filterProject=t},expression:"filterProject"}})],1),a("el-divider"),e.currentNodeKey?e._e():a("el-checkbox",{staticClass:"cs-cbx",on:{change:function(t){return e.selectAll(t,1)}},model:{value:e.isAllProject,callback:function(t){e.isAllProject=t},expression:"isAllProject"}},[a("strong",[e._v("项目权限（所有项目）")])]),a("virtual-list",{staticClass:"cs-list",attrs:{"data-key":"uid","data-sources":e.projectItems,keeps:25,"data-component":e.itemComponent}})],1),a("div",{staticClass:"list-item"},[a("div",{staticClass:"item-top"},[a("span",{staticClass:"item-label"},[e._v("工厂")]),a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.filterFactory,callback:function(t){e.filterFactory=t},expression:"filterFactory"}})],1),a("el-divider"),e.currentNodeKey?e._e():a("el-checkbox",{staticClass:"cs-cbx",on:{change:function(t){return e.selectAll(t,2)}},model:{value:e.isAllFactory,callback:function(t){e.isAllFactory=t},expression:"isAllFactory"}},[a("strong",[e._v("工厂权限（所有工厂）")])]),a("virtual-list",{staticClass:"cs-list",attrs:{"data-key":"uid","data-sources":e.factoryItems,keeps:25,"data-component":e.itemComponent}})],1)])},r=[]},"65c4":function(e,t,a){"use strict";a("f44b")},"676d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-container",{staticStyle:{height:"100%"}},[a("el-aside",{staticStyle:{"padding-right":"12px"},attrs:{width:"386px"}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",staticStyle:{display:"flex","flex-direction":"row","align-items":"center","justify-content":"space-between"},attrs:{slot:"header"},slot:"header"},[a("span",{staticClass:"l-title"},[e._v("部门列表")]),a("div",[a("el-button",{attrs:{disabled:""}},[e._v("导入")]),a("el-button",{on:{click:e.createDept}},[e._v("新增")]),a("el-button",{attrs:{disabled:!e.currentNodeId},on:{click:function(t){return e.handleOpenDataRole("部门")}}},[e._v("部门授权")])],1)]),a("div",{staticClass:"scroll-wrapper"},[a("el-tree",{ref:"tree",attrs:{data:e.treeData,"node-key":"Id","highlight-current":"","current-node-key":e.currentNodeId,props:{label:"Label",children:"Children",isLeaf:!1},"default-expand-all":"","expand-on-click-node":!1,"filter-node-method":e.filterNode},on:{"current-change":e.currentNodeChange},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.node,r=t.data;return a("div",{staticClass:"custom-tree-node"},[r.Data.Is_Company?a("i",{staticClass:"iconfont icon-building-filled",staticStyle:{color:"rgba(18, 157, 131, 0.8)","margin-right":"4px"}}):a("svg-icon",{staticClass:"icon-svg",attrs:{name:"icon-users","icon-class":"icon-users"}}),a("span",{staticStyle:{overflow:"hidden",flex:"auto","text-overflow":"ellipsis",width:"0"},attrs:{title:n.label}},[e._v(e._s(n.label))]),n.isCurrent?a("span",{staticClass:"tree-btns"},[a("i",{staticClass:"el-icon-delete",staticStyle:{color:"rgba(251, 107, 127, 1)"},on:{click:function(t){return e.deleteTreeItem(n)}}}),a("i",{staticClass:"el-icon-edit",on:{click:function(t){return e.editTreeItem(n)}}})]):e._e()],1)}}])})],1)])],1),a("el-main",{staticStyle:{padding:"0"}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"filters",staticStyle:{"font-size":"14px"}},[a("span",{staticStyle:{"font-weight":"bold","font-size":"18px","padding-top":"3px","margin-right":"16px"}},[e._v(e._s(e.currentNode?e.currentNode.Data.Display_Name:""))]),a("div",[a("el-input",{staticStyle:{width:"160px"},attrs:{placeholder:"账户/姓名/手机号",clearable:""},on:{input:e.handleSearch},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.query.Search,callback:function(t){e.$set(e.query,"Search",t)},expression:"query.Search"}}),e._e()],1)]),a("div",{staticClass:"btn-group"},[a("el-button",{attrs:{type:"success"},on:{click:function(t){return e.handleOpenUserAddEdit("add")}}},[e._v("新增用户")]),a("el-button",{attrs:{type:"primary",disabled:e.Ids.length<=0},on:{click:e.handleOpenUserRole}},[e._v("角色权限")]),a("el-button",{attrs:{type:"primary",disabled:e.Ids.length<=0},on:{click:function(t){return e.handleOpenDataRole("用户")}}},[e._v("用户授权")]),a("el-button",{attrs:{type:"primary",disabled:0===e.Ids.length||e.Ids.length>1},on:{click:e.UpdateLeader}},[e._v("设为负责人")]),a("el-button",{attrs:{type:"success"},on:{click:function(t){e.imptDlgVisible=!0}}},[e._v("导入")]),a("el-button",{attrs:{disabled:e.Ids.length<=0,type:"primary"},on:{click:e.Reset}},[e._v("重置密码")]),a("el-button",{attrs:{type:"warning",disabled:e.Ids.length<=0},on:{click:e.UpdateStates}},[e._v("停用")]),a("el-button",{attrs:{type:"danger",disabled:e.Ids.length<=0},on:{click:e.delUser}},[e._v("删除")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{height:"100%"}},[a("DynamicDataTable",{ref:"table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,border:"",stripe:"",total:e.query.TotalCount},on:{gridSizeChange:e.gridSizeChange,gridPageChange:e.gridPageChange,"sort-change":e.gridSortChange,multiSelectedChange:e.selectedChange,handleRowClick:e.handleRowClick},scopedSlots:e._u([{key:"Display_Name",fn:function(t){var n=t.row;return[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-tooltip",{attrs:{disabled:!e.currentNode.Data||e.currentNode.Data.Leader!==n.Id,effect:"dark",content:"负责人",placement:"top"}},[a("div",{staticClass:"mark"},[e.currentNode.Data&&e.currentNode.Data.Leader===n.Id?a("el-avatar",{staticStyle:{background:"#f56c6c"}},[e._v("责")]):e._e()],1)]),a("el-tooltip",{attrs:{disabled:!e.currentNode.Data||e.currentNode.Data.Department_Administrator!==n.Id,effect:"dark",content:"管理员",placement:"top"}},[a("div",{staticClass:"mark"},[e.currentNode.Data&&e.currentNode.Data.Department_Administrator===n.Id?a("el-avatar",{staticStyle:{background:"#67c23a"}},[e._v("管")]):e._e()],1)]),a("span",{staticStyle:{"white-space":"nowrap","margin-left":"8px",display:"inline-block"}},[e._v(e._s(n.Display_Name))])],1)]}},{key:"op",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return t.stopPropagation(),e.handleOpenUserAddEdit("edit",n)}}},[e._v("编辑")])]}}])})],1)])],1)],1),a("add-edit-dep",{ref:"AddEditDep",attrs:{"tree-data":e.treeData,"multi-mode":!0},on:{changeData:e.getDeptTreeData}}),e.showUserDialog?a("add-edit-user",{ref:"AddEditUser",attrs:{"show-dialog":e.showUserDialog,"department-id":e.currentNodeId},on:{"update:showDialog":function(t){e.showUserDialog=t},"update:show-dialog":function(t){e.showUserDialog=t},resetData:function(){e.getPageList(e.query)}}}):e._e(),a("user-role",{ref:"UserRole",attrs:{"user-ids":e.Ids},on:{resetData:function(){e.getPageList(e.query)}}}),a("DepartmentAuth",{ref:"DataRole",attrs:{ids:e.usersOrDepts,"auth-type":e.authType},on:{resetData:function(){e.getPageList(e.query)}}}),a("table-info-dialog",{ref:"tableInfoDialog",on:{handleEdit:e.handleEditRoles}}),a("el-dialog",{attrs:{title:"导入用户",visible:e.imptDlgVisible,width:"30%"},on:{"update:visible":function(t){e.imptDlgVisible=t}}},[a("el-upload",{ref:"upload",staticStyle:{width:"180px",margin:"0 auto"},attrs:{"before-upload":e.importData,"on-success":e.importSuccess,limit:1,"show-file-list":!1,accept:".xls, .xlsx",action:"string"}},[a("div",{staticStyle:{"text-align":"center"}},[a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])]),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"large",icon:"el-icon-download"},on:{click:e.handleDownload}},[e._v(" 下载导入模板 ")])],1)],1)],1)},r=[]},"69e6":function(e,t,a){"use strict";a("163f")},"6f40":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"show",rawName:"v-show",value:e.dialogVisible,expression:"dialogVisible"}],ref:"dialog",staticClass:"card-dialog-container hidden-scroll"},[a("i",{staticClass:"el-icon-close dialog-close",on:{click:e.handleClose}}),a("div",{staticClass:"card-dialog-box"},[a("div",{staticClass:"top-info"},[a("svg-icon",{attrs:{"class-name":"icon-users","icon-class":"icon-user-blue"}}),a("span",{staticClass:"user-name"},[e._v(e._s(e.Display_Name))]),a("span",{staticClass:"user-status"},[e._v("（"+e._s(e.UserStatusName)+"）")])],1),a("c-section",{attrs:{"data-list":e.WorkingObjectAuth,type:"1","user-id":e.userId,"has-edit":"","type-name":"数据权限对应角色"},on:{handleEdit:e.handleEdit}}),a("c-section",{attrs:{"data-list":e.GroupAuth,type:"2","type-name":"所在用户群组角色"}})],1)])},r=[]},7626:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{dataList:{type:Array,default:function(){return[]}},typeName:{type:String,default:""},type:{type:String,default:"1"},hasEdit:{type:Boolean,default:!1},squareColor:{type:Object,default:function(){return{background:"#3ECC93"}}}},data:function(){return{}},methods:{handleEdit:function(){this.$emit("handleEdit")}}}},"77ae":function(e,t,a){"use strict";a.r(t);var n=a("d360"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"798f":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"section-container"},[a("div",{staticClass:"top-info"},[a("span",{staticClass:"icon-square",style:e.squareColor}),a("span",{staticClass:"title"},[e._v(e._s(e.typeName))]),a("div",{staticClass:"dotted-line"}),e.hasEdit?a("el-button",{attrs:{size:"large",type:"text"},on:{click:e.handleEdit}},[e._v("编辑")]):e._e()],1),e._l(e.dataList,(function(t,n){return a("div",{key:n,staticClass:"data-card-box"},["1"===e.type?a("svg-icon",{attrs:{"icon-class":"icon-projs"}}):a("svg-icon",{attrs:{"icon-class":"icon-users"}}),a("span",{staticClass:"card-box-title"},[e._v(e._s(t.Name))]),a("div",{staticClass:"tag-box"},e._l(t.Value,(function(t,n){return a("el-tag",{key:n,attrs:{type:"info"}},[e._v(e._s(t.RoleName))])})),1)],1)}))],2)},r=[]},"7bd3":function(e,t,a){"use strict";a("ad506")},"7ce1":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530"));a("d9e2"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("d866"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("2532");var i=a("6186"),s=a("0187"),o=a("ed08"),l=n(a("225e"));t.default={components:{CompanySelect:l.default},props:{DepartmentId:{type:String,default:""},showDialog:{type:Boolean,default:!1}},data:function(){var e=function(e,t,a){if(t){var n=/^[a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*@[a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*[.][a-zA-Z]{2,4}$/.test(t);n?a():a(new Error("邮箱格式不正确"))}else a()},t=function(e,t,a){var n=/^1[3456789]\d{9}$/.test(t);n?a():a(new Error("手机号格式不正确"))};return{active:0,userId:"",title:"",searchVal:"",tableData:[],options:[],isEdit:!0,btnLoading:!1,addBtnDisabled:!1,form:{Login_Account:"",Display_Name:"",Department_Id:"",Mobile:"",Email:"",Is_Enabled:!0,Job_Number:"",Remark:""},treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},rules:{Login_Account:[{required:!0,message:"请输入账户名",trigger:"blur"}],Display_Name:[{required:!0,message:"请输入姓名",trigger:"blur"}],Department_Id:[{required:!0,message:"请选择",trigger:"change"}],Mobile:[{required:!0,validator:t,trigger:"blur"}],Email:[{validator:e,trigger:"blur"}]}}},computed:{dialogVisible:{set:function(e){this.$emit("update:showDialog",e)},get:function(){return this.showDialog}}},methods:{handleOpen:function(e,t){var a=this;this.isEdit="add"!==e,this.dialogVisible=!0,this.getRole(),(0,i.GetDepartmentTree)({isAll:!1}).then((function(e){a.$refs.treeSelect.treeDataUpdateFun(e.Data)})),"add"===e?(this.title="添加用户",this.form.Department_Id=this.DepartmentId):(this.title="编辑用户",this.$nextTick((function(){a.form=JSON.parse(JSON.stringify(t))})))},handleAddClick:function(){this.tableData.length!==this.options.length?this.tableData.push({Id:"",Name:"",Type:"",Remark:""}):this.addBtnDisabled=!0},roleChange:function(e,t){var a=this.options.find((function(t){return t.Id===e}));this.tableData[t].Id=e,this.tableData[t].Name=a.Name,this.tableData[t].Type=a.Type,this.tableData[t].Remark=a.Remark,this.tData=(0,o.deepClone)(this.tableData)},searchValChange:function(e){this.tableData=e?(this.tData||[]).filter((function(e){return e.Name.includes(e)})):(0,o.deepClone)(this.tData)},getRole:function(){var e=this;(0,s.GetRoleList)({}).then((function(t){t.IsSucceed?e.options=t.Data.map((function(e){return e.disabled=!1,e})):e.$message({message:t.Message,type:"error"})}))},handleClose:function(){this.resetForm("form"),this.dialogVisible=!1},remove:function(e){this.tableData.splice(e,1)},pre:function(){this.active--},next:function(){var e=this;if(0===this.active)this.$refs.form.validate((function(t){t&&e.active++}));else if(1===this.active){var t=this.tableData.every((function(e){return e.Name}));t?this.active++:this.$message({message:"角色不能为空",type:"warning"})}else this.active++},handleSubmit:function(){var e=this;this.btnLoading=!0;var t=(0,r.default)({},this.form);this.isEdit||(t.Permissions=this.$refs["company"].getResultData(),t.Roles=this.tableData.map((function(e){return e.Id}))),(0,i.SaveUser)(t).then((function(t){t.IsSucceed?(e.$message({type:"success",message:e.title="修改成功"}),e.dialogVisible=!1,e.$emit("resetData",!0),e.resetForm("form")):e.$message({type:"error",message:t.Message})})).finally((function(t){e.btnLoading=!1}))},handleCancel:function(){this.resetForm("form")},resetForm:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1}}}},8081:function(e,t,a){"use strict";a("3431")},8280:function(e,t,a){"use strict";a.r(t);var n=a("7626"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"83fe":function(e,t,a){"use strict";a.r(t);var n=a("5368"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},8930:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetUserByRoleName=i,t.GetUserServiceVersion=s;var r=n(a("b775"));function i(e){return(0,r.default)({url:"/SYS/User/GetUserByRoleName",method:"post",data:e})}function s(e){return(0,r.default)({url:"/master/tenantuser/GetUserServiceVersion",method:"post",data:e})}},"8aff":function(e,t,a){"use strict";a("f8d1")},"92ca":function(e,t,a){"use strict";a.r(t);var n=a("01e6"),r=a("77ae");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=o.exports},9584:function(e,t,a){},"98a8":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"80%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?a("company-select",{ref:"company",attrs:{"department-id":e.ids[0],"user-id":e.userId,"page-type":e.pageType}}):e._e(),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},9929:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),i=n(a("c14f")),s=n(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("25f0");var o=a("6186"),l=n(a("1396")),c=n(a("0f97")),d=n(a("be82")),u=n(a("3b91")),f=n(a("1aad")),p=n(a("480a")),m=a("c9d9");t.default={name:"MultiCompany",components:{AddEditDep:d.default,AddEditUser:u.default,UserRole:f.default,DepartmentAuth:p.default,DynamicDataTable:c.default,TableInfoDialog:l.default},data:function(){return{loading:!1,imptDlgVisible:!1,currentNodeId:"",currentNode:{Data:{}},treeData:[],tbData:[],showUserDialog:!1,tbConfig:{Is_Page:!0,Height:0,Pager_Align:"right",Row_Number:30,Is_Auto_Width:!1,Is_Row_Number:!0,Is_Sortable:!0,Is_Select:!0,Op_Width:80},query:{Date_Range:[],Page:1,PageSize:30,SortName:"",SortOrder:"",TotalCount:0,DepartmentId:""},columns:[{Code:"Login_Account",Display_Name:"账户名",Type:"text",Filter_Type:"text",Is_Display:!0,Width:100,Align:"left",Is_CustomSort:!0,Sort_Type:""},{Code:"Display_Name",Display_Name:"姓名",Type:"text",Filter_Type:"text",Is_Display:!0,Width:160,Align:"left",Is_CustomSort:!0,Sort_Type:""},{Code:"Mobile",Display_Name:"手机号",Type:"text",Is_Display:!0,Is_Frozen:!1,Width:100,Align:"left",Is_Formatter:!1,Formatter:"",Is_CustomSort:!0,Sort_Type:""},{Code:"Email",Display_Name:"邮箱",Type:"text",Filter_Type:"text",Range:"",Is_Display:!0,Is_Frozen:!1,Width:160,Align:"left",Is_Formatter:!1,Formatter:"",Is_CustomSort:!0,Sort_Type:""},{Code:"DepartmentName",Display_Name:"部门",Type:"text",Filter_Type:"text",Range:"",Is_Display:!0,Is_Frozen:!1,Width:0,Align:"left",Is_Formatter:!1,Formatter:"",Is_CustomSort:!0,Sort_Type:""},{Code:"UserStatusName",Display_Name:"状态",Type:"text",Filter_Type:"text",Range:"",Is_Display:!0,Is_Frozen:!1,Width:0,Align:"left",Is_Formatter:!1,Formatter:"",Is_CustomSort:!0,Sort_Type:""},{Code:"Login_Lasttime",Display_Name:"最后登录时间",Type:"date",Filter_Type:"",Range:"",Is_Display:!0,Is_Edit:!0,Is_Frozen:!1,Width:160,Align:"left",Is_Formatter:!1,Formatter:"yyyy-MM-dd HH:mm",Is_CustomSort:!0,Sort_Type:""}],selected:[],authType:""}},computed:{Ids:function(){return this.selected.map((function(e){return e.Id}))},usersOrDepts:function(){return"用户"===this.authType?this.Ids:"部门"===this.authType?[this.currentNodeId]:[]}},created:function(){var e=this;this.getDeptTreeData().then((function(){e.getPageList(e.query)}))},methods:{getDeptTreeData:function(e){var t=this;return(0,s.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,o.GetDepartmentTree)({isAll:!1}).then((function(a){a.IsSucceed&&(t.treeData=a.Data,e&&t.$nextTick((function(e){var a;t.currentNode=t.treeData[0],null===(a=t.$refs.tree)||void 0===a||a.setCurrentKey(t.currentNodeId)})))}));case 1:return a.a(2,a.v)}}),a)})))()},getPageList:function(e){var t=this;e||(e=this.query),this.loading=!0;var a=(0,r.default)({},e);return delete a["Date_Range"],(0,o.GetUserPage)(a).then((function(e){e.IsSucceed&&(t.tbData=e.Data.Data,t.query.TotalCount=e.Data.TotalCount)})).finally((function(){t.loading=!1}))},UpdateLeader:function(){var e=this;1===this.Ids.length?(0,o.UpdateDepartmentLeader)({departmentId:this.currentNodeId,userId:this.Ids.toString()}).then(function(){var t=(0,s.default)((0,i.default)().m((function t(a){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!a.IsSucceed){t.n=2;break}return e.$message({type:"success",message:"设置成功!"}),t.n=1,e.getDeptTreeData(e.currentNodeId);case 1:e.getPageList(e.query);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}()):this.$message({type:"error",message:"负责人只能单个设置！"})},UpdateStates:function(){var e=this;(0,o.UpdateUserStates)({userIds:this.Ids.toString(),status:"is_enabled"}).then((function(t){!0===t.IsSucceed&&(e.$message({type:"success",message:"停用成功!"}),e.getPageList(e.query))}))},delUser:function(){var e=this;this.$confirm("是否删除该用户?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.DeleteUser)({Ids:e.Ids.toString()}).then((function(t){!0===t.IsSucceed&&(e.$message({type:"success",message:"删除成功"}),e.getPageList(e.query))}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},Reset:function(){var e=this;(0,o.ResetPassword)({id:this.Ids.toString()}).then((function(t){!0===t.IsSucceed?(e.$message({type:"success",message:t.Message}),e.getPageList(e.query)):e.$message({type:"error",message:t.Message})}))},handleDownload:function(){var e=m.fileUploadOriginUrl+"/Template/UserImportTemplate.xlsx";window.open(e)},importData:function(e){var t=this,a=new FormData;return a.append("files",e),(0,o.ImportUser)(a).then((function(e){if(e.IsSucceed||null==e.data)e.IsSucceed?(t.$message({message:e.Data,type:"success"}),t.getPageList(t.query)):(t.$message({message:e.Message,type:"error"}),t.$emit("resetData"));else{var a=m.fileUploadOriginUrl+e.Data;window.open(a)}t.imptDlgVisible=!1})),!1},importSuccess:function(){},debounce:function(e,t){null!=this.timer&&clearTimeout(this.timer),this.timer=setTimeout(e,t)},handleSearch:function(){this.query.Page=1,this.debounce(this.getPageList,500)},currentNodeChange:function(e){this.currentNodeId=e.Id,this.currentNode=e,this.query.DepartmentId=this.currentNodeId,this.query.Page=1,this.getPageList(this.query)},filterNode:function(e,t){return!e||-1!==t.Label.indexOf(e)},handleOpenAddEdit:function(e,t){this.$refs.AddEditDep.handleOpen(e,t)},handleRowClick:function(e){e.row;return!1},handleEditRoles:function(){this.handleOpenUserRole()},handleOpenUserRole:function(){this.$refs.UserRole.handleOpen(this.selected)},handleOpenDataRole:function(e){this.authType=e;var t=this.$refs["tree"].getCurrentNode();this.$refs.DataRole.handleOpen(t,e,this.selected)},createDept:function(){var e,t;this.currentNode?(e="add",t={Data:{Parent_Id:this.currentNodeId}}):(e="add",t=void 0),this.handleOpenAddEdit(e,t)},deleteTreeItem:function(e){var t=this,a=this.tbData.length>0?2:1,n=1===a?"确认删除本部门？":"尚有用户存在，请移除后再删除。",r={confirmButtonText:1===a?"删除":"关闭",type:"warning"};2===a&&(r.showCancelButton=!1),this.$confirm(n,"提示",r).then((function(){2!==a&&(0,o.DeleteDepartment)({id:e.data.Id}).then((function(e){!0===e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.currentNodeId="",t.currentNode=null,t.getDeptTreeData()):t.$message.warning(e.Message)}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},editTreeItem:function(e){this.handleOpenAddEdit("edit",e.data)},handleOpenUserAddEdit:function(e,t){var a=this;this.showUserDialog=!0,this.$nextTick((function(n){a.$refs.AddEditUser.handleOpen(e,t)}))},gridSizeChange:function(e){e.page;var t=e.size;this.tbConfig.Row_Number=this.query.PageSize=t,this.query.Page=1,this.getPageList(this.query)},gridPageChange:function(e){var t=e.page;this.query.Page=t,this.getPageList(this.query)},gridSortChange:function(e){e.column,e.prop,e.order},selectedChange:function(e){this.selected=e}}}},"99fb":function(e,t,a){"use strict";a.r(t);var n=a("2098"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"9ce1":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("keep-alive",[a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"36%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-card",{staticClass:"card",staticStyle:{border:"none"},attrs:{shadow:"none"}},[a("div",{staticStyle:{height:"450px",display:"flex","flex-direction":"column"}},[a("el-input",{staticClass:"c-input",attrs:{clearable:"",placeholder:"输入关键字进行过滤"},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}}),a("div",{staticClass:"scroll-wrapper",staticStyle:{flex:"auto",height:"0","overflow-y":"auto","margin-top":"16px"}},[a("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.roleLoading,expression:"roleLoading"}],ref:"roleTree",attrs:{"filter-node-method":e.filterNode,"show-checkbox":"",data:e.treeData,props:e.defaultProps,"node-key":"Id","check-on-click-node":"","default-expand-all":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.data;return a("span",{staticClass:"custom-tree-node cs-z-ellipsis"},[a("span",{staticClass:"cs-z-ellipsis",staticStyle:{width:"50%"}},[n.Is_Directory?a("svg-icon",{staticStyle:{margin:"0 5px"},attrs:{"icon-class":"icon-folder","class-name":"class-icon"}}):a("svg-icon",{staticStyle:{margin:"0 5px"},attrs:{"icon-class":"icon-users","class-name":"class-icon"}}),e._v(e._s(n.Label)+" ")],1),n.Data.Remark.length<18?a("span",{staticClass:"remark cs-z-ellipsis",staticStyle:{width:"50%"}},[e._v(e._s(n.Data.Remark))]):a("el-tooltip",{attrs:{"popper-class":"item",effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(e._s(n.Data.Remark))]),a("span",{staticClass:"remark cs-z-ellipsis",staticStyle:{width:"50%"}},[e._v(e._s(n.Data.Remark))])])],1)}}])})],1)],1)]),a("div",{staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleSelectAll}},[e._v(e._s(e.allSelected?"清空":"全选"))]),a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确定")])],1)],1)],1)},r=[]},"9fe7":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"show",rawName:"v-show",value:e.source.show||e.source.currentNodeKey,expression:"source.show||source.currentNodeKey"}],class:["cs-item-x",{m10:!e.source.isCompanyAll}]},[a("el-checkbox",{on:{change:function(t){return e.source.change(e.source)}},model:{value:e.source.checked,callback:function(t){e.$set(e.source,"checked",t)},expression:"source.checked"}},[a("span",{class:{isStrong:e.source.isCompanyAll}},[e._v(e._s(e.source.text))])]),e.source.isAll?a("el-divider"):e._e()],1)},r=[]},ad506:function(e,t,a){},ba56:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9c4"),a("b64b"),a("d3b7");var r=n(a("5530")),i=a("6186");t.default={props:{treeData:{type:Array,default:function(){return function(){return[]}}},dept:{type:Object,default:function(){return{}}},multiMode:{type:Boolean,default:!0}},data:function(){return{title:"",dialogVisible:!1,btnLoading:!1,users:[],form:{Display_Name:"",Type:"",Parent_Id:"",Code:"",Sort:0,Remark:"",Is_Company:!1,Business_Range:[]},treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},Option:[],rules:{Display_Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Type:[{required:!0,message:"请选择部门类型",trigger:"blur"}],Sort:[{required:!0,message:"排序不能为空"},{type:"number",message:"排序必须为数字值"}],Is_Company:[{required:!0,message:"请选择是否为公司类型",trigger:"blur"}]}}},watch:{"form.Is_Independent_Authorize":function(){this.form.Is_Independent_Authorize||(this.form.Department_Administrator="")},form:function(){this.form.Id&&this.getAllDeptUsers()}},created:function(){var e=this;(0,i.GetDictionaryDetailListByCode)({dictionaryCode:"DepartmentType"}).then((function(t){e.Option=t.Data}))},methods:{getAllDeptUsers:function(){var e=this;(0,i.GetUserList)({DepartmentId:""}).then((function(t){t.IsSucceed&&(e.users=t.Data)}))},handleOpen:function(e,t){var a,n=this;(t&&t.Data&&!t.Data.Business_Range&&(t.Data.Business_Range=[]),this.dialogVisible=!0,this.$nextTick((function(e){n.treeParams.data=n.treeData,n.$refs.treeSelect.treeDataUpdateFun(n.treeData)})),"add"===e)?(this.title="添加",this.form={Display_Name:"",Type:"",Parent_Id:"",Code:"",Sort:0,Remark:"",Is_Independent_Authorize:!1,Business_Range:[],Is_Company:!1},this.form.Parent_Id=(null===(a=this.treeData[0])||void 0===a?void 0:a.Id)||""):this.$nextTick((function(e){n.form=(0,r.default)((0,r.default)({},n.form),JSON.parse(JSON.stringify(t.Data))),n.title=n.form.Id?"编辑":"新增"}))},handleClose:function(){this.resetForm("form")},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,i.UpdateDepartment)(e.form).then((function(t){!0===t.IsSucceed?(e.$message({type:"success",message:e.title+"成功"}),e.$emit("changeData"),e.dept.Leader=e.form.Leader,e.dept.Department_Administrator=e.form.Department_Administrator,e.form={Display_Name:"",Type:"",Parent_Id:"",Code:"",Sort:0,Remark:"",Business_Range:[],Is_Company:!1},e.dialogVisible=!1):e.$message({message:t.Message,type:"error"})})).finally((function(t){e.btnLoading=!1}))}))},handleCancel:function(){this.resetForm("form")},resetForm:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1}}}},be82:function(e,t,a){"use strict";a.r(t);var n=a("3751"),r=a("5502");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("8081");var s=a("2877"),o=Object(s["a"])(r["default"],n["a"],n["b"],!1,null,"4a351de1",null);t["default"]=o.exports},d188:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("3c22d")),i=a("6186");t.default={components:{cSection:r.default},data:function(){return{dialogVisible:!1,showTimer:2,Display_Name:"",UserStatusName:"",WorkingObjectAuth:[],GroupAuth:[],userId:"",inEdit:!1}},methods:{handleClose:function(){this.$refs.dialog.classList.remove("dp-block"),this.dialogVisible=!1},handleOpen:function(e){var t=this;this.dialogVisible=!0,this.UserStatusName=e.UserStatusName,this.Display_Name=e.Display_Name,this.userId=e.Id,(0,i.GetAuthObjByUserId)({userId:e.Id}).then((function(e){t.WorkingObjectAuth=e.Data.WorkingObjectAuth,t.GroupAuth=e.Data.GroupAuth})),this.$nextTick((function(e){t.$refs.dialog.classList.add("dp-block")}))},handleEdit:function(e){this.dialogVisible=!0,this.inEdit=!0,this.$emit("handleEdit")},handleHidden:function(){this.dialogVisible=!1}}}},d360:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2bce"));t.default={name:"SysDepartment",components:{MultiCompany:r.default}}},d5ac:function(e,t,a){"use strict";a.r(t);var n=a("7ce1"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},df8c:function(e,t,a){"use strict";a.r(t);var n=a("2d86"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},dfd0:function(e,t,a){"use strict";a("9584")},e0a3:function(e,t,a){},ead4:function(e,t,a){},f44b:function(e,t,a){},f8d1:function(e,t,a){},fa56:function(e,t,a){"use strict";a.r(t);var n=a("9929"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a}}]);