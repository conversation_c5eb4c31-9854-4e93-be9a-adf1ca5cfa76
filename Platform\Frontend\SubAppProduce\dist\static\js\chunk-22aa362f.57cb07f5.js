(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-22aa362f"],{"0187":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteRole=s,t.GetRoleMenusObj=f,t.GetRoleTree=r,t.GetRoleWorkingObjListByUser=c,t.GetUserListByRole=l,t.GetUserRoleTreeWithoutObject=m,t.GetWorkingObjTree=p,t.SaveDepartmentObject=g,t.SaveRole=i,t.SaveRoleMenu=d,t.SaveUserAuthorize=u,t.SaveUserObject=h;var o=a(n("b775"));function r(){return(0,o.default)({url:"/SYS/Role/GetRoleTree",method:"post"})}function i(e){return(0,o.default)({url:"/SYS/Role/SaveRole",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/Role/DeleteRole",method:"post",data:e})}function l(e){return(0,o.default)({url:"/SYS/Role/GetUserListByRole",method:"post",data:e})}function u(e){return(0,o.default)({url:"/SYS/Role/SaveUserAuthorize",method:"post",data:e})}function c(e){return(0,o.default)({url:"/SYS/Role/GetRoleWorkingObjListByUser",method:"post",data:e})}function d(e){return(0,o.default)({url:"/SYS/Role/SaveRoleMenu",method:"post",data:e})}function f(e){return(0,o.default)({url:"/SYS/Role/GetRoleMenusObj",method:"post",data:e})}function m(e){return(0,o.default)({url:"/SYS/User/GetUserRoleTreeWithoutObject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/SYS/User/GetWorkingObjTree",method:"post",data:e})}function h(e){return(0,o.default)({url:"/SYS/User/SaveUserObject",method:"post",data:e})}function g(e){return(0,o.default)({url:"/SYS/User/SaveDepartmentObject",method:"post",data:e})}},"05f1":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("c14f")),r=a(n("1da1")),i=a(n("5530"));n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("e9c4"),n("d3b7"),n("3ca3"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494");var s=a(n("7962")),l=a(n("34e9")),u=a(n("2082")),c=a(n("0f97")),d=a(n("5cc7")),f=n("9643"),m=n("1b69"),p=n("3166"),h=n("f2f6"),g=n("ed08"),v=n("f4f2"),b=n("fd31"),S=a(n("1704")),P=a(n("8057")),I=a(n("a6ec")),y=a(n("68c7")),_=n("2f62"),C={0:"草稿",2:"待过磅",3:"已过磅",4:"未验收",5:"部分验收",6:"已验收",7:"已退货",999:"审批中","-1":"已退回"};t.default={components:{TopHeader:l.default,Monitor:s.default,DynamicDataTable:c.default,PrintDialog:S.default,radioDialog:P.default,checkDialog:y.default,dialogExcel:I.default},filters:{sendDateFilter:function(e){return(0,g.parseTime)(new Date(e))}},mixins:[u.default,d.default],data:function(){return{selectList:[],statusInfo:C,IsVisabel:!1,btnLoading:!1,form:{Code:"",Status:null,ProjectId:"",VehicleNo:"",Consignee:"",IsReturn:null,Is_Weight_Warning:null,dateRange:["",""],PageInfo:{ParameterJson:[],Page:1,PageSize:20}},form2:{ProjectId:"",Area_Id:"",InstallUnit_Id:""},rules:{ProjectId:[{required:!0,message:"请选择",trigger:"change"}]},pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-864e5),e.$emit("pick",[n,t])}},{text:"最近一周",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-6048e5),e.$emit("pick",[n,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-2592e6),e.$emit("pick",[n,t])}}]},selectParams:{clearable:!0,placeholder:"请选择"},ProjectId:"",dialogVisible:!1,pageLoading:!1,addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([n.e("chunk-2a1b0cc1"),n.e("chunk-dbdc95ec")]).then(n.bind(null,"8d41"))},name:"PROShipSentAdd",meta:{title:"新建发货单"}},{path:this.$route.path+"/edit",hidden:!0,component:function(){return Promise.all([n.e("chunk-2a1b0cc1"),n.e("chunk-ef2c1388")]).then(n.bind(null,"4a91"))},name:"PROShipSentEdit",meta:{title:"编辑发货单"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return n.e("chunk-677d4355").then(n.bind(null,"3bba"))},name:"PROShipSentDetail",meta:{title:"详情"}},{path:this.$route.path+"/changeRecord",hidden:!0,component:function(){return n.e("chunk-5ca2d19e").then(n.bind(null,"6038"))},name:"PROShipSentChangeRecord",meta:{title:"发货单变更记录"}}],projects:[],treeParamsArea:{"check-strictly":!0,"expand-on-click-node":!1,"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},styles:{width:"100%"},SetupPositionData:[],queryInfo:{Page:1,PageSize:10,ParameterJson:[]},queryInfo2:{BeginDate:"",EndDate:"",PageInfo:{ParameterJson:[],Page:1,PageSize:2}},tbConfig:{Pager_Align:"center",Op_Width:280},columns:[],tbData:[],total:0,tbLoading:!1,selectRow:{},totalData:{Allsteelamount:0,Allsteelweight:0},ProfessionalType:null,title:"",Is_Integration:!1}},computed:(0,i.default)({comList:function(){return this.Is_Integration?this.columns:this.columns.filter((function(e){return"SumAcceptCount"!==e.Code&&"SumPendingCount"!==e.Code}))}},(0,_.mapGetters)("factoryInfo",["Component_Shipping_Approval","Shipping_Approval_LowerLimit","Shipping_Weigh_Enabled"])),created:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.$store.dispatch("factoryInfo/getWorkshop"),t.n=1,e.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:e.Is_Integration=t.v,e.getFactoryTypeOption(),e.getProjectPageList();case 2:return t.a(2)}}),t)})))()},mounted:function(){},methods:{handleCancelFlow:function(e){var t=this;this.$confirm("是否撤回?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.CancelFlow)({instanceId:e}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},submitForReview:function(e){var t=this,n=e.Id;e.VehicleNo?this.$confirm("是否提交审核?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.SubmitApproval)({Id:n}).then((function(e){e.IsSucceed?(t.fetchData(),t.$message({message:"操作成功",type:"success"})):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})})):this.$message({message:"发货单车辆信息未完善，请完善后提交",type:"warning"})},handleMonitor:function(e){this.$refs["monitor"].opendialog(e,!1)},getAuditStatus:function(e){return this.Component_Shipping_Approval&&1e3*(this.Shipping_Approval_LowerLimit||0)<e.Project_Sending_Weight},getFactoryTypeOption:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,b.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_component_out_bill_list,".concat(e.ProfessionalType[0].Code));case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,i.default)({},this.form);delete t["dateRange"],this.form.dateRange=this.form.dateRange||[],t.BeginDate=(0,g.parseTime)(this.form.dateRange[0])?(0,g.parseTime)(this.form.dateRange[0]):"",t.EndDate=(0,g.parseTime)(this.form.dateRange[1])?(0,g.parseTime)(this.form.dateRange[1]):"",(0,f.GetProjectSendingInfoPagelist)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.SendDate=e.SendDate?(0,g.parseTime)(new Date(e.SendDate),"{y}-{m}-{d}"):e.SendDate,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1})),(0,f.GetProjectSendingAllCount)((0,i.default)({},t)).then((function(t){t.IsSucceed?e.totalData=t.Data:e.$message({message:t.Message,type:"error"})}))},getPageList:function(){this.fetchData()},handSubmit:function(e){var t=this,n=e.Id;e.VehicleNo?this.$confirm("是否提交过磅?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.SubmitWeighingForPC)({Id:n}).then((function(e){e.IsSucceed?(t.fetchData(),t.$message({message:e.Data,type:"success"})):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})})):this.$message({message:"发货单车辆信息未完善，请完善后提交",type:"warning"})},datePickerwrapper:function(){this.form.dateRange||(this.form.dateRange=["",""]),this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},submitForm2:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var n=t.form2,a=n.ProjectId,o=n.Area_Id,r=n.InstallUnit_Id,i=t.projects.find((function(e){return e.Id===t.form2.ProjectId})),s=i.Name,l=i.Id,u=i.Code,c=(i.SPIC_UserName,i.Address),d=i.Receiver,f=i.Receiver_Tel,m=i.Sys_Project_Id,p=i.Receive_UserName,h={ProjectId:a,Area_Id:o,InstallUnit_Id:r,Id:l,Name:s,Code:u,Address:c,Receiver:d,Receiver_Tel:f,Sys_Project_Id:m,Receive_UserName:p,ProfessionalType:t.ProfessionalType};t.$router.push({name:"PROShipSentAdd",query:{pg_redirect:"PROShipSent",p:encodeURIComponent(JSON.stringify(h))}}),t.dialogVisible=!1,t.$refs.form2.resetFields()}))},resetForm2:function(e){this.dialogVisible=!1,this.$refs[e].resetFields()},handleClose:function(){this.$refs.form2.resetFields(),this.dialogVisible=!1},handleAdd:function(){this.dialogVisible=!0},getProjectPageList:function(){var e=this;(0,m.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projects=t.Data.Data)}))},projectIdChange:function(e){},projectIdClear:function(e){this.$refs.form2.resetFields()},getAreaList:function(){var e=this;(0,p.GeAreaTrees)({projectId:this.form2.ProjectId}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},filterFun:function(e,t){this.$refs[t].filterFun(e)},areaChange:function(e){this.getInstall()},areaClear:function(){this.form2.Area_Id="",this.form.InstallUnit_Id=""},getInstall:function(){var e=this;(0,h.GetInstallUnitPageList)({Area_Id:this.form2.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.SetupPositionData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},handleEdit:function(e,t){this.$router.push({name:"PROShipSentEdit",query:{pg_redirect:"PROShipSent",id:e,isSub:t}})},handleWithdraw:function(e){var t=this;this.$confirm("撤回至草稿, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.WithdrawDraft)({id:e}).then((function(e){e.IsSucceed?(t.$message({message:"撤销成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleSub:function(e){var t=this,n=e.Id;e.VehicleNo?this.$confirm("提交该发货单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pageLoading=!0,(0,f.SubmitProjectSending)({Id:n}).then((function(e){e.IsSucceed?(t.$message({message:"发货成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"}),t.pageLoading=!1}))})).catch((function(){t.pageLoading=!1})):this.$message({message:"发货单车辆信息未完善，请完善后提交",type:"warning"})},handleDel:function(e){var t=this;this.$confirm("是否删除该发货单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DeleteProjectSendingInfo)({Id:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleInfo:function(e){this.$router.push({name:"PROShipSentDetail",query:{pg_redirect:"PROShipSent",id:e}})},handleChange:function(e){this.$router.push({name:"PROShipSentChangeRecord",query:{pg_redirect:"PROShipSent",id:e}})},handleExport:function(){var e=this;this.title="导出",this.$nextTick((function(t){e.$refs.radioDialog.handleOpen(e.selectList)}))},handleExportExcel:function(){this.title="导出",this.$refs.dialogExcel.handleOpen(this.selectList)},handlePrint:function(){this.title="打印",this.$refs.radioDialog.handleOpen(this.selectList)},handlePrintNoWeight:function(){var e=this;(0,f.TransformsWithoutWeight)({sendId:this.selectRow.Id}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,v.baseUrl)());window.open(n.href,"_blank"),e.$message({type:"success",message:"打印成功!"})}else e.$message({message:t.Message,type:"error"})}))},handleLeadingOut:function(){var e=this;this.btnLoading=!0;var t=(0,i.default)({},this.form);delete t["dateRange"],delete t["PageInfo"],this.form.dateRange=this.form.dateRange||[],t.BeginDate=(0,g.parseTime)(this.form.dateRange[0])?(0,g.parseTime)(this.form.dateRange[0]):"",t.EndDate=(0,g.parseTime)(this.form.dateRange[1])?(0,g.parseTime)(this.form.dateRange[1]):"",(0,f.ExportInvoiceList)((0,i.default)({},t)).then((function(t){if(t.IsSucceed){e.$message.success("导出成功");var n=new URL(t.Data,(0,v.baseUrl)());window.open(n.href)}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))},handleSelectionChange:function(e){this.selectList=e},selectChange:function(e){var t=e.selection,n=e.row;this.$refs.dyTable.$refs.dtable.clearSelection(),0!=t.length?this.selectRow=n:this.selectRow="",t.length>1&&t.shift(),this.$refs.dyTable.$refs.dtable.toggleRowSelection(n,!!t.length)},handleSelectAll:function(){},handelView:function(e){var t=this;this.IsVisabel=!0,this.$nextTick((function(n){t.$refs.checkDialog.handelOpen(e)}))}}}},"071e":function(e,t,n){"use strict";n.r(t);var a=n("05f1"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"08b2":function(e,t,n){"use strict";n.r(t);var a=n("2979"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"09f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,n,a){return e/=a/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,n){var i=r(),s=e-i,l=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=l;var e=Math.easeInOutQuad(u,i,s,t);o(e),u<t?a(c):n&&"function"===typeof n&&n()};c()}},"0cff":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"验收情况",visible:e.dialogVisible,width:"60%",close:e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("div",{staticClass:"dialog_wapper"},[n("div",{staticClass:"base_Info"},[n("el-form",{ref:"formInline",attrs:{model:e.formInline,"label-width":"120px",inline:""}},[n("el-form-item",{attrs:{label:"项目名称："}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.ProjectName,callback:function(t){e.$set(e.formInline,"ProjectName",t)},expression:"formInline.ProjectName"}})],1),n("el-form-item",{attrs:{label:"发货单号："}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.Code,callback:function(t){e.$set(e.formInline,"Code",t)},expression:"formInline.Code"}})],1),n("el-form-item",{attrs:{label:"发货总数："}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.SteelAmount,callback:function(t){e.$set(e.formInline,"SteelAmount",t)},expression:"formInline.SteelAmount"}})],1),n("el-form-item",{attrs:{label:"发货总量(kg)： "}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.SteelWeight,callback:function(t){e.$set(e.formInline,"SteelWeight",t)},expression:"formInline.SteelWeight"}})],1)],1)],1),n("div",{staticClass:"main_wapper"},[n("div",{staticClass:"search_wapper"},[n("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"构件名称",prop:"SteelName"}},[n("el-input",{attrs:{placeholder:"请输入"},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1),n("el-form-item",{attrs:{label:"所属包号",prop:"PackageSn"}},[n("el-input",{attrs:{placeholder:"请输入"},model:{value:e.form.PackageSn,callback:function(t){e.$set(e.form,"PackageSn",t)},expression:"form.PackageSn"}})],1),n("el-form-item",{attrs:{label:"是否直发件",prop:"IsDirect"}},[n("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.IsDirect,callback:function(t){e.$set(e.form,"IsDirect",t)},expression:"form.IsDirect"}},[n("el-option",{attrs:{label:"是",value:!0}}),n("el-option",{attrs:{label:"否",value:!1}})],1)],1),n("el-form-item",[n("el-form-item",{attrs:{label:"验收状态",prop:"Status"}},[n("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},[n("el-option",{attrs:{label:"待验收",value:0}}),n("el-option",{attrs:{label:"已验收",value:1}}),n("el-option",{attrs:{label:"已退货",value:2}})],1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.searchInfo}},[e._v("搜索")]),n("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.fetchData()}}},[e._v("重置")])],1)],1)],1)],1),n("div",{staticClass:"table_wapper"},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t,a){return n("vxe-column",{key:a,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"","min-width":t.Width,align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["IsDirect"==t.Code?{key:"default",fn:function(a){var o=a.row;return[o[t.Code]?n("el-tag",{attrs:{type:"success"}},[e._v("是")]):n("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}:"Status"==t.Code?{key:"default",fn:function(a){var o=a.row;return[n("span",[e._v(e._s(0===o[t.Code]?"待验收":1===o[t.Code]?"已验收":2===o[t.Code]?"已退货":"-"))])]}}:{key:"default",fn:function(a){var o=a.row;return[0!=o[t.Code]?n("span",[e._v(e._s(o[t.Code]||"-"))]):n("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})})),1)],1),n("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)]),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)])],1)},o=[]},1704:function(e,t,n){"use strict";n.r(t);var a=n("92be"),o=n("31e7");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("9382");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,null,null);t["default"]=s.exports},"1d76":function(e,t,n){"use strict";n.r(t);var a=n("8029"),o=n("8800");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("3af9");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"8927ee52",null);t["default"]=s.exports},"27a0":function(e,t,n){},2979:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("5530")),r=a(n("c14f")),i=a(n("1da1")),s=n("9643"),l=a(n("333d")),u=n("c685"),c=a(n("5f52"));t.default={components:{Pagination:l.default},mixins:[c.default],data:function(){return{tablePageSize:u.tablePageSize,dialogVisible:!1,formInline:{ProjectName:"",Areaposition:"",SetupPosition:"",Code:"",SteelAmount:0,SteelWeight:0},form:{IsDirect:null,Status:null,SteelName:"",PackageSn:"",Id:""},tbData:[],columns:[],tbLoading:!1,queryInfo:{Page:1,PageSize:20},total:0}},mounted:function(){return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{handleClose:function(){this.dialogVisible=!1},handelOpen:function(e){var t=this;return(0,i.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,t.getTableConfig("ProSendCheck");case 1:return t.form.Id=e,t.dialogVisible=!0,n.n=2,t.fetchData();case 2:return n.a(2)}}),n)})))()},searchInfo:function(){this.queryInfo={Page:1,PageSize:20},this.fetchData()},fetchData:function(){var e=this;(0,s.GetProjectAcceptInfoPagelist)((0,o.default)({PageInfo:this.queryInfo},this.form)).then((function(t){if(t.IsSucceed){var n=t.Data,a=n.Areaposition,o=n.Code,r=n.SteelAmount,i=n.SteelWeight,s=n.SetupPosition,l=n.ProjectName,u=n.PageInfo;Object.assign(e.formInline,{Areaposition:a,Code:o,SteelAmount:r,SteelWeight:i,SetupPosition:s,ProjectName:l}),e.tbData=u.Data,e.total=u.TotalCount}else e.$message({message:t.Message,type:"error"})}))},changePage:function(){this.fetchData()}}}},3166:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=c,t.GeAreaTrees=_,t.GetFileSync=k,t.GetInstallUnitIdNameList=y,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=C,t.GetProjectAreaTreeList=I,t.GetProjectEntity=l,t.GetProjectList=s,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=P,t.GetSchedulingPartList=w,t.IsEnableProjectMonomer=d,t.SaveProject=u,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=v,t.UpdateProjectTemplateContract=b,t.UpdateProjectTemplateOther=S;var o=a(n("b775")),r=a(n("4328"));function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function k(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"31c9":function(e,t,n){"use strict";n.r(t);var a=n("49de"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"31e7":function(e,t,n){"use strict";n.r(t);var a=n("620a"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},3428:function(e,t,n){"use strict";n.r(t);var a=n("5505"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"3af9":function(e,t,n){"use strict";n("b03a")},4278:function(e,t,n){"use strict";n.r(t);var a=n("0cff"),o=n("31c9");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("a9c2");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"75559486",null);t["default"]=s.exports},"42e2":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("c14f")),r=a(n("1da1")),i=a(n("5530"));n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("e9c4"),n("b64b"),n("d3b7"),n("3ca3"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494");var s=a(n("7962")),l=a(n("34e9")),u=a(n("2082")),c=a(n("0f97")),d=a(n("5cc7")),f=n("9643"),m=n("1b69"),p=n("3166"),h=n("f2f6"),g=n("ed08"),v=n("f4f2"),b=n("fd31"),S=a(n("bef7")),P=a(n("ca4f")),I=a(n("5610")),y=a(n("4278")),_=n("2f62"),C={0:"草稿",2:"待过磅",3:"已过磅",4:"未验收",5:"部分验收",6:"已验收",7:"已退货",999:"审批中","-1":"已退回"};t.default={components:{TopHeader:l.default,Monitor:s.default,DynamicDataTable:c.default,PrintDialog:S.default,radioDialog:P.default,checkDialog:y.default,dialogExcel:I.default},filters:{sendDateFilter:function(e){return(0,g.parseTime)(new Date(e))}},mixins:[u.default,d.default],data:function(){return{selectList:[],statusInfo:C,IsVisabel:!1,btnLoading:!1,form:{Code:"",Status:null,ProjectId:"",VehicleNo:"",Consignee:"",IsReturn:null,Is_Weight_Warning:null,dateRange:["",""],PageInfo:{ParameterJson:[],Page:1,PageSize:20}},form2:{ProjectId:"",Area_Id:"",InstallUnit_Id:""},rules:{ProjectId:[{required:!0,message:"请选择",trigger:"change"}]},pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-864e5),e.$emit("pick",[n,t])}},{text:"最近一周",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-6048e5),e.$emit("pick",[n,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-2592e6),e.$emit("pick",[n,t])}}]},selectParams:{clearable:!0,placeholder:"请选择"},ProjectId:"",dialogVisible:!1,pageLoading:!1,addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([n.e("chunk-2d0e2790"),n.e("chunk-65738496"),n.e("chunk-7720064d")]).then(n.bind(null,"d634"))},name:"PROShipSentAdd",meta:{title:"新建发货单"}},{path:this.$route.path+"/edit",hidden:!0,component:function(){return Promise.all([n.e("chunk-2d0e2790"),n.e("chunk-65738496"),n.e("chunk-79692042")]).then(n.bind(null,"733c"))},name:"PROShipSentEdit",meta:{title:"编辑发货单"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return n.e("chunk-a53a61a6").then(n.bind(null,"18a7"))},name:"PROShipSentDetail",meta:{title:"详情"}},{path:this.$route.path+"/changeRecord",hidden:!0,component:function(){return n.e("chunk-139a0a5c").then(n.bind(null,"d76f"))},name:"PROShipSentChangeRecord",meta:{title:"发货单变更记录"}}],projects:[],treeParamsArea:{"check-strictly":!0,"expand-on-click-node":!1,"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},styles:{width:"100%"},SetupPositionData:[],queryInfo:{Page:1,PageSize:10,ParameterJson:[]},queryInfo2:{BeginDate:"",EndDate:"",PageInfo:{ParameterJson:[],Page:1,PageSize:2}},tbConfig:{Pager_Align:"center",Op_Width:280},columns:[],tbData:[],total:0,tbLoading:!1,selectRow:{},totalData:{Allsteelamount:0,Allsteelweight:0},ProfessionalType:null,title:"",Is_Integration:!1}},computed:(0,i.default)({selectEmpty:function(){return 0===Object.keys(this.selectRow).length},comList:function(){return this.Is_Integration?this.columns:this.columns.filter((function(e){return"SumAcceptCount"!==e.Code&&"SumPendingCount"!==e.Code}))}},(0,_.mapGetters)("factoryInfo",["Component_Shipping_Approval","autoGenerate","Shipping_Approval_LowerLimit","Shipping_Weigh_Enabled"])),activated:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.$route.query.refresh&&e.fetchData();case 1:return t.a(2)}}),t)})))()},created:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.$store.dispatch("factoryInfo/getWorkshop"),t.n=1,e.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:e.Is_Integration=t.v,e.getFactoryTypeOption(),e.getProjectPageList();case 2:return t.a(2)}}),t)})))()},mounted:function(){},methods:{handleCancelFlow:function(e){var t=this;this.$confirm("是否撤回?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.CancelFlow)({instanceId:e}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},submitForReview:function(e){var t=this,n=e.Id;e.VehicleNo?this.$confirm("是否提交审核?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.SubmitApproval)({Id:n}).then((function(e){e.IsSucceed?(t.fetchData(),t.$message({message:"操作成功",type:"success"})):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})})):this.$message({message:"发货单车辆信息未完善，请完善后提交",type:"warning"})},handleMonitor:function(e){this.$refs["monitor"].opendialog(e,!1)},getAuditStatus:function(e){return this.Component_Shipping_Approval&&1e3*(this.Shipping_Approval_LowerLimit||0)<e.Project_Sending_Weight},getFactoryTypeOption:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,b.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_component_out_bill_list,".concat(e.ProfessionalType[0].Code));case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,i.default)({},this.form);delete t["dateRange"],this.form.dateRange=this.form.dateRange||[],t.BeginDate=(0,g.parseTime)(this.form.dateRange[0])?(0,g.parseTime)(this.form.dateRange[0]):"",t.EndDate=(0,g.parseTime)(this.form.dateRange[1])?(0,g.parseTime)(this.form.dateRange[1]):"",(0,f.GetProjectSendingInfoPagelist)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.SendDate=e.SendDate?(0,g.parseTime)(new Date(e.SendDate),"{y}-{m}-{d}"):e.SendDate,e})),e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1})),(0,f.GetProjectSendingAllCount)((0,i.default)({},t)).then((function(t){t.IsSucceed?e.totalData=t.Data:e.$message({message:t.Message,type:"error"})}))},getPageList:function(){this.fetchData()},handSubmit:function(e){var t=this,n=e.Id;e.VehicleNo?this.$confirm("是否提交过磅?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.SubmitWeighingForPC)({Id:n}).then((function(e){e.IsSucceed?(t.fetchData(),t.$message({message:e.Data,type:"success"})):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})})):this.$message({message:"发货单车辆信息未完善，请完善后提交",type:"warning"})},datePickerwrapper:function(){this.form.dateRange||(this.form.dateRange=["",""]),this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},submitForm2:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var n=t.form2,a=n.ProjectId,o=n.Area_Id,r=n.InstallUnit_Id,i=t.projects.find((function(e){return e.Id===t.form2.ProjectId})),s=i.Name,l=i.Id,u=i.Code,c=(i.SPIC_UserName,i.Address),d=i.Receiver,f=i.Receiver_Tel,m=i.Sys_Project_Id,p=i.Receive_UserName,h={ProjectId:a,Area_Id:o,InstallUnit_Id:r,Id:l,Name:s,Code:u,Address:c,Receiver:d,Receiver_Tel:f,Sys_Project_Id:m,Receive_UserName:p,autoGenerate:t.autoGenerate,ProfessionalType:t.ProfessionalType};t.$router.push({name:"PROShipSentAdd",query:{pg_redirect:"PROShipSent",p:encodeURIComponent(JSON.stringify(h))}}),t.dialogVisible=!1,t.$refs.form2.resetFields()}))},resetForm2:function(e){this.dialogVisible=!1,this.$refs[e].resetFields()},handleClose:function(){this.$refs.form2.resetFields(),this.dialogVisible=!1},handleAdd:function(){this.dialogVisible=!0},getProjectPageList:function(){var e=this;(0,m.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projects=t.Data.Data)}))},projectIdChange:function(e){},projectIdClear:function(e){this.$refs.form2.resetFields()},getAreaList:function(){var e=this;(0,p.GeAreaTrees)({projectId:this.form2.ProjectId}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},filterFun:function(e,t){this.$refs[t].filterFun(e)},areaChange:function(e){this.getInstall()},areaClear:function(){this.form2.Area_Id="",this.form.InstallUnit_Id=""},getInstall:function(){var e=this;(0,h.GetInstallUnitPageList)({Area_Id:this.form2.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.SetupPositionData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},handleEdit:function(e,t){this.$router.push({name:"PROShipSentEdit",query:{pg_redirect:"PROShipSent",id:e,isSub:t?"1":"0",p:encodeURIComponent(JSON.stringify({autoGenerate:this.autoGenerate}))}})},handleWithdraw:function(e){var t=this;this.$confirm("撤回至草稿, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.WithdrawDraft)({id:e}).then((function(e){e.IsSucceed?(t.$message({message:"撤销成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){}))},handleSub:function(e){var t=this,n=e.Id;e.VehicleNo?this.$confirm("提交该发货单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pageLoading=!0,(0,f.SubmitProjectSending)({Id:n}).then((function(e){e.IsSucceed?(t.$message({message:"发货成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"}),t.pageLoading=!1}))})).catch((function(){t.pageLoading=!1})):this.$message({message:"发货单车辆信息未完善，请完善后提交",type:"warning"})},handleDel:function(e){var t=this;this.$confirm("是否删除该发货单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DeleteProjectSendingInfo)({Id:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleInfo:function(e){this.$router.push({name:"PROShipSentDetail",query:{pg_redirect:"PROShipSent",id:e}})},handleChange:function(e){this.$router.push({name:"PROShipSentChangeRecord",query:{pg_redirect:"PROShipSent",id:e}})},handleExport:function(){var e=this;this.title="导出",this.$nextTick((function(t){e.$refs.radioDialog.handleOpen(e.selectList)}))},handleExportExcel:function(){this.title="导出",this.$refs.dialogExcel.handleOpen(this.selectList)},handlePrint:function(){this.title="打印",this.$refs.radioDialog.handleOpen(this.selectList)},handlePrintNoWeight:function(){var e=this;(0,f.TransformsWithoutWeight)({sendId:this.selectRow.Id}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,v.baseUrl)());window.open(n.href,"_blank"),e.$message({type:"success",message:"打印成功!"})}else e.$message({message:t.Message,type:"error"})}))},handleLeadingOut:function(){var e=this;this.btnLoading=!0;var t=(0,i.default)({},this.form);delete t["dateRange"],delete t["PageInfo"],this.form.dateRange=this.form.dateRange||[],t.BeginDate=(0,g.parseTime)(this.form.dateRange[0])?(0,g.parseTime)(this.form.dateRange[0]):"",t.EndDate=(0,g.parseTime)(this.form.dateRange[1])?(0,g.parseTime)(this.form.dateRange[1]):"",(0,f.ExportInvoiceList)((0,i.default)({},t)).then((function(t){if(t.IsSucceed){e.$message.success("导出成功");var n=new URL(t.Data,(0,v.baseUrl)());window.open(n.href)}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))},handleSelectionChange:function(e){this.selectList=e},selectChange:function(e){var t=e.selection,n=e.row;this.$refs.dyTable.$refs.dtable.clearSelection(),0!=t.length?this.selectRow=n:this.selectRow={},t.length>1&&t.shift(),this.$refs.dyTable.$refs.dtable.toggleRowSelection(n,!!t.length)},handleSelectAll:function(){},handelView:function(e){var t=this;this.IsVisabel=!0,this.$nextTick((function(n){t.$refs.checkDialog.handelOpen(e)}))}}}},"49de":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("5530")),r=a(n("c14f")),i=a(n("1da1")),s=n("9643"),l=a(n("333d")),u=n("c685"),c=a(n("5f52"));t.default={components:{Pagination:l.default},mixins:[c.default],data:function(){return{tablePageSize:u.tablePageSize,dialogVisible:!1,formInline:{ProjectName:"",Areaposition:"",SetupPosition:"",Code:"",SteelAmount:0,SteelWeight:0},form:{IsDirect:null,Status:null,SteelName:"",PackageSn:"",Id:""},tbData:[],columns:[],tbLoading:!1,queryInfo:{Page:1,PageSize:20},total:0}},mounted:function(){return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{handleClose:function(){this.dialogVisible=!1},handelOpen:function(e){var t=this;return(0,i.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,t.getTableConfig("ProSendCheck");case 1:return t.form.Id=e,t.dialogVisible=!0,n.n=2,t.fetchData();case 2:return n.a(2)}}),n)})))()},searchInfo:function(){this.queryInfo={Page:1,PageSize:20},this.fetchData()},fetchData:function(){var e=this;(0,s.GetProjectAcceptInfoPagelist)((0,o.default)({PageInfo:this.queryInfo},this.form)).then((function(t){if(t.IsSucceed){var n=t.Data,a=n.Areaposition,o=n.Code,r=n.SteelAmount,i=n.SteelWeight,s=n.SetupPosition,l=n.ProjectName,u=n.PageInfo;Object.assign(e.formInline,{Areaposition:a,Code:o,SteelAmount:r,SteelWeight:i,SetupPosition:s,ProjectName:l}),e.tbData=u.Data,e.total=u.TotalCount}else e.$message({message:t.Message,type:"error"})}))},changePage:function(){this.fetchData()}}}},"4e82":function(e,t,n){"use strict";var a=n("23e7"),o=n("e330"),r=n("59ed"),i=n("7b0b"),s=n("07fa"),l=n("083a"),u=n("577e"),c=n("d039"),d=n("addb"),f=n("a640"),m=n("3f7e"),p=n("99f4"),h=n("1212"),g=n("ea83"),v=[],b=o(v.sort),S=o(v.push),P=c((function(){v.sort(void 0)})),I=c((function(){v.sort(null)})),y=f("sort"),_=!c((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var e,t,n,a,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)v.push({k:t+a,v:n})}for(v.sort((function(e,t){return t.v-e.v})),a=0;a<v.length;a++)t=v[a].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),C=P||!I||!y||!_,w=function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:u(t)>u(n)?1:-1}};a({target:"Array",proto:!0,forced:C},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(_)return void 0===e?b(t):b(t,e);var n,a,o=[],u=s(t);for(a=0;a<u;a++)a in t&&S(o,t[a]);d(o,w(e)),n=s(o),a=0;while(a<n)t[a]=o[a++];while(a<u)l(t,a++);return t}})},5250:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"验收情况",visible:e.dialogVisible,width:"60%",close:e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("div",{staticClass:"dialog_wapper"},[n("div",{staticClass:"base_Info"},[n("el-form",{ref:"formInline",attrs:{model:e.formInline,"label-width":"120px",inline:""}},[n("el-form-item",{attrs:{label:"项目名称："}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.ProjectName,callback:function(t){e.$set(e.formInline,"ProjectName",t)},expression:"formInline.ProjectName"}})],1),n("el-form-item",{attrs:{label:"发货单号："}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.Code,callback:function(t){e.$set(e.formInline,"Code",t)},expression:"formInline.Code"}})],1),n("el-form-item",{attrs:{label:"发货总数："}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.SteelAmount,callback:function(t){e.$set(e.formInline,"SteelAmount",t)},expression:"formInline.SteelAmount"}})],1),n("el-form-item",{attrs:{label:"发货总量(kg)： "}},[n("el-input",{attrs:{disabled:""},model:{value:e.formInline.SteelWeight,callback:function(t){e.$set(e.formInline,"SteelWeight",t)},expression:"formInline.SteelWeight"}})],1)],1)],1),n("div",{staticClass:"main_wapper"},[n("div",{staticClass:"search_wapper"},[n("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"构件名称",prop:"SteelName"}},[n("el-input",{attrs:{placeholder:"请输入"},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1),n("el-form-item",{attrs:{label:"所属包号",prop:"PackageSn"}},[n("el-input",{attrs:{placeholder:"请输入"},model:{value:e.form.PackageSn,callback:function(t){e.$set(e.form,"PackageSn",t)},expression:"form.PackageSn"}})],1),n("el-form-item",{attrs:{label:"是否直发件",prop:"IsDirect"}},[n("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.IsDirect,callback:function(t){e.$set(e.form,"IsDirect",t)},expression:"form.IsDirect"}},[n("el-option",{attrs:{label:"是",value:!0}}),n("el-option",{attrs:{label:"否",value:!1}})],1)],1),n("el-form-item",[n("el-form-item",{attrs:{label:"验收状态",prop:"Status"}},[n("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},[n("el-option",{attrs:{label:"待验收",value:0}}),n("el-option",{attrs:{label:"已验收",value:1}}),n("el-option",{attrs:{label:"已退货",value:2}})],1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.searchInfo}},[e._v("搜索")]),n("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.fetchData()}}},[e._v("重置")])],1)],1)],1)],1),n("div",{staticClass:"table_wapper"},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t,a){return n("vxe-column",{key:a,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"","min-width":t.Width,align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["IsDirect"==t.Code?{key:"default",fn:function(a){var o=a.row;return[n("span",[e._v(e._s(o[t.Code]?"是":"否"))])]}}:"Status"==t.Code?{key:"default",fn:function(a){var o=a.row;return[n("span",[e._v(e._s(0===o[t.Code]?"待验收":1===o[t.Code]?"已验收":2===o[t.Code]?"已退货":"-"))])]}}:{key:"default",fn:function(a){var o=a.row;return[0!=o[t.Code]?n("span",[e._v(e._s(o[t.Code]||"-"))]):n("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})})),1)],1),n("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)]),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)])],1)},o=[]},5505:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d3b7"),n("3ca3"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494");var a=n("f4f2"),o=n("9643");t.default={components:{},props:{title:{type:String,default:"预览"},sendId:{type:String,default:""}},data:function(){return{dialogVisible:!1,preview_type:"1"}},created:function(){},mounted:function(){},methods:{handleOpen:function(){this.dialogVisible=!0},submit:function(){"预览"==this.title?this.handlePrint():this.handleExport()},handleClose:function(){this.preview_type="1",this.dialogVisible=!1},handleExport:function(){var e=this;(0,o.ExportSendSteel)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,a.baseUrl)());window.open(n.href,"_blank"),e.$message({type:"success",message:"导出成功!"}),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))},handlePrint:function(){var e=this;(0,o.TransformsByType)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,a.baseUrl)());window.open(n.href,"_blank"),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))}}}},5610:function(e,t,n){"use strict";n.r(t);var a=n("f039"),o=n("da6d");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"c2f2f496",null);t["default"]=s.exports},"56a9":function(e,t,n){"use strict";n.r(t);var a=n("a6d2"),o=n("071e");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("67dd");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"4cd63f0b",null);t["default"]=s.exports},5909:function(e,t,n){},"5cc7":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("4de4"),n("d81d"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,o.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(n){n.IsSucceed?(t.ProfessionalType=n.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:n.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(n){(0,a.GetGridByCode)({code:e}).then((function(e){var a=e.IsSucceed,o=e.Data,r=e.Message;if(a){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,o.Grid),t.columns=(o.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+o.Grid.Row_Number:t.form.PageSize=+o.Grid.Row_Number,n(t.columns)}else t.$message({message:r,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},"5ea4":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("7d54"),n("ab43"),n("b680"),n("b64b"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494");var o,r=a(n("5530")),i=a(n("c14f")),s=a(n("1da1")),l=n("f4f2"),u=n("9643"),c=n("7e18"),d=n("0f64");t.default={components:{},props:{title:{type:String,default:"打印"},sendId:{type:String,default:""},sendData:{type:Object,default:function(){return{}}}},data:function(){return{btnLoading:!1,dialogVisible:!1,preview_type:"1",templateList:[],mode:1,templateId:""}},created:function(){this.getTemplateList()},mounted:function(){},methods:{getTemplateList:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetPrintTemplateList)({type:e.mode});case 1:n=t.v,n.IsSucceed&&(e.templateList=n.Data,n.Data&&n.Data[0]?e.templateId=n.Data[0].Id:e.$message.error("您还未配置发货单打印模板，请先配置模板"));case 2:return t.a(2)}}),t)})))()},handleOpen:function(e){this.selectList=e,this.dialogVisible=!0},submit:function(){var e=this;this.btnLoading=!0;var t=(0,c.GetSendingBillData)({ids:this.selectList.map((function(e){return e.Id}))}),n=(0,c.GetPrintTemplateEntity)({id:this.templateId});Promise.all([t,n]).then((function(t){var n=t.filter((function(e){return!e.IsSucceed})).map((function(e){return e.Message}));if(n.length>0)return e.$message({message:n.join("; "),type:"error"}),void(e.btnLoading=!1);var a=t[0].Data,i=JSON.parse(t[1].Data.Data);o=new d.hiprint.PrintTemplate({template:i});var s=[];if(a.forEach((function(t,n){var a=0,o=0;t.Items=t.Items.map((function(t){return t.IsPackage=t.SteelSpec===t.SteelLength,"2"==e.preview_type?(t.SteelWeight=t.GrossWeight,t.SteelAllWeight=t.GrossAllWeight):(a+=t.SteelAmount,o+=t.SteelAllWeight),t})),t.Items.push({SteelName:"合计",SteelAmount:a,SteelAllWeight:o.toFixed(2)}),t.Items.push({SteelName:"备注",IsRemark:!0,SteelSpec:t.Main.Remarks}),s.push((0,r.default)((0,r.default)({},t.Main),{},{Table:t.Items}))})),"打印"==e.title)o.print(s);else{var l="";1===e.selectList.length&&(l=e.selectList[0].Code),o.toPdf(s,"发货单"+l)}e.btnLoading=!1})).catch((function(t){e.btnLoading=!1}))},handleClose:function(){this.preview_type="1",this.dialogVisible=!1},handleExport:function(){var e=this;(0,u.ExportSendSteel)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,l.baseUrl)());window.open(n.href,"_blank"),e.$message({type:"success",message:"导出成功!"}),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))},handlePrint:function(){var e=this;(0,u.TransformsByType)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,l.baseUrl)());window.open(n.href,"_blank"),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))}}}},"5f52":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("dca8"),n("d3b7");var o=a(n("c14f")),r=a(n("1da1")),i=n("6186"),s=n("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,r.default)((0,o.default)().m((function n(){return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,t.getTypeList();case 1:return n.n=2,t.getTable(e);case 2:return n.a(2)}}),n)})))()},getTypeList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var n=Object.freeze(t.Data);if(n.length>0){var a,o=null===(a=n[0])||void 0===a?void 0:a.Id;e.Code=n.find((function(e){return e.Id===o})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(n){(0,i.GetGridByCode)({code:e+","+t.Code}).then((function(e){var a=e.IsSucceed,o=e.Data,r=e.Message;if(a){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,o.Grid),t.columns=(o.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),o.Grid.Is_Page&&(t.queryInfo.PageSize=+o.Grid.Row_Number),n(t.columns)}else t.$message({message:r,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===t){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"620a":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7"),n("3ca3"),n("ddb0");var o=a(n("5530")),r=n("6186");t.default={data:function(){return{dialogVisible:!1,keyValue:"",loading:!0}},methods:{open:function(e){this.dialogVisible=!0,this.keyValue=e},printFrame:function(){var e;null===(e=this.$refs.frame.contentDocument.getElementById("btn"))||void 0===e||e.click()},frameLoaded:function(){var e=this,t=[],n={},a=0,i=0,s=(0,r.GetReportDataTable)({templateName:"成品出库实发单钢构打印",keyValue:this.keyValue,type:"main"}).then((function(r){if(r.IsSucceed){var s,l=null!==(s=r.Data)&&void 0!==s?s:[];if(0!==l.length){t=r.Data;var u=(0,o.default)({},r.Data[0]),c=u.address,d=u.receiver,f=u.project_name,m=u.stock_out_id,p=u.out_date,h=u.driver_name,g=u.out_username,v=u.driver_tel,b=u.out_remark,S=u.car_no,P=u.reamrk,I=u.receiver_tel,y=u.number;t.map((function(e,t){a=t+1,i+=e.component_netweight})),n={address:c,receiver:d,project_name:f,stock_out_id:m,out_date:p,driver_name:h,out_username:g,driver_tel:v,out_remark:b,car_no:S,reamrk:P,sumNum:a,sumWeight:i,receiver_tel:I,number:y}}}else e.$message.error(r.Message),setTimeout((function(){e.dialogVisible=!1}),1e3)}));Promise.all([s]).then((function(){setTimeout((function(){var a,o;e.loading=!1,(null===(a=e.$refs.frame)||void 0===a||null===(a=a.contentWindow)||void 0===a?void 0:a.getPrintData)&&(null===(o=e.$refs.frame)||void 0===o||null===(o=o.contentWindow)||void 0===o||o.getPrintData({tmpl:"StockOut",data:{tableData:t,otherInfo:n,entities:[]}}))}),500)}))}}}},"64d0":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{title:"打印条码阅览",visible:e.dialogVisible,width:"100%","append-to-body":!0,fullscreen:!0,"custom-class":"print-preview-dialog",center:!0},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("template",{slot:"title"},[n("span",{staticStyle:{"font-size":"1.6em",color:"#666"}},[e._v("打印条码阅览")])]),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{height:"100%",width:"100%"}},[n("iframe",{ref:"frame",attrs:{src:"/SubAppProduce/static#/singles/print-preview",width:"100%",height:"100%",frameborder:"0"},on:{load:e.frameLoaded}})]),n("template",{slot:"footer"},[n("div",{staticClass:"print-tip"},[n("div",{staticClass:"tips"},[n("strong",[e._v("注意")]),e._v("：打印之前，请进行扫码确认二维码信息准确无误 ")]),n("el-button",{on:{click:e.printFrame}},[e._v("打印")])],1)])],2)},o=[]},"67dd":function(e,t,n){"use strict";n("ee63")},"68c7":function(e,t,n){"use strict";n.r(t);var a=n("5250"),o=n("08b2");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("7365");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"031e9e78",null);t["default"]=s.exports},7365:function(e,t,n){"use strict";n("5909")},"7e18":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeletePrintTemplate=i,t.GetPrintTemplateEntity=s,t.GetPrintTemplateList=l,t.GetSendingBillData=u,t.SavePrintTemplateEntity=r;var o=a(n("b775"));function r(e){return(0,o.default)({url:"/SYS/printtemplate/SavePrintTemplateEntity",method:"post",data:e})}function i(e){return(0,o.default)({url:"/SYS/printtemplate/DeletePrintTemplate",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/printtemplate/GetPrintTemplateEntity",method:"post",data:e})}function l(e){return(0,o.default)({url:"/SYS/printtemplate/GetPrintTemplateList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetSendingBillData",method:"post",data:e})}},8029:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{display:"flex","flex-direction":"column"}},[n("div",{staticClass:"cs-z-page-main-content",staticStyle:{height:"auto","margin-bottom":"16px"}},[n("top-header",{staticStyle:{height:"100px","line-height":"normal"},scopedSlots:e._u([{key:"left",fn:function(){return[n("el-form",{ref:"searchForm",staticClass:"demo-form-inline form-search",staticStyle:{height:"100px"},attrs:{inline:!0,model:e.form}},[n("el-form-item",{attrs:{label:"发货单号：",prop:"Code"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"发货单状态：",prop:"Status"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},e._l(e.statusInfo,(function(e,t){return n("el-option",{key:t,attrs:{label:e,value:t}})})),1)],1),n("el-form-item",{attrs:{label:"项目名称",prop:"ProjectId"}},[n("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.projects,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),n("el-form-item",{attrs:{label:"收货人：",prop:"Consignee"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Consignee,callback:function(t){e.$set(e.form,"Consignee",t)},expression:"form.Consignee"}})],1),n("el-form-item",{attrs:{label:"车牌号：",prop:"VehicleNo"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.VehicleNo,callback:function(t){e.$set(e.form,"VehicleNo",t)},expression:"form.VehicleNo"}})],1),n("el-form-item",{attrs:{label:"是否退货",prop:"IsReturn"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.IsReturn,callback:function(t){e.$set(e.form,"IsReturn",t)},expression:"form.IsReturn"}},[n("el-option",{attrs:{label:"无退货",value:!1}}),n("el-option",{attrs:{label:"有退货",value:!0}})],1)],1),n("el-form-item",{attrs:{label:"过磅预警",prop:"Is_Weight_Warning"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Weight_Warning,callback:function(t){e.$set(e.form,"Is_Weight_Warning",t)},expression:"form.Is_Weight_Warning"}},[n("el-option",{attrs:{label:"是",value:!0}}),n("el-option",{attrs:{label:"否",value:!1}})],1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(){e.form.PageInfo.Page=1,e.getPageList()}}},[e._v("查询")]),n("el-button",{on:{click:function(t){return e.resetForm("searchForm")}}},[e._v("重置")])],1)],1)]},proxy:!0}])})],1),n("div",{staticClass:"cs-z-page-main-content",staticStyle:{flex:"1",display:"-webkit-box"}},[n("div",{staticStyle:{color:"rgba(34, 40, 52, 0.65)",padding:"10px 0px"}},[n("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增发货单")]),n("el-button",{attrs:{type:"success",disabled:!e.selectList.length},on:{click:e.handleExport}},[e._v("导出发货单(pdf)")]),n("el-button",{attrs:{type:"success",disabled:e.selectEmpty},on:{click:e.handleExportExcel}},[e._v("导出发货单(excel)")]),n("el-button",{attrs:{type:"success",disabled:!e.selectList.length},on:{click:e.handlePrint}},[e._v("打印")]),n("el-button",{attrs:{type:"success",loading:e.btnLoading,disabled:0===e.tbData.length},on:{click:e.handleLeadingOut}},[e._v("导出")]),n("div",{staticClass:"date-picker-wrapper"},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange,callback:function(t){e.$set(e.form,"dateRange",t)},expression:"form.dateRange"}})],1),e.ProfessionalType?n("div",{staticClass:"total-wrapper"},[n("span",{staticStyle:{margin:"0 24px 0 12px"}},[e._v("总数："+e._s(e.totalData.Allsteelamount))]),n("span",[e._v("总重："+e._s(e.totalData.Allsteelweight)+"（"+e._s(e.ProfessionalType[0].Unit)+"）")])]):e._e()],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper",staticStyle:{flex:"1 1 auto"}},[n("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.comList,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,select:e.selectChange,multiSelectedChange:e.handleSelectionChange,selectAll:e.handleSelectAll},scopedSlots:e._u([{key:"SumNetWeight",fn:function(t){var n=t.row;return[e._v(" "+e._s(e._f("displayValue")(n.SumNetWeight))+" ")]}},{key:"Status",fn:function(t){var n=t.row;return[e._v(" "+e._s(e.statusInfo[n.Status])+" ")]}},{key:"Number",fn:function(t){var a=t.row;return[n("div",[e._v(e._s(a.Number||"-"))])]}},{key:"SumReturnCount",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(null!=a.SumReturnCount?a.SumReturnCount:"-"))])]}},{key:"SumAcceptCount",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(null!=a.SumAcceptCount?a.SumAcceptCount:"-"))])]}},{key:"SumPendingCount",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(null!=a.SumPendingCount?a.SumPendingCount:"-"))])]}},{key:"SendDate",fn:function(t){var a=t.row;return[n("div",[e._v(" "+e._s(a.SendDate||"—")+" ")])]}},{key:"op",fn:function(t){var a=t.row,o=t.index;return["0"==a.Status?[n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleEdit(a.Id,!1)}}},[e._v("编辑")]),e.Shipping_Weigh_Enabled?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handSubmit(a)}}},[e._v("提交过磅")]):[e.getAuditStatus(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.submitForReview(a)}}},[e._v("提交审核")]):n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleSub(a)}}},[e._v("提交发货")])],n("el-button",{staticStyle:{color:"red"},attrs:{index:o,type:"text"},on:{click:function(t){return e.handleDel(a.Id)}}},[e._v("删除")])]:["2"==a.Status?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleWithdraw(a.Id)}}},[e._v("撤回草稿")]):e._e(),"999"!=a.Status?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleEdit(a.Id,"-1"!=a.Status)}}},[e._v("编辑")]):e._e(),n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleInfo(a.Id)}}},[e._v("查看")]),[4,5,6].includes(+a.Status)?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleChange(a.Id)}}},[e._v("变更记录")]):e._e(),e.Is_Integration&&[4,5,6].includes(+a.Status)?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handelView(a.Id)}}},[e._v("验收情况")]):e._e()],999==a.Status?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleCancelFlow(a.FlowId)}}},[e._v("撤回")]):e._e(),3==a.Status?[n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleWithdraw(a.Id)}}},[e._v("撤回草稿")]),e.getAuditStatus(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.submitForReview(a)}}},[e._v("提交审核")]):n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleSub(a)}}},[e._v("提交发货")])]:e._e(),a.FlowId?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleMonitor(a.FlowId)}}},[e._v("监控")]):e._e(),-1==a.Status?[e.Shipping_Weigh_Enabled?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handSubmit(a)}}},[e._v("提交过磅")]):[e.getAuditStatus(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.submitForReview(a)}}},[e._v("提交审核")]):n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleSub(a)}}},[e._v("提交发货")])]]:e._e()]}}])})],1),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增发货单",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n("el-form",{ref:"form2",staticClass:"demo-ruleForm",attrs:{model:e.form2,rules:e.rules,"label-width":"70px"}},[n("el-form-item",{attrs:{label:"项目",prop:"ProjectId"}},[n("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.projectIdChange,clear:e.projectIdClear},model:{value:e.form2.ProjectId,callback:function(t){e.$set(e.form2,"ProjectId",t)},expression:"form2.ProjectId"}},e._l(e.projects,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.resetForm2("form2")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm2("form2")}}},[e._v("确 定")])],1)],1)],1)],1),n("PrintDialog",{ref:"PrintDialog"}),n("radioDialog",{ref:"radioDialog",attrs:{"send-id":e.selectRow.Id,title:e.title,"send-data":e.selectRow}}),n("dialogExcel",{ref:"dialogExcel",attrs:{"send-id":e.selectRow.Id,title:e.title,"send-data":e.selectRow}}),n("checkDialog",{ref:"checkDialog"}),n("Monitor",{ref:"monitor"})],1)},o=[]},8057:function(e,t,n){"use strict";n.r(t);var a=n("95cd"),o=n("dfbe");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"1ed920fb",null);t["default"]=s.exports},"80daf":function(e,t,n){"use strict";n.r(t);var a=n("e1c4"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},8638:function(e,t,n){},8800:function(e,t,n){"use strict";n.r(t);var a=n("42e2"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},"8c2c":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:e.title+"重量选择",visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("span",[e._v("重量选项：")]),n("el-radio-group",{model:{value:e.preview_type,callback:function(t){e.preview_type=t},expression:"preview_type"}},[n("el-radio",{attrs:{label:"1"}},[e._v("净重")]),n("el-radio",{attrs:{label:"2"}},[e._v("毛重")]),n("el-radio",{attrs:{label:"3"}},[e._v("无重量")])],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},o=[]},"92be":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{title:"打印条码阅览",visible:e.dialogVisible,width:"100%","append-to-body":!0,fullscreen:!0,"custom-class":"print-preview-dialog",center:!0},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("template",{slot:"title"},[n("span",{staticStyle:{"font-size":"1.6em",color:"#666"}},[e._v("打印条码阅览")])]),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{height:"100%",width:"100%"}},[n("iframe",{ref:"frame",attrs:{src:"/SubAppProduce/static#/singles/print-preview",width:"100%",height:"100%",frameborder:"0"},on:{load:e.frameLoaded}})]),n("template",{slot:"footer"},[n("div",{staticClass:"print-tip"},[n("div",{staticClass:"tips"},[n("strong",[e._v("注意")]),e._v("：打印之前，请进行扫码确认二维码信息准确无误 ")]),n("el-button",{on:{click:e.printFrame}},[e._v("打印")])],1)])],2)},o=[]},9382:function(e,t,n){"use strict";n("8638")},9400:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7"),n("3ca3"),n("ddb0");var o=a(n("5530")),r=n("6186");t.default={data:function(){return{dialogVisible:!1,keyValue:"",loading:!0}},methods:{open:function(e){this.dialogVisible=!0,this.keyValue=e},printFrame:function(){var e;null===(e=this.$refs.frame.contentDocument.getElementById("btn"))||void 0===e||e.click()},frameLoaded:function(){var e=this,t=[],n={},a=0,i=0,s=(0,r.GetReportDataTable)({templateName:"成品出库实发单钢构打印",keyValue:this.keyValue,type:"main"}).then((function(r){if(r.IsSucceed){var s,l=null!==(s=r.Data)&&void 0!==s?s:[];if(0!==l.length){t=r.Data;var u=(0,o.default)({},r.Data[0]),c=u.address,d=u.receiver,f=u.project_name,m=u.stock_out_id,p=u.out_date,h=u.driver_name,g=u.out_username,v=u.driver_tel,b=u.out_remark,S=u.car_no,P=u.reamrk,I=u.receiver_tel,y=u.number;t.map((function(e,t){a=t+1,i+=e.component_netweight})),n={address:c,receiver:d,project_name:f,stock_out_id:m,out_date:p,driver_name:h,out_username:g,driver_tel:v,out_remark:b,car_no:S,reamrk:P,sumNum:a,sumWeight:i,receiver_tel:I,number:y}}}else e.$message.error(r.Message),setTimeout((function(){e.dialogVisible=!1}),1e3)}));Promise.all([s]).then((function(){setTimeout((function(){var a,o;e.loading=!1,(null===(a=e.$refs.frame)||void 0===a||null===(a=a.contentWindow)||void 0===a?void 0:a.getPrintData)&&(null===(o=e.$refs.frame)||void 0===o||null===(o=o.contentWindow)||void 0===o||o.getPrintData({tmpl:"StockOut",data:{tableData:t,otherInfo:n,entities:[]}}))}),500)}))}}}},"95cd":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:e.title+"模板选择",visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("span",[e._v("模板选项：")]),n("el-radio-group",{model:{value:e.templateId,callback:function(t){e.templateId=t},expression:"templateId"}},e._l(e.templateList,(function(t){return n("el-radio",{key:t.Id,attrs:{label:t.Id}},[e._v(e._s(t.Name))])})),1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},o=[]},9643:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProjectSendingInfo=y,t.CancelFlow=z,t.DeleteProjectSendingInfo=l,t.EditProjectSendingInfo=d,t.ExportComponentStockOutInfo=k,t.ExportInvoiceList=M,t.ExportSendSteel=f,t.ExportSendingDetailInfoList=m,t.GetLocationList=G,t.GetProduceCompentEntity=S,t.GetProducedPartToSendPageList=T,t.GetProjectAcceptInfoPagelist=U,t.GetProjectSendingAllCount=g,t.GetProjectSendingInfoAndItemPagelist=R,t.GetProjectSendingInfoLogPagelist=$,t.GetProjectSendingInfoPagelist=s,t.GetProjectsendinginEntity=c,t.GetReadyForDeliverSummary=D,t.GetReadyForDeliveryComponentPageList=w,t.GetReadyForDeliveryPageList=C,t.GetReturnHistoryPageList=N,t.GetSendToReturnPageList=O,t.GetStockOutBillInfoPageList=j,t.GetStockOutDetailList=v,t.GetStockOutDetailPageList=x,t.GetStockOutDocEntity=_,t.GetStockOutDocPageList=i,t.GetWaitingStockOutPageList=b,t.GetWarehouseListOfCurFactory=L,t.GetWeighingReviewList=W,t.SaveStockOut=I,t.SubmitApproval=B,t.SubmitProjectSending=u,t.SubmitReturnToStockIn=P,t.SubmitWeighingForPC=E,t.Transforms=p,t.TransformsByType=A,t.TransformsWithoutWeight=h,t.WeighingReviewSubmit=F,t.WithdrawDraft=V;var o=a(n("b775")),r=a(n("4328"));function i(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDocPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoPagelist",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentStockOut/DeleteProjectSendingInfo",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitProjectSending",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectsendinginEntity",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/ComponentStockOut/EditProjectSendingInfo",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportSendSteel",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportSendingDetailInfoList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ComponentStockOut/Transforms",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/ComponentStockOut/TransformsWithoutWeight",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingAllCount",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailList",method:"post",data:r.default.stringify(e)})}function b(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetWaitingStockOutPageList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProduceCompentEntity",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/ComponentReturn/SubmitReturnToStockIn",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SaveStockOut",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/ComponentStockOut/AddProjectSendingInfo",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDocEntity",method:"post",data:r.default.stringify(e)})}function C(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryPageList",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryComponentPageList",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportComponentStockOutInfo",method:"post",data:r.default.stringify(e)})}function D(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliverSummary",method:"post",data:r.default.stringify(e)})}function R(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoAndItemPagelist",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/ComponentReturn/GetSendToReturnPageList",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailPageList",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutBillInfoPageList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoLogPagelist",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProducedPartToSendPageList",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/ComponentStockOut/TransformsByType",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectAcceptInfoPagelist",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/ComponentReturn/GetReturnHistoryPageList",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/ComponentStockOut/GetWeighingReviewList",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/ComponentStockOut/WeighingReviewSubmit",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitWeighingForPC",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/ComponentStockOut/WithdrawDraft",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportInvoiceList",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitApproval",method:"post",data:e})}function z(e){return(0,o.default)({url:"/Sys/FlowInstances/CancelFlow",method:"post",data:e})}},a6d2:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{display:"flex","flex-direction":"column"}},[n("div",{staticClass:"cs-z-page-main-content",staticStyle:{height:"auto","margin-bottom":"16px"}},[n("top-header",{staticStyle:{height:"100px","line-height":"normal"},scopedSlots:e._u([{key:"left",fn:function(){return[n("el-form",{ref:"searchForm",staticClass:"demo-form-inline form-search",staticStyle:{height:"100px"},attrs:{inline:!0,model:e.form}},[n("el-form-item",{attrs:{label:"发货单号：",prop:"Code"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"发货单状态：",prop:"Status"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},e._l(e.statusInfo,(function(e,t){return n("el-option",{key:t,attrs:{label:e,value:t}})})),1)],1),n("el-form-item",{attrs:{label:"项目名称",prop:"ProjectId"}},[n("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.projects,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),n("el-form-item",{attrs:{label:"收货人：",prop:"Consignee"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Consignee,callback:function(t){e.$set(e.form,"Consignee",t)},expression:"form.Consignee"}})],1),n("el-form-item",{attrs:{label:"车牌号：",prop:"VehicleNo"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.VehicleNo,callback:function(t){e.$set(e.form,"VehicleNo",t)},expression:"form.VehicleNo"}})],1),n("el-form-item",{attrs:{label:"是否退货",prop:"IsReturn"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.IsReturn,callback:function(t){e.$set(e.form,"IsReturn",t)},expression:"form.IsReturn"}},[n("el-option",{attrs:{label:"无退货",value:!1}}),n("el-option",{attrs:{label:"有退货",value:!0}})],1)],1),n("el-form-item",{attrs:{label:"过磅预警",prop:"Is_Weight_Warning"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Weight_Warning,callback:function(t){e.$set(e.form,"Is_Weight_Warning",t)},expression:"form.Is_Weight_Warning"}},[n("el-option",{attrs:{label:"是",value:!0}}),n("el-option",{attrs:{label:"否",value:!1}})],1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:function(){e.form.PageInfo.Page=1,e.getPageList()}}},[e._v("查询")]),n("el-button",{on:{click:function(t){return e.resetForm("searchForm")}}},[e._v("重置")])],1)],1)]},proxy:!0}])})],1),n("div",{staticClass:"cs-z-page-main-content",staticStyle:{flex:"1",display:"-webkit-box"}},[n("div",{staticStyle:{color:"rgba(34, 40, 52, 0.65)",padding:"10px 20px"}},[n("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增发货单")]),n("el-button",{attrs:{type:"success",disabled:!e.selectList.length},on:{click:e.handleExport}},[e._v("导出发货单(pdf)")]),n("el-button",{attrs:{type:"success",disabled:!e.selectRow},on:{click:e.handleExportExcel}},[e._v("导出发货单(excel)")]),n("el-button",{attrs:{type:"success",disabled:!e.selectList.length},on:{click:e.handlePrint}},[e._v("打印")]),n("el-button",{attrs:{type:"success",loading:e.btnLoading,disabled:0===e.tbData.length},on:{click:e.handleLeadingOut}},[e._v("导出")]),n("div",{staticClass:"date-picker-wrapper"},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.datePickerwrapper},model:{value:e.form.dateRange,callback:function(t){e.$set(e.form,"dateRange",t)},expression:"form.dateRange"}})],1),e.ProfessionalType?n("div",{staticClass:"total-wrapper"},[n("span",{staticStyle:{margin:"0 24px 0 12px"}},[e._v("构件总量："+e._s(e.totalData.Allsteelamount))]),n("span",[e._v("发货总量："+e._s(e.totalData.Allsteelweight)+"（"+e._s(e.ProfessionalType[0].Unit)+"）")])]):e._e()],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper",staticStyle:{flex:"1 1 auto"}},[n("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.comList,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,select:e.selectChange,multiSelectedChange:e.handleSelectionChange,selectAll:e.handleSelectAll},scopedSlots:e._u([{key:"SumNetWeight",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.SumNetWeight)+" ")]}},{key:"Status",fn:function(t){var n=t.row;return[e._v(" "+e._s(e.statusInfo[n.Status])+" ")]}},{key:"Number",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.Number||"-"))])]}},{key:"SumReturnCount",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(null!=a.SumReturnCount?a.SumReturnCount:"-"))])]}},{key:"SumAcceptCount",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(null!=a.SumAcceptCount?a.SumAcceptCount:"-"))])]}},{key:"SumPendingCount",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(null!=a.SumPendingCount?a.SumPendingCount:"-"))])]}},{key:"SendDate",fn:function(t){var a=t.row;return[n("div",[e._v(" "+e._s(a.SendDate||"—")+" ")])]}},{key:"op",fn:function(t){var a=t.row,o=t.index;return["0"==a.Status?[n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleEdit(a.Id,!1)}}},[e._v("编辑")]),e.Shipping_Weigh_Enabled?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handSubmit(a)}}},[e._v("提交过磅")]):[e.getAuditStatus(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.submitForReview(a)}}},[e._v("提交审核")]):n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleSub(a)}}},[e._v("提交发货")])],n("el-button",{staticStyle:{color:"red"},attrs:{index:o,type:"text"},on:{click:function(t){return e.handleDel(a.Id)}}},[e._v("删除")])]:["2"==a.Status?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleWithdraw(a.Id)}}},[e._v("撤回草稿")]):e._e(),"999"!=a.Status?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleEdit(a.Id,"-1"!=a.Status)}}},[e._v("编辑")]):e._e(),n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleInfo(a.Id)}}},[e._v("查看")]),[4,5,6].includes(+a.Status)?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleChange(a.Id)}}},[e._v("变更记录")]):e._e(),e.Is_Integration&&[4,5,6].includes(+a.Status)?n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handelView(a.Id)}}},[e._v("验收情况")]):e._e()],999==a.Status?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleCancelFlow(a.FlowId)}}},[e._v("撤回")]):e._e(),3==a.Status?[n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleWithdraw(a.Id)}}},[e._v("撤回草稿")]),e.getAuditStatus(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.submitForReview(a)}}},[e._v("提交审核")]):n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleSub(a)}}},[e._v("提交发货")])]:e._e(),a.FlowId?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleMonitor(a.FlowId)}}},[e._v("监控")]):e._e(),-1==a.Status?[e.Shipping_Weigh_Enabled?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handSubmit(a)}}},[e._v("提交过磅")]):[e.getAuditStatus(a)?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.submitForReview(a)}}},[e._v("提交审核")]):n("el-button",{attrs:{index:o,type:"text"},on:{click:function(t){return e.handleSub(a)}}},[e._v("提交发货")])]]:e._e()]}}])})],1),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增发货单",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n("el-form",{ref:"form2",staticClass:"demo-ruleForm",attrs:{model:e.form2,rules:e.rules,"label-width":"70px"}},[n("el-form-item",{attrs:{label:"项目",prop:"ProjectId"}},[n("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.projectIdChange,clear:e.projectIdClear},model:{value:e.form2.ProjectId,callback:function(t){e.$set(e.form2,"ProjectId",t)},expression:"form2.ProjectId"}},e._l(e.projects,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.resetForm2("form2")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm2("form2")}}},[e._v("确 定")])],1)],1)],1)],1),n("PrintDialog",{ref:"PrintDialog"}),n("radioDialog",{ref:"radioDialog",attrs:{"send-id":e.selectRow.Id,title:e.title,"send-data":e.selectRow}}),n("dialogExcel",{ref:"dialogExcel",attrs:{"send-id":e.selectRow.Id,title:e.title,"send-data":e.selectRow}}),n("checkDialog",{ref:"checkDialog"}),n("Monitor",{ref:"monitor"})],1)},o=[]},a6ec:function(e,t,n){"use strict";n.r(t);var a=n("8c2c"),o=n("3428");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"cf510ad4",null);t["default"]=s.exports},a9c2:function(e,t,n){"use strict";n("db88")},af17:function(e,t,n){"use strict";n("27a0")},b03a:function(e,t,n){},b224:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:e.title+"模板选择",visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("span",[e._v("模板选项：")]),n("el-radio-group",{model:{value:e.templateId,callback:function(t){e.templateId=t},expression:"templateId"}},e._l(e.templateList,(function(t){return n("el-radio",{key:t.Id,attrs:{label:t.Id}},[e._v(e._s(t.Name))])})),1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},o=[]},bef7:function(e,t,n){"use strict";n.r(t);var a=n("64d0"),o=n("c359");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);n("af17");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,null,null);t["default"]=s.exports},c359:function(e,t,n){"use strict";n.r(t);var a=n("9400"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},ca4f:function(e,t,n){"use strict";n.r(t);var a=n("b224"),o=n("ed3f");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"365f691a",null);t["default"]=s.exports},cd62:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d3b7"),n("3ca3"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494");var a=n("f4f2"),o=n("9643");t.default={components:{},props:{title:{type:String,default:"预览"},sendId:{type:String,default:""}},data:function(){return{dialogVisible:!1,preview_type:"1"}},created:function(){},mounted:function(){},methods:{handleOpen:function(){this.dialogVisible=!0},submit:function(){"预览"==this.title?this.handlePrint():this.handleExport()},handleClose:function(){this.preview_type="1",this.dialogVisible=!1},handleExport:function(){var e=this;(0,o.ExportSendSteel)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,a.baseUrl)());window.open(n.href,"_blank"),e.$message({type:"success",message:"导出成功!"}),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))},handlePrint:function(){var e=this;(0,o.TransformsByType)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,a.baseUrl)());window.open(n.href,"_blank"),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))}}}},d04d:function(e,t,n){"use strict";n.r(t);var a=n("f2f0"),o=n("80daf");for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"5e1e8af3",null);t["default"]=s.exports},da6d:function(e,t,n){"use strict";n.r(t);var a=n("cd62"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},db88:function(e,t,n){},dca8:function(e,t,n){"use strict";var a=n("23e7"),o=n("bb2f"),r=n("d039"),i=n("861d"),s=n("f183").onFreeze,l=Object.freeze,u=r((function(){l(1)}));a({target:"Object",stat:!0,forced:u,sham:!o},{freeze:function(e){return l&&i(e)?l(s(e)):e}})},dfbe:function(e,t,n){"use strict";n.r(t);var a=n("e98a"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},e1c4:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("5530")),r=n("2f62"),i=a(n("56a9")),s=a(n("1d76"));t.default={name:"PROShipSent",components:{v3:i.default,v4:s.default},computed:(0,o.default)({},(0,r.mapGetters)("tenant",["isVersionFour"]))}},e41b:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=c,t.GetPartsList=s,t.GetProjectAreaTreeList=r,t.ImportParts=u,t.SaveProjectAreaSort=i;var o=a(n("b775"));function r(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},e98a:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("7d54"),n("ab43"),n("b680"),n("b64b"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494");var o,r=a(n("5530")),i=a(n("c14f")),s=a(n("1da1")),l=n("f4f2"),u=n("9643"),c=n("7e18"),d=n("0f64");t.default={components:{},props:{title:{type:String,default:"打印"},sendId:{type:String,default:""},sendData:{type:Object,default:function(){return{}}}},data:function(){return{btnLoading:!1,dialogVisible:!1,preview_type:"1",templateList:[],mode:1,templateId:""}},created:function(){this.getTemplateList()},mounted:function(){},methods:{getTemplateList:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetPrintTemplateList)({type:e.mode});case 1:n=t.v,n.IsSucceed&&(e.templateList=n.Data,n.Data&&n.Data[0]?e.templateId=n.Data[0].Id:e.$message.error("您还未配置发货单打印模板，请先配置模板"));case 2:return t.a(2)}}),t)})))()},handleOpen:function(e){this.selectList=e,this.dialogVisible=!0},submit:function(){var e=this;this.btnLoading=!0;var t=(0,c.GetSendingBillData)({ids:this.selectList.map((function(e){return e.Id}))}),n=(0,c.GetPrintTemplateEntity)({id:this.templateId});Promise.all([t,n]).then((function(t){var n=t.filter((function(e){return!e.IsSucceed})).map((function(e){return e.Message}));if(n.length>0)return e.$message({message:n.join("; "),type:"error"}),void(e.btnLoading=!1);var a=t[0].Data,i=JSON.parse(t[1].Data.Data);o=new d.hiprint.PrintTemplate({template:i});var s=[];if(a.forEach((function(t,n){var a=0,o=0;t.Items=t.Items.map((function(t){return t.IsPackage=t.SteelSpec===t.SteelLength,"2"==e.preview_type?(t.SteelWeight=t.GrossWeight,t.SteelAllWeight=t.GrossAllWeight):(a+=t.SteelAmount,o+=t.SteelAllWeight),t})),t.Items.push({SteelName:"合计",SteelAmount:a,SteelAllWeight:o.toFixed(2)}),t.Items.push({SteelName:"备注",IsRemark:!0,SteelSpec:t.Main.Remarks}),s.push((0,r.default)((0,r.default)({},t.Main),{},{Table:t.Items}))})),"打印"==e.title)o.print(s);else{var l="";1===e.selectList.length&&(l=e.selectList[0].Code),o.toPdf(s,"发货单"+l)}e.btnLoading=!1})).catch((function(t){e.btnLoading=!1}))},handleClose:function(){this.preview_type="1",this.dialogVisible=!1},handleExport:function(){var e=this;(0,u.ExportSendSteel)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,l.baseUrl)());window.open(n.href,"_blank"),e.$message({type:"success",message:"导出成功!"}),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))},handlePrint:function(){var e=this;(0,u.TransformsByType)({sendId:this.sendId,preview_type:this.preview_type}).then((function(t){if(t.IsSucceed){var n=new URL(t.Data,(0,l.baseUrl)());window.open(n.href,"_blank"),e.handleClose()}else e.$message({message:t.Message,type:"error"})}))}}}},ea13:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteGroup=u,t.DeleteGroupRole=h,t.DeleteGroupUser=d,t.DeleteUserRole=y,t.GetGroupEntity=s,t.GetGroupList=r,t.GetGroupRole=m,t.GetGroupTree=i,t.GetGroupUser=f,t.GetGroupUserByRole=S,t.GetRoleListCanAdd=p,t.GetShuttleUserList=P,t.GetWorkingObjTreeListByGroupId=b,t.SaveGroup=l,t.SaveGroupObject=v,t.SaveGroupRole=g,t.SaveGroupUser=c,t.SaveUserRoles=I;var o=a(n("b775"));function r(){return(0,o.default)({url:"/SYS/UserGroup/GetGroupList",method:"post"})}function i(e){return(0,o.default)({url:"/SYS/UserGroup/GetGroupTree",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/UserGroup/GetGroupEntity",method:"post",data:e})}function l(e){return(0,o.default)({url:"/SYS/UserGroup/SaveGroup",method:"post",data:e})}function u(e){return(0,o.default)({url:"/SYS/UserGroup/DeleteGroup",method:"post",data:e})}function c(e){return(0,o.default)({url:"/SYS/UserGroup/SaveGroupUser",method:"post",data:e})}function d(e){return(0,o.default)({url:"/SYS/UserGroup/DeleteGroupUser",method:"post",data:e})}function f(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetGroupUser",data:e})}function m(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetGroupRole",data:e})}function p(e){return(0,o.default)({method:"post",url:"sys/UserGroup/GetRoleListCanAdd",data:e})}function h(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/DeleteGroupRole",data:e})}function g(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/SaveGroupRole",data:e})}function v(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/SaveGroupObject",data:e})}function b(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetWorkingObjTreeListByGroupId",data:e})}function S(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetGroupUserByRole",data:e})}function P(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/GetShuttleUserList",data:e})}function I(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/SaveUserRoles",data:e})}function y(e){return(0,o.default)({method:"post",url:"/SYS/UserGroup/DeleteUserRole",data:e})}},ed3f:function(e,t,n){"use strict";n.r(t);var a=n("5ea4"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=o.a},ee63:function(e,t,n){},f039:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:e.title+"重量选择",visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("span",[e._v("重量选项：")]),n("el-radio-group",{model:{value:e.preview_type,callback:function(t){e.preview_type=t},expression:"preview_type"}},[n("el-radio",{attrs:{label:"1"}},[e._v("净重")]),n("el-radio",{attrs:{label:"2"}},[e._v("毛重")]),n("el-radio",{attrs:{label:"3"}},[e._v("无重量")])],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},o=[]},f2f0:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.isVersionFour?n("v4"):n("v3")],1)},o=[]},f2f6:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=l,t.CheckPlanTime=u,t.DeleteInstallUnit=m,t.GetCompletePercent=b,t.GetEntity=P,t.GetInstallUnitAllInfo=d,t.GetInstallUnitComponentPageList=v,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=c,t.GetInstallUnitList=s,t.GetInstallUnitPageList=i,t.GetProjectInstallUnitList=S,t.ImportInstallUnit=h,t.InstallUnitInfoTemplate=p,t.SaveInstallUnit=g,t.SaveOhterSourceInstallUnit=I;var o=a(n("b775")),r=a(n("4328"));function i(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function u(e){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function c(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function d(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function m(e){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function p(e){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function h(e){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function P(e){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(e)})}function I(e){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},f382:function(e,t,n){"use strict";function a(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=a(e.Children)),!0)}))}function o(e){e.map((function(e){if(e.Is_Directory||!e.Children)return o(e.Children);delete e.Children}))}function r(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function a(e,t,o){for(var r=0;r<e.length;r++){var i=e[r];if(i.Id===t)return n&&o.push(i),o;if(i.Children&&i.Children.length){if(o.push(i),a(i.Children,t,o).length)return o;o.pop()}}return[]}return a(e,t,[])}function i(e){return e.Children&&e.Children.length>0?i(e.Children[0]):e}function s(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&s(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=o,t.disableDirectory=s,t.findAllParentNode=r,t.findFirstNode=i,t.getDirectoryTree=a,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7")}}]);