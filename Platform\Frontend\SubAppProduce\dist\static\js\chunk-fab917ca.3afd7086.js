(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-fab917ca"],{"205a":function(e,n,t){"use strict";t.r(n);var u=t("4dd0"),a=t("f461");for(var r in a)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(r);var o=t("2877"),c=Object(o["a"])(a["default"],u["a"],u["b"],!1,null,"6b1e9aca",null);n["default"]=c.exports},"3a45":function(e,n,t){"use strict";var u=t("4ea4").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=u(t("751c"));n.default={name:"PROReportHistory",provide:{pageType:"com"},components:{Home:a.default}}},"4dd0":function(e,n,t){"use strict";t.d(n,"a",(function(){return u})),t.d(n,"b",(function(){return a}));var u=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("home")},a=[]},f461:function(e,n,t){"use strict";t.r(n);var u=t("3a45"),a=t.n(u);for(var r in u)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(r);n["default"]=a.a}}]);