(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7d759025"],{"1be8":function(t,e,o){"use strict";o.r(e);var n=o("6d1b"),r=o.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"30cb":function(t,e,o){"use strict";o.r(e);var n=o("7fae9"),r=o("1be8");for(var a in r)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(a);o("69fd");var u=o("2877"),i=Object(u["a"])(r["default"],n["a"],n["b"],!1,null,"605b1faf",null);e["default"]=i.exports},3166:function(t,e,o){"use strict";var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=P,e.DeleteProject=l,e.GeAreaTrees=O,e.GetFileSync=R,e.GetInstallUnitIdNameList=y,e.GetNoBindProjectList=m,e.GetPartDeepenFileList=D,e.GetProjectAreaTreeList=b,e.GetProjectEntity=d,e.GetProjectList=i,e.GetProjectPageList=u,e.GetProjectTemplate=p,e.GetPushProjectPageList=G,e.GetSchedulingPartList=v,e.IsEnableProjectMonomer=s,e.SaveProject=c,e.UpdateProjectTemplateBase=h,e.UpdateProjectTemplateContacts=g,e.UpdateProjectTemplateContract=j,e.UpdateProjectTemplateOther=C;var r=n(o("b775")),a=n(o("4328"));function u(t){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:a.default.stringify(t)})}function c(t){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:a.default.stringify(t)})}function s(t){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function G(t){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function O(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function R(t){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"69fd":function(t,e,o){"use strict";o("d3ba")},"6d1b":function(t,e,o){"use strict";var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("4de4"),o("caad"),o("d81d"),o("e9f5"),o("910d"),o("ab43"),o("d3b7"),o("2532"),o("c7cd");var r=n(o("c14f")),a=n(o("1da1")),u=o("f84a"),i=o("6186"),d=o("c3c6"),c=o("3166"),l=o("8975"),s=n(o("c1df"));e.default={name:"PROItemInventoryStatistics",data:function(){return{downloadLoading:!1,project:"",beginDate:"",endDate:"",ProjectNameData:[],tbData:[],columns:[]}},computed:{selectedDateRange:{get:function(){return[(0,l.timeFormat)(this.beginDate),(0,l.timeFormat)(this.endDate)]},set:function(t){if(t){var e=t[0],o=t[1];this.beginDate=(0,l.timeFormat)(e),this.endDate=(0,l.timeFormat)(o)}else this.beginDate="",this.endDate=""}}},mounted:function(){var t=this;return(0,a.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.beginDate=(0,s.default)().startOf("month").format("YYYY-MM-DD"),t.endDate=(0,s.default)().endOf("month").format("YYYY-MM-DD"),t.getProjectOption(),e.n=1,t.getTableConfig("PROItemInventoryStatisticsConfig");case 1:t.fetchData();case 2:return e.a(2)}}),e)})))()},methods:{handleExport:function(){var t=this.$refs.xTable1.getCheckboxRecords(),e=t.length>0?t:this.tbData;this.$refs.xTable1.exportData({filename:"项目库存统计",type:"xlsx",data:e,columnFilterMethod:function(t){var e=t.column;return!!e.field}})},getProjectOption:function(){var t=this;(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},fetchData:function(){var t=this;(0,u.GetProjectWarehouseDataStatistics)({sysProjectId:this.project,beginDate:this.beginDate,endDate:this.endDate}).then((function(e){e.IsSucceed?t.tbData=e.Data.Data.map((function(t){return t.checked=!1,t})):t.$message({message:e.Message,type:"error"})}))},getTableConfig:function(t){var e=this;return(0,a.default)((0,r.default)().m((function o(){return(0,r.default)().w((function(o){while(1)switch(o.n){case 0:return o.n=1,(0,i.GetGridByCode)({code:t}).then((function(t){var o=t.IsSucceed,n=t.Data,r=t.Message;if(o){e.tbConfig=Object.assign({},e.tbConfig,n.Grid);var a=n.ColumnList||[];e.columns=a.filter((function(t){return t.Is_Display})).map((function(t){return d.FIX_COLUMN.includes(t.Code)&&(t.fixed="left"),t}))}else e.$message({message:r,type:"error"})}));case 1:return o.a(2)}}),o)})))()}}}},"7fae9":function(t,e,o){"use strict";o.d(e,"a",(function(){return n})),o.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[o("div",{staticClass:"cs-z-page-main-content"},[o("el-form",{ref:"form",attrs:{inline:"","label-width":"80px"}},[o("el-form-item",{attrs:{label:"选择日期:"}},[o("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:t.selectedDateRange,callback:function(e){t.selectedDateRange=e},expression:"selectedDateRange"}})],1),o("el-form-item",{attrs:{label:"项目:"}},[o("el-select",{attrs:{filterable:"",placeholder:"请选择",clearable:""},model:{value:t.project,callback:function(e){t.project=e},expression:"project"}},t._l(t.ProjectNameData,(function(t){return o("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Sys_Project_Id}})})),1)],1),o("el-form-item",[o("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("搜 索")]),o("el-button",{attrs:{loading:t.downloadLoading,type:"success"},on:{click:t.handleExport}},[t._v("导 出")])],1)],1),o("div",{staticClass:"cs-main"},[o("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","checkbox-config":{checkField:"checked"},height:"auto",align:"left",stripe:"","row-config":{isCurrent:!0,isHover:!0},size:"medium",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}}},[o("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),t._l(t.columns,(function(e){return[o("vxe-column",{key:e.Id,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:e.Code,title:e.Display_Name,"min-width":e.Width},scopedSlots:t._u([{key:"default",fn:function(o){var n=o.row;return[t._v(" "+t._s(n[e.Code]||"0")+" ")]}}],null,!0)})]}))],2)],1)],1)])},r=[]},c3c6:function(t,e,o){"use strict";var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.uniqueCode=e.getDraftQuery=e.FIX_COLUMN=void 0,o("b0c0");var r=n(o("5530"));e.getDraftQuery=function(t,e,o,n,a){return{name:t,query:(0,r.default)({status:e,pg_type:o,pg_redirect:a.name},n)}},e.uniqueCode=function(t){return"uuid"},e.FIX_COLUMN=["Comp_Code","Project_Name","Area_Name","Part_Code","InstallUnit_Name"]},d3ba:function(t,e,o){},f84a:function(t,e,o){"use strict";var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportAllProjectComponentProductionFlowData=v,e.ExportProjectAreaProgress=_,e.ExportProjectComponentProductionFlowData=D,e.ExportSchedulingPlanProcess=R,e.GetCompFinishProcess=u,e.GetCompInPercent=c,e.GetCompOutPercent=l,e.GetCompPercent=s,e.GetComponentListByStatus=G,e.GetComponentYieldByStatus=b,e.GetDaysAndWeight=f,e.GetInstallUnitProducedCount1=p,e.GetInstallUnitProducedCount2=h,e.GetNestingFinishPercent=i,e.GetPartFinishPercent=d,e.GetProcessLoad=P,e.GetProjectAreaProgressList=j,e.GetProjectAreaProgressSummary=C,e.GetProjectComponentProductionFlowPageList=y,e.GetProjectSchdulingSendingData=O,e.GetProjectWarehouseDataStatistics=g,e.GetTeamLoad=m;var r=n(o("b775")),a=n(o("4328"));function u(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompFinishProcess",method:"post",data:a.default.stringify(t)})}function i(t){return(0,r.default)({url:"/PRO/ProductionCount/GetNestingFinishPercent",method:"post",data:a.default.stringify(t)})}function d(t){return(0,r.default)({url:"/PRO/ProductionCount/GetPartFinishPercent",method:"post",data:a.default.stringify(t)})}function c(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompInPercent",method:"post",data:a.default.stringify(t)})}function l(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompOutPercent",method:"post",data:a.default.stringify(t)})}function s(t){return(0,r.default)({url:"/PRO/ProductionCount/GetCompPercent",method:"post",data:a.default.stringify(t)})}function f(t){return(0,r.default)({url:"/PRO/ProductionCount/GetDaysAndWeight",method:"post",data:a.default.stringify(t)})}function P(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProcessLoad",method:"post",data:a.default.stringify(t)})}function m(t){return(0,r.default)({url:"/PRO/ProductionCount/GetTeamLoad",method:"post",data:a.default.stringify(t)})}function p(t){return(0,r.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount1",method:"post",data:a.default.stringify(t)})}function h(t){return(0,r.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount2",method:"post",data:a.default.stringify(t)})}function g(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectWarehouseDataStatistics",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressList",method:"post",data:t})}function C(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressSummary",method:"post",data:t})}function G(t){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentListByStatus",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentYieldByStatus",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectComponentProductionFlowPageList",method:"post",data:t})}function O(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectSchdulingSendingData",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PRO/ProductionCount/ExportProjectComponentProductionFlowData",method:"post",data:t})}function v(t){return(0,r.default)({url:"/pro/ProductionCount/ExportAllProjectComponentProductionFlowData",method:"post",data:t})}function R(t){return(0,r.default)({url:"/pro/ProductionCount/ExportSchedulingPlanProcess",method:"post",data:t})}function _(t){return(0,r.default)({url:"/pro/ProductionCount/ExportProjectAreaProgress",method:"post",data:t})}}}]);