(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-491b44cc"],{"0130":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:e.title,visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"60%","destroy-on-close":"","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form}},[e.pageType<2?[a("el-form-item",{attrs:{label:"所属项目"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:0==e.pageType?"原料名称":"辅料名称"}},[a("el-input",{model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"代码规格"}},[a("el-input",{model:{value:e.form.Specification,callback:function(t){e.$set(e.form,"Specification",t)},expression:"form.Specification"}})],1)]:[a("el-form-item",{attrs:{label:2==e.pageType?"构件名称":"半成品名称"}},[a("el-input",{model:{value:e.form.Comp_Code,callback:function(t){e.$set(e.form,"Comp_Code",t)},expression:"form.Comp_Code"}})],1),a("el-form-item",{attrs:{label:"项目：",prop:"Project_Id"}},[a("el-select",{staticClass:"w100",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域：",prop:"region"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x w100",attrs:{disabled:!e.form.Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"批次：",prop:"region"}},[a("el-select",{staticClass:"w100",attrs:{disabled:!e.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:e.changeInstall},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.PositionDataList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("搜索")]),a("el-button",{on:{click:e.onCancel}},[e._v("重置")])],1)],2),a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"edit-config":{trigger:"click",mode:"cell"},align:"left",height:"350","show-overflow":"tooltip",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{type:"checkbox",width:"60",fixed:"left"}}),e._l(e.columnsList,(function(t,i){return["Type"==t.Code?a("vxe-column",{key:i,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,align:t.Align,sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(1==a.Type?"零件半成品":"构件半成品")+" ")]}}],null,!0)}):a("vxe-column",{key:i,attrs:{field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width,align:t.Align}})]}))],2),a("div",{staticStyle:{"text-align":"right"}},[a("Pagination",{staticClass:"pagination",attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")])],1)],1)},o=[]},"39cd":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(a("5530"));a("4de4"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("a9e3"),a("d3b7");var n=i(a("15ac")),l=i(a("57ec")),r=i(a("83b4")),s=a("03c9"),c=a("c685"),d=i(a("333d"));t.default={components:{Pagination:d.default},mixins:[n.default,l.default,r.default],props:{pageType:{type:Number,default:0}},data:function(){return{title:"新增",dialogVisible:!1,form:{Name:"",Specification:"",ProjectId:"",Type:0},queryInfo:{Page:1,PageSize:20},total:0,tbLoading:!1,tablePageSize:c.tablePageSize,tbData:[],columns:[],type:"add",rowItem:[],rowIndex:0,multipleSelection:[]}},computed:{PositionDataList:function(){return this.SetupPositionData.filter((function(e){var t;return""!=(null!==(t=e.Name)&&void 0!==t?t:"")}))},columnsList:function(){return this.columns.filter((function(e){return!["移库数量","移库总重(kg)"].includes(e.Display_Name)}))}},created:function(){this.getProjectOption()},mounted:function(){},methods:{handleOpen:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;this.onCancel(),this.getProjectOption(),this.type=e,this.rowItem={},this.rowItem=t,this.rowIndex=a,this.fetchData(),this.title="add"==e?"新增":"修改";var i=2==this.pageType?"TransferOrderDetail":3==this.pageType?"SemiFinishProductDetail":1==this.pageType?"AuxiliaryMaterialDetail":"RowMaterialDetail";this.getTableConfig(i),this.dialogVisible=!0},fetchData:function(){var e=this,t=this.pageType<2?s.GetMaterialsInformation:s.GetComponenntStockPageList,a=[];this.rowItem.length>0&&this.rowItem.map((function(t){e.pageType>1?a.push({Move_Object_Id:t.Move_Object_Id?t.Move_Object_Id:t.Id,Location_Id:t.From_Location_Id}):a.push({Move_Object_Id:t.Move_Object_Id,Location_Id:t.To_Location_Id})})),t((0,o.default)((0,o.default)((0,o.default)({},this.form),this.queryInfo),{},{Type:this.pageType,SelectedList:a})).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({type:"error",message:t.Message})}))},submit:function(){"add"==this.type?this.$emit("selectData",{data:this.multipleSelection,type:"add"}):1==this.multipleSelection.length?this.$emit("selectData",{data:this.multipleSelection,type:"modify",index:this.rowIndex}):this.$message.warning("当前操作只能选择一个数据"),this.handleClose()},handleClose:function(){this.dialogVisible=!1},tbSelectChange:function(e){this.multipleSelection=e.records},onCancel:function(){this.form={Name:"",Specification:"",ProjectId:"",Type:0},this.fetchData()},changeInstall:function(){this.$forceUpdate()},pageChange:function(){this.fetchData()}}}},"57ca":function(e,t,a){"use strict";a.r(t);var i=a("f4cd"),o=a("c1da");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("9e64");var l=a("2877"),r=Object(l["a"])(o["default"],i["a"],i["b"],!1,null,"25cf1384",null);t["default"]=r.exports},"5fad":function(e,t,a){"use strict";a("730f")},"730f":function(e,t,a){},8617:function(e,t,a){},"88cd":function(e,t,a){"use strict";var i=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(a("2909"));a("99af"),a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("a732"),a("a9e3"),a("d3b7");var n=a("ed08"),l=i(a("15ac")),r=i(a("dbe8")),s=i(a("57ec")),c=a("03c9"),d=i(a("c1df"));t.default={components:{Dialog:r.default},mixins:[l.default,s.default],data:function(){return{Id:"",Type:0,form:{Receipt_Number:"",To_Warehouse_Id:"",To_Location_Id:"",Move_Date:(0,d.default)().format("YYYY-MM-DD"),Remark:""},queryInfo:{Page:1,PageSize:20},total:0,Status:0,tbLoading:!1,isEdit:!1,tbData:[],columns:[],rules:{To_Warehouse_Id:[{required:!0,message:"请选择仓库",trigger:"change"}],To_Location_Id:[{required:!0,message:"请选择库位",trigger:"change"}]}}},created:function(){this.Type=Number(this.$route.query.Type),this.Id=this.$route.query.row.Id,this.isEdit=this.$route.query.isEdit;var e=2==this.Type?"TransferOrderDetail":3==this.Type?"SemiFinishProductDetail":1==this.Type?"AuxiliaryMaterialDetail":"RowMaterialDetail";this.getTableConfig(e),this.getWarehouseList(this.Type)},mounted:function(){var e,t=this;""!=(null!==(e=this.Id)&&void 0!==e?e:"")&&(0,c.GetTransferOrderDetail)({Type:this.Type,Id:this.Id}).then((function(e){e.IsSucceed?(t.Status=e.Data.Status,t.tbData=t.Type<1?e.Data.Raw_Move_List:1==t.Type?e.Data.Aux_Move_List:e.Data.Component_Move_List,Object.assign(t.form,e.Data.Move_Receipt),t.wareChange(t.form.To_Warehouse_Id),t.tbData.map((function(e){e.TransferTotalWeight=e.Num*e.NetWeight}))):t.$message.error(e.Message)}))},methods:{closeView:function(){(0,n.closeTagView)(this.$store,this.$route)},handleDel:function(){this.$refs.xTable.removeCheckboxRow();var e=this.$refs.xTable.getRemoveRecords();this.tbData=this.tbData.filter((function(t){return!e.some((function(e){return e.Id==t.Id}))}))},inputClick:function(e){1!=this.Status&&this.$refs.dialog.handleOpen("modify",this.tbData,e)},handleAdd:function(){this.$refs.dialog.handleOpen("add",this.tbData)},selectData:function(e){var t;"add"==e.type?(t=this.tbData).push.apply(t,(0,o.default)(e.data)):Object.assign.apply(Object,[this.tbData[e.index]].concat((0,o.default)(e.data)));this.tbData.map((function(e){var t;""!=(null!==(t=e.StockQuantity)&&void 0!==t?t:"")&&(e.Stock_Count=e.StockQuantity),e.Num=e.Stock_Count,e.TransferTotalWeight=e.Num*e.NetWeight}))},calculateWeight:function(e,t){t.Num=""==e?1:e,t.TransferTotalWeight=t.Num*t.NetWeight},handleSubmit:function(e){var t=this;this.form.Type=this.Type;var a={Move_Receipt:this.form,Status:e};this.tbData.map((function(e){e.Move_Object_Id=e.Move_Object_Id?e.Move_Object_Id:e.Id})),0==this.Type?a.Raw_Move_List=this.tbData:1==this.Type?a.Aux_Move_List=this.tbData:a.Component_Move_List=this.tbData;var i="true"==this.isEdit?c.UpdateTransferOrderDetail:c.AddTransferOrderDetail;this.$refs.form.validate((function(e){if(!e)return!1;i(a).then((function(e){e.IsSucceed?(t.$message.success("保存成功"),t.closeView()):t.$message.error(e.Message)}))}))}}}},"9e64":function(e,t,a){"use strict";a("8617")},c1da:function(e,t,a){"use strict";a.r(t);var i=a("88cd"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},dbe8:function(e,t,a){"use strict";a.r(t);var i=a("0130"),o=a("e12d");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("5fad");var l=a("2877"),r=Object(l["a"])(o["default"],i["a"],i["b"],!1,null,"2e9d9b51",null);t["default"]=r.exports},e12d:function(e,t,a){"use strict";a.r(t);var i=a("39cd"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=o.a},f4cd:function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return o}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card h100"},[a("div",{staticClass:"addOrderContainer"},[a("div",{staticClass:"top"},[a("h2",[e._v("移库单信息")]),a("el-row",{staticStyle:{"margin-right":"50px"}},[a("el-form",{ref:"form",staticClass:"demo-form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"移库单号："}},[e._v(" "+e._s(""==e.form.Receipt_Number?"自动生成":e.form.Receipt_Number)+" ")])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"调入仓库：",prop:"To_Warehouse_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.wareChange},model:{value:e.form.To_Warehouse_Id,callback:function(t){e.$set(e.form,"To_Warehouse_Id",t)},expression:"form.To_Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"调入库位：",prop:"To_Location_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.To_Warehouse_Id,placeholder:"请选择"},model:{value:e.form.To_Location_Id,callback:function(t){e.$set(e.form,"To_Location_Id",t)},expression:"form.To_Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"移库日期：",prop:"Move_Date"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:e.form.Move_Date,callback:function(t){e.$set(e.form,"Move_Date",t)},expression:"form.Move_Date"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注：",prop:"Remark"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入内容"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1)],1)],1),a("div",{staticClass:"middle"},[a("h2",[e._v("移库明细")]),1!=e.Status?a("div",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增")]),a("el-button",{attrs:{type:"danger"},on:{click:e.handleDel}},[e._v("删除")])],1):e._e()]),a("div",{staticClass:"bottom"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"edit-config":{trigger:"click",mode:"cell"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[a("vxe-column",{attrs:{type:"checkbox",width:"60",fixed:"left"}}),e._l(e.columns,(function(t,i){return["移库数量"==t.Display_Name?a("vxe-column",{key:i,attrs:{field:t.Code,title:t.Display_Name,"edit-render":{},"min-width":t.Width,align:t.Align,fixed:"right"},scopedSlots:e._u([{key:"edit",fn:function(t){var i=t.row;return[a("vxe-input",{attrs:{min:1,max:i.StockQuantity,type:"integer"},on:{change:function(t){var a=t.value;e.calculateWeight(a,i)}},model:{value:i.Num,callback:function(t){e.$set(i,"Num",t)},expression:"row.Num"}})]}},{key:"default",fn:function(t){var i=t.row;return[e._v(" "+e._s(i.Num)+" "),a("i",{staticClass:"el-icon-edit"})]}}],null,!0)}):"Name"==t.Code?a("vxe-column",{key:i,attrs:{field:t.Code,title:1==e.Type?"辅料名称":"原料名称","min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row,o=t.rowIndex;return[a("vxe-input",{staticClass:"my-search",attrs:{readonly:""},on:{"suffix-click":function(t){return e.inputClick(o)}},scopedSlots:e._u([{key:"suffix",fn:function(){return[a("i",{staticClass:"iconfont icon-task-time"})]},proxy:!0}],null,!0),model:{value:i.Name,callback:function(t){e.$set(i,"Name",t)},expression:"row.Name"}})]}}],null,!0)}):"Comp_Code"==t.Code?a("vxe-column",{key:i,attrs:{field:t.Code,title:2==e.Type?"构件名称":"半成品名称","min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row,o=t.rowIndex;return[a("vxe-input",{staticClass:"my-search",attrs:{readonly:""},on:{"suffix-click":function(t){return e.inputClick(o)}},scopedSlots:e._u([{key:"suffix",fn:function(){return[a("i",{staticClass:"iconfont icon-task-time"})]},proxy:!0}],null,!0),model:{value:i.Comp_Code,callback:function(t){e.$set(i,"Comp_Code",t)},expression:"row.Comp_Code"}})]}}],null,!0)}):"Type"==t.Code?a("vxe-column",{key:i,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(1==a.Type?"零件半成品":"构件半成品")+" ")]}}],null,!0)}):a("vxe-column",{key:i,attrs:{field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width,align:t.Align}})]}))],2)],1),a("div",{staticClass:"buttonGroup"},[1!=e.Status?[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{on:{click:function(t){return e.handleSubmit(0)}}},[e._v("保存草稿")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit(1)}}},[e._v("提交")])]:[a("el-button",{on:{click:e.closeView}},[e._v("关闭 ")])]],2)])]),a("Dialog",{ref:"dialog",attrs:{"page-type":e.Type},on:{selectData:e.selectData}})],1)},o=[]}}]);