(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-69ba4257"],{"07d1":function(t,e,n){"use strict";n.r(e);var a=n("4f9d"),o=n("68b8");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("a72a");var i=n("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"c5720432",null);e["default"]=l.exports},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,i=t.Data,l=t.Message;if(a){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),s=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(e.columns)}else e.$message({message:l,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===e){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},1778:function(t,e,n){},"1dba":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("14d9"),n("e9f5"),n("910d"),n("7d54"),n("a732"),n("d3b7"),n("159b");var o=a(n("c14f")),r=a(n("1da1")),i=n("cf45"),l=n("5e99"),s=n("2e8a"),c=n("8975"),u=n("f2f6"),d=n("1b69");e.default={data:function(){return{plmProjectId:"",title:"",btnLoading:!1,categoryOption:[],unitOption:[],factoryOption:[],detailList:[],projectOption:[],form:{Component_Category:"",Project_Id:"",Name:"",Shipping_Duration:"",Plan_Begin_Date:"",Plan_End_Date:""},detailForm:{},rules:{Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Shipping_Duration:[{required:!0,message:"天数不能为空"},{type:"number",message:"天数必须为数字值"}],Project_Id:[{required:!0,message:"请选择项目",trigger:"change"}],Component_Category:[{required:!0,message:"请选择构件类别",trigger:"change"}],Plan_Begin_Date:[{required:!0,message:"请选择时间",trigger:"change"}]}}},computed:{isPlmProject:function(){return!(!this.plmProjectId||"undefined"===this.plmProjectId)},planTime:{get:function(){return[(0,c.timeFormat)(this.form.Plan_Begin_Date),(0,c.timeFormat)(this.form.Plan_End_Date)]},set:function(t){if(t){var e=t[0],n=t[1];this.form.Plan_Begin_Date=(0,c.timeFormat)(e),this.form.Plan_End_Date=(0,c.timeFormat)(n)}else this.form.Plan_Begin_Date="",this.form.Plan_End_Date=""}}},mounted:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return t.plmProjectId=t.$route.query.id,t.Source=t.$route.query.source,e.n=1,(0,i.getDictionary)("component_category");case 1:return t.categoryOption=e.v,e.n=2,(0,i.getDictionary)("unit");case 2:t.unitOption=e.v,t.getProjectList(),t.isPlmProject&&t.getPlmInfo();case 3:return e.a(2)}}),e)})))()},methods:{getPlmInfo:function(){var t=this;(0,u.GetEntity)({id:this.plmProjectId}).then((function(e){e.IsSucceed?t.form.Project_Id=e.Data.Sys_Project_Id:t.$message({message:e.Message,type:"error"})}))},getFactory:function(){var t=this;(0,l.GetFactoryList)({Category:this.form.Component_Category}).then((function(e){e.IsSucceed&&(t.factoryOption=e.Data)}))},getProjectList:function(){var t=this;(0,d.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projectOption=e.Data)}))},getDetail:function(){var t=this;(0,s.GetComponentTypeList)({Level:1,Category:this.form.Component_Category}).then((function(e){e.IsSucceed&&(t.detailList=e.Data,t.detailList.forEach((function(e){t.$set(e,"Plan_Amount",void 0),t.$set(e,"Unit",e.Summary_Unit)})))})),this.getFactory()},handleSubmit:function(t){var e=this,n=this.detailList.some((function(t){return t.Plan_Amount>=0}));n?this.$refs[t].validate((function(t){if(!t)return!1;var n={entity:e.form,details:[]},a="";e.isPlmProject?(n.entity.Sys_Project_Id=e.form.Project_Id,n.entity.Source=e.Source,a=e.projectOption.filter((function(t){return t.Sys_Project_Id===e.form.Project_Id}))):(n.entity.Source=2,a=e.projectOption.filter((function(t){return t.Id===e.form.Project_Id}))),n.entity.Project_Code=a.length&&a[0].Code,n.entity.Project_Name=a.length&&a[0].Name,e.detailList.forEach((function(t){n.details.push({Component_Type_Id:t.Id,Component_Type_Code:t.Code,Component_Type_Name:t.Name,Plan_Amount:t.Plan_Amount,Unit:t.Summary_Unit})})),e.btnLoading=!0,e.isPlmProject?e.submitOtherApi(n):e.installSubmit(n)})):this.$message({message:"安装单元明细数值不能全为空",type:"warning"})},installSubmit:function(t){var e=this;(0,u.SaveInstallUnit)(t).then((function(t){t.IsSucceed?e.submitSuccess():e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))},submitOtherApi:function(t){var e=this;(0,u.SaveOhterSourceInstallUnit)(t).then((function(t){t.IsSucceed?e.submitSuccess():e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))},submitSuccess:function(){this.$message({message:"添加成功",type:"success"}),this.resetForm("form"),this.$emit("refresh")},resetForm:function(){this.$emit("close")}}}},"2e8a":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteComponentType=u,e.GetCompTypeTree=d,e.GetComponentTypeEntity=c,e.GetComponentTypeList=i,e.GetFactoryCompTypeIndentifySetting=g,e.GetTableSettingList=m,e.GetTypePageList=l,e.RestoreTemplateType=_,e.SavDeepenTemplateSetting=y,e.SaveCompTypeIdentifySetting=b,e.SaveComponentType=s,e.SaveProBimComponentType=f,e.UpdateColumnSetting=h,e.UpdateComponentPartTableSetting=p;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:r.default.stringify(t)})}function u(t){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:r.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:r.default.stringify(t)})}function f(t){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:t})}function g(t){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:t})}function b(t){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:t})}function y(t){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:t})}function _(t){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:t})}},"37c3":function(t,e,n){"use strict";n("6ac3")},"4e82":function(t,e,n){"use strict";var a=n("23e7"),o=n("e330"),r=n("59ed"),i=n("7b0b"),l=n("07fa"),s=n("083a"),c=n("577e"),u=n("d039"),d=n("addb"),f=n("a640"),m=n("3f7e"),p=n("99f4"),h=n("1212"),g=n("ea83"),b=[],y=o(b.sort),_=o(b.push),v=u((function(){b.sort(void 0)})),P=u((function(){b.sort(null)})),I=f("sort"),C=!u((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var t,e,n,a,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)b.push({k:e+a,v:n})}for(b.sort((function(t,e){return e.v-t.v})),a=0;a<b.length;a++)e=b[a].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),D=v||!P||!I||!C,S=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:c(e)>c(n)?1:-1}};a({target:"Array",proto:!0,forced:D},{sort:function(t){void 0!==t&&r(t);var e=i(this);if(C)return void 0===t?y(e):y(e,t);var n,a,o=[],c=l(e);for(a=0;a<c;a++)a in e&&_(o,e[a]);d(o,S(t)),n=l(o),a=0;while(a<n)e[a]=o[a++];while(a<c)s(e,a++);return e}})},"4f9d":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"项目名：",prop:"ProjectName"}},[n("strong",[t._v(t._s(t.form.ProjectName))])]),n("el-form-item",{attrs:{label:"安装单元名称：",prop:"Name"}},[n("strong",[t._v(t._s(t.form.Name))])]),n("el-form-item",{attrs:{label:"计划时间调整：",prop:"EndDate"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","value-format":"yyyy-MM-dd",align:"right","picker-options":t.expireTimeOption,"start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:t.dataChange},model:{value:t.Plan_Date,callback:function(e){t.Plan_Date=e},expression:"Plan_Date"}}),t.showBox?n("label",{staticStyle:{float:"right","font-size":"12px"}},[t._v("（为保证生产的稳定性，不建议做时间调整。）")]):t._e()],1),t.showBox?n("el-form-item",{attrs:{label:"调整原因：",prop:"AdjustReason"}},[n("el-input",{attrs:{autosize:{minRows:5,maxRows:5},"show-word-limit":"",type:"textarea"},model:{value:t.form.AdjustReason,callback:function(e){t.$set(t.form,"AdjustReason",e)},expression:"form.AdjustReason"}})],1):t._e(),n("el-form-item",{staticClass:"dialog-footer"},[n("el-button",{attrs:{loading:t.btnLoading,type:"primary"},on:{click:function(e){return t.handleSubmit("form")}}},[t._v("确 定")])],1)],1)],1)},o=[]},5521:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"项目名称：",prop:"Project_Id"}},[n("el-select",{staticClass:"w100",attrs:{filterable:"",disabled:t.isPlmProject,placeholder:"请选择",clearable:""},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},[t.isPlmProject?t._l(t.projectOption,(function(t){return n("el-option",{key:t.Sys_Project_Id,attrs:{label:t.Name,value:t.Sys_Project_Id}})})):t._l(t.projectOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})}))],2)],1),n("el-form-item",{attrs:{label:"安装单元名称：",prop:"Name"}},[n("el-input",{attrs:{clearable:""},model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name",e)},expression:"form.Name"}})],1),n("el-form-item",{attrs:{label:"构件类别：",prop:"Component_Category"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择"},on:{change:t.getDetail},model:{value:t.form.Component_Category,callback:function(e){t.$set(t.form,"Component_Category",e)},expression:"form.Component_Category"}},t._l(t.categoryOption,(function(t){return n("el-option",{key:t.Value,attrs:{label:t.Display_Name,value:t.Display_Name}})})),1)],1),n("el-form-item",{attrs:{label:"安装单元明细："}},[n("el-card",{staticClass:"inner-card",attrs:{shadow:"none"}},[n("el-form",{attrs:{"label-width":"80px"}},t._l(t.detailList,(function(e){return n("el-form-item",{key:e.Id,attrs:{label:e.Name}},[n("div",{staticStyle:{display:"flex"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden",staticStyle:{width:"80%"},attrs:{placeholder:"请输入"},model:{value:e.Plan_Amount,callback:function(n){t.$set(e,"Plan_Amount",t._n(n))},expression:"item.Plan_Amount"}}),n("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(e.Unit))])],1)])})),1)],1),n("div",{staticClass:"tips"},[t._v("注：至少需要填入一个构件大类的值")])],1),n("el-form-item",{attrs:{label:"发运时长：",prop:"Shipping_Duration"}},[n("el-input",{model:{value:t.form.Shipping_Duration,callback:function(e){t.$set(t.form,"Shipping_Duration",t._n(e))},expression:"form.Shipping_Duration"}},[n("template",{slot:"append"},[t._v("天")])],2)],1),n("el-form-item",{attrs:{label:"计划时间：",prop:"Plan_Begin_Date"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.planTime,callback:function(e){t.planTime=e},expression:"planTime"}})],1)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:t.resetForm}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit("form")}}},[t._v("确 定")])],1)],1)},o=[]},"633d":function(t,e,n){"use strict";n("e43d")},"68b8":function(t,e,n){"use strict";n.r(e);var a=n("fb00"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"6ac3":function(t,e,n){},7100:function(t,e,n){"use strict";n("9211")},"713a":function(t,e,n){"use strict";n.r(e);var a=n("ece3"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"776a":function(t,e,n){"use strict";n.r(e);var a=n("bd1a"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},8053:function(t,e,n){"use strict";n.r(e);var a=n("d2c2"),o=n("776a");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("633d");var i=n("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"4d498965",null);e["default"]=l.exports},"80dd":function(t,e,n){"use strict";n.r(e);var a=n("9ac3"),o=n("713a");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("7100");var i=n("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"954796c8",null);e["default"]=l.exports},9211:function(t,e,n){},"9ac3":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"box"},[n("span",{staticStyle:{display:"inline-block"}},[t._v("1.选择项目")]),n("el-select",{staticStyle:{width:"70%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.ProjectId,callback:function(e){t.ProjectId=e},expression:"ProjectId"}},t._l(t.proOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("div",{staticClass:"box"},[n("span",[t._v("2.下载模板")]),n("el-button",{attrs:{size:"large"},on:{click:t.handleDownload}},[n("svg-icon",{attrs:{"icon-class":"document_form_icon"}}),t._v(" 安装单元模板 ")],1)],1),n("div",{staticClass:"upload-box"},[n("span",{staticStyle:{"margin-bottom":"20px",display:"inline-block"}},[t._v(" 3.上传文件 ")]),n("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}})],1),n("footer",{staticClass:"cs-footer"},[n("el-button",{attrs:{disabled:t.btnLoading},on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:function(e){return t.handleSubmit()}}},[t._v("确 定")])],1)])},o=[]},a1d0:function(t,e,n){"use strict";n.r(e);var a=n("5521"),o=n("ce0a");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("37c3");var i=n("2877"),l=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"435a4a40",null);e["default"]=l.exports},a72a:function(t,e,n){"use strict";n("1778")},bd1a:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("c14f")),r=a(n("1da1"));n("4de4"),n("7db0"),n("14d9"),n("b0c0"),n("e9f5"),n("910d"),n("f665"),n("dca8"),n("d3b7"),n("3ca3"),n("ddb0");var i=a(n("0f97")),l=a(n("34e9")),s=a(n("a1d0")),c=a(n("80dd")),u=a(n("07d1")),d=a(n("2082")),f=n("f2f6"),m=a(n("15ac")),p=n("1b69");e.default={name:"PROInstallationUnit",components:{DynamicDataTable:i.default,TopHeader:l.default,unitDialog:s.default,importDialog:c.default,planDialog:u.default},mixins:[d.default,m.default],data:function(){return{plmProjectId:"",addPageArray:[{path:"install_unit/detail",hidden:!0,component:function(){return n.e("chunk-f2c9a4f2").then(n.bind(null,"986e"))},name:"PROInstallationUnitDetail",meta:{title:"详情"}}],currentComponent:"",title:"",dialogVisible:!1,tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},tableData:[],columns:[],tbData:[],projects:[],total:0,tbLoading:!1,hasProject:!1}},computed:{isPlmProject:function(){return!(!this.plmProjectId||"undefined"===this.plmProjectId)},plmNoData:function(){return this.isPlmProject&&!this.hasProject}},mounted:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return t.plmProjectId=t.$route.query.id,e.n=1,t.getTableConfig("pro_installunit_list");case 1:return e.n=2,t.getComponentType();case 2:t.isPlmProject?(t.hasProject=t.getSearch(),t.hasProject&&t.fetchData()):t.fetchData();case 3:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.tbLoading=!0,(0,f.GetInstallUnitPageList)(this.queryInfo).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},getSearch:function(){var t=this;if(this.isPlmProject){var e,n=null===(e=this.projects.find((function(e){return e.Sys_Project_Id===t.plmProjectId})))||void 0===e?void 0:e.Name;return!!n&&(this.$refs.dyTable.searchedField["Project_Name"]=n,this.queryInfo.ParameterJson.push({Key:"Project_Name",Type:"text",Filter_Type:"radio",Value:[n]}),!0)}},getInnerTable:function(t,e){var n,a=this;"External_Details"===e.Code?n=1:"Demand_Content"===e.Code&&(n=2),(0,f.GetCompletePercent)({installunitId:t.Id,type:n}).then((function(t){t.IsSucceed&&(a.tableData=t.Data.filter((function(t){return t.total>0})))}))},handleDelete:function(t){var e=this;this.$confirm("是否删除该数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DeleteInstallUnit)({id:t.Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.fetchData()):e.$message({message:"删除失败",type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},getComponentType:function(){var t=this;return new Promise((function(e){(0,p.GetProjectList)({}).then((function(n){n.IsSucceed&&(t.projects=Object.freeze(n.Data),e())}))}))},handleClose:function(){this.dialogVisible=!1},handleAdd:function(){this.currentComponent="unitDialog",this.title="新建",this.dialogVisible=!0},handleInfoImport:function(){this.currentComponent="importDialog",this.title="安装单元信息导入",this.dialogVisible=!0},handleChangePlan:function(t){var e=this;this.currentComponent="planDialog",this.title="计划时间调整",this.dialogVisible=!0,this.$nextTick((function(n){e.$refs["content"].getData(t)}))},handleDetail:function(t){this.$router.push({name:"PROInstallationUnitDetail",query:{id:t.Id,pg_redirect:this.$route.name}})}}}},ce0a:function(t,e,n){"use strict";n.r(e);var a=n("1dba"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},cf45:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=o,n("d3b7");var a=n("6186");function o(t){return new Promise((function(e,n){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},d2c2:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[n("div",{staticClass:"cs-z-page-main-content"},[n("top-header",{scopedSlots:t._u([{key:"right",fn:function(){return[n("el-button",{attrs:{type:"primary",disabled:t.plmNoData},on:{click:t.handleInfoImport}},[t._v("安装单元信息导入")]),n("el-button",{attrs:{type:"primary",disabled:t.plmNoData},on:{click:t.handleAdd}},[t._v("新建安装单元")])]},proxy:!0}])}),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[n("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,config:t.tbConfig,data:t.tbData,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"op",fn:function(e){var a=e.row;return[2===a.Source?n("el-button",{attrs:{type:"text",disabled:a.Is_Completed||2!==a.Source},on:{click:function(e){return t.handleChangePlan(a)}}},[t._v("计划调整时间 ")]):t._e(),2===a.Source&&!t.isPlmProject||3===a.Source&&t.isPlmProject?n("el-button",{staticClass:"txt-red",attrs:{disabled:a.Comp_Count>0,type:"text"},on:{click:function(e){return t.handleDelete(a)}}},[t._v("删除 ")]):t._e(),n("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleDetail(a)}}},[t._v("详情")])]}},{key:"Plan_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Plan_Date))+" ")]}},{key:"Ultimate_Demand_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Ultimate_Demand_Date))+" ")]}},{key:"Complete_Content",fn:function(e){var a=e.row;return[n("el-tooltip",{attrs:{effect:"dark",placement:"bottom"}},[n("div",{staticClass:"cs-tip-content",attrs:{slot:"content"},slot:"content"},[t._v(t._s(a.Complete_Content))]),n("div",{staticClass:"cs-tip-x"},[n("span",{staticClass:"cs-tip-label"},[t._v(" "+t._s(a.Complete_Content))])])])]}},{key:"Demand_Content",fn:function(e){var a=e.row,o=e.column;return[n("el-tooltip",{attrs:{effect:"dark",placement:"bottom"}},[n("div",{staticClass:"cs-tip-content",attrs:{slot:"content"},slot:"content"},[t._v(t._s(a.Demand_Content))]),n("div",{staticClass:"cs-tip-x"},[n("span",{staticClass:"cs-tip-label"},[t._v(" "+t._s(a.Demand_Content))])])]),n("el-popover",{attrs:{placement:"bottom",trigger:"click",width:"400"},on:{show:function(e){return t.getInnerTable(a,o)}}},[n("div",{staticClass:"tooltip-content"},[n("el-table",{staticClass:"tip-tb",attrs:{data:t.tableData}},[n("el-table-column",{attrs:{label:"类别",prop:"component_type_name"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",{staticStyle:{color:"#222834","font-weight":"bold"}},[t._v(t._s(a.component_type_name))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"需求量",prop:"total"}}),n("el-table-column",{attrs:{label:"已完成",prop:"complete_amount"}}),n("el-table-column",{attrs:{label:"完成率","min-width":"100px",prop:"percent"},scopedSlots:t._u([{key:"default",fn:function(t){var e=t.row;return[n("el-progress",{staticStyle:{"text-direction":"rtl"},attrs:{percentage:e.percent}})]}}],null,!0)})],1)],1),n("el-link",{staticStyle:{"margin-left":"2px"},attrs:{slot:"reference",underline:!1,type:"primary"},slot:"reference"},[n("i",{staticClass:"el-icon-chat-line-square"})])],1)]}},{key:"External_Details",fn:function(e){var a=e.row,o=e.column;return[n("el-tooltip",{attrs:{effect:"dark",placement:"bottom"}},[n("div",{staticClass:"cs-tip-content",attrs:{slot:"content"},slot:"content"},[t._v(t._s(a.External_Details))]),n("div",{staticClass:"cs-tip-x"},[n("span",{staticClass:"cs-tip-label"},[t._v(" "+t._s(a.External_Details))])])]),n("el-popover",{attrs:{placement:"bottom",trigger:"click",width:"400"},on:{show:function(e){return t.getInnerTable(a,o)}}},[n("div",{staticClass:"tooltip-content"},[n("el-table",{staticClass:"tip-tb",attrs:{data:t.tableData}},[n("el-table-column",{attrs:{label:"类别",prop:"component_type_name"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("span",{staticStyle:{color:"#222834","font-weight":"bold"}},[t._v(t._s(a.component_type_name))])]}}],null,!0)}),n("el-table-column",{attrs:{label:"需求量",prop:"total"}}),n("el-table-column",{attrs:{label:"已完成",prop:"complete_amount"}}),n("el-table-column",{attrs:{label:"完成率","min-width":"100px",prop:"percent"},scopedSlots:t._u([{key:"default",fn:function(t){var e=t.row;return[n("el-progress",{staticStyle:{"text-direction":"rtl"},attrs:{percentage:e.percent}})]}}],null,!0)})],1)],1),n("el-link",{staticStyle:{"margin-left":"2px"},attrs:{slot:"reference",underline:!1,type:"primary"},slot:"reference"},[n("i",{staticClass:"el-icon-chat-line-square"})])],1)]}},{key:"hsearch_Project_Name",fn:function(e){var a=e.column;return[n("el-select",{attrs:{disabled:t.isPlmProject,placeholder:"请选择",filterable:"",clearable:""},on:{change:t.showSearchBtn},model:{value:t.$refs.dyTable.searchedField[a.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,a.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},[n("el-option",{attrs:{label:"全部",value:""}}),t._l(t.projects,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})}))],2)]}}])})],1)],1),n("el-dialog",{attrs:{title:t.title,visible:t.dialogVisible,width:"40%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[t.dialogVisible?n(t.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":t.dialogVisible},on:{close:t.handleClose,refresh:t.fetchData}}):t._e()],1)],1)},o=[]},dca8:function(t,e,n){"use strict";var a=n("23e7"),o=n("bb2f"),r=n("d039"),i=n("861d"),l=n("f183").onFreeze,s=Object.freeze,c=r((function(){s(1)}));a({target:"Object",stat:!0,forced:c,sham:!o},{freeze:function(t){return s&&i(t)?s(l(t)):t}})},e43d:function(t,e,n){},ece3:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("3796")),r=n("f2f6"),i=n("1b69"),l=n("ed08");e.default={components:{UploadExcel:o.default},data:function(){return{options:[],btnLoading:!1,ProjectId:"",proOption:[]}},mounted:function(){this.getProjectList()},methods:{handleDownload:function(){var t=this;(0,r.InstallUnitInfoTemplate)({}).then((function(e){e.IsSucceed?window.open((0,l.combineURL)(t.$baseUrl,e.Data)):t.$message({message:e.Message,type:"error"})}))},getProjectList:function(){var t=this;(0,i.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.proOption=e.Data)}))},beforeUpload:function(t){var e=this,n=new FormData;n.append("ProjectId",this.ProjectId),n.append("files",t),this.btnLoading=!0,(0,r.ImportInstallUnit)(n).then((function(t){if(t.IsSucceed)e.$message({message:"导入成功",type:"success"}),e.$emit("refresh");else if(e.$message({message:t.Message,type:"error"}),t.Data){var n=(0,l.combineURL)(e.$baseUrl,t.Data);window.open(n,"_blank")}e.$emit("close"),e.btnLoading=!1}))},handleSubmit:function(){this.ProjectId?this.$refs.upload.handleSubmit():this.$message({message:"请选择项目",type:"info"})}}}},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=s,e.CheckPlanTime=c,e.DeleteInstallUnit=m,e.GetCompletePercent=y,e.GetEntity=v,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=b,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=u,e.GetInstallUnitList=l,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=_,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=P;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function c(t){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function u(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function P(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},fb00:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("5530")),r=a(n("c14f")),i=a(n("1da1"));n("d3b7");var l=n("f2f6"),s=n("8975");e.default={props:{dialogVisible:{type:Boolean,default:!1}},data:function(){return{expireTimeOption:{disabledDate:function(t){return t.getTime()<Date.now()-864e5}},btnLoading:!1,showBox:!1,form:{Id:"",ProjectName:"",Name:"",BeginDate:"",EndDate:"",AdjustReason:""},checkTimeStatus:!1,rules:{EndDate:[{required:!0,message:"请选择日期",trigger:"change"}]}}},computed:{Plan_Date:{get:function(){return[(0,s.timeFormat)(this.form.BeginDate),(0,s.timeFormat)(this.form.EndDate)]},set:function(t){if(t){var e=t[0],n=t[1];this.form.BeginDate=(0,s.timeFormat)(e),this.form.EndDate=(0,s.timeFormat)(n)}else this.form.BeginDate="",this.form.EndDate=""}}},methods:{getData:function(t){var e=t.Id,n=t.Project_Name,a=t.Name,o=t.Plan_Date.length&&t.Plan_Date.split("~");this.form.BeginDate=o[0]||"",this.form.EndDate=o[1]||"",this.form.Id=e,this.form.ProjectName=n,this.form.Name=a,this.form.AdjustReason=""},checkTime:function(){var t=this;return new Promise((function(e,n){(0,l.CheckPlanTime)({id:t.form.Id,beginDate:t.form.BeginDate,endDate:t.form.EndDate}).then((function(n){n.IsSucceed?(t.checkTimeStatus=n.Data,e(t.checkTimeStatus)):t.$message({message:n.Message,type:"error"})}))}))},dataChange:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){var n;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:if(t.form.EndDate){e.n=1;break}return e.a(2);case 1:return e.n=2,t.checkTime();case 2:n=e.v,t.showBox=!n;case 3:return e.a(2)}}),e)})))()},handleSubmit:function(t){var e=this;!this.showBox||this.form.AdjustReason?this.$refs[t].validate((function(t){t&&(e.btnLoading=!0,(0,l.AdjustPlanTime)((0,o.default)((0,o.default)({},e.form),{},{isException:e.checkTimeStatus})).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh"),e.showBox=!1):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1})))})):this.$message({message:"请填写调整原因",type:"info"})}}}}}]);