(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5616148f"],{1561:function(t,e,n){"use strict";n.r(e);var a=n("f2a3"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),i=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,o=t.Data,u=t.Message;if(a){if(!o)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,o.Grid),l=n?(null===o||void 0===o?void 0:o.ColumnList)||[]:(null===o||void 0===o?void 0:o.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+o.Grid.Row_Number||i.tablePageSize[0]),r(e.columns)}else e.$message({message:u,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var i=this.columns[a];if(i.Code===e){n.Type=i.Type,n.Filter_Type=i.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1aa7f":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.loading?t._e():n("main-page",{ref:"main",attrs:{"tb-config-code":t.tbConfigCode,"name-list":t.nameList,"add-page-array":t.addPageArray,"tb-data":t.tbData,total:t.total,"default-page":t.defaultPage},on:{"update:defaultPage":function(e){t.defaultPage=e},"update:default-page":function(e){t.defaultPage=e},fetchData:t.fetchData}})},i=[]},3749:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("c14f")),r=a(n("1da1"));n("14d9"),n("a9e3");var o=a(n("34e9")),u=a(n("0f97")),l=a(n("15ac")),s=a(n("3dcb")),d=a(n("2082")),c=a(n("4b32")),f=n("f2f6");e.default={components:{TopHeader:o.default,DynamicDataTable:u.default,BtnGroup:s.default,CheckInfo:c.default},mixins:[l.default,d.default],props:{nameList:{type:Object,default:function(){}},addPageArray:{type:Array,default:function(){return[]}},tbData:{type:Array,default:function(){return[]}},tbConfigCode:{type:String,default:""},total:{type:Number,default:0},defaultPage:{type:Number,default:0}},data:function(){return{btnOptions:[{label:"单据",value:0},{label:"明细",value:1}],planTime:"",options:[],value:"",tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},tableData:[],columns:[],unitOption:[],tbLoading:!0}},computed:{typeBtn:{get:function(){return this.defaultPage},set:function(t){this.$emit("update:defaultPage",t)}}},watch:{defaultPage:function(t,e){this.init()}},mounted:function(){this.init()},methods:{init:function(){var t=this;return(0,r.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return t.tbLoading=!0,e.n=1,t.getTableConfig(t.tbConfigCode);case 1:t.fetchData(),t.getSearchType();case 2:return e.a(2)}}),e)})))()},fetchData:function(){this.$emit("fetchData",this.queryInfo)},getSearchType:function(){var t=this;(0,f.GetProjectInstallUnitList)({}).then((function(e){e.IsSucceed?t.unitOption=e.Data:t.$message({message:e.Message,type:"error"})}))},showTable:function(){this.tbLoading=!1},handleAdd:function(){this.$router.push({name:this.nameList.add,query:{pg_redirect:this.nameList.parent}})},handleEdit:function(t){this.$router.push({name:this.nameList.edit,query:{pg_redirect:this.nameList.parent,id:t}})},handleInfo:function(t){this.$router.push({name:this.nameList.detail,query:{pg_redirect:this.nameList.parent,id:t}})},handleInfoDetail:function(t){t.Unique_Code=t.unique_code,this.$refs.info.handleOpen(t)}}}},"3f35":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportPackingList=f,e.GeneratePackCode=o,e.GetPacking2ndEntity=l,e.GetPacking2ndPageList=u,e.GetPackingGroupByDirectDetailList=r,e.GetWaitPack2ndPageList=s,e.SavePacking2nd=c,e.UnzipPacking2nd=d;var i=a(n("b775"));a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function o(){return(0,i.default)({url:"/PRO/Packing/GeneratePackCode",method:"post"})}function u(t){return(0,i.default)({url:"/PRO/Packing/GetPacking2ndPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/Packing/GetPacking2ndEntity",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/Packing/GetWaitPack2ndPageList",method:"post",data:t})}function d(t){return(0,i.default)({url:"/PRO/Packing/UnzipPacking2nd",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/Packing/SavePacking2nd",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/Packing/ExportPackingList",method:"post",data:t})}},"40d1":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n("3f35");e.default={data:function(){return{innerTableData:[],dialogVisible:!1}},methods:{handleInfo:function(t){var e=this;(0,a.GetPackingGroupByDirectDetailList)({Unique_Code:t.Unique_Code}).then((function(t){t.IsSucceed?e.innerTableData=t.Data:e.$message({message:t.Message,type:"error"})}))},handleOpen:function(t){this.dialogVisible=!0,this.handleInfo(t)},handleClose:function(){}}}},"4b32":function(t,e,n){"use strict";n.r(e);var a=n("a729"),i=n("939e");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n("2877"),u=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"784abcb7",null);e["default"]=u.exports},"4e82":function(t,e,n){"use strict";var a=n("23e7"),i=n("e330"),r=n("59ed"),o=n("7b0b"),u=n("07fa"),l=n("083a"),s=n("577e"),d=n("d039"),c=n("addb"),f=n("a640"),h=n("3f7e"),p=n("99f4"),m=n("1212"),g=n("ea83"),b=[],P=i(b.sort),v=i(b.push),y=d((function(){b.sort(void 0)})),I=d((function(){b.sort(null)})),_=f("sort"),C=!d((function(){if(m)return m<70;if(!(h&&h>3)){if(p)return!0;if(g)return g<603;var t,e,n,a,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)b.push({k:e+a,v:n})}for(b.sort((function(t,e){return e.v-t.v})),a=0;a<b.length;a++)e=b[a].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),R=y||!I||!_||!C,O=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:s(e)>s(n)?1:-1}};a({target:"Array",proto:!0,forced:R},{sort:function(t){void 0!==t&&r(t);var e=o(this);if(C)return void 0===t?P(e):P(e,t);var n,a,i=[],s=u(e);for(a=0;a<s;a++)a in e&&v(i,e[a]);c(i,O(t)),n=u(i),a=0;while(a<n)e[a]=i[a++];while(a<s)l(e,a++);return e}})},"62d4":function(t,e,n){"use strict";n.r(e);var a=n("739e"),i=n("ecd3");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("dcc0");var o=n("2877"),u=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"e8c56f9c",null);e["default"]=u.exports},"630a":function(t,e,n){},"739e":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[n("div",{staticClass:" cs-z-page-main-content"},[n("top-header",{scopedSlots:t._u([{key:"left",fn:function(){return[n("el-form",{staticClass:"cs-form",attrs:{inline:""}},[n("el-form-item",[n("btn-group",{attrs:{size:"mini",options:t.btnOptions},model:{value:t.typeBtn,callback:function(e){t.typeBtn=e},expression:"typeBtn"}})],1)],1)]},proxy:!0},0===t.defaultPage?{key:"right",fn:function(){return[n("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新 增")])]},proxy:!0}:null],null,!0)}),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[t.tbLoading?t._e():n("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,config:t.tbConfig,data:t.tbData,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"Return_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Return_Date))+" ")]}},{key:"Cancel_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Cancel_Date))+" ")]}},{key:"hsearch_pi_name",fn:function(e){var a=e.column;return[n("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:t.showSearchBtn},model:{value:t.$refs.dyTable.searchedField[a.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,a.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},t._l(t.unitOption,(function(t,e){return n("el-option",{key:e,attrs:{label:t.pi_name,value:t.pi_name}})})),1)]}},{key:"op",fn:function(e){var a=e.row,i=e.index;return[0===t.defaultPage?n("span",[n("el-button",{attrs:{index:i,type:"text"},on:{click:function(e){return t.handleEdit(a.Id)}}},[t._v("编辑")]),n("el-button",{attrs:{index:i,type:"text"},on:{click:function(e){return t.handleInfo(a.Id)}}},[t._v("查看")])],1):t._e(),1===t.defaultPage&&"打包件"===a.c_type?n("el-button",{attrs:{index:i,type:"text"},on:{click:function(e){return t.handleInfoDetail(a)}}},[t._v("查看")]):t._e()]}}],null,!1,776074831)})],1),n("check-info",{ref:"info"})],1)])},i=[]},"82a3":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportReturnInfo=f,e.GetReturnDetailList=d,e.GetReturnDetailPageList=u,e.GetReturnDocEntity=s,e.GetReturnDocPageList=o,e.GetStockOutPageList=l,e.SaveStockReturn=c;var i=a(n("b775")),r=a(n("4328"));function o(t){return(0,i.default)({url:"/PRO/ComponentReturn/GetReturnDocPageList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/ComponentReturn/GetReturnDetailPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/ComponentReturn/GetStockOutPageList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/ComponentReturn/GetReturnDocEntity",method:"post",data:r.default.stringify(t)})}function d(t){return(0,i.default)({url:"/PRO/ComponentReturn/GetReturnDetailList",method:"post",data:r.default.stringify(t)})}function c(t){return(0,i.default)({url:"/PRO/ComponentReturn/SaveStockReturn",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/ComponentReturn/ExportReturnInfo",method:"post",data:t})}},"939e":function(t,e,n){"use strict";n.r(e);var a=n("40d1"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},a729:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{title:"提示","append-to-body":"",visible:t.dialogVisible,width:"50%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n("el-table",{staticClass:"inner-tb",staticStyle:{width:"100%"},attrs:{data:t.innerTableData}},[n("el-table-column",{attrs:{align:"center",prop:"InstallUnit_Name",label:"生产单元"}}),n("el-table-column",{attrs:{align:"center",prop:"Component_Code",label:"编号",width:"180"}}),n("el-table-column",{attrs:{align:"center",prop:"Unique_Code",label:"唯一码"}}),n("el-table-column",{attrs:{align:"center",prop:"Spec",label:"规格型号"}}),n("el-table-column",{attrs:{align:"center",prop:"Num",label:"数量"}}),n("el-table-column",{attrs:{align:"center",prop:"NetWeight",label:"总重（kg）"}})],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("确 定")])],1)],1)},i=[]},d47a:function(t,e,n){"use strict";n.r(e);var a=n("1aa7f"),i=n("1561");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n("2877"),u=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"376c5229",null);e["default"]=u.exports},dcc0:function(t,e,n){"use strict";n("630a")},ecd3:function(t,e,n){"use strict";n.r(e);var a=n("3749"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},f2a3:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0"),n("d3b7"),n("3ca3"),n("ddb0");var i=a(n("62d4")),r=n("82a3");e.default={name:"PROReturnGoods",components:{mainPage:i.default},data:function(){return{addPageArray:[],loading:!0,nameList:{parent:this.$route.name,add:"PROReturnGoodsAdd",edit:"PROReturnGoodsEdit",detail:"PROReturnGoodsDetail"},defaultPage:0,tbData:[],total:0}},computed:{name:function(){return this.$route.name},tbConfigCode:function(){return 0===this.defaultPage?"pro_return_bill_list":"pro_total_return_list"}},mounted:function(){this.initRouter()},methods:{fetchData:function(t){0===this.defaultPage?this.fetchDetailPage(t):this.fetchDocPage(t)},fetchDetailPage:function(t){var e=this;(0,r.GetReturnDocPageList)(t).then((function(t){var n=t.IsSucceed,a=t.Message,i=t.Data;n?(e.tbData=i.Data,e.total=i.TotalCount,e.$refs.main.showTable()):e.$message({message:a,type:"error"})}))},fetchDocPage:function(t){var e=this;(0,r.GetReturnDetailPageList)(t).then((function(t){var n=t.IsSucceed,a=t.Message,i=t.Data;n?(e.tbData=i.Data,e.total=i.TotalCount,e.$refs.main.showTable()):e.$message({message:a,type:"error"})}))},initRouter:function(){this.addPageArray=[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([n.e("chunk-fb4375a2"),n.e("chunk-3c068ec4")]).then(n.bind(null,"1b6d"))},name:this.nameList.add,meta:{title:"新增成品退货单"}},{path:this.$route.path+"/edit",hidden:!0,component:function(){return Promise.all([n.e("chunk-fb4375a2"),n.e("chunk-4a87c8a8")]).then(n.bind(null,"17c1"))},name:this.nameList.edit,meta:{title:"编辑退货明细"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return n.e("chunk-f74a2aee").then(n.bind(null,"1976"))},name:this.nameList.detail,meta:{title:"成品退货详情"}}],this.loading=!1}}}},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=l,e.CheckPlanTime=s,e.DeleteInstallUnit=h,e.GetCompletePercent=P,e.GetEntity=y,e.GetInstallUnitAllInfo=c,e.GetInstallUnitComponentPageList=b,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=d,e.GetInstallUnitList=u,e.GetInstallUnitPageList=o,e.GetProjectInstallUnitList=v,e.ImportInstallUnit=m,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=I;var i=a(n("b775")),r=a(n("4328"));function o(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function s(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function y(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function I(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);