(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3ce38219"],{"12b2":function(e,t,n){},"21e2":function(e,t,n){"use strict";n.r(t);var a=n("49b0"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},2391:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("87b6"));t.default={name:"PROTransferReceiveView",components:{Detail:i.default},data:function(){return{}}}},"3cea":function(e,t,n){"use strict";n("12b2")},"49b0":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("7d54"),n("ab43"),n("b64b"),n("d3b7"),n("159b");var i=a(n("c14f")),o=a(n("1da1")),r=a(n("15ac")),s=n("7f9d"),l=n("ed08"),c=a(n("6612"));t.default={filters:{filterNum:function(e){return e?(0,c.default)(e).divide(1e3).format("0.[00]"):0}},mixins:[r.default],data:function(){return{finishList:[],tbLoading:!1,pageLoading:!1,pageType:"",queryInfo:{Page:1,PageSize:-1},tbData:[],columns:[],multipleSelection:[],formInline:{},Tenant_Code:localStorage.getItem("tenant")}},computed:{isCom:function(){return"com"===this.pageType},isView:function(){var e;return"view"===(null===(e=this.$route.query)||void 0===e?void 0:e.type)}},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n,a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:t.p=0,a=JSON.parse(decodeURIComponent(null===(n=e.$route.query)||void 0===n?void 0:n.other)),e.formInline=Object.assign({},e.formInline,a),e.pageType=e.$route.query.pg_type,t.n=2;break;case 1:return t.p=1,t.v,e.$message({message:"参数错误",type:"error"}),t.a(2);case 2:return t.n=3,e.getTableConfig(e.isCom?"PROComTransferReceiveNewDetail":"PROPartTransferReceiveNewDetail");case 3:e.isView||e.columns.push({Code:"Receiving_Count",Display_Name:"接收",Width:160}),e.queryInfo.PageSize=-1,e.fetchData();case 4:return t.a(2)}}),t,null,[[0,1]])})))()},methods:{checkMethod:function(e){var t=e.row;return!t.stopFlag},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,e.fetchList();case 1:return n=t.v,t.n=2,e.getStopList(n);case 2:e.tbData=n,e.multipleSelection=[],e.tbLoading=!1;case 3:return t.a(2)}}),t)})))()},getStopList:function(e){var t=this;return(0,o.default)((0,i.default)().m((function n(){var a,o;return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return a="Id",o=e.map((function(e){return{Id:e[a],Type:t.isCom?2:1}})),n.n=1,(0,s.GetStopList)(o).then((function(n){if(n.IsSucceed){var i={};n.Data.forEach((function(e){i[e.Id]=!!e.Is_Stop})),e.forEach((function(e){i[e[a]]&&t.$set(e,"stopFlag",i[e[a]])}))}}));case 1:return n.a(2)}}),n)})))()},fetchList:function(){var e=this,t=s.GetToReceiveTaskDetailList;return new Promise((function(n,a){t({Type:e.isCom?2:1,Working_Team_Id:e.formInline.tid,Task_Code:e.formInline.Task_Code,Project_Id:e.formInline.pid,Area_Id:e.formInline.aid,InstallUnit_Id:e.formInline.iid}).then((function(e){e.IsSucceed?n(e.Data.map((function(e){return e.checked=!1,e.Receiving_Count=e.Can_Receive_Count,e}))):a(e.Message)}))})).catch((function(t){e.$message({message:t,type:"error"})}))},handleReceive:function(e){var t=this,n=e?"是否默认接收当前全部数据?":"是否接收当前数据?";this.$confirm(n,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n=null;n=e?t.tbData.filter((function(e){return e.Receiving_Count>0&&!e.stopFlag})).map((function(e){return{Task_Id:e.Task_Id,Receiving_Count:e.Receiving_Count}})):t.multipleSelection.filter((function(e){return e.Receiving_Count>0&&!e.stopFlag})).map((function(e){return{Task_Id:e.Task_Id,Receiving_Count:e.Receiving_Count}})),n.length?(t.pageLoading=!0,(0,s.ReceiveTaskFromStock)({Type:t.isCom?2:1,List:n}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"}),t.pageLoading=!1}))):t.$message({message:"暂无可接收数据",type:"warning"})})).catch((function(){t.$message({type:"info",message:"已取消"}),t.pageLoading=!1}))},tbSelectChange:function(e){this.multipleSelection=e.records},closeView:function(){(0,l.closeTagView)(this.$store,this.$route)}}}},"87b6":function(e,t,n){"use strict";n.r(t);var a=n("ed63"),i=n("21e2");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("3cea");var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"a3b2c726",null);t["default"]=s.exports},dd06:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("detail",{attrs:{"is-view":""}})},i=[]},e858:function(e,t,n){"use strict";n.r(t);var a=n("2391"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},ed63:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"box-card h100",attrs:{"element-loading-text":"加载中"}},[n("h4",{staticClass:"topTitle"},[n("span"),e._v("基本信息")]),n("el-form",{ref:"formInline",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline}},[n("el-row",[n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"任务单编号:",prop:"Transfer_Code"}},[n("span",[e._v(e._s(e.formInline.Task_Code))])])],1),e.isCom?e._e():[n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"项目名称:",prop:"Project_Name"}},[n("span",[e._v(e._s(e.formInline.Project_Name))])])],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"区域名称:",prop:"Area_Name"}},[n("span",[e._v(e._s(e.formInline.Area_Name))])])],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"批次名称:",prop:"InstallUnit_Name"}},[n("span",[e._v(e._s(e.formInline.InstallUnit_Name))])])],1)],n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"库存数量:",prop:"Stock_Count"}},[n("span",[e._v(e._s(e.formInline.Stock_Count))])])],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"接收工序:",prop:"Working_Process_Name"}},[n("span",[e._v(e._s(e.formInline.Working_Process_Name))])])],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"接收班组:",prop:"Working_Team_Name"}},[n("span",[e._v(e._s(e.formInline.Working_Team_Name))])])],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"需接收数量:",prop:"Current_Task_Count"}},[n("span",[e._v(e._s(e.formInline.Current_Task_Count))])])],1),n("el-col",{attrs:{span:4}},[n("el-form-item",{attrs:{label:"已接收数量:",prop:"Received_Count"}},[n("span",[e._v(e._s(e.formInline.Received_Count))])])],1)],2)],1),n("el-divider",{staticClass:"elDivder"}),n("vxe-toolbar",{ref:"xToolbar",scopedSlots:e._u([{key:"buttons",fn:function(){return[e.isView?e._e():n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleReceive(!0)}}},[e._v("全部接收")])]},proxy:!0}])}),n("div",{staticClass:"tb-x"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},"checkbox-config":{checkField:"checked",checkMethod:e.checkMethod},align:"left",height:"100%","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Code"===t.Code?n("vxe-column",{key:t.Id,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,align:t.Align,width:"auto"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.stopFlag?n("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),n("span",[e._v(e._s(a.Code))])]}}],null,!0)}):"Receiving_Count"===t.Code?n("vxe-column",{key:t.Id,attrs:{fixed:"right",field:t.Code,title:t.Display_Name,"min-width":t.Width,align:t.Align,width:"auto"},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("vxe-input",{attrs:{min:0,type:"number",max:i.Can_Receive_Count},on:{change:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})]}}],null,!0)}):n("vxe-column",{key:t.Id,attrs:{fixed:["Code"].includes(t.Code)?"left":"Can_Receive_Count"===t.Code?"right":"","show-overflow":"tooltip",sortable:"",align:t.Align,width:"auto",field:t.Code,title:t.Display_Name,"min-width":t.Width}})]}))],2)],1),e.isView?e._e():n("div",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:e.closeView}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleReceive(!1)}}},[e._v("接 收")])],1)],1)],1)},i=[]},ef68:function(e,t,n){"use strict";n.r(t);var a=n("dd06"),i=n("e858");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"523a380c",null);t["default"]=s.exports}}]);