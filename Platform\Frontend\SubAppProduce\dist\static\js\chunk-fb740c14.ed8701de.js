(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-fb740c14"],{"01e1":function(t,e,a){"use strict";a.r(e);var n=a("52b8"),i=a("4a7e");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("22d2a");var s=a("2877"),r=Object(s["a"])(i["default"],n["a"],n["b"],!1,null,"1fb0d734",null);e["default"]=r.exports},"22d2a":function(t,e,a){"use strict";a("9973")},"433f":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("5530"));a("14d9"),a("b0c0"),a("d3b7"),a("3ca3"),a("ddb0");var o=n(a("2082")),s=a("20ff");e.default={name:"FlowGeneralFlow",components:{},mixins:[o.default],data:function(){return{addPageArray:[{path:this.$route.path+"/create/type/:type/:id?",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-de86d6a0"),a.e("chunk-54fa4a98"),a.e("chunk-3a3c0f1b"),a.e("chunk-30a601f0")]).then(a.bind(null,"0f54"))},name:"GeneralFlowCreate",meta:{title:"发起流程"}}],flow_types:[],query:{PageSize:-1,Page:1,TotalCount:0,Apply_Type:"1"}}},created:function(){this.loadSchemes()},methods:{create:function(t){this.openPage("GeneralFlowCreate",{type:t.Web_Id})},loadSchemes:function(){var t=this;return(0,s.FormsLoad)(this.query).then((function(e){if(e.IsSucceed){var a=e.Data,n=a.Data,i=a.Page,o=a.TotalCount;t.flow_types=n,t.query.Page=i,t.query.TotalCount=o}t.tbLoading=!1}))},openPage:function(t,e,a){e=null!==e&&void 0!==e?e:{},a=null!==a&&void 0!==a?a:{},this.$router.push({name:t,params:(0,i.default)({},e),query:(0,i.default)({pg_redirect:this.$route.name},a)})}}}},"4a7e":function(t,e,a){"use strict";a.r(e);var n=a("433f"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"52b8":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 pd-16"},[a("div",{staticClass:"full-h-flex"},[a("el-card",{staticClass:"auto-h",attrs:{shadow:"always"}},[a("div",{staticClass:"full-h-flex"},[a("div",{staticClass:"flex-scroll-box-v"},[a("div",{staticClass:"my-grid",staticStyle:{padding:"0px 20px"}},t._l(t.flow_types,(function(e){return a("el-card",{key:e.Id,staticStyle:{width:"286px"},attrs:{shadow:"hover","body-style":{padding:"0"}}},[a("div",{staticStyle:{position:"relative",margin:"3em 16px"}},[a("el-avatar",{staticClass:"hav"},[t._v(" "+t._s(e.Name?e.Name.substr(0,1):"")+" ")]),a("span",{staticStyle:{display:"inline-block",position:"absolute",bottom:"16px",left:"84px","font-size":"1.28em"}},[t._v(t._s(e.Name))])],1),a("div",{staticClass:"bottom",on:{click:function(a){return t.create(e)}}},[a("div",[a("i",{staticClass:"el-icon-edit"}),t._v(" 发起审批")])])])})),1)])])])],1)])},i=[]},9973:function(t,e,a){}}]);