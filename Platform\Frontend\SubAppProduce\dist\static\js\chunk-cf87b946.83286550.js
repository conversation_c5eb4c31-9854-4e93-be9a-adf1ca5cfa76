(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-cf87b946"],{"003d":function(e,t,i){},"03d5":function(e,t,i){"use strict";i("2d88")},"06b4":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("5530")),n=a(i("c14f")),o=a(i("1da1"));i("4de4"),i("7db0"),i("d81d"),i("e9f5"),i("910d"),i("f665"),i("ab43"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7");var s=i("9002"),l=i("93aa"),u=a(i("6612")),c=i("ed08");t.default={props:{bigTypeData:{type:Number,default:1},formData:{type:Object,default:function(){}},projectList:{type:Array,required:!0,default:function(){return[]}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{OrderCode:"",CategoryId:"",RawName:"",Supplier:"",SupplierName:"",SysProjectId:"",Thick:"",Width:"",Length:"",Material:"",Raw_FullName:"",IsFinished:"0"},selectRow:null,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[],BigType:1}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.PurchaseSubId===t.PurchaseSubId}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)},bigTypeData:{handler:function(e,t){this.BigType=e},immediate:!0}},created:function(){this.getCategoryTreeList()},mounted:function(){this.getConfig();var e=this.formData,t=e.Supplier,i=e.SysProjectId;t&&i&&(this.form.Supplier=t,this.form.SysProjectId=i)},methods:{getConfig:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PROAddRawPurchase");case 1:e.columns=t.v,e.columnsOption(1),e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChange:function(e){var t=this;return(0,o.default)((0,n.default)().m((function i(){return(0,n.default)().w((function(i){while(1)switch(i.n){case 0:return t.BigType=e,t.resetForm("form"),i.n=1,t.columnsOption();case 1:return i.a(2)}}),i)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.BigType,e.currentColumns=JSON.parse(JSON.stringify(e.columns));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;(0,l.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$nextTick((function(i){var a;null===(a=e.$refs)||void 0===a||a.treeSelect.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawProcurementDetails)((0,r.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.InStoreCount=e.AvailableCount,e.WareWeight=(0,u.default)(e.WareWeight||0).divide(1e3).format("0.[00000]"),e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},addJoinedIds:function(){},addToList:function(){var e=(0,c.deepClone)(this.multipleSelection);this.addJoinedIds(),this.$emit("getAddList",e),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e}))),this.addJoinedIds(),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},"0b513":function(e,t,i){"use strict";i.r(t);var a=i("346d"),r=i("d111");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("501a");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"069062c4",null);t["default"]=s.exports},"129c":function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"contentBox"},[i("div",{staticClass:"cs-main"},[i("div",{staticClass:"cs-left"},[i("div",{staticClass:"cs-left__text"},[e._v("物料类别")]),i("div",{staticClass:"cs-left__tree"},[i("tree-detail",{ref:"tree",staticStyle:{"margin-top":"20px"},attrs:{icon:"icon-folder",loading:e.treeLoading,"tree-data":e.treeData,"show-detail":"","expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick}})],1)]),i("div",{staticClass:"cs-right"},[i("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80"}},[i("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[i("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1),i("el-form-item",{attrs:{label:"原料名称",prop:"Materiel_Name"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Materiel_Name,callback:function(t){e.$set(e.form,"Materiel_Name",t)},expression:"form.Materiel_Name"}})],1),i("el-form-item",{attrs:{label:"唯一编码",prop:"Materiel_Code"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Materiel_Code,callback:function(t){e.$set(e.form,"Materiel_Code",t)},expression:"form.Materiel_Code"}})],1),i("el-form-item",{attrs:{label:"规格(厚度mm)",prop:"Spec"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1),i("el-form-item",{attrs:{label:"材质",prop:"Material"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material",t)},expression:"form.Material"}})],1),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("查询")]),i("el-button",{staticStyle:{"margin-left":"10px"},on:{click:e.searchReset}},[e._v("重置")])],1),i("div",{staticClass:"tb-wrapper"},[i("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?i("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[i("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):i("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(e){return[i("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:e.Code,title:e.Display_Name,"min-width":e.Width}})]}))],2)],1),i("div",{staticClass:"cs-footer"},[i("div",{staticClass:"data-info"},[i("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),i("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)],1)]),i("div",{staticClass:"button"},[i("el-button",{on:{click:e.handleClose}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)])},r=[]},1338:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a15b"),i("14d9"),i("13d5"),i("e9f5"),i("9485"),i("d3b7");var a=i("2245");t.default={data:function(){return{activeName:"1",radioSelect:null,btnLoading:!1,form:{},list:[],tableData:[],specificationUsage:0}},watch:{specificationUsage:function(e,t){1===e&&(this.activeName="2")}},methods:{getOption:function(e){var t=this;this.specificationUsage=e.SpecificationUsage,e&&(this.list=e.RcptRawParams.split("*")),(0,a.GetRawStandardsList)({rawId:e.RawId}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleRadioChange:function(e,t){e.stopPropagation(),this.currentRow=Object.assign({},t)},submit:function(){var e=this;if("1"===this.activeName){var t=!0,i=this.list.reduce((function(i,a){if(e.form[a])return i.push(e.form[a]),i;t=!1}),[]);if(!t)return void this.$message({message:"输入数据不能为0",type:"warning"});this.$emit("standard",{type:1,val:i.join("*")})}else{if(!this.currentRow)return void this.$message({message:"请选择规格",type:"warning"});this.$emit("standard",{type:2,val:this.currentRow})}this.handleClose()},handleClose:function(){this.$emit("close")}}}},"14fb":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("5530")),n=a(i("c14f")),o=a(i("1da1"));i("99af"),i("4de4"),i("7db0"),i("d81d"),i("e9f5"),i("910d"),i("f665"),i("ab43"),i("d3b7"),i("ac1f"),i("3ca3"),i("841c"),i("ddb0");var s=i("ed08"),l=i("5480"),u=i("93aa"),c=a(i("1463")),d=a(i("333d")),f=i("c685"),h=i("8378");t.default={components:{TreeDetail:c.default,Pagination:d.default},props:{isSingle:{type:Boolean,default:!1},isPurchasing:{type:Boolean,default:!0},isCustomer:{type:Boolean,default:!0},isPurchase:{type:Boolean,default:!0},isManual:{type:Boolean,default:!0},projectId:{type:String,default:""},projectList:{type:Array,default:function(){return[]}}},data:function(){return{tablePageSize:f.tablePageSize,treeLoading:!1,expandedKey:"",versionCode:"",treeData:[],Category_Id:"",catalogDetail:{},form:{Materiel_Name:"",Materiel_Code:"",Spec:"",Material:"",Raw_FullName:""},selectRow:null,tbLoading:!1,saveLoading:!1,queryInfo:{Page:1,PageSize:20,ParameterJson:[]},total:0,versionList:[],columns:[],currentColumns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{showVersion:function(){return!this.isPurchasing},isSteelPlate:function(){var e,t=this,i=this.catalogDetail.Pid?null===(e=this.treeData.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";return"板材"===this.catalogDetail.Name||"板材"===i}},watch:{isSteelPlate:function(){this.getCurrentColumn()},showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getVersionList();case 1:return t.n=2,e.getConfig();case 2:return t.n=3,e.getTreeList();case 3:e.getCurrentColumn(),e.search=(0,s.debounce)(e.fetchData,800,!0);case 4:return t.a(2)}}),t)})))()},methods:{getCurrentColumn:function(){var e,t=this,i=this.catalogDetail.Pid?null===(e=this.treeData.find((function(e){return e.Id===t.catalogDetail.Pid})))||void 0===e?void 0:e.Label:"";this.currentColumns=this.columns.filter((function(e){return"板材"===t.catalogDetail.Name||"板材"===i?"Spec"!==e.Code:"Thick"!==e.Code}))},versionChange:function(){this.fetchData(1)},getVersionList:function(){var e=this,t=this.projectList.find((function(t){return t.Id===e.projectId})),i="";return t&&(i=t.Sys_Project_Id),new Promise((function(t,a){(0,h.GetList)({sysProjectId:i,inStoryType:e.isManual?3:2}).then((function(i){if(i.IsSucceed){e.versionList=i.Data;var a=e.versionList.find((function(e){return e.Is_System}));a&&(e.versionCode=a.Id)}else e.$message({message:i.Message,type:"error"});t()}))}))},getTreeList:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,t.n=1,(0,u.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeData=[{Label:"全部",Id:"all",Data:{Category:{Name:"",Pid:""}}}].concat(t.Data),e.expandedKey=e.treeData[0].Id,e.Category_Id=e.treeData[0].Id,e.catalogDetail=e.treeData[0].Data.Category,e.fetchData(1)):e.$message({message:t.Message,type:"error"}),e.treeLoading=!1})).catch((function(){e.treeLoading=!1}));case 1:return t.a(2)}}),t)})))()},handleNodeClick:function(e){this.Category_Id=e.Id,this.catalogDetail=e.Data.Category,this.queryInfo.Page=1,this.fetchData()},setRow:function(e){},getConfig:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PRORawList");case 1:e.columns=t.v;case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},searchReset:function(){this.$refs["form"].resetFields(),this.fetchData(1)},fetchData:function(e){var t=this;return(0,o.default)((0,n.default)().m((function i(){return(0,n.default)().w((function(i){while(1)switch(i.n){case 0:e&&(t.queryInfo.Page=e),t.tbLoading=!0,(0,u.GetRawPageList)((0,r.default)((0,r.default)({Category_Id:"all"===t.Category_Id?"":t.Category_Id},t.form),t.queryInfo)).then((function(e){e.IsSucceed?(t.fTable=e.Data.Data.filter((function(e){return e.Is_Enabled})),t.total=e.Data.TotalCount,t.multipleSelection=[]):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}));case 1:return i.a(2)}}),i)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.BigType=e.Big_Type,e.RawCode=e.Code,e.CategoryId=e.Category_Id,e.RawName=e.Name,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")},changePage:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=20),Promise.all([e.fetchData()]).then((function(e){}));case 1:return t.a(2)}}),t)})))()}}}},"1ac5":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("2909")),n=a(i("c14f")),o=a(i("1da1"));i("4de4"),i("7db0"),i("caad"),i("d81d"),i("14d9"),i("a434"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("ab43"),i("e9c4"),i("a9e3"),i("b680"),i("b64b"),i("d3b7"),i("2532"),i("159b");var s=i("ed08"),l=i("30c8"),u=i("8fea");t.default={props:{isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1},isReplacePurchase:{type:Boolean,default:!1},formStatus:{type:Number,default:0},checkTypeList:{type:Array,required:!0,default:function(){return[]}},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{tbLoading:!1,multipleSelection:[],rootColumns:[],columns:[],tbData:[],BigType:1,renderComponent:!0,num:1,itemKey:""}},computed:{isReturn:function(){return 88===this.formStatus}},watch:{bigTypeData:{handler:function(e,t){this.BigType=e,this.columnsOption()},immediate:!1}},inject:["checkDuplicate"],created:function(){},mounted:function(){},methods:{rowStyle:function(e){var t=e.row;if(t.WaitInStoreWeight<0)return{"background-color":"#F2AAB3!important"}},changeKeyInfo:function(e,t){var i=this.tbData.map((function(t){return t[e]})),a=(0,s.uniqueArr)(i);1===a.length?this.$emit("changeKeyInfo",{key:e,val:t}):this.$emit("changeKeyInfo",{key:e,val:""})},changeFormInfo:function(e,t){var i=this;this.tbData.forEach((function(a){i.$set(a,e,t)}))},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},init:function(e){e.forEach((function(e){e.Style=e.Style?JSON.parse(e.Style):"","ReturnSupplierName"===e.Code&&(e.Is_Edit=!1)})),this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.itemKey=Math.random(),e.columns=JSON.parse(JSON.stringify(e.rootColumns));case 1:return t.a(2)}}),t)})))()},getCheckList:function(e,t){var i=this,a=[];a=this.checkDuplicate()?["PurchaseNo","ProjectName","Project_Code","SupplierName","RawNameFull","CategoryName","RawName","RawCode","Material","Spec","Actual_Spec","TaxUnitPrice","Tax_Rate","Warehouse_Location","FurnaceBatchNo"]:[];var r="Width",n="Length";this.checkTypeList.map((function(o){o.BigType===e&&(o.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),o.checkList=o.checkList.map((function(e){return e.Code})),o.checkSameList=t.filter((function(e){return e.Is_Display&&a.includes(e.Code)})),o.checkSameList=o.checkSameList.map((function(e){return e.Code})),i.checkDuplicate&&(o.checkSameList.unshift("PurchaseSubId"),2===e?o.checkSameList.push(n):1===e&&(o.checkSameList.push(r),o.checkSameList.push(n))),o.remarkList=t.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),o.remarkList=o.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}})))}))},getAllColumnsOption:function(){var e=this,t=[1,2,3],i=JSON.parse(JSON.stringify(this.rootColumns));t.forEach((function(t){e.getCheckList(t,i)}))},tbfetchData:function(){this.tbData=[]},handleCopy:function(e,t){var i=JSON.parse(JSON.stringify(e));i.Sub_Id&&(i.Sub_Id=""),delete i._X_ROW_KEY,this.$emit("updateTb",[i],"copy"),this.tbData.splice(t+1,0,i)},setRowFullName:function(e){e.Raw_FullName=(0,l.getMaterialName)(e)},lengthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},widthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,i=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.map((function(e){return e.Spec=1===e.BigType?e.Thick:e.Spec,e.Actual_Spec=e.Actual_Spec||(1===e.BigType?e.Thick:e.Spec),e.RawNameFull=(0,l.getMaterialName)(e),e.Specific_Gravity&&3!==e.BigType||!e.PurchaseCount||(e.InStoreWeight=(e.PurchaseWeight||0)/u.WEIGHT_CONVERSION,e.InStoreWeightKG=e.InStoreWeight*u.WEIGHT_CONVERSION),a&&(e.InStoreCount=e.PurchaseCount||0,e.InStoreWeight=e.PurchaseWeight||0,e.InStoreWeightKG=e.InStoreWeight*u.WEIGHT_CONVERSION),i.checkWeight(e),e.rowIndex="",i.voucherTaxAllPriceChange(e),e}));(t=this.tbData).push.apply(t,(0,r.default)(n))},checkWeight:function(e){0!==e.Specific_Gravity&&!e.Specific_Gravity||3===e.BigType||(1!==e.BigType&&2!==e.BigType||0!==e.Specific_Gravity&&0!==e.Length?1===e.BigType?"花纹板"===e.CategoryName?0===e.Length||0===e.Width?e.InStoreWeightKG=0:e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":0===e.Thick||0===e.Width?e.InStoreWeightKG=0:e.Thick&&e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Thick*e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":2===e.BigType&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":e.InStoreWeightKG=0,e.InStoreWeight=e.InStoreWeightKG||0===e.InStoreWeightKG?Number((e.InStoreWeightKG/u.WEIGHT_CONVERSION).toFixed(u.WEIGHT_DECIMAL)):0),this.countTaxUnitPrice(e,"InStoreCount_Width_Length")},countTaxUnitPrice:function(e,t){"InStoreWeight"===t?e.InStoreWeightKG=e.InStoreWeight||0===e.InStoreWeight?Number(e.InStoreWeight*u.WEIGHT_CONVERSION):0:"Pound_Weight"===t?e.Pound_Weight_KG=e.Pound_Weight||0===e.Pound_Weight?Number(e.Pound_Weight*u.WEIGHT_CONVERSION):0:"Voucher_Weight"===t&&(e.Voucher_Weight_KG=e.Voucher_Weight||0===e.Voucher_Weight?Number(e.Voucher_Weight*u.WEIGHT_CONVERSION):0),"TaxUnitPrice"===t&&(e.TaxUnitPriceKG=Number((e.TaxUnitPrice/u.WEIGHT_CONVERSION).toFixed(u.UNIT_PRICE_KG_DECIMAL))),"TaxUnitPrice"!==t&&"InStoreWeight"!==t&&"InStoreCount_Width_Length"!==t||(99===e.BigType?0===e.InStoreCount||0===e.TaxUnitPrice?e.Tax_All_Price=0:e.InStoreCount&&e.TaxUnitPrice?e.Tax_All_Price=e.InStoreCount*e.TaxUnitPrice:e.Tax_All_Price="":0===e.InStoreWeightKG||0===e.TaxUnitPriceKG?e.Tax_All_Price=0:e.InStoreWeightKG&&e.TaxUnitPriceKG?e.Tax_All_Price=e.InStoreWeightKG*e.TaxUnitPriceKG:e.Tax_All_Price=""),this.taxAllPriceChange(e),this.voucherTaxAllPriceChange(e),this.$emit("updateRow")},taxAllPriceChange:function(e){e.NoTaxAllPrice=(0,l.getNoTaxAllPrice)(e)},voucherTaxAllPriceChange:function(e){e.VoucherTaxAllPrice=(0,l.getVoucherTaxAllPrice)(e),e.VoucherNoTaxAllPrice=(0,l.getVoucherNoTaxAllPrice)(e)},supplierNameChange:function(e,t){var i=this.supplierList.find((function(t){return t.Id===e.value}));t.ReturnSupplierName=i?i.Name:""},outStoreChange:function(e){var t=e.Specific_Gravity,i=0;i=t?1===e.BigType?"花纹板"===e.CategoryName?Number(e.Width*e.Length*t*e.OutStoreCount):Number(e.Width*e.Length*Number(e.Spec)*t*e.OutStoreCount):2===e.BigType?Number(e.Length*t*e.OutStoreCount):Number(e.PerWeight*e.OutStoreCount):e.PerWeight*e.OutStoreCount,e.OutStoreWeight=(i/u.WEIGHT_CONVERSION).toFixed(u.WEIGHT_DECIMAL)/1,e.ReturnVoucherWeight=(e.PerVoucherWeight*e.OutStoreCount/u.WEIGHT_CONVERSION).toFixed(u.WEIGHT_DECIMAL)/1,this.$emit("updateRow")}}}},"28b0":function(e,t,i){"use strict";i.r(t);var a=i("bbd2"),r=i("edce");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("7fc7");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"370349c8",null);t["default"]=s.exports},"29c6":function(e,t,i){"use strict";i.r(t);var a=i("47d6"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"2d88":function(e,t,i){},3340:function(e,t,i){"use strict";i("d56b")},"346d":function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("vxe-table",{key:2,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"500","show-overflow":"","empty-text":"无重复项！","row-style":e.rowStyle,"auto-resize":!0,size:"medium",data:e.resultArray,resizable:"","scroll-y":{enabled:!0},"tooltip-config":{enterable:!0}}},[i("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e._l(e.columns,(function(t){return[i("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.row;return[i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]}}],null,!0)})]}))],2),i("div",{staticClass:"footer"},[i("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("不合并手动处理")]),i("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.submit}},[e._v("合并后提交")])],1)],1)},r=[]},"3eb8":function(e,t,i){"use strict";i("50c7")},"47d6":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("4de4"),i("7db0"),i("caad"),i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("e9f5"),i("910d"),i("f665"),i("ab43"),i("d3b7"),i("2532");var r=a(i("2909")),n=a(i("c14f")),o=a(i("1da1")),s=i("e144");t.default={props:{isReplacePurchase:{type:Boolean,default:!1},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}},checkTypeList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},Is_Component:"",value:"",options:[],list:[{id:(0,s.v4)(),val:void 0,key:""}],SupplierName:"",PartyUnitName:"",ProjectName:""}},mounted:function(){return(0,o.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{optionChange:function(e,t){var i;if("supplierArray"===t)this.SupplierName=null===(i=this.supplierList.find((function(t){return t.Id===e})))||void 0===i?void 0:i.Name;else if("partyUnitArray"===t){var a;this.PartyUnitName=null===(a=this.partyUnitList.find((function(t){return t.Id===e})))||void 0===a?void 0:a.Name}else if("projectArray"===t){var r;this.ProjectName=null===(r=this.projectList.find((function(t){return t.Sys_Project_Id===e})))||void 0===r?void 0:r.Short_Name}},handleAdd:function(){this.list.push({id:(0,s.v4)(),val:void 0,key:""})},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){var i,a,r,o;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.btnLoading=!0,i={},a=0;case 1:if(!(a<e.list.length)){t.n=4;break}if(r=e.list[a],r.val){t.n=2;break}if(o=!0,"Width"===r.key||"Length"===r.key||"InStoreCount"===r.key||"InStoreWeight"===r.key?0===r.val?e.$message({message:"值不能为0",type:"warning"}):e.$message({message:"值不能为空",type:"warning"}):"Tax_Rate"===r.key||"TaxUnitPrice"===r.key?o=!1:e.$message({message:"值不能为空",type:"warning"}),e.btnLoading=!1,!o){t.n=2;break}return t.a(2);case 2:i[r.key]=r.val;case 3:a++,t.n=1;break;case 4:e.list.map((function(t){"Supplier"===t.key?t.name=e.SupplierName:"PartyUnit"===t.key?t.name=e.PartyUnitName:"SysProjectId"===t.key&&(t.name=e.ProjectName)})),e.$emit("batchEditor",e.list),e.btnLoading=!1;case 5:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(i){return(!t.list.map((function(e){return e.key})).includes(i.key)||i.key===e)&&i.label}))},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},init:function(e,t,i){var a;if(this.selectList=e,1===t)this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(a=this.options).push.apply(a,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===i?this.options.push({key:"Width",label:"宽度(mm)",type:"number"},{key:"Length",label:"长度(mm)",type:"number"},{key:"InStoreCount",label:"入库数量",type:"number"},{key:"Pound_Weight",label:"磅重(t)",type:"number"}):2===i?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==i&&4!==i||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}));else if(2===t){var n;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(n=this.options).push.apply(n,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===i?this.options.push({key:"Length",label:"长度(mm)",type:"number"},{key:"InStoreCount",label:"入库数量",type:"number"},{key:"Pound_Weight",label:"磅重(t)",type:"number"}):2===i?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==i&&4!==i||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}else if(3===t){var o;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(o=this.options).push.apply(o,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===i?this.options.push({key:"InStoreCount",label:"入库数量",type:"number"},{key:"Pound_Weight",label:"磅重(t)",type:"number"}):2===i?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==i&&4!==i||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}else{var s;this.options=[{key:"FurnaceBatchNo",label:"炉批号 ",type:"string"}],(s=this.options).push.apply(s,(0,r.default)(this.checkTypeList.find((function(e){return 1===e.BigType})).remarkList)),1===i?this.options.push({key:"InStoreCount",label:"入库数量",type:"number"}):2===i?(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})):3!==i&&4!==i||(this.options.push({key:"Material",label:"材质",type:"string"},{key:"TaxUnitPrice",label:"含税单价(元)",type:"number"},{key:"Tax_Rate",label:"税率",type:"number"}),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"}))}}}}},"501a":function(e,t,i){"use strict";i("ace8")},"50c7":function(e,t,i){},"525f":function(e,t,i){"use strict";i.r(t);var a=i("82d9"),r=i("fdfa");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("d3d1");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"52d847c6",null);t["default"]=s.exports},"52ad":function(e,t,i){"use strict";i.r(t);var a=i("ec55"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"56e7":function(e,t,i){"use strict";i("003d")},"58ab":function(e,t,i){"use strict";i.r(t);var a=i("1ac5"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"58cc":function(e,t,i){"use strict";i.r(t);var a=i("cc0a"),r=i("afac");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("3eb8");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"2988df08",null);t["default"]=s.exports},"601e":function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"contentBox"},[i("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[i("el-row",[[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],i("el-col",{attrs:{span:2}},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),i("div",{staticClass:"tb-wrapper"},[i("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?i("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[i("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):i("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[i("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(i){var a=i.row;return[e._v(" "+e._s(0===a[t.Code]?"按需使用":1===a[t.Code]?"使用标准规格":2===a[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(i){var a=i.row;return[e._v(" "+e._s(e._f("displayValue")(a[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),i("div",{staticClass:"button"},[i("el-button",{on:{click:e.handleClose}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},r=[]},"626c":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("2909")),n=a(i("c14f")),o=a(i("1da1")),s=a(i("5530"));i("99af"),i("4de4"),i("7db0"),i("c740"),i("caad"),i("a15b"),i("d81d"),i("14d9"),i("13d5"),i("fb6a"),i("a434"),i("b0c0"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("ab43"),i("9485"),i("e9c4"),i("a9e3"),i("b680"),i("b64b"),i("d3b7"),i("ac1f"),i("25f0"),i("2532"),i("38cf"),i("841c"),i("498a"),i("159b");var l=i("ed08"),u=a(i("9395")),c=a(i("d37f")),d=a(i("6f65")),f=a(i("fd11")),h=a(i("8d38")),p=a(i("525f")),m=a(i("775a")),g=a(i("879a")),y=a(i("28b0")),b=a(i("92f08")),v=a(i("c7ab")),_=a(i("a657")),S=a(i("b76a")),C=i("93aa"),x=i("6186"),I=i("3166"),P=i("5480"),w=i("cf45"),T=a(i("0b513")),N=i("2f62"),k=i("30c8"),R=i("e144"),W=a(i("5d4b")),D=a(i("29d9")),L=a(i("b5b0"));t.default={components:{SelectLocation:L.default,SelectWarehouse:D.default,SelectMaterialStoreType:W.default,Repeat:T.default,RawManualTb:d.default,AddPurchaseList:p.default,PurchaseTb:u.default,CustomerTb:c.default,BatchEdit:f.default,ImportFile:g.default,Warehouse:y.default,Standard:b.default,AddList:h.default,AddRawMaterialList:m.default,OSSUpload:v.default,DynamicTableFields:_.default,draggable:S.default},props:{pageType:{type:Number,default:void 0}},data:function(){return{returning:!1,isDraft:!0,tbDataInfo:[],isRetract:!1,tbLoading:!1,filterVisible:!1,statisticTime:"",multipleSelection:[],ProjectList:[],PartyUnitList:[],SupplierList:[],formStatus:+this.$route.query.status||"",form:{InStoreNo:"",InStoreType:+this.$route.query.type||1,InStoreDate:this.getDate(),Delivery_No:"",Purchase_Contract_No:"",CarNumber:"",Is_Replace_Purchase:!1,Driver:"",DriverMobile:"",Car_Pound_Weight:null,Car_Pound_Weight_KG:null,Car_Voucher_Weight:null,Car_Voucher_Weight_KG:null,Remark:"",ProjectName:"",ProjectId:"",SysProjectId:"",PartyUnitName:"",PartyUnit:"",SupplierName:"",Supplier:"",Attachment:"",Status:null},rules:{InStoreType:[{required:!0,message:"请选择",trigger:"change"}],InStoreDate:[{required:!0,message:"请选择日期",trigger:"change"}],PartyUnit:[{required:!0,message:"请选择",trigger:"change"}],Is_Replace_Purchase:[{required:!0,message:"请选择",trigger:"change"}],ProjectId:[{required:!0,message:"请选择",trigger:"change"}],Supplier:[{required:!0,message:"请选择",trigger:"change"}]},searchForm:{RawNameFull:"",RawName:"",Thick:"",Spec:"",Material:"",SysProjectId:"",CategoryId:"",Length:"",Width:"",Supplier:"",PartyUnit:"",WarehouseId:"",Location_Id:""},treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},currentComponent:"",title:"",dWidth:"60%",submitLoading:!1,saveLoading:!1,search:function(){return{}},openAddList:!1,dialogVisible:!1,dialogRepeatVisible:!1,BigType:1,fileListData:[],fileListArr:[],searchNum:1,rootTableData:[],tableData:[],typeNumber1:0,typeNumber2:0,typeNumber3:0,typeNumber4:0,popoverVisible:!1,rootColumns:[],tableConfigCode:"",manualHideTableColumns:[],RawReceiptTypeList:[],checkTypeList:[{BigType:1,checkList:[],checkSameList:[],remarkList:[]},{BigType:2,checkList:[],checkSameList:[],remarkList:[]},{BigType:3,checkList:[],checkSameList:[],remarkList:[]},{BigType:99,checkList:[],checkSameList:[],remarkList:[]}],statisticsData:{OutStoreCount:0,InStoreCountTotal:0,InStoreWeightTotal:0,PoundWeightTotal:0,Tax_All_Price:0,VoucherTaxAllPrice:0,VoucherWeightTotal:0}}},computed:(0,s.default)({isView:function(){return 3===this.pageType},isAdd:function(){return 1===this.pageType},isEdit:function(){return 2===this.pageType},isPurchase:function(){return 1==this.form.InStoreType},isCustomer:function(){return 2==this.form.InStoreType},isManual:function(){return 3==this.form.InStoreType},isReturn:function(){return 88===this.formStatus},currentTbComponent:function(e){e.isPurchase,e.isCustomer,e.isManual;return u.default}},(0,N.mapGetters)("factoryInfo",["checkDuplicate"])),created:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getFactoryInfo();case 1:return t.n=2,e.getOMALatestStatisticTime();case 2:return t.n=3,e.getTableColumns();case 3:return t.n=4,e.getRawReceiptTypeList();case 4:return t.n=5,e.getSuppliers();case 5:return t.n=6,e.getPartyAs();case 6:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.search=(0,l.debounce)(e.fetchData,800,!0),t.n=1,e.getProject();case 1:if(e.isAdd){t.n=2;break}return t.n=2,e.getInfo();case 2:e.isReturn||e.getCategoryTreeList();case 3:return t.a(2)}}),t)})))()},provide:function(){return{formData:this.form,checkDuplicate:this.getDuplicate}},methods:{changeKeyInfo:function(e){var t=e.key,i=e.val;this.form[t]=i},changeFormKey:function(e){this.$refs["table"].changeFormInfo(e,this.form[e])},getDuplicate:function(){return this.checkDuplicate},getFactoryInfo:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("factoryInfo/getWorkshop");case 1:return t.a(2)}}),t)})))()},handleRetract:function(){this.isRetract=!this.isRetract},handleSearch:function(){var e=this;this.tableData=JSON.parse(JSON.stringify(this.rootTableData)),this.searchForm.RawNameFull&&(this.tableData=this.tableData.filter((function(t){return(0,k.isMaterialInclude)(t.Raw_FullName,e.searchForm.RawNameFull)}))),this.searchForm.RawName&&(this.tableData=this.tableData.filter((function(t){return t.RawName===e.searchForm.RawName}))),this.searchForm.Thick&&(this.tableData=this.tableData.filter((function(t){return Number(t.Thick)===Number(e.searchForm.Thick)}))),this.searchForm.Spec&&(this.tableData=this.tableData.filter((function(t){return t.Spec==e.searchForm.Spec}))),this.searchForm.Material&&(this.tableData=this.tableData.filter((function(t){return t.Material===e.searchForm.Material}))),this.searchForm.SysProjectId&&(this.tableData=this.tableData.filter((function(t){return t.SysProjectId===e.searchForm.SysProjectId}))),this.searchForm.CategoryId&&(this.tableData=this.tableData.filter((function(t){return t.CategoryId===e.searchForm.CategoryId}))),this.searchForm.Length&&(this.tableData=this.tableData.filter((function(t){return Number(t.Length)===Number(e.searchForm.Length)}))),this.searchForm.Width&&(this.tableData=this.tableData.filter((function(t){return Number(t.Width)===Number(e.searchForm.Width)}))),this.searchForm.Supplier&&(this.tableData=this.tableData.filter((function(t){return t.Supplier===e.searchForm.Supplier}))),this.searchForm.PartyUnit&&(this.tableData=this.tableData.filter((function(t){return t.PartyUnit===e.searchForm.PartyUnit}))),this.searchForm.WarehouseId&&(this.tableData=this.tableData.filter((function(t){return t.WarehouseId===e.searchForm.WarehouseId}))),this.searchForm.LocationId&&(this.tableData=this.tableData.filter((function(t){return t.LocationId===e.searchForm.LocationId}))),this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.tableData))},handleReset:function(){this.searchForm.RawNameFull="",this.searchForm.RawName="",this.searchForm.Thick="",this.searchForm.Spec="",this.searchForm.Material="",this.searchForm.SysProjectId="",this.searchForm.CategoryId="",this.searchForm.Length="",this.searchForm.Width="",this.searchForm.Supplier="",this.searchForm.PartyUnit="",this.searchForm.WarehouseId="",this.searchForm.LocationId="",this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.rootTableData)),this.tableData=JSON.parse(JSON.stringify(this.rootTableData))},getTableColumns:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){var i,a;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:if(1!=e.form.InStoreType){t.n=2;break}return t.n=1,(0,P.getTableConfig)("PRORawPurchaseList",e.BigType);case 1:e.rootColumns=t.v,e.tableConfigCode="PRORawPurchaseList",t.n=6;break;case 2:if(2!=e.form.InStoreType){t.n=4;break}return t.n=3,(0,P.getTableConfig)("PRORawCustomerList",e.BigType);case 3:e.rootColumns=t.v,e.tableConfigCode="PRORawCustomerList",t.n=6;break;case 4:if(3!=e.form.InStoreType){t.n=6;break}return t.n=5,(0,P.getTableConfig)("PRORawManualList",e.BigType);case 5:e.rootColumns=t.v,e.tableConfigCode="PRORawManualList";case 6:88===+e.$route.query.status&&(a=[{Code:"AvailableCount",Display_Name:"可退数量",Width:150,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"OutStoreCount",Display_Name:"退货数量",Width:120,Is_Edit:!0,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"OutStoreWeight",Display_Name:"退货理重",Width:120,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"ReturnSupplierName",Display_Name:"退货供应商",Width:120,Is_Edit:!0,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"ReturnVoucherWeight",Display_Name:"退货凭证重",Width:120,Frozen_Dirction:"right",Is_Frozen:!0},{Code:"ReturnPoundWeight",Display_Name:"退货磅重",Width:120,Frozen_Dirction:"right",Is_Edit:!0,Is_Frozen:!0}],e.rootColumns.forEach((function(e){e.Is_Edit=!1})),(i=e.rootColumns).push.apply(i,a)),e.$refs["table"].init(e.rootColumns);case 7:return t.a(2)}}),t)})))()},getManualHideTableColumns:function(){1===this.BigType?this.manualHideTableColumns=this.rootColumns.filter((function(e){return["Spec"].includes(e.Code)})):2===this.BigType?this.manualHideTableColumns=this.rootColumns.filter((function(e){return["Thick","Actual_Thick","Width"].includes(e.Code)})):3===this.BigType?this.manualHideTableColumns=this.rootColumns.filter((function(e){return["Thick","Actual_Thick","Width","Length"].includes(e.Code)})):99===this.BigType&&(this.manualHideTableColumns=this.rootColumns.filter((function(e){return["Thick","Actual_Thick","Width","Length","InStoreWeight","Pound_Weight","Voucher_Weight","FurnaceBatchNo"].includes(e.Code)})))},handleSaveTbSet:function(e){},updateColumn:function(e){this.getTableColumns()},getRawReceiptTypeList:function(){var e=this;(0,w.getDictionary)("RawReceiptType").then((function(t){e.RawReceiptTypeList=t.filter((function(e){return e.Is_Enabled}))}))},radioChange:function(e){this.BigType=e,this.multipleSelection=[],this.handleReset(),this.getManualHideTableColumns()},uploadSuccess:function(e,t,i){this.fileListArr=JSON.parse(JSON.stringify(i))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},handlePreview:function(e){return(0,o.default)((0,n.default)().m((function t(){var i;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:if(i="",!e.response||!e.response.encryptionUrl){t.n=1;break}i=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,x.GetOssUrl)({url:e.encryptionUrl});case 2:i=t.v,i=i.Data;case 3:window.open(i);case 4:return t.a(2)}}),t)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})},fetchData:function(){},getInfo:function(){var e=this,t=1==this.form.InStoreType?C.PurchaseInStoreDetail:2==this.form.InStoreType?C.PartAInStoreDetail:3==this.form.InStoreType?C.ManualInStoreDetail:C.SurplusInStoreDetail;88===this.formStatus&&(t=C.GetRawDetailByReceipt),t({inStoreNo:this.$route.query.id}).then((function(t){if(t.IsSucceed){var i=t.Data.Receipt,a=t.Data.Sub,r=i.InStoreDate,n=i.CarNumber,o=i.Driver,s=i.DriverMobile,l=i.Car_Pound_Weight,u=i.Car_Voucher_Weight,c=i.Remark,d=i.ProjectId,f=i.SysProjectId,h=i.PartyUnit,p=i.Supplier,m=i.Attachment,g=i.Status,y=i.Delivery_No,b=i.Purchase_Contract_No,v=i.Is_Replace_Purchase;if(e.form.InStoreDate=e.getDate(new Date(r)),e.form.CarNumber=n,e.form.Driver=o,e.form.DriverMobile=s,e.form.Car_Pound_Weight_KG=l,e.form.Car_Pound_Weight=l?Number((l/1e3).toFixed(3)):l,e.form.Car_Voucher_Weight_KG=u,e.form.Car_Voucher_Weight=u?Number((u/1e3).toFixed(3)):u,e.form.Remark=c,e.form.ProjectId=d,e.form.SysProjectId=f,e.form.ProjectName=f?e.ProjectList.find((function(e){return e.Sys_Project_Id===f})).Short_Name:"",e.form.PartyUnit=h,e.form.PartyUnitName=h?e.PartyUnitList.find((function(e){return e.Id===h})).Name:"",e.form.Supplier=p,e.form.SupplierName=p?e.SupplierList.find((function(e){return e.Id===p})).Name:"",e.form.Status=g,e.form.Delivery_No=y,e.form.Purchase_Contract_No=b,e.form.Is_Replace_Purchase=v,m){e.form.Attachment=m;var _=m.split(",");_.forEach((function(t){var i=t.indexOf("?Expires=")>-1?t.substring(0,t.lastIndexOf("?Expires=")):t,a=decodeURI(i.substring(i.lastIndexOf("/")+1)),r={};r.name=a,r.url=i,r.encryptionUrl=i,e.fileListData.push(r),e.fileListArr.push(r)}))}var S=a.map((function(e,t){return e.index=(0,R.v4)(),e.Warehouse_Location=e.WarehouseName?e.WarehouseName+"/"+e.LocationName:"",e.InStoreWeightKG=e.InStoreWeight,e.Pound_Weight_KG=e.Pound_Weight,e.Voucher_Weight_KG=e.Voucher_Weight,e.TaxUnitPriceKG=e.TaxUnitPrice,e.NoTaxUnitPriceKG=e.NoTaxUnitPrice,e.InStoreWeight=Number((e.InStoreWeightKG/1e3).toFixed(5)),e.Pound_Weight=Number((e.Pound_Weight_KG/1e3).toFixed(3)),e.Voucher_Weight=Number((e.Voucher_Weight_KG/1e3).toFixed(3)),e.TaxUnitPrice=Number((1e3*e.TaxUnitPriceKG).toFixed(2)),e.NoTaxUnitPrice=Number((1e3*e.NoTaxUnitPriceKG).toFixed(2)),e.Width=3===e.BigType||2===e.BigType?0:e.Width,e.Length=3===e.BigType?0:e.Length,e}));e.$nextTick((function(t){e.$refs["table"].tbData=JSON.parse(JSON.stringify(S)),e.tableData=JSON.parse(JSON.stringify(S)),e.rootTableData=JSON.parse(JSON.stringify(S)),e.countStatistics()}))}else e.$message({message:t.Message,type:"error"})}))},getOMALatestStatisticTime:function(){var e=this;(0,C.GetOMALatestStatisticTime)().then((function(t){t.IsSucceed&&(e.statisticTime=t.Data)}))},getSuppliers:function(){var e=this;(0,C.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var i=[];for(var a in t.Data){var r={Id:a,Name:t.Data[a]};i.push(r)}e.SupplierList=i}else e.$message({message:t.Message,type:"error"})}))},getPartyAs:function(){var e=this;(0,C.GetPartyAs)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var i=[];for(var a in t.Data){var r={Id:a,Name:t.Data[a]};i.push(r)}e.PartyUnitList=i}else e.$message({message:t.Message,type:"error"})}))},getProject:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,I.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectList=t.Data.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,C.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?e.treeParams.data=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},changeWarehouse:function(e){this.currentRow=e,this.handleWarehouse(!0)},getWarehouse:function(e){var t=this,i=e.warehouse,a=e.location;this.currentRow?(this.currentRow.WarehouseId=i.Id,this.currentRow.LocationId=a.Id,this.$set(this.currentRow,"WarehouseName",i.Display_Name),this.$set(this.currentRow,"LocationName",a.Display_Name),this.$set(this.currentRow,"Warehouse_Location",i.Display_Name+"/"+a.Display_Name)):this.multipleSelection.forEach((function(e,r){t.$set(e,"WarehouseName",i.Display_Name),t.$set(e,"LocationName",a.Display_Name),t.$set(e,"Warehouse_Location",i.Display_Name+"/"+a.Display_Name),e.LocationId=a.Id,e.WarehouseId=i.Id})),this.handleUpdateRow()},batchEditorFn:function(e){var t=this;this.multipleSelection.forEach((function(i,a){e.forEach((function(e){if(t.$set(i,e.key,e.val),"Supplier"===e.key)t.$set(i,"SupplierName",e.name);else if("PartyUnit"===e.key)t.$set(i,"PartyUnitName",e.name);else if("SysProjectId"===e.key){var a=t.ProjectList.find((function(t){return t.Sys_Project_Id===e.val})),r=i.Versions.find((function(e){return e.Id===a.Version_Id}));if(r)i.Specific_Gravity=null===r||void 0===r?void 0:r.Specific_Gravity,i.Version_Id=null===r||void 0===r?void 0:r.Id;else{var n=i.Versions.find((function(e){return e.Is_System}));i.Specific_Gravity=null===n||void 0===n?void 0:n.Specific_Gravity,i.Version_Id=null===n||void 0===n?void 0:n.Id}t.$set(i,"ProjectName",e.name),t.$set(i,"Project_Code",null===a||void 0===a?void 0:a.Code)}else"TaxUnitPrice"===e.key&&(t.$set(i,"TaxUnitPriceKG",Number((e.val/1e3).toFixed(2))),0===i.InStoreWeightKG||0===i.TaxUnitPriceKG?t.$set(i,"Tax_All_Price",0):i.InStoreWeightKG&&i.TaxUnitPriceKG?t.$set(i,"Tax_All_Price",i.InStoreWeightKG*i.TaxUnitPriceKG):t.$set(i,"Tax_All_Price",""))}))})),this.multipleSelection.forEach((function(i,a){e.forEach((function(e){["SysProjectId","Width","Length","InStoreCount"].includes(e.key)?t.$refs["table"].checkWeight(i):["Pound_Weight","TaxUnitPrice"].includes(e.key)&&t.$refs["table"].countTaxUnitPrice(i,e.key)}))})),this.handleUpdateRow(),this.handleClose()},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(i){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,i=e.val;1===t?this.$set(this.currentRow,"StandardDesc",i):(this.$set(this.currentRow,"StandardDesc",i.StandardDesc),this.currentRow.StandardId=i.StandardId)},typeChange:function(e){0!==e&&(this.BigType=1,this.fileListData=[],this.form.DriverMobile="",this.$refs["form"].resetFields(),this.form.ProjectName="",this.form.SysProjectId="",this.form.SupplierName="",this.form.Supplier="",this.form.PartyUnitName="",this.form.PartyUnit="",this.form.Is_Replace_Purchase=!1,this.form.InStoreType=e,this.multipleSelection=[],this.rootTableData=[],this.handleReset(),this.countStatistics(),this.getTableColumns())},isReplacePurchaseChange:function(e){e?(this.typeNumber1=0,this.typeNumber2=0,this.typeNumber3=0,this.typeNumber4=0,this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset(),this.countStatistics()):(this.form.ProjectId="",this.form.SysProjectId="",this.form.Supplier="")},projectChange:function(e){this.form.ProjectName=this.ProjectList.find((function(t){return t.Id===e})).Short_Name,this.form.SysProjectId=this.ProjectList.find((function(t){return t.Id===e})).Sys_Project_Id,e&&(this.typeNumber1=0,this.typeNumber2=0,this.typeNumber3=0,this.typeNumber4=0,this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},supplierChange:function(e){this.form.SupplierName=this.SupplierList.find((function(t){return t.Id===e})).Name,e&&(this.typeNumber1=0,this.typeNumber2=0,this.typeNumber3=0,this.typeNumber4=0,this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},partyUnitChange:function(e){this.form.PartyUnitName=this.PartyUnitList.find((function(t){return t.Id===e})).Name,e&&(this.typeNumber1=0,this.typeNumber2=0,this.typeNumber3=0,this.typeNumber4=0,this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},blurWeight:function(e){"Car_Pound_Weight"===e?this.form.Car_Pound_Weight_KG=this.form.Car_Pound_Weight?Number(1e3*this.form.Car_Pound_Weight):this.form.Car_Pound_Weight:"Car_Voucher_Weight"===e&&(this.form.Car_Voucher_Weight_KG=this.form.Car_Voucher_Weight?Number(1e3*this.form.Car_Voucher_Weight):this.form.Car_Voucher_Weight)},openAddDialog:function(e){var t=this;if(this.form.Is_Replace_Purchase){if(!(1!=this.form.InStoreType&&3!=this.form.InStoreType||this.form.ProjectId&&this.form.Supplier))return void this.$message({message:"请先选择所属项目和供应商",type:"warning"});if(2==this.form.InStoreType&&(!this.form.ProjectId||!this.form.PartyUnit))return void this.$message({message:"请先选择所属项目和甲方单位",type:"warning"})}this.currentRow=e,this.openAddList=!0,e?(this.isSingle=!0,this.$nextTick((function(i){t.$refs["draft"].setRow(e)}))):this.isSingle=!1},closeView:function(){(0,l.closeTagView)(this.$store,this.$route)},handleImport:function(){this.currentComponent="ImportFile",this.dWidth="40%",this.title="原料导入",this.dialogVisible=!0},getAddList:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(i&&(this.form.Delivery_No="",this.form.CarNumber="",this.form.Driver="",this.form.DriverMobile="",e.forEach((function(e,t){e.InStoreWeightKG=1e3*e.InStoreWeight,e.Pound_Weight_KG=1e3*e.Pound_Weight,e.Voucher_Weight_KG=1e3*e.Voucher_Weight}))),e.map((function(e){return i||(t.form.Delivery_No&&(e.Delivery_No=t.form.Delivery_No),t.form.CarNumber&&(e.CarNumber=t.form.CarNumber),t.form.Driver&&(e.Driver=t.form.Driver),t.form.DriverMobile&&(e.DriverMobile=t.form.DriverMobile)),e.TaxUnitPriceKG=e.TaxUnitPrice,e.TaxUnitPrice=Number((1e3*e.TaxUnitPriceKG).toFixed(2)),e.Width=3===e.BigType||2===e.BigType?0:e.Width,e.Length=3===e.BigType?0:e.Length,e.Raw_FullName=(0,k.getMaterialName)({BigType:e.BigType,RawName:e.RawName,Material:e.Material,Thick:e.Thick,Spec:e.Spec,Width:e.Width,Length:e.Length}),e})),!this.form.Is_Replace_Purchase||2!=this.form.InStoreType&&3!=this.form.InStoreType)i||this.form.Is_Replace_Purchase||2!=this.form.InStoreType&&3!=this.form.InStoreType||e.forEach((function(e,t){if(e.ProjectName&&e.Version_Id);else{var i,a,r=null===(i=e.Versions.find((function(e){return e.Is_System})))||void 0===i?void 0:i.Specific_Gravity;e.Specific_Gravity=null!==r&&void 0!==r?r:null,e.Version_Id=null===(a=e.Versions.find((function(e){return e.Is_System})))||void 0===a?void 0:a.Id}}));else{var a=this.ProjectList.find((function(e){return e.Sys_Project_Id===t.form.SysProjectId}));e.forEach((function(e){var r;if(e.ProjectName=t.form.ProjectName,e.Project_Code=null===(r=t.ProjectList.find((function(e){return e.Sys_Project_Id===t.form.SysProjectId})))||void 0===r?void 0:r.Code,e.SysProjectId=t.form.SysProjectId,e.SupplierName=t.form.SupplierName,e.Supplier=t.form.Supplier,e.PartyUnitName=t.form.PartyUnitName,e.PartyUnit=t.form.PartyUnit,a&&!i){var n=e.Versions.find((function(e){return e.Id===a.Version_Id}));e.Specific_Gravity=null===n||void 0===n?void 0:n.Specific_Gravity,e.Version_Id=null===n||void 0===n?void 0:n.Id}}))}this.handleUpdateTb(e,"add",i)},countStatistics:function(e){var t=this;this.statisticsData={OutStoreCount:0,InStoreCountTotal:0,InStoreWeightTotal:0,PoundWeightTotal:0,Tax_All_Price:0,VoucherTaxAllPrice:0,VoucherWeightTotal:0},this.rootTableData.map((function(e){t.statisticsData.InStoreCountTotal+=Number((null===e||void 0===e?void 0:e.InStoreCount)||0),t.statisticsData.InStoreWeightTotal+=Number((null===e||void 0===e?void 0:e.InStoreWeight)||0),t.statisticsData.PoundWeightTotal+=Number((null===e||void 0===e?void 0:e.Pound_Weight)||0),t.statisticsData.VoucherWeightTotal+=Number((null===e||void 0===e?void 0:e.Voucher_Weight)||0),t.statisticsData.Tax_All_Price+=Number((null===e||void 0===e?void 0:e.Tax_All_Price)||0),t.statisticsData.VoucherTaxAllPrice+=Number((null===e||void 0===e?void 0:e.VoucherTaxAllPrice)||0)}))},handleUpdateTb:function(e,t,i){var a,n;e.map((function(e,t){e.index=(0,R.v4)()}));var o=JSON.parse(JSON.stringify(e));"add"===t&&this.$refs["table"].addData(o,i),(a=this.tableData).push.apply(a,(0,r.default)(o)),(n=this.rootTableData).push.apply(n,(0,r.default)(o)),this.countStatistics()},handleUpdateRow:function(){var e=JSON.parse(JSON.stringify(this.$refs.table.tbData));this.tableData=JSON.parse(JSON.stringify(e)),this.rootTableData.map((function(t){var i=e.find((function(e){return e.index===t.index}));i&&Object.assign(t,i)})),this.countStatistics()},importData:function(e){this.$refs["table"].importData(e)},getRowName:function(e){var t=e.Name,i=e.Id;this.currentRow.Name=t,this.currentRow.RawId=i,this.currentRow.StandardDesc=""},handleBatchEdit:function(){var e=this;this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.multipleSelection,e.BigType,e.form.InStoreType)}))},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handleWarehouse:function(e){this.currentComponent="Warehouse",this.dWidth="40%",this.title="批量选择仓库/库位",!e&&(this.currentRow=null),this.dialogVisible=!0},handleDelete:function(e){var t,i=JSON.parse(JSON.stringify(this.$refs.table.tbData)),a=JSON.parse(JSON.stringify(this.rootTableData));this.multipleSelection.forEach((function(e,t){var r=i.findIndex((function(t){return t.index===e.index}));i.splice(r,1);var n=a.findIndex((function(t){return t.index===e.index}));a.splice(n,1)})),this.$refs.table.tbData=JSON.parse(JSON.stringify(i)),this.tableData=JSON.parse(JSON.stringify(i)),this.rootTableData=JSON.parse(JSON.stringify(a)),this.multipleSelection=[],null===(t=this.$refs.table)||void 0===t||null===(t=t.$refs)||void 0===t||t.xTable.clearCheckboxRow(),this.countStatistics()},handleClose:function(e){this.openAddList=!1,this.dialogVisible=!1,this.dialogRepeatVisible=!1},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},checkValidate:function(){this.handleUpdateRow();var e=(0,l.deepClone)(this.rootTableData);if(this.tbDataInfo=e,!e.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var t={status:!0,type:"",msg:""},i=e;if(i.length&&t.status&&(t=this.checkTb(i)),!t.status)return this.$message({message:"".concat(t.type+t.msg||"必填字段","不能为空"),type:"warning"}),{status:!1};var a={status:!0,type:"",msg:""};return a.status?{data:e,status:!0}:(this.$message({message:"".concat(a.type,"存在重复数据"),type:"warning"}),{status:!1})},checkTb:function(e){for(var t,i=this,a=function(t){for(var a,r=i.checkTypeList.find((function(i){return i.BigType===e[t].BigType})).checkList,n=e[t],o=function(){var e=r[s];if(["",null,void 0].includes(n[e])){var t=i.$refs.table.rootColumns,a=t.find((function(t){return t.Code===e}));return{v:{v:{status:!1,msg:null===a||void 0===a?void 0:a.Display_Name,type:1===n.BigType?"板材":2===n.BigType?"型材":3===n.BigType?"钢卷":"其他"}}}}},s=0;s<r.length;s++)if(a=o(),a)return a.v;delete n._X_ROW_KEY,delete n.WarehouseName,delete n.LocationName},r=0;r<e.length;r++)if(t=a(r),t)return t.v;return{status:!0,msg:"",type:""}},handleSaveDraft:function(){this.isDraft=!0,this.saveDraft(1,!1)},submitInfo:function(){var e=this;this.isDraft?this.saveDraft(1,!0):this.$confirm("确认提交入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveDraft(3,!0)})).catch((function(){var t;e.$message({type:"info",message:"已取消"}),null===(t=e.$refs["repeat"])||void 0===t||t.setLoading(!1)}))},handleSubmit:function(e){this.isDraft=!1,this.saveDraft(3,!1)},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.handleUpdateRow();var a=this.checkValidate(),r=a.data,n=a.status;n&&this.$refs["form"].validate((function(a){if(!a)return!1;var n=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){n.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)}));var o=(0,s.default)({},e.form);o.Car_Pound_Weight=o.Car_Pound_Weight_KG,o.Car_Voucher_Weight=o.Car_Voucher_Weight_KG,o.Attachment=n.join(","),o.Status=1===t?1:3,o.InStoreNo=e.$route.query.id,r.map((function(e){e.InStoreWeight=e.InStoreWeightKG,e.Pound_Weight=e.Pound_Weight_KG,e.Voucher_Weight=e.Voucher_Weight_KG,e.TaxUnitPrice=e.TaxUnitPriceKG}));var l=e.checkSameInfo(),u=l.show,c=l.resultArray,d=l.indexes;!i&&u&&e.checkDuplicate?(e.dialogRepeatVisible=!0,e.$nextTick((function(t){e.$refs["repeat"].setTbData(c,d)}))):(1===t?e.saveLoading=!0:e.submitLoading=!0,e.submitDraft(o,r))}))},submitDraft:function(e,t){var i=this,a=1==e.InStoreType?C.MaterielRawPurchaseInStore:2==e.InStoreType?C.MaterielRawPartyAInStore:3==e.InStoreType?C.MaterielRawManualInStore:C.MaterielRawSurplusInStore;a({Receipt:e,Sub:t}).then((function(e){var t;e.IsSucceed?(i.$message({message:"保存成功",type:"success"}),i.closeView()):i.$message({message:e.Message,type:"error"}),i.saveLoading=!1,i.submitLoading=!1,null===(t=i.$refs["repeat"])||void 0===t||t.setLoading(!1)}))},setSelectRow:function(e){this.multipleSelection=e},getDate:function(e){var t=e||new Date,i=t.getFullYear(),a=("0"+(t.getMonth()+1)).slice(-2),r=("0"+t.getDate()).slice(-2);return"".concat(i,"-").concat(a,"-").concat(r)},checkSameInfo:function(){var e=this,t=JSON.parse(JSON.stringify(this.$refs.table.tbData));t.forEach((function(t){var i=e.checkTypeList.find((function(e){return e.BigType===t.BigType})),a=i.checkSameList.filter((function(e){return!!e}));t.currentKey=a.reduce((function(e,i){return e+"-"+(t[i]||"").toString().trim()}),"")}));var i=t,a=i.reduce((function(e,t){return t.currentKey&&(e[t.currentKey]=(e[t.currentKey]||0)+1),e}),{}),r=Object.keys(a).filter((function(e){return a[e]>1})),n=i.filter((function(e){return r.includes(e.currentKey)})),o=n.reduce((function(e,t){return e[t.currentKey]||(e[t.currentKey]=[]),e[t.currentKey].push(t),e}),{}),s=Object.keys(o).reduce((function(e,t){return e=e.concat(o[t]),e}),[]),l=s.reduce((function(e,t,i){return 0!==i&&t.currentKey===s[i-1].currentKey||e.push(i),e}),[]);return{show:s.length>0,resultArray:s,indexes:l}},toggleFilter:function(){this.filterVisible=!this.filterVisible},handleReturn:function(){var e=this,t=this.checkValidate(),i=t.data,a=t.status;a&&this.$refs["form"].validate((function(t){if(!t)return!1;var a=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){a.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)}));var r="",n=(0,s.default)({},e.form);n.Car_Pound_Weight=n.Car_Pound_Weight_KG,n.Car_Voucher_Weight=n.Car_Voucher_Weight_KG,n.Attachment=a.join(","),n.InStoreNo=e.$route.query.id;var o=i.filter((function(e){return e.InStoreWeight=e.InStoreWeightKG,e.Pound_Weight=e.Pound_Weight_KG,e.Voucher_Weight=e.Voucher_Weight_KG,e.OutStoreWeight=1e3*e.OutStoreWeight,e.ReturnVoucherWeight=1e3*e.ReturnVoucherWeight,e.ReturnPoundWeight=1e3*Number(e.ReturnPoundWeight),(e.OutStoreWeight>=e.AvailableWeight&&e.OutStoreCount!=e.AvailableCount||e.OutStoreWeight!=e.AvailableWeight&&e.OutStoreCount>=e.AvailableCount)&&(r="明细数据中数量或重量有一个值为库存最大值但另一个值非最大值，非最大值将自动被改为最大值",e.OutStoreCount=e.AvailableCount,e.OutStoreWeight=e.AvailableWeight),e.TaxUnitPrice=e.TaxUnitPriceKG,e.OutStoreCount>0}));r&&e.$message.info(r),e.returning=!0,(0,C.RawReturnByReceipt)({Receipt:n,Sub:o}).then((function(t){t.IsSucceed?(e.$message({message:"退货成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"}),e.returning=!1}))}))}}}},"67a3":function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[1!==e.formData.InStoreType?i("div",{staticClass:"cs-alert"},[i("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("点击此处下载导入模板")])],1):e._e(),i("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),i("footer",{staticClass:"cs-footer"},[i("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),i("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},r=[]},"6d3b":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("c14f")),n=a(i("1da1"));i("d81d"),i("e9f5"),i("ab43"),i("d3b7"),i("ac1f"),i("841c");var o=i("ed08"),s=i("5480"),l=i("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,n.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,n.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},"6dcb":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("caad"),i("d81d"),i("e9f5"),i("ab43"),i("d3b7"),i("2532");var r=a(i("6612"));t.default={props:{columns:{type:Array,default:function(){return[]}}},data:function(){return{resultArray:[],indexes:[],loading:!1}},methods:{setLoading:function(e){this.loading=e},submit:function(){this.loading=!0,this.$emit("submit",1)},setTbData:function(e,t){this.resultArray=e.map((function(e){return e.InStoreWeight=(0,r.default)(e.InStoreWeightKG).divide(1e3).format("0.[00000]"),e.Pound_Weight=(0,r.default)(e.Pound_Weight_KG).divide(1e3).format("0.[000]"),e.Voucher_Weight=(0,r.default)(e.Voucher_Weight_KG).divide(1e3).format("0.[000]"),e.TaxUnitPrice=(0,r.default)(e.TaxUnitPriceKG).multiply(1e3).format("0.[00]"),e})),this.indexes=t},rowStyle:function(e){var t=e.rowIndex;if(this.indexes.includes(t))return{backgroundColor:"#ED8591",color:"#ffffff"}}}}},"6de4":function(e,t,i){},"6f65":function(e,t,i){"use strict";i.r(t);var a=i("a8f5"),r=i("fcac");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"eb17f1ac",null);t["default"]=s.exports},"775a":function(e,t,i){"use strict";i.r(t);var a=i("129c"),r=i("dbdd");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("d0a4");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"ce0d4930",null);t["default"]=s.exports},"7fc7":function(e,t,i){"use strict";i("c93b")},81028:function(e,t,i){"use strict";i.r(t);var a=i("958a"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"82d9":function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"contentBox"},[i("el-tabs",{on:{"tab-click":e.handleSearch},model:{value:e.form.IsFinished,callback:function(t){e.$set(e.form,"IsFinished",t)},expression:"form.IsFinished"}},[i("el-tab-pane",{attrs:{label:"未完成",name:"0"}}),i("el-tab-pane",{attrs:{label:"已完成",name:"1"}})],1),i("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[i("el-row",[i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"采购单号",prop:"OrderCode"}},[i("el-input",{attrs:{clearable:"",placeholder:"采购单号",type:"text"},model:{value:e.form.OrderCode,callback:function(t){e.$set(e.form,"OrderCode","string"===typeof t?t.trim():t)},expression:"form.OrderCode"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[i("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"原料分类",prop:"CategoryId"}},[i("el-tree-select",{ref:"treeSelect",staticClass:"input",staticStyle:{width:"100%"},attrs:{"tree-params":e.treeParams},model:{value:e.form.CategoryId,callback:function(t){e.$set(e.form,"CategoryId",t)},expression:"form.CategoryId"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[i("el-input",{attrs:{clearable:"",placeholder:"原料名称",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"供应商",prop:"SupplierName"}},[i("el-input",{attrs:{clearable:"",placeholder:"供应商",type:"text"},model:{value:e.form.SupplierName,callback:function(t){e.$set(e.form,"SupplierName","string"===typeof t?t.trim():t)},expression:"form.SupplierName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[i("el-select",{attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}},e._l(e.projectList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"厚度",prop:"Thick"}},[i("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"厚度"},model:{value:e.form.Thick,callback:function(t){e.$set(e.form,"Thick","string"===typeof t?t.trim():t)},expression:"form.Thick"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[i("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width","string"===typeof t?t.trim():t)},expression:"form.Width"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"长度",prop:"Length"}},[i("el-input",{staticClass:"input-number",attrs:{type:"number",clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length","string"===typeof t?t.trim():t)},expression:"form.Length"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"材质",prop:"Material"}},[i("el-input",{attrs:{type:"text",clearable:"",placeholder:"材质"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"0"}},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),i("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],1)],1),i("div",{staticClass:"tb-wrapper"},[i("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[i("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[i("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["WareCount"===t.Code?{key:"default",fn:function(t){var a=t.row;return[i("span",{style:{color:a.WareCount>a.PurchaseCount?"red":""}},[e._v(" "+e._s(a.WareCount)+" ")])]}}:"PurchaseWeight"===t.Code?{key:"default",fn:function(t){var i=t.row;return[e._v(" "+e._s(Number((i.PurchaseWeight/1e3).toFixed(5)))+" ")]}}:"Actual_Spec"===t.Code?{key:"default",fn:function(t){var i=t.row;return[e._v(" "+e._s(i.Actual_Spec||"-")+" ")]}}:"DeliveryTime"===t.Code?{key:"default",fn:function(i){var a=i.row;return[e._v(" "+e._s(e._f("timeFormat")(a[t.Code],"{y}-{m}-{d}"))+" ")]}}:"PurchaseTime"===t.Code?{key:"default",fn:function(i){var a=i.row;return[e._v(" "+e._s(e._f("timeFormat")(a[t.Code],"{y}-{m}-{d}"))+" ")]}}:{key:"default",fn:function(i){var a=i.row;return[e._v(" "+e._s(a[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),i("div",{staticClass:"button"},[i("el-button",{on:{click:e.handleClose}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},r=[]},8378:function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CreateVersion=R,t.DelAuxCategoryEntity=v,t.DelAuxEntity=C,t.DelCategoryEntity=l,t.DelRawEntity=d,t.DeleteVersion=D,t.EditAuxEnabled=S,t.EditRawEnabled=c,t.ExportAuxForProject=B,t.ExportAuxList=N,t.ExportFindRawInAndOut=M,t.ExportInOutStoreReport=X,t.ExportPicking=O,t.ExportRawList=T,t.ExportRecSendProjectMaterialReport=Y,t.ExportRecSendProjectReport=q,t.ExportReceiving=L,t.ExportStagnationInventory=H,t.ExportStoreReport=z,t.FindInAndOutPageList=U,t.FindPickingNewPageList=G,t.FindPickingPageList=A,t.FindReceivingNewPageList=$,t.FindReceivingPageList=F,t.GetAuxCategoryDetail=b,t.GetAuxCategoryTreeList=g,t.GetAuxDetail=I,t.GetAuxFilterDataSummary=J,t.GetAuxForProjectDetail=K,t.GetAuxForProjectPageList=j,t.GetAuxPageList=x,t.GetAuxTemplate=P,t.GetAuxWHSummaryList=E,t.GetCategoryDetail=s,t.GetCategoryTreeList=n,t.GetCycleDate=V,t.GetList=k,t.GetRawDetail=h,t.GetRawPageList=f,t.GetRawTemplate=p,t.ImportAuxList=w,t.ImportRawList=m,t.SaveAuxCategoryEntity=y,t.SaveAuxEntity=_,t.SaveCategoryEntity=o,t.SaveRawEntity=u,t.UpdateVersion=W;var r=a(i("b775"));function n(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function o(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:e,timeout:12e5})}function g(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:e,timeout:12e5})}function T(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:e})}function R(e){return(0,r.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:e})}},"8759e":function(e,t,i){"use strict";i.r(t);var a=i("1338"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"879a":function(e,t,i){"use strict";i.r(t);var a=i("67a3"),r=i("52ad");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("3340");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"65c0f006",null);t["default"]=s.exports},"8d38":function(e,t,i){"use strict";i.r(t);var a=i("601e"),r=i("96b9");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("03d5");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"a47fb806",null);t["default"]=s.exports},"926e":function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[2===e.specificationUsage||0===e.specificationUsage?i("el-tab-pane",{attrs:{label:"非标规格",name:"1"}},[i("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},e._l(e.list,(function(t){return i("el-form-item",{key:t,attrs:{label:t}},[i("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:e.form[t],callback:function(i){e.$set(e.form,t,i)},expression:"form[item]"}})],1)})),1)],1):e._e(),1===e.specificationUsage||0===e.specificationUsage?i("el-tab-pane",{attrs:{label:"标准规格",name:"2"}},[i("el-table",{ref:"multipleTable",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData}},[i("el-table-column",{attrs:{width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(i){return i.stopPropagation(),function(i){return e.handleRadioChange(i,t.row)}(i)}},model:{value:e.radioSelect,callback:function(t){e.radioSelect=t},expression:"radioSelect"}})]}}],null,!1,3152109164)}),i("el-table-column",{attrs:{align:"center",prop:"StandardDesc",label:"规格"}})],1)],1):e._e()],1),i("div",{staticClass:"dialog-footer"},[i("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),i("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},"92f08":function(e,t,i){"use strict";i.r(t);var a=i("926e"),r=i("8759e");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("c9bd");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"7bed79da",null);t["default"]=s.exports},9395:function(e,t,i){"use strict";i.r(t);var a=i("e152"),r=i("58ab");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"0dd2f1bb",null);t["default"]=s.exports},"958a":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("2909")),n=a(i("c14f")),o=a(i("1da1"));i("4de4"),i("7db0"),i("caad"),i("d81d"),i("14d9"),i("a434"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("ab43"),i("e9c4"),i("a9e3"),i("b680"),i("b64b"),i("d3b7"),i("2532"),i("159b");var s=i("ed08"),l=i("30c8"),u=i("8fea");t.default={props:{isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1},isReplacePurchase:{type:Boolean,default:!1},formStatus:{type:Number,default:0},checkTypeList:{type:Array,required:!0,default:function(){return[]}},projectList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,default:function(){return[]}}},data:function(){return{multipleSelection:[],rootColumns:[],columns:[],tbData:[],BigType:1,renderComponent:!0,num:1,itemKey:""}},computed:{isReturn:function(){return 88===this.formStatus}},inject:["checkDuplicate"],watch:{bigTypeData:{handler:function(e,t){this.BigType=e,this.columnsOption()},immediate:!1},isReplacePurchase:{handler:function(e,t){e?(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"PartyUnitName"!==e.Code||(e.Is_Edit=!1,e.Is_Must_Input=!1)})),this.init(this.rootColumns),this.forceRerender()):(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"PartyUnitName"!==e.Code||(e.Is_Edit=!0,e.Is_Must_Input=!0)})),this.init(this.rootColumns),this.forceRerender())},immediate:!1}},created:function(){return(0,o.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},mounted:function(){},methods:{setRowFullName:function(e){e.Raw_FullName=(0,l.getMaterialName)(e)},lengthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},widthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},changeKeyInfo:function(e,t){var i=this.tbData.map((function(t){return t[e]})),a=(0,s.uniqueArr)(i);1===a.length?this.$emit("changeKeyInfo",{key:e,val:t}):this.$emit("changeKeyInfo",{key:e,val:""})},changeFormInfo:function(e,t){var i=this;this.tbData.forEach((function(a){i.$set(a,e,t)}))},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},init:function(e){e=e.filter((function(e){return e.Style=e.Style?JSON.parse(e.Style):"","ReturnSupplierName"!==e.Code})),this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.itemKey=Math.random(),e.columns=JSON.parse(JSON.stringify(e.rootColumns));case 1:return t.a(2)}}),t)})))()},getCheckList:function(e,t){var i=this,a=[];a=this.checkDuplicate()?["ProjectName","Project_Code","PartyUnitName","CategoryName","RawNameFull","RawName","RawCode","Material","Spec","Actual_Spec","TaxUnitPrice","Tax_Rate","Warehouse_Location","FurnaceBatchNo"]:[];var r="Width",n="Length";this.checkTypeList.map((function(o){o.BigType===e&&(o.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),o.checkList=o.checkList.map((function(e){return e.Code})),o.checkSameList=t.filter((function(e){return a.includes(e.Code)})),o.checkSameList=o.checkSameList.map((function(e){return e.Code})),i.checkDuplicate()&&(o.checkSameList.unshift("RawCode"),2===e?o.checkSameList.push(n):1===e&&(o.checkSameList.push(r),o.checkSameList.push(n))),o.remarkList=t.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),o.remarkList=o.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}})))}))},getAllColumnsOption:function(){var e=this,t=[1,2,3],i=JSON.parse(JSON.stringify(this.rootColumns));t.forEach((function(t){e.getCheckList(t,i)}))},tbfetchData:function(){this.tbData=[]},handleCopy:function(e,t){var i=JSON.parse(JSON.stringify(e));i.Sub_Id&&(i.Sub_Id=""),delete i._X_ROW_KEY,this.$emit("updateTb",[i],"copy"),this.tbData.splice(t+1,0,i)},projectChange:function(e,t){var i=this.projectList.find((function(e){return e.Sys_Project_Id===t.SysProjectId}));if(i){t.ProjectName=i.Short_Name,t.Project_Code=i.Code;var a=t.Versions.find((function(e){return e.Id===i.Version_Id}));a&&(t.Specific_Gravity=a.Specific_Gravity,t.Version_Id=i.Version_Id)}this.checkWeight(t),this.$emit("updateRow")},partyUnitNameChange:function(e){e.PartyUnitName=this.partyUnitList.find((function(t){return t.Id===e.PartyUnit})).Name,this.$emit("updateRow")},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,i=this,a=e.map((function(e){return e.Actual_Thick=e.Actual_Thick||e.Thick,e.Spec=1===e.BigType?e.Thick:e.Spec,e.Actual_Spec=e.Actual_Spec||(1===e.BigType?e.Thick:e.Spec),e.RawNameFull=(0,l.getMaterialName)(e),e.TaxUnitPrice=e.TaxUnitPrice||0,"number"!==typeof e.Tax_Rate&&(e.Tax_Rate=13),e.Specific_Gravity&&3!==e.BigType?i.checkWeight(e):i.countTaxUnitPrice(e,"InStoreCount_Width_Length"),e.rowIndex="",e}));(t=this.tbData).push.apply(t,(0,r.default)(a)),this.tbData=JSON.parse(JSON.stringify(this.tbData))},checkWeight:function(e){0!==e.Specific_Gravity&&!e.Specific_Gravity||3===e.BigType||(1!==e.BigType&&2!==e.BigType||0!==e.Specific_Gravity&&0!==e.Length?1===e.BigType?"花纹板"===e.CategoryName?0===e.Length||0===e.Width?e.InStoreWeightKG=0:e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":0===e.Thick||0===e.Width?e.InStoreWeightKG=0:e.Thick&&e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Thick*e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":2===e.BigType&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":e.InStoreWeightKG=0,e.InStoreWeight=e.InStoreWeightKG||0===e.InStoreWeightKG?Number((e.InStoreWeightKG/u.WEIGHT_CONVERSION).toFixed(u.WEIGHT_DECIMAL)):""),this.countTaxUnitPrice(e,"InStoreCount_Width_Length")},countTaxUnitPrice:function(e,t){"InStoreWeight"===t?e.InStoreWeightKG=e.InStoreWeight||0===e.InStoreWeight?Number(e.InStoreWeight*u.WEIGHT_CONVERSION):"":"Pound_Weight"===t?e.Pound_Weight_KG=e.Pound_Weight||0===e.Pound_Weight?Number(e.Pound_Weight*u.WEIGHT_CONVERSION):"":"Voucher_Weight"===t&&(e.Voucher_Weight_KG=e.Voucher_Weight||0===e.Voucher_Weight?Number(e.Voucher_Weight*u.WEIGHT_CONVERSION):""),"TaxUnitPrice"===t&&(e.TaxUnitPriceKG=Number((e.TaxUnitPrice/u.WEIGHT_CONVERSION).toFixed(u.UNIT_PRICE_KG_DECIMAL))),"TaxUnitPrice"!==t&&"Tax_Rate"!==t||(e.NoTaxUnitPrice=e.TaxUnitPrice&&e.Tax_Rate?Number((e.TaxUnitPrice/(1+e.Tax_Rate/100)).toFixed(u.UNIT_PRICE_DECIMAL)):0),"TaxUnitPrice"!==t&&"InStoreWeight"!==t&&"InStoreCount_Width_Length"!==t||(99===e.BigType?0===e.InStoreCount||0===e.TaxUnitPrice?e.Tax_All_Price=0:e.InStoreCount&&e.TaxUnitPrice?e.Tax_All_Price=Number((e.InStoreCount*e.TaxUnitPrice).toFixed(2)):e.Tax_All_Price="":0===e.InStoreWeightKG||0===e.TaxUnitPriceKG?e.Tax_All_Price=0:e.InStoreWeightKG&&e.TaxUnitPriceKG?e.Tax_All_Price=Number((e.InStoreWeightKG*e.TaxUnitPriceKG).toFixed(2)):e.Tax_All_Price="",this.taxAllPriceChange(e),this.voucherTaxAllPriceChange(e)),this.taxAllPriceChange(e),this.voucherTaxAllPriceChange(e),this.$emit("updateRow")},taxAllPriceChange:function(e){e.NoTaxAllPrice=(0,l.getNoTaxAllPrice)(e)},voucherTaxAllPriceChange:function(e){e.VoucherTaxAllPrice=(0,l.getVoucherTaxAllPrice)(e),e.VoucherNoTaxAllPrice=(0,l.getVoucherNoTaxAllPrice)(e)},tableEditData:function(e){e.RawNameFull=(0,l.getMaterialName)(e),this.$emit("updateRow")},tableEditRemark:function(e){this.setRowFullName(e),this.$emit("updateRow")},outStoreChange:function(e){var t=e.Specific_Gravity,i=0;i=t?1===e.BigType?"花纹板"===e.CategoryName?Number((e.Width*e.Length*t*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):Number((e.Width*e.Length*Number(e.Spec)*t*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):2===e.BigType?Number((e.Length*t*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):Number((e.PerWeight*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.PerWeight*e.OutStoreCount,e.OutStoreWeight=(i/u.WEIGHT_CONVERSION).toFixed(u.WEIGHT_DECIMAL)/1,e.ReturnVoucherWeight=(e.PerVoucherWeight*e.OutStoreCount/u.WEIGHT_CONVERSION).toFixed(u.WEIGHT_DECIMAL)/1,this.$emit("updateRow")}}}},"96b9":function(e,t,i){"use strict";i.r(t);var a=i("6d3b"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},"9e4a":function(e,t,i){},a8f5:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.renderComponent?i("vxe-table",{key:e.itemKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[i("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e.isView||e.isReturn?e._e():i("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60",title:""}}),e.isView||e.isReturn?e._e():i("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,r=t.rowIndex;return[i("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&a.Sub_Id)},on:{click:function(t){return e.handleCopy(a,r)}}},[e._v("复制")])]}}],null,!1,2613506362)}),e._l(e.columns,(function(t){return[i("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([t.Style.tips?{key:"header",fn:function(){return[i("span",[e._v(e._s(t.Display_Name))]),i("el-tooltip",{staticClass:"item",attrs:{effect:"dark"}},[i("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(t.Style.tips)},slot:"content"}),i("i",{staticClass:"el-icon-question",staticStyle:{cursor:"pointer","font-size":"16px"}})])]},proxy:!0}:null,{key:"default",fn:function(a){var r=a.row;return[i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(a){var r=a.row;return["Actual_Thick"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Width"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType||2===r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.widthChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):t.Code.includes("Remark")?i("div",[i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Length"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.lengthChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Material"===t.Code?i("div",[i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.tableEditData(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"InStoreCount"===t.Code?i("div",[i("vxe-input",{attrs:{min:0,controls:!1,type:"number"},on:{change:function(t){return e.checkWeight(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"InStoreWeight"===t.Code?i("div",[r.Specific_Gravity&&3!==r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:5,min:0},expression:"{ toFixed: 5, min: 0 }"}],attrs:{type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"InStoreWeight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Pound_Weight"===t.Code?i("div",[i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Pound_Weight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Voucher_Weight"===t.Code?i("div",[i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Voucher_Weight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("div",[e._v(" "+e._s(r.WarehouseName)+"/"+e._s(r.LocationName)+" "),i("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(r)}}})])]):"FurnaceBatchNo"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r.FurnaceBatchNo,callback:function(t){e.$set(r,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})],1):"ProjectName"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-select",{attrs:{filterable:"",transfer:"",clearable:""},on:{change:function(t){return e.projectChange(r)}},model:{value:r.SysProjectId,callback:function(t){e.$set(r,"SysProjectId",t)},expression:"row.SysProjectId"}},e._l(e.projectList,(function(e){return i("vxe-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Short_Name}})})),1)],1):"SupplierName"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-select",{attrs:{transfer:"",filterable:"",clearable:""},on:{change:function(t){return e.supplierChange(r)}},model:{value:r.Supplier,callback:function(t){e.$set(r,"Supplier",t)},expression:"row.Supplier"}},e._l(e.supplierList,(function(e){return i("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):"TaxUnitPrice"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"TaxUnitPrice")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Delivery_No"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("Delivery_No",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"CarNumber"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("CarNumber",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Driver"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("Driver",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"DriverMobile"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("DriverMobile",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Tax_Rate"===t.Code?i("div",[i("vxe-input",{attrs:{type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Tax_Rate")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"OutStoreCount"===t.Code?i("div",[3===e.formStatus&&r.OutStoreCount?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{max:r.AvailableCount,min:0,type:"number"},on:{change:function(t){return e.outStoreChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"ReturnSupplierName"===t.Code?i("div",[3===e.formStatus&&r.ReturnSupplier||r.SupplierName?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-select",{attrs:{filterable:"",transfer:""},on:{change:function(t){return e.supplierNameChange(t,r)}},model:{value:r.ReturnSupplier,callback:function(t){e.$set(r,"ReturnSupplier",t)},expression:"row.ReturnSupplier"}},e._l(e.supplierList,(function(e){return i("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},r=[]},ace8:function(e,t,i){},ad97:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("7db0"),i("e9f5"),i("f665"),i("a9e3"),i("d3b7");var a=i("9643");t.default={props:{pageType:{type:Number,default:void 0}},data:function(){return{warehouses:[],locations:[],form:{Warehouse_Id:"",Location_Id:""},btnLoading:!1,rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){this.getWarehouseListOfCurFactory()},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var i=e.warehouses.find((function(t){return t.Id===e.form.Warehouse_Id})),a=e.locations.find((function(t){return t.Id===e.form.Location_Id}));e.$emit("warehouse",{warehouse:i,location:a}),e.btnLoading=!1,e.handleClose()}))},getWarehouseListOfCurFactory:function(){var e=this;(0,a.GetWarehouseListOfCurFactory)({type:0===this.pageType?"原材料仓库":"辅料仓库"}).then((function(t){if(t.IsSucceed){if(e.warehouses=t.Data,e.warehouses.length){var i=e.warehouses.find((function(e){return e.Is_Default}));i&&(e.form.Warehouse_Id=i.Id,e.getLocationList(!0))}}else e.$message({message:t.Message,type:"error"})}))},wareChange:function(e){this.form.Location_Id="",e&&this.getLocationList(!0)},getLocationList:function(e){var t=this;(0,a.GetLocationList)({Warehouse_Id:this.form.Warehouse_Id}).then((function(i){if(i.IsSucceed){if(t.locations=i.Data,t.locations.length&&e){var a=t.locations.find((function(e){return e.Is_Default}));a&&(t.form.Location_Id=a.Id)}}else t.$message({message:i.Message,type:"error"})}))},handleClose:function(){this.$emit("close")}}}},afac:function(e,t,i){"use strict";i.r(t);var a=i("626c"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},bb2d:function(e,t,i){},bbd2:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),i("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),i("div",{staticClass:"dialog-footer"},[i("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),i("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},r=[]},c93b:function(e,t,i){},c9bd:function(e,t,i){"use strict";i("bb2d")},c9d7:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e._l(e.list,(function(t,a){return i("el-row",{key:t.id,staticClass:"item-x"},[i("div",{staticClass:"item"},[i("label",[e._v(" 属性名称 "),i("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.key,callback:function(i){e.$set(t,"key",i)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return i("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),i("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[i("label",[e._v("请输入值 "),e.checkType(t.key,"number")?i("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(i){e.$set(t,"val",i)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?i("el-input",{model:{value:t.val,callback:function(i){e.$set(t,"val",i)},expression:"info.val"}}):e._e(),e.checkType(t.key,"supplierArray")?i("el-select",{attrs:{placeholder:"请选择供应商",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"supplierArray")}},model:{value:t.val,callback:function(i){e.$set(t,"val",i)},expression:"info.val"}},e._l(e.supplierList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"partyUnitArray")?i("el-select",{attrs:{placeholder:"请选择甲方单位",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"partyUnitArray")}},model:{value:t.val,callback:function(i){e.$set(t,"val",i)},expression:"info.val"}},e._l(e.partyUnitList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"projectArray")?i("el-select",{attrs:{placeholder:"请选择所属项目",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"projectArray")}},model:{value:t.val,callback:function(i){e.$set(t,"val",i)},expression:"info.val"}},e._l(e.projectList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1):e._e()],1)]),i("span",{staticClass:"item-span"},0===a?[i("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[i("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),i("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(a)}}})])])})),i("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[i("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},r=[]},cc0a:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container abs100"},[i("el-card",{staticClass:"box-card",style:e.isRetract?"height: 110px; overflow: hidden;":""},[i("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"10px","padding-bottom":"10px","border-bottom":"1px solid #d0d3db"}},[i("div",{staticClass:"toolbar-title"},[i("span"),e._v("入库单信息")]),i("div",{staticClass:"retract-container",on:{click:e.handleRetract}},[i("el-button",{attrs:{type:"text"}},[e._v(e._s(e.isRetract?"展开":"收起"))]),i("el-button",{attrs:{type:"text",icon:e.isRetract?"el-icon-arrow-down":"el-icon-arrow-up"}})],1)]),i("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[i("el-row",[i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"入库类型",prop:"InStoreType"}},[i("SelectMaterialStoreType",{attrs:{disabled:e.isView||e.isEdit||e.isReturn,type:"RawInStoreType"},on:{change:e.typeChange},model:{value:e.form.InStoreType,callback:function(t){e.$set(e.form,"InStoreType",t)},expression:"form.InStoreType"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"入库日期",prop:"InStoreDate"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus||e.isReturn,"picker-options":{disabledDate:function(t){return t.getTime()<new Date(e.statisticTime).getTime()}},"value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.InStoreDate,callback:function(t){e.$set(e.form,"InStoreDate",t)},expression:"form.InStoreDate"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"送货单编号",prop:"Delivery_No"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"送货单编号",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("Delivery_No")}},model:{value:e.form.Delivery_No,callback:function(t){e.$set(e.form,"Delivery_No",t)},expression:"form.Delivery_No"}})],1)],1),1==e.form.InStoreType?i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"采购合同编号",prop:"Purchase_Contract_No"}},[i("el-input",{ref:"elInput",staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus||e.isReturn,placeholder:"采购合同编号",type:"text",clearable:""},model:{value:e.form.Purchase_Contract_No,callback:function(t){e.$set(e.form,"Purchase_Contract_No",t)},expression:"form.Purchase_Contract_No"}})],1)],1):e._e(),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"是否代购",prop:"Is_Replace_Purchase"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus||e.isReturn,placeholder:"请选择"},on:{change:e.isReplacePurchaseChange},model:{value:e.form.Is_Replace_Purchase,callback:function(t){e.$set(e.form,"Is_Replace_Purchase",t)},expression:"form.Is_Replace_Purchase"}},[i("el-option",{attrs:{label:"是",value:!0}}),i("el-option",{attrs:{label:"否",value:!1}})],1)],1)],1),e.form.Is_Replace_Purchase?i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"所属项目",prop:"ProjectId"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus||e.isReturn},on:{change:e.projectChange},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1):e._e(),1!=e.form.InStoreType&&3!=e.form.InStoreType||!e.form.Is_Replace_Purchase?e._e():i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus||e.isReturn},on:{change:e.supplierChange},model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}},e._l(e.SupplierList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),2==e.form.InStoreType&&e.form.Is_Replace_Purchase?i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnit"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"甲方单位",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus||e.isReturn},on:{change:e.partyUnitChange},model:{value:e.form.PartyUnit,callback:function(t){e.$set(e.form,"PartyUnit",t)},expression:"form.PartyUnit"}},e._l(e.PartyUnitList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),1==e.form.InStoreType||2==e.form.InStoreType||3==e.form.InStoreType?i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"车牌号",prop:"CarNumber"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"车牌号",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("CarNumber")}},model:{value:e.form.CarNumber,callback:function(t){e.$set(e.form,"CarNumber",t)},expression:"form.CarNumber"}})],1)],1):e._e(),1==e.form.InStoreType||2==e.form.InStoreType||3==e.form.InStoreType?i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"司机信息",prop:"Driver"}},[i("el-input",{staticStyle:{width:"35%","margin-right":"10px"},attrs:{disabled:e.isView||e.isReturn,placeholder:"姓名",type:"text"},on:{change:function(t){return e.changeFormKey("Driver")}},model:{value:e.form.Driver,callback:function(t){e.$set(e.form,"Driver",t)},expression:"form.Driver"}}),i("el-input",{staticStyle:{width:"calc(100% - 35% - 10px)"},attrs:{disabled:e.isView||e.isReturn,placeholder:"手机号",type:"text"},on:{change:function(t){return e.changeFormKey("DriverMobile")}},model:{value:e.form.DriverMobile,callback:function(t){e.$set(e.form,"DriverMobile",t)},expression:"form.DriverMobile"}})],1)],1):e._e(),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"整车磅重(t)",prop:"Car_Pound_Weight"}},[i("el-input",{staticClass:"input-number",staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"整车磅重",type:"number"},on:{blur:function(t){return e.blurWeight("Car_Pound_Weight")}},model:{value:e.form.Car_Pound_Weight,callback:function(t){e.$set(e.form,"Car_Pound_Weight",t)},expression:"form.Car_Pound_Weight"}})],1)],1),i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"整车凭证重(t)",prop:"Car_Voucher_Weight"}},[i("el-input",{staticClass:"input-number",staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"整车凭证重",type:"number"},on:{blur:function(t){return e.blurWeight("Car_Voucher_Weight")}},model:{value:e.form.Car_Voucher_Weight,callback:function(t){e.$set(e.form,"Car_Voucher_Weight",t)},expression:"form.Car_Voucher_Weight"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,rows:1,"show-word-limit":"",maxlength:100,placeholder:"备注",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[i("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:5,multiple:!0,"on-success":function(t,i,a){e.uploadSuccess(t,i,a)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"file-list":e.fileListData,"show-file-list":!0,disabled:e.isView||3===e.formStatus||e.isReturn}},[e.isView||3===e.formStatus||e.isReturn?e._e():i("el-button",{attrs:{type:"primary"}},[e._v("上传文件")])],1)],1)],1)],1)],1)],1),i("el-card",{staticClass:"box-card box-card-tb"},[i("div",{staticClass:"toolbar-container"},[i("div",{staticClass:"toolbar-title"},[i("span"),e._v("入库明细")]),i("div",{staticClass:"statistics-container"},[[i("div",{staticClass:"statistics-item"},[i("span",{staticStyle:{"margin-right":"16px"}},[e._v("数量")]),i("span",[e._v(e._s(Number(e.statisticsData.InStoreCountTotal.toFixed(2))))])]),i("div",{staticClass:"statistics-item"},[i("span",{staticStyle:{"margin-right":"16px"}},[e._v("理重(t)")]),i("span",[e._v(e._s(e.statisticsData.InStoreWeightTotal.toFixed(5)/1))])]),i("div",{staticClass:"statistics-item"},[i("span",{staticStyle:{"margin-right":"16px"}},[e._v("凭证重(t)")]),i("span",[e._v(e._s(e.statisticsData.VoucherWeightTotal.toFixed(3)/1))])]),i("div",{staticClass:"statistics-item"},[i("span",{staticStyle:{"margin-right":"16px"}},[e._v("磅重(t)")]),i("span",[e._v(e._s(e.statisticsData.PoundWeightTotal.toFixed(3)/1))])]),i("div",{staticClass:"statistics-item"},[i("span",{staticStyle:{"margin-right":"16px"}},[e._v("含税总价")]),i("span",[e._v(e._s(e.statisticsData.Tax_All_Price.toFixed(3)/1))])])]],2)]),i("el-divider",{staticClass:"elDivder"}),i("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"0px"}},[i("el-form",{ref:"searchForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"原料全名",prop:"RawNameFull"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"通配符%",clearable:""},model:{value:e.searchForm.RawNameFull,callback:function(t){e.$set(e.searchForm,"RawNameFull",t)},expression:"searchForm.RawNameFull"}})],1),i("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[i("el-input",{staticStyle:{"min-width":"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),i("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),i("el-form-item",{attrs:{label:"材质",prop:"Material","label-width":"50px"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Material,callback:function(t){e.$set(e.searchForm,"Material",t)},expression:"searchForm.Material"}})],1),i("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.SysProjectId,callback:function(t){e.$set(e.searchForm,"SysProjectId",t)},expression:"searchForm.SysProjectId"}},e._l(e.ProjectList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),e.filterVisible?[i("el-form-item",{attrs:{label:"原料分类",prop:"CategoryId"}},[i("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.searchForm.CategoryId,callback:function(t){e.$set(e.searchForm,"CategoryId",t)},expression:"searchForm.CategoryId"}})],1),i("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Width,callback:function(t){e.$set(e.searchForm,"Width",t)},expression:"searchForm.Width"}})],1),i("el-form-item",{attrs:{label:"长度",prop:"Length"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Length,callback:function(t){e.$set(e.searchForm,"Length",t)},expression:"searchForm.Length"}})],1),i("el-form-item",{attrs:{label:"仓库",prop:"WarehouseId"}},[i("SelectWarehouse",{model:{value:e.searchForm.WarehouseId,callback:function(t){e.$set(e.searchForm,"WarehouseId",t)},expression:"searchForm.WarehouseId"}})],1),i("el-form-item",{attrs:{label:"库位",prop:"LocationId"}},[i("SelectLocation",{attrs:{"warehouse-id":e.searchForm.WarehouseId},model:{value:e.searchForm.LocationId,callback:function(t){e.$set(e.searchForm,"LocationId",t)},expression:"searchForm.LocationId"}})],1),1==e.form.InStoreType||3==e.form.InStoreType?i("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商",clearable:"",filterable:""},model:{value:e.searchForm.Supplier,callback:function(t){e.$set(e.searchForm,"Supplier",t)},expression:"searchForm.Supplier"}},e._l(e.SupplierList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),2==e.form.InStoreType?i("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnit"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"甲方单位",clearable:"",filterable:""},model:{value:e.searchForm.PartyUnit,callback:function(t){e.$set(e.searchForm,"PartyUnit",t)},expression:"searchForm.PartyUnit"}},e._l(e.PartyUnitList,(function(e){return i("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e()]:e._e(),i("el-form-item",{staticClass:"last-btn"},[i("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),i("el-button",{staticClass:"reset-btn",on:{click:e.handleReset}},[e._v("重置")]),i("el-button",{on:{click:e.toggleFilter}},[e._v(e._s(e.filterVisible?"收起筛选":"展开筛选"))]),e.rootColumns?i("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-columns":e.rootColumns,"table-config-code":e.tableConfigCode,"manual-hide-columns":e.manualHideTableColumns,type:e.BigType},on:{updateColumn:e.updateColumn}}):e._e()],1)],2)],1),i("el-divider",{staticClass:"elDivder"}),e.isView?e._e():i("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"8px"}},[e.isView||e.isReturn?e._e():i("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),i("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")]),i("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"primary",plain:""},on:{click:e.handleBatchEdit}},[e._v("批量编辑 ")]),i("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleWarehouse(!1)}}},[e._v("批量选择仓库/库位 ")]),i("el-button",{on:{click:e.handleImport}},[e._v("导入")])]},proxy:!0}],null,!1,2779568447)})],1),i("div",{staticClass:"tb-x"},[i(e.currentTbComponent,{ref:"table",tag:"component",attrs:{"is-view":e.isView,"big-type-data":e.BigType,"check-type-list":e.checkTypeList,"is-replace-purchase":e.form.Is_Replace_Purchase,"project-list":e.ProjectList,"supplier-list":e.SupplierList,"party-unit-list":e.PartyUnitList,"form-status":e.formStatus||0},on:{changeStandard:e.changeStandard,changeWarehouse:e.changeWarehouse,select:e.setSelectRow,updateRow:e.handleUpdateRow,updateTb:e.handleUpdateTb,changeKeyInfo:e.changeKeyInfo}})],1),e.isView?e._e():i("el-divider",{staticClass:"elDivder"}),e.isView?e._e():i("footer",[i("div",{staticClass:"data-info"},[e.isReturn?e._e():i("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),i("div",[i("el-button",{on:{click:e.closeView}},[e._v("取消")]),88===e.formStatus?i("el-button",{attrs:{loading:e.returning,type:"primary"},on:{click:e.handleReturn}},[e._v("确认退货")]):[3!==e.formStatus?i("el-button",{attrs:{loading:e.saveLoading,disabled:e.submitLoading},on:{click:e.handleSaveDraft}},[e._v("保存草稿 ")]):e._e(),i("el-button",{attrs:{type:"primary",disabled:e.saveLoading,loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v("提交入库")])]],2)])],1),e.dialogVisible?i("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[i(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":0,"project-list":e.ProjectList,"supplier-list":e.SupplierList,"party-unit-list":e.PartyUnitList,"is-replace-purchase":e.form.Is_Replace_Purchase,"check-type-list":e.checkTypeList},on:{close:e.handleClose,warehouse:e.getWarehouse,batchEditor:e.batchEditorFn,importData:e.importData,standard:e.getStandard,refresh:e.fetchData,getAddList:function(t){return e.getAddList(t,!0)}}})],1):e._e(),i("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增原料",visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[e.isPurchase?i("add-purchase-list",{ref:"draft",attrs:{"is-single":e.isSingle,"big-type-data":e.BigType,"form-data":e.form,"project-list":e.ProjectList,"joined-items":e.rootTableData},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}}):i("add-raw-material-list",{ref:"draft",attrs:{"is-purchasing":e.form.Is_Replace_Purchase,"is-single":e.isSingle,"is-manual":e.isManual,"is-purchase":e.isPurchase,"is-customer":e.isCustomer,"project-id":e.form.ProjectId,"project-list":e.ProjectList},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2),e.dialogRepeatVisible?i("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"检查重复",visible:e.dialogRepeatVisible,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.dialogRepeatVisible=t},close:e.handleClose}},[i("Repeat",{ref:"repeat",attrs:{columns:e.rootColumns},on:{submit:function(t){return e.submitInfo(1,!0)},close:e.handleClose}})],1):e._e()],1)},r=[]},ce0d:function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(i("2909")),n=(a(i("53ca")),a(i("c14f"))),o=a(i("1da1"));i("4de4"),i("7db0"),i("caad"),i("d81d"),i("14d9"),i("a434"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("ab43"),i("e9c4"),i("a9e3"),i("b680"),i("b64b"),i("d3b7"),i("2532"),i("159b");var s=i("ed08"),l=i("30c8"),u=i("8fea");t.default={props:{isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1},isReplacePurchase:{type:Boolean,default:!1},formStatus:{type:Number,default:0},checkTypeList:{type:Array,required:!0,default:function(){return[]}},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{tbLoading:!1,multipleSelection:[],rootColumns:[],columns:[],tbData:[],BigType:1,renderComponent:!0,num:1,itemKey:""}},inject:["checkDuplicate"],computed:{isReturn:function(){return 88===this.formStatus}},watch:{bigTypeData:{handler:function(e,t){this.BigType=e,this.columnsOption()},immediate:!1},isReplacePurchase:{handler:function(e,t){e?(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"SupplierName"!==e.Code||(e.Is_Edit=!1,e.Is_Must_Input=!1)})),this.init(this.rootColumns),this.forceRerender()):(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"SupplierName"!==e.Code||(e.Is_Edit=!0)})),this.init(this.rootColumns),this.forceRerender())},immediate:!1}},created:function(){return(0,o.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},mounted:function(){},methods:{taxAllPriceChange:function(e){e.NoTaxAllPrice=(0,l.getNoTaxAllPrice)(e)},voucherTaxAllPriceChange:function(e){e.VoucherTaxAllPrice=(0,l.getVoucherTaxAllPrice)(e),e.VoucherNoTaxAllPrice=(0,l.getVoucherNoTaxAllPrice)(e)},changeKeyInfo:function(e,t){var i=this.tbData.map((function(t){return t[e]})),a=(0,s.uniqueArr)(i);1===a.length?this.$emit("changeKeyInfo",{key:e,val:t}):this.$emit("changeKeyInfo",{key:e,val:""})},changeFormInfo:function(e,t){var i=this;this.tbData.forEach((function(a){i.$set(a,e,t)}))},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},init:function(e){e.forEach((function(e){e.Style=e.Style?JSON.parse(e.Style):""})),this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.itemKey=Math.random(),e.columns=JSON.parse(JSON.stringify(e.rootColumns));case 1:return t.a(2)}}),t)})))()},getCheckList:function(e,t){var i=this,a=[];this.checkDuplicate()&&(a=["ProjectName","Project_Code","SupplierName","CategoryName","RawNameFull","RawName","RawCode","Material","Spec","Actual_Spec","TaxUnitPrice","Tax_Rate","Warehouse_Location","FurnaceBatchNo"]);var r="Width",n="Length";this.checkTypeList.map((function(o){o.BigType===e&&(o.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),o.checkList=o.checkList.map((function(e){return e.Code})),o.checkSameList=t.filter((function(e){return a.includes(e.Code)})),o.checkSameList=o.checkSameList.map((function(e){return e.Code})),i.checkDuplicate()&&(o.checkSameList.unshift("RawCode"),2===e?o.checkSameList.push(n):1===e&&(o.checkSameList.push(r),o.checkSameList.push(n))),o.remarkList=t.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),o.remarkList=o.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}})))}))},getAllColumnsOption:function(){var e=this,t=[1,2,3],i=JSON.parse(JSON.stringify(this.rootColumns));t.forEach((function(t){e.getCheckList(t,i)}))},tbfetchData:function(){this.tbData=[]},handleCopy:function(e,t){var i=JSON.parse(JSON.stringify(e));i.Sub_Id&&(i.Sub_Id=""),delete i._X_ROW_KEY,this.$emit("updateTb",[i],"copy"),this.tbData.splice(t+1,0,i)},projectChange:function(e){if(e.SysProjectId){var t=this.projectList.find((function(t){return t.Sys_Project_Id===e.SysProjectId}));if(t){e.ProjectName=t.Short_Name,e.Project_Code=t.Code;var i=e.Versions.find((function(e){return e.Id===t.Version_Id}));e.Specific_Gravity=i.Specific_Gravity,e.Version_Id=t.Version_Id}this.checkWeight(e)}else e.ProjectName="";this.$emit("updateRow")},supplierChange:function(e){e.Supplier?e.SupplierName=this.supplierList.find((function(t){return t.Id===e.Supplier})).Name:e.SupplierName="",this.$emit("updateRow")},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,i=this,a=e.map((function(e){return e.Spec=1===e.BigType?e.Thick:e.Spec,e.Actual_Spec=e.Actual_Spec||(1===e.BigType?e.Thick:e.Spec),e.RawNameFull=(0,l.getMaterialName)(e),e.TaxUnitPrice=e.TaxUnitPrice||0,"number"!==typeof e.Tax_Rate&&(e.Tax_Rate=13),e.Specific_Gravity&&3!==e.BigType?i.checkWeight(e):i.countTaxUnitPrice(e,"InStoreCount_Width_Length"),e.rowIndex="",e}));(t=this.tbData).push.apply(t,(0,r.default)(a)),this.tbData=JSON.parse(JSON.stringify(this.tbData))},setRowFullName:function(e){e.Raw_FullName=(0,l.getMaterialName)(e)},lengthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},widthChange:function(e){this.checkWeight(e),this.setRowFullName(e)},checkWeight:function(e){0!==e.Specific_Gravity&&!e.Specific_Gravity||3===e.BigType||(1!==e.BigType&&2!==e.BigType||0!==e.Specific_Gravity&&0!==e.Length?1===e.BigType?"花纹板"===e.CategoryName?0===e.Length||0===e.Width?e.InStoreWeightKG=0:e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":0===e.Thick||0===e.Width?e.InStoreWeightKG=0:e.Thick&&e.Width&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Thick*e.Width*e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":2===e.BigType&&e.Length&&e.Specific_Gravity?e.InStoreWeightKG=Number((e.Length*e.Specific_Gravity*e.InStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.InStoreWeightKG="":e.InStoreWeightKG=0,e.InStoreWeight=e.InStoreWeightKG||0===e.InStoreWeightKG?Number((e.InStoreWeightKG/1e3).toFixed(5)):""),this.countTaxUnitPrice(e,"InStoreCount_Width_Length")},countTaxUnitPrice:function(e,t){"InStoreWeight"===t?e.InStoreWeightKG=e.InStoreWeight||0===e.InStoreWeight?Number((1e3*e.InStoreWeight).toFixed(u.WEIGHT_KG_DECIMAL)):"":"Pound_Weight"===t?e.Pound_Weight_KG=e.Pound_Weight||0===e.Pound_Weight?Number((1e3*e.Pound_Weight).toFixed(u.WEIGHT_KG_DECIMAL)):"":"Voucher_Weight"===t&&(e.Voucher_Weight_KG=e.Voucher_Weight||0===e.Voucher_Weight?Number((1e3*e.Voucher_Weight).toFixed(u.WEIGHT_KG_DECIMAL)):""),"TaxUnitPrice"===t&&(e.TaxUnitPriceKG=Number((e.TaxUnitPrice/1e3).toFixed(6))),"TaxUnitPrice"!==t&&"Tax_Rate"!==t||(e.NoTaxUnitPrice=e.TaxUnitPrice&&e.Tax_Rate?Number((e.TaxUnitPrice/(1+e.Tax_Rate/100)).toFixed(6)):0),"TaxUnitPrice"!==t&&"InStoreWeight"!==t&&"InStoreCount_Width_Length"!==t||(99===e.BigType?0===e.InStoreCount||0===e.TaxUnitPrice?e.Tax_All_Price=0:e.InStoreCount&&e.TaxUnitPrice?e.Tax_All_Price=Number((e.InStoreCount*e.TaxUnitPrice).toFixed(2)):e.Tax_All_Price="":0===e.InStoreWeightKG||0===e.TaxUnitPriceKG?e.Tax_All_Price=0:e.InStoreWeightKG&&e.TaxUnitPriceKG?e.Tax_All_Price=Number((e.InStoreWeightKG*e.TaxUnitPriceKG).toFixed(2)):e.Tax_All_Price=""),this.taxAllPriceChange(e),this.voucherTaxAllPriceChange(e),this.$emit("updateRow")},tableEditData:function(e){e.RawNameFull=(0,l.getMaterialName)(e),this.$emit("updateRow")},supplierNameChange:function(e,t){var i=this.supplierList.find((function(t){return t.Id===e.value}));t.ReturnSupplierName=i?i.Name:""},outStoreChange:function(e){var t=e.Specific_Gravity,i=0;i=t?1===e.BigType?"花纹板"===e.CategoryName?Number((e.Width*e.Length*t*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):Number((e.Width*e.Length*Number(e.Spec)*t*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):2===e.BigType?Number((e.Length*t*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):Number((e.PerWeight*e.OutStoreCount).toFixed(u.WEIGHT_KG_DECIMAL)):e.PerWeight*e.OutStoreCount,e.OutStoreWeight=(i/1e3).toFixed(u.WEIGHT_KG_DECIMAL)/1,e.ReturnVoucherWeight=(e.PerVoucherWeight*e.OutStoreCount/1e3).toFixed(u.WEIGHT_KG_DECIMAL)/1,this.$emit("updateRow")}}}},d0a4:function(e,t,i){"use strict";i("9e4a")},d111:function(e,t,i){"use strict";i.r(t);var a=i("6dcb"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},d37f:function(e,t,i){"use strict";i.r(t);var a=i("f466"),r=i("81028");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"316889ed",null);t["default"]=s.exports},d3d1:function(e,t,i){"use strict";i("6de4")},d56b:function(e,t,i){},dbdd:function(e,t,i){"use strict";i.r(t);var a=i("14fb"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},e152:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.renderComponent?i("vxe-table",{key:e.itemKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checCheckboxkMethod},"row-style":e.rowStyle},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[i("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e.isView||e.isReturn?e._e():i("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60",title:""}}),e.isView||e.isReturn?e._e():i("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,r=t.rowIndex;return[i("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&a.Sub_Id)},on:{click:function(t){return e.handleCopy(a,r)}}},[e._v("复制")])]}}],null,!1,2613506362)}),e._l(e.columns,(function(t){return[i("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([t.Style.tips?{key:"header",fn:function(){return[i("span",[e._v(e._s(t.Display_Name))]),i("el-tooltip",{staticClass:"item",attrs:{effect:"dark"}},[i("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(t.Style.tips)},slot:"content"}),i("i",{staticClass:"el-icon-question",staticStyle:{cursor:"pointer","font-size":"16px"}})])]},proxy:!0}:null,{key:"default",fn:function(a){var r=a.row;return["NoTaxUnitPrice"===t.Code?[e._v(" "+e._s(Number((r.TaxUnitPrice/(1+r.Tax_Rate/100)).toFixed(5)))+" ")]:[i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]]}},t.Is_Edit?{key:"edit",fn:function(a){var r=a.row;return["Actual_Spec"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Width"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType||2===r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.widthChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Length"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.lengthChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"InStoreCount"===t.Code?i("div",[i("vxe-input",{ref:"numberInput",refInFor:!0,attrs:{min:0,controls:!1,type:"number"},on:{change:function(t){return e.checkWeight(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"InStoreWeight"===t.Code?i("div",[r.Specific_Gravity&&3!==r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:5,min:0},expression:"{ toFixed: 5, min: 0 }"}],attrs:{type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"InStoreWeight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"PurchaseWeight"===t.Code?i("div",[r.Specific_Gravity&&3!==r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:5,min:0},expression:"{ toFixed: 5, min: 0 }"}],attrs:{type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"PurchaseWeight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Pound_Weight"===t.Code?i("div",[i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Pound_Weight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):t.Code.includes("Remark")?i("div",[i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Voucher_Weight"===t.Code?i("div",[i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Voucher_Weight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("div",[e._v(" "+e._s(r.WarehouseName)+"/"+e._s(r.LocationName)+" "),i("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(r)}}})])]):"FurnaceBatchNo"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r.FurnaceBatchNo,callback:function(t){e.$set(r,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})],1):"TaxUnitPrice"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"TaxUnitPrice")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"ReturnPoundWeight"===t.Code?i("div",[i("vxe-input",{model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Delivery_No"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("Delivery_No",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"CarNumber"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("CarNumber",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Driver"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("Driver",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"DriverMobile"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("DriverMobile",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"OutStoreCount"===t.Code?i("div",[3===e.formStatus&&r.OutStoreCount?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{max:r.AvailableCount,min:0,type:"number"},on:{change:function(t){return e.outStoreChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},r=[]},ec55:function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("d81d"),i("e9f5"),i("ab43"),i("e9c4"),i("a9e3"),i("b680"),i("d3b7");var r=a(i("3796")),n=i("ed08"),o=i("93aa");t.default={components:{Upload:r.default},data:function(){return{btnLoading:!1,schdulingPlanId:""}},inject:["formData"],mounted:function(){},methods:{getTemplate:function(){var e=this;(0,o.GetImportTemplate)({inStoreType:this.formData.InStoreType}).then((function(t){window.open((0,n.combineURL)(e.$baseUrl,t.Data))}))},beforeUpload:function(e){var t=this,i={In_Store_Type:this.formData.InStoreType,Is_Replace_Purchase:this.formData.Is_Replace_Purchase,SysProjectId:this.formData.SysProjectId,PartyUnit:this.formData.PartyUnit,Supplier:this.formData.Supplier},a=new FormData;a.append("Receipt",JSON.stringify(i)),a.append("Files",e),this.btnLoading=!0,(0,o.Import)(a).then((function(e){if(e.IsSucceed){var i=e.Data?e.Data.map((function(e){return e.TaxUnitPrice=Number((e.TaxUnitPrice/1e3).toFixed(3)),e.TaxUnitPriceKG=e.TaxUnitPrice,e})):[];t.$emit("getAddList",i),t.$message({type:"success",message:"导入成功"}),t.$emit("close")}else t.$message({type:"error",message:e.Message}),e.Data&&window.open((0,n.combineURL)(t.$baseUrl,e.Data));t.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},edce:function(e,t,i){"use strict";i.r(t);var a=i("ad97"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},f466:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.renderComponent?i("vxe-table",{key:e.itemKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"","auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checCheckboxkMethod}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[i("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e.isView||e.isReturn?e._e():i("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60",title:""}}),e.isView||e.isReturn?e._e():i("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,r=t.rowIndex;return[i("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&a.Sub_Id)},on:{click:function(t){return e.handleCopy(a,r)}}},[e._v("复制")])]}}],null,!1,2613506362)}),e._l(e.columns,(function(t){return[i("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([t.Style.tips?{key:"header",fn:function(){return[i("span",[e._v(e._s(t.Display_Name))]),i("el-tooltip",{staticClass:"item",attrs:{effect:"dark"}},[i("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(t.Style.tips)},slot:"content"}),i("i",{staticClass:"el-icon-question",staticStyle:{cursor:"pointer","font-size":"16px"}})])]},proxy:!0}:null,{key:"default",fn:function(a){var r=a.row;return[i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(a){var r=a.row;return["Actual_Thick"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Width"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType||2===r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.widthChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Length"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id||3===r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.lengthChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Material"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.tableEditRemark(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):t.Code.includes("Remark")?i("div",[i("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.tableEditData(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"InStoreCount"===t.Code?i("div",[i("vxe-input",{attrs:{controls:!1,min:0,type:"number"},on:{change:function(t){return e.checkWeight(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"InStoreWeight"===t.Code?i("div",[r.Specific_Gravity&&3!==r.BigType?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:5,min:0},expression:"{ toFixed: 5, min: 0 }"}],attrs:{type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"InStoreWeight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Pound_Weight"===t.Code?i("div",[i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Pound_Weight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Voucher_Weight"===t.Code?i("div",[i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Voucher_Weight")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("div",[e._v(" "+e._s(r.WarehouseName)+"/"+e._s(r.LocationName)+" "),i("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(r)}}})])]):"FurnaceBatchNo"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r.FurnaceBatchNo,callback:function(t){e.$set(r,"FurnaceBatchNo",t)},expression:"row.FurnaceBatchNo"}})],1):"ProjectName"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-select",{attrs:{filterable:"",transfer:""},on:{change:function(t){return e.projectChange(t,r)}},model:{value:r.SysProjectId,callback:function(t){e.$set(r,"SysProjectId",t)},expression:"row.SysProjectId"}},e._l(e.projectList,(function(e){return i("vxe-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Short_Name}})})),1)],1):"PartyUnitName"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-select",{attrs:{filterable:"",transfer:""},on:{change:function(t){return e.partyUnitNameChange(r)}},model:{value:r.PartyUnit,callback:function(t){e.$set(r,"PartyUnit",t)},expression:"row.PartyUnit"}},e._l(e.partyUnitList,(function(e){return i("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):"TaxUnitPrice"===t.Code?i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"TaxUnitPrice")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"Delivery_No"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("Delivery_No",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Tax_Rate"===t.Code?i("div",[i("vxe-input",{attrs:{type:"number"},on:{change:function(t){return e.countTaxUnitPrice(r,"Tax_Rate")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):"CarNumber"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("CarNumber",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"Driver"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("Driver",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"DriverMobile"===t.Code?i("div",[i("vxe-input",{on:{change:function(i){return e.changeKeyInfo("DriverMobile",r[t.Code])}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1):"OutStoreCount"===t.Code?i("div",[3===e.formStatus&&r.OutStoreCount?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{max:r.AvailableCount,min:0,type:"number"},on:{change:function(t){return e.outStoreChange(r)}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,e._n(i))},expression:"row[item.Code]"}})],1):i("div",[3===e.formStatus&&r.Sub_Id?i("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))]):i("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:r[t.Code],callback:function(i){e.$set(r,t.Code,i)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},r=[]},fcac:function(e,t,i){"use strict";i.r(t);var a=i("ce0d"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a},fd11:function(e,t,i){"use strict";i.r(t);var a=i("c9d7"),r=i("29c6");for(var n in r)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(n);i("56e7");var o=i("2877"),s=Object(o["a"])(r["default"],a["a"],a["b"],!1,null,"f202a17a",null);t["default"]=s.exports},fdfa:function(e,t,i){"use strict";i.r(t);var a=i("06b4"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=r.a}}]);