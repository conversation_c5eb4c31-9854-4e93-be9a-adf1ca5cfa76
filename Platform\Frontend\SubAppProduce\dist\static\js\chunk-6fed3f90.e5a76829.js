(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6fed3f90"],{"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),i=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,r=t.Data,l=t.Message;if(a){if(!r)return void e.$message({message:"表格配置不存在",type:"error"});var u=[];e.tbConfig=Object.assign({},e.tbConfig,r.Grid),u=n?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=u.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+r.Grid.Row_Number||i.tablePageSize[0]),o(e.columns)}else e.$message({message:l,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var i=this.columns[a];if(i.Code===e){n.Type=i.Type,n.Filter_Type=i.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"233f":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.loading?t._e():n("main-page",{ref:"main",attrs:{"tb-config-code":t.tbConfigCode,"name-list":t.nameList,"add-page-array":t.addPageArray,"tb-data":t.tbData,total:t.total,"default-page":t.defaultPage},on:{"update:defaultPage":function(e){t.defaultPage=e},"update:default-page":function(e){t.defaultPage=e},fetchData:t.fetchData}})},i=[]},"2e8e":function(t,e,n){"use strict";n.r(e);var a=n("233f"),i=n("f05d9");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"4417f6e1",null);e["default"]=l.exports},3749:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("c14f")),o=a(n("1da1"));n("14d9"),n("a9e3");var r=a(n("34e9")),l=a(n("0f97")),u=a(n("15ac")),c=a(n("3dcb")),s=a(n("2082")),d=a(n("4b32")),f=n("f2f6");e.default={components:{TopHeader:r.default,DynamicDataTable:l.default,BtnGroup:c.default,CheckInfo:d.default},mixins:[u.default,s.default],props:{nameList:{type:Object,default:function(){}},addPageArray:{type:Array,default:function(){return[]}},tbData:{type:Array,default:function(){return[]}},tbConfigCode:{type:String,default:""},total:{type:Number,default:0},defaultPage:{type:Number,default:0}},data:function(){return{btnOptions:[{label:"单据",value:0},{label:"明细",value:1}],planTime:"",options:[],value:"",tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},tableData:[],columns:[],unitOption:[],tbLoading:!0}},computed:{typeBtn:{get:function(){return this.defaultPage},set:function(t){this.$emit("update:defaultPage",t)}}},watch:{defaultPage:function(t,e){this.init()}},mounted:function(){this.init()},methods:{init:function(){var t=this;return(0,o.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return t.tbLoading=!0,e.n=1,t.getTableConfig(t.tbConfigCode);case 1:t.fetchData(),t.getSearchType();case 2:return e.a(2)}}),e)})))()},fetchData:function(){this.$emit("fetchData",this.queryInfo)},getSearchType:function(){var t=this;(0,f.GetProjectInstallUnitList)({}).then((function(e){e.IsSucceed?t.unitOption=e.Data:t.$message({message:e.Message,type:"error"})}))},showTable:function(){this.tbLoading=!1},handleAdd:function(){this.$router.push({name:this.nameList.add,query:{pg_redirect:this.nameList.parent}})},handleEdit:function(t){this.$router.push({name:this.nameList.edit,query:{pg_redirect:this.nameList.parent,id:t}})},handleInfo:function(t){this.$router.push({name:this.nameList.detail,query:{pg_redirect:this.nameList.parent,id:t}})},handleInfoDetail:function(t){t.Unique_Code=t.unique_code,this.$refs.info.handleOpen(t)}}}},"3f35":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportPackingList=f,e.GeneratePackCode=r,e.GetPacking2ndEntity=u,e.GetPacking2ndPageList=l,e.GetPackingGroupByDirectDetailList=o,e.GetWaitPack2ndPageList=c,e.SavePacking2nd=d,e.UnzipPacking2nd=s;var i=a(n("b775"));a(n("4328"));function o(t){return(0,i.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function r(){return(0,i.default)({url:"/PRO/Packing/GeneratePackCode",method:"post"})}function l(t){return(0,i.default)({url:"/PRO/Packing/GetPacking2ndPageList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/Packing/GetPacking2ndEntity",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/Packing/GetWaitPack2ndPageList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/Packing/UnzipPacking2nd",method:"post",data:t})}function d(t){return(0,i.default)({url:"/PRO/Packing/SavePacking2nd",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/Packing/ExportPackingList",method:"post",data:t})}},"40d1":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n("3f35");e.default={data:function(){return{innerTableData:[],dialogVisible:!1}},methods:{handleInfo:function(t){var e=this;(0,a.GetPackingGroupByDirectDetailList)({Unique_Code:t.Unique_Code}).then((function(t){t.IsSucceed?e.innerTableData=t.Data:e.$message({message:t.Message,type:"error"})}))},handleOpen:function(t){this.dialogVisible=!0,this.handleInfo(t)},handleClose:function(){}}}},"4b32":function(t,e,n){"use strict";n.r(e);var a=n("a729"),i=n("939e");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"784abcb7",null);e["default"]=l.exports},"4e82":function(t,e,n){"use strict";var a=n("23e7"),i=n("e330"),o=n("59ed"),r=n("7b0b"),l=n("07fa"),u=n("083a"),c=n("577e"),s=n("d039"),d=n("addb"),f=n("a640"),p=n("3f7e"),h=n("99f4"),m=n("1212"),g=n("ea83"),b=[],P=i(b.sort),v=i(b.push),y=s((function(){b.sort(void 0)})),C=s((function(){b.sort(null)})),k=f("sort"),I=!s((function(){if(m)return m<70;if(!(p&&p>3)){if(h)return!0;if(g)return g<603;var t,e,n,a,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)b.push({k:e+a,v:n})}for(b.sort((function(t,e){return e.v-t.v})),a=0;a<b.length;a++)e=b[a].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),_=y||!C||!k||!I,S=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:c(e)>c(n)?1:-1}};a({target:"Array",proto:!0,forced:_},{sort:function(t){void 0!==t&&o(t);var e=r(this);if(I)return void 0===t?P(e):P(e,t);var n,a,i=[],c=l(e);for(a=0;a<c;a++)a in e&&v(i,e[a]);d(i,S(t)),n=l(i),a=0;while(a<n)e[a]=i[a++];while(a<c)u(e,a++);return e}})},"62d4":function(t,e,n){"use strict";n.r(e);var a=n("739e"),i=n("ecd3");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("dcc0");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"e8c56f9c",null);e["default"]=l.exports},"630a":function(t,e,n){},"739e":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[n("div",{staticClass:" cs-z-page-main-content"},[n("top-header",{scopedSlots:t._u([{key:"left",fn:function(){return[n("el-form",{staticClass:"cs-form",attrs:{inline:""}},[n("el-form-item",[n("btn-group",{attrs:{size:"mini",options:t.btnOptions},model:{value:t.typeBtn,callback:function(e){t.typeBtn=e},expression:"typeBtn"}})],1)],1)]},proxy:!0},0===t.defaultPage?{key:"right",fn:function(){return[n("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新 增")])]},proxy:!0}:null],null,!0)}),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[t.tbLoading?t._e():n("dynamic-data-table",{ref:"dyTable",attrs:{columns:t.columns,config:t.tbConfig,data:t.tbData,page:t.queryInfo.Page,total:t.total,border:"",stripe:""},on:{gridPageChange:t.handlePageChange,gridSizeChange:t.handlePageChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"Return_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Return_Date))+" ")]}},{key:"Cancel_Date",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Cancel_Date))+" ")]}},{key:"hsearch_pi_name",fn:function(e){var a=e.column;return[n("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:t.showSearchBtn},model:{value:t.$refs.dyTable.searchedField[a.Code],callback:function(e){t.$set(t.$refs.dyTable.searchedField,a.Code,e)},expression:"$refs.dyTable.searchedField[column.Code]"}},t._l(t.unitOption,(function(t,e){return n("el-option",{key:e,attrs:{label:t.pi_name,value:t.pi_name}})})),1)]}},{key:"op",fn:function(e){var a=e.row,i=e.index;return[0===t.defaultPage?n("span",[n("el-button",{attrs:{index:i,type:"text"},on:{click:function(e){return t.handleEdit(a.Id)}}},[t._v("编辑")]),n("el-button",{attrs:{index:i,type:"text"},on:{click:function(e){return t.handleInfo(a.Id)}}},[t._v("查看")])],1):t._e(),1===t.defaultPage&&"打包件"===a.c_type?n("el-button",{attrs:{index:i,type:"text"},on:{click:function(e){return t.handleInfoDetail(a)}}},[t._v("查看")]):t._e()]}}],null,!1,776074831)})],1),n("check-info",{ref:"info"})],1)])},i=[]},"939e":function(t,e,n){"use strict";n.r(e);var a=n("40d1"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},a729:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{title:"提示","append-to-body":"",visible:t.dialogVisible,width:"50%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n("el-table",{staticClass:"inner-tb",staticStyle:{width:"100%"},attrs:{data:t.innerTableData}},[n("el-table-column",{attrs:{align:"center",prop:"InstallUnit_Name",label:"生产单元"}}),n("el-table-column",{attrs:{align:"center",prop:"Component_Code",label:"编号",width:"180"}}),n("el-table-column",{attrs:{align:"center",prop:"Unique_Code",label:"唯一码"}}),n("el-table-column",{attrs:{align:"center",prop:"Spec",label:"规格型号"}}),n("el-table-column",{attrs:{align:"center",prop:"Num",label:"数量"}}),n("el-table-column",{attrs:{align:"center",prop:"NetWeight",label:"总重（kg）"}})],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("确 定")])],1)],1)},i=[]},abfb:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.ExportCancelStockInfo=h,e.GetInStockPageList=s,e.GetStockCancelDetailList=u,e.GetStockCancelDetailPageList=l,e.GetStockCancelDocEntity=c,e.GetStockCancelDocPageList=r,e.RemoveDetail=f,e.RemoveMain=p,e.SaveComponentStockCancel=d;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDocPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDetailPageList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDetailList",method:"post",data:o.default.stringify(t)})}function c(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/GetStockCancelDocEntity",method:"post",data:o.default.stringify(t)})}function s(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/GetInStockPageList",method:"post",data:t})}function d(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/SaveComponentStockCancel",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/RemoveDetail",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/RemoveMain",method:"post",data:o.default.stringify(t)})}function h(t){return(0,i.default)({url:"/PRO/ComponentStockCancel/ExportCancelStockInfo",method:"post",data:o.default.stringify(t)})}},b616:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0"),n("d3b7"),n("3ca3"),n("ddb0");var i=a(n("62d4")),o=n("abfb");e.default={name:"PROReturnStocks",components:{mainPage:i.default},data:function(){return{addPageArray:[],loading:!0,nameList:{parent:this.$route.name,add:"PROReturnStacksAdd",edit:"PROReturnStacksEdit",detail:"PROReturnStacksDetail"},defaultPage:0,tbData:[],total:0}},computed:{name:function(){return this.$route.name},tbConfigCode:function(){return 0===this.defaultPage?"pro_cancel_stock_bill_list":"pro_total_cancel_stock_list"}},mounted:function(){this.initRouter()},methods:{fetchData:function(t){0===this.defaultPage?this.fetchDocPage(t):this.fetchDetailPage(t)},fetchDetailPage:function(t){var e=this;(0,o.GetStockCancelDetailPageList)(t).then((function(t){var n=t.IsSucceed,a=t.Message,i=t.Data;n?(e.tbData=i.Data,e.total=i.TotalCount,e.$refs.main.showTable()):e.$message({message:a,type:"error"})}))},fetchDocPage:function(t){var e=this;(0,o.GetStockCancelDocPageList)(t).then((function(t){var n=t.IsSucceed,a=t.Message,i=t.Data;n?(e.tbData=i.Data,e.total=i.TotalCount,e.$refs.main.showTable()):e.$message({message:a,type:"error"})}))},initRouter:function(){this.addPageArray=[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([n.e("chunk-fb4375a2"),n.e("chunk-d3a6cb88")]).then(n.bind(null,"cccb"))},name:this.nameList.add,meta:{title:"新增成品退库单"}},{path:this.$route.path+"/edit",hidden:!0,component:function(){return Promise.all([n.e("chunk-fb4375a2"),n.e("chunk-11125714")]).then(n.bind(null,"bd91"))},name:this.nameList.edit,meta:{title:"编辑退库明细"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return n.e("chunk-b82f30c8").then(n.bind(null,"103e"))},name:this.nameList.detail,meta:{title:"成品退库详情"}}],this.loading=!1}}}},dcc0:function(t,e,n){"use strict";n("630a")},ecd3:function(t,e,n){"use strict";n.r(e);var a=n("3749"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},f05d9:function(t,e,n){"use strict";n.r(e);var a=n("b616"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=u,e.CheckPlanTime=c,e.DeleteInstallUnit=p,e.GetCompletePercent=P,e.GetEntity=y,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=b,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=s,e.GetInstallUnitList=l,e.GetInstallUnitPageList=r,e.GetProjectInstallUnitList=v,e.ImportInstallUnit=m,e.InstallUnitInfoTemplate=h,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=C;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function s(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function y(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(t)})}function C(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);