(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3a3c0f1b"],{"18a5":function(t,e,n){"use strict";var i=n("23e7"),r=n("857a"),o=n("af03");i({target:"String",proto:!0,forced:o("anchor")},{anchor:function(t){return r(this,"a","name",t)}})},"4c53":function(t,e,n){"use strict";var i=n("23e7"),r=n("857a"),o=n("af03");i({target:"String",proto:!0,forced:o("sub")},{sub:function(){return r(this,"sub","","")}})},"611b":function(t,e,n){(function(e,n){t.exports=n()})("undefined"!==typeof self&&self,(function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="fb15")}({"0029":function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},"0185":function(t,e,n){var i=n("e5fa");t.exports=function(t){return Object(i(t))}},"01f9":function(t,e,n){"use strict";var i=n("2d00"),r=n("5ca1"),o=n("2aba"),s=n("32e9"),a=n("84f2"),u=n("41a0"),l=n("7f20"),c=n("38fd"),h=n("2b4c")("iterator"),d=!([].keys&&"next"in[].keys()),f="@@iterator",p="keys",g="values",v=function(){return this};t.exports=function(t,e,n,m,b,y,x){u(n,e,m);var P,_,C,E=function(t){if(!d&&t in D)return D[t];switch(t){case p:return function(){return new n(this,t)};case g:return function(){return new n(this,t)}}return function(){return new n(this,t)}},j=e+" Iterator",S=b==g,w=!1,D=t.prototype,A=D[h]||D[f]||b&&D[b],O=A||E(b),I=b?S?E("entries"):O:void 0,k="Array"==e&&D.entries||A;if(k&&(C=c(k.call(new t)),C!==Object.prototype&&C.next&&(l(C,j,!0),i||"function"==typeof C[h]||s(C,h,v))),S&&A&&A.name!==g&&(w=!0,O=function(){return A.call(this)}),i&&!x||!d&&!w&&D[h]||s(D,h,O),a[e]=O,a[j]=v,b)if(P={values:S?O:E(g),keys:y?O:E(p),entries:I},x)for(_ in P)_ in D||o(D,_,P[_]);else r(r.P+r.F*(d||w),e,P);return P}},"02f4":function(t,e,n){var i=n("4588"),r=n("be13");t.exports=function(t){return function(e,n){var o,s,a=String(r(e)),u=i(n),l=a.length;return u<0||u>=l?t?"":void 0:(o=a.charCodeAt(u),o<55296||o>56319||u+1===l||(s=a.charCodeAt(u+1))<56320||s>57343?t?a.charAt(u):o:t?a.slice(u,u+2):s-56320+(o-55296<<10)+65536)}}},"0390":function(t,e,n){"use strict";var i=n("02f4")(!0);t.exports=function(t,e,n){return e+(n?i(t,e).length:1)}},"03ca":function(t,e,n){"use strict";var i=n("f2fe");function r(t){var e,n;this.promise=new t((function(t,i){if(void 0!==e||void 0!==n)throw TypeError("Bad Promise constructor");e=t,n=i})),this.resolve=i(e),this.reject=i(n)}t.exports.f=function(t){return new r(t)}},"0a49":function(t,e,n){var i=n("9b43"),r=n("626a"),o=n("4bf8"),s=n("9def"),a=n("cd1c");t.exports=function(t,e){var n=1==t,u=2==t,l=3==t,c=4==t,h=6==t,d=5==t||h,f=e||a;return function(e,a,p){for(var g,v,m=o(e),b=r(m),y=i(a,p,3),x=s(b.length),P=0,_=n?f(e,x):u?f(e,0):void 0;x>P;P++)if((d||P in b)&&(g=b[P],v=y(g,P,m),t))if(n)_[P]=v;else if(v)switch(t){case 3:return!0;case 5:return g;case 6:return P;case 2:_.push(g)}else if(c)return!1;return h?-1:l||c?c:_}}},"0a91":function(t,e,n){n("b42c"),n("93c4"),t.exports=n("b77f")},"0bfb":function(t,e,n){"use strict";var i=n("cb7c");t.exports=function(){var t=i(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"0d58":function(t,e,n){var i=n("ce10"),r=n("e11e");t.exports=Object.keys||function(t){return i(t,r)}},"0f89":function(t,e,n){var i=n("6f8a");t.exports=function(t){if(!i(t))throw TypeError(t+" is not an object!");return t}},"103a":function(t,e,n){var i=n("da3c").document;t.exports=i&&i.documentElement},1169:function(t,e,n){var i=n("2d95");t.exports=Array.isArray||function(t){return"Array"==i(t)}},"11e9":function(t,e,n){var i=n("52a7"),r=n("4630"),o=n("6821"),s=n("6a99"),a=n("69a8"),u=n("c69a"),l=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?l:function(t,e){if(t=o(t),e=s(e,!0),u)try{return l(t,e)}catch(n){}if(a(t,e))return r(!i.f.call(t,e),t[e])}},"12fd":function(t,e,n){var i=n("6f8a"),r=n("da3c").document,o=i(r)&&i(r.createElement);t.exports=function(t){return o?r.createElement(t):{}}},"12fd9":function(t,e){},1495:function(t,e,n){var i=n("86cc"),r=n("cb7c"),o=n("0d58");t.exports=n("9e1e")?Object.defineProperties:function(t,e){r(t);var n,s=o(e),a=s.length,u=0;while(a>u)i.f(t,n=s[u++],e[n]);return t}},1938:function(t,e,n){var i=n("d13f");i(i.S,"Array",{isArray:n("b5aa")})},"196c":function(t,e){t.exports=function(t,e,n){var i=void 0===n;switch(e.length){case 0:return i?t():t.call(n);case 1:return i?t(e[0]):t.call(n,e[0]);case 2:return i?t(e[0],e[1]):t.call(n,e[0],e[1]);case 3:return i?t(e[0],e[1],e[2]):t.call(n,e[0],e[1],e[2]);case 4:return i?t(e[0],e[1],e[2],e[3]):t.call(n,e[0],e[1],e[2],e[3])}return t.apply(n,e)}},"1b55":function(t,e,n){var i=n("7772")("wks"),r=n("7b00"),o=n("da3c").Symbol,s="function"==typeof o,a=t.exports=function(t){return i[t]||(i[t]=s&&o[t]||(s?o:r)("Symbol."+t))};a.store=i},"1b8f":function(t,e,n){var i=n("a812"),r=Math.max,o=Math.min;t.exports=function(t,e){return t=i(t),t<0?r(t+e,0):o(t,e)}},"1be4":function(t,e,n){"use strict";var i=n("da3c"),r=n("a7d3"),o=n("3adc"),s=n("7d95"),a=n("1b55")("species");t.exports=function(t){var e="function"==typeof r[t]?r[t]:i[t];s&&e&&!e[a]&&o.f(e,a,{configurable:!0,get:function(){return this}})}},"1c01":function(t,e,n){var i=n("5ca1");i(i.S+i.F*!n("9e1e"),"Object",{defineProperty:n("86cc").f})},"1fa8":function(t,e,n){var i=n("cb7c");t.exports=function(t,e,n,r){try{return r?e(i(n)[0],n[1]):e(n)}catch(s){var o=t["return"];throw void 0!==o&&i(o.call(t)),s}}},"214f":function(t,e,n){"use strict";n("b0c5");var i=n("2aba"),r=n("32e9"),o=n("79e5"),s=n("be13"),a=n("2b4c"),u=n("520a"),l=a("species"),c=!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),h=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2===n.length&&"a"===n[0]&&"b"===n[1]}();t.exports=function(t,e,n){var d=a(t),f=!o((function(){var e={};return e[d]=function(){return 7},7!=""[t](e)})),p=f?!o((function(){var e=!1,n=/a/;return n.exec=function(){return e=!0,null},"split"===t&&(n.constructor={},n.constructor[l]=function(){return n}),n[d](""),!e})):void 0;if(!f||!p||"replace"===t&&!c||"split"===t&&!h){var g=/./[d],v=n(s,d,""[t],(function(t,e,n,i,r){return e.exec===u?f&&!r?{done:!0,value:g.call(e,n,i)}:{done:!0,value:t.call(n,e,i)}:{done:!1}})),m=v[0],b=v[1];i(String.prototype,t,m),r(RegExp.prototype,d,2==e?function(t,e){return b.call(t,this,e)}:function(t){return b.call(t,this)})}}},"230e":function(t,e,n){var i=n("d3f4"),r=n("7726").document,o=i(r)&&i(r.createElement);t.exports=function(t){return o?r.createElement(t):{}}},2312:function(t,e,n){t.exports=n("8ce0")},"23c6":function(t,e,n){var i=n("2d95"),r=n("2b4c")("toStringTag"),o="Arguments"==i(function(){return arguments}()),s=function(t,e){try{return t[e]}catch(n){}};t.exports=function(t){var e,n,a;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=s(e=Object(t),r))?n:o?i(e):"Object"==(a=i(e))&&"function"==typeof e.callee?"Arguments":a}},2418:function(t,e,n){var i=n("6a9b"),r=n("a5ab"),o=n("1b8f");t.exports=function(t){return function(e,n,s){var a,u=i(e),l=r(u.length),c=o(s,l);if(t&&n!=n){while(l>c)if(a=u[c++],a!=a)return!0}else for(;l>c;c++)if((t||c in u)&&u[c]===n)return t||c||0;return!t&&-1}}},"245b":function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},2621:function(t,e){e.f=Object.getOwnPropertySymbols},2695:function(t,e,n){var i=n("43c8"),r=n("6a9b"),o=n("2418")(!1),s=n("5d8f")("IE_PROTO");t.exports=function(t,e){var n,a=r(t),u=0,l=[];for(n in a)n!=s&&i(a,n)&&l.push(n);while(e.length>u)i(a,n=e[u++])&&(~o(l,n)||l.push(n));return l}},"27ee":function(t,e,n){var i=n("23c6"),r=n("2b4c")("iterator"),o=n("84f2");t.exports=n("8378").getIteratorMethod=function(t){if(void 0!=t)return t[r]||t["@@iterator"]||o[i(t)]}},"28a5":function(t,e,n){"use strict";var i=n("aae3"),r=n("cb7c"),o=n("ebd6"),s=n("0390"),a=n("9def"),u=n("5f1b"),l=n("520a"),c=n("79e5"),h=Math.min,d=[].push,f="split",p="length",g="lastIndex",v=4294967295,m=!c((function(){RegExp(v,"y")}));n("214f")("split",2,(function(t,e,n,c){var b;return b="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[p]||2!="ab"[f](/(?:ab)*/)[p]||4!="."[f](/(.?)(.?)/)[p]||"."[f](/()()/)[p]>1||""[f](/.?/)[p]?function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!i(t))return n.call(r,t,e);var o,s,a,u=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),h=0,f=void 0===e?v:e>>>0,m=new RegExp(t.source,c+"g");while(o=l.call(m,r)){if(s=m[g],s>h&&(u.push(r.slice(h,o.index)),o[p]>1&&o.index<r[p]&&d.apply(u,o.slice(1)),a=o[0][p],h=s,u[p]>=f))break;m[g]===o.index&&m[g]++}return h===r[p]?!a&&m.test("")||u.push(""):u.push(r.slice(h)),u[p]>f?u.slice(0,f):u}:"0"[f](void 0,0)[p]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,i){var r=t(this),o=void 0==n?void 0:n[e];return void 0!==o?o.call(n,r,i):b.call(String(r),n,i)},function(t,e){var i=c(b,t,this,e,b!==n);if(i.done)return i.value;var l=r(t),d=String(this),f=o(l,RegExp),p=l.unicode,g=(l.ignoreCase?"i":"")+(l.multiline?"m":"")+(l.unicode?"u":"")+(m?"y":"g"),y=new f(m?l:"^(?:"+l.source+")",g),x=void 0===e?v:e>>>0;if(0===x)return[];if(0===d.length)return null===u(y,d)?[d]:[];var P=0,_=0,C=[];while(_<d.length){y.lastIndex=m?_:0;var E,j=u(y,m?d:d.slice(_));if(null===j||(E=h(a(y.lastIndex+(m?0:_)),d.length))===P)_=s(d,_,p);else{if(C.push(d.slice(P,_)),C.length===x)return C;for(var S=1;S<=j.length-1;S++)if(C.push(j[S]),C.length===x)return C;_=P=E}}return C.push(d.slice(P)),C}]}))},"2a4e":function(t,e,n){var i=n("a812"),r=n("e5fa");t.exports=function(t){return function(e,n){var o,s,a=String(r(e)),u=i(n),l=a.length;return u<0||u>=l?t?"":void 0:(o=a.charCodeAt(u),o<55296||o>56319||u+1===l||(s=a.charCodeAt(u+1))<56320||s>57343?t?a.charAt(u):o:t?a.slice(u,u+2):s-56320+(o-55296<<10)+65536)}}},"2aba":function(t,e,n){var i=n("7726"),r=n("32e9"),o=n("69a8"),s=n("ca5a")("src"),a=n("fa5b"),u="toString",l=(""+a).split(u);n("8378").inspectSource=function(t){return a.call(t)},(t.exports=function(t,e,n,a){var u="function"==typeof n;u&&(o(n,"name")||r(n,"name",e)),t[e]!==n&&(u&&(o(n,s)||r(n,s,t[e]?""+t[e]:l.join(String(e)))),t===i?t[e]=n:a?t[e]?t[e]=n:r(t,e,n):(delete t[e],r(t,e,n)))})(Function.prototype,u,(function(){return"function"==typeof this&&this[s]||a.call(this)}))},"2aeb":function(t,e,n){var i=n("cb7c"),r=n("1495"),o=n("e11e"),s=n("613b")("IE_PROTO"),a=function(){},u="prototype",l=function(){var t,e=n("230e")("iframe"),i=o.length,r="<",s=">";e.style.display="none",n("fab2").appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write(r+"script"+s+"document.F=Object"+r+"/script"+s),t.close(),l=t.F;while(i--)delete l[u][o[i]];return l()};t.exports=Object.create||function(t,e){var n;return null!==t?(a[u]=i(t),n=new a,a[u]=null,n[s]=t):n=l(),void 0===e?n:r(n,e)}},"2b4c":function(t,e,n){var i=n("5537")("wks"),r=n("ca5a"),o=n("7726").Symbol,s="function"==typeof o,a=t.exports=function(t){return i[t]||(i[t]=s&&o[t]||(s?o:r)("Symbol."+t))};a.store=i},"2d00":function(t,e){t.exports=!1},"2d95":function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},"2ea1":function(t,e,n){var i=n("6f8a");t.exports=function(t,e){if(!i(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!i(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")}},"2f21":function(t,e,n){"use strict";var i=n("79e5");t.exports=function(t,e){return!!t&&i((function(){e?t.call(null,(function(){}),1):t.call(null)}))}},"2fdb":function(t,e,n){"use strict";var i=n("5ca1"),r=n("d2c8"),o="includes";i(i.P+i.F*n("5147")(o),"String",{includes:function(t){return!!~r(this,t,o).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"302f":function(t,e,n){var i=n("0f89"),r=n("f2fe"),o=n("1b55")("species");t.exports=function(t,e){var n,s=i(t).constructor;return void 0===s||void 0==(n=i(s)[o])?e:r(n)}},"32e9":function(t,e,n){var i=n("86cc"),r=n("4630");t.exports=n("9e1e")?function(t,e,n){return i.f(t,e,r(1,n))}:function(t,e,n){return t[e]=n,t}},"33a4":function(t,e,n){var i=n("84f2"),r=n("2b4c")("iterator"),o=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||o[r]===t)}},3425:function(t,e,n){"use strict";var i=function(){var t,e=this,n=e.$createElement,i=e._self._c||n;return i("div",{class:[(t={},t[e.classNameActive]=e.enabled,t[e.classNameDragging]=e.dragging,t[e.classNameResizing]=e.resizing,t[e.classNameDraggable]=e.draggable,t[e.classNameResizable]=e.resizable,t),e.className],style:e.style,on:{mousedown:e.elementMouseDown,touchstart:e.elementTouchDown}},[e._l(e.actualHandles,(function(t){return i("div",{key:t,class:[e.classNameHandle,e.classNameHandle+"-"+t],style:e.handleStyle(t),on:{mousedown:function(n){return n.stopPropagation(),n.preventDefault(),e.handleDown(t,n)},touchstart:function(n){return n.stopPropagation(),n.preventDefault(),e.handleTouchDown(t,n)}}},[e._t(t)],2)})),e._t("default")],2)},r=[],o=(n("1c01"),n("58b2"),n("8e6e"),n("f3e2"),n("456d"),n("85f2")),s=n.n(o);function a(t,e,n){return e in t?s()(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}n("a481"),n("28a5");var u=n("a745"),l=n.n(u);function c(t){if(l()(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}var h=n("774e"),d=n.n(h),f=n("c8bb"),p=n.n(f);function g(t){if(p()(Object(t))||"[object Arguments]"===Object.prototype.toString.call(t))return d()(t)}function v(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function m(t){return c(t)||g(t)||v()}n("ac4d"),n("8a81"),n("6c7b"),n("96cf");var b=n("795b"),y=n.n(b);function x(t,e,n,i,r,o,s){try{var a=t[o](s),u=a.value}catch(l){return void n(l)}a.done?e(u):y.a.resolve(u).then(i,r)}function P(t){return function(){var e=this,n=arguments;return new y.a((function(i,r){var o=t.apply(e,n);function s(t){x(o,i,r,s,a,"next",t)}function a(t){x(o,i,r,s,a,"throw",t)}s(void 0)}))}}function _(t){if(l()(t))return t}n("3b2b");var C=n("5d73"),E=n.n(C);function j(t,e){if(p()(Object(t))||"[object Arguments]"===Object.prototype.toString.call(t)){var n=[],i=!0,r=!1,o=void 0;try{for(var s,a=E()(t);!(i=(s=a.next()).done);i=!0)if(n.push(s.value),e&&n.length===e)break}catch(u){r=!0,o=u}finally{try{i||null==a["return"]||a["return"]()}finally{if(r)throw o}}return n}}function S(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}function w(t,e){return _(t)||j(t,e)||S()}function D(t){return"function"===typeof t||"[object Function]"===Object.prototype.toString.call(t)}function A(t,e,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,r=Math.round(e/i/t[0])*t[0],o=Math.round(n/i/t[1])*t[1];return[r,o]}function O(t,e,n){return t-e-n}function I(t,e,n){return t-e-n}function k(t,e,n){return null!==e&&t<e?e:null!==n&&n<t?n:t}function T(t,e,n){var i=t,r=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"].find((function(t){return D(i[t])}));if(!D(i[r]))return!1;do{if(i[r](e))return!0;if(i===n)return!1;i=i.parentNode}while(i);return!1}function M(t){var e=window.getComputedStyle(t);return[parseFloat(e.getPropertyValue("width"),10),parseFloat(e.getPropertyValue("height"),10)]}function L(t,e,n){t&&(t.attachEvent?t.attachEvent("on"+e,n):t.addEventListener?t.addEventListener(e,n,!0):t["on"+e]=n)}function F(t,e,n){t&&(t.detachEvent?t.detachEvent("on"+e,n):t.removeEventListener?t.removeEventListener(e,n,!0):t["on"+e]=null)}function R(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function N(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?R(Object(n),!0).forEach((function(e){a(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}n("6762"),n("2fdb"),n("d25f"),n("ac6a"),n("cadf"),n("5df3"),n("4f7f"),n("c5f6"),n("7514"),n("6b54"),n("87b3");var G={mouse:{start:"mousedown",move:"mousemove",stop:"mouseup"},touch:{start:"touchstart",move:"touchmove",stop:"touchend"}},H={userSelect:"none",MozUserSelect:"none",WebkitUserSelect:"none",MsUserSelect:"none"},B={userSelect:"auto",MozUserSelect:"auto",WebkitUserSelect:"auto",MsUserSelect:"auto"},U=G.mouse,z={replace:!0,name:"vue-draggable-resizable",props:{className:{type:String,default:"vdr"},classNameDraggable:{type:String,default:"draggable"},classNameResizable:{type:String,default:"resizable"},classNameDragging:{type:String,default:"dragging"},classNameResizing:{type:String,default:"resizing"},classNameActive:{type:String,default:"active"},classNameHandle:{type:String,default:"handle"},disableUserSelect:{type:Boolean,default:!0},enableNativeDrag:{type:Boolean,default:!1},preventDeactivation:{type:Boolean,default:!1},active:{type:Boolean,default:!1},draggable:{type:Boolean,default:!0},resizable:{type:Boolean,default:!0},lockAspectRatio:{type:Boolean,default:!1},w:{type:[Number,String],default:200,validator:function(t){return"number"===typeof t?t>0:"auto"===t}},h:{type:[Number,String],default:200,validator:function(t){return"number"===typeof t?t>0:"auto"===t}},minWidth:{type:Number,default:0,validator:function(t){return t>=0}},minHeight:{type:Number,default:0,validator:function(t){return t>=0}},maxWidth:{type:Number,default:null,validator:function(t){return t>=0}},maxHeight:{type:Number,default:null,validator:function(t){return t>=0}},x:{type:Number,default:0},y:{type:Number,default:0},z:{type:[String,Number],default:"auto",validator:function(t){return"string"===typeof t?"auto"===t:t>=0}},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]},validator:function(t){var e=new Set(["tl","tm","tr","mr","br","bm","bl","ml"]);return new Set(t.filter((function(t){return e.has(t)}))).size===t.length}},dragHandle:{type:String,default:null},dragCancel:{type:String,default:null},axis:{type:String,default:"both",validator:function(t){return["x","y","both"].includes(t)}},grid:{type:Array,default:function(){return[1,1]}},parent:{type:[Boolean,String],default:!1},onDragStart:{type:Function,default:function(){return!0}},onDrag:{type:Function,default:function(){return!0}},onResizeStart:{type:Function,default:function(){return!0}},onResize:{type:Function,default:function(){return!0}},isConflictCheck:{type:Boolean,default:!1},snap:{type:Boolean,default:!1},snapTolerance:{type:Number,default:5,validator:function(t){return"number"===typeof t}},scaleRatio:{type:Number,default:1,validator:function(t){return"number"===typeof t}},handleInfo:{type:Object,default:function(){return{size:8,offset:-5,switch:!0}}}},data:function(){return{left:this.x,top:this.y,right:null,bottom:null,width:null,height:null,widthTouched:!1,heightTouched:!1,aspectFactor:null,parentWidth:null,parentHeight:null,minW:this.minWidth,minH:this.minHeight,maxW:this.maxWidth,maxH:this.maxHeight,handle:null,enabled:this.active,resizing:!1,dragging:!1,zIndex:this.z}},created:function(){this.maxWidth&&(this.minWidth,this.maxWidth),this.maxWidth&&(this.minHeight,this.maxHeight),this.resetBoundsAndMouseState()},mounted:function(){this.enableNativeDrag||(this.$el.ondragstart=function(){return!1});var t=this.getParentSize(),e=w(t,2),n=e[0],i=e[1];this.parentWidth=n,this.parentHeight=i;var r=M(this.$el),o=w(r,2),s=o[0],a=o[1];this.aspectFactor=("auto"!==this.w?this.w:s)/("auto"!==this.h?this.h:a),this.width="auto"!==this.w?this.w:s,this.height="auto"!==this.h?this.h:a,this.right=this.parentWidth-this.width-this.left,this.bottom=this.parentHeight-this.height-this.top,this.settingAttribute(),L(document.documentElement,"mousedown",this.deselect),L(document.documentElement,"touchend touchcancel",this.deselect),L(window,"resize",this.checkParentSize)},beforeDestroy:function(){F(document.documentElement,"mousedown",this.deselect),F(document.documentElement,"touchstart",this.handleUp),F(document.documentElement,"mousemove",this.move),F(document.documentElement,"touchmove",this.move),F(document.documentElement,"mouseup",this.handleUp),F(document.documentElement,"touchend touchcancel",this.deselect),F(window,"resize",this.checkParentSize)},methods:{resetBoundsAndMouseState:function(){this.mouseClickPosition={mouseX:0,mouseY:0,x:0,y:0,w:0,h:0},this.bounds={minLeft:null,maxLeft:null,minRight:null,maxRight:null,minTop:null,maxTop:null,minBottom:null,maxBottom:null}},checkParentSize:function(){if(this.parent){var t=this.getParentSize(),e=w(t,2),n=e[0],i=e[1];this.right=n-this.width-this.left,this.bottom=i-this.height-this.top,this.parentWidth=n,this.parentHeight=i}},getParentSize:function(){if(!0===this.parent){var t=window.getComputedStyle(this.$el.parentNode,null);return[parseInt(t.getPropertyValue("width"),10),parseInt(t.getPropertyValue("height"),10)]}if("string"===typeof this.parent){var e=document.querySelector(this.parent);if(!(e instanceof HTMLElement))throw new Error("The selector ".concat(this.parent," does not match any element"));return[e.offsetWidth,e.offsetHeight]}return[null,null]},elementTouchDown:function(t){U=G.touch,this.elementDown(t)},elementMouseDown:function(t){U=G.mouse,this.elementDown(t)},elementDown:function(t){if(!(t instanceof MouseEvent&&1!==t.which)){var e=t.target||t.srcElement;if(this.$el.contains(e)){if(!1===this.onDragStart(t))return;if(this.dragHandle&&!T(e,this.dragHandle,this.$el)||this.dragCancel&&T(e,this.dragCancel,this.$el))return void(this.dragging=!1);this.enabled||(this.enabled=!0,this.$emit("activated"),this.$emit("update:active",!0)),this.draggable&&(this.dragging=!0),this.mouseClickPosition.mouseX=t.touches?t.touches[0].pageX:t.pageX,this.mouseClickPosition.mouseY=t.touches?t.touches[0].pageY:t.pageY,this.mouseClickPosition.left=this.left,this.mouseClickPosition.right=this.right,this.mouseClickPosition.top=this.top,this.mouseClickPosition.bottom=this.bottom,this.mouseClickPosition.w=this.width,this.mouseClickPosition.h=this.height,this.parent&&(this.bounds=this.calcDragLimits()),L(document.documentElement,U.move,this.move),L(document.documentElement,U.stop,this.handleUp)}}},calcDragLimits:function(){return{minLeft:this.left%this.grid[0],maxLeft:Math.floor((this.parentWidth-this.width-this.left)/this.grid[0])*this.grid[0]+this.left,minRight:this.right%this.grid[0],maxRight:Math.floor((this.parentWidth-this.width-this.right)/this.grid[0])*this.grid[0]+this.right,minTop:this.top%this.grid[1],maxTop:Math.floor((this.parentHeight-this.height-this.top)/this.grid[1])*this.grid[1]+this.top,minBottom:this.bottom%this.grid[1],maxBottom:Math.floor((this.parentHeight-this.height-this.bottom)/this.grid[1])*this.grid[1]+this.bottom}},deselect:function(t){var e=t.target||t.srcElement,n=new RegExp(this.className+"-([trmbl]{2})","");this.$el.contains(e)||n.test(e.className)||(this.enabled&&!this.preventDeactivation&&(this.enabled=!1,this.$emit("deactivated"),this.$emit("update:active",!1)),F(document.documentElement,U.move,this.handleResize)),this.resetBoundsAndMouseState()},handleTouchDown:function(t,e){U=G.touch,this.handleDown(t,e)},handleDown:function(t,e){e instanceof MouseEvent&&1!==e.which||!1!==this.onResizeStart(t,e)&&(e.stopPropagation&&e.stopPropagation(),this.lockAspectRatio&&!t.includes("m")?this.handle="m"+t.substring(1):this.handle=t,this.resizing=!0,this.mouseClickPosition.mouseX=e.touches?e.touches[0].pageX:e.pageX,this.mouseClickPosition.mouseY=e.touches?e.touches[0].pageY:e.pageY,this.mouseClickPosition.left=this.left,this.mouseClickPosition.right=this.right,this.mouseClickPosition.top=this.top,this.mouseClickPosition.bottom=this.bottom,this.mouseClickPosition.w=this.width,this.mouseClickPosition.h=this.height,this.bounds=this.calcResizeLimits(),L(document.documentElement,U.move,this.handleResize),L(document.documentElement,U.stop,this.handleUp))},calcResizeLimits:function(){var t=this.minW,e=this.minH,n=this.maxW,i=this.maxH,r=this.aspectFactor,o=w(this.grid,2),s=o[0],a=o[1],u=this.width,l=this.height,c=this.left,h=this.top,d=this.right,f=this.bottom;this.lockAspectRatio&&(t/e>r?e=t/r:t=r*e,n&&i?(n=Math.min(n,r*i),i=Math.min(i,n/r)):n?i=n/r:i&&(n=r*i)),n-=n%s,i-=i%a;var p={minLeft:null,maxLeft:null,minTop:null,maxTop:null,minRight:null,maxRight:null,minBottom:null,maxBottom:null};return this.parent?(p.minLeft=c%s,p.maxLeft=c+Math.floor((u-t)/s)*s,p.minTop=h%a,p.maxTop=h+Math.floor((l-e)/a)*a,p.minRight=d%s,p.maxRight=d+Math.floor((u-t)/s)*s,p.minBottom=f%a,p.maxBottom=f+Math.floor((l-e)/a)*a,n&&(p.minLeft=Math.max(p.minLeft,this.parentWidth-d-n),p.minRight=Math.max(p.minRight,this.parentWidth-c-n)),i&&(p.minTop=Math.max(p.minTop,this.parentHeight-f-i),p.minBottom=Math.max(p.minBottom,this.parentHeight-h-i)),this.lockAspectRatio&&(p.minLeft=Math.max(p.minLeft,c-h*r),p.minTop=Math.max(p.minTop,h-c/r),p.minRight=Math.max(p.minRight,d-f*r),p.minBottom=Math.max(p.minBottom,f-d/r))):(p.minLeft=null,p.maxLeft=c+Math.floor((u-t)/s)*s,p.minTop=null,p.maxTop=h+Math.floor((l-e)/a)*a,p.minRight=null,p.maxRight=d+Math.floor((u-t)/s)*s,p.minBottom=null,p.maxBottom=f+Math.floor((l-e)/a)*a,n&&(p.minLeft=-(d+n),p.minRight=-(c+n)),i&&(p.minTop=-(f+i),p.minBottom=-(h+i)),this.lockAspectRatio&&n&&i&&(p.minLeft=Math.min(p.minLeft,-(d+n)),p.minTop=Math.min(p.minTop,-(i+f)),p.minRight=Math.min(p.minRight,-c-n),p.minBottom=Math.min(p.minBottom,-h-i))),p},move:function(t){this.resizing?this.handleResize(t):this.dragging&&this.handleDrag(t)},handleDrag:function(){var t=P(regeneratorRuntime.mark((function t(e){var n,i,r,o,s,a,u,l,c,h,d,f,p,g;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=this.axis,i=this.grid,r=this.bounds,o=this.mouseClickPosition,s=n&&"y"!==n?o.mouseX-(e.touches?e.touches[0].pageX:e.pageX):0,a=n&&"x"!==n?o.mouseY-(e.touches?e.touches[0].pageY:e.pageY):0,u=A(i,s,a,this.scaleRatio),l=w(u,2),c=l[0],h=l[1],d=k(o.left-c,r.minLeft,r.maxLeft),f=k(o.top-h,r.minTop,r.maxTop),!1!==this.onDrag(d,f)){t.next=11;break}return t.abrupt("return");case 11:return p=k(o.right+c,r.minRight,r.maxRight),g=k(o.bottom+h,r.minBottom,r.maxBottom),this.left=d,this.top=f,this.right=p,this.bottom=g,t.next=19,this.snapCheck();case 19:this.$emit("dragging",this.left,this.top);case 20:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),moveHorizontally:function(t){var e=A(this.grid,t,this.top,this.scale),n=w(e,2),i=n[0],r=(n[1],k(i,this.bounds.minLeft,this.bounds.maxLeft));this.left=r,this.right=this.parentWidth-this.width-r},moveVertically:function(t){var e=A(this.grid,this.left,t,this.scale),n=w(e,2),i=(n[0],n[1]),r=k(i,this.bounds.minTop,this.bounds.maxTop);this.top=r,this.bottom=this.parentHeight-this.height-r},handleResize:function(t){var e=this.left,n=this.top,i=this.right,r=this.bottom,o=this.mouseClickPosition,s=(this.lockAspectRatio,this.aspectFactor),a=o.mouseX-(t.touches?t.touches[0].pageX:t.pageX),u=o.mouseY-(t.touches?t.touches[0].pageY:t.pageY);!this.widthTouched&&a&&(this.widthTouched=!0),!this.heightTouched&&u&&(this.heightTouched=!0);var l=A(this.grid,a,u,this.scaleRatio),c=w(l,2),h=c[0],d=c[1];this.handle.includes("b")?(r=k(o.bottom+d,this.bounds.minBottom,this.bounds.maxBottom),this.lockAspectRatio&&this.resizingOnY&&(i=this.right-(this.bottom-r)*s)):this.handle.includes("t")&&(n=k(o.top-d,this.bounds.minTop,this.bounds.maxTop),this.lockAspectRatio&&this.resizingOnY&&(e=this.left-(this.top-n)*s)),this.handle.includes("r")?(i=k(o.right+h,this.bounds.minRight,this.bounds.maxRight),this.lockAspectRatio&&this.resizingOnX&&(r=this.bottom-(this.right-i)/s)):this.handle.includes("l")&&(e=k(o.left-h,this.bounds.minLeft,this.bounds.maxLeft),this.lockAspectRatio&&this.resizingOnX&&(n=this.top-(this.left-e)/s));var f=O(this.parentWidth,e,i),p=I(this.parentHeight,n,r);!1!==this.onResize(this.handle,e,n,f,p)&&(this.left=e,this.top=n,this.right=i,this.bottom=r,this.width=f,this.height=p,this.$emit("resizing",this.left,this.top,this.width,this.height))},changeWidth:function(t){var e=A(this.grid,t,0,this.scale),n=w(e,2),i=n[0],r=(n[1],k(this.parentWidth-i-this.left,this.bounds.minRight,this.bounds.maxRight)),o=this.bottom;this.lockAspectRatio&&(o=this.bottom-(this.right-r)/this.aspectFactor);var s=O(this.parentWidth,this.left,r),a=I(this.parentHeight,this.top,o);this.right=r,this.bottom=o,this.width=s,this.height=a},changeHeight:function(t){var e=A(this.grid,0,t,this.scale),n=w(e,2),i=(n[0],n[1]),r=k(this.parentHeight-i-this.top,this.bounds.minBottom,this.bounds.maxBottom),o=this.right;this.lockAspectRatio&&(o=this.right-(this.bottom-r)*this.aspectFactor);var s=O(this.parentWidth,this.left,o),a=I(this.parentHeight,this.top,r);this.right=o,this.bottom=r,this.width=s,this.height=a},handleUp:function(){var t=P(regeneratorRuntime.mark((function t(e){var n,i,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(r in this.handle=null,n=new Array(3).fill({display:!1,position:"",origin:"",lineLength:""}),i={vLine:[],hLine:[]},i)i[r]=JSON.parse(JSON.stringify(n));if(!this.resizing){t.next=10;break}return this.resizing=!1,t.next=8,this.conflictCheck();case 8:this.$emit("refLineParams",i),this.$emit("resizestop",this.left,this.top,this.width,this.height);case 10:if(!this.dragging){t.next=16;break}return this.dragging=!1,t.next=14,this.conflictCheck();case 14:this.$emit("refLineParams",i),this.$emit("dragstop",this.left,this.top);case 16:this.resetBoundsAndMouseState(),F(document.documentElement,U.move,this.handleResize);case 18:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),settingAttribute:function(){this.$el.setAttribute("data-is-check","".concat(this.isConflictCheck)),this.$el.setAttribute("data-is-snap","".concat(this.snap))},conflictCheck:function(){var t=this.top,e=this.left,n=this.width,i=this.height;if(this.isConflictCheck){var r=this.$el.parentNode.childNodes,o=!0,s=!1,a=void 0;try{for(var u,l=r[Symbol.iterator]();!(o=(u=l.next()).done);o=!0){var c=u.value;if(void 0!==c.className&&!c.className.includes(this.classNameActive)&&null!==c.getAttribute("data-is-check")&&"false"!==c.getAttribute("data-is-check")){var h=c.offsetWidth,d=c.offsetHeight,f=this.formatTransformVal(c.style.transform),p=w(f,2),g=p[0],v=p[1],m=t>=v&&e>=g&&v+d>t&&g+h>e||t<=v&&e<g&&t+i>v&&e+n>g,b=e<=g&&t>=v&&e+n>g&&t<v+d||t<v&&e>g&&t+i>v&&e<g+h,y=t<=v&&e>=g&&t+i>v&&e<g+h||t>=v&&e<=g&&t<v+d&&e>g+h,x=t<=v&&e>=g&&t+i>v&&e<g+h||t>=v&&e<=g&&t<v+d&&e>g+h,P=e>=g&&t>=v&&e<g+h&&t<v+d||t>v&&e<=g&&e+n>g&&t<v+d,_=t<=v&&e>=g&&t+i>v&&e<g+h||t>=v&&e<=g&&t<v+d&&e+n>g;(m||b||y||x||P||_)&&(this.top=this.mouseClickPosition.top,this.left=this.mouseClickPosition.left,this.right=this.mouseClickPosition.right,this.bottom=this.mouseClickPosition.bottom,this.width=this.mouseClickPosition.w,this.height=this.mouseClickPosition.h)}}}catch(C){s=!0,a=C}finally{try{o||null==l.return||l.return()}finally{if(s)throw a}}}},snapCheck:function(){var t=P(regeneratorRuntime.mark((function t(){var e,n,i,r,o,s,a,u,l,c,h,d,f,p,g,v,m,b,y,x,P,_,C,E,j,S,D,A,O,I,k,T,M,L,F,R,N,G,H,B,U,z,W,Y,X,V,q,$;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.width,n=this.height,!this.snap){t.next=41;break}for(l in i=this.left,r=this.left+e,o=this.top,s=this.top+n,a=new Array(3).fill({display:!1,position:"",origin:"",lineLength:""}),u={vLine:[],hLine:[]},u)u[l]=JSON.parse(JSON.stringify(a));return c=this.$el.parentNode.childNodes,h={value:{x:[[],[],[]],y:[[],[],[]]},display:[],position:[]},t.next=14,this.getActiveAll(c);case 14:for(d=t.sent,f=d.groupWidth,p=d.groupHeight,g=d.groupLeft,v=d.groupTop,m=d.bln,m||(e=f,n=p,i=g,r=g+f,o=v,s=v+p),b=!0,y=!1,x=void 0,t.prev=24,P=c[Symbol.iterator]();!(b=(_=P.next()).done);b=!0)if(C=_.value,void 0!==C.className&&!C.className.includes(this.classNameActive)&&null!==C.getAttribute("data-is-snap")&&"false"!==C.getAttribute("data-is-snap"))for(E=C.offsetWidth,j=C.offsetHeight,S=this.formatTransformVal(C.style.transform),D=w(S,2),A=D[0],O=D[1],I=A+E,k=O+j,T=Math.abs(o+n/2-(O+j/2))<=this.snapTolerance,M=Math.abs(i+e/2-(A+E/2))<=this.snapTolerance,L=Math.abs(O-s)<=this.snapTolerance,F=Math.abs(k-s)<=this.snapTolerance,R=Math.abs(O-o)<=this.snapTolerance,N=Math.abs(k-o)<=this.snapTolerance,G=Math.abs(A-r)<=this.snapTolerance,H=Math.abs(I-r)<=this.snapTolerance,B=Math.abs(A-i)<=this.snapTolerance,U=Math.abs(I-i)<=this.snapTolerance,h["display"]=[L,F,R,N,T,T,G,H,B,U,M,M],h["position"]=[O,k,O,k,O+j/2,O+j/2,A,I,A,I,A+E/2,A+E/2],L&&(m&&(this.top=O-n,this.bottom=this.parentHeight-this.top-n),h.value.y[0].push(A,I,i,r)),R&&(m&&(this.top=O,this.bottom=this.parentHeight-this.top-n),h.value.y[0].push(A,I,i,r)),F&&(m&&(this.top=k-n,this.bottom=this.parentHeight-this.top-n),h.value.y[1].push(A,I,i,r)),N&&(m&&(this.top=k,this.bottom=this.parentHeight-this.top-n),h.value.y[1].push(A,I,i,r)),G&&(m&&(this.left=A-e,this.right=this.parentWidth-this.left-e),h.value.x[0].push(O,k,o,s)),B&&(m&&(this.left=A,this.right=this.parentWidth-this.left-e),h.value.x[0].push(O,k,o,s)),H&&(m&&(this.left=I-e,this.right=this.parentWidth-this.left-e),h.value.x[1].push(O,k,o,s)),U&&(m&&(this.left=I,this.right=this.parentWidth-this.left-e),h.value.x[1].push(O,k,o,s)),T&&(m&&(this.top=O+j/2-n/2,this.bottom=this.parentHeight-this.top-n),h.value.y[2].push(A,I,i,r)),M&&(m&&(this.left=A+E/2-e/2,this.right=this.parentWidth-this.left-e),h.value.x[2].push(O,k,o,s)),z=[0,1,0,1,2,2,0,1,0,1,2,2],W=0;W<=z.length;W++)Y=W<6?"y":"x",X=W<6?"hLine":"vLine",h.display[W]&&(V=this.calcLineValues(h.value[Y][z[W]]),q=V.origin,$=V.length,u[X][z[W]].display=h.display[W],u[X][z[W]].position=h.position[W]+"px",u[X][z[W]].origin=q,u[X][z[W]].lineLength=$);t.next=32;break;case 28:t.prev=28,t.t0=t["catch"](24),y=!0,x=t.t0;case 32:t.prev=32,t.prev=33,b||null==P.return||P.return();case 35:if(t.prev=35,!y){t.next=38;break}throw x;case 38:return t.finish(35);case 39:return t.finish(32);case 40:this.$emit("refLineParams",u);case 41:case"end":return t.stop()}}),t,this,[[24,28,32,40],[33,,35,39]])})));function e(){return t.apply(this,arguments)}return e}(),calcLineValues:function(t){var e=Math.max.apply(Math,m(t))-Math.min.apply(Math,m(t))+"px",n=Math.min.apply(Math,m(t))+"px";return{length:e,origin:n}},getActiveAll:function(){var t=P(regeneratorRuntime.mark((function t(e){var n,i,r,o,s,a,u,l,c,h,d,f,p,g,v,m,b,y,x,P,_,C,E,j,S;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(n=[],i=[],r=[],o=0,s=0,a=0,u=0,l=!0,c=!1,h=void 0,t.prev=10,d=e[Symbol.iterator]();!(l=(f=d.next()).done);l=!0)p=f.value,void 0!==p.className&&p.className.includes(this.classNameActive)&&n.push(p);t.next=18;break;case 14:t.prev=14,t.t0=t["catch"](10),c=!0,h=t.t0;case 18:t.prev=18,t.prev=19,l||null==d.return||d.return();case 21:if(t.prev=21,!c){t.next=24;break}throw h;case 24:return t.finish(21);case 25:return t.finish(18);case 26:if(g=n.length,!(g>1)){t.next=51;break}for(v=!0,m=!1,b=void 0,t.prev=31,y=n[Symbol.iterator]();!(v=(x=y.next()).done);v=!0)P=x.value,_=P.offsetLeft,C=_+P.offsetWidth,E=P.offsetTop,j=E+P.offsetHeight,i.push(E,j),r.push(_,C);t.next=39;break;case 35:t.prev=35,t.t1=t["catch"](31),m=!0,b=t.t1;case 39:t.prev=39,t.prev=40,v||null==y.return||y.return();case 42:if(t.prev=42,!m){t.next=45;break}throw b;case 45:return t.finish(42);case 46:return t.finish(39);case 47:o=Math.max.apply(Math,r)-Math.min.apply(Math,r),s=Math.max.apply(Math,i)-Math.min.apply(Math,i),a=Math.min.apply(Math,r),u=Math.min.apply(Math,i);case 51:return S=1===g,t.abrupt("return",{groupWidth:o,groupHeight:s,groupLeft:a,groupTop:u,bln:S});case 53:case"end":return t.stop()}}),t,this,[[10,14,18,26],[19,,21,25],[31,35,39,47],[40,,42,46]])})));function e(e){return t.apply(this,arguments)}return e}(),formatTransformVal:function(t){var e=t.replace(/[^0-9\-,]/g,"").split(","),n=w(e,2),i=n[0],r=n[1];return void 0===r&&(r=0),[+i,+r]}},computed:{handleStyle:function(){var t=this;return function(e){if(!t.handleInfo.switch)return{display:t.enabled?"block":"none"};var n=(t.handleInfo.size/t.scaleRatio).toFixed(2),i=(t.handleInfo.offset/t.scaleRatio).toFixed(2),r=(n/2).toFixed(2),o={tl:{top:"".concat(i,"px"),left:"".concat(i,"px")},tm:{top:"".concat(i,"px"),left:"calc(50% - ".concat(r,"px)")},tr:{top:"".concat(i,"px"),right:"".concat(i,"px")},mr:{top:"calc(50% - ".concat(r,"px)"),right:"".concat(i,"px")},br:{bottom:"".concat(i,"px"),right:"".concat(i,"px")},bm:{bottom:"".concat(i,"px"),right:"calc(50% - ".concat(r,"px)")},bl:{bottom:"".concat(i,"px"),left:"".concat(i,"px")},ml:{top:"calc(50% - ".concat(r,"px)"),left:"".concat(i,"px")}},s={width:"".concat(n,"px"),height:"".concat(n,"px"),top:o[e].top,left:o[e].left,right:o[e].right,bottom:o[e].bottom};return s.display=t.enabled?"block":"none",s}},style:function(){return N({transform:"translate(".concat(this.left,"px, ").concat(this.top,"px)"),width:this.computedWidth,height:this.computedHeight,zIndex:this.zIndex},this.dragging&&this.disableUserSelect?H:B)},actualHandles:function(){return this.resizable?this.handles:[]},computedWidth:function(){return"auto"!==this.w||this.widthTouched?this.width+"px":"auto"},computedHeight:function(){return"auto"!==this.h||this.heightTouched?this.height+"px":"auto"},resizingOnX:function(){return Boolean(this.handle)&&(this.handle.includes("l")||this.handle.includes("r"))},resizingOnY:function(){return Boolean(this.handle)&&(this.handle.includes("t")||this.handle.includes("b"))},isCornerHandle:function(){return Boolean(this.handle)&&["tl","tr","br","bl"].includes(this.handle)}},watch:{active:function(t){this.enabled=t,t?this.$emit("activated"):this.$emit("deactivated")},z:function(t){(t>=0||"auto"===t)&&(this.zIndex=t)},x:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcDragLimits()),this.moveHorizontally(t))},y:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcDragLimits()),this.moveVertically(t))},lockAspectRatio:function(t){this.aspectFactor=t?this.width/this.height:void 0},minWidth:function(t){t>0&&t<=this.width&&(this.minW=t)},minHeight:function(t){t>0&&t<=this.height&&(this.minH=t)},maxWidth:function(t){this.maxW=t},maxHeight:function(t){this.maxH=t},w:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcResizeLimits()),this.changeWidth(t))},h:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcResizeLimits()),this.changeHeight(t))}}},W=z;function Y(t,e,n,i,r,o,s,a){var u,l="function"===typeof t?t.options:t;if(e&&(l.render=e,l.staticRenderFns=n,l._compiled=!0),i&&(l.functional=!0),o&&(l._scopeId="data-v-"+o),s?(u=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),r&&r.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(s)},l._ssrRegister=u):r&&(u=a?function(){r.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:r),u)if(l.functional){l._injectStyles=u;var c=l.render;l.render=function(t,e){return u.call(e),c(t,e)}}else{var h=l.beforeCreate;l.beforeCreate=h?[].concat(h,u):[u]}return{exports:t,options:l}}var X=Y(W,i,r,!1,null,null,null);e["a"]=X.exports},"36bd":function(t,e,n){"use strict";var i=n("4bf8"),r=n("77f1"),o=n("9def");t.exports=function(t){var e=i(this),n=o(e.length),s=arguments.length,a=r(s>1?arguments[1]:void 0,n),u=s>2?arguments[2]:void 0,l=void 0===u?n:r(u,n);while(l>a)e[a++]=t;return e}},"36dc":function(t,e,n){var i=n("da3c"),r=n("df0a").set,o=i.MutationObserver||i.WebKitMutationObserver,s=i.process,a=i.Promise,u="process"==n("6e1f")(s);t.exports=function(){var t,e,n,l=function(){var i,r;u&&(i=s.domain)&&i.exit();while(t){r=t.fn,t=t.next;try{r()}catch(o){throw t?n():e=void 0,o}}e=void 0,i&&i.enter()};if(u)n=function(){s.nextTick(l)};else if(!o||i.navigator&&i.navigator.standalone)if(a&&a.resolve){var c=a.resolve(void 0);n=function(){c.then(l)}}else n=function(){r.call(i,l)};else{var h=!0,d=document.createTextNode("");new o(l).observe(d,{characterData:!0}),n=function(){d.data=h=!h}}return function(i){var r={fn:i,next:void 0};e&&(e.next=r),t||(t=r,n()),e=r}}},"37c8":function(t,e,n){e.f=n("2b4c")},3846:function(t,e,n){n("9e1e")&&"g"!=/./g.flags&&n("86cc").f(RegExp.prototype,"flags",{configurable:!0,get:n("0bfb")})},"38fd":function(t,e,n){var i=n("69a8"),r=n("4bf8"),o=n("613b")("IE_PROTO"),s=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=r(t),i(t,o)?t[o]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?s:null}},3904:function(t,e,n){var i=n("8ce0");t.exports=function(t,e,n){for(var r in e)n&&t[r]?t[r]=e[r]:i(t,r,e[r]);return t}},"3a72":function(t,e,n){var i=n("7726"),r=n("8378"),o=n("2d00"),s=n("37c8"),a=n("86cc").f;t.exports=function(t){var e=r.Symbol||(r.Symbol=o?{}:i.Symbol||{});"_"==t.charAt(0)||t in e||a(e,t,{value:s.f(t)})}},"3adc":function(t,e,n){var i=n("0f89"),r=n("a47f"),o=n("2ea1"),s=Object.defineProperty;e.f=n("7d95")?Object.defineProperty:function(t,e,n){if(i(t),e=o(e,!0),i(n),r)try{return s(t,e,n)}catch(a){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},"3b2b":function(t,e,n){var i=n("7726"),r=n("5dbc"),o=n("86cc").f,s=n("9093").f,a=n("aae3"),u=n("0bfb"),l=i.RegExp,c=l,h=l.prototype,d=/a/g,f=/a/g,p=new l(d)!==d;if(n("9e1e")&&(!p||n("79e5")((function(){return f[n("2b4c")("match")]=!1,l(d)!=d||l(f)==f||"/a/i"!=l(d,"i")})))){l=function(t,e){var n=this instanceof l,i=a(t),o=void 0===e;return!n&&i&&t.constructor===l&&o?t:r(p?new c(i&&!o?t.source:t,e):c((i=t instanceof l)?t.source:t,i&&o?u.call(t):e),n?this:h,l)};for(var g=function(t){t in l||o(l,t,{configurable:!0,get:function(){return c[t]},set:function(e){c[t]=e}})},v=s(c),m=0;v.length>m;)g(v[m++]);h.constructor=l,l.prototype=h,n("2aba")(i,"RegExp",l)}n("7a56")("RegExp")},"41a0":function(t,e,n){"use strict";var i=n("2aeb"),r=n("4630"),o=n("7f20"),s={};n("32e9")(s,n("2b4c")("iterator"),(function(){return this})),t.exports=function(t,e,n){t.prototype=i(s,{next:r(1,n)}),o(t,e+" Iterator")}},"436c":function(t,e,n){var i=n("1b55")("iterator"),r=!1;try{var o=[7][i]();o["return"]=function(){r=!0},Array.from(o,(function(){throw 2}))}catch(s){}t.exports=function(t,e){if(!e&&!r)return!1;var n=!1;try{var o=[7],a=o[i]();a.next=function(){return{done:n=!0}},o[i]=function(){return a},t(o)}catch(s){}return n}},"43c8":function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},"456d":function(t,e,n){var i=n("4bf8"),r=n("0d58");n("5eda")("keys",(function(){return function(t){return r(i(t))}}))},4588:function(t,e){var n=Math.ceil,i=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?i:n)(t)}},4630:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},"4a59":function(t,e,n){var i=n("9b43"),r=n("1fa8"),o=n("33a4"),s=n("cb7c"),a=n("9def"),u=n("27ee"),l={},c={};e=t.exports=function(t,e,n,h,d){var f,p,g,v,m=d?function(){return t}:u(t),b=i(n,h,e?2:1),y=0;if("function"!=typeof m)throw TypeError(t+" is not iterable!");if(o(m)){for(f=a(t.length);f>y;y++)if(v=e?b(s(p=t[y])[0],p[1]):b(t[y]),v===l||v===c)return v}else for(g=m.call(t);!(p=g.next()).done;)if(v=r(g,b,p.value,e),v===l||v===c)return v},e.BREAK=l,e.RETURN=c},"4bf8":function(t,e,n){var i=n("be13");t.exports=function(t){return Object(i(t))}},"4f7f":function(t,e,n){"use strict";var i=n("c26b"),r=n("b39a"),o="Set";t.exports=n("e0b8")(o,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return i.def(r(this,o),t=0===t?0:t,t)}},i)},5147:function(t,e,n){var i=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[i]=!1,!"/./"[t](e)}catch(r){}}return!0}},"520a":function(t,e,n){"use strict";var i=n("0bfb"),r=RegExp.prototype.exec,o=String.prototype.replace,s=r,a="lastIndex",u=function(){var t=/a/,e=/b*/g;return r.call(t,"a"),r.call(e,"a"),0!==t[a]||0!==e[a]}(),l=void 0!==/()??/.exec("")[1],c=u||l;c&&(s=function(t){var e,n,s,c,h=this;return l&&(n=new RegExp("^"+h.source+"$(?!\\s)",i.call(h))),u&&(e=h[a]),s=r.call(h,t),u&&s&&(h[a]=h.global?s.index+s[0].length:e),l&&s&&s.length>1&&o.call(s[0],n,(function(){for(c=1;c<arguments.length-2;c++)void 0===arguments[c]&&(s[c]=void 0)})),s}),t.exports=s},"52a7":function(t,e){e.f={}.propertyIsEnumerable},5537:function(t,e,n){var i=n("8378"),r=n("7726"),o="__core-js_shared__",s=r[o]||(r[o]={});(t.exports=function(t,e){return s[t]||(s[t]=void 0!==e?e:{})})("versions",[]).push({version:i.version,mode:n("2d00")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"560b":function(t,e,n){var i=n("bc25"),r=n("9c93"),o=n("c227"),s=n("0f89"),a=n("a5ab"),u=n("f159"),l={},c={};e=t.exports=function(t,e,n,h,d){var f,p,g,v,m=d?function(){return t}:u(t),b=i(n,h,e?2:1),y=0;if("function"!=typeof m)throw TypeError(t+" is not iterable!");if(o(m)){for(f=a(t.length);f>y;y++)if(v=e?b(s(p=t[y])[0],p[1]):b(t[y]),v===l||v===c)return v}else for(g=m.call(t);!(p=g.next()).done;)if(v=r(g,b,p.value,e),v===l||v===c)return v},e.BREAK=l,e.RETURN=c},"57f7":function(t,e,n){n("93c4"),n("6109"),t.exports=n("a7d3").Array.from},"58b2":function(t,e,n){var i=n("5ca1");i(i.S+i.F*!n("9e1e"),"Object",{defineProperties:n("1495")})},"5b5f":function(t,e,n){"use strict";var i,r,o,s,a=n("b457"),u=n("da3c"),l=n("bc25"),c=n("7d8a"),h=n("d13f"),d=n("6f8a"),f=n("f2fe"),p=n("b0bc"),g=n("560b"),v=n("302f"),m=n("df0a").set,b=n("36dc")(),y=n("03ca"),x=n("75c9"),P=n("8a12"),_=n("decf"),C="Promise",E=u.TypeError,j=u.process,S=j&&j.versions,w=S&&S.v8||"",D=u[C],A="process"==c(j),O=function(){},I=r=y.f,k=!!function(){try{var t=D.resolve(1),e=(t.constructor={})[n("1b55")("species")]=function(t){t(O,O)};return(A||"function"==typeof PromiseRejectionEvent)&&t.then(O)instanceof e&&0!==w.indexOf("6.6")&&-1===P.indexOf("Chrome/66")}catch(i){}}(),T=function(t){var e;return!(!d(t)||"function"!=typeof(e=t.then))&&e},M=function(t,e){if(!t._n){t._n=!0;var n=t._c;b((function(){var i=t._v,r=1==t._s,o=0,s=function(e){var n,o,s,a=r?e.ok:e.fail,u=e.resolve,l=e.reject,c=e.domain;try{a?(r||(2==t._h&&R(t),t._h=1),!0===a?n=i:(c&&c.enter(),n=a(i),c&&(c.exit(),s=!0)),n===e.promise?l(E("Promise-chain cycle")):(o=T(n))?o.call(n,u,l):u(n)):l(i)}catch(h){c&&!s&&c.exit(),l(h)}};while(n.length>o)s(n[o++]);t._c=[],t._n=!1,e&&!t._h&&L(t)}))}},L=function(t){m.call(u,(function(){var e,n,i,r=t._v,o=F(t);if(o&&(e=x((function(){A?j.emit("unhandledRejection",r,t):(n=u.onunhandledrejection)?n({promise:t,reason:r}):(i=u.console)&&i.error&&i.error("Unhandled promise rejection",r)})),t._h=A||F(t)?2:1),t._a=void 0,o&&e.e)throw e.v}))},F=function(t){return 1!==t._h&&0===(t._a||t._c).length},R=function(t){m.call(u,(function(){var e;A?j.emit("rejectionHandled",t):(e=u.onrejectionhandled)&&e({promise:t,reason:t._v})}))},N=function(t){var e=this;e._d||(e._d=!0,e=e._w||e,e._v=t,e._s=2,e._a||(e._a=e._c.slice()),M(e,!0))},G=function(t){var e,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===t)throw E("Promise can't be resolved itself");(e=T(t))?b((function(){var i={_w:n,_d:!1};try{e.call(t,l(G,i,1),l(N,i,1))}catch(r){N.call(i,r)}})):(n._v=t,n._s=1,M(n,!1))}catch(i){N.call({_w:n,_d:!1},i)}}};k||(D=function(t){p(this,D,C,"_h"),f(t),i.call(this);try{t(l(G,this,1),l(N,this,1))}catch(e){N.call(this,e)}},i=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1},i.prototype=n("3904")(D.prototype,{then:function(t,e){var n=I(v(this,D));return n.ok="function"!=typeof t||t,n.fail="function"==typeof e&&e,n.domain=A?j.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&M(this,!1),n.promise},catch:function(t){return this.then(void 0,t)}}),o=function(){var t=new i;this.promise=t,this.resolve=l(G,t,1),this.reject=l(N,t,1)},y.f=I=function(t){return t===D||t===s?new o(t):r(t)}),h(h.G+h.W+h.F*!k,{Promise:D}),n("c0d8")(D,C),n("1be4")(C),s=n("a7d3")[C],h(h.S+h.F*!k,C,{reject:function(t){var e=I(this),n=e.reject;return n(t),e.promise}}),h(h.S+h.F*(a||!k),C,{resolve:function(t){return _(a&&this===s?D:this,t)}}),h(h.S+h.F*!(k&&n("436c")((function(t){D.all(t)["catch"](O)}))),C,{all:function(t){var e=this,n=I(e),i=n.resolve,r=n.reject,o=x((function(){var n=[],o=0,s=1;g(t,!1,(function(t){var a=o++,u=!1;n.push(void 0),s++,e.resolve(t).then((function(t){u||(u=!0,n[a]=t,--s||i(n))}),r)})),--s||i(n)}));return o.e&&r(o.v),n.promise},race:function(t){var e=this,n=I(e),i=n.reject,r=x((function(){g(t,!1,(function(t){e.resolve(t).then(n.resolve,i)}))}));return r.e&&i(r.v),n.promise}})},"5ca1":function(t,e,n){var i=n("7726"),r=n("8378"),o=n("32e9"),s=n("2aba"),a=n("9b43"),u="prototype",l=function(t,e,n){var c,h,d,f,p=t&l.F,g=t&l.G,v=t&l.S,m=t&l.P,b=t&l.B,y=g?i:v?i[e]||(i[e]={}):(i[e]||{})[u],x=g?r:r[e]||(r[e]={}),P=x[u]||(x[u]={});for(c in g&&(n=e),n)h=!p&&y&&void 0!==y[c],d=(h?y:n)[c],f=b&&h?a(d,i):m&&"function"==typeof d?a(Function.call,d):d,y&&s(y,c,d,t&l.U),x[c]!=d&&o(x,c,f),m&&P[c]!=d&&(P[c]=d)};i.core=r,l.F=1,l.G=2,l.S=4,l.P=8,l.B=16,l.W=32,l.U=64,l.R=128,t.exports=l},"5cc5":function(t,e,n){var i=n("2b4c")("iterator"),r=!1;try{var o=[7][i]();o["return"]=function(){r=!0},Array.from(o,(function(){throw 2}))}catch(s){}t.exports=function(t,e){if(!e&&!r)return!1;var n=!1;try{var o=[7],a=o[i]();a.next=function(){return{done:n=!0}},o[i]=function(){return a},t(o)}catch(s){}return n}},"5ce7":function(t,e,n){"use strict";var i=n("7108"),r=n("f845"),o=n("c0d8"),s={};n("8ce0")(s,n("1b55")("iterator"),(function(){return this})),t.exports=function(t,e,n){t.prototype=i(s,{next:r(1,n)}),o(t,e+" Iterator")}},"5d73":function(t,e,n){t.exports=n("0a91")},"5d8f":function(t,e,n){var i=n("7772")("keys"),r=n("7b00");t.exports=function(t){return i[t]||(i[t]=r(t))}},"5dbc":function(t,e,n){var i=n("d3f4"),r=n("8b97").set;t.exports=function(t,e,n){var o,s=e.constructor;return s!==n&&"function"==typeof s&&(o=s.prototype)!==n.prototype&&i(o)&&r&&r(t,o),t}},"5df3":function(t,e,n){"use strict";var i=n("02f4")(!0);n("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=i(e,n),this._i+=t.length,{value:t,done:!1})}))},"5eda":function(t,e,n){var i=n("5ca1"),r=n("8378"),o=n("79e5");t.exports=function(t,e){var n=(r.Object||{})[t]||Object[t],s={};s[t]=e(n),i(i.S+i.F*o((function(){n(1)})),"Object",s)}},"5f1b":function(t,e,n){"use strict";var i=n("23c6"),r=RegExp.prototype.exec;t.exports=function(t,e){var n=t.exec;if("function"===typeof n){var o=n.call(t,e);if("object"!==typeof o)throw new TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==i(t))throw new TypeError("RegExp#exec called on incompatible receiver");return r.call(t,e)}},6109:function(t,e,n){"use strict";var i=n("bc25"),r=n("d13f"),o=n("0185"),s=n("9c93"),a=n("c227"),u=n("a5ab"),l=n("b3ec"),c=n("f159");r(r.S+r.F*!n("436c")((function(t){Array.from(t)})),"Array",{from:function(t){var e,n,r,h,d=o(t),f="function"==typeof this?this:Array,p=arguments.length,g=p>1?arguments[1]:void 0,v=void 0!==g,m=0,b=c(d);if(v&&(g=i(g,p>2?arguments[2]:void 0,2)),void 0==b||f==Array&&a(b))for(e=u(d.length),n=new f(e);e>m;m++)l(n,m,v?g(d[m],m):d[m]);else for(h=b.call(d),n=new f;!(r=h.next()).done;m++)l(n,m,v?s(h,g,[r.value,m],!0):r.value);return n.length=m,n}})},"613b":function(t,e,n){var i=n("5537")("keys"),r=n("ca5a");t.exports=function(t){return i[t]||(i[t]=r(t))}},"626a":function(t,e,n){var i=n("2d95");t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==i(t)?t.split(""):Object(t)}},6762:function(t,e,n){"use strict";var i=n("5ca1"),r=n("c366")(!0);i(i.P,"Array",{includes:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")("includes")},"67ab":function(t,e,n){var i=n("ca5a")("meta"),r=n("d3f4"),o=n("69a8"),s=n("86cc").f,a=0,u=Object.isExtensible||function(){return!0},l=!n("79e5")((function(){return u(Object.preventExtensions({}))})),c=function(t){s(t,i,{value:{i:"O"+ ++a,w:{}}})},h=function(t,e){if(!r(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!o(t,i)){if(!u(t))return"F";if(!e)return"E";c(t)}return t[i].i},d=function(t,e){if(!o(t,i)){if(!u(t))return!0;if(!e)return!1;c(t)}return t[i].w},f=function(t){return l&&p.NEED&&u(t)&&!o(t,i)&&c(t),t},p=t.exports={KEY:i,NEED:!1,fastKey:h,getWeak:d,onFreeze:f}},6821:function(t,e,n){var i=n("626a"),r=n("be13");t.exports=function(t){return i(r(t))}},"69a8":function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},"6a99":function(t,e,n){var i=n("d3f4");t.exports=function(t,e){if(!i(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!i(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")}},"6a9b":function(t,e,n){var i=n("8bab"),r=n("e5fa");t.exports=function(t){return i(r(t))}},"6b54":function(t,e,n){"use strict";n("3846");var i=n("cb7c"),r=n("0bfb"),o=n("9e1e"),s="toString",a=/./[s],u=function(t){n("2aba")(RegExp.prototype,s,t,!0)};n("79e5")((function(){return"/a/b"!=a.call({source:"a",flags:"b"})}))?u((function(){var t=i(this);return"/".concat(t.source,"/","flags"in t?t.flags:!o&&t instanceof RegExp?r.call(t):void 0)})):a.name!=s&&u((function(){return a.call(this)}))},"6c7b":function(t,e,n){var i=n("5ca1");i(i.P,"Array",{fill:n("36bd")}),n("9c6c")("fill")},"6e1f":function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},"6f42":function(t,e,n){},"6f8a":function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},7108:function(t,e,n){var i=n("0f89"),r=n("f568"),o=n("0029"),s=n("5d8f")("IE_PROTO"),a=function(){},u="prototype",l=function(){var t,e=n("12fd")("iframe"),i=o.length,r="<",s=">";e.style.display="none",n("103a").appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write(r+"script"+s+"document.F=Object"+r+"/script"+s),t.close(),l=t.F;while(i--)delete l[u][o[i]];return l()};t.exports=Object.create||function(t,e){var n;return null!==t?(a[u]=i(t),n=new a,a[u]=null,n[s]=t):n=l(),void 0===e?n:r(n,e)}},7514:function(t,e,n){"use strict";var i=n("5ca1"),r=n("0a49")(5),o="find",s=!0;o in[]&&Array(1)[o]((function(){s=!1})),i(i.P+i.F*s,"Array",{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")(o)},"75c9":function(t,e){t.exports=function(t){try{return{e:!1,v:t()}}catch(e){return{e:!0,v:e}}}},7633:function(t,e,n){var i=n("2695"),r=n("0029");t.exports=Object.keys||function(t){return i(t,r)}},7726:function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},"774e":function(t,e,n){t.exports=n("57f7")},7772:function(t,e,n){var i=n("a7d3"),r=n("da3c"),o="__core-js_shared__",s=r[o]||(r[o]={});(t.exports=function(t,e){return s[t]||(s[t]=void 0!==e?e:{})})("versions",[]).push({version:i.version,mode:n("b457")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"77f1":function(t,e,n){var i=n("4588"),r=Math.max,o=Math.min;t.exports=function(t,e){return t=i(t),t<0?r(t+e,0):o(t,e)}},"795b":function(t,e,n){t.exports=n("dd04")},"79e5":function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},"7a56":function(t,e,n){"use strict";var i=n("7726"),r=n("86cc"),o=n("9e1e"),s=n("2b4c")("species");t.exports=function(t){var e=i[t];o&&e&&!e[s]&&r.f(e,s,{configurable:!0,get:function(){return this}})}},"7b00":function(t,e){var n=0,i=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+i).toString(36))}},"7bbc":function(t,e,n){var i=n("6821"),r=n("9093").f,o={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(t){try{return r(t)}catch(e){return s.slice()}};t.exports.f=function(t){return s&&"[object Window]"==o.call(t)?a(t):r(i(t))}},"7d8a":function(t,e,n){var i=n("6e1f"),r=n("1b55")("toStringTag"),o="Arguments"==i(function(){return arguments}()),s=function(t,e){try{return t[e]}catch(n){}};t.exports=function(t){var e,n,a;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=s(e=Object(t),r))?n:o?i(e):"Object"==(a=i(e))&&"function"==typeof e.callee?"Arguments":a}},"7d95":function(t,e,n){t.exports=!n("d782")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},"7f20":function(t,e,n){var i=n("86cc").f,r=n("69a8"),o=n("2b4c")("toStringTag");t.exports=function(t,e,n){t&&!r(t=n?t:t.prototype,o)&&i(t,o,{configurable:!0,value:e})}},8378:function(t,e){var n=t.exports={version:"2.6.11"};"number"==typeof __e&&(__e=n)},"84f2":function(t,e){t.exports={}},"85f2":function(t,e,n){t.exports=n("ec5b")},"86cc":function(t,e,n){var i=n("cb7c"),r=n("c69a"),o=n("6a99"),s=Object.defineProperty;e.f=n("9e1e")?Object.defineProperty:function(t,e,n){if(i(t),e=o(e,!0),i(n),r)try{return s(t,e,n)}catch(a){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},"87b3":function(t,e,n){var i=Date.prototype,r="Invalid Date",o="toString",s=i[o],a=i.getTime;new Date(NaN)+""!=r&&n("2aba")(i,o,(function(){var t=a.call(this);return t===t?s.call(this):r}))},8875:function(t,e,n){var i,r,o;(function(n,s){r=[],i=s,o="function"===typeof i?i.apply(e,r):i,void 0===o||(t.exports=o)})("undefined"!==typeof self&&self,(function(){function t(){if(document.currentScript)return document.currentScript;try{throw new Error}catch(h){var t,e,n,i=/.*at [^(]*\((.*):(.+):(.+)\)$/gi,r=/@([^@]*):(\d+):(\d+)\s*$/gi,o=i.exec(h.stack)||r.exec(h.stack),s=o&&o[1]||!1,a=o&&o[2]||!1,u=document.location.href.replace(document.location.hash,""),l=document.getElementsByTagName("script");s===u&&(t=document.documentElement.outerHTML,e=new RegExp("(?:[^\\n]+?\\n){0,"+(a-2)+"}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*","i"),n=t.replace(e,"$1").trim());for(var c=0;c<l.length;c++){if("interactive"===l[c].readyState)return l[c];if(l[c].src===s)return l[c];if(s===u&&l[c].innerHTML&&l[c].innerHTML.trim()===n)return l[c]}return null}}return t}))},"89ca":function(t,e,n){n("b42c"),n("93c4"),t.exports=n("d38f")},"8a12":function(t,e,n){var i=n("da3c"),r=i.navigator;t.exports=r&&r.userAgent||""},"8a81":function(t,e,n){"use strict";var i=n("7726"),r=n("69a8"),o=n("9e1e"),s=n("5ca1"),a=n("2aba"),u=n("67ab").KEY,l=n("79e5"),c=n("5537"),h=n("7f20"),d=n("ca5a"),f=n("2b4c"),p=n("37c8"),g=n("3a72"),v=n("d4c0"),m=n("1169"),b=n("cb7c"),y=n("d3f4"),x=n("4bf8"),P=n("6821"),_=n("6a99"),C=n("4630"),E=n("2aeb"),j=n("7bbc"),S=n("11e9"),w=n("2621"),D=n("86cc"),A=n("0d58"),O=S.f,I=D.f,k=j.f,T=i.Symbol,M=i.JSON,L=M&&M.stringify,F="prototype",R=f("_hidden"),N=f("toPrimitive"),G={}.propertyIsEnumerable,H=c("symbol-registry"),B=c("symbols"),U=c("op-symbols"),z=Object[F],W="function"==typeof T&&!!w.f,Y=i.QObject,X=!Y||!Y[F]||!Y[F].findChild,V=o&&l((function(){return 7!=E(I({},"a",{get:function(){return I(this,"a",{value:7}).a}})).a}))?function(t,e,n){var i=O(z,e);i&&delete z[e],I(t,e,n),i&&t!==z&&I(z,e,i)}:I,q=function(t){var e=B[t]=E(T[F]);return e._k=t,e},$=W&&"symbol"==typeof T.iterator?function(t){return"symbol"==typeof t}:function(t){return t instanceof T},J=function(t,e,n){return t===z&&J(U,e,n),b(t),e=_(e,!0),b(n),r(B,e)?(n.enumerable?(r(t,R)&&t[R][e]&&(t[R][e]=!1),n=E(n,{enumerable:C(0,!1)})):(r(t,R)||I(t,R,C(1,{})),t[R][e]=!0),V(t,e,n)):I(t,e,n)},K=function(t,e){b(t);var n,i=v(e=P(e)),r=0,o=i.length;while(o>r)J(t,n=i[r++],e[n]);return t},Z=function(t,e){return void 0===e?E(t):K(E(t),e)},Q=function(t){var e=G.call(this,t=_(t,!0));return!(this===z&&r(B,t)&&!r(U,t))&&(!(e||!r(this,t)||!r(B,t)||r(this,R)&&this[R][t])||e)},tt=function(t,e){if(t=P(t),e=_(e,!0),t!==z||!r(B,e)||r(U,e)){var n=O(t,e);return!n||!r(B,e)||r(t,R)&&t[R][e]||(n.enumerable=!0),n}},et=function(t){var e,n=k(P(t)),i=[],o=0;while(n.length>o)r(B,e=n[o++])||e==R||e==u||i.push(e);return i},nt=function(t){var e,n=t===z,i=k(n?U:P(t)),o=[],s=0;while(i.length>s)!r(B,e=i[s++])||n&&!r(z,e)||o.push(B[e]);return o};W||(T=function(){if(this instanceof T)throw TypeError("Symbol is not a constructor!");var t=d(arguments.length>0?arguments[0]:void 0),e=function(n){this===z&&e.call(U,n),r(this,R)&&r(this[R],t)&&(this[R][t]=!1),V(this,t,C(1,n))};return o&&X&&V(z,t,{configurable:!0,set:e}),q(t)},a(T[F],"toString",(function(){return this._k})),S.f=tt,D.f=J,n("9093").f=j.f=et,n("52a7").f=Q,w.f=nt,o&&!n("2d00")&&a(z,"propertyIsEnumerable",Q,!0),p.f=function(t){return q(f(t))}),s(s.G+s.W+s.F*!W,{Symbol:T});for(var it="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),rt=0;it.length>rt;)f(it[rt++]);for(var ot=A(f.store),st=0;ot.length>st;)g(ot[st++]);s(s.S+s.F*!W,"Symbol",{for:function(t){return r(H,t+="")?H[t]:H[t]=T(t)},keyFor:function(t){if(!$(t))throw TypeError(t+" is not a symbol!");for(var e in H)if(H[e]===t)return e},useSetter:function(){X=!0},useSimple:function(){X=!1}}),s(s.S+s.F*!W,"Object",{create:Z,defineProperty:J,defineProperties:K,getOwnPropertyDescriptor:tt,getOwnPropertyNames:et,getOwnPropertySymbols:nt});var at=l((function(){w.f(1)}));s(s.S+s.F*at,"Object",{getOwnPropertySymbols:function(t){return w.f(x(t))}}),M&&s(s.S+s.F*(!W||l((function(){var t=T();return"[null]"!=L([t])||"{}"!=L({a:t})||"{}"!=L(Object(t))}))),"JSON",{stringify:function(t){var e,n,i=[t],r=1;while(arguments.length>r)i.push(arguments[r++]);if(n=e=i[1],(y(e)||void 0!==t)&&!$(t))return m(e)||(e=function(t,e){if("function"==typeof n&&(e=n.call(this,t,e)),!$(e))return e}),i[1]=e,L.apply(M,i)}}),T[F][N]||n("32e9")(T[F],N,T[F].valueOf),h(T,"Symbol"),h(Math,"Math",!0),h(i.JSON,"JSON",!0)},"8b97":function(t,e,n){var i=n("d3f4"),r=n("cb7c"),o=function(t,e){if(r(t),!i(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,i){try{i=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),i(t,[]),e=!(t instanceof Array)}catch(r){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:i(t,n),t}}({},!1):void 0),check:o}},"8bab":function(t,e,n){var i=n("6e1f");t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==i(t)?t.split(""):Object(t)}},"8ce0":function(t,e,n){var i=n("3adc"),r=n("f845");t.exports=n("7d95")?function(t,e,n){return i.f(t,e,r(1,n))}:function(t,e,n){return t[e]=n,t}},"8e6e":function(t,e,n){var i=n("5ca1"),r=n("990b"),o=n("6821"),s=n("11e9"),a=n("f1ae");i(i.S,"Object",{getOwnPropertyDescriptors:function(t){var e,n,i=o(t),u=s.f,l=r(i),c={},h=0;while(l.length>h)n=u(i,e=l[h++]),void 0!==n&&a(c,e,n);return c}})},9093:function(t,e,n){var i=n("ce10"),r=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,r)}},"93c4":function(t,e,n){"use strict";var i=n("2a4e")(!0);n("e4a9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=i(e,n),this._i+=t.length,{value:t,done:!1})}))},"96cf":function(t,e,n){var i=function(t){"use strict";var e,n=Object.prototype,i=n.hasOwnProperty,r="function"===typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",s=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function u(t,e,n,i){var r=e&&e.prototype instanceof g?e:g,o=Object.create(r.prototype),s=new D(i||[]);return o._invoke=E(t,n,s),o}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(i){return{type:"throw",arg:i}}}t.wrap=u;var c="suspendedStart",h="suspendedYield",d="executing",f="completed",p={};function g(){}function v(){}function m(){}var b={};b[o]=function(){return this};var y=Object.getPrototypeOf,x=y&&y(y(A([])));x&&x!==n&&i.call(x,o)&&(b=x);var P=m.prototype=g.prototype=Object.create(b);function _(t){["next","throw","return"].forEach((function(e){t[e]=function(t){return this._invoke(e,t)}}))}function C(t){function e(n,r,o,s){var a=l(t[n],t,r);if("throw"!==a.type){var u=a.arg,c=u.value;return c&&"object"===typeof c&&i.call(c,"__await")?Promise.resolve(c.__await).then((function(t){e("next",t,o,s)}),(function(t){e("throw",t,o,s)})):Promise.resolve(c).then((function(t){u.value=t,o(u)}),(function(t){return e("throw",t,o,s)}))}s(a.arg)}var n;function r(t,i){function r(){return new Promise((function(n,r){e(t,i,n,r)}))}return n=n?n.then(r,r):r()}this._invoke=r}function E(t,e,n){var i=c;return function(r,o){if(i===d)throw new Error("Generator is already running");if(i===f){if("throw"===r)throw o;return O()}n.method=r,n.arg=o;while(1){var s=n.delegate;if(s){var a=j(s,n);if(a){if(a===p)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===c)throw i=f,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=d;var u=l(t,e,n);if("normal"===u.type){if(i=n.done?f:h,u.arg===p)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=f,n.method="throw",n.arg=u.arg)}}}function j(t,n){var i=t.iterator[n.method];if(i===e){if(n.delegate=null,"throw"===n.method){if(t.iterator["return"]&&(n.method="return",n.arg=e,j(t,n),"throw"===n.method))return p;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var r=l(i,t.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,p;var o=r.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,p):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,p)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function w(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function A(t){if(t){var n=t[o];if(n)return n.call(t);if("function"===typeof t.next)return t;if(!isNaN(t.length)){var r=-1,s=function n(){while(++r<t.length)if(i.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return s.next=s}}return{next:O}}function O(){return{value:e,done:!0}}return v.prototype=P.constructor=m,m.constructor=v,m[a]=v.displayName="GeneratorFunction",t.isGeneratorFunction=function(t){var e="function"===typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,a in t||(t[a]="GeneratorFunction")),t.prototype=Object.create(P),t},t.awrap=function(t){return{__await:t}},_(C.prototype),C.prototype[s]=function(){return this},t.AsyncIterator=C,t.async=function(e,n,i,r){var o=new C(u(e,n,i,r));return t.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},_(P),P[a]="Generator",P[o]=function(){return this},P.toString=function(){return"[object Generator]"},t.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){while(e.length){var i=e.pop();if(i in t)return n.value=i,n.done=!1,n}return n.done=!0,n}},t.values=A,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(w),!t)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0],e=t.completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(i,r){return a.type="throw",a.arg=t,n.next=i,r&&(n.method="next",n.arg=e),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return r("end");if(s.tryLoc<=this.prev){var u=i.call(s,"catchLoc"),l=i.call(s,"finallyLoc");if(u&&l){if(this.prev<s.catchLoc)return r(s.catchLoc,!0);if(this.prev<s.finallyLoc)return r(s.finallyLoc)}else if(u){if(this.prev<s.catchLoc)return r(s.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return r(s.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=t,s.arg=e,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),w(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var i=n.completion;if("throw"===i.type){var r=i.arg;w(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,i){return this.delegate={iterator:A(t),resultName:n,nextLoc:i},"next"===this.method&&(this.arg=e),p}},t}(t.exports);try{regeneratorRuntime=i}catch(r){Function("r","regeneratorRuntime = r")(i)}},"990b":function(t,e,n){var i=n("9093"),r=n("2621"),o=n("cb7c"),s=n("7726").Reflect;t.exports=s&&s.ownKeys||function(t){var e=i.f(o(t)),n=r.f;return n?e.concat(n(t)):e}},"9b43":function(t,e,n){var i=n("d8e8");t.exports=function(t,e,n){if(i(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,i){return t.call(e,n,i)};case 3:return function(n,i,r){return t.call(e,n,i,r)}}return function(){return t.apply(e,arguments)}}},"9c6c":function(t,e,n){var i=n("2b4c")("unscopables"),r=Array.prototype;void 0==r[i]&&n("32e9")(r,i,{}),t.exports=function(t){r[i][t]=!0}},"9c93":function(t,e,n){var i=n("0f89");t.exports=function(t,e,n,r){try{return r?e(i(n)[0],n[1]):e(n)}catch(s){var o=t["return"];throw void 0!==o&&i(o.call(t)),s}}},"9def":function(t,e,n){var i=n("4588"),r=Math.min;t.exports=function(t){return t>0?r(i(t),9007199254740991):0}},"9e1e":function(t,e,n){t.exports=!n("79e5")((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},a47f:function(t,e,n){t.exports=!n("7d95")&&!n("d782")((function(){return 7!=Object.defineProperty(n("12fd")("div"),"a",{get:function(){return 7}}).a}))},a481:function(t,e,n){"use strict";var i=n("cb7c"),r=n("4bf8"),o=n("9def"),s=n("4588"),a=n("0390"),u=n("5f1b"),l=Math.max,c=Math.min,h=Math.floor,d=/\$([$&`']|\d\d?|<[^>]*>)/g,f=/\$([$&`']|\d\d?)/g,p=function(t){return void 0===t?t:String(t)};n("214f")("replace",2,(function(t,e,n,g){return[function(i,r){var o=t(this),s=void 0==i?void 0:i[e];return void 0!==s?s.call(i,o,r):n.call(String(o),i,r)},function(t,e){var r=g(n,t,this,e);if(r.done)return r.value;var h=i(t),d=String(this),f="function"===typeof e;f||(e=String(e));var m=h.global;if(m){var b=h.unicode;h.lastIndex=0}var y=[];while(1){var x=u(h,d);if(null===x)break;if(y.push(x),!m)break;var P=String(x[0]);""===P&&(h.lastIndex=a(d,o(h.lastIndex),b))}for(var _="",C=0,E=0;E<y.length;E++){x=y[E];for(var j=String(x[0]),S=l(c(s(x.index),d.length),0),w=[],D=1;D<x.length;D++)w.push(p(x[D]));var A=x.groups;if(f){var O=[j].concat(w,S,d);void 0!==A&&O.push(A);var I=String(e.apply(void 0,O))}else I=v(j,d,S,w,A,e);S>=C&&(_+=d.slice(C,S)+I,C=S+j.length)}return _+d.slice(C)}];function v(t,e,i,o,s,a){var u=i+t.length,l=o.length,c=f;return void 0!==s&&(s=r(s),c=d),n.call(a,c,(function(n,r){var a;switch(r.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,i);case"'":return e.slice(u);case"<":a=s[r.slice(1,-1)];break;default:var c=+r;if(0===c)return n;if(c>l){var d=h(c/10);return 0===d?n:d<=l?void 0===o[d-1]?r.charAt(1):o[d-1]+r.charAt(1):n}a=o[c-1]}return void 0===a?"":a}))}}))},a5ab:function(t,e,n){var i=n("a812"),r=Math.min;t.exports=function(t){return t>0?r(i(t),9007199254740991):0}},a745:function(t,e,n){t.exports=n("d604")},a7d3:function(t,e){var n=t.exports={version:"2.6.11"};"number"==typeof __e&&(__e=n)},a812:function(t,e){var n=Math.ceil,i=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?i:n)(t)}},aa77:function(t,e,n){var i=n("5ca1"),r=n("be13"),o=n("79e5"),s=n("fdef"),a="["+s+"]",u="​",l=RegExp("^"+a+a+"*"),c=RegExp(a+a+"*$"),h=function(t,e,n){var r={},a=o((function(){return!!s[t]()||u[t]()!=u})),l=r[t]=a?e(d):s[t];n&&(r[n]=l),i(i.P+i.F*a,"String",r)},d=h.trim=function(t,e){return t=String(r(t)),1&e&&(t=t.replace(l,"")),2&e&&(t=t.replace(c,"")),t};t.exports=h},aae3:function(t,e,n){var i=n("d3f4"),r=n("2d95"),o=n("2b4c")("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==r(t))}},ac4d:function(t,e,n){n("3a72")("asyncIterator")},ac6a:function(t,e,n){for(var i=n("cadf"),r=n("0d58"),o=n("2aba"),s=n("7726"),a=n("32e9"),u=n("84f2"),l=n("2b4c"),c=l("iterator"),h=l("toStringTag"),d=u.Array,f={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=r(f),g=0;g<p.length;g++){var v,m=p[g],b=f[m],y=s[m],x=y&&y.prototype;if(x&&(x[c]||a(x,c,d),x[h]||a(x,h,m),u[m]=d,b))for(v in i)x[v]||o(x,v,i[v],!0)}},b0bc:function(t,e){t.exports=function(t,e,n,i){if(!(t instanceof e)||void 0!==i&&i in t)throw TypeError(n+": incorrect invocation!");return t}},b0c5:function(t,e,n){"use strict";var i=n("520a");n("5ca1")({target:"RegExp",proto:!0,forced:i!==/./.exec},{exec:i})},b22a:function(t,e){t.exports={}},b39a:function(t,e,n){var i=n("d3f4");t.exports=function(t,e){if(!i(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},b3e7:function(t,e){t.exports=function(){}},b3ec:function(t,e,n){"use strict";var i=n("3adc"),r=n("f845");t.exports=function(t,e,n){e in t?i.f(t,e,r(0,n)):t[e]=n}},b42c:function(t,e,n){n("fa54");for(var i=n("da3c"),r=n("8ce0"),o=n("b22a"),s=n("1b55")("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),u=0;u<a.length;u++){var l=a[u],c=i[l],h=c&&c.prototype;h&&!h[s]&&r(h,s,l),o[l]=o.Array}},b457:function(t,e){t.exports=!0},b5aa:function(t,e,n){var i=n("6e1f");t.exports=Array.isArray||function(t){return"Array"==i(t)}},b604:function(t,e,n){"use strict";var i=n("d13f"),r=n("a7d3"),o=n("da3c"),s=n("302f"),a=n("decf");i(i.P+i.R,"Promise",{finally:function(t){var e=s(this,r.Promise||o.Promise),n="function"==typeof t;return this.then(n?function(n){return a(e,t()).then((function(){return n}))}:t,n?function(n){return a(e,t()).then((function(){throw n}))}:t)}})},b635:function(t,e,n){"use strict";(function(t){n.d(e,"b",(function(){return r})),n("6f42");var i=n("3425");function r(t){r.installed||(r.installed=!0,t.component("VueDraggableResizable",i["a"]))}var o={install:r},s=null;"undefined"!==typeof window?s=window.Vue:"undefined"!==typeof t&&(s=t.Vue),s&&s.use(o),e["a"]=i["a"]}).call(this,n("c8ba"))},b77f:function(t,e,n){var i=n("0f89"),r=n("f159");t.exports=n("a7d3").getIterator=function(t){var e=r(t);if("function"!=typeof e)throw TypeError(t+" is not iterable!");return i(e.call(t))}},bc25:function(t,e,n){var i=n("f2fe");t.exports=function(t,e,n){if(i(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,i){return t.call(e,n,i)};case 3:return function(n,i,r){return t.call(e,n,i,r)}}return function(){return t.apply(e,arguments)}}},be13:function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},c0d8:function(t,e,n){var i=n("3adc").f,r=n("43c8"),o=n("1b55")("toStringTag");t.exports=function(t,e,n){t&&!r(t=n?t:t.prototype,o)&&i(t,o,{configurable:!0,value:e})}},c227:function(t,e,n){var i=n("b22a"),r=n("1b55")("iterator"),o=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||o[r]===t)}},c26b:function(t,e,n){"use strict";var i=n("86cc").f,r=n("2aeb"),o=n("dcbc"),s=n("9b43"),a=n("f605"),u=n("4a59"),l=n("01f9"),c=n("d53b"),h=n("7a56"),d=n("9e1e"),f=n("67ab").fastKey,p=n("b39a"),g=d?"_s":"size",v=function(t,e){var n,i=f(e);if("F"!==i)return t._i[i];for(n=t._f;n;n=n.n)if(n.k==e)return n};t.exports={getConstructor:function(t,e,n,l){var c=t((function(t,i){a(t,c,e,"_i"),t._t=e,t._i=r(null),t._f=void 0,t._l=void 0,t[g]=0,void 0!=i&&u(i,n,t[l],t)}));return o(c.prototype,{clear:function(){for(var t=p(this,e),n=t._i,i=t._f;i;i=i.n)i.r=!0,i.p&&(i.p=i.p.n=void 0),delete n[i.i];t._f=t._l=void 0,t[g]=0},delete:function(t){var n=p(this,e),i=v(n,t);if(i){var r=i.n,o=i.p;delete n._i[i.i],i.r=!0,o&&(o.n=r),r&&(r.p=o),n._f==i&&(n._f=r),n._l==i&&(n._l=o),n[g]--}return!!i},forEach:function(t){p(this,e);var n,i=s(t,arguments.length>1?arguments[1]:void 0,3);while(n=n?n.n:this._f){i(n.v,n.k,this);while(n&&n.r)n=n.p}},has:function(t){return!!v(p(this,e),t)}}),d&&i(c.prototype,"size",{get:function(){return p(this,e)[g]}}),c},def:function(t,e,n){var i,r,o=v(t,e);return o?o.v=n:(t._l=o={i:r=f(e,!0),k:e,v:n,p:i=t._l,n:void 0,r:!1},t._f||(t._f=o),i&&(i.n=o),t[g]++,"F"!==r&&(t._i[r]=o)),t},getEntry:v,setStrong:function(t,e,n){l(t,e,(function(t,n){this._t=p(t,e),this._k=n,this._l=void 0}),(function(){var t=this,e=t._k,n=t._l;while(n&&n.r)n=n.p;return t._t&&(t._l=n=n?n.n:t._t._f)?c(0,"keys"==e?n.k:"values"==e?n.v:[n.k,n.v]):(t._t=void 0,c(1))}),n?"entries":"values",!n,!0),h(e)}}},c366:function(t,e,n){var i=n("6821"),r=n("9def"),o=n("77f1");t.exports=function(t){return function(e,n,s){var a,u=i(e),l=r(u.length),c=o(s,l);if(t&&n!=n){while(l>c)if(a=u[c++],a!=a)return!0}else for(;l>c;c++)if((t||c in u)&&u[c]===n)return t||c||0;return!t&&-1}}},c5f6:function(t,e,n){"use strict";var i=n("7726"),r=n("69a8"),o=n("2d95"),s=n("5dbc"),a=n("6a99"),u=n("79e5"),l=n("9093").f,c=n("11e9").f,h=n("86cc").f,d=n("aa77").trim,f="Number",p=i[f],g=p,v=p.prototype,m=o(n("2aeb")(v))==f,b="trim"in String.prototype,y=function(t){var e=a(t,!1);if("string"==typeof e&&e.length>2){e=b?e.trim():d(e,3);var n,i,r,o=e.charCodeAt(0);if(43===o||45===o){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===o){switch(e.charCodeAt(1)){case 66:case 98:i=2,r=49;break;case 79:case 111:i=8,r=55;break;default:return+e}for(var s,u=e.slice(2),l=0,c=u.length;l<c;l++)if(s=u.charCodeAt(l),s<48||s>r)return NaN;return parseInt(u,i)}}return+e};if(!p(" 0o1")||!p("0b1")||p("+0x1")){p=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof p&&(m?u((function(){v.valueOf.call(n)})):o(n)!=f)?s(new g(y(e)),n,p):y(e)};for(var x,P=n("9e1e")?l(g):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),_=0;P.length>_;_++)r(g,x=P[_])&&!r(p,x)&&h(p,x,c(g,x));p.prototype=v,v.constructor=p,n("2aba")(i,f,p)}},c609:function(t,e,n){"use strict";var i=n("d13f"),r=n("03ca"),o=n("75c9");i(i.S,"Promise",{try:function(t){var e=r.f(this),n=o(t);return(n.e?e.reject:e.resolve)(n.v),e.promise}})},c69a:function(t,e,n){t.exports=!n("9e1e")&&!n("79e5")((function(){return 7!=Object.defineProperty(n("230e")("div"),"a",{get:function(){return 7}}).a}))},c8ba:function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(i){"object"===typeof window&&(n=window)}t.exports=n},c8bb:function(t,e,n){t.exports=n("89ca")},ca5a:function(t,e){var n=0,i=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+i).toString(36))}},cadf:function(t,e,n){"use strict";var i=n("9c6c"),r=n("d53b"),o=n("84f2"),s=n("6821");t.exports=n("01f9")(Array,"Array",(function(t,e){this._t=s(t),this._i=0,this._k=e}),(function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,r(1)):r(0,"keys"==e?n:"values"==e?t[n]:[n,t[n]])}),"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},cb7c:function(t,e,n){var i=n("d3f4");t.exports=function(t){if(!i(t))throw TypeError(t+" is not an object!");return t}},cd1c:function(t,e,n){var i=n("e853");t.exports=function(t,e){return new(i(t))(e)}},ce10:function(t,e,n){var i=n("69a8"),r=n("6821"),o=n("c366")(!1),s=n("613b")("IE_PROTO");t.exports=function(t,e){var n,a=r(t),u=0,l=[];for(n in a)n!=s&&i(a,n)&&l.push(n);while(e.length>u)i(a,n=e[u++])&&(~o(l,n)||l.push(n));return l}},d13f:function(t,e,n){var i=n("da3c"),r=n("a7d3"),o=n("bc25"),s=n("8ce0"),a=n("43c8"),u="prototype",l=function(t,e,n){var c,h,d,f=t&l.F,p=t&l.G,g=t&l.S,v=t&l.P,m=t&l.B,b=t&l.W,y=p?r:r[e]||(r[e]={}),x=y[u],P=p?i:g?i[e]:(i[e]||{})[u];for(c in p&&(n=e),n)h=!f&&P&&void 0!==P[c],h&&a(y,c)||(d=h?P[c]:n[c],y[c]=p&&"function"!=typeof P[c]?n[c]:m&&h?o(d,i):b&&P[c]==d?function(t){var e=function(e,n,i){if(this instanceof t){switch(arguments.length){case 0:return new t;case 1:return new t(e);case 2:return new t(e,n)}return new t(e,n,i)}return t.apply(this,arguments)};return e[u]=t[u],e}(d):v&&"function"==typeof d?o(Function.call,d):d,v&&((y.virtual||(y.virtual={}))[c]=d,t&l.R&&x&&!x[c]&&s(x,c,d)))};l.F=1,l.G=2,l.S=4,l.P=8,l.B=16,l.W=32,l.U=64,l.R=128,t.exports=l},d25f:function(t,e,n){"use strict";var i=n("5ca1"),r=n("0a49")(2);i(i.P+i.F*!n("2f21")([].filter,!0),"Array",{filter:function(t){return r(this,t,arguments[1])}})},d2c8:function(t,e,n){var i=n("aae3"),r=n("be13");t.exports=function(t,e,n){if(i(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(r(t))}},d38f:function(t,e,n){var i=n("7d8a"),r=n("1b55")("iterator"),o=n("b22a");t.exports=n("a7d3").isIterable=function(t){var e=Object(t);return void 0!==e[r]||"@@iterator"in e||o.hasOwnProperty(i(e))}},d3f4:function(t,e){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},d4c0:function(t,e,n){var i=n("0d58"),r=n("2621"),o=n("52a7");t.exports=function(t){var e=i(t),n=r.f;if(n){var s,a=n(t),u=o.f,l=0;while(a.length>l)u.call(t,s=a[l++])&&e.push(s)}return e}},d53b:function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},d604:function(t,e,n){n("1938"),t.exports=n("a7d3").Array.isArray},d782:function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},d8e8:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},da3c:function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},dcbc:function(t,e,n){var i=n("2aba");t.exports=function(t,e,n){for(var r in e)i(t,r,e[r],n);return t}},dd04:function(t,e,n){n("12fd9"),n("93c4"),n("b42c"),n("5b5f"),n("b604"),n("c609"),t.exports=n("a7d3").Promise},decf:function(t,e,n){var i=n("0f89"),r=n("6f8a"),o=n("03ca");t.exports=function(t,e){if(i(t),r(e)&&e.constructor===t)return e;var n=o.f(t),s=n.resolve;return s(e),n.promise}},df0a:function(t,e,n){var i,r,o,s=n("bc25"),a=n("196c"),u=n("103a"),l=n("12fd"),c=n("da3c"),h=c.process,d=c.setImmediate,f=c.clearImmediate,p=c.MessageChannel,g=c.Dispatch,v=0,m={},b="onreadystatechange",y=function(){var t=+this;if(m.hasOwnProperty(t)){var e=m[t];delete m[t],e()}},x=function(t){y.call(t.data)};d&&f||(d=function(t){var e=[],n=1;while(arguments.length>n)e.push(arguments[n++]);return m[++v]=function(){a("function"==typeof t?t:Function(t),e)},i(v),v},f=function(t){delete m[t]},"process"==n("6e1f")(h)?i=function(t){h.nextTick(s(y,t,1))}:g&&g.now?i=function(t){g.now(s(y,t,1))}:p?(r=new p,o=r.port2,r.port1.onmessage=x,i=s(o.postMessage,o,1)):c.addEventListener&&"function"==typeof postMessage&&!c.importScripts?(i=function(t){c.postMessage(t+"","*")},c.addEventListener("message",x,!1)):i=b in l("script")?function(t){u.appendChild(l("script"))[b]=function(){u.removeChild(this),y.call(t)}}:function(t){setTimeout(s(y,t,1),0)}),t.exports={set:d,clear:f}},e0b8:function(t,e,n){"use strict";var i=n("7726"),r=n("5ca1"),o=n("2aba"),s=n("dcbc"),a=n("67ab"),u=n("4a59"),l=n("f605"),c=n("d3f4"),h=n("79e5"),d=n("5cc5"),f=n("7f20"),p=n("5dbc");t.exports=function(t,e,n,g,v,m){var b=i[t],y=b,x=v?"set":"add",P=y&&y.prototype,_={},C=function(t){var e=P[t];o(P,t,"delete"==t||"has"==t?function(t){return!(m&&!c(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return m&&!c(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,n){return e.call(this,0===t?0:t,n),this})};if("function"==typeof y&&(m||P.forEach&&!h((function(){(new y).entries().next()})))){var E=new y,j=E[x](m?{}:-0,1)!=E,S=h((function(){E.has(1)})),w=d((function(t){new y(t)})),D=!m&&h((function(){var t=new y,e=5;while(e--)t[x](e,e);return!t.has(-0)}));w||(y=e((function(e,n){l(e,y,t);var i=p(new b,e,y);return void 0!=n&&u(n,v,i[x],i),i})),y.prototype=P,P.constructor=y),(S||D)&&(C("delete"),C("has"),v&&C("get")),(D||j)&&C(x),m&&P.clear&&delete P.clear}else y=g.getConstructor(e,t,v,x),s(y.prototype,n),a.NEED=!0;return f(y,t),_[t]=y,r(r.G+r.W+r.F*(y!=b),_),m||g.setStrong(y,t,v),y}},e11e:function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},e341:function(t,e,n){var i=n("d13f");i(i.S+i.F*!n("7d95"),"Object",{defineProperty:n("3adc").f})},e4a9:function(t,e,n){"use strict";var i=n("b457"),r=n("d13f"),o=n("2312"),s=n("8ce0"),a=n("b22a"),u=n("5ce7"),l=n("c0d8"),c=n("ff0c"),h=n("1b55")("iterator"),d=!([].keys&&"next"in[].keys()),f="@@iterator",p="keys",g="values",v=function(){return this};t.exports=function(t,e,n,m,b,y,x){u(n,e,m);var P,_,C,E=function(t){if(!d&&t in D)return D[t];switch(t){case p:return function(){return new n(this,t)};case g:return function(){return new n(this,t)}}return function(){return new n(this,t)}},j=e+" Iterator",S=b==g,w=!1,D=t.prototype,A=D[h]||D[f]||b&&D[b],O=A||E(b),I=b?S?E("entries"):O:void 0,k="Array"==e&&D.entries||A;if(k&&(C=c(k.call(new t)),C!==Object.prototype&&C.next&&(l(C,j,!0),i||"function"==typeof C[h]||s(C,h,v))),S&&A&&A.name!==g&&(w=!0,O=function(){return A.call(this)}),i&&!x||!d&&!w&&D[h]||s(D,h,O),a[e]=O,a[j]=v,b)if(P={values:S?O:E(g),keys:y?O:E(p),entries:I},x)for(_ in P)_ in D||o(D,_,P[_]);else r(r.P+r.F*(d||w),e,P);return P}},e5fa:function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},e853:function(t,e,n){var i=n("d3f4"),r=n("1169"),o=n("2b4c")("species");t.exports=function(t){var e;return r(t)&&(e=t.constructor,"function"!=typeof e||e!==Array&&!r(e.prototype)||(e=void 0),i(e)&&(e=e[o],null===e&&(e=void 0))),void 0===e?Array:e}},ebd6:function(t,e,n){var i=n("cb7c"),r=n("d8e8"),o=n("2b4c")("species");t.exports=function(t,e){var n,s=i(t).constructor;return void 0===s||void 0==(n=i(s)[o])?e:r(n)}},ec5b:function(t,e,n){n("e341");var i=n("a7d3").Object;t.exports=function(t,e,n){return i.defineProperty(t,e,n)}},f159:function(t,e,n){var i=n("7d8a"),r=n("1b55")("iterator"),o=n("b22a");t.exports=n("a7d3").getIteratorMethod=function(t){if(void 0!=t)return t[r]||t["@@iterator"]||o[i(t)]}},f1ae:function(t,e,n){"use strict";var i=n("86cc"),r=n("4630");t.exports=function(t,e,n){e in t?i.f(t,e,r(0,n)):t[e]=n}},f2fe:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},f3e2:function(t,e,n){"use strict";var i=n("5ca1"),r=n("0a49")(0),o=n("2f21")([].forEach,!0);i(i.P+i.F*!o,"Array",{forEach:function(t){return r(this,t,arguments[1])}})},f568:function(t,e,n){var i=n("3adc"),r=n("0f89"),o=n("7633");t.exports=n("7d95")?Object.defineProperties:function(t,e){r(t);var n,s=o(e),a=s.length,u=0;while(a>u)i.f(t,n=s[u++],e[n]);return t}},f605:function(t,e){t.exports=function(t,e,n,i){if(!(t instanceof e)||void 0!==i&&i in t)throw TypeError(n+": incorrect invocation!");return t}},f845:function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},fa54:function(t,e,n){"use strict";var i=n("b3e7"),r=n("245b"),o=n("b22a"),s=n("6a9b");t.exports=n("e4a9")(Array,"Array",(function(t,e){this._t=s(t),this._i=0,this._k=e}),(function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,r(1)):r(0,"keys"==e?n:"values"==e?t[n]:[n,t[n]])}),"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},fa5b:function(t,e,n){t.exports=n("5537")("native-function-to-string",Function.toString)},fab2:function(t,e,n){var i=n("7726").document;t.exports=i&&i.documentElement},fb15:function(t,e,n){"use strict";if(n.r(e),n.d(e,"install",(function(){return s["b"]})),"undefined"!==typeof window){var i=window.document.currentScript,r=n("8875");i=r(),"currentScript"in document||Object.defineProperty(document,"currentScript",{get:r});var o=i&&i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);o&&(n.p=o[1])}var s=n("b635");e["default"]=s["a"]},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},ff0c:function(t,e,n){var i=n("43c8"),r=n("0185"),o=n("5d8f")("IE_PROTO"),s=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=r(t),i(t,o)?t[o]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?s:null}}})["default"]}))},c78c:function(t,e,n){},cb29:function(t,e,n){"use strict";var i=n("23e7"),r=n("81d5"),o=n("44d2");i({target:"Array",proto:!0},{fill:r}),o("fill")},e193:function(t,e,n){(function(){"undefined"==typeof Math.sgn&&(Math.sgn=function(t){return 0==t?0:t>0?1:-1});var t={subtract:function(t,e){return{x:t.x-e.x,y:t.y-e.y}},dotProduct:function(t,e){return t.x*e.x+t.y*e.y},square:function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},scale:function(t,e){return{x:t.x*e,y:t.y*e}}},n=64,i=Math.pow(2,-n-1),r=function(e,n){for(var i=[],r=s(e,n),o=n.length-1,u=2*o-1,l=a(r,u,i,0),c=t.subtract(e,n[0]),d=t.square(c),f=0,p=0;p<l;p++){c=t.subtract(e,h(n,o,i[p],null,null));var g=t.square(c);g<d&&(d=g,f=i[p])}return c=t.subtract(e,n[o]),g=t.square(c),g<d&&(d=g,f=1),{location:f,distance:d}},o=function(t,e){var n=r(t,e);return{point:h(e,e.length-1,n.location,null,null),location:n.location}},s=function(e,n){for(var i=n.length-1,r=2*i-1,o=[],s=[],a=[],u=[],l=[[1,.6,.3,.1],[.4,.6,.6,.4],[.1,.3,.6,1]],c=0;c<=i;c++)o[c]=t.subtract(n[c],e);for(c=0;c<=i-1;c++)s[c]=t.subtract(n[c+1],n[c]),s[c]=t.scale(s[c],3);for(var h=0;h<=i-1;h++)for(var d=0;d<=i;d++)a[h]||(a[h]=[]),a[h][d]=t.dotProduct(s[h],o[d]);for(c=0;c<=r;c++)u[c]||(u[c]=[]),u[c].y=0,u[c].x=parseFloat(c)/r;for(var f=i,p=i-1,g=0;g<=f+p;g++){var v=Math.max(0,g-p),m=Math.min(g,f);for(c=v;c<=m;c++){var b=g-c;u[c+b].y+=a[b][c]*l[b][c]}}return u},a=function(t,e,i,r){var o,s,d=[],f=[],p=[],g=[];switch(u(t,e)){case 0:return 0;case 1:if(r>=n)return i[0]=(t[0].x+t[e].x)/2,1;if(l(t,e))return i[0]=c(t,e),1;break}h(t,e,.5,d,f),o=a(d,e,p,r+1),s=a(f,e,g,r+1);for(var v=0;v<o;v++)i[v]=p[v];for(v=0;v<s;v++)i[v+o]=g[v];return o+s},u=function(t,e){var n,i,r=0;n=i=Math.sgn(t[0].y);for(var o=1;o<=e;o++)n=Math.sgn(t[o].y),n!=i&&r++,i=n;return r},l=function(t,e){var n,r,o,s,a,u,l,c,h,d,f,p,g,v,m,b,y,x;u=t[0].y-t[e].y,l=t[e].x-t[0].x,c=t[0].x*t[e].y-t[e].x*t[0].y,y=x=0;for(var P=1;P<e;P++){var _=u*t[P].x+l*t[P].y+c;_>y?y=_:_<x&&(x=_)}return f=0,p=1,g=0,v=u,m=l,b=c-y,h=f*m-v*p,d=1/h,r=(p*b-m*g)*d,v=u,m=l,b=c-x,h=f*m-v*p,d=1/h,o=(p*b-m*g)*d,s=Math.min(r,o),a=Math.max(r,o),n=a-s,n<i?1:0},c=function(t,e){var n=1,i=0,r=t[e].x-t[0].x,o=t[e].y-t[0].y,s=t[0].x-0,a=t[0].y-0,u=r*i-o*n,l=1/u,c=(r*a-o*s)*l;return 0+n*c},h=function(t,e,n,i,r){for(var o=[[]],s=0;s<=e;s++)o[0][s]=t[s];for(var a=1;a<=e;a++)for(s=0;s<=e-a;s++)o[a]||(o[a]=[]),o[a][s]||(o[a][s]={}),o[a][s].x=(1-n)*o[a-1][s].x+n*o[a-1][s+1].x,o[a][s].y=(1-n)*o[a-1][s].y+n*o[a-1][s+1].y;if(null!=i)for(s=0;s<=e;s++)i[s]=o[s][0];if(null!=r)for(s=0;s<=e;s++)r[s]=o[e-s][s];return o[e][0]},d={},f=function(t){var e=d[t];if(!e){e=[];var n=function(){return function(e){return Math.pow(e,t)}},i=function(){return function(e){return Math.pow(1-e,t)}},r=function(t){return function(e){return t}},o=function(){return function(t){return t}},s=function(){return function(t){return 1-t}},a=function(t){return function(e){for(var n=1,i=0;i<t.length;i++)n*=t[i](e);return n}};e.push(new n);for(var u=1;u<t;u++){for(var l=[new r(t)],c=0;c<t-u;c++)l.push(new o);for(c=0;c<u;c++)l.push(new s);e.push(new a(l))}e.push(new i),d[t]=e}return e},p=function(t,e){for(var n=f(t.length-1),i=0,r=0,o=0;o<t.length;o++)i+=t[o].x*n[o](e),r+=t[o].y*n[o](e);return{x:i,y:r}},g=function(t,e){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},v=function(t){return t[0].x===t[1].x&&t[0].y===t[1].y},m=function(t,e,n){if(v(t))return{point:t[0],location:e};var i=p(t,e),r=0,o=e,s=n>0?1:-1,a=null;while(r<Math.abs(n))o+=.005*s,a=p(t,o),r+=g(a,i),i=a;return{point:a,location:o}},b=function(t){(new Date).getTime();if(v(t))return 0;var e=p(t,0),n=0,i=0,r=1,o=null;while(i<1)i+=.005*r,o=p(t,i),n+=g(o,e),e=o;return n},y=function(t,e,n){return m(t,e,n).point},x=function(t,e,n){return m(t,e,n).location},P=function(t,e){var n=p(t,e),i=p(t.slice(0,t.length-1),e),r=i.y-n.y,o=i.x-n.x;return 0===r?1/0:Math.atan(r/o)},_=function(t,e,n){var i=m(t,e,n);return i.location>1&&(i.location=1),i.location<0&&(i.location=0),P(t,i.location)},C=function(t,e,n,i){i=null==i?0:i;var r=m(t,e,i),o=P(t,r.location),s=Math.atan(-1/o),a=n/2*Math.sin(s),u=n/2*Math.cos(s);return[{x:r.point.x+u,y:r.point.y+a},{x:r.point.x-u,y:r.point.y-a}]},E=function(t,e,n,i,r){var o=i-e,s=t-n,a=t*(e-i)+e*(n-t),u=D(r),l=[o*u[0][0]+s*u[1][0],o*u[0][1]+s*u[1][1],o*u[0][2]+s*u[1][2],o*u[0][3]+s*u[1][3]+a],c=O.apply(null,l),h=[];if(null!=c)for(var d=0;d<3;d++){var f,p=c[d],g=Math.pow(p,2),v=Math.pow(p,3),m=[u[0][0]*v+u[0][1]*g+u[0][2]*p+u[0][3],u[1][0]*v+u[1][1]*g+u[1][2]*p+u[1][3]];f=n-t!==0?(m[0]-t)/(n-t):(m[1]-e)/(i-e),p>=0&&p<=1&&f>=0&&f<=1&&h.push(m)}return h},j=function(t,e,n,i,r){var o=[];return o.push.apply(o,E(t,e,t+n,e,r)),o.push.apply(o,E(t+n,e,t+n,e+i,r)),o.push.apply(o,E(t+n,e+i,t,e+i,r)),o.push.apply(o,E(t,e+i,t,e,r)),o},S=function(t,e){var n=[];return n.push.apply(n,E(t.x,t.y,t.x+t.w,t.y,e)),n.push.apply(n,E(t.x+t.w,t.y,t.x+t.w,t.y+t.h,e)),n.push.apply(n,E(t.x+t.w,t.y+t.h,t.x,t.y+t.h,e)),n.push.apply(n,E(t.x,t.y+t.h,t.x,t.y,e)),n};function w(t,e){return[-t[0][e]+3*t[1][e]+-3*t[2][e]+t[3][e],3*t[0][e]-6*t[1][e]+3*t[2][e],-3*t[0][e]+3*t[1][e],t[0][e]]}function D(t){return[w(t,"x"),w(t,"y")]}function A(t){return t<0?-1:t>0?1:0}function O(t,e,n,i){var r,o,s=e/t,a=n/t,u=i/t,l=(3*a-Math.pow(s,2))/9,c=(9*s*a-27*u-2*Math.pow(s,3))/54,h=Math.pow(l,3)+Math.pow(c,2),d=[];if(h>=0)r=A(c+Math.sqrt(h))*Math.pow(Math.abs(c+Math.sqrt(h)),1/3),o=A(c-Math.sqrt(h))*Math.pow(Math.abs(c-Math.sqrt(h)),1/3),d[0]=-s/3+(r+o),d[1]=-s/3-(r+o)/2,d[2]=-s/3-(r+o)/2,0!==Math.abs(Math.sqrt(3)*(r-o)/2)&&(d[1]=-1,d[2]=-1);else{var f=Math.acos(c/Math.sqrt(-Math.pow(l,3)));d[0]=2*Math.sqrt(-l)*Math.cos(f/3)-s/3,d[1]=2*Math.sqrt(-l)*Math.cos((f+2*Math.PI)/3)-s/3,d[2]=2*Math.sqrt(-l)*Math.cos((f+4*Math.PI)/3)-s/3}for(var p=0;p<3;p++)(d[p]<0||d[p]>1)&&(d[p]=-1);return d}var I=this.jsBezier={distanceFromCurve:r,gradientAtPoint:P,gradientAtPointAlongCurveFrom:_,nearestPointOnCurve:o,pointOnCurve:p,pointAlongCurveFrom:y,perpendicularToCurveAt:C,locationAlongCurveFrom:x,getLength:b,lineIntersection:E,boxIntersection:j,boundingBoxIntersection:S,version:"0.9.0"};e.jsBezier=I}).call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,n=t.Biltong={version:"0.4.0"};e.Biltong=n;var i=function(t){return"[object Array]"===Object.prototype.toString.call(t)},r=function(t,e,n){return t=i(t)?t:[t.x,t.y],e=i(e)?e:[e.x,e.y],n(t,e)},o=n.gradient=function(t,e){return r(t,e,(function(t,e){return e[0]==t[0]?e[1]>t[1]?1/0:-1/0:e[1]==t[1]?e[0]>t[0]?0:-0:(e[1]-t[1])/(e[0]-t[0])}))},s=(n.normal=function(t,e){return-1/o(t,e)},n.lineLength=function(t,e){return r(t,e,(function(t,e){return Math.sqrt(Math.pow(e[1]-t[1],2)+Math.pow(e[0]-t[0],2))}))},n.quadrant=function(t,e){return r(t,e,(function(t,e){return e[0]>t[0]||e[0]==t[0]?e[1]>t[1]?2:1:e[1]>t[1]?3:4}))}),a=(n.theta=function(t,e){return r(t,e,(function(t,e){var n=o(t,e),i=Math.atan(n),r=s(t,e);return 4!=r&&3!=r||(i+=Math.PI),i<0&&(i+=2*Math.PI),i}))},n.intersects=function(t,e){var n=t.x,i=t.x+t.w,r=t.y,o=t.y+t.h,s=e.x,a=e.x+e.w,u=e.y,l=e.y+e.h;return n<=s&&s<=i&&r<=u&&u<=o||n<=a&&a<=i&&r<=u&&u<=o||n<=s&&s<=i&&r<=l&&l<=o||n<=a&&s<=i&&r<=l&&l<=o||s<=n&&n<=a&&u<=r&&r<=l||s<=i&&i<=a&&u<=r&&r<=l||s<=n&&n<=a&&u<=o&&o<=l||s<=i&&n<=a&&u<=o&&o<=l},n.encloses=function(t,e,n){var i=t.x,r=t.x+t.w,o=t.y,s=t.y+t.h,a=e.x,u=e.x+e.w,l=e.y,c=e.y+e.h,h=function(t,e,i,r){return n?t<=e&&i>=r:t<e&&i>r};return h(i,a,r,u)&&h(o,l,s,c)},[null,[1,-1],[1,1],[-1,1],[-1,-1]]),u=[null,[-1,-1],[-1,1],[1,1],[1,-1]];n.pointOnLine=function(t,e,n){var i=o(t,e),r=s(t,e),l=n>0?a[r]:u[r],c=Math.atan(i),h=Math.abs(n*Math.sin(c))*l[1],d=Math.abs(n*Math.cos(c))*l[0];return{x:t.x+d,y:t.y+h}},n.perpendicularLineTo=function(t,e,n){var i=o(t,e),r=Math.atan(-1/i),s=n/2*Math.sin(r),a=n/2*Math.cos(r);return[{x:e.x+a,y:e.y+s},{x:e.x-a,y:e.y-s}]}}.call("undefined"!==typeof window?window:this),function(){"use strict";function t(t,e,n,i,r,o,s,a){return new Touch({target:e,identifier:F(),pageX:n,pageY:i,screenX:r,screenY:o,clientX:s||r,clientY:a||o})}function n(){var t=[];return Array.prototype.push.apply(t,arguments),t.item=function(t){return this[t]},t}function i(e,i,r,o,s,a,u,l){return n(t.apply(null,arguments))}var r=this,o=function(t,e,n){n=n||t.parentNode;for(var i=n.querySelectorAll(e),r=0;r<i.length;r++)if(i[r]===t)return!0;return!1},s=function(t){return"string"==typeof t||t.constructor===String?document.getElementById(t):t},a=function(t){return t.srcElement||t.target},u=function(t,e,n,i){if(i){if("undefined"!==typeof t.path&&t.path.indexOf)return{path:t.path,end:t.path.indexOf(n)};var r={path:[],end:-1},o=function(t){r.path.push(t),t===n?r.end=r.path.length-1:null!=t.parentNode&&o(t.parentNode)};return o(e),r}return{path:[e],end:1}},l=function(t,e){for(var n=0,i=t.length;n<i;n++)if(t[n]==e)break;n<t.length&&t.splice(n,1)},c=1,h=function(t,e,n){var i=c++;return t.__ta=t.__ta||{},t.__ta[e]=t.__ta[e]||{},t.__ta[e][i]=n,n.__tauid=i,i},d=function(t,e,n){if(t.__ta&&t.__ta[e]&&delete t.__ta[e][n.__tauid],n.__taExtra){for(var i=0;i<n.__taExtra.length;i++)M(t,n.__taExtra[i][0],n.__taExtra[i][1]);n.__taExtra.length=0}n.__taUnstore&&n.__taUnstore()},f=function(t,e,n,i){if(null==t)return n;var r=t.split(","),s=function(i){s.__tauid=n.__tauid;var l=a(i),c=l,h=u(i,l,e,null!=t);if(-1!=h.end)for(var d=0;d<h.end;d++){c=h.path[d];for(var f=0;f<r.length;f++)o(c,r[f],e)&&n.apply(c,arguments)}};return p(n,i,s),s},p=function(t,e,n){t.__taExtra=t.__taExtra||[],t.__taExtra.push([e,n])},g=function(t,e,n,i){if(P&&C[e]){var r=f(i,t,n,C[e]);T(t,C[e],r,n)}"focus"===e&&null==t.getAttribute("tabindex")&&t.setAttribute("tabindex","1"),T(t,e,f(i,t,n,e),n)},v=function(t,e,n,i){if(null==t.__taSmartClicks){var r=function(e){t.__tad=w(e)},o=function(e){t.__tau=w(e)},s=function(e){if(t.__tad&&t.__tau&&t.__tad[0]===t.__tau[0]&&t.__tad[1]===t.__tau[1])for(var n=0;n<t.__taSmartClicks.length;n++)t.__taSmartClicks[n].apply(a(e),[e])};g(t,"mousedown",r,i),g(t,"mouseup",o,i),g(t,"click",s,i),t.__taSmartClicks=[]}t.__taSmartClicks.push(n),n.__taUnstore=function(){l(t.__taSmartClicks,n)}},m={tap:{touches:1,taps:1},dbltap:{touches:1,taps:2},contextmenu:{touches:2,taps:1}},b=function(t,e){return function(n,i,r,s){if("contextmenu"==i&&_)g(n,i,r,s);else{if(null==n.__taTapHandler){var c=n.__taTapHandler={tap:[],dbltap:[],contextmenu:[],down:!1,taps:0,downSelectors:[]},h=function(i){for(var r=a(i),l=u(i,r,n,null!=s),h=!1,d=0;d<l.end;d++){if(h)return;r=l.path[d];for(var g=0;g<c.downSelectors.length;g++)if(null==c.downSelectors[g]||o(r,c.downSelectors[g],n)){c.down=!0,setTimeout(f,t),setTimeout(p,e),h=!0;break}}},d=function(t){if(c.down){var e,i,r=a(t);c.taps++;var s=k(t);for(var l in m)if(m.hasOwnProperty(l)){var h=m[l];if(h.touches===s&&(1===h.taps||h.taps===c.taps))for(var d=0;d<c[l].length;d++){i=u(t,r,n,null!=c[l][d][1]);for(var f=0;f<i.end;f++)if(e=i.path[f],null==c[l][d][1]||o(e,c[l][d][1],n)){c[l][d][0].apply(e,[t]);break}}}}},f=function(){c.down=!1},p=function(){c.taps=0};g(n,"mousedown",h),g(n,"mouseup",d)}n.__taTapHandler.downSelectors.push(s),n.__taTapHandler[i].push([r,s]),r.__taUnstore=function(){l(n.__taTapHandler[i],r)}}}},y=function(t,e,n,i){for(var r in n.__tamee[t])n.__tamee[t].hasOwnProperty(r)&&n.__tamee[t][r].apply(i,[e])},x=function(){var t=[];return function(e,n,i,r){if(!e.__tamee){e.__tamee={over:!1,mouseenter:[],mouseexit:[]};var s=function(n){var i=a(n);(null==r&&i==e&&!e.__tamee.over||o(i,r,e)&&(null==i.__tamee||!i.__tamee.over))&&(y("mouseenter",n,e,i),i.__tamee=i.__tamee||{},i.__tamee.over=!0,t.push(i))},u=function(n){for(var i=a(n),r=0;r<t.length;r++)i!=t[r]||o(n.relatedTarget||n.toElement,"*",i)||(i.__tamee.over=!1,t.splice(r,1),y("mouseexit",n,e,i))};T(e,"mouseover",f(r,e,s,"mouseover"),s),T(e,"mouseout",f(r,e,u,"mouseout"),u)}i.__taUnstore=function(){delete e.__tamee[n][i.__tauid]},h(e,n,i),e.__tamee[n][i.__tauid]=i}},P="ontouchstart"in document.documentElement||navigator.maxTouchPoints,_="onmousedown"in document.documentElement,C={mousedown:"touchstart",mouseup:"touchend",mousemove:"touchmove"},E=function(){var t=-1;if("Microsoft Internet Explorer"==navigator.appName){var e=navigator.userAgent,n=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");null!=n.exec(e)&&(t=parseFloat(RegExp.$1))}return t}(),j=E>-1&&E<9,S=function(t,e){if(null==t)return[0,0];var n=I(t),i=O(n,0);return[i[e+"X"],i[e+"Y"]]},w=function(t){return null==t?[0,0]:j?[t.clientX+document.documentElement.scrollLeft,t.clientY+document.documentElement.scrollTop]:S(t,"page")},D=function(t){return S(t,"screen")},A=function(t){return S(t,"client")},O=function(t,e){return t.item?t.item(e):t[e]},I=function(t){return t.touches&&t.touches.length>0?t.touches:t.changedTouches&&t.changedTouches.length>0?t.changedTouches:t.targetTouches&&t.targetTouches.length>0?t.targetTouches:[t]},k=function(t){return I(t).length},T=function(t,e,n,i){if(h(t,e,n),i.__tauid=n.__tauid,t.addEventListener)t.addEventListener(e,n,!1);else if(t.attachEvent){var r=e+n.__tauid;t["e"+r]=n,t[r]=function(){t["e"+r]&&t["e"+r](window.event)},t.attachEvent("on"+e,t[r])}},M=function(t,e,n){null!=n&&L(t,(function(){var i=s(this);if(d(i,e,n),null!=n.__tauid)if(i.removeEventListener)i.removeEventListener(e,n,!1),P&&C[e]&&i.removeEventListener(C[e],n,!1);else if(this.detachEvent){var r=e+n.__tauid;i[r]&&i.detachEvent("on"+e,i[r]),i[r]=null,i["e"+r]=null}n.__taTouchProxy&&M(t,n.__taTouchProxy[1],n.__taTouchProxy[0])}))},L=function(t,e){if(null!=t){t="undefined"!==typeof Window&&"unknown"!==typeof t.top&&t==t.top?[t]:"string"!==typeof t&&null==t.tagName&&null!=t.length?t:"string"===typeof t?document.querySelectorAll(t):[t];for(var n=0;n<t.length;n++)e.apply(t[n])}},F=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0,n="x"==t?e:3&e|8;return n.toString(16)}))};r.Mottle=function(t){t=t||{};var e=t.clickThreshold||250,n=t.dblClickThreshold||450,r=new x,o=new b(e,n),a=t.smartClicks,u=function(t,e,n,i){null!=n&&L(t,(function(){var t=s(this);a&&"click"===e?v(t,e,n,i):"tap"===e||"dbltap"===e||"contextmenu"===e?o(t,e,n,i):"mouseenter"===e||"mouseexit"==e?r(t,e,n,i):g(t,e,n,i)}))};this.remove=function(t){return L(t,(function(){var t=s(this);if(t.__ta)for(var e in t.__ta)if(t.__ta.hasOwnProperty(e))for(var n in t.__ta[e])t.__ta[e].hasOwnProperty(n)&&M(t,e,t.__ta[e][n]);t.parentNode&&t.parentNode.removeChild(t)})),this},this.on=function(t,e,n,i){var r=arguments[0],o=4==arguments.length?arguments[2]:null,s=arguments[1],a=arguments[arguments.length-1];return u(r,s,a,o),this},this.off=function(t,e,n){return M(t,e,n),this},this.trigger=function(t,e,n,r){var o=_&&("undefined"===typeof MouseEvent||null==n||n.constructor===MouseEvent),a=P&&!_&&C[e]?C[e]:e,u=!(P&&!_&&C[e]),l=w(n),c=D(n),h=A(n);return L(t,(function(){var t,d=s(this);n=n||{screenX:c[0],screenY:c[1],clientX:h[0],clientY:h[1]};var f=function(t){r&&(t.payload=r)},p={TouchEvent:function(t){var e=i(window,d,0,l[0],l[1],c[0],c[1],h[0],h[1]),n=t.initTouchEvent||t.initEvent;n(a,!0,!0,window,null,c[0],c[1],h[0],h[1],!1,!1,!1,!1,e,e,e,1,0)},MouseEvents:function(t){t.initMouseEvent(a,!0,!0,window,0,c[0],c[1],h[0],h[1],!1,!1,!1,!1,1,d)}};if(document.createEvent){var g=!u&&!o&&P&&C[e],v=g?"TouchEvent":"MouseEvents";t=document.createEvent(v),p[v](t),f(t),d.dispatchEvent(t)}else document.createEventObject&&(t=document.createEventObject(),t.eventType=t.eventName=a,t.screenX=c[0],t.screenY=c[1],t.clientX=h[0],t.clientY=h[1],f(t),d.fireEvent("on"+a,t))})),this}},r.Mottle.consume=function(t,e){t.stopPropagation?t.stopPropagation():t.returnValue=!1,!e&&t.preventDefault&&t.preventDefault()},r.Mottle.pageLocation=w,r.Mottle.setForceTouchEvents=function(t){P=t},r.Mottle.setForceMouseEvents=function(t){_=t},r.Mottle.version="0.8.0",e.Mottle=r.Mottle}.call("undefined"===typeof window?this:window),function(){"use strict";var t=this,n=function(t,e,n){return-1===t.indexOf(e)&&(n?t.unshift(e):t.push(e),!0)},i=function(t,e){var n=t.indexOf(e);-1!==n&&t.splice(n,1)},r=function(t,e){for(var n=[],i=0;i<t.length;i++)-1===e.indexOf(t[i])&&n.push(t[i]);return n},o=function(t){return null!=t&&("string"===typeof t||t.constructor===String)},s=function(t){var e=t.getBoundingClientRect(),n=document.body,i=document.documentElement,r=window.pageYOffset||i.scrollTop||n.scrollTop,o=window.pageXOffset||i.scrollLeft||n.scrollLeft,s=i.clientTop||n.clientTop||0,a=i.clientLeft||n.clientLeft||0,u=e.top+r-s,l=e.left+o-a;return{top:Math.round(u),left:Math.round(l)}},a=function(t,e,n){n=n||t.parentNode;for(var i=n.querySelectorAll(e),r=0;r<i.length;r++)if(i[r]===t)return!0;return!1},u=function(t,e,n){if(a(e,n,t))return e;var i=e.parentNode;while(null!=i&&i!==t){if(a(i,n,t))return i;i=i.parentNode}},l=function(t,e,n){for(var i=null,r=e.getAttribute("katavorio-draggable"),o=null!=r?"[katavorio-draggable='"+r+"'] ":"",s=0;s<t.length;s++)if(i=u(e,n,o+t[s].selector),null!=i){if(t[s].filter){var l=a(n,t[s].filter,i),c=!0===t[s].filterExclude;if(c&&!l||l)return null}return[t[s],i]}return null},c=function(){var t=-1;if("Microsoft Internet Explorer"===navigator.appName){var e=navigator.userAgent,n=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");null!=n.exec(e)&&(t=parseFloat(RegExp.$1))}return t}(),h=10,d=10,f=c>-1&&c<9,p=9===c,g=function(t){if(f)return[t.clientX+document.documentElement.scrollLeft,t.clientY+document.documentElement.scrollTop];var e=m(t),n=v(e,0);return p?[n.pageX||n.clientX,n.pageY||n.clientY]:[n.pageX,n.pageY]},v=function(t,e){return t.item?t.item(e):t[e]},m=function(t){return t.touches&&t.touches.length>0?t.touches:t.changedTouches&&t.changedTouches.length>0?t.changedTouches:t.targetTouches&&t.targetTouches.length>0?t.targetTouches:[t]},b={delegatedDraggable:"katavorio-delegated-draggable",draggable:"katavorio-draggable",droppable:"katavorio-droppable",drag:"katavorio-drag",selected:"katavorio-drag-selected",active:"katavorio-drag-active",hover:"katavorio-drag-hover",noSelect:"katavorio-drag-no-select",ghostProxy:"katavorio-ghost-proxy",clonedDrag:"katavorio-clone-drag"},y="katavorio-drag-scope",x=["stop","start","drag","drop","over","out","beforeStart"],P=function(){},_=function(){return!0},C=function(t,e,n){for(var i=0;i<t.length;i++)t[i]!=n&&e(t[i])},E=function(t,e,n,i){C(t,(function(t){t.setActive(e),e&&t.updatePosition(),n&&t.setHover(i,e)}))},j=function(t,e){if(null!=t){t=o(t)||null!=t.tagName||null==t.length?[t]:t;for(var n=0;n<t.length;n++)e.apply(t[n],[t[n]])}},S=function(t){t.stopPropagation?(t.stopPropagation(),t.preventDefault()):t.returnValue=!1},w="input,textarea,select,button,option",D=function(t,e,n){var i=t.srcElement||t.target;return!a(i,n.getInputFilterSelector(),e)},A=function(t,e,n,i){this.params=e||{},this.el=t,this.params.addClass(this.el,this._class),this.uuid=M();var r=!0;return this.setEnabled=function(t){r=t},this.isEnabled=function(){return r},this.toggleEnabled=function(){r=!r},this.setScope=function(t){this.scopes=t?t.split(/\s+/):[i]},this.addScope=function(t){var e={};for(var n in j(this.scopes,(function(t){e[t]=!0})),j(t?t.split(/\s+/):[],(function(t){e[t]=!0})),this.scopes=[],e)this.scopes.push(n)},this.removeScope=function(t){var e={};for(var n in j(this.scopes,(function(t){e[t]=!0})),j(t?t.split(/\s+/):[],(function(t){delete e[t]})),this.scopes=[],e)this.scopes.push(n)},this.toggleScope=function(t){var e={};for(var n in j(this.scopes,(function(t){e[t]=!0})),j(t?t.split(/\s+/):[],(function(t){e[t]?delete e[t]:e[t]=!0})),this.scopes=[],e)this.scopes.push(n)},this.setScope(e.scope),this.k=e.katavorio,e.katavorio},O=function(){return!0},I=function(){return!1},k=function(t,e,n,i){this._class=n.draggable;var r=A.apply(this,arguments);this.rightButtonCanDrag=this.params.rightButtonCanDrag;var u,c,f,p,v,m,y=[0,0],x=null,P=null,C=[0,0],j=!1,w=[0,0],k=!1!==this.params.consumeStartEvent,T=this.el,F=this.params.clone,R=(this.params.scroll,!1!==e.multipleDrop),N=!1,G=null,H=[],B=null,U=e.ghostProxyParent;if(u=!0===e.ghostProxy?O:e.ghostProxy&&"function"===typeof e.ghostProxy?e.ghostProxy:function(t,e){return!(!B||!B.useGhostProxy)&&B.useGhostProxy(t,e)},c=e.makeGhostProxy?e.makeGhostProxy:function(t){return B&&B.makeGhostProxy?B.makeGhostProxy(t):t.cloneNode(!0)},e.selector){var z=t.getAttribute("katavorio-draggable");null==z&&(z=""+(new Date).getTime(),t.setAttribute("katavorio-draggable",z)),H.push(e)}var W,Y=e.snapThreshold,X=function(t,e,n,i,r){var o=Math.floor(t[0]/e),s=e*o,a=s+e,u=Math.abs(t[0]-s)<=i?s:Math.abs(a-t[0])<=i?a:t[0],l=Math.floor(t[1]/n),c=n*l,h=c+n,d=Math.abs(t[1]-c)<=r?c:Math.abs(h-t[1])<=r?h:t[1];return[u,d]};this.posses=[],this.posseRoles={},this.toGrid=function(t){if(null==this.params.grid)return t;var e=this.params.grid?this.params.grid[0]/2:Y||h/2,n=this.params.grid?this.params.grid[1]/2:Y||d/2;return X(t,this.params.grid[0],this.params.grid[1],e,n)},this.snap=function(t,e){if(null!=T){t=t||(this.params.grid?this.params.grid[0]:h),e=e||(this.params.grid?this.params.grid[1]:d);var n=this.params.getPosition(T),i=this.params.grid?this.params.grid[0]/2:Y,r=this.params.grid?this.params.grid[1]/2:Y,o=X(n,t,e,i,r);return this.params.setPosition(T,o),o}},this.setUseGhostProxy=function(t){u=t?O:I};var V=function(t){return!1===e.allowNegative?[Math.max(0,t[0]),Math.max(0,t[1])]:t},q=function(t){W="function"===typeof t?t:t?function(t,e,n,i){return V([Math.max(0,Math.min(n.w-i[0],t[0])),Math.max(0,Math.min(n.h-i[1],t[1]))])}.bind(this):function(t){return V(t)}}.bind(this);q("function"===typeof this.params.constrain?this.params.constrain:this.params.constrain||this.params.containment),this.setConstrain=function(t){q(t)};var $,J=function(t,e,n,i){return null!=B&&B.constrain&&"function"===typeof B.constrain?B.constrain(t,e,n,i):W(t,e,n,i)};this.setRevert=function(t){$=t},this.params.revert&&($=this.params.revert);var K=function(t){return"function"===typeof t?(t._katavorioId=M(),t._katavorioId):t},Z={},Q=function(t){for(var e in Z){var n=Z[e],i=n[0](t);if(n[1]&&(i=!i),!i)return!1}return!0},tt=this.setFilter=function(e,n){if(e){var i=K(e);Z[i]=[function(n){var i,r=n.srcElement||n.target;return o(e)?i=a(r,e,t):"function"===typeof e&&(i=e(n,t)),i},!1!==n]}};this.addFilter=tt,this.removeFilter=function(t){var e="function"===typeof t?t._katavorioId:t;delete Z[e]};this.clearAllFilters=function(){Z={}},this.canDrag=this.params.canDrag||_;var et,nt=[],it=[];this.addSelector=function(t){t.selector&&H.push(t)},this.downListener=function(t){if(!t.defaultPrevented){var e=this.rightButtonCanDrag||3!==t.which&&2!==t.button;if(e&&this.isEnabled()&&this.canDrag()){var i=Q(t)&&D(t,this.el,this.k);if(i){if(B=null,G=null,H.length>0){var o=l(H,this.el,t.target||t.srcElement);if(null!=o&&(B=o[0],G=o[1]),null==G)return}else G=this.el;if(F)if(T=G.cloneNode(!0),this.params.addClass(T,b.clonedDrag),T.setAttribute("id",null),T.style.position="absolute",null!=this.params.parent){var a=this.params.getPosition(this.el);T.style.left=a[0]+"px",T.style.top=a[1]+"px",this.params.parent.appendChild(T)}else{var u=s(G);T.style.left=u.left+"px",T.style.top=u.top+"px",document.body.appendChild(T)}else T=G;k&&S(t),y=g(t),T&&T.parentNode&&(w=[T.parentNode.scrollLeft,T.parentNode.scrollTop]),this.params.bind(document,"mousemove",this.moveListener),this.params.bind(document,"mouseup",this.upListener),r.markSelection(this),r.markPosses(this),this.params.addClass(document.body,n.noSelect),st("beforeStart",{el:this.el,pos:x,e:t,drag:this})}else this.params.consumeFilteredEvents&&S(t)}}}.bind(this),this.moveListener=function(t){if(y){if(!j){var e=st("start",{el:this.el,pos:x,e:t,drag:this});if(!1!==e){if(!y)return;this.mark(!0),j=!0}else this.abort()}if(y){it.length=0;var n=g(t),i=n[0]-y[0],o=n[1]-y[1],s=this.params.ignoreZoom?1:r.getZoom();T&&T.parentNode&&(i+=T.parentNode.scrollLeft-w[0],o+=T.parentNode.scrollTop-w[1]),i/=s,o/=s,this.moveBy(i,o,t),r.updateSelection(i,o,this),r.updatePosses(i,o,this)}}}.bind(this),this.upListener=function(t){y&&(y=null,this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.params.removeClass(document.body,n.noSelect),this.unmark(t),r.unmarkSelection(this,t),r.unmarkPosses(this,t),this.stop(t),r.notifyPosseDragStop(this,t),j=!1,it.length=0,F?(T&&T.parentNode&&T.parentNode.removeChild(T),T=null):$&&!0===$(T,this.params.getPosition(T))&&(this.params.setPosition(T,x),st("revert",T)))}.bind(this),this.getFilters=function(){return Z},this.abort=function(){null!=y&&this.upListener()},this.getDragElement=function(t){return t?G||this.el:T||this.el};var rt={start:[],drag:[],stop:[],over:[],out:[],beforeStart:[],revert:[]};e.events.start&&rt.start.push(e.events.start),e.events.beforeStart&&rt.beforeStart.push(e.events.beforeStart),e.events.stop&&rt.stop.push(e.events.stop),e.events.drag&&rt.drag.push(e.events.drag),e.events.revert&&rt.revert.push(e.events.revert),this.on=function(t,e){rt[t]&&rt[t].push(e)},this.off=function(t,e){if(rt[t]){for(var n=[],i=0;i<rt[t].length;i++)rt[t][i]!==e&&n.push(rt[t][i]);rt[t]=n}};var ot,st=function(t,e){var n=null;if(B&&B[t])n=B[t](e);else if(rt[t])for(var i=0;i<rt[t].length;i++)try{var r=rt[t][i](e);null!=r&&(n=r)}catch(o){}return n};this.notifyStart=function(t){st("start",{el:this.el,pos:this.params.getPosition(T),e:t,drag:this})},this.stop=function(t,e){if(e||j){var n=[],i=r.getSelection(),o=this.params.getPosition(T);if(i.length>0)for(var s=0;s<i.length;s++){var a=this.params.getPosition(i[s].el);n.push([i[s].el,{left:a[0],top:a[1]},i[s]])}else n.push([T,{left:o[0],top:o[1]},this]);st("stop",{el:T,pos:ot||o,finalPos:o,e:t,drag:this,selection:n})}},this.mark=function(t){var e;x=this.params.getPosition(T),P=this.params.getPosition(T,!0),C=[P[0]-x[0],P[1]-x[1]],this.size=this.params.getSize(T),nt=r.getMatchingDroppables(this),E(nt,!0,!1,this),this.params.addClass(T,this.params.dragClass||n.drag),e=this.params.getConstrainingRectangle?this.params.getConstrainingRectangle(T):this.params.getSize(T.parentNode),et={w:e[0],h:e[1]},v=0,m=0,t&&r.notifySelectionDragStart(this)},this.unmark=function(t,i){if(E(nt,!1,!0,this),N&&u(G,T)?(ot=[T.offsetLeft-v,T.offsetTop-m],T.parentNode.removeChild(T),T=G):ot=null,this.params.removeClass(T,this.params.dragClass||n.drag),nt.length=0,N=!1,!i){it.length>0&&ot&&e.setPosition(G,ot),it.sort(L);for(var r=0;r<it.length;r++){var o=it[r].drop(this,t);if(!0===o)break}}},this.moveBy=function(t,n,i){it.length=0;var r=this.toGrid([x[0]+t,x[1]+n]),o=J(r,T,et,this.size);if(u(this.el,T))if(r[0]!==o[0]||r[1]!==o[1]){if(!N){var s=c(G);e.addClass(s,b.ghostProxy),U?(U.appendChild(s),f=e.getPosition(G.parentNode,!0),p=e.getPosition(e.ghostProxyParent,!0),v=f[0]-p[0],m=f[1]-p[1]):G.parentNode.appendChild(s),T=s,N=!0}o=r}else N&&(T.parentNode.removeChild(T),T=G,N=!1,f=null,p=null,v=0,m=0);var a={x:o[0],y:o[1],w:this.size[0],h:this.size[1]},l={x:a.x+C[0],y:a.y+C[1],w:a.w,h:a.h},h=null;this.params.setPosition(T,[o[0]+v,o[1]+m]);for(var d=0;d<nt.length;d++){var g={x:nt[d].pagePosition[0],y:nt[d].pagePosition[1],w:nt[d].size[0],h:nt[d].size[1]};this.params.intersects(l,g)&&(R||null==h||h===nt[d].el)&&nt[d].canDrop(this)?(h||(h=nt[d].el),it.push(nt[d]),nt[d].setHover(this,!0,i)):nt[d].isHover()&&nt[d].setHover(this,!1,i)}st("drag",{el:this.el,pos:o,e:i,drag:this})},this.destroy=function(){this.params.unbind(this.el,"mousedown",this.downListener),this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.downListener=null,this.upListener=null,this.moveListener=null},this.params.bind(this.el,"mousedown",this.downListener),this.params.handle?tt(this.params.handle,!1):tt(this.params.filter,this.params.filterExclude)},T=function(t,e,n,i){this._class=n.droppable,this.params=e||{},this.rank=e.rank||0,this._activeClass=this.params.activeClass||n.active,this._hoverClass=this.params.hoverClass||n.hover,A.apply(this,arguments);var r=!1;this.allowLoopback=!1!==this.params.allowLoopback,this.setActive=function(t){this.params[t?"addClass":"removeClass"](this.el,this._activeClass)},this.updatePosition=function(){this.position=this.params.getPosition(this.el),this.pagePosition=this.params.getPosition(this.el,!0),this.size=this.params.getSize(this.el)},this.canDrop=this.params.canDrop||function(t){return!0},this.isHover=function(){return r},this.setHover=function(t,e,n){(e||null==this.el._katavorioDragHover||this.el._katavorioDragHover===t.el._katavorio)&&(this.params[e?"addClass":"removeClass"](this.el,this._hoverClass),this.el._katavorioDragHover=e?t.el._katavorio:null,r!==e&&this.params.events[e?"over":"out"]({el:this.el,e:n,drag:t,drop:this}),r=e)},this.drop=function(t,e){return this.params.events["drop"]({drag:t,e:e,drop:this})},this.destroy=function(){this._class=null,this._activeClass=null,this._hoverClass=null,r=null}},M=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0,n="x"===t?e:3&e|8;return n.toString(16)}))},L=function(t,e){return t.rank<e.rank?1:t.rank>e.rank?-1:0},F=function(t){return null==t?null:(t="string"===typeof t||t.constructor===String?document.getElementById(t):t,null==t?null:(t._katavorio=t._katavorio||M(),t))};t.Katavorio=function(t){var e=[],s={};this._dragsByScope={},this._dropsByScope={};var a=1,u=function(t,e){j(t,(function(t){for(var n=0;n<t.scopes.length;n++)e[t.scopes[n]]=e[t.scopes[n]]||[],e[t.scopes[n]].push(t)}))},l=function(e,n){var i=0;return j(e,(function(e){for(var r=0;r<e.scopes.length;r++)if(n[e.scopes[r]]){var o=t.indexOf(n[e.scopes[r]],e);-1!==o&&(n[e.scopes[r]].splice(o,1),i++)}})),i>0},c=(this.getMatchingDroppables=function(t){for(var e=[],n={},i=0;i<t.scopes.length;i++){var r=this._dropsByScope[t.scopes[i]];if(r)for(var o=0;o<r.length;o++)!r[o].canDrop(t)||n[r[o].uuid]||!r[o].allowLoopback&&r[o].el===t.el||(n[r[o].uuid]=!0,e.push(r[o]))}return e.sort(L),e},function(e){e=e||{};var n,i={events:{}};for(n in t)i[n]=t[n];for(n in e)i[n]=e[n];for(n=0;n<x.length;n++)i.events[x[n]]=e[x[n]]||P;return i.katavorio=this,i}.bind(this)),h=function(t,e){for(var n=0;n<x.length;n++)e[x[n]]&&t.on(x[n],e[x[n]])}.bind(this),d={},f=t.css||{},p=t.scope||y;for(var g in b)d[g]=b[g];for(var g in f)d[g]=f[g];var v=t.inputFilterSelector||w;this.getInputFilterSelector=function(){return v},this.setInputFilterSelector=function(t){return v=t,this},this.draggable=function(e,n){var i=[];return j(e,function(e){if(e=F(e),null!=e)if(null==e._katavorioDrag){var r=c(n);e._katavorioDrag=new k(e,r,d,p),u(e._katavorioDrag,this._dragsByScope),i.push(e._katavorioDrag),t.addClass(e,r.selector?d.delegatedDraggable:d.draggable)}else h(e._katavorioDrag,n)}.bind(this)),i},this.droppable=function(e,n){var i=[];return j(e,function(e){if(e=F(e),null!=e){var r=new T(e,c(n),d,p);e._katavorioDrop=e._katavorioDrop||[],e._katavorioDrop.push(r),u(r,this._dropsByScope),i.push(r),t.addClass(e,d.droppable)}}.bind(this)),i},this.select=function(n){return j(n,(function(){var n=F(this);n&&n._katavorioDrag&&(s[n._katavorio]||(e.push(n._katavorioDrag),s[n._katavorio]=[n,e.length-1],t.addClass(n,d.selected)))})),this},this.deselect=function(n){return j(n,(function(){var n=F(this);if(n&&n._katavorio){var i=s[n._katavorio];if(i){for(var r=[],o=0;o<e.length;o++)e[o].el!==n&&r.push(e[o]);e=r,delete s[n._katavorio],t.removeClass(n,d.selected)}}})),this},this.deselectAll=function(){for(var n in s){var i=s[n];t.removeClass(i[0],d.selected)}e.length=0,s={}},this.markSelection=function(t){C(e,(function(t){t.mark()}),t)},this.markPosses=function(t){t.posses&&j(t.posses,(function(e){t.posseRoles[e]&&D[e]&&C(D[e].members,(function(t){t.mark()}),t)}))},this.unmarkSelection=function(t,n){C(e,(function(t){t.unmark(n)}),t)},this.unmarkPosses=function(t,e){t.posses&&j(t.posses,(function(n){t.posseRoles[n]&&D[n]&&C(D[n].members,(function(t){t.unmark(e,!0)}),t)}))},this.getSelection=function(){return e.slice(0)},this.updateSelection=function(t,n,i){C(e,(function(e){e.moveBy(t,n)}),i)};var m=function(t,e){e.posses&&j(e.posses,(function(n){e.posseRoles[n]&&D[n]&&C(D[n].members,(function(e){t(e)}),e)}))};this.updatePosses=function(t,e,n){m((function(n){n.moveBy(t,e)}),n)},this.notifyPosseDragStop=function(t,e){m((function(t){t.stop(e,!0)}),t)},this.notifySelectionDragStop=function(t,n){C(e,(function(t){t.stop(n,!0)}),t)},this.notifySelectionDragStart=function(t,n){C(e,(function(t){t.notifyStart(n)}),t)},this.setZoom=function(t){a=t},this.getZoom=function(){return a};var _=function(t,e,n,i){j(t,(function(t){l(t,n),t[i](e),u(t,n)}))};j(["set","add","remove","toggle"],function(t){this[t+"Scope"]=function(e,n){_(e._katavorioDrag,n,this._dragsByScope,t+"Scope"),_(e._katavorioDrop,n,this._dropsByScope,t+"Scope")}.bind(this),this[t+"DragScope"]=function(e,n){_(e.constructor===k?e:e._katavorioDrag,n,this._dragsByScope,t+"Scope")}.bind(this),this[t+"DropScope"]=function(e,n){_(e.constructor===T?e:e._katavorioDrop,n,this._dropsByScope,t+"Scope")}.bind(this)}.bind(this)),this.snapToGrid=function(t,e){for(var n in this._dragsByScope)C(this._dragsByScope[n],(function(n){n.snap(t,e)}))},this.getDragsForScope=function(t){return this._dragsByScope[t]},this.getDropsForScope=function(t){return this._dropsByScope[t]};var E=function(t,n,i){if(t=F(t),t[n]){var r=e.indexOf(t[n]);r>=0&&e.splice(r,1),l(t[n],i)&&j(t[n],(function(t){t.destroy()})),delete t[n]}},S=function(t,e,n,i){t=F(t),t[e]&&t[e].off(n,i)};this.elementRemoved=function(t){t["_katavorioDrag"]&&this.destroyDraggable(t),t["_katavorioDrop"]&&this.destroyDroppable(t)},this.destroyDraggable=function(t,e,n){1===arguments.length?E(t,"_katavorioDrag",this._dragsByScope):S(t,"_katavorioDrag",e,n)},this.destroyDroppable=function(t,e,n){1===arguments.length?E(t,"_katavorioDrop",this._dropsByScope):S(t,"_katavorioDrop",e,n)},this.reset=function(){this._dragsByScope={},this._dropsByScope={},e=[],s={},D={}};var D={},A=function(t,e,i){var r=o(e)?e:e.id,s=!!o(e)||!1!==e.active,a=D[r]||function(){var t={name:r,members:[]};return D[r]=t,t}();return j(t,(function(t){if(t._katavorioDrag){if(i&&null!=t._katavorioDrag.posseRoles[a.name])return;n(a.members,t._katavorioDrag),n(t._katavorioDrag.posses,a.name),t._katavorioDrag.posseRoles[a.name]=s}})),a};this.addToPosse=function(t,e){for(var n=[],i=1;i<arguments.length;i++)n.push(A(t,arguments[i]));return 1===n.length?n[0]:n},this.setPosse=function(t,e){for(var n=[],i=1;i<arguments.length;i++)n.push(A(t,arguments[i],!0).name);return j(t,function(t){if(t._katavorioDrag){var e=r(t._katavorioDrag.posses,n),i=[];Array.prototype.push.apply(i,t._katavorioDrag.posses);for(var o=0;o<e.length;o++)this.removeFromPosse(t,e[o])}}.bind(this)),1===n.length?n[0]:n},this.removeFromPosse=function(t,e){if(arguments.length<2)throw new TypeError("No posse id provided for remove operation");for(var n=1;n<arguments.length;n++)e=arguments[n],j(t,(function(t){if(t._katavorioDrag&&t._katavorioDrag.posses){var n=t._katavorioDrag;j(e,(function(t){i(D[t].members,n),i(n.posses,t),delete n.posseRoles[t]}))}}))},this.removeFromAllPosses=function(t){j(t,(function(t){if(t._katavorioDrag&&t._katavorioDrag.posses){var e=t._katavorioDrag;j(e.posses,(function(t){i(D[t].members,e)})),e.posses.length=0,e.posseRoles={}}}))},this.setPosseState=function(t,e,n){var i=D[e];i&&j(t,(function(t){t._katavorioDrag&&t._katavorioDrag.posses&&(t._katavorioDrag.posseRoles[i.name]=n)}))}},t.Katavorio.version="1.0.0",e.Katavorio=t.Katavorio}.call("undefined"!==typeof window?window:this),function(){var t=this;t.jsPlumbUtil=t.jsPlumbUtil||{};var n=t.jsPlumbUtil;function i(t){return"[object Array]"===Object.prototype.toString.call(t)}function r(t){return"[object Number]"===Object.prototype.toString.call(t)}function o(t){return"string"===typeof t}function s(t){return"boolean"===typeof t}function a(t){return null==t}function u(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function l(t){return"[object Date]"===Object.prototype.toString.call(t)}function c(t){return"[object Function]"===Object.prototype.toString.call(t)}function h(t){return c(t)&&null!=t.name&&t.name.length>0}function d(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}function f(t){if(o(t))return""+t;if(s(t))return!!t;if(l(t))return new Date(t.getTime());if(c(t))return t;if(i(t)){for(var e=[],n=0;n<t.length;n++)e.push(f(t[n]));return e}if(u(t)){var r={};for(var a in t)r[a]=f(t[a]);return r}return t}function p(t,e,n,r){var a,l,c={},h={};for(n=n||[],r=r||[],l=0;l<n.length;l++)c[n[l]]=!0;for(l=0;l<r.length;l++)h[r[l]]=!0;var d=f(t);for(l in e)if(null==d[l]||h[l])d[l]=e[l];else if(o(e[l])||s(e[l]))c[l]?(a=[],a.push.apply(a,i(d[l])?d[l]:[d[l]]),a.push.apply(a,s(e[l])?e[l]:[e[l]]),d[l]=a):d[l]=e[l];else if(i(e[l]))a=[],i(d[l])&&a.push.apply(a,d[l]),a.push.apply(a,e[l]),d[l]=a;else if(u(e[l]))for(var p in u(d[l])||(d[l]={}),e[l])d[l][p]=e[l][p];return d}function g(t,e,n){if(null!=t){var i=t,r=i;return e.replace(/([^\.])+/g,(function(t,e,i,o){var s=t.match(/([^\[0-9]+){1}(\[)([0-9+])/),a=i+t.length>=o.length,u=function(){return r[s[1]]||function(){return r[s[1]]=[],r[s[1]]}()};if(a)s?u()[s[3]]=n:r[t]=n;else if(s){var l=u();r=l[s[3]]||function(){return l[s[3]]={},l[s[3]]}()}else r=r[t]||function(){return r[t]={},r[t]}();return""})),t}}function v(t,e,n){for(var i=0;i<n.length;i++){var r=n[i][0][n[i][1]].apply(n[i][0],n[i][2]);if(r===e)return r}return t}function m(t,e,n,r){var s=function(t){var n=t.match(/(\${.*?})/g);if(null!=n)for(var i=0;i<n.length;i++){var r=e[n[i].substring(2,n[i].length-1)]||"";null!=r&&(t=t.replace(n[i],r))}return t},a=function(t){if(null!=t){if(o(t))return s(t);if(!c(t)||r||null!=n&&0!==(t.name||"").indexOf(n)){if(i(t)){for(var l=[],h=0;h<t.length;h++)l.push(a(t[h]));return l}if(u(t)){var d={};for(var f in t)d[f]=a(t[f]);return d}return t}return t(e)}};return a(t)}function b(t,e){if(t)for(var n=0;n<t.length;n++)if(e(t[n]))return n;return-1}function y(t,e){var n=b(t,e);return n>-1&&t.splice(n,1),-1!==n}function x(t,e){var n=t.indexOf(e);return n>-1&&t.splice(n,1),-1!==n}function P(t,e,n){-1===b(t,n)&&t.push(e)}function _(t,e,n,i){var r=t[e];return null==r&&(r=[],t[e]=r),r[i?"unshift":"push"](n),r}function C(t,e,n){return-1===t.indexOf(e)&&(n?t.unshift(e):t.push(e),!0)}function E(t,e,n){var r;e=i(e)?e:[e];var o=function(e){var n=e.__proto__;while(null!=n)if(null!=n.prototype){for(var i in n.prototype)n.prototype.hasOwnProperty(i)&&!t.prototype.hasOwnProperty(i)&&(t.prototype[i]=n.prototype[i]);n=n.prototype.__proto__}else n=null};for(r=0;r<e.length;r++){for(var s in e[r].prototype)e[r].prototype.hasOwnProperty(s)&&!t.prototype.hasOwnProperty(s)&&(t.prototype[s]=e[r].prototype[s]);o(e[r])}var a=function(t,n){return function(){for(r=0;r<e.length;r++)e[r].prototype[t]&&e[r].prototype[t].apply(this,arguments);return n.apply(this,arguments)}},u=function(e){for(var n in e)t.prototype[n]=a(n,e[n])};if(arguments.length>2)for(r=2;r<arguments.length;r++)u(arguments[r]);return t}e.jsPlumbUtil=n,n.isArray=i,n.isNumber=r,n.isString=o,n.isBoolean=s,n.isNull=a,n.isObject=u,n.isDate=l,n.isFunction=c,n.isNamedFunction=h,n.isEmpty=d,n.clone=f,n.merge=p,n.replace=g,n.functionChain=v,n.populate=m,n.findWithFunction=b,n.removeWithFunction=y,n.remove=x,n.addWithFunction=P,n.addToList=_,n.suggest=C,n.extend=E;for(var j=[],S=0;S<256;S++)j[S]=(S<16?"0":"")+S.toString(16);function w(){var t=4294967295*Math.random()|0,e=4294967295*Math.random()|0,n=4294967295*Math.random()|0,i=4294967295*Math.random()|0;return j[255&t]+j[t>>8&255]+j[t>>16&255]+j[t>>24&255]+"-"+j[255&e]+j[e>>8&255]+"-"+j[e>>16&15|64]+j[e>>24&255]+"-"+j[63&n|128]+j[n>>8&255]+"-"+j[n>>16&255]+j[n>>24&255]+j[255&i]+j[i>>8&255]+j[i>>16&255]+j[i>>24&255]}function D(t){if(null==t)return null;var e=t.replace(/^\s\s*/,""),n=/\s/,i=e.length;while(n.test(e.charAt(--i)));return e.slice(0,i+1)}function A(t,e){t=null==t.length||"string"===typeof t?[t]:t;for(var n=0;n<t.length;n++)e(t[n])}function O(t,e){for(var n=[],i=0;i<t.length;i++)n.push(e(t[i]));return n}function I(t,e,n){n=n||"parent";var i=function(t){return t?e[t]:null},r=function(t){return t?i(t[n]):null},o=function(t,e){if(null==t)return e;var n=["anchor","anchors","cssClass","connector","paintStyle","hoverPaintStyle","endpoint","endpoints"];"override"===e.mergeStrategy&&Array.prototype.push.apply(n,["events","overlays"]);var i=p(t,e,[],n);return o(r(t),i)},s=function(t){if(null==t)return{};if("string"===typeof t)return i(t);if(t.length){var e=!1,n=0,r=void 0;while(!e&&n<t.length)r=s(t[n]),r?e=!0:n++;return r}},a=s(t);return a?o(r(a),a):{}}function k(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(n.logEnabled&&"undefined"!==typeof console)try{}catch(i){}}function T(t,e,n){return function(){var i=null;try{null!=e&&(i=e.apply(this,arguments))}catch(r){k("jsPlumb function failed : "+r)}if(null!=t&&(null==n||i!==n))try{i=t.apply(this,arguments)}catch(r){k("wrapped function failed : "+r)}return i}}n.uuid=w,n.fastTrim=D,n.each=A,n.map=O,n.mergeWithParents=I,n.logEnabled=!0,n.log=k,n.wrap=T;var M=function(){function t(){var t=this;this._listeners={},this.eventsSuspended=!1,this.tick=!1,this.eventsToDieOn={ready:!0},this.queue=[],this.bind=function(e,n,i){var r=function(e){_(t._listeners,e,n,i),n.__jsPlumb=n.__jsPlumb||{},n.__jsPlumb[w()]=e};if("string"===typeof e)r(e);else if(null!=e.length)for(var o=0;o<e.length;o++)r(e[o]);return t},this.fire=function(t,e,n){if(this.tick)this.queue.unshift(arguments);else{if(this.tick=!0,!this.eventsSuspended&&this._listeners[t]){var i=this._listeners[t].length,r=0,o=!1,s=null;if(!this.shouldFireEvent||this.shouldFireEvent(t,e,n))while(!o&&r<i&&!1!==s){if(this.eventsToDieOn[t])this._listeners[t][r].apply(this,[e,n]);else try{s=this._listeners[t][r].apply(this,[e,n])}catch(a){k("jsPlumb: fire failed for event "+t+" : "+a)}r++,null!=this._listeners&&null!=this._listeners[t]||(o=!0)}}this.tick=!1,this._drain()}return this},this._drain=function(){var e=t.queue.pop();e&&t.fire.apply(t,e)},this.unbind=function(t,e){if(0===arguments.length)this._listeners={};else if(1===arguments.length){if("string"===typeof t)delete this._listeners[t];else if(t.__jsPlumb){var n=void 0;for(var i in t.__jsPlumb)n=t.__jsPlumb[i],x(this._listeners[n]||[],t)}}else 2===arguments.length&&x(this._listeners[t]||[],e);return this},this.getListener=function(e){return t._listeners[e]},this.setSuspendEvents=function(e){t.eventsSuspended=e},this.isSuspendEvents=function(){return t.eventsSuspended},this.silently=function(e){t.setSuspendEvents(!0);try{e()}catch(n){k("Cannot execute silent function "+n)}t.setSuspendEvents(!1)},this.cleanupListeners=function(){for(var e in t._listeners)t._listeners[e]=null}}return t}();function L(t,e,n){var i=[t[0]-e[0],t[1]-e[1]],r=Math.cos(n/360*Math.PI*2),o=Math.sin(n/360*Math.PI*2);return[i[0]*r-i[1]*o+e[0],i[1]*r+i[0]*o+e[1],r,o]}function F(t,e){var n=L(t,[0,0],e);return[Math.round(n[0]),Math.round(n[1])]}n.EventGenerator=M,n.rotatePoint=L,n.rotateAnchorOrientation=F}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this;t.jsPlumbUtil.matchesSelector=function(t,e,n){n=n||t.parentNode;for(var i=n.querySelectorAll(e),r=0;r<i.length;r++)if(i[r]===t)return!0;return!1},t.jsPlumbUtil.consume=function(t,e){t.stopPropagation?t.stopPropagation():t.returnValue=!1,!e&&t.preventDefault&&t.preventDefault()},t.jsPlumbUtil.sizeElement=function(t,e,n,i,r){t&&(t.style.height=r+"px",t.height=r,t.style.width=i+"px",t.width=i,t.style.left=e+"px",t.style.top=n+"px")}}.call("undefined"!==typeof window?window:this),function(){var t={deriveAnchor:function(t,e,n,i){return{top:["TopRight","TopLeft"],bottom:["BottomRight","BottomLeft"]}[t][e]}},e=this,n=function(t,e){this.count=0,this.instance=t,this.lists={},this.options=e||{},this.instance.addList=function(t,e){return this.listManager.addList(t,e)},this.instance.removeList=function(t){this.listManager.removeList(t)},this.instance.bind("manageElement",function(t){for(var e=this.instance.getSelector(t.el,"[jtk-scrollable-list]"),n=0;n<e.length;n++)this.addList(e[n])}.bind(this)),this.instance.bind("unmanageElement",(function(t){this.removeList(t.el)})),this.instance.bind("connection",function(t,e){null==e&&(this._maybeUpdateParentList(t.source),this._maybeUpdateParentList(t.target))}.bind(this))};e.jsPlumbListManager=n,n.prototype={addList:function(e,n){var r=this.instance.extend({},t);this.instance.extend(r,this.options),n=this.instance.extend(r,n||{});var o=[this.instance.getInstanceIndex(),this.count++].join("_");this.lists[o]=new i(this.instance,e,n,o)},removeList:function(t){var e=this.lists[t._jsPlumbList];e&&(e.destroy(),delete this.lists[t._jsPlumbList])},_maybeUpdateParentList:function(t){var e=t.parentNode,n=this.instance.getContainer();while(null!=e&&e!==n){if(null!=e._jsPlumbList&&null!=this.lists[e._jsPlumbList])return void e._jsPlumbScrollHandler();e=e.parentNode}}};var i=function(t,e,n,i){function r(t,e,i,r){return n.anchor?n.anchor:n.deriveAnchor(t,e,i,r)}function o(t,e,i,r){return n.deriveEndpoint?n.deriveEndpoint(t,e,i,r):n.endpoint?n.endpoint:i.type}function s(e){var n=e.parentNode,i=t.getContainer();while(null!=n&&n!==i){if(t.hasClass(n,"jtk-managed"))return void t.recalculateOffsets(n);n=n.parentNode}}e["_jsPlumbList"]=i;var a=function(n){for(var i=t.getSelector(e,".jtk-managed"),a=t.getId(e),u=0;u<i.length;u++){if(i[u].offsetTop<e.scrollTop)i[u]._jsPlumbProxies||(i[u]._jsPlumbProxies=i[u]._jsPlumbProxies||[],t.select({source:i[u]}).each((function(n){t.proxyConnection(n,0,e,a,(function(){return o("top",0,n.endpoints[0],n)}),(function(){return r("top",0,n.endpoints[0],n)})),i[u]._jsPlumbProxies.push([n,0])})),t.select({target:i[u]}).each((function(n){t.proxyConnection(n,1,e,a,(function(){return o("top",1,n.endpoints[1],n)}),(function(){return r("top",1,n.endpoints[1],n)})),i[u]._jsPlumbProxies.push([n,1])})));else if(i[u].offsetTop+i[u].offsetHeight>e.scrollTop+e.offsetHeight)i[u]._jsPlumbProxies||(i[u]._jsPlumbProxies=i[u]._jsPlumbProxies||[],t.select({source:i[u]}).each((function(n){t.proxyConnection(n,0,e,a,(function(){return o("bottom",0,n.endpoints[0],n)}),(function(){return r("bottom",0,n.endpoints[0],n)})),i[u]._jsPlumbProxies.push([n,0])})),t.select({target:i[u]}).each((function(n){t.proxyConnection(n,1,e,a,(function(){return o("bottom",1,n.endpoints[1],n)}),(function(){return r("bottom",1,n.endpoints[1],n)})),i[u]._jsPlumbProxies.push([n,1])})));else if(i[u]._jsPlumbProxies){for(var l=0;l<i[u]._jsPlumbProxies.length;l++)t.unproxyConnection(i[u]._jsPlumbProxies[l][0],i[u]._jsPlumbProxies[l][1],a);delete i[u]._jsPlumbProxies}t.revalidate(i[u])}s(e)};t.setAttribute(e,"jtk-scrollable-list","true"),e._jsPlumbScrollHandler=a,t.on(e,"scroll",a),a(),this.destroy=function(){t.off(e,"scroll",a),delete e._jsPlumbScrollHandler;for(var n=t.getSelector(e,".jtk-managed"),i=t.getId(e),r=0;r<n.length;r++)if(n[r]._jsPlumbProxies){for(var o=0;o<n[r]._jsPlumbProxies.length;o++)t.unproxyConnection(n[r]._jsPlumbProxies[o][0],n[r]._jsPlumbProxies[o][1],i);delete n[r]._jsPlumbProxies}}}}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,n=t.jsPlumbUtil,i=function(t){if(t._jsPlumb.paintStyle&&t._jsPlumb.hoverPaintStyle){var e={};p.extend(e,t._jsPlumb.paintStyle),p.extend(e,t._jsPlumb.hoverPaintStyle),delete t._jsPlumb.hoverPaintStyle,e.gradient&&t._jsPlumb.paintStyle.fill&&delete e.gradient,t._jsPlumb.hoverPaintStyle=e}},r=["tap","dbltap","click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","contextmenu"],o=function(t,e,n,i){var r=t.getAttachedElements();if(r)for(var o=0,s=r.length;o<s;o++)i&&i===r[o]||r[o].setHover(e,!0,n)},s=function(t){return null==t?null:t.split(" ")},a=function(t,e,n){for(var i in e)t[i]=n},u=function(t,e,i){if(t.getDefaultType){var r=t.getTypeDescriptor(),o={},s=t.getDefaultType(),u=n.merge({},s);a(o,s,"__default");for(var l=0,c=t._jsPlumb.types.length;l<c;l++){var h=t._jsPlumb.types[l];if("__default"!==h){var d=t._jsPlumb.instance.getType(h,r);if(null!=d){var f=["anchor","anchors","connector","paintStyle","hoverPaintStyle","endpoint","endpoints","connectorOverlays","connectorStyle","connectorHoverStyle","endpointStyle","endpointHoverStyle"],p=[];"override"===d.mergeStrategy?Array.prototype.push.apply(f,["events","overlays","cssClass"]):p.push("cssClass"),u=n.merge(u,d,p,f),a(o,d,h)}}}e&&(u=n.populate(u,e,"_")),t.applyType(u,i,o),i||t.repaint()}},l=t.jsPlumbUIComponent=function(t){n.EventGenerator.apply(this,arguments);var e=this,i=arguments,r=e.idPrefix,o=r+(new Date).getTime();this._jsPlumb={instance:t._jsPlumb,parameters:t.parameters||{},paintStyle:null,hoverPaintStyle:null,paintStyleInUse:null,hover:!1,beforeDetach:t.beforeDetach,beforeDrop:t.beforeDrop,overlayPlacements:[],hoverClass:t.hoverClass||t._jsPlumb.Defaults.HoverClass,types:[],typeCache:{}},this.cacheTypeItem=function(t,e,n){this._jsPlumb.typeCache[n]=this._jsPlumb.typeCache[n]||{},this._jsPlumb.typeCache[n][t]=e},this.getCachedTypeItem=function(t,e){return this._jsPlumb.typeCache[e]?this._jsPlumb.typeCache[e][t]:null},this.getId=function(){return o};var s=t.overlays||[],a={};if(this.defaultOverlayKeys){for(var u=0;u<this.defaultOverlayKeys.length;u++)Array.prototype.push.apply(s,this._jsPlumb.instance.Defaults[this.defaultOverlayKeys[u]]||[]);for(u=0;u<s.length;u++){var l=p.convertToFullOverlaySpec(s[u]);a[l[1].id]=l}}var c={overlays:a,parameters:t.parameters||{},scope:t.scope||this._jsPlumb.instance.getDefaultScope()};if(this.getDefaultType=function(){return c},this.appendToDefaultType=function(t){for(var e in t)c[e]=t[e]},t.events)for(var h in t.events)e.bind(h,t.events[h]);this.clone=function(){var t=Object.create(this.constructor.prototype);return this.constructor.apply(t,i),t}.bind(this),this.isDetachAllowed=function(t){var e=!0;if(this._jsPlumb.beforeDetach)try{e=this._jsPlumb.beforeDetach(t)}catch(i){n.log("jsPlumb: beforeDetach callback failed",i)}return e},this.isDropAllowed=function(t,e,i,r,o,s,a){var u=this._jsPlumb.instance.checkCondition("beforeDrop",{sourceId:t,targetId:e,scope:i,connection:r,dropEndpoint:o,source:s,target:a});if(this._jsPlumb.beforeDrop)try{u=this._jsPlumb.beforeDrop({sourceId:t,targetId:e,scope:i,connection:r,dropEndpoint:o,source:s,target:a})}catch(l){n.log("jsPlumb: beforeDrop callback failed",l)}return u};var d=[];this.setListenerComponent=function(t){for(var e=0;e<d.length;e++)d[e][3]=t}},c=function(t,e){var n=t._jsPlumb.types[e],i=t._jsPlumb.instance.getType(n,t.getTypeDescriptor());null!=i&&i.cssClass&&t.canvas&&t._jsPlumb.instance.removeClass(t.canvas,i.cssClass)};n.extend(t.jsPlumbUIComponent,n.EventGenerator,{getParameter:function(t){return this._jsPlumb.parameters[t]},setParameter:function(t,e){this._jsPlumb.parameters[t]=e},getParameters:function(){return this._jsPlumb.parameters},setParameters:function(t){this._jsPlumb.parameters=t},getClass:function(){return p.getClass(this.canvas)},hasClass:function(t){return p.hasClass(this.canvas,t)},addClass:function(t){p.addClass(this.canvas,t)},removeClass:function(t){p.removeClass(this.canvas,t)},updateClasses:function(t,e){p.updateClasses(this.canvas,t,e)},setType:function(t,e,n){this.clearTypes(),this._jsPlumb.types=s(t)||[],u(this,e,n)},getType:function(){return this._jsPlumb.types},reapplyTypes:function(t,e){u(this,t,e)},hasType:function(t){return-1!==this._jsPlumb.types.indexOf(t)},addType:function(t,e,n){var i=s(t),r=!1;if(null!=i){for(var o=0,a=i.length;o<a;o++)this.hasType(i[o])||(this._jsPlumb.types.push(i[o]),r=!0);r&&u(this,e,n)}},removeType:function(t,e,n){var i=s(t),r=!1,o=function(t){var e=this._jsPlumb.types.indexOf(t);return-1!==e&&(c(this,e),this._jsPlumb.types.splice(e,1),!0)}.bind(this);if(null!=i){for(var a=0,l=i.length;a<l;a++)r=o(i[a])||r;r&&u(this,e,n)}},clearTypes:function(t,e){for(var n=this._jsPlumb.types.length,i=0;i<n;i++)c(this,0),this._jsPlumb.types.splice(0,1);u(this,t,e)},toggleType:function(t,e,n){var i=s(t);if(null!=i){for(var r=0,o=i.length;r<o;r++){var a=this._jsPlumb.types.indexOf(i[r]);-1!==a?(c(this,a),this._jsPlumb.types.splice(a,1)):this._jsPlumb.types.push(i[r])}u(this,e,n)}},applyType:function(t,e){if(this.setPaintStyle(t.paintStyle,e),this.setHoverPaintStyle(t.hoverPaintStyle,e),t.parameters)for(var n in t.parameters)this.setParameter(n,t.parameters[n]);this._jsPlumb.paintStyleInUse=this.getPaintStyle()},setPaintStyle:function(t,e){this._jsPlumb.paintStyle=t,this._jsPlumb.paintStyleInUse=this._jsPlumb.paintStyle,i(this),e||this.repaint()},getPaintStyle:function(){return this._jsPlumb.paintStyle},setHoverPaintStyle:function(t,e){this._jsPlumb.hoverPaintStyle=t,i(this),e||this.repaint()},getHoverPaintStyle:function(){return this._jsPlumb.hoverPaintStyle},destroy:function(t){(t||null==this.typeId)&&(this.cleanupListeners(),this.clone=null,this._jsPlumb=null)},isHover:function(){return this._jsPlumb.hover},setHover:function(t,e,n){if(this._jsPlumb&&!this._jsPlumb.instance.currentlyDragging&&!this._jsPlumb.instance.isHoverSuspended()){this._jsPlumb.hover=t;var i=t?"addClass":"removeClass";null!=this.canvas&&(null!=this._jsPlumb.instance.hoverClass&&this._jsPlumb.instance[i](this.canvas,this._jsPlumb.instance.hoverClass),null!=this._jsPlumb.hoverClass&&this._jsPlumb.instance[i](this.canvas,this._jsPlumb.hoverClass)),null!=this._jsPlumb.hoverPaintStyle&&(this._jsPlumb.paintStyleInUse=t?this._jsPlumb.hoverPaintStyle:this._jsPlumb.paintStyle,this._jsPlumb.instance.isSuspendDrawing()||(n=n||jsPlumbUtil.uuid(),this.repaint({timestamp:n,recalc:!1}))),this.getAttachedElements&&!e&&o(this,t,jsPlumbUtil.uuid(),this)}}});var h=0,d=function(){var t=h+1;return h++,t},f=t.jsPlumbInstance=function(e){this.version="2.15.6",this.Defaults={Anchor:"Bottom",Anchors:[null,null],ConnectionsDetachable:!0,ConnectionOverlays:[],Connector:"Bezier",Container:null,DoNotThrowErrors:!1,DragOptions:{},DropOptions:{},Endpoint:"Dot",EndpointOverlays:[],Endpoints:[null,null],EndpointStyle:{fill:"#456"},EndpointStyles:[null,null],EndpointHoverStyle:null,EndpointHoverStyles:[null,null],HoverPaintStyle:null,LabelStyle:{color:"black"},ListStyle:{},LogEnabled:!1,Overlays:[],MaxConnections:1,PaintStyle:{"stroke-width":4,stroke:"#456"},ReattachConnections:!1,RenderMode:"svg",Scope:"jsPlumb_DefaultScope"},e&&p.extend(this.Defaults,e),this.logEnabled=this.Defaults.LogEnabled,this._connectionTypes={},this._endpointTypes={},n.EventGenerator.apply(this);var i=this,o=d(),s=i.bind,a={},u=1,c=function(t){if(null==t)return null;if(3===t.nodeType||8===t.nodeType)return{el:t,text:!0};var e=i.getElement(t);return{el:e,id:n.isString(t)&&null==e?t:W(e)}};for(var h in this.getInstanceIndex=function(){return o},this.setZoom=function(t,e){return u=t,i.fire("zoom",u),e&&i.repaintEverything(),!0},this.getZoom=function(){return u},this.Defaults)a[h]=this.Defaults[h];var f,g=[];this.unbindContainer=function(){if(null!=f&&g.length>0)for(var t=0;t<g.length;t++)i.off(f,g[t][0],g[t][1])},this.setContainer=function(t){this.unbindContainer(),t=this.getElement(t),this.select().each((function(e){e.moveParent(t)})),this.selectEndpoints().each((function(e){e.moveParent(t)}));var e=f;f=t,g.length=0;for(var n={endpointclick:"endpointClick",endpointdblclick:"endpointDblClick"},o=function(t,e,r){var o=e.srcElement||e.target,s=(o&&o.parentNode?o.parentNode._jsPlumb:null)||(o?o._jsPlumb:null)||(o&&o.parentNode&&o.parentNode.parentNode?o.parentNode.parentNode._jsPlumb:null);if(s){s.fire(t,s,e);var a=r&&n[r+t]||t;i.fire(a,s.component||s,e)}},s=function(t,e,n){g.push([t,n]),i.on(f,t,e,n)},a=function(t){s(t,".jtk-connector",(function(e){o(t,e)})),s(t,".jtk-endpoint",(function(e){o(t,e,"endpoint")})),s(t,".jtk-overlay",(function(e){o(t,e)}))},u=0;u<r.length;u++)a(r[u]);for(var l in P){var c=P[l].el;c.parentNode===e&&(e.removeChild(c),f.appendChild(c))}},this.getContainer=function(){return f},this.bind=function(t,e){"ready"===t&&m?e():s.apply(i,[t,e])},i.importDefaults=function(t){for(var e in t)i.Defaults[e]=t[e];return t.Container&&i.setContainer(t.Container),i},i.restoreDefaults=function(){return i.Defaults=p.extend({},a),i};var v=null,m=!1,b=[],y={},x={},P={},_={},C={},E=!1,j=[],S=!1,w=null,D=this.Defaults.Scope,A=1,O=function(){return""+A++},I=function(t,e){f?f.appendChild(t):e?this.getElement(e).appendChild(t):this.appendToRoot(t)}.bind(this),k=function(t,e,n,r){var o={c:[],e:[]};if(!S&&(t=i.getElement(t),null!=t)){var s=W(t),a=t.querySelectorAll(".jtk-managed");null==n&&(n=jsPlumbUtil.uuid());ft({elId:s,offset:e,recalc:!1,timestamp:n});for(var u=0;u<a.length;u++)ft({elId:a[u].getAttribute("id"),recalc:!0,timestamp:n});var l=i.router.redraw(s,e,n,null,r);if(Array.prototype.push.apply(o.c,l.c),Array.prototype.push.apply(o.e,l.e),a)for(var c=0;c<a.length;c++)l=i.router.redraw(a[c].getAttribute("id"),null,n,null,r,!0),Array.prototype.push.apply(o.c,l.c),Array.prototype.push.apply(o.e,l.e)}return o},T=function(t){return x[t]},M=function(t,e){for(var n=t.scope.split(/\s/),i=e.scope.split(/\s/),r=0;r<n.length;r++)for(var o=0;o<i.length;o++)if(i[o]===n[r])return!0;return!1},L=function(t,e){var n=p.extend({},t);for(var i in e)e[i]&&(n[i]=e[i]);return n},F=function(t,e){var r=p.extend({},t);if(e&&p.extend(r,e),r.source&&(r.source.endpoint?r.sourceEndpoint=r.source:r.source=i.getElement(r.source)),r.target&&(r.target.endpoint?r.targetEndpoint=r.target:r.target=i.getElement(r.target)),t.uuids&&(r.sourceEndpoint=T(t.uuids[0]),r.targetEndpoint=T(t.uuids[1])),r.sourceEndpoint&&r.sourceEndpoint.isFull())n.log(i,"could not add connection; source endpoint is full");else if(r.targetEndpoint&&r.targetEndpoint.isFull())n.log(i,"could not add connection; target endpoint is full");else{if(!r.type&&r.sourceEndpoint&&(r.type=r.sourceEndpoint.connectionType),r.sourceEndpoint&&r.sourceEndpoint.connectorOverlays){r.overlays=r.overlays||[];for(var o=0,s=r.sourceEndpoint.connectorOverlays.length;o<s;o++)r.overlays.push(r.sourceEndpoint.connectorOverlays[o])}r.sourceEndpoint&&r.sourceEndpoint.scope&&(r.scope=r.sourceEndpoint.scope),!r["pointer-events"]&&r.sourceEndpoint&&r.sourceEndpoint.connectorPointerEvents&&(r["pointer-events"]=r.sourceEndpoint.connectorPointerEvents);var a=function(t,e,n){var o=L(e,{anchor:r.anchors?r.anchors[n]:r.anchor,endpoint:r.endpoints?r.endpoints[n]:r.endpoint,paintStyle:r.endpointStyles?r.endpointStyles[n]:r.endpointStyle,hoverPaintStyle:r.endpointHoverStyles?r.endpointHoverStyles[n]:r.endpointHoverStyle});return i.addEndpoint(t,o)},u=function(t,e,n,i){if(r[t]&&!r[t].endpoint&&!r[t+"Endpoint"]&&!r.newConnection){var o=W(r[t]),s=n[o];if(s=s?s[i]:null,s){if(!s.enabled)return!1;var u=p.extend({},s.def);delete u.label;var l=null!=s.endpoint&&s.endpoint._jsPlumb?s.endpoint:a(r[t],u,e);if(l.isFull())return!1;r[t+"Endpoint"]=l,!r.scope&&u.scope&&(r.scope=u.scope),s.uniqueEndpoint?s.endpoint?l.finalEndpoint=s.endpoint:(s.endpoint=l,l.setDeleteOnEmpty(!1)):l.setDeleteOnEmpty(!0),0===e&&s.def.connectorOverlays&&(r.overlays=r.overlays||[],Array.prototype.push.apply(r.overlays,s.def.connectorOverlays))}}};if(!1!==u("source",0,this.sourceEndpointDefinitions,r.type||"default")&&!1!==u("target",1,this.targetEndpointDefinitions,r.type||"default"))return r.sourceEndpoint&&r.targetEndpoint&&(M(r.sourceEndpoint,r.targetEndpoint)||(r=null)),r}}.bind(i),R=function(t){var e=i.Defaults.ConnectionType||i.getDefaultConnectionType();t._jsPlumb=i,t.newConnection=R,t.newEndpoint=G,t.endpointsByUUID=x,t.endpointsByElement=y,t.finaliseConnection=N,t.id="con_"+O();var n=new e(t);return n.isDetachable()&&(n.endpoints[0].initDraggable("_jsPlumbSource"),n.endpoints[1].initDraggable("_jsPlumbTarget")),n},N=i.finaliseConnection=function(t,e,n,r){if(e=e||{},t.suspendedEndpoint||b.push(t),t.pending=null,t.endpoints[0].isTemporarySource=!1,!1!==r&&i.router.newConnection(t),k(t.source),!e.doNotFireConnectionEvent&&!1!==e.fireEvent){var o={connection:t,source:t.source,target:t.target,sourceId:t.sourceId,targetId:t.targetId,sourceEndpoint:t.endpoints[0],targetEndpoint:t.endpoints[1]};i.fire("connection",o,n)}},G=function(t,e){var n=i.Defaults.EndpointType||p.Endpoint,r=p.extend({},t);r._jsPlumb=i,r.newConnection=R,r.newEndpoint=G,r.endpointsByUUID=x,r.endpointsByElement=y,r.fireDetachEvent=V,r.elementId=e||W(r.source);var o=new n(r);return o.id="ep_"+O(),dt(r.elementId,r.source),p.headless||i.getDragManager().endpointAdded(r.source,e),o},H=function(t,e,n){var i=y[t];if(i&&i.length)for(var r=0,o=i.length;r<o;r++){for(var s=0,a=i[r].connections.length;s<a;s++){var u=e(i[r].connections[s]);if(u)return}n&&n(i[r])}},B=function(t,e,n){e="block"===e;var i=null;n&&(i=function(t){t.setVisible(e,!0,!0)});var r=c(t);H(r.id,(function(t){if(e&&n){var i=t.sourceId===r.id?1:0;t.endpoints[i].isVisible()&&t.setVisible(!0)}else t.setVisible(e)}),i)},U=function(t,e){var n=null;e&&(n=function(t){var e=t.isVisible();t.setVisible(!e)}),H(t,(function(t){var e=t.isVisible();t.setVisible(!e)}),n)},z=function(t){var e=_[t];return e?{o:e,s:j[t]}:ft({elId:t})},W=function(t,e,r){if(n.isString(t))return t;if(null==t)return null;var s=i.getAttribute(t,"id");return s&&"undefined"!==s||(2===arguments.length&&void 0!==arguments[1]?s=e:(1===arguments.length||3===arguments.length&&!arguments[2])&&(s="jsPlumb_"+o+"_"+O()),r||i.setAttribute(t,"id",s)),s};this.setConnectionBeingDragged=function(t){E=t},this.isConnectionBeingDragged=function(){return E},this.getManagedElements=function(){return P},this.connectorClass="jtk-connector",this.connectorOutlineClass="jtk-connector-outline",this.connectedClass="jtk-connected",this.hoverClass="jtk-hover",this.endpointClass="jtk-endpoint",this.endpointConnectedClass="jtk-endpoint-connected",this.endpointFullClass="jtk-endpoint-full",this.endpointDropAllowedClass="jtk-endpoint-drop-allowed",this.endpointDropForbiddenClass="jtk-endpoint-drop-forbidden",this.overlayClass="jtk-overlay",this.draggingClass="jtk-dragging",this.elementDraggingClass="jtk-element-dragging",this.sourceElementDraggingClass="jtk-source-element-dragging",this.targetElementDraggingClass="jtk-target-element-dragging",this.endpointAnchorClassPrefix="jtk-endpoint-anchor",this.hoverSourceClass="jtk-source-hover",this.hoverTargetClass="jtk-target-hover",this.dragSelectClass="jtk-drag-select",this.Anchors={},this.Connectors={svg:{}},this.Endpoints={svg:{}},this.Overlays={svg:{}},this.ConnectorRenderers={},this.SVG="svg",this.addEndpoint=function(t,e,r){r=r||{};var o=p.extend({},r);p.extend(o,e),o.endpoint=o.endpoint||i.Defaults.Endpoint,o.paintStyle=o.paintStyle||i.Defaults.EndpointStyle;for(var s=[],a=n.isArray(t)||null!=t.length&&!n.isString(t)?t:[t],u=0,l=a.length;u<l;u++){o.source=i.getElement(a[u]),ct(o.source);var c=W(o.source),h=G(o,c),d=dt(c,o.source,null,!S).info.o;n.addToList(y,c,h),S||h.paint({anchorLoc:h.anchor.compute({xy:[d.left,d.top],wh:j[c],element:h,timestamp:w,rotation:this.getRotation(c)}),timestamp:w}),s.push(h)}return 1===s.length?s[0]:s},this.addEndpoints=function(t,e,r){for(var o=[],s=0,a=e.length;s<a;s++){var u=i.addEndpoint(t,e[s],r);n.isArray(u)?Array.prototype.push.apply(o,u):o.push(u)}return o},this.animate=function(t,e,r){if(!this.animationSupported)return!1;r=r||{};var o=i.getElement(t),s=W(o),a=p.animEvents.step,u=p.animEvents.complete;r[a]=n.wrap(r[a],(function(){i.revalidate(s)})),r[u]=n.wrap(r[u],(function(){i.revalidate(s)})),i.doAnimate(o,e,r)},this.checkCondition=function(t,e){var r=i.getListener(t),o=!0;if(r&&r.length>0){var s=Array.prototype.slice.call(arguments,1);try{for(var a=0,u=r.length;a<u;a++)o=o&&r[a].apply(r[a],s)}catch(l){n.log(i,"cannot check condition ["+t+"]"+l)}}return o},this.connect=function(t,e){var i,r=F(t,e);if(r){if(null==r.source&&null==r.sourceEndpoint)return void n.log("Cannot establish connection - source does not exist");if(null==r.target&&null==r.targetEndpoint)return void n.log("Cannot establish connection - target does not exist");ct(r.source),i=R(r),N(i,r)}return i};var Y=[{el:"source",elId:"sourceId",epDefs:"sourceEndpointDefinitions"},{el:"target",elId:"targetId",epDefs:"targetEndpointDefinitions"}],X=function(t,e,n,i){var r,o,s,a=Y[n],u=t[a.elId],l=(t[a.el],t.endpoints[n]),c={index:n,originalSourceId:0===n?u:t.sourceId,newSourceId:t.sourceId,originalTargetId:1===n?u:t.targetId,newTargetId:t.targetId,connection:t};if(e.constructor===p.Endpoint)r=e,r.addConnection(t),e=r.element;else if(o=W(e),s=this[a.epDefs][o],o===t[a.elId])r=null;else if(s)for(var h in s){if(!s[h].enabled)return;r=null!=s[h].endpoint&&s[h].endpoint._jsPlumb?s[h].endpoint:this.addEndpoint(e,s[h].def),s[h].uniqueEndpoint&&(s[h].endpoint=r),r.addConnection(t)}else r=t.makeEndpoint(0===n,e,o);return null!=r&&(l.detachFromConnection(t),t.endpoints[n]=r,t[a.el]=r.element,t[a.elId]=r.elementId,c[0===n?"newSourceId":"newTargetId"]=r.elementId,q(c),i||t.repaint()),c.element=e,c}.bind(this);this.setSource=function(t,e,n){var i=X(t,e,0,n);this.router.sourceOrTargetChanged(i.originalSourceId,i.newSourceId,t,i.el,0)},this.setTarget=function(t,e,n){var i=X(t,e,1,n);this.router.sourceOrTargetChanged(i.originalTargetId,i.newTargetId,t,i.el,1)},this.deleteEndpoint=function(t,e,n){var r="string"===typeof t?x[t]:t;return r&&i.deleteObject({endpoint:r,dontUpdateHover:e,deleteAttachedObjects:n}),i},this.deleteEveryEndpoint=function(){var t=i.setSuspendDrawing(!0);for(var e in y){var n=y[e];if(n&&n.length)for(var r=0,o=n.length;r<o;r++)i.deleteEndpoint(n[r],!0)}y={},P={},x={},_={},C={},i.router.reset();var s=i.getDragManager();return s&&s.reset(),t||i.setSuspendDrawing(!1),i};var V=function(t,e,n){var r=i.Defaults.ConnectionType||i.getDefaultConnectionType(),o=t.constructor===r,s=o?{connection:t,source:t.source,target:t.target,sourceId:t.sourceId,targetId:t.targetId,sourceEndpoint:t.endpoints[0],targetEndpoint:t.endpoints[1]}:t;e&&i.fire("connectionDetached",s,n),i.fire("internal.connectionDetached",s,n),i.router.connectionDetached(s)},q=i.fireMoveEvent=function(t,e){i.fire("connectionMoved",t,e)};this.unregisterEndpoint=function(t){for(var e in t._jsPlumb.uuid&&(x[t._jsPlumb.uuid]=null),i.router.deleteEndpoint(t),y){var n=y[e];if(n){for(var r=[],o=0,s=n.length;o<s;o++)n[o]!==t&&r.push(n[o]);y[e]=r}y[e].length<1&&delete y[e]}};var $="isDetachAllowed",J="beforeDetach",K="checkCondition";this.deleteConnection=function(t,e){return!(null==t||(e=e||{},!e.force&&!n.functionChain(!0,!1,[[t.endpoints[0],$,[t]],[t.endpoints[1],$,[t]],[t,$,[t]],[i,K,[J,t]]])))&&(t.setHover(!1),V(t,!t.pending&&!1!==e.fireEvent,e.originalEvent),t.endpoints[0].detachFromConnection(t),t.endpoints[1].detachFromConnection(t),n.removeWithFunction(b,(function(e){return t.id===e.id})),t.cleanup(),t.destroy(),!0)},this.deleteEveryConnection=function(t){t=t||{};var e=b.length,n=0;return i.batch((function(){for(var r=0;r<e;r++)n+=i.deleteConnection(b[0],t)?1:0})),n},this.deleteConnectionsForElement=function(t,e){e=e||{},t=i.getElement(t);var n=W(t),r=y[n];if(r&&r.length)for(var o=0,s=r.length;o<s;o++)r[o].deleteEveryConnection(e);return i},this.deleteObject=function(t){var e={endpoints:{},connections:{},endpointCount:0,connectionCount:0},r=!1!==t.deleteAttachedObjects,o=function(n){null!=n&&null==e.connections[n.id]&&(t.dontUpdateHover||null==n._jsPlumb||n.setHover(!1),e.connections[n.id]=n,e.connectionCount++)},s=function(n){if(null!=n&&null==e.endpoints[n.id]&&(t.dontUpdateHover||null==n._jsPlumb||n.setHover(!1),e.endpoints[n.id]=n,e.endpointCount++,r))for(var i=0;i<n.connections.length;i++){var s=n.connections[i];o(s)}};for(var a in t.connection?o(t.connection):s(t.endpoint),e.connections){var u=e.connections[a];if(u._jsPlumb){n.removeWithFunction(b,(function(t){return u.id===t.id})),V(u,!1!==t.fireEvent&&!u.pending,t.originalEvent);var l=null==t.deleteAttachedObjects?null:!t.deleteAttachedObjects;u.endpoints[0].detachFromConnection(u,null,l),u.endpoints[1].detachFromConnection(u,null,l),u.cleanup(!0),u.destroy(!0)}}for(var c in e.endpoints){var h=e.endpoints[c];h._jsPlumb&&(i.unregisterEndpoint(h),h.cleanup(!0),h.destroy(!0))}return e};var Z=function(t,e,n,i){for(var r=0,o=t.length;r<o;r++)t[r][e].apply(t[r],n);return i(t)},Q=function(t,e,n){for(var i=[],r=0,o=t.length;r<o;r++)i.push([t[r][e].apply(t[r],n),t[r]]);return i},tt=function(t,e,n){return function(){return Z(t,e,arguments,n)}},et=function(t,e){return function(){return Q(t,e,arguments)}},nt=function(t,e){var n=[];if(t)if("string"===typeof t){if("*"===t)return t;n.push(t)}else if(e)n=t;else if(t.length)for(var i=0,r=t.length;i<r;i++)n.push(c(t[i]).id);else n.push(c(t).id);return n},it=function(t,e,n){return"*"===t||(t.length>0?-1!==t.indexOf(e):!n)};this.getConnections=function(t,e){t?t.constructor===String&&(t={scope:t}):t={};for(var n=t.scope||i.getDefaultScope(),r=nt(n,!0),o=nt(t.source),s=nt(t.target),a=!e&&r.length>1?{}:[],u=function(t,n){if(!e&&r.length>1){var i=a[t];null==i&&(i=a[t]=[]),i.push(n)}else a.push(n)},l=0,c=b.length;l<c;l++){var h=b[l],d=h.proxies&&h.proxies[0]?h.proxies[0].originalEp.elementId:h.sourceId,f=h.proxies&&h.proxies[1]?h.proxies[1].originalEp.elementId:h.targetId;it(r,h.scope)&&it(o,d)&&it(s,f)&&u(h.scope,h)}return a};var rt=function(t,e){return function(n){for(var i=0,r=t.length;i<r;i++)n(t[i]);return e(t)}},ot=function(t){return function(e){return t[e]}},st=function(t,e){var n,i,r={length:t.length,each:rt(t,e),get:ot(t)},o=["setHover","removeAllOverlays","setLabel","addClass","addOverlay","removeOverlay","removeOverlays","showOverlay","hideOverlay","showOverlays","hideOverlays","setPaintStyle","setHoverPaintStyle","setSuspendEvents","setParameter","setParameters","setVisible","repaint","addType","toggleType","removeType","removeClass","setType","bind","unbind"],s=["getLabel","getOverlay","isHover","getParameter","getParameters","getPaintStyle","getHoverPaintStyle","isVisible","hasType","getType","isSuspendEvents"];for(n=0,i=o.length;n<i;n++)r[o[n]]=tt(t,o[n],e);for(n=0,i=s.length;n<i;n++)r[s[n]]=et(t,s[n]);return r},at=function(t){var e=st(t,at);return p.extend(e,{setDetachable:tt(t,"setDetachable",at),setReattach:tt(t,"setReattach",at),setConnector:tt(t,"setConnector",at),delete:function(){for(var e=0,n=t.length;e<n;e++)i.deleteConnection(t[e])},isDetachable:et(t,"isDetachable"),isReattach:et(t,"isReattach")})},ut=function(t){var e=st(t,ut);return p.extend(e,{setEnabled:tt(t,"setEnabled",ut),setAnchor:tt(t,"setAnchor",ut),isEnabled:et(t,"isEnabled"),deleteEveryConnection:function(){for(var e=0,n=t.length;e<n;e++)t[e].deleteEveryConnection()},delete:function(){for(var e=0,n=t.length;e<n;e++)i.deleteEndpoint(t[e])}})};this.select=function(t){return t=t||{},t.scope=t.scope||"*",at(t.connections||i.getConnections(t,!0))},this.selectEndpoints=function(t){t=t||{},t.scope=t.scope||"*";var e=!t.element&&!t.source&&!t.target,n=e?"*":nt(t.element),i=e?"*":nt(t.source),r=e?"*":nt(t.target),o=nt(t.scope,!0),s=[];for(var a in y){var u=it(n,a,!0),l=it(i,a,!0),c="*"!==i,h=it(r,a,!0),d="*"!==r;if(u||l||h)t:for(var f=0,p=y[a].length;f<p;f++){var g=y[a][f];if(it(o,g.scope,!0)){var v=c&&i.length>0&&!g.isSource,m=d&&r.length>0&&!g.isTarget;if(v||m)continue t;s.push(g)}}}return ut(s)},this.getAllConnections=function(){return b},this.getDefaultScope=function(){return D},this.getEndpoint=T,this.getEndpoints=function(t){return y[c(t).id]||[]},this.getDefaultEndpointType=function(){return p.Endpoint},this.getDefaultConnectionType=function(){return p.Connection},this.getId=W,this.draw=k,this.info=c,this.appendElement=I;var lt=!1;this.isHoverSuspended=function(){return lt},this.setHoverSuspended=function(t){lt=t},this.hide=function(t,e){return B(t,"none",e),i},this.idstamp=O;var ct=function(t){if(!f&&t){var e=i.getElement(t);e.offsetParent&&i.setContainer(e.offsetParent)}},ht=function(){i.Defaults.Container&&i.setContainer(i.Defaults.Container)},dt=i.manage=function(t,e,n,r){return P[t]?r&&(P[t].info=ft({elId:t,timestamp:w,recalc:!0})):(P[t]={el:e,endpoints:[],connections:[],rotation:0},P[t].info=ft({elId:t,timestamp:w}),i.addClass(e,"jtk-managed"),n||i.fire("manageElement",{id:t,info:P[t].info,el:e})),P[t]};this.unmanage=function(t){if(P[t]){var e=P[t].el;i.removeClass(e,"jtk-managed"),delete P[t],i.fire("unmanageElement",{id:t,el:e})}},this.rotate=function(t,e,n){return P[t]&&(P[t].rotation=e,P[t].el.style.transform="rotate("+e+"deg)",P[t].el.style.transformOrigin="center center",!0!==n)?this.revalidate(t):{c:[],e:[]}},this.getRotation=function(t){return P[t]&&P[t].rotation||0};var ft=function(t){var e,n=t.timestamp,r=t.recalc,o=t.offset,s=t.elId;return S&&!n&&(n=w),!r&&n&&n===C[s]?{o:t.offset||_[s],s:j[s]}:(r||!o&&null==_[s]?(e=P[s]?P[s].el:null,null!=e&&(j[s]=i.getSize(e),_[s]=i.getOffset(e),C[s]=n)):(_[s]=o||_[s],null==j[s]&&(e=P[s].el,null!=e&&(j[s]=i.getSize(e))),C[s]=n),_[s]&&!_[s].right&&(_[s].right=_[s].left+j[s][0],_[s].bottom=_[s].top+j[s][1],_[s].width=j[s][0],_[s].height=j[s][1],_[s].centerx=_[s].left+_[s].width/2,_[s].centery=_[s].top+_[s].height/2),{o:_[s],s:j[s]})};this.updateOffset=ft,this.init=function(){m||(ht(),i.router=new t.jsPlumb.DefaultRouter(i),i.anchorManager=i.router.anchorManager,m=!0,i.fire("ready",i))}.bind(this),this.log=v,this.jsPlumbUIComponent=l,this.makeAnchor=function(){var e,r=function(e,n){if(t.jsPlumb.Anchors[e])return new t.jsPlumb.Anchors[e](n);if(!i.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown anchor type '"+e+"'"}};if(0===arguments.length)return null;var o=arguments[0],s=arguments[1],a=null;if(o.compute&&o.getOrientation)return o;if("string"===typeof o)a=r(arguments[0],{elementId:s,jsPlumbInstance:i});else if(n.isArray(o))if(n.isArray(o[0])||n.isString(o[0]))2===o.length&&n.isObject(o[1])?n.isString(o[0])?(e=t.jsPlumb.extend({elementId:s,jsPlumbInstance:i},o[1]),a=r(o[0],e)):(e=t.jsPlumb.extend({elementId:s,jsPlumbInstance:i,anchors:o[0]},o[1]),a=new t.jsPlumb.DynamicAnchor(e)):a=new p.DynamicAnchor({anchors:o,selector:null,elementId:s,jsPlumbInstance:i});else{var u={x:o[0],y:o[1],orientation:o.length>=4?[o[2],o[3]]:[0,0],offsets:o.length>=6?[o[4],o[5]]:[0,0],elementId:s,jsPlumbInstance:i,cssClass:7===o.length?o[6]:null};a=new t.jsPlumb.Anchor(u),a.clone=function(){return new t.jsPlumb.Anchor(u)}}return a.id||(a.id="anchor_"+O()),a},this.makeAnchors=function(e,r,o){for(var s=[],a=0,u=e.length;a<u;a++)"string"===typeof e[a]?s.push(t.jsPlumb.Anchors[e[a]]({elementId:r,jsPlumbInstance:o})):n.isArray(e[a])&&s.push(i.makeAnchor(e[a],r,o));return s},this.makeDynamicAnchor=function(e,n){return new t.jsPlumb.DynamicAnchor({anchors:e,selector:n,elementId:null,jsPlumbInstance:i})},this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={};var pt=function(t,e,n,i,r){for(var o=t.target||t.srcElement,s=!1,a=i.getSelector(e,n),u=0;u<a.length;u++)if(a[u]===o){s=!0;break}return r?!s:s},gt=function(e,r,o,s,a){var u=new l(r),c=r._jsPlumb.EndpointDropHandler({jsPlumb:i,enabled:function(){return e.def.enabled},isFull:function(){var t=i.select({target:e.id}).length;return e.def.maxConnections>0&&t>=e.def.maxConnections},element:e.el,elementId:e.id,isSource:s,isTarget:a,addClass:function(t){i.addClass(e.el,t)},removeClass:function(t){i.removeClass(e.el,t)},onDrop:function(t){var e=t.endpoints[0];e.anchor.locked=!1},isDropAllowed:function(){return u.isDropAllowed.apply(u,arguments)},isRedrop:function(t){return null!=t.suspendedElement&&null!=t.suspendedEndpoint&&t.suspendedEndpoint.element===e.el},getEndpoint:function(n){var o=e.def.endpoint;if(null==o||null==o._jsPlumb){var s=i.deriveEndpointAndAnchorSpec(n.getType().join(" "),!0),a=s.endpoints?t.jsPlumb.extend(r,{endpoint:e.def.def.endpoint||s.endpoints[1]}):r;s.anchors&&(a=t.jsPlumb.extend(a,{anchor:e.def.def.anchor||s.anchors[1]})),o=i.addEndpoint(e.el,a),o._mtNew=!0}if(r.uniqueEndpoint&&(e.def.endpoint=o),o.setDeleteOnEmpty(!0),n.isDetachable()&&o.initDraggable(),null!=o.anchor.positionFinder){var u=i.getUIPosition(arguments,i.getZoom()),l=i.getOffset(e.el),c=i.getSize(e.el),h=null==u?[0,0]:o.anchor.positionFinder(u,l,c,o.anchor.constructorParams);o.anchor.x=h[0],o.anchor.y=h[1]}return o},maybeCleanup:function(t){t._mtNew&&0===t.connections.length?i.deleteObject({endpoint:t}):delete t._mtNew}}),h=t.jsPlumb.dragEvents.drop;return o.scope=o.scope||r.scope||i.Defaults.Scope,o[h]=n.wrap(o[h],c,!0),o.rank=r.rank||0,a&&(o[t.jsPlumb.dragEvents.over]=function(){return!0}),!1===r.allowLoopback&&(o.canDrop=function(t){var n=t.getDragElement()._jsPlumbRelatedElement;return n!==e.el}),i.initDroppable(e.el,o,"internal"),c};this.makeTarget=function(e,n,r){var o=t.jsPlumb.extend({_jsPlumb:this},r);t.jsPlumb.extend(o,n);for(var s=o.maxConnections||-1,a=function(e){var n=c(e),r=n.id,a=t.jsPlumb.extend({},o.dropOptions||{}),u=o.connectionType||"default";this.targetEndpointDefinitions[r]=this.targetEndpointDefinitions[r]||{},ct(r),n.el._isJsPlumbGroup&&null==a.rank&&(a.rank=-1);var l={def:t.jsPlumb.extend({},o),uniqueEndpoint:o.uniqueEndpoint,maxConnections:s,enabled:!0};o.createEndpoint&&(l.uniqueEndpoint=!0,l.endpoint=i.addEndpoint(e,l.def),l.endpoint.setDeleteOnEmpty(!1)),n.def=l,this.targetEndpointDefinitions[r][u]=l,gt(n,o,a,!0===o.isSource,!0),n.el._katavorioDrop[n.el._katavorioDrop.length-1].targetDef=l}.bind(this),u=e.length&&e.constructor!==String?e:[e],l=0,h=u.length;l<h;l++)a(u[l]);return this},this.unmakeTarget=function(t,e){var n=c(t);return i.destroyDroppable(n.el,"internal"),e||delete this.targetEndpointDefinitions[n.id],this},this.makeSource=function(e,r,o){var s=t.jsPlumb.extend({_jsPlumb:this},o);t.jsPlumb.extend(s,r);var a=s.connectionType||"default",l=i.deriveEndpointAndAnchorSpec(a);s.endpoint=s.endpoint||l.endpoints[0],s.anchor=s.anchor||l.anchors[0];for(var h=s.maxConnections||-1,d=s.onMaxConnections,f=function(r){var o=r.id,l=this.getElement(r.el);this.sourceEndpointDefinitions[o]=this.sourceEndpointDefinitions[o]||{},ct(o);var c={def:t.jsPlumb.extend({},s),uniqueEndpoint:s.uniqueEndpoint,maxConnections:h,enabled:!0};s.createEndpoint&&(c.uniqueEndpoint=!0,c.endpoint=i.addEndpoint(e,c.def),c.endpoint.setDeleteOnEmpty(!1)),this.sourceEndpointDefinitions[o][a]=c,r.def=c;var f=t.jsPlumb.dragEvents.stop,p=t.jsPlumb.dragEvents.drag,g=t.jsPlumb.extend({},s.dragOptions||{}),v=g.drag,m=g.stop,b=null,y=!1;g.scope=g.scope||s.scope,g[p]=n.wrap(g[p],(function(){v&&v.apply(this,arguments),y=!1})),g[f]=n.wrap(g[f],function(){if(m&&m.apply(this,arguments),this.currentlyDragging=!1,null!=b._jsPlumb){var t=s.anchor||this.Defaults.Anchor,e=b.anchor,n=b.connections[0],r=this.makeAnchor(t,o,this),a=b.element;if(null!=r.positionFinder){var u=i.getOffset(a),l=this.getSize(a),c={left:u.left+e.x*l[0],top:u.top+e.y*l[1]},h=r.positionFinder(c,u,l,r.constructorParams);r.x=h[0],r.y=h[1]}b.setAnchor(r,!0),b.repaint(),this.repaint(b.elementId),null!=n&&this.repaint(n.targetId)}}.bind(this));var x=function(e){if(3!==e.which&&2!==e.button){o=this.getId(this.getElement(r.el));var c=this.sourceEndpointDefinitions[o][a];if(c.enabled){if(s.filter){var f=n.isString(s.filter)?pt(e,r.el,s.filter,this,s.filterExclude):s.filter(e,r.el);if(!1===f)return}var p=this.select({source:o}).length;if(c.maxConnections>=0&&p>=c.maxConnections)return d&&d({element:r.el,maxConnections:h},e),!1;var v=t.jsPlumb.getPositionOnElement(e,l,u),m={};t.jsPlumb.extend(m,c.def),m.isTemporarySource=!0,m.anchor=[v[0],v[1],0,0],m.dragOptions=g,c.def.scope&&(m.scope=c.def.scope),b=this.addEndpoint(o,m),y=!0,b.setDeleteOnEmpty(!0),c.uniqueEndpoint&&(c.endpoint?b.finalEndpoint=c.endpoint:(c.endpoint=b,b.setDeleteOnEmpty(!1)));var x=function(){i.off(b.canvas,"mouseup",x),i.off(r.el,"mouseup",x),y&&(y=!1,i.deleteEndpoint(b))};i.on(b.canvas,"mouseup",x),i.on(r.el,"mouseup",x);var P={};if(c.def.extract)for(var _ in c.def.extract){var C=(e.srcElement||e.target).getAttribute(_);C&&(P[c.def.extract[_]]=C)}i.trigger(b.canvas,"mousedown",e,P),n.consume(e)}}}.bind(this);this.on(r.el,"mousedown",x),c.trigger=x,s.filter&&(n.isString(s.filter)||n.isFunction(s.filter))&&i.setDragFilter(r.el,s.filter);var P=t.jsPlumb.extend({},s.dropOptions||{});gt(r,s,P,!0,!0===s.isTarget)}.bind(this),p=e.length&&e.constructor!==String?e:[e],g=0,v=p.length;g<v;g++)f(c(p[g]));return this},this.unmakeSource=function(t,e,n){var r=c(t);i.destroyDroppable(r.el,"internal");var o=this.sourceEndpointDefinitions[r.id];if(o)for(var s in o)if(null==e||e===s){var a=o[s].trigger;a&&i.off(r.el,"mousedown",a),n||delete this.sourceEndpointDefinitions[r.id][s]}return this},this.unmakeEverySource=function(){for(var t in this.sourceEndpointDefinitions)i.unmakeSource(t,null,!0);return this.sourceEndpointDefinitions={},this};var vt=function(t,e,i){e=n.isArray(e)?e:[e];var r=W(t);i=i||"default";for(var o=0;o<e.length;o++){var s=this[e[o]][r];if(s&&s[i])return s[i].def.scope||this.Defaults.Scope}}.bind(this),mt=function(t,e,i,r){i=n.isArray(i)?i:[i];var o=W(t);r=r||"default";for(var s=0;s<i.length;s++){var a=this[i[s]][o];a&&a[r]&&(a[r].def.scope=e)}}.bind(this);this.getScope=function(t,e){return vt(t,["sourceEndpointDefinitions","targetEndpointDefinitions"])},this.getSourceScope=function(t){return vt(t,"sourceEndpointDefinitions")},this.getTargetScope=function(t){return vt(t,"targetEndpointDefinitions")},this.setScope=function(t,e,n){this.setSourceScope(t,e,n),this.setTargetScope(t,e,n)},this.setSourceScope=function(t,e,n){mt(t,e,"sourceEndpointDefinitions",n),this.setDragScope(t,e)},this.setTargetScope=function(t,e,n){mt(t,e,"targetEndpointDefinitions",n),this.setDropScope(t,e)},this.unmakeEveryTarget=function(){for(var t in this.targetEndpointDefinitions)i.unmakeTarget(t,!0);return this.targetEndpointDefinitions={},this};var bt=function(t,e,r,o,s){var a,u,l,h="source"===t?this.sourceEndpointDefinitions:this.targetEndpointDefinitions;if(s=s||"default",e.length&&!n.isString(e)){a=[];for(var d=0,f=e.length;d<f;d++)u=c(e[d]),h[u.id]&&h[u.id][s]&&(a[d]=h[u.id][s].enabled,l=o?!a[d]:r,h[u.id][s].enabled=l,i[l?"removeClass":"addClass"](u.el,"jtk-"+t+"-disabled"))}else{u=c(e);var p=u.id;h[p]&&h[p][s]&&(a=h[p][s].enabled,l=o?!a:r,h[p][s].enabled=l,i[l?"removeClass":"addClass"](u.el,"jtk-"+t+"-disabled"))}return a}.bind(this),yt=function(t,e){if(null!=t){if(n.isString(t)||!t.length)return e.apply(this,[t]);if(t.length)return e.apply(this,[t[0]])}}.bind(this);this.toggleSourceEnabled=function(t,e){return bt("source",t,null,!0,e),this.isSourceEnabled(t,e)},this.setSourceEnabled=function(t,e,n){return bt("source",t,e,null,n)},this.isSource=function(t,e){return e=e||"default",yt(t,function(t){var n=this.sourceEndpointDefinitions[c(t).id];return null!=n&&null!=n[e]}.bind(this))},this.isSourceEnabled=function(t,e){return e=e||"default",yt(t,function(t){var n=this.sourceEndpointDefinitions[c(t).id];return n&&n[e]&&!0===n[e].enabled}.bind(this))},this.toggleTargetEnabled=function(t,e){return bt("target",t,null,!0,e),this.isTargetEnabled(t,e)},this.isTarget=function(t,e){return e=e||"default",yt(t,function(t){var n=this.targetEndpointDefinitions[c(t).id];return null!=n&&null!=n[e]}.bind(this))},this.isTargetEnabled=function(t,e){return e=e||"default",yt(t,function(t){var n=this.targetEndpointDefinitions[c(t).id];return n&&n[e]&&!0===n[e].enabled}.bind(this))},this.setTargetEnabled=function(t,e,n){return bt("target",t,e,null,n)},this.ready=function(t){i.bind("ready",t)};var xt=function(t,e){if("object"===typeof t&&t.length)for(var n=0,r=t.length;n<r;n++)e(t[n]);else e(t);return i};this.repaint=function(t,e,n){return xt(t,(function(t){k(t,e,n)}))},this.revalidate=function(t,e,n){var r=n?t:i.getId(t);i.updateOffset({elId:r,recalc:!0,timestamp:e});var o=i.getDragManager();return o&&o.updateOffsets(r),k(t,null,e)},this.repaintEverything=function(){var t,e=jsPlumbUtil.uuid();for(t in y)i.updateOffset({elId:t,recalc:!0,timestamp:e});for(t in y)k(t,null,e);return this},this.removeAllEndpoints=function(t,e,n){n=n||[];var r=function(t){var o,s,a=c(t),u=y[a.id];if(u)for(n.push(a),o=0,s=u.length;o<s;o++)i.deleteEndpoint(u[o],!1);if(delete y[a.id],e&&a.el&&3!==a.el.nodeType&&8!==a.el.nodeType)for(o=0,s=a.el.childNodes.length;o<s;o++)r(a.el.childNodes[o])};return r(t),this};var Pt=function(t,e){i.removeAllEndpoints(t.id,!0,e);for(var n=i.getDragManager(),r=function(t){n&&n.elementRemoved(t.id),i.router.elementRemoved(t.id),i.isSource(t.el)&&i.unmakeSource(t.el),i.isTarget(t.el)&&i.unmakeTarget(t.el),i.destroyDraggable(t.el),i.destroyDroppable(t.el),delete i.floatingConnections[t.id],delete P[t.id],delete _[t.id],t.el&&(i.removeElement(t.el),t.el._jsPlumb=null)},o=1;o<e.length;o++)r(e[o]);r(t)};this.remove=function(t,e){var n=c(t),r=[];return n.text&&n.el.parentNode?n.el.parentNode.removeChild(n.el):n.id&&i.batch((function(){Pt(n,r)}),!0===e),i},this.empty=function(t,e){var n=[],r=function(t,e){var i=c(t);if(i.text)i.el.parentNode.removeChild(i.el);else if(i.el){while(i.el.childNodes.length>0)r(i.el.childNodes[0]);e||Pt(i,n)}};return i.batch((function(){r(t,!0)}),!1===e),i},this.reset=function(t){i.silently(function(){lt=!1,i.removeAllGroups(),i.removeGroupManager(),i.deleteEveryEndpoint(),t||i.unbind(),this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={},b.length=0,this.doReset&&this.doReset()}.bind(this))},this.destroy=function(){this.reset(),f=null,g=null};var _t=function(t){t.canvas&&t.canvas.parentNode&&t.canvas.parentNode.removeChild(t.canvas),t.cleanup(),t.destroy()};this.clear=function(){i.select().each(_t),i.selectEndpoints().each(_t),y={},x={}},this.setDefaultScope=function(t){return D=t,i},this.deriveEndpointAndAnchorSpec=function(t,e){for(var n=((e?"":"default ")+t).split(/[\s]/),r=null,o=null,s=null,a=null,u=0;u<n.length;u++){var l=i.getType(n[u],"connection");l&&(l.endpoints&&(r=l.endpoints),l.endpoint&&(o=l.endpoint),l.anchors&&(a=l.anchors),l.anchor&&(s=l.anchor))}return{endpoints:r||[o,o],anchors:a||[s,s]}},this.setId=function(t,e,i){var r;n.isString(t)?r=t:(t=this.getElement(t),r=this.getId(t));var o=this.getConnections({source:r,scope:"*"},!0),s=this.getConnections({target:r,scope:"*"},!0);e=""+e,i?t=this.getElement(e):(t=this.getElement(r),this.setAttribute(t,"id",e)),y[e]=y[r]||[];for(var a=0,u=y[e].length;a<u;a++)y[e][a].setElementId(e),y[e][a].setReferenceElement(t);delete y[r],this.sourceEndpointDefinitions[e]=this.sourceEndpointDefinitions[r],delete this.sourceEndpointDefinitions[r],this.targetEndpointDefinitions[e]=this.targetEndpointDefinitions[r],delete this.targetEndpointDefinitions[r],this.router.changeId(r,e);var l=this.getDragManager();l&&l.changeId(r,e),P[e]=P[r],delete P[r];var c=function(n,i,r){for(var o=0,s=n.length;o<s;o++)n[o].endpoints[i].setElementId(e),n[o].endpoints[i].setReferenceElement(t),n[o][r+"Id"]=e,n[o][r]=t};c(o,0,"source"),c(s,1,"target"),this.repaint(e)},this.setDebugLog=function(t){v=t},this.setSuspendDrawing=function(t,e){var n=S;return S=t,w=t?(new Date).getTime():null,e&&this.repaintEverything(),n},this.isSuspendDrawing=function(){return S},this.getSuspendedAt=function(){return w},this.batch=function(t,e){var i=this.isSuspendDrawing();i||this.setSuspendDrawing(!0);try{t()}catch(r){n.log("Function run while suspended failed",r)}i||this.setSuspendDrawing(!1,!e)},this.doWhileSuspended=this.batch,this.getCachedData=z,this.show=function(t,e){return B(t,"block",e),i},this.toggleVisible=U,this.addListener=this.bind;var Ct=[];this.registerFloatingConnection=function(t,e,i){Ct[t.id]=e,n.addToList(y,t.id,i)},this.getFloatingConnectionFor=function(t){return Ct[t]},this.listManager=new t.jsPlumbListManager(this,this.Defaults.ListStyle)};n.extend(t.jsPlumbInstance,n.EventGenerator,{setAttribute:function(t,e,n){this.setAttribute(t,e,n)},getAttribute:function(e,n){return this.getAttribute(t.jsPlumb.getElement(e),n)},convertToFullOverlaySpec:function(t){return n.isString(t)&&(t=[t,{}]),t[1].id=t[1].id||n.uuid(),t},registerConnectionType:function(e,n){if(this._connectionTypes[e]=t.jsPlumb.extend({},n),n.overlays){for(var i={},r=0;r<n.overlays.length;r++){var o=this.convertToFullOverlaySpec(n.overlays[r]);i[o[1].id]=o}this._connectionTypes[e].overlays=i}},registerConnectionTypes:function(t){for(var e in t)this.registerConnectionType(e,t[e])},registerEndpointType:function(e,n){if(this._endpointTypes[e]=t.jsPlumb.extend({},n),n.overlays){for(var i={},r=0;r<n.overlays.length;r++){var o=this.convertToFullOverlaySpec(n.overlays[r]);i[o[1].id]=o}this._endpointTypes[e].overlays=i}},registerEndpointTypes:function(t){for(var e in t)this.registerEndpointType(e,t[e])},getType:function(t,e){return"connection"===e?this._connectionTypes[t]:this._endpointTypes[t]},setIdChanged:function(t,e){this.setId(t,e,!0)},setParent:function(t,e){var n=this.getElement(t),i=this.getId(n),r=this.getElement(e),o=this.getId(r),s=this.getDragManager();n.parentNode.removeChild(n),r.appendChild(n),s&&s.setParent(n,i,r,o)},extend:function(t,e,n){var i;if(n)for(i=0;i<n.length;i++)t[n[i]]=e[n[i]];else for(i in e)t[i]=e[i];return t},floatingConnections:{},getFloatingAnchorIndex:function(t){return t.endpoints[0].isFloating()?0:t.endpoints[1].isFloating()?1:-1},proxyConnection:function(t,e,n,i,r,o){var s,a=t.endpoints[e].elementId,u=t.endpoints[e];t.proxies=t.proxies||[],s=t.proxies[e]?t.proxies[e].ep:this.addEndpoint(n,{endpoint:r(t,e),anchor:o(t,e),parameters:{isProxyEndpoint:!0}}),s.setDeleteOnEmpty(!0),t.proxies[e]={ep:s,originalEp:u},0===e?this.router.sourceOrTargetChanged(a,i,t,n,0):this.router.sourceOrTargetChanged(a,i,t,n,1),u.detachFromConnection(t,null,!0),s.connections=[t],t.endpoints[e]=s,u.setVisible(!1),t.setVisible(!0),this.revalidate(n)},unproxyConnection:function(t,e,n){if(null!=t._jsPlumb&&null!=t.proxies&&null!=t.proxies[e]){var i=t.proxies[e].originalEp.element,r=t.proxies[e].originalEp.elementId;t.endpoints[e]=t.proxies[e].originalEp,0===e?this.router.sourceOrTargetChanged(n,r,t,i,0):this.router.sourceOrTargetChanged(n,r,t,i,1),t.proxies[e].ep.detachFromConnection(t,null),t.proxies[e].originalEp.addConnection(t),t.isVisible()&&t.proxies[e].originalEp.setVisible(!0),delete t.proxies[e]}}});var p=new f;t.jsPlumb=p,p.getInstance=function(t,e){var n=new f(t);if(e)for(var i in e)n[i]=e[i];return n.init(),n},p.each=function(t,e){if(null!=t)if("string"===typeof t)e(p.getElement(t));else if(null!=t.length)for(var n=0;n<t.length;n++)e(p.getElement(t[n]));else e(t)},e.jsPlumb=p}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i="__label",r=function(t,n){var r={cssClass:n.cssClass,labelStyle:t.labelStyle,id:i,component:t,_jsPlumb:t._jsPlumb.instance},o=e.extend(r,n);return new(e.Overlays[t._jsPlumb.instance.getRenderMode()].Label)(o)},o=function(t,i){var r=null;if(n.isArray(i)){var o=i[0],s=e.extend({component:t,_jsPlumb:t._jsPlumb.instance},i[1]);3===i.length&&e.extend(s,i[2]),r=new(e.Overlays[t._jsPlumb.instance.getRenderMode()][o])(s)}else r=i.constructor===String?new(e.Overlays[t._jsPlumb.instance.getRenderMode()][i])({component:t,_jsPlumb:t._jsPlumb.instance}):i;return r.id=r.id||n.uuid(),t.cacheTypeItem("overlay",r,r.id),t._jsPlumb.overlays[r.id]=r,r};e.OverlayCapableJsPlumbUIComponent=function(e){t.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.overlays={},this._jsPlumb.overlayPositions={},e.label&&(this.getDefaultType().overlays[i]=["Label",{label:e.label,location:e.labelLocation||this.defaultLabelLocation||.5,labelStyle:e.labelStyle||this._jsPlumb.instance.Defaults.LabelStyle,id:i}]),this.setListenerComponent=function(t){if(this._jsPlumb)for(var e in this._jsPlumb.overlays)this._jsPlumb.overlays[e].setListenerComponent(t)}},e.OverlayCapableJsPlumbUIComponent.applyType=function(t,e){if(e.overlays){var n,i={};for(n in e.overlays){var r=t._jsPlumb.overlays[e.overlays[n][1].id];if(r)r.updateFrom(e.overlays[n][1]),i[e.overlays[n][1].id]=!0,r.reattach(t._jsPlumb.instance,t);else{var o=t.getCachedTypeItem("overlay",e.overlays[n][1].id);null!=o?(o.reattach(t._jsPlumb.instance,t),o.setVisible(!0),o.updateFrom(e.overlays[n][1]),t._jsPlumb.overlays[o.id]=o):o=t.addOverlay(e.overlays[n],!0),i[o.id]=!0}}for(n in t._jsPlumb.overlays)null==i[t._jsPlumb.overlays[n].id]&&t.removeOverlay(t._jsPlumb.overlays[n].id,!0)}},n.extend(e.OverlayCapableJsPlumbUIComponent,t.jsPlumbUIComponent,{setHover:function(t,e){if(this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged())for(var n in this._jsPlumb.overlays)this._jsPlumb.overlays[n][t?"addClass":"removeClass"](this._jsPlumb.instance.hoverClass)},addOverlay:function(t,e){var i=o(this,t);if(this.getData&&"Label"===i.type&&n.isArray(t)){var r=this.getData(),s=t[1];if(r){var a=s.labelLocationAttribute||"labelLocation",u=r?r[a]:null;u&&(i.loc=u)}}return e||this.repaint(),i},getOverlay:function(t){return this._jsPlumb.overlays[t]},getOverlays:function(){return this._jsPlumb.overlays},hideOverlay:function(t){var e=this.getOverlay(t);e&&e.hide()},hideOverlays:function(){for(var t in this._jsPlumb.overlays)this._jsPlumb.overlays[t].hide()},showOverlay:function(t){var e=this.getOverlay(t);e&&e.show()},showOverlays:function(){for(var t in this._jsPlumb.overlays)this._jsPlumb.overlays[t].show()},removeAllOverlays:function(t){for(var e in this._jsPlumb.overlays)this._jsPlumb.overlays[e].cleanup&&this._jsPlumb.overlays[e].cleanup();this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null,this._jsPlumb.overlayPlacements={},t||this.repaint()},removeOverlay:function(t,e){var n=this._jsPlumb.overlays[t];n&&(n.setVisible(!1),!e&&n.cleanup&&n.cleanup(),delete this._jsPlumb.overlays[t],this._jsPlumb.overlayPositions&&delete this._jsPlumb.overlayPositions[t],this._jsPlumb.overlayPlacements&&delete this._jsPlumb.overlayPlacements[t])},removeOverlays:function(){for(var t=0,e=arguments.length;t<e;t++)this.removeOverlay(arguments[t])},moveParent:function(t){if(this.bgCanvas&&(this.bgCanvas.parentNode.removeChild(this.bgCanvas),t.appendChild(this.bgCanvas)),this.canvas&&this.canvas.parentNode)for(var e in this.canvas.parentNode.removeChild(this.canvas),t.appendChild(this.canvas),this._jsPlumb.overlays)if(this._jsPlumb.overlays[e].isAppendedAtTopLevel){var n=this._jsPlumb.overlays[e].getElement();n.parentNode.removeChild(n),t.appendChild(n)}},getLabel:function(){var t=this.getOverlay(i);return null!=t?t.getLabel():null},getLabelOverlay:function(){return this.getOverlay(i)},setLabel:function(t){var e=this.getOverlay(i);if(e)t.constructor===String||t.constructor===Function?e.setLabel(t):(t.label&&e.setLabel(t.label),t.location&&e.setLocation(t.location));else{var n=t.constructor===String||t.constructor===Function?{label:t}:t;e=r(this,n),this._jsPlumb.overlays[i]=e}this._jsPlumb.instance.isSuspendDrawing()||this.repaint()},cleanup:function(t){for(var e in this._jsPlumb.overlays)this._jsPlumb.overlays[e].cleanup(t),this._jsPlumb.overlays[e].destroy(t);t&&(this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null)},setVisible:function(t){this[t?"showOverlays":"hideOverlays"]()},setAbsoluteOverlayPosition:function(t,e){this._jsPlumb.overlayPositions[t.id]=e},getAbsoluteOverlayPosition:function(t){return this._jsPlumb.overlayPositions?this._jsPlumb.overlayPositions[t.id]:null},_clazzManip:function(t,e,n){if(!n)for(var i in this._jsPlumb.overlays)this._jsPlumb.overlays[i][t+"Class"](e)},addClass:function(t,e){this._clazzManip("add",t,e)},removeClass:function(t,e){this._clazzManip("remove",t,e)}})}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=function(t,e,n){var i=!1;return{drag:function(){if(i)return i=!1,!0;if(e.element){var r=n.getUIPosition(arguments,n.getZoom());null!=r&&n.setPosition(e.element,r),n.repaint(e.element,r),t.paint({anchorPoint:t.anchor.getCurrentLocation({element:t})})}},stopDrag:function(){i=!0}}},r=function(t,e,n,i){var r=e.createElement("div",{position:"absolute"});e.appendElement(r);var o=e.getId(r);e.setPosition(r,n),r.style.width=i[0]+"px",r.style.height=i[1]+"px",e.manage(o,r,!0),t.id=o,t.element=r},o=function(t,n,i,r,o,s,a,u){var l=new e.FloatingAnchor({reference:n,referenceCanvas:r,jsPlumbInstance:s});return a({paintStyle:t,endpoint:i,anchor:l,source:o,scope:u})},s=["connectorStyle","connectorHoverStyle","connectorOverlays","connector","connectionType","connectorClass","connectorHoverClass"],a=function(t,e){var n=0;if(null!=e)for(var i=0;i<t.connections.length;i++)if(t.connections[i].sourceId===e||t.connections[i].targetId===e){n=i;break}return t.connections[n]};e.Endpoint=function(t){var u=t._jsPlumb,l=t.newConnection,c=t.newEndpoint;this.idPrefix="_jsplumb_e_",this.defaultLabelLocation=[.5,.5],this.defaultOverlayKeys=["Overlays","EndpointOverlays"],e.OverlayCapableJsPlumbUIComponent.apply(this,arguments),this.appendToDefaultType({connectionType:t.connectionType,maxConnections:null==t.maxConnections?this._jsPlumb.instance.Defaults.MaxConnections:t.maxConnections,paintStyle:t.endpointStyle||t.paintStyle||t.style||this._jsPlumb.instance.Defaults.EndpointStyle||e.Defaults.EndpointStyle,hoverPaintStyle:t.endpointHoverStyle||t.hoverPaintStyle||this._jsPlumb.instance.Defaults.EndpointHoverStyle||e.Defaults.EndpointHoverStyle,connectorStyle:t.connectorStyle,connectorHoverStyle:t.connectorHoverStyle,connectorClass:t.connectorClass,connectorHoverClass:t.connectorHoverClass,connectorOverlays:t.connectorOverlays,connector:t.connector,connectorTooltip:t.connectorTooltip}),this._jsPlumb.enabled=!(!1===t.enabled),this._jsPlumb.visible=!0,this.element=e.getElement(t.source),this._jsPlumb.uuid=t.uuid,this._jsPlumb.floatingEndpoint=null;var h=null;this._jsPlumb.uuid&&(t.endpointsByUUID[this._jsPlumb.uuid]=this),this.elementId=t.elementId,this.dragProxy=t.dragProxy,this._jsPlumb.connectionCost=t.connectionCost,this._jsPlumb.connectionsDirected=t.connectionsDirected,this._jsPlumb.currentAnchorClass="",this._jsPlumb.events={};var d=!0===t.deleteOnEmpty;this.setDeleteOnEmpty=function(t){d=t};var f=function(){var t=u.endpointAnchorClassPrefix+"-"+this._jsPlumb.currentAnchorClass;this._jsPlumb.currentAnchorClass=this.anchor.getCssClass();var n=u.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");this.removeClass(t),this.addClass(n),e.updateClasses(this.element,n,t)}.bind(this);this.prepareAnchor=function(t){var e=this._jsPlumb.instance.makeAnchor(t,this.elementId,u);return e.bind("anchorChanged",function(t){this.fire("anchorChanged",{endpoint:this,anchor:t}),f()}.bind(this)),e},this.setPreparedAnchor=function(t,e){return this._jsPlumb.instance.continuousAnchorFactory.clear(this.elementId),this.anchor=t,f(),e||this._jsPlumb.instance.repaint(this.elementId),this},this.setAnchor=function(t,e){var n=this.prepareAnchor(t);return this.setPreparedAnchor(n,e),this};var p=function(t){if(this.connections.length>0)for(var e=0;e<this.connections.length;e++)this.connections[e].setHover(t,!1);else this.setHover(t)}.bind(this);this.bind("mouseover",(function(){p(!0)})),this.bind("mouseout",(function(){p(!1)})),t._transient||this._jsPlumb.instance.router.addEndpoint(this,this.elementId),this.prepareEndpoint=function(i,r){var o,s=function(t,n){var i=u.getRenderMode();if(e.Endpoints[i][t])return new e.Endpoints[i][t](n);if(!u.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown endpoint type '"+t+"'"}},a={_jsPlumb:this._jsPlumb.instance,cssClass:t.cssClass,container:t.container,tooltip:t.tooltip,connectorTooltip:t.connectorTooltip,endpoint:this};return n.isString(i)?o=s(i,a):n.isArray(i)?(a=n.merge(i[1],a),o=s(i[0],a)):o=i.clone(),o.clone=function(){return n.isString(i)?s(i,a):n.isArray(i)?(a=n.merge(i[1],a),s(i[0],a)):void 0}.bind(this),o.typeId=r,o},this.setEndpoint=function(t,e){var n=this.prepareEndpoint(t);this.setPreparedEndpoint(n,!0)},this.setPreparedEndpoint=function(t,e){null!=this.endpoint&&(this.endpoint.cleanup(),this.endpoint.destroy()),this.endpoint=t,this.type=this.endpoint.type,this.canvas=this.endpoint.canvas},e.extend(this,t,s),this.isSource=t.isSource||!1,this.isTemporarySource=t.isTemporarySource||!1,this.isTarget=t.isTarget||!1,this.connections=t.connections||[],this.connectorPointerEvents=t["connector-pointer-events"],this.scope=t.scope||u.getDefaultScope(),this.timestamp=null,this.reattachConnections=t.reattach||u.Defaults.ReattachConnections,this.connectionsDetachable=u.Defaults.ConnectionsDetachable,!1!==t.connectionsDetachable&&!1!==t.detachable||(this.connectionsDetachable=!1),this.dragAllowedWhenFull=!1!==t.dragAllowedWhenFull,t.onMaxConnections&&this.bind("maxConnections",t.onMaxConnections),this.addConnection=function(t){this.connections.push(t),this[(this.connections.length>0?"add":"remove")+"Class"](u.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](u.endpointFullClass)},this.detachFromConnection=function(t,e,n){e=null==e?this.connections.indexOf(t):e,e>=0&&(this.connections.splice(e,1),this[(this.connections.length>0?"add":"remove")+"Class"](u.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](u.endpointFullClass)),!n&&d&&0===this.connections.length&&u.deleteObject({endpoint:this,fireEvent:!1,deleteAttachedObjects:!0!==n})},this.deleteEveryConnection=function(t){for(var e=this.connections.length,n=0;n<e;n++)u.deleteConnection(this.connections[0],t)},this.detachFrom=function(t,e,n){for(var i=[],r=0;r<this.connections.length;r++)this.connections[r].endpoints[1]!==t&&this.connections[r].endpoints[0]!==t||i.push(this.connections[r]);for(var o=0,s=i.length;o<s;o++)u.deleteConnection(i[0]);return this},this.getElement=function(){return this.element},this.setElement=function(i){var r=this._jsPlumb.instance.getId(i),o=this.elementId;return n.removeWithFunction(t.endpointsByElement[this.elementId],function(t){return t.id===this.id}.bind(this)),this.element=e.getElement(i),this.elementId=u.getId(this.element),u.router.rehomeEndpoint(this,o,this.element),u.dragManager.endpointAdded(this.element),n.addToList(t.endpointsByElement,r,this),this},this.makeInPlaceCopy=function(){var e=this.anchor.getCurrentLocation({element:this}),n=this.anchor.getOrientation(this),i=this.anchor.getCssClass(),r={bind:function(){},compute:function(){return[e[0],e[1]]},getCurrentLocation:function(){return[e[0],e[1]]},getOrientation:function(){return n},getCssClass:function(){return i}};return c({dropOptions:t.dropOptions,anchor:r,source:this.element,paintStyle:this.getPaintStyle(),endpoint:t.hideOnDrag?"Blank":this.endpoint,_transient:!0,scope:this.scope,reference:this})},this.connectorSelector=function(){return this.connections[0]},this.setStyle=this.setPaintStyle,this.paint=function(t){t=t||{};var e=t.timestamp,n=!(!1===t.recalc);if(!e||this.timestamp!==e){var i=u.updateOffset({elId:this.elementId,timestamp:e}),r=t.offset?t.offset.o:i.o;if(null!=r){var o=t.anchorPoint,s=t.connectorPaintStyle;if(null==o){var l=t.dimensions||i.s,c={xy:[r.left,r.top],wh:l,element:this,timestamp:e};if(n&&this.anchor.isDynamic&&this.connections.length>0){var h=a(this,t.elementWithPrecedence),d=h.endpoints[0]===this?1:0,f=0===d?h.sourceId:h.targetId,p=u.getCachedData(f),g=p.o,v=p.s;c.index=0===d?1:0,c.connection=h,c.txy=[g.left,g.top],c.twh=v,c.tElement=h.endpoints[d],c.tRotation=u.getRotation(f)}else this.connections.length>0&&(c.connection=this.connections[0]);c.rotation=u.getRotation(this.elementId),o=this.anchor.compute(c)}for(var m in this.endpoint.compute(o,this.anchor.getOrientation(this),this._jsPlumb.paintStyleInUse,s||this.paintStyleInUse),this.endpoint.paint(this._jsPlumb.paintStyleInUse,this.anchor),this.timestamp=e,this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(m)){var b=this._jsPlumb.overlays[m];b.isVisible()&&(this._jsPlumb.overlayPlacements[m]=b.draw(this.endpoint,this._jsPlumb.paintStyleInUse),b.paint(this._jsPlumb.overlayPlacements[m]))}}}},this.getTypeDescriptor=function(){return"endpoint"},this.isVisible=function(){return this._jsPlumb.visible},this.repaint=this.paint;var g=!1;this.initDraggable=function(){if(!g&&e.isDragSupported(this.element)){var s,a={id:null,element:null},d=null,f=!1,p=null,v=i(this,a,u),m=t.dragOptions||{},b={},y=e.dragEvents.start,x=e.dragEvents.stop,P=e.dragEvents.drag,_=e.dragEvents.beforeStart,C=function(t){s=t.e.payload||{}},E=function(n){d=this.connectorSelector();var i=!0;this.isEnabled()||(i=!1),null!=d||this.isSource||this.isTemporarySource||(i=!1),!this.isSource||!this.isFull()||null!=d&&this.dragAllowedWhenFull||(i=!1),null==d||d.isDetachable(this)||(this.isFull()?i=!1:d=null);var h=u.checkCondition(null==d?"beforeDrag":"beforeStartDetach",{endpoint:this,source:this.element,sourceId:this.elementId,connection:d});if(!1===h?i=!1:"object"===typeof h?e.extend(h,s||{}):h=s||{},!1===i)return u.stopDrag&&u.stopDrag(this.canvas),v.stopDrag(),!1;for(var g=0;g<this.connections.length;g++)this.connections[g].setHover(!1);this.addClass("endpointDrag"),u.setConnectionBeingDragged(!0),d&&!this.isFull()&&this.isSource&&(d=null),u.updateOffset({elId:this.elementId});var m=this._jsPlumb.instance.getOffset(this.canvas),b=this.canvas,y=this._jsPlumb.instance.getSize(this.canvas);r(a,u,m,y),u.setAttributes(this.canvas,{dragId:a.id,elId:this.elementId});var x=this.dragProxy||this.endpoint;if(null==this.dragProxy&&null!=this.connectionType){var P=this._jsPlumb.instance.deriveEndpointAndAnchorSpec(this.connectionType);P.endpoints[1]&&(x=P.endpoints[1])}var _=this._jsPlumb.instance.makeAnchor("Center");_.isFloating=!0,this._jsPlumb.floatingEndpoint=o(this.getPaintStyle(),_,x,this.canvas,a.element,u,c,this.scope);var C=this._jsPlumb.floatingEndpoint.anchor;if(null==d)this.setHover(!1,!1),d=l({sourceEndpoint:this,targetEndpoint:this._jsPlumb.floatingEndpoint,source:this.element,target:a.element,anchors:[this.anchor,this._jsPlumb.floatingEndpoint.anchor],paintStyle:t.connectorStyle,hoverPaintStyle:t.connectorHoverStyle,connector:t.connector,overlays:t.connectorOverlays,type:this.connectionType,cssClass:this.connectorClass,hoverClass:this.connectorHoverClass,scope:t.scope,data:h}),d.pending=!0,d.addClass(u.draggingClass),this._jsPlumb.floatingEndpoint.addClass(u.draggingClass),this._jsPlumb.floatingEndpoint.anchor=C,u.fire("connectionDrag",d),u.router.newConnection(d);else{f=!0,d.setHover(!1);var E=d.endpoints[0].id===this.id?0:1;this.detachFromConnection(d,null,!0);var j=u.getDragScope(b);u.setAttribute(this.canvas,"originalScope",j),u.fire("connectionDrag",d),0===E?(p=[d.source,d.sourceId,b,j],u.router.sourceOrTargetChanged(d.endpoints[E].elementId,a.id,d,a.element,0)):(p=[d.target,d.targetId,b,j],u.router.sourceOrTargetChanged(d.endpoints[E].elementId,a.id,d,a.element,1)),d.suspendedEndpoint=d.endpoints[E],d.suspendedElement=d.endpoints[E].getElement(),d.suspendedElementId=d.endpoints[E].elementId,d.suspendedElementType=0===E?"source":"target",d.suspendedEndpoint.setHover(!1),this._jsPlumb.floatingEndpoint.referenceEndpoint=d.suspendedEndpoint,d.endpoints[E]=this._jsPlumb.floatingEndpoint,d.addClass(u.draggingClass),this._jsPlumb.floatingEndpoint.addClass(u.draggingClass)}u.registerFloatingConnection(a,d,this._jsPlumb.floatingEndpoint),u.currentlyDragging=!0}.bind(this),j=function(){if(u.setConnectionBeingDragged(!1),d&&null!=d.endpoints){var t=u.getDropEvent(arguments),e=u.getFloatingAnchorIndex(d);if(d.endpoints[0===e?1:0].anchor.locked=!1,d.removeClass(u.draggingClass),this._jsPlumb&&(d.deleteConnectionNow||d.endpoints[e]===this._jsPlumb.floatingEndpoint)&&f&&d.suspendedEndpoint){0===e?(d.floatingElement=d.source,d.floatingId=d.sourceId,d.floatingEndpoint=d.endpoints[0],d.floatingIndex=0,d.source=p[0],d.sourceId=p[1]):(d.floatingElement=d.target,d.floatingId=d.targetId,d.floatingEndpoint=d.endpoints[1],d.floatingIndex=1,d.target=p[0],d.targetId=p[1]);var n=this._jsPlumb.floatingEndpoint;u.setDragScope(p[2],p[3]),d.endpoints[e]=d.suspendedEndpoint,d.isReattach()||d._forceReattach||d._forceDetach||!u.deleteConnection(d,{originalEvent:t})?(d.setHover(!1),d._forceDetach=null,d._forceReattach=null,this._jsPlumb.floatingEndpoint.detachFromConnection(d),d.suspendedEndpoint.addConnection(d),1===e?u.router.sourceOrTargetChanged(d.floatingId,d.targetId,d,d.target,e):u.router.sourceOrTargetChanged(d.floatingId,d.sourceId,d,d.source,e),u.repaint(p[1])):u.deleteObject({endpoint:n})}this.deleteAfterDragStop?u.deleteObject({endpoint:this}):this._jsPlumb&&this.paint({recalc:!1}),u.fire("connectionDragStop",d,t),d.pending&&u.fire("connectionAborted",d,t),u.currentlyDragging=!1,d.suspendedElement=null,d.suspendedEndpoint=null,d=null}a&&a.element&&u.remove(a.element,!1,!1),h&&u.deleteObject({endpoint:h}),this._jsPlumb&&(this.canvas.style.visibility="visible",this.anchor.locked=!1,this._jsPlumb.floatingEndpoint=null)}.bind(this);m=e.extend(b,m),m.scope=this.scope||m.scope,m[_]=n.wrap(m[_],C,!1),m[y]=n.wrap(m[y],E,!1),m[P]=n.wrap(m[P],v.drag),m[x]=n.wrap(m[x],j),m.multipleDrop=!1,m.canDrag=function(){return this.isSource||this.isTemporarySource||this.connections.length>0&&!1!==this.connectionsDetachable}.bind(this),u.initDraggable(this.canvas,m,"internal"),this.canvas._jsPlumbRelatedElement=this.element,g=!0}};var v=t.endpoint||this._jsPlumb.instance.Defaults.Endpoint||e.Defaults.Endpoint;this.setEndpoint(v,!0);var m=t.anchor?t.anchor:t.anchors?t.anchors:u.Defaults.Anchor||"Top";this.setAnchor(m,!0);var b=["default",t.type||""].join(" ");this.addType(b,t.data,!0),this.canvas=this.endpoint.canvas,this.canvas._jsPlumb=this,this.initDraggable();var y=function(i,r,o,s){if(e.isDropSupported(this.element)){var a=t.dropOptions||u.Defaults.DropOptions||e.Defaults.DropOptions;a=e.extend({},a),a.scope=a.scope||this.scope;var l=e.dragEvents.drop,c=e.dragEvents.over,h=e.dragEvents.out,d=this,f=u.EndpointDropHandler({getEndpoint:function(){return d},jsPlumb:u,enabled:function(){return null==o||o.isEnabled()},isFull:function(){return o.isFull()},element:this.element,elementId:this.elementId,isSource:this.isSource,isTarget:this.isTarget,addClass:function(t){d.addClass(t)},removeClass:function(t){d.removeClass(t)},isDropAllowed:function(){return d.isDropAllowed.apply(d,arguments)},reference:s,isRedrop:function(t,e){return t.suspendedEndpoint&&e.reference&&t.suspendedEndpoint.id===e.reference.id}});a[l]=n.wrap(a[l],f,!0),a[c]=n.wrap(a[c],function(){var t=e.getDragObject(arguments),n=u.getAttribute(e.getElement(t),"dragId"),i=u.getFloatingConnectionFor(n);if(null!=i){var r=u.getFloatingAnchorIndex(i),o=this.isTarget&&0!==r||i.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===i.suspendedEndpoint.id;if(o){var s=u.checkCondition("checkDropAllowed",{sourceEndpoint:i.endpoints[r],targetEndpoint:this,connection:i});this[(s?"add":"remove")+"Class"](u.endpointDropAllowedClass),this[(s?"remove":"add")+"Class"](u.endpointDropForbiddenClass),i.endpoints[r].anchor.over(this.anchor,this)}}}.bind(this)),a[h]=n.wrap(a[h],function(){var t=e.getDragObject(arguments),n=null==t?null:u.getAttribute(e.getElement(t),"dragId"),i=n?u.getFloatingConnectionFor(n):null;if(null!=i){var r=u.getFloatingAnchorIndex(i),o=this.isTarget&&0!==r||i.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===i.suspendedEndpoint.id;o&&(this.removeClass(u.endpointDropAllowedClass),this.removeClass(u.endpointDropForbiddenClass),i.endpoints[r].anchor.out())}}.bind(this)),u.initDroppable(i,a,"internal",r)}}.bind(this);return this.anchor.isFloating||y(this.canvas,!(t._transient||this.anchor.isFloating),this,t.reference),this},n.extend(e.Endpoint,e.OverlayCapableJsPlumbUIComponent,{setVisible:function(t,e,n){if(this._jsPlumb.visible=t,this.canvas&&(this.canvas.style.display=t?"block":"none"),this[t?"showOverlays":"hideOverlays"](),!e)for(var i=0;i<this.connections.length;i++)if(this.connections[i].setVisible(t),!n){var r=this===this.connections[i].endpoints[0]?1:0;1===this.connections[i].endpoints[r].connections.length&&this.connections[i].endpoints[r].setVisible(t,!0,!0)}},getAttachedElements:function(){return this.connections},applyType:function(t,n){this.setPaintStyle(t.endpointStyle||t.paintStyle,n),this.setHoverPaintStyle(t.endpointHoverStyle||t.hoverPaintStyle,n),null!=t.maxConnections&&(this._jsPlumb.maxConnections=t.maxConnections),t.scope&&(this.scope=t.scope),e.extend(this,t,s),null!=t.cssClass&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,t.cssClass),e.OverlayCapableJsPlumbUIComponent.applyType(this,t)},isEnabled:function(){return this._jsPlumb.enabled},setEnabled:function(t){this._jsPlumb.enabled=t},cleanup:function(){var t=this._jsPlumb.instance.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");e.removeClass(this.element,t),this.anchor=null,this.endpoint.cleanup(!0),this.endpoint.destroy(),this.endpoint=null,this._jsPlumb.instance.destroyDraggable(this.canvas,"internal"),this._jsPlumb.instance.destroyDroppable(this.canvas,"internal")},setHover:function(t){this.endpoint&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&this.endpoint.setHover(t)},isFull:function(){return 0===this._jsPlumb.maxConnections||!(this.isFloating()||this._jsPlumb.maxConnections<0||this.connections.length<this._jsPlumb.maxConnections)},isFloating:function(){return null!=this.anchor&&this.anchor.isFloating},isConnectedTo:function(t){var e=!1;if(t)for(var n=0;n<this.connections.length;n++)if(this.connections[n].endpoints[1]===t||this.connections[n].endpoints[0]===t){e=!0;break}return e},getConnectionCost:function(){return this._jsPlumb.connectionCost},setConnectionCost:function(t){this._jsPlumb.connectionCost=t},areConnectionsDirected:function(){return this._jsPlumb.connectionsDirected},setConnectionsDirected:function(t){this._jsPlumb.connectionsDirected=t},setElementId:function(t){this.elementId=t,this.anchor.elementId=t},setReferenceElement:function(t){this.element=e.getElement(t)},setDragAllowedWhenFull:function(t){this.dragAllowedWhenFull=t},equals:function(t){return this.anchor.equals(t.anchor)},getUuid:function(){return this._jsPlumb.uuid},computeAnchor:function(t){return this.anchor.compute(t)}}),t.jsPlumbInstance.prototype.EndpointDropHandler=function(t){return function(e){var i=t.jsPlumb;t.removeClass(i.endpointDropAllowedClass),t.removeClass(i.endpointDropForbiddenClass);var r=i.getDropEvent(arguments),o=i.getDragObject(arguments),s=i.getAttribute(o,"dragId"),a=(i.getAttribute(o,"elId"),i.getAttribute(o,"originalScope")),u=i.getFloatingConnectionFor(s);if(null!=u){var l=null!=u.suspendedEndpoint;if(!l||null!=u.suspendedEndpoint._jsPlumb){var c=t.getEndpoint(u);if(null!=c){if(t.isRedrop(u,t))return u._forceReattach=!0,u.setHover(!1),void(t.maybeCleanup&&t.maybeCleanup(c));var h=i.getFloatingAnchorIndex(u);if(0===h&&!t.isSource||1===h&&!t.isTarget)t.maybeCleanup&&t.maybeCleanup(c);else{t.onDrop&&t.onDrop(u),a&&i.setDragScope(o,a);var d=t.isFull(e);if(d&&c.fire("maxConnections",{endpoint:this,connection:u,maxConnections:c._jsPlumb.maxConnections},r),!d&&t.enabled()){var f=!0;0===h?(u.floatingElement=u.source,u.floatingId=u.sourceId,u.floatingEndpoint=u.endpoints[0],u.floatingIndex=0,u.source=t.element,u.sourceId=i.getId(t.element)):(u.floatingElement=u.target,u.floatingId=u.targetId,u.floatingEndpoint=u.endpoints[1],u.floatingIndex=1,u.target=t.element,u.targetId=i.getId(t.element)),l&&u.suspendedEndpoint.id!==c.id&&(u.isDetachAllowed(u)&&u.endpoints[h].isDetachAllowed(u)&&u.suspendedEndpoint.isDetachAllowed(u)&&i.checkCondition("beforeDetach",u)||(f=!1));var p=function(e){u.endpoints[h].detachFromConnection(u),u.suspendedEndpoint&&u.suspendedEndpoint.detachFromConnection(u),u.endpoints[h]=c,c.addConnection(u);var o=c.getParameters();for(var s in o)u.setParameter(s,o[s]);if(l){var a=u.suspendedEndpoint.elementId;i.fireMoveEvent({index:h,originalSourceId:0===h?a:u.sourceId,newSourceId:0===h?c.elementId:u.sourceId,originalTargetId:1===h?a:u.targetId,newTargetId:1===h?c.elementId:u.targetId,originalSourceEndpoint:0===h?u.suspendedEndpoint:u.endpoints[0],newSourceEndpoint:0===h?c:u.endpoints[0],originalTargetEndpoint:1===h?u.suspendedEndpoint:u.endpoints[1],newTargetEndpoint:1===h?c:u.endpoints[1],connection:u},r)}else o.draggable&&i.initDraggable(this.element,t.dragOptions,"internal",i);if(1===h?i.router.sourceOrTargetChanged(u.floatingId,u.targetId,u,u.target,1):i.router.sourceOrTargetChanged(u.floatingId,u.sourceId,u,u.source,0),u.endpoints[0].finalEndpoint){var d=u.endpoints[0];d.detachFromConnection(u),u.endpoints[0]=u.endpoints[0].finalEndpoint,u.endpoints[0].addConnection(u)}n.isObject(e)&&u.mergeData(e),i.finaliseConnection(u,null,r,!1),u.setHover(!1),i.revalidate(u.endpoints[0].element)}.bind(this),g=function(){u.suspendedEndpoint&&(u.endpoints[h]=u.suspendedEndpoint,u.setHover(!1),u._forceDetach=!0,0===h?(u.source=u.suspendedEndpoint.element,u.sourceId=u.suspendedEndpoint.elementId):(u.target=u.suspendedEndpoint.element,u.targetId=u.suspendedEndpoint.elementId),u.suspendedEndpoint.addConnection(u),1===h?i.router.sourceOrTargetChanged(u.floatingId,u.targetId,u,u.target,1):i.router.sourceOrTargetChanged(u.floatingId,u.sourceId,u,u.source,0),i.repaint(u.sourceId),u._forceDetach=!1)};if(f=f&&t.isDropAllowed(u.sourceId,u.targetId,u.scope,u,c),f)return p(f),!0;g()}t.maybeCleanup&&t.maybeCleanup(c),i.currentlyDragging=!1}}}}}}}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=function(t,i,r,o,s){if(e.Connectors[i]=e.Connectors[i]||{},null==e.Connectors[i][r]){if(null==e.Connectors[r]){if(t.Defaults.DoNotThrowErrors)return null;throw new TypeError("jsPlumb: unknown connector type '"+r+"'")}e.Connectors[i][r]=function(){e.Connectors[r].apply(this,arguments),e.ConnectorRenderers[i].apply(this,arguments)},n.extend(e.Connectors[i][r],[e.Connectors[r],e.ConnectorRenderers[i]])}return new e.Connectors[i][r](o,s)},r=function(t,e,n){return t?n.makeAnchor(t,e,n):null},o=function(t,e,i,r){null!=e&&(e._jsPlumbConnections=e._jsPlumbConnections||{},r?delete e._jsPlumbConnections[t.id]:e._jsPlumbConnections[t.id]=!0,n.isEmpty(e._jsPlumbConnections)?i.removeClass(e,i.connectedClass):i.addClass(e,i.connectedClass))};e.Connection=function(t){var i=t.newEndpoint;this.id=t.id,this.connector=null,this.idPrefix="_jsplumb_c_",this.defaultLabelLocation=.5,this.defaultOverlayKeys=["Overlays","ConnectionOverlays"],this.previousConnection=t.previousConnection,this.source=e.getElement(t.source),this.target=e.getElement(t.target),e.OverlayCapableJsPlumbUIComponent.apply(this,arguments),t.sourceEndpoint?(this.source=t.sourceEndpoint.getElement(),this.sourceId=t.sourceEndpoint.elementId):this.sourceId=this._jsPlumb.instance.getId(this.source),t.targetEndpoint?(this.target=t.targetEndpoint.getElement(),this.targetId=t.targetEndpoint.elementId):this.targetId=this._jsPlumb.instance.getId(this.target),this.scope=t.scope,this.endpoints=[],this.endpointStyles=[];var r=this._jsPlumb.instance;r.manage(this.sourceId,this.source),r.manage(this.targetId,this.target),this._jsPlumb.visible=!0,this._jsPlumb.params={cssClass:t.cssClass,container:t.container,"pointer-events":t["pointer-events"],editorParams:t.editorParams,overlays:t.overlays},this._jsPlumb.lastPaintedAt=null,this.bind("mouseover",function(){this.setHover(!0)}.bind(this)),this.bind("mouseout",function(){this.setHover(!1)}.bind(this)),this.makeEndpoint=function(e,n,o,s,a){return o=o||this._jsPlumb.instance.getId(n),this.prepareEndpoint(r,i,this,s,e?0:1,t,n,o,a)},t.type&&(t.endpoints=t.endpoints||this._jsPlumb.instance.deriveEndpointAndAnchorSpec(t.type).endpoints);var o=this.makeEndpoint(!0,this.source,this.sourceId,t.sourceEndpoint),s=this.makeEndpoint(!1,this.target,this.targetId,t.targetEndpoint);o&&n.addToList(t.endpointsByElement,this.sourceId,o),s&&n.addToList(t.endpointsByElement,this.targetId,s),this.scope||(this.scope=this.endpoints[0].scope),null!=t.deleteEndpointsOnEmpty&&(this.endpoints[0].setDeleteOnEmpty(t.deleteEndpointsOnEmpty),this.endpoints[1].setDeleteOnEmpty(t.deleteEndpointsOnEmpty));var a=r.Defaults.ConnectionsDetachable;!1===t.detachable&&(a=!1),!1===this.endpoints[0].connectionsDetachable&&(a=!1),!1===this.endpoints[1].connectionsDetachable&&(a=!1);var u=t.reattach||this.endpoints[0].reattachConnections||this.endpoints[1].reattachConnections||r.Defaults.ReattachConnections;this.appendToDefaultType({detachable:a,reattach:u,paintStyle:this.endpoints[0].connectorStyle||this.endpoints[1].connectorStyle||t.paintStyle||r.Defaults.PaintStyle||e.Defaults.PaintStyle,hoverPaintStyle:this.endpoints[0].connectorHoverStyle||this.endpoints[1].connectorHoverStyle||t.hoverPaintStyle||r.Defaults.HoverPaintStyle||e.Defaults.HoverPaintStyle});var l=r.getSuspendedAt();if(!r.isSuspendDrawing()){var c=r.getCachedData(this.sourceId),h=c.o,d=c.s,f=r.getCachedData(this.targetId),p=f.o,g=f.s,v=l||jsPlumbUtil.uuid(),m=this.endpoints[0].anchor.compute({xy:[h.left,h.top],wh:d,element:this.endpoints[0],elementId:this.endpoints[0].elementId,txy:[p.left,p.top],twh:g,tElement:this.endpoints[1],timestamp:v,rotation:r.getRotation(this.endpoints[0].elementId)});this.endpoints[0].paint({anchorLoc:m,timestamp:v}),m=this.endpoints[1].anchor.compute({xy:[p.left,p.top],wh:g,element:this.endpoints[1],elementId:this.endpoints[1].elementId,txy:[h.left,h.top],twh:d,tElement:this.endpoints[0],timestamp:v,rotation:r.getRotation(this.endpoints[1].elementId)}),this.endpoints[1].paint({anchorLoc:m,timestamp:v})}this.getTypeDescriptor=function(){return"connection"},this.getAttachedElements=function(){return this.endpoints},this.isDetachable=function(t){return!1!==this._jsPlumb.detachable&&(null!=t?!0===t.connectionsDetachable:!0===this._jsPlumb.detachable)},this.setDetachable=function(t){this._jsPlumb.detachable=!0===t},this.isReattach=function(){return!0===this._jsPlumb.reattach||!0===this.endpoints[0].reattachConnections||!0===this.endpoints[1].reattachConnections},this.setReattach=function(t){this._jsPlumb.reattach=!0===t},this._jsPlumb.cost=t.cost||this.endpoints[0].getConnectionCost(),this._jsPlumb.directed=t.directed,null==t.directed&&(this._jsPlumb.directed=this.endpoints[0].areConnectionsDirected());var b=e.extend({},this.endpoints[1].getParameters());e.extend(b,this.endpoints[0].getParameters()),e.extend(b,this.getParameters()),this.setParameters(b),this.setConnector(this.endpoints[0].connector||this.endpoints[1].connector||t.connector||r.Defaults.Connector||e.Defaults.Connector,!0);var y=null!=t.data&&n.isObject(t.data)?t.data:{};this.getData=function(){return y},this.setData=function(t){y=t||{}},this.mergeData=function(t){y=e.extend(y,t)};var x=["default",this.endpoints[0].connectionType,this.endpoints[1].connectionType,t.type].join(" ");/[^\s]/.test(x)&&this.addType(x,t.data,!0),this.updateConnectedClass()},n.extend(e.Connection,e.OverlayCapableJsPlumbUIComponent,{applyType:function(t,n,i){var r=null;null!=t.connector&&(r=this.getCachedTypeItem("connector",i.connector),null==r&&(r=this.prepareConnector(t.connector,i.connector),this.cacheTypeItem("connector",r,i.connector)),this.setPreparedConnector(r)),null!=t.detachable&&this.setDetachable(t.detachable),null!=t.reattach&&this.setReattach(t.reattach),t.scope&&(this.scope=t.scope),null!=t.cssClass&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,t.cssClass);var o=null;t.anchor?(o=this.getCachedTypeItem("anchors",i.anchor),null==o&&(o=[this._jsPlumb.instance.makeAnchor(t.anchor),this._jsPlumb.instance.makeAnchor(t.anchor)],this.cacheTypeItem("anchors",o,i.anchor))):t.anchors&&(o=this.getCachedTypeItem("anchors",i.anchors),null==o&&(o=[this._jsPlumb.instance.makeAnchor(t.anchors[0]),this._jsPlumb.instance.makeAnchor(t.anchors[1])],this.cacheTypeItem("anchors",o,i.anchors))),null!=o&&(this.endpoints[0].anchor=o[0],this.endpoints[1].anchor=o[1],this.endpoints[1].anchor.isDynamic&&this._jsPlumb.instance.repaint(this.endpoints[1].elementId)),e.OverlayCapableJsPlumbUIComponent.applyType(this,t)},addClass:function(t,e){e&&(this.endpoints[0].addClass(t),this.endpoints[1].addClass(t),this.suspendedEndpoint&&this.suspendedEndpoint.addClass(t)),this.connector&&this.connector.addClass(t)},removeClass:function(t,e){e&&(this.endpoints[0].removeClass(t),this.endpoints[1].removeClass(t),this.suspendedEndpoint&&this.suspendedEndpoint.removeClass(t)),this.connector&&this.connector.removeClass(t)},isVisible:function(){return this._jsPlumb.visible},setVisible:function(t){this._jsPlumb.visible=t,this.connector&&this.connector.setVisible(t),this.repaint()},cleanup:function(){this.updateConnectedClass(!0),this.endpoints=null,this.source=null,this.target=null,null!=this.connector&&(this.connector.cleanup(!0),this.connector.destroy(!0)),this.connector=null},updateConnectedClass:function(t){this._jsPlumb&&(o(this,this.source,this._jsPlumb.instance,t),o(this,this.target,this._jsPlumb.instance,t))},setHover:function(e){this.connector&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&(this.connector.setHover(e),t.jsPlumb[e?"addClass":"removeClass"](this.source,this._jsPlumb.instance.hoverSourceClass),t.jsPlumb[e?"addClass":"removeClass"](this.target,this._jsPlumb.instance.hoverTargetClass))},getUuids:function(){return[this.endpoints[0].getUuid(),this.endpoints[1].getUuid()]},getCost:function(){return this._jsPlumb?this._jsPlumb.cost:-1/0},setCost:function(t){this._jsPlumb.cost=t},isDirected:function(){return this._jsPlumb.directed},getConnector:function(){return this.connector},prepareConnector:function(t,e){var r,o={_jsPlumb:this._jsPlumb.instance,cssClass:this._jsPlumb.params.cssClass,container:this._jsPlumb.params.container,"pointer-events":this._jsPlumb.params["pointer-events"]},s=this._jsPlumb.instance.getRenderMode();return n.isString(t)?r=i(this._jsPlumb.instance,s,t,o,this):n.isArray(t)&&(r=1===t.length?i(this._jsPlumb.instance,s,t[0],o,this):i(this._jsPlumb.instance,s,t[0],n.merge(t[1],o),this)),null!=e&&(r.typeId=e),r},setPreparedConnector:function(t,e,n,i){if(this.connector!==t){var r,o="";if(null!=this.connector&&(r=this.connector,o=r.getClass(),this.connector.cleanup(),this.connector.destroy()),this.connector=t,i&&this.cacheTypeItem("connector",t,i),this.canvas=this.connector.canvas,this.bgCanvas=this.connector.bgCanvas,this.connector.reattach(this._jsPlumb.instance),this.addClass(o),this.canvas&&(this.canvas._jsPlumb=this),this.bgCanvas&&(this.bgCanvas._jsPlumb=this),null!=r)for(var s=this.getOverlays(),a=0;a<s.length;a++)s[a].transfer&&s[a].transfer(this.connector);n||this.setListenerComponent(this.connector),e||this.repaint()}},setConnector:function(t,e,n,i){var r=this.prepareConnector(t,i);this.setPreparedConnector(r,e,n,i)},paint:function(t){if(!this._jsPlumb.instance.isSuspendDrawing()&&this._jsPlumb.visible){t=t||{};var e=t.timestamp,n=!1,i=n?this.sourceId:this.targetId,r=n?this.targetId:this.sourceId,o=n?0:1,s=n?1:0;if(null==e||e!==this._jsPlumb.lastPaintedAt){var a=this._jsPlumb.instance.updateOffset({elId:r}).o,u=this._jsPlumb.instance.updateOffset({elId:i}).o,l=this.endpoints[s],c=this.endpoints[o],h=l.anchor.getCurrentLocation({xy:[a.left,a.top],wh:[a.width,a.height],element:l,timestamp:e,rotation:this._jsPlumb.instance.getRotation(this.sourceId)}),d=c.anchor.getCurrentLocation({xy:[u.left,u.top],wh:[u.width,u.height],element:c,timestamp:e,rotation:this._jsPlumb.instance.getRotation(this.targetId)});this.connector.resetBounds(),this.connector.compute({sourcePos:h,targetPos:d,sourceOrientation:l.anchor.getOrientation(l),targetOrientation:c.anchor.getOrientation(c),sourceEndpoint:this.endpoints[s],targetEndpoint:this.endpoints[o],"stroke-width":this._jsPlumb.paintStyleInUse.strokeWidth,sourceInfo:a,targetInfo:u});var f={minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0};for(var p in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(p)){var g=this._jsPlumb.overlays[p];g.isVisible()&&(this._jsPlumb.overlayPlacements[p]=g.draw(this.connector,this._jsPlumb.paintStyleInUse,this.getAbsoluteOverlayPosition(g)),f.minX=Math.min(f.minX,this._jsPlumb.overlayPlacements[p].minX),f.maxX=Math.max(f.maxX,this._jsPlumb.overlayPlacements[p].maxX),f.minY=Math.min(f.minY,this._jsPlumb.overlayPlacements[p].minY),f.maxY=Math.max(f.maxY,this._jsPlumb.overlayPlacements[p].maxY))}var v=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||1)/2,m=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||0),b={xmin:Math.min(this.connector.bounds.minX-(v+m),f.minX),ymin:Math.min(this.connector.bounds.minY-(v+m),f.minY),xmax:Math.max(this.connector.bounds.maxX+(v+m),f.maxX),ymax:Math.max(this.connector.bounds.maxY+(v+m),f.maxY)};for(var y in this.connector.paintExtents=b,this.connector.paint(this._jsPlumb.paintStyleInUse,null,b),this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(y)){var x=this._jsPlumb.overlays[y];x.isVisible()&&x.paint(this._jsPlumb.overlayPlacements[y],b)}}this._jsPlumb.lastPaintedAt=e}},repaint:function(t){var e=jsPlumb.extend(t||{},{});e.elId=this.sourceId,this.paint(e)},prepareEndpoint:function(t,n,i,o,s,a,u,l,c){var h;if(o)i.endpoints[s]=o,o.addConnection(i);else{a.endpoints||(a.endpoints=[null,null]);var d=c||a.endpoints[s]||a.endpoint||t.Defaults.Endpoints[s]||e.Defaults.Endpoints[s]||t.Defaults.Endpoint||e.Defaults.Endpoint;a.endpointStyles||(a.endpointStyles=[null,null]),a.endpointHoverStyles||(a.endpointHoverStyles=[null,null]);var f=a.endpointStyles[s]||a.endpointStyle||t.Defaults.EndpointStyles[s]||e.Defaults.EndpointStyles[s]||t.Defaults.EndpointStyle||e.Defaults.EndpointStyle;null==f.fill&&null!=a.paintStyle&&(f.fill=a.paintStyle.stroke),null==f.outlineStroke&&null!=a.paintStyle&&(f.outlineStroke=a.paintStyle.outlineStroke),null==f.outlineWidth&&null!=a.paintStyle&&(f.outlineWidth=a.paintStyle.outlineWidth);var p=a.endpointHoverStyles[s]||a.endpointHoverStyle||t.Defaults.EndpointHoverStyles[s]||e.Defaults.EndpointHoverStyles[s]||t.Defaults.EndpointHoverStyle||e.Defaults.EndpointHoverStyle;null!=a.hoverPaintStyle&&(null==p&&(p={}),null==p.fill&&(p.fill=a.hoverPaintStyle.stroke));var g=a.anchors?a.anchors[s]:a.anchor?a.anchor:r(t.Defaults.Anchors[s],l,t)||r(e.Defaults.Anchors[s],l,t)||r(t.Defaults.Anchor,l,t)||r(e.Defaults.Anchor,l,t),v=a.uuids?a.uuids[s]:null;h=n({paintStyle:f,hoverPaintStyle:p,endpoint:d,connections:[i],uuid:v,anchor:g,source:u,scope:a.scope,reattach:a.reattach||t.Defaults.ReattachConnections,detachable:a.detachable||t.Defaults.ConnectionsDetachable}),null==o&&h.setDeleteOnEmpty(!0),i.endpoints[s]=h,!1===a.drawEndpoints&&h.setVisible(!1,!0,!0)}return h},replaceEndpoint:function(t,e){var n=this.endpoints[t],i=n.elementId,r=this._jsPlumb.instance.getEndpoints(i),o=r.indexOf(n),s=this.makeEndpoint(0===t,n.element,i,null,e);this.endpoints[t]=s,r.splice(o,1,s),this._jsPlumb.instance.deleteObject({endpoint:n,deleteAttachedObjects:!1}),this._jsPlumb.instance.fire("endpointReplaced",{previous:n,current:s}),this._jsPlumb.instance.router.sourceOrTargetChanged(this.endpoints[1].elementId,this.endpoints[1].elementId,this,this.endpoints[1].element,1)}})}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumbUtil,n=t.jsPlumb;n.AnchorManager=function(t){var i={},r={},o={},s={},a=this,u={},l=t.jsPlumbInstance,c={},h=function(t,e,n,i,r,o,s,a){for(var u=[],l=e[r?0:1]/(i.length+1),c=0;c<i.length;c++){var h=(c+1)*l,d=o*e[r?1:0];s&&(h=e[r?0:1]-h);var f=r?h:d,p=n.left+f,g=f/e[0],v=r?d:h,m=n.top+v,b=v/e[1];if(0!==a){var y=jsPlumbUtil.rotatePoint([p,m],[n.centerx,n.centery],a);p=y[0],m=y[1]}u.push([p,m,g,b,i[c][1],i[c][2]])}return u},d=function(t,e){return e[0][0]-t[0][0]},f=function(t,e){var n=t[0][0]<0?-Math.PI-t[0][0]:Math.PI-t[0][0],i=e[0][0]<0?-Math.PI-e[0][0]:Math.PI-e[0][0];return n-i},p={top:f,right:d,bottom:d,left:f},g=function(t,e){return t.sort(e)},v=function(t,e){var n=l.getCachedData(t),i=n.s,s=n.o,a=function(e,n,i,s,a,u,c){if(s.length>0)for(var d=g(s,p[e]),f="right"===e||"top"===e,v=l.getRotation(t),m=h(e,n,i,d,a,u,f,v),b=function(t,e){r[t.id]=[e[0],e[1],e[2],e[3]],o[t.id]=c},y=0;y<m.length;y++){var x=m[y][4],P=x.endpoints[0].elementId===t,_=x.endpoints[1].elementId===t;P&&b(x.endpoints[0],m[y]),_&&b(x.endpoints[1],m[y])}};a("bottom",i,s,e.bottom,!0,1,[0,1]),a("top",i,s,e.top,!0,0,[0,-1]),a("left",i,s,e.left,!1,0,[-1,0]),a("right",i,s,e.right,!1,1,[1,0])};this.reset=function(){i={},s={},u={}},this.addFloatingConnection=function(t,e){c[t]=e},this.newConnection=function(t){var i=t.sourceId,r=t.targetId,o=t.endpoints,a=!0,u=function(u,l,c,h,d){i===r&&c.isContinuous&&(t._jsPlumb.instance.removeElement(o[1].canvas),a=!1),e.addToList(s,h,[d,l,c.constructor===n.DynamicAnchor])};u(0,o[0],o[0].anchor,r,t),a&&u(1,o[1],o[1].anchor,i,t)};var m=function(t){(function(t,n){if(t){var i=function(t){return t[4]===n};e.removeWithFunction(t.top,i),e.removeWithFunction(t.left,i),e.removeWithFunction(t.bottom,i),e.removeWithFunction(t.right,i)}})(u[t.elementId],t.id)};this.connectionDetached=function(t,n){var i=t.connection||t,r=t.sourceId,o=t.targetId,u=i.endpoints,l=function(t,n,i,r,o){e.removeWithFunction(s[r],(function(t){return t[0].id===o.id}))};l(1,u[1],u[1].anchor,r,i),l(0,u[0],u[0].anchor,o,i),i.floatingId&&(l(i.floatingIndex,i.floatingEndpoint,i.floatingEndpoint.anchor,i.floatingId,i),m(i.floatingEndpoint)),m(i.endpoints[0]),m(i.endpoints[1]),n||(a.redraw(i.sourceId),i.targetId!==i.sourceId&&a.redraw(i.targetId))},this.addEndpoint=function(t,n){e.addToList(i,n,t)},this.changeId=function(t,e){s[e]=s[t],i[e]=i[t],delete s[t],delete i[t]},this.getConnectionsFor=function(t){return s[t]||[]},this.getEndpointsFor=function(t){return i[t]||[]},this.deleteEndpoint=function(t){e.removeWithFunction(i[t.elementId],(function(e){return e.id===t.id})),m(t)},this.elementRemoved=function(t){delete c[t],delete i[t],i[t]=[]};var b=function(n,i,r,o,s,a,u,l,c,h,d,f){var p,g,v=-1,m=-1,b=o.endpoints[u],y=b.id,x=[1,0][u],P=[[i,r],o,s,a,y],_=n[c],C=b._continuousAnchorEdge?n[b._continuousAnchorEdge]:null;if(C){var E=e.findWithFunction(C,(function(t){return t[4]===y}));if(-1!==E)for(C.splice(E,1),p=0;p<C.length;p++)g=C[p][1],e.addWithFunction(d,g,(function(t){return t.id===g.id})),e.addWithFunction(f,C[p][1].endpoints[u],(function(t){return t.id===g.endpoints[u].id})),e.addWithFunction(f,C[p][1].endpoints[x],(function(t){return t.id===g.endpoints[x].id}))}for(p=0;p<_.length;p++)g=_[p][1],1===t.idx&&_[p][3]===a&&-1===m&&(m=p),e.addWithFunction(d,g,(function(t){return t.id===g.id})),e.addWithFunction(f,_[p][1].endpoints[u],(function(t){return t.id===g.endpoints[u].id})),e.addWithFunction(f,_[p][1].endpoints[x],(function(t){return t.id===g.endpoints[x].id}));if(-1!==v)_[v]=P;else{var j=l?-1!==m?m:0:_.length;_.splice(j,0,P)}b._continuousAnchorEdge=c};this.sourceOrTargetChanged=function(t,i,r,o,a){if(0===a){if(t!==i){r.sourceId=i,r.source=o,e.removeWithFunction(s[t],(function(t){return t[0].id===r.id}));var u=e.findWithFunction(s[r.targetId],(function(t){return t[0].id===r.id}));u>-1&&(s[r.targetId][u][0]=r,s[r.targetId][u][1]=r.endpoints[0],s[r.targetId][u][2]=r.endpoints[0].anchor.constructor===n.DynamicAnchor),e.addToList(s,i,[r,r.endpoints[1],r.endpoints[1].anchor.constructor===n.DynamicAnchor]),r.endpoints[1].anchor.isContinuous&&(r.source===r.target?r._jsPlumb.instance.removeElement(r.endpoints[1].canvas):null==r.endpoints[1].canvas.parentNode&&r._jsPlumb.instance.appendElement(r.endpoints[1].canvas)),r.updateConnectedClass()}}else if(1===a){var l=r.endpoints[0].elementId;r.target=o,r.targetId=i;var c=e.findWithFunction(s[l],(function(t){return t[0].id===r.id})),h=e.findWithFunction(s[t],(function(t){return t[0].id===r.id}));-1!==c&&(s[l][c][0]=r,s[l][c][1]=r.endpoints[1],s[l][c][2]=r.endpoints[1].anchor.constructor===n.DynamicAnchor),h>-1&&(s[t].splice(h,1),e.addToList(s,i,[r,r.endpoints[0],r.endpoints[0].anchor.constructor===n.DynamicAnchor])),r.updateConnectedClass()}},this.rehomeEndpoint=function(t,e,n){var r=i[e]||[],o=l.getId(n);if(o!==e){var s=r.indexOf(t);if(s>-1){var u=r.splice(s,1)[0];a.add(u,o)}}for(var c=0;c<t.connections.length;c++)t.connections[c].sourceId===e?a.sourceOrTargetChanged(e,t.elementId,t.connections[c],t.element,0):t.connections[c].targetId===e&&a.sourceOrTargetChanged(e,t.elementId,t.connections[c],t.element,1)},this.redraw=function(t,r,o,a,h,d){var f=[],p=[],g=[];if(!l.isSuspendDrawing()){var m=i[t]||[],y=s[t]||[];o=o||jsPlumbUtil.uuid(),a=a||{left:0,top:0},r&&(r={left:r.left+a.left,top:r.top+a.top});for(var x=l.updateOffset({elId:t,offset:r,recalc:!1,timestamp:o}),P={},_=0;_<y.length;_++){var C=y[_][0],E=C.sourceId,j=C.targetId,S=C.endpoints[0].anchor.isContinuous,w=C.endpoints[1].anchor.isContinuous;if(S||w){var D=E+"_"+j,A=P[D],O=C.sourceId===t?1:0,I=l.getRotation(j),k=l.getRotation(E);S&&!u[E]&&(u[E]={top:[],right:[],bottom:[],left:[]}),w&&!u[j]&&(u[j]={top:[],right:[],bottom:[],left:[]}),t!==j&&l.updateOffset({elId:j,timestamp:o}),t!==E&&l.updateOffset({elId:E,timestamp:o});var T=l.getCachedData(j),M=l.getCachedData(E);j===E&&(S||w)?(b(u[E],-Math.PI/2,0,C,!1,j,0,!1,"top",E,f,p),b(u[j],-Math.PI/2,0,C,!1,E,1,!1,"top",j,f,p)):(A||(A=this.calculateOrientation(E,j,M.o,T.o,C.endpoints[0].anchor,C.endpoints[1].anchor,C,k,I),P[D]=A),S&&b(u[E],A.theta,0,C,!1,j,0,!1,A.a[0],E,f,p),w&&b(u[j],A.theta2,-1,C,!0,E,1,!0,A.a[1],j,f,p)),S&&e.addWithFunction(g,E,(function(t){return t===E})),w&&e.addWithFunction(g,j,(function(t){return t===j})),e.addWithFunction(f,C,(function(t){return t.id===C.id})),(S&&0===O||w&&1===O)&&e.addWithFunction(p,C.endpoints[O],(function(t){return t.id===C.endpoints[O].id}))}}for(_=0;_<m.length;_++)0===m[_].connections.length&&m[_].anchor.isContinuous&&(u[t]||(u[t]={top:[],right:[],bottom:[],left:[]}),b(u[t],-Math.PI/2,0,{endpoints:[m[_],m[_]],paint:function(){}},!1,t,0,!1,m[_].anchor.getDefaultFace(),t,f,p),e.addWithFunction(g,t,(function(e){return e===t})));for(_=0;_<g.length;_++)v(g[_],u[g[_]]);for(_=0;_<m.length;_++)m[_].paint({timestamp:o,offset:x,dimensions:x.s,recalc:!0!==d});for(_=0;_<p.length;_++){var L=l.getCachedData(p[_].elementId);p[_].paint({timestamp:null,offset:L,dimensions:L.s})}for(_=0;_<y.length;_++){var F=y[_][1];if(F.anchor.constructor===n.DynamicAnchor){F.paint({elementWithPrecedence:t,timestamp:o}),e.addWithFunction(f,y[_][0],(function(t){return t.id===y[_][0].id}));for(var R=0;R<F.connections.length;R++)F.connections[R]!==y[_][0]&&e.addWithFunction(f,F.connections[R],(function(t){return t.id===F.connections[R].id}))}else e.addWithFunction(f,y[_][0],(function(t){return t.id===y[_][0].id}))}var N=c[t];for(N&&N.paint({timestamp:o,recalc:!1,elId:t}),_=0;_<f.length;_++)f[_].paint({elId:t,timestamp:null,recalc:!1,clearEdits:h})}return{c:f,e:p}};var y=function(t){e.EventGenerator.apply(this),this.type="Continuous",this.isDynamic=!0,this.isContinuous=!0;for(var n=t.faces||["top","right","bottom","left"],i=!(!1===t.clockwise),s={},a={top:"bottom",right:"left",left:"right",bottom:"top"},u={top:"right",right:"bottom",left:"top",bottom:"left"},l={top:"left",right:"top",left:"bottom",bottom:"right"},c=i?u:l,h=i?l:u,d=t.cssClass||"",f=null,p=null,g=["left","right"],v=["top","bottom"],m=null,b=0;b<n.length;b++)s[n[b]]=!0;this.getDefaultFace=function(){return 0===n.length?"top":n[0]},this.isRelocatable=function(){return!0},this.isSnapOnRelocate=function(){return!0},this.verifyEdge=function(t){return s[t]?t:s[a[t]]?a[t]:s[c[t]]?c[t]:s[h[t]]?h[t]:t},this.isEdgeSupported=function(t){return null==m?null==p?!0===s[t]:p===t:-1!==m.indexOf(t)},this.setCurrentFace=function(t,e){f=t,e&&null!=p&&(p=f)},this.getCurrentFace=function(){return f},this.getSupportedFaces=function(){var t=[];for(var e in s)s[e]&&t.push(e);return t},this.lock=function(){p=f},this.unlock=function(){p=null},this.isLocked=function(){return null!=p},this.lockCurrentAxis=function(){null!=f&&(m="left"===f||"right"===f?g:v)},this.unlockCurrentAxis=function(){m=null},this.compute=function(t){return r[t.element.id]||[0,0]},this.getCurrentLocation=function(t){return r[t.element.id]||[0,0]},this.getOrientation=function(t){return o[t.id]||[0,0]},this.getCssClass=function(){return d}};l.continuousAnchorFactory={get:function(t){return new y(t)},clear:function(t){delete r[t]}}},n.AnchorManager.prototype.calculateOrientation=function(t,e,n,i,r,o,s,a,u){var l={HORIZONTAL:"horizontal",VERTICAL:"vertical",DIAGONAL:"diagonal",IDENTITY:"identity"},c=["left","top","right","bottom"];if(t===e)return{orientation:l.IDENTITY,a:["top","top"]};var h=Math.atan2(i.centery-n.centery,i.centerx-n.centerx),d=Math.atan2(n.centery-i.centery,n.centerx-i.centerx),f=[],p={};(function(t,e){for(var n=0;n<t.length;n++)if(p[t[n]]={left:[e[n][0].left,e[n][0].centery],right:[e[n][0].right,e[n][0].centery],top:[e[n][0].centerx,e[n][0].top],bottom:[e[n][0].centerx,e[n][0].bottom]},0!==e[n][1])for(var i in p[t[n]])p[t[n]][i]=jsPlumbUtil.rotatePoint(p[t[n]][i],[e[n][0].centerx,e[n][0].centery],e[n][1])})(["source","target"],[[n,a],[i,u]]);for(var g=0;g<c.length;g++)for(var v=0;v<c.length;v++)f.push({source:c[g],target:c[v],dist:Biltong.lineLength(p.source[c[g]],p.target[c[v]])});f.sort((function(t,e){return t.dist<e.dist?-1:t.dist>e.dist?1:0}));for(var m=f[0].source,b=f[0].target,y=0;y<f.length;y++)if(m=r.isContinuous&&r.locked?r.getCurrentFace():!r.isContinuous||r.isEdgeSupported(f[y].source)?f[y].source:null,b=o.isContinuous&&o.locked?o.getCurrentFace():!o.isContinuous||o.isEdgeSupported(f[y].target)?f[y].target:null,null!=m&&null!=b)break;return r.isContinuous&&r.setCurrentFace(m),o.isContinuous&&o.setCurrentFace(b),{a:[m,b],theta:h,theta2:d}},n.Anchor=function(t){this.x=t.x||0,this.y=t.y||0,this.elementId=t.elementId,this.cssClass=t.cssClass||"",this.orientation=t.orientation||[0,0],this.lastReturnValue=null,this.offsets=t.offsets||[0,0],this.timestamp=null,this._unrotatedOrientation=[this.orientation[0],this.orientation[1]],this.relocatable=!1!==t.relocatable,this.snapOnRelocate=!1!==t.snapOnRelocate,this.locked=!1,e.EventGenerator.apply(this),this.compute=function(t){var e=t.xy,n=t.wh,i=t.timestamp;if(i&&i===this.timestamp)return this.lastReturnValue;var r=[e[0]+this.x*n[0]+this.offsets[0],e[1]+this.y*n[1]+this.offsets[1],this.x,this.y],o=t.rotation;if(null!=o&&0!==o){var s=jsPlumbUtil.rotatePoint(r,[e[0]+n[0]/2,e[1]+n[1]/2],o);this.orientation[0]=Math.round(this._unrotatedOrientation[0]*s[2]-this._unrotatedOrientation[1]*s[3]),this.orientation[1]=Math.round(this._unrotatedOrientation[1]*s[2]+this._unrotatedOrientation[0]*s[3]),this.lastReturnValue=[s[0],s[1],this.x,this.y]}else this.orientation[0]=this._unrotatedOrientation[0],this.orientation[1]=this._unrotatedOrientation[1],this.lastReturnValue=r;return this.timestamp=i,this.lastReturnValue},this.getCurrentLocation=function(t){return t=t||{},null==this.lastReturnValue||null!=t.timestamp&&this.timestamp!==t.timestamp?this.compute(t):this.lastReturnValue},this.setPosition=function(t,e,n,i,r){this.locked&&!r||(this.x=t,this.y=e,this.orientation=[n,i],this.lastReturnValue=null)}},e.extend(n.Anchor,e.EventGenerator,{equals:function(t){if(!t)return!1;var e=t.getOrientation(),n=this.getOrientation();return this.x===t.x&&this.y===t.y&&this.offsets[0]===t.offsets[0]&&this.offsets[1]===t.offsets[1]&&n[0]===e[0]&&n[1]===e[1]},getOrientation:function(){return this.orientation},getCssClass:function(){return this.cssClass}}),n.FloatingAnchor=function(t){n.Anchor.apply(this,arguments);var e=t.reference,i=t.referenceCanvas,r=n.getSize(i),o=0,s=0,a=null,u=null;this.orientation=null,this.x=0,this.y=0,this.isFloating=!0,this.compute=function(t){var e=t.xy,n=[e[0]+r[0]/2,e[1]+r[1]/2];return u=n,n},this.getOrientation=function(t){if(a)return a;var n=e.getOrientation(t);return[Math.abs(n[0])*o*-1,Math.abs(n[1])*s*-1]},this.over=function(t,e){a=t.getOrientation(e)},this.out=function(){a=null},this.getCurrentLocation=function(t){return null==u?this.compute(t):u}},e.extend(n.FloatingAnchor,n.Anchor);var i=function(t,e,i){return t.constructor===n.Anchor?t:e.makeAnchor(t,i,e)};n.DynamicAnchor=function(t){n.Anchor.apply(this,arguments),this.isDynamic=!0,this.anchors=[],this.elementId=t.elementId,this.jsPlumbInstance=t.jsPlumbInstance;for(var e=0;e<t.anchors.length;e++)this.anchors[e]=i(t.anchors[e],this.jsPlumbInstance,this.elementId);this.getAnchors=function(){return this.anchors};var r=this.anchors.length>0?this.anchors[0]:null,o=r,s=function(t,e,n,i,r,o,s){var a=i[0]+t.x*r[0],u=i[1]+t.y*r[1],l=i[0]+r[0]/2,c=i[1]+r[1]/2;if(null!=o&&0!==o){var h=jsPlumbUtil.rotatePoint([a,u],[l,c],o);a=h[0],u=h[1]}return Math.sqrt(Math.pow(e-a,2)+Math.pow(n-u,2))+Math.sqrt(Math.pow(l-a,2)+Math.pow(c-u,2))},a=t.selector||function(t,e,n,i,r,o,a){for(var u=n[0]+i[0]/2,l=n[1]+i[1]/2,c=-1,h=1/0,d=0;d<a.length;d++){var f=s(a[d],u,l,t,e,r,o);f<h&&(c=d+0,h=f)}return a[c]};this.compute=function(t){var e=t.xy,n=t.wh,i=t.txy,s=t.twh,u=t.rotation,l=t.tRotation;return this.timestamp=t.timestamp,this.locked||null==i||null==s?(this.lastReturnValue=r.compute(t),this.lastReturnValue):(t.timestamp=null,r=a(e,n,i,s,u,l,this.anchors),this.x=r.x,this.y=r.y,r!==o&&this.fire("anchorChanged",r),o=r,this.lastReturnValue=r.compute(t),this.lastReturnValue)},this.getCurrentLocation=function(t){return null!=r?r.getCurrentLocation(t):null},this.getOrientation=function(t){return null!=r?r.getOrientation(t):[0,0]},this.over=function(t,e){null!=r&&r.over(t,e)},this.out=function(){null!=r&&r.out()},this.setAnchor=function(t){r=t},this.getCssClass=function(){return r&&r.getCssClass()||""},this.setAnchorCoordinates=function(t){var e=jsPlumbUtil.findWithFunction(this.anchors,(function(e){return e.x===t[0]&&e.y===t[1]}));return-1!==e&&(this.setAnchor(this.anchors[e]),!0)}},e.extend(n.DynamicAnchor,n.Anchor);var r=function(t,e,i,r,o,s){n.Anchors[o]=function(n){var a=n.jsPlumbInstance.makeAnchor([t,e,i,r,0,0],n.elementId,n.jsPlumbInstance);return a.type=o,s&&s(a,n),a}};r(.5,0,0,-1,"TopCenter"),r(.5,1,0,1,"BottomCenter"),r(0,.5,-1,0,"LeftMiddle"),r(1,.5,1,0,"RightMiddle"),r(.5,0,0,-1,"Top"),r(.5,1,0,1,"Bottom"),r(0,.5,-1,0,"Left"),r(1,.5,1,0,"Right"),r(.5,.5,0,0,"Center"),r(1,0,0,-1,"TopRight"),r(1,1,0,1,"BottomRight"),r(0,0,0,-1,"TopLeft"),r(0,1,0,1,"BottomLeft"),n.Defaults.DynamicAnchors=function(t){return t.jsPlumbInstance.makeAnchors(["TopCenter","RightMiddle","BottomCenter","LeftMiddle"],t.elementId,t.jsPlumbInstance)},n.Anchors.AutoDefault=function(t){var e=t.jsPlumbInstance.makeDynamicAnchor(n.Defaults.DynamicAnchors(t));return e.type="AutoDefault",e};var o=function(t,e){n.Anchors[t]=function(n){var i=n.jsPlumbInstance.makeAnchor(["Continuous",{faces:e}],n.elementId,n.jsPlumbInstance);return i.type=t,i}};n.Anchors.Continuous=function(t){return t.jsPlumbInstance.continuousAnchorFactory.get(t)},o("ContinuousLeft",["left"]),o("ContinuousTop",["top"]),o("ContinuousBottom",["bottom"]),o("ContinuousRight",["right"]),r(0,0,0,0,"Assign",(function(t,e){var n=e.position||"Fixed";t.positionFinder=n.constructor===String?e.jsPlumbInstance.AnchorPositionFinders[n]:n,t.constructorParams=e})),t.jsPlumbInstance.prototype.AnchorPositionFinders={Fixed:function(t,e,n){return[(t.left-e.left)/n[0],(t.top-e.top)/n[1]]},Grid:function(t,e,n,i){var r=t.left-e.left,o=t.top-e.top,s=n[0]/i.grid[0],a=n[1]/i.grid[1],u=Math.floor(r/s),l=Math.floor(o/a);return[(u*s+s/2)/n[0],(l*a+a/2)/n[1]]}},n.Anchors.Perimeter=function(t){t=t||{};var e=t.anchorCount||60,n=t.shape;if(!n)throw new Error("no shape supplied to Perimeter Anchor type");var i=function(){for(var t=.5,n=2*Math.PI/e,i=0,r=[],o=0;o<e;o++){var s=t+t*Math.sin(i),a=t+t*Math.cos(i);r.push([s,a,0,0]),i+=n}return r},r=function(t){for(var n=e/t.length,i=[],r=function(t,r,o,s,a,u,l){n=e*a;for(var c=(o-t)/n,h=(s-r)/n,d=0;d<n;d++)i.push([t+c*d,r+h*d,null==u?0:u,null==l?0:l])},o=0;o<t.length;o++)r.apply(null,t[o]);return i},o=function(t){for(var e=[],n=0;n<t.length;n++)e.push([t[n][0],t[n][1],t[n][2],t[n][3],1/t.length,t[n][4],t[n][5]]);return r(e)},s=function(){return o([[0,0,1,0,0,-1],[1,0,1,1,1,0],[1,1,0,1,0,1],[0,1,0,0,-1,0]])},a={Circle:i,Ellipse:i,Diamond:function(){return o([[.5,0,1,.5],[1,.5,.5,1],[.5,1,0,.5],[0,.5,.5,0]])},Rectangle:s,Square:s,Triangle:function(){return o([[.5,0,1,1],[1,1,0,1],[0,1,.5,0]])},Path:function(t){for(var e=t.points,n=[],i=0,o=0;o<e.length-1;o++){var s=Math.sqrt(Math.pow(e[o][2]-e[o][0])+Math.pow(e[o][3]-e[o][1]));i+=s,n.push([e[o][0],e[o][1],e[o+1][0],e[o+1][1],s])}for(var a=0;a<n.length;a++)n[a][4]=n[a][4]/i;return r(n)}},u=function(t,e){for(var n=[],i=e/180*Math.PI,r=0;r<t.length;r++){var o=t[r][0]-.5,s=t[r][1]-.5;n.push([o*Math.cos(i)-s*Math.sin(i)+.5,o*Math.sin(i)+s*Math.cos(i)+.5,t[r][2],t[r][3]])}return n};if(!a[n])throw new Error("Shape ["+n+"] is unknown by Perimeter Anchor type");var l=a[n](t);t.rotation&&(l=u(l,t.rotation));var c=t.jsPlumbInstance.makeDynamicAnchor(l);return c.type="Perimeter",c}}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=(t.jsPlumbUtil,t.jsPlumb);e.DefaultRouter=function(t){this.jsPlumbInstance=t,this.anchorManager=new e.AnchorManager({jsPlumbInstance:t}),this.sourceOrTargetChanged=function(t,e,n,i,r){this.anchorManager.sourceOrTargetChanged(t,e,n,i,r)},this.reset=function(){this.anchorManager.reset()},this.changeId=function(t,e){this.anchorManager.changeId(t,e)},this.elementRemoved=function(t){this.anchorManager.elementRemoved(t)},this.newConnection=function(t){this.anchorManager.newConnection(t)},this.connectionDetached=function(t,e){this.anchorManager.connectionDetached(t,e)},this.redraw=function(t,e,n,i,r,o){return this.anchorManager.redraw(t,e,n,i,r,o)},this.deleteEndpoint=function(t){this.anchorManager.deleteEndpoint(t)},this.rehomeEndpoint=function(t,e,n){this.anchorManager.rehomeEndpoint(t,e,n)},this.addEndpoint=function(t,e){this.anchorManager.addEndpoint(t,e)}}}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=t.Biltong;e.Segments={AbstractSegment:function(t){this.params=t,this.findClosestPointOnPath=function(t,e){return{d:1/0,x:null,y:null,l:null}},this.getBounds=function(){return{minX:Math.min(t.x1,t.x2),minY:Math.min(t.y1,t.y2),maxX:Math.max(t.x1,t.x2),maxY:Math.max(t.y1,t.y2)}},this.lineIntersection=function(t,e,n,i){return[]},this.boxIntersection=function(t,e,n,i){var r=[];return r.push.apply(r,this.lineIntersection(t,e,t+n,e)),r.push.apply(r,this.lineIntersection(t+n,e,t+n,e+i)),r.push.apply(r,this.lineIntersection(t+n,e+i,t,e+i)),r.push.apply(r,this.lineIntersection(t,e+i,t,e)),r},this.boundingBoxIntersection=function(t){return this.boxIntersection(t.x,t.y,t.w,t.y)}},Straight:function(t){e.Segments.AbstractSegment.apply(this,arguments);var n,r,o,s,a,u,l,c=function(){n=Math.sqrt(Math.pow(a-s,2)+Math.pow(l-u,2)),r=i.gradient({x:s,y:u},{x:a,y:l}),o=-1/r};this.type="Straight",this.getLength=function(){return n},this.getGradient=function(){return r},this.getCoordinates=function(){return{x1:s,y1:u,x2:a,y2:l}},this.setCoordinates=function(t){s=t.x1,u=t.y1,a=t.x2,l=t.y2,c()},this.setCoordinates({x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2}),this.getBounds=function(){return{minX:Math.min(s,a),minY:Math.min(u,l),maxX:Math.max(s,a),maxY:Math.max(u,l)}},this.pointOnPath=function(t,e){if(0!==t||e){if(1!==t||e){var r=e?t>0?t:n+t:t*n;return i.pointOnLine({x:s,y:u},{x:a,y:l},r)}return{x:a,y:l}}return{x:s,y:u}},this.gradientAtPoint=function(t){return r},this.pointAlongPathFrom=function(t,e,n){var r=this.pointOnPath(t,n),o=e<=0?{x:s,y:u}:{x:a,y:l};return e<=0&&Math.abs(e)>1&&(e*=-1),i.pointOnLine(r,o,e)};var h=function(t,e,n){return n>=Math.min(t,e)&&n<=Math.max(t,e)},d=function(t,e,n){return Math.abs(n-t)<Math.abs(n-e)?t:e};this.findClosestPointOnPath=function(t,e){var c={d:1/0,x:null,y:null,l:null,x1:s,x2:a,y1:u,y2:l};if(0===r)c.y=u,c.x=h(s,a,t)?t:d(s,a,t);else if(r===1/0||r===-1/0)c.x=s,c.y=h(u,l,e)?e:d(u,l,e);else{var f=u-r*s,p=e-o*t,g=(p-f)/(r-o),v=r*g+f;c.x=h(s,a,g)?g:d(s,a,g),c.y=h(u,l,v)?v:d(u,l,v)}var m=i.lineLength([c.x,c.y],[s,u]);return c.d=i.lineLength([t,e],[c.x,c.y]),c.l=m/n,c};var f=function(t,e,n){return n>e?e<=t&&t<=n:e>=t&&t>=n},p=f;this.lineIntersection=function(t,e,n,o){var c=Math.abs(i.gradient({x:t,y:e},{x:n,y:o})),h=Math.abs(r),d=h===1/0?s:u-h*s,f=[],g=c===1/0?t:e-c*t;if(c!==h)if(c===1/0&&0===h)p(t,s,a)&&p(u,e,o)&&(f=[t,u]);else if(0===c&&h===1/0)p(e,u,l)&&p(s,t,n)&&(f=[s,e]);else{var v,m;c===1/0?(v=t,p(v,s,a)&&(m=h*t+d,p(m,e,o)&&(f=[v,m]))):0===c?(m=e,p(m,u,l)&&(v=(e-d)/h,p(v,t,n)&&(f=[v,m]))):(v=(g-d)/(h-c),m=h*v+d,p(v,s,a)&&p(m,u,l)&&(f=[v,m]))}return f},this.boxIntersection=function(t,e,n,i){var r=[];return r.push.apply(r,this.lineIntersection(t,e,t+n,e)),r.push.apply(r,this.lineIntersection(t+n,e,t+n,e+i)),r.push.apply(r,this.lineIntersection(t+n,e+i,t,e+i)),r.push.apply(r,this.lineIntersection(t,e+i,t,e)),r},this.boundingBoxIntersection=function(t){return this.boxIntersection(t.x,t.y,t.w,t.h)}},Arc:function(t){e.Segments.AbstractSegment.apply(this,arguments);var n=function(e,n){return i.theta([t.cx,t.cy],[e,n])},r=function(t,e){if(t.anticlockwise){var n=t.startAngle<t.endAngle?t.startAngle+o:t.startAngle,i=Math.abs(n-t.endAngle);return n-i*e}var r=t.endAngle<t.startAngle?t.endAngle+o:t.endAngle,s=Math.abs(r-t.startAngle);return t.startAngle+s*e},o=2*Math.PI;this.radius=t.r,this.anticlockwise=t.ac,this.type="Arc",t.startAngle&&t.endAngle?(this.startAngle=t.startAngle,this.endAngle=t.endAngle,this.x1=t.cx+this.radius*Math.cos(t.startAngle),this.y1=t.cy+this.radius*Math.sin(t.startAngle),this.x2=t.cx+this.radius*Math.cos(t.endAngle),this.y2=t.cy+this.radius*Math.sin(t.endAngle)):(this.startAngle=n(t.x1,t.y1),this.endAngle=n(t.x2,t.y2),this.x1=t.x1,this.y1=t.y1,this.x2=t.x2,this.y2=t.y2),this.endAngle<0&&(this.endAngle+=o),this.startAngle<0&&(this.startAngle+=o);var s=this.endAngle<this.startAngle?this.endAngle+o:this.endAngle;this.sweep=Math.abs(s-this.startAngle),this.anticlockwise&&(this.sweep=o-this.sweep);var a=2*Math.PI*this.radius,u=this.sweep/o,l=a*u;this.getLength=function(){return l},this.getBounds=function(){return{minX:t.cx-t.r,maxX:t.cx+t.r,minY:t.cy-t.r,maxY:t.cy+t.r}};var c=1e-10,h=function(t){var e=Math.floor(t),n=Math.ceil(t);return t-e<c?e:n-t<c?n:t};this.pointOnPath=function(e,n){if(0===e)return{x:this.x1,y:this.y1,theta:this.startAngle};if(1===e)return{x:this.x2,y:this.y2,theta:this.endAngle};n&&(e/=l);var i=r(this,e),o=t.cx+t.r*Math.cos(i),s=t.cy+t.r*Math.sin(i);return{x:h(o),y:h(s),theta:i}},this.gradientAtPoint=function(e,n){var r=this.pointOnPath(e,n),o=i.normal([t.cx,t.cy],[r.x,r.y]);return this.anticlockwise||o!==1/0&&o!==-1/0||(o*=-1),o},this.pointAlongPathFrom=function(e,n,i){var r=this.pointOnPath(e,i),o=n/a*2*Math.PI,s=this.anticlockwise?-1:1,u=r.theta+s*o,l=t.cx+this.radius*Math.cos(u),c=t.cy+this.radius*Math.sin(u);return{x:l,y:c}}},Bezier:function(n){this.curve=[{x:n.x1,y:n.y1},{x:n.cp1x,y:n.cp1y},{x:n.cp2x,y:n.cp2y},{x:n.x2,y:n.y2}];var i=function(t){return t[0].x===t[1].x&&t[0].y===t[1].y},r=function(t,e){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},o=function(t){var e={x:0,y:0};if(0===t)return this.curve[0];var n=this.curve.length-1;if(1===t)return this.curve[n];var i=this.curve,r=1-t;if(0===n)return this.curve[0];if(1===n)return{x:r*i[0].x+t*i[1].x,y:r*i[0].y+t*i[1].y};if(n<4){var o,s,a,u=r*r,l=t*t,c=0;return 2===n?(i=[i[0],i[1],i[2],e],o=u,s=r*t*2,a=l):3===n&&(o=u*r,s=u*t*3,a=r*l*3,c=t*l),{x:o*i[0].x+s*i[1].x+a*i[2].x+c*i[3].x,y:o*i[0].y+s*i[1].y+a*i[2].y+c*i[3].y}}return e}.bind(this),s=function(t){var e=[];t--;for(var n=0;n<=t;n++)e.push(o(n/t));return e},a=function(){i(this.curve)&&(this.length=0);var t=16,e=s(t);this.length=0;for(var n=0;n<t-1;n++){var o=e[n],a=e[n+1];this.length+=r(o,a)}}.bind(this);e.Segments.AbstractSegment.apply(this,arguments);this.bounds={minX:Math.min(n.x1,n.x2,n.cp1x,n.cp2x),minY:Math.min(n.y1,n.y2,n.cp1y,n.cp2y),maxX:Math.max(n.x1,n.x2,n.cp1x,n.cp2x),maxY:Math.max(n.y1,n.y2,n.cp1y,n.cp2y)},this.type="Bezier",a();var u=function(e,n,i){return i&&(n=t.jsBezier.locationAlongCurveFrom(e,n>0?0:1,n)),n};this.pointOnPath=function(e,n){return e=u(this.curve,e,n),t.jsBezier.pointOnCurve(this.curve,e)},this.gradientAtPoint=function(e,n){return e=u(this.curve,e,n),t.jsBezier.gradientAtPoint(this.curve,e)},this.pointAlongPathFrom=function(e,n,i){return e=u(this.curve,e,i),t.jsBezier.pointAlongCurveFrom(this.curve,e,n)},this.getLength=function(){return this.length},this.getBounds=function(){return this.bounds},this.findClosestPointOnPath=function(e,n){var i=t.jsBezier.nearestPointOnCurve({x:e,y:n},this.curve);return{d:Math.sqrt(Math.pow(i.point.x-e,2)+Math.pow(i.point.y-n,2)),x:i.point.x,y:i.point.y,l:1-i.location,s:this}},this.lineIntersection=function(e,n,i,r){return t.jsBezier.lineIntersection(e,n,i,r,this.curve)}}},e.SegmentRenderer={getPath:function(t,e){return{Straight:function(e){var n=t.getCoordinates();return(e?"M "+n.x1+" "+n.y1+" ":"")+"L "+n.x2+" "+n.y2},Bezier:function(e){var n=t.params;return(e?"M "+n.x2+" "+n.y2+" ":"")+"C "+n.cp2x+" "+n.cp2y+" "+n.cp1x+" "+n.cp1y+" "+n.x1+" "+n.y1},Arc:function(e){var n=t.params,i=t.sweep>Math.PI?1:0,r=t.anticlockwise?0:1;return(e?"M"+t.x1+" "+t.y1+" ":"")+"A "+t.radius+" "+n.r+" 0 "+i+","+r+" "+t.x2+" "+t.y2}}[t.type](e)}};var r=function(){this.resetBounds=function(){this.bounds={minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}},this.resetBounds()};e.Connectors.AbstractConnector=function(t){r.apply(this,arguments);var o=[],s=0,a=[],u=[],l=t.stub||0,c=n.isArray(l)?l[0]:l,h=n.isArray(l)?l[1]:l,d=t.gap||0,f=n.isArray(d)?d[0]:d,p=n.isArray(d)?d[1]:d,g=null,v=null;this.getPathData=function(){for(var t="",n=0;n<o.length;n++)t+=e.SegmentRenderer.getPath(o[n],0===n),t+=" ";return t},this.findSegmentForPoint=function(t,e){for(var n={d:1/0,s:null,x:null,y:null,l:null},i=0;i<o.length;i++){var r=o[i].findClosestPointOnPath(t,e);r.d<n.d&&(n.d=r.d,n.l=r.l,n.x=r.x,n.y=r.y,n.s=o[i],n.x1=r.x1,n.x2=r.x2,n.y1=r.y1,n.y2=r.y2,n.index=i,n.connectorLocation=a[i][0]+r.l*(a[i][1]-a[i][0]))}return n},this.lineIntersection=function(t,e,n,i){for(var r=[],s=0;s<o.length;s++)r.push.apply(r,o[s].lineIntersection(t,e,n,i));return r},this.boxIntersection=function(t,e,n,i){for(var r=[],s=0;s<o.length;s++)r.push.apply(r,o[s].boxIntersection(t,e,n,i));return r},this.boundingBoxIntersection=function(t){for(var e=[],n=0;n<o.length;n++)e.push.apply(e,o[n].boundingBoxIntersection(t));return e};var m=function(){for(var t=0,e=0;e<o.length;e++){var n=o[e].getLength();u[e]=n/s,a[e]=[t,t+=n/s]}},b=function(t,e){var n,i,r;if(e&&(t=t>0?t/s:(s+t)/s),1===t)n=o.length-1,r=1;else if(0===t)r=0,n=0;else if(t>=.5){for(n=0,r=0,i=a.length-1;i>-1;i--)if(a[i][1]>=t&&a[i][0]<=t){n=i,r=(t-a[i][0])/u[i];break}}else for(n=a.length-1,r=1,i=0;i<a.length;i++)if(a[i][1]>=t){n=i,r=(t-a[i][0])/u[i];break}return{segment:o[n],proportion:r,index:n}},y=function(t,n,i){if(i.x1!==i.x2||i.y1!==i.y2){var r=new e.Segments[n](i);o.push(r),s+=r.getLength(),t.updateBounds(r)}},x=function(){s=o.length=a.length=u.length=0};this.setSegments=function(t){g=[],s=0;for(var e=0;e<t.length;e++)g.push(t[e]),s+=t[e].getLength()},this.getLength=function(){return s};var P=function(t){this.strokeWidth=t.strokeWidth;var e=i.quadrant(t.sourcePos,t.targetPos),n=t.targetPos[0]<t.sourcePos[0],r=t.targetPos[1]<t.sourcePos[1],o=t.strokeWidth||1,s=t.sourceEndpoint.anchor.getOrientation(t.sourceEndpoint),a=t.targetEndpoint.anchor.getOrientation(t.targetEndpoint),u=n?t.targetPos[0]:t.sourcePos[0],l=r?t.targetPos[1]:t.sourcePos[1],d=Math.abs(t.targetPos[0]-t.sourcePos[0]),g=Math.abs(t.targetPos[1]-t.sourcePos[1]);if(0===s[0]&&0===s[1]||0===a[0]&&0===a[1]){var v=d>g?0:1,m=[1,0][v];s=[],a=[],s[v]=t.sourcePos[v]>t.targetPos[v]?-1:1,a[v]=t.sourcePos[v]>t.targetPos[v]?1:-1,s[m]=0,a[m]=0}var b=n?d+f*s[0]:f*s[0],y=r?g+f*s[1]:f*s[1],x=n?p*a[0]:d+p*a[0],P=r?p*a[1]:g+p*a[1],_=s[0]*a[0]+s[1]*a[1],C={sx:b,sy:y,tx:x,ty:P,lw:o,xSpan:Math.abs(x-b),ySpan:Math.abs(P-y),mx:(b+x)/2,my:(y+P)/2,so:s,to:a,x:u,y:l,w:d,h:g,segment:e,startStubX:b+s[0]*c,startStubY:y+s[1]*c,endStubX:x+a[0]*h,endStubY:P+a[1]*h,isXGreaterThanStubTimes2:Math.abs(b-x)>c+h,isYGreaterThanStubTimes2:Math.abs(y-P)>c+h,opposite:-1===_,perpendicular:0===_,orthogonal:1===_,sourceAxis:0===s[0]?"y":"x",points:[u,l,d,g,b,y,x,P],stubs:[c,h]};return C.anchorOrientation=C.opposite?"opposite":C.orthogonal?"orthogonal":"perpendicular",C};this.getSegments=function(){return o},this.updateBounds=function(t){var e=t.getBounds();this.bounds.minX=Math.min(this.bounds.minX,e.minX),this.bounds.maxX=Math.max(this.bounds.maxX,e.maxX),this.bounds.minY=Math.min(this.bounds.minY,e.minY),this.bounds.maxY=Math.max(this.bounds.maxY,e.maxY)};return this.pointOnPath=function(t,e){var n=b(t,e);return n.segment&&n.segment.pointOnPath(n.proportion,!1)||[0,0]},this.gradientAtPoint=function(t,e){var n=b(t,e);return n.segment&&n.segment.gradientAtPoint(n.proportion,!1)||0},this.pointAlongPathFrom=function(t,e,n){var i=b(t,n);return i.segment&&i.segment.pointAlongPathFrom(i.proportion,e,!1)||[0,0]},this.compute=function(t){v=P.call(this,t),x(),this._compute(v,t),this.x=v.points[0],this.y=v.points[1],this.w=v.points[2],this.h=v.points[3],this.segment=v.segment,m()},{addSegment:y,prepareCompute:P,sourceStub:c,targetStub:h,maxStub:Math.max(c,h),sourceGap:f,targetGap:p,maxGap:Math.max(f,p)}},n.extend(e.Connectors.AbstractConnector,r),e.Endpoints.AbstractEndpoint=function(t){r.apply(this,arguments);var e=this.compute=function(t,e,n,i){var r=this._compute.apply(this,arguments);return this.x=r[0],this.y=r[1],this.w=r[2],this.h=r[3],this.bounds.minX=this.x,this.bounds.minY=this.y,this.bounds.maxX=this.x+this.w,this.bounds.maxY=this.y+this.h,r};return{compute:e,cssClass:t.cssClass}},n.extend(e.Endpoints.AbstractEndpoint,r),e.Endpoints.Dot=function(t){this.type="Dot";e.Endpoints.AbstractEndpoint.apply(this,arguments);t=t||{},this.radius=t.radius||10,this.defaultOffset=.5*this.radius,this.defaultInnerRadius=this.radius/3,this._compute=function(t,e,n,i){this.radius=n.radius||this.radius;var r=t[0]-this.radius,o=t[1]-this.radius,s=2*this.radius,a=2*this.radius;if(n.stroke){var u=n.strokeWidth||1;r-=u,o-=u,s+=2*u,a+=2*u}return[r,o,s,a,this.radius]}},n.extend(e.Endpoints.Dot,e.Endpoints.AbstractEndpoint),e.Endpoints.Rectangle=function(t){this.type="Rectangle";e.Endpoints.AbstractEndpoint.apply(this,arguments);t=t||{},this.width=t.width||20,this.height=t.height||20,this._compute=function(t,e,n,i){var r=n.width||this.width,o=n.height||this.height,s=t[0]-r/2,a=t[1]-o/2;return[s,a,r,o]}},n.extend(e.Endpoints.Rectangle,e.Endpoints.AbstractEndpoint);var o=function(t){e.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.displayElements=[]};n.extend(o,e.jsPlumbUIComponent,{getDisplayElements:function(){return this._jsPlumb.displayElements},appendDisplayElement:function(t){this._jsPlumb.displayElements.push(t)}}),e.Endpoints.Image=function(i){this.type="Image",o.apply(this,arguments),e.Endpoints.AbstractEndpoint.apply(this,arguments);var r=i.onload,s=i.src||i.url,a=i.cssClass?" "+i.cssClass:"";this._jsPlumb.img=new Image,this._jsPlumb.ready=!1,this._jsPlumb.initialized=!1,this._jsPlumb.deleted=!1,this._jsPlumb.widthToUse=i.width,this._jsPlumb.heightToUse=i.height,this._jsPlumb.endpoint=i.endpoint,this._jsPlumb.img.onload=function(){null!=this._jsPlumb&&(this._jsPlumb.ready=!0,this._jsPlumb.widthToUse=this._jsPlumb.widthToUse||this._jsPlumb.img.width,this._jsPlumb.heightToUse=this._jsPlumb.heightToUse||this._jsPlumb.img.height,r&&r(this))}.bind(this),this._jsPlumb.endpoint.setImage=function(t,e){var n=t.constructor===String?t:t.src;r=e,this._jsPlumb.img.src=n,null!=this.canvas&&this.canvas.setAttribute("src",this._jsPlumb.img.src)}.bind(this),this._jsPlumb.endpoint.setImage(s,r),this._compute=function(t,e,n,i){return this.anchorPoint=t,this._jsPlumb.ready?[t[0]-this._jsPlumb.widthToUse/2,t[1]-this._jsPlumb.heightToUse/2,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse]:[0,0,0,0]},this.canvas=e.createElement("img",{position:"absolute",margin:0,padding:0,outline:0},this._jsPlumb.instance.endpointClass+a),this._jsPlumb.widthToUse&&this.canvas.setAttribute("width",this._jsPlumb.widthToUse),this._jsPlumb.heightToUse&&this.canvas.setAttribute("height",this._jsPlumb.heightToUse),this._jsPlumb.instance.appendElement(this.canvas),this.actuallyPaint=function(t,e,i){if(!this._jsPlumb.deleted){this._jsPlumb.initialized||(this.canvas.setAttribute("src",this._jsPlumb.img.src),this.appendDisplayElement(this.canvas),this._jsPlumb.initialized=!0);var r=this.anchorPoint[0]-this._jsPlumb.widthToUse/2,o=this.anchorPoint[1]-this._jsPlumb.heightToUse/2;n.sizeElement(this.canvas,r,o,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse)}},this.paint=function(e,n){null!=this._jsPlumb&&(this._jsPlumb.ready?this.actuallyPaint(e,n):t.setTimeout(function(){this.paint(e,n)}.bind(this),200))}},n.extend(e.Endpoints.Image,[o,e.Endpoints.AbstractEndpoint],{cleanup:function(t){t&&(this._jsPlumb.deleted=!0,this.canvas&&this.canvas.parentNode.removeChild(this.canvas),this.canvas=null)}}),e.Endpoints.Blank=function(t){e.Endpoints.AbstractEndpoint.apply(this,arguments);this.type="Blank",o.apply(this,arguments),this._compute=function(t,e,n,i){return[t[0],t[1],10,0]};var i=t.cssClass?" "+t.cssClass:"";this.canvas=e.createElement("div",{display:"block",width:"1px",height:"1px",background:"transparent",position:"absolute"},this._jsPlumb.instance.endpointClass+i),this._jsPlumb.instance.appendElement(this.canvas),this.paint=function(t,e){n.sizeElement(this.canvas,this.x,this.y,this.w,this.h)}},n.extend(e.Endpoints.Blank,[e.Endpoints.AbstractEndpoint,o],{cleanup:function(){this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas)}}),e.Endpoints.Triangle=function(t){this.type="Triangle",e.Endpoints.AbstractEndpoint.apply(this,arguments);var n=this;t=t||{},t.width=t.width||55,t.height=t.height||55,this.width=t.width,this.height=t.height,this._compute=function(t,e,i,r){var o=i.width||n.width,s=i.height||n.height,a=t[0]-o/2,u=t[1]-s/2;return[a,u,o,s]}};var s=e.Overlays.AbstractOverlay=function(t){this.visible=!0,this.isAppendedAtTopLevel=!0,this.component=t.component,this.loc=null==t.location?.5:t.location,this.endpointLoc=null==t.endpointLocation?[.5,.5]:t.endpointLocation,this.visible=!1!==t.visible};s.prototype={cleanup:function(t){t&&(this.component=null,this.canvas=null,this.endpointLoc=null)},reattach:function(t,e){},setVisible:function(t){this.visible=t,this.component.repaint()},isVisible:function(){return this.visible},hide:function(){this.setVisible(!1)},show:function(){this.setVisible(!0)},incrementLocation:function(t){this.loc+=t,this.component.repaint()},setLocation:function(t){this.loc=t,this.component.repaint()},getLocation:function(){return this.loc},updateFrom:function(){}},e.Overlays.Arrow=function(t){this.type="Arrow",s.apply(this,arguments),this.isAppendedAtTopLevel=!1,t=t||{};var r=this;this.length=t.length||20,this.width=t.width||20,this.id=t.id,this.direction=(t.direction||1)<0?-1:1;var o=t.paintStyle||{"stroke-width":1},a=t.foldback||.623;this.computeMaxSize=function(){return 1.5*r.width},this.elementCreated=function(n,i){if(this.path=n,t.events)for(var r in t.events)e.on(n,r,t.events[r])},this.draw=function(t,e){var r,s,u,l,c;if(t.pointAlongPathFrom){if(n.isString(this.loc)||this.loc>1||this.loc<0){var h=parseInt(this.loc,10),d=this.loc<0?1:0;r=t.pointAlongPathFrom(d,h,!1),s=t.pointAlongPathFrom(d,h-this.direction*this.length/2,!1),u=i.pointOnLine(r,s,this.length)}else if(1===this.loc){if(r=t.pointOnPath(this.loc),s=t.pointAlongPathFrom(this.loc,-this.length),u=i.pointOnLine(r,s,this.length),-1===this.direction){var f=u;u=r,r=f}}else if(0===this.loc){if(u=t.pointOnPath(this.loc),s=t.pointAlongPathFrom(this.loc,this.length),r=i.pointOnLine(u,s,this.length),-1===this.direction){var p=u;u=r,r=p}}else r=t.pointAlongPathFrom(this.loc,this.direction*this.length/2),s=t.pointOnPath(this.loc),u=i.pointOnLine(r,s,this.length);l=i.perpendicularLineTo(r,u,this.width),c=i.pointOnLine(r,u,a*this.length);var g={hxy:r,tail:l,cxy:c},v=o.stroke||e.stroke,m=o.fill||e.stroke,b=o.strokeWidth||e.strokeWidth;return{component:t,d:g,"stroke-width":b,stroke:v,fill:m,minX:Math.min(r.x,l[0].x,l[1].x),maxX:Math.max(r.x,l[0].x,l[1].x),minY:Math.min(r.y,l[0].y,l[1].y),maxY:Math.max(r.y,l[0].y,l[1].y)}}return{component:t,minX:0,maxX:0,minY:0,maxY:0}}},n.extend(e.Overlays.Arrow,s,{updateFrom:function(t){this.length=t.length||this.length,this.width=t.width||this.width,this.direction=null!=t.direction?t.direction:this.direction,this.foldback=t.foldback||this.foldback},cleanup:function(){this.path&&this.path.parentNode&&this.path.parentNode.removeChild(this.path)}}),e.Overlays.PlainArrow=function(t){t=t||{};var n=e.extend(t,{foldback:1});e.Overlays.Arrow.call(this,n),this.type="PlainArrow"},n.extend(e.Overlays.PlainArrow,e.Overlays.Arrow),e.Overlays.Diamond=function(t){t=t||{};var n=t.length||40,i=e.extend(t,{length:n/2,foldback:2});e.Overlays.Arrow.call(this,i),this.type="Diamond"},n.extend(e.Overlays.Diamond,e.Overlays.Arrow);var a=function(t,e){return(null==t._jsPlumb.cachedDimensions||e)&&(t._jsPlumb.cachedDimensions=t.getDimensions()),t._jsPlumb.cachedDimensions},u=function(t){e.jsPlumbUIComponent.apply(this,arguments),s.apply(this,arguments);var i=this.fire;this.fire=function(){i.apply(this,arguments),this.component&&this.component.fire.apply(this.component,arguments)},this.detached=!1,this.id=t.id,this._jsPlumb.div=null,this._jsPlumb.initialised=!1,this._jsPlumb.component=t.component,this._jsPlumb.cachedDimensions=null,this._jsPlumb.create=t.create,this._jsPlumb.initiallyInvisible=!1===t.visible,this.getElement=function(){if(null==this._jsPlumb.div){var n=this._jsPlumb.div=e.getElement(this._jsPlumb.create(this._jsPlumb.component));n.style.position="absolute",jsPlumb.addClass(n,this._jsPlumb.instance.overlayClass+" "+(this.cssClass?this.cssClass:t.cssClass?t.cssClass:"")),this._jsPlumb.instance.appendElement(n),this._jsPlumb.instance.getId(n),this.canvas=n;var i="translate(-50%, -50%)";n.style.webkitTransform=i,n.style.mozTransform=i,n.style.msTransform=i,n.style.oTransform=i,n.style.transform=i,n._jsPlumb=this,!1===t.visible&&(n.style.display="none")}return this._jsPlumb.div},this.draw=function(t,e,i){var r=a(this);if(null!=r&&2===r.length){var o={x:0,y:0};if(i)o={x:i[0],y:i[1]};else if(t.pointOnPath){var s=this.loc,u=!1;(n.isString(this.loc)||this.loc<0||this.loc>1)&&(s=parseInt(this.loc,10),u=!0),o=t.pointOnPath(s,u)}else{var l=this.loc.constructor===Array?this.loc:this.endpointLoc;o={x:l[0]*t.w,y:l[1]*t.h}}var c=o.x-r[0]/2,h=o.y-r[1]/2;return{component:t,d:{minx:c,miny:h,td:r,cxy:o},minX:c,maxX:c+r[0],minY:h,maxY:h+r[1]}}return{minX:0,maxX:0,minY:0,maxY:0}}};n.extend(u,[e.jsPlumbUIComponent,s],{getDimensions:function(){return[1,1]},setVisible:function(t){this._jsPlumb.div&&(this._jsPlumb.div.style.display=t?"block":"none",t&&this._jsPlumb.initiallyInvisible&&(a(this,!0),this.component.repaint(),this._jsPlumb.initiallyInvisible=!1))},clearCachedDimensions:function(){this._jsPlumb.cachedDimensions=null},cleanup:function(t){t?null!=this._jsPlumb.div&&(this._jsPlumb.div._jsPlumb=null,this._jsPlumb.instance.removeElement(this._jsPlumb.div)):(this._jsPlumb&&this._jsPlumb.div&&this._jsPlumb.div.parentNode&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div),this.detached=!0)},reattach:function(t,e){null!=this._jsPlumb.div&&t.getContainer().appendChild(this._jsPlumb.div),this.detached=!1},computeMaxSize:function(){var t=a(this);return Math.max(t[0],t[1])},paint:function(t,e){this._jsPlumb.initialised||(this.getElement(),t.component.appendDisplayElement(this._jsPlumb.div),this._jsPlumb.initialised=!0,this.detached&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div)),this._jsPlumb.div.style.left=t.component.x+t.d.minx+"px",this._jsPlumb.div.style.top=t.component.y+t.d.miny+"px"}}),e.Overlays.Custom=function(t){this.type="Custom",u.apply(this,arguments)},n.extend(e.Overlays.Custom,u),e.Overlays.GuideLines=function(){var t=this;t.length=50,t.strokeWidth=5,this.type="GuideLines",s.apply(this,arguments),e.jsPlumbUIComponent.apply(this,arguments),this.draw=function(e,n){var r=e.pointAlongPathFrom(t.loc,t.length/2),o=e.pointOnPath(t.loc),s=i.pointOnLine(r,o,t.length),a=i.perpendicularLineTo(r,s,40),u=i.perpendicularLineTo(s,r,20);return{connector:e,head:r,tail:s,headLine:u,tailLine:a,minX:Math.min(r.x,s.x,u[0].x,u[1].x),minY:Math.min(r.y,s.y,u[0].y,u[1].y),maxX:Math.max(r.x,s.x,u[0].x,u[1].x),maxY:Math.max(r.y,s.y,u[0].y,u[1].y)}}},e.Overlays.Label=function(t){this.labelStyle=t.labelStyle;this.cssClass=null!=this.labelStyle?this.labelStyle.cssClass:null;var n=e.extend({create:function(){return e.createElement("div")}},t);if(e.Overlays.Custom.call(this,n),this.type="Label",this.label=t.label||"",this.labelText=null,this.labelStyle){var i=this.getElement();if(this.labelStyle.font=this.labelStyle.font||"12px sans-serif",i.style.font=this.labelStyle.font,i.style.color=this.labelStyle.color||"black",this.labelStyle.fill&&(i.style.background=this.labelStyle.fill),this.labelStyle.borderWidth>0){var r=this.labelStyle.borderStyle?this.labelStyle.borderStyle:"black";i.style.border=this.labelStyle.borderWidth+"px solid "+r}this.labelStyle.padding&&(i.style.padding=this.labelStyle.padding)}},n.extend(e.Overlays.Label,e.Overlays.Custom,{cleanup:function(t){t&&(this.div=null,this.label=null,this.labelText=null,this.cssClass=null,this.labelStyle=null)},getLabel:function(){return this.label},setLabel:function(t){this.label=t,this.labelText=null,this.clearCachedDimensions(),this.update(),this.component.repaint()},getDimensions:function(){return this.update(),u.prototype.getDimensions.apply(this,arguments)},update:function(){if("function"===typeof this.label){var t=this.label(this);this.getElement().innerHTML=t.replace(/\r\n/g,"<br/>")}else null==this.labelText&&(this.labelText=this.label,this.getElement().innerHTML=this.labelText.replace(/\r\n/g,"<br/>"))},updateFrom:function(t){null!=t.label&&this.setLabel(t.label)}})}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumbUtil,n=t.jsPlumbInstance,i="jtk-group-collapsed",r="jtk-group-expanded",o="[jtk-group-content]",s="elementDraggable",a="stop",u="revert",l="_groupManager",c="_jsPlumbGroup",h="_jsPlumbGroupDrag",d="group:addMember",f="group:removeMember",p="group:add",g="group:remove",v="group:expand",m="group:collapse",b="groupDragStop",y="connectionMoved",x="internal.connectionDetached",P="removeAll",_="orphanAll",C="show",E="hide",j=function(t){var n={},o={},s={},a=this;function u(e,n){var i=t.getContainer(),r=!1;while(!r){if(null==e||e===i)return!1;if(e===n)return!0;e=e.parentNode}}function l(t){delete t.proxies;var n,i=o[t.id];null!=i&&(n=function(e){return e.id===t.id},e.removeWithFunction(i.connections.source,n),e.removeWithFunction(i.connections.target,n),delete o[t.id]),i=s[t.id],null!=i&&(n=function(e){return e.id===t.id},e.removeWithFunction(i.connections.source,n),e.removeWithFunction(i.connections.target,n),delete s[t.id])}function h(e,n){for(var i=e.getEl().querySelectorAll(".jtk-managed"),r=0;r<i.length;r++)t[n?C:E](i[r],!0)}t.bind("connection",(function(n){var i=t.getGroupFor(n.source),r=t.getGroupFor(n.target);null!=i&&null!=r&&i===r?(o[n.connection.id]=i,s[n.connection.id]=i):(null!=i&&(e.suggest(i.connections.source,n.connection),o[n.connection.id]=i),null!=r&&(e.suggest(r.connections.target,n.connection),s[n.connection.id]=r))})),t.bind(x,(function(t){l(t.connection)})),t.bind(y,(function(t){var e=0===t.index?o:s,n=e[t.connection.id];if(n){var i=n.connections[0===t.index?"source":"target"],r=i.indexOf(t.connection);-1!==r&&i.splice(r,1)}})),this.addGroup=function(e){t.addClass(e.getEl(),r),n[e.id]=e,e.manager=this,j(e),t.fire(p,{group:e})},this.addToGroup=function(e,n,i){if(e=this.getGroup(e),e){var r=e.getEl();if(n._isJsPlumbGroup)return;var o=n._jsPlumbGroup;if(o!==e){t.removeFromDragSelection(n);var s=t.getOffset(n,!0),u=e.collapsed?t.getOffset(r,!0):t.getOffset(e.getDragArea(),!0);null!=o&&(o.remove(n,!1,i,!1,e),a.updateConnectionsForGroup(o)),e.add(n,i);var l=function(t,n){var i=0===n?1:0;t.each((function(t){t.setVisible(!1),t.endpoints[i].element._jsPlumbGroup===e?(t.endpoints[i].setVisible(!1),b(t,i,e)):(t.endpoints[n].setVisible(!1),f(t,n,e))}))};e.collapsed&&(l(t.select({source:n}),0),l(t.select({target:n}),1));var c=t.getId(n);t.dragManager.setParent(n,c,r,t.getId(r),s);var h={left:s.left-u.left,top:s.top-u.top};if(t.setPosition(n,h),t.dragManager.revalidateParent(n,c,s),a.updateConnectionsForGroup(e),t.revalidate(c),!i){var p={group:e,el:n,pos:h};o&&(p.sourceGroup=o),t.fire(d,p)}}}},this.removeFromGroup=function(t,e,n){if(t=this.getGroup(t),t){if(t.collapsed){var i=function(n,i){for(var r=0;r<n.length;r++){var o=n[r];if(o.proxies)for(var s=0;s<o.proxies.length;s++)if(null!=o.proxies[s]){var a=o.proxies[s].originalEp.element;(a===e||u(a,e))&&b(o,i,t)}}};i(t.connections.source.slice(),0),i(t.connections.target.slice(),1)}t.remove(e,null,n)}},this.getGroup=function(t){var i=t;if(e.isString(t)&&(i=n[t],null==i))throw new TypeError("No such group ["+t+"]");return i},this.getGroups=function(){var t=[];for(var e in n)t.push(n[e]);return t},this.removeGroup=function(e,i,r,o){e=this.getGroup(e),this.expandGroup(e,!0);var s=e[i?P:_](r,o);return t.remove(e.getEl()),delete n[e.id],delete t._groups[e.id],t.fire(g,{group:e}),s},this.removeAllGroups=function(t,e,i){for(var r in n)this.removeGroup(n[r],t,e,i)};var f=function(e,n,i){var r=e.endpoints[0===n?1:0].element;if(!r[c]||r[c].shouldProxy()||!r[c].collapsed){var o=i.getEl(),s=t.getId(o);t.proxyConnection(e,n,o,s,(function(t,e){return i.getEndpoint(t,e)}),(function(t,e){return i.getAnchor(t,e)}))}};this.collapseGroup=function(e){if(e=this.getGroup(e),null!=e&&!e.collapsed){var n=e.getEl();if(h(e,!1),e.shouldProxy()){var o=function(t,n){for(var i=0;i<t.length;i++){var r=t[i];f(r,n,e)}};o(e.connections.source,0),o(e.connections.target,1)}e.collapsed=!0,t.removeClass(n,r),t.addClass(n,i),t.revalidate(n),t.fire(m,{group:e})}};var b=function(e,n,i){t.unproxyConnection(e,n,t.getId(i.getEl()))};function j(e){for(var n=e.getMembers().slice(),i=[],r=0;r<n.length;r++)Array.prototype.push.apply(i,n[r].querySelectorAll(".jtk-managed"));Array.prototype.push.apply(n,i);var a=t.getConnections({source:n,scope:"*"},!0),u=t.getConnections({target:n,scope:"*"},!0),l={};e.connections.source.length=0,e.connections.target.length=0;var c=function(n){for(var i=0;i<n.length;i++)if(!l[n[i].id]){l[n[i].id]=!0;var r=t.getGroupFor(n[i].source),a=t.getGroupFor(n[i].target);r===e?(a!==e&&e.connections.source.push(n[i]),o[n[i].id]=e):a===e&&(e.connections.target.push(n[i]),s[n[i].id]=e)}};c(a),c(u)}this.expandGroup=function(e,n){if(e=this.getGroup(e),null!=e&&e.collapsed){var o=e.getEl();if(h(e,!0),e.shouldProxy()){var s=function(t,n){for(var i=0;i<t.length;i++){var r=t[i];b(r,n,e)}};s(e.connections.source,0),s(e.connections.target,1)}e.collapsed=!1,t.addClass(o,r),t.removeClass(o,i),t.revalidate(o),this.repaintGroup(e),n||t.fire(v,{group:e})}},this.repaintGroup=function(e){e=this.getGroup(e);for(var n=e.getMembers(),i=0;i<n.length;i++)t.revalidate(n[i])},this.updateConnectionsForGroup=j,this.refreshAllGroups=function(){for(var e in n)j(n[e]),t.dragManager.updateOffsets(t.getId(n[e].getEl()))}},S=function(n,i){var r=this,l=i.el;this.getEl=function(){return l},this.id=i.id||e.uuid(),l._isJsPlumbGroup=!0;var d=this.getDragArea=function(){var t=n.getSelector(l,o);return t&&t.length>0?t[0]:l},p=!0===i.ghost,g=p||!0===i.constrain,v=!1!==i.revert,m=!0===i.orphan,y=!0===i.prune,x=!0===i.dropOverride,P=!1!==i.proxied,_=[];if(this.connections={source:[],target:[],internal:[]},this.getAnchor=function(t,e){return i.anchor||"Continuous"},this.getEndpoint=function(t,e){return i.endpoint||["Dot",{radius:10}]},this.collapsed=!1,!1!==i.draggable){var C={drag:function(){for(var t=0;t<_.length;t++)n.draw(_[t])},stop:function(t){n.fire(b,jsPlumb.extend(t,{group:r}))},scope:h};i.dragOptions&&t.jsPlumb.extend(C,i.dragOptions),n.draggable(i.el,C)}!1!==i.droppable&&n.droppable(i.el,{drop:function(t){var e=t.drag.el;if(!e._isJsPlumbGroup){var i=e._jsPlumbGroup;if(i!==r){if(null!=i&&i.overrideDrop(e,r))return;n.getGroupManager().addToGroup(r,e,!1)}}}});var E=function(t,e){for(var n=null==t.nodeType?t:[t],i=0;i<n.length;i++)e(n[i])};function j(t){return t.offsetParent}function S(t,e){var i=j(t),r=n.getSize(i),o=n.getSize(t),s=e[0],a=s+o[0],u=e[1],l=u+o[1];return a>0&&s<r[0]&&l>0&&u<r[1]}function w(t){var e=n.getId(t),i=n.getOffset(t);return t.parentNode.removeChild(t),n.getContainer().appendChild(t),n.setPosition(t,i),O(t),n.dragManager.clearParent(t,e),[e,i]}function D(t){var e=[];function i(t,e,i){var r=null;if(!S(t,[e,i])){var o=t._jsPlumbGroup;y?n.remove(t):r=w(t),o.remove(t)}return r}for(var r=0;r<t.selection.length;r++)e.push(i(t.selection[r][0],t.selection[r][1].left,t.selection[r][1].top));return 1===e.length?e[0]:e}function A(t){var e=n.getId(t);n.revalidate(t),n.dragManager.revalidateParent(t,e)}function O(t){t._katavorioDrag&&((y||m)&&t._katavorioDrag.off(a,D),y||m||!v||(t._katavorioDrag.off(u,A),t._katavorioDrag.setRevert(null)))}function I(t){t._katavorioDrag&&((y||m)&&t._katavorioDrag.on(a,D),g&&t._katavorioDrag.setConstrain(!0),p&&t._katavorioDrag.setUseGhostProxy(!0),y||m||!v||(t._katavorioDrag.on(u,A),t._katavorioDrag.setRevert((function(t,e){return!S(t,e)}))))}this.overrideDrop=function(t,e){return x&&(v||y||m)},this.add=function(t,e){var i=d();E(t,(function(t){if(null!=t._jsPlumbGroup){if(t._jsPlumbGroup===r)return;t._jsPlumbGroup.remove(t,!0,e,!1)}t._jsPlumbGroup=r,_.push(t),n.isAlreadyDraggable(t)&&I(t),t.parentNode!==i&&i.appendChild(t)})),n.getGroupManager().updateConnectionsForGroup(r)},this.remove=function(t,i,o,s,a){E(t,(function(t){if(t._jsPlumbGroup===r){if(delete t._jsPlumbGroup,e.removeWithFunction(_,(function(e){return e===t})),i)try{r.getDragArea().removeChild(t)}catch(u){jsPlumbUtil.log("Could not remove element from Group "+u)}if(O(t),!o){var s={group:r,el:t};a&&(s.targetGroup=a),n.fire(f,s)}}})),s||n.getGroupManager().updateConnectionsForGroup(r)},this.removeAll=function(t,e){for(var i=0,o=_.length;i<o;i++){var s=_[0];r.remove(s,t,e,!0),n.remove(s,!0)}_.length=0,n.getGroupManager().updateConnectionsForGroup(r)},this.orphanAll=function(){for(var t={},e=0;e<_.length;e++){var n=w(_[e]);t[n[0]]=n[1]}return _.length=0,t},this.getMembers=function(){return _},l[c]=this,n.bind(s,function(t){t.el._jsPlumbGroup===this&&I(t.el)}.bind(this)),this.shouldProxy=function(){return P},n.getGroupManager().addGroup(this)};n.prototype.addGroup=function(t){var e=this;if(e._groups=e._groups||{},null!=e._groups[t.id])throw new TypeError("cannot create Group ["+t.id+"]; a Group with that ID exists");if(null!=t.el[c])throw new TypeError("cannot create Group ["+t.id+"]; the given element is already a Group");var n=new S(e,t);return e._groups[n.id]=n,t.collapsed&&this.collapseGroup(n),n},n.prototype.addToGroup=function(t,e,n){var i=function(e){var i=this.getId(e);this.manage(i,e),this.getGroupManager().addToGroup(t,e,n)}.bind(this);if(Array.isArray(e))for(var r=0;r<e.length;r++)i(e[r]);else i(e)},n.prototype.removeFromGroup=function(t,e,n){this.getGroupManager().removeFromGroup(t,e,n),this.getContainer().appendChild(e)},n.prototype.removeGroup=function(t,e,n,i){return this.getGroupManager().removeGroup(t,e,n,i)},n.prototype.removeAllGroups=function(t,e,n){this.getGroupManager().removeAllGroups(t,e,n)},n.prototype.getGroup=function(t){return this.getGroupManager().getGroup(t)},n.prototype.getGroups=function(){return this.getGroupManager().getGroups()},n.prototype.expandGroup=function(t){this.getGroupManager().expandGroup(t)},n.prototype.collapseGroup=function(t){this.getGroupManager().collapseGroup(t)},n.prototype.repaintGroup=function(t){this.getGroupManager().repaintGroup(t)},n.prototype.toggleGroup=function(t){t=this.getGroupManager().getGroup(t),null!=t&&this.getGroupManager()[t.collapsed?"expandGroup":"collapseGroup"](t)},n.prototype.getGroupManager=function(){var t=this[l];return null==t&&(t=this[l]=new j(this)),t},n.prototype.removeGroupManager=function(){delete this[l]},n.prototype.getGroupFor=function(t){if(t=this.getElement(t),t){var e=this.getContainer(),n=!1,i=null;while(!n)null==t||t===e?n=!0:t[c]?(i=t[c],t,n=!0):t=t.parentNode;return i}}}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i="Straight",r="Arc",o=function(t){this.type="Flowchart",t=t||{},t.stub=null==t.stub?30:t.stub;var n,o=e.Connectors.AbstractConnector.apply(this,arguments),s=null==t.midpoint||isNaN(t.midpoint)?.5:t.midpoint,a=!0===t.alwaysRespectStubs,u=null,l=null,c=null!=t.cornerRadius?t.cornerRadius:0,h=(t.loopbackRadius,function(t){return t<0?-1:0===t?0:1}),d=function(t){return[h(t[2]-t[0]),h(t[3]-t[1])]},f=function(t,e,n,i){if(u!==e||l!==n){var r=null==u?i.sx:u,o=null==l?i.sy:l,s=r===e?"v":"h";u=e,l=n,t.push([r,o,e,n,s])}},p=function(t){return Math.sqrt(Math.pow(t[0]-t[2],2)+Math.pow(t[1]-t[3],2))},g=function(t){var e=[];return e.push.apply(e,t),e},v=function(t,e,n){for(var s,a,u,l=null,h=0;h<e.length-1;h++){if(l=l||g(e[h]),s=g(e[h+1]),a=d(l),u=d(s),c>0&&l[4]!==s[4]){var f=Math.min(p(l),p(s)),v=Math.min(c,f/2);l[2]-=a[0]*v,l[3]-=a[1]*v,s[0]+=u[0]*v,s[1]+=u[1]*v;var m=a[1]===u[0]&&1===u[0]||a[1]===u[0]&&0===u[0]&&a[0]!==u[1]||a[1]===u[0]&&-1===u[0],b=s[1]>l[3]?1:-1,y=s[0]>l[2]?1:-1,x=b===y,P=x&&m||!x&&!m?s[0]:l[2],_=x&&m||!x&&!m?l[3]:s[1];o.addSegment(t,i,{x1:l[0],y1:l[1],x2:l[2],y2:l[3]}),o.addSegment(t,r,{r:v,x1:l[2],y1:l[3],x2:s[0],y2:s[1],cx:P,cy:_,ac:m})}else{var C=l[2]===l[0]?0:l[2]>l[0]?n.lw/2:-n.lw/2,E=l[3]===l[1]?0:l[3]>l[1]?n.lw/2:-n.lw/2;o.addSegment(t,i,{x1:l[0]-C,y1:l[1]-E,x2:l[2]+C,y2:l[3]+E})}l=s}null!=s&&o.addSegment(t,i,{x1:s[0],y1:s[1],x2:s[2],y2:s[3]})};this.midpoint=s,this._compute=function(t,e){n=[],u=null,l=null,null;var i=function(){return[t.startStubX,t.startStubY,t.endStubX,t.endStubY]},r={perpendicular:i,orthogonal:i,opposite:function(e){var n=t,i="x"===e?0:1,r={x:function(){return 1===n.so[i]&&(n.startStubX>n.endStubX&&n.tx>n.startStubX||n.sx>n.endStubX&&n.tx>n.sx)||-1===n.so[i]&&(n.startStubX<n.endStubX&&n.tx<n.startStubX||n.sx<n.endStubX&&n.tx<n.sx)},y:function(){return 1===n.so[i]&&(n.startStubY>n.endStubY&&n.ty>n.startStubY||n.sy>n.endStubY&&n.ty>n.sy)||-1===n.so[i]&&(n.startStubY<n.endStubY&&n.ty<n.startStubY||n.sy<n.endStubY&&n.ty<n.sy)}};return!a&&r[e]()?{x:[(t.sx+t.tx)/2,t.startStubY,(t.sx+t.tx)/2,t.endStubY],y:[t.startStubX,(t.sy+t.ty)/2,t.endStubX,(t.sy+t.ty)/2]}[e]:[t.startStubX,t.startStubY,t.endStubX,t.endStubY]}},c=r[t.anchorOrientation](t.sourceAxis),h="x"===t.sourceAxis?0:1,d="x"===t.sourceAxis?1:0,p=c[h],g=c[d],m=c[h+2],b=c[d+2];f(n,c[0],c[1],t);var y=t.startStubX+(t.endStubX-t.startStubX)*s,x=t.startStubY+(t.endStubY-t.startStubY)*s,P={x:[0,1],y:[1,0]},_={perpendicular:function(e){var n=t,i={x:[[[1,2,3,4],null,[2,1,4,3]],null,[[4,3,2,1],null,[3,4,1,2]]],y:[[[3,2,1,4],null,[2,3,4,1]],null,[[4,1,2,3],null,[1,4,3,2]]]},r={x:[[n.startStubX,n.endStubX],null,[n.endStubX,n.startStubX]],y:[[n.startStubY,n.endStubY],null,[n.endStubY,n.startStubY]]},o={x:[[y,n.startStubY],[y,n.endStubY]],y:[[n.startStubX,x],[n.endStubX,x]]},s={x:[[n.endStubX,n.startStubY]],y:[[n.startStubX,n.endStubY]]},a={x:[[n.startStubX,n.endStubY],[n.endStubX,n.endStubY]],y:[[n.endStubX,n.startStubY],[n.endStubX,n.endStubY]]},u={x:[[n.startStubX,x],[n.endStubX,x],[n.endStubX,n.endStubY]],y:[[y,n.startStubY],[y,n.endStubY],[n.endStubX,n.endStubY]]},l={x:[n.startStubY,n.endStubY],y:[n.startStubX,n.endStubX]},c=P[e][0],h=P[e][1],d=n.so[c]+1,f=n.to[h]+1,p=-1===n.to[h]&&l[e][1]<l[e][0]||1===n.to[h]&&l[e][1]>l[e][0],g=r[e][d][0],v=r[e][d][1],m=i[e][d][f];return n.segment===m[3]||n.segment===m[2]&&p?o[e]:n.segment===m[2]&&v<g?s[e]:n.segment===m[2]&&v>=g||n.segment===m[1]&&!p?u[e]:n.segment===m[0]||n.segment===m[1]&&p?a[e]:void 0},orthogonal:function(e,n,i,r,o){var s=t,a={x:-1===s.so[0]?Math.min(n,r):Math.max(n,r),y:-1===s.so[1]?Math.min(n,r):Math.max(n,r)}[e];return{x:[[a,i],[a,o],[r,o]],y:[[i,a],[o,a],[o,r]]}[e]},opposite:function(n,i,r,s){var a=t,u={x:"y",y:"x"}[n],l={x:"height",y:"width"}[n],c=a["is"+n.toUpperCase()+"GreaterThanStubTimes2"];if(e.sourceEndpoint.elementId===e.targetEndpoint.elementId){var d=r+(1-e.sourceEndpoint.anchor[u])*e.sourceInfo[l]+o.maxStub;return{x:[[i,d],[s,d]],y:[[d,i],[d,s]]}[n]}return!c||1===a.so[h]&&i>s||-1===a.so[h]&&i<s?{x:[[i,x],[s,x]],y:[[y,i],[y,s]]}[n]:1===a.so[h]&&i<s||-1===a.so[h]&&i>s?{x:[[y,a.sy],[y,a.ty]],y:[[a.sx,x],[a.tx,x]]}[n]:void 0}},C=_[t.anchorOrientation](t.sourceAxis,p,g,m,b);if(C)for(var E=0;E<C.length;E++)f(n,C[E][0],C[E][1],t);f(n,c[2],c[3],t),f(n,t.tx,t.ty,t),v(this,n,t)}};e.Connectors.Flowchart=o,n.extend(e.Connectors.Flowchart,e.Connectors.AbstractConnector)}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil;e.Connectors.AbstractBezierConnector=function(t){t=t||{};var n,i=!1!==t.showLoopback,r=(t.curviness,t.margin||5),o=(t.proximityLimit,t.orientation&&"clockwise"===t.orientation),s=t.loopbackRadius||25;return this._compute=function(t,e){var a=e.sourcePos,u=e.targetPos,l=Math.abs(a[0]-u[0]),c=Math.abs(a[1]-u[1]);if(i&&e.sourceEndpoint.elementId===e.targetEndpoint.elementId){!0;var h=e.sourcePos[0],d=e.sourcePos[1]-r,f=h,p=d-s,g=f-s,v=p-s;l=2*s,c=2*s,t.points[0]=g,t.points[1]=v,t.points[2]=l,t.points[3]=c,n.addSegment(this,"Arc",{loopback:!0,x1:h-g+4,y1:d-v,startAngle:0,endAngle:2*Math.PI,r:s,ac:!o,x2:h-g-4,y2:d-v,cx:f-g,cy:p-v})}else!1,this._computeBezier(t,e,a,u,l,c)},n=e.Connectors.AbstractConnector.apply(this,arguments),n},n.extend(e.Connectors.AbstractBezierConnector,e.Connectors.AbstractConnector);var i=function(t){t=t||{},this.type="Bezier";var n=e.Connectors.AbstractBezierConnector.apply(this,arguments),i=t.curviness||150,r=10;this.getCurviness=function(){return i},this._findControlPoint=function(t,e,n,o,s,a,u){var l=a[0]!==u[0]||a[1]===u[1],c=[];return l?(0===u[0]?c.push(n[0]<e[0]?t[0]+r:t[0]-r):c.push(t[0]+i*u[0]),0===u[1]?c.push(n[1]<e[1]?t[1]+r:t[1]-r):c.push(t[1]+i*a[1])):(0===a[0]?c.push(e[0]<n[0]?t[0]+r:t[0]-r):c.push(t[0]-i*a[0]),0===a[1]?c.push(e[1]<n[1]?t[1]+r:t[1]-r):c.push(t[1]+i*u[1])),c},this._computeBezier=function(t,e,i,r,o,s){var a,u,l=i[0]<r[0]?o:0,c=i[1]<r[1]?s:0,h=i[0]<r[0]?0:o,d=i[1]<r[1]?0:s;a=this._findControlPoint([l,c],i,r,e.sourceEndpoint,e.targetEndpoint,t.so,t.to),u=this._findControlPoint([h,d],r,i,e.targetEndpoint,e.sourceEndpoint,t.to,t.so),n.addSegment(this,"Bezier",{x1:l,y1:c,x2:h,y2:d,cp1x:a[0],cp1y:a[1],cp2x:u[0],cp2y:u[1]})}};e.Connectors.Bezier=i,n.extend(i,e.Connectors.AbstractBezierConnector)}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=function(t,e,n,i){return t<=n&&i<=e?1:t<=n&&e<=i?2:n<=t&&i>=e?3:4},r=function(t,e,n,i,r,o,s,a,u){return a<=u?[t,e]:1===n?i[3]<=0&&r[3]>=1?[t+(i[2]<.5?-1*o:o),e]:i[2]>=1&&r[2]<=0?[t,e+(i[3]<.5?-1*s:s)]:[t+-1*o,e+-1*s]:2===n?i[3]>=1&&r[3]<=0?[t+(i[2]<.5?-1*o:o),e]:i[2]>=1&&r[2]<=0?[t,e+(i[3]<.5?-1*s:s)]:[t+o,e+-1*s]:3===n?i[3]>=1&&r[3]<=0?[t+(i[2]<.5?-1*o:o),e]:i[2]<=0&&r[2]>=1?[t,e+(i[3]<.5?-1*s:s)]:[t+-1*o,e+-1*s]:4===n?i[3]<=0&&r[3]>=1?[t+(i[2]<.5?-1*o:o),e]:i[2]<=0&&r[2]>=1?[t,e+(i[3]<.5?-1*s:s)]:[t+o,e+-1*s]:void 0},o=function(t){t=t||{},this.type="StateMachine";var n,o=e.Connectors.AbstractBezierConnector.apply(this,arguments),s=t.curviness||10,a=t.margin||5,u=t.proximityLimit||80;t.orientation&&t.orientation;this._computeBezier=function(t,e,l,c,h,d){var f=e.sourcePos[0]<e.targetPos[0]?0:h,p=e.sourcePos[1]<e.targetPos[1]?0:d,g=e.sourcePos[0]<e.targetPos[0]?h:0,v=e.sourcePos[1]<e.targetPos[1]?d:0;0===e.sourcePos[2]&&(f-=a),1===e.sourcePos[2]&&(f+=a),0===e.sourcePos[3]&&(p-=a),1===e.sourcePos[3]&&(p+=a),0===e.targetPos[2]&&(g-=a),1===e.targetPos[2]&&(g+=a),0===e.targetPos[3]&&(v-=a),1===e.targetPos[3]&&(v+=a);var m,b,y,x,P=(f+g)/2,_=(p+v)/2,C=i(f,p,g,v),E=Math.sqrt(Math.pow(g-f,2)+Math.pow(v-p,2));n=r(P,_,C,e.sourcePos,e.targetPos,s,s,E,u),m=n[0],b=n[0],y=n[1],x=n[1],o.addSegment(this,"Bezier",{x1:g,y1:v,x2:f,y2:p,cp1x:m,cp1y:y,cp2x:b,cp2y:x})}};e.Connectors.StateMachine=o,n.extend(o,e.Connectors.AbstractBezierConnector)}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i="Straight",r=function(t){this.type=i;var n=e.Connectors.AbstractConnector.apply(this,arguments);this._compute=function(t,e){n.addSegment(this,i,{x1:t.sx,y1:t.sy,x2:t.startStubX,y2:t.startStubY}),n.addSegment(this,i,{x1:t.startStubX,y1:t.startStubY,x2:t.endStubX,y2:t.endStubY}),n.addSegment(this,i,{x1:t.endStubX,y1:t.endStubY,x2:t.tx,y2:t.ty})}};e.Connectors.Straight=r,n.extend(r,e.Connectors.AbstractConnector)}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i={"stroke-linejoin":"stroke-linejoin","stroke-dashoffset":"stroke-dashoffset","stroke-linecap":"stroke-linecap"},r="stroke-dasharray",o="dashstyle",s="linearGradient",a="radialGradient",u="defs",l="fill",c="stop",h="stroke",d="stroke-width",f="style",p="none",g="jsplumb_gradient_",v="strokeWidth",m={svg:"http://www.w3.org/2000/svg"},b=function(t,e){for(var n in e)t.setAttribute(n,""+e[n])},y=function(t,n){return n=n||{},n.version="1.1",n.xmlns=m.svg,e.createElementNS(m.svg,t,null,null,n)},x=function(t){return"position:absolute;left:"+t[0]+"px;top:"+t[1]+"px"},P=function(t){for(var e=t.querySelectorAll(" defs,linearGradient,radialGradient"),n=0;n<e.length;n++)e[n].parentNode.removeChild(e[n])},_=function(t,e,n,i,r){var o,d=g+r._jsPlumb.instance.idstamp();P(t),o=n.gradient.offset?y(a,{id:d}):y(s,{id:d,gradientUnits:"userSpaceOnUse"});var f=y(u);t.appendChild(f),f.appendChild(o);for(var p=0;p<n.gradient.stops.length;p++){var v=1===r.segment||2===r.segment?p:n.gradient.stops.length-1-p,m=n.gradient.stops[v][1],b=y(c,{offset:Math.floor(100*n.gradient.stops[p][0])+"%","stop-color":m});o.appendChild(b)}var x=n.stroke?h:l;e.setAttribute(x,"url(#"+d+")")},C=function(t,e,n,s,a){if(e.setAttribute(l,n.fill?n.fill:p),e.setAttribute(h,n.stroke?n.stroke:p),n.gradient?_(t,e,n,s,a):(P(t),e.setAttribute(f,"")),n.strokeWidth&&e.setAttribute(d,n.strokeWidth),n[o]&&n[v]&&!n[r]){var u=-1===n[o].indexOf(",")?" ":",",c=n[o].split(u),g="";c.forEach((function(t){g+=Math.floor(t*n.strokeWidth)+u})),e.setAttribute(r,g)}else n[r]&&e.setAttribute(r,n[r]);for(var m in i)n[m]&&e.setAttribute(i[m],n[m])},E=function(t,e,n){t.childNodes.length>n?t.insertBefore(e,t.childNodes[n]):t.appendChild(e)};n.svg={node:y,attr:b,pos:x};var j=function(t){var i=t.pointerEventsSpec||"all",r={};e.jsPlumbUIComponent.apply(this,t.originalArgs),this.canvas=null,this.path=null,this.svg=null,this.bgCanvas=null;var o=t.cssClass+" "+(t.originalArgs[0].cssClass||""),s={style:"",width:0,height:0,"pointer-events":i,position:"absolute"};this.svg=y("svg",s),t.useDivWrapper?(this.canvas=e.createElement("div",{position:"absolute"}),n.sizeElement(this.canvas,0,0,1,1),this.canvas.className=o):(b(this.svg,{class:o}),this.canvas=this.svg),t._jsPlumb.appendElement(this.canvas,t.originalArgs[0].parent),t.useDivWrapper&&this.canvas.appendChild(this.svg);var a=[this.canvas];return this.getDisplayElements=function(){return a},this.appendDisplayElement=function(t){a.push(t)},this.paint=function(e,i,o){if(null!=e){var s,a=[this.x,this.y],u=[this.w,this.h];null!=o&&(o.xmin<0&&(a[0]+=o.xmin),o.ymin<0&&(a[1]+=o.ymin),u[0]=o.xmax+(o.xmin<0?-o.xmin:0),u[1]=o.ymax+(o.ymin<0?-o.ymin:0)),t.useDivWrapper?(n.sizeElement(this.canvas,a[0],a[1],u[0]>0?u[0]:1,u[1]>0?u[1]:1),a[0]=0,a[1]=0,s=x([0,0])):s=x([a[0],a[1]]),r.paint.apply(this,arguments),b(this.svg,{style:s,width:u[0]||1,height:u[1]||1})}},{renderer:r}};n.extend(j,e.jsPlumbUIComponent,{cleanup:function(t){t||null==this.typeId?(this.canvas&&(this.canvas._jsPlumb=null),this.svg&&(this.svg._jsPlumb=null),this.bgCanvas&&(this.bgCanvas._jsPlumb=null),this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.svg=null,this.canvas=null,this.path=null,this.group=null,this._jsPlumb=null):(this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.bgCanvas.parentNode.removeChild(this.bgCanvas))},reattach:function(t){var e=t.getContainer();this.canvas&&null==this.canvas.parentNode&&e.appendChild(this.canvas),this.bgCanvas&&null==this.bgCanvas.parentNode&&e.appendChild(this.bgCanvas)},setVisible:function(t){this.canvas&&(this.canvas.style.display=t?"block":"none")}}),e.ConnectorRenderers.svg=function(t){var n=this,i=j.apply(this,[{cssClass:t._jsPlumb.connectorClass,originalArgs:arguments,pointerEventsSpec:"none",_jsPlumb:t._jsPlumb}]);i.renderer.paint=function(i,r,o){var s=n.getSegments(),a="",u=[0,0];if(o.xmin<0&&(u[0]=-o.xmin),o.ymin<0&&(u[1]=-o.ymin),s.length>0){a=n.getPathData();var l={d:a,transform:"translate("+u[0]+","+u[1]+")","pointer-events":t["pointer-events"]||"visibleStroke"},c=null,h=[n.x,n.y,n.w,n.h];if(i.outlineStroke){var d=i.outlineWidth||1,f=i.strokeWidth+2*d;c=e.extend({},i),delete c.gradient,c.stroke=i.outlineStroke,c.strokeWidth=f,null==n.bgPath?(n.bgPath=y("path",l),e.addClass(n.bgPath,e.connectorOutlineClass),E(n.svg,n.bgPath,0)):b(n.bgPath,l),C(n.svg,n.bgPath,c,h,n)}null==n.path?(n.path=y("path",l),E(n.svg,n.path,i.outlineStroke?1:0)):b(n.path,l),C(n.svg,n.path,i,h,n)}}},n.extend(e.ConnectorRenderers.svg,j);var S=e.SvgEndpoint=function(t){var n=j.apply(this,[{cssClass:t._jsPlumb.endpointClass,originalArgs:arguments,pointerEventsSpec:"all",useDivWrapper:!0,_jsPlumb:t._jsPlumb}]);n.renderer.paint=function(t){var n=e.extend({},t);n.outlineStroke&&(n.stroke=n.outlineStroke),null==this.node?(this.node=this.makeNode(n),this.svg.appendChild(this.node)):null!=this.updateNode&&this.updateNode(this.node),C(this.svg,this.node,n,[this.x,this.y,this.w,this.h],this),x(this.node,[this.x,this.y])}.bind(this)};n.extend(S,j),e.Endpoints.svg.Dot=function(){e.Endpoints.Dot.apply(this,arguments),S.apply(this,arguments),this.makeNode=function(t){return y("circle",{cx:this.w/2,cy:this.h/2,r:this.radius})},this.updateNode=function(t){b(t,{cx:this.w/2,cy:this.h/2,r:this.radius})}},n.extend(e.Endpoints.svg.Dot,[e.Endpoints.Dot,S]),e.Endpoints.svg.Rectangle=function(){e.Endpoints.Rectangle.apply(this,arguments),S.apply(this,arguments),this.makeNode=function(t){return y("rect",{width:this.w,height:this.h})},this.updateNode=function(t){b(t,{width:this.w,height:this.h})}},n.extend(e.Endpoints.svg.Rectangle,[e.Endpoints.Rectangle,S]),e.Endpoints.svg.Image=e.Endpoints.Image,e.Endpoints.svg.Blank=e.Endpoints.Blank,e.Overlays.svg.Label=e.Overlays.Label,e.Overlays.svg.Custom=e.Overlays.Custom;var w=function(t,n){t.apply(this,n),e.jsPlumbUIComponent.apply(this,n),this.isAppendedAtTopLevel=!1;this.path=null,this.paint=function(t,e){if(t.component.svg&&e){null==this.path&&(this.path=y("path",{"pointer-events":"all"}),t.component.svg.appendChild(this.path),this.elementCreated&&this.elementCreated(this.path,t.component),this.canvas=t.component.svg);var r=n&&1===n.length&&n[0].cssClass||"",o=[0,0];e.xmin<0&&(o[0]=-e.xmin),e.ymin<0&&(o[1]=-e.ymin),b(this.path,{d:i(t.d),class:r,stroke:t.stroke?t.stroke:null,fill:t.fill?t.fill:null,transform:"translate("+o[0]+","+o[1]+")"})}};var i=function(t){return isNaN(t.cxy.x)||isNaN(t.cxy.y)?"":"M"+t.hxy.x+","+t.hxy.y+" L"+t.tail[0].x+","+t.tail[0].y+" L"+t.cxy.x+","+t.cxy.y+" L"+t.tail[1].x+","+t.tail[1].y+" L"+t.hxy.x+","+t.hxy.y};this.transfer=function(t){t.canvas&&this.path&&this.path.parentNode&&(this.path.parentNode.removeChild(this.path),t.canvas.appendChild(this.path))}},D={cleanup:function(t){null!=this.path&&(t?this._jsPlumb.instance.removeElement(this.path):this.path.parentNode&&this.path.parentNode.removeChild(this.path))},reattach:function(t,e){this.path&&e.canvas&&e.canvas.appendChild(this.path)},setVisible:function(t){null!=this.path&&(this.path.style.display=t?"block":"none")}};n.extend(w,[e.jsPlumbUIComponent,e.Overlays.AbstractOverlay]),e.Overlays.svg.Arrow=function(){w.apply(this,[e.Overlays.Arrow,arguments])},n.extend(e.Overlays.svg.Arrow,[e.Overlays.Arrow,w],D),e.Overlays.svg.PlainArrow=function(){w.apply(this,[e.Overlays.PlainArrow,arguments])},n.extend(e.Overlays.svg.PlainArrow,[e.Overlays.PlainArrow,w],D),e.Overlays.svg.Diamond=function(){w.apply(this,[e.Overlays.Diamond,arguments])},n.extend(e.Overlays.svg.Diamond,[e.Overlays.Diamond,w],D),e.Overlays.svg.GuideLines=function(){var t,n,i=null,r=this;e.Overlays.GuideLines.apply(this,arguments),this.paint=function(e,s){null==i&&(i=y("path"),e.connector.svg.appendChild(i),r.attachListeners(i,e.connector),r.attachListeners(i,r),t=y("path"),e.connector.svg.appendChild(t),r.attachListeners(t,e.connector),r.attachListeners(t,r),n=y("path"),e.connector.svg.appendChild(n),r.attachListeners(n,e.connector),r.attachListeners(n,r));var a=[0,0];s.xmin<0&&(a[0]=-s.xmin),s.ymin<0&&(a[1]=-s.ymin),b(i,{d:o(e.head,e.tail),stroke:"red",fill:null,transform:"translate("+a[0]+","+a[1]+")"}),b(t,{d:o(e.tailLine[0],e.tailLine[1]),stroke:"blue",fill:null,transform:"translate("+a[0]+","+a[1]+")"}),b(n,{d:o(e.headLine[0],e.headLine[1]),stroke:"green",fill:null,transform:"translate("+a[0]+","+a[1]+")"})};var o=function(t,e){return"M "+t.x+","+t.y+" L"+e.x+","+e.y}},n.extend(e.Overlays.svg.GuideLines,e.Overlays.GuideLines)}.call("undefined"!==typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=t.Katavorio,r=t.Biltong,o=function(e){var n=e._mottle;return n||(n=e._mottle=new t.Mottle),n},s=function(t,n){n=n||"main";var o="_katavorio_"+n,s=t[o],a=t.getEventManager();return s||(s=new i({bind:a.on,unbind:a.off,getSize:e.getSize,getConstrainingRectangle:function(t){return[t.parentNode.scrollWidth,t.parentNode.scrollHeight]},getPosition:function(e,n){var i=t.getOffset(e,n,e._katavorioDrag?e.offsetParent:null);return[i.left,i.top]},setPosition:function(t,e){t.style.left=e[0]+"px",t.style.top=e[1]+"px"},addClass:e.addClass,removeClass:e.removeClass,intersects:r.intersects,indexOf:function(t,e){return t.indexOf(e)},scope:t.getDefaultScope(),css:{noSelect:t.dragSelectClass,droppable:"jtk-droppable",draggable:"jtk-draggable",drag:"jtk-drag",selected:"jtk-drag-selected",active:"jtk-drag-active",hover:"jtk-drag-hover",ghostProxy:"jtk-ghost-proxy"}}),s.setZoom(t.getZoom()),t[o]=s,t.bind("zoom",s.setZoom)),s},a=function(t){var e=t.el._jsPlumbDragOptions,n=!0;return e.canDrag&&(n=e.canDrag()),n&&(this.setHoverSuspended(!0),this.select({source:t.el}).addClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:t.el}).addClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),this.setConnectionBeingDragged(!0)),n},u=function(t){var e=this.getUIPosition(arguments,this.getZoom());if(null!=e){var n=t.el._jsPlumbDragOptions;this.draw(t.el,e,null,!0),n._dragging&&this.addClass(t.el,"jtk-dragged"),n._dragging=!0}},l=function(t){for(var e,n=t.selection,i=function(n){var i;null!=n[1]&&(e=this.getUIPosition([{el:n[2].el,pos:[n[1].left,n[1].top]}]),i=this.draw(n[2].el,e)),null!=n[0]._jsPlumbDragOptions&&delete n[0]._jsPlumbDragOptions._dragging,this.removeClass(n[0],"jtk-dragged"),this.select({source:n[2].el}).removeClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:n[2].el}).removeClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),t.e._drawResult=t.e._drawResult||{c:[],e:[],a:[]},Array.prototype.push.apply(t.e._drawResult.c,i.c),Array.prototype.push.apply(t.e._drawResult.e,i.e),Array.prototype.push.apply(t.e._drawResult.a,i.a),this.getDragManager().dragEnded(n[2].el)}.bind(this),r=0;r<n.length;r++)i(n[r]);this.setHoverSuspended(!1),this.setConnectionBeingDragged(!1)},c=function(t,e){var i=function(i){if(null!=e[i]){if(n.isString(e[i])){var r=e[i].match(/-=/)?-1:1,o=e[i].substring(2);return t[i]+r*o}return e[i]}return t[i]};return[i("left"),i("top")]},h=function(t,e){if(null==e)return[0,0];var n=v(e),i=g(n,0);return[i[t+"X"],i[t+"Y"]]},d=h.bind(this,"page"),f=h.bind(this,"screen"),p=h.bind(this,"client"),g=function(t,e){return t.item?t.item(e):t[e]},v=function(t){return t.touches&&t.touches.length>0?t.touches:t.changedTouches&&t.changedTouches.length>0?t.changedTouches:t.targetTouches&&t.targetTouches.length>0?t.targetTouches:[t]},m=function(t){var e={},n=[],i={},r={},o={};this.register=function(s){var a,u=t.getId(s);e[u]||(e[u]=s,n.push(s),i[u]={});var l=function(e){if(e)for(var n=0;n<e.childNodes.length;n++)if(3!==e.childNodes[n].nodeType&&8!==e.childNodes[n].nodeType){var c=jsPlumb.getElement(e.childNodes[n]),h=t.getId(e.childNodes[n],null,!0);if(h&&r[h]&&r[h]>0){a||(a=t.getOffset(s));var d=t.getOffset(c);i[u][h]={id:h,offset:{left:d.left-a.left,top:d.top-a.top}},o[h]=u}l(e.childNodes[n])}};l(s)},this.updateOffsets=function(e,n){if(null!=e){n=n||{};var r,s=jsPlumb.getElement(e),a=t.getId(s),u=i[a];if(u)for(var l in u)if(u.hasOwnProperty(l)){var c=jsPlumb.getElement(l),h=n[l]||t.getOffset(c);if(null==c.offsetParent&&null!=i[a][l])continue;r||(r=t.getOffset(s)),i[a][l]={id:l,offset:{left:h.left-r.left,top:h.top-r.top}},o[l]=a}}},this.endpointAdded=function(n,s){s=s||t.getId(n);var a=document.body,u=n.parentNode;r[s]=r[s]?r[s]+1:1;while(null!=u&&u!==a){var l=t.getId(u,null,!0);if(l&&e[l]){var c=t.getOffset(u);if(null==i[l][s]){var h=t.getOffset(n);i[l][s]={id:s,offset:{left:h.left-c.left,top:h.top-c.top}},o[s]=l}break}u=u.parentNode}},this.endpointDeleted=function(t){if(r[t.elementId]&&(r[t.elementId]--,r[t.elementId]<=0))for(var e in i)i.hasOwnProperty(e)&&i[e]&&(delete i[e][t.elementId],delete o[t.elementId])},this.changeId=function(t,e){i[e]=i[t],i[t]={},o[e]=o[t],o[t]=null},this.getElementsForDraggable=function(t){return i[t]},this.elementRemoved=function(t){var e=o[t];e&&(i[e]&&delete i[e][t],delete o[t])},this.reset=function(){e={},n=[],i={},r={}},this.dragEnded=function(e){if(null!=e.offsetParent){var n=t.getId(e),i=o[n];i&&this.updateOffsets(i)}},this.setParent=function(e,n,r,s,a){var u=o[n];i[s]||(i[s]={});var l=t.getOffset(r),c=a||t.getOffset(e);u&&i[u]&&delete i[u][n],i[s][n]={id:n,offset:{left:c.left-l.left,top:c.top-l.top}},o[n]=s},this.clearParent=function(t,e){var n=o[e];n&&(delete i[n][e],delete o[e])},this.revalidateParent=function(e,n,i){var r=o[n];if(r){var s={};s[n]=i,this.updateOffsets(r,s),t.revalidate(r)}},this.getDragAncestor=function(e){var n=jsPlumb.getElement(e),i=t.getId(n),r=o[i];return r?jsPlumb.getElement(r):null}},b=function(t,e,i){e=n.fastTrim(e),"undefined"!==typeof t.className.baseVal?t.className.baseVal=e:t.className=e;try{var r=t.classList;if(null!=r){while(r.length>0)r.remove(r.item(0));for(var o=0;o<i.length;o++)i[o]&&r.add(i[o])}}catch(s){n.log("JSPLUMB: cannot set class list",s)}},y=function(t){return"undefined"===typeof t.className.baseVal?t.className:t.className.baseVal},x=function(t,e,i){e=null==e?[]:n.isArray(e)?e:e.split(/\s+/),i=null==i?[]:n.isArray(i)?i:i.split(/\s+/);var r=y(t),o=r.split(/\s+/),s=function(t,e){for(var n=0;n<e.length;n++)if(t)-1===o.indexOf(e[n])&&o.push(e[n]);else{var i=o.indexOf(e[n]);-1!==i&&o.splice(i,1)}};s(!0,e),s(!1,i),b(t,o.join(" "),o)};t.jsPlumb.extend(t.jsPlumbInstance.prototype,{headless:!1,pageLocation:d,screenLocation:f,clientLocation:p,getDragManager:function(){return null==this.dragManager&&(this.dragManager=new m(this)),this.dragManager},recalculateOffsets:function(t){this.getDragManager().updateOffsets(t)},createElement:function(t,e,n,i){return this.createElementNS(null,t,e,n,i)},createElementNS:function(t,e,n,i,r){var o,s=null==t?document.createElement(e):document.createElementNS(t,e);for(o in n=n||{},n)s.style[o]=n[o];for(o in i&&(s.className=i),r=r||{},r)s.setAttribute(o,""+r[o]);return s},getAttribute:function(t,e){return null!=t.getAttribute?t.getAttribute(e):null},setAttribute:function(t,e,n){null!=t.setAttribute&&t.setAttribute(e,n)},setAttributes:function(t,e){for(var n in e)e.hasOwnProperty(n)&&t.setAttribute(n,e[n])},appendToRoot:function(t){document.body.appendChild(t)},getRenderModes:function(){return["svg"]},getClass:y,addClass:function(t,e){jsPlumb.each(t,(function(t){x(t,e)}))},hasClass:function(t,e){return t=jsPlumb.getElement(t),t.classList?t.classList.contains(e):-1!==y(t).indexOf(e)},removeClass:function(t,e){jsPlumb.each(t,(function(t){x(t,null,e)}))},toggleClass:function(t,e){jsPlumb.hasClass(t,e)?jsPlumb.removeClass(t,e):jsPlumb.addClass(t,e)},updateClasses:function(t,e,n){jsPlumb.each(t,(function(t){x(t,e,n)}))},setClass:function(t,e){null!=e&&jsPlumb.each(t,(function(t){b(t,e,e.split(/\s+/))}))},setPosition:function(t,e){t.style.left=e.left+"px",t.style.top=e.top+"px"},getPosition:function(t){var e=function(e){var n=t.style[e];return n?n.substring(0,n.length-2):0};return{left:e("left"),top:e("top")}},getStyle:function(t,e){return"undefined"!==typeof window.getComputedStyle?getComputedStyle(t,null).getPropertyValue(e):t.currentStyle[e]},getSelector:function(t,e){var n=null;return n=1===arguments.length?null!=t.nodeType?t:document.querySelectorAll(t):t.querySelectorAll(e),n},getOffset:function(t,e,n){t=jsPlumb.getElement(t),n=n||this.getContainer();var i={left:t.offsetLeft,top:t.offsetTop},r=e||null!=n&&t!==n&&t.offsetParent!==n?t.offsetParent:null,o=function(t){null!=t&&t!==document.body&&(t.scrollTop>0||t.scrollLeft>0)&&(i.left-=t.scrollLeft,i.top-=t.scrollTop)}.bind(this);while(null!=r)i.left+=r.offsetLeft,i.top+=r.offsetTop,o(r),r=e?r.offsetParent:r.offsetParent===n?null:r.offsetParent;if(null!=n&&!e&&(n.scrollTop>0||n.scrollLeft>0)){var s=null!=t.offsetParent?this.getStyle(t.offsetParent,"position"):"static",a=this.getStyle(t,"position");"absolute"!==a&&"fixed"!==a&&"absolute"!==s&&"fixed"!==s&&(i.left-=n.scrollLeft,i.top-=n.scrollTop)}return i},getPositionOnElement:function(t,e,n){var i="undefined"!==typeof e.getBoundingClientRect?e.getBoundingClientRect():{left:0,top:0,width:0,height:0},r=document.body,o=document.documentElement,s=window.pageYOffset||o.scrollTop||r.scrollTop,a=window.pageXOffset||o.scrollLeft||r.scrollLeft,u=o.clientTop||r.clientTop||0,l=o.clientLeft||r.clientLeft||0,c=0,h=0,d=i.top+s-u+c*n,f=i.left+a-l+h*n,p=jsPlumb.pageLocation(t),g=i.width||e.offsetWidth*n,v=i.height||e.offsetHeight*n,m=(p[0]-f)/g,b=(p[1]-d)/v;return[m,b]},getAbsolutePosition:function(t){var e=function(e){var n=t.style[e];if(n)return parseFloat(n.substring(0,n.length-2))};return[e("left"),e("top")]},setAbsolutePosition:function(t,e,n,i){n?this.animate(t,{left:"+="+(e[0]-n[0]),top:"+="+(e[1]-n[1])},i):(t.style.left=e[0]+"px",t.style.top=e[1]+"px")},getSize:function(t){return[t.offsetWidth,t.offsetHeight]},getWidth:function(t){return t.offsetWidth},getHeight:function(t){return t.offsetHeight},getRenderMode:function(){return"svg"},draggable:function(t,e){var i;return t=n.isArray(t)||null!=t.length&&!n.isString(t)?t:[t],Array.prototype.slice.call(t).forEach(function(t){i=this.info(t),i.el&&this._initDraggableIfNecessary(i.el,!0,e,i.id,!0)}.bind(this)),this},snapToGrid:function(t,e,n){var i=[],r=function(t){var r=this.info(t);if(null!=r.el&&r.el._katavorioDrag){var o=r.el._katavorioDrag.snap(e,n);this.revalidate(r.el),i.push([r.el,o])}}.bind(this);if(1===arguments.length||3===arguments.length)r(t,e,n);else{var o=this.getManagedElements();for(var s in o)r(s,arguments[0],arguments[1])}return i},initDraggable:function(t,e,n){s(this,n).draggable(t,e),t._jsPlumbDragOptions=e},destroyDraggable:function(t,e){s(this,e).destroyDraggable(t),t._jsPlumbDragOptions=null,t._jsPlumbRelatedElement=null},unbindDraggable:function(t,e,n,i){s(this,i).destroyDraggable(t,e,n)},setDraggable:function(t,e){return jsPlumb.each(t,function(t){this.isDragSupported(t)&&(this._draggableStates[this.getAttribute(t,"id")]=e,this.setElementDraggable(t,e))}.bind(this))},_draggableStates:{},toggleDraggable:function(t){var e;return jsPlumb.each(t,function(t){var n=this.getAttribute(t,"id");return e=null!=this._draggableStates[n]&&this._draggableStates[n],e=!e,this._draggableStates[n]=e,this.setDraggable(t,e),e}.bind(this)),e},_initDraggableIfNecessary:function(t,e,i,r,o){if(!jsPlumb.headless){var s=null!=e&&e;if(s&&jsPlumb.isDragSupported(t,this)){var c=i||this.Defaults.DragOptions;if(c=jsPlumb.extend({},c),jsPlumb.isAlreadyDraggable(t,this))i.force&&this.initDraggable(t,c);else{var h=jsPlumb.dragEvents.drag,d=jsPlumb.dragEvents.stop,f=jsPlumb.dragEvents.start;this.manage(r,t),c[f]=n.wrap(c[f],a.bind(this)),c[h]=n.wrap(c[h],u.bind(this)),c[d]=n.wrap(c[d],l.bind(this));var p=this.getId(t);this._draggableStates[p]=!0;var g=this._draggableStates[p];c.disabled=null!=g&&!g,this.initDraggable(t,c),this.getDragManager().register(t),o&&this.fire("elementDraggable",{el:t,options:c})}}}},animationSupported:!0,getElement:function(t){return null==t?null:(t="string"===typeof t?t:null==t.tagName&&null!=t.length&&null==t.enctype?t[0]:t,"string"===typeof t?document.getElementById(t):t)},removeElement:function(t){s(this).elementRemoved(t),this.getEventManager().remove(t)},doAnimate:function(t,n,i){i=i||{};var r=this.getOffset(t),o=c(r,n),s=o[0]-r.left,a=o[1]-r.top,u=i.duration||250,l=15,h=u/l,d=l/u*s,f=l/u*a,p=0,g=setInterval((function(){e.setPosition(t,{left:r.left+d*(p+1),top:r.top+f*(p+1)}),null!=i.step&&i.step(p,Math.ceil(h)),p++,p>=h&&(window.clearInterval(g),null!=i.complete&&i.complete())}),l)},destroyDroppable:function(t,e){s(this,e).destroyDroppable(t)},unbindDroppable:function(t,e,n,i){s(this,i).destroyDroppable(t,e,n)},droppable:function(t,e){var i;return t=n.isArray(t)||null!=t.length&&!n.isString(t)?t:[t],e=e||{},e.allowLoopback=!1,Array.prototype.slice.call(t).forEach(function(t){i=this.info(t),i.el&&this.initDroppable(i.el,e)}.bind(this)),this},initDroppable:function(t,e,n){s(this,n).droppable(t,e)},isAlreadyDraggable:function(t){return null!=t._katavorioDrag},isDragSupported:function(t,e){return!0},isDropSupported:function(t,e){return!0},isElementDraggable:function(t){return t=e.getElement(t),t._katavorioDrag&&t._katavorioDrag.isEnabled()},getDragObject:function(t){return t[0].drag.getDragElement()},getDragScope:function(t){return t._katavorioDrag&&t._katavorioDrag.scopes.join(" ")||""},getDropEvent:function(t){return t[0].e},getUIPosition:function(t,e){var n=t[0].el;if(null==n.offsetParent)return null;var i=t[0].finalPos||t[0].pos,r={left:i[0],top:i[1]};if(n._katavorioDrag&&n.offsetParent!==this.getContainer()){var o=this.getOffset(n.offsetParent);r.left+=o.left,r.top+=o.top}return r},setDragFilter:function(t,e,n){t._katavorioDrag&&t._katavorioDrag.setFilter(e,n)},setElementDraggable:function(t,n){t=e.getElement(t),t._katavorioDrag&&t._katavorioDrag.setEnabled(n)},setDragScope:function(t,e){t._katavorioDrag&&t._katavorioDrag.k.setDragScope(t,e)},setDropScope:function(t,e){t._katavorioDrop&&t._katavorioDrop.length>0&&t._katavorioDrop[0].k.setDropScope(t,e)},addToPosse:function(t,n){var i=Array.prototype.slice.call(arguments,1),r=s(this);e.each(t,(function(t){t=[e.getElement(t)],t.push.apply(t,i),r.addToPosse.apply(r,t)}))},setPosse:function(t,n){var i=Array.prototype.slice.call(arguments,1),r=s(this);e.each(t,(function(t){t=[e.getElement(t)],t.push.apply(t,i),r.setPosse.apply(r,t)}))},removeFromPosse:function(t,n){var i=Array.prototype.slice.call(arguments,1),r=s(this);e.each(t,(function(t){t=[e.getElement(t)],t.push.apply(t,i),r.removeFromPosse.apply(r,t)}))},removeFromAllPosses:function(t){var n=s(this);e.each(t,(function(t){n.removeFromAllPosses(e.getElement(t))}))},setPosseState:function(t,n,i){var r=s(this);e.each(t,(function(t){r.setPosseState(e.getElement(t),n,i)}))},dragEvents:{start:"start",stop:"stop",drag:"drag",step:"step",over:"over",out:"out",drop:"drop",complete:"complete",beforeStart:"beforeStart"},animEvents:{step:"step",complete:"complete"},stopDrag:function(t){t._katavorioDrag&&t._katavorioDrag.abort()},addToDragSelection:function(t){var e=this.getElement(t);null==e||!e._isJsPlumbGroup&&null!=e._jsPlumbGroup||s(this).select(t)},removeFromDragSelection:function(t){s(this).deselect(t)},getDragSelection:function(){return s(this).getSelection()},clearDragSelection:function(){s(this).deselectAll()},trigger:function(t,e,n,i){this.getEventManager().trigger(t,e,n,i)},doReset:function(){for(var t in this)0===t.indexOf("_katavorio_")&&this[t].reset()},getEventManager:function(){return o(this)},on:function(t,e,n){return this.getEventManager().on.apply(this,arguments),this},off:function(t,e,n){return this.getEventManager().off.apply(this,arguments),this}});var P=function(t){var e=function(){/complete|loaded|interactive/.test(document.readyState)&&"undefined"!==typeof document.body&&null!=document.body?t():setTimeout(e,9)};e()};P(e.init)}.call("undefined"!==typeof window?window:this)}}]);