(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3fa3429a"],{"161c":function(e,t,n){"use strict";n.r(t);var a=n("d4b6"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},"1eeb":function(e,t,n){"use strict";n.r(t);var a=n("b873"),r=n("8c13");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"1a7e8364",null);t["default"]=s.exports},"234a":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tree-container"},[n("div",{staticClass:"title"},[n("el-radio-group",{on:{change:e.changeType},model:{value:e.activeType,callback:function(t){e.activeType=t},expression:"activeType"}},[n("el-radio-button",{attrs:{label:"comp"}},[e._v("构件大类")]),n("el-radio-button",{attrs:{label:"part"}},[e._v("零件大类")])],1),n("div",{staticClass:"btn-x"},[n("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1),n("div",{staticClass:"tree-wrapper"},[n("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"tree",attrs:{"current-node-key":e.currentNodeKey,"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading","empty-text":"暂无数据","highlight-current":"","node-key":"Id","default-expand-all":"","expand-on-click-node":!1,data:e.treeData,props:{label:"Label",children:"Children"}},on:{"node-click":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node;t.data;return n("span",{staticClass:"custom-tree-node"},[n("svg-icon",{attrs:{"icon-class":a.expanded?"icon-folder-open":"icon-folder","class-name":"class-icon"}}),n("span",{staticClass:"cs-label",attrs:{title:a.label}},[e._v(e._s(a.label))])],1)}}])})],1)])},r=[]},"2a7f":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeletePartType=d,t.GetConsumingProcessAllList=y,t.GetFactoryPartTypeIndentifySetting=c,t.GetPartTypeEntity=p,t.GetPartTypeList=s,t.GetPartTypePageList=i,t.GetPartTypeTree=m,t.SaveConsumingProcessAllList=g,t.SavePartType=u,t.SavePartTypeIdentifySetting=f,t.SettingDefault=l;var r=a(n("b775")),o=a(n("4328"));function i(e){return(0,r.default)({url:"/PRO/PartType/GetPartTypePageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/PartType/GetPartTypeList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/PartType/SettingDefault",method:"post",data:o.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/PartType/SavePartType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/PartType/DeletePartType",method:"post",data:o.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/PartType/GetFactoryPartTypeIndentifySetting",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/PartType/SavePartTypeIdentifySetting",method:"post",data:e})}function m(e){return(0,r.default)({url:"/pro/parttype/GetPartTypeTree",method:"post",data:o.default.stringify(e)})}function p(e){return(0,r.default)({url:"/pro/parttype/GetPartTypeEntity",method:"post",data:o.default.stringify(e)})}function y(e){return(0,r.default)({url:"/PRO/PartType/GetConsumingProcessAllList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/PartType/SaveConsumingProcessAllList",method:"post",data:e})}},"2e8a":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=d,t.GetCompTypeTree=c,t.GetComponentTypeEntity=u,t.GetComponentTypeList=i,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=m,t.GetTypePageList=s,t.RestoreTemplateType=b,t.SavDeepenTemplateSetting=v,t.SaveCompTypeIdentifySetting=h,t.SaveComponentType=l,t.SaveProBimComponentType=f,t.UpdateColumnSetting=y,t.UpdateComponentPartTableSetting=p;var r=a(n("b775")),o=a(n("4328"));function i(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:o.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:o.default.stringify(e)})}function f(e){return(0,r.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,r.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function h(e){return(0,r.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function v(e){return(0,r.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function b(e){return(0,r.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"8c13":function(e,t,n){"use strict";n.r(t);var a=n("9061"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},9061:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("c14f")),o=a(n("1da1"));n("a9e3");var i=n("2e8a"),s=n("2a7f");t.default={props:{addLevel:{type:Number,default:1},parentId:{type:String,default:""},isComp:{type:Boolean,default:!0}},data:function(){return{btnLoading:!1,form:{Code:"",Name:"",Is_Component:"",Lead_Time:0},rules:{Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Code:[{required:!0,message:"请输入编码",trigger:"blur"}],Is_Component:[{required:!0,message:"请选择是否直发件",trigger:"change"}],Lead_Time:[{required:!0,message:"请输入周期",trigger:"blur"}]}}},computed:{levelName:function(){return 1===this.addLevel?"一级":2===this.addLevel?"二级":3===this.addLevel?"三级":""}},watch:{isComp:function(e){this.rules.Is_Component[0].required=e}},methods:{submit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,o.default)((0,r.default)().m((function t(n){var a,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(n){t.n=1;break}return t.a(2,!1);case 1:return e.form.Category=e.$route.query.typeCode,e.form.Professional_Id=e.$route.query.typeId,e.form.Level=e.addLevel,e.addLevel>1&&(e.form.Parent_Id=e.parentId),e.btnLoading=!0,a=e.isComp?i.SaveProBimComponentType:s.SavePartType,t.n=2,a(e.form);case 2:o=t.v,o.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("getTreeList")):e.$message({message:o.Message,type:"error"}),e.btnLoading=!1;case 3:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())}}}},"9c45":function(e,t,n){},a3a2:function(e,t,n){},a5af:function(e,t,n){"use strict";n.r(t);var a=n("234a"),r=n("161c");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("d3bb");var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"3a9fb4b6",null);t["default"]=s.exports},b26b7:function(e,t,n){"use strict";n.r(t);var a=n("e081"),r=n("ebc0");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("febd");var i=n("2877"),s=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"e7df1bf0",null);t["default"]=s.exports},b873:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[n("el-form-item",{attrs:{label:e.levelName+"大类名称",prop:"Name"}},[n("el-input",{attrs:{clearable:"",maxlength:"50"},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name","string"===typeof t?t.trim():t)},expression:"form.Name"}})],1),n("el-form-item",{attrs:{label:e.levelName+"大类编号",prop:"Code"}},[n("el-input",{attrs:{clearable:"",maxlength:"50"},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code","string"===typeof t?t.trim():t)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"生产周期",prop:"Lead_Time"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{clearable:""},model:{value:e.form.Lead_Time,callback:function(t){e.$set(e.form,"Lead_Time",e._n(t))},expression:"form.Lead_Time"}})],1),e.isComp?n("el-form-item",{attrs:{label:"直发件",prop:"Is_Component"}},[n("el-radio-group",{model:{value:e.form.Is_Component,callback:function(t){e.$set(e.form,"Is_Component",t)},expression:"form.Is_Component"}},[n("el-radio",{attrs:{label:!0}},[e._v("否")]),n("el-radio",{attrs:{label:!1}},[e._v("是")])],1)],1):e._e(),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("保存")])],1)],1)},r=[]},d3bb:function(e,t,n){"use strict";n("9c45")},d4b6:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("c14f")),o=a(n("1da1")),i=n("2e8a"),s=n("2a7f");t.default={data:function(){return{treeData:[],loading:!0,currentNodeKey:"",activeType:"comp"}},watch:{currentNodeKey:function(e){this.$emit("showRight",""!==e)}},mounted:function(){this.fetchData()},methods:{changeType:function(){this.$emit("changeType",this.activeType),this.currentNodeKey="",this.fetchData()},fetchData:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.loading=!0,"comp"!==e.activeType){t.n=2;break}return t.n=1,(0,i.GetCompTypeTree)({professional:e.$route.query.typeCode});case 1:n=t.v,t.n=4;break;case 2:return t.n=3,(0,s.GetPartTypeTree)({professionalId:e.$route.query.typeId});case 3:n=t.v;case 4:n.IsSucceed?e.treeData=n.Data:(e.$message({message:n.Message,type:"error"}),e.treeData=[]),e.loading=!1,e.currentNodeKey&&e.setTreeNode();case 5:return t.a(2)}}),t)})))()},setTreeNode:function(){var e=this;this.$emit("nodeClick",this.$refs["tree"].getNode(this.currentNodeKey)),this.$nextTick((function(t){e.$refs["tree"].setCurrentKey(e.currentNodeKey)}))},handleAdd:function(){this.$emit("AddFirst")},handleNodeClick:function(e,t){this.currentNodeKey=e.Id,this.$emit("nodeClick",t)},resetKey:function(e){e===this.currentNodeKey&&(this.currentNodeKey="")}}}},e081:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"container abs100"},[n("div",{staticClass:"top-x"},[n("el-button",{on:{click:e.goBack}},[e._v("返回")])],1),n("div",{staticClass:"card-x"},[n("tree-data",{ref:"tree",on:{nodeClick:e.nodeClick,changeType:e.changeType,AddFirst:e.addFirst,showRight:e.showRight}}),n("div",{staticClass:"right-card"},[e.showForm?n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[n("el-form-item",{attrs:{label:e.levelName+"大类名称",prop:"Name"}},[n("el-input",{attrs:{clearable:"",maxlength:"50"},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name","string"===typeof t?t.trim():t)},expression:"form.Name"}})],1),n("el-form-item",{attrs:{label:e.levelName+"大类编号",prop:"Code"}},[n("el-input",{attrs:{disabled:""},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"生产周期",prop:"Lead_Time"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{clearable:""},model:{value:e.form.Lead_Time,callback:function(t){e.$set(e.form,"Lead_Time",e._n(t))},expression:"form.Lead_Time"}})],1),e.isComp?n("el-form-item",{attrs:{label:"直发件",prop:"Is_Component"}},[n("el-radio-group",{model:{value:e.form.Is_Component,callback:function(t){e.$set(e.form,"Is_Component",t)},expression:"form.Is_Component"}},[n("el-radio",{attrs:{label:!0}},[e._v("否")]),n("el-radio",{attrs:{label:!1}},[e._v("是")])],1)],1):e._e(),n("el-form-item",[e.level<3?n("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:e.addNext}},[e._v("新增下一级")]):e._e()],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",loading:e.submitLoading,disabled:e.isDefault},on:{click:e.submit}},[e._v("保存")]),n("el-button",{attrs:{type:"danger",loading:e.deleteLoading,disabled:e.hasChildrenNode||e.isDefault},on:{click:e.handleDelete}},[e._v("删除")])],1)],1):e._e()],1)],1),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?n(e.currentComponent,{ref:"content",tag:"component",attrs:{"add-level":e.addLevel,"parent-id":e.parentId,"is-comp":e.isComp},on:{close:e.handleClose,getTreeList:e.getTreeData}}):e._e()],1)],1)},r=[]},ebc0:function(e,t,n){"use strict";n.r(t);var a=n("ec3d"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},ec3d:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("c14f")),o=a(n("1da1")),i=n("ed08"),s=a(n("a5af")),l=a(n("1eeb")),u=n("2e8a"),d=n("2a7f");t.default={name:"ProfessionalCategoryListInfo",components:{TreeData:s.default,Add:l.default},data:function(){return{level:1,addLevel:void 0,dialogVisible:!1,submitLoading:!1,deleteLoading:!1,showForm:!1,hasChildrenNode:!0,currentComponent:"",parentId:"",title:"",form:{Name:"",Code:"",Is_Component:"",Lead_Time:0},rules:{Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Code:[{required:!0,message:"请输入编码",trigger:"blur"}],Is_Component:[{required:!0,message:"请选择是否直发件",trigger:"change"}],Lead_Time:[{required:!0,message:"请输入周期",trigger:"blur"}]},Is_Component:"",activeType:"comp",isDefault:!1}},computed:{levelName:function(){return 1===this.level?"一级":2===this.level?"二级":3===this.level?"三级":""},isComp:function(){return"comp"===this.activeType}},watch:{isComp:function(e){this.rules.Is_Component[0].required=e}},methods:{addNext:function(){this.currentComponent="Add",this.addLevel=this.level+1,this.title="新增下一级",this.parentId=this.form.Id,this.dialogVisible=!0},showRight:function(e){this.showForm=e},submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.Is_Component!=e.form.Is_Component&&e.isComp?e.$confirm("直发件属性不会同步到已导入构件清单中，确认修改？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitConfirm()})).catch((function(){e.$message({type:"info",message:"已取消修改"})})):e.submitConfirm()}))},submitConfirm:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var n,a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.submitLoading=!0,n=e.isComp?u.SaveProBimComponentType:d.SavePartType,t.n=1,n(e.form);case 1:a=t.v,a.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.getTreeData()):e.$message({message:a.Message,type:"error"}),e.submitLoading=!1;case 2:return t.a(2)}}),t)})))()},getTreeData:function(){this.$refs["tree"].fetchData()},addFirst:function(){this.currentComponent="Add",this.title="新增类别",this.addLevel=1,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},changeType:function(e){this.activeType=e},nodeClick:function(e){this.level=e.level,this.hasChildrenNode=e.childNodes.length>0,this.getInfo(e.data.Id)},getInfo:function(e){var t=this;return(0,o.default)((0,r.default)().m((function n(){var a,o;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return a=t.isComp?u.GetComponentTypeEntity:d.GetPartTypeEntity,n.n=1,a({id:e});case 1:o=n.v,o.IsSucceed?(Object.assign(t.form,o.Data),t.isComp?(t.isDefault=!1,t.Is_Component=o.Data.Is_Component):t.isDefault=!!o.Data.Is_Default):t.$message({message:o.Message,type:"error"});case 2:return n.a(2)}}),n)})))()},goBack:function(){(0,i.closeTagView)(this.$store,this.$route)},handleDelete:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.$confirm("是否删除当前类别?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((0,o.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.deleteLoading=!0,!e.isComp){t.n=2;break}return t.n=1,(0,u.DeleteComponentType)({ids:e.form.Id});case 1:n=t.v,t.n=4;break;case 2:return t.n=3,(0,d.DeletePartType)({id:e.form.Id});case 3:n=t.v;case 4:e.deleteLoading=!1,n.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.getTreeData(),e.$refs["tree"].resetKey(e.form.Id)):e.$message({message:n.Message,type:"error"});case 5:return t.a(2)}}),t)})))).catch((function(){e.$message({type:"info",message:"已取消删除"})}));case 1:return t.a(2)}}),t)})))()}}}},febd:function(e,t,n){"use strict";n("a3a2")}}]);