(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4d089e42"],{2419:function(e,t,a){"use strict";a.r(t);var n=a("e611"),i=a("f8e8");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("efa5");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"4ff981a4",null);t["default"]=l.exports},3782:function(e,t,a){},"4f39":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.parseTime=r,t.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var i=n(a("53ca"));function r(e,t){if(0===arguments.length||!e)return null;var a,n=t||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,i.default)(e)?a=e:("string"===typeof e&&(e=/^[0-9]+$/.test(e)?parseInt(e):e.replace(new RegExp(/-/gm),"/")),"number"===typeof e&&10===e.toString().length&&(e*=1e3),a=new Date(e));var r={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=n.replace(/{([ymdhisa])+}/g,(function(e,t){var a=r[t];return"a"===t?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!e)return"";if(-1!==e.indexOf("~")){var a=e.split("~"),n=r(new Date(a[0]),t)+" ~ "+r(new Date(a[1]),t);return n}return e&&e.length>0?r(new Date(e),t):void 0}},"95c1":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var i=n(a("c14f")),r=n(a("1da1")),o=n(a("ac03")),l=a("cf45"),c=a("4f39"),s=n(a("3502")),u=a("767d"),d=a("bc41"),f=a("05e0");t.default={name:"OMAMarkingFeeDetailInfo",components:{VTable:s.default},mixins:[o.default],data:function(){return{form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],showExport:!1}},computed:{curTitle:function(){return"".concat((0,c.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")}},beforeCreate:function(){this.curModuleKey=d.curModuleKey},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.showExport=e.getRoles("OMAMarkingDetailFyExport"),e.form.FactoryId=e.factoryId,e.$route.query.d?e.form.StatisticalDate=e.$route.query.d:e.form.StatisticalDate=e.originDate,t.n=1,(0,l.getDictionary)("FeeType");case 1:return a=t.v,e.feeTypeOption=a.filter((function(e){return!["FeeRateWage","FeePiecework"].includes(e.Value)})),e.fetchData(),t.n=2,(0,l.getDictionary)("Engineering Status");case 2:e.projectOption=t.v;case 3:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.checkDate()&&(this.loading=!0,(0,u.GetYXFeesDailyDetailList)(this.form).then((function(t){if(t.IsSucceed){e.tableData=((null===t||void 0===t?void 0:t.Data)||[]).map((function(e){var t=e.Fee_Item;return t.forEach((function(t){e[t.Code]=t.Value})),e}));var a=e.setTotalData(e.tableData,e.generateColumn()),n=a.column;e.columns=n,e.$refs["tb"].setColumns(n)}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1})))},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var e=180,t=[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:4},field:"ProjectAbbreviation",minWidth:f.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:f.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:f.ProjectStatusW}]}]},{title:"公共成本摊销系数(%)",params:{rowSpan:2},fixed:"left",children:[{params:{none:"none"},children:[{params:{none:"none"},field:"PublicCostAmortizationFactor",minWidth:180}]}]},{title:"生产项目辅材成本",children:[{title:"辅材合价(元)",children:[{minWidth:e,field:"Project_Aux_Fees",title:0,isTotal:!0}]}]},{title:"生产公共辅材成本",children:[{title:"辅材合价(元)",children:[{minWidth:e,field:"Public_Aux_Fees",title:0,isTotal:!0}]}]},{title:"管理费用",children:[]}];if(this.feeTypeOption.length){var a=[];this.feeTypeOption.forEach((function(t,n){var i={title:t.Display_Name+"(元)",children:[{minWidth:e,field:t.Value,title:0,isTotal:!0}]};a.push(i)})),a.push({title:"小计(元)",children:[{minWidth:e,field:"total",title:0,isTotal:!0}]}),t[t.length-1].children=a}return t}}}},cf45:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=i,a("d3b7");var n=a("6186");function i(e){return new Promise((function(t,a){(0,n.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},e611:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":e.pickerOptions},on:{change:e.fetchData},model:{value:e.form.StatisticalDate,callback:function(t){e.$set(e.form,"StatisticalDate",t)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:e.form.SearchKey,callback:function(t){e.$set(e.form,"SearchKey",t)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.ProjectStatus,callback:function(t){e.$set(e.form,"ProjectStatus",t)},expression:"form.ProjectStatus"}},e._l(e.projectOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("查询")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("label",[e._v("数据列表")]),a("div",{staticClass:"btn-x"},[e.showExport?a("el-button",{attrs:{disabled:e.isEmpty},on:{click:function(t){return e.handleExport(e.curTitle)}}},[e._v("导出报表")]):e._e()],1)]),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:e.loading,total:e.total},on:{setEmpty:e.setEmpty,pageChange:e.changePage}})],1)],1)])},i=[]},efa5:function(e,t,a){"use strict";a("3782")},f8e8:function(e,t,a){"use strict";a.r(t);var n=a("95c1"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a}}]);