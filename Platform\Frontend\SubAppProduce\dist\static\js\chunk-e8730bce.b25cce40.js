(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-e8730bce"],{7053:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{width:"100%",height:"calc(100vh - 90px)"}},[e.iframeUrl?r("iframe",{staticClass:"pc iframe",staticStyle:{width:"100%",height:"100%"},attrs:{id:"iframeId",src:e.iframeUrl,frameborder:"0",scrolling:"auto"}}):e._e()])},n=[]},c1f6:function(e,t,r){"use strict";r.r(t);var a=r("cfb9"),n=r.n(a);for(var c in a)["default"].indexOf(c)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(c);t["default"]=n.a},ca1d:function(e,t,r){"use strict";r.r(t);var a=r("7053"),n=r("c1f6");for(var c in n)["default"].indexOf(c)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(c);var u=r("2877"),o=Object(u["a"])(n["default"],a["a"],a["b"],!1,null,"4ccef0be",null);t["default"]=o.exports},cfb9:function(e,t,r){"use strict";var a=r("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("99af");var n=a(r("c14f")),c=a(r("1da1")),u=r("c24f");t.default={name:"ModelCompare",data:function(){return{iframeUrl:""}},mounted:function(){this.getUrl()},methods:{getUrl:function(){var e=this;return(0,c.default)((0,n.default)().m((function t(){var r,a,c;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:t.n=1;break;case 1:return t.n=2,(0,u.getConfigure)({code:"glendale_url"});case 2:r=t.v.Data;case 3:a=e.$route.query.modelId,c=e.$route.query.modelName,e.iframeUrl="".concat(r,"/#/modelLinkage?projectId=").concat(localStorage.getItem("CurReferenceId"),"&token=").concat(e.$store.state.user.token,"&auth_id=").concat(e.$store.state.user.Last_Working_Object_Id,"&modelId=").concat(a,"&modelName=").concat(c);case 4:return t.a(2)}}),t)})))()}}}}}]);