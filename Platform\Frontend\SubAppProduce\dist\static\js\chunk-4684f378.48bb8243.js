(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4684f378"],{"0974":function(e,t,a){},"2c83":function(e,t,a){"use strict";a.r(t);var r=a("8e47"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"433e":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("div",{staticClass:"purchase-order-detail"},[a("div",{staticClass:"title"},[e._v("采购订单信息")]),a("div",{staticClass:"detail-info"},[a("el-row",[a("el-col",{attrs:{span:6}},[a("span",[e._v("采购单号: ")]),a("span",[e._v(e._s(e.orderBaseData.Order_Code))])]),a("el-col",{attrs:{span:6}},[a("span",[e._v("申请时间: ")]),a("span",[e._v(e._s(e.orderBaseData.Apply_Date))])]),a("el-col",{attrs:{span:6}},[a("span",[e._v("制单部门: ")]),a("span",[e._v(e._s(e.orderBaseData.DepartmentName))])]),a("el-col",{attrs:{span:6}},[a("span",[e._v("制单人: ")]),a("span",[e._v(e._s(e.orderBaseData.Apply_UserName))])])],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("span",[e._v("供应商: ")]),a("span",[e._v(e._s(e.orderBaseData.VendorName||"/"))])]),a("el-col",{attrs:{span:6}},[a("span",[e._v("采购类型: ")]),a("span",[e._v(e._s(e.orderBaseData.PurchaseTypeDesc))])]),a("el-col",{attrs:{span:6}},[a("span",[e._v("采购工厂: ")]),a("span",[e._v(e._s(e.orderBaseData.FactoryName))])]),a("el-col",{attrs:{span:6}},[a("span",[e._v("采购金额: ")]),a("span",[e._v(e._s(e.orderBaseData.Purchase_Price))])])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("span",[e._v("备注: ")]),a("span",[e._v(e._s(e.orderBaseData.Memo||"/"))])])],1),a("el-row",[a("el-col",{staticStyle:{display:"flex","justify-content":"flex-start"},attrs:{span:24}},[a("span",[e._v("文件: ")]),e.AttList.length>0?a("div",e._l(e.AttList,(function(t,r){return a("div",{key:r,staticStyle:{cursor:"pointer"},on:{click:function(a){return e.openFile(t)}}},[a("i",{staticClass:"el-icon-link"}),e._v(e._s(t.File_Name))])})),0):a("div",[e._v("/")])])],1)],1)]),a("div",{staticClass:"detail-title"},[e._v("采购订单明细")]),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"72px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料名称",prop:"Material_Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"关键字模糊搜索",type:"text"},model:{value:e.form.Material_Name,callback:function(t){e.$set(e.form,"Material_Name","string"===typeof t?t.trim():t)},expression:"form.Material_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格",prop:"Specs"}},[a("el-input",{attrs:{clearable:"",placeholder:"关键字模糊搜索",type:"text"},model:{value:e.form.Specs,callback:function(t){e.$set(e.form,"Specs","string"===typeof t?t.trim():t)},expression:"form.Specs"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"ProjectName"}},[a("el-select",{staticClass:"input-query",attrs:{filterable:"",clearable:""},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.ProjectList,(function(e,t){return a("el-option",{key:t,attrs:{label:e.ProjectName,value:e.ProjectId}})})),1)],1)],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.tableDataFilter,resizable:"","tooltip-config":{enterable:!0}}},[a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["InventoryAmout"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a["InventoryAmout"])+"/"+e._s((a["InventoryQuantity"]/1e3).toFixed(5))+" ")]}}:"LeftAmount"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.bePurchased)+" ")]}}:"LeftQuantity"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.bePurchasedWeight)+" ")]}}:"Tax_Total_Price"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a["Tax_Total_Price"])+" ")]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("关闭")])],1)],1)},n=[]},"4dff":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1"));a("4de4"),a("caad"),a("d81d"),a("14d9"),a("13d5"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("b680"),a("d3b7"),a("07ac"),a("2532"),a("159b");var o=a("93aa"),s=a("6186"),l=a("ed08");t.default={props:{purchaseId:{type:String,default:""}},data:function(){return{form:{Material_Name:"",Specs:"",ProjectId:""},orderBaseData:{},columns:[],tableData:[],AttList:[],ProjectList:[],tbLoading:!1}},computed:{tableDataFilter:function(){var e=this,t=this.tableData.filter((function(t){return(""===e.form.Material_Name||t.Material_Name.includes(e.form.Material_Name))&&(""===e.form.Specs||t.Specs.includes(e.form.Specs))&&(""===e.form.ProjectId||t.Project_Id===e.form.ProjectId)}));return t}},watch:{purchaseId:{handler:function(e,t){e&&(this.form={Material_Name:"",Specs:"",ProjectId:""},this.orderBaseData={},this.tableData=[],this.AttList=[],this.ProjectList=[])},immediate:!0,deep:!0}},created:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig();case 1:return t.a(2)}}),t)})))()},mounted:function(){},methods:{sumPrice:function(e){var t=Number(e.Quantity),a=Number(e.Tax_Unit_Price);if(t&&a){var r=(t*a).toFixed(3);return e.money=r,r}return e.money=0,""},init:function(){this.tbLoading=!0,this.getTableConfig()},getTableConfig:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){var a,r;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetGridByCode)({code:"PurchaseOrderDetailGB"});case 1:a=t.v,a.IsSucceed?(r=["No_Tax_Total_Price","Cert_No_Tax_Total_Price","VoucherTaxAmount"],e.columns=a.Data.ColumnList.filter((function(e){return!r.includes(e.Code)})),e.fetchData()):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},fetchData:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:(0,o.GetOrderDetail)({Id:e.purchaseId}).then((function(t){if(t.IsSucceed){e.orderBaseData=t.Data,e.orderBaseData.Apply_Date=(0,l.parseTime)(new Date(e.orderBaseData.Apply_Date),"{y}-{m}-{d}"),e.AttList=t.Data.AttList;var a=t.Data.DetailList;e.tableData=a.map((function(t){return t.Purchase_Time=t.Purchase_Time?(0,l.parseTime)(new Date(t.Purchase_Time),"{y}-{m}-{d}"):t.Purchase_Time,t.Delivery_Time=t.Delivery_Time?(0,l.parseTime)(new Date(t.Delivery_Time),"{y}-{m}-{d}"):t.Delivery_Time,t.Ori_Amount=t.Amount,t.Ori_Quantity=t.Quantity,t.VoucherWeight=(((null===t||void 0===t?void 0:t.VoucherWeight)||0)/1e3).toFixed(5),t.LeftQuantity=(((null===t||void 0===t?void 0:t.LeftQuantity)||0)/1e3).toFixed(5),t.TotalInStoreWeight=(t.TotalInStoreWeight?t.TotalInStoreWeight/1e3:0).toFixed(5),t.Tax_Total_Price=e.sumPrice(t),t.Quantity=Number(t.Quantity/1e3).toFixed(5),t.Tax_Unit_Price=Number(1e3*t.Tax_Unit_Price).toFixed(2),t.bePurchased=(t.Amount||0)-(t.Ware_Quantity||0),t.bePurchasedWeight=(((t.Ori_Quantity||0)-(t.Ware_Amount||0))/1e3).toFixed(5),t}));var r=[];a.forEach((function(e){var t={};t.ProjectId=e.Project_Id,t.ProjectName=e.ProjectName,r.push(t)})),e.ProjectList=Object.values(r.reduce((function(e,t){return e[t.ProjectId]=t,e}),{})),e.tbLoading=!1}else e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},handleClose:function(){this.$emit("close")},openFile:function(e){(0,s.GetOssUrl)({url:e.File_Url,day:30}).then((function(e){window.open(e.Data)}))}}}},"589bb":function(e,t,a){},"770d":function(e,t,a){"use strict";a("589bb")},"79f7":function(e,t,a){"use strict";a("0974")},"8e47":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1")),o=r(a("9fb0")),s=a("ed08"),l=a("9002"),c=r(a("333d")),u=a("93aa"),d=a("c685");t.default={name:"PRORawPurchaseOrderList",components:{PurchaseOrderDetail:o.default,Pagination:c.default},data:function(){return{searchForm:{purchaseNo:""},columns:[],fTable:[],tbConfig:{},inStoreNo:this.$route.query.inStoreNo,queryInfo:{Page:1,PageSize:20,total:0},tablePageSize:d.tablePageSize,tbLoading:!1,visiblePurchaseOrderDetail:!1,CurPurchaseId:""}},computed:{},watch:{},created:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getConfig();case 1:return t.a(2)}}),t)})))()},mounted:function(){},methods:{getConfig:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,(0,l.getTableConfig)("PRORawOrderList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},fetchData:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:(0,u.GetRawOrderList)({inStoreNo:e.inStoreNo,purchaseNo:e.searchForm.purchaseNo}).then((function(t){t.IsSucceed?e.fTable=t.Data:e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},changePage:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10);case 1:return t.a(2)}}),t)})))()},handlePurchaseNo:function(e){var t=this;this.CurPurchaseId=e.Id,this.visiblePurchaseOrderDetail=!0,this.$nextTick((function(e){t.$refs["PurchaseOrderDetailRef"].init()}))},handleClose:function(e){this.visiblePurchaseOrderDetail=!1},toBack:function(){(0,s.closeTagView)(this.$store,this.$route)}}}},9002:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTableConfig=void 0,a("d3b7");var r=a("6186"),n=void 0;t.getTableConfig=function(e){return new Promise((function(t,a){(0,r.GetGridByCode)({code:e}).then((function(e){var a=e.IsSucceed,r=e.Data,i=e.Message;if(a){var o=r.ColumnList||[];t(o)}else n.$message({message:i,type:"error"})}))}))}},"9fb0":function(e,t,a){"use strict";a.r(t);var r=a("433e"),n=a("b252");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("79f7");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"12b7da1c",null);t["default"]=s.exports},a743:function(e,t,a){"use strict";a.r(t);var r=a("a7d0"),n=a("2c83");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("770d");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"33afb09a",null);t["default"]=s.exports},a7d0:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"top-btn",on:{click:e.toBack}},[a("el-button",[e._v("返回")])],1),a("el-card",{staticClass:"box-card box-card-tb"},[a("div",{staticClass:"toolbar-container"},[a("el-form",{ref:"searchFormRef",staticClass:"search-form",attrs:{inline:"",model:e.searchForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"采购单号",prop:"purchaseNo"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{clearable:"",placeholder:"关键字模糊搜索",type:"text"},model:{value:e.searchForm.purchaseNo,callback:function(t){e.$set(e.searchForm,"purchaseNo","string"===typeof t?t.trim():t)},expression:"searchForm.purchaseNo"}})],1),a("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"0"}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("searchFormRef")}}},[e._v("重置")])],1)],1)],1),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}}},[a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["Create_Time"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d} {h}:{i}"))+" ")]}}:"Purchase_No"===t.Code?{key:"default",fn:function(r){var n=r.row;return[a("span",{staticStyle:{color:"#1890ff",cursor:"pointer"},on:{click:function(t){return e.handlePurchaseNo(n)}}},[e._v(" "+e._s(e._f("displayValue")(n[t.Code])))])]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"采购订单详情",visible:e.visiblePurchaseOrderDetail,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.visiblePurchaseOrderDetail=t},close:e.handleClose}},[a("purchase-order-detail",{ref:"PurchaseOrderDetailRef",attrs:{"purchase-id":e.CurPurchaseId},on:{close:e.handleClose}})],1)],1)},n=[]},b252:function(e,t,a){"use strict";a.r(t);var r=a("4dff"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a}}]);