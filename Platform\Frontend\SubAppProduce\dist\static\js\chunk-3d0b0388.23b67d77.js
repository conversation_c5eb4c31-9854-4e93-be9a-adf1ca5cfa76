(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3d0b0388"],{"04a69":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("5530")),i=o(a("c14f")),r=o(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("5319"),a("5b81"),a("498a"),a("ddb0");var c=o(a("e8fe")),l=o(a("64868")),s=a("221f"),u=o(a("0f97")),d=o(a("a888")),f=a("fd31"),h=a("8975"),p=o(a("2082")),m=a("7de9"),_=a("ed08"),b=o(a("333d")),g=a("c685"),y=o(a("15ac"));t.default={directives:{elDragDialog:d.default},components:{Pagination:b.default,DynamicDataTable:u.default,rectificationDialog:l.default,addDialog:c.default},mixins:[p.default,y.default],props:{searchDetail:{type:Object,default:function(){return{}}}},data:function(){return{selectList:[],width:"60%",code:"",TypeId:"",typeOption:"",dialogVisible:!1,dialogVisible2:!1,loading:!1,dialogTitle:"",Ismodal:!0,dialogData:{},currentComponent:"",tbConfig:{},Data:[],Date_Time:"",columns:[],tbData:[],queryInfo:{Page:1,PageSize:g.tablePageSize[0]},total:0,tablePageSize:g.tablePageSize,gridCode:"Pro_Inpection_summary_list",searchHeight:0,CheckResultData:[],CheckNodeList:[],CheckObjectData:[],check_object_id:"",ProjectNameData:[],check_object_Name:"",addPageArray:[{path:this.$route.path+"/check",hidden:!0,component:function(){return a.e("chunk-486711de").then(a.bind(null,"9a46"))},name:"PROQualityInfoSummary",meta:{title:"查看"}}]}},mounted:function(){this.getTypeList()},methods:{handleSearch:function(){},getTypeList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,o,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,o=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(o),e.typeOption.length>0&&(e.TypeId=null===(n=e.typeOption[0])||void 0===n?void 0:n.Id,e.code=e.typeOption.find((function(t){return t.Id===e.TypeId})).Code,e.getTableConfig(e.gridCode+","+e.code)),e.fetchData()):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},handelWaitInspect:function(e){var t=this;this.generateComponent("查看","addDialog"),this.width="500px",this.$nextTick((function(a){t.$refs["content"].init("查看",e)}))},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e);var a=JSON.parse(JSON.stringify(this.searchDetail)),o=a.SteelName.trim().replaceAll(" ","\n");a.Pick_Date&&2===a.Pick_Date.length?(a.BeginDate=a.Pick_Date[0],a.EndDate=a.Pick_Date[1]):(a.BeginDate=null,a.EndDate=null),this.loading=!0,(0,s.GetPageQualitySummary)((0,n.default)((0,n.default)({pageInfo:this.queryInfo},a),{},{SteelName:o,Check_Style:1})).then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1}))},setGridData:function(e){this.tbData=this.tbData=e.Data.map((function(e){return e.Id=e.SheetId,e.Rectify_Date=e.Rectify_Date?(0,h.timeFormat)(e.Rectify_Date,"{y}-{m}-{d}"):"-",e.Pick_Date=e.Pick_Date?(0,h.timeFormat)(e.Pick_Date,"{y}-{m}-{d}"):"-",e})),this.total=e.TotalCount},multiSelectedChange:function(e){this.selectList=e.records},generateComponent:function(e,t){this.dialogTitle=e,this.currentComponent=t,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.fetchData(1)},getrectificationRecord:function(e){var t=this;(0,s.RectificationRecord)({sheetid:e.SheetId}).then((function(a){a.IsSucceed?0===a.Data.length?t.$message({type:"error",message:"暂无操作记录"}):(t.generateComponent("整改记录","rectificationDialog"),t.$nextTick((function(a){t.$refs["content"].init(e)}))):t.$message({type:"error",message:a.Message})}))},handelrectifiction:function(e){this.getrectificationRecord(e)},handleInfo:function(e){this.$router.push({name:"PROQualityInfoSummary",query:{pg_redirect:"PROQualitySummary",sheetId:e,isCheck:!0}})},exportTb:function(){var e=this,t=this.selectList.map((function(e){return e.SheetId}));this.$emit("setExportLoading",!0);var a=JSON.parse(JSON.stringify(this.searchDetail)),o=a.SteelName.trim().replaceAll(" ","\n");a.Pick_Date&&2===a.Pick_Date.length?(a.BeginDate=a.Pick_Date[0],a.EndDate=a.Pick_Date[1]):(a.BeginDate=null,a.EndDate=null),(0,m.ExportInspsectionSummaryInfo)((0,n.default)((0,n.default)({pageInfo:this.queryInfo},a),{},{SteelName:o,Check_Style:1,SheetIds:t})).then((function(t){t.IsSucceed?window.open((0,_.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"}),e.$emit("setExportLoading",!1)}))}}}},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=r,Math.easeInOutQuad=function(e,t,a,o){return e/=o/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(e,t,a){var r=i(),c=e-r,l=20,s=0;t="undefined"===typeof t?500:t;var u=function(){s+=l;var e=Math.easeInOutQuad(s,r,c,t);n(e),s<t?o(u):a&&"function"===typeof a&&a()};u()}},"0de6":function(e,t,a){},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var o=a("6186"),n=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,o.GetGridByCode)({code:e,IsAll:a}).then((function(e){var o=e.IsSucceed,r=e.Data,c=e.Message;if(o){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,r.Grid),l=a?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+r.Grid.Row_Number||n.tablePageSize[0]),i(t.columns)}else t.$message({message:c,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,o=e.type;this.queryInfo.Page="limit"===o?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var o=0;o<this.columns.length;o++){var n=this.columns[o];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"17ff":function(e,t,a){"use strict";a.r(t);var o=a("f53b"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"184f":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("div",{staticClass:"cs-bottom-wapper"},[a("div",{staticClass:"fff tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.loading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0},"checkbox-config":{checkField:"checked",trigger:"row"},"row-config":{isHover:!0}},on:{"checkbox-all":e.multiSelectedChange,"checkbox-change":e.multiSelectedChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"44"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["Check_Result"===t.Code?{key:"default",fn:function(t){var o=t.row;return[o.Check_Result?["合格"===o.Check_Result?a("el-tag",{attrs:{type:"success"}},[e._v(e._s(o.Check_Result))]):a("el-tag",{attrs:{type:"danger"}},[e._v(e._s(o.Check_Result))])]:a("span",[e._v("-")])]}}:"Status"===t.Code?{key:"default",fn:function(t){var o=t.row;return["已完成"===o.Status?a("span",{staticClass:"by-dot by-dot-success"},[e._v(" "+e._s(o.Status||"-")+" ")]):"待复核"===o.Status||"待整改"===o.Status?a("span",{staticClass:"by-dot by-dot-primary"},[e._v(" "+e._s(o.Status||"-")+" ")]):"待质检"===o.Status||"草稿"===o.Status?a("span",{staticClass:"by-dot by-dot-info"},[e._v(" "+e._s(o.Status||"-")+" ")]):a("span",[e._v(" "+e._s(o.Status||"-")+" ")])]}}:{key:"default",fn:function(o){var n=o.row;return[a("span",[e._v(e._s(""===n[t.Code]||null==n[t.Code]?"-":n[t.Code]))])]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",align:"center",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return["待质检"!==o.Status&&"草稿"!==o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleInfo(o.SheetId)}}},[e._v("查看")]):e._e(),"待质检"===o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handelWaitInspect(o)}}},[e._v("查看")]):e._e()]}}])})],2)],1),a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.selectList.length)+" 条数据 ")]),a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)]),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",on:{close:e.handleClose}})],1):e._e()],1)},n=[]},"221f":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Add=l,t.AddLanch=c,t.DelLanch=u,t.EditLanch=f,t.EntityLanch=i,t.GetEditById=d,t.GetPageQualitySummary=p,t.GetPartAndSteelBacrode=r,t.RectificationRecord=h,t.SubmitLanch=s;var n=o(a("b775"));function i(e){return(0,n.default)({url:"/PRO/Inspection/EntityLanch",method:"post",data:e})}function r(e){return(0,n.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Inspection/Add",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Inspection/EditLanch",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Inspection/GetPageQualitySummary",method:"post",data:e})}},"2d6d":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var n=o(a("ade3")),i=a("7de9"),r=a("6186"),c=a("221f"),l=o(a("0f97"));t.default={components:{DynamicDataTable:l.default},data:function(){return{chooseTitle:"",Code:"",loading:!1,tbConfig:{},columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:10},selectList:[],gridCode:"pro_bitch_steel_list",form:(0,n.default)((0,n.default)({Check_Object_Type:"",Check_Node_Id:"",Check_Type:"",Number:"",Qualified_Count:0,Unqualified_Count:0,ZG_Code:"",Rectifier_Id:""},"Rectifier_Id",""),"Status",""),CheckTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],CheckNodeList:[],CheckObjectData:[],plm_Factory_Sheets:[],detailInfo:{},UserList:[]}},mounted:function(){this.getCheckType(),this.getFactoryPeoplelist()},methods:{init:function(e,t){this.detailInfo=t,"构件"==t.Check_Object_Type?this.gridCode="pro_bitch_steel_list":"零件"==t.Check_Object_Type&&(this.gridCode="pro_bitch_part_list"),this.EditInfo(t),this.getCheckType(),this.Code=e,this.shawDialog=!0,this.getGridByCode()},EditInfo:function(e){var t=this;(0,c.GetEditById)({Sheetid:e.SheetId}).then((function(a){if(a.IsSucceed){t.plm_Factory_Sheets=a.Data.plm_Factory_Sheets[0],t.form.Check_Object_Type=t.plm_Factory_Sheets.Check_Object_Type_Id,t.form.Number=t.plm_Factory_Sheets.Number,t.form.Check_Result=t.plm_Factory_Sheets.Sheet_Result,t.form.ZG_Code=e.Rectifier_Code,t.form.Rectify_Date=e.Rectify_Date,t.form.Rectifier_Id=e.Rectifier_Id,t.form.Status=e.Status,t.changeObject(t.form.Check_Object_Type),t.form.Check_Node_Id=t.plm_Factory_Sheets.Check_Node_Id,t.form.Check_Type=t.plm_Factory_Sheets.Check_Type,"构件"==e.Check_Object_Type?t.tbData=a.Data.Reads:"零件"==e.Check_Object_Type&&(t.tbData=a.Data.Parts),t.form.Qualified_Count=0,t.form.Unqualified_Count=0;for(var o=0;o<a.Data.plm_Factory_Sheets.length;o++){for(var n=a.Data.plm_Factory_Sheets[o].Check_Object_Id,i=0;i<t.tbData.length;i++)"构件"==e.Check_Object_Type?n===t.tbData[i].SteelUnique&&(t.$set(t.tbData[i],"Unqualified_Count",a.Data.plm_Factory_Sheets[o].Unqualified_Count),0==a.Data.plm_Factory_Sheets[o].Unqualified_Count&&t.tbData.splice(i,1)):"零件"==e.Check_Object_Type&&n===t.tbData[i].Id&&(t.$set(t.tbData[i],"Unqualified_Count",a.Data.plm_Factory_Sheets[o].Unqualified_Count),0==a.Data.plm_Factory_Sheets[o].Unqualified_Count&&t.tbData.splice(i,1));t.form.Qualified_Count+=a.Data.plm_Factory_Sheets[o].Qualified_Count,t.form.Unqualified_Count+=a.Data.plm_Factory_Sheets[o].Unqualified_Count}}else t.$message({type:"error",message:a.Message})}))},getFactoryPeoplelist:function(){var e=this;(0,i.GetFactoryPeoplelist)().then((function(t){t.IsSucceed?e.UserList=t.Data:e.$message({type:"error",message:t.Message})}))},getNode:function(e){this.CheckObjectData.find((function(t){return t.Display_Name==e}))},getCheckType:function(){var e=this;(0,i.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:t.Message})})).catch((function(){}))},changeObject:function(e){var t;this.tbData=[];var a=null===(t=this.CheckObjectData.find((function(t){return t.Id==e})))||void 0===t?void 0:t.Display_Name;switch(this.chooseTitle=a,a){case"构件":this.check_object_id="0",this.gridCode="pro_bitch_steel_list";break;case"零件":this.check_object_id="1",this.gridCode="pro_bitch_part_list";break;case"物料":this.check_object_id="2";break;default:this.check_object_id="0"}this.getGridByCode(),this.getNodeList(e)},getNodeList:function(e){var t=this;(0,i.GetNodeList)({check_object_id:e}).then((function(e){e.IsSucceed?t.$nextTick((function(){t.CheckNodeList=e.Data})):t.$message({type:"error",message:"res.Message"})}))},changeCheckNode:function(e){this.CheckTypeList=[];var t=this.CheckNodeList.find((function(t){return t.Id===e})).Check_Type;"-1"==t?this.CheckTypeList=[{Name:"质量",Id:"1"},{Name:"探伤",Id:"2"}]:"1"==t?this.CheckTypeList=[{Name:"质量",Id:"1"}]:"2"==t&&(this.CheckTypeList=[{Name:"探伤",Id:"2"}])},getGridByCode:function(){var e=this;this.loading=!0,(0,r.GetGridByCode)({Code:this.gridCode+","+this.Code}).then((function(t){t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList))})).then((function(){e.loading=!1}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{}),this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){this.columns=e.map((function(e){return"报检数量"==e.Display_Name&&(e.Display_Name="不合格数量",e.Code="Unqualified_Count"),e})).filter((function(e){return"数量"!=e.Display_Name}))},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount,this.TotalAmount=e.TotalAmount,this.TotalWeight=e.TotalWeight},gridPageChange:function(e){var t=e.page;this.pageInfo.Page=Number(t),this.fetchData()},gridSizeChange:function(e){var t=e.size;this.tbConfig.Row_Number=Number(t),this.pageInfo.PageSize=Number(t),this.pageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},inputBlur:function(e,t,a){var o=Number(t);o<1||o>Number(a.SteelAmount)?a.Inspction_Count=a.SteelAmount:a.Inspction_Count=o}}}},3166:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=h,t.DeleteProject=u,t.GeAreaTrees=I,t.GetFileSync=P,t.GetInstallUnitIdNameList=v,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=S,t.GetProjectAreaTreeList=k,t.GetProjectEntity=l,t.GetProjectList=c,t.GetProjectPageList=r,t.GetProjectTemplate=m,t.GetPushProjectPageList=C,t.GetSchedulingPartList=D,t.IsEnableProjectMonomer=d,t.SaveProject=s,t.UpdateProjectTemplateBase=_,t.UpdateProjectTemplateContacts=b,t.UpdateProjectTemplateContract=g,t.UpdateProjectTemplateOther=y;var n=o(a("b775")),i=o(a("4328"));function r(e){return(0,n.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:i.default.stringify(e)})}function s(e){return(0,n.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Project/DeleteProject",method:"post",data:i.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function P(e){return(0,n.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"32d0":function(e,t,a){"use strict";a("0de6")},4995:function(e,t,a){"use strict";a.r(t);var o=a("2d6d"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"4c3e":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{height:"100%"}},[a("div",{staticClass:"cs-bottom-wapper"},[a("div",{staticClass:"fff tb-x"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.loading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0},"checkbox-config":{checkField:"checked",trigger:"row"},"row-config":{isHover:!0}},on:{"checkbox-all":e.multiSelectedChange,"checkbox-change":e.multiSelectedChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"44"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["Check_Result"===t.Code?{key:"default",fn:function(t){var o=t.row;return[o.Check_Result?["合格"===o.Check_Result?a("el-tag",{attrs:{type:"success"}},[e._v(e._s(o.Check_Result))]):a("el-tag",{attrs:{type:"danger"}},[e._v(e._s(o.Check_Result))])]:a("span",[e._v("-")])]}}:"Status"===t.Code?{key:"default",fn:function(t){var o=t.row;return["已完成"===o.Status?a("span",{staticClass:"by-dot by-dot-success"},[e._v(" "+e._s(o.Status||"-")+" ")]):"待复核"===o.Status||"待整改"===o.Status?a("span",{staticClass:"by-dot by-dot-primary"},[e._v(" "+e._s(o.Status||"-")+" ")]):"待质检"===o.Status||"草稿"===o.Status?a("span",{staticClass:"by-dot by-dot-info"},[e._v(" "+e._s(o.Status||"-")+" ")]):a("span",[e._v(" "+e._s(o.Status||"-")+" ")])]}}:{key:"default",fn:function(o){var n=o.row;return[a("span",[e._v(e._s(e._f("displayValue")(n[t.Code])))])]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",align:"center",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return["草稿"!=o.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handelView(o)}}},[e._v("查看")]):e._e()]}}])})],2)],1),a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.selectList.length)+" 条数据 ")]),a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)]),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",on:{close:e.handleClose}})],1):e._e()],1)},n=[]},"4e82":function(e,t,a){"use strict";var o=a("23e7"),n=a("e330"),i=a("59ed"),r=a("7b0b"),c=a("07fa"),l=a("083a"),s=a("577e"),u=a("d039"),d=a("addb"),f=a("a640"),h=a("3f7e"),p=a("99f4"),m=a("1212"),_=a("ea83"),b=[],g=n(b.sort),y=n(b.push),C=u((function(){b.sort(void 0)})),k=u((function(){b.sort(null)})),v=f("sort"),I=!u((function(){if(m)return m<70;if(!(h&&h>3)){if(p)return!0;if(_)return _<603;var e,t,a,o,n="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(o=0;o<47;o++)b.push({k:t+o,v:a})}for(b.sort((function(e,t){return t.v-e.v})),o=0;o<b.length;o++)t=b[o].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}})),S=C||!k||!v||!I,D=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:s(t)>s(a)?1:-1}};o({target:"Array",proto:!0,forced:S},{sort:function(e){void 0!==e&&i(e);var t=r(this);if(I)return void 0===e?g(t):g(t,e);var a,o,n=[],s=c(t);for(o=0;o<s;o++)o in t&&y(n,t[o]);d(n,D(e)),a=c(n),o=0;while(o<a)t[o]=n[o++];while(o<s)l(t,o++);return t}})},"5c29":function(e,t,a){"use strict";a.r(t);var o=a("04a69"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"5e4c":function(e,t,a){"use strict";a("fa2b")},64868:function(e,t,a){"use strict";a.r(t);var o=a("f2b4"),n=a("e5cd");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("32d0");var r=a("2877"),c=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"5162a46a",null);t["default"]=c.exports},6801:function(e,t,a){"use strict";a("bf85")},6892:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("ab43"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("5319"),a("5b81"),a("498a");var n=o(a("5530")),i=o(a("c14f")),r=o(a("1da1")),c=o(a("846e")),l=o(a("b21c")),s=(a("6186"),a("221f")),u=o(a("0f97")),d=o(a("a888")),f=a("fd31"),h=a("8975"),p=a("7de9"),m=a("ed08"),_=o(a("333d")),b=o(a("15ac")),g=a("c685");t.default={directives:{elDragDialog:d.default},components:{Pagination:_.default,DynamicDataTable:u.default,rectificationSheet:l.default,InspectDoc:c.default},mixins:[b.default],props:{searchDetail:{type:Object,default:function(){return{}}}},data:function(){return{width:"60%",code:"",TypeId:"",typeOption:"",dialogVisible:!1,loading:!1,dialogTitle:"",Ismodal:!0,dialogData:{},currentComponent:"",tbConfig:{},Data:[],columns:[],tbData:[],queryInfo:{Page:1,PageSize:g.tablePageSize[0]},tablePageSize:g.tablePageSize,total:0,gridCode:"Pro_Inpection_summary_list_spot",searchHeight:0,CheckResultData:[],CheckNodeList:[],CheckObjectData:[],check_object_id:"",ProjectNameData:[],check_object_Name:"",selectList:[]}},mounted:function(){this.getTypeList()},methods:{handleSearch:function(){},getTypeList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,o,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,o=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(o),e.typeOption.length>0&&(e.TypeId=null===(n=e.typeOption[0])||void 0===n?void 0:n.Id,e.code=e.typeOption.find((function(t){return t.Id===e.TypeId})).Code,e.getTableConfig(e.gridCode+","+e.code)),e.fetchData(1)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.loading=!0;var a=JSON.parse(JSON.stringify(this.searchDetail)),o=a.SteelName.trim().replaceAll(" ","\n");a.Pick_Date&&2===a.Pick_Date.length?(a.BeginDate=a.Pick_Date[0],a.EndDate=a.Pick_Date[1]):(a.BeginDate=null,a.EndDate=null),(0,s.GetPageQualitySummary)((0,n.default)((0,n.default)({pageInfo:this.queryInfo},a),{},{SteelName:o,Check_Style:0})).then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1}))},handelSheet:function(e){var t=this;this.generateComponent("整改单","rectificationSheet"),this.$nextTick((function(a){t.$refs["content"].init(t.code,e)}))},handelView:function(e){var t=this;this.generateComponent("查看质检单","inspectDoc"),this.$nextTick((function(a){t.$refs["content"].init(t.code,e)}))},setGridData:function(e){this.tbData=this.tbData=e.Data.map((function(e){return e.Id=e.SheetId,e.Rectify_Date=e.Rectify_Date?(0,h.timeFormat)(e.Rectify_Date,"{y}-{m}-{d}"):"-",e.Pick_Date=e.Pick_Date?(0,h.timeFormat)(e.Pick_Date,"{y}-{m}-{d}"):"-",e})),this.total=e.TotalCount},multiSelectedChange:function(e){this.selectList=e.records},generateComponent:function(e,t){this.dialogTitle=e,this.currentComponent=t,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1,this.fetchData(1)},exportTb:function(){var e=this,t=this.selectList.map((function(e){return e.SheetId}));this.$emit("setExportLoading",!0);var a=JSON.parse(JSON.stringify(this.searchDetail)),o=a.SteelName.trim().replaceAll(" ","\n");a.Pick_Date&&2===a.Pick_Date.length?(a.BeginDate=a.Pick_Date[0],a.EndDate=a.Pick_Date[1]):(a.BeginDate=null,a.EndDate=null),(0,p.ExportInspsectionSummaryInfo)((0,n.default)((0,n.default)({pageInfo:this.queryInfo},a),{},{SteelName:o,Check_Style:0,SheetIds:t})).then((function(t){t.IsSucceed?window.open((0,m.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"}),e.$emit("setExportLoading",!1)}))}}}},"702c":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"wrapper-c"},[a("div",{staticClass:"header_tab"},[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"全检",name:"全检"}}),a("el-tab-pane",{attrs:{label:"抽检",name:"抽检"}})],1)],1),a("div",{staticClass:"search_wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.changeObject},model:{value:e.form.Check_Object_Type,callback:function(t){e.$set(e.form,"Check_Object_Type",t)},expression:"form.Check_Object_Type"}},e._l(e.CheckObjectData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Display_Name}})})),1)],1)],1),a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!e.form.Check_Object_Type},model:{value:e.form.Check_Node_Id,callback:function(t){e.$set(e.form,"Check_Node_Id",t)},expression:"form.Check_Node_Id"}},e._l(e.CheckNodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"质检结果",prop:"Check_Result"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Check_Result,callback:function(t){e.$set(e.form,"Check_Result",t)},expression:"form.Check_Result"}},[a("el-option",{attrs:{label:"合格",value:"合格"}}),a("el-option",{attrs:{label:"不合格",value:"不合格"}}),"全检"===e.activeName?a("el-option",{attrs:{label:"未一次合格",value:"未一次合格"}}):e._e()],1)],1)],1),a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"质检时间",prop:"Pick_Date"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.form.Pick_Date,callback:function(t){e.$set(e.form,"Pick_Date",t)},expression:"form.Pick_Date"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"质检单号",prop:"Number_Like"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入质检单号"},model:{value:e.form.Number_Like,callback:function(t){e.$set(e.form,"Number_Like",t)},expression:"form.Number_Like "}})],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},["全检"==e.activeName?a("el-form-item",{attrs:{label:"名称",prop:"SteelName"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入（空格间隔筛选多个）"},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1):e._e()],1),a("el-col",{attrs:{span:5,lg:4,xl:5}},[a("el-form-item",{attrs:{label:"单据状态",prop:"Status"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}},[a("el-option",{attrs:{label:"草稿",value:"草稿"}}),a("el-option",{attrs:{label:"待整改",value:"待整改"}}),a("el-option",{attrs:{label:"待复核",value:"待复核"}}),a("el-option",{attrs:{label:"待质检",value:"待质检"}}),a("el-option",{attrs:{label:"已完成",value:"已完成"}})],1)],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:5}},["全检"==e.activeName?a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1):e._e()],1),a("el-col",{attrs:{span:4,lg:5,xl:4}},[a("el-form-item",{attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")]),a("el-button",{attrs:{type:"success",loading:e.exportLoading},on:{click:e.handleExport}},[e._v("导出")])],1)],1)],1)],1)],1),a("div",{staticClass:"main-wrapper"},["全检"==e.activeName?a("full-check",{ref:"fullCheckRef",attrs:{"search-detail":e.form},on:{setExportLoading:e.setExportLoading}}):e._e(),"抽检"==e.activeName?a("spot-check",{ref:"spotCheckRef",attrs:{"search-detail":e.form},on:{setExportLoading:e.setExportLoading}}):e._e()],1)])])},n=[]},"740e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var o=a("221f"),n=a("8975");a("0e9a"),t.default={data:function(){return{list:[],srcList:[]}},methods:{init:function(e){this.getrectificationRecord(e.SheetId)},getrectificationRecord:function(e){var t=this;this.srcList=[],(0,o.RectificationRecord)({sheetid:e}).then((function(e){e.IsSucceed?0!=e.Data.length?t.list=e.Data.map((function(e){switch(e.Reply.Type){case 1:e.Reply.Type="整改";break;case 2:e.Reply.Type="复核";break;case 3:e.Reply.Type="评论";break;case 0:e.Reply.Type="移交";break;case-1:e.Reply.Type="初次整改";break}return e.Reply.ActionTime=(0,n.timeFormat)(e.Reply.ActionTime,"{y}-{m}-{d} {h}:{i}:{s}"),e.Attachments.forEach((function(e){t.srcList.push(e.File_Url)})),e})):t.$message({type:"error",message:"暂无操作记录"}):t.$message({type:"error",message:e.Message})}))}}}},"74a0":function(e,t,a){"use strict";a.r(t);var o=a("184f"),n=a("5c29");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("eedb5");var r=a("2877"),c=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"2f39f4d6",null);t["default"]=c.exports},"7de9":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=h,t.AddCheckItemCombination=b,t.AddCheckType=l,t.DelNode=P,t.DelQualityList=I,t.DeleteCheckItem=f,t.DeleteCheckType=s,t.EntityCheckItem=p,t.EntityCheckType=r,t.EntityQualityList=v,t.ExportInspsectionSummaryInfo=x,t.GetCheckGroupList=_,t.GetCheckItemList=d,t.GetCheckTypeList=c,t.GetCompTypeTree=N,t.GetDictionaryDetailListByCode=i,t.GetEntityNode=D,t.GetFactoryPeoplelist=y,t.GetFactoryProfessionalByCode=O,t.GetMaterialType=j,t.GetNodeList=S,t.GetProEntities=g,t.GetProcessCodeList=C,t.QualityList=k,t.SaveCheckItem=m,t.SaveCheckType=u,t.SaveNode=T;var n=o(a("b775"));function i(e){return(0,n.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function r(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},"846e":function(e,t,a){"use strict";a.r(t);var o=a("87be"),n=a("17ff");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("5e4c");var r=a("2877"),c=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"6e17efba",null);t["default"]=c.exports},"848e":function(e,t,a){},8599:function(e,t,a){"use strict";a.r(t);var o=a("702c"),n=a("9697");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("6801");var r=a("2877"),c=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"9c722a82",null);t["default"]=c.exports},"870a":function(e,t,a){},8794:function(e,t,a){"use strict";a.r(t);var o=a("96f0"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"879fb":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var n=o(a("74a0")),i=o(a("8ce9")),r=a("7de9"),c=a("3166");a("8975"),t.default={name:"PROStartInspect",components:{fullCheck:n.default,spotCheck:i.default},data:function(){return{exportLoading:!1,activeName:"全检",form:{Status:"",Check_Result:"",Project_Id:"",Check_Object_Type:"",SteelName:"",Check_Node_Id:"",Number_Like:"",Pick_Date:[],BeginDate:null,EndDate:null},CheckNodeList:[],CheckObjectData:[],check_object_id:null,ProjectNameData:[],Check_Style:"1"}},watch:{activeName:{handler:function(e,t){this.form={Status:"",Check_Result:"",Project_Id:"",Check_Object_Type:"",SteelName:"",Check_Node_Id:"",Number_Like:""},"全检"===e?this.Check_Style="1":"抽检"===e&&(this.Check_Style="0"),this.exportLoading=!1},deep:!0}},mounted:function(){this.getCheckType(),this.getProjectOption()},methods:{getProjectOption:function(){var e=this;(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getCheckType:function(){var e=this;(0,r.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:"res.Message"})})).catch((function(){}))},changeObject:function(e){var t,a=this;this.form.Check_Node_Id="";var o=null===(t=this.CheckObjectData.find((function(t){return t.Display_Name===e})))||void 0===t?void 0:t.Id;(0,r.GetNodeList)({check_object_id:o,Check_Style:this.Check_Style}).then((function(e){e.IsSucceed?a.CheckNodeList=e.Data:a.$message({type:"error",message:e.Message})}))},handleSearch:function(){"全检"===this.activeName?this.$refs.fullCheckRef.fetchData(1):"抽检"===this.activeName&&this.$refs.spotCheckRef.fetchData(1)},handleExport:function(){"全检"===this.activeName?this.$refs.fullCheckRef.exportTb():"抽检"===this.activeName&&this.$refs.spotCheckRef.exportTb()},setExportLoading:function(e){this.exportLoading=e}}}},"87be":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog_wapper"},[a("div",{staticClass:"select_Wapper"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"质检单号",prop:"Number"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.Number,callback:function(t){e.$set(e.form,"Number",t)},expression:"form.Number"}})],1),a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:""},on:{change:e.changeObject},model:{value:e.form.Check_Object_Type,callback:function(t){e.$set(e.form,"Check_Object_Type",t)},expression:"form.Check_Object_Type"}},e._l(e.CheckObjectData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:""},on:{change:e.changeCheckNode},model:{value:e.form.Check_Node_Id,callback:function(t){e.$set(e.form,"Check_Node_Id",t)},expression:"form.Check_Node_Id"}},e._l(e.CheckNodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-select",{attrs:{placeholder:"请选择",disabled:""},model:{value:e.form.Check_Type,callback:function(t){e.$set(e.form,"Check_Type",t)},expression:"form.Check_Type"}},e._l(e.CheckTypeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fff cs-z-tb-wrapper"},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.pageInfo.TotalCount,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.gridPageChange,gridSizeChange:e.gridSizeChange,multiSelectedChange:e.multiSelectedChange}})],1),"待质检"!=e.Status?a("div",{staticClass:"count_Wapper"},[a("el-form",{ref:"countForm",attrs:{model:e.countForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"实际抽检数量",prop:"Inspction_Count"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.countForm.Steel_Count,callback:function(t){e.$set(e.countForm,"Steel_Count",t)},expression:"countForm.Steel_Count"}})],1),a("el-form-item",{attrs:{label:"总不合格数量",prop:"Unqualified_Count"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.countForm.Unqualified_Count,callback:function(t){e.$set(e.countForm,"Unqualified_Count",t)},expression:"countForm.Unqualified_Count"}})],1),a("el-form-item",{attrs:{label:"合格数量",prop:"Qualified_Count"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.countForm.Qualified_Count,callback:function(t){e.$set(e.countForm,"Qualified_Count",t)},expression:"countForm.Qualified_Count"}})],1),a("el-form-item",{attrs:{label:"合格率",prop:"Qualification_rate"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.countForm.Qualification_rate,callback:function(t){e.$set(e.countForm,"Qualification_rate",t)},expression:"countForm.Qualification_rate"}})],1),a("el-form-item",{attrs:{label:"抽检率",prop:"Sampling_rate"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.countForm.Sampling_rate,callback:function(t){e.$set(e.countForm,"Sampling_rate",t)},expression:"countForm.Sampling_rate"}})],1),a("el-form-item",{attrs:{label:"质检结果",prop:"Check_Result"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.countForm.Check_Result,callback:function(t){e.$set(e.countForm,"Check_Result",t)},expression:"countForm.Check_Result"}})],1)],1)],1):e._e()])},n=[]},"8ce9":function(e,t,a){"use strict";a.r(t);var o=a("4c3e"),n=a("8e7c");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("a477");var r=a("2877"),c=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"024ab810",null);t["default"]=c.exports},"8e7c":function(e,t,a){"use strict";a.r(t);var o=a("6892"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},9697:function(e,t,a){"use strict";a.r(t);var o=a("879fb"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"96f0":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var n=o(a("5530")),i=o(a("c14f")),r=o(a("1da1")),c=o(a("0f97")),l=o(a("a888")),s=a("7de9"),u=a("221f");t.default={directives:{elDragDialog:l.default},components:{DynamicDataTable:c.default},data:function(){return{SaveLoading:!1,SubmitLoading:!1,form:{Check_Object_Type:"",SteelName:"",Check_Node_Id:"",Check_Type:""},chooseTitle:"",currentComponent:"",title:"",dialogVisible:!1,dialogTitle:"",width:"60%",type:"",addComTitle:"添加构件",CheckTypeList:[{Name:"质量",Id:"1"},{Name:"探伤",Id:"2"}],CheckNodeList:[],CheckObjectData:[],rules:{Check_Object_Type:[{required:!0,message:"请填写完整表单",trigger:"change"}],Check_Node_Id:[{required:!0,message:"请填写完整表单",trigger:"change"}],Check_Type:[{required:!0,message:"请填写完整表单",trigger:"change"}],SteelName:[{required:!0,message:"请填写完整表单",trigger:"blur"}]}}},mounted:function(){this.getCheckType()},methods:{init:function(e,t){var a=this;return(0,r.default)((0,i.default)().m((function o(){var n;return(0,i.default)().w((function(o){while(1)switch(o.n){case 0:if(a.type=e||"","查看"!=e){o.n=2;break}return o.n=1,a.getCheckType();case 1:a.form.Check_Object_Type=null===(n=a.CheckObjectData.find((function(e){return e.Display_Name===t.Check_Object_Type})))||void 0===n?void 0:n.Id,a.changeObject(a.form.Check_Object_Type),a.form.Check_Node_Id=t.Check_Node_Id,a.form.SteelName=t.SteelName,a.form.Check_Type=t.Check_Type;case 2:return o.a(2)}}),o)})))()},handelName:function(e){this.form.SteelName=e.SteelName?e.SteelName:e.Code,this.form.Check_Object_Id=e.Id},getCheckType:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:"res.Message"})})).catch((function(){}));case 1:return t.a(2)}}),t)})))()},changeObject:function(e){var t,a=this;this.form.Check_Node_Id="",this.form.Check_Type="",this.form.SteelName="";var o=null===(t=this.CheckObjectData.find((function(t){return t.Id==e})))||void 0===t?void 0:t.Display_Name;switch(this.chooseTitle=o,o){case"构件":this.check_object_id="0";break;case"零件":this.check_object_id="1";break;case"物料":this.check_object_id="2";break;case"部件":this.check_object_id="3";break;default:this.check_object_id="0"}(0,s.GetNodeList)({check_object_id:e,Check_Style:"1"}).then((function(e){e.IsSucceed?a.CheckNodeList=e.Data:a.$message({type:"error",message:"res.Message"})}))},changeCheckNode:function(e){var t;this.CheckTypeList=[],this.form.Check_Type="";var a=null===(t=this.CheckNodeList.find((function(t){return t.Id===e})))||void 0===t?void 0:t.Check_Type;"-1"==a?(this.CheckTypeList=[{Name:"质量",Id:"1"},{Name:"探伤",Id:"2"}],this.form.Check_Type="1"):"1"==a?(this.CheckTypeList=[{Name:"质量",Id:"1"}],this.form.Check_Type="1"):"2"==a&&(this.CheckTypeList=[{Name:"探伤",Id:"2"}],this.form.Check_Type="2")},chooseComponent:function(){this.$store.dispatch("qualityCheck/changeRadio",!0),this.$emit("openDialog",this.check_object_id,this.chooseTitle)},handleClose:function(){this.dialogVisible=!1},AddSave:function(e,t){var a=this;t?this.SubmitLoading=!0:this.SaveLoading=!0,this.$refs[e].validate((function(e){if(!e)return a.SubmitLoading=!1,a.SaveLoading=!1,!1;(0,u.Add)({SheetModel:(0,n.default)((0,n.default)({},a.form),{},{Check_Object_Type:a.check_object_id,Check_Object_Type_Id:a.form.Check_Object_Type,Check_Style:1}),sumbimt:t}).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"保存成功"}),a.SubmitLoading=!1,a.SaveLoading=!1,a.$emit("close"),a.$emit("refresh")):(a.SubmitLoading=!1,a.SaveLoading=!1,a.$message({type:"warning",message:e.Message}))}))}))}}}},"9f31":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog_wapper"},[a("div",{staticClass:"select_Wapper"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"质检单号",prop:"Number"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.Number,callback:function(t){e.$set(e.form,"Number",t)},expression:"form.Number"}})],1),a("el-form-item",{attrs:{label:"整改单号",prop:"ZG_Code"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.ZG_Code,callback:function(t){e.$set(e.form,"ZG_Code",t)},expression:"form.ZG_Code"}})],1),a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:""},on:{change:e.changeObject},model:{value:e.form.Check_Object_Type,callback:function(t){e.$set(e.form,"Check_Object_Type",t)},expression:"form.Check_Object_Type"}},e._l(e.CheckObjectData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:""},on:{change:e.changeCheckNode},model:{value:e.form.Check_Node_Id,callback:function(t){e.$set(e.form,"Check_Node_Id",t)},expression:"form.Check_Node_Id"}},e._l(e.CheckNodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-select",{attrs:{placeholder:"请选择",disabled:""},model:{value:e.form.Check_Type,callback:function(t){e.$set(e.form,"Check_Type",t)},expression:"form.Check_Type"}},e._l(e.CheckTypeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"不合格数量",prop:"Unqualified_Count"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.Unqualified_Count,callback:function(t){e.$set(e.form,"Unqualified_Count",t)},expression:"form.Unqualified_Count"}})],1),a("el-form-item",{attrs:{label:"整改人",prop:"Rectifier_Id "}},[a("el-select",{attrs:{clearable:"",disabled:""},model:{value:e.form.Rectifier_Id,callback:function(t){e.$set(e.form,"Rectifier_Id",t)},expression:"form.Rectifier_Id"}},e._l(e.UserList,(function(e,t){return a("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"整改时限",prop:"Rectify_Date"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.Rectify_Date,callback:function(t){e.$set(e.form,"Rectify_Date",t)},expression:"form.Rectify_Date"}})],1),a("el-form-item",{attrs:{label:"整改状态",prop:"Status "}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.Status,callback:function(t){e.$set(e.form,"Status",t)},expression:"form.Status"}})],1)],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"fff cs-z-tb-wrapper"},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.pageInfo.TotalCount,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.gridPageChange,gridSizeChange:e.gridSizeChange,multiSelectedChange:e.multiSelectedChange}})],1)])},n=[]},a477:function(e,t,a){"use strict";a("870a")},a888:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("d565")),i=function(e){e.directive("el-drag-dialog",n.default)};window.Vue&&(window["el-drag-dialog"]=n.default,Vue.use(i)),n.default.install=i;t.default=n.default},b21c:function(e,t,a){"use strict";a.r(t);var o=a("9f31"),n=a("4995");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("d480");var r=a("2877"),c=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"11532b72",null);t["default"]=c.exports},bf85:function(e,t,a){},c484:function(e,t,a){},d480:function(e,t,a){"use strict";a("f6ec")},d565:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319");t.default={bind:function(e,t,a){var o=e.querySelector(".el-dialog__header"),n=e.querySelector(".el-dialog");o.style.cssText+=";cursor:move;",n.style.cssText+=";top:0px;";var i=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();o.onmousedown=function(e){var t=e.clientX-o.offsetLeft,r=e.clientY-o.offsetTop,c=n.offsetWidth,l=n.offsetHeight,s=document.body.clientWidth,u=document.body.clientHeight,d=n.offsetLeft,f=s-n.offsetLeft-c,h=n.offsetTop,p=u-n.offsetTop-l,m=i(n,"left"),_=i(n,"top");m.includes("%")?(m=+document.body.clientWidth*(+m.replace(/\%/g,"")/100),_=+document.body.clientHeight*(+_.replace(/\%/g,"")/100)):(m=+m.replace(/\px/g,""),_=+_.replace(/\px/g,"")),document.onmousemove=function(e){var o=e.clientX-t,i=e.clientY-r;-o>d?o=-d:o>f&&(o=f),-i>h?i=-h:i>p&&(i=p),n.style.cssText+=";left:".concat(o+m,"px;top:").concat(i+_,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}},dca8:function(e,t,a){"use strict";var o=a("23e7"),n=a("bb2f"),i=a("d039"),r=a("861d"),c=a("f183").onFreeze,l=Object.freeze,s=i((function(){l(1)}));o({target:"Object",stat:!0,forced:s,sham:!n},{freeze:function(e){return l&&r(e)?l(c(e)):e}})},e5cd:function(e,t,a){"use strict";a.r(t);var o=a("740e"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},e8fe:function(e,t,a){"use strict";a.r(t);var o=a("efee"),n=a("8794");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("f60ec");var r=a("2877"),c=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"4d6d47b0",null);t["default"]=c.exports},eedb5:function(e,t,a){"use strict";a("c484")},efee:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:"查看"==e.type},on:{change:e.changeObject},model:{value:e.form.Check_Object_Type,callback:function(t){e.$set(e.form,"Check_Object_Type",t)},expression:"form.Check_Object_Type"}},e._l(e.CheckObjectData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!e.form.Check_Object_Type||"查看"==e.type},on:{change:e.changeCheckNode},model:{value:e.form.Check_Node_Id,callback:function(t){e.$set(e.form,"Check_Node_Id",t)},expression:"form.Check_Node_Id"}},e._l(e.CheckNodeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"名称",prop:"SteelName"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",disabled:!e.form.Check_Object_Type||"查看"==e.type},on:{click:e.chooseComponent},slot:"append"})],1)],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-select",{attrs:{placeholder:"请选择",disabled:!e.form.Check_Node_Id||1==e.CheckTypeList.length||"查看"==e.type},on:{change:function(t){return e.$forceUpdate()}},model:{value:e.form.Check_Type,callback:function(t){e.$set(e.form,"Check_Type",t)},expression:"form.Check_Type"}},e._l(e.CheckTypeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),"查看"!=e.type?a("el-form-item",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.SaveLoading},on:{click:function(t){return e.AddSave("form",!1)}}},[e._v("保 存")]),a("el-button",{attrs:{type:"primary",loading:e.SubmitLoading},on:{click:function(t){return e.AddSave("form",!0)}}},[e._v("提 交")])],1):e._e()],1)],1)},n=[]},f2b4:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrapper"},[a("div",{staticClass:"wrapper-main"},e._l(e.list,(function(t,o){return a("ul",{key:o},[a("li",{staticClass:"top"},[a("span",{staticClass:"left-title"},[a("i"),e._v(e._s(t.Reply.Create_UserName))]),a("span",{staticStyle:{color:"#298dff","margin-right":"10px"}},[e._v(e._s(t.Reply.Type))]),a("span",[e._v(e._s(t.Reply.ActionTime))])]),"复核"==t.Reply.Type?a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Reply.Type)+"状态")]),a("span",{staticStyle:{color:"#00c361"}},[e._v(e._s(t.Reply.IsPass?"合格":"不合格"))])]):e._e(),a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Reply.Type)+"内容")]),a("span",[e._v(e._s(t.Reply.Suggestion))])]),a("li",[a("div",{staticClass:"left-title"},[e._v("图片")]),e._l(t.Attachments,(function(t,o){return a("div",{key:o,staticClass:"img_Wrapper"},[a("el-image",{staticStyle:{width:"100px",height:"100px"},attrs:{src:t.File_Url,"preview-src-list":e.srcList}})],1)}))],2)])})),0)])},n=[]},f53b:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("ab43"),a("a9e3"),a("b680"),a("d3b7");var n=a("6186"),i=a("7de9"),r=a("221f"),c=o(a("0f97"));t.default={components:{DynamicDataTable:c.default},data:function(){return{chooseTitle:"",Code:"",loading:!1,tbConfig:{},columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:10},selectList:[],gridCode:"pro_bitch_steel_list",form:{Check_Object_Type:"",Check_Node_Id:"",Check_Type:"",Number:""},countForm:{Inspction_Count:0,Unqualified_Count:0,Qualified_Count:0,Qualification_rate:0,Check_Result:"",Sampling_rate:0,Steel_Count:0},CheckTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],CheckNodeList:[],CheckObjectData:[],disable:!1,plm_Factory_Sheets:[],detailInfo:{},Status:"待质检"}},mounted:function(){this.getCheckType()},methods:{init:function(e,t){this.detailInfo=t,"构件"==t.Check_Object_Type?this.gridCode="pro_bitch_steel_list":("零件"==t.Check_Object_Type||"部件"==t.Check_Object_Type)&&(this.gridCode="pro_bitch_part_list"),this.EditInfo(t),this.getCheckType(),this.Code=e,this.shawDialog=!0,this.getGridByCode()},EditInfo:function(e){var t=this;(0,r.GetEditById)({Sheetid:e.SheetId}).then((function(a){if(a.IsSucceed){t.plm_Factory_Sheets=a.Data.plm_Factory_Sheets[0],t.form.Check_Object_Type=t.plm_Factory_Sheets.Check_Object_Type_Id,t.form.Number=t.plm_Factory_Sheets.Number,t.countForm.Check_Result=e.Check_Result,t.Status=e.Status,t.changeObject(t.form.Check_Object_Type),t.form.Check_Node_Id=t.plm_Factory_Sheets.Check_Node_Id,t.form.Check_Type=t.plm_Factory_Sheets.Check_Type,"构件"==e.Check_Object_Type?t.tbData=a.Data.Reads:("零件"==e.Check_Object_Type||"部件"==e.Check_Object_Type)&&(t.tbData=a.Data.Parts),t.countForm.Qualified_Count=0,t.countForm.Unqualified_Count=0,t.countForm.Inspction_Count=0,t.countForm.Qualification_rate=0,t.countForm.Sampling_rate=0;for(var o=0;o<a.Data.plm_Factory_Sheets.length;o++){for(var n=a.Data.plm_Factory_Sheets[o].Check_Object_Id,i=0;i<t.tbData.length;i++)"构件"==e.Check_Object_Type?n===t.tbData[i].SteelUnique&&(t.$set(t.tbData[i],"Inspction_Count",a.Data.plm_Factory_Sheets[o].Inspction_Count),"待质检"!=t.Status&&(t.$set(t.tbData[i],"Unqualified_Count",a.Data.plm_Factory_Sheets[o].Unqualified_Count),t.$set(t.tbData[i],"Qualified_Count",a.Data.plm_Factory_Sheets[o].Qualified_Count))):"零件"==e.Check_Object_Type?n===t.tbData[i].Id&&(t.$set(t.tbData[i],"Inspction_Count",a.Data.plm_Factory_Sheets[o].Inspction_Count),"待质检"!=t.Status&&(t.$set(t.tbData[i],"Unqualified_Count",a.Data.plm_Factory_Sheets[o].Unqualified_Count),t.$set(t.tbData[i],"Qualified_Count",a.Data.plm_Factory_Sheets[o].Qualified_Count))):"部件"==e.Check_Object_Type&&n===t.tbData[i].Id&&(t.$set(t.tbData[i],"Inspction_Count",a.Data.plm_Factory_Sheets[o].Inspction_Count),"待质检"!=t.Status&&(t.$set(t.tbData[i],"Unqualified_Count",a.Data.plm_Factory_Sheets[o].Unqualified_Count),t.$set(t.tbData[i],"Qualified_Count",a.Data.plm_Factory_Sheets[o].Qualified_Count)));t.countForm.Qualified_Count+=a.Data.plm_Factory_Sheets[o].Qualified_Count,t.countForm.Unqualified_Count+=a.Data.plm_Factory_Sheets[o].Unqualified_Count,t.countForm.Inspction_Count+=a.Data.plm_Factory_Sheets[o].Inspction_Count,t.countForm.Steel_Count+=a.Data.plm_Factory_Sheets[o].Steel_Count}t.countForm.Qualification_rate=t.countForm.Steel_Count||t.countForm.Qualified_Count?(t.countForm.Qualified_Count/t.countForm.Steel_Count*100).toFixed(2)+"%":"0%",t.countForm.Sampling_rate=t.countForm.Inspction_Count?(t.countForm.Steel_Count/t.countForm.Inspction_Count*100).toFixed(2)+"%":"0%"}else t.$message({type:"error",message:a.Message})}))},getNode:function(e){this.CheckObjectData.find((function(t){return t.Display_Name==e}))},getCheckType:function(){var e=this;(0,i.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?e.CheckObjectData=t.Data:e.$message({type:"error",message:t.Message})})).catch((function(){}))},changeObject:function(e){var t;this.tbData=[],this.form.Check_Node_Id="",this.form.Check_Type="",this.form.Check_Type="";var a=null===(t=this.CheckObjectData.find((function(t){return t.Id==e})))||void 0===t?void 0:t.Display_Name;switch(this.chooseTitle=a,a){case"构件":this.check_object_id="0",this.gridCode="pro_bitch_steel_list";break;case"零件":this.check_object_id="1",this.gridCode="pro_bitch_part_list";break;case"物料":this.check_object_id="2";break;case"部件":this.check_object_id="3",this.gridCode="pro_bitch_part_list";break;default:this.check_object_id="0"}this.getGridByCode(),this.getNodeList(e)},getNodeList:function(e){var t=this;(0,i.GetNodeList)({check_object_id:e}).then((function(e){e.IsSucceed?t.$nextTick((function(){t.CheckNodeList=e.Data})):t.$message({type:"error",message:"res.Message"})}))},changeCheckNode:function(e){this.CheckTypeList=[];var t=this.CheckNodeList.find((function(t){return t.Id===e})).Check_Type;"-1"==t?this.CheckTypeList=[{Name:"质量",Id:"1"},{Name:"探伤",Id:"2"}]:"1"==t?this.CheckTypeList=[{Name:"质量",Id:"1"}]:"2"==t&&(this.CheckTypeList=[{Name:"探伤",Id:"2"}])},getGridByCode:function(){var e=this;this.loading=!0,(0,n.GetGridByCode)({Code:this.gridCode+","+this.Code}).then((function(t){t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList))})).then((function(){e.loading=!1}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{}),this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){"待质检"!=this.Status&&e.map((function(e){return e.Is_Display||(e.Is_Display=!0),e})),"3"===this.check_object_id&&e.map((function(e){return"Code"===e.Code&&(e.Display_Name="部件名称"),e})),this.columns=e},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount,this.TotalAmount=e.TotalAmount,this.TotalWeight=e.TotalWeight},gridPageChange:function(e){var t=e.page;this.pageInfo.Page=Number(t),this.fetchData()},gridSizeChange:function(e){var t=e.size;this.tbConfig.Row_Number=Number(t),this.pageInfo.PageSize=Number(t),this.pageInfo.Page=1,this.fetchData()},multiSelectedChange:function(e){this.selectList=e},inputBlur:function(e,t,a){var o=Number(t);o<1||o>Number(a.SteelAmount)?a.Inspction_Count=a.SteelAmount:a.Inspction_Count=o}}}},f60ec:function(e,t,a){"use strict";a("848e")},f6ec:function(e,t,a){},fa2b:function(e,t,a){}}]);