(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-82a15512"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=o,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=s(),i=t-o,l=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=l;var t=Math.easeInOutQuad(c,o,i,e);r(t),c<e?n(u):a&&"function"===typeof a&&a()};u()}},3166:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=m,e.CancelBindBimProject=f,e.DeleteProject=u,e.GeAreaTrees=j,e.GetFileSync=L,e.GetInstallUnitIdNameList=b,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=R,e.GetProjectAreaTreeList=v,e.GetProjectEntity=l,e.GetProjectList=i,e.GetProjectPageList=o,e.GetProjectTemplate=h,e.GetPushProjectPageList=y,e.GetSchedulingPartList=D,e.IsEnableProjectMonomer=d,e.SaveProject=c,e.UpdateProjectTemplateBase=P,e.UpdateProjectTemplateContacts=I,e.UpdateProjectTemplateContract=g,e.UpdateProjectTemplateOther=_;var r=n(a("b775")),s=n(a("4328"));function o(t){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:s.default.stringify(t)})}function c(t){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:s.default.stringify(t)})}function d(t){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function D(t){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function L(t){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"4e82":function(t,e,a){"use strict";var n=a("23e7"),r=a("e330"),s=a("59ed"),o=a("7b0b"),i=a("07fa"),l=a("083a"),c=a("577e"),u=a("d039"),d=a("addb"),m=a("a640"),f=a("3f7e"),p=a("99f4"),h=a("1212"),P=a("ea83"),I=[],g=r(I.sort),_=r(I.push),y=u((function(){I.sort(void 0)})),v=u((function(){I.sort(null)})),b=m("sort"),j=!u((function(){if(h)return h<70;if(!(f&&f>3)){if(p)return!0;if(P)return P<603;var t,e,a,n,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)I.push({k:e+n,v:a})}for(I.sort((function(t,e){return e.v-t.v})),n=0;n<I.length;n++)e=I[n].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),R=y||!v||!b||!j,D=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:R},{sort:function(t){void 0!==t&&s(t);var e=o(this);if(j)return void 0===t?g(e):g(e,t);var a,n,r=[],c=i(e);for(n=0;n<c;n++)n in e&&_(r,e[n]);d(r,D(t)),a=i(r),n=0;while(n<a)e[n]=r[n++];while(n<c)l(e,n++);return e}})},5746:function(t,e,a){"use strict";a.r(e);var n=a("c891"),r=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=r.a},"7bfc0":function(t,e,a){},"87c9":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AssignReplacementTask=f,e.EditReplacementApply=c,e.FindPartReplaceApplyById=i,e.GetPartReplaceApplyPageList=o,e.GetReplaceApprovePageList=d,e.GetReplacementTaskPageList=m,e.GetReplacementTaskProcessPageList=p,e.GetReplacementTaskTracePageList=P,e.GetTeamListByUser=s,e.GetWorkingTeams=I,e.SavePartReplaceApply=l,e.SavePartReplaceComfirm=u,e.UpdateProcessTaskStatus=h;var r=n(a("b775"));n(a("4328"));function s(t){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}function o(t){return(0,r.default)({url:"/PRO/Replacement/GetPartReplaceApplyPageList",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/Replacement/FindPartReplaceApplyById",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/Replacement/SavePartReplaceApply",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/Replacement/EditReplacementApply",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/Replacement/SavePartReplaceComfirm",method:"post",data:t})}function d(t){return(0,r.default)({url:"/PRO/Replacement/GetReplaceApprovePageList",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskPageList",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Replacement/AssignReplacementTask",method:"post",data:t})}function p(t){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskProcessPageList",method:"post",data:t})}function h(t){return(0,r.default)({url:"/PRO/Replacement/UpdateProcessTaskStatus",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Replacement/GetReplacementTaskTracePageList",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}},a36c:function(t,e,a){"use strict";a.r(e);var n=a("ad98"),r=a("5746");for(var s in r)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(s);a("d2af");var o=a("2877"),i=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"28fc54d2",null);e["default"]=i.exports},ad98:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100",staticStyle:{display:"flex"}},[a("div",{class:t.Iscontract?"cs-left-contract fff h100":"cs-left fff h100"},[a("div",{staticClass:"fff h100 cs-scroll",staticStyle:{padding:"16px 10px 16px 16px","border-radius":"4px"}},[a("div",{style:{display:t.Iscontract?"none":"block"}},[a("el-input",{attrs:{placeholder:"请输入项目名称"},model:{value:t.projectName,callback:function(e){t.projectName=e},expression:"projectName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.fetchTreeData},slot:"append"})],1)],1),a("tree-detail",{ref:"tree",staticStyle:{"margin-top":"20px",height:"calc(100% - 36px)"},attrs:{icon:"icon-folder",loading:t.treeLoading,"tree-data":t.treeData,"show-detail":"","expanded-key":t.expandedKey},on:{handleNodeClick:t.handleNodeClick}})],1),a("div",{staticClass:"stretch-btn",style:{right:t.Iscontract?"-26px":"-20px"},on:{click:function(e){return t.handelarrow()}}},[a("div",{staticClass:"center-btn"},[a("i",{class:t.Iscontract?"el-icon-arrow-right":"el-icon-arrow-left"})])])]),a("div",{staticClass:"cs-right",staticStyle:{"padding-right":"0"}},[a("div",{staticClass:"container"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:t.customParams,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:t.IsInstallUnit},model:{value:t.customParams.InstallUnit_Id,callback:function(e){t.$set(t.customParams,"InstallUnit_Id",e)},expression:"customParams.InstallUnit_Id"}},t._l(t.installUnitIdNameList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"补换单号",prop:"Replace_Code"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text"},model:{value:t.customParams.Replace_Code,callback:function(e){t.$set(t.customParams,"Replace_Code",e)},expression:"customParams.Replace_Code"}})],1),[a("el-form-item",{attrs:{label:"零件名称",prop:"Codes_Format"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text"},model:{value:t.customParams.Codes_Format,callback:function(e){t.$set(t.customParams,"Codes_Format",e)},expression:"customParams.Codes_Format"}})],1)],a("el-form-item",{attrs:{label:"是否完成",prop:"Is_Finish"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.customParams.Is_Finish,callback:function(e){t.$set(t.customParams,"Is_Finish",e)},expression:"customParams.Is_Finish"}},t._l(t.IsFinishData,(function(t){return a("el-option",{key:t.Value,attrs:{label:t.Lable,value:t.Value}})})),1)],1),a("el-form-item",{attrs:{label:"申请日期",prop:"questDate"}},[a("el-date-picker",{attrs:{type:"daterange",clearable:!0,align:"right","value-format":"yyyy-MM-dd","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.questDate,callback:function(e){t.questDate=e},expression:"questDate"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),a("el-button",{on:{click:t.resetSearch}},[t._v("重置")])],1)],2)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper",staticStyle:{"padding-top":"20px"}},[a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:t.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},t._l(t.columns,(function(e,n){return a("vxe-column",{key:n,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,field:e.Code,title:e.Display_Name,"min-width":e.minWidth,width:e.Width},scopedSlots:t._u([{key:"default",fn:function(n){var r=n.row;return["Is_Finish"==e.Code?a("div",[r.Is_Finish?a("span",{staticStyle:{color:"#67c23a"}},[t._v("是")]):a("span",{staticStyle:{color:"#f56c6c"}},[t._v("否")])]):a("div",[a("span",[t._v(t._s(r[e.Code]||"-"))])])]}}],null,!0)})})),1)],1),a("div",{staticClass:"cs-bottom"},[a("div",{staticClass:"cs-component-num"}),a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.changePage}})],1)])])])])])},r=[]},c891:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(a("5530")),s=n(a("c14f")),o=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("d81d"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("a732"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("5319"),a("498a"),a("c7cd"),a("ddb0");var i=a("6186"),l=a("fd31"),c=a("3166"),u=a("87c9"),d=n(a("1463")),m=n(a("333d")),f=a("8975"),p=n(a("dd30")),h=a("c685");e.default={name:"PROPartReplaceFlow",components:{TreeDetail:d.default,Pagination:m.default},mixins:[p.default],data:function(){return{expandedKey:"-1",tablePageSize:h.tablePageSize,treeData:[],treeLoading:!0,projectName:"",columns:[],tbData:[],total:0,tbLoading:!1,queryInfo:{Page:1,PageSize:10},installUnitIdNameList:[],pickerOptions:{shortcuts:[{text:"今天",onClick:function(t){var e=new Date,a=new Date;t.$emit("pick",[a,e])}},{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}}]},customParams:{Sys_Project_Id:"",Project_Id:"",InstallUnit_Id:"",InstallUnit_Name:"",Area_Id:"",Area_Name:"",Start_Date:"",End_Date:"",Codes_Format:"",Codes:"",Is_Finish:null,Replace_Code:""},IsFinishData:[{Value:!0,Lable:"是"},{Value:!1,Lable:"否"}],IsInstallUnit:!0,selectList:[],typeOption:[],Unit:"",Proportion:0,gridCode:"part_replace_flow_page_list",Iscontract:!1}},computed:{typeEntity:function(){var t=this;return this.typeOption.find((function(e){return e.Id===t.customParams.TypeId}))},questDate:{get:function(){return[(0,f.timeFormat)(this.customParams.Start_Date),(0,f.timeFormat)(this.customParams.End_Date)]},set:function(t){if(t){var e=t[0],a=t[1];this.customParams.Start_Date=(0,f.timeFormat)(e),this.customParams.End_Date=(0,f.timeFormat)(a)}else this.customParams.Start_Date="",this.customParams.End_Date=""}},Finish_Date:{get:function(){return[(0,f.timeFormat)(this.customParams.Start_Finish_Date),(0,f.timeFormat)(this.customParams.End_Finish_Date)]},set:function(t){if(t){var e=t[0],a=t[1];this.customParams.Start_Finish_Date=(0,f.timeFormat)(e),this.customParams.End_Finish_Date=(0,f.timeFormat)(a)}else this.customParams.Start_Finish_Date="",this.customParams.End_Finish_Date=""}}},watch:{"customParams.TypeId":function(t,e){e&&"0"!==e&&this.fetchData()}},created:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTypeList();case 1:return e.n=2,t.fetchData();case 2:t.fetchTreeData();case 3:return e.a(2)}}),e)})))()},activated:function(){!this.isUpdate&&this.fetchData(1)},mounted:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:t.isUpdate=!0;case 1:return e.a(2)}}),e)})))()},methods:{fetchTreeData:function(){var t=this;(0,c.GetProjectAreaTreeList)({MenuId:this.$route.meta.Id,projectName:this.projectName}).then((function(e){var a=[{ParentNodes:null,Id:"-1",Code:"全部",Label:"全部",Level:null,Data:{},Children:[]}],n=a.concat(e.Data);n.map((function(t){0===t.Children.length?t.Is_Imported=!1:(t.Data.Is_Imported=t.Children.some((function(t){return!0===t.Data.Is_Imported})),t.Is_Directory=!0,t.Children.map((function(t){t.Children.length>0&&(t.Is_Directory=!0)})))})),t.treeData=n,t.expandedKey=t.customParams.Area_Id?t.customParams.Area_Id:t.customParams.Project_Id?t.customParams.Project_Id:"-1",t.treeLoading=!1}))},handleNodeClick:function(t){this.customParams.InstallUnit_Id="";var e="-1"===t.Id?"":t.Id;t.ParentNodes?(this.customParams.Sys_Project_Id=t.Data.Sys_Project_Id,this.customParams.Project_Id=t.Data.Project_Id,this.customParams.Area_Id=t.Id,this.customParams.Area_Name=t.Data.Name):(this.customParams.Sys_Project_Id=e,this.customParams.Project_Id=e,this.customParams.Area_Id="",this.customParams.Area_Name=t.Data.Name),this.fetchList(),0===t.Children.length&&t.ParentNodes?(this.IsInstallUnit=!1,this.getInstallUnitIdNameList(e,t)):this.IsInstallUnit=!0},getInstallUnitIdNameList:function(t,e){var a=this;""===t||e.Children.length>0?this.installUnitIdNameList=[]:(0,c.GetInstallUnitIdNameList)({Area_Id:t}).then((function(t){a.installUnitIdNameList=t.Data}))},getTableConfig:function(t){var e=this;return new Promise((function(a){(0,i.GetGridByCode)({code:t+","+e.typeOption.find((function(t){return t.Id===e.customParams.TypeId})).Code}).then((function(t){var n=t.IsSucceed,r=t.Data,s=t.Message;if(n){if(!r)return e.$message.error("当前专业没有配置相对应表格"),void(e.tbLoading=!0);e.tbConfig=Object.assign({},e.tbConfig,r.Grid);var o=r.ColumnList||[],i=o.sort((function(t,e){return t.Sort-e.Sort}));e.columns=i.filter((function(t){return t.Is_Display})).map((function(t){return"Replace_Code"===t.Code&&(t.fixed="left"),t})),e.columns.map((function(t){if("Remark"===t.Code)return t.minWidth="160",t.Width="auto",t})),e.queryInfo.PageSize=+r.Grid.Row_Number||20,a(e.columns);var l=JSON.parse(JSON.stringify(e.columns));e.columnsOption=l}else e.$message({message:s,type:"error"})}))}))},fetchList:function(){var t=this;return new Promise((function(e){(0,u.GetReplacementTaskTracePageList)((0,r.default)((0,r.default)({},t.queryInfo),t.customParams)).then((function(a){a.IsSucceed?(t.queryInfo.PageSize=a.Data.PageSize,t.total=a.Data.TotalCount,t.tbData=a.Data.Data.map((function(t){return t.Quest_Date=(0,f.timeFormat)(t.Quest_Date,"{y}-{m}-{d}"),t.Demand_Finish_Date=(0,f.timeFormat)(t.Demand_Finish_Date,"{y}-{m}-{d}"),t.Reality_Finish_Date=(0,f.timeFormat)(t.Reality_Finish_Date,"{y}-{m}-{d}"),t})),t.selectList=[]):t.$message({message:a.Message,type:"error"}),e()})).finally((function(e){t.isUpdate=!1}))}))},fetchData:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig(t.gridCode);case 1:t.tbLoading=!0,Promise.all([t.fetchList()]).then((function(e){t.tbLoading=!1}));case 2:return e.a(2)}}),e)})))()},changePage:function(t){var e=this;return(0,o.default)((0,s.default)().m((function a(){var n,r;return(0,s.default)().w((function(a){while(1)switch(a.n){case 0:n=t.page,r=t.limit,e.tbLoading=!0,e.queryInfo.PageSize=r,e.queryInfo.Page=n,Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 1:return a.a(2)}}),a)})))()},tbSelectChange:function(t){this.selectList=t.records},getTypeList:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){var a,n,r,o;return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=e.v,n=a.Data,a.IsSucceed?(t.typeOption=Object.freeze(n),t.typeOption.length>0&&(t.Proportion=n[0].Proportion,t.Unit=n[0].Unit,t.customParams.TypeId=null===(r=t.typeOption[0])||void 0===r?void 0:r.Id,t.customParams.Type_Name=null===(o=t.typeOption[0])||void 0===o?void 0:o.Name)):t.$message({message:a.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},resetSearch:function(){var t=this;this.$nextTick((function(){t.$refs["form"].resetFields(),t.questDate="",t.handleSearch()}))},handleSearch:function(){var t=this.customParams.Codes_Format.trim();t=t.replace(/\s+/g,"\n"),this.customParams.Codes=t,this.queryInfo.Page=1,this.fetchList()},handelarrow:function(){this.Iscontract=!this.Iscontract,this.Iscontract?this.leftWidth=40:this.leftWidth=320}}}},d2af:function(t,e,a){"use strict";a("7bfc0")},dca8:function(t,e,a){"use strict";var n=a("23e7"),r=a("bb2f"),s=a("d039"),o=a("861d"),i=a("f183").onFreeze,l=Object.freeze,c=s((function(){l(1)}));n({target:"Object",stat:!0,forced:c,sham:!r},{freeze:function(t){return l&&o(t)?l(i(t)):t}})},dd30:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("7db0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("d3b7"),a("159b");var n=a("3166"),r=a("87c9"),s=a("f2f6"),o=a("8975");e.default={data:function(){return{queryForm:{Sys_Project_Id:"",projectId:"",install:"",areaId:""},projectOption:[],areaList:[],installOption:[],treeParams:{data:[],filterable:!1,clickParent:!0,props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},replace_Teams:[],replace_Teams_Factory:[],form:{Replace_Teams_Default_Id:""}}},mounted:function(){this.getProjectOption(),this.getTeamListByUser(),this.getWorkingTeams()},methods:{getAreaList:function(){var t,e=this;!0!==this.projectAreaIsImported&&!1!==this.projectAreaIsImported||(t=this.projectAreaIsImported),(0,n.GeAreaTrees)({projectId:this.queryForm.projectId,Area_Id:this.queryForm.install,Is_Imported:t}).then((function(t){if(t.IsSucceed){var a=t.Data;e.setDisabledTree(a),e.treeParams.data=a,e.$nextTick((function(t){e.$refs.treeSelect.treeDataUpdateFun(a)}))}else e.$message({message:t.Message,type:"error"})}))},getInstall:function(){var t=this;(0,s.GetInstallUnitPageList)({Area_Id:this.queryForm.areaId,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.installOption=e.Data.Data.filter((function(t){return t.Id})):t.$message({message:e.Message,type:"error"})}))},projectChange:function(){var t=this;this.queryForm.areaId="",this.queryForm.install="",this.installOption=[],this.queryForm.Sys_Project_Id=this.projectOption.find((function(e){return e.Id===t.queryForm.projectId})).Sys_Project_Id,this.queryForm.projectId&&this.getAreaList()},areaChange:function(t){if(this.areaIsImport=!1,this.queryForm.install="",this.queryForm.areaId&&!this.areaIsImport){this.getInstall();var e=null===t||void 0===t?void 0:t.Data,a=e.Demand_Begin_Date,n=e.Demand_End_Date;this.getAreaTime((0,o.timeFormat)(a),(0,o.timeFormat)(n))}},installChange:function(){this.getInstall()},areaClear:function(){this.queryForm.install=""},getProjectOption:function(){var t,e=this;!0!==this.projectAreaIsImported&&!1!==this.projectAreaIsImported||(t=this.projectAreaIsImported),(0,n.GetProjectPageList)({Page:1,PageSize:-1,Is_Imported:t}).then((function(t){t.IsSucceed?e.projectOption=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var a=t.Children;a&&a.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(a))}))},getAreaTime:function(){},getTeamListByUser:function(){var t=this;(0,r.GetTeamListByUser)({}).then((function(e){var a=e.IsSucceed,n=e.Data;a&&(t.replace_Teams=n,1===n.length&&(t.form.Replace_Teams_Default_Id=n[0].Id))}))},getWorkingTeams:function(){var t=this;(0,r.GetWorkingTeams)({}).then((function(e){var a=e.IsSucceed,n=e.Data;a&&(t.replace_Teams_Factory=n)}))}}}},e41b:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteByIds=l,e.GetPartsImportTemplate=u,e.GetPartsList=i,e.GetProjectAreaTreeList=s,e.ImportParts=c,e.SaveProjectAreaSort=o;var r=n(a("b775"));function s(t){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function o(t){return(0,r.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:t})}function u(t){return(0,r.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:t})}},f2f6:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=l,e.CheckPlanTime=c,e.DeleteInstallUnit=f,e.GetCompletePercent=g,e.GetEntity=y,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=I,e.GetInstallUnitDetailList=m,e.GetInstallUnitEntity=u,e.GetInstallUnitList=i,e.GetInstallUnitPageList=o,e.GetProjectInstallUnitList=_,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=P,e.SaveOhterSourceInstallUnit=v;var r=n(a("b775")),s=n(a("4328"));function o(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function l(t){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function c(t){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function u(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function m(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function f(t){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function g(t){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function y(t){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:s.default.stringify(t)})}function v(t){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);