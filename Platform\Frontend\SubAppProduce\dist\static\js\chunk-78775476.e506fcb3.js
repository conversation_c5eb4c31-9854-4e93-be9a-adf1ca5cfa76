(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-78775476"],{"209b":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CheckPackCode=u,t.ExportComponentStockInInfo=p,t.ExportPackingInInfo=m,t.ExportWaitingStockIn2ndList=O,t.FinishCollect=k,t.From_Stock_Status_TYPES=void 0,t.GetComponentStockInEntity=l,t.GetLocationList=r,t.GetPackingDetailList=f,t.GetPackingEntity=b,t.GetPackingGroupByDirectDetailList=d,t.GetStockInDetailList=c,t.GetStockMoveDetailList=C,t.GetWarehouseListOfCurFactory=i,t.HandleInventoryItem=S,t.PackingTypes=t.PackingStatus=t.InventoryComponentTypes=t.InventoryCheckStatus=t.InventoryCheckExceptions=void 0,t.RemoveMain=h,t.SaveComponentScrap=P,t.SaveInventory=_,t.SavePacking=g,t.SaveStockIn=s,t.SaveStockMove=y,t.StockInTypes=void 0,t.UnzipPacking=v,t.UpdateBillReady=I,t.UpdateMaterialReady=T;var n=o(a("b775"));o(a("4328")),t.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],t.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],t.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],t.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],t.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],t.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],t.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function i(e){return(0,n.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:e})}function r(e){return(0,n.default)({url:"/PRO/Location/GetLocationList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:e}})}function c(e,t){return(0,n.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:e,isEdit:t}})}function u(e,t){return(0,n.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:e,id:t}})}function d(e){return(0,n.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:e}})}function m(e){return(0,n.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:e}})}function h(e){return(0,n.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:e}})}function g(e){return(0,n.default)({url:"/PRO/Packing/SavePacking",method:"post",data:e})}function v(e){var t=e.id,a=e.locationId;return(0,n.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:t,locationId:a}})}function b(e){var t=e.id,a=e.code;return(0,n.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:t,code:a}})}function C(e){return(0,n.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:e}})}function y(e){return(0,n.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:e})}function S(e){var t=e.id,a=e.type,o=e.value;return(0,n.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:t,type:a,value:o}})}function P(e){return(0,n.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:e}})}function I(e){var t=e.installId,a=e.isReady;return(0,n.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:t,isReady:a}})}function T(e){return(0,n.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:e})}},"2dd3":function(e,t,a){},"2e8a":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=u,t.GetCompTypeTree=d,t.GetComponentTypeEntity=c,t.GetComponentTypeList=r,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=p,t.GetTypePageList=s,t.RestoreTemplateType=C,t.SavDeepenTemplateSetting=b,t.SaveCompTypeIdentifySetting=v,t.SaveComponentType=l,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=m;var n=o(a("b775")),i=o(a("4328"));function r(e){return(0,n.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:i.default.stringify(e)})}function u(e){return(0,n.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:i.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:i.default.stringify(e)})}function f(e){return(0,n.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,n.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function v(e){return(0,n.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function b(e){return(0,n.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function C(e){return(0,n.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"2f9f":function(e,t,a){"use strict";a("cc54")},"3b60":function(e,t,a){},"6cfc":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"stockin-details"},[a("div",{staticClass:"f-head"}),a("DynamicDataTable",{ref:"table",attrs:{config:e.tbConfig,columns:e.columns,data:e.data,border:"",total:e.query.TotalCount,page:e.query.Page},on:{multiSelectedChange:e.multiSelectedChange,columnSearchChange:e.columnSearchChange,tableSearch:e.tableSearch,gridSizeChange:e.gridSizeChange,gridPageChange:e.gridPageChange},scopedSlots:e._u([{key:"hsearch_C_Type",fn:function(t){var o=t.column;return[a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.cTypeChange},model:{value:e.$refs.table.searchedField[o.Code],callback:function(t){e.$set(e.$refs.table.searchedField,o.Code,t)},expression:"$refs.table.searchedField[column.Code]"}},[a("el-option",{attrs:{label:"钢构",value:"钢构"}})],1)]}},{key:"hsearch_Component_Type_Name",fn:function(t){var o=t.column;return[a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.componentChange},model:{value:e.$refs.table.searchedField[o.Code],callback:function(t){e.$set(e.$refs.table.searchedField,o.Code,t)},expression:"$refs.table.searchedField[column.Code]"}},e._l(e.comTypeOptions,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)]}}])},[a("template",{slot:"append"},[a("div",{staticClass:"tb-status"},[a("strong",[e._v("已选: ")]),a("span",[e._v(e._s(e.checkedRows.length))])])])],2),a("el-form",{staticStyle:{"margin-top":"16px"},attrs:{inline:!0}},[a("el-form-item",{attrs:{label:"库位选择"}}),a("el-form-item",{attrs:{label:"仓库",required:""}},[a("el-select",{attrs:{placeholder:"选择仓库"},on:{change:e.wareChange},model:{value:e.form.To_Warehouse_Id,callback:function(t){e.$set(e.form,"To_Warehouse_Id",t)},expression:"form.To_Warehouse_Id"}},e._l(e.wares,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",required:""}},[a("el-select",{attrs:{placeholder:"选择库位"},model:{value:e.form.To_Location_Id,callback:function(t){e.$set(e.form,"To_Location_Id",t)},expression:"form.To_Location_Id"}},e._l(e.form.locs,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("div",{staticStyle:{"text-align":"center","margin-top":"10px"}},[a("el-button",{on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.addItem}},[e._v("添加")])],1)],1)},n=[]},"76cc":function(e,t,a){},"92da":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("5530"));a("99af"),a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7"),a("3ca3"),a("159b"),a("ddb0");var i=o(a("0f97")),r=a("6186"),s=o(a("b775")),l=a("209b"),c=a("2dd9"),u=a("2e8a");t.default={name:"AddDetail",components:{DynamicDataTable:i.default},props:{wares:{type:Array,default:function(){return[]}},used:{type:String,default:""},ProjectName:{type:String,default:""}},data:function(){return{gridCode:"pro_waiting_move_list",form:{To_Location_Id:"",To_Warehouse_Id:"",locs:[]},locs:[],tbConfig:{},columns:[],data:[],checkedRows:[],fiterArrObj:{},query:{Page:1,TotalCount:0,PageSize:0},ParameterJson:[],comTypeOptions:[]}},created:function(){var e=this;Promise.all([]).then((function(){return(0,r.GetGridByCode)({Code:e.gridCode}).then((function(t){t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList))}))})).then((function(){e.$nextTick((function(t){e.fiterArrObj["Project_Name"]=e.ProjectName,e.$refs.table.searchedField["Project_Name"]=e.ProjectName,e.getTableData()}))}))},methods:{componentChange:function(e){this.fiterArrObj["Component_Type_Name"]=e,this.$refs.table.showSearch()},cTypeChange:function(e){this.fiterArrObj["C_Type"]=e,this.getSearchComOptions(e),this.$refs.table.showSearch()},getSearchComOptions:function(e){var t=this;(0,u.GetComponentTypeList)({Level:1,Category:e}).then((function(e){e.IsSucceed?t.comTypeOptions=e.Data:t.$message({message:e.Message,type:"error"})}))},cancel:function(){this.$emit("dialogCancel")},multiSelectedChange:function(e){this.checkedRows=e},setGrid:function(e){this.tbConfig=Object.assign({},e,{Pager_Align:"center",Op_Width:120,Height:450}),this.query.PageSize=Number(e.Row_Number)},setCols:function(e){var t=this;e.forEach((function(e){"From_Warehouse_Name"===e.Code&&(e.Range=JSON.stringify(t.wares.map((function(e){return{label:e.Display_Name,value:e.Display_Name}}))))})),this.columns=e.concat([])},setGridData:function(e){this.data=e.Data},getTableData:function(){var e=this;if(this.tbConfig.Data_Url)return(0,s.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.query,{Component_Ids:this.used,ParameterJson:(0,c.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(t){t.IsSucceed&&(e.setGridData(t.Data),e.query.TotalCount=t.Data.TotalCount)}))},columnSearchChange:function(e){var t=this,a=e.column,o=e.value;if(this.fiterArrObj[a.Code]=o,"From_Warehouse_Name"===a.Code){this.fiterArrObj["From_Location_Name"]=this.$refs.table.searchedField["From_Location_Name"]=null;var n=this.wares.find((function(e){return e.Display_Name===o}));if(!n)return;(0,l.GetLocationList)({Warehouse_Id:n.Id}).then((function(e){e.IsSucceed&&(t.locs=e.Data)})).then((function(){var e=t.columns.find((function(e){return"From_Location_Name"===e.Code}));e&&(e.Range=JSON.stringify(t.locs.map((function(e){return{label:e.Display_Name,value:e.Display_Name}}))))}))}},wareChange:function(e){var t=this;(0,l.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.form.locs=e.Data)}))},tableSearch:function(){this.query.Page=1,this.getTableData()},gridSizeChange:function(e){var t=e.size;this.query.PageSize=t,this.tbConfig=Object.assign({},this.tbConfig,{Row_Number:t}),this.getTableData()},gridPageChange:function(e){var t=e.page;this.query.Page=t,this.getTableData()},addItem:function(){var e=this;if(this.checkedRows.length<=0)return this.$message.warning("尚未选择要添加的明细");if(!this.form.To_Warehouse_Id)return this.$message.warning("尚未指定仓库");if(!this.form.To_Location_Id)return this.$message.warning("尚未指定库位");var t=this.wares.find((function(t){return t.Id===e.form.To_Warehouse_Id})),a=this.form.locs.find((function(t){return t.Id===e.form.To_Location_Id}));this.$emit("dialogFormSubmitSuccess",{type:"add",data:this.checkedRows.map((function(o){return(0,n.default)((0,n.default)({},o),{},{To_Warehouse_Id:e.form.To_Warehouse_Id,To_Location_Id:e.form.To_Location_Id,To_Location_Name:null===a||void 0===a?void 0:a.Display_Name,To_Warehouse_Name:null===t||void 0===t?void 0:t.Display_Name})}))})}}}},"9d9c":function(e,t,a){"use strict";a("3b60")},a2c0:function(e,t,a){"use strict";a.r(t);var o=a("ee24"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},b74b:function(e,t,a){"use strict";a.r(t);var o=a("92da"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},b9ce:function(e,t,a){"use strict";a("76cc")},c9d1:function(e,t,a){"use strict";a.r(t);var o=a("6cfc"),n=a("b74b");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("9d9c"),a("d05e");var r=a("2877"),s=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"1b6ad2f8",null);t["default"]=s.exports},cc54:function(e,t,a){},d05e:function(e,t,a){"use strict";a("2dd3")},d868:function(e,t,a){"use strict";a.r(t);var o=a("f2412"),n=a("a2c0");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("2f9f"),a("b9ce");var r=a("2877"),s=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"abf1504e",null);t["default"]=s.exports},ee24:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("3ca3"),a("159b"),a("ddb0");var n=a("1b69"),i=a("209b"),r=o(a("0f97")),s=o(a("c9d1")),l=a("6186"),c=a("ed08");t.default={name:"AddStock",components:{DynamicDataTable:r.default,AddDetail:s.default},data:function(){return{loading:!1,btnLoading:!1,isClicked:!1,gridCode:"pro_stock_move_detail_list",projects:[],wares:[],locs:[],fiterArrObj:{},tbConfig:{},columns:[],form:{Project_Id:"",To_Location_Id:"",To_Warehouse_Id:"",Remark:""},data:[],checkedRows:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},computed:{project:function(){var e,t=null===(e=this.$route.params)||void 0===e?void 0:e.project;return null!==t&&void 0!==t?t:null},type:function(){var e,t;return null!==(e=null===(t=this.$route.params)||void 0===t?void 0:t.type)&&void 0!==e?e:0}},created:function(){var e,t=this;this.form.Project_Id=null===(e=this.project)||void 0===e?void 0:e.Id,Promise.all([(0,n.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)})),(0,i.GetWarehouseListOfCurFactory)().then((function(e){e.IsSucceed&&(t.wares=e.Data)}))]).then((function(){(0,l.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))}))}))},methods:{tagBack:function(){(0,c.closeTagView)(this.$store,this.$route)},multiSelectedChange:function(e){this.checkedRows=e},setGrid:function(e){this.tbConfig=Object.assign({},e,{Pager_Align:"center",Op_Width:120,Is_Filter:!1,Is_Page:!1})},setCols:function(e){this.columns=e.concat([])},setGridData:function(e){this.data=e.Data},openDialog:function(e){e&&"[object Object]"===Object.prototype.toString.call(e)||(e={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,e,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(e){var t=e.type,a=e.data;switch(this.dialogCancel(),t){case"reload":break;case"add":this.addData(a);break}},addData:function(e){var t=this;e.forEach((function(e){t.data.push(Object.assign({},e))}))},deleteRows:function(){var e=this.checkedRows.map((function(e){return e.Component_Id}));this.data=this.data.filter((function(t){return e.indexOf(t.Component_Id)<0}))},save:function(){var e=this;if(!this.form.Project_Id)return this.$message.warning("没有选择有效项目");if(this.data.length<=0)return this.$message.warning("没有有效的移库明细");this.isClicked=!0;var t=this.formatPostData();this.btnLoading=!0,(0,i.SaveStockMove)(t).then((function(t){var a,o;t.IsSucceed?(e.$message.success(null!==(a=t.Message)&&void 0!==a?a:""),e.$router.push("/pro/inventory-transfer").then((function(){e.$store.dispatch("tagsView/delView",e.$store.state.tagsView.visitedViews.splice(e.$store.state.tagsView.visitedViews.length-2,1))}))):e.$message.warning(null!==(o=t.Message)&&void 0!==o?o:"");e.btnLoading=!1}))},formatPostData:function(){var e,t,a=this,o={entity:{Project_Id:this.form.Project_Id,Project_Name:null===(e=this.project)||void 0===e?void 0:e.Name,Project_Code:null===(t=this.project)||void 0===t?void 0:t.Code,Remark:this.form.Remark},details:[]};return this.data.forEach((function(e){o.details.push(Object.assign({},e,{Project_Id:a.form.Project_Id}))})),o}}}},f2412:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:e.tagBack}}),a("span",[e._v("新增移库单")]),a("div",{staticClass:"right-fix"},[a("el-button",{attrs:{loading:e.btnLoading,type:"primary",disabled:e.isClicked||e.data.length<=0},on:{click:e.save}},[e._v("保存")]),a("el-button",{on:{click:e.tagBack}},[e._v("取消")])],1)],1),a("div",{staticClass:"header"},[a("el-form",{staticClass:"h-adv-form",attrs:{inline:!0}},[a("el-form-item",{attrs:{label:"项目名称",required:""}},[a("el-select",{attrs:{placeholder:"选择项目",disabled:""},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projects,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{staticClass:"flex-fix",staticStyle:{flex:"1"},attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"备注..."},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),a("div",{staticClass:"header",staticStyle:{padding:"16px 0"}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){e.openDialog({title:"添加明细",component:"AddDetail",width:"75%",props:{ProjectName:e.projects.find((function(t){return t.Id===e.form.Project_Id})).Name,wares:e.wares,used:e.data.map((function(e){return e.Component_Id})).join(",")}})}}},[e._v("添加明细")]),a("el-button",{attrs:{disabled:e.checkedRows.length<=0,type:"primary",size:"mini"},on:{click:e.deleteRows}},[e._v("删除明细")])],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{config:e.tbConfig,columns:e.columns,data:e.data,border:""},on:{multiSelectedChange:e.multiSelectedChange}},[a("template",{slot:"append"},[a("div",{staticClass:"tb-status"},[a("strong",[e._v("合计: ")]),a("span",[e._v(e._s(e.data.length)+" 个")]),e._e()])])],2)],1)]),a("el-dialog",{attrs:{title:e.dialogCfgs.title,visible:e.dialogShow,width:e.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(t){e.dialogShow=t}}},[a("keep-alive",[e.dialogShow?a(e.dialogCfgs.component,e._b({tag:"component",on:{dialogCancel:e.dialogCancel,dialogFormSubmitSuccess:e.dialogFormSubmitSuccess}},"component",e.dialogCfgs.props,!1)):e._e()],1)],1)],1)},n=[]}}]);