(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-68672354"],{"22fc":function(t,e,a){"use strict";a.r(e);var i=a("86112"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},"2c5e":function(t,e,a){"use strict";a("e6ad4")},"2d32":function(t,e,a){},"45de":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"c-upload",staticStyle:{"margin-top":"-16px"}},[a("div",{staticClass:"tbox",staticStyle:{"flex-direction":"column"}},[a("el-row",{staticStyle:{width:"100%"},attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[t._v(" 1.选择项目 ")]),a("el-col",{attrs:{span:16}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},on:{change:t.projSelectHandle},model:{value:t.formData.Project_Id,callback:function(e){t.$set(t.formData,"Project_Id",e)},expression:"formData.Project_Id"}},[a("el-option",{attrs:{label:"选择项目",value:""}}),t._l(t.projs,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})}))],2)],1)],1),a("el-row",{staticStyle:{width:"100%","margin-top":"16px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[t._v(" 安装单元名称 ")]),a("el-col",{attrs:{span:16}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},model:{value:t.formData.InstallUnit_Id,callback:function(e){t.$set(t.formData,"InstallUnit_Id",e)},expression:"formData.InstallUnit_Id"}},[a("el-option",{attrs:{label:"选择安装单元",value:""}}),t._l(t.units,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})}))],2)],1)],1)],1),a("div",{staticClass:"tbox"},[a("p",[t._v("2.下载模板")]),a("el-button",{staticStyle:{border:"1px solid #D9DBE2",padding:"8px 20px"},attrs:{icon:"el-icon-document",type:"text"},on:{click:t.downTmpl}},[t._v(t._s(t.tmpl.title))])],1),a("div",{staticStyle:{background:"#F7F8F9",padding:"12px 20px",border:"1px solid #D9DBE2"}},[a("div",{staticStyle:{"margin-bottom":"16px"}},[t._v(" 3.上传文件 ")]),a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:"",limit:1,action:t.action,headers:t.reqHeader,multiple:t.multiple,accept:t.exts.map((function(t){return"."+t})).join(","),"file-list":t.fileList,"auto-upload":!1,"before-upload":t.beforeUpload,"on-progress":t.uploadProgressChange,"on-change":t.uploadStatusChange,"on-error":t.uploadError,"on-exceed":t.uploadExceed,"on-success":t.uploadSuccess,name:"files",data:t.formData},scopedSlots:t._u([{key:"file",fn:function(e){var i=e.file;return a("div",{},[a("div",{class:{"up-item":!0,error:"fail"===i.status}},[t.progresses[i.name]>0?a("div",{staticClass:"percent",style:{width:t.progresses[i.name]+"%"}}):t._e(),a("div",{staticClass:"bar"},[a("div",{staticClass:"title"},[a("svg-icon",{staticStyle:{height:"30px",width:"30px"},attrs:{name:t.extIcon(i),"icon-class":t.extIcon(i)}}),t._v(" "+t._s(i.name)+" ")],1),a("div",{staticClass:"remove"},["fail"===i.status?a("i",{staticClass:"el-icon-refresh-right",attrs:{title:"重新上传"},on:{click:function(e){return t.reUpload(i)}}}):t._e(),a("i",{staticClass:"el-icon-close",attrs:{title:"移除文件"},on:{click:function(e){return t.removeFile(i)}}})])])])])}}])},[a("svg-icon",{staticClass:"icon-svg",attrs:{name:"upload-icon","icon-class":"upload-icon",width:"200",height:"200"}}),a("div",{staticClass:"el-upload__text"},[t._v(" 将文件拖到此处，或"),a("em",[t._v("点击选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 支持格式："+t._s(t.exts.join("、"))+"，最大文件限制："+t._s(t.filesize)+"M ")])])],1)],1),a("div",{staticClass:"tbox",staticStyle:{"margin-top":"12px","flex-direction":"column","align-items":"flex-start"}},[a("div",{staticStyle:{"margin-bottom":"12px",wdith:"100%"}},[t._v(" 4.更新方式 ")]),a("el-row",{staticStyle:{width:"100%"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.formData.Import_Type,callback:function(e){t.$set(t.formData,"Import_Type",e)},expression:"formData.Import_Type"}},[a("el-option",{attrs:{label:"选择更新方式",value:""}}),a("el-option",{attrs:{label:"更新",value:2}}),a("el-option",{attrs:{label:"覆盖",value:1}})],1)],1),2===t.formData.Import_Type?[a("el-col",{attrs:{span:6}},[a("el-checkbox",{model:{value:t.formData.Is_Supplement,callback:function(e){t.$set(t.formData,"Is_Supplement",e)},expression:"formData.Is_Supplement"}},[t._v("补充")])],1),a("el-col",{attrs:{span:6}},[a("el-checkbox",{model:{value:t.formData.Is_Changed,callback:function(e){t.$set(t.formData,"Is_Changed",e)},expression:"formData.Is_Changed"}},[t._v("变更")])],1)]:t._e()],2)],1),t._e(),a("div",{staticStyle:{"text-align":"right","margin-top":"12px"}},[a("el-button",{attrs:{size:"mini",disabled:t.btnLoading},on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"success",size:"mini",loading:t.btnLoading},on:{click:t.beginUpload}},[t._v("导入")])],1)])},n=[]},"48f0":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 flex-pd16-wrap"},[a("div",{staticClass:"page-main-content"},[a("el-container",{staticStyle:{height:"100%"}},[a("el-header",{staticClass:"art-header"},[a("el-button",{attrs:{type:"danger",size:"mini"}},[t._v("删除")]),a("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.openDialog({title:"导入",width:"480px",component:"CUpload",props:{exts:["xls","xlsx"],tmpl:{title:"进度计划模板"}}})}}},[t._v("导入")])],1),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"art-main"},[t.loading?t._e():a("DynamicDataTable",{attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""},scopedSlots:t._u([{key:"op",fn:function(e){var i=e.row;e.column,e.$index;return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(e){return t.editRow(i)}}},[t._v("调整数量")]),t._v(" | "),a("el-link",{attrs:{type:"primary",underline:!1}},[t._v("编辑")]),t._v(" | "),a("el-link",{attrs:{type:"primary",underline:!1}},[t._v("删除")])]}}],null,!1,223385020)},[a("template",{slot:"f"},[a("el-select",{attrs:{value:"1",placeholder:"请选择"}},[a("el-option",{attrs:{label:"1",value:"1"}})],1)],1)],2)],1)],1)],1),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[a(t.dialogCfgs.component,t._b({tag:"component",attrs:{name:t.dialogCfgs.title}},"component",t.dialogCfgs.props,!1))],1)],1)],1)},n=[]},"4e82":function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),s=a("59ed"),o=a("7b0b"),r=a("07fa"),l=a("083a"),c=a("577e"),d=a("d039"),u=a("addb"),f=a("a640"),p=a("3f7e"),m=a("99f4"),h=a("1212"),g=a("ea83"),b=[],v=n(b.sort),_=n(b.push),x=d((function(){b.sort(void 0)})),I=d((function(){b.sort(null)})),y=f("sort"),C=!d((function(){if(h)return h<70;if(!(p&&p>3)){if(m)return!0;if(g)return g<603;var t,e,a,i,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(i=0;i<47;i++)b.push({k:e+i,v:a})}for(b.sort((function(t,e){return e.v-t.v})),i=0;i<b.length;i++)e=b[i].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),D=x||!I||!y||!C,S=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};i({target:"Array",proto:!0,forced:D},{sort:function(t){void 0!==t&&s(t);var e=o(this);if(C)return void 0===t?v(e):v(e,t);var a,i,n=[],c=r(e);for(i=0;i<c;i++)i in e&&_(n,e[i]);u(n,S(t)),a=r(n),i=0;while(i<a)e[i]=n[i++];while(i<c)l(e,i++);return e}})},"5b8b":function(t,e,a){"use strict";a.r(e);var i=a("80bb"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},"6bfa":function(t,e,a){"use strict";a.r(e);var i=a("48f0"),n=a("22fc");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("2c5e");var o=a("2877"),r=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"abcffc9c",null);e["default"]=r.exports},"7efa":function(t,e,a){"use strict";a("2d32")},"80bb":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("4de4"),a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");var n=i(a("b775")),s=(i(a("4328")),i(a("4360"))),o=a("5f87"),r=a("ed08");e.default={name:"CUpload",components:{},props:{action:{type:String,default:""},exts:{type:Array,default:function(){return[]}},tmpl:{type:Object,default:function(){return{}}},multiple:{type:Boolean,default:!1},filesize:{type:Number,default:5}},data:function(){return{apis:{GetProjectList:"/PRO/Project/GetProjectList",GetInstallUnitList:"/PRO/InstallUnit/GetInstallUnitList",ProductionDataTemplate:"/PRO/Component/ProductionDataTemplate"},templateUrl:"",updateType:"Is_Supplement",completed:!0,projs:[],units:[],formData:{Project_Id:"",InstallUnit_Id:"",Is_Changed:!1,Is_Supplement:!0,Import_Type:2,Type:0},fileList:[],progresses:{},reqHeader:null,btnLoading:!1}},watch:{"formData.Import_Type":function(t){(2===t||1===t)&&(this.formData["Is_Changed"]=!1,this.formData["Is_Supplement"]=!1)}},created:function(){this.tmpl.templateUrl&&(this.templateUrl=this.tmpl.templateUrl),this.getTemplate(),this.loadProjects(),this.reqHeader={},s.default.getters.token&&(this.reqHeader["Authorization"]=(0,o.getToken)()),s.default.getters.Last_Working_Object_Id&&(this.reqHeader.Last_Working_Object_Id=s.default.getters.Last_Working_Object_Id)},methods:{getTemplate:function(){var t=this;(0,n.default)({url:this.apis.ProductionDataTemplate,method:"post",data:{}}).then((function(e){e.IsSucceed&&(t.templateUrl=(0,r.combineURL)(t.$baseUrl,e.Data.split("/").map((function(t){return encodeURIComponent(t)})).join("/")))}))},downTmpl:function(){this.templateUrl&&window.open(this.templateUrl,"_blank")},cancel:function(){this.$refs.upload.clearFiles(),this.fileList=[],this.$emit("dialogCancel")},confirmImport:function(){this.$emit("dialogFormSubmitSuccess",{type:"reload"})},beginUpload:function(){return this.formData.Project_Id?this.formData.InstallUnit_Id?this.formData.Import_Type?(this.fileList=this.fileList.map((function(t){return t.status="ready",t})),this.btnLoading=!0,void this.$refs.upload.submit()):this.$message.warning("选择更新方式"):this.$message.warning("选择安装单元"):this.$message.warning("选择项目")},uploadExceed:function(){this.$message({message:"每次只能上传一个文件，如果需要上传新的文件，请删除原有的文件后再次上传",type:"warning"})},beforeUpload:function(t){var e=t.name.split(".").pop(),a=!0;this.exts.length>0&&(a=this.exts.indexOf(e)>-1);var i=!0;return i=t.size/1024/1024<this.filesize,a||this.$message.error("上传文件格式错误!"),i||this.$message.error("上传文件大小不能超过 "+this.filesize+"MB!"),a&&i},uploadProgressChange:function(t,e,a){var i=this;this.progresses[e.name]=t.percent,100===t.percent&&setTimeout((function(){i.progresses[e.name]=0,i.progresses=Object.assign({},i.progresses)}),600)},uploadStatusChange:function(t,e){"ready"===t.status?this.fileList.push(t):"fail"===t.status&&(this.fileList=this.fileList.concat([]));var a=!0;this.fileList.forEach((function(t){"success"!==t.status&&(a=!1)})),a&&(this.$message.success("导入成功"),this.$emit("dialogFormSubmitSuccess",{type:"reload",data:null}),this.cancel()),this.btnLoading=!1},uploadError:function(t,e,a){},uploadSuccess:function(t,e,a){if(!t.IsSucceed){e.status="fail",this.$message.error(t.Message);try{var i=(0,r.combineURL)(this.$baseUrl,t.Data.split("/").map((function(t){return encodeURIComponent(t)})).join("/"));window.open(i,"_blank")}catch(n){}}},reUpload:function(t){t.status="ready",this.$refs.upload.submit()},removeFile:function(t){this.fileList=this.fileList.filter((function(e){return e.name!==t.name}))},loadProjects:function(){var t=this;(0,n.default)({url:this.apis.GetProjectList,method:"post",data:{}}).then((function(e){e.IsSucceed&&(t.projs=e.Data)}))},projSelectHandle:function(t){this.formData.InstallUnit_Id="",t?this.loadInstallUnitsList():this.units=[]},loadInstallUnitsList:function(){var t=this;(0,n.default)({url:this.apis.GetInstallUnitList,method:"post",data:{Project_Id:this.formData.Project_Id}}).then((function(e){e.IsSucceed&&e.IsSucceed&&(t.units=e.Data)}))},extIcon:function(t){var e="document_unknown_icon";switch(t.name.split(".").pop()){case"xls":case"xlsx":e="document_form_icon";break;case"txt":e="document_txt_icon";break;case"doc":case"docx":e="document_word_icon";break;case"zip":case"7z":case"rar":e="document_zip_icon";break;case"png":case"jpg":case"jpeg":case"gif":case"bmp":e="multimedia_image_icon";break;case"ppt":case"pptx":e="document_ppt_icon";break;case"pdf":e="document_pdf_icon";break}return e}}}},86112:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7"),a("25f0");var n=i(a("0f97")),s=i(a("fa4a")),o=a("6186"),r=i(a("b775"));e.default={components:{DynamicDataTable:n.default,CUpload:s.default},data:function(){return{loading:!0,gridCode:"Priority_Analyse_List",tbConfig:{},columns:[],data:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},created:function(){var t=this;(0,o.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){return t.tbConfig.Data_Url="/SYS/Log/GetLogList",t.getTableData()})).then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center"})},setCols:function(t){this.columns=t},setGridData:function(t){this.data=t.Data},getTableData:function(){return this.tbConfig.Data_Url?(0,r.default)({url:this.tbConfig.Data_Url,method:"post",data:{}}):Promise.reject("invalid data api...")},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},editRow:function(t){t.onEdit=!0}}}},a068:function(t,e,a){},e6ad4:function(t,e,a){},f784:function(t,e,a){"use strict";a("a068")},fa4a:function(t,e,a){"use strict";a.r(e);var i=a("45de"),n=a("5b8b");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("f784"),a("7efa");var o=a("2877"),r=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"55c7c63c",null);e["default"]=r.exports}}]);