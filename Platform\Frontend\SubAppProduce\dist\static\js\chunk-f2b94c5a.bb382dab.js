(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-f2b94c5a"],{"0206":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"bim-dialog",attrs:{"append-to-body":e.appendToBody,title:e.dialogTitle,"diy-name":e.diyName,visible:e.dialogVisible,width:e.dialogWidth,"before-close":e.handleClose,"close-on-click-modal":!1,top:e.top,modal:e.modal,"modal-append-to-body":e.modalAppendToBody},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{staticClass:"dialogDiv",on:{drop:function(t){return t.preventDefault(),e.onDrop(t)},dragover:function(t){return t.preventDefault(),e.onDragover(t)}}},[e._t("default",[a("p",[e._v("弹框自定义的内容")])])],2),e.hidebtn?e._e():e._t("btngroup",[a("span",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.Cancel}},[e._v("取 消")]),e.diyName?[a("el-button",{on:{click:function(t){return e.operateDiyButton(e.diyName)}}},[e._v(e._s(e.diyName))])]:e._e(),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.Submit}},[e._v(e._s(e.confirmBtnText))])],2)],{slot:"footer"})],2)},i=[]},"0339":function(e,t,a){"use strict";a.r(t);var n=a("5683"),i=a("892b");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("2e0f");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"4a1e51b6",null);t["default"]=l.exports},"06a2":function(e,t,a){"use strict";a.r(t);var n=a("5f83"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"0ce7":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),r=n(a("1da1"));a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var o=n(a("9b15")),l=a("5c96"),s=a("21c4"),c=a("6186"),d=function(){(0,c.SecurityToken)().then((function(e){sessionStorage.setItem("ossToken",JSON.stringify(e.Data))}))};d(),setInterval((function(){d()}),114e4);t.default={name:"OSSUpload",mixins:[l.Upload],props:{ossOnSuccess:{type:Function,default:Function},piecesize:{type:Number,default:2},httpRequest:{type:Function,default:function(e){var t=this,a=JSON.parse(sessionStorage.getItem("ossToken"));this.$nextTick((function(){var n,l=null!==(n=t.data)&&void 0!==n&&n.piecesize?1*t.data.piecesize:2,d=new o.default({region:"oss-"+a.regionId,secure:!0,accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken,bucket:a.bucket,refreshSTSToken:function(){var e=(0,r.default)((0,i.default)().m((function e(){var a;return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.securityToken();case 1:return a=e.v,e.a(2,{accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken})}}),e)})));function a(){return e.apply(this,arguments)}return a}(),refreshSTSTokenInterval:9e5}),u=e.file,f=new Date;d.multipartUpload((0,s.getTenantId)()+"/"+f.getFullYear()+"/"+(1*f.getMonth()+1)+"/"+f.getDate()+"/"+f.getMinutes()+"_"+f.getSeconds()+"_"+f.getMilliseconds()+"/"+u.name,u,{progress:function(t,a){this.process=a,e.onProgress({percent:Math.floor(100*t)})},parallel:4,partSize:1048576*l,meta:{}}).then((function(t){if(200===t.res.statusCode){var a,n=t.res.requestUrls[0]&&t.res.requestUrls[0].split("?")[0];!1===(null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.callback)?e.onSuccess({Data:n+"*"+u.size+"*"+u.name.substr(u.name.lastIndexOf("."))+"*"+u.name}):(0,c.GetOssUrl)({url:n}).then((function(t){e.onSuccess({Data:n+"*"+u.size+"*"+u.name.substr(u.name.lastIndexOf("."))+"*"+u.name,encryptionUrl:t.Data})}))}}),(function(t){e.onError(t)}))}))}}},data:function(){return{process:null}},watch:{process:function(e){this.$emit("getprocess",e)}},mounted:function(){},methods:{handleFn:function(e){},securityToken:function(){return new Promise((function(e,t){(0,c.SecurityToken)({}).then((function(t){e(t.Data)})).catch((function(e){t(e)}))}))}}}},"0f51":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("3b6a");t.default={props:{dialogTitle:{type:String,default:""},visible:{type:Boolean,default:!1},dialogWidth:{type:String,default:"960px"},appendToBody:{type:Boolean,default:!1},hidebtn:{type:Boolean,default:!1},top:{type:String,default:"15vh"},confirmBtnText:{type:String,default:"确 定"},diyName:{type:String,default:""},modal:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},modalAppendToBody:{type:Boolean,default:!0}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("updateVisible",e)}}},methods:{onDrop:function(e){e.preventDefault()},onDragover:function(e){e.preventDefault()},Cancel:function(){this.$emit("cancelbtn")},Submit:function(){this.$emit("submitbtn")},handleClose:function(){this.$emit("handleClose")},operateDiyButton:function(e){this.$emit("getdiybutton",e)}}}},1389:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("a630"),a("caad"),a("a15b"),a("d81d"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("466d"),a("159b"),a("ddb0");var i=n(a("c14f")),r=n(a("1da1")),o=n(a("2909")),l=n(a("5530")),s=a("0d9a"),c=n(a("bc3a")),d=n(a("c7ab")),u=a("b28f"),f=a("5012"),p=a("c24f"),m=a("0e9a"),h=a("21c4"),b=n(a("bf8b")),g=a("e144"),v=a("ed08"),y=a("2ef0"),_=(a("f382"),a("2f62")),I={Id:"",Doc_Catelog:"",Doc_Type:"",Project_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",IsChanged:!1,Is_Load:!1,Doc_File:"",ishistory:!0,BimId:"",Drawing_Match_Type:1,Is_Big_File:!1,model:{UserIds:[],Title:"",Content:""},Component_Codes:[]};t.default={components:{OSSUpload:d.default,formItem:b.default},props:{typeEntity:{type:Object,default:function(){}},isCzd:{type:Boolean,default:!1},isLJXT:{type:Boolean,default:!1},isBJXT:{type:Boolean,default:!1},isGjXT:{type:Boolean,default:!1}},data:function(){return{isNotify:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,btnLoading:!1,form:(0,l.default)({},I),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}],Drawing_Match_Type:[{required:!0,message:"请选择",trigger:"change"}]},treeParams:{clickParent:!0,filterable:!1,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},selectParams:{multiple:!1,clearable:!0,placeholder:"请输入内容"},fileType:"",curFile:"",bimvizId:"",isDeep:!1,projectId:"",professionalCode:"",isSHQD:"",isEditSCYTH:!1,modelApp:"glendale",uploadFileLength:0,fileNums:0,compList:[],selectLoading:!1,allCompList:[],allPartList:[],relatedItem:[],disabledOptions:[!1,!1,!1]}},computed:(0,l.default)({drawingList:function(){var e=this.isComp?"构件号":this.isPart?"零件名":"部件名";return[{label:"名称",value:1,disabled:this.disabledOptions[0]},{label:"".concat(e,"_页码_版本"),value:2,disabled:this.disabledOptions[1]},{label:"".concat(e,"_版本"),value:3,disabled:this.disabledOptions[2]}]},BIMFiles:function(){return"PLMBimFiles"===this.$route.name},isGlendale:function(){return this.BIMFiles&&"glendale"===this.modelApp},CadFiles:function(){return"PRODeepenFiles"===this.$route.name},isCZD:function(){return this.isCzd||"原材料材质单"===this.form.Type_Name},isComp:function(){return"构件详图"===this.form.Type_Name||this.isGjXT},isPart:function(){return"零件详图"===this.form.Type_Name||this.isLJXT},isUnitPart:function(){return"部件详图"===this.form.Type_Name||this.isBJXT}},(0,_.mapGetters)("tenant",["isVersionFour"])),watch:{"fileList.length":function(e){if(this.isVersionFour){var t=this.fileList.some((function(e){return!e.name.includes("_")})),a=this.fileList.every((function(e){return(e.name.match(/_/g)||[]).length>=1})),n=this.fileList.every((function(e){return(e.name.match(/_/g)||[]).length>=2}));this.disabledOptions=[!1,!1,!1],t?(this.disabledOptions[1]=!0,this.disabledOptions[2]=!0,2!==this.form.Drawing_Match_Type&&3!==this.form.Drawing_Match_Type||(this.form.Drawing_Match_Type=1)):n?(this.disabledOptions[1]=!1,this.disabledOptions[2]=!1):a&&(this.disabledOptions[1]=!0,this.disabledOptions[2]=!1,2===this.form.Drawing_Match_Type&&(this.form.Drawing_Match_Type=1))}}},created:function(){this.fileType=this.$route.name,"PLMPicVideoFiles"===this.fileType&&(this.allowFile="image/*"),this.BIMFiles&&(this.allowFile=".ifc,.bzip,.bzip2,.glzip"),this.isVersionFour||delete this.form.Drawing_Match_Type},methods:{getCompList:function(){var e=this;this.isPart?(0,s.GetPartCodeList)({SysProjectId:this.projectId,type:2}).then((function(t){t.IsSucceed?e.allPartList=t.Data:e.$message({message:t.Message,type:"error"})})):this.isUnitPart?(0,s.GetPartCodeList)({SysProjectId:this.projectId,type:3}).then((function(t){t.IsSucceed?e.allPartList=t.Data:e.$message({message:t.Message,type:"error"})})):(0,s.GetProjectComponentCodeList)({Sys_Project_Id:this.projectId}).then((function(t){t.IsSucceed?e.allCompList=t.Data||[]:e.$message({message:t.Message,type:"error"})}))},remoteMethod:function(e){var t=this;e?(this.selectLoading=!0,setTimeout((function(){var a=e.split(" ").map((function(e){return e.toLowerCase()})).filter((function(e){return""!==e}));t.compList=(t.isPart||t.isUnitPart?t.allPartList:t.allCompList).filter((function(e){return a.length>1?a.some((function(t){return e.toLowerCase()===t})):a.some((function(t){return e.toLowerCase().includes(t)}))})),t.compList=t.compList.map((function(e){return{label:e,value:e}})),t.compList.length>0&&t.compList.unshift({label:"全部",value:"all"}),t.selectLoading=!1}),200)):this.compList=[]},selectChange:function(e){if(e.includes("all")){var t=(0,v.deepClone)(this.compList);t.shift(),this.relatedItem=this.relatedItem.filter((function(e){return"all"!==e})),this.relatedItem=Array.from(new Set([].concat((0,o.default)(this.relatedItem),(0,o.default)(t.map((function(e){return e.value}))))))}},visibleChange:function(e){e||this.remoteMethod()},onExceed:function(){"edit"===this.type?this.$message.error("最多只能上传1个文件"):this.$message.error("最多只能上传1000个文件")},getTemplate:function(){var e=this,t="plm_steels_detailImport,".concat(this.professionalCode);(0,f.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,m.downloadBlobFile)(t,"".concat(e.form.Type_Name,"_深化清单导入模板"))}))},handleRadioChange:function(e){e&&"edit"===this.type&&this.form.Doc_Title&&(this.form.model.Title="资料变更通知："+this.form.Doc_Title)},handleChange:function(e,t){this.uploadFileLength=t.length},beforeUpload:function(e){this.curFile=e,this.loading=!0,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this,n=0;this.fileList.filter((function(t,a){t.name===e.name&&(n=a)})),this.fileList.splice(n,1),this.attachments.splice(n,1);var i="",r="";t.forEach((function(e){i=i+","+e.name.substring(0,e.name.lastIndexOf(".")),r=r+","+e.name})),this.form.Doc_Title=i.substring(1),this.form.Doc_Content=i.substring(1),this.form.Doc_File=r.substring(1),this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.loading=!t.every((function(e){return"success"===e.status})),setTimeout((function(){a.btnLoading=!t.every((function(e){return"success"===e.status}))}),1e3)},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){var n=this;a=(0,y.uniqBy)(a,"name"),this.fileList=a,this.attachments=this.fileList.filter((function(e){return"success"===e.status})).map((function(e){return{File_Url:e.response.Data.split("*")[0],File_Size:e.response.Data.split("*")[1],File_Type:e.response.Data.split("*")[2],File_Name:e.response.Data.split("*")[3]}}));var i=this.attachments.map((function(e){return e.File_Name})).join(","),r=this.attachments.map((function(e){return e.File_Name.substring(0,e.File_Name.lastIndexOf("."))})).join(",");if(this.form.Doc_Title=this.form.Doc_Content=r,this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.form.Doc_File=i,this.loading=!a.every((function(e){return"success"===e.status})),this.fileNums++,this.fileNums>this.uploadFileLength-10&&a.some((function(e){return"ready"===e.status}))){var o=a.filter((function(e){return"ready"===e.status}));o.forEach((function(e){n.retryUpload(e)}))}setTimeout((function(){n.btnLoading=!a.every((function(e){return"success"===e.status}))}),1e3)},retryUpload:function(e){this.$refs.companyUpload.submit(e)},handleOpen:function(e,t,a){var n=this,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]&&arguments[5];this.isNotify=!1,this.projectId=r,this.isDeep=i,this.form=(0,l.default)((0,l.default)({},I),{},{model:{UserIds:[],Title:"",Content:""}}),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.isSHQD=t.isSHQD,this.professionalCode=t.Code,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.isEditSCYTH=o,this.fileNums=0,"add"===this.type?(this.fileList=[],this.title="新增文件",this.form.Id=""):(this.title="编辑文件",(0,s.AttachmentGetEntities)({recordId:t.Id}).then((function(e){n.attachments=e.Data.map((function(e){return delete e.Id,e})),n.fileList=e.Data.map((function(e){return e.name=e.File_Name,e}))})),(0,s.FileGetEntity)({id:t.Id}).then((function(e){e.IsSucceed&&(n.form=(0,l.default)((0,l.default)({},e.Data),{},{model:{UserIds:[],Title:"",Content:""}}),n.isCZD&&(n.relatedItem=e.Data.Component_Codes),(n.isComp||n.isPart||n.isUnitPart)&&(n.relatedItem=e.Data.Codes))}))),this.$nextTick((function(e){n.getCompList()}))},handleClose:function(){try{this.relatedItem=[],this.attachments=[],this.$refs["form"].resetFields(),this.isNotify=!1,this.btnLoading=!1,this.loading=!1,this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,r.default)((0,i.default)().m((function t(a){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!a){t.n=6;break}if(e.loading=!0,e.btnLoading=!0,"edit"!==e.type){t.n=1;break}return e.updateInfo(),t.a(2);case 1:if(!e.CadFiles||!0!==e.form.Is_Big_File){t.n=4;break}return t.n=2,(0,p.getConfigure)({code:"glendale_cad_token"});case 2:return u.Glendale.cadToken=t.v.Data,t.n=3,(0,p.getConfigure)({code:"glendale_cad_url"});case 3:u.Glendale.cadUrl=t.v.Data,c.default.all(e.fileList.map((function(e,t){var a=e.raw.name.split("."),n=a[a.length-1];if("glzip"===n||"dwg"===n){var i=new FormData;i.append("file",e.raw);var r=(0,l.default)((0,l.default)({},u.Glendale.optionsCad),{},{UniqueCode:"".concat((0,h.getTenantId)(),"__").concat((0,g.v4)()),Name:encodeURIComponent(e.name)});return c.default.post("".concat(u.Glendale.cadUrl).concat(u.Glendale.uploadUrl,"?input=").concat(JSON.stringify(r)),i,{headers:{Token:u.Glendale.cadToken,"Content-Type":"multipart/form-data"}}).then((function(t){e.BimId=t.data.datas.lightweightName}))}}))).then((function(t){var a=e.attachments.map((function(t){var a=e.fileList.find((function(e){return e.name===t.File_Name}));return a.BimId}));e.form.BimId=a.join(","),e.updateInfo()})),t.n=5;break;case 4:e.updateInfo();case 5:t.n=7;break;case 6:return e.$message({message:"请将表单填写完整",type:"warning"}),t.a(2,!1);case 7:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},updateInfo:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,r,o,l,c,d,u;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(a=Array.from(new Set(e.attachments.map((function(e){return e.File_Name})))).map((function(t){return e.attachments.find((function(e){return e.File_Name===t}))})),n=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),o={},o=e.isNotify?e.form.model:{UserIds:[],Title:"",Content:""},(e.isComp||e.isPart||e.isUnitPart)&&(l=e.relatedItem),e.isCZD&&(e.form.Component_Codes=e.relatedItem),c=e.isComp?0:e.isPart?1:e.isUnitPart?3:null,"add"!==e.type){t.n=2;break}return d=e.isDeep?s.AddDeepFile:s.FileAdd,u={file:e.form,model:o,attachmentList:a,codes:l,type:c},e.isDeep&&(u.file.Type_Name=e.professionalCode),e.isDeep&&"2"===n&&(u.projectId=e.projectId,u.factoryId=localStorage.getItem("CurReferenceId")),e.isDeep||"2"!==n||(u.file.Project_Id=e.projectId),t.n=1,d(u);case 1:r=t.v,t.n=4;break;case 2:return t.n=3,(0,s.FileEdit)({file:e.form,model:o,attachmentList:a,codes:l,type:c});case 3:r=t.v;case 4:r.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.$emit("getData",e.form.Doc_Type),e.loading=!1,e.btnLoading=!1,e.handleClose()):(e.loading=!1,e.btnLoading=!1,e.$message.error(r.Message));case 5:return t.a(2)}}),t)})))()},getAllLoadingFiles:function(){var e=this;(0,s.GetLoadingFiles)().then((function(t){e.updateLoadingFiles(t.Data)}))},updateLoadingFiles:function(e){var t=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,key:BIM_API_CONFIG.KEY}),a=t.getModelProjectManager(),n={SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,LoadFiles:e};a.updateSceneSettings(BIM_API_CONFIG.USER_NAME,this.bimvizId,n,(function(e){}))}}}},1771:function(e,t,a){"use strict";a.r(t);var n=a("e447"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"1cdf":function(e,t,a){"use strict";a.r(t);var n=a("6cd2"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"270c":function(e,t,a){"use strict";a("fd51")},"2e00":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetEpcEntity=r,t.GetUserListByObjId=l,t.SaveEpcProject=o;var i=n(a("b775"));function r(e){return(0,i.default)({url:"/EPC/Project/GetEpcEntity",method:"post",data:e})}function o(e){return(0,i.default)({url:"/EPC/Project/SaveEpcProject",method:"post",data:e})}function l(e){return(0,i.default)({url:"/EPC/Project/GetUserListByObjId",method:"post",data:e})}},"2e0f":function(e,t,a){"use strict";a("a1c1")},"31b1":function(e,t,a){"use strict";a.r(t);var n=a("3895"),i=a("b2e8");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("270c");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"d9a81c76",null);t["default"]=l.exports},"36f3":function(e,t,a){"use strict";a.r(t);var n=a("0206"),i=a("7ba4");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("aa20");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,null,null);t["default"]=l.exports},3895:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plmdialog plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose,close:e.handleClose}},[e.isDeep||e.isSHQD?a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1):e._e(),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[e.isCZD||e.isComp||e.isPart||e.isUnitPart?a("el-form-item",{attrs:{label:e.isPart?"关联零件":e.isComp?"关联构件":e.isUnitPart?"关联部件":"关联构件",prop:"Type_Name"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{filterable:"",remote:"",multiple:"","reserve-keyword":"",loading:e.selectLoading,"remote-method":e.remoteMethod},on:{"visible-change":e.visibleChange,change:e.selectChange},model:{value:e.relatedItem,callback:function(t){e.relatedItem=t},expression:"relatedItem"}},e._l(e.compList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),a("el-form-item",{attrs:{label:"是否变更",prop:"IsChanged"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("否")])],1),a("el-form-item",{attrs:{label:"是否大型图纸文件",prop:"Is_Big_File","label-width":"193px"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v(" 是否大型图纸文件 "),a("el-tooltip",{attrs:{content:"常规导入选“否”，当导入布置图时请选“是”",placement:"top"}},[a("i",{staticClass:"el-icon-question"})])],1),a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Big_File,callback:function(t){e.$set(e.form,"Is_Big_File",t)},expression:"form.Is_Big_File"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Big_File,callback:function(t){e.$set(e.form,"Is_Big_File",t)},expression:"form.Is_Big_File"}},[e._v("否")])],1),e.isVersionFour&&(e.isComp||e.isPart||e.isUnitPart)?a("el-form-item",{attrs:{label:"图纸命名匹配方式",prop:"Drawing_Match_Type","label-width":"180px"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.Drawing_Match_Type,callback:function(t){e.$set(e.form,"Drawing_Match_Type",t)},expression:"form.Drawing_Match_Type"}},e._l(e.drawingList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,disabled:e.disabled,value:e.value}})})),1)],1):e._e(),"新增文件"==e.title?a("el-form-item",{attrs:{label:"是否将重复文件移入历史文件库",prop:"ishistory","label-width":"260px"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("否")])],1):e._e(),e.BIMFiles?a("el-form-item",{attrs:{label:"是否加载",prop:"Is_Load"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("否")])],1):e._e(),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"companyUpload",staticClass:"upload-demo",attrs:{drag:"",data:{callback:!1},action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:e.isDeep||e.isSHQD||e.isEditSCYTH||"edit"===e.type?1:1e3,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:"",accept:e.allowFile,disabled:"edit"===e.type&&e.BIMFiles,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("el-form-item",{attrs:{label:"是否通知"}},[a("el-radio",{attrs:{label:!0},on:{input:e.handleRadioChange},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("否")])],1),e.isNotify?[a("el-form-item",{attrs:{label:"通知标题",prop:"model.Title",rules:{required:!0,message:"通知标题不能为空",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.model.Title,callback:function(t){e.$set(e.form.model,"Title",t)},expression:"form.model.Title"}})],1),a("el-form-item",{attrs:{label:"通知人员",prop:"model.UserIds",rules:[{required:!0,message:"请选择通知人员",trigger:"change"}]}},[a("form-item",{attrs:{"project-id":e.projectId,type:"contacts",filterable:"",multiple:"",width:"360px"},model:{value:e.form.model.UserIds,callback:function(t){e.$set(e.form.model,"UserIds",t)},expression:"form.model.UserIds"}})],1),a("el-form-item",{attrs:{label:"通知内容"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{type:"textarea"},model:{value:e.form.model.Content,callback:function(t){e.$set(e.form.model,"Content",t)},expression:"form.model.Content"}})],1)]:e._e()],2),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},i=[]},4529:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("e9c4"),a("d3b7");var i=n(a("a7c7")),r=a("0d9a"),o=a("ed08");t.default={name:"History",components:{"el-dialog":i.default},data:function(){return{historyVisible:!1,filter:"",tablaData:[],ids:[]}},methods:{handleOpen:function(e){var t=this;this.tablaData=[],this.historyVisible=!0,(0,r.FileHistory)({id:e}).then((function(e){t.tablaData=e.Data.Data,t.list=(0,o.deepClone)(t.tablaData)}))},search:function(){var e=this;this.tablaData=this.list.filter((function(t){var a=JSON.stringify(t);return-1!==a.indexOf(e.filter)}))},preview:function(e){this.$emit("preview",e)},selectionChange:function(e){this.ids=e.map((function(e){return e.Id}))},download:function(){this.ids.length?this.$emit("download",this.ids):this.$message.warning("请先选择记录")}}}},"4e74":function(e,t,a){"use strict";a.r(t);var n=a("b86b0"),i=a("06a2");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("bc11");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"1c53619e",null);t["default"]=l.exports},5012:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Add=_,t.AddSteel=I,t.AddSynNew=c,t.BatchAdd=y,t.BatchEdit=v,t.DeleteBatch=m,t.DeletePackage=S,t.DeleteSteel=D,t.Deletepart=$,t.DeletepartByfindkeywodes=j,t.DownAllFiles=p,t.EditPackage=w,t.EditPartpage=F,t.EditPartpagelist=B,t.ExportComponentList=f,t.ExportPlanpartInfo=N,t.ExportPlanpartcountInfo=O,t.GetAreaPageList=k,t.GetEntities=u,t.GetEntity=h,t.GetList=P,t.GetPageStorageBySearchPackages=o,t.GetPageStorageHistorySteel=s,t.GetPageStorageSteelsBySearchItem=l,t.GetPartEntity=L,t.GetPartWeightList=E,t.GetProfessEntities=g,t.GetSummaryStorageBySearch=T,t.PackageGetEntity=C,t.PartDeepeningImportTemplate=M,t.ProjectSchedule=x,t.SaveEntity=b,t.SteelBardcodeDataTemplate=d;var i=n(a("4328")),r=n(a("b775"));function o(e){return(0,r.default)({url:"/plm/package/GetPageStorageBySearchPackages",method:"post",data:e})}function l(e){return(0,r.default)({url:"/plm/package/GetPageStorageSteelsBySearchItem",method:"post",data:e})}function s(e){return(0,r.default)({url:"/plm/component/GetPageStorageHistorySteel",method:"post",data:e})}function c(e){return(0,r.default)({url:"/plm/Component/AddSynNew",method:"post",data:e,timeout:18e5})}function d(e){return(0,r.default)({url:"/plm/Component/SteelBardcodeDataTemplate",method:"post",data:e})}function u(e){return(0,r.default)({url:"/plm/plm_project_areas/GetEntities",method:"post",data:e})}function f(e){return(0,r.default)({url:"/plm/Component/ExportComponentList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/plm/Component/DownAllFiles",method:"post",data:e,responseType:"blob"})}function m(e){return(0,r.default)({url:"/plm/Component/DeleteBatch",method:"post",data:e})}function h(e){return(0,r.default)({url:"/plm/Component/GetEntity",method:"post",data:e})}function b(e){return(0,r.default)({url:"/plm/Component/SaveEntity",method:"post",data:e})}function g(e){return(0,r.default)({url:"/plm/Plm_Professional_Type/GetEntities",method:"post",data:e})}function v(e){return(0,r.default)({url:"/plm/Component/BatchEdit",method:"post",data:i.default.stringify(e)})}function y(e){return(0,r.default)({url:"/plm/Package/BatchAdd",method:"post",data:e})}function _(e){return(0,r.default)({url:"/plm/Package/Add",method:"post",data:e})}function I(e){return(0,r.default)({url:"/plm/Package/AddSteel",method:"post",data:e})}function C(e){return(0,r.default)({url:"/plm/Package/GetEntity",method:"post",data:i.default.stringify(e)})}function w(e){return(0,r.default)({url:"/plm/Package/EditPackage",method:"post",data:e})}function S(e){return(0,r.default)({url:"/plm/Package/DeletePackage",method:"post",data:e})}function D(e){return(0,r.default)({url:"/plm/Package/DeleteSteel",method:"post",data:i.default.stringify(e)})}function P(e){return(0,r.default)({url:"/plm/Plm_Professional_Type/GetList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/plm/Plm_Project_Areas/GetAreaPageList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PLM/Component/GetSummaryStorageBySearch",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PLM/Plm_SteelsList/ProjectSchedule",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/Part/PartDeepeningImportTemplate",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/Part/GetPartEntity",method:"post",data:e,timeout:12e5})}function F(e){return(0,r.default)({url:"/PRO/Part/EditPartpage",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/Part/EditPartpagelist",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Part/DeletepartByfindkeywodes",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/Part/ExportPlanpartInfo",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Part/ExportPlanpartcountInfo",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/Part/GetPartWeightList",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/Part/Deletepart",method:"post",data:e})}},"50d9":function(e,t,a){"use strict";a("ecba")},5529:function(e,t,a){"use strict";a.r(t);var n=a("9908c"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},5683:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{"dialog-title":"历史记录","dialog-width":"1200px",visible:e.historyVisible,hidebtn:""},on:{"update:visible":function(t){e.historyVisible=t},handleClose:function(t){e.historyVisible=!1}}},[a("el-row",{attrs:{type:"flex",justify:"space-between"}},[a("el-button",{on:{click:e.download}},[a("i",{staticClass:"el-icon-download"}),e._v(" 下载")]),a("el-input",{staticClass:"input-with-select",attrs:{placeholder:"请输入内容"},model:{value:e.filter,callback:function(t){e.filter=t},expression:"filter"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.search},slot:"append"})],1)],1),a("div",{staticClass:"plm-bimtable"},[a("el-table",{staticStyle:{"margin-top":"20px"},attrs:{data:e.tablaData},on:{"selection-change":e.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"Doc_Title",label:"标题"}}),a("el-table-column",{attrs:{prop:"Doc_Content",label:"简要描述"}}),a("el-table-column",{attrs:{prop:"Doc_File",label:"附件信息"}}),a("el-table-column",{attrs:{prop:"Create_UserName",label:"变更人",width:"90px"}}),a("el-table-column",{attrs:{prop:"Create_Date",label:"变更时间",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("timeFormat")(t.row.Create_Date,"{y}-{m}-{d} {h}:{i}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.preview(t.row)}}},[e._v("查看")])]}}])})],1),a("div",{staticClass:"sum"},[e._v(" 已选"+e._s(e.ids.length)+"条数据 ")])],1)],1)},i=[]},"5f83":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),r=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("ac1f"),a("25f0"),a("2532"),a("3ca3"),a("841c"),a("159b"),a("ddb0");var o=n(a("1463")),l=n(a("b878")),s=n(a("31b1")),c=n(a("36f3")),d=n(a("d563")),u=n(a("0339")),f=n(a("bc3a")),p=a("21a6"),m=n(a("c4e3")),h=a("0e9a"),b=a("d363"),g=a("361f"),v=a("0d9a"),y=(a("6186"),a("2e00")),_=a("21c4"),I=n(a("8325")),C=n(a("d7b0")),w=n(a("2b0e")),S=a("dc02"),D=a("8975"),P=a("1b69"),k=a("6186"),T=a("c685"),x=a("f4f2"),M=a("f382"),L=n(a("21f5"));w.default.use(S.VueJsonp);var F,B=null;t.default={name:"ProjectFiles",directives:{clipboard:I.default},components:{TreeDetail:o.default,AddEditDep:l.default,bimdialog:s.default,"el-dialog":c.default,history:u.default,QrcodeVue:C.default,scan:d.default,ElTableEmpty:L.default},props:{viewBy:{type:String,default:"list"}},data:function(){return{tablePageSize:T.tablePageSize,checkedItems:[],selecting:!1,pageInfo:{Page:1,PageSize:20,TotalCount:0},fileModel:{typeId:"",doc_Catelog:"",Title:"",startTime:"",endTime:""},type:"",data:{Id:"",Catalog_Code:"",name:""},activeName:"first",filterText:"",areaName:"",areaList:[],treeData:[],tableData:[],isGjXT:!1,isLJXT:!1,isBJXT:!1,isCom:!1,treeLoading:!1,DepartmentName:"",DepartmentId:"",expandedKey:"",elsebuttons:[],attachments:[],imageType:[".png",".jpg",".jpeg",".gif"],dialogVisible:!1,moveToFolderId:"",ids:[],videoType:[".mp4",".mv",".avi",".3gp"],bimvizId:null,bimvizStatus:"",selectNames:[],shareUrl:"",shareVisible:!1,shareTime:"",selectList:[],tableLoading:!1,dateRange:[],projectName:"",pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},currImageIndex:0,notify:"",projectList:[],projectId:"",mode:"",fileOperate:!1,treeOperate:!1,isCZD:!1}},computed:{isCheckedAll:function(){return this.tableData.length===this.ids.length},srcList:function(){return this.tableData.map((function(e){return e.File_Url}))},PLMBIMFiles:function(){return"PLMBimFiles"===this.$route.name},PLMDeepenFiles:function(){return"PLMDeepenFiles"===this.$route.name},EPCBIMFiles:function(){return"EPCBimFiles"===this.$route.name},BIMFiles:function(){return"EPCBimFiles"===this.$route.name||"PLMBimFiles"===this.$route.name},PLMStandard:function(){return"PLMStandard"===this.$route.name||"PLMTechnologyFiles"===this.$route.name},isGCLXD:function(){return"工程联系单"===this.data.name},isHTPSJL:function(){return"合同评审记录"===this.data.name}},watch:{ids:{handler:function(){var e=this;this.selectList=this.tableData.filter((function(t){return e.ids.includes(t.Id)}))},deep:!0},dateRange:function(e){try{this.fileModel.startTime=e[0],this.fileModel.endTime=e[1]}catch(t){this.fileModel.startTime=null,this.fileModel.endTime=null}},"fileModel.typeId":function(){var e=this;this.$nextTick((function(){e.$refs.table.doLayout()}))}},created:function(){this.type=this.$route.name,this.fileModel.doc_Catelog="PLMDeepenFiles",this.data.Catalog_Code=this.$route.name,this.projectName=localStorage.getItem("ProjectName")},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(a=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),"2"!==a){t.n=2;break}return e.mode="factory",e.fileOperate=!1,t.n=1,e.getProjectList();case 1:e.projectList.length&&e.fetchTreeData(),t.n=3;break;case 2:e.fileOperate=!0,e.fetchTreeData();case 3:e.getData(),e.BIMFiles&&(e.getBimvizStatus(),F=setInterval((function(){e.getBimvizStatus()}),1e4));case 4:return t.a(2)}}),t)})))()},destroyed:function(){clearInterval(F)},methods:{resetSearch:function(){this.areaName="",this.dateRange="",this.search()},getAreaList:function(){var e=this;(0,P.GetTuzhiInstallUnitList)({typeId:this.fileModel.typeId,Projectid:this.fileModel.Projectid}).then((function(t){t.IsSucceed?(e.areaList=(t.Data||[]).map((function(e){return{value:e,label:e||"全部"}})),e.areaName=""):e.$message({message:t.Message,type:"error"})}))},projectChange:function(){sessionStorage.setItem("cadFileProjectId",this.projectId),this.fetchTreeData(),this.getData()},getProjectList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,e.tableLoading=!0,t.n=1,(0,P.GetProjectPageList)({PageSize:-1});case 1:a=t.v,e.projectList=a.Data.Data,a.Data.Data.length?e.projectId=sessionStorage.getItem("cadFileProjectId")?sessionStorage.getItem("cadFileProjectId"):a.Data.Data[0].Sys_Project_Id:(e.$message.error("暂无项目"),e.treeLoading=!1,e.tableLoading=!1);case 2:return t.a(2)}}),t)})))()},getBimvizStatus:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getBimVizId();case 1:a=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,restport:BIM_API_CONFIG.MODEL_PORT,key:BIM_API_CONFIG.KEY}),n=a.getProjectBuildManager(),n.getRebuildSceneState(BIM_API_CONFIG.USER_NAME,e.bimvizId,(function(t){0===t.State?e.bimvizStatus="正在编译":-1===t.State?e.bimvizStatus="无模型":2===t.State?e.bimvizStatus="编译失败":1===t.State&&(e.bimvizStatus="编译成功")}));case 2:return t.a(2)}}),t)})))()},generateComponents:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,r;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(a=e.selectList,n=a.map((function(e){return~e.Doc_File.indexOf(".")?e.Doc_File:e.Doc_File+e.File_Type})),"factory"!==e.mode){t.n=2;break}return t.n=1,(0,v.CommonImportDeependToComp)({fileList:a.map((function(e){return e.File_Url})).join("|||"),nameList:n.join("|||"),idList:e.ids.join("|||"),code:e.data.Code,factoryId:localStorage.getItem("CurReferenceId"),proProjectId:e.projectList.find((function(t){return t.Sys_Project_Id===e.projectId})).Id});case 1:r=t.v,t.n=4;break;case 2:return t.n=3,(0,v.ImportDeependToMaterial)({fileList:a.map((function(e){return e.File_Url})).join("|||"),nameList:n.join("|||"),idList:e.ids.join("|||"),code:e.data.Code});case 3:r=t.v;case 4:r.IsSucceed?(e.getData(),e.$message.success(r.Message)):e.$message.error(r.Message);case 5:return t.a(2)}}),t)})))()},scan:function(){this.$refs.scan.handleOpen()},shareFiles:function(){var e=this.BIMFiles?this.selectNames:this.ids;e.length?!this.BIMFiles&&e.length>1?this.$message.warning("只能选择一个文件"):this.buildShareUrl():this.$message.warning("请选择文件")},buildShareUrl:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,r,o,l,s,c,d,u,f,p;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(window.jsonpCallback=function(t){e.getToken(t.token)},a=e.BIMFiles?e.selectNames:e.ids,n="http://cloud.bimtk.com/",r="",!e.BIMFiles){t.n=1;break}r=".bzip2",t.n=8;break;case 1:if(r=e.selectList[0].File_Type,o=e.selectList[0].Pdf_Url,l=e.selectList[0].File_Url,".xls"!==r&&".xlsx"!==r&&".doc"!==r&&".docx"!==r){t.n=4;break}if(r=".pdf",o){t.n=3;break}return s=e.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),t.n=2,(0,h.fileToPdf2)(l);case 2:o=t.v,s.close();case 3:a=[o],t.n=8;break;case 4:if(".pdf"!==r){t.n=5;break}r=".pdf",a=[l],t.n=8;break;case 5:if(".dwg"!==r){t.n=6;break}a=[l],t.n=8;break;case 6:if(".png"!==r&&".jpg"!==r&&".jpeg"!==r&&".gif"!==r){t.n=7;break}r=".image",a=[l],t.n=8;break;case 7:return e.$message("该格式的文件暂不支持分享"),t.a(2);case 8:if(c=localStorage.getItem("CurReferenceId"),d="".concat(n,"h5/plm-projectFiles-share-index.html?tenantID=").concat((0,_.getTenantId)(),"&projectId=").concat(c,"&fileType=").concat(r,"&fileIds=").concat(escape(encodeURI(a)),"&time=").concat(e.shareTime||0),!e.BIMFiles){t.n=10;break}return t.n=9,e.getBimVizId();case 9:u=t.v,d="".concat(d,"&bimvizId=").concat(u);case 10:f={strUrl:d},e.shareTime&&(p=new Date(e.shareTime).toString(),f.date=(0,D.timeFormat)(p,"{y}-{m}-{d} {h}:{i}:{s}")),(0,v.GetShortUrl)(f).then((function(t){e.shareUrl="".concat(n,"h5/a-b.html?t=").concat((0,_.getTenantId)(),"&u=").concat(t.Data,"&h=").concat((0,x.baseUrl)()),e.shareVisible=!0}));case 11:return t.a(2)}}),t)})))()},clipboardSuccess:function(){this.$message.success("复制成功")},changeLoad:function(e,t){var a=this;return(0,r.default)((0,i.default)().m((function n(){var r,o;return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return r=a.ids.length?a.ids:[t],n.n=1,(0,v.ChangeLoad)({ids:r,load:e});case 1:return a.getData(),n.n=2,(0,v.GetLoadingFiles)();case 2:o=n.v,a.updateLoadingFiles(o.Data);case 3:return n.a(2)}}),n)})))()},updateLoadingFiles:function(e){var t=this;return(0,r.default)((0,i.default)().m((function a(){var n,r,o;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getBimVizId();case 1:n=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,key:BIM_API_CONFIG.KEY}),r=n.getModelProjectManager(),o={SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,LoadFiles:e},r.updateSceneSettings(BIM_API_CONFIG.USER_NAME,t.bimvizId,o,(function(e){e.IsSuccess?this.$message.success(e.Message):this.$message.error(e.Message)}));case 2:return a.a(2)}}),a)})))()},handlePageSizeChange:function(e){this.pageInfo.PageSize=e,this.getData()},search:function(){this.pageInfo.Page=1,this.getData()},getData:function(){var e=this;this.PLMStandard&&(this.fileModel.IsProject=!0),"factory"===this.mode&&(this.fileModel.Projectid=this.projectId),this.tableLoading=!0;var t={fileModel:this.fileModel,pageInfo:this.pageInfo};this.isGjXT||this.isLJXT||this.isBJXT?t.fileModel.InstallUnit_Name=this.areaName:t.fileModel.InstallUnit_Name=void 0,(0,v.GetFilesByType)(t).then((function(t){if(t.IsSucceed){var a=t.Data,n=a.Data,i=a.Page,r=a.PageSize,o=a.TotalCount;e.tableData=n.map((function(e){return e.checked=!1,e})),e.pageInfo={Page:i,PageSize:r,TotalCount:o}}else e.$message({message:t.Message,type:"error"});e.tableLoading=!1}))},handleImport:function(){},handleNodeClick:function(e){this.expandedKey=e.Id;var t=(0,M.findAllParentNode)(this.treeData,e.Id,!0);if(t.length&&(this.isGjXT=t[0].Label.includes("构件详图"),this.isLJXT=t[0].Label.includes("零件详图"),this.isBJXT=t[0].Label.includes("部件详图")),this.isCom=e.Label.includes("构件"),e.Data){var a,n;"factory"===this.mode||"Systerm"===e.Data.Create_UserName||"System"===e.Data.Create_UserName?(this.treeOperate=e.Data.Is_Disabled,"factory"===this.mode&&(this.fileOperate=e.Data.Is_Disabled)):this.treeOperate=!0;var i=null===(a=this.treeData.find((function(e){return"深化清单"===e.Label})))||void 0===a?void 0:a.Id;this.data.isSHQD=e.ParentNodes===i,this.isCZD="原材料材质单"===e.Label,this.fileModel.typeId=e.Id,this.data.Id=e.Id,this.data.name=e.Label,this.data.Catalog_Code=e.Code,this.data.Code=null===(n=e.Data)||void 0===n?void 0:n.English_Name,(this.isGjXT||this.isLJXT||this.isBJXT)&&this.getAreaList(),this.getData()}},handleOpenAddEdit:function(e,t){this.$refs.AddEditDep.handleOpen(e,t,this.treeData,"factory"===this.mode,"factory"===this.mode?this.projectId:null)},handleTabClick:function(e,t){},View:function(e){var t=this;return(0,r.default)((0,i.default)().m((function a(){var n,r,o,l,s;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,v.AttachmentGetEntities)({recordId:e.Id});case 1:if(n=a.v,t.attachments=n.Data,!(t.attachments.length>0)){a.n=6;break}if(r=t.attachments[0].File_Url,o=t.attachments[0].Pdf_Url,l=t.attachments[0].File_Type,".xls"!==l&&".xlsx"!==l&&".doc"!==l&&".docx"!==l){a.n=5;break}if(!o){a.n=2;break}window.open(o+"#toolbar=0","_blank"),a.n=4;break;case 2:return s=t.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),a.n=3,(0,h.fileToPdf)(r,!1);case 3:s.close();case 4:a.n=6;break;case 5:".dwg"===l?window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+(0,h.parseOssUrl)(r),"_blank"):(".pdf"===l&&(r+="#toolbar=0"),window.open(r,"_blank"));case 6:return a.a(2)}}),a)})))()},Edit:function(e){var t=this;return(0,r.default)((0,i.default)().m((function a(){var n;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(n="",!t.BIMFiles){a.n=2;break}return a.n=1,t.getBimVizId();case 1:n=a.v;case 2:t.$refs.dialog.handleOpen("edit",e,n,!1,t.projectId,!0);case 3:return a.a(2)}}),a)})))()},selectionChange:function(e){this.selectNames=e.map((function(e){return e.Doc_File})),this.ids=e.map((function(e){return e.Id})),this.selectList=e},checkAll:function(){this.ids=this.tableData.map((function(e){return e.Id}))},cancelCheckAll:function(){this.ids=[]},Delete:function(){var e=this;0!==this.ids.length?this.$confirm(" 确认删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((0,r.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if((0,v.FileDelete)({ids:e.ids}).then((function(t){!0===t.IsSucceed&&(e.$message({message:"删除成功",type:"success"}),e.getData(e.fileModel.typeId))})),!e.BIMFiles){t.n=2;break}return t.n=1,e.getBimVizId();case 1:a=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,restport:BIM_API_CONFIG.MODEL_PORT,key:BIM_API_CONFIG.KEY}),n=a.getModelProjectManager(),e.selectNames.forEach((function(t){n.removeProjectFile(BIM_API_CONFIG.USER_NAME,e.bimvizId,t,(function(e){}))}));case 2:return t.a(2)}}),t)})))).catch((function(){e.$message({type:"info",message:"已取消删除"})})):this.$message({message:"请先选择记录",type:"warning"})},chooseMove:function(){0!==this.ids.length?(this.moveToFolderId="",this.dialogVisible=!0):this.$message({message:"请先选择记录",type:"warning"})},chooseMoveFolder:function(e){this.moveToFolderId=e.Id},Move:function(){var e=this;this.moveToFolderId?(0,v.FileMove)({ids:this.ids,doc_Type:this.moveToFolderId}).then((function(t){t.IsSucceed&&(e.$message.success("移动成功"),e.dialogVisible=!1,e.getData())})):this.$message.warning("请选择要移动到的文件夹")},decodeStr:function(e){return decodeURIComponent(e)},Download:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.ids;t&&t.length?(this.notify=this.$notify.info({title:"提示",message:"文件下载中，请稍后...",duration:0}),(0,v.GetEntitiesByRecordId)({ids:t}).then((function(a){if(e.attachments=a.Data,e.attachments.length>0){var n=e.attachments,i=e.attachments[0].File_Url;if(1===t.length){var r=e.attachments.map((function(e){return e.File_Name})).join(",");(0,h.downloadfile)(i,r,e.attachments[0].File_Type,e.notify)}else{var o="".concat(localStorage.getItem("ProjectName"),"-").concat(e.data.name,"-").concat((new Date).toLocaleDateString());e.handleBatchDownload(n,o)}}}))):this.$message.warning("请选择要下载的文件")},handleBatchDownload:function(e,t){var a=this;return(0,r.default)((0,i.default)().m((function n(){var r,o,l,s;return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return r=e,o=new m.default,l={},s=[],n.n=1,r.forEach((function(e){var t=a.getFile(e.File_Url).then((function(t){var a=e.File_Name;o.file(a,t,{binary:!0}),l[a]=t}));s.push(t)}));case 1:Promise.all(s).then((function(){o.generateAsync({type:"blob"}).then((function(e){(0,p.saveAs)(e,t+".zip"),a.loading=!1,a.notify&&a.notify.close()})).catch((function(e){a.loading=!1,a.$message.error("网络出现了一点小问题，请稍后重试")}))}));case 2:return n.a(2)}}),n)})))()},previewImage:function(e,t){var a=this;this.$nextTick((function(){var e=a,n=document.getElementsByClassName("el-image-viewer__canvas")[0],i=document.createElement("div");function r(){i.innerHTML='\n          <div style="color: #ffffff;position: fixed;top: 20px;left: 20px;height: 50px;width: 310px;font-size: 23px;">'.concat(e.tableData[e.currImageIndex].Doc_Content,"</div>\n        "),n.appendChild(i)}a.currImageIndex=t,r(),document.getElementsByClassName("el-image-viewer__next")[0].addEventListener("click",(function(){a.currImageIndex++,r()})),document.getElementsByClassName("el-image-viewer__prev")[0].addEventListener("click",(function(){a.currImageIndex--,r()})),document.onkeydown=function(e){switch(e.keyCode){case 37:a.currImageIndex--,r();break;case 39:a.currImageIndex++,r();break;default:}}}))},getFile:function(e){return new Promise((function(t,a){(0,f.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))},viewHistory:function(){this.ids.length?this.$refs.history.handleOpen(this.ids[0]):this.$message.warning("请先选择记录")},handleNodeButtonDelete:function(e){var t=this,a="确认删除本类别？",n={confirmButtonText:"删除",type:"warning"};0===this.pageInfo.TotalCount&&0===e.Children.length?this.$confirm(a,"提示",n).then((function(){(0,v.FileTypeDelete)({id:e.Id}).then(function(){var a=(0,r.default)((0,i.default)().m((function a(n){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(!0!==n.IsSucceed){a.n=2;break}return t.$message({type:"success",message:"删除成功!"}),t.expandedKey===e.Id&&(t.expandedKey="",t.fileModel.typeId="",t.data={Id:"",Catalog_Code:"",name:""}),a.n=1,t.fetchTreeData();case 1:t.getData(),a.n=3;break;case 2:t.$message.error(n.Data);case 3:return a.a(2)}}),a)})));return function(e){return a.apply(this,arguments)}}())})).catch((function(){t.$message({type:"info",message:"已取消删除"})})):this.$message({message:"本类别存有文件或子节点，不能删除",type:"warning"})},fetchTreeData:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,a={catalogCode:"PLMDeepenFiles"},"factory"===e.mode?a.proSysProjectId=e.projectId:a.bimProjectId=localStorage.getItem("CurReferenceId"),t.n=1,(0,k.GetFileType)(a);case 1:n=t.v,e.treeData=n.Data,e.treeLoading=!1;case 2:return t.a(2)}}),t)})))()},setCurrentNode:function(e){},Add:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!e.data.Id){t.n=3;break}if(a="",!e.BIMFiles){t.n=2;break}return t.n=1,e.getBimVizId();case 1:a=t.v;case 2:e.$refs.dialog.handleOpen("add",e.data,a,!1,e.projectId,!1),t.n=4;break;case 3:return e.$message({message:"请先选择类别",type:"warning"}),t.a(2);case 4:return t.a(2)}}),t)})))()},buildBimVizSpace:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,o,l,s,c,d,u;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.$refs.dialog.loading=!0,t.n=1,e.getTenantInfo();case 1:a=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,restport:BIM_API_CONFIG.MODEL_PORT,key:BIM_API_CONFIG.KEY}),n=B.Name,o=B.Code,l=new BIMVIZ.ModelProject(o,n,0,""),s=BIMVIZ.SceneDomain.Architectural,BIM_API_CONFIG.SceneDomain&&"Rabar"==BIM_API_CONFIG.SceneDomain&&(s=BIMVIZ.SceneDomain.Rabar),c=new BIMVIZ.ProjectSettings({BackgroundStyle:BIMVIZ.SceneBackgroundStyle.White,SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,ThemeStyle:BIMVIZ.SceneThemeStyle.ModelSelt,GroundStyle:BIMVIZ.SceneGroundStyle.None,SceneDomain:s,MaxMemory:8e3,SceneLoadMode:2}),d=new BIMVIZ.ProjectInfo(l,c),u=a.getModelProjectManager(),u.addProject(BIM_API_CONFIG.USER_NAME,d,function(){var t=(0,r.default)((0,i.default)().m((function t(a){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!a.IsSuccess){t.n=3;break}if(e.bimvizId=a.ProjectId,B.Bim_Viz_Id=e.bimvizId,!e.PLMBIMFiles){t.n=1;break}return t.n=1,(0,g.GetPlmProjectEdit)({plm_Projects:B});case 1:if(!e.EPCBIMFiles){t.n=2;break}return t.n=2,(0,b.SaveEpcBimVizId)({bimVizId:e.bimvizId,projectId:B.Project_Id});case 2:return e.$refs.dialog.loading=!1,t.a(2,e.bimvizId);case 3:return e.$message.error("场景生成失败"),t.a(2,e.bimvizId);case 4:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}(),(function(){window.reload()}));case 2:return t.a(2)}}),t)})))()},createScene:function(){var e=this;this.$confirm("您确认要重新生成模型场景吗","提示",{type:"warning"}).then((0,r.default)((0,i.default)().m((function t(){var a,n,r;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getBimVizId();case 1:return t.n=2,e.getTenantInfo();case 2:return t.n=3,(0,f.default)({method:"get",url:"http://116.62.156.119:7004/api/user/token?devkey=".concat(BIM_API_CONFIG.KEY)});case 3:return a=t.v,n=a.data.token,t.n=4,(0,f.default)({method:"get",headers:{Authorization:"Bearer "+n},url:"http://116.62.156.119:7004/api/projectbuild/RebuildScene?username=".concat(BIM_API_CONFIG.USER_NAME,"&projid=").concat(e.bimvizId,"&cmd=RebuildAll")});case 4:r=t.v,r.data.IsSuccess?e.$message.success(r.data.Message):e.$message.error(r.data.Message);case 5:return t.a(2)}}),t)}))))},getBimVizId:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(e.bimvizId){t.n=9;break}if(!e.PLMBIMFiles){t.n=2;break}return t.n=1,(0,g.GetPlmProjectGetEntity)({id:localStorage.getItem("CurReferenceId")});case 1:a=t.v;case 2:if(!e.EPCBIMFiles){t.n=4;break}return t.n=3,(0,y.GetEpcEntity)({id:localStorage.getItem("CurReferenceId")});case 3:a=t.v;case 4:if(!a.IsSucceed||!a.Data){t.n=7;break}if(B=a.Data,!a.Data.Bim_Viz_Id){t.n=5;break}e.bimvizId=a.Data.Bim_Viz_Id,t.n=6;break;case 5:return t.n=6,e.buildBimVizSpace();case 6:t.n=8;break;case 7:e.$message.error("获取项目信息失败");case 8:return t.a(2,e.bimvizId);case 9:return t.a(2,e.bimvizId);case 10:return t.a(2)}}),t)})))()},getTenantInfo:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,b.GetTenantModelExtend)();case 1:if(a=t.v,a.Data){t.n=2;break}return e.$confirm("请先在系统偏好设置中，设置BIM模型的配置信息","提示"),t.a(2);case 2:n=a.Data.split(",").map((function(e){return e.split(":")})),BIM_API_CONFIG.KEY=n.filter((function(e){return"key"===e[0]}))[0][1],BIM_API_CONFIG.USER_NAME=n.filter((function(e){return"username"===e[0]}))[0][1];try{BIM_API_CONFIG.SceneDomain=n.filter((function(e){return"scenedomain"===e[0]}))[0][1]}catch(i){}return t.a(2,BIM_API_CONFIG)}}),t)})))()}}}},"6cd2":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("4e74"));t.default={name:"PLMDeepenFiles",components:{commonFiles:i.default}}},"772c":function(e,t,a){"use strict";a.r(t);var n=a("cf2d"),i=a("1cdf");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"76f2bbbf",null);t["default"]=l.exports},"7ba4":function(e,t,a){"use strict";a.r(t);var n=a("0f51"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"7c57":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block"}},[e.label?a("span",{staticClass:"text",style:"width:"+e.labelWidth},[e._v(e._s(e.label)+" ")]):e._e(),a("el-select",{staticClass:"select",style:"width:"+e.width,attrs:{loading:e.loading,filterable:e.filterable,clearable:e.clearable,disabled:e.disabled,readonly:e.readonly,multiple:e.multiple,placeholder:e.placeholder,"multiple-limit":e.multipleLimit,"popper-append-to-body":e.popperAppendToBody},on:{blur:e.blur,focus:e.focus,change:e.change},model:{value:e.tmpvalue,callback:function(t){e.tmpvalue=t},expression:"tmpvalue"}},["projectlist"===e.thistype?e._l(e.options,(function(t){return a("el-option",{key:t.Sys_Project_Id,attrs:{label:"projectlist"===e.type?t.Name:t.Short_Name,value:t.Sys_Project_Id}})})):e._e(),"managerlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.ProjectManagerId,attrs:{label:e.ProjectManagerName,value:e.ProjectManagerId}})})):e._e(),"majorlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"userlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"authuserlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"factorylist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"arealist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"contacts"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.User_Id,attrs:{label:e.Employor_Name,value:e.User_Id}})})):e._e(),"dictionary"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e()],2)],1)},i=[]},8608:function(e,t,a){},"892b":function(e,t,a){"use strict";a.r(t);var n=a("4529"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"8a0a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),r=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var o=a("a5f2");t.default={props:{type:{type:String,default:""},label:{type:String,default:""},labelWidth:{type:String,default:""},value:{type:String|Array,default:""},filterable:{type:Boolean,default:!0},clearable:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},multipleLimit:{type:Number,default:0},placeholder:{type:String,default:"请选择"},width:{type:String,default:"200px"},popperAppendToBody:{type:Boolean,default:!0},customparam:{type:Object,default:function(){}},defaultfirst:{type:Boolean,default:!1},projectId:{type:String,default:""}},data:function(){return{loading:!1,options:[],thistype:"",tmpvalue:this.value,tmpkey:"Id"}},watch:{tmpvalue:function(e){this.$emit("input",e)},value:function(e){this.tmpvalue=e}},mounted:function(){this.getdata()},methods:{change:function(){var e=this;if(this.multiple){var t=[];this.tmpvalue.map((function(a){t.push(e.options.find((function(t){return t[e.tmpkey]===a})))})),this.$emit("change",t)}else this.$emit("change",this.options.find((function(t){return t[e.tmpkey]===e.tmpvalue})))},blur:function(e){this.$emit("blur",e)},focus:function(e){this.$emit("focus",e)},getdata:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.loading=!0,e.thistype=e.type,a=e.type,t.n="projectlist"===a?1:"projectshortlist"===a?3:"managerlist"===a?5:"projectmajorlist"===a?7:"sysmajorlist"===a?9:"userlist"===a?11:"authuserlist"===a?13:"factorylist"===a?15:"arealist"===a?17:"contacts"===a?19:21;break;case 1:return t.n=2,e.getproject();case 2:return e.tmpkey="Sys_Project_Id",t.a(3,23);case 3:return e.thistype="projectlist",e.tmpkey="Sys_Project_Id",t.n=4,e.getproject();case 4:return t.a(3,23);case 5:return e.tmpkey="ProjectManagerId",t.n=6,e.getmanager();case 6:return t.a(3,23);case 7:return e.thistype="majorlist",e.tmpkey="Id",t.n=8,e.getmajor(!1);case 8:return t.a(3,23);case 9:return e.thistype="majorlist",e.tmpkey="Id",t.n=10,e.getmajor(!0);case 10:return t.a(3,23);case 11:return e.tmpkey="Id",t.n=12,e.getuser();case 12:return t.a(3,23);case 13:return e.tmpkey="Id",t.n=14,e.getAuthuser();case 14:return t.a(3,23);case 15:return e.tmpkey="Id",t.n=16,e.getfactory();case 16:return t.a(3,23);case 17:return e.tmpkey="Id",t.n=18,e.getarea();case 18:return t.a(3,23);case 19:return e.tmpkey="Id",t.n=20,e.getContacts();case 20:return t.a(3,23);case 21:return e.thistype="dictionary",e.tmpkey="Id",t.n=22,e.getdictionary(e.type);case 22:return t.a(3,23);case 23:e.defaultfirst&&!e.tmpvalue&&(e.tmpvalue=e.options[0][e.tmpkey]),e.loading=!1;case 24:return t.a(2)}}),t)})))()},getContacts:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,o.GetProjectContacts)({model:{ProjectId:e.projectId,IsSysUser:!0,Platform:~~localStorage.getItem("CurPlatform")}});case 1:a=t.v,a.IsSucceed&&(e.options=a.Data);case 2:return t.a(2)}}),t)})))()}}}},"9908c":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("a7c7"));t.default={components:{bimdialog:i.default},props:{typeid:{type:String,default:""}},data:function(){return{dialogVisible:!1}},mounted:function(){},methods:{handleSubmit:function(){this.$emit("resetData"),this.dialogVisible=!1},handleClose:function(){this.dialogVisible=!1},handleOpen:function(){var e=this;this.dialogVisible=!0,this.$nextTick((function(){var t=document.getElementById("iframeId");t.contentWindow.postMessage({data:e.typeid},"*")}))}}}},a1c1:function(e,t,a){},a4a3:function(e,t,a){},a5f2:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetProjectContacts=r;var i=n(a("b775"));function r(e){return(0,i.default)({url:"/SYS/Sys_Project_Contacts/GetProjectList",method:"post",data:e})}},a79c:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{attrs:{"dialog-title":"文件扫描","dialog-width":"1000px",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit()},cancelbtn:e.handleClose,handleClose:e.handleClose}},[a("div",{staticStyle:{width:"100%",height:"850px",margin:"0 12px 16px 16px",background:"#fff","padding-right":"16px"}},[a("iframe",{staticClass:"pc iframe",staticStyle:{width:"100%",height:"850px"},attrs:{id:"iframeId",src:"/static/axbauche/axbauche.html",frameborder:"0",scrolling:"no"}})])])},i=[]},aa20:function(e,t,a){"use strict";a("8608")},ad23:function(e,t,a){"use strict";a("a4a3")},b28f:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Glendale=void 0;var n=a("21c4");t.Glendale={baseUrl:"https://glendale-api.bimtk.com",uploadUrl:"/api/app/model/upload-file",externalUploadUrl:"/api/app/model/transcode-file",token:"cbe8c300dfb0280d71bf6546087c6d2fa49260085f53fca86420a895151316b47ecc664d935a21368f69b5af976ee234c74ec744e94e92ff68302806829f371f258c47f45b34485a2edb350010e2f42cd5b388af45c6b0706420a895151316b43747e3055c8e840016a6f6286831e20c2c8552e87ca11ef2ff11ee90514f34c99862338911c1f8c3484b6adefea47c1a13904f8abbaa3690edd3506dd71556198a94867b381ae88ecd6668e8ca6b6d286dce7e5d799b0a175d5afe5554a43322fe0821394937a5ec4e7b1a14ad02799235303fcd390fe68b88e6e56cb30380a7e82ecf81d1cced49bf42e57a167f6571a4f422a627b1b73db6c19a148ad18fb8598216e137795cc77fcba7ce8189fbc2d0c629f88d9f278b9f7189b6679a6b403cc9af099bcccfada205cfe438acf9ce55f7b7caa9ac5f713013404ec41c18c9cc748e652ce16fe7061c32d550edfda330c1555e0c08e51f698f96b91615d8220acd9cb22f32e975867209ea42bb96fb24680a43f9a8e0d8ff0545d55d0ee8954fdc9df330134f94052efc320a8c1a143e939c0e3b1ad476cf3630a2823a9a2d90e800d9b0be613ba7550ed054853e32510f8a6ea5c1dff9c3d61f7cf5f649cb25f0dbad9f7f66e32df742996903a326a7550ed054853e32510f8a6ea5c1dff98b6284e2288790dacf3630a2823a9a2d22bd09a00b213aae3866768ae2f24dde59f3f21ab1f820a9f37cc8bbee6776a194ceb5e6731e17b2badc7a1defb9170dcd8bfe92fe65c7f7433660bbb3cf8a0c1054f377da280222753151533a277044a238855226f63007d9dfdb117379ef800f473f1cc81a2741888bce192514399191b48af868eda4878aa9a449dea5d6a83212c061951e07b453e3e8a5ebf625bfafafd0506174e868046aea1d307c2532d9dfdb117379ef807362257cdfe5a976546538785d7cc0b4cd8bfe92fe65c7f7aed0269fce94ac324ca2ef020dc56bee92846bcbf7f17fbb041466e1708a43962f0f529858b2645ef09b5a6429c7bb718647f5c40fce1c35cc43571d292f54baa4596f82cf86665b367e01045009c18a2b543696b93a5bd44ca8168b525235150ffadf3f628aa7e8a20b9e74d76bd3a1e26b026e5297005a780f011db11ddb55fbabdc0f876c1cdfa5ef7db74bbc52e19c6396ffcf10044da26ecd334722494fc515b205e9624a4468762f63f0143583d1eeaaa85ff95ee50d8f953f44140ab40f29957f914b224124c1472866c2b48ef1d75032e402346dd66bd69478590684d8534c4d15a3c1bd672c07d671e1eaee4318e4f4b8a3801a663d5782e42d99c72c45cc2fe30743fe3c6aa30110f046086ef85cfad28d045a8156b326e2f4461369f266ad62ac2edf64d1767ac6ea3d83c9ba33a4517541406791c384c597539cc1ffcdbe8f3563a16e8a817067c3f375a338711971acc3b1d020470e76e4265682b70b4bded8768f01225025794367ef5cff14a517c70d3f412eb1403a714dcc9a4218257c98e433bcc9baedce6c26ebfb778919929fc8e65e3f8253971ef20b10b7b200fbc7c3d3f71e19f74e660313cf3630a2823a9a2dc2a17ec077fd21bf5125cdd7384aad1b3050717025af01dcfac91bea061f4ff4d8636a404c93c92c9ad3fe161773254f285f618f84d5ac97e6cd674b6676107fc8b14b44ceeed441af4b194f05a956194bd7fd5f13f06806",options:{InitiatingUser:"admin",UniqueCode:"20228_29",Priority:"202",ModelUploadUrl:"".concat((0,n.getTenantId)()),OtherInfo:"",ConfigJson:{style:1,zGrid:1,viewStyle:0,drawing:0,accuracy:3,parametric:1,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:1,unitRatio:.001,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:1,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:1}},optionsCad:{Name:"图纸12月30",InitiatingUser:"admin",Priority:"202",UniqueCode:"test001",IsCAD:!0,ModelUploadUrl:"".concat((0,n.getTenantId)()),ConfigJson:{style:1,zGrid:0,viewStyle:0,drawing:0,accuracy:5,parametric:0,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:0,unitRatio:1,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:0,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:0,faceNumLimit:1e6}}}},b2e8:function(e,t,a){"use strict";a.r(t);var n=a("1389"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},b4e5:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("bimdialog",{attrs:{"dialog-title":e.title,visible:e.dialogVisible,"dialog-width":"32%"},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:e.handleSubmit,cancelbtn:e.handleCancel,handleClose:e.handleCancel}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"类别名称",prop:"Document_Name"}},[a("el-input",{model:{value:e.form.Document_Name,callback:function(t){e.$set(e.form,"Document_Name",t)},expression:"form.Document_Name"}})],1),a("el-form-item",{attrs:{label:"类别编码",prop:"English_Name"}},[a("el-input",{model:{value:e.form.English_Name,callback:function(t){e.$set(e.form,"English_Name",t)},expression:"form.English_Name"}})],1),a("el-form-item",{attrs:{label:"上级目录",prop:"Parent_Id"}},[a("el-tree-select",{ref:"treeSelect",attrs:{"tree-params":e.treeParams},model:{value:e.form.Parent_Id,callback:function(t){e.$set(e.form,"Parent_Id",t)},expression:"form.Parent_Id"}})],1),a("el-form-item",{attrs:{label:"排序号",prop:"Sort"}},[a("el-input",{model:{value:e.form.Sort,callback:function(t){e.$set(e.form,"Sort",e._n(t))},expression:"form.Sort"}})],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleCancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit()}}},[e._v("确 定")])],1)],1)},i=[]},b86b0:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-row",{staticStyle:{height:"100%",overflow:"auto","box-sizing":"border-box"},attrs:{gutter:12,type:"flex"}},[a("el-card",{staticClass:"cs-fill-card h100",staticStyle:{width:"304px"},attrs:{id:"sliderLeft",shadow:"none"}},["factory"===e.mode?a("el-row",{attrs:{type:"flex"}},[a("div",{staticStyle:{width:"100px",height:"32px","line-height":"32px"}},[e._v("当前项目：")]),a("el-select",{staticStyle:{"margin-bottom":"10px"},attrs:{placeholder:"请选择",filterable:""},on:{change:e.projectChange},model:{value:e.projectId,callback:function(t){e.projectId=t},expression:"projectId"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1):e._e(),a("el-row",{staticClass:"dep-top-bar",attrs:{justify:"space-between",type:"flex"}},[a("el-col",[a("span",{staticClass:"dep-tree-title"},[e._v("文件类别")])]),a("el-col",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{icon:"el-icon-plus"},on:{click:function(t){return e.handleOpenAddEdit("add",e.data)}}},[e._v("新增")])],1)],1),a("tree-detail",{ref:"tree",attrs:{"expand-on-click-node":!1,"button-type-array":e.treeOperate?["delete","edit"]:[],loading:e.treeLoading,"tree-data":e.treeData,"same-icon":"","show-detail":"","expanded-key":e.expandedKey,icon:"icon-folder"},on:{handleNodeButtonDelete:e.handleNodeButtonDelete,handleNodeButtonEdit:function(t){return e.handleOpenAddEdit("edit",t)},setCurrentNode:e.setCurrentNode,handleNodeClick:e.handleNodeClick}})],1),a("div",{directives:[{name:"resize-width",rawName:"v-resize-width"}],staticClass:"drag-bar"}),e.isGCLXD?[a("iframe",{staticClass:"cs-fill-card h100",staticStyle:{flex:"1","margin-left":"6px"},attrs:{frameborder:"0",src:"http://work.jgsteel.cn:8010/WorkFlow/qsm/BimInstanceList?adtag=app&typeid=1&projectname="+e.projectName}})]:e.isHTPSJL?[a("iframe",{staticClass:"cs-fill-card h100",staticStyle:{flex:"1","margin-left":"6px"},attrs:{frameborder:"0",src:"http://work.jgsteel.cn:8010/WorkFlow/qsm/BimInstanceList?adtag=app&typeid=2&projectname="+e.projectName}})]:[a("el-card",{staticClass:"cs-fill-card  cs-fill-card2 h100",staticStyle:{flex:"1","margin-left":"6px"}},[a("div",{staticStyle:{"padding-bottom":"20px",display:"flex","flex-wrap":"wrap"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.Add()}}},[e._v("新增")]),a("el-button",{attrs:{type:"danger"},on:{click:e.Delete}},[e._v("删除")]),"net"!==e.viewBy||e.selecting?e._e():a("el-button",{on:{click:function(t){e.selecting=!0}}},[e._v("选择")]),"net"===e.viewBy&&e.selecting?a("el-button",{on:{click:function(t){e.selecting=!1,e.ids=[]}}},[e._v("取消选择")]):e._e(),"net"===e.viewBy&&e.selecting&&!e.isCheckedAll?a("el-button",{on:{click:e.checkAll}},[e._v("全选")]):e._e(),"net"===e.viewBy&&e.selecting&&e.isCheckedAll?a("el-button",{on:{click:e.cancelCheckAll}},[e._v("取消全选")]):e._e(),"list"===e.viewBy||e.selecting?[a("el-button",{on:{click:e.chooseMove}},[e._v("移动")]),a("el-button",{on:{click:function(t){return e.Download()}}},[e._v("下载")]),a("el-button",{on:{click:e.viewHistory}},[e._v("历史记录")]),"PLMEngineeringFiles"===e.type?a("el-button",{on:{click:e.scan}},[e._v("文件扫描")]):e._e()]:e._e(),e.BIMFiles?a("el-button",{attrs:{type:"success"},on:{click:e.createScene}},[e._v("生成场景")]):e._e(),e.BIMFiles&&e.bimvizStatus?a("el-button",{attrs:{readonly:""}},[e._v(e._s(e.bimvizStatus))]):e._e(),a("el-button",{attrs:{type:"success"},on:{click:e.shareFiles}},[e._v("分享")]),a("div",{staticStyle:{"margin-left":"auto",display:"flex","flex-wrap":"wrap"}},[e.isGjXT||e.isLJXT||e.isBJXT?a("div",{staticClass:"cs-area"},[a("span",[e._v("批次：")]),a("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.areaName,callback:function(t){e.areaName=t},expression:"areaName"}},e._l(e.areaList,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),a("el-date-picker",{staticStyle:{"margin-right":"10px"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}}),a("el-input",{staticStyle:{width:"200px","margin-right":"20px"},attrs:{placeholder:"请输入内容"},model:{value:e.fileModel.Title,callback:function(t){e.$set(e.fileModel,"Title",t)},expression:"fileModel.Title"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},slot:"append"})],1),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v(" 搜索 ")]),a("el-button",{on:{click:e.resetSearch}},[e._v(" 重置 ")])],1)],1)],2),a("el-row",{staticClass:"plm-bimtable"},["list"===e.viewBy?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"table",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"100%",stripe:""},on:{"selection-change":e.selectionChange},scopedSlots:e._u([{key:"empty",fn:function(){return[a("ElTableEmpty")]},proxy:!0}],null,!1,1319441631)},[a("el-table-column",{attrs:{type:"selection",width:"44"}}),a("el-table-column",{attrs:{prop:"Doc_Title",label:"标题","show-overflow-tooltip":"","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(a){return e.View(t.row)}}},[e._v(e._s(e.decodeStr(t.row.Doc_Title||t.row.File_Name)))])]}}],null,!1,2654813818)}),a("el-table-column",{attrs:{prop:"Doc_Content",label:"简要描述","show-overflow-tooltip":"","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.PLMDeepenFiles&&4==t.row.IsCaitu?a("span",{staticStyle:{color:"#f00"}},[e._v(e._s(e.decodeStr(t.row.Doc_Content)))]):a("span",[e._v(e._s(e.decodeStr(t.row.Doc_Content)))])]}}],null,!1,387084664)}),a("el-table-column",{attrs:{prop:"Doc_File",label:"附件信息","show-overflow-tooltip":"","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.decodeStr(t.row.Doc_File)))])]}}],null,!1,611683983)}),e.BIMFiles?[a("el-table-column",{attrs:{align:"center",label:"默认加载","min-width":"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{size:"mini "},on:{change:function(a){return e.changeLoad(a,t.row.Id)}},model:{value:t.row.Is_Load,callback:function(a){e.$set(t.row,"Is_Load",a)},expression:"scope.row.Is_Load"}})]}}],null,!1,1152783437)})]:[a("el-table-column",{attrs:{align:"center",label:"变更文件",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{disabled:"",size:"mini "},model:{value:t.row.IsChanged,callback:function(a){e.$set(t.row,"IsChanged",a)},expression:"scope.row.IsChanged"}})]}}],null,!1,219204479)})],e.data.isSHQD?a("el-table-column",{attrs:{align:"center",label:"自动生成构件清单",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:t.row.IsCaitu?"success":"danger"}},[e._v(" "+e._s(t.row.IsCaitu?"已生成":"未生成")+" ")])]}}],null,!1,3021671944)}):e._e(),e.isGjXT||e.isLJXT||e.isBJXT?a("el-table-column",{attrs:{align:"center",label:e.isGjXT?"是否对应构件":e.isLJXT?"是否对应零件":"是否对应部件",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isCom?a("el-tag",{attrs:{type:t.row.IsComponentDraft?"success":"danger"}},[e._v(" "+e._s(t.row.IsComponentDraft?"有":"无")+" ")]):a("el-tag",{attrs:{type:t.row.IsPartDraft?"success":"danger"}},[e._v(" "+e._s(t.row.IsPartDraft?"有":"无")+" ")])]}}],null,!1,157514183)}):e._e(),e.isGjXT||e.isLJXT||e.isBJXT?a("el-table-column",{attrs:{prop:"Area_Name",label:"区域","min-width":"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.Area_Name||"-")+" ")]}}],null,!1,2104561471)}):e._e(),e.isGjXT||e.isLJXT||e.isBJXT?a("el-table-column",{attrs:{prop:"InstallUnit_Name",label:"批次","show-overflow-tooltip":"","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.InstallUnit_Name||"-")+" ")]}}],null,!1,2595344591)}):e._e(),a("el-table-column",{attrs:{align:"center",prop:"Create_UserName",label:"编辑人",width:"110"}}),a("el-table-column",{attrs:{align:"center",prop:"Create_Date",label:"编辑时间",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e._f("timeFormat")(t.row.Create_Date,"{y}-{m}-{d} {h}:{i}"))+" ")]}}],null,!1,4079019612)}),a("el-table-column",{attrs:{label:"操作",width:"96",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isEdit?[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editRow(t.row)}}},[e._v("完成")]),a("el-button",{attrs:{type:"text"},on:{click:function(e){t.row.isEdit=!1}}},[e._v("取消")])]:[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.Edit(t.row)}}},[e._v("编辑")]),e.BIMFiles?e._e():a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.View(t.row)}}},[e._v("查看")])]]}}],null,!1,4021593432)})],2):a("div",{staticClass:"imageList"},[a("el-row",[a("el-checkbox-group",{model:{value:e.ids,callback:function(t){e.ids=t},expression:"ids"}},e._l(e.tableData,(function(t,n){return a("el-col",{key:t.Id,attrs:{span:6}},[a("div",{staticClass:"item"},[e.selecting?a("el-checkbox",{staticStyle:{"box-sizing":"border-box",width:"100%"},attrs:{label:t.Id}},[e.videoType.includes(t.File_Type)?a("div",{staticClass:"picture"},[a("video",{attrs:{src:t.File_Url,controls:"controls",height:"130px",width:"100%"}})]):a("el-image",{staticClass:"picture",attrs:{src:t.File_Url}},[a("div",{staticClass:"error-slot",attrs:{slot:"error"},slot:"error"},[a("svg-icon",{attrs:{"icon-class":"default-picture","class-name":"picture"}})],1)])],1):[e.videoType.includes(t.File_Type)?a("div",{staticClass:"picture"},[a("video",{attrs:{src:t.File_Url,controls:"controls",height:"130px",width:"100%"}})]):a("el-image",{staticClass:"picture",attrs:{src:t.File_Url,"preview-src-list":e.srcList},on:{click:function(a){return e.previewImage(t,n)}}},[a("div",{staticClass:"error-slot",attrs:{slot:"error"},slot:"error"},[a("svg-icon",{attrs:{"icon-class":"default-picture","class-name":"picture"}})],1)])],a("footer",{staticClass:"footer"},[a("div",{staticClass:"title-date"},[a("div",{staticClass:"title"},[e._v(e._s(t.Doc_Title||t.File_Name))]),a("div",{staticClass:"date"},[e._v(e._s(e._f("timeFormat")(t.Create_Date,"{y}-{m}-{d} {h}:{i}")))])]),a("div",{staticClass:"content"},[e._v(" "+e._s(t.Doc_Title||t.File_Name)+" ")])])],2)])})),1)],1)],1),a("div",{staticClass:"page"},[a("el-pagination",{attrs:{"current-page":e.pageInfo.Page,"page-sizes":e.tablePageSize,"page-size":e.pageInfo.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pageInfo.TotalCount},on:{"update:currentPage":function(t){return e.$set(e.pageInfo,"Page",t)},"update:current-page":function(t){return e.$set(e.pageInfo,"Page",t)},"size-change":e.handlePageSizeChange,"current-change":e.getData}})],1)],1)],1)]],2),a("bimdialog",{ref:"dialog",attrs:{"bimviz-id":e.bimvizId,"project-id":e.projectId,"is-czd":e.isCZD,"is-l-j-x-t":e.isLJXT,"is-b-j-x-t":e.isBJXT,"is-gj-x-t":e.isGjXT},on:{getData:e.getData}}),a("add-edit-dep",{ref:"AddEditDep",attrs:{"tree-data":e.treeData},on:{changeData:e.fetchTreeData}}),a("el-dialog",{attrs:{"dialog-title":"移动到","dialog-width":"500px",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:e.Move,cancelbtn:function(t){e.dialogVisible=!1},handleClose:function(t){e.dialogVisible=!1}}},[a("tree-detail",{ref:"tree",attrs:{"expand-on-click-node":!1,loading:e.treeLoading,"tree-data":e.treeData,"same-icon":"","expanded-key":e.expandedKey,icon:"icon-folder"},on:{handleNodeClick:e.chooseMoveFolder}})],1),a("el-dialog",{attrs:{"dialog-title":"文件分享","dialog-width":"600px",visible:e.shareVisible,hidebtn:""},on:{"update:visible":function(t){e.shareVisible=t},handleClose:function(t){e.shareVisible=!1,e.shareTime=""}}},[a("el-form",[a("el-form-item",{attrs:{label:"分享链接"}},[e._v(" "+e._s(e.shareUrl)+" "),a("el-button",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:e.shareUrl,expression:"shareUrl",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:e.clipboardSuccess,expression:"clipboardSuccess",arg:"success"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary"}},[e._v("复制")])],1),a("el-form-item",{attrs:{label:"失效时间"}},[a("el-date-picker",{attrs:{type:"datetime",clearable:"","value-format":"timestamp",placeholder:"请选择失效时间"},on:{change:e.buildShareUrl},model:{value:e.shareTime,callback:function(t){e.shareTime=t},expression:"shareTime"}}),a("i",{staticStyle:{"margin-left":"10px",color:"rgba(34, 40, 52, 0.24)"}},[e._v("不设置代表永久有效")])],1),a("el-form-item",{attrs:{label:"二维码"}},[a("qrcode-vue",{attrs:{size:124,value:e.shareUrl,"class-name":"qrcode",level:"H"}})],1)],1)],1),a("history",{ref:"history",on:{preview:e.View,download:e.Download}}),a("scan",{ref:"scan",attrs:{typeid:e.fileModel.typeId},on:{resetData:e.getData}})],1)},i=[]},b878:function(e,t,a){"use strict";a.r(t);var n=a("b4e5"),i=a("1771");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("ad23");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"7bd9e54d",null);t["default"]=l.exports},bc11:function(e,t,a){"use strict";a("ccbf")},bf8b:function(e,t,a){"use strict";a.r(t);var n=a("7c57"),i=a("cf8f");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("50d9");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,null,null);t["default"]=l.exports},c7ab:function(e,t,a){"use strict";a.r(t);var n=a("f68a");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var r,o,l=a("2877"),s=Object(l["a"])(n["default"],r,o,!1,null,null,null);t["default"]=s.exports},ccbf:function(e,t,a){},cf2d:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("common-files")],1)},i=[]},cf8f:function(e,t,a){"use strict";a.r(t);var n=a("8a0a"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},d363:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetCurPlmProjectGetEntity=r,t.GetTenantModelExtend=o,t.SaveEpcBimVizId=l;var i=n(a("b775"));function r(e){return(0,i.default)({url:"PLM/XModel/GetProjectInfo",method:"post",data:e})}function o(){return(0,i.default)({url:"SYS/PreferenceSetting/GetPreferenceSettingValue",method:"post",data:{code:"BimModel"}})}function l(e){return(0,i.default)({url:"/EPC/Project/SaveEpcBimVizId",method:"post",data:e})}},d563:function(e,t,a){"use strict";a.r(t);var n=a("a79c"),i=a("5529");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"5a0c1fd9",null);t["default"]=l.exports},dc02:function(e,t,a){"use strict";function n(e,t,a){void 0===a&&(a="[]"),e=e.replace(/=/g,"");var i=[];if(null==t)return i;switch(t.constructor){case String:case Number:case Boolean:i.push(encodeURIComponent(e)+"="+encodeURIComponent(t));break;case Array:t.forEach((function(t){i=i.concat(n(""+e+a+"=",t,a))}));break;case Object:Object.keys(t).forEach((function(r){var o=t[r];i=i.concat(n(e+"["+r+"]",o,a))}))}return i}function i(e){var t=[];return e.forEach((function(e){"string"==typeof e?t.push(e):t=t.concat(i(e))})),t}
/**
 * Vue Jsonp.
 * # Carry Your World #
 *
 * @author: LancerComet
 * @license: MIT
 */a.r(t),a.d(t,"VueJsonp",(function(){return r})),a.d(t,"jsonp",(function(){return o}));var r={install:function(e){e.prototype.$jsonp=o}};function o(e,t,a){var r;if(void 0===t&&(t={}),"string"!=typeof e)throw new Error('[Vue-jsonp] Type of param "url" is not string.');if("object"!=typeof t||!t)throw new Error("[Vue-jsonp] Invalid params, should be an object.");var o="number"==typeof a?a:null!==(r=null==a?void 0:a.timeout)&&void 0!==r?r:5e3,l="[]";if("object"==typeof a){var s=a.arrayIndicator;"string"==typeof s&&(l=s)}return new Promise((function(a,r){var s="string"==typeof t.callbackQuery?t.callbackQuery:"callback",c="string"==typeof t.callbackName?t.callbackName:"jsonp_"+(Math.floor(1e5*Math.random())*Date.now()).toString(16);t[s]=c,delete t.callbackQuery,delete t.callbackName;var d=[];Object.keys(t).forEach((function(e){d=d.concat(n(e,t[e],l))}));var u=i(d).join("&"),f=function(){p(),clearTimeout(h),r({status:400,statusText:"Bad Request"})},p=function(){b.removeEventListener("error",f)},m=function(){document.body.removeChild(b),delete window[c]},h=null;o>-1&&(h=setTimeout((function(){p(),m(),r({statusText:"Request Timeout",status:408})}),o)),window[c]=function(e){clearTimeout(h),p(),m(),a(e)};var b=document.createElement("script");b.addEventListener("error",f),b.src=e+(/\?/.test(e)?"&":"?")+u,document.body.appendChild(b)}))}},e447:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a("0d9a"),r=n(a("a7c7"));t.default={components:{bimdialog:r.default},props:{treeData:{type:Array,default:function(){return[]}}},data:function(){return{title:"文件类别",type:"",dialogVisible:!1,form:{Document_Name:"",English_Name:"",Parent_Id:"",Catalog_Code:"",Sort:"",Is_System:!1},treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},rules:{Document_Name:[{required:!0,message:"请输入类别名称",trigger:"blur"}],Sort:[{required:!0,message:"排序不能为空"},{type:"number",message:"排序必须为数字值"}]}}},created:function(){},methods:{handleOpen:function(e,t,a,n,r){var o=this;this.type=e,this.dialogVisible=!0,this.$nextTick((function(){o.$refs.treeSelect.treeDataUpdateFun(a)})),"add"===e?(this.title="添加",this.form={Document_Name:"",English_Name:"",Parent_Id:t.Id,Catalog_Code:"PLMDeepenFiles",Sort:"",Is_System:!1,Project_Id:r||localStorage.getItem("CurReferenceId"),Is_Disabled:n}):(this.title="编辑",(0,i.FileTypeGetEntity)({id:t.Id}).then((function(e){1==e.IsSucceed&&(o.form=e.Data)})))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;"add"===e.type?(0,i.FileAddType)({sys_File_Type:e.form}).then((function(t){!0===t.IsSucceed?(e.$message({type:"success",message:e.title+"成功"}),e.$emit("changeData"),e.form={Document_Name:"",English_Name:"",Parent_Id:"",Catalog_Code:"",Sort:"",Is_System:!1},e.dialogVisible=!1):e.$message.error(t.Data)})):(0,i.FileTypeEdit)({sys_File_Type:e.form}).then((function(t){!0===t.IsSucceed?(e.$message({type:"success",message:e.title+"成功"}),e.$emit("changeData"),e.form={Document_Name:"",English_Name:"",Parent_Id:"",Catalog_Code:"",Sort:"",Is_System:!1},e.dialogVisible=!1):e.$message.error(t.Data)}))}))},handleCancel:function(){this.resetForm("form")},resetForm:function(e){this.$refs[e].resetFields(),this.dialogVisible=!1}}}},ecba:function(e,t,a){},f382:function(e,t,a){"use strict";function n(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=n(e.Children)),!0)}))}function i(e){e.map((function(e){if(e.Is_Directory||!e.Children)return i(e.Children);delete e.Children}))}function r(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function n(e,t,i){for(var r=0;r<e.length;r++){var o=e[r];if(o.Id===t)return a&&i.push(o),i;if(o.Children&&o.Children.length){if(i.push(o),n(o.Children,t,i).length)return i;i.pop()}}return[]}return n(e,t,[])}function o(e){return e.Children&&e.Children.length>0?o(e.Children[0]):e}function l(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&l(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=i,t.disableDirectory=l,t.findAllParentNode=r,t.findFirstNode=o,t.getDirectoryTree=n,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7")},f68a:function(e,t,a){"use strict";a.r(t);var n=a("0ce7"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},fd51:function(e,t,a){}}]);