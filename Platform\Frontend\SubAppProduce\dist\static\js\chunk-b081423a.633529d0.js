(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b081423a"],{"12cc":function(t,e,i){},"22d6":function(t,e,i){"use strict";i.r(e);var a=i("a7e3"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"4f39":function(t,e,i){"use strict";var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=r,e.timeFormat=o,i("d3b7"),i("4d63"),i("c607"),i("ac1f"),i("2c3e"),i("00b4"),i("25f0"),i("4d90"),i("5319");var n=a(i("53ca"));function r(t,e){if(0===arguments.length||!t)return null;var i,a=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,n.default)(t)?i=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),i=new Date(t));var r={y:i.getFullYear(),m:i.getMonth()+1,d:i.getDate(),h:i.getHours(),i:i.getMinutes(),s:i.getSeconds(),a:i.getDay()},o=a.replace(/{([ymdhisa])+}/g,(function(t,e){var i=r[e];return"a"===e?["日","一","二","三","四","五","六"][i]:i.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var i=t.split("~"),a=r(new Date(i[0]),e)+" ~ "+r(new Date(i[1]),e);return a}return t&&t.length>0?r(new Date(t),e):void 0}},"7d8d":function(t,e,i){"use strict";i.d(e,"a",(function(){return a})),i.d(e,"b",(function(){return n}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[i("div",{staticClass:"cs-z-page-main-content"},[i("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[i("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),i("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[i("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),i("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[i("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),i("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),i("el-divider"),i("div",{staticClass:"tb-info"},[i("label",[t._v("数据列表")]),i("div",{staticClass:"btn-x"},[t.showExport?i("el-button",{attrs:{disabled:t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e()],1)]),i("div",{staticClass:"tb-x"},[i("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},n=[]},a667a:function(t,e,i){"use strict";i.r(e);var a=i("7d8d"),n=i("22d6");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("d217");var o=i("2877"),l=Object(o["a"])(n["default"],a["a"],a["b"],!1,null,"54384296",null);e["default"]=l.exports},a7e3:function(t,e,i){"use strict";var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d3b7");var n=a(i("c14f")),r=a(i("1da1")),o=a(i("ac03")),l=i("cf45"),c=i("4f39"),s=a(i("3502")),d=i("767d"),u=i("bc41"),f=i("05e0");e.default={name:"OMAMarkingCostDetailInfo",components:{VTable:s.default},mixins:[o.default],data:function(){return{form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],showExport:!1}},computed:{curTitle:function(){return"".concat((0,c.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")}},beforeCreate:function(){this.curModuleKey=u.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.showExport=t.getRoles("OMAMarkingDetailCbExport"),t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,t.fetchData(),e.n=1,(0,l.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate(this.form.StatisticalDate)&&(this.loading=!0,(0,d.GetYXCostDailyDetailList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[];var i=t.setTotalData(t.tableData,t.generateColumn()),a=i.column;t.columns=a,t.$refs["tb"].setColumns(a)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var t=180;return[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:5},field:"ProjectAbbreviation",minWidth:f.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:f.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:f.ProjectStatusW}]}]},{title:"物控调拨单价(元)",params:{rowSpan:2},fixed:"left",children:[{params:{none:"none"},children:[{params:{none:"none"},field:"MaterialControlTransferUnitPrice",minWidth:160}]}]},{title:"生产调拨单价(元)",params:{rowSpan:2},fixed:"left",children:[{params:{none:"none"},children:[{params:{none:"none"},field:"Produce_Allocate_Unit_Price",minWidth:160}]}]},{title:"产品成本",children:[{title:"生产部成品入库量(T)",children:[{minWidth:t,field:"Component_Stock_In_Amount",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:t,field:"Product_Cost_SubTotal",title:0,isTotal:!0}]}]},{title:"成品外购成本",children:[{title:"外协工厂成品入库量(T)",children:[{minWidth:t,field:"BGBL_Component_Stock_In_Amount",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:t,field:"BGBL_Product_Cost_SubTotal",title:0,isTotal:!0}]}]},{title:"加工劳务成本",children:[{title:"外协工厂成品入库量(T)",children:[{minWidth:t,field:"BG_Component_Stock_In_Amount",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:t,field:"BG_Product_Cost_SubTotal",title:0,isTotal:!0}]}]},{title:"营销主材成本",children:[{title:"营销部采购主材领用量(T)",children:[{minWidth:210,field:"RawMaterialAmount",title:0,isTotal:!0}]},{title:"营销部甲供主材领用量(T)",children:[{minWidth:210,field:"SelfSupplyingRawMaterialAmount",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:t,field:"RawMaterialSubtotal",title:0,isTotal:!0}]}]},{title:"安装劳务成本",children:[{title:"其他部门调入成本(元)",children:[{minWidth:t,field:"Installation_Labor_Cost",title:0,isTotal:!0}]}]}]}}}},cf45:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,i("d3b7");var a=i("6186");function n(t){return new Promise((function(e,i){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},d217:function(t,e,i){"use strict";i("12cc")}}]);