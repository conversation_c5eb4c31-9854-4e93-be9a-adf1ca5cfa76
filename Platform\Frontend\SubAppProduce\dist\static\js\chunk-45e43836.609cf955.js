(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-45e43836"],{"0e64":function(t,e,n){"use strict";n("1334")},1334:function(t,e,n){},"3d79":function(t,e,n){"use strict";n.r(e);var i=n("ed77"),a=n("c461");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var r=n("2877"),c=Object(r["a"])(a["default"],i["a"],i["b"],!1,null,null,null);e["default"]=c.exports},"673e":function(t,e,n){"use strict";var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("14d9"),n("fb6a"),n("d3b7"),n("25f0"),n("3ca3"),n("ddb0");var a=i(n("0f97")),o=n("6186"),r=i(n("b775")),c=n("209b"),s=n("2dd9"),u=i(n("3d79"));e.default={name:"CheckDetail",components:{DynamicDataTable:a.default,ExceptionRemark:u.default},data:function(){return{gridCode:"pro_stock_inventory_result_list",tbConfig:{},columns:[],data:[],query:{Page:1,TotalCount:0,PageSize:0},ParameterJson:[],fiterArrObj:{},dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},computed:{billId:function(){var t;return null===(t=this.$route.query)||void 0===t?void 0:t.id},bill:function(){var t;return null===(t=this.$route.params)||void 0===t?void 0:t.row},exceptions:function(){return c.InventoryCheckExceptions}},created:function(){var t=this;Promise.all([]).then((function(){return(0,o.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))}))})).then((function(){t.getTableData()}))},methods:{tagBack:function(){var t=this,e=this.$route;this.$store.dispatch("tagsView/delView",e).then((function(n){var i=n.visitedViews;if(e.path===t.$route.path){var a=i.slice(-1)[0];t.$router.push(a.fullPath)}}))},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120}),this.query.PageSize=t.Row_Number},setCols:function(t){this.columns=t.concat([])},setGridData:function(t){this.data=t.Data},getTableData:function(){var t=this;this.tbConfig.Data_Url&&(0,r.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.query,{Bill_Id:this.billId,ParameterJson:(0,s.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(e){e.IsSucceed&&(t.setGridData(e.Data),t.query.TotalCount=e.Data.TotalCount)}))},gridSizeChange:function(t){var e=t.size;this.query.Page=1,this.query.PageSize=e,this.getTableData()},gridPageChange:function(t){var e=t.page;this.query.Page=e,this.getTableData()},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},handleException:function(t){this.openDialog({title:"异常处理",width:"360px",component:"ExceptionRemark",props:{row:t}})},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.type,n=t.data;switch(this.dialogCancel(),e){case"reload":break;case"handle":this.exceptionHanlder(n.Remark,n.row);break}},exceptionHanlder:function(t,e){var n=this;(0,c.HandleInventoryItem)({id:e.Id,type:"Remark",value:encodeURIComponent(t)}).then((function(t){var i;t.IsSucceed?e.Status_Name="已处理":n.$message.warning(null!==(i=t.Message)&&void 0!==i?i:"")}))},exceptionTypeChange:function(t,e){var n=this;(0,c.HandleInventoryItem)({id:t.Id,type:"Exception_Type",value:encodeURIComponent(e)}).then((function(t){var e;t.IsSucceed||n.$message.warning(null!==(e=t.Message)&&void 0!==e?e:"")}))}}}},80097:function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return a}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[n("div",{staticClass:"sch-detail"},[n("header",[n("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),n("span",[t._v(t._s(t.bill.Name))]),n("div",{staticClass:"right-fix"},[n("el-button",[t._v("打印")]),n("el-button",{attrs:{type:"success"}},[t._v("导出")])],1)],1),n("div",{staticClass:"twrap"},[n("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""},on:{gridSizeChange:t.gridSizeChange,gridPageChange:t.gridPageChange},scopedSlots:t._u([{key:"Result",fn:function(e){var i=e.row,a=e.column;e.$index;return[n("div",{class:"rlt-status "+("盘亏"===i[a.Code]?"red":"盘盈"===i[a.Code]?"green":"")},[t._v(" "+t._s(i[a.Code])+" ")])]}},{key:"Exception_Type",fn:function(e){var i=e.row,a=e.column;e.$index;return[n("el-select",{staticStyle:{width:"96%"},attrs:{placeholder:"-"},on:{change:function(e){return t.exceptionTypeChange(i,e)}},model:{value:i[a.Code],callback:function(e){t.$set(i,a.Code,e)},expression:"row[column.Code]"}},[t._l(t.exceptions,(function(e,a){return[e.Status==i["Result"]?n("el-option",{key:a,attrs:{label:e.label,value:e.value}}):t._e()]}))],2)]}},{key:"Status_Name",fn:function(e){var i=e.row,a=e.column;e.$index;return["未处理"===i[a.Code]?n("el-link",{attrs:{type:"danger",underline:!1},on:{click:function(e){return t.handleException(i)}}},[t._v("立即处理")]):n("el-link",{attrs:{underline:!1,type:""}},[t._v(t._s(i[a.Code]))])]}}])})],1)]),n("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[n("keep-alive",[t.dialogShow?n(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},a=[]},b79d2:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={name:"ExceptionRemark",props:{row:{type:Object,default:function(){return{}}}},data:function(){return{Remark:""}},created:function(){this.Remark=this.row.Remark},methods:{cancel:function(){this.$emit("dialogCancel")},submit:function(){if(!this.Remark)return this.$message.warning("输入处理备注");this.$emit("dialogFormSubmitSuccess",{type:"handle",data:{Remark:this.Remark,row:this.row}})}}}},c164:function(t,e,n){"use strict";n("fcf3")},c461:function(t,e,n){"use strict";n.r(e);var i=n("b79d2"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},c841:function(t,e,n){"use strict";n.r(e);var i=n("673e"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},ed77:function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return a}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"margin-top":"-16px"}},[n("p",{staticStyle:{color:"red"}},[t._v("异常状态处理备注填写：")]),n("div",[n("el-input",{attrs:{type:"textarea",maxlength:"200",rows:"4","show-word-limit":""},model:{value:t.Remark,callback:function(e){t.Remark=e},expression:"Remark"}})],1),n("div",{staticStyle:{"text-align":"right","margin-top":"16px"}},[n("el-button",{on:{click:t.cancel}},[t._v("取消")]),n("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确定")])],1)])},a=[]},f7ab:function(t,e,n){"use strict";n.r(e);var i=n("80097"),a=n("c841");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("0e64"),n("c164");var r=n("2877"),c=Object(r["a"])(a["default"],i["a"],i["b"],!1,null,"2431f597",null);e["default"]=c.exports},fcf3:function(t,e,n){}}]);