(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-92aa86a4"],{"05c4":function(e,t,a){"use strict";a.r(t);var n=a("0a58"),r=a("6118");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("dc2d");var i=a("2877"),d=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"7bfdbd84",null);t["default"]=d.exports},"0a58":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("el-button",{staticStyle:{"margin-bottom":"16px"},on:{click:e.tagBack}},[e._v("返回")]),a("div",{staticClass:"sch-detail"},[a("div",{directives:[{name:"show",rawName:"v-show",value:"add"===e.status,expression:"status==='add'"}],staticClass:"cs-custom-header"},[a("div",{staticClass:"header-text"},[e._v("审批意见")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"add"===e.status,expression:"status==='add'"}],staticClass:"handling-opinions"},[a("el-form",{ref:"formOpinions",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,"label-width":"85px"}},[a("el-form-item",{attrs:{label:"处理意见:",prop:"Handling_Opinions"}},[a("el-input",{staticStyle:{width:"500px"},attrs:{type:"textarea",disabled:e.isView,autosize:{minRows:4,maxRows:6},placeholder:"请输入"},model:{value:e.form.Handling_Opinions,callback:function(t){e.$set(e.form,"Handling_Opinions",t)},expression:"form.Handling_Opinions"}})],1)],1)],1),e._m(0),a("div",{staticClass:"form-search"},[a("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,"label-width":"85px"}},[a("el-form-item",{attrs:{label:"补换单号:",prop:"Remark"}},[a("el-input",{staticStyle:{width:"260px"},attrs:{disabled:!0,placeholder:"请输入"},model:{value:e.Replace_Code,callback:function(t){e.Replace_Code=t},expression:"Replace_Code"}})],1),a("el-form-item",{attrs:{label:"项目名称:"}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",filterable:"",disabled:!0},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projectOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域:"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"cs-tree-x",attrs:{"tree-params":e.treeParams,"select-params":{clearable:!1},disabled:!0},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"批次:"}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",filterable:"",disabled:!0},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.installOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{required:"",label:"补换班组:",prop:"Replace_Teams_Id",rules:{required:!0,message:"请选择",trigger:"change"}}},[a("el-select",{staticStyle:{width:"260px"},attrs:{placeholder:"请选择",disabled:!0,filterable:""},model:{value:e.form.Replace_Teams_Id,callback:function(t){e.$set(e.form,"Replace_Teams_Id",t)},expression:"form.Replace_Teams_Id"}},e._l(e.replace_Teams_Factory,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"申请日期:",prop:"Quest_Date"}},[a("el-date-picker",{staticStyle:{width:"260px"},attrs:{disabled:!0,"value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期"},model:{value:e.form.Quest_Date,callback:function(t){e.$set(e.form,"Quest_Date",t)},expression:"form.Quest_Date"}})],1),a("el-form-item",{attrs:{label:"备注:",prop:"Remark"}},[a("el-input",{staticStyle:{width:"260px"},attrs:{disabled:!0},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),e._m(1),a("div",{staticClass:"twrap"},[a("div",{staticStyle:{height:"100%"}},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t){return a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:""},scopedSlots:e._u([{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(n[t.Code]||"-")+" ")]}}],null,!0)})})),1)],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.queryHistoriesList.length>0,expression:"queryHistoriesList.length>0"}],staticClass:"flow-info"},[a("div",{staticClass:"flow-info_title"},[e._v("流转信息")]),e._l(e.queryHistoriesList,(function(t,n){return a("div",{key:n},[0==t.Verification_Status?[a("div",{staticClass:"flow-info_user"},[e._v("发起人")]),a("div",{staticClass:"flow-info_options"},[a("span",[e._v(e._s(t.Create_UserName))]),a("span"),a("span",[e._v(e._s(t.Create_Date))])])]:[a("div",{staticClass:"flow-info_user"},[e._v("审批人")]),a("div",{staticClass:"flow-info_options"},[a("span",[e._v(e._s(t.Create_UserName))]),a("span",[e._v(e._s(1===t.Verification_Status?"通过":"退回"))]),a("span",[e._v(e._s(t.Create_Date))]),a("div",{staticClass:"remark"},[e._v(" 处理意见："+e._s(t.Verification_Opinion)+" ")])])]],2)}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:"view"!==e.status,expression:"status!=='view'"}],staticClass:"header-btns"},[a("el-button",{attrs:{loading:e.btnLoading,disabled:e.isClicked},on:{click:function(t){return e.save(3)}}},[e._v("退回")]),a("el-button",{attrs:{loading:e.btnLoading,type:"primary",disabled:e.isClicked},on:{click:function(t){return e.save(1)}}},[e._v("通过")])],1)])],1)},r=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-custom-header"},[a("div",{staticClass:"header-text"},[e._v("基本信息")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-custom-header"},[a("div",{staticClass:"header-text"},[e._v("补换零件列表")])])}]},6118:function(e,t,a){"use strict";a.r(t);var n=a("85b8"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"7015f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddChangeCopyHistory=y,t.AgainSubmitChangeOrder=_,t.BatchReuseEngineeringContactChangedComponentPart=ie,t.BatchReuseEngineeringContactMocComponentPart=de,t.CancelChangeOrder=O,t.ChangeMocOrderStatus=ee,t.CheckCanMocName=F,t.DeleteChangeOrder=c,t.DeleteChangeOrderV2=D,t.DeleteChangeReason=h,t.DeleteChangeType=d,t.DeleteEngineeringContactChangeOrder=J,t.DeleteMocOrder=W,t.DeleteMocType=Pe,t.ExportEngineeringContactChangedAddComponentPart=le,t.ExportEngineeringContactChangedComponentPartPageList=te,t.ExportEngineeringContactMocComponentPartPageList=re,t.ExportMocAddComponentPart=ce,t.FinishEngineeringContactChangeOrder=Z,t.GetChangeCopyHistoryList=b,t.GetChangeOrdeDetail=f,t.GetChangeOrderPageList=u,t.GetChangeOrderTaskInfo=k,t.GetChangeOrderTaskPageList=S,t.GetChangeOrderV2=V,t.GetChangeOrderV2PageList=E,t.GetChangeReason=g,t.GetChangeType=o,t.GetChangedComponentPartPageList=N,t.GetChangedComponentPartProductionList=j,t.GetCompAndPartSchdulingPageList=L,t.GetCompAndPartTaskList=M,t.GetCompanyUserPageList=T,t.GetEngineeringContactChangeOrder=z,t.GetEngineeringContactChangeOrderPageList=q,t.GetEngineeringContactChangedAddComponentPartPageList=se,t.GetEngineeringContactChangedAddComponentPartSummary=ue,t.GetEngineeringContactChangedComponentPartPageList=Y,t.GetEngineeringContactChangedSummary=X,t.GetEngineeringContactFileInfo=Q,t.GetEngineeringContactMocAddComponentPartSummary=fe,t.GetEngineeringContactMocComponentPartPageList=K,t.GetEngineeringContactMocSummary=ne,t.GetFactoryChangeTypeListV2=U,t.GetFactoryPeoplelist=s,t.GetMocAddComponentPartPageList=ge,t.GetMocModelList=_e,t.GetMocOrderInfo=pe,t.GetMocOrderPageList=me,t.GetMocOrderTypeList=Ce,t.GetMyChangeOrderPageList=P,t.GetProjectAreaChangeTreeList=H,t.GetProjectChangeOrderList=G,t.GetTypeReason=p,t.ImportChangFile=he,t.ImportChangeDeependFile=C,t.QueryHistories=R,t.ReuseEngineeringContactChangedComponentPart=ae,t.ReuseEngineeringContactMocComponentPart=oe,t.SaveChangeOrder=l,t.SaveChangeOrderTask=w,t.SaveChangeOrderV2=x,t.SaveChangeReason=m,t.SaveChangeType=i,t.SaveEngineeringContactChangeOrder=B,t.SaveMocOrder=ve,t.SaveMocOrderType=Oe,t.SubmitChangeOrder=v,t.SubmitChangeOrderV2=A,t.SubmitMocOrder=$,t.Verification=I;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Change/GetChangeType",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Change/SaveChangeType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeType",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrder",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrder",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrdeDetail",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Change/GetChangeReason",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Change/SaveChangeReason",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeReason",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Change/GetTypeReason",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Change/ImportChangeDeependFile",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Change/GetMyChangeOrderPageList",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Change/CancelChangeOrder",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Change/AgainSubmitChangeOrder",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrder",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Change/AddChangeCopyHistory",method:"post",data:e})}function R(e){return(0,r.default)({url:"SYS/FlowInstances/QueryHistories?"+e,method:"get",data:e})}function b(e){return(0,r.default)({url:"/PRO/Change/GetChangeCopyHistoryList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/SYS/FlowInstances/Verification",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/Change/GetProjectChangeOrderList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskPageList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartSchdulingPageList",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/SaveChangeOrderTask",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompAndPartTaskList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/ChangeOrderTask/GetChangeOrderTaskInfo",method:"post",data:e})}function T(e){return(0,r.default)({url:"/Platform/User/GetCompanyUserPageList",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2PageList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/Change/SaveChangeOrderV2",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/Change/DeleteChangeOrderV2",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/Change/SubmitChangeOrderV2",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/moc/SubmitMocOrder",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/moc/CheckCanMocName",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/Change/GetChangeOrderV2",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/Project/GetProjectAreaChangeTreeList",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartPageList",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Change/GetChangedComponentPartProductionList",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrderPageList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/Change/GetFactoryChangeTypeListV2",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactFileInfo",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/Change/SaveEngineeringContactChangeOrder",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangeOrder",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/Change/DeleteEngineeringContactChangeOrder",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/moc/DeleteMocOrder",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocComponentPartPageList",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedSummary",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PRO/Change/FinishEngineeringContactChangeOrder",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/MOC/ChangeMocOrderStatus",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedComponentPartPageList",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Change/ReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocSummary",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Moc/ExportEngineeringContactMocComponentPartPageList",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Moc/ReuseEngineeringContactMocComponentPart",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Change/BatchReuseEngineeringContactChangedComponentPart",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/Moc/BatchReuseEngineeringContactMocComponentPart",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartPageList",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/Change/GetEngineeringContactChangedAddComponentPartSummary",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Change/ExportEngineeringContactChangedAddComponentPart",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PRO/Moc/ExportMocAddComponentPart",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/Moc/GetEngineeringContactMocAddComponentPartSummary",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/Moc/GetMocAddComponentPartPageList",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderPageList",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/moc/ImportChangFile",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderInfo",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PRO/moc/GetMocOrderTypeList",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/moc/DeleteMocType",method:"post",data:e})}function Oe(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrderType",method:"post",data:e})}function _e(e){return(0,r.default)({url:"/PRO/moc/GetMocModelList",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PRO/moc/SaveMocOrder",method:"post",data:e})}},"85b8":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("3ca3"),a("c7cd"),a("ddb0");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),d=a("6186"),s=a("fd31"),u=a("87c9"),l=a("7015f"),c=a("4799"),f=n(a("dd30")),g=a("8975"),m=a("ed08");t.default={name:"PackingAdd",components:{},mixins:[f.default],data:function(){return{tbLoading:!1,btnLoading:!1,isClicked:!1,confirmed:!1,warehouses:[],locations:[],tbConfig:{},columns:[],tbData:[],Part_Header:[],pageInfo:{Page:1,TotalCount:0,PageSize:-1,PageSizes:[20,40,60,80,100]},form:{ProjectName:"",Project_Id:"",Sys_Project_Id:"",Area_Id:"",InstallUnit_Id:"",Replace_Teams_Id:"",Quest_Date:"",Remark:"",Handling_Opinions:""},Replace_Code:"",Instance_Id:"",Details:[],queryHistoriesList:[],gridCode:"pro_parts_replace_page_res",Proportion:0,Unit:"",Id:""}},computed:{status:function(){return this.$router.currentRoute.query.status},isView:function(){return"view"===this.$router.currentRoute.query.status},dateDefault:function(){var e=new Date,t=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate();return t}},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTypeList();case 1:return t.n=2,e.fetchData();case 2:return t.a(2)}}),t)})))()},mounted:function(){var e=this;this.form.Project_Id=this.$router.currentRoute.query.projectId,this.queryForm.projectId=this.$router.currentRoute.query.projectId,this.form.Area_Id=this.$router.currentRoute.query.areaId,this.queryForm.areaId=this.$router.currentRoute.query.areaId,this.form.InstallUnit_Id=this.$router.currentRoute.query.install,this.queryForm.install=this.$router.currentRoute.query.install,this.Id=this.$router.currentRoute.query.Id,this.getAreaList(),this.getInstall(),(0,c.getFactoryProfessional)().then((function(t){e.Proportion=t[0].Proportion,e.Unit=t[0].Unit}))},methods:{getTypeList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,n,r,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.form.TypeId=null===(r=e.typeOption[0])||void 0===r?void 0:r.Id,e.form.Type_Name=null===(i=e.typeOption[0])||void 0===i?void 0:i.Name)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,d.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.form.TypeId})).Code}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=r.ColumnList||[],d=i.sort((function(e,t){return e.Sort-t.Sort}));t.columns=d.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),e})),t.columns.push({Display_Name:"补换数量",Code:"Amount"}),t.columns.map((function(e){if("Weight"===e.Code)return e.minWidth="160",e.Width="auto",e})),a(t.columns);var s=JSON.parse(JSON.stringify(t.columns));t.columnsOption=s}else t.$message({message:o,type:"error"})}))}))},fetchData:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:if(e.tbLoading=!0,e.Id){t.n=2;break}return e.tbLoading=!1,t.a(2);case 2:Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 3:return t.a(2)}}),t)})))()},fetchList:function(){var e=this;return new Promise((function(t){(0,u.FindPartReplaceApplyById)({Id:e.Id}).then((function(a){if(a.IsSucceed){var n=a.Data.Part_Header;e.tbData=a.Data.Part_List,e.Part_Header=n,e.Replace_Code=n.Replace_Code,e.form.Replace_Teams_Id=n.Replace_Teams_Id,e.form.Quest_Date=n.Quest_Date,e.Audit_Results_Id=n.Audit_Results_Name||2,e.Replace_Status_Id=-1===n.Replace_Status_Name?null:n.Replace_Status_Name,e.form.Remark=n.Remark,e.Instance_Id=n.Instance_Id,e.selectList=[],e.getQueryHistories()}else e.$message({message:a.Message,type:"error"});t()}))}))},getQueryHistories:function(){var e=this;(0,l.QueryHistories)("FlowInstanceId=".concat(this.Instance_Id)).then((function(t){if(t.IsSucceed){var a=t.Data.reverse();e.queryHistoriesList=a.map((function(e){return e.Create_Date=e.Create_Date?(0,g.timeFormat)(e.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"):e.Create_Date,e}))}}))},tagBack:function(){var e=this;this.$confirm("此操作不会保存数据，是否离开?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.confirmed=!0,(0,m.closeTagView)(e.$store,e.$route)})).catch((function(){e.$message({type:"info",message:"已取消"})}))},save:function(e){var t=this;this.isClicked=!0,this.btnLoading=!0;var a={};a.VerificationFinally=e,a.VerificationOpinion=this.form.Handling_Opinions,a.flowInstanceId=this.Instance_Id,(0,l.Verification)((0,r.default)({},a)).then((function(e){e.IsSucceed?(t.$message({message:"审批成功",type:"success"}),(0,m.closeTagView)(t.$store,t.$route)):t.$message({message:e.Message,type:"error"}),t.isClicked=!1,t.btnLoading=!1}))}}}},dc2d:function(e,t,a){"use strict";a("ddac")},ddac:function(e,t,a){}}]);