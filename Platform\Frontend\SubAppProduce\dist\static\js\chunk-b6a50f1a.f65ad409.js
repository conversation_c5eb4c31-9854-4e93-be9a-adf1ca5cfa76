(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b6a50f1a"],{"0755":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-z-flex-pd16-wrap abs100"},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"领料单号"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"领用工序"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"领用班组"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"领用项目"}},[a("el-input",{attrs:{placeholder:"请输入",type:"text"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("删除")]),a("el-button",{on:{click:e.handleReset}},[e._v("保存领料单")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(n){var i=n.row;return["AvailableWeight"===t.Code?a("span",[e._v(" "+e._s(i[t.Code]||0===i[t.Code]?(i[t.Code]/1e3).toFixed(5):"-")+" ")]):a("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}}],null,!0)})]}))],2)],1),a("footer",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选 "+e._s(e.multipleSelection.length)+" 条数据 ")]),a("Pagination",{attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)],1)])},i=[]},"3ce4":function(e,t,a){"use strict";a.r(t);var n=a("df73"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"4afc":function(e,t,a){"use strict";a("ed98")},"8d03":function(e,t,a){"use strict";a.r(t);var n=a("0755"),i=a("3ce4");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("4afc");var l=a("2877"),r=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"b46d6b8c",null);t["default"]=r.exports},df73:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("c14f")),o=n(a("1da1")),l=n(a("333d")),r=n(a("15ac")),s=a("c685");t.default={name:"ProMaterialRequisition",components:{Pagination:l.default},mixins:[r.default],data:function(){return{form:{},tbLoading:!1,options:[],columns:[],tbData:[],multipleSelection:[],tablePageSize:s.tablePageSize,total:0,queryInfo:{Page:1,PageSize:20}}},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("PRONestingManagementIndex");case 1:e.fetchData(1);case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(e){e&&(this.queryInfo.Pageage=e)},handleSearch:function(){},handleReset:function(){this.$refs["form"].resetFields()}}}},ed98:function(e,t,a){}}]);