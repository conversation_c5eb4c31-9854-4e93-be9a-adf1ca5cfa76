(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-268b62d0"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=u,Math.easeInOutQuad=function(e,t,a,r){return e/=r/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function u(e,t,a){var u=i(),o=e-u,l=20,s=0;t="undefined"===typeof t?500:t;var c=function(){s+=l;var e=Math.easeInOutQuad(s,u,o,t);n(e),s<t?r(c):a&&"function"===typeof a&&a()};c()}},"0a67":function(e,t,a){"use strict";a.r(t);var r=a("5ed6"),n=a("e09e");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("a018");var u=a("2877"),o=Object(u["a"])(n["default"],r["a"],r["b"],!1,null,"646650f5",null);t["default"]=o.exports},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186"),n=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,r.GetGridByCode)({code:e,IsAll:a}).then((function(e){var r=e.IsSucceed,u=e.Data,o=e.Message;if(r){if(!u)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,u.Grid),l=a?(null===u||void 0===u?void 0:u.ColumnList)||[]:(null===u||void 0===u?void 0:u.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+u.Grid.Row_Number||n.tablePageSize[0]),i(t.columns)}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,r=e.type;this.queryInfo.Page="limit"===r?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1e50":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),i=r(a("c14f")),u=r(a("1da1"));a("14d9"),a("b0c0"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("ddb0");var o=a("ed08"),l=r(a("15ac")),s=r(a("333d")),c=a("c685"),d=r(a("2082")),f=a("2245"),p=r(a("5d4b"));t.default={name:"PROAuxiliaryMaterialReceipt",components:{Pagination:s.default},mixins:[l.default,d.default,p.default],data:function(){return{addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-72cd76c0"),a.e("chunk-b32e68f0"),a.e("chunk-6654959c")]).then(a.bind(null,"2168"))},name:"PROAuxMaterialReceiptAdd",meta:{title:"新建入库单"}},{path:this.$route.path+"/edit/:id",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-72cd76c0"),a.e("chunk-b32e68f0"),a.e("chunk-434356f8")]).then(a.bind(null,"d9366"))},name:"PROAuxMaterialReceiptEdit",meta:{title:"编辑入库单"}},{path:this.$route.path+"/view/:id",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-72cd76c0"),a.e("chunk-b32e68f0"),a.e("chunk-04882114")]).then(a.bind(null,"b4f0"))},name:"PROAuxMaterialReceiptView",meta:{title:"查看入库单"}}],tablePageSize:c.tablePageSize,tbLoading:!1,columns:[],tbData:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1,PageSize:c.tablePageSize[0]},total:0,form:{ReceiptNumber:"",EntryType:"",EntryDate:"",Status:void 0,Type:1},rules:{},search:function(){return{}}}},mounted:function(){var e=this;return(0,u.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.search=(0,o.debounce)(e.fetchData,800,!0),t.n=1,e.getTableConfig("PROAuxMaterialReceiptList");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},activated:function(){this.fetchData(1)},methods:{fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),(0,f.GetWarehouseReceiptPageList)((0,n.default)({PageInfo:this.queryInfo},this.form)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.multipleSelection=[],t.tbLoading=!1}))},handleAdd:function(){this.$router.push({name:"PROAuxMaterialReceiptAdd",query:{pg_redirect:this.$route.name}})},handleReset:function(){this.$refs["form"].resetFields(),this.search(1)},handleView:function(e){this.$router.push({name:"PROAuxMaterialReceiptView",query:{pg_redirect:this.$route.name},params:{id:e.Id}})},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},handleDelete:function(e){var t=this;this.$confirm("是否删除该入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.DeleteWarehouseReceipt)({id:[e.Id]}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(e){this.$router.push({name:"PROAuxMaterialReceiptEdit",query:{pg_redirect:this.$route.name},params:{id:e.Id}})},handleSubmit:function(e){var t=this;this.$confirm("确认提交入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.SubmitWarehouseReceipt)({id:[e.Id]}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"提交成功!"}),t.fetchData(1)):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))}}}},2245:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ActiveAuxMaterial=D,t.ActiveRawMaterial=y,t.DeleteAuxMaterial=w,t.DeleteMaterialCategory=d,t.DeleteMaterials=s,t.DeleteRawMaterial=R,t.DeleteWarehouseReceipt=O,t.ExportPurchaseDetail=L,t.GetAuxMaterialEntity=b,t.GetAuxMaterialPageList=g,t.GetAuxStandardsList=E,t.GetAuxWarehouseReceiptEntity=C,t.GetMaterialCategoryList=c,t.GetMaterialImportPageList=u,t.GetPurchaseDetail=G,t.GetPurchaseDetailList=$,t.GetRawMaterialEntity=m,t.GetRawMaterialPageList=h,t.GetRawStandardsList=x,t.GetRawWarehouseReceiptEntity=A,t.GetWarehouseReceiptPageList=S,t.ImportMatAux=_,t.ImportMatAuxRcpt=k,t.ImportMatRaw=v,t.ImportMatRawRcpt=V,t.ImportMaterial=l,t.MaterialDataTemplate=o,t.SaveAuxMaterialEntity=P,t.SaveAuxWarehouseReceipt=N,t.SaveMaterialCategory=f,t.SaveRawMaterialEntity=p,t.SaveRawWarehouseReceipt=I,t.SubmitWarehouseReceipt=T,t.TemplateDownload=M;var n=r(a("b775")),i=r(a("4328"));function u(e){return(0,n.default)({url:"/PRO/Material/GetMaterialImportPageList",method:"post",data:e})}function o(){return(0,n.default)({url:"/PRO/Material/MaterialDataTemplate",method:"post"})}function l(e){return(0,n.default)({url:"/PRO/Material/ImportMaterial",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Material/DeleteMaterials",method:"post",data:i.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/Material/GetMaterialCategoryList",method:"post",params:e})}function d(e){return(0,n.default)({url:"/PRO/Material/DeleteMaterialCategory",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Material/SaveMaterialCategory",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Material/SaveRawMaterialEntity",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Material/GetRawMaterialEntity",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Material/GetRawMaterialPageList",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Material/ActiveRawMaterial",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Material/DeleteRawMaterial",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialPageList",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialEntity",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/Material/TemplateDownload",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Material/ImportMatRaw",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Material/ImportMatAux",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Material/SaveAuxMaterialEntity",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Material/ActiveAuxMaterial",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/Material/DeleteAuxMaterial",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Material/GetWarehouseReceiptPageList",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/Material/GetRawStandardsList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Material/SaveRawWarehouseReceipt",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/Material/DeleteWarehouseReceipt",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/Material/GetRawWarehouseReceiptEntity",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Material/SubmitWarehouseReceipt",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/Material/GetAuxWarehouseReceiptEntity",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/Material/SaveAuxWarehouseReceipt",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/Material/GetAuxStandardsList",method:"post",data:e})}function V(e){return(0,n.default)({url:"/PRO/Material/ImportMatRawRcpt",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Material/ImportMatAuxRcpt",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetail",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/Material/ExportPurchaseDetail",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetailList",method:"post",data:e})}},"5ed6":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card h100"},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"单据编号",prop:"ReceiptNumber"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.ReceiptNumber,callback:function(t){e.$set(e.form,"ReceiptNumber",t)},expression:"form.ReceiptNumber"}})],1),a("el-form-item",{attrs:{label:"入库类型",prop:"EntryType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.EntryType,callback:function(t){e.$set(e.form,"EntryType",t)},expression:"form.EntryType"}},[a("el-option",{attrs:{label:"手动入库",value:0}}),a("el-option",{attrs:{label:"退料入库",value:1}}),a("el-option",{attrs:{label:"采购入库",value:2}})],1)],1),a("el-form-item",{attrs:{label:"入库日期",prop:"EntryDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"","value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.EntryDate,callback:function(t){e.$set(e.form,"EntryDate",t)},expression:"form.EntryDate"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),a("el-divider",{staticClass:"elDivder"}),a("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增入库单")])]},proxy:!0}])}),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,sortable:""},scopedSlots:e._u(["EntryDate"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]}}:"CreateDate"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}:"Status"===t.Code?{key:"default",fn:function(t){var r=t.row;return[0===r.Status?a("el-tag",{attrs:{type:"danger"}},[e._v("草稿")]):e._e(),1===r.Status?a("el-tag",{attrs:{type:"success"}},[e._v("已提交")]):e._e()]}}:"EntryType"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(0===a.EntryType?"手动入库":1===a.EntryType?"退料入库":"采购入库")+" ")]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(r)}}},[e._v("查看")]),0===r.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleSubmit(r)}}},[e._v("提交")]):e._e(),0===r.Status?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]):e._e(),0===r.Status?a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")]):e._e()]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)],1)],1)},n=[]},"8fea":function(e,t){e.exports={WEIGHT_CONVERSION:1e3,WEIGHT_DECIMAL:5,WEIGHT_KG_DECIMAL:2,COUNT_DECIMAL:2,UNIT_PRICE_DECIMAL:6,UNIT_PRICE_KG_DECIMAL:9,RawInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],RawReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],RawAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],RawOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:3}],RawAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}],AuxInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],AuxReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],AuxAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],AuxOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:2}],AuxAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}]}},a018:function(e,t,a){"use strict";a("cfc0")},cfc0:function(e,t,a){},e09e:function(e,t,a){"use strict";a.r(t);var r=a("1e50"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a}}]);