(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-9b3894c8"],{"0d44":function(e,t,a){"use strict";a.r(t);var r=a("7d99"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},"46b7":function(e,t,a){"use strict";a.r(t);var r=a("d6c4"),n=a("0d44");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("ac6d");var i=a("2877"),u=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"7d6c897e",null);t["default"]=u.exports},"5f52":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("dca8"),a("d3b7");var n=r(a("c14f")),o=r(a("1da1")),i=a("6186"),u=a("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,o.default)((0,n.default)().m((function a(){return(0,n.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getTypeList();case 1:return a.n=2,t.getTable(e);case 2:return a.a(2)}}),a)})))()},getTypeList:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var r,n=null===(r=a[0])||void 0===r?void 0:r.Id;e.Code=a.find((function(e){return e.Id===n})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(a){(0,i.GetGridByCode)({code:e+","+t.Code}).then((function(e){var r=e.IsSucceed,n=e.Data,o=e.Message;if(r){if(!n)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,n.Grid),t.columns=(n.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),n.Grid.Is_Page&&(t.queryInfo.PageSize=+n.Grid.Row_Number),a(t.columns)}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var r=0;r<this.columns.length;r++){var n=this.columns[r];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"6bc1":function(e,t,a){"use strict";a.r(t);var r=a("e2fc"),n=a("8d87");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("7d2f");var i=a("2877"),u=Object(i["a"])(n["default"],r["a"],r["b"],!1,null,"3ce7b27d",null);t["default"]=u.exports},7196:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteWorkshop=s,t.GetFactoryPeoplelist=o,t.GetWorkshopEntity=l,t.GetWorkshopPageList=u,t.SaveEntity=i;var n=r(a("b775"));function o(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:e})}},"7d2f":function(e,t,a){"use strict";a("f3af")},"7d99":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7");var n=r(a("5530")),o=r(a("c14f")),i=r(a("1da1")),u=r(a("5f52")),l=r(a("0f97")),s=a("3c4a"),d=a("ed08"),c=a("8378");t.default={components:{DynamicDataTable:l.default},mixins:[u.default],props:{searchDetail:{type:Object,default:function(){return{}}},activeName:{type:String,default:"0"}},data:function(){return{pgLoading:!1,btnloading:!1,btnLoading2:!1,queryInfo:{Page:1,PageSize:20},columns:[],currentColumns:[],tbData:[],tbConfig:{Op_Width:180},total:0,dialogVisible:!1,dialogTitle:"",width:0,currentComponent:"",form:{},totalNum:{},materialType:0}},computed:{storeType:function(){return"1"===this.activeName?0:1},storeTypeName:function(){return"1"===this.activeName?"入库":"出库"}},watch:{activeName:{handler:function(e){var t=this;return(0,i.default)((0,o.default)().m((function a(){var r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:t.pgLoading=!0,r=e,a.n="1"===r?1:"2"===r?3:5;break;case 1:return a.n=2,t.getTableConfig("pro_flow_warehousing_detail");case 2:return t.currentColumns=t.columns.filter((function(e){return"辅料名称"!==e.Display_Name&&"规格"!==e.Display_Name})),t.pgLoading=!1,a.a(3,6);case 3:return a.n=4,t.getTableConfig("pro_flow_outbound_detail");case 4:return t.currentColumns=t.columns.filter((function(e){return"辅料名称"!==e.Display_Name&&"规格"!==e.Display_Name})),t.pgLoading=!1,a.a(3,6);case 5:case 6:t.currentColumns.find((function(e){return"Modify_Time"===e.Code})).Frozen_Dirction="right";case 7:return a.a(2)}}),a)})))()},immediate:!0}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.fetchData();case 1:return t.a(2)}}),t)})))()},methods:{fetchData:function(e){var t=this,a=this.searchDetail,r=a.RawNameFull,o=a.Materiel_Name,i=a.Category_Id,u=a.In_Store_Type,l=a.Out_Store_Type,c=a.Sys_Project_Id,f=a.Supplier,p=a.Party_Unit,m=a.WH_Id,_=a.Location_Id,h=a.DateRange,y=a.ReceiveUserId;this.form={},this.form.RawNameFull=r,this.form.Materiel_Name=o,this.form.Category_Id=i,this.form.In_Store_Type=u,this.form.Out_Store_Type=l,this.form.Sys_Project_Id=c,this.form.ReceiveUserId=y,this.form.Supplier=f,this.form.Party_Unit=p,this.form.WH_Id=m,this.form.Location_Id=_,this.form.Store_Date_Begin=h?h[0]:"",this.form.Store_Date_End=h?h[1]:"",this.form.Flow_Type=10,e&&(this.queryInfo.Page=e);var v="";"1"===this.activeName?v=s.GetInPageList:"2"===this.activeName&&(delete this.form.Supplier,delete this.form.Party_Unit,v=s.GetOutPageList),v((0,n.default)((0,n.default)({},this.form),this.queryInfo)).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.map((function(e){return e.In_Out_Store_Date=e.In_Out_Store_Date?(0,d.parseTime)(new Date(e.In_Out_Store_Date),"{y}-{m}-{d}"):e.In_Out_Store_Date,e})),t.total=e.Data.TotalCount):t.$message.error(e.Message)})).finally((function(e){}));var g=1==this.activeName?s.GetInPageListSum:s.GetOutSum;g((0,n.default)((0,n.default)({},this.form),this.queryInfo)).then((function(e){t.totalNum=e.Data||{}}))},handelExport:function(){var e=this;this.btnloading=!0,(0,s.ExportFlow)((0,n.default)({type:"1"===this.activeName?1:2,Page:1,PageSize:-1},this.form)).then((function(t){t.IsSucceed?window.open((0,d.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)})).finally((function(t){e.btnloading=!1}))},exportInOutStoreReport:function(){var e=this;this.btnLoading2=!0,(0,c.ExportInOutStoreReport)((0,n.default)({materialType:this.materialType,storeType:this.activeName-1},this.form)).then((function(t){t.IsSucceed?window.open((0,d.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)})).finally((function(t){e.btnLoading2=!1}))}}}},8378:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CreateVersion=k,t.DelAuxCategoryEntity=g,t.DelAuxEntity=b,t.DelCategoryEntity=l,t.DelRawEntity=c,t.DeleteVersion=N,t.EditAuxEnabled=P,t.EditRawEnabled=d,t.ExportAuxForProject=z,t.ExportAuxList=O,t.ExportFindRawInAndOut=j,t.ExportInOutStoreReport=X,t.ExportPicking=M,t.ExportRawList=S,t.ExportRecSendProjectMaterialReport=Q,t.ExportRecSendProjectReport=K,t.ExportReceiving=L,t.ExportStagnationInventory=B,t.ExportStoreReport=J,t.FindInAndOutPageList=W,t.FindPickingNewPageList=G,t.FindPickingPageList=F,t.FindReceivingNewPageList=A,t.FindReceivingPageList=E,t.GetAuxCategoryDetail=v,t.GetAuxCategoryTreeList=h,t.GetAuxDetail=x,t.GetAuxFilterDataSummary=H,t.GetAuxForProjectDetail=q,t.GetAuxForProjectPageList=V,t.GetAuxPageList=w,t.GetAuxTemplate=C,t.GetAuxWHSummaryList=$,t.GetCategoryDetail=u,t.GetCategoryTreeList=o,t.GetCycleDate=U,t.GetList=D,t.GetRawDetail=p,t.GetRawPageList=f,t.GetRawTemplate=m,t.ImportAuxList=I,t.ImportRawList=_,t.SaveAuxCategoryEntity=y,t.SaveAuxEntity=R,t.SaveCategoryEntity=i,t.SaveRawEntity=s,t.UpdateVersion=T;var n=r(a("b775"));function o(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:e,timeout:12e5})}function h(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:e,timeout:12e5})}function S(e){return(0,n.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:e})}function k(e){return(0,n.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:e})}function U(e){return(0,n.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:e})}function j(e){return(0,n.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:e})}function V(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:e})}function q(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:e})}function z(e){return(0,n.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:e})}function H(e){return(0,n.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:e})}function B(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:e})}function J(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:e})}function K(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:e})}function Q(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:e})}function X(e){return(0,n.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:e})}},"8d87":function(e,t,a){"use strict";a.r(t);var r=a("9528"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=n.a},9528:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("e9f5"),a("910d"),a("d3b7");var n=r(a("c14f")),o=r(a("2909")),i=r(a("1da1")),u=r(a("bc29")),l=r(a("40c9")),s=r(a("46b7")),d=a("8378"),c=a("cf45"),f=r(a("c13a"));t.default={name:"PROMaterialInventory",components:{SelectDepartmentUser:f.default,inventory:s.default},mixins:[u.default,l.default],data:function(){return{activeName:"1",form:{RawNameFull:"",Materiel_Name:"",Category_Id:"",In_Store_Type:"",Out_Store_Type:"",Sys_Project_Id:"",Supplier:"",Party_Unit:"",DateRange:"",WH_Id:"",Location_Id:"",ReceiveUserId:""},categoryOptions:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},treeList:[],RawReceiptTypeList:[],RawOutboundTypeList:[]}},mounted:function(){this.getCategoryList(),this.getBaseData()},methods:{getBaseData:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.getDictionary)("RawReceiptType").then((function(t){e.RawReceiptTypeList=t.filter((function(e){return e.Is_Enabled})),e.RawReceiptTypeList.push({Display_Name:"原料退库",Value:"4"})}));case 1:return t.n=2,(0,c.getDictionary)("RawOutboundType").then((function(t){e.RawOutboundTypeList=t.filter((function(e){return e.Is_Enabled}))}));case 2:return t.n=3,(0,c.getDictionary)("RawGoodsReturnType").then((function(t){var a;(a=e.RawOutboundTypeList).push.apply(a,(0,o.default)(t.filter((function(e){return e.Is_Enabled}))))}));case 3:return t.a(2)}}),t)})))()},handleTap:function(){var e=this;this.form={Materiel_Name:"",Category_Id:"",In_Store_Type:"",Out_Store_Type:"",Sys_Project_Id:"",Supplier:"",Party_Unit:"",DateRange:"",WH_Id:"",Location_Id:""},this.$nextTick((function(t){e.$refs.inventoryRef.fetchData(1)}))},handleSearch:function(){this.$refs.inventoryRef.fetchData(1)},getCategoryList:function(){var e=this;(0,d.GetCategoryTreeList)({}).then((function(t){if(t.IsSucceed){e.treeList=t.Data;var a=t.Data;e.categoryOptions.data=a,e.$nextTick((function(t){e.$refs.treeSelectArea.treeDataUpdateFun(a)}))}else e.$message.error(t.Message)}))}}}},ac6d:function(e,t,a){"use strict";a("d96b")},cf45:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=n,a("d3b7");var r=a("6186");function n(e){return new Promise((function(t,a){(0,r.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},d6c4:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-wapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{loading:e.btnloading},on:{click:e.handelExport}},[e._v("导出")]),a("el-button",{attrs:{loading:e.btnLoading2},on:{click:e.exportInOutStoreReport}},[e._v("导出"+e._s(e.storeTypeName)+"报表")]),a("div",{staticClass:"total-wrapper"},[a("span",{staticStyle:{margin:"0 12px"}},[e._v("含税总金额："+e._s(e.totalNum.Alltax||0)+" "),a("span",{staticStyle:{"margin-left":"20px"}},[e._v("不含税总金额："+e._s(e.totalNum.NoAllTax||0))])])])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{height:"0",flex:"1"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.currentColumns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange},scopedSlots:e._u([{key:"RawNameFull",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Materiel_Name)+e._s(r.Material)+e._s(r.Spec?r.Spec:r.Thick||"-"))])]}},{key:"Spec",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Spec?r.Spec:r.Thick||"-"))])]}},{key:"Width",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Width||"-"))])]}},{key:"Length",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Length||"-"))])]}},{key:"Project_Name",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Project_Name||"-"))])]}},{key:"Supplier",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Supplier||"-"))])]}},{key:"Party_Unit",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Party_Unit||"-"))])]}},{key:"Receiving_Team",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Receiving_Team||"-"))])]}},{key:"Receiving_Person",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Receiving_Person||"-"))])]}},{key:"Pick_Department_Name",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Pick_Department_Name||"-"))])]}},{key:"Use_Processing_Name",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Use_Processing_Name||"-"))])]}},{key:"Car_Number",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Car_Number||"-"))])]}},{key:"Total_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Total_Weight||0===r.Total_Weight?(r.Total_Weight/1e3).toFixed(5):"-"))])]}},{key:"Pound_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Pound_Weight||0===r.Pound_Weight?(r.Pound_Weight/1e3).toFixed(3):"-"))])]}},{key:"Car_Voucher_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Car_Voucher_Weight||0===r.Car_Voucher_Weight?(r.Car_Voucher_Weight/1e3).toFixed(3):"-"))])]}},{key:"Car_Pound_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Car_Pound_Weight||0===r.Car_Pound_Weight?(r.Car_Pound_Weight/1e3).toFixed(3):"-"))])]}},{key:"Voucher_Weight",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Voucher_Weight||0===r.Voucher_Weight?(r.Voucher_Weight/1e3).toFixed(3):"-"))])]}},{key:"Delivery_No",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Delivery_No||"-"))])]}},{key:"Purchase_Contract_No",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Purchase_Contract_No||"-"))])]}},{key:"Tax_Unit_Price",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Tax_Unit_Price||0===r.Tax_Unit_Price?(1e3*r.Tax_Unit_Price).toFixed(2):"-"))])]}},{key:"NoTax_Unit_Price",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.NoTax_Unit_Price||0===r.NoTax_Unit_Price?(1e3*r.NoTax_Unit_Price).toFixed(2):"-"))])]}},{key:"Tax_Rate",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Tax_Rate||"-"))])]}},{key:"Tax_All_Price",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Tax_All_Price||0===r.Tax_All_Price?r.Tax_All_Price.toFixed(2):"-"))])]}},{key:"Remark",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Remark||"-"))])]}},{key:"Remark2",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Remark2||"-"))])]}},{key:"Remark3",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Remark3||"-"))])]}},{key:"Remark4",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Remark4||"-"))])]}},{key:"Remark5",fn:function(t){var r=t.row;return[a("div",[e._v(e._s(r.Remark5||"-"))])]}}])})],1)])},n=[]},d96b:function(e,t,a){},e2fc:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{ref:"searchDom",staticClass:"header_wrapper"},[a("el-tabs",{staticClass:"tab_header search-wrapper",on:{"tab-click":e.handleTap},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"入库明细",name:"1"}}),a("el-tab-pane",{attrs:{label:"出库明细",name:"2"}})],1),a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"原料全名",prop:"RawNameFull"}},[a("el-input",{attrs:{type:"text",placeholder:"通配符%",clearable:""},model:{value:e.form.RawNameFull,callback:function(t){e.$set(e.form,"RawNameFull",t)},expression:"form.RawNameFull"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"Materiel_Name"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Materiel_Name,callback:function(t){e.$set(e.form,"Materiel_Name",t)},expression:"form.Materiel_Name"}})],1),a("el-form-item",{attrs:{label:"原料分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0},"tree-params":e.categoryOptions},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1),"1"===e.activeName?a("el-form-item",{attrs:{label:"入库类型",prop:"In_Store_Type"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.In_Store_Type,callback:function(t){e.$set(e.form,"In_Store_Type",t)},expression:"form.In_Store_Type"}},e._l(e.RawReceiptTypeList,(function(e){return a("el-option",{key:e.Value,attrs:{label:e.Display_Name,value:Number(e.Value)}})})),1)],1):a("el-form-item",{attrs:{label:"出库类型",prop:"Out_Store_Type"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Out_Store_Type,callback:function(t){e.$set(e.form,"Out_Store_Type",t)},expression:"form.Out_Store_Type"}},e._l(e.RawOutboundTypeList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:Number(e.Value)}})})),1)],1),a("el-form-item",{attrs:{label:"所属项目",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),"1"===e.activeName?a("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}})],1):e._e(),"1"===e.activeName?a("el-form-item",{attrs:{label:"甲方单位",prop:"Party_Unit"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Party_Unit,callback:function(t){e.$set(e.form,"Party_Unit",t)},expression:"form.Party_Unit"}})],1):e._e(),a("el-form-item",{attrs:{label:"1"===e.activeName?"入库时间":"出库时间",prop:"DateRange"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.form.DateRange,callback:function(t){e.$set(e.form,"DateRange",t)},expression:"form.DateRange"}})],1),a("el-form-item",{attrs:{label:"仓库",prop:"WH_Id"}},[a("el-select",{ref:"WarehouseRef",attrs:{clearable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.WH_Id,callback:function(t){e.$set(e.form,"WH_Id",t)},expression:"form.WH_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{ref:"LocationRef",attrs:{clearable:"",placeholder:"请选择库位",disabled:!e.form.WH_Id},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),"2"===e.activeName?a("el-form-item",{attrs:{label:"领用人",prop:"ReceiveUserId"}},[a("SelectDepartmentUser",{model:{value:e.form.ReceiveUserId,callback:function(t){e.$set(e.form,"ReceiveUserId",t)},expression:"form.ReceiveUserId"}})],1):e._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1)],1),a("div",{staticClass:"main-wrapper"},[a("inventory",{ref:"inventoryRef",attrs:{"search-detail":e.form,"active-name":e.activeName}})],1)])},n=[]},f3af:function(e,t,a){}}]);