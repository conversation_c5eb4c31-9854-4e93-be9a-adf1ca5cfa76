(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-a345eb26"],{"4f39":function(t,e,i){"use strict";var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=r,e.timeFormat=o,i("d3b7"),i("4d63"),i("c607"),i("ac1f"),i("2c3e"),i("00b4"),i("25f0"),i("4d90"),i("5319");var n=a(i("53ca"));function r(t,e){if(0===arguments.length||!t)return null;var i,a=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,n.default)(t)?i=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),i=new Date(t));var r={y:i.getFullYear(),m:i.getMonth()+1,d:i.getDate(),h:i.getHours(),i:i.getMinutes(),s:i.getSeconds(),a:i.getDay()},o=a.replace(/{([ymdhisa])+}/g,(function(t,e){var i=r[e];return"a"===e?["日","一","二","三","四","五","六"][i]:i.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var i=t.split("~"),a=r(new Date(i[0]),e)+" ~ "+r(new Date(i[1]),e);return a}return t&&t.length>0?r(new Date(t),e):void 0}},5745:function(t,e,i){"use strict";i.r(e);var a=i("6eac"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},6439:function(t,e,i){"use strict";i.d(e,"a",(function(){return a})),i.d(e,"b",(function(){return n}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[i("div",{staticClass:"cs-z-page-main-content"},[i("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[i("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[i("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),i("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[i("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),i("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[i("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),i("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),i("el-divider"),i("div",{staticClass:"tb-info"},[i("label",[t._v("数据列表")]),i("div",{staticClass:"btn-x"},[t.showExport?i("el-button",{attrs:{disabled:t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e()],1)]),i("div",{staticClass:"tb-x"},[i("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},n=[]},"6eac":function(t,e,i){"use strict";var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9"),i("b0c0"),i("d3b7");var n=a(i("c14f")),r=a(i("1da1")),o=a(i("ac03")),l=i("cf45"),c=i("4f39"),s=a(i("3502")),d=i("d7ff"),u=i("8ff5"),f=i("05e0");e.default={name:"OMAProductionBalanceDetailInfo",components:{VTable:s.default},mixins:[o.default],data:function(){return{form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],showExport:!1}},computed:{curTitle:function(){return"".concat((0,c.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")}},beforeCreate:function(){this.curModuleKey=u.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.showExport=t.getRoles("OMAProBaExport"),t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,t.fetchData(),e.n=1,(0,l.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate()&&(this.loading=!0,(0,d.GetSCStockDailyDetailList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[];var i=t.setTotalData(t.tableData,t.generateColumn()),a=i.column;t.columns=a,t.$refs["tb"].setColumns(a)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleProductionSemiInfo:function(){this.$router.push({name:"OMAProductionBalanceDetailSemiInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var t=this,e=this.$createElement,i=180;return[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:6},field:"ProjectAbbreviation",minWidth:f.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:f.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:f.ProjectStatusW}]}]},{title:"公共结存摊销系数(%)",params:{rowSpan:2},fixed:"left",children:[{params:{none:"none"},children:[{params:{none:"none"},field:"PublicStockAmortizationFactor",minWidth:160}]}]},{title:"损耗率(%)",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"Loss_Rate",minWidth:100}]}]},{title:"物控调拨单价(元)",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"MaterialControlTransferUnitPrice",minWidth:150}]}]},{title:"前道工序主材结存",children:[{title:"下料工序原材料领用量(T)",children:[{minWidth:200,field:"XL_Material_Amount",title:0,isTotal:!0}]},{title:"下料工序加工量(T)",children:[{minWidth:i,field:"XL_Processing_Amount",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:i,field:"XL_Material_Stock_Price",title:0,isTotal:!0}]}]},{slots:{header:function(){var i=[e("span",["半成品结存"])];return t.getRoles("OMAProBaSeDetail")&&i.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleProductionSemiInfo()}}},["查看详情"])),i}},children:[{title:"半成品量(T)",children:[{minWidth:i,field:"Semi_Product_Stock",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:i,field:"Semi_Product_Stock_Price",title:0,isTotal:!0}]}]},{title:"生产部主材结存额",children:[{title:"生产部原材料领用量(T)",children:[{minWidth:i,field:"SC_Material_Amount",title:0,isTotal:!0}]},{title:"生产部成品入库量(T)",children:[{minWidth:i,field:"SC_Product_Stock_In_Amount",title:0,isTotal:!0}]},{title:"生产部主材结存量(T)",children:[{minWidth:i,field:"SC_Material_Stock_Amount",title:0,isTotal:!0}]},{title:"结存金额(元)",children:[{minWidth:i,field:"Material_Stock_Price",title:0,isTotal:!0}]}]},{title:"生产项目辅材结存",children:[{title:"辅材合价(元)",children:[{minWidth:i,field:"Aux_Stock_Price",title:0,isTotal:!0}]}]}]}}}},"914d":function(t,e,i){},96787:function(t,e,i){"use strict";i("914d")},aeea:function(t,e,i){"use strict";i.r(e);var a=i("6439"),n=i("5745");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("96787");var o=i("2877"),l=Object(o["a"])(n["default"],a["a"],a["b"],!1,null,"f86565b4",null);e["default"]=l.exports},cf45:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,i("d3b7");var a=i("6186");function n(t){return new Promise((function(e,i){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}}}]);