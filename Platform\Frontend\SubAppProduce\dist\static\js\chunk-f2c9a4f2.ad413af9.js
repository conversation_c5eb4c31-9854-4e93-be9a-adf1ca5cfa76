(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-f2c9a4f2"],{"0cd9":function(e,t,a){"use strict";a("17cb")},"17cb":function(e,t,a){},3516:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return s}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:" cs-z-page-main-content"},[a("div",{staticClass:"cs-header"},[a("router-link",{attrs:{to:{name:"PROInstallationUnit"}}},[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""}})],1),a("strong",{staticClass:"title"},[e._v(e._s(e.Project_Name)+" / "+e._s(e.ctitle))])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-z-tb-wrapper fff"},[a("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{tableSearch:e.tableSearch,gridSizeChange:e.handlePageChange,gridPageChange:e.handlePageChange},scopedSlots:e._u([{key:"hsearch_type_name",fn:function(t){var n=t.column;return[a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.fPartChange},model:{value:e.$refs.dyTable.searchedField[n.Code],callback:function(t){e.$set(e.$refs.dyTable.searchedField,n.Code,t)},expression:"$refs.dyTable.searchedField[column.Code]"}},[a("el-option",{attrs:{label:"全部",value:""}}),e._l(e.cmptTypes_1,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})}))],2)]}},{key:"hsearch_sub_type_name",fn:function(t){t.column;return[a("el-select",{attrs:{disabled:!e.$refs.dyTable.searchedField["type_name"],placeholder:"请选择",clearable:""},on:{change:e.showSearchBtn},model:{value:e.subTypeName,callback:function(t){e.subTypeName=t},expression:"subTypeName"}},[a("el-option",{attrs:{label:"全部",value:""}}),e._l(e.cmptTypes_2,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})}))],2)]}},{key:"production_status_name",fn:function(t){var n=t.row;return[a("span",{staticClass:"cs-status-circle",style:{background:e.getStatueInfo(n.production_status)}}),a("span",[e._v(e._s(n.production_status_name))])]}}])})],1)])])},s=[]},"6709b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var s=n(a("c14f")),c=n(a("1da1")),r=n(a("0f97")),i=a("f2f6"),o=n(a("15ac")),l=a("2e8a");t.default={name:"PROInstallationUnitDetail",components:{DynamicDataTable:r.default},mixins:[o.default],data:function(){return{queryInfo:{Page:1,PageSize:10,InstallUnit_Id:this.$route.query.id},tbLoading:!0,total:0,columns:[],tbData:[],tbConfig:{Pager_Align:"center"},Project_Name:"",subTypeName:"",ctitle:"",cmptTypes_1:[],cmptTypes_2:[]}},mounted:function(){var e=this;return(0,c.default)((0,s.default)().m((function t(){return(0,s.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("pro_installunit_component_list");case 1:e.fetchData(),e.getComponentType(),e.$nextTick((function(t){e.subTypeName=e.$refs.dyTable.searchedField["sub_type_name"]}));case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,i.GetInstallUnitComponentPageList)(this.queryInfo).then((function(t){e.tbData=t.Data.Data,e.Project_Name=t.Data.Project_Name,e.ctitle=t.Data.InstallUnit_Name,e.total=t.Data.TotalCount,e.tbLoading=!1}))},fPartChange:function(e){this.showSearchBtn(),this.subTypeName="",this.cmptTypes_2=[],e&&this.getLitterPartChange(e)},getLitterPartChange:function(e){var t=this,a=this.cmptTypes_1.find((function(t){return t.Name===e}));(0,l.GetComponentTypeList)({Level:2,Parent_Id:a.Id}).then((function(e){e.IsSucceed&&(t.cmptTypes_2=e.Data)}))},getComponentType:function(){var e=this;(0,l.GetComponentTypeList)({Level:1}).then((function(t){t.IsSucceed&&(e.cmptTypes_1=t.Data)}))},getStatueInfo:function(e){switch(e){case 1:return"#EBF1F6";case 2:return"#91D5FF";case 3:return"#FAC414";case 4:return"#3ECC93"}}}}},"986e":function(e,t,a){"use strict";a.r(t);var n=a("3516"),s=a("ee6e");for(var c in s)["default"].indexOf(c)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(c);a("0cd9");var r=a("2877"),i=Object(r["a"])(s["default"],n["a"],n["b"],!1,null,"3c6db95c",null);t["default"]=i.exports},ee6e:function(e,t,a){"use strict";a.r(t);var n=a("6709b"),s=a.n(n);for(var c in n)["default"].indexOf(c)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(c);t["default"]=s.a}}]);