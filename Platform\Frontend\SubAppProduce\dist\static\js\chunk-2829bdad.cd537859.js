(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2829bdad"],{"0ec2":function(t,e,n){},2072:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[n("div",{staticClass:"sch-detail"},[n("div",{staticStyle:{padding:"12px 0"}},[n("el-card",{staticClass:"h-card",staticStyle:{position:"relative"},attrs:{shadow:"never"}},[n("div",{staticStyle:{position:"absolute",right:"16px",top:"16px","z-index":"999"}},[n("el-button",{attrs:{size:"mini"},on:{click:t.printMe}},[t._v("打印")]),n("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.exportMe}},[t._v("导出")])],1),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:6}},[n("header",{staticClass:"grid-content bg-purple"},[n("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),n("span",[t._v("打包件名称")])],1)]),n("el-col",{attrs:{span:6}},[n("label",[t._v("包编号: ")]),t._v(" "),n("span",[t._v(t._s(t.entity.Code))])]),3===t.entity.Stock_Status?n("el-col",{attrs:{span:6}},[n("label",[t._v("仓库: ")]),t._v(" "),n("span",[t._v(t._s(t.entity.Warehouse_Name))])]):t._e(),n("el-col",{attrs:{span:6}},[n("label",[t._v("打包时间: ")]),n("span",[t._v(t._s(t.formatDate(t.entity.In_Date,"yyyy-MM-dd")))])])],1),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:5,offset:1}},[n("label",[t._v("项目名称: ")]),n("span",[t._v(t._s(t.entity.Project_Name))])]),n("el-col",{attrs:{span:6}},[n("label",[t._v("包类型: ")]),n("span",[t._v(t._s(t.entity.Is_Component?"构件":"直发件"))])]),3===t.entity.Stock_Status?n("el-col",{attrs:{span:6}},[n("label",[t._v("库位: ")]),t._v(" "),n("span",[t._v(t._s(t.entity.Location_Name))])]):t._e(),n("el-col",{attrs:{span:6}},[n("label",[t._v("备注: ")]),t._v(" "),n("span",[t._v(t._s(t.entity.Remark))])])],1)],1)],1),n("div",{staticClass:"twrap"},[n("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""}},[n("template",{slot:"append"},[n("div",{staticClass:"tb-status"},[n("strong",[t._v("合计: ")]),n("span",[t._v(t._s(t.data.length)+" 个")]),n("span",[t._v(t._s(((t.data.length>0?t.data.map((function(t){return t.NetWeight})).reduce((function(t,e){return Number(t)+Number(e)})):0)/1e3).toFixed(2))+" 吨")])])])],2)],1)]),n("PrintDialog",{ref:"PrintDialog"})],1)},i=[]},"21fa":function(t,e,n){"use strict";n.r(e);var a=n("2072"),i=n("747c");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("2e16"),n("2e88");var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"6d975f8a",null);e["default"]=s.exports},"2e16":function(t,e,n){"use strict";n("ebc14")},"2e88":function(t,e,n){"use strict";n("0ec2")},"747c":function(t,e,n){"use strict";n.r(e);var a=n("d0e4"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"95d1":function(t,e,n){"use strict";n.r(e);var a=n("e4a3"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"9af6":function(t,e,n){"use strict";n("f1e3")},b3e6:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{title:"打印条码阅览",visible:t.dialogVisible,width:"100%","append-to-body":!0,fullscreen:!0,"custom-class":"print-preview-dialog",center:!0},on:{"update:visible":function(e){t.dialogVisible=e}}},[n("template",{slot:"title"},[n("span",{staticStyle:{"font-size":"1.6em",color:"#666"}},[t._v("打印条码阅览")])]),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{height:"100%",width:"100%"}},[n("iframe",{ref:"frame",attrs:{src:"/#/singles/print-preview",width:"100%",height:"100%",frameborder:"0"},on:{load:t.frameLoaded}})]),n("template",{slot:"footer"},[n("div",{staticClass:"print-tip"},[n("div",{staticClass:"tips"},[n("strong",[t._v("注意")]),t._v("：打印之前，请进行扫码确认二维码信息准确无误 ")]),n("el-button",{on:{click:t.printFrame}},[t._v("打印")])],1)])],2)},i=[]},d0e4:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("a15b"),n("d81d"),n("e9f5"),n("ab43"),n("d3b7"),n("25f0");var i=a(n("5530")),o=n("209b"),r=a(n("ed0f")),s=a(n("0f97")),l=n("6186"),c=n("6f23"),u=n("ed08");e.default={name:"PackingDetail",components:{DynamicDataTable:s.default,PrintDialog:r.default},data:function(){return{gridCode:"pro_packing_detail_list",fiterArrObj:{},tbConfig:{},columns:[],data:[],entity:{}}},computed:{row:function(){return this.$route.params.row}},created:function(){var t,e,n,a,r=this;(this.row&&(this.entity=(0,i.default)({},this.row)),null!==(t=this.$route.query)&&void 0!==t&&t.id||null!==(e=this.$route.query)&&void 0!==e&&e.code)&&(0,o.GetPackingEntity)({id:null===(n=this.$route.query)||void 0===n?void 0:n.id,code:null===(a=this.$route.query)||void 0===a?void 0:a.code}).then((function(t){t.IsSucceed&&(r.entity=Object.assign({},r.row,(0,i.default)({},t.Data)))})).then((function(){(0,o.GetPackingDetailList)({Id:r.entity.Id}).then((function(t){t.IsSucceed&&r.setGridData(t)}))}));(0,l.GetGridByCode)({Code:this.gridCode}).then((function(t){t.IsSucceed&&(r.setGrid(t.Data.Grid),r.setCols(t.Data.ColumnList))}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120})},setCols:function(t){this.columns=t.concat([])},setGridData:function(t){this.data=t.Data},tagBack:function(){(0,u.closeTagView)(this.$store,this.$route)},printMe:function(){this.$refs.PrintDialog.open(this.entity.Id)},exportMe:function(){var t=this;(0,o.ExportPackingInInfo)(this.entity.Id).then((function(e){if(e.IsSucceed){var n=(0,u.combineURL)(t.$baseUrl,e.Data.split("/").map((function(t){return encodeURIComponent(t.toString())})).join("/"));window.open(n,"_blank")}}))},formatDate:function(t,e){return(0,c.formatDate)(t,e)}}}},e4a3:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7");var i=a(n("5530")),o=n("6186");e.default={data:function(){return{dialogVisible:!1,keyValue:"",loading:!0}},methods:{open:function(t){this.dialogVisible=!0,this.keyValue=t},printFrame:function(){var t;null===(t=this.$refs.frame.contentDocument.getElementById("btn"))||void 0===t||t.click()},frameLoaded:function(){var t=this,e=[],n={},a=0,r=0;(0,o.GetReportDataTable)({templateName:"打包件清单打印",keyValue:this.keyValue,type:"main"}).then((function(t){if(0!==t.Data.length){e=t.Data;var o=(0,i.default)({},t.Data[0]),s=o.print_title,l=o.pack_code,c=o.create_date,u=o.create_username,d=o.project_name,f=o.reamrk;e.map((function(t){a+=t.num,r+=t.component_weight})),n={print_title:s,pack_code:l,create_date:c,create_username:u,project_name:d,reamrk:f,sumNum:a,sumWeight:r}}})),setTimeout((function(){var a,i;t.loading=!1,(null===(a=t.$refs.frame)||void 0===a||null===(a=a.contentWindow)||void 0===a?void 0:a.getPrintData)&&(null===(i=t.$refs.frame)||void 0===i||null===(i=i.contentWindow)||void 0===i||i.getPrintData({tmpl:"Packing",data:{tableData:e,otherInfo:n,entities:[]}}))}),500)}}}},ebc14:function(t,e,n){},ed0f:function(t,e,n){"use strict";n.r(e);var a=n("b3e6"),i=n("95d1");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("9af6");var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=s.exports},f1e3:function(t,e,n){}}]);