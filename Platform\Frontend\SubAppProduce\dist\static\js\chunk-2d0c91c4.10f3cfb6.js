(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2d0c91c4"],{"586a":function(t,o,e){"use strict";var n=e("4ea4").default;Object.defineProperty(o,"__esModule",{value:!0}),o.AddDelayed=B,o.AdjustComponentNum=G,o.AppendImportDeepFile=w,o.AppendImportDeepFiles=Q,o.BatchUpateCompProperty=W,o.BatchUpdateComponentImportInfo=nt,o.BatchUpdateComponentProcessInfo=ro,o.BatchUpdatePartProcessInfo=Po,o.BatchUpdateUnitProcessInfo=io,o.CancelScheduling=a,o.CancelSchedulingBase=d,o.CommitScheduling=h,o.CommitSchedulingBase=C,o.ComponentImportTemplate=j,o.<PERSON>=M,o.Delete<PERSON>llComponentWithQuery=_,o.DeleteComponents=K,o.DownAllFiles=q,o.EntityComponentQueryInfo=ct,o.ExportComponentInfo=tt,o.ExportComponentModelInfo=O,o.ExportComponentModelInfoByInstall=g,o.ExportComponentProcessInfo=po,o.ExportComponentSchedulingInfo=ot,o.ExportComponentSendOnTimeData=lt,o.ExportComponentTypeStock=Et,o.ExportDeepenFullSchedulingInfo=no,o.ExportFactoryOutputPageList=Ut,o.ExportFactorySchedulingPlan=Bt,o.ExportFactoryTeamYieldForDay=Wt,o.ExportGetFactoryTeamYieldMonth=Yt,o.ExportOutputAblityList=wt,o.ExportPartProcessInfo=Oo,o.ExportProducingKittingPageList=at,o.ExportProjectProgress=Lt,o.ExportProjectTraceCount=ht,o.ExportSupplyPlan=Jt,o.ExportThreeBom=vt,o.ExportToScheduleComponentInfo=c,o.ExportUnitProcessInfo=fo,o.FactoryOutput=qt,o.FactoryOutputPageList=xt,o.GenerateDeepenFileFromDirect=_t,o.GenerateDirectComponent=eo,o.GetComponentDeepenFileList=N,o.GetComponentEntityWithUniqueCode=D,o.GetComponentImportDetailPageList=J,o.GetComponentImportEntity=V,o.GetComponentLeadTime=T,o.GetComponentListByStatus=It,o.GetComponentLogList=F,o.GetComponentModelList=H,o.GetComponentProcessSummaryInfo=lo,o.GetComponentProductionDetailPageList=ft,o.GetComponentProductionSummaryInfo=st,o.GetComponentProductionTrack=to,o.GetComponentQueryInfo=it,o.GetComponentQueryPageInfo=dt,o.GetComponentSendOnTimeRageList=pt,o.GetComponentStockReport=mt,o.GetComponentStockStaticsListJson=L,o.GetComponentSummaryInfo=$,o.GetComponentTypeStockPageList=Dt,o.GetComponentYieldByStatus=Tt,o.GetDelayed=A,o.GetFactorySchdulingDayPlanYield=Ct,o.GetFactorySchedulingPlanPageList=kt,o.GetFactorySchedulingPlanSummaryInfo=At,o.GetFactoryTeamYieldForDay=Mt,o.GetFactoryTeamYieldForMonth=jt,o.GetImportHistoryPageList=v,o.GetInstallUnitCompPageList=k,o.GetPartDeepenFileList=Z,o.GetPartListWithComponent=X,o.GetPartProcessWeightList=go,o.GetProduceTraceCount=Ot,o.GetProducingKittingPageList=ut,o.GetProjectAreaProgressList=Ft,o.GetProjectAreaProgressSummary=St,o.GetProjectLifeCycleProgress=Gt,o.GetProjectProgressList=yt,o.GetProjectTraceCount=Pt,o.GetSchedulingComponentByInstallPageList=U,o.GetSchedulingComponentInstallPageList=E,o.GetSchedulingComponentPageList=f,o.GetSchedulingList=rt,o.GetSchedulingProcessingCompList=gt,o.GetSchedulingProcessingPartList=Rt,o.GetSteelCadAndBimId=Kt,o.GetSupplyPlanPageList=Qt,o.GetTrackList=oo,o.GetUnPreparedList=Ro,o.GetUnitProcessWeightList=so,o.GetWorkTeamOutput=zt,o.GetWorkTeamOutputV3=Zt,o.GetWorkingProcedureSummary=s,o.ImportComponentExtendInfo=Xt,o.ImportComponentModelInfo=et,o.ImportComponentWithModelInfo=y,o.ImportDeepFile=b,o.ImportModelInfo=R,o.ImportPartTechnologyFile=Co,o.ImportSchedulingInfo=i,o.ImportSchedulingInfoWithAlloct=m,o.ImportSteelTechnologyFile=ao,o.ImportUnitTechnologyFile=co,o.IsImportedComponentsWithoutModel=I,o.MonthlyFactoyOutput=Nt,o.OutputAblityList=bt,o.PartTechnologyImportTemplate=ho,o.ProcessOutput=Vt,o.ProductionDataTemplate=l,o.ProductionModelDataTemplate=S,o.ScheduleComponentAndAllocation=x,o.ScheduleComponentProductionDate=p,o.SteelTechnologyImportTemplate=uo,o.ThreeBomImportTemplate=Y,o.UnitTechnologyImportTemplate=mo,o.UpdateComponentPrinted=P,o.UpdatePartAggregateId=$t,o.UpdateSingleComponentImportInfo=z,o.YearlyFactoryOutput=Ht;var r=n(e("b775")),u=n(e("4328"));function a(t){return(0,r.default)({url:"/PRO/Component/CancelScheduling",method:"post",data:u.default.stringify(t)})}function d(t){return(0,r.default)({url:"/PRO/Component/CancelSchedulingBase",method:"post",data:u.default.stringify(t)})}function p(t){return(0,r.default)({url:"/PRO/Component/ScheduleComponentProductionDate",method:"post",data:u.default.stringify(t)})}function l(t){return(0,r.default)({url:"/PRO/Component/ProductionDataTemplate",method:"post",data:t})}function i(t){return(0,r.default)({url:"/PRO/Component/ImportSchedulingInfo",method:"post",data:t})}function m(t){return(0,r.default)({url:"/PRO/Component/ImportSchedulingInfoWithAlloct",method:"post",data:t})}function c(t){return(0,r.default)({url:"/PRO/Component/ExportToScheduleComponentInfo",method:"post",data:t})}function f(t){return(0,r.default)({url:"/PRO/Component/GetSchedulingComponentPageList",method:"post",data:t})}function s(t){return(0,r.default)({url:"/PRO/Component/GetWorkingProcedureSummary",method:"post",data:t})}function P(t){return(0,r.default)({url:"/PRO/Component/UpdateComponentPrinted",method:"post",data:u.default.stringify(t)})}function h(t){return(0,r.default)({url:"/PRO/Component/CommitScheduling",method:"post",data:u.default.stringify(t)})}function C(t){return(0,r.default)({url:"/PRO/Component/CommitSchedulingBase",method:"post",data:u.default.stringify(t)})}function O(t){return(0,r.default)({url:"/PRO/Component/ExportComponentModelInfo",method:"post",data:u.default.stringify(t)})}function g(t){return(0,r.default)({url:"/PRO/Component/ExportComponentModelInfoByInstall",method:"post",data:t})}function R(t){return(0,r.default)({url:"/PRO/Component/ImportModelInfo",method:"post",data:t})}function y(t){return(0,r.default)({url:"/PRO/Component/ImportComponentWithModelInfo",method:"post",data:t})}function I(t){return(0,r.default)({url:"/PRO/Component/IsImportedComponentsWithoutModel",method:"post",data:u.default.stringify(t)})}function G(t){return(0,r.default)({url:"/PRO/Component/AdjustComponentNum",method:"post",data:u.default.stringify(t)})}function S(){return(0,r.default)({url:"/PRO/Component/ProductionModelDataTemplate",method:"post"})}function T(t){return(0,r.default)({url:"/PRO/Component/GetComponentLeadTime",method:"post",data:u.default.stringify(t)})}function L(t){return(0,r.default)({url:"/PRO/Component/GetComponentStockStaticsListJson",method:"post",data:u.default.stringify(t)})}function F(t){return(0,r.default)({url:"/PRO/Component/GetComponentLogList",method:"post",data:u.default.stringify(t)})}function D(t){return(0,r.default)({url:"/PRO/Component/GetComponentEntityWithUniqueCode",method:"post",data:u.default.stringify(t)})}function E(t){return(0,r.default)({url:"/PRO/Component/GetSchedulingComponentInstallPageList",method:"post",data:t})}function x(t){return(0,r.default)({url:"/PRO/Component/ScheduleComponentAndAllocation",method:"post",data:t})}function U(t){return(0,r.default)({url:"/PRO/Component/GetSchedulingComponentByInstallPageList",method:"post",data:t})}function k(t){return(0,r.default)({url:"/PRO/Component/GetInstallUnitCompPageList",method:"post",data:t})}function A(t){return(0,r.default)({url:"/PRO/Component/GetDelayed",method:"post",data:t})}function B(t){return(0,r.default)({url:"/PRO/Component/AddDelayed",method:"post",data:t})}function M(t){return(0,r.default)({url:"/PRO/Component/DelDelayed",method:"post",data:t})}function W(t){return(0,r.default)({url:"/PRO/Component/BatchUpateCompProperty",method:"post",data:t})}function j(t){return(0,r.default)({url:"/PRO/Component/ComponentImportTemplate",method:"post",data:t})}function Y(t){return(0,r.default)({url:"/PRO/Component/ThreeBomImportTemplate",method:"post",data:t})}function b(t){return(0,r.default)({url:"/PRO/Component/ImportDeepFile",method:"post",data:t})}function w(t){return(0,r.default)({url:"/PRO/Component/AppendImportDeepFile",method:"post",data:t})}function Q(t){return(0,r.default)({url:"/PRO/Component/AppendImportDeepFiles",method:"post",data:t})}function J(t){return(0,r.default)({url:"/PRO/Component/GetComponentImportDetailPageList",method:"post",data:t})}function K(t){return(0,r.default)({url:"/PRO/Component/DeleteComponents",method:"post",data:t})}function _(t){return(0,r.default)({url:"/PRO/Component/DeleteAllComponentWithQuery",method:"post",data:t})}function v(t){return(0,r.default)({url:"/PRO/Component/GetImportHistoryPageList",method:"post",data:t})}function q(t){return(0,r.default)({url:"/PRO/Component/DownAllFiles",method:"post",data:t})}function H(t){return(0,r.default)({url:"/PRO/Component/GetComponentModelList",method:"post",data:t})}function N(t){return(0,r.default)({url:"/PRO/Component/GetComponentDeepenFileList",method:"post",data:t})}function V(t){return(0,r.default)({url:"/PRO/Component/GetComponentImportEntity",method:"post",data:t})}function z(t){return(0,r.default)({url:"/PRO/Component/UpdateSingleComponentImportInfo",method:"post",data:t})}function X(t){return(0,r.default)({url:"/PRO/Component/GetPartListWithComponent",method:"post",data:t})}function Z(t){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function $(t){return(0,r.default)({url:"/PRO/Component/GetComponentSummaryInfo",method:"post",data:t})}function tt(t){return(0,r.default)({url:"/PRO/Component/ExportComponentInfo",method:"post",data:t})}function ot(t){return(0,r.default)({url:"/PRO/Component/ExportComponentSchedulingInfo",method:"post",data:t})}function et(t){return(0,r.default)({url:"/PRO/Component/ImportComponentModelInfo",method:"post",data:t})}function nt(t){return(0,r.default)({url:"/PRO/Component/BatchUpdateComponentImportInfo",method:"post",data:t})}function rt(t){return(0,r.default)({url:"/PRO/Component/GetSchedulingList",method:"post",data:t})}function ut(t){return(0,r.default)({url:"/PRO/Component/GetProducingKittingPageList",method:"post",data:t})}function at(t){return(0,r.default)({url:"/PRO/Component/ExportProducingKittingPageList",method:"post",data:t})}function dt(t){return(0,r.default)({url:"/PRO/Component/GetComponentQueryPageInfo",method:"post",data:t})}function pt(t){return(0,r.default)({url:"/PRO/Component/GetComponentSendOnTimeRageList",method:"post",data:t})}function lt(t){return(0,r.default)({url:"/PRO/Component/ExportComponentSendOnTimeData",method:"post",data:t})}function it(t){return(0,r.default)({url:"/PRO/Component/GetComponentQueryInfo",method:"post",data:t})}function mt(t){return(0,r.default)({url:"/PRO/Component/GetComponentStockReport",method:"post",data:t})}function ct(t){return(0,r.default)({url:"/PRO/Component/EntityComponentQueryInfo",method:"post",data:t})}function ft(t){return(0,r.default)({url:"/PRO/Component/GetComponentProductionDetailPageList",method:"post",data:t})}function st(t){return(0,r.default)({url:"/PRO/Component/GetComponentProductionSummaryInfo",method:"post",data:t})}function Pt(t){return(0,r.default)({url:"/PRO/Component/GetProjectTraceCount",method:"post",data:t})}function ht(t){return(0,r.default)({url:"/PRO/Component/ExportProjectTraceCount",method:"post",data:t})}function Ct(t){return(0,r.default)({url:"/PRO/Component/GetFactorySchdulingDayPlanYield",method:"post",data:t})}function Ot(t){return(0,r.default)({url:"/PRO/Component/GetProduceTraceCount",method:"post",data:t})}function gt(t){return(0,r.default)({url:"/PRO/Component/GetSchedulingProcessingCompList",method:"post",data:t})}function Rt(t){return(0,r.default)({url:"/PRO/Component/GetSchedulingProcessingPartList",method:"post",data:t})}function yt(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectProgressList",method:"post",data:t})}function It(t){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentListByStatus",method:"post",data:t})}function Gt(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectLifeCycleProgress",method:"post",data:t})}function St(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressSummary",method:"post",data:t})}function Tt(t){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentYieldByStatus",method:"post",data:t})}function Lt(t){return(0,r.default)({url:"/PRO/ProductionCount/ExportProjectProgress",method:"post",data:t})}function Ft(t){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressList",method:"post",data:t})}function Dt(t){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentTypeStockPageList",method:"post",data:t})}function Et(t){return(0,r.default)({url:"/PRO/ProductionCount/ExportComponentTypeStock",method:"post",data:t})}function xt(t){return(0,r.default)({url:"/PRO/ProductionTask/FactoryOutputPageList",method:"post",data:t})}function Ut(t){return(0,r.default)({url:"/PRO/ProductionTask/ExportFactoryOutputPageList",method:"post",data:t})}function kt(t){return(0,r.default)({url:"/PRO/ProductionTask/GetFactorySchedulingPlanPageList",method:"post",data:t})}function At(t){return(0,r.default)({url:"/PRO/ProductionTask/GetFactorySchedulingPlanSummaryInfo",method:"post",data:t})}function Bt(t){return(0,r.default)({url:"/PRO/ProductionTask/ExportFactorySchedulingPlanPageList",method:"post",data:t})}function Mt(t){return(0,r.default)({url:"/PRO/ProductionTask/GetFactoryTeamYieldForDay",method:"post",data:t})}function Wt(t){return(0,r.default)({url:"/PRO/ProductionTask/ExportGetFactoryTeamYieldForDay",method:"post",data:t})}function jt(t){return(0,r.default)({url:"/PRO/ProductionTask/GetFactoryTeamYieldForMonth",method:"post",data:t})}function Yt(t){return(0,r.default)({url:"/PRO/ProductionTask/ExportGetFactoryTeamYieldForMonth",method:"post",data:t})}function bt(t){return(0,r.default)({url:"/PRO/ProductionTask/OutputAblityList",method:"post",data:t})}function wt(t){return(0,r.default)({url:"/PRO/ProductionTask/ExportOutputAblityList",method:"post",data:t})}function Qt(t){return(0,r.default)({url:"/Pro/IntegrationSupplyPlan/GetSupplyPlanPageList",method:"post",data:t})}function Jt(t){return(0,r.default)({url:"/Pro/IntegrationSupplyPlan/ExportSupplyPlan",method:"post",data:t})}function Kt(t){return(0,r.default)({url:"/PRO/Component/GetSteelCadAndBimId",method:"post",data:t})}function _t(t){return(0,r.default)({url:"/PRO/Component/GenerateDeepenFileFromDirect",method:"post",data:t})}function vt(t){return(0,r.default)({url:"/PRO/Component/ExportThreeBom",method:"post",data:t})}function qt(t){return(0,r.default)({url:"/PRO/ProductionCount/FactoryOutput",method:"post",data:t})}function Ht(t){return(0,r.default)({url:"/PRO/ProductionCount/YearlyFactoryOutput",method:"post",data:t})}function Nt(t){return(0,r.default)({url:"/PRO/ProductionCount/MonthlyFactoyOutput",method:"post",data:t})}function Vt(t){return(0,r.default)({url:"/PRO/ProductionCount/ProcessOutput",method:"post",data:t})}function zt(t){return(0,r.default)({url:"/PRO/ProductionCount/GetWorkTeamOutput",method:"post",data:t})}function Xt(t){return(0,r.default)({url:"/PRO/component/ImportComponentExtendInfo",method:"post",data:t})}function Zt(t){return(0,r.default)({url:"/PRO/ProductionCount/GetWorkTeamOutputV3",method:"post",data:t})}function $t(t){return(0,r.default)({url:"/PRO/Part/UpdatePartAggregateId",method:"post",data:t})}function to(t){return(0,r.default)({url:"/PRO/ProductionReport/GetComponentProductionTrack",method:"post",data:t})}function oo(t){return(0,r.default)({url:"/PRO/Part/GetTrackList",method:"post",data:t})}function eo(t){return(0,r.default)({url:"/PRO/change/GenerateDirectComponent",method:"post",data:t})}function no(t){return(0,r.default)({url:"/PRO/component/ExportDeepenFullSchedulingInfo",method:"post",data:t})}function ro(t){return(0,r.default)({url:"/PRO/Component/BatchUpdateComponentProcessInfo",method:"post",data:t})}function uo(t){return(0,r.default)({url:"/PRO/Component/SteelTechnologyImportTemplate",method:"post",data:t})}function ao(t){return(0,r.default)({url:"/PRO/Component/ImportSteelTechnologyFile",method:"post",data:t})}function po(t){return(0,r.default)({url:"/PRO/Component/ExportComponentProcessInfo",method:"post",data:t})}function lo(t){return(0,r.default)({url:"/PRO/Component/GetComponentProcessSummaryInfo",method:"post",data:t})}function io(t){return(0,r.default)({url:"/PRO/Unit/BatchUpdateUnitProcessInfo",method:"post",data:t})}function mo(t){return(0,r.default)({url:"/PRO/Unit/UnitTechnologyImportTemplate",method:"post",data:t})}function co(t){return(0,r.default)({url:"/PRO/Unit/ImportUnitTechnologyFile",method:"post",data:t})}function fo(t){return(0,r.default)({url:"/PRO/Unit/ExportUnitProcessInfo",method:"post",data:t})}function so(t){return(0,r.default)({url:"/PRO/Unit/GetUnitProcessWeightList",method:"post",data:t})}function Po(t){return(0,r.default)({url:"/PRO/Part/BatchUpdatePartProcessInfo",method:"post",data:t})}function ho(t){return(0,r.default)({url:"/PRO/Part/PartTechnologyImportTemplate",method:"post",data:t})}function Co(t){return(0,r.default)({url:"/PRO/Part/ImportPartTechnologyFile",method:"post",data:t})}function Oo(t){return(0,r.default)({url:"/PRO/Part/ExportPartProcessInfo",method:"post",data:t})}function go(t){return(0,r.default)({url:"/PRO/Part/GetPartProcessWeightList",method:"post",data:t})}function Ro(t){return(0,r.default)({url:"/PRO/Component/GetUnPreparedList",method:"post",data:t})}}}]);