(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-afed0720"],{"322e":function(t,e,o){"use strict";o("bac6")},4263:function(t,e,o){"use strict";o.r(e);var n=o("dbbb"),a=o("6232");for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);o("322e");var u=o("2877"),d=Object(u["a"])(a["default"],n["a"],n["b"],!1,null,"49654dfa",null);e["default"]=d.exports},"4d55":function(t,e,o){"use strict";var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("4de4"),o("d81d"),o("e9f5"),o("910d"),o("ab43"),o("b680"),o("d3b7"),o("3ca3"),o("ddb0");var a=n(o("34e9")),r=n(o("c1df")),u=o("5e99"),d=o("6b87");o("8975"),e.default={name:"PROMonthReport",components:{Header:a.default},data:function(){return{form:{countMonth:"",factoryId:""},Columns:[],calendarTime:"",factoryName:"",tableData:[],options:[],Weight:0}},created:function(){var t=this;this.form.countMonth=(0,r.default)().format("YYYY-M"),this.calendarTime=(0,r.default)().format("YYYY年MM月"),Promise.all([(0,u.GetFactoryList)({}).then((function(e){e.IsSucceed?(t.options=e.Data,t.form.factoryId=e.Data[0].Id,t.factoryName=e.Data[0].Name):t.$message({type:"error",message:e.Message})}))]).then((function(){(0,d.GetComponentCountByMonth)(t.form).then((function(e){if(e.IsSucceed){t.tableData=e.Data.list,t.Columns=e.Data.Columns;var o=0;t.tableData.map((function(t){o+=t["合计"]})),t.Weight=o.toFixed(2)}else t.$message({type:"error",message:e.Message})}))}))},methods:{fetchData:function(){var t=this;this.calendarTime=(0,r.default)(this.form.countMonth).format("YYYY年MM月"),this.factoryName=this.options.filter((function(e){if(e.Id===t.form.factoryId)return e}))[0].Name,(0,d.GetComponentCountByMonth)(this.form).then((function(e){if(e.IsSucceed){t.tableData=e.Data.list,t.Columns=e.Data.Columns;var o=0;t.tableData.map((function(t){o+=t["合计"]})),t.Weight=o.toFixed(2)}else t.$message({type:"error",message:e.Message})}))},handleExport:function(){var t=this,e=this.Columns,n=this.Columns,a=this.formatJson(n,this.tableData);o.e("chunk-2d0cc0b6").then(o.t.bind(null,"4bf8",7)).then((function(o){o.export_json_to_excel({header:e,data:a,filename:"".concat(t.calendarTime," 生产月报"),autoWidth:!0,bookType:"xlsx"})}))},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},fixed:function(t){return("项目"===t||"合计"===t)&&"left"}}}},6232:function(t,e,o){"use strict";o.r(e);var n=o("4d55"),a=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"6b87":function(t,e,o){"use strict";var n=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetComponentCountByMonth=l,e.GetComponentInfoPageList=m,e.GetComponentNoProduceDetailPageList=d,e.GetComponentPageList=f,e.GetComponentProducedByDays=r,e.GetComponentProducedDay=c,e.GetFactoryComponentYield=P,e.GetFactorySchdulingPlanYield=y,e.GetFactoryTeamYield=h,e.GetFactoryTeamYieldForDay=p,e.GetInstallUnitProducedCount=u,e.GetProducedDetailPageList=i,e.GetTeamProducedCountByDate=s;var a=n(o("b775"));n(o("4328"));function r(t){return(0,a.default)({url:"/PRO/ProductionCount/GetComponentProducedByDays",method:"post",data:t})}function u(t){return(0,a.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/ProductionCount/GetComponentNoProduceDetailPageList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/PRO/ProductionCount/GetProducedDetailPageList",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/ProductionCount/GetComponentProducedDay",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/ProductionCount/GetComponentCountByMonth",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/ProductionCount/GetTeamProducedCountByDate",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/Component/GetComponentPageList",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/Component/GetComponentInfoPageList",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/ProductionCount/GetFactoryTeamYieldForDay",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/ProductionCount/GetFactoryTeamYield",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/ProductionCount/GetFactoryComponentYield",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/Component/GetFactorySchdulingPlanYield",method:"post",data:t})}},bac6:function(t,e,o){},dbbb:function(t,e,o){"use strict";o.d(e,"a",(function(){return n})),o.d(e,"b",(function(){return a}));var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("el-card",{staticClass:"box-card"},[o("Header",{attrs:{padding:"0"},scopedSlots:t._u([{key:"left",fn:function(){return[o("el-form",{attrs:{"label-width":"40px",inline:!0}},[t._e(),o("el-form-item",{attrs:{label:"日期"}},[o("el-date-picker",{attrs:{type:"month",clearable:!1,"value-format":"yyyy-M",placeholder:"选择日期"},on:{change:t.fetchData},model:{value:t.form.countMonth,callback:function(e){t.$set(t.form,"countMonth",e)},expression:"form.countMonth"}})],1)],1)]},proxy:!0},{key:"right",fn:function(){return[o("el-button",{attrs:{type:"success"},on:{click:t.handleExport}},[t._v("导出")])]},proxy:!0}])}),o("h4",{staticStyle:{"text-align":"center"}},[t._v(" "+t._s(t.calendarTime)+" 生产月报 ")]),o("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"620",border:""}},t._l(t.Columns,(function(e,n){return o("el-table-column",{key:n,attrs:{prop:e,"show-overflow-tooltip":"",label:e,fixed:t.fixed(e)}})})),1),o("div",{staticClass:"sum"},[t._v("本月完成合计："+t._s((t.Weight/1e3).toFixed(2))+" 吨；")])],1)],1)},a=[]}}]);