(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5ca2d19e"],{"08d0":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var i=n(a("5530")),o=n(a("c14f")),r=n(a("1da1")),c=n(a("7c56")),s=a("9643"),d=n(a("0f97")),f=n(a("34e9")),u=a("ed08"),l=a("fd31"),g=n(a("5cc7"));t.default={components:{TitleInfo:c.default,TopHeader:f.default,DynamicDataTable:d.default},mixins:[g.default],data:function(){return{isEdit:!0,Is_Pack:!1,isClicked:!1,width:"40%",btnLoading:!1,currentComponent:"",title:"",dialogVisible:!1,form:{SendInfoID:"",PageInfo:{Page:1,PageSize:20}},plm_ProjectSendingInfo:{},produced_Components:[],projectSendingInfo_Item:[],Itemdetail:[],PackagesList:[],ProfessionalType:[],carOptions:[],projects:"",Id:"",projectId:"",planTime:"",showDialog:!1,tbConfig:{Pager_Align:"center"},columns:[],tbData:[],tbData2:[],total:0,tbLoading:!1,selectList:[],sums:[]}},computed:{},created:function(){this.form.SendInfoID=this.$route.query.id,this.getFactoryTypeOption()},methods:{getFactoryTypeOption:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_component_out_change_record_list,".concat(e.ProfessionalType[0].Code));case 2:e.fetchData();case 3:return t.a(2)}}),t)})))()},toBack:function(){(0,u.closeTagView)(this.$store,this.$route)},fetchData:function(){var e=this;this.tbLoading=!0,(0,s.GetProjectSendingInfoLogPagelist)((0,i.default)({},this.form)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data.map((function(e){return e.SendDate=e.SendDate?(0,u.parseTime)(new Date(e.SendDate),"{y}-{m}-{d} {h}:{i}:{s}"):e.SendDate,e.Create_Date=e.Create_Date?(0,u.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Create_Date,e})),e.total=t.Data.TotalCount,e.tbLoading=!1):e.$message({message:t.Message,type:"error"})}))},multiSelectedChange:function(e){this.selectList=e},close:function(){this.dialogVisible=!1},handleInfo:function(e){this.$refs.info.handleOpen(e)}}}},"1b65":function(e,t,a){"use strict";a.r(t);var n=a("95fd"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},6038:function(e,t,a){"use strict";a.r(t);var n=a("fe3b"),i=a("be5ec");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("7a92");var r=a("2877"),c=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"5391b5ce",null);t["default"]=c.exports},"7a92":function(e,t,a){"use strict";a("ac4a")},"7c56":function(e,t,a){"use strict";a.r(t);var n=a("abe7"),i=a("1b65");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("f5f7");var r=a("2877"),c=Object(r["a"])(i["default"],n["a"],n["b"],!1,null,"3eff9b38",null);t["default"]=c.exports},"95fd":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{title:{type:String,default:""}},data:function(){return{}}}},abe7:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"title-box"},[a("div",{staticClass:"title"},[e._v(" "+e._s(e.title)+" ")]),a("div",[e._t("default")],2)])},i=[]},ac4a:function(e,t,a){},b46e:function(e,t,a){},be5ec:function(e,t,a){"use strict";a.r(t);var n=a("08d0"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},f5f7:function(e,t,a){"use strict";a("b46e")},fe3b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{staticStyle:{padding:"0"},scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header"},[a("el-button",{on:{click:e.toBack}},[e._v("返回")])],1)]},proxy:!0}])}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.form.PageInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange}})],1)],1)])},i=[]}}]);