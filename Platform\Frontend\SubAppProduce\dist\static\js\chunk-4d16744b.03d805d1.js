(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4d16744b"],{"31f3":function(n,e,t){"use strict";t.r(e);var u=t("3350"),r=t("bba8");for(var a in r)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(a);var o=t("2877"),f=Object(o["a"])(r["default"],u["a"],u["b"],!1,null,"007417fc",null);e["default"]=f.exports},3350:function(n,e,t){"use strict";t.d(e,"a",(function(){return u})),t.d(e,"b",(function(){return r}));var u=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("home")},r=[]},"58f4":function(n,e,t){"use strict";var u=t("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=u(t("6f86"));e.default={name:"PROAuxiliaryMaterialAllocation",components:{home:r.default},data:function(){return{}},provide:{Type:1},created:function(){},mounted:function(){},methods:{}}},bba8:function(n,e,t){"use strict";t.r(e);var u=t("58f4"),r=t.n(u);for(var a in u)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(a);e["default"]=r.a}}]);