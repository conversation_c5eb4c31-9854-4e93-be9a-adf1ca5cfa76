(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-20aa4a33"],{37412:function(e,t,o){"use strict";o.d(t,"a",(function(){return a})),o.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[o("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"cs-z-page-main-content"},[o("SearchHeader",{attrs:{padding:"0"},scopedSlots:e._u([{key:"left",fn:function(){return[o("el-form",{ref:"searchForm",attrs:{model:e.searchForm,"label-width":"80px",inline:!0}},[o("el-form-item",{attrs:{label:"年份",prop:"year"}},[o("el-date-picker",{attrs:{type:"year","value-format":"yyyy",clearable:!1,placeholder:"选择年","picker-options":e.pickerOptions},model:{value:e.searchForm.year,callback:function(t){e.$set(e.searchForm,"year",t)},expression:"searchForm.year"}})],1),o("el-form-item",{attrs:{label:"班组",prop:"teamId"}},[o("el-select",{attrs:{placeholder:"请选择..."},model:{value:e.searchForm.teamId,callback:function(t){e.$set(e.searchForm,"teamId",t)},expression:"searchForm.teamId"}},[[o("el-option",{attrs:{label:"全部",value:""}}),e._l(e.workingTeamsList,(function(e){return o("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})}))]],2)],1),o("el-form-item",[o("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),o("el-button",{on:{click:function(t){e.$refs["searchForm"].resetFields(),e.handleSearch()}}},[e._v("重置")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[o("el-button",{attrs:{type:"success"},on:{click:e.handleExport}},[e._v("导出")])]},proxy:!0}])}),o("div",{staticClass:"tb-wrapper"},[o("vxe-table",{ref:"vxeTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",stripe:"",align:"left",height:"100%",resizable:"","empty-text":"暂无数据","show-footer":"","footer-cell-class-name":e.footerCellClassName,"footer-method":e.footerMethod,data:e.tableData}},e._l(e.tableColumn,(function(e){return o("vxe-column",{key:e.key,attrs:{type:e.type,field:e.field,title:e.title,fixed:e.fixed,"min-width":e.minWidth,width:e.width,"sort-type":e.sortType,sortable:e.sortable,filters:e.filters}})})),1),o("vxe-pager",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{border:"",align:"center","current-page":e.tablePage.currentPage,"page-size":e.tablePage.pageSize,"page-sizes":e.tablePage.pageSizes,total:e.tablePage.totalResult,layouts:["Total","Sizes","PrevJump","PrevPage","Number","NextPage","NextJump","FullJump"]},on:{"update:currentPage":function(t){return e.$set(e.tablePage,"currentPage",t)},"update:current-page":function(t){return e.$set(e.tablePage,"currentPage",t)},"update:pageSize":function(t){return e.$set(e.tablePage,"pageSize",t)},"update:page-size":function(t){return e.$set(e.tablePage,"pageSize",t)},"update:pageSizes":function(t){return e.$set(e.tablePage,"pageSizes",t)},"update:page-sizes":function(t){return e.$set(e.tablePage,"pageSizes",t)},"page-change":e.handlePageChange}})],1)],1)])},n=[]},"6b87":function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetComponentCountByMonth=d,t.GetComponentInfoPageList=m,t.GetComponentNoProduceDetailPageList=i,t.GetComponentPageList=f,t.GetComponentProducedByDays=r,t.GetComponentProducedDay=l,t.GetFactoryComponentYield=g,t.GetFactorySchdulingPlanYield=P,t.GetFactoryTeamYield=p,t.GetFactoryTeamYieldForDay=h,t.GetInstallUnitProducedCount=u,t.GetProducedDetailPageList=s,t.GetTeamProducedCountByDate=c;var n=a(o("b775"));a(o("4328"));function r(e){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentProducedByDays",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/ProductionCount/GetInstallUnitProducedCount",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentNoProduceDetailPageList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/ProductionCount/GetProducedDetailPageList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentProducedDay",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/ProductionCount/GetComponentCountByMonth",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/ProductionCount/GetTeamProducedCountByDate",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Component/GetComponentPageList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Component/GetComponentInfoPageList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/ProductionCount/GetFactoryTeamYieldForDay",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/ProductionCount/GetFactoryTeamYield",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/ProductionCount/GetFactoryComponentYield",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Component/GetFactorySchdulingPlanYield",method:"post",data:e})}},7283:function(e,t,o){"use strict";o.r(t);var a=o("37412"),n=o("95e4");for(var r in n)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(r);o("c9db");var u=o("2877"),i=Object(u["a"])(n["default"],a["a"],a["b"],!1,null,"5636b470",null);t["default"]=i.exports},"95e4":function(e,t,o){"use strict";o.r(t);var a=o("df39"),n=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},a024:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=l,t.AddProessLib=I,t.AddTechnology=s,t.AddWorkingProcess=i,t.DelLib=z,t.DeleteProcess=v,t.DeleteProcessFlow=L,t.DeleteTechnology=G,t.DeleteWorkingTeams=k,t.GetAllProcessList=f,t.GetCheckGroupList=x,t.GetChildComponentTypeList=_,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=W,t.GetFactoryWorkingTeam=P,t.GetGroupItemsList=T,t.GetLibList=u,t.GetLibListType=B,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=p,t.GetProcessList=d,t.GetProcessListBase=c,t.GetProcessListTeamBase=D,t.GetProcessListWithUserBase=S,t.GetProcessWorkingTeamBase=N,t.GetTeamListByUser=$,t.GetTeamProcessList=b,t.GetWorkingTeam=y,t.GetWorkingTeamBase=w,t.GetWorkingTeamInfo=F,t.GetWorkingTeams=C,t.GetWorkingTeamsPageList=O,t.SaveWorkingTeams=R,t.UpdateProcessTeam=g;var n=a(o("b775")),r=a(o("4328"));function u(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function s(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function l(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function m(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function p(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function P(){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function b(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function T(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function L(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function G(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function v(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function w(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function D(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function S(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function z(e){return(0,n.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function B(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},c9db:function(e,t,o){"use strict";o("f19a")},df39:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(o("c14f")),r=a(o("1da1"));o("99af"),o("d81d"),o("14d9"),o("a434"),o("e9f5"),o("7d54"),o("ab43"),o("e9c4"),o("a9e3"),o("b64b"),o("d3b7"),o("25f0"),o("3ca3"),o("159b"),o("ddb0");var u=a(o("34e9")),i=o("a024"),s=o("6b87"),l=o("fd31"),d=(a(o("c1df")),o("c685"));t.default={name:"PROGroupMonthOutputReport",components:{SearchHeader:u.default},data:function(){return{pickerOptions:{disabledDate:function(e){var t=(new Date).getFullYear();return e.getTime()>new Date(t,11,31).getTime()}},searchForm:{year:"",teamId:""},workingTeamsList:[],workingTeamsCount:0,tableColumn:[],tableData:[],sumsJson:{},tablePage:{currentPage:1,pageSize:10,pageSizes:d.tablePageSize,totalResult:300},Unit:"",Professional_Code:"",loading:!1}},created:function(){var e=(new Date).getFullYear();this.searchForm.year=e.toString(),this.getGetWorkingTeamsList(),this.getTypeList()},mounted:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.getDataList();case 1:return t.a(2)}}),t)})))()},methods:{handleSearch:function(){this.loading=!0,this.tableColumn=[],this.tableData=[],this.getDataList()},getTypeList:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){var o,a;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:o=t.v,a=o.Data,o.IsSucceed?(e.Professional_Code=a[0].Code,e.Unit=a[0].Unit):e.$message({message:o.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getGetWorkingTeamsList:function(){var e=this;(0,i.GetWorkingTeams)().then((function(t){t.IsSucceed?(e.workingTeamsList=t.Data,e.workingTeamsCount=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})}))},getDataList:function(){var e=this;(0,s.GetFactoryTeamYield)({Factory_Id:localStorage.getItem("CurReferenceId"),Year:this.searchForm.year,Team_Id:this.searchForm.teamId}).then((function(t){if(t.IsSucceed){e.tableData=t.Data;var o=t.Data[0],a=1;for(var n in o){var r={};r.key=a++,r.field=n,r.title="班组名称"==n?n:"".concat(n,"（").concat(e.Unit,"）"),"班组名称"==n?(r.minWidth="200",r.fixed="left"):"平均月产量"==n||"合计产量"==n?(r.minWidth="160",r.sortType="number",r.sortable=!0,r.fixed="right"):(r.minWidth="140",r.sortType="number",r.sortable=!0),e.tableColumn.push(r)}e.loading=!1}else e.$message({type:"error",message:t.Message})}))},handleExport:function(){var e=JSON.parse(JSON.stringify(this.tableData)),t=e[0],a=[],n=[];for(var r in t)"班组名称"==r?a.push(r):a.push(r+"("+this.Unit+")"),n.push(r);a.splice(a.length-1),n.splice(n.length-1),e.push(this.sumsJson);var u=this.formatJson(n,e);o.e("chunk-2d0cc0b6").then(o.t.bind(null,"4bf8",7)).then((function(e){e.export_json_to_excel({header:a,data:u,filename:"".concat(localStorage.getItem("ProjectName")," ~ 班组月产量报表"),autoWidth:!0,bookType:"xlsx"})}))},formatJson:function(e,t){return t.map((function(t){return e.map((function(e){return t[e]}))}))},footerCellClassName:function(e){var t=e.$rowIndex;e.column,e.columnIndex;if(0===t)return"col-footer"},footerMethod:function(e){var t=this,o=e.columns,a=e.data,n=[],r={};return o.forEach((function(e,o){if(0===o)r[e.field]="合计：",n.push("合计：");else{var u=null;u=t.sumNum(a,e.property),r[e.field]=u,n.push(u)}})),this.sumsJson=r,[n]},sumNum:function(e,t){var o=0;return e.forEach((function(e){o+=Number(e[t]?e[t]:0)})),Math.round(1e3*parseFloat(o))/1e3},handlePageChange:function(e){var t=e.currentPage,o=e.pageSize;this.tablePage.currentPage=t,this.tablePage.pageSize=o}}}},f19a:function(e,t,o){}}]);