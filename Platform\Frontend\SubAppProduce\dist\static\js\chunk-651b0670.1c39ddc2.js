(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-651b0670"],{"0741":function(e,t,n){},"10d2":function(e,t,n){"use strict";n.r(t);var a=n("72e5"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},"15ac":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");t.default={methods:{getTableConfig:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,a.GetGridByCode)({code:e,IsAll:n}).then((function(e){var a=e.IsSucceed,r=e.Data,s=e.Message;if(a){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});var u=[];t.tbConfig=Object.assign({},t.tbConfig,r.Grid),u=n?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=u.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+r.Grid.Row_Number||o.tablePageSize[0]),i(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit,a=e.type;this.queryInfo.Page="limit"===a?1:t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===t){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"2e8a":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=d,t.GetCompTypeTree=c,t.GetComponentTypeEntity=l,t.GetComponentTypeList=r,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=m,t.GetTypePageList=s,t.RestoreTemplateType=v,t.SavDeepenTemplateSetting=b,t.SaveCompTypeIdentifySetting=y,t.SaveComponentType=u,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=p;var o=a(n("b775")),i=a(n("4328"));function r(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:i.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:i.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:i.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function y(e){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function b(e){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function v(e){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"2fc2":function(e,t,n){"use strict";n("d08e")},"4e82":function(e,t,n){"use strict";var a=n("23e7"),o=n("e330"),i=n("59ed"),r=n("7b0b"),s=n("07fa"),u=n("083a"),l=n("577e"),d=n("d039"),c=n("addb"),f=n("a640"),m=n("3f7e"),p=n("99f4"),h=n("1212"),g=n("ea83"),y=[],b=o(y.sort),v=o(y.push),C=d((function(){y.sort(void 0)})),T=d((function(){y.sort(null)})),_=f("sort"),P=!d((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var e,t,n,a,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)y.push({k:t+a,v:n})}for(y.sort((function(e,t){return t.v-e.v})),a=0;a<y.length;a++)t=y[a].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),S=C||!T||!_||!P,I=function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:l(t)>l(n)?1:-1}};a({target:"Array",proto:!0,forced:S},{sort:function(e){void 0!==e&&i(e);var t=r(this);if(P)return void 0===e?b(t):b(t,e);var n,a,o=[],l=s(t);for(a=0;a<l;a++)a in t&&v(o,t[a]);c(o,I(e)),n=s(o),a=0;while(a<n)t[a]=o[a++];while(a<l)u(t,a++);return t}})},5628:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[n("div",{staticClass:"cs-z-page-main-content"},[n("div",{staticClass:"tex-right"},[n("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新 增")])],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff  cs-z-tb-wrapper"},[n("dynamic-data-table",{ref:"dyTable",attrs:{columns:e.columns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{tableSearch:e.tableSearch,gridSizeChange:e.handlePageChange,gridPageChange:e.handlePageChange},scopedSlots:e._u([{key:"op",fn:function(t){var a=t.row;t.index;return[n("el-button",{staticClass:"txt-red",attrs:{type:"text"},on:{click:function(t){return e.handleDelete(a)}}},[e._v("删除")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")])]}}])})],1),e.dialogVisible?n("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e()],1)])},o=[]},"6c7c":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n("c14f")),i=a(n("1da1")),r=a(n("15ac")),s=a(n("0f97")),u=a(n("817f")),l=n("2e8a");t.default={name:"PROBasicComType",components:{DynamicDataTable:s.default,AddEdit:u.default},mixins:[r.default],data:function(){return{tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],tbData:[],total:0,tbLoading:!1,title:"",dialogVisible:!1,currentComponent:""}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("pro_component_type_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,l.GetTypePageList)(this.queryInfo).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.tbLoading=!1):e.$message({message:t.Message,type:"error"})}))},handleAdd:function(){this.currentComponent="AddEdit",this.title="新增",this.dialogVisible=!0},handleEdit:function(e){var t=this;this.currentComponent="AddEdit",this.title="编辑",this.dialogVisible=!0,this.$nextTick((function(n){t.$refs["content"].EditInit(e)}))},handleDelete:function(e){var t=this;this.$confirm("请确认是否删除所选项?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,l.DeleteComponentType)({ids:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleClose:function(){this.dialogVisible=!1}}}},"72e5":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("14d9"),n("a434");var o=a(n("c14f")),i=a(n("1da1")),r=n("2e8a"),s=n("cf45");t.default={data:function(){return{title:"",btnLoading:!1,form:{Category:"",Code:"",Lead_Time:void 0,Name:"",subList:[]},comType:[],rules:{Code:[{required:!0,message:"请输入",trigger:"blur"}],Name:[{required:!0,message:"请输入",trigger:"blur"}],Category:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){this.init()},methods:{init:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getDictionary)("component_category");case 1:e.comType=t.v;case 2:return t.a(2)}}),t)})))()},EditInit:function(e){var t=this;(0,r.GetComponentTypeEntity)({id:e.Id}).then((function(e){if(e.IsSucceed){var n=e.Data,a=n.Category,o=n.Code,i=n.Lead_Time,r=n.Name,s=n.subList,u=n.Id;t.form.Category=a,t.form.Code=o,t.form.Lead_Time=i,t.form.Name=r,t.form.subList=s,t.form.Id=u}else t.$message({message:e.Message,type:"error"})}))},handleOpen:function(e){},handleClose:function(){this.$emit("close")},handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.btnLoading=!0,(0,r.SaveComponentType)(t.form).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.$emit("close"),t.$emit("refresh")):t.$message({message:e.Message,type:"error"})})),t.btnLoading=!1}))},handleAddItem:function(){this.form.subList.push({Lead_Time:void 0,Name:"",Code:""})},handleDeleteItem:function(e){this.form.subList.splice(e,1)}}}},"817f":function(e,t,n){"use strict";n.r(t);var a=n("95d0"),o=n("10d2");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("b064");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"0e8065f1",null);t["default"]=s.exports},"95d0":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return o}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"构件类别",prop:"Category"}},[n("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Category,callback:function(t){e.$set(e.form,"Category",t)},expression:"form.Category"}},e._l(e.comType,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Display_Name}})})),1)],1),n("el-form-item",{attrs:{label:"编号",prop:"Code"}},[n("el-input",{model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"生产周期",prop:"Lead_Time"}},[n("el-input",{attrs:{placeholder:"请输入内容"},model:{value:e.form.Lead_Time,callback:function(t){e.$set(e.form,"Lead_Time",e._n(t))},expression:"form.Lead_Time"}},[n("template",{slot:"append"},[e._v("天")])],2)],1),n("el-form-item",{attrs:{label:"构件大类名称",prop:"Name"}},[n("el-input",{model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),n("el-form-item",{attrs:{label:"构件小类"}},[e._l(e.form.subList,(function(t,a){return n("div",{key:a,staticClass:"cs-inline"},[n("el-input",{staticClass:"mr-16",attrs:{placeholder:"编号"},model:{value:t.Code,callback:function(n){e.$set(t,"Code",n)},expression:"item.Code"}}),n("el-input",{staticClass:"mr-16",attrs:{placeholder:"构件小类名称"},model:{value:t.Name,callback:function(n){e.$set(t,"Name",n)},expression:"item.Name"}}),n("el-input",{staticClass:"mr-8",attrs:{placeholder:"天数"},model:{value:t.Lead_Time,callback:function(n){e.$set(t,"Lead_Time",e._n(n))},expression:"item.Lead_Time"}}),n("i",{staticClass:"el-icon-remove-outline pointer txt-red",on:{click:function(t){return e.handleDeleteItem(a)}}})],1)})),n("div",{staticClass:"btn-d",on:{click:e.handleAddItem}},[e._v("+新增构件小类")])],2),n("el-form-item",{staticStyle:{"text-align":"center"}},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},o=[]},a351:function(e,t,n){"use strict";n.r(t);var a=n("5628"),o=n("b2d0");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("2fc2");var r=n("2877"),s=Object(r["a"])(o["default"],a["a"],a["b"],!1,null,"fcbe8500",null);t["default"]=s.exports},b064:function(e,t,n){"use strict";n("0741")},b2d0:function(e,t,n){"use strict";n.r(t);var a=n("6c7c"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},cf45:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=o,n("d3b7");var a=n("6186");function o(e){return new Promise((function(t,n){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},d08e:function(e,t,n){}}]);