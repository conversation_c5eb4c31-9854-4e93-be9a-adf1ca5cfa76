(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-509cb544"],{"0ce7":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1"));a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var o=r(a("9b15")),s=a("5c96"),u=a("21c4"),l=a("6186"),c=function(){(0,l.SecurityToken)().then((function(e){sessionStorage.setItem("ossToken",JSON.stringify(e.Data))}))};c(),setInterval((function(){c()}),114e4);t.default={name:"OSSUpload",mixins:[s.Upload],props:{ossOnSuccess:{type:Function,default:Function},piecesize:{type:Number,default:2},httpRequest:{type:Function,default:function(e){var t=this,a=JSON.parse(sessionStorage.getItem("ossToken"));this.$nextTick((function(){var r,s=null!==(r=t.data)&&void 0!==r&&r.piecesize?1*t.data.piecesize:2,c=new o.default({region:"oss-"+a.regionId,secure:!0,accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken,bucket:a.bucket,refreshSTSToken:function(){var e=(0,i.default)((0,n.default)().m((function e(){var a;return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.securityToken();case 1:return a=e.v,e.a(2,{accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken})}}),e)})));function a(){return e.apply(this,arguments)}return a}(),refreshSTSTokenInterval:9e5}),d=e.file,f=new Date;c.multipartUpload((0,u.getTenantId)()+"/"+f.getFullYear()+"/"+(1*f.getMonth()+1)+"/"+f.getDate()+"/"+f.getMinutes()+"_"+f.getSeconds()+"_"+f.getMilliseconds()+"/"+d.name,d,{progress:function(t,a){this.process=a,e.onProgress({percent:Math.floor(100*t)})},parallel:4,partSize:1048576*s,meta:{}}).then((function(t){if(200===t.res.statusCode){var a,r=t.res.requestUrls[0]&&t.res.requestUrls[0].split("?")[0];!1===(null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.callback)?e.onSuccess({Data:r+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name}):(0,l.GetOssUrl)({url:r}).then((function(t){e.onSuccess({Data:r+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name,encryptionUrl:t.Data})}))}}),(function(t){e.onError(t)}))}))}}},data:function(){return{process:null}},watch:{process:function(e){this.$emit("getprocess",e)}},mounted:function(){},methods:{handleFn:function(e){},securityToken:function(){return new Promise((function(e,t){(0,l.SecurityToken)({}).then((function(t){e(t.Data)})).catch((function(e){t(e)}))}))}}}},2008:function(e,t,a){},2245:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ActiveAuxMaterial=O,t.ActiveRawMaterial=g,t.DeleteAuxMaterial=D,t.DeleteMaterialCategory=d,t.DeleteMaterials=l,t.DeleteRawMaterial=v,t.DeleteWarehouseReceipt=I,t.ExportPurchaseDetail=U,t.GetAuxMaterialEntity=S,t.GetAuxMaterialPageList=b,t.GetAuxStandardsList=M,t.GetAuxWarehouseReceiptEntity=k,t.GetMaterialCategoryList=c,t.GetMaterialImportPageList=o,t.GetPurchaseDetail=$,t.GetPurchaseDetailList=E,t.GetRawMaterialEntity=p,t.GetRawMaterialPageList=m,t.GetRawStandardsList=x,t.GetRawWarehouseReceiptEntity=P,t.GetWarehouseReceiptPageList=R,t.ImportMatAux=_,t.ImportMatAuxRcpt=W,t.ImportMatRaw=w,t.ImportMatRawRcpt=A,t.ImportMaterial=u,t.MaterialDataTemplate=s,t.SaveAuxMaterialEntity=C,t.SaveAuxWarehouseReceipt=L,t.SaveMaterialCategory=f,t.SaveRawMaterialEntity=h,t.SaveRawWarehouseReceipt=T,t.SubmitWarehouseReceipt=N,t.TemplateDownload=y;var n=r(a("b775")),i=r(a("4328"));function o(e){return(0,n.default)({url:"/PRO/Material/GetMaterialImportPageList",method:"post",data:e})}function s(){return(0,n.default)({url:"/PRO/Material/MaterialDataTemplate",method:"post"})}function u(e){return(0,n.default)({url:"/PRO/Material/ImportMaterial",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Material/DeleteMaterials",method:"post",data:i.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/Material/GetMaterialCategoryList",method:"post",params:e})}function d(e){return(0,n.default)({url:"/PRO/Material/DeleteMaterialCategory",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Material/SaveMaterialCategory",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Material/SaveRawMaterialEntity",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Material/GetRawMaterialEntity",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Material/GetRawMaterialPageList",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Material/ActiveRawMaterial",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Material/DeleteRawMaterial",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialPageList",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Material/GetAuxMaterialEntity",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Material/TemplateDownload",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/Material/ImportMatRaw",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/Material/ImportMatAux",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/Material/SaveAuxMaterialEntity",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/Material/ActiveAuxMaterial",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Material/DeleteAuxMaterial",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Material/GetWarehouseReceiptPageList",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/Material/GetRawStandardsList",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Material/SaveRawWarehouseReceipt",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Material/DeleteWarehouseReceipt",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Material/GetRawWarehouseReceiptEntity",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/Material/SubmitWarehouseReceipt",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Material/GetAuxWarehouseReceiptEntity",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/Material/SaveAuxWarehouseReceipt",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/Material/GetAuxStandardsList",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/Material/ImportMatRawRcpt",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/Material/ImportMatAuxRcpt",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetail",method:"post",data:e})}function U(e){return(0,n.default)({url:"/PRO/Material/ExportPurchaseDetail",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/Material/GetPurchaseDetailList",method:"post",data:e})}},2325:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("点击此处下载导入模板")])],1),a("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},n=[]},2559:function(e,t,a){"use strict";a.r(t);var r=a("cc58"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},"2e2e":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],a("el-col",{attrs:{span:2}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?a("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[a("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(0===r[t.Code]?"按需使用":1===r[t.Code]?"使用标准规格":2===r[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},n=[]},3610:function(e,t,a){"use strict";a.r(t);var r=a("2e2e"),n=a("8585");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("b35e9");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"01b3f3e2",null);t["default"]=s.exports},3973:function(e,t,a){},"4cbe":function(e,t,a){},"4ec9":function(e,t,a){"use strict";a("6f48")},"52d8":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),i=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("7db0"),a("c740"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7");var s=a("9002"),u=a("3c4a"),l=a("ed08"),c=r(a("bad9")),d=r(a("d9e9"));t.default={components:{SelectExternal:d.default,SelectProject:c.default},props:{formData:{type:Object,default:function(){}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{RawName:"",Spec:"",Material:"",Supplier:"",SupplierName:"",PartyUnit:"",PartyUnitName:"",Raw_Property:"",SysProjectId:"",InStoreNo:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],currentColumns:[],tbConfig:{},multipleSelection:[],originalData:[]}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.Store_Sub_Id===t.Store_Sub_Id}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig();var e=this.formData,t=e.Supplier,a=e.PartyUnit;(t||a)&&(this.form.Supplier=t,this.form.PartyUnit=a)},methods:{getConfig:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("pro_add_aux_material_outbound_detail_list");case 1:e.columns=t.v,e.columnsOption(),e.fetchData();case 2:return t.a(2)}}),t)})))()},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a,r,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:a=e.formData.OutStoreType,e.currentColumns=JSON.parse(JSON.stringify(e.columns)),4===a?(r=e.currentColumns.findIndex((function(e){return"PartyUnitName"===e.Code})),-1!==r&&e.currentColumns.splice(r,1)):2===a&&(n=e.currentColumns.findIndex((function(e){return"SupplierName"===e.Code})),-1!==n&&e.currentColumns.splice(n,1));case 1:return t.a(2)}}),t)})))()},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,u.FindStoreDetail)((0,n.default)((0,n.default)({},e.form),{},{OutStoreType:e.formData.OutStoreType})).then((function(t){t.IsSucceed?(e.originalData=t.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e}))),this.$emit("close")},addToList:function(){var e=(0,l.deepClone)(this.multipleSelection);this.$emit("getAddList",e.map((function(e){return e.PurchaseCount=e.OutStoreCount,e}))),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleClose:function(){this.$emit("close")}}}},"68b0":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"90px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"入库单号",prop:"InStoreNo"}},[a("el-input",{model:{value:e.form.InStoreNo,callback:function(t){e.$set(e.form,"InStoreNo",t)},expression:"form.InStoreNo"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"辅料名称",prop:"RawName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商/甲方",prop:"Supplier"}},[a("SelectExternal",{model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.currentColumns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["AvailableCount"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(0===r[t.Code]?"0":r[t.Code]||"-")+" ")]}}:"InStoreDate"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]}}:"Warehouse_Location"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.WarehouseName)+"/"+e._s(a.LocationName)+" ")]}}:"TaxUnitPrice"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||0===r[t.Code]?r[t.Code].toFixed(2):"-")+" ")]}}:"RawName"===t.Code?{key:"default",fn:function(t){var r=t.row;return[a("div",[r.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),r.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),e._v(" "+e._s(r.RawName))],1)]}}:"OutStoreCount"===t.Code?{key:"default",fn:function(r){var n=r.row;return[a("vxe-input",{attrs:{min:0,max:n.AvailableCount,type:"number"},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,e._n(a))},expression:"row[item.Code]"}})]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(r[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},n=[]},"6f48":function(e,t,a){"use strict";var r=a("6d61"),n=a("6566");r("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n)},"72f6":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n=r(a("3796")),i=a("2245"),o=a("ed08");t.default={components:{Upload:n.default},props:{pageType:{type:Number,default:void 0}},data:function(){return{btnLoading:!1,schdulingPlanId:""}},methods:{getTemplate:function(){var e=this;(0,i.TemplateDownload)({templateType:0===this.pageType?"YLRK":"FLRK"}).then((function(t){window.open((0,o.combineURL)(e.$baseUrl,t.Data))}))},beforeUpload:function(e){var t,a=this,r=new FormData;r.append("files",e),this.btnLoading=!0,t=0===this.pageType?i.ImportMatRawRcpt:i.ImportMatAuxRcpt,t(r).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"导入成功"}),a.$emit("importData",e.Data),a.$emit("close")):a.$message({type:"error",message:e.Message}),a.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},"73d2":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("2909")),i=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7");var s=a("ed08"),u=a("3c4a"),l=a("90d1");t.default={props:{isView:{type:Boolean,default:!1},bigTypeData:{type:Number,default:1},OutStoreType:{type:Number,default:1},isReplacePurchase:{type:Boolean,default:!1},checkTypeList:{type:Array,required:!0,default:function(){return[]}},projectList:{type:Array,required:!0,default:function(){return[]}},supplierList:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{tbLoading:!1,multipleSelection:[],rootColumns:[],columns:[],tbData:[],originalData:[],BigType:1,renderComponent:!0,num:1}},watch:{"tbData.length":{handler:function(e,t){this.tbData.map((function(e,t){return e.index=t,e}))},immediate:!0}},created:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.$route.query.InStoreNo&&e.getDetail();case 1:return t.a(2)}}),t)})))()},mounted:function(){},methods:{getDetail:function(){var e=this;(0,u.FindStoreDetail)({OutStoreType:4,InStoreNo:this.$route.query.InStoreNo}).then((function(t){e.addData(t.Data)}))},formatTime:function(e){return(0,s.parseTime)(new Date(e),"{y}-{m}-{d}")},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},init:function(e){this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption()},columnsOption:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var a,r,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:a=e.BigType,e.columns=JSON.parse(JSON.stringify(e.rootColumns)),4===e.OutStoreType?(r=e.columns.findIndex((function(e){return"PartyUnitName"===e.Code})),-1!==r&&e.columns.splice(r,1),e.getCheckList(a,e.columns)):2===e.OutStoreType&&(n=e.columns.findIndex((function(e){return"SupplierName"===e.Code})),-1!==n&&e.columns.splice(n,1),e.getCheckList(a,e.columns));case 1:return t.a(2)}}),t)})))()},getCheckList:function(e,t){this.checkTypeList.map((function(a){a.BigType===e&&(a.checkList=t.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),a.checkList=a.checkList.map((function(e){return e.Code})),a.checkSameList=t.filter((function(e){return e.Is_Edit&&e.Is_Display&&"OutStoreCount"!==e.Code&&"OutStoreWeight"!==e.Code&&"Pound_Weight"!==e.Code&&-1===e.Code.indexOf("Remark")})),a.checkSameList=a.checkSameList.map((function(e){return e.Code})))}))},tbfetchData:function(){this.tbData=[]},handleCopy:function(e,t){var a=this;return(0,o.default)((0,i.default)().m((function r(){return(0,i.default)().w((function(r){while(1)switch(r.n){case 0:a.tbData.splice(t+1,0,JSON.parse(JSON.stringify(e)));case 1:return r.a(2)}}),r)})))()},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,a=this,r=e.map((function(e){return e.Actual_Thick=e.Thick,a.checkWeight(e),e}));(t=this.tbData).push.apply(t,(0,n.default)(r))},checkWeight:function(e){Number(e.OutStoreCount)>0||(e.OutStoreCount=e.AvailableCount),1!==e.BigType&&2!==e.BigType||0!==e.Specific_Gravity&&0!==e.Length?1===e.BigType?0===e.Thick||0===e.Width?e.OutStoreWeightKG=0:e.Thick&&e.Width&&e.Length&&e.Specific_Gravity?e.OutStoreWeightKG=Number((e.Thick*e.Width*e.Length*e.Specific_Gravity*e.OutStoreCount).toFixed(2)):e.AvailableCount&&e.AvailableWeight?e.OutStoreWeightKG=Number((e.AvailableWeight/e.AvailableCount*e.OutStoreCount).toFixed(2)):e.OutStoreWeightKG="":2===e.BigType?e.Length&&e.Specific_Gravity?e.OutStoreWeightKG=Number((e.Length*e.Specific_Gravity*e.OutStoreCount).toFixed(2)):e.AvailableCount&&e.AvailableWeight?e.OutStoreWeightKG=Number((e.AvailableWeight/e.AvailableCount*e.OutStoreCount).toFixed(2)):e.OutStoreWeightKG="":3===e.BigType&&e.AvailableCount&&e.AvailableWeight?e.OutStoreWeightKG=Number((e.AvailableWeight/e.AvailableCount*e.OutStoreCount).toFixed(2)):e.OutStoreWeightKG="":e.OutStoreWeightKG=0,e.OutStoreWeight=e.OutStoreWeightKG||0===e.OutStoreWeightKG?Number((e.OutStoreWeightKG/1e3).toFixed(3)):"",this.$emit("updateRow")},blurWeight:function(e,t){"OutStoreWeight"===t?e.OutStoreWeightKG=e.OutStoreWeight||0===e.OutStoreWeight?Number((1e3*e.OutStoreWeight).toFixed(2)):"":"Pound_Weight"===t&&(e.Pound_Weight_KG=e.Pound_Weight||0===e.Pound_Weight?Number((1e3*e.Pound_Weight).toFixed(2)):""),this.$emit("updateRow")},checkCount:function(e){(e.OutStoreCount||0===e.OutStoreCount)&&e.OutStoreCount>e.AvailableCount&&(e.OutStoreCount=e.AvailableCount,this.$emit("updateRow"))},footerMethod:function(e){var t=this,a=e.columns,r=e.data,n=[a.map((function(e,a){return["AvailableCount","NoTaxAllPrice","OutStoreCount"].includes(e.field)?t.sumNum(r,e.field,l.DETAIL_TOTAL_PRICE_DECIMAL):0===a?"合计":null}))];return n},sumNum:function(e,t,a){for(var r=0,n=0;n<e.length;n++)r+=Number(e[n][t])||0;return r.toFixed(a)/1}}}},7485:function(e,t,a){"use strict";a.r(t);var r=a("c3fe"),n=a("b293");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("a204");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"4c6bee07",null);t["default"]=s.exports},8585:function(e,t,a){"use strict";a.r(t);var r=a("d681"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},9002:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTableConfig=void 0,a("d3b7");var r=a("6186"),n=void 0;t.getTableConfig=function(e){return new Promise((function(t,a){(0,r.GetGridByCode)({code:e}).then((function(e){var a=e.IsSucceed,r=e.Data,i=e.Message;if(a){var o=r.ColumnList||[];t(o)}else n.$message({message:i,type:"error"})}))}))}},"90d1":function(e,t){e.exports={WEIGHT_DECIMAL:5,INBOUND_DETAIL_UNIT_PRICE_DECIMAL:6,DETAIL_TOTAL_PRICE_DECIMAL:2,COUNT_DECIMAL:2,UNIT_WEIGHT_DECIMAL:100,TAX_MODE:0,SUMMARY_FIELDS:["Theory_Weight","InStoreWeight","Voucher_Weight","InStoreCount","TaxTotalPrice","NoTaxTotalPrice","TaxPrice"],INBOUND_DETAIL_HIDE_FIELDS:{isPurchase:["PartyUnitName"],isCustomer:["PurchaseNo","SupplierName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"],isManual:["PurchaseNo","PartyUnitName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"]},INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS:["SupplierName","ProjectName","Material","Tax_Rate","NoTaxUnitPrice","TaxUnitPrice"],INBOUND_DETAIL_SUMMARY_FIELDS:["InStoreCount","InStoreWeight","Voucher_Weight","NoTaxAllPrice","Tax_All_Price","Adjust_Amount","Tax","Theory_Weight"],OutBOUND_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","AvailableCount","AvailableWeight","NoTaxAllPrice","Tax_All_Price","Out_Store_Weight","Out_Store_Count","Returned_Weight","Returned_Count","InStoreCount","InStoreWeight"],Return_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","NoTaxAllPrice","Tax_All_Price","AvailableCount","AvailableWeight","Voucher_Weight"]}},"9adf":function(e,t,a){"use strict";a.r(t);var r=a("2325"),n=a("d852");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("bd95");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"4a50d31e",null);t["default"]=s.exports},"9d7e2":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.FindPickingNewSum=o,t.FindReceivingNewSum=s,t.GetAuxCostReport=u,t.GetAuxCostReportSummary=l,t.GetListForContractSetting=i,t.GetListForContractSettingForMateriel=c;var n=r(a("b775"));function i(e){return(0,n.default)({url:"/SYS/ExternalCompany/GetListForContractSetting",method:"post",data:e})}function o(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindPickingNewSum",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/MaterielFlow/FindReceivingNewSum",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/MaterielReport/GetAuxCostReport",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/MaterielReport/GetAuxCostReportSummary",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/MaterielRawInStore/GetListForContractSettingForMateriel",method:"post",data:e})}},a204:function(e,t,a){"use strict";a("b162")},a5d76:function(e,t,a){"use strict";a("ea44")},a933:function(e,t,a){"use strict";a.r(t);var r=a("68b0"),n=a("dffa8");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("e4ba");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"c9bb976c",null);t["default"]=s.exports},b162:function(e,t,a){},b293:function(e,t,a){"use strict";a.r(t);var r=a("debe"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},b35e9:function(e,t,a){"use strict";a("4cbe")},bd95:function(e,t,a){"use strict";a("3973")},c017:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[2===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"非标规格",name:"1"}},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"80px"}},e._l(e.list,(function(t){return a("el-form-item",{key:t,attrs:{label:t}},[a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:e.form[t],callback:function(a){e.$set(e.form,t,a)},expression:"form[item]"}})],1)})),1)],1):e._e(),1===e.specificationUsage||0===e.specificationUsage?a("el-tab-pane",{attrs:{label:"标准规格",name:"2"}},[a("el-table",{ref:"multipleTable",staticClass:"cs-custom-table",staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.$index},nativeOn:{change:function(a){return a.stopPropagation(),function(a){return e.handleRadioChange(a,t.row)}(a)}},model:{value:e.radioSelect,callback:function(t){e.radioSelect=t},expression:"radioSelect"}})]}}],null,!1,3152109164)}),a("el-table-column",{attrs:{align:"center",prop:"StandardDesc",label:"规格"}})],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},n=[]},c3fe:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card",style:e.isRetract?"height: 110px; overflow: hidden;":""},[a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"10px","padding-bottom":"10","border-bottom":"1px solid #D0D3DB"}},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("退货单信息")]),a("div",{staticClass:"retract-container",on:{click:e.handleRetract}},[a("el-button",{attrs:{type:"text"}},[e._v(e._s(e.isRetract?"展开":"收起"))]),a("el-button",{attrs:{type:"text",icon:e.isRetract?"el-icon-arrow-down":"el-icon-arrow-up"}})],1)]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退货类型",prop:"OutStoreType"}},[a("SelectMaterialStoreType",{attrs:{disabled:e.isView||e.isEdit,type:"AuxReturnStoreType"},on:{change:e.typeChange},model:{value:e.form.OutStoreType,callback:function(t){e.$set(e.form,"OutStoreType",t)},expression:"form.OutStoreType"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退货日期",prop:"OutStoreDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView,"value-format":"yyyy-MM-dd",type:"date","picker-options":e.pickerOptions},model:{value:e.form.OutStoreDate,callback:function(t){e.$set(e.form,"OutStoreDate",t)},expression:"form.OutStoreDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView,rows:1,"show-word-limit":"",maxlength:100,placeholder:"备注",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:5,multiple:!0,"on-success":function(t,a,r){e.uploadSuccess(t,a,r)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"show-file-list":!0,"file-list":e.fileListData,disabled:e.isView}},[e.isView?e._e():a("el-button",{attrs:{type:"primary",disabled:e.isView}},[e._v("上传文件")])],1)],1)],1)],1)],1)],1),a("el-card",{staticClass:"box-card box-card-tb"},[a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-title"},[a("span"),e._v("退货单明细")])]),a("el-divider",{staticClass:"elDivder"}),a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"0px"}},[a("el-form",{ref:"searchForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm}},[e.isView?e._e():[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],a("el-form-item",{staticStyle:{"margin-left":"10px"},attrs:{label:"辅料名称",prop:"RawName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.SysProjectId,callback:function(t){e.$set(e.searchForm,"SysProjectId",t)},expression:"searchForm.SysProjectId"}},e._l(e.ProjectList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")]),a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.getTableColumns}})],2)],1),a("el-divider",{staticClass:"elDivder"}),a("div",{staticClass:"tb-x"},[a(e.currentTbComponent,{ref:"table",tag:"component",attrs:{"is-view":e.isView,"out-store-type":e.form.OutStoreType,"big-type-data":e.BigType,"check-type-list":e.checkTypeList,"project-list":e.ProjectList,"supplier-list":e.SupplierList,"party-unit-list":e.PartyUnitList},on:{changeStandard:e.changeStandard,changeWarehouse:e.changeWarehouse,select:e.setSelectRow,updateRow:e.handleUpdateRow}})],1),e.isView?e._e():a("el-divider",{staticClass:"elDivder"}),e.isView?e._e():a("footer",[a("div",{staticClass:"data-info"},[a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("div",[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{loading:e.saveLoading},on:{click:function(t){return e.saveDraft(1)}}},[e._v("保存草稿 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("提交退货")])],1)])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":0},on:{close:e.handleClose,warehouse:e.getWarehouse,batchEditor:e.batchEditorFn,importData:e.importData,standard:e.getStandard,refresh:e.fetchData}})],1):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增",visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[a("AddList2",{ref:"draft",attrs:{"form-data":e.form,joinedItems:e.rootTableData},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2)],1)},n=[]},c7ab:function(e,t,a){"use strict";a.r(t);var r=a("f68a");for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);var i,o,s=a("2877"),u=Object(s["a"])(r["default"],i,o,!1,null,null,null);t["default"]=u.exports},cc58:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b"),a("14d9"),a("13d5"),a("e9f5"),a("9485"),a("d3b7");var r=a("2245");t.default={data:function(){return{activeName:"1",radioSelect:null,btnLoading:!1,form:{},list:[],tableData:[],specificationUsage:0}},watch:{specificationUsage:function(e,t){1===e&&(this.activeName="2")}},methods:{getOption:function(e){var t=this;this.specificationUsage=e.SpecificationUsage,e&&(this.list=e.RcptRawParams.split("*")),(0,r.GetRawStandardsList)({rawId:e.RawId}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleRadioChange:function(e,t){e.stopPropagation(),this.currentRow=Object.assign({},t)},submit:function(){var e=this;if("1"===this.activeName){var t=!0,a=this.list.reduce((function(a,r){if(e.form[r])return a.push(e.form[r]),a;t=!1}),[]);if(!t)return void this.$message({message:"输入数据不能为0",type:"warning"});this.$emit("standard",{type:1,val:a.join("*")})}else{if(!this.currentRow)return void this.$message({message:"请选择规格",type:"warning"});this.$emit("standard",{type:2,val:this.currentRow})}this.handleClose()},handleClose:function(){this.$emit("close")}}}},d022:function(e,t,a){"use strict";a.r(t);var r=a("73d2"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},d681:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("c14f")),i=r(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("841c");var o=a("ed08"),s=a("9002"),u=a("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,i.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,u.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},d852:function(e,t,a){"use strict";a.r(t);var r=a("72f6"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},db38:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.renderComponent?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"show-footer":"","footer-method":e.footerMethod},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(r){var n=r.row;return["Warehouse_Location"===t.Code?a("span",[e._v(" "+e._s(n.WarehouseName)+"/"+e._s(n.LocationName)+" ")]):"InStoreDate"===t.Code?a("span",[e._v(" "+e._s(n.InStoreDate?e.formatTime(n.InStoreDate):"-")+" ")]):"RawName"===t.Code?[a("div",[n.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),n.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),n.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(n.RawName))],1)]:"TaxUnitPrice"===t.Code?a("span",[e._v(" "+e._s(n[t.Code]||0===n[t.Code]?n[t.Code].toFixed(2):"-")+" ")]):a("span",[e._v(e._s(n[t.Code]||0==n[t.Code]?n[t.Code]:t.Is_Edit?"":"-"))])]}},t.Is_Edit?{key:"edit",fn:function(r){var n=r.row;return["OutStoreCount"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{blur:function(t){return e.checkWeight(n)},"prev-number":function(t){return e.checkWeight(n)},"next-number":function(t){return e.checkWeight(n)},change:function(t){return e.checkCount(n)}},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):a("div",[a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:n[t.Code],callback:function(a){e.$set(n,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},n=[]},debe:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a("5530")),i=r(a("2909")),o=r(a("c14f")),s=r(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("c740"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("4ec9"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("3ca3"),a("841c"),a("159b"),a("ddb0");var u=a("ed08"),l=r(a("f8f2")),c=r(a("3610")),d=r(a("a933")),f=r(a("9adf")),h=r(a("fb1a")),p=a("3166"),m=r(a("c7ab")),g=a("6186"),v=a("5480"),b=r(a("d7a3")),S=a("93aa"),y=a("cf45"),w=a("3c4a"),_=a("e144"),C=r(a("a657")),O=r(a("5d4b"));t.default={components:{SelectMaterialStoreType:O.default,DynamicTableFields:C.default,AddList2:d.default,ReceiveTb:l.default,ImportFile:f.default,Standard:h.default,AddList:c.default,OSSUpload:m.default},mixins:[b.default],props:{pageType:{type:Number,default:void 0}},data:function(){var e=this;return{isRetract:!1,tbLoading:!1,currentTbComponent:l.default,ProjectList:[],PartyUnitList:[],SupplierList:[],multipleSelection:[],form:{OutStoreNo:"",OutStoreType:+this.$route.query.OutStoreType||4,OutStoreDate:this.getDate(),Supplier:"",PartyUnit:"",Remark:"",Attachment:""},rules:{OutStoreType:[{required:!0,message:"请选择",trigger:"change"}],OutStoreDate:[{required:!0,message:"请选择",trigger:"change"}],PartyUnit:[{required:!0,message:"请选择",trigger:"change"}]},currentComponent:"",title:"",dWidth:"60%",saveLoading:!1,search:function(){return{}},openAddList:!1,dialogVisible:!1,BigType:1,fileListData:[],fileListArr:[],searchNum:1,rootTableData:[],tableData:[],typeNumber1:0,typeNumber2:0,typeNumber3:0,typeNumber4:0,backendDate:null,pickerOptions:{disabledDate:function(t){return t.getTime()<new Date(e.backendDate).getTime()}},searchForm:{RawName:"",Thick:"",Spec:"",Material:"",SysProjectId:""},rootColumns:[],RawGoodsReturnTypeList:[],checkTypeList:[{BigType:1,checkList:[],checkSameList:[]},{BigType:2,checkList:[],checkSameList:[]},{BigType:3,checkList:[],checkSameList:[]},{BigType:99,checkList:[],checkSameList:[]}],gridCode:"PROAuxProcurementOutList"}},computed:{isAdd:function(){return 1===this.pageType},isEdit:function(){return 2===this.pageType},isView:function(){return 3===this.pageType},isSelfProcurement:function(){return 4==this.form.OutStoreType},isCustomer:function(){return 2==this.form.OutStoreType}},created:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getOMALatestStatisticTime();case 1:return t.n=2,e.getCurFactory();case 2:return t.n=3,e.getBaseData();case 3:return t.n=4,e.getTableColumns();case 4:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.search=(0,u.debounce)(e.fetchData,800,!0),e.isAdd||e.getInfo();case 1:return t.a(2)}}),t)})))()},methods:{handleRetract:function(){this.isRetract=!this.isRetract},radioChange:function(e){this.BigType=e,this.multipleSelection=[],this.handleReset()},handleSearch:function(){if(this.searchNum++,1===this.searchNum){var e=this.$refs.table.tbData;this.rootTableData=JSON.parse(JSON.stringify(e))}if(this.tableData=JSON.parse(JSON.stringify(this.rootTableData)),this.searchForm.RawName){var t=new RegExp(this.searchForm.RawName,"i");this.tableData=this.tableData.filter((function(e){return t.test(e.RawName)}))}if(this.searchForm.Spec){var a=new RegExp(this.searchForm.Spec,"i");this.tableData=this.tableData.filter((function(e){return a.test(e.Spec)}))}if(this.searchForm.Material){var r=new RegExp(this.searchForm.Material,"i");this.tableData=this.tableData.filter((function(e){return r.test(e.Material)}))}if(this.searchForm.SysProjectId){var n=new RegExp(this.searchForm.SysProjectId,"i");this.tableData=this.tableData.filter((function(e){return n.test(e.Sys_Project_Id)}))}this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.tableData))},handleReset:function(){this.searchForm.RawName="",this.searchForm.Spec="",this.searchForm.Material="",this.searchForm.SysProjectId="",this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.rootTableData)),this.tableData=JSON.parse(JSON.stringify(this.rootTableData))},getOMALatestStatisticTime:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,S.GetOMALatestStatisticTime)({});case 1:a=t.v,a.IsSucceed?e.backendDate=a.Data||"":e.message.error(a.Mesaage);case 2:return t.a(2)}}),t)})))()},uploadSuccess:function(e,t,a){this.fileListArr=JSON.parse(JSON.stringify(a))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},handlePreview:function(e){return(0,s.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(a="",!e.response||!e.response.encryptionUrl){t.n=1;break}a=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,g.GetOssUrl)({url:e.encryptionUrl});case 2:a=t.v,a=a.Data;case 3:window.open(a);case 4:return t.a(2)}}),t)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})},getInfo:function(){var e=this;this.form.OutStoreNo=this.$route.query.OutStoreNo,this.form.OutStoreType=+this.$route.query.OutStoreType;var t=4==this.form.OutStoreType?w.SelfAuxReturnOutStoreDetail:w.PartyAAuxOutStoreDetail;t({outStoreNo:this.form.OutStoreNo}).then((function(t){if(t.IsSucceed){var a=t.Data.Receipt,r=t.Data.Sub.map((function(e){return e})),n=a.OutStoreDate,i=a.Remark,o=a.Sys_Project_Id,s=a.Pick_Department_Id,u=a.ReceiveUserId,l=a.Use_Processing_Id,c=a.Attachment,d=a.Status;e.form.OutStoreDate=e.getDate(new Date(n)),e.form.Remark=i,e.form.SysProjectId=o,e.form.Pick_Department_Id=s,e.form.ReceiveUserId=u,e.form.Use_Processing_Id=l,e.form.Status=d,e.form.Pick_Department_Id&&e.departmentChange();var f=r.map((function(e,t){return e.index=(0,_.v4)(),e.Warehouse_Location=e.WarehouseName?e.WarehouseName+"/"+e.LocationName:"",e.OutStoreWeightKG=e.OutStoreWeight,e.OutStoreWeight=Number((e.OutStoreWeightKG/1e3).toFixed(3)),e}));if(e.$nextTick((function(t){e.$refs["table"].tbData=JSON.parse(JSON.stringify(f)),e.tableData=JSON.parse(JSON.stringify(f)),e.rootTableData=JSON.parse(JSON.stringify(f)),e.$refs["table"].originalData=f})),c){e.form.Attachment=c;var h=c.split(",");h.forEach((function(t){var a=t.indexOf("?Expires=")>-1?t.substring(0,t.lastIndexOf("?Expires=")):t,r=decodeURI(a.substring(a.lastIndexOf("/")+1)),n={};n.name=r,n.url=a,n.encryptionUrl=a,e.fileListData.push(n),e.fileListArr.push(n)}))}}else e.$message({message:t.Message,type:"error"})}))},getTableColumns:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,v.getTableConfig)(e.gridCode);case 1:e.rootColumns=t.v,e.$refs["table"].init(e.rootColumns),e.$refs["table"].forceRerender();case 2:return t.a(2)}}),t)})))()},getBaseData:function(){var e=this;(0,y.getDictionary)("RawGoodsReturnType").then((function(t){e.RawGoodsReturnTypeList=t.filter((function(e){return e.Is_Enabled}))})),(0,p.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectList=t.Data.Data:e.$message({message:t.Message,type:"error"})})),(0,S.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var r in t.Data){var n={Id:r,Name:t.Data[r]};a.push(n)}e.SupplierList=a}else e.$message({message:t.Message,type:"error"})})),(0,S.GetPartyAs)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var r in t.Data){var n={Id:r,Name:t.Data[r]};a.push(n)}e.PartyUnitList=a}else e.$message({message:t.Message,type:"error"})}))},departmentChange:function(){this.form.Pick_Department_Id?this.getUserPageList(this.form.Pick_Department_Id):this.form.ReceiveUserId=""},changeWarehouse:function(e){this.currentRow=e},getWarehouse:function(e){e.warehouse,e.location},batchEditorFn:function(e){},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,a=e.val;1===t?this.$set(this.currentRow,"StandardDesc",a):(this.$set(this.currentRow,"StandardDesc",a.StandardDesc),this.currentRow.StandardId=a.StandardId)},typeChange:function(e){0!==e&&(this.BigType=1,this.fileListData=[],this.$refs["form"].resetFields(),this.form.OutStoreType=e,this.rootTableData=[],this.handleReset(),this.multipleSelection=[])},projectChange:function(){this.BigType=1,this.typeNumber1=0,this.typeNumber2=0,this.typeNumber3=0,this.typeNumber4=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset()},supplierChange:function(e){this.multipleSelection=[],this.rootTableData=[],this.handleReset()},partyUnitChange:function(e){this.multipleSelection=[],this.rootTableData=[],this.handleReset()},getAddList:function(e){this.BigType=e[0].BigType,this.$refs["table"].addData(e),this.handleUpdateTb(e)},handleUpdateTb:function(e){var t,a;e.map((function(e,t){e.index=(0,_.v4)()}));var r=JSON.parse(JSON.stringify(e));(t=this.tableData).push.apply(t,(0,i.default)(r)),(a=this.rootTableData).push.apply(a,(0,i.default)(r))},handleUpdateRow:function(){var e=JSON.parse(JSON.stringify(this.$refs.table.tbData));this.tableData=JSON.parse(JSON.stringify(e)),this.rootTableData.map((function(t){var a=e.find((function(e){return e.index===t.index}));a&&Object.assign(t,a)}))},importData:function(e){this.$refs["table"].importData(e)},getRowName:function(e){var t=e.Name,a=e.Id;this.currentRow.Name=t,this.currentRow.RawId=a,this.currentRow.StandardDesc=""},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handleWarehouse:function(e){},handleImport:function(){this.currentComponent="ImportFile",this.dWidth="40%",this.title="原料导入",this.dialogVisible=!0},handleDelete:function(){var e,t=this.$refs.table.tbData;this.multipleSelection.forEach((function(e,a){var r=t.findIndex((function(t){return t.index===e.index}));t.splice(r,1)})),this.tableData=JSON.parse(JSON.stringify(t)),this.rootTableData=JSON.parse(JSON.stringify(t)),this.multipleSelection=[],null===(e=this.$refs.table)||void 0===e||null===(e=e.$refs)||void 0===e||e.xTable.clearCheckboxRow()},handleClose:function(e){this.openAddList=!1,this.dialogVisible=!1},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},checkValidate:function(){var e=(0,u.deepClone)(this.$refs["table"].tbData);return e.length?{data:e,status:!0}:(this.$message({message:"数据不能为空",type:"warning"}),{status:!1})},checkTb:function(e){for(var t=this,a=this.checkTypeList.find((function(t){return t.BigType===e[0].BigType})).checkList,r=0;r<e.length;r++){for(var n,i=e[r],o=function(){var e=a[s];if(["",null,void 0].includes(i[e])){var r=t.$refs.table.rootColumns,n=r.find((function(t){return t.Code===e}));return{v:{status:!1,msg:null===n||void 0===n?void 0:n.Display_Name,type:1===i.BigType?"板材":2===i.BigType?"型材":3===i.BigType?"钢卷":"其他"}}}},s=0;s<a.length;s++)if(n=o(),n)return n.v;delete i._X_ROW_KEY,delete i.WarehouseName,delete i.LocationName}return{status:!0,msg:"",type:""}},checkSameTb:function(e){for(var t,a=["Store_Sub_Id"],r=new Map,n=function(){var t=e[i],n=a.map((function(e){return t[e]})).join("-");if(r.has(n))return{v:{status:!1,msg:"",type:1===t.BigType?"板材":2===t.BigType?"型材":3===t.BigType?"钢卷":"其他"}};r.set(n,!0)},i=0;i<e.length;i++)if(t=n(),t)return t.v;return{status:!0,msg:"",type:""}},handleSubmit:function(e){var t=this;this.$confirm("确认提交退货单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.saveDraft(3)})).catch((function(){t.$message({type:"info",message:"已取消"})}))},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=this.checkValidate(),r=a.data,i=a.status;i&&this.$refs["form"].validate((function(a){if(!a)return!1;var i=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){i.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)})),e.form.Attachment=i.join(","),e.form.Status=1===t?1:3;var o=(0,n.default)({},e.form);r.map((function(e){e.OutStoreWeight=e.OutStoreWeightKG,e.Pound_Weight=e.Pound_Weight_KG}));var s="";4==e.form.OutStoreType?s=w.SelfAuxReturnOutStore:2==e.form.OutStoreType&&(s=w.PartyAAuxOutStore,delete o.ReceiveUserId,delete o.Pick_Department_Id),s({Receipt:e.form,Sub:r}).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.closeView()):e.$message({message:t.Message,type:"error"}),e.saveLoading=!1}))}))},openAddDialog:function(e){this.openAddList=!0},closeView:function(){(0,u.closeTagView)(this.$store,this.$route)},setSelectRow:function(e){this.multipleSelection=e},getDate:function(e){var t=e||new Date,a=t.getFullYear(),r=("0"+(t.getMonth()+1)).slice(-2),n=("0"+t.getDate()).slice(-2);return"".concat(a,"-").concat(r,"-").concat(n)}}}},dffa8:function(e,t,a){"use strict";a.r(t);var r=a("52d8"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},e144:function(e,t,a){"use strict";a.r(t),a.d(t,"v1",(function(){return c})),a.d(t,"v3",(function(){return k})),a.d(t,"v4",(function(){return L["a"]})),a.d(t,"v5",(function(){return E})),a.d(t,"NIL",(function(){return F})),a.d(t,"version",(function(){return j})),a.d(t,"validate",(function(){return d["a"]})),a.d(t,"stringify",(function(){return o["a"]})),a.d(t,"parse",(function(){return h}));var r,n,i=a("d8f8"),o=a("58cf"),s=0,u=0;function l(e,t,a){var l=t&&a||0,c=t||new Array(16);e=e||{};var d=e.node||r,f=void 0!==e.clockseq?e.clockseq:n;if(null==d||null==f){var h=e.random||(e.rng||i["a"])();null==d&&(d=r=[1|h[0],h[1],h[2],h[3],h[4],h[5]]),null==f&&(f=n=16383&(h[6]<<8|h[7]))}var p=void 0!==e.msecs?e.msecs:Date.now(),m=void 0!==e.nsecs?e.nsecs:u+1,g=p-s+(m-u)/1e4;if(g<0&&void 0===e.clockseq&&(f=f+1&16383),(g<0||p>s)&&void 0===e.nsecs&&(m=0),m>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");s=p,u=m,n=f,p+=122192928e5;var v=(1e4*(268435455&p)+m)%4294967296;c[l++]=v>>>24&255,c[l++]=v>>>16&255,c[l++]=v>>>8&255,c[l++]=255&v;var b=p/4294967296*1e4&268435455;c[l++]=b>>>8&255,c[l++]=255&b,c[l++]=b>>>24&15|16,c[l++]=b>>>16&255,c[l++]=f>>>8|128,c[l++]=255&f;for(var S=0;S<6;++S)c[l+S]=d[S];return t||Object(o["a"])(c)}var c=l,d=a("06e4");function f(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");var t,a=new Uint8Array(16);return a[0]=(t=parseInt(e.slice(0,8),16))>>>24,a[1]=t>>>16&255,a[2]=t>>>8&255,a[3]=255&t,a[4]=(t=parseInt(e.slice(9,13),16))>>>8,a[5]=255&t,a[6]=(t=parseInt(e.slice(14,18),16))>>>8,a[7]=255&t,a[8]=(t=parseInt(e.slice(19,23),16))>>>8,a[9]=255&t,a[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,a[11]=t/4294967296&255,a[12]=t>>>24&255,a[13]=t>>>16&255,a[14]=t>>>8&255,a[15]=255&t,a}var h=f;function p(e){e=unescape(encodeURIComponent(e));for(var t=[],a=0;a<e.length;++a)t.push(e.charCodeAt(a));return t}var m="6ba7b810-9dad-11d1-80b4-00c04fd430c8",g="6ba7b811-9dad-11d1-80b4-00c04fd430c8",v=function(e,t,a){function r(e,r,n,i){if("string"===typeof e&&(e=p(e)),"string"===typeof r&&(r=h(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var s=new Uint8Array(16+e.length);if(s.set(r),s.set(e,r.length),s=a(s),s[6]=15&s[6]|t,s[8]=63&s[8]|128,n){i=i||0;for(var u=0;u<16;++u)n[i+u]=s[u];return n}return Object(o["a"])(s)}try{r.name=e}catch(n){}return r.DNS=m,r.URL=g,r};function b(e){if("string"===typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var a=0;a<t.length;++a)e[a]=t.charCodeAt(a)}return S(w(_(e),8*e.length))}function S(e){for(var t=[],a=32*e.length,r="0123456789abcdef",n=0;n<a;n+=8){var i=e[n>>5]>>>n%32&255,o=parseInt(r.charAt(i>>>4&15)+r.charAt(15&i),16);t.push(o)}return t}function y(e){return 14+(e+64>>>9<<4)+1}function w(e,t){e[t>>5]|=128<<t%32,e[y(t)-1]=t;for(var a=1732584193,r=-271733879,n=-1732584194,i=271733878,o=0;o<e.length;o+=16){var s=a,u=r,l=n,c=i;a=R(a,r,n,i,e[o],7,-680876936),i=R(i,a,r,n,e[o+1],12,-389564586),n=R(n,i,a,r,e[o+2],17,606105819),r=R(r,n,i,a,e[o+3],22,-1044525330),a=R(a,r,n,i,e[o+4],7,-176418897),i=R(i,a,r,n,e[o+5],12,1200080426),n=R(n,i,a,r,e[o+6],17,-1473231341),r=R(r,n,i,a,e[o+7],22,-45705983),a=R(a,r,n,i,e[o+8],7,1770035416),i=R(i,a,r,n,e[o+9],12,-1958414417),n=R(n,i,a,r,e[o+10],17,-42063),r=R(r,n,i,a,e[o+11],22,-1990404162),a=R(a,r,n,i,e[o+12],7,1804603682),i=R(i,a,r,n,e[o+13],12,-40341101),n=R(n,i,a,r,e[o+14],17,-1502002290),r=R(r,n,i,a,e[o+15],22,1236535329),a=x(a,r,n,i,e[o+1],5,-165796510),i=x(i,a,r,n,e[o+6],9,-1069501632),n=x(n,i,a,r,e[o+11],14,643717713),r=x(r,n,i,a,e[o],20,-373897302),a=x(a,r,n,i,e[o+5],5,-701558691),i=x(i,a,r,n,e[o+10],9,38016083),n=x(n,i,a,r,e[o+15],14,-660478335),r=x(r,n,i,a,e[o+4],20,-405537848),a=x(a,r,n,i,e[o+9],5,568446438),i=x(i,a,r,n,e[o+14],9,-1019803690),n=x(n,i,a,r,e[o+3],14,-187363961),r=x(r,n,i,a,e[o+8],20,1163531501),a=x(a,r,n,i,e[o+13],5,-1444681467),i=x(i,a,r,n,e[o+2],9,-51403784),n=x(n,i,a,r,e[o+7],14,1735328473),r=x(r,n,i,a,e[o+12],20,-1926607734),a=T(a,r,n,i,e[o+5],4,-378558),i=T(i,a,r,n,e[o+8],11,-2022574463),n=T(n,i,a,r,e[o+11],16,1839030562),r=T(r,n,i,a,e[o+14],23,-35309556),a=T(a,r,n,i,e[o+1],4,-1530992060),i=T(i,a,r,n,e[o+4],11,1272893353),n=T(n,i,a,r,e[o+7],16,-155497632),r=T(r,n,i,a,e[o+10],23,-1094730640),a=T(a,r,n,i,e[o+13],4,681279174),i=T(i,a,r,n,e[o],11,-358537222),n=T(n,i,a,r,e[o+3],16,-722521979),r=T(r,n,i,a,e[o+6],23,76029189),a=T(a,r,n,i,e[o+9],4,-640364487),i=T(i,a,r,n,e[o+12],11,-421815835),n=T(n,i,a,r,e[o+15],16,530742520),r=T(r,n,i,a,e[o+2],23,-995338651),a=I(a,r,n,i,e[o],6,-198630844),i=I(i,a,r,n,e[o+7],10,1126891415),n=I(n,i,a,r,e[o+14],15,-1416354905),r=I(r,n,i,a,e[o+5],21,-57434055),a=I(a,r,n,i,e[o+12],6,1700485571),i=I(i,a,r,n,e[o+3],10,-1894986606),n=I(n,i,a,r,e[o+10],15,-1051523),r=I(r,n,i,a,e[o+1],21,-2054922799),a=I(a,r,n,i,e[o+8],6,1873313359),i=I(i,a,r,n,e[o+15],10,-30611744),n=I(n,i,a,r,e[o+6],15,-1560198380),r=I(r,n,i,a,e[o+13],21,1309151649),a=I(a,r,n,i,e[o+4],6,-145523070),i=I(i,a,r,n,e[o+11],10,-1120210379),n=I(n,i,a,r,e[o+2],15,718787259),r=I(r,n,i,a,e[o+9],21,-343485551),a=C(a,s),r=C(r,u),n=C(n,l),i=C(i,c)}return[a,r,n,i]}function _(e){if(0===e.length)return[];for(var t=8*e.length,a=new Uint32Array(y(t)),r=0;r<t;r+=8)a[r>>5]|=(255&e[r/8])<<r%32;return a}function C(e,t){var a=(65535&e)+(65535&t),r=(e>>16)+(t>>16)+(a>>16);return r<<16|65535&a}function O(e,t){return e<<t|e>>>32-t}function D(e,t,a,r,n,i){return C(O(C(C(t,e),C(r,i)),n),a)}function R(e,t,a,r,n,i,o){return D(t&a|~t&r,e,t,n,i,o)}function x(e,t,a,r,n,i,o){return D(t&r|a&~r,e,t,n,i,o)}function T(e,t,a,r,n,i,o){return D(t^a^r,e,t,n,i,o)}function I(e,t,a,r,n,i,o){return D(a^(t|~r),e,t,n,i,o)}var P=b,N=v("v3",48,P),k=N,L=a("ec26");function M(e,t,a,r){switch(e){case 0:return t&a^~t&r;case 1:return t^a^r;case 2:return t&a^t&r^a&r;case 3:return t^a^r}}function A(e,t){return e<<t|e>>>32-t}function W(e){var t=[1518500249,1859775393,2400959708,3395469782],a=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"===typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var n=0;n<r.length;++n)e.push(r.charCodeAt(n))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=e.length/4+2,o=Math.ceil(i/16),s=new Array(o),u=0;u<o;++u){for(var l=new Uint32Array(16),c=0;c<16;++c)l[c]=e[64*u+4*c]<<24|e[64*u+4*c+1]<<16|e[64*u+4*c+2]<<8|e[64*u+4*c+3];s[u]=l}s[o-1][14]=8*(e.length-1)/Math.pow(2,32),s[o-1][14]=Math.floor(s[o-1][14]),s[o-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<o;++d){for(var f=new Uint32Array(80),h=0;h<16;++h)f[h]=s[d][h];for(var p=16;p<80;++p)f[p]=A(f[p-3]^f[p-8]^f[p-14]^f[p-16],1);for(var m=a[0],g=a[1],v=a[2],b=a[3],S=a[4],y=0;y<80;++y){var w=Math.floor(y/20),_=A(m,5)+M(w,g,v,b)+S+t[w]+f[y]>>>0;S=b,b=v,v=A(g,30)>>>0,g=m,m=_}a[0]=a[0]+m>>>0,a[1]=a[1]+g>>>0,a[2]=a[2]+v>>>0,a[3]=a[3]+b>>>0,a[4]=a[4]+S>>>0}return[a[0]>>24&255,a[0]>>16&255,a[0]>>8&255,255&a[0],a[1]>>24&255,a[1]>>16&255,a[1]>>8&255,255&a[1],a[2]>>24&255,a[2]>>16&255,a[2]>>8&255,255&a[2],a[3]>>24&255,a[3]>>16&255,a[3]>>8&255,255&a[3],a[4]>>24&255,a[4]>>16&255,a[4]>>8&255,255&a[4]]}var $=W,U=v("v5",80,$),E=U,F="00000000-0000-0000-0000-000000000000";function G(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}var j=G},e4ba:function(e,t,a){"use strict";a("2008")},ea44:function(e,t,a){},f68a:function(e,t,a){"use strict";a.r(t);var r=a("0ce7"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);t["default"]=n.a},f8f2:function(e,t,a){"use strict";a.r(t);var r=a("db38"),n=a("d022");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"14d353a2",null);t["default"]=s.exports},fb1a:function(e,t,a){"use strict";a.r(t);var r=a("c017"),n=a("2559");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("a5d76");var o=a("2877"),s=Object(o["a"])(n["default"],r["a"],r["b"],!1,null,"0d4600cf",null);t["default"]=s.exports}}]);