(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5edf2ede"],{"090b":function(t,e,a){"use strict";a("2ba4f")},"239a":function(t,e,a){"use strict";a.r(e);var r=a("f83c"),o=a("b91c");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("090b");var i=a("2877"),l=Object(i["a"])(o["default"],r["a"],r["b"],!1,null,"890bd3c6",null);e["default"]=l.exports},"2ba4f":function(t,e,a){},4644:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("2532"),a("159b");var o=r(a("c14f")),n=r(a("1da1")),i=r(a("5530")),l=r(a("bae6")),u=r(a("bad9")),s=a("93aa"),d=r(a("1463")),c=(a("0e9a"),a("5480")),f=a("ed08"),p={IsQualified:"2",ProjectId:"",InStoreNo:""},m={MaterielType:"",AllName:"",Name:"",Spec:"",Material:""};e.default={name:"TestSheet",components:{TreeDetail:d.default,SelectProject:u.default,ExpandableSection:l.default},data:function(){return{showExpand:!0,pgLoading:!1,treeLoading:!1,sheetSearchForm:(0,i.default)({},p),tbSearchForm:(0,i.default)({},m),sheetList:[],curSheet:{},dialogVisible:!1,batchList:[{prop:"",value:"",type:""}],batchProps:[],tableData:[],columns:[],tableLoading:!1,multipleSelection:[],gridCode:"materialTest",defaultCheckedKeys:[],loading:!1}},computed:{materialTypeName:function(){return"MaterialTestSheetRaw"===this.$route.name?"原料":"辅料"},materialType:function(){return"MaterialTestSheetRaw"===this.$route.name?"0":"1"},isRaw:function(){return"MaterialTestSheetRaw"===this.$route.name}},created:function(){this.$route.query.InStoreNo?(this.tbSearchForm.InStoreNo=this.$route.query.InStoreNo,this.tbSearchForm.MaterielType=this.materialType,this.defaultCheckedKeys=[this.$route.query.InStoreNo],this.curSheet.IsQualified=2,this.fetchData()):this.handleReset(),this.getTableColumns(),this.getCheckList()},methods:{getTableColumns:function(){var t=this;return(0,n.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,c.getTableConfig)(t.gridCode);case 1:t.columns=e.v;case 2:return e.a(2)}}),e)})))()},resetSheetForm:function(){this.sheetSearchForm=(0,i.default)({},p),this.getCheckList()},tbSelectChange:function(t){this.multipleSelection=t.records},getCheckList:function(){var t=this;(0,s.GetTestInStoreOrderList)({model:(0,i.default)((0,i.default)({},this.sheetSearchForm),{},{MaterielType:this.materialType})}).then((function(e){t.sheetList=e.Data.map((function(t){return t.Label=t.InStoreNo,t}))}))},handleNodeClick:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.tbSearchForm.InStoreNo=t.InStoreNo,this.curSheet=t,this.fetchData()},handleSearch:function(){this.fetchData()},handleReset:function(){this.tbSearchForm=(0,i.default)({InStoreNo:this.tbSearchForm.InStoreNo},m),this.tbSearchForm.MaterielType=this.materialType,this.fetchData()},toBatch:function(){this.batchList=[{prop:"",value:"",type:""}];var t=[{id:"MaterielCertificateNo",name:"材质证书号"},{id:"CertificateNo",name:"证书编号"},{id:"QualifiedNum",name:"合格数量",type:"int"}];this.batchProps=t.map((function(t){return t.disabled=!1,t})),this.dialogVisible=!0},propChange:function(t,e){var a,r=this;this.batchList[e].value="",this.batchList[e].type=null===(a=this.batchProps.find((function(e){return e.id===t})))||void 0===a?void 0:a.type,this.batchProps=this.batchProps.map((function(t){return t.disabled=!1,t})),this.batchProps.forEach((function(t){r.batchList.find((function(e){return t.id===e.prop}))&&(t.disabled=!0)}))},addProp:function(){this.batchList.push({prop:"",value:"",type:""})},removeProp:function(t){this.batchList.splice(t,1),this.propChange()},submitBatchEdit:function(){var t=this;this.batchList.forEach((function(e){var a=e.prop,r=e.value,o=t.multipleSelection.map((function(t){return t.Id}));t.tableData.forEach((function(e){o.includes(e.Id)&&("QualifiedNum"===a&&r>e.Num?t.$set(e,a,e.Num):t.$set(e,a,r))})),t.dialogVisible=!1})),this.$refs.xTable.updateFooter()},fetchData:function(){var t=this;this.tableLoading=!0,(0,s.GetTestDetail)({model:this.tbSearchForm}).then((function(e){t.tableData=e.Data})).finally((function(){t.tableLoading=!1}))},setResult:function(t){var e=this;this.loading=!0,this.saveSheet(),(0,s.SetQualified)({model:{InStoreNo:this.tbSearchForm.InStoreNo,IsQualified:t}}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"操作成功"}),e.handleNodeClick(),e.getCheckList()):e.$message({type:"error",message:t.Message})})).finally((function(){e.loading=!1}))},saveSheet:function(){var t=this;this.loading=!0,(0,s.SetTestDetail)(this.tableData).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"保存成功"}),t.fetchData()):t.$message({type:"error",message:e.Message})})).finally((function(){t.loading=!1}))},exportExcel:function(){var t=this;this.loading=!0,(0,s.ExportTestDetail)({InStoreNo:this.tbSearchForm.InStoreNo,MaterielType:this.materialType}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"导出成功"}),window.open((0,f.combineURL)(t.$baseUrl,e.Data),"_blank")):t.$message({type:"error",message:e.Message})})).finally((function(){t.loading=!1}))},footerMethod:function(t){var e=this,a=t.columns,r=t.data,o=[a.map((function(t,a){return["Num","QualifiedNum"].includes(t.field)?e.sumNum(r,t.field):0===a?"合计":null}))];return o},sumNum:function(t,e){for(var a=0,r=0;r<t.length;r++)a+=Number(t[r][e]);return a}}}},5480:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getTableConfig=e.getRoleInfo=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var r=a("6186"),o=a("c24f"),n=void 0;e.getTableConfig=function(t,e){return new Promise((function(a,o){(0,r.GetGridByCode)({code:t,businessType:e}).then((function(t){var e=t.IsSucceed,r=t.Data,o=t.Message;if(e){var i=(r.ColumnList||[]).filter((function(t){return t.Is_Display}));a(i)}else n.$message({message:o,type:"error"})}))}))},e.getRoleInfo=function(t){return new Promise((function(e,a){(0,o.RoleAuthorization)({roleType:3,menuType:1,menuId:t}).then((function(t){if(t.IsSucceed){var r=((null===t||void 0===t?void 0:t.Data)||[]).map((function(t){return t.Code}));e(r)}else n.$message({message:t.Message,type:"error"}),a(t.message)}))}))}},"93aa":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=E,e.AuxInStoreExport=H,e.AuxReturnByReceipt=Y,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=B,e.DeleteInStore=b,e.DeletePicking=Ot,e.ExportCheckReceipt=ft,e.ExportInstoreReceipt=ct,e.ExportMoneyAdjustOrder=wt,e.ExportPicking=xt,e.ExportProcess=jt,e.ExportTestDetail=vt,e.FindAuxPageList=U,e.FindRawPageList=D,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=X,e.GetAuxImportTemplate=_,e.GetAuxPageList=at,e.GetAuxPickOutStoreSubList=j,e.GetAuxProcurementDetails=rt,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=g,e.GetImportTemplate=C,e.GetInstoreDetail=P,e.GetMoneyAdjustDetailPageList=Mt,e.GetOMALatestStatisticTime=L,e.GetOrderDetail=it,e.GetPartyAs=I,e.GetPickLockStoreToChuku=_t,e.GetPickPlate=Et,e.GetPickSelectPageList=Tt,e.GetPickSelectSubList=Ft,e.GetPickingDetail=At,e.GetPickingTypeSettingDetail=Gt,e.GetProjectListForTenant=ot,e.GetRawDetailByReceipt=lt,e.GetRawOrderList=nt,e.GetRawPageList=v,e.GetRawPickOutStoreSubList=F,e.GetRawProcurementDetails=M,e.GetRawSurplusReturnStoreDetail=T,e.GetReturnPlate=$t,e.GetStoreSelectPage=Lt,e.GetSuppliers=R,e.GetTestDetail=Rt,e.GetTestInStoreOrderList=Pt,e.Import=A,e.ImportCheckReceipt=mt,e.ImportInstoreReceipt=pt,e.InStoreListSummary=st,e.LockPicking=Dt,e.ManualAuxInStoreDetail=W,e.ManualInStoreDetail=h,e.MaterielAuxInStoreList=$,e.MaterielAuxManualInStore=J,e.MaterielAuxPurchaseInStore=q,e.MaterielAuxSubmitInStore=Q,e.MaterielPartyAInStorel=K,e.MaterielRawInStoreList=n,e.MaterielRawInStoreListInSumNew=l,e.MaterielRawInStoreListNew=i,e.MaterielRawManualInStore=x,e.MaterielRawPartyAInStore=y,e.MaterielRawPurchaseInStore=w,e.MaterielRawSubmitInStore=u,e.MaterielRawSurplusInStore=k,e.OutStoreListSummary=dt,e.PartAInStoreDetail=m,e.PartyAInInStoreDetail=V,e.PurchaseAuxInStoreDetail=z,e.PurchaseInStoreDetail=p,e.RawInStoreExport=G,e.RawReturnByReceipt=ut,e.RawSurplusReturnStore=N,e.SaveInStore=O,e.SavePicking=Ct,e.SetQualified=It,e.SetTestDetail=gt,e.StoreMoneyAdjust=St,e.SubmitApproval=f,e.SubmitAuxApproval=c,e.SubmitInStore=ht,e.SubmitPicking=kt,e.SurplusInStoreDetail=S,e.UnLockPicking=Nt,e.UpdateInvoiceInfo=yt,e.Withdraw=s,e.WithdrawAux=d,e.WithdrawChecked=bt;var o=r(a("b775"));function n(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function i(t){return(0,o.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function P(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function M(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function A(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function N(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function E(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function $(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function Q(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function B(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function z(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function V(t){return(0,o.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function q(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function K(t){return(0,o.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function J(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function H(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function X(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function Y(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,o.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function at(t){return(0,o.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function rt(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function ot(t){return(0,o.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function nt(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function it(t){return(0,o.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function lt(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function ut(t){return(0,o.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function st(t){return(0,o.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function dt(t){return(0,o.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function ct(t){return(0,o.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function ft(t){return(0,o.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function pt(t){return(0,o.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function mt(t){return(0,o.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function ht(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function St(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function Pt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function bt(t){return(0,o.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function Rt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function It(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function gt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function vt(t){return(0,o.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function Mt(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function wt(t){return(0,o.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function yt(t){return(0,o.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function xt(t){return(0,o.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function kt(t){return(0,o.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function Ot(t){return(0,o.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function Lt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function Ct(t){return(0,o.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function At(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function Gt(t){return(0,o.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function Dt(t){return(0,o.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function Nt(t){return(0,o.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function Tt(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function Ft(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function _t(t){return(0,o.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function Et(t){return(0,o.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function $t(t){return(0,o.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function jt(t){return(0,o.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},b91c:function(t,e,a){"use strict";a.r(e);var r=a("4644"),o=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(n);e["default"]=o.a},e41b:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteByIds=u,e.GetPartsImportTemplate=d,e.GetPartsList=l,e.GetProjectAreaTreeList=n,e.ImportParts=s,e.SaveProjectAreaSort=i;var o=r(a("b775"));function n(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:t})}},f83c:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return o}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pgLoading,expression:"pgLoading"}],staticClass:"h100 app-wrapper",attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff",attrs:{width:300},model:{value:t.showExpand,callback:function(e){t.showExpand=e},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[t.showExpand?[a("el-row",{staticStyle:{"align-items":"center","margin-bottom":"10px"},attrs:{type:"flex"}},[a("div",{staticClass:"title"},[t._v("入库单")]),a("el-button",{attrs:{type:"primary"},on:{click:t.getCheckList}},[t._v("搜索")]),a("el-button",{attrs:{type:"default"},on:{click:t.resetSheetForm}},[t._v("重置")])],1),a("div",{staticClass:"tree-search"},[a("el-form",{attrs:{inline:""}},[a("el-form-item",{attrs:{label:"项目名称"}},[a("SelectProject",{model:{value:t.sheetSearchForm.ProjectId,callback:function(e){t.$set(t.sheetSearchForm,"ProjectId",e)},expression:"sheetSearchForm.ProjectId"}})],1),a("el-form-item",{attrs:{label:"检测结果"}},[a("el-select",{attrs:{clearable:""},model:{value:t.sheetSearchForm.IsQualified,callback:function(e){t.$set(t.sheetSearchForm,"IsQualified",e)},expression:"sheetSearchForm.IsQualified"}},[a("el-option",{attrs:{label:"未检测",value:"2"}}),a("el-option",{attrs:{label:"合格",value:"1"}})],1)],1),a("el-form-item",{attrs:{label:"入库单号"}},[a("el-input",{staticStyle:{width:"196px"},attrs:{clearable:""},model:{value:t.sheetSearchForm.InStoreNo,callback:function(e){t.$set(t.sheetSearchForm,"InStoreNo",e)},expression:"sheetSearchForm.InStoreNo"}})],1)],1)],1)]:t._e(),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder",loading:t.treeLoading,"tree-data":t.sheetList,"show-status":"","show-detail":"","default-checked-keys":t.defaultCheckedKeys,"node-key":"InStoreNo"},on:{handleNodeClick:t.handleNodeClick},scopedSlots:t._u([{key:"csLabel",fn:function(e){var r=e.data;return[a("span",[t._v(t._s(r.Label))]),a("i",{class:["cs-tag",["fourRed","fourGreen","fourOrange"][r.IsQualified]]},[t._v("("+t._s(["不合格","已合格","未检测"][r.IsQualified])+")")])]}}])})],1)],2)]),a("div",{staticClass:"cs-right"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",attrs:{inline:""}},[t.isRaw?a("el-form-item",{attrs:{label:t.materialTypeName+"全名",prop:"AllName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"通配符%",clearable:""},model:{value:t.tbSearchForm.AllName,callback:function(e){t.$set(t.tbSearchForm,"AllName",e)},expression:"tbSearchForm.AllName"}})],1):t._e(),a("el-form-item",{attrs:{label:t.materialTypeName+"名称",prop:"Name"}},[a("el-input",{staticStyle:{"min-width":"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:t.tbSearchForm.Name,callback:function(e){t.$set(t.tbSearchForm,"Name",e)},expression:"tbSearchForm.Name"}})],1),a("el-form-item",{attrs:{label:"规格/厚度",prop:"Spec"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:t.tbSearchForm.Spec,callback:function(e){t.$set(t.tbSearchForm,"Spec",e)},expression:"tbSearchForm.Spec"}})],1),t.isRaw?a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:t.tbSearchForm.Material,callback:function(e){t.$set(t.tbSearchForm,"Material",e)},expression:"tbSearchForm.Material"}})],1):t._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),a("el-button",{staticClass:"reset-btn",on:{click:t.handleReset}},[t._v("重置")])],1)],1)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},[2==t.curSheet.IsQualified?[a("el-button",{attrs:{type:"primary",disabled:!t.tbSearchForm.InStoreNo,loading:t.loading},on:{click:function(e){return t.setResult(1)}}},[t._v("合格")]),a("el-button",{attrs:{type:"danger",disabled:!t.tbSearchForm.InStoreNo,loading:t.loading},on:{click:function(e){return t.setResult(0)}}},[t._v("不合格")]),a("el-button",{attrs:{type:"primary",disabled:t.multipleSelection&&!t.multipleSelection.length},on:{click:t.toBatch}},[t._v("批量编辑")])]:t._e(),a("el-button",{attrs:{type:"default",disabled:!t.tbSearchForm.InStoreNo,loading:t.loading},on:{click:t.exportExcel}},[t._v("导出来货检验记录")]),2==t.curSheet.IsQualified?a("el-button",{staticStyle:{"margin-left":"auto"},attrs:{type:"primary",loading:t.loading,disabled:!t.tbSearchForm.InStoreNo},on:{click:t.saveSheet}},[t._v("保存")]):t._e()],2),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","show-footer":"","footer-method":t.footerMethod,"row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"","auto-resize":!0,stripe:"",size:"medium",data:t.tableData,resizable:"","edit-config":{trigger:"click",mode:"cell"},"tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60",title:""}}),a("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),t._l(t.columns,(function(e){return[a("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:e.Align,field:e.Code,visible:e.Is_Display,title:e.Is_Must_Input?"*"+e.Display_Name:e.Display_Name,"min-width":e.Width,"edit-render":e.Is_Edit?{}:null},scopedSlots:t._u([{key:"default",fn:function(r){var o=r.row;return[a("span",[t._v(" "+t._s(t._f("displayValue")(o[e.Code])))])]}},e.Is_Edit?{key:"edit",fn:function(r){var o=r.row;return["QualifiedNum"===e.Code?a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:0,min:0,max:o.Num},expression:"{ toFixed: 0, min: 0,max:row.Num }"}],model:{value:o[e.Code],callback:function(a){t.$set(o,e.Code,a)},expression:"row[item.Code]"}}):a("el-input",{attrs:{type:"text"},model:{value:o[e.Code],callback:function(a){t.$set(o,e.Code,a)},expression:"row[item.Code]"}})]}}:null],null,!0)})]}))],2)],1)])])],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"批量编辑",visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{attrs:{inline:"","label-width":"80px"}},t._l(t.batchList,(function(e,r){return a("el-row",{key:e.id},[a("el-form-item",{attrs:{label:"属性名称"}},[a("el-select",{attrs:{filterable:""},on:{change:function(e){return t.propChange(e,r)},focus:function(e){return t.propChange(e,r)}},model:{value:e.prop,callback:function(a){t.$set(e,"prop",a)},expression:"item.prop"}},t._l(t.batchProps,(function(t){return a("el-option",{key:t.id,attrs:{disabled:t.disabled,label:t.name,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"请输入值"}},["int"===e.type?[a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:0,min:0},expression:"{toFixed:0,min:0}"}],key:e.prop,attrs:{placeholder:"请输入数字"},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"item.value"}})]:[a("el-input",{key:e.prop,attrs:{placeholder:"请输入"},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"item.value"}})]],2),t.batchList.length<t.batchProps.length?a("i",{staticClass:"el-icon-circle-plus-outline prop-icon",on:{click:t.addProp}}):t._e(),t.batchList.length>1?a("i",{staticClass:"el-icon-remove-outline  prop-icon",staticStyle:{color:"#fc8999"},on:{click:function(e){return t.removeProp(r)}}}):t._e()],1)})),1),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitBatchEdit()}}},[t._v("确 定")])],1)],1)],1)},o=[]}}]);