(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-c1ecc42a"],{"1afa":function(e,t,a){"use strict";a.r(t);var n=a("7665"),r=a("37c5");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("299ca");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"7b0e9aab",null);t["default"]=l.exports},"1f85":function(e,t,a){},"299ca":function(e,t,a){"use strict";a("8aa8")},"2ee2":function(e,t,a){"use strict";a("95aa")},"321b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("bc29")),o=n(a("40c9")),i=n(a("1afa")),l=a("8378");t.default={name:"PROMaterialInventory",components:{inventory:i.default},mixins:[r.default,o.default],data:function(){return{showSearch:!1,IsAux:!0,activeName:"1",form:{Spec:"",Raw_Name:"",Category_Id:"",WH_Id:"",Location_Id:"",Sys_Project_Id:"",Supplier:""},categoryOptions:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},treeList:[]}},mounted:function(){this.getCategoryList()},methods:{handleTap:function(){},handleSearch:function(){this.$refs.inventoryRef.fetchData()},getCategoryList:function(){var e=this;(0,l.GetAuxCategoryTreeList)({}).then((function(t){if(t.IsSucceed){e.treeList=t.Data;var a=t.Data;e.categoryOptions.data=a,e.$nextTick((function(t){e.$refs.treeSelectArea.treeDataUpdateFun(a)}))}else e.$message.error(t.Message)}))}}}},3520:function(e,t,a){"use strict";a.r(t);var n=a("5705"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},3672:function(e,t,a){"use strict";a.r(t);var n=a("7929"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},3679:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog-wapper"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{height:"100%",flex:"1"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.currentColumns,config:e.tbConfig,data:e.tbData,border:"",stripe:"",height:"100%"},scopedSlots:e._u([{key:"Unlock_Count",fn:function(t){var n=t.row,r=t.column;return[a("div",[r.Is_Edit?a("el-input",{staticClass:"input-number",attrs:{type:"number",placeholder:"请输入",min:0,max:n.In_Store_Count?n.In_Store_Count:""},on:{change:function(t){return e.changeUnlockCount(t,n)}},model:{value:n.Unlock_Count,callback:function(t){e.$set(n,"Unlock_Count",e._n(t))},expression:"row.Unlock_Count"}}):a("span",[e._v(e._s(n.Unlock_Count))])],1)]}},{key:"Tax_Unit_Price",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(n.Tax_Unit_Price||0===n.Tax_Unit_Price?n.Tax_Unit_Price.toFixed(2):"-")+" ")])]}},{key:"Tax_Unit_Price_New",fn:function(t){var n=t.row,r=t.column;return[a("div",[r.Is_Edit?a("el-input",{staticClass:"input-number",attrs:{type:"number",placeholder:"请输入",min:0},on:{change:function(t){return e.changeTaxUnitPrice(n)}},model:{value:n.Tax_Unit_Price_New,callback:function(t){e.$set(n,"Tax_Unit_Price_New",e._n(t))},expression:"row.Tax_Unit_Price_New"}}):a("span",[e._v(e._s(n.Tax_Unit_Price_New))])],1)]}},{key:"Tax_All_Price_New",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(n.Tax_All_Price_New||0===n.Tax_All_Price_New?n.Tax_All_Price_New:"-")+" ")])]}},{key:"Lock_Count",fn:function(t){var n=t.row;return[a("div",[a("el-input",{staticClass:"input-number",attrs:{type:"number",placeholder:"请输入",min:0,max:n.In_Store_Count?n.In_Store_Count:""},on:{change:function(t){return e.changeLockCount(t,n)}},model:{value:n.Lock_Count,callback:function(t){e.$set(n,"Lock_Count",e._n(t))},expression:"row.Lock_Count"}})],1)]}},{key:"Affiliation_Project_Name",fn:function(t){var n=t.row;return[a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:!Boolean(n.Lock_Count)},model:{value:n.Affiliation_Project_Id,callback:function(t){e.$set(n,"Affiliation_Project_Id",t)},expression:"row.Affiliation_Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)]}}])})],1),0===e.flag?a("div",{staticClass:"footer-wrapper"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.saveLoading},on:{click:e.handleSave}},[e._v("确定 ")])],1):e._e()])},r=[]},"37c5":function(e,t,a){"use strict";a.r(t);var n=a("eaba"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},5705:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),o=n(a("1da1"));a("a9e3"),a("ac1f"),a("841c");var i=n(a("bad9")),l=n(a("7387"));t.default={components:{bimtable:l.default,SelectProject:i.default},props:{materialType:{type:[String,Number],default:0}},data:function(){return{pgLoading:!1,form:{MaterialType:"",Material_Name_All:"",Region_Project_Id:"",In_Project_Id:"",To_Project_Id:"",Transfer_Date_Begin:"",Transfer_Date_End:"",Transfer_Date:[]},gridCode:""}},watch:{"form.Transfer_Date":{handler:function(){this.form.Transfer_Date_Begin=this.form.Transfer_Date?this.form.Transfer_Date[0]:"",this.form.Transfer_Date_End=this.form.Transfer_Date?this.form.Transfer_Date[1]:""},immediate:!0},materialType:{handler:function(){this.form.MaterialType=this.materialType,this.gridCode=0==this.materialType?"materialTransformLog":"materialAuxTransformLog",this.search()},immediate:!0}},mounted:function(){return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{search:function(){var e;null===(e=this.$refs.table)||void 0===e||e.refresh()},reset:function(){this.form={MaterialType:this.form.MaterialType,Material_Name_All:"",Region_Project_Id:"",In_Project_Id:"",To_Project_Id:"",Transfer_Date_Begin:"",Transfer_Date_End:"",Transfer_Date:[]},this.search()}}}},"5f52":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("dca8"),a("d3b7");var r=n(a("c14f")),o=n(a("1da1")),i=a("6186"),l=a("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,o.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getTypeList();case 1:return a.n=2,t.getTable(e);case 2:return a.a(2)}}),a)})))()},getTypeList:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a=Object.freeze(t.Data);if(a.length>0){var n,r=null===(n=a[0])||void 0===n?void 0:n.Id;e.Code=a.find((function(e){return e.Id===r})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(a){(0,i.GetGridByCode)({code:e+","+t.Code}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,r.Grid),t.columns=(r.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),r.Grid.Is_Page&&(t.queryInfo.PageSize=+r.Grid.Row_Number),a(t.columns)}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var r=this.columns[n];if(r.Code===t){a.Type=r.Type,a.Filter_Type=r.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"67b3":function(e,t,a){"use strict";a.r(t);var n=a("e361"),r=a("3520");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("2ee2");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"c0f17488",null);t["default"]=l.exports},"6ba5":function(e,t,a){},7665:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table-wapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{type:"primary"},on:{click:e.viewTransformLog}},[e._v("转工程记录")])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{height:"0",flex:"1"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.currentColumns,data:e.tbData,config:e.tbConfig,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,multiSelectedChange:e.tbSelectChange},scopedSlots:e._u([{key:"Raw_Name",fn:function(t){var n=t.row;return[a("div",[n.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),n.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),e._v(e._s(n.Raw_Name)+" ")],1)]}},{key:"Material",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Material||"-"))])]}},{key:"Supplier",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Supplier||"-"))])]}},{key:"Party_Unit",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Party_Unit||"-"))])]}},{key:"Total_Count",fn:function(t){var n=t.row;return[n.Total_Count?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n,1)}}},[e._v(e._s(n.Total_Count))]):a("span",[e._v("-")])]}},{key:"Total_Lock_Count",fn:function(t){var n=t.row;return[n.Total_Lock_Count?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n,2)}}},[e._v(e._s(n.Total_Lock_Count))]):a("span",[e._v("-")])]}},{key:"op",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handelLock(n,3)}}},[e._v("转工程")])]}}])})],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],ref:"content",staticClass:"plm-custom-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"material-type":1},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e()],1)},r=[]},7921:function(e,t,a){"use strict";a("6ba5")},7929:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("a9e3"),a("b680"),a("d3b7"),a("25f0");var r=n(a("c14f")),o=n(a("1da1")),i=n(a("5f52")),l=n(a("0f97")),u=a("3c4a"),c=n(a("bc29")),s=a("ed08");t.default={components:{DynamicDataTable:l.default},mixins:[i.default,c.default],data:function(){return{pgLoading:!1,saveLoading:!1,columns:[],currentColumns:[],tbData:[],flag:1,tbConfig:{},ProjectId:""}},mounted:function(){return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{batchUnlock:function(e){var t=this;return(0,o.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,t.pgLoading=!0,t.isBatchUnlock=!0,t.flag=4,a.n=1,t.getTableConfig("pro_public_allocation");case 1:t.currentColumns=t.columns.filter((function(e){return"Lock_Count"!==e.Code&&"Affiliation_Project_Name"!==e.Code&&"In_Store_Weight"!==e.Code&&"Furnace_Batch_No"!==e.Code&&"Unlock_Weight"!==e.Code&&"Lock_Weight"!==e.Code&&"Purchase_Ready_Lock_Count"!==e.Code&&"Actual_Thick"!==e.Code})).map((function(e){return["Unlock_Count","Tax_Unit_Price_New"].includes(e.Code)&&(e.Is_Edit=!1),e})),t.fetNewDetail(e),a.n=3;break;case 2:a.p=2,a.v,t.pgLoading=!1;case 3:return a.a(2)}}),a,null,[[0,2]])})))()},fetNewDetail:function(e){var t=this,a=e.map((function(e){return e.Store_Id})).toString();(0,u.GetAuxBatchInventoryDetailList)({Store_Id:a}).then((function(e){e.IsSucceed?t.tbData=e.Data.map((function(e){return e.In_Store_Date=e.In_Store_Date?(0,s.parseTime)(new Date(e.In_Store_Date),"{y}-{m}-{d}"):"",e.Tax_Rate_New=e.Tax_Rate,e.Unlock_Count=e.In_Store_Count,e.Tax_Unit_Price_New=e.Tax_Unit_Price,e.Tax_All_Price_New=e.Tax_All_Price||"",e.NewNoTaxAllPrice=(e.Tax_All_Price_New/(1+e.Tax_Rate/100)).toFixed(3)/1||0,e})):t.$message({message:e.Message,type:"error"}),t.pgLoading=!1}))},init:function(e,t,a){var n=this;return(0,o.default)((0,r.default)().m((function o(){return(0,r.default)().w((function(r){while(1)switch(r.n){case 0:return n.ProjectId=a,n.flag=t,r.n=1,n.getTableConfig("pro_public_allocation");case 1:return n.currentColumns=n.columns,1===t?n.currentColumns=n.currentColumns.filter((function(e){return"Project_Name"!==e.Code&&"Unlock_Count"!==e.Code&&"Unlock_Weight"!==e.Code&&"Lock_Count"!==e.Code&&"Lock_Weight"!==e.Code&&"Affiliation_Project_Name"!==e.Code&&"Tax_Unit_Price_New"!==e.Code&&"Tax_All_Price_New"!==e.Code&&"Furnace_Batch_No"!==e.Code&&"In_Store_Weight"!==e.Code&&"Purchase_Ready_Lock_Count"!==e.Code&&"Actual_Thick"!==e.Code})):2===t?n.currentColumns=n.currentColumns.filter((function(e){return"Unlock_Count"!==e.Code&&"Lock_Count"!==e.Code&&"Lock_Weight"!==e.Code&&"Affiliation_Project_Name"!==e.Code&&"In_Store_Weight"!==e.Code&&"Furnace_Batch_No"!==e.Code&&"Unlock_Count"!==e.Code&&"Unlock_Weight"!==e.Code&&"Tax_Unit_Price_New"!==e.Code&&"Tax_All_Price_New"!==e.Code&&"Purchase_Ready_Lock_Count"!==e.Code&&"Actual_Thick"!==e.Code})):0===t&&(n.currentColumns=n.currentColumns.filter((function(e){return"Unlock_Count"!==e.Code&&"Project_Name"!==e.Code&&"In_Store_Weight"!==e.Code&&"Furnace_Batch_No"!==e.Code&&"Unlock_Weight"!==e.Code&&"Lock_Weight"!==e.Code&&"Purchase_Ready_Lock_Count"!==e.Code&&"Actual_Thick"!==e.Code}))),r.n=2,n.fetchData(e);case 2:return r.a(2)}}),o)})))()},fetchData:function(e){var t=this;return(0,o.default)((0,r.default)().m((function a(){var n;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return n={Inventory_Type:t.flag,Store_Id:e.Store_Id,Big_Type:100},a.n=1,(0,u.GetAuxInventoryDetailList)(n).then((function(a){a.IsSucceed?t.tbData=a.Data.map((function(t){return t.In_Store_Date=t.In_Store_Date?(0,s.parseTime)(new Date(t.In_Store_Date),"{y}-{m}-{d}"):"",t.Tax_Rate_New=t.Tax_Rate,t.Unlock_Count="",t.Lock_Count="",t.Tax_Unit_Price_New=t.Tax_Unit_Price||0===t.Tax_Unit_Price?t.Tax_Unit_Price:"",t.Tax_All_Price_New=t.Tax_All_Price||"",t.Affiliation_Project_Name="",t.Affiliation_Project_Id="",t.Store_Id=e.Store_Id,t})):t.$message.error(a.Message)}));case 1:return a.a(2)}}),a)})))()},changeTaxUnitPrice:function(e){Number(e.Tax_Unit_Price_New)>0?0===this.flag&&(0===e.Lock_Count||0===e.Tax_Unit_Price_New?e.Tax_All_Price_New=0:e.Lock_Count&&e.Tax_Unit_Price_New?e.Tax_All_Price_New=Number((e.Lock_Count*e.Tax_Unit_Price_New).toFixed(2)):e.Tax_All_Price_New=""):e.Tax_All_Price_New="",this.$set(e,"NewNoTaxAllPrice",(e.Tax_All_Price_New/(1+e.Tax_Rate/100)).toFixed(3)/1||0)},changeUnlockCount:function(e,t){Number(e)>0&&Number(e)<=Number(t.In_Store_Count)?this.changeTaxUnitPrice(t):(t.Unlock_Count="",t.Tax_All_Price_New="")},changeLockCount:function(e,t){Number(e)>0&&Number(e)<=Number(t.In_Store_Count)?this.changeTaxUnitPrice(t):(t.Lock_Count="",t.Affiliation_Project_Id="",t.Tax_All_Price_New=""),t.NewNoTaxAllPrice=(t.Tax_All_Price_New/(1+t.Tax_Rate/100)).toFixed(3)/1||0},handleSave:function(){var e=this,t=this.tbData.filter((function(e){return e.Lock_Count}));if(0!==t.length){var a=!1;0===this.flag&&t.map((function(e){e.Affiliation_Project_Id||(a=!0),e.Sys_Project_Id=e.Affiliation_Project_Id})),a?this.$message.warning("有锁定数量,所属项目不能为空"):(t=t.map((function(e){return{MaterialType:1,Store_Id:e.Store_Id,Store_Sub_Id:e.Store_Sub_Id,In_Store_Sub_Id:e.In_Store_Sub_Id,To_Sys_Project_Id:e.Sys_Project_Id,Transfer_Count:e.Lock_Count}})),this.saveLoading=!0,(0,u.TransferRawLock)(t).then((function(t){t.IsSucceed?(e.$message.success("保存成功"),e.$emit("refresh"),e.$emit("close")):e.$message.error(t.Message)})).finally((function(t){e.saveLoading=!1})))}else this.$message.warning(this.$message.warning("转工程数据最少为一条"))},handleClose:function(){this.$emit("close")}}}},8378:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CreateVersion=L,t.DelAuxCategoryEntity=v,t.DelAuxEntity=b,t.DelCategoryEntity=u,t.DelRawEntity=d,t.DeleteVersion=A,t.EditAuxEnabled=y,t.EditRawEnabled=s,t.ExportAuxForProject=z,t.ExportAuxList=S,t.ExportFindRawInAndOut=G,t.ExportInOutStoreReport=X,t.ExportPicking=M,t.ExportRawList=R,t.ExportRecSendProjectMaterialReport=Q,t.ExportRecSendProjectReport=K,t.ExportReceiving=D,t.ExportStagnationInventory=H,t.ExportStoreReport=J,t.FindInAndOutPageList=$,t.FindPickingNewPageList=U,t.FindPickingPageList=E,t.FindReceivingNewPageList=j,t.FindReceivingPageList=O,t.GetAuxCategoryDetail=C,t.GetAuxCategoryTreeList=h,t.GetAuxDetail=x,t.GetAuxFilterDataSummary=q,t.GetAuxForProjectDetail=V,t.GetAuxForProjectPageList=B,t.GetAuxPageList=w,t.GetAuxTemplate=I,t.GetAuxWHSummaryList=W,t.GetCategoryDetail=l,t.GetCategoryTreeList=o,t.GetCycleDate=F,t.GetList=k,t.GetRawDetail=_,t.GetRawPageList=f,t.GetRawTemplate=m,t.ImportAuxList=T,t.ImportRawList=p,t.SaveAuxCategoryEntity=g,t.SaveAuxEntity=P,t.SaveCategoryEntity=i,t.SaveRawEntity=c,t.UpdateVersion=N;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveCategoryEntity",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetCategoryDetail",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelCategoryEntity",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/SaveRawEntity",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/EditRawEnabled",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/DelRawEntity",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetRawDetail",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/GetTemplate",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/ImportRawList",method:"post",data:e,timeout:12e5})}function h(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveCategoryEntity",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetCategoryDetail",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelCategoryEntity",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/SaveAuxEntity",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/EditAuxEnabled",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/DelAuxEntity",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetAuxDetail",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/GetTemplate",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ImportAuxList",method:"post",data:e,timeout:12e5})}function R(e){return(0,r.default)({url:"/PRO/MaterielRawConfig/ExportRawList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/MaterielAuxConfig/ExportAuxList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/MaterielVersion/GetList",method:"get",params:e})}function L(e){return(0,r.default)({url:"/PRO/MaterielVersion/Create",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/MaterielVersion/Update",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/MaterielVersion/Delete",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/MaterielFlow/ExportReceiving",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/MaterielFlow/ExportPicking",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingPageList",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindReceivingNewPageList",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingPageList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/MaterielFlow/FindPickingNewPageList",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/MaterielReport/FindInAndOutPageList",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Communal/GetCycleDate",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/MaterielReport/ExportFindRawInAndOut",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxWHSummaryList",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectPageList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxForProjectDetail",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/MaterielInventory/ExportAuxForProject",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxFilterDataSummary",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStagnationInventory",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportStoreReport",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectReport",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportRecSendProjectMaterialReport",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/MaterielReportNew/ExportInOutStoreReport",method:"post",data:e})}},"8aa8":function(e,t,a){},9335:function(e,t,a){"use strict";a("1f85")},"95aa":function(e,t,a){},"9f4b":function(e,t,a){"use strict";a.r(t);var n=a("3679"),r=a("3672");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("7921");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"268f02c9",null);t["default"]=l.exports},a97d:function(e,t,a){"use strict";a.r(t);var n=a("321b"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},bcc61:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{ref:"searchDom",staticClass:"header_wrapper"},[a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:"","label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"辅料名称",prop:"Raw_Name"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Raw_Name,callback:function(t){e.$set(e.form,"Raw_Name",t)},expression:"form.Raw_Name"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0},"tree-params":e.categoryOptions},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1)],1),a("transition",{attrs:{name:"fade"}},[e.showSearch?a("span",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"仓库",prop:"WH_Id"}},[a("el-select",{ref:"WarehouseRef",attrs:{clearable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.WH_Id,callback:function(t){e.$set(e.form,"WH_Id",t)},expression:"form.WH_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{ref:"LocationRef",attrs:{clearable:"",placeholder:"请选择库位",disabled:!e.form.WH_Id},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"项目",prop:"Sys_Project_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}})],1)],1)],1):e._e()]),a("el-col",{attrs:{span:6}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch()}}},[e._v("重置")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){e.showSearch=!e.showSearch}}},[e._v(e._s(e.showSearch?"收起":"展开"))]),e.showSearch?a("i",{staticClass:"el-icon-caret-top",staticStyle:{color:"#409EFF"}}):a("i",{staticClass:"el-icon-caret-bottom",staticStyle:{color:"#409EFF"}})],1)],1)],1)],1)],1)]),a("div",{staticClass:"main-wrapper"},[a("inventory",{ref:"inventoryRef",attrs:{"search-detail":e.form,"active-name":e.activeName}})],1)])},r=[]},c438:function(e,t,a){"use strict";a.r(t);var n=a("bcc61"),r=a("a97d");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("9335");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"30304c00",null);t["default"]=l.exports},e361:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog-wapper"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"cs-z-tb-wrapper",staticStyle:{flex:"1",height:"100%"},attrs:{"element-loading-text":"加载中","element-loading-spinner":"el-icon-loading"}},[a("el-form",{attrs:{inline:""}},[0==e.materialType?a("el-form-item",{attrs:{label:"原料全名"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Material_Name_All,callback:function(t){e.$set(e.form,"Material_Name_All",t)},expression:"form.Material_Name_All"}})],1):a("el-form-item",{attrs:{label:"辅料名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Material_Name_All,callback:function(t){e.$set(e.form,"Material_Name_All",t)},expression:"form.Material_Name_All"}})],1),a("el-form-item",{attrs:{label:"初始项目"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.Region_Project_Id,callback:function(t){e.$set(e.form,"Region_Project_Id",t)},expression:"form.Region_Project_Id"}})],1),a("el-form-item",{attrs:{label:"转入项目"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.To_Project_Id,callback:function(t){e.$set(e.form,"To_Project_Id",t)},expression:"form.To_Project_Id"}})],1),a("el-form-item",{attrs:{label:"转出项目"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.In_Project_Id,callback:function(t){e.$set(e.form,"In_Project_Id",t)},expression:"form.In_Project_Id"}})],1),a("el-form-item",{attrs:{label:"分配日期"}},[a("el-date-picker",{staticStyle:{width:"250px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.form.Transfer_Date,callback:function(t){e.$set(e.form,"Transfer_Date",t)},expression:"form.Transfer_Date"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.search}},[e._v("搜索")]),a("el-button",{attrs:{type:"default"},on:{click:e.reset}},[e._v("重置")])],1)],1),a("bimtable",{ref:"table",attrs:{tablecode:e.gridCode,"custom-param":e.form,"case-conversion":!1}})],1)])},r=[]},eaba:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d3b7");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=n(a("5f52")),u=n(a("0f97")),c=n(a("9f4b")),s=a("3c4a"),d=n(a("67b3"));t.default={components:{DynamicDataTable:u.default,stockDialog:c.default,logDialog:d.default},mixins:[l.default],props:{searchDetail:{type:Object,default:function(){return{}}},activeName:{type:String,default:"0"}},data:function(){return{selectArray:[],pgLoading:!1,btnloading:!1,queryInfo:{Page:1,PageSize:20},columns:[],currentColumns:[],tbData:[],tbConfig:{Op_Width:76},total:0,dialogVisible:!1,dialogTitle:"",width:0,currentComponent:"",form:{Raw_Name:"",Spec:"",Thick:"",WH_Id:"",Sys_Project_Id:"",Category_Id:"",Location_Id:"",Big_Type:1}}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("PROAuxMaterialAllocationNew");case 1:return e.currentColumns=e.columns,t.n=2,e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{tbSelectChange:function(e){this.selectArray=e},fetchData:function(){var e=this,t=this.searchDetail,a=t.Raw_Name,n=t.Spec,o=t.Sys_Project_Id,i=t.Category_Id,l=t.WH_Id,u=t.Location_Id,c=t.Supplier;this.form={},this.form.Raw_Name=a,this.form.WH_Id=l,this.form.Sys_Project_Id=o,this.form.Category_Id=i,this.form.Location_Id=u,this.form.Supplier=c,this.form.Spec=n,this.form.Big_Type=100,this.pgLoading=!0,(0,s.GetAuxInventoryPageList)((0,r.default)((0,r.default)({},this.form),this.queryInfo)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message.error(t.Message)})).finally((function(t){e.pgLoading=!1}))},generateComponent:function(e,t,a){this.dialogTitle=e,this.currentComponent=t,this.width=a,this.dialogVisible=!0},handelBatchLock:function(){var e=this;this.generateComponent("转工程记录","stockDialog","60%"),this.$nextTick((function(t){e.$refs.content.batchUnlock(e.selectArray)}))},handleClose:function(){this.dialogVisible=!1},handleView:function(e,t){var a=this;this.generateComponent("".concat(1===t?"公共库存":"锁定库存"),"stockDialog","60%"),this.$nextTick((function(n){a.$refs.content.init(e,t,a.form.ProjectId)}))},handelLock:function(e){var t=this;this.generateComponent("转工程","stockDialog","60%"),this.$nextTick((function(a){t.$refs.content.init(e,0,t.form.ProjectId,t.activeName)}))},viewTransformLog:function(){this.generateComponent("转工程记录","logDialog","90%"),this.$nextTick((function(e){}))}}}}}]);