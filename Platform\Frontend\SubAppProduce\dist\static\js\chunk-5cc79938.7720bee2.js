(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5cc79938"],{"0eb1":function(e,t,a){"use strict";a.r(t);var o=a("b382"),l=a("3f5d");for(var i in l)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(i);a("7dc2");var r=a("2877"),s=Object(r["a"])(l["default"],o["a"],o["b"],!1,null,"7cae3e32",null);t["default"]=s.exports},"3f5d":function(e,t,a){"use strict";a.r(t);var o=a("975e"),l=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=l.a},"43d8":function(e,t,a){"use strict";a.r(t);var o=a("7b6f"),l=a("721f");for(var i in l)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(i);var r=a("2877"),s=Object(r["a"])(l["default"],o["a"],o["b"],!1,null,"4c864444",null);t["default"]=s.exports},"721f":function(e,t,a){"use strict";a.r(t);var o=a("7380"),l=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=l.a},7380:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a("4744");t.default={data:function(){return{title:"",dialogVisible:!1,form:{Settings_Name:"",Custome_Keys:"",Custome_Value:"",Remark:"",fileList:[]},rules:{Settings_Name:[{required:!0,message:"请输入偏好名称",trigger:"blur"}],Custome_Keys:[{required:!0,message:"请输入自定义键",trigger:"blur"}],Custome_Value:[{required:!0,message:"请输入自定义值",trigger:"blur"}]}}},methods:{dialogOpen:function(e,t){this.dialogVisible=!0,this.title=e,"edit"===e&&(this.form=Object.assign({},t))},handleClose:function(){this.form={Settings_Name:"",Custome_Keys:"",Custome_Value:"",Remark:""},this.dialogVisible=!1},uploadSuccess:function(e,t,a){this.form.Custome_Value=e.Data.split("*")[0]},showUpdate:function(){return"Home_Page_Url"!==this.form.Custome_Keys&&"Web_Logo"!==this.form.Custome_Keys},dialogSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return e.$message({type:"error",message:"请填完表单在提交"}),!1;(0,o.SavePreferenceSetting)(e.form).then((function(t){!0===t.IsSucceed?(e.$message({type:"success",message:"操作成功"}),e.$emit("changeTable"),e.form={Settings_Name:"",Custome_Keys:"",Custome_Value:"",Remark:""},e.dialogVisible=!1):e.$message({message:t.Message,type:"error"})}))}))}}}},"7b6f":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"add"===e.title?"新增偏好":"修改偏好",visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"偏好名称",prop:"Settings_Name"}},[a("el-input",{attrs:{disabled:"add"!==e.title},model:{value:e.form.Settings_Name,callback:function(t){e.$set(e.form,"Settings_Name",t)},expression:"form.Settings_Name"}})],1),a("el-form-item",{attrs:{label:"自定义键",prop:"Custome_Keys"}},[a("el-input",{attrs:{disabled:"add"!==e.title},model:{value:e.form.Custome_Keys,callback:function(t){e.$set(e.form,"Custome_Keys",t)},expression:"form.Custome_Keys"}})],1),e.showUpdate()?a("el-form-item",{attrs:{label:"自定义值",prop:"Custome_Value"}},[a("el-input",{model:{value:e.form.Custome_Value,callback:function(t){e.$set(e.form,"Custome_Value",t)},expression:"form.Custome_Value"}})],1):[a("el-form-item",{attrs:{label:"自定义值",prop:"Custome_Value"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Custome_Value,callback:function(t){e.$set(e.form,"Custome_Value",t)},expression:"form.Custome_Value"}})],1),a("el-form-item",{attrs:{label:"",prop:""}},[a("el-upload",{staticClass:"upload-demo",attrs:{action:e.$store.state.uploadUrl,limit:1,accept:".jpg,.jpeg,.png,.webp,.JPG,.JPEG,.PNG","on-success":e.uploadSuccess,"file-list":e.form.fileList}},[a("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1)],1)],a("el-form-item",{attrs:{label:"说明",prop:"Remark"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:4,maxRows:6},disabled:"add"!==e.title},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],2),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.dialogSubmit}},[e._v("确 定")])],1)],1)},l=[]},"7dc2":function(e,t,a){"use strict";a("e392")},"975e":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=a("4744"),i=o(a("43d8"));t.default={name:"SystemSetting",components:{Dialog:i.default},data:function(){return{tableData:[]}},created:function(){var e=this;(0,l.GetPreferenceSettingList)({}).then((function(t){e.tableData=t.Data}))},methods:{handleClick:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.$refs.dialog.dialogOpen(e,t)},changeTable:function(){var e=this;(0,l.GetPreferenceSettingList)({}).then((function(t){e.tableData=t.Data}))}}}},b382:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"container cs-z-shadow"},[a("el-button",{staticStyle:{"margin-bottom":"20px"},attrs:{type:"primary"},on:{click:function(t){return e.handleClick("add")}}},[e._v("新增")]),a("el-table",{staticStyle:{width:"100%","min-height":"calc(100vh - 200px)"},attrs:{data:e.tableData,border:"","max-height":"760"}},[a("el-table-column",{attrs:{prop:"Settings_Name",label:"偏好名称",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Custome_Keys",label:"自定义键",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Custome_Value",label:"自定义值",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"Remark",label:"说明",align:"center",width:"500","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.handleClick("edit",t.row)}}},[e._v("修改")])]}}])})],1)],1),a("Dialog",{ref:"dialog",on:{changeTable:e.changeTable}})],1)},l=[]},e392:function(e,t,a){}}]);