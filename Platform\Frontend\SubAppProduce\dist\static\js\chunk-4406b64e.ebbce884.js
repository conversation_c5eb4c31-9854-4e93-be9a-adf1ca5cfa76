(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4406b64e"],{"09f4":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=r,Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(t,e,a){var r=i(),u=t-r,l=20,s=0;e="undefined"===typeof e?500:e;var c=function(){s+=l;var t=Math.easeInOutQuad(s,r,u,e);o(t),s<e?n(c):a&&"function"===typeof a&&a()};c()}},"17c8":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("333d")),i=a("ae4d");e.default={components:{Pagination:o.default},mixins:[i.operate],props:{isUpdate:{type:Boolean,default:!1}},data:function(){return{loading:!1,tableData:[],status:2,btnLoading:!1,PageInfo:{Page:1,PageSize:20,TotalCount:0}}},activated:function(){this.isUpdate&&this.fetchData()}}},"2ad8":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-drawer",{attrs:{title:"详情",size:"30%",visible:t.drawer,direction:"rtl"},on:{"update:visible":function(e){t.drawer=e}}},[a("span",{staticClass:"cs-title"},[t._v(t._s(t.detailInfo.Title))]),a("span",{staticClass:"cs-content"},[t._v(t._s(t.detailInfo.Content))]),a("el-divider"),a("div",{staticClass:"tb-wrapper"},[a("table",[a("thead",[a("tr",[a("th",[t._v("构件编号")]),a("th",[t._v("规格")]),a("th",[t._v("单重(kg)")]),a("th",[t._v("总重(kg)")]),a("th",[t._v("数量")])])]),a("tbody",t._l(t.detailList,(function(e,n){return a("tr",{key:n},[a("td",[t._v(t._s(e.Comp_Code))]),a("td",[t._v(t._s(e.Spec))]),a("td",[t._v(t._s(e.Weight))]),a("td",[t._v(t._s(e.Total_Weight))]),a("td",[t._v(t._s(e.Allocation_Count))])])})),0)])]),a("el-pagination",{attrs:{small:"","page-sizes":[30],"current-page":t.queryInfo.Page,layout:"prev, pager, next",total:t.total},on:{"update:currentPage":function(e){return t.$set(t.queryInfo,"Page",e)},"update:current-page":function(e){return t.$set(t.queryInfo,"Page",e)},"current-change":t.handleCurrentChange}})],1)},o=[]},"2df5":function(t,e,a){"use strict";a.r(e);var n=a("8cce"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},"3fdd":function(t,e,a){"use strict";a.r(e);var n=a("2ad8"),o=a("5718");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("64bc");var r=a("2877"),u=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"6c893c36",null);e["default"]=u.exports},"403c":function(t,e,a){"use strict";a("e9f7")},"4c1ec":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-tabs",{staticClass:"cs-tabs",attrs:{type:"card"},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"未读",name:"first"}},[a("keep-alive",["first"===t.activeName?a("unread",{attrs:{"is-update":t.isUpdate},on:{"update:isUpdate":function(e){t.isUpdate=e},"update:is-update":function(e){t.isUpdate=e},detail:t.detail}}):t._e()],1)],1),a("el-tab-pane",{attrs:{label:"已读",name:"second"}},[a("keep-alive",["second"===t.activeName?a("read",{attrs:{"is-update":t.isUpdate},on:{detail:t.detail}}):t._e()],1)],1)],1),t.drawer?a(t.currentComponent,{ref:"details",tag:"component",attrs:{show:t.drawer},on:{"update:show":function(e){t.drawer=e}}}):t._e()],1)},o=[]},"4c61":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"log-inner-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{size:"large",data:t.tableData,stripe:""}},[a("el-table-column",{attrs:{align:"center",type:"index",width:"50"}}),a("el-table-column",{attrs:{align:"center",prop:"Title",label:"标题"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.Title))])]}}])}),a("el-table-column",{attrs:{align:"center",prop:"Content",label:"内容"}}),a("el-table-column",{attrs:{align:"center",prop:"Bill_No",label:"单据号"}}),a("el-table-column",{attrs:{align:"center",prop:"Remark",label:"备注"}}),a("el-table-column",{attrs:{align:"center",label:"创建时间"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])})],1),t.PageInfo.TotalCount>0?a("pagination",{attrs:{total:t.PageInfo.TotalCount,page:t.PageInfo.Page,limit:t.PageInfo.PageSize},on:{"update:page":function(e){return t.$set(t.PageInfo,"Page",e)},"update:limit":function(e){return t.$set(t.PageInfo,"PageSize",e)},pagination:t.fetchData}}):t._e()],1)},o=[]},5718:function(t,e,a){"use strict";a.r(e);var n=a("c3dd"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},"584c":function(t,e,a){},"5f54":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"log-inner-box"},[a("el-row",{attrs:{type:"flex",justify:"end"}},[a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{"text-align":"right","margin-right":"20px"}},[t._e(),t._e()],1)])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticStyle:{width:"100%"},attrs:{stripe:"",size:"large",data:t.tableData},on:{"selection-change":t.handleSelectionChange,select:t.handleSelect,"select-all":t.selectAll}},[a("el-table-column",{attrs:{align:"center",type:"index",width:"50"}}),a("el-table-column",{attrs:{align:"center",prop:"Title",label:"标题"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("span",[t._v(t._s(n.Title))])]}}])}),a("el-table-column",{attrs:{align:"center",prop:"Content",label:"内容"}}),a("el-table-column",{attrs:{align:"center",prop:"Bill_No",label:"单据号"}}),a("el-table-column",{attrs:{align:"center",prop:"Remark",label:"备注"}}),a("el-table-column",{attrs:{align:"center",label:"创建时间"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])}),a("el-table-column",{attrs:{prop:"",label:"操作",width:"120",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleSubmit2(n.Id)}}},[t._v("已读")])]}}])})],1),t.PageInfo.TotalCount>0?a("pagination",{attrs:{total:t.PageInfo.TotalCount,page:t.PageInfo.Page,limit:t.PageInfo.PageSize},on:{"update:page":function(e){return t.$set(t.PageInfo,"Page",e)},"update:limit":function(e){return t.$set(t.PageInfo,"PageSize",e)},pagination:t.fetchData}}):t._e()],1)},o=[]},"64bc":function(t,e,a){"use strict";a("584c")},"8cce":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("14d9"),a("e9f5"),a("7d54"),a("d3b7"),a("25f0"),a("159b");var o=n(a("333d")),i=a("ae4d"),r=a("4f7e");e.default={components:{Pagination:o.default},mixins:[i.operate],props:{isUpdate:{type:Boolean,default:!1}},data:function(){return{loading:!1,btnLoading:!1,btnAllLoading:!1,tableData:[],status:1,multipleSelection:[],PageInfo:{Page:1,PageSize:20,TotalCount:0}}},activated:function(){this.$emit("update:isUpdate",!1)},methods:{handleSubmit:function(t,e){var a=this,n="",o=[];e?this.$confirm("是否将所有消息标为已读?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n="",a.btnAllLoading=!0,a.submitInfo(n,e)})).catch((function(){a.$message({type:"info",message:"已取消全部已读"})})):(this.multipleSelection.forEach((function(t,e){o.push(t.Id)})),n=o.toString(),this.btnLoading=!0,this.submitInfo(n,e))},handleSubmit2:function(t){this.submitInfo(t,!1)},submitInfo:function(t,e){var a=this;(0,r.UpdateNoticeStatus)({noticeId:t,status:"Is_Read",value:!0,is_all:e}).then((function(t){t.IsSucceed&&(a.$message({message:"修改成功",type:"success"}),a.fetchData(),a.$emit("update:isUpdate",!0)),a.btnLoading=!1,a.btnAllLoading=!1}))},handleSelectionChange:function(t){this.multipleSelection=t},selectAll:function(){this.$refs.table.clearSelection()},handleSelect:function(t,e){if(t.length>1){var a=t.shift();this.$refs.table.toggleRowSelection(a,!1)}}}}},9188:function(t,e,a){},9646:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("ba518")),i=n(a("a576")),r=n(a("3fdd"));e.default={name:"SysMessage",components:{read:o.default,unread:i.default,ProTaskDetail:r.default},data:function(){return{activeName:"first",isUpdate:!1,drawer:!1,currentComponent:""}},methods:{detail:function(t){var e=this;this.currentComponent="ProTaskDetail",this.drawer=!0,this.$nextTick((function(a){"ProduceTask"===t.Type&&e.$refs["details"].init(t)}))}}}},"9ca7":function(t,e,a){"use strict";a.r(e);var n=a("4c1ec"),o=a("da4c");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("ee8b");var r=a("2877"),u=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"0e7acb2e",null);e["default"]=u.exports},a576:function(t,e,a){"use strict";a.r(e);var n=a("5f54"),o=a("2df5");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("e36f");var r=a("2877"),u=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"6b5a7799",null);e["default"]=u.exports},a675:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetAllocationAppMessage=r,e.GetAllocationLogPageList=i,e.GetProductionCompPageList=s,e.GetProductionCompProjectInstallPageList=u,e.GetProductionPartPageList=c,e.GetProductionProjectInstallPageList=l,e.GetReadyCompPageList=d;var o=n(a("b775"));function i(t){return(0,o.default)({url:"/PRO/ProductionPDA/GetAllocationLogPageList",method:"post",data:t})}function r(t){return(0,o.default)({url:"/PRO/ProductionPDA/GetAllocationAppMessage",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionCompProjectInstallPageList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionProjectInstallPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionCompPageList",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/ProductionPDA/GetProductionPartPageList",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/ProductionPDA/GetReadyCompPageList",method:"post",data:t})}},ae4d:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.operate=void 0;var o=n(a("5530")),i=a("4f7e");a("ed08"),e.operate={mounted:function(){this.fetchData()},methods:{fetchData:function(){var t=this;this.loading=!0,(0,i.GetNoticePageList)((0,o.default)({status:this.status},this.PageInfo)).then((function(e){if(e.IsSucceed){var a=e.Data,n=a.Data,o=a.Page,i=a.PageSize,r=a.TotalCount;1===t.status&&t.$store.dispatch("sysInfo/changeReadNNum",e.Data.TotalCount),t.tableData=n,t.PageInfo.PageSize=i,t.PageInfo.Page=o,t.PageInfo.TotalCount=r,t.loading=!1}}))},handleDetail:function(t){this.$emit("detail",t)}}}},ba518:function(t,e,a){"use strict";a.r(e);var n=a("4c61"),o=a("be7c");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("403c");var r=a("2877"),u=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"2fc8de4f",null);e["default"]=u.exports},be7c:function(t,e,a){"use strict";a.r(e);var n=a("17c8"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},c3dd:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("c14f")),i=n(a("1da1")),r=n(a("333d")),u=a("a675");e.default={name:"ProTaskDetail",components:{Pagination:r.default},props:{show:{type:Boolean,default:!1}},data:function(){return{detailList:[],detailInfo:{Title:"",Content:""},total:0,queryInfo:{Page:0,PageSize:30,Code:"",Installunit_Id:""}}},computed:{drawer:{get:function(){return this.show},set:function(t){this.$emit("update:show",t)}}},methods:{init:function(t){var e=this;return(0,i.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return e.detailInfo=t,Object.assign(e.detailInfo,t),e.drawer=!0,a.n=1,e.fetchData();case 1:e.getTbInfo();case 2:return a.a(2)}}),a)})))()},fetchData:function(){var t=this;return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,u.GetAllocationAppMessage)({id:t.detailInfo.Id}).then((function(e){if(e.IsSucceed){var a=e.Data,n=a.Install_UnitId,o=a.Code;t.queryInfo.Installunit_Id=n,t.queryInfo.Code=o}else t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},getTbInfo:function(){var t=this;this.detailList=[],(0,u.GetAllocationLogPageList)(this.queryInfo).then((function(e){e.IsSucceed?(t.detailList=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})}))},handleCurrentChange:function(t){this.queryInfo.Page=t,this.getTbInfo()}}}},da4c:function(t,e,a){"use strict";a.r(e);var n=a("9646"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},e36f:function(t,e,a){"use strict";a("9188")},e9f7:function(t,e,a){},ee8b:function(t,e,a){"use strict";a("f8fe")},f8fe:function(t,e,a){}}]);