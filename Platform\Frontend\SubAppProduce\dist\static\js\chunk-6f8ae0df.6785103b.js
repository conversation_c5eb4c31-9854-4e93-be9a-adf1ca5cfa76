(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6f8ae0df"],{2079:function(e,t,a){"use strict";a("8cd5")},"398d":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"stockin-details"},[a("div",{staticClass:"search-form"},[a("el-form",{ref:"form",attrs:{model:e.form,inline:!0,"label-width":"70px"}},[a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"构件名称",prop:"Component_Codes_Format"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入（空格间隔筛选多个）",clearable:""},model:{value:e.form.Component_Codes_Format,callback:function(t){e.$set(e.form,"Component_Codes_Format",t)},expression:"form.Component_Codes_Format"}})],1),a("el-form-item",{attrs:{label:"构件名称(模糊)",prop:"Search_Component_Codes_Format","label-width":"110px"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Search_Component_Codes_Format,callback:function(t){e.$set(e.form,"Search_Component_Codes_Format",t)},expression:"form.Search_Component_Codes_Format"}})],1),a("el-form-item",{attrs:{label:"构件类型",prop:"Component_Type"}},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"请选择"},model:{value:e.form.Component_Type,callback:function(t){e.$set(e.form,"Component_Type",t)},expression:"form.Component_Type"}},e._l(e.TypeData,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Name,value:e.Code}})})),1)],1),a("el-form-item",{attrs:{label:"直发件",prop:"Is_Component"}},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"请选择"},model:{value:e.form.Is_Component,callback:function(t){e.$set(e.form,"Is_Component",t)},expression:"form.Is_Component"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1),a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length",t)},expression:"form.Length"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Texture"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Texture,callback:function(t){e.$set(e.form,"Texture",t)},expression:"form.Texture"}})],1),a("el-form-item",[a("el-button",{on:{click:function(t){return e.resetSearch()}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")])],1)],1)],1),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",data:e.tbData,"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",resizable:"","tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checkMethod}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"sort-change":e.setFilter}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox"}}),e._l(e.columns,(function(t,o){return["Is_Component_Name"===t.Code?a("vxe-column",{key:o,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"",field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return["直发件"==o.Is_Component_Name?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}],null,!0)}):"SteelName"===t.Code?a("vxe-column",{key:o,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"",field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[o.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),e._v(" "+e._s(o.SteelName)+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"",field:t.Code,title:t.Display_Name,sortable:"","min-width":t.Width,align:t.Align}})]}))],2)],1),a("Pagination",{staticClass:"pagination",attrs:{total:e.queryInfo.TotalCount,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}}),a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1)],1)},n=[]},"4a42":function(e,t,a){"use strict";a.r(t);var o=a("8a65"),n=a("dc70");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("9d59");var r=a("2877"),s=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"c15c1c70",null);t["default"]=s.exports},"776b":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("5530")),i=o(a("c14f")),r=o(a("1da1"));a("99af"),a("7db0"),a("d81d"),a("4e82"),a("e9f5"),a("d866"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("ac1f"),a("466d"),a("5319"),a("498a"),a("159b");var s=a("6186"),l=a("4d7a"),c=a("2e8a"),d=a("3f35"),u=a("3166"),f=o(a("333d")),m=a("c685"),h=a("7f9d");t.default={name:"PackingAddDetail",components:{Pagination:f.default},props:{formParams:{type:Object,default:function(){return{}}},componentIds:{type:String,default:""}},data:function(){return{tbLoading:!1,total:0,tablePageSize:m.tablePageSize,treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},form:{Search_Component_Codes_Format:"",Component_Codes_Format:"",Component_Codes:"",Component_Type:"",Is_Component:null,Spec:"",Length:"",Texture:"",Area_Id:""},TypeData:[],Is_Component_Data:[{Name:"是",Id:!0},{Name:"否",Id:!1}],tbConfig:{Is_Sortable:!0},columns:[],tbData:[],multiSelected:[],Details:[],queryInfo:{Page:1,TotalCount:0,PageSize:m.tablePageSize[0]},gridCode:"pro_wait_packing_list",ProfessionalCode:"",ProfessionalId:"",loading:!1}},computed:{},created:function(){},mounted:function(){var e=this;(0,l.getFactoryProfessional)().then((function(t){e.ProfessionalCode=t[0].Code,e.ProfessionalId=t[0].Id,e.getComponentTypeList(),e.getGridByCode()})),this.getAreaList()},methods:{checkMethod:function(e){var t=e.row;return!t.stopFlag},tbSelectChange:function(e){this.multiSelected=e.records},getGridByCode:function(){var e=this;(0,s.GetGridByCode)({Code:this.gridCode}).then((function(t){t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList.map((function(t){return t.Is_Sortable=!0,"SteelName"===t.Code&&(t.sortMethod=e.customSort),t}))),e.getWaitPack2ndPageList())}))},areaClear:function(){this.form.Area_Id=""},getStopList:function(e,t){var a=this;return(0,r.default)((0,i.default)().m((function o(){var n,r;return(0,i.default)().w((function(o){while(1)switch(o.n){case 0:if(n=e.map((function(e){return{Id:e[t],Type:2}})),!n.length){o.n=1;break}if(r=n.every((function(e){return!e.Id})),!r){o.n=1;break}return o.a(2);case 1:return o.n=2,(0,h.GetStopList)(n).then((function(o){if(o.IsSucceed){var n={};o.Data.forEach((function(e){n[e.Id]=!!e.Is_Stop})),e.forEach((function(e){n[e[t]]&&a.$set(e,"stopFlag",n[e[t]])}))}}));case 2:return o.a(2)}}),o)})))()},getComponentTypeList:function(){var e=this;(0,c.GetComponentTypeList)({Level:1,Category_Id:this.ProfessionalId,Factory_Id:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var a={};e.formParams.Type?(a=t.Data.find((function(t){return t.Code===e.formParams.Type})),e.TypeData=[a],e.form.Component_Type=a.Code):e.TypeData=t.Data}}))},getWaitPack2ndPageList:function(){var e=this;this.loading=!0;var t={},a="";3===this.formParams.Stock_Status&&(t={Type:1,Location_Id:this.formParams.Location_Id}),a=this.formParams.ProjectID?this.formParams.ProjectID:this.formParams.Sys_Project_Id,(0,d.GetWaitPack2ndPageList)((0,n.default)((0,n.default)((0,n.default)((0,n.default)({},this.queryInfo),this.form),t),{},{Sys_Project_Id:a,Component_Ids:this.componentIds})).then((function(t){t.IsSucceed&&e.setGridData(t.Data)}))},handleSearch:function(){var e=this.form.Component_Codes_Format.trim();e=e.replace(/\s+/g,"\n"),this.form.Component_Codes=e,this.queryInfo.Page=1,this.getWaitPack2ndPageList()},resetSearch:function(){this.$refs["form"].resetFields(),this.handleSearch()},pageChange:function(e){var t=e.page,a=e.limit,o=e.type;this.queryInfo.Page="limit"===o?1:t,this.queryInfo.PageSize=a,this.getWaitPack2ndPageList()},cancel:function(){this.$emit("dialogCancel")},setGrid:function(e){this.tbConfig=Object.assign({},e,{Height:420}),this.queryInfo.PageSize=Number(this.tbConfig.Row_Number)},setCols:function(e){this.columns=e.concat([])},setGridData:function(e){this.tbData=e.Data.map((function(e){return e.stopFlag=!1,e})),this.getStopList(this.tbData,"Import_Detail_Id"),this.queryInfo.TotalCount=e.TotalCount,this.loading=!1},gridSizeChange:function(e){e.page;var t=e.size;this.setGrid((0,n.default)((0,n.default)({},this.tbConfig),{},{Row_Number:t})),this.queryInfo.PageSize=t,this.queryInfo.Page=1,this.getWaitPack2ndPageList()},save:function(){if(this.multiSelected.length<=0)return this.$message.warning("请选择需要打包的构件明细");this.Details=this.multiSelected.map((function(e){return Object.assign({},e,{})})),this.$emit("dialogFormSubmitSuccess",{type:"merge",data:this.Details})},setFilter:function(e){var t=this,a=e.sortList;"SteelName"===a.field&&this.tbData.sort((function(e,a){return t.customSort(e,a)}))},customSort:function(e,t){function a(e){var t=e.match(/(\d+)(?!.*\d)/),a=e.replace(/\d+$/,"");return{stringPart:a,numberPart:t?parseInt(t[0]):null}}var o=a(e.SteelName),n=a(t.SteelName);return o.stringPart!==n.stringPart?o.stringPart.localeCompare(n.stringPart):o.numberPart!==n.numberPart?o.numberPart-n.numberPart:0},getAreaList:function(){var e=this,t=this.formParams.ProjectID?this.formParams.ProjectID:this.formParams.Sys_Project_Id;(0,u.GeAreaTrees)({sysProjectId:t}).then((function(t){if(t.IsSucceed){var a=t.Data;e.setDisabledTree(a),e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))}else e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var a=e.Children;a&&a.length?e.disabled=!0:(e.disabled=!1,t.setDisabledTree(a))}))}}}},"8a65":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("el-button",{staticStyle:{"margin-bottom":"16px"},on:{click:e.tagBack}},[e._v("返回")]),a("div",{staticClass:"sch-detail"},[a("div",{staticClass:"cs-custom-header"},[a("div",{staticClass:"header-text"},[e._v("打包件信息")]),a("div",{staticClass:"header-btns"},[a("el-button",{attrs:{loading:e.btnLoading,type:"primary",disabled:e.isClicked||e.form.Stock_Status>3},on:{click:e.save}},[e._v("保存")]),a("el-button",{on:{click:e.tagBack}},[e._v("取消")])],1)]),a("div",{staticClass:"form-search"},[a("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,"label-width":"105px"}},[a("el-form-item",{attrs:{label:"打包件类型:"}},[a("el-select",{ref:"StockStatusRef",attrs:{placeholder:"请选择",filterable:"",disabled:!0},model:{value:e.form.From_Stock_Status,callback:function(t){e.$set(e.form,"From_Stock_Status",t)},expression:"form.From_Stock_Status"}},e._l(e.Stock_Status_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"包名称:"}},[a("el-input",{attrs:{disabled:1!=e.mode},model:{value:e.form.PkgNO,callback:function(t){e.$set(e.form,"PkgNO",t)},expression:"form.PkgNO"}})],1),e.form.Stock_Status>=3?[a("el-form-item",{attrs:{label:"仓库:"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Warehouse_Name,callback:function(t){e.$set(e.form,"Warehouse_Name",t)},expression:"form.Warehouse_Name"}})],1),a("el-form-item",{attrs:{label:"库位:"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Location_Name,callback:function(t){e.$set(e.form,"Location_Name",t)},expression:"form.Location_Name"}})],1)]:e._e(),a("el-form-item",{attrs:{label:"项目:"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.ProjectName,callback:function(t){e.$set(e.form,"ProjectName",t)},expression:"form.ProjectName"}})],1),e.form.Type_Name||1==e.mode?[a("el-form-item",{attrs:{label:"构件类型:"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1)]:e._e(),e.form.Volume||1==e.mode?[a("el-form-item",{attrs:{label:"体积:"}},[a("el-input",{model:{value:e.form.Volume,callback:function(t){e.$set(e.form,"Volume",t)},expression:"form.Volume"}})],1)]:e._e(),e.form.DIM||1==e.mode?[a("el-form-item",{attrs:{label:"尺寸:"}},[a("el-input",{model:{value:e.form.DIM,callback:function(t){e.$set(e.form,"DIM",t)},expression:"form.DIM"}})],1)]:e._e(),a("el-form-item",{attrs:{label:"毛重系数:"}},[a("el-input",{attrs:{disabled:1!=e.mode},model:{value:e.form.Gross,callback:function(t){e.$set(e.form,"Gross",t)},expression:"form.Gross"}})],1),e.form.Departure||1==e.mode?[a("el-form-item",{attrs:{label:"起运港:"}},[a("el-input",{attrs:{disabled:1!=e.mode},model:{value:e.form.Departure,callback:function(t){e.$set(e.form,"Departure",t)},expression:"form.Departure"}})],1)]:e._e(),e.form.ContractNo||1==e.mode?[a("el-form-item",{attrs:{label:"项目合同编号:"}},[a("el-input",{attrs:{disabled:1!=e.mode},model:{value:e.form.ContractNo,callback:function(t){e.$set(e.form,"ContractNo",t)},expression:"form.ContractNo"}})],1)]:e._e(),a("el-form-item",{attrs:{label:"收件人:"}},[a("el-input",{model:{value:e.form.Addressee,callback:function(t){e.$set(e.form,"Addressee",t)},expression:"form.Addressee"}})],1),e.form.Departure||1==e.mode?[a("el-form-item",{attrs:{label:"备注:"}},[a("el-input",{attrs:{disabled:1!=e.mode},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)]:e._e(),1==e.mode?[a("el-form-item",{attrs:{label:"包编号:"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.PackageSN,callback:function(t){e.$set(e.form,"PackageSN",t)},expression:"form.PackageSN"}})],1)]:e._e()],2)],1),a("div",{staticClass:"assistant-wrapper"},[a("div",[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{disabled:e.form.Stock_Status>3||3==e.form.Stock_Status&&0===e.form.From_Stock_Status,type:"primary"},on:{click:function(t){e.openDialog({title:"添加",component:"PackingAddDetail",width:"80%",props:{formParams:e.form,componentIds:e.tbData.map((function(e){return e.Component_Id})).toString()}})}}},[e._v("添加构件")]),1==e.mode?a("el-button",{attrs:{type:"info",disabled:e.multiSelected.length<=0||e.form.Stock_Status>3||3==e.form.Stock_Status&&0===e.form.From_Stock_Status},on:{click:e.releaseComponent}},[e._v("释放")]):a("el-button",{attrs:{disabled:e.multiSelected.length<=0},on:{click:e.deleteRows}},[e._v("删除构件")])],1),a("div",{staticClass:"total-wrapper"},[a("span",[a("strong",[e._v("合计: ")]),e._v("构件打包总数 "+e._s(e.totalAmount)+" 件，")]),a("span",[e._v(e._s(e.totalWeight)+" "+e._s(e.Unit))])])])]),a("div",{staticClass:"twrap"},[a("div",{staticStyle:{height:"100%"}},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.tbData,"checkbox-config":{checkField:"checked",checkMethod:e.checkMethod},resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.multiSelectedChange,"checkbox-change":e.multiSelectedChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["SteelAmount"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"","edit-render":{},"min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"edit",fn:function(t){var o=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:o.Wait_Pack_Num,disabled:e.form.Stock_Status>3},model:{value:o.SteelAmount,callback:function(t){e.$set(o,"SteelAmount",e._n(t))},expression:"row.SteelAmount"}})]}},{key:"default",fn:function(t){var o=t.row;return[a("div",[e._v(" "+e._s(e._f("displayValue")(o.SteelAmount)))])]}}],null,!0)}):"Is_Component_Name"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",width:t.Width,"min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[a("div",["直发件"==o.Is_Component_Name?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])],1)]}}],null,!0)}):"SteelName"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"",width:t.Width,"min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[o.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),e._v(" "+e._s(o.SteelName)+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,width:t.Width,"min-width":t.Width,align:t.Align},scopedSlots:e._u([{key:"default",fn:function(o){var n=o.row;return[a("div",[e._v(" "+e._s(n[t.Code]?n[t.Code]:"-"))])]}}],null,!0)})]}))],2)],1)])]),a("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"plm-custom-dialog",attrs:{title:e.dialogCfgs.title,visible:e.dialogShow,width:e.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(t){e.dialogShow=t}}},[a("keep-alive",[e.dialogShow?a(e.dialogCfgs.component,e._b({tag:"component",on:{dialogCancel:e.dialogCancel,dialogFormSubmitSuccess:e.dialogFormSubmitSuccess}},"component",e.dialogCfgs.props,!1)):e._e()],1)],1)],1)},n=[]},"8cd5":function(e,t,a){},9694:function(e,t,a){},"9aae":function(e,t,a){"use strict";a.r(t);var o=a("776b"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a},"9d59":function(e,t,a){"use strict";a("9694")},a999:function(e,t,a){"use strict";a.r(t);var o=a("398d"),n=a("9aae");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);a("2079");var r=a("2877"),s=Object(r["a"])(n["default"],o["a"],o["b"],!1,null,"4fb4681b",null);t["default"]=s.exports},b536:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("c14f")),i=o(a("1da1"));a("99af"),a("4de4"),a("caad"),a("d81d"),a("13d5"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("2532"),a("c7cd"),a("159b");var r=a("6186"),s=a("2e8a"),l=o(a("0f97")),c=o(a("a999")),d=a("3f35"),u=a("4d7a"),f=a("ed08"),m=a("7f9d");t.default={name:"PackingAdd",components:{DynamicDataTable:l.default,PackingAddDetail:c.default},data:function(){return{tbLoading:!1,btnLoading:!1,isClicked:!1,confirmed:!1,warehouses:[],locations:[],tbConfig:{},columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:-1,PageSizes:[20,40,60,80,100]},form:{From_Stock_Status:null,Stock_Status:null,Warehouse_Id:"",Location_Id:"",ProjectName:"",Project_Id:"",Sys_Project_Id:"",PkgNO:"",TypeId:"",Type:"",ContractNo:"",Volume:"",DIM:"",Gross:null,Departure:"",Remark:"",PackageSN:""},Stock_Status_Data:[{Name:"工厂打包",Id:0},{Name:"仓库打包",Id:3},{Name:"仓库打包",Id:4}],TypeData:[],Details:[],multiSelected:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"50%"},gridCode:"pro_packing_detail_list",Proportion:0,Unit:"",totalAmount:0,totalWeight:0,Id:""}},computed:{mode:function(){return this.$router.currentRoute.query.mode}},mounted:function(){var e=this;this.Id=this.$router.currentRoute.query.Id,(0,u.getFactoryProfessional)().then((function(t){e.Proportion=t[0].Proportion,e.Unit=t[0].Unit,e.getComponentTypeList()}))},beforeRouteEnter:function(e,t,a){a((function(e){e.initRouterParams()}))},beforeRouteUpdate:function(e,t,a){this.initRouterParams(),a()},beforeRouteLeave:function(e,t,a){this.confirmed?a():this.$confirm("此操作不会保存已修改但尚未保存的数据，是否离开?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a()}))},created:function(){this.initRouterParams(),this.getGridByCode()},methods:{getComponentTypeList:function(){var e=this;(0,s.GetComponentTypeList)({Level:1,Category_Id:this.ProfessionalId,Factory_Id:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed&&(e.TypeData=t.Data)}))},getGridByCode:function(){var e=this;this.tbLoading=!0,(0,r.GetGridByCode)({Code:this.gridCode}).then((function(t){var a=t.IsSucceed,o=t.Data;t.Message;if(a){e.tbConfig=Object.assign({},e.tbConfig,o.Grid);var n=o.ColumnList||[];e.columns=n.filter((function(e){return e.Is_Display})).map((function(e){return 0==e.Width?e.minWidth=120:e.minWidth=e.Width,u.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),e})),e.tbLoading=!1,1==e.mode&&e.Id&&e.fetchData()}}))},fetchData:function(){var e=this;this.tbLoading=!0,(0,d.GetPacking2ndEntity)({id:this.Id}).then((function(t){t.IsSucceed&&e.setGridData(t.Data)})).catch(console.error).finally((function(){e.tbLoading=!1}))},checkMethod:function(e){var t=e.row;return!t.stopFlag},tagBack:function(){var e=this;this.$confirm("此操作不会保存数据，是否离开?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.confirmed=!0,(0,f.closeTagView)(e.$store,e.$route)})).catch((function(){e.$message({type:"info",message:"已取消"})}))},subWeight:function(){var e=0,t=0;this.tbData.length<=0?(e=0,t=0):(e=this.tbData.map((function(t){return t.SteelAmount+=e,t})).reduce((function(e,t){return e+t.SteelAmount}),0),t=this.tbData.map((function(e){return e.totalW=(e.SteelWeight||0)*(e.SteelAmount||0),e})).reduce((function(e,t){return e+t.totalW}),0)),this.totalAmount=e,this.totalWeight=Math.round(t/this.Proportion*1e3)/1e3},multiSelectedChange:function(e){e.checked;var t=this.$refs.xTable.getCheckboxRecords();this.multiSelected=t},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"SteelAmount"===t.field},initRouterParams:function(){if(1!=this.mode){var e=JSON.parse(sessionStorage.getItem("PackageParams"));this.form=Object.assign({},this.form,e)}},setGridData:function(e){var t=this;this.form=e.Entity,this.tbData=e.Details,this.getStopList(this.tbData,"Component_Id"),this.TypeData.forEach((function(e){e.Code==t.form.Type&&(t.form.Type_Name=e.Name)})),this.subWeight()},getStopList:function(e,t){var a=this;return(0,i.default)((0,n.default)().m((function o(){var i;return(0,n.default)().w((function(o){while(1)switch(o.n){case 0:return i=e.map((function(e){return{Id:e[t],Type:2}})),o.n=1,(0,m.GetStopList)(i).then((function(o){if(o.IsSucceed){var n={};o.Data.forEach((function(e){n[e.Id]=!!e.Is_Stop})),e.forEach((function(e){n[e[t]]&&a.$set(e,"stopFlag",n[e[t]])}))}}));case 1:return o.a(2)}}),o)})))()},openDialog:function(e){e&&"[object Object]"===Object.prototype.toString.call(e)||(e={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,e,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(e){var t=e.type,a=e.data;switch(this.dialogCancel(),t){case"merge":this.mergeEntity(a);break}},mergeEntity:function(e){e.map((function(e){return e.SteelAmount=e.Wait_Pack_Num,e})),this.Details=e,this.tbData=this.tbData.concat(e),this.pageInfo.TotalCount=this.tbData.length,this.subWeight()},gridPageChange:function(e){var t=e.page;this.pageInfo.Page=Number(t),this.fetchData()},gridSizeChange:function(e){var t=e.size;this.tbConfig.Row_Number=Number(t),this.pageInfo.PageSize=Number(t),this.pageInfo.Page=1,this.fetchData()},handlePageChange:function(e){var t=e.currentPage,a=e.pageSize;this.pageInfo.Page=t,this.pageInfo.PageSize=a,this.fetchData()},deleteRows:function(){var e=this;this.multiSelected.forEach((function(t){var a=t.Component_Id;e.tbData=e.tbData.filter((function(e){return a!==e.Component_Id}))})),this.subWeight()},releaseComponent:function(){this.multiSelected.length==this.tbData.length?this.releasePackage():(this.deleteRows(),this.save())},releasePackage:function(){var e=this;(0,d.UnzipPacking2nd)({Ids:this.Id}).then((function(t){t.IsSucceed?(e.confirmed=!0,""===t.Message?e.$message.success("批量释放成功"):e.$message.success(t.Message),(0,f.closeTagView)(e.$store,e.$route)):e.$message.warning(t.Message||"")}))},save:function(){var e=this;this.isClicked=!0,this.btnLoading=!0,this.form.ProjectID=this.form.Sys_Project_Id,(0,d.SavePacking2nd)({Entity:this.form,Details:this.tbData}).then((function(t){t.IsSucceed?(e.confirmed=!0,e.$message.success(t.Message||""),(0,f.closeTagView)(e.$store,e.$route)):(e.isClicked=!1,e.btnLoading=!1,e.$message.warning(t.Message||""))})).catch((function(){e.$message({type:"info",message:res.Message}),e.btnLoading=!1}))},formatSaveData:function(){var e=this,t={entity:{},details:[]};return Object.keys(this.entity).forEach((function(a){"details"!==a&&(t.entity[a]=e.entity[a])})),t.details=this.entity.details,t}}}},dc70:function(e,t,a){"use strict";a.r(t);var o=a("b536"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=n.a}}]);