(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-481cb5ca"],{"0cda":function(t,e,a){"use strict";a.r(e);var o=a("3829"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);e["default"]=n.a},"1a0d":function(t,e,a){"use strict";a.r(e);var o=a("a641"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);e["default"]=n.a},"1aec":function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("7db0"),a("a15b"),a("d81d"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("159b");var n=o(a("4f2b")),r=o(a("b775")),i=(a("c9d9"),a("ed08"));e.default={name:"PrintDialog",props:{label:{type:String,default:""},items:{type:Array,default:function(){return[]}},tmpid:{type:String,default:""},type:{type:String,default:"main"}},data:function(){return{apis:{GetTemplatePageList:"/PRO/PrintTemplate/GetTemplatePageList",GetPrintTemplateList:"/PRO/PrintTemplate/GetPrintTemplateList",GetTemplteData:"/PRO/PrintTemplate/GetTemplteData"},tmpls:[],current:"",query:{PageSize:12,Page:1,TotalCount:0},dialogVisible:!1}},computed:{tmpl:function(){var t,e=this;return null!==(t=this.tmpls.find((function(t){return t.Id===e.current})))&&void 0!==t?t:{}}},mounted:function(){(0,n.default)()},created:function(){var t=this;this.current=this.tmpid,this.tmpid?this.GetPrintTemplateList({}).then((function(e){if(e.IsSucceed){var a,o=e.Data.find((function(e,a){if(e.Id===t.tmpid)return{item:o,index:a+1}}));if(o)a=e.Data.length<=t.query.PageSize?1:o.i%t.query.PageSize>0?parseInt(o.i/t.query.PageSize)+1:parseInt(o.i/t.query),t.itemOnPage=a}})).then((function(){t.getTemplates()})):this.getTemplates()},methods:{GetTemplatePageList:function(t){return(0,r.default)({url:this.apis.GetTemplatePageList,method:"post",data:t})},GetPrintTemplateList:function(t){return(0,r.default)({url:this.apis.GetPrintTemplateList,method:"post",data:t})},getTemplates:function(){var t=this;this.GetTemplatePageList(this.query).then((function(e){e.IsSucceed&&(e.Data.Data.forEach((function(e){e.thumbnail_url=(0,i.combineURL)(t.$baseUrl,e.thumbnail_url)})),t.tmpls=e.Data.Data,t.query.TotalCount=e.Data.TotalCount)}))},selectTmpl:function(t){this.current=t},getTemplteData:function(){return(0,r.default)({url:this.apis.GetTemplteData,method:"post",params:{id:this.tmpl.Id}})},printme:function(){var t=this;if(!this.tmpl.Id)return this.$message.warning("请先选择打印模板");this.dialogVisible=!0,this.$nextTick((function(e){t.showPrintModel(t.tmpl)}))},handleClose:function(){this.dialogVisible=!1},handlePrint:function(){var t;this.$print(this.$refs.print),(0,r.default)({url:"/PRO/Component/UpdateComponentPrinted",method:"post",params:{ids:null===(t=this.items.map((function(t){return t.Id})))||void 0===t?void 0:t.join(",")}})},getUrl:function(t){return(0,i.combineURL)(this.$baseUrl,t)},showPrintModel:function(t){rubylong.grhtml5.barcodeURL=this.getUrl("/PRO/PrintTemplate/GetBarcode");var e,a=this.getUrl("/PRO/PrintTemplate/GetTemplteData?id="+t.Id),o=this.getUrl("/PRO/PrintTemplate/GetReportData?templateName=".concat(t.Display_Name,"&keyValue=").concat(this.items.map((function(t){return t.Id})).toString(),"&type=").concat(this.type));e=rubylong.grhtml5.insertReportViewer("report_print",a,o),e.start()}}}},"2dd9":function(t,e,a){"use strict";function o(t,e){t||(t={}),e||(e=[]);var a=[],o=function(o){var n;n="[object Array]"===Object.prototype.toString.call(t[o])?t[o]:[t[o]];var r=n.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(r.filter((function(t){return null!==t})).length<=0&&(r=null),r){var i={Key:o,Value:r,Type:"",Filter_Type:""},l=e.find((function(t){return t.Code===o}));i.Type=null===l||void 0===l?void 0:l.Type,i.Filter_Type=null===l||void 0===l?void 0:l.Filter_Type,a.push(i)}};for(var n in t)o(n);return a}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=o,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("25f0")},"32a2":function(t,e,a){"use strict";a("bcf4")},3319:function(t,e,a){},3829:function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("ddb0");var n=o(a("0f97")),r=a("209b"),i=a("6186"),l=o(a("b775")),s=o(a("6e3b")),c=o(a("7491")),u=a("2dd9"),d=o(a("a7d8")),f=a("ed08"),m=o(a("83b4"));e.default={name:"PROInventoryPackage",mixins:[m.default],components:{DynamicDataTable:n.default,PackingDelBox:c.default,AddPackDialog:s.default,PrintDialog:d.default},data:function(){return{searchHeight:0,form:{InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Area_Id:"",AreaPosition:"",Warehouse_Id:"",Location_Id:"",Code:"",SearchDate:""},pickerOptions:{shortcuts:[{text:"今天",onClick:function(t){var e=new Date,a=new Date;t.$emit("pick",[a,e])}},{text:"最近一周",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-6048e5),t.$emit("pick",[a,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,a=new Date;a.setTime(a.getTime()-2592e6),t.$emit("pick",[a,e])}}]},addPageArray:[{path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-2829bdad").then(a.bind(null,"21fa"))},name:"PROPackingDetail",meta:{title:"打包件详情"}}],warehouses:[],locations:[],gridCode:"pro_packing_list",loading:!1,apis:{},tbConfig:{},columns:[],data:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},ParameterJson:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},checkedRows:[],installOption:[],installName:""}},beforeRouteEnter:function(t,e,a){a((function(t){t.tbConfig.Data_Url&&t.getTableData()}))},mounted:function(){this.searchHeight=this.$refs.searchDom.offsetHeight+200},created:function(){var t=this;Promise.all([(0,r.GetWarehouseListOfCurFactory)({}).then((function(e){e.IsSucceed&&(t.warehouses=e.Data)}))]).then((function(){(0,i.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData().then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1}))}))}))},methods:{installNameChange:function(t){this.$refs.table.searchedField["InstallUnit_Name"]=t,this.$refs.table.showSearch()},wareChange:function(t){var e=this;this.form.Location_Id="",(0,r.GetLocationList)({Warehouse_Id:t}).then((function(t){t.IsSucceed&&(e.locations=t.Data)}))},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:200}),this.filterData.PageSize=Number(this.tbConfig.Row_Number)},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},setCols:function(t){this.columns=t,this.columns=this.columns.map((function(t){return t.Is_Sortable=t.Is_Sort,t}))},handleSearch:function(){},getTableData:function(){return this.dynTblOptBak&&this.resetDynTblOpts(),this.tbConfig.Data_Url?(0,l.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{ParameterJson:(0,u.setParameterJson)(this.fiterArrObj,this.columns)})}):Promise.reject("invalid data api...")},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange(1)},filterChange:function(t){var e=this;this.filterData.Page=null!==t&&void 0!==t?t:1,this.getTableData().then((function(t){t.IsSucceed&&e.setGridData(t.Data)}))},gridPageChange:function(t){var e=t.page;this.filterData.Page=Number(e),this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=Number(e),this.filterData.PageSize=Number(e),this.filterData.Page=1,this.filterChange(1)},openPageTab:function(t,e,o){var n=o.params,r=o.query;this.addPageArray.push({path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-2d0e2790"),a.e("chunk-6f8ae0df"),a.e("chunk-75129aeb")]).then(a.bind(null,"4a42"))},name:"PROPackingAdd",meta:{title:"Add"==e?"新增打包件":"编辑打包件"}}),this.initPage(this.$route.name),sessionStorage.setItem("PackageParams",JSON.stringify(n)),this.$router.push({name:t,query:Object.assign({},r,{pg_redirect:this.$route.name}),params:n})},initPage:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.addPageArray,a=this.$router.getRoutes().find((function(e){return e.name===t}));a.parent&&(0,f.handleAddRouterPage)(e,a.parent.name,this.$route.path)},newPack:function(t){this.openDialog({title:"选择项目",width:"420px",component:"AddPackDialog",props:{type:t}})},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.stockin,a=t.type;this.dialogCancel(),this.openPageTab("PROPackingAdd","Add",{query:{type:a},params:e})},deleteRow:function(t){var e=this;if("待入库"===t.Stock_Status_Name||t.Is_Component)if("待入库"===t.Stock_Status_Name)this.$confirm("此操作将删除当前包, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,r.UnzipPacking)({id:t.Id,locationId:t.Location_Id}).then((function(a){a.IsSucceed?(e.$message.success(a.Message||""),e.data=e.data.filter((function(e){return e.Id!==t.Id}))):e.$message.warning(a.Message||"")}))})).catch((function(){e.$message({type:"info",message:"已取消"})}));else{if("已入库"!==t.Stock_Status_Name)return;this.openDialog({title:"删除",width:"420px",component:"PackingDelBox",props:{row:t,warehouses:this.warehouses}})}},multiSelectedChange:function(t){this.checkedRows=t}}}},3884:function(t,e,a){"use strict";a.r(e);var o=a("1aec"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);e["default"]=n.a},"3f57":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 flex-pd16-wrap"},[a("div",{staticClass:"page-main-content cs-z-shadow"},[a("div",{staticStyle:{height:"100%"}},[a("div",{ref:"searchDom",staticClass:"form-search"},[a("el-form",{ref:"form",attrs:{model:t.form,inline:"","label-width":"80px"}},[a("el-form-item",{attrs:{label:"包编号"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:t.form.Code,callback:function(e){t.$set(t.form,"Code",e)},expression:"form.Code"}})],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},t._l(t.ProjectNameData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!t.form.Project_Id,"select-params":{clearable:!0},"tree-params":t.treeParamsArea},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{attrs:{disabled:!t.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:t.setupPositionChange},model:{value:t.form.InstallUnit_Id,callback:function(e){t.$set(t.form,"InstallUnit_Id",e)},expression:"form.InstallUnit_Id"}},t._l(t.SetupPositionData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"仓库名称"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:t.wareChange},model:{value:t.form.Warehouse_Id,callback:function(e){t.$set(t.form,"Warehouse_Id",e)},expression:"form.Warehouse_Id"}},t._l(t.warehouses,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位名称"}},[a("el-select",{attrs:{disabled:!t.form.Warehouse_Id,clearable:"",placeholder:"请选择"},model:{value:t.form.Location_Id,callback:function(e){t.$set(t.form,"Location_Id",e)},expression:"form.Location_Id"}},t._l(t.locations,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),a("el-button",{on:{click:function(e){t.$refs["form"].resetFields(),t.handleSearch()}}},[t._v("重置")])],1)],1)],1),a("div",{staticClass:"assistant-wrapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.newPack(1)}}},[t._v("导入打包")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.newPack(1)}}},[t._v("导出打包件")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.newPack(2)}}},[t._v("打包")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.newPack(2)}}},[t._v("批量释放")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){return t.newPack(2)}}},[t._v("打印二维码")])],1),a("div",{staticClass:"date-picker-wrapper"},[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},on:{change:t.dateChange},model:{value:t.form.SearchDate,callback:function(e){t.$set(t.form,"SearchDate",e)},expression:"form.SearchDate"}})],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"no-v-padding",style:{height:"calc(100vh - "+t.searchHeight+"px)",padding:0}},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{tableSearch:t.tableSearch,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange,multiSelectedChange:t.multiSelectedChange},scopedSlots:t._u([{key:"Create_Date",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Create_Date))+" ")]}},{key:"op",fn:function(e){var o=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openPageTab("PROPackingAdd","Edit",{query:{type:3===o.From_Stock_Status?2:1,mode:1,id:o.Id},params:o})}}},[t._v("编辑")]),a("el-divider",{attrs:{direction:"vertical"}}),"待入库"==o.Stock_Status_Name||"已入库"==o.Stock_Status_Name&&"构件包"===o.Is_Component_Name?[a("el-button",{staticClass:"txt-red",attrs:{type:"text"},on:{click:function(e){return t.deleteRow(o)}}},[t._v("删除")]),a("el-divider",{attrs:{direction:"vertical"}})]:t._e(),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openPageTab("PROPackingDetail","Detail",{query:{id:o.Id},params:{row:o}})}}},[t._v("查看详情")])]}}])})],1)],1)]),a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess,refresh:t.tableSearch}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},n=[]},"6e3b":function(t,e,a){"use strict";a.r(e);var o=a("bde4"),n=a("1a0d");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("8a5a");var i=a("2877"),l=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"41f4a739",null);e["default"]=l.exports},7491:function(t,e,a){"use strict";a.r(e);var o=a("c657"),n=a("ca0d");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var i=a("2877"),l=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,null,null);e["default"]=l.exports},"78b1":function(t,e,a){"use strict";a.r(e);var o=a("3f57"),n=a("0cda");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("32a2");var i=a("2877"),l=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"6f315fde",null);e["default"]=l.exports},"8a5a":function(t,e,a){"use strict";a("ed4b")},"9caf":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"my-print-tmpls"},[a("el-container",[a("el-header",[a("div",[t._v(" 已选"+t._s(t.label)+": "),a("strong",[t._v(t._s(t.items.length))])]),a("div",[t._v("标签模板: "+t._s(t.tmpl.Display_Name))])]),a("el-main",{staticStyle:{height:"400px"}},[a("ul",t._l(t.tmpls,(function(e){return a("li",{key:e.Id,class:{active:e.Id===t.current}},[a("div",{staticClass:"temp",on:{click:function(a){return t.selectTmpl(e.Id)}}},[a("img",{staticStyle:{width:"240px !important",height:"180px"},attrs:{src:t.getUrl(e.Thumbnail_Url),alt:e.Display_Name}})]),a("div",{staticClass:"title"},[t._v(" "+t._s(e.Display_Name)+" ")])])})),0)]),a("el-footer",{staticStyle:{"text-align":"center"}},[a("el-pagination",{attrs:{small:"",background:"",layout:"prev, pager, next",total:t.query.TotalCount}})],1),a("el-footer",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{type:"success",size:"small"},on:{click:t.printme}},[t._v("打印")])],1)],1),a("el-dialog",{attrs:{title:"提示",visible:t.dialogVisible,width:"50%","append-to-body":""},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[a("div",{ref:"print",staticClass:"qrcode-box print-result result",attrs:{id:"report_print"}}),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handlePrint}},[t._v("确 定")])],1)])],1)},n=[]},a641:function(t,e,a){"use strict";var o=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var n=o(a("c14f")),r=o(a("1da1")),i=a("2e8a"),l=a("4d7a"),s=o(a("83b4")),c=o(a("33cf")),u=a("2c08");e.default={name:"AddPackDialog",mixins:[s.default,c.default],props:{},data:function(){return{TypeData:[],ProjectNameData:[],form:{From_Stock_Status:null,Stock_Status:null,Warehouse_Id:"",Location_Id:"",ProjectName:"",Project_Id:"",Sys_Project_Id:"",PkgNO:"",TypeId:"",Type:"",ContractNo:"",Volume:"",DIM:"",Gross:0,Departure:"",Remark:""},consigneeName:"",Stock_Status_Data:[{Name:"工厂打包",Id:0},{Name:"仓库打包",Id:3}],btnLoading:!1,ProfessionalCode:"",ProfessionalId:""}},computed:{},watch:{"form.From_Stock_Status":function(t,e){3===t?(this.form.Warehouse_Id=this.queryObj.Warehouse_Id,this.wareChange(this.form.Warehouse_Id),this.form.Location_Id=this.queryObj.Location_Id):(this.form.Warehouse_Id="",this.form.Location_Id="")}},mounted:function(){var t=this;return(0,r.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,u.getQueryParam)(["Project_Id","Sys_Project_Id","ProjectName","Warehouse_Id","Location_Id","From_Stock_Status"],"steelPack");case 1:t.queryObj=e.v,t.form.Project_Id=t.queryObj.Project_Id,t.form.Sys_Project_Id=t.queryObj.Sys_Project_Id,t.form.ProjectName=t.queryObj.ProjectName,t.form.Warehouse_Id=t.queryObj.Warehouse_Id,t.form.Location_Id=t.queryObj.Location_Id,t.form.From_Stock_Status=t.queryObj.From_Stock_Status,(0,l.getFactoryProfessional)().then((function(e){t.ProfessionalCode=e[0].Code,t.ProfessionalId=e[0].Id,t.form.TypeId=e[0].Id,t.getComponentTypeList()}));case 2:return e.a(2)}}),e)})))()},created:function(){},methods:{getComponentTypeList:function(){var t=this;(0,i.GetComponentTypeList)({Level:1,Category_Id:this.ProfessionalId,Factory_Id:localStorage.getItem("CurReferenceId")}).then((function(e){e.IsSucceed&&(t.TypeData=e.Data)}))},steelTypeChange:function(){var t=this;this.$nextTick((function(){t.form.Type_Name=t.$refs["SteelTypeRef"].selected.currentLabel}))},Stock_Status_Change:function(t){var e=this;this.$nextTick((function(){e.form.Stock_Status_Name=e.$refs["StockStatusRef"].selected.currentLabel}))},cancel:function(){this.$emit("dialogCancel")},submit:function(){var t,e,a=this;return 0!=this.form.From_Stock_Status&&3!=this.form.From_Stock_Status?this.$message.warning("选择打包件类型"):this.form.PkgNO?this.form.Project_Id?3!=this.form.From_Stock_Status||this.form.Warehouse_Id&&this.form.Location_Id?(this.form.Stock_Status=this.form.From_Stock_Status,this.form.Addressee=this.consigneeName,this.btnLoading=!0,(0,u.addSearchLog)({Project_Id:this.form.Project_Id,Sys_Project_Id:this.form.Sys_Project_Id,ProjectName:this.form.ProjectName,From_Stock_Status:this.form.From_Stock_Status,Warehouse_Id:this.form.Warehouse_Id,Warehouse_Name:(null===(t=this.warehouses.find((function(t){return t.Id===a.form.Warehouse_Id})))||void 0===t?void 0:t.Display_Name)||"",Location_Name:(null===(e=this.locations.find((function(t){return t.Id===a.form.Location_Id})))||void 0===e?void 0:e.Display_Name)||"",Location_Id:this.form.Location_Id},"steelPack"),void this.$emit("dialogFormSubmitSuccess",{form:this.form})):this.$message.warning("必须选择仓库和库位"):this.$message.warning("必须选择项目"):this.$message.warning("必须输入包名称")}}}},a7d8:function(t,e,a){"use strict";a.r(e);var o=a("9caf"),n=a("3884");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("cc4f");var i=a("2877"),l=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"4186c322",null);e["default"]=l.exports},b6be:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var o=a("209b");e.default={name:"PackingDelBox",props:{row:{type:Object,default:function(){return{}}},warehouses:{type:Array,default:function(){return[]}}},data:function(){return{locations:[],form:{Warehouse_Id:"",Location_Id:""}}},created:function(){},methods:{cancel:function(){this.$emit("dialogCancel")},wareChange:function(t){var e=this;this.form.Location_Id="";this.warehouses.find((function(e){return e.Id===t}));(0,o.GetLocationList)({Warehouse_Id:t}).then((function(t){t.IsSucceed&&(e.locations=t.Data)}))},submit:function(){var t=this;this.$confirm("此操作将删除当前包, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,o.UnzipPacking)({id:t.row.Id,locationId:t.form.Location_Id}).then((function(e){e.IsSucceed?t.$message.success(e.Message||""):t.$message.warning(e.Message||""),t.$emit("dialogCancel"),t.$emit("refresh")}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))}}}},bcf4:function(t,e,a){},bde4:function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dialog-steps"},[a("div",{staticClass:"step"},[a("section",[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"100px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"打包件类型",required:""}},[a("el-select",{ref:"StockStatusRef",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},on:{change:t.Stock_Status_Change},model:{value:t.form.From_Stock_Status,callback:function(e){t.$set(t.form,"From_Stock_Status",e)},expression:"form.From_Stock_Status"}},t._l(t.Stock_Status_Data,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包名称",required:""}},[a("el-input",{model:{value:t.form.PkgNO,callback:function(e){t.$set(t.form,"PkgNO",e)},expression:"form.PkgNO"}})],1)],1)],1),3==t.form.From_Stock_Status?[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"仓库",required:""}},[a("el-select",{ref:"WarehouseRef",staticStyle:{width:"100%"},attrs:{placeholder:"请选择仓库"},on:{change:t.wareChange},model:{value:t.form.Warehouse_Id,callback:function(e){t.$set(t.form,"Warehouse_Id",e)},expression:"form.Warehouse_Id"}},t._l(t.warehouses,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"库位",required:""}},[a("el-select",{ref:"LocationRef",staticStyle:{width:"100%"},attrs:{placeholder:"请选择库位"},on:{change:t.locationChange},model:{value:t.form.Location_Id,callback:function(e){t.$set(t.form,"Location_Id",e)},expression:"form.Location_Id"}},t._l(t.locations,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1)],1)]:t._e(),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目",required:""}},[a("el-select",{ref:"ProjectName",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},on:{change:t.projectChangeSingle},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},t._l(t.ProjectNameData,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"构件类型"}},[a("el-select",{ref:"SteelTypeRef",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:""},on:{change:t.steelTypeChange},model:{value:t.form.Type,callback:function(e){t.$set(t.form,"Type",e)},expression:"form.Type"}},t._l(t.TypeData,(function(t){return a("el-option",{key:t.Code,attrs:{label:t.Name,value:t.Code}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"体积"}},[a("el-input",{model:{value:t.form.Volume,callback:function(e){t.$set(t.form,"Volume",e)},expression:"form.Volume"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"尺寸"}},[a("el-input",{model:{value:t.form.DIM,callback:function(e){t.$set(t.form,"DIM",e)},expression:"form.DIM"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"毛重系数"}},[a("el-input",{model:{value:t.form.Gross,callback:function(e){t.$set(t.form,"Gross",e)},expression:"form.Gross"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"起运港"}},[a("el-input",{model:{value:t.form.Departure,callback:function(e){t.$set(t.form,"Departure",e)},expression:"form.Departure"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目合同编号"}},[a("el-input",{model:{value:t.form.ContractNo,callback:function(e){t.$set(t.form,"ContractNo",e)},expression:"form.ContractNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"收件人"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.consigneeName,callback:function(e){t.consigneeName=e},expression:"consigneeName"}})],1)],1)],1),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",placeholder:"备注",maxlength:"150","show-word-limit":""},model:{value:t.form.Remark,callback:function(e){t.$set(t.form,"Remark",e)},expression:"form.Remark"}})],1)],2)],1)]),a("div",{staticClass:"step",staticStyle:{"text-align":"center"}},[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.submit}},[t._v("确定")])],1)])},n=[]},c657:function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stockin-details"},[a("p",{staticStyle:{"margin-top":"-12px",color:"#FB6B7F"}},[t._v(" 打包件将被删除，请重新选择包内物品存放位置 ")]),a("el-form",{ref:"form",staticStyle:{background:"#F2F2F2",padding:"16px 0"},attrs:{model:t.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"仓库"}},[a("el-select",{attrs:{placeholder:"请选择仓库"},on:{change:t.wareChange},model:{value:t.form.Warehouse_Id,callback:function(e){t.$set(t.form,"Warehouse_Id",e)},expression:"form.Warehouse_Id"}},t._l(t.warehouses,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位名称"}},[a("el-select",{attrs:{placeholder:"请选择库位"},model:{value:t.form.Location_Id,callback:function(e){t.$set(t.form,"Location_Id",e)},expression:"form.Location_Id"}},t._l(t.locations,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1),a("div",{staticStyle:{"text-align":"right","margin-top":"24px"}},[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确定")])],1)],1)},n=[]},ca0d:function(t,e,a){"use strict";a.r(e);var o=a("b6be"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);e["default"]=n.a},cc4f:function(t,e,a){"use strict";a("3319")},ed4b:function(t,e,a){}}]);