(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-54044e49"],{"188f":function(t,e,a){"use strict";a("7f2e")},"1a7a":function(t,e,a){},"209b":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CheckPackCode=s,e.ExportComponentStockInInfo=p,e.ExportPackingInInfo=v,e.ExportWaitingStockIn2ndList=R,e.FinishCollect=g,e.From_Stock_Status_TYPES=void 0,e.GetComponentStockInEntity=u,e.GetLocationList=i,e.GetPackingDetailList=f,e.GetPackingEntity=P,e.GetPackingGroupByDirectDetailList=d,e.GetStockInDetailList=c,e.GetStockMoveDetailList=h,e.GetWarehouseListOfCurFactory=r,e.HandleInventoryItem=C,e.PackingTypes=e.PackingStatus=e.InventoryComponentTypes=e.InventoryCheckStatus=e.InventoryCheckExceptions=void 0,e.RemoveMain=m,e.SaveComponentScrap=I,e.SaveInventory=y,e.SavePacking=k,e.SaveStockIn=l,e.SaveStockMove=S,e.StockInTypes=void 0,e.UnzipPacking=b,e.UpdateBillReady=_,e.UpdateMaterialReady=O;var o=n(a("b775"));n(a("4328")),e.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],e.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],e.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],e.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],e.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],e.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],e.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function r(t){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:t}})}function c(t,e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:t,isEdit:e}})}function s(t,e){return(0,o.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:t,id:e}})}function d(t){return(0,o.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:t}})}function v(t){return(0,o.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:t}})}function m(t){return(0,o.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:t}})}function k(t){return(0,o.default)({url:"/PRO/Packing/SavePacking",method:"post",data:t})}function b(t){var e=t.id,a=t.locationId;return(0,o.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:e,locationId:a}})}function P(t){var e=t.id,a=t.code;return(0,o.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:e,code:a}})}function h(t){return(0,o.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:t}})}function S(t){return(0,o.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:t})}function C(t){var e=t.id,a=t.type,n=t.value;return(0,o.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:e,type:a,value:n}})}function I(t){return(0,o.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:t}})}function _(t){var e=t.installId,a=t.isReady;return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:e,isReady:a}})}function O(t){return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:t})}},"33b0":function(t,e,a){"use strict";a.r(e);var n=a("8bc2"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=o.a},"521a":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("div",{staticStyle:{padding:"12px 0"}},[a("el-card",{staticClass:"h-card",attrs:{shadow:"never"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("header",{staticClass:"grid-content bg-purple"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),a("span",[t._v(t._s(t.row.Project_Name))])],1)]),a("el-col",{attrs:{span:18}},[a("label",[t._v("移库单号: ")]),t._v(" "),a("span",[t._v(t._s(t.row.Id))])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5,offset:1}},[a("label",[t._v("移库人员: ")]),t._v(" "),a("span",[t._v(t._s(t.row.Move_UserName))])]),a("el-col",{attrs:{span:5}},[a("label",[t._v("移库时间: ")]),t._v(" "),a("span",[t._v(t._s(t.formatDate(t.row.Move_Date,"yyyy-MM-dd")))])]),a("el-col",{attrs:{span:13}},[a("label",[t._v("备注: ")]),t._v(" "),a("span",[t._v(t._s(t.row.Remark))])])],1)],1)],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""}})],1)])])},o=[]},5506:function(t,e,a){"use strict";a.r(e);var n=a("521a"),o=a("33b0");for(var r in o)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(r);a("e1f9"),a("188f");var i=a("2877"),l=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"6921b8a2",null);e["default"]=l.exports},"7f2e":function(t,e,a){},"8bc2":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af");var o=n(a("0f97")),r=a("209b"),i=a("6f23"),l=a("6186"),u=a("ed08");e.default={name:"Detail",components:{DynamicDataTable:o.default},data:function(){return{gridCode:"pro_stock_move_detail_list",fiterArrObj:{},tbConfig:{},columns:[],data:[]}},computed:{row:function(){var t;return null===(t=this.$route.params)||void 0===t?void 0:t.row}},created:function(){var t=this;(0,l.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData()}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120})},setCols:function(t){this.columns=t.concat([])},setGridData:function(t){this.data=t.Data},tagBack:function(){(0,u.closeTagView)(this.$store,this.$route)},getTableData:function(){var t=this;this.tbConfig.Data_Url&&(0,r.GetStockMoveDetailList)(this.row.Id).then((function(e){e.IsSucceed&&t.setGridData(e)}))},formatDate:function(t,e){return(0,i.formatDate)(t,e)}}}},e1f9:function(t,e,a){"use strict";a("1a7a")}}]);