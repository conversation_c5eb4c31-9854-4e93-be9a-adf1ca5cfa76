(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-825fb472"],{"3e9e":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return r}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("label",[t._v("数据列表")]),a("div",{staticClass:"btn-x"},[t.getRoles("OMAProOutputDetailExport")?a("el-button",{attrs:{disabled:t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e()],1)]),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},r=[]},4434:function(t,e,a){"use strict";a("5149")},"4f39":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=i,e.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var r=n(a("53ca"));function i(t,e){if(0===arguments.length||!t)return null;var a,n=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,r.default)(t)?a=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),a=new Date(t));var i={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=n.replace(/{([ymdhisa])+}/g,(function(t,e){var a=i[e];return"a"===e?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var a=t.split("~"),n=i(new Date(a[0]),e)+" ~ "+i(new Date(a[1]),e);return n}return t&&t.length>0?i(new Date(t),e):void 0}},5149:function(t,e,a){},"5c02":function(t,e,a){"use strict";a.r(e);var n=a("3e9e"),r=a("b077");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("4434");var o=a("2877"),c=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"9e8a060c",null);e["default"]=c.exports},b077:function(t,e,a){"use strict";a.r(e);var n=a("bb94"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},bb94:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7");var r=n(a("c14f")),i=n(a("1da1")),o=n(a("ac03")),c=a("cf45"),l=a("4f39"),s=n(a("3502")),u=a("d7ff"),d=a("8ff5"),f=a("05e0");e.default={name:"OMAProductionOutputInfo",components:{VTable:s.default},mixins:[o.default],data:function(){return{form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[]}},computed:{curTitle:function(){return"".concat((0,l.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")}},mounted:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,t.fetchData(),e.n=1,(0,c.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},beforeCreate:function(){this.curModuleKey=d.curModuleKey},methods:{fetchData:function(){var t=this;this.checkDate()&&(this.loading=!0,(0,u.GetSCOutputDailyDetailList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[];var a=t.setTotalData(t.tableData,t.generateColumn()),n=a.column;t.columns=n,t.$refs["tb"].setColumns(n)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var t=180;return[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:5},field:"ProjectAbbreviation",minWidth:f.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:f.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:f.ProjectStatusW}]}]},{title:"生产调拨单价(元)",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"Produce_Allocate_Unit_Price",minWidth:80}]}]},{title:"公共成本摊销系数(%)",params:{rowSpan:2},fixed:"left",children:[{params:{none:"none"},children:[{params:{none:"none"},field:"PublicCostAmortizationFactor",minWidth:80}]}]},{title:"生产成品产值",children:[{title:"构件成品入库量(T)",children:[{minWidth:t,field:"Stock_In_Amount",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:t,field:"Stock_In_Price",title:0,isTotal:!0}]}]}]}}}},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=r,a("d3b7");var n=a("6186");function r(t){return new Promise((function(e,a){(0,n.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}}}]);