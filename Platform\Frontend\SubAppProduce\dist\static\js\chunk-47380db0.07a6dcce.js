(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-47380db0"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=r(),s=e-i,l=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=l;var e=Math.easeInOutQuad(u,i,s,t);o(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},"0b35":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,inline:"",rules:e.rules,"label-width":"100px"}},[a("h3",[e._v("基本信息")]),a("el-form-item",{attrs:{label:"班组名称："}},[e._v(" "+e._s(e.form.Name||"-")+" ")]),a("el-form-item",{attrs:{label:"班组长："}},[e._v(" "+e._s(e.form.Manager_UserName||"-")+" ")]),a("el-form-item",{attrs:{label:"负荷提醒线："}},[e._v(" "+e._s(e.form.Load||"-")+" ")]),e.Is_Workshop_Enabled?a("el-form-item",{attrs:{label:"所属车间："}},[e._v(" "+e._s(e.form.Workshop_Name||"-")+" ")]):e._e(),a("h3",[e._v("班组成员")]),a("div",{staticClass:"tag-x"},[a("div",{staticClass:"tag-wrapper"},e._l(e.tags,(function(t){return a("el-tag",{key:t.User_Id,attrs:{size:"large",type:"info"}},[e._v(" "+e._s(t.User_Name)+" ")])})),1)])],1)],1)},o=[]},"0fa9":function(e,t,a){"use strict";a.r(t);var n=a("6d88"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"156f":function(e,t,a){},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,s=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1ba7":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("caad"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("2532");var o=n(a("5530")),r=n(a("2909")),i=n(a("c14f")),s=n(a("1da1")),l=n(a("1463")),u=(n(a("0f97")),n(a("15ac"))),c=a("6186"),d=n(a("333d")),f=a("c685");t.default={components:{TreeDetail:l.default,Pagination:d.default},mixins:[u.default],props:{show:{type:Boolean,default:!1},tags:{type:Array,default:function(){return[]}}},data:function(){return{treeLoading:!1,expandedKey:"",queryInfo:{Page:1,PageSize:f.tablePageSize[0],Search:"",ParameterJson:[]},tablePageSize:f.tablePageSize,tbConfig:{Pager_Align:"center",Is_Reserve:!0},currentComponent:"",treeData:[],tableData:[],columns:[],total:0,tbLoading:!1,selectList:[],checkData:""}},computed:{dialogVisible:{get:function(){return this.show},set:function(e){this.$emit("update:show",e)}}},mounted:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,t.n=1,e.getTableConfig("sys_depart_list_dep");case 1:e.fetchTreeData();case 2:return t.a(2)}}),t)})))()},methods:{setSelectRow:function(e){var t=this;this.$nextTick((function(a){var n,o=e.map((function(e){return e.Id})),i=t.tableData.filter((function(e){return o.includes(e.Id)}));(n=t.$refs["xTable"]).setCheckboxRow.apply(n,(0,r.default)(i).concat([!0])),t.selectList=i}))},fetchTreeData:function(e){var t=this;this.treeLoading=!0,(0,c.GetDepartmentTreebyId)().then((function(e){e.IsSucceed?(t.treeData=e.Data,t.checkData=t.treeData[0].Id,t.departmentId=t.checkData,t.fetchData()):t.$message({message:e.Message,type:"error"}),t.treeLoading=!1}))},fetchData:function(){var e=this;this.tbLoading=!0,(0,c.GetUserPage)((0,o.default)({DepartmentId:this.departmentId},this.queryInfo)).then((function(t){t.IsSucceed?(e.tableData=t.Data.Data||[],e.total=t.Data.TotalCount,e.setSelectRow(e.tags)):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleNodeClick:function(e){this.departmentId=e.Id,this.fetchData(this.departmentId)},setCurrentNode:function(){},handleSelectionChange:function(e){this.selectList=e.records},handleClose:function(){this.dialogVisible=!1},handleSearch:function(){this.queryInfo.Page=1,this.debounce(this.fetchData,500)},debounce:function(e,t){null!=this.timer&&clearTimeout(this.timer),this.timer=setTimeout(e,t)},handleSubmit:function(){this.$emit("selectList",this.selectList),this.dialogVisible=!1}}}},"323e2":function(e,t,a){"use strict";a("5707")},"340f":function(e,t,a){"use strict";a.r(t);var n=a("7ad3"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"366a":function(e,t,a){"use strict";a.r(t);var n=a("a3c9"),o=a("51203");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("7488d");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"3a6a14fc",null);t["default"]=s.exports},"389c":function(e,t,a){},"496d":function(e,t,a){"use strict";a.r(t);var n=a("623f"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"4e82":function(e,t,a){"use strict";var n=a("23e7"),o=a("e330"),r=a("59ed"),i=a("7b0b"),s=a("07fa"),l=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),h=a("3f7e"),m=a("99f4"),g=a("1212"),p=a("ea83"),y=[],b=o(y.sort),_=o(y.push),v=c((function(){y.sort(void 0)})),P=c((function(){y.sort(null)})),k=f("sort"),L=!c((function(){if(g)return g<70;if(!(h&&h>3)){if(m)return!0;if(p)return p<603;var e,t,a,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:t+n,v:a})}for(y.sort((function(e,t){return t.v-e.v})),n=0;n<y.length;n++)t=y[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),D=v||!P||!k||!L,I=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:D},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(L)return void 0===e?b(t):b(t,e);var a,n,o=[],u=s(t);for(n=0;n<u;n++)n in t&&_(o,t[n]);d(o,I(e)),a=s(o),n=0;while(n<a)t[n]=o[n++];while(n<u)l(t,n++);return t}})},51203:function(e,t,a){"use strict";a.r(t);var n=a("1ba7"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},5707:function(e,t,a){},"5bf2":function(e,t,a){"use strict";a("389c")},"5edd":function(e,t,a){"use strict";a.r(t);var n=a("d47d"),o=a("340f");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("6853d");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"3076eea2",null);t["default"]=s.exports},"623f":function(e,t,a){"use strict";a("9485");var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("2909"));a("99af"),a("7db0"),a("c740"),a("d81d"),a("14d9"),a("13d5"),a("a434"),a("e9f5"),a("f665"),a("ab43"),a("d3b7");var r=n(a("366a")),i=a("a024"),s=a("7196");t.default={components:{AddUser:r.default},data:function(){return{showDialog:!1,tags:[],form:{Name:"",Manager_UserName:"",Manager_UserId:"",Load:"",Workshop_Name:"",Workshop_Id:""},rules:{Name:[{required:!0,message:"请输入",trigger:"blur"}]},classList:[],workshopList:[],Is_Workshop_Enabled:""}},created:function(){},mounted:function(){this.getTeam()},methods:{inputBlur:function(e){e<0&&(this.form.Load=0)},initData:function(e,t){var a=this;e?(this.isEdit=!0,(0,i.GetWorkingTeamInfo)({id:e}).then((function(e){if(e.IsSucceed){var n=e.Data,o=n.Manager_UserName,r=n.Manager_UserId,i=n.Load,s=n.Name,l=n.Users,u=n.Id,c=n.Workshop_Name,d=n.Workshop_Id;a.form.Manager_UserName=o,a.form.Manager_UserId=r,a.form.Load=i,a.form.Name=s,a.isEditId=u,a.form.Workshop_Name=c,a.form.Workshop_Id=d,a.Is_Workshop_Enabled=t,a.tags=l.map((function(e){return a.$set(e,"Display_Name",e.User_Name),a.$set(e,"Id",e.User_Id),e}))}else a.$message({message:e.Message,type:"error"})}))):this.Is_Workshop_Enabled=t},managerChange:function(e){var t=this.classList.find((function(t){return t.Id===e}));if(t){this.form.Manager_UserName=t.Display_Name;var a=this.tags.findIndex((function(e){return e.Id===t.Id}));-1===a&&this.tags.push({Display_Name:t.Display_Name,Id:t.Id,User_Id:t.Id,User_Name:t.Display_Name})}},workshopChange:function(e){this.form.Workshop_Name=this.workshopList.find((function(t){return t.Id===e})).Display_Name},getTeam:function(){var e=this;(0,i.GetFactoryPeoplelist)().then((function(t){t.IsSucceed?e.classList=t.Data:e.$message({message:t.Message,type:"error"})})),(0,s.GetWorkshopPageList)({Page:-1}).then((function(t){t.IsSucceed?e.workshopList=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},handleAdd:function(){this.showDialog=!0},getSelectList:function(e){var t={};this.tags=[].concat((0,o.default)(this.tags),(0,o.default)(e)).reduce((function(e,a){return!t[a.Id]&&(t[a.Id]=e.push(a)),e}),[])},deleteTag:function(e){var t=this.tags.findIndex((function(t){return t.Id===e.Id}));-1!==t&&this.tags.splice(t,1)},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;var a={Name:e.form.Name,Manager_UserName:e.form.Manager_UserName,Manager_UserId:e.form.Manager_UserId,Load:e.form.Load,Members:e.tags.map((function(e){return e.Id})),Workshop_Id:e.form.Workshop_Id};e.isEdit&&(a.Id=e.isEditId),(0,i.SaveWorkingTeams)(a).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})}))}))}}}},"6853d":function(e,t,a){"use strict";a("ddf6")},"6d88":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a024");t.default={data:function(){return{tags:[],form:{Name:"",Manager_UserName:"",Manager_UserId:"",Load:"",Workshop_Name:""},rules:{},Is_Workshop_Enabled:""}},created:function(){},methods:{initData:function(e,t){var a=this;(0,n.GetWorkingTeamInfo)({id:e.Id}).then((function(n){if(n.IsSucceed){var o=n.Data,r=o.Manager_UserName,i=o.Manager_UserId,s=o.Load,l=o.Name,u=o.Users;a.form.Manager_UserName=r,a.form.Manager_UserId=i,a.form.Load=s,a.form.Name=l,a.tags=u,a.form.Workshop_Name=e.Workshop_Name,a.Is_Workshop_Enabled=t}else a.$message({message:n.Message,type:"error"})}))}}}},7196:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteWorkshop=u,t.GetFactoryPeoplelist=r,t.GetWorkshopEntity=l,t.GetWorkshopPageList=s,t.SaveEntity=i;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:e})}},"7488d":function(e,t,a){"use strict";a("156f")},"7ad3":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1"));a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("25f0");var i=n(a("15ac")),s=n(a("0f97")),l=n(a("34e9")),u=a("a024"),c=n(a("8cfb")),d=n(a("b903")),f=n(a("d7a3"));t.default={name:"PROGroup",components:{DynamicDataTable:s.default,TopHeader:l.default,info:d.default,detail:c.default},mixins:[i.default,f.default],data:function(){return{tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},currentComponent:"",title:"",columns:[],tbData:[],total:0,tbLoading:!1,dialogVisible:!1,selectList:[],keywords:""}},watch:{columns:function(e){this.FactoryDetailData.Is_Workshop_Enabled||e.map((function(e){"Workshop_Name"===e.Code&&(e.Is_Display=!1)}))}},created:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getCurFactory();case 1:return e.tbLoading=!0,t.n=2,e.getTableConfig("pro_group_list");case 2:return t.n=3,e.fetchData();case 3:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,u.GetWorkingTeamsPageList)({keywords:this.keywords,pageInfo:this.queryInfo}).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleClose:function(){this.dialogVisible=!1},handleSelectionChange:function(e){this.selectList=e},handleAdd:function(){var e=this;this.currentComponent="detail",this.title="新增",this.dialogVisible=!0,this.$nextTick((function(t){e.$refs["content"].initData("",e.FactoryDetailData.Is_Workshop_Enabled)}))},handleEdit:function(e){var t=this;this.currentComponent="detail",this.title="编辑",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].initData(e.Id,t.FactoryDetailData.Is_Workshop_Enabled)}))},handleDetail:function(e){var t=this;this.currentComponent="info",this.title="查看",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].initData(e,t.FactoryDetailData.Is_Workshop_Enabled)}))},handleDelete:function(e,t){var a=this,n=e?this.selectList.map((function(e){return e.Id})).toString():t.Id;this.$confirm("是否删除选中班组?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.DeleteWorkingTeams)({ids:n.toString()}).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"删除成功!"}),a.fetchData()):a.$message({message:e.Message,type:"error"})}))})).catch((function(){a.$message({type:"info",message:"已取消删除"})}))},handleSearch:function(){this.fetchData()},reset:function(){this.keywords="",this.fetchData()}}}},"8cfb":function(e,t,a){"use strict";a.r(t);var n=a("9865"),o=a("496d");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("5bf2");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"4ad76888",null);t["default"]=s.exports},9865:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"16px"}},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("h3",[e._v("基本信息")]),a("el-form-item",{attrs:{label:"班组名称",prop:"Name"}},[a("el-input",{model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"班组长",prop:"Manager_UserId"}},[a("el-select",{staticClass:"w100",attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.managerChange},model:{value:e.form.Manager_UserId,callback:function(t){e.$set(e.form,"Manager_UserId",t)},expression:"form.Manager_UserId"}},e._l(e.classList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"负荷提醒线",prop:"Load"}},[a("el-input",{staticClass:"input-number",attrs:{placeholder:"请输入",type:"number",min:"0"},on:{blur:function(t){return e.inputBlur(e.form.Load)}},model:{value:e.form.Load,callback:function(t){e.$set(e.form,"Load",e._n(t))},expression:"form.Load"}},[a("template",{slot:"append"},[e._v("吨")])],2)],1),e.Is_Workshop_Enabled?a("el-form-item",{attrs:{label:"所属车间",prop:"Workshop_Id"}},[a("el-select",{staticClass:"w100",attrs:{clearable:"",filterable:"",placeholder:"请选择"},on:{change:e.workshopChange},model:{value:e.form.Workshop_Id,callback:function(t){e.$set(e.form,"Workshop_Id",t)},expression:"form.Workshop_Id"}},e._l(e.workshopList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1):e._e(),a("h3",[e._v("班组成员")]),a("div",{staticClass:"tag-x"},[a("div",{staticClass:"tag-wrapper"},e._l(e.tags,(function(t){return a("el-tag",{key:t.Id,attrs:{size:"large",closable:"",type:"info"},on:{close:function(a){return e.deleteTag(t)}}},[e._v(" "+e._s(t.Display_Name)+" ")])})),1),a("div",{staticClass:"add-btn",on:{click:function(t){return t.stopPropagation(),e.handleAdd(t)}}},[a("el-icon",{staticClass:"el-icon-plus"}),a("span",[e._v("添加")])],1)])],1),a("footer",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1),e.showDialog?a("add-user",{attrs:{tags:e.tags,show:e.showDialog},on:{"update:show":function(t){e.showDialog=t},selectList:e.getSelectList}}):e._e()],1)},o=[]},a024:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=N,t.AddTechnology=l,t.AddWorkingProcess=s,t.DelLib=R,t.DeleteProcess=L,t.DeleteProcessFlow=P,t.DeleteTechnology=k,t.DeleteWorkingTeams=C,t.GetAllProcessList=f,t.GetCheckGroupList=x,t.GetChildComponentTypeList=M,t.GetFactoryAllProcessList=h,t.GetFactoryPeoplelist=W,t.GetFactoryWorkingTeam=y,t.GetGroupItemsList=v,t.GetLibList=i,t.GetLibListType=F,t.GetProcessFlow=m,t.GetProcessFlowListWithTechnology=g,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=G,t.GetProcessListWithUserBase=O,t.GetProcessWorkingTeamBase=$,t.GetTeamListByUser=U,t.GetTeamProcessList=_,t.GetWorkingTeam=b,t.GetWorkingTeamBase=S,t.GetWorkingTeamInfo=w,t.GetWorkingTeams=D,t.GetWorkingTeamsPageList=I,t.SaveWorkingTeams=T,t.UpdateProcessTeam=p;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function h(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function g(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function y(){return(0,o.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function b(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function _(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function v(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function P(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function k(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function L(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function S(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function G(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function O(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a3c9:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],attrs:{title:"添加人员",visible:e.dialogVisible,width:"80%",top:"10vh","append-to-body":"","custom-class":"dialogCustomClass"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("div",{staticClass:"dialog-wrapper"},[a("div",{staticClass:"left"},[a("span",{staticClass:"title"},[e._v("部门列表")]),a("tree-detail",{ref:"tree",attrs:{"default-expand-all":!1,"expand-on-click-node":!1,"expanded-key":e.checkData,loading:e.treeLoading,"tree-data":e.treeData,icon:"icon-users"},on:{handleNodeClick:e.handleNodeClick,setCurrentNode:e.setCurrentNode}})],1),a("div",{staticClass:"right"},[a("div",{staticClass:"title"},[a("span",[e._v("人员列表")]),a("el-input",{staticStyle:{width:"180px","margin-right":"20px"},attrs:{placeholder:"账户/姓名/手机号",clearable:""},on:{input:e.handleSearch},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.queryInfo.Search,callback:function(t){e.$set(e.queryInfo,"Search",t)},expression:"queryInfo.Search"}})],1),a("div",{staticClass:"cs-z-tb-wrapper"},[a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tableData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.handleSelectionChange,"checkbox-change":e.handleSelectionChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{"min-width":t.Width||120,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["Login_Lasttime"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(e._f("timeFormat")(n.Login_Lasttime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"page-total"},[a("div",{staticStyle:{display:"inline-block","margin-top":"16px"}},[a("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[e._v("已选"+e._s(e.selectList.length)+"条数据")])],1),a("Pagination",{staticClass:"cs-table-pagination",staticStyle:{display:"inline-block",float:"right"},attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)])])]),a("footer",[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)])},o=[]},b903:function(e,t,a){"use strict";a.r(t);var n=a("0b35"),o=a("0fa9");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("323e2");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"16f19a1e",null);t["default"]=s.exports},d47d:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{attrs:{padding:"0"},scopedSlots:e._u([{key:"right",fn:function(){return[a("el-form",{attrs:{"label-width":"80px",inline:!0}},[a("el-form-item",{attrs:{label:"班组名称"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.keywords,callback:function(t){e.keywords=t},expression:"keywords"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{on:{click:e.reset}},[e._v("重置")])],1)],1)]},proxy:!0},{key:"left",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增")]),a("el-button",{attrs:{type:"danger",disabled:!e.selectList.length},on:{click:function(t){return e.handleDelete(!0)}}},[e._v("删除")])]},proxy:!0}])}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.handleSelectionChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch},scopedSlots:e._u([{key:"Manager_UserName",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Manager_UserName||"-"))])]}},{key:"Load_Unit",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Load_Unit||"-"))])]}},{key:"Workshop_Name",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Workshop_Name||"-"))])]}},{key:"op",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(n)}}},[e._v("查看")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(n)}}},[e._v("编辑")]),a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(!1,n)}}},[e._v("删除")])]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"cs-dialog",attrs:{title:e.title,visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible},on:{close:e.handleClose,refresh:e.fetchData}}):e._e()],1)],1)},o=[]},d7a3:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),i=a("5e99"),s=a("fd31"),l=a("7196");t.default={data:function(){return{FactoryDetailData:{},ProfessionalType:[],factoryPeoplelist:[]}},methods:{getCurFactory:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,i.GetCurFactory)({}).then((function(t){t.IsSucceed?e.FactoryDetailData=t.Data[0]:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryTypeOption:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryPeoplelist:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryPeoplelist)({}).then((function(t){t.IsSucceed?e.factoryPeoplelist=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getManageCycle:function(){if(this.FactoryDetailData.Manage_Cycle_Enabled){var e,t,a,n,o,r,i=(new Date).getFullYear(),s=(new Date).getMonth()+1;1===this.FactoryDetailData.Manage_Cycle_Begin_Type?s-1===0?(e=i-1,t=12):(e=i,t=s-1):2===this.FactoryDetailData.Manage_Cycle_Begin_Type?(e=i,t=s):3===this.FactoryDetailData.Manage_Cycle_Begin_Type&&(s+1===13?(e=i+1,t=1):(e=i,t=s+1)),a=this.checkDate(e,t,this.FactoryDetailData.Manage_Cycle_Begin_Date),1===this.FactoryDetailData.Manage_Cycle_End_Type?s-1===0?(n=i-1,o=12):(n=i,o=s-1):2===this.FactoryDetailData.Manage_Cycle_End_Type?(n=i,o=s):3===this.FactoryDetailData.Manage_Cycle_End_Type&&(s+1===13?(n=i+1,o=1):(n=i,o=s+1)),r=this.checkDate(n,o,this.FactoryDetailData.Manage_Cycle_End_Date);var l=e+"-"+this.zeroFill(t)+"-"+this.zeroFill(a),u=n+"-"+this.zeroFill(o)+"-"+this.zeroFill(r);return[l,u]}return!1},checkDate:function(e,t,a){var n;return n=e%4===0?2===t?a>29?29:a:(4===t||6===t||9===t||11===t)&&a>30?30:a:2===t?a>28?28:a:(4===t||6===t||9===t||11===t)&&a>30?30:a,n},zeroFill:function(e){return e<10?"0"+e:e}}}},ddf6:function(e,t,a){},e41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=c,t.GetPartsList=s,t.GetProjectAreaTreeList=r,t.ImportParts=u,t.SaveProjectAreaSort=i;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}}}]);