(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-23a66896"],{"00e4":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"edit-dialog-container"},[n("el-form",{ref:"form",staticClass:"form-container",attrs:{model:t.formData,"label-width":"100px"}},[n("el-row",{attrs:{gutter:16}},t._l(t.editableColumns,(function(e){return n("el-col",{key:e.Code,attrs:{span:12}},[n("el-form-item",{staticClass:"form-item",attrs:{label:e.Display_Name}},[t.isDateField(e.Code)?n("el-date-picker",{staticClass:"full-width",attrs:{type:"date",disabled:!e.Is_Edit,"value-format":"yyyy-MM-dd",format:"yyyy-MM-dd",placeholder:"选择日期"},model:{value:t.formData[e.Code],callback:function(n){t.$set(t.formData,e.Code,n)},expression:"formData[column.Code]"}}):t.isNumberField(e)?n("el-input-number",{staticClass:"full-width cs-number-btn-hidden cs-input",attrs:{disabled:!e.Is_Edit||"Deviation"===e.Code,precision:e.Digit_Number||0,min:"ActualLength"===e.Code?0:void 0,placeholder:"",clearable:""},on:{change:function(n){return t.handleNumberChange(e.Code)}},model:{value:t.formData[e.Code],callback:function(n){t.$set(t.formData,e.Code,t._n(n))},expression:"formData[column.Code]"}}):n("div",{staticClass:"input-with-counter"},[n("el-input",{staticClass:"full-width",attrs:{disabled:!e.Is_Edit,maxlength:t.getMaxLength(e),"show-word-limit":""},model:{value:t.formData[e.Code],callback:function(n){t.$set(t.formData,e.Code,n)},expression:"formData[column.Code]"}})],1)],1)],1)})),1)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:t.cancel}},[t._v("取消")]),n("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确定")])],1)],1)},i=[]},"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=r,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function i(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(t,e,n){var r=o(),l=t-r,s=20,u=0;e="undefined"===typeof e?500:e;var c=function(){u+=s;var t=Math.easeInOutQuad(u,r,l,e);i(t),u<e?a(c):n&&"function"===typeof n&&n()};c()}},"0fd9":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("2909")),o=a(n("53ca")),r=a(n("5530")),l=a(n("c14f")),s=a(n("1da1"));n("4de4"),n("7db0"),n("caad"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("d3b7"),n("25f0"),n("2532"),n("3ca3"),n("498a"),n("159b"),n("ddb0");var u=n("f2f6"),c=n("3166"),d=a(n("333d")),f=n("c685"),m=a(n("15ac")),p=n("d51a"),h=a(n("a657")),g=n("7196"),_=n("ed08"),I=a(n("7dc7")),v=a(n("64cb")),b=a(n("5ac4")),P=n("4f39");e.default={name:"PROCompQISummary",components:{DynamicTableFields:h.default,Pagination:d.default,ReportSetting:v.default,ComImport:I.default,EditDialog:b.default},mixins:[m.default],data:function(){return{dialogVisible:!1,exportLoading:!1,totalNum:0,totalWeight:0,curSearch:1,gridCode:"",currentComponent:"",title:"",compCode:"",currentRow:null,form:{Assembly_Welding_Inspection_Date_Begin:"",Assembly_Welding_Inspection_Date_End:"",Galvanizing_Inspection_Time_Begin:"",Galvanizing_Inspection_Time_End:"",Painting_Inspection_Date_Begin:"",Painting_Inspection_Date_End:"",Packaging_Inspection_Date_Begin:"",Packaging_Inspection_Date_End:"",Project_Id:"",Sys_Project_Id:"",Area_Ids:[],InstallUnit_Ids:[],Codes:"",Code_Like:"",Workshop_Id:""},projectOption:[],treeParamsArea:{"default-expand-all":!0,filterable:!0,clickParent:!0,checkStrictly:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},multipleSelection:[],areaOption:[],installOption:[],workshopOptions:[],columns:[],tableData:[],customColumns:[],tbLoading:!1,tablePageSize:f.tablePageSize,queryInfo:{Page:1,PageSize:f.tablePageSize[0]},total:0}},computed:{Assembly_Welding_Inspection_DateTime:{get:function(){return[(0,P.timeFormat)(this.form.Assembly_Welding_Inspection_Date_Begin),(0,P.timeFormat)(this.form.Assembly_Welding_Inspection_Date_End)]},set:function(t){if(t){var e=t[0],n=t[1];this.form.Assembly_Welding_Inspection_Date_Begin=(0,P.timeFormat)(e),this.form.Assembly_Welding_Inspection_Date_End=(0,P.timeFormat)(n)}else this.form.Assembly_Welding_Inspection_Date_Begin="",this.form.Assembly_Welding_Inspection_Date_End=""}},Galvanizing_Inspection_Time:{get:function(){return[(0,P.timeFormat)(this.form.Galvanizing_Inspection_Time_Begin),(0,P.timeFormat)(this.form.Galvanizing_Inspection_Time_End)]},set:function(t){if(t){var e=t[0],n=t[1];this.form.Galvanizing_Inspection_Time_Begin=(0,P.timeFormat)(e),this.form.Galvanizing_Inspection_Time_End=(0,P.timeFormat)(n)}else this.form.Galvanizing_Inspection_Time_Begin="",this.form.Galvanizing_Inspection_Time_End=""}},Painting_Inspection_DateTime:{get:function(){return[(0,P.timeFormat)(this.form.Painting_Inspection_Date_Begin),(0,P.timeFormat)(this.form.Painting_Inspection_Date_End)]},set:function(t){if(t){var e=t[0],n=t[1];this.form.Painting_Inspection_Date_Begin=(0,P.timeFormat)(e),this.form.Painting_Inspection_Date_End=(0,P.timeFormat)(n)}else this.form.Painting_Inspection_Date_Begin="",this.form.Painting_Inspection_Date_End=""}},Packaging_Inspection_DateTime:{get:function(){return[(0,P.timeFormat)(this.form.Packaging_Inspection_Date_Begin),(0,P.timeFormat)(this.form.Packaging_Inspection_Date_End)]},set:function(t){if(t){var e=t[0],n=t[1];this.form.Packaging_Inspection_Date_Begin=(0,P.timeFormat)(e),this.form.Packaging_Inspection_Date_End=(0,P.timeFormat)(n)}else this.form.Packaging_Inspection_Date_Begin="",this.form.Packaging_Inspection_Date_End=""}}},watch:{compCode:function(t){this.updateFormFields(t,this.curSearch)},curSearch:function(t){this.updateFormFields(this.compCode,t)},"form.Project_Id":function(t){var e=this;if(t){var n,a=null===(n=this.projectOption.find((function(t){return t.Id===e.form.Project_Id})))||void 0===n?void 0:n.Sys_Project_Id;this.form.Sys_Project_Id=a}else this.form.Sys_Project_Id=""}},created:function(){var t=this;return(0,s.default)((0,l.default)().m((function e(){return(0,l.default)().w((function(e){while(1)switch(e.n){case 0:return e.p=0,t.tbLoading=!0,t.gridCode="PROQIReportConfig",e.n=1,t.initColumn();case 1:return e.n=2,t.getProjectOption();case 2:t.getWorkShop(),t.fetchData(1),e.n=4;break;case 3:e.p=3,e.v,t.tbLoading=!1;case 4:return e.a(2)}}),e,null,[[0,3]])})))()},mounted:function(){},methods:{filterNodeMethod:function(t){this.$refs.treeSelectArea.$refs.tree.filter(t)},initColumn:function(){var t=this;return(0,s.default)((0,l.default)().m((function e(){return(0,l.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig(t.gridCode,!0);case 1:t.customColumns=t.columns.filter((function(t){return t.Code.includes("CustomColumn")})),t.columns=t.columns.filter((function(t){return t.Is_Display}));case 2:return e.a(2)}}),e)})))()},handleClose:function(){this.dialogVisible=!1},updateFormFields:function(t,e){var n=t.trim();if(!n)return this.form.Codes="",void(this.form.Code_Like="");this.form.Codes=1===e?n:"",this.form.Code_Like=1!==e?n:""},fetchData:function(t){var e=this;this.tbLoading=!0,t&&(this.queryInfo.Page=t);var n=this.form.Codes.split(" ").filter((function(t){return!!t}));(0,p.GetCompQISummary)((0,r.default)((0,r.default)({},this.form),{},{Codes:n},this.queryInfo)).then((function(t){var n,a;t.IsSucceed?(e.tableData=t.Data.Data,e.total=t.Data.TotalCount,e.totalNum=(null===(n=t.Data)||void 0===n?void 0:n.TotalNum)||0,e.totalWeight=(null===(a=t.Data)||void 0===a?void 0:a.TotalWeight)||0):e.$message({message:t.Message,type:"error"})})).finally((function(){e.tbLoading=!1}))},getWorkShop:function(){var t=this;(0,g.GetWorkshopPageList)({Page:1,PageSize:-1}).then((function(e){var n;e.IsSucceed?(null!==e&&void 0!==e&&null!==(n=e.Data)&&void 0!==n&&n.Data||(t.workshopOptions=[]),t.workshopOptions=e.Data.Data.map((function(t){return{Id:t.Id,Display_Name:t.Display_Name}}))):t.$message({message:e.Message,type:"error"})}))},changeColumn:function(){var t=this;return(0,s.default)((0,l.default)().m((function e(){return(0,l.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getTableConfig(t.gridCode);case 1:return e.a(2)}}),e)})))()},reset:function(){this.compCode="",this.curSearch=1,this.$refs.formRef.resetFields(),this.form.Project_Id=this.projectOption[0].Id,this.Assembly_Welding_Inspection_DateTime="",this.Galvanizing_Inspection_Time="",this.Painting_Inspection_DateTime="",this.Packaging_Inspection_DateTime="",this.fetchData(1)},search:function(){this.fetchData(1)},getProjectOption:function(){var t=this;return new Promise((function(e,n){(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(n){var a;n.IsSucceed?(t.projectOption=(null===(a=n.Data)||void 0===a?void 0:a.Data)||[],t.projectOption.length&&(t.form.Project_Id=t.projectOption[0].Id,t.getAreaList())):t.$message({message:n.Message,type:"error"});e()}))}))},projectChange:function(t){this.form.Area_Ids=[],this.form.InstallUnit_Ids=[],this.treeParamsArea.data=[],this.installOption=[],t&&this.getAreaList()},areaRemoveTag:function(t){var e=this;this.$nextTick((function(){e.form.InstallUnit_Ids=[],e.getInstall()}))},areaClear:function(){this.form.Area_Ids=[],this.form.InstallUnit_Ids=[],this.installOption=[]},getAreaList:function(){var t=this;(0,c.GeAreaTrees)({projectId:this.form.Project_Id}).then((function(e){if(e.IsSucceed){var n=e.Data;t.setDisabledTree(n),t.treeParamsArea.data=n,t.$nextTick((function(e){var a;null===(a=t.$refs.treeSelectArea)||void 0===a||a.treeDataUpdateFun(n)}))}else t.$message({message:e.Message,type:"error"})}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var n=t.Children;n&&n.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(n))}))},areaCheck:function(t){var e=this;this.$nextTick((function(){e.form.InstallUnit_Ids=[],e.getInstall()}))},areaChange:function(){this.form.InstallUnit_Ids=[],this.getInstall()},getInstall:function(){var t=this,e=[];if(this.installOption=[],this.form.Area_Ids&&this.form.Area_Ids.length&&"object"===(0,o.default)(this.form.Area_Ids)){var n=this.form.Area_Ids.filter((function(t){return!!t}));n.forEach((function(t){e.push((0,u.GetInstallUnitPageList)({Area_Id:t,Page:1,PageSize:-1}))}));var a=[];Promise.all(e).then((function(e){e.forEach((function(t){var e;a.push.apply(a,(0,i.default)((null===(e=t.Data)||void 0===e?void 0:e.Data)||[]))})),t.installOption=a.filter((function(t){return!!t}))}))}},formatDate:function(t){return t?(0,P.timeFormat)(t):"-"},exportData:function(){var t=this;this.exportLoading=!0;var e=this.multipleSelection.map((function(t){return t.Unique_Id})).filter((function(t){return!!t})).toString();(0,p.ExportQISummary)((0,r.default)((0,r.default)({},this.form),{},{Ids:e})).then((function(e){e.IsSucceed?(t.$message({message:"导出成功",type:"success"}),window.open((0,_.combineURL)(t.$baseUrl,e.Data),"_blank")):t.$message({message:e.Message,type:"error"})})).finally((function(){t.exportLoading=!1}))},logQuery:function(){this.dialogVisible=!0,this.currentComponent="ComImport",this.title="数据导入"},reportSetting:function(){var t=this;this.currentComponent="ReportSetting",this.title="报表设置",this.dialogVisible=!0,this.$nextTick((function(e){t.$refs["content"].handleOpen(t.customColumns)}))},tbSelectChange:function(t){this.multipleSelection=t.records},handleEdit:function(t){this.currentRow=t,this.currentComponent="EditDialog",this.title="编辑数据",this.dialogVisible=!0},handleSubmit:function(t){var e=this;this.tbLoading=!0,(0,p.SaveQIReportData)(t).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.handleClose(),e.fetchData(1)):e.$message({message:t.Message||"保存失败",type:"error"})})).catch((function(t){e.$message({message:"保存失败: "+t,type:"error"})})).finally((function(){e.tbLoading=!1}))}}}},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),i=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,r=t.Data,l=t.Message;if(a){if(!r)return void e.$message({message:"表格配置不存在",type:"error"});var s=[];e.tbConfig=Object.assign({},e.tbConfig,r.Grid),s=n?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=s.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+r.Grid.Row_Number||i.tablePageSize[0]),o(e.columns)}else e.$message({message:l,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var i=this.columns[a];if(i.Code===e){n.Type=i.Type,n.Filter_Type=i.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=c,e.GeAreaTrees=D,e.GetFileSync=k,e.GetInstallUnitIdNameList=y,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=C,e.GetProjectAreaTreeList=P,e.GetProjectEntity=s,e.GetProjectList=l,e.GetProjectPageList=r,e.GetProjectTemplate=h,e.GetPushProjectPageList=b,e.GetSchedulingPartList=S,e.IsEnableProjectMonomer=d,e.SaveProject=u,e.UpdateProjectTemplateBase=g,e.UpdateProjectTemplateContacts=_,e.UpdateProjectTemplateContract=I,e.UpdateProjectTemplateOther=v;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(t)})}function u(t){return(0,i.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(t)})}function d(t){return(0,i.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,i.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,i.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function _(t){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function I(t){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function y(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function D(t){return(0,i.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function C(t){return(0,i.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function S(t){return(0,i.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function k(t){return(0,i.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"4f39":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=o,e.timeFormat=r,n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("4d90"),n("5319");var i=a(n("53ca"));function o(t,e){if(0===arguments.length||!t)return null;var n,a=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,i.default)(t)?n=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),n=new Date(t));var o={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},r=a.replace(/{([ymdhisa])+}/g,(function(t,e){var n=o[e];return"a"===e?["日","一","二","三","四","五","六"][n]:n.toString().padStart(2,"0")}));return r}function r(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var n=t.split("~"),a=o(new Date(n[0]),e)+" ~ "+o(new Date(n[1]),e);return a}return t&&t.length>0?o(new Date(t),e):void 0}},"58a2":function(t,e,n){"use strict";n("6e91")},"5ac4":function(t,e,n){"use strict";n.r(e);var a=n("00e4"),i=n("d4b8");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("db51");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"70673fe8",null);e["default"]=l.exports},"64cb":function(t,e,n){"use strict";n.r(e);var a=n("6cfe"),i=n("fa4e");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("a0cd");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"12d2617b",null);e["default"]=l.exports},"6cfe":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("h2",[t._v("自定义字段配置")]),n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",border:"",stripe:"",resizable:"","show-overflow":"",loading:t.settingLoading,data:t.tableData,"edit-config":{beforeEditMethod:t.activeRowMethod,trigger:"click",showStatus:!0,mode:"row"}},on:{"edit-closed":t.editClosedEvent}},[n("vxe-column",{attrs:{align:"left",field:"Display_Name",title:"字段名称","min-width":"180","edit-render":{}},scopedSlots:t._u([{key:"edit",fn:function(e){var a=e.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.Display_Name,callback:function(e){t.$set(a,"Display_Name",e)},expression:"row.Display_Name"}})]}}])}),n("vxe-column",{attrs:{align:"left",field:"Is_Display",title:"是否启用","min-width":"180","edit-render":{}},scopedSlots:t._u([{key:"edit",fn:function(e){var a=e.row;return[n("div",[n("el-radio",{attrs:{label:!0},model:{value:a.Is_Display,callback:function(e){t.$set(a,"Is_Display",e)},expression:"row.Is_Display"}},[t._v("是")]),n("el-radio",{attrs:{label:!1},model:{value:a.Is_Display,callback:function(e){t.$set(a,"Is_Display",e)},expression:"row.Is_Display"}},[t._v("否")])],1)]}},{key:"default",fn:function(e){var a=e.row;return[n("div",[a.Is_Display?n("el-tag",{attrs:{type:"success"}},[t._v("是")]):n("el-tag",{attrs:{type:"danger"}},[t._v("否")])],1)]}}])})],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确定")])],1)],1)},i=[]},"6e91":function(t,e,n){},7196:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteWorkshop=u,e.GetFactoryPeoplelist=o,e.GetWorkshopEntity=s,e.GetWorkshopPageList=l,e.SaveEntity=r;var i=a(n("b775"));function o(t){return(0,i.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function r(t){return(0,i.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:t})}},7781:function(t,e,n){"use strict";n.r(e);var a=n("0fd9"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"7dc7":function(t,e,n){"use strict";n.r(e);var a=n("fb53"),i=n("984f");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("bacd");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"a5dc24ec",null);e["default"]=l.exports},8117:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"container abs100"},[n("div",{staticClass:"filter-bar"},[n("el-form",{ref:"formRef",attrs:{model:t.form,"label-width":"80px",inline:""}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[n("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},t._l(t.projectOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域",prop:"Area_Ids","label-width":"50px"}},[n("el-tree-select",{key:t.form.Project_Id,ref:"treeSelectArea",staticClass:"cs-tree-x w100",attrs:{disabled:!t.form.Project_Id,"select-params":{clearable:!0,filterable:!0,multiple:!0},"show-checkbox":!0,"tree-params":t.treeParamsArea},on:{"select-clear":t.areaClear,searchFun:t.filterNodeMethod,"node-click":t.areaChange,removeTag:t.areaRemoveTag,check:t.areaCheck},model:{value:t.form.Area_Ids,callback:function(e){t.$set(t.form,"Area_Ids",e)},expression:"form.Area_Ids"}})],1),n("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Ids","label-width":"50px"}},[n("el-select",{attrs:{multiple:"",filterable:"",clearable:"",placeholder:"请选择",disabled:!t.form.Area_Ids},model:{value:t.form.InstallUnit_Ids,callback:function(e){t.$set(t.form,"InstallUnit_Ids",e)},expression:"form.InstallUnit_Ids"}},t._l(t.installOption,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"构件名称","label-width":"80px"}},[n("el-input",{staticClass:"input-with-select",attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:t.compCode,callback:function(e){t.compCode=e},expression:"compCode"}},[n("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:t.curSearch,callback:function(e){t.curSearch=e},expression:"curSearch"}},[n("el-option",{attrs:{label:"精准查询",value:1}}),n("el-option",{attrs:{label:"模糊查询",value:2}})],1)],1)],1),n("el-form-item",{attrs:{label:"制作车间",prop:"Workshop_Id"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Workshop_Id,callback:function(e){t.$set(t.form,"Workshop_Id",e)},expression:"form.Workshop_Id"}},t._l(t.workshopOptions,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"装焊检查时间","label-width":"110px"}},[n("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.Assembly_Welding_Inspection_DateTime,callback:function(e){t.Assembly_Welding_Inspection_DateTime=e},expression:"Assembly_Welding_Inspection_DateTime"}})],1),n("el-form-item",{attrs:{label:"镀锌检验时间","label-width":"100px"}},[n("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.Galvanizing_Inspection_Time,callback:function(e){t.Galvanizing_Inspection_Time=e},expression:"Galvanizing_Inspection_Time"}})],1),n("el-form-item",{attrs:{label:"油漆检验时间","label-width":"100px"}},[n("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.Painting_Inspection_DateTime,callback:function(e){t.Painting_Inspection_DateTime=e},expression:"Painting_Inspection_DateTime"}})],1),n("el-form-item",{attrs:{label:"打包检验时间","label-width":"100px"}},[n("el-date-picker",{staticStyle:{width:"220px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.Packaging_Inspection_DateTime,callback:function(e){t.Packaging_Inspection_DateTime=e},expression:"Packaging_Inspection_DateTime"}})],1),n("el-form-item",[n("el-button",{attrs:{disabled:t.tbLoading},on:{click:t.reset}},[t._v("重置")]),n("el-button",{attrs:{type:"primary",disabled:t.tbLoading},on:{click:t.search}},[t._v("搜索")])],1)],1)],1),n("div",{staticClass:"main-content"},[n("vxe-toolbar",{scopedSlots:t._u([{key:"buttons",fn:function(){return[n("el-button",{attrs:{type:"success",loading:t.exportLoading},on:{click:t.exportData}},[t._v("导出数据")]),n("el-button",{attrs:{type:"primary"},on:{click:t.logQuery}},[t._v("数据导入")]),n("el-button",{attrs:{type:"primary"},on:{click:t.reportSetting}},[t._v("报表设置")])]},proxy:!0},{key:"tools",fn:function(){return[n("div",{staticClass:"flex-center",staticStyle:{"margin-right":"10px"}},[n("span",{staticStyle:{"margin-right":"10px"}},[t._v("构件数量："+t._s(t.totalNum))]),n("span",[t._v("构件总重："+t._s(t.totalWeight)+"kg")])]),n("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":t.gridCode},on:{updateColumn:t.changeColumn}})]},proxy:!0}])}),n("div",{staticClass:"tb-x"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",data:t.tableData,"row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:t.tbLoading,stripe:"",size:"medium",resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox"}}),t._l(t.columns,(function(e,a){return[["InspectionDate","SandblastingTime","PaintingInspectionDate","ShippingDate","ReleaseTime","PackagingInspectionDate","PackagingDate","PaintingApplicationDate","AssemblyWeldingInspectionDate","GalvanizingInspectionTime","GalvanizingDispatchTime","AssemblyWeldingApplicationDate"].includes(e.Code)?n("vxe-column",{key:a,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"",field:e.Code,title:e.Display_Name,sortable:"","min-width":e.Width,align:e.Align},scopedSlots:t._u([{key:"default",fn:function(n){var a=n.row;return[t._v(" "+t._s(t.formatDate(a[e.Code]))+" ")]}}],null,!0)}):n("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"",field:e.Code,title:e.Display_Name,sortable:"","min-width":e.Width,align:e.Align}})]})),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleEdit(a)}}},[t._v("编辑")])]}}])})],2)],1),n("Pagination",{staticClass:"pagination",attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1),t.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:"EditDialog"===t.currentComponent?"60%":"40%",top:"10vh"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n(t.currentComponent,{ref:"content",tag:"component",attrs:{columns:t.columns,"row-data":t.currentRow},on:{close:t.handleClose,submit:t.handleSubmit,refreshColumn:t.initColumn,refresh:function(e){return t.fetchData(1)}}})],1):t._e()],1)},i=[]},"83af3":function(t,e,n){},8788:function(t,e,n){},"90bf":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d3b7");var i=a(n("3796")),o=n("ed08"),r=n("d51a");e.default={components:{UploadExcel:i.default},data:function(){return{btnLoading:!1}},methods:{beforeUpload:function(t){var e=this;if(t){var n=new FormData;n.append("file",t),this.btnLoading=!0,(0,r.ImportQISummary)(n).then((function(t){t.IsSucceed?(e.$message({message:"导入成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):(t.Data&&window.open((0,o.combineURL)(e.$baseUrl,t.Data)),e.$message({message:t.Message,type:"error"}))})).finally((function(t){e.btnLoading=!1}))}},handleSubmit:function(){this.$refs.upload.handleSubmit()}}}},"915e":function(t,e,n){"use strict";n.r(e);var a=n("8117"),i=n("7781");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("58a2");var r=n("2877"),l=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"015ecce6",null);e["default"]=l.exports},"984f":function(t,e,n){"use strict";n.r(e);var a=n("90bf"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},a0cd:function(t,e,n){"use strict";n("bdd3")},bacd:function(t,e,n){"use strict";n("83af3")},bdd3:function(t,e,n){},d4b8:function(t,e,n){"use strict";n.r(e);var a=n("d559"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},d51a:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddLanch=l,e.BatchManageSaveCheck=I,e.DelLanch=_,e.DeleteToleranceSetting=j,e.EntityQualityManagement=d,e.ExportQISummary=O,e.GetCheckingEntity=b,e.GetCompPartForSpotCheckPageList=C,e.GetCompQISummary=k,e.GetDictionaryDetailListByCode=s,e.GetEditById=v,e.GetFactoryPeoplelist=m,e.GetNodeList=u,e.GetPageFeedBack=P,e.GetPageQualityManagement=o,e.GetPartAndSteelBacrode=c,e.GetSheetDwg=p,e.GetSpotCheckingEntity=S,e.GetToleranceSettingList=G,e.ImportQISummary=w,e.ManageAdd=r,e.RectificationRecord=D,e.SaveFeedBack=y,e.SavePass=h,e.SaveQIReportData=R,e.SaveTesting=f,e.SaveToleranceSetting=T,e.SubmitLanch=g;var i=a(n("b775"));function o(t){return(0,i.default)({url:"/PRO/Inspection/GetPageQualityManagement",method:"post",data:t})}function r(t){return(0,i.default)({url:"/PRO/Inspection/ManageAdd",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:t})}function s(t){return(0,i.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:t})}function c(t){return(0,i.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:t})}function d(t){return(0,i.default)({url:"/PRO/Inspection/EntityQualityManagement",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/Inspection/SaveTesting",method:"post",data:t})}function m(t){return(0,i.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/Inspection/GetSheetDwg",method:"post",data:t})}function h(t){return(0,i.default)({url:"/PRO/Inspection/SavePass",method:"post",data:t,timeout:12e5})}function g(t){return(0,i.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:t})}function _(t){return(0,i.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:t})}function I(t){return(0,i.default)({url:"/PRO/Inspection/BatchManageSaveCheck",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:t})}function b(t){return(0,i.default)({url:"/PRO/Inspection/GetCheckingEntity",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/Inspection/GetPageFeedBack",method:"post",data:t})}function y(t){return(0,i.default)({url:"/PRO/Inspection/SaveFeedBack",method:"post",data:t})}function D(t){return(0,i.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:t})}function C(t){return(0,i.default)({url:"/PRO/Inspection/GetCompPartForSpotCheckPageList",method:"post",data:t})}function S(t){return(0,i.default)({url:"/PRO/Inspection/GetSpotCheckingEntity",method:"post",data:t})}function k(t){return(0,i.default)({url:"/PRO/Inspection/GetCompQISummary",method:"post",data:t})}function O(t){return(0,i.default)({url:"/PRO/Inspection/ExportQISummary",method:"post",data:t})}function w(t){return(0,i.default)({url:"/PRO/Inspection/ImportQISummary",method:"post",data:t})}function R(t){return(0,i.default)({url:"/PRO/Inspection/SaveQIReportData",method:"post",data:t})}function T(t){return(0,i.default)({url:"/pro/Inspection/SaveToleranceSetting",method:"post",data:t})}function j(t){return(0,i.default)({url:"/pro/Inspection/DeleteToleranceSetting",method:"post",data:t})}function G(t){return(0,i.default)({url:"/pro/Inspection/GetToleranceSettingList",method:"post",data:t})}},d559:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("caad"),n("e9f5"),n("910d"),n("e9c4"),n("a9e3"),n("b680"),n("b64b"),n("d3b7");e.default={name:"EditDialog",props:{columns:{type:Array,required:!0},rowData:{type:Object,required:!0}},data:function(){return{formData:{}}},computed:{editableColumns:function(){return this.columns.filter((function(t){return"operation"!==t.Code&&t.Is_Display}))}},watch:{"formData.ActualLength":{handler:function(t){this.calculateDeviation()}},"formData.RequiredLength":{handler:function(t){this.calculateDeviation()}}},created:function(){this.formData=JSON.parse(JSON.stringify(this.rowData))},methods:{isDateField:function(t){var e=["InspectionDate","SandblastingTime","PaintingInspectionDate","ShippingDate","ReleaseTime","PackagingInspectionDate","PackagingDate","PaintingApplicationDate","AssemblyWeldingInspectionDate","GalvanizingInspectionTime","GalvanizingDispatchTime","AssemblyWeldingApplicationDate"];return e.includes(t)},isNumberField:function(t){return"number"===t.Type},getMaxLength:function(t){return 50},handleNumberChange:function(t){"ActualLength"!==t&&"RequiredLength"!==t||this.calculateDeviation()},calculateDeviation:function(){var t=parseFloat(this.formData.ActualLength)||0,e=parseFloat(this.formData.RequiredLength)||0;this.$set(this.formData,"Deviation",Number((t-e).toFixed(2)))},cancel:function(){this.$emit("close")},submit:function(){this.$emit("submit",this.formData)}}}},db51:function(t,e,n){"use strict";n("8788")},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=s,e.CheckPlanTime=u,e.DeleteInstallUnit=m,e.GetCompletePercent=I,e.GetEntity=b,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=_,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=l,e.GetInstallUnitPageList=r,e.GetProjectInstallUnitList=v,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=P;var i=a(n("b775")),o=a(n("4328"));function r(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function s(t){return(0,i.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function u(t){return(0,i.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,i.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,i.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,i.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function _(t){return(0,i.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function I(t){return(0,i.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function v(t){return(0,i.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function b(t){return(0,i.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(t)})}function P(t){return(0,i.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},f7f3:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("5530"));n("7db0"),n("e9f5"),n("f665"),n("d3b7");var o=n("6186"),r=n("ed08");e.default={data:function(){return{settingLoading:!1,saveLoing:!1,shouldRefresh:!1,tableData:[]}},mounted:function(){},methods:{handleOpen:function(t){this.shouldRefresh=!1,this.tableData=(0,r.deepClone)(t)},activeRowMethod:function(t){t.row,t.rowIndex;return!0},editClosedEvent:function(t){var e=this,n=t.row,a=t.column;if(n.Display_Name){var r=this.tableData.find((function(t){return t!==n&&t.Display_Name===n.Display_Name}));if(r)this.$message({message:"显示名称不能重复",type:"warning"});else{var l=this.$refs.xTable,s=a.field,u=!1;l.isUpdateByRow(n,s)&&(u=!0),u&&(0,o.SaveGridColumn)((0,i.default)((0,i.default)({},n),{},{Is_Must_Input:!0})).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.shouldRefresh=!0):e.$message({message:t.Message,type:"error"})}))}}else this.$message({message:"名称不能为空",type:"warning"})},save:function(){this.shouldRefresh&&this.$emit("refreshColumn"),this.$emit("close")}}}},fa4e:function(t,e,n){"use strict";n.r(e);var a=n("f7f3"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},fb53:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("upload-excel",{ref:"upload",attrs:{"before-upload":t.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},i=[]}}]);