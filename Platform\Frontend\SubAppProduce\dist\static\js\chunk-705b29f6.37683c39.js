(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-705b29f6","chunk-75129aeb"],{"00a7":function(e,t,a){},"04f7":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("2532"),a("c7cd");a("f2f6"),a("3f35");var n=a("6186"),o=a("edcd"),i=a("f2d0"),r=a("209b");t.default={name:"DirectDialog",props:{rowsData:{type:Array,default:function(){return[]}},Is_Pack:{type:Boolean,default:!0}},data:function(){return{warehouses:[],locations:[],tbLoading:!1,btnLoading:!1,form:{Warehouse_Id:"",Location_Id:""},columns:[],tbData:[],tbConfig:{},TotalCount:0,Page:0,PageSize:-1,gridCode:"pro_choose_package_stockin_list"}},watch:{rowsData:{handler:function(e,t){this.tbData=JSON.parse(JSON.stringify(e))},immediate:!0,deep:!0}},mounted:function(){this.getGridByCode()},created:function(){this.getWarehouseListOfCurFactory()},methods:{getGridByCode:function(){var e=this;(0,n.GetGridByCode)({Code:this.gridCode}).then((function(t){var a=t.IsSucceed,n=t.Data,o=t.Message;if(a){e.tbConfig=Object.assign({},e.tbConfig,n.Grid);var r=n.ColumnList||[];e.columns=r.filter((function(e){return e.Is_Display})).map((function(e){return i.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),e}))}else e.$message({message:o,type:"error"})})).then((function(){}))},getWarehouseListOfCurFactory:function(){var e=this;(0,r.GetWarehouseListOfCurFactory)({}).then((function(t){t.IsSucceed&&(e.warehouses=t.Data)}))},wareChange:function(e){var t=this;this.form.Location_Id="",(0,r.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Num"===t.field},deleteRowData:function(e,t){this.tbData.splice(t,1)},cancel:function(){this.$emit("dialogCancel")},submit:function(){var e=this;return this.form.Warehouse_Id?this.form.Location_Id?(this.btnLoading=!0,void(0,o.SaveStockIn2nd)({Warehouse_Id:this.form.Warehouse_Id,Location_Id:this.form.Location_Id,Is_Pack:this.Is_Pack,Details:this.tbData}).then((function(t){t.IsSucceed?e.$emit("dialogFormSubmitSuccess"):e.btnLoading=!1}))):this.$message.warning("必须选择库位"):this.$message.warning("必须选择仓库")}}}},"06dd":function(e,t,a){"use strict";a.r(t);var n=a("6af1"),o=a("acd4");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("73a4");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"13721226",null);t["default"]=l.exports},"0790":function(e,t,a){"use strict";a.r(t);var n=a("165b"),o=a("4683");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("744b");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"50f1ef00",null);t["default"]=l.exports},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=r,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(e,t,a){var r=i(),l=e-r,s=20,c=0;t="undefined"===typeof t?500:t;var u=function(){c+=s;var e=Math.easeInOutQuad(c,r,l,t);o(e),c<t?n(u):a&&"function"===typeof a&&a()};u()}},"12cb0":function(e,t,a){"use strict";a("00a7")},1411:function(e,t,a){"use strict";a("ae45")},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(i){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,r=e.Data,l=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});var s=[];t.tbConfig=Object.assign({},t.tbConfig,r.Grid),s=a?(null===r||void 0===r?void 0:r.ColumnList)||[]:(null===r||void 0===r?void 0:r.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=s.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+r.Grid.Row_Number||o.tablePageSize[0]),i(t.columns)}else t.$message({message:l,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"165b":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog cs-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tableData,resizable:"","tooltip-config":{enterable:!0}}},e._l(e.columns,(function(t){return a("vxe-column",{key:t.field,attrs:{title:t.title,sortable:"",align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(o[t.field]||"-"))])]}}],null,!0)})})),1),a("Pagination",{attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}}),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)],1)},o=[]},"2c7f":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-main-wp"},[a("div",{staticClass:"assistant-wrapper"},[a("div",{staticClass:"btn-wrapper"},[e.revokeWarehousingBtn?a("el-button",{attrs:{size:"small",type:"primary",disabled:0===e.selectList.length},on:{click:e.revokeFn}},[e._v("撤销入库")]):e._e()],1),a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-info"},[a("span",[e._v("构件总数 "+e._s(e.TotalAmount)+" 件")]),a("span",[e._v("构件总量 "+e._s(e.TotalWeight)+" "+e._s(e.unit))])])]),a("div",{staticClass:"date-picker-wrapper"},[a("el-date-picker",{attrs:{type:"daterange",align:"right",clearable:!0,"value-format":"yyyy-MM-dd","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:function(t){return e.dateChange()}},model:{value:e.SearchDate,callback:function(t){e.SearchDate=t},expression:"SearchDate"}})],1)]),a("div",{staticClass:"cs-z-tb-wrapper"},[a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"auto",align:"left",stripe:"",data:e.tbData,resizable:"","checkbox-config":{checkField:"checked",checkMethod:e.checkMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"40"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["ScanDate"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(e._f("timeFormat")(n.ScanDate)))])]}}:"Is_Direct"===t.Code?{key:"default",fn:function(t){var n=t.row;return[n.Is_Direct?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}:"SteelName"===t.Code?{key:"default",fn:function(t){var n=t.row;return[n.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),e._v(" "+e._s(n.SteelName)+" ")]}}:"Is_Direct_Name"===t.Code?{key:"default",fn:function(t){var n=t.row;return["是"===n.Is_Direct_Name?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}:["SteelName","PackageSn","ExtensionName","Return_Count","SteelWeight","In_Count","In_Weight","InstallUnit_Name"].includes(t.Code)?{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(e._s(o[t.Code]?o[t.Code]:"-"))])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})]}))],2)],1),a("Pagination",{attrs:{total:e.totalNum,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)])},o=[]},"2de2":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var o=n(a("5530")),i=n(a("c14f")),r=n(a("1da1")),l=a("edcd"),s=a("7f9d"),c=n(a("0790")),u=a("209b"),d=a("ed08"),f=n(a("15ac")),m=n(a("333d")),p=a("c685");t.default={name:"SingleList",components:{DialogInfo:c.default,Pagination:m.default},mixins:[f.default],props:{unit:{type:String,default:""},formData:{type:Object,default:function(){return{}}}},data:function(){return{exportLoading:!1,dialog:!1,tbLoading:!1,TotalAmount:0,TotalWeight:0,totalNum:0,queryInfo:{Page:1,PageSize:p.tablePageSize[0]},tablePageSize:p.tablePageSize,tbConfig:{},columns:[],tbData:[],dialogInnerColumn:[{title:"管理编号",field:"SerialNumber"},{title:"模型ID",field:"Model_Id"}],selectList:[],loading:!1,gridCode:"pro_wait_single_stockin_list"}},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:return t.a(2)}}),t)})))()},methods:{checkMethod:function(e){var t=e.row;return!t.stopFlag},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0;var a=JSON.parse(JSON.stringify(this.formData));a.C_Type="构件",(0,l.GetWaitingStockIn2ndPageList)((0,o.default)((0,o.default)({},this.queryInfo),a)).then((function(e){if(e.IsSucceed){var a=e.Data;t.tbData=a.Data,t.getStopList(t.tbData,"Import_Detail_Id"),t.totalNum=a.TotalCount,t.TotalAmount=a.TotalAmount,t.TotalWeight=a.TotalWeight}})).catch(console.error).finally((function(){t.tbLoading=!1,t.selectList=[]}))},getStopList:function(e,t){var a=this;return(0,r.default)((0,i.default)().m((function n(){var o;return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return o=e.map((function(e){return{Id:e[t],Type:2}})),n.n=1,(0,s.GetStopList)(o).then((function(n){if(n.IsSucceed){var o={};n.Data.forEach((function(e){o[e.Id]=!!e.Is_Stop})),e.forEach((function(e){o[e[t]]&&a.$set(e,"stopFlag",o[e[t]])}))}}));case 1:return n.a(2)}}),n)})))()},stockinFn:function(){this.$emit("stockinOption",this.selectList)},handleInfo:function(e){this.dialog=!0,this.getList(e)},getList:function(e){var t=this;(0,l.GetComponentList)({Import_Detail_Id:e.Import_Detail_Id,Model_Ids:e.Model_Ids}).then((function(e){e.IsSucceed?t.$refs["dialog"].fetchData((null===e||void 0===e?void 0:e.Data)||[]):t.$message({message:e.Message,type:"error"})}))},tbSelectChange:function(e){this.selectList=e.records},handleExport:function(){var e=this;this.exportLoading=!0;var t=(0,d.deepClone)(this.formData);t.C_Type="构件",(0,u.ExportWaitingStockIn2ndList)((0,o.default)((0,o.default)({},this.queryInfo),t)).then((function(t){t.IsSucceed?window.open((0,d.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"})})).finally((function(t){e.exportLoading=!1}))}}}},3079:function(e,t,a){"use strict";a.r(t);var n=a("5a37"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"3b5f":function(e,t,a){"use strict";a.r(t);var n=a("2de2"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"3bfa":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-main-wp"},[a("div",{staticClass:"assistant-wrapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{size:"small",type:"primary",disabled:0===e.selectList.length},on:{click:e.stockinFn}},[e._v("成品入库 ")]),a("el-button",{attrs:{size:"small",type:"primary",disabled:0===e.tbData.length,loading:e.exportLoading},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-info"},[a("span",[e._v("构件总数 "+e._s(e.TotalAmount)+" 件")]),a("span",[e._v("构件总量 "+e._s(e.TotalWeight)+" "+e._s(e.unit))])])])]),a("div",{staticClass:"cs-z-tb-wrapper"},[a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"auto",align:"left",stripe:"",data:e.tbData,resizable:"","checkbox-config":{checkField:"checked",checkMethod:e.checkMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"40"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u(["Model_Id"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Model_Id?n.Model_Id:"-"))])]}}:"Component_Code"===t.Code?{key:"default",fn:function(t){var n=t.row;return[n.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),e._v(" "+e._s(n.Component_Code)+" ")]}}:"Is_Direct"===t.Code?{key:"default",fn:function(t){var n=t.row;return[n.Is_Direct?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}:"InstallUnit_Name"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.InstallUnit_Name?n.InstallUnit_Name:"-"))])]}}:"Wait_In_Count"===t.Code?{key:"default",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleInfo(n)}}},[e._v(" "+e._s(n.Wait_In_Count?n.Wait_In_Count:"-")+" ")])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})]}))],2)],1),a("Pagination",{attrs:{total:e.totalNum,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1),e.dialog?a("dialog-info",{ref:"dialog",attrs:{title:"待入库数量",columns:e.dialogInnerColumn,visible:e.dialog},on:{"update:visible":function(t){e.dialog=t}}}):e._e()],1)},o=[]},"3f35":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportPackingList=f,t.GeneratePackCode=r,t.GetPacking2ndEntity=s,t.GetPacking2ndPageList=l,t.GetPackingGroupByDirectDetailList=i,t.GetWaitPack2ndPageList=c,t.SavePacking2nd=d,t.UnzipPacking2nd=u;var o=n(a("b775"));n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:e})}function r(){return(0,o.default)({url:"/PRO/Packing/GeneratePackCode",method:"post"})}function l(e){return(0,o.default)({url:"/PRO/Packing/GetPacking2ndPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Packing/GetPacking2ndEntity",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Packing/GetWaitPack2ndPageList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Packing/UnzipPacking2nd",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Packing/SavePacking2nd",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Packing/ExportPackingList",method:"post",data:e})}},4378:function(e,t,a){},4382:function(e,t,a){"use strict";a.r(t);var n=a("7f89"),o=a("ce31");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("fa83");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"0362cea5",null);t["default"]=l.exports},4683:function(e,t,a){"use strict";a.r(t);var n=a("a9ba"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"4e2d":function(e,t,a){},"4e82":function(e,t,a){"use strict";var n=a("23e7"),o=a("e330"),i=a("59ed"),r=a("7b0b"),l=a("07fa"),s=a("083a"),c=a("577e"),u=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),p=a("99f4"),g=a("1212"),h=a("ea83"),v=[],b=o(v.sort),_=o(v.push),y=u((function(){v.sort(void 0)})),C=u((function(){v.sort(null)})),I=f("sort"),k=!u((function(){if(g)return g<70;if(!(m&&m>3)){if(p)return!0;if(h)return h<603;var e,t,a,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)v.push({k:t+n,v:a})}for(v.sort((function(e,t){return t.v-e.v})),n=0;n<v.length;n++)t=v[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),S=y||!C||!I||!k,w=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:c(t)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:S},{sort:function(e){void 0!==e&&i(e);var t=r(this);if(k)return void 0===e?b(t):b(t,e);var a,n,o=[],c=l(t);for(n=0;n<c;n++)n in t&&_(o,t[n]);d(o,w(e)),a=l(o),n=0;while(n<a)t[n]=o[n++];while(n<c)s(t,n++);return t}})},"53fa":function(e,t,a){"use strict";a.r(t);var n=a("2c7f"),o=a("8b30");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("12cb0");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"38d77595",null);t["default"]=l.exports},"5a37":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("2532"),a("c7cd"),a("159b");var n=a("6186"),o=a("edcd"),i=a("f2d0");t.default={name:"RevokeDialog",props:{rowsData:{type:Array,default:function(){return[]}}},data:function(){return{tbLoading:!1,btnLoading:!1,columns:[],tbData:[],tbConfig:{},TotalCount:0,Page:0,PageSize:-1,gridCode:"pro_record_package_revoke_list"}},watch:{rowsData:{handler:function(e,t){this.tbData=JSON.parse(JSON.stringify(e)),this.tbData.map((function(e){return e.Num=e.In_Count,e}))},immediate:!0,deep:!0}},created:function(){this.getGridByCode()},mounted:function(){},methods:{getGridByCode:function(){var e=this;(0,n.GetGridByCode)({Code:this.gridCode}).then((function(t){var a=t.IsSucceed,n=t.Data,o=t.Message;if(a){e.tbConfig=Object.assign({},e.tbConfig,n.Grid);var r=n.ColumnList||[];e.columns=r.filter((function(e){return e.Is_Display})).map((function(e){return i.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),e}))}else e.$message({message:o,type:"error"})})).catch((function(){}))},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Num"===t.field},deleteRowData:function(e,t){this.tbData.splice(t,1)},cancel:function(){this.$emit("dialogCancel")},submit:function(){var e=this;if(0!=this.tbData.length){var t=[];this.tbData.forEach((function(e){var a={};a.Pro_Detail_Id=e.Pro_Detail_Id,a.Component_Id=e.Component_Id,a.SteelAmount=parseInt(e.Num),t.push(a)})),this.btnLoading=!0,(0,o.DeleteStockInRecord)(t).then((function(t){t.IsSucceed?(e.$message.success("撤销成功"),e.$emit("dialogCancel","3")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1})).catch((function(){e.$message({message:res.Message,type:"error"}),e.btnLoading=!1}))}else this.$emit("dialogCancel")}}}},"5b04":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var o=n(a("5530")),i=n(a("c14f")),r=n(a("1da1")),l=a("c24f"),s=a("edcd"),c=a("8975"),u=n(a("333d")),d=n(a("15ac")),f=a("c685"),m=a("7f9d");t.default={name:"PackageList",components:{Pagination:u.default},filters:{timeFormat:c.timeFormat},mixins:[d.default],props:{unit:{type:String,default:""},formData:{type:Object,default:function(){return{}}}},data:function(){return{SearchDate:null,TotalAmount:0,TotalWeight:0,totalNum:0,queryInfo:{Page:1,PageSize:f.tablePageSize[0]},tablePageSize:f.tablePageSize,tbConfig:{},columns:[],tbData:[],selectList:[],tbLoading:!1,loading:!1,gridCode:"pro_record_package_stockin_list",pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},revokeWarehousingBtn:null}},watch:{formData:{handler:function(e,t){},immediate:!0,deep:!0}},created:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getRoleAuthorization();case 1:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:return t.a(2)}}),t)})))()},methods:{checkMethod:function(e){var t=e.row;return!t.stopFlag},getStopList:function(e,t){var a=this;return(0,r.default)((0,i.default)().m((function n(){var o;return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return o=e.map((function(e){return{Id:e[t],Type:2}})),n.n=1,(0,m.GetStopList)(o).then((function(n){if(n.IsSucceed){var o={};n.Data.forEach((function(e){o[e.Id]=!!e.Is_Stop})),e.forEach((function(e){o[e[t]]&&a.$set(e,"stopFlag",o[e[t]])}))}}));case 1:return n.a(2)}}),n)})))()},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0;var a=JSON.parse(JSON.stringify(this.formData));(0,s.GetUserStockInRecordPageList)((0,o.default)((0,o.default)({},this.queryInfo),a)).then((function(e){if(e.IsSucceed){var a=e.Data;t.tbData=a.Data,t.getStopList(t.tbData,"Component_Id"),t.totalNum=a.TotalCount,t.TotalAmount=a.TotalAmount?a.TotalAmount:0,t.TotalWeight=a.TotalWeight?a.TotalWeight:0}})).catch(console.error).finally((function(){t.tbLoading=!1}))},steelStockinDetail:function(e){this.$parent.openPackageDetail(e)},dateChange:function(){this.$emit("stockinDateOption",this.SearchDate)},stockinFn:function(){this.$emit("stockinOption",this.selectList)},tbSelectChange:function(e){this.selectList=e.records},revokeFn:function(){this.selectList.find((function(e){return!0===e.Is_Pack}))?this.$message.warning("所选构件有已在库内打包，请先从包里释放后再撤回"):this.$emit("revokeOption",this.selectList)},getRoleAuthorization:function(){var e=this;(0,l.RoleAuthorization)({menuId:this.$route.meta.Id,roleType:3,userId:localStorage.getItem("UserId")}).then((function(t){t.IsSucceed?t.Data.find((function(e){return"revokeWarehousing"===e.Code}))?e.revokeWarehousingBtn=!0:e.revokeWarehousingBtn=!1:e.$message({type:"warning",message:t.Message})}))}}}},"5d260":function(e,t,a){"use strict";a("ed474")},"67bc":function(e,t,a){"use strict";a.r(t);var n=a("94b9"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"6af1":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-main-wp"},[a("div",{staticClass:"assistant-wrapper"},[a("div",{staticClass:"btn-wrapper"},[a("el-button",{attrs:{size:"small",type:"primary",disabled:0===e.selectList.length},on:{click:e.stockinFn}},[e._v("成品入库")])],1),a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-info"},[a("span",[e._v("打包件总数 "+e._s(e.TotalAmount)+" 件")]),a("span",[e._v("打包件总量 "+e._s(e.TotalWeight)+" "+e._s(e.unit))])])])]),a("div",{staticClass:"cs-z-tb-wrapper"},[a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",loading:e.tbLoading,"element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"auto",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"40"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u([["Type","Volume","DIM","Gross","SteelAllWeight","ContractNo","Departure","Addressee"].includes(t.Code)?{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(e._s(o[t.Code]?o[t.Code]:"-"))])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]}}],null,!0)})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"150","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.steelStockinDetail(n)}}},[e._v("打包件详情")])]}}])})],2)],1),a("Pagination",{attrs:{total:e.totalNum,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)])},o=[]},"6e0d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",[a("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form,"label-width":"100px"}},[a("el-row",{staticStyle:{"margin-bottom":"10px"},attrs:{gutter:20}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"所属包号："}},[a("div",[e._v(e._s(e.row.Unique_Code))])]),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"项目名称："}},[a("div",[e._v(e._s(e.row.Project_Name))])]),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"打包件类型："}},[a("div",[e._v(e._s(e.row.Stock_Status_Name))])]),"仓库打包"==e.row.Stock_Status_Name?[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"仓库名称："}},[a("div",[e._v(e._s(e.row.Warehouse_Name))])]),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"库位名称："}},[a("div",[e._v(e._s(e.row.Location_Name))])])]:e._e()],2),a("el-row",{attrs:{gutter:20}},[a("el-form-item",{attrs:{label:"直发件",prop:"Is_Direct","label-width":"80px"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Direct,callback:function(t){e.$set(e.form,"Is_Direct",t)},expression:"form.Is_Direct"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"构件名称",prop:"Codes","label-width":"80px"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:e.form.Codes,callback:function(t){e.$set(e.form,"Codes",t)},expression:"form.Codes"}})],1),a("el-form-item",{attrs:{label:"模型ID",prop:"Model_Ids","label-width":"80px"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:e.form.Model_Ids,callback:function(t){e.$set(e.form,"Model_Ids",t)},expression:"form.Model_Ids"}})],1),a("el-form-item",{attrs:{label:"构件类型",prop:"ComponentType","label-width":"80px"}},[a("el-select",{ref:"SteelTypeRef",attrs:{placeholder:"请选择",filterable:""},on:{change:e.steelTypeChange},model:{value:e.form.ComponentType,callback:function(t){e.$set(e.form,"ComponentType",t)},expression:"form.ComponentType"}},e._l(e.TypeData,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Name,value:e.Code}})})),1)],1),a("el-form-item",{attrs:{label:"材质",prop:"Texture","label-width":"80px"}},[a("el-input",{attrs:{type:"text"},model:{value:e.form.Texture,callback:function(t){e.$set(e.form,"Texture",t)},expression:"form.Texture"}})],1),a("el-form-item",{attrs:{label:"长度",prop:"Length","label-width":"80px"}},[a("el-input",{attrs:{step:"any",type:"number"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length",t)},expression:"form.Length"}})],1),a("el-form-item",{attrs:{label:"规格",prop:"Spec","label-width":"80px"}},[a("el-input",{attrs:{type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.resetSearch}},[e._v("重置")])],1)],1)],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{height:"380px"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.pageInfo.TotalCount,page:e.pageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.gridPageChange,gridSizeChange:e.gridSizeChange},scopedSlots:e._u([{key:"ExtensionName",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.ExtensionName?n.ExtensionName:"-"))])]}}])})],1)])},o=[]},"73a4":function(e,t,a){"use strict";a("ea209")},"744b":function(e,t,a){"use strict";a("8269")},"7f89":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",[a("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form}},[a("el-form-item",{attrs:{label:"仓库：","label-width":"80px",required:""}},[a("el-select",{attrs:{placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位：","label-width":"80px",required:""}},[a("el-select",{attrs:{placeholder:"请选择库位"},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1)],1),a("div",{staticStyle:{"margin-bottom":"40px",height:"320px"}},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return["Num"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,align:t.Align,"min-width":t.Width,sortable:"","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.Wait_In_Count},model:{value:n.Num,callback:function(t){e.$set(n,"Num",t)},expression:"row.Num"}})]}},{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(e._f("displayValue")(n.Num)))])]}}],null,!0)}):"Is_Direct"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,align:t.Align,sortable:"","min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[n.Is_Direct?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(o[t.Code]?o[t.Code]:"-"))])]}}],null,!0)})]})),a("vxe-column",{attrs:{title:"操作",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,o=t.rowIndex;return[[a("vxe-button",{attrs:{type:"text",status:"primary"},on:{click:function(t){return e.deleteRowData(n,o)}}},[e._v("删除")])]]}}])})],2)],1),a("div",{staticClass:"align-center"},[a("el-button",{on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确定")])],1)])},o=[]},8269:function(e,t,a){},86392:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{staticClass:"search-wrapper"},[a("el-tabs",{on:{"tab-click":e.handleTabsClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"单件入库",name:"single"}}),a("el-tab-pane",{attrs:{label:"打包件入库",name:"package"}}),a("el-tab-pane",{attrs:{label:"我的入库记录",name:"record"}})],1),a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1),"single"==e.activeName||"record"==e.activeName?[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{key:1,ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1)]:e._e(),"single"==e.activeName||"record"==e.activeName?[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",attrs:{disabled:!e.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:e.setupPositionChange},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1)]:e._e(),"single"==e.activeName||"package"==e.activeName?[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"构件类型",prop:"ComponentType"}},[a("el-tree-select",{key:2,ref:"treeSelectObjectType",staticClass:"cs-tree-x",staticStyle:{width:"100%"},attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},model:{value:e.form.ComponentType,callback:function(t){e.$set(e.form,"ComponentType",t)},expression:"form.ComponentType"}})],1)],1)]:e._e(),"single"==e.activeName||"record"==e.activeName?[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"直发件",prop:"Is_Direct"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Direct,callback:function(t){e.$set(e.form,"Is_Direct",t)},expression:"form.Is_Direct"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1)]:e._e(),"record"==e.activeName?[a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"是否打包件",prop:"C_Type"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.C_Type,callback:function(t){e.$set(e.form,"C_Type",t)},expression:"form.C_Type"}},e._l(e.C_Type_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1)]:e._e(),"single"==e.activeName?[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"材质",prop:"Texture"}},[a("el-input",{attrs:{type:"text"},model:{value:e.form.Texture,callback:function(t){e.$set(e.form,"Texture",t)},expression:"form.Texture"}})],1)],1)]:e._e(),"single"==e.activeName||"record"==e.activeName?[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"构件名称",prop:"Codes_Format"}},[a("el-input",{attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text"},model:{value:e.form.Codes_Format,callback:function(t){e.$set(e.form,"Codes_Format",t)},expression:"form.Codes_Format"}})],1)],1)]:e._e(),"single"==e.activeName?[a("el-col",{attrs:{span:4,lg:4,xl:3}},[a("el-form-item",{attrs:{label:"模型ID",prop:"Model_Ids_Format"}},[a("el-input",{attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text"},model:{value:e.form.Model_Ids_Format,callback:function(t){e.$set(e.form,"Model_Ids_Format",t)},expression:"form.Model_Ids_Format"}})],1)],1)]:e._e(),"package"==e.activeName||"record"==e.activeName?[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"包编号",prop:"PackageSns_Format"}},[a("el-input",{attrs:{placeholder:"请输入（空格间隔筛选多个）",type:"text"},model:{value:e.form.PackageSns_Format,callback:function(t){e.$set(e.form,"PackageSns_Format",t)},expression:"form.PackageSns_Format"}})],1)],1)]:e._e(),"package"==e.activeName||"record"==e.activeName?[a("el-col",{attrs:{span:5,lg:5,xl:5}},[a("el-form-item",{attrs:{label:"包名称",prop:"PkgNO"}},[a("el-input",{attrs:{type:"text"},model:{value:e.form.PkgNO,callback:function(t){e.$set(e.form,"PkgNO",t)},expression:"form.PkgNO"}})],1)],1)]:e._e(),"package"==e.activeName?[a("el-col",{attrs:{span:4,lg:4,xl:3}},[a("el-form-item",{attrs:{label:"项目合同编号",prop:"ContractNo"}},[a("el-input",{attrs:{type:"text"},model:{value:e.form.ContractNo,callback:function(t){e.$set(e.form,"ContractNo",t)},expression:"form.ContractNo"}})],1)],1)]:e._e(),a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",{attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.resetSearch}},[e._v("重置")])],1)],1)],2)],1)],1),"single"==e.activeName?a("div",{staticClass:"main-wrapper"},[a("SingleList",{ref:"singleListRef",attrs:{unit:e.Unit,"form-data":e.form},on:{stockinOption:e.addDirect}})],1):"package"==e.activeName?a("div",{staticClass:"main-wrapper"},[a("PackageList",{ref:"packageListRef",attrs:{unit:e.Unit,"form-data":e.form},on:{stockinOption:e.addProduction}})],1):a("div",{staticClass:"main-wrapper"},[a("RecordList",{ref:"recordListRef",attrs:{unit:e.Unit,"form-data":e.form},on:{stockinDateOption:e.dateOption,revokeOption:e.revokeOptionFn}})],1),a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:e.dialogCfgs.title,visible:e.dialogShow,width:e.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(t){e.dialogShow=t}}},[a("keep-alive",[e.dialogShow?a(e.dialogCfgs.component,e._b({tag:"component",on:{dialogCancel:e.dialogCancel,dialogFormSubmitSuccess:e.dialogFormSubmitSuccess}},"component",e.dialogCfgs.props,!1)):e._e()],1)],1)],1)},o=[]},"8a5f":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",[a("el-form",{ref:"form",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.form}},[a("el-form-item",{attrs:{label:"仓库：","label-width":"80px",required:""}},[a("el-select",{attrs:{placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位：","label-width":"80px",required:""}},[a("el-select",{attrs:{placeholder:"请选择库位"},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1)],1),a("div",{staticStyle:{"margin-bottom":"40px",height:"320px"}},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return["Num"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"","edit-render":{},align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.Wait_In_Count},model:{value:n.Num,callback:function(t){e.$set(n,"Num",t)},expression:"row.Num"}})]}},{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(0==n.Num?1:n.Num))])]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(o[t.Code]?o[t.Code]:"-"))])]}}],null,!0)})]})),a("vxe-column",{attrs:{title:"操作",fixed:"right",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,o=t.rowIndex;return[[a("vxe-button",{attrs:{type:"text",status:"primary"},on:{click:function(t){return e.deleteRowData(n,o)}}},[e._v("删除")])]]}}])})],2)],1),a("div",{staticClass:"align-center"},[a("el-button",{on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确定")])],1)])},o=[]},"8b30":function(e,t,a){"use strict";a.r(t);var n=a("5b04"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"8f7b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),i=n(a("1da1"));a("b0c0"),a("d3b7"),a("ac1f"),a("25f0"),a("3ca3"),a("5319"),a("498a"),a("ddb0");a("2e8a");var r=n(a("4382")),l=n(a("b8b1")),s=n(a("ac5d")),c=n(a("06dd")),u=n(a("53fa")),d=n(a("d5e9")),f=n(a("dcce")),m=n(a("2082")),p=n(a("83b4")),g=n(a("33cf")),h=a("4d7a"),v=a("fd31"),b=a("2c08");t.default={name:"PROProductStockinList",components:{DirectDialog:r.default,ProductDialog:l.default,SingleList:s.default,PackageList:c.default,RecordList:u.default,PackageDetail:d.default,RevokeDialog:f.default},mixins:[m.default,p.default,g.default],data:function(){return{Is_Component_Data:[{Name:"是",Id:!0},{Name:"否",Id:!1}],C_Type_Data:[{Name:"构件",Id:"构件"},{Name:"打包件",Id:"打包件"}],treeSelectParams:{placeholder:"请选择",clearable:!0},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},form:{InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Area_Id:"",AreaPosition:"",Is_Direct:null,Codes:"",Codes_Format:"",Model_Ids:"",Model_Ids_Format:"",Texture:"",ComponentType:"",PkgNO:"",PackageSns:"",PackageSns_Format:"",ContractNo:"",C_Type:"",BeginDate:"",EndDate:""},activeName:"single",addPageArray:[{path:this.$route.path+"/addstock",hidden:!0,component:function(){return a.e("chunk-662cddcb").then(a.bind(null,"ffa9"))},name:"AddStock",meta:{title:"入库实收编辑"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return Promise.all([a.e("chunk-2d0c91c4"),a.e("chunk-560f118c")]).then(a.bind(null,"5bd6e"))},name:"AddStockDetail",meta:{title:"入库实收详情"}}],Unit:"",ProfessionalCode:"",ProfessionalId:"",dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},mounted:function(){var e=this;(0,h.getFactoryProfessional)().then((function(t){e.Unit=t[0].Unit,e.ProfessionalCode=t[0].Code,e.ProfessionalId=t[0].Id,e.getComponentTypeList()}))},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,b.getQueryParam)(["Project_Id","ProjectName","Sys_Project_Id"]);case 1:a=t.v,e.form.Project_Id=a.Project_Id,e.form.Sys_Project_Id=a.Sys_Project_Id,e.form.ProjectName=a.ProjectName,e.projectChange(e.form.Project_Id);case 2:return t.a(2)}}),t)})))()},methods:{getComponentTypeList:function(){var e=this;(0,v.GetCompTypeTree)({professional:"Steel"}).then((function(t){t.IsSucceed?(e.ObjectTypeList.data=t.Data,e.$nextTick((function(a){var n;null===(n=e.$refs)||void 0===n||null===(n=n.treeSelectObjectType)||void 0===n||n.treeDataUpdateFun(t.Data)}))):e.$message({type:"error",message:t.Message})}))},steelTypeChange:function(){var e=this;this.$nextTick((function(){e.form.Type_Name=e.$refs["SteelTypeRef"].selected.currentLabel}))},resetSearch:function(){var e=this;Object.assign(this.form,{InstallUnit_Id:"",SetupPosition:"",Sys_Project_Id:"",ProjectName:"",Project_Id:"",Area_Id:"",AreaPosition:"",Is_Direct:null,Codes:"",Codes_Format:"",Model_Ids:"",Model_Ids_Format:"",Texture:"",ComponentType:"",PkgNO:"",PackageSns:"",PackageSns_Format:"",ContractNo:"",C_Type:"",BeginDate:"",EndDate:""}),this.$nextTick((function(){e.handleSearch()}))},revokeOptionFn:function(e){this.openDialog({title:"撤销入库",width:"950px",component:"RevokeDialog",props:{rowsData:e}})},handleSearch:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,n,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:a=e.form.Codes_Format.trim(),a=a.replace(/\s+/g,"\n"),e.form.Codes=a,n=e.form.Model_Ids_Format.trim(),n=n.replace(/\s+/g,"\n"),e.form.Model_Ids=n,i=e.form.PackageSns_Format.trim(),i=i.replace(/\s+/g,"\n"),e.form.PackageSns=i,"single"===e.activeName?e.$refs.singleListRef.fetchData(1):"package"===e.activeName?e.$refs.packageListRef.fetchData(1):e.$refs.recordListRef.fetchData(1),(0,b.addSearchLog)(e.form);case 1:return t.a(2)}}),t)})))()},handleTabsClick:function(e,t){var a=this;this.activeName=e.name,Object.assign(this.form,{InstallUnit_Id:"",SetupPosition:"",Area_Id:"",AreaPosition:"",Is_Direct:null,Codes:"",Codes_Format:"",Model_Ids:"",Model_Ids_Format:"",Texture:"",ComponentType:"",PkgNO:"",PackageSns:"",PackageSns_Format:"",ContractNo:"",C_Type:"",BeginDate:"",EndDate:""}),this.$nextTick((function(){a.handleSearch()}))},addDirect:function(e){this.openDialog({title:"成品入库",width:"950px",component:"DirectDialog",props:{rowsData:e,Is_Pack:"single"!==this.activeName}})},addProduction:function(e){this.openDialog({title:"打包件入库",width:"950px",component:"ProductDialog",props:{rowsData:e,Is_Pack:"single"!==this.activeName}})},openPackageDetail:function(e){this.openDialog({title:"打包件详情",width:"950px",component:"PackageDetail",props:{row:e}})},dateOption:function(e){e?(this.form.BeginDate=e[0],this.form.EndDate=e[1]):(this.form.BeginDate="",this.form.EndDate=""),this.$refs.recordListRef.fetchData()},openDialog:function(e){e&&"[object Object]"===Object.prototype.toString.call(e)||(e={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,e,{}),this.dialogShow=!0},dialogCancel:function(e){this.dialogShow=!1,e&&"3"===e&&this.$refs.recordListRef.fetchData()},dialogFormSubmitSuccess:function(){this.dialogCancel(),"single"===this.activeName&&this.$refs.singleListRef.fetchData(),"package"===this.activeName&&this.$refs.packageListRef.fetchData()}}}},"94b9":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("5530"));a("a9e3"),a("d3b7");var i=a("6186"),r=a("edcd"),l=a("2e8a"),s=a("4d7a"),c=n(a("0f97"));t.default={name:"PackageDetail",components:{DynamicDataTable:c.default},props:{row:{type:Object,default:function(){return{}}}},data:function(){return{Is_Component_Data:[{Name:"是",Id:!0},{Name:"否",Id:!1}],TypeData:[],form:{Is_Direct:null,Codes:"",Model_Ids:"",ComponentType:"",Texture:"",Length:"",Spec:""},tbConfig:{},columns:[],tbData:[],pageInfo:{Page:1,TotalCount:0,PageSize:0},gridCode:"pro_package_stockin_detail",loading:!1,ProfessionalCode:"",ProfessionalId:""}},mounted:function(){var e=this;(0,s.getFactoryProfessional)().then((function(t){e.ProfessionalCode=t[0].Code,e.ProfessionalId=t[0].Id,e.getComponentTypeList()})),this.getGridByCode()},methods:{getComponentTypeList:function(){var e=this;(0,l.GetComponentTypeList)({Level:1,Category_Id:this.ProfessionalId,Factory_Id:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed&&(e.TypeData=t.Data)}))},steelTypeChange:function(){var e=this;this.$nextTick((function(){e.form.Type_Name=e.$refs["SteelTypeRef"].selected.currentLabel}))},getGridByCode:function(){var e=this;this.loading=!0,(0,i.GetGridByCode)({Code:this.gridCode}).then((function(t){t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList))})).then((function(){e.fetchData()}))},fetchData:function(){var e=this;(0,r.GetPackageItemList)((0,o.default)({Id:this.row.Component_Id},this.form)).then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)})).catch(console.error).finally((function(){e.loading=!1}))},setGrid:function(e){this.tbConfig=Object.assign({},e,{}),this.pageInfo.PageSize=parseInt(this.tbConfig.Row_Number)},setCols:function(e){this.columns=e},setGridData:function(e){this.tbData=e.Data,this.pageInfo.TotalCount=e.TotalCount},handleSearch:function(){this.fetchData()},resetSearch:function(){this.$refs["form"].resetFields(),this.form.Type_Name="",this.handleSearch()},gridPageChange:function(e){var t=e.page;this.pageInfo.Page=Number(t),this.fetchData()},gridSizeChange:function(e){var t=e.size;this.tbConfig.Row_Number=Number(t),this.pageInfo.PageSize=Number(t),this.pageInfo.Page=1,this.fetchData()},closeDialog:function(){this.$emit("dialogCancel")}}}},"960c":function(e,t,a){},"96f7":function(e,t,a){"use strict";a.r(t);var n=a("04f7"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},9952:function(e,t,a){},a6fc:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{"margin-bottom":"40px",height:"320px"}},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return["Num"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,sortable:"","edit-render":{},align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.In_Count},model:{value:n.Num,callback:function(t){e.$set(n,"Num",t)},expression:"row.Num"}})]}},{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(e._f("displayValue")(n.Num)))])]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",field:t.Code,title:t.Display_Name,align:t.Align,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(o[t.Code]?o[t.Code]:"-"))])]}}],null,!0)})]})),a("vxe-column",{attrs:{title:"操作",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row,o=t.rowIndex;return[[a("vxe-button",{attrs:{type:"text",status:"primary"},on:{click:function(t){return e.deleteRowData(n,o)}}},[e._v("删除")])]]}}])})],2)],1),a("div",{staticClass:"align-center"},[a("el-button",{on:{click:e.cancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确定")])],1)])},o=[]},a9ba:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("fb6a");var o=n(a("15ac")),i=a("c685"),r=n(a("333d"));t.default={components:{Pagination:r.default},mixins:[o.default],props:{title:{type:String,default:""},visible:{type:Boolean,default:!1},columns:{type:Array,default:function(){return[]}}},data:function(){return{tablePageSize:i.tablePageSize,tbLoading:!1,tableData:[],queryInfo:{Page:1,PageSize:i.tablePageSize[0]},total:0}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},methods:{handleOpen:function(){this.dialogVisible=!0},fetchData:function(e){this.list=e,this.total=e.length,this.pageChange({page:1,limit:i.tablePageSize[0]})},handleClose:function(){},pageChange:function(e){var t=e.page,a=e.limit;this.tableData=1===t?this.list.slice(0,a):this.list.slice((t-1)*a,t*a)}}}},ac5d:function(e,t,a){"use strict";a.r(t);var n=a("3bfa"),o=a("3b5f");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("d327");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"1b2e615a",null);t["default"]=l.exports},acd4:function(e,t,a){"use strict";a.r(t);var n=a("cf88"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},ae45:function(e,t,a){},b8b1:function(e,t,a){"use strict";a.r(t);var n=a("8a5f"),o=a("96f7");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("fe35");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"4bcfee20",null);t["default"]=l.exports},bfa0:function(e,t,a){"use strict";a.r(t);var n=a("8f7b"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},ce31:function(e,t,a){"use strict";a.r(t);var n=a("f7a1"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},cf88:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9c4"),a("b64b"),a("d3b7");var o=n(a("5530")),i=n(a("c14f")),r=n(a("1da1")),l=a("edcd"),s=n(a("333d")),c=n(a("15ac")),u=a("c685");t.default={name:"PackageList",components:{Pagination:s.default},mixins:[c.default],props:{unit:{type:String,default:""},formData:{type:Object,default:function(){return{}}}},data:function(){return{TotalAmount:0,TotalWeight:0,totalNum:0,tbConfig:{},columns:[],tbData:[],selectList:[],queryInfo:{Page:1,PageSize:u.tablePageSize[0]},tablePageSize:u.tablePageSize,loading:!1,tbLoading:!1,gridCode:"pro_wait_package_stockin_list"}},mounted:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig(e.gridCode);case 1:e.fetchData(1);case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0;var a=JSON.parse(JSON.stringify(this.formData));(0,l.GetWaitingStockInPacking2ndPageList)((0,o.default)((0,o.default)({},this.queryInfo),a)).then((function(e){if(e.IsSucceed){var a=e.Data;t.tbData=a.Data,t.totalNum=a.TotalCount,t.TotalAmount=a.TotalAmount?a.TotalAmount:0,t.TotalWeight=a.TotalWeight?a.TotalWeight:0}})).catch(console.error).finally((function(){t.tbLoading=!1,t.selectList=[]}))},steelStockinDetail:function(e){this.$parent.openPackageDetail(e)},stockinFn:function(){this.$emit("stockinOption",this.selectList)},tbSelectChange:function(e){this.selectList=e.records}}}},d327:function(e,t,a){"use strict";a("960c")},d5e9:function(e,t,a){"use strict";a.r(t);var n=a("6e0d"),o=a("67bc");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("5d260");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"d191bf2c",null);t["default"]=l.exports},dcce:function(e,t,a){"use strict";a.r(t);var n=a("a6fc"),o=a("3079");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("f934d");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"0e142874",null);t["default"]=l.exports},e3cf:function(e,t,a){"use strict";a.r(t);var n=a("86392"),o=a("bfa0");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("1411");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"391d99a4",null);t["default"]=l.exports},ea209:function(e,t,a){},ed474:function(e,t,a){},edcd:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteStockInRecord=u,t.GetComponentList=d,t.GetPackageItemList=s,t.GetUserRepairStockInRecordPageList=m,t.GetUserStockInRecordPageList=c,t.GetWaitingStockIn2ndPageList=i,t.GetWaitingStockInPacking2ndPageList=l,t.SaveStockIn2nd=r,t.SubmitRepairToStockIn=f;var o=n(a("b775"));n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetWaitingStockIn2ndPageList",method:"post",data:e})}function r(e){return(0,o.default)({url:"/PRO/ComponentStockIn/SaveStockIn2nd",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetWaitingStockInPacking2ndPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Packing/GetPackageItemList",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetUserStockInRecordPageList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ComponentStockIn/DeleteStockInRecord",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetComponentList",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/ComponentStockIn/SubmitRepairToStockIn",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetUserRepairStockInRecordPageList",method:"post",data:e})}},f2d0:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FIX_COLUMN=void 0;t.FIX_COLUMN=[]},f7a1:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("d81d"),a("a434"),a("e9f5"),a("910d"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("2532"),a("c7cd");var n=a("6186"),o=a("edcd"),i=a("f2d0"),r=a("209b");t.default={name:"DirectDialog",props:{rowsData:{type:Array,default:function(){return[]}},Is_Pack:{type:Boolean,default:!0}},data:function(){return{warehouses:[],locations:[],tbLoading:!1,btnLoading:!1,form:{Warehouse_Id:"",Location_Id:""},columns:[],tbData:[],tbConfig:{},TotalCount:0,Page:0,PageSize:-1,gridCode:"pro_choose_single_stockin_list"}},watch:{rowsData:{handler:function(e,t){this.tbData=JSON.parse(JSON.stringify(e)),this.tbData.map((function(e){return 0==e.Num&&(e.Num=e.Wait_In_Count),e}))},immediate:!0,deep:!0}},mounted:function(){this.getGridByCode()},created:function(){this.getWarehouseListOfCurFactory()},methods:{getGridByCode:function(){var e=this;(0,n.GetGridByCode)({Code:this.gridCode}).then((function(t){var a=t.IsSucceed,n=t.Data,o=t.Message;if(a){e.tbConfig=Object.assign({},e.tbConfig,n.Grid);var r=n.ColumnList||[];e.columns=r.filter((function(e){return e.Is_Display})).map((function(e){return i.FIX_COLUMN.includes(e.Code)&&(e.fixed="left"),e}))}else e.$message({message:o,type:"error"})})).catch((function(){}))},getWarehouseListOfCurFactory:function(){var e=this;(0,r.GetWarehouseListOfCurFactory)({}).then((function(t){t.IsSucceed&&(e.warehouses=t.Data)}))},wareChange:function(e){var t=this;this.form.Location_Id="",(0,r.GetLocationList)({Warehouse_Id:e}).then((function(e){e.IsSucceed&&(t.locations=e.Data)}))},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Num"===t.field},deleteRowData:function(e,t){this.tbData.splice(t,1)},cancel:function(){this.$emit("dialogCancel")},submit:function(){var e=this;return this.form.Warehouse_Id?this.form.Location_Id?(this.btnLoading=!0,void(0,o.SaveStockIn2nd)({Warehouse_Id:this.form.Warehouse_Id,Location_Id:this.form.Location_Id,Is_Pack:this.Is_Pack,Details:this.tbData}).then((function(t){t.IsSucceed?e.$emit("dialogFormSubmitSuccess"):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1})).catch((function(){e.btnLoading=!1}))):this.$message.warning("必须选择库位"):this.$message.warning("必须选择仓库")}}}},f934d:function(e,t,a){"use strict";a("4378")},fa83:function(e,t,a){"use strict";a("9952")},fe35:function(e,t,a){"use strict";a("4e2d")}}]);