(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-66f5fba2","chunk-2d0e2790","chunk-2d0c91c4"],{"0018":function(e,t,a){"use strict";a("d446")},"03f9":function(e,t,a){"use strict";a.r(t);var n=a("2870"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"03ff":function(e,t,a){"use strict";a.r(t);var n=a("8bcc"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"045f":function(e,t,a){"use strict";a("dbba")},"04d8":function(e,t,a){},"05d2":function(e,t,a){"use strict";a("7a9f")},"07a8":function(e,t,a){"use strict";a.r(t);var n=a("3c033"),o=a("c625");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("65d9");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"114b120e",null);t["default"]=s.exports},"0885":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("d3b7"),a("159b");var n=a("a667");t.default={data:function(){return{form:{Type:"",Remark:""},btnLoading:!1,labelList:[],filterLabelList:[],rules:{Type:{required:!0,message:"请选择",trigger:"change"}}}},methods:{init:function(e){var t=this,a=e.filter((function(e){return e.Sup_Count>0}));this.labelList=[],this.filterLabelList=[],a.forEach((function(e){1===e.Sup_Count&&t.labelList.push({label:e.SteelName,unPackCount:1,SteelUnique:e.SteelUnique,number:1})})),a.forEach((function(e){e.Sup_Count>1&&t.filterLabelList.push({label:e.SteelName,unPackCount:e.Sup_Count,SteelUnique:e.SteelUnique,number:1})}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};e.labelList.concat(e.filterLabelList).forEach((function(e){a.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),"1"===e.form.Type?(a.Package.Remark=e.form.Remark,e.btnLoading=!0,(0,n.Add)(a).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))):e.$emit("checkPackage",{data:a,type:3})}))}}}},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=r(),s=e-i,l=20,c=0;t="undefined"===typeof t?500:t;var u=function(){c+=l;var e=Math.easeInOutQuad(c,i,s,t);o(e),c<t?n(u):a&&"function"===typeof a&&a()};u()}},"0a9e":function(e,t,a){"use strict";a.r(t);var n=a("d5c5"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"0bf5":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("d3b7");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=n(a("0f97")),l=n(a("15ac")),c=a("a667");t.default={components:{DynamicDataTable:s.default},mixins:[l.default],props:{zParams:{type:Object,default:function(){}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbLoading:!1,tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},columns:[],total:0,tbData:[]}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_item_page_list,".concat(e.typeEntity.Code));case 1:a=t.v,e.$emit("getColumn",a.filter((function(e){return e.Is_Display}))||[]);case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){this.$emit("getList")},fetchRightData:function(e){var t=this;if(!e)return this.tbData=[],void(this.total=0);this.tbLoading=!0,(0,c.GetPageStorageSteelsBySearchItem)((0,o.default)((0,o.default)({Id:e},this.zParams),{},{PageInfo:(0,o.default)({},this.queryInfo)})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleSelect:function(e){this.$emit("selectList",e)}}}},"0bfe":function(e,t,a){"use strict";a.r(t);var n=a("c7edb"),o=a("a033");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("c92b");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"72933378",null);t["default"]=s.exports},"0ce7":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1"));a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var i=n(a("9b15")),s=a("5c96"),l=a("21c4"),c=a("6186"),u=function(){(0,c.SecurityToken)().then((function(e){sessionStorage.setItem("ossToken",JSON.stringify(e.Data))}))};u(),setInterval((function(){u()}),114e4);t.default={name:"OSSUpload",mixins:[s.Upload],props:{ossOnSuccess:{type:Function,default:Function},piecesize:{type:Number,default:2},httpRequest:{type:Function,default:function(e){var t=this,a=JSON.parse(sessionStorage.getItem("ossToken"));this.$nextTick((function(){var n,s=null!==(n=t.data)&&void 0!==n&&n.piecesize?1*t.data.piecesize:2,u=new i.default({region:"oss-"+a.regionId,secure:!0,accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken,bucket:a.bucket,refreshSTSToken:function(){var e=(0,r.default)((0,o.default)().m((function e(){var a;return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.securityToken();case 1:return a=e.v,e.a(2,{accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken})}}),e)})));function a(){return e.apply(this,arguments)}return a}(),refreshSTSTokenInterval:9e5}),d=e.file,f=new Date;u.multipartUpload((0,l.getTenantId)()+"/"+f.getFullYear()+"/"+(1*f.getMonth()+1)+"/"+f.getDate()+"/"+f.getMinutes()+"_"+f.getSeconds()+"_"+f.getMilliseconds()+"/"+d.name,d,{progress:function(t,a){this.process=a,e.onProgress({percent:Math.floor(100*t)})},parallel:4,partSize:1048576*s,meta:{}}).then((function(t){if(200===t.res.statusCode){var a,n=t.res.requestUrls[0]&&t.res.requestUrls[0].split("?")[0];!1===(null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.callback)?e.onSuccess({Data:n+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name}):(0,c.GetOssUrl)({url:n}).then((function(t){e.onSuccess({Data:n+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name,encryptionUrl:t.Data})}))}}),(function(t){e.onError(t)}))}))}}},data:function(){return{process:null}},watch:{process:function(e){this.$emit("getprocess",e)}},mounted:function(){},methods:{handleFn:function(e){},securityToken:function(){return new Promise((function(e,t){(0,c.SecurityToken)({}).then((function(t){e(t.Data)})).catch((function(e){t(e)}))}))}}}},"0d40":function(e,t,a){"use strict";a.r(t);var n=a("3b2f"),o=a("6737");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("fed2");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"12c00924",null);t["default"]=s.exports},"0d54":function(e,t,a){"use strict";a.r(t);var n=a("8662"),o=a("687a");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("5332");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"c5031a66",null);t["default"]=s.exports},"0d9a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddArea=oe,t.AddDeepFile=ee,t.AddMonomer=J,t.AreaDelete=ie,t.AreaGetEntity=ae,t.AttachmentGetEntities=U,t.ChangeLoad=P,t.CommonImportDeependToComp=X,t.ContactsAdd=M,t.ContactsDelete=$,t.ContactsEdit=F,t.ContactsGetEntities=G,t.ContactsGetEntity=j,t.ContactsGetTreeList=R,t.DeleteMonomer=Z,t.DepartmentAdd=L,t.DepartmentDelete=k,t.DepartmentEdit=T,t.DepartmentGetEntities=D,t.DepartmentGetEntity=I,t.DepartmentGetList=w,t.EditArea=re,t.EditMonomer=Q,t.FileAdd=b,t.FileAddType=f,t.FileDelete=h,t.FileEdit=v,t.FileGetEntity=p,t.FileHistory=S,t.FileMove=_,t.FileTypeAdd=d,t.FileTypeDelete=u,t.FileTypeEdit=m,t.FileTypeGetEntities=r,t.FileTypeGetEntity=i,t.GeAreaTrees=ne,t.GetAreaTreeList=te,t.GetDictionaryDetailListByCode=A,t.GetEntitiesByRecordId=E,t.GetEntitiesProject=l,t.GetFileCatalog=s,t.GetFilesByType=c,t.GetGetMonomerList=H,t.GetLoadingFiles=C,t.GetMonomerEntity=Y,t.GetPartCodeList=y,t.GetProMonomerList=K,t.GetProjectComponentCodeList=g,t.GetProjectEntity=se,t.GetProjectsflowmanagementAdd=B,t.GetProjectsflowmanagementEdit=W,t.GetProjectsflowmanagementInfo=z,t.GetShortUrl=q,t.ImportDeependToSteel=V,t.ImportPartList=le,t.SysuserGetUserEntity=O,t.SysuserGetUserList=x,t.UserGroupTree=N;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:e})}function i(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetFileCatalog",method:"post",data:e})}function l(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntitiesProject",method:"post",data:e})}function c(e){return(0,o.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:e})}function u(e){return(0,o.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:e})}function d(e){return(0,o.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:e})}function f(e){return(0,o.default)({url:"/SYS/Sys_FileType/AddType",method:"post",data:e})}function m(e){return(0,o.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:e})}function p(e){return(0,o.default)({url:"/SYS/Sys_File/GetEntity",method:"post",data:e})}function h(e){return(0,o.default)({url:"/SYS/Sys_File/Delete",method:"post",data:e})}function g(e){return(0,o.default)({url:"/pro/component/GetProjectComponentCodeList",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Part/GetPartCodeList",method:"post",data:e})}function b(e){return(0,o.default)({url:"/SYS/Sys_File/Add",method:"post",data:e})}function v(e){return(0,o.default)({url:"/SYS/Sys_File/Edit",method:"post",data:e})}function _(e){return(0,o.default)({url:"/SYS/Sys_File/Move",method:"post",data:e})}function P(e){return(0,o.default)({url:"/SYS/Sys_File/IsLoad",method:"post",data:e})}function S(e){return(0,o.default)({url:"/SYS/Sys_File/OldFile",method:"post",data:e})}function C(e){return(0,o.default)({url:"/SYS/Sys_File/GetBIMList",method:"post",data:e})}function I(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetEntity",method:"post",data:e})}function D(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetEntities",method:"post",data:e})}function k(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Delete",method:"post",data:e})}function T(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Edit",method:"post",data:e})}function L(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Add",method:"post",data:e})}function w(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetList",method:"post",data:e})}function x(e){return(0,o.default)({url:"/SYS/User/GetUserList",method:"post",data:e})}function O(e){return(0,o.default)({url:"/SYS/User/GetUserEntity",method:"post",data:e})}function N(e){return(0,o.default)({url:"/SYS/UserGroup/GetChildGroupTree",method:"post",data:e})}function A(e){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function j(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetEntity",method:"post",data:e})}function G(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:e})}function R(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:e})}function $(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Delete",method:"post",data:e})}function F(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Edit",method:"post",data:e})}function M(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Add",method:"post",data:e})}function U(e){return(0,o.default)({url:"/SYS/Sys_File/GetAttachmentEntities",method:"post",data:e})}function E(e){return(0,o.default)({url:"/SYS/Sys_File/GetEntitiesByRecordId",method:"post",data:e})}function B(e){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/Add",method:"post",data:e})}function W(e){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/Edit",method:"post",data:e})}function z(e){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/GetEntity",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PLM/XModel/GetShortUrl",method:"post",data:e})}function V(e){return(0,o.default)({url:"/plm/component/ImportDeependToSteel",method:"get",params:e})}function H(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetGetMonomerList",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetMonomerEntity",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetProMonomerList",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/AddMonomer",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/EditMonomer",method:"post",data:e})}function Z(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/DeleteMonomer",method:"post",data:e})}function X(e){return(0,o.default)({url:"/plm/MaterialInfo/CommonImportDeependToComp",method:"get",params:e})}function ee(e){return(0,o.default)({url:"/PLM/Component/AddDeepFile",method:"post",data:e,timeout:18e5})}function te(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetAreaTreeList",method:"post",data:e})}function ae(e){return(0,o.default)({url:"/PRO/Project/GetArea",method:"post",data:e})}function ne(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function oe(e){return(0,o.default)({url:"/PRO/Project/AddArea",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/Project/EditArea",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/Project/Delete",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:e})}function le(e){return(0,o.default)({url:"/PRO/Part/ImportPartList",method:"post",data:e})}},"0da3":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("d3b7"),a("159b");var n=a("a667");t.default={data:function(){return{form:{Type:"",Remark:""},btnLoading:!1,labelList:[],filterLabelList:[],rules:{Type:{required:!0,message:"请选择",trigger:"change"}}}},methods:{init:function(e){var t=this,a=e.filter((function(e){return e.Sup_Count>0}));this.labelList=[],this.filterLabelList=[],a.forEach((function(e){1===e.Sup_Count&&t.labelList.push({label:e.SteelName,unPackCount:1,SteelUnique:e.SteelUnique,number:1})})),a.forEach((function(e){e.Sup_Count>1&&t.filterLabelList.push({label:e.SteelName,unPackCount:e.Sup_Count,SteelUnique:e.SteelUnique,number:1})}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};e.labelList.concat(e.filterLabelList).forEach((function(e){a.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),"1"===e.form.Type?(a.Package.Remark=e.form.Remark,e.btnLoading=!0,(0,n.Add)(a).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))):e.$emit("checkPackage",{data:a,type:3})}))}}}},"0eac":function(e,t,a){"use strict";a.r(t);var n=a("ab53"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"0ed8":function(e,t,a){"use strict";a("db2c4")},"0f42":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("5530"));a("e9c4"),a("b64b");var r=a("a667");t.default={props:{customParams:{type:Object,default:function(){}}},data:function(){return{form:{Remark:"",Type:""},btnLoading:!1,rules:{Type:[{required:!0,message:"请选择",trigger:"change"}]}}},methods:{handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=JSON.parse(JSON.stringify(e.customParams));delete a.pageInfo;var n={Package:{ProjectID:localStorage.getItem("CurReferenceId"),Remark:e.form.Remark},Steels:[],Search:(0,o.default)((0,o.default)({},a),{},{Type:e.form.Type})};"2"!==e.form.Type?(e.btnLoading=!0,(0,r.BatchAdd)(n).then((function(t){t.IsSucceed?e.$emit("refresh"):e.$message({message:t.Message,type:"error"}),e.$emit("close"),e.btnLoading=!1}))):e.$emit("checkPackage",{data:n,type:1})}))}}}},"0f94":function(e,t,a){"use strict";a.r(t);var n=a("479a"),o=a("bcd7");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"11a9462e",null);t["default"]=s.exports},"0fbf":function(e,t,a){"use strict";a.r(t);var n=a("2290"),o=a("2a16");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"735a6f50",null);t["default"]=s.exports},"10a2":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"i-dialog",attrs:{"append-to-body":"",visible:e.visible,title:"编辑包信息",width:"30%"},on:{"update:visible":function(t){e.visible=t},close:e.handleClose}},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包编号",prop:"PackageSN"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.PackageSN,callback:function(t){e.$set(e.form,"PackageSN",t)},expression:"form.PackageSN"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总重量",prop:"AllWeight"}},[a("el-input",{model:{value:e.form.AllWeight,callback:function(t){e.$set(e.form,"AllWeight",t)},expression:"form.AllWeight"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包名称",prop:"PkgNO"}},[a("el-input",{model:{value:e.form.PkgNO,callback:function(t){e.$set(e.form,"PkgNO",t)},expression:"form.PkgNO"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目合同编号",prop:"ContractNo"}},[a("el-input",{model:{value:e.form.ContractNo,callback:function(t){e.$set(e.form,"ContractNo",t)},expression:"form.ContractNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"构件类型",prop:"GoodsName"}},[a("el-input",{model:{value:e.form.GoodsName,callback:function(t){e.$set(e.form,"GoodsName",t)},expression:"form.GoodsName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"收件人",prop:"Addressee"}},[a("el-input",{model:{value:e.form.Addressee,callback:function(t){e.$set(e.form,"Addressee",t)},expression:"form.Addressee"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"体积",prop:"Volume"}},[a("el-input",{model:{value:e.form.Volume,callback:function(t){e.$set(e.form,"Volume",t)},expression:"form.Volume"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"毛重系数",prop:"Gross"}},[a("el-input",{model:{value:e.form.Gross,callback:function(t){e.$set(e.form,"Gross",t)},expression:"form.Gross"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"起运港",prop:"Departure"}},[a("el-input",{model:{value:e.form.Departure,callback:function(t){e.$set(e.form,"Departure",t)},expression:"form.Departure"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"尺寸",prop:"DIM"}},[a("el-input",{model:{value:e.form.DIM,callback:function(t){e.$set(e.form,"DIM",t)},expression:"form.DIM"}})],1)],1),a("el-col",{attrs:{span:24,prop:"Remark"}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:50,"show-word-limit":"",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:e.handleClose}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1)],1)},o=[]},"110d":function(e,t,a){},11723:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"custom-tb cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,"pager-count":5,"small-pagination":"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,handleRowClick:e.handleRowClick,select:e.handleSelect,selectAll:e.selectAll,tableSearch:e.tableSearch}})],1)},o=[]},"11cc":function(e,t,a){"use strict";a.r(t);var n=a("0bf5"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},1276:function(e,t,a){"use strict";var n=a("c65b"),o=a("e330"),r=a("d784"),i=a("825a"),s=a("861d"),l=a("1d80"),c=a("4840"),u=a("8aa5"),d=a("50c4"),f=a("577e"),m=a("dc4a"),p=a("14c3"),h=a("9f7f"),g=a("d039"),y=h.UNSUPPORTED_Y,b=4294967295,v=Math.min,_=o([].push),P=o("".slice),S=!g((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var a="ab".split(e);return 2!==a.length||"a"!==a[0]||"b"!==a[1]})),C="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;r("split",(function(e,t,a){var o="0".split(void 0,0).length?function(e,a){return void 0===e&&0===a?[]:n(t,this,e,a)}:t;return[function(t,a){var r=l(this),i=s(t)?m(t,e):void 0;return i?n(i,t,r,a):n(o,f(r),t,a)},function(e,n){var r=i(this),s=f(e);if(!C){var l=a(o,r,s,n,o!==t);if(l.done)return l.value}var m=c(r,RegExp),h=r.unicode,g=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(y?"g":"y"),S=new m(y?"^(?:"+r.source+")":r,g),I=void 0===n?b:n>>>0;if(0===I)return[];if(0===s.length)return null===p(S,s)?[s]:[];var D=0,k=0,T=[];while(k<s.length){S.lastIndex=y?0:k;var L,w=p(S,y?P(s,k):s);if(null===w||(L=v(d(S.lastIndex+(y?k:0)),s.length))===D)k=u(s,k,h);else{if(_(T,P(s,D,k)),T.length===I)return T;for(var x=1;x<=w.length-1;x++)if(_(T,w[x]),T.length===I)return T;k=D=L}}return _(T,P(s,D)),T}]}),C||!S,y)},1373:function(e,t,a){"use strict";a.r(t);var n=a("2b9d"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},1389:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("a630"),a("caad"),a("a15b"),a("d81d"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("466d"),a("159b"),a("ddb0");var o=n(a("c14f")),r=n(a("1da1")),i=n(a("2909")),s=n(a("5530")),l=a("0d9a"),c=n(a("bc3a")),u=n(a("c7ab")),d=a("b28f"),f=a("5012"),m=a("c24f"),p=a("0e9a"),h=a("21c4"),g=n(a("bf8b")),y=a("e144"),b=a("ed08"),v=a("2ef0"),_=(a("f382"),a("2f62")),P={Id:"",Doc_Catelog:"",Doc_Type:"",Project_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",IsChanged:!1,Is_Load:!1,Doc_File:"",ishistory:!0,BimId:"",Drawing_Match_Type:1,Is_Big_File:!1,model:{UserIds:[],Title:"",Content:""},Component_Codes:[]};t.default={components:{OSSUpload:u.default,formItem:g.default},props:{typeEntity:{type:Object,default:function(){}},isCzd:{type:Boolean,default:!1},isLJXT:{type:Boolean,default:!1},isBJXT:{type:Boolean,default:!1},isGjXT:{type:Boolean,default:!1}},data:function(){return{isNotify:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,btnLoading:!1,form:(0,s.default)({},P),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}],Drawing_Match_Type:[{required:!0,message:"请选择",trigger:"change"}]},treeParams:{clickParent:!0,filterable:!1,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},selectParams:{multiple:!1,clearable:!0,placeholder:"请输入内容"},fileType:"",curFile:"",bimvizId:"",isDeep:!1,projectId:"",professionalCode:"",isSHQD:"",isEditSCYTH:!1,modelApp:"glendale",uploadFileLength:0,fileNums:0,compList:[],selectLoading:!1,allCompList:[],allPartList:[],relatedItem:[],disabledOptions:[!1,!1,!1]}},computed:(0,s.default)({drawingList:function(){var e=this.isComp?"构件号":this.isPart?"零件名":"部件名";return[{label:"名称",value:1,disabled:this.disabledOptions[0]},{label:"".concat(e,"_页码_版本"),value:2,disabled:this.disabledOptions[1]},{label:"".concat(e,"_版本"),value:3,disabled:this.disabledOptions[2]}]},BIMFiles:function(){return"PLMBimFiles"===this.$route.name},isGlendale:function(){return this.BIMFiles&&"glendale"===this.modelApp},CadFiles:function(){return"PRODeepenFiles"===this.$route.name},isCZD:function(){return this.isCzd||"原材料材质单"===this.form.Type_Name},isComp:function(){return"构件详图"===this.form.Type_Name||this.isGjXT},isPart:function(){return"零件详图"===this.form.Type_Name||this.isLJXT},isUnitPart:function(){return"部件详图"===this.form.Type_Name||this.isBJXT}},(0,_.mapGetters)("tenant",["isVersionFour"])),watch:{"fileList.length":function(e){if(this.isVersionFour){var t=this.fileList.some((function(e){return!e.name.includes("_")})),a=this.fileList.every((function(e){return(e.name.match(/_/g)||[]).length>=1})),n=this.fileList.every((function(e){return(e.name.match(/_/g)||[]).length>=2}));this.disabledOptions=[!1,!1,!1],t?(this.disabledOptions[1]=!0,this.disabledOptions[2]=!0,2!==this.form.Drawing_Match_Type&&3!==this.form.Drawing_Match_Type||(this.form.Drawing_Match_Type=1)):n?(this.disabledOptions[1]=!1,this.disabledOptions[2]=!1):a&&(this.disabledOptions[1]=!0,this.disabledOptions[2]=!1,2===this.form.Drawing_Match_Type&&(this.form.Drawing_Match_Type=1))}}},created:function(){this.fileType=this.$route.name,"PLMPicVideoFiles"===this.fileType&&(this.allowFile="image/*"),this.BIMFiles&&(this.allowFile=".ifc,.bzip,.bzip2,.glzip"),this.isVersionFour||delete this.form.Drawing_Match_Type},methods:{getCompList:function(){var e=this;this.isPart?(0,l.GetPartCodeList)({SysProjectId:this.projectId,type:2}).then((function(t){t.IsSucceed?e.allPartList=t.Data:e.$message({message:t.Message,type:"error"})})):this.isUnitPart?(0,l.GetPartCodeList)({SysProjectId:this.projectId,type:3}).then((function(t){t.IsSucceed?e.allPartList=t.Data:e.$message({message:t.Message,type:"error"})})):(0,l.GetProjectComponentCodeList)({Sys_Project_Id:this.projectId}).then((function(t){t.IsSucceed?e.allCompList=t.Data||[]:e.$message({message:t.Message,type:"error"})}))},remoteMethod:function(e){var t=this;e?(this.selectLoading=!0,setTimeout((function(){var a=e.split(" ").map((function(e){return e.toLowerCase()})).filter((function(e){return""!==e}));t.compList=(t.isPart||t.isUnitPart?t.allPartList:t.allCompList).filter((function(e){return a.length>1?a.some((function(t){return e.toLowerCase()===t})):a.some((function(t){return e.toLowerCase().includes(t)}))})),t.compList=t.compList.map((function(e){return{label:e,value:e}})),t.compList.length>0&&t.compList.unshift({label:"全部",value:"all"}),t.selectLoading=!1}),200)):this.compList=[]},selectChange:function(e){if(e.includes("all")){var t=(0,b.deepClone)(this.compList);t.shift(),this.relatedItem=this.relatedItem.filter((function(e){return"all"!==e})),this.relatedItem=Array.from(new Set([].concat((0,i.default)(this.relatedItem),(0,i.default)(t.map((function(e){return e.value}))))))}},visibleChange:function(e){e||this.remoteMethod()},onExceed:function(){"edit"===this.type?this.$message.error("最多只能上传1个文件"):this.$message.error("最多只能上传1000个文件")},getTemplate:function(){var e=this,t="plm_steels_detailImport,".concat(this.professionalCode);(0,f.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,p.downloadBlobFile)(t,"".concat(e.form.Type_Name,"_深化清单导入模板"))}))},handleRadioChange:function(e){e&&"edit"===this.type&&this.form.Doc_Title&&(this.form.model.Title="资料变更通知："+this.form.Doc_Title)},handleChange:function(e,t){this.uploadFileLength=t.length},beforeUpload:function(e){this.curFile=e,this.loading=!0,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this,n=0;this.fileList.filter((function(t,a){t.name===e.name&&(n=a)})),this.fileList.splice(n,1),this.attachments.splice(n,1);var o="",r="";t.forEach((function(e){o=o+","+e.name.substring(0,e.name.lastIndexOf(".")),r=r+","+e.name})),this.form.Doc_Title=o.substring(1),this.form.Doc_Content=o.substring(1),this.form.Doc_File=r.substring(1),this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.loading=!t.every((function(e){return"success"===e.status})),setTimeout((function(){a.btnLoading=!t.every((function(e){return"success"===e.status}))}),1e3)},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){var n=this;a=(0,v.uniqBy)(a,"name"),this.fileList=a,this.attachments=this.fileList.filter((function(e){return"success"===e.status})).map((function(e){return{File_Url:e.response.Data.split("*")[0],File_Size:e.response.Data.split("*")[1],File_Type:e.response.Data.split("*")[2],File_Name:e.response.Data.split("*")[3]}}));var o=this.attachments.map((function(e){return e.File_Name})).join(","),r=this.attachments.map((function(e){return e.File_Name.substring(0,e.File_Name.lastIndexOf("."))})).join(",");if(this.form.Doc_Title=this.form.Doc_Content=r,this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.form.Doc_File=o,this.loading=!a.every((function(e){return"success"===e.status})),this.fileNums++,this.fileNums>this.uploadFileLength-10&&a.some((function(e){return"ready"===e.status}))){var i=a.filter((function(e){return"ready"===e.status}));i.forEach((function(e){n.retryUpload(e)}))}setTimeout((function(){n.btnLoading=!a.every((function(e){return"success"===e.status}))}),1e3)},retryUpload:function(e){this.$refs.companyUpload.submit(e)},handleOpen:function(e,t,a){var n=this,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0,i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];this.isNotify=!1,this.projectId=r,this.isDeep=o,this.form=(0,s.default)((0,s.default)({},P),{},{model:{UserIds:[],Title:"",Content:""}}),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.isSHQD=t.isSHQD,this.professionalCode=t.Code,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.isEditSCYTH=i,this.fileNums=0,"add"===this.type?(this.fileList=[],this.title="新增文件",this.form.Id=""):(this.title="编辑文件",(0,l.AttachmentGetEntities)({recordId:t.Id}).then((function(e){n.attachments=e.Data.map((function(e){return delete e.Id,e})),n.fileList=e.Data.map((function(e){return e.name=e.File_Name,e}))})),(0,l.FileGetEntity)({id:t.Id}).then((function(e){e.IsSucceed&&(n.form=(0,s.default)((0,s.default)({},e.Data),{},{model:{UserIds:[],Title:"",Content:""}}),n.isCZD&&(n.relatedItem=e.Data.Component_Codes),(n.isComp||n.isPart||n.isUnitPart)&&(n.relatedItem=e.Data.Codes))}))),this.$nextTick((function(e){n.getCompList()}))},handleClose:function(){try{this.relatedItem=[],this.attachments=[],this.$refs["form"].resetFields(),this.isNotify=!1,this.btnLoading=!1,this.loading=!1,this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,r.default)((0,o.default)().m((function t(a){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(!a){t.n=6;break}if(e.loading=!0,e.btnLoading=!0,"edit"!==e.type){t.n=1;break}return e.updateInfo(),t.a(2);case 1:if(!e.CadFiles||!0!==e.form.Is_Big_File){t.n=4;break}return t.n=2,(0,m.getConfigure)({code:"glendale_cad_token"});case 2:return d.Glendale.cadToken=t.v.Data,t.n=3,(0,m.getConfigure)({code:"glendale_cad_url"});case 3:d.Glendale.cadUrl=t.v.Data,c.default.all(e.fileList.map((function(e,t){var a=e.raw.name.split("."),n=a[a.length-1];if("glzip"===n||"dwg"===n){var o=new FormData;o.append("file",e.raw);var r=(0,s.default)((0,s.default)({},d.Glendale.optionsCad),{},{UniqueCode:"".concat((0,h.getTenantId)(),"__").concat((0,y.v4)()),Name:encodeURIComponent(e.name)});return c.default.post("".concat(d.Glendale.cadUrl).concat(d.Glendale.uploadUrl,"?input=").concat(JSON.stringify(r)),o,{headers:{Token:d.Glendale.cadToken,"Content-Type":"multipart/form-data"}}).then((function(t){e.BimId=t.data.datas.lightweightName}))}}))).then((function(t){var a=e.attachments.map((function(t){var a=e.fileList.find((function(e){return e.name===t.File_Name}));return a.BimId}));e.form.BimId=a.join(","),e.updateInfo()})),t.n=5;break;case 4:e.updateInfo();case 5:t.n=7;break;case 6:return e.$message({message:"请将表单填写完整",type:"warning"}),t.a(2,!1);case 7:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},updateInfo:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n,r,i,s,c,u,d;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(a=Array.from(new Set(e.attachments.map((function(e){return e.File_Name})))).map((function(t){return e.attachments.find((function(e){return e.File_Name===t}))})),n=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),i={},i=e.isNotify?e.form.model:{UserIds:[],Title:"",Content:""},(e.isComp||e.isPart||e.isUnitPart)&&(s=e.relatedItem),e.isCZD&&(e.form.Component_Codes=e.relatedItem),c=e.isComp?0:e.isPart?1:e.isUnitPart?3:null,"add"!==e.type){t.n=2;break}return u=e.isDeep?l.AddDeepFile:l.FileAdd,d={file:e.form,model:i,attachmentList:a,codes:s,type:c},e.isDeep&&(d.file.Type_Name=e.professionalCode),e.isDeep&&"2"===n&&(d.projectId=e.projectId,d.factoryId=localStorage.getItem("CurReferenceId")),e.isDeep||"2"!==n||(d.file.Project_Id=e.projectId),t.n=1,u(d);case 1:r=t.v,t.n=4;break;case 2:return t.n=3,(0,l.FileEdit)({file:e.form,model:i,attachmentList:a,codes:s,type:c});case 3:r=t.v;case 4:r.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.$emit("getData",e.form.Doc_Type),e.loading=!1,e.btnLoading=!1,e.handleClose()):(e.loading=!1,e.btnLoading=!1,e.$message.error(r.Message));case 5:return t.a(2)}}),t)})))()},getAllLoadingFiles:function(){var e=this;(0,l.GetLoadingFiles)().then((function(t){e.updateLoadingFiles(t.Data)}))},updateLoadingFiles:function(e){var t=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,key:BIM_API_CONFIG.KEY}),a=t.getModelProjectManager(),n={SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,LoadFiles:e};a.updateSceneSettings(BIM_API_CONFIG.USER_NAME,this.bimvizId,n,(function(e){}))}}}},1567:function(e,t,a){"use strict";a.r(t);var n=a("11723"),o=a("6413");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("045f");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"2532fe4d",null);t["default"]=s.exports},1591:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),e._l(e.filterLabelList,(function(t){return a("el-form-item",{key:t.SteelUnique,attrs:{label:t.label}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{max:t.unPackCount,clearable:""},model:{value:t.number,callback:function(a){e.$set(t,"number",a)},expression:"item.number"}}),a("div",[e._v("未打包数量："+e._s(t.unPackCount))])],1)})),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],2)},o=[]},1599:function(e,t,a){"use strict";a.r(t);var n=a("2a70"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,s=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var o=this.columns[n];if(o.Code===t){a.Type=o.Type,a.Filter_Type=o.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"176d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.list,(function(t,n){return a("el-row",{key:t.id,staticClass:"item-x"},[a("div",{staticClass:"item"},[a("label",[e._v(" 属性名称 "),a("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.key,callback:function(a){e.$set(t,"key",a)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),a("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[a("label",[e._v("请输入值 "),e.checkType(t.key,"number")?a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?a("el-input",{model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"array")&&"Is_Component"===t.key?a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),a("el-tree-select",{directives:[{name:"show",rawName:"v-show",value:e.checkType(t.key,"array")&&"SteelType"===t.key,expression:"checkType(info.key,'array') && info.key==='SteelType'"}],ref:"treeSelect",refInFor:!0,staticStyle:{width:"100%",display:"inline-block"},attrs:{"tree-params":e.treeParams},on:{"node-click":e.steelTypeChange},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}})],1)]),a("span",{staticClass:"item-span"},0===n?[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),a("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(n)}}})])])})),a("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},o=[]},18304:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("2909")),r=n(a("5530")),i=n(a("c14f")),s=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("3ca3"),a("5319"),a("1276"),a("c7cd"),a("159b"),a("ddb0");var l=a("4744"),c=a("6186"),u=a("fd31"),d=a("586a"),f=a("3166"),m=a("c24f"),p=a("2e8a"),h=n(a("1463")),g=n(a("34e9")),y=n(a("07a8")),b=n(a("22be")),v=n(a("d266")),_=n(a("58e2")),P=n(a("1f7f")),S=n(a("9b25")),C=n(a("c7d43")),I=n(a("4b6d")),D=n(a("2934")),k=n(a("b8ae")),T=n(a("4be1")),L=n(a("4f93")),w=n(a("7072")),x=n(a("d407")),O=n(a("0d40")),N=n(a("31b1")),A=n(a("a888")),j=n(a("333d")),G=a("8975"),R=n(a("6347")),$=n(a("3a4f")),F=n(a("bc3a")),M=n(a("f151")),U=a("ed08"),E=a("c685"),B=a("e144"),W=a("f4f2"),z=(a("f382"),n(a("4292"))),q=n(a("bae6")),V=n(a("4791")),H=n(a("6612")),Y="$_$";t.default={directives:{elDragDialog:A.default,sysUseType:M.default},components:{ExpandableSection:q.default,LocationImport:z.default,TreeDetail:h.default,TopHeader:g.default,comImport:y.default,comImportByFactory:v.default,BatchEdit:P.default,HistoryExport:_.default,GeneratePack:D.default,Edit:C.default,ComponentPack:S.default,OneClickGeneratePack:I.default,Pagination:j.default,bimdialog:$.default,ComponentsHistory:b.default,ProductionConfirm:k.default,PartList:T.default,SteelMeans:L.default,ModelComponentCode:w.default,ProductionDetails:x.default,ModelListImport:O.default,comDrawdialog:N.default,TracePlot:V.default},mixins:[R.default],data:function(){return{showExpand:!0,isAutoSplit:void 0,tablePageSize:E.tablePageSize,syncVisible:!1,syncForm:{Is_Sync_To_Part:null},syncRules:{Is_Sync_To_Part:{required:!0,message:"请选择是否同步到相关零件",trigger:"change"}},treeSelectParams:{placeholder:"请选择",clearable:!0},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},treeData:[],treeLoading:!0,expandedKey:"",projectName:"",statusType:"",searchHeight:0,searchStatus:!0,tbData:[],total:0,tbLoading:!1,pgLoading:!1,queryInfo:{Page:1,PageSize:10,ParameterJson:[]},customPageSize:[10,20,50,100],installUnitIdNameList:[],nameMode:1,names:"",customParams:{Code_Like:"",Spec:"",Texture:"",Is_Direct:"",Create_UserName:"",InstallUnit_Id:"",SteelNames:"",TypeId:"",Sys_Project_Id:"",Project_Id:"",Area_Id:"",Project_Name:"",Area_Name:""},Unit:"",Proportion:0,customDialogParams:{},dialogVisible:!1,currentComponent:"",selectList:[],factoryOption:[],projectList:[],typeOption:[],treeParamsSteel:[],columns:[],columnsOption:[],title:"",width:"60%",tipLabel:"",monomerList:[],mode:"",isMonomer:!0,historyVisible:!1,sysUseType:void 0,productionConfirm:"",SteelFormEditData:{},deepenTotalLength:0,SteelAmountTotal:0,SchedulingNumTotal:0,SteelAllWeightTotal:0,SchedulingAllWeightTotal:0,FinishCountTotal:0,FinishWeightTotal:0,IsComponentTotal:0,TotalGrossWeight:0,IsComponentTotalSteelAllWeight:0,leftCol:4,rightCol:40,leftWidth:320,drawer:!1,iframeKey:"",iframeUrl:"",baseCadUrl:"",extensionName:"",fileBim:"",drawersull:!1,fullscreenid:"",fullbimid:"",templateUrl:"",command:"cover",currentLastLevel:!1,cadRowCode:"",cadRowProjectId:"",IsUploadCad:!1,comDrawData:{},currentNode:{},trackDrawer:!1,trackDrawerTitle:"",trackDrawerData:{}}},computed:{typeEntity:function(){var e=this;return this.typeOption.find((function(t){return t.Id===e.customParams.TypeId}))},showTotalLength:function(){var e=[this.customParams.Fuzzy_Search_Col,this.customParams.Fuzzy_Search_Col2,this.customParams.Fuzzy_Search_Col3,this.customParams.Fuzzy_Search_Col4];return e.includes("SteelLength")&&e.includes("SteelSpec")},filterText:function(){return this.projectName+Y+this.statusType},TotalGrossWeightT:function(){return(0,H.default)(this.TotalGrossWeight||0).divide(1e3).format("0.[000]")}},watch:{"customParams.TypeId":function(e,t){t&&"0"!==t&&this.fetchData()},names:function(e,t){this.changeMode()},nameMode:function(e,t){this.changeMode()}},created:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return(0,m.getConfigure)({code:"glendale_url"}).then((function(t){e.baseCadUrl=t.Data})),t.n=1,e.getPreferenceSettingValue();case 1:return t.n=2,e.getTypeList();case 2:e.fetchTreeData(),e.getFileType();case 3:return t.a(2)}}),t)})))()},mounted:function(){var e=this;this.searchHeight=this.$refs.searchDom.offsetHeight+327,window.addEventListener("message",this.frameListener),this.$once("hook:beforeDestroy",(function(){window.removeEventListener("message",e.frameListener)}))},activated:function(){var e=this;window.addEventListener("message",this.frameListener),this.$once("hook:deactivated",(function(){window.removeEventListener("message",e.frameListener)}))},methods:{changeMode:function(e){1===this.nameMode?(this.customParams.Code_Like=this.names,this.customParams.SteelNames=""):(this.customParams.Code_Like="",this.customParams.SteelNames=this.names.replace(/\s+/g,"\n"))},getComponentInfo:function(e){var t=e.Drawing?e.Drawing.split(","):[],a=e.File_Url?e.File_Url.split(","):[];0!==t.length?(t.length>0&&(this.drawingActive=t[0]),t.length>0&&a.length>0&&(this.drawingDataList=t.map((function(e,t){return{name:e,label:e,url:a[t]}}))),this.getComponentInfoDrawing(e)):this.$message({message:"当前构件无图纸",type:"warning"})},getComponentInfoDrawing:function(e){var t=this,a=e.Id;(0,d.GetSteelCadAndBimId)({importDetailId:a}).then((function(a){if(a.IsSucceed){var n,o=null===(n=a.Data)||void 0===n?void 0:n[0];t.extensionName=o.ExtensionName,t.fileBim=o.fileBim,t.IsUploadCad=o.IsUpload,t.cadRowCode=e.SteelName,t.cadRowProjectId=e.Sys_Project_Id,t.fileView(o.ExtensionName,o.fileBim)}}))},fileView:function(e,t){this.iframeKey=(0,B.v4)(),this.iframeUrl="".concat(this.baseCadUrl,"?router=1&iframeId=1&baseUrl=").concat((0,W.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id")),this.drawer=!0},handleCloseDrawer:function(){this.drawer=!1},renderIframe:function(){var e=this.extensionName,t=this.fileBim;this.iframeUrl="".concat(this.baseCadUrl,"?router=1&iframeId=1&baseUrl=").concat((0,W.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id")),this.fullscreenid=e,this.fullbimid=t},fullscreen:function(e){this.templateUrl="",0===e?this.templateUrl="".concat(this.baseCadUrl,"?router=1&iframeId=2&baseUrl=").concat((0,W.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id")):1===e&&(this.templateUrl="".concat(this.baseCadUrl,"?router=1&iframeId=3&baseUrl=").concat((0,W.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id"))),this.drawersull=!0},frameListener:function(e){var t=e.data;"loaded"===t.type&&("1"===t.data.iframeId?document.getElementById("frame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{featureId:this.fullscreenid,cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showModel:!!this.fullscreenid,showCad:this.IsUploadCad}},"*"):"2"===t.data.iframeId?document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{featureId:this.fullscreenid,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showModel:!!this.fullscreenid}},"*"):"3"===t.data.iframeId&&document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showCad:this.IsUploadCad}},"*"))},fetchTreeData:function(){var e=this;(0,f.GetProjectAreaTreeList)({MenuId:this.$route.meta.Id,projectName:this.projectName}).then((function(t){if(0!==t.Data.length){var a=t.Data;a.map((function(e){return 0===e.Children.length?e.Data.Is_Imported=!1:(e.Data.Is_Imported=e.Children.some((function(e){return!0===e.Data.Is_Imported})),e.Is_Directory=!0,e.Children.map((function(e){e.Children.length>0&&(e.Is_Directory=!0)}))),e})),e.treeData=a,0===Object.keys(e.currentNode).length?e.setKey():e.handleNodeClick(e.currentNode),e.treeLoading=!1}else e.treeLoading=!1}))},setKey:function(){var e=this,t=function(a){for(var n=0;n<a.length;n++){var o=a[n],r=o.Data,i=o.Children;return!r.ParentId||null!==i&&void 0!==i&&i.length?i&&i.length>0?t(i):void e.handleNodeClick(o):(e.currentNode=r,void e.handleNodeClick(o))}};return t(this.treeData)},handleNodeClick:function(e){var t,a,n=this;(this.handleSearch("reset",!1),this.currentNode=e,this.expandedKey=e.Id,this.$nextTick((function(e){var t=n.$refs["tree"].$refs.tree.getNode(n.expandedKey);t&&(n.isAutoSplit=null===t||void 0===t?void 0:t.data.Data.Is_Auto_Split)})),this.InstallUnit_Id="",null===e.ParentNodes&&"全部"!==e.Code?(this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id,this.customParams.Project_Id=e.Data.Id,this.customParams.Area_Name="",this.customParams.Area_Id=""):(this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id,this.customParams.Project_Id=e.Data.Project_Id,this.customParams.Area_Id=e.Data.Id),this.isAutoSplit=null===(t=e.Data)||void 0===t?void 0:t.Is_Auto_Split,this.currentLastLevel=!(!e.Data.Level||0!==e.Children.length),this.currentLastLevel)&&(this.customParams.Project_Name=null===(a=e.Data)||void 0===a?void 0:a.Project_Name,this.customParams.Area_Name=e.Label);var o=-1===e.Id?"":e.Id;this.pgLoading=!0,this.getInstallUnitIdNameList(o,e),this.fetchData(),this.getComponentSummaryInfo()},getInstallUnitIdNameList:function(e,t){var a=this;""===e||t.Children.length>0?this.installUnitIdNameList=[]:(0,f.GetInstallUnitIdNameList)({Area_Id:e}).then((function(e){a.installUnitIdNameList=e.Data}))},handleSearch:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.searchStatus=!1,e&&(this.$refs.customParams.resetFields(),this.names="",this.searchStatus=!0),t&&this.fetchData()},getPreferenceSettingValue:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:(0,l.GetPreferenceSettingValue)({Code:"Production_Confirm"}).then((function(t){e.productionConfirm=t.Data}));case 1:return t.a(2)}}),t)})))()},getComponentSummaryInfo:function(){var e=this;(0,d.GetComponentSummaryInfo)((0,r.default)({},this.customParams)).then((function(t){t.IsSucceed?(e.SteelAmountTotal=Math.round(1e3*t.Data.DeepenNum)/1e3,e.SchedulingNumTotal=Math.round(1e3*t.Data.SchedulingNum)/1e3,e.SteelAllWeightTotal=Math.round(1e3*t.Data.DeepenWeight)/1e3,e.SchedulingAllWeightTotal=Math.round(1e3*t.Data.SchedulingWeight)/1e3,e.FinishCountTotal=Math.round(1e3*t.Data.Finish_Count)/1e3,e.FinishWeightTotal=Math.round(1e3*t.Data.Finish_Weight)/1e3,e.IsComponentTotal=t.Data.Direct_Count||0,e.TotalGrossWeight=t.Data.TotalGrossWeight||0,e.IsComponentTotalSteelAllWeight=Math.round(1e3*(t.Data.Direct_Weight||0))/1e3):e.$message({message:t.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,c.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.customParams.TypeId})).Code}).then((function(e){var n=e.IsSucceed,o=e.Data,r=e.Message;if(n){if(!o)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,o.Grid);var i=o.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return"SteelName"===e.Code&&(e.fixed="left"),e})),t.queryInfo.PageSize=+o.Grid.Row_Number||20;var s=JSON.parse(JSON.stringify(t.columns));t.columnsOption=s.filter((function(e){return"操作时间"!==e.Display_Name&&"安装位置"!==e.Display_Name&&"模型ID"!==e.Display_Name&&"深化资料"!==e.Display_Name&&"备注"!==e.Display_Name&&"零件"!==e.Display_Name&&"排产数量"!==e.Display_Name&&-1===e.Code.indexOf("Attr")&&"构件类型"!==e.Display_Name&&"批次"!==e.Display_Name})),a(t.columns)}else t.$message({message:r,type:"error"})}))}))},getComponentImportDetailPageList:function(){var e=this;return new Promise((function(t){var a=d.GetComponentImportDetailPageList;a((0,r.default)((0,r.default)({},e.queryInfo),e.customParams)).then((function(a){a.IsSucceed?(e.tbData=a.Data.Data.map((function(e){return e.Create_Date=(0,G.timeFormat)(e.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.deepenTotalLength=a.Data.DeepenTotalLength||0,e.queryInfo.PageSize=a.Data.PageSize,e.total=a.Data.TotalCount,e.selectList=[]):e.$message({message:a.Message,type:"error"}),t()}))}))},fetchData:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_component_page_list");case 1:e.tbLoading=!0,Promise.all([e.getComponentImportDetailPageList()]).then((function(t){e.tbLoading=!1,e.pgLoading=!1}));case 2:return t.a(2)}}),t)})))()},changePage:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),Promise.all([e.getComponentImportDetailPageList()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.SteelAmountTotal=0,this.SchedulingNumTotal=0,this.SteelAllWeightTotal=0,this.SchedulingAllWeightTotal=0,this.FinishCountTotal=0,this.FinishWeightTotal=0,this.IsComponentTotal=0,this.TotalGrossWeight=0,this.IsComponentTotalSteelAllWeight=0;var a=0,n=0,o=0,r=0;this.selectList.length>0?(this.selectList.forEach((function(e){var i=null==e.SchedulingNum?0:e.SchedulingNum;t.SteelAmountTotal+=e.SteelAmount,t.SchedulingNumTotal+=e.SchedulingNum,t.FinishCountTotal+=e.Finish_Count,t.TotalGrossWeight+=e.TotalGrossWeight,a+=e.SteelAllWeight,n+=e.SteelWeight*i,o+=e.Finish_Weight,t.IsComponentTotal+="False"===e.Is_Component?e.SteelAmount:0,r+="False"===e.Is_Component?e.SteelAllWeight:0})),this.SteelAllWeightTotal=Math.round(a/this.Proportion*1e3)/1e3,this.SchedulingAllWeightTotal=Math.round(n/this.Proportion*1e3)/1e3,this.FinishWeightTotal=Math.round(o/this.Proportion*1e3)/1e3,this.IsComponentTotalSteelAllWeight=Math.round(r/this.Proportion*1e3)/1e3):this.getComponentSummaryInfo()},getTbData:function(e){var t=e.CountInfo;this.tipLabel=t},getTypeList:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var a,n,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.customParams.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id),e.getCompTypeTree(e.typeOption[0].Code)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getCompTypeTree:function(e){var t=this;this.loading=!0,(0,p.GetCompTypeTree)({professional:e}).then((function(e){e.IsSucceed?(t.treeParamsSteel=e.Data,t.ObjectTypeList.data=e.Data,t.$nextTick((function(a){t.$refs.treeSelectObjectType.treeDataUpdateFun(e.Data)}))):(t.$message({message:e.Message,type:"error"}),t.treeData=[])})).finally((function(e){t.loading=!1}))},handleSearchDelete:function(){var e=this;if(""===this.customParams.Project_Id)return this.$message({type:"warning",message:"请选择项目"}),!1;this.$confirm("此操作将删除搜索的数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.DeleteAllComponentWithQuery)((0,r.default)({},e.customParams)).then((function(t){t.IsSucceed?(e.fetchData(),e.$message({message:"删除成功",type:"success"})):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleDelete:function(){var e=this;this.$confirm("此操作将删除选择数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.DeleteComponents)({ids:e.selectList.map((function(e){return e.Id})).toString()}).then((function(t){t.IsSucceed?(e.fetchData(),e.fetchTreeData(),e.$message({message:"删除成功",type:"success"})):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(e){var t=this;this.width="45%",this.generateComponent("编辑构件","Edit"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},handleBatchEdit:function(){var e=this,t=this.selectList.filter((function(e){return null!=e.SchedulingNum&&e.SchedulingNum>0}));t.length>0?this.$message({type:"error",message:"选中行包含已排产的构件,编辑信息需要进行变更操作"}):(this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.selectList,e.columnsOption)})))},handleView:function(e){var t=this;this.width="45%",this.generateComponent("查看构件","Edit"),this.$nextTick((function(a){e.isReadOnly=!0,t.$refs["content"].init(e)}))},handleViewPart:function(e){var t=this;this.width="60%",this.generateComponent("零件清单","PartList"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleViewSH:function(e,t){var a=this;this.width="40%",this.generateComponent("查看深化资料","SteelMeans"),this.$nextTick((function(n){a.$refs["content"].init(e,t)}))},handleSteelMeans:function(e){this.handleViewSH(e,1)},handleViewModel:function(e){var t=this;this.width="40%",this.generateComponent("模型构件唯一码列表","ModelComponentCode"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleViewScheduling:function(e){var t=this;this.width="30%",this.generateComponent("生产详情","ProductionDetails"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleHistory:function(e){this.generateComponent("构件变更历史","ComponentsHistory"),this.customDialogParams={steelUnique:e.SteelUnique}},locationExport:function(){this.handleSteelExport(3)},handleSteelExport:function(e){var t=this;return(0,s.default)((0,i.default)().m((function a(){var n,o;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:if(""!==t.customParams.Sys_Project_Id||0!==t.selectList.length){a.n=1;break}return t.$message({type:"warning",message:"请选择项目"}),a.a(2,!1);case 1:return n=(0,r.default)((0,r.default)({Type:e,Import_Detail_Ids:t.selectList.map((function(e){return e.Id}))},t.customParams),{},{Sys_Project_Id:t.customParams.Sys_Project_Id}),a.n=2,(0,d.ExportComponentInfo)(n);case 2:if(o=a.v,o.IsSucceed){a.n=3;break}return t.$message({message:o.Message,type:"error"}),a.a(2);case 3:localStorage.getItem("ProjectName")+"_构件导出明细","application/octet-stream"===o.type?".rar":".xls",window.open((0,U.combineURL)(t.$baseUrl,o.Data),"_blank");case 4:return a.a(2)}}),a)})))()},getFile:function(e){return new Promise((function(t,a){(0,F.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))},modelListImport:function(){this.width="30%",this.generateComponent("模型清单导入","ModelListImport")},LocationImport:function(){this.width="30%",this.generateComponent("位置信息导入","LocationImport")},handleCommand:function(e,t){this.command=e,1===t?this.deepListImport(1):0===t&&this.deepListImport(0)},deepListImport:function(e){var t={Catalog_Code:"PLMDeepenFiles",Code:this.typeEntity.Code,name:this.typeEntity.Name};"true"===this.productionConfirm&&0===e?(this.width="30%",this.generateComponent("导入构件","ProductionConfirm")):this.$refs.dialog.handleOpen("add",t,null,!0,"",e,"",this.command,this.customParams)},deepListImportAgin:function(e){var t={Catalog_Code:"PLMDeepenFiles",Code:this.typeEntity.Code,name:this.typeEntity.Name};this.$refs.dialog.handleOpen("add",t,null,!0,"",0,e,this.command,this.customParams)},handleSchedulingInfoExport:function(){var e=this;(0,d.ExportComponentSchedulingInfo)({ids:this.selectList.map((function(e){return e.Id})).toString()}).then((function(t){t.IsSucceed?(window.open((0,U.combineURL)(e.$baseUrl,t.Data),"_blank"),t.Message&&e.$alert(t.Message,"导出通知",{confirmButtonText:"我知道了"})):e.$message({message:t.Message,type:"error"})}))},handleHistoryExport:function(){if(""===this.customParams.Project_Id)return this.$message({type:"warning",message:"请选择项目"}),!1;this.width="60%",this.generateComponent("历史清单导出","HistoryExport")},handleComponentPack:function(e){var t=this,a=e.data,n=e.type,o=void 0===n?2:n;this.width="80%",this.generateComponent("查看构件包","ComponentPack"),this.$nextTick((function(e){a&&t.$refs["content"].getSubmitObj(a),t.$refs["content"].handlePackage(o)}))},handleAllPack:function(){this.width="30%",this.generateComponent("查询结果一键打包","OneClickGeneratePack"),this.customDialogParams=this.customParams},handleGenerate:function(){var e=this;this.width="30%",this.generateComponent("生成构件包","GeneratePack"),this.$nextTick((function(t){e.$refs["content"].init(e.selectList)}))},handleClose:function(e){this.dialogVisible=!1,!0!==e&&!1!==e||this.deepListImportAgin(e)},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},fetchTreeDataLocal:function(){},customFilterFun:function(e,t,a){var n=e.split(Y),r=n[0],i=n[1];if(!e)return!0;var s=a.parent,l=[a.label],c=[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"],u=1;while(u<a.level)l=[].concat((0,o.default)(l),[s.label]),c=[].concat((0,o.default)(c),[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"]),s=s.parent,u++;l=l.filter((function(e){return!!e})),c=c.filter((function(e){return!!e}));var d=!0,f=!0;return this.statusType&&(f=c.some((function(e){return-1!==e.indexOf(i)}))),this.projectName&&(d=l.some((function(e){return-1!==e.indexOf(r)}))),d&&f},getFileType:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var a,n,o,r;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return n={catalogCode:"PLMDeepenFiles"},t.n=1,(0,c.GetFileType)(n);case 1:o=t.v,r=o.Data.find((function(e){return"构件详图"===e.Label})),e.comDrawData={isSHQD:!1,Id:r.Id,name:r.Label,Catalog_Code:r.Code,Code:null===(a=r.Data)||void 0===a?void 0:a.English_Name};case 2:return t.a(2)}}),t)})))()},handelImport:function(){this.$refs.comDrawdialogRef.handleOpen("add",this.comDrawData,"",!1,this.customParams.Sys_Project_Id,!1)},handleTrack:function(e){this.trackDrawer=!0,this.trackDrawerTitle=e.SteelName,this.trackDrawerData=e}}}},"1a25":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("e9c4"),a("d3b7"),a("25f0"),a("159b");var o=n(a("34e9")),r=n(a("1567")),i=n(a("987c")),s=n(a("15ac")),l=n(a("9b39")),c=a("a667");t.default={components:{TopHeader:o.default,zLeft:r.default,Dialog:l.default,zRight:i.default},mixins:[s.default],props:{selectList:{type:Array,default:function(){return[]}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{search:"",search2:"",select:"",dialogVisible:!1,leftSelectList:[],rightSelectList:[],columnOption:[],currentPackId:"",submitObj:{},fromType:2}},methods:{handleClose:function(){this.dialogVisible=!1},clearRightData:function(){this.currentPackId="",this.fetchRight()},fetchLeft:function(){this.$refs["left"].fetchData()},fetchRight:function(){this.$refs["right"].fetchRightData(this.currentPackId)},leftClick:function(e){this.currentPackId=e,this.fetchRight()},handleDeleteLeft:function(){var e=this;this.$confirm("是否删除构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.DeletePackage)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString()}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},getColumn:function(e){this.columnOption=e},handleDeleteRight:function(){var e=this;this.$confirm("是否删除该构件?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.DeleteSteel)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString(),itemId:JSON.stringify(e.rightSelectList.map((function(e){return e.Id})))}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft(),e.$refs["right"].fetchRightData()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(){var e=this;this.dialogVisible=!0,this.$nextTick((function(t){e.$refs["dialog"].fetchData(e.leftSelectList)}))},getSubmitObj:function(e){this.submitObj=e},handleAdd:function(){var e=this;if(this.selectList.length||2!==this.fromType)if(this.leftSelectList.length){var t=this.selectList.every((function(e){return 1===e.Sup_Count&&1===e.SteelAmount}));t||2!==this.fromType?this.$confirm("确定加入此构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitObj.Package.Id=e.leftSelectList.map((function(e){return e.Id})).toString(),1===e.fromType?(0,c.BatchAdd)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"})})):(0,c.AddSteel)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))})).catch((function(){e.$message({type:"info",message:"已取消"})})):this.$message({message:"请点击生成构件包",type:"warning"})}else this.$message({message:"请先选择包",type:"warning"});else this.$message({message:"请先选择构件",type:"warning"})},getLeftList:function(e){this.leftSelectList=e},getRightList:function(e){this.rightSelectList=e},handlePackage:function(e){if(this.fromType=e,2===e){var t={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};this.selectList.forEach((function(e){t.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),this.submitObj=t}}}}},"1bde":function(e,t,a){},"1c0a":function(e,t,a){"use strict";a("c93e")},"1d8f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7"),a("ac1f"),a("3ca3"),a("466d"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var o=n(a("c14f")),r=n(a("1da1")),i=n(a("5530")),s=n(a("bbc2")),l=a("586a"),c=a("ed08"),u={Id:"",Is_Auto_Split:void 0,Doc_Catelog:"",Doc_Type:"",Project_Name:"",Project_Id:"",Sys_Project_Id:"",Area_Name:"",Area_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",IsChanged:!1,Is_Load:!1,Doc_File:"",ishistory:!0,Is_Skip_Production:!1,ProfessionalCode:"",Type:0,Template_Type:2};t.default={components:{OSSUpload:s.default},props:{typeEntity:{type:Object,default:function(){}},isAutoSplit:{type:[Boolean,void 0],default:void 0}},data:function(){return{isDynamicTemplate:!1,btnLoading:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,form:(0,i.default)({},u),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}],Is_Auto_Split:[{required:!0,message:"请选择",trigger:"change"}]},fileType:"",curFile:"",bimvizId:"",isDeep:!1,projectId:"",isSHQD:"",command:"cover"}},watch:{isAutoSplit:function(e,t){this.$set(this.form,"Is_Auto_Split",e)}},mounted:function(){this.$set(this.form,"Is_Auto_Split",this.isAutoSplit)},created:function(){this.fileType=this.$route.name},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},getTemplate:function(){var e=this,t={ProfessionalCode:this.form.ProfessionalCode,Type:this.form.Type};t.Template_Type=this.form.Template_Type,(0,l.ComponentImportTemplate)((0,i.default)({},t)).then((function(t){t.IsSucceed?window.open((0,c.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"})}))},getBlob:function(e){return new Promise((function(t){var a=new XMLHttpRequest;a.open("GET",e,!0),a.responseType="blob",a.onload=function(){200===a.status&&t(a.response)},a.send()}))},saveAs:function(e,t){var a=document.createElement("a");a.href=window.URL.createObjectURL(e),a.download=t,a.click()},downFile:function(e){var t=this;this.getBlob(e).then((function(e){t.saveAs(e,"信用权证使用导入模板件名.xlsx")}))},handleChange:function(e,t){this.fileList=t.slice(-1),t.length>1&&(this.attachments.splice(-1),this.form.Doc_File="",this.form.Doc_Content="",this.form.Doc_Title="")},beforeUpload:function(e){this.curFile=e,this.loading=!0,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this,n=0;this.fileList.filter((function(t,a){t.name===e.name&&(n=a)})),this.fileList.splice(n,1),this.attachments.splice(n,1),this.form.Doc_File=this.form.Doc_File.replace(e.name,""),this.form.Doc_Content=this.form.Doc_File.replace(e.name,""),this.form.Doc_Title=this.form.Doc_File.replace(e.name,""),this.loading=!t.every((function(e){return"success"===e.status})),setTimeout((function(){a.btnLoading=!t.every((function(e){return"success"===e.status}))}),1e3)},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){var n=this;this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]});var o=this.form.Doc_Title+(this.form.Doc_Title?",":"")+e.Data.split("*")[3];this.form.Doc_Title=o.substring(0,o.lastIndexOf(".")),this.form.Doc_Content=this.form.Doc_Title,this.form.Doc_File=this.form.Doc_File+(this.form.Doc_File?",":"")+e.Data.split("*")[3],this.loading=!a.every((function(e){return"success"===e.status})),setTimeout((function(){n.btnLoading=!a.every((function(e){return"success"===e.status}))}),1e3)},handleOpen:function(e,t,a){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0,i=arguments.length>6?arguments[6]:void 0,s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"cover",l=arguments.length>8?arguments[8]:void 0;this.projectId=o,this.isDeep=n,this.form=Object.assign(this.form,u),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.isSHQD=t.isSHQD,this.form.ProfessionalCode=t.Code,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.form.Type=r,this.form.Is_Skip_Production=""!==i&&i,this.command=s,this.form.Project_Name=l.Project_Name,this.form.Sys_Project_Id=l.Sys_Project_Id,this.form.Area_Name=l.Area_Name,this.form.Area_Id=l.Area_Id,this.isDynamicTemplate=!1,this.Template_Type=2,"add"===this.type&&(this.fileList=[],this.form.Id=""),"cover"===this.command?this.title="覆盖文件":"add"===this.command&&(this.title="新增文件"),this.$set(this.form,"Is_Auto_Split",this.isAutoSplit)},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.btnLoading=!1,this.loading=!1,this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,r.default)((0,o.default)().m((function t(a){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(!a){t.n=1;break}e.loading=!0,e.btnLoading=!0,e.updateInfo(),t.n=2;break;case 1:return e.$message({message:"请将表单填写完整",type:"warning"}),t.a(2,!1);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},updateInfo:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(a=(0,i.default)({},e.form),!a.Is_Auto_Split||1!==a.Type){t.n=1;break}return e.getSplitInfo().then((function(){e.updatePartAggregateId()})),t.a(2);case 1:"cover"===e.command?e.submitCoverAdd(a):"add"===e.command&&e.submitAdd(a);case 2:return t.a(2)}}),t)})))()},submitCoverAdd:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,(0,l.ImportDeepFile)((0,i.default)((0,i.default)({},e),{},{AttachmentList:t.attachments}));case 1:if(n=a.v,!n.IsSucceed){a.n=3;break}return t.$message({message:"保存成功",type:"success"}),a.n=2,t.updatePartAggregateId();case 2:t.$emit("getData",t.form.Doc_Type),t.$emit("getProjectAreaData"),t.handleClose(),a.n=4;break;case 3:n.Data&&window.open((0,c.combineURL)(t.$baseUrl,n.Data),"_blank"),t.$message.error(n.Message);case 4:a.n=6;break;case 5:a.p=5,a.v,t.$message.error("保存失败");case 6:return a.p=6,t.loading=!1,t.btnLoading=!1,a.f(6);case 7:return a.a(2)}}),a,null,[[0,5,6,7]])})))()},submitAdd:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,(0,l.AppendImportDeepFile)((0,i.default)((0,i.default)({},e),{},{AttachmentList:t.attachments}));case 1:if(n=a.v,!n.IsSucceed){a.n=3;break}return t.$message({message:"保存成功",type:"success"}),a.n=2,t.updatePartAggregateId();case 2:t.$emit("getData",t.form.Doc_Type),t.$emit("getProjectAreaData"),t.handleClose(),a.n=4;break;case 3:n.Data&&window.open((0,c.combineURL)(t.$baseUrl,n.Data),"_blank"),t.$message.error(n.Message);case 4:a.n=6;break;case 5:a.p=5,a.v,t.$message.error("保存失败");case 6:return a.p=6,t.loading=!1,t.btnLoading=!1,a.f(6);case 7:return a.a(2)}}),a,null,[[0,5,6,7]])})))()},getSplitInfo:function(){var e=this,t=this.form,a=t.ProfessionalCode,n=t.Type,o=t.Is_Skip_Production,r=t.Sys_Project_Id,i=t.Area_Id,s={ProfessionalCode:a,Type:n,Is_Skip_Production:o,Sys_Project_Id:r,Area_Id:i,AttachmentList:this.attachments,Is_Auto_Split:!0};(0,l.GenerateDeepenFileFromDirect)(s).then((function(t){t.IsSucceed?e.open(t.Data):e.$message({message:t.Message,type:"error"})}))},updatePartAggregateId:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.UpdatePartAggregateId)({AreaId:e.form.Area_Id}).then((function(t){t.IsSucceed||e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},open:function(e){var t=this,a=this.$createElement,n="",s=e.match(/\/([^/]+\.xls)$/);s&&(n=s[1]);var l=(0,i.default)({},this.form);this.$msgbox({title:"提示",message:a("div",null,[a("div",null,"清单已拆分完成, 是否确定导入?"),a("a",{attrs:{href:(0,c.combineURL)(this.$baseUrl,e),target:"_blank",style:"color: #298DFF"}},n)]),showCancelButton:!0,confirmButtonText:"确定",cancelButtonText:"取消",beforeClose:function(){var e=(0,r.default)((0,o.default)().m((function e(a,n,r){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:if("confirm"!==a){e.n=4;break}if(n.confirmButtonLoading=!0,n.confirmButtonText="提交...","cover"!==t.command){e.n=2;break}return e.n=1,t.submitCoverAdd(l);case 1:e.n=3;break;case 2:if("add"!==t.command){e.n=3;break}return e.n=3,t.submitAdd(l);case 3:r(),setTimeout((function(){n.confirmButtonLoading=!1}),300),e.n=5;break;case 4:t.loading=!1,t.btnLoading=!1,r();case 5:return e.a(2)}}),e)})));function a(t,a,n){return e.apply(this,arguments)}return a}()}).then((function(e){}))},radioChange:function(e){1===e?(this.isDynamicTemplate=!0,this.form.Is_Auto_Split=void 0):this.isDynamicTemplate=!1}}}},"1e04":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.getMonomerList},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"安装单元"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}},e._l(e.unitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},o=[]},"1f05":function(e,t,a){"use strict";a.r(t);var n=a("a428"),o=a("823e");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"6b633873",null);t["default"]=s.exports},"1f71":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.isVersionFour?a("v4"):a("v3")],1)},o=[]},"1f7f":function(e,t,a){"use strict";a.r(t);var n=a("c663"),o=a("6124");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("9e65");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"27839d8e",null);t["default"]=s.exports},2290:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._v(" 111"+e._s(e.customParams)+" ")])},o=[]},"22be":function(e,t,a){"use strict";a.r(t);var n=a("3765"),o=a("56618");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"71e8a031",null);t["default"]=s.exports},"23e9":function(e,t,a){},"25f1":function(e,t,a){"use strict";a.r(t);var n=a("0f42"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},2611:function(e,t,a){"use strict";a("7aec")},"270c":function(e,t,a){"use strict";a("fd51")},"27f3":function(e,t,a){"use strict";a("5a71")},2870:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=n(a("0f97"));t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"ExtensionName",Display_Name:"模型唯一ID",Align:"center",Is_Display:!0,Sort:1},{Code:"SerialNumber",Display_Name:"管理编号",Align:"center",Is_Display:!0,Sort:2},{Code:"TopHeight",Display_Name:"顶标高",Align:"center",Is_Display:!0,Sort:3},{Code:"BottomHeight",Display_Name:"底标高",Align:"center",Is_Display:!0,Sort:4},{Code:"Axis",Display_Name:"位置轴线",Align:"center",Is_Display:!0,Sort:5}],total:0,tbData:[],rowId:0}},mounted:function(){},methods:{init:function(e,t){var a=this;this.rowId=e.Id,this.tbLoading=!0,(0,i.GetComponentModelList)({id:e.Id}).then((function(e){e.IsSucceed?a.tbData=e.Data:a.$message({message:e.Message,type:"error"}),a.tbLoading=!1}))},handleView:function(e){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()}}}},2934:function(e,t,a){"use strict";a.r(t);var n=a("e180"),o=a("4d1f");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"16ab5bd8",null);t["default"]=s.exports},"2a16":function(e,t,a){"use strict";a.r(t);var n=a("5563"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"2a70":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b");var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=n(a("0f97")),c=a("0e9a");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Type",Display_Name:"文件类型",Is_Display:!0,Sort:1},{Code:"Title",Display_Name:"文件名称",Is_Display:!0,Sort:2},{Code:"Url",Display_Name:"操作",Align:"right",Is_Display:!0,Sort:3}],total:0,tbData:[],rowId:0,meansType:0}},mounted:function(){},methods:{init:function(e,t){var a=this;this.rowId=e.Id,this.meansType=t;var n=0==t?i.GetComponentDeepenFileList:i.GetPartDeepenFileList;this.tbLoading=!0,n({id:e.Id}).then((function(e){e.IsSucceed?a.tbData=e.Data:a.$message({message:e.Message,type:"error"}),a.tbLoading=!1}))},handleView:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n,r,i,s,l;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:if(n=e.lastIndexOf("."),r=e.lastIndexOf("?"),i=e.substring(n,r),".pdf"!==i){a.n=1;break}window.open(e+"#toolbar=0","_blank"),a.n=4;break;case 1:if(".xls"!==i&&".xlsx"!==i&&".doc"!==i&&".docx"!==i){a.n=3;break}return s=t.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),a.n=2,(0,c.fileToPdf)(e,!1);case 2:s.close(),a.n=4;break;case 3:".dwg"===i?(l=e.split(".com/"),l[1]=encodeURIComponent(l[1]),e=l.join(".com/"),window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+e,"_blank")):(".pdf"===i&&(e+="#toolbar=0"),window.open(e,"_blank"));case 4:return a.a(2)}}),a)})))()}}}},"2aa92":function(e,t,a){},"2b10":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("e9c4"),a("d3b7"),a("25f0"),a("159b");var o=n(a("34e9")),r=n(a("a602")),i=n(a("1f05")),s=n(a("15ac")),l=n(a("e89c")),c=a("a667");t.default={components:{TopHeader:o.default,zLeft:r.default,Dialog:l.default,zRight:i.default},mixins:[s.default],props:{selectList:{type:Array,default:function(){return[]}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{search:"",search2:"",select:"",dialogVisible:!1,leftSelectList:[],rightSelectList:[],columnOption:[],currentPackId:"",submitObj:{},fromType:2}},methods:{handleClose:function(){this.dialogVisible=!1},clearRightData:function(){this.currentPackId="",this.fetchRight()},fetchLeft:function(){this.$refs["left"].fetchData()},fetchRight:function(){this.$refs["right"].fetchRightData(this.currentPackId)},leftClick:function(e){this.currentPackId=e,this.fetchRight()},handleDeleteLeft:function(){var e=this;this.$confirm("是否删除构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.DeletePackage)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString()}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},getColumn:function(e){this.columnOption=e},handleDeleteRight:function(){var e=this;this.$confirm("是否删除该构件?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.DeleteSteel)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString(),itemId:JSON.stringify(e.rightSelectList.map((function(e){return e.Id})))}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft(),e.$refs["right"].fetchRightData()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(){var e=this;this.dialogVisible=!0,this.$nextTick((function(t){e.$refs["dialog"].fetchData(e.leftSelectList)}))},getSubmitObj:function(e){this.submitObj=e},handleAdd:function(){var e=this;if(this.selectList.length||2!==this.fromType)if(this.leftSelectList.length){var t=this.selectList.every((function(e){return 1===e.Sup_Count&&1===e.SteelAmount}));t||2!==this.fromType?this.$confirm("确定加入此构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitObj.Package.Id=e.leftSelectList.map((function(e){return e.Id})).toString(),1===e.fromType?(0,c.BatchAdd)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"})})):(0,c.AddSteel)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))})).catch((function(){e.$message({type:"info",message:"已取消"})})):this.$message({message:"请点击生成构件包",type:"warning"})}else this.$message({message:"请先选择包",type:"warning"});else this.$message({message:"请先选择构件",type:"warning"})},getLeftList:function(e){this.leftSelectList=e},getRightList:function(e){this.rightSelectList=e},handlePackage:function(e){if(this.fromType=e,2===e){var t={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};this.selectList.forEach((function(e){t.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),this.submitObj=t}}}}},"2b76":function(e,t,a){"use strict";a("9c50")},"2b9d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("5530")),r=a("2f62"),i=n(a("90a5")),s=n(a("fd71"));t.default={name:"PROComponentList",components:{v3:i.default,v4:s.default},computed:(0,o.default)({},(0,r.mapGetters)("tenant",["isVersionFour"]))}},"2c61":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCarNum=p,t.Delete=h,t.DeleteScanSteel=y,t.EditWithParts=i,t.GetCadUrlBySteelName=f,t.GetDetaileEntities=u,t.GetForgetScanWeight=c,t.GetNode=g,t.GetPageEntities=s,t.GetPageEntitiesForExproNew=m,t.GetPageStorageBySearch=b,t.GetPartPageList=_,t.GetProjectsNodeList=r,t.GetSteelHistory=d,t.GetTotalWeight=l,t.GetUserNodeList=v;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PLM/Plm_Projects_Node/GetNodeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PLM/Trace/EditWithParts",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PLM/Trace/GetPageEntities",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PLM/Trace/GetTotalWeight",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PLM/Trace/GetForgetScanWeight",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PLM/Trace/GetDetaileEntities",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PLM/Trace/GetSteelHistory",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PLM/Trace/GetCadUrlBySteelName",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PLM/Trace/GetPageEntitiesForExproNew",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PLM/Trace/AddCarNum",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PLM/Trace/Delete",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PLM/Trace/GetNode",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PLM/Trace/DeleteScanSteel",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PLM/Component/GetPageStorageBySearch",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PLM/AppScan/GetUserNodeList",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Part/GetPartPageList",method:"post",data:e})}},"2e8a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=u,t.GetCompTypeTree=d,t.GetComponentTypeEntity=c,t.GetComponentTypeList=i,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=m,t.GetTypePageList=s,t.RestoreTemplateType=v,t.SavDeepenTemplateSetting=b,t.SaveCompTypeIdentifySetting=y,t.SaveComponentType=l,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=p;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:r.default.stringify(e)})}function u(e){return(0,o.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:r.default.stringify(e)})}function f(e){return(0,o.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,o.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function y(e){return(0,o.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function b(e){return(0,o.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function v(e){return(0,o.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=u,t.GeAreaTrees=C,t.GetFileSync=k,t.GetInstallUnitIdNameList=S,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=I,t.GetProjectAreaTreeList=P,t.GetProjectEntity=l,t.GetProjectList=s,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=_,t.GetSchedulingPartList=D,t.IsEnableProjectMonomer=d,t.SaveProject=c,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=y,t.UpdateProjectTemplateContract=b,t.UpdateProjectTemplateOther=v;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(e)})}function d(e){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function k(e){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"31b1":function(e,t,a){"use strict";a.r(t);var n=a("3895"),o=a("b2e8");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("270c");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"d9a81c76",null);t["default"]=s.exports},33191:function(e,t,a){"use strict";a.r(t);var n=a("3a4c"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"33c8":function(e,t,a){},3457:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=a("a667"),l=n(a("bbc2")),c=a("0e9a"),u=a("66f9"),d=a("2c61"),f=a("1b69"),m=a("f2f6"),p=a("2d91");t.default={name:"ComponentImport",components:{OSSUpload:l.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Project_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[],projectList:[],unitList:[]}},watch:{typeId:function(e){this.form.Type_Id=e},"form.MonomerId":function(){this.getArea()},"form.Area_Name":function(e){e&&this.getUnitList()}},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.form.Factory_Id=localStorage.getItem("CurReferenceId"),e.getInfo(),t.n=1,e.getProjectList();case 1:return t.n=2,e.getMonomerStatus();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,p.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=3;break}return t.n=2,e.getMonomerList();case 2:t.n=4;break;case 3:e.getArea();case 4:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Batches="",e.form.MonomerId="",e.form.Area_Name="",t.n=1,(0,u.GetProMonomerList)({projectId:e.form.Project_Id,id:e.projectList.find((function(t){return t.Sys_Project_Id===e.form.Project_Id})).Id});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getUnitList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return a={Sys_Project_Id:e.form.Project_Id},e.isUnityInfo&&(a=(0,o.default)((0,o.default)({},a),{},{Area_Id:null===(n=e.AreaOption.find((function(t){return t.Name===e.form.Area_Name})))||void 0===n?void 0:n.Id})),t.n=1,(0,m.GetInstallUnitList)(a);case 1:i=t.v,e.unitList=i.Data;case 2:return t.a(2)}}),t)})))()},getProjectList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetProjectList)({});case 1:a=t.v,a.IsSucceed?(e.projectList=a.Data,e.projectList.length>0&&(e.form.Project_Id=e.projectList[0].Sys_Project_Id)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName")},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,s.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,c.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getArea:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,s.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code,ProjectId:e.form.Project_Id}});case 1:a=t.v,a.IsSucceed?(e.AreaOption=a.Data,e.form.Area_Name=a.Data[0].Name):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,o=a.Type_Id,r=a.Project_Id,i=a.Area_Name,s=a.Factory_Id,l=a.MonomerId,c=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",o),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",e.projectList.find((function(e){return e.Sys_Project_Id===r})).Id),e.fileFormData.append("Area_Name",i),e.fileFormData.append("Factory_Id",s),e.fileFormData.append("MonomerId",l),e.fileFormData.append("Batches",c),(0,d.CommonImportModelToComp)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},"350f":function(e,t,a){},3594:function(e,t,a){"use strict";a("1bde")},3765:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._v(" 111"+e._s(e.customParams)+" ")])},o=[]},3876:function(e,t,a){"use strict";a.r(t);var n=a("b9ca"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},3895:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plmdialog plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose,close:e.handleClose}},[e.isDeep||e.isSHQD?a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1):e._e(),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[e.isCZD||e.isComp||e.isPart||e.isUnitPart?a("el-form-item",{attrs:{label:e.isPart?"关联零件":e.isComp?"关联构件":e.isUnitPart?"关联部件":"关联构件",prop:"Type_Name"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{filterable:"",remote:"",multiple:"","reserve-keyword":"",loading:e.selectLoading,"remote-method":e.remoteMethod},on:{"visible-change":e.visibleChange,change:e.selectChange},model:{value:e.relatedItem,callback:function(t){e.relatedItem=t},expression:"relatedItem"}},e._l(e.compList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),a("el-form-item",{attrs:{label:"是否变更",prop:"IsChanged"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("否")])],1),a("el-form-item",{attrs:{label:"是否大型图纸文件",prop:"Is_Big_File","label-width":"193px"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v(" 是否大型图纸文件 "),a("el-tooltip",{attrs:{content:"常规导入选“否”，当导入布置图时请选“是”",placement:"top"}},[a("i",{staticClass:"el-icon-question"})])],1),a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Big_File,callback:function(t){e.$set(e.form,"Is_Big_File",t)},expression:"form.Is_Big_File"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Big_File,callback:function(t){e.$set(e.form,"Is_Big_File",t)},expression:"form.Is_Big_File"}},[e._v("否")])],1),e.isVersionFour&&(e.isComp||e.isPart||e.isUnitPart)?a("el-form-item",{attrs:{label:"图纸命名匹配方式",prop:"Drawing_Match_Type","label-width":"180px"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.Drawing_Match_Type,callback:function(t){e.$set(e.form,"Drawing_Match_Type",t)},expression:"form.Drawing_Match_Type"}},e._l(e.drawingList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,disabled:e.disabled,value:e.value}})})),1)],1):e._e(),"新增文件"==e.title?a("el-form-item",{attrs:{label:"是否将重复文件移入历史文件库",prop:"ishistory","label-width":"260px"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("否")])],1):e._e(),e.BIMFiles?a("el-form-item",{attrs:{label:"是否加载",prop:"Is_Load"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("否")])],1):e._e(),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"companyUpload",staticClass:"upload-demo",attrs:{drag:"",data:{callback:!1},action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:e.isDeep||e.isSHQD||e.isEditSCYTH||"edit"===e.type?1:1e3,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:"",accept:e.allowFile,disabled:"edit"===e.type&&e.BIMFiles,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("el-form-item",{attrs:{label:"是否通知"}},[a("el-radio",{attrs:{label:!0},on:{input:e.handleRadioChange},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("否")])],1),e.isNotify?[a("el-form-item",{attrs:{label:"通知标题",prop:"model.Title",rules:{required:!0,message:"通知标题不能为空",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.model.Title,callback:function(t){e.$set(e.form.model,"Title",t)},expression:"form.model.Title"}})],1),a("el-form-item",{attrs:{label:"通知人员",prop:"model.UserIds",rules:[{required:!0,message:"请选择通知人员",trigger:"change"}]}},[a("form-item",{attrs:{"project-id":e.projectId,type:"contacts",filterable:"",multiple:"",width:"360px"},model:{value:e.form.model.UserIds,callback:function(t){e.$set(e.form.model,"UserIds",t)},expression:"form.model.UserIds"}})],1),a("el-form-item",{attrs:{label:"通知内容"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{type:"textarea"},model:{value:e.form.model.Content,callback:function(t){e.$set(e.form.model,"Content",t)},expression:"form.model.Content"}})],1)]:e._e()],2),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},o=[]},"38fb":function(e,t,a){},"394d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose,close:e.handleClose}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v("注意：请先"),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"导入方式",prop:"areaType"}},[a("el-radio-group",{model:{value:e.areaType,callback:function(t){e.areaType=t},expression:"areaType"}},[a("el-radio",{attrs:{label:2}},[e._v("多区域导入")]),a("el-radio",{attrs:{label:1}},[e._v("单区域导入")])],1)],1),a("el-form-item",{attrs:{label:"是否跳过生产",prop:"Is_Skip_Production"}},[a("el-radio-group",{model:{value:e.form.Is_Skip_Production,callback:function(t){e.$set(e.form,"Is_Skip_Production",t)},expression:"form.Is_Skip_Production"}},[a("el-radio",{attrs:{label:!0}},[e._v("是")]),a("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),1===e.areaType?a("el-form-item",{attrs:{label:"区域",prop:"Area_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}})],1):e._e(),a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:2,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit()}}},[e._v("确 定")])],1)],1)},o=[]},"3a4c":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=n(a("0f97"));t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"ExtensionName",Display_Name:"模型唯一ID",Align:"center",Is_Display:!0,Sort:1},{Code:"SerialNumber",Display_Name:"管理编号",Align:"center",Is_Display:!0,Sort:2},{Code:"TopHeight",Display_Name:"顶标高",Align:"center",Is_Display:!0,Sort:3},{Code:"BottomHeight",Display_Name:"底标高",Align:"center",Is_Display:!0,Sort:4},{Code:"Axis",Display_Name:"位置轴线",Align:"center",Is_Display:!0,Sort:5}],total:0,tbData:[],rowId:0}},mounted:function(){},methods:{init:function(e,t){var a=this;this.rowId=e.Id,this.tbLoading=!0,(0,i.GetComponentModelList)({id:e.Id}).then((function(e){e.IsSucceed?a.tbData=e.Data:a.$message({message:e.Message,type:"error"}),a.tbLoading=!1}))},handleView:function(e){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()}}}},"3a4f":function(e,t,a){"use strict";a.r(t);var n=a("5baa"),o=a("9c03");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("f1b36");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"b4044b3e",null);t["default"]=s.exports},"3b2f":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{width:"100%","text-align":"center","margin-bottom":"20px"}},[a("el-button",{attrs:{type:"success",plain:""},on:{click:function(t){return e.handleSteelExport(0)}}},[e._v("导出未绑定的构件")]),a("el-button",{attrs:{type:"success",plain:""},on:{click:function(t){return e.handleSteelExport(1)}}},[e._v("导出已绑定的构件")])],1),a("div",{staticStyle:{width:"100%",height:"130px","text-align":"center","margin-bottom":"20px",display:"flex","justify-content":"center","align-items":"flex-start"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:1,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload",staticStyle:{"font-size":"36px"}}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.importModelFn()}}},[e._v("确 定")])],1)])},o=[]},"3b5f3":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("841c");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=n(a("0f97")),l=n(a("15ac")),c=a("a667");t.default={components:{DynamicDataTable:s.default},mixins:[l.default],props:{search:{type:String,default:""},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},tbData:[],columns:[],total:0,tbLoading:!1,currentRow:null}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_page_list,".concat(e.typeEntity.Code));case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,c.GetPageStorageBySearchPackages)({PageInfo:(0,o.default)((0,o.default)({},this.queryInfo),{},{Search:this.search}),TypeCode:this.typeEntity.Code}).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.$emit("setSelect",[]),e.$emit("clearRightData","")):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleRowClick:function(e){var t=e.row;this.$emit("leftClick",t.Id),this.handleSelect({row:t})},handleSelect:function(e){var t,a=this,n=e.row;if(n.Id===(null===(t=this.currentRow)||void 0===t?void 0:t.Id))return this.$refs.dyTable.clearSelection(),this.currentRow=null,void this.$emit("selectList",[]);this.currentRow=n,this.$refs.dyTable.clearSelection(),this.$nextTick((function(e){a.$refs.dyTable.toggleRowSelection(n)})),this.$emit("selectList",[n])},selectAll:function(){this.$refs.dyTable.clearSelection()}}}},"3b65":function(e,t,a){"use strict";a("861e")},"3b8d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7");var o=n(a("bbc2")),r=a("586a");t.default={components:{OSSUpload:o.default},props:{typeEntity:{type:Object,default:function(){}}},data:function(){return{btnLoading:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,attachments:[],projectId:""}},computed:{},created:function(){},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},handleChange:function(){},beforeUpload:function(e){this.curFile=e,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=0;this.fileList.filter((function(t,n){t.name===e.name&&(a=n)})),this.fileList.splice(a,1),this.attachments.splice(a,1),this.btnLoading=!t.every((function(e){return"success"===e.status}))},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]}),this.btnLoading=!a.every((function(e){return"success"===e.status}))},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.fileList=[],this.dialogVisible=!1}catch(e){}},importModelFn:function(){var e=this;if(0==this.fileList.length)return this.$message({message:"请上传文件",type:"warning"}),!1;(0,r.ImportComponentExtendInfo)({uploadUrl:this.attachments[0].File_Url}).then((function(t){t.IsSucceed?(e.$message({message:"导入成功",type:"success"}),e.$emit("close")):e.$message({message:t.Message,type:"error"})}))},handleSteelExport:function(e){this.$emit("locationExport")}}}},"3c033":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"加工工厂",prop:"Factory_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"构件批次"}},[a("el-input",{model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}})],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},o=[]},"3d53":function(e,t,a){},"3d8b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("841c");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=n(a("0f97")),l=n(a("15ac")),c=a("a667");t.default={components:{DynamicDataTable:s.default},mixins:[l.default],props:{search:{type:String,default:""},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},tbData:[],columns:[],total:0,tbLoading:!1,currentRow:null}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_page_list,".concat(e.typeEntity.Code));case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,c.GetPageStorageBySearchPackages)({PageInfo:(0,o.default)((0,o.default)({},this.queryInfo),{},{Search:this.search}),TypeCode:this.typeEntity.Code}).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.$emit("setSelect",[]),e.$emit("clearRightData","")):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleRowClick:function(e){var t=e.row;this.$emit("leftClick",t.Id),this.handleSelect({row:t})},handleSelect:function(e){var t,a=this,n=e.row;if(n.Id===(null===(t=this.currentRow)||void 0===t?void 0:t.Id))return this.$refs.dyTable.clearSelection(),this.currentRow=null,void this.$emit("selectList",[]);this.currentRow=n,this.$refs.dyTable.clearSelection(),this.$nextTick((function(e){a.$refs.dyTable.toggleRowSelection(n)})),this.$emit("selectList",[n])},selectAll:function(){this.$refs.dyTable.clearSelection()}}}},4292:function(e,t,a){"use strict";a.r(t);var n=a("91178"),o=a("3876");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("9a89");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"75a37044",null);t["default"]=s.exports},4356:function(e,t,a){"use strict";a.r(t);var n=a("ce3c"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"436e":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=a("6f23"),c=n(a("0f97"));t.default={components:{DynamicDataTable:c.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},columns:[{Code:"Scheduling_Status",Display_Name:"构件状态",Align:"center",Is_Display:!0,Sort:1},{Code:"Component_Count",Display_Name:"数量（件）",Align:"center",Is_Display:!0,Sort:2}],tbData:[],Plan_Date:"",Is_Over_Time:!1,rowId:0}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id,this.tbLoading=!0,(0,i.GetSchedulingList)({id:e.Id}).then((function(e){e.IsSucceed?(t.Plan_Date=(0,l.formatDate)(e.Data.Plan_Date,"yyyy-MM-dd"),t.Is_Over_Time=e.Data.Is_Over_Time,t.tbData=e.Data.Scheduling_List):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleView:function(e){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()}}}},4408:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("e9f5"),a("7d54"),a("d3b7"),a("25f0"),a("3ca3"),a("159b"),a("ddb0");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=a("586a"),l=n(a("15ac")),c=n(a("0f97")),u=(a("ed08"),a("21a6")),d=n(a("c4e3")),f=n(a("bc3a"));t.default={components:{DynamicDataTable:c.default},mixins:[l.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{searchDate:[],tbLoading:!1,btnLoading:!1,tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],total:0,tbData:[],selectArray:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("steels_import_file_page_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,o.default)({},this.queryInfo);this.searchDate&&this.searchDate.length>1?(t.HisStartDate=this.searchDate[0],t.HisEndDate=this.searchDate[1]):(t.HisStartDate="",t.HisEndDate=""),t.Sys_Project_Id=this.sysProjectId,(0,s.GetImportHistoryPageList)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},multiSelectedChange:function(e){this.selectArray=e},handleOpen:function(e){window.open(e,"_blank")},onSubmit:function(){this.btnLoading=!0,this.handleBatchDownload(this.selectArray,"历史清单导出")},handleBatchDownload:function(e,t){var a=this;return(0,i.default)((0,r.default)().m((function n(){var o,i,s,l;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return o=e,i=new d.default,s={},l=[],n.n=1,o.forEach((function(e){var t=a.getFile(e.FileUrl).then((function(t){var a=e.FileName;i.file(a,t,{binary:!0}),s[a]=t}));l.push(t)}));case 1:Promise.all(l).then((function(){i.generateAsync({type:"blob"}).then((function(e){(0,u.saveAs)(e,t+".zip"),a.btnLoading=!1,a.notify&&a.notify.close()})).catch((function(e){a.btnLoading=!1,a.$message.error("网络出现了一点小问题，请稍后重试")}))}));case 2:return n.a(2)}}),n)})))()},getFile:function(e){return new Promise((function(t,a){(0,f.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))}}}},4471:function(e,t,a){"use strict";a.r(t);var n=a("a41b"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},4548:function(e,t,a){},4561:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={name:"ComponentsHistory",props:{customParams:{type:Object,default:function(){return{}}}},created:function(){var e=this;(0,n.GetComponentChangeHistory)({steelUnique:this.customParams.steelUnique}).then((function(t){e.tableData=t.Data}))}}},4791:function(e,t,a){"use strict";a.r(t);var n=a("cb62"),o=a("7e6f1");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("c108"),a("27f3");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"04e3b6fc",null);t["default"]=s.exports},"479a":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""},scopedSlots:e._u([{key:"Url",fn:function(t){var n=t.row;return[a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n.Url)}}},[e._v("查看")])],1)]}}])})],1)])},o=[]},"47aa":function(e,t,a){"use strict";a.r(t);var n=a("bba0"),o=a("d594");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("c465");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"02b134af",null);t["default"]=s.exports},"4b6d":function(e,t,a){"use strict";a.r(t);var n=a("c404"),o=a("25f1");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"7ba93d1c",null);t["default"]=s.exports},"4b7d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b");var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=n(a("0f97")),c=a("0e9a");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Type",Display_Name:"文件类型",Is_Display:!0,Sort:1},{Code:"Title",Display_Name:"文件名称",Is_Display:!0,Sort:2},{Code:"Url",Display_Name:"操作",Align:"right",Is_Display:!0,Sort:3}],total:0,tbData:[],rowId:0,meansType:0}},mounted:function(){},methods:{init:function(e,t){var a=this;this.rowId=e.Id,this.meansType=t;var n=0==t?i.GetComponentDeepenFileList:i.GetPartDeepenFileList;this.tbLoading=!0,n({id:0==t?e.Id:e.Part_Aggregate_Id}).then((function(e){e.IsSucceed?a.tbData=e.Data:a.$message({message:e.Message,type:"error"}),a.tbLoading=!1}))},handleView:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n,r,i,s,l;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:if(n=e.lastIndexOf("."),r=e.lastIndexOf("?"),i=e.substring(n,r),".pdf"!==i){a.n=1;break}window.open(e+"#toolbar=0","_blank"),a.n=4;break;case 1:if(".xls"!==i&&".xlsx"!==i&&".doc"!==i&&".docx"!==i){a.n=3;break}return s=t.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),a.n=2,(0,c.fileToPdf)(e,!1);case 2:s.close(),a.n=4;break;case 3:".dwg"===i?(l=e.split(".com/"),l[1]=encodeURIComponent(l[1]),e=l.join(".com/"),window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+e,"_blank")):(".pdf"===i&&(e+="#toolbar=0"),window.open(e,"_blank"));case 4:return a.a(2)}}),a)})))()}}}},"4be1":function(e,t,a){"use strict";a.r(t);var n=a("d3be"),o=a("cd93");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("cf9a");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"80d1aefe",null);t["default"]=s.exports},"4c33":function(e,t,a){"use strict";a("6cad")},"4d1f":function(e,t,a){"use strict";a.r(t);var n=a("0da3"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"4e82":function(e,t,a){"use strict";var n=a("23e7"),o=a("e330"),r=a("59ed"),i=a("7b0b"),s=a("07fa"),l=a("083a"),c=a("577e"),u=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),p=a("99f4"),h=a("1212"),g=a("ea83"),y=[],b=o(y.sort),v=o(y.push),_=u((function(){y.sort(void 0)})),P=u((function(){y.sort(null)})),S=f("sort"),C=!u((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var e,t,a,n,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:t+n,v:a})}for(y.sort((function(e,t){return t.v-e.v})),n=0;n<y.length;n++)t=y[n].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),I=_||!P||!S||!C,D=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:c(t)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:I},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(C)return void 0===e?b(t):b(t,e);var a,n,o=[],c=s(t);for(n=0;n<c;n++)n in t&&v(o,t[n]);d(o,D(e)),a=s(o),n=0;while(n<a)t[n]=o[n++];while(n<c)l(t,n++);return t}})},"4f93":function(e,t,a){"use strict";a.r(t);var n=a("c895"),o=a("1599");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"731342be",null);t["default"]=s.exports},5012:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Add=_,t.AddSteel=P,t.AddSynNew=c,t.BatchAdd=v,t.BatchEdit=b,t.DeleteBatch=p,t.DeletePackage=I,t.DeleteSteel=D,t.Deletepart=F,t.DeletepartByfindkeywodes=j,t.DownAllFiles=m,t.EditPackage=C,t.EditPartpage=N,t.EditPartpagelist=A,t.ExportComponentList=f,t.ExportPlanpartInfo=G,t.ExportPlanpartcountInfo=R,t.GetAreaPageList=T,t.GetEntities=d,t.GetEntity=h,t.GetList=k,t.GetPageStorageBySearchPackages=i,t.GetPageStorageHistorySteel=l,t.GetPageStorageSteelsBySearchItem=s,t.GetPartEntity=O,t.GetPartWeightList=$,t.GetProfessEntities=y,t.GetSummaryStorageBySearch=L,t.PackageGetEntity=S,t.PartDeepeningImportTemplate=x,t.ProjectSchedule=w,t.SaveEntity=g,t.SteelBardcodeDataTemplate=u;var o=n(a("4328")),r=n(a("b775"));function i(e){return(0,r.default)({url:"/plm/package/GetPageStorageBySearchPackages",method:"post",data:e})}function s(e){return(0,r.default)({url:"/plm/package/GetPageStorageSteelsBySearchItem",method:"post",data:e})}function l(e){return(0,r.default)({url:"/plm/component/GetPageStorageHistorySteel",method:"post",data:e})}function c(e){return(0,r.default)({url:"/plm/Component/AddSynNew",method:"post",data:e,timeout:18e5})}function u(e){return(0,r.default)({url:"/plm/Component/SteelBardcodeDataTemplate",method:"post",data:e})}function d(e){return(0,r.default)({url:"/plm/plm_project_areas/GetEntities",method:"post",data:e})}function f(e){return(0,r.default)({url:"/plm/Component/ExportComponentList",method:"post",data:e})}function m(e){return(0,r.default)({url:"/plm/Component/DownAllFiles",method:"post",data:e,responseType:"blob"})}function p(e){return(0,r.default)({url:"/plm/Component/DeleteBatch",method:"post",data:e})}function h(e){return(0,r.default)({url:"/plm/Component/GetEntity",method:"post",data:e})}function g(e){return(0,r.default)({url:"/plm/Component/SaveEntity",method:"post",data:e})}function y(e){return(0,r.default)({url:"/plm/Plm_Professional_Type/GetEntities",method:"post",data:e})}function b(e){return(0,r.default)({url:"/plm/Component/BatchEdit",method:"post",data:o.default.stringify(e)})}function v(e){return(0,r.default)({url:"/plm/Package/BatchAdd",method:"post",data:e})}function _(e){return(0,r.default)({url:"/plm/Package/Add",method:"post",data:e})}function P(e){return(0,r.default)({url:"/plm/Package/AddSteel",method:"post",data:e})}function S(e){return(0,r.default)({url:"/plm/Package/GetEntity",method:"post",data:o.default.stringify(e)})}function C(e){return(0,r.default)({url:"/plm/Package/EditPackage",method:"post",data:e})}function I(e){return(0,r.default)({url:"/plm/Package/DeletePackage",method:"post",data:e})}function D(e){return(0,r.default)({url:"/plm/Package/DeleteSteel",method:"post",data:o.default.stringify(e)})}function k(e){return(0,r.default)({url:"/plm/Plm_Professional_Type/GetList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/plm/Plm_Project_Areas/GetAreaPageList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PLM/Component/GetSummaryStorageBySearch",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PLM/Plm_SteelsList/ProjectSchedule",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/Part/PartDeepeningImportTemplate",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Part/GetPartEntity",method:"post",data:e,timeout:12e5})}function N(e){return(0,r.default)({url:"/PRO/Part/EditPartpage",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/Part/EditPartpagelist",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Part/DeletepartByfindkeywodes",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/Part/ExportPlanpartInfo",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/Part/ExportPlanpartcountInfo",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/Part/GetPartWeightList",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Part/Deletepart",method:"post",data:e})}},"508b":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)},o=[]},"50b3":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("2909"));a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("ddb0");var r=a("586a");t.default={name:"TracePlot",props:{trackDrawerData:{type:Object,default:function(){return{}}}},data:function(){return{activities:[],one:{DeepenAmount:"",ImportUserName:"",ImportDate:""},two:{SchduleStatus:0,SchduleAmount:0,SchduleDate:"",SchduleUserName:""},three:[],four:{RukuStatus:0,RukuList:[]},five:{SendStatus:0,DeliveryList:[]},isNeedProduct:!0,IsMultiSchdule:!1,isMoreSchduleStatus:0,loading:!0}},mounted:function(){this.getComponentProductionTrack()},methods:{getComponentProductionTrack:function(){var e=this;this.loading=!0,(0,r.GetComponentProductionTrack)({SysProjectId:this.trackDrawerData.Sys_Project_Id,CompIds:this.trackDrawerData.Id}).then((function(t){var a=t.Data,n=a.DeepenAmount,r=a.ImportUserName,i=a.ImportDate,s=a.SchduleStatus,l=a.SchduleAmount,c=a.SchduleUserName,u=a.SchduleDate,d=a.IsNeedProduct,f=a.ProcessList,m=a.RukuStatus,p=a.RukuList,h=a.SendStatus,g=a.DeliveryList,y=a.IsMultiSchdule;e.one={DeepenAmount:n,ImportUserName:r,ImportDate:i},e.isNeedProduct=d,e.IsMultiSchdule=y,e.two={SchduleStatus:s,SchduleAmount:l,SchduleUserName:c,SchduleDate:u},e.three=f;var b=(0,o.default)(new Set(f.map((function(e){return e.ProcessStatus}))));e.isMoreSchduleStatus=b.includes(1)?1:b.includes(2)?2:0,e.four={RukuStatus:m,RukuList:p},e.five={SendStatus:h,DeliveryList:g}})).finally((function(){e.loading=!1}))},getIconOrColor:function(e,t){return"icon"===e?2===t?"el-icon-success":"el-icon-yuanxing":"iconColor"===e?1===t?"inProgress":2===t?"complete":"init":"color"===e?1===t?"blue1":2===t?"green":"":"bgIcon"===e?1===t?"cs-unComplete-icon":2===t?"cs-success-icon":"cs-init-icon":void 0}}}},"50d9":function(e,t,a){"use strict";a("ecba")},5313:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("div",[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.searchDate,callback:function(t){e.searchDate=t},expression:"searchDate"}}),a("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("查询")])],1),a("div",[a("el-button",{attrs:{type:"primary",disabled:!e.selectArray.length,loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("导出")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,size:"mini",border:"",stripe:""},on:{multiSelectedChange:e.multiSelectedChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange},scopedSlots:e._u([{key:"Create_Date",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Create_Date))+" ")]}},{key:"FileUrl",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),e.handleOpen(n.FileUrl)}}},[e._v(" "+e._s(n.FileUrl))])]}},{key:"Modify_Date ",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Modify_Date))+" ")]}}])})],1)])},o=[]},5332:function(e,t,a){"use strict";a("ddcb")},"539c":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("d3b7");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=n(a("0f97")),l=n(a("15ac")),c=a("a667");t.default={components:{DynamicDataTable:s.default},mixins:[l.default],props:{zParams:{type:Object,default:function(){}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbLoading:!1,tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},columns:[],total:0,tbData:[]}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_item_page_list,".concat(e.typeEntity.Code));case 1:a=t.v,e.$emit("getColumn",a.filter((function(e){return e.Is_Display}))||[]);case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){this.$emit("getList")},fetchRightData:function(e){var t=this;if(!e)return this.tbData=[],void(this.total=0);this.tbLoading=!0,(0,c.GetPageStorageSteelsBySearchItem)((0,o.default)((0,o.default)({Id:e},this.zParams),{},{PageInfo:(0,o.default)({},this.queryInfo)})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleSelect:function(e){this.$emit("selectList",e)}}}},5548:function(e,t,a){"use strict";a.r(t);var n=a("b411"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},5563:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={name:"ComponentsHistory",props:{customParams:{type:Object,default:function(){return{}}}},created:function(){var e=this;(0,n.GetComponentChangeHistory)({steelUnique:this.customParams.steelUnique}).then((function(t){e.tableData=t.Data}))}}},"55cb":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"h100 app-wrapper",attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff",attrs:{width:300},model:{value:e.showExpand,callback:function(t){e.showExpand=t},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[a("div",{staticClass:"tree-search"},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"导入状态选择"},model:{value:e.statusType,callback:function(t){e.statusType=t},expression:"statusType"}},[a("el-option",{attrs:{label:"已导入",value:"已导入"}}),a("el-option",{attrs:{label:"未导入",value:"未导入"}}),a("el-option",{attrs:{label:"已变更",value:"已变更"}})],1),a("el-input",{attrs:{placeholder:"关键词搜索",size:"small",clearable:"","suffix-icon":"el-icon-search"},on:{blur:e.fetchTreeDataLocal,clear:e.fetchTreeDataLocal},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fetchTreeDataLocal(t)}},model:{value:e.projectName,callback:function(t){e.projectName="string"===typeof t?t.trim():t},expression:"projectName"}})],1),a("el-divider",{staticClass:"cs-divider"}),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":e.customFilterFun,loading:e.treeLoading,"tree-data":e.treeData,"show-status":"","show-detail":"","filter-text":e.filterText,"expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick},scopedSlots:e._u([{key:"csLabel",fn:function(t){var n=t.showStatus,o=t.data;return[o.ParentNodes?e._e():a("span",{staticClass:"cs-blue"},[e._v("("+e._s(o.Code)+")")]),e._v(e._s(o.Label)+" "),n&&"全部"!=o.Label?[o.Data.Is_Deepen_Change?a("span",{staticClass:"cs-tag redBg"},[a("i",{staticClass:"fourRed"},[e._v("已变更")])]):a("span",{class:["cs-tag",1==o.Data.Is_Imported?"greenBg":"orangeBg"]},[a("i",{class:[1==o.Data.Is_Imported?"fourGreen":"fourOrange"]},[e._v(e._s(1==o.Data.Is_Imported?"已导入":"未导入"))])])]:e._e()]}}])})],1)],1)]),a("div",{staticClass:"cs-right"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",staticClass:"demo-form-inline",attrs:{model:e.customParams,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"构件名称",prop:"Names"}},[a("el-input",{staticClass:"input-with-select",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.names,callback:function(t){e.names=t},expression:"names"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.nameMode,callback:function(t){e.nameMode=t},expression:"nameMode"}},[a("el-option",{attrs:{label:"模糊搜索",value:1}}),a("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:6}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"60px",label:"批次",prop:"InstallUnit_Ids"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",multiple:"",placeholder:"请选择",disabled:!Boolean(e.customParams.Area_Id)},model:{value:e.customParams.InstallUnit_Ids,callback:function(t){e.$set(e.customParams,"InstallUnit_Ids",t)},expression:"customParams.InstallUnit_Ids"}},e._l(e.installUnitIdNameList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:4,lg:5,xl:4}},[a("el-form-item",{attrs:{"label-width":"92px",prop:"SteelType"},scopedSlots:e._u([{key:"label",fn:function(){return[a("span",[e._v("构件类型")])]},proxy:!0}])},[a("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},model:{value:e.customParams.SteelType,callback:function(t){e.$set(e.customParams,"SteelType",t)},expression:"customParams.SteelType"}})],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"构件号",prop:"SteelNumber"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.SteelNumber,callback:function(t){e.$set(e.customParams,"SteelNumber",t)},expression:"customParams.SteelNumber"}})],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"构件序号",prop:"SteelCode"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.SteelCode,callback:function(t){e.$set(e.customParams,"SteelCode",t)},expression:"customParams.SteelCode"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6,lg:6,xl:6}},[a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Spec,callback:function(t){e.$set(e.customParams,"Spec",t)},expression:"customParams.Spec"}})],1)],1),a("el-col",{attrs:{span:5,lg:5,xl:6}},[a("el-form-item",{attrs:{"label-width":"60px",label:"材质",prop:"Texture"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Texture,callback:function(t){e.$set(e.customParams,"Texture",t)},expression:"customParams.Texture"}})],1)],1),a("el-col",{attrs:{span:4,lg:5,xl:4}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"92px",label:"是否直发件",prop:"Is_Direct"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.customParams.Is_Direct,callback:function(t){e.$set(e.customParams,"Is_Direct",t)},expression:"customParams.Is_Direct"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",{attrs:{label:"操作人",prop:"Create_UserName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Create_UserName,callback:function(t){e.$set(e.customParams,"Create_UserName",t)},expression:"customParams.Create_UserName"}})],1)],1),a("el-col",{attrs:{span:4,lg:4,xl:4}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSearch()}}},[e._v("搜索 ")]),a("el-button",{on:{click:function(t){return e.handleSearch("reset")}}},[e._v("重置")])],1)],1)],1)],1)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},[a("div",[a("el-dropdown",{attrs:{trigger:"click",placement:"bottom-start"},on:{command:function(t){return e.handleCommand(t,1)}}},[a("el-button",{attrs:{type:"primary",disabled:!e.currentLastLevel}},[e._v("多级清单导入 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{disabled:e.allStopFlag,command:"add"}},[e._v("新增导入")]),a("el-dropdown-item",{attrs:{disabled:e.allStopFlag,command:"cover"}},[e._v("覆盖导入")]),a("el-dropdown-item",{attrs:{disabled:e.allStopFlag,command:"halfcover"}},[e._v("部分覆盖导入")])],1)],1),a("el-button",{attrs:{type:"primary"},on:{click:e.modelListImport}},[e._v("导入模型清单 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.LocationImport}},[e._v("位置信息导入 ")]),e.isVersionFour?e._e():a("el-button",{attrs:{disabled:!e.selectList.length},on:{click:e.handleSchedulingInfoExport}},[e._v("导出排产单模板 ")]),e.isVersionFour?a("el-dropdown",{attrs:{trigger:"click",placement:"bottom-start"},on:{command:e.handleExport}},[a("el-button",{attrs:{type:"primary",disabled:!e.currentLastLevel}},[e._v("导出 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"com"}},[e._v("纯构件")]),a("el-dropdown-item",{attrs:{command:"all"}},[e._v("完整清单")])],1)],1):a("el-button",{on:{click:function(t){return e.handleSteelExport(2)}}},[e._v("导出构件")]),a("el-button",{on:{click:e.handleHistoryExport}},[e._v("历史清单导出")]),a("el-button",{attrs:{loading:e.scheduleLoading,disabled:!e.selectList.length},on:{click:e.handleScheduleExport}},[e._v("排产单导出")]),a("el-button",{attrs:{disabled:!e.selectList.length||e.selectList.some((function(e){return e.stopFlag})),type:"primary",plain:""},on:{click:e.handleBatchEdit}},[e._v("批量编辑 ")]),a("el-button",{attrs:{type:"danger",plain:"",disabled:!e.selectList.length||e.selectList.some((function(e){return e.stopFlag}))},on:{click:e.handleDelete}},[e._v("删除选中 ")]),a("el-button",{attrs:{type:"success",plain:"",disabled:!Boolean(e.customParams.Sys_Project_Id)},on:{click:e.handelImport}},[e._v("图纸导入 ")])],1),e.showTotalLength?a("div",{staticClass:"cs-length"},[a("span",{staticClass:"txt-green"},[e._v("累计长度："+e._s(e.deepenTotalLength))])]):e._e()]),a("div",{staticClass:"info-box"},[a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("深化总数")]),a("i",[e._v(e._s(e.SteelAmountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("深化总量")]),a("i",[e._v(e._s(e.SteelAllWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("排产总数")]),a("i",[e._v(e._s(e.SchedulingNumTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("排产总量")]),a("i",[e._v(e._s(e.SchedulingAllWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col",staticStyle:{cursor:"pointer"},on:{click:function(t){return e.getProcessData()}}},[a("span",[a("span",{staticClass:"info-label"},[e._v("完成总数")]),a("i",[e._v(e._s(e.FinishCountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("完成总量")]),a("i",[e._v(e._s(e.FinishWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("直发件总数")]),a("i",[e._v(e._s(e.IsComponentTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("直发件总量")]),a("i",[e._v(e._s(e.IsComponentTotalSteelAllWeight)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("毛重合计")]),a("i",[e._v(e._s(e.TotalGrossWeightT)+" t")])])])]),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"auto","auto-resize":"",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0},"row-config":{isHover:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"44"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width?t.Width:120},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return["SteelName"==t.Code?a("div",[o.Is_Change?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("变")]):e._e(),o.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),a("span",{staticClass:"isPicActive",on:{click:function(t){return e.getComponentInfo(o)}}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])],1):"SteelAmount"==t.Code?a("div",[1==o.Is_Component_Status?a("span",{staticStyle:{color:"#298dff"}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" 件")]):a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.handleViewModel(o)}}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" 件")])]):"SchedulingNum"==t.Code?a("div",[o[t.Code]?a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.handleViewScheduling(o)}}},[e._v(e._s(o[t.Code]+" 件"))]):a("span",[e._v("-")])]):"SH"==t.Code?a("div",[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleViewSH(o,0)}}},[e._v("查看 ")])],1):"Part"==t.Code?a("div",[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleViewPart(o)}}},[e._v("查看 ")])],1):"Is_Component"==t.Code||"Is_Component_Status"==t.Code?a("div",[a("span",["True"===o.Is_Component?a("el-tag",{attrs:{type:"danger"}},[e._v("否")]):a("el-tag",{attrs:{type:"success"}},[e._v("是")])],1)]):"Drawing"==t.Code?a("div",["暂无"!==o.Drawing?a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.getComponentInfo(o)}}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" ")]):a("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]):a("div",[a("span",[e._v(e._s(o[t.Code]||"-"))])])]}}],null,!0)})})),a("vxe-column",{attrs:{fixed:"right",align:"left",title:"操作",width:"150","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("详情 ")]),a("el-button",{attrs:{disabled:n.stopFlag,type:"text"},on:{click:function(t){return e.handleEdit(n)}}},[e._v("编辑 ")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleTrack(n)}}},[e._v("轨迹图 ")])]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)])])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":e.selectList,"custom-params":e.customDialogParams,"type-id":e.customParams.TypeId,"type-entity":e.typeEntity,"params-steel":e.treeParamsSteel,"project-id":e.customParams.Project_Id,"sys-project-id":e.customParams.Sys_Project_Id},on:{close:e.handleClose,refresh:e.fetchData,checkPackage:e.handleComponentPack,checkSteelMeans:e.handleSteelMeans,checkModelList:e.handleSteelExport,locationExport:e.locationExport}})],1):e._e(),a("bimdialog",{ref:"dialog",attrs:{"is-auto-split":e.isAutoSplit,"type-entity":e.typeEntity},on:{getData:e.fetchData,getProjectAreaData:e.fetchTreeData}}),a("el-drawer",{attrs:{visible:e.trackDrawer,direction:"rtl",size:"30%","destroy-on-close":"","custom-class":"trackDrawerClass"},on:{"update:visible":function(t){e.trackDrawer=t}},scopedSlots:e._u([{key:"title",fn:function(){return[a("div",[a("span",[e._v(e._s(e.trackDrawerTitle))]),a("span",{staticStyle:{"margin-left":"24px"}},[e._v(e._s(e.trackDrawerData.SteelAmount))])])]},proxy:!0}])},[a("TracePlot",{attrs:{"track-drawer-data":e.trackDrawerData}})],1),a("comDrawdialog",{ref:"comDrawdialogRef",on:{getData:e.fetchData}}),a("modelDrawing",{ref:"modelDrawingRef",attrs:{type:"构件"}})],1)},o=[]},"560b":function(e,t,a){},56618:function(e,t,a){"use strict";a.r(t);var n=a("4561"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"56da":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-drawer",{attrs:{visible:e.drawer,direction:"btt",size:"60%","destroy-on-close":"","before-close":e.handleCloseDrawer},on:{"update:visible":function(t){e.drawer=t},opened:e.renderIframe}},["构件"===e.type?a("div",{staticStyle:{width:"100%",display:"flex","align-items":"center"}},[a("div",{staticStyle:{width:"50%",display:"flex","justify-content":"space-between","align-items":"center","padding-right":"30px"}},[a("div",{staticStyle:{"margin-left":"10px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v(e._s(e.extensionName?"构件模型":"构件图纸"))])]),e.fullscreenid?a("el-button",{on:{click:function(t){return e.fullscreen(0)}}},[e._v("全屏")]):e._e(),e.fullbimid?a("el-button",{on:{click:function(t){return e.fullscreen(1)}}},[e._v("全屏")]):e._e()],1),a("div",{staticStyle:{width:"50%"}},[e.extensionName?a("div",{staticStyle:{"margin-left":"10px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v("构件图纸")])]):e._e()])]):a("div",{staticStyle:{width:"100%",display:"flex"}},[a("div",{staticStyle:{"margin-left":"20px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v(e._s(e.type)+"图纸")])]),e.fileBim?a("el-button",{staticStyle:{"margin-left":"42%"},on:{click:function(t){return e.fullscreen(2)}}},[e._v("全屏")]):e._e()],1),a("iframe",{key:e.iframeKey,staticStyle:{width:"100%",border:"0px",margin:"0",height:"60vh"},attrs:{id:"frame",src:e.iframeUrl}})]),a("el-drawer",{attrs:{visible:e.drawersull,direction:"btt",size:"100%","destroy-on-close":""},on:{"update:visible":function(t){e.drawersull=t}}},[e.templateUrl?a("iframe",{staticStyle:{width:"96%","margin-left":"2%",height:"70vh","margin-top":"2%"},attrs:{id:"fullFrame",src:e.templateUrl,frameborder:"0"}}):e._e()])],1)},o=[]},58185:function(e,t,a){},"586a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddDelayed=j,t.AdjustComponentNum=C,t.AppendImportDeepFile=U,t.AppendImportDeepFiles=E,t.BatchUpateCompProperty=R,t.BatchUpdateComponentImportInfo=ne,t.BatchUpdateComponentProcessInfo=ot,t.BatchUpdatePartProcessInfo=pt,t.BatchUpdateUnitProcessInfo=ct,t.CancelScheduling=i,t.CancelSchedulingBase=s,t.CommitScheduling=g,t.CommitSchedulingBase=y,t.ComponentImportTemplate=$,t.DelDelayed=G,t.DeleteAllComponentWithQuery=z,t.DeleteComponents=W,t.DownAllFiles=V,t.EntityComponentQueryInfo=fe,t.ExportComponentInfo=ee,t.ExportComponentModelInfo=b,t.ExportComponentModelInfoByInstall=v,t.ExportComponentProcessInfo=st,t.ExportComponentSchedulingInfo=te,t.ExportComponentSendOnTimeData=ce,t.ExportComponentTypeStock=we,t.ExportDeepenFullSchedulingInfo=nt,t.ExportFactoryOutputPageList=Oe,t.ExportFactorySchedulingPlan=je,t.ExportFactoryTeamYieldForDay=Re,t.ExportGetFactoryTeamYieldMonth=Fe,t.ExportOutputAblityList=Ue,t.ExportPartProcessInfo=yt,t.ExportProducingKittingPageList=ie,t.ExportProjectProgress=ke,t.ExportProjectTraceCount=ge,t.ExportSupplyPlan=Be,t.ExportThreeBom=qe,t.ExportToScheduleComponentInfo=f,t.ExportUnitProcessInfo=ft,t.FactoryOutput=Ve,t.FactoryOutputPageList=xe,t.GenerateDeepenFileFromDirect=ze,t.GenerateDirectComponent=at,t.GetComponentDeepenFileList=Y,t.GetComponentEntityWithUniqueCode=L,t.GetComponentImportDetailPageList=B,t.GetComponentImportEntity=K,t.GetComponentLeadTime=D,t.GetComponentListByStatus=Se,t.GetComponentLogList=T,t.GetComponentModelList=H,t.GetComponentProcessSummaryInfo=lt,t.GetComponentProductionDetailPageList=me,t.GetComponentProductionSummaryInfo=pe,t.GetComponentProductionTrack=et,t.GetComponentQueryInfo=ue,t.GetComponentQueryPageInfo=se,t.GetComponentSendOnTimeRageList=le,t.GetComponentStockReport=de,t.GetComponentStockStaticsListJson=k,t.GetComponentSummaryInfo=X,t.GetComponentTypeStockPageList=Le,t.GetComponentYieldByStatus=De,t.GetDelayed=A,t.GetFactorySchdulingDayPlanYield=ye,t.GetFactorySchedulingPlanPageList=Ne,t.GetFactorySchedulingPlanSummaryInfo=Ae,t.GetFactoryTeamYieldForDay=Ge,t.GetFactoryTeamYieldForMonth=$e,t.GetImportHistoryPageList=q,t.GetInstallUnitCompPageList=N,t.GetPartDeepenFileList=Z,t.GetPartListWithComponent=Q,t.GetPartProcessWeightList=bt,t.GetProduceTraceCount=be,t.GetProducingKittingPageList=re,t.GetProjectAreaProgressList=Te,t.GetProjectAreaProgressSummary=Ie,t.GetProjectLifeCycleProgress=Ce,t.GetProjectProgressList=Pe,t.GetProjectTraceCount=he,t.GetSchedulingComponentByInstallPageList=O,t.GetSchedulingComponentInstallPageList=w,t.GetSchedulingComponentPageList=m,t.GetSchedulingList=oe,t.GetSchedulingProcessingCompList=ve,t.GetSchedulingProcessingPartList=_e,t.GetSteelCadAndBimId=We,t.GetSupplyPlanPageList=Ee,t.GetTrackList=tt,t.GetUnPreparedList=vt,t.GetUnitProcessWeightList=mt,t.GetWorkTeamOutput=Je,t.GetWorkTeamOutputV3=Ze,t.GetWorkingProcedureSummary=p,t.ImportComponentExtendInfo=Qe,t.ImportComponentModelInfo=ae,t.ImportComponentWithModelInfo=P,t.ImportDeepFile=M,t.ImportModelInfo=_,t.ImportPartTechnologyFile=gt,t.ImportSchedulingInfo=u,t.ImportSchedulingInfoWithAlloct=d,t.ImportSteelTechnologyFile=it,t.ImportUnitTechnologyFile=dt,t.IsImportedComponentsWithoutModel=S,t.MonthlyFactoyOutput=Ye,t.OutputAblityList=Me,t.PartTechnologyImportTemplate=ht,t.ProcessOutput=Ke,t.ProductionDataTemplate=c,t.ProductionModelDataTemplate=I,t.ScheduleComponentAndAllocation=x,t.ScheduleComponentProductionDate=l,t.SteelTechnologyImportTemplate=rt,t.ThreeBomImportTemplate=F,t.UnitTechnologyImportTemplate=ut,t.UpdateComponentPrinted=h,t.UpdatePartAggregateId=Xe,t.UpdateSingleComponentImportInfo=J,t.YearlyFactoryOutput=He;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/Component/CancelScheduling",method:"post",data:r.default.stringify(e)})}function s(e){return(0,o.default)({url:"/PRO/Component/CancelSchedulingBase",method:"post",data:r.default.stringify(e)})}function l(e){return(0,o.default)({url:"/PRO/Component/ScheduleComponentProductionDate",method:"post",data:r.default.stringify(e)})}function c(e){return(0,o.default)({url:"/PRO/Component/ProductionDataTemplate",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/Component/ImportSchedulingInfo",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/Component/ImportSchedulingInfoWithAlloct",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/Component/ExportToScheduleComponentInfo",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/Component/GetSchedulingComponentPageList",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/Component/GetWorkingProcedureSummary",method:"post",data:e})}function h(e){return(0,o.default)({url:"/PRO/Component/UpdateComponentPrinted",method:"post",data:r.default.stringify(e)})}function g(e){return(0,o.default)({url:"/PRO/Component/CommitScheduling",method:"post",data:r.default.stringify(e)})}function y(e){return(0,o.default)({url:"/PRO/Component/CommitSchedulingBase",method:"post",data:r.default.stringify(e)})}function b(e){return(0,o.default)({url:"/PRO/Component/ExportComponentModelInfo",method:"post",data:r.default.stringify(e)})}function v(e){return(0,o.default)({url:"/PRO/Component/ExportComponentModelInfoByInstall",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/Component/ImportModelInfo",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/Component/ImportComponentWithModelInfo",method:"post",data:e})}function S(e){return(0,o.default)({url:"/PRO/Component/IsImportedComponentsWithoutModel",method:"post",data:r.default.stringify(e)})}function C(e){return(0,o.default)({url:"/PRO/Component/AdjustComponentNum",method:"post",data:r.default.stringify(e)})}function I(){return(0,o.default)({url:"/PRO/Component/ProductionModelDataTemplate",method:"post"})}function D(e){return(0,o.default)({url:"/PRO/Component/GetComponentLeadTime",method:"post",data:r.default.stringify(e)})}function k(e){return(0,o.default)({url:"/PRO/Component/GetComponentStockStaticsListJson",method:"post",data:r.default.stringify(e)})}function T(e){return(0,o.default)({url:"/PRO/Component/GetComponentLogList",method:"post",data:r.default.stringify(e)})}function L(e){return(0,o.default)({url:"/PRO/Component/GetComponentEntityWithUniqueCode",method:"post",data:r.default.stringify(e)})}function w(e){return(0,o.default)({url:"/PRO/Component/GetSchedulingComponentInstallPageList",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/Component/ScheduleComponentAndAllocation",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Component/GetSchedulingComponentByInstallPageList",method:"post",data:e})}function N(e){return(0,o.default)({url:"/PRO/Component/GetInstallUnitCompPageList",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/Component/GetDelayed",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/Component/AddDelayed",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/Component/DelDelayed",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/Component/BatchUpateCompProperty",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/Component/ComponentImportTemplate",method:"post",data:e})}function F(e){return(0,o.default)({url:"/PRO/Component/ThreeBomImportTemplate",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/Component/ImportDeepFile",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/Component/AppendImportDeepFile",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/Component/AppendImportDeepFiles",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/Component/GetComponentImportDetailPageList",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/Component/DeleteComponents",method:"post",data:e})}function z(e){return(0,o.default)({url:"/PRO/Component/DeleteAllComponentWithQuery",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/Component/GetImportHistoryPageList",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/Component/DownAllFiles",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PRO/Component/GetComponentModelList",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PRO/Component/GetComponentDeepenFileList",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PRO/Component/GetComponentImportEntity",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PRO/Component/UpdateSingleComponentImportInfo",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PRO/Component/GetPartListWithComponent",method:"post",data:e})}function Z(e){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PRO/Component/GetComponentSummaryInfo",method:"post",data:e})}function ee(e){return(0,o.default)({url:"/PRO/Component/ExportComponentInfo",method:"post",data:e})}function te(e){return(0,o.default)({url:"/PRO/Component/ExportComponentSchedulingInfo",method:"post",data:e})}function ae(e){return(0,o.default)({url:"/PRO/Component/ImportComponentModelInfo",method:"post",data:e})}function ne(e){return(0,o.default)({url:"/PRO/Component/BatchUpdateComponentImportInfo",method:"post",data:e})}function oe(e){return(0,o.default)({url:"/PRO/Component/GetSchedulingList",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/Component/GetProducingKittingPageList",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/Component/ExportProducingKittingPageList",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/Component/GetComponentQueryPageInfo",method:"post",data:e})}function le(e){return(0,o.default)({url:"/PRO/Component/GetComponentSendOnTimeRageList",method:"post",data:e})}function ce(e){return(0,o.default)({url:"/PRO/Component/ExportComponentSendOnTimeData",method:"post",data:e})}function ue(e){return(0,o.default)({url:"/PRO/Component/GetComponentQueryInfo",method:"post",data:e})}function de(e){return(0,o.default)({url:"/PRO/Component/GetComponentStockReport",method:"post",data:e})}function fe(e){return(0,o.default)({url:"/PRO/Component/EntityComponentQueryInfo",method:"post",data:e})}function me(e){return(0,o.default)({url:"/PRO/Component/GetComponentProductionDetailPageList",method:"post",data:e})}function pe(e){return(0,o.default)({url:"/PRO/Component/GetComponentProductionSummaryInfo",method:"post",data:e})}function he(e){return(0,o.default)({url:"/PRO/Component/GetProjectTraceCount",method:"post",data:e})}function ge(e){return(0,o.default)({url:"/PRO/Component/ExportProjectTraceCount",method:"post",data:e})}function ye(e){return(0,o.default)({url:"/PRO/Component/GetFactorySchdulingDayPlanYield",method:"post",data:e})}function be(e){return(0,o.default)({url:"/PRO/Component/GetProduceTraceCount",method:"post",data:e})}function ve(e){return(0,o.default)({url:"/PRO/Component/GetSchedulingProcessingCompList",method:"post",data:e})}function _e(e){return(0,o.default)({url:"/PRO/Component/GetSchedulingProcessingPartList",method:"post",data:e})}function Pe(e){return(0,o.default)({url:"/PRO/ProductionCount/GetProjectProgressList",method:"post",data:e})}function Se(e){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentListByStatus",method:"post",data:e})}function Ce(e){return(0,o.default)({url:"/PRO/ProductionCount/GetProjectLifeCycleProgress",method:"post",data:e})}function Ie(e){return(0,o.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressSummary",method:"post",data:e})}function De(e){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentYieldByStatus",method:"post",data:e})}function ke(e){return(0,o.default)({url:"/PRO/ProductionCount/ExportProjectProgress",method:"post",data:e})}function Te(e){return(0,o.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressList",method:"post",data:e})}function Le(e){return(0,o.default)({url:"/PRO/ProductionCount/GetComponentTypeStockPageList",method:"post",data:e})}function we(e){return(0,o.default)({url:"/PRO/ProductionCount/ExportComponentTypeStock",method:"post",data:e})}function xe(e){return(0,o.default)({url:"/PRO/ProductionTask/FactoryOutputPageList",method:"post",data:e})}function Oe(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportFactoryOutputPageList",method:"post",data:e})}function Ne(e){return(0,o.default)({url:"/PRO/ProductionTask/GetFactorySchedulingPlanPageList",method:"post",data:e})}function Ae(e){return(0,o.default)({url:"/PRO/ProductionTask/GetFactorySchedulingPlanSummaryInfo",method:"post",data:e})}function je(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportFactorySchedulingPlanPageList",method:"post",data:e})}function Ge(e){return(0,o.default)({url:"/PRO/ProductionTask/GetFactoryTeamYieldForDay",method:"post",data:e})}function Re(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportGetFactoryTeamYieldForDay",method:"post",data:e})}function $e(e){return(0,o.default)({url:"/PRO/ProductionTask/GetFactoryTeamYieldForMonth",method:"post",data:e})}function Fe(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportGetFactoryTeamYieldForMonth",method:"post",data:e})}function Me(e){return(0,o.default)({url:"/PRO/ProductionTask/OutputAblityList",method:"post",data:e})}function Ue(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportOutputAblityList",method:"post",data:e})}function Ee(e){return(0,o.default)({url:"/Pro/IntegrationSupplyPlan/GetSupplyPlanPageList",method:"post",data:e})}function Be(e){return(0,o.default)({url:"/Pro/IntegrationSupplyPlan/ExportSupplyPlan",method:"post",data:e})}function We(e){return(0,o.default)({url:"/PRO/Component/GetSteelCadAndBimId",method:"post",data:e})}function ze(e){return(0,o.default)({url:"/PRO/Component/GenerateDeepenFileFromDirect",method:"post",data:e})}function qe(e){return(0,o.default)({url:"/PRO/Component/ExportThreeBom",method:"post",data:e})}function Ve(e){return(0,o.default)({url:"/PRO/ProductionCount/FactoryOutput",method:"post",data:e})}function He(e){return(0,o.default)({url:"/PRO/ProductionCount/YearlyFactoryOutput",method:"post",data:e})}function Ye(e){return(0,o.default)({url:"/PRO/ProductionCount/MonthlyFactoyOutput",method:"post",data:e})}function Ke(e){return(0,o.default)({url:"/PRO/ProductionCount/ProcessOutput",method:"post",data:e})}function Je(e){return(0,o.default)({url:"/PRO/ProductionCount/GetWorkTeamOutput",method:"post",data:e})}function Qe(e){return(0,o.default)({url:"/PRO/component/ImportComponentExtendInfo",method:"post",data:e})}function Ze(e){return(0,o.default)({url:"/PRO/ProductionCount/GetWorkTeamOutputV3",method:"post",data:e})}function Xe(e){return(0,o.default)({url:"/PRO/Part/UpdatePartAggregateId",method:"post",data:e})}function et(e){return(0,o.default)({url:"/PRO/ProductionReport/GetComponentProductionTrack",method:"post",data:e})}function tt(e){return(0,o.default)({url:"/PRO/Part/GetTrackList",method:"post",data:e})}function at(e){return(0,o.default)({url:"/PRO/change/GenerateDirectComponent",method:"post",data:e})}function nt(e){return(0,o.default)({url:"/PRO/component/ExportDeepenFullSchedulingInfo",method:"post",data:e})}function ot(e){return(0,o.default)({url:"/PRO/Component/BatchUpdateComponentProcessInfo",method:"post",data:e})}function rt(e){return(0,o.default)({url:"/PRO/Component/SteelTechnologyImportTemplate",method:"post",data:e})}function it(e){return(0,o.default)({url:"/PRO/Component/ImportSteelTechnologyFile",method:"post",data:e})}function st(e){return(0,o.default)({url:"/PRO/Component/ExportComponentProcessInfo",method:"post",data:e})}function lt(e){return(0,o.default)({url:"/PRO/Component/GetComponentProcessSummaryInfo",method:"post",data:e})}function ct(e){return(0,o.default)({url:"/PRO/Unit/BatchUpdateUnitProcessInfo",method:"post",data:e})}function ut(e){return(0,o.default)({url:"/PRO/Unit/UnitTechnologyImportTemplate",method:"post",data:e})}function dt(e){return(0,o.default)({url:"/PRO/Unit/ImportUnitTechnologyFile",method:"post",data:e})}function ft(e){return(0,o.default)({url:"/PRO/Unit/ExportUnitProcessInfo",method:"post",data:e})}function mt(e){return(0,o.default)({url:"/PRO/Unit/GetUnitProcessWeightList",method:"post",data:e})}function pt(e){return(0,o.default)({url:"/PRO/Part/BatchUpdatePartProcessInfo",method:"post",data:e})}function ht(e){return(0,o.default)({url:"/PRO/Part/PartTechnologyImportTemplate",method:"post",data:e})}function gt(e){return(0,o.default)({url:"/PRO/Part/ImportPartTechnologyFile",method:"post",data:e})}function yt(e){return(0,o.default)({url:"/PRO/Part/ExportPartProcessInfo",method:"post",data:e})}function bt(e){return(0,o.default)({url:"/PRO/Part/GetPartProcessWeightList",method:"post",data:e})}function vt(e){return(0,o.default)({url:"/PRO/Component/GetUnPreparedList",method:"post",data:e})}},"58e2":function(e,t,a){"use strict";a.r(t);var n=a("5313"),o=a("b375");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("5a0c1");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"f58a26ec",null);t["default"]=s.exports},"59c0":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"i-dialog",attrs:{"append-to-body":"",visible:e.visible,title:"编辑包信息",width:"30%"},on:{"update:visible":function(t){e.visible=t},close:e.handleClose}},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包编号",prop:"PackageSN"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.PackageSN,callback:function(t){e.$set(e.form,"PackageSN",t)},expression:"form.PackageSN"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总重量",prop:"AllWeight"}},[a("el-input",{model:{value:e.form.AllWeight,callback:function(t){e.$set(e.form,"AllWeight",t)},expression:"form.AllWeight"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包名称",prop:"PkgNO"}},[a("el-input",{model:{value:e.form.PkgNO,callback:function(t){e.$set(e.form,"PkgNO",t)},expression:"form.PkgNO"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目合同编号",prop:"ContractNo"}},[a("el-input",{model:{value:e.form.ContractNo,callback:function(t){e.$set(e.form,"ContractNo",t)},expression:"form.ContractNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"构件类型",prop:"GoodsName"}},[a("el-input",{model:{value:e.form.GoodsName,callback:function(t){e.$set(e.form,"GoodsName",t)},expression:"form.GoodsName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"收件人",prop:"Addressee"}},[a("el-input",{model:{value:e.form.Addressee,callback:function(t){e.$set(e.form,"Addressee",t)},expression:"form.Addressee"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"体积",prop:"Volume"}},[a("el-input",{model:{value:e.form.Volume,callback:function(t){e.$set(e.form,"Volume",t)},expression:"form.Volume"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"毛重系数",prop:"Gross"}},[a("el-input",{model:{value:e.form.Gross,callback:function(t){e.$set(e.form,"Gross",t)},expression:"form.Gross"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"起运港",prop:"Departure"}},[a("el-input",{model:{value:e.form.Departure,callback:function(t){e.$set(e.form,"Departure",t)},expression:"form.Departure"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"尺寸",prop:"DIM"}},[a("el-input",{model:{value:e.form.DIM,callback:function(t){e.$set(e.form,"DIM",t)},expression:"form.DIM"}})],1)],1),a("el-col",{attrs:{span:24,prop:"Remark"}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:50,"show-word-limit":"",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:e.handleClose}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1)],1)},o=[]},"59f0":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=a("6186"),l=a("3166"),c=a("f2f6");t.default={props:{typeEntity:{type:Object,default:function(){return{}}},paramsSteel:{type:Array,default:function(){return[]}}},data:function(){return{SteelName_Old:"",SteelAmount_Old:"",Project_Id_Old:"",Area_Id_Old:"",InstallUnit_Id_Old:"",isReadOnly:!1,btnLoading:!1,Is_Skip_Production:null,ProducedCount:0,form:{Id:"",SteelName:"",SteelSpec:"",SteelMaterial:"",SteelLength:"",SteelWeight:"",SteelAmount:"",SchedulingNum:"",SteelAllWeight:"",SteelType:"",InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Area_Id:"",AreaPosition:"",Is_Component:"",Create_UserName:"",Create_Date:"",Remark:"",RKPackCount:""},extendField:[],rules:{SteelName:{required:!0,message:"请输入",trigger:"blur"},SteelSpec:{required:!0,message:"请输入",trigger:"blur"},SteelLength:{required:!0,message:"请输入",trigger:"blur"},SteelWeight:{required:!0,message:"请输入",trigger:"blur"},SteelAmount:{required:!0,message:"请输入",trigger:"blur"},SteelAllWeight:{required:!0,message:"请输入",trigger:"blur"},SteelMaterial:{required:!0,message:"请输入",trigger:"blur"},Project_Id:{required:!0,message:"请选择",trigger:"change"},Area_Id:{required:!0,message:"请选择",trigger:"change"},Is_Component:{required:!0,message:"请选择",trigger:"change"}},Is_Component_Data:[{Name:"否",Id:!0},{Name:"是",Id:!1}],show:!1,ProjectNameData:[],SetupPositionData:[],treeParamsSteel:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:this.paramsSteel,props:{children:"Children",label:"Label",value:"Data"}},treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}}}},created:function(){this.getFormProps()},mounted:function(){this.getProjectOption()},methods:{init:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return t.isReadOnly=e.isReadOnly,a.n=1,(0,i.GetComponentImportEntity)({id:e.Id}).then((function(a){if(a.IsSucceed){t.SteelName_Old=a.Data.ImportDetail.SteelName,t.SteelAmount_Old=a.Data.ImportDetail.SteelAmount,t.Project_Id_Old=a.Data.ImportDetail.Project_Id,t.Area_Id_Old=a.Data.ImportDetail.Area_Id,t.InstallUnit_Id_Old=a.Data.ImportDetail.InstallUnit_Id,t.form=a.Data.ImportDetail;var n=a.Data.ImportExtend;n.length>0&&(t.propsList=t.propsList&&t.propsList.concat(n));var o=JSON.parse(JSON.stringify(t.form));n.forEach((function(e){o[e.Code]=e.Value})),t.form=Object.assign({},o),t.extendField=n,t.form.RKPackCount=e.RKPackCount,t.form.SchedulingNum=e.SchedulingNum,t.form.Create_UserName=e.Create_UserName,t.form.Create_Date=e.Create_Date,t.form.TotalGrossWeight=e.TotalGrossWeight,t.Is_Skip_Production=JSON.parse(e.Is_Skip_Production),t.ProducedCount=e.ProducedCount,t.form.SteelAllWeight=Math.round(t.form.SteelWeight*t.form.SteelAmount*1e3)/1e3}else t.$message({message:a.Message,type:"error"})}));case 1:return a.n=2,t.getAreaList();case 2:return a.n=3,t.getInstall();case 3:return a.a(2)}}),a)})))()},getFormProps:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getColumnConfiguration(e.typeEntity.Code);case 1:a=t.v,e.propsList=a,e.show=!0;case 2:return t.a(2)}}),t)})))()},getLabel:function(e){var t=this.getPropsName(e);if(!t)try{this.rules[e].required=!1}catch(a){}return t},calculationAllWeight:function(){this.form.SteelAllWeight=Math.round(this.form.SteelWeight*this.form.SteelAmount*1e3)/1e3,this.form.TotalGrossWeight=Math.round(this.form.GrossWeight*this.form.SteelAmount*1e3)/1e3},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,i.UpdateSingleComponentImportInfo)(e.form).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))},getProjectOption:function(){var e=this;(0,l.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getAreaList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GeAreaTrees)({projectId:e.form.Project_Id}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getInstall:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetInstallUnitPageList)({Area_Id:e.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.$nextTick((function(a){e.SetupPositionData=t.Data.Data})):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},projectChange:function(e){var t,a=this;this.$nextTick((function(){a.form.ProjectName=a.$refs["ProjectName"].selected.currentLabel})),this.form.Sys_Project_Id=null===(t=this.ProjectNameData.find((function(t){return t.Id===e})))||void 0===t?void 0:t.Sys_Project_Id,this.form.Area_Id="",this.form.AreaPosition="",this.treeParamsArea.data=[],this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getAreaList()},areaChange:function(e){this.form.AreaPosition=e.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getInstall()},setupPositionChange:function(){var e=this;this.$nextTick((function(){e.form.SetupPosition=e.$refs["SetupPosition"].selected.currentLabel}))},getColumnConfiguration:function(e){var t=arguments;return(0,r.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_component_page_list",a.n=1,(0,s.GetGridByCode)({code:n+","+e});case 1:return r=a.v,a.a(2,r.Data.ColumnList.filter((function(e){return e.Is_Display})))}}),a)})))()},getPropsName:function(e){var t;return null===(t=this.propsList.find((function(t){return t.Code.toLowerCase()===e.toLowerCase()})))||void 0===t?void 0:t.Display_Name},steelTypeChange:function(e){var t=JSON.parse(e.Code);this.form.Is_Component=1!=t}}}},"5a0c1":function(e,t,a){"use strict";a("350f")},"5a71":function(e,t,a){},"5baa":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose,close:e.handleClose}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v("注意：请先"),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"下载模板",prop:"Template_Type"}},[a("el-radio-group",{on:{input:e.radioChange},model:{value:e.form.Template_Type,callback:function(t){e.$set(e.form,"Template_Type",t)},expression:"form.Template_Type"}},[a("el-radio",{attrs:{label:2}},[e._v("固定模板")]),a("el-radio",{attrs:{label:1}},[e._v("动态模板")])],1)],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),a("el-form-item",{attrs:{label:"区域",prop:"Area_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}})],1),a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),1!==e.form.Type||e.isDynamicTemplate?e._e():a("el-form-item",{attrs:{label:"自动拆分直发件",prop:"Is_Auto_Split"}},[a("el-radio-group",{attrs:{disabled:[!0,!1].includes(e.isAutoSplit)},model:{value:e.form.Is_Auto_Split,callback:function(t){e.$set(e.form,"Is_Auto_Split",t)},expression:"form.Is_Auto_Split"}},[a("el-radio",{attrs:{label:!1}},[e._v("否")]),a("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:2,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},o=[]},"5bfdb":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("main",{staticClass:"main"},[a("div",{staticClass:"left"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{type:"primary",disabled:!e.leftSelectList.length},on:{click:e.handleEdit}},[e._v("编辑包信息")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("加入")]),a("el-button",{attrs:{type:"danger",disabled:!e.leftSelectList.length},on:{click:e.handleDeleteLeft}},[e._v("删除包")])]},proxy:!0},{key:"right",fn:function(){return[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"名称/包编号"},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.fetchLeft},slot:"append"})],1)]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[a("z-left",{ref:"left",attrs:{search:e.search,"type-entity":e.typeEntity},on:{leftClick:e.leftClick,selectList:e.getLeftList,setSelect:e.getLeftList,clearRightData:e.clearRightData}})],1)],1),a("div",{staticClass:"right"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{disabled:!e.rightSelectList.length,type:"danger"},on:{click:e.handleDeleteRight}},[e._v("删除")])]},proxy:!0},{key:"right",fn:function(){return[a("div",{staticStyle:{display:"flex"}},[a("div",[a("label",[e._v("查询条件 "),a("el-input",{staticClass:"input-with-select",staticStyle:{width:"300px"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.search2,callback:function(t){e.search2=t},expression:"search2"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{slot:"prepend",clearable:"",placeholder:"请选择"},slot:"prepend",model:{value:e.select,callback:function(t){e.select=t},expression:"select"}},e._l(e.columnOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1)]),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchRight}},[e._v("查询")])],1)])]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[e.typeEntity.Id?a("z-right",{ref:"right",attrs:{"z-params":{Fuzzy_Search_Col:e.select,Fuzzy_Search:e.search2},"type-entity":e.typeEntity},on:{getColumn:e.getColumn,selectList:e.getRightList,getList:e.fetchRight}}):e._e()],1)],1)]),e.dialogVisible?a("Dialog",{ref:"dialog",attrs:{"dialog-visible":e.dialogVisible},on:{"update:dialogVisible":function(t){e.dialogVisible=t},"update:dialog-visible":function(t){e.dialogVisible=t},refresh:e.fetchLeft,handleClose:e.handleClose}}):e._e()],1)},o=[]},"5caa":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""}})],1)])},o=[]},"5da8":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{width:"100%","text-align":"center","margin-bottom":"20px"}},[a("el-button",{attrs:{type:"success",plain:""},on:{click:e.handleSteelExport}},[e._v("导出构件清单")])],1),a("div",{staticStyle:{width:"100%",height:"130px","text-align":"center","margin-bottom":"20px",display:"flex","justify-content":"center","align-items":"flex-start"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:1,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload",staticStyle:{"font-size":"36px"}}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.importModelFn()}}},[e._v("确 定")])],1)])},o=[]},"5f45":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("2532"),a("159b");var o=n(a("c14f")),r=n(a("1da1")),i=a("e144"),s=a("6186"),l=a("586a"),c=a("2e8a"),u=a("fd31");t.default={props:{typeEntity:{type:Object,default:function(){}},projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},Is_Component_Data:[{Name:"是",Id:"否"},{Name:"否",Id:"是"}],Is_Component:"",value:"",options:[{key:"SteelSpec",label:"规格",type:"string"},{key:"SteelWeight",label:"单重",type:"number"},{key:"SteelLength",label:"长度",type:"number"},{key:"SteelAmount",label:"深化数量",type:"number"},{key:"SteelMaterial",label:"材质 ",type:"string"},{key:"Is_Component",label:"是否直发件",type:"array"},{key:"Remark",label:"备注",type:"string"},{key:"GrossWeight",label:"单毛重",type:"number"}],list:[{id:(0,i.v4)(),val:void 0,key:""}]}},mounted:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getCompTypeTree();case 1:return t.n=2,e.getUserableAttr();case 2:return a=e.options.filter((function(e,t){return t})).map((function(e){return e.key})),t.n=3,e.convertCode(e.typeEntity.Code,a);case 3:n=t.v,e.options=e.options.map((function(e,t){var a;t&&(e.label=null===(a=n.filter((function(e){return e.Is_Display})).find((function(t){return t.Code===e.key})))||void 0===a?void 0:a.Display_Name);return e}));case 4:return t.a(2)}}),t)})))()},methods:{getUserableAttr:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetUserableAttr)({IsComponent:!0}).then((function(t){if(t.IsSucceed){var a=t.Data,n=[];a.forEach((function(e){var t={};t.key=e.Code,t.lable=e.Display_Name,t.type="string",n.push(t)})),e.options=e.options.concat(n)}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getCompTypeTree:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetCompTypeTree)({professional:e.typeEntity.Code}).then((function(t){t.IsSucceed?e.$refs.treeSelect.forEach((function(e){e.treeDataUpdateFun(t.Data)})):(e.$message({message:t.Message,type:"error"}),e.treeData=[])})).finally((function(e){}));case 1:return t.a(2)}}),t)})))()},handleAdd:function(){this.list.push({id:(0,i.v4)(),val:void 0,key:""}),this.getCompTypeTree()},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n,r;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.btnLoading=!0,a={},n=0;case 1:if(!(n<e.list.length)){t.n=4;break}if(r=e.list[n],r.val){t.n=2;break}return("SteelWeight"===r.key||"SteelLength"===r.key||"SteelAmount"===r.key||"GrossWeight"===r.key)&&0===r.val?e.$message({message:"值不能为0",type:"warning"}):e.$message({message:"值不能为空",type:"warning"}),e.btnLoading=!1,t.a(2);case 2:a[r.key]=r.val;case 3:n++,t.n=1;break;case 4:return t.n=5,(0,l.BatchUpdateComponentImportInfo)({Ids:e.selectList.map((function(e){return e.Id})).toString(),EditInfo:a}).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}));case 5:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(a){return(!t.list.map((function(e){return e.key})).includes(a.key)||a.key===e)&&a.label}))},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},init:function(e,t){this.selectList=e},getColumnConfiguration:function(e){var t=arguments;return(0,r.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_component_page_list",a.n=1,(0,s.GetGridByCode)({code:n+","+e});case 1:return r=a.v,a.a(2,r.Data.ColumnList)}}),a)})))()},convertCode:function(e){var t=arguments,a=this;return(0,r.default)((0,o.default)().m((function n(){var r,i,s,l,c,u;return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return r=t.length>1&&void 0!==t[1]?t[1]:[],i=t.length>2?t[2]:void 0,s=a.selectList.filter((function(e){return null!==e.SteelType&&""!==e.SteelType})),l=s.length>0?r.filter((function(e){return"Is_Component"!==e})):r,n.n=1,a.getColumnConfiguration(e,i);case 1:return c=n.v,u=c.filter((function(e){var t=l.map((function(e){return e.toLowerCase()}));return t.includes(e.Code.toLowerCase())})),n.a(2,u)}}),n)})))()},steelTypeChange:function(e){this.Is_Component="true"==e.Code?"是":"否"}}}},6124:function(e,t,a){"use strict";a.r(t);var n=a("5f45"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},6413:function(e,t,a){"use strict";a.r(t);var n=a("3b5f3"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},6521:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1"));t.default={data:function(){return{btnLoading:!1,form:{ProductionConfirm:null},rules:{ProductionConfirm:{required:!0,message:"请选择是否需要生产管理过程",trigger:"change"}},show:!1}},mounted:function(){this.getFormProps()},methods:{getFormProps:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.show=!0;case 1:return t.a(2)}}),t)})))()},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,e.$emit("close",e.form.ProductionConfirm)}))}}}},"65d9":function(e,t,a){"use strict";a("8549")},"663b":function(e,t,a){"use strict";a.r(t);var n=a("176d"),o=a("6c9b");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("97b9");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"f73d62a8",null);t["default"]=s.exports},"66f9":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddArea=ae,t.AddDeepFile=Z,t.AddMonomer=Y,t.AppendImportPartList=se,t.AreaDelete=oe,t.AreaGetEntity=ee,t.AttachmentGetEntities=F,t.ChangeLoad=v,t.CommonImportDeependToComp=Q,t.ContactsAdd=$,t.ContactsDelete=G,t.ContactsEdit=R,t.ContactsGetEntities=A,t.ContactsGetEntity=N,t.ContactsGetTreeList=j,t.DeleteMonomer=J,t.DepartmentAdd=k,t.DepartmentDelete=I,t.DepartmentEdit=D,t.DepartmentGetEntities=C,t.DepartmentGetEntity=S,t.DepartmentGetList=T,t.EditArea=ne,t.EditMonomer=K,t.FileAdd=g,t.FileAddType=f,t.FileDelete=h,t.FileEdit=y,t.FileGetEntity=p,t.FileHistory=_,t.FileMove=b,t.FileTypeAdd=d,t.FileTypeDelete=u,t.FileTypeEdit=m,t.FileTypeGetEntities=r,t.FileTypeGetEntity=i,t.GeAreaTrees=te,t.GetAreaTreeList=X,t.GetDictionaryDetailListByCode=O,t.GetEntitiesByRecordId=M,t.GetEntitiesProject=l,t.GetFileCatalog=s,t.GetFilesByType=c,t.GetGetMonomerList=q,t.GetLoadingFiles=P,t.GetMonomerEntity=V,t.GetProMonomerList=H,t.GetProjectEntity=re,t.GetProjectsflowmanagementAdd=U,t.GetProjectsflowmanagementEdit=E,t.GetProjectsflowmanagementInfo=B,t.GetShortUrl=W,t.ImportDeependToSteel=z,t.ImportPartList=ie,t.SysuserGetUserEntity=w,t.SysuserGetUserList=L,t.UserGroupTree=x;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:e})}function i(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:e})}function s(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetFileCatalog",method:"post",data:e})}function l(e){return(0,o.default)({url:"/SYS/Sys_FileType/GetEntitiesProject",method:"post",data:e})}function c(e){return(0,o.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:e})}function u(e){return(0,o.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:e})}function d(e){return(0,o.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:e})}function f(e){return(0,o.default)({url:"/SYS/Sys_FileType/AddType",method:"post",data:e})}function m(e){return(0,o.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:e})}function p(e){return(0,o.default)({url:"/SYS/Sys_File/GetEntity",method:"post",data:e})}function h(e){return(0,o.default)({url:"/SYS/Sys_File/Delete",method:"post",data:e})}function g(e){return(0,o.default)({url:"/SYS/Sys_File/Add",method:"post",data:e})}function y(e){return(0,o.default)({url:"/SYS/Sys_File/Edit",method:"post",data:e})}function b(e){return(0,o.default)({url:"/SYS/Sys_File/Move",method:"post",data:e})}function v(e){return(0,o.default)({url:"/SYS/Sys_File/IsLoad",method:"post",data:e})}function _(e){return(0,o.default)({url:"/SYS/Sys_File/OldFile",method:"post",data:e})}function P(e){return(0,o.default)({url:"/SYS/Sys_File/GetBIMList",method:"post",data:e})}function S(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetEntity",method:"post",data:e})}function C(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetEntities",method:"post",data:e})}function I(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Delete",method:"post",data:e})}function D(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Edit",method:"post",data:e})}function k(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/Add",method:"post",data:e})}function T(e){return(0,o.default)({url:"/SYS/Sys_Contacts_Department/GetList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/SYS/User/GetUserList",method:"post",data:e})}function w(e){return(0,o.default)({url:"/SYS/User/GetUserEntity",method:"post",data:e})}function x(e){return(0,o.default)({url:"/SYS/UserGroup/GetChildGroupTree",method:"post",data:e})}function O(e){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function N(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetEntity",method:"post",data:e})}function A(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:e})}function j(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:e})}function G(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Delete",method:"post",data:e})}function R(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Edit",method:"post",data:e})}function $(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/Add",method:"post",data:e})}function F(e){return(0,o.default)({url:"/SYS/Sys_File/GetAttachmentEntities",method:"post",data:e})}function M(e){return(0,o.default)({url:"/SYS/Sys_File/GetEntitiesByRecordId",method:"post",data:e})}function U(e){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/Add",method:"post",data:e})}function E(e){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/Edit",method:"post",data:e})}function B(e){return(0,o.default)({url:"/SYS/Sys_Projectsflowmanagement/GetEntity",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PLM/XModel/GetShortUrl",method:"post",data:e})}function z(e){return(0,o.default)({url:"/plm/component/ImportDeependToSteel",method:"get",params:e})}function q(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetGetMonomerList",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetMonomerEntity",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetProMonomerList",method:"post",data:e})}function Y(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/AddMonomer",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/EditMonomer",method:"post",data:e})}function J(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/DeleteMonomer",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/plm/MaterialInfo/CommonImportDeependToComp",method:"get",params:e})}function Z(e){return(0,o.default)({url:"/PLM/Component/AddDeepFile",method:"post",data:e,timeout:18e5})}function X(e){return(0,o.default)({url:"/PLM/Plm_Project_Areas/GetAreaTreeList",method:"post",data:e})}function ee(e){return(0,o.default)({url:"/PRO/Project/GetArea",method:"post",data:e})}function te(e){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function ae(e){return(0,o.default)({url:"/PRO/Project/AddArea",method:"post",data:e})}function ne(e){return(0,o.default)({url:"/PRO/Project/EditArea",method:"post",data:e})}function oe(e){return(0,o.default)({url:"/PRO/Project/Delete",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/Part/ImportPartList",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/Part/AppendImportPartList",method:"post",data:e})}},6737:function(e,t,a){"use strict";a.r(t);var n=a("f85b"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"687a":function(e,t,a){"use strict";a.r(t);var n=a("436e"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},6907:function(e,t,a){"use strict";a.r(t);var n=a("c15b"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"6ab4":function(e,t,a){"use strict";a.r(t);var n=a("ce70"),o=a("aa54");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("8150");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"4dfed603",null);t["default"]=s.exports},"6c02":function(e,t,a){},"6c9b":function(e,t,a){"use strict";a.r(t);var n=a("dd27"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"6cad":function(e,t,a){},"6def":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""}})],1)])},o=[]},7072:function(e,t,a){"use strict";a.r(t);var n=a("5caa"),o=a("03f9");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("e65d");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"0ce6d634",null);t["default"]=s.exports},7291:function(e,t,a){"use strict";a.r(t);var n=a("6521"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},7326:function(e,t,a){"use strict";a("d53d")},"750a":function(e,t,a){},"7a6e":function(e,t,a){},"7a9f":function(e,t,a){},"7aec":function(e,t,a){},"7c57":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block"}},[e.label?a("span",{staticClass:"text",style:"width:"+e.labelWidth},[e._v(e._s(e.label)+" ")]):e._e(),a("el-select",{staticClass:"select",style:"width:"+e.width,attrs:{loading:e.loading,filterable:e.filterable,clearable:e.clearable,disabled:e.disabled,readonly:e.readonly,multiple:e.multiple,placeholder:e.placeholder,"multiple-limit":e.multipleLimit,"popper-append-to-body":e.popperAppendToBody},on:{blur:e.blur,focus:e.focus,change:e.change},model:{value:e.tmpvalue,callback:function(t){e.tmpvalue=t},expression:"tmpvalue"}},["projectlist"===e.thistype?e._l(e.options,(function(t){return a("el-option",{key:t.Sys_Project_Id,attrs:{label:"projectlist"===e.type?t.Name:t.Short_Name,value:t.Sys_Project_Id}})})):e._e(),"managerlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.ProjectManagerId,attrs:{label:e.ProjectManagerName,value:e.ProjectManagerId}})})):e._e(),"majorlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"userlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"authuserlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"factorylist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"arealist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"contacts"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.User_Id,attrs:{label:e.Employor_Name,value:e.User_Id}})})):e._e(),"dictionary"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e()],2)],1)},o=[]},"7e6f1":function(e,t,a){"use strict";a.r(t);var n=a("50b3"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"7f9d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPartTeamProcessAllocation=Ue,t.AdjustSubAssemblyTeamProcessAllocation=Ae,t.AdjustTeamProcessAllocation=Ne,t.ApplyCheck=Ve,t.BatchReceiveTaskFromStock=wt,t.BatchReceiveTransferTask=Fe,t.BatchWithdrawSimplifiedProcessingHistory=ht,t.BeginProcess=W,t.BochuAddTask=qt,t.CancelNestingBill=K,t.CancelSchduling=ve,t.CancelTransferTask=Ee,t.CancelUnitSchduling=at,t.CheckSchduling=We,t.ComponentAllocationWorkingTeam=f,t.ComponentAllocationWorkingTeamBase=m,t.CreateCompTransferBill=z,t.CreateCompTransferByTransferCode=Z,t.CreatePartTransferByPartCodes=J,t.CreatePartTransferByTaskBill=q,t.CreatePartTransferByTransferCode=Q,t.DelSchdulingPlan=_e,t.DelSchdulingPlanById=Pe,t.DeleteNestingResult=vt,t.DownNestingTaskTemplate=ae,t.ExportAllocationComponent=C,t.ExportSimplifiedProcessingHistory=Rt,t.ExportTaskCodeDetails=De,t.ExportTransferCodeDetail=Re,t.GetAllWorkingTeamComponentCount=h,t.GetAllWorkingTeamComponentCountBase=y,t.GetAllWorkingTeamPartCount=T,t.GetAllWorkingTeamPartCountBase=g,t.GetBuildReturnRecordList=ct,t.GetCanSchdulingComps=le,t.GetCanSchdulingPartList=ue,t.GetCheckUserRankList=Ot,t.GetCheckingItemList=At,t.GetCheckingQuestionList=jt,t.GetCompSchdulingInfoDetail=ce,t.GetCompSchdulingPageList=pe,t.GetCompTaskPageList=dt,t.GetCompTaskPartCompletionStock=$t,t.GetComponentPartComplexity=b,t.GetCoordinateProcess=P,t.GetDetailSummaryList=Ut,t.GetDwg=gt,t.GetMonthlyFullCheckProducedData=Gt,t.GetNestingBillBoardPageList=E,t.GetNestingBillDetailList=Mt,t.GetNestingBillPageList=x,t.GetNestingBillTreeList=Ft,t.GetNestingFiles=_t,t.GetNestingMaterialWithPart=Kt,t.GetNestingPartList=St,t.GetNestingResultPageList=yt,t.GetNestingSurplusList=bt,t.GetNestingTaskInfoDetail=$,t.GetPageProcessTransferDetailBase=ie,t.GetPageSchdulingComps=se,t.GetPartPrepareList=Xe,t.GetPartSchdulingCancelHistory=$e,t.GetPartSchdulingInfoDetail=Qe,t.GetPartSchdulingPageList=de,t.GetPartTaskBoard=U,t.GetPartWithParentPageList=Yt,t.GetProcessAllocationComponentBasePageList=S,t.GetProcessAllocationComponentPageList=s,t.GetProcessAllocationPartBasePageList=D,t.GetProcessAllocationPartPageList=I,t.GetProcessPartTransferDetail=ee,t.GetProcessTransferDetail=X,t.GetProcessTransferPageList=B,t.GetSchdulingCancelHistory=we,t.GetSchdulingPageList=me,t.GetSchdulingPartUsePageList=ze,t.GetSchdulingWorkingTeams=ge,t.GetSemiFinishedStock=Ct,t.GetSimplifiedProcessingHistory=mt,t.GetSimplifiedProcessingSummary=pt,t.GetSourceFinishedList=It,t.GetStopList=Jt,t.GetTargetReceiveList=Dt,t.GetTaskPartPrepareList=et,t.GetTeamCompHistory=oe,t.GetTeamPartUseList=qe,t.GetTeamProcessAllocation=Oe,t.GetTeamStockPageList=re,t.GetTeamTaskAllocationPageList=xe,t.GetTeamTaskBoardPageList=F,t.GetTeamTaskDetails=Ie,t.GetTeamTaskPageList=Ce,t.GetTenantFactoryType=ne,t.GetToReceiveTaskDetailList=Tt,t.GetToReceiveTaskList=kt,t.GetTransferCancelDetails=Be,t.GetTransferDetail=Ge,t.GetTransferHistory=je,t.GetTransferPageList=M,t.GetUnitSchdulingInfoDetail=Ze,t.GetUnitSchdulingPageList=he,t.GetWorkingTeamCheckingList=Nt,t.GetWorkingTeamComponentCount=p,t.GetWorkingTeamComponentCountBase=c,t.GetWorkingTeamLoadRealTime=ut,t.GetWorkingTeamPartCount=L,t.GetWorkingTeamPartCountBase=w,t.GetWorkingTeamsPageList=st,t.GetYearlyFullCheckProducedData=xt,t.ImportNestingFiles=Pt,t.ImportNestingInfo=v,t.ImportSchduling=ye,t.ImportThumbnail=Vt,t.ImportUpdateComponentWorkingTeam=i,t.ImportUpdatePartWorkingTeam=k,t.LentakExport=Wt,t.PartsAllocationWorkingTeamBase=N,t.PartsAllocationWrokingTeam=O,t.PartsBatchAllocationWorkingTeamBase=A,t.ProAddQuest=te,t.ProfilesExport=zt,t.ReceiveTaskFromStock=Lt,t.ReceiveTransferBill=H,t.ReceiveTransferTask=Me,t.RevocationComponentAllocation=l,t.SaveChangeZeroComponentRecoil=lt,t.SaveCompSchdulingDraft=He,t.SaveCompTransferBill=V,t.SaveComponentSchedulingWorkshop=nt,t.SaveNestingPartInfo=R,t.SavePartSchdulingDraft=Ye,t.SavePartSchdulingDraftNew=Ke,t.SavePartSchedulingWorkshop=ot,t.SavePartSchedulingWorkshopNew=rt,t.SavePartTransferBill=Y,t.SaveSchdulingDraft=fe,t.SaveSchdulingTask=be,t.SaveSchdulingTaskById=Se,t.SaveUnitSchdulingDraftNew=Je,t.SaveUnitSchedulingWorkshopNew=it,t.SigmaWOLExport=Bt,t.SimplifiedProcessing=ft,t.TeamProcessingByTaskCode=Te,t.TeamTaskProcessing=Le,t.TeamTaskTransfer=ke,t.UpdateBatchCompAllocationWorkingTeamBase=d,t.UpdateBatchPartsAllocationWrokingTeamBase=G,t.UpdateComponentAllocationWorkingTeamBase=u,t.UpdateMachineName=Et,t.UpdatePartsAllocationWorkingTeamBase=j,t.UploadNestingFiles=_,t.WithdrawPicking=Ht,t.WithdrawScheduling=tt;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/ProductionTask/ImportUpdateComponentWorkingTeam",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentPageList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ProductionTask/RevocationComponentAllocation",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCountBase",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ProductionTask/UpdateComponentAllocationWorkingTeamBase",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/ProductionTask/UpdateBatchCompAllocationWorkingTeamBase",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeam",method:"post",data:r.default.stringify(e)})}function m(e){return(0,o.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeamBase",method:"post",data:e})}function p(e){return(0,o.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCount",method:"post",data:r.default.stringify(e)})}function h(e){return(0,o.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCount",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCountBase",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCountBase",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/ProductionTask/GetComponentPartComplexity",method:"post",data:r.default.stringify(e)})}function v(e){return(0,o.default)({url:"/PRO/ProductionTask/ImportNestingInfo",method:"post",data:e})}function _(e){return(0,o.default)({url:"/PRO/ProductionTask/UploadNestingFiles",method:"post",data:e})}function P(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCoordinateProcess",method:"post",data:r.default.stringify(e)})}function S(e){return(0,o.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentBasePageList",method:"post",data:e})}function C(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportAllocationComponent",method:"post",data:e})}function I(e){return(0,o.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartPageList",method:"post",data:e})}function D(e){return(0,o.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartBasePageList",method:"post",data:e})}function k(e){return(0,o.default)({url:"/PRO/ProductionTask/ImportUpdatePartWorkingTeam",method:"post",data:e})}function T(e){return(0,o.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCount",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCount",method:"post",data:r.default.stringify(e)})}function w(e){return(0,o.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCountBase",method:"post",data:r.default.stringify(e)})}function x(e){return(0,o.default)({url:"/PRO/ProductionTask/GetNestingBillPageList",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/ProductionTask/PartsAllocationWrokingTeam",method:"post",data:r.default.stringify(e)})}function N(e){return(0,o.default)({url:"/PRO/ProductionTask/PartsAllocationWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function A(e){return(0,o.default)({url:"/PRO/ProductionTask/PartsBatchAllocationWorkingTeamBase",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/ProductionTask/UpdatePartsAllocationWorkingTeamBase",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/ProductionTask/UpdateBatchPartsAllocationWrokingTeamBase",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/ProductionTask/SaveNestingPartInfo",method:"post",data:r.default.stringify(e)})}function $(e){return(0,o.default)({url:"/PRO/ProductionTask/GetNestingTaskInfoDetail",method:"post",data:r.default.stringify(e)})}function F(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamTaskBoardPageList",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTransferPageList",method:"post",data:e})}function U(e){return(0,o.default)({url:"/PRO/ProductionTask/GetPartTaskBoard",method:"post",data:e})}function E(e){return(0,o.default)({url:"/PRO/ProductionTask/GetNestingBillBoardPageList",method:"post",data:e})}function B(e){return(0,o.default)({url:"/PRO/ProductionTask/GetProcessTransferPageList",method:"post",data:e})}function W(e){return(0,o.default)({url:"/PRO/ProductionTask/BeginProcess",method:"post",data:r.default.stringify(e)})}function z(e){return(0,o.default)({url:"/PRO/ProductionTask/CreateCompTransferBill",method:"post",data:e})}function q(e){return(0,o.default)({url:"/PRO/ProductionTask/CreatePartTransferByTaskBill",method:"post",data:e})}function V(e){return(0,o.default)({url:"/PRO/ProductionTask/SaveCompTransferBill",method:"post",data:e})}function H(e){return(0,o.default)({url:"/PRO/ProductionTask/ReceiveTransferBill",method:"post",data:r.default.stringify(e)})}function Y(e){return(0,o.default)({url:"/PRO/ProductionTask/SavePartTransferBill",method:"post",data:e})}function K(e){return(0,o.default)({url:"/PRO/ProductionTask/CancelNestingBill",method:"post",data:r.default.stringify(e)})}function J(e){return(0,o.default)({url:"/PRO/ProductionTask/CreatePartTransferByPartCodes",method:"post",data:e})}function Q(e){return(0,o.default)({url:"/PRO/ProductionTask/CreatePartTransferByTransferCode",method:"post",data:r.default.stringify(e)})}function Z(e){return(0,o.default)({url:"/PRO/ProductionTask/CreateCompTransferByTransferCode",method:"post",data:e})}function X(e){return(0,o.default)({url:"/PRO/ProductionTask/GetProcessTransferDetail",method:"post",data:r.default.stringify(e)})}function ee(e){return(0,o.default)({url:"/PRO/ProductionTask/GetProcessPartTransferDetail",method:"post",data:r.default.stringify(e)})}function te(e){return(0,o.default)({url:"/PRO/ProductionTask/ProAddQuest",method:"post",data:r.default.stringify(e)})}function ae(e){return(0,o.default)({url:"/PRO/ProductionTask/DownNestingTaskTemplate",method:"post",data:e})}function ne(){return(0,o.default)({url:"/PRO/ProductionTask/GetTenantFactoryType",method:"post"})}function oe(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamCompHistory",method:"post",data:e})}function re(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamStockPageList",method:"post",data:e})}function ie(e){return(0,o.default)({url:"/PRO/ProductionTask/GetPageProcessTransferDetailBase",method:"post",data:e})}function se(e){return(0,o.default)({url:"/PRO/ProductionTask/GetPageSchdulingComps",method:"post",data:e})}function le(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCanSchdulingComps",method:"post",data:e})}function ce(e){return(0,o.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingInfoDetail",method:"post",data:e})}function ue(e){return(0,o.default)({url:"/PRO/nesting/GetCanSchdulingPartList",method:"post",data:e})}function de(e){return(0,o.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingPageList",method:"post",data:e})}function fe(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SaveSchdulingDraft",method:"post",data:e})}function me(e){return(0,o.default)({url:"/PRO/ProductionTask/GetSchdulingPageList",method:"post",data:e})}function pe(e){return(0,o.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingPageList",method:"post",data:e})}function he(e){return(0,o.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingPageList",method:"post",data:e})}function ge(e){return(0,o.default)({url:"/PRO/ProductionTask/GetSchdulingWorkingTeams",method:"post",data:e})}function ye(e){return(0,o.default)({url:"/PRO/ProductionSchduling/ImportCompSchduling",method:"post",timeout:12e5,data:e})}function be(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTask",method:"post",data:e})}function ve(e){return(0,o.default)({url:"/PRO/ProductionTask/CancelSchduling",method:"post",data:e})}function _e(e){return(0,o.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlan",method:"post",data:e})}function Pe(e){return(0,o.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlanById",method:"post",data:e})}function Se(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTaskById",method:"post",data:e,timeout:12e5})}function Ce(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamTaskPageList",method:"post",data:e})}function Ie(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamTaskDetails",method:"post",data:e})}function De(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportTaskCodeDetails",method:"post",data:e})}function ke(e,t){return(0,o.default)({url:"/PRO/ProductionTask/TeamTaskTransfer",method:"post",data:e,params:t})}function Te(e){return(0,o.default)({url:"/PRO/ProductionTask/TeamProcessingByTaskCode",method:"post",data:e})}function Le(e){return(0,o.default)({url:"/PRO/ProductionTask/TeamTaskProcessing",method:"post",data:e})}function we(e){return(0,o.default)({url:"/PRO/ProductionTask/GetSchdulingCancelHistory",method:"post",data:e})}function xe(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamTaskAllocationPageList",method:"post",data:e})}function Oe(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamProcessAllocation",method:"post",data:e})}function Ne(e){return(0,o.default)({url:"/PRO/ProductionSchduling/AdjustCompTeamProcessAllocation",method:"post",data:e})}function Ae(e){return(0,o.default)({url:"/PRO/ProductionSchduling/AdjustSubAssemblyTeamProcessAllocation",method:"post",data:e})}function je(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTransferHistory",method:"post",data:e})}function Ge(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTransferDetail",method:"post",data:e})}function Re(e){return(0,o.default)({url:"/PRO/ProductionTask/ExportTransferCodeDetail",method:"post",data:e})}function $e(e){return(0,o.default)({url:"/PRO/ProductionTask/GetPartSchdulingCancelHistory",method:"post",data:e})}function Fe(e){return(0,o.default)({url:"/PRO/ProductionTask/BatchReceiveTransferTask",method:"post",data:r.default.stringify(e)})}function Me(e){return(0,o.default)({url:"/PRO/ProductionTask/ReceiveTransferTask",method:"post",data:e})}function Ue(e){return(0,o.default)({url:"/PRO/ProductionSchduling/AdjustPartTeamProcessAllocation",method:"post",data:e})}function Ee(e){return(0,o.default)({url:"/PRO/ProductionTask/CancelTransferTask",method:"post",data:e})}function Be(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTransferCancelDetails",method:"post",data:e})}function We(e){return(0,o.default)({url:"/PRO/ProductionTask/CheckSchduling",method:"post",data:r.default.stringify(e)})}function ze(e){return(0,o.default)({url:"/PRO/ProductionTask/GetSchdulingPartUsePageList",method:"post",data:e})}function qe(e){return(0,o.default)({url:"/PRO/ProductionTask/GetTeamPartUseList",method:"post",data:e})}function Ve(e){return(0,o.default)({url:"/PRO/ProductionTask/ApplyCheck",method:"post",data:e})}function He(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SaveCompSchdulingDraft",method:"post",data:e,timeout:12e5})}function Ye(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraft",method:"post",data:e,timeout:12e5})}function Ke(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraftNew",method:"post",data:e,timeout:12e5})}function Je(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SaveUnitSchdulingDraftNew",method:"post",data:e,timeout:12e5})}function Qe(e){return(0,o.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingInfoDetail",method:"post",data:e,timeout:12e5})}function Ze(e){return(0,o.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingInfoDetail",method:"post",data:e,timeout:12e5})}function Xe(e){return(0,o.default)({url:"/PRO/ProductionTask/GetPartPrepareList",method:"post",data:e})}function et(e){return(0,o.default)({url:"/pro/productiontask/GetTaskPartPrepareList",method:"post",data:e})}function tt(e){return(0,o.default)({url:"/PRO/ProductionSchduling/WithdrawScheduling",method:"post",data:e})}function at(e){return(0,o.default)({url:"/PRO/ProductionSchduling/CancelUnitSchduling",method:"post",data:e})}function nt(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SaveComponentSchedulingWorkshop",method:"post",data:e})}function ot(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshop",method:"post",data:e})}function rt(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshopNew",method:"post",data:e})}function it(e){return(0,o.default)({url:"/PRO/ProductionSchduling/SaveUnitSchedulingWorkshopNew",method:"post",data:e})}function st(e){return(0,o.default)({url:"/PRO/ZeroComponentRecoil/GetWorkingTeamsPageList",method:"post",data:e})}function lt(e){return(0,o.default)({url:"/PRO/ZeroComponentRecoil/SaveChangeZeroComponentRecoil",method:"post",data:e})}function ct(e){return(0,o.default)({url:"/PRO/ZeroComponentRecoil/GetBuildReturnRecordList",method:"post",data:e})}function ut(e){return(0,o.default)({url:"/PRO/ProductionTask/GetWorkingTeamLoadRealTime",method:"post",data:e})}function dt(e){return(0,o.default)({url:"/PRO/ProductionTask/GetCompTaskPageList",method:"post",data:e})}function ft(e){return(0,o.default)({url:"/PRO/ProductionTask/SimplifiedProcessing",method:"post",data:e})}function mt(e){return(0,o.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingHistory",method:"post",data:e})}function pt(e){return(0,o.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingSummary",method:"post",data:e})}function ht(e){return(0,o.default)({url:"/PRO/ProductionTask/BatchWithdrawSimplifiedProcessingHistory",method:"post",data:e})}function gt(e){return(0,o.default)({url:"/PRO/ProductionTask/GetDwg",method:"post",data:e})}function yt(e){return(0,o.default)({url:"/PRO/nesting/GetNestingResultPageList",method:"post",data:e})}function bt(e){return(0,o.default)({url:"/PRO/nesting/GetNestingSurplusList",method:"post",data:e})}function vt(e){return(0,o.default)({url:"/PRO/nesting/DeleteNestingResult",method:"post",data:e})}function _t(e){return(0,o.default)({url:"/PRO/nesting/GetNestingFiles",method:"post",data:e})}function Pt(e){return(0,o.default)({url:"/PRO/nesting/Import",method:"post",data:e})}function St(e){return(0,o.default)({url:"/PRO/nesting/GetNestingPartList",method:"post",data:e})}function Ct(e){return(0,o.default)({url:"/PRO/productiontask/GetSemiFinishedStock",method:"post",data:e})}function It(e){return(0,o.default)({url:"/PRO/productiontask/GetSourceFinishedList",method:"post",data:e})}function Dt(e){return(0,o.default)({url:"/PRO/productiontask/GetTargetReceiveList",method:"post",data:e})}function kt(e){return(0,o.default)({url:"/PRO/productiontask/GetToReceiveTaskList",method:"post",data:e})}function Tt(e){return(0,o.default)({url:"/PRO/productiontask/GetToReceiveTaskDetailList",method:"post",data:e})}function Lt(e){return(0,o.default)({url:"/PRO/productiontask/ReceiveTaskFromStock",method:"post",data:e})}function wt(e){return(0,o.default)({url:"/PRO/productiontask/BatchReceiveTaskFromStock",method:"post",data:e})}function xt(e){return(0,o.default)({url:"/PRO/InspectionAnalysis/GetYearlyFullCheckProducedData",method:"post",data:e})}function Ot(e){return(0,o.default)({url:"/PRO/InspectionAnalysis/GetCheckUserRankList",method:"post",data:e})}function Nt(e){return(0,o.default)({url:"/PRO/InspectionAnalysis/GetWorkingTeamCheckingList",method:"post",data:e})}function At(e){return(0,o.default)({url:"/PRO/InspectionAnalysis/GetCheckingItemList",method:"post",data:e})}function jt(e){return(0,o.default)({url:"/PRO/InspectionAnalysis/GetCheckingQuestionList",method:"post",data:e})}function Gt(e){return(0,o.default)({url:"/PRO/InspectionAnalysis/GetMonthlyFullCheckProducedData",method:"post",data:e})}function Rt(e){return(0,o.default)({url:"/PRO/productiontask/ExportSimplifiedProcessingHistory",method:"post",data:e})}function $t(e){return(0,o.default)({url:"/PRO/productiontask/GetCompTaskPartCompletionStock",method:"post",data:e})}function Ft(e){return(0,o.default)({url:"/Pro/NestingBill/GetPageList",method:"post",data:e})}function Mt(e){return(0,o.default)({url:"/Pro/NestingBill/GetDetailList",method:"post",data:e})}function Ut(e){return(0,o.default)({url:"/Pro/NestingBill/GetDetailSummaryList",method:"post",data:e})}function Et(e){return(0,o.default)({url:"/Pro/NestingBill/UpdateMachineName",method:"post",data:e})}function Bt(e){return(0,o.default)({url:"/PRO/CustomUssl/SigmaWOLExport",method:"post",data:e})}function Wt(e){return(0,o.default)({url:"/PRO/CustomUssl/LentakExport",method:"post",data:e})}function zt(e){return(0,o.default)({url:"/PRO/CustomUssl/ProfilesExport",method:"post",data:e})}function qt(e){return(0,o.default)({url:"/PRO/CustomUssl/BochuAddTask",method:"post",data:e})}function Vt(e){return(0,o.default)({url:"/Pro/Nesting/ImportThumbnail",method:"post",data:e})}function Ht(e){return(0,o.default)({url:"/Pro/MaterielPicking/WithdrawPicking",method:"post",data:e})}function Yt(e){return(0,o.default)({url:"/PRO/productiontask/GetPartWithParentPageList",method:"post",data:e})}function Kt(e){return(0,o.default)({url:"/PRO/productiontask/GetNestingMaterialWithPart",method:"post",data:e})}function Jt(e){return(0,o.default)({url:"/PRO/MOC/GetStopList",method:"post",data:e})}},8150:function(e,t,a){"use strict";a("23e9")},"823e":function(e,t,a){"use strict";a.r(t);var n=a("539c"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},8549:function(e,t,a){},"861e":function(e,t,a){},8662:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("span"),e._v("预计交付时间："+e._s(e.Plan_Date)),a("span",[e._v("是否超时："),a("span",[e._v(e._s(1==e.Is_Over_Time?"是":"否"))])])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,border:"",stripe:""}})],1)])},o=[]},8747:function(e,t,a){"use strict";a.r(t);var n=a("3457"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"88a9":function(e,t,a){"use strict";a.r(t);var n=a("eeef"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},8953:function(e,t,a){"use strict";a.r(t);var n=a("dc623"),o=a("f53f");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("2611");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"438cea2e",null);t["default"]=s.exports},8982:function(e,t,a){"use strict";a.r(t);var n=a("3b8d"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"8a0a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var i=a("a5f2");t.default={props:{type:{type:String,default:""},label:{type:String,default:""},labelWidth:{type:String,default:""},value:{type:String|Array,default:""},filterable:{type:Boolean,default:!0},clearable:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},multipleLimit:{type:Number,default:0},placeholder:{type:String,default:"请选择"},width:{type:String,default:"200px"},popperAppendToBody:{type:Boolean,default:!0},customparam:{type:Object,default:function(){}},defaultfirst:{type:Boolean,default:!1},projectId:{type:String,default:""}},data:function(){return{loading:!1,options:[],thistype:"",tmpvalue:this.value,tmpkey:"Id"}},watch:{tmpvalue:function(e){this.$emit("input",e)},value:function(e){this.tmpvalue=e}},mounted:function(){this.getdata()},methods:{change:function(){var e=this;if(this.multiple){var t=[];this.tmpvalue.map((function(a){t.push(e.options.find((function(t){return t[e.tmpkey]===a})))})),this.$emit("change",t)}else this.$emit("change",this.options.find((function(t){return t[e.tmpkey]===e.tmpvalue})))},blur:function(e){this.$emit("blur",e)},focus:function(e){this.$emit("focus",e)},getdata:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.loading=!0,e.thistype=e.type,a=e.type,t.n="projectlist"===a?1:"projectshortlist"===a?3:"managerlist"===a?5:"projectmajorlist"===a?7:"sysmajorlist"===a?9:"userlist"===a?11:"authuserlist"===a?13:"factorylist"===a?15:"arealist"===a?17:"contacts"===a?19:21;break;case 1:return t.n=2,e.getproject();case 2:return e.tmpkey="Sys_Project_Id",t.a(3,23);case 3:return e.thistype="projectlist",e.tmpkey="Sys_Project_Id",t.n=4,e.getproject();case 4:return t.a(3,23);case 5:return e.tmpkey="ProjectManagerId",t.n=6,e.getmanager();case 6:return t.a(3,23);case 7:return e.thistype="majorlist",e.tmpkey="Id",t.n=8,e.getmajor(!1);case 8:return t.a(3,23);case 9:return e.thistype="majorlist",e.tmpkey="Id",t.n=10,e.getmajor(!0);case 10:return t.a(3,23);case 11:return e.tmpkey="Id",t.n=12,e.getuser();case 12:return t.a(3,23);case 13:return e.tmpkey="Id",t.n=14,e.getAuthuser();case 14:return t.a(3,23);case 15:return e.tmpkey="Id",t.n=16,e.getfactory();case 16:return t.a(3,23);case 17:return e.tmpkey="Id",t.n=18,e.getarea();case 18:return t.a(3,23);case 19:return e.tmpkey="Id",t.n=20,e.getContacts();case 20:return t.a(3,23);case 21:return e.thistype="dictionary",e.tmpkey="Id",t.n=22,e.getdictionary(e.type);case 22:return t.a(3,23);case 23:e.defaultfirst&&!e.tmpvalue&&(e.tmpvalue=e.options[0][e.tmpkey]),e.loading=!1;case 24:return t.a(2)}}),t)})))()},getContacts:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,i.GetProjectContacts)({model:{ProjectId:e.projectId,IsSysUser:!0,Platform:~~localStorage.getItem("CurPlatform")}});case 1:a=t.v,a.IsSucceed&&(e.options=a.Data);case 2:return t.a(2)}}),t)})))()}}}},"8b7f":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.getMonomerList},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"安装单元"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}},e._l(e.unitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},o=[]},"8bcc":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af");var o=n(a("c14f")),r=n(a("1da1")),i=a("e144"),s=a("c24f"),l=a("f4f2");t.default={props:{type:{type:String,default:""}},data:function(){return{baseCadUrl:"",drawer:!1,drawersull:!1,iframeKey:"",fullscreenid:"",iframeUrl:"",fullbimid:"",fileBim:"",IsUploadCad:!1,cadRowCode:"",cadRowProjectId:"",extensionName:"",templateUrl:""}},computed:{},created:function(){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},mounted:function(){var e=this;window.addEventListener("message",this.frameListener),this.$once("hook:beforeDestroy",(function(){window.removeEventListener("message",e.frameListener)}))},activated:function(){var e=this;window.addEventListener("message",this.frameListener),this.$once("hook:deactivated",(function(){window.removeEventListener("message",e.frameListener)}))},methods:{getBaseCadUrl:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:t.n=1;break;case 1:return t.n=2,(0,s.getConfigure)({code:"glendale_url"});case 2:a=t.v,e.baseCadUrl=a.Data;case 3:return t.a(2)}}),t)})))()},dwgInit:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getBaseCadUrl();case 1:t.extensionName="",t.extensionName=e.extensionName,t.fileBim=e.fileBim,t.IsUploadCad=e.IsUpload,t.cadRowCode=e.Code,t.cadRowProjectId=e.Sys_Project_Id,t.fileView();case 2:return a.a(2)}}),a)})))()},handleCloseDrawer:function(){this.drawer=!1},fileView:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.drawer=!0,t.n=1,e.$nextTick();case 1:e.iframeKey=(0,i.v4)(),a="构件"===e.type?1:11,e.iframeUrl="".concat(e.baseCadUrl,"?router=1&iframeId=").concat(a,"&baseUrl=").concat((0,l.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id"));case 2:return t.a(2)}}),t)})))()},renderIframe:function(){var e="构件"===this.type?1:11;this.iframeUrl="".concat(this.baseCadUrl,"?router=1&iframeId=").concat(e,"&baseUrl=").concat((0,l.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id")),this.fullscreenid=this.extensionName,this.fullbimid=this.fileBim},fullscreen:function(e){var t=null;t="构件"===this.type?0===e?2:3:13,this.templateUrl="",this.templateUrl="".concat(this.baseCadUrl,"?router=1&iframeId=").concat(t,"&baseUrl=").concat((0,l.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id")),this.drawersull=!0},frameListener:function(e){var t=e.data;"loaded"===t.type&&("1"===t.data.iframeId?document.getElementById("frame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{featureId:this.fullscreenid,cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showModel:!!this.fullscreenid,showCad:this.IsUploadCad}},"*"):"2"===t.data.iframeId?document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{featureId:this.fullscreenid,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showModel:!!this.fullscreenid}},"*"):"3"===t.data.iframeId?document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showCad:this.IsUploadCad}},"*"):"11"===t.data.iframeId?document.getElementById("frame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showCad:this.IsUploadCad,isSubAssembly:"部件"===this.type,isPart:!0}},"*"):"13"===t.data.iframeId&&document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showCad:this.IsUploadCad,isSubAssembly:"部件"===this.type,isPart:!0}},"*"))}}}},"8bde":function(e,t,a){"use strict";a("3d53")},"8c1b":function(e,t,a){},"8c32":function(e,t,a){"use strict";a.r(t);var n=a("2b10"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"8df6":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.handleSelect,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1)},o=[]},"8fd6":function(e,t,a){"use strict";a("7a6e")},"90a5":function(e,t,a){"use strict";a.r(t);var n=a("ed69"),o=a("a70f");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("2b76");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"d4688380",null);t["default"]=s.exports},91178:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{width:"100%","text-align":"center","margin-bottom":"20px"}},[a("el-button",{attrs:{type:"success",plain:""},on:{click:e.handleSteelExport}},[e._v("导出构件清单")])],1),a("div",{staticStyle:{width:"100%",height:"130px","text-align":"center","margin-bottom":"20px",display:"flex","justify-content":"center","align-items":"flex-start"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:1,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload",staticStyle:{"font-size":"36px"}}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.importModelFn()}}},[e._v("确 定")])],1)])},o=[]},"918a":function(e,t,a){"use strict";a.r(t);var n=a("f490"),o=a("6907");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},"942d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"title"},[e._v("非直发件")]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""}})],1),a("div",{staticClass:"title",staticStyle:{"margin-top":"30px"}},[e._v("直发件")]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns_zf,config:e.tbConfig,data:e.tbDataZf,page:e.queryInfo.Page,total:e.total,stripe:""}})],1)])},o=[]},9511:function(e,t,a){"use strict";a.r(t);var n=a("1f71"),o=a("1373");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"6f604e66",null);t["default"]=s.exports},9668:function(e,t,a){"use strict";a.r(t);var n=a("59f0"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"97b9":function(e,t,a){"use strict";a("8c1b")},"97da":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1"));a("d81d"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var i=a("586a"),s=n(a("15ac")),l=n(a("0f97")),c=n(a("6612")),u=a("7f9d");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1,Height:600},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Code",Display_Name:"部件/零件名称",Is_Frozen:!0,Frozen_To:"left",Min_Width:160,Is_Display:!0,Sort:1},{Code:"Spec",Display_Name:"规格",Min_Width:160,Is_Display:!0,Sort:2},{Code:"Length",Display_Name:"长度",Min_Width:160,Is_Display:!0,Sort:3},{Code:"Component_Code",Display_Name:"所属构件",Min_Width:140,Is_Display:!0,Sort:4},{Code:"Num",Display_Name:"需求数量",Min_Width:120,Is_Display:!0,Sort:5},{Code:"SchedulingNum",Display_Name:"排产数量",Min_Width:120,Is_Display:!0,Sort:6},{Code:"Producting_Count",Display_Name:"生产中数量",Min_Width:140,Is_Display:!0,Sort:6},{Code:"Weight",Display_Name:"单重（kg）",Min_Width:120,Is_Display:!0,Sort:7},{Code:"Total_Weight",Display_Name:"总重（kg）",Min_Width:120,Is_Display:!0,Sort:8},{Code:"Is_Main",Display_Name:"是否主零件",Min_Width:140,Is_Display:!0,Sort:10},{Code:"Remark",Display_Name:"备注",Min_Width:160,Is_Display:!0,Sort:11},{Code:"SH",Display_Name:"深化资料",Is_Frozen:!0,Frozen_To:"right",Min_Width:120,Is_Display:!0,Sort:12}],steelTotalNum:0,steelTotalWeight:0,total:0,tbData:[],rowId:0,SteelName:""}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id,this.SteelName=e.SteelName,this.tbLoading=!0,(0,i.GetPartListWithComponent)({id:e.Id}).then(function(){var e=(0,r.default)((0,o.default)().m((function e(a){var n,r;return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:if(!a.IsSucceed){e.n=2;break}return t.tbData=a.Data,n=0,r=0,a.Data.forEach((function(e){1!==e.Part_Grade&&(n+=e.Num,r+=e.Total_Weight)})),t.steelTotalNum=(0,c.default)(n).format("0.[00]"),t.steelTotalWeight=(0,c.default)(r).format("0.[00]"),e.n=1,t.getStopList();case 1:e.n=3;break;case 2:t.$message({message:a.Message,type:"error"});case 3:t.tbLoading=!1;case 4:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}())},getStopList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return a=e.tbData.map((function(e){return{Id:e.Part_Aggregate_Id,Type:0===e.Part_Grade?1:3}})),t.n=1,(0,u.GetStopList)(a).then((function(t){if(t.IsSucceed){var a={};t.Data.forEach((function(e){a[e.Id]=null!==e.Is_Stop})),e.tbData.forEach((function(t){a[t.Part_Aggregate_Id]&&e.$set(t,"stopFlag",a[t.Part_Aggregate_Id])}))}}));case 1:return t.a(2)}}),t)})))()},handleView:function(e){this.$emit("checkSteelMeans",e)}}}},"987c":function(e,t,a){"use strict";a.r(t);var n=a("8df6"),o=a("11cc");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"14053952",null);t["default"]=s.exports},"9a89":function(e,t,a){"use strict";a("2aa92")},"9b25":function(e,t,a){"use strict";a.r(t);var n=a("5bfdb"),o=a("8c32");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("a7ad");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"f7169a7c",null);t["default"]=s.exports},"9b39":function(e,t,a){"use strict";a.r(t);var n=a("59c0"),o=a("d369");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("05d2");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"17de8baf",null);t["default"]=s.exports},"9ba4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=n(a("15ac")),l=a("6f23"),c=n(a("0f97"));t.default={components:{DynamicDataTable:c.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},columns:[{Code:"Scheduling_Status",Display_Name:"构件状态",Align:"center",Is_Display:!0,Sort:1},{Code:"Component_Count",Display_Name:"数量（件）",Align:"center",Is_Display:!0,Sort:2}],tbData:[],Plan_Date:"",Is_Over_Time:!1,rowId:0}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id,this.tbLoading=!0,(0,i.GetSchedulingList)({id:e.Id}).then((function(e){e.IsSucceed?(t.Plan_Date=(0,l.formatDate)(e.Data.Plan_Date,"yyyy-MM-dd"),t.Is_Over_Time=e.Data.Is_Over_Time,t.tbData=e.Data.Scheduling_List):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleView:function(e){return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()}}}},"9bb7":function(e,t,a){"use strict";a.r(t);var n=a("0885"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"9c03":function(e,t,a){"use strict";a.r(t);var n=a("1d8f"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},"9c32":function(e,t,a){},"9c50":function(e,t,a){},"9e65":function(e,t,a){"use strict";a("110d")},"9f5c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.show?a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("项目信息")])]),e.getLabel("ProjectName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("ProjectName"),prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",staticStyle:{width:"100%"},attrs:{disabled:!0,clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("AreaPosition")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("AreaPosition"),prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"w100",attrs:{"tree-params":e.treeParamsArea,disabled:!0,placeholder:"请选择"},on:{"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1):e._e(),e.getLabel("SetupPosition")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SetupPosition"),prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",staticStyle:{width:"100%"},attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0,clearable:"",placeholder:"请选择"},on:{change:e.setupPositionChange},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("基础信息")])]),e.getLabel("SteelName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelName"),prop:"SteelName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1)],1):e._e(),e.getLabel("SteelSpec")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelSpec"),prop:"SteelSpec"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.SteelSpec,callback:function(t){e.$set(e.form,"SteelSpec",t)},expression:"form.SteelSpec"}})],1)],1):e._e(),e.getLabel("SteelWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelWeight"),prop:"SteelWeight"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0,"on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))"},on:{input:e.calculationAllWeight},model:{value:e.form.SteelWeight,callback:function(t){e.$set(e.form,"SteelWeight",t)},expression:"form.SteelWeight"}})],1)],1):e._e(),e.getLabel("SteelAllWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelAllWeight"),prop:"SteelAllWeight"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelAllWeight,callback:function(t){e.$set(e.form,"SteelAllWeight",t)},expression:"form.SteelAllWeight"}})],1)],1):e._e(),e.getLabel("GrossWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("GrossWeight"),prop:"GrossWeight"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0,"on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))"},on:{input:e.calculationAllWeight},model:{value:e.form.GrossWeight,callback:function(t){e.$set(e.form,"GrossWeight",t)},expression:"form.GrossWeight"}})],1)],1):e._e(),e.getLabel("TotalGrossWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("TotalGrossWeight"),prop:"TotalGrossWeight"}},[a("el-input",{attrs:{type:"number",disabled:!0},model:{value:e.form.TotalGrossWeight,callback:function(t){e.$set(e.form,"TotalGrossWeight",t)},expression:"form.TotalGrossWeight"}})],1)],1):e._e(),e.getLabel("SteelLength")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelLength"),prop:"SteelLength"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.SteelLength,callback:function(t){e.$set(e.form,"SteelLength",t)},expression:"form.SteelLength"}})],1)],1):e._e(),e.getLabel("SteelAmount")?a("el-col",{attrs:{span:12}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:e.getLabel("SteelAmount"),prop:"SteelAmount"}},[a("el-input",{staticClass:"cs-num",attrs:{type:"number",disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0||!e.Is_Skip_Production&&e.ProducedCount>0,"on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))"},on:{input:e.calculationAllWeight},model:{value:e.form.SteelAmount,callback:function(t){e.$set(e.form,"SteelAmount",t)},expression:"form.SteelAmount"}})],1)],1):e._e(),e.getLabel("SchedulingNum")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SchedulingNum"),prop:"SchedulingNum"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SchedulingNum,callback:function(t){e.$set(e.form,"SchedulingNum",t)},expression:"form.SchedulingNum"}})],1)],1):e._e(),e.getLabel("SteelType")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelType"),prop:"SteelType"}},[a("el-tree-select",{ref:"treeSelectSteel",attrs:{"tree-params":e.treeParamsSteel,placeholder:"请选择",clearable:"",disabled:!0},on:{"node-click":e.steelTypeChange},model:{value:e.form.SteelType,callback:function(t){e.$set(e.form,"SteelType",t)},expression:"form.SteelType"}})],1)],1):e._e(),e.getLabel("SteelMaterial")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelMaterial"),prop:"SteelMaterial"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.SteelMaterial,callback:function(t){e.$set(e.form,"SteelMaterial",t)},expression:"form.SteelMaterial"}})],1)],1):e._e(),e.getLabel("Is_Component")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Is_Component"),prop:"Is_Component"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!0,clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Component,callback:function(t){e.$set(e.form,"Is_Component",t)},expression:"form.Is_Component"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("Create_UserName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Create_UserName"),prop:"Create_UserName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Create_UserName,callback:function(t){e.$set(e.form,"Create_UserName",t)},expression:"form.Create_UserName"}})],1)],1):e._e(),e.getLabel("Create_Date")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Create_Date"),prop:"Create_Date"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Create_Date,callback:function(t){e.$set(e.form,"Create_Date",t)},expression:"form.Create_Date"}})],1)],1):e._e(),e.getLabel("Remark")?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:e.getLabel("Remark"),prop:"Remark"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1):e._e()],1),e.extendField.length>0?a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("拓展字段")])]),e._l(e.extendField,(function(t){return a("el-col",{key:t.Code,attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel(t.Code),prop:t.Code}},[a("el-input",{attrs:{disabled:e.isReadOnly},model:{value:e.form[t.Code],callback:function(a){e.$set(e.form,t.Code,a)},expression:"form[item.Code]"}})],1)],1)}))],2):e._e(),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),e.isReadOnly?e._e():a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1):e._e()],1)},o=[]},a033:function(e,t,a){"use strict";a.r(t);var n=a("97da"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},a37d:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"加工工厂",prop:"Factory_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"构件批次"}},[a("el-input",{model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}})],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},o=[]},a41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("5530"));a("e9c4"),a("b64b");var r=a("a667");t.default={props:{customParams:{type:Object,default:function(){}}},data:function(){return{form:{Remark:"",Type:""},btnLoading:!1,rules:{Type:[{required:!0,message:"请选择",trigger:"change"}]}}},methods:{handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=JSON.parse(JSON.stringify(e.customParams));delete a.pageInfo;var n={Package:{ProjectID:localStorage.getItem("CurReferenceId"),Remark:e.form.Remark},Steels:[],Search:(0,o.default)((0,o.default)({},a),{},{Type:e.form.Type})};"2"!==e.form.Type?(e.btnLoading=!0,(0,r.BatchAdd)(n).then((function(t){t.IsSucceed?e.$emit("refresh"):e.$message({message:t.Message,type:"error"}),e.$emit("close"),e.btnLoading=!1}))):e.$emit("checkPackage",{data:n,type:1})}))}}}},a428:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.handleSelect,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1)},o=[]},a5f2:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetProjectContacts=r;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/SYS/Sys_Project_Contacts/GetProjectList",method:"post",data:e})}},a5f4:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={props:{dialogVisible:{type:Boolean,default:!1}},data:function(){return{form:{PackageSN:"",AllWeight:"",PkgNO:"",ContractNo:"",GoodsName:"",Addressee:"",Volume:"",Gross:"",Departure:"",DIM:"",Remark:""},btnLoading:!1,visible:!1,rules:{},options:[],listOption:[]}},watch:{dialogVisible:{handler:function(e){this.visible=e},immediate:!0}},methods:{handleClose:function(){this.$emit("update:dialogVisible",!1)},fetchData:function(e){var t=this;(0,n.PackageGetEntity)({packageId:e[0].Id}).then((function(e){e.IsSucceed?t.form=e.Data:t.$message({message:e.Message,type:"error"})})),this.getList()},getList:function(){var e=this;(0,n.GetList)({}).then((function(t){t.IsSucceed?e.listOption=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.btnLoading=!0,(0,n.EditPackage)(this.form).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.handleClose(),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}}},a602:function(e,t,a){"use strict";a.r(t);var n=a("cedf"),o=a("a8de");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("7326");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"1872a24c",null);t["default"]=s.exports},a70f:function(e,t,a){"use strict";a.r(t);var n=a("18304"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},a7ad:function(e,t,a){"use strict";a("f0e8")},a888:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("d565")),r=function(e){e.directive("el-drag-dialog",o.default)};window.Vue&&(window["el-drag-dialog"]=o.default,Vue.use(r)),o.default.install=r;t.default=o.default},a8de:function(e,t,a){"use strict";a.r(t);var n=a("3d8b"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},a90a:function(e,t,a){"use strict";a("38fb")},aa54:function(e,t,a){"use strict";a.r(t);var n=a("1a25"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},ab53:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("5530")),r=a("586a"),i=n(a("15ac")),s=n(a("0f97"));t.default={components:{DynamicDataTable:s.default},mixins:[i.default],data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Working_Process_Name",Display_Name:"构件工序",Is_Display:!0,Sort:1},{Code:"Working_Process_Code",Display_Name:"工序代号",Is_Display:!0,Sort:2},{Code:"Finish_Count",Display_Name:"完成数量(件)",Align:"center",Is_Display:!0,Sort:3},{Code:"Finish_Weight",Display_Name:"完成重量(kg)",Align:"center",Is_Display:!0,Sort:4}],columns_zf:[{Code:"Working_Process_Name",Display_Name:"零件工序",Is_Display:!0,Sort:1},{Code:"Working_Process_Code",Display_Name:"工序代号",Is_Display:!0,Sort:2},{Code:"Finish_Count",Display_Name:"完成数量(件)",Align:"center",Is_Display:!0,Sort:3},{Code:"Finish_Weight",Display_Name:"完成重量(kg)",Align:"center",Is_Display:!0,Sort:4}],total:0,tbData:[],tbDataZf:[]}},mounted:function(){},methods:{init:function(e,t){var a=this;this.tbLoading=!0,(0,r.GetComponentProcessSummaryInfo)((0,o.default)((0,o.default)({},e),{},{Ids:t})).then((function(e){e.IsSucceed?(a.tbData=e.Data.ProcessList,a.tbDataZf=e.Data.ProcessZFList):a.$message({message:e.Message,type:"error"}),a.tbLoading=!1}))}}}},ab9e:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.show?a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("项目信息")])]),e.getLabel("ProjectName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("ProjectName"),prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",staticStyle:{width:"100%"},attrs:{disabled:!0,clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("AreaPosition")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("AreaPosition"),prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"w100",attrs:{"tree-params":e.treeParamsArea,disabled:!0,placeholder:"请选择"},on:{"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1):e._e(),e.getLabel("SetupPosition")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SetupPosition"),prop:"InstallUnit_Id"}},[a("el-select",{ref:"SetupPosition",staticStyle:{width:"100%"},attrs:{disabled:!0,clearable:"",placeholder:"请选择"},on:{change:e.setupPositionChange},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("基础信息")])]),e.getLabel("SteelName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelName"),prop:"SteelName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1)],1):e._e(),e.getLabel("SteelSpec")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelSpec"),prop:"SteelSpec"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.SteelSpec,callback:function(t){e.$set(e.form,"SteelSpec",t)},expression:"form.SteelSpec"}})],1)],1):e._e(),e.getLabel("SteelWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelWeight"),prop:"SteelWeight"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0,"on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))"},on:{input:e.calculationAllWeight},model:{value:e.form.SteelWeight,callback:function(t){e.$set(e.form,"SteelWeight",t)},expression:"form.SteelWeight"}})],1)],1):e._e(),e.getLabel("SteelAllWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelAllWeight"),prop:"SteelAllWeight"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SteelAllWeight,callback:function(t){e.$set(e.form,"SteelAllWeight",t)},expression:"form.SteelAllWeight"}})],1)],1):e._e(),e.getLabel("GrossWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("GrossWeight"),prop:"GrossWeight"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0,"on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))"},on:{input:e.calculationAllWeight},model:{value:e.form.GrossWeight,callback:function(t){e.$set(e.form,"GrossWeight",t)},expression:"form.GrossWeight"}})],1)],1):e._e(),e.getLabel("TotalGrossWeight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("TotalGrossWeight"),prop:"TotalGrossWeight"}},[a("el-input",{attrs:{type:"number",disabled:!0},model:{value:e.form.TotalGrossWeight,callback:function(t){e.$set(e.form,"TotalGrossWeight",t)},expression:"form.TotalGrossWeight"}})],1)],1):e._e(),e.getLabel("SteelLength")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelLength"),prop:"SteelLength"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.SteelLength,callback:function(t){e.$set(e.form,"SteelLength",t)},expression:"form.SteelLength"}})],1)],1):e._e(),e.getLabel("SteelAmount")?a("el-col",{attrs:{span:12}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:e.getLabel("SteelAmount"),prop:"SteelAmount"}},[a("el-input",{staticClass:"cs-num",attrs:{type:"number",disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0||!e.Is_Skip_Production&&e.ProducedCount>0,"on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))"},on:{input:e.calculationAllWeight},model:{value:e.form.SteelAmount,callback:function(t){e.$set(e.form,"SteelAmount",t)},expression:"form.SteelAmount"}})],1)],1):e._e(),e.getLabel("SchedulingNum")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SchedulingNum"),prop:"SchedulingNum"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.SchedulingNum,callback:function(t){e.$set(e.form,"SchedulingNum",t)},expression:"form.SchedulingNum"}})],1)],1):e._e(),e.getLabel("SteelType")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelType"),prop:"SteelType"}},[a("el-tree-select",{ref:"treeSelectSteel",attrs:{"tree-params":e.treeParamsSteel,placeholder:"请选择",clearable:"",disabled:!0},on:{"node-click":e.steelTypeChange},model:{value:e.form.SteelType,callback:function(t){e.$set(e.form,"SteelType",t)},expression:"form.SteelType"}})],1)],1):e._e(),e.getLabel("SteelMaterial")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("SteelMaterial"),prop:"SteelMaterial"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.SteelMaterial,callback:function(t){e.$set(e.form,"SteelMaterial",t)},expression:"form.SteelMaterial"}})],1)],1):e._e(),e.getLabel("Is_Component")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Is_Component"),prop:"Is_Component"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!0,clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Component,callback:function(t){e.$set(e.form,"Is_Component",t)},expression:"form.Is_Component"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("Create_UserName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Create_UserName"),prop:"Create_UserName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Create_UserName,callback:function(t){e.$set(e.form,"Create_UserName",t)},expression:"form.Create_UserName"}})],1)],1):e._e(),e.getLabel("Create_Date")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Create_Date"),prop:"Create_Date"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Create_Date,callback:function(t){e.$set(e.form,"Create_Date",t)},expression:"form.Create_Date"}})],1)],1):e._e(),e.getLabel("Remark")?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:e.getLabel("Remark"),prop:"Remark"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.SchedulingNum>0||e.form.RKPackCount>0},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1):e._e()],1),e.extendField.length>0?a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("拓展字段")])]),e._l(e.extendField,(function(t){return a("el-col",{key:t.Code,attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel(t.Code),prop:t.Code}},[a("el-input",{attrs:{disabled:e.isReadOnly},model:{value:e.form[t.Code],callback:function(a){e.$set(e.form,t.Code,a)},expression:"form[item.Code]"}})],1)],1)}))],2):e._e(),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),e.isReadOnly?e._e():a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1):e._e()],1)},o=[]},acca:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.show?a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"180px"}},[a("el-form-item",{attrs:{label:"是否需要生产管理过程",prop:"ProductionConfirm"}},[a("el-radio-group",{model:{value:e.form.ProductionConfirm,callback:function(t){e.$set(e.form,"ProductionConfirm",t)},expression:"form.ProductionConfirm"}},[a("el-radio",{attrs:{label:!1}},[e._v("是")]),a("el-radio",{attrs:{label:!0}},[e._v("否")])],1)],1),a("div",{staticStyle:{"font-size":"14px",color:"#FF9400",width:"100%","text-align":"center",margin:"10px 0 30px 0"}},[e._v("不需要生产过程的构件导入后直接到待入库，排产数量为 0")]),a("el-form-item",{staticStyle:{"text-align":"right","margin-bottom":"0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1):e._e()],1)},o=[]},b28f:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Glendale=void 0;var n=a("21c4");t.Glendale={baseUrl:"https://glendale-api.bimtk.com",uploadUrl:"/api/app/model/upload-file",externalUploadUrl:"/api/app/model/transcode-file",token:"cbe8c300dfb0280d71bf6546087c6d2fa49260085f53fca86420a895151316b47ecc664d935a21368f69b5af976ee234c74ec744e94e92ff68302806829f371f258c47f45b34485a2edb350010e2f42cd5b388af45c6b0706420a895151316b43747e3055c8e840016a6f6286831e20c2c8552e87ca11ef2ff11ee90514f34c99862338911c1f8c3484b6adefea47c1a13904f8abbaa3690edd3506dd71556198a94867b381ae88ecd6668e8ca6b6d286dce7e5d799b0a175d5afe5554a43322fe0821394937a5ec4e7b1a14ad02799235303fcd390fe68b88e6e56cb30380a7e82ecf81d1cced49bf42e57a167f6571a4f422a627b1b73db6c19a148ad18fb8598216e137795cc77fcba7ce8189fbc2d0c629f88d9f278b9f7189b6679a6b403cc9af099bcccfada205cfe438acf9ce55f7b7caa9ac5f713013404ec41c18c9cc748e652ce16fe7061c32d550edfda330c1555e0c08e51f698f96b91615d8220acd9cb22f32e975867209ea42bb96fb24680a43f9a8e0d8ff0545d55d0ee8954fdc9df330134f94052efc320a8c1a143e939c0e3b1ad476cf3630a2823a9a2d90e800d9b0be613ba7550ed054853e32510f8a6ea5c1dff9c3d61f7cf5f649cb25f0dbad9f7f66e32df742996903a326a7550ed054853e32510f8a6ea5c1dff98b6284e2288790dacf3630a2823a9a2d22bd09a00b213aae3866768ae2f24dde59f3f21ab1f820a9f37cc8bbee6776a194ceb5e6731e17b2badc7a1defb9170dcd8bfe92fe65c7f7433660bbb3cf8a0c1054f377da280222753151533a277044a238855226f63007d9dfdb117379ef800f473f1cc81a2741888bce192514399191b48af868eda4878aa9a449dea5d6a83212c061951e07b453e3e8a5ebf625bfafafd0506174e868046aea1d307c2532d9dfdb117379ef807362257cdfe5a976546538785d7cc0b4cd8bfe92fe65c7f7aed0269fce94ac324ca2ef020dc56bee92846bcbf7f17fbb041466e1708a43962f0f529858b2645ef09b5a6429c7bb718647f5c40fce1c35cc43571d292f54baa4596f82cf86665b367e01045009c18a2b543696b93a5bd44ca8168b525235150ffadf3f628aa7e8a20b9e74d76bd3a1e26b026e5297005a780f011db11ddb55fbabdc0f876c1cdfa5ef7db74bbc52e19c6396ffcf10044da26ecd334722494fc515b205e9624a4468762f63f0143583d1eeaaa85ff95ee50d8f953f44140ab40f29957f914b224124c1472866c2b48ef1d75032e402346dd66bd69478590684d8534c4d15a3c1bd672c07d671e1eaee4318e4f4b8a3801a663d5782e42d99c72c45cc2fe30743fe3c6aa30110f046086ef85cfad28d045a8156b326e2f4461369f266ad62ac2edf64d1767ac6ea3d83c9ba33a4517541406791c384c597539cc1ffcdbe8f3563a16e8a817067c3f375a338711971acc3b1d020470e76e4265682b70b4bded8768f01225025794367ef5cff14a517c70d3f412eb1403a714dcc9a4218257c98e433bcc9baedce6c26ebfb778919929fc8e65e3f8253971ef20b10b7b200fbc7c3d3f71e19f74e660313cf3630a2823a9a2dc2a17ec077fd21bf5125cdd7384aad1b3050717025af01dcfac91bea061f4ff4d8636a404c93c92c9ad3fe161773254f285f618f84d5ac97e6cd674b6676107fc8b14b44ceeed441af4b194f05a956194bd7fd5f13f06806",options:{InitiatingUser:"admin",UniqueCode:"20228_29",Priority:"202",ModelUploadUrl:"".concat((0,n.getTenantId)()),OtherInfo:"",ConfigJson:{style:1,zGrid:1,viewStyle:0,drawing:0,accuracy:3,parametric:1,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:1,unitRatio:.001,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:1,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:1}},optionsCad:{Name:"图纸12月30",InitiatingUser:"admin",Priority:"202",UniqueCode:"test001",IsCAD:!0,ModelUploadUrl:"".concat((0,n.getTenantId)()),ConfigJson:{style:1,zGrid:0,viewStyle:0,drawing:0,accuracy:5,parametric:0,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:0,unitRatio:1,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:0,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:0,faceNumLimit:1e6}}}},b2e8:function(e,t,a){"use strict";a.r(t);var n=a("1389"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},b300:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={props:{dialogVisible:{type:Boolean,default:!1}},data:function(){return{form:{PackageSN:"",AllWeight:"",PkgNO:"",ContractNo:"",GoodsName:"",Addressee:"",Volume:"",Gross:"",Departure:"",DIM:"",Remark:""},btnLoading:!1,visible:!1,rules:{},options:[],listOption:[]}},watch:{dialogVisible:{handler:function(e){this.visible=e},immediate:!0}},methods:{handleClose:function(){this.$emit("update:dialogVisible",!1)},fetchData:function(e){var t=this;(0,n.PackageGetEntity)({packageId:e[0].Id}).then((function(e){e.IsSucceed?t.form=e.Data:t.$message({message:e.Message,type:"error"})})),this.getList()},getList:function(){var e=this;(0,n.GetList)({}).then((function(t){t.IsSucceed?e.listOption=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.btnLoading=!0,(0,n.EditPackage)(this.form).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.handleClose(),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}}},b375:function(e,t,a){"use strict";a.r(t);var n=a("4408"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},b411:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var o=n(a("c14f")),r=n(a("1da1")),i=a("586a"),s=a("6186"),l=a("3166"),c=a("f2f6");t.default={props:{typeEntity:{type:Object,default:function(){return{}}},paramsSteel:{type:Array,default:function(){return[]}}},data:function(){return{SteelName_Old:"",SteelAmount_Old:"",Project_Id_Old:"",Area_Id_Old:"",InstallUnit_Id_Old:"",isReadOnly:!1,btnLoading:!1,Is_Skip_Production:null,ProducedCount:0,form:{Id:"",SteelName:"",SteelSpec:"",SteelMaterial:"",SteelLength:"",SteelWeight:"",SteelAmount:"",SchedulingNum:"",SteelAllWeight:"",SteelType:"",InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Area_Id:"",AreaPosition:"",Is_Component:"",Create_UserName:"",Create_Date:"",Remark:"",RKPackCount:""},extendField:[],rules:{SteelName:{required:!0,message:"请输入",trigger:"blur"},SteelSpec:{required:!0,message:"请输入",trigger:"blur"},SteelLength:{required:!0,message:"请输入",trigger:"blur"},SteelWeight:{required:!0,message:"请输入",trigger:"blur"},SteelAmount:{required:!0,message:"请输入",trigger:"blur"},SteelAllWeight:{required:!0,message:"请输入",trigger:"blur"},SteelMaterial:{required:!0,message:"请输入",trigger:"blur"},Project_Id:{required:!0,message:"请选择",trigger:"change"},Area_Id:{required:!0,message:"请选择",trigger:"change"},Is_Component:{required:!0,message:"请选择",trigger:"change"}},Is_Component_Data:[{Name:"否",Id:!0},{Name:"是",Id:!1}],show:!1,ProjectNameData:[],SetupPositionData:[],treeParamsSteel:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:this.paramsSteel,props:{children:"Children",label:"Label",value:"Data"}},treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}}}},created:function(){this.getFormProps()},mounted:function(){this.getProjectOption()},methods:{init:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return t.isReadOnly=e.isReadOnly,a.n=1,(0,i.GetComponentImportEntity)({id:e.Id}).then((function(a){if(a.IsSucceed){t.SteelName_Old=a.Data.ImportDetail.SteelName,t.SteelAmount_Old=a.Data.ImportDetail.SteelAmount,t.Project_Id_Old=a.Data.ImportDetail.Project_Id,t.Area_Id_Old=a.Data.ImportDetail.Area_Id,t.InstallUnit_Id_Old=a.Data.ImportDetail.InstallUnit_Id,t.form=a.Data.ImportDetail;var n=a.Data.ImportExtend;n.length>0&&(t.propsList=t.propsList&&t.propsList.concat(n));var o=JSON.parse(JSON.stringify(t.form));n.forEach((function(e){o[e.Code]=e.Value})),t.form=Object.assign({},o),t.extendField=n,t.form.RKPackCount=e.RKPackCount,t.form.SchedulingNum=e.SchedulingNum,t.form.Create_UserName=e.Create_UserName,t.form.Create_Date=e.Create_Date,t.form.TotalGrossWeight=e.TotalGrossWeight,t.Is_Skip_Production=JSON.parse(e.Is_Skip_Production),t.ProducedCount=e.ProducedCount,t.form.SteelAllWeight=Math.round(t.form.SteelWeight*t.form.SteelAmount*1e3)/1e3}else t.$message({message:a.Message,type:"error"})}));case 1:return a.n=2,t.getAreaList();case 2:return a.n=3,t.getInstall();case 3:return a.a(2)}}),a)})))()},getFormProps:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getColumnConfiguration(e.typeEntity.Code);case 1:a=t.v,e.propsList=a,e.show=!0;case 2:return t.a(2)}}),t)})))()},getLabel:function(e){var t=this.getPropsName(e);if(!t)try{this.rules[e].required=!1}catch(a){}return t},calculationAllWeight:function(){this.form.SteelAllWeight=Math.round(this.form.SteelWeight*this.form.SteelAmount*1e3)/1e3,this.form.TotalGrossWeight=Math.round(this.form.GrossWeight*this.form.SteelAmount*1e3)/1e3},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,i.UpdateSingleComponentImportInfo)(e.form).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))},getProjectOption:function(){var e=this;(0,l.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getAreaList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GeAreaTrees)({projectId:e.form.Project_Id}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getInstall:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetInstallUnitPageList)({Area_Id:e.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.$nextTick((function(a){e.SetupPositionData=t.Data.Data})):e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},projectChange:function(e){var t,a=this;this.$nextTick((function(){a.form.ProjectName=a.$refs["ProjectName"].selected.currentLabel})),this.form.Sys_Project_Id=null===(t=this.ProjectNameData.find((function(t){return t.Id===e})))||void 0===t?void 0:t.Sys_Project_Id,this.form.Area_Id="",this.form.AreaPosition="",this.treeParamsArea.data=[],this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getAreaList()},areaChange:function(e){this.form.AreaPosition=e.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getInstall()},setupPositionChange:function(){var e=this;this.$nextTick((function(){e.form.SetupPosition=e.$refs["SetupPosition"].selected.currentLabel}))},getColumnConfiguration:function(e){var t=arguments;return(0,r.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_component_page_list",a.n=1,(0,s.GetGridByCode)({code:n+","+e});case 1:return r=a.v,a.a(2,r.Data.ColumnList.filter((function(e){return e.Is_Display})))}}),a)})))()},getPropsName:function(e){var t;return null===(t=this.propsList.find((function(t){return t.Code.toLowerCase()===e.toLowerCase()})))||void 0===t?void 0:t.Display_Name},steelTypeChange:function(e){var t=JSON.parse(e.Code);this.form.Is_Component=1!=t}}}},b5b07:function(e,t,a){"use strict";a("560b")},b639:function(e,t,a){"use strict";a.r(t);var n=a("c05f"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},b8ae:function(e,t,a){"use strict";a.r(t);var n=a("acca"),o=a("7291");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},b9ca:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7");var o=n(a("bbc2")),r=a("586a");t.default={components:{OSSUpload:o.default},props:{typeEntity:{type:Object,default:function(){}}},data:function(){return{btnLoading:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,attachments:[],projectId:""}},computed:{},created:function(){},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},handleChange:function(){},beforeUpload:function(e){this.curFile=e,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=0;this.fileList.filter((function(t,n){t.name===e.name&&(a=n)})),this.fileList.splice(a,1),this.attachments.splice(a,1),this.btnLoading=!t.every((function(e){return"success"===e.status}))},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]}),this.btnLoading=!a.every((function(e){return"success"===e.status}))},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.fileList=[],this.dialogVisible=!1}catch(e){}},importModelFn:function(){var e=this;if(0==this.fileList.length)return this.$message({message:"请上传文件",type:"warning"}),!1;(0,r.ImportComponentExtendInfo)({uploadUrl:this.attachments[0].File_Url}).then((function(t){t.IsSucceed?(e.$message({message:"导入成功",type:"success"}),e.$emit("close")):e.$message({message:t.Message,type:"error"})}))},handleSteelExport:function(e){this.$emit("locationExport")}}}},b9eb:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2"),a("d3b7"),a("25f0");var o=n(a("4360"));function r(e,t){var a=t.value,n=o.default.getters&&o.default.getters.sysUseType;if("[object Number]"!==Object.prototype.toString.call(a)||"number"!==typeof n)throw new Error('need sysUseType! Like v-sys-use-type="123"');a!==n&&e.parentNode&&e.parentNode.removeChild(e)}t.default={inserted:function(e,t){r(e,t)},update:function(e,t){r(e,t)}}},bba0:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticStyle:{width:"100%","text-align":"center","margin-bottom":"20px"}},[a("el-button",{attrs:{type:"success",plain:""},on:{click:function(t){return e.handleSteelExport(0)}}},[e._v("导出未绑定的构件")]),a("el-button",{attrs:{type:"success",plain:""},on:{click:function(t){return e.handleSteelExport(1)}}},[e._v("导出已绑定的构件")])],1),a("div",{staticStyle:{width:"100%",height:"130px","text-align":"center","margin-bottom":"20px",display:"flex","justify-content":"center","align-items":"flex-start"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:1,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload",staticStyle:{"font-size":"36px"}}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.importModelFn()}}},[e._v("确 定")])],1)])},o=[]},bc54:function(e,t,a){"use strict";a.r(t);var n=a("9ba4"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},bcd7:function(e,t,a){"use strict";a.r(t);var n=a("4b7d"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},bd51:function(e,t,a){"use strict";a("ccc2")},bf8b:function(e,t,a){"use strict";a.r(t);var n=a("7c57"),o=a("cf8f");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("50d9");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},c05f:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7"),a("ac1f"),a("3ca3"),a("466d"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var o=n(a("c14f")),r=n(a("1da1")),i=n(a("5530")),s=n(a("bbc2")),l=a("586a"),c=a("ed08"),u=a("2f62"),d={Id:"",Is_Auto_Split:void 0,Doc_Catelog:"",Doc_Type:"",Project_Name:"",Project_Id:"",Sys_Project_Id:"",Area_Name:"",Area_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",IsChanged:!1,Is_Load:!1,Doc_File:"",ishistory:!0,Is_Skip_Production:!1,ProfessionalCode:"",Type:0,Template_Type:2};t.default={components:{OSSUpload:s.default},props:{typeEntity:{type:Object,default:function(){}},isAutoSplit:{type:[Boolean,void 0],default:void 0}},computed:(0,i.default)({},(0,u.mapGetters)("tenant",["isVersionFour"])),data:function(){return{isDynamicTemplate:!1,btnLoading:!1,type:"",areaType:2,allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,form:(0,i.default)({},d),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}]},fileType:"",curFile:"",bimvizId:"",isDeep:!1,projectId:"",isSHQD:"",command:"cover"}},watch:{isAutoSplit:function(e,t){this.$set(this.form,"Is_Auto_Split",e)}},mounted:function(){this.$set(this.form,"Is_Auto_Split",this.isAutoSplit)},created:function(){this.fileType=this.$route.name},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},getTemplate:function(){var e=this,t={ProfessionalCode:this.form.ProfessionalCode,Type:this.form.Type};t.Template_Type=this.form.Template_Type,(0,l.ThreeBomImportTemplate)({}).then((function(t){t.IsSucceed?window.open((0,c.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"})}))},getBlob:function(e){return new Promise((function(t){var a=new XMLHttpRequest;a.open("GET",e,!0),a.responseType="blob",a.onload=function(){200===a.status&&t(a.response)},a.send()}))},saveAs:function(e,t){var a=document.createElement("a");a.href=window.URL.createObjectURL(e),a.download=t,a.click()},downFile:function(e){var t=this;this.getBlob(e).then((function(e){t.saveAs(e,"信用权证使用导入模板件名.xlsx")}))},handleChange:function(e,t){this.fileList=t.slice(-1),t.length>1&&(this.attachments.splice(-1),this.form.Doc_File="",this.form.Doc_Content="",this.form.Doc_Title="")},beforeUpload:function(e){this.curFile=e,this.loading=!0,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this,n=0;this.fileList.filter((function(t,a){t.name===e.name&&(n=a)})),this.fileList.splice(n,1),this.attachments.splice(n,1),this.form.Doc_File=this.form.Doc_File.replace(e.name,""),this.form.Doc_Content=this.form.Doc_File.replace(e.name,""),this.form.Doc_Title=this.form.Doc_File.replace(e.name,""),this.loading=!t.every((function(e){return"success"===e.status})),setTimeout((function(){a.btnLoading=!t.every((function(e){return"success"===e.status}))}),1e3)},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){var n=this;this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]});var o=this.form.Doc_Title+(this.form.Doc_Title?",":"")+e.Data.split("*")[3];this.form.Doc_Title=o.substring(0,o.lastIndexOf(".")),this.form.Doc_Content=this.form.Doc_Title,this.form.Doc_File=this.form.Doc_File+(this.form.Doc_File?",":"")+e.Data.split("*")[3],this.loading=!a.every((function(e){return"success"===e.status})),setTimeout((function(){n.btnLoading=!a.every((function(e){return"success"===e.status}))}),1e3)},handleOpen:function(e,t,a){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0,i=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"cover",s=arguments.length>8?arguments[8]:void 0;this.projectId=o,this.isDeep=n,this.form=Object.assign(this.form,d),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.isSHQD=t.isSHQD,this.form.ProfessionalCode=t.Code,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.form.Type=r,this.command=i,this.form.Project_Name=s.Project_Name,this.form.Sys_Project_Id=s.Sys_Project_Id,this.form.Area_Name=s.Area_Name,this.form.Area_Id=s.Area_Id,this.isDynamicTemplate=!1,this.Template_Type=2,"add"===this.type&&(this.fileList=[],this.form.Id=""),"cover"===this.command?(this.title="覆盖文件",this.form.ImportType=2):"add"===this.command?(this.title="新增文件",this.form.ImportType=1):"halfcover"===this.command&&(this.title="部分覆盖导入",this.form.ImportType=3),this.$set(this.form,"Is_Auto_Split",this.isAutoSplit)},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.btnLoading=!1,this.loading=!1,this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$refs["form"].validate(function(){var a=(0,r.default)((0,o.default)().m((function a(n){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:if(!n){a.n=1;break}e.loading=!0,e.btnLoading=!0,e.updateInfo(t),a.n=2;break;case 1:return e.$message({message:"请将表单填写完整",type:"warning"}),a.a(2,!1);case 2:return a.a(2)}}),a)})));return function(e){return a.apply(this,arguments)}}())},updateInfo:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:n=(0,i.default)((0,i.default)({},t.form),{},{IsOk:e}),t.submitAdd(n);case 1:return a.a(2)}}),a)})))()},submitCoverAdd:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,a.n=1,(0,l.AppendImportDeepFiles)((0,i.default)((0,i.default)({},e),{},{AttachmentList:t.attachments}));case 1:if(n=a.v,!n.IsSucceed){a.n=3;break}return t.$message({message:"保存成功",type:"success"}),a.n=2,t.updatePartAggregateId();case 2:t.$emit("getData",t.form.Doc_Type),t.$emit("getProjectAreaData"),t.handleClose(),a.n=4;break;case 3:n.Data&&window.open((0,c.combineURL)(t.$baseUrl,n.Data),"_blank"),t.$message.error(n.Message);case 4:a.n=6;break;case 5:a.p=5,a.v,t.$message.error("保存失败");case 6:return a.p=6,t.loading=!1,t.btnLoading=!1,a.f(6);case 7:return a.a(2)}}),a,null,[[0,5,6,7]])})))()},submitAdd:function(e){var t=this;return(0,r.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return a.p=0,n=(0,i.default)({},e),2===t.areaType&&(n.Area_Id=void 0,n.Area_Name=void 0),a.n=1,(0,l.AppendImportDeepFiles)((0,i.default)((0,i.default)({},n),{},{AttachmentList:t.attachments}));case 1:if(r=a.v,!r.IsSucceed){a.n=5;break}if(r.Data){a.n=3;break}return t.$message({message:"保存成功",type:"success"}),a.n=2,t.updatePartAggregateId();case 2:t.$emit("getData",t.form.Doc_Type),t.$emit("getProjectAreaData"),t.handleClose(),a.n=4;break;case 3:t.$confirm(r.Data,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.handleSubmit(!0)})).catch((function(){t.$message({type:"info",message:"已取消"})}));case 4:a.n=6;break;case 5:r.Data&&window.open((0,c.combineURL)(t.$baseUrl,r.Data),"_blank"),t.$message.error(r.Message);case 6:a.n=8;break;case 7:a.p=7,a.v,t.$message.error("保存失败");case 8:return a.p=8,t.loading=!1,t.btnLoading=!1,a.f(8);case 9:return a.a(2)}}),a,null,[[0,7,8,9]])})))()},getSplitInfo:function(){var e=this,t=this.form,a=t.ProfessionalCode,n=t.Type,o=t.Is_Skip_Production,r=t.Sys_Project_Id,i=t.Area_Id,s={ProfessionalCode:a,Type:n,Is_Skip_Production:o,Sys_Project_Id:r,Area_Id:i,AttachmentList:this.attachments,Is_Auto_Split:!0};(0,l.GenerateDeepenFileFromDirect)(s).then((function(t){t.IsSucceed?e.open(t.Data):e.$message({message:t.Message,type:"error"})}))},updatePartAggregateId:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.UpdatePartAggregateId)({AreaId:e.form.Area_Id}).then((function(t){t.IsSucceed||e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},open:function(e){var t=this,a=this.$createElement,n="",s=e.match(/\/([^/]+\.xls)$/);s&&(n=s[1]);var l=(0,i.default)({},this.form);this.$msgbox({title:"提示",message:a("div",null,[a("div",null,"清单已拆分完成, 是否确定导入?"),a("a",{attrs:{href:(0,c.combineURL)(this.$baseUrl,e),target:"_blank",style:"color: #298DFF"}},n)]),showCancelButton:!0,confirmButtonText:"确定",cancelButtonText:"取消",beforeClose:function(){var e=(0,r.default)((0,o.default)().m((function e(a,n,r){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:if("confirm"!==a){e.n=2;break}return n.confirmButtonLoading=!0,n.confirmButtonText="提交...",e.n=1,t.submitAdd(l);case 1:r(),setTimeout((function(){n.confirmButtonLoading=!1}),300),e.n=3;break;case 2:t.loading=!1,t.btnLoading=!1,r();case 3:return e.a(2)}}),e)})));function a(t,a,n){return e.apply(this,arguments)}return a}()}).then((function(e){}))},radioChange:function(e){1===e?(this.isDynamicTemplate=!0,this.form.Is_Auto_Split=void 0):this.isDynamicTemplate=!1}}}},c108:function(e,t,a){"use strict";a("9c32")},c15b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("c14f")),r=n(a("1da1"));t.default={data:function(){return{btnLoading:!1,form:{ProductionConfirm:null},rules:{ProductionConfirm:{required:!0,message:"请选择是否需要生产管理过程",trigger:"change"}},show:!1}},mounted:function(){this.getFormProps()},methods:{getFormProps:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.show=!0;case 1:return t.a(2)}}),t)})))()},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,e.$emit("close",e.form.ProductionConfirm)}))}}}},c348:function(e,t,a){},c3b1:function(e,t,a){"use strict";a.r(t);var n=a("cc0d"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},c3d7:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("e9f5"),a("7d54"),a("d3b7"),a("25f0"),a("3ca3"),a("159b"),a("ddb0");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=a("586a"),l=n(a("15ac")),c=n(a("0f97")),u=(a("ed08"),a("21a6")),d=n(a("c4e3")),f=n(a("bc3a"));t.default={components:{DynamicDataTable:c.default},mixins:[l.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{searchDate:[],tbLoading:!1,btnLoading:!1,tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],total:0,tbData:[],selectArray:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("steels_import_file_page_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0;var t=(0,o.default)({},this.queryInfo);this.searchDate&&this.searchDate.length>1?(t.HisStartDate=this.searchDate[0],t.HisEndDate=this.searchDate[1]):(t.HisStartDate="",t.HisEndDate=""),t.Sys_Project_Id=this.sysProjectId,(0,s.GetImportHistoryPageList)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},multiSelectedChange:function(e){this.selectArray=e},handleOpen:function(e){window.open(e,"_blank")},onSubmit:function(){this.btnLoading=!0,this.handleBatchDownload(this.selectArray,"历史清单导出")},handleBatchDownload:function(e,t){var a=this;return(0,i.default)((0,r.default)().m((function n(){var o,i,s,l;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return o=e,i=new d.default,s={},l=[],n.n=1,o.forEach((function(e){var t=a.getFile(e.FileUrl).then((function(t){var a=e.FileName;i.file(a,t,{binary:!0}),s[a]=t}));l.push(t)}));case 1:Promise.all(l).then((function(){i.generateAsync({type:"blob"}).then((function(e){(0,u.saveAs)(e,t+".zip"),a.btnLoading=!1,a.notify&&a.notify.close()})).catch((function(e){a.btnLoading=!1,a.$message.error("网络出现了一点小问题，请稍后重试")}))}));case 2:return n.a(2)}}),n)})))()},getFile:function(e){return new Promise((function(t,a){(0,f.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))}}}},c404:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)},o=[]},c465:function(e,t,a){"use strict";a("e179")},c625:function(e,t,a){"use strict";a.r(t);var n=a("d15f"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},c663:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.list,(function(t,n){return a("el-row",{key:t.id,staticClass:"item-x"},[a("div",{staticClass:"item"},[a("label",[e._v(" 属性名称 "),a("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.key,callback:function(a){e.$set(t,"key",a)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),a("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[a("label",[e._v("请输入值 "),e.checkType(t.key,"number")?a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?a("el-input",{model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"array")&&"Is_Component"===t.key?a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.Is_Component_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),a("el-tree-select",{directives:[{name:"show",rawName:"v-show",value:e.checkType(t.key,"array")&&"SteelType"===t.key,expression:"checkType(info.key,'array') && info.key==='SteelType'"}],ref:"treeSelect",refInFor:!0,staticStyle:{width:"100%",display:"inline-block"},attrs:{"tree-params":e.treeParams},on:{"node-click":e.steelTypeChange},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}})],1)]),a("span",{staticClass:"item-span"},0===n?[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),a("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(n)}}})])])})),a("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},o=[]},c71a:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7");var o=n(a("bbc2")),r=a("586a");n(a("4360")),t.default={components:{OSSUpload:o.default},props:{typeEntity:{type:Object,default:function(){}}},data:function(){return{btnLoading:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,attachments:[],projectId:""}},computed:{},created:function(){},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},handleChange:function(){},beforeUpload:function(e){this.curFile=e,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=0;this.fileList.filter((function(t,n){t.name===e.name&&(a=n)})),this.fileList.splice(a,1),this.attachments.splice(a,1),this.btnLoading=!t.every((function(e){return"success"===e.status}))},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]}),this.btnLoading=!a.every((function(e){return"success"===e.status}))},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.fileList=[],this.dialogVisible=!1}catch(e){}},importModelFn:function(){var e=this;if(0==this.fileList.length)return this.$message({message:"请上传文件",type:"warning"}),!1;(0,r.ImportComponentModelInfo)({uploadUrl:this.attachments[0].File_Url}).then((function(t){t.IsSucceed?(e.$message({message:"模型清单导入成功",type:"warning"}),e.$emit("close")):e.$message({message:t.Message,type:"error"})}))},handleSteelExport:function(e){this.$emit("checkModelList",e)}}}},c7ab:function(e,t,a){"use strict";a.r(t);var n=a("f68a");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);var r,i,s=a("2877"),l=Object(s["a"])(n["default"],r,i,!1,null,null,null);t["default"]=l.exports},c7d43:function(e,t,a){"use strict";a.r(t);var n=a("ab9e"),o=a("5548");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("3b65");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"36a329f9",null);t["default"]=s.exports},c7edb:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("div",{staticClass:"top-info-title"},[e._v(e._s(e.SteelName))]),a("div",[e._v("需求零件总数："),a("span",[e._v(e._s(e.steelTotalNum))]),e._v(" 件 需求零件总重："),a("span",[e._v(e._s(e.steelTotalWeight))]),e._v(" kg")])]),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"500",align:"left",stripe:"",data:e.tbData,resizable:"","tree-config":{transform:!0,rowField:"Id",parentField:"ParentId"},"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t,n){return[a("vxe-column",{key:t.Code,attrs:{"tree-node":0===n,"min-width":t.Min_Width,fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return["Code"===t.Code?[o.Is_Change?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("变")]):e._e(),o.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),a("span",[e._v(e._s(o.Code))]),a("span",{staticStyle:{"margin-left":"5px"}},[1===o.Part_Grade?a("el-tag",[e._v("部")]):a("el-tag",{attrs:{type:"success"}},[e._v("零")])],1)]:"SchedulingNum"===t.Code?a("span",[e._v(" "+e._s(o.SchedulingNum?o.SchedulingNum:"-")+" ")]):"Producting_Count"===t.Code?a("span",[e._v(" "+e._s(o.Producting_Count?o.Producting_Count:"-")+" ")]):"Is_Main"===t.Code?a("span",[1===o.Part_Grade?a("span",[e._v("-")]):a("span",[a("el-tag",{attrs:{type:o.Is_Main?"success":"danger"}},[e._v(e._s(o.Is_Main?"是":"否")+" ")])],1)]):"SH"===t.Code?a("span",[0==o.SH?a("div",[e._v("/")]):a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(o)}}},[e._v("查看")])],1)]):a("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" ")])]}}],null,!0)})]}))],2)],1)])},o=[]},c895:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""},scopedSlots:e._u([{key:"Url",fn:function(t){var n=t.row;return[a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n.Url)}}},[e._v("查看")])],1)]}}])})],1)])},o=[]},c92b:function(e,t,a){"use strict";a("6c02")},c93e:function(e,t,a){},cb62:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"drawer-container"},[a("el-timeline",[a("el-timeline-item",{staticClass:"complete",attrs:{icon:"el-icon-success",size:"large"}},[a("div",{staticClass:"title"},[e._v("深化导入")]),a("div",{staticClass:"content"},[a("div",{staticClass:"info green"},[e._v("导入数量:"+e._s(e.one.DeepenAmount))]),a("div",{staticClass:"preson"},[e._v(e._s(e.one.ImportUserName))]),a("div",{staticClass:"time"},[e._v(e._s(e.one.ImportDate))])])]),e.isNeedProduct?[a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.two.SchduleStatus),attrs:{icon:e.getIconOrColor("icon",e.two.SchduleStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("排产完成")]),a("div",{staticClass:"content"},[a("div",{class:e.getIconOrColor("color",e.two.SchduleStatus)},[e._v("排产数量:"+e._s(e.two.SchduleAmount))]),0!==e.two.SchduleStatus?a("div",{staticClass:"preson"},[e._v(e._s(e.two.SchduleUserName))]):e._e(),0!==e.two.SchduleStatus?a("div",{staticClass:"time"},[e._v(e._s(e.two.SchduleDate))]):e._e()])]),e.two.SchduleAmount>0?[e.IsMultiSchdule?[a("el-timeline-item",{staticClass:"moreSchdule",class:e.getIconOrColor("iconColor",e.isMoreSchduleStatus),attrs:{icon:e.getIconOrColor("icon",e.isMoreSchduleStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("所有工序")]),a("div",{staticClass:"inner-content"},e._l(e.three,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"inner-title"},[a("span",{class:["inner-title-icon",e.getIconOrColor("bgIcon",t.ProcessStatus)]}),a("span",{staticClass:"title"},[e._v(e._s(t.ProcessName))])]),a("div",{staticClass:"zj-x ml15"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.TeamList.length>0?e._l(t.TeamList,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.TeamStatus)},[e._v(e._s(t.TeamName)+"完成:"+e._s(t.TeamAmount))]),t.TeamAmount?a("div",{staticClass:"time"},[e._v(e._s(t.TeamDate))]):e._e(),e._v(" "+e._s(852)+" ")])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)})),0)])]:e._l(e.three,(function(t,n){return a("el-timeline-item",{key:n,class:e.getIconOrColor("iconColor",t.ProcessStatus),attrs:{icon:e.getIconOrColor("icon",t.ProcessStatus),type:t.type,size:"large"}},[a("div",{staticClass:"title"},[e._v(e._s(t.ProcessName))]),a("div",{staticClass:"zj-x"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.TeamList.length>0?e._l(t.TeamList,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.TeamStatus)},[e._v(e._s(t.TeamName)+"完成:"+e._s(t.TeamAmount))]),t.TeamAmount?a("div",{staticClass:"time"},[e._v(e._s(t.TeamDate))]):e._e()])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)}))]:e._e()]:e._e(),e.isNeedProduct&&0!==e.two.SchduleStatus||!e.isNeedProduct?[a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.four.RukuStatus),attrs:{icon:e.getIconOrColor("icon",e.four.RukuStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("入库完成")]),e.four.RukuList.length>0?e._l(e.four.RukuList,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"content"},[a("div",{staticClass:"green"},[e._v("入库数量："+e._s(t.RukuAmount))]),a("div",{staticClass:"preson"},[e._v(e._s(t.RukuUserName))]),a("div",{staticClass:"time"},[e._v(e._s(t.RukuDate))])]),a("div",{staticClass:"content"},[a("div",{staticClass:"blue1"},[e._v(e._s(t.WarehouseName)+"/"+e._s(t.LocationName))])])])})):[a("div",{staticClass:"content"},[a("div",[e._v("入库数量：0")])])]],2)]:e._e(),e.isNeedProduct&&0!==e.two.SchduleStatus||!e.isNeedProduct?[a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.five.SendStatus),attrs:{icon:e.getIconOrColor("icon",e.five.SendStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("发货完成")]),e.five.DeliveryList.length>0?e._l(e.five.DeliveryList,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"content"},[a("div",{staticClass:"green"},[e._v("发货数量："+e._s(t.DeliveryAmount))]),a("div",{staticClass:"preson"},[e._v(e._s(t.DeliveryUserName))]),a("div",{staticClass:"time"},[e._v(e._s(t.DeliveryDate))])]),a("div",{staticClass:"content"},[a("div",{staticClass:"blue2"},[e._v("发货单号："+e._s(t.DeliveryNo))])])])})):[a("div",{staticClass:"content"},[a("div",[e._v("发货数量：0")])])]],2)]:e._e()],2)],1)},o=[]},cc0d:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("2909"));a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("b64b"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("ddb0");var r=a("586a");t.default={name:"TracePlot",props:{trackDrawerData:{type:Object,default:function(){return{}}}},data:function(){return{MocContent:[],ChangeUserName:"",ChangeDate:"",activities:[],one:{DeepenAmount:"",ImportUserName:"",ImportDate:""},two:{SchduleStatus:0,SchduleAmount:0,SchduleDate:"",SchduleUserName:""},three:[],four:{RukuStatus:0,RukuList:[]},five:{SendStatus:0,DeliveryList:[]},isNeedProduct:!0,IsMultiSchdule:!1,isMoreSchduleStatus:0,loading:!0}},mounted:function(){this.getComponentProductionTrack()},methods:{getComponentProductionTrack:function(){var e=this;this.loading=!0,(0,r.GetComponentProductionTrack)({SysProjectId:this.trackDrawerData.Sys_Project_Id,CompIds:this.trackDrawerData.Id}).then((function(t){var a=t.Data,n=a.DeepenAmount,r=a.ImportUserName,i=a.ImportDate,s=a.SchduleStatus,l=a.SchduleAmount,c=a.SchduleUserName,u=a.SchduleDate,d=a.IsNeedProduct,f=a.ProcessList,m=a.MocContent,p=a.ChangeUserName,h=a.ChangeDate,g=a.RukuStatus,y=a.RukuList,b=a.SendStatus,v=a.DeliveryList,_=a.IsMultiSchdule;e.MocContent=m&&JSON.parse(m||[]),e.ChangeUserName=p,e.ChangeDate=h,e.one={DeepenAmount:n,ImportUserName:r,ImportDate:i},e.isNeedProduct=d,e.IsMultiSchdule=_,e.two={SchduleStatus:s,SchduleAmount:l,SchduleUserName:c,SchduleDate:u},e.three=f;var P=(0,o.default)(new Set(f.map((function(e){return e.ProcessStatus}))));e.isMoreSchduleStatus=P.includes(1)?1:P.includes(2)?2:0,e.four={RukuStatus:g,RukuList:y},e.five={SendStatus:b,DeliveryList:v}})).finally((function(){e.loading=!1}))},getIconOrColor:function(e,t){return"icon"===e?2===t?"el-icon-success":"el-icon-yuanxing":"iconColor"===e?1===t?"inProgress":2===t?"complete":"init":"color"===e?1===t?"blue1":2===t?"green":"":"bgIcon"===e?1===t?"cs-unComplete-icon":2===t?"cs-success-icon":"cs-init-icon":void 0}}}},ccc2:function(e,t,a){},cd93:function(e,t,a){"use strict";a.r(t);var n=a("f84b"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},ce3c:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("e9c4"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("5319"),a("1276"),a("c7cd"),a("159b");var o=n(a("2909")),r=n(a("c14f")),i=n(a("1da1")),s=n(a("5530")),l=a("4744"),c=a("6186"),u=a("fd31"),d=a("586a"),f=a("3166"),m=(a("c24f"),a("2e8a")),p=n(a("1463")),h=n(a("34e9")),g=n(a("cfd9")),y=n(a("0fbf")),b=n(a("f675")),v=n(a("8953")),_=n(a("663b")),P=n(a("6ab4")),S=n(a("d7c4")),C=n(a("ef26")),I=n(a("f6ef")),D=n(a("918a")),k=n(a("0bfe")),T=n(a("0f94")),L=n(a("d33a")),w=n(a("fe55")),x=n(a("0d54")),O=n(a("47aa")),N=n(a("31b1")),A=n(a("a888")),j=n(a("333d")),G=a("8975"),R=n(a("6347")),$=n(a("ff7e7")),F=n(a("bc3a")),M=n(a("f151")),U=a("ed08"),E=a("c685"),B=(a("e144"),a("f4f2"),a("f382"),a("7f9d")),W=n(a("d7b4")),z=n(a("bae6")),q=n(a("fdec")),V=n(a("6612")),H=a("2f62"),Y=n(a("d4ea")),K="$_$";t.default={directives:{elDragDialog:A.default,sysUseType:M.default},components:{ExpandableSection:z.default,LocationImport:W.default,TreeDetail:p.default,TopHeader:h.default,comImport:g.default,comImportByFactory:b.default,BatchEdit:_.default,HistoryExport:v.default,GeneratePack:I.default,Edit:S.default,ComponentPack:P.default,OneClickGeneratePack:C.default,Pagination:j.default,bimdialog:$.default,ComponentsHistory:y.default,ProductionConfirm:D.default,PartList:k.default,SteelMeans:T.default,ModelComponentCode:w.default,ProductionDetails:x.default,ModelListImport:O.default,comDrawdialog:N.default,TracePlot:q.default,modelDrawing:Y.default,ProcessData:L.default},mixins:[R.default],data:function(){return{allStopFlag:!1,showExpand:!0,isAutoSplit:void 0,tablePageSize:E.tablePageSize,syncVisible:!1,syncForm:{Is_Sync_To_Part:null},syncRules:{Is_Sync_To_Part:{required:!0,message:"请选择是否同步到相关零件",trigger:"change"}},treeSelectParams:{placeholder:"请选择",clearable:!0},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},treeData:[],treeLoading:!0,expandedKey:"",projectName:"",statusType:"",searchHeight:0,searchStatus:!0,tbData:[],total:0,tbLoading:!1,pgLoading:!1,queryInfo:{Page:1,PageSize:10,ParameterJson:[]},customPageSize:[10,20,50,100],installUnitIdNameList:[],nameMode:1,names:"",customParams:{Code_Like:"",Spec:"",Texture:"",Is_Direct:"",Create_UserName:"",InstallUnit_Ids:[],SteelNames:"",TypeId:"",Sys_Project_Id:"",Project_Id:"",Area_Id:"",Project_Name:"",SteelCode:"",SteelNumber:"",Area_Name:""},Unit:"",Proportion:0,customDialogParams:{},dialogVisible:!1,currentComponent:"",selectList:[],factoryOption:[],projectList:[],typeOption:[],treeParamsSteel:[],columns:[],columnsOption:[],title:"",width:"60%",tipLabel:"",monomerList:[],mode:"",isMonomer:!0,historyVisible:!1,sysUseType:void 0,productionConfirm:"",SteelFormEditData:{},deepenTotalLength:0,SteelAmountTotal:0,SchedulingNumTotal:0,SteelAllWeightTotal:0,SchedulingAllWeightTotal:0,FinishCountTotal:0,FinishWeightTotal:0,IsComponentTotal:0,TotalGrossWeight:0,IsComponentTotalSteelAllWeight:0,leftCol:4,rightCol:40,leftWidth:320,drawer:!1,scheduleLoading:!1,command:"cover",currentLastLevel:!1,cadRowCode:"",cadRowProjectId:"",IsUploadCad:!1,comDrawData:{},currentNode:{},trackDrawer:!1,trackDrawerTitle:"",trackDrawerData:{}}},computed:(0,s.default)((0,s.default)({},(0,H.mapGetters)("tenant",["isVersionFour"])),{},{typeEntity:function(){var e=this;return this.typeOption.find((function(t){return t.Id===e.customParams.TypeId}))},showTotalLength:function(){var e=[this.customParams.Fuzzy_Search_Col,this.customParams.Fuzzy_Search_Col2,this.customParams.Fuzzy_Search_Col3,this.customParams.Fuzzy_Search_Col4];return e.includes("SteelLength")&&e.includes("SteelSpec")},filterText:function(){return this.projectName+K+this.statusType},TotalGrossWeightT:function(){return(0,V.default)(this.TotalGrossWeight||0).format("0.[000]")}}),watch:{"customParams.TypeId":function(e,t){t&&"0"!==t&&this.fetchData()},names:function(e,t){this.changeMode()},nameMode:function(e,t){this.changeMode()}},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getPreferenceSettingValue();case 1:return t.n=2,e.getTypeList();case 2:e.fetchTreeData(),e.getFileType();case 3:return t.a(2)}}),t)})))()},mounted:function(){},activated:function(){},methods:{changeMode:function(e){1===this.nameMode?(this.customParams.Code_Like=this.names,this.customParams.SteelNames=""):(this.customParams.Code_Like="",this.customParams.SteelNames=this.names.replace(/\s+/g,"\n"))},getComponentInfo:function(e){var t=e.Drawing?e.Drawing.split(","):[],a=e.File_Url?e.File_Url.split(","):[];t.length>0&&a.length>0&&(this.drawingActive=t[0]),t.length>0&&a.length>0&&(this.drawingDataList=t.map((function(e,t){return{name:e,label:e,url:a[t]}}))),this.getComponentInfoDrawing(e)},getComponentInfoDrawing:function(e){var t=this,a=e.Id;(0,d.GetSteelCadAndBimId)({importDetailId:a}).then((function(a){if(a.IsSucceed){var n,o=null===(n=a.Data)||void 0===n?void 0:n[0];if(!e.File_Url&&!o.ExtensionName)return void t.$message({message:"当前构件无图纸和模型",type:"warning"});var r={extensionName:o.ExtensionName,fileBim:o.fileBim,IsUpload:o.IsUpload,Code:e.SteelName,Sys_Project_Id:e.Sys_Project_Id};t.$nextTick((function(e){t.$refs.modelDrawingRef.dwgInit(r)}))}}))},fetchTreeData:function(){var e=this;(0,f.GetProjectAreaTreeList)({Type:0,MenuId:this.$route.meta.Id,projectName:this.projectName}).then((function(t){if(0!==t.Data.length){var a=t.Data;a.map((function(e){return 0===e.Children.length?e.Data.Is_Imported=!1:(e.Data.Is_Imported=e.Children.some((function(e){return!0===e.Data.Is_Imported})),e.Is_Directory=!0,e.Children.map((function(e){e.Children.length>0&&(e.Is_Directory=!0)}))),e})),e.treeData=a,0===Object.keys(e.currentNode).length?e.setKey():e.handleNodeClick(e.currentNode),e.treeLoading=!1}else e.treeLoading=!1}))},setKey:function(){var e=this,t=function(a){for(var n=0;n<a.length;n++){var o=a[n],r=o.Data,i=o.Children;return!r.ParentId||null!==i&&void 0!==i&&i.length?i&&i.length>0?t(i):void e.handleNodeClick(o):(e.currentNode=r,void e.handleNodeClick(o))}};return t(this.treeData)},handleNodeClick:function(e){var t,a,n=this;(this.handleSearch("reset",!1,"default"),this.currentNode=e,this.expandedKey=e.Id,this.$nextTick((function(e){var t=n.$refs["tree"].$refs.tree.getNode(n.expandedKey);t&&(n.isAutoSplit=null===t||void 0===t?void 0:t.data.Data.Is_Auto_Split)})),this.InstallUnit_Id="",null===e.ParentNodes&&"全部"!==e.Code?(this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id,this.customParams.Project_Id=e.Data.Id,this.customParams.Area_Name="",this.customParams.Area_Id=""):(this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id,this.customParams.Project_Id=e.Data.Project_Id,this.customParams.Area_Id=e.Data.Id),this.isAutoSplit=null===(t=e.Data)||void 0===t?void 0:t.Is_Auto_Split,this.currentLastLevel=!(!e.Data.Level||0!==e.Children.length),this.currentLastLevel)&&(this.customParams.Project_Name=null===(a=e.Data)||void 0===a?void 0:a.Project_Name,this.customParams.Area_Name=e.Label);var o=-1===e.Id?"":e.Id;this.pgLoading=!0,this.getInstallUnitIdNameList(o,e),this.fetchData(),this.getComponentSummaryInfo()},getInstallUnitIdNameList:function(e,t){var a=this;""===e||t.Children.length>0?this.installUnitIdNameList=[]:(0,f.GetInstallUnitIdNameList)({Area_Id:e}).then((function(e){a.installUnitIdNameList=e.Data}))},handleSearch:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";this.searchStatus=!1,e&&(this.$refs.customParams.resetFields(),this.names="",this.searchStatus=!0),t&&this.fetchData(),""===a&&this.getComponentSummaryInfo()},getPreferenceSettingValue:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:(0,l.GetPreferenceSettingValue)({Code:"Production_Confirm"}).then((function(t){e.productionConfirm=t.Data}));case 1:return t.a(2)}}),t)})))()},getComponentSummaryInfo:function(){var e=this;(0,d.GetComponentSummaryInfo)((0,s.default)({},this.customParams)).then((function(t){t.IsSucceed?(e.SteelAmountTotal=Math.round(1e3*t.Data.DeepenNum)/1e3,e.SchedulingNumTotal=Math.round(1e3*t.Data.SchedulingNum)/1e3,e.SteelAllWeightTotal=Math.round(1e3*t.Data.DeepenWeight)/1e3,e.SchedulingAllWeightTotal=Math.round(1e3*t.Data.SchedulingWeight)/1e3,e.FinishCountTotal=Math.round(1e3*t.Data.Finish_Count)/1e3,e.FinishWeightTotal=Math.round(1e3*t.Data.Finish_Weight)/1e3,e.IsComponentTotal=t.Data.Direct_Count||0,e.TotalGrossWeight=t.Data.TotalGrossWeight||0,e.IsComponentTotalSteelAllWeight=Math.round(1e3*(t.Data.Direct_Weight||0))/1e3,e.allStopFlag=!!t.Data.Is_Stop):e.$message({message:t.Message,type:"error"})}))},getProcessData:function(){var e=this;this.width="40%",this.generateComponent("构件工序完成量","ProcessData"),this.$nextTick((function(t){e.$refs["content"].init(e.customParams,e.selectList.map((function(e){return e.Id})).toString())}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,c.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.customParams.TypeId})).Code}).then((function(e){var n=e.IsSucceed,o=e.Data,r=e.Message;if(n){if(!o)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,o.Grid);var i=o.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return"SteelName"===e.Code&&(e.fixed="left"),e})),t.queryInfo.PageSize=+o.Grid.Row_Number||20;var s=JSON.parse(JSON.stringify(t.columns));t.columnsOption=s.filter((function(e){return"操作时间"!==e.Display_Name&&"安装位置"!==e.Display_Name&&"模型ID"!==e.Display_Name&&"深化资料"!==e.Display_Name&&"备注"!==e.Display_Name&&"零件"!==e.Display_Name&&"排产数量"!==e.Display_Name&&-1===e.Code.indexOf("Attr")&&"构件类型"!==e.Display_Name&&"批次"!==e.Display_Name})),a(t.columns)}else t.$message({message:r,type:"error"})}))}))},getComponentImportDetailPageList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.p=0,t.n=1,(0,d.GetComponentImportDetailPageList)((0,s.default)((0,s.default)({},e.queryInfo),e.customParams));case 1:if(a=t.v,!a.IsSucceed){t.n=3;break}return e.tbData=(a.Data.Data||[]).map((function(e){return e.Create_Date=(0,G.timeFormat)(e.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.deepenTotalLength=a.Data.DeepenTotalLength||0,e.queryInfo.PageSize=a.Data.PageSize,e.total=a.Data.TotalCount,e.selectList=[],t.n=2,e.getStopList();case 2:t.n=4;break;case 3:e.$message({message:a.Message,type:"error"});case 4:t.n=6;break;case 5:t.p=5,t.v,e.$message({message:"获取构件列表失败",type:"error"});case 6:return t.a(2)}}),t,null,[[0,5]])})))()},getStopList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(e.tbData&&e.tbData.length){t.n=1;break}return t.a(2);case 1:return a=e.tbData.map((function(e){return{Id:e.Id,Type:2}})),t.p=2,t.n=3,(0,B.GetStopList)(a);case 3:n=t.v,n.IsSucceed&&(o={},n.Data.forEach((function(e){o[e.Id]=null!==e.Is_Stop})),e.tbData.forEach((function(t){o[t.Id]&&e.$set(t,"stopFlag",o[t.Id])}))),t.n=5;break;case 4:t.p=4,t.v;case 5:return t.a(2)}}),t,null,[[2,4]])})))()},fetchData:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_component_page_list");case 1:e.tbLoading=!0,e.getComponentImportDetailPageList().then((function(t){e.tbLoading=!1,e.pgLoading=!1}));case 2:return t.a(2)}}),t)})))()},changePage:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),e.getComponentImportDetailPageList().then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.SteelAmountTotal=0,this.SchedulingNumTotal=0,this.SteelAllWeightTotal=0,this.SchedulingAllWeightTotal=0,this.FinishCountTotal=0,this.FinishWeightTotal=0,this.IsComponentTotal=0,this.TotalGrossWeight=0,this.IsComponentTotalSteelAllWeight=0;var a=0,n=0,o=0,r=0;this.selectList.length>0?(this.selectList.forEach((function(e){var i=null==e.SchedulingNum?0:e.SchedulingNum;t.SteelAmountTotal+=e.SteelAmount,t.SchedulingNumTotal+=e.SchedulingNum,t.FinishCountTotal+=e.Finish_Count,t.TotalGrossWeight+=e.TotalGrossWeight/1e3,a+=e.SteelAllWeight,n+=e.SteelWeight*i,o+=e.Finish_Weight,t.IsComponentTotal+="False"===e.Is_Component?e.SteelAmount:0,r+="False"===e.Is_Component?e.SteelAllWeight:0})),this.SteelAllWeightTotal=Math.round(a/this.Proportion*1e3)/1e3,this.SchedulingAllWeightTotal=Math.round(n/this.Proportion*1e3)/1e3,this.FinishWeightTotal=Math.round(o/this.Proportion*1e3)/1e3,this.IsComponentTotalSteelAllWeight=Math.round(r/this.Proportion*1e3)/1e3):this.getComponentSummaryInfo()},getTbData:function(e){var t=e.CountInfo;this.tipLabel=t},getTypeList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.customParams.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id),e.getCompTypeTree(e.typeOption[0].Code)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getCompTypeTree:function(e){var t=this;this.loading=!0,(0,m.GetCompTypeTree)({professional:e}).then((function(e){e.IsSucceed?(t.treeParamsSteel=e.Data,t.ObjectTypeList.data=e.Data,t.$nextTick((function(a){t.$refs.treeSelectObjectType.treeDataUpdateFun(e.Data)}))):(t.$message({message:e.Message,type:"error"}),t.treeData=[])})).finally((function(e){t.loading=!1}))},handleSearchDelete:function(){var e=this;if(""===this.customParams.Project_Id)return this.$message({type:"warning",message:"请选择项目"}),!1;this.$confirm("此操作将删除搜索的数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.DeleteAllComponentWithQuery)((0,s.default)({},e.customParams)).then((function(t){t.IsSucceed?(e.fetchData(),e.$message({message:"删除成功",type:"success"})):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleDelete:function(){var e=this;this.$confirm("此操作将删除选择数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,d.DeleteComponents)({ids:e.selectList.map((function(e){return e.Id})).toString()}).then((function(t){t.IsSucceed?(e.fetchData(),e.fetchTreeData(),e.$message({message:"删除成功",type:"success"})):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(e){var t=this;this.width="45%",this.generateComponent("编辑构件","Edit"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},handleBatchEdit:function(){var e=this,t=this.selectList.filter((function(e){return null!=e.SchedulingNum&&e.SchedulingNum>0}));t.length>0?this.$message({type:"error",message:"选中行包含已排产的构件,编辑信息需要进行变更操作"}):(this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.selectList,e.columnsOption)})))},handleView:function(e){var t=this;this.width="45%",this.generateComponent("查看构件","Edit"),this.$nextTick((function(a){e.isReadOnly=!0,t.$refs["content"].init(e)}))},handleViewPart:function(e){var t=this;this.width="60%",this.generateComponent("零部件清单","PartList"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleViewSH:function(e,t){var a=this;this.width="40%",this.generateComponent("查看深化资料","SteelMeans"),this.$nextTick((function(n){a.$refs["content"].init(e,t)}))},handleSteelMeans:function(e){this.handleViewSH(e,1)},handleViewModel:function(e){var t=this;this.width="40%",this.generateComponent("模型构件唯一码列表","ModelComponentCode"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleViewScheduling:function(e){var t=this;this.width="30%",this.generateComponent("生产详情","ProductionDetails"),this.$nextTick((function(a){t.$refs["content"].init(e)}))},handleHistory:function(e){this.generateComponent("构件变更历史","ComponentsHistory"),this.customDialogParams={steelUnique:e.SteelUnique}},locationExport:function(){this.handleSteelExport(3)},handleExport:function(e){"com"===e?this.handleSteelExport(2):this.handleExportAll()},handleExportAll:function(){var e=this;(0,d.ExportThreeBom)((0,s.default)((0,s.default)((0,s.default)({},this.queryInfo),this.customParams),{},{Ids:this.selectList.map((function(e){return e.Id})).toString()})).then((function(t){t.IsSucceed?window.open((0,U.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"})}))},handleSteelExport:function(e){var t=this;return(0,i.default)((0,r.default)().m((function a(){var n,o;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:if(""!==t.customParams.Sys_Project_Id||0!==t.selectList.length){a.n=1;break}return t.$message({type:"warning",message:"请选择项目"}),a.a(2,!1);case 1:return n=(0,s.default)((0,s.default)({Type:e,Import_Detail_Ids:t.selectList.map((function(e){return e.Id}))},t.customParams),{},{Sys_Project_Id:t.customParams.Sys_Project_Id}),a.n=2,(0,d.ExportComponentInfo)(n);case 2:if(o=a.v,o.IsSucceed){a.n=3;break}return t.$message({message:o.Message,type:"error"}),a.a(2);case 3:localStorage.getItem("ProjectName")+"_构件导出明细","application/octet-stream"===o.type?".rar":".xls",window.open((0,U.combineURL)(t.$baseUrl,o.Data),"_blank");case 4:return a.a(2)}}),a)})))()},getFile:function(e){return new Promise((function(t,a){(0,F.default)({method:"get",url:e,responseType:"arraybuffer"}).then((function(e){t(e.data)})).catch((function(e){a(e.toString())}))}))},modelListImport:function(){this.width="30%",this.generateComponent("模型清单导入","ModelListImport")},LocationImport:function(){this.width="30%",this.generateComponent("位置信息导入","LocationImport")},handleCommand:function(e,t){this.command=e,1===t?this.deepListImport(1):0===t&&this.deepListImport(0)},deepListImport:function(e){var t={Catalog_Code:"PLMDeepenFiles",Code:this.typeEntity.Code,name:this.typeEntity.Name};"true"===this.productionConfirm&&0===e?(this.width="30%",this.generateComponent("导入构件","ProductionConfirm")):this.$refs.dialog.handleOpen("add",t,null,!0,"",e,"",this.command,this.customParams)},deepListImportAgin:function(e){var t={Catalog_Code:"PLMDeepenFiles",Code:this.typeEntity.Code,name:this.typeEntity.Name};this.$refs.dialog.handleOpen("add",t,null,!0,"",0,e,this.command,this.customParams)},handleSchedulingInfoExport:function(){var e=this;(0,d.ExportComponentSchedulingInfo)({ids:this.selectList.map((function(e){return e.Id})).toString()}).then((function(t){t.IsSucceed?(window.open((0,U.combineURL)(e.$baseUrl,t.Data),"_blank"),t.Message&&e.$alert(t.Message,"导出通知",{confirmButtonText:"我知道了"})):e.$message({message:t.Message,type:"error"})}))},handleHistoryExport:function(){if(""===this.customParams.Project_Id)return this.$message({type:"warning",message:"请选择项目"}),!1;this.width="60%",this.generateComponent("历史清单导出","HistoryExport")},handleScheduleExport:function(){var e=this;this.scheduleLoading=!0;var t=this.selectList.map((function(e){return e.Id})).toString();(0,d.ExportDeepenFullSchedulingInfo)({Ids:t}).then((function(t){t.IsSucceed?(e.$message({message:"导出成功",type:"success"}),window.open((0,U.combineURL)(e.$baseUrl,t.Data),"_blank")):e.$message({message:t.Message,type:"error"})})).finally((function(t){e.scheduleLoading=!1}))},handleComponentPack:function(e){var t=this,a=e.data,n=e.type,o=void 0===n?2:n;this.width="80%",this.generateComponent("查看构件包","ComponentPack"),this.$nextTick((function(e){a&&t.$refs["content"].getSubmitObj(a),t.$refs["content"].handlePackage(o)}))},handleAllPack:function(){this.width="30%",this.generateComponent("查询结果一键打包","OneClickGeneratePack"),this.customDialogParams=this.customParams},handleGenerate:function(){var e=this;this.width="30%",this.generateComponent("生成构件包","GeneratePack"),this.$nextTick((function(t){e.$refs["content"].init(e.selectList)}))},handleClose:function(e){this.dialogVisible=!1,!0!==e&&!1!==e||this.deepListImportAgin(e)},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},fetchTreeDataLocal:function(){},customFilterFun:function(e,t,a){var n=e.split(K),r=n[0],i=n[1];if(!e)return!0;var s=a.parent,l=[a.label],c=[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"],u=1;while(u<a.level)l=[].concat((0,o.default)(l),[s.label]),c=[].concat((0,o.default)(c),[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"]),s=s.parent,u++;l=l.filter((function(e){return!!e})),c=c.filter((function(e){return!!e}));var d=!0,f=!0;return this.statusType&&(f=c.some((function(e){return-1!==e.indexOf(i)}))),this.projectName&&(d=l.some((function(e){return-1!==e.indexOf(r)}))),d&&f},getFileType:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,o,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return n={catalogCode:"PLMDeepenFiles"},t.n=1,(0,c.GetFileType)(n);case 1:o=t.v,i=o.Data.find((function(e){return"构件详图"===e.Label})),e.comDrawData={isSHQD:!1,Id:i.Id,name:i.Label,Catalog_Code:i.Code,Code:null===(a=i.Data)||void 0===a?void 0:a.English_Name};case 2:return t.a(2)}}),t)})))()},handelImport:function(){this.$refs.comDrawdialogRef.handleOpen("add",this.comDrawData,"",!1,this.customParams.Sys_Project_Id,!1)},handleTrack:function(e){this.trackDrawer=!0,this.trackDrawerTitle=e.SteelName,this.trackDrawerData=e}}}},ce70:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("main",{staticClass:"main"},[a("div",{staticClass:"left"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{type:"primary",disabled:!e.leftSelectList.length},on:{click:e.handleEdit}},[e._v("编辑包信息")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("加入")]),a("el-button",{attrs:{type:"danger",disabled:!e.leftSelectList.length},on:{click:e.handleDeleteLeft}},[e._v("删除包")])]},proxy:!0},{key:"right",fn:function(){return[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"名称/包编号"},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.fetchLeft},slot:"append"})],1)]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[a("z-left",{ref:"left",attrs:{search:e.search,"type-entity":e.typeEntity},on:{leftClick:e.leftClick,selectList:e.getLeftList,setSelect:e.getLeftList,clearRightData:e.clearRightData}})],1)],1),a("div",{staticClass:"right"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{disabled:!e.rightSelectList.length,type:"danger"},on:{click:e.handleDeleteRight}},[e._v("删除")])]},proxy:!0},{key:"right",fn:function(){return[a("div",{staticStyle:{display:"flex"}},[a("div",[a("label",[e._v("查询条件 "),a("el-input",{staticClass:"input-with-select",staticStyle:{width:"300px"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.search2,callback:function(t){e.search2=t},expression:"search2"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{slot:"prepend",clearable:"",placeholder:"请选择"},slot:"prepend",model:{value:e.select,callback:function(t){e.select=t},expression:"select"}},e._l(e.columnOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1)]),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchRight}},[e._v("查询")])],1)])]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[e.typeEntity.Id?a("z-right",{ref:"right",attrs:{"z-params":{Fuzzy_Search_Col:e.select,Fuzzy_Search:e.search2},"type-entity":e.typeEntity},on:{getColumn:e.getColumn,selectList:e.getRightList,getList:e.fetchRight}}):e._e()],1)],1)]),e.dialogVisible?a("Dialog",{ref:"dialog",attrs:{"dialog-visible":e.dialogVisible},on:{"update:dialogVisible":function(t){e.dialogVisible=t},"update:dialog-visible":function(t){e.dialogVisible=t},refresh:e.fetchLeft,handleClose:e.handleClose}}):e._e()],1)},o=[]},cedf:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"custom-tb cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,"pager-count":5,"small-pagination":"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,handleRowClick:e.handleRowClick,select:e.handleSelect,selectAll:e.selectAll,tableSearch:e.tableSearch}})],1)},o=[]},cf8f:function(e,t,a){"use strict";a.r(t);var n=a("8a0a"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},cf9a:function(e,t,a){"use strict";a("33c8")},cfd9:function(e,t,a){"use strict";a.r(t);var n=a("a37d"),o=a("0a9e");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("0018");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"7c621650",null);t["default"]=s.exports},d15f:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var o=n(a("c14f")),r=n(a("1da1")),i=a("a667"),s=n(a("bbc2")),l=a("0e9a"),c=a("66f9"),u=a("2d91"),d=a("5e99");t.default={name:"ComponentImport",components:{OSSUpload:s.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,factoryOption:[],typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Factory_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[]}},watch:{typeId:function(e){this.form.Type_Id=e}},mounted:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.getInfo(),t.n=1,e.getMonomerStatus();case 1:return t.n=2,e.getArea();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=2;break}return t.n=2,e.getMonomerList();case 2:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetGetMonomerList)({projectId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName"),this.form.ProjectId=localStorage.getItem("CurReferenceId"),this.getFactory()},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,i.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,l.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getFactory:function(){var e=this;(0,d.GetPlmProjectFactoryPageList)({PageSize:1e3,Page:1,Plm_Project_Id:this.form.ProjectId}).then((function(t){t.IsSucceed?(e.factoryOption=t.Data.Data,e.factoryOption.length>0&&(e.form.Factory_Id=e.factoryOption[0].Id)):e.$message({message:t.Message,type:"error"})}))},getArea:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,i.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code}});case 1:a=t.v,a.IsSucceed?e.AreaOption=a.Data:e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,o=a.Type_Id,r=a.ProjectId,s=a.Area_Name,l=a.Factory_Id,c=a.MonomerId,u=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",o),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",r),e.fileFormData.append("Area_Name",s),e.fileFormData.append("Factory_Id",l),e.fileFormData.append("MonomerId",c),e.fileFormData.append("Batches",u),(0,i.AddSynNew)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},d266:function(e,t,a){"use strict";a.r(t);var n=a("8b7f"),o=a("88a9");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("1c0a");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"9d527a74",null);t["default"]=s.exports},d33a:function(e,t,a){"use strict";a.r(t);var n=a("942d"),o=a("0eac");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("f71b");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"35b11c7c",null);t["default"]=s.exports},d369:function(e,t,a){"use strict";a.r(t);var n=a("a5f4"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},d3be:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[e._v(" 需求零件总数："),a("span",[e._v(e._s(e.steelTotalNum))]),e._v(" 件 需求零件总重："),a("span",[e._v(e._s(e.steelTotalWeight))]),e._v(" kg ")]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},scopedSlots:e._u([{key:"SchedulingNum",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(n.SchedulingNum?n.SchedulingNum:"-")+" ")])]}},{key:"Is_Main",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(1==n.Is_Main?"是":"否")+" ")])]}},{key:"SH",fn:function(t){var n=t.row;return[0==n.SH?a("div",[e._v("/")]):a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("查看")])],1)]}}])})],1)])},o=[]},d407:function(e,t,a){"use strict";a.r(t);var n=a("e829"),o=a("bc54");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("3594");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"2afa4d8c",null);t["default"]=s.exports},d446:function(e,t,a){},d4ea:function(e,t,a){"use strict";a.r(t);var n=a("56da"),o=a("03ff");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,null,null);t["default"]=s.exports},d53d:function(e,t,a){},d565:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319");t.default={bind:function(e,t,a){var n=e.querySelector(".el-dialog__header"),o=e.querySelector(".el-dialog");n.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var r=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();n.onmousedown=function(e){var t=e.clientX-n.offsetLeft,i=e.clientY-n.offsetTop,s=o.offsetWidth,l=o.offsetHeight,c=document.body.clientWidth,u=document.body.clientHeight,d=o.offsetLeft,f=c-o.offsetLeft-s,m=o.offsetTop,p=u-o.offsetTop-l,h=r(o,"left"),g=r(o,"top");h.includes("%")?(h=+document.body.clientWidth*(+h.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(h=+h.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var n=e.clientX-t,r=e.clientY-i;-n>d?n=-d:n>f&&(n=f),-r>m?r=-m:r>p&&(r=p),o.style.cssText+=";left:".concat(n+h,"px;top:").concat(r+g,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}},d594:function(e,t,a){"use strict";a.r(t);var n=a("c71a"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},d5c5:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var o=n(a("c14f")),r=n(a("1da1")),i=a("a667"),s=n(a("bbc2")),l=a("0e9a"),c=a("66f9"),u=a("2d91"),d=a("5e99");t.default={name:"ComponentImport",components:{OSSUpload:s.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,factoryOption:[],typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Factory_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[]}},watch:{typeId:function(e){this.form.Type_Id=e}},mounted:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.getInfo(),t.n=1,e.getMonomerStatus();case 1:return t.n=2,e.getArea();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=2;break}return t.n=2,e.getMonomerList();case 2:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetGetMonomerList)({projectId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName"),this.form.ProjectId=localStorage.getItem("CurReferenceId"),this.getFactory()},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,i.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,l.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getFactory:function(){var e=this;(0,d.GetPlmProjectFactoryPageList)({PageSize:1e3,Page:1,Plm_Project_Id:this.form.ProjectId}).then((function(t){t.IsSucceed?(e.factoryOption=t.Data.Data,e.factoryOption.length>0&&(e.form.Factory_Id=e.factoryOption[0].Id)):e.$message({message:t.Message,type:"error"})}))},getArea:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,i.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code}});case 1:a=t.v,a.IsSucceed?e.AreaOption=a.Data:e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,o=a.Type_Id,r=a.ProjectId,s=a.Area_Name,l=a.Factory_Id,c=a.MonomerId,u=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",o),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",r),e.fileFormData.append("Area_Name",s),e.fileFormData.append("Factory_Id",l),e.fileFormData.append("MonomerId",c),e.fileFormData.append("Batches",u),(0,i.AddSynNew)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},d7b4:function(e,t,a){"use strict";a.r(t);var n=a("5da8"),o=a("8982");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("e2d7");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"0c53a7e3",null);t["default"]=s.exports},d7c4:function(e,t,a){"use strict";a.r(t);var n=a("9f5c"),o=a("9668");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("8bde");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"6783622d",null);t["default"]=s.exports},dadd:function(e,t,a){},db2c4:function(e,t,a){},dbba:function(e,t,a){},dc623:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("div",[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.searchDate,callback:function(t){e.searchDate=t},expression:"searchDate"}}),a("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("查询")])],1),a("div",[a("el-button",{attrs:{type:"primary",disabled:!e.selectArray.length,loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("导出")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,size:"mini",border:"",stripe:""},on:{multiSelectedChange:e.multiSelectedChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange},scopedSlots:e._u([{key:"Create_Date",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Create_Date))+" ")]}},{key:"FileUrl",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return t.stopPropagation(),e.handleOpen(n.FileUrl)}}},[e._v(" "+e._s(n.FileUrl))])]}},{key:"Modify_Date ",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Modify_Date))+" ")]}}])})],1)])},o=[]},dca8:function(e,t,a){"use strict";var n=a("23e7"),o=a("bb2f"),r=a("d039"),i=a("861d"),s=a("f183").onFreeze,l=Object.freeze,c=r((function(){l(1)}));n({target:"Object",stat:!0,forced:c,sham:!o},{freeze:function(e){return l&&i(e)?l(s(e)):e}})},dd27:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("2532"),a("159b");var o=n(a("c14f")),r=n(a("1da1")),i=a("e144"),s=a("6186"),l=a("586a"),c=a("2e8a"),u=a("fd31");t.default={props:{typeEntity:{type:Object,default:function(){}},projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},Is_Component_Data:[{Name:"是",Id:"否"},{Name:"否",Id:"是"}],Is_Component:"",value:"",options:[{key:"SetupPosition",label:"批次",type:"string"},{key:"SteelSpec",label:"规格",type:"string"},{key:"SteelWeight",label:"单重",type:"number"},{key:"SteelLength",label:"长度",type:"number"},{key:"SteelAmount",label:"深化数量",type:"number"},{key:"SteelMaterial",label:"材质 ",type:"string"},{key:"Is_Component",label:"是否直发件",type:"array"},{key:"Remark",label:"备注",type:"string"},{key:"GrossWeight",label:"单毛重",type:"number"}],list:[{id:(0,i.v4)(),val:void 0,key:""}]}},mounted:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getCompTypeTree();case 1:return t.n=2,e.getUserableAttr();case 2:return a=e.options.filter((function(e,t){return t})).map((function(e){return e.key})),t.n=3,e.convertCode(e.typeEntity.Code,a);case 3:n=t.v,e.options=e.options.map((function(e,t){var a;t&&(e.label=null===(a=n.filter((function(e){return e.Is_Display})).find((function(t){return t.Code===e.key})))||void 0===a?void 0:a.Display_Name);return e}));case 4:return t.a(2)}}),t)})))()},methods:{getUserableAttr:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetUserableAttr)({IsComponent:!0}).then((function(t){if(t.IsSucceed){var a=t.Data,n=[];a.forEach((function(e){var t={};t.key=e.Code,t.lable=e.Display_Name,t.type="string",n.push(t)})),e.options=e.options.concat(n)}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getCompTypeTree:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetCompTypeTree)({professional:e.typeEntity.Code}).then((function(t){t.IsSucceed?e.$refs.treeSelect.forEach((function(e){e.treeDataUpdateFun(t.Data)})):(e.$message({message:t.Message,type:"error"}),e.treeData=[])})).finally((function(e){}));case 1:return t.a(2)}}),t)})))()},handleAdd:function(){this.list.push({id:(0,i.v4)(),val:void 0,key:""}),this.getCompTypeTree()},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){var a,n,r;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.btnLoading=!0,a={},n=0;case 1:if(!(n<e.list.length)){t.n=4;break}if(r=e.list[n],r.val){t.n=2;break}return("SteelWeight"===r.key||"SteelLength"===r.key||"SteelAmount"===r.key||"GrossWeight"===r.key)&&0===r.val?e.$message({message:"值不能为0",type:"warning"}):e.$message({message:"值不能为空",type:"warning"}),e.btnLoading=!1,t.a(2);case 2:a[r.key]=r.val;case 3:n++,t.n=1;break;case 4:return t.n=5,(0,l.BatchUpdateComponentImportInfo)({Ids:e.selectList.map((function(e){return e.Id})).toString(),EditInfo:a}).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}));case 5:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(a){return(!t.list.map((function(e){return e.key})).includes(a.key)||a.key===e)&&a.label}))},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},init:function(e,t){this.selectList=e},getColumnConfiguration:function(e){var t=arguments;return(0,r.default)((0,o.default)().m((function a(){var n,r;return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_component_page_list",a.n=1,(0,s.GetGridByCode)({code:n+","+e});case 1:return r=a.v,a.a(2,r.Data.ColumnList)}}),a)})))()},convertCode:function(e){var t=arguments,a=this;return(0,r.default)((0,o.default)().m((function n(){var r,i,s,l,c,u;return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return r=t.length>1&&void 0!==t[1]?t[1]:[],i=t.length>2?t[2]:void 0,s=a.selectList.filter((function(e){return null!==e.SteelType&&""!==e.SteelType})),l=s.length>0?r.filter((function(e){return"Is_Component"!==e})):r,n.n=1,a.getColumnConfiguration(e,i);case 1:return c=n.v,u=c.filter((function(e){var t=l.map((function(e){return e.toLowerCase()}));return t.includes(e.Code.toLowerCase())})),n.a(2,u)}}),n)})))()},steelTypeChange:function(e){this.Is_Component="true"==e.Code?"是":"否"}}}},ddcb:function(e,t,a){},e179:function(e,t,a){},e180:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),e._l(e.filterLabelList,(function(t){return a("el-form-item",{key:t.SteelUnique,attrs:{label:t.label}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{max:t.unPackCount,clearable:""},model:{value:t.number,callback:function(a){e.$set(t,"number",a)},expression:"item.number"}}),a("div",[e._v("未打包数量："+e._s(t.unPackCount))])],1)})),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],2)},o=[]},e2d7:function(e,t,a){"use strict";a("4548")},e41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=l,t.GetPartsImportTemplate=u,t.GetPartsList=s,t.GetProjectAreaTreeList=r,t.ImportParts=c,t.SaveProjectAreaSort=i;var o=n(a("b775"));function r(e){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,o.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},e65d:function(e,t,a){"use strict";a("04d8")},e829:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("span"),e._v("预计交付时间："+e._s(e.Plan_Date)),a("span",[e._v("是否超时："),a("span",[e._v(e._s(1==e.Is_Over_Time?"是":"否"))])])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,border:"",stripe:""}})],1)])},o=[]},e876:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"drawer-container"},[a("el-timeline",[a("el-timeline-item",{staticClass:"complete",attrs:{icon:"el-icon-success",size:"large"}},[a("div",{staticClass:"title"},[e._v("深化导入")]),a("div",{staticClass:"content"},[a("div",{staticClass:"info green"},[e._v("导入数量:"+e._s(e.one.DeepenAmount))]),a("div",{staticClass:"preson"},[e._v(e._s(e.one.ImportUserName))]),a("div",{staticClass:"time"},[e._v(e._s(e.one.ImportDate))])])]),e.MocContent&&e.MocContent.length?a("el-timeline-item",{staticClass:"complete",attrs:{icon:"el-icon-success",size:"large"}},[a("div",{staticClass:"title"},[e._v("变更")]),a("div",{staticClass:"content"},[a("div",{staticClass:"info "},[e._v(e._s(e.ChangeUserName))]),a("div",{staticClass:"info "},[e._v(e._s(e.ChangeDate))])]),a("div",{staticClass:"content flex-wrap"},e._l(e.MocContent,(function(t,n){return a("div",{key:n,staticClass:"mb-4 info green"},[a("span",{staticClass:"info"},[e._v(e._s(t.ChangeFieldName)+"：")]),a("span",{staticClass:"info"},[e._v(e._s(t.BeforeValue)+" -> ")]),a("span",{staticClass:"info"},[e._v(e._s(t.AfterValue))])])})),0)]):e._e(),e.isNeedProduct?[a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.two.SchduleStatus),attrs:{icon:e.getIconOrColor("icon",e.two.SchduleStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("排产完成")]),a("div",{staticClass:"content"},[a("div",{class:e.getIconOrColor("color",e.two.SchduleStatus)},[e._v("排产数量:"+e._s(e.two.SchduleAmount))]),0!==e.two.SchduleStatus?a("div",{staticClass:"preson"},[e._v(e._s(e.two.SchduleUserName))]):e._e(),0!==e.two.SchduleStatus?a("div",{staticClass:"time"},[e._v(e._s(e.two.SchduleDate))]):e._e()])]),e.two.SchduleAmount>0?[e.IsMultiSchdule?[a("el-timeline-item",{staticClass:"moreSchdule",class:e.getIconOrColor("iconColor",e.isMoreSchduleStatus),attrs:{icon:e.getIconOrColor("icon",e.isMoreSchduleStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("所有工序")]),a("div",{staticClass:"inner-content"},e._l(e.three,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"inner-title"},[a("span",{class:["inner-title-icon",e.getIconOrColor("bgIcon",t.ProcessStatus)]}),a("span",{staticClass:"title"},[e._v(e._s(t.ProcessName))])]),a("div",{staticClass:"zj-x ml15"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.TeamList.length>0?e._l(t.TeamList,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.TeamStatus)},[e._v(e._s(t.TeamName)+"完成:"+e._s(t.TeamAmount))]),t.TeamAmount?a("div",{staticClass:"time"},[e._v(e._s(t.TeamDate))]):e._e(),e._v(" "+e._s(852)+" ")])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)})),0)])]:e._l(e.three,(function(t,n){return a("el-timeline-item",{key:n,class:e.getIconOrColor("iconColor",t.ProcessStatus),attrs:{icon:e.getIconOrColor("icon",t.ProcessStatus),type:t.type,size:"large"}},[a("div",{staticClass:"title"},[e._v(e._s(t.ProcessName))]),a("div",{staticClass:"zj-x"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.TeamList.length>0?e._l(t.TeamList,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.TeamStatus)},[e._v(e._s(t.TeamName)+"完成:"+e._s(t.TeamAmount))]),t.TeamAmount?a("div",{staticClass:"time"},[e._v(e._s(t.TeamDate))]):e._e()])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)}))]:e._e()]:e._e(),e.isNeedProduct&&0!==e.two.SchduleStatus||!e.isNeedProduct?[a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.four.RukuStatus),attrs:{icon:e.getIconOrColor("icon",e.four.RukuStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("入库完成")]),e.four.RukuList.length>0?e._l(e.four.RukuList,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"content"},[a("div",{staticClass:"green"},[e._v("入库数量："+e._s(t.RukuAmount))]),a("div",{staticClass:"preson"},[e._v(e._s(t.RukuUserName))]),a("div",{staticClass:"time"},[e._v(e._s(t.RukuDate))])]),a("div",{staticClass:"content"},[a("div",{staticClass:"blue1"},[e._v(e._s(t.WarehouseName)+"/"+e._s(t.LocationName))])])])})):[a("div",{staticClass:"content"},[a("div",[e._v("入库数量：0")])])]],2)]:e._e(),e.isNeedProduct&&0!==e.two.SchduleStatus||!e.isNeedProduct?[a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.five.SendStatus),attrs:{icon:e.getIconOrColor("icon",e.five.SendStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("发货完成")]),e.five.DeliveryList.length>0?e._l(e.five.DeliveryList,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"content"},[a("div",{staticClass:"green"},[e._v("发货数量："+e._s(t.DeliveryAmount))]),a("div",{staticClass:"preson"},[e._v(e._s(t.DeliveryUserName))]),a("div",{staticClass:"time"},[e._v(e._s(t.DeliveryDate))])]),a("div",{staticClass:"content"},[a("div",{staticClass:"blue2"},[e._v("发货单号："+e._s(t.DeliveryNo))])])])})):[a("div",{staticClass:"content"},[a("div",[e._v("发货数量：0")])])]],2)]:e._e()],2)],1)},o=[]},e89c:function(e,t,a){"use strict";a.r(t);var n=a("10a2"),o=a("f5ee");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("4c33");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"8dafafa4",null);t["default"]=s.exports},ecba:function(e,t,a){},ed69:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"h100 app-wrapper",attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff ",attrs:{width:300},model:{value:e.showExpand,callback:function(t){e.showExpand=t},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[a("div",{staticClass:"tree-search"},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"导入状态选择"},model:{value:e.statusType,callback:function(t){e.statusType=t},expression:"statusType"}},[a("el-option",{attrs:{label:"已导入",value:"已导入"}}),a("el-option",{attrs:{label:"未导入",value:"未导入"}}),a("el-option",{attrs:{label:"已变更",value:"已变更"}})],1),a("el-input",{attrs:{placeholder:"关键词搜索",size:"small",clearable:"","suffix-icon":"el-icon-search"},on:{blur:e.fetchTreeDataLocal,clear:e.fetchTreeDataLocal},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fetchTreeDataLocal(t)}},model:{value:e.projectName,callback:function(t){e.projectName="string"===typeof t?t.trim():t},expression:"projectName"}})],1),a("el-divider",{staticClass:"cs-divider"}),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":e.customFilterFun,loading:e.treeLoading,"tree-data":e.treeData,"show-status":"","show-detail":"","filter-text":e.filterText,"expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick},scopedSlots:e._u([{key:"csLabel",fn:function(t){var n=t.showStatus,o=t.data;return[o.ParentNodes?e._e():a("span",{staticClass:"cs-blue"},[e._v("("+e._s(o.Code)+")")]),e._v(e._s(o.Label)+" "),n&&"全部"!=o.Label?[o.Data.Is_Deepen_Change?a("span",{staticClass:"cs-tag redBg"},[a("i",{staticClass:"fourRed"},[e._v("已变更")])]):a("span",{class:["cs-tag",1==o.Data.Is_Imported?"greenBg":"orangeBg"]},[a("i",{class:[1==o.Data.Is_Imported?"fourGreen":"fourOrange"]},[e._v(e._s(1==o.Data.Is_Imported?"已导入":"未导入"))])])]:e._e()]}}])})],1)],1)]),a("div",{staticClass:"cs-right"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",staticClass:"demo-form-inline",attrs:{model:e.customParams,"label-width":"90px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"构件名称",prop:"Names"}},[a("el-input",{staticClass:"input-with-select",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.names,callback:function(t){e.names=t},expression:"names"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.nameMode,callback:function(t){e.nameMode=t},expression:"nameMode"}},[a("el-option",{attrs:{label:"模糊搜索",value:1}}),a("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{prop:"SteelType"},scopedSlots:e._u([{key:"label",fn:function(){return[a("span",[e._v("构件类型")])]},proxy:!0}])},[a("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},model:{value:e.customParams.SteelType,callback:function(t){e.$set(e.customParams,"SteelType",t)},expression:"customParams.SteelType"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{"label-width":"60px",label:"规格",prop:"Spec"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Spec,callback:function(t){e.$set(e.customParams,"Spec",t)},expression:"customParams.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{"label-width":"60px",label:"材质",prop:"Texture"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Texture,callback:function(t){e.$set(e.customParams,"Texture",t)},expression:"customParams.Texture"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"mb0",attrs:{label:"是否直发件",prop:"Is_Direct"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.customParams.Is_Direct,callback:function(t){e.$set(e.customParams,"Is_Direct",t)},expression:"customParams.Is_Direct"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"操作人",prop:"Create_UserName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Create_UserName,callback:function(t){e.$set(e.customParams,"Create_UserName",t)},expression:"customParams.Create_UserName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"60px",label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:!Boolean(e.customParams.Area_Id)},model:{value:e.customParams.InstallUnit_Id,callback:function(t){e.$set(e.customParams,"InstallUnit_Id",t)},expression:"customParams.InstallUnit_Id"}},e._l(e.installUnitIdNameList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSearch()}}},[e._v("搜索 ")]),a("el-button",{on:{click:function(t){return e.handleSearch("reset")}}},[e._v("重置")])],1)],1)],1)],1)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},[a("div",[a("el-dropdown",{attrs:{trigger:"click",placement:"bottom-start"},on:{command:function(t){return e.handleCommand(t,1)}}},[a("el-button",{attrs:{type:"primary",disabled:!e.currentLastLevel}},[e._v("构件/零件导入 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"cover"}},[e._v("覆盖导入")]),a("el-dropdown-item",{attrs:{command:"add"}},[e._v("新增导入")])],1)],1),a("el-dropdown",{attrs:{trigger:"click",placement:"bottom-start"},on:{command:function(t){return e.handleCommand(t,0)}}},[a("el-button",{attrs:{type:"primary",disabled:!e.currentLastLevel}},[e._v("构件导入 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"cover"}},[e._v("覆盖导入")]),a("el-dropdown-item",{attrs:{command:"add"}},[e._v("新增导入")])],1)],1),a("el-button",{attrs:{type:"primary"},on:{click:e.modelListImport}},[e._v("导入模型清单 ")]),a("el-button",{attrs:{type:"primary"},on:{click:e.LocationImport}},[e._v("位置信息导入 ")]),a("el-button",{attrs:{disabled:!e.selectList.length},on:{click:e.handleSchedulingInfoExport}},[e._v("导出排产单模板 ")]),a("el-button",{on:{click:function(t){return e.handleSteelExport(2)}}},[e._v("导出构件")]),a("el-button",{on:{click:e.handleHistoryExport}},[e._v("历史清单导出")]),a("el-button",{attrs:{disabled:!e.selectList.length,type:"primary",plain:""},on:{click:e.handleBatchEdit}},[e._v("批量编辑 ")]),a("el-button",{attrs:{type:"danger",plain:"",disabled:!e.selectList.length},on:{click:e.handleDelete}},[e._v("删除选中 ")]),a("el-button",{attrs:{type:"success",plain:"",disabled:!Boolean(e.customParams.Sys_Project_Id)},on:{click:e.handelImport}},[e._v("图纸导入 ")])],1),e.showTotalLength?a("div",{staticClass:"cs-length"},[a("span",{staticClass:"txt-green"},[e._v("累计长度："+e._s(e.deepenTotalLength))])]):e._e()]),a("div",{staticClass:"info-box"},[a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("深化总数")]),a("i",[e._v(e._s(e.SteelAmountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("深化总量")]),a("i",[e._v(e._s(e.SteelAllWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("排产总数")]),a("i",[e._v(e._s(e.SchedulingNumTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("排产总量")]),a("i",[e._v(e._s(e.SchedulingAllWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("完成总数")]),a("i",[e._v(e._s(e.FinishCountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("完成总量")]),a("i",[e._v(e._s(e.FinishWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("直发件总数")]),a("i",[e._v(e._s(e.IsComponentTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("直发件总量")]),a("i",[e._v(e._s(e.IsComponentTotalSteelAllWeight)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("毛重合计")]),a("i",[e._v(e._s(e.TotalGrossWeightT)+" t")])])])]),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"auto","auto-resize":"",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width?t.Width:120},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return["SteelName"==t.Code?a("div",[a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.getComponentInfo(o)}}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]):"SteelAmount"==t.Code?a("div",[1==o.Is_Component_Status?a("span",{staticStyle:{color:"#298dff"}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" 件")]):a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.handleViewModel(o)}}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" 件")])]):"SchedulingNum"==t.Code?a("div",[o[t.Code]?a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.handleViewScheduling(o)}}},[e._v(e._s(o[t.Code]+" 件"))]):a("span",[e._v("-")])]):"SH"==t.Code?a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleViewSH(o,0)}}},[e._v("查看 ")])],1):"Part"==t.Code?a("div",[a("el-button",{directives:[{name:"show",rawName:"v-show",value:o.Part>0,expression:"row.Part > 0"}],attrs:{type:"text"},on:{click:function(t){return e.handleViewPart(o)}}},[e._v("查看 ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:0==o.Part,expression:"row.Part == 0"}]},[e._v("-")])],1):"Is_Component"==t.Code?a("div",["True"===o.Is_Component?a("span",[e._v("否")]):"False"===o.Is_Component?a("span",[e._v("是")]):a("span",[e._v("-")])]):"Drawing"==t.Code?a("div",["暂无"!==o.Drawing?a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.getComponentInfo(o)}}},[e._v(" "+e._s(e._f("displayValue")(o[t.Code]))+" ")]):a("span",[e._v(" "+e._s(e._f("displayValue")(o[t.Code])))])]):a("div",[a("span",[e._v(e._s(o[t.Code]||"-"))])])]}}],null,!0)})})),a("vxe-column",{attrs:{fixed:"right",align:"left",title:"操作",width:"140","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("详情 ")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(n)}}},[e._v("编辑 ")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleTrack(n)}}},[e._v("轨迹图 ")])]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)])])],1),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":e.selectList,"custom-params":e.customDialogParams,"type-id":e.customParams.TypeId,"type-entity":e.typeEntity,"params-steel":e.treeParamsSteel,"project-id":e.customParams.Project_Id,"sys-project-id":e.customParams.Sys_Project_Id},on:{close:e.handleClose,refresh:e.fetchData,checkPackage:e.handleComponentPack,checkSteelMeans:e.handleSteelMeans,checkModelList:e.handleSteelExport,locationExport:e.locationExport}})],1):e._e(),a("bimdialog",{ref:"dialog",attrs:{"is-auto-split":e.isAutoSplit,"type-entity":e.typeEntity},on:{getData:e.fetchData,getProjectAreaData:e.fetchTreeData}}),a("el-drawer",{attrs:{visible:e.drawer,direction:"btt",size:"60%","destroy-on-close":"","before-close":e.handleCloseDrawer},on:{"update:visible":function(t){e.drawer=t},opened:e.renderIframe}},[a("div",{staticStyle:{width:"100%",display:"flex","align-items":"center"}},[a("div",{staticStyle:{width:"50%",display:"flex","justify-content":"space-between","align-items":"center","padding-right":"30px"}},[a("div",{staticStyle:{"margin-left":"10px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v(e._s(e.extensionName?"构件模型":"构件图纸"))])]),e.fullscreenid?a("el-button",{on:{click:function(t){return e.fullscreen(0)}}},[e._v("全屏")]):e._e(),e.fullbimid?a("el-button",{on:{click:function(t){return e.fullscreen(1)}}},[e._v("全屏")]):e._e()],1),a("div",{staticStyle:{width:"50%"}},[e.extensionName?a("div",{staticStyle:{"margin-left":"10px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v("构件图纸")])]):e._e()])]),a("iframe",{key:e.iframeKey,staticStyle:{width:"100%",border:"0px",margin:"0",height:"60vh"},attrs:{id:"frame",src:e.iframeUrl}})]),a("el-drawer",{attrs:{visible:e.drawersull,direction:"btt",size:"100%","destroy-on-close":""},on:{"update:visible":function(t){e.drawersull=t}}},[e.templateUrl?a("iframe",{staticStyle:{width:"96%","margin-left":"2%",height:"70vh","margin-top":"2%"},attrs:{id:"fullFrame",src:e.templateUrl,frameborder:"0"}}):e._e()]),a("el-drawer",{attrs:{visible:e.trackDrawer,direction:"rtl",size:"30%","destroy-on-close":"","custom-class":"trackDrawerClass"},on:{"update:visible":function(t){e.trackDrawer=t}},scopedSlots:e._u([{key:"title",fn:function(){return[a("div",[a("span",[e._v(e._s(e.trackDrawerTitle))]),a("span",{staticStyle:{"margin-left":"24px"}},[e._v(e._s(e.trackDrawerData.SteelAmount))])])]},proxy:!0}])},[a("TracePlot",{attrs:{"track-drawer-data":e.trackDrawerData}})],1),a("comDrawdialog",{ref:"comDrawdialogRef",on:{getData:e.fetchData}})],1)},o=[]},eeef:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var o=n(a("5530")),r=n(a("c14f")),i=n(a("1da1")),s=a("a667"),l=n(a("bbc2")),c=a("0e9a"),u=a("66f9"),d=a("2c61"),f=a("1b69"),m=a("f2f6"),p=a("2d91");t.default={name:"ComponentImport",components:{OSSUpload:l.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Project_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[],projectList:[],unitList:[]}},watch:{typeId:function(e){this.form.Type_Id=e},"form.MonomerId":function(){this.getArea()},"form.Area_Name":function(e){e&&this.getUnitList()}},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.form.Factory_Id=localStorage.getItem("CurReferenceId"),e.getInfo(),t.n=1,e.getProjectList();case 1:return t.n=2,e.getMonomerStatus();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,p.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=3;break}return t.n=2,e.getMonomerList();case 2:t.n=4;break;case 3:e.getArea();case 4:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Batches="",e.form.MonomerId="",e.form.Area_Name="",t.n=1,(0,u.GetProMonomerList)({projectId:e.form.Project_Id,id:e.projectList.find((function(t){return t.Sys_Project_Id===e.form.Project_Id})).Id});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getUnitList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return a={Sys_Project_Id:e.form.Project_Id},e.isUnityInfo&&(a=(0,o.default)((0,o.default)({},a),{},{Area_Id:null===(n=e.AreaOption.find((function(t){return t.Name===e.form.Area_Name})))||void 0===n?void 0:n.Id})),t.n=1,(0,m.GetInstallUnitList)(a);case 1:i=t.v,e.unitList=i.Data;case 2:return t.a(2)}}),t)})))()},getProjectList:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetProjectList)({});case 1:a=t.v,a.IsSucceed?(e.projectList=a.Data,e.projectList.length>0&&(e.form.Project_Id=e.projectList[0].Sys_Project_Id)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName")},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,s.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,c.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getArea:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,s.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code,ProjectId:e.form.Project_Id}});case 1:a=t.v,a.IsSucceed?(e.AreaOption=a.Data,e.form.Area_Name=a.Data[0].Name):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,o=a.Type_Id,r=a.Project_Id,i=a.Area_Name,s=a.Factory_Id,l=a.MonomerId,c=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",o),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",e.projectList.find((function(e){return e.Sys_Project_Id===r})).Id),e.fileFormData.append("Area_Name",i),e.fileFormData.append("Factory_Id",s),e.fileFormData.append("MonomerId",l),e.fileFormData.append("Batches",c),(0,d.CommonImportModelToComp)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},ef26:function(e,t,a){"use strict";a.r(t);var n=a("508b"),o=a("4471");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"5ab71a5e",null);t["default"]=s.exports},f0e8:function(e,t,a){},f151:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(a("b9eb")),r=function(e){e.directive("permission",o.default)};window.Vue&&(window["sysUseType"]=o.default,Vue.use(r)),o.default.install=r;t.default=o.default},f1b36:function(e,t,a){"use strict";a("dadd")},f22b5:function(e,t,a){"use strict";a("58185")},f2f6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=l,t.CheckPlanTime=c,t.DeleteInstallUnit=m,t.GetCompletePercent=b,t.GetEntity=_,t.GetInstallUnitAllInfo=d,t.GetInstallUnitComponentPageList=y,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=u,t.GetInstallUnitList=s,t.GetInstallUnitPageList=i,t.GetProjectInstallUnitList=v,t.ImportInstallUnit=h,t.InstallUnitInfoTemplate=p,t.SaveInstallUnit=g,t.SaveOhterSourceInstallUnit=P;var o=n(a("b775")),r=n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function c(e){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function u(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function d(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function m(e){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function p(e){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function h(e){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function g(e){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function y(e){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function b(e){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function v(e){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function _(e){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(e)})}function P(e){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},f382:function(e,t,a){"use strict";function n(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=n(e.Children)),!0)}))}function o(e){e.map((function(e){if(e.Is_Directory||!e.Children)return o(e.Children);delete e.Children}))}function r(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function n(e,t,o){for(var r=0;r<e.length;r++){var i=e[r];if(i.Id===t)return a&&o.push(i),o;if(i.Children&&i.Children.length){if(o.push(i),n(i.Children,t,o).length)return o;o.pop()}}return[]}return n(e,t,[])}function i(e){return e.Children&&e.Children.length>0?i(e.Children[0]):e}function s(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&s(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=o,t.disableDirectory=s,t.findAllParentNode=r,t.findFirstNode=i,t.getDirectoryTree=n,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7")},f490:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.show?a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"180px"}},[a("el-form-item",{attrs:{label:"是否需要生产管理过程",prop:"ProductionConfirm"}},[a("el-radio-group",{model:{value:e.form.ProductionConfirm,callback:function(t){e.$set(e.form,"ProductionConfirm",t)},expression:"form.ProductionConfirm"}},[a("el-radio",{attrs:{label:!1}},[e._v("是")]),a("el-radio",{attrs:{label:!0}},[e._v("否")])],1)],1),a("div",{staticStyle:{"font-size":"14px",color:"#FF9400",width:"100%","text-align":"center",margin:"10px 0 30px 0"}},[e._v("不需要生产过程的构件导入后直接到待入库，排产数量为 0")]),a("el-form-item",{staticStyle:{"text-align":"right","margin-bottom":"0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1):e._e()],1)},o=[]},f53f:function(e,t,a){"use strict";a.r(t);var n=a("c3d7"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},f5ee:function(e,t,a){"use strict";a.r(t);var n=a("b300"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},f675:function(e,t,a){"use strict";a.r(t);var n=a("1e04"),o=a("8747");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("bd51");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"50459507",null);t["default"]=s.exports},f68a:function(e,t,a){"use strict";a.r(t);var n=a("0ce7"),o=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=o.a},f6ef:function(e,t,a){"use strict";a.r(t);var n=a("1591"),o=a("9bb7");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"5bd45715",null);t["default"]=s.exports},f71b:function(e,t,a){"use strict";a("c348")},f84b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9f5"),a("7d54"),a("d3b7"),a("159b");var o=a("586a"),r=n(a("15ac")),i=n(a("0f97")),s=n(a("6612"));t.default={components:{DynamicDataTable:i.default},mixins:[r.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1,Height:600},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Code",Display_Name:"零件名称",Is_Frozen:!0,Frozen_Dirction:"left",Min_Width:160,Is_Display:!0,Sort:1},{Code:"Spec",Display_Name:"规格",Min_Width:160,Is_Display:!0,Sort:2},{Code:"Length",Display_Name:"长度",Min_Width:160,Is_Display:!0,Sort:3},{Code:"Component_Code",Display_Name:"所属构件",Min_Width:140,Is_Display:!0,Sort:4},{Code:"Num",Display_Name:"需求数量",Min_Width:120,Is_Display:!0,Sort:5},{Code:"SchedulingNum",Display_Name:"排产数量",Min_Width:120,Is_Display:!0,Sort:6},{Code:"Weight",Display_Name:"单重（kg）",Min_Width:120,Is_Display:!0,Sort:7},{Code:"Total_Weight",Display_Name:"总重（kg）",Min_Width:120,Is_Display:!0,Sort:8},{Code:"Shape",Display_Name:"形状",Min_Width:140,Is_Display:!0,Sort:9},{Code:"Is_Main",Display_Name:"是否主零件",Min_Width:140,Is_Display:!0,Sort:10},{Code:"Remark",Display_Name:"备注",Min_Width:160,Is_Display:!0,Sort:11},{Code:"SH",Display_Name:"深化资料",Is_Frozen:!0,Frozen_Dirction:"right",Min_Width:120,Is_Display:!0,Sort:12}],steelTotalNum:0,steelTotalWeight:0,total:0,tbData:[],rowId:0}},mounted:function(){},methods:{init:function(e){var t=this;this.rowId=e.Id,this.tbLoading=!0;var a=0,n=0;(0,o.GetPartListWithComponent)({id:e.Id}).then((function(e){e.IsSucceed?(t.tbData=e.Data,e.Data.forEach((function(e){a+=e.Num,n+=e.Total_Weight})),t.steelTotalNum=(0,s.default)(a).format("0.[00]"),t.steelTotalWeight=(0,s.default)(n).format("0.[00]")):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleView:function(e){this.$emit("checkSteelMeans",e)}}}},f85b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7");var o=n(a("bbc2")),r=a("586a");n(a("4360")),t.default={components:{OSSUpload:o.default},props:{typeEntity:{type:Object,default:function(){}}},data:function(){return{btnLoading:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,attachments:[],projectId:""}},computed:{},created:function(){},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},handleChange:function(){},beforeUpload:function(e){this.curFile=e,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=0;this.fileList.filter((function(t,n){t.name===e.name&&(a=n)})),this.fileList.splice(a,1),this.attachments.splice(a,1),this.btnLoading=!t.every((function(e){return"success"===e.status}))},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]}),this.btnLoading=!a.every((function(e){return"success"===e.status}))},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.fileList=[],this.dialogVisible=!1}catch(e){}},importModelFn:function(){var e=this;if(0==this.fileList.length)return this.$message({message:"请上传文件",type:"warning"}),!1;(0,r.ImportComponentModelInfo)({uploadUrl:this.attachments[0].File_Url}).then((function(t){t.IsSucceed?(e.$message({message:"模型清单导入成功",type:"warning"}),e.$emit("close")):e.$message({message:t.Message,type:"error"})}))},handleSteelExport:function(e){this.$emit("checkModelList",e)}}}},fd51:function(e,t,a){},fd71:function(e,t,a){"use strict";a.r(t);var n=a("55cb"),o=a("4356");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("f22b5");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"4966584e",null);t["default"]=s.exports},fdec:function(e,t,a){"use strict";a.r(t);var n=a("e876"),o=a("c3b1");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("0ed8"),a("a90a");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"b2824e50",null);t["default"]=s.exports},fe55:function(e,t,a){"use strict";a.r(t);var n=a("6def"),o=a("33191");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("b5b07");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"039645d3",null);t["default"]=s.exports},fed2:function(e,t,a){"use strict";a("750a")},ff7e7:function(e,t,a){"use strict";a.r(t);var n=a("394d"),o=a("b639");for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);a("8fd6");var i=a("2877"),s=Object(i["a"])(o["default"],n["a"],n["b"],!1,null,"25945ce3",null);t["default"]=s.exports}}]);