(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4a9b6fe4"],{"03cd":function(t,e,n){!function(e,n){t.exports=n()}(0,(function(){return function(t){function e(i){if(n[i])return n[i].exports;var s=n[i]={i:i,l:!1,exports:{}};return t[i].call(s.exports,s,s.exports,e),s.l=!0,s.exports}var n={};return e.m=t,e.c=n,e.i=function(t){return t},e.d=function(t,n,i){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:i})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=5)}([function(t,e,n){"use strict";function i(){var t={},e=!1,n=0,s=arguments.length;for("[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(e=arguments[0],n++);n<s;n++){var r=arguments[n];!function(n){for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(e&&"[object Object]"===Object.prototype.toString.call(n[s])?t[s]=i(!0,t[s],n[s]):t[s]=n[s])}(r)}return t}e.a=i},function(t,e){!function(){"use strict";var e="undefined"!=typeof window&&void 0!==window.document?window.document:{},n=void 0!==t&&t.exports,i=function(){for(var t,n=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],i=0,s=n.length,r={};i<s;i++)if((t=n[i])&&t[1]in e){for(i=0;i<t.length;i++)r[n[0][i]]=t[i];return r}return!1}(),s={change:i.fullscreenchange,error:i.fullscreenerror},r={request:function(t,n){return new Promise(function(s,r){var o=function(){this.off("change",o),s()}.bind(this);this.on("change",o),t=t||e.documentElement;var a=t[i.requestFullscreen](n);a instanceof Promise&&a.then(o).catch(r)}.bind(this))},exit:function(){return new Promise(function(t,n){if(this.isFullscreen){var s=function(){this.off("change",s),t()}.bind(this);this.on("change",s);var r=e[i.exitFullscreen]();r instanceof Promise&&r.then(s).catch(n)}else t()}.bind(this))},toggle:function(t,e){return this.isFullscreen?this.exit():this.request(t,e)},onchange:function(t){this.on("change",t)},onerror:function(t){this.on("error",t)},on:function(t,n){var i=s[t];i&&e.addEventListener(i,n,!1)},off:function(t,n){var i=s[t];i&&e.removeEventListener(i,n,!1)},raw:i};i?(Object.defineProperties(r,{isFullscreen:{get:function(){return Boolean(e[i.fullscreenElement])}},element:{enumerable:!0,get:function(){return e[i.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(e[i.fullscreenEnabled])}}}),n?t.exports=r:window.screenfull=r):n?t.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()},function(t,e,n){"use strict";function i(t,e){t.style.position=e.position,t.style.left=e.left,t.style.top=e.top,t.style.width=e.width,t.style.height=e.height}function s(t){var e=t.element;e&&(e.classList.remove(t.options.fullscreenClass),(t.options.teleport||t.options.pageOnly)&&(t.options.teleport&&u&&(u.insertBefore(e,l),u.removeChild(l)),e.__styleCache&&i(e,e.__styleCache)))}var r=n(1),o=n.n(r),a=n(0),c={callback:function(){},fullscreenClass:"fullscreen",pageOnly:!1,teleport:!1},l=void 0,u=void 0,h={options:null,element:null,isFullscreen:!1,isEnabled:o.a.isEnabled,toggle:function(t,e,n){return void 0===n?this.isFullscreen?this.exit():this.request(t,e):n?this.request(t,e):this.exit()},request:function(t,e){var r=this;if(this.isFullscreen)return Promise.resolve();if(t||(t=document.body),this.options=n.i(a.a)({},c,e),t===document.body&&(this.options.teleport=!1),o.a.isEnabled||(this.options.pageOnly=!0),t.classList.add(this.options.fullscreenClass),this.options.teleport||this.options.pageOnly){var h=t.style,f=h.position,d=h.left,p=h.top,m=h.width,v=h.height;t.__styleCache={position:f,left:d,top:p,width:m,height:v},i(t,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"})}if(this.options.teleport&&(u=t.parentNode)&&(l=document.createComment("fullscreen-token"),u.insertBefore(l,t),document.body.appendChild(t)),this.options.pageOnly){var g=function t(e){"Escape"===e.key&&(document.removeEventListener("keyup",t),r.exit())};return this.isFullscreen=!0,this.element=t,document.removeEventListener("keyup",g),document.addEventListener("keyup",g),this.options.callback&&this.options.callback(this.isFullscreen),Promise.resolve()}var y=function e(){o.a.isFullscreen||(o.a.off("change",e),s(r)),r.isFullscreen=o.a.isFullscreen,r.options.teleport?r.element=t||null:r.element=o.a.element,r.options.callback&&r.options.callback(o.a.isFullscreen)};return o.a.on("change",y),o.a.request(this.options.teleport?document.body:t)},exit:function(){return this.isFullscreen?this.options.pageOnly?(s(this),this.isFullscreen=!1,this.element=null,this.options.callback&&this.options.callback(this.isFullscreen),Promise.resolve()):o.a.exit():Promise.resolve()}};h.support=h.isEnabled,h.getState=function(){return h.isFullscreen},h.enter=h.request,e.a=h},function(t,e,n){"use strict";function i(t,e){var n={};for(var i in t)e.indexOf(i)>=0||Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i]);return n}var s=n(2),r=n(0),o=function(t,e){var o=function(){var t=void 0,o={teleport:e.modifiers.teleport,pageOnly:e.modifiers.pageOnly};if(e.value)if("string"==typeof e.value)t=e.value;else{var a=e.value,c=a.target,l=i(a,["target"]);t=c,o=n.i(r.a)(o,l)}"string"==typeof t&&(t=document.querySelector(t)),s.a.toggle(t,o)};t._onClickFullScreen&&t.removeEventListener("click",t._onClickFullScreen),t.addEventListener("click",o),t._onClickFullScreen=o};e.a=o},function(t,e,n){var i=n(7)(n(6),n(8),null,null);t.exports=i.exports},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n(4),s=n.n(i),r=n(2),o=n(3),a=n(1),c=n.n(a),l=n(0);n.d(e,"screenfull",(function(){return c.a})),n.d(e,"api",(function(){return r.a})),n.d(e,"directive",(function(){return o.a})),n.d(e,"component",(function(){return s.a})),e.default={install:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.name||"fullscreen";t.component(i,n.i(l.a)(s.a,{name:i})),t.prototype["$"+i]=r.a,t.directive(i,o.a)}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n(1),s=n.n(i);e.default={props:{value:{type:Boolean,default:!1},fullscreen:{type:Boolean,default:!1},exitOnClickWrapper:{type:Boolean,default:!0},fullscreenClass:{type:String,default:"fullscreen"},pageOnly:{type:Boolean,default:!1},teleport:{type:Boolean,default:!1}},data:function(){return{isFullscreen:!1,isEnabled:!1}},computed:{support:function(){return this.isEnabled},isPageOnly:function(){return this.pageOnly||!s.a.isEnabled},wrapperStyle:function(){return(this.isPageOnly||this.teleport)&&this.isFullscreen?{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}:void 0}},methods:{toggle:function(t){void 0===t?this.isFullscreen?this.exit():this.request():t?this.request():this.exit()},request:function(){if(this.isPageOnly?(this.isFullscreen=!0,this.onChangeFullScreen(),document.removeEventListener("keyup",this.keypressCallback),document.addEventListener("keyup",this.keypressCallback)):(s.a.off("change",this.fullScreenCallback),s.a.on("change",this.fullScreenCallback),s.a.request(this.teleport?document.body:this.$el)),this.teleport){if(this.$el.parentNode===document.body)return;this.__parentNode=this.$el.parentNode,this.__token=document.createComment("fullscreen-token"),this.__parentNode.insertBefore(this.__token,this.$el),document.body.appendChild(this.$el)}},exit:function(){this.isFullscreen&&(this.isPageOnly?(this.isFullscreen=!1,this.onChangeFullScreen(),document.removeEventListener("keyup",this.keypressCallback)):s.a.exit())},shadeClick:function(t){t.target===this.$el&&this.exitOnClickWrapper&&this.exit()},fullScreenCallback:function(){s.a.isFullscreen||s.a.off("change",this.fullScreenCallback),this.isFullscreen=s.a.isFullscreen,this.onChangeFullScreen()},keypressCallback:function(t){"Escape"===t.key&&this.exit()},onChangeFullScreen:function(){this.isFullscreen||this.teleport&&this.__parentNode&&(this.__parentNode.insertBefore(this.$el,this.__token),this.__parentNode.removeChild(this.__token)),this.$emit("change",this.isFullscreen),this.$emit("update:fullscreen",this.isFullscreen),this.$emit("input",this.isFullscreen)},enter:function(){this.request()},getState:function(){return this.isFullscreen}},watch:{value:function(t){t!==this.isFullscreen&&(t?this.request():this.exit())},fullscreen:function(t){t!==this.isFullscreen&&(t?this.request():this.exit())}},created:function(){this.isEnabled=s.a.isEnabled}}},function(t,e){t.exports=function(t,e,n,i){var s,r=t=t||{},o=typeof t.default;"object"!==o&&"function"!==o||(s=t,r=t.default);var a="function"==typeof r?r.options:r;if(e&&(a.render=e.render,a.staticRenderFns=e.staticRenderFns),n&&(a._scopeId=n),i){var c=Object.create(a.computed||null);Object.keys(i).forEach((function(t){var e=i[t];c[t]=function(){return e}})),a.computed=c}return{esModule:s,exports:r,options:a}}},function(t,e){t.exports={render:function(){var t,e=this,n=e.$createElement;return(e._self._c||n)("div",e._b({ref:"wrapper",class:(t={},t[e.fullscreenClass]=e.isFullscreen,t),style:e.wrapperStyle,on:{click:function(t){return e.shadeClick(t)}}},"div",e.$attrs,!1),[e._t("default")],2)},staticRenderFns:[]}}])}))},"0891":function(t,e,n){"use strict";n.r(e);var i=n("9817"),s=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=s.a},"2e0a":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/<EMAIL>"},"349f":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/<EMAIL>"},4086:function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/<EMAIL>"},4673:function(t,e,n){},"5a0c":function(t,e,n){!function(e,n){t.exports=n()}(0,(function(){"use strict";var t=1e3,e=6e4,n=36e5,i="millisecond",s="second",r="minute",o="hour",a="day",c="week",l="month",u="quarter",h="year",f="date",d="Invalid Date",p=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,v={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],n=t%100;return"["+t+(e[(n-20)%10]||e[n]||e[0])+"]"}},g=function(t,e,n){var i=String(t);return!i||i.length>=e?t:""+Array(e+1-i.length).join(n)+t},y={s:g,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),i=Math.floor(n/60),s=n%60;return(e<=0?"+":"-")+g(i,2,"0")+":"+g(s,2,"0")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var i=12*(n.year()-e.year())+(n.month()-e.month()),s=e.clone().add(i,l),r=n-s<0,o=e.clone().add(i+(r?-1:1),l);return+(-(i+(n-s)/(r?s-o:o-s))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:l,y:h,w:c,d:a,D:f,h:o,m:r,s:s,ms:i,Q:u}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},_="en",b={};b[_]=v;var w="$isDayjsObject",S=function(t){return t instanceof $||!(!t||!t[w])},C=function t(e,n,i){var s;if(!e)return _;if("string"==typeof e){var r=e.toLowerCase();b[r]&&(s=r),n&&(b[r]=n,s=r);var o=e.split("-");if(!s&&o.length>1)return t(o[0])}else{var a=e.name;b[a]=e,s=a}return!i&&s&&(_=s),s||!i&&_},x=function(t,e){if(S(t))return t.clone();var n="object"==typeof e?e:{};return n.date=t,n.args=arguments,new $(n)},k=y;k.l=C,k.i=S,k.w=function(t,e){return x(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var $=function(){function v(t){this.$L=C(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[w]=!0}var g=v.prototype;return g.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(k.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var i=e.match(p);if(i){var s=i[2]-1||0,r=(i[7]||"0").substring(0,3);return n?new Date(Date.UTC(i[1],s,i[3]||1,i[4]||0,i[5]||0,i[6]||0,r)):new Date(i[1],s,i[3]||1,i[4]||0,i[5]||0,i[6]||0,r)}}return new Date(e)}(t),this.init()},g.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},g.$utils=function(){return k},g.isValid=function(){return!(this.$d.toString()===d)},g.isSame=function(t,e){var n=x(t);return this.startOf(e)<=n&&n<=this.endOf(e)},g.isAfter=function(t,e){return x(t)<this.startOf(e)},g.isBefore=function(t,e){return this.endOf(e)<x(t)},g.$g=function(t,e,n){return k.u(t)?this[e]:this.set(n,t)},g.unix=function(){return Math.floor(this.valueOf()/1e3)},g.valueOf=function(){return this.$d.getTime()},g.startOf=function(t,e){var n=this,i=!!k.u(e)||e,u=k.p(t),d=function(t,e){var s=k.w(n.$u?Date.UTC(n.$y,e,t):new Date(n.$y,e,t),n);return i?s:s.endOf(a)},p=function(t,e){return k.w(n.toDate()[t].apply(n.toDate("s"),(i?[0,0,0,0]:[23,59,59,999]).slice(e)),n)},m=this.$W,v=this.$M,g=this.$D,y="set"+(this.$u?"UTC":"");switch(u){case h:return i?d(1,0):d(31,11);case l:return i?d(1,v):d(0,v+1);case c:var _=this.$locale().weekStart||0,b=(m<_?m+7:m)-_;return d(i?g-b:g+(6-b),v);case a:case f:return p(y+"Hours",0);case o:return p(y+"Minutes",1);case r:return p(y+"Seconds",2);case s:return p(y+"Milliseconds",3);default:return this.clone()}},g.endOf=function(t){return this.startOf(t,!1)},g.$set=function(t,e){var n,c=k.p(t),u="set"+(this.$u?"UTC":""),d=(n={},n[a]=u+"Date",n[f]=u+"Date",n[l]=u+"Month",n[h]=u+"FullYear",n[o]=u+"Hours",n[r]=u+"Minutes",n[s]=u+"Seconds",n[i]=u+"Milliseconds",n)[c],p=c===a?this.$D+(e-this.$W):e;if(c===l||c===h){var m=this.clone().set(f,1);m.$d[d](p),m.init(),this.$d=m.set(f,Math.min(this.$D,m.daysInMonth())).$d}else d&&this.$d[d](p);return this.init(),this},g.set=function(t,e){return this.clone().$set(t,e)},g.get=function(t){return this[k.p(t)]()},g.add=function(i,u){var f,d=this;i=Number(i);var p=k.p(u),m=function(t){var e=x(d);return k.w(e.date(e.date()+Math.round(t*i)),d)};if(p===l)return this.set(l,this.$M+i);if(p===h)return this.set(h,this.$y+i);if(p===a)return m(1);if(p===c)return m(7);var v=(f={},f[r]=e,f[o]=n,f[s]=t,f)[p]||1,g=this.$d.getTime()+i*v;return k.w(g,this)},g.subtract=function(t,e){return this.add(-1*t,e)},g.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||d;var i=t||"YYYY-MM-DDTHH:mm:ssZ",s=k.z(this),r=this.$H,o=this.$m,a=this.$M,c=n.weekdays,l=n.months,u=n.meridiem,h=function(t,n,s,r){return t&&(t[n]||t(e,i))||s[n].slice(0,r)},f=function(t){return k.s(r%12||12,t,"0")},p=u||function(t,e,n){var i=t<12?"AM":"PM";return n?i.toLowerCase():i};return i.replace(m,(function(t,i){return i||function(t){switch(t){case"YY":return String(e.$y).slice(-2);case"YYYY":return k.s(e.$y,4,"0");case"M":return a+1;case"MM":return k.s(a+1,2,"0");case"MMM":return h(n.monthsShort,a,l,3);case"MMMM":return h(l,a);case"D":return e.$D;case"DD":return k.s(e.$D,2,"0");case"d":return String(e.$W);case"dd":return h(n.weekdaysMin,e.$W,c,2);case"ddd":return h(n.weekdaysShort,e.$W,c,3);case"dddd":return c[e.$W];case"H":return String(r);case"HH":return k.s(r,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return p(r,o,!0);case"A":return p(r,o,!1);case"m":return String(o);case"mm":return k.s(o,2,"0");case"s":return String(e.$s);case"ss":return k.s(e.$s,2,"0");case"SSS":return k.s(e.$ms,3,"0");case"Z":return s}return null}(t)||s.replace(":","")}))},g.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},g.diff=function(i,f,d){var p,m=this,v=k.p(f),g=x(i),y=(g.utcOffset()-this.utcOffset())*e,_=this-g,b=function(){return k.m(m,g)};switch(v){case h:p=b()/12;break;case l:p=b();break;case u:p=b()/3;break;case c:p=(_-y)/6048e5;break;case a:p=(_-y)/864e5;break;case o:p=_/n;break;case r:p=_/e;break;case s:p=_/t;break;default:p=_}return d?p:k.a(p)},g.daysInMonth=function(){return this.endOf(l).$D},g.$locale=function(){return b[this.$L]},g.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),i=C(t,e,!0);return i&&(n.$L=i),n},g.clone=function(){return k.w(this.$d,this)},g.toDate=function(){return new Date(this.valueOf())},g.toJSON=function(){return this.isValid()?this.toISOString():null},g.toISOString=function(){return this.$d.toISOString()},g.toString=function(){return this.$d.toUTCString()},v}(),P=$.prototype;return x.prototype=P,[["$ms",i],["$s",s],["$m",r],["$H",o],["$W",a],["$M",l],["$y",h],["$D",f]].forEach((function(t){P[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),x.extend=function(t,e){return t.$i||(t(e,$,x),t.$i=!0),x},x.locale=C,x.isDayjs=S,x.unix=function(t){return x(1e3*t)},x.en=b[_],x.Ls=b,x.p={},x}))},64860:function(t,e,n){"use strict";var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetWorkingTeamCheckingList=r,e.GetWorkingTeamYield=o;var s=i(n("b775"));function r(t){return(0,s.default)({url:"/PRO/ProductionCount/GetWorkingTeamCheckingList",method:"post",data:t})}function o(t){return(0,s.default)({url:"/PRO/ProductionCount/GetWorkingTeamYield",method:"post",data:t})}},"6a45":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/<EMAIL>"},"77eb":function(t,e,n){"use strict";n("ea8a")},"90ed":function(t,e,n){"use strict";n("4673")},"940e":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/<EMAIL>"},9817:function(t,e,n){"use strict";var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("fb6a"),n("a9e3"),n("b680");var s=i(n("a939")),r=n("64860"),o=i(n("03cd")),a=i(n("2b0e")),c=i(n("5a0c"));a.default.use(o.default);e.default={name:"ErgonomicsPioneerList",components:{vueSeamlessScroll:s.default},data:function(){return{fullscreen:!1,Type:1,TenantCode:this.$route.query.Tenant_Id||"pjszgc",productionRankingList:[],productionRankingListTop:[],teamRankingList:[],teamRankingListTop:[],dateRange:[(0,c.default)(this.getToday()).subtract(3,"month").format("YYYY-MM-DD"),this.getToday()],num:0}},watch:{fullscreen:function(t){this.num++}},mounted:function(){var t=this;this.getWorkEfficiencyRanking(),this.$nextTick((function(){t.onResize(),window.addEventListener("resize",t.onResize)}))},beforeDestroy:function(){window.removeEventListener("resize",this.onResize)},methods:{getToday:function(){return(0,c.default)(new Date).format("YYYY-MM-DD")},toggle:function(){this.fullscreen=!this.fullscreen},onResize:function(){var t=this;this.$nextTick((function(e){t.updateScale()}))},updateScale:function(){var t=1920,e=1080,n=this.$refs["page-container"],i=n.clientWidth,s=n.clientHeight,r=i/s<t/e?i/t:s/e,o=document.getElementsByClassName("team-wrapper")[0];o.style.transform="scale(".concat(r,") translate(-50%, -50%)")},getWorkEfficiencyRanking:function(t){var e=this;if(!t){var n=(0,c.default)(new Date).format("YYYY-MM-DD"),i=(0,c.default)(new Date).subtract(3,"month").format("YYYY-MM-DD");t=[i,n]}var s={Begin_Date:t[0],End_Date:t[1]};(0,r.GetWorkingTeamYield)(s).then((function(t){e.productionRankingListTop=t.Data.slice(0,3),e.productionRankingList=t.Data.slice(3,t.Data.length)})),(0,r.GetWorkingTeamCheckingList)(s).then((function(t){e.teamRankingListTop=t.Data.slice(0,3),e.teamRankingList=t.Data.slice(3,t.Data.length)}))},getSearch:function(t){this.getWorkEfficiencyRanking(t)},fixedNumber:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return t?Number(t).toFixed(e):"--"}}}},"9cfc":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/<EMAIL>"},a939:function(t,e,n){!function(e,n){t.exports=n()}("undefined"!=typeof self&&self,(function(){return function(t){function e(i){if(n[i])return n[i].exports;var s=n[i]={i:i,l:!1,exports:{}};return t[i].call(s.exports,s,s.exports,e),s.l=!0,s.exports}var n={};return e.m=t,e.c=n,e.d=function(t,n,i){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:i})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=1)}([function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),n(4)();var i=n(5),s=n(6);e.default={name:"vue-seamless-scroll",data:function(){return{xPos:0,yPos:0,delay:0,copyHtml:"",height:0,width:0,realBoxWidth:0}},props:{data:{type:Array,default:function(){return[]}},classOption:{type:Object,default:function(){return{}}}},computed:{leftSwitchState:function(){return this.xPos<0},rightSwitchState:function(){return Math.abs(this.xPos)<this.realBoxWidth-this.width},leftSwitchClass:function(){return this.leftSwitchState?"":this.options.switchDisabledClass},rightSwitchClass:function(){return this.rightSwitchState?"":this.options.switchDisabledClass},leftSwitch:function(){return{position:"absolute",margin:this.height/2+"px 0 0 -"+this.options.switchOffset+"px",transform:"translate(-100%,-50%)"}},rightSwitch:function(){return{position:"absolute",margin:this.height/2+"px 0 0 "+(this.width+this.options.switchOffset)+"px",transform:"translateY(-50%)"}},float:function(){return this.isHorizontal?{float:"left",overflow:"hidden"}:{overflow:"hidden"}},pos:function(){return{transform:"translate("+this.xPos+"px,"+this.yPos+"px)",transition:"all "+this.ease+" "+this.delay+"ms",overflow:"hidden"}},defaultOption:function(){return{step:1,limitMoveNum:5,hoverStop:!0,direction:1,openTouch:!0,singleHeight:0,singleWidth:0,waitTime:1e3,switchOffset:30,autoPlay:!0,navigation:!1,switchSingleStep:134,switchDelay:400,switchDisabledClass:"disabled",isSingleRemUnit:!1}},options:function(){return s({},this.defaultOption,this.classOption)},navigation:function(){return this.options.navigation},autoPlay:function(){return!this.navigation&&this.options.autoPlay},scrollSwitch:function(){return this.data.length>=this.options.limitMoveNum},hoverStopSwitch:function(){return this.options.hoverStop&&this.autoPlay&&this.scrollSwitch},canTouchScroll:function(){return this.options.openTouch},isHorizontal:function(){return this.options.direction>1},baseFontSize:function(){return this.options.isSingleRemUnit?parseInt(window.getComputedStyle(document.documentElement,null).fontSize):1},realSingleStopWidth:function(){return this.options.singleWidth*this.baseFontSize},realSingleStopHeight:function(){return this.options.singleHeight*this.baseFontSize},step:function(){var t=this.options.step;return this.isHorizontal?this.realSingleStopWidth:this.realSingleStopHeight,t}},methods:{reset:function(){this._cancle(),this._initMove()},leftSwitchClick:function(){if(this.leftSwitchState)return Math.abs(this.xPos)<this.options.switchSingleStep?void(this.xPos=0):void(this.xPos+=this.options.switchSingleStep)},rightSwitchClick:function(){if(this.rightSwitchState)return this.realBoxWidth-this.width+this.xPos<this.options.switchSingleStep?void(this.xPos=this.width-this.realBoxWidth):void(this.xPos-=this.options.switchSingleStep)},_cancle:function(){cancelAnimationFrame(this.reqFrame||"")},touchStart:function(t){var e=this;if(this.canTouchScroll){var n=void 0,i=t.targetTouches[0],s=this.options,r=s.waitTime,o=s.singleHeight,a=s.singleWidth;this.startPos={x:i.pageX,y:i.pageY},this.startPosY=this.yPos,this.startPosX=this.xPos,o&&a?(n&&clearTimeout(n),n=setTimeout((function(){e._cancle()}),r+20)):this._cancle()}},touchMove:function(t){if(!(!this.canTouchScroll||t.targetTouches.length>1||t.scale&&1!==t.scale)){var e=t.targetTouches[0],n=this.options.direction;this.endPos={x:e.pageX-this.startPos.x,y:e.pageY-this.startPos.y},event.preventDefault();var i=Math.abs(this.endPos.x)<Math.abs(this.endPos.y)?1:0;1===i&&n<2?this.yPos=this.startPosY+this.endPos.y:0===i&&n>1&&(this.xPos=this.startPosX+this.endPos.x)}},touchEnd:function(){var t=this;if(this.canTouchScroll){var e=void 0,n=this.options.direction;if(this.delay=50,1===n)this.yPos>0&&(this.yPos=0);else if(0===n){var i=this.realBoxHeight/2*-1;this.yPos<i&&(this.yPos=i)}else if(2===n)this.xPos>0&&(this.xPos=0);else if(3===n){var s=-1*this.realBoxWidth;this.xPos<s&&(this.xPos=s)}e&&clearTimeout(e),e=setTimeout((function(){t.delay=0,t._move()}),this.delay)}},enter:function(){this.hoverStopSwitch&&this._stopMove()},leave:function(){this.hoverStopSwitch&&this._startMove()},_move:function(){this.isHover||(this._cancle(),this.reqFrame=requestAnimationFrame(function(){var t=this,e=this.realBoxHeight/2,n=this.realBoxWidth/2,i=this.options,s=i.direction,r=i.waitTime,o=this.step;1===s?(Math.abs(this.yPos)>=e&&(this.$emit("ScrollEnd"),this.yPos=0),this.yPos-=o):0===s?(this.yPos>=0&&(this.$emit("ScrollEnd"),this.yPos=-1*e),this.yPos+=o):2===s?(Math.abs(this.xPos)>=n&&(this.$emit("ScrollEnd"),this.xPos=0),this.xPos-=o):3===s&&(this.xPos>=0&&(this.$emit("ScrollEnd"),this.xPos=-1*n),this.xPos+=o),this.singleWaitTime&&clearTimeout(this.singleWaitTime),this.realSingleStopHeight?Math.abs(this.yPos)%this.realSingleStopHeight<o?this.singleWaitTime=setTimeout((function(){t._move()}),r):this._move():this.realSingleStopWidth&&Math.abs(this.xPos)%this.realSingleStopWidth<o?this.singleWaitTime=setTimeout((function(){t._move()}),r):this._move()}.bind(this)))},_initMove:function(){var t=this;this.$nextTick((function(){var e=t.options.switchDelay,n=t.autoPlay,i=t.isHorizontal;if(t._dataWarm(t.data),t.copyHtml="",i){t.height=t.$refs.wrap.offsetHeight,t.width=t.$refs.wrap.offsetWidth;var s=t.$refs.slotList.offsetWidth;n&&(s=2*s+1),t.$refs.realBox.style.width=s+"px",t.realBoxWidth=s}if(!n)return t.ease="linear",void(t.delay=e);t.ease="ease-in",t.delay=0,t.scrollSwitch?(t.copyHtml=t.$refs.slotList.innerHTML,setTimeout((function(){t.realBoxHeight=t.$refs.realBox.offsetHeight,t._move()}),0)):(t._cancle(),t.yPos=t.xPos=0)}))},_dataWarm:function(t){t.length},_startMove:function(){this.isHover=!1,this._move()},_stopMove:function(){this.isHover=!0,this.singleWaitTime&&clearTimeout(this.singleWaitTime),this._cancle()}},mounted:function(){this._initMove()},watch:{data:function(t,e){this._dataWarm(t),i(t,e)||this.reset()},autoPlay:function(t){t?this.reset():this._stopMove()}},beforeCreate:function(){this.reqFrame=null,this.singleWaitTime=null,this.isHover=!1,this.ease="ease-in"},beforeDestroy:function(){this._cancle(),clearTimeout(this.singleWaitTime)}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n(2),s=function(t){return t&&t.__esModule?t:{default:t}}(i);s.default.install=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.component(e.componentName||s.default.name,s.default)},"undefined"!=typeof window&&window.Vue&&Vue.component(s.default.name,s.default),e.default=s.default},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n(0),s=n.n(i);for(var r in i)"default"!==r&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n(7),a=n(3),c=a(s.a,o.a,!1,null,null,null);e.default=c.exports},function(t,e){t.exports=function(t,e,n,i,s,r){var o,a=t=t||{},c=typeof t.default;"object"!==c&&"function"!==c||(o=t,a=t.default);var l,u="function"==typeof a?a.options:a;if(e&&(u.render=e.render,u.staticRenderFns=e.staticRenderFns,u._compiled=!0),n&&(u.functional=!0),s&&(u._scopeId=s),r?(l=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(r)},u._ssrRegister=l):i&&(l=i),l){var h=u.functional,f=h?u.render:u.beforeCreate;h?(u._injectStyles=l,u.render=function(t,e){return l.call(e),f(t,e)}):u.beforeCreate=f?[].concat(f,l):[l]}return{esModule:o,exports:a,options:u}}},function(t,e){var n=function(){window.cancelAnimationFrame=function(){return window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.oCancelAnimationFrame||window.msCancelAnimationFrame||function(t){return window.clearTimeout(t)}}(),window.requestAnimationFrame=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return window.setTimeout(t,1e3/60)}}()};t.exports=n},function(t,e){var n=function(t,e){if(t===e)return!0;if(t.length!==e.length)return!1;for(var n=0;n<t.length;++n)if(t[n]!==e[n])return!1;return!0};t.exports=n},function(t,e){function n(){Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)});var t=void 0,e=void 0,s=void 0,r=void 0,o=void 0,a=void 0,c=1,l=arguments[0]||{},u=!1,h=arguments.length;if("boolean"==typeof l&&(u=l,l=arguments[1]||{},c++),"object"!==(void 0===l?"undefined":i(l))&&"function"!=typeof l&&(l={}),c===h)return l;for(;c<h;c++)if(null!=(e=arguments[c]))for(t in e)s=l[t],r=e[t],o=Array.isArray(r),u&&r&&("object"===(void 0===r?"undefined":i(r))||o)?(o?(o=!1,a=s&&Array.isArray(s)?s:[]):a=s&&"object"===(void 0===s?"undefined":i(s))?s:{},l[t]=n(u,a,r)):void 0!==r&&(l[t]=r);return l}var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};t.exports=n},function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{ref:"wrap"},[t.navigation?n("div",{class:t.leftSwitchClass,style:t.leftSwitch,on:{click:t.leftSwitchClick}},[t._t("left-switch")],2):t._e(),t._v(" "),t.navigation?n("div",{class:t.rightSwitchClass,style:t.rightSwitch,on:{click:t.rightSwitchClick}},[t._t("right-switch")],2):t._e(),t._v(" "),n("div",{ref:"realBox",style:t.pos,on:{mouseenter:t.enter,mouseleave:t.leave,touchstart:t.touchStart,touchmove:t.touchMove,touchend:t.touchEnd}},[n("div",{ref:"slotList",style:t.float},[t._t("default")],2),t._v(" "),n("div",{style:t.float,domProps:{innerHTML:t._s(t.copyHtml)}})])])},s=[],r={render:i,staticRenderFns:s};e.a=r}]).default}))},ba75:function(t,e,n){"use strict";n.r(e);var i=n("f9f0"),s=n("0891");for(var r in s)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(r);n("77eb"),n("90ed");var o=n("2877"),a=Object(o["a"])(s["default"],i["a"],i["b"],!1,null,"33c24eb1",null);e["default"]=a.exports},d795:function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/<EMAIL>"},ea8a:function(t,e,n){},f9f0:function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return s}));var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("fullscreen",{staticStyle:{width:"100%"},model:{value:t.fullscreen,callback:function(e){t.fullscreen=e},expression:"fullscreen"}},[i("div",{ref:"page-container",staticClass:"page-container"},[i("div",{staticClass:"team-wrapper"},[i("div",{staticClass:"NoticeBoardHeader"},[i("div",{staticClass:"company"}),i("div",{staticClass:"title"},[t._v("班组排名")]),i("div",{staticClass:"tool"},[i("div",{staticClass:"company-date"},[i("el-date-picker",{key:t.num,attrs:{type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd","append-to-body":!t.fullscreen,"popper-class":t.fullscreen?"team-rank-popover-class":"",align:t.fullscreen?"center":"left"},on:{change:t.getSearch},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}}),i("svg-icon",{staticClass:"screen-full",attrs:{"icon-class":t.fullscreen?"exit-fullscreen":"fullscreen"},on:{click:t.toggle}})],1)])]),i("div",{staticClass:"page-container-content"},[i("div",{staticClass:"contentItem"},[i("div",{staticClass:"header"},[i("img",{attrs:{src:n("9cfc"),alt:""}}),i("span",[t._v("班组一次合格率")])]),i("div",{staticClass:"conatinerBox borderLine"},[i("div",{staticClass:"conatiner"},[i("div",{staticClass:"otherTheChartScrollBox"},[i("vue-seamless-scroll",{staticClass:"otherTheChartScroll",attrs:{data:t.teamRankingList,step:.3}},[i("div",{staticClass:"otherTheChart"},t._l(t.teamRankingList,(function(e,s){return i("div",{key:s,staticClass:"otherTheChartItem"},[i("div",{staticClass:"otherTheChartItemTop",staticStyle:{"margin-left":"10px"}},[i("div",{staticClass:"number"},[t._v(t._s(s+4))]),i("div",{staticClass:"chartInfo",staticStyle:{"justify-content":"space-between"}},[i("div",{staticClass:"info",staticStyle:{"text-align":"center"}},[i("span",{staticClass:"title"},[t._v(t._s(e.Working_Team_Name))]),i("span",{staticClass:"precent"},[t._v(t._s(null===e.First_Pass_Rate?"-":e.First_Pass_Rate)+"%")])]),i("div",{staticClass:"progress"},[i("div",{staticClass:"progressBar",style:{width:(e.First_Pass_Rate||0)+"%"}})])])]),i("div",{staticClass:"otherTheChartItemBottom"},[i("img",{attrs:{src:n("6a45"),alt:""}})])])})),0)])],1),i("div",{staticClass:"theChartsBox"},t._l(t.teamRankingListTop,(function(e,s){return i("div",{key:s,staticClass:"theCharts"},[i("div",{staticClass:"trophyBox"},[0==s?i("img",{staticClass:"trophy",attrs:{src:n("2e0a"),alt:""}}):t._e(),1==s?i("img",{staticClass:"trophyOthers",attrs:{src:n("940e"),alt:""}}):t._e(),2==s?i("img",{staticClass:"trophyOthers",attrs:{src:n("4086"),alt:""}}):t._e(),0==s?i("span",{staticClass:"trophySpan"},[t._v(t._s(s+1))]):t._e(),0!=s?i("span",{staticClass:"trophyOthersSpan"},[t._v(t._s(s+1))]):t._e()]),i("div",{staticClass:"trophyInfo"},[i("div",{class:{infoAvator:0==s,infoAvatorOther:0!=s}},[e.Manager_Pic_Url?i("el-image",{staticStyle:{width:"100%",height:"100%","border-radius":"6px"},attrs:{src:e.Manager_Pic_Url,fit:"contain"}}):i("img",{attrs:{src:n("349f"),alt:""}})],1),i("div",{staticClass:"infoName"},[i("span",[t._v(t._s(e.Working_Team_Name)+" ")]),0==s?i("span",{staticStyle:{color:"#ffd15c","margin-left":"30px"}},[t._v(t._s(e.First_Pass_Rate)+"% ")]):t._e(),1==s?i("span",{staticStyle:{"margin-left":"15px"}},[t._v(t._s(e.First_Pass_Rate)+"% ")]):t._e(),2==s?i("span",{staticStyle:{color:"#da6700","margin-left":"15px"}},[t._v(t._s(e.First_Pass_Rate)+"% ")]):t._e()])])])})),0)])])]),i("div",{staticClass:"contentItem"},[i("div",{staticClass:"header"},[i("img",{attrs:{src:n("d795"),alt:""}}),i("span",[t._v("班组产量")])]),i("div",{staticClass:"conatinerBox borderLine"},[i("div",{staticClass:"conatiner"},[i("div",{staticClass:"theChartsBox"},t._l(t.productionRankingListTop,(function(e,s){return i("div",{key:s,staticClass:"theCharts"},[i("div",{staticClass:"trophyBox"},[0==s?i("img",{staticClass:"trophy",attrs:{src:n("2e0a"),alt:""}}):t._e(),1==s?i("img",{staticClass:"trophyOthers",attrs:{src:n("940e"),alt:""}}):t._e(),2==s?i("img",{staticClass:"trophyOthers",attrs:{src:n("4086"),alt:""}}):t._e(),0==s?i("span",{staticClass:"trophySpan"},[t._v(t._s(s+1))]):t._e(),0!=s?i("span",{staticClass:"trophyOthersSpan"},[t._v(t._s(s+1))]):t._e()]),i("div",{staticClass:"trophyInfo"},[i("div",{class:{infoAvator:0==s,infoAvatorOther:0!=s}},[e.Manager_Pic_Url?i("el-image",{staticStyle:{width:"100%",height:"100%","border-radius":"6px"},attrs:{src:e.Manager_Pic_Url,fit:"contain"}}):i("img",{attrs:{src:n("349f"),alt:""}})],1),i("div",{staticClass:"infoName"},[i("span",[t._v(t._s(e.Working_Team_Name)+" ")]),0==s?i("span",{staticStyle:{color:"#ffd15c","margin-left":"30px"}},[t._v(t._s(e.Yield)+"t ")]):t._e(),1==s?i("span",{staticStyle:{"margin-left":"15px"}},[t._v(t._s(e.Yield)+"t ")]):t._e(),2==s?i("span",{staticStyle:{color:"#da6700","margin-left":"15px"}},[t._v(t._s(e.Yield)+"t ")]):t._e()])])])})),0),i("div",{staticClass:"otherTheChartScrollBox"},[i("vue-seamless-scroll",{staticClass:"otherTheChartScroll",attrs:{data:t.productionRankingList,step:.3}},[i("div",{staticClass:"otherTheChart"},t._l(t.productionRankingList,(function(e,s){return i("div",{key:s,staticClass:"otherTheChartItem"},[i("div",{staticClass:"otherTheChartItemTop"},[i("div",{staticClass:"number"},[t._v(t._s(s+4))]),i("div",{staticClass:"chartInfo"},[i("div",{staticClass:"info",staticStyle:{display:"block"}},[i("div",{staticClass:"title"},[t._v(t._s(e.Working_Team_Name))]),i("div",{staticClass:"precent",staticStyle:{"margin-top":"10px"}},[t._v(t._s(e.Yield)+"t")])])])]),i("div",{staticClass:"otherTheChartItemBottom"},[i("img",{attrs:{src:n("6a45"),alt:""}})])])})),0)])],1)])])])])])])])},s=[]}}]);