(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-8e9c5f82"],{"15fd":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=i,n("a4d3");var r=a(n("ccb5"));function a(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(null==e)return{};var n,a,i=(0,r.default)(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(a=0;a<o.length;a++)n=o[a],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}},"1ac1":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("15fd"));n("4de4"),n("caad"),n("d81d"),n("14d9"),n("b0c0"),n("e9f5"),n("910d"),n("7d54"),n("ab43"),n("d3b7"),n("159b");var i=r(n("c14f")),o=r(n("1da1")),l=r(n("ac03")),c=n("cf45"),s=n("4f39"),u=r(n("3502")),d=n("d7ff"),f=n("8ff5"),p=n("05e0"),h=["Fee_Item"];t.default={name:"OMAProductionFeeDetailInfo",components:{VTable:u.default},mixins:[l.default],data:function(){return{form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],showExport:!1}},computed:{curTitle:function(){return"".concat((0,s.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")}},beforeCreate:function(){this.curModuleKey=f.curModuleKey},mounted:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.showExport=e.getRoles("OMAProFeeDetailExport"),e.form.FactoryId=e.factoryId,e.$route.query.d?e.form.StatisticalDate=e.$route.query.d:e.form.StatisticalDate=e.originDate,t.n=1,(0,c.getDictionary)("FeeType");case 1:return n=t.v,e.feeTypeOption=n.filter((function(e){return!["FeeRateWage","FeePiecework"].includes(e.Value)})),e.fetchData(),t.n=2,(0,c.getDictionary)("Engineering Status");case 2:e.projectOption=t.v;case 3:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.checkDate()&&(this.loading=!0,(0,d.GetSCFeesDailyDetailList)(this.form).then((function(t){if(t.IsSucceed){e.tableData=((null===t||void 0===t?void 0:t.Data)||[]).map((function(e){var t=e.Fee_Item,n=(0,a.default)(e,h);return t.forEach((function(e){n[e.Code]=e.Value})),n}));var n=e.setTotalData(e.tableData,e.generateColumn()),r=n.column;e.columns=r,e.$refs["tb"].setColumns(r)}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1})))},handleProductionFeeInfo:function(){this.$router.push({name:"OMAProductionFeeDetailWorker",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleProductionFeeOutInfo:function(){this.$router.push({name:"OMAOProductionFeeDetailOutsourcing",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var e=this,t=this.$createElement,n=180,r=[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:4},field:"ProjectAbbreviation",minWidth:p.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:p.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:p.ProjectStatusW}]}]},{title:"公共成本摊销系数(%)",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"PublicCostAmortizationFactor",minWidth:160}]}]},{title:"生产项目辅材成本",children:[{title:"辅材合价(元)",children:[{minWidth:n,field:"Project_Aux_Fees",title:0,isTotal:!0}]}]},{title:"生产公共辅材成本",children:[{title:"辅材合价(元)",children:[{minWidth:n,field:"Public_Aux_Fees",title:0,isTotal:!0}]}]},{slots:{header:function(){var n=[t("span",["劳资-生产工人"])];return e.getRoles("OMAProPeoDetail")&&n.push(t("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return e.handleProductionFeeInfo()}}},["查看详情"])),n}},children:[{title:"小计(元)",children:[{minWidth:n,field:"Process_Fees",title:0,isTotal:!0}]}]},{slots:{header:function(){var n=[t("span",["劳资-工序外包"])];return e.getRoles("OMAProProcessDetail")&&n.push(t("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return e.handleProductionFeeOutInfo()}}},["查看详情"])),n}},children:[{title:"小计(元)",children:[{minWidth:n,field:"External_Process_Fees",title:0,isTotal:!0}]}]},{title:"管理费用",children:[]}];if(this.feeTypeOption.length){var a=[];this.feeTypeOption.forEach((function(e,t){var r={title:e.Display_Name+"(元)",children:[{minWidth:n,field:e.Value,title:0,isTotal:!0}]};a.push(r)})),a.push({title:"小计(元)",children:[{minWidth:n,field:"total",title:0,isTotal:!0}]}),r[r.length-1].children=a}return r}}}},"209a":function(e,t,n){"use strict";n("62d7")},"3d5b":function(e,t,n){"use strict";n.r(t);var r=n("4efc"),a=n("c194b");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("209a");var o=n("2877"),l=Object(o["a"])(a["default"],r["a"],r["b"],!1,null,"c38931b2",null);t["default"]=l.exports},"4efc":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[n("div",{staticClass:"cs-z-page-main-content"},[n("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:e.form,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[n("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":e.pickerOptions},on:{change:e.fetchData},model:{value:e.form.StatisticalDate,callback:function(t){e.$set(e.form,"StatisticalDate",t)},expression:"form.StatisticalDate"}})],1),n("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[n("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:e.form.SearchKey,callback:function(t){e.$set(e.form,"SearchKey",t)},expression:"form.SearchKey"}})],1),n("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[n("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.ProjectStatus,callback:function(t){e.$set(e.form,"ProjectStatus",t)},expression:"form.ProjectStatus"}},e._l(e.projectOption,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("查询")]),n("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),n("el-divider"),n("div",{staticClass:"tb-info"},[n("label",[e._v("数据列表")]),n("div",{staticClass:"btn-x"},[e.showExport?n("el-button",{attrs:{disabled:e.isEmpty},on:{click:function(t){return e.handleExport(e.curTitle)}}},[e._v("导出报表")]):e._e()],1)]),n("div",{staticClass:"tb-x"},[n("v-table",{ref:"tb",attrs:{loading:e.loading,total:e.total},on:{setEmpty:e.setEmpty,pageChange:e.changePage}})],1)],1)])},a=[]},"4f39":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.parseTime=i,t.timeFormat=o,n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("4d90"),n("5319");var a=r(n("53ca"));function i(e,t){if(0===arguments.length||!e)return null;var n,r=t||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,a.default)(e)?n=e:("string"===typeof e&&(e=/^[0-9]+$/.test(e)?parseInt(e):e.replace(new RegExp(/-/gm),"/")),"number"===typeof e&&10===e.toString().length&&(e*=1e3),n=new Date(e));var i={y:n.getFullYear(),m:n.getMonth()+1,d:n.getDate(),h:n.getHours(),i:n.getMinutes(),s:n.getSeconds(),a:n.getDay()},o=r.replace(/{([ymdhisa])+}/g,(function(e,t){var n=i[t];return"a"===t?["日","一","二","三","四","五","六"][n]:n.toString().padStart(2,"0")}));return o}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!e)return"";if(-1!==e.indexOf("~")){var n=e.split("~"),r=i(new Date(n[0]),t)+" ~ "+i(new Date(n[1]),t);return r}return e&&e.length>0?i(new Date(e),t):void 0}},"62d7":function(e,t,n){},c194b:function(e,t,n){"use strict";n.r(t);var r=n("1ac1"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},ccb5:function(e,t,n){"use strict";function r(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},cf45:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=a,n("d3b7");var r=n("6186");function a(e){return new Promise((function(t,n){(0,r.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}}}]);