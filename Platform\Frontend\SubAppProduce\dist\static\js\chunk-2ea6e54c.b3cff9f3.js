(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2ea6e54c"],{"0149":function(e,t,n){"use strict";n.r(t);var a=n("d837"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"0227":function(e,t,n){},"0258":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("vxe-table",{key:2,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"500","show-overflow":"","empty-text":"无重复项！","row-style":e.rowStyle,"auto-resize":!0,size:"medium",data:e.resultArray,resizable:"","scroll-y":{enabled:!0},"tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}}],null,!0)})]}))],2),n("div",{staticClass:"footer"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("不合并手动处理")]),n("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.submit}},[e._v("合并后提交")])],1)],1)},i=[]},"04f1":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("2909")),r=a(n("c14f")),o=a(n("1da1"));n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("a434"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("a9e3"),n("b680"),n("b64b"),n("d3b7"),n("159b");var s=n("cf45"),l=n("ed08"),u=n("30c8");t.default={props:{isView:{type:Boolean,default:!1},isReplacePurchase:{type:Boolean,default:!1},projectOptions:{type:Array,required:!0,default:function(){return[]}},formStatus:{type:Number,default:0},checkTypeList:{type:Object,required:!0,default:function(){}},SupplierData:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{tbLoading:!1,itemKey:"",renderComponent:!0,multipleSelection:[],textureOption:[],rootColumns:[],columns:[],tbData:[],texture:{}}},inject:["checkDuplicate"],watch:{"tbData.length":{handler:function(e,t){this.tbData.map((function(e,t){return e.index=t,e}))},immediate:!0},isReplacePurchase:{handler:function(e,t){e?(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"SupplierName"!==e.Code||(e.Is_Edit=!1,e.Is_Must_Input=!1)})),this.init(this.rootColumns),this.forceRerender()):(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"SupplierName"!==e.Code||(e.Is_Edit=!0)})),this.init(this.rootColumns),this.forceRerender())},immediate:!1}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTexture();case 1:return t.a(2)}}),t)})))()},mounted:function(){return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{changeKeyInfo:function(e,t){var n=this.tbData.map((function(t){return t[e]})),a=(0,l.uniqueArr)(n);1===a.length?this.$emit("changeKeyInfo",{key:e,val:t}):this.$emit("changeKeyInfo",{key:e,val:""})},changeFormInfo:function(e,t){var n=this;this.tbData.forEach((function(a){n.$set(a,e,t)}))},handleCopy:function(e,t){var n=JSON.parse(JSON.stringify(e));n.Sub_Id&&(n.Sub_Id=""),delete n._X_ROW_KEY,this.$emit("updateTb",[n]),this.tbData.splice(t+1,0,n)},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},init:function(e){this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){this.itemKey=Math.random(),this.columns=JSON.parse(JSON.stringify(this.rootColumns))},getAllColumnsOption:function(){var e=JSON.parse(JSON.stringify(this.rootColumns));this.getCheckList(e)},getCheckList:function(e){var t=[];this.checkDuplicate()&&(t=["ProjectName","Project_Code","SupplierName","CategoryName","RawName","RawCode","Material","Spec","Measure_Unit","TaxUnitPrice","Tax_Rate","Warehouse_Location"]),this.checkTypeList.checkSameList=t,this.checkTypeList.checkList=e.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),this.checkTypeList.checkList=this.checkTypeList.checkList.map((function(e){return e.Code})),this.checkTypeList.remarkList=e.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),this.checkTypeList.remarkList=this.checkTypeList.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}}))},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},checkNum:function(e){Number(e.InStoreCount)>0||(e.InStoreCount=e.PurchaseCount),this.countTaxUnitPrice(e),this.$emit("updateRow")},addData:function(e){var t,n=this,a=e.map((function(e){return e.TaxUnitPrice=e.TaxUnitPrice||0,"number"!==typeof e.Tax_Rate&&(e.Tax_Rate=13),n.checkNum(e),e.rowIndex="",e}));(t=this.tbData).push.apply(t,(0,i.default)(a)),this.tbData=JSON.parse(JSON.stringify(this.tbData))},tbfetchData:function(){this.tbData=[]},projectChange:function(e){e.SysProjectId?(e.ProjectName=this.projectOptions.find((function(t){return t.Sys_Project_Id===e.SysProjectId})).Short_Name,e.Project_Code=this.projectOptions.find((function(t){return t.Sys_Project_Id===e.SysProjectId})).Code):(e.ProjectName="",e.Project_Code=""),this.$emit("updateRow")},supplierChange:function(e){e.Supplier?e.SupplierName=this.SupplierData.find((function(t){return t.Id===e.Supplier})).Name:e.SupplierName="",this.$emit("updateRow")},countTaxUnitPrice:function(e){0===e.InStoreCount||0===e.TaxUnitPrice?e.Tax_All_Price=0:e.InStoreCount&&e.TaxUnitPrice?e.Tax_All_Price=Number((e.InStoreCount*e.TaxUnitPrice).toFixed(2)):(e.Tax_All_Price="",this.$emit("updateRow")),this.taxAllPriceChange(e)},taxAllPriceChange:function(e){e.NoTaxAllPrice=(0,u.getNoTaxAllPrice)(e),e.NoTaxUnitPrice=(e.TaxUnitPrice/(1+e.Tax_Rate/100)).toFixed(3)/1||0},countChange:function(e,t){var n=e.value;t.PerWtg&&(t.TotalPerWtg=numeral(n*t.PerWtg).format("0.[00]")),t.PerLen&&(t.TotalPerLen=numeral(n*t.PerLen).format("0.[00]"))},getTextureType:function(e){var t=this.textureOption.find((function(t){return t.Id===e}));return t&&t.Display_Name},getTexture:function(){var e=this;(0,s.getDictionary)("Texture").then((function(t){e.textureOption=t,e.textureOption.forEach((function(t,n){e.$set(e.texture,t.Value,t.Display_Name)}))}))},changeInStoreCount:function(e){}}}},"071b":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("4de4"),n("d81d"),n("e9f5"),n("910d"),n("ab43"),n("d3b7"),n("ac1f"),n("3ca3"),n("841c"),n("ddb0");var i=a(n("5530")),r=a(n("c14f")),o=a(n("1da1")),s=n("ed08"),l=n("5480"),u=n("93aa"),c=a(n("1463")),d=a(n("333d")),f=n("c685");t.default={components:{TreeDetail:c.default,Pagination:d.default},props:{isSingle:{type:Boolean,default:!1}},data:function(){return{tablePageSize:f.tablePageSize,treeLoading:!1,expandedKey:"",treeData:[],Category_Id:"",form:{Materiel_Name:"",Materiel_Code:"",Spec:""},selectRow:null,tbLoading:!1,saveLoading:!1,queryInfo:{Page:1,PageSize:20,ParameterJson:[]},total:0,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTreeList();case 1:e.getConfig(),e.search=(0,s.debounce)(e.fetchData,800,!0);case 2:return t.a(2)}}),t)})))()},methods:{getTreeList:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.treeLoading=!0,t.n=1,(0,u.GetAuxCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeData=[{Label:"全部",Id:"all",Data:{Category:{Name:"",Pid:""}}}].concat(t.Data),e.expandedKey=e.treeData[0].Id,e.Category_Id=e.treeData[0].Id):e.$message({message:t.Message,type:"error"}),e.treeLoading=!1})).catch((function(){e.treeLoading=!1}));case 1:return t.a(2)}}),t)})))()},handleNodeClick:function(e){this.queryInfo.Page=1,this.Category_Id=e.Id,this.fetchData()},setRow:function(e){},getConfig:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.getTableConfig)("PROAddManualAuxList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},searchReset:function(){this.$refs["form"].resetFields(),this.fetchData()},fetchData:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,u.GetAuxPageList)((0,i.default)((0,i.default)({Category_Id:"all"===e.Category_Id?"":e.Category_Id},e.form),e.queryInfo)).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data.filter((function(e){return e.Is_Enabled})),e.total=t.Data.TotalCount,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(e){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.BigType=e.Big_Type,e.RawCode=e.Code,e.CategoryId=e.Category_Id,e.RawName=e.Name,e}))),e&&this.$emit("close")},handleClose:function(){this.$emit("close")},changePage:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=20),Promise.all([e.fetchData()]).then((function(e){}));case 1:return t.a(2)}}),t)})))()}}}},"08a0":function(e,t,n){"use strict";n("59ea")},"0d37":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"contentBox"},[n("div",{staticStyle:{display:"flex","justify-content":"flex-start"}},[n("div",{staticStyle:{width:"300px",border:"1px solid #E8E8E8","border-radius":"5px","padding-top":"15px","margin-right":"20px"}},[n("div",{staticStyle:{"font-size":"16px","padding-left":"10px","font-weight":"600"}},[e._v("物料类别")]),n("div",[n("tree-detail",{ref:"tree",staticStyle:{"margin-top":"20px",height:"50vh"},attrs:{icon:"icon-folder",loading:e.treeLoading,"tree-data":e.treeData,"show-detail":"","expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick}})],1)]),n("div",{staticStyle:{flex:"1"}},[n("el-form",{ref:"form",attrs:{inline:"",model:e.form,"label-width":"120"}},[n("el-form-item",{attrs:{label:"辅料名称",prop:"Materiel_Name"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Materiel_Name,callback:function(t){e.$set(e.form,"Materiel_Name",t)},expression:"form.Materiel_Name"}})],1),n("el-form-item",{attrs:{label:"唯一编码",prop:"Materiel_Code"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Materiel_Code,callback:function(t){e.$set(e.form,"Materiel_Code",t)},expression:"form.Materiel_Code"}})],1),n("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")]),n("el-button",{staticStyle:{"margin-left":"10px"},on:{click:e.searchReset}},[e._v("重置")]),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(t){return e.handleSave(!1)}}},[e._v("加入列表")])],1),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?n("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[n("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(e){return[n("vxe-column",{key:e.Code,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:e.Code,title:e.Display_Name,"min-width":e.Width}})]}))],2)],1),n("div",{staticStyle:{"margin-top":"10px",display:"flex","justify-content":"space-between"}},[n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),n("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)],1)]),n("div",{staticClass:"button"},[n("el-button",{on:{click:e.handleClose}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:function(t){return e.handleSave(!0)}}},[e._v("保存 ")])],1)])},i=[]},"0e51":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"contentBox"},[n("el-tabs",{on:{"tab-click":e.handleSearch},model:{value:e.form.IsFinished,callback:function(t){e.$set(e.form,"IsFinished",t)},expression:"form.IsFinished"}},[n("el-tab-pane",{attrs:{label:"未完成",name:"0"}}),n("el-tab-pane",{attrs:{label:"已完成",name:"1"}})],1),n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[n("el-row",[n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"采购单号:",prop:"OrderCode"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.OrderCode,callback:function(t){e.$set(e.form,"OrderCode","string"===typeof t?t.trim():t)},expression:"form.OrderCode"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"辅料分类:",prop:"CategoryId"}},[n("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.form.CategoryId,callback:function(t){e.$set(e.form,"CategoryId",t)},expression:"form.CategoryId"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"辅料名称:",prop:"RawName"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"供应商:",prop:"SupplierId"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.SupplierId,callback:function(t){e.$set(e.form,"SupplierId","string"===typeof t?t.trim():t)},expression:"form.SupplierId"}})],1)],1),e.formData.Is_Replace_Purchase?e._e():n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"所属项目:",prop:"SysProjectId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}},e._l(e.projectOptions,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),n("el-button",{staticStyle:{"margin-left":"10px"},on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.addToList}},[e._v("加入列表")])],1)],1)],1),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["PurchaseWeight"===t.Code?{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e._f("displayValue")(a[t.Code]))+"/"+e._s(a["WaitInStoreWeight"])+" ")]}}:"DeliveryTime"===t.Code?{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e._f("timeFormat")(a[t.Code],"{y}-{m}-{d}"))+" ")]}}:"PurchaseTime"===t.Code?{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e._f("timeFormat")(a[t.Code],"{y}-{m}-{d}"))+" ")]}}:"InStoreCount"===t.Code?{key:"default",fn:function(a){var i=a.row;return[n("vxe-input",{attrs:{min:0,max:i.AvailableCount,type:"number"},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})]}}:{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(a[t.Code]||"-")+" ")]}}],null,!0)})]}))],2)],1),n("div",{staticClass:"button"},[n("el-button",{on:{click:e.handleClose}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},"0ff4":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("caad"),n("2532");a(n("6612")),t.default={props:{columns:{type:Array,default:function(){return[]}}},data:function(){return{resultArray:[],indexes:[],loading:!1}},methods:{setLoading:function(e){this.loading=e},submit:function(){this.loading=!0,this.$emit("submit",1)},setTbData:function(e,t){this.resultArray=e,this.indexes=t},rowStyle:function(e){var t=e.rowIndex;if(this.indexes.includes(t))return{backgroundColor:"#ED8591",color:"#ffffff"}}}}},1024:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("c14f")),r=a(n("1da1"));n("d81d"),n("e9f5"),n("ab43"),n("d3b7"),n("ac1f"),n("841c");var o=n("ed08"),s=n("5480"),l=n("2245");t.default={props:{isSingle:{type:Boolean,default:!1}},data:function(){return{form:{Name:""},selectRow:null,tbLoading:!1,saveLoading:!1,columns:[],fTable:[],tbConfig:{},multipleSelection:[],search:function(){return{}}}},computed:{},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getConfig(),this.search=(0,o.debounce)(this.fetchData,800,!0)},methods:{setRow:function(e){},getConfig:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PRORawList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},radioChangeEvent:function(e){var t=e.row;this.selectRow=t},clearRadioRowEevnt:function(){this.selectRow=null,this.$refs.xTable1.clearRadioRow()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetRawMaterialPageList)({RawName:e.form.Name,IsActive:!0,PageInfo:{Page:1,PageSize:99999}}).then((function(t){t.IsSucceed?(e.fTable=t.Data.Data,e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.isSingle?this.$emit("getRowName",this.selectRow):this.$emit("getAddList",this.multipleSelection.map((function(e){return e.RawId=e.Id,e.RcptRawParams=e.RawSpParams,delete e.Id,e}))),this.$emit("close")},handleClose:function(){this.$emit("close")}}}},1172:function(e,t,n){"use strict";n.r(t);var a=n("0d37"),i=n("887b");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("56d5");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"481defff",null);t["default"]=s.exports},"21c9":function(e,t,n){"use strict";n("5131")},"26d8":function(e,t,n){"use strict";n.r(t);var a=n("8f73"),i=n("0149");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("e6b1");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"adc99a36",null);t["default"]=s.exports},"28b0":function(e,t,n){"use strict";n.r(t);var a=n("bbd2"),i=n("edce");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("7fc7");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"370349c8",null);t["default"]=s.exports},2906:function(e,t,n){"use strict";n.r(t);var a=n("0ff4"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"2a4f":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["InStoreCount"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.InStoreCount))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{min:0,type:"number"},model:{value:a.InStoreCount,callback:function(t){e.$set(a,"InStoreCount",e._n(t))},expression:"row.InStoreCount"}})]}}],null,!0)}):"Material"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.Material))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.Material,callback:function(t){e.$set(a,"Material",e._n(t))},expression:"row.Material"}})]}}],null,!0)}):"TaxUnitPrice"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.TaxUnitPrice))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{min:0,type:"number"},model:{value:a.TaxUnitPrice,callback:function(t){e.$set(a,"TaxUnitPrice",e._n(t))},expression:"row.TaxUnitPrice"}})]}}],null,!0)}):"WarehouseId"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"edit",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.WarehouseName)+"/"+e._s(a.LocationName)+" "),n("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(a)}}})]}},{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.WarehouseName)+"/"+e._s(n.LocationName)+" ")]}}],null,!0)}):"Remark"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.Remark))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.Remark,callback:function(t){e.$set(a,"Remark",t)},expression:"row.Remark"}})]}}],null,!0)}):"Delivery_No"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.Delivery_No))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.Delivery_No,callback:function(t){e.$set(a,"Delivery_No",t)},expression:"row.Delivery_No"}})]}}],null,!0)}):"CarNumber"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.CarNumber))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.CarNumber,callback:function(t){e.$set(a,"CarNumber",t)},expression:"row.CarNumber"}})]}}],null,!0)}):"Driver"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.Driver))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.Driver,callback:function(t){e.$set(a,"Driver",t)},expression:"row.Driver"}})]}}],null,!0)}):"DriverMobile"===t.Code?n("vxe-column",{key:t.Code,attrs:{field:t.Code,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"",title:t.Display_Name,"min-width":t.Width,"edit-render":e.isView?null:{},sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(e._s(a.DriverMobile))])]}},{key:"edit",fn:function(t){var a=t.row;return[n("vxe-input",{attrs:{type:"text"},model:{value:a.DriverMobile,callback:function(t){e.$set(a,"DriverMobile",t)},expression:"row.DriverMobile"}})]}}],null,!0)}):n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width}})]}))],2)},i=[]},"2ce6":function(e,t,n){"use strict";n.r(t);var a=n("5764"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},3069:function(e,t,n){"use strict";n.r(t);var a=n("d4da"),i=n("776f");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"11b31aa1",null);t["default"]=s.exports},3317:function(e,t,n){"use strict";n.r(t);var a=n("ce62"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"40cad":function(e,t,n){},"4e34":function(e,t,n){"use strict";n.r(t);var a=n("c1dd"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},5131:function(e,t,n){},"56d5":function(e,t,n){"use strict";n("0227")},5764:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("2909")),r=a(n("c14f")),o=a(n("1da1"));n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("a434"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("a9e3"),n("b680"),n("b64b"),n("d3b7"),n("159b");var s=n("cf45"),l=n("ed08"),u=n("30c8");t.default={props:{isView:{type:Boolean,default:!1},isReplacePurchase:{type:Boolean,default:!1},checkTypeList:{type:Object,required:!0,default:function(){}},formStatus:{type:Number,default:0},projectOptions:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}}},data:function(){return{itemKey:"",tbLoading:!1,renderComponent:!0,multipleSelection:[],textureOption:[],rootColumns:[],columns:[],tbData:[],texture:{}}},inject:["checkDuplicate"],watch:{isReplacePurchase:{handler:function(e,t){e?(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"PartyUnitName"!==e.Code||(e.Is_Edit=!1,e.Is_Must_Input=!1)})),this.init(this.rootColumns),this.forceRerender()):(this.rootColumns.map((function(e,t){"ProjectName"!==e.Code&&"PartyUnitName"!==e.Code||(e.Is_Edit=!0,e.Is_Must_Input=!0)})),this.init(this.rootColumns),this.forceRerender())},immediate:!1}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTexture();case 1:return t.a(2)}}),t)})))()},mounted:function(){return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{changeKeyInfo:function(e,t){var n=this.tbData.map((function(t){return t[e]})),a=(0,l.uniqueArr)(n);1===a.length?this.$emit("changeKeyInfo",{key:e,val:t}):this.$emit("changeKeyInfo",{key:e,val:""})},changeFormInfo:function(e,t){var n=this;this.tbData.forEach((function(a){n.$set(a,e,t)}))},handleCopy:function(e,t){var n=JSON.parse(JSON.stringify(e));n.Sub_Id&&(n.Sub_Id=""),delete n._X_ROW_KEY,this.$emit("updateTb",[n],"copy"),this.tbData.splice(t+1,0,n)},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},init:function(e){this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){this.itemKey=Math.random(),this.columns=JSON.parse(JSON.stringify(this.rootColumns))},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},checkNum:function(e){Number(e.InStoreCount)>0||(e.InStoreCount=e.PurchaseCount),this.countTaxUnitPrice(e)},getAllColumnsOption:function(){var e=JSON.parse(JSON.stringify(this.rootColumns));this.getCheckList(e)},getCheckList:function(e){var t=[];this.checkDuplicate()&&(t=["ProjectName","Project_Code","PartyUnitName","CategoryName","RawName","RawCode","Material","Spec","Measure_Unit","TaxUnitPrice","Tax_Rate","Warehouse_Location"]),this.checkTypeList.checkSameList=t,this.checkTypeList.checkList=e.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),this.checkTypeList.checkList=this.checkTypeList.checkList.map((function(e){return e.Code})),this.checkTypeList.remarkList=e.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),this.checkTypeList.remarkList=this.checkTypeList.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}}))},addData:function(e){var t,n=this,a=e.map((function(e){return e.rowIndex="",e.TaxUnitPrice=e.TaxUnitPrice||0,"number"!==typeof e.Tax_Rate&&(e.Tax_Rate=13),n.checkNum(e),e}));(t=this.tbData).push.apply(t,(0,i.default)(a)),this.tbData=JSON.parse(JSON.stringify(this.tbData))},tbfetchData:function(){this.tbData=[]},projectChange:function(e){e.ProjectName=this.projectOptions.find((function(t){return t.Sys_Project_Id===e.SysProjectId})).Short_Name,e.Project_Code=this.projectOptions.find((function(t){return t.Sys_Project_Id===e.SysProjectId})).Code,this.$emit("updateRow")},partyUnitNameChange:function(e){e.PartyUnitName=this.partyUnitList.find((function(t){return t.Id===e.PartyUnit})).Name,this.$emit("updateRow")},countTaxUnitPrice:function(e){0===e.InStoreCount||0===e.TaxUnitPrice?e.Tax_All_Price=0:e.InStoreCount&&e.TaxUnitPrice?e.Tax_All_Price=Number((e.InStoreCount*e.TaxUnitPrice).toFixed(2)):e.Tax_All_Price="",this.taxAllPriceChange(e),this.$emit("updateRow")},taxAllPriceChange:function(e){e.NoTaxAllPrice=(0,u.getNoTaxAllPrice)(e),e.NoTaxUnitPrice=(e.TaxUnitPrice/(1+e.Tax_Rate/100)).toFixed(3)/1||0},countChange:function(e,t){var n=e.value;t.PerWtg&&(t.TotalPerWtg=numeral(n*t.PerWtg).format("0.[00]")),t.PerLen&&(t.TotalPerLen=numeral(n*t.PerLen).format("0.[00]"))},getTextureType:function(e){var t=this.textureOption.find((function(t){return t.Id===e}));return t&&t.Display_Name},getTexture:function(){var e=this;(0,s.getDictionary)("Texture").then((function(t){e.textureOption=t,e.textureOption.forEach((function(t,n){e.$set(e.texture,t.Value,t.Display_Name)}))}))},changeInStoreCount:function(e){}}}},"5968e":function(e,t,n){"use strict";n.r(t);var a=n("a03d"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"59ea":function(e,t,n){},"5ac8":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e._l(e.list,(function(t,a){return n("el-row",{key:t.id,staticClass:"item-x"},[n("div",{staticClass:"item"},[n("label",[e._v(" 属性名称 "),n("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.key,callback:function(n){e.$set(t,"key",n)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return n("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),n("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[n("label",[e._v("请输入值 "),e.checkType(t.key,"number")?n("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(n){e.$set(t,"val",n)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?n("el-input",{model:{value:t.val,callback:function(n){e.$set(t,"val",n)},expression:"info.val"}}):e._e(),e.checkType(t.key,"supplierArray")?n("el-select",{attrs:{placeholder:"请选择供应商",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"supplierArray")}},model:{value:t.val,callback:function(n){e.$set(t,"val",n)},expression:"info.val"}},e._l(e.SupplierData,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"partyUnitArray")?n("el-select",{attrs:{placeholder:"请选择甲方单位",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"partyUnitArray")}},model:{value:t.val,callback:function(n){e.$set(t,"val",n)},expression:"info.val"}},e._l(e.partyUnitList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1):e._e(),e.checkType(t.key,"projectArray")?n("el-select",{attrs:{placeholder:"请选择所属项目",clearable:"",filterable:""},on:{change:function(t){return e.optionChange(t,"projectArray")}},model:{value:t.val,callback:function(n){e.$set(t,"val",n)},expression:"info.val"}},e._l(e.projectOptions,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1):e._e()],1)]),n("span",{staticClass:"item-span"},0===a?[n("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[n("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),n("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(a)}}})])])})),n("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},i=[]},"5b80":function(e,t,n){"use strict";n.r(t);var a=n("7c59"),i=n("b958");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("08a0");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"21bc4d42",null);t["default"]=s.exports},"5c34":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("c14f")),r=a(n("1da1"));n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("13d5"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("9485"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7");var o=n("93aa");t.default={props:{isView:{type:Boolean,default:!1},checkTypeList:{type:Object,required:!0,default:function(){}},inStoreType:{type:Number,default:1},formStatus:{type:Number,default:0}},data:function(){return{tbLoading:!1,multipleSelection:[],suppliersOption:[],rootColumns:[],columns:[],tbData:[],texture:{},itemKey:"",totalReturnNum:0}},watch:{},created:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getSuppliers();case 1:return t.a(2)}}),t)})))()},mounted:function(){return(0,r.default)((0,i.default)().m((function e(){return(0,i.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},inject:["checkDuplicate"],methods:{init:function(e){this.rootColumns=JSON.parse(JSON.stringify(e)),this.rootColumns.push({Code:"AvailableCount",Display_Name:"可退数量",Is_Edit:!1,fixed:"right",Is_Display:!0,Width:120},{Code:"OutStoreCount",Display_Name:"退货数量",Is_Edit:!0,fixed:"right",Is_Display:!0,Is_Must_Input:!1,Width:150},{Code:"ReturnSupplierName",Display_Name:"退货供应商",Is_Edit:!0,fixed:"right",Is_Display:3===this.inStoreType,Is_Must_Input:!1,Width:150}),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){this.itemKey=Math.random(),this.columns=JSON.parse(JSON.stringify(this.rootColumns))},getCheckList:function(e){this.checkTypeList.checkList=e.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),this.checkTypeList.checkList=this.checkTypeList.checkList.map((function(e){return e.Code})),this.checkTypeList.remarkList=e.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),this.checkTypeList.remarkList=this.checkTypeList.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}}))},getAllColumnsOption:function(){var e=JSON.parse(JSON.stringify(this.rootColumns));this.getCheckList(e)},getSuppliers:function(){var e=this;(0,o.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){var n=[];for(var a in t.Data){var i={Id:a,Name:t.Data[a]};n.push(i)}e.suppliersOption=n}))},changeSupplier:function(e,t){var n;e.value?t.ReturnSupplierName=null===(n=this.suppliersOption.find((function(t){return t.Id===e.value})))||void 0===n?void 0:n.Name:t.ReturnSupplierName=""},changeNum:function(e){this.totalReturnNum=0,this.totalReturnNum=this.tbData.reduce((function(e,t){return e+Number((null===t||void 0===t?void 0:t.OutStoreCount)||0)}),0),this.$emit("returnNum",this.totalReturnNum)}}}},"5d80":function(e,t,n){"use strict";n.r(t);var a=n("5c34"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},6240:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("2909")),r=a(n("c14f")),o=a(n("1da1")),s=a(n("5530"));n("99af"),n("4de4"),n("7db0"),n("c740"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("13d5"),n("fb6a"),n("a434"),n("b0c0"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("9485"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7"),n("4d63"),n("c607"),n("ac1f"),n("2c3e"),n("00b4"),n("25f0"),n("2532"),n("38cf"),n("841c"),n("498a"),n("159b");var l=n("ed08"),u=a(n("8bbc7")),c=a(n("ac65")),d=a(n("3069")),f=a(n("85e9")),h=a(n("c9bd0")),p=a(n("5b80")),m=a(n("b0eb")),v=a(n("1172")),b=a(n("26d8")),y=a(n("28b0")),g=a(n("a657")),_=n("93aa"),S=n("6186"),C=(a(n("6612")),n("5480")),x=n("3166"),w=a(n("c7ab")),I=a(n("b76a")),k=n("2f62"),D=a(n("fe08")),N=a(n("a6bb")),P=n("e144");t.default={components:{Repeat:D.default,RawManualTb:u.default,AddPurchaseList:m.default,PurchaseTb:c.default,AsupplyTb:f.default,ManualTb:d.default,ReturnTb:N.default,BatchEdit:h.default,ImportFile:b.default,Warehouse:y.default,AddList:p.default,AddAuxMaterialList:v.default,OSSUpload:w.default,DynamicTableFields:g.default,draggable:I.default},props:{pageType:{type:Number,default:void 0}},data:function(){var e=this;return{dialogRepeatVisible:!1,popoverVisible:!1,isRetract:!1,tbLoading:!1,projectOptions:[],multipleSelection:[],formStatus:+this.$route.query.status||"",PartyUnitData:[],SupplierData:[],form:{InStoreNo:"",Delivery_No:"",Is_Replace_Purchase:!1,InStoreType:parseInt(this.$route.query.type)||1,InStoreDate:this.getDate(),CarNumber:"",Driver:"",DriverMobile:"",Remark:"",PartyUnit:"",Supplier:"",ProjectName:"",ProjectId:"",SysProjectId:"",PartyUnitName:"",SupplierName:"",Attachment:"",Status:null},PartyUnitList:[],rootColumns:[],tableConfigCode:"",manualHideTableColumns:[],searchNum:1,searchForm:{RawName:"",Spec:"",SysProjectId:""},checkTypeList:{checkSameList:[],checkList:[],remarkList:[]},rules:{InStoreType:[{required:!0,message:"请选择",trigger:"change"}],InStoreDate:[{required:!0,message:"请选择日期",trigger:"change"}],ProjectId:[{required:!0,message:"请选择所属项目",trigger:"change"}],PartyUnit:[{required:!0,message:"请选择甲方单位",trigger:"change"}],Supplier:[{required:!0,message:"请输入供应商",trigger:"change"}]},currentComponent:"",title:"",dWidth:"60%",isSingle:!1,saveLoading:!1,saveSubmitLoading:!1,saveReturnLoading:!1,backendDate:null,pickerOptions:{disabledDate:function(t){return t.getTime()<new Date(e.backendDate).getTime()}},search:function(){return{}},openAddList:!1,dialogVisible:!1,rootTableData:[],fileListData:[],fileListArr:[],statisticsData:{InStoreCountTotal:0,Tax_All_Price:0},tableData:[]}},computed:(0,s.default)({isView:function(){return 3===this.pageType},isAdd:function(){return 1===this.pageType},isEdit:function(){return 2===this.pageType},isReturn:function(){return 4===this.pageType},isPurchase:function(){return 1===this.form.InStoreType},isAsupply:function(){return 2===this.form.InStoreType},isManual:function(){return 3===this.form.InStoreType},currentTbComponent:function(e){var t=e.isPurchase,n=e.isAsupply,a=e.isManual,i=e.isReturn;return i?N.default:t?c.default:n?f.default:a?d.default:u.default}},(0,k.mapGetters)("factoryInfo",["checkDuplicate"])),watch:{tableData:{deep:!0,handler:function(e){}}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getFactoryInfo();case 1:return t.n=2,e.getSuppliers();case 2:return t.n=3,e.getTableColumns();case 3:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getOMALatestStatisticTime();case 1:return e.search=(0,l.debounce)(e.fetchData,800,!0),t.n=2,e.getProject();case 2:return t.n=3,e.getPartyAs();case 3:if(e.isAdd){t.n=4;break}return t.n=4,e.getInfo();case 4:return t.a(2)}}),t)})))()},provide:function(){return{formData:this.form,checkDuplicate:this.getDuplicate}},methods:{changeKeyInfo:function(e){var t=e.key,n=e.val;this.form[t]=n},changeFormKey:function(e){this.$refs["table"].changeFormInfo(e,this.form[e])},getDuplicate:function(){return this.checkDuplicate},getFactoryInfo:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("factoryInfo/getWorkshop");case 1:return t.a(2)}}),t)})))()},getOMALatestStatisticTime:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,_.GetOMALatestStatisticTime)({});case 1:n=t.v,n.IsSucceed?e.backendDate=n.Data||"":e.message.error(n.Mesaage);case 2:return t.a(2)}}),t)})))()},getPartyAs:function(){var e=this;(0,_.GetPartyAs)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var n=[];for(var a in t.Data){var i={Id:a,Name:t.Data[a]};n.push(i)}e.PartyUnitList=n}else e.$message({message:t.Message,type:"error"})}))},handleSaveTbSet:function(){},isReplacePurchaseChange:function(e){e?(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset()):(this.form.ProjectId="",this.form.SysProjectId="",this.form.Supplier="",this.form.PartyUnit="")},handleUpdateTb:function(e,t){var n,a;e.map((function(e,t){e.index=(0,P.v4)()}));var r=JSON.parse(JSON.stringify(e));"add"===t&&this.$refs["table"].addData(r),(n=this.tableData).push.apply(n,(0,i.default)(r)),(a=this.rootTableData).push.apply(a,(0,i.default)(r)),this.countStatistics()},handleUpdateRow:function(){this.rootTableData=JSON.parse(JSON.stringify(this.$refs.table.tbData)),this.countStatistics()},handleRetract:function(){this.isRetract=!this.isRetract},uploadSuccess:function(e,t,n){this.fileListArr=JSON.parse(JSON.stringify(n))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},updateColumn:function(e){this.getTableColumns()},getTableColumns:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(1!==e.form.InStoreType){t.n=2;break}return t.n=1,(0,C.getTableConfig)("PROPurchaseAuxListNew");case 1:e.rootColumns=t.v,e.tableConfigCode="PROPurchaseAuxListNew",t.n=6;break;case 2:if(2!==e.form.InStoreType){t.n=4;break}return t.n=3,(0,C.getTableConfig)("PROAsupplyAuxListNew");case 3:e.rootColumns=t.v,e.tableConfigCode="PROAsupplyAuxListNew",t.n=6;break;case 4:if(3!==e.form.InStoreType){t.n=6;break}return t.n=5,(0,C.getTableConfig)("PROManualAuxListNew");case 5:e.rootColumns=t.v,e.tableConfigCode="PROManualAuxListNew";case 6:e.$refs["table"].init(e.rootColumns);case 7:return t.a(2)}}),t)})))()},getManualHideTableColumns:function(){this.manualHideTableColumns=this.rootColumns},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})},handlePreview:function(e){return(0,o.default)((0,r.default)().m((function t(){var n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(n="",!e.response||!e.response.encryptionUrl){t.n=1;break}n=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,S.GetOssUrl)({url:e.encryptionUrl});case 2:n=t.v,n=n.Data;case 3:window.open(n);case 4:return t.a(2)}}),t)})))()},fetchData:function(){},getInfo:function(){var e=this,t=this.isReturn?_.GetAuxDetailByReceipt:1===this.form.InStoreType?_.PurchaseAuxInStoreDetail:2===this.form.InStoreType?_.PartyAInInStoreDetail:(this.form.InStoreType,_.ManualAuxInStoreDetail);t({inStoreNo:this.$route.query.id}).then((function(t){if(t.IsSucceed){var n=t.Data.Receipt,a=t.Data.Sub,i=n.InStoreDate,r=n.CarNumber,o=n.Driver,s=n.DriverMobile,l=n.Remark,u=n.SysProjectId,c=n.ProjectId,d=n.PartyUnit,f=n.Supplier,h=n.Attachment,p=n.Status,m=n.Delivery_No,v=n.Purchase_Contract_No,b=n.Is_Replace_Purchase;if(e.form.InStoreDate=e.getDate(new Date(i)),e.form.CarNumber=r,e.form.Driver=o,e.form.DriverMobile=s,e.form.Remark=l,e.form.ProjectName=u?e.projectOptions.find((function(e){return e.Sys_Project_Id===u})).Short_Name:"",e.form.ProjectId=c,e.form.SysProjectId=u,e.form.SupplierName=f?e.SupplierData.find((function(e){return e.Id===f})).Name:"",e.form.PartyUnit=d,e.form.Supplier=f,e.form.Status=p,e.form.Delivery_No=m,e.form.Purchase_Contract_No=v,e.form.Is_Replace_Purchase=b,h){e.form.Attachment=h;var y=h.split(",");y.forEach((function(t){var n=t.indexOf("?Expires=")>-1?t.substring(0,t.lastIndexOf("?Expires=")):t,a=decodeURI(n.substring(n.lastIndexOf("/")+1)),i={};i.name=a,i.url=n,i.encryptionUrl=n,e.fileListData.push(i),e.fileListArr.push(i)}))}var g=a.map((function(e){return e.index=(0,P.v4)(),e.Warehouse_Location=e.WarehouseName?e.WarehouseName+"/"+e.LocationName:"",e}));e.$nextTick((function(t){e.isReturn?e.$refs["table"].tbData=g.map((function(e){return e.isEditSupplier=!e.ReturnSupplierName,e})):e.$refs["table"].tbData=g,e.tableData=JSON.parse(JSON.stringify(g)),e.rootTableData=JSON.parse(JSON.stringify(g)),e.countStatistics()}))}else e.$message({message:t.Message,type:"error"})}))},getSuppliers:function(){var e=this;(0,_.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var n=[];for(var a in t.Data){var i={Id:a,Name:t.Data[a]};n.push(i)}e.SupplierData=n}else e.$message({message:t.Message,type:"error"})}))},getProject:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,x.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.projectOptions=t.Data.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},changeWarehouse:function(e){this.currentRow=e,this.handleWarehouse(!0)},getWarehouse:function(e){var t=this,n=e.warehouse,a=e.location;this.currentRow?(this.currentRow.WarehouseId=a.Warehouse_Id,this.currentRow.LocationId=a.Id,this.$set(this.currentRow,"WarehouseName",n.Display_Name),this.$set(this.currentRow,"LocationName",a.Display_Name),this.$set(this.currentRow,"Warehouse_Location",n.Display_Name+"/"+a.Display_Name)):this.multipleSelection.forEach((function(e,i){t.$set(e,"WarehouseName",n.Display_Name),t.$set(e,"LocationName",a.Display_Name),t.$set(e,"Warehouse_Location",n.Display_Name+"/"+a.Display_Name),e.LocationId=a.Id,e.WarehouseId=n.Id})),this.handleUpdateRow()},batchEditorFn:function(e){var t=this;this.multipleSelection.forEach((function(n,a){e.forEach((function(e){if(t.$set(n,e.key,e.val),"Supplier"===e.key)t.$set(n,"SupplierName",e.name);else if("PartyUnit"===e.key)t.$set(n,"PartyUnitName",e.name);else if("SysProjectId"===e.key){var a;t.$set(n,"ProjectName",e.name),t.$set(n,"Project_Code",null===(a=t.projectOptions.find((function(t){return t.Sys_Project_Id===e.val})))||void 0===a?void 0:a.Code)}}))})),1!=this.form.InStoreType&&this.multipleSelection.forEach((function(n,a){e.forEach((function(e){["InStoreCount","TaxUnitPrice"].includes(e.key)&&t.$refs["table"].countTaxUnitPrice(n,"item.key")}))})),this.handleClose()},changeStandard:function(e){var t=this;this.currentRow=e,this.currentComponent="Standard",this.dWidth="40%",this.title="选择规格",this.dialogVisible=!0,this.$nextTick((function(n){t.$refs["content"].getOption(e)}))},getStandard:function(e){var t=e.type,n=e.val;1===t?this.$set(this.currentRow,"StandardDesc",n):(this.$set(this.currentRow,"StandardDesc",n.StandardDesc),this.currentRow.StandardId=n.StandardId)},typeChange:function(e){0!==e&&(this.BigType=1,this.form.Attachment="",this.tableData.length=0,this.form.DriverMobile="",this.fileListData=[],this.$refs["form"].resetFields(),this.form.Is_Replace_Purchase=!1,this.form.InStoreType=e,this.multipleSelection=[],this.rootTableData=[],this.handleReset(),this.getTableColumns()),this.countStatistics()},handleReset:function(){this.searchForm.RawName="",this.searchForm.SysProjectId="",this.searchForm.Spec="",this.$refs["table"].tbData=JSON.parse(JSON.stringify(this.rootTableData)),this.tableData=JSON.parse(JSON.stringify(this.rootTableData))},getAddList:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];n?(this.form.Delivery_No="",this.form.CarNumber="",this.form.Driver="",this.form.DriverMobile=""):e.forEach((function(e){n||(t.form.Delivery_No&&(e.Delivery_No=t.form.Delivery_No),t.form.CarNumber&&(e.CarNumber=t.form.CarNumber),t.form.Driver&&(e.Driver=t.form.Driver),t.form.DriverMobile&&(e.DriverMobile=t.form.DriverMobile))})),!this.form.Is_Replace_Purchase||2!==this.form.InStoreType&&3!==this.form.InStoreType||e.map((function(e){var n;e.ProjectName=t.form.ProjectName,e.Project_Code=null===(n=t.projectOptions.find((function(e){return e.Sys_Project_Id===t.form.SysProjectId})))||void 0===n?void 0:n.Code,e.SysProjectId=t.form.SysProjectId,e.SupplierName=t.form.SupplierName,e.Supplier=t.form.Supplier,e.PartyUnitName=t.form.PartyUnitName,e.PartyUnit=t.form.PartyUnit})),this.handleUpdateTb(e,"add")},countStatistics:function(){var e=this;this.statisticsData={InStoreCountTotal:0,Tax_All_Price:0},this.rootTableData.map((function(t){e.statisticsData.InStoreCountTotal+=t.InStoreCount||0,e.statisticsData.Tax_All_Price+=Number((null===t||void 0===t?void 0:t.Tax_All_Price)||0)}))},handleSearch:function(){var e=this;if(this.searchNum++,1===this.searchNum){var t=this.$refs.table.tbData;this.rootTableData=JSON.parse(JSON.stringify(t))}if(this.tableData=JSON.parse(JSON.stringify(this.rootTableData)),this.searchForm.RawName){var n=new RegExp(this.searchForm.RawName,"i");this.tableData=this.tableData.filter((function(e){return n.test(e.RawName)}))}if(this.searchForm.Spec&&(this.tableData=this.tableData.filter((function(t){return t.Spec.includes(e.searchForm.Spec)}))),this.searchForm.SysProjectId){var a=new RegExp(this.searchForm.SysProjectId,"i");this.tableData=this.tableData.filter((function(e){return a.test(e.SysProjectId)}))}this.$refs["table"].tbData=this.tableData},importData:function(e){this.$refs["table"].importData(e)},getRowName:function(e){var t=e.Name,n=e.Id;this.currentRow.Name=t,this.currentRow.RawId=n,this.currentRow.StandardDesc=""},handleBatchEdit:function(){var e=this;this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.multipleSelection,e.form.InStoreType)}))},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handleWarehouse:function(e){this.currentComponent="Warehouse",this.dWidth="40%",this.title="批量选择仓库/库位",!e&&(this.currentRow=null),this.dialogVisible=!0},handleImport:function(){this.currentComponent="ImportFile",this.dWidth="40%",this.title="辅料导入",this.dialogVisible=!0},handleDelete:function(e){var t=JSON.parse(JSON.stringify(this.$refs.table.tbData)),n=JSON.parse(JSON.stringify(this.rootTableData));this.multipleSelection.forEach((function(e,a){var i=t.findIndex((function(t){return t.index===e.index}));t.splice(i,1);var r=n.findIndex((function(t){return t.index===e.index}));n.splice(r,1)})),this.$refs.table.tbData=JSON.parse(JSON.stringify(t)),this.tableData=JSON.parse(JSON.stringify(t)),this.rootTableData=JSON.parse(JSON.stringify(n)),this.multipleSelection=[],this.countStatistics()},handleClose:function(e){this.openAddList=!1,this.dialogVisible=!1,this.dialogRepeatVisible=!1},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){},checkValidate:function(){var e=(0,l.deepClone)(this.$refs["table"].tbData);if(!e.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var t=this.checkTb(e),n=t.status,a=t.msg;return n?{data:e,status:!0}:(this.$message({message:"".concat(a||"必填字段","不能为空"),type:"warning"}),{status:!1})},checkTb:function(e){for(var t=this,n=this.checkTypeList.checkList,a=0;a<e.length;a++){for(var i,r=e[a],o=function(){var e=n[s];if(["",null,void 0].includes(r[e])){var a=t.$refs.table.columns,i=a.find((function(t){return t.Code===e}));return{v:{status:!1,msg:null===i||void 0===i?void 0:i.Display_Name}}}},s=0;s<n.length;s++)if(i=o(),i)return i.v;delete r._X_ROW_KEY,delete r.WarehouseName,delete r.LocationName}return{status:!0,msg:""}},saveDraft:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=this.checkValidate(),i=a.data,r=a.status;r&&this.$refs["form"].validate((function(a){if(!a)return!1;var r=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){r.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)})),e.form.Attachment=r.join(","),e.form.Status=1===t?1:3,e.form.InStoreNo=e.$route.query.id||"";var o=(0,s.default)({},e.form),l=e.checkSameInfo(),u=l.show,c=l.resultArray,d=l.indexes;!n&&u&&e.checkDuplicate?(e.dialogRepeatVisible=!0,e.$nextTick((function(t){e.$refs["repeat"].setTbData(c,d)}))):1===t?(e.saveLoading=!0,e.submitDraft(o,i)):2===t?(e.saveSubmitLoading=!0,e.submitDraft(o,i)):3===t&&e.submitReturn(o,i)}))},submitDraft:function(e,t){var n,a=this;1===this.form.InStoreType?n=_.MaterielAuxPurchaseInStore:2===this.form.InStoreType?n=_.MaterielPartyAInStorel:3===this.form.InStoreType&&(n=_.MaterielAuxManualInStore),n({Receipt:e,Sub:t}).then((function(e){var t;e.IsSucceed?(a.$message({message:"保存成功",type:"success"}),a.closeView()):a.$message({message:e.Message,type:"error"}),a.saveLoading=!1,a.saveSubmitLoading=!1,a.saveReturnLoading=!1,null===(t=a.$refs["repeat"])||void 0===t||t.setLoading(!1)}))},submitReturn:function(e,t){var n=this;if(t=t.filter((function(e){return e.OutStoreCount})),0!==t.length){var a=new Promise((function(e,a){t.find((function(e){return!e.ReturnSupplier&&3===n.form.InStoreType}));e()}));a.then((function(){n.saveReturnLoading=!0,(0,_.AuxReturnByReceipt)({Receipt:e,Sub:t}).then((function(e){var t;e.IsSucceed?(n.$message({message:"保存成功",type:"success"}),n.closeView()):n.$message({message:e.Message,type:"error"}),n.saveReturnLoading=!1,null===(t=n.$refs["repeat"])||void 0===t||t.setLoading(!1)}))})).catch((function(){return!1}))}else this.$message({message:"请至少填写一条退货数据",type:"error"})},getReturnNum:function(e){},projectChange:function(e){this.form.ProjectName=this.projectOptions.find((function(t){return t.Id===e})).Short_Name,this.form.SysProjectId=this.projectOptions.find((function(t){return t.Id===e})).Sys_Project_Id,e&&(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},supplierChange:function(e){this.form.SupplierName=this.SupplierData.find((function(t){return t.Id===e})).Name,e&&(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},partyUnitChange:function(e){this.form.PartyUnitName=this.PartyUnitList.find((function(t){return t.Id===e})).Name,e&&(this.tableData.length=0,this.multipleSelection=[],this.rootTableData=[],this.handleReset())},openAddDialog:function(e){var t=this;if(this.form.Is_Replace_Purchase){if(!(1!==this.form.InStoreType&&3!==this.form.InStoreType||this.form.SysProjectId&&this.form.Supplier))return void this.$message({message:"请先选择所属项目和供应商",type:"warning"});if(2===this.form.InStoreType&&(!this.form.SysProjectId||!this.form.PartyUnit))return void this.$message({message:"请先选择所属项目和甲方单位",type:"warning"})}this.currentRow=e,this.openAddList=!0,e?(this.isSingle=!0,this.$nextTick((function(n){t.$refs["draft"].setRow(e)}))):this.isSingle=!1},closeView:function(){(0,l.closeTagView)(this.$store,this.$route)},setSelectRow:function(e){this.multipleSelection=e},getDate:function(e){var t=e||new Date,n=t.getFullYear(),a=("0"+(t.getMonth()+1)).slice(-2),i=("0"+t.getDate()).slice(-2);return"".concat(n,"-").concat(a,"-").concat(i)},handleSaveDraft:function(){this.isDraft=!0,this.saveDraft(1,!1)},submitInfo:function(){var e=this;this.isDraft?this.saveDraft(1,!0):this.$confirm("确认提交入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.saveDraft(2,!0)})).catch((function(){e.$message({type:"info",message:"已取消"}),e.$refs["repeat"].setLoading(!1)}))},handleSubmit:function(){this.isDraft=!1,this.saveDraft(2,!1)},checkSameInfo:function(){var e=this,t=JSON.parse(JSON.stringify(this.$refs.table.tbData));t.forEach((function(t){var n=e.checkTypeList.checkSameList.filter((function(e){return!!e}));t.currentKey=n.reduce((function(e,n){return e+"-"+(t[n]||"").toString().trim()}),"")}));var n=t,a=n.reduce((function(e,t){return t.currentKey&&(e[t.currentKey]=(e[t.currentKey]||0)+1),e}),{}),i=Object.keys(a).filter((function(e){return a[e]>1})),r=n.filter((function(e){return i.includes(e.currentKey)})),o=r.reduce((function(e,t){return e[t.currentKey]||(e[t.currentKey]=[]),e[t.currentKey].push(t),e}),{}),s=Object.keys(o).reduce((function(e,t){return e=e.concat(o[t]),e}),[]),l=s.reduce((function(e,t,n){return 0!==n&&t.currentKey===s[n-1].currentKey||e.push(n),e}),[]);return{show:s.length>0,resultArray:s,indexes:l}},handelReturn:function(){this.isDraft=!1,this.saveDraft(3,!1)}}}},7575:function(e,t,n){},"776f":function(e,t,n){"use strict";n.r(t);var a=n("04f1"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"7c59":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"contentBox"},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[n("el-row",[[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"原料名称",prop:"Name"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},on:{clear:e.search},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1)],n("el-col",{attrs:{span:2}},[n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:e.search}},[e._v("查询")])],1)],2)],1),n("div",{staticClass:"tb-wrapper"},[n("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange,"radio-change":e.radioChangeEvent}},[e._v(" > "),e.isSingle?n("vxe-column",{attrs:{fixed:"left",type:"radio",width:"60"},scopedSlots:e._u([{key:"header",fn:function(){return[n("vxe-button",{attrs:{type:"text",disabled:!e.selectRow},on:{click:e.clearRadioRowEevnt}},[e._v("取消")])]},proxy:!0}],null,!1,2688545180)}):n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["SpecificationUsage"===t.Code?{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(0===a[t.Code]?"按需使用":1===a[t.Code]?"使用标准规格":2===a[t.Code]?"不使用标准规格":"")+" ")]}}:{key:"default",fn:function(n){var a=n.row;return[e._v(" "+e._s(e._f("displayValue")(a[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),n("div",{staticClass:"button"},[n("el-button",{on:{click:e.handleClose}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",disabled:e.isSingle&&!e.selectRow||!e.isSingle&&!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},"7ea7":function(e,t,n){"use strict";n("fac9")},"7fc7":function(e,t,n){"use strict";n("c93b")},"85e9":function(e,t,n){"use strict";n.r(t);var a=n("b9d9"),i=n("2ce6");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"43989d13",null);t["default"]=s.exports},"887b":function(e,t,n){"use strict";n.r(t);var a=n("071b"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"8bbc7":function(e,t,n){"use strict";n.r(t);var a=n("2a4f"),i=n("3317");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"26e5066a",null);t["default"]=s.exports},"8f73":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[1!==e.formData.InStoreType?n("div",{staticClass:"cs-alert"},[n("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("点击此处下载导入模板")])],1):e._e(),n("upload",{ref:"upload",attrs:{"before-upload":e.beforeUpload}}),n("footer",{staticClass:"cs-footer"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},i=[]},"9d87":function(e,t,n){"use strict";n.r(t);var a=n("e11b0"),i=n("d270");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("cf00");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"243d2329",null);t["default"]=s.exports},a03d:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("2909")),r=a(n("c14f")),o=a(n("1da1"));n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("a434"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("a9e3"),n("b680"),n("b64b"),n("d3b7"),n("159b");var s=n("cf45"),l=n("ed08"),u=n("30c8");t.default={props:{isView:{type:Boolean,default:!1},checkTypeList:{type:Object,required:!0,default:function(){}},formStatus:{type:Number,default:0}},data:function(){return{tbLoading:!1,multipleSelection:[],textureOption:[],rootColumns:[],columns:[],tbData:[],texture:{},itemKey:""}},watch:{},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTexture();case 1:return t.a(2)}}),t)})))()},mounted:function(){return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},inject:["checkDuplicate"],methods:{changeKeyInfo:function(e,t){var n=this.tbData.map((function(t){return t[e]})),a=(0,l.uniqueArr)(n);1===a.length?this.$emit("changeKeyInfo",{key:e,val:t}):this.$emit("changeKeyInfo",{key:e,val:""})},changeFormInfo:function(e,t){var n=this;this.tbData.forEach((function(a){n.$set(a,e,t)}))},handleCopy:function(e,t){var n=this;return(0,o.default)((0,r.default)().m((function a(){var i;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:i=JSON.parse(JSON.stringify(e)),i.Sub_Id&&(i.Sub_Id=""),delete i._X_ROW_KEY,n.$emit("updateTb",[i]),n.tbData.splice(t+1,0,i);case 1:return a.a(2)}}),a)})))()},checCheckboxkMethod:function(e){var t=e.row;return!(3===this.formStatus&&t.Sub_Id)},init:function(e){this.rootColumns=JSON.parse(JSON.stringify(e)),this.columnsOption(),this.getAllColumnsOption()},columnsOption:function(){this.itemKey=Math.random(),this.columns=JSON.parse(JSON.stringify(this.rootColumns))},getCheckList:function(e){var t=[];this.checkDuplicate()&&(t=["PurchaseNo","ProjectName","Project_Code","SupplierName","CategoryName","RawName","RawCode","Material","Spec","Measure_Unit","TaxUnitPrice","Tax_Rate","Warehouse_Location"]),this.checkTypeList.checkSameList=t,this.checkTypeList.checkList=e.filter((function(e){return e.Is_Must_Input&&e.Is_Display})),this.checkTypeList.checkList=this.checkTypeList.checkList.map((function(e){return e.Code})),this.checkTypeList.remarkList=e.filter((function(e){return e.Is_Display&&-1!==e.Code.indexOf("Remark")})),this.checkTypeList.remarkList=this.checkTypeList.remarkList.map((function(e){return{key:e.Code,label:e.Display_Name,type:"string"}}))},getAllColumnsOption:function(){var e=JSON.parse(JSON.stringify(this.rootColumns));this.getCheckList(e)},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,n=this,a=e.map((function(e){return e.InStoreCount=e.PurchaseCount,n.checkNum(e),e.rowIndex="",e}));(t=this.tbData).push.apply(t,(0,i.default)(a))},checkNum:function(e){this.countTaxUnitPrice(e)},countTaxUnitPrice:function(e){0===e.InStoreCount||0===e.TaxUnitPrice?e.Tax_All_Price=0:e.InStoreCount&&e.TaxUnitPrice?e.Tax_All_Price=Number((e.InStoreCount*e.TaxUnitPrice).toFixed(2)):e.Tax_All_Price="",this.$emit("updateRow"),this.taxAllPriceChange(e)},taxAllPriceChange:function(e){e.NoTaxAllPrice=(0,u.getNoTaxAllPrice)(e),e.NoTaxUnitPrice=(e.TaxUnitPrice/(1+e.Tax_Rate/100)).toFixed(3)/1||0},countChange:function(e,t){var n=e.value;t.PerWtg&&(t.TotalPerWtg=numeral(n*t.PerWtg).format("0.[00]")),t.PerLen&&(t.TotalPerLen=numeral(n*t.PerLen).format("0.[00]"))},getTextureType:function(e){var t=this.textureOption.find((function(t){return t.Id===e}));return t&&t.Display_Name},getTexture:function(){var e=this;(0,s.getDictionary)("Texture").then((function(t){e.textureOption=t,e.textureOption.forEach((function(t,n){e.$set(e.texture,t.Value,t.Display_Name)}))}))},changeInStoreCount:function(e){}}}},a6bb:function(e,t,n){"use strict";n.r(t);var a=n("d8c8"),i=n("5d80");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"7589f37f",null);t["default"]=s.exports},ac65:function(e,t,n){"use strict";n.r(t);var a=n("f8bf"),i=n("5968e");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"18aa1aa0",null);t["default"]=s.exports},ad97:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("a9e3"),n("d3b7");var a=n("9643");t.default={props:{pageType:{type:Number,default:void 0}},data:function(){return{warehouses:[],locations:[],form:{Warehouse_Id:"",Location_Id:""},btnLoading:!1,rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){this.getWarehouseListOfCurFactory()},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var n=e.warehouses.find((function(t){return t.Id===e.form.Warehouse_Id})),a=e.locations.find((function(t){return t.Id===e.form.Location_Id}));e.$emit("warehouse",{warehouse:n,location:a}),e.btnLoading=!1,e.handleClose()}))},getWarehouseListOfCurFactory:function(){var e=this;(0,a.GetWarehouseListOfCurFactory)({type:0===this.pageType?"原材料仓库":"辅料仓库"}).then((function(t){if(t.IsSucceed){if(e.warehouses=t.Data,e.warehouses.length){var n=e.warehouses.find((function(e){return e.Is_Default}));n&&(e.form.Warehouse_Id=n.Id,e.getLocationList(!0))}}else e.$message({message:t.Message,type:"error"})}))},wareChange:function(e){this.form.Location_Id="",e&&this.getLocationList(!0)},getLocationList:function(e){var t=this;(0,a.GetLocationList)({Warehouse_Id:this.form.Warehouse_Id}).then((function(n){if(n.IsSucceed){if(t.locations=n.Data,t.locations.length&&e){var a=t.locations.find((function(e){return e.Is_Default}));a&&(t.form.Location_Id=a.Id)}}else t.$message({message:n.Message,type:"error"})}))},handleClose:function(){this.$emit("close")}}}},b0eb:function(e,t,n){"use strict";n.r(t);var a=n("0e51"),i=n("bf8e");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("f9e3");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"425ffe08",null);t["default"]=s.exports},b958:function(e,t,n){"use strict";n.r(t);var a=n("1024"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},b9d9:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.renderComponent?n("vxe-table",{key:e.itemKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checCheckboxkMethod}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e.isView?e._e():n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,i=t.rowIndex;return[n("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&a.Sub_Id)},on:{click:function(t){return e.handleCopy(a,i)}}},[e._v("复制")])]}}],null,!1,1582048890)}),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(a){var i=a.row;return["InStoreCount"===t.Code?n("div",[n("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.checkNum(i)}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("div",[e._v(" "+e._s(i.WarehouseName)+"/"+e._s(i.LocationName)+" "),n("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(i)}}})])]):"ProjectName"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-select",{attrs:{transfer:"",filterable:""},on:{change:function(t){return e.projectChange(i)}},model:{value:i.SysProjectId,callback:function(t){e.$set(i,"SysProjectId",t)},expression:"row.SysProjectId"}},e._l(e.projectOptions,(function(e){return n("vxe-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Short_Name}})})),1)],1):"PartyUnitName"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-select",{attrs:{transfer:"",filterable:""},on:{change:function(t){return e.partyUnitNameChange(i)}},model:{value:i.PartyUnit,callback:function(t){e.$set(i,"PartyUnit",t)},expression:"row.PartyUnit"}},e._l(e.partyUnitList,(function(e){return n("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):"TaxUnitPrice"===t.Code||"Tax_Rate"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(i)}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):"Delivery_No"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("Delivery_No",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"CarNumber"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("CarNumber",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"Driver"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("Driver",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"DriverMobile"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("DriverMobile",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},i=[]},bbd2:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},i=[]},bf8e:function(e,t,n){"use strict";n.r(t);var a=n("f3de"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},c1dd:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("7db0"),n("caad"),n("d81d"),n("14d9"),n("a434"),n("b0c0"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("d3b7"),n("2532");var i=a(n("ade3")),r=a(n("2909")),o=a(n("c14f")),s=a(n("1da1")),l=n("e144");n("93aa"),t.default={props:{isReplacePurchase:{type:Boolean,default:!1},SupplierData:{type:Array,required:!0,default:function(){return[]}},projectOptions:{type:Array,required:!0,default:function(){return[]}},partyUnitList:{type:Array,required:!0,default:function(){return[]}},checkTypeList:{type:Object,required:!0,default:function(){}}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}},Is_Component:"",value:"",options:[],list:[{id:(0,l.v4)(),val:void 0,key:""}],SupplierName:"",PartyUnitName:"",ProjectName:""}},mounted:function(){return(0,s.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:case 1:return e.a(2)}}),e)})))()},methods:(0,i.default)((0,i.default)({optionChange:function(e,t){var n;if("supplierArray"===t)this.SupplierName=null===(n=this.SupplierData.find((function(t){return t.Id===e})))||void 0===n?void 0:n.Name;else if("partyUnitArray"===t){var a;this.PartyUnitName=null===(a=this.partyUnitList.find((function(t){return t.Id===e})))||void 0===a?void 0:a.Name}else if("projectArray"===t){var i;this.ProjectName=null===(i=this.projectOptions.find((function(t){return t.Sys_Project_Id===e})))||void 0===i?void 0:i.Short_Name}},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},handleAdd:function(){this.list.push({id:(0,l.v4)(),val:void 0,key:""})},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,s.default)((0,o.default)().m((function t(){var n,a,i,r;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.btnLoading=!0,n={},a=0;case 1:if(!(a<e.list.length)){t.n=4;break}if(i=e.list[a],i.val){t.n=2;break}if(r=!0,"Width"===i.key||"Length"===i.key||"InStoreCount"===i.key||"InStoreWeight"===i.key?0===i.val?e.$message({message:"值不能为0",type:"warning"}):e.$message({message:"值不能为空",type:"warning"}):"Tax_Rate"===i.key||"TaxUnitPrice"===i.key?r=!1:e.$message({message:"值不能为空",type:"warning"}),e.btnLoading=!1,!r){t.n=2;break}return t.a(2);case 2:n[i.key]=i.val;case 3:a++,t.n=1;break;case 4:e.list.map((function(t){"Supplier"===t.key?t.name=e.SupplierName:"PartyUnit"===t.key?t.name=e.PartyUnitName:"SysProjectId"===t.key&&(t.name=e.ProjectName)})),e.$emit("batchEditor",e.list),e.btnLoading=!1;case 5:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(n){return(!t.list.map((function(e){return e.key})).includes(n.key)||n.key===e)&&n.label}))}},"checkType",(function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t})),"init",(function(e,t){var n;if(this.selectList=e,1===t)this.options=[{key:"InStoreCount",label:"入库数量",type:"number"}],(n=this.options).push.apply(n,(0,r.default)(this.checkTypeList.remarkList));else if(2===t){var a;this.options.push({key:"Tax_Rate",label:"税率",type:"number"},{key:"Material",label:"材质",type:"string"},{key:"InStoreCount",label:"入库数量",type:"number"},{key:"TaxUnitPrice",label:"含税单价（元）",type:"number"},(a=this.options).push.apply(a,(0,r.default)(this.checkTypeList.remarkList))),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"PartyUnit",label:"甲方单位",type:"partyUnitArray"})}else if(3===t){var i;this.options.push({key:"Tax_Rate",label:"税率",type:"number"},{key:"Material",label:"材质",type:"string"},{key:"InStoreCount",label:"入库数量",type:"number"},{key:"TaxUnitPrice",label:"含税单价（元）",type:"number"},(i=this.options).push.apply(i,(0,r.default)(this.checkTypeList.remarkList))),this.isReplacePurchase||this.options.unshift({key:"SysProjectId",label:"所属项目",type:"projectArray"},{key:"Supplier",label:"供应商",type:"supplierArray"})}}))}},c93b:function(e,t,n){},c9bd0:function(e,t,n){"use strict";n.r(t);var a=n("5ac8"),i=n("4e34");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("21c9");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"f4055c6e",null);t["default"]=s.exports},ce62:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("2909")),r=a(n("c14f")),o=a(n("1da1"));n("7db0"),n("c740"),n("caad"),n("d81d"),n("14d9"),n("a434"),n("e9f5"),n("f665"),n("7d54"),n("ab43"),n("d3b7"),n("2532"),n("c7cd"),n("159b");var s=n("5480"),l=n("cf45"),u=a(n("6612")),c=n("ed08");t.default={props:{isView:{type:Boolean,default:!1}},data:function(){return{tbLoading:!1,multipleSelection:[],textureOption:[],columns:[],tbData:[],texture:{}}},watch:{"tbData.length":{handler:function(e,t){this.tbData.map((function(e,t){return e.index=t,e}))},immediate:!0}},created:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTexture();case 1:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.columnsOption();case 1:return t.a(2)}}),t)})))()},methods:{changeKeyInfo:function(e,t){var n=this.tbData.map((function(t){return t[e]})),a=(0,c.uniqueArr)(n);1===a.length?this.$emit("changeKeyInfo",{key:e,val:t}):this.$emit("changeKeyInfo",{key:e,val:""})},changeFormInfo:function(e,t){var n=this;this.tbData.forEach((function(a){n.$set(a,e,t)}))},columnsOption:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var n,a,i,o,l;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PROAuxPurchaseAddList");case 1:n=t.v,a=[],e.columns=n.map((function(e){return a.includes(e.Code)&&(e.fixed="right"),e})),i=e.columns.findIndex((function(e){return"PurchaseNo"===e.Code})),-1!==i&&e.columns.splice(i,1),o=e.columns.findIndex((function(e){return"ProjectName"===e.Code})),-1!==o&&e.columns.splice(o,1),l=e.columns.findIndex((function(e){return"PurchaseCount"===e.Code})),-1!==l&&e.columns.splice(l,1);case 2:return t.a(2)}}),t)})))()},tbSelectChange:function(e){this.multipleSelection=e.records,this.$emit("select",this.multipleSelection)},handleWarehouse:function(e){this.$emit("changeWarehouse",e)},addData:function(e){var t,n=e.map((function(e){return e}));(t=this.tbData).push.apply(t,(0,i.default)(n))},countChange:function(e,t){var n=e.value;t.PerWtg&&(t.TotalPerWtg=(0,u.default)(n*t.PerWtg).format("0.[00]")),t.PerLen&&(t.TotalPerLen=(0,u.default)(n*t.PerLen).format("0.[00]"))},getTextureType:function(e){var t=this.textureOption.find((function(t){return t.Id===e}));return t&&t.Display_Name},getTexture:function(){var e=this;(0,l.getDictionary)("Texture").then((function(t){e.textureOption=t,e.textureOption.forEach((function(t,n){e.$set(e.texture,t.Value,t.Display_Name)}))}))}}}},cf00:function(e,t,n){"use strict";n("7575")},cf45:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDictionary=i,n("d3b7");var a=n("6186");function i(e){return new Promise((function(t,n){(0,a.GetDictionaryDetailListByCode)({dictionaryCode:e}).then((function(e){e.IsSucceed&&t(e.Data)}))}))}},d270:function(e,t,n){"use strict";n.r(t);var a=n("6240"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},d478:function(e,t,n){},d4da:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.renderComponent?n("vxe-table",{key:e.itemKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"tooltip-config":{enterable:!0},"checkbox-config":{checkMethod:e.checCheckboxkMethod}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),n("vxe-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:"left",align:"center"}}),e.isView?e._e():n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,i=t.rowIndex;return[n("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&a.Sub_Id)},on:{click:function(t){return e.handleCopy(a,i)}}},[e._v("复制")])]}}],null,!1,1582048890)}),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(a){var i=a.row;return["InStoreCount"===t.Code?n("div",[n("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.checkNum(i)}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("div",[e._v(" "+e._s(i.WarehouseName)+"/"+e._s(i.LocationName)+" "),n("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(i)}}})])]):"ProjectName"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-select",{attrs:{filterable:"",transfer:"",clearable:""},on:{change:function(t){return e.projectChange(i)}},model:{value:i.SysProjectId,callback:function(t){e.$set(i,"SysProjectId",t)},expression:"row.SysProjectId"}},e._l(e.projectOptions,(function(e){return n("vxe-option",{key:e.Sys_Project_Id,attrs:{filterable:"",value:e.Sys_Project_Id,label:e.Short_Name}})})),1)],1):"SupplierName"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-select",{attrs:{transfer:"",clearable:"",filterable:""},on:{change:function(t){return e.supplierChange(i)}},model:{value:i.Supplier,callback:function(t){e.$set(i,"Supplier",t)},expression:"row.Supplier"}},e._l(e.SupplierData,(function(e){return n("vxe-option",{key:e.Id,attrs:{value:e.Id,label:e.Name}})})),1)],1):"TaxUnitPrice"===t.Code||"Tax_Rate"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(i)}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):"Delivery_No"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("Delivery_No",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"CarNumber"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("CarNumber",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"Driver"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("Driver",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"DriverMobile"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("DriverMobile",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()},i=[]},d837:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("e9c4");var i=a(n("3796")),r=n("ed08"),o=n("93aa");t.default={components:{Upload:i.default},data:function(){return{btnLoading:!1,schdulingPlanId:""}},inject:["formData"],mounted:function(){},methods:{getTemplate:function(){var e=this;(0,o.GetAuxImportTemplate)({inStoreType:this.formData.InStoreType}).then((function(t){t.IsSucceed?window.open((0,r.combineURL)(e.$baseUrl,t.Data)):e.$message({type:"error",message:t.Message})}))},beforeUpload:function(e){var t=this,n={In_Store_Type:this.formData.InStoreType,Is_Replace_Purchase:this.formData.Is_Replace_Purchase,SysProjectId:this.formData.SysProjectId,PartyUnit:this.formData.PartyUnit,Supplier:this.formData.Supplier},a=new FormData;a.append("Receipt",JSON.stringify(n)),a.append("Files",e),this.btnLoading=!0,(0,o.AuxImport)(a).then((function(e){if(e.IsSucceed){var n=e.Data?e.Data:[];t.$emit("getAddList",n),t.$message({type:"success",message:"导入成功"}),t.$emit("close")}else t.$message({type:"error",message:e.Message}),e.Data&&window.open((0,r.combineURL)(t.$baseUrl,e.Data));t.btnLoading=!1}))},handleSubmit:function(){this.$refs.upload.handleSubmit()},setRow:function(e){this.schdulingPlanId=e.Schduling_Id}}}},d8c8:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("vxe-table",{key:e.itemKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!0},"checkbox-config":{reserve:!0},"tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{type:"seq",width:"60",title:"序号",fixed:"left",align:"center"}}),e._l(e.columns,(function(t){return n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,visible:t.Is_Display,"min-width":t.Width,title:t.Display_Name,"edit-render":"OutStoreCount"===t.Code||"ReturnSupplierName"===t.Code?{}:null},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(a){var i=a.row;return["OutStoreCount"===t.Code?n("div",[n("vxe-input",{attrs:{min:0,max:i["AvailableCount"],type:"number"},on:{change:function(t){return e.changeNum(i)}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):"ReturnSupplierName"===t.Code?n("div",[i.isEditSupplier?n("vxe-select",{attrs:{transfer:"",filterable:"",clearable:""},on:{change:function(t){return e.changeSupplier(t,i)}},model:{value:i.ReturnSupplier,callback:function(t){e.$set(i,"ReturnSupplier",t)},expression:"row.ReturnSupplier"}},e._l(e.suppliersOption,(function(e,t){return n("vxe-option",{key:t,attrs:{value:e.Id,label:e.Name}})})),1):n("span",[e._v(e._s(i.ReturnSupplierName))])],1):n("div",[n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])])]}}:null],null,!0)})}))],2)},i=[]},e11b0:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container abs100"},[n("el-card",{staticClass:"box-card",style:e.isRetract?"height: 122px; overflow: hidden;":""},[n("div",{staticClass:"toolbar-container",staticStyle:{margin:"0"}},[n("div",{staticClass:"toolbar-title"},[n("span"),e._v("入库单信息")]),n("div",{staticClass:"retract-container"},[n("el-button",{attrs:{type:"text"},on:{click:e.handleRetract}},[e._v(e._s(e.isRetract?"展开":"收起"))]),n("el-button",{attrs:{type:"text",icon:e.isRetract?"el-icon-arrow-down":"el-icon-arrow-up"}})],1)]),n("el-divider",{staticClass:"elDivder"}),n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"入库类型",prop:"InStoreType"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isEdit||e.isReturn,placeholder:"请选择"},on:{change:e.typeChange},model:{value:e.form.InStoreType,callback:function(t){e.$set(e.form,"InStoreType",t)},expression:"form.InStoreType"}},[n("el-option",{attrs:{label:"采购入库",value:1}}),n("el-option",{attrs:{label:"甲供入库",value:2}}),n("el-option",{attrs:{label:"手动入库",value:3}})],1)],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"入库日期",prop:"InStoreDate"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus,"picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd",type:"date",clearable:""},model:{value:e.form.InStoreDate,callback:function(t){e.$set(e.form,"InStoreDate",t)},expression:"form.InStoreDate"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"送货单编号",prop:"Delivery_No"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"送货单编号",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("Delivery_No")}},model:{value:e.form.Delivery_No,callback:function(t){e.$set(e.form,"Delivery_No",t)},expression:"form.Delivery_No"}})],1)],1),1===e.form.InStoreType?n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"采购合同编号",prop:"Purchase_Contract_No"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus,placeholder:"采购合同编号",type:"text",clearable:""},model:{value:e.form.Purchase_Contract_No,callback:function(t){e.$set(e.form,"Purchase_Contract_No",t)},expression:"form.Purchase_Contract_No"}})],1)],1):e._e(),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"是否代购",prop:"Is_Replace_Purchase"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||3===e.formStatus,placeholder:"请选择"},on:{change:e.isReplacePurchaseChange},model:{value:e.form.Is_Replace_Purchase,callback:function(t){e.$set(e.form,"Is_Replace_Purchase",t)},expression:"form.Is_Replace_Purchase"}},[n("el-option",{attrs:{label:"是",value:!0}}),n("el-option",{attrs:{label:"否",value:!1}})],1)],1)],1),e.form.Is_Replace_Purchase?n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"所属项目",prop:"ProjectId"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"所属项目",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus},on:{change:e.projectChange},model:{value:e.form.ProjectId,callback:function(t){e.$set(e.form,"ProjectId",t)},expression:"form.ProjectId"}},e._l(e.projectOptions,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1)],1):e._e(),1!==e.form.InStoreType&&3!==e.form.InStoreType||!e.form.Is_Replace_Purchase?e._e():n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"供应商",prop:"Supplier"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus},on:{change:e.supplierChange},model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}},e._l(e.SupplierData,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),2===e.form.InStoreType&&e.form.Is_Replace_Purchase?n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnit"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"甲方单位",clearable:"",filterable:"",disabled:e.isView||3===e.formStatus},on:{change:e.partyUnitChange},model:{value:e.form.PartyUnit,callback:function(t){e.$set(e.form,"PartyUnit",t)},expression:"form.PartyUnit"}},e._l(e.PartyUnitList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"车牌号",prop:"CarNumber"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,placeholder:"车牌号",type:"text",clearable:""},on:{change:function(t){return e.changeFormKey("CarNumber")}},model:{value:e.form.CarNumber,callback:function(t){e.$set(e.form,"CarNumber",t)},expression:"form.CarNumber"}})],1)],1),n("el-col",{attrs:{span:6}},[n("el-form-item",{attrs:{label:"司机信息",prop:"Driver"}},[n("el-input",{staticStyle:{width:"35%","margin-right":"10px"},attrs:{disabled:e.isView||e.isReturn,placeholder:"姓名",type:"text"},on:{change:function(t){return e.changeFormKey("Driver")}},model:{value:e.form.Driver,callback:function(t){e.$set(e.form,"Driver",t)},expression:"form.Driver"}}),n("el-input",{staticStyle:{width:"calc(100% - 35% - 10px)"},attrs:{disabled:e.isView||e.isReturn,placeholder:"手机号",type:"text"},on:{change:function(t){return e.changeFormKey("DriverMobile")}},model:{value:e.form.DriverMobile,callback:function(t){e.$set(e.form,"DriverMobile",t)},expression:"form.DriverMobile"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:100,placeholder:"备注",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[n("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:5,multiple:!0,"on-success":function(t,n,a){e.uploadSuccess(t,n,a)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"file-list":e.fileListData,"show-file-list":!0,disabled:e.isView||3===e.formStatus}},[e.isView||e.isReturn||3===e.formStatus?e._e():n("el-button",{attrs:{type:"primary",disabled:e.isView||e.isReturn||3===e.formStatus}},[e._v("上传文件")])],1)],1)],1)],1)],1)],1),n("el-card",{staticClass:"box-card box-card-tb"},[n("div",{staticClass:"toolbar-container"},[n("div",{staticClass:"toolbar-title"},[n("span"),e._v("入库明细")]),n("div",{staticClass:"statistics-container"},[n("div",{staticClass:"statistics-item"},[n("span",{staticStyle:{"margin-right":"16px"}},[e._v("数量")]),n("span",[e._v(e._s(e.statisticsData.InStoreCountTotal))])]),n("div",{staticClass:"statistics-item"},[n("span",{staticStyle:{"margin-right":"16px"}},[e._v("含税总价")]),n("span",[e._v(e._s(e.statisticsData.Tax_All_Price))])])])]),n("el-divider",{staticClass:"elDivder"}),e.isView?e._e():n("div",{staticStyle:{"margin-bottom":"0px",display:"flex","justify-content":"space-between"}},[n("div",[e.isView||e.isReturn?e._e():n("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),n("el-button",{attrs:{type:"danger",disabled:!e.multipleSelection.length},on:{click:e.handleDelete}},[e._v("删除")]),n("el-button",{attrs:{type:"primary",plain:"",disabled:!e.multipleSelection.length},on:{click:e.handleBatchEdit}},[e._v("批量编辑")]),n("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){return e.handleWarehouse(!1)}}},[e._v("批量选择仓库/库位")]),n("el-button",{on:{click:e.handleImport}},[e._v("导入")])]},proxy:!0}],null,!1,1670957855)})],1),n("div",[n("el-form",{ref:"searchForm",attrs:{inline:"",model:e.searchForm,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"辅料名称",prop:"RawName"}},[n("el-input",{staticClass:"input",attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),n("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[n("el-input",{staticClass:"input",attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),n("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[n("el-select",{staticClass:"input",attrs:{placeholder:"所属项目",clearable:"",filterable:""},model:{value:e.searchForm.SysProjectId,callback:function(t){e.$set(e.searchForm,"SysProjectId",t)},expression:"searchForm.SysProjectId"}},e._l(e.projectOptions,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),n("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1),n("div",[e.rootColumns?n("DynamicTableFields",{attrs:{title:"表格配置","table-columns":e.rootColumns,"table-config-code":e.tableConfigCode,"manual-hide-columns":e.manualHideTableColumns},on:{updateColumn:e.updateColumn}}):e._e()],1)]),n("div",{staticClass:"tb-x"},[n(e.currentTbComponent,{ref:"table",tag:"component",attrs:{"is-view":e.isView,"page-type":e.pageType,"in-store-type":e.form.InStoreType,"is-replace-purchase":e.form.Is_Replace_Purchase,"project-options":e.projectOptions,"party-unit-list":e.PartyUnitList,"check-type-list":e.checkTypeList,"supplier-data":e.SupplierData,"form-status":e.formStatus||0},on:{changeStandard:e.changeStandard,changeWarehouse:e.changeWarehouse,updateTb:e.handleUpdateTb,updateRow:e.handleUpdateRow,select:e.setSelectRow,returnNum:e.getReturnNum,changeKeyInfo:e.changeKeyInfo}})],1),e.isView?e._e():n("el-divider",{staticClass:"elDivder"}),e.isView||e.isReturn?e._e():n("footer",[n("div",{staticClass:"data-info"},[n("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),n("div",[n("el-button",{on:{click:e.closeView}},[e._v("取消")]),3!==e.formStatus?n("el-button",{attrs:{loading:e.saveLoading},on:{click:e.handleSaveDraft}},[e._v("保存草稿 ")]):e._e(),n("el-button",{attrs:{loading:e.saveSubmitLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("提交入库")])],1)]),e.isReturn?n("footer",[n("div",{staticStyle:{width:"100%","text-align":"right"}},[n("el-button",{on:{click:e.closeView}},[e._v("取消")]),n("el-button",{attrs:{loading:e.saveReturnLoading,type:"primary"},on:{click:e.handelReturn}},[e._v("确认退货")])],1)]):e._e()],1),e.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.dWidth,top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"page-type":1,"supplier-data":e.SupplierData,"project-options":e.projectOptions,"party-unit-list":e.PartyUnitList,"check-type-list":e.checkTypeList,"is-replace-purchase":e.form.Is_Replace_Purchase},on:{close:e.handleClose,warehouse:e.getWarehouse,batchEditor:e.batchEditorFn,importData:e.importData,standard:e.getStandard,refresh:e.fetchData,getAddList:function(t){return e.getAddList(t,!0)}}})],1):e._e(),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:(e.isPurchase,"新增辅料"),visible:e.openAddList,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.openAddList=t},close:e.handleClose}},[e.openAddList?[e.isPurchase?n("add-purchase-list",{ref:"draft",attrs:{"project-options":e.projectOptions,"is-single":e.isSingle,"form-data":e.form,"joined-items":e.rootTableData},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}}):n("add-aux-material-list",{ref:"draft",attrs:{"is-single":e.isSingle},on:{getAddList:e.getAddList,getRowName:e.getRowName,close:e.handleClose}})]:e._e()],2),e.dialogRepeatVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"检查重复",visible:e.dialogRepeatVisible,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.dialogRepeatVisible=t},close:e.handleClose}},[n("Repeat",{ref:"repeat",attrs:{columns:e.rootColumns},on:{submit:function(t){return e.submitInfo(1,!0)},close:e.handleClose}})],1):e._e()],1)},i=[]},e6b1:function(e,t,n){"use strict";n("d478")},edce:function(e,t,n){"use strict";n.r(t);var a=n("ad97"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},f3de:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("5530")),r=a(n("c14f")),o=a(n("1da1"));n("4de4"),n("7db0"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("d3b7");var s=n("9002"),l=n("93aa"),u=n("ed08");t.default={props:{projectOptions:{type:Array,required:!0,default:function(){return[]}},formData:{type:Object,default:function(){return{}}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{OrderCode:"",CategoryId:"",RawName:"",SupplierId:"",SysProjectId:""},selectRow:null,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],tbConfig:{},multipleSelection:[],originalData:[]}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.PurchaseSubId===t.PurchaseSubId}))}))}},watch:{showDialog:function(e){e&&(this.saveLoading=!1)}},mounted:function(){this.getList(),this.getConfig();var e=this.formData,t=e.Supplier,n=e.SysProjectId;t&&n&&(this.form.Supplier=t,this.form.SysProjectId=n)},methods:{getConfig:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.getTableConfig)("PROAddPurchaseAuxList");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},getList:function(){var e=this;(0,l.GetAuxCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelect.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,l.GetAuxProcurementDetails)((0,i.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.InStoreCount=e.AvailableCount,e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"})})),e.tbLoading=!1;case 1:return t.a(2)}}),t)})))()},handleSave:function(){this.$emit("getAddList",this.multipleSelection.map((function(e){return e.PurchaseCount=e.InStoreCount,e}))),this.$emit("close")},addToList:function(){var e=(0,u.deepClone)(this.multipleSelection);this.$emit("getAddList",e.map((function(e){return e.PurchaseCount=e.InStoreCount,e}))),this.multipleSelection=[],this.$refs.xTable1.clearCheckboxRow()},handleClose:function(){this.$emit("close")}}}},f8bf:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("vxe-table",{key:e.itemKey,ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0,keyField:"index"},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",showIcon:!e.isView},"checkbox-config":{checkMethod:e.checCheckboxkMethod},"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():n("vxe-column",{attrs:{fixed:"left",title:"",type:"checkbox",width:"60"}}),e.isView?e._e():n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,i=t.rowIndex;return[n("el-button",{attrs:{type:"text",disabled:Boolean(3===e.formStatus&&a.Sub_Id)},on:{click:function(t){return e.handleCopy(a,i)}}},[e._v("复制")])]}}],null,!1,1582048890)}),e._l(e.columns,(function(t){return[n("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",sortable:"",align:"left",field:t.Code,visible:t.Is_Display,"min-width":t.Width,title:t.Is_Must_Input?"*"+t.Display_Name:t.Display_Name,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(a){var i=a.row;return["InStoreCount"===t.Code?n("div",[n("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.checkNum(i)}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):"Warehouse_Location"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("div",[e._v(" "+e._s(i.WarehouseName)+"/"+e._s(i.LocationName)+" "),n("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(i)}}})])]):"Delivery_No"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("Delivery_No",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"CarNumber"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("CarNumber",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"Driver"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("Driver",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"DriverMobile"===t.Code?n("div",[n("vxe-input",{on:{change:function(n){return e.changeKeyInfo("DriverMobile",i[t.Code])}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1):"TaxUnitPrice"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(i,"TaxUnitPrice")}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):"Tax_Rate"===t.Code?n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.countTaxUnitPrice(i,"Tax_Rate")}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,e._n(n))},expression:"row[item.Code]"}})],1):n("div",[3===e.formStatus&&i.Sub_Id?n("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))]):n("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(n){e.$set(i,t.Code,n)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2)},i=[]},f9e3:function(e,t,n){"use strict";n("40cad")},fac9:function(e,t,n){},fe08:function(e,t,n){"use strict";n.r(t);var a=n("0258"),i=n("2906");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("7ea7");var o=n("2877"),s=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"21fbca31",null);t["default"]=s.exports}}]);