(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-68531b92"],{"1d4a":function(e,t,a){"use strict";a.r(t);var o=a("4e6a"),r=a("f3be");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("f35f");var s=a("2877"),l=Object(s["a"])(r["default"],o["a"],o["b"],!1,null,"2994d520",null);t["default"]=l.exports},"4e6a":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return r}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap flex",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"search-wrapper fff"},[a("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.form.StoreType,callback:function(t){e.$set(e.form,"StoreType",t)},expression:"form.StoreType"}},[a("el-tab-pane",{attrs:{label:"采购成本",name:"1"}}),a("el-tab-pane",{attrs:{label:"生产成本",name:"2"}})],1),a("el-form",{ref:"form1",staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{model:e.form,inline:""}},[e.isReceive?[a("el-form-item",{attrs:{label:"采购单号",prop:"PurchaseNo"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.PurchaseNo,callback:function(t){e.$set(e.form,"PurchaseNo",t)},expression:"form.PurchaseNo"}})],1),a("el-form-item",{attrs:{label:"供应商/甲方",prop:"Supplier"}},[a("SelectExternal",{model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}})],1),a("el-form-item",{attrs:{label:"操作时间",prop:"EndDate"}},[a("el-date-picker",{staticClass:"date-picker",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"辅料名称",prop:"AuxName"}},[a("el-input",{attrs:{type:"text",placeholder:"通配符%",clearable:""},model:{value:e.form.AuxName,callback:function(t){e.$set(e.form,"AuxName",t)},expression:"form.AuxName"}})],1),a("el-form-item",{attrs:{label:"规格",prop:"AuxSpec"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.AuxSpec,callback:function(t){e.$set(e.form,"AuxSpec",t)},expression:"form.AuxSpec"}})],1),a("el-form-item",{attrs:{label:"类型",prop:"InStoreTypeList"}},[a("SelectMaterialStoreType",{attrs:{type:"AuxAllInStoreType",multiple:""},model:{value:e.form.InStoreTypeList,callback:function(t){e.$set(e.form,"InStoreTypeList",t)},expression:"form.InStoreTypeList"}})],1),a("el-form-item",{attrs:{label:"所属项目",prop:"Project_Id"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}})],1)]:[a("el-form-item",{attrs:{label:"所属项目",prop:"Project_Id"}},[a("SelectProject",{attrs:{"has-no-project":""},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}})],1),a("el-form-item",{attrs:{label:"操作时间",prop:"EndDate"}},[a("el-date-picker",{staticClass:"date-picker",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"领料/退库部门",prop:"Pick_Department_Id"}},[a("SelectDepartment",{model:{value:e.form.DepartmentId,callback:function(t){e.$set(e.form,"DepartmentId",t)},expression:"form.DepartmentId"}})],1),a("el-form-item",{attrs:{label:"领料班组",prop:"WorkingTeamId"}},[a("SelectTeam",{model:{value:e.form.WorkingTeamId,callback:function(t){e.$set(e.form,"WorkingTeamId",t)},expression:"form.WorkingTeamId"}})],1),a("el-form-item",{attrs:{label:"领料/退库人",prop:"ReceiveUserId"}},[a("SelectDepartmentUser",{attrs:{"department-id":e.form.DepartmentId},model:{value:e.form.ReceiveUserId,callback:function(t){e.$set(e.form,"ReceiveUserId",t)},expression:"form.ReceiveUserId"}})],1),a("el-form-item",{attrs:{label:"工序",prop:"Use_Processing_Id"}},[a("SelectProcess",{model:{value:e.form.Use_Processing_Id,callback:function(t){e.$set(e.form,"Use_Processing_Id",t)},expression:"form.Use_Processing_Id"}})],1),a("el-form-item",{attrs:{label:"供应商/甲方",prop:"Supplier"}},[a("SelectExternal",{model:{value:e.form.Supplier,callback:function(t){e.$set(e.form,"Supplier",t)},expression:"form.Supplier"}})],1),a("el-form-item",{attrs:{label:"类型",prop:"OutStoreTypeList"}},[a("SelectMaterialStoreType",{attrs:{type:"AuxAllOutStoreType",multiple:""},model:{value:e.form.OutStoreTypeList,callback:function(t){e.$set(e.form,"OutStoreTypeList",t)},expression:"form.OutStoreTypeList"}})],1),a("el-form-item",{attrs:{label:"辅料名称",prop:"AuxName"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.AuxName,callback:function(t){e.$set(e.form,"AuxName",t)},expression:"form.AuxName"}})],1)],a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:e.tbLoading||e.btnLoading},on:{click:function(t){return e.fetchData(1)}}},[e._v("搜索")]),a("el-button",{attrs:{disabled:e.tbLoading||e.btnLoading},on:{click:function(t){return e.handleReset("form1")}}},[e._v("重置 ")])],1),a("div",{staticStyle:{"margin-left":"auto"}},[a("DynamicTableFields",{attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.changeColumn}})],1)],2)],1),a("main",{staticClass:"main-wrapper fff"},[e.isReceive?[a("div",{staticClass:"total-container"},[a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-item"},[a("div",[e._v("入库金额")]),a("div",[e._v(e._s(e.sum.RukuTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("退货金额")]),a("div",[e._v(e._s(e.sum.TuihuoTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("合计")]),a("div",{staticClass:"font-weight"},[e._v(e._s(Number((e.sum.RukuTaxAmount+e.sum.TuihuoTaxAmount).toFixed(2)))+"元")])]),a("div",{staticClass:"tag"},[e._v("含税")])]),a("div",{staticClass:"total-wrapper orange"},[a("div",{staticClass:"total-item"},[a("div",[e._v("入库金额")]),a("div",[e._v(e._s(e.sum.RukuNoTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("退货金额")]),a("div",[e._v(e._s(e.sum.TuihuoNoTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("合计")]),a("div",{staticClass:"font-weight"},[e._v(e._s(Number((e.sum.RukuNoTaxAmount+e.sum.TuihuoNoTaxAmount).toFixed(2)))+"元")])]),a("div",{staticClass:"tag"},[e._v("不含税")])])])]:[a("div",{staticClass:"total-container"},[a("div",{staticClass:"total-wrapper"},[a("div",{staticClass:"total-item"},[a("div",[e._v("出库金额")]),a("div",[e._v(e._s(e.sum.ChukuTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("退库金额")]),a("div",[e._v(e._s(e.sum.TuikuTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("合计")]),a("div",{staticClass:"font-weight"},[e._v(e._s(Number((e.sum.ChukuTaxAmount+e.sum.TuikuTaxAmount).toFixed(2)))+"元")])]),a("div",{staticClass:"tag"},[e._v("含税")])]),a("div",{staticClass:"total-wrapper orange"},[a("div",{staticClass:"total-item"},[a("div",[e._v("出库金额")]),a("div",[e._v(e._s(e.sum.ChukuNoTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("退库金额")]),a("div",[e._v(e._s(e.sum.TuikuNoTaxAmount)+"元")])]),a("div",{staticClass:"total-item"},[a("div",[e._v("合计")]),a("div",{staticClass:"font-weight"},[e._v(e._s(Number((e.sum.ChukuNoTaxAmount+e.sum.TuikuNoTaxAmount).toFixed(2)))+"元")])]),a("div",{staticClass:"tag"},[e._v("不含税")])])])],a("div",{directives:[{name:"loading",rawName:"v-loading",value:!e.showTable,expression:"!showTable"}],staticClass:"tb-x"},[e.showTable?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto",loading:e.tbLoading,"show-overflow":"",stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Column_Id,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,align:t.Align,fixed:t.Is_Frozen?t.Frozen_Dirction||"left":""},scopedSlots:e._u([{key:"default",fn:function(a){var o=a.row;return[e._v(" "+e._s(o[t.Code]||"-")+" ")]}}],null,!0)})]}))],2):e._e()],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)],2)])},r=[]},"7d68":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var r=o(a("5530")),i=o(a("c14f")),s=o(a("1da1")),l=a("c685"),n=o(a("15ac")),u=o(a("333d")),c=a("8975"),m=o(a("807d")),d=a("9d7e2"),f=a("b4f1");t.default={name:"PROWarMaterialCostDetail",components:{Pagination:u.default},mixins:[n.default,m.default],data:function(){return{total:0,queryInfo:{Page:1,PageSize:20},btnLoading:!1,tbLoading:!1,tablePageSize:l.tablePageSize,tbData:[],columns:[],form:{PurchaseNo:"",Project_Id:"",AuxName:"",BeginDate:"",EndDate:"",InStoreTypeList:[],OutStoreTypeList:[],StoreType:"1",AuxSpec:"",Use_Processing_Id:""},sum:{}}},computed:{isReceive:function(){return"1"==this.form.StoreType},gridCode:function(){return this.isReceive?"PROAuxMaterialCostDetailReceiving":"PROAuxMaterialCostDetailPicking"},dateRange:{get:function(){return[(0,c.timeFormat)(this.form.BeginDate),(0,c.timeFormat)(this.form.EndDate)]},set:function(e){if(e){var t=e[0],a=e[1];this.form.BeginDate=(0,c.timeFormat)(t),this.form.EndDate=(0,c.timeFormat)(a)}else this.form.BeginDate="",this.form.EndDate=""}}},mounted:function(){this.changeColumn(),this.fetchData()},methods:{changeColumn:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.showTable=!1,t.n=1,e.getTableConfig(e.gridCode);case 1:e.showTable=!0;case 2:return t.a(2)}}),t)})))()},handleClick:function(){this.changeColumn(),this.fetchData(1)},fetchData:function(e){var t=this;e&&(this.queryInfo.Page=e),this.tbLoading=!0;var a=(0,r.default)((0,r.default)({Mat_Type:1},this.form),this.queryInfo);(0,d.GetAuxCostReport)(a).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data.map((function(e){return e.TaxAmount=e.TaxAmount?(0,f.comdify)(e.TaxAmount+""):"-",e.NoTaxAmount=e.NoTaxAmount?(0,f.comdify)(e.NoTaxAmount+""):"-",e})),t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1})),(0,d.GetAuxCostReportSummary)(a).then((function(e){e.IsSucceed?t.sum=e.Data:t.$message({message:e.Message,type:"error"})}))},handleReset:function(e){this.dateRange="",this.$refs[e].resetFields(),this.form={PurchaseNo:"",Project_Id:"",AuxName:"",BeginDate:"",EndDate:"",InStoreTypeList:[],OutStoreTypeList:[],StoreType:this.form.StoreType,AuxSpec:"",Use_Processing_Id:""},this.fetchData(1)}}}},ef0d:function(e,t,a){},f35f:function(e,t,a){"use strict";a("ef0d")},f3be:function(e,t,a){"use strict";a.r(t);var o=a("7d68"),r=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);t["default"]=r.a}}]);