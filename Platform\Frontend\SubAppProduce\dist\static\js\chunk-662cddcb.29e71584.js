(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-662cddcb"],{"0751":function(t,e,i){"use strict";i.d(e,"a",(function(){return n})),i.d(e,"b",(function(){return a}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[i("div",{staticClass:"sch-detail"},[i("header",[i("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),i("span",[t._v(t._s(t.pageTile))]),i("div",{staticClass:"right-fix"},[i("el-button",{attrs:{type:"primary",loading:t.btnLoading,disabled:t.isClicked},on:{click:t.save}},[t._v("保存")]),i("el-button",{on:{click:t.tagBack}},[t._v("取消")])],1)],1),i("div",{staticClass:"header"},[i("el-form",{attrs:{inline:!0,"label-width":"90px"}},[i("el-row",{attrs:{gutter:16}},[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"项目名称",required:""}},[i("el-select",{attrs:{placeholder:"选择项目",disabled:""},model:{value:t.stockinEntity.Project_Id,callback:function(e){t.$set(t.stockinEntity,"Project_Id",e)},expression:"stockinEntity.Project_Id"}},t._l(t.projects,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)],1),1==t.listType?[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"生产单元",required:""}},[i("el-select",{attrs:{placeholder:"生产单元",disabled:""},model:{value:t.stockinEntity.InstallUnit_Id,callback:function(e){t.$set(t.stockinEntity,"InstallUnit_Id",e)},expression:"stockinEntity.InstallUnit_Id"}},t._l(t.units,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1)],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"选择仓库",required:""}},[i("el-select",{attrs:{placeholder:"选择仓库"},on:{change:t.warehouseChange},model:{value:t.stockinEntity.Warehouse_Id,callback:function(e){t.$set(t.stockinEntity,"Warehouse_Id",e)},expression:"stockinEntity.Warehouse_Id"}},t._l(t.warehouses,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"选择库位",required:""}},[i("el-select",{attrs:{placeholder:"选择库位"},model:{value:t.stockinEntity.Location_Id,callback:function(e){t.$set(t.stockinEntity,"Location_Id",e)},expression:"stockinEntity.Location_Id"}},t._l(t.warelocations,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"打包件编号"}},[i("el-input",{staticStyle:{width:"100px"},attrs:{placeholder:"打包件编号"},model:{value:t.stockinEntity.Component_Code,callback:function(e){t.$set(t.stockinEntity,"Component_Code",e)},expression:"stockinEntity.Component_Code"}})],1)],1)]:t._e(),[i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"入库类型"}},[i("el-select",{attrs:{placeholder:"选择业务类型",disabled:""},model:{value:t.stockinEntity.In_Type,callback:function(e){t.$set(t.stockinEntity,"In_Type",e)},expression:"stockinEntity.In_Type"}},t._l(t.StockInTypes,(function(t,e){return i("el-option",{key:e,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],i("el-col",{attrs:{span:6}},[i("el-form-item",{attrs:{label:"备注"}},[i("el-input",{attrs:{placeholder:"备注..."},model:{value:t.stockinEntity.Remark,callback:function(e){t.$set(t.stockinEntity,"Remark",e)},expression:"stockinEntity.Remark"}})],1)],1)],2)],1)],1),i("div",{staticClass:"header",staticStyle:{padding:"16px 0"}},[2==t.listType?i("el-button",{attrs:{size:"mini",disabled:t.checkedRows<=0},on:{click:function(e){return t.openDialog({title:"修改库位",component:"LocationChagne",width:"450px",props:{warehouses:t.warehouses}})}}},[t._v("修改位置")]):t._e(),i("el-button",{attrs:{type:"primary",size:"mini",disabled:!(t.stockinEntity.Project_Id&&t.stockinEntity.Warehouse_Id&&t.stockinEntity.Location_Id)&&1==t.listType||2==t.listType&&(!t.stockinEntity.Project_Id||!t.stockinEntity.In_Type)},on:{click:function(e){return t.openDialog({title:"添加明细",component:"AddDetail",width:"60%",props:{ctype:t.listType,entity:t.stockinEntity,warehouses:t.warehouses}})}}},[t._v("添加明细")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:t.checkedRows.length<=0},on:{click:t.deleteRows}},[t._v("删除明细")])],1),i("div",{staticClass:"twrap"},[i("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.stockinEntity.details,border:""},on:{multiSelectedChange:t.multiSelectedChange},scopedSlots:t._u([{key:"NetWeight",fn:function(e){var i=e.row;return[t._v(" "+t._s(((1==t.listType?i["Num"]:1)*i["NetWeight"]).toFixed(2))+" ")]}},{key:"op",fn:function(e){var n=e.row;return[n["Is_Pack"]?i("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openDialog({title:"打包件明细",component:"PackDetails",width:"75%",props:{row:n}})}}},[t._v("查看详情")]):t._e()]}}])},[i("template",{slot:"append"},[i("div",{staticClass:"tb-status"},[i("strong",[t._v("合计: ")]),i("span",[t._v(t._s(t.stockinEntity.details.length)+" 个")]),i("span",[t._v(t._s(t.sumWeight)+" 吨")])])])],2)],1)]),i("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[i("keep-alive",[t.dialogShow?i(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},a=[]},"121a":function(t,e,i){"use strict";var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af"),i("4de4"),i("7db0"),i("14d9"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("a9e3"),i("b680"),i("d3b7"),i("25f0"),i("3ca3"),i("159b"),i("ddb0");var a=i("1b69"),o=n(i("0f97")),s=n(i("e1c3")),c=i("f2f6"),r=i("209b"),l=i("6186"),d=n(i("77ad")),u=n(i("808c")),f=i("ed08");e.default={name:"AddStock",components:{DynamicDataTable:o.default,AddDetail:s.default,PackDetails:d.default,LocationChagne:u.default},data:function(){return{loading:!1,btnLoading:!1,isClicked:!1,gridCode:"pro_packing_in_detail_list",stockinEntity:{Id:"",In_Type:"",Remark:"",Location_Id:"",Warehouse_Id:"",Project_Id:"",InstallUnit_Id:"",InstallUnit_Name:"",Component_Code:"",Factory_Id:"",Packing:{},details:[]},listType:"",pageTile:"",projects:[],units:[],warehouses:[],warelocations:[],tbConfig:{},columns:[],checkedRows:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},StockInTypes:r.StockInTypes}},computed:{sumWeight:function(){var t=this,e=0,i=1;return this.stockinEntity.details.forEach((function(n){var a;1==t.listType&&(i=null!==(a=n.Num)&&void 0!==a?a:1);e+=Number(n.NetWeight*i)||0})),(e/1e3).toFixed(3)}},created:function(){var t=this;this.listType=this.$route.query.type,2==this.listType&&(this.gridCode="pro_component_in_detail_list"),this.stockinEntity.Id=this.$route.query.id,this.stockinEntity.In_Type=this.$route.params.In_Type,this.stockinEntity.Project_Id=this.$route.params.Project_Id,this.stockinEntity.InstallUnit_Id=this.$route.params.InstallUnit_Id,this.stockinEntity.InstallUnit_Name=this.$route.params.InstallUnit_Name,this.stockinEntity.Factory_Id=this.$route.params.Factory_Id,this.stockinEntity.Warehouse_Id=this.$route.params.Warehouse_Id,this.stockinEntity.Location_Id=this.$route.params.Location_Id,this.stockinEntity.Component_Code=this.$route.params.Code,this.stockinEntity.Remark=this.$route.params.Remark,this.getWarehouse(),1===this.listType?(this.pageTile="直发件入库实收单",this.stockinEntity.In_Type="生产入库"):this.pageTile="成品入库实收单",Promise.all([(0,a.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data,(0,c.GetInstallUnitList)({Project_Id:t.stockinEntity.Project_Id}).then((function(e){e.IsSucceed&&(t.units=e.Data,t.$forceUpdate())})))})),(0,r.GetWarehouseListOfCurFactory)().then((function(e){e.IsSucceed&&(t.warehouses=e.Data)}))]).then((function(){(0,l.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})),t.stockinEntity.Id&&((0,r.GetComponentStockInEntity)(t.stockinEntity.Id).then((function(e){var i,n,a,o;e.IsSucceed&&(t.stockinEntity=Object.assign({},t.stockinEntity,e.Data.entity,{packing:e.Data.packing}),e.Data.packing&&(t.stockinEntity.Component_Code=e.Data.packing.Code,t.stockinEntity.InstallUnit_Id=null!==(i=null===(n=e.Data.packing)||void 0===n?void 0:n.InstallUnit_Id)&&void 0!==i?i:t.stockinEntity.InstallUnit_Id,t.stockinEntity.Location_Id=null===(a=e.Data.packing)||void 0===a?void 0:a.Location_Id,t.stockinEntity.Warehouse_Id=null===(o=e.Data.packing)||void 0===o?void 0:o.Warehouse_Id,t.getWarehouse()))})),(0,r.GetStockInDetailList)(t.stockinEntity.Id).then((function(e){e.IsSucceed&&(e.Data.forEach((function(t){t.Is_Persistence=!0})),t.stockinEntity.details=e.Data)})))}))},methods:{getWarehouse:function(){var t=this;this.stockinEntity.Warehouse_Id&&(0,r.GetLocationList)({Warehouse_Id:this.stockinEntity.Warehouse_Id}).then((function(e){e.IsSucceed&&(t.warelocations=e.Data,t.$forceUpdate())}))},tagBack:function(){(0,f.closeTagView)(this.$store,this.$route)},multiSelectedChange:function(t){this.checkedRows=t},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120})},setCols:function(t){t.forEach((function(t){t.Code})),this.columns=t.concat([])},setGridData:function(t){this.data=t.Data},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.type,i=t.data;switch(this.dialogCancel(),e){case"reload":break;case"merge":this.mergeEntity(i);break;case"loc_change":this.changeLocation(i)}},mergeEntity:function(t){var e,i=this;null===(e=t.details)||void 0===e||e.forEach((function(t){var e;1==i.listType&&(e=i.stockinEntity.details.find((function(e){return e.InstallUnit_Id===t.InstallUnit_Id&&e.Component_Code===t.Component_Code}))),2==i.listType&&(e=i.stockinEntity.details.find((function(e){return e.Unique_Code===t.Unique_Code}))),e?(e.Num+=t.Num,e.Num>e.Wait_Pack_Num&&(e.Num=e.Wait_Pack_Num)):i.stockinEntity.details.push(t)})),this.stockinEntity=Object.assign({},this.stockinEntity)},changeLocation:function(t){this.checkedRows.forEach((function(e){var i,n,a,o;e.Warehouse_Id=null===(i=t.ware)||void 0===i?void 0:i.Id,e.Warehouse_Name=null===(n=t.ware)||void 0===n?void 0:n.Display_Name,e.Location_Id=null===(a=t.loc)||void 0===a?void 0:a.Id,e.Location_Name=null===(o=t.loc)||void 0===o?void 0:o.Display_Name}))},warehouseChange:function(t){var e=this;this.stockinEntity.Location_Id="";var i=this.warehouses.find((function(e){return e.Id===t}));this.stockinEntity.Factory_Id=null===i||void 0===i?void 0:i.Factory_Id,(0,r.GetLocationList)({Warehouse_Id:t}).then((function(t){t.IsSucceed&&(e.warelocations=t.Data)}))},deleteRows:function(){var t=this;1==this.listType?(this.stockinEntity=Object.assign({},this.stockinEntity),this.checkedRows.forEach((function(e){var i=e.Component_Code;t.stockinEntity.details=t.stockinEntity.details.filter((function(t){return i!==t.Component_Code}))})),this.stockinEntity=Object.assign({},this.stockinEntity)):this.checkedRows.forEach((function(e){var i=e.Component_Id;t.stockinEntity.details=t.stockinEntity.details.filter((function(t){return i!==t.Component_Id}))}))},save:function(){var t=this;this.isClicked=!0;var e=this.formatSaveData();this.btnLoading=!0,1!=this.listType||this.stockinEntity.Id?(0,r.SaveStockIn)(e).then((function(e){e.IsSucceed?(t.$message.success(e.Message),(0,f.closeTagView)(t.$store,t.$route)):(t.btnLoading=!1,t.$message.warning(e.Message)),t.btnLoading=!1})):(0,r.CheckPackCode)(e.packing.Code).then((function(i){i.IsSucceed?(0,r.SaveStockIn)(e).then((function(e){e.IsSucceed?(t.$message.success(e.Message),(0,f.closeTagView)(t.$store,t.$route)):(t.isClicked=!1,t.$message.warning(e.Message)),t.btnLoading=!1})):(t.isClicked=!1,t.btnLoading=!1,t.$message.warning("打包件编号未通过校验，请修改后重试"))}))},formatSaveData:function(){var t,e,i,n,a,o=this,s={},c=this.projects.find((function(t){return t.Id===o.stockinEntity.Project_Id}));return s.details=this.stockinEntity.details,s.entity={Id:this.stockinEntity.Id,In_Type:this.stockinEntity.In_Type,Type:1==this.listType?2:1,Project_Id:this.stockinEntity.Project_Id,Project_Name:null!==(t=c.Name)&&void 0!==t?t:"",Remark:this.stockinEntity.Remark,In_Date:this.stockinEntity.In_Date||new Date,Warehouse_Id:this.stockinEntity.Warehouse_Id,Location_Id:this.stockinEntity.Location_Id},s.packing={Project_Id:this.stockinEntity.Project_Id,Code:this.stockinEntity.Component_Code,Warehouse_Id:(null===(e=this.stockinEntity.packing)||void 0===e?void 0:e.Warehouse_Id)||this.stockinEntity.Warehouse_Id,Location_Id:(null===(i=this.stockinEntity.packing)||void 0===i?void 0:i.Location_Id)||this.stockinEntity.Location_Id,Project_Name:null!==(n=c.Name)&&void 0!==n?n:"",InstallUnit_Id:(null===(a=this.stockinEntity.packing)||void 0===a?void 0:a.InstallUnit_Id)||this.stockinEntity.InstallUnit_Id,Remark:this.stockinEntity.Remark},s}}}},1860:function(t,e,i){"use strict";var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;n(i("53ca"));var a=n(i("5530"));i("99af"),i("7db0"),i("d81d"),i("14d9"),i("e9f5"),i("f665"),i("7d54"),i("ab43"),i("a9e3"),i("d3b7"),i("159b");var o=n(i("0f97")),s=i("6186"),c=i("2dd9"),r=i("209b"),l=n(i("b775"));e.default={name:"AddDetail",components:{DynamicDataTable:o.default},props:{entity:{type:Object,default:function(){return{}}},ctype:{type:Number,default:""},warehouses:{type:Array,default:function(){return[]}}},data:function(){return{gridCode:1===this.ctype?"pro_waiting_direct_in_list":"pro_waiting_component_in_list",tbConfig:{},columns:[],data:[],warelocations:[],checkedRows:[],form:{Warehouse_Id:"",Location_Id:""},fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},ParameterJson:[],persistences:[],rules:{Warehouse_Id:[{required:!0,message:"请选择仓库",trigger:"change"}],Location_Id:[{required:!0,message:"请选择库位",trigger:"change"}]}}},computed:{validData:function(){var t=this,e=[];if(1===this.ctype)this.data.forEach((function(i){var n=t.entity.details.find((function(t){return t.InstallUnit_Id===i.InstallUnit_Id&&t.Component_Code===i.Component_Code})),a=Object.assign({},i,{Wait_Pack_Num:n?i.Wait_Pack_Num-n.Num:i.Wait_Pack_Num});e.push(a)}));else{var i=this.entity.details.map((function(t){return t.Unique_Code}));this.data.forEach((function(t){i.indexOf(t.Unique_Code)<0&&e.push(t)}))}return e}},created:function(){var t=this;this.entity.Warehouse_Id&&(this.form.Warehouse_Id=this.entity.Warehouse_Id,(0,r.GetLocationList)({Warehouse_Id:this.entity.Warehouse_Id}).then((function(e){e.IsSucceed&&(t.warelocations=e.Data)}))),this.form.Location_Id=this.entity.Location_Id,(0,s.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList),t.tableSearch())})).then((function(){t.tableSearch(t.$refs.table.searchedField,1)})),this.entity.Id&&(0,r.GetStockInDetailList)(this.entity.Id).then((function(e){e.IsSucceed&&(t.persistences=e.Data)}))},methods:{cancel:function(){this.$emit("dialogCancel")},multiSelectedChange:function(t){this.checkedRows=t},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120,Height:480}),this.filterData.PageSize=t.Row_Number},setCols:function(t){this.columns=t.concat([])},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},gridSizeChange:function(t){t.page;var e=t.size;this.setGrid((0,a.default)((0,a.default)({},this.tbConfig),{},{Row_Number:e})),this.filterData.PageSize=e,this.filterData.Page=1,this.tableSearch(this.$refs.table.searchedField)},gridPageChange:function(t){var e=t.page;this.tableSearch(this.$refs.table.searchedField,e)},tableSearch:function(t,e){var i=this;this.ParameterJson=(0,c.setParameterJson)(t,this.columns),this.filterData.Page=null!==e&&void 0!==e?e:1,(0,l.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{ParameterJson:this.ParameterJson,Project_Id:this.entity.Project_Id,InstallUnit_Id:this.entity.InstallUnit_Id,Factory_Id:this.entity.Factory_Id,C_Type:1===this.ctype?"直发件":"",In_Type:this.entity.In_Type})}).then((function(t){t.IsSucceed&&(1==i.ctype&&t.Data.Data.forEach((function(t){var e=i.persistences.find((function(e){return e.InstallUnit_Id===t.InstallUnit_Id&&e.Component_Code===t.Component_Code}));e&&(t.Wait_Pack_Num+=e.Num)})),i.setGridData(t.Data))}))},cellEditorChanged:function(t){t.index;var e=t.row,i=t.key,n=t.value;"Num"===i&&(n>e["Wait_Pack_Num"]&&(e[i]=e["Wait_Pack_Num"]),n<0&&(e[i]=0))},save:function(){var t=this;if(this.checkedRows.length<=0)this.$message.warning("尚未选择明细");else if(1===this.ctype){if(this.checkedRows.find((function(t){return 0===t.Num||isNaN(t.Num)})))return this.$message.warning("所选明细中带有打包数量为 0 的记录");this.form.details=this.checkedRows.map((function(t){return Object.assign({},t,{Warehouse_Id:t.Warehouse_Id,Location_Id:t.Warehouse_Id,Location_Name:t.Location_Name,Warehouse_Name:t.Warehouse_Name})})),this.$emit("dialogFormSubmitSuccess",{type:"merge",data:this.form})}else this.$refs.form.validate((function(e){if(e){if(2===t.ctype&&(!t.form.Warehouse_Id||!t.form.Location_Id))return t.$message.warning("为当前所选的明细选择仓库及库位");t.form.details=t.checkedRows.map((function(e){return Object.assign({},e,{Warehouse_Id:t.form.Warehouse_Id,Location_Id:t.form.Location_Id,Location_Name:t.form.Location_Name,Warehouse_Name:t.form.Warehouse_Name})})),t.$emit("dialogFormSubmitSuccess",{type:"merge",data:t.form})}}))},wareChange:function(t){var e=this;this.form.Location_Id="";var i=this.warehouses.find((function(e){return e.Id===t}));this.form.Warehouse_Name=i.Display_Name,this.form.Factory_Id=null===i||void 0===i?void 0:i.Factory_Id,(0,r.GetLocationList)({Warehouse_Id:t}).then((function(t){t.IsSucceed&&(e.warelocations=t.Data)}))},locChange:function(t){var e=this.warelocations.find((function(e){return e.Id===t}));this.form.Location_Name=e.Display_Name}}}},"2dd9":function(t,e,i){"use strict";function n(t,e){t||(t={}),e||(e=[]);var i=[],n=function(n){var a;a="[object Array]"===Object.prototype.toString.call(t[n])?t[n]:[t[n]];var o=a.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(o.filter((function(t){return null!==t})).length<=0&&(o=null),o){var s={Key:n,Value:o,Type:"",Filter_Type:""},c=e.find((function(t){return t.Code===n}));s.Type=null===c||void 0===c?void 0:c.Type,s.Filter_Type=null===c||void 0===c?void 0:c.Filter_Type,i.push(s)}};for(var a in t)n(a);return i}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=n,i("4de4"),i("7db0"),i("d81d"),i("14d9"),i("e9f5"),i("910d"),i("f665"),i("ab43"),i("d3b7"),i("25f0")},"3faa":function(t,e,i){"use strict";i("7917f")},"44ca":function(t,e,i){"use strict";i.d(e,"a",(function(){return n})),i.d(e,"b",(function(){return a}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"stockin-details"},[1!=t.ctype?i("el-form",{ref:"form",attrs:{model:t.form,"label-width":"60px",rules:t.rules}},[i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{"label-width":"120px",label:"选择存放位置："}})],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[i("el-select",{attrs:{placeholder:"请选择仓库"},on:{change:t.wareChange},model:{value:t.form.Warehouse_Id,callback:function(e){t.$set(t.form,"Warehouse_Id",e)},expression:"form.Warehouse_Id"}},t._l(t.warehouses,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[i("el-select",{attrs:{placeholder:"请选择库位"},on:{change:t.locChange},model:{value:t.form.Location_Id,callback:function(e){t.$set(t.form,"Location_Id",e)},expression:"form.Location_Id"}},t._l(t.warelocations,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1)],1)],1):t._e(),i("div",[i("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.validData,border:"",total:t.filterData.TotalCount,page:t.filterData.Page},on:{multiSelectedChange:t.multiSelectedChange,tableSearch:t.tableSearch,cellEditorChanged:t.cellEditorChanged,gridSizeChange:t.gridSizeChange,gridPageChange:t.gridPageChange}})],1),i("div",{staticStyle:{"text-align":"center"}},[i("el-button",{on:{click:t.cancel}},[t._v("取消")]),i("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("添加")])],1)],1)},a=[]},"5e80":function(t,e,i){},6640:function(t,e,i){"use strict";i.r(e);var n=i("879f"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"75d2":function(t,e,i){"use strict";i.d(e,"a",(function(){return n})),i.d(e,"b",(function(){return a}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{"margin-top":"-10px"}},[i("el-form",{attrs:{model:t.form,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"仓库"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择仓库"},on:{change:t.wareChange},model:{value:t.form.Warehouse_Id,callback:function(e){t.$set(t.form,"Warehouse_Id",e)},expression:"form.Warehouse_Id"}},t._l(t.warehouses,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),i("el-form-item",{attrs:{label:"库位名称"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择库位"},model:{value:t.form.Location_Id,callback:function(e){t.$set(t.form,"Location_Id",e)},expression:"form.Location_Id"}},t._l(t.locationList,(function(t){return i("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),i("div",{staticStyle:{"text-align":"center"}},[i("el-button",{on:{click:t.cancel}},[t._v("取消")]),i("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确定")])],1)],1)],1)},a=[]},"77ad":function(t,e,i){"use strict";i.r(e);var n=i("b480"),a=i("6640");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var s=i("2877"),c=Object(s["a"])(a["default"],n["a"],n["b"],!1,null,null,null);e["default"]=c.exports},"7917f":function(t,e,i){},"808c":function(t,e,i){"use strict";i.r(e);var n=i("75d2"),a=i("c608");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var s=i("2877"),c=Object(s["a"])(a["default"],n["a"],n["b"],!1,null,null,null);e["default"]=c.exports},"879f":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a15b"),i("d81d"),i("e9f5"),i("ab43"),i("d3b7"),i("25f0");var n=i("6186"),a=i("209b"),o=i("ed08");e.default={name:"PackDetails",props:{row:{type:Object,default:function(){return{}}}},data:function(){return{gridCode:"pro_packing_in_detail_list",columns:[],dialogData:[]}},created:function(){var t=this;(0,n.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.columns=e.Data.ColumnList)})),(0,a.GetPackingGroupByDirectDetailList)({Unique_Code:this.row.Unique_Code}).then((function(e){e.IsSucceed&&(t.dialogData=e.Data)}))},methods:{exportExcel:function(){var t=this;(0,a.ExportPackingInInfo)(this.row.Id).then((function(e){if(e.IsSucceed){var i=(0,o.combineURL)(t.$baseUrl,e.Data.split("/").map((function(t){return encodeURIComponent(t.toString())})).join("/"));window.open(i,"_blank")}}))},printList:function(){}}}},"8ba9":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("7db0"),i("e9f5"),i("f665"),i("d3b7");var n=i("209b");e.default={name:"LocationChagne",props:{warehouses:{type:Array,default:function(){return[]}}},data:function(){return{locationList:[],form:{Warehouse_Id:"",Location_Id:"",Factory_Id:""}}},methods:{wareChange:function(t){var e=this;this.form.Location_Id="";var i=this.warehouses.find((function(e){return e.Id===t}));this.form.Factory_Id=null===i||void 0===i?void 0:i.Factory_Id,(0,n.GetLocationList)({Warehouse_Id:t}).then((function(t){t.IsSucceed&&(e.locationList=t.Data)}))},cancel:function(){this.$emit("dialogCancel")},submit:function(){var t=this;if(!this.form.Warehouse_Id||!this.form.Location_Id)return this.$message.warning("选择有效的仓库及库位");var e=this.warehouses.find((function(e){return e.Id===t.form.Warehouse_Id})),i=this.locationList.find((function(e){return e.Id===t.form.Location_Id}));this.$emit("dialogFormSubmitSuccess",{type:"loc_change",data:{ware:e,loc:i}})}}}},"8fdc":function(t,e,i){},"9b1e":function(t,e,i){"use strict";i.r(e);var n=i("121a"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},a03c:function(t,e,i){},b480:function(t,e,i){"use strict";i.d(e,"a",(function(){return n})),i.d(e,"b",(function(){return a}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticStyle:{"margin-top":"-10px","margin-bottom":"12px"}},[i("el-button",{attrs:{size:"mini"},on:{click:t.printList}},[t._v("打印")]),i("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.exportExcel}},[t._v("导出")])],1),i("el-table",{attrs:{height:560,data:t.dialogData}},t._l(t.columns,(function(t){return i("el-table-column",{key:t.Code,attrs:{prop:t.Code,label:t.Display_Name}})})),1)],1)},a=[]},c608:function(t,e,i){"use strict";i.r(e);var n=i("8ba9"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},d42b:function(t,e,i){"use strict";i("5e80")},e1c3:function(t,e,i){"use strict";i.r(e);var n=i("44ca"),a=i("f49b");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("3faa"),i("d42b");var s=i("2877"),c=Object(s["a"])(a["default"],n["a"],n["b"],!1,null,"7c06d43a",null);e["default"]=c.exports},f08f:function(t,e,i){"use strict";i("8fdc")},f49b:function(t,e,i){"use strict";i.r(e);var n=i("1860"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},fa46:function(t,e,i){"use strict";i("a03c")},ffa9:function(t,e,i){"use strict";i.r(e);var n=i("0751"),a=i("9b1e");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("f08f"),i("fa46");var s=i("2877"),c=Object(s["a"])(a["default"],n["a"],n["b"],!1,null,"791cab54",null);e["default"]=c.exports}}]);