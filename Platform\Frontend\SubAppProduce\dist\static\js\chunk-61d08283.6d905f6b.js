(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-61d08283"],{"1e19":function(e,t,o){"use strict";o.d(t,"a",(function(){return a})),o.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[o("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"cs-z-page-main-content"},[o("SearchHeader",{attrs:{padding:"0"},scopedSlots:e._u([{key:"left",fn:function(){return[o("el-form",{ref:"searchForm",attrs:{model:e.searchForm,"label-width":"80px",inline:!0}},[o("el-form-item",{attrs:{label:"统计日期",prop:"finishDate"}},[o("el-date-picker",{attrs:{type:"date",align:"right","unlink-panels":"",clearable:!1,"value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.searchForm.finishDate,callback:function(t){e.$set(e.searchForm,"finishDate",t)},expression:"searchForm.finishDate"}})],1),o("el-form-item",{attrs:{label:"班组",prop:"teamId"}},[o("el-select",{attrs:{clearable:"",placeholder:"请选择..."},model:{value:e.searchForm.teamId,callback:function(t){e.$set(e.searchForm,"teamId",t)},expression:"searchForm.teamId"}},[e._l(e.workingTeamsList,(function(e){return o("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})}))],2)],1),o("el-form-item",[o("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),o("el-button",{on:{click:function(t){e.resetForm(),e.handleSearch()}}},[e._v("重置")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[o("el-button",{attrs:{type:"success"},on:{click:e.handleExport}},[e._v("导出")])]},proxy:!0}])}),o("div",{staticClass:"tb-wrapper"},[o("vxe-table",{ref:"vxeTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",stripe:"",align:"left",height:"100%",resizable:"","empty-text":"暂无数据","show-footer":"","footer-cell-class-name":e.footerCellClassName,"footer-method":e.footerMethod,data:e.tableData}},e._l(e.tableColumn,(function(e){return o("vxe-column",{key:e.key,attrs:{type:e.type,field:e.field,title:e.title,fixed:e.fixed,"min-width":e.minWidth,width:e.width,"sort-type":e.sortType,sortable:e.sortable,filters:e.filters}})})),1)],1)],1)])},n=[]},3176:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(o("c14f")),r=a(o("1da1"));o("99af"),o("d81d"),o("14d9"),o("a434"),o("e9f5"),o("7d54"),o("ab43"),o("e9c4"),o("a9e3"),o("b64b"),o("d3b7"),o("3ca3"),o("159b"),o("ddb0");var s=a(o("34e9")),i=o("a024"),l=o("586a");a(o("c1df")),t.default={name:"PROGroupProducePlanReport",components:{SearchHeader:s.default},data:function(){return{searchForm:{finishDate:"".concat((new Date).getFullYear(),"-").concat((new Date).getMonth()+1,"-").concat((new Date).getDate()),teamId:""},workingTeamsList:[],workingTeamsCount:0,ProfessionalType:[],tableColumn:[],tableData:[],loading:!1}},created:function(){},mounted:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.getGetWorkingTeamsList(),e.getDataList();case 1:return t.a(2)}}),t)})))()},methods:{defaultPickerDate:function(){var e=new Date,t=new Date;e.setTime(t.getTime()+2592e6);var o=new Date(t),a=new Date(e);this.searchForm.finishDate=[o.getFullYear()+"-"+(o.getMonth()+1)+"-"+o.getDate(),a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate()]},handleSearch:function(){this.loading=!0,this.tableColumn=[],this.tableData=[],this.getDataList()},resetForm:function(){this.$refs["searchForm"].resetFields()},getGetWorkingTeamsList:function(){var e=this;(0,i.GetWorkingTeams)().then((function(t){t.IsSucceed?(e.workingTeamsList=t.Data,e.workingTeamsCount=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})}))},getDataList:function(){var e=this;(0,l.GetFactorySchdulingDayPlanYield)({factoryId:localStorage.getItem("CurReferenceId"),begin:this.searchForm.finishDate,code:this.searchForm.professionalId,teamId:this.searchForm.teamId}).then((function(t){if(t.IsSucceed){e.tableData=t.Data;var o=t.Data[0],a=1;for(var n in o){var r={};r.key=a++,r.field=n,r.title="班组名称"==n?n:"".concat(n,"（t）"),"班组名称"==n?(r.minWidth="200",r.fixed="left"):"平均日排产量"==n||"合计排产总量"==n?(r.minWidth="160",r.sortType="number",r.sortable=!0,r.fixed="right"):(r.minWidth="140",r.sortType="number",r.sortable=!0),e.tableColumn.push(r)}e.loading=!1}else e.$message({type:"error",message:t.Message})}))},handleExport:function(){var e=JSON.parse(JSON.stringify(this.tableData)),t=e[0],a=[],n=[];for(var r in t)"班组名称"==r?a.push(r):a.push(r+"（t）"),n.push(r);a.splice(a.length-1),n.splice(n.length-1),e.push(this.sumsJson);var s=this.formatJson(n,e);o.e("chunk-2d0cc0b6").then(o.t.bind(null,"4bf8",7)).then((function(e){e.export_json_to_excel({header:a,data:s,filename:"".concat(localStorage.getItem("ProjectName")," ~ 班组生产日计划"),autoWidth:!0,bookType:"xlsx"})}))},formatJson:function(e,t){return t.map((function(t){return e.map((function(e){return t[e]}))}))},footerCellClassName:function(e){var t=e.$rowIndex;e.column,e.columnIndex;if(0===t)return"col-footer"},footerMethod:function(e){var t=this,o=e.columns,a=e.data,n=[],r={};return o.forEach((function(e,o){if(0===o)r[e.field]="合计：",n.push("合计：");else{var s=null;s=t.sumNum(a,e.property),r[e.field]=s,n.push(s)}})),this.sumsJson=r,[n]},sumNum:function(e,t){var o=0;return e.forEach((function(e){o+=Number(e[t]?e[t]:0)})),Math.round(1e3*parseFloat(o))/1e3}}}},"511e":function(e,t,o){"use strict";o.r(t);var a=o("3176"),n=o.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},"647f":function(e,t,o){"use strict";o.r(t);var a=o("1e19"),n=o("511e");for(var r in n)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(r);o("a665f");var s=o("2877"),i=Object(s["a"])(n["default"],a["a"],a["b"],!1,null,"6b8513b2",null);t["default"]=i.exports},a024:function(e,t,o){"use strict";var a=o("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=_,t.AddTechnology=l,t.AddWorkingProcess=i,t.DelLib=A,t.DeleteProcess=v,t.DeleteProcessFlow=L,t.DeleteTechnology=G,t.DeleteWorkingTeams=w,t.GetAllProcessList=f,t.GetCheckGroupList=I,t.GetChildComponentTypeList=S,t.GetFactoryAllProcessList=h,t.GetFactoryPeoplelist=C,t.GetFactoryWorkingTeam=y,t.GetGroupItemsList=P,t.GetLibList=s,t.GetLibListType=B,t.GetProcessFlow=m,t.GetProcessFlowListWithTechnology=g,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=W,t.GetProcessListWithUserBase=x,t.GetProcessWorkingTeamBase=M,t.GetTeamListByUser=N,t.GetTeamProcessList=T,t.GetWorkingTeam=b,t.GetWorkingTeamBase=R,t.GetWorkingTeamInfo=F,t.GetWorkingTeams=k,t.GetWorkingTeamsPageList=D,t.SaveWorkingTeams=O,t.UpdateProcessTeam=p;var n=a(o("b775")),r=a(o("4328"));function s(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function l(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function h(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function g(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function y(){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function b(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function T(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function P(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function L(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function G(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function v(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function R(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function W(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function x(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function A(e){return(0,n.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function B(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function M(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function N(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a665f:function(e,t,o){"use strict";o("cbe3")},cbe3:function(e,t,o){}}]);