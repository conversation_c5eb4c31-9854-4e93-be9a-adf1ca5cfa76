(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2d0e2790"],{"7f9d":function(t,e,o){"use strict";var a=o("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPartTeamProcessAllocation=bt,e.AdjustSubAssemblyTeamProcessAllocation=It,e.AdjustTeamProcessAllocation=Nt,e.ApplyCheck=Jt,e.BatchReceiveTaskFromStock=We,e.BatchReceiveTransferTask=Et,e.BatchWithdrawSimplifiedProcessingHistory=pe,e.BeginProcess=Z,e.BochuAddTask=_e,e.CancelNestingBill=z,e.CancelSchduling=kt,e.CancelTransferTask=Mt,e.CancelUnitSchduling=oe,e.CheckSchduling=Zt,e.ComponentAllocationWorkingTeam=P,e.ComponentAllocationWorkingTeamBase=f,e.CreateCompTransferBill=Q,e.CreateCompTransferByTransferCode=X,e.CreatePartTransferByPartCodes=K,e.CreatePartTransferByTaskBill=_,e.CreatePartTransferByTransferCode=V,e.DelSchdulingPlan=Rt,e.DelSchdulingPlanById=Gt,e.DeleteNestingResult=ke,e.DownNestingTaskTemplate=ot,e.ExportAllocationComponent=O,e.ExportSimplifiedProcessingHistory=Fe,e.ExportTaskCodeDetails=Bt,e.ExportTransferCodeDetail=Ft,e.GetAllWorkingTeamComponentCount=p,e.GetAllWorkingTeamComponentCountBase=g,e.GetAllWorkingTeamPartCount=y,e.GetAllWorkingTeamPartCountBase=h,e.GetBuildReturnRecordList=se,e.GetCanSchdulingComps=dt,e.GetCanSchdulingPartList=lt,e.GetCheckUserRankList=ve,e.GetCheckingItemList=Ie,e.GetCheckingQuestionList=Ue,e.GetCompSchdulingInfoDetail=st,e.GetCompSchdulingPageList=mt,e.GetCompTaskPageList=ce,e.GetCompTaskPartCompletionStock=xe,e.GetComponentPartComplexity=T,e.GetCoordinateProcess=G,e.GetDetailSummaryList=be,e.GetDwg=he,e.GetMonthlyFullCheckProducedData=we,e.GetNestingBillBoardPageList=M,e.GetNestingBillDetailList=He,e.GetNestingBillPageList=D,e.GetNestingBillTreeList=Ee,e.GetNestingFiles=Re,e.GetNestingMaterialWithPart=ze,e.GetNestingPartList=Se,e.GetNestingResultPageList=ge,e.GetNestingSurplusList=Te,e.GetNestingTaskInfoDetail=x,e.GetPageProcessTransferDetailBase=ut,e.GetPageSchdulingComps=it,e.GetPartPrepareList=$t,e.GetPartSchdulingCancelHistory=xt,e.GetPartSchdulingInfoDetail=Vt,e.GetPartSchdulingPageList=ct,e.GetPartTaskBoard=b,e.GetPartWithParentPageList=qe,e.GetProcessAllocationComponentBasePageList=S,e.GetProcessAllocationComponentPageList=i,e.GetProcessAllocationPartBasePageList=B,e.GetProcessAllocationPartPageList=C,e.GetProcessPartTransferDetail=tt,e.GetProcessTransferDetail=$,e.GetProcessTransferPageList=j,e.GetSchdulingCancelHistory=Wt,e.GetSchdulingPageList=ft,e.GetSchdulingPartUsePageList=Qt,e.GetSchdulingWorkingTeams=ht,e.GetSemiFinishedStock=Oe,e.GetSimplifiedProcessingHistory=fe,e.GetSimplifiedProcessingSummary=me,e.GetSourceFinishedList=Ce,e.GetStopList=Ke,e.GetTargetReceiveList=Be,e.GetTaskPartPrepareList=te,e.GetTeamCompHistory=nt,e.GetTeamPartUseList=_t,e.GetTeamProcessAllocation=vt,e.GetTeamStockPageList=rt,e.GetTeamTaskAllocationPageList=Dt,e.GetTeamTaskBoardPageList=E,e.GetTeamTaskDetails=Ct,e.GetTeamTaskPageList=Ot,e.GetTenantFactoryType=at,e.GetToReceiveTaskDetailList=ye,e.GetToReceiveTaskList=Le,e.GetTransferCancelDetails=jt,e.GetTransferDetail=wt,e.GetTransferHistory=Ut,e.GetTransferPageList=H,e.GetUnitSchdulingInfoDetail=Xt,e.GetUnitSchdulingPageList=pt,e.GetWorkingTeamCheckingList=Ne,e.GetWorkingTeamComponentCount=m,e.GetWorkingTeamComponentCountBase=s,e.GetWorkingTeamLoadRealTime=le,e.GetWorkingTeamPartCount=A,e.GetWorkingTeamPartCountBase=W,e.GetWorkingTeamsPageList=ie,e.GetYearlyFullCheckProducedData=De,e.ImportNestingFiles=Ge,e.ImportNestingInfo=k,e.ImportSchduling=gt,e.ImportThumbnail=Je,e.ImportUpdateComponentWorkingTeam=u,e.ImportUpdatePartWorkingTeam=L,e.LentakExport=Ze,e.PartsAllocationWorkingTeamBase=N,e.PartsAllocationWrokingTeam=v,e.PartsBatchAllocationWorkingTeamBase=I,e.ProAddQuest=et,e.ProfilesExport=Qe,e.ReceiveTaskFromStock=Ae,e.ReceiveTransferBill=Y,e.ReceiveTransferTask=Ht,e.RevocationComponentAllocation=d,e.SaveChangeZeroComponentRecoil=de,e.SaveCompSchdulingDraft=Yt,e.SaveCompTransferBill=J,e.SaveComponentSchedulingWorkshop=ae,e.SaveNestingPartInfo=F,e.SavePartSchdulingDraft=qt,e.SavePartSchdulingDraftNew=zt,e.SavePartSchedulingWorkshop=ne,e.SavePartSchedulingWorkshopNew=re,e.SavePartTransferBill=q,e.SaveSchdulingDraft=Pt,e.SaveSchdulingTask=Tt,e.SaveSchdulingTaskById=St,e.SaveUnitSchdulingDraftNew=Kt,e.SaveUnitSchedulingWorkshopNew=ue,e.SigmaWOLExport=je,e.SimplifiedProcessing=Pe,e.TeamProcessingByTaskCode=yt,e.TeamTaskProcessing=At,e.TeamTaskTransfer=Lt,e.UpdateBatchCompAllocationWorkingTeamBase=c,e.UpdateBatchPartsAllocationWrokingTeamBase=w,e.UpdateComponentAllocationWorkingTeamBase=l,e.UpdateMachineName=Me,e.UpdatePartsAllocationWorkingTeamBase=U,e.UploadNestingFiles=R,e.WithdrawPicking=Ye,e.WithdrawScheduling=ee;var n=a(o("b775")),r=a(o("4328"));function u(t){return(0,n.default)({url:"/PRO/ProductionTask/ImportUpdateComponentWorkingTeam",method:"post",data:t})}function i(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentPageList",method:"post",data:t})}function d(t){return(0,n.default)({url:"/PRO/ProductionTask/RevocationComponentAllocation",method:"post",data:t})}function s(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCountBase",method:"post",data:t})}function l(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdateComponentAllocationWorkingTeamBase",method:"post",data:t})}function c(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdateBatchCompAllocationWorkingTeamBase",method:"post",data:t})}function P(t){return(0,n.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeam",method:"post",data:r.default.stringify(t)})}function f(t){return(0,n.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeamBase",method:"post",data:t})}function m(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCount",method:"post",data:r.default.stringify(t)})}function p(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCount",method:"post",data:t})}function h(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCountBase",method:"post",data:t})}function g(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCountBase",method:"post",data:t})}function T(t){return(0,n.default)({url:"/PRO/ProductionTask/GetComponentPartComplexity",method:"post",data:r.default.stringify(t)})}function k(t){return(0,n.default)({url:"/PRO/ProductionTask/ImportNestingInfo",method:"post",data:t})}function R(t){return(0,n.default)({url:"/PRO/ProductionTask/UploadNestingFiles",method:"post",data:t})}function G(t){return(0,n.default)({url:"/PRO/ProductionTask/GetCoordinateProcess",method:"post",data:r.default.stringify(t)})}function S(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentBasePageList",method:"post",data:t})}function O(t){return(0,n.default)({url:"/PRO/ProductionTask/ExportAllocationComponent",method:"post",data:t})}function C(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartPageList",method:"post",data:t})}function B(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartBasePageList",method:"post",data:t})}function L(t){return(0,n.default)({url:"/PRO/ProductionTask/ImportUpdatePartWorkingTeam",method:"post",data:t})}function y(t){return(0,n.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCount",method:"post",data:t})}function A(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCount",method:"post",data:r.default.stringify(t)})}function W(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCountBase",method:"post",data:r.default.stringify(t)})}function D(t){return(0,n.default)({url:"/PRO/ProductionTask/GetNestingBillPageList",method:"post",data:t})}function v(t){return(0,n.default)({url:"/PRO/ProductionTask/PartsAllocationWrokingTeam",method:"post",data:r.default.stringify(t)})}function N(t){return(0,n.default)({url:"/PRO/ProductionTask/PartsAllocationWorkingTeamBase",method:"post",data:r.default.stringify(t)})}function I(t){return(0,n.default)({url:"/PRO/ProductionTask/PartsBatchAllocationWorkingTeamBase",method:"post",data:t})}function U(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdatePartsAllocationWorkingTeamBase",method:"post",data:t})}function w(t){return(0,n.default)({url:"/PRO/ProductionTask/UpdateBatchPartsAllocationWrokingTeamBase",method:"post",data:t})}function F(t){return(0,n.default)({url:"/PRO/ProductionTask/SaveNestingPartInfo",method:"post",data:r.default.stringify(t)})}function x(t){return(0,n.default)({url:"/PRO/ProductionTask/GetNestingTaskInfoDetail",method:"post",data:r.default.stringify(t)})}function E(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskBoardPageList",method:"post",data:t})}function H(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferPageList",method:"post",data:t})}function b(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPartTaskBoard",method:"post",data:t})}function M(t){return(0,n.default)({url:"/PRO/ProductionTask/GetNestingBillBoardPageList",method:"post",data:t})}function j(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessTransferPageList",method:"post",data:t})}function Z(t){return(0,n.default)({url:"/PRO/ProductionTask/BeginProcess",method:"post",data:r.default.stringify(t)})}function Q(t){return(0,n.default)({url:"/PRO/ProductionTask/CreateCompTransferBill",method:"post",data:t})}function _(t){return(0,n.default)({url:"/PRO/ProductionTask/CreatePartTransferByTaskBill",method:"post",data:t})}function J(t){return(0,n.default)({url:"/PRO/ProductionTask/SaveCompTransferBill",method:"post",data:t})}function Y(t){return(0,n.default)({url:"/PRO/ProductionTask/ReceiveTransferBill",method:"post",data:r.default.stringify(t)})}function q(t){return(0,n.default)({url:"/PRO/ProductionTask/SavePartTransferBill",method:"post",data:t})}function z(t){return(0,n.default)({url:"/PRO/ProductionTask/CancelNestingBill",method:"post",data:r.default.stringify(t)})}function K(t){return(0,n.default)({url:"/PRO/ProductionTask/CreatePartTransferByPartCodes",method:"post",data:t})}function V(t){return(0,n.default)({url:"/PRO/ProductionTask/CreatePartTransferByTransferCode",method:"post",data:r.default.stringify(t)})}function X(t){return(0,n.default)({url:"/PRO/ProductionTask/CreateCompTransferByTransferCode",method:"post",data:t})}function $(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessTransferDetail",method:"post",data:r.default.stringify(t)})}function tt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetProcessPartTransferDetail",method:"post",data:r.default.stringify(t)})}function et(t){return(0,n.default)({url:"/PRO/ProductionTask/ProAddQuest",method:"post",data:r.default.stringify(t)})}function ot(t){return(0,n.default)({url:"/PRO/ProductionTask/DownNestingTaskTemplate",method:"post",data:t})}function at(){return(0,n.default)({url:"/PRO/ProductionTask/GetTenantFactoryType",method:"post"})}function nt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamCompHistory",method:"post",data:t})}function rt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamStockPageList",method:"post",data:t})}function ut(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPageProcessTransferDetailBase",method:"post",data:t})}function it(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPageSchdulingComps",method:"post",data:t})}function dt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetCanSchdulingComps",method:"post",data:t})}function st(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingInfoDetail",method:"post",data:t})}function lt(t){return(0,n.default)({url:"/PRO/nesting/GetCanSchdulingPartList",method:"post",data:t})}function ct(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingPageList",method:"post",data:t})}function Pt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveSchdulingDraft",method:"post",data:t})}function ft(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingPageList",method:"post",data:t})}function mt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingPageList",method:"post",data:t})}function pt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingPageList",method:"post",data:t})}function ht(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingWorkingTeams",method:"post",data:t})}function gt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/ImportCompSchduling",method:"post",timeout:12e5,data:t})}function Tt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTask",method:"post",data:t})}function kt(t){return(0,n.default)({url:"/PRO/ProductionTask/CancelSchduling",method:"post",data:t})}function Rt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlan",method:"post",data:t})}function Gt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlanById",method:"post",data:t})}function St(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTaskById",method:"post",data:t,timeout:12e5})}function Ot(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskPageList",method:"post",data:t})}function Ct(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskDetails",method:"post",data:t})}function Bt(t){return(0,n.default)({url:"/PRO/ProductionTask/ExportTaskCodeDetails",method:"post",data:t})}function Lt(t,e){return(0,n.default)({url:"/PRO/ProductionTask/TeamTaskTransfer",method:"post",data:t,params:e})}function yt(t){return(0,n.default)({url:"/PRO/ProductionTask/TeamProcessingByTaskCode",method:"post",data:t})}function At(t){return(0,n.default)({url:"/PRO/ProductionTask/TeamTaskProcessing",method:"post",data:t})}function Wt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingCancelHistory",method:"post",data:t})}function Dt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamTaskAllocationPageList",method:"post",data:t})}function vt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamProcessAllocation",method:"post",data:t})}function Nt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/AdjustCompTeamProcessAllocation",method:"post",data:t})}function It(t){return(0,n.default)({url:"/PRO/ProductionSchduling/AdjustSubAssemblyTeamProcessAllocation",method:"post",data:t})}function Ut(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferHistory",method:"post",data:t})}function wt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferDetail",method:"post",data:t})}function Ft(t){return(0,n.default)({url:"/PRO/ProductionTask/ExportTransferCodeDetail",method:"post",data:t})}function xt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPartSchdulingCancelHistory",method:"post",data:t})}function Et(t){return(0,n.default)({url:"/PRO/ProductionTask/BatchReceiveTransferTask",method:"post",data:r.default.stringify(t)})}function Ht(t){return(0,n.default)({url:"/PRO/ProductionTask/ReceiveTransferTask",method:"post",data:t})}function bt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/AdjustPartTeamProcessAllocation",method:"post",data:t})}function Mt(t){return(0,n.default)({url:"/PRO/ProductionTask/CancelTransferTask",method:"post",data:t})}function jt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTransferCancelDetails",method:"post",data:t})}function Zt(t){return(0,n.default)({url:"/PRO/ProductionTask/CheckSchduling",method:"post",data:r.default.stringify(t)})}function Qt(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSchdulingPartUsePageList",method:"post",data:t})}function _t(t){return(0,n.default)({url:"/PRO/ProductionTask/GetTeamPartUseList",method:"post",data:t})}function Jt(t){return(0,n.default)({url:"/PRO/ProductionTask/ApplyCheck",method:"post",data:t})}function Yt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveCompSchdulingDraft",method:"post",data:t,timeout:12e5})}function qt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraft",method:"post",data:t,timeout:12e5})}function zt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraftNew",method:"post",data:t,timeout:12e5})}function Kt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveUnitSchdulingDraftNew",method:"post",data:t,timeout:12e5})}function Vt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingInfoDetail",method:"post",data:t,timeout:12e5})}function Xt(t){return(0,n.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingInfoDetail",method:"post",data:t,timeout:12e5})}function $t(t){return(0,n.default)({url:"/PRO/ProductionTask/GetPartPrepareList",method:"post",data:t})}function te(t){return(0,n.default)({url:"/pro/productiontask/GetTaskPartPrepareList",method:"post",data:t})}function ee(t){return(0,n.default)({url:"/PRO/ProductionSchduling/WithdrawScheduling",method:"post",data:t})}function oe(t){return(0,n.default)({url:"/PRO/ProductionSchduling/CancelUnitSchduling",method:"post",data:t})}function ae(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveComponentSchedulingWorkshop",method:"post",data:t})}function ne(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshop",method:"post",data:t})}function re(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshopNew",method:"post",data:t})}function ue(t){return(0,n.default)({url:"/PRO/ProductionSchduling/SaveUnitSchedulingWorkshopNew",method:"post",data:t})}function ie(t){return(0,n.default)({url:"/PRO/ZeroComponentRecoil/GetWorkingTeamsPageList",method:"post",data:t})}function de(t){return(0,n.default)({url:"/PRO/ZeroComponentRecoil/SaveChangeZeroComponentRecoil",method:"post",data:t})}function se(t){return(0,n.default)({url:"/PRO/ZeroComponentRecoil/GetBuildReturnRecordList",method:"post",data:t})}function le(t){return(0,n.default)({url:"/PRO/ProductionTask/GetWorkingTeamLoadRealTime",method:"post",data:t})}function ce(t){return(0,n.default)({url:"/PRO/ProductionTask/GetCompTaskPageList",method:"post",data:t})}function Pe(t){return(0,n.default)({url:"/PRO/ProductionTask/SimplifiedProcessing",method:"post",data:t})}function fe(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingHistory",method:"post",data:t})}function me(t){return(0,n.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingSummary",method:"post",data:t})}function pe(t){return(0,n.default)({url:"/PRO/ProductionTask/BatchWithdrawSimplifiedProcessingHistory",method:"post",data:t})}function he(t){return(0,n.default)({url:"/PRO/ProductionTask/GetDwg",method:"post",data:t})}function ge(t){return(0,n.default)({url:"/PRO/nesting/GetNestingResultPageList",method:"post",data:t})}function Te(t){return(0,n.default)({url:"/PRO/nesting/GetNestingSurplusList",method:"post",data:t})}function ke(t){return(0,n.default)({url:"/PRO/nesting/DeleteNestingResult",method:"post",data:t})}function Re(t){return(0,n.default)({url:"/PRO/nesting/GetNestingFiles",method:"post",data:t})}function Ge(t){return(0,n.default)({url:"/PRO/nesting/Import",method:"post",data:t})}function Se(t){return(0,n.default)({url:"/PRO/nesting/GetNestingPartList",method:"post",data:t})}function Oe(t){return(0,n.default)({url:"/PRO/productiontask/GetSemiFinishedStock",method:"post",data:t})}function Ce(t){return(0,n.default)({url:"/PRO/productiontask/GetSourceFinishedList",method:"post",data:t})}function Be(t){return(0,n.default)({url:"/PRO/productiontask/GetTargetReceiveList",method:"post",data:t})}function Le(t){return(0,n.default)({url:"/PRO/productiontask/GetToReceiveTaskList",method:"post",data:t})}function ye(t){return(0,n.default)({url:"/PRO/productiontask/GetToReceiveTaskDetailList",method:"post",data:t})}function Ae(t){return(0,n.default)({url:"/PRO/productiontask/ReceiveTaskFromStock",method:"post",data:t})}function We(t){return(0,n.default)({url:"/PRO/productiontask/BatchReceiveTaskFromStock",method:"post",data:t})}function De(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetYearlyFullCheckProducedData",method:"post",data:t})}function ve(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetCheckUserRankList",method:"post",data:t})}function Ne(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetWorkingTeamCheckingList",method:"post",data:t})}function Ie(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetCheckingItemList",method:"post",data:t})}function Ue(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetCheckingQuestionList",method:"post",data:t})}function we(t){return(0,n.default)({url:"/PRO/InspectionAnalysis/GetMonthlyFullCheckProducedData",method:"post",data:t})}function Fe(t){return(0,n.default)({url:"/PRO/productiontask/ExportSimplifiedProcessingHistory",method:"post",data:t})}function xe(t){return(0,n.default)({url:"/PRO/productiontask/GetCompTaskPartCompletionStock",method:"post",data:t})}function Ee(t){return(0,n.default)({url:"/Pro/NestingBill/GetPageList",method:"post",data:t})}function He(t){return(0,n.default)({url:"/Pro/NestingBill/GetDetailList",method:"post",data:t})}function be(t){return(0,n.default)({url:"/Pro/NestingBill/GetDetailSummaryList",method:"post",data:t})}function Me(t){return(0,n.default)({url:"/Pro/NestingBill/UpdateMachineName",method:"post",data:t})}function je(t){return(0,n.default)({url:"/PRO/CustomUssl/SigmaWOLExport",method:"post",data:t})}function Ze(t){return(0,n.default)({url:"/PRO/CustomUssl/LentakExport",method:"post",data:t})}function Qe(t){return(0,n.default)({url:"/PRO/CustomUssl/ProfilesExport",method:"post",data:t})}function _e(t){return(0,n.default)({url:"/PRO/CustomUssl/BochuAddTask",method:"post",data:t})}function Je(t){return(0,n.default)({url:"/Pro/Nesting/ImportThumbnail",method:"post",data:t})}function Ye(t){return(0,n.default)({url:"/Pro/MaterielPicking/WithdrawPicking",method:"post",data:t})}function qe(t){return(0,n.default)({url:"/PRO/productiontask/GetPartWithParentPageList",method:"post",data:t})}function ze(t){return(0,n.default)({url:"/PRO/productiontask/GetNestingMaterialWithPart",method:"post",data:t})}function Ke(t){return(0,n.default)({url:"/PRO/MOC/GetStopList",method:"post",data:t})}}}]);