(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-754c25c8"],{"091b":function(t,e,a){},"0f22":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("3ca3"),a("ddb0");var i=n(a("0f97")),r=n(a("b775")),o=a("6186");e.default={name:"ScrapDetails",components:{DynamicDataTable:i.default},props:{row:{type:Object,default:function(){return{}}}},data:function(){return{gridCode:"pro_component_scrap_detail_list",tbConfig:{},columns:[],data:[]}},created:function(){var t=this;(0,o.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData()}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:160,Height:520})},setGridData:function(t){this.data=t.Data},setCols:function(t){this.columns=t},getTableData:function(){var t=this;return this.dynTblOptBak&&this.resetDynTblOpts(),this.tbConfig.Data_Url?(0,r.default)({url:this.tbConfig.Data_Url,method:"post",params:{billId:this.row.Id}}).then((function(e){e.IsSucceed&&t.setGridData(e)})):Promise.reject("invalid data api...")},handleExport:function(){var t=this,e=this.columns.filter((function(t){return t.Is_Display})).map((function(t){return t.Display_Name})),n=this.columns.filter((function(t){return t.Is_Display})).map((function(t){return t.Code})),i=this.formatJson(n,this.data);a.e("chunk-2d0cc0b6").then(a.t.bind(null,"4bf8",7)).then((function(a){a.export_json_to_excel({header:e,data:i,filename:"".concat(t.row.Id),autoWidth:!0,bookType:"xlsx"})}))},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}}},"28ba":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");e.default={name:"AddTransferDialog",props:{projects:{type:Array,default:function(){return[]}}},data:function(){return{project_id:""}},methods:{submit:function(){var t=this;if(!this.project_id)return this.$message.warning("必须选择所属项目");this.$emit("dialogFormSubmitSuccess",{type:"newtab",data:this.projects.find((function(e){return e.Id===t.project_id}))})}}}},"2dd9":function(t,e,a){"use strict";function n(t,e){t||(t={}),e||(e=[]);var a=[],n=function(n){var i;i="[object Array]"===Object.prototype.toString.call(t[n])?t[n]:[t[n]];var r=i.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(r.filter((function(t){return null!==t})).length<=0&&(r=null),r){var o={Key:n,Value:r,Type:"",Filter_Type:""},c=e.find((function(t){return t.Code===n}));o.Type=null===c||void 0===c?void 0:c.Type,o.Filter_Type=null===c||void 0===c?void 0:c.Filter_Type,a.push(o)}};for(var i in t)n(i);return a}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=n,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("25f0")},"2f31":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("d81d"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7"),a("25f0"),a("159b");var i=n(a("0f97")),r=a("6186"),o=n(a("b775")),c=a("2dd9");e.default={name:"AddDetail",components:{DynamicDataTable:i.default},props:{project:{type:Object,default:function(){return{}}},used:{type:Array,default:function(){return[]}}},data:function(){return{gridCode:"pro_waiting_scrap_list",tbConfig:{},columns:[],data:[],checkedRows:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},ParameterJson:[]}},created:function(){var t,e=this;this.fiterArrObj["Project_Name"]=null===(t=this.project)||void 0===t?void 0:t.Name,(0,r.GetGridByCode)({Code:this.gridCode}).then((function(t){var a;t.IsSucceed&&(e.setGrid(t.Data.Grid),e.setCols(t.Data.ColumnList),e.$refs.table.searchedField["Project_Name"]=null===(a=e.project)||void 0===a?void 0:a.Name)}))},methods:{cancel:function(){this.$emit("dialogCancel")},multiSelectedChange:function(t){this.checkedRows=t},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120,Height:480}),this.filterData.PageSize=Number(t.Row_Number)},setCols:function(t){var e=this;t.forEach((function(t){"Project_Name"===t.Code&&(t.Range=JSON.stringify([{label:e.project.Name,value:e.project.Name}]))})),this.columns=t.concat([])},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},gridSizeChange:function(t){var e=t.size;this.filterData.PageSize=e,this.filterData.Page=1,this.getTableData()},gridPageChange:function(t){t.page;this.filterData.Page=1,this.getTableData()},tableSearch:function(t){this.fiterArrObj=t,this.filterData.Page=1,this.getTableData()},getTableData:function(){var t=this;(0,o.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{Component_Ids:this.used.map((function(t){return t.Component_Id})).toString(),ParameterJson:(0,c.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(e){e.IsSucceed&&(t.setGridData(e.Data),t.filterData.TotalCount=e.Data.TotalCount)}))},addItem:function(){if(this.checkedRows.length<=0)return this.$message.warning("尚未选择报废明细");this.$emit("dialogFormSubmitSuccess",{type:"merge",data:this.checkedRows})}}}},"3cd72":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("14d9"),a("b0c0"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("ddb0");var i=n(a("0f97")),r=a("6186"),o=n(a("b775")),c=a("2dd9"),s=n(a("2082")),u=a("1b69"),l=n(a("ef75")),d=n(a("ef57")),f=n(a("41c1"));e.default={name:"PROInventoryScrap",components:{DynamicDataTable:i.default,AddDetail:l.default,AddTransferDialog:d.default,ScrapDetails:f.default},mixins:[s.default],data:function(){return{addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return a.e("chunk-e26717b2").then(a.bind(null,"3179"))},name:"ScrapAdd",meta:{title:"新增成品报废单"}}],gridCode:"pro_component_scrap_bill_list",loading:!1,apis:{},projects:[],tbConfig:{},columns:[],data:[],ParameterJson:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},beforeRouteEnter:function(t,e,a){a((function(t){t.tbConfig.Data_Url&&t.getTableData()}))},created:function(){var t=this;Promise.all([(0,u.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))]).then((function(){return(0,r.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))}))})).then((function(){t.getTableData()}))},methods:{openPageTab:function(t,e,a){this.$router.push({name:t,query:{pg_redirect:this.$route.name,id:e},params:a})},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:160}),this.filterData.PageSize=Number(this.tbConfig.Row_Number)},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},setCols:function(t){this.columns=t},getTableData:function(){var t=this;return this.dynTblOptBak&&this.resetDynTblOpts(),this.tbConfig.Data_Url?(0,o.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{ParameterJson:(0,c.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(e){e.IsSucceed&&t.setGridData(e.Data)})):Promise.reject("invalid data api...")},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.type,a=t.data;this.dialogCancel(),"newtab"===e&&this.openPageTab("ScrapAdd",null,{type:0,project:a})},confirmRow:function(t){var e=this;this.$createElement;this.$confirm("清确认是否讲所选报废单恢复正常。","提示",{confirmButtonText:"恢复正常",cancelButtonText:"取消",type:"warning",center:!0}).then((function(){e.$message({type:"success",message:"删除成功!"})})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},gridSizeChange:function(t){var e=t.size;this.filterData.PageSize=e,this.filterData.Page=1,this.getTableData()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.getTableData()},tableSearch:function(t){this.fiterArrObj=t,this.filterData.Page=1,this.getTableData()}}}},"41c1":function(t,e,a){"use strict";a.r(e);var n=a("7a92f"),i=a("9f58");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,null,null);e["default"]=c.exports},"46c5":function(t,e,a){"use strict";a.r(e);var n=a("5aba"),i=a("bf3a");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("5ee0");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"5ef75b9f",null);e["default"]=c.exports},"4e82":function(t,e,a){"use strict";var n=a("23e7"),i=a("e330"),r=a("59ed"),o=a("7b0b"),c=a("07fa"),s=a("083a"),u=a("577e"),l=a("d039"),d=a("addb"),f=a("a640"),g=a("3f7e"),h=a("99f4"),p=a("1212"),b=a("ea83"),m=[],v=i(m.sort),D=i(m.push),_=l((function(){m.sort(void 0)})),C=l((function(){m.sort(null)})),y=f("sort"),S=!l((function(){if(p)return p<70;if(!(g&&g>3)){if(h)return!0;if(b)return b<603;var t,e,a,n,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)m.push({k:e+n,v:a})}for(m.sort((function(t,e){return e.v-t.v})),n=0;n<m.length;n++)e=m[n].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),j=_||!C||!y||!S,O=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:u(e)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:j},{sort:function(t){void 0!==t&&r(t);var e=o(this);if(S)return void 0===t?v(e):v(e,t);var a,n,i=[],u=c(e);for(n=0;n<u;n++)n in e&&D(i,e[n]);d(i,O(t)),a=c(i),n=0;while(n<a)e[n]=i[n++];while(n<u)s(e,n++);return e}})},5416:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",[a("el-form-item",{attrs:{label:"选择项目",required:""}},[a("el-select",{attrs:{filterable:""},model:{value:t.project_id,callback:function(e){t.project_id=e},expression:"project_id"}},t._l(t.projects,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("el-form-item",{staticStyle:{"text-align":"center"}},[a("el-button",{on:{click:function(e){return t.$emit("dialogCancel")}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("确定")])],1)],1)],1)},i=[]},"5aba":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 flex-pd16-wrap"},[a("div",{staticClass:"page-main-content cs-z-shadow"},[a("el-container",{staticStyle:{height:"100%"}},[a("el-header",{staticStyle:{height:"auto","margin-bottom":"4px",display:"flex","flex-direction":"row","flex-wrap":"wrap"}},[a("div",{staticClass:"btns"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.openDialog({title:"新增报废单",width:"360px",component:"AddTransferDialog",props:{projects:t.projects}})}}},[t._v("新增")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0"}},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:"",total:t.filterData.TotalCount,page:t.filterData.Page},on:{gridSizeChange:t.gridSizeChange,gridPageChange:t.gridPageChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"Scrap_Date",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Scrap_Date))+" ")]}},{key:"op",fn:function(e){var n=e.row;e.column,e.$index;return[t._e(),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openDialog({title:"查看详情",component:"ScrapDetails",width:"75%",props:{row:n}})}}},[t._v("查看详情")])]}}])})],1)],1)],1),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},i=[]},"5ee0":function(t,e,a){"use strict";a("f6a2")},"6ac4":function(t,e,a){"use strict";a.r(e);var n=a("2f31"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},7992:function(t,e,a){"use strict";a("ddfd")},"7a92f":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-top":"-16px"}},[a("div",{staticStyle:{display:"flex","flex-direction":"row","align-items":"center","margin-bottom":"16px"}},[a("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.handleExport}},[t._v("导出")])],1),a("div",[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""},scopedSlots:t._u([{key:"Out_Date",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Out_Date))+" ")]}}])})],1)])},i=[]},"9e13":function(t,e,a){"use strict";a.r(e);var n=a("28ba"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"9f58":function(t,e,a){"use strict";a.r(e);var n=a("0f22"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},bf3a:function(t,e,a){"use strict";a.r(e);var n=a("3cd72"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},ddfd:function(t,e,a){},e970:function(t,e,a){"use strict";a("091b")},ef57:function(t,e,a){"use strict";a.r(e);var n=a("5416"),i=a("9e13");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,null,null);e["default"]=c.exports},ef75:function(t,e,a){"use strict";a.r(e);var n=a("f37c"),i=a("6ac4");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("7992"),a("e970");var o=a("2877"),c=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"a5b468ee",null);e["default"]=c.exports},f37c:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stockin-details"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{multiSelectedChange:t.multiSelectedChange,gridSizeChange:t.gridSizeChange,gridPageChange:t.gridPageChange,tableSearch:t.tableSearch}}),a("div",{staticStyle:{"text-align":"center","margin-top":"24px"}},[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.addItem}},[t._v("添加")])],1)],1)},i=[]},f6a2:function(t,e,a){}}]);