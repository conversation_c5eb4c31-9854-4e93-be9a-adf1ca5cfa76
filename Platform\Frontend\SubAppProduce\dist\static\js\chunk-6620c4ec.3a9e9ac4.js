(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6620c4ec"],{"00e3":function(t,e,a){"use strict";a.r(e);var r=a("6dda"),s=a("ff8d");for(var o in s)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(o);a("41a6");var n=a("2877"),i=Object(n["a"])(s["default"],r["a"],r["b"],!1,null,"5d02e1d0",null);e["default"]=i.exports},3166:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=h,e.DeleteProject=u,e.GeAreaTrees=k,e.GetFileSync=W,e.GetInstallUnitIdNameList=_,e.GetNoBindProjectList=m,e.GetPartDeepenFileList=L,e.GetProjectAreaTreeList=D,e.GetProjectEntity=c,e.GetProjectList=i,e.GetProjectPageList=n,e.GetProjectTemplate=p,e.GetPushProjectPageList=C,e.GetSchedulingPartList=T,e.IsEnableProjectMonomer=d,e.SaveProject=l,e.UpdateProjectTemplateBase=g,e.UpdateProjectTemplateContacts=P,e.UpdateProjectTemplateContract=y,e.UpdateProjectTemplateOther=v;var s=r(a("b775")),o=r(a("4328"));function n(t){return(0,s.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function i(t){return(0,s.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function c(t){return(0,s.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(t)})}function l(t){return(0,s.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function u(t){return(0,s.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(t)})}function d(t){return(0,s.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,s.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function h(t){return(0,s.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function m(t){return(0,s.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function p(t){return(0,s.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function g(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function P(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function y(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function v(t){return(0,s.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function C(t){return(0,s.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function D(t){return(0,s.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function _(t){return(0,s.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function k(t){return(0,s.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function L(t){return(0,s.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function T(t){return(0,s.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function W(t){return(0,s.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"38f4":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/总产量********************"},"41a6":function(t,e,a){"use strict";a("f2c0")},"6dda":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return s}));var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container abs100"},[a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"top"},[a("div",{staticClass:"container"},[a("div",{staticClass:"form"},[a("div",{staticClass:"form-left"}),a("div",{staticClass:"form-right"},[a("el-date-picker",{attrs:{type:"year",placeholder:"选择年",size:"small","value-format":"yyyy",clearable:!1},on:{change:t.changeYear},model:{value:t.form.year,callback:function(e){t.$set(t.form,"year",e)},expression:"form.year"}})],1)]),a("div",{staticClass:"content"},[a("div",{staticClass:"content-left"},[a("div",{staticClass:"card default"},[t._m(0),a("div",{staticClass:"text"},[a("div",[t._v("年度累计总产量")]),a("div",[t._v(t._s(t.data.YearToDateTotalOutput||"0")+" t")])])]),a("div",{staticClass:"card pass"},[t._m(1),a("div",{staticClass:"text"},[a("div",[t._v("年度指标完成率")]),a("div",[t._v(t._s(t.data.AnnualTargetCompletionRate||"0")+"%")])])])]),a("div",{staticClass:"content-right"},[a("div",{staticClass:"card2-container"},[t._l(t.MonthlyFactoyOutputList,(function(e,r){return[r<6?a("div",{key:r,staticClass:"crad2",attrs:{id:t.form.year==t.currentYear&&e.Name==t.currentMonth?"selected":""}},[a("div",{staticClass:"text"},[a("div",[t._v(t._s(e.Name)+"月")]),a("div",[t._v(t._s(e.Value||"0")+"t")])])]):t._e()]}))],2),a("div",{staticClass:"card2-container"},[t._l(t.MonthlyFactoyOutputList,(function(e,r){return[r>=6?a("div",{key:r,staticClass:"crad2",attrs:{id:t.form.year==t.currentYear&&e.Name==t.currentMonth?"selected":""}},[a("div",{staticClass:"text"},[a("div",[t._v(t._s(e.Name)+"月")]),a("div",[t._v(t._s(e.Value||"0")+"t")])])]):t._e()]}))],2)])])])]),t.FactoryDetailData.Is_Workshop_Enabled?a("div",{staticClass:"mid one"},[a("div",{staticClass:"container"},[a("div",{staticClass:"form"},[t._m(2),a("div",{staticClass:"form-right"},[a("span",{staticClass:"demonstration"},[t._v("统计日期")]),a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small","value-format":"yyyy-MM-dd"},on:{change:t.changeWorkshopDate},model:{value:t.form.workshopDate,callback:function(e){t.$set(t.form,"workshopDate",e)},expression:"form.workshopDate"}})],1)]),a("div",{staticClass:"content"},[t.WorkshopDataList.length>8?a("div",{class:t.CurrentWorkshopPage<=1?"btn disable":"btn",on:{click:function(e){return t.lastPage(t.CurrentWorkshopPage,"Workshop")}}},[a("div",{staticClass:"triangle"})]):t._e(),a("div",{staticClass:"card3-container"},[t.CurrentWorkshopDataList.length>0?t._l(t.CurrentWorkshopDataList,(function(e,r){return a("div",{key:r,staticClass:"crad3"},[a("div",{staticClass:"text"},[a("div",[t._v(t._s(e.Name||"-"))]),a("div",[a("span",[t._v("数量(件)")]),a("span",{staticClass:"default"},[t._v(t._s(e.Amount||"0"))])]),a("div",[a("span",[t._v("重量(t)")]),a("span",{staticClass:"pass"},[t._v(t._s(e.Value||"0"))])])])])})):[a("div",{staticClass:"empty-container"})]],2),t.WorkshopDataList.length>8?a("div",{class:t.CurrentWorkshopPage>=t.MaxWorkshopPageSize?"btn disable":"btn",on:{click:function(e){return t.nextPage(t.CurrentWorkshopPage,t.MaxWorkshopPageSize,"Workshop")}}},[a("div",{staticClass:"triangle right"})]):t._e()])])]):t._e(),t._m(3),a("div",{staticClass:"mid two"},[a("div",{staticClass:"container"},[a("div",{staticClass:"form"},[t._m(4),a("div",{staticClass:"form-right"},[a("span",{staticClass:"demonstration"},[t._v("项目")]),a("el-select",{attrs:{placeholder:"请选择",size:"small",clearable:""},on:{change:t.changeProject},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}},t._l(t.ProjectList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Sys_Project_Id}})})),1),a("span",{staticClass:"demonstration"},[t._v("工序")]),a("el-select",{attrs:{placeholder:"请选择",size:"small",clearable:""},on:{change:t.changeProcess},model:{value:t.form.WorkingProcessId,callback:function(e){t.$set(t.form,"WorkingProcessId",e)},expression:"form.WorkingProcessId"}},t._l(t.ProcessList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1),a("span",{staticClass:"demonstration"},[t._v("统计日期")]),a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small","value-format":"yyyy-MM-dd"},on:{change:t.changeProcessDate},model:{value:t.form.processDate,callback:function(e){t.$set(t.form,"processDate",e)},expression:"form.processDate"}})],1)]),a("div",{staticClass:"content"},[t.ProcessDataList.length>8?a("div",{class:t.CurrentProcessPage<=1?"btn disable":"btn",on:{click:function(e){return t.lastPage(t.CurrentProcessPage,"Process")}}},[a("div",{staticClass:"triangle"})]):t._e(),a("div",{staticClass:"card3-container"},[t.CurrentProcessDataList.length>0?t._l(t.CurrentProcessDataList,(function(e,r){return a("div",{key:r,class:t.form.WorkingTeamName===e.Name?"crad3 selected":"crad3"},[a("div",{staticClass:"text"},[a("div",[t._v(t._s(e.Name||"-"))]),a("div",[a("span",[t._v("转入")]),a("span",{staticClass:"default"},[t._v(t._s(e.Receive_Value||"0")+" t")])]),a("div",[a("span",[t._v("转出")]),a("span",{staticClass:"default"},[t._v(t._s(e.Finish_Value||"0")+" t")])]),a("div",[a("span",[t._v("实时在制品")]),a("span",{staticClass:"pass"},[t._v(t._s(e.Producing_Value||"0")+" t")])]),a("div",[a("span",[t._v("当日在制品")]),a("span",{staticClass:"pass"},[t._v(t._s(e.Last_Date_Producing_Value||"0")+" t")])]),a("div",[a("span",[t._v("加工量")]),a("span",{staticClass:"pass"},[t._v(t._s(e.Process_Count||"0")+" t")])])])])})):[a("div",{staticClass:"empty-container"})]],2),t.ProcessDataList.length>8?a("div",{class:t.CurrentProcessPage>=t.MaxProcessPageSize?"btn disable":"btn",on:{click:function(e){return t.nextPage(t.CurrentProcessPage,t.MaxProcessPageSize,"Process")}}},[a("div",{staticClass:"triangle right"})]):t._e()])])]),a("div",{staticClass:"bottom"},[a("div",{staticClass:"container"},[a("div",{staticClass:"form"},[a("div",{staticClass:"form-left"},[a("span",[t._v("班组报工产量")]),a("span",{on:{click:t.handelView}},[t._v("查看详情"),a("i",{staticClass:"el-icon-arrow-right",staticStyle:{color:"#298DFF"}})])]),a("div",{staticClass:"form-right"},[a("span",{staticClass:"demonstration"},[t._v("班组")]),a("el-select",{attrs:{placeholder:"请选择",size:"small",clearable:""},on:{change:t.changeWorkTeam},model:{value:t.form.WorkingTeamId,callback:function(e){t.$set(t.form,"WorkingTeamId",e)},expression:"form.WorkingTeamId"}},t._l(t.WorkingTeamList,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1),a("span",{staticClass:"demonstration"},[t._v("统计日期")]),a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",size:"small","value-format":"yyyy-MM-dd"},on:{change:t.changeWorkTeamDate},model:{value:t.form.teamDate,callback:function(e){t.$set(t.form,"teamDate",e)},expression:"form.teamDate"}})],1)]),a("div",{staticClass:"content"},[a("div",{staticClass:"table-container"},[a("vxe-grid",{staticClass:"cs-vxe-table",attrs:{resizable:"",height:"100%","empty-text":"暂无数据",stripe:"",loading:t.tableLoading,columns:t.tableColumn,"row-config":{height:40},data:t.tableData,"show-overflow":"tooltip",align:"center"},scopedSlots:t._u([{key:"ScheduleCount",fn:function(e){var r=e.row;return[a("div",[t._v(t._s(r.ScheduleCount||"0"))])]}},{key:"ScheduleAmount",fn:function(e){var r=e.row;return[a("div",[t._v(t._s(r.ScheduleAmount||"0"))])]}},{key:"CompletedCount",fn:function(e){var r=e.row;return[a("div",[t._v(t._s(r.CompletedCount||"0"))])]}},{key:"CompletedAmount",fn:function(e){var r=e.row;return[a("div",[t._v(t._s(r.CompletedAmount||"0"))])]}},{key:"CompletionRate",fn:function(t){var e=t.row;return[a("el-progress",{attrs:{"text-inside":!0,"stroke-width":14,percentage:Number((100*e.CompletionRate).toFixed(1))}})]}}])})],1)])])])])])},s=[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"img"},[r("img",{attrs:{src:a("38f4"),alt:""}})])},function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"img"},[r("img",{attrs:{src:a("7f6f"),alt:""}})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"form-left"},[a("span",[t._v("车间产量统计")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"line"},[a("div")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"form-left"},[a("span",[t._v("工序生产情况")])])}]},7196:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteWorkshop=l,e.GetFactoryPeoplelist=o,e.GetWorkshopEntity=c,e.GetWorkshopPageList=i,e.SaveEntity=n;var s=r(a("b775"));function o(t){return(0,s.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function n(t){return(0,s.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:t})}function i(t){return(0,s.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:t})}function c(t){return(0,s.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:t})}function l(t){return(0,s.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:t})}},"76fb":function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("d81d"),a("14d9"),a("fb6a"),a("e9f5"),a("f665"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7");var s=r(a("c14f")),o=r(a("1da1")),n=a("b4f1"),i=a("ed08"),c=a("586a"),l=a("a024"),u=a("3166"),d=r(a("d7a3"));e.default={mixins:[d.default],data:function(){return{loading:!1,btnLoading:!1,tableLoading:!1,form:{year:String((new Date).getFullYear()),workshopDate:[(0,i.parseTime)(new Date,"{y}-{m}")+"-01",(0,i.parseTime)(new Date,"{y}-{m}-{d}")],Sys_Project_Id:"",WorkingProcessId:"",processDate:[(0,i.parseTime)(new Date,"{y}-{m}")+"-01",(0,i.parseTime)(new Date,"{y}-{m}-{d}")],WorkingTeamId:"",WorkingTeamName:"",teamDate:[(0,i.parseTime)(new Date,"{y}-{m}")+"-01",(0,i.parseTime)(new Date,"{y}-{m}-{d}")]},data:{YearToDateTotalOutput:"",AnnualTargetCompletionRate:""},currentYear:String((new Date).getFullYear()),currentMonth:String((new Date).getMonth()+1),currentDate:String((new Date).getDate()),MonthlyFactoyOutputList:[],WorkshopDataList:[],CurrentWorkshopDataList:[],CurrentWorkshopPage:1,MaxWorkshopPageSize:0,ProjectList:[],ProcessList:[],ProcessDataList:[],CurrentProcessDataList:[],CurrentProcessPage:1,MaxProcessPageSize:0,WorkingTeamList:[],tableColumn:[{field:"WorkingTeam",title:"班组名称",minWidth:120},{field:"WorkingTeamLeader",title:"班组长",minWidth:120},{field:"ScheduleCount",title:"排产数量(件)",minWidth:120,slots:{default:"ScheduleCount"}},{field:"ScheduleAmount",title:"排产量(t)",minWidth:120,slots:{default:"ScheduleAmount"}},{field:"CompletedCount",title:"完成数量(件)",minWidth:120,slots:{default:"CompletedCount"}},{field:"CompletedAmount",title:"完成量(t)",minWidth:120,slots:{default:"CompletedAmount"}},{field:"CompletionRate",title:"完成率",minWidth:300,slots:{default:"CompletionRate"}}],tableData:[]}},created:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getCurFactory();case 1:t.FactoryDetailData.Manage_Cycle_Enabled&&(t.form.workshopDate=t.getManageCycle(),t.form.processDate=t.getManageCycle(),t.form.teamDate=t.getManageCycle()),t.getBaseData(),t.fetchData();case 2:return e.a(2)}}),e)})))()},mounted:function(){},methods:{getBaseData:function(){var t=this;(0,u.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectList=e.Data.Data:t.$message.error(e.Message)})),(0,l.GetProcessListBase)().then((function(e){e.IsSucceed?t.ProcessList=e.Data:t.$message.error(e.Message)})),(0,l.GetWorkingTeams)().then((function(e){e.IsSucceed?t.WorkingTeamList=e.Data:t.$message.error(e.Message)}))},fetchData:function(){this.getYearlyFactoryOutput(),this.getMonthlyFactoyOutput(),this.getFactoryOutput(),this.getProcessOutput(),this.getWorkTeamOutput()},getYearlyFactoryOutput:function(){var t=this;(0,c.YearlyFactoryOutput)({year:this.form.year}).then((function(e){e.IsSucceed?(t.data.YearToDateTotalOutput=e.Data.YearToDateTotalOutput?(0,n.comdify)(String(e.Data.YearToDateTotalOutput)):"",t.data.AnnualTargetCompletionRate=e.Data.AnnualTargetCompletionRate):t.$message.error(e.Message)})).finally((function(t){}))},getMonthlyFactoyOutput:function(){var t=this;(0,c.MonthlyFactoyOutput)({year:this.form.year}).then((function(e){e.IsSucceed?t.MonthlyFactoyOutputList=e.Data.map((function(t){return t.Value=t.Value?(0,n.comdify)(String(t.Value)):"",t})):t.$message.error(e.Message)})).finally((function(t){}))},changeYear:function(){this.getYearlyFactoryOutput(),this.getMonthlyFactoyOutput()},getFactoryOutput:function(){var t=this;(0,c.FactoryOutput)({TimeStart:this.form.workshopDate?this.form.workshopDate[0]:"",TimeEnd:this.form.workshopDate?this.form.workshopDate[1]:""}).then((function(e){e.IsSucceed?(t.WorkshopDataList=e.Data.WorkshopList.map((function(t){return t.Value=t.Value?(0,n.comdify)(String(t.Value)):"",t})),t.CurrentWorkshopPage=1,t.WorkshopDataList.length>8?(t.CurrentWorkshopDataList=t.WorkshopDataList.slice(0,8),t.MaxWorkshopPageSize=Math.ceil(t.WorkshopDataList.length/8)):t.CurrentWorkshopDataList=JSON.parse(JSON.stringify(t.WorkshopDataList))):t.$message.error(e.Message)})).finally((function(t){}))},changeWorkshopDate:function(){this.getFactoryOutput()},lastPage:function(t,e){t<=1||("Workshop"===e?(t--,this.CurrentWorkshopDataList=this.WorkshopDataList.slice(8*t-8,8*t),this.CurrentWorkshopPage--):"Process"===e&&(t--,this.CurrentProcessDataList=this.ProcessDataList.slice(8*t-8,8*t),this.CurrentProcessPage--))},nextPage:function(t,e,a){t>=e||("Workshop"===a?(t++,this.CurrentWorkshopDataList=this.WorkshopDataList.slice(8*t-8,8*t),this.CurrentWorkshopPage++):"Process"===a&&(t++,this.CurrentProcessDataList=this.ProcessDataList.slice(8*t-8,8*t),this.CurrentProcessPage++))},getProcessOutput:function(){var t=this;(0,c.ProcessOutput)({TimeStart:this.form.processDate?this.form.processDate[0]:"",TimeEnd:this.form.processDate?this.form.processDate[1]:"",WorkingProcessId:"",Sys_Project_Id:this.form.Sys_Project_Id}).then((function(e){e.IsSucceed?(t.ProcessDataList=e.Data.map((function(t){return t.Receive_Value=t.Receive_Value?(0,n.comdify)(String(t.Receive_Value)):"",t.Finish_Value=t.Finish_Value?(0,n.comdify)(String(t.Finish_Value)):"",t})),t.CurrentProcessPage=1,t.ProcessDataList.length>8?(t.CurrentProcessDataList=t.ProcessDataList.slice(0,8),t.MaxProcessPageSize=Math.ceil(t.ProcessDataList.length/8)):t.CurrentProcessDataList=JSON.parse(JSON.stringify(t.ProcessDataList))):t.$message.error(e.Message)})).finally((function(t){}))},changeProject:function(){this.getProcessOutput()},changeProcess:function(){var t=this;this.form.WorkingProcessId?(this.form.WorkingTeamName=this.ProcessList.find((function(e){return e.Id===t.form.WorkingProcessId})).Name,this.ProcessDataList.map((function(e,a){e.Name===t.form.WorkingTeamName&&(t.CurrentProcessPage=Math.ceil((a+1)/8))})),this.CurrentProcessDataList=this.ProcessDataList.slice(8*this.CurrentProcessPage-8,8*this.CurrentProcessPage)):this.form.WorkingTeamName=""},changeProcessDate:function(){this.getProcessOutput()},getWorkTeamOutput:function(){var t=this;(0,c.GetWorkTeamOutput)({TimeStart:this.form.teamDate?this.form.teamDate[0]:"",TimeEnd:this.form.teamDate?this.form.teamDate[1]:"",WorkingTeamId:this.form.WorkingTeamId}).then((function(e){e.IsSucceed?t.tableData=e.Data.Data.map((function(t){return t})):t.$message.error(e.Message)})).finally((function(t){}))},changeWorkTeam:function(){this.getWorkTeamOutput()},changeWorkTeamDate:function(){this.getWorkTeamOutput()},handelView:function(){this.$router.push({name:"PROTeamOutputReport"})}}}},"7f6f":function(t,e){t.exports="https://integ-plat-produce-test.bimtk.com/static/img/完成率********************"},a024:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProcessFlow=l,e.AddProessLib=M,e.AddTechnology=c,e.AddWorkingProcess=i,e.DelLib=I,e.DeleteProcess=k,e.DeleteProcessFlow=D,e.DeleteTechnology=_,e.DeleteWorkingTeams=b,e.GetAllProcessList=f,e.GetCheckGroupList=j,e.GetChildComponentTypeList=w,e.GetFactoryAllProcessList=h,e.GetFactoryPeoplelist=S,e.GetFactoryWorkingTeam=P,e.GetGroupItemsList=C,e.GetLibList=n,e.GetLibListType=x,e.GetProcessFlow=m,e.GetProcessFlowListWithTechnology=p,e.GetProcessList=u,e.GetProcessListBase=d,e.GetProcessListTeamBase=F,e.GetProcessListWithUserBase=R,e.GetProcessWorkingTeamBase=B,e.GetTeamListByUser=A,e.GetTeamProcessList=v,e.GetWorkingTeam=y,e.GetWorkingTeamBase=G,e.GetWorkingTeamInfo=O,e.GetWorkingTeams=L,e.GetWorkingTeamsPageList=T,e.SaveWorkingTeams=W,e.UpdateProcessTeam=g;var s=r(a("b775")),o=r(a("4328"));function n(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:t})}function i(t){return(0,s.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:o.default.stringify(t)})}function c(t){return(0,s.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:o.default.stringify(t)})}function l(t){return(0,s.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:o.default.stringify(t)})}function u(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:o.default.stringify(t)})}function d(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:o.default.stringify(t)})}function f(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:o.default.stringify(t)})}function h(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:t})}function m(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:o.default.stringify(t)})}function p(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:t})}function g(t){return(0,s.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:o.default.stringify(t)})}function P(){return(0,s.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:o.default.stringify(t)})}function v(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:o.default.stringify(t)})}function C(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:o.default.stringify(t)})}function D(t){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:o.default.stringify(t)})}function _(t){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:o.default.stringify(t)})}function k(t){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:t})}function L(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:t})}function T(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:t})}function W(t){return(0,s.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:t})}function b(t){return(0,s.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:t})}function O(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:o.default.stringify(t)})}function G(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:o.default.stringify(t)})}function F(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:o.default.stringify(t)})}function R(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:t})}function S(t){return(0,s.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function j(t){return(0,s.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:t})}function M(t){return(0,s.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:t})}function w(t){return(0,s.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:t})}function I(t){return(0,s.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:t})}function x(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:t})}function B(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:t})}function A(t){return(0,s.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:t})}},d7a3:function(t,e,a){"use strict";var r=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=r(a("c14f")),o=r(a("1da1")),n=a("5e99"),i=a("fd31"),c=a("7196");e.default={data:function(){return{FactoryDetailData:{},ProfessionalType:[],factoryPeoplelist:[]}},methods:{getCurFactory:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,n.GetCurFactory)({}).then((function(e){e.IsSucceed?t.FactoryDetailData=e.Data[0]:t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},getFactoryTypeOption:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,i.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(e){e.IsSucceed?t.ProfessionalType=e.Data:t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},getFactoryPeoplelist:function(){var t=this;return(0,o.default)((0,s.default)().m((function e(){return(0,s.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,c.GetFactoryPeoplelist)({}).then((function(e){e.IsSucceed?t.factoryPeoplelist=e.Data:t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},getManageCycle:function(){if(this.FactoryDetailData.Manage_Cycle_Enabled){var t,e,a,r,s,o,n=(new Date).getFullYear(),i=(new Date).getMonth()+1;1===this.FactoryDetailData.Manage_Cycle_Begin_Type?i-1===0?(t=n-1,e=12):(t=n,e=i-1):2===this.FactoryDetailData.Manage_Cycle_Begin_Type?(t=n,e=i):3===this.FactoryDetailData.Manage_Cycle_Begin_Type&&(i+1===13?(t=n+1,e=1):(t=n,e=i+1)),a=this.checkDate(t,e,this.FactoryDetailData.Manage_Cycle_Begin_Date),1===this.FactoryDetailData.Manage_Cycle_End_Type?i-1===0?(r=n-1,s=12):(r=n,s=i-1):2===this.FactoryDetailData.Manage_Cycle_End_Type?(r=n,s=i):3===this.FactoryDetailData.Manage_Cycle_End_Type&&(i+1===13?(r=n+1,s=1):(r=n,s=i+1)),o=this.checkDate(r,s,this.FactoryDetailData.Manage_Cycle_End_Date);var c=t+"-"+this.zeroFill(e)+"-"+this.zeroFill(a),l=r+"-"+this.zeroFill(s)+"-"+this.zeroFill(o);return[c,l]}return!1},checkDate:function(t,e,a){var r;return r=t%4===0?2===e?a>29?29:a:(4===e||6===e||9===e||11===e)&&a>30?30:a:2===e?a>28?28:a:(4===e||6===e||9===e||11===e)&&a>30?30:a,r},zeroFill:function(t){return t<10?"0"+t:t}}}},f2c0:function(t,e,a){},ff8d:function(t,e,a){"use strict";a.r(e);var r=a("76fb"),s=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);e["default"]=s.a}}]);