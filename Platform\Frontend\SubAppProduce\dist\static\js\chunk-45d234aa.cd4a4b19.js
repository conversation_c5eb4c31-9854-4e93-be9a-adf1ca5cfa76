(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-45d234aa","chunk-2d0e2790","chunk-2d0c91c4"],{"039b":function(e,t,a){"use strict";a("d5af9")},"03ff":function(e,t,a){"use strict";a.r(t);var n=a("8bcc"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"070e":function(e,t,a){},"09a5":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909"));a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("b64b"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("ddb0");var o=a("ed08"),i=a("586a");t.default={name:"TracePlot",props:{trackDrawerData:{type:Object,default:function(){return{}}}},data:function(){return{activities:[],MocContent:[],ChangeUserName:"",ChangeDate:"",one:{Count:0,Operator:"",OperateTime:""},two:{},three:[],isMoreSchdule:!1,isMoreSchduleStatus:0,loading:!0}},mounted:function(){this.getComponentProductionTrack()},methods:{getComponentProductionTrack:function(){var e=this;this.loading=!0,(0,i.GetTrackList)({PartAggregateId:this.trackDrawerData.Part_Aggregate_Id}).then((function(t){var a=t.Data,n=a.Deepen,o=a.Schduled,i=a.ChangeInfo,l=a.WorkProcessList,s=(null===i||void 0===i?void 0:i.ChangeUserName)||"",u=(null===i||void 0===i?void 0:i.ChangeDate)||"",c=null===i||void 0===i?void 0:i.MocContent;Object.assign(e.one,n),e.ChangeUserName=s,e.ChangeDate=u,e.MocContent=c&&JSON.parse(c||[]),e.two=o||{Status:(null===o||void 0===o?void 0:o.Status)||1},e.isMoreSchdule=null===o||void 0===o?void 0:o.IsMultipleTeam,e.three=l;var d=(0,r.default)(new Set(l.map((function(e){return e.Status}))));e.isMoreSchduleStatus=d.includes(2)?2:d.includes(3)?3:1})).finally((function(){e.loading=!1}))},getIconOrColor:function(e,t){return"icon"===e?3===t?"el-icon-success":"el-icon-yuanxing":"iconColor"===e?2===t?"inProgress":3===t?"complete":"init":"color"===e?2===t?"blue1":3===t?"green":"":"bgIcon"===e?2===t?"cs-unComplete-icon":3===t?"cs-success-icon":"cs-init-icon":void 0},getTime:function(e){return(0,o.parseTime)(e,"{y}-{m}-{d} {h}:{i}:{s}")}}}},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=o(),l=e-i,s=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=s;var e=Math.easeInOutQuad(u,i,l,t);r(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},"0bb3":function(e,t,a){"use strict";a.r(t);var n=a("f802"),r=a("140a");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("82e1");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"178a5afd",null);t["default"]=l.exports},"0cdd":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=a("a667"),s=n(a("bbc2")),u=a("0e9a"),c=a("66f9"),d=a("2c61"),f=a("1b69"),m=a("f2f6"),p=a("2d91");t.default={name:"ComponentImport",components:{OSSUpload:s.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Project_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[],projectList:[],unitList:[]}},watch:{typeId:function(e){this.form.Type_Id=e},"form.MonomerId":function(){this.getArea()},"form.Area_Name":function(e){e&&this.getUnitList()}},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.form.Factory_Id=localStorage.getItem("CurReferenceId"),e.getInfo(),t.n=1,e.getProjectList();case 1:return t.n=2,e.getMonomerStatus();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,p.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=3;break}return t.n=2,e.getMonomerList();case 2:t.n=4;break;case 3:e.getArea();case 4:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Batches="",e.form.MonomerId="",e.form.Area_Name="",t.n=1,(0,c.GetProMonomerList)({projectId:e.form.Project_Id,id:e.projectList.find((function(t){return t.Sys_Project_Id===e.form.Project_Id})).Id});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getUnitList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,n,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return a={Sys_Project_Id:e.form.Project_Id},e.isUnityInfo&&(a=(0,r.default)((0,r.default)({},a),{},{Area_Id:null===(n=e.AreaOption.find((function(t){return t.Name===e.form.Area_Name})))||void 0===n?void 0:n.Id})),t.n=1,(0,m.GetInstallUnitList)(a);case 1:i=t.v,e.unitList=i.Data;case 2:return t.a(2)}}),t)})))()},getProjectList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetProjectList)({});case 1:a=t.v,a.IsSucceed?(e.projectList=a.Data,e.projectList.length>0&&(e.form.Project_Id=e.projectList[0].Sys_Project_Id)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName")},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,l.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,u.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getArea:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,l.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code,ProjectId:e.form.Project_Id}});case 1:a=t.v,a.IsSucceed?(e.AreaOption=a.Data,e.form.Area_Name=a.Data[0].Name):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,r=a.Type_Id,o=a.Project_Id,i=a.Area_Name,l=a.Factory_Id,s=a.MonomerId,u=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",r),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",e.projectList.find((function(e){return e.Sys_Project_Id===o})).Id),e.fileFormData.append("Area_Name",i),e.fileFormData.append("Factory_Id",l),e.fileFormData.append("MonomerId",s),e.fileFormData.append("Batches",u),(0,d.CommonImportModelToComp)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},"0ce7":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),o=n(a("1da1"));a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var i=n(a("9b15")),l=a("5c96"),s=a("21c4"),u=a("6186"),c=function(){(0,u.SecurityToken)().then((function(e){sessionStorage.setItem("ossToken",JSON.stringify(e.Data))}))};c(),setInterval((function(){c()}),114e4);t.default={name:"OSSUpload",mixins:[l.Upload],props:{ossOnSuccess:{type:Function,default:Function},piecesize:{type:Number,default:2},httpRequest:{type:Function,default:function(e){var t=this,a=JSON.parse(sessionStorage.getItem("ossToken"));this.$nextTick((function(){var n,l=null!==(n=t.data)&&void 0!==n&&n.piecesize?1*t.data.piecesize:2,c=new i.default({region:"oss-"+a.regionId,secure:!0,accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken,bucket:a.bucket,refreshSTSToken:function(){var e=(0,o.default)((0,r.default)().m((function e(){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.securityToken();case 1:return a=e.v,e.a(2,{accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken})}}),e)})));function a(){return e.apply(this,arguments)}return a}(),refreshSTSTokenInterval:9e5}),d=e.file,f=new Date;c.multipartUpload((0,s.getTenantId)()+"/"+f.getFullYear()+"/"+(1*f.getMonth()+1)+"/"+f.getDate()+"/"+f.getMinutes()+"_"+f.getSeconds()+"_"+f.getMilliseconds()+"/"+d.name,d,{progress:function(t,a){this.process=a,e.onProgress({percent:Math.floor(100*t)})},parallel:4,partSize:1048576*l,meta:{}}).then((function(t){if(200===t.res.statusCode){var a,n=t.res.requestUrls[0]&&t.res.requestUrls[0].split("?")[0];!1===(null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.callback)?e.onSuccess({Data:n+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name}):(0,u.GetOssUrl)({url:n}).then((function(t){e.onSuccess({Data:n+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name,encryptionUrl:t.Data})}))}}),(function(t){e.onError(t)}))}))}}},data:function(){return{process:null}},watch:{process:function(e){this.$emit("getprocess",e)}},mounted:function(){},methods:{handleFn:function(e){},securityToken:function(){return new Promise((function(e,t){(0,u.SecurityToken)({}).then((function(t){e(t.Data)})).catch((function(e){t(e)}))}))}}}},"0cea":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose,close:e.handleClose}},[a("div"),e.isDeep||e.isSHQD?a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载零件导入模板")])],1):e._e(),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"下载模板",prop:"Template_Type"}},[a("el-radio",{attrs:{label:2},model:{value:e.form.Template_Type,callback:function(t){e.$set(e.form,"Template_Type",t)},expression:"form.Template_Type"}},[e._v("固定模板")]),a("el-radio",{attrs:{label:1},model:{value:e.form.Template_Type,callback:function(t){e.$set(e.form,"Template_Type",t)},expression:"form.Template_Type"}},[e._v("动态模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),a("el-form-item",{attrs:{label:"区域",prop:"Area_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}})],1),a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:2,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},r=[]},"0d9a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddArea=re,t.AddDeepFile=ee,t.AddMonomer=K,t.AreaDelete=ie,t.AreaGetEntity=ae,t.AttachmentGetEntities=U,t.ChangeLoad=P,t.CommonImportDeependToComp=X,t.ContactsAdd=F,t.ContactsDelete=$,t.ContactsEdit=M,t.ContactsGetEntities=j,t.ContactsGetEntity=G,t.ContactsGetTreeList=A,t.DeleteMonomer=Z,t.DepartmentAdd=L,t.DepartmentDelete=D,t.DepartmentEdit=k,t.DepartmentGetEntities=T,t.DepartmentGetEntity=I,t.DepartmentGetList=w,t.EditArea=oe,t.EditMonomer=Q,t.FileAdd=y,t.FileAddType=f,t.FileDelete=h,t.FileEdit=_,t.FileGetEntity=p,t.FileHistory=C,t.FileMove=v,t.FileTypeAdd=d,t.FileTypeDelete=c,t.FileTypeEdit=m,t.FileTypeGetEntities=o,t.FileTypeGetEntity=i,t.GeAreaTrees=ne,t.GetAreaTreeList=te,t.GetDictionaryDetailListByCode=R,t.GetEntitiesByRecordId=E,t.GetEntitiesProject=s,t.GetFileCatalog=l,t.GetFilesByType=u,t.GetGetMonomerList=Y,t.GetLoadingFiles=S,t.GetMonomerEntity=H,t.GetPartCodeList=b,t.GetProMonomerList=J,t.GetProjectComponentCodeList=g,t.GetProjectEntity=le,t.GetProjectsflowmanagementAdd=B,t.GetProjectsflowmanagementEdit=W,t.GetProjectsflowmanagementInfo=z,t.GetShortUrl=q,t.ImportDeependToSteel=V,t.ImportPartList=se,t.SysuserGetUserEntity=O,t.SysuserGetUserList=x,t.UserGroupTree=N;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:e})}function i(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:e})}function l(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetFileCatalog",method:"post",data:e})}function s(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntitiesProject",method:"post",data:e})}function u(e){return(0,r.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:e})}function c(e){return(0,r.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:e})}function d(e){return(0,r.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:e})}function f(e){return(0,r.default)({url:"/SYS/Sys_FileType/AddType",method:"post",data:e})}function m(e){return(0,r.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:e})}function p(e){return(0,r.default)({url:"/SYS/Sys_File/GetEntity",method:"post",data:e})}function h(e){return(0,r.default)({url:"/SYS/Sys_File/Delete",method:"post",data:e})}function g(e){return(0,r.default)({url:"/pro/component/GetProjectComponentCodeList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Part/GetPartCodeList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/SYS/Sys_File/Add",method:"post",data:e})}function _(e){return(0,r.default)({url:"/SYS/Sys_File/Edit",method:"post",data:e})}function v(e){return(0,r.default)({url:"/SYS/Sys_File/Move",method:"post",data:e})}function P(e){return(0,r.default)({url:"/SYS/Sys_File/IsLoad",method:"post",data:e})}function C(e){return(0,r.default)({url:"/SYS/Sys_File/OldFile",method:"post",data:e})}function S(e){return(0,r.default)({url:"/SYS/Sys_File/GetBIMList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetEntity",method:"post",data:e})}function T(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetEntities",method:"post",data:e})}function D(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Delete",method:"post",data:e})}function k(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Edit",method:"post",data:e})}function L(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Add",method:"post",data:e})}function w(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/SYS/User/GetUserList",method:"post",data:e})}function O(e){return(0,r.default)({url:"/SYS/User/GetUserEntity",method:"post",data:e})}function N(e){return(0,r.default)({url:"/SYS/UserGroup/GetChildGroupTree",method:"post",data:e})}function R(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function G(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetEntity",method:"post",data:e})}function j(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:e})}function A(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:e})}function $(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Delete",method:"post",data:e})}function M(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Edit",method:"post",data:e})}function F(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Add",method:"post",data:e})}function U(e){return(0,r.default)({url:"/SYS/Sys_File/GetAttachmentEntities",method:"post",data:e})}function E(e){return(0,r.default)({url:"/SYS/Sys_File/GetEntitiesByRecordId",method:"post",data:e})}function B(e){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/Add",method:"post",data:e})}function W(e){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/Edit",method:"post",data:e})}function z(e){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/GetEntity",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PLM/XModel/GetShortUrl",method:"post",data:e})}function V(e){return(0,r.default)({url:"/plm/component/ImportDeependToSteel",method:"get",params:e})}function Y(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetGetMonomerList",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetMonomerEntity",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetProMonomerList",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/AddMonomer",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/EditMonomer",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/DeleteMonomer",method:"post",data:e})}function X(e){return(0,r.default)({url:"/plm/MaterialInfo/CommonImportDeependToComp",method:"get",params:e})}function ee(e){return(0,r.default)({url:"/PLM/Component/AddDeepFile",method:"post",data:e,timeout:18e5})}function te(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetAreaTreeList",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Project/GetArea",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Project/AddArea",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Project/EditArea",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Project/Delete",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/Part/ImportPartList",method:"post",data:e})}},"0e5d":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"drawer-container"},[a("el-timeline",[a("el-timeline-item",{staticClass:"complete",attrs:{icon:"el-icon-success",size:"large"}},[a("div",{staticClass:"title"},[e._v("深化导入")]),a("div",{staticClass:"content"},[a("div",{staticClass:"info green"},[e._v("导入数量:"+e._s(e.one.Count))]),a("div",{staticClass:"preson"},[e._v(e._s(e.one.Operator))]),a("div",{staticClass:"time"},[e._v(e._s(e.getTime(e.one.OperateTime)))])])]),a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.two.Status),attrs:{icon:e.getIconOrColor("icon",e.two.Status),size:"large"}},[a("div",{staticClass:"title"},[e._v("排产完成")]),a("div",{staticClass:"content"},[a("div",{class:e.getIconOrColor("color",e.two.Status)},[e._v("排产数量:"+e._s(e.two.Count||0))]),1!==e.two.Status?a("div",{staticClass:"preson"},[e._v(e._s(e.two.Operator))]):e._e(),1!==e.two.Status?a("div",{staticClass:"time"},[e._v(e._s(e.getTime(e.two.OperateTime)))]):e._e()])]),1!==e.two.Status?[e.isMoreSchdule?[a("el-timeline-item",{staticClass:"moreSchdule",class:e.getIconOrColor("iconColor",e.isMoreSchduleStatus),attrs:{icon:e.getIconOrColor("icon",e.isMoreSchduleStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("所有工序")]),a("div",{staticClass:"inner-content"},e._l(e.three,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"inner-title"},[a("span",{class:["inner-title-icon",e.getIconOrColor("bgIcon",t.Status)]}),a("span",{staticClass:"title"},[e._v(e._s(t.ProcessName))])]),a("div",{staticClass:"zj-x ml15"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.Subs.length>0?e._l(t.Subs,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.Status)},[e._v(e._s(t.Operator)+"完成:"+e._s(t.Count))]),t.Count?a("div",{staticClass:"time"},[e._v(e._s(e.getTime(t.OperateTime)))]):e._e()])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)})),0)])]:e._l(e.three,(function(t,n){return a("el-timeline-item",{key:n,class:e.getIconOrColor("iconColor",t.Status),attrs:{icon:e.getIconOrColor("icon",t.Status),type:t.type,size:"large"}},[a("div",{staticClass:"title"},[e._v(e._s(t.ProcessName))]),a("div",{staticClass:"zj-x"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.Subs.length>0?e._l(t.Subs,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.Status)},[e._v(e._s(t.Operator)+"完成:"+e._s(t.Count))]),t.Count?a("div",{staticClass:"time"},[e._v(e._s(e.getTime(t.OperateTime)))]):e._e()])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)}))]:e._e()],2)],1)},r=[]},"0f9e":function(e,t,a){},1276:function(e,t,a){"use strict";var n=a("c65b"),r=a("e330"),o=a("d784"),i=a("825a"),l=a("861d"),s=a("1d80"),u=a("4840"),c=a("8aa5"),d=a("50c4"),f=a("577e"),m=a("dc4a"),p=a("14c3"),h=a("9f7f"),g=a("d039"),b=h.UNSUPPORTED_Y,y=4294967295,_=Math.min,v=r([].push),P=r("".slice),C=!g((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var a="ab".split(e);return 2!==a.length||"a"!==a[0]||"b"!==a[1]})),S="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;o("split",(function(e,t,a){var r="0".split(void 0,0).length?function(e,a){return void 0===e&&0===a?[]:n(t,this,e,a)}:t;return[function(t,a){var o=s(this),i=l(t)?m(t,e):void 0;return i?n(i,t,o,a):n(r,f(o),t,a)},function(e,n){var o=i(this),l=f(e);if(!S){var s=a(r,o,l,n,r!==t);if(s.done)return s.value}var m=u(o,RegExp),h=o.unicode,g=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(b?"g":"y"),C=new m(b?"^(?:"+o.source+")":o,g),I=void 0===n?y:n>>>0;if(0===I)return[];if(0===l.length)return null===p(C,l)?[l]:[];var T=0,D=0,k=[];while(D<l.length){C.lastIndex=b?0:D;var L,w=p(C,b?P(l,D):l);if(null===w||(L=_(d(C.lastIndex+(b?D:0)),l.length))===T)D=c(l,D,h);else{if(v(k,P(l,T,D)),k.length===I)return k;for(var x=1;x<=w.length-1;x++)if(v(k,w[x]),k.length===I)return k;D=T=L}}return v(k,P(l,T)),k}]}),S||!C,b)},1298:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=a("a667"),s=n(a("15ac")),u=n(a("0f97")),c=a("ed08"),d=a("21a6");t.default={components:{DynamicDataTable:u.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{searchDate:[],tbLoading:!1,btnLoading:!1,tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],total:0,tbData:[],selectArray:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return a=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),"2"===a&&(e.mode="factory"),t.n=1,e.getTableConfig("steels_import_file_page_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0;var t={PageInfo:(0,r.default)({},this.queryInfo)};this.searchDate&&this.searchDate.length>1?(t.HisStartDate=this.searchDate[0],t.HisEndDate=this.searchDate[1]):(t.HisStartDate="",t.HisEndDate=""),"factory"===this.mode&&(t.ProjectId=this.sysProjectId),(0,l.GetPageStorageHistorySteel)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},multiSelectedChange:function(e){this.selectArray=e},onSubmit:function(){var e=this;this.btnLoading=!0;var t=this.selectArray.map((function(t){return/http/.test(t.FileUrl)?t.FileUrl:(0,c.combineURL)(e.$baseUrl,t.FileUrl)})).toString();(0,l.DownAllFiles)({urlStr:t,itemId:"历史清单导出"}).then((function(t){if("text/xml"===t.type)e.$message({message:"找不到文件",type:"error"});else{var a="历史清单导出",n=new Blob([t],{type:"application/zip;charset=utf-8"});(0,d.saveAs)(n,a)}e.btnLoading=!1}))}}}},1389:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("a630"),a("caad"),a("a15b"),a("d81d"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("466d"),a("159b"),a("ddb0");var r=n(a("c14f")),o=n(a("1da1")),i=n(a("2909")),l=n(a("5530")),s=a("0d9a"),u=n(a("bc3a")),c=n(a("c7ab")),d=a("b28f"),f=a("5012"),m=a("c24f"),p=a("0e9a"),h=a("21c4"),g=n(a("bf8b")),b=a("e144"),y=a("ed08"),_=a("2ef0"),v=(a("f382"),a("2f62")),P={Id:"",Doc_Catelog:"",Doc_Type:"",Project_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",IsChanged:!1,Is_Load:!1,Doc_File:"",ishistory:!0,BimId:"",Drawing_Match_Type:1,Is_Big_File:!1,model:{UserIds:[],Title:"",Content:""},Component_Codes:[]};t.default={components:{OSSUpload:c.default,formItem:g.default},props:{typeEntity:{type:Object,default:function(){}},isCzd:{type:Boolean,default:!1},isLJXT:{type:Boolean,default:!1},isBJXT:{type:Boolean,default:!1},isGjXT:{type:Boolean,default:!1}},data:function(){return{isNotify:!1,type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,btnLoading:!1,form:(0,l.default)({},P),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}],Drawing_Match_Type:[{required:!0,message:"请选择",trigger:"change"}]},treeParams:{clickParent:!0,filterable:!1,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},selectParams:{multiple:!1,clearable:!0,placeholder:"请输入内容"},fileType:"",curFile:"",bimvizId:"",isDeep:!1,projectId:"",professionalCode:"",isSHQD:"",isEditSCYTH:!1,modelApp:"glendale",uploadFileLength:0,fileNums:0,compList:[],selectLoading:!1,allCompList:[],allPartList:[],relatedItem:[],disabledOptions:[!1,!1,!1]}},computed:(0,l.default)({drawingList:function(){var e=this.isComp?"构件号":this.isPart?"零件名":"部件名";return[{label:"名称",value:1,disabled:this.disabledOptions[0]},{label:"".concat(e,"_页码_版本"),value:2,disabled:this.disabledOptions[1]},{label:"".concat(e,"_版本"),value:3,disabled:this.disabledOptions[2]}]},BIMFiles:function(){return"PLMBimFiles"===this.$route.name},isGlendale:function(){return this.BIMFiles&&"glendale"===this.modelApp},CadFiles:function(){return"PRODeepenFiles"===this.$route.name},isCZD:function(){return this.isCzd||"原材料材质单"===this.form.Type_Name},isComp:function(){return"构件详图"===this.form.Type_Name||this.isGjXT},isPart:function(){return"零件详图"===this.form.Type_Name||this.isLJXT},isUnitPart:function(){return"部件详图"===this.form.Type_Name||this.isBJXT}},(0,v.mapGetters)("tenant",["isVersionFour"])),watch:{"fileList.length":function(e){if(this.isVersionFour){var t=this.fileList.some((function(e){return!e.name.includes("_")})),a=this.fileList.every((function(e){return(e.name.match(/_/g)||[]).length>=1})),n=this.fileList.every((function(e){return(e.name.match(/_/g)||[]).length>=2}));this.disabledOptions=[!1,!1,!1],t?(this.disabledOptions[1]=!0,this.disabledOptions[2]=!0,2!==this.form.Drawing_Match_Type&&3!==this.form.Drawing_Match_Type||(this.form.Drawing_Match_Type=1)):n?(this.disabledOptions[1]=!1,this.disabledOptions[2]=!1):a&&(this.disabledOptions[1]=!0,this.disabledOptions[2]=!1,2===this.form.Drawing_Match_Type&&(this.form.Drawing_Match_Type=1))}}},created:function(){this.fileType=this.$route.name,"PLMPicVideoFiles"===this.fileType&&(this.allowFile="image/*"),this.BIMFiles&&(this.allowFile=".ifc,.bzip,.bzip2,.glzip"),this.isVersionFour||delete this.form.Drawing_Match_Type},methods:{getCompList:function(){var e=this;this.isPart?(0,s.GetPartCodeList)({SysProjectId:this.projectId,type:2}).then((function(t){t.IsSucceed?e.allPartList=t.Data:e.$message({message:t.Message,type:"error"})})):this.isUnitPart?(0,s.GetPartCodeList)({SysProjectId:this.projectId,type:3}).then((function(t){t.IsSucceed?e.allPartList=t.Data:e.$message({message:t.Message,type:"error"})})):(0,s.GetProjectComponentCodeList)({Sys_Project_Id:this.projectId}).then((function(t){t.IsSucceed?e.allCompList=t.Data||[]:e.$message({message:t.Message,type:"error"})}))},remoteMethod:function(e){var t=this;e?(this.selectLoading=!0,setTimeout((function(){var a=e.split(" ").map((function(e){return e.toLowerCase()})).filter((function(e){return""!==e}));t.compList=(t.isPart||t.isUnitPart?t.allPartList:t.allCompList).filter((function(e){return a.length>1?a.some((function(t){return e.toLowerCase()===t})):a.some((function(t){return e.toLowerCase().includes(t)}))})),t.compList=t.compList.map((function(e){return{label:e,value:e}})),t.compList.length>0&&t.compList.unshift({label:"全部",value:"all"}),t.selectLoading=!1}),200)):this.compList=[]},selectChange:function(e){if(e.includes("all")){var t=(0,y.deepClone)(this.compList);t.shift(),this.relatedItem=this.relatedItem.filter((function(e){return"all"!==e})),this.relatedItem=Array.from(new Set([].concat((0,i.default)(this.relatedItem),(0,i.default)(t.map((function(e){return e.value}))))))}},visibleChange:function(e){e||this.remoteMethod()},onExceed:function(){"edit"===this.type?this.$message.error("最多只能上传1个文件"):this.$message.error("最多只能上传1000个文件")},getTemplate:function(){var e=this,t="plm_steels_detailImport,".concat(this.professionalCode);(0,f.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,p.downloadBlobFile)(t,"".concat(e.form.Type_Name,"_深化清单导入模板"))}))},handleRadioChange:function(e){e&&"edit"===this.type&&this.form.Doc_Title&&(this.form.model.Title="资料变更通知："+this.form.Doc_Title)},handleChange:function(e,t){this.uploadFileLength=t.length},beforeUpload:function(e){this.curFile=e,this.loading=!0,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this,n=0;this.fileList.filter((function(t,a){t.name===e.name&&(n=a)})),this.fileList.splice(n,1),this.attachments.splice(n,1);var r="",o="";t.forEach((function(e){r=r+","+e.name.substring(0,e.name.lastIndexOf(".")),o=o+","+e.name})),this.form.Doc_Title=r.substring(1),this.form.Doc_Content=r.substring(1),this.form.Doc_File=o.substring(1),this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.loading=!t.every((function(e){return"success"===e.status})),setTimeout((function(){a.btnLoading=!t.every((function(e){return"success"===e.status}))}),1e3)},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){var n=this;a=(0,_.uniqBy)(a,"name"),this.fileList=a,this.attachments=this.fileList.filter((function(e){return"success"===e.status})).map((function(e){return{File_Url:e.response.Data.split("*")[0],File_Size:e.response.Data.split("*")[1],File_Type:e.response.Data.split("*")[2],File_Name:e.response.Data.split("*")[3]}}));var r=this.attachments.map((function(e){return e.File_Name})).join(","),o=this.attachments.map((function(e){return e.File_Name.substring(0,e.File_Name.lastIndexOf("."))})).join(",");if(this.form.Doc_Title=this.form.Doc_Content=o,this.form.model.Title=("edit"===this.type?"资料变更通知：":"资料新增通知：")+this.form.Doc_Title,this.form.Doc_File=r,this.loading=!a.every((function(e){return"success"===e.status})),this.fileNums++,this.fileNums>this.uploadFileLength-10&&a.some((function(e){return"ready"===e.status}))){var i=a.filter((function(e){return"ready"===e.status}));i.forEach((function(e){n.retryUpload(e)}))}setTimeout((function(){n.btnLoading=!a.every((function(e){return"success"===e.status}))}),1e3)},retryUpload:function(e){this.$refs.companyUpload.submit(e)},handleOpen:function(e,t,a){var n=this,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4?arguments[4]:void 0,i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];this.isNotify=!1,this.projectId=o,this.isDeep=r,this.form=(0,l.default)((0,l.default)({},P),{},{model:{UserIds:[],Title:"",Content:""}}),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.isSHQD=t.isSHQD,this.professionalCode=t.Code,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.isEditSCYTH=i,this.fileNums=0,"add"===this.type?(this.fileList=[],this.title="新增文件",this.form.Id=""):(this.title="编辑文件",(0,s.AttachmentGetEntities)({recordId:t.Id}).then((function(e){n.attachments=e.Data.map((function(e){return delete e.Id,e})),n.fileList=e.Data.map((function(e){return e.name=e.File_Name,e}))})),(0,s.FileGetEntity)({id:t.Id}).then((function(e){e.IsSucceed&&(n.form=(0,l.default)((0,l.default)({},e.Data),{},{model:{UserIds:[],Title:"",Content:""}}),n.isCZD&&(n.relatedItem=e.Data.Component_Codes),(n.isComp||n.isPart||n.isUnitPart)&&(n.relatedItem=e.Data.Codes))}))),this.$nextTick((function(e){n.getCompList()}))},handleClose:function(){try{this.relatedItem=[],this.attachments=[],this.$refs["form"].resetFields(),this.isNotify=!1,this.btnLoading=!1,this.loading=!1,this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,o.default)((0,r.default)().m((function t(a){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(!a){t.n=6;break}if(e.loading=!0,e.btnLoading=!0,"edit"!==e.type){t.n=1;break}return e.updateInfo(),t.a(2);case 1:if(!e.CadFiles||!0!==e.form.Is_Big_File){t.n=4;break}return t.n=2,(0,m.getConfigure)({code:"glendale_cad_token"});case 2:return d.Glendale.cadToken=t.v.Data,t.n=3,(0,m.getConfigure)({code:"glendale_cad_url"});case 3:d.Glendale.cadUrl=t.v.Data,u.default.all(e.fileList.map((function(e,t){var a=e.raw.name.split("."),n=a[a.length-1];if("glzip"===n||"dwg"===n){var r=new FormData;r.append("file",e.raw);var o=(0,l.default)((0,l.default)({},d.Glendale.optionsCad),{},{UniqueCode:"".concat((0,h.getTenantId)(),"__").concat((0,b.v4)()),Name:encodeURIComponent(e.name)});return u.default.post("".concat(d.Glendale.cadUrl).concat(d.Glendale.uploadUrl,"?input=").concat(JSON.stringify(o)),r,{headers:{Token:d.Glendale.cadToken,"Content-Type":"multipart/form-data"}}).then((function(t){e.BimId=t.data.datas.lightweightName}))}}))).then((function(t){var a=e.attachments.map((function(t){var a=e.fileList.find((function(e){return e.name===t.File_Name}));return a.BimId}));e.form.BimId=a.join(","),e.updateInfo()})),t.n=5;break;case 4:e.updateInfo();case 5:t.n=7;break;case 6:return e.$message({message:"请将表单填写完整",type:"warning"}),t.a(2,!1);case 7:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},updateInfo:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a,n,o,i,l,u,c,d;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(a=Array.from(new Set(e.attachments.map((function(e){return e.File_Name})))).map((function(t){return e.attachments.find((function(e){return e.File_Name===t}))})),n=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),i={},i=e.isNotify?e.form.model:{UserIds:[],Title:"",Content:""},(e.isComp||e.isPart||e.isUnitPart)&&(l=e.relatedItem),e.isCZD&&(e.form.Component_Codes=e.relatedItem),u=e.isComp?0:e.isPart?1:e.isUnitPart?3:null,"add"!==e.type){t.n=2;break}return c=e.isDeep?s.AddDeepFile:s.FileAdd,d={file:e.form,model:i,attachmentList:a,codes:l,type:u},e.isDeep&&(d.file.Type_Name=e.professionalCode),e.isDeep&&"2"===n&&(d.projectId=e.projectId,d.factoryId=localStorage.getItem("CurReferenceId")),e.isDeep||"2"!==n||(d.file.Project_Id=e.projectId),t.n=1,c(d);case 1:o=t.v,t.n=4;break;case 2:return t.n=3,(0,s.FileEdit)({file:e.form,model:i,attachmentList:a,codes:l,type:u});case 3:o=t.v;case 4:o.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.$emit("getData",e.form.Doc_Type),e.loading=!1,e.btnLoading=!1,e.handleClose()):(e.loading=!1,e.btnLoading=!1,e.$message.error(o.Message));case 5:return t.a(2)}}),t)})))()},getAllLoadingFiles:function(){var e=this;(0,s.GetLoadingFiles)().then((function(t){e.updateLoadingFiles(t.Data)}))},updateLoadingFiles:function(e){var t=new BIMVIZ.WebApi({ip:BIM_API_CONFIG.IP,port:BIM_API_CONFIG.DB_PORT,key:BIM_API_CONFIG.KEY}),a=t.getModelProjectManager(),n={SceneComplex:BIMVIZ.SceneLoadMode.Dynamic,LoadFiles:e};a.updateSceneSettings(BIM_API_CONFIG.USER_NAME,this.bimvizId,n,(function(e){}))}}}},"13b3":function(e,t,a){"use strict";a.r(t);var n=a("fc05"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"13e8":function(e,t,a){"use strict";a.r(t);var n=a("40f4"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"140a":function(e,t,a){"use strict";a.r(t);var n=a("afdd"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},1451:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,stripe:""}})],1)])},r=[]},"155b5":function(e,t,a){},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,l=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var s=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),s=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=s.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||r.tablePageSize[0]),o(t.columns)}else t.$message({message:l,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var r=this.columns[n];if(r.Code===t){a.Type=r.Type,a.Filter_Type=r.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},1927:function(e,t,a){"use strict";a.r(t);var n=a("422f"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"19ad":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),e._l(e.filterLabelList,(function(t){return a("el-form-item",{key:t.SteelUnique,attrs:{label:t.label}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{max:t.unPackCount,clearable:""},model:{value:t.number,callback:function(a){e.$set(t,"number",a)},expression:"item.number"}}),a("div",[e._v("未打包数量："+e._s(t.unPackCount))])],1)})),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],2)},r=[]},"1a23":function(e,t,a){},"1d47":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,"empty-text":"暂无数据",border:""}},[a("el-table-column",{attrs:{prop:"Scheduling_Status",label:"零件状态",align:"center"}}),a("el-table-column",{attrs:{prop:"Component_Count",label:"数量",align:"center"}})],1),a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{"text-align":"right","margin-top":"10px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")])],1)])],1)],1)},r=[]},"1dbd":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530"));a("e9c4"),a("b64b");var o=a("a667");t.default={props:{customParams:{type:Object,default:function(){}}},data:function(){return{form:{Remark:"",Type:""},btnLoading:!1,rules:{Type:[{required:!0,message:"请选择",trigger:"change"}]}}},methods:{handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=JSON.parse(JSON.stringify(e.customParams));delete a.pageInfo;var n={Package:{ProjectID:localStorage.getItem("CurReferenceId"),Remark:e.form.Remark},Steels:[],Search:(0,r.default)((0,r.default)({},a),{},{Type:e.form.Type})};"2"!==e.form.Type?(e.btnLoading=!0,(0,o.BatchAdd)(n).then((function(t){t.IsSucceed?e.$emit("refresh"):e.$message({message:t.Message,type:"error"}),e.$emit("close"),e.btnLoading=!1}))):e.$emit("checkPackage",{data:n,type:1})}))}}}},"1dfd":function(e,t,a){"use strict";a.r(t);var n=a("7fe3"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},23061:function(e,t,a){"use strict";a.r(t);var n=a("6ca5"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"23e0":function(e,t,a){"use strict";a.r(t);var n=a("abeb"),r=a("4372");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"52639cd5",null);t["default"]=l.exports},"243b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("841c");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=n(a("0f97")),s=n(a("15ac")),u=a("a667");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{search:{type:String,default:""},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},tbData:[],columns:[],total:0,tbLoading:!1,currentRow:null}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_page_list,".concat(e.typeEntity.Code));case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,u.GetPageStorageBySearchPackages)({PageInfo:(0,r.default)((0,r.default)({},this.queryInfo),{},{Search:this.search}),TypeCode:this.typeEntity.Code}).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.$emit("setSelect",[]),e.$emit("clearRightData","")):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleRowClick:function(e){var t=e.row;this.$emit("leftClick",t.Id),this.handleSelect({row:t})},handleSelect:function(e){var t,a=this,n=e.row;if(n.Id===(null===(t=this.currentRow)||void 0===t?void 0:t.Id))return this.$refs.dyTable.clearSelection(),this.currentRow=null,void this.$emit("selectList",[]);this.currentRow=n,this.$refs.dyTable.clearSelection(),this.$nextTick((function(e){a.$refs.dyTable.toggleRowSelection(n)})),this.$emit("selectList",[n])},selectAll:function(){this.$refs.dyTable.clearSelection()}}}},2495:function(e,t,a){"use strict";a.r(t);var n=a("3033"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},2516:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("e9c4"),a("d3b7"),a("25f0"),a("159b");var r=n(a("34e9")),o=n(a("f39a")),i=n(a("f300")),l=n(a("15ac")),s=n(a("ca2d")),u=a("a667");t.default={components:{TopHeader:r.default,zLeft:o.default,Dialog:s.default,zRight:i.default},mixins:[l.default],props:{selectList:{type:Array,default:function(){return[]}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{search:"",search2:"",select:"",dialogVisible:!1,leftSelectList:[],rightSelectList:[],columnOption:[],currentPackId:"",submitObj:{},fromType:2}},methods:{handleClose:function(){this.dialogVisible=!1},clearRightData:function(){this.currentPackId="",this.fetchRight()},fetchLeft:function(){this.$refs["left"].fetchData()},fetchRight:function(){this.$refs["right"].fetchRightData(this.currentPackId)},leftClick:function(e){this.currentPackId=e,this.fetchRight()},handleDeleteLeft:function(){var e=this;this.$confirm("是否删除构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.DeletePackage)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString()}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},getColumn:function(e){this.columnOption=e},handleDeleteRight:function(){var e=this;this.$confirm("是否删除该构件?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.DeleteSteel)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString(),itemId:JSON.stringify(e.rightSelectList.map((function(e){return e.Id})))}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft(),e.$refs["right"].fetchRightData()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(){var e=this;this.dialogVisible=!0,this.$nextTick((function(t){e.$refs["dialog"].fetchData(e.leftSelectList)}))},getSubmitObj:function(e){this.submitObj=e},handleAdd:function(){var e=this;if(this.selectList.length||2!==this.fromType)if(this.leftSelectList.length){var t=this.selectList.every((function(e){return 1===e.Sup_Count&&1===e.SteelAmount}));t||2!==this.fromType?this.$confirm("确定加入此构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitObj.Package.Id=e.leftSelectList.map((function(e){return e.Id})).toString(),1===e.fromType?(0,u.BatchAdd)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"})})):(0,u.AddSteel)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))})).catch((function(){e.$message({type:"info",message:"已取消"})})):this.$message({message:"请点击生成构件包",type:"warning"})}else this.$message({message:"请先选择包",type:"warning"});else this.$message({message:"请先选择构件",type:"warning"})},getLeftList:function(e){this.leftSelectList=e},getRightList:function(e){this.rightSelectList=e},handlePackage:function(e){if(this.fromType=e,2===e){var t={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};this.selectList.forEach((function(e){t.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),this.submitObj=t}}}}},2694:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),o=a("586a"),i=n(a("15ac")),l=n(a("0f97"));t.default={components:{DynamicDataTable:l.default},mixins:[i.default],data:function(){return{tbLoading:!1,tbConfig:{Is_Page:!1},queryInfo:{Page:1,PageSize:10},columns:[{Code:"Working_Process_Name",Display_Name:"零件工序",Is_Display:!0,Sort:1},{Code:"Working_Process_Code",Display_Name:"工序代号",Is_Display:!0,Sort:2},{Code:"Finish_Count",Display_Name:"完成数量(件)",Align:"center",Is_Display:!0,Sort:3},{Code:"Finish_Weight",Display_Name:"完成重量(kg)",Align:"center",Is_Display:!0,Sort:4}],total:0,tbData:[]}},mounted:function(){},methods:{init:function(e,t,a){var n=this;this.tbLoading=!0,(0,o.GetPartProcessWeightList)((0,r.default)((0,r.default)({},e),{},{InstallUnit_Ids:t,Ids:a})).then((function(e){e.IsSucceed?n.tbData=e.Data.ProcessList:n.$message({message:e.Message,type:"error"}),n.tbLoading=!1}))}}}},"270c":function(e,t,a){"use strict";a("fd51")},"27b2":function(e,t,a){"use strict";a("73be")},"27e5":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.list,(function(t,n){return a("el-row",{key:t.id,staticClass:"item-x"},[a("div",{staticClass:"item"},[a("label",[e._v(" 属性名称 "),a("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.key,callback:function(a){e.$set(t,"key",a)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),a("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[a("label",[e._v("请输入值 "),e.checkType(t.key,"number")?a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?a("el-input",{model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"array")&&"Is_Main"===t.key?a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.Is_Main_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1):e._e()],1)]),a("span",{staticClass:"item-span"},0===n?[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),a("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(n)}}})])])})),a("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},r=[]},"29cd":function(e,t,a){"use strict";a.r(t);var n=a("1dbd"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"2a7f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeletePartType=c,t.GetConsumingProcessAllList=h,t.GetFactoryPartTypeIndentifySetting=d,t.GetPartTypeEntity=p,t.GetPartTypeList=l,t.GetPartTypePageList=i,t.GetPartTypeTree=m,t.SaveConsumingProcessAllList=g,t.SavePartType=u,t.SavePartTypeIdentifySetting=f,t.SettingDefault=s;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/PartType/GetPartTypePageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/PartType/GetPartTypeList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/PartType/SettingDefault",method:"post",data:o.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/PartType/SavePartType",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/PartType/DeletePartType",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/PartType/GetFactoryPartTypeIndentifySetting",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/PartType/SavePartTypeIdentifySetting",method:"post",data:e})}function m(e){return(0,r.default)({url:"/pro/parttype/GetPartTypeTree",method:"post",data:o.default.stringify(e)})}function p(e){return(0,r.default)({url:"/pro/parttype/GetPartTypeEntity",method:"post",data:o.default.stringify(e)})}function h(e){return(0,r.default)({url:"/PRO/PartType/GetConsumingProcessAllList",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/PartType/SaveConsumingProcessAllList",method:"post",data:e})}},"2c61":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCarNum=p,t.Delete=h,t.DeleteScanSteel=b,t.EditWithParts=i,t.GetCadUrlBySteelName=f,t.GetDetaileEntities=c,t.GetForgetScanWeight=u,t.GetNode=g,t.GetPageEntities=l,t.GetPageEntitiesForExproNew=m,t.GetPageStorageBySearch=y,t.GetPartPageList=v,t.GetProjectsNodeList=o,t.GetSteelHistory=d,t.GetTotalWeight=s,t.GetUserNodeList=_;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/PLM/Plm_Projects_Node/GetNodeList",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PLM/Trace/EditWithParts",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PLM/Trace/GetPageEntities",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PLM/Trace/GetTotalWeight",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PLM/Trace/GetForgetScanWeight",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PLM/Trace/GetDetaileEntities",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PLM/Trace/GetSteelHistory",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PLM/Trace/GetCadUrlBySteelName",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PLM/Trace/GetPageEntitiesForExproNew",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PLM/Trace/AddCarNum",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PLM/Trace/Delete",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PLM/Trace/GetNode",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PLM/Trace/DeleteScanSteel",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PLM/Component/GetPageStorageBySearch",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PLM/AppScan/GetUserNodeList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Part/GetPartPageList",method:"post",data:e})}},"2d44":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.getMonomerList},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"安装单元"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}},e._l(e.unitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},r=[]},"2e8a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=c,t.GetCompTypeTree=d,t.GetComponentTypeEntity=u,t.GetComponentTypeList=i,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=m,t.GetTypePageList=l,t.RestoreTemplateType=_,t.SavDeepenTemplateSetting=y,t.SaveCompTypeIdentifySetting=b,t.SaveComponentType=s,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=p;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:o.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:o.default.stringify(e)})}function f(e){return(0,r.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,r.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function b(e){return(0,r.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function y(e){return(0,r.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function _(e){return(0,r.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"2fcd":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.promptBox=void 0,a("99af");var r=n(a("c14f")),o=n(a("1da1")),i=a("5c96"),l="auth-files-msg-box",s="输入不正确",u="确认进行此操作吗？请在下方输入框内输入";t.promptBox=function(){var e=(0,o.default)((0,r.default)().m((function e(t){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return a=t.title,e.n=1,i.MessageBox.prompt('<div class="tips">'.concat(u,'”<span class="strong">').concat(a,"</span>“！</div>"),a,{confirmButtonText:"确定",cancelButtonText:"取消",customClass:l,dangerouslyUseHTMLString:!0,inputValidator:function(e){return e===a},inputErrorMessage:s});case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}()},3033:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("d3b7");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=n(a("0f97")),s=n(a("15ac")),u=a("a667");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{zParams:{type:Object,default:function(){}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbLoading:!1,tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},columns:[],total:0,tbData:[]}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_item_page_list,".concat(e.typeEntity.Code));case 1:a=t.v,e.$emit("getColumn",a.filter((function(e){return e.Is_Display}))||[]);case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){this.$emit("getList")},fetchRightData:function(e){var t=this;if(!e)return this.tbData=[],void(this.total=0);this.tbLoading=!0,(0,u.GetPageStorageSteelsBySearchItem)((0,r.default)((0,r.default)({Id:e},this.zParams),{},{PageInfo:(0,r.default)({},this.queryInfo)})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleSelect:function(e){this.$emit("selectList",e)}}}},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=c,t.GeAreaTrees=S,t.GetFileSync=D,t.GetInstallUnitIdNameList=C,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=I,t.GetProjectAreaTreeList=P,t.GetProjectEntity=s,t.GetProjectList=l,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=v,t.GetSchedulingPartList=T,t.IsEnableProjectMonomer=d,t.SaveProject=u,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=b,t.UpdateProjectTemplateContract=y,t.UpdateProjectTemplateOther=_;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function D(e){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"31b1":function(e,t,a){"use strict";a.r(t);var n=a("3895"),r=a("b2e8");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("270c");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"d9a81c76",null);t["default"]=l.exports},3228:function(e,t,a){"use strict";a.r(t);var n=a("da6db"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},3253:function(e,t,a){"use strict";a.r(t);var n=a("bf03"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},3258:function(e,t,a){"use strict";a("40a2")},"325d":function(e,t,a){"use strict";a.r(t);var n=a("0cdd"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"32c9":function(e,t,a){"use strict";a.r(t);var n=a("09a5"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},3300:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909"));a("caad"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("ddb0");var o=a("ed08"),i=a("586a");t.default={name:"TracePlot",props:{trackDrawerData:{type:Object,default:function(){return{}}}},data:function(){return{activities:[],one:{},two:{},three:[],isMoreSchdule:!1,isMoreSchduleStatus:0,loading:!0}},mounted:function(){this.getComponentProductionTrack()},methods:{getComponentProductionTrack:function(){var e=this;this.loading=!0,(0,i.GetTrackList)({PartAggregateId:this.trackDrawerData.Part_Aggregate_Id}).then((function(t){var a=t.Data,n=a.Deepen,o=a.Schduled,i=a.WorkProcessList;e.one=n,e.two=o||{Status:(null===o||void 0===o?void 0:o.Status)||1},e.isMoreSchdule=null===o||void 0===o?void 0:o.IsMultipleTeam,e.three=i;var l=(0,r.default)(new Set(i.map((function(e){return e.Status}))));e.isMoreSchduleStatus=l.includes(2)?2:l.includes(3)?3:1})).finally((function(){e.loading=!1}))},getIconOrColor:function(e,t){return"icon"===e?3===t?"el-icon-success":"el-icon-yuanxing":"iconColor"===e?2===t?"inProgress":3===t?"complete":"init":"color"===e?2===t?"blue1":3===t?"green":"":"bgIcon"===e?2===t?"cs-unComplete-icon":3===t?"cs-success-icon":"cs-init-icon":void 0},getTime:function(e){return(0,o.parseTime)(e,"{y}-{m}-{d} {h}:{i}:{s}")}}}},"33ab":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._v(" 111"+e._s(e.customParams)+" ")])},r=[]},"34f7":function(e,t,a){"use strict";a.r(t);var n=a("8a0d4"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"351d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=a("a667"),s=n(a("15ac")),u=n(a("0f97")),c=a("ed08"),d=a("21a6");t.default={components:{DynamicDataTable:u.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""}},data:function(){return{searchDate:[],tbLoading:!1,btnLoading:!1,tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:10,ParameterJson:[]},columns:[],total:0,tbData:[],selectArray:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]}}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return a=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),"2"===a&&(e.mode="factory"),t.n=1,e.getTableConfig("steels_import_file_page_list");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0;var t={PageInfo:(0,r.default)({},this.queryInfo)};this.searchDate&&this.searchDate.length>1?(t.HisStartDate=this.searchDate[0],t.HisEndDate=this.searchDate[1]):(t.HisStartDate="",t.HisEndDate=""),"factory"===this.mode&&(t.ProjectId=this.sysProjectId),(0,l.GetPageStorageHistorySteel)(t).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},multiSelectedChange:function(e){this.selectArray=e},onSubmit:function(){var e=this;this.btnLoading=!0;var t=this.selectArray.map((function(t){return/http/.test(t.FileUrl)?t.FileUrl:(0,c.combineURL)(e.$baseUrl,t.FileUrl)})).toString();(0,l.DownAllFiles)({urlStr:t,itemId:"历史清单导出"}).then((function(t){if("text/xml"===t.type)e.$message({message:"找不到文件",type:"error"});else{var a="历史清单导出",n=new Blob([t],{type:"application/zip;charset=utf-8"});(0,d.saveAs)(n,a)}e.btnLoading=!1}))}}}},3669:function(e,t,a){"use strict";a.r(t);var n=a("e612"),r=a("965b");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("c804");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"0d928533",null);t["default"]=l.exports},3895:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plmdialog plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose,close:e.handleClose}},[e.isDeep||e.isSHQD?a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1):e._e(),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[e.isCZD||e.isComp||e.isPart||e.isUnitPart?a("el-form-item",{attrs:{label:e.isPart?"关联零件":e.isComp?"关联构件":e.isUnitPart?"关联部件":"关联构件",prop:"Type_Name"}},[a("el-select",{staticStyle:{width:"360px"},attrs:{filterable:"",remote:"",multiple:"","reserve-keyword":"",loading:e.selectLoading,"remote-method":e.remoteMethod},on:{"visible-change":e.visibleChange,change:e.selectChange},model:{value:e.relatedItem,callback:function(t){e.relatedItem=t},expression:"relatedItem"}},e._l(e.compList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),a("el-form-item",{attrs:{label:"是否变更",prop:"IsChanged"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.IsChanged,callback:function(t){e.$set(e.form,"IsChanged",t)},expression:"form.IsChanged"}},[e._v("否")])],1),a("el-form-item",{attrs:{label:"是否大型图纸文件",prop:"Is_Big_File","label-width":"193px"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v(" 是否大型图纸文件 "),a("el-tooltip",{attrs:{content:"常规导入选“否”，当导入布置图时请选“是”",placement:"top"}},[a("i",{staticClass:"el-icon-question"})])],1),a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Big_File,callback:function(t){e.$set(e.form,"Is_Big_File",t)},expression:"form.Is_Big_File"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Big_File,callback:function(t){e.$set(e.form,"Is_Big_File",t)},expression:"form.Is_Big_File"}},[e._v("否")])],1),e.isVersionFour&&(e.isComp||e.isPart||e.isUnitPart)?a("el-form-item",{attrs:{label:"图纸命名匹配方式",prop:"Drawing_Match_Type","label-width":"180px"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.Drawing_Match_Type,callback:function(t){e.$set(e.form,"Drawing_Match_Type",t)},expression:"form.Drawing_Match_Type"}},e._l(e.drawingList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,disabled:e.disabled,value:e.value}})})),1)],1):e._e(),"新增文件"==e.title?a("el-form-item",{attrs:{label:"是否将重复文件移入历史文件库",prop:"ishistory","label-width":"260px"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.ishistory,callback:function(t){e.$set(e.form,"ishistory",t)},expression:"form.ishistory"}},[e._v("否")])],1):e._e(),e.BIMFiles?a("el-form-item",{attrs:{label:"是否加载",prop:"Is_Load"}},[a("el-radio",{attrs:{label:!0},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.form.Is_Load,callback:function(t){e.$set(e.form,"Is_Load",t)},expression:"form.Is_Load"}},[e._v("否")])],1):e._e(),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"companyUpload",staticClass:"upload-demo",attrs:{drag:"",data:{callback:!1},action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:e.isDeep||e.isSHQD||e.isEditSCYTH||"edit"===e.type?1:1e3,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:"",accept:e.allowFile,disabled:"edit"===e.type&&e.BIMFiles,"on-exceed":e.onExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("el-form-item",{attrs:{label:"是否通知"}},[a("el-radio",{attrs:{label:!0},on:{input:e.handleRadioChange},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("是")]),a("el-radio",{attrs:{label:!1},model:{value:e.isNotify,callback:function(t){e.isNotify=t},expression:"isNotify"}},[e._v("否")])],1),e.isNotify?[a("el-form-item",{attrs:{label:"通知标题",prop:"model.Title",rules:{required:!0,message:"通知标题不能为空",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.model.Title,callback:function(t){e.$set(e.form.model,"Title",t)},expression:"form.model.Title"}})],1),a("el-form-item",{attrs:{label:"通知人员",prop:"model.UserIds",rules:[{required:!0,message:"请选择通知人员",trigger:"change"}]}},[a("form-item",{attrs:{"project-id":e.projectId,type:"contacts",filterable:"",multiple:"",width:"360px"},model:{value:e.form.model.UserIds,callback:function(t){e.$set(e.form.model,"UserIds",t)},expression:"form.model.UserIds"}})],1),a("el-form-item",{attrs:{label:"通知内容"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{type:"textarea"},model:{value:e.form.model.Content,callback:function(t){e.$set(e.form.model,"Content",t)},expression:"form.model.Content"}})],1)]:e._e()],2),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},r=[]},"397a":function(e,t,a){},"3b00":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"加工工厂",prop:"Factory_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"构件批次"}},[a("el-input",{model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}})],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},r=[]},"3b7d8":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"加工工厂",prop:"Factory_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Factory_Id,callback:function(t){e.$set(e.form,"Factory_Id",t)},expression:"form.Factory_Id"}},e._l(e.factoryOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"构件批次"}},[a("el-input",{model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}})],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},r=[]},"3b8f":function(e,t,a){"use strict";a.r(t);var n=a("aa74");a.d(t,"version",(function(){return n["cb"]})),a.d(t,"dependencies",(function(){return n["l"]})),a.d(t,"PRIORITY",(function(){return n["g"]})),a.d(t,"init",(function(){return n["B"]})),a.d(t,"connect",(function(){return n["j"]})),a.d(t,"disconnect",(function(){return n["n"]})),a.d(t,"disConnect",(function(){return n["m"]})),a.d(t,"dispose",(function(){return n["o"]})),a.d(t,"getInstanceByDom",(function(){return n["w"]})),a.d(t,"getInstanceById",(function(){return n["x"]})),a.d(t,"registerTheme",(function(){return n["R"]})),a.d(t,"registerPreprocessor",(function(){return n["P"]})),a.d(t,"registerProcessor",(function(){return n["Q"]})),a.d(t,"registerPostInit",(function(){return n["N"]})),a.d(t,"registerPostUpdate",(function(){return n["O"]})),a.d(t,"registerUpdateLifecycle",(function(){return n["T"]})),a.d(t,"registerAction",(function(){return n["H"]})),a.d(t,"registerCoordinateSystem",(function(){return n["I"]})),a.d(t,"getCoordinateSystemDimensions",(function(){return n["v"]})),a.d(t,"registerLocale",(function(){return n["L"]})),a.d(t,"registerLayout",(function(){return n["J"]})),a.d(t,"registerVisual",(function(){return n["U"]})),a.d(t,"registerLoading",(function(){return n["K"]})),a.d(t,"setCanvasCreator",(function(){return n["V"]})),a.d(t,"registerMap",(function(){return n["M"]})),a.d(t,"getMap",(function(){return n["y"]})),a.d(t,"registerTransform",(function(){return n["S"]})),a.d(t,"dataTool",(function(){return n["k"]})),a.d(t,"zrender",(function(){return n["eb"]})),a.d(t,"matrix",(function(){return n["D"]})),a.d(t,"vector",(function(){return n["bb"]})),a.d(t,"zrUtil",(function(){return n["db"]})),a.d(t,"color",(function(){return n["i"]})),a.d(t,"throttle",(function(){return n["X"]})),a.d(t,"helper",(function(){return n["A"]})),a.d(t,"use",(function(){return n["Z"]})),a.d(t,"setPlatformAPI",(function(){return n["W"]})),a.d(t,"parseGeoJSON",(function(){return n["F"]})),a.d(t,"parseGeoJson",(function(){return n["G"]})),a.d(t,"number",(function(){return n["E"]})),a.d(t,"time",(function(){return n["Y"]})),a.d(t,"graphic",(function(){return n["z"]})),a.d(t,"format",(function(){return n["u"]})),a.d(t,"util",(function(){return n["ab"]})),a.d(t,"env",(function(){return n["p"]})),a.d(t,"List",(function(){return n["e"]})),a.d(t,"Model",(function(){return n["f"]})),a.d(t,"Axis",(function(){return n["a"]})),a.d(t,"ComponentModel",(function(){return n["c"]})),a.d(t,"ComponentView",(function(){return n["d"]})),a.d(t,"SeriesModel",(function(){return n["h"]})),a.d(t,"ChartView",(function(){return n["b"]})),a.d(t,"innerDrawElementOnCanvas",(function(){return n["C"]})),a.d(t,"extendComponentModel",(function(){return n["r"]})),a.d(t,"extendComponentView",(function(){return n["s"]})),a.d(t,"extendSeriesModel",(function(){return n["t"]})),a.d(t,"extendChartView",(function(){return n["q"]}))},"3c2f":function(e,t,a){"use strict";a.r(t);var n=a("e010"),r=a("13e8");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("708a9");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"4f31db66",null);t["default"]=l.exports},"3c97":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,"empty-text":"暂无数据",border:""}},[a("el-table-column",{attrs:{prop:"Type",label:"文件类型",align:"center"}}),a("el-table-column",{attrs:{prop:"Title",label:"文件名称",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleClick(t.row)}}},[e._v("查看")])]}}])})],1),a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{"text-align":"right","margin-top":"10px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")])],1)])],1)],1)},r=[]},"3df0":function(e,t,a){"use strict";a.r(t);var n=a("3b00"),r=a("3253");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("ea62");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"90da485a",null);t["default"]=l.exports},"3f77":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.isVersionFour?a("v4"):a("v3")],1)},r=[]},"3ff8":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getPropsName=t.getColumnConfiguration=t.convertCode=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var r=n(a("c14f")),o=n(a("1da1")),i=a("8899");t.getColumnConfiguration=function(){var e=(0,o.default)((0,r.default)().m((function e(t){var a,n,o=arguments;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return a=o.length>1&&void 0!==o[1]?o[1]:"plm_steels_page_list",e.n=1,(0,i.GetConfigTemplateList)({typeCode:"".concat(a,",").concat(t)});case 1:return n=e.v,e.a(2,n.Data.configItems)}}),e)})));return function(t){return e.apply(this,arguments)}}(),t.convertCode=function(){var e=(0,o.default)((0,r.default)().m((function e(t){var a=arguments;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return a.length>1&&void 0!==a[1]?a[1]:[],e.a(2,[])}}),e)})));return function(t){return e.apply(this,arguments)}}(),t.getPropsName=function(e,t){var a;return null===(a=e.find((function(e){return e.Code.toLowerCase()===t.toLowerCase()})))||void 0===a?void 0:a.Display_Name}},"40a2":function(e,t,a){},"40f4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909")),o=n(a("5530")),i=n(a("c14f")),l=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("a15b"),a("d81d"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("e9c4"),a("a9e3"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("5319"),a("5b81"),a("1276"),a("498a"),a("c7cd"),a("159b");var s=a("a667"),u=a("2c61"),c=a("6186"),d=a("fd31"),f=a("3166"),m=n(a("1463")),p=n(a("34e9")),h=n(a("cb19")),g=n(a("afa5")),b=n(a("6a37")),y=n(a("b4a4f")),_=n(a("7dde")),v=n(a("7981")),P=n(a("4fca1")),C=n(a("9526")),S=n(a("d29d")),I=n(a("59e73")),T=n(a("968b6")),D=n(a("b9cc")),k=n(a("add1")),L=n(a("a888")),w=n(a("333d")),x=a("8975"),O=n(a("6347")),N=n(a("0bb3")),R=n(a("f151")),G=a("2fcd"),j=a("ed08"),A=a("c685"),$=(a("0e9a"),a("2a7f")),M=(a("f4f2"),a("e144"),a("586a")),F=(a("c24f"),n(a("bae6"))),U=n(a("31b1")),E=n(a("a2ff")),B=a("7f9d"),W=n(a("d4ea")),z="$_$";t.default={name:"PROPartList",directives:{elDragDialog:L.default,sysUseType:R.default},components:{ExpandableSection:F.default,TreeDetail:m.default,TopHeader:p.default,comImport:h.default,comImportByFactory:b.default,BatchEdit:_.default,HistoryExport:y.default,GeneratePack:S.default,Edit:P.default,ComponentPack:v.default,OneClickGeneratePack:C.default,Pagination:w.default,bimdialog:N.default,ComponentsHistory:g.default,DeepMaterial:I.default,Schduling:T.default,comDrawdialog:U.default,TracePlot:E.default,PartSplit:D.default,modelDrawing:W.default,ProcessData:k.default},mixins:[O.default],data:function(){return{allStopFlag:!1,showExpand:!0,drawer:!1,drawersull:!1,iframeKey:"",fullscreenid:"",iframeUrl:"",fullbimid:"",expandedKey:"",tablePageSize:A.tablePageSize,partTypeOption:[],treeData:[],treeLoading:!0,projectName:"",statusType:"",searchHeight:0,tbData:[],total:0,tbLoading:!1,pgLoading:!1,countLoading:!1,queryInfo:{Page:1,PageSize:10,ParameterJson:[]},customPageSize:[10,20,50,100],installUnitIdNameList:[],nameMode:1,montageOption:[{value:!0,label:"是"},{value:!1,label:"否"}],customParams:{TypeId:"",Type_Name:"",Code:"",Code_Like:"",Spec:"",DateName:"",Texture:"",isMontage:null,InstallUnit_Id:[],Part_Type_Id:"",InstallUnit_Name:"",Sys_Project_Id:"",Project_Id:"",Area_Id:"",Project_Name:"",Area_Name:""},names:"",customDialogParams:{},dialogVisible:!1,currentComponent:"",selectList:[],factoryOption:[],projectList:[],typeOption:[],columns:[],columnsOption:[],title:"",width:"60%",tipLabel:"",monomerList:[],mode:"",isMonomer:!0,historyVisible:!1,sysUseType:void 0,deleteContent:!0,SteelAmountTotal:0,SchedulingNumTotal:0,SteelAllWeightTotal:0,SchedulingAllWeightTotal:0,FinishCountTotal:0,FinishWeightTotal:0,Unit:"",Proportion:0,command:"cover",currentLastLevel:!1,templateUrl:"",currentNode:{},comDrawData:{},trackDrawer:!1,trackDrawerTitle:"",trackDrawerData:{}}},computed:{showP9Btn:function(){return this.AuthButtons.buttons.some((function(e){return"p9BtnAdd"===e.Code}))},typeEntity:function(){var e=this;return this.typeOption.find((function(t){return t.Id===e.customParams.TypeId}))},PID:function(){var e,t=this;return null===(e=this.projectList.find((function(e){return e.Sys_Project_Id===t.customParams.Project_Id})))||void 0===e?void 0:e.Id},filterText:function(){return this.projectName+z+this.statusType}},watch:{"customParams.TypeId":function(e,t){t&&"0"!==t&&this.fetchData()},names:function(e,t){this.changeMode()},nameMode:function(e,t){this.changeMode()}},mounted:function(){this.pgLoading=!0,this.getPartWeightList(),this.getPartType(),this.searchHeight=this.$refs.searchDom.offsetHeight+327},created:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTypeList();case 1:return t.n=2,e.getTableConfig("plm_parts_page_list");case 2:e.fetchTreeData(),e.getFileType(),e.Keywords01Value;case 3:return t.a(2)}}),t)})))()},methods:{changeMode:function(){1===this.nameMode?(this.customParams.Code_Like=this.names,this.customParams.Code=""):(this.customParams.Code_Like="",this.customParams.Code=this.names.replace(/\s+/g,"\n"))},fetchTreeData:function(){var e=this;(0,f.GetProjectAreaTreeList)({MenuId:this.$route.meta.Id,Type:0,projectName:this.projectName}).then((function(t){if(0!==t.Data.length){var a=t.Data;a.map((function(e){0===e.Children.length?e.Is_Imported=!1:(e.Data.Is_Imported=e.Children.some((function(e){return!0===e.Data.Is_Imported})),e.Is_Directory=!0,e.Children.map((function(e){e.Children.length>0&&(e.Is_Directory=!0)})))})),e.treeData=a,0===Object.keys(e.currentNode).length?e.setKey():e.handleNodeClick(e.currentNode),e.treeLoading=!1}else e.treeLoading=!1}))},setKey:function(){var e=this,t=function(a){for(var n=0;n<a.length;n++){var r=a[n],o=r.Data,i=r.Children;return!o.ParentId||null!==i&&void 0!==i&&i.length?i&&i.length>0?t(i):void e.handleNodeClick(r):(e.currentNode=o,void e.handleNodeClick(r))}};return t(this.treeData)},handleNodeClick:function(e){this.handelsearch("reset",!1),this.currentNode=e,this.expandedKey=e.Id;var t,a="-1"===e.Id?"":e.Id;(e.ParentNodes?(this.customParams.Project_Id=e.Data.Project_Id,this.customParams.Area_Id=e.Id,this.customParams.Area_Name=e.Data.Name,this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id):(this.customParams.Project_Id=a,this.customParams.Area_Id="",this.customParams.Area_Name=e.Data.Name,this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id),this.currentLastLevel=!(!e.Data.Level||0!==e.Children.length),this.currentLastLevel)&&(this.customParams.Project_Name=null===(t=e.Data)||void 0===t?void 0:t.Project_Name,this.customParams.Area_Name=e.Label);this.queryInfo.Page=1,this.pgLoading=!0,this.fetchList(),this.getInstallUnitIdNameList(a,e),this.getPartWeightList()},getInstallUnitIdNameList:function(e,t){var a=this;""===e||t.Children.length>0?this.installUnitIdNameList=[]:(0,f.GetInstallUnitIdNameList)({Area_Id:e}).then((function(e){a.installUnitIdNameList=e.Data}))},getProcessData:function(){var e=this,t=JSON.parse(JSON.stringify(this.customParams)),a=t.InstallUnit_Id.join(",");delete t.InstallUnit_Id,this.width="40%",this.generateComponent("零件工序完成量","ProcessData"),this.$nextTick((function(n){e.$refs["content"].init(t,a,e.selectList.map((function(e){return e.Part_Aggregate_Id})).toString())}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,c.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.customParams.TypeId})).Code}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=r.ColumnList||[],l=i.sort((function(e,t){return e.Sort-t.Sort}));t.columns=l.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),e})),t.queryInfo.PageSize=+r.Grid.Row_Number||20,a(t.columns);var s=JSON.parse(JSON.stringify(t.columns));t.columnsOption=s.filter((function(e){return"操作时间"!==e.Display_Name&&"模型ID"!==e.Display_Name&&"深化资料"!==e.Display_Name&&"备注"!==e.Display_Name&&"排产数量"!==e.Display_Name&&-1===e.Code.indexOf("Attr")&&"批次"!==e.Display_Name}))}else t.$message({message:o,type:"error"})}))}))},fetchList:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a=JSON.parse(JSON.stringify(e.customParams)),n=a.InstallUnit_Id.join(","),delete a.InstallUnit_Id,t.n=1,(0,u.GetPartPageList)((0,o.default)((0,o.default)((0,o.default)({},e.queryInfo),a),{},{Code:a.Code.trim().replaceAll(" ","\n"),InstallUnit_Ids:n})).then((function(t){t.IsSucceed?(e.queryInfo.PageSize=t.Data.PageSize,e.total=t.Data.TotalCount,e.tbData=t.Data.Data.map((function(e){return e.Is_Main=e.Is_Main?"是":"否",e.Exdate=(0,x.timeFormat)(e.Exdate,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.selectList=[],e.getStopList()):e.$message({message:t.Message,type:"error"})})).finally((function(){e.tbLoading=!1,e.pgLoading=!1}));case 1:return t.a(2)}}),t)})))()},getStopList:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a=e.tbData.map((function(e){return{Id:e.Part_Aggregate_Id,Type:1}})),t.n=1,(0,B.GetStopList)(a).then((function(t){if(t.IsSucceed){var a={};t.Data.forEach((function(e){a[e.Id]=null!==e.Is_Stop})),e.tbData.forEach((function(t){a[t.Part_Aggregate_Id]&&e.$set(t,"stopFlag",a[t.Part_Aggregate_Id])}))}}));case 1:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_parts_page_list");case 1:e.tbLoading=!0,e.fetchList().then((function(t){e.tbLoading=!1}));case 2:return t.a(2)}}),t)})))()},changePage:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),e.fetchList().then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},getTbData:function(e){e.YearAllWeight,e.YearSteel;var t=e.CountInfo;this.tipLabel=t},getTypeList:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a,n,r,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a=null,n=null,t.n=1,(0,d.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.customParams.TypeId=null===(r=e.typeOption[0])||void 0===r?void 0:r.Id,e.customParams.Type_Name=null===(o=e.typeOption[0])||void 0===o?void 0:o.Name)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},handleDelete:function(){var e=this;this.$confirm("此操作将删除选择数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.Deletepart)({ids:e.selectList.map((function(e){return e.Part_Aggregate_Id})).toString()}).then((function(t){t.IsSucceed?(e.fetchData(),e.$message({message:"删除成功",type:"success"}),e.getPartWeightList(),e.fetchTreeData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(e){var t=this;this.width="45%",this.generateComponent("编辑零件","Edit"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},handleBatchEdit:function(){var e=this,t=this.selectList.filter((function(e){return null!=e.Schduling_Count&&e.Schduling_Count>0}));t.length>0?this.$message({type:"error",message:"选中行包含已排产的零件,编辑信息需要进行变更操作"}):(this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.selectList,e.columnsOption)})))},handleView:function(e){var t=this;this.width="45%",this.generateComponent("详情","Edit"),this.$nextTick((function(a){e.isReadOnly=!0,t.$refs["content"].init(e)}))},handleExport:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:a={Part_Aggregate_Ids:e.selectList.map((function(e){return e.Part_Aggregate_Id})).toString(),ProfessionalCode:e.typeEntity.Code},(0,s.ExportPlanpartInfo)(a).then((function(t){t.IsSucceed?window.open((0,j.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)}));case 1:return t.a(2)}}),t)})))()},partSplit:function(){var e=this,t=this.selectList[0].Type_Name,a=this.selectList[0].Schduling_Count;"直发件"===t||a>0?this.$message({type:"error",message:"零件已排产或是直发件,无法拆分"}):(this.width="45%",this.generateComponent("零件拆分","PartSplit"),this.$nextTick((function(t){e.$refs["content"].init(e.selectList)})))},modelListImport:function(){var e=this,t={Part_Aggregate_Ids:this.selectList.map((function(e){return e.Part_Aggregate_Id})).toString(),ProfessionalCode:this.typeEntity.Code};(0,s.ExportPlanpartcountInfo)(t).then((function(t){t.IsSucceed?(window.open((0,j.combineURL)(e.$baseUrl,t.Data),"_blank"),t.Message&&e.$alert(t.Message,"导出通知",{confirmButtonText:"我知道了"})):e.$message.error(t.Message)}))},handleCommand:function(e){this.command=e,this.deepListImport()},deepListImport:function(){var e={Catalog_Code:"PLMDeepenFiles",Code:this.typeEntity.Code,name:this.typeEntity.Name};this.$refs.dialog.handleOpen("add",e,null,!0,this.PID,this.command,this.customParams)},handleAllDelete:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!e.customParams.Project_Id){t.n=3;break}return t.n=1,(0,G.promptBox)({title:"删除"});case 1:return t.n=2,(0,s.DeletepartByfindkeywodes)((0,o.default)((0,o.default)({},e.customParams),e.queryInfo)).then((function(t){t.IsSucceed?(e.$message.success("删除成功"),e.fetchData(),e.fetchTreeData()):e.$message.error(t.Message)}));case 2:t.n=4;break;case 3:e.$message.warning("请先选择项目");case 4:return t.a(2)}}),t)})))()},handleClose:function(){this.dialogVisible=!1},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handelsearch:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.deleteContent=!1,e&&(this.$refs.customParams.resetFields(),this.deleteContent=!0,this.names=""),t&&this.fetchData(),this.getPartWeightList()},handleDeepMaterial:function(e){var t=this;this.width="45%",this.generateComponent("查看深化资料","DeepMaterial"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},handelSchduling:function(e){var t=this;this.width="45%",this.generateComponent("生产详情","Schduling"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},getPartWeightList:function(){var e=this;this.countLoading=!0;var t=JSON.parse(JSON.stringify(this.customParams)),a=t.InstallUnit_Id.join(",");delete t.InstallUnit_Id,(0,s.GetPartWeightList)((0,o.default)((0,o.default)((0,o.default)({},this.queryInfo),t),{},{InstallUnit_Ids:a})).then((function(t){t.IsSucceed?(e.SteelAmountTotal=Math.round(1e3*t.Data.DeepenNum)/1e3,e.SchedulingNumTotal=Math.round(1e3*t.Data.SchedulingNum)/1e3,e.SteelAllWeightTotal=Math.round(1e3*t.Data.DeepenWeight)/1e3,e.SchedulingAllWeightTotal=Math.round(1e3*t.Data.SchedulingWeight)/1e3,e.FinishCountTotal=Math.round(1e3*t.Data.Finish_Count)/1e3,e.FinishWeightTotal=Math.round(1e3*t.Data.Finish_Weight)/1e3):e.$message.error(t.Message),e.countLoading=!1}))},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.SteelAmountTotal=0,this.SchedulingNumTotal=0,this.SteelAllWeightTotal=0,this.SchedulingAllWeightTotal=0,this.FinishCountTotal=0,this.FinishWeightTotal=0;var a=0,n=0,r=0;this.selectList.length>0?(this.selectList.forEach((function(e){var o=null==e.Schduling_Count?0:e.Schduling_Count;t.SteelAmountTotal+=e.Num,t.SchedulingNumTotal+=Number(e.Schduling_Count),t.FinishCountTotal+=e.Finish_Count,a+=e.Total_Weight,n+=e.Weight*o,r+=e.Finish_Weight})),this.SteelAllWeightTotal=Math.round(a/this.Proportion*1e3)/1e3,this.SchedulingAllWeightTotal=Math.round(n/this.Proportion*1e3)/1e3,this.FinishWeightTotal=Math.round(r/this.Proportion*1e3)/1e3):this.getPartWeightList()},fetchTreeDataLocal:function(){},getPartInfo:function(e){var t=e.Drawing?e.Drawing.split(","):[],a=e.File_Url?e.File_Url.split(","):[];0!==a.length?(t.length>0&&a.length>0&&(this.drawingActive=t[0]),t.length>0&&a.length>0&&(this.drawingDataList=t.map((function(e,t){return{name:e,label:e,url:a[t]}}))),this.getPartInfoDrawing(e)):this.$message({message:"当前零件无图纸",type:"warning"})},getPartInfoDrawing:function(e){var t=this,a=e.Part_Aggregate_Id;(0,M.GetSteelCadAndBimId)({importDetailId:a}).then((function(a){if(a.IsSucceed){var n={extensionName:a.Data[0].ExtensionName,fileBim:a.Data[0].fileBim,IsUpload:a.Data[0].IsUpload,Code:e.Code,Sys_Project_Id:e.Sys_Project_Id};t.$refs.modelDrawingRef.dwgInit(n)}}))},customFilterFun:function(e,t,a){var n=e.split(z),o=n[0],i=n[1];if(!e)return!0;var l=a.parent,s=[a.label],u=[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"],c=1;while(c<a.level)s=[].concat((0,r.default)(s),[l.label]),u=[].concat((0,r.default)(u),[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"]),l=l.parent,c++;s=s.filter((function(e){return!!e})),u=u.filter((function(e){return!!e}));var d=!0,f=!0;return this.statusType&&(f=u.some((function(e){return-1!==e.indexOf(i)}))),this.projectName&&(d=s.some((function(e){return-1!==e.indexOf(o)}))),d&&f},getPartType:function(){var e=this;(0,$.GetPartTypeList)({}).then((function(t){t.IsSucceed?e.partTypeOption=t.Data.map((function(e){return{label:e.Name,value:e.Id}})):e.$message({message:t.Message,type:"error"})}))},getFileType:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a,n,r,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return n={catalogCode:"PLMDeepenFiles"},t.n=1,(0,c.GetFileType)(n);case 1:r=t.v,o=r.Data.find((function(e){return"零件详图"===e.Label})),e.comDrawData={isSHQD:!1,Id:o.Id,name:o.Label,Catalog_Code:o.Code,Code:null===(a=o.Data)||void 0===a?void 0:a.English_Name};case 2:return t.a(2)}}),t)})))()},handelImport:function(){this.$refs.comDrawdialogRef.handleOpen("add",this.comDrawData,"",!1,this.customParams.Sys_Project_Id,!1)},handleTrack:function(e){this.trackDrawer=!0,this.trackDrawerTitle=e.Code,this.trackDrawerData=e}}}},"41bd":function(e,t,a){"use strict";a("aa27")},"422f":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("2532"),a("159b");var r=n(a("c14f")),o=n(a("2909")),i=n(a("1da1")),l=a("a667"),s=a("e144"),u=(a("3ff8"),a("6186")),c=a("fd31");t.default={props:{typeEntity:{type:Object,default:function(){}},AreaId:{type:String,default:""},ProjectId:{type:String,default:""}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},value:"",options:[{key:"Spec",label:"规格",type:"string"},{key:"Length",label:"长度",type:"number"},{key:"Texture",label:"材质",type:"string"},{key:"Weight",label:"单重",type:"number"},{key:"Shape",label:"形状",type:"string"},{key:"Is_Main",label:"是否主零件",type:"array"},{key:"Times",label:"单数",type:"number"},{key:"Remark",label:"备注",type:"string"}],list:[{id:(0,s.v4)(),val:void 0,key:""}],Is_Main_Data:[{Name:"是",Id:!0},{Name:"否",Id:!1}]}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getUserableAttr();case 1:return a=e.options.filter((function(e,t){return t})).map((function(e){return e.key})),t.n=2,e.convertCode(e.typeEntity.Code,a,"plm_parts_page_list");case 2:n=t.v,e.options=e.options.map((function(e,t){var a;t&&(e.label=null===(a=n.filter((function(e){return e.Is_Display})).find((function(t){return t.Code===e.key})))||void 0===a?void 0:a.Display_Name);return e})),e.options=(0,o.default)(e.options);case 3:return t.a(2)}}),t)})))()},methods:{getUserableAttr:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetUserableAttr)({IsComponent:!1}).then((function(t){if(t.IsSucceed){var a=t.Data,n=[];a.forEach((function(e){var t={};t.key=e.Code,t.lable=e.Display_Name,t.type="string",n.push(t)})),e.options=e.options.concat(n)}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},init:function(e,t){this.selectList=e;var a=e.filter((function(e){return null!==e.Component_Code&&""!==e.Component_Code}));this.options=a.length>0?this.options.filter((function(e){return"Num"!=e.key})):this.options},handleAdd:function(){this.list.push({id:(0,s.v4)(),val:void 0,key:""})},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,o,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.btnLoading=!0,a=[],n=0;case 1:if(!(n<e.list.length)){t.n=4;break}if(o={},i=e.list[n],i.val){t.n=2;break}return("Length"===i.key||"Num"===i.key||"Weight"===i.key||"Times"===i.key)&&0===i.val?e.$message({message:"值不能为0",type:"warning"}):e.$message({message:"值不能为空",type:"warning"}),e.btnLoading=!1,t.a(2);case 2:o.code=i.key,o.value=i.val,a.push(o);case 3:n++,t.n=1;break;case 4:return t.n=5,(0,l.EditPartpagelist)({Ids:e.selectList.map((function(e){return e.Part_Aggregate_Id})).toString(),Keysmodel:a,Area_Id:e.AreaId,Project_Id:e.ProjectId}).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}));case 5:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(a){return(!t.list.map((function(e){return e.key})).includes(a.key)||a.key===e)&&a.label}))},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},getColumnConfiguration:function(e){var t=arguments;return(0,i.default)((0,r.default)().m((function a(){var n,o;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_parts_page_list",a.n=1,(0,u.GetGridByCode)({code:n+","+e});case 1:return o=a.v,a.a(2,o.Data.ColumnList)}}),a)})))()},convertCode:function(e){var t=arguments,a=this;return(0,i.default)((0,r.default)().m((function n(){var o,i,l,s;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return o=t.length>1&&void 0!==t[1]?t[1]:[],i=t.length>2?t[2]:void 0,n.n=1,a.getColumnConfiguration(e,i);case 1:return l=n.v,s=l.filter((function(e){var t=o.map((function(e){return e.toLowerCase()}));return t.includes(e.Code.toLowerCase())})),n.a(2,s)}}),n)})))()}}}},"42c3e":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("main",{staticClass:"main"},[a("div",{staticClass:"left"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{type:"primary",disabled:!e.leftSelectList.length},on:{click:e.handleEdit}},[e._v("编辑包信息")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("加入")]),a("el-button",{attrs:{type:"danger",disabled:!e.leftSelectList.length},on:{click:e.handleDeleteLeft}},[e._v("删除包")])]},proxy:!0},{key:"right",fn:function(){return[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"名称/包编号"},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.fetchLeft},slot:"append"})],1)]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[a("z-left",{ref:"left",attrs:{search:e.search,"type-entity":e.typeEntity},on:{leftClick:e.leftClick,selectList:e.getLeftList,setSelect:e.getLeftList,clearRightData:e.clearRightData}})],1)],1),a("div",{staticClass:"right"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{disabled:!e.rightSelectList.length,type:"danger"},on:{click:e.handleDeleteRight}},[e._v("删除")])]},proxy:!0},{key:"right",fn:function(){return[a("div",{staticStyle:{display:"flex"}},[a("div",[a("label",[e._v("查询条件 "),a("el-input",{staticClass:"input-with-select",staticStyle:{width:"300px"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.search2,callback:function(t){e.search2=t},expression:"search2"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{slot:"prepend",clearable:"",placeholder:"请选择"},slot:"prepend",model:{value:e.select,callback:function(t){e.select=t},expression:"select"}},e._l(e.columnOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1)]),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchRight}},[e._v("查询")])],1)])]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[e.typeEntity.Id?a("z-right",{ref:"right",attrs:{"z-params":{Fuzzy_Search_Col:e.select,Fuzzy_Search:e.search2},"type-entity":e.typeEntity},on:{getColumn:e.getColumn,selectList:e.getRightList,getList:e.fetchRight}}):e._e()],1)],1)]),e.dialogVisible?a("Dialog",{ref:"dialog",attrs:{"dialog-visible":e.dialogVisible},on:{"update:dialogVisible":function(t){e.dialogVisible=t},"update:dialog-visible":function(t){e.dialogVisible=t},refresh:e.fetchLeft,handleClose:e.handleClose}}):e._e()],1)},r=[]},4321:function(e,t,a){},4372:function(e,t,a){"use strict";a.r(t);var n=a("8c24"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"446e":function(e,t,a){"use strict";a.r(t);var n=a("27e5"),r=a("7826");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("9db3");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"be5283a8",null);t["default"]=l.exports},"44b0":function(e,t,a){"use strict";a.r(t);var n=a("42c3e"),r=a("694f");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("41bd");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"90823a3a",null);t["default"]=l.exports},4522:function(e,t,a){"use strict";a("0f9e")},4704:function(e,t,a){},"47e7b":function(e,t,a){"use strict";a.r(t);var n=a("d4ac"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},4951:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.promptBox=void 0,a("99af");var r=n(a("c14f")),o=n(a("1da1")),i=a("5c96"),l="auth-files-msg-box",s="输入不正确",u="确认进行此操作吗？请在下方输入框内输入";t.promptBox=function(){var e=(0,o.default)((0,r.default)().m((function e(t){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return a=t.title,e.n=1,i.MessageBox.prompt('<div class="tips">'.concat(u,'”<span class="strong">').concat(a,"</span>“！</div>"),a,{confirmButtonText:"确定",cancelButtonText:"取消",customClass:l,dangerouslyUseHTMLString:!0,inputValidator:function(e){return e===a},inputErrorMessage:s});case 1:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}()},"4bb9":function(e,t,a){"use strict";a.r(t);var n=a("2694"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"4bbf":function(e,t,a){"use strict";a("f397")},"4c94":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("main",{staticClass:"main"},[a("div",{staticClass:"left"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{type:"primary",disabled:!e.leftSelectList.length},on:{click:e.handleEdit}},[e._v("编辑包信息")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("加入")]),a("el-button",{attrs:{type:"danger",disabled:!e.leftSelectList.length},on:{click:e.handleDeleteLeft}},[e._v("删除包")])]},proxy:!0},{key:"right",fn:function(){return[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"名称/包编号"},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:e.fetchLeft},slot:"append"})],1)]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[a("z-left",{ref:"left",attrs:{search:e.search,"type-entity":e.typeEntity},on:{leftClick:e.leftClick,selectList:e.getLeftList,setSelect:e.getLeftList,clearRightData:e.clearRightData}})],1)],1),a("div",{staticClass:"right"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{disabled:!e.rightSelectList.length,type:"danger"},on:{click:e.handleDeleteRight}},[e._v("删除")])]},proxy:!0},{key:"right",fn:function(){return[a("div",{staticStyle:{display:"flex"}},[a("div",[a("label",[e._v("查询条件 "),a("el-input",{staticClass:"input-with-select",staticStyle:{width:"300px"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.search2,callback:function(t){e.search2=t},expression:"search2"}},[a("el-select",{staticStyle:{width:"120px"},attrs:{slot:"prepend",clearable:"",placeholder:"请选择"},slot:"prepend",model:{value:e.select,callback:function(t){e.select=t},expression:"select"}},e._l(e.columnOption,(function(e){return a("el-option",{key:e.Code,attrs:{label:e.Display_Name,value:e.Code}})})),1)],1)],1)]),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:e.fetchRight}},[e._v("查询")])],1)])]},proxy:!0}])}),a("div",{staticClass:"tb-container"},[e.typeEntity.Id?a("z-right",{ref:"right",attrs:{"z-params":{Fuzzy_Search_Col:e.select,Fuzzy_Search:e.search2},"type-entity":e.typeEntity},on:{getColumn:e.getColumn,selectList:e.getRightList,getList:e.fetchRight}}):e._e()],1)],1)]),e.dialogVisible?a("Dialog",{ref:"dialog",attrs:{"dialog-visible":e.dialogVisible},on:{"update:dialogVisible":function(t){e.dialogVisible=t},"update:dialog-visible":function(t){e.dialogVisible=t},refresh:e.fetchLeft,handleClose:e.handleClose}}):e._e()],1)},r=[]},"4e82":function(e,t,a){"use strict";var n=a("23e7"),r=a("e330"),o=a("59ed"),i=a("7b0b"),l=a("07fa"),s=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),p=a("99f4"),h=a("1212"),g=a("ea83"),b=[],y=r(b.sort),_=r(b.push),v=c((function(){b.sort(void 0)})),P=c((function(){b.sort(null)})),C=f("sort"),S=!c((function(){if(h)return h<70;if(!(m&&m>3)){if(p)return!0;if(g)return g<603;var e,t,a,n,r="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)b.push({k:t+n,v:a})}for(b.sort((function(e,t){return t.v-e.v})),n=0;n<b.length;n++)t=b[n].k.charAt(0),r.charAt(r.length-1)!==t&&(r+=t);return"DGBEFHACIJK"!==r}})),I=v||!P||!C||!S,T=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};n({target:"Array",proto:!0,forced:I},{sort:function(e){void 0!==e&&o(e);var t=i(this);if(S)return void 0===e?y(t):y(t,e);var a,n,r=[],u=l(t);for(n=0;n<u;n++)n in t&&_(r,t[n]);d(r,T(e)),a=l(r),n=0;while(n<a)t[n]=r[n++];while(n<u)s(t,n++);return t}})},"4fb1":function(e,t,a){"use strict";a.r(t);var n=a("ccb3"),r=a("5b12");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("9e58");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"07cf7baa",null);t["default"]=l.exports},"4fca1":function(e,t,a){"use strict";a.r(t);var n=a("e0cf"),r=a("1dfd");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("4bbf");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"006d1fbb",null);t["default"]=l.exports},5012:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.Add=v,t.AddSteel=P,t.AddSynNew=u,t.BatchAdd=_,t.BatchEdit=y,t.DeleteBatch=p,t.DeletePackage=I,t.DeleteSteel=T,t.Deletepart=M,t.DeletepartByfindkeywodes=G,t.DownAllFiles=m,t.EditPackage=S,t.EditPartpage=N,t.EditPartpagelist=R,t.ExportComponentList=f,t.ExportPlanpartInfo=j,t.ExportPlanpartcountInfo=A,t.GetAreaPageList=k,t.GetEntities=d,t.GetEntity=h,t.GetList=D,t.GetPageStorageBySearchPackages=i,t.GetPageStorageHistorySteel=s,t.GetPageStorageSteelsBySearchItem=l,t.GetPartEntity=O,t.GetPartWeightList=$,t.GetProfessEntities=b,t.GetSummaryStorageBySearch=L,t.PackageGetEntity=C,t.PartDeepeningImportTemplate=x,t.ProjectSchedule=w,t.SaveEntity=g,t.SteelBardcodeDataTemplate=c;var r=n(a("4328")),o=n(a("b775"));function i(e){return(0,o.default)({url:"/plm/package/GetPageStorageBySearchPackages",method:"post",data:e})}function l(e){return(0,o.default)({url:"/plm/package/GetPageStorageSteelsBySearchItem",method:"post",data:e})}function s(e){return(0,o.default)({url:"/plm/component/GetPageStorageHistorySteel",method:"post",data:e})}function u(e){return(0,o.default)({url:"/plm/Component/AddSynNew",method:"post",data:e,timeout:18e5})}function c(e){return(0,o.default)({url:"/plm/Component/SteelBardcodeDataTemplate",method:"post",data:e})}function d(e){return(0,o.default)({url:"/plm/plm_project_areas/GetEntities",method:"post",data:e})}function f(e){return(0,o.default)({url:"/plm/Component/ExportComponentList",method:"post",data:e})}function m(e){return(0,o.default)({url:"/plm/Component/DownAllFiles",method:"post",data:e,responseType:"blob"})}function p(e){return(0,o.default)({url:"/plm/Component/DeleteBatch",method:"post",data:e})}function h(e){return(0,o.default)({url:"/plm/Component/GetEntity",method:"post",data:e})}function g(e){return(0,o.default)({url:"/plm/Component/SaveEntity",method:"post",data:e})}function b(e){return(0,o.default)({url:"/plm/Plm_Professional_Type/GetEntities",method:"post",data:e})}function y(e){return(0,o.default)({url:"/plm/Component/BatchEdit",method:"post",data:r.default.stringify(e)})}function _(e){return(0,o.default)({url:"/plm/Package/BatchAdd",method:"post",data:e})}function v(e){return(0,o.default)({url:"/plm/Package/Add",method:"post",data:e})}function P(e){return(0,o.default)({url:"/plm/Package/AddSteel",method:"post",data:e})}function C(e){return(0,o.default)({url:"/plm/Package/GetEntity",method:"post",data:r.default.stringify(e)})}function S(e){return(0,o.default)({url:"/plm/Package/EditPackage",method:"post",data:e})}function I(e){return(0,o.default)({url:"/plm/Package/DeletePackage",method:"post",data:e})}function T(e){return(0,o.default)({url:"/plm/Package/DeleteSteel",method:"post",data:r.default.stringify(e)})}function D(e){return(0,o.default)({url:"/plm/Plm_Professional_Type/GetList",method:"post",data:e})}function k(e){return(0,o.default)({url:"/plm/Plm_Project_Areas/GetAreaPageList",method:"post",data:e})}function L(e){return(0,o.default)({url:"/PLM/Component/GetSummaryStorageBySearch",method:"post",data:e})}function w(e){return(0,o.default)({url:"/PLM/Plm_SteelsList/ProjectSchedule",method:"post",data:e})}function x(e){return(0,o.default)({url:"/PRO/Part/PartDeepeningImportTemplate",method:"post",data:e})}function O(e){return(0,o.default)({url:"/PRO/Part/GetPartEntity",method:"post",data:e,timeout:12e5})}function N(e){return(0,o.default)({url:"/PRO/Part/EditPartpage",method:"post",data:e})}function R(e){return(0,o.default)({url:"/PRO/Part/EditPartpagelist",method:"post",data:e})}function G(e){return(0,o.default)({url:"/PRO/Part/DeletepartByfindkeywodes",method:"post",data:e})}function j(e){return(0,o.default)({url:"/PRO/Part/ExportPlanpartInfo",method:"post",data:e})}function A(e){return(0,o.default)({url:"/PRO/Part/ExportPlanpartcountInfo",method:"post",data:e})}function $(e){return(0,o.default)({url:"/PRO/Part/GetPartWeightList",method:"post",data:e})}function M(e){return(0,o.default)({url:"/PRO/Part/Deletepart",method:"post",data:e})}},"50d9":function(e,t,a){"use strict";a("ecba")},5565:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909")),o=n(a("5530")),i=n(a("c14f")),l=n(a("1da1"));a("99af"),a("4de4"),a("7db0"),a("a15b"),a("d81d"),a("4e82"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732"),a("e9c4"),a("a9e3"),a("dca8"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("3ca3"),a("5319"),a("5b81"),a("1276"),a("498a"),a("c7cd"),a("159b"),a("ddb0");var s=a("a667"),u=a("2c61"),c=a("6186"),d=a("fd31"),f=a("3166"),m=n(a("1463")),p=n(a("34e9")),h=n(a("3df0")),g=n(a("55d3")),b=n(a("9dd2")),y=n(a("4fb1")),_=n(a("446e")),v=n(a("44b0")),P=n(a("8471")),C=n(a("99e5")),S=n(a("f38c")),I=n(a("23e0")),T=n(a("c2d4")),D=n(a("a888")),k=n(a("333d")),L=a("8975"),w=n(a("6347")),x=n(a("b90b")),O=n(a("f151")),N=a("4951"),R=a("ed08"),G=a("c685"),j=(a("0e9a"),a("2a7f")),A=(a("f4f2"),a("e144"),a("586a")),$=(a("c24f"),n(a("bae6"))),M=n(a("31b1")),F=n(a("e322")),U=n(a("d4ea")),E="$_$";t.default={name:"PROPartList",directives:{elDragDialog:D.default,sysUseType:O.default},components:{ExpandableSection:$.default,TreeDetail:m.default,TopHeader:p.default,comImport:h.default,comImportByFactory:b.default,BatchEdit:_.default,HistoryExport:y.default,GeneratePack:S.default,Edit:P.default,ComponentPack:v.default,OneClickGeneratePack:C.default,Pagination:k.default,bimdialog:x.default,ComponentsHistory:g.default,DeepMaterial:I.default,Schduling:T.default,comDrawdialog:M.default,TracePlot:F.default,modelDrawing:U.default},mixins:[w.default],data:function(){return{showExpand:!0,drawer:!1,drawersull:!1,iframeKey:"",fullscreenid:"",iframeUrl:"",fullbimid:"",expandedKey:"",tablePageSize:G.tablePageSize,partTypeOption:[],treeData:[],treeLoading:!0,projectName:"",statusType:"",searchHeight:0,tbData:[],total:0,tbLoading:!1,pgLoading:!1,queryInfo:{Page:1,PageSize:10,ParameterJson:[]},customPageSize:[10,20,50,100],installUnitIdNameList:[],nameMode:1,customParams:{TypeId:"",Type_Name:"",Code:"",Code_Like:"",Spec:"",DateName:"",Texture:"",InstallUnit_Id:[],Part_Type_Id:"",InstallUnit_Name:"",Sys_Project_Id:"",Project_Id:"",Area_Id:"",Project_Name:"",Area_Name:""},names:"",customDialogParams:{},dialogVisible:!1,currentComponent:"",selectList:[],factoryOption:[],projectList:[],typeOption:[],columns:[],columnsOption:[],title:"",width:"60%",tipLabel:"",monomerList:[],mode:"",isMonomer:!0,historyVisible:!1,sysUseType:void 0,deleteContent:!0,SteelAmountTotal:0,SchedulingNumTotal:0,SteelAllWeightTotal:0,SchedulingAllWeightTotal:0,FinishCountTotal:0,FinishWeightTotal:0,Unit:"",fileBim:"",Proportion:0,command:"cover",currentLastLevel:!1,templateUrl:"",cadRowCode:"",cadRowProjectId:"",IsUploadCad:!1,currentNode:{},comDrawData:{},trackDrawer:!1,trackDrawerTitle:"",trackDrawerData:{}}},computed:{showP9Btn:function(){return this.AuthButtons.buttons.some((function(e){return"p9BtnAdd"===e.Code}))},typeEntity:function(){var e=this;return this.typeOption.find((function(t){return t.Id===e.customParams.TypeId}))},PID:function(){var e,t=this;return null===(e=this.projectList.find((function(e){return e.Sys_Project_Id===t.customParams.Project_Id})))||void 0===e?void 0:e.Id},filterText:function(){return this.projectName+E+this.statusType}},watch:{"customParams.TypeId":function(e,t){t&&"0"!==t&&this.fetchData()},names:function(e,t){this.changeMode()},nameMode:function(e,t){this.changeMode()}},mounted:function(){this.pgLoading=!0,this.getPartWeightList(),this.getPartType(),this.searchHeight=this.$refs.searchDom.offsetHeight+327},created:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTypeList();case 1:e.fetchTreeData(),e.getFileType(),e.Keywords01Value;case 2:return t.a(2)}}),t)})))()},methods:{changeMode:function(){1===this.nameMode?(this.customParams.Code_Like=this.names,this.customParams.Code=""):(this.customParams.Code_Like="",this.customParams.Code=this.names.replace(/\s+/g,"\n"))},fetchTreeData:function(){var e=this;(0,f.GetProjectAreaTreeList)({Type:0,MenuId:this.$route.meta.Id,projectName:this.projectName}).then((function(t){if(0!==t.Data.length){var a=t.Data;a.map((function(e){0===e.Children.length?e.Is_Imported=!1:(e.Data.Is_Imported=e.Children.some((function(e){return!0===e.Data.Is_Imported})),e.Is_Directory=!0,e.Children.map((function(e){e.Children.length>0&&(e.Is_Directory=!0)})))})),e.treeData=a,0===Object.keys(e.currentNode).length?e.setKey():e.handleNodeClick(e.currentNode),e.treeLoading=!1}else e.treeLoading=!1}))},setKey:function(){var e=this,t=function(a){for(var n=0;n<a.length;n++){var r=a[n],o=r.Data,i=r.Children;return!o.ParentId||null!==i&&void 0!==i&&i.length?i&&i.length>0?t(i):void e.handleNodeClick(r):(e.currentNode=o,void e.handleNodeClick(r))}};return t(this.treeData)},handleNodeClick:function(e){this.handelsearch("reset",!1),this.currentNode=e,this.expandedKey=e.Id,this.customParams.InstallUnit_Id=[];var t,a="-1"===e.Id?"":e.Id;(e.ParentNodes?(this.customParams.Project_Id=e.Data.Project_Id,this.customParams.Area_Id=e.Id,this.customParams.Area_Name=e.Data.Name,this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id):(this.customParams.Project_Id=a,this.customParams.Area_Id="",this.customParams.Area_Name=e.Data.Name,this.customParams.Sys_Project_Id=e.Data.Sys_Project_Id),this.currentLastLevel=!(!e.Data.Level||0!==e.Children.length),this.currentLastLevel)&&(this.customParams.Project_Name=null===(t=e.Data)||void 0===t?void 0:t.Project_Name,this.customParams.Area_Name=e.Label);this.pgLoading=!0,this.fetchList(),this.getInstallUnitIdNameList(a,e),this.getPartWeightList()},getInstallUnitIdNameList:function(e,t){var a=this;""===e||t.Children.length>0?this.installUnitIdNameList=[]:(0,f.GetInstallUnitIdNameList)({Area_Id:e}).then((function(e){a.installUnitIdNameList=e.Data}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,c.GetGridByCode)({code:e+","+t.typeOption.find((function(e){return e.Id===t.customParams.TypeId})).Code}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbConfig=Object.assign({},t.tbConfig,r.Grid);var i=r.ColumnList||[],l=i.sort((function(e,t){return e.Sort-t.Sort}));t.columns=l.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),e})),t.queryInfo.PageSize=+r.Grid.Row_Number||20,a(t.columns);var s=JSON.parse(JSON.stringify(t.columns));t.columnsOption=s.filter((function(e){return"操作时间"!==e.Display_Name&&"模型ID"!==e.Display_Name&&"深化资料"!==e.Display_Name&&"备注"!==e.Display_Name&&"排产数量"!==e.Display_Name&&-1===e.Code.indexOf("Attr")&&"批次"!==e.Display_Name}))}else t.$message({message:o,type:"error"})}))}))},fetchList:function(){var e=this;return new Promise((function(t){var a=JSON.parse(JSON.stringify(e.customParams)),n=a.InstallUnit_Id.join(",");delete a.InstallUnit_Id,(0,u.GetPartPageList)((0,o.default)((0,o.default)((0,o.default)({},e.queryInfo),a),{},{Code:e.customParams.Code.trim().replaceAll(" ","\n"),InstallUnit_Ids:n})).then((function(a){a.IsSucceed?(e.queryInfo.PageSize=a.Data.PageSize,e.total=a.Data.TotalCount,e.tbData=a.Data.Data.map((function(e){return e.Is_Main=e.Is_Main?"是":"否",e.Exdate=(0,L.timeFormat)(e.Exdate,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.selectList=[]):e.$message({message:a.Message,type:"error"}),t()})).finally((function(){e.tbLoading=!1,e.pgLoading=!1}))}))},fetchData:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_parts_page_list");case 1:e.tbLoading=!0,Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 2:return t.a(2)}}),t)})))()},changePage:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},getTbData:function(e){e.YearAllWeight,e.YearSteel;var t=e.CountInfo;this.tipLabel=t},getTypeList:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a,n,r,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return a=null,n=null,t.n=1,(0,d.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.customParams.TypeId=null===(r=e.typeOption[0])||void 0===r?void 0:r.Id,e.customParams.Type_Name=null===(o=e.typeOption[0])||void 0===o?void 0:o.Name)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},handleDelete:function(){var e=this;this.$confirm("此操作将删除选择数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.Deletepart)({ids:e.selectList.map((function(e){return e.Part_Aggregate_Id})).toString()}).then((function(t){t.IsSucceed?(e.fetchData(),e.$message({message:"删除成功",type:"success"}),e.getPartWeightList(),e.fetchTreeData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(e){var t=this;this.width="45%",this.generateComponent("编辑零件","Edit"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},handleBatchEdit:function(){var e=this,t=this.selectList.filter((function(e){return null!=e.Schduling_Count&&e.Schduling_Count>0}));t.length>0?this.$message({type:"error",message:"选中行包含已排产的零件,编辑信息需要进行变更操作"}):(this.width="40%",this.generateComponent("批量编辑","BatchEdit"),this.$nextTick((function(t){e.$refs["content"].init(e.selectList,e.columnsOption)})))},handleView:function(e){var t=this;this.width="45%",this.generateComponent("详情","Edit"),this.$nextTick((function(a){e.isReadOnly=!0,t.$refs["content"].init(e)}))},handleExport:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:a={Part_Aggregate_Ids:e.selectList.map((function(e){return e.Part_Aggregate_Id})).toString(),ProfessionalCode:e.typeEntity.Code},(0,s.ExportPlanpartInfo)(a).then((function(t){t.IsSucceed?window.open((0,R.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)}));case 1:return t.a(2)}}),t)})))()},modelListImport:function(){var e=this,t={Part_Aggregate_Ids:this.selectList.map((function(e){return e.Part_Aggregate_Id})).toString(),ProfessionalCode:this.typeEntity.Code};(0,s.ExportPlanpartcountInfo)(t).then((function(t){t.IsSucceed?(window.open((0,R.combineURL)(e.$baseUrl,t.Data),"_blank"),t.Message&&e.$alert(t.Message,"导出通知",{confirmButtonText:"我知道了"})):e.$message.error(t.Message)}))},handleCommand:function(e){this.command=e,this.deepListImport()},deepListImport:function(){var e={Catalog_Code:"PLMDeepenFiles",Code:this.typeEntity.Code,name:this.typeEntity.Name};this.$refs.dialog.handleOpen("add",e,null,!0,this.PID,this.command,this.customParams)},handleAllDelete:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(!e.customParams.Project_Id){t.n=3;break}return t.n=1,(0,N.promptBox)({title:"删除"});case 1:return t.n=2,(0,s.DeletepartByfindkeywodes)((0,o.default)((0,o.default)({},e.customParams),e.queryInfo)).then((function(t){t.IsSucceed?(e.$message.success("删除成功"),e.fetchData(),e.fetchTreeData()):e.$message.error(t.Message)}));case 2:t.n=4;break;case 3:e.$message.warning("请先选择项目");case 4:return t.a(2)}}),t)})))()},handleClose:function(){this.dialogVisible=!1},generateComponent:function(e,t){this.title=e,this.currentComponent=t,this.dialogVisible=!0},handelsearch:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.deleteContent=!1,e&&(this.$refs.customParams.resetFields(),this.deleteContent=!0,this.names=""),t&&this.fetchData()},handleDeepMaterial:function(e){var t=this;this.width="45%",this.generateComponent("查看深化资料","DeepMaterial"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},handelSchduling:function(e){var t=this;this.width="45%",this.generateComponent("生产详情","Schduling"),this.$nextTick((function(a){e.isReadOnly=!1,t.$refs["content"].init(e)}))},getPartWeightList:function(){var e=this,t=JSON.parse(JSON.stringify(this.customParams)),a=t.InstallUnit_Id.join(",");delete t.InstallUnit_Id,(0,s.GetPartWeightList)((0,o.default)((0,o.default)((0,o.default)({},this.queryInfo),t),{},{InstallUnit_Ids:a})).then((function(t){t.IsSucceed?(e.SteelAmountTotal=Math.round(1e3*t.Data.DeepenNum)/1e3,e.SchedulingNumTotal=Math.round(1e3*t.Data.SchedulingNum)/1e3,e.SteelAllWeightTotal=Math.round(1e3*t.Data.DeepenWeight)/1e3,e.SchedulingAllWeightTotal=Math.round(1e3*t.Data.SchedulingWeight)/1e3,e.FinishCountTotal=Math.round(1e3*t.Data.Finish_Count)/1e3,e.FinishWeightTotal=Math.round(1e3*t.Data.Finish_Weight)/1e3):e.$message.error(t.Message)}))},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.SteelAmountTotal=0,this.SchedulingNumTotal=0,this.SteelAllWeightTotal=0,this.SchedulingAllWeightTotal=0,this.FinishCountTotal=0,this.FinishWeightTotal=0;var a=0,n=0,r=0;this.selectList.length>0?(this.selectList.forEach((function(e){var o=null==e.Schduling_Count?0:e.Schduling_Count;t.SteelAmountTotal+=e.Num,t.SchedulingNumTotal+=Number(e.Schduling_Count),t.FinishCountTotal+=e.Finish_Count,a+=e.Total_Weight,n+=e.Weight*o,r+=e.Finish_Weight})),this.SteelAllWeightTotal=Math.round(a/this.Proportion*1e3)/1e3,this.SchedulingAllWeightTotal=Math.round(n/this.Proportion*1e3)/1e3,this.FinishWeightTotal=Math.round(r/this.Proportion*1e3)/1e3):this.getPartWeightList()},fetchTreeDataLocal:function(){},getPartInfo:function(e){var t=e.Drawing?e.Drawing.split(","):[],a=e.File_Url?e.File_Url.split(","):[];0!==t.length?(t.length>0&&(this.drawingActive=t[0]),t.length>0&&a.length>0&&(this.drawingDataList=t.map((function(e,t){return{name:e,label:e,url:a[t]}}))),this.getPartInfoDrawing(e)):this.$message({message:"当前零件无图纸",type:"warning"})},getPartInfoDrawing:function(e){var t=this,a=e.Part_Aggregate_Id;(0,A.GetSteelCadAndBimId)({importDetailId:a}).then((function(a){if(a.IsSucceed){var n={extensionName:a.Data[0].ExtensionName,fileBim:a.Data[0].fileBim,IsUpload:a.Data[0].IsUpload,Code:e.Code,Sys_Project_Id:e.Sys_Project_Id};t.$refs.modelDrawingRef.dwgInit(n)}}))},customFilterFun:function(e,t,a){var n=e.split(E),o=n[0],i=n[1];if(!e)return!0;var l=a.parent,s=[a.label],u=[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"],c=1;while(c<a.level)s=[].concat((0,r.default)(s),[l.label]),u=[].concat((0,r.default)(u),[t.Data.Is_Deepen_Change?"已变更":t.Data.Is_Imported?"已导入":"未导入"]),l=l.parent,c++;s=s.filter((function(e){return!!e})),u=u.filter((function(e){return!!e}));var d=!0,f=!0;return this.statusType&&(f=u.some((function(e){return-1!==e.indexOf(i)}))),this.projectName&&(d=s.some((function(e){return-1!==e.indexOf(o)}))),d&&f},getPartType:function(){var e=this;(0,j.GetPartTypeList)({}).then((function(t){t.IsSucceed?e.partTypeOption=t.Data.map((function(e){return{label:e.Name,value:e.Id}})):e.$message({message:t.Message,type:"error"})}))},getFileType:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a,n,r,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return n={catalogCode:"PLMDeepenFiles"},t.n=1,(0,c.GetFileType)(n);case 1:r=t.v,o=r.Data.find((function(e){return"零件详图"===e.Label})),e.comDrawData={isSHQD:!1,Id:o.Id,name:o.Label,Catalog_Code:o.Code,Code:null===(a=o.Data)||void 0===a?void 0:a.English_Name};case 2:return t.a(2)}}),t)})))()},handelImport:function(){this.$refs.comDrawdialogRef.handleOpen("add",this.comDrawData,"",!1,this.customParams.Sys_Project_Id,!1)},handleTrack:function(e){this.trackDrawer=!0,this.trackDrawerTitle=e.Code,this.trackDrawerData=e}}}},"55d3":function(e,t,a){"use strict";a.r(t);var n=a("33ab"),r=a("47e7b");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"4ff225f3",null);t["default"]=l.exports},"56da":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-drawer",{attrs:{visible:e.drawer,direction:"btt",size:"60%","destroy-on-close":"","before-close":e.handleCloseDrawer},on:{"update:visible":function(t){e.drawer=t},opened:e.renderIframe}},["构件"===e.type?a("div",{staticStyle:{width:"100%",display:"flex","align-items":"center"}},[a("div",{staticStyle:{width:"50%",display:"flex","justify-content":"space-between","align-items":"center","padding-right":"30px"}},[a("div",{staticStyle:{"margin-left":"10px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v(e._s(e.extensionName?"构件模型":"构件图纸"))])]),e.fullscreenid?a("el-button",{on:{click:function(t){return e.fullscreen(0)}}},[e._v("全屏")]):e._e(),e.fullbimid?a("el-button",{on:{click:function(t){return e.fullscreen(1)}}},[e._v("全屏")]):e._e()],1),a("div",{staticStyle:{width:"50%"}},[e.extensionName?a("div",{staticStyle:{"margin-left":"10px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v("构件图纸")])]):e._e()])]):a("div",{staticStyle:{width:"100%",display:"flex"}},[a("div",{staticStyle:{"margin-left":"20px"}},[a("span",{staticStyle:{display:"inline-block",width:"100px"}},[e._v(e._s(e.type)+"图纸")])]),e.fileBim?a("el-button",{staticStyle:{"margin-left":"42%"},on:{click:function(t){return e.fullscreen(2)}}},[e._v("全屏")]):e._e()],1),a("iframe",{key:e.iframeKey,staticStyle:{width:"100%",border:"0px",margin:"0",height:"60vh"},attrs:{id:"frame",src:e.iframeUrl}})]),a("el-drawer",{attrs:{visible:e.drawersull,direction:"btt",size:"100%","destroy-on-close":""},on:{"update:visible":function(t){e.drawersull=t}}},[e.templateUrl?a("iframe",{staticStyle:{width:"96%","margin-left":"2%",height:"70vh","margin-top":"2%"},attrs:{id:"fullFrame",src:e.templateUrl,frameborder:"0"}}):e._e()])],1)},r=[]},5833:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"h100",staticStyle:{display:"flex"},attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff",attrs:{width:300},model:{value:e.showExpand,callback:function(t){e.showExpand=t},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[a("div",{staticClass:"tree-search"},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"导入状态选择"},model:{value:e.statusType,callback:function(t){e.statusType=t},expression:"statusType"}},[a("el-option",{attrs:{label:"已导入",value:"已导入"}}),a("el-option",{attrs:{label:"未导入",value:"未导入"}}),a("el-option",{attrs:{label:"已变更",value:"已变更"}})],1),a("el-input",{attrs:{placeholder:"关键词搜索",size:"small",clearable:"","suffix-icon":"el-icon-search"},on:{blur:e.fetchTreeDataLocal,clear:e.fetchTreeDataLocal},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fetchTreeDataLocal(t)}},model:{value:e.projectName,callback:function(t){e.projectName="string"===typeof t?t.trim():t},expression:"projectName"}})],1),a("el-divider",{staticClass:"cs-divider"}),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":e.customFilterFun,loading:e.treeLoading,"tree-data":e.treeData,"show-status":"","show-detail":"","filter-text":e.filterText,"expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick},scopedSlots:e._u([{key:"csLabel",fn:function(t){var n=t.showStatus,r=t.data;return[r.ParentNodes?e._e():a("span",{staticClass:"cs-blue"},[e._v("("+e._s(r.Code)+")")]),e._v(e._s(r.Label)+" "),n&&"全部"!=r.Label?[r.Data.Is_Deepen_Change?a("span",{staticClass:"cs-tag redBg"},[a("i",{staticClass:"fourRed"},[e._v("已变更")])]):a("span",{class:["cs-tag",1==r.Data.Is_Imported?"greenBg":"orangeBg"]},[a("i",{class:[1==r.Data.Is_Imported?"fourGreen":"fourOrange"]},[e._v(e._s(1==r.Data.Is_Imported?"已导入":"未导入"))])])]:e._e()]}}])})],1)],1)]),a("div",{staticClass:"cs-right",staticStyle:{"padding-right":"0"}},[a("div",{staticClass:"container"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",staticClass:"demo-form-inline",attrs:{model:e.customParams}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{"label-width":"80px",label:"零件名称",prop:"Names"}},[a("el-input",{staticClass:"input-with-select",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.names,callback:function(t){e.names=t},expression:"names"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.nameMode,callback:function(t){e.nameMode=t},expression:"nameMode"}},[a("el-option",{attrs:{label:"模糊搜索",value:1}}),a("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{"label-width":"80px",label:"零件种类",prop:"Part_Type_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.customParams.Part_Type_Id,callback:function(t){e.$set(e.customParams,"Part_Type_Id",t)},expression:"customParams.Part_Type_Id"}},e._l(e.partTypeOption,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{"label-width":"60px",label:"规格",prop:"Spec"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Spec,callback:function(t){e.$set(e.customParams,"Spec",t)},expression:"customParams.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{"label-width":"60px",label:"材质",prop:"Texture"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Texture,callback:function(t){e.$set(e.customParams,"Texture",t)},expression:"customParams.Texture"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{"label-width":"80px",label:"操作人",prop:"DateName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.DateName,callback:function(t){e.$set(e.customParams,"DateName",t)},expression:"customParams.DateName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"80px",label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",clearable:"",placeholder:"请选择",disabled:!Boolean(e.customParams.Area_Id)},model:{value:e.customParams.InstallUnit_Id,callback:function(t){e.$set(e.customParams,"InstallUnit_Id",t)},expression:"customParams.InstallUnit_Id"}},e._l(e.installUnitIdNameList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"20px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handelsearch()}}},[e._v("搜索 ")]),a("el-button",{on:{click:function(t){return e.handelsearch("reset")}}},[e._v("重置")])],1)],1)],1)],1)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},[[a("el-dropdown",{attrs:{trigger:"click",placement:"bottom-start"},on:{command:e.handleCommand}},[a("el-button",{attrs:{type:"primary",disabled:!e.currentLastLevel}},[e._v("零件导入 "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"cover"}},[e._v("覆盖导入")]),a("el-dropdown-item",{attrs:{command:"add"}},[e._v("新增导入")])],1)],1),a("el-button",{attrs:{disabled:!e.selectList.length},on:{click:e.modelListImport}},[e._v("导出零件排产单模板")]),a("el-button",{attrs:{disabled:!e.selectList.length},on:{click:e.handleExport}},[e._v("导出零件")]),a("el-button",{attrs:{disabled:!e.selectList.length,type:"primary",plain:""},on:{click:e.handleBatchEdit}},[e._v("批量编辑")]),a("el-button",{attrs:{type:"danger",plain:"",disabled:!e.selectList.length},on:{click:e.handleDelete}},[e._v("删除选中")]),a("el-button",{attrs:{type:"success",plain:"",disabled:!Boolean(e.customParams.Sys_Project_Id)},on:{click:e.handelImport}},[e._v("图纸导入 ")])]],2),a("div",{staticClass:"info-box"},[a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("深化总数")]),a("i",[e._v(e._s(e.SteelAmountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("深化总量")]),a("i",[e._v(e._s(e.SteelAllWeightTotal)+"t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("排产总数")]),a("i",[e._v(e._s(e.SchedulingNumTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("排产总量")]),a("i",[e._v(e._s(e.SchedulingAllWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("完成总数")]),a("i",[e._v(e._s(e.FinishCountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("完成总量")]),a("i",[e._v(e._s(e.FinishWeightTotal)+" t")])])])]),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,width:t.Width?t.Width:120},scopedSlots:e._u([{key:"default",fn:function(n){var r=n.row;return["Code"==t.Code?a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.getPartInfo(r)}}},[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])],1):"Deep_Material"==t.Code?a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDeepMaterial(r)}}},[e._v("查看")])],1):"Num"==t.Code&&r[t.Code]>0?a("div",[r[t.Code]?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+"件")]):a("span",[e._v("-")])]):"Schduling_Count"==t.Code&&r[t.Code]>0?a("div",[r[t.Code]?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handelSchduling(r)}}},[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+"件")]):e._e()],1):"Drawing"==t.Code?a("div",["暂无"!==r.Drawing?a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.getPartInfo(r)}}},[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]):a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]):a("div",[a("span",[e._v(e._s(r[t.Code]||"-"))])])]}}],null,!0)})})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"140","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("详情")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(n)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleTrack(n)}}},[e._v("轨迹图 ")])]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)])])])],1),a("div",{staticClass:"card"}),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":e.selectList,"custom-params":e.customDialogParams,"type-id":e.customParams.TypeId,"type-entity":e.typeEntity,"project-id":e.customParams.Project_Id,"sys-project-id":e.customParams.Project_Id},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e(),a("bimdialog",{ref:"dialog",attrs:{"type-entity":e.typeEntity,"area-id":e.customParams.Area_Id,"project-id":e.customParams.Project_Id},on:{getData:e.fetchData,getTreeData:e.fetchTreeData}}),a("el-drawer",{attrs:{visible:e.drawersull,direction:"btt",size:"100%","destroy-on-close":""},on:{"update:visible":function(t){e.drawersull=t}}},[e.templateUrl?a("iframe",{staticStyle:{width:"96%","margin-left":"2%",height:"70vh","margin-top":"2%"},attrs:{id:"fullFrame",src:e.templateUrl,frameborder:"0"}}):e._e()]),a("el-drawer",{attrs:{visible:e.trackDrawer,direction:"rtl",size:"30%","destroy-on-close":"","custom-class":"trackDrawerClass"},on:{"update:visible":function(t){e.trackDrawer=t}},scopedSlots:e._u([{key:"title",fn:function(){return[a("div",[a("span",[e._v(e._s(e.trackDrawerTitle))]),a("span",{staticStyle:{"margin-left":"24px"}},[e._v(e._s(e.trackDrawerData.Num))])])]},proxy:!0}])},[a("TracePlot",{attrs:{"track-drawer-data":e.trackDrawerData}})],1),a("comDrawdialog",{ref:"comDrawdialogRef",on:{getData:e.fetchData}}),a("modelDrawing",{ref:"modelDrawingRef",attrs:{type:"零件"}})],1)},r=[]},"586a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddDelayed=G,t.AdjustComponentNum=S,t.AppendImportDeepFile=U,t.AppendImportDeepFiles=E,t.BatchUpateCompProperty=A,t.BatchUpdateComponentImportInfo=ne,t.BatchUpdateComponentProcessInfo=rt,t.BatchUpdatePartProcessInfo=pt,t.BatchUpdateUnitProcessInfo=ut,t.CancelScheduling=i,t.CancelSchedulingBase=l,t.CommitScheduling=g,t.CommitSchedulingBase=b,t.ComponentImportTemplate=$,t.DelDelayed=j,t.DeleteAllComponentWithQuery=z,t.DeleteComponents=W,t.DownAllFiles=V,t.EntityComponentQueryInfo=fe,t.ExportComponentInfo=ee,t.ExportComponentModelInfo=y,t.ExportComponentModelInfoByInstall=_,t.ExportComponentProcessInfo=lt,t.ExportComponentSchedulingInfo=te,t.ExportComponentSendOnTimeData=ue,t.ExportComponentTypeStock=we,t.ExportDeepenFullSchedulingInfo=nt,t.ExportFactoryOutputPageList=Oe,t.ExportFactorySchedulingPlan=Ge,t.ExportFactoryTeamYieldForDay=Ae,t.ExportGetFactoryTeamYieldMonth=Me,t.ExportOutputAblityList=Ue,t.ExportPartProcessInfo=bt,t.ExportProducingKittingPageList=ie,t.ExportProjectProgress=De,t.ExportProjectTraceCount=ge,t.ExportSupplyPlan=Be,t.ExportThreeBom=qe,t.ExportToScheduleComponentInfo=f,t.ExportUnitProcessInfo=ft,t.FactoryOutput=Ve,t.FactoryOutputPageList=xe,t.GenerateDeepenFileFromDirect=ze,t.GenerateDirectComponent=at,t.GetComponentDeepenFileList=H,t.GetComponentEntityWithUniqueCode=L,t.GetComponentImportDetailPageList=B,t.GetComponentImportEntity=J,t.GetComponentLeadTime=T,t.GetComponentListByStatus=Ce,t.GetComponentLogList=k,t.GetComponentModelList=Y,t.GetComponentProcessSummaryInfo=st,t.GetComponentProductionDetailPageList=me,t.GetComponentProductionSummaryInfo=pe,t.GetComponentProductionTrack=et,t.GetComponentQueryInfo=ce,t.GetComponentQueryPageInfo=le,t.GetComponentSendOnTimeRageList=se,t.GetComponentStockReport=de,t.GetComponentStockStaticsListJson=D,t.GetComponentSummaryInfo=X,t.GetComponentTypeStockPageList=Le,t.GetComponentYieldByStatus=Te,t.GetDelayed=R,t.GetFactorySchdulingDayPlanYield=be,t.GetFactorySchedulingPlanPageList=Ne,t.GetFactorySchedulingPlanSummaryInfo=Re,t.GetFactoryTeamYieldForDay=je,t.GetFactoryTeamYieldForMonth=$e,t.GetImportHistoryPageList=q,t.GetInstallUnitCompPageList=N,t.GetPartDeepenFileList=Z,t.GetPartListWithComponent=Q,t.GetPartProcessWeightList=yt,t.GetProduceTraceCount=ye,t.GetProducingKittingPageList=oe,t.GetProjectAreaProgressList=ke,t.GetProjectAreaProgressSummary=Ie,t.GetProjectLifeCycleProgress=Se,t.GetProjectProgressList=Pe,t.GetProjectTraceCount=he,t.GetSchedulingComponentByInstallPageList=O,t.GetSchedulingComponentInstallPageList=w,t.GetSchedulingComponentPageList=m,t.GetSchedulingList=re,t.GetSchedulingProcessingCompList=_e,t.GetSchedulingProcessingPartList=ve,t.GetSteelCadAndBimId=We,t.GetSupplyPlanPageList=Ee,t.GetTrackList=tt,t.GetUnPreparedList=_t,t.GetUnitProcessWeightList=mt,t.GetWorkTeamOutput=Ke,t.GetWorkTeamOutputV3=Ze,t.GetWorkingProcedureSummary=p,t.ImportComponentExtendInfo=Qe,t.ImportComponentModelInfo=ae,t.ImportComponentWithModelInfo=P,t.ImportDeepFile=F,t.ImportModelInfo=v,t.ImportPartTechnologyFile=gt,t.ImportSchedulingInfo=c,t.ImportSchedulingInfoWithAlloct=d,t.ImportSteelTechnologyFile=it,t.ImportUnitTechnologyFile=dt,t.IsImportedComponentsWithoutModel=C,t.MonthlyFactoyOutput=He,t.OutputAblityList=Fe,t.PartTechnologyImportTemplate=ht,t.ProcessOutput=Je,t.ProductionDataTemplate=u,t.ProductionModelDataTemplate=I,t.ScheduleComponentAndAllocation=x,t.ScheduleComponentProductionDate=s,t.SteelTechnologyImportTemplate=ot,t.ThreeBomImportTemplate=M,t.UnitTechnologyImportTemplate=ct,t.UpdateComponentPrinted=h,t.UpdatePartAggregateId=Xe,t.UpdateSingleComponentImportInfo=K,t.YearlyFactoryOutput=Ye;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/Component/CancelScheduling",method:"post",data:o.default.stringify(e)})}function l(e){return(0,r.default)({url:"/PRO/Component/CancelSchedulingBase",method:"post",data:o.default.stringify(e)})}function s(e){return(0,r.default)({url:"/PRO/Component/ScheduleComponentProductionDate",method:"post",data:o.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/Component/ProductionDataTemplate",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Component/ImportSchedulingInfo",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Component/ImportSchedulingInfoWithAlloct",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Component/ExportToScheduleComponentInfo",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Component/GetSchedulingComponentPageList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Component/GetWorkingProcedureSummary",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Component/UpdateComponentPrinted",method:"post",data:o.default.stringify(e)})}function g(e){return(0,r.default)({url:"/PRO/Component/CommitScheduling",method:"post",data:o.default.stringify(e)})}function b(e){return(0,r.default)({url:"/PRO/Component/CommitSchedulingBase",method:"post",data:o.default.stringify(e)})}function y(e){return(0,r.default)({url:"/PRO/Component/ExportComponentModelInfo",method:"post",data:o.default.stringify(e)})}function _(e){return(0,r.default)({url:"/PRO/Component/ExportComponentModelInfoByInstall",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Component/ImportModelInfo",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Component/ImportComponentWithModelInfo",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Component/IsImportedComponentsWithoutModel",method:"post",data:o.default.stringify(e)})}function S(e){return(0,r.default)({url:"/PRO/Component/AdjustComponentNum",method:"post",data:o.default.stringify(e)})}function I(){return(0,r.default)({url:"/PRO/Component/ProductionModelDataTemplate",method:"post"})}function T(e){return(0,r.default)({url:"/PRO/Component/GetComponentLeadTime",method:"post",data:o.default.stringify(e)})}function D(e){return(0,r.default)({url:"/PRO/Component/GetComponentStockStaticsListJson",method:"post",data:o.default.stringify(e)})}function k(e){return(0,r.default)({url:"/PRO/Component/GetComponentLogList",method:"post",data:o.default.stringify(e)})}function L(e){return(0,r.default)({url:"/PRO/Component/GetComponentEntityWithUniqueCode",method:"post",data:o.default.stringify(e)})}function w(e){return(0,r.default)({url:"/PRO/Component/GetSchedulingComponentInstallPageList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/Component/ScheduleComponentAndAllocation",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Component/GetSchedulingComponentByInstallPageList",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/Component/GetInstallUnitCompPageList",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/Component/GetDelayed",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/Component/AddDelayed",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Component/DelDelayed",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/Component/BatchUpateCompProperty",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/Component/ComponentImportTemplate",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/Component/ThreeBomImportTemplate",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/Component/ImportDeepFile",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/Component/AppendImportDeepFile",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/Component/AppendImportDeepFiles",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/Component/GetComponentImportDetailPageList",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/Component/DeleteComponents",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/Component/DeleteAllComponentWithQuery",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/Component/GetImportHistoryPageList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/Component/DownAllFiles",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/Component/GetComponentModelList",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/Component/GetComponentDeepenFileList",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/Component/GetComponentImportEntity",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/Component/UpdateSingleComponentImportInfo",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/Component/GetPartListWithComponent",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/Component/GetComponentSummaryInfo",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/Component/ExportComponentInfo",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/Component/ExportComponentSchedulingInfo",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Component/ImportComponentModelInfo",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Component/BatchUpdateComponentImportInfo",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Component/GetSchedulingList",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Component/GetProducingKittingPageList",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Component/ExportProducingKittingPageList",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Component/GetComponentQueryPageInfo",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/Component/GetComponentSendOnTimeRageList",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/Component/ExportComponentSendOnTimeData",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PRO/Component/GetComponentQueryInfo",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/Component/GetComponentStockReport",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/Component/EntityComponentQueryInfo",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/Component/GetComponentProductionDetailPageList",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/Component/GetComponentProductionSummaryInfo",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/Component/GetProjectTraceCount",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/Component/ExportProjectTraceCount",method:"post",data:e})}function be(e){return(0,r.default)({url:"/PRO/Component/GetFactorySchdulingDayPlanYield",method:"post",data:e})}function ye(e){return(0,r.default)({url:"/PRO/Component/GetProduceTraceCount",method:"post",data:e})}function _e(e){return(0,r.default)({url:"/PRO/Component/GetSchedulingProcessingCompList",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PRO/Component/GetSchedulingProcessingPartList",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectProgressList",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentListByStatus",method:"post",data:e})}function Se(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectLifeCycleProgress",method:"post",data:e})}function Ie(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressSummary",method:"post",data:e})}function Te(e){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentYieldByStatus",method:"post",data:e})}function De(e){return(0,r.default)({url:"/PRO/ProductionCount/ExportProjectProgress",method:"post",data:e})}function ke(e){return(0,r.default)({url:"/PRO/ProductionCount/GetProjectAreaProgressList",method:"post",data:e})}function Le(e){return(0,r.default)({url:"/PRO/ProductionCount/GetComponentTypeStockPageList",method:"post",data:e})}function we(e){return(0,r.default)({url:"/PRO/ProductionCount/ExportComponentTypeStock",method:"post",data:e})}function xe(e){return(0,r.default)({url:"/PRO/ProductionTask/FactoryOutputPageList",method:"post",data:e})}function Oe(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportFactoryOutputPageList",method:"post",data:e})}function Ne(e){return(0,r.default)({url:"/PRO/ProductionTask/GetFactorySchedulingPlanPageList",method:"post",data:e})}function Re(e){return(0,r.default)({url:"/PRO/ProductionTask/GetFactorySchedulingPlanSummaryInfo",method:"post",data:e})}function Ge(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportFactorySchedulingPlanPageList",method:"post",data:e})}function je(e){return(0,r.default)({url:"/PRO/ProductionTask/GetFactoryTeamYieldForDay",method:"post",data:e})}function Ae(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportGetFactoryTeamYieldForDay",method:"post",data:e})}function $e(e){return(0,r.default)({url:"/PRO/ProductionTask/GetFactoryTeamYieldForMonth",method:"post",data:e})}function Me(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportGetFactoryTeamYieldForMonth",method:"post",data:e})}function Fe(e){return(0,r.default)({url:"/PRO/ProductionTask/OutputAblityList",method:"post",data:e})}function Ue(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportOutputAblityList",method:"post",data:e})}function Ee(e){return(0,r.default)({url:"/Pro/IntegrationSupplyPlan/GetSupplyPlanPageList",method:"post",data:e})}function Be(e){return(0,r.default)({url:"/Pro/IntegrationSupplyPlan/ExportSupplyPlan",method:"post",data:e})}function We(e){return(0,r.default)({url:"/PRO/Component/GetSteelCadAndBimId",method:"post",data:e})}function ze(e){return(0,r.default)({url:"/PRO/Component/GenerateDeepenFileFromDirect",method:"post",data:e})}function qe(e){return(0,r.default)({url:"/PRO/Component/ExportThreeBom",method:"post",data:e})}function Ve(e){return(0,r.default)({url:"/PRO/ProductionCount/FactoryOutput",method:"post",data:e})}function Ye(e){return(0,r.default)({url:"/PRO/ProductionCount/YearlyFactoryOutput",method:"post",data:e})}function He(e){return(0,r.default)({url:"/PRO/ProductionCount/MonthlyFactoyOutput",method:"post",data:e})}function Je(e){return(0,r.default)({url:"/PRO/ProductionCount/ProcessOutput",method:"post",data:e})}function Ke(e){return(0,r.default)({url:"/PRO/ProductionCount/GetWorkTeamOutput",method:"post",data:e})}function Qe(e){return(0,r.default)({url:"/PRO/component/ImportComponentExtendInfo",method:"post",data:e})}function Ze(e){return(0,r.default)({url:"/PRO/ProductionCount/GetWorkTeamOutputV3",method:"post",data:e})}function Xe(e){return(0,r.default)({url:"/PRO/Part/UpdatePartAggregateId",method:"post",data:e})}function et(e){return(0,r.default)({url:"/PRO/ProductionReport/GetComponentProductionTrack",method:"post",data:e})}function tt(e){return(0,r.default)({url:"/PRO/Part/GetTrackList",method:"post",data:e})}function at(e){return(0,r.default)({url:"/PRO/change/GenerateDirectComponent",method:"post",data:e})}function nt(e){return(0,r.default)({url:"/PRO/component/ExportDeepenFullSchedulingInfo",method:"post",data:e})}function rt(e){return(0,r.default)({url:"/PRO/Component/BatchUpdateComponentProcessInfo",method:"post",data:e})}function ot(e){return(0,r.default)({url:"/PRO/Component/SteelTechnologyImportTemplate",method:"post",data:e})}function it(e){return(0,r.default)({url:"/PRO/Component/ImportSteelTechnologyFile",method:"post",data:e})}function lt(e){return(0,r.default)({url:"/PRO/Component/ExportComponentProcessInfo",method:"post",data:e})}function st(e){return(0,r.default)({url:"/PRO/Component/GetComponentProcessSummaryInfo",method:"post",data:e})}function ut(e){return(0,r.default)({url:"/PRO/Unit/BatchUpdateUnitProcessInfo",method:"post",data:e})}function ct(e){return(0,r.default)({url:"/PRO/Unit/UnitTechnologyImportTemplate",method:"post",data:e})}function dt(e){return(0,r.default)({url:"/PRO/Unit/ImportUnitTechnologyFile",method:"post",data:e})}function ft(e){return(0,r.default)({url:"/PRO/Unit/ExportUnitProcessInfo",method:"post",data:e})}function mt(e){return(0,r.default)({url:"/PRO/Unit/GetUnitProcessWeightList",method:"post",data:e})}function pt(e){return(0,r.default)({url:"/PRO/Part/BatchUpdatePartProcessInfo",method:"post",data:e})}function ht(e){return(0,r.default)({url:"/PRO/Part/PartTechnologyImportTemplate",method:"post",data:e})}function gt(e){return(0,r.default)({url:"/PRO/Part/ImportPartTechnologyFile",method:"post",data:e})}function bt(e){return(0,r.default)({url:"/PRO/Part/ExportPartProcessInfo",method:"post",data:e})}function yt(e){return(0,r.default)({url:"/PRO/Part/GetPartProcessWeightList",method:"post",data:e})}function _t(e){return(0,r.default)({url:"/PRO/Component/GetUnPreparedList",method:"post",data:e})}},5917:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"custom-tb cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,"pager-count":5,"small-pagination":"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,handleRowClick:e.handleRowClick,select:e.handleSelect,selectAll:e.selectAll,tableSearch:e.tableSearch}})],1)},r=[]},"59e73":function(e,t,a){"use strict";a.r(t);var n=a("3c97"),r=a("c596");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"0abc8854",null);t["default"]=l.exports},"5a04":function(e,t,a){"use strict";a.r(t);var n=a("a3e4"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"5b12":function(e,t,a){"use strict";a.r(t);var n=a("1298"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"60e2":function(e,t,a){},"61d6":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var r=n(a("ade3")),o=n(a("5530")),i=n(a("c14f")),l=n(a("1da1")),s=a("a667"),u=a("6186"),c=(a("2e8a"),a("3166")),d=a("f2f6");t.default={props:{typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{isReadOnly:!1,btnLoading:!1,form:{SteelUnique:"",Code:"",Spec:"",Length:"",Texture:"",Num:"",Schduling_Count:"",Shape:"",Weight:"",Total_Weight:"",Type_Name:"",Project_Name:"",Project_Id:"",InstallUnit_Id:"",InstallUnit_Name:"",Is_Main:"",Component_Code:"",DateName:"",Exdate:"",Remark:"",Area_Id:"",Area_Name:"",Times:0},extendform:{},Pro_part_extend:{},Is_Main_Data:[{Name:"是",Id:!0},{Name:"否",Id:!1}],ProjectNameData:[],SetupPositionData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},extendField:[],factoryOption:[],TypeOption:[],rules:{Code:{required:!0,message:"请输入",trigger:"blur"},Spec:{required:!0,message:"请输入",trigger:"blur"},Weight:{required:!0,message:"请输入",trigger:"blur"},Total_Weight:{required:!0,message:"请输入",trigger:"blur"},Length:{required:!0,message:"请选择",trigger:"change"},Num:{required:!0,message:"请选择",trigger:"change"},Texture:{required:!0,message:"请选择",trigger:"change"}},show:!1}},mounted:function(){this.getFormProps(),this.getProjectOption()},methods:(0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)({getFormProps:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getColumnConfiguration(e.typeEntity.Code);case 1:a=t.v,e.propsList=a,e.show=!0;case 2:return t.a(2)}}),t)})))()},getLabel:function(e){var t=this.getPropsName(e);if(!t)try{this.rules[e].required=!1}catch(a){}return t},getProjectOption:function(){var e=this;(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},init:function(e){var t=this;this.isReadOnly=e.isReadOnly,(0,s.GetPartEntity)({id:e.Part_Aggregate_Id}).then((function(a){if(a.IsSucceed){t.form=a.Data.Pro_lan_art,t.extendField=a.Data.ImportExtend;var n=a.Data.ImportExtend;t.Pro_part_extend=a.Data.Pro_part_extend,t.propsList=t.propsList.concat(n);var r=JSON.parse(JSON.stringify(t.extendform));n.forEach((function(e){r[e.Code]=e.Value})),t.extendform=Object.assign({},r),t.extendField=n,t.form.Exdate=e.Exdate,t.form.DateName=e.DateName,t.form.Schduling_Count=e.Schduling_Count,t.form.Comp_Amount=e.Comp_Amount,Math.round(t.form.Weight*t.form.Num*1e3),t.getAreaList(),t.getInstall()}else t.$message({message:a.Message,type:"error"})}))},calculationAllWeight:function(){this.form.Total_Weight=Math.round(this.form.Weight*this.form.Num*1e3)/1e3},calculationNum:function(){this.form.Comp_Amount&&this.form.Component_Code&&(this.form.Num=this.form.Times*this.form.Comp_Amount)},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,s.EditPartpage)({Pro_lan_art:e.form,Pro_part_extend:(0,o.default)((0,o.default)({},e.Pro_part_extend),e.extendform)}).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))}},"getProjectOption",(function(){var e=this;(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))})),"getAreaList",(function(){var e=this;(0,c.GeAreaTrees)({projectId:this.form.Project_Id}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))})),"getInstall",(function(){var e=this;(0,d.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.SetupPositionData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))})),"projectChange",(function(){var e=this;this.$nextTick((function(){e.form.ProjectName=e.$refs["ProjectName"].selected.currentLabel})),this.form.Area_Id="",this.form.Area_Name="",this.treeParamsArea.data=[],this.$nextTick((function(t){e.$refs.treeSelectArea.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.InstallUnit_Name="",this.getAreaList()})),"areaChange",(function(e){this.form.Area_Name=e.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.InstallUnit_Name="",this.getInstall()})),"setupPositionChange",(function(){var e=this;this.$nextTick((function(){e.form.InstallUnit_Name=e.$refs["InstallUnit_Name"].selected.currentLabel}))})),"getColumnConfiguration",(function(e){var t=arguments;return(0,l.default)((0,i.default)().m((function a(){var n,r;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_parts_page_list",a.n=1,(0,u.GetGridByCode)({code:n+","+e});case 1:return r=a.v,a.a(2,r.Data.ColumnList.filter((function(e){return e.Is_Display})))}}),a)})))()})),"getPropsName",(function(e){var t;return null===(t=this.propsList.find((function(t){return t.Code.toLowerCase()===e.toLowerCase()})))||void 0===t?void 0:t.Display_Name}))}},"623d":function(e,t,a){},"627c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)},r=[]},"66f9":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddArea=ae,t.AddDeepFile=Z,t.AddMonomer=H,t.AppendImportPartList=le,t.AreaDelete=re,t.AreaGetEntity=ee,t.AttachmentGetEntities=M,t.ChangeLoad=_,t.CommonImportDeependToComp=Q,t.ContactsAdd=$,t.ContactsDelete=j,t.ContactsEdit=A,t.ContactsGetEntities=R,t.ContactsGetEntity=N,t.ContactsGetTreeList=G,t.DeleteMonomer=K,t.DepartmentAdd=D,t.DepartmentDelete=I,t.DepartmentEdit=T,t.DepartmentGetEntities=S,t.DepartmentGetEntity=C,t.DepartmentGetList=k,t.EditArea=ne,t.EditMonomer=J,t.FileAdd=g,t.FileAddType=f,t.FileDelete=h,t.FileEdit=b,t.FileGetEntity=p,t.FileHistory=v,t.FileMove=y,t.FileTypeAdd=d,t.FileTypeDelete=c,t.FileTypeEdit=m,t.FileTypeGetEntities=o,t.FileTypeGetEntity=i,t.GeAreaTrees=te,t.GetAreaTreeList=X,t.GetDictionaryDetailListByCode=O,t.GetEntitiesByRecordId=F,t.GetEntitiesProject=s,t.GetFileCatalog=l,t.GetFilesByType=u,t.GetGetMonomerList=q,t.GetLoadingFiles=P,t.GetMonomerEntity=V,t.GetProMonomerList=Y,t.GetProjectEntity=oe,t.GetProjectsflowmanagementAdd=U,t.GetProjectsflowmanagementEdit=E,t.GetProjectsflowmanagementInfo=B,t.GetShortUrl=W,t.ImportDeependToSteel=z,t.ImportPartList=ie,t.SysuserGetUserEntity=w,t.SysuserGetUserList=L,t.UserGroupTree=x;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntities",method:"post",data:e})}function i(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntity",method:"post",data:e})}function l(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetFileCatalog",method:"post",data:e})}function s(e){return(0,r.default)({url:"/SYS/Sys_FileType/GetEntitiesProject",method:"post",data:e})}function u(e){return(0,r.default)({url:"/SYS/Sys_File/GetPicEntities",method:"post",data:e})}function c(e){return(0,r.default)({url:"/SYS/Sys_FileType/Delete",method:"post",data:e})}function d(e){return(0,r.default)({url:"/SYS/Sys_FileType/Add",method:"post",data:e})}function f(e){return(0,r.default)({url:"/SYS/Sys_FileType/AddType",method:"post",data:e})}function m(e){return(0,r.default)({url:"/SYS/Sys_FileType/Edit",method:"post",data:e})}function p(e){return(0,r.default)({url:"/SYS/Sys_File/GetEntity",method:"post",data:e})}function h(e){return(0,r.default)({url:"/SYS/Sys_File/Delete",method:"post",data:e})}function g(e){return(0,r.default)({url:"/SYS/Sys_File/Add",method:"post",data:e})}function b(e){return(0,r.default)({url:"/SYS/Sys_File/Edit",method:"post",data:e})}function y(e){return(0,r.default)({url:"/SYS/Sys_File/Move",method:"post",data:e})}function _(e){return(0,r.default)({url:"/SYS/Sys_File/IsLoad",method:"post",data:e})}function v(e){return(0,r.default)({url:"/SYS/Sys_File/OldFile",method:"post",data:e})}function P(e){return(0,r.default)({url:"/SYS/Sys_File/GetBIMList",method:"post",data:e})}function C(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetEntity",method:"post",data:e})}function S(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetEntities",method:"post",data:e})}function I(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Delete",method:"post",data:e})}function T(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Edit",method:"post",data:e})}function D(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/Add",method:"post",data:e})}function k(e){return(0,r.default)({url:"/SYS/Sys_Contacts_Department/GetList",method:"post",data:e})}function L(e){return(0,r.default)({url:"/SYS/User/GetUserList",method:"post",data:e})}function w(e){return(0,r.default)({url:"/SYS/User/GetUserEntity",method:"post",data:e})}function x(e){return(0,r.default)({url:"/SYS/UserGroup/GetChildGroupTree",method:"post",data:e})}function O(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function N(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetEntity",method:"post",data:e})}function R(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetEntities",method:"post",data:e})}function G(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetTreeList",method:"post",data:e})}function j(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Delete",method:"post",data:e})}function A(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Edit",method:"post",data:e})}function $(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/Add",method:"post",data:e})}function M(e){return(0,r.default)({url:"/SYS/Sys_File/GetAttachmentEntities",method:"post",data:e})}function F(e){return(0,r.default)({url:"/SYS/Sys_File/GetEntitiesByRecordId",method:"post",data:e})}function U(e){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/Add",method:"post",data:e})}function E(e){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/Edit",method:"post",data:e})}function B(e){return(0,r.default)({url:"/SYS/Sys_Projectsflowmanagement/GetEntity",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PLM/XModel/GetShortUrl",method:"post",data:e})}function z(e){return(0,r.default)({url:"/plm/component/ImportDeependToSteel",method:"get",params:e})}function q(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetGetMonomerList",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetMonomerEntity",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetProMonomerList",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/AddMonomer",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/EditMonomer",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/DeleteMonomer",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/plm/MaterialInfo/CommonImportDeependToComp",method:"get",params:e})}function Z(e){return(0,r.default)({url:"/PLM/Component/AddDeepFile",method:"post",data:e,timeout:18e5})}function X(e){return(0,r.default)({url:"/PLM/Plm_Project_Areas/GetAreaTreeList",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/Project/GetArea",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/Project/AddArea",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/Project/EditArea",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/Project/Delete",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/Part/ImportPartList",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/Part/AppendImportPartList",method:"post",data:e})}},"68a9":function(e,t,a){"use strict";a("a076")},"694f":function(e,t,a){"use strict";a.r(t);var n=a("2516"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"6a37":function(e,t,a){"use strict";a.r(t);var n=a("2d44"),r=a("325d");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("8c82");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"7af04a1d",null);t["default"]=l.exports},"6a7e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("d3b7"),a("159b");var n=a("a667");t.default={data:function(){return{form:{Type:"",Remark:""},btnLoading:!1,labelList:[],filterLabelList:[],rules:{Type:{required:!0,message:"请选择",trigger:"change"}}}},methods:{init:function(e){var t=this,a=e.filter((function(e){return e.Sup_Count>0}));this.labelList=[],this.filterLabelList=[],a.forEach((function(e){1===e.Sup_Count&&t.labelList.push({label:e.SteelName,unPackCount:1,SteelUnique:e.SteelUnique,number:1})})),a.forEach((function(e){e.Sup_Count>1&&t.filterLabelList.push({label:e.SteelName,unPackCount:e.Sup_Count,SteelUnique:e.SteelUnique,number:1})}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};e.labelList.concat(e.filterLabelList).forEach((function(e){a.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),"1"===e.form.Type?(a.Package.Remark=e.form.Remark,e.btnLoading=!0,(0,n.Add)(a).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))):e.$emit("checkPackage",{data:a,type:3})}))}}}},"6ca5":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("3166");t.default={props:{typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tableData:[]}},mounted:function(){},methods:{init:function(e){var t=this;(0,n.GetSchedulingPartList)({id:e.Part_Aggregate_Id}).then((function(e){e.IsSucceed?t.tableData=e.Data.Scheduling_List:t.$message({message:e.Message,type:"error"})}))}}}},"6cc4":function(e,t,a){"use strict";a.r(t);var n=a("f7b1"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"708a9":function(e,t,a){"use strict";a("89bf")},"738b":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.handleSelect,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1)},r=[]},"73be":function(e,t,a){},7536:function(e,t,a){"use strict";a.r(t);var n=a("351d"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},7826:function(e,t,a){"use strict";a.r(t);var n=a("c1d5"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},7913:function(e,t,a){"use strict";a.r(t);var n=a("3f77"),r=a("34f7");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"462ee959",null);t["default"]=l.exports},7981:function(e,t,a){"use strict";a.r(t);var n=a("4c94"),r=a("a13f");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("c605");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"341aece4",null);t["default"]=l.exports},"7c57":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"inline-block"}},[e.label?a("span",{staticClass:"text",style:"width:"+e.labelWidth},[e._v(e._s(e.label)+" ")]):e._e(),a("el-select",{staticClass:"select",style:"width:"+e.width,attrs:{loading:e.loading,filterable:e.filterable,clearable:e.clearable,disabled:e.disabled,readonly:e.readonly,multiple:e.multiple,placeholder:e.placeholder,"multiple-limit":e.multipleLimit,"popper-append-to-body":e.popperAppendToBody},on:{blur:e.blur,focus:e.focus,change:e.change},model:{value:e.tmpvalue,callback:function(t){e.tmpvalue=t},expression:"tmpvalue"}},["projectlist"===e.thistype?e._l(e.options,(function(t){return a("el-option",{key:t.Sys_Project_Id,attrs:{label:"projectlist"===e.type?t.Name:t.Short_Name,value:t.Sys_Project_Id}})})):e._e(),"managerlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.ProjectManagerId,attrs:{label:e.ProjectManagerName,value:e.ProjectManagerId}})})):e._e(),"majorlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"userlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"authuserlist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e(),"factorylist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"arealist"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})):e._e(),"contacts"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.User_Id,attrs:{label:e.Employor_Name,value:e.User_Id}})})):e._e(),"dictionary"===e.thistype?e._l(e.options,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})):e._e()],2)],1)},r=[]},"7dde":function(e,t,a){"use strict";a.r(t);var n=a("d25b"),r=a("1927");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("27b2");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"16feab6a",null);t["default"]=l.exports},"7f9d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPartTeamProcessAllocation=Ue,t.AdjustSubAssemblyTeamProcessAllocation=Re,t.AdjustTeamProcessAllocation=Ne,t.ApplyCheck=Ve,t.BatchReceiveTaskFromStock=wt,t.BatchReceiveTransferTask=Me,t.BatchWithdrawSimplifiedProcessingHistory=ht,t.BeginProcess=W,t.BochuAddTask=qt,t.CancelNestingBill=J,t.CancelSchduling=_e,t.CancelTransferTask=Ee,t.CancelUnitSchduling=at,t.CheckSchduling=We,t.ComponentAllocationWorkingTeam=f,t.ComponentAllocationWorkingTeamBase=m,t.CreateCompTransferBill=z,t.CreateCompTransferByTransferCode=Z,t.CreatePartTransferByPartCodes=K,t.CreatePartTransferByTaskBill=q,t.CreatePartTransferByTransferCode=Q,t.DelSchdulingPlan=ve,t.DelSchdulingPlanById=Pe,t.DeleteNestingResult=_t,t.DownNestingTaskTemplate=ae,t.ExportAllocationComponent=S,t.ExportSimplifiedProcessingHistory=At,t.ExportTaskCodeDetails=Te,t.ExportTransferCodeDetail=Ae,t.GetAllWorkingTeamComponentCount=h,t.GetAllWorkingTeamComponentCountBase=b,t.GetAllWorkingTeamPartCount=k,t.GetAllWorkingTeamPartCountBase=g,t.GetBuildReturnRecordList=ut,t.GetCanSchdulingComps=se,t.GetCanSchdulingPartList=ce,t.GetCheckUserRankList=Ot,t.GetCheckingItemList=Rt,t.GetCheckingQuestionList=Gt,t.GetCompSchdulingInfoDetail=ue,t.GetCompSchdulingPageList=pe,t.GetCompTaskPageList=dt,t.GetCompTaskPartCompletionStock=$t,t.GetComponentPartComplexity=y,t.GetCoordinateProcess=P,t.GetDetailSummaryList=Ut,t.GetDwg=gt,t.GetMonthlyFullCheckProducedData=jt,t.GetNestingBillBoardPageList=E,t.GetNestingBillDetailList=Ft,t.GetNestingBillPageList=x,t.GetNestingBillTreeList=Mt,t.GetNestingFiles=vt,t.GetNestingMaterialWithPart=Jt,t.GetNestingPartList=Ct,t.GetNestingResultPageList=bt,t.GetNestingSurplusList=yt,t.GetNestingTaskInfoDetail=$,t.GetPageProcessTransferDetailBase=ie,t.GetPageSchdulingComps=le,t.GetPartPrepareList=Xe,t.GetPartSchdulingCancelHistory=$e,t.GetPartSchdulingInfoDetail=Qe,t.GetPartSchdulingPageList=de,t.GetPartTaskBoard=U,t.GetPartWithParentPageList=Ht,t.GetProcessAllocationComponentBasePageList=C,t.GetProcessAllocationComponentPageList=l,t.GetProcessAllocationPartBasePageList=T,t.GetProcessAllocationPartPageList=I,t.GetProcessPartTransferDetail=ee,t.GetProcessTransferDetail=X,t.GetProcessTransferPageList=B,t.GetSchdulingCancelHistory=we,t.GetSchdulingPageList=me,t.GetSchdulingPartUsePageList=ze,t.GetSchdulingWorkingTeams=ge,t.GetSemiFinishedStock=St,t.GetSimplifiedProcessingHistory=mt,t.GetSimplifiedProcessingSummary=pt,t.GetSourceFinishedList=It,t.GetStopList=Kt,t.GetTargetReceiveList=Tt,t.GetTaskPartPrepareList=et,t.GetTeamCompHistory=re,t.GetTeamPartUseList=qe,t.GetTeamProcessAllocation=Oe,t.GetTeamStockPageList=oe,t.GetTeamTaskAllocationPageList=xe,t.GetTeamTaskBoardPageList=M,t.GetTeamTaskDetails=Ie,t.GetTeamTaskPageList=Se,t.GetTenantFactoryType=ne,t.GetToReceiveTaskDetailList=kt,t.GetToReceiveTaskList=Dt,t.GetTransferCancelDetails=Be,t.GetTransferDetail=je,t.GetTransferHistory=Ge,t.GetTransferPageList=F,t.GetUnitSchdulingInfoDetail=Ze,t.GetUnitSchdulingPageList=he,t.GetWorkingTeamCheckingList=Nt,t.GetWorkingTeamComponentCount=p,t.GetWorkingTeamComponentCountBase=u,t.GetWorkingTeamLoadRealTime=ct,t.GetWorkingTeamPartCount=L,t.GetWorkingTeamPartCountBase=w,t.GetWorkingTeamsPageList=lt,t.GetYearlyFullCheckProducedData=xt,t.ImportNestingFiles=Pt,t.ImportNestingInfo=_,t.ImportSchduling=be,t.ImportThumbnail=Vt,t.ImportUpdateComponentWorkingTeam=i,t.ImportUpdatePartWorkingTeam=D,t.LentakExport=Wt,t.PartsAllocationWorkingTeamBase=N,t.PartsAllocationWrokingTeam=O,t.PartsBatchAllocationWorkingTeamBase=R,t.ProAddQuest=te,t.ProfilesExport=zt,t.ReceiveTaskFromStock=Lt,t.ReceiveTransferBill=Y,t.ReceiveTransferTask=Fe,t.RevocationComponentAllocation=s,t.SaveChangeZeroComponentRecoil=st,t.SaveCompSchdulingDraft=Ye,t.SaveCompTransferBill=V,t.SaveComponentSchedulingWorkshop=nt,t.SaveNestingPartInfo=A,t.SavePartSchdulingDraft=He,t.SavePartSchdulingDraftNew=Je,t.SavePartSchedulingWorkshop=rt,t.SavePartSchedulingWorkshopNew=ot,t.SavePartTransferBill=H,t.SaveSchdulingDraft=fe,t.SaveSchdulingTask=ye,t.SaveSchdulingTaskById=Ce,t.SaveUnitSchdulingDraftNew=Ke,t.SaveUnitSchedulingWorkshopNew=it,t.SigmaWOLExport=Bt,t.SimplifiedProcessing=ft,t.TeamProcessingByTaskCode=ke,t.TeamTaskProcessing=Le,t.TeamTaskTransfer=De,t.UpdateBatchCompAllocationWorkingTeamBase=d,t.UpdateBatchPartsAllocationWrokingTeamBase=j,t.UpdateComponentAllocationWorkingTeamBase=c,t.UpdateMachineName=Et,t.UpdatePartsAllocationWorkingTeamBase=G,t.UploadNestingFiles=v,t.WithdrawPicking=Yt,t.WithdrawScheduling=tt;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/ProductionTask/ImportUpdateComponentWorkingTeam",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/ProductionTask/RevocationComponentAllocation",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCountBase",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/ProductionTask/UpdateComponentAllocationWorkingTeamBase",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/ProductionTask/UpdateBatchCompAllocationWorkingTeamBase",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeam",method:"post",data:o.default.stringify(e)})}function m(e){return(0,r.default)({url:"/PRO/ProductionTask/ComponentAllocationWorkingTeamBase",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/ProductionTask/GetWorkingTeamComponentCount",method:"post",data:o.default.stringify(e)})}function h(e){return(0,r.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCount",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCountBase",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamComponentCountBase",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/ProductionTask/GetComponentPartComplexity",method:"post",data:o.default.stringify(e)})}function _(e){return(0,r.default)({url:"/PRO/ProductionTask/ImportNestingInfo",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/ProductionTask/UploadNestingFiles",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCoordinateProcess",method:"post",data:o.default.stringify(e)})}function C(e){return(0,r.default)({url:"/PRO/ProductionTask/GetProcessAllocationComponentBasePageList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportAllocationComponent",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartPageList",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/ProductionTask/GetProcessAllocationPartBasePageList",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/ProductionTask/ImportUpdatePartWorkingTeam",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/ProductionTask/GetAllWorkingTeamPartCount",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCount",method:"post",data:o.default.stringify(e)})}function w(e){return(0,r.default)({url:"/PRO/ProductionTask/GetWorkingTeamPartCountBase",method:"post",data:o.default.stringify(e)})}function x(e){return(0,r.default)({url:"/PRO/ProductionTask/GetNestingBillPageList",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/ProductionTask/PartsAllocationWrokingTeam",method:"post",data:o.default.stringify(e)})}function N(e){return(0,r.default)({url:"/PRO/ProductionTask/PartsAllocationWorkingTeamBase",method:"post",data:o.default.stringify(e)})}function R(e){return(0,r.default)({url:"/PRO/ProductionTask/PartsBatchAllocationWorkingTeamBase",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/ProductionTask/UpdatePartsAllocationWorkingTeamBase",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/ProductionTask/UpdateBatchPartsAllocationWrokingTeamBase",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/ProductionTask/SaveNestingPartInfo",method:"post",data:o.default.stringify(e)})}function $(e){return(0,r.default)({url:"/PRO/ProductionTask/GetNestingTaskInfoDetail",method:"post",data:o.default.stringify(e)})}function M(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamTaskBoardPageList",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTransferPageList",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/ProductionTask/GetPartTaskBoard",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/ProductionTask/GetNestingBillBoardPageList",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/ProductionTask/GetProcessTransferPageList",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/ProductionTask/BeginProcess",method:"post",data:o.default.stringify(e)})}function z(e){return(0,r.default)({url:"/PRO/ProductionTask/CreateCompTransferBill",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/ProductionTask/CreatePartTransferByTaskBill",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/ProductionTask/SaveCompTransferBill",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/ProductionTask/ReceiveTransferBill",method:"post",data:o.default.stringify(e)})}function H(e){return(0,r.default)({url:"/PRO/ProductionTask/SavePartTransferBill",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/ProductionTask/CancelNestingBill",method:"post",data:o.default.stringify(e)})}function K(e){return(0,r.default)({url:"/PRO/ProductionTask/CreatePartTransferByPartCodes",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/ProductionTask/CreatePartTransferByTransferCode",method:"post",data:o.default.stringify(e)})}function Z(e){return(0,r.default)({url:"/PRO/ProductionTask/CreateCompTransferByTransferCode",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/ProductionTask/GetProcessTransferDetail",method:"post",data:o.default.stringify(e)})}function ee(e){return(0,r.default)({url:"/PRO/ProductionTask/GetProcessPartTransferDetail",method:"post",data:o.default.stringify(e)})}function te(e){return(0,r.default)({url:"/PRO/ProductionTask/ProAddQuest",method:"post",data:o.default.stringify(e)})}function ae(e){return(0,r.default)({url:"/PRO/ProductionTask/DownNestingTaskTemplate",method:"post",data:e})}function ne(){return(0,r.default)({url:"/PRO/ProductionTask/GetTenantFactoryType",method:"post"})}function re(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamCompHistory",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamStockPageList",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/PRO/ProductionTask/GetPageProcessTransferDetailBase",method:"post",data:e})}function le(e){return(0,r.default)({url:"/PRO/ProductionTask/GetPageSchdulingComps",method:"post",data:e})}function se(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCanSchdulingComps",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingInfoDetail",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/PRO/nesting/GetCanSchdulingPartList",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingPageList",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SaveSchdulingDraft",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/ProductionTask/GetSchdulingPageList",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/ProductionSchduling/GetCompSchdulingPageList",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingPageList",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/ProductionTask/GetSchdulingWorkingTeams",method:"post",data:e})}function be(e){return(0,r.default)({url:"/PRO/ProductionSchduling/ImportCompSchduling",method:"post",timeout:12e5,data:e})}function ye(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTask",method:"post",data:e})}function _e(e){return(0,r.default)({url:"/PRO/ProductionTask/CancelSchduling",method:"post",data:e})}function ve(e){return(0,r.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlan",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/ProductionSchduling/DelSchdulingPlanById",method:"post",data:e})}function Ce(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SaveSchdulingTaskById",method:"post",data:e,timeout:12e5})}function Se(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamTaskPageList",method:"post",data:e})}function Ie(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamTaskDetails",method:"post",data:e})}function Te(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportTaskCodeDetails",method:"post",data:e})}function De(e,t){return(0,r.default)({url:"/PRO/ProductionTask/TeamTaskTransfer",method:"post",data:e,params:t})}function ke(e){return(0,r.default)({url:"/PRO/ProductionTask/TeamProcessingByTaskCode",method:"post",data:e})}function Le(e){return(0,r.default)({url:"/PRO/ProductionTask/TeamTaskProcessing",method:"post",data:e})}function we(e){return(0,r.default)({url:"/PRO/ProductionTask/GetSchdulingCancelHistory",method:"post",data:e})}function xe(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamTaskAllocationPageList",method:"post",data:e})}function Oe(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamProcessAllocation",method:"post",data:e})}function Ne(e){return(0,r.default)({url:"/PRO/ProductionSchduling/AdjustCompTeamProcessAllocation",method:"post",data:e})}function Re(e){return(0,r.default)({url:"/PRO/ProductionSchduling/AdjustSubAssemblyTeamProcessAllocation",method:"post",data:e})}function Ge(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTransferHistory",method:"post",data:e})}function je(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTransferDetail",method:"post",data:e})}function Ae(e){return(0,r.default)({url:"/PRO/ProductionTask/ExportTransferCodeDetail",method:"post",data:e})}function $e(e){return(0,r.default)({url:"/PRO/ProductionTask/GetPartSchdulingCancelHistory",method:"post",data:e})}function Me(e){return(0,r.default)({url:"/PRO/ProductionTask/BatchReceiveTransferTask",method:"post",data:o.default.stringify(e)})}function Fe(e){return(0,r.default)({url:"/PRO/ProductionTask/ReceiveTransferTask",method:"post",data:e})}function Ue(e){return(0,r.default)({url:"/PRO/ProductionSchduling/AdjustPartTeamProcessAllocation",method:"post",data:e})}function Ee(e){return(0,r.default)({url:"/PRO/ProductionTask/CancelTransferTask",method:"post",data:e})}function Be(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTransferCancelDetails",method:"post",data:e})}function We(e){return(0,r.default)({url:"/PRO/ProductionTask/CheckSchduling",method:"post",data:o.default.stringify(e)})}function ze(e){return(0,r.default)({url:"/PRO/ProductionTask/GetSchdulingPartUsePageList",method:"post",data:e})}function qe(e){return(0,r.default)({url:"/PRO/ProductionTask/GetTeamPartUseList",method:"post",data:e})}function Ve(e){return(0,r.default)({url:"/PRO/ProductionTask/ApplyCheck",method:"post",data:e})}function Ye(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SaveCompSchdulingDraft",method:"post",data:e,timeout:12e5})}function He(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraft",method:"post",data:e,timeout:12e5})}function Je(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SavePartSchdulingDraftNew",method:"post",data:e,timeout:12e5})}function Ke(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SaveUnitSchdulingDraftNew",method:"post",data:e,timeout:12e5})}function Qe(e){return(0,r.default)({url:"/PRO/ProductionSchduling/GetPartSchdulingInfoDetail",method:"post",data:e,timeout:12e5})}function Ze(e){return(0,r.default)({url:"/PRO/ProductionSchduling/GetUnitSchdulingInfoDetail",method:"post",data:e,timeout:12e5})}function Xe(e){return(0,r.default)({url:"/PRO/ProductionTask/GetPartPrepareList",method:"post",data:e})}function et(e){return(0,r.default)({url:"/pro/productiontask/GetTaskPartPrepareList",method:"post",data:e})}function tt(e){return(0,r.default)({url:"/PRO/ProductionSchduling/WithdrawScheduling",method:"post",data:e})}function at(e){return(0,r.default)({url:"/PRO/ProductionSchduling/CancelUnitSchduling",method:"post",data:e})}function nt(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SaveComponentSchedulingWorkshop",method:"post",data:e})}function rt(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshop",method:"post",data:e})}function ot(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SavePartSchedulingWorkshopNew",method:"post",data:e})}function it(e){return(0,r.default)({url:"/PRO/ProductionSchduling/SaveUnitSchedulingWorkshopNew",method:"post",data:e})}function lt(e){return(0,r.default)({url:"/PRO/ZeroComponentRecoil/GetWorkingTeamsPageList",method:"post",data:e})}function st(e){return(0,r.default)({url:"/PRO/ZeroComponentRecoil/SaveChangeZeroComponentRecoil",method:"post",data:e})}function ut(e){return(0,r.default)({url:"/PRO/ZeroComponentRecoil/GetBuildReturnRecordList",method:"post",data:e})}function ct(e){return(0,r.default)({url:"/PRO/ProductionTask/GetWorkingTeamLoadRealTime",method:"post",data:e})}function dt(e){return(0,r.default)({url:"/PRO/ProductionTask/GetCompTaskPageList",method:"post",data:e})}function ft(e){return(0,r.default)({url:"/PRO/ProductionTask/SimplifiedProcessing",method:"post",data:e})}function mt(e){return(0,r.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingHistory",method:"post",data:e})}function pt(e){return(0,r.default)({url:"/PRO/ProductionTask/GetSimplifiedProcessingSummary",method:"post",data:e})}function ht(e){return(0,r.default)({url:"/PRO/ProductionTask/BatchWithdrawSimplifiedProcessingHistory",method:"post",data:e})}function gt(e){return(0,r.default)({url:"/PRO/ProductionTask/GetDwg",method:"post",data:e})}function bt(e){return(0,r.default)({url:"/PRO/nesting/GetNestingResultPageList",method:"post",data:e})}function yt(e){return(0,r.default)({url:"/PRO/nesting/GetNestingSurplusList",method:"post",data:e})}function _t(e){return(0,r.default)({url:"/PRO/nesting/DeleteNestingResult",method:"post",data:e})}function vt(e){return(0,r.default)({url:"/PRO/nesting/GetNestingFiles",method:"post",data:e})}function Pt(e){return(0,r.default)({url:"/PRO/nesting/Import",method:"post",data:e})}function Ct(e){return(0,r.default)({url:"/PRO/nesting/GetNestingPartList",method:"post",data:e})}function St(e){return(0,r.default)({url:"/PRO/productiontask/GetSemiFinishedStock",method:"post",data:e})}function It(e){return(0,r.default)({url:"/PRO/productiontask/GetSourceFinishedList",method:"post",data:e})}function Tt(e){return(0,r.default)({url:"/PRO/productiontask/GetTargetReceiveList",method:"post",data:e})}function Dt(e){return(0,r.default)({url:"/PRO/productiontask/GetToReceiveTaskList",method:"post",data:e})}function kt(e){return(0,r.default)({url:"/PRO/productiontask/GetToReceiveTaskDetailList",method:"post",data:e})}function Lt(e){return(0,r.default)({url:"/PRO/productiontask/ReceiveTaskFromStock",method:"post",data:e})}function wt(e){return(0,r.default)({url:"/PRO/productiontask/BatchReceiveTaskFromStock",method:"post",data:e})}function xt(e){return(0,r.default)({url:"/PRO/InspectionAnalysis/GetYearlyFullCheckProducedData",method:"post",data:e})}function Ot(e){return(0,r.default)({url:"/PRO/InspectionAnalysis/GetCheckUserRankList",method:"post",data:e})}function Nt(e){return(0,r.default)({url:"/PRO/InspectionAnalysis/GetWorkingTeamCheckingList",method:"post",data:e})}function Rt(e){return(0,r.default)({url:"/PRO/InspectionAnalysis/GetCheckingItemList",method:"post",data:e})}function Gt(e){return(0,r.default)({url:"/PRO/InspectionAnalysis/GetCheckingQuestionList",method:"post",data:e})}function jt(e){return(0,r.default)({url:"/PRO/InspectionAnalysis/GetMonthlyFullCheckProducedData",method:"post",data:e})}function At(e){return(0,r.default)({url:"/PRO/productiontask/ExportSimplifiedProcessingHistory",method:"post",data:e})}function $t(e){return(0,r.default)({url:"/PRO/productiontask/GetCompTaskPartCompletionStock",method:"post",data:e})}function Mt(e){return(0,r.default)({url:"/Pro/NestingBill/GetPageList",method:"post",data:e})}function Ft(e){return(0,r.default)({url:"/Pro/NestingBill/GetDetailList",method:"post",data:e})}function Ut(e){return(0,r.default)({url:"/Pro/NestingBill/GetDetailSummaryList",method:"post",data:e})}function Et(e){return(0,r.default)({url:"/Pro/NestingBill/UpdateMachineName",method:"post",data:e})}function Bt(e){return(0,r.default)({url:"/PRO/CustomUssl/SigmaWOLExport",method:"post",data:e})}function Wt(e){return(0,r.default)({url:"/PRO/CustomUssl/LentakExport",method:"post",data:e})}function zt(e){return(0,r.default)({url:"/PRO/CustomUssl/ProfilesExport",method:"post",data:e})}function qt(e){return(0,r.default)({url:"/PRO/CustomUssl/BochuAddTask",method:"post",data:e})}function Vt(e){return(0,r.default)({url:"/Pro/Nesting/ImportThumbnail",method:"post",data:e})}function Yt(e){return(0,r.default)({url:"/Pro/MaterielPicking/WithdrawPicking",method:"post",data:e})}function Ht(e){return(0,r.default)({url:"/PRO/productiontask/GetPartWithParentPageList",method:"post",data:e})}function Jt(e){return(0,r.default)({url:"/PRO/productiontask/GetNestingMaterialWithPart",method:"post",data:e})}function Kt(e){return(0,r.default)({url:"/PRO/MOC/GetStopList",method:"post",data:e})}},"7fe3":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var r=n(a("ade3")),o=n(a("5530")),i=n(a("c14f")),l=n(a("1da1")),s=a("a667"),u=a("6186"),c=(a("2e8a"),a("3166")),d=a("f2f6");t.default={props:{typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{isReadOnly:!1,btnLoading:!1,form:{SteelUnique:"",Code:"",Spec:"",Length:"",Width:"",Texture:"",Num:"",Schduling_Count:"",Shape:"",Weight:"",Gross_Weight:"",Type_Name:"",Project_Name:"",Project_Id:"",InstallUnit_Id:"",InstallUnit_Name:"",Is_Main:"",Component_Code:"",DateName:"",Exdate:"",Remark:"",Area_Id:"",Area_Name:"",Times:0,Part_Pattern:"",Technology_Code:"",Technology_Remark:"",Texture_Replacement:"",Spec_Replacement:"",Margin:"",EA_Number:"",Dxf_Url:"",ABM:"",PayCode:"",Layer:""},extendform:{},Pro_part_extend:{},Is_Main_Data:[{Name:"是",Id:!0},{Name:"否",Id:!1}],ProjectNameData:[],SetupPositionData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},extendField:[],factoryOption:[],TypeOption:[],rules:{Code:{required:!0,message:"请输入",trigger:"blur"},Spec:{required:!0,message:"请输入",trigger:"blur"},Weight:{required:!0,message:"请输入",trigger:"blur"},Total_Weight:{required:!0,message:"请输入",trigger:"blur"},Length:{required:!0,message:"请选择",trigger:"change"},Num:{required:!0,message:"请选择",trigger:"change"},Texture:{required:!0,message:"请选择",trigger:"change"}},show:!1}},mounted:function(){this.getFormProps(),this.getProjectOption()},methods:(0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)((0,r.default)({getFormProps:function(){var e=this;return(0,l.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getColumnConfiguration(e.typeEntity.Code);case 1:a=t.v,e.propsList=a,e.show=!0;case 2:return t.a(2)}}),t)})))()},getLabel:function(e){var t=this.getPropsName(e);if(!t)try{this.rules[e].required=!1}catch(a){}return t},getProjectOption:function(){var e=this;(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},init:function(e){var t=this;this.isReadOnly=e.isReadOnly,(0,s.GetPartEntity)({id:e.Part_Aggregate_Id}).then((function(a){if(a.IsSucceed){a.Data.Pro_lan_art.Width="",a.Data.Pro_lan_art.Part_Pattern="",a.Data.Pro_lan_art.Technology_Code="",a.Data.Pro_lan_art.Technology_Remark="",a.Data.Pro_lan_art.Texture_Replacement="",a.Data.Pro_lan_art.Spec_Replacement="",a.Data.Pro_lan_art.Margin="",a.Data.Pro_lan_art.EA_Number="",a.Data.Pro_lan_art.Dxf_Url="",a.Data.Pro_lan_art.ABM="",a.Data.Pro_lan_art.PayCode="",a.Data.Pro_lan_art.Layer="",t.form=a.Data.Pro_lan_art,t.extendField=a.Data.ImportExtend;var n=a.Data.ImportExtend;t.Pro_part_extend=a.Data.Pro_part_extend,t.propsList=t.propsList.concat(n);var r=JSON.parse(JSON.stringify(t.extendform));n.forEach((function(e){r[e.Code]=e.Value})),t.extendform=Object.assign({},r),t.extendField=n,t.form.Exdate=e.Exdate,t.form.DateName=e.DateName,t.form.Schduling_Count=e.Schduling_Count,t.form.Comp_Amount=e.Comp_Amount,t.form.Width=e.Width,t.form.Part_Pattern=e.Part_Pattern,t.form.Technology_Code=e.Technology_Code,t.form.Technology_Remark=e.Technology_Remark,t.form.Texture_Replacement=e.Texture_Replacement,t.form.Spec_Replacement=e.Spec_Replacement,t.form.Margin=e.Margin,t.form.EA_Number=e.EA_Number,t.form.Dxf_Url=e.Dxf_Url,t.form.ABM=e.ABM,t.form.PayCode=e.PayCode,t.form.Layer=e.Layer,Math.round(t.form.Weight*t.form.Num*1e3),t.getAreaList(),t.getInstall()}else t.$message({message:a.Message,type:"error"})}))},calculationAllWeight:function(){this.form.Total_Weight=Math.round(this.form.Weight*this.form.Num*1e3)/1e3},calculationNum:function(){this.form.Comp_Amount&&this.form.Component_Code&&(this.form.Num=this.form.Times*this.form.Comp_Amount)},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,s.EditPartpage)({Pro_lan_art:e.form,Pro_part_extend:(0,o.default)((0,o.default)((0,o.default)({},e.Pro_part_extend),e.extendform),{},{Width:e.form.Width,Part_Pattern:e.form.Part_Pattern,Technology_Code:e.form.Technology_Code,Technology_Remark:e.form.Technology_Remark,Texture_Replacement:e.form.Texture_Replacement,Spec_Replacement:e.form.Spec_Replacement,Margin:e.form.Margin,EA_Number:e.form.EA_Number,Dxf_Url:e.form.Dxf_Url,ABM:e.form.ABM,PayCode:e.form.PayCode,Layer:e.form.Layer})}).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))}},"getProjectOption",(function(){var e=this;(0,c.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))})),"getAreaList",(function(){var e=this;(0,c.GeAreaTrees)({projectId:this.form.Project_Id}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))})),"getInstall",(function(){var e=this;(0,d.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.SetupPositionData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))})),"projectChange",(function(){var e=this;this.$nextTick((function(){e.form.ProjectName=e.$refs["ProjectName"].selected.currentLabel})),this.form.Area_Id="",this.form.Area_Name="",this.treeParamsArea.data=[],this.$nextTick((function(t){e.$refs.treeSelectArea.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.InstallUnit_Name="",this.getAreaList()})),"areaChange",(function(e){this.form.Area_Name=e.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.InstallUnit_Name="",this.getInstall()})),"setupPositionChange",(function(){var e=this;this.$nextTick((function(){e.form.InstallUnit_Name=e.$refs["InstallUnit_Name"].selected.currentLabel}))})),"getColumnConfiguration",(function(e){var t=arguments;return(0,l.default)((0,i.default)().m((function a(){var n,r;return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_parts_page_list",a.n=1,(0,u.GetGridByCode)({code:n+","+e});case 1:return r=a.v,a.a(2,r.Data.ColumnList.filter((function(e){return e.Is_Display})))}}),a)})))()})),"getPropsName",(function(e){var t;return null===(t=this.propsList.find((function(t){return t.Code.toLowerCase()===e.toLowerCase()})))||void 0===t?void 0:t.Display_Name}))}},"82e1":function(e,t,a){"use strict";a("cc4c")},8471:function(e,t,a){"use strict";a.r(t);var n=a("fc2b"),r=a("9e12");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("b1e7");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"1ee2c131",null);t["default"]=l.exports},8489:function(e,t,a){"use strict";a.r(t);var n=a("a13fd"),r=a("b9d8");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("abad");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"28f620ad",null);t["default"]=l.exports},"86b7":function(e,t,a){},"884f":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={props:{dialogVisible:{type:Boolean,default:!1}},data:function(){return{form:{PackageSN:"",AllWeight:"",PkgNO:"",ContractNo:"",GoodsName:"",Addressee:"",Volume:"",Gross:"",Departure:"",DIM:"",Remark:""},btnLoading:!1,visible:!1,rules:{},options:[],listOption:[]}},watch:{dialogVisible:{handler:function(e){this.visible=e},immediate:!0}},methods:{handleClose:function(){this.$emit("update:dialogVisible",!1)},fetchData:function(e){var t=this;(0,n.PackageGetEntity)({packageId:e[0].Id}).then((function(e){e.IsSucceed?t.form=e.Data:t.$message({message:e.Message,type:"error"})})),this.getList()},getList:function(){var e=this;(0,n.GetList)({}).then((function(t){t.IsSucceed?e.listOption=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.btnLoading=!0,(0,n.EditPackage)(this.form).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.handleClose(),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}}},"88fe":function(e,t,a){"use strict";a.r(t);var n=a("dad01"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"89bf":function(e,t,a){},"8a0a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),o=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var i=a("a5f2");t.default={props:{type:{type:String,default:""},label:{type:String,default:""},labelWidth:{type:String,default:""},value:{type:String|Array,default:""},filterable:{type:Boolean,default:!0},clearable:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},multipleLimit:{type:Number,default:0},placeholder:{type:String,default:"请选择"},width:{type:String,default:"200px"},popperAppendToBody:{type:Boolean,default:!0},customparam:{type:Object,default:function(){}},defaultfirst:{type:Boolean,default:!1},projectId:{type:String,default:""}},data:function(){return{loading:!1,options:[],thistype:"",tmpvalue:this.value,tmpkey:"Id"}},watch:{tmpvalue:function(e){this.$emit("input",e)},value:function(e){this.tmpvalue=e}},mounted:function(){this.getdata()},methods:{change:function(){var e=this;if(this.multiple){var t=[];this.tmpvalue.map((function(a){t.push(e.options.find((function(t){return t[e.tmpkey]===a})))})),this.$emit("change",t)}else this.$emit("change",this.options.find((function(t){return t[e.tmpkey]===e.tmpvalue})))},blur:function(e){this.$emit("blur",e)},focus:function(e){this.$emit("focus",e)},getdata:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.loading=!0,e.thistype=e.type,a=e.type,t.n="projectlist"===a?1:"projectshortlist"===a?3:"managerlist"===a?5:"projectmajorlist"===a?7:"sysmajorlist"===a?9:"userlist"===a?11:"authuserlist"===a?13:"factorylist"===a?15:"arealist"===a?17:"contacts"===a?19:21;break;case 1:return t.n=2,e.getproject();case 2:return e.tmpkey="Sys_Project_Id",t.a(3,23);case 3:return e.thistype="projectlist",e.tmpkey="Sys_Project_Id",t.n=4,e.getproject();case 4:return t.a(3,23);case 5:return e.tmpkey="ProjectManagerId",t.n=6,e.getmanager();case 6:return t.a(3,23);case 7:return e.thistype="majorlist",e.tmpkey="Id",t.n=8,e.getmajor(!1);case 8:return t.a(3,23);case 9:return e.thistype="majorlist",e.tmpkey="Id",t.n=10,e.getmajor(!0);case 10:return t.a(3,23);case 11:return e.tmpkey="Id",t.n=12,e.getuser();case 12:return t.a(3,23);case 13:return e.tmpkey="Id",t.n=14,e.getAuthuser();case 14:return t.a(3,23);case 15:return e.tmpkey="Id",t.n=16,e.getfactory();case 16:return t.a(3,23);case 17:return e.tmpkey="Id",t.n=18,e.getarea();case 18:return t.a(3,23);case 19:return e.tmpkey="Id",t.n=20,e.getContacts();case 20:return t.a(3,23);case 21:return e.thistype="dictionary",e.tmpkey="Id",t.n=22,e.getdictionary(e.type);case 22:return t.a(3,23);case 23:e.defaultfirst&&!e.tmpvalue&&(e.tmpvalue=e.options[0][e.tmpkey]),e.loading=!1;case 24:return t.a(2)}}),t)})))()},getContacts:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,i.GetProjectContacts)({model:{ProjectId:e.projectId,IsSysUser:!0,Platform:~~localStorage.getItem("CurPlatform")}});case 1:a=t.v,a.IsSucceed&&(e.options=a.Data);case 2:return t.a(2)}}),t)})))()}}}},"8a0d4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),o=a("2f62"),i=n(a("e55d")),l=n(a("3c2f"));t.default={name:"PROPartList",components:{v3:i.default,v4:l.default},computed:(0,r.default)({},(0,o.mapGetters)("tenant",["isVersionFour"]))}},"8bcc":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af");var r=n(a("c14f")),o=n(a("1da1")),i=a("e144"),l=a("c24f"),s=a("f4f2");t.default={props:{type:{type:String,default:""}},data:function(){return{baseCadUrl:"",drawer:!1,drawersull:!1,iframeKey:"",fullscreenid:"",iframeUrl:"",fullbimid:"",fileBim:"",IsUploadCad:!1,cadRowCode:"",cadRowProjectId:"",extensionName:"",templateUrl:""}},computed:{},created:function(){return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},mounted:function(){var e=this;window.addEventListener("message",this.frameListener),this.$once("hook:beforeDestroy",(function(){window.removeEventListener("message",e.frameListener)}))},activated:function(){var e=this;window.addEventListener("message",this.frameListener),this.$once("hook:deactivated",(function(){window.removeEventListener("message",e.frameListener)}))},methods:{getBaseCadUrl:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:t.n=1;break;case 1:return t.n=2,(0,l.getConfigure)({code:"glendale_url"});case 2:a=t.v,e.baseCadUrl=a.Data;case 3:return t.a(2)}}),t)})))()},dwgInit:function(e){var t=this;return(0,o.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getBaseCadUrl();case 1:t.extensionName="",t.extensionName=e.extensionName,t.fileBim=e.fileBim,t.IsUploadCad=e.IsUpload,t.cadRowCode=e.Code,t.cadRowProjectId=e.Sys_Project_Id,t.fileView();case 2:return a.a(2)}}),a)})))()},handleCloseDrawer:function(){this.drawer=!1},fileView:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.drawer=!0,t.n=1,e.$nextTick();case 1:e.iframeKey=(0,i.v4)(),a="构件"===e.type?1:11,e.iframeUrl="".concat(e.baseCadUrl,"?router=1&iframeId=").concat(a,"&baseUrl=").concat((0,s.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id"));case 2:return t.a(2)}}),t)})))()},renderIframe:function(){var e="构件"===this.type?1:11;this.iframeUrl="".concat(this.baseCadUrl,"?router=1&iframeId=").concat(e,"&baseUrl=").concat((0,s.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id")),this.fullscreenid=this.extensionName,this.fullbimid=this.fileBim},fullscreen:function(e){var t=null;t="构件"===this.type?0===e?2:3:13,this.templateUrl="",this.templateUrl="".concat(this.baseCadUrl,"?router=1&iframeId=").concat(t,"&baseUrl=").concat((0,s.baseUrl)(),"&token=").concat(localStorage.getItem("Token"),"&auth_id=").concat(localStorage.getItem("Last_Working_Object_Id")),this.drawersull=!0},frameListener:function(e){var t=e.data;"loaded"===t.type&&("1"===t.data.iframeId?document.getElementById("frame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{featureId:this.fullscreenid,cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showModel:!!this.fullscreenid,showCad:this.IsUploadCad}},"*"):"2"===t.data.iframeId?document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{featureId:this.fullscreenid,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showModel:!!this.fullscreenid}},"*"):"3"===t.data.iframeId?document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showCad:this.IsUploadCad}},"*"):"11"===t.data.iframeId?document.getElementById("frame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showCad:this.IsUploadCad,isSubAssembly:"部件"===this.type,isPart:!0}},"*"):"13"===t.data.iframeId&&document.getElementById("fullFrame").contentWindow.postMessage({type:"router",path:"/modelCad",query:{cadId:this.fileBim,projectId:this.cadRowProjectId,steelName:this.cadRowCode,showCad:this.IsUploadCad,isSubAssembly:"部件"===this.type,isPart:!0}},"*"))}}}},"8c24":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b");var r=n(a("c14f")),o=n(a("1da1")),i=a("3166"),l=a("0e9a");t.default={props:{typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tableData:[]}},mounted:function(){},methods:{init:function(e){var t=this;(0,i.GetPartDeepenFileList)({id:e.Id}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleClick:function(e){var t=this;return(0,o.default)((0,r.default)().m((function a(){var n,o,i,s,u;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:if(n=e.Url.lastIndexOf("."),o=e.Url.lastIndexOf("?"),i=e.Url.substring(n,o),".pdf"!==i){a.n=1;break}window.open(e.Url+"#toolbar=0","_blank"),a.n=4;break;case 1:if(".xls"!==i&&".xlsx"!==i&&".doc"!==i&&".docx"!==i){a.n=3;break}return s=t.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),a.n=2,(0,l.fileToPdf)(e.Url,!1);case 2:s.close(),a.n=4;break;case 3:".dwg"===i?(u=url.split(".com/"),u[1]=encodeURIComponent(u[1]),url=u.join(".com/"),window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+e.Url,"_blank")):(".pdf"===i&&(e.Url+="#toolbar=0"),window.open(e.Url,"_blank"));case 4:window.open(e.Url);case 5:return a.a(2)}}),a)})))()}}}},"8c68":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,"empty-text":"暂无数据",border:""}},[a("el-table-column",{attrs:{prop:"Scheduling_Status",label:"零件状态",align:"center"}}),a("el-table-column",{attrs:{prop:"Component_Count",label:"数量",align:"center"}})],1),a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{"text-align":"right","margin-top":"10px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")])],1)])],1)],1)},r=[]},"8c82":function(e,t,a){"use strict";a("ba14")},"90d3":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),e._l(e.filterLabelList,(function(t){return a("el-form-item",{key:t.SteelUnique,attrs:{label:t.label}},[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",attrs:{max:t.unPackCount,clearable:""},model:{value:t.number,callback:function(a){e.$set(t,"number",a)},expression:"item.number"}}),a("div",[e._v("未打包数量："+e._s(t.unPackCount))])],1)})),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],2)},r=[]},9119:function(e,t,a){"use strict";a("623d")},9375:function(e,t,a){},9526:function(e,t,a){"use strict";a.r(t);var n=a("a5180"),r=a("a4c7");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"22ee7413",null);t["default"]=l.exports},"957c3":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("3166");t.default={props:{typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tableData:[]}},mounted:function(){},methods:{init:function(e){var t=this;(0,n.GetSchedulingPartList)({id:e.Part_Aggregate_Id}).then((function(e){e.IsSucceed?t.tableData=e.Data.Scheduling_List:t.$message({message:e.Message,type:"error"})}))}}}},"965b":function(e,t,a){"use strict";a.r(t);var n=a("e0a5"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"968b6":function(e,t,a){"use strict";a.r(t);var n=a("8c68"),r=a("23061");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"5b9c0c25",null);t["default"]=l.exports},9695:function(e,t,a){"use strict";a("155b5")},"969f":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={props:{dialogVisible:{type:Boolean,default:!1}},data:function(){return{form:{PackageSN:"",AllWeight:"",PkgNO:"",ContractNo:"",GoodsName:"",Addressee:"",Volume:"",Gross:"",Departure:"",DIM:"",Remark:""},btnLoading:!1,visible:!1,rules:{},options:[],listOption:[]}},watch:{dialogVisible:{handler:function(e){this.visible=e},immediate:!0}},methods:{handleClose:function(){this.$emit("update:dialogVisible",!1)},fetchData:function(e){var t=this;(0,n.PackageGetEntity)({packageId:e[0].Id}).then((function(e){e.IsSucceed?t.form=e.Data:t.$message({message:e.Message,type:"error"})})),this.getList()},getList:function(){var e=this;(0,n.GetList)({}).then((function(t){t.IsSucceed?e.listOption=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;this.btnLoading=!0,(0,n.EditPackage)(this.form).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.handleClose(),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}}},"970b":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a15b");var r=n(a("c14f")),o=n(a("1da1")),i=a("3166"),l=a("0e9a");t.default={props:{typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tableData:[]}},mounted:function(){},methods:{init:function(e){var t=this;(0,i.GetPartDeepenFileList)({id:e.Id}).then((function(e){e.IsSucceed?t.tableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleClick:function(e){var t=this;return(0,o.default)((0,r.default)().m((function a(){var n,o,i,s,u;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:if(n=e.Url.lastIndexOf("."),o=e.Url.lastIndexOf("?"),i=e.Url.substring(n,o),".pdf"!==i){a.n=1;break}window.open(e.Url+"#toolbar=0","_blank"),a.n=4;break;case 1:if(".xls"!==i&&".xlsx"!==i&&".doc"!==i&&".docx"!==i){a.n=3;break}return s=t.$loading({lock:!0,text:"正在加载请稍后",spinner:"el-icon-loading"}),a.n=2,(0,l.fileToPdf)(e.Url,!1);case 2:s.close(),a.n=4;break;case 3:".dwg"===i?(u=url.split(".com/"),u[1]=encodeURIComponent(u[1]),url=u.join(".com/"),window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+e.Url,"_blank")):(".pdf"===i&&(e.Url+="#toolbar=0"),window.open(e.Url,"_blank"));case 4:window.open(e.Url);case 5:return a.a(2)}}),a)})))()}}}},"97a9":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("e9f5"),a("910d"),a("d3b7");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=n(a("0f97")),s=n(a("15ac")),u=a("a667");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{zParams:{type:Object,default:function(){}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbLoading:!1,tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},columns:[],total:0,tbData:[]}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_item_page_list,".concat(e.typeEntity.Code));case 1:a=t.v,e.$emit("getColumn",a.filter((function(e){return e.Is_Display}))||[]);case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){this.$emit("getList")},fetchRightData:function(e){var t=this;if(!e)return this.tbData=[],void(this.total=0);this.tbLoading=!0,(0,u.GetPageStorageSteelsBySearchItem)((0,r.default)((0,r.default)({Id:e},this.zParams),{},{PageInfo:(0,r.default)({},this.queryInfo)})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"}),t.tbLoading=!1}))},handleSelect:function(e){this.$emit("selectList",e)}}}},99215:function(e,t,a){},"99e5":function(e,t,a){"use strict";a.r(t);var n=a("627c"),r=a("29cd");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"127562b4",null);t["default"]=l.exports},"9db3":function(e,t,a){"use strict";a("397a")},"9dd2":function(e,t,a){"use strict";a.r(t);var n=a("b64a"),r=a("caf5");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("d037");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"7f042f5c",null);t["default"]=l.exports},"9e12":function(e,t,a){"use strict";a.r(t);var n=a("61d6"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"9e58":function(e,t,a){"use strict";a("f1a7")},a076:function(e,t,a){},a13f:function(e,t,a){"use strict";a.r(t);var n=a("f7b46"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},a13fd:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"i-dialog",attrs:{"append-to-body":"",visible:e.visible,title:"编辑包信息",width:"30%"},on:{"update:visible":function(t){e.visible=t},close:e.handleClose}},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包编号",prop:"PackageSN"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.PackageSN,callback:function(t){e.$set(e.form,"PackageSN",t)},expression:"form.PackageSN"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总重量",prop:"AllWeight"}},[a("el-input",{model:{value:e.form.AllWeight,callback:function(t){e.$set(e.form,"AllWeight",t)},expression:"form.AllWeight"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包名称",prop:"PkgNO"}},[a("el-input",{model:{value:e.form.PkgNO,callback:function(t){e.$set(e.form,"PkgNO",t)},expression:"form.PkgNO"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目合同编号",prop:"ContractNo"}},[a("el-input",{model:{value:e.form.ContractNo,callback:function(t){e.$set(e.form,"ContractNo",t)},expression:"form.ContractNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"构件类型",prop:"GoodsName"}},[a("el-input",{model:{value:e.form.GoodsName,callback:function(t){e.$set(e.form,"GoodsName",t)},expression:"form.GoodsName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"收件人",prop:"Addressee"}},[a("el-input",{model:{value:e.form.Addressee,callback:function(t){e.$set(e.form,"Addressee",t)},expression:"form.Addressee"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"体积",prop:"Volume"}},[a("el-input",{model:{value:e.form.Volume,callback:function(t){e.$set(e.form,"Volume",t)},expression:"form.Volume"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"毛重系数",prop:"Gross"}},[a("el-input",{model:{value:e.form.Gross,callback:function(t){e.$set(e.form,"Gross",t)},expression:"form.Gross"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"起运港",prop:"Departure"}},[a("el-input",{model:{value:e.form.Departure,callback:function(t){e.$set(e.form,"Departure",t)},expression:"form.Departure"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"尺寸",prop:"DIM"}},[a("el-input",{model:{value:e.form.DIM,callback:function(t){e.$set(e.form,"DIM",t)},expression:"form.DIM"}})],1)],1),a("el-col",{attrs:{span:24,prop:"Remark"}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:50,"show-word-limit":"",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:e.handleClose}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1)],1)},r=[]},a2ff:function(e,t,a){"use strict";a.r(t);var n=a("d457"),r=a("32c9");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("9695"),a("4522");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"5a99fc22",null);t["default"]=l.exports},a3e4:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("cb29"),a("14d9"),a("13d5"),a("a434"),a("e9f5"),a("7d54"),a("9485"),a("a732"),a("e9c4"),a("b680"),a("b64b"),a("d3b7"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("159b"),a("ddb0");var r=n(a("c14f")),o=n(a("1da1")),i=a("a667"),l=a("e144"),s=(a("3ff8"),a("6186"),a("fd31"),a("3b8f"),n(a("6612")));t.default={props:{typeEntity:{type:Object,default:function(){}},AreaId:{type:String,default:""},ProjectId:{type:String,default:""}},data:function(){return{btnLoading:!1,infoShow:!1,splitType:"1",partList:[{Id:(0,l.v4)(),Code:"",Length:0,Width:0,Weight:0,Gross_Weight:0}],rowData:{}}},mounted:function(){return(0,o.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{init:function(e){this.selectList=e,this.rowData=this.selectList[0],this.updatePartList()},handleAdd:function(){this.partList.length>=10?this.$message.warning("最多添加10个零件"):(this.partList.push({Id:(0,l.v4)(),Code:this.selectList[0].Code,Length:0,Width:0,Weight:0,Gross_Weight:0}),this.updatePartList())},handleDelete:function(e){this.partList.splice(e,1),this.updatePartList()},updatePartList:function(){var e=this,t=this.partList.length,a=t>1?this.splitAverage(this.selectList[0].Length,t):[this.selectList[0].Length],n=t>1?this.splitAverage(this.selectList[0].Width,t):[this.selectList[0].Width],r=t>1?this.splitAverage(this.selectList[0].Weight,t):[this.selectList[0].Weight],o=t>1?this.splitAverage(this.selectList[0].Gross_Weight,t):[this.selectList[0].Gross_Weight];this.partList.forEach((function(t,i){t.Code="".concat(e.selectList[0].Code,"#").concat(i+1),t.Length=a[i],t.Width=n[i]||null,t.Weight=r[i],t.Gross_Weight=o[i]||null}))},splitAverage:function(e,t){var a=e/t,n=parseFloat(a.toFixed(2)),r=new Array(t).fill(n),o=r.reduce((function(e,t){return e+t}),0),i=e-o;return 0!==i&&(r[r.length-1]=parseFloat((r[0]+i).toFixed(2))),r},onSubmit:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a,n,o,i,l,u,c,d,f,m,p;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(a=function(t){return e.partList.reduce((function(e,a){var n=(0,s.default)(a[t]||0).value();return(0,s.default)(e).add(n).value()}),0)},n=a("Length"),o=a("Width"),i=a("Weight"),l=n===e.rowData.Length,u=o===e.rowData.Width,c=i===e.rowData.Weight,d=!0,e.partList.forEach((function(e){e.Code&&e.Length&&e.Weight||(d=!1)})),d){t.n=1;break}return e.$message.warning("请填写所有拆分零件的必填字段"),t.a(2);case 1:if(f=new Set,m=e.partList.some((function(e){return!!f.has(e.Code)||(f.add(e.Code),!1)})),!m){t.n=2;break}return e.$message.warning("拆分后零件名称重复，请修改"),t.a(2);case 2:l&&u&&c?e.$confirm("请确认是否进行零件拆分","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){e.partSplitFn()})).catch((function(){e.$message({type:"info",message:"已取消拆分"})})):(p="",p=l||u||c?l||u?l||c?u||c?l?u?c?"":"单重":"宽度":"长度":"宽度、单重":"长度、单重":"长度、宽度":"长度、宽度、单重",e.$confirm("拆分后零件的".concat(p,"不等于原零件，请确认是否拆分"),"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){e.partSplitFn()})).catch((function(){e.$message({type:"info",message:"已取消拆分"})})));case 3:return t.a(2)}}),t)})))()},partSplitFn:function(){var e=this;this.btnLoading=!0,(0,i.PartSplit)({Part_Aggregate_Id:this.rowData.Part_Aggregate_Id,SplitType:this.splitType,Splits:this.partList}).then((function(t){t.IsSucceed?(e.$emit("close"),e.$emit("refresh")):(e.$message({message:t.Message,type:"error"}),e.btnLoading=!1)}))},showInfoMore:function(){this.infoShow=!this.infoShow}}}},a4c7:function(e,t,a){"use strict";a.r(t);var n=a("b256"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},a5180:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"生成方式",prop:"Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"创建新构件包",value:"1"}}),a("el-option",{attrs:{label:"加入已有构件包",value:"2"}})],1)],1),"1"===e.form.Type?a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1):e._e(),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)},r=[]},a529:function(e,t,a){"use strict";a.r(t);var n=a("6a7e"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},a54c:function(e,t,a){"use strict";a("4321")},a55d:function(e,t,a){"use strict";a.r(t);var n=a("5565"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},a5f2:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.GetProjectContacts=o;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/SYS/Sys_Project_Contacts/GetProjectList",method:"post",data:e})}},a888:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("d565")),o=function(e){e.directive("el-drag-dialog",r.default)};window.Vue&&(window["el-drag-dialog"]=r.default,Vue.use(o)),r.default.install=o;t.default=r.default},aa27:function(e,t,a){},abad:function(e,t,a){"use strict";a("9375")},abeb:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,"empty-text":"暂无数据",border:""}},[a("el-table-column",{attrs:{prop:"Type",label:"文件类型",align:"center"}}),a("el-table-column",{attrs:{prop:"Title",label:"文件名称",align:"center"}}),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.handleClick(t.row)}}},[e._v("查看")])]}}])})],1),a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{"text-align":"right","margin-top":"10px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")])],1)])],1)],1)},r=[]},add1:function(e,t,a){"use strict";a.r(t);var n=a("1451"),r=a("4bb9");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"1625571b",null);t["default"]=l.exports},afa5:function(e,t,a){"use strict";a.r(t);var n=a("b17f"),r=a("6cc4");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"5163f512",null);t["default"]=l.exports},afdd:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7"),a("ac1f"),a("5319");var r=n(a("c14f")),o=n(a("1da1")),i=n(a("5530")),l=a("66f9"),s=a("586a"),u=n(a("bbc2")),c=a("a667"),d=a("ed08"),f={Id:"",Doc_Catelog:"",Doc_Type:"",Project_Name:"",Project_Id:"",Sys_Project_Id:"",Area_Name:"",Area_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",Is_Load:!1,Doc_File:"",Professional_Code:"",Template_Type:2,File_Url:""};t.default={components:{OSSUpload:u.default},props:{typeEntity:{type:Object,default:function(){}}},data:function(){return{type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,form:(0,i.default)({},f),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}]},treeParams:{clickParent:!0,filterable:!1,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},selectParams:{multiple:!1,clearable:!0,placeholder:"请输入内容"},fileType:"",curFile:"",bimvizId:"",isDeep:!1,projectId:"",isSHQD:"",btnLoading:!1,command:"cover"}},created:function(){this.fileType=this.$route.name,"PLMPicVideoFiles"===this.fileType&&(this.allowFile="image/*"),this.BIMFiles&&(this.allowFile=".ifc,.bzip,.bzip2")},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},getTemplate:function(){var e=this,t=this.form.Professional_Code;(0,c.PartDeepeningImportTemplate)({Professional_Code:t,Template_Type:this.form.Template_Type}).then((function(t){t.IsSucceed?window.open((0,d.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)}))},handleChange:function(e,t){this.fileList=t.slice(-1),t.length>1&&(this.attachments.splice(-1),this.form.Doc_File="",this.form.Doc_Content="",this.form.Doc_Title="")},beforeUpload:function(e){this.curFile=e,this.loading=!0,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this,n=0;this.fileList.filter((function(t,a){t.name===e.name&&(n=a)})),this.fileList.splice(n,1),this.attachments.splice(n,1),this.form.Doc_File=this.form.Doc_File.replace(e.name,""),this.form.Doc_Content=this.form.Doc_File.replace(e.name,""),this.form.Doc_Title=this.form.Doc_File.replace(e.name,""),this.loading=!t.every((function(e){return"success"===e.status})),setTimeout((function(){a.btnLoading=!t.every((function(e){return"success"===e.status}))}),1e3)},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){var n=this;this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]});var r=this.form.Doc_Title+(this.form.Doc_Title?",":"")+e.Data.split("*")[3];this.form.Doc_Title=r.substring(0,r.lastIndexOf(".")),this.form.Doc_Content=this.form.Doc_Title,this.form.Doc_File=this.form.Doc_File+(this.form.Doc_File?",":"")+e.Data.split("*")[3],this.form.File_Url=e.Data.split("*")[0],this.loading=!a.every((function(e){return"success"===e.status})),setTimeout((function(){n.btnLoading=!a.every((function(e){return"success"===e.status}))}),1e3)},handleOpen:function(e,t,a){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"cover",l=arguments.length>6?arguments[6]:void 0;this.projectId=r,this.isDeep=n,this.form=(0,i.default)({},f),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.isSHQD=t.isSHQD,this.form.Professional_Code=t.Code,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.command=o,this.form.Project_Name=l.Project_Name,this.form.Sys_Project_Id=l.Sys_Project_Id,this.form.Area_Name=l.Area_Name,this.form.Area_Id=l.Area_Id,"add"===this.type&&(this.fileList=[],this.title="新增文件",this.form.Id=""),"cover"===this.command?this.title="覆盖文件":"add"===this.command&&(this.title="新增文件")},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.btnLoading=!1,this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,o.default)((0,r.default)().m((function t(a){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(!a){t.n=1;break}e.loading=!0,e.btnLoading=!0,e.updateInfo(),t.n=2;break;case 1:return e.$message({message:"请将表单填写完整",type:"warning"}),t.a(2,!1);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},updateInfo:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a,n,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(a=(0,i.default)((0,i.default)({},e.form),{},{AttachmentList:e.attachments}),"cover"!==e.command){t.n=9;break}return t.p=1,t.n=2,(0,l.ImportPartList)(a);case 2:if(n=t.v,!n.IsSucceed){t.n=4;break}return e.$message({message:"保存成功",type:"success"}),t.n=3,e.updatePartAggregateId();case 3:e.$emit("getData",e.form.Doc_Type),e.$emit("getTreeData"),e.handleClose(),t.n=5;break;case 4:n.Data&&window.open((0,d.combineURL)(e.$baseUrl,n.Data),"_blank"),e.$message.error(n.Message);case 5:t.n=7;break;case 6:t.p=6,t.v,e.$message.error("保存失败");case 7:return t.p=7,e.btnLoading=!1,e.loading=!1,t.f(7);case 8:t.n=18;break;case 9:if("add"!==e.command){t.n=18;break}return t.p=10,t.n=11,(0,l.AppendImportPartList)(a);case 11:if(o=t.v,!o.IsSucceed){t.n=13;break}return e.$message({message:"保存成功",type:"success"}),t.n=12,e.updatePartAggregateId();case 12:e.$emit("getData",e.form.Doc_Type),e.$emit("getTreeData"),e.handleClose(),t.n=14;break;case 13:o.Data&&window.open((0,d.combineURL)(e.$baseUrl,o.Data),"_blank"),e.$message.error(o.Message);case 14:t.n=16;break;case 15:t.p=15,t.v,e.$message.error("保存失败");case 16:return t.p=16,e.btnLoading=!1,e.loading=!1,t.f(16);case 17:case 18:return t.a(2)}}),t,null,[[10,15,16,17],[1,6,7,8]])})))()},updatePartAggregateId:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.UpdatePartAggregateId)({AreaId:e.form.Area_Id}).then((function(t){t.IsSucceed||e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()}}}},b17f:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._v(" 111"+e._s(e.customParams)+" ")])},r=[]},b1e7:function(e,t,a){"use strict";a("4704")},b256:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530"));a("e9c4"),a("b64b");var o=a("a667");t.default={props:{customParams:{type:Object,default:function(){}}},data:function(){return{form:{Remark:"",Type:""},btnLoading:!1,rules:{Type:[{required:!0,message:"请选择",trigger:"change"}]}}},methods:{handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=JSON.parse(JSON.stringify(e.customParams));delete a.pageInfo;var n={Package:{ProjectID:localStorage.getItem("CurReferenceId"),Remark:e.form.Remark},Steels:[],Search:(0,r.default)((0,r.default)({},a),{},{Type:e.form.Type})};"2"!==e.form.Type?(e.btnLoading=!0,(0,o.BatchAdd)(n).then((function(t){t.IsSucceed?e.$emit("refresh"):e.$message({message:t.Message,type:"error"}),e.$emit("close"),e.btnLoading=!1}))):e.$emit("checkPackage",{data:n,type:1})}))}}}},b28f:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Glendale=void 0;var n=a("21c4");t.Glendale={baseUrl:"https://glendale-api.bimtk.com",uploadUrl:"/api/app/model/upload-file",externalUploadUrl:"/api/app/model/transcode-file",token:"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",options:{InitiatingUser:"admin",UniqueCode:"20228_29",Priority:"202",ModelUploadUrl:"".concat((0,n.getTenantId)()),OtherInfo:"",ConfigJson:{style:1,zGrid:1,viewStyle:0,drawing:0,accuracy:3,parametric:1,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:1,unitRatio:.001,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:1,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:1}},optionsCad:{Name:"图纸12月30",InitiatingUser:"admin",Priority:"202",UniqueCode:"test001",IsCAD:!0,ModelUploadUrl:"".concat((0,n.getTenantId)()),ConfigJson:{style:1,zGrid:0,viewStyle:0,drawing:0,accuracy:5,parametric:0,familyName:"",writetype:0,locationType:0,vertexNormal:1,isExportLines:0,unitRatio:1,type:2,offsetX:0,offsetY:0,offsetZ:0,isInstance:1,maxCountInstance:100,isLod:0,isCad:0,srs:"",srsOrigin:[],longitude:1.9003144895714261,latitude:.5969026041820608,transHeight:0,edgeOn:0,level:1,xCount:1,yCount:1,draco:0,faceNumLimit:1e6}}}},b2e8:function(e,t,a){"use strict";a.r(t);var n=a("1389"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},b4a4f:function(e,t,a){"use strict";a.r(t);var n=a("b9bd"),r=a("7536");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("9119");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"11e51c6c",null);t["default"]=l.exports},b5d4:function(e,t,a){},b64a:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载构件导入模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.getMonomerList},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{label:e.Name,value:e.Sys_Project_Id}})})),1)],1),a("el-form-item",{attrs:{label:"统一分区信息",required:""}},[a("el-select",{staticStyle:{width:"100%"},on:{change:e.unityInfo},model:{value:e.form.IsUnifyPartiotion,callback:function(t){e.$set(e.form,"IsUnifyPartiotion",t)},expression:"form.IsUnifyPartiotion"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1)],1),e.form.IsUnifyPartiotion?[e.showMonomer?a("el-form-item",{attrs:{label:"所属单体",prop:"MonomerId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{change:e.getArea},model:{value:e.form.MonomerId,callback:function(t){e.$set(e.form,"MonomerId",t)},expression:"form.MonomerId"}},e._l(e.MonomerList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"所属区域",prop:"Area_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}},e._l(e.AreaOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1)]:e._e(),a("el-form-item",{attrs:{label:"安装单元"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.form.Batches,callback:function(t){e.$set(e.form,"Batches",t)},expression:"form.Batches"}},e._l(e.unitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"上传文件"}},[a("el-input",{staticClass:"cs-up",attrs:{placeholder:"请选择",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("OSSUpload",{ref:"upload",staticClass:"z-upload",attrs:{action:"","before-upload":e.beforeUpload,limits:1,"on-success":e.uploadSuccess,"show-file-list":!1,accept:".xls, .xlsx","btn-icon":"el-icon-upload"}},[a("el-button",{attrs:{loading:e.loading,icon:"el-icon-upload",size:"small"}},[e._v("请选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._t("tips")],2)],1)],1)])],1),a("el-form-item",{staticStyle:{"text-align":"right","margin-top":"50px"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确 定")])],1)],2)],1)},r=[]},b6be2:function(e,t,a){"use strict";a("070e")},b90b:function(e,t,a){"use strict";a.r(t);var n=a("0cea"),r=a("13b3");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("3258");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"019f497c",null);t["default"]=l.exports},b9bd:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("div",[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.searchDate,callback:function(t){e.searchDate=t},expression:"searchDate"}}),a("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("查询")])],1),a("div",[a("el-button",{attrs:{type:"primary",disabled:!e.selectArray.length,loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("导出")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.multiSelectedChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange},scopedSlots:e._u([{key:"Create_Date",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Create_Date))+" ")]}},{key:"Modify_Date ",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Modify_Date))+" ")]}}])})],1)])},r=[]},b9cc:function(e,t,a){"use strict";a.r(t);var n=a("c7781"),r=a("5a04");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("deca1");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"736c0c99",null);t["default"]=l.exports},b9d8:function(e,t,a){"use strict";a.r(t);var n=a("969f"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},b9eb:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d9e2"),a("d3b7"),a("25f0");var r=n(a("4360"));function o(e,t){var a=t.value,n=r.default.getters&&r.default.getters.sysUseType;if("[object Number]"!==Object.prototype.toString.call(a)||"number"!==typeof n)throw new Error('need sysUseType! Like v-sys-use-type="123"');a!==n&&e.parentNode&&e.parentNode.removeChild(e)}t.default={inserted:function(e,t){o(e,t)},update:function(e,t){o(e,t)}}},ba14:function(e,t,a){},bf03:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var r=n(a("c14f")),o=n(a("1da1")),i=a("a667"),l=n(a("bbc2")),s=a("0e9a"),u=a("66f9"),c=a("2d91"),d=a("5e99");t.default={name:"ComponentImport",components:{OSSUpload:l.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,factoryOption:[],typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Factory_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[]}},watch:{typeId:function(e){this.form.Type_Id=e}},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.getInfo(),t.n=1,e.getMonomerStatus();case 1:return t.n=2,e.getArea();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=2;break}return t.n=2,e.getMonomerList();case 2:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetGetMonomerList)({projectId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName"),this.form.ProjectId=localStorage.getItem("CurReferenceId"),this.getFactory()},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,i.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,s.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getFactory:function(){var e=this;(0,d.GetPlmProjectFactoryPageList)({PageSize:1e3,Page:1,Plm_Project_Id:this.form.ProjectId}).then((function(t){t.IsSucceed?(e.factoryOption=t.Data.Data,e.factoryOption.length>0&&(e.form.Factory_Id=e.factoryOption[0].Id)):e.$message({message:t.Message,type:"error"})}))},getArea:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,i.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code}});case 1:a=t.v,a.IsSucceed?e.AreaOption=a.Data:e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,r=a.Type_Id,o=a.ProjectId,l=a.Area_Name,s=a.Factory_Id,u=a.MonomerId,c=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",r),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",o),e.fileFormData.append("Area_Name",l),e.fileFormData.append("Factory_Id",s),e.fileFormData.append("MonomerId",u),e.fileFormData.append("Batches",c),(0,i.AddSynNew)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},bf8b:function(e,t,a){"use strict";a.r(t);var n=a("7c57"),r=a("cf8f");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("50d9");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=l.exports},c174:function(e,t,a){"use strict";a("d7b3")},c1d5:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("2532"),a("159b");var r=n(a("c14f")),o=n(a("2909")),i=n(a("1da1")),l=a("a667"),s=a("e144"),u=(a("3ff8"),a("6186")),c=a("fd31");t.default={props:{typeEntity:{type:Object,default:function(){}},AreaId:{type:String,default:""},ProjectId:{type:String,default:""}},data:function(){return{btnLoading:!1,treeParams:{"default-expand-all":!0,filterable:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},value:"",options:[{key:"Spec",label:"规格",type:"string"},{key:"Length",label:"长度",type:"number"},{key:"Texture",label:"材质",type:"string"},{key:"Weight",label:"单重",type:"number"},{key:"Shape",label:"形状",type:"string"},{key:"Is_Main",label:"是否主零件",type:"array"},{key:"Times",label:"单数",type:"number"},{key:"Remark",label:"备注",type:"string"}],list:[{id:(0,s.v4)(),val:void 0,key:""}],Is_Main_Data:[{Name:"是",Id:!0},{Name:"否",Id:!1}]}},mounted:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getUserableAttr();case 1:return a=e.options.filter((function(e,t){return t})).map((function(e){return e.key})),t.n=2,e.convertCode(e.typeEntity.Code,a,"plm_parts_page_list");case 2:n=t.v,e.options=e.options.map((function(e,t){var a;t&&(e.label=null===(a=n.filter((function(e){return e.Is_Display})).find((function(t){return t.Code===e.key})))||void 0===a?void 0:a.Display_Name);return e})),e.options=(0,o.default)(e.options);case 3:return t.a(2)}}),t)})))()},methods:{getUserableAttr:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetUserableAttr)({IsComponent:!1}).then((function(t){if(t.IsSucceed){var a=t.Data,n=[];a.forEach((function(e){var t={};t.key=e.Code,t.lable=e.Display_Name,t.type="string",n.push(t)})),e.options=e.options.concat(n)}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},init:function(e,t){this.selectList=e;var a=e.filter((function(e){return null!==e.Component_Code&&""!==e.Component_Code}));this.options=a.length>0?this.options.filter((function(e){return"Num"!=e.key})):this.options},handleAdd:function(){this.list.push({id:(0,s.v4)(),val:void 0,key:""})},handleDelete:function(e){this.list.splice(e,1)},onSubmit:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){var a,n,o,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.btnLoading=!0,a=[],n=0;case 1:if(!(n<e.list.length)){t.n=4;break}if(o={},i=e.list[n],i.val){t.n=2;break}return("Length"===i.key||"Num"===i.key||"Weight"===i.key||"Times"===i.key)&&0===i.val?e.$message({message:"值不能为0",type:"warning"}):e.$message({message:"值不能为空",type:"warning"}),e.btnLoading=!1,t.a(2);case 2:o.code=i.key,o.value=i.val,a.push(o);case 3:n++,t.n=1;break;case 4:return t.n=5,(0,l.EditPartpagelist)({Ids:e.selectList.map((function(e){return e.Part_Aggregate_Id})).toString(),Keysmodel:a,Area_Id:e.AreaId,Project_Id:e.ProjectId}).then((function(t){t.IsSucceed?(e.$message({message:"修改成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}));case 5:return t.a(2)}}),t)})))()},filterOption:function(e){var t=this;return this.options.filter((function(a){return(!t.list.map((function(e){return e.key})).includes(a.key)||a.key===e)&&a.label}))},checkType:function(e,t){return!!e&&this.options.find((function(t){return t.key===e})).type===t},getColumnConfiguration:function(e){var t=arguments;return(0,i.default)((0,r.default)().m((function a(){var n,o;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return n=t.length>1&&void 0!==t[1]?t[1]:"plm_parts_page_list",a.n=1,(0,u.GetGridByCode)({code:n+","+e});case 1:return o=a.v,a.a(2,o.Data.ColumnList)}}),a)})))()},convertCode:function(e){var t=arguments,a=this;return(0,i.default)((0,r.default)().m((function n(){var o,i,l,s;return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return o=t.length>1&&void 0!==t[1]?t[1]:[],i=t.length>2?t[2]:void 0,n.n=1,a.getColumnConfiguration(e,i);case 1:return l=n.v,s=l.filter((function(e){var t=o.map((function(e){return e.toLowerCase()}));return t.includes(e.Code.toLowerCase())})),n.a(2,s)}}),n)})))()}}}},c2d4:function(e,t,a){"use strict";a.r(t);var n=a("1d47"),r=a("c924");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"03a6a5c6",null);t["default"]=l.exports},c596:function(e,t,a){"use strict";a.r(t);var n=a("970b"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},c605:function(e,t,a){"use strict";a("b5d4")},c6f9:function(e,t,a){"use strict";a.r(t);var n=a("eddd"),r=a("ff21");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"d6142508",null);t["default"]=l.exports},c75c9:function(e,t,a){"use strict";a("99215")},c7781:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"part-info"},[a("div",[a("span",[a("label",[e._v("零件名称：")]),e._v(e._s(e.rowData.Code))]),a("span",[a("label",[e._v("规格：")]),e._v(e._s(e.rowData.Spec))]),a("i",{class:[e.infoShow?"el-icon-arrow-up":"el-icon-arrow-down"],on:{click:e.showInfoMore}})]),a("div",{directives:[{name:"show",rawName:"v-show",value:e.infoShow,expression:"infoShow"}]},[a("span",[a("label",[e._v("长度：")]),e._v(e._s(e.rowData.Length))]),a("span",[a("label",[e._v("宽度：")]),e._v(e._s(e.rowData.Width))]),a("span",[a("label",[e._v("厚度：")]),e._v(e._s(e.rowData.Thick))]),a("span",[a("label",[e._v("材质：")]),e._v(e._s(e.rowData.Texture))]),a("span",[a("label",[e._v("单重（kg）：")]),e._v(e._s(e.rowData.Weight))]),a("span",[a("label",[e._v("单毛重（kg）：")]),e._v(e._s(e.rowData.Gross_Weight))]),a("span",[a("label",[e._v("数量：")]),e._v(e._s(e.rowData.Num))])])]),a("div",{staticClass:"split-type"},[a("label",[e._v("拆分类型：")]),a("el-radio-group",{model:{value:e.splitType,callback:function(t){e.splitType=t},expression:"splitType"}},[a("el-radio",{attrs:{label:"1"}},[e._v("型材拆分")]),a("el-radio",{attrs:{label:"2"}},[e._v("板材拆分")])],1)],1),a("div",{staticClass:"part-wrapper"},e._l(e.partList,(function(t,n){return a("el-row",{key:n,staticClass:"item-x"},[a("div",{staticClass:"item"},[a("div",[a("label",{attrs:{for:""}},[a("i",[e._v("*")]),e._v("零件名称：")]),a("el-input",{model:{value:t.Code,callback:function(a){e.$set(t,"Code","string"===typeof a?a.trim():a)},expression:"item.Code"}})],1),a("div",[a("label",{attrs:{for:""}},[a("i",[e._v("*")]),e._v("长度：")]),a("el-input",{attrs:{type:"number"},model:{value:t.Length,callback:function(a){e.$set(t,"Length","string"===typeof a?a.trim():a)},expression:"item.Length"}})],1),"2"===e.splitType?a("div",[a("label",{attrs:{for:""}},[e._v("宽度：")]),a("el-input",{attrs:{type:"number"},model:{value:t.Width,callback:function(a){e.$set(t,"Width","string"===typeof a?a.trim():a)},expression:"item.Width"}})],1):e._e(),a("div",[a("label",{attrs:{for:""}},[a("i",[e._v("*")]),e._v("单重（kg）：")]),a("el-input",{attrs:{type:"number"},model:{value:t.Weight,callback:function(a){e.$set(t,"Weight","string"===typeof a?a.trim():a)},expression:"item.Weight"}})],1),a("div",[a("label",{attrs:{for:""}},[e._v("单毛重（kg）：")]),a("el-input",{attrs:{type:"number"},model:{value:t.Gross_Weight,callback:function(a){e.$set(t,"Gross_Weight","string"===typeof a?a.trim():a)},expression:"item.Gross_Weight"}})],1)]),a("span",{staticClass:"item-span"},0===n?[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),a("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(n)}}})])])})),1),a("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)])},r=[]},c7ab:function(e,t,a){"use strict";a.r(t);var n=a("f68a");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);var o,i,l=a("2877"),s=Object(l["a"])(n["default"],o,i,!1,null,null,null);t["default"]=s.exports},c804:function(e,t,a){"use strict";a("86b7")},c924:function(e,t,a){"use strict";a.r(t);var n=a("957c3"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},ca2d:function(e,t,a){"use strict";a.r(t);var n=a("d35c"),r=a("cf52");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("68a9");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"6b8085a8",null);t["default"]=l.exports},caf5:function(e,t,a){"use strict";a.r(t);var n=a("f080"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},cb19:function(e,t,a){"use strict";a.r(t);var n=a("3b7d8"),r=a("88fe");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("b6be2");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"7b11bd9c",null);t["default"]=l.exports},cb29:function(e,t,a){"use strict";var n=a("23e7"),r=a("81d5"),o=a("44d2");n({target:"Array",proto:!0},{fill:r}),o("fill")},cc4c:function(e,t,a){},ccb3:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content"},[a("div",{staticClass:"top-info"},[a("div",[a("el-date-picker",{attrs:{type:"daterange",align:"right","unlink-panels":"","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.searchDate,callback:function(t){e.searchDate=t},expression:"searchDate"}}),a("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("查询")])],1),a("div",[a("el-button",{attrs:{type:"primary",disabled:!e.selectArray.length,loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("导出")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-container"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.multiSelectedChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange},scopedSlots:e._u([{key:"Create_Date",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Create_Date))+" ")]}},{key:"Modify_Date ",fn:function(t){var a=t.row;return[e._v(" "+e._s(e._f("timeFormat")(a.Modify_Date))+" ")]}}])})],1)])},r=[]},cf52:function(e,t,a){"use strict";a.r(t);var n=a("884f"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},cf8f:function(e,t,a){"use strict";a.r(t);var n=a("8a0a"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},d037:function(e,t,a){"use strict";a("d15e")},d15e:function(e,t,a){},d25b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.list,(function(t,n){return a("el-row",{key:t.id,staticClass:"item-x"},[a("div",{staticClass:"item"},[a("label",[e._v(" 属性名称 "),a("el-select",{staticStyle:{width:"calc(100% - 65px)"},attrs:{clearable:"",placeholder:"请选择"},model:{value:t.key,callback:function(a){e.$set(t,"key",a)},expression:"info.key"}},e._l(e.filterOption(t.key),(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)],1)]),a("div",{staticClass:"item",staticStyle:{"line-height":"32px"}},[a("label",[e._v("请输入值 "),e.checkType(t.key,"number")?a("el-input-number",{staticClass:"cs-number-btn-hidden",attrs:{min:0},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"string")?a("el-input",{model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}}):e._e(),e.checkType(t.key,"array")&&"Is_Main"===t.key?a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.val,callback:function(a){e.$set(t,"val",a)},expression:"info.val"}},e._l(e.Is_Main_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1):e._e()],1)]),a("span",{staticClass:"item-span"},0===n?[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}})]:[a("i",{staticClass:"el-icon-circle-plus-outline",on:{click:e.handleAdd}}),a("i",{staticClass:"el-icon-remove-outline txt-red",on:{click:function(t){return e.handleDelete(n)}}})])])})),a("div",{staticStyle:{"text-align":"right",width:"100%",padding:"20px 2% 0 0"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.onSubmit}},[e._v("确定")])],1)],2)},r=[]},d29d:function(e,t,a){"use strict";a.r(t);var n=a("90d3"),r=a("a529");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"65e2adda",null);t["default"]=l.exports},d35c:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"i-dialog",attrs:{"append-to-body":"",visible:e.visible,title:"编辑包信息",width:"30%"},on:{"update:visible":function(t){e.visible=t},close:e.handleClose}},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包编号",prop:"PackageSN"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.PackageSN,callback:function(t){e.$set(e.form,"PackageSN",t)},expression:"form.PackageSN"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"总重量",prop:"AllWeight"}},[a("el-input",{model:{value:e.form.AllWeight,callback:function(t){e.$set(e.form,"AllWeight",t)},expression:"form.AllWeight"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"包名称",prop:"PkgNO"}},[a("el-input",{model:{value:e.form.PkgNO,callback:function(t){e.$set(e.form,"PkgNO",t)},expression:"form.PkgNO"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"项目合同编号",prop:"ContractNo"}},[a("el-input",{model:{value:e.form.ContractNo,callback:function(t){e.$set(e.form,"ContractNo",t)},expression:"form.ContractNo"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"构件类型",prop:"GoodsName"}},[a("el-input",{model:{value:e.form.GoodsName,callback:function(t){e.$set(e.form,"GoodsName",t)},expression:"form.GoodsName"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"收件人",prop:"Addressee"}},[a("el-input",{model:{value:e.form.Addressee,callback:function(t){e.$set(e.form,"Addressee",t)},expression:"form.Addressee"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"体积",prop:"Volume"}},[a("el-input",{model:{value:e.form.Volume,callback:function(t){e.$set(e.form,"Volume",t)},expression:"form.Volume"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"毛重系数",prop:"Gross"}},[a("el-input",{model:{value:e.form.Gross,callback:function(t){e.$set(e.form,"Gross",t)},expression:"form.Gross"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"起运港",prop:"Departure"}},[a("el-input",{model:{value:e.form.Departure,callback:function(t){e.$set(e.form,"Departure",t)},expression:"form.Departure"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"尺寸",prop:"DIM"}},[a("el-input",{model:{value:e.form.DIM,callback:function(t){e.$set(e.form,"DIM",t)},expression:"form.DIM"}})],1)],1),a("el-col",{attrs:{span:24,prop:"Remark"}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},maxlength:50,"show-word-limit":"",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:e.handleClose}},[e._v("关 闭")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1)],1)},r=[]},d457:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"drawer-container"},[a("el-timeline",[a("el-timeline-item",{staticClass:"complete",attrs:{icon:"el-icon-success",size:"large"}},[a("div",{staticClass:"title"},[e._v("深化导入")]),a("div",{staticClass:"content"},[a("div",{staticClass:"info green"},[e._v("导入数量:"+e._s(e.one.Count))]),a("div",{staticClass:"preson"},[e._v(e._s(e.one.Operator))]),a("div",{staticClass:"time"},[e._v(e._s(e.getTime(e.one.OperateTime)))])])]),e.MocContent&&e.MocContent.length?a("el-timeline-item",{staticClass:"complete",attrs:{icon:"el-icon-success",size:"large"}},[a("div",{staticClass:"title"},[e._v("变更")]),a("div",{staticClass:"content"},[a("div",{staticClass:"info "},[e._v(e._s(e.ChangeUserName))]),a("div",{staticClass:"info "},[e._v(e._s(e.ChangeDate))])]),a("div",{staticClass:"content flex-wrap"},e._l(e.MocContent,(function(t,n){return a("div",{key:n,staticClass:"mb-4 info green"},[a("span",{staticClass:"info"},[e._v(e._s(t.ChangeFieldName)+"：")]),a("span",{staticClass:"info"},[e._v(e._s(t.BeforeValue)+" -> ")]),a("span",{staticClass:"info"},[e._v(e._s(t.AfterValue))])])})),0)]):e._e(),a("el-timeline-item",{class:e.getIconOrColor("iconColor",e.two.Status),attrs:{icon:e.getIconOrColor("icon",e.two.Status),size:"large"}},[a("div",{staticClass:"title"},[e._v("排产完成")]),a("div",{staticClass:"content"},[a("div",{class:e.getIconOrColor("color",e.two.Status)},[e._v("排产数量:"+e._s(e.two.Count||0))]),1!==e.two.Status?a("div",{staticClass:"preson"},[e._v(e._s(e.two.Operator))]):e._e(),1!==e.two.Status?a("div",{staticClass:"time"},[e._v(e._s(e.getTime(e.two.OperateTime)))]):e._e()])]),1!==e.two.Status?[e.isMoreSchdule?[a("el-timeline-item",{staticClass:"moreSchdule",class:e.getIconOrColor("iconColor",e.isMoreSchduleStatus),attrs:{icon:e.getIconOrColor("icon",e.isMoreSchduleStatus),size:"large"}},[a("div",{staticClass:"title"},[e._v("所有工序")]),a("div",{staticClass:"inner-content"},e._l(e.three,(function(t,n){return a("div",{key:n},[a("div",{staticClass:"inner-title"},[a("span",{class:["inner-title-icon",e.getIconOrColor("bgIcon",t.Status)]}),a("span",{staticClass:"title"},[e._v(e._s(t.ProcessName))])]),a("div",{staticClass:"zj-x ml15"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.Subs.length>0?e._l(t.Subs,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.Status)},[e._v(e._s(t.Operator)+"完成:"+e._s(t.Count))]),t.Count?a("div",{staticClass:"time"},[e._v(e._s(e.getTime(t.OperateTime)))]):e._e()])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)})),0)])]:e._l(e.three,(function(t,n){return a("el-timeline-item",{key:n,class:e.getIconOrColor("iconColor",t.Status),attrs:{icon:e.getIconOrColor("icon",t.Status),type:t.type,size:"large"}},[a("div",{staticClass:"title"},[e._v(e._s(t.ProcessName))]),a("div",{staticClass:"zj-x"},[t.Check_Finish_Count?a("span",{staticClass:"content-primary"},[e._v("质检完成:"+e._s(t.Check_Finish_Count))]):e._e(),t.Last_Check_UserName?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_UserName))]):e._e(),t.Last_Check_Date?a("span",{staticClass:"content-x"},[e._v(e._s(t.Last_Check_Date))]):e._e()]),t.Subs.length>0?e._l(t.Subs,(function(t,n){return a("div",{key:n,staticClass:"content"},[a("div",{class:e.getIconOrColor("color",t.Status)},[e._v(e._s(t.Operator)+"完成:"+e._s(t.Count))]),t.Count?a("div",{staticClass:"time"},[e._v(e._s(e.getTime(t.OperateTime)))]):e._e()])})):[a("div",{staticClass:"content"},[a("div",{staticClass:"preson"},[e._v("未分配班组")])])]],2)}))]:e._e()],2)],1)},r=[]},d4ac:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={name:"ComponentsHistory",props:{customParams:{type:Object,default:function(){return{}}}},created:function(){var e=this;(0,n.GetComponentChangeHistory)({steelUnique:this.customParams.steelUnique}).then((function(t){e.tableData=t.Data}))}}},d4ea:function(e,t,a){"use strict";a.r(t);var n=a("56da"),r=a("03ff");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,null,null);t["default"]=l.exports},d565:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319");t.default={bind:function(e,t,a){var n=e.querySelector(".el-dialog__header"),r=e.querySelector(".el-dialog");n.style.cssText+=";cursor:move;",r.style.cssText+=";top:0px;";var o=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();n.onmousedown=function(e){var t=e.clientX-n.offsetLeft,i=e.clientY-n.offsetTop,l=r.offsetWidth,s=r.offsetHeight,u=document.body.clientWidth,c=document.body.clientHeight,d=r.offsetLeft,f=u-r.offsetLeft-l,m=r.offsetTop,p=c-r.offsetTop-s,h=o(r,"left"),g=o(r,"top");h.includes("%")?(h=+document.body.clientWidth*(+h.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(h=+h.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var n=e.clientX-t,o=e.clientY-i;-n>d?n=-d:n>f&&(n=f),-o>m?o=-m:o>p&&(o=p),r.style.cssText+=";left:".concat(n+h,"px;top:").concat(o+g,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}},d5af9:function(e,t,a){},d7b3:function(e,t,a){},da6db:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("14d9"),a("e9f5"),a("910d"),a("7d54"),a("d3b7"),a("159b");var n=a("a667");t.default={data:function(){return{form:{Type:"",Remark:""},btnLoading:!1,labelList:[],filterLabelList:[],rules:{Type:{required:!0,message:"请选择",trigger:"change"}}}},methods:{init:function(e){var t=this,a=e.filter((function(e){return e.Sup_Count>0}));this.labelList=[],this.filterLabelList=[],a.forEach((function(e){1===e.Sup_Count&&t.labelList.push({label:e.SteelName,unPackCount:1,SteelUnique:e.SteelUnique,number:1})})),a.forEach((function(e){e.Sup_Count>1&&t.filterLabelList.push({label:e.SteelName,unPackCount:e.Sup_Count,SteelUnique:e.SteelUnique,number:1})}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};e.labelList.concat(e.filterLabelList).forEach((function(e){a.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),"1"===e.form.Type?(a.Package.Remark=e.form.Remark,e.btnLoading=!0,(0,n.Add)(a).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))):e.$emit("checkPackage",{data:a,type:3})}))}}}},dad01:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var r=n(a("c14f")),o=n(a("1da1")),i=a("a667"),l=n(a("bbc2")),s=a("0e9a"),u=a("66f9"),c=a("2d91"),d=a("5e99");t.default={name:"ComponentImport",components:{OSSUpload:l.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,factoryOption:[],typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Factory_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[]}},watch:{typeId:function(e){this.form.Type_Id=e}},mounted:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.getInfo(),t.n=1,e.getMonomerStatus();case 1:return t.n=2,e.getArea();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=2;break}return t.n=2,e.getMonomerList();case 2:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetGetMonomerList)({projectId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName"),this.form.ProjectId=localStorage.getItem("CurReferenceId"),this.getFactory()},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,i.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,s.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getFactory:function(){var e=this;(0,d.GetPlmProjectFactoryPageList)({PageSize:1e3,Page:1,Plm_Project_Id:this.form.ProjectId}).then((function(t){t.IsSucceed?(e.factoryOption=t.Data.Data,e.factoryOption.length>0&&(e.form.Factory_Id=e.factoryOption[0].Id)):e.$message({message:t.Message,type:"error"})}))},getArea:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,i.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code}});case 1:a=t.v,a.IsSucceed?e.AreaOption=a.Data:e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,r=a.Type_Id,o=a.ProjectId,l=a.Area_Name,s=a.Factory_Id,u=a.MonomerId,c=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",r),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",o),e.fileFormData.append("Area_Name",l),e.fileFormData.append("Factory_Id",s),e.fileFormData.append("MonomerId",u),e.fileFormData.append("Batches",c),(0,i.AddSynNew)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},dca8:function(e,t,a){"use strict";var n=a("23e7"),r=a("bb2f"),o=a("d039"),i=a("861d"),l=a("f183").onFreeze,s=Object.freeze,u=o((function(){s(1)}));n({target:"Object",stat:!0,forced:u,sham:!r},{freeze:function(e){return s&&i(e)?s(l(e)):e}})},deca1:function(e,t,a){"use strict";a("60e2")},e010:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pgLoading,expression:"pgLoading"}],staticClass:"h100",staticStyle:{display:"flex"},attrs:{"element-loading-text":"加载中"}},[a("ExpandableSection",{staticClass:"cs-left fff",attrs:{width:300},model:{value:e.showExpand,callback:function(t){e.showExpand=t},expression:"showExpand"}},[a("div",{staticClass:"inner-wrapper"},[a("div",{staticClass:"tree-search"},[a("el-select",{staticClass:"search-select",attrs:{clearable:"",placeholder:"导入状态选择"},model:{value:e.statusType,callback:function(t){e.statusType=t},expression:"statusType"}},[a("el-option",{attrs:{label:"已导入",value:"已导入"}}),a("el-option",{attrs:{label:"未导入",value:"未导入"}}),a("el-option",{attrs:{label:"已变更",value:"已变更"}})],1),a("el-input",{attrs:{placeholder:"关键词搜索",size:"small",clearable:"","suffix-icon":"el-icon-search"},on:{blur:e.fetchTreeDataLocal,clear:e.fetchTreeDataLocal},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fetchTreeDataLocal(t)}},model:{value:e.projectName,callback:function(t){e.projectName="string"===typeof t?t.trim():t},expression:"projectName"}})],1),a("el-divider",{staticClass:"cs-divider"}),a("div",{staticClass:"tree-x cs-scroll"},[a("tree-detail",{ref:"tree",attrs:{icon:"icon-folder","is-custom-filter":"","custom-filter-fun":e.customFilterFun,loading:e.treeLoading,"tree-data":e.treeData,"show-status":"","show-detail":"","filter-text":e.filterText,"expanded-key":e.expandedKey},on:{handleNodeClick:e.handleNodeClick},scopedSlots:e._u([{key:"csLabel",fn:function(t){var n=t.showStatus,r=t.data;return[r.ParentNodes?e._e():a("span",{staticClass:"cs-blue"},[e._v("("+e._s(r.Code)+")")]),e._v(e._s(r.Label)+" "),n&&"全部"!=r.Label?[r.Data.Is_Deepen_Change?a("span",{staticClass:"cs-tag redBg"},[a("i",{staticClass:"fourRed"},[e._v("已变更")])]):a("span",{class:["cs-tag",1==r.Data.Is_Imported?"greenBg":"orangeBg"]},[a("i",{class:[1==r.Data.Is_Imported?"fourGreen":"fourOrange"]},[e._v(e._s(1==r.Data.Is_Imported?"已导入":"未导入"))])])]:e._e()]}}])})],1)],1)]),a("div",{staticClass:"cs-right",staticStyle:{"padding-right":"0"}},[a("div",{staticClass:"container"},[a("div",{ref:"searchDom",staticClass:"cs-from"},[a("div",{staticClass:"cs-search"},[a("el-form",{ref:"customParams",staticClass:"demo-form-inline",attrs:{model:e.customParams,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"零件名称",prop:"Names"}},[a("el-input",{staticClass:"input-with-select",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入内容",size:"small"},model:{value:e.names,callback:function(t){e.names=t},expression:"names"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.nameMode,callback:function(t){e.nameMode=t},expression:"nameMode"}},[a("el-option",{attrs:{label:"模糊搜索",value:1}}),a("el-option",{attrs:{label:"精确搜索",value:2}})],1)],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"零件种类",prop:"Part_Type_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.customParams.Part_Type_Id,callback:function(t){e.$set(e.customParams,"Part_Type_Id",t)},expression:"customParams.Part_Type_Id"}},e._l(e.partTypeOption,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格",prop:"Spec"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Spec,callback:function(t){e.$set(e.customParams,"Spec",t)},expression:"customParams.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质",prop:"Texture"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.Texture,callback:function(t){e.$set(e.customParams,"Texture",t)},expression:"customParams.Texture"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"操作人",prop:"DateName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.customParams.DateName,callback:function(t){e.$set(e.customParams,"DateName",t)},expression:"customParams.DateName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"mb0",attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",clearable:"",placeholder:"请选择",disabled:!Boolean(e.customParams.Area_Id)},model:{value:e.customParams.InstallUnit_Id,callback:function(t){e.$set(e.customParams,"InstallUnit_Id",t)},expression:"customParams.InstallUnit_Id"}},e._l(e.installUnitIdNameList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"是否拼接",prop:"isMontage"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:""},model:{value:e.customParams.isMontage,callback:function(t){e.$set(e.customParams,"isMontage",t)},expression:"customParams.isMontage"}},e._l(e.montageOption,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"mb0",attrs:{"label-width":"16px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handelsearch()}}},[e._v("搜索 ")]),a("el-button",{on:{click:function(t){return e.handelsearch("reset")}}},[e._v("重置")])],1)],1)],1)],1)],1)]),a("div",{staticClass:"fff cs-z-tb-wrapper"},[a("div",{staticClass:"cs-button-box"},[[a("el-button",{attrs:{type:"primary",disabled:!e.selectList.length||1!==e.selectList.length||e.selectList.some((function(e){return e.stopFlag}))},on:{click:e.partSplit}},[e._v("零件拆分")]),a("el-button",{attrs:{disabled:!e.selectList.length},on:{click:e.handleExport}},[e._v("导出零件")]),a("el-button",{attrs:{disabled:!e.selectList.length||e.selectList.some((function(e){return e.stopFlag})),type:"primary",plain:""},on:{click:e.handleBatchEdit}},[e._v("批量编辑")]),a("el-button",{attrs:{type:"success",plain:"",disabled:!Boolean(e.customParams.Sys_Project_Id)},on:{click:e.handelImport}},[e._v("图纸导入 ")])]],2),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.countLoading,expression:"countLoading"}],staticClass:"info-box"},[a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("深化总数")]),a("i",[e._v(e._s(e.SteelAmountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("深化总量")]),a("i",[e._v(e._s(e.SteelAllWeightTotal)+"t")])])]),a("div",{staticClass:"cs-col"},[a("span",[a("span",{staticClass:"info-label"},[e._v("排产总数")]),a("i",[e._v(e._s(e.SchedulingNumTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("排产总量")]),a("i",[e._v(e._s(e.SchedulingAllWeightTotal)+" t")])])]),a("div",{staticClass:"cs-col",staticStyle:{cursor:"pointer"},on:{click:function(t){return e.getProcessData()}}},[a("span",[a("span",{staticClass:"info-label"},[e._v("完成总数")]),a("i",[e._v(e._s(e.FinishCountTotal)+" 件")])]),a("span",[a("span",{staticClass:"info-label"},[e._v("完成总量")]),a("i",[e._v(e._s(e.FinishWeightTotal)+" t")])])])]),a("div",{staticClass:"tb-container"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"44"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,width:t.Width?t.Width:120},scopedSlots:e._u([{key:"default",fn:function(n){var r=n.row;return["Code"==t.Code?a("div",[r.Is_Change?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("变")]):e._e(),r.stopFlag?a("el-tag",{staticStyle:{"margin-right":"8px"},attrs:{type:"danger"}},[e._v("停")]):e._e(),a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.getPartInfo(r)}}},[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])],1):"Is_Split"==t.Code?a("div",[!0===r.Is_Split?a("el-tag",[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])],1):"Deep_Material"==t.Code?a("div",[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handleDeepMaterial(r)}}},[e._v("查看")])],1):"Num"==t.Code&&r[t.Code]>0?a("div",[r[t.Code]?a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+"件")]):a("span",[e._v("-")])]):"Schduling_Count"==t.Code&&r[t.Code]>0?a("div",[r[t.Code]?a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.handelSchduling(r)}}},[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+"件")]):e._e()],1):"Drawing"==t.Code?a("div",["暂无"!==r.Drawing?a("span",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.getPartInfo(r)}}},[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]):a("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]):a("div",[a("span",[e._v(e._s(void 0!==r[t.Code]&&null!==r[t.Code]?r[t.Code]:"-"))])])]}}],null,!0)})})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"150","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("详情")]),a("el-button",{attrs:{disabled:n.stopFlag,type:"text"},on:{click:function(t){return e.handleEdit(n)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleTrack(n)}}},[e._v("轨迹图 ")])]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)])])])],1),a("div",{staticClass:"card"}),e.dialogVisible?a("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"select-list":e.selectList,"custom-params":e.customDialogParams,"type-id":e.customParams.TypeId,"type-entity":e.typeEntity,"project-id":e.customParams.Project_Id,"sys-project-id":e.customParams.Project_Id},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e(),a("bimdialog",{ref:"dialog",attrs:{"type-entity":e.typeEntity,"area-id":e.customParams.Area_Id,"project-id":e.customParams.Project_Id},on:{getData:e.fetchData,getTreeData:e.fetchTreeData}}),a("el-drawer",{attrs:{visible:e.drawersull,direction:"btt",size:"100%","destroy-on-close":""},on:{"update:visible":function(t){e.drawersull=t}}},[e.templateUrl?a("iframe",{staticStyle:{width:"96%","margin-left":"2%",height:"70vh","margin-top":"2%"},attrs:{id:"fullFrame",src:e.templateUrl,frameborder:"0"}}):e._e()]),a("el-drawer",{attrs:{visible:e.trackDrawer,direction:"rtl",size:"30%","destroy-on-close":"","custom-class":"trackDrawerClass"},on:{"update:visible":function(t){e.trackDrawer=t}},scopedSlots:e._u([{key:"title",fn:function(){return[a("div",[a("span",[e._v(e._s(e.trackDrawerTitle))]),a("span",{staticStyle:{"margin-left":"24px"}},[e._v(e._s(e.trackDrawerData.Num))])])]},proxy:!0}])},[a("TracePlot",{attrs:{"track-drawer-data":e.trackDrawerData}})],1),a("comDrawdialog",{ref:"comDrawdialogRef",on:{getData:e.fetchData}}),a("modelDrawing",{ref:"modelDrawingRef",attrs:{type:"零件"}})],1)},r=[]},e0a5:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("ac1f"),a("841c");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=n(a("0f97")),s=n(a("15ac")),u=a("a667");t.default={components:{DynamicDataTable:l.default},mixins:[s.default],props:{search:{type:String,default:""},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{tbConfig:{Pager_Align:"right",Pager_Layout:"total,sizes,prev,pager,next"},queryInfo:{Page:1,PageSize:20,ParameterJson:[]},tbData:[],columns:[],total:0,tbLoading:!1,currentRow:null}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("plm_packages_page_list,".concat(e.typeEntity.Code));case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,u.GetPageStorageBySearchPackages)({PageInfo:(0,r.default)((0,r.default)({},this.queryInfo),{},{Search:this.search}),TypeCode:this.typeEntity.Code}).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.$emit("setSelect",[]),e.$emit("clearRightData","")):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleRowClick:function(e){var t=e.row;this.$emit("leftClick",t.Id),this.handleSelect({row:t})},handleSelect:function(e){var t,a=this,n=e.row;if(n.Id===(null===(t=this.currentRow)||void 0===t?void 0:t.Id))return this.$refs.dyTable.clearSelection(),this.currentRow=null,void this.$emit("selectList",[]);this.currentRow=n,this.$refs.dyTable.clearSelection(),this.$nextTick((function(e){a.$refs.dyTable.toggleRowSelection(n)})),this.$emit("selectList",[n])},selectAll:function(){this.$refs.dyTable.clearSelection()}}}},e0cf:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.show?a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("项目信息")])]),e.getLabel("Project_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Project_Name"),prop:"Project_Id"}},[a("el-select",{ref:"Project_Name",staticStyle:{width:"100%"},attrs:{disabled:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("Area_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Area_Name"),prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"w100",attrs:{"tree-params":e.treeParamsArea,disabled:"",placeholder:"请选择"},on:{"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1):e._e(),e.getLabel("InstallUnit_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("InstallUnit_Name"),prop:"InstallUnit_Id"}},[a("el-select",{ref:"InstallUnit_Name",staticStyle:{width:"100%"},attrs:{disabled:"",clearable:"",placeholder:"请选择"},on:{change:e.setupPositionChange},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("基础信息")])]),e.getLabel("Code")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Code"),prop:"Code"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1)],1):e._e(),e.getLabel("Spec")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Spec"),prop:"Spec"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1)],1):e._e(),e.getLabel("Length")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Length"),prop:"Length"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length",t)},expression:"form.Length"}})],1)],1):e._e(),e.getLabel("Width")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Width"),prop:"Width"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width",t)},expression:"form.Width"}})],1)],1):e._e(),e.getLabel("Texture")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Texture"),prop:"Texture"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Texture,callback:function(t){e.$set(e.form,"Texture",t)},expression:"form.Texture"}})],1)],1):e._e(),e.getLabel("Num")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Num"),prop:"Num"}},[a("el-input",{attrs:{disabled:""},on:{input:e.calculationAllWeight},model:{value:e.form.Num,callback:function(t){e.$set(e.form,"Num",t)},expression:"form.Num"}})],1)],1):e._e(),e.getLabel("Schduling_Count")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Schduling_Count"),prop:"Schduling_Count"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Schduling_Count,callback:function(t){e.$set(e.form,"Schduling_Count",t)},expression:"form.Schduling_Count"}})],1)],1):e._e(),e.getLabel("Shape")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Shape"),prop:"Shape"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Shape,callback:function(t){e.$set(e.form,"Shape",t)},expression:"form.Shape"}})],1)],1):e._e(),e.getLabel("Weight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Weight"),prop:"Weight"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.Schduling_Count>0},on:{input:e.calculationAllWeight},model:{value:e.form.Weight,callback:function(t){e.$set(e.form,"Weight",t)},expression:"form.Weight"}})],1)],1):e._e(),e.getLabel("Gross_Weight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Gross_Weight"),prop:"Gross_Weight"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Gross_Weight,callback:function(t){e.$set(e.form,"Gross_Weight",t)},expression:"form.Gross_Weight"}})],1)],1):e._e(),e.getLabel("Type_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Type_Name"),prop:"Type_Name"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1)],1):e._e(),e.getLabel("Part_Pattern")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Part_Pattern"),prop:"Part_Pattern"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Part_Pattern,callback:function(t){e.$set(e.form,"Part_Pattern",t)},expression:"form.Part_Pattern"}})],1)],1):e._e(),e.getLabel("Technology_Code")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Technology_Code"),prop:"Technology_Code"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Technology_Code,callback:function(t){e.$set(e.form,"Technology_Code",t)},expression:"form.Technology_Code"}})],1)],1):e._e(),e.getLabel("Technology_Remark")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Technology_Remark"),prop:"Technology_Remark"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Technology_Remark,callback:function(t){e.$set(e.form,"Technology_Remark",t)},expression:"form.Technology_Remark"}})],1)],1):e._e(),e.getLabel("Texture_Replacement")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Texture_Replacement"),prop:"Texture_Replacement"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Texture_Replacement,callback:function(t){e.$set(e.form,"Texture_Replacement",t)},expression:"form.Texture_Replacement"}})],1)],1):e._e(),e.getLabel("Spec_Replacement")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Spec_Replacement"),prop:"Spec_Replacement"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Spec_Replacement,callback:function(t){e.$set(e.form,"Spec_Replacement",t)},expression:"form.Spec_Replacement"}})],1)],1):e._e(),e.getLabel("Margin")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Margin"),prop:"Margin"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Margin,callback:function(t){e.$set(e.form,"Margin",t)},expression:"form.Margin"}})],1)],1):e._e(),e.getLabel("EA_Number")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("EA_Number"),prop:"EA_Number"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.EA_Number,callback:function(t){e.$set(e.form,"EA_Number",t)},expression:"form.EA_Number"}})],1)],1):e._e(),e.getLabel("Dxf_Url")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Dxf_Url"),prop:"Dxf_Url"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Dxf_Url,callback:function(t){e.$set(e.form,"Dxf_Url",t)},expression:"form.Dxf_Url"}})],1)],1):e._e(),e.getLabel("ABM")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("ABM"),prop:"ABM"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.ABM,callback:function(t){e.$set(e.form,"ABM",t)},expression:"form.ABM"}})],1)],1):e._e(),e.getLabel("PayCode")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("PayCode"),prop:"PayCode"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.PayCode,callback:function(t){e.$set(e.form,"PayCode",t)},expression:"form.PayCode"}})],1)],1):e._e(),e.getLabel("Layer")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Layer"),prop:"Layer"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Layer,callback:function(t){e.$set(e.form,"Layer",t)},expression:"form.Layer"}})],1)],1):e._e(),e.getLabel("Is_Main")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Is_Main"),prop:"Is_Main"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0,clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Main,callback:function(t){e.$set(e.form,"Is_Main",t)},expression:"form.Is_Main"}},e._l(e.Is_Main_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("Times")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Times"),prop:"Times"}},[a("el-input",{attrs:{type:"number","on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))",disabled:e.isReadOnly||e.form.Schduling_Count>0},on:{input:e.calculationNum},model:{value:e.form.Times,callback:function(t){e.$set(e.form,"Times",t)},expression:"form.Times"}})],1)],1):e._e(),e.getLabel("Component_Code")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Component_Code"),prop:"Component_Code"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Component_Code,callback:function(t){e.$set(e.form,"Component_Code",t)},expression:"form.Component_Code"}})],1)],1):e._e(),e.getLabel("DateName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("DateName"),prop:"DateName"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.DateName,callback:function(t){e.$set(e.form,"DateName",t)},expression:"form.DateName"}})],1)],1):e._e(),e.getLabel("Exdate")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Exdate"),prop:"Exdate"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Exdate,callback:function(t){e.$set(e.form,"Exdate",t)},expression:"form.Exdate"}})],1)],1):e._e(),e.getLabel("Remark")?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:e.getLabel("Remark"),prop:"Remark"}},[a("el-input",{attrs:{disabled:e.isReadOnly||this.form.Schduling_Count>0},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1):e._e()],1),e.extendField.length>0?a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("拓展字段")])]),e._l(e.extendField,(function(t,n){return a("el-col",{key:n,attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel(t.Code),prop:t.Code}},[a("el-input",{attrs:{disabled:e.isReadOnly},model:{value:e.extendform[t.Code],callback:function(a){e.$set(e.extendform,t.Code,a)},expression:"extendform[item.Code]"}})],1)],1)}))],2):e._e(),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),e.isReadOnly?e._e():a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1):e._e()],1)},r=[]},e144:function(e,t,a){"use strict";a.r(t),a.d(t,"v1",(function(){return c})),a.d(t,"v3",(function(){return N})),a.d(t,"v4",(function(){return R["a"]})),a.d(t,"v5",(function(){return F})),a.d(t,"NIL",(function(){return U})),a.d(t,"version",(function(){return B})),a.d(t,"validate",(function(){return d["a"]})),a.d(t,"stringify",(function(){return i["a"]})),a.d(t,"parse",(function(){return m}));var n,r,o=a("d8f8"),i=a("58cf"),l=0,s=0;function u(e,t,a){var u=t&&a||0,c=t||new Array(16);e=e||{};var d=e.node||n,f=void 0!==e.clockseq?e.clockseq:r;if(null==d||null==f){var m=e.random||(e.rng||o["a"])();null==d&&(d=n=[1|m[0],m[1],m[2],m[3],m[4],m[5]]),null==f&&(f=r=16383&(m[6]<<8|m[7]))}var p=void 0!==e.msecs?e.msecs:Date.now(),h=void 0!==e.nsecs?e.nsecs:s+1,g=p-l+(h-s)/1e4;if(g<0&&void 0===e.clockseq&&(f=f+1&16383),(g<0||p>l)&&void 0===e.nsecs&&(h=0),h>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");l=p,s=h,r=f,p+=122192928e5;var b=(1e4*(268435455&p)+h)%4294967296;c[u++]=b>>>24&255,c[u++]=b>>>16&255,c[u++]=b>>>8&255,c[u++]=255&b;var y=p/4294967296*1e4&268435455;c[u++]=y>>>8&255,c[u++]=255&y,c[u++]=y>>>24&15|16,c[u++]=y>>>16&255,c[u++]=f>>>8|128,c[u++]=255&f;for(var _=0;_<6;++_)c[u+_]=d[_];return t||Object(i["a"])(c)}var c=u,d=a("06e4");function f(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");var t,a=new Uint8Array(16);return a[0]=(t=parseInt(e.slice(0,8),16))>>>24,a[1]=t>>>16&255,a[2]=t>>>8&255,a[3]=255&t,a[4]=(t=parseInt(e.slice(9,13),16))>>>8,a[5]=255&t,a[6]=(t=parseInt(e.slice(14,18),16))>>>8,a[7]=255&t,a[8]=(t=parseInt(e.slice(19,23),16))>>>8,a[9]=255&t,a[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,a[11]=t/4294967296&255,a[12]=t>>>24&255,a[13]=t>>>16&255,a[14]=t>>>8&255,a[15]=255&t,a}var m=f;function p(e){e=unescape(encodeURIComponent(e));for(var t=[],a=0;a<e.length;++a)t.push(e.charCodeAt(a));return t}var h="6ba7b810-9dad-11d1-80b4-00c04fd430c8",g="6ba7b811-9dad-11d1-80b4-00c04fd430c8",b=function(e,t,a){function n(e,n,r,o){if("string"===typeof e&&(e=p(e)),"string"===typeof n&&(n=m(n)),16!==n.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var l=new Uint8Array(16+e.length);if(l.set(n),l.set(e,n.length),l=a(l),l[6]=15&l[6]|t,l[8]=63&l[8]|128,r){o=o||0;for(var s=0;s<16;++s)r[o+s]=l[s];return r}return Object(i["a"])(l)}try{n.name=e}catch(r){}return n.DNS=h,n.URL=g,n};function y(e){if("string"===typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var a=0;a<t.length;++a)e[a]=t.charCodeAt(a)}return _(P(C(e),8*e.length))}function _(e){for(var t=[],a=32*e.length,n="0123456789abcdef",r=0;r<a;r+=8){var o=e[r>>5]>>>r%32&255,i=parseInt(n.charAt(o>>>4&15)+n.charAt(15&o),16);t.push(i)}return t}function v(e){return 14+(e+64>>>9<<4)+1}function P(e,t){e[t>>5]|=128<<t%32,e[v(t)-1]=t;for(var a=1732584193,n=-271733879,r=-1732584194,o=271733878,i=0;i<e.length;i+=16){var l=a,s=n,u=r,c=o;a=D(a,n,r,o,e[i],7,-680876936),o=D(o,a,n,r,e[i+1],12,-389564586),r=D(r,o,a,n,e[i+2],17,606105819),n=D(n,r,o,a,e[i+3],22,-1044525330),a=D(a,n,r,o,e[i+4],7,-176418897),o=D(o,a,n,r,e[i+5],12,1200080426),r=D(r,o,a,n,e[i+6],17,-1473231341),n=D(n,r,o,a,e[i+7],22,-45705983),a=D(a,n,r,o,e[i+8],7,1770035416),o=D(o,a,n,r,e[i+9],12,-1958414417),r=D(r,o,a,n,e[i+10],17,-42063),n=D(n,r,o,a,e[i+11],22,-1990404162),a=D(a,n,r,o,e[i+12],7,1804603682),o=D(o,a,n,r,e[i+13],12,-40341101),r=D(r,o,a,n,e[i+14],17,-1502002290),n=D(n,r,o,a,e[i+15],22,1236535329),a=k(a,n,r,o,e[i+1],5,-165796510),o=k(o,a,n,r,e[i+6],9,-1069501632),r=k(r,o,a,n,e[i+11],14,643717713),n=k(n,r,o,a,e[i],20,-373897302),a=k(a,n,r,o,e[i+5],5,-701558691),o=k(o,a,n,r,e[i+10],9,38016083),r=k(r,o,a,n,e[i+15],14,-660478335),n=k(n,r,o,a,e[i+4],20,-405537848),a=k(a,n,r,o,e[i+9],5,568446438),o=k(o,a,n,r,e[i+14],9,-1019803690),r=k(r,o,a,n,e[i+3],14,-187363961),n=k(n,r,o,a,e[i+8],20,1163531501),a=k(a,n,r,o,e[i+13],5,-1444681467),o=k(o,a,n,r,e[i+2],9,-51403784),r=k(r,o,a,n,e[i+7],14,1735328473),n=k(n,r,o,a,e[i+12],20,-1926607734),a=L(a,n,r,o,e[i+5],4,-378558),o=L(o,a,n,r,e[i+8],11,-2022574463),r=L(r,o,a,n,e[i+11],16,1839030562),n=L(n,r,o,a,e[i+14],23,-35309556),a=L(a,n,r,o,e[i+1],4,-1530992060),o=L(o,a,n,r,e[i+4],11,1272893353),r=L(r,o,a,n,e[i+7],16,-155497632),n=L(n,r,o,a,e[i+10],23,-1094730640),a=L(a,n,r,o,e[i+13],4,681279174),o=L(o,a,n,r,e[i],11,-358537222),r=L(r,o,a,n,e[i+3],16,-722521979),n=L(n,r,o,a,e[i+6],23,76029189),a=L(a,n,r,o,e[i+9],4,-640364487),o=L(o,a,n,r,e[i+12],11,-421815835),r=L(r,o,a,n,e[i+15],16,530742520),n=L(n,r,o,a,e[i+2],23,-995338651),a=w(a,n,r,o,e[i],6,-198630844),o=w(o,a,n,r,e[i+7],10,1126891415),r=w(r,o,a,n,e[i+14],15,-1416354905),n=w(n,r,o,a,e[i+5],21,-57434055),a=w(a,n,r,o,e[i+12],6,1700485571),o=w(o,a,n,r,e[i+3],10,-1894986606),r=w(r,o,a,n,e[i+10],15,-1051523),n=w(n,r,o,a,e[i+1],21,-2054922799),a=w(a,n,r,o,e[i+8],6,1873313359),o=w(o,a,n,r,e[i+15],10,-30611744),r=w(r,o,a,n,e[i+6],15,-1560198380),n=w(n,r,o,a,e[i+13],21,1309151649),a=w(a,n,r,o,e[i+4],6,-145523070),o=w(o,a,n,r,e[i+11],10,-1120210379),r=w(r,o,a,n,e[i+2],15,718787259),n=w(n,r,o,a,e[i+9],21,-343485551),a=S(a,l),n=S(n,s),r=S(r,u),o=S(o,c)}return[a,n,r,o]}function C(e){if(0===e.length)return[];for(var t=8*e.length,a=new Uint32Array(v(t)),n=0;n<t;n+=8)a[n>>5]|=(255&e[n/8])<<n%32;return a}function S(e,t){var a=(65535&e)+(65535&t),n=(e>>16)+(t>>16)+(a>>16);return n<<16|65535&a}function I(e,t){return e<<t|e>>>32-t}function T(e,t,a,n,r,o){return S(I(S(S(t,e),S(n,o)),r),a)}function D(e,t,a,n,r,o,i){return T(t&a|~t&n,e,t,r,o,i)}function k(e,t,a,n,r,o,i){return T(t&n|a&~n,e,t,r,o,i)}function L(e,t,a,n,r,o,i){return T(t^a^n,e,t,r,o,i)}function w(e,t,a,n,r,o,i){return T(a^(t|~n),e,t,r,o,i)}var x=y,O=b("v3",48,x),N=O,R=a("ec26");function G(e,t,a,n){switch(e){case 0:return t&a^~t&n;case 1:return t^a^n;case 2:return t&a^t&n^a&n;case 3:return t^a^n}}function j(e,t){return e<<t|e>>>32-t}function A(e){var t=[1518500249,1859775393,2400959708,3395469782],a=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"===typeof e){var n=unescape(encodeURIComponent(e));e=[];for(var r=0;r<n.length;++r)e.push(n.charCodeAt(r))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var o=e.length/4+2,i=Math.ceil(o/16),l=new Array(i),s=0;s<i;++s){for(var u=new Uint32Array(16),c=0;c<16;++c)u[c]=e[64*s+4*c]<<24|e[64*s+4*c+1]<<16|e[64*s+4*c+2]<<8|e[64*s+4*c+3];l[s]=u}l[i-1][14]=8*(e.length-1)/Math.pow(2,32),l[i-1][14]=Math.floor(l[i-1][14]),l[i-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<i;++d){for(var f=new Uint32Array(80),m=0;m<16;++m)f[m]=l[d][m];for(var p=16;p<80;++p)f[p]=j(f[p-3]^f[p-8]^f[p-14]^f[p-16],1);for(var h=a[0],g=a[1],b=a[2],y=a[3],_=a[4],v=0;v<80;++v){var P=Math.floor(v/20),C=j(h,5)+G(P,g,b,y)+_+t[P]+f[v]>>>0;_=y,y=b,b=j(g,30)>>>0,g=h,h=C}a[0]=a[0]+h>>>0,a[1]=a[1]+g>>>0,a[2]=a[2]+b>>>0,a[3]=a[3]+y>>>0,a[4]=a[4]+_>>>0}return[a[0]>>24&255,a[0]>>16&255,a[0]>>8&255,255&a[0],a[1]>>24&255,a[1]>>16&255,a[1]>>8&255,255&a[1],a[2]>>24&255,a[2]>>16&255,a[2]>>8&255,255&a[2],a[3]>>24&255,a[3]>>16&255,a[3]>>8&255,255&a[3],a[4]>>24&255,a[4]>>16&255,a[4]>>8&255,255&a[4]]}var $=A,M=b("v5",80,$),F=M,U="00000000-0000-0000-0000-000000000000";function E(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}var B=E},e322:function(e,t,a){"use strict";a.r(t);var n=a("0e5d"),r=a("f387");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("039b"),a("a54c");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"5f199130",null);t["default"]=l.exports},e41b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteByIds=s,t.GetPartsImportTemplate=c,t.GetPartsList=l,t.GetProjectAreaTreeList=o,t.ImportParts=u,t.SaveProjectAreaSort=i;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:e})}},e55d:function(e,t,a){"use strict";a.r(t);var n=a("5833"),r=a("a55d");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("c75c9");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"5ceac44f",null);t["default"]=l.exports},e612:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"custom-tb cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,"pager-count":5,"small-pagination":"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,handleRowClick:e.handleRowClick,select:e.handleSelect,selectAll:e.selectAll,tableSearch:e.tableSearch}})],1)},r=[]},ea62:function(e,t,a){"use strict";a("1a23")},ecba:function(e,t,a){},eddd:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"h100"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.handleSelect,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch}})],1)},r=[]},f080:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("7db0"),a("e9f5"),a("f665"),a("d3b7");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),l=a("a667"),s=n(a("bbc2")),u=a("0e9a"),c=a("66f9"),d=a("2c61"),f=a("1b69"),m=a("f2f6"),p=a("2d91");t.default={name:"ComponentImport",components:{OSSUpload:s.default},props:{typeId:{type:String,default:""},typeEntity:{type:Object,default:function(){}}},data:function(){return{form:{Project_Name:"",Type_Id:"",Factory_Id:"",Project_Id:"",Area_Name:"",fileName:"",MonomerId:"",Batches:"",IsUnifyPartiotion:!0},listType:"plm_steels_modelImport",templateUrl:"",loading:!1,btnLoading:!1,typeOption:[],AreaOption:[],fileFormData:new FormData,rules:{Project_Id:[{required:!0,message:"请选择",trigger:"change"}],Area_Name:[{required:!0,message:"请选择",trigger:"change"}],MonomerId:[{required:!0,message:"请选择",trigger:"change"}]},showMonomer:!1,MonomerList:[],projectList:[],unitList:[]}},watch:{typeId:function(e){this.form.Type_Id=e},"form.MonomerId":function(){this.getArea()},"form.Area_Name":function(e){e&&this.getUnitList()}},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Type_Id=e.typeId,e.form.Factory_Id=localStorage.getItem("CurReferenceId"),e.getInfo(),t.n=1,e.getProjectList();case 1:return t.n=2,e.getMonomerStatus();case 2:return t.a(2)}}),t)})))()},methods:{unityInfo:function(e){e&&(this.form.MonomerId=null,this.form.Area_Name=null,this.form.Factory_Id=null),this.rules.MonomerId[0].required=e,this.rules.Area_Name[0].required=e,this.rules.Factory_Id[0].required=e},getMonomerStatus:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,p.GetPreferenceSettingValue)({code:"Monomer"});case 1:if(a=t.v,e.showMonomer="1"===a.Data,!e.showMonomer){t.n=3;break}return t.n=2,e.getMonomerList();case 2:t.n=4;break;case 3:e.getArea();case 4:return t.a(2)}}),t)})))()},getMonomerList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Batches="",e.form.MonomerId="",e.form.Area_Name="",t.n=1,(0,c.GetProMonomerList)({projectId:e.form.Project_Id,id:e.projectList.find((function(t){return t.Sys_Project_Id===e.form.Project_Id})).Id});case 1:a=t.v,e.MonomerList=a.Data,e.form.MonomerId=a.Data?a.Data[0].Id:"";case 2:return t.a(2)}}),t)})))()},getUnitList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,n,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return a={Sys_Project_Id:e.form.Project_Id},e.isUnityInfo&&(a=(0,r.default)((0,r.default)({},a),{},{Area_Id:null===(n=e.AreaOption.find((function(t){return t.Name===e.form.Area_Name})))||void 0===n?void 0:n.Id})),t.n=1,(0,m.GetInstallUnitList)(a);case 1:i=t.v,e.unitList=i.Data;case 2:return t.a(2)}}),t)})))()},getProjectList:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetProjectList)({});case 1:a=t.v,a.IsSucceed?(e.projectList=a.Data,e.projectList.length>0&&(e.form.Project_Id=e.projectList[0].Sys_Project_Id)):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getInfo:function(){this.form.Project_Name=localStorage.getItem("ProjectName")},typeSelectHandle:function(e){var t=this;e&&this.getTemplate(this.typeOption.find((function(e){return e.Id===t.form.Type_Id})).Code)},getTemplate:function(){var e=this,t="".concat(this.listType,",").concat(this.typeEntity.Code),a=("plm_steels_detailImport"===this.listType?"深化清单导入":"模型清单导入")+"模板";(0,l.SteelBardcodeDataTemplate)({templateCode:t}).then((function(t){(0,u.downloadBlobFile)(t,"".concat(e.typeEntity.Name,"_").concat(a))}))},getArea:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.form.Area_Name="",t.n=1,(0,l.GetAreaList)({model:{MonomerId:e.form.MonomerId,ProfessionalCode:e.typeEntity.Code,ProjectId:e.form.Project_Id}});case 1:a=t.v,a.IsSucceed?(e.AreaOption=a.Data,e.form.Area_Name=a.Data[0].Name):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},beforeUpload:function(e){this.loading=!0},uploadSuccess:function(e,t){if(e&&e.Data&&!(e.Data.length<4)){var a=e.Data.split("*"),n={File_Url:a[0],File_Size:a[1],File_Type:a[2],File_Name:a[3]};this.form.fileName=n.File_Name,this.fileFormData.append("File_Url",n.File_Url),this.fileFormData.append("files",t.raw),this.loading=!1}},onSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.btnLoading=!0;var a=e.form,n=a.Project_Name,r=a.Type_Id,o=a.Project_Id,i=a.Area_Name,l=a.Factory_Id,s=a.MonomerId,u=a.Batches;e.fileFormData.append("Project_Name",n),e.fileFormData.append("Type_Id",r),e.fileFormData.append("Type_Code",e.typeEntity.Code),e.fileFormData.append("Project_Id",e.projectList.find((function(e){return e.Sys_Project_Id===o})).Id),e.fileFormData.append("Area_Name",i),e.fileFormData.append("Factory_Id",l),e.fileFormData.append("MonomerId",s),e.fileFormData.append("Batches",u),(0,d.CommonImportModelToComp)(e.fileFormData).then((function(t){t.IsSucceed?(e.$message({message:t.Message,type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))}}}},f151:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("b9eb")),o=function(e){e.directive("permission",r.default)};window.Vue&&(window["sysUseType"]=r.default,Vue.use(o)),r.default.install=o;t.default=r.default},f1a7:function(e,t,a){},f1eb:function(e,t,a){"use strict";a.r(t);var n=a("243b"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},f2f6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=s,t.CheckPlanTime=u,t.DeleteInstallUnit=m,t.GetCompletePercent=y,t.GetEntity=v,t.GetInstallUnitAllInfo=d,t.GetInstallUnitComponentPageList=b,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=c,t.GetInstallUnitList=l,t.GetInstallUnitPageList=i,t.GetProjectInstallUnitList=_,t.ImportInstallUnit=h,t.InstallUnitInfoTemplate=p,t.SaveInstallUnit=g,t.SaveOhterSourceInstallUnit=P;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function u(e){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function c(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function d(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function m(e){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function p(e){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function h(e){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(e)})}function P(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},f300:function(e,t,a){"use strict";a.r(t);var n=a("738b"),r=a("2495");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"6aaab5c6",null);t["default"]=l.exports},f382:function(e,t,a){"use strict";function n(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=n(e.Children)),!0)}))}function r(e){e.map((function(e){if(e.Is_Directory||!e.Children)return r(e.Children);delete e.Children}))}function o(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function n(e,t,r){for(var o=0;o<e.length;o++){var i=e[o];if(i.Id===t)return a&&r.push(i),r;if(i.Children&&i.Children.length){if(r.push(i),n(i.Children,t,r).length)return r;r.pop()}}return[]}return n(e,t,[])}function i(e){return e.Children&&e.Children.length>0?i(e.Children[0]):e}function l(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&l(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=r,t.disableDirectory=l,t.findAllParentNode=o,t.findFirstNode=i,t.getDirectoryTree=n,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7")},f387:function(e,t,a){"use strict";a.r(t);var n=a("3300"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},f38c:function(e,t,a){"use strict";a.r(t);var n=a("19ad"),r=a("3228");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"65e4a412",null);t["default"]=l.exports},f397:function(e,t,a){},f39a:function(e,t,a){"use strict";a.r(t);var n=a("5917"),r=a("f1eb");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("c174");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"480a00b2",null);t["default"]=l.exports},f68a:function(e,t,a){"use strict";a.r(t);var n=a("0ce7"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},f7b1:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("a667");t.default={name:"ComponentsHistory",props:{customParams:{type:Object,default:function(){return{}}}},created:function(){var e=this;(0,n.GetComponentChangeHistory)({steelUnique:this.customParams.steelUnique}).then((function(t){e.tableData=t.Data}))}}},f7b46:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("e9f5"),a("d866"),a("7d54"),a("ab43"),a("e9c4"),a("d3b7"),a("25f0"),a("159b");var r=n(a("34e9")),o=n(a("3669")),i=n(a("c6f9")),l=n(a("15ac")),s=n(a("8489")),u=a("a667");t.default={components:{TopHeader:r.default,zLeft:o.default,Dialog:s.default,zRight:i.default},mixins:[l.default],props:{selectList:{type:Array,default:function(){return[]}},typeEntity:{type:Object,default:function(){return{}}}},data:function(){return{search:"",search2:"",select:"",dialogVisible:!1,leftSelectList:[],rightSelectList:[],columnOption:[],currentPackId:"",submitObj:{},fromType:2}},methods:{handleClose:function(){this.dialogVisible=!1},clearRightData:function(){this.currentPackId="",this.fetchRight()},fetchLeft:function(){this.$refs["left"].fetchData()},fetchRight:function(){this.$refs["right"].fetchRightData(this.currentPackId)},leftClick:function(e){this.currentPackId=e,this.fetchRight()},handleDeleteLeft:function(){var e=this;this.$confirm("是否删除构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.DeletePackage)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString()}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},getColumn:function(e){this.columnOption=e},handleDeleteRight:function(){var e=this;this.$confirm("是否删除该构件?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.DeleteSteel)({packageId:e.leftSelectList.map((function(e){return e.Id})).toString(),itemId:JSON.stringify(e.rightSelectList.map((function(e){return e.Id})))}).then((function(t){e.$message({type:"success",message:"删除成功!"}),e.fetchLeft(),e.$refs["right"].fetchRightData()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(){var e=this;this.dialogVisible=!0,this.$nextTick((function(t){e.$refs["dialog"].fetchData(e.leftSelectList)}))},getSubmitObj:function(e){this.submitObj=e},handleAdd:function(){var e=this;if(this.selectList.length||2!==this.fromType)if(this.leftSelectList.length){var t=this.selectList.every((function(e){return 1===e.Sup_Count&&1===e.SteelAmount}));t||2!==this.fromType?this.$confirm("确定加入此构件包?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitObj.Package.Id=e.leftSelectList.map((function(e){return e.Id})).toString(),1===e.fromType?(0,u.BatchAdd)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"})})):(0,u.AddSteel)(e.submitObj).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))})).catch((function(){e.$message({type:"info",message:"已取消"})})):this.$message({message:"请点击生成构件包",type:"warning"})}else this.$message({message:"请先选择包",type:"warning"});else this.$message({message:"请先选择构件",type:"warning"})},getLeftList:function(e){this.leftSelectList=e},getRightList:function(e){this.rightSelectList=e},handlePackage:function(e){if(this.fromType=e,2===e){var t={Package:{ProjectID:localStorage.getItem("CurReferenceId")},Steels:[]};this.selectList.forEach((function(e){t.Steels.push({SteelUnique:e.SteelUnique,SteelAmount:e.number})})),this.submitObj=t}}}}},f802:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"570px",top:"5vh",loading:e.loading},on:{"update:visible":function(t){e.dialogVisible=t},submitbtn:function(t){return e.handleSubmit("form")},cancelbtn:e.handleClose,handleClose:e.handleClose,close:e.handleClose}},[a("div"),e.isDeep||e.isSHQD?a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:e.getTemplate}},[e._v("下载零件导入模板")])],1):e._e(),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"下载模板",prop:"Template_Type"}},[a("el-radio",{attrs:{label:2},model:{value:e.form.Template_Type,callback:function(t){e.$set(e.form,"Template_Type",t)},expression:"form.Template_Type"}},[e._v("固定模板")]),a("el-radio",{attrs:{label:1},model:{value:e.form.Template_Type,callback:function(t){e.$set(e.form,"Template_Type",t)},expression:"form.Template_Type"}},[e._v("动态模板")])],1),a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Project_Name,callback:function(t){e.$set(e.form,"Project_Name",t)},expression:"form.Project_Name"}})],1),a("el-form-item",{attrs:{label:"区域",prop:"Area_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Area_Name,callback:function(t){e.$set(e.form,"Area_Name",t)},expression:"form.Area_Name"}})],1),a("el-form-item",{attrs:{label:"类别名称",prop:"Type_Name"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"Doc_Title"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Title,callback:function(t){e.$set(e.form,"Doc_Title",t)},expression:"form.Doc_Title"}})],1),a("el-form-item",{attrs:{label:"简要描述",prop:"Doc_Content"}},[a("el-input",{staticStyle:{width:"360px"},model:{value:e.form.Doc_Content,callback:function(t){e.$set(e.form,"Doc_Content",t)},expression:"form.Doc_Content"}})],1),a("el-form-item",{attrs:{label:"附件信息",prop:"Doc_File"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{disabled:""},model:{value:e.form.Doc_File,callback:function(t){e.$set(e.form,"Doc_File",t)},expression:"form.Doc_File"}})],1),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{ref:"company",staticClass:"upload-demo",attrs:{drag:"",action:e.$store.state.uploadUrl,"on-change":e.handleChange,"before-upload":e.beforeUpload,"file-list":e.fileList,limit:2,"on-success":e.uploadSuccess,"on-error":e.uploadError,"before-remove":e.beforeRemove,"on-remove":e.handleRemove,multiple:!1,accept:e.allowFile}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},r=[]},fc05:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("14d9"),a("fb6a"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("d3b7"),a("ac1f"),a("5319");var r=n(a("c14f")),o=n(a("1da1")),i=n(a("5530")),l=a("66f9"),s=a("586a"),u=n(a("bbc2")),c=a("a667"),d=a("ed08"),f={Id:"",Doc_Catelog:"",Doc_Type:"",Project_Name:"",Project_Id:"",Sys_Project_Id:"",Area_Name:"",Area_Id:"",Type_Name:"",Doc_Title:"",Doc_Content:"",Is_Load:!1,Doc_File:"",Professional_Code:"",Template_Type:2,File_Url:""};t.default={components:{OSSUpload:u.default},props:{typeEntity:{type:Object,default:function(){}}},data:function(){return{type:"",allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",fileList:[],dialogVisible:!1,title:"上传文件",loading:!1,form:(0,i.default)({},f),attachments:[],rules:{Doc_Title:[{required:!0,message:"请输入标题",trigger:"blur"}]},treeParams:{clickParent:!0,filterable:!1,"check-strictly":!0,"default-expand-all":!0,"expand-on-click-node":!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},selectParams:{multiple:!1,clearable:!0,placeholder:"请输入内容"},fileType:"",curFile:"",bimvizId:"",isDeep:!1,projectId:"",isSHQD:"",btnLoading:!1,command:"cover"}},created:function(){this.fileType=this.$route.name,"PLMPicVideoFiles"===this.fileType&&(this.allowFile="image/*"),this.BIMFiles&&(this.allowFile=".ifc,.bzip,.bzip2")},methods:{onExceed:function(){this.$message.error("只能上传一个文件")},getTemplate:function(){var e=this,t=this.form.Professional_Code;(0,c.PartDeepeningImportTemplate)({Professional_Code:t,Template_Type:this.form.Template_Type}).then((function(t){t.IsSucceed?window.open((0,d.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message.error(t.Message)}))},handleChange:function(e,t){this.fileList=t.slice(-1),t.length>1&&(this.attachments.splice(-1),this.form.Doc_File="",this.form.Doc_Content="",this.form.Doc_Title="")},beforeUpload:function(e){this.curFile=e,this.loading=!0,this.btnLoading=!0},beforeRemove:function(e){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this,n=0;this.fileList.filter((function(t,a){t.name===e.name&&(n=a)})),this.fileList.splice(n,1),this.attachments.splice(n,1),this.form.Doc_File=this.form.Doc_File.replace(e.name,""),this.form.Doc_Content=this.form.Doc_File.replace(e.name,""),this.form.Doc_Title=this.form.Doc_File.replace(e.name,""),this.loading=!t.every((function(e){return"success"===e.status})),setTimeout((function(){a.btnLoading=!t.every((function(e){return"success"===e.status}))}),1e3)},uploadError:function(e,t,a){this.$message.error("".concat(t.name,"上传失败"))},uploadSuccess:function(e,t,a){var n=this;this.fileList=a,this.attachments.push({File_Url:e.Data.split("*")[0],File_Size:e.Data.split("*")[1],File_Type:e.Data.split("*")[2],File_Name:e.Data.split("*")[3]});var r=this.form.Doc_Title+(this.form.Doc_Title?",":"")+e.Data.split("*")[3];this.form.Doc_Title=r.substring(0,r.lastIndexOf(".")),this.form.Doc_Content=this.form.Doc_Title,this.form.Doc_File=this.form.Doc_File+(this.form.Doc_File?",":"")+e.Data.split("*")[3],this.form.File_Url=e.Data.split("*")[0],this.loading=!a.every((function(e){return"success"===e.status})),setTimeout((function(){n.btnLoading=!a.every((function(e){return"success"===e.status}))}),1e3)},handleOpen:function(e,t,a){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"cover",l=arguments.length>6?arguments[6]:void 0;this.projectId=r,this.isDeep=n,this.form=(0,i.default)({},f),this.form.Type_Name=t.name,this.form.Doc_Type=t.Id,this.form.Doc_Catelog=t.Catalog_Code,this.isSHQD=t.isSHQD,this.form.Professional_Code=t.Code,this.dialogVisible=!0,this.type=e,this.bimvizId=a,this.command=o,this.form.Project_Name=l.Project_Name,this.form.Sys_Project_Id=l.Sys_Project_Id,this.form.Area_Name=l.Area_Name,this.form.Area_Id=l.Area_Id,"add"===this.type&&(this.fileList=[],this.title="新增文件",this.form.Id=""),"cover"===this.command?this.title="覆盖文件":"add"===this.command&&(this.title="新增文件")},handleClose:function(){try{this.attachments=[],this.$refs["form"].resetFields(),this.btnLoading=!1,this.fileList=[],this.dialogVisible=!1}catch(e){}},handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=(0,o.default)((0,r.default)().m((function t(a){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(!a){t.n=1;break}e.loading=!0,e.btnLoading=!0,e.updateInfo(),t.n=2;break;case 1:return e.$message({message:"请将表单填写完整",type:"warning"}),t.a(2,!1);case 2:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},updateInfo:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){var a,n,o;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:if(a=(0,i.default)((0,i.default)({},e.form),{},{AttachmentList:e.attachments}),"cover"!==e.command){t.n=9;break}return t.p=1,t.n=2,(0,l.ImportPartList)(a);case 2:if(n=t.v,!n.IsSucceed){t.n=4;break}return e.$message({message:"保存成功",type:"success"}),t.n=3,e.updatePartAggregateId();case 3:e.$emit("getData",e.form.Doc_Type),e.$emit("getTreeData"),e.handleClose(),t.n=5;break;case 4:n.Data&&window.open((0,d.combineURL)(e.$baseUrl,n.Data),"_blank"),e.$message.error(n.Message);case 5:t.n=7;break;case 6:t.p=6,t.v,e.$message.error("保存失败");case 7:return t.p=7,e.btnLoading=!1,e.loading=!1,t.f(7);case 8:t.n=18;break;case 9:if("add"!==e.command){t.n=18;break}return t.p=10,t.n=11,(0,l.AppendImportPartList)(a);case 11:if(o=t.v,!o.IsSucceed){t.n=13;break}return e.$message({message:"保存成功",type:"success"}),t.n=12,e.updatePartAggregateId();case 12:e.$emit("getData",e.form.Doc_Type),e.$emit("getTreeData"),e.handleClose(),t.n=14;break;case 13:o.Data&&window.open((0,d.combineURL)(e.$baseUrl,o.Data),"_blank"),e.$message.error(o.Message);case 14:t.n=16;break;case 15:t.p=15,t.v,e.$message.error("保存失败");case 16:return t.p=16,e.btnLoading=!1,e.loading=!1,t.f(16);case 17:case 18:return t.a(2)}}),t,null,[[10,15,16,17],[1,6,7,8]])})))()},updatePartAggregateId:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.UpdatePartAggregateId)({AreaId:e.form.Area_Id}).then((function(t){t.IsSucceed||e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()}}}},fc2b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.show?a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("项目信息")])]),e.getLabel("Project_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Project_Name"),prop:"Project_Id"}},[a("el-select",{ref:"Project_Name",staticStyle:{width:"100%"},attrs:{disabled:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("Area_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Area_Name"),prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"w100",attrs:{"tree-params":e.treeParamsArea,disabled:"",placeholder:"请选择"},on:{"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1):e._e(),e.getLabel("InstallUnit_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("InstallUnit_Name"),prop:"InstallUnit_Id"}},[a("el-select",{ref:"InstallUnit_Name",staticStyle:{width:"100%"},attrs:{disabled:"",clearable:"",placeholder:"请选择"},on:{change:e.setupPositionChange},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e()],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("基础信息")])]),e.getLabel("Code")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Code"),prop:"Code"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1)],1):e._e(),e.getLabel("Spec")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Spec"),prop:"Spec"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec",t)},expression:"form.Spec"}})],1)],1):e._e(),e.getLabel("Length")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Length"),prop:"Length"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length",t)},expression:"form.Length"}})],1)],1):e._e(),e.getLabel("Texture")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Texture"),prop:"Texture"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Texture,callback:function(t){e.$set(e.form,"Texture",t)},expression:"form.Texture"}})],1)],1):e._e(),e.getLabel("Num")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Num"),prop:"Num"}},[a("el-input",{attrs:{disabled:""},on:{input:e.calculationAllWeight},model:{value:e.form.Num,callback:function(t){e.$set(e.form,"Num",t)},expression:"form.Num"}})],1)],1):e._e(),e.getLabel("Schduling_Count")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Schduling_Count"),prop:"Schduling_Count"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Schduling_Count,callback:function(t){e.$set(e.form,"Schduling_Count",t)},expression:"form.Schduling_Count"}})],1)],1):e._e(),e.getLabel("Shape")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Shape"),prop:"Shape"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Shape,callback:function(t){e.$set(e.form,"Shape",t)},expression:"form.Shape"}})],1)],1):e._e(),e.getLabel("Weight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Weight"),prop:"Weight"}},[a("el-input",{attrs:{type:"number",disabled:e.isReadOnly||e.form.Schduling_Count>0},on:{input:e.calculationAllWeight},model:{value:e.form.Weight,callback:function(t){e.$set(e.form,"Weight",t)},expression:"form.Weight"}})],1)],1):e._e(),e.getLabel("Total_Weight")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Total_Weight"),prop:"Total_Weight"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Total_Weight,callback:function(t){e.$set(e.form,"Total_Weight",t)},expression:"form.Total_Weight"}})],1)],1):e._e(),e.getLabel("Type_Name")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Type_Name"),prop:"Type_Name"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Type_Name,callback:function(t){e.$set(e.form,"Type_Name",t)},expression:"form.Type_Name"}})],1)],1):e._e(),e.getLabel("Is_Main")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Is_Main"),prop:"Is_Main"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0,clearable:"",placeholder:"请选择"},model:{value:e.form.Is_Main,callback:function(t){e.$set(e.form,"Is_Main",t)},expression:"form.Is_Main"}},e._l(e.Is_Main_Data,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),e.getLabel("Times")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Times"),prop:"Times"}},[a("el-input",{attrs:{type:"number","on-keypress":"return (/[\\d.]/.test(String.fromCharCode(event.keyCode)))",disabled:e.isReadOnly||e.form.Schduling_Count>0},on:{input:e.calculationNum},model:{value:e.form.Times,callback:function(t){e.$set(e.form,"Times",t)},expression:"form.Times"}})],1)],1):e._e(),e.getLabel("Component_Code")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Component_Code"),prop:"Component_Code"}},[a("el-input",{attrs:{disabled:e.isReadOnly||e.form.Schduling_Count>0},model:{value:e.form.Component_Code,callback:function(t){e.$set(e.form,"Component_Code",t)},expression:"form.Component_Code"}})],1)],1):e._e(),e.getLabel("DateName")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("DateName"),prop:"DateName"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.DateName,callback:function(t){e.$set(e.form,"DateName",t)},expression:"form.DateName"}})],1)],1):e._e(),e.getLabel("Exdate")?a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel("Exdate"),prop:"Exdate"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Exdate,callback:function(t){e.$set(e.form,"Exdate",t)},expression:"form.Exdate"}})],1)],1):e._e(),e.getLabel("Remark")?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:e.getLabel("Remark"),prop:"Remark"}},[a("el-input",{attrs:{disabled:e.isReadOnly||this.form.Schduling_Count>0},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1):e._e()],1),e.extendField.length>0?a("el-row",[a("el-col",{attrs:{span:24}},[a("span",{staticClass:"left-border"}),a("span",{staticClass:"cs-title"},[e._v("拓展字段")])]),e._l(e.extendField,(function(t,n){return a("el-col",{key:n,attrs:{span:12}},[a("el-form-item",{attrs:{label:e.getLabel(t.Code),prop:t.Code}},[a("el-input",{attrs:{disabled:e.isReadOnly},model:{value:e.extendform[t.Code],callback:function(a){e.$set(e.extendform,t.Code,a)},expression:"extendform[item.Code]"}})],1)],1)}))],2):e._e(),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),e.isReadOnly?e._e():a("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)],1):e._e()],1)},r=[]},fd51:function(e,t,a){},ff21:function(e,t,a){"use strict";a.r(t);var n=a("97a9"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a}}]);