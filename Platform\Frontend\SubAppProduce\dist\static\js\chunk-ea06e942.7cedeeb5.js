(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ea06e942"],{"02bc":function(e,t,a){"use strict";a.r(t);var n=a("121c"),o=a("a132c");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("b58f");var r=a("2877"),s=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"726acfbb",null);t["default"]=s.exports},"09d8":function(e,t,a){},"0d32":function(e,t,a){"use strict";a.r(t);var n=a("7f11"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},"0e35":function(e,t,a){"use strict";a.r(t);var n=a("a868"),o=a("22fb");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("cb31");var r=a("2877"),s=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"61aef023",null);t["default"]=s.exports},"121c":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container abs100"},[a("div",{ref:"searchDom",staticClass:"header_wrapper"},[a("el-tabs",{staticClass:"search-wrapper",on:{"tab-click":e.handleTap},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"返修列表",name:"返修列表"}}),a("el-tab-pane",{attrs:{label:"返修记录",name:"返修记录"}})],1),a("div",{staticClass:"search-wrapper"},[a("el-form",{ref:"form",staticClass:"demo-form-inline form-search",staticStyle:{height:"auto"},attrs:{inline:!0,"label-width":"100px",model:e.form}},[a("el-form-item",{attrs:{label:"名称：",prop:"SearchCode"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入（空格间隔筛选多个）"},model:{value:e.form.SearchCode,callback:function(t){e.$set(e.form,"SearchCode",t)},expression:"form.SearchCode"}})],1),a("el-form-item",{attrs:{label:"来源项目：",prop:"Project_Id"}},[a("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"来源区域：",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"仓库名称",prop:"Warehouse_Id"}},[a("el-select",{ref:"WarehouseRef",attrs:{clearable:"",placeholder:"请选择仓库"},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位名称",prop:"Location_Id"}},[a("el-select",{ref:"LocationRef",attrs:{clearable:"",placeholder:"请选择库位",disabled:!e.form.Warehouse_Id},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:function(t){e.$refs["form"].resetFields(),e.handleSearch("reset")}}},[e._v("重置")])],1)],1)],1)],1),a("div",{staticClass:"main-wrapper",staticStyle:{flex:"1"}},["返修列表"==e.activeName?a("Repair",{ref:"repairRef",attrs:{"search-detail":e.form,"is-integration":e.Is_Integration}}):a("RepairHistory",{ref:"repairHistoryRef",attrs:{"search-detail":e.form,"is-integration":e.Is_Integration}})],1)])},o=[]},22938:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"min-height":"500px",display:"flex","flex-direction":"column"}},[a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"退货仓库",prop:"Warehouse_Id"}},[a("el-select",{ref:"WarehouseRef",staticStyle:{width:"100%"},attrs:{placeholder:"请选择仓库",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"退货库位",prop:"Location_Id"}},[a("el-select",{ref:"LocationRef",staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择库位",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:8}})],1)],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,align:"left",stripe:"",data:e.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:e.activeCellMethod},"tooltip-config":{showAll:!0,enterable:!0}}},[e._l(e.columns,(function(t){return["Operation_Count"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"edit-render":{},"min-width":"200",width:"200"},scopedSlots:e._u([{key:"edit",fn:function(t){var n=t.row;return[a("vxe-input",{attrs:{type:"integer",min:1,max:n.Stock_Count,disabled:1===n.Stock_Count},model:{value:n.Operation_Count,callback:function(t){e.$set(n,"Operation_Count",e._n(t))},expression:"row.Operation_Count"}})]}},{key:"default",fn:function(t){var n=t.row;return[a("div",[e._v(" "+e._s(n.Operation_Count))])]}}],null,!0)}):a("vxe-column",{key:t.Id,attrs:{field:t.Code,title:t.Display_Name,sortable:"","min-width":"120"},scopedSlots:e._u(["Source"===t.Code?{key:"default",fn:function(t){var n=t.row;return[1===n.Source?a("div",[e._v("零件退返")]):2===n.Source?a("div",[e._v("构件退返")]):3===n.Source?a("div",[e._v("现场退货")]):a("div",[e._v("-")])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(" "+e._s(o[t.Code]||"-"))])]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确定")])],1)])},o=[]},"22fb":function(e,t,a){"use strict";a.r(t);var n=a("7df4"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},3173:function(e,t,a){"use strict";a("9877")},"408d":function(e,t,a){"use strict";a("4add")},"4add":function(e,t,a){},"5cc7":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),o=a("fd31");t.default={methods:{getFactoryTypeOption:function(e){var t=this;(0,o.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(a){a.IsSucceed?(t.ProfessionalType=a.Data,t.getTableConfig("".concat(e,",").concat(t.ProfessionalType[0].Code))):t.$message({message:a.Message,type:"error"})}))},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,n.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,o=e.Data,i=e.Message;if(n){if(!o)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,o.Grid),t.columns=(o.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),t.form.PageInfo?t.form.PageInfo.PageSize=+o.Grid.Row_Number:t.form.PageSize=+o.Grid.Row_Number,a(t.columns)}else t.$message({message:i,type:"error"})}))}))},handlePageChange:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size):(this.form.Page=1,this.form.PageSize=e.size),this.fetchData()}}}},"7df4":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("dca8"),a("d3b7"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0");var o=n(a("5530")),i=n(a("c14f")),r=n(a("1da1")),s=a("6186"),c=n(a("333d")),l=a("abf9"),u=a("fd31"),d=a("8975"),f=a("c685"),m=n(a("faac"));t.default={components:{Pagination:c.default,RepairDialog:m.default},props:{searchDetail:{type:Object,default:function(){}},isIntegration:{type:Boolean,default:!1}},data:function(){return{dialogVisible:!1,currentComponent:"RepairDialog",title:"确认退货",tablePageSize:f.tablePageSize,Unit:"",TypeId:"",Proportion:0,typeOption:"",Date_Time:[],searchDate:{StartTime:"",EndTime:""},columns:[],tbData:[],tbLoading:!0,total:0,queryInfo:{Page:1,PageSize:20},pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},KC_Count:0,KC_Weith_Count:0,Select_KC_Count:0,Select_KC_Weith_Count:0,selectList:[]}},mounted:function(){},methods:{getTypeList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id,e.getTableConfig("PRORepairToStockIn"),e.fetchList())):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getTableConfig:function(e){var t=this;(0,s.GetGridByCode)({code:e+","+this.typeOption.find((function(e){return e.Id===t.TypeId})).Code}).then((function(e){var a=e.IsSucceed,n=e.Data,o=e.Message;if(a){if(!n)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbLoading=!1;var i=n.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),e}))}else t.$message({message:o,type:"error"})}))},changePage:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=20),Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},fetchList:function(){var e=this;(0,l.GetListComponent)((0,o.default)((0,o.default)((0,o.default)((0,o.default)({},this.searchDetail),this.searchDate),this.queryInfo),{},{Query_Type:3,Source:3})).then((function(t){e.tbData=t.Data.Data.map((function(e){return e.Is_Component=e.Is_Component?"否":"是",e.In_Date=(0,d.timeFormat)(e.In_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.total=t.Data.TotalCount}))},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.Select_KC_Count=0,this.Select_KC_Weith_Count=0;var a=0;this.selectList.length>0&&(this.selectList.forEach((function(e){t.Select_KC_Count+=e.Stock_Count,a+=Number(e.ToWeight)})),this.Select_KC_Weith_Count=Math.round(a/this.Proportion*1e3)/1e3)},changesearchDate:function(e){e?(this.searchDate.StartTime=e[0],this.searchDate.EndTime=e[1]):(this.searchDate.StartTime="",this.searchDate.EndTime=""),this.fetchList()},handleReturn:function(){var e=this;this.currentComponent="RepairDialog",this.width="65%",this.dialogVisible=!0,this.title="返修入库",this.$nextTick((function(t){e.$refs.content.init(e.selectList)}))},close:function(){this.dialogVisible=!1}}}},"7f11":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("dca8"),a("d3b7"),a("3ca3"),a("c7cd"),a("159b"),a("ddb0");var o=n(a("5530")),i=n(a("c14f")),r=n(a("1da1")),s=a("6186"),c=n(a("333d")),l=a("edcd"),u=a("fd31"),d=a("8975"),f=a("c685");t.default={components:{Pagination:c.default},props:{searchDetail:{type:Object,default:function(){}}},data:function(){return{tablePageSize:f.tablePageSize,Unit:"",TypeId:"",Proportion:0,typeOption:"",Date_Time:[],searchDate:{StartTime:"",EndTime:""},columns:[],tbData:[],dialog:!1,tbLoading:!0,total:0,queryInfo:{Page:1,PageSize:20},pickerOptions:{shortcuts:[{text:"今天",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[a,t])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}}]},KC_Count:0,KC_Weith_Count:0,Select_KC_Count:0,Select_KC_Weith_Count:0,selectList:[]}},mounted:function(){},methods:{getTypeList:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){var a,n,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:a=t.v,n=a.Data,a.IsSucceed?(e.typeOption=Object.freeze(n),e.typeOption.length>0&&(e.Proportion=n[0].Proportion,e.Unit=n[0].Unit,e.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id,e.getTableConfig("PRORepairToStockInHistory"),e.fetchList())):e.$message({message:a.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},getTableConfig:function(e){var t=this;(0,s.GetGridByCode)({code:e+","+this.typeOption.find((function(e){return e.Id===t.TypeId})).Code}).then((function(e){var a=e.IsSucceed,n=e.Data,o=e.Message;if(a){if(!n)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbLoading=!1;var i=n.ColumnList||[];t.columns=i.filter((function(e){return e.Is_Display})).map((function(e){return"Code"===e.Code&&(e.fixed="left"),e}))}else t.$message({message:o,type:"error"})}))},changePage:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,("number"!==typeof e.queryInfo.PageSize||e.queryInfo.PageSize<1)&&(e.queryInfo.PageSize=10),Promise.all([e.fetchList()]).then((function(t){e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},fetchList:function(){var e=this;(0,l.GetUserRepairStockInRecordPageList)((0,o.default)((0,o.default)({},this.searchDetail),this.queryInfo)).then((function(t){e.tbData=t.Data.Data.map((function(e){return e.Is_Component=e.Is_Component?"否":"是",e.ScanDate=(0,d.timeFormat)(e.ScanDate,"{y}-{m}-{d}"),e})),e.total=t.Data.TotalCount}))},tbSelectChange:function(e){var t=this;this.selectList=e.records,this.Select_KC_Count=0,this.Select_KC_Weith_Count=0;var a=0;this.selectList.length>0&&(this.selectList.forEach((function(e){t.Select_KC_Count+=e.Stock_Count,a+=Number(e.ToWeight)})),this.Select_KC_Weith_Count=Math.round(a/this.Proportion*1e3)/1e3)},changesearchDate:function(e){e?(this.searchDate.StartTime=e[0],this.searchDate.EndTime=e[1]):(this.searchDate.StartTime="",this.searchDate.EndTime=""),this.fetchList()}}}},"8ca6":function(e,t,a){"use strict";a.r(t);var n=a("dda2"),o=a("0d32");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("3173");var r=a("2877"),s=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"0b7f9462",null);t["default"]=s.exports},9877:function(e,t,a){},a132c:function(e,t,a){"use strict";a.r(t);var n=a("d109"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},a868:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"assistant-box"},[a("div",{staticClass:"assistant-wrapper"},[a("div",{staticClass:"btn-wrapper"},[(e.isIntegration,[a("el-button",{attrs:{size:"small",type:"primary",disabled:e.selectList.length<=0},on:{click:e.handleReturn}},[e._v("批量返修入库")])])],2),a("div",{staticClass:"total-wrapper"})]),a("div",{staticClass:"table-wrapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",data:e.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"40"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width,visible:t.visible},scopedSlots:e._u(["Source"===t.Code?{key:"default",fn:function(t){var n=t.row;return[1===n.Source?a("div",[e._v("零件退返")]):2===n.Source?a("div",[e._v("构件退返")]):3===n.Source?a("div",[e._v("现场退货")]):a("div",[e._v("-")])]}}:{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(e._s(o[t.Code]||"-"))])]}}],null,!0)})}))],2)],1),a("div",{staticClass:"page-total"},[a("div",{staticStyle:{display:"inline-block"}},[a("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[e._v("已选"+e._s(e.selectList.length)+"条数据")])],1),a("Pagination",{staticClass:"cs-table-pagination",staticStyle:{display:"inline-block",float:"right"},attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible},on:{close:e.close,refresh:e.fetchList}})],1):e._e()],1)},o=[]},a9f9:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("159b");var o=n(a("c14f")),i=n(a("1da1")),r=n(a("5cc7")),s=a("edcd"),c=a("fd31"),l=n(a("2ea6"));t.default={components:{},mixins:[r.default,l.default],data:function(){return{warehouseType:"成品仓库",tbConfig:{Pager_Align:"center"},columns:[],tbData:[],total:0,tbLoading:!1,btnLoading:!1,form:{Warehouse_Id:"",Location_Id:"",Remark:"",PageInfo:{Page:1,PageSize:20}},rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]},selectList:[],ProfessionalType:[]}},created:function(){},methods:{init:function(e){var t=this;return(0,i.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return t.tbLoading=!0,e&&e.map((function(e){e.Return_Count=e.Stock_Count})),t.tbData=JSON.parse(JSON.stringify(e)),t.tbData.map((function(e){return e.Operation_Count=e.Stock_Count,e})),t.total=t.tbData.length,a.n=1,t.getFactoryTypeOption();case 1:return a.a(2)}}),a)})))()},getFactoryTypeOption:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("PRORepairToStockInDetail,".concat(e.ProfessionalType[0].Code));case 2:e.tbLoading=!1;case 3:return t.a(2)}}),t)})))()},activeCellMethod:function(e){e.row;var t=e.column;e.columnIndex;return"Operation_Count"===t.field},showTooltipMethod:function(e){e.type,e.column,e.row,e.items,e._columnIndex;return null},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){var a=!0;if(e.tbData.forEach((function(e){e.Operation_Count||(a=!1)})),!a)return void e.$message({message:"请填写返修数",type:"error"});e.btnLoading=!0;var n={Location_Id:e.form.Location_Id,Warehouse_Id:e.form.Warehouse_Id,Remark:e.form.Remark,StockInList:e.tbData||[]};(0,s.SubmitRepairToStockIn)(n).then((function(t){t.IsSucceed?(e.btnLoading=!1,e.$message({message:"返修成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):(e.btnLoading=!1,e.$message({message:t.Message,type:"error"}))}))}}))},multiSelectedChange:function(e){this.selectList=e},handleClose:function(){this.$emit("close")},toggleSelection:function(e){var t=this;this.$nextTick((function(a){t.tbData.forEach((function(a){e.find((function(e){return e.Id===a.Id}))&&t.$refs.dyTable.$refs.dtable.toggleRowSelection(a)}))}))},getPageList:function(){},resetForm:function(e){this.$refs[e].resetFields()}}}},b58f:function(e,t,a){"use strict";a("09d8")},b592:function(e,t,a){},cb31:function(e,t,a){"use strict";a("b592")},d109:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("b0c0"),a("ac1f"),a("5319"),a("5b81"),a("498a");var o=n(a("c14f")),i=n(a("1da1")),r=n(a("83b4")),s=n(a("2ea6")),c=n(a("0e35")),l=n(a("8ca6")),u=a("2c08");t.default={components:{Repair:c.default,RepairHistory:l.default},mixins:[r.default,s.default],data:function(){return{activeName:"返修列表",form:{InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Sys_Project_Id:"",Area_Id:"",Warehouse_Id:"",Location_Id:"",Codes:"",SearchCode:"",Component_Codes:"",Is_Direct:""},tbConfig:{},columns:[],tbData:[],Is_Integration:!1}},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.warehouseType="半成品仓库",t.n=1,e.getIntegrationStatus();case 1:return t.n=2,e.getProjectNan();case 2:e.$nextTick((function(t){e.$refs["repairRef"].getTypeList()}));case 3:return t.a(2)}}),t)})))()},mounted:function(){return(0,i.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.a(2)}}),e)})))()},methods:{getIntegrationStatus:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:a=t.v,e.Is_Integration=a;case 2:return t.a(2)}}),t)})))()},handleTap:function(e){var t=this;return(0,i.default)((0,o.default)().m((function a(){return(0,o.default)().w((function(a){while(1)switch(a.n){case 0:return t.warehouseType="返修记录"===e.name?"成品仓库":"半成品仓库",Object.assign(t.form,{InstallUnit_Id:"",SetupPosition:"",Area_Id:"",Warehouse_Id:"",Location_Id:"",Codes:"",SearchCode:"",Component_Codes:"",Is_Direct:""}),t.getWarehouseList(),a.n=1,t.getProjectNan();case 1:"返修列表"===t.activeName?t.$nextTick((function(e){t.$refs["repairRef"].getTypeList()})):t.$refs["repairHistoryRef"].getTypeList();case 2:return a.a(2)}}),a)})))()},getProjectNan:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.getQueryParam)(["Project_Id","Sys_Project_Id"]);case 1:a=t.v,e.form.Project_Id=a.Project_Id,e.form.Sys_Project_Id=a.Sys_Project_Id,e.form.ProjectName=a.ProjectName,e.projectChange(a.Project_Id);case 2:return t.a(2)}}),t)})))()},handleSearch:function(e){this.form.Component_Codes=this.form.SearchCode.trim().replaceAll(" ","\n"),e&&"reset"===e&&(this.form.ProjectName="",this.form.Sys_Project_Id=""),"返修列表"===this.activeName?this.$refs.repairRef.getTypeList():"返修记录"===this.activeName&&this.$refs.repairHistoryRef.getTypeList(),(0,u.addSearchLog)(this.form)}}}},daf0:function(e,t,a){"use strict";a.r(t);var n=a("a9f9"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=o.a},dda2:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return o}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"assistant-box"},[a("div",{staticClass:"table-wrapper"},[a("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",data:e.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"40"}}),e._l(e.columns,(function(t,n){return a("vxe-column",{key:n,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u([{key:"default",fn:function(n){var o=n.row;return[a("div",[e._v(e._s(o[t.Code]||"-"))])]}}],null,!0)})}))],2)],1),a("div",{staticClass:"page-total"},[a("div",{staticStyle:{display:"inline-block"}},[a("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[e._v("已选"+e._s(e.selectList.length)+"条数据")])],1),a("Pagination",{staticClass:"cs-table-pagination",staticStyle:{display:"inline-block",float:"right"},attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)])},o=[]},edcd:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteStockInRecord=u,t.GetComponentList=d,t.GetPackageItemList=c,t.GetUserRepairStockInRecordPageList=m,t.GetUserStockInRecordPageList=l,t.GetWaitingStockIn2ndPageList=i,t.GetWaitingStockInPacking2ndPageList=s,t.SaveStockIn2nd=r,t.SubmitRepairToStockIn=f;var o=n(a("b775"));n(a("4328"));function i(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetWaitingStockIn2ndPageList",method:"post",data:e})}function r(e){return(0,o.default)({url:"/PRO/ComponentStockIn/SaveStockIn2nd",method:"post",data:e})}function s(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetWaitingStockInPacking2ndPageList",method:"post",data:e})}function c(e){return(0,o.default)({url:"/PRO/Packing/GetPackageItemList",method:"post",data:e})}function l(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetUserStockInRecordPageList",method:"post",data:e})}function u(e){return(0,o.default)({url:"/PRO/ComponentStockIn/DeleteStockInRecord",method:"post",data:e})}function d(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetComponentList",method:"post",data:e})}function f(e){return(0,o.default)({url:"/PRO/ComponentStockIn/SubmitRepairToStockIn",method:"post",data:e})}function m(e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetUserRepairStockInRecordPageList",method:"post",data:e})}},faac:function(e,t,a){"use strict";a.r(t);var n=a("22938"),o=a("daf0");for(var i in o)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(i);a("408d");var r=a("2877"),s=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"2387f162",null);t["default"]=s.exports}}]);