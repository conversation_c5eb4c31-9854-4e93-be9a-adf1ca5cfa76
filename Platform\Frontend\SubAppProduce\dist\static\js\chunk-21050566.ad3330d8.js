(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-21050566"],{"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,n){var i=r(),u=t-i,d=20,s=0;e="undefined"===typeof e?500:e;var c=function(){s+=d;var t=Math.easeInOutQuad(s,i,u,e);o(t),s<e?a(c):n&&"function"===typeof n&&n()};c()}},"15ac":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("c685");e.default={methods:{getTableConfig:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,a.GetGridByCode)({code:t,IsAll:n}).then((function(t){var a=t.IsSucceed,i=t.Data,u=t.Message;if(a){if(!i)return void e.$message({message:"表格配置不存在",type:"error"});var d=[];e.tbConfig=Object.assign({},e.tbConfig,i.Grid),d=n?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=d.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+i.Grid.Row_Number||o.tablePageSize[0]),r(e.columns)}else e.$message({message:u,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,n=t.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:e,this.fetchData()},pageChange:function(t){var e=t.page,n=t.limit,a=t.type;this.queryInfo.Page="limit"===a?1:e,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var n={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?n.Value=t[e]:n.Value=[t[e]];for(var a=0;a<this.columns.length;a++){var o=this.columns[a];if(o.Code===e){n.Type=o.Type,n.Filter_Type=o.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"221f":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.Add=d,e.AddLanch=u,e.DelLanch=c,e.EditLanch=f,e.EntityLanch=r,e.GetEditById=l,e.GetPageQualitySummary=m,e.GetPartAndSteelBacrode=i,e.RectificationRecord=p,e.SubmitLanch=s;var o=a(n("b775"));function r(t){return(0,o.default)({url:"/PRO/Inspection/EntityLanch",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/Inspection/Add",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Inspection/EditLanch",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Inspection/GetPageQualitySummary",method:"post",data:t})}},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=p,e.DeleteProject=c,e.GeAreaTrees=G,e.GetFileSync=j,e.GetInstallUnitIdNameList=R,e.GetNoBindProjectList=m,e.GetPartDeepenFileList=S,e.GetProjectAreaTreeList=v,e.GetProjectEntity=d,e.GetProjectList=u,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=O,e.GetSchedulingPartList=C,e.IsEnableProjectMonomer=l,e.SaveProject=s,e.UpdateProjectTemplateBase=P,e.UpdateProjectTemplateContacts=I,e.UpdateProjectTemplateContract=y,e.UpdateProjectTemplateOther=g;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function s(t){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function l(t){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function j(t){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"4d7a":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.FIX_COLUMN=void 0,e.getFactoryProfessional=u;var o=a(n("c14f")),r=a(n("1da1"));n("d3b7");var i=n("fd31");function u(){return new Promise((function(t,e){(0,i.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then(function(){var n=(0,r.default)((0,o.default)().m((function n(a){return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:a.IsSucceed?t(a.Data):e("error");case 1:return n.a(2)}}),n)})));return function(t){return n.apply(this,arguments)}}()).catch((function(t){e("error")}))}))}e.FIX_COLUMN=["SteelName"]},"4e82":function(t,e,n){"use strict";var a=n("23e7"),o=n("e330"),r=n("59ed"),i=n("7b0b"),u=n("07fa"),d=n("083a"),s=n("577e"),c=n("d039"),l=n("addb"),f=n("a640"),p=n("3f7e"),m=n("99f4"),h=n("1212"),P=n("ea83"),I=[],y=o(I.sort),g=o(I.push),O=c((function(){I.sort(void 0)})),v=c((function(){I.sort(null)})),R=f("sort"),G=!c((function(){if(h)return h<70;if(!(p&&p>3)){if(m)return!0;if(P)return P<603;var t,e,n,a,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)I.push({k:e+a,v:n})}for(I.sort((function(t,e){return e.v-t.v})),a=0;a<I.length;a++)e=I[a].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),S=O||!v||!R||!G,C=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:s(e)>s(n)?1:-1}};a({target:"Array",proto:!0,forced:S},{sort:function(t){void 0!==t&&r(t);var e=i(this);if(G)return void 0===t?y(e):y(e,t);var n,a,o=[],s=u(e);for(a=0;a<s;a++)a in e&&g(o,e[a]);l(o,C(t)),n=u(o),a=0;while(a<n)e[a]=o[a++];while(a<s)d(e,a++);return e}})},"5cc7":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("4de4"),n("d81d"),n("e9f5"),n("910d"),n("ab43"),n("d3b7");var a=n("6186"),o=n("fd31");e.default={methods:{getFactoryTypeOption:function(t){var e=this;(0,o.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(n){n.IsSucceed?(e.ProfessionalType=n.Data,e.getTableConfig("".concat(t,",").concat(e.ProfessionalType[0].Code))):e.$message({message:n.Message,type:"error"})}))},getTableConfig:function(t){var e=this;return new Promise((function(n){(0,a.GetGridByCode)({code:t}).then((function(t){var a=t.IsSucceed,o=t.Data,r=t.Message;if(a){if(!o)return void e.$message({message:"表格配置不存在",type:"error"});e.tbConfig=Object.assign({},e.tbConfig,o.Grid),e.columns=(o.ColumnList.filter((function(t){return t.Is_Display}))||[]).map((function(t){return t.Is_Resizable=!0,t})),e.form.PageInfo?e.form.PageInfo.PageSize=+o.Grid.Row_Number:e.form.PageSize=+o.Grid.Row_Number,n(e.columns)}else e.$message({message:r,type:"error"})}))}))},handlePageChange:function(t){this.form.PageInfo?this.form.PageInfo.Page=t.page:this.form.Page=t.page,this.fetchData()},handleSizeChange:function(t){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=t.size):(this.form.Page=1,this.form.PageSize=t.size),this.fetchData()}}}},"7de9":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddCheckItem=p,e.AddCheckItemCombination=I,e.AddCheckType=d,e.DelNode=j,e.DelQualityList=G,e.DeleteCheckItem=f,e.DeleteCheckType=s,e.EntityCheckItem=m,e.EntityCheckType=i,e.EntityQualityList=R,e.ExportInspsectionSummaryInfo=_,e.GetCheckGroupList=P,e.GetCheckItemList=l,e.GetCheckTypeList=u,e.GetCompTypeTree=b,e.GetDictionaryDetailListByCode=r,e.GetEntityNode=C,e.GetFactoryPeoplelist=g,e.GetFactoryProfessionalByCode=T,e.GetMaterialType=D,e.GetNodeList=S,e.GetProEntities=y,e.GetProcessCodeList=O,e.QualityList=v,e.SaveCheckItem=h,e.SaveCheckType=c,e.SaveNode=L;var o=a(n("b775"));function r(t){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/Inspection/QualityList",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/Inspection/DelNode",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:t})}},a888:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=a(n("d565")),r=function(t){t.directive("el-drag-dialog",o.default)};window.Vue&&(window["el-drag-dialog"]=o.default,Vue.use(r)),o.default.install=r;e.default=o.default},d51a:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddLanch=u,e.BatchManageSaveCheck=y,e.DelLanch=I,e.DeleteToleranceSetting=_,e.EntityQualityManagement=l,e.ExportQISummary=L,e.GetCheckingEntity=O,e.GetCompPartForSpotCheckPageList=S,e.GetCompQISummary=j,e.GetDictionaryDetailListByCode=d,e.GetEditById=g,e.GetFactoryPeoplelist=p,e.GetNodeList=s,e.GetPageFeedBack=v,e.GetPageQualityManagement=r,e.GetPartAndSteelBacrode=c,e.GetSheetDwg=m,e.GetSpotCheckingEntity=C,e.GetToleranceSettingList=U,e.ImportQISummary=b,e.ManageAdd=i,e.RectificationRecord=G,e.SaveFeedBack=R,e.SavePass=h,e.SaveQIReportData=T,e.SaveTesting=f,e.SaveToleranceSetting=D,e.SubmitLanch=P;var o=a(n("b775"));function r(t){return(0,o.default)({url:"/PRO/Inspection/GetPageQualityManagement",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/Inspection/ManageAdd",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:t})}function d(t){return(0,o.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/Inspection/EntityQualityManagement",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Inspection/SaveTesting",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Inspection/GetSheetDwg",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Inspection/SavePass",method:"post",data:t,timeout:12e5})}function P(t){return(0,o.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/Inspection/BatchManageSaveCheck",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/Inspection/GetCheckingEntity",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/Inspection/GetPageFeedBack",method:"post",data:t})}function R(t){return(0,o.default)({url:"/PRO/Inspection/SaveFeedBack",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/Inspection/GetCompPartForSpotCheckPageList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/Inspection/GetSpotCheckingEntity",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/Inspection/GetCompQISummary",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/Inspection/ExportQISummary",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/Inspection/ImportQISummary",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/Inspection/SaveQIReportData",method:"post",data:t})}function D(t){return(0,o.default)({url:"/pro/Inspection/SaveToleranceSetting",method:"post",data:t})}function _(t){return(0,o.default)({url:"/pro/Inspection/DeleteToleranceSetting",method:"post",data:t})}function U(t){return(0,o.default)({url:"/pro/Inspection/GetToleranceSettingList",method:"post",data:t})}},d565:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af"),n("caad"),n("ac1f"),n("2532"),n("5319");e.default={bind:function(t,e,n){var a=t.querySelector(".el-dialog__header"),o=t.querySelector(".el-dialog");a.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var r=function(){return window.document.currentStyle?function(t,e){return t.currentStyle[e]}:function(t,e){return getComputedStyle(t,!1)[e]}}();a.onmousedown=function(t){var e=t.clientX-a.offsetLeft,i=t.clientY-a.offsetTop,u=o.offsetWidth,d=o.offsetHeight,s=document.body.clientWidth,c=document.body.clientHeight,l=o.offsetLeft,f=s-o.offsetLeft-u,p=o.offsetTop,m=c-o.offsetTop-d,h=r(o,"left"),P=r(o,"top");h.includes("%")?(h=+document.body.clientWidth*(+h.replace(/\%/g,"")/100),P=+document.body.clientHeight*(+P.replace(/\%/g,"")/100)):(h=+h.replace(/\px/g,""),P=+P.replace(/\px/g,"")),document.onmousemove=function(t){var a=t.clientX-e,r=t.clientY-i;-a>l?a=-l:a>f&&(a=f),-r>p?r=-p:r>m&&(r=m),o.style.cssText+=";left:".concat(a+h,"px;top:").concat(r+P,"px;"),n.child.$emit("dragDialog")},document.onmouseup=function(t){document.onmousemove=null,document.onmouseup=null}}}}},db46:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("7d54"),n("d3b7"),n("159b");var a=n("3166"),o=n("f2f6");e.default={data:function(){return{ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},SetupPositionData:[],consigneeName:""}},mounted:function(){this.getProjectOption()},methods:{getProjectOption:function(){var t=this;(0,a.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getAreaList:function(t){var e=this;(0,a.GeAreaTrees)({projectId:t}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},getInstall:function(){var t=this;(0,o.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.SetupPositionData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},projectChangeSingle:function(t){var e,n=this;this.$nextTick((function(){n.form.ProjectName=n.$refs["ProjectName"].selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.getProjectEntity(t)},projectChange:function(t){var e,n=this,a=null===(e=this.ProjectNameData.find((function(e){return e.Sys_Project_Id===t})))||void 0===e?void 0:e.Id;this.form.Area_Id="",this.treeParamsArea.data=[],this.$nextTick((function(t){n.$refs.treeSelectArea.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",t&&this.getAreaList(a)},areaChange:function(t){this.SetupPositionData=[],this.form.InstallUnit_Id="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.InstallUnit_Id=""},setupPositionChange:function(){},dateChange:function(t){},getProjectEntity:function(t){var e=this;(0,a.GetProjectEntity)({id:t}).then((function(t){if(t.IsSucceed){var n="",a=t.Data.Contacts;a.forEach((function(t){"Consignee"==t.Type&&(n=t.Name)})),e.consigneeName=n}else e.$message({message:t.Message,type:"error"})}))}}}},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=d,e.CheckPlanTime=s,e.DeleteInstallUnit=p,e.GetCompletePercent=y,e.GetEntity=O,e.GetInstallUnitAllInfo=l,e.GetInstallUnitComponentPageList=I,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=u,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=g,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=m,e.SaveInstallUnit=P,e.SaveOhterSourceInstallUnit=v;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function s(t){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function l(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function p(t){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function m(t){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function O(t){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function v(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}}}]);