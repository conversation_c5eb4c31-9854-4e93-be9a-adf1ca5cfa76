(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ad4dfdfa"],{"0404":function(t,e,n){"use strict";n.r(e);var a=n("eca2"),o=n("b0ab");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("e6ca");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"0c8fcc3a",null);e["default"]=s.exports},"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,n){var i=r(),s=t-i,u=20,l=0;e="undefined"===typeof e?500:e;var d=function(){l+=u;var t=Math.easeInOutQuad(l,i,s,e);o(t),l<e?a(d):n&&"function"===typeof n&&n()};d()}},"13d57":function(t,e,n){"use strict";n.r(e);var a=n("8759"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"16c0":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b0c0"),n("498a");var a=n("eb4a");e.default={name:"FlowModuleNote",props:{isEdit:{type:Boolean,default:!0},datas:{type:Object,default:function(){return{}}}},data:function(){return{form:{User_Name:this.$store.getters.name,Days:"",Request_Comment:""},btnLoading:!1,rules:{Request_Comment:[{required:!0,message:"原因不能为空",trigger:"blur"}],Days:[{required:!0,message:"天数不能为空"},{type:"number",message:"天数必须为数字值"}]}}},computed:{btnDisabled:{get:function(){return!this.form.Request_Comment.trim().length},set:function(t){this.btnDisabled=this.form.Request_Comment.trim().length}}},watch:{datas:function(t,e){this.isEdit||(this.form=Object.assign(this.form,t))}},methods:{handleSubmit:function(){var t=this;this.$refs.ruleForm.validate((function(e){if(!e)return!1;t.btnLoading=!0,(0,a.SaveBusinessData)({webfromId:t.$route.name,model:t.form}).then((function(e){e.IsSucceed?(t.$refs.ruleForm.resetFields(),t.$message({message:"保存成功",type:"success"}),t.$emit("close"),t.$emit("refresh")):t.$message({message:e.Message,type:"error"}),t.btnLoading=!1}))}))}}}},"1f3d":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dropdown",{attrs:{trigger:"click","show-timeout":100}},[n("el-button",{attrs:{plain:""}},[t._v(t._s(t.getTxt())+" "),n("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),n("el-dropdown-menu",{staticClass:"no-padding",attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",[n("el-radio-group",{staticStyle:{padding:"10px"},model:{value:t.comment_disabled,callback:function(e){t.comment_disabled=e},expression:"comment_disabled"}},[n("el-radio",{attrs:{label:1}},[t._v("同意")]),n("el-radio",{attrs:{label:2}},[t._v("不同意")]),n("el-radio",{attrs:{label:3}},[t._v("驳回")])],1)],1)],1)],1)},o=[]},"3af0":function(t,e,n){"use strict";n.r(e);var a=n("e72c"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"66ec":function(t,e,n){},"6bec":function(t,e,n){},"7ca4":function(t,e,n){"use strict";n.r(e);var a=n("bd37"),o=n("3af0");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("eac7"),n("fd35");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"16fd3b1f",null);e["default"]=s.exports},8759:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={props:["value"],computed:{comment_disabled:{get:function(){return this.value},set:function(t){this.$emit("input",t)}}},methods:{getTxt:function(){switch(this.comment_disabled){case 1:return"同意";case 2:return"不同意";case 3:return"驳回";default:break}}}}},"89b7":function(t,e,n){},b0ab:function(t,e,n){"use strict";n.r(e);var a=n("16c0"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},bac8:function(t,e,n){"use strict";n.r(e);var a=n("1f3d"),o=n("13d57");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,null,null);e["default"]=s.exports},bd37:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"abs100  cs-z-flex-pd16-wrap"},[n("div",{staticClass:" cs-z-page-main-content"},[n("div",{staticStyle:{"text-align":"right","margin-bottom":"10px"}},[n("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新增")])],1),n("el-table",{staticStyle:{width:"100%"},attrs:{height:"100%",data:t.tableData}},[n("el-table-column",{attrs:{type:"index",width:"50"}}),n("el-table-column",{attrs:{type:"selection",width:"55"}}),n("el-table-column",{attrs:{align:"center",prop:"Id",label:"id"}}),n("el-table-column",{attrs:{align:"center",prop:"User_Name",label:"申请人"}}),n("el-table-column",{attrs:{align:"center",prop:"User_Name",label:"请假天数"}}),n("el-table-column",{attrs:{align:"center",prop:"Create_Date",label:"创建日期"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[t._v(" "+t._s(t._f("timeFormat")(n.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])}),n("el-table-column",{attrs:{align:"center",prop:"status",label:"状态"}}),n("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[a.hasPermission?n("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm(a)}}},[t._v("审核")]):t._e()]}}])})],1),t.pageInfo.TotalCount>0?n("pagination",{attrs:{total:t.pageInfo.TotalCount,page:t.pageInfo.Page,limit:t.pageInfo.PageSize},on:{"update:page":function(e){return t.$set(t.pageInfo,"Page",e)},"update:limit":function(e){return t.$set(t.pageInfo,"PageSize",e)},pagination:t.fetchData}}):t._e(),t.dialogVisible?n("el-dialog",{attrs:{title:"添加",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose}},[n(t.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":t.dialogVisible},on:{close:t.handleClose,refresh:t.fetchData}})],1):t._e()],1)])},o=[]},e6ca:function(t,e,n){"use strict";n("89b7")},e72c:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("14d9"),n("b0c0"),n("e9f5"),n("7d54"),n("ab43"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0");var o=n("eb4a"),r=a(n("bac8")),i=a(n("0404")),s=a(n("2082")),u=a(n("333d"));e.default={name:"FlowModuleNoteList",components:{Note:i.default,verifyStatus:r.default,Pagination:u.default},mixins:[s.default],data:function(){return{tableData:[],postObj:{},pageInfo:{Page:1,PageSize:10,TotalCount:0},currentComponent:"Note",dialogVisible:!1,addPageArray:[{path:this.$route.path+"/verify",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-de86d6a0"),n.e("chunk-54fa4a98"),n.e("chunk-3a3c0f1b"),n.e("chunk-6b48c568")]).then(n.bind(null,"5539"))},name:"FlowPendingFlowVerify",hidden:!0,meta:{title:"审核"}}]}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var t=this;(0,o.GetLeavePageList)(this.pageInfo).then((function(e){e.IsSucceed?(t.tableData=e.Data.Data,t.pageInfo.PageSize=e.Data.PageSize,t.pageInfo.Page=e.Data.Page,t.pageInfo.TotalCount=e.Data.TotalCount,t.checkVer()):t.$message({message:e.Message,type:"error"})}))},checkVer:function(){var t=this;(0,o.CheckBusinessVerification)({webId:this.$route.name,ids:this.tableData.map((function(t){return t.Id}))}).then((function(e){var n={0:"未完成",1:"完成",2:"同意",3:"表不同意",4:"驳回"};t.tableData.forEach((function(a){for(var o=0;o<e.Data.length;o++){var r=e.Data[o];a.Id!==r.Pk_Value||!r.Is_Verification||0!==r.Is_Finish&&2!==r.Is_Finish||t.$set(a,"hasPermission",!0),a.Id===r.Pk_Value&&(t.$set(a,"status",n[r.Is_Finish]),t.$set(a,"flowId",r.Id))}}))}))},submitForm:function(t){this.$router.push({name:"FlowPendingFlowVerify",query:{id:t.flowId,pg_redirect:this.$route.name}})},handleAdd:function(){this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1}}}},eac7:function(t,e,n){"use strict";n("6bec")},eb4a:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddContractPriceProcess=F,e.AddConventionalContractProcess=y,e.AddOtherSignedContractProcess=_,e.AddSealProcess=C,e.AddSpecialPurchaseProcess=P,e.AddTechSolutionProcess=I,e.CheckBusinessVerification=w,e.CheckExistFlow=f,e.FlowInstancesCancel=d,e.FlowInstancesDelete=h,e.FlowInstancesGet=b,e.FlowInstancesLoad=m,e.FlowSchemesAdd=s,e.FlowSchemesDelete=l,e.FlowSchemesGet=i,e.FlowSchemesLoad=c,e.FlowSchemesUpdate=u,e.GetContractPriceEntity=x,e.GetConventionalContractEntity=k,e.GetFileInfo=Y,e.GetFlowSchemeByFromId=D,e.GetFlowSchemeNodeByFromId=E,e.GetLeavePageList=v,e.GetListEntitiesProject=V,e.GetOneEntitiesProject=T,e.GetOtherSignedContractEntity=G,e.GetSchemeObjectIds=j,e.GetSealEntity=$,e.GetSpecialPurchaseEntity=O,e.QueryHistories=S,e.SaveBusinessData=p,e.SaveDesignFlow=A,e.Verification=g;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({method:"get",url:"/SYS/FlowSchemes/Get",params:t})}function s(t){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Add",data:t})}function u(t){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Update",data:t})}function l(t){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Delete",data:t})}function d(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/CancelFlow",data:t})}function c(t){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/Load",data:t})}function f(t){return(0,o.default)({method:"post",url:"/SYS/FlowSchemes/CheckExistFlow",data:t})}function m(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/Load",data:t})}function p(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/SaveBusinessData",data:t})}function h(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/Delete",data:t})}function b(t){return(0,o.default)({method:"get",url:"/SYS/FlowInstances/Get",params:t})}function g(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/Verification",data:t})}function S(t){return(0,o.default)({method:"get",url:"/SYS/FlowInstances/QueryHistories",params:t})}function v(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/GetLeavePageList",data:r.default.stringify(t)})}function w(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/CheckBusinessVerification",data:r.default.stringify(t)})}function y(t){return(0,o.default)({method:"post",url:"/EPC/ConventionalContract/AddProcess",data:t})}function _(t){return(0,o.default)({method:"post",url:"/EPC/OtherSignedContract/AddProcess",data:t})}function F(t){return(0,o.default)({method:"post",url:"/EPC/ContractPrice/AddProcess",data:t})}function P(t){return(0,o.default)({method:"post",url:"/EPC/SpecialPurchase/AddProcess",data:t})}function C(t){return(0,o.default)({method:"post",url:"/EPC/Seal/AddProcess",data:t})}function I(t){return(0,o.default)({method:"post",url:"/EPC/TechnicalScheme/AddProcess",data:t})}function E(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/GetFlowSchemeNodeByFromId",data:t})}function D(t){return(0,o.default)({method:"post",url:"/SYS/FlowInstances/GetFlowSchemeByFromId",data:t})}function k(t){return(0,o.default)({method:"post",url:"/EPC/ConventionalContract​/GetEntity",data:t})}function G(t){return(0,o.default)({method:"post",url:"/EPC/OtherSignedContract/GetEntity",data:t})}function x(t){return(0,o.default)({method:"post",url:"/EPC/ContractPrice/GetEntity",data:t})}function O(t){return(0,o.default)({method:"post",url:"/EPC/SpecialPurchase/GetEntity",data:t})}function $(t){return(0,o.default)({method:"post",url:"/EPC/Seal/GetEntity",data:t})}function A(t){return(0,o.default)({method:"post",url:"/SYS/Sys_File/SaveDesignFlow",data:t})}function T(t){return(0,o.default)({method:"post",url:"/Sys/Sys_FileType/GetOneEntitiesProject",data:t})}function V(t){return(0,o.default)({method:"post",url:"/Sys/Sys_FileType/GetListEntitiesProject",data:t})}function Y(t){return(0,o.default)({method:"post",url:"/Sys/Sys_FileType/GetEntity",data:t})}function j(t){return(0,o.default)({method:"post",url:"/Sys/FlowSchemes/GetSchemeObjectIds",data:t})}},eca2:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-card",{staticClass:"box-card",attrs:{shadow:"never"}},[n("el-form",{ref:"ruleForm",staticStyle:{width:"100%"},attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"姓名",prop:"User_Name"}},[n("el-input",{attrs:{disabled:!0},model:{value:t.form.User_Name,callback:function(e){t.$set(t.form,"User_Name",e)},expression:"form.User_Name"}})],1),n("el-form-item",{attrs:{label:"天数",prop:"Days"}},[n("el-input-number",{attrs:{disabled:!t.isEdit,min:1},model:{value:t.form.Days,callback:function(e){t.$set(t.form,"Days",e)},expression:"form.Days"}})],1),n("el-form-item",{attrs:{label:"原因",prop:"Request_Comment"}},[n("el-input",{attrs:{autosize:{minRows:3,maxRows:5},disabled:!t.isEdit,maxlength:"50","show-word-limit":"",type:"textarea"},model:{value:t.form.Request_Comment,callback:function(e){t.$set(t.form,"Request_Comment",e)},expression:"form.Request_Comment"}})],1)],1)],1),t.isEdit?n("el-button",{staticClass:"cs-btn",attrs:{disabled:t.btnDisabled,loading:t.btnLoading,type:"primary"},on:{click:t.handleSubmit}},[t._v("保存 ")]):t._e()],1)},o=[]},fd35:function(t,e,n){"use strict";n("66ec")}}]);