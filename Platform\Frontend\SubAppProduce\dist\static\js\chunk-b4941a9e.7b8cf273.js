(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-b4941a9e"],{1162:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i=a("7757"),n=a("4f39");e.default={props:{statisticalDate:{type:String,default:""},factoryId:{type:String,required:!0},reportType:{type:Number,required:!0}},data:function(){return{showTip:!1,time:"",curDate:""}},watch:{statisticalDate:function(t){this.curDate=t,this.curDate&&this.getUpdate()}},methods:{getUpdate:function(){var t=this;(0,i.GetBusinessLastUpdateDate)({StatisticalDate:this.curDate,FactoryId:this.factoryId,ReportType:this.reportType}).then((function(e){if(e.IsSucceed){var a=e.Data,i=a.Business_Last_Date,r=a.Account_Generate_Date,o=new Date(i),l=new Date(r);o>l?(t.showTip=!0,t.time=(0,n.parseTime)(o)):(t.showTip=!1,t.time="")}else t.$message({message:e.Message,type:"error"})}))}}}},"1b7e":function(t,e,a){"use strict";a.r(e);var i=a("1162"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"2a80":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.exportExcel=void 0,a("99af"),a("cb29"),a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var n=i(a("c14f")),r=i(a("1da1")),o=i(a("e8ae")),l=i(a("21a6"));e.exportExcel=function(){var t=(0,r.default)((0,n.default)().m((function t(e,a){var i,r,s,c,u,d,f,m;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return i=new o.default.Workbook,r=i.addWorksheet("Sheet1"),r.properties.defaultColWidth=25,r.properties.defaultRowHeight=20,s=r.addRow(a.map((function(t){return t.header}))),r.columns=a,r.addRows(e),c={fill:{type:"pattern",pattern:"solid",fgColor:{argb:"D2F4F2"}},border:{top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}},s.eachCell({includeEmpty:!0},(function(t){t.fill=c.fill,t.border=c.border,t.alignment={vertical:"middle",horizontal:"center"}})),u={color:{argb:"FF0000"}},r.getCell("E1").font=u,d=r.getColumn("F"),d.eachCell({includeEmpty:!0},(function(t,e){if(e>1){var a=t.value;t.value={formula:"SUM(D".concat(e,", E").concat(e,")"),result:a}}})),t.n=1,i.xlsx.writeBuffer();case 1:f=t.v,m=new Blob([f],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),l.default.saveAs(m,"修正数据模板.xlsx");case 2:return t.a(2)}}),t)})));return function(e,a){return t.apply(this,arguments)}}()},"3fe7":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",data:t.tableData,stripe:"",loading:t.loading,align:"left",height:"500",resizable:"","empty-text":"暂无数据"}},[a("vxe-column",{attrs:{fixed:"left","min-width":"200",field:"ProjectAbbreviation",title:"项目简称"}}),a("vxe-column",{attrs:{fixed:"left","min-width":"200",field:"ProjectNumber",title:"项目编号"}}),a("vxe-column",{attrs:{"min-width":"200",field:"Fix_Value",title:"生产产值修正值"}}),a("vxe-column",{attrs:{"min-width":"200",field:"Remark",title:"备注说明"}}),a("vxe-column",{attrs:{"min-width":"150",field:"Create_UserName",title:"操作人"}}),a("vxe-column",{attrs:{"min-width":"200",field:"Create_Date",title:"操作时间"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])})],1),a("div",{staticClass:"cs-footer"},[a("el-button",{on:{click:t.handleClose}},[t._v("取 消")])],1)],1)},n=[]},"40c7":function(t,e,a){"use strict";a("85ce")},"427f":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"数据是否调整",prop:"IsAdjust"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.IsAdjust,callback:function(e){t.$set(t.form,"IsAdjust",e)},expression:"form.IsAdjust"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"有调整",value:!0}}),a("el-option",{attrs:{label:"无调整",value:!1}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:t.loading},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("UpdateDate",{ref:"updateDate",attrs:{"statistical-date":t.form.StatisticalDate,"factory-id":t.form.FactoryId,"report-type":2}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showBtn,expression:"showBtn"}],staticClass:"btn-x"},[t.isView&&t.unCheck&&t.getRoles("OMAProUpdate")?a("el-button",{attrs:{disabled:t.loading,loading:t.updateBtn},on:{click:t.handleUpdate}},[t._v("更新数据")]):t._e(),t.isView&&t.unCheck&&t.getRoles("OMAProImport")?a("el-button",{attrs:{disabled:t.loading},on:{click:t.handleImport}},[t._v("导入修正记录")]):t._e(),t.getRoles("OMAProRecord")?a("el-button",{on:{click:t.handleRecord}},[t._v(" 修正记录")]):t._e(),t.getRoles("OMAProDetailExport")?a("el-button",{attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e(),t.unCheck&&t.getRoles("OMAProSubmit")?a("el-button",{attrs:{type:"primary",disabled:t.loading},on:{click:t.handleSubmit}},[t._v("提交核算")]):t._e(),t.toBeConfirmed&&t.getRoles("OMAProCheck")?[a("el-button",{attrs:{type:"danger",disabled:t.loading},on:{click:function(e){return t.handleCheck(!1)}}},[t._v("审核不通过")]),a("el-button",{attrs:{type:"success",disabled:t.loading},on:{click:function(e){return t.handleCheck(!0)}}},[t._v("审核通过")])]:t._e()],2)],1),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1),t.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.width},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleClose,refresh:t.fetchData}},[a(t.currentComponent,{ref:"content",tag:"component",on:{close:t.handleClose,refresh:t.fetchData}})],1):t._e()],1)])},n=[]},"44bf":function(t,e,a){"use strict";a("df64")},"4f39":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=r,e.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var n=i(a("53ca"));function r(t,e){if(0===arguments.length||!t)return null;var a,i=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,n.default)(t)?a=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),a=new Date(t));var r={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=i.replace(/{([ymdhisa])+}/g,(function(t,e){var a=r[e];return"a"===e?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var a=t.split("~"),i=r(new Date(a[0]),e)+" ~ "+r(new Date(a[1]),e);return i}return t&&t.length>0?r(new Date(t),e):void 0}},"57ae":function(t,e,a){"use strict";a.r(e);var i=a("b375c"),n=a("7e1c");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("40c7");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"73e4fcd6",null);e["default"]=l.exports},"58eb":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7");var n=a("d7ff"),r=i(a("6612"));e.default={data:function(){return{tableData:[],loading:!1}},methods:{fetchData:function(t){var e=this;this.loading=!0;var a=t.StatisticalDate,i=t.FactoryId;(0,n.GetFixRecord)({StatisticalDate:a,Fix_Business_Type:1,FactoryId:i}).then((function(t){t.IsSucceed?e.tableData=((null===t||void 0===t?void 0:t.Data)||[]).map((function(t){return t.Fix_Value=(0,r.default)(t.Fix_Value).format("0,0.[00]"),t})):e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1}))},handleClose:function(){this.$emit("close")}}}},6776:function(t,e,a){"use strict";a.r(e);var i=a("8501"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},7288:function(t,e,a){},"7e1c":function(t,e,a){"use strict";a.r(e);var i=a("9527"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},8501:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("ab43"),a("d3b7");var n=i(a("c14f")),r=i(a("1da1")),o=i(a("ac03")),l=i(a("97176")),s=i(a("9769")),c=i(a("57ae")),u=a("cf45"),d=a("4f39"),f=a("d7ff"),m=i(a("3502")),h=i(a("90f5")),p=a("8ff5"),b=a("db0a"),g=a("05e0");e.default={name:"OMAProductionReportDetail",components:{UpdateDate:h.default,VTable:m.default,RecordDialog:s.default,ImportFile:c.default,CorrectDialog:l.default},mixins:[o.default],data:function(){return{addPageArray:[],unCheck:!1,dialogVisible:!1,title:"",currentComponent:"",width:"40%",updateBtn:!1,showBtn:!1,isView:!1,toBeConfirmed:!1,form:{IsAdjust:"",StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[]}},computed:{curTitle:function(){return"".concat((0,d.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计金额（元）")}},beforeCreate:function(){this.curModuleKey=p.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,t.toBeConfirmed="check"===t.$route.query.type,t.isView="view"===t.$route.query.type,t.fetchData(),e.n=1,(0,u.getDictionary)("Engineering Status");case 1:t.projectOption=e.v;case 2:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate()&&(this.loading=!0,(0,f.GetSCProjectSummaryList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[],t.tableData.length?(t.toBeConfirmed=2===t.tableData[0].AccountingStatus,t.unCheck=1===t.tableData[0].AccountingStatus,t.isView=!t.toBeConfirmed,t.showBtn=!0):t.showBtn=!1;var a=t.setTotalData(t.tableData,t.generateColumn()),i=a.column;t.columns=i,t.$refs["tb"].setColumns(i)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleClose:function(){this.dialogVisible=!1},handleUpdate:function(){var t=this;this.$confirm("是否更新数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.updateBtn=!0,(0,b.DailyBatch)({Factory_Id:t.form.FactoryId,Date:t.form.StatisticalDate}).then((function(e){e.IsSucceed?(t.fetchData(),t.$refs["updateDate"].getUpdate(),t.$message({message:"更新成功",type:"success"})):t.$message({message:e.Message,type:"error"}),t.updateBtn=!1}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleSubmit:function(){var t=this;this.$confirm("是否提交核算?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.SubmitAccounting)({StatisticalDate:t.form.StatisticalDate,FactoryId:t.form.FactoryId}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消"})}))},handleRecord:function(){var t=this;this.currentComponent="RecordDialog",this.title="修正记录",this.width="70%",this.dialogVisible=!0,this.$nextTick((function(e){t.$refs["content"].fetchData({StatisticalDate:t.form.StatisticalDate,FactoryId:t.form.FactoryId})}))},handleProductionInfo:function(){this.$router.push({name:"OMAProductionOutputInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleProductionCostInfo:function(){this.$router.push({name:"OMAProductionCostDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleProductionFeeInfo:function(){this.$router.push({name:"OMAProductionFeeDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleProductionBalanceInfo:function(){this.$router.push({name:"OMAProductionBalanceDetailInfo",query:{pg_redirect:this.$route.name,d:this.form.StatisticalDate}})},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey="",this.form.IsAdjust=""},handleCheck:function(t){var e=this;this.$confirm("是否确定审核".concat(t?"通过":"不通过","?"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,f.ReviewStockControlSummary)({FactoryId:e.form.FactoryId,Is_Approved:t,StatisticalDate:e.form.StatisticalDate}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handleEdit:function(t,e){var a=this;this.currentComponent="CorrectDialog",this.title=e?"查看修正值":"编辑修正值",this.width="40%",this.dialogVisible=!0,this.$nextTick((function(i){a.$refs["content"].initData({isDetail:e,Statics_Date:a.form.StatisticalDate,Factory_Id:a.form.FactoryId,Sys_Project_Id:t.Sys_Project_Id,ProjectAbbreviation:t.ProjectAbbreviation,ProjectNumber:t.ProjectNumber,ProjectStatus:t.ProjectStatus})}))},handleImport:function(){var t=this;this.currentComponent="ImportFile",this.title="导入修正记录";var e=[{header:"项目简称",key:"ProjectAbbreviation",width:15},{header:"项目编号",key:"ProjectNumber",width:15},{header:"项目状态",key:"ProjectStatus",width:15},{header:"生产产值-成品产值(元)",key:"Product_Output",width:20},{header:"生产产值-修正值(元)",key:"Product_Output_Fix",width:20},{header:"生产产值-小计(元)",key:"Output_SubTotal",width:20}];this.tableData.map((function(t){return{ProjectAbbreviation:t.ProjectAbbreviation,ProjectNumber:t.ProjectNumber,ProjectStatus:t.ProjectStatus,Product_Output:t.Product_Output,Product_Output_Fix:t.Product_Output_Fix,Output_SubTotal:t.Output_SubTotal}})),this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].setInfo(t.tableData,e,{Date:t.form.StatisticalDate,Factory_Id:t.form.FactoryId})}))},generateColumn:function(){var t=this,e=this.$createElement,a=140;return[{fixed:"left",title:"项目简称",params:{rowSpan:2},children:[{params:{none:"none"},children:[{title:this.curTitle,params:{colSpan:3},minWidth:g.ProjectAbbreviationW,field:"ProjectAbbreviation"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目编号",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:g.ProjectNumberW,field:"ProjectNumber"}]}]},{fixed:"left",params:{rowSpan:2},title:"项目状态",children:[{params:{none:"none"},children:[{params:{none:"none"},minWidth:g.ProjectStatusW,field:"ProjectStatus"}]}]},{params:{bg:"bg-green"},slots:{header:function(){var a=[e("span",["生产产值(元) "])];return t.getRoles("OMAProOutputDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleProductionInfo()}}},["查看详情"])),a}},children:[{title:"成品产值(元)",children:[{minWidth:a,field:"Product_Output",title:0,isTotal:!0}]},{title:"修正值(元)",children:[{minWidth:a,field:"Product_Output_Fix",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Output_SubTotal",title:0,isTotal:!0}]}]},{params:{bg:"bg-cyan"},title:"营业外收入",children:[{title:"小计(元)",children:[{minWidth:a,field:"NonBusiness_Income",title:0,isTotal:!0}]}]},{params:{bg:"bg-blue"},slots:{header:function(){var a=[e("span",["生产成本(元) "])];return t.getRoles("OMAProCostDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleProductionCostInfo()}}},["查看详情"])),a}},children:[{title:"主材成本-采购(元)",children:[{minWidth:a,field:"Raw_Purchase_Cost_Production",title:0,isTotal:!0}]},{title:"主材成本-甲供(元)",children:[{minWidth:a,field:"Raw_Self_Supplying_Cost_Production",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Cost_SubTotal",title:0,isTotal:!0}]}]},{title:"资产折旧(元)",params:{bg:"bg-purple"},children:[{title:"折旧费用(元)",children:[{minWidth:a,field:"Assets_Depreciation_Fee",title:0,isTotal:!0}]}]},{params:{bg:"bg-yellow"},slots:{header:function(){var a=[e("span",["生产费用(元) "])];return t.getRoles("OMAProFeeDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleProductionFeeInfo()}}},["查看详情"])),a}},children:[{title:"项目辅材(元)",children:[{minWidth:a,field:"Project_Aux_Fee",title:0,isTotal:!0}]},{title:"公共辅材(元)",children:[{minWidth:a,field:"Public_Aux_Fee",title:0,isTotal:!0}]},{title:"劳资-生产工人",children:[{minWidth:a,field:"Labor_Salary",title:0,isTotal:!0}]},{title:"劳资-工序外包",children:[{minWidth:a,field:"External_Labor_Salary",title:0,isTotal:!0}]},{title:"管理费用(元)",children:[{minWidth:a,field:"Manage_Fee",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Fees_SubTotal",title:0,isTotal:!0}]}]},{title:"生产结存(元)",params:{bg:"bg-orange"},slots:{header:function(){var a=[e("span",["生产结存(元) "])];return t.getRoles("OMAProBaDetail")&&a.push(e("span",{style:"color:#0064FB;cursor:pointer",on:{click:function(){return t.handleProductionBalanceInfo()}}},["查看详情"])),a}},children:[{title:"半成品主材结存(元)",children:[{minWidth:160,field:"Semi_Finished_Stock",title:0,isTotal:!0}]},{title:"项目辅材结存(元)",children:[{minWidth:a,field:"Project_Aux_Stock",title:0,isTotal:!0}]},{title:"小计(元)",children:[{minWidth:a,field:"Stock_SubTotal",title:0,isTotal:!0}]}]},{title:"生产利润(元)",params:{bg:"bg-pink"},children:[{title:"产值+营业外收入-成本-资产折旧-费用+结存差",children:[{minWidth:380,field:"Profit",title:0,isTotal:!0}]}]},{title:"操作",params:{empty:!0},visible:!0,fixed:"right",minWidth:a,slots:{default:function(a){var i=a.row,n=a.column,r=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleEdit(i)}}},["编辑"]),o=e("el-button",{attrs:{type:"text"},on:{click:function(){return t.handleEdit(i,!0)}}},["修正详情"]);if(t.toBeConfirmed){if(t.getRoles("OMAProCheck"))return n.params.empty=!1,[o]}else if(t.unCheck&&t.getRoles("OMAProEdit"))return n.params.empty=!1,[r]}}}]}}}},"85ce":function(t,e,a){},"90f5":function(t,e,a){"use strict";a.r(e);var i=a("b5756"),n=a("1b7e");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("e209");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"79bd74a2",null);e["default"]=l.exports},9527:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("5530")),r=a("2a80"),o=a("d7ff"),l=i(a("bbc2")),s=a("ed08");e.default={components:{OSSUpload:l.default},data:function(){return{btnLoading:!1,fileList:[]}},methods:{getTemplate:function(){(0,r.exportExcel)(this.tb,this.header)},uploadSuccess:function(t,e,a){this.File_Url=t.Data.split("*")[0],this.File_Name=t.Data.split("*")[3],this.fileList=[e]},handleSubmit:function(){var t=this;this.btnLoading=!0,(0,o.ImportSCFixRecord)((0,n.default)({File_Url:this.File_Url,File_Name:this.File_Name},this.info)).then((function(e){if(e.IsSucceed)t.$message({type:"success",message:"导入成功"}),t.$emit("close"),t.$emit("refresh");else if(t.$message({message:e.Message,type:"error"}),e.Data){var a=(0,s.combineURL)(t.$baseUrl,e.Data);window.open(a,"_blank")}t.btnLoading=!1}))},setInfo:function(t,e,a){this.tb=t,this.header=e,this.info=a}}}},97176:function(t,e,a){"use strict";a.r(e);var i=a("988e"),n=a("dcbe");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("a454");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"3fab3b8f",null);e["default"]=l.exports},9769:function(t,e,a){"use strict";a.r(e);var i=a("3fe7"),n=a("aa42");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("e299");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"2fed517c",null);e["default"]=l.exports},"988e":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"150px"}},[a("el-form-item",{attrs:{label:"项目简称"}},[t._v(" "+t._s(t.form.ProjectAbbreviation)+" ")]),a("el-form-item",{attrs:{label:"项目编号",prop:"ProjectNumber"}},[t._v(" "+t._s(t.form.ProjectNumber)+" ")]),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[t._v(" "+t._s(t.form.ProjectStatus)+" ")]),a("el-form-item",{attrs:{label:"生产产值修正值",prop:"Fix_Value"}},[t.isDetail?a("span",[t._v(t._s(t.form.Fix_Value))]):a("el-input-number",{staticClass:"w100 cs-number-btn-hidden",model:{value:t.form.Fix_Value,callback:function(e){t.$set(t.form,"Fix_Value",e)},expression:"form.Fix_Value"}})],1),a("el-form-item",{attrs:{label:"备注说明",prop:"Remark"}},[t.isDetail?a("span",[t._v(t._s(t.form.Remark))]):a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"200",type:"textarea"},model:{value:t.form.Remark,callback:function(e){t.$set(t.form,"Remark",e)},expression:"form.Remark"}})],1),a("el-form-item",{staticClass:"cs-footer"},[a("el-button",{on:{click:t.handleClose}},[t._v("取 消")]),a("el-button",{attrs:{loading:t.btnLoading,type:"primary"},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},n=[]},"994b":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7");var i=a("d7ff");e.default={data:function(){return{form:{ProjectAbbreviation:"",ProjectNumber:"",ProjectStatus:"",Fix_Value:void 0,Remark:""},btnLoading:!1,isDetail:!1,loading:!1,rules:{Fix_Value:[{required:!0,message:"请输入",trigger:"blur"}]}}},methods:{initData:function(t){this.otherData=t;var e=t.ProjectStatus,a=t.Statics_Date,i=t.Factory_Id,n=t.Sys_Project_Id,r=t.ProjectAbbreviation,o=t.ProjectNumber,l=t.isDetail;this.isDetail=l,this.form.ProjectAbbreviation=r,this.form.ProjectNumber=o,this.form.ProjectStatus=e,this.getInfo({StatisticalDate:a,FactoryId:i,Fix_Business_Type:1,Sys_Project_Id:n,Is_Lastest:!0})},getInfo:function(t){var e=this;this.loading=!0,(0,i.GetFixRecord)(t).then((function(t){if(t.IsSucceed){var a=t.Data;if(a&&a.length){var i=a[0];e.form.Remark=i.Remark,e.form.Fix_Value=i.Fix_Value}else e.form.Remark="",e.form.Fix_Value=""}else e.$message({message:t.Message,type:"error"})})).finally((function(){e.loading=!1}))},handleSubmit:function(){var t=this;this.$refs["form"].validate((function(e){if(e){var a=t.otherData,n=a.Statics_Date,r=a.Factory_Id,o=a.Sys_Project_Id;t.btnLoading=!0,(0,i.SubmitFixValue)({Statics_Date:n,Factory_Id:r,Sys_Project_Id:o,Fix_Value:t.form.Fix_Value,Remark:t.form.Remark,Fix_Business_Type:1}).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.$emit("refresh"),t.handleClose()):t.$message({message:e.Message,type:"error"})})).finally((function(){t.btnLoading=!1}))}}))},handleClose:function(){this.$emit("close")}}}},a454:function(t,e,a){"use strict";a("7288")},aa42:function(t,e,a){"use strict";a.r(e);var i=a("58eb"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},af23c:function(t,e,a){"use strict";a.r(e);var i=a("427f"),n=a("6776");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("44bf");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"31dc967a",null);e["default"]=l.exports},b375c:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"cs-alert"},[a("el-button",{attrs:{type:"text"},on:{click:t.getTemplate}},[t._v("点击此处下载导入模板")])],1),a("OSSUpload",{ref:"upload",staticClass:"cs-upload",attrs:{drag:"",action:"",limits:1,"file-list":t.fileList,"on-success":t.uploadSuccess,"show-file-list":!0,accept:".xls, .xlsx"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),a("em",[t._v("点击上传")])])]),a("footer",{staticClass:"cs-footer"},[a("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},n=[]},b5756:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",[a("span",{staticClass:"cs-label"},[t._v("数据列表")]),t.time?a("span",{staticClass:"cs-time"},[t._v("数据更新时间："+t._s(t.time))]):t._e(),t.showTip?a("span",{staticClass:"cs-tip"},[t._v("有数据变更请点击更新数据")]):t._e()])},n=[]},cb29:function(t,e,a){"use strict";var i=a("23e7"),n=a("81d5"),r=a("44d2");i({target:"Array",proto:!0},{fill:n}),r("fill")},cbdc:function(t,e,a){},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,a("d3b7");var i=a("6186");function n(t){return new Promise((function(e,a){(0,i.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},db0a:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DailyBatch=u,e.GetAccountingDefaultSetting=o,e.GetProjectAccountingSettingDetail=l,e.GetProjectListForAccounting=r,e.SaveDefaultAccountingSetting=c,e.SaveProjectAccountingSetting=s;var n=i(a("b775"));function r(t){return(0,n.default)({url:"/oma/AccountingSetting/GetProjectListForAccounting",method:"post",data:t})}function o(t){return(0,n.default)({url:"/oma/AccountingSetting/GetAccountingDefaultSetting",method:"post",data:t})}function l(t){return(0,n.default)({url:"/oma/AccountingSetting/GetProjectAccountingSettingDetail",method:"post",data:t})}function s(t){return(0,n.default)({url:"/oma/AccountingSetting/SaveProjectAccountingSetting",method:"post",data:t})}function c(t){return(0,n.default)({url:"/oma/AccountingSetting/SaveDefaultAccountingSetting",method:"post",data:t})}function u(t){return(0,n.default)({url:"/oma/AccountingSetting/DailyBatch",method:"post",data:t})}},dcbe:function(t,e,a){"use strict";a.r(e);var i=a("994b"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},df64:function(t,e,a){},e0f1:function(t,e,a){},e209:function(t,e,a){"use strict";a("cbdc")},e299:function(t,e,a){"use strict";a("e0f1")}}]);