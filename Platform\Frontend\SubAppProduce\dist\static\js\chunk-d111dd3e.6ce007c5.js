(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-d111dd3e"],{"01eb":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("c14f")),l=a(i("1da1"));i("4de4"),i("7db0"),i("c740"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("e9c4"),i("b64b"),i("d3b7"),i("159b");var s,r=i("0f64"),o=a(i("3fb2")),d=i("7e18"),p=i("641a"),c=(i("4744"),a(i("c0e9"))),h=i("2d91"),m=i("ed08");r.hiPrintPlugin.disAutoConnect();t.default={name:"ShipTemplatePrintDetail",data:function(){return{curPaper:{type:"A4",width:210,height:296.6},curPaperType:"A4",paperTypes:(0,m.deepClone)(p.paperTypes),paperWidth:"220",paperHeight:"80",tmplList:[],mode:1,activeIndex:"",keyword:"",toEdit:"",form:{Name:"",Type:1,Data:"",Base64Image:""},logoUrl:i("f8ae"),scaleValue:1,scaleMax:5,scaleMin:.5}},computed:{filteredTmplList:function(){var e=this;return this.tmplList.filter((function(t){return t.Name.indexOf(e.keyword)>-1}))}},mounted:function(){this.init(),this.buildLeftElement(),this.buildDesigner()},methods:{changeScale:function(e){var t=this.scaleValue;e?(t+=.1,t>this.scaleMax&&(t=5)):(t-=.1,t<this.scaleMin&&(t=.5)),s&&(s.zoom(t),this.scaleValue=t)},getImage:function(){return(0,l.default)((0,n.default)().m((function e(){var t,i;return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,c.default)(document.getElementById("hiprint-printTemplate"),{useCORS:!0});case 1:return t=e.v,i=t.toDataURL(),e.a(2,i)}}),e)})))()},getLogo:function(){var e=this;(0,h.GetCompany)().then((function(t){e.logoUrl=t.Data.Icon}))},tmplSelect:function(e){this.toEdit="",this.activeIndex=e,this.form&&this.form.Id===e||this.loadTemplate(e)},cloneTemplate:function(){var e=this;return(0,l.default)((0,n.default)().m((function t(){var i;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return i=JSON.parse(JSON.stringify(e.form)),delete i.Id,t.n=1,(0,d.SavePrintTemplateEntity)(i).then((function(t){t.IsSucceed?(e.$message.success("复制成功"),e.getTemplateList()):e.$message.error(t.Message)}));case 1:return t.a(2)}}),t)})))()},deleteTemplate:function(e){var t=this;this.$confirm("是否删除所选内容","提示",{confirmButtonText:"确定",cancelButtonText:"取消",center:!0}).then((function(){(0,d.DeletePrintTemplate)({id:e}).then((function(e){e.IsSucceed?(t.$message.success("删除成功"),t.getTemplateList()):t.$message.error(e.Message)})),t.toEdit&&(t.toEdit="")})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},init:function(){var e=this;return(0,l.default)((0,n.default)().m((function t(){var i;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.getLogo(),i=o.default.find((function(t){return t.value==e.mode})),r.hiprint.init({providers:[i.f]}),e.getTemplateList();case 1:return t.a(2)}}),t)})))()},buildLeftElement:function(){r.hiprint.PrintElementTypeManager.buildByHtml($(".ep-draggable-item"))},buildDesigner:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};$("#hiprint-printTemplate").empty(),s=new r.hiprint.PrintTemplate({template:e,settingContainer:"#PrintElementOptionSetting"}),s.design("#hiprint-printTemplate")},handlePrint:function(){var e={},t=s.getJson().panels[0].printElements;t.forEach((function(t){"Table"==t.options.field?e[t.options.field]=JSON.parse(t.options.testData):e[t.options.field]=t.options.testData||t.options}));var i={leftOffset:-1,topOffset:-1},a={callback:function(){}};s.print(e,i,a)},changePaper:function(){var e=this,t=this.paperTypes.find((function(t){return t.type===e.curPaperType}));"自定义纸张"===this.curPaperType?s.setPaper(this.paperWidth,this.paperHeight):s.setPaper(t.width,t.height)},createTemplate:function(){this.form={Name:"",Type:1,Data:"",Base64Image:""},this.clearTemplate()},saveTemplate:function(){var e=this;return(0,l.default)((0,n.default)().m((function t(){var i;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getImage();case 1:if(e.form.Base64Image=t.v,e.form.Name){t.n=2;break}return e.$message.error("请输入模板名称"),t.a(2);case 2:i=s.getJson(),e.form.Data=JSON.stringify(i),(0,d.SavePrintTemplateEntity)(e.form).then((function(t){t.IsSucceed?(e.$message.success("保存成功"),e.getTemplateList()):e.$message.error(t.Message)}));case 3:return t.a(2)}}),t)})))()},loadTemplate:function(e){var t=this;return(0,l.default)((0,n.default)().m((function i(){var a,l,s,r,o,p,c,h;return(0,n.default)().w((function(i){while(1)switch(i.n){case 0:return t.clearTemplate(),i.n=1,(0,d.GetPrintTemplateEntity)({id:e});case 1:a=i.v,t.form=a.Data,l=JSON.parse(a.Data.Data);try{s=l.panels[0].printElements.findIndex((function(e){return"Logo"===e.options.field})),l.panels[0].printElements[s].options.src=t.logoUrl}catch(n){}r=l,t.buildDesigner(r),o=r.panels[0],p=o.width,c=o.height,h=t.paperTypes.find((function(e){return e.width==p&e.height==c})),t.curPaper=h||{type:"自定义纸张",width:p,height:c},t.curPaperType=t.curPaper.type,t.paperWidth=p,t.paperHeight=c,t.changePaper();case 2:return i.a(2)}}),i)})))()},clearTemplate:function(){$("#hiprint-printTemplate").empty(),this.buildDesigner()},getTemplateList:function(){var e=this;return(0,l.default)((0,n.default)().m((function t(){var i;return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,d.GetPrintTemplateList)({type:e.mode});case 1:i=t.v,e.tmplList=i.Data;case 2:return t.a(2)}}),t)})))()}}}},"3fb2":function(e,t,i){"use strict";var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.DeliveryNoteProvider=void 0,i("e9c4");var n=a(i("c14f")),l=a(i("1da1")),s=i("0f64"),r=(i("4744"),i("2d91")),o=t.DeliveryNoteProvider=function(e){var t=function(){var e=(0,l.default)((0,n.default)().m((function e(t){var i,a;return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,r.GetCompany)();case 1:i=e.v,a=i.Data.Icon,t.removePrintElementTypes("Shipment"),t.addPrintElementTypes(1,[new s.hiprint.PrintElementTypeGroup("平台",[{tid:"Shipment.Logo",title:"Logo图片",data:"",type:"image",options:{field:"Logo",height:40,width:40,src:a}},{tid:"Shipment.QrcodeText",title:"二维码",data:"XS888888888",type:"text",options:{field:"QrcodeText",testData:"XS888888888",height:64,fontSize:12,lineHeight:18,textType:"qrcode"}},{tid:"Shipment.Code",title:"单据号",data:"BIM-CK-00249",type:"text",options:{testData:"BIM-CK-00249",field:"Code",fontSize:12,width:200}},{tid:"Shipment.SendDate",title:"日期",data:"2024-04-03",type:"text",options:{testData:"2024-04-03",fontSize:12,field:"SendDate"}},{tid:"Shipment.Number",title:"发货序号",data:"1",type:"text",options:{field:"Number",testData:"1",fontSize:12}},{tid:"Shipment.Address",title:"项目地址",data:"浙江省绍兴市柯桥区鉴湖路1587号",type:"text",options:{field:"Address",testData:"浙江省绍兴市柯桥区鉴湖路1587号",fontSize:12,width:300}},{tid:"Shipment.ProjectName",title:"项目名称",data:"比姆泰客测试项目",type:"text",options:{field:"ProjectName",testData:"比姆泰客测试项目",fontSize:12,width:250}},{tid:"Shipment.MakerName",title:"出库人",data:"Lily",type:"text",options:{field:"MakerName",testData:"Lily",fontSize:12}},{tid:"Shipment.Consignee",title:"收货人",data:"Lily",type:"text",options:{field:"Consignee",testData:"Lily",fontSize:12}},{tid:"Shipment.ConsigneeTel",title:"联系电话",data:"185****4235",type:"text",options:{field:"ConsigneeTel",testData:"185****4235",fontSize:12,width:150}},{tid:"Shipment.VehicleNo",title:"车牌",data:"浙D388432",type:"text",options:{field:"VehicleNo",testData:"浙D388432",fontSize:12}},{tid:"Shipment.Telephone",title:"司机电话",data:"185****4235",type:"text",options:{field:"Telephone",testData:"185****4235",fontSize:12,width:150}},{tid:"Shipment.Pound_Weight",title:"磅重（kg）",data:"5369.64",type:"text",options:{field:"Pound_Weight",testData:"5369.64",fontSize:12,width:150}},{tid:"Shipment.Tare_Weight",title:"皮重（kg）",data:"5369.64",type:"text",options:{field:"Tare_Weight",testData:"5369.64",fontSize:12,width:150}},{tid:"Shipment.Net_Weight",title:"净重（kg）",data:"5369.64",type:"text",options:{field:"Net_Weight",testData:"5369.64",fontSize:12,width:150}},{tid:"Shipment.customText",title:"自定义文本",customText:"自定义文本",custom:!0,type:"text"}]),new s.hiprint.PrintElementTypeGroup("表格/其他",[{tid:"Shipment.Table",title:"订单数据",type:"table",options:{field:"Table",tableHeaderRepeat:"first",tableFooterRepeat:"last",fields:[{text:"序号",field:"RowNo"},{text:"构件/包名称",field:"SteelName"},{text:"数量",field:"SteelAmount"},{text:"规格",field:"SteelSpec"},{text:"长度",field:"SteelLength"},{text:"单重（kg）",field:"SteelWeight"},{text:"总重（kg）",field:"SteelAllWeight"},{text:"单毛重（kg）",field:"GrossWeight"},{text:"总毛重（kg）",field:"GrossAllWeight"},{text:"备注（要货区域）",field:"AreaPosition"}],testData:JSON.stringify([{SteelName:"构件包-1",SteelAmount:2,SteelSpec:"PKG00068",SteelLength:"50",AreaPosition:"第一批次加工构件",SteelWeight:"1000",SteelAllWeight:"2000",GrossWeight:"1050",GrossAllWeight:"2100",RowNo:1,Unit:"kg",IsPackage:!0},{SteelName:"AKP-001",SteelAmount:2,SteelSpec:"88*88",SteelLength:"50",AreaPosition:"第一批次加工构件",SteelWeight:"1000",SteelAllWeight:"2000",GrossWeight:"1050",GrossAllWeight:"2100",RowNo:2,Unit:"kg"},{SteelName:"AKP-002",SteelAmount:3,SteelSpec:"88*88",SteelLength:"50",AreaPosition:"第一批次加工构件",SteelWeight:"1000",SteelAllWeight:"3000",GrossWeight:"1050",GrossAllWeight:3150,RowNo:3,Unit:"kg"},{SteelName:"合计",SteelAmount:5,SteelSpec:"",SteelLength:"",AreaPosition:"",SteelWeight:"",SteelAllWeight:"5000",GrossWeight:"",GrossAllWeight:7350,RowNo:4,Unit:""},{SteelName:"备注",SteelAmount:5,SteelSpec:"",SteelLength:"",AreaPosition:"",SteelWeight:"",SteelAllWeight:"5000",GrossWeight:"",GrossAllWeight:7350,RowNo:4,Unit:"",IsRemark:!0}])},editable:!0,columnDisplayEditable:!0,columnDisplayIndexEditable:!0,columnTitleEditable:!0,columnResizable:!0,columnAlignEditable:!0,isEnableEditField:!0,isEnableContextMenu:!0,isEnableInsertRow:!0,isEnableDeleteRow:!0,isEnableInsertColumn:!0,isEnableDeleteColumn:!0,isEnableMergeCell:!0,columns:[[{title:"序号",align:"center",field:"RowNo",width:80,checked:!1,drag:!1,isDrag:!1},{title:"构件/包名称",align:"center",field:"SteelName",width:140,checked:!0,drag:!1,isDrag:!1},{title:"规格",align:"center",field:"SteelSpec",width:100,checked:!0,isDrag:!1},{title:"长度",align:"center",field:"SteelLength",width:80,checked:!0},{title:"数量",align:"center",field:"SteelAmount",width:80,checked:!0},{title:"单重（kg）",align:"center",field:"SteelWeight",width:110,checked:!0},{title:"总重（kg）",align:"center",field:"SteelAllWeight",width:110,checked:!0},{title:"单毛重（kg）",align:"center",field:"GrossWeight",width:120,checked:!1},{title:"总毛重（kg）",align:"center",field:"GrossAllWeight",width:120,checked:!1},{title:"备注（要货区域）",align:"center",field:"AreaPosition",width:160,checked:!0}]],rowsColumnsMerge:function(e,t,i){return"SteelSpec"===t.field&&e.IsPackage?[1,100]:"RowNo"!==t.field&&"SteelName"!==t.field&&e.IsPackage?[1,0]:1===i&&e.IsRemark?[1,100]:0!==i&&e.IsRemark?[1,0]:void 0}}]),new s.hiprint.PrintElementTypeGroup("辅助",[{tid:"Shipment.hline",title:"横线",type:"hline",options:{field:"hline"}},{field:"vline",tid:"Shipment.vline",title:"竖线",type:"vline",options:{field:"vline"}},{field:"rect",tid:"Shipment.rect",title:"矩形",type:"rect",options:{field:"rect"}},{field:"oval",tid:"Shipment.oval",title:"椭圆",type:"oval",options:{field:"oval"}}])]);case 2:return e.a(2)}}),e)})));return function(t){return e.apply(this,arguments)}}();return{addElementTypes:t}};t.default=[{name:"发货单",value:1,f:o()}]},"447f":function(e,t,i){"use strict";i("b459")},"512e":function(e,t,i){"use strict";i.r(t);var a=i("ea2e"),n=i("b889");for(var l in n)["default"].indexOf(l)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(l);i("447f");var s=i("2877"),r=Object(s["a"])(n["default"],a["a"],a["b"],!1,null,"dbb88578",null);t["default"]=r.exports},"641a":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.templateTypes=t.paperTypes=void 0;t.paperTypes=[{type:"A3",width:420,height:296.6},{type:"A4",width:210,height:296.6},{type:"A5",width:210,height:147.6},{type:"B3",width:500,height:352.6},{type:"B4",width:250,height:352.6},{type:"B5",width:250,height:175.6},{type:"自定义纸张",width:220,height:80}],t.templateTypes=[{name:"发货单",code:"DeliveryNote"},{name:"条码打印",code:"Barcode"}]},b459:function(e,t,i){},b889:function(e,t,i){"use strict";i.r(t);var a=i("01eb"),n=i.n(a);for(var l in a)["default"].indexOf(l)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(l);t["default"]=n.a},ea2e:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return n}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[i("div",{staticStyle:{display:"flex",height:"100%"}},[i("el-aside",{staticClass:"cs-z-page-main-content",staticStyle:{background:"#FFF","margin-right":"16px",width:"20vw","min-width":"320px"}},[i("el-row",{staticStyle:{"flex-shrink":"0"},attrs:{gutter:4}},[i("el-col",{attrs:{span:17}},[i("el-input",{attrs:{placeholder:"请输入内容","suffix-icon":"el-icon-search"},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1),i("el-col",{attrs:{span:7}},[i("el-button",{attrs:{type:"primary"},on:{click:e.createTemplate}},[e._v("新建模板")])],1)],1),i("div",{staticClass:"tmpl-list"},[i("el-menu",{staticClass:"tmpl-menu",attrs:{"default-active":String(e.activeIndex)}},e._l(e.filteredTmplList,(function(t){return i("el-menu-item",{key:t.Id,staticStyle:{"padding-left":"12px"},attrs:{index:t.Id,title:t.Name}},[i("div",{staticStyle:{overflow:"hidden","max-width":"220px","text-overflow":"ellipsis"},on:{click:function(i){return i.stopPropagation(),e.tmplSelect(t.Id)}}},[i("i",{staticClass:"el-icon-document"}),e._v(e._s(t.Name)+" ")]),String(e.activeIndex)===t.Id?[i("el-link",{attrs:{underline:!1,type:"danger"},on:{click:function(i){return e.deleteTemplate(t.Id)}}},[i("i",{staticClass:"right-align-icon el-icon-delete"})]),i("el-link",{attrs:{underline:!1,type:"primary"},on:{click:function(i){return e.cloneTemplate(t.Id)}}},[i("i",{staticClass:"right-align-icon el-icon-copy-document"})])]:e._e()],2)})),1)],1),i("div",{staticClass:"flex-row justify-center flex-wrap",staticStyle:{display:"flex","flex-wrap":"wrap"}},[i("div",{staticClass:"title"},[e._v("标题区")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Logo"}},[e._v(" Logo图片 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.QrcodeText"}},[e._v(" 二维码 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Code"}},[e._v(" 单据号 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.SendDate"}},[e._v(" 日期 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Number"}},[e._v(" 发货序号 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Address"}},[e._v(" 项目地址 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.ProjectName"}},[e._v(" 项目名称 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.MakerName"}},[e._v(" 出库人 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Consignee"}},[e._v(" 收货人 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.ConsigneeTel"}},[e._v(" 联系电话 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.VehicleNo"}},[e._v(" 车牌 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Telephone"}},[e._v(" 司机电话 ")]),i("div",{staticClass:"title"},[e._v("数据区")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Table"}},[e._v(" 构件/包数据表 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Pound_Weight"}},[e._v(" 磅重（kg） ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Tare_Weight"}},[e._v(" 皮重（kg） ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.Net_Weight"}},[e._v(" 净重（kg） ")]),i("div",{staticClass:"title"},[e._v("其他")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.customText"}},[e._v(" 自定义文本 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.hline"}},[e._v(" 横线 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.vline"}},[e._v(" 竖线 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.rect"}},[e._v(" 矩形 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Shipment.oval"}},[e._v(" 椭圆 ")])])],1),i("el-container",{staticClass:"cs-z-page-main-content"},[i("div",{staticClass:"header"},[i("el-button",{attrs:{type:"primary",sizi:"mini"},on:{click:e.handlePrint}},[e._v("打印预览")]),i("el-button",{attrs:{type:"success",sizi:"mini"},on:{click:e.saveTemplate}},[e._v("保存模板")]),i("el-button",{attrs:{type:"danger",sizi:"mini"},on:{click:e.clearTemplate}},[e._v("清空")]),i("span",{staticClass:"label"},[e._v("模板名称")]),i("el-input",{staticStyle:{width:"150px"},attrs:{maxlength:50},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}}),i("span",{staticClass:"label"},[e._v("模板布局")]),i("el-select",{staticStyle:{width:"120px"},on:{change:e.changePaper},model:{value:e.curPaperType,callback:function(t){e.curPaperType=t},expression:"curPaperType"}},e._l(e.paperTypes,(function(e){return i("el-option",{key:e.type,attrs:{value:e.type,label:e.type}})})),1),"自定义纸张"===e.curPaperType?i("div",[i("span",{staticClass:"label"},[e._v("宽")]),i("el-input",{staticClass:"input",attrs:{type:"input"},on:{change:e.changePaper},model:{value:e.paperWidth,callback:function(t){e.paperWidth=t},expression:"paperWidth"}}),i("span",{staticClass:"label"},[e._v("高")]),i("el-input",{staticClass:"input",attrs:{type:"input"},on:{change:e.changePaper},model:{value:e.paperHeight,callback:function(t){e.paperHeight=t},expression:"paperHeight"}})],1):e._e(),i("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[i("i",{staticClass:"el-icon-zoom-out zoom-btn",on:{click:function(t){return e.changeScale(!1)}}}),i("div",{staticClass:"zoom"},[e._v(e._s(~~(100*e.scaleValue))+"%")]),i("i",{staticClass:"el-icon-zoom-in zoom-btn",on:{click:function(t){return e.changeScale(!0)}}})])],1),i("div",{staticStyle:{"margin-top":"10px",display:"flex"}},[i("div",{staticStyle:{flex:"1","padding-left":"16px","padding-top":"16px",overflow:"auto"}},[i("div",{attrs:{id:"hiprint-printTemplate"}})]),i("div",{staticClass:"hinnn-layout-sider",staticStyle:{width:"20vw","min-width":"300px","margin-left":"16px"}},[i("div",{attrs:{id:"PrintElementOptionSetting"}})])])])],1)])},n=[]}}]);