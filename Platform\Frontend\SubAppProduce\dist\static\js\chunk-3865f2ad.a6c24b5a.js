(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-3865f2ad"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=o(),l=e-i,s=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=s;var e=Math.easeInOutQuad(u,i,l,t);r(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var n=a("6186"),r=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(o){(0,n.GetGridByCode)({code:e,IsAll:a}).then((function(e){var n=e.IsSucceed,i=e.Data,l=e.Message;if(n){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var s=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),s=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=s.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||r.tablePageSize[0]),o(t.columns)}else t.$message({message:l,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,n=e.type;this.queryInfo.Page="limit"===n?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var n=0;n<this.columns.length;n++){var r=this.columns[n];if(r.Code===t){a.Type=r.Type,a.Filter_Type=r.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"2e91":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card h100"},[a("el-form",{ref:"form",attrs:{inline:"",model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Project_Id"}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.projectChange},model:{value:e.form.Project_Id,callback:function(t){e.$set(e.form,"Project_Id",t)},expression:"form.Project_Id"}},e._l(e.ProjectNameData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},on:{"select-clear":e.areaClear,"node-click":e.areaChange},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{attrs:{disabled:!e.form.Area_Id,clearable:"",placeholder:"请选择"},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"转移单号",prop:"Transfer_Code"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.Transfer_Code,callback:function(t){e.$set(e.form,"Transfer_Code",t)},expression:"form.Transfer_Code"}})],1),a("el-form-item",{attrs:{label:"转出班组",prop:"Source_Team_Id"}},[a("el-select",{staticClass:"w100",attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Source_Team_Id,callback:function(t){e.$set(e.form,"Source_Team_Id",t)},expression:"form.Source_Team_Id"}},e._l(e.groupOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"接收班组"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Target_Team_Id,callback:function(t){e.$set(e.form,"Target_Team_Id",t)},expression:"form.Target_Team_Id"}},e._l(e.teamOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"状态",prop:"Transfer_Status"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Transfer_Status,callback:function(t){e.$set(e.form,"Transfer_Status",t)},expression:"form.Transfer_Status"}},[a("el-option",{attrs:{label:"未接收",value:1}}),a("el-option",{attrs:{label:"部分接收",value:2}}),a("el-option",{attrs:{label:"已拒绝",value:3}}),a("el-option",{attrs:{label:"接收完成",value:4}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.search(1)}}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1),a("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary",loading:e.receiveLoading,disabled:!e.multipleSelection.length},on:{click:e.handleBathReceive}},[e._v("批量接收任务")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length},on:{click:e.printEvent}},[e._v("打印单据")])]},proxy:!0}])}),a("div",{staticClass:"tb-x"},[a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.tbData,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return["Create_Date"===t.Code||"Transfer_Time"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,align:t.Align,width:"auto",sortable:""},scopedSlots:e._u([{key:"default",fn:function(a){var n=a.row;return[e._v(" "+e._s(e._f("timeFormat")(n[t.Code],"{y}-{m}-{d}"))+" ")]}}],null,!0)}):"Transfer_Status"===t.Code?a("vxe-column",{key:t.Code,attrs:{field:t.Code,title:t.Display_Name,"min-width":t.Width,align:t.Align,width:"auto",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(1===a.Transfer_Status?"未接收":2===a.Transfer_Status?"部分接收":3===a.Transfer_Status?"已拒绝":4===a.Transfer_Status?"接收完成":a.Transfer_Status)+" ")]}}],null,!0)}):a("vxe-column",{key:t.Code,attrs:{field:t.Code,align:t.Align,width:"auto",title:t.Display_Name,sortable:"","min-width":t.Width}})]})),a("vxe-column",{attrs:{fixed:"right",title:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleView(n)}}},[e._v("查看")]),[1,2].includes(n.Transfer_Status)?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(n)}}},[e._v("接收")]):e._e()]}}])})],2)],1),a("div",{staticClass:"cs-bottom"},[a("Pagination",{attrs:{total:e.total,"page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.pageChange}})],1)],1)],1)},r=[]},"2f83":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("3166"),r=a("f2f6");t.default={data:function(){return{ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,"check-strictly":!1,filterable:!1,data:[],props:{children:"Children",label:"Label",value:"Id"}},SetupPositionData:[]}},mounted:function(){this.getProjectOption()},methods:{getProjectOption:function(){var e=this;(0,n.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getAreaList:function(){var e=this;(0,n.GeAreaTrees)({projectId:this.form.Project_Id}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},getInstall:function(){var e=this;(0,r.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.SetupPositionData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},projectChange:function(e){var t=this;this.form.Area_Id="",this.treeParamsArea.data=[],this.$nextTick((function(e){t.$refs.treeSelectArea.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",e&&this.getAreaList()},areaChange:function(e){this.SetupPositionData=[],this.form.InstallUnit_Id="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.InstallUnit_Id=""}}}},3166:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=c,t.GeAreaTrees=_,t.GetFileSync=G,t.GetInstallUnitIdNameList=v,t.GetNoBindProjectList=h,t.GetPartDeepenFileList=L,t.GetProjectAreaTreeList=I,t.GetProjectEntity=s,t.GetProjectList=l,t.GetProjectPageList=i,t.GetProjectTemplate=p,t.GetPushProjectPageList=b,t.GetSchedulingPartList=O,t.IsEnableProjectMonomer=d,t.SaveProject=u,t.UpdateProjectTemplateBase=P,t.UpdateProjectTemplateContacts=g,t.UpdateProjectTemplateContract=y,t.UpdateProjectTemplateOther=T;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function G(e){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"8a10":function(e,t,a){"use strict";a("c787")},a024:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=D,t.AddTechnology=s,t.AddWorkingProcess=l,t.DelLib=x,t.DeleteProcess=_,t.DeleteProcessFlow=I,t.DeleteTechnology=v,t.DeleteWorkingTeams=R,t.GetAllProcessList=f,t.GetCheckGroupList=w,t.GetChildComponentTypeList=A,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=k,t.GetFactoryWorkingTeam=g,t.GetGroupItemsList=b,t.GetLibList=i,t.GetLibListType=B,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=p,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=j,t.GetProcessListWithUserBase=U,t.GetProcessWorkingTeamBase=$,t.GetTeamListByUser=F,t.GetTeamProcessList=T,t.GetWorkingTeam=y,t.GetWorkingTeamBase=C,t.GetWorkingTeamInfo=S,t.GetWorkingTeams=L,t.GetWorkingTeamsPageList=O,t.SaveWorkingTeams=G,t.UpdateProcessTeam=P;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:o.default.stringify(e)})}function s(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:o.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:o.default.stringify(e)})}function c(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:o.default.stringify(e)})}function f(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:o.default.stringify(e)})}function m(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:o.default.stringify(e)})}function p(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:o.default.stringify(e)})}function g(){return(0,r.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function y(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:o.default.stringify(e)})}function T(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:o.default.stringify(e)})}function b(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:o.default.stringify(e)})}function I(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:o.default.stringify(e)})}function v(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:o.default.stringify(e)})}function _(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:o.default.stringify(e)})}function C(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:o.default.stringify(e)})}function j(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:o.default.stringify(e)})}function U(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function $(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a13b:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1"));a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("3ca3"),a("841c"),a("ddb0");var l=a("ed08"),s=a("7f9d"),u=a("a024"),c=n(a("15ac")),d=n(a("2082")),f=n(a("333d")),m=a("c685"),h=n(a("2f83")),p="\n        .is--print{\n          width:95% !important;\n          margin:10px auto !important;\n        }\n        ";t.default={components:{Pagination:f.default},mixins:[c.default,d.default,h.default],data:function(){return{tablePageSize:m.tablePageSize,addPageArray:[{path:this.$route.path+"/view",hidden:!0,component:function(){return Promise.all([a.e("chunk-a123ae64"),a.e("chunk-94f80966")]).then(a.bind(null,"985a"))},name:"PROTransferReceiveView",meta:{title:"查看"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return Promise.all([a.e("chunk-a123ae64"),a.e("chunk-f1668d52")]).then(a.bind(null,"dcda"))},name:"PROTransferReceiveDetail",meta:{title:"接收"}}],tbLoading:!1,receiveLoading:!1,columns:[],teamOptions:[],groupOption:[],tbData:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1,PageSize:m.tablePageSize[0]},total:0,form:{Transfer_Code:"",Source_Team_Id:"",Target_Team_Id:"",Transfer_Status:void 0,Project_Id:"",Area_Id:"",InstallUnit_Id:""},rules:{},search:function(){return{}}}},inject:["pageType"],computed:{isCom:function(){return"com"===this.pageType}},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.search=(0,l.debounce)(e.fetchData,800,!0),t.n=1,e.getTableConfig("PROComTransferReceive");case 1:return t.n=2,e.getProcessTeam();case 2:return t.n=3,e.getAllTeamOption();case 3:e.fetchData();case 4:return t.a(2)}}),t)})))()},methods:{fetchData:function(e){var t=this;this.tbLoading=!0,e&&(this.queryInfo.Page=e),(0,s.GetTransferHistory)((0,r.default)((0,r.default)((0,r.default)({},this.queryInfo),this.form),{},{Process_Type:this.isCom?2:1,Transfer_Model:2})).then((function(e){e.IsSucceed?(t.tbData=e.Data.Data,t.total=e.Data.TotalCount):t.$message({message:e.Message,type:"error"})})).finally((function(e){t.multipleSelection=[],t.tbLoading=!1}))},getAllTeamOption:function(){var e=this;(0,u.GetProcessWorkingTeamBase)({type:this.isCom?1:2}).then((function(t){if(t.IsSucceed){for(var a=JSON.parse(JSON.stringify(t.Data)),n=[],r=0;r<a.length;r++)-1==n.indexOf(a[r].Name)?n.push(a[r].Name):(a.splice(r,1),r--);e.groupOption=a}else e.$message({message:t.Message,type:"error"})}))},getProcessTeam:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,u.GetTeamListByUser)({type:e.isCom?1:2}).then((function(t){e.teamOptions=t.Data.map((function(e){return{value:e.Id,label:e.Name}})),e.teamOptions.length>0&&(e.form.Target_Team_Id=e.teamOptions[0].value)}));case 1:return t.a(2)}}),t)})))()},handleReset:function(){this.$refs["form"].resetFields(),this.search(1)},handleView:function(e){this.$router.push({name:"PROTransferReceiveView",query:{type:"view",pg_type:this.isCom?"com":"part",pg_redirect:this.isCom?"PROComTransferReceive":"PROPartTransferReceive",other:encodeURIComponent(JSON.stringify(e))}})},tbSelectChange:function(e){this.multipleSelection=e.records},handleDetail:function(e){this.$router.push({name:"PROTransferReceiveDetail",query:{type:"detail",pg_type:this.isCom?"com":"part",pg_redirect:this.isCom?"PROComTransferReceive":"PROPartTransferReceive",other:encodeURIComponent(JSON.stringify(e))}})},handleBathReceive:function(){var e=this;this.form.Target_Team_Id?(this.receiveLoading=!0,(0,s.BatchReceiveTransferTask)({transferCodes:this.multipleSelection.map((function(e){return e.Transfer_Code})).toString(),targetTeamId:this.form.Target_Team_Id}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"}),e.receiveLoading=!1}))):this.$message({message:"接收班组为空",type:"error"})},printEvent:function(){this.$refs.xTable.print({sheetName:"打印表格",style:p,columns:this.columns.map((function(e){return{field:e.Code}})),mode:"selected",beforePrintMethod:function(e){var t=e.content;return t}})}}}},af68:function(e,t,a){"use strict";a.r(t);var n=a("2e91"),r=a("fd66");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("8a10");var i=a("2877"),l=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"0a591eda",null);t["default"]=l.exports},c787:function(e,t,a){},f2f6:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AdjustPlanTime=s,t.CheckPlanTime=u,t.DeleteInstallUnit=m,t.GetCompletePercent=y,t.GetEntity=b,t.GetInstallUnitAllInfo=d,t.GetInstallUnitComponentPageList=g,t.GetInstallUnitDetailList=f,t.GetInstallUnitEntity=c,t.GetInstallUnitList=l,t.GetInstallUnitPageList=i,t.GetProjectInstallUnitList=T,t.ImportInstallUnit=p,t.InstallUnitInfoTemplate=h,t.SaveInstallUnit=P,t.SaveOhterSourceInstallUnit=I;var r=n(a("b775")),o=n(a("4328"));function i(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:e})}function u(e){return(0,r.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:e})}function c(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:e})}function d(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:e})}function f(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:e})}function m(e){return(0,r.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:e})}function h(e){return(0,r.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:e})}function p(e){return(0,r.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:o.default.stringify(e)})}function I(e){return(0,r.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:e})}},fd66:function(e,t,a){"use strict";a.r(t);var n=a("a13b"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a}}]);