(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-7c53422e"],{"088b":function(t,e,n){"use strict";n("93ab")},"09f4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=i,Math.easeInOutQuad=function(t,e,n,a){return t/=a/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(t,e,n){var i=r(),s=t-i,u=20,l=0;e="undefined"===typeof e?500:e;var c=function(){l+=u;var t=Math.easeInOutQuad(l,i,s,e);o(t),l<e?a(c):n&&"function"===typeof n&&n()};c()}},1119:function(t,e,n){},"1a3b":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("7db0"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("dca8"),n("b64b"),n("d3b7"),n("159b");var o=a(n("c14f")),r=a(n("1da1")),i=n("6186"),s=n("9643"),u=n("fd31"),l=a(n("2ea6"));e.default={components:{},mixins:[l.default],data:function(){return{warehouseType:"半成品仓库",tbConfig:{Pager_Align:"center"},columns:[],tbData:[],total:0,tbLoading:!1,btnLoading:!1,form:{Warehouse_Id:"",Location_Id:"",Remark:"",PageInfo:{Page:1,PageSize:20}},rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]},status:"",selectList:[],ProfessionalType:[],isIntegration:!1}},created:function(){},methods:{getTypeList:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){var n,a,r;return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,u.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:n=e.v,a=n.Data,n.IsSucceed?(t.typeOption=Object.freeze(a),t.typeOption.length>0&&(t.Proportion=a[0].Proportion,t.Unit=a[0].Unit,t.TypeId=null===(r=t.typeOption[0])||void 0===r?void 0:r.Id,t.getTableConfig("pro_products_return_detail"))):t.$message({message:n.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},getTableConfig:function(t){var e=this;(0,i.GetGridByCode)({code:t+","+this.typeOption.find((function(t){return t.Id===e.TypeId})).Code}).then((function(t){var n=t.IsSucceed,a=t.Data,o=t.Message;if(n){if(!a)return e.$message.error("当前专业没有配置相对应表格"),void(e.tbLoading=!0);e.tbLoading=!1;var r=a.ColumnList||[];e.columns=r.filter((function(t){return t.Is_Display})).map((function(t){return"refuse"===e.status&&"Operation_Count"===t.Code&&(t.Display_Name="拒绝退货数量"),t}))}else e.$message({message:o,type:"error"})}))},init:function(t,e,n){var a=this;return(0,r.default)((0,o.default)().m((function r(){return(0,o.default)().w((function(o){while(1)switch(o.n){case 0:return a.tbLoading=!0,t&&t.map((function(t){t.Return_Count=t.Wait_Return_Count})),a.tbData=JSON.parse(JSON.stringify(t)),a.tbData.map((function(t){return t.Operation_Count=t.Wait_Return_Count,t})),a.total=a.tbData.length,a.status=e,a.isIntegration=n||!1,o.n=1,a.getTypeList();case 1:a.tbLoading=!1;case 2:return o.a(2)}}),r)})))()},activeCellMethod:function(t){t.row;var e=t.column;t.columnIndex;return"Operation_Count"===e.field},handleSubmit:function(){"refuse"===this.status?this.submitReturnToStockIn():this.warehouseLocationForm()},warehouseLocationForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&t.submitReturnToStockIn()}))},submitReturnToStockIn:function(){var t=this,e=!0;if(this.tbData.forEach((function(t){t.Operation_Count||(e=!1)})),e){this.btnLoading=!0;var n={Location_Id:this.form.Location_Id,Warehouse_Id:this.form.Warehouse_Id,Remark:this.form.Remark,ReturnList:this.tbData||[],Return_Type:"refuse"===this.status?2:1};(0,s.SubmitReturnToStockIn)(n).then((function(e){e.IsSucceed?(t.btnLoading=!1,t.$message({message:"操作成功",type:"success"}),t.$emit("close"),t.$emit("refresh")):(t.btnLoading=!1,t.$message({message:e.Message,type:"error"}))}))}else"refuse"===this.status?this.$message({message:"请填写拒绝退货数量",type:"error"}):this.$message({message:"请填写确认退货数量",type:"error"})},multiSelectedChange:function(t){this.selectList=t},handleClose:function(){this.$emit("close")},toggleSelection:function(t){var e=this;this.$nextTick((function(n){e.tbData.forEach((function(n){t.find((function(t){return t.Id===n.Id}))&&e.$refs.dyTable.$refs.dtable.toggleRowSelection(n)}))}))},getPageList:function(){},resetForm:function(t){this.$refs[t].resetFields()}}}},"1e71":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("7db0"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("a9e3"),n("dca8"),n("d3b7"),n("3ca3"),n("c7cd"),n("159b"),n("ddb0");var o=a(n("5530")),r=a(n("c14f")),i=a(n("1da1")),s=n("6186"),u=a(n("333d")),l=n("9643"),c=n("fd31"),d=n("8975"),f=n("c685");e.default={components:{Pagination:u.default},props:{searchDetail:{type:Object,default:function(){}},isIntegration:{type:Boolean,default:!1}},data:function(){return{tablePageSize:f.tablePageSize,Unit:"",TypeId:"",Proportion:0,typeOption:"",Date_Time:[],searchDate:{StartTime:"",EndTime:""},columns:[],tbData:[],dialog:!1,tbLoading:!0,total:0,queryInfo:{Page:1,PageSize:20},pickerOptions:{shortcuts:[{text:"今天",onClick:function(t){var e=new Date,n=new Date;t.$emit("pick",[n,e])}},{text:"最近一周",onClick:function(t){var e=new Date,n=new Date;n.setTime(n.getTime()-6048e5),t.$emit("pick",[n,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,n=new Date;n.setTime(n.getTime()-2592e6),t.$emit("pick",[n,e])}}]},KC_Count:0,KC_Weith_Count:0,Select_KC_Count:0,Select_KC_Weith_Count:0,selectList:[]}},mounted:function(){this.getTypeList()},methods:{getTypeList:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){var n,a,o;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,c.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:n=e.v,a=n.Data,n.IsSucceed?(t.typeOption=Object.freeze(a),t.typeOption.length>0&&(t.Proportion=a[0].Proportion,t.Unit=a[0].Unit,t.TypeId=null===(o=t.typeOption[0])||void 0===o?void 0:o.Id,t.getTableConfig("pro_products_return_history"),t.fetchList())):t.$message({message:n.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},getTableConfig:function(t){var e=this;(0,s.GetGridByCode)({code:t+","+this.typeOption.find((function(t){return t.Id===e.TypeId})).Code}).then((function(t){var n=t.IsSucceed,a=t.Data,o=t.Message;if(n){if(!a)return e.$message.error("当前专业没有配置相对应表格"),void(e.tbLoading=!0);e.tbLoading=!1;var r=a.ColumnList||[];e.columns=r.filter((function(t){return t.Is_Display})).map((function(t){return"Code"===t.Code&&(t.fixed="left"),e.isIntegration||("Total_Count"===t.Code&&(t.Display_Name="发货数量"),"Refused_Count"===t.Code&&(t.visible=!1)),t}))}else e.$message({message:o,type:"error"})}))},changePage:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:t.tbLoading=!0,("number"!==typeof t.queryInfo.PageSize||t.queryInfo.PageSize<1)&&(t.queryInfo.PageSize=10),Promise.all([t.fetchList()]).then((function(e){t.tbLoading=!1}));case 1:return e.a(2)}}),e)})))()},fetchList:function(){var t=this;(0,l.GetReturnHistoryPageList)((0,o.default)((0,o.default)({},this.searchDetail),this.queryInfo)).then((function(e){t.tbData=e.Data.Data.map((function(t){return t.Is_Component=t.Is_Component?"否":"是",t.Is_Direct=t.Is_Component?"否":"是",t.Last_Operation_Date=(0,d.timeFormat)(t.Last_Operation_Date,"{y}-{m}-{d} {h}:{i}:{s}"),t})),t.total=e.Data.TotalCount}))},tbSelectChange:function(t){var e=this;this.selectList=t.records,this.Select_KC_Count=0,this.Select_KC_Weith_Count=0;var n=0;this.selectList.length>0&&(this.selectList.forEach((function(t){e.Select_KC_Count+=t.Stock_Count,n+=Number(t.ToWeight)})),this.Select_KC_Weith_Count=Math.round(n/this.Proportion*1e3)/1e3)},changesearchDate:function(t){t?(this.searchDate.StartTime=t[0],this.searchDate.EndTime=t[1]):(this.searchDate.StartTime="",this.searchDate.EndTime=""),this.fetchList()}}}},"209b":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CheckPackCode=c,e.ExportComponentStockInInfo=m,e.ExportPackingInInfo=p,e.ExportWaitingStockIn2ndList=L,e.FinishCollect=_,e.From_Stock_Status_TYPES=void 0,e.GetComponentStockInEntity=u,e.GetLocationList=i,e.GetPackingDetailList=f,e.GetPackingEntity=v,e.GetPackingGroupByDirectDetailList=d,e.GetStockInDetailList=l,e.GetStockMoveDetailList=b,e.GetWarehouseListOfCurFactory=r,e.HandleInventoryItem=C,e.PackingTypes=e.PackingStatus=e.InventoryComponentTypes=e.InventoryCheckStatus=e.InventoryCheckExceptions=void 0,e.RemoveMain=h,e.SaveComponentScrap=y,e.SaveInventory=S,e.SavePacking=g,e.SaveStockIn=s,e.SaveStockMove=I,e.StockInTypes=void 0,e.UnzipPacking=P,e.UpdateBillReady=O,e.UpdateMaterialReady=k;var o=a(n("b775"));a(n("4328")),e.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],e.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],e.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],e.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],e.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],e.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],e.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function r(t){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function i(t){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:t}})}function l(t,e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:t,isEdit:e}})}function c(t,e){return(0,o.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:t,id:e}})}function d(t){return(0,o.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:t}})}function p(t){return(0,o.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:t}})}function h(t){return(0,o.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:t}})}function g(t){return(0,o.default)({url:"/PRO/Packing/SavePacking",method:"post",data:t})}function P(t){var e=t.id,n=t.locationId;return(0,o.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:e,locationId:n}})}function v(t){var e=t.id,n=t.code;return(0,o.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:e,code:n}})}function b(t){return(0,o.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:t}})}function I(t){return(0,o.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:t})}function C(t){var e=t.id,n=t.type,a=t.value;return(0,o.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:e,type:n,value:a}})}function y(t){return(0,o.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:t}})}function O(t){var e=t.installId,n=t.isReady;return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:e,isReady:n}})}function k(t){return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:t})}function L(t){return(0,o.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:t})}},2645:function(t,e,n){"use strict";n.r(e);var a=n("daa3"),o=n("35434");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("b1ec");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"ca8f37f8",null);e["default"]=s.exports},"2ea6":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n("209b");e.default={data:function(){return{warehouses:[],locations:[]}},mounted:function(){this.getWarehouseList()},methods:{getWarehouseList:function(){var t=this;(0,a.GetWarehouseListOfCurFactory)({type:this.warehouseType?this.warehouseType:"成品仓库"}).then((function(e){e.IsSucceed&&(t.warehouses=e.Data)}))},wareChange:function(t){var e=this;this.form.Location_Id="",this.$nextTick((function(){e.form.Warehouse_Name=e.$refs["WarehouseRef"].selected.currentLabel})),(0,a.GetLocationList)({Warehouse_Id:t}).then((function(t){t.IsSucceed&&(e.locations=t.Data)}))},locationChange:function(t){var e=this;this.$nextTick((function(){e.form.Location_Name=e.$refs["LocationRef"].selected.currentLabel}))}}}},3166:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=m,e.DeleteProject=c,e.GeAreaTrees=y,e.GetFileSync=k,e.GetInstallUnitIdNameList=C,e.GetNoBindProjectList=p,e.GetPartDeepenFileList=_,e.GetProjectAreaTreeList=S,e.GetProjectEntity=u,e.GetProjectList=s,e.GetProjectPageList=i,e.GetProjectTemplate=h,e.GetPushProjectPageList=I,e.GetSchedulingPartList=O,e.IsEnableProjectMonomer=d,e.SaveProject=l,e.UpdateProjectTemplateBase=g,e.UpdateProjectTemplateContacts=P,e.UpdateProjectTemplateContract=v,e.UpdateProjectTemplateOther=b;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:r.default.stringify(t)})}function l(t){return(0,o.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/Project/DeleteProject",method:"post",data:r.default.stringify(t)})}function d(t){return(0,o.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function k(t){return(0,o.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"326e":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"min-height":"500px",display:"flex","flex-direction":"column"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:"refuse"!=t.status,expression:"status!='refuse'"}]},[n("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"退货仓库",prop:"Warehouse_Id"}},[n("el-select",{ref:"WarehouseRef",staticStyle:{width:"100%"},attrs:{placeholder:"请选择仓库",filterable:"",clearable:""},on:{change:t.wareChange},model:{value:t.form.Warehouse_Id,callback:function(e){t.$set(t.form,"Warehouse_Id",e)},expression:"form.Warehouse_Id"}},t._l(t.warehouses,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:"退货库位",prop:"Location_Id"}},[n("el-select",{ref:"LocationRef",staticStyle:{width:"100%"},attrs:{disabled:!t.form.Warehouse_Id,placeholder:"请选择库位",filterable:"",clearable:""},model:{value:t.form.Location_Id,callback:function(e){t.$set(t.form,"Location_Id",e)},expression:"form.Location_Id"}},t._l(t.locations,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1)],1),n("el-col",{attrs:{span:8}})],1)],1)],1),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[n("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:t.tbLoading,align:"left",stripe:"",data:t.tbData,resizable:"","edit-config":{trigger:"click",mode:"cell",activeMethod:t.activeCellMethod},"tooltip-config":{showAll:!0,enterable:!0}}},[t._l(t.columns,(function(e){return["Operation_Count"===e.Code?n("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,"edit-render":{},"min-width":e.Width,align:e.Align,sortable:"",width:"200"},scopedSlots:t._u([{key:"edit",fn:function(e){var a=e.row;return[n("vxe-input",{attrs:{type:"integer",min:1,max:a.Wait_Return_Count,disabled:1===a.Wait_Return_Count||t.isIntegration},model:{value:a.Operation_Count,callback:function(e){t.$set(a,"Operation_Count",t._n(e))},expression:"row.Operation_Count"}})]}},{key:"default",fn:function(e){var a=e.row;return[n("div",[t._v(" "+t._s(t._f("displayValue")(a.Operation_Count)))])]}}],null,!0)}):n("vxe-column",{key:e.Id,attrs:{field:e.Code,title:e.Display_Name,"min-width":e.Width,align:e.Align,sortable:""},scopedSlots:t._u([{key:"default",fn:function(a){var o=a.row;return[n("div",[t._v(t._s(o[e.Code]||"-"))])]}}],null,!0)})]}))],2)],1),n("div",{staticClass:"dialog-footer"},[n("el-button",{on:{click:function(e){return t.$emit("close")}}},[t._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:t.btnLoading},on:{click:t.handleSubmit}},[t._v("确定")])],1)])},o=[]},35434:function(t,e,n){"use strict";n.r(e);var a=n("1e71"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"40fc":function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("ac1f"),n("5319"),n("5b81"),n("498a");var o=a(n("c14f")),r=a(n("1da1")),i=a(n("83b4")),s=a(n("a1a6")),u=a(n("2645"));e.default={components:{ReturnGoods:s.default,ReturnGoodsHistory:u.default},mixins:[i.default],data:function(){return{activeName:"退货列表",form:{InstallUnit_Id:"",SetupPosition:"",ProjectName:"",Project_Id:"",Sys_Project_Id:"",Area_Id:"",Codes:"",SearchCode:"",Component_Codes:"",Is_Direct:""},tbConfig:{},columns:[],tbData:[],Is_Integration:!1}},created:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getIntegrationStatus();case 1:return e.a(2)}}),e)})))()},mounted:function(){return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.a(2)}}),t)})))()},methods:{getIntegrationStatus:function(){var t=this;return(0,r.default)((0,o.default)().m((function e(){var n;return(0,o.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:n=e.v,t.Is_Integration=n;case 2:return e.a(2)}}),e)})))()},handleTap:function(t){},handleSearch:function(t){this.form.Component_Codes=this.form.SearchCode.trim().replaceAll(" ","\n"),t&&"reset"===t&&(this.form.ProjectName="",this.form.Sys_Project_Id=""),"退货列表"===this.activeName?this.$refs.returnGoodsRef.getTypeList():"退货记录"===this.activeName&&this.$refs.returnGoodsHistoryRef.getTypeList()}}}},"4e83":function(t,e,n){"use strict";n("938a")},5393:function(t,e,n){"use strict";n.r(e);var a=n("95cf2"),o=n("fcaa");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("4e83");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"22d59f18",null);e["default"]=s.exports},"83b4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("7d54"),n("d3b7"),n("159b");var a=n("3166"),o=n("f2f6");e.default={data:function(){return{ProjectNameData:[],treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},SetupPositionData:[]}},mounted:function(){this.getProjectOption()},methods:{getProjectOption:function(){var t=this;(0,a.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectNameData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},getAreaList:function(){var t=this,e=this.form.Project_Id?this.form.Project_Id:this.form.ProjectId;(0,a.GeAreaTrees)({projectId:e}).then((function(e){if(e.IsSucceed){var n=e.Data;t.setDisabledTree(n),t.treeParamsArea.data=n,t.$nextTick((function(e){var a;null===(a=t.$refs.treeSelectArea)||void 0===a||a.treeDataUpdateFun(n)}))}else t.$message({message:e.Message,type:"error"})}))},getInstall:function(){var t=this;(0,o.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(e){e.IsSucceed&&e.IsSucceed?t.SetupPositionData=e.Data.Data:t.$message({message:e.Message,type:"error"})}))},projectChangeSingle:function(t){var e,n=this;this.$nextTick((function(){n.form.ProjectName=n.$refs["ProjectName"].selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.getProjectEntity(t)},projectChange:function(t){var e,n=this;this.$nextTick((function(){var t;n.form.ProjectName=null===(t=n.$refs["ProjectName"])||void 0===t?void 0:t.selected.currentLabel})),this.form.Sys_Project_Id=null===(e=this.ProjectNameData.find((function(e){return e.Id===t})))||void 0===e?void 0:e.Sys_Project_Id,this.form.Area_Id="",this.form.AreaPosition="",this.treeParamsArea.data=[],this.$nextTick((function(t){var e;null===(e=n.$refs.treeSelectArea)||void 0===e||e.treeDataUpdateFun([])})),this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",t&&this.getAreaList()},areaChange:function(t){this.form.AreaPosition=t.Label,this.SetupPositionData=[],this.form.InstallUnit_Id="",this.form.SetupPosition="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.AreaPosition="",this.form.InstallUnit_Id="",this.form.SetupPosition=""},setupPositionChange:function(){var t=this;this.$nextTick((function(){t.form.SetupPosition=t.$refs["SetupPosition"].selected.currentLabel}))},setDisabledTree:function(t){var e=this;t&&t.forEach((function(t){var n=t.Children;n&&n.length?t.disabled=!0:(t.disabled=!1,e.setDisabledTree(n))}))},dateChange:function(t){},getProjectEntity:function(t){var e=this;(0,a.GetProjectEntity)({id:t}).then((function(t){if(t.IsSucceed){var n="",a=t.Data.Contacts;a.forEach((function(t){"Consignee"===t.Type&&(n=t.Name)})),e.consigneeName=n}else e.$message({message:t.Message,type:"error"})}))}}}},"84f2":function(t,e,n){"use strict";n.r(e);var a=n("1a3b"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"8dac":function(t,e,n){"use strict";n("1119")},"938a":function(t,e,n){},"93ab":function(t,e,n){},"94ef":function(t,e,n){"use strict";n.r(e);var a=n("b156"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a},"95cf2":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"container abs100"},[n("div",{ref:"searchDom",staticClass:"header_wrapper"},[n("el-tabs",{staticClass:"search-wrapper",on:{"tab-click":t.handleTap},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"退货列表",name:"退货列表"}}),n("el-tab-pane",{attrs:{label:"退货记录",name:"退货记录"}})],1),n("div",{staticClass:"search-wrapper"},[n("el-form",{ref:"form",staticClass:"demo-form-inline form-search",staticStyle:{height:"auto"},attrs:{inline:!0,"label-width":"100px",model:t.form}},[n("el-form-item",{attrs:{label:"构件名称：",prop:"SearchCode"}},[n("el-input",{attrs:{type:"text",placeholder:"请输入（空格间隔筛选多个）"},model:{value:t.form.SearchCode,callback:function(e){t.$set(t.form,"SearchCode",e)},expression:"form.SearchCode"}})],1),n("el-form-item",{attrs:{label:"是否直发件：",prop:"Is_Direct"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.form.Is_Direct,callback:function(e){t.$set(t.form,"Is_Direct",e)},expression:"form.Is_Direct"}},[n("el-option",{attrs:{label:"是",value:!0}}),n("el-option",{attrs:{label:"否",value:!1}})],1)],1),n("el-form-item",{attrs:{label:"发货单号：",prop:"Codes"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请输入"},model:{value:t.form.Codes,callback:function(e){t.$set(t.form,"Codes",e)},expression:"form.Codes"}})],1),n("el-form-item",{attrs:{label:"项目名称：",prop:"Project_Id"}},[n("el-select",{ref:"ProjectName",attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:t.projectChange},model:{value:t.form.Project_Id,callback:function(e){t.$set(t.form,"Project_Id",e)},expression:"form.Project_Id"}},t._l(t.ProjectNameData,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Short_Name,value:t.Id}})})),1)],1),n("el-form-item",{attrs:{label:"区域：",prop:"Area_Id"}},[n("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!t.form.Project_Id,"select-params":{clearable:!0},"tree-params":t.treeParamsArea},on:{"select-clear":t.areaClear,"node-click":t.areaChange},model:{value:t.form.Area_Id,callback:function(e){t.$set(t.form,"Area_Id",e)},expression:"form.Area_Id"}})],1),n("el-form-item",{attrs:{label:"批次：",prop:"InstallUnit_Id"}},[n("el-select",{ref:"SetupPosition",attrs:{disabled:!t.form.Area_Id,clearable:"",placeholder:"请选择"},on:{change:t.setupPositionChange},model:{value:t.form.InstallUnit_Id,callback:function(e){t.$set(t.form,"InstallUnit_Id",e)},expression:"form.InstallUnit_Id"}},t._l(t.SetupPositionData,(function(t){return n("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),n("el-button",{on:{click:function(e){t.$refs["form"].resetFields(),t.handleSearch("reset")}}},[t._v("重置")])],1)],1)],1)],1),n("div",{staticClass:"main-wrapper",staticStyle:{flex:"1"}},["退货列表"==t.activeName?n("ReturnGoods",{ref:"returnGoodsRef",attrs:{"search-detail":t.form,"is-integration":t.Is_Integration}}):n("ReturnGoodsHistory",{ref:"returnGoodsHistoryRef",attrs:{"search-detail":t.form,"is-integration":t.Is_Integration}})],1)])},o=[]},9643:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProjectSendingInfo=C,e.CancelFlow=q,e.DeleteProjectSendingInfo=u,e.EditProjectSendingInfo=d,e.ExportComponentStockOutInfo=k,e.ExportInvoiceList=M,e.ExportSendSteel=f,e.ExportSendingDetailInfoList=m,e.GetLocationList=T,e.GetProduceCompentEntity=b,e.GetProducedPartToSendPageList=U,e.GetProjectAcceptInfoPagelist=E,e.GetProjectSendingAllCount=g,e.GetProjectSendingInfoAndItemPagelist=R,e.GetProjectSendingInfoLogPagelist=x,e.GetProjectSendingInfoPagelist=s,e.GetProjectsendinginEntity=c,e.GetReadyForDeliverSummary=L,e.GetReadyForDeliveryComponentPageList=O,e.GetReadyForDeliveryPageList=_,e.GetReturnHistoryPageList=W,e.GetSendToReturnPageList=D,e.GetStockOutBillInfoPageList=w,e.GetStockOutDetailList=P,e.GetStockOutDetailPageList=j,e.GetStockOutDocEntity=y,e.GetStockOutDocPageList=i,e.GetWaitingStockOutPageList=v,e.GetWarehouseListOfCurFactory=G,e.GetWeighingReviewList=N,e.SaveStockOut=S,e.SubmitApproval=B,e.SubmitProjectSending=l,e.SubmitReturnToStockIn=I,e.SubmitWeighingForPC=z,e.Transforms=p,e.TransformsByType=$,e.TransformsWithoutWeight=h,e.WeighingReviewSubmit=A,e.WithdrawDraft=F;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDocPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoPagelist",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ComponentStockOut/DeleteProjectSendingInfo",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitProjectSending",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectsendinginEntity",method:"post",data:t})}function d(t){return(0,o.default)({url:"/PRO/ComponentStockOut/EditProjectSendingInfo",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportSendSteel",method:"post",data:t})}function m(t){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportSendingDetailInfoList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ComponentStockOut/Transforms",method:"post",data:t})}function h(t){return(0,o.default)({url:"/PRO/ComponentStockOut/TransformsWithoutWeight",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingAllCount",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailList",method:"post",data:r.default.stringify(t)})}function v(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetWaitingStockOutPageList",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProduceCompentEntity",method:"post",data:t})}function I(t){return(0,o.default)({url:"/PRO/ComponentReturn/SubmitReturnToStockIn",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/ComponentStockOut/SaveStockOut",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/ComponentStockOut/AddProjectSendingInfo",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDocEntity",method:"post",data:r.default.stringify(t)})}function _(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryPageList",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryComponentPageList",method:"post",data:t})}function k(t){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportComponentStockOutInfo",method:"post",data:r.default.stringify(t)})}function L(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliverSummary",method:"post",data:r.default.stringify(t)})}function R(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoAndItemPagelist",method:"post",data:t})}function D(t){return(0,o.default)({url:"/PRO/ComponentReturn/GetSendToReturnPageList",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailPageList",method:"post",data:t})}function w(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetStockOutBillInfoPageList",method:"post",data:t})}function G(t){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function T(t){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function x(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoLogPagelist",method:"post",data:t})}function U(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProducedPartToSendPageList",method:"post",data:t})}function $(t){return(0,o.default)({url:"/PRO/ComponentStockOut/TransformsByType",method:"post",data:t})}function E(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetProjectAcceptInfoPagelist",method:"post",data:t})}function W(t){return(0,o.default)({url:"/PRO/ComponentReturn/GetReturnHistoryPageList",method:"post",data:t})}function N(t){return(0,o.default)({url:"/PRO/ComponentStockOut/GetWeighingReviewList",method:"post",data:t})}function A(t){return(0,o.default)({url:"/PRO/ComponentStockOut/WeighingReviewSubmit",method:"post",data:t})}function z(t){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitWeighingForPC",method:"post",data:t})}function F(t){return(0,o.default)({url:"/PRO/ComponentStockOut/WithdrawDraft",method:"post",data:t})}function M(t){return(0,o.default)({url:"/PRO/ComponentStockOut/ExportInvoiceList",method:"post",data:t})}function B(t){return(0,o.default)({url:"/PRO/ComponentStockOut/SubmitApproval",method:"post",data:t})}function q(t){return(0,o.default)({url:"/Sys/FlowInstances/CancelFlow",method:"post",data:t})}},"9a72":function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"assistant-box"},[n("div",{staticClass:"assistant-wrapper"},[n("div",{staticClass:"btn-wrapper"},[t.isIntegration?[n("el-button",{attrs:{size:"small",type:"primary",disabled:t.selectList.length<=0},on:{click:function(e){return t.handleReturn("confirm")}}},[t._v("确认退货")]),n("el-button",{attrs:{size:"small",type:"warning",disabled:t.selectList.length<=0},on:{click:function(e){return t.handleReturn("refuse")}}},[t._v("拒绝退货")])]:[n("el-button",{attrs:{size:"small",type:"primary",disabled:t.selectList.length<=0},on:{click:t.handleReturn}},[t._v("成品退货")])]],2),n("div",{staticClass:"total-wrapper"})]),n("div",{staticClass:"table-wrapper"},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",data:t.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),t._l(t.columns,(function(e,a){return n("vxe-column",{key:a,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,field:e.Code,title:e.Display_Name,"min-width":e.minWidth,width:e.Width,visible:e.visible},scopedSlots:t._u(["Request_Date"===e.Code?{key:"default",fn:function(n){var a=n.row;return[t._v(" "+t._s(t._f("timeFormat")(a[e.Code],"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}:null],null,!0)})}))],2)],1),n("div",{staticClass:"page-total"},[n("div",{staticStyle:{display:"inline-block"}},[n("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[t._v("已选"+t._s(t.selectList.length)+"条数据")])],1),n("Pagination",{staticClass:"cs-table-pagination",staticStyle:{display:"inline-block",float:"right"},attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.changePage}})],1),t.dialogVisible?n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:t.title,visible:t.dialogVisible,width:t.width},on:{"update:visible":function(e){t.dialogVisible=e},close:t.close}},[n(t.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":t.dialogVisible},on:{close:t.close,refresh:t.fetchList}})],1):t._e()],1)},o=[]},a1a6:function(t,e,n){"use strict";n.r(e);var a=n("9a72"),o=n("94ef");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("8dac");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"10e2a7b8",null);e["default"]=s.exports},a491:function(t,e,n){},b156:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("4de4"),n("7db0"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("ab43"),n("a9e3"),n("dca8"),n("d3b7"),n("3ca3"),n("c7cd"),n("159b"),n("ddb0");var o=a(n("5530")),r=a(n("c14f")),i=a(n("1da1")),s=n("6186"),u=a(n("333d")),l=n("9643"),c=n("fd31"),d=n("8975"),f=n("c685"),m=a(n("db918"));e.default={components:{Pagination:u.default,BackDialog:m.default},props:{searchDetail:{type:Object,default:function(){}},isIntegration:{type:Boolean,default:!1}},data:function(){return{dialogVisible:!1,currentComponent:"BackDialog",title:"确认退货",tablePageSize:f.tablePageSize,Unit:"",TypeId:"",Proportion:0,typeOption:"",Date_Time:[],searchDate:{StartTime:"",EndTime:""},columns:[],tbData:[],tbLoading:!0,total:0,queryInfo:{Page:1,PageSize:20},pickerOptions:{shortcuts:[{text:"今天",onClick:function(t){var e=new Date,n=new Date;t.$emit("pick",[n,e])}},{text:"最近一周",onClick:function(t){var e=new Date,n=new Date;n.setTime(n.getTime()-6048e5),t.$emit("pick",[n,e])}},{text:"最近一个月",onClick:function(t){var e=new Date,n=new Date;n.setTime(n.getTime()-2592e6),t.$emit("pick",[n,e])}}]},KC_Count:0,KC_Weith_Count:0,Select_KC_Count:0,Select_KC_Weith_Count:0,selectList:[]}},mounted:function(){this.getTypeList()},methods:{getTypeList:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){var n,a,o;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,c.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:n=e.v,a=n.Data,n.IsSucceed?(t.typeOption=Object.freeze(a),t.typeOption.length>0&&(t.Proportion=a[0].Proportion,t.Unit=a[0].Unit,t.TypeId=null===(o=t.typeOption[0])||void 0===o?void 0:o.Id,t.getTableConfig("pro_products_return_list"),t.fetchList())):t.$message({message:n.Message,type:"error"});case 2:return e.a(2)}}),e)})))()},getTableConfig:function(t){var e=this;(0,s.GetGridByCode)({code:t+","+this.typeOption.find((function(t){return t.Id===e.TypeId})).Code}).then((function(t){var n=t.IsSucceed,a=t.Data,o=t.Message;if(n){if(!a)return e.$message.error("当前专业没有配置相对应表格"),void(e.tbLoading=!0);e.tbLoading=!1;var r=a.ColumnList||[];e.columns=r.filter((function(t){return t.Is_Display})).map((function(t){return"Code"===t.Code&&(t.fixed="left"),e.isIntegration||("Total_Count"===t.Code&&(t.Display_Name="已发货数量"),"Refused_Count"!==t.Code&&"Request_Date"!==t.Code||(t.visible=!1)),t}))}else e.$message({message:o,type:"error"})}))},changePage:function(){var t=this;return(0,i.default)((0,r.default)().m((function e(){return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:t.tbLoading=!0,("number"!==typeof t.queryInfo.PageSize||t.queryInfo.PageSize<1)&&(t.queryInfo.PageSize=20),Promise.all([t.fetchList()]).then((function(e){t.tbLoading=!1}));case 1:return e.a(2)}}),e)})))()},fetchList:function(){var t=this;(0,l.GetSendToReturnPageList)((0,o.default)((0,o.default)({},this.searchDetail),this.queryInfo)).then((function(e){t.tbData=e.Data.Data.map((function(t){return t.Is_Direct=t.Is_Component?"否":"是",t.In_Date=(0,d.timeFormat)(t.In_Date,"{y}-{m}-{d} {h}:{i}:{s}"),t})),t.total=e.Data.TotalCount}))},tbSelectChange:function(t){var e=this;this.selectList=t.records,this.Select_KC_Count=0,this.Select_KC_Weith_Count=0;var n=0;this.selectList.length>0&&(this.selectList.forEach((function(t){e.Select_KC_Count+=t.Stock_Count,n+=Number(t.ToWeight)})),this.Select_KC_Weith_Count=Math.round(n/this.Proportion*1e3)/1e3)},changesearchDate:function(t){t?(this.searchDate.StartTime=t[0],this.searchDate.EndTime=t[1]):(this.searchDate.StartTime="",this.searchDate.EndTime=""),this.fetchList()},handleReturn:function(t){var e=this,n="";t&&"refuse"===t&&(n="refuse"),this.currentComponent="BackDialog",this.width="65%",this.dialogVisible=!0,this.title="confirm"===t?"确认退货":"refuse"===t?"拒绝退货":"成品退货",this.$nextTick((function(t){e.$refs.content.init(e.selectList,n,e.isIntegration)}))},close:function(){this.dialogVisible=!1}}}},b1ec:function(t,e,n){"use strict";n("a491")},daa3:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return o}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"assistant-box"},[n("div",{staticClass:"table-wrapper"},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",data:t.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),t._l(t.columns,(function(e,a){return n("vxe-column",{key:a,attrs:{fixed:e.Is_Frozen?e.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:e.Align,field:e.Code,title:e.Display_Name,"min-width":e.minWidth,width:e.Width,visible:e.visible},scopedSlots:t._u(["Is_Direct"===e.Code?{key:"default",fn:function(e){var a=e.row;return["是"===a.Is_Direct?n("el-tag",{attrs:{type:"success"}},[t._v("是")]):n("el-tag",{attrs:{type:"danger"}},[t._v("否")])]}}:null],null,!0)})}))],2)],1),n("div",{staticClass:"page-total"},[n("div",{staticStyle:{display:"inline-block"}},[n("el-tag",{staticStyle:{padding:"0 24px"},attrs:{size:"medium"}},[t._v("已选"+t._s(t.selectList.length)+"条数据")])],1),n("Pagination",{staticClass:"cs-table-pagination",staticStyle:{display:"inline-block",float:"right"},attrs:{total:t.total,"max-height":"100%","page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.changePage}})],1)])},o=[]},db918:function(t,e,n){"use strict";n.r(e);var a=n("326e"),o=n("84f2");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("088b");var i=n("2877"),s=Object(i["a"])(o["default"],a["a"],a["b"],!1,null,"08f54590",null);e["default"]=s.exports},dca8:function(t,e,n){"use strict";var a=n("23e7"),o=n("bb2f"),r=n("d039"),i=n("861d"),s=n("f183").onFreeze,u=Object.freeze,l=r((function(){u(1)}));a({target:"Object",stat:!0,forced:l,sham:!o},{freeze:function(t){return u&&i(t)?u(s(t)):t}})},f2f6:function(t,e,n){"use strict";var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AdjustPlanTime=u,e.CheckPlanTime=l,e.DeleteInstallUnit=m,e.GetCompletePercent=v,e.GetEntity=I,e.GetInstallUnitAllInfo=d,e.GetInstallUnitComponentPageList=P,e.GetInstallUnitDetailList=f,e.GetInstallUnitEntity=c,e.GetInstallUnitList=s,e.GetInstallUnitPageList=i,e.GetProjectInstallUnitList=b,e.ImportInstallUnit=h,e.InstallUnitInfoTemplate=p,e.SaveInstallUnit=g,e.SaveOhterSourceInstallUnit=S;var o=a(n("b775")),r=a(n("4328"));function i(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitPageList",method:"post",data:t})}function s(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitList",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/InstallUnit/AdjustPlanTime",method:"post",params:t})}function l(t){return(0,o.default)({url:"/PRO/InstallUnit/CheckPlanTime",method:"post",params:t})}function c(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitEntity",method:"post",params:t})}function d(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitAllInfo",method:"post",params:t})}function f(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitDetailList",method:"post",params:t})}function m(t){return(0,o.default)({url:"/PRO/InstallUnit/DeleteInstallUnit",method:"post",params:t})}function p(t){return(0,o.default)({url:"/PRO/InstallUnit/InstallUnitInfoTemplate",method:"post",params:t})}function h(t){return(0,o.default)({url:"/PRO/InstallUnit/ImportInstallUnit",method:"post",data:t})}function g(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveInstallUnit",method:"post",data:t})}function P(t){return(0,o.default)({url:"/PRO/InstallUnit/GetInstallUnitComponentPageList",method:"post",data:t})}function v(t){return(0,o.default)({url:"/PRO/InstallUnit/GetCompletePercent",method:"post",data:t})}function b(t){return(0,o.default)({url:"/PRO/InstallUnit/GetProjectInstallUnitList",method:"post",data:t})}function I(t){return(0,o.default)({url:"/plm/Plm_Projects/GetEntity",method:"post",data:r.default.stringify(t)})}function S(t){return(0,o.default)({url:"/PRO/InstallUnit/SaveOhterSourceInstallUnit",method:"post",data:t})}},fcaa:function(t,e,n){"use strict";n.r(e);var a=n("40fc"),o=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=o.a}}]);