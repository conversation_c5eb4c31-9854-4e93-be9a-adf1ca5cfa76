(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-c012ebd0"],{"388b":function(t,e,a){},"4f39":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.parseTime=r,e.timeFormat=o,a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("4d90"),a("5319");var n=i(a("53ca"));function r(t,e){if(0===arguments.length||!t)return null;var a,i=e||"{y}-{m}-{d} {h}:{i}:{s}";"object"===(0,n.default)(t)?a=t:("string"===typeof t&&(t=/^[0-9]+$/.test(t)?parseInt(t):t.replace(new RegExp(/-/gm),"/")),"number"===typeof t&&10===t.toString().length&&(t*=1e3),a=new Date(t));var r={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=i.replace(/{([ymdhisa])+}/g,(function(t,e){var a=r[e];return"a"===e?["日","一","二","三","四","五","六"][a]:a.toString().padStart(2,"0")}));return o}function o(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"{y}-{m}-{d}";if(!t)return"";if(-1!==t.indexOf("~")){var a=t.split("~"),i=r(new Date(a[0]),e)+" ~ "+r(new Date(a[1]),e);return i}return t&&t.length>0?r(new Date(t),e):void 0}},"777d":function(t,e,a){"use strict";a("388b")},9300:function(t,e,a){"use strict";a.r(e);var i=a("be44"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},be44:function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("14d9"),a("e9f5"),a("7d54"),a("d3b7"),a("159b");var n=i(a("c14f")),r=i(a("1da1")),o=i(a("ac03")),l=a("cf45"),c=a("4f39"),s=i(a("3502")),u=a("9a77"),d=a("b60f"),f=a("05e0");e.default={name:"OMAPurchaseFeeDetailInfo",components:{VTable:s.default},mixins:[o.default],data:function(){return{form:{StatisticalDate:"",SearchKey:"",ProjectStatus:"",FactoryId:""},projectOption:[],showExport:!1}},computed:{curTitle:function(){return"".concat((0,c.timeFormat)(this.form.StatisticalDate,"{y}年{m}月{d}日"),"项目合计")}},beforeCreate:function(){this.curModuleKey=u.curModuleKey},mounted:function(){var t=this;return(0,r.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.showExport=t.getRoles("OMAPurchaseDetailFeeExport"),t.form.FactoryId=t.factoryId,t.$route.query.d?t.form.StatisticalDate=t.$route.query.d:t.form.StatisticalDate=t.originDate,e.n=1,(0,l.getDictionary)("FeeType");case 1:return t.feeTypeOption=e.v,t.fetchData(),e.n=2,(0,l.getDictionary)("Engineering Status");case 2:t.projectOption=e.v;case 3:return e.a(2)}}),e)})))()},methods:{fetchData:function(){var t=this;this.checkDate()&&(this.loading=!0,(0,d.GetDDFeesDailyDetailList)(this.form).then((function(e){if(e.IsSucceed){t.tableData=(null===e||void 0===e?void 0:e.Data)||[];var a=t.setTotalData(t.tableData,t.generateColumn()),i=a.column;t.columns=i,t.$refs["tb"].setColumns(i)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.loading=!1})))},handleReset:function(){this.form.ProjectStatus="",this.form.SearchKey=""},generateColumn:function(){var t=180,e=[{title:"项目简称",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{colSpan:3},field:"ProjectAbbreviation",minWidth:f.ProjectAbbreviationW,title:this.curTitle}]}]},{title:"项目编号",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectNumber",minWidth:f.ProjectNumberW}]}]},{title:"项目状态",fixed:"left",params:{rowSpan:2},children:[{params:{none:"none"},children:[{params:{none:"none"},field:"ProjectStatus",minWidth:f.ProjectStatusW}]}]},{title:"项目辅材",children:[{title:"辅材合价(元)",children:[{minWidth:t,field:"Project_Aux_Cost",title:0,isTotal:!0}]}]},{title:"公共辅材",children:[{title:"辅材合价(元)",children:[{minWidth:t,field:"Public_Aux_Cost",title:0,isTotal:!0}]}]},{title:"代付费用",children:[]}];if(this.feeTypeOption.length){var a=[];this.feeTypeOption.forEach((function(e,i){var n={title:e.Display_Name+"(元)",children:[{minWidth:t,field:e.Value,title:0,isTotal:!0}]};a.push(n)})),a.push({title:"小计(元)",children:[{minWidth:t,field:"total",title:0,isTotal:!0}]}),e[e.length-1].children=a}return e}}}},cd6e:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap",staticStyle:{"min-width":"1000px"}},[a("div",{staticClass:"cs-z-page-main-content"},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:"",model:t.form,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"统计时间",prop:"StatisticalDate"}},[a("el-date-picker",{staticClass:"cs-date",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",size:"medium",type:"date","picker-options":t.pickerOptions},on:{change:t.fetchData},model:{value:t.form.StatisticalDate,callback:function(e){t.$set(t.form,"StatisticalDate",e)},expression:"form.StatisticalDate"}})],1),a("el-form-item",{attrs:{label:"项目搜索",prop:"SearchKey"}},[a("el-input",{attrs:{placeholder:"输入项目简称或项目编号"},model:{value:t.form.SearchKey,callback:function(e){t.$set(t.form,"SearchKey",e)},expression:"form.SearchKey"}})],1),a("el-form-item",{attrs:{label:"项目状态",prop:"ProjectStatus"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.form.ProjectStatus,callback:function(e){t.$set(t.form,"ProjectStatus",e)},expression:"form.ProjectStatus"}},t._l(t.projectOption,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("查询")]),a("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),a("el-divider"),a("div",{staticClass:"tb-info"},[a("label",[t._v("数据列表")]),a("div",{staticClass:"btn-x"},[t.showExport?a("el-button",{attrs:{disabled:t.loading||t.isEmpty},on:{click:function(e){return t.handleExport(t.curTitle)}}},[t._v("导出报表")]):t._e()],1)]),a("div",{staticClass:"tb-x"},[a("v-table",{ref:"tb",attrs:{loading:t.loading,total:t.total},on:{setEmpty:t.setEmpty,pageChange:t.changePage}})],1)],1)])},n=[]},cf45:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDictionary=n,a("d3b7");var i=a("6186");function n(t){return new Promise((function(e,a){(0,i.GetDictionaryDetailListByCode)({dictionaryCode:t}).then((function(t){t.IsSucceed&&e(t.Data)}))}))}},ee9f:function(t,e,a){"use strict";a.r(e);var i=a("cd6e"),n=a("9300");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("777d");var o=a("2877"),l=Object(o["a"])(n["default"],i["a"],i["b"],!1,null,"b66be0bc",null);e["default"]=l.exports}}]);