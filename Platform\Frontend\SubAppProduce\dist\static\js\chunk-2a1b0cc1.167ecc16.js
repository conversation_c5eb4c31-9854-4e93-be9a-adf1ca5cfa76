(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2a1b0cc1"],{"0c2d":function(e,t,a){},"0ce7":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),o=n(a("1da1"));a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var i=n(a("9b15")),s=a("5c96"),l=a("21c4"),u=a("6186"),c=function(){(0,u.SecurityToken)().then((function(e){sessionStorage.setItem("ossToken",JSON.stringify(e.Data))}))};c(),setInterval((function(){c()}),114e4);t.default={name:"OSSUpload",mixins:[s.Upload],props:{ossOnSuccess:{type:Function,default:Function},piecesize:{type:Number,default:2},httpRequest:{type:Function,default:function(e){var t=this,a=JSON.parse(sessionStorage.getItem("ossToken"));this.$nextTick((function(){var n,s=null!==(n=t.data)&&void 0!==n&&n.piecesize?1*t.data.piecesize:2,c=new i.default({region:"oss-"+a.regionId,secure:!0,accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken,bucket:a.bucket,refreshSTSToken:function(){var e=(0,o.default)((0,r.default)().m((function e(){var a;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.securityToken();case 1:return a=e.v,e.a(2,{accessKeyId:a.AccessKeyId,accessKeySecret:a.AccessKeySecret,stsToken:a.SecurityToken})}}),e)})));function a(){return e.apply(this,arguments)}return a}(),refreshSTSTokenInterval:9e5}),d=e.file,f=new Date;c.multipartUpload((0,l.getTenantId)()+"/"+f.getFullYear()+"/"+(1*f.getMonth()+1)+"/"+f.getDate()+"/"+f.getMinutes()+"_"+f.getSeconds()+"_"+f.getMilliseconds()+"/"+d.name,d,{progress:function(t,a){this.process=a,e.onProgress({percent:Math.floor(100*t)})},parallel:4,partSize:1048576*s,meta:{}}).then((function(t){if(200===t.res.statusCode){var a,n=t.res.requestUrls[0]&&t.res.requestUrls[0].split("?")[0];!1===(null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.callback)?e.onSuccess({Data:n+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name}):(0,u.GetOssUrl)({url:n}).then((function(t){e.onSuccess({Data:n+"*"+d.size+"*"+d.name.substr(d.name.lastIndexOf("."))+"*"+d.name,encryptionUrl:t.Data})}))}}),(function(t){e.onError(t)}))}))}}},data:function(){return{process:null}},watch:{process:function(e){this.$emit("getprocess",e)}},mounted:function(){},methods:{handleFn:function(e){},securityToken:function(){return new Promise((function(e,t){(0,u.SecurityToken)({}).then((function(t){e(t.Data)})).catch((function(e){t(e)}))}))}}}},"0e48":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("a15b"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("25f0"),a("159b");var r=n(a("5530")),o=n(a("c14f")),i=n(a("1da1")),s=n(a("5cc7")),l=n(a("0f97")),u=a("9643"),c=n(a("4b32")),d=a("3166"),f=a("f2f6"),m=a("fd31"),p=a("7de9");t.default={components:{DynamicDataTable:l.default,CheckInfo:c.default},mixins:[s.default],props:{projectId:{type:String,default:""},sysProjectId:{type:String,default:""},addRadio:{type:String,default:"pro_waiting_out_list"},isPack:{type:Boolean,default:!1}},data:function(){return{tbConfig:{Pager_Align:"center"},columns:[],tbData:[],total:0,addLoading:!1,tbLoading:!1,form:{Sys_Project_id:"",Area_Id:"",InstallUnit_Id:"",Code:"",Warehouse_Id:"",Location_Id:"",ComponentType:[],Is_Pack:!1,Model_Ids:[],PageInfo:{ParameterJson:[],Page:1,PageSize:20}},selectParams:{clearable:!0,placeholder:"请选择"},styles:{width:"100%"},treeParamsArea:{"check-strictly":!0,"expand-on-click-node":!1,"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},SetupPositionData:[],selectList:[],parentArray:[],factoryOption:[],locationName:"",locationOption:[],cmptTypes_1:[],cmptTypes_2:[],installOption:[],ProfessionalType:[],warehouses:[],locations:[],treeSelectParams:{placeholder:"请选择"},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data"}}}},created:function(){this.isPack||this.getAreaList(),this.getWarehouseListOfCurFactory(),this.form.Is_Pack=this.isPack,this.isPack,this.form.Sys_Project_id=this.sysProjectId},methods:{init:function(e,t){var a=this;return(0,i.default)((0,o.default)().m((function n(){return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return a.form.Model_Ids=e.map((function(e){return e.Model_Ids})).toString(),a.form.Component_Ids=e.map((function(e){return e.Id})).filter((function(e){return!!e})),a.parentArray=e,a.tbLoading=!0,a.originTbData=t,n.n=1,a.getFactoryTypeOption();case 1:a.fetchData();case 2:return n.a(2)}}),n)})))()},getFactoryTypeOption:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,m.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("".concat(e.addRadio,",").concat(e.ProfessionalType[0].Code));case 2:if(e.isPack){t.n=3;break}return t.n=3,e.getObjectTypeList(e.ProfessionalType[0].Code);case 3:return t.a(2)}}),t)})))()},fetchData:function(){var e=this,t=(0,r.default)({},this.form);delete t["ComponentType"],t.ComponentType=this.form.ComponentType.join(",");var a=(0,r.default)({},t);if(!this.isPack){var n=this.originTbData.map((function(e){return{Import_Detail_Id:e.Import_Detail_Id,Location_Id:e.Location_Id}}));a.SelectedList=n}(0,u.GetProduceCompentEntity)(a).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount,e.tbLoading=!1):e.$message({message:t.Message,type:"error"}),e.addLoading=!1}))},getObjectTypeList:function(e){var t=this;(0,p.GetCompTypeTree)({professional:e}).then((function(e){e.IsSucceed?(t.ObjectTypeList.data=e.Data,t.$nextTick((function(a){t.$refs.treeSelectObjectType.treeDataUpdateFun(e.Data)}))):t.$message({type:"error",message:e.Message})}))},removeTagFn:function(e,t){},_searchFun:function(e){this.$refs.treeSelectObjectType.filterFun(e)},getAreaList:function(){var e=this;(0,d.GeAreaTrees)({sysProjectId:this.sysProjectId}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(a){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},filterFun:function(e,t){this.$refs[t].filterFun(e)},areaChange:function(e){this.form.InstallUnit_Id="",this.getInstall()},areaClear:function(){this.form.Area_Id="",this.form.InstallUnit_Id=""},getInstall:function(){var e=this;(0,f.GetInstallUnitPageList)({Area_Id:this.form.Area_Id,Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&t.IsSucceed?e.SetupPositionData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getWarehouseListOfCurFactory:function(){var e=this;(0,u.GetWarehouseListOfCurFactory)().then((function(t){t.IsSucceed?e.warehouses=t.Data:e.$message({message:t.Message,type:"error"})}))},wareChange:function(e){this.getLocationList()},getLocationList:function(){var e=this;(0,u.GetLocationList)({Warehouse_Id:this.form.Warehouse_Id}).then((function(t){t.IsSucceed?e.locations=t.Data:e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){this.$emit("selectList",this.selectList),this.$emit("close"),this.$emit("reCount")},multiSelectedChange:function(e){this.selectList=e,this.selectList.forEach((function(e){e.S_Count=e.Stock_Count,e.Area_Name=e.Area_Name,e.Wait_Stock_Count=e.Stock_Count}))},handleInfo:function(e){this.$refs.info.handleOpen(e)},handleOpen:function(){},handleClose:function(){this.$emit("close")},toggleSelection:function(e){var t=this;this.$nextTick((function(a){t.tbData.forEach((function(a){e.find((function(e){return e.Id===a.Id}))&&t.$refs.dyTable.$refs.dtable.toggleRowSelection(a)}))}))},getPageList:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleAdd:function(){var e=this;this.addLoading=!0,this.$emit("selectList",this.selectList),this.$nextTick((function(t){var a=null;e.isPack?(a=e.$parent.$parent.tbData2,a=a.filter((function(e){return!e.isOld}))):(a=e.$parent.$parent.tbData,e.originTbData=a),e.form.Model_Ids=a.map((function(e){return e.Model_Ids})).toString(),e.form.Component_Ids=a.map((function(e){return e.Id})).filter((function(e){return!!e})),e.fetchData()}))}}}},1207:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("ac1f"),a("5319");var n=a("4872");t.default={data:function(){return{form:{License:"",Contact_UserName:"",Tare_Weight:"",Mobile:""},btnLoading:!1,rules:{Mobile:[{required:!0,message:"请输入司机电话",trigger:"blur"}],License:[{required:!0,message:"请输入车牌号",trigger:"blur"}],Contact_UserName:[{required:!0,message:"请输入司机姓名",trigger:"blur"}]}}},methods:{getLicense:function(e){this.form.License=e.replace(/([a-zA-Z])/g,(function(e){return e.toUpperCase()}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.btnLoading=!0,(0,n.SaveCar)(e.form).then((function(t){t.IsSucceed?(e.$message({message:"添加成功",type:"success"}),e.form.detail="".concat(e.form.License,"(").concat(e.form.Contact_UserName," ").concat(e.form.Mobile,")"),e.$emit("addCarData",e.form),e.$emit("close")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1})))}))}}}},"1b65":function(e,t,a){"use strict";a.r(t);var n=a("95fd"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"28c8a":function(e,t,a){"use strict";a.r(t);var n=a("96e4"),r=a("5686");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("3cb0");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"316315cb",null);t["default"]=s.exports},3031:function(e,t,a){"use strict";a.r(t);var n=a("1207"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"3c4a":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AuxOutExport=E,t.AuxReturnByReceipt=ce,t.EditAuxOutStatus=U,t.EditOutStatus=O,t.ExportFlow=g,t.ExportProjectRawAnalyse=me,t.ExportRawForProject=ge,t.FindAuxFlowList=ie,t.FindAuxPageList=x,t.FindPageList=j,t.FindProjectRawAnalyse=X,t.FindRawFlowList=re,t.FindRawPageList=K,t.FindReturnStoreNewPageList=P,t.FindReturnStoreNewSum=I,t.FindStoreDetail=B,t.GetAuxBatchInventoryDetailList=fe,t.GetAuxDetailByReceipt=ue,t.GetAuxInventoryDetailList=c,t.GetAuxInventoryPageList=u,t.GetAuxSummary=$,t.GetAuxWarningPageList=d,t.GetCurUserCompanyId=T,t.GetFirstLevelDepartsUnderFactory=L,t.GetInPageList=f,t.GetInPageListSum=h,t.GetMaterielRawOutStoreList=_,t.GetOutFromSourceData=D,t.GetOutPageList=m,t.GetOutSum=p,t.GetPickOutDetail=W,t.GetProjectRawAnalyseByProject=oe,t.GetProjectRawAnalyseDetail=ee,t.GetProjectRawAnalyseSum=te,t.GetRawBatchInventoryDetailList=de,t.GetRawDetailByReceipt=le,t.GetRawFilterDataSummary=_e,t.GetRawForProjectDetail=be,t.GetRawForProjectPageList=he,t.GetRawInventoryDetailList=i,t.GetRawInventoryPageList=o,t.GetRawSummary=l,t.GetRawWHSummaryList=pe,t.GetRawWarningPageList=s,t.GetTeamListByUserForMateriel=Pe,t.GetUserPage=w,t.List=b,t.ListDetail=k,t.OutSourcingOutStore=v,t.OutSourcingOutStoreDetail=C,t.PartyAAuxOutStore=N,t.PartyAAuxOutStoreDetail=G,t.PartyAOutStore=H,t.PartyAOutStoreDetail=Y,t.PickOutStore=F,t.PickUpOutStore=y,t.PickUpOutStoreDetail=S,t.RawOutExport=R,t.RawReturnByReceipt=se,t.SelfAuxReturnOutStore=A,t.SelfAuxReturnOutStoreDetail=M,t.SelfReturnOutStore=Q,t.SelfReturnOutStoreDetail=Z,t.SetAuxLT=ne,t.SetAuxLock=q,t.SetAuxUnlock=J,t.SetRawLT=ae,t.SetRawLock=z,t.SetRawUnlock=V,t.TransferRawLock=Ie;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawInventoryPageList",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawInventoryDetailList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawWarningPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawSummary",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxInventoryPageList",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxInventoryDetailList",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxWarningPageList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/MaterielFlow/GetInPageList",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/MaterielFlow/GetOutPageList",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/MaterielFlow/GetOutSum",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/MaterielFlow/GetInPageListSum",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/MaterielFlow/ExportFlow",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/List",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/List",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/MaterielReturnStore/FindReturnStoreNewPageList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/MaterielReturnStore/FindReturnStoreNewSum",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/PickUpOutStore",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/OutSourcingOutStore",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/PickUpOutStoreDetail",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/OutSourcingOutStoreDetail",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/MaterielRawInStore/ListDetail",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/MaterielRawOutStoreNew/GetOutFromSourceData",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/EditOutStatus",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/MaterielRawOutStore/Export",method:"post",data:e})}function L(e){return(0,r.default)({url:"/OMA/Common/GetFirstLevelDepartsUnderFactory",method:"post",data:e})}function w(e){return(0,r.default)({url:"/SYS/User/GetUserPage",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Communal/GetCurUserCompanyId",method:"post",data:e})}function x(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/FindAuxPageList",method:"post",data:e})}function A(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfAuxReturnOutStore",method:"post",data:e})}function N(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAAuxOutStore",method:"post",data:e})}function M(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfAuxReturnOutStoreDetail",method:"post",data:e})}function G(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAAuxOutStoreDetail",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/FindPageList",method:"post",data:e})}function $(e){return(0,r.default)({url:"PRO/MaterielInventory/GetAuxSummary",method:"post",data:e})}function F(e){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/PickOutStore",method:"post",data:e})}function W(e){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/GetPickOutDetail",method:"post",data:e})}function U(e){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/EditOutStatus",method:"post",data:e})}function E(e){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/Export",method:"post",data:e})}function B(e){return(0,r.default)({url:"/PRO/MaterielAuxOutStore/GetOutFromSourceData ",method:"post",data:e})}function z(e){return(0,r.default)({url:"/PRO/MaterielAssign/SetRawLock",method:"post",data:e})}function V(e){return(0,r.default)({url:"/PRO/MaterielAssign/SetRawUnlock",method:"post",data:e})}function q(e){return(0,r.default)({url:"/PRO/MaterielAssign/SetAuxLock",method:"post",data:e})}function J(e){return(0,r.default)({url:"/PRO/MaterielAssign/SetAuxUnlock",method:"post",data:e})}function K(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/FindRawPageList",method:"post",data:e})}function Q(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfReturnOutStore",method:"post",data:e})}function H(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAOutStore",method:"post",data:e})}function Y(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/PartyAOutStoreDetail",method:"post",data:e})}function Z(e){return(0,r.default)({url:"/PRO/MaterielReturnGoods/SelfReturnOutStoreDetail",method:"post",data:e})}function X(e){return(0,r.default)({url:"/PRO/MaterielReport/FindProjectRawAnalyse",method:"post",data:e})}function ee(e){return(0,r.default)({url:"/PRO/MaterielReport/GetProjectRawAnalyseDetail",method:"post",data:e})}function te(e){return(0,r.default)({url:"/PRO/MaterielReport/GetProjectRawAnalyseSum",method:"post",data:e})}function ae(e){return(0,r.default)({url:"/PRO/MaterielInventory/SetRawLT",method:"post",data:e})}function ne(e){return(0,r.default)({url:"/PRO/MaterielInventory/SetAuxLT",method:"post",data:e})}function re(e){return(0,r.default)({url:"/PRO/MaterielInventory/FindRawFlowList",method:"post",data:e})}function oe(e){return(0,r.default)({url:"/pro/MaterielReport/GetProjectRawAnalyseByProject",method:"post",data:e})}function ie(e){return(0,r.default)({url:"/pro/MaterielInventory/FindAuxFlowList",method:"post",data:e})}function se(e){return(0,r.default)({url:"/pro/MaterielReturnStore/RawReturnByReceipt",method:"post",data:e})}function le(e){return(0,r.default)({url:"/pro/MaterielReturnStore/GetRawDetailByReceipt",method:"post",data:e})}function ue(e){return(0,r.default)({url:"/pro/MaterielReturnStore/GetAuxDetailByReceipt",method:"post",data:e})}function ce(e){return(0,r.default)({url:"/pro/MaterielReturnStore/AuxReturnByReceipt",method:"post",data:e})}function de(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawBatchInventoryDetailList",method:"post",data:e})}function fe(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetAuxBatchInventoryDetailList",method:"post",data:e})}function me(e){return(0,r.default)({url:"/PRO/MaterielReport/ExportProjectRawAnalyse",method:"post",data:e})}function pe(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawWHSummaryList",method:"post",data:e})}function he(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawForProjectPageList",method:"post",data:e})}function ge(e){return(0,r.default)({url:"/PRO/MaterielInventory/ExportRawForProject",method:"post",data:e})}function be(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawForProjectDetail",method:"post",data:e})}function _e(e){return(0,r.default)({url:"/PRO/MaterielInventory/GetRawFilterDataSummary",method:"post",data:e})}function Pe(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetTeamListByUserForMateriel",method:"post",data:e})}function Ie(e){return(0,r.default)({url:"/Pro/MaterielAssignNew/TransferRawLock",method:"post",data:e})}},"3cb0":function(e,t,a){"use strict";a("0c2d")},"3ee5":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"车牌号",prop:"License"}},[a("el-input",{on:{change:e.getLicense},model:{value:e.form.License,callback:function(t){e.$set(e.form,"License",t)},expression:"form.License"}})],1),a("el-form-item",{attrs:{label:"司机姓名",prop:"Contact_UserName"}},[a("el-input",{model:{value:e.form.Contact_UserName,callback:function(t){e.$set(e.form,"Contact_UserName",t)},expression:"form.Contact_UserName"}})],1),a("el-form-item",{attrs:{label:"司机电话",prop:"Mobile"}},[a("el-input",{model:{value:e.form.Mobile,callback:function(t){e.$set(e.form,"Mobile",t)},expression:"form.Mobile"}})],1),a("el-form-item",{attrs:{label:"皮重",prop:"Tare_Weight"}},[a("el-input",{model:{value:e.form.Tare_Weight,callback:function(t){e.$set(e.form,"Tare_Weight",t)},expression:"form.Tare_Weight"}})],1),a("el-form-item",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)],1)},r=[]},"3f1d":function(e,t,a){"use strict";a("4619")},"3f35":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportPackingList=f,t.GeneratePackCode=i,t.GetPacking2ndEntity=l,t.GetPacking2ndPageList=s,t.GetPackingGroupByDirectDetailList=o,t.GetWaitPack2ndPageList=u,t.SavePacking2nd=d,t.UnzipPacking2nd=c;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:e})}function i(){return(0,r.default)({url:"/PRO/Packing/GeneratePackCode",method:"post"})}function s(e){return(0,r.default)({url:"/PRO/Packing/GetPacking2ndPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Packing/GetPacking2ndEntity",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Packing/GetWaitPack2ndPageList",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Packing/UnzipPacking2nd",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Packing/SavePacking2nd",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Packing/ExportPackingList",method:"post",data:e})}},"40d1":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a("3f35");t.default={data:function(){return{innerTableData:[],dialogVisible:!1}},methods:{handleInfo:function(e){var t=this;(0,n.GetPackingGroupByDirectDetailList)({Unique_Code:e.Unique_Code}).then((function(e){e.IsSucceed?t.innerTableData=e.Data:t.$message({message:e.Message,type:"error"})}))},handleOpen:function(e){this.dialogVisible=!0,this.handleInfo(e)},handleClose:function(){}}}},4157:function(e,t,a){"use strict";a("68be")},"43c7":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header"},[a("el-button",{on:{click:e.toBack}},[e._v("返回")])],1)]},proxy:!0},{key:"right",fn:function(){return[a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSubmit}},[e._v("保存")])]},proxy:!0}])}),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货单号",prop:"receiveNum"}},[a("el-input",{attrs:{disabled:!0,placeholder:"自动生成"},model:{value:e.form.receiveNum,callback:function(t){e.$set(e.form,"receiveNum",t)},expression:"form.receiveNum"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"项目名称",prop:"projectName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.projectName,callback:function(t){e.$set(e.form,"projectName",t)},expression:"form.projectName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"收货人",prop:"receiveName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入内容"},model:{value:e.form.receiveName,callback:function(t){e.$set(e.form,"receiveName",t)},expression:"form.receiveName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"收货人电话",prop:"Receiver_Tel"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Receiver_Tel,callback:function(t){e.$set(e.form,"Receiver_Tel",t)},expression:"form.Receiver_Tel"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"车辆信息",prop:"License"}},[a("el-select",{staticStyle:{width:"70%"},attrs:{clearable:"",disabled:(e.pageStatus>=2||1===e.pageStatus)&&e.isEdit,placeholder:"请选择",filterable:""},on:{change:e.carChange},model:{value:e.form.License,callback:function(t){e.$set(e.form,"License",t)},expression:"form.License"}},e._l(e.carOptions,(function(e){return a("el-option",{key:e.License,attrs:{label:e.detail,value:e.License}})})),1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:e.handleEditCar}},[e._v("新增车辆")])],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货部门",prop:"Depart_Id"}},[!0===e.isProductweight?a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.form.Depart_Id,callback:function(t){e.$set(e.form,"Depart_Id",t)},expression:"form.Depart_Id"}},e._l(e.pickDepartmentList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1):a("el-tree-select",{ref:"treeSelectDepart",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0},"tree-params":e.treeParamsDepart},on:{"select-clear":e.departClear,"node-click":e.departChange},model:{value:e.form.Depart_Id,callback:function(t){e.$set(e.form,"Depart_Id",t)},expression:"form.Depart_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货人",prop:"issueName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.issueName,callback:function(t){e.$set(e.form,"issueName",t)},expression:"form.issueName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发货时间",prop:"Out_Date"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.Out_Date,callback:function(t){e.$set(e.form,"Out_Date",t)},expression:"form.Out_Date"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"专业",prop:"ProfessionalTypeName"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.ProfessionalTypeName,callback:function(t){e.$set(e.form,"ProfessionalTypeName",t)},expression:"form.ProfessionalTypeName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"收货地址",prop:"Address"}},[a("el-input",{model:{value:e.form.Address,callback:function(t){e.$set(e.form,"Address",t)},expression:"form.Address"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{autosize:{minRows:2,maxRows:2},maxlength:1e3,"show-word-limit":"",type:"textarea"},model:{value:e.form.Remarks,callback:function(t){e.$set(e.form,"Remarks",t)},expression:"form.Remarks"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{action:"alioss",limit:10,multiple:!0,"on-success":function(t,a,n){e.uploadSuccess(t,a,n)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.handleExceed,"file-list":e.fileListData,"show-file-list":!0,disabled:!1}},[a("el-button",{attrs:{type:"primary",disabled:!1}},[e._v("上传文件")])],1)],1)],1)],1)],1),e.isEdit?a("div",[a("h4",[e._v(" 过磅信息")]),a("el-form",{ref:"form",attrs:{inline:"",model:e.weightform,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"皮重"}},[e._v(" "+e._s(e.weightform.Tare_Weight)+"kg ")]),a("el-form-item",{attrs:{label:"理重"}},[e._v(" "+e._s(e.weightform.Reason_Weight)+"kg ")]),a("el-form-item",{attrs:{label:"磅重"}},[e._v(" "+e._s(e.weightform.Pound_Weight)+"kg ")]),a("el-form-item",{attrs:{label:"净重",prop:"region"}},[a("span",{class:{"cs-red":e.showRed}},[e._v(e._s(e.netWeight)+" "),e.showRed?a("span",[e._v("（"+e._s(e.getNum(e.netWeight,e.weightform.Reason_Weight)>0?"高于":"低于")+"理重"+e._s(Math.abs(+e.getNum(e.netWeight,e.weightform.Reason_Weight)))+"kg）")]):e._e()])]),a("el-form-item",{attrs:{label:"过磅备注",prop:"region"}},[e._v(" "+e._s(e.plm_ProjectSendingInfo.Pound_Remark)+" ")]),a("el-form-item",{attrs:{label:"附件"}},[e._l(e.weightFileInfo,(function(t,n){return[a("el-link",{key:n,attrs:{href:t.url,target:"_blank"}},[e._v(e._s(t.name))]),n!==e.weightFileInfo.length-1?a("el-divider",{key:n,attrs:{direction:"vertical"}}):e._e()]}))],2)],1)],1):e._e(),a("top-header",{staticStyle:{"margin-bottom":"10px"},scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header",staticStyle:{"margin-bottom":"20px"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:e.radioChange},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[a("el-radio-button",{attrs:{label:"pro_component_out_detail_list"}},[e._v("构件")]),a("el-radio-button",{attrs:{label:"pro_package_out_detail_list"}},[e._v("打包件")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[e.sendNumber?a("div",{staticClass:"statistics-container"},[a("div",{staticClass:"statistics-item",staticStyle:{"margin-right":"0"}},[a("span",[e._v("发货序号：")]),a("span",[e._v(e._s(e.sendNumber))])])]):e._e(),e.isSub?e._e():a("el-button",{attrs:{disabled:!e.selectList.length,size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除")]),e.isSub?e._e():a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("添加")])]},proxy:!0}])}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:!1===e.Is_Pack?e.tbData:e.tbData2,page:e.form.PageInfo.Page,"sum-values":e.sums,"select-width":70,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"S_Count",fn:function(t){var n=t.row;return[e.Is_Pack||e.isSub?a("div",[e._v(e._s(n.S_Count))]):a("div",[a("el-input",{staticStyle:{width:"50px",border:"1px solid #eee","border-radius":"4px"},attrs:{type:"text",readonly:1==n.Wait_Stock_Count},on:{blur:function(t){e.inputBlur(t,n.S_Count,n)}},model:{value:n.S_Count,callback:function(t){e.$set(n,"S_Count",t)},expression:"row.S_Count"}})],1)]}},{key:"PackageSn",fn:function(t){var n=t.row;return[a("div",{staticStyle:{color:"#298dff",cursor:"pointer"},on:{click:function(t){return e.handleDetail(n)}}},[e._v(e._s(n.PackageSn))])]}}])})],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:e.width,top:e.topDialog},on:{"update:visible":function(t){e.dialogVisible=t},close:e.close}},[a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible,"project-id":e.projectId,"sys-project-id":e.form.ProjectId,"add-radio":e.addradio,"is-pack":e.Is_Pack},on:{addCarData:e.addCarData,close:e.close,reCount:e.getTotal,refresh:e.fetchData,selectList:e.addSelectList}})],1):e._e(),a("check-info",{ref:"info"})],1)])},r=[]},4619:function(e,t,a){},4872:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.CarDataTemplate=d,t.DeleteCar=u,t.GetCarList=o,t.GetCarPageList=s,t.GetCurCarPageList=i,t.ImportCar=c,t.SaveCar=l;var r=n(a("b775"));n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Car/GetCarList",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Car/GetCurCarPageList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Car/GetCarPageList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Car/SaveCar",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Car/DeleteCar",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Car/ImportCar",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Car/CarDataTemplate",method:"post",data:e})}},"4b32":function(e,t,a){"use strict";a.r(t);var n=a("a729"),r=a("939e");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"784abcb7",null);t["default"]=s.exports},5686:function(e,t,a){"use strict";a.r(t);var n=a("8b69"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"575c":function(e,t,a){"use strict";a.r(t);var n=a("0e48"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"588d":function(e,t,a){},"591d":function(e,t,a){"use strict";a.r(t);var n=a("3ee5"),r=a("3031");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("3f1d");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"0cb01311",null);t["default"]=s.exports},"68be":function(e,t,a){},"74f4":function(e,t,a){"use strict";a.r(t);var n=a("43c7"),r=a("b52c");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("bd4d");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"5e8e9786",null);t["default"]=s.exports},"7c56":function(e,t,a){"use strict";a.r(t);var n=a("abe7"),r=a("1b65");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("f5f7");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"3eff9b38",null);t["default"]=s.exports},"7de9":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=m,t.AddCheckItemCombination=b,t.AddCheckType=l,t.DelNode=D,t.DelQualityList=S,t.DeleteCheckItem=f,t.DeleteCheckType=u,t.EntityCheckItem=p,t.EntityCheckType=i,t.EntityQualityList=v,t.ExportInspsectionSummaryInfo=T,t.GetCheckGroupList=g,t.GetCheckItemList=d,t.GetCheckTypeList=s,t.GetCompTypeTree=R,t.GetDictionaryDetailListByCode=o,t.GetEntityNode=k,t.GetFactoryPeoplelist=P,t.GetFactoryProfessionalByCode=L,t.GetMaterialType=w,t.GetNodeList=C,t.GetProEntities=_,t.GetProcessCodeList=I,t.QualityList=y,t.SaveCheckItem=h,t.SaveCheckType=c,t.SaveNode=O;var r=n(a("b775"));function o(e){return(0,r.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function i(e){return(0,r.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function d(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function S(e){return(0,r.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function k(e){return(0,r.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function D(e){return(0,r.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function O(e){return(0,r.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function R(e){return(0,r.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function L(e){return(0,r.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function T(e){return(0,r.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},"8b69":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("c14f")),o=n(a("1da1")),i=(a("d7b0"),n(a("0f97"))),s=n(a("5cc7")),l=a("3f35");t.default={components:{DynamicDataTable:i.default},mixins:[s.default],data:function(){return{tbLoading:!1,ProfessionalType:[],tbConfig:{},columns:[],total:0,form:{PageInfo:{Page:1,PageSize:20}},packDetail:{},packDetailList:[]}},methods:{init:function(e){var t=this;return(0,o.default)((0,r.default)().m((function a(){return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return t.tbLoading=!0,a.n=1,t.getFactoryTypeOption("pro_waiting_out_detail_package");case 1:t.fetchData(e);case 2:return a.a(2)}}),a)})))()},fetchData:function(e){var t=this;(0,l.GetPacking2ndEntity)({id:e}).then((function(e){e.IsSucceed?(t.packDetail=e.Data.Entity,t.packDetailList=e.Data.Details,t.tbLoading=!1):t.$message({message:e.Message,type:"error"})}))}}}},"90a0":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("99af"),a("4de4"),a("7db0"),a("c740"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9f5"),a("d866"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("25f0"),a("3ca3"),a("159b"),a("ddb0");var r=n(a("2909")),o=n(a("c14f")),i=n(a("1da1")),s=n(a("7c56")),l=n(a("9bfc")),u=n(a("28c8a")),c=a("9643"),d=a("3c4a"),f=a("4744"),m=n(a("0f97")),p=n(a("591d")),h=a("4872"),g=n(a("34e9")),b=n(a("4b32")),_=a("ed08"),P=a("1b69"),I=a("6186"),y=a("fd31"),v=n(a("6612")),S=n(a("c7ab")),C=a("0e9a");t.default={components:{OSSUpload:S.default,TitleInfo:s.default,AddDialog:l.default,TopHeader:g.default,DynamicDataTable:m.default,CarDialog:p.default,CheckInfo:b.default,packDetail:u.default},props:{isEdit:{type:Boolean,default:!1}},data:function(){return{pageStatus:void 0,radio:"pro_component_out_detail_list",addradio:"pro_waiting_out_list",Is_Pack:!1,isClicked:!1,isSub:!1,width:"40%",topDialog:"1vh",btnLoading:!1,currentComponent:"",title:"",loading:!1,dialogVisible:!1,sendNumber:"",form:{ProjectId:"",Out_Date:new Date,Remarks:"",Contact_UserName:"",Mobile:"",License:"",Address:"",receiveName:"",issueName:"",Receiver_Tel:"",Area_Id:"",projectName:"",receiveNum:"",ProfessionalTypeName:"",Depart_Id:"",PageInfo:{ParameterJson:[],Page:1,PageSize:20}},treeParamsDepart:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},pickDepartmentList:[],plm_ProjectSendingInfo:{},produced_Components:[],weightFileInfo:[],projectSendingInfo_Item:[],Itemdetail:[],PackagesList:[],ProfessionalType:[],fileListArr:[],carOptions:[],projects:"",Id:"",projectId:"",planTime:"",showDialog:!1,tbConfig:{Pager_Align:"center"},columns:[],tbData:[],tbData2:[],total:0,tbLoading:!1,selectList:[],fileListData:[],sums:[],rules:{Out_Date:[{required:!0,message:"请选择发货时间",trigger:"change"}],Depart_Id:[{required:!0,message:"请选择",trigger:"change"}],issueName:[{required:!0,message:"请输入",trigger:"blur"}]},old_Component_Ids:[],weightform:{Tare_Weight:0,Reason_Weight:0,Pound_Weight:0,Net_Weight:0,Weigh_Warning_Threshold:0},isProductweight:null}},computed:{netWeight:function(){return this.weightform.Pound_Weight&&this.weightform.Tare_Weight?this.weightform.Pound_Weight-this.weightform.Tare_Weight:0},showRed:function(e){var t=e.netWeight;return Math.abs(t-this.weightform.Reason_Weight)>=this.weightform.Weigh_Warning_Threshold}},watch:{"tbData.length":{handler:function(){this.getTotal()}}},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.isSub=e.$route.query.isSub||!1,t.n=1,e.getSettingProductweight();case 1:e.isProductweight?e.getFactoryDepartmentData():e.getDepartmentTree(),e.getFactoryTypeOption(),e.getAllCarList();case 2:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a,n,r,i,s,l;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:e.isEdit?e.getInfo():(a=JSON.parse(decodeURIComponent(e.$route.query.p)),n=a.Name,r=a.Id,i=a.Code,s=a.Address,l=a.Sys_Project_Id,e.projectId=r,e.Project_Code=i,e.form.projectName=n,e.form.Address=s,e.form.ProjectId=l,e.form.issueName=e.$store.state.user.name,e.getProjectEntity(e.projectId));case 1:return t.a(2)}}),t)})))()},methods:{getSettingProductweight:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,f.GetPreferenceSettingValue)({code:"Productweight"});case 1:a=t.v,"true"===a.Data&&(e.isProductweight=!0);case 2:return t.a(2)}}),t)})))()},getFactoryDepartmentData:function(){var e=this;(0,d.GetFirstLevelDepartsUnderFactory)({FactoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){e.pickDepartmentList=t.Data;var a=localStorage.getItem("DepartmentId"),n=e.pickDepartmentList.find((function(e){return e.Id===a}));n&&!e.isEdit&&(e.form.Depart_Id=n.Id)}else e.$message({message:t.Message,type:"error"})}))},getFlattenedSelectableItems:function(e){if(!e||!e.length)return[];var t=[],a=function(e){e&&e.length&&e.forEach((function(e){var n,r,o=e.Children;!0!==(null===(n=e.Data)||void 0===n?void 0:n.Is_Company)&&"1"!==(null===(r=e.Data)||void 0===r?void 0:r.Type)&&t.push(e),o&&o.length>0&&a(o)}))};a(e);var n=t.find((function(e){return e.Id===localStorage.getItem("DepartmentId")}));n&&(this.form.Depart_Id=n.Id)},getDepartmentTree:function(){var e=this;(0,I.GetCompanyDepartTree)({isAll:!0}).then((function(t){if(t.IsSucceed){var a=t.Data;e.setDisabledTree(a),e.treeParamsDepart.data=a,e.$nextTick((function(t){var n;null===(n=e.$refs.treeSelectDepart)||void 0===n||n.treeDataUpdateFun(a);e.getFlattenedSelectableItems(a)}))}else e.$message({message:t.Message,type:"error"})}))},setDisabledTree:function(e){var t=this;e&&e.forEach((function(e){var a=e.Children;!0===e.Data.Is_Company||"1"===e.Data.Type?e.disabled=!0:e.disabled=!1,a.length>0&&t.setDisabledTree(a)}))},departClear:function(){},departChange:function(){},getNum:function(e,t){return(0,v.default)(e).subtract(t).format("0.[000]")},getFactoryTypeOption:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,y.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?(e.ProfessionalType=t.Data,e.form.ProfessionalTypeName=e.ProfessionalType[0].Name):e.$message({message:t.Message,type:"error"})}));case 1:return t.n=2,e.getTableConfig("pro_component_out_detail_list,".concat(e.ProfessionalType[0].Code));case 2:return t.a(2)}}),t)})))()},getProjectEntity:function(e){var t=this;(0,P.GetProjectEntity)({Id:e}).then((function(e){if(e.IsSucceed){var a=e.Data.Contacts.find((function(e){return"Consignee"==e.Type}));t.form.receiveName=null===a||void 0===a?void 0:a.Name,t.form.Receiver_Tel=null===a||void 0===a?void 0:a.Tel}}))},getProjectPageList:function(){var e=this;(0,P.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projects=t.Data.Data)}))},toBack:function(){var e=this;this.$confirm("此操作不会保存编辑数据，是否退出？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,_.closeTagView)(e.$store,e.$route)})).catch((function(){e.$message({type:"info",message:"已取消"})}))},radioChange:function(e){this.getTableConfig("".concat(e,",").concat(this.ProfessionalType[0].Code)),"pro_component_out_detail_list"===e?(this.addradio="pro_waiting_out_list",this.Is_Pack=!1):"pro_package_out_detail_list"===e&&(this.addradio="pro_waiting_out_list_package",this.Is_Pack=!0)},inputBlur:function(e,t,a){t<1||t>a.Wait_Stock_Count?a.S_Count=a.Wait_Stock_Count:a.S_Count=Number(t),a.AllWeight=this.getAllWeight(a),this.Itemdetail.find((function(e){e.Component_Id==a.Id&&(e.SteelAmount=a.S_Count)}))},handleSubmit:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.isClicked=!0;var a=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){a.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)}));var n={plm_ProjectSendingInfo:{Attachment:a.toString(),ProjectId:e.projectId,Consignee:e.form.receiveName,ConsigneeTel:e.form.Receiver_Tel,Depart_Id:e.form.Depart_Id,MakerName:e.form.issueName,VehicleNo:e.form.License,DriverName:e.form.Contact_UserName,Telephone:e.form.Mobile,SendDate:(0,_.parseTime)(e.form.Out_Date,"{y}-{m}-{d} {h}:{i}:{s}"),Remarks:e.form.Remarks,ProjectName:e.form.projectName,TypeId:e.ProfessionalType[0].Code,Address:e.form.Address},projectSendingInfo_Item:[]},o=[].concat((0,r.default)(e.tbData),(0,r.default)(e.tbData2));if(o.filter((function(e){if(e.PackageSn){e.Id;var t=e.Stock_Count,a=e.PackageSn,r=e.Warehouse_Id,o=e.Location_Id,i=e.Netweight,s=e.AllWeight,l=e.DIM,u=e.Volume,c=e.AllAmount,d=e.Import_Detail_Id,f=e.Model_Ids,m=e.isOld,p={PackageSn:a||"",SteelAmount:t||1,Warehouse_Id:r||"",Location_Id:o||"",SteelWeight:i||"",Import_Detail_Id:d,Model_Ids:f,DIM:l,Volume:u,AllWeight:s,AllAmount:c,isOld:m};m||n.projectSendingInfo_Item.push(p)}else{var h=e.Id,g=e.S_Count,b=(e.Stock_Count,e.Warehouse_Id),_=e.Import_Detail_Id,P=e.Model_Ids,I=e.Location_Id,y=e.Netweight,v=e.AllWeight,S=e.isOld,C={Component_Id:h||"",SteelAmount:g||"",Warehouse_Id:b||"",Location_Id:I||"",SteelWeight:y||"",Import_Detail_Id:_,Model_Ids:P,isOld:S,AllWeight:v};S||(delete C.isOld,n.projectSendingInfo_Item.push(C))}})),e.btnLoading=!0,e.isEdit){e.plm_ProjectSendingInfo.Consignee=e.form.receiveName,e.plm_ProjectSendingInfo.ConsigneeTel=e.form.Receiver_Tel,e.plm_ProjectSendingInfo.VehicleNo=e.form.License,e.plm_ProjectSendingInfo.DriverName=e.form.Contact_UserName,e.plm_ProjectSendingInfo.Telephone=e.form.Mobile,e.plm_ProjectSendingInfo.Depart_Id=e.form.Depart_Id,e.plm_ProjectSendingInfo.MakerName=e.form.issueName,e.plm_ProjectSendingInfo.Address=e.form.Address,e.plm_ProjectSendingInfo.SendDate=(0,_.parseTime)(e.form.Out_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e.plm_ProjectSendingInfo.Remarks=e.form.Remarks;var i=[];e.fileListArr.length>0&&e.fileListArr.forEach((function(e){i.push(e.response&&e.response.encryptionUrl?e.response.encryptionUrl:e.encryptionUrl)})),e.plm_ProjectSendingInfo.Attachment=i.toString(),n.plm_ProjectSendingInfo=e.plm_ProjectSendingInfo,n.projectSendingInfo_Item=[].concat((0,r.default)(e.Itemdetail),(0,r.default)(e.PackagesList),(0,r.default)(n.projectSendingInfo_Item)),0==n.projectSendingInfo_Item.length?e.$message({message:"不能保存空发货单",type:"error"}):(e.loading=!0,(0,c.EditProjectSendingInfo)(n).then((function(t){t.IsSucceed?(e.$message({message:"编辑成功",type:"success"}),(0,_.closeTagView)(e.$store,e.$route)):(e.isClicked=!1,e.$message({message:t.Message,type:"error"})),e.loading=!1,e.btnLoading=!1})))}else 0==n.projectSendingInfo_Item.length?e.$message({message:"不能保存空发货单",type:"error"}):(e.loading=!0,(0,c.AddProjectSendingInfo)(n).then((function(t){t.IsSucceed?(e.$message({message:"添加成功",type:"success"}),(0,_.closeTagView)(e.$store,e.$route)):(e.isClicked=!1,e.$message({message:t.Message,type:"error"})),e.btnLoading=!1,e.loading=!1})))}}))},getInfo:function(){var e=this;(0,c.GetProjectsendinginEntity)({Id:this.$route.query.id}).then(function(){var t=(0,i.default)((0,o.default)().m((function t(a){var n,r,s,l,u,c,d,f,m,p,h,g,b,_,P,I,y,v;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(!a.IsSucceed){t.n=3;break}if(e.plm_ProjectSendingInfo=a.Data.Plm_ProjectSendingInfo,e.Itemdetail=a.Data.Itemdetail,e.PackagesList=a.Data.PackagesList,e.weightform=a.Data.WeightInfo,e.pageStatus=e.plm_ProjectSendingInfo.Status,n=e.plm_ProjectSendingInfo,n.Id,r=n.Code,s=n.ProjectId,l=n.ProjectName,u=n.Consignee,c=n.ConsigneeTel,d=n.Depart_Id,f=n.MakerName,m=n.VehicleNo,p=n.DriverName,h=n.Telephone,g=n.Address,b=n.Attachment,_=n.SendDate,P=n.Remarks,I=n.Number,e.form.ProjectId=s,e.form.receiveNum=r,e.form.projectName=l,e.form.receiveName=u,e.form.Receiver_Tel=c,e.form.Depart_Id=d,e.form.issueName=f,e.form.License=m,e.form.Contact_UserName=p,e.form.Mobile=h,e.form.Address=g,e.form.Out_Date=new Date(_),e.form.Remarks=P,e.sendNumber=I,m&&e.carOptions.every((function(e){return e.License!==m}))&&e.carOptions.push({License:m,Contact_UserName:p,Mobile:h,detail:m?"".concat(m,"(").concat(p," ").concat(h,")"):""}),b&&(y=b.split(","),y.forEach((function(t){var a=t.indexOf("?Expires=")>-1?t.substring(0,t.lastIndexOf("?Expires=")):t,n=decodeURI(a.substring(a.lastIndexOf("/")+1)),r={};r.name=decodeURIComponent(n),r.url=a,r.encryptionUrl=a,e.fileListData.push(r),e.fileListArr.push(r)}))),!e.weightform.Attachment_Weight){t.n=2;break}return v=e.weightform.Attachment_Weight.split(",").map(function(){var t=(0,i.default)((0,o.default)().m((function t(a){var n,r,i;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return n=a.split("?")[0],t.n=1,e.handleUrl(n);case 1:return r=t.v,i=(0,C.getFileNameFromUrl)(n),t.a(2,{url:r,name:i})}}),t)})));return function(e){return t.apply(this,arguments)}}()),t.n=1,Promise.all(v);case 1:e.weightFileInfo=t.v;case 2:e.Itemdetail.forEach((function(t,a){var n=t.Component_Id,r=t.S_Count,o=t.SteelWeight,i=t.AllWeight,s=t.Name,l=t.Spec,u=t.Length,c=t.WarehouseName,d=t.Code,f=t.LocationName,m=t.Area_Name,p=t.Wait_Stock_Count,h=t.Import_Detail_Id,g=t.Location_Id,b=t.SerialNumber,_={Id:n,Area_Name:m,Name:s,Spec:l,Length:u,WarehouseName:c,Code:d,LocationName:f,Import_Detail_Id:h,Location_Id:g,S_Count:r,Wait_Stock_Count:p,Netweight:o,AllWeight:i,isOld:!0,SerialNumber:b};e.tbData.push(_),e.old_Component_Ids.push(n)})),e.PackagesList.forEach((function(t,a){var n=t.PkgNO,r=t.PackageSn,o=t.AllWeight,i=t.Volume,s=t.AllAmount,l=t.WarehouseName,u=t.LocationName,c=t.DIM,d=t.PackageId,f={PkgNO:n,PackageSn:r,AllWeight:o,AllAmount:s,Volume:i,WarehouseName:l,LocationName:u,isOld:!0,DIM:c,PackageId:d};e.tbData2.push(f)})),t.n=4;break;case 3:e.$message({message:a.Message,type:"error"});case 4:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleUrl:function(e){return(0,i.default)((0,o.default)().m((function t(){var a,n;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,I.GetOssUrl)({url:e});case 1:return a=t.v,n=a.Data,t.a(2,n)}}),t)})))()},carChange:function(e){var t=this;if(!e)return this.form.Contact_UserName="",this.form.Mobile="",void(this.form.License="");var a=this.carOptions.find((function(e){return e.License===t.form.License}));this.form.Contact_UserName=a.Contact_UserName,this.form.Mobile=a.Mobile,this.form.License=a.License},projectIdChange:function(e){},projectIdClear:function(e){},getAllWeight:function(e){return Number(e.S_Count*e.Netweight).toFixed(2)/1},addSelectList:function(e){var t=this;!1===this.Is_Pack?(e.forEach((function(e){e.AllWeight=t.getAllWeight(e),t.tbData.push(e)})),this.tbData=JSON.parse(JSON.stringify(this.tbData)),this.total=this.tbData.length):!0===this.Is_Pack&&(e.forEach((function(e){t.tbData2.push(e)})),this.total=this.tbData2.length)},handleDelete:function(){var e=this;this.$confirm("删除该数据, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.Is_Pack?e.selectList.forEach((function(t){var a=e.tbData2.findIndex((function(e){return e.PackageSn===t.PackageSn}));if(-1!==a&&e.tbData2.splice(a,1),e.isEdit){var n=e.PackagesList.findIndex((function(e){return e.PackageSn===t.PackageSn}));-1!==n&&e.PackagesList.splice(n,1)}})):e.selectList.forEach((function(t){var a=e.tbData.findIndex((function(e){return e.Import_Detail_Id+e.Location_Id===t.Import_Detail_Id+t.Location_Id}));if(-1!==a&&e.tbData.splice(a,1),e.isEdit){var n=e.Itemdetail.findIndex((function(e){return e.Component_Id===t.Id}));-1!==n&&e.Itemdetail.splice(n,1)}})),e.$message({type:"success",message:"删除成功!"})})).catch((function(){}))},multiSelectedChange:function(e){this.selectList=e},fetchData:function(){},getTotal:function(){},getAllCarList:function(){var e=this;return new Promise((function(t){(0,h.GetCurCarPageList)({}).then((function(a){a.IsSucceed?(e.carOptions=a.Data,e.carOptions.forEach((function(t,a){e.$set(t,"detail","".concat(t.License,"(").concat(t.Contact_UserName," ").concat(t.Mobile,")"))})),t()):e.$message({message:a.Message,type:"error"})}))}))},addCarData:function(e){this.getAllCarList()},handleAdd:function(){var e=this;if(this.currentComponent="AddDialog",this.width="80%",this.topDialog="1vh",this.dialogVisible=!0,!1===this.Is_Pack){this.title="添加构件";var t=this.tbData.filter((function(e){return!e.isOld}));this.$nextTick((function(a){e.$refs.content.init(t,e.tbData)}))}else if(!0===this.Is_Pack){this.title="添加打包件";var a=this.tbData2.filter((function(e){return!e.isOld}));this.$nextTick((function(t){e.$refs.content.init(a,e.tbData2)}))}},handleDetail:function(e){var t=this;this.currentComponent="packDetail",this.width="60%",this.title="打包件详情",this.topDialog="10vh",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs.content.init(e.PackageId||e.Id)}))},handleEditCar:function(){this.currentComponent="CarDialog",this.title="新增车辆",this.topDialog="10vh",this.dialogVisible=!0},close:function(){this.dialogVisible=!1},handleInfo:function(e){this.$refs.info.handleOpen(e)},getTableConfig:function(e){var t=this;return new Promise((function(a){(0,I.GetGridByCode)({code:e}).then((function(e){var n=e.IsSucceed,r=e.Data,o=e.Message;if(n){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,r.Grid),t.columns=(r.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e})),t.form.PageInfo.PageSize=+r.Grid.Row_Number,t.isSub&&(t.tbConfig.Is_Select=!1,t.tbConfig.Is_Row_Number=!0),a(t.columns)}else t.$message({message:o,type:"error"})}))}))},uploadSuccess:function(e,t,a){this.fileListArr=JSON.parse(JSON.stringify(a))},uploadRemove:function(e,t){this.fileListArr=JSON.parse(JSON.stringify(t))},handlePreview:function(e){return(0,i.default)((0,o.default)().m((function t(){var a;return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:if(a="",!e.response||!e.response.encryptionUrl){t.n=1;break}a=e.response.encryptionUrl,t.n=3;break;case 1:return t.n=2,(0,I.GetOssUrl)({url:e.encryptionUrl});case 2:a=t.v,a=a.Data;case 3:window.open(a);case 4:return t.a(2)}}),t)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过10个"})}}}},"939e":function(e,t,a){"use strict";a.r(t);var n=a("40d1"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},"95fd":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{title:{type:String,default:""}},data:function(){return{}}}},"96e4":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pack-container"},[a("div",{staticClass:"statistical-container"},[a("span",[e._v("构件总数 "+e._s(e.packDetail.AllAmount)+" 件")]),a("span",[e._v("构件总重 "+e._s(e.packDetail.AllWeight)+" kg")])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"table-container"},[a("dynamic-data-table",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.packDetailList,page:e.form.PageInfo.Page,total:e.total,border:"",stripe:""},scopedSlots:e._u([{key:"Is_Component_Name",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Is_Component_Name?"直发件"==n.Is_Component_Name?"是":"否":"-"))])]}}])})],1)])},r=[]},"9bfc":function(e,t,a){"use strict";a.r(t);var n=a("e47b"),r=a("575c");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("4157");var i=a("2877"),s=Object(i["a"])(r["default"],n["a"],n["b"],!1,null,"2e864424",null);t["default"]=s.exports},a729:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"提示","append-to-body":"",visible:e.dialogVisible,width:"50%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-table",{staticClass:"inner-tb",staticStyle:{width:"100%"},attrs:{data:e.innerTableData}},[a("el-table-column",{attrs:{align:"center",prop:"InstallUnit_Name",label:"生产单元"}}),a("el-table-column",{attrs:{align:"center",prop:"Component_Code",label:"编号",width:"180"}}),a("el-table-column",{attrs:{align:"center",prop:"Unique_Code",label:"唯一码"}}),a("el-table-column",{attrs:{align:"center",prop:"Spec",label:"规格型号"}}),a("el-table-column",{attrs:{align:"center",prop:"Num",label:"数量"}}),a("el-table-column",{attrs:{align:"center",prop:"NetWeight",label:"总重（kg）"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("确 定")])],1)],1)},r=[]},abe7:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"title-box"},[a("div",{staticClass:"title"},[e._v(" "+e._s(e.title)+" ")]),a("div",[e._t("default")],2)])},r=[]},b46e:function(e,t,a){},b52c:function(e,t,a){"use strict";a.r(t);var n=a("90a0"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},bd4d:function(e,t,a){"use strict";a("588d")},c7ab:function(e,t,a){"use strict";a.r(t);var n=a("f68a");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);var o,i,s=a("2877"),l=Object(s["a"])(n["default"],o,i,!1,null,null,null);t["default"]=l.exports},e47b:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"min-height":"800px",display:"flex","flex-direction":"column"}},[a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("el-row",[e.isPack?e._e():a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[a("el-tree-select",{ref:"treeSelectArea",staticClass:"treeselect",attrs:{"select-params":e.selectParams,styles:e.styles,"tree-params":e.treeParamsArea},on:{searchFun:function(t){return e.filterFun(t,"treeSelectArea")},"node-click":e.areaChange,"select-clear":e.areaClear},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1)],1),e.isPack?e._e():a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"批次",prop:"InstallUnit_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Area_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.InstallUnit_Id,callback:function(t){e.$set(e.form,"InstallUnit_Id",t)},expression:"form.InstallUnit_Id"}},e._l(e.SetupPositionData,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),e.isPack?a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"包编号",prop:"Code"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1)],1):a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"构件名称",prop:"Code"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),e.isPack?e._e():a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"构件类型",prop:"ComponentType"}},[a("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",staticStyle:{width:"100%"},attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},on:{removeTag:e.removeTagFn,searchFun:e._searchFun},model:{value:e.form.ComponentType,callback:function(t){e.$set(e.form,"ComponentType",t)},expression:"form.ComponentType"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(){e.form.PageInfo.Page=1,e.getPageList()}}},[e._v("查询")]),a("el-button",{on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")])],1)],1),a("el-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[a("el-form-item",[a("el-button",{attrs:{type:"primary",loading:e.addLoading,disabled:!e.selectList.length},on:{click:e.handleAdd}},[e._v("加入列表")])],1)],1)],1)],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",staticStyle:{"margin-left":"20px"},attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.form.PageInfo.Page,total:e.total,border:"",stripe:""},on:{gridPageChange:e.handlePageChange,gridSizeChange:e.handleSizeChange,multiSelectedChange:e.multiSelectedChange}})],1),a("span",{staticClass:"dialog-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",disabled:!e.selectList.length},on:{click:e.handleSubmit}},[e._v("添 加")])],1),a("check-info",{ref:"info"})],1)},r=[]},f5f7:function(e,t,a){"use strict";a("b46e")},f68a:function(e,t,a){"use strict";a.r(t);var n=a("0ce7"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a}}]);