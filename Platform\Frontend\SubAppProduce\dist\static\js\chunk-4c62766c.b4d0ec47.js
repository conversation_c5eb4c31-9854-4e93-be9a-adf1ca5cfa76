(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4c62766c"],{"031c":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("d3b7");var n=i(a("b775"));e.default={props:{unit:{type:Object,default:function(){return{}}}},data:function(){return{apis:{GetExternalList:"/sys/externalcompany/GetListByServiceDicDetail",ConfirmAssistFactory:"/PRO/OperationPlan/ConfirmAssistFactory",GetUserList:"/sys/user/GetUserList"},asists:[],asistsChargers:[],form:{factoryId:"",beginDate:"",endDate:"",userId:""}}},created:function(){var t=this;this.form.installId=this.unit.Id,this.form.userId=this.unit.PIC_UserId,this.GetExternalList({code:"factory_outsourcing_company"}).then((function(e){e.IsSucceed&&(t.asists=e.Data)})),this.getAsistsChargers().then((function(e){e.IsSucceed&&(t.asistsChargers=e.Data)}))},methods:{cancel:function(){this.$emit("dialogCancel")},setUserNmae:function(t){var e=this.asistsChargers.find((function(e){return e.Id===t}));e&&(this.form.userName=e.Display_Name)},confirmAssist:function(){var t=this;return this.form.installId?this.form.factoryId?this.form.beginDate?this.form.endDate?void(0,n.default)({url:this.apis.ConfirmAssistFactory,method:"post",params:this.form}).then((function(e){e.IsSucceed?(t.$emit("dialogFormSubmitSuccess",{type:"reload"}),t.$emit("dialogCancel")):t.$message.warning(e.Message)})):this.$message.warning("选择外协结束日期"):this.$message.warning("选择外协开始日期"):this.$message.warning("选择外协单位"):this.$message.warning("无效的安装单元")},GetExternalList:function(t){return(0,n.default)({url:this.apis.GetExternalList,method:"post",data:t})},getAsistsChargers:function(){return(0,n.default)({url:this.apis.GetUserList,method:"post",data:{DepartmentCode:"OUT_Director"}})}}}},"0339b":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"c-upload",staticStyle:{"margin-top":"-16px"}},[a("div",{staticClass:"tbox",staticStyle:{"flex-direction":"column"}},[a("el-row",{staticStyle:{width:"100%"},attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[t._v(" 1.选择项目 ")]),a("el-col",{attrs:{span:16}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:t.projSelectHandle},model:{value:t.formData.Project_Id,callback:function(e){t.$set(t.formData,"Project_Id",e)},expression:"formData.Project_Id"}},[a("el-option",{attrs:{label:"选择项目",value:""}}),t._l(t.projs,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})}))],2)],1)],1),a("el-row",{staticStyle:{width:"100%","margin-top":"16px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[t._v(" 安装单元名称 ")]),a("el-col",{attrs:{span:16}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.formData.InstallUnit_Id,callback:function(e){t.$set(t.formData,"InstallUnit_Id",e)},expression:"formData.InstallUnit_Id"}},[a("el-option",{attrs:{label:"选择安装单元",value:""}}),t._l(t.units,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})}))],2)],1)],1)],1),a("div",{staticClass:"tbox"},[a("p",[t._v("2.下载模板")]),a("el-button",{staticStyle:{border:"1px solid #D9DBE2",padding:"8px 20px"},attrs:{icon:"el-icon-document",type:"text"},on:{click:t.downTmpl}},[t._v("安装单元模板")])],1),a("div",{staticStyle:{background:"#F7F8F9",padding:"20px",border:"1px solid #D9DBE2"}},[a("div",{staticStyle:{"margin-bottom":"16px"}},[t._v(" 3.上传文件 ")]),a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:"",action:t.action,multiple:t.multiple,headers:t.reqHeader,accept:t.exts.map((function(t){return"."+t})).join(","),"file-list":t.fileList,"before-upload":t.beforeUpload,"on-progress":t.uploadProgressChange,"on-change":t.uploadStatusChange,"on-error":t.uploadError,"on-success":t.uploadSuccess,"auto-upload":!1,name:"files"},scopedSlots:t._u([{key:"file",fn:function(e){var i=e.file;return a("div",{},[a("div",{class:{"up-item":!0,error:"fail"===i.status}},[t.progresses[i.name]>0?a("div",{staticClass:"percent",style:{width:t.progresses[i.name]+"%"}}):t._e(),a("div",{staticClass:"bar"},[a("div",{staticClass:"title"},[a("svg-icon",{staticStyle:{height:"30px",width:"30px"},attrs:{name:t.extIcon(i),"icon-class":t.extIcon(i)}}),t._v(" "+t._s(i.name)+" ")],1),a("div",{staticClass:"remove"},["fail"===i.status?a("i",{staticClass:"el-icon-refresh-right",attrs:{title:"重新上传"},on:{click:function(e){return t.reUpload(i)}}}):t._e(),a("i",{staticClass:"el-icon-close",attrs:{title:"移除文件"},on:{click:function(e){return t.removeFile(i)}}})])])])])}}])},[a("svg-icon",{staticClass:"icon-svg",attrs:{name:"upload-icon","icon-class":"upload-icon",width:"200",height:"200"}}),a("div",{staticClass:"el-upload__text"},[t._v(" 将文件拖到此处，或"),a("em",[t._v("点击上传")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 支持格式："+t._s(t.exts.join("、"))+" ")])])],1)],1),a("div",{staticStyle:{"text-align":"right","margin-top":"20px"}},[a("el-button",{attrs:{size:"mini"},on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.beginUpload}},[t._v("导入")])],1)])},n=[]},"076a":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("159b"),a("ddb0");var n=i(a("2082")),s=i(a("0f97")),r=i(a("e622")),o=i(a("5ebe")),l=i(a("2429")),c=a("6186"),d=a("1b69"),u=i(a("b775")),f=a("2dd9"),p=a("ed08");e.default={name:"PROSchedulesOutsource",components:{DynamicDataTable:s.default,OutsourceSelect:r.default,CUpload:o.default,Process:l.default},mixins:[n.default],data:function(){return{apis:{AssistFactoryFinish:"/PRO/OperationPlan/AssistFactoryFinish",ImportFactoryFeedback:(0,p.combineURL)(this.$baseUrl,"/PRO/OperationPlan/ImportFactoryFeedback")},loading:!0,gridCode:"Assist_Plan_List",tbConfig:{},columns:[],data:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},addPageArray:[{path:this.$route.path+"/detail",hidden:!0,component:function(){return Promise.resolve().then(a.bind(null,"2429"))},name:"AssistDetail",meta:{title:"进度跟踪"}}],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0}}},created:function(){var t=this;Promise.all([(0,d.GetProjectList)({}).then((function(e){t.projs=null===e||void 0===e?void 0:e.Data})),(0,d.GetFactoryList)({}).then((function(e){t.factoryies=null===e||void 0===e?void 0:e.Data}))]).then((function(){(0,c.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData().then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1}))}))}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center"}),this.filterData.PageSize=this.tbConfig.Row_Number},setCols:function(t){var e=this;t.forEach((function(t){"Project_Name"===t.Code&&(t.Range=JSON.stringify(e.projs.map((function(t){return{label:t.Name,value:t.Name}}))))})),this.columns=t},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},getTableData:function(){return this.tbConfig.Data_Url?(0,u.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData)}):Promise.reject("invalid data api...")},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange()},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},filterChange:function(t){var e=this;this.filterData=Object.assign({},this.filterData,{Page:Number(t||1)}),this.filterData.ParameterJson=(0,f.setParameterJson)(this.fiterArrObj,this.columns),this.loading=!0,this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)})).finally((function(){e.loading=!1}))},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.PageSize=e,this.filterData.Page=1,this.filterChange()},handleCommand:function(t,e,a){var i=this;if(2!==a){var n={};switch(a.toString()){case"0":this.$router.push({name:"AssistDetail",query:{id:t.Id,pg_redirect:this.$route.name}});break;case"1":n.width="480px",n.title="导入更新",n.component="CUpload",n.props={action:this.apis.ImportFactoryFeedback},this.openDialog(n);break}}else this.$confirm('<div style="text-align:center;padding:24px;">确认完成后，发包方将收到完成消息。</div>',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",dangerouslyUseHTMLString:!0,type:"text"}).then((function(){i.assistFactoryFinish(t)})).catch((function(){i.$message({type:"info",message:"已取消"})}))},assistFactoryFinish:function(t){var e=this;(0,u.default)({url:this.apis.AssistFactoryFinish,method:"post",params:{installId:t.Id}}).then((function(t){t.IsSucceed&&(e.filterChange(e.filterData.Page),e.$message.success("确认完成"))}))},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=this,a=t.type;t.data;switch(a){case"reload":this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)}));break}},getStateColor:function(t){var e="";switch(t){case"未开始":e="#F1B430";break;case"进行中":e="#298DFF";break;case"已完成":e="#3ECC93";break}return e}}}},"0a2a":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");var n=i(a("b775")),s=(i(a("4328")),i(a("4360"))),r=a("5f87"),o=a("ed08");e.default={name:"CUpload",props:{action:{type:String,default:""},exts:{type:Array,default:function(){return[]}},tmpl:{type:Object,default:function(){return{}}},multiple:{type:Boolean,default:!1},filesize:{type:Number,default:5}},data:function(){return{apis:{GetProjectList:"/PRO/Project/GetProjectList",GetInstallUnitList:"/PRO/InstallUnit/GetInstallUnitList"},templateUrl:"",fileList:[],projs:[],units:[],progresses:{},reqHeader:null,formData:{Project_Id:"",InstallUnit_Id:""}}},created:function(){this.loadProjects(),this.reqHeader={},s.default.getters.token&&(this.reqHeader["Authorization"]=(0,r.getToken)()),s.default.getters.Last_Working_Object_Id&&(this.reqHeader.Last_Working_Object_Id=s.default.getters.Last_Working_Object_Id)},methods:{cancel:function(){this.$refs.upload.clearFiles(),this.fileList=[],this.$emit("dialogCancel")},beginUpload:function(){return this.formData.Project_Id?this.formData.InstallUnit_Id?(this.fileList=this.fileList.map((function(t){return t.status="ready",t})),void this.$refs.upload.submit()):this.$message.warning("选择安装单元"):this.$message.warning("选择项目")},reUpload:function(t){t.status="ready",this.$refs.upload.submit()},loadProjects:function(){var t=this;(0,n.default)({url:this.apis.GetProjectList,method:"post",data:{}}).then((function(e){e.IsSucceed&&(t.projs=e.Data)}))},projSelectHandle:function(t){t?this.loadInstallUnitsList():(this.units=[],this.formData.InstallUnit_Id="")},loadInstallUnitsList:function(){var t=this;(0,n.default)({url:this.apis.GetInstallUnitList,method:"post",data:{Project_Id:this.formData.Project_Id}}).then((function(e){e.IsSucceed&&e.IsSucceed&&(t.units=e.Data)}))},downTmpl:function(){var t=(0,o.combineURL)(this.$baseUrl,"/Template/PRO/外协工厂反馈导入模板.xlsx");window.open(t,"_blank")},beforeUpload:function(t){var e=t.name.split(".").pop(),a=!0;this.exts.length>0&&(a=this.exts.indexOf(e)>-1);var i=!0;return i=t.size/1024/1024<this.filesize,a||this.$message.error("上传文件格式错误!"),i||this.$message.error("上传文件大小不能超过 "+this.filesize+"MB!"),a&&i},uploadProgressChange:function(t,e,a){var i=this;this.progresses[e.name]=t.percent,100===t.percent&&setTimeout((function(){i.progresses[e.name]=0,i.progresses=Object.assign({},i.progresses)}),600)},uploadStatusChange:function(t,e){"ready"===t.status?this.fileList.push(t):"fail"===t.status&&(this.fileList=this.fileList.concat([]));var a=!0;this.fileList.forEach((function(t){"success"!==t.status&&(a=!1)})),a&&(this.$emit("dialogFormSubmitSuccess",{type:"reload",data:null}),this.cancel())},uploadError:function(t,e,a){},uploadSuccess:function(t,e,a){t.IsSucceed||(e.status="fail",this.$message.error(t.Message))},extIcon:function(t){var e="document_unknown_icon";switch(t.name.split(".").pop()){case"xls":case"xlsx":e="document_form_icon";break;case"txt":e="document_txt_icon";break;case"doc":case"docx":e="document_word_icon";break;case"zip":case"7z":case"rar":e="document_zip_icon";break;case"png":case"jpg":case"jpeg":case"gif":case"bmp":e="multimedia_image_icon";break;case"ppt":case"pptx":e="document_ppt_icon";break;case"pdf":e="document_pdf_icon";break}return e}}}},"0f330":function(t,e,a){"use strict";a("ec9d")},"222d":function(t,e,a){"use strict";a.r(e);var i=a("076a"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},2429:function(t,e,a){"use strict";a.r(e);var i=a("8216d"),n=a("f08d");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("0f330"),a("7a2c");var r=a("2877"),o=Object(r["a"])(n["default"],i["a"],i["b"],!1,null,"305d0686",null);e["default"]=o.exports},"2dd9":function(t,e,a){"use strict";function i(t,e){t||(t={}),e||(e=[]);var a=[],i=function(i){var n;n="[object Array]"===Object.prototype.toString.call(t[i])?t[i]:[t[i]];var s=n.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(s.filter((function(t){return null!==t})).length<=0&&(s=null),s){var r={Key:i,Value:s,Type:"",Filter_Type:""},o=e.find((function(t){return t.Code===i}));r.Type=null===o||void 0===o?void 0:o.Type,r.Filter_Type=null===o||void 0===o?void 0:o.Filter_Type,a.push(r)}};for(var n in t)i(n);return a}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=i,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("25f0")},"353f":function(t,e,a){"use strict";var i=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("b775")),s=i(a("0f97")),r=a("ed08");e.default={name:"Process",components:{DynamicDataTable:s.default},props:{row:{type:Object,default:function(){return{}}}},beforeRouteEnter:function(t,e,a){a((function(t){t.InstallUnit_Id=t.filterData.InstallUnit_Id=t.$route.query.id,t.getInstallUnitInfo().then((function(){t.getTableData().then((function(e){return e.IsSucceed&&(t.setGridData(e.Data),t.loading=!1),!0}))}))}))},data:function(){return{apis:{GetInstallUnitEntity:"/PRO/InstallUnit/GetInstallUnitEntity",GetFactoryFeedback:"/PRO/OperationPlan/GetFactoryFeedback"},drawerShow:!1,gridCode:"",columns:[],tbConfig:{},data:[],filterData:{},loading:!0,unitFullInfo:{},InstallUnit_Id:""}},created:function(){this.tbConfig={Data_Url:this.apis.GetFactoryFeedback,Display_Name:"外协进度跟踪",Height:0,Is_Auto_Width:!1,Is_Filter:!1,Width:0},this.columns=[{Align:"center",Code:"project_name",Display_Name:"项目名称",Filter_Type:null,Formatter:null,Type:"text",Width:160,Is_Display:!0},{Align:"center",Code:"type",Display_Name:"构件类型",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"code",Display_Name:"构件编号",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"unique_code",Display_Name:"唯一码",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"ultimate_demand_end_date",Display_Name:"需求时间",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"spec",Display_Name:"规格",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"netweight",Display_Name:"重量(kg)",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"length",Display_Name:"长度(mm)",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"production_status",Display_Name:"当前状态",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"workshop",Display_Name:"当前车间",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"process",Display_Name:"当前工序",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0},{Align:"center",Code:"work_group",Display_Name:"当前班组",Filter_Type:null,Formatter:null,Type:"text",Width:120,Is_Display:!0}]},methods:{getTableData:function(){return(0,n.default)({url:this.tbConfig.Data_Url,method:"post",params:{installId:this.InstallUnit_Id}})},setGridData:function(t){this.data=t},getInstallUnitInfo:function(){var t=this;return(0,n.default)({url:this.apis.GetInstallUnitEntity,method:"post",params:{id:this.InstallUnit_Id}}).then((function(e){if(!e.IsSucceed)return t.$message.warning(e.Message);t.unitFullInfo=e.Data}))},tableSearch:function(t){},tagBack:function(){(0,r.closeTagView)(this.$store,this.$route)},scrollTimeline:function(){this.$refs.scrollConatiner&&(this.$refs.scrollConatiner.scrollHeight<=this.$refs.scrollConatiner.offsetHeight||this.$refs.scrollConatiner.scrollTo({top:this.$refs.scrollConatiner.scrollTop+50}))},openDrawer:function(){}}}},"36b7":function(t,e,a){},"3bca":function(t,e,a){"use strict";a.r(e);var i=a("031c"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},"3ed4":function(t,e,a){"use strict";a.r(e);var i=a("0a2a"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},4135:function(t,e,a){"use strict";a.r(e);var i=a("a6c9"),n=a("222d");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("c5826");var r=a("2877"),o=Object(r["a"])(n["default"],i["a"],i["b"],!1,null,"5b75c940",null);e["default"]=o.exports},"4e82":function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),s=a("59ed"),r=a("7b0b"),o=a("07fa"),l=a("083a"),c=a("577e"),d=a("d039"),u=a("addb"),f=a("a640"),p=a("3f7e"),m=a("99f4"),h=a("1212"),g=a("ea83"),b=[],v=n(b.sort),_=n(b.push),y=d((function(){b.sort(void 0)})),C=d((function(){b.sort(null)})),D=f("sort"),I=!d((function(){if(h)return h<70;if(!(p&&p>3)){if(m)return!0;if(g)return g<603;var t,e,a,i,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(i=0;i<47;i++)b.push({k:e+i,v:a})}for(b.sort((function(t,e){return e.v-t.v})),i=0;i<b.length;i++)e=b[i].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),x=y||!C||!D||!I,S=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};i({target:"Array",proto:!0,forced:x},{sort:function(t){void 0!==t&&s(t);var e=r(this);if(I)return void 0===t?v(e):v(e,t);var a,i,n=[],c=o(e);for(i=0;i<c;i++)i in e&&_(n,e[i]);u(n,S(t)),a=o(n),i=0;while(i<a)e[i]=n[i++];while(i<c)l(e,i++);return e}})},"5ebe":function(t,e,a){"use strict";a.r(e);var i=a("0339b"),n=a("3ed4");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("7b94"),a("7435");var r=a("2877"),o=Object(r["a"])(n["default"],i["a"],i["b"],!1,null,"015e6683",null);e["default"]=o.exports},7435:function(t,e,a){"use strict";a("ee80")},"7a2c":function(t,e,a){"use strict";a("eaf6")},"7b94":function(t,e,a){"use strict";a("b804b")},"8216d":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),a("span",[t._v(t._s(t.unitFullInfo.Project_Name||"")+" / "+t._s(t.unitFullInfo.Name||""))])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"twrap"},[t.loading?t._e():a("DynamicDataTable",{attrs:{config:{Is_Filter:!1,Code:t.row.Code},border:"",columns:t.columns,data:t.data},on:{tableSearch:t.tableSearch},scopedSlots:t._u([{key:"unique_code",fn:function(e){var i=e.row,n=(e.$index,e.column);return[a("el-link",{attrs:{underline:!1,type:"primary"},on:{click:t.openDrawer}},[t._v(" "+t._s(i[n.Code])+" ")])]}}],null,!1,3074839332)})],1)]),a("el-drawer",{attrs:{title:"我是标题",visible:t.drawerShow,direction:"rtl",modal:!1},on:{"update:visible":function(e){t.drawerShow=e}}},[a("template",{slot:"title"},[a("strong",[t._v("构件历史")])]),a("div",{staticClass:"timeline-wrapper"},[a("div",{staticClass:"scroll-control",on:{click:t.scrollTimeline}},[a("i",{staticClass:"el-icon-d-arrow-left"})]),a("div",{staticClass:"qrcode"},[a("div",{staticClass:"info"},[a("h3",[t._v("成都自然博物馆")]),a("div",{staticClass:"spec"},[a("span",[t._v("钢梁-1")]),a("span",[t._v("GL-10")])]),a("div",{staticClass:"no"},[t._v("1458852")])]),a("div",{staticClass:"img"},[a("img",{attrs:{src:"",alt:""}})])]),a("div",{ref:"scrollConatiner",staticClass:"scroll"},[a("el-timeline",t._l([{content:"活动按期开始",timestamp:"2018-04-15",icon:"iconfont icon-node-filled",iconColor:"green"},{content:"通过审核",timestamp:"2018-04-13",icon:"iconfont icon-node-filled"},{content:"创建成功",timestamp:"2018-04-11",icon:"iconfont icon-node-filled"}],(function(e,i){return a("el-timeline-item",{key:i,attrs:{size:"normal",color:"#ffffff",timestamp:e.timestamp}},[a("template",{slot:"dot"},[a("i",{class:e.icon,style:{fontSize:"1.6em",marginLeft:"-6px",marginTop:"10px",color:e.iconColor}})]),t._v(" "+t._s(e.content)+" ")],2)})),1)],1)])],2)],1)},n=[]},"9283e":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"16px"}},[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"外协单位"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择单位"},model:{value:t.form.factoryId,callback:function(e){t.$set(t.form,"factoryId",e)},expression:"form.factoryId"}},t._l(t.asists,(function(t,e){return a("el-option",{key:e,attrs:{label:t.FullName,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{label:"外协开始时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期","suffix-icon":"el-icon-date"},model:{value:t.form.beginDate,callback:function(e){t.$set(t.form,"beginDate",e)},expression:"form.beginDate"}})],1),a("el-form-item",{attrs:{label:"外协结束时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期","suffix-icon":"el-icon-date"},model:{value:t.form.endDate,callback:function(e){t.$set(t.form,"endDate",e)},expression:"form.endDate"}})],1),a("el-form-item",{attrs:{label:"外协负责人"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择负责人"},on:{change:t.setUserNmae},model:{value:t.form.userId,callback:function(e){t.$set(t.form,"userId",e)},expression:"form.userId"}},t._l(t.asistsChargers,(function(t,e){return a("el-option",{key:e,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("el-form-item",{attrs:{align:"right"}},[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.confirmAssist}},[t._v("确定")])],1)],1)],1)},n=[]},a6c9:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return n}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 flex-pd16-wrap"},[a("div",{staticClass:"page-main-content  cs-z-shadow"},[a("el-container",{staticStyle:{height:"100%"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{padding:"0"}},[a("DynamicDataTable",{attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{tableSearch:t.tableSearch,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange},scopedSlots:t._u([{key:"State",fn:function(e){var i=e.row,n=e.column;return[a("span",[a("span",{staticClass:"dot",style:{background:t.getStateColor(i[n.Code])}}),t._v(t._s(i[n.Code])+" ")])]}},{key:"op",fn:function(e){var i=e.row,n=e.$index;return["未开始"===i["State"]?[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(e){return t.openDialog({title:"外协单位选择",width:"560px",component:"OutsourceSelect",props:{unit:i}})}}},[t._v("选择外协")])]:"进行中"===i["State"]?[a("el-dropdown",{attrs:{placement:"top-start",trigger:"click"},on:{command:function(e){return t.handleCommand(i,n,e)}}},[a("el-button",{attrs:{size:"large",type:"text",icon:"el-icon-s-operation"}}),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"0"}},[a("i",{staticClass:"el-icon-timer"}),t._v(" 进度跟踪 ")]),a("el-dropdown-item",{attrs:{command:"1"}},[a("i",{staticClass:"el-icon-thumb"}),t._v("导入更新")]),a("el-dropdown-item",{attrs:{command:"2"}},[a("i",{staticClass:"el-icon-document-checked"}),t._v("确认完成")])],1)],1)]:t._e()]}}])})],1)],1)],1),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",attrs:{name:t.dialogCfgs.title},on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},n=[]},b804b:function(t,e,a){},c5826:function(t,e,a){"use strict";a("36b7")},e622:function(t,e,a){"use strict";a.r(e);var i=a("9283e"),n=a("3bca");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);var r=a("2877"),o=Object(r["a"])(n["default"],i["a"],i["b"],!1,null,"782ff329",null);e["default"]=o.exports},eaf6:function(t,e,a){},ec9d:function(t,e,a){},ee80:function(t,e,a){},f08d:function(t,e,a){"use strict";a.r(e);var i=a("353f"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a}}]);