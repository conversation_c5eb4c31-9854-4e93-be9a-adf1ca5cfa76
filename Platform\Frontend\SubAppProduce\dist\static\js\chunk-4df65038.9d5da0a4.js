(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4df65038"],{"0218":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cs-z-flex-pd16-wrap x-container"},[a("top-header",{staticStyle:{padding:"0 8px","margin-bottom":"10px"},scopedSlots:e._u([{key:"left",fn:function(){return[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])]},proxy:!0},{key:"right",fn:function(){return[a("el-select",{attrs:{placeholder:"请选择仓库类型",clearable:""},model:{value:e.keywords,callback:function(t){e.keywords=t},expression:"keywords"}},[a("el-option",{attrs:{label:"成品仓库",value:"成品仓库"}}),a("el-option",{attrs:{label:"半成品仓库",value:"半成品仓库"}}),a("el-option",{attrs:{label:"辅料仓库",value:"辅料仓库"}}),a("el-option",{attrs:{label:"原材料仓库",value:"原材料仓库"}})],1),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查 询")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])]},proxy:!0}])}),a("div",{staticClass:"cs-main"},e._l(e.list,(function(t,n){return a("card",{key:n,attrs:{item:t},on:{delete:function(a){return e.handleDelete(t)},edit:e.handleEdit}})})),1),a("div",{staticClass:"cs-pagination-container"},[a("Pagination",{attrs:{total:e.total,page:e.listQuery.Page,limit:e.listQuery.PageSize,"page-sizes":e.tablePageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"Page",t)},"update:limit":function(t){return e.$set(e.listQuery,"PageSize",t)},pagination:e.fetchData}})],1),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:e.title,visible:e.dialogVisible,width:"40%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?a(e.currentComponent,{ref:"content",tag:"component",on:{close:e.handleClose,refresh:e.fetchData}}):e._e()],1):e._e()],1)},r=[]},"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=o,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=i(),s=e-o,l=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=l;var e=Math.easeInOutQuad(u,o,s,t);r(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},"2d48":function(e,t,a){"use strict";a.r(t);var n=a("376e"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"2e46":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteWarehouse=c,t.GetWarehoseTypeUnionWarehouseName=s,t.GetWarehouseEntity=d,t.GetWarehouseList=f,t.GetWarehouseListOfCurFactory=o,t.GetWarehousePageList=l,t.SaveWarehouse=u;var r=n(a("b775")),i=n(a("4328"));function o(e){return(0,r.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Warehouse/GetWarehoseTypeUnionWarehouseName",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Warehouse/GetWarehousePageList",method:"post",data:e})}function u(e){return(0,r.default)({url:"/PRO/Warehouse/SaveWarehouse",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Warehouse/DeleteWarehouse",method:"post",data:i.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/Warehouse/GetWarehouseEntity",method:"post",data:i.default.stringify(e)})}function f(e){return(0,r.default)({url:"/PRO/Warehouse/GetWarehouseList",method:"post",data:e})}},"2f1d":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var r=n(a("c14f")),i=n(a("1da1")),o=a("2e46"),s=a("5e99");t.default={data:function(){return{defaultDisabled:!1,btnLoading:!1,factory:[],form:{Is_Default:void 0,Factory_Id:"",Type:"",Display_Name:"",Capacity:"",Address:"",Remark:""},rules:{Type:[{required:!0,message:"请选择仓库类型",trigger:"change"}],Display_Name:[{required:!0,message:"请输入仓库名称",trigger:"blur"}],Factory_Id:[{required:!0,message:"请选择工厂",trigger:"change"}],Is_Default:[{required:!1,message:"请选择",trigger:"change"}]},ProfessionalType:[]}},mounted:function(){this.init()},methods:{typeChange:function(e){var t="辅料仓库"===e||"原材料仓库"===e;this.rules.Is_Default[0].required=t,this.defaultDisabled=!t,t||(this.form.Is_Default=void 0)},init:function(){var e=this;(0,s.GetCurFactory)({}).then((function(t){t.IsSucceed?e.factory=t.Data:e.$message({message:t.Message,type:"error"})}))},handleOpen:function(e){var t=this;return(0,i.default)((0,r.default)().m((function a(){var n,i,o,s,l,u,c,d,f;return(0,r.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,t.getDetail(e);case 1:n=a.v,i=n.Address,o=n.Factory_Id,s=n.Remark,l=n.Display_Name,u=n.Id,c=n.Capacity,d=n.Type,f=n.Is_Default,t.form.Factory_Id=o,t.form.Type=d,t.form.Display_Name=l,t.form.Remark=s,t.form.Address=i,t.form.Is_Default=f,t.form.Id=u,t.form.Capacity=c,t.typeChange(d);case 2:return a.a(2)}}),a)})))()},getDetail:function(e){var t=this;return new Promise((function(a){(0,o.GetWarehouseEntity)({id:e.Id}).then((function(e){e.IsSucceed?a(e.Data):t.$message({message:e.Message,type:"error"})}))}))},handleClose:function(){this.$emit("close")},handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.btnLoading=!0;var a=JSON.parse(JSON.stringify(t.form));a.Capacity=Number(a.Capacity),(0,o.SaveWarehouse)(a).then((function(e){e.IsSucceed?(t.$message({message:"操作成功",type:"success"}),t.btnLoading=!1,t.$emit("close"),t.$emit("refresh")):(t.$message({message:e.Message,type:"error"}),t.btnLoading=!1)}))}))}}}},"376e":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{item:{type:Object,default:function(){}}},methods:{handleEdit:function(e){this.$emit("edit",e)},handleDelete:function(e){this.$emit("delete",e)}}}},"3ece":function(e,t,a){"use strict";a.r(t);var n=a("d667"),r=a("2d48");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("8990");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"3090107e",null);t["default"]=s.exports},"59ea1":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"仓库类型",prop:"Type"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.typeChange},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[a("el-option",{attrs:{label:"成品仓库",value:"成品仓库"}}),a("el-option",{attrs:{label:"半成品仓库",value:"半成品仓库"}}),a("el-option",{attrs:{label:"辅料仓库",value:"辅料仓库"}}),a("el-option",{attrs:{label:"原材料仓库",value:"原材料仓库"}})],1)],1),a("el-form-item",{attrs:{label:"仓库名称",prop:"Display_Name"}},[a("el-input",{model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}})],1),a("el-form-item",{attrs:{label:"仓库容量",prop:"Capacity"}},[a("el-input",{attrs:{step:"any",type:"number",min:"0"},model:{value:e.form.Capacity,callback:function(t){e.$set(e.form,"Capacity",t)},expression:"form.Capacity"}})],1),a("el-form-item",{attrs:{label:"仓库所在位置",prop:"Address"}},[a("el-input",{model:{value:e.form.Address,callback:function(t){e.$set(e.form,"Address",t)},expression:"form.Address"}})],1),a("el-form-item",{attrs:{label:"是否默认仓库",prop:"Is_Default"}},[a("el-radio-group",{attrs:{disabled:e.defaultDisabled},model:{value:e.form.Is_Default,callback:function(t){e.$set(e.form,"Is_Default",t)},expression:"form.Is_Default"}},[a("el-radio",{attrs:{label:!0}},[e._v("是")]),a("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1),a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:50,type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},r=[]},6621:function(e,t,a){"use strict";a.r(t);var n=a("2f1d"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},8990:function(e,t,a){"use strict";a("b1b2")},"9cc3":function(e,t,a){"use strict";a.r(t);var n=a("0218"),r=a("f4a9");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("b1f7");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"a0778218",null);t["default"]=s.exports},a8ba:function(e,t,a){},b1b2:function(e,t,a){},b1f7:function(e,t,a){"use strict";a("a8ba")},cf0c:function(e,t,a){"use strict";a.r(t);var n=a("59ea1"),r=a("6621");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("f888");var o=a("2877"),s=Object(o["a"])(r["default"],n["a"],n["b"],!1,null,"3b95b19a",null);t["default"]=s.exports},cf17:function(e,t,a){},d667:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"card-wrapper"},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{"label-width":"90px"}},[a("el-form-item",{attrs:{label:"仓库名称："}},[a("strong",{staticClass:"cs-w60"},[e._v(e._s(e.item.Display_Name))])]),a("el-form-item",{attrs:{label:"仓库位置：",prop:"region"}},[a("strong",[e._v(e._s(e.item.Address||"-"))])]),a("el-form-item",{attrs:{label:"仓库类型：",prop:"region"}},[a("strong",[e._v(e._s(e.item.Type||"-"))])]),a("el-form-item",{staticClass:"cs-bz",attrs:{label:"备注："}},[a("span",[e._v(e._s(e.item.Remark||"-"))])]),a("el-form-item",{attrs:{label:"关联库位："}},[a("span",[e._v(e._s(e.item.Union||"-"))])]),"原材料仓库"===e.item.Type||"辅料仓库"===e.item.Type?a("el-form-item",{attrs:{label:"默认仓库：",prop:"Is_Default"}},[a("el-tag",{attrs:{type:e.item.Is_Default?"success":"danger"}},[e._v(e._s(e.item.Is_Default?"是":"否"))])],1):e._e()],1),a("div",{staticClass:"btn"},[a("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.handleDelete(e.item)}}},[e._v("删 除")]),a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.handleEdit(e.item)}}},[e._v("编 辑")])],1)],1)},r=[]},f4a9:function(e,t,a){"use strict";a.r(t);var n=a("fd7a"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},f888:function(e,t,a){"use strict";a("cf17")},fd7a:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("5530")),i=n(a("3ece")),o=n(a("cf0c")),s=a("2e46"),l=n(a("333d")),u=a("c685"),c=n(a("34e9")),d=a("5e99");t.default={name:"PROBasicWarehouse",components:{Card:i.default,AddEdit:o.default,Pagination:l.default,TopHeader:c.default},data:function(){return{tablePageSize:u.tablePageSize,total:0,listQuery:{PageSize:20,Page:1},value:"",title:"",list:[],currentComponent:"",dialogVisible:!1,factory:[],keywords:""}},mounted:function(){this.getBaseData(),this.fetchData()},methods:{getBaseData:function(){var e=this;(0,d.GetCurFactory)({}).then((function(t){t.IsSucceed?e.factory=t.Data:e.$message({message:t.Message,type:"error"})}))},fetchData:function(){var e=this;(0,s.GetWarehousePageList)((0,r.default)((0,r.default)({},this.listQuery),{},{type:this.keywords})).then((function(t){t.IsSucceed?(e.list=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},handleReset:function(){this.keywords="",this.fetchData()},handleAdd:function(){this.openDialog(!1)},handleEdit:function(e){this.openDialog(!0,e)},openDialog:function(e,t){var a=this;this.currentComponent="AddEdit",this.title=e?"编辑":"新增",this.dialogVisible=!0,this.$nextTick((function(n){e&&a.$refs["content"].handleOpen(t)}))},handleClose:function(){this.dialogVisible=!1},handleDelete:function(e){var t=this;this.$confirm("是否删除该仓库?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.DeleteWarehouse)({ids:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))}))}}}}}]);