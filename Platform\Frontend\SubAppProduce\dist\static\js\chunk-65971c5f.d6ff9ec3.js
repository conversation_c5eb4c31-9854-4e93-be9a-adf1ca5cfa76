(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-65971c5f"],{"15c0":function(t,e,i){"use strict";i.r(e);var a=i("3d65"),r=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(n);e["default"]=r.a},"3d65":function(t,e,i){"use strict";var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=a(i("c14f")),n=a(i("1da1"));i("4de4"),i("7db0"),i("c740"),i("e9f5"),i("910d"),i("f665"),i("7d54"),i("e9c4"),i("b64b"),i("d3b7"),i("159b");var s,l=i("0f64"),d=a(i("96739")),o=i("7e18"),c=i("84e1"),p=(i("4744"),a(i("c0e9"))),u=i("2d91"),m=i("ed08");l.hiPrintPlugin.disAutoConnect();var f=null;e.default={name:"ShipTemplatePrintDetail",data:function(){return{curPaper:{type:"自定义纸张",width:100,height:60},curPaperType:"自定义纸张",paperTypes:(0,m.deepClone)(c.paperTypes),paperWidth:"100",paperHeight:"60",tmplList:[],mode:this.$route.query.mode,activeIndex:"",keyword:"",toEdit:"",form:{Name:"",Type:this.$route.query.mode,Data:"",Base64Image:""},logoUrl:i("f8ae"),scaleValue:1,scaleMax:5,scaleMin:.5,curImageId:"",imageObj:{}}},computed:{filteredTmplList:function(){var t=this;return this.tmplList.filter((function(e){return e.Name.indexOf(t.keyword)>-1}))}},mounted:function(){this.init(),this.buildLeftElement(),this.buildDesigner()},beforeDestroy:function(){clearInterval(f)},methods:{cloneTemplate:function(){var t=this;return(0,n.default)((0,r.default)().m((function e(){var i;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return i=JSON.parse(JSON.stringify(t.form)),delete i.Id,e.n=1,(0,o.SavePrintTemplateEntity)(i).then((function(e){e.IsSucceed?(t.$message.success("复制成功"),t.getTemplateList()):t.$message.error(e.Message)}));case 1:return e.a(2)}}),e)})))()},handleFileChange:function(t){var e=this,i=t.target.files[0];if(i){var a=new FileReader;a.onload=function(t){e.imageObj[e.curImageId]=t.target.result},a.readAsDataURL(i)}},changeScale:function(t){var e=this.scaleValue;t?(e+=.1,e>this.scaleMax&&(e=5)):(e-=.1,e<this.scaleMin&&(e=.5)),s&&(s.zoom(e),this.scaleValue=e)},getImage:function(){return(0,n.default)((0,r.default)().m((function t(){var e,i;return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return document.querySelectorAll(".hiprint-printElement-text-content").forEach((function(t){t.style.border=window.getComputedStyle(t).border})),document.querySelectorAll(".hiprint-printPaper.design").forEach((function(t){t.style.border=window.getComputedStyle(t).border})),document.querySelectorAll(".hiprint-printElement-text-content").forEach((function(t){t.style.border="none"})),document.querySelectorAll(".hiprint-printPaper.design").forEach((function(t){t.style.border="none"})),t.n=1,(0,p.default)(document.getElementsByClassName("hiprint-printPaper")[0],{useCORS:!0});case 1:return e=t.v,document.querySelectorAll(".hiprint-printElement-text-content").forEach((function(t,e){t.style.border=t.getAttribute("data-original-border")||"",t.removeAttribute("data-original-border")})),document.querySelectorAll(".hiprint-printPaper.design").forEach((function(t,e){t.style.border=t.getAttribute("data-original-border")||"",t.removeAttribute("data-original-border")})),i=e.toDataURL(),t.a(2,i)}}),t)})))()},getLogo:function(){var t=this;(0,u.GetCompany)().then((function(e){t.logoUrl=e.Data.Icon}))},tmplSelect:function(t){this.toEdit="",this.activeIndex=t,this.form&&this.form.Id===t||this.loadTemplate(t)},deleteTemplate:function(t){var e=this;this.$confirm("是否删除所选内容","提示",{confirmButtonText:"确定",cancelButtonText:"取消",center:!0}).then((function(){(0,o.DeletePrintTemplate)({id:t}).then((function(t){t.IsSucceed?(e.$message.success("删除成功"),e.getTemplateList()):e.$message.error(t.Message)})),e.toEdit&&(e.toEdit="")})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},init:function(){var t=this;return(0,n.default)((0,r.default)().m((function e(){var i;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:t.getLogo(),i=d.default.find((function(e){return e.value==t.mode})),l.hiprint.init({providers:[i.f]}),t.getTemplateList();case 1:return e.a(2)}}),e)})))()},buildLeftElement:function(){l.hiprint.PrintElementTypeManager.buildByHtml($(".ep-draggable-item"))},buildDesigner:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};$("#hiprint-printTemplate").empty(),s=new l.hiprint.PrintTemplate({template:e,settingContainer:"#PrintElementOptionSetting",onImageChooseClick:function(e){t.curImageId=e.el.id,clearInterval(f),t.$refs.imageUploader.click(),f=setInterval((function(){e.refresh(t.imageObj[e.el.id])}),100)}}),s.design("#hiprint-printTemplate"),s.setPaper(this.paperWidth,this.paperHeight)},handlePrint:function(){var t={},e=s.getJson().panels[0].printElements;e.forEach((function(e){"Table"==e.options.field?t[e.options.field]=JSON.parse(e.options.testData):t[e.options.field]=e.options.testData||e.options}));var i={leftOffset:-1,topOffset:-1},a={callback:function(){}};s.print(t,i,a)},changePaper:function(){var t=this,e=this.paperTypes.find((function(e){return e.type===t.curPaperType}));e=(0,m.deepClone)(e),"自定义纸张"===this.curPaperType?s.setPaper(this.paperWidth,this.paperHeight):s.setPaper(e.width,e.height)},createTemplate:function(){this.form={Name:"",Type:this.mode,Data:"",Base64Image:""},this.clearTemplate()},saveTemplate:function(){var t=this;return(0,n.default)((0,r.default)().m((function e(){var i;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,t.getImage();case 1:if(t.form.Base64Image=e.v,t.form.Name){e.n=2;break}return t.$message.error("请输入模板名称"),e.a(2);case 2:i=s.getJson(),t.form.Data=JSON.stringify(i),(0,o.SavePrintTemplateEntity)(t.form).then((function(e){e.IsSucceed?(t.$message.success("保存成功"),t.getTemplateList()):t.$message.error(e.Message)}));case 3:return e.a(2)}}),e)})))()},loadTemplate:function(t){var e=this;return(0,n.default)((0,r.default)().m((function i(){var a,n,s,l,d,c,p,u;return(0,r.default)().w((function(i){while(1)switch(i.n){case 0:return e.clearTemplate(),i.n=1,(0,o.GetPrintTemplateEntity)({id:t});case 1:a=i.v,e.form=a.Data,n=JSON.parse(a.Data.Data);try{s=n.panels[0].printElements.findIndex((function(t){return"Logo"===t.options.field})),n.panels[0].printElements[s].options.src=e.logoUrl}catch(r){}l=n,e.buildDesigner(l),d=l.panels[0],c=d.width,p=d.height,u=e.paperTypes.find((function(t){return t.width==c&t.height==p})),e.curPaper=u||{type:"自定义纸张",width:c,height:p},e.curPaperType=e.curPaper.type,e.paperWidth=c,e.paperHeight=p,e.changePaper();case 2:return i.a(2)}}),i)})))()},clearTemplate:function(){$("#hiprint-printTemplate").empty(),this.buildDesigner()},getTemplateList:function(){var t=this;return(0,n.default)((0,r.default)().m((function e(){var i;return(0,r.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,o.GetPrintTemplateList)({type:t.mode});case 1:i=e.v,t.tmplList=i.Data;case 2:return e.a(2)}}),e)})))()}}}},6991:function(t,e,i){"use strict";i("95d6")},"755d":function(t,e,i){"use strict";i.r(e);var a=i("91e5"),r=i("15c0");for(var n in r)["default"].indexOf(n)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(n);i("6991");var s=i("2877"),l=Object(s["a"])(r["default"],a["a"],a["b"],!1,null,"277b501a",null);e["default"]=l.exports},"84e1":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.paperTypes=void 0;e.paperTypes=[{type:"A3",width:420,height:296.6},{type:"A4",width:210,height:296.6},{type:"A5",width:210,height:147.6},{type:"B3",width:500,height:352.6},{type:"B4",width:250,height:352.6},{type:"B5",width:250,height:175.6},{type:"自定义纸张",width:100,height:60}]},"91e5":function(t,e,i){"use strict";i.d(e,"a",(function(){return a})),i.d(e,"b",(function(){return r}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[i("div",{staticStyle:{display:"flex",height:"100%"}},[i("el-aside",{staticClass:"cs-z-page-main-content",staticStyle:{background:"#FFF","margin-right":"16px",width:"20vw","min-width":"320px"}},[i("el-row",{staticStyle:{"flex-shrink":"0"},attrs:{gutter:4}},[i("el-col",{attrs:{span:17}},[i("el-input",{attrs:{placeholder:"请输入内容","suffix-icon":"el-icon-search"},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),i("el-col",{attrs:{span:7}},[i("el-button",{attrs:{type:"primary"},on:{click:t.createTemplate}},[t._v("新建模板")])],1)],1),i("div",{staticClass:"tmpl-list"},[i("el-menu",{staticClass:"tmpl-menu",attrs:{"default-active":String(t.activeIndex)}},t._l(t.filteredTmplList,(function(e){return i("el-menu-item",{key:e.Id,staticStyle:{"padding-left":"12px"},attrs:{index:e.Id,title:e.Name}},[i("div",{staticStyle:{overflow:"hidden","max-width":"220px","text-overflow":"ellipsis"},on:{click:function(i){return i.stopPropagation(),t.tmplSelect(e.Id)}}},[i("i",{staticClass:"el-icon-document"}),t._v(t._s(e.Name)+" ")]),String(t.activeIndex)===e.Id?[i("el-link",{attrs:{underline:!1,type:"danger"},on:{click:function(i){return t.deleteTemplate(e.Id)}}},[i("i",{staticClass:"right-align-icon el-icon-delete"})]),i("el-link",{attrs:{underline:!1,type:"primary"},on:{click:function(i){return t.cloneTemplate(e.Id)}}},[i("i",{staticClass:"right-align-icon el-icon-copy-document"})])]:t._e()],2)})),1)],1),2==t.mode?i("div",{staticClass:"flex-row justify-center flex-wrap",staticStyle:{display:"flex","flex-wrap":"wrap"}},[i("div",{staticClass:"title"},[t._v("条码区")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.customImage"}},[t._v(" 图片 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.Barcode_Url"}},[t._v(" 二维码 ")]),i("div",{staticClass:"title"},[t._v("数据区")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.ProjectName"}},[t._v(" 项目名称 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.AreaPosition"}},[t._v(" 区域 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SetupPosition"}},[t._v(" 批次 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SteelType"}},[t._v(" 构件类型 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SteelName"}},[t._v(" 构件名称 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SteelAmount"}},[t._v(" 数量 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.IsDirect"}},[t._v(" 是否是直发件 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SteelWeight"}},[t._v(" 单重（kg） ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.GrossWeight"}},[t._v(" 单毛重（kg） ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SteelSpec"}},[t._v(" 规格 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SteelMaterial"}},[t._v(" 材质 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.Area"}},[t._v(" 面积 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SteelLength"}},[t._v(" 长度（mm，含连接件） ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.SerialNumber"}},[t._v(" 管理编号 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.Axis"}},[t._v(" 位置轴线 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.TopHeight"}},[t._v(" 顶标高（m） ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.BottomHeight"}},[t._v(" 底标高（m） ")]),i("div",{staticClass:"title"},[t._v("其他")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.customText"}},[t._v(" 自定义文本 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.hline"}},[t._v(" 横线 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.vline"}},[t._v(" 竖线 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.rect"}},[t._v(" 矩形 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.oval"}},[t._v(" 椭圆 ")])]):t._e(),3==t.mode?i("div",{staticClass:"flex-row justify-center flex-wrap",staticStyle:{display:"flex","flex-wrap":"wrap"}},[i("div",{staticClass:"title"},[t._v("条码区")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.customImage"}},[t._v(" 图片 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.Barcode_Url"}},[t._v(" 二维码 ")]),i("div",{staticClass:"title"},[t._v("数据区")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.ProjectName"}},[t._v(" 项目名称 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.PackageSN"}},[t._v(" 包编号 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.AllWeight"}},[t._v(" 总重（t） ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.Remark"}},[t._v(" 备注 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.PNum"}},[t._v(" 数量 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.PkgNo"}},[t._v(" 包名称 ")]),i("div",{staticClass:"title"},[t._v("其他")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.customText"}},[t._v(" 自定义文本 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.hline"}},[t._v(" 横线 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.vline"}},[t._v(" 竖线 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.rect"}},[t._v(" 矩形 ")]),i("div",{staticClass:"ep-draggable-item item",attrs:{tid:"Barcode.oval"}},[t._v(" 椭圆 ")])]):t._e()],1),i("el-container",{staticClass:"cs-z-page-main-content"},[i("div",{staticClass:"header"},[i("el-button",{attrs:{type:"primary",sizi:"mini"},on:{click:t.handlePrint}},[t._v("打印预览")]),i("el-button",{attrs:{type:"success",sizi:"mini"},on:{click:t.saveTemplate}},[t._v("保存模板")]),i("el-button",{attrs:{type:"danger",sizi:"mini"},on:{click:t.clearTemplate}},[t._v("清空")]),i("span",{staticClass:"label"},[t._v("模板名称")]),i("el-input",{staticStyle:{width:"150px"},attrs:{maxlength:50},model:{value:t.form.Name,callback:function(e){t.$set(t.form,"Name",e)},expression:"form.Name"}}),i("span",{staticClass:"label"},[t._v("模板布局")]),i("el-select",{staticStyle:{width:"120px"},on:{change:t.changePaper},model:{value:t.curPaperType,callback:function(e){t.curPaperType=e},expression:"curPaperType"}},t._l(t.paperTypes,(function(t){return i("el-option",{key:t.type,attrs:{value:t.type,label:t.type}})})),1),"自定义纸张"===t.curPaperType?i("div",[i("span",{staticClass:"label"},[t._v("宽")]),i("el-input",{staticClass:"input",attrs:{type:"input"},on:{change:t.changePaper},model:{value:t.paperWidth,callback:function(e){t.paperWidth=e},expression:"paperWidth"}}),i("span",{staticClass:"label"},[t._v("高")]),i("el-input",{staticClass:"input",attrs:{type:"input"},on:{change:t.changePaper},model:{value:t.paperHeight,callback:function(e){t.paperHeight=e},expression:"paperHeight"}})],1):t._e(),i("div",{staticStyle:{display:"flex","align-items":"center","margin-left":"10px"}},[i("i",{staticClass:"el-icon-zoom-out zoom-btn",on:{click:function(e){return t.changeScale(!1)}}}),i("div",{staticClass:"zoom"},[t._v(t._s(~~(100*t.scaleValue))+"%")]),i("i",{staticClass:"el-icon-zoom-in zoom-btn",on:{click:function(e){return t.changeScale(!0)}}})])],1),i("div",{staticStyle:{"margin-top":"10px",display:"flex"}},[i("div",{staticStyle:{flex:"1","padding-left":"16px","padding-top":"16px",overflow:"auto"}},[i("div",{attrs:{id:"hiprint-printTemplate"}})]),i("div",{staticClass:"hinnn-layout-sider",staticStyle:{width:"20vw","min-width":"300px","margin-left":"16px"}},[i("div",{attrs:{id:"PrintElementOptionSetting"}})])])])],1),i("input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"imageUploader",attrs:{type:"file",accept:"image/*"},on:{change:t.handleFileChange}})])},r=[]},"95d6":function(t,e,i){},96739:function(t,e,i){"use strict";var a=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.PackageProvider=e.BarcodeProvider=void 0;var r=a(i("c14f")),n=a(i("1da1")),s=i("0f64"),l=(i("4744"),i("2d91"),e.BarcodeProvider=function(t){var e=function(){var t=(0,n.default)((0,r.default)().m((function t(e){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.removePrintElementTypes("Barcode"),e.addPrintElementTypes(1,[new s.hiprint.PrintElementTypeGroup("平台",[{tid:"Barcode.customImage",title:"图片",data:"",type:"image",options:{height:40,width:40}},{tid:"Barcode.Barcode_Url",title:"二维码",data:"XS888888888",type:"text",options:{field:"Barcode_Url",testData:"XS888888888",height:64,fontSize:9,lineHeight:18,textType:"qrcode"}},{tid:"Barcode.ProjectName",title:"项目名称",data:"XXX项目",type:"text",options:{testData:"XXX项目",field:"ProjectName",fontSize:9,width:120}},{tid:"Barcode.AreaPosition",title:"区域",data:"1-1#楼九节柱",type:"text",options:{testData:"1-1#楼九节柱",fontSize:9,field:"AreaPosition"}},{tid:"Barcode.SetupPosition",title:"批次",data:"1",type:"text",options:{field:"SetupPosition",testData:"1",fontSize:9}},{tid:"Barcode.SteelType",title:"构件类型",data:"XGB",type:"text",options:{field:"SteelType",testData:"XGB",fontSize:9}},{tid:"Barcode.SteelName",title:"构件名称",data:"GL2-1",type:"text",options:{field:"SteelName",testData:"GL2-1",fontSize:9,width:120}},{tid:"Barcode.SteelAmount",title:"数量",data:1,type:"text",options:{field:"SteelAmount",testData:1,fontSize:9}},{tid:"Barcode.IsDirect",title:"是否是直发件",data:"是",type:"text",options:{field:"IsDirect",testData:"是",fontSize:9}},{tid:"Barcode.SteelWeight",title:"单重（kg）",data:"142.98",type:"text",options:{field:"SteelWeight",testData:"142.98",fontSize:9}},{tid:"Barcode.GrossWeight",title:"单毛重（kg）",data:"154.1",type:"text",options:{field:"GrossWeight",testData:"154.1",fontSize:9}},{tid:"Barcode.SteelSpec",title:"规格",data:"3*5*8",type:"text",options:{field:"SteelSpec",testData:"3*5*8",fontSize:9}},{tid:"Barcode.SteelMaterial",title:"材质",data:"Q235B",type:"text",options:{field:"SteelMaterial",testData:"Q235B",fontSize:9,width:120}},{tid:"Barcode.Area",title:"面积",data:"125.68",type:"text",options:{field:"Area",testData:"125.68",fontSize:9,width:120}},{tid:"Barcode.SteelLength",title:"长度（mm，含连接件）",data:"125",type:"text",options:{field:"SteelLength",testData:"125",fontSize:9,width:140}},{tid:"Barcode.SerialNumber",title:"管理编号",data:"1/1",type:"text",options:{field:"SerialNumber",testData:"1/1",fontSize:9,width:120}},{tid:"Barcode.Axis",title:"位置轴线",data:"1~2轴交A~B轴",type:"text",options:{field:"Axis",testData:"1~2轴交A~B轴",fontSize:9,width:20}},{tid:"Barcode.TopHeight",title:"顶标高（m）",data:"*****",type:"text",options:{field:"TopHeight",testData:"*****",fontSize:9,width:120}},{tid:"Barcode.BottomHeight",title:"底标高（m）",data:"-2.34",type:"text",options:{field:"BottomHeight",testData:"-2.34",fontSize:9,width:120}},{tid:"Barcode.customText",title:"自定义文本",customText:"自定义文本",custom:!0,type:"text"}]),new s.hiprint.PrintElementTypeGroup("辅助",[{tid:"Barcode.hline",title:"横线",type:"hline",options:{field:"hline"}},{field:"vline",tid:"Barcode.vline",title:"竖线",type:"vline",options:{field:"vline"}},{field:"rect",tid:"Barcode.rect",title:"矩形",type:"rect",options:{field:"rect"}},{field:"oval",tid:"Barcode.oval",title:"椭圆",type:"oval",options:{field:"oval"}}])]);case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}();return{addElementTypes:e}}),d=e.PackageProvider=function(t){var e=function(){var t=(0,n.default)((0,r.default)().m((function t(e){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:e.removePrintElementTypes("Barcode"),e.addPrintElementTypes(1,[new s.hiprint.PrintElementTypeGroup("平台",[{tid:"Barcode.customImage",title:"图片",data:"",type:"image",options:{height:40,width:40}},{tid:"Barcode.Barcode_Url",title:"二维码",data:"XS888888888",type:"text",options:{field:"Barcode_Url",testData:"XS888888888",height:64,fontSize:9,lineHeight:18,textType:"qrcode"}},{tid:"Barcode.ProjectName",title:"项目名称",data:"XXX项目",type:"text",options:{testData:"XXX项目",field:"ProjectName",fontSize:9,width:120}},{tid:"Barcode.PackageSN",title:"包编号",data:"PKG00001",type:"text",options:{testData:"PKG00001",fontSize:9,field:"PackageSN"}},{tid:"Barcode.AllWeight",title:"总重（t）",data:"563.22",type:"text",options:{field:"AllWeight",testData:"5.623",fontSize:9}},{tid:"Barcode.Remark",title:"备注",data:"备注内容",type:"text",options:{field:"Remark",testData:"备注内容",fontSize:9}},{tid:"Barcode.PNum",title:"数量",data:"5",type:"text",options:{field:"PNum",testData:"5",fontSize:9,width:120}},{tid:"Barcode.PkgNo",title:"包名称",data:"S3区屋面",type:"text",options:{field:"PkgNo",testData:"S3区屋面",fontSize:9}},{tid:"Barcode.GoodsName",title:"类型",data:"GXB",type:"text",options:{field:"GoodsName",testData:"GXB",fontSize:9}},{tid:"Barcode.customText",title:"自定义文本",customText:"自定义文本",custom:!0,type:"text"}]),new s.hiprint.PrintElementTypeGroup("辅助",[{tid:"Barcode.hline",title:"横线",type:"hline",options:{field:"hline"}},{field:"vline",tid:"Barcode.vline",title:"竖线",type:"vline",options:{field:"vline"}},{field:"rect",tid:"Barcode.rect",title:"矩形",type:"rect",options:{field:"rect"}},{field:"oval",tid:"Barcode.oval",title:"椭圆",type:"oval",options:{field:"oval"}}])]);case 1:return t.a(2)}}),t)})));return function(e){return t.apply(this,arguments)}}();return{addElementTypes:e}};e.default=[{name:"构件条码",value:2,f:l()},{name:"打包件条码",value:3,f:d()}]}}]);