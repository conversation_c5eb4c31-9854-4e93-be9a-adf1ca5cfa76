(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-cd7a8712"],{"09f4":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=l,Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(e,t,a){var l=o(),i=e-l,s=20,d=0;t="undefined"===typeof t?500:t;var c=function(){d+=s;var e=Math.easeInOutQuad(d,l,i,t);r(e),d<t?n(c):a&&"function"===typeof a&&a()};c()}},"35f6":function(e,t,a){"use strict";a("dc64")},"3ed6":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("2909")),o=n(a("5530"));a("99af"),a("4de4"),a("c740"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var l=a("6186"),i=a("20ff"),s=a("77a7"),d=a("f382"),c=a("ed08");t.default={data:function(){return{title:"",dialogVisible:!1,btnLoading:!1,type:"",platformOption:[],form:{Name:"",Model_Name:"",Web_Id:"",Platform:"",Tb_Name:"",Description:"",Is_Attachment:!1,Is_Use_Private:!1,Fk_Field:"",Update_Url:"",Apply_Type:0},selectParams:{multiple:!1,clearable:!1,placeholder:"请选择"},treeParams:{data:[],filterable:!1,clickParent:!0,checkStrictly:!0,defaultExpandAll:!0,includeHalfChecked:!0,showCheckbox:!0,props:{children:"Children",label:"Label",value:"Id"}},selectCheckArray:[],modelOption:[],rules:{Name:[{required:!0,message:"请输入名称",trigger:"blur"}],Web_Id:[{required:!0,message:"页面表单",trigger:"blur"}],Model_Name:[{required:!0,message:"请选择表单Model",trigger:"change"}],Apply_Type:[{required:!0,message:"请选择流程类型",trigger:"change"}]}}},methods:{currentModelChange:function(e){var t=this.modelOption.filter((function(t){return t.Model_Name===e}));this.form.Platform=t[0].Platform,this.form.Tb_Name=t[0].Table,this.form.Update_Url=t[0].Update_Url,this.form.Fk_Field=t[0].PrimaryKey},currentPlatformChange:function(e){},handleOpen:function(e,t){var a=this;this.dialogVisible=!0,this.type=e,"add"===this.type?(this.title="添加",this.$delete(this.form,"Id")):this.$nextTick((function(e){a.title="编辑";var n=t.Id,r=t.Name,o=t.Model_Name,l=t.Web_Id,i=t.Platform,s=t.Tb_Name,d=t.Description,c=t.Is_Attachment,u=t.Is_Use_Private,f=t.Fk_Field,m=t.Update_Url,p=t.Apply_Type;a.form={Id:n,Name:r,Model_Name:o,Web_Id:l,Platform:i,Tb_Name:s,Fk_Field:f,Update_Url:m,Description:d,Is_Attachment:c,Is_Use_Private:u,Apply_Type:p||0}})),this.getModelOption(),(0,l.GetDictionaryDetailListByCode)({dictionaryCode:"platform"}).then((function(e){a.platformOption=e.Data}))},fetchTreeData:function(e){var t=this;(0,s.GetWorkingObjectTree)({model:{Platform:e}}).then((function(e){e.IsSucceed&&(t.treeParams.data=e.Data,t.$refs.treeSelect.treeDataUpdateFun(e.Data))}))},getModelOption:function(){var e=this;(0,i.FormsGetFromModel)().then((function(t){e.modelOption=t.Data}))},handleClose:function(){this.resetForm("form")},handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var a=(0,o.default)({},t.form);t.btnLoading=!0,"add"===t.type&&(0,i.FormsAdd)(a).then((function(e){t.resetForm("form"),t.$message({message:"添加成功",type:"success"}),t.$emit("update")})),"edit"===t.type&&(0,i.FormsUpdate)(a).then((function(e){t.resetForm("form"),t.$message({message:"修改成功",type:"success"}),t.$emit("update")})),t.btnLoading=!1}))},resetForm:function(e){this.$refs[e].resetFields(),this.platformOption=[],this.dialogVisible=!1},handleCheck:function(e,t,a){var n=this,o=this.form.Working_Object_Id.findIndex((function(t){return t===e.Id}));-1!==o&&this.form.Working_Object_Id.splice(o,1),t.checkedKeys.forEach((function(e){var t;(t=n.selectCheckArray).push.apply(t,(0,r.default)((0,d.findAllParentNode)(n.treeParams.data,e).map((function(e){return e.Id}))).concat([e]))})),this.selectCheckArray=(0,c.uniqueArr)(this.selectCheckArray),this.$nextTick((function(e){n.$set(n.form,"Working_Object_Id",n.selectCheckArray)}))}}}},"4ac3":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.dialogVisible,width:"30%"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"名称",prop:"Name"}},[a("el-input",{model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),a("el-form-item",{attrs:{label:"页面表单",prop:"Web_Id"}},[a("el-input",{model:{value:e.form.Web_Id,callback:function(t){e.$set(e.form,"Web_Id",t)},expression:"form.Web_Id"}})],1),a("el-form-item",{attrs:{label:"表单模型",prop:"Model_Name"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.currentModelChange},model:{value:e.form.Model_Name,callback:function(t){e.$set(e.form,"Model_Name",t)},expression:"form.Model_Name"}},e._l(e.modelOption,(function(e){return a("el-option",{key:e.Model_Name,attrs:{label:e.Description,value:e.Model_Name}})})),1)],1),a("el-form-item",{attrs:{label:"表单类型",prop:"Apply_Type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.Apply_Type,callback:function(t){e.$set(e.form,"Apply_Type",t)},expression:"form.Apply_Type"}},[a("el-option",{attrs:{label:"业务表单",value:0}}),a("el-option",{attrs:{label:"通用表单",value:1}})],1)],1),a("el-form-item",{attrs:{label:"平台",prop:"Platform"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:""},on:{change:e.currentPlatformChange},model:{value:e.form.Platform,callback:function(t){e.$set(e.form,"Platform",t)},expression:"form.Platform"}},e._l(e.platformOption,(function(e){return a("el-option",{key:e.Value,attrs:{label:e.Display_Name,value:e.Value}})})),1)],1),a("el-form-item",{attrs:{label:"业务数据库表名",prop:"Tb_Name"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Tb_Name,callback:function(t){e.$set(e.form,"Tb_Name",t)},expression:"form.Tb_Name"}})],1),a("el-form-item",{attrs:{label:"主键",prop:"Fk_Field"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Fk_Field,callback:function(t){e.$set(e.form,"Fk_Field",t)},expression:"form.Fk_Field"}})],1),a("el-form-item",{attrs:{label:"回调路径",prop:"Update_Url"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.Update_Url,callback:function(t){e.$set(e.form,"Update_Url",t)},expression:"form.Update_Url"}})],1),a("el-form-item",{attrs:{label:"摘要",prop:"Description"}},[a("el-input",{attrs:{autosize:{minRows:3,maxRows:5},"show-word-limit":"",maxlength:"50",type:"textarea"},model:{value:e.form.Description,callback:function(t){e.$set(e.form,"Description",t)},expression:"form.Description"}})],1),a("el-form-item",{attrs:{label:"是否含附件:",prop:"Is_Attachment"}},[a("el-radio-group",{model:{value:e.form.Is_Attachment,callback:function(t){e.$set(e.form,"Is_Attachment",t)},expression:"form.Is_Attachment"}},[a("el-radio",{attrs:{label:!0}},[e._v("是")]),a("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1),a("el-form-item",{attrs:{label:"是否优先使用私有流程设计模板:",prop:"Is_Use_Private"}},[a("el-radio-group",{model:{value:e.form.Is_Use_Private,callback:function(t){e.$set(e.form,"Is_Use_Private",t)},expression:"form.Is_Use_Private"}},[a("el-radio",{attrs:{label:!0}},[e._v("是")]),a("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)},r=[]},"64d1":function(e,t,a){"use strict";a.r(t);var n=a("9933"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},9933:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9"),a("e9f5"),a("7d54"),a("d3b7"),a("159b");var r=n(a("5530")),o=n(a("34e9")),l=a("20ff"),i=n(a("f8a2")),s=n(a("333d"));t.default={name:"SysFormDesignList",components:{TopHeader:o.default,bimDialog:i.default,Pagination:s.default},filters:{statusFilter:function(e){var t={0:"color-success",1:"color-danger"};return t[e]}},data:function(){return{pageInfo:{Page:1,PageSize:20,TotalCount:0},statusOptions:[{key:0,display_name:"正常"},{key:1,display_name:"停用"}],tableData:[],searchInput:"",tbLoading:!1,showDescription:!0,multipleSelection:[],tableKey:0}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,l.FormsLoad)((0,r.default)({},this.pageInfo)).then((function(t){if(t.IsSucceed){var a=t.Data,n=a.Data,r=a.Page,o=a.PageSize,l=a.TotalCount;e.tableData=n,e.pageInfo.Page=r,e.pageInfo.PageSize=o,e.pageInfo.TotalCount=l}else e.$message({message:t.Message,type:"error"});e.tbLoading=!1}))},handleSelectionChange:function(e){this.multipleSelection=e},handleAdd:function(){this.$refs.dialog.handleOpen("add")},handleEdit:function(e){this.$refs.dialog.handleOpen("edit",e)},handleDelete:function(){var e=this;this.multipleSelection.length?this.$confirm("是否删除选中的表单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=[];e.multipleSelection.forEach((function(e){t.push(e.Id)})),(0,l.FormsDelete)({ids:t}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})})):this.$message({message:"请先选择表单",type:"info"})},handleModifyStatus:function(){}}}},a44e:function(e,t,a){"use strict";a.r(t);var n=a("b912"),r=a("64d1");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);var l=a("2877"),i=Object(l["a"])(r["default"],n["a"],n["b"],!1,null,"34c69ca0",null);t["default"]=i.exports},b912:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{},[a("top-header",{scopedSlots:e._u([{key:"right",fn:function(){return[a("el-button",{attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("添加")]),a("el-button",{attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.handleDelete}},[e._v("删除")])]},proxy:!0}])}),a("div",[a("div",{staticStyle:{height:"100%"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],key:"Id",ref:"mainTable",staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"55"}}),a("el-table-column",{attrs:{align:"center",label:"名称","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",{staticClass:"link-type",on:{click:function(t){return e.handleEdit(n)}}},[e._v(e._s(n.Name))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"页面表单","min-width":"120px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",[e._v(e._s(n.Web_Id))])]}}])}),e.showDescription?a("el-table-column",{attrs:{align:"center",label:"描述","min-width":"150px"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("span",{staticClass:"txt-red"},[e._v(e._s(n.Description))])]}}],null,!1,1847884386)}):e._e(),a("el-table-column",{attrs:{align:"center",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:[t.row.Is_Disabled?"txt-red":"txt-green"]},[e._v(e._s(e.statusOptions.find((function(e){return e.key==t.row.Is_Disabled})).display_name))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"230"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.handleEdit(n)}}},[e._v("编辑")])]}}])})],1),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.pageInfo.TotalCount>0,expression:"pageInfo.TotalCount>0"}],attrs:{total:e.pageInfo.TotalCount,page:e.pageInfo.Page,limit:e.pageInfo.PageSize},on:{"update:page":function(t){return e.$set(e.pageInfo,"Page",t)},"update:limit":function(t){return e.$set(e.pageInfo,"PageSize",t)},pagination:e.fetchData}})],1)]),a("bim-dialog",{ref:"dialog",on:{update:e.fetchData}})],1)},r=[]},dc64:function(e,t,a){},f382:function(e,t,a){"use strict";function n(e){return e.filter((function(e){return!!e.Is_Directory&&(e.Children&&e.Children.length&&(e.Children=n(e.Children)),!0)}))}function r(e){e.map((function(e){if(e.Is_Directory||!e.Children)return r(e.Children);delete e.Children}))}function o(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function n(e,t,r){for(var o=0;o<e.length;o++){var l=e[o];if(l.Id===t)return a&&r.push(l),r;if(l.Children&&l.Children.length){if(r.push(l),n(l.Children,t,r).length)return r;r.pop()}}return[]}return n(e,t,[])}function l(e){return e.Children&&e.Children.length>0?l(e.Children[0]):e}function i(e){e.map((function(e){e.Is_Directory&&(e.disabled=!0,e.Children&&e.Children.length>0&&i(e.Children))}))}Object.defineProperty(t,"__esModule",{value:!0}),t.clearLeafChildren=r,t.disableDirectory=i,t.findAllParentNode=o,t.findFirstNode=l,t.getDirectoryTree=n,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7")},f7b4:function(e,t,a){"use strict";a.r(t);var n=a("3ed6"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},f8a2:function(e,t,a){"use strict";a.r(t);var n=a("4ac3"),r=a("f7b4");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("35f6");var l=a("2877"),i=Object(l["a"])(r["default"],n["a"],n["b"],!1,null,"208d5402",null);t["default"]=i.exports}}]);