(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-ab72440e"],{"321bf":function(e,n,t){"use strict";t.d(n,"a",(function(){return u})),t.d(n,"b",(function(){return r}));var u=function(){var e=this,n=e.$createElement,t=e._self._c||n;return t("home")},r=[]},4285:function(e,n,t){"use strict";t.r(n);var u=t("b0bc"),r=t.n(u);for(var a in u)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(a);n["default"]=r.a},"7e99":function(e,n,t){"use strict";t.r(n);var u=t("321bf"),r=t("4285");for(var a in r)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(a);var f=t("2877"),c=Object(f["a"])(r["default"],u["a"],u["b"],!1,null,"fcaf7020",null);n["default"]=c.exports},b0bc:function(e,n,t){"use strict";var u=t("4ea4").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=u(t("d24a"));n.default={name:"PROComTransferReceiveNew",provide:{pageType:"com"},components:{Home:r.default}}}}]);