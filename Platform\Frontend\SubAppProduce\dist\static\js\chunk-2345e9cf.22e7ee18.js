(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-2345e9cf"],{"09f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.scrollTo=i,Math.easeInOutQuad=function(e,t,n,a){return e/=a/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,n){var i=o(),c=e-i,s=20,u=0;t="undefined"===typeof t?500:t;var l=function(){u+=s;var e=Math.easeInOutQuad(u,i,c,t);r(e),u<t?a(l):n&&"function"===typeof n&&n()};l()}},3166:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.BindBimProject=f,t.CancelBindBimProject=m,t.DeleteProject=l,t.GeAreaTrees=I,t.GetFileSync=S,t.GetInstallUnitIdNameList=w,t.GetNoBindProjectList=p,t.GetPartDeepenFileList=j,t.GetProjectAreaTreeList=b,t.GetProjectEntity=s,t.GetProjectList=c,t.GetProjectPageList=i,t.GetProjectTemplate=h,t.GetPushProjectPageList=P,t.GetSchedulingPartList=C,t.IsEnableProjectMonomer=d,t.SaveProject=u,t.UpdateProjectTemplateBase=g,t.UpdateProjectTemplateContacts=_,t.UpdateProjectTemplateContract=v,t.UpdateProjectTemplateOther=y;var r=a(n("b775")),o=a(n("4328"));function i(e){return(0,r.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:e})}function c(e){return(0,r.default)({url:"/PRO/Project/GetProjectList",method:"post",data:e})}function s(e){return(0,r.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:o.default.stringify(e)})}function u(e){return(0,r.default)({url:"/PRO/Project/SaveProject",method:"post",data:e})}function l(e){return(0,r.default)({url:"/PRO/Project/DeleteProject",method:"post",data:o.default.stringify(e)})}function d(e){return(0,r.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:e})}function f(e){return(0,r.default)({url:"/PRO/Project/BindBimProject",method:"post",data:e})}function m(e){return(0,r.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:e})}function p(e){return(0,r.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:e})}function h(e){return(0,r.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:e})}function g(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:e})}function _(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:e})}function v(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:e})}function y(e){return(0,r.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:e})}function P(e){return(0,r.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:e})}function b(e){return(0,r.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:e})}function w(e){return(0,r.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:e})}function I(e){return(0,r.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:e})}function j(e){return(0,r.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:e})}function C(e){return(0,r.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:e})}function S(e){return(0,r.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:e})}},"4ca5b":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[n("div",{staticClass:"cs-z-page-main-content",staticStyle:{padding:"0"}},[n("top-header",{staticStyle:{height:"auto","border-bottom":"16px solid #f2f3f5"},attrs:{padding:"16px 20px 0 16px"},scopedSlots:e._u([{key:"left",fn:function(){return[n("div",[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",inline:!0}},[n("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[n("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择..."},on:{change:e.projectChange},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},[e._l(e.ProjectNameData,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})}))],2)],1),n("el-form-item",{attrs:{label:"区域",prop:"Area_Id"}},[n("el-tree-select",{ref:"treeSelectArea",staticClass:"cs-tree-x",attrs:{disabled:!e.form.Project_Id,"select-params":{clearable:!0},"tree-params":e.treeParamsArea},model:{value:e.form.Area_Id,callback:function(t){e.$set(e.form,"Area_Id",t)},expression:"form.Area_Id"}})],1),n("el-form-item",{attrs:{label:"要货时间",prop:"planTime"}},[n("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},on:{change:e.changeTime},model:{value:e.form.planTime,callback:function(t){e.$set(e.form,"planTime",t)},expression:"form.planTime"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),n("el-button",{on:{click:function(t){e.resetForm(),e.handleSearch()}}},[e._v("重置")])],1)],1)],1)]},proxy:!0}])}),n("div",{staticClass:"cs-z-tb-wrapper fff"},[n("el-button",{staticStyle:{"margin-bottom":"20px",width:"65px"},attrs:{loading:e.btLoading,type:"success"},on:{click:e.handleExport}},[e._v("导 出 ")]),n("div",{staticClass:"table-wapper"},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据",height:"100%",align:"left",stripe:"",data:e.tbData,resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[n("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.comList,(function(t,a){return n("vxe-column",{key:t.Id,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"","min-width":t.Width,align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u([{key:"default",fn:function(a){var r=a.row;return[0!=r[t.Code]?n("span",[e._v(e._s(r[t.Code]||"-"))]):n("span",[e._v(" "+e._s(e._f("displayValue")(r[t.Code])))])]}}],null,!0)})}))],2)],1),n("div",{staticClass:"cs-bottom"},[n("div",{staticClass:"cs-component-num"},[n("div",{staticClass:"cs-col"},[n("span",[e._v("构件总数")]),e.comp_count?n("i",[e._v(e._s(e.comp_count))]):n("i",[e._v("-")])]),n("div",{staticClass:"cs-col"},[n("span",[e._v("构件总重(t)")]),e.comp_weight?n("i",[e._v(e._s(Number(e.comp_weight).toFixed(2)))]):n("i",[e._v("-")])]),e._l(Object.keys(e.summaryList),(function(t,a){return n("div",{key:a,staticClass:"cs-col"},[n("span",[e._v(e._s(t))]),e.summaryList[t]?n("i",[e._v(e._s(parseFloat(e.summaryList[t]).toFixed(2)))]):n("i",[e._v("-")])])}))],2),n("Pagination",{staticClass:"cs-table-pagination",attrs:{total:e.total,"max-height":"100%","page-sizes":e.tablePageSize,page:e.queryInfo.Page,limit:e.queryInfo.PageSize,layout:"total, sizes, prev, pager, next, jumper"},on:{"update:page":function(t){return e.$set(e.queryInfo,"Page",t)},"update:limit":function(t){return e.$set(e.queryInfo,"PageSize",t)},pagination:e.changePage}})],1)],1)],1)])},r=[]},5218:function(e,t,n){"use strict";n.r(t);var a=n("4ca5b"),r=n("67bd");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("63de");var i=n("2877"),c=Object(i["a"])(r["default"],a["a"],a["b"],!1,null,"5cec0c0c",null);t["default"]=c.exports},"5f52":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("dca8"),n("d3b7");var r=a(n("c14f")),o=a(n("1da1")),i=n("6186"),c=n("fd31");t.default={data:function(){return{Code:""}},methods:{getTableConfig:function(e){var t=this;return(0,o.default)((0,r.default)().m((function n(){return(0,r.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,t.getTypeList();case 1:return n.n=2,t.getTable(e);case 2:return n.a(2)}}),n)})))()},getTypeList:function(){var e=this;return(0,o.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){if(t.IsSucceed){var n=Object.freeze(t.Data);if(n.length>0){var a,r=null===(a=n[0])||void 0===a?void 0:a.Id;e.Code=n.find((function(e){return e.Id===r})).Code}}else e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getTable:function(e){var t=this;return new Promise((function(n){(0,i.GetGridByCode)({code:e+","+t.Code}).then((function(e){var a=e.IsSucceed,r=e.Data,o=e.Message;if(a){if(!r)return void t.$message({message:"表格配置不存在",type:"error"});t.tbConfig=Object.assign({},t.tbConfig,r.Grid),t.columns=(r.ColumnList.filter((function(e){return e.Is_Display}))||[]).map((function(e){return e.Is_Resizable=!0,e})),r.Grid.Is_Page&&(t.queryInfo.PageSize=+r.Grid.Row_Number),n(t.columns)}else t.$message({message:o,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,n=e.size;this.tbConfig.Row_Number=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(n||this.tbConfig.Row_Number),this.queryInfo.Page=n?1:t,this.fetchData()},pageChange:function(e){var t=e.page,n=e.limit;this.queryInfo.Page=t,this.queryInfo.PageSize=n,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var n={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?n.Value=e[t]:n.Value=[e[t]];for(var a=0;a<this.columns.length;a++){var r=this.columns[a];if(r.Code===t){n.Type=r.Type,n.Filter_Type=r.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(n)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"63de":function(e,t,n){"use strict";n("6b88")},"67bd":function(e,t,n){"use strict";n.r(t);var a=n("e6afa"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},"6b88":function(e,t,n){},dca8:function(e,t,n){"use strict";var a=n("23e7"),r=n("bb2f"),o=n("d039"),i=n("861d"),c=n("f183").onFreeze,s=Object.freeze,u=o((function(){s(1)}));a({target:"Object",stat:!0,forced:u,sham:!r},{freeze:function(e){return s&&i(e)?s(c(e)):e}})},e144:function(e,t,n){"use strict";n.r(t),n.d(t,"v1",(function(){return l})),n.d(t,"v3",(function(){return x})),n.d(t,"v4",(function(){return k["a"]})),n.d(t,"v5",(function(){return F})),n.d(t,"NIL",(function(){return q})),n.d(t,"version",(function(){return E})),n.d(t,"validate",(function(){return d["a"]})),n.d(t,"stringify",(function(){return i["a"]})),n.d(t,"parse",(function(){return m}));var a,r,o=n("d8f8"),i=n("58cf"),c=0,s=0;function u(e,t,n){var u=t&&n||0,l=t||new Array(16);e=e||{};var d=e.node||a,f=void 0!==e.clockseq?e.clockseq:r;if(null==d||null==f){var m=e.random||(e.rng||o["a"])();null==d&&(d=a=[1|m[0],m[1],m[2],m[3],m[4],m[5]]),null==f&&(f=r=16383&(m[6]<<8|m[7]))}var p=void 0!==e.msecs?e.msecs:Date.now(),h=void 0!==e.nsecs?e.nsecs:s+1,g=p-c+(h-s)/1e4;if(g<0&&void 0===e.clockseq&&(f=f+1&16383),(g<0||p>c)&&void 0===e.nsecs&&(h=0),h>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");c=p,s=h,r=f,p+=122192928e5;var _=(1e4*(268435455&p)+h)%4294967296;l[u++]=_>>>24&255,l[u++]=_>>>16&255,l[u++]=_>>>8&255,l[u++]=255&_;var v=p/4294967296*1e4&268435455;l[u++]=v>>>8&255,l[u++]=255&v,l[u++]=v>>>24&15|16,l[u++]=v>>>16&255,l[u++]=f>>>8|128,l[u++]=255&f;for(var y=0;y<6;++y)l[u+y]=d[y];return t||Object(i["a"])(l)}var l=u,d=n("06e4");function f(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}var m=f;function p(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}var h="6ba7b810-9dad-11d1-80b4-00c04fd430c8",g="6ba7b811-9dad-11d1-80b4-00c04fd430c8",_=function(e,t,n){function a(e,a,r,o){if("string"===typeof e&&(e=p(e)),"string"===typeof a&&(a=m(a)),16!==a.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var c=new Uint8Array(16+e.length);if(c.set(a),c.set(e,a.length),c=n(c),c[6]=15&c[6]|t,c[8]=63&c[8]|128,r){o=o||0;for(var s=0;s<16;++s)r[o+s]=c[s];return r}return Object(i["a"])(c)}try{a.name=e}catch(r){}return a.DNS=h,a.URL=g,a};function v(e){if("string"===typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return y(b(w(e),8*e.length))}function y(e){for(var t=[],n=32*e.length,a="0123456789abcdef",r=0;r<n;r+=8){var o=e[r>>5]>>>r%32&255,i=parseInt(a.charAt(o>>>4&15)+a.charAt(15&o),16);t.push(i)}return t}function P(e){return 14+(e+64>>>9<<4)+1}function b(e,t){e[t>>5]|=128<<t%32,e[P(t)-1]=t;for(var n=1732584193,a=-271733879,r=-1732584194,o=271733878,i=0;i<e.length;i+=16){var c=n,s=a,u=r,l=o;n=S(n,a,r,o,e[i],7,-680876936),o=S(o,n,a,r,e[i+1],12,-389564586),r=S(r,o,n,a,e[i+2],17,606105819),a=S(a,r,o,n,e[i+3],22,-1044525330),n=S(n,a,r,o,e[i+4],7,-176418897),o=S(o,n,a,r,e[i+5],12,1200080426),r=S(r,o,n,a,e[i+6],17,-1473231341),a=S(a,r,o,n,e[i+7],22,-45705983),n=S(n,a,r,o,e[i+8],7,1770035416),o=S(o,n,a,r,e[i+9],12,-1958414417),r=S(r,o,n,a,e[i+10],17,-42063),a=S(a,r,o,n,e[i+11],22,-1990404162),n=S(n,a,r,o,e[i+12],7,1804603682),o=S(o,n,a,r,e[i+13],12,-40341101),r=S(r,o,n,a,e[i+14],17,-1502002290),a=S(a,r,o,n,e[i+15],22,1236535329),n=T(n,a,r,o,e[i+1],5,-165796510),o=T(o,n,a,r,e[i+6],9,-1069501632),r=T(r,o,n,a,e[i+11],14,643717713),a=T(a,r,o,n,e[i],20,-373897302),n=T(n,a,r,o,e[i+5],5,-701558691),o=T(o,n,a,r,e[i+10],9,38016083),r=T(r,o,n,a,e[i+15],14,-660478335),a=T(a,r,o,n,e[i+4],20,-405537848),n=T(n,a,r,o,e[i+9],5,568446438),o=T(o,n,a,r,e[i+14],9,-1019803690),r=T(r,o,n,a,e[i+3],14,-187363961),a=T(a,r,o,n,e[i+8],20,1163531501),n=T(n,a,r,o,e[i+13],5,-1444681467),o=T(o,n,a,r,e[i+2],9,-51403784),r=T(r,o,n,a,e[i+7],14,1735328473),a=T(a,r,o,n,e[i+12],20,-1926607734),n=D(n,a,r,o,e[i+5],4,-378558),o=D(o,n,a,r,e[i+8],11,-2022574463),r=D(r,o,n,a,e[i+11],16,1839030562),a=D(a,r,o,n,e[i+14],23,-35309556),n=D(n,a,r,o,e[i+1],4,-1530992060),o=D(o,n,a,r,e[i+4],11,1272893353),r=D(r,o,n,a,e[i+7],16,-155497632),a=D(a,r,o,n,e[i+10],23,-1094730640),n=D(n,a,r,o,e[i+13],4,681279174),o=D(o,n,a,r,e[i],11,-358537222),r=D(r,o,n,a,e[i+3],16,-722521979),a=D(a,r,o,n,e[i+6],23,76029189),n=D(n,a,r,o,e[i+9],4,-640364487),o=D(o,n,a,r,e[i+12],11,-421815835),r=D(r,o,n,a,e[i+15],16,530742520),a=D(a,r,o,n,e[i+2],23,-995338651),n=L(n,a,r,o,e[i],6,-198630844),o=L(o,n,a,r,e[i+7],10,1126891415),r=L(r,o,n,a,e[i+14],15,-1416354905),a=L(a,r,o,n,e[i+5],21,-57434055),n=L(n,a,r,o,e[i+12],6,1700485571),o=L(o,n,a,r,e[i+3],10,-1894986606),r=L(r,o,n,a,e[i+10],15,-1051523),a=L(a,r,o,n,e[i+1],21,-2054922799),n=L(n,a,r,o,e[i+8],6,1873313359),o=L(o,n,a,r,e[i+15],10,-30611744),r=L(r,o,n,a,e[i+6],15,-1560198380),a=L(a,r,o,n,e[i+13],21,1309151649),n=L(n,a,r,o,e[i+4],6,-145523070),o=L(o,n,a,r,e[i+11],10,-1120210379),r=L(r,o,n,a,e[i+2],15,718787259),a=L(a,r,o,n,e[i+9],21,-343485551),n=I(n,c),a=I(a,s),r=I(r,u),o=I(o,l)}return[n,a,r,o]}function w(e){if(0===e.length)return[];for(var t=8*e.length,n=new Uint32Array(P(t)),a=0;a<t;a+=8)n[a>>5]|=(255&e[a/8])<<a%32;return n}function I(e,t){var n=(65535&e)+(65535&t),a=(e>>16)+(t>>16)+(n>>16);return a<<16|65535&n}function j(e,t){return e<<t|e>>>32-t}function C(e,t,n,a,r,o){return I(j(I(I(t,e),I(a,o)),r),n)}function S(e,t,n,a,r,o,i){return C(t&n|~t&a,e,t,r,o,i)}function T(e,t,n,a,r,o,i){return C(t&a|n&~a,e,t,r,o,i)}function D(e,t,n,a,r,o,i){return C(t^n^a,e,t,r,o,i)}function L(e,t,n,a,r,o,i){return C(n^(t|~a),e,t,r,o,i)}var A=v,O=_("v3",48,A),x=O,k=n("ec26");function G(e,t,n,a){switch(e){case 0:return t&n^~t&a;case 1:return t^n^a;case 2:return t&n^t&a^n&a;case 3:return t^n^a}}function R(e,t){return e<<t|e>>>32-t}function N(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"===typeof e){var a=unescape(encodeURIComponent(e));e=[];for(var r=0;r<a.length;++r)e.push(a.charCodeAt(r))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var o=e.length/4+2,i=Math.ceil(o/16),c=new Array(i),s=0;s<i;++s){for(var u=new Uint32Array(16),l=0;l<16;++l)u[l]=e[64*s+4*l]<<24|e[64*s+4*l+1]<<16|e[64*s+4*l+2]<<8|e[64*s+4*l+3];c[s]=u}c[i-1][14]=8*(e.length-1)/Math.pow(2,32),c[i-1][14]=Math.floor(c[i-1][14]),c[i-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<i;++d){for(var f=new Uint32Array(80),m=0;m<16;++m)f[m]=c[d][m];for(var p=16;p<80;++p)f[p]=R(f[p-3]^f[p-8]^f[p-14]^f[p-16],1);for(var h=n[0],g=n[1],_=n[2],v=n[3],y=n[4],P=0;P<80;++P){var b=Math.floor(P/20),w=R(h,5)+G(b,g,_,v)+y+t[b]+f[P]>>>0;y=v,v=_,_=R(g,30)>>>0,g=h,h=w}n[0]=n[0]+h>>>0,n[1]=n[1]+g>>>0,n[2]=n[2]+_>>>0,n[3]=n[3]+v>>>0,n[4]=n[4]+y>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]}var z=N,U=_("v5",80,z),F=U,q="00000000-0000-0000-0000-000000000000";function $(e){if(!Object(d["a"])(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}var E=$},e6afa:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("5530")),o=a(n("c14f")),i=a(n("1da1"));n("99af"),n("4de4"),n("7db0"),n("14d9"),n("a434"),n("e9f5"),n("910d"),n("f665"),n("7d54"),n("a9e3"),n("d3b7"),n("3ca3"),n("159b"),n("ddb0");var c=a(n("34e9")),s=a(n("333d")),u=a(n("5f52")),l=n("586a"),d=n("8975"),f=n("3166"),m=n("ed08"),p=n("c685"),h=n("e144");t.default={name:"ProSupplyPlan",components:{TopHeader:c.default,Pagination:s.default},mixins:[u.default],data:function(){return{tablePageSize:p.tablePageSize,warning:"warning",ProjectNameData:[],currentComponent:"",title:"",width:"",columns:[],tbData:[],selectList:[],tbLoading:!1,btLoading:!1,tbConfig:{},total:0,ProjectStatusData:[],form:{Sys_Project_Id:"",Project_Id:"",Area_Id:"",planTime:[],Cooperate_Demand_End_Date:null,Cooperate_Demand_Begin_Date:null},queryInfo:{Page:1,PageSize:20},treeParamsArea:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},summaryList:{},TotalSummaryList:{},comp_count:0,comp_weight:0,total_comp_count:0,total_comp_weight:0,summaryJson:{},typeList:[],pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-6048e5),e.$emit("pick",[n,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-2592e6),e.$emit("pick",[n,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,n=new Date;n.setTime(n.getTime()-7776e6),e.$emit("pick",[n,t])}}]},Is_Integration:!1}},computed:{comList:function(){return this.Is_Integration?this.columns:this.columns.filter((function(e){return"site_description"!==e.Code&&"supply_order_code"!==e.Code}))}},created:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.$store.dispatch("user/getPreferenceSetting","Is_Integration");case 1:return e.Is_Integration=t.v,t.n=2,e.getProjectOption();case 2:return t.a(2)}}),t)})))()},mounted:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("ProSupplyPlan");case 1:return t.n=2,e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{getProjectOption:function(){var e=this;(0,f.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.ProjectNameData=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},getAreaList:function(){var e=this,t=this.form.Project_Id;(0,f.GeAreaTrees)({projectId:t}).then((function(t){t.IsSucceed?(e.treeParamsArea.data=t.Data,e.$nextTick((function(n){e.$refs.treeSelectArea.treeDataUpdateFun(t.Data)}))):e.$message({message:t.Message,type:"error"})}))},projectChange:function(e){var t,n=this;this.form.Project_Id=null===(t=this.ProjectNameData.find((function(t){return t.Sys_Project_Id===e})))||void 0===t?void 0:t.Id,this.form.Area_Id="",this.treeParamsArea.data=[],this.$nextTick((function(e){n.$refs.treeSelectArea.treeDataUpdateFun([])})),e&&this.getAreaList()},changeTime:function(e){this.form.Cooperate_Demand_Begin_Date=e?e[0]:null,this.form.Cooperate_Demand_End_Date=e?e[1]:null},changePage:function(e){var t=this;return(0,i.default)((0,o.default)().m((function n(){return(0,o.default)().w((function(n){while(1)switch(n.n){case 0:return t.tbLoading=!0,n.n=1,t.getTableConfig("ProSupplyPlan");case 1:("number"!==typeof t.queryInfo.PageSize||t.queryInfo.PageSize<1)&&(t.queryInfo.PageSize=20),t.queryInfo.PageSize=e.limit,Promise.all([t.fetchData()]).then((function(e){t.tbLoading=!1}));case 2:return n.a(2)}}),n)})))()},tbSelectChange:function(e){var t=this;if(this.selectList=e.records,this.selectList.length>0){var n=0,a=0;for(var r in this.summaryJson)this.summaryJson[r]=0;this.selectList.forEach((function(e){for(var r in n+=Number(e.Actual_Total_Weight||0),a+=Number(e.Actual_Total_Count||0),e)Object.prototype.hasOwnProperty.call(t.summaryJson,r)&&(t.summaryJson[r]+=Number(e[r]))})),this.comp_weight=n,this.comp_count=a;var o=function(e){var n,a;null!==(n=t.typeList.find((function(t){return t.Code==e})))&&void 0!==n&&n.Name&&"total"!=e&&(t.summaryList[null===(a=t.typeList.find((function(t){return t.Code==e})))||void 0===a?void 0:a.Name]=t.summaryJson[e])};for(var i in this.summaryJson)o(i)}else this.comp_weight=this.total_comp_weight,this.comp_count=this.total_comp_count,this.summaryList=this.TotalSummaryList},resetForm:function(){this.form.Cooperate_Demand_Begin_Date=null,this.form.Cooperate_Demand_End_Date=null,this.form.Project_Id="",this.$refs["form"].resetFields()},handleSearch:function(){var e=this;return(0,i.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return e.queryInfo.Page=1,t.n=1,e.getTableConfig("ProSupplyPlan");case 1:return t.n=2,e.debounce(e.fetchData(),500);case 2:return t.a(2)}}),t)})))()},handleClose:function(){},debounce:function(e,t){null!=this.timer&&clearTimeout(this.timer),this.timer=setTimeout(e,t)},fetchData:function(){var e=this;this.tbLoading=!0,(0,l.GetSupplyPlanPageList)((0,r.default)((0,r.default)({},this.queryInfo),this.form)).then((function(t){if(t.IsSucceed){var n,a=t.Data.Data,r=a.Actual_Data_List,o=a.Summart_Data_List,i=a.Supply_Plan_List,c=a.Type_List,s=[];c.forEach((function(e){if("total"!=e.Code){var t={};t.Code=e.Code,t.Display_Name=e.Name,t.Id=(0,h.v4)(),t.Width=150,s.push(t)}})),e.typeList=c,(n=e.columns).splice.apply(n,[8,0].concat(s)),i.forEach((function(e){var t,n;e.Actual_Total_Count=null===(t=r.find((function(t){return t.sys_project_id===e.sys_project_id&&t.area_id===e.area_id})))||void 0===t?void 0:t.actual_total_count,e.Actual_Total_Weight=null===(n=r.find((function(t){return t.sys_project_id===e.sys_project_id&&t.area_id===e.area_id})))||void 0===n?void 0:n.actual_total_weight,e.Cooperate_Demand_Begin_Date=(0,d.timeFormat)(e.cooperate_demand_begin_date,"{y}-{m}-{d}"),e.Cooperate_Demand_End_Date=(0,d.timeFormat)(e.cooperate_demand_end_date,"{y}-{m}-{d}"),e.create_date=(0,d.timeFormat)(e.create_date,"{y}-{m}-{d}")})),e.summaryJson=o[0];var u={},l=function(t){var n,a;null!==(n=c.find((function(e){return e.Code==t})))&&void 0!==n&&n.Name&&"total"!=t&&(u[null===(a=c.find((function(e){return e.Code==t})))||void 0===a?void 0:a.Name]=e.summaryJson[t])};for(var f in e.summaryJson)l(f);e.total_comp_count=e.summaryJson.comp_count,e.comp_count=e.summaryJson.comp_count,e.comp_weight=e.summaryJson.comp_weight,e.total_comp_weight=e.summaryJson.comp_weight,e.summaryList=u,e.TotalSummaryList=u,e.tbData=i,e.total=t.Data.TotalCount}else e.$message({message:t.Message,type:"error"})})).finally((function(t){e.tbLoading=!1}))},handleExport:function(){var e=this;this.btLoading=!0,(0,l.ExportSupplyPlan)((0,r.default)({},this.form)).then((function(t){t.IsSucceed?window.open((0,m.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"})})).finally((function(t){e.btLoading=!1}))}}}}}]);