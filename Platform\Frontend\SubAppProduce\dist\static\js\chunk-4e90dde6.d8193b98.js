(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4e90dde6"],{1276:function(e,t,n){"use strict";var r=n("c65b"),a=n("e330"),o=n("d784"),s=n("825a"),i=n("861d"),l=n("1d80"),c=n("4840"),u=n("8aa5"),d=n("50c4"),f=n("577e"),m=n("dc4a"),p=n("14c3"),h=n("9f7f"),g=n("d039"),_=h.UNSUPPORTED_Y,b=4294967295,y=Math.min,v=a([].push),C=a("".slice),T=!g((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),k="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;o("split",(function(e,t,n){var a="0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:r(t,this,e,n)}:t;return[function(t,n){var o=l(this),s=i(t)?m(t,e):void 0;return s?r(s,t,o,n):r(a,f(o),t,n)},function(e,r){var o=s(this),i=f(e);if(!k){var l=n(a,o,i,r,a!==t);if(l.done)return l.value}var m=c(o,RegExp),h=o.unicode,g=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(_?"g":"y"),T=new m(_?"^(?:"+o.source+")":o,g),I=void 0===r?b:r>>>0;if(0===I)return[];if(0===i.length)return null===p(T,i)?[i]:[];var L=0,P=0,S=[];while(P<i.length){T.lastIndex=_?0:P;var x,O=p(T,_?C(i,P):i);if(null===O||(x=y(d(T.lastIndex+(_?P:0)),i.length))===L)P=u(i,P,h);else{if(v(S,C(i,L,P)),S.length===I)return S;for(var G=1;G<=O.length-1;G++)if(v(S,O[G]),S.length===I)return S;P=L=x}}return v(S,C(i,L)),S}]}),k||!T,_)},"15fd":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,n("a4d3");var r=a(n("ccb5"));function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(null==e)return{};var n,a,o=(0,r.default)(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)n=s[a],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}},"1f50":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("d3b7");var a=r(n("c14f")),o=r(n("1da1")),s=n("a024");t.default={data:function(){return{allList:[],selectList:[],list:[],value:[],processId:"",filterMethod:function(e,t){return t.Display_Name.indexOf(e)>-1}}},methods:{init:function(e){var t=this;return(0,o.default)((0,a.default)().m((function n(){return(0,a.default)().w((function(n){while(1)switch(n.n){case 0:return t.processId=e.Id,n.n=1,t.getAllList();case 1:return n.n=2,t.getCurrentList(e);case 2:return n.a(2)}}),n)})))()},getAllList:function(){var e=this;return new Promise((function(t){(0,s.GetWorkingTeams)({}).then((function(n){n.IsSucceed?(e.allList=n.Data.map((function(t){return e.$set(t,"Display_Name",t.Name),t})),t()):e.$message({message:n.Message,type:"error"})}))}))},getCurrentList:function(){var e=this;return new Promise((function(t){(0,s.GetWorkingTeamBase)({processId:e.processId}).then((function(n){n.IsSucceed?(e.selectList=n.Data.map((function(e){return e.Id})),t()):e.$message({message:n.Message,type:"error"})}))}))},change:function(e){this.selectList=e},handleSubmit:function(){var e=this;(0,s.UpdateProcessTeam)({processId:this.processId,teams:this.selectList}).then((function(t){t.IsSucceed?(e.$emit("close"),e.$emit("refresh"),e.$message({message:"修改成功",type:"success"})):e.$message({message:t.Message,type:"error"})}))}}}},"21f7":function(e,t,n){"use strict";n.r(t);var r=n("6a9be"),a=n("f2a1");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("f9348");var s=n("2877"),i=Object(s["a"])(a["default"],r["a"],r["b"],!1,null,"98dc3622",null);t["default"]=i.exports},"2a7f":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeletePartType=u,t.GetConsumingProcessAllList=h,t.GetFactoryPartTypeIndentifySetting=d,t.GetPartTypeEntity=p,t.GetPartTypeList=i,t.GetPartTypePageList=s,t.GetPartTypeTree=m,t.SaveConsumingProcessAllList=g,t.SavePartType=c,t.SavePartTypeIdentifySetting=f,t.SettingDefault=l;var a=r(n("b775")),o=r(n("4328"));function s(e){return(0,a.default)({url:"/PRO/PartType/GetPartTypePageList",method:"post",data:e})}function i(e){return(0,a.default)({url:"/PRO/PartType/GetPartTypeList",method:"post",data:e})}function l(e){return(0,a.default)({url:"/PRO/PartType/SettingDefault",method:"post",data:o.default.stringify(e)})}function c(e){return(0,a.default)({url:"/PRO/PartType/SavePartType",method:"post",data:e})}function u(e){return(0,a.default)({url:"/PRO/PartType/DeletePartType",method:"post",data:o.default.stringify(e)})}function d(e){return(0,a.default)({url:"/PRO/PartType/GetFactoryPartTypeIndentifySetting",method:"post",data:e})}function f(e){return(0,a.default)({url:"/PRO/PartType/SavePartTypeIdentifySetting",method:"post",data:e})}function m(e){return(0,a.default)({url:"/pro/parttype/GetPartTypeTree",method:"post",data:o.default.stringify(e)})}function p(e){return(0,a.default)({url:"/pro/parttype/GetPartTypeEntity",method:"post",data:o.default.stringify(e)})}function h(e){return(0,a.default)({url:"/PRO/PartType/GetConsumingProcessAllList",method:"post",data:e})}function g(e){return(0,a.default)({url:"/PRO/PartType/SaveConsumingProcessAllList",method:"post",data:e})}},"2e8a":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteComponentType=u,t.GetCompTypeTree=d,t.GetComponentTypeEntity=c,t.GetComponentTypeList=s,t.GetFactoryCompTypeIndentifySetting=g,t.GetTableSettingList=m,t.GetTypePageList=i,t.RestoreTemplateType=y,t.SavDeepenTemplateSetting=b,t.SaveCompTypeIdentifySetting=_,t.SaveComponentType=l,t.SaveProBimComponentType=f,t.UpdateColumnSetting=h,t.UpdateComponentPartTableSetting=p;var a=r(n("b775")),o=r(n("4328"));function s(e){return(0,a.default)({url:"/PRO/ComponentType/GetComponentTypeList",method:"post",data:e})}function i(e){return(0,a.default)({url:"/PRO/ComponentType/GetTypePageList",method:"post",data:e})}function l(e){return(0,a.default)({url:"/PRO/ComponentType/SaveComponentType",method:"post",data:e})}function c(e){return(0,a.default)({url:"/PRO/ComponentType/GetComponentTypeEntity",method:"post",data:o.default.stringify(e)})}function u(e){return(0,a.default)({url:"/PRO/ComponentType/DeleteComponentType",method:"post",data:o.default.stringify(e)})}function d(e){return(0,a.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:o.default.stringify(e)})}function f(e){return(0,a.default)({url:"/PRO/ComponentType/SaveProBimComponentType",method:"post",data:e})}function m(e){return(0,a.default)({url:"/PRO/ProfessionalType/GetTableSettingList",method:"post",data:e})}function p(e){return(0,a.default)({url:"/PRO/ProfessionalType/UpdateComponentPartTableSetting",method:"post",data:e})}function h(e){return(0,a.default)({url:"/PRO/ProfessionalType/UpdateColumnSetting",method:"post",data:e})}function g(e){return(0,a.default)({url:"/pro/componenttype/GetFactoryCompTypeIndentifySetting",method:"post",data:e})}function _(e){return(0,a.default)({url:"/pro/componenttype/SaveCompTypeIdentifySetting",method:"post",data:e})}function b(e){return(0,a.default)({url:"/pro/ProfessionalType/SavDeepenTemplateSetting",method:"post",data:e})}function y(e){return(0,a.default)({url:"/pro/ProfessionalType/RestoreTemplateType",method:"post",data:e})}},"3dcd":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("34e9")),o=r(n("21f7")),s=r(n("7319")),i=r(n("3fda")),l=r(n("adf1")),c=r(n("fae7")),u=n("a024"),d=r(n("21f5"));t.default={name:"PROProcessManagement",components:{ElTableEmpty:d.default,TopHeader:a.default,Add:o.default,partRecognitionConfig:i.default,compRecognitionConfig:l.default,PartTakeConfig:c.default,ZClass:s.default},data:function(){return{tableData:[],currentComponent:"",title:"",rowInfo:null,type:"",pageLoading:!1,dialogVisible:!1,formInline:{name:"",code:""}}},mounted:function(){this.fetchData()},methods:{fetchData:function(){var e=this;this.pageLoading=!0,(0,u.GetProcessListBase)(this.formInline).then((function(t){t.IsSucceed?e.tableData=t.Data:e.$message({message:t.Message,type:"error"}),e.pageLoading=!1}))},handleClose:function(){this.dialogVisible=!1},handleConfig:function(){this.title="零件识别配置",this.currentComponent="partRecognitionConfig",this.dialogVisible=!0},handleConfigComp:function(){this.title="构件识别配置",this.currentComponent="compRecognitionConfig",this.dialogVisible=!0},handleTakeConfig:function(){this.title="零件领用配置",this.currentComponent="PartTakeConfig",this.dialogVisible=!0},handleDialog:function(e,t){this.currentComponent="Add",this.type=e,"add"===e?this.title="新建":(this.title="编辑",this.rowInfo=t),this.dialogVisible=!0},handleManage:function(e){var t=this;this.currentComponent="ZClass",this.title="班组管理",this.dialogVisible=!0,this.$nextTick((function(n){t.$refs.content.init(e)}))},handleDelete:function(e){var t=this;this.$confirm("是否删除当前工艺?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,u.DeleteProcess)({processId:e}).then((function(e){e.IsSucceed?(t.$message({message:"删除成功",type:"success"}),t.fetchData()):t.$message({message:e.Message,type:"error"})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))}}}},"3fda":function(e,t,n){"use strict";n.r(t);var r=n("ab0d"),a=n("5ee3");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("581a");var s=n("2877"),i=Object(s["a"])(a["default"],r["a"],r["b"],!1,null,"6c482ccf",null);t["default"]=i.exports},"455c":function(e,t,n){"use strict";n.r(t);var r=n("9bd3"),a=n("a29a");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("70ba");var s=n("2877"),i=Object(s["a"])(a["default"],r["a"],r["b"],!1,null,"c2d463b4",null);t["default"]=i.exports},5127:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-form",{ref:"form",attrs:{"label-width":"120px"}},[e._l(e.list,(function(t,r){return n("el-form-item",{key:r,attrs:{"show-message":!1,label:t.Part_Type_Name,prop:"mainPart"}},[n("el-select",{attrs:{clearable:""},model:{value:t.Working_Process_Id,callback:function(n){e.$set(t,"Working_Process_Id",n)},expression:"item.Working_Process_Id"}},e._l(e.selectList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)})),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],2)},a=[]},"57ac":function(e,t,n){},"581a":function(e,t,n){"use strict";n("aa99")},"5ee3":function(e,t,n){"use strict";n.r(t);var r=n("a400"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},"6a9be":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"form-wrapper"},[n("div",{staticClass:"form-x"},[n("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[n("el-form-item",{attrs:{label:"名称",prop:"Name"}},[n("el-input",{attrs:{maxlength:30,placeholder:"最多30个字","show-word-limit":""},model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1),n("el-form-item",{attrs:{label:"代号",prop:"Code"}},[n("el-input",{attrs:{maxlength:30,placeholder:"字母+数字，30字符","show-word-limit":""},on:{input:function(t){return e.form.Code=e.codeChange(t)}},model:{value:e.form.Code,callback:function(t){e.$set(e.form,"Code",t)},expression:"form.Code"}})],1),n("el-form-item",{attrs:{label:"类型",prop:"Type"}},[n("el-radio-group",{on:{change:e.changeType},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},[n("el-radio",{attrs:{label:1}},[e._v("构件工序")]),e.isVersionFour?n("el-radio",{attrs:{label:3}},[e._v("部件工序")]):e._e(),e.hiddenPart?e._e():n("el-radio",{attrs:{label:2}},[e._v("零件工序")])],1)],1),n("el-form-item",{attrs:{label:"排序",prop:"Sort"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0,"step-strictly":"",step:1,placeholder:"请输入",clearable:""},model:{value:e.form.Sort,callback:function(t){e.$set(e.form,"Sort",t)},expression:"form.Sort"}})],1),n("el-form-item",{attrs:{label:"协调人",prop:"Coordinate_UserId"}},[n("el-select",{staticClass:"w100",attrs:{clearable:"",filterable:"",placeholder:"请选择"},model:{value:e.form.Coordinate_UserId,callback:function(t){e.$set(e.form,"Coordinate_UserId",t)},expression:"form.Coordinate_UserId"}},e._l(e.optionsUserList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"是否外协",prop:"Is_External"}},[n("el-radio-group",{model:{value:e.form.Is_External,callback:function(t){e.$set(e.form,"Is_External",t)},expression:"form.Is_External"}},[n("el-radio",{attrs:{label:!0}},[e._v("是")]),n("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1),n("el-form-item",{attrs:{label:"是否质检",prop:"Is_Need_Check"}},[n("el-radio-group",{on:{change:e.radioChange},model:{value:e.form.Is_Need_Check,callback:function(t){e.$set(e.form,"Is_Need_Check",t)},expression:"form.Is_Need_Check"}},[n("el-radio",{attrs:{label:!0}},[e._v("是")]),n("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1),1===e.form.Type||3===e.form.Type?n("el-form-item",{attrs:{label:"是否装焊工序",prop:"Is_Welding_Assembling"}},[n("el-radio-group",{model:{value:e.form.Is_Welding_Assembling,callback:function(t){e.$set(e.form,"Is_Welding_Assembling",t)},expression:"form.Is_Welding_Assembling"}},[n("el-radio",{attrs:{label:!0}},[e._v("是")]),n("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1):e._e(),2===e.form.Type?n("el-form-item",{attrs:{label:"是否下料工序",prop:"Is_Cutting"}},[n("el-radio-group",{model:{value:e.form.Is_Cutting,callback:function(t){e.$set(e.form,"Is_Cutting",t)},expression:"form.Is_Cutting"}},[n("el-radio",{attrs:{label:!0}},[e._v("是")]),n("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1):e._e(),e.form.Is_Need_Check?[n("el-form-item",{attrs:{label:"质检方式",prop:"Check_Style"}},[n("el-radio-group",{on:{change:e.radioCheckStyleChange},model:{value:e.form.Check_Style,callback:function(t){e.$set(e.form,"Check_Style",t)},expression:"form.Check_Style"}},[n("el-radio",{attrs:{label:"0"}},[e._v("抽检")]),n("el-radio",{attrs:{label:"1"}},[e._v("全检")])],1)],1),n("el-form-item",{attrs:{label:"质检类型",prop:""}},["0"===e.form.Check_Style?n("el-radio-group",{on:{change:e.radioCheckTypeChange},model:{value:e.CheckChange,callback:function(t){e.CheckChange=t},expression:"CheckChange"}},[n("el-radio",{staticClass:"customRadioClass",attrs:{label:!0}},[n("div",{staticClass:"checkboxFlex"},[n("span",[e._v(" 探伤")]),e.CheckChange?n("div",{staticStyle:{"margin-left":"30px"}},[n("span",{staticStyle:{color:"rgba(34, 40, 52, 0.65)"}},[e._v("探伤员：")]),n("el-select",{attrs:{filterable:"",clearable:"",multiple:"",placeholder:"请选择探伤员"},on:{change:e.changeTc},model:{value:e.TC_Check_UserIds,callback:function(t){e.TC_Check_UserIds=t},expression:"TC_Check_UserIds"}},e._l(e.optionsUserList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1):e._e()])]),n("el-radio",{staticClass:"customRadioClass",attrs:{label:!1}},[n("div",{staticClass:"checkboxFlex"},[n("span",[e._v(" 质量")]),0==e.CheckChange?n("div",{staticStyle:{"margin-left":"30px"}},[n("span",{staticStyle:{color:"rgba(34, 40, 52, 0.65)"}},[e._v("质检员：")]),n("el-select",{attrs:{filterable:"",clearable:"",multiple:"",placeholder:"请选择质检员"},on:{change:e.changeZL},model:{value:e.ZL_Check_UserIds,callback:function(t){e.ZL_Check_UserIds=t},expression:"ZL_Check_UserIds"}},e._l(e.optionsUserList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1):e._e()])])],1):n("div",[n("div",[n("el-checkbox",{on:{change:function(t){return e.checkboxChange(t,1)}},model:{value:e.form.Is_Need_TC,callback:function(t){e.$set(e.form,"Is_Need_TC",t)},expression:"form.Is_Need_TC"}},[n("span",[e._v(" 探伤")])]),n("span",{staticStyle:{"margin-left":"30px"}},[n("span",{staticStyle:{color:"rgba(34, 40, 52, 0.65)"}},[e._v("探伤员：")]),n("el-select",{attrs:{filterable:"",clearable:"",disabled:!e.form.Is_Need_TC,multiple:"",placeholder:"请选择探伤员"},on:{change:e.changeTc},model:{value:e.TC_Check_UserIds,callback:function(t){e.TC_Check_UserIds=t},expression:"TC_Check_UserIds"}},e._l(e.optionsUserList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),n("div",[n("el-checkbox",{on:{change:function(t){return e.checkboxChange(t,2)}},model:{value:e.form.Is_Need_ZL,callback:function(t){e.$set(e.form,"Is_Need_ZL",t)},expression:"form.Is_Need_ZL"}},[n("span",[e._v(" 质量")])]),n("span",{staticStyle:{"margin-left":"30px"}},[n("span",{staticStyle:{color:"rgba(34, 40, 52, 0.65)"}},[e._v("质检员：")]),n("el-select",{attrs:{disabled:!e.form.Is_Need_ZL,filterable:"",clearable:"",multiple:"",placeholder:"请选择质检员"},on:{change:e.changeZL},model:{value:e.ZL_Check_UserIds,callback:function(t){e.ZL_Check_UserIds=t},expression:"ZL_Check_UserIds"}},e._l(e.optionsUserList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1)])],1)]:e._e(),n("el-form-item",{attrs:{label:"是否启用",prop:"Is_Enable"}},[n("el-radio-group",{model:{value:e.form.Is_Enable,callback:function(t){e.$set(e.form,"Is_Enable",t)},expression:"form.Is_Enable"}},[n("el-radio",{attrs:{label:!0}},[e._v("是")]),n("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1),2===e.form.Type?n("el-form-item",{attrs:{label:"是否套料工序",prop:"Is_Nest"}},[n("el-radio-group",{model:{value:e.form.Is_Nest,callback:function(t){e.$set(e.form,"Is_Nest",t)},expression:"form.Is_Nest"}},[n("el-radio",{attrs:{label:!0}},[e._v("是")]),n("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1):e._e(),n("el-form-item",{attrs:{label:"加工班组",prop:"Working_Team_Ids"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择加工班组"},model:{value:e.form.Working_Team_Ids,callback:function(t){e.$set(e.form,"Working_Team_Ids",t)},expression:"form.Working_Team_Ids"}},e._l(e.optionsWorkingTeamsList,(function(e){return n("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"备注"}},[n("el-input",{attrs:{type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],2)],1),n("div",{staticClass:"btn-x"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{loading:e.btnLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定 ")])],1)])},a=[]},"6ba0":function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("d3b7"),n("ac1f"),n("5319");var a=r(n("c14f")),o=r(n("1da1")),s=r(n("15fd")),i=r(n("5530")),l=n("6186"),c=n("a024"),u=n("2f62"),d=["Is_Nest"];t.default={props:{type:{type:String,default:""},rowInfo:{type:Object,default:function(){return{}}}},data:function(){return{checkList:[],btnLoading:!1,hiddenPart:!1,form:{Code:"",Name:"",Type:"",Coordinate_UserId:"",Sort:void 0,Is_Enable:!0,Is_External:!1,Is_Nest:!1,Is_Need_Check:!0,Is_Need_TC:!0,Is_Welding_Assembling:void 0,Is_Cutting:void 0,TC_Check_UserId:"",Is_Need_ZL:!1,ZL_Check_UserId:"",Check_Style:"0",Working_Team_Ids:[],Remark:""},ZL_Check_UserIds:[],TC_Check_UserIds:[],CheckChange:!0,userOptions:[],optionsUserList:[],optionsGroupList:[],optionsWorkingTeamsList:[],rules:{Code:[{required:!0,message:"请输入代号",trigger:"blur"},{max:30,message:"长度在 30 个字符内",trigger:"blur"}],Name:[{required:!0,message:"请输入名称",trigger:"blur"},{max:30,message:"长度在 30 个字符内",trigger:"blur"}],Type:[{required:!0,message:"请选择类型",trigger:"change"}],Sort:[{required:!0,message:"请输入",trigger:"blur"}],Is_Need_Check:[{required:!0,message:"请选择是否质检",trigger:"change"}]}}},computed:(0,i.default)({},(0,u.mapGetters)("tenant",["isVersionFour"])),mounted:function(){this.getUserList(),this.getFactoryPeoplelist(),this.getWorkingTeamsList(),"edit"===this.type&&this.initForm()},methods:{initForm:function(){var e=this.rowInfo,t=e.Is_Nest,n=(0,s.default)(e,d);this.form=Object.assign({},n,{Is_Nest:!!t}),this.form.Is_Need_Check&&("1"===this.form.Check_Style||(this.CheckChange=!!this.form.Is_Need_TC,this.form.Is_Need_ZL&&this.form.Is_Need_TC&&(this.form.Is_Need_TC=!0,this.form.Is_Need_ZL=!1))),this.ZL_Check_UserIds=this.form.ZL_Check_UserId?this.form.ZL_Check_UserId.split(","):[],this.TC_Check_UserIds=this.form.TC_Check_UserId?this.form.TC_Check_UserId.split(","):[]},getUserList:function(){var e=this;(0,l.GetUserList)({}).then((function(t){t.IsSucceed?e.userOptions=t.Data:e.$message({message:t.Message,type:"error"})}))},getFactoryPeoplelist:function(){var e=this;(0,c.GetFactoryPeoplelist)({}).then((function(t){t.IsSucceed?e.optionsUserList=t.Data:e.$message({message:t.Message,type:"error"})}))},getCheckGroupList:function(){var e=this;return(0,o.default)((0,a.default)().m((function t(){return(0,a.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetCheckGroupList)({}).then((function(t){t.IsSucceed?e.optionsGroupList=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:case 2:return t.a(2)}}),t)})))()},getWorkingTeamsList:function(){var e=this;(0,c.GetWorkingTeams)({}).then((function(t){t.IsSucceed?e.optionsWorkingTeamsList=t.Data:e.$message({message:t.Message,type:"error"})}))},radioCheckStyleChange:function(e){"0"===e&&(this.form.Is_Need_TC=!0,this.form.Is_Need_ZL=!1),this.ZL_Check_UserIds=[],this.TC_Check_UserIds=[],this.form.ZL_Check_UserId="",this.form.TC_Check_UserId=""},radioCheckTypeChange:function(e){e?(this.form.Is_Need_TC=!0,this.form.Is_Need_ZL=!1,this.ZL_Check_UserIds=[],this.form.ZL_Check_UserId=""):(this.form.Is_Need_ZL=!0,this.form.Is_Need_TC=!1,this.TC_Check_UserIds=[],this.form.TC_Check_UserId="")},radioChange:function(e){0==e?(this.form.checkChange=!1,this.form.Is_Need_TC=!1,this.form.Is_Need_ZL=!1,this.TC_Check_UserIds=[],this.ZL_Check_UserIds=[],this.form.ZL_Check_UserId="",this.form.TC_Check_UserId="",this.form.Check_Style=""):(this.form.checkChange=!0,this.form.Is_Need_TC=!0,this.form.Is_Need_ZL=!1,this.CheckChange=!!this.form.Is_Need_TC,this.form.Check_Style="0")},changeType:function(){1===this.form.Type||3===this.form.Type?this.form.Is_Cutting=void 0:2===this.form.Type&&(this.form.Is_Welding_Assembling=void 0)},typeChange:function(){this.form.Task_Model=""},changeTc:function(e){this.form.TC_Check_UserId="";for(var t=0;t<e.length;t++)t==e.length-1?this.form.TC_Check_UserId+=e[t]:this.form.TC_Check_UserId+=e[t]+","},changeZL:function(e){this.form.ZL_Check_UserId="";for(var t=0;t<e.length;t++)t==e.length-1?this.form.ZL_Check_UserId+=e[t]:this.form.ZL_Check_UserId+=e[t]+","},checkboxChange:function(e,t){1===t&&(e||(this.TC_Check_UserIds=[])),2===t&&(e||(this.ZL_Check_UserIds=[]))},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(t){e.btnLoading=!0;var n=e.optionsUserList.find((function(t){return t.Id===e.form.Coordinate_UserId}));if(n&&(e.form.Coordinate_UserName=n.Display_Name),e.form.Is_Need_Check){if(0==e.form.Is_Need_ZL&&0==e.form.Is_Need_TC)return e.$message.error("请选择质检类型"),void(e.btnLoading=!1)}else e.form.Check_Style=null,e.form.Check_Group_List=[];var r=e.form.Is_Need_ZL?e.form.ZL_Check_UserId:"",a=e.form.Is_Need_TC?e.form.TC_Check_UserId:"";return e.form.Is_Need_ZL&&""==(null!==r&&void 0!==r?r:"")?(e.$message.error("请选择质检员"),void(e.btnLoading=!1)):e.form.Is_Need_TC&&""==(null!==a&&void 0!==a?a:"")?(e.$message.error("请选择探伤员"),void(e.btnLoading=!1)):void(0,c.AddWorkingProcess)((0,i.default)((0,i.default)({},e.form),{},{ZL_Check_UserId:r,TC_Check_UserId:a})).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.$emit("refresh"),e.$emit("close")):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}}))},codeChange:function(e){return e.replace(/[^a-zA-Z0-9]/g,"")}}}},"70ba":function(e,t,n){"use strict";n("fc22")},7319:function(e,t,n){"use strict";n.r(t);var r=n("9f90"),a=n("ccb0");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);var s=n("2877"),i=Object(s["a"])(a["default"],r["a"],r["b"],!1,null,"3452bd14",null);t["default"]=i.exports},"7a32":function(e,t,n){},8693:function(e,t,n){"use strict";n("7a32")},"8dd8":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("e9f5"),n("910d"),n("d3b7");var r=n("2a7f"),a=n("a024");t.default={data:function(){return{list:[],btnLoading:!1,selectList:[]}},mounted:function(){this.getProcessList()},methods:{getTypeList:function(){var e=this;(0,r.GetConsumingProcessAllList)({}).then((function(t){t.IsSucceed?e.list=t.Data:e.$message({message:t.Message,type:"error"})}))},getProcessList:function(){var e=this;(0,a.GetProcessList)({type:1}).then((function(t){e.selectList=t.Data})).finally((function(){e.getTypeList()}))},handleSubmit:function(){var e=this;this.btnLoading=!0,(0,r.SaveConsumingProcessAllList)(this.list.filter((function(e){return e.Working_Process_Id}))).then((function(t){t.IsSucceed?(e.$message.success("保存成功"),e.$emit("close")):e.$message.error(t.Message)})).finally((function(){e.btnLoading=!1}))},mainBlur:function(e){}}}},9257:function(e,t,n){"use strict";n.r(t);var r=n("8dd8"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},"9bd3":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.pageLoading,expression:"pageLoading"}],staticClass:"cs-z-page-main-content"},[n("top-header",{attrs:{padding:"0"},scopedSlots:e._u([{key:"left",fn:function(){return[n("div",[n("el-button",{attrs:{icon:"el-icon-plus",type:"primary",size:"small"},on:{click:function(t){return e.handleDialog("add")}}},[e._v("新增")]),n("el-button",{attrs:{type:"success",size:"small"},on:{click:e.handleConfig}},[e._v("零件识别配置")]),n("el-button",{attrs:{type:"success",size:"small"},on:{click:e.handleConfigComp}},[e._v("构件识别配置")]),n("el-button",{attrs:{type:"success",size:"small"},on:{click:e.handleTakeConfig}},[e._v("零件领用配置")])],1)]},proxy:!0},{key:"right",fn:function(){return[n("el-form",{staticClass:"demo-form-inline",staticStyle:{"line-height":"32px"},attrs:{inline:!0,model:e.formInline}},[n("el-form-item",{attrs:{label:"工序名称"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入工序名称"},model:{value:e.formInline.name,callback:function(t){e.$set(e.formInline,"name",t)},expression:"formInline.name"}})],1),n("el-form-item",{attrs:{label:"代号"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入代号"},model:{value:e.formInline.code,callback:function(t){e.$set(e.formInline,"code",t)},expression:"formInline.code"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.fetchData}},[e._v("查询")])],1)],1)]},proxy:!0}])}),n("el-table",{staticClass:"cs-custom-table tb",staticStyle:{width:"100%"},attrs:{border:"",stripe:"",height:"100%",data:e.tableData},scopedSlots:e._u([{key:"empty",fn:function(){return[n("ElTableEmpty")]},proxy:!0}])},[n("el-table-column",{attrs:{prop:"Professional_Code",label:"专业"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.Professional_Code||"-")+" ")]}}])}),n("el-table-column",{attrs:{"min-width":"150px",prop:"Name",label:"工序名称"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.Name||"-")+" ")]}}])}),n("el-table-column",{attrs:{"min-width":"120px",prop:"Code",label:"代号"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.Code||"-")+" ")]}}])}),n("el-table-column",{attrs:{"min-width":"150px",prop:"Type_Name",label:"类型"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[1===r.Type?n("span",{staticStyle:{color:"#d29730"}},[n("i",{staticClass:"iconfont icon-steel cs-tb-icon"}),e._v(" 构件工序 ")]):e._e(),2===r.Type?n("span",{staticStyle:{color:"#20bbc7"}},[n("i",{staticClass:"iconfont icon-material-filled cs-tb-icon"}),e._v(" 零件工序 ")]):e._e(),3===r.Type?n("span",{staticStyle:{color:"#de85e4"}},[n("i",{staticClass:"iconfont icon-material-filled cs-tb-icon"}),e._v(" 部件工序 ")]):e._e()]}}])}),n("el-table-column",{attrs:{"min-width":"150px",prop:"Coordinate_UserName",label:"工序协调人",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.Coordinate_UserName||"-")+" ")]}}])}),n("el-table-column",{attrs:{"min-width":"150px",prop:"Is_Need_Check",label:"必须质检",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.Is_Need_Check?n("el-tag",{attrs:{type:"success"}},[e._v("是")]):n("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}])}),n("el-table-column",{attrs:{"min-width":"150px",prop:"Check_Style",label:"质检方式"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(0==n.Check_Style?"抽检":1==n.Check_Style?"全检":"-")+" ")]}}])}),n("el-table-column",{attrs:{"min-width":"150px",prop:"Team_Names",label:"加工班组","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.Team_Names.length?n("div",e._l(r.Team_Names.split(";"),(function(t,r){return n("el-tag",{key:r,staticClass:"cs-tag"},[e._v(" "+e._s(t)+" ")])})),1):n("div",[e._v(e._s("-"))])]}}])}),n("el-table-column",{attrs:{"min-width":"120px",prop:"Is_Enable",label:"是否启用",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.Is_Enable?n("el-tag",{attrs:{type:"success"}},[e._v("是")]):n("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}])}),n("el-table-column",{attrs:{"min-width":"150px",prop:"Is_Nest",label:"是否套料工序",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[2===r.Type?n("div",[r.Is_Nest?n("el-tag",{attrs:{type:"success"}},[e._v("是")]):n("el-tag",{attrs:{type:"danger"}},[e._v("否")])],1):n("div",[e._v(" "+e._s("-")+" ")])]}}])}),n("el-table-column",{attrs:{prop:"Remark",label:"备注",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._v(" "+e._s(n.Remark||"-")+" ")]}}])}),n("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[n("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handleDialog("edit",r)}}},[e._v("编辑")]),n("el-button",{staticClass:"btn-del txt-red",attrs:{type:"text",size:"small"},on:{click:function(t){return e.handleDelete(r.Id)}}},[e._v("删除")])]}}])})],1),e.dialogVisible?n("el-dialog",{directives:[{name:"dialog-drag",rawName:"v-dialog-drag"}],staticClass:"cs-dialog",attrs:{title:e.title,visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"530px",top:"5vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"row-info":e.rowInfo,type:e.type,"dialog-visible":e.dialogVisible},on:{close:e.handleClose,refresh:e.fetchData}})],1):e._e()],1)])},a=[]},"9f2e":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"form-wrapper"},[n("div",{staticClass:"form-x"},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("el-radio-group",{model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}},[n("el-radio",{attrs:{label:!1}},[e._v("否")]),n("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1),e.form.enable?e._l(e.list,(function(t,r){return n("el-form-item",{key:r,attrs:{"show-message":!1,label:t.Comp_Type_Name,prop:"mainPart"}},[n("el-input",{attrs:{placeholder:"请输入（多个使用'"+e.splitSymbol+"'隔开），单个配置不超过10个字符",clearable:""},on:{blur:e.mainBlur},model:{value:e.form["item"+r],callback:function(t){e.$set(e.form,"item"+r,"string"===typeof t?t.trim():t)},expression:"form['item'+index]"}})],1)})):e._e()],2)],1),n("div",{staticClass:"btn-x"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)])},a=[]},"9f90":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-transfer",{attrs:{filterable:"","button-texts":["移除","添加"],"filter-method":e.filterMethod,titles:["全部班组","已选班组"],"filter-placeholder":"搜索...",data:e.allList,props:{label:"Display_Name",value:"Id",key:"Id"}},on:{change:e.change},model:{value:e.selectList,callback:function(t){e.selectList=t},expression:"selectList"}}),n("div",{staticStyle:{"text-align":"right","margin-top":"10px"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1)},a=[]},a024:function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=c,t.AddProessLib=U,t.AddTechnology=l,t.AddWorkingProcess=i,t.DelLib=D,t.DeleteProcess=k,t.DeleteProcessFlow=C,t.DeleteTechnology=T,t.DeleteWorkingTeams=S,t.GetAllProcessList=f,t.GetCheckGroupList=$,t.GetChildComponentTypeList=R,t.GetFactoryAllProcessList=m,t.GetFactoryPeoplelist=w,t.GetFactoryWorkingTeam=_,t.GetGroupItemsList=v,t.GetLibList=s,t.GetLibListType=W,t.GetProcessFlow=p,t.GetProcessFlowListWithTechnology=h,t.GetProcessList=u,t.GetProcessListBase=d,t.GetProcessListTeamBase=G,t.GetProcessListWithUserBase=N,t.GetProcessWorkingTeamBase=Z,t.GetTeamListByUser=A,t.GetTeamProcessList=y,t.GetWorkingTeam=b,t.GetWorkingTeamBase=O,t.GetWorkingTeamInfo=x,t.GetWorkingTeams=I,t.GetWorkingTeamsPageList=L,t.SaveWorkingTeams=P,t.UpdateProcessTeam=g;var a=r(n("b775")),o=r(n("4328"));function s(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function i(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:o.default.stringify(e)})}function l(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:o.default.stringify(e)})}function c(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:o.default.stringify(e)})}function u(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:o.default.stringify(e)})}function d(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:o.default.stringify(e)})}function f(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:o.default.stringify(e)})}function m(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function p(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:o.default.stringify(e)})}function h(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function g(e){return(0,a.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:o.default.stringify(e)})}function _(){return(0,a.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function b(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:o.default.stringify(e)})}function y(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:o.default.stringify(e)})}function v(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:o.default.stringify(e)})}function C(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:o.default.stringify(e)})}function T(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:o.default.stringify(e)})}function k(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function I(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function L(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function P(e){return(0,a.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function S(e){return(0,a.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function x(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:o.default.stringify(e)})}function O(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:o.default.stringify(e)})}function G(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:o.default.stringify(e)})}function N(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function w(e){return(0,a.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function $(e){return(0,a.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function U(e){return(0,a.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function R(e){return(0,a.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function D(e){return(0,a.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function W(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function Z(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function A(e){return(0,a.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},a29a:function(e,t,n){"use strict";n.r(t);var r=n("3dcd"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},a400:function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("5530")),o=r(n("2909"));n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7"),n("ac1f"),n("00b4"),n("1276");var s=n("ed08"),i=n("2a7f"),l="|";t.default={data:function(){return{form:{enable:!1,identifyAttr:1},list:[],splitSymbol:l,btnLoading:!1}},mounted:function(){this.getTypeList()},methods:{getTypeList:function(){var e=this;(0,i.GetFactoryPartTypeIndentifySetting)({}).then((function(t){if(t.IsSucceed){var n=t.Data,r=n.Is_Enabled,a=n.Setting_List;if(e.form.enable=r,e.list=a.map((function(t,n){return e.$set(e.form,"item"+n,t.Prefixs||""),t})),a.length>0){var o=a[0].Identify_Attr;e.form.identifyAttr=1===o||2===o?o:1}}else e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;if(this.form.enable){for(var t=[],n=0;n<this.list.length;n++){var r=/^(?!.*\|\|)(?!.*\|$)(?!^\|)[^|]{1,10}(?:\|[^|]{1,10})*$/;if(!r.test(this.form["item".concat(n)]))return void this.$message({message:"".concat(this.list[n].Part_Type_Name,"配置不符合要求"),type:"warning"});var l=this.form["item".concat(n)].split(this.splitSymbol).filter((function(e){return!!e}));if(0===l.length)return void this.$message({message:"".concat(this.list[n].Part_Type_Name,"不能为空"),type:"warning"});for(var c=0;c<l.length;c++){var u=l[c];if(u.length>10)return void this.$message({message:"".concat(this.list[n].Part_Type_Name,"单个配置，不能超过10个字符"),type:"warning"})}t.push.apply(t,(0,o.default)(l))}var d=(0,s.uniqueArr)(t);if(d.length!==t.length)return void this.$message({message:"配置不能相同",type:"warning"})}this.btnLoading=!0,(0,i.SavePartTypeIdentifySetting)({Is_Enabled:this.form.enable,Setting_List:this.list.map((function(t,n){return(0,a.default)((0,a.default)({},t),{},{Prefixs:e.form["item".concat(n)],Identify_Attr:e.form.identifyAttr})}))}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close")):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))},mainBlur:function(e){}}}},aa99:function(e,t,n){},ab0d:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"form-wrapper"},[n("div",{staticClass:"form-x"},[n("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px"}},[n("el-form-item",{attrs:{label:"是否启用",prop:"enable"}},[n("el-radio-group",{model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}},[n("el-radio",{attrs:{label:!1}},[e._v("否")]),n("el-radio",{attrs:{label:!0}},[e._v("是")])],1)],1),e.form.enable?[n("el-form-item",{attrs:{label:"识别类型",prop:"identifyAttr"}},[n("el-radio-group",{model:{value:e.form.identifyAttr,callback:function(t){e.$set(e.form,"identifyAttr",t)},expression:"form.identifyAttr"}},[n("el-radio",{attrs:{label:1}},[e._v("零件名称前缀")]),n("el-radio",{attrs:{label:2}},[e._v("零件规格前缀")])],1)],1),e._l(e.list,(function(t,r){return n("el-form-item",{key:r,attrs:{"show-message":!1,label:t.Part_Type_Name,prop:"mainPart"}},[n("el-input",{attrs:{placeholder:"请输入（多个使用'"+e.splitSymbol+"'隔开），单个配置不超过10个字符",clearable:""},on:{blur:e.mainBlur},model:{value:e.form["item"+r],callback:function(t){e.$set(e.form,"item"+r,"string"===typeof t?t.trim():t)},expression:"form['item'+index]"}})],1)}))]:e._e()],2)],1),n("div",{staticClass:"btn-x"},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.handleSubmit}},[e._v("确 定")])],1)])},a=[]},adf1:function(e,t,n){"use strict";n.r(t);var r=n("9f2e"),a=n("c6d9");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("8693");var s=n("2877"),i=Object(s["a"])(a["default"],r["a"],r["b"],!1,null,"42ce45a6",null);t["default"]=i.exports},c6d9:function(e,t,n){"use strict";n.r(t);var r=n("e282"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},ccb0:function(e,t,n){"use strict";n.r(t);var r=n("1f50"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},ccb5:function(e,t,n){"use strict";function r(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},e282:function(e,t,n){"use strict";var r=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("5530")),o=r(n("2909"));n("4de4"),n("d81d"),n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("d3b7"),n("ac1f"),n("00b4"),n("1276");var s=n("ed08"),i=n("2e8a"),l="|";t.default={data:function(){return{form:{enable:!1},list:[],splitSymbol:l,btnLoading:!1}},mounted:function(){this.getTypeList()},methods:{getTypeList:function(){var e=this;(0,i.GetFactoryCompTypeIndentifySetting)({}).then((function(t){if(t.IsSucceed){var n=t.Data,r=n.Is_Enabled,a=n.Setting_List;e.form.enable=r,e.list=a.map((function(t,n){return e.$set(e.form,"item"+n,t.Prefixs||""),t}))}else e.$message({message:t.Message,type:"error"})}))},handleSubmit:function(){var e=this;if(this.form.enable){for(var t=[],n=0;n<this.list.length;n++){var r=/^(?!.*\|\|)(?!.*\|$)(?!^\|)[^|]{0,10}(?:\|[^|]{0,10})*$/;if(!r.test(this.form["item".concat(n)]))return void this.$message({message:"".concat(this.list[n].Comp_Type_Name,"配置不符合要求"),type:"warning"});for(var l=this.form["item".concat(n)].split(this.splitSymbol).filter((function(e){return!!e})),c=0;c<l.length;c++){var u=l[c];if(u.length>10)return void this.$message({message:"".concat(this.list[n].Comp_Type_Name,"单个配置，不能超过10个字符"),type:"warning"})}t.push.apply(t,(0,o.default)(l))}var d=(0,s.uniqueArr)(t);if(d.length!==t.length)return void this.$message({message:"配置不能相同",type:"warning"})}this.btnLoading=!0,(0,i.SaveCompTypeIdentifySetting)({Is_Enabled:this.form.enable,Setting_List:this.list.map((function(t,n){return(0,a.default)((0,a.default)({},t),{},{Prefixs:e.form["item".concat(n)]})}))}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.$emit("close")):e.$message({message:t.Message,type:"error"})})).finally((function(){e.btnLoading=!1}))},mainBlur:function(e){}}}},f2a1:function(e,t,n){"use strict";n.r(t);var r=n("6ba0"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},f9348:function(e,t,n){"use strict";n("57ac")},fae7:function(e,t,n){"use strict";n.r(t);var r=n("5127"),a=n("9257");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);var s=n("2877"),i=Object(s["a"])(a["default"],r["a"],r["b"],!1,null,"701a71b3",null);t["default"]=i.exports},fc22:function(e,t,n){}}]);