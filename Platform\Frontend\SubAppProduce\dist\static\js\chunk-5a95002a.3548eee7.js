(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5a95002a"],{"0187":function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteRole=i,e.GetRoleMenusObj=f,e.GetRoleTree=n,e.GetRoleWorkingObjListByUser=s,e.GetUserListByRole=l,e.GetUserRoleTreeWithoutObject=p,e.GetWorkingObjTree=m,e.SaveDepartmentObject=h,e.SaveRole=u,e.SaveRoleMenu=c,e.SaveUserAuthorize=d,e.SaveUserObject=S;var a=o(r("b775"));function n(){return(0,a.default)({url:"/SYS/Role/GetRoleTree",method:"post"})}function u(t){return(0,a.default)({url:"/SYS/Role/SaveRole",method:"post",data:t})}function i(t){return(0,a.default)({url:"/SYS/Role/DeleteRole",method:"post",data:t})}function l(t){return(0,a.default)({url:"/SYS/Role/GetUserListByRole",method:"post",data:t})}function d(t){return(0,a.default)({url:"/SYS/Role/SaveUserAuthorize",method:"post",data:t})}function s(t){return(0,a.default)({url:"/SYS/Role/GetRoleWorkingObjListByUser",method:"post",data:t})}function c(t){return(0,a.default)({url:"/SYS/Role/SaveRoleMenu",method:"post",data:t})}function f(t){return(0,a.default)({url:"/SYS/Role/GetRoleMenusObj",method:"post",data:t})}function p(t){return(0,a.default)({url:"/SYS/User/GetUserRoleTreeWithoutObject",method:"post",data:t})}function m(t){return(0,a.default)({url:"/SYS/User/GetWorkingObjTree",method:"post",data:t})}function S(t){return(0,a.default)({url:"/SYS/User/SaveUserObject",method:"post",data:t})}function h(t){return(0,a.default)({url:"/SYS/User/SaveDepartmentObject",method:"post",data:t})}},"09f4":function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scrollTo=u,Math.easeInOutQuad=function(t,e,r,o){return t/=o/2,t<1?r/2*t*t+e:(t--,-r/2*(t*(t-2)-1)+e)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function a(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function u(t,e,r){var u=n(),i=t-u,l=20,d=0;e="undefined"===typeof e?500:e;var s=function(){d+=l;var t=Math.easeInOutQuad(d,u,i,e);a(t),d<e?o(s):r&&"function"===typeof r&&r()};s()}},"15ac":function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("4de4"),r("d81d"),r("14d9"),r("e9f5"),r("910d"),r("ab43"),r("d3b7");var o=r("6186"),a=r("c685");e.default={methods:{getTableConfig:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(n){(0,o.GetGridByCode)({code:t,IsAll:r}).then((function(t){var o=t.IsSucceed,u=t.Data,i=t.Message;if(o){if(!u)return void e.$message({message:"表格配置不存在",type:"error"});var l=[];e.tbConfig=Object.assign({},e.tbConfig,u.Grid),l=r?(null===u||void 0===u?void 0:u.ColumnList)||[]:(null===u||void 0===u?void 0:u.ColumnList.filter((function(t){return t.Is_Display})))||[],e.columns=l.map((function(t){return t.Is_Resizable=!0,t.Is_Sortable=!0,t.Align=t.Align||"left",t})),e.queryInfo&&(e.queryInfo.PageSize=+u.Grid.Row_Number||a.tablePageSize[0]),n(e.columns)}else e.$message({message:i,type:"error"})}))}))},handlePageChange:function(t){var e=t.page,r=t.size;this.tbConfig.Row_Number=parseInt(r||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(r||this.tbConfig.Row_Number),this.queryInfo.Page=r?1:e,this.fetchData()},pageChange:function(t){var e=t.page,r=t.limit,o=t.type;this.queryInfo.Page="limit"===o?1:e,this.queryInfo.PageSize=r,this.fetchData()},tableSearch:function(t){for(var e in this.queryInfo.ParameterJson=[],t){var r={Key:e,Type:"",Filter_Type:""};t[e]instanceof Array?r.Value=t[e]:r.Value=[t[e]];for(var o=0;o<this.columns.length;o++){var a=this.columns[o];if(a.Code===e){r.Type=a.Type,r.Filter_Type=a.Filter_Type;break}}t[e]&&t[e].length&&this.queryInfo.ParameterJson.push(r)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(t){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},3166:function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BindBimProject=f,e.CancelBindBimProject=p,e.DeleteProject=s,e.GeAreaTrees=G,e.GetFileSync=b,e.GetInstallUnitIdNameList=I,e.GetNoBindProjectList=m,e.GetPartDeepenFileList=w,e.GetProjectAreaTreeList=g,e.GetProjectEntity=l,e.GetProjectList=i,e.GetProjectPageList=u,e.GetProjectTemplate=S,e.GetPushProjectPageList=y,e.GetSchedulingPartList=M,e.IsEnableProjectMonomer=c,e.SaveProject=d,e.UpdateProjectTemplateBase=h,e.UpdateProjectTemplateContacts=P,e.UpdateProjectTemplateContract=R,e.UpdateProjectTemplateOther=O;var a=o(r("b775")),n=o(r("4328"));function u(t){return(0,a.default)({url:"/PRO/Project/GetProjectPageList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/PRO/Project/GetProjectList",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/Project/GetProjectEntity",method:"post",data:n.default.stringify(t)})}function d(t){return(0,a.default)({url:"/PRO/Project/SaveProject",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/Project/DeleteProject",method:"post",data:n.default.stringify(t)})}function c(t){return(0,a.default)({url:"/PRO/Project/IsEnableProjectMonomer",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/Project/BindBimProject",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/Project/CancelBindBimProject",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/Project/GetNoBindProjectList",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/Project/GetProjectTemplate",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/Project/UpdateProjectTemplateBase",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/Project/UpdateProjectTemplateContacts",method:"post",data:t})}function R(t){return(0,a.default)({url:"/PRO/Project/UpdateProjectTemplateContract",method:"post",data:t})}function O(t){return(0,a.default)({url:"/PRO/Project/UpdateProjectTemplateOther",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/Project/GetPushProjectPageList",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function I(t){return(0,a.default)({url:"/PRO/InstallUnit/GetInstallUnitIdNameList",method:"post",data:t})}function G(t){return(0,a.default)({url:"/PRO/Project/GeAreaTrees",method:"post",data:t})}function w(t){return(0,a.default)({url:"/PRO/Component/GetPartDeepenFileList",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/Part/GetSchedulingPartList",method:"post",data:t})}function b(t){return(0,a.default)({url:"SYS/Sys_FileType/GetFileSync",method:"post",data:t})}},"3c4a":function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxOutExport=$,e.AuxReturnByReceipt=st,e.EditAuxOutStatus=B,e.EditOutStatus=v,e.ExportFlow=h,e.ExportProjectRawAnalyse=pt,e.ExportRawForProject=ht,e.FindAuxFlowList=ut,e.FindAuxPageList=k,e.FindPageList=N,e.FindProjectRawAnalyse=Z,e.FindRawFlowList=at,e.FindRawPageList=H,e.FindReturnStoreNewPageList=O,e.FindReturnStoreNewSum=y,e.FindStoreDetail=W,e.GetAuxBatchInventoryDetailList=ft,e.GetAuxDetailByReceipt=dt,e.GetAuxInventoryDetailList=s,e.GetAuxInventoryPageList=d,e.GetAuxSummary=F,e.GetAuxWarningPageList=c,e.GetCurUserCompanyId=x,e.GetFirstLevelDepartsUnderFactory=A,e.GetInPageList=f,e.GetInPageListSum=S,e.GetMaterielRawOutStoreList=R,e.GetOutFromSourceData=b,e.GetOutPageList=p,e.GetOutSum=m,e.GetPickOutDetail=E,e.GetProjectRawAnalyseByProject=nt,e.GetProjectRawAnalyseDetail=tt,e.GetProjectRawAnalyseSum=et,e.GetRawBatchInventoryDetailList=ct,e.GetRawDetailByReceipt=lt,e.GetRawFilterDataSummary=Rt,e.GetRawForProjectDetail=Pt,e.GetRawForProjectPageList=St,e.GetRawInventoryDetailList=u,e.GetRawInventoryPageList=n,e.GetRawSummary=l,e.GetRawWHSummaryList=mt,e.GetRawWarningPageList=i,e.GetTeamListByUserForMateriel=Ot,e.GetUserPage=L,e.List=P,e.ListDetail=M,e.OutSourcingOutStore=I,e.OutSourcingOutStoreDetail=w,e.PartyAAuxOutStore=T,e.PartyAAuxOutStoreDetail=j,e.PartyAOutStore=Q,e.PartyAOutStoreDetail=K,e.PickOutStore=U,e.PickUpOutStore=g,e.PickUpOutStoreDetail=G,e.RawOutExport=D,e.RawReturnByReceipt=it,e.SelfAuxReturnOutStore=C,e.SelfAuxReturnOutStoreDetail=_,e.SelfReturnOutStore=J,e.SelfReturnOutStoreDetail=X,e.SetAuxLT=ot,e.SetAuxLock=q,e.SetAuxUnlock=z,e.SetRawLT=rt,e.SetRawLock=V,e.SetRawUnlock=Y,e.TransferRawLock=yt;var a=o(r("b775"));function n(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawInventoryPageList",method:"post",data:t})}function u(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawInventoryDetailList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawWarningPageList",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawSummary",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetAuxInventoryPageList",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetAuxInventoryDetailList",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetAuxWarningPageList",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/MaterielFlow/GetInPageList",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/MaterielFlow/GetOutPageList",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/MaterielFlow/GetOutSum",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/MaterielFlow/GetInPageListSum",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/MaterielFlow/ExportFlow",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/List",method:"post",data:t})}function R(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/List",method:"post",data:t})}function O(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/FindReturnStoreNewPageList",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/FindReturnStoreNewSum",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/PickUpOutStore",method:"post",data:t})}function I(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/OutSourcingOutStore",method:"post",data:t})}function G(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/PickUpOutStoreDetail",method:"post",data:t})}function w(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/OutSourcingOutStoreDetail",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/ListDetail",method:"post",data:t})}function b(t){return(0,a.default)({url:"/PRO/MaterielRawOutStoreNew/GetOutFromSourceData",method:"post",data:t})}function v(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/EditOutStatus",method:"post",data:t})}function D(t){return(0,a.default)({url:"/PRO/MaterielRawOutStore/Export",method:"post",data:t})}function A(t){return(0,a.default)({url:"/OMA/Common/GetFirstLevelDepartsUnderFactory",method:"post",data:t})}function L(t){return(0,a.default)({url:"/SYS/User/GetUserPage",method:"post",data:t})}function x(t){return(0,a.default)({url:"/PRO/Communal/GetCurUserCompanyId",method:"post",data:t})}function k(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/FindAuxPageList",method:"post",data:t})}function C(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/SelfAuxReturnOutStore",method:"post",data:t})}function T(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/PartyAAuxOutStore",method:"post",data:t})}function _(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/SelfAuxReturnOutStoreDetail",method:"post",data:t})}function j(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/PartyAAuxOutStoreDetail",method:"post",data:t})}function N(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/FindPageList",method:"post",data:t})}function F(t){return(0,a.default)({url:"PRO/MaterielInventory/GetAuxSummary",method:"post",data:t})}function U(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/PickOutStore",method:"post",data:t})}function E(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/GetPickOutDetail",method:"post",data:t})}function B(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/EditOutStatus",method:"post",data:t})}function $(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/Export",method:"post",data:t})}function W(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/GetOutFromSourceData ",method:"post",data:t})}function V(t){return(0,a.default)({url:"/PRO/MaterielAssign/SetRawLock",method:"post",data:t})}function Y(t){return(0,a.default)({url:"/PRO/MaterielAssign/SetRawUnlock",method:"post",data:t})}function q(t){return(0,a.default)({url:"/PRO/MaterielAssign/SetAuxLock",method:"post",data:t})}function z(t){return(0,a.default)({url:"/PRO/MaterielAssign/SetAuxUnlock",method:"post",data:t})}function H(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/FindRawPageList",method:"post",data:t})}function J(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/SelfReturnOutStore",method:"post",data:t})}function Q(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/PartyAOutStore",method:"post",data:t})}function K(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/PartyAOutStoreDetail",method:"post",data:t})}function X(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/SelfReturnOutStoreDetail",method:"post",data:t})}function Z(t){return(0,a.default)({url:"/PRO/MaterielReport/FindProjectRawAnalyse",method:"post",data:t})}function tt(t){return(0,a.default)({url:"/PRO/MaterielReport/GetProjectRawAnalyseDetail",method:"post",data:t})}function et(t){return(0,a.default)({url:"/PRO/MaterielReport/GetProjectRawAnalyseSum",method:"post",data:t})}function rt(t){return(0,a.default)({url:"/PRO/MaterielInventory/SetRawLT",method:"post",data:t})}function ot(t){return(0,a.default)({url:"/PRO/MaterielInventory/SetAuxLT",method:"post",data:t})}function at(t){return(0,a.default)({url:"/PRO/MaterielInventory/FindRawFlowList",method:"post",data:t})}function nt(t){return(0,a.default)({url:"/pro/MaterielReport/GetProjectRawAnalyseByProject",method:"post",data:t})}function ut(t){return(0,a.default)({url:"/pro/MaterielInventory/FindAuxFlowList",method:"post",data:t})}function it(t){return(0,a.default)({url:"/pro/MaterielReturnStore/RawReturnByReceipt",method:"post",data:t})}function lt(t){return(0,a.default)({url:"/pro/MaterielReturnStore/GetRawDetailByReceipt",method:"post",data:t})}function dt(t){return(0,a.default)({url:"/pro/MaterielReturnStore/GetAuxDetailByReceipt",method:"post",data:t})}function st(t){return(0,a.default)({url:"/pro/MaterielReturnStore/AuxReturnByReceipt",method:"post",data:t})}function ct(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawBatchInventoryDetailList",method:"post",data:t})}function ft(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetAuxBatchInventoryDetailList",method:"post",data:t})}function pt(t){return(0,a.default)({url:"/PRO/MaterielReport/ExportProjectRawAnalyse",method:"post",data:t})}function mt(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawWHSummaryList",method:"post",data:t})}function St(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawForProjectPageList",method:"post",data:t})}function ht(t){return(0,a.default)({url:"/PRO/MaterielInventory/ExportRawForProject",method:"post",data:t})}function Pt(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawForProjectDetail",method:"post",data:t})}function Rt(t){return(0,a.default)({url:"/PRO/MaterielInventory/GetRawFilterDataSummary",method:"post",data:t})}function Ot(t){return(0,a.default)({url:"/PRO/TechnologyLib/GetTeamListByUserForMateriel",method:"post",data:t})}function yt(t){return(0,a.default)({url:"/Pro/MaterielAssignNew/TransferRawLock",method:"post",data:t})}},5480:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getTableConfig=e.getRoleInfo=void 0,r("4de4"),r("d81d"),r("e9f5"),r("910d"),r("ab43"),r("d3b7");var o=r("6186"),a=r("c24f"),n=void 0;e.getTableConfig=function(t,e){return new Promise((function(r,a){(0,o.GetGridByCode)({code:t,businessType:e}).then((function(t){var e=t.IsSucceed,o=t.Data,a=t.Message;if(e){var u=(o.ColumnList||[]).filter((function(t){return t.Is_Display}));r(u)}else n.$message({message:a,type:"error"})}))}))},e.getRoleInfo=function(t){return new Promise((function(e,r){(0,a.RoleAuthorization)({roleType:3,menuType:1,menuId:t}).then((function(t){if(t.IsSucceed){var o=((null===t||void 0===t?void 0:t.Data)||[]).map((function(t){return t.Code}));e(o)}else n.$message({message:t.Message,type:"error"}),r(t.message)}))}))}},"5d26":function(t,e,r){"use strict";r.r(e);var o=r("7bf1"),a=r("c9cdb");for(var n in a)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(n);r("6cdb");var u=r("2877"),i=Object(u["a"])(a["default"],o["a"],o["b"],!1,null,"fbf68376",null);e["default"]=i.exports},"6cdb":function(t,e,r){"use strict";r("dcf4")},"7bf1":function(t,e,r){"use strict";r.d(e,"a",(function(){return o})),r.d(e,"b",(function(){return a}));var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"app-container abs100"},[r("el-card",{staticClass:"box-card h100"},[r("el-tabs",{on:{"tab-click":t.handleReset},model:{value:t.form.ReleaseStatus,callback:function(e){t.$set(t.form,"ReleaseStatus",e)},expression:"form.ReleaseStatus"}},[r("el-tab-pane",{attrs:{label:"已下达",name:"2"}}),r("el-tab-pane",{attrs:{label:"未下达",name:"1"}})],1),r("el-form",{ref:"form",attrs:{inline:"",model:t.form,rules:t.rules}},[r("el-form-item",{attrs:{label:"辅料单号",prop:"InStoreNo"}},[r("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:t.form.InStoreNo,callback:function(e){t.$set(t.form,"InStoreNo",e)},expression:"form.InStoreNo"}})],1),r("el-form-item",{attrs:{label:"类型",prop:"InStoreTypeList"}},[r("SelectMaterialStoreType",{staticStyle:{width:"260px"},attrs:{type:"RawAllInStoreType",multiple:""},model:{value:t.form.InStoreTypeList,callback:function(e){t.$set(t.form,"InStoreTypeList",e)},expression:"form.InStoreTypeList"}})],1),r("el-form-item",{attrs:{label:"日期",prop:"InStoreDate"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange",align:"right","unlink-panels":"","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.form.InStoreDate,callback:function(e){t.$set(t.form,"InStoreDate",e)},expression:"form.InStoreDate"}})],1),r("el-form-item",{attrs:{label:"所属项目",prop:"Sys_Project_Id"}},[r("SelectProject",{attrs:{"has-no-project":""},model:{value:t.form.Sys_Project_Id,callback:function(e){t.$set(t.form,"Sys_Project_Id",e)},expression:"form.Sys_Project_Id"}})],1),r("el-form-item",{attrs:{label:"采购单号",prop:"PurchaseNo"}},[r("el-input",{attrs:{clearable:"",placeholder:"请输入"},model:{value:t.form.PurchaseNo,callback:function(e){t.$set(t.form,"PurchaseNo",e)},expression:"form.PurchaseNo"}})],1),r("el-form-item",{attrs:{label:"送货单编号",prop:"Delivery_No"}},[r("el-input",{attrs:{placeholder:"请输入",clearable:""},model:{value:t.form.Delivery_No,callback:function(e){t.$set(t.form,"Delivery_No",e)},expression:"form.Delivery_No"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.search(1)}}},[t._v("搜索")]),r("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1),r("el-divider",{staticClass:"elDivder"}),r("vxe-toolbar",{scopedSlots:t._u([{key:"buttons",fn:function(){return[r("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("新增入库单")]),t.showReturnBtn?r("el-button",{attrs:{type:"danger"},on:{click:t.handleAddReturn}},[t._v("新增退货单")]):t._e(),r("el-button",{attrs:{loading:t.btnLoading,disabled:0===t.multipleSelection.length},on:{click:t.handelExport}},[t._v("导出")]),r("div",{staticStyle:{"margin-left":"auto"}},[2==t.form.ReleaseStatus?r("span",{staticStyle:{color:"#409EFF","font-size":"14px"}},[r("span",[t._v("入库数量："+t._s(t.sum.RukuQuantity))]),r("span",{staticStyle:{"margin-left":"10px"}},[t._v("退货数量："+t._s(t.sum.TuihuoQuantity))])]):t._e(),r("DynamicTableFields",{staticStyle:{"margin-left":"20px"},attrs:{title:"表格配置","table-config-code":t.gridCode},on:{updateColumn:t.getGridConfig}})],1)]},proxy:!0}])}),r("div",{staticClass:"tb-x"},[t.showTable?r("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"",align:"left",height:"auto","show-overflow":"",loading:t.tbLoading,stripe:"",size:"medium",data:t.tbData,resizable:"","row-config":{isCurrent:!0,isHover:!0,keyField:"Id"},"checkbox-config":{reserve:!0},"tooltip-config":{enterable:!0}},on:{"checkbox-all":t.tbSelectChange,"checkbox-change":t.tbSelectChange}},[r("vxe-column",{attrs:{type:"checkbox",width:"60",fixed:"left",align:"center"}}),r("vxe-column",{attrs:{type:"seq",title:"序号",width:"60",fixed:"left",align:"center"}}),t._l(t.columns,(function(e){return[r("vxe-column",{key:e.Code,attrs:{field:e.Code,title:e.Display_Name,"min-width":e.Width,sortable:"",fixed:e.Is_Frozen?e.Frozen_Dirction||"left":""},scopedSlots:t._u(["InStoreNo"===e.Code?{key:"default",fn:function(o){var a=o.row;return[r("el-button",{style:{color:0==a.rkType?"#67C23A":"#F56C6C"},attrs:{type:"text"},on:{click:function(e){return t.handleView(a)}}},[t._v(" "+t._s(t._f("displayValue")(a[e.Code])))])]}}:"CreateTime"===e.Code?{key:"default",fn:function(r){var o=r.row;return[t._v(" "+t._s(t._f("timeFormat")(o[e.Code],"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}:"InStoreDate"===e.Code?{key:"default",fn:function(r){var o=r.row;return[t._v(" "+t._s(t._f("timeFormat")(o[e.Code],"{y}-{m}-{d}"))+" ")]}}:{key:"default",fn:function(r){var o=r.row;return[t._v(" "+t._s(o[e.Code]||"-")+" ")]}}],null,!0)})]})),r("vxe-column",{attrs:{fixed:"right",title:"操作",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var o=e.row;return[1!==o.Status&&4!==o.Status?r("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleView(o)}}},[t._v("查看")]):t._e(),2===o.Status?r("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(e){return t.handleCancelFlow(o.Flow_Id)}}},[t._v("回收")]):t._e(),2===o.Status||4===o.Status?r("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleMonitor(o.Flow_Id)}}},[t._v("监控")]):t._e(),1!==o.Status&&4!==o.Status&&(3!==o.Status||o.Is_Sub_Out)||0!=o.rkType?t._e():r("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleEdit(o)}}},[t._v("编辑")]),1===o.Status||4===o.Status?r("el-button",{attrs:{type:"text"},on:{click:function(e){return t.handleSubmit(o)}}},[t._v("提交")]):t._e(),1===o.Status||4===o.Status?r("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(e){return t.handleDelete(o)}}},[t._v(" 删除 ")]):t._e(),3===o.Status&&0==o.rkType?r("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(e){return t.handleReturn(o)}}},[t._v(" 退货 ")]):t._e()]}}],null,!1,317229967)})],2):t._e()],1),r("div",{staticClass:"cs-bottom"},[r("Pagination",{attrs:{total:t.total,"page-sizes":t.tablePageSize,page:t.queryInfo.Page,limit:t.queryInfo.PageSize},on:{"update:page":function(e){return t.$set(t.queryInfo,"Page",e)},"update:limit":function(e){return t.$set(t.queryInfo,"PageSize",e)},pagination:t.pageChange}})],1)],1),r("Monitor",{ref:"monitor"})],1)},a=[]},"8fea":function(t,e){t.exports={WEIGHT_CONVERSION:1e3,WEIGHT_DECIMAL:5,WEIGHT_KG_DECIMAL:2,COUNT_DECIMAL:2,UNIT_PRICE_DECIMAL:6,UNIT_PRICE_KG_DECIMAL:9,RawInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],RawReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],RawAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],RawOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:3}],RawAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}],AuxInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3}],AuxReturnStoreType:[{Display_Name:"自采退货",Value:4},{Display_Name:"甲供退货",Value:2}],AuxAllInStoreType:[{Display_Name:"采购入库",Value:1},{Display_Name:"甲供入库",Value:2},{Display_Name:"手动入库",Value:3},{Display_Name:"自采退货",Value:7},{Display_Name:"甲供退货",Value:8}],AuxOutStoreType:[{Display_Name:"领用出库",Value:1},{Display_Name:"手动出库",Value:2}],AuxAllOutStoreType:[{Display_Name:"领用出库",Value:5},{Display_Name:"手动出库",Value:6},{Display_Name:"退库",Value:4}]}},"93aa":function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AuxImport=F,e.AuxInStoreExport=Q,e.AuxReturnByReceipt=X,e.AuxSurplusReturnStore=tt,e.DeleteAuxInStore=$,e.DeleteInStore=R,e.DeletePicking=Dt,e.ExportCheckReceipt=ft,e.ExportInstoreReceipt=ct,e.ExportMoneyAdjustOrder=wt,e.ExportPicking=bt,e.ExportProcess=Et,e.ExportTestDetail=It,e.FindAuxPageList=W,e.FindRawPageList=C,e.GetAuxCategoryTreeList=Z,e.GetAuxDetailByReceipt=K,e.GetAuxImportTemplate=N,e.GetAuxPageList=rt,e.GetAuxPickOutStoreSubList=E,e.GetAuxProcurementDetails=ot,e.GetAuxSurplusReturnStoreDetail=et,e.GetCategoryTreeList=g,e.GetImportTemplate=L,e.GetInstoreDetail=P,e.GetMoneyAdjustDetailPageList=Gt,e.GetOMALatestStatisticTime=A,e.GetOrderDetail=ut,e.GetPartyAs=y,e.GetPickLockStoreToChuku=Nt,e.GetPickPlate=Ft,e.GetPickSelectPageList=_t,e.GetPickSelectSubList=jt,e.GetPickingDetail=xt,e.GetPickingTypeSettingDetail=kt,e.GetProjectListForTenant=at,e.GetRawDetailByReceipt=it,e.GetRawOrderList=nt,e.GetRawPageList=I,e.GetRawPickOutStoreSubList=j,e.GetRawProcurementDetails=G,e.GetRawSurplusReturnStoreDetail=_,e.GetReturnPlate=Ut,e.GetStoreSelectPage=At,e.GetSuppliers=O,e.GetTestDetail=Ot,e.GetTestInStoreOrderList=Pt,e.Import=x,e.ImportCheckReceipt=mt,e.ImportInstoreReceipt=pt,e.InStoreListSummary=dt,e.LockPicking=Ct,e.ManualAuxInStoreDetail=q,e.ManualInStoreDetail=S,e.MaterielAuxInStoreList=U,e.MaterielAuxManualInStore=J,e.MaterielAuxPurchaseInStore=z,e.MaterielAuxSubmitInStore=B,e.MaterielPartyAInStorel=H,e.MaterielRawInStoreList=n,e.MaterielRawInStoreListInSumNew=i,e.MaterielRawInStoreListNew=u,e.MaterielRawManualInStore=b,e.MaterielRawPartyAInStore=M,e.MaterielRawPurchaseInStore=w,e.MaterielRawSubmitInStore=l,e.MaterielRawSurplusInStore=v,e.OutStoreListSummary=st,e.PartAInStoreDetail=m,e.PartyAInInStoreDetail=Y,e.PurchaseAuxInStoreDetail=V,e.PurchaseInStoreDetail=p,e.RawInStoreExport=k,e.RawReturnByReceipt=lt,e.RawSurplusReturnStore=T,e.SaveInStore=D,e.SavePicking=Lt,e.SetQualified=yt,e.SetTestDetail=gt,e.StoreMoneyAdjust=ht,e.SubmitApproval=f,e.SubmitAuxApproval=c,e.SubmitInStore=St,e.SubmitPicking=vt,e.SurplusInStoreDetail=h,e.UnLockPicking=Tt,e.UpdateInvoiceInfo=Mt,e.Withdraw=d,e.WithdrawAux=s,e.WithdrawChecked=Rt;var a=o(r("b775"));function n(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/List",method:"post",data:t})}function u(t){return(0,a.default)({url:"/pro/MaterielRawInStoreNew/GetPageList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/pro/MaterielRawInStore/ListInSumNew",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SubmitInStore",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/Withdraw",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/Withdraw",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/SubmitApproval",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SubmitApproval",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PurchaseInStoreDetail",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PartAInStoreDetail",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/ManualInStoreDetail",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SurplusInStoreDetail",method:"post",data:t})}function P(t){return(0,a.default)({url:"/Pro/MaterielRawInStoreNew/GetInstoreDetail",method:"post",data:t})}function R(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/DeleteInStore",method:"post",data:t})}function O(t){return(0,a.default)({url:"/PRO/Communal/GetSuppliers",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/Communal/GetPartyAs",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/MaterielRawConfig/GetCategoryTreeList",method:"post",data:t})}function I(t){return(0,a.default)({url:"/PRO/MaterielRawConfig/GetRawPageList",method:"post",data:t})}function G(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetRawProcurementDetails",method:"post",data:t})}function w(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PurchaseInStore",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/PartyAInStore",method:"post",data:t})}function b(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/ManualInStore",method:"post",data:t})}function v(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/SurplusInStore",method:"post",data:t})}function D(t){return(0,a.default)({url:"/PRO/MaterielRawInStoreNew/SaveInStore",method:"post",data:t})}function A(t){return(0,a.default)({url:"/PRO/Communal/GetOMALatestStatisticTime",method:"post",data:t})}function L(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/GetImportTemplate",method:"post",data:t})}function x(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/Import",method:"post",data:t})}function k(t){return(0,a.default)({url:"/PRO/MaterielRawInStore/Export",method:"post",data:t})}function C(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/FindRawPageList",method:"post",data:t})}function T(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/RawSurplusReturnStore",method:"post",data:t})}function _(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetRawSurplusReturnStoreDetail",method:"post",data:t})}function j(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetRawPickOutStoreSubList",method:"post",data:t})}function N(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/GetImportTemplate",method:"post",data:t})}function F(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/Import",method:"post",data:t})}function U(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/List",method:"post",data:t})}function E(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetAuxPickOutStoreSubList",method:"post",data:t})}function B(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/SubmitInStore",method:"post",data:t})}function $(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/DeleteInStore",method:"post",data:t})}function W(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/FindAuxPageList",method:"post",data:t})}function V(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStoreDetail",method:"post",data:t})}function Y(t){return(0,a.default)({url:"/pro/MaterielAuxInStore/PartyAInStoreDetail",method:"post",data:t})}function q(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/ManualInStoreDetail",method:"post",data:t})}function z(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/PurchaseInStore",method:"post",data:t})}function H(t){return(0,a.default)({url:"PRO/MaterielAuxInStore/PartyAInStore",method:"post",data:t})}function J(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/ManualInStore",method:"post",data:t})}function Q(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/Export",method:"post",data:t})}function K(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/GetAuxDetailByReceipt",method:"post",data:t})}function X(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/AuxReturnByReceipt",method:"post",data:t})}function Z(t){return(0,a.default)({url:"/PRO/MaterielAuxConfig/GetCategoryTreeList",method:"post",data:t})}function tt(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/AuxSurplusReturnStore",method:"post",data:t})}function et(t){return(0,a.default)({url:"/PRO/MaterielReturnStore/GetAuxSurplusReturnStoreDetail",method:"post",data:t})}function rt(t){return(0,a.default)({url:"/PRO/MaterielAuxConfig/GetAuxPageList",method:"post",data:t})}function ot(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetAuxProcurementDetails",method:"post",data:t})}function at(t){return(0,a.default)({url:"/PRO/Project/GetProjectListForTenant",method:"post",data:t})}function nt(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetRawOrderList",method:"post",data:t})}function ut(t){return(0,a.default)({url:"/PRO/MaterielPurchase/GetOrderDetail",method:"post",data:t})}function it(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/GetRawDetailByReceipt",method:"post",data:t})}function lt(t){return(0,a.default)({url:"/PRO/MaterielReturnGoods/RawReturnByReceipt",method:"post",data:t})}function dt(t){return(0,a.default)({url:"/PRO/MaterielAuxInStore/ListSummary",method:"post",data:t})}function st(t){return(0,a.default)({url:"/PRO/MaterielAuxOutStore/ListSummary",method:"post",data:t})}function ct(t){return(0,a.default)({url:"/PRO/CustomUssl/ExportInstoreReceipt",method:"post",data:t})}function ft(t){return(0,a.default)({url:"/PRO/CustomUssl/ExportCheckReceipt",method:"post",data:t})}function pt(t){return(0,a.default)({url:"/Pro/CustomUssl/ImportInstoreReceipt",method:"post",data:t})}function mt(t){return(0,a.default)({url:"/Pro/CustomUssl/ImportCheckReceipt",method:"post",data:t})}function St(t){return(0,a.default)({url:"/Pro/MaterielRawInStoreNew/SubmitInStore",method:"post",data:t})}function ht(t){return(0,a.default)({url:"/PRO/MaterielMoneyAdjust/StoreMoneyAdjust",method:"post",data:t})}function Pt(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/GetTestInStoreOrderList",method:"post",data:t})}function Rt(t){return(0,a.default)({url:"/Pro/MaterielRawInStoreNew/WithdrawChecked",method:"post",data:t})}function Ot(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/GetTestDetail",method:"post",data:t})}function yt(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/SetQualified",method:"post",data:t})}function gt(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/SetTestDetail",method:"post",data:t})}function It(t){return(0,a.default)({url:"/PRO/MaterielTestOrder/ExportTestDetail",method:"post",data:t})}function Gt(t){return(0,a.default)({url:"/PRO/MaterielMoneyAdjust/GetMoneyAdjustDetailPageList",method:"post",data:t})}function wt(t){return(0,a.default)({url:"/PRO/MaterielMoneyAdjust/ExportMoneyAdjustOrder",method:"post",data:t})}function Mt(t){return(0,a.default)({url:"/PRO/MaterielRawInStoreNew/UpdateInvoiceInfo",method:"post",data:t})}function bt(t){return(0,a.default)({url:"/Pro/MaterielPicking/ExportPicking",method:"post",data:t})}function vt(t){return(0,a.default)({url:"/Pro/MaterielPicking/SubmitPicking",method:"post",data:t})}function Dt(t){return(0,a.default)({url:"/Pro/MaterielPicking/DeletePicking",method:"post",data:t})}function At(t){return(0,a.default)({url:"/Pro/MaterielPicking/GetStoreSelectPage",method:"post",data:t})}function Lt(t){return(0,a.default)({url:"/Pro/MaterielPicking/SavePicking",method:"post",data:t})}function xt(t){return(0,a.default)({url:"/Pro/MaterielPicking/GetPickingDetail",method:"post",data:t})}function kt(t){return(0,a.default)({url:"/Pro/MaterielPicking/GetPickingTypeSettingDetail",method:"post",data:t})}function Ct(t){return(0,a.default)({url:"/Pro/MaterielPicking/LockPicking",method:"post",data:t})}function Tt(t){return(0,a.default)({url:"/Pro/MaterielPicking/UnLockPicking",method:"post",data:t})}function _t(t){return(0,a.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectPageList",method:"post",data:t})}function jt(t){return(0,a.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickSelectSubList",method:"post",data:t})}function Nt(t){return(0,a.default)({url:"/PRO/MaterielRawOutStoreNew/GetPickLockStoreToChuku",method:"post",data:t})}function Ft(t){return(0,a.default)({url:"/Pro/Nesting/GetPickPlate",method:"post",data:t})}function Ut(t){return(0,a.default)({url:"/Pro/Nesting/GetReturnPlate",method:"post",data:t})}function Et(t){return(0,a.default)({url:"/Pro/Nesting/ExportProcess",method:"post",data:t})}},9643:function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AddProjectSendingInfo=I,e.CancelFlow=Y,e.DeleteProjectSendingInfo=l,e.EditProjectSendingInfo=c,e.ExportComponentStockOutInfo=b,e.ExportInvoiceList=W,e.ExportSendSteel=f,e.ExportSendingDetailInfoList=p,e.GetLocationList=C,e.GetProduceCompentEntity=O,e.GetProducedPartToSendPageList=_,e.GetProjectAcceptInfoPagelist=N,e.GetProjectSendingAllCount=h,e.GetProjectSendingInfoAndItemPagelist=D,e.GetProjectSendingInfoLogPagelist=T,e.GetProjectSendingInfoPagelist=i,e.GetProjectsendinginEntity=s,e.GetReadyForDeliverSummary=v,e.GetReadyForDeliveryComponentPageList=M,e.GetReadyForDeliveryPageList=w,e.GetReturnHistoryPageList=F,e.GetSendToReturnPageList=A,e.GetStockOutBillInfoPageList=x,e.GetStockOutDetailList=P,e.GetStockOutDetailPageList=L,e.GetStockOutDocEntity=G,e.GetStockOutDocPageList=u,e.GetWaitingStockOutPageList=R,e.GetWarehouseListOfCurFactory=k,e.GetWeighingReviewList=U,e.SaveStockOut=g,e.SubmitApproval=V,e.SubmitProjectSending=d,e.SubmitReturnToStockIn=y,e.SubmitWeighingForPC=B,e.Transforms=m,e.TransformsByType=j,e.TransformsWithoutWeight=S,e.WeighingReviewSubmit=E,e.WithdrawDraft=$;var a=o(r("b775")),n=o(r("4328"));function u(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDocPageList",method:"post",data:t})}function i(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoPagelist",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/ComponentStockOut/DeleteProjectSendingInfo",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitProjectSending",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectsendinginEntity",method:"post",data:t})}function c(t){return(0,a.default)({url:"/PRO/ComponentStockOut/EditProjectSendingInfo",method:"post",data:t})}function f(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportSendSteel",method:"post",data:t})}function p(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportSendingDetailInfoList",method:"post",data:t})}function m(t){return(0,a.default)({url:"/PRO/ComponentStockOut/Transforms",method:"post",data:t})}function S(t){return(0,a.default)({url:"/PRO/ComponentStockOut/TransformsWithoutWeight",method:"post",data:t})}function h(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingAllCount",method:"post",data:t})}function P(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailList",method:"post",data:n.default.stringify(t)})}function R(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetWaitingStockOutPageList",method:"post",data:t})}function O(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProduceCompentEntity",method:"post",data:t})}function y(t){return(0,a.default)({url:"/PRO/ComponentReturn/SubmitReturnToStockIn",method:"post",data:t})}function g(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SaveStockOut",method:"post",data:t})}function I(t){return(0,a.default)({url:"/PRO/ComponentStockOut/AddProjectSendingInfo",method:"post",data:t})}function G(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDocEntity",method:"post",data:n.default.stringify(t)})}function w(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryPageList",method:"post",data:t})}function M(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliveryComponentPageList",method:"post",data:t})}function b(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportComponentStockOutInfo",method:"post",data:n.default.stringify(t)})}function v(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetReadyForDeliverSummary",method:"post",data:n.default.stringify(t)})}function D(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoAndItemPagelist",method:"post",data:t})}function A(t){return(0,a.default)({url:"/PRO/ComponentReturn/GetSendToReturnPageList",method:"post",data:t})}function L(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutDetailPageList",method:"post",data:t})}function x(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetStockOutBillInfoPageList",method:"post",data:t})}function k(t){return(0,a.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function C(t){return(0,a.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function T(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectSendingInfoLogPagelist",method:"post",data:t})}function _(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProducedPartToSendPageList",method:"post",data:t})}function j(t){return(0,a.default)({url:"/PRO/ComponentStockOut/TransformsByType",method:"post",data:t})}function N(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetProjectAcceptInfoPagelist",method:"post",data:t})}function F(t){return(0,a.default)({url:"/PRO/ComponentReturn/GetReturnHistoryPageList",method:"post",data:t})}function U(t){return(0,a.default)({url:"/PRO/ComponentStockOut/GetWeighingReviewList",method:"post",data:t})}function E(t){return(0,a.default)({url:"/PRO/ComponentStockOut/WeighingReviewSubmit",method:"post",data:t})}function B(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitWeighingForPC",method:"post",data:t})}function $(t){return(0,a.default)({url:"/PRO/ComponentStockOut/WithdrawDraft",method:"post",data:t})}function W(t){return(0,a.default)({url:"/PRO/ComponentStockOut/ExportInvoiceList",method:"post",data:t})}function V(t){return(0,a.default)({url:"/PRO/ComponentStockOut/SubmitApproval",method:"post",data:t})}function Y(t){return(0,a.default)({url:"/Sys/FlowInstances/CancelFlow",method:"post",data:t})}},bbfc:function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(r("5530")),n=o(r("c14f")),u=o(r("1da1"));r("99af"),r("caad"),r("d81d"),r("14d9"),r("b0c0"),r("e9f5"),r("ab43"),r("e9c4"),r("b64b"),r("d3b7"),r("ac1f"),r("2532"),r("3ca3"),r("841c"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494");var i=r("ed08"),l=o(r("15ac")),d=o(r("333d")),s=r("c685"),c=o(r("2082")),f=r("3166"),p=r("93aa"),m=r("f4f2"),S=r("9643"),h=r("5480"),P=o(r("5d4b")),R=o(r("a657")),O=r("3c4a"),y=o(r("bad9")),g=o(r("7962"));e.default={name:"PROAuxiliaryMaterialReceipt",components:{SelectProject:y.default,DynamicTableFields:R.default,Pagination:d.default,SelectMaterialStoreType:P.default,Monitor:g.default},mixins:[l.default,c.default],data:function(){return{btnLoading:!1,addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-5836e6ec"),r.e("chunk-2ea6e54c"),r.e("chunk-6fec96b8")]).then(r.bind(null,"348a"))},name:"PROAuxMaterialReceiptAdd",meta:{title:"新建入库单"}},{path:this.$route.path+"/edit/:id/:type",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-5836e6ec"),r.e("chunk-2ea6e54c"),r.e("chunk-69d65ca3")]).then(r.bind(null,"5662"))},name:"PROAuxMaterialReceiptEdit",meta:{title:"编辑入库单"}},{path:this.$route.path+"/view/:id/:type",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-5836e6ec"),r.e("chunk-2ea6e54c"),r.e("chunk-1628c9fb")]).then(r.bind(null,"c481"))},name:"PROAuxMaterialReceiptView",meta:{title:"查看入库单"}},{path:this.$route.path+"/return/:id/:type",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-5836e6ec"),r.e("chunk-2ea6e54c"),r.e("chunk-2758ba67")]).then(r.bind(null,"062c"))},name:"PROAuxMaterialReceiptReturn",meta:{title:"退货"}},{path:this.$route.path+"/add-return",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-509cb544"),r.e("chunk-d4061ecc")]).then(r.bind(null,"0d6e"))},name:"PROAuxGoodsReturnAddReturn",meta:{title:"新建退货单"}},{path:this.$route.path+"/edit-return",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-509cb544"),r.e("chunk-8baccff4")]).then(r.bind(null,"3771"))},name:"PROAuxGoodsReturnEdit",meta:{title:"编辑退货单"}},{path:this.$route.path+"/view-return",hidden:!0,component:function(){return Promise.all([r.e("chunk-commons"),r.e("chunk-509cb544"),r.e("chunk-2cf33e8e")]).then(r.bind(null,"ddad"))},name:"PROAuxGoodsReturnView",meta:{title:"查看退货单"}}],tablePageSize:s.tablePageSize,showReturnBtn:!1,tbLoading:!1,columns:[],tbData:[],ProjectList:[],multipleSelection:[],tbConfig:{},queryInfo:{Page:1,PageSize:s.tablePageSize[0]},total:0,form:{Sys_Project_Id:"",Delivery_No:"",InStoreNo:"",InStoreTypeList:[],InStoreDate:"",Status:null,beg:null,end:null,ReleaseStatus:"2"},rules:{},search:function(){return{}},gridCode:"PROAuxMaterialReceiptList",showTable:!0,sum:{}}},mounted:function(){var t=this;return(0,u.default)((0,n.default)().m((function e(){var r;return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.getProject(),t.search=(0,i.debounce)(t.fetchData,800,!0),e.n=1,t.getGridConfig();case 1:return t.fetchData(1),e.n=2,(0,h.getRoleInfo)(t.$route.meta.Id);case 2:r=e.v,Array.isArray(r)&&r.includes("AuxInReturnBtn")&&(t.showReturnBtn=!0);case 3:return e.a(2)}}),e)})))()},activated:function(){this.fetchData(1)},methods:{handleMonitor:function(t){this.$refs["monitor"].opendialog(t,!1)},getGridConfig:function(){var t=this;return(0,u.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return t.showTable=!1,e.n=1,t.getTableConfig(t.gridCode);case 1:t.showTable=!0;case 2:return e.a(2)}}),e)})))()},fetchData:function(t){var e=this;this.form.beg=this.form.InStoreDate?(0,i.parseTime)(this.form.InStoreDate[0],"{y}-{m}-{d}"):null,this.form.end=this.form.InStoreDate?(0,i.parseTime)(this.form.InStoreDate[1],"{y}-{m}-{d}"):null,t&&(this.queryInfo.Page=t);var r=JSON.parse(JSON.stringify(this.form));r.InStoreType||(r.InStoreType=0),r.Status||(r.Status=0),this.tbLoading=!0,(0,p.MaterielAuxInStoreList)((0,a.default)((0,a.default)({},this.queryInfo),r)).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"})})).finally((function(r){t&&(e.$refs.xTable.clearCheckboxRow(),e.multipleSelection=[]),e.tbLoading=!1})),(0,p.InStoreListSummary)((0,a.default)({},r)).then((function(t){t.IsSucceed?e.sum=t.Data:e.$message({message:t.Message,type:"error"})}))},getProject:function(){var t=this;return(0,u.default)((0,n.default)().m((function e(){return(0,n.default)().w((function(e){while(1)switch(e.n){case 0:return e.n=1,(0,f.GetProjectPageList)({Page:1,PageSize:-1}).then((function(e){e.IsSucceed?t.ProjectList=e.Data.Data:t.$message({message:e.Message,type:"error"})}));case 1:return e.a(2)}}),e)})))()},handleAdd:function(){this.$router.push({name:"PROAuxMaterialReceiptAdd",query:{pg_redirect:this.$route.name}})},handleReset:function(){this.$refs.xTable.clearCheckboxRow(),this.multipleSelection=[],this.$refs["form"].resetFields(),this.search(1)},handleView:function(t){0==t.rkType?this.$router.push({name:"PROAuxMaterialReceiptView",query:{pg_redirect:this.$route.name,id:t.InStoreNo,type:t.InStoreType,status:t.Status},params:{id:t.InStoreNo,type:t.InStoreType,status:t.Status}}):this.$router.push({name:"PROAuxGoodsReturnView",query:{pg_redirect:this.$route.name,OutStoreNo:t.InStoreNo,OutStoreType:t.InStoreType}})},handleDelete:function(t){var e=this;this.$confirm("是否删除该入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){0==t.rkType?(0,p.DeleteAuxInStore)({inStoreNo:t.InStoreNo}).then((function(t){t.IsSucceed?(e.$message({message:"删除成功",type:"success"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})})):(0,O.EditAuxOutStatus)({OutStoreNo:t.InStoreNo,Status:1,Is_Deleted:!0}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"删除成功!"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},handleEdit:function(t){0==t.rkType?this.$router.push({name:"PROAuxMaterialReceiptEdit",query:{pg_redirect:this.$route.name,id:t.InStoreNo,type:t.InStoreType,status:t.Status},params:{id:t.InStoreNo,type:t.InStoreType,status:t.Status}}):this.$router.push({name:"PROAuxGoodsReturnEdit",query:{pg_redirect:this.$route.name,OutStoreNo:t.InStoreNo,OutStoreType:t.InStoreType}})},handleReturn:function(t){this.$router.push({name:"PROAuxMaterialReceiptReturn",query:{pg_redirect:this.$route.name,id:t.InStoreNo,type:t.InStoreType,status:t.Status},params:{id:t.InStoreNo,type:t.InStoreType,status:t.Status}})},handleSubmit:function(t){var e=this;this.$confirm("确认提交入库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){0==t.rkType?(0,p.SubmitAuxApproval)({Id:t.Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"提交成功!"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})})):(0,O.EditAuxOutStatus)({OutStoreNo:t.InStoreNo,Status:3,Is_Deleted:!1}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"提交成功!"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handelExport:function(){var t=this,e=this.multipleSelection.map((function(t){return t.Id}));this.btnLoading=!0,(0,p.AuxInStoreExport)({request:e}).then((function(e){if(e.IsSucceed){t.$message.success("导出成功");var r=new URL(e.Data,(0,m.baseUrl)());window.open(r.href)}else t.$message({message:e.Message,type:"error"})})).finally((function(){t.btnLoading=!1}))},tbSelectChange:function(t){this.multipleSelection=t.records.concat(t.reserves)},handleCancelFlow:function(t){var e=this;this.$confirm("是否撤回?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,S.CancelFlow)({instanceId:t}).then((function(t){t.IsSucceed?(e.$message({message:"操作成功",type:"success"}),e.fetchData()):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handleAddReturn:function(){this.$router.push({name:"PROAuxGoodsReturnAddReturn",query:{pg_redirect:this.$route.name}})},widthdraw:function(t){var e=this;this.$confirm("是否撤回?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,p.WithdrawAux)({id:t.Id}).then((function(t){t.IsSucceed?(e.$message({message:"撤回成功",type:"success"}),e.fetchData(1)):e.$message({message:t.Message,type:"error"})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}},c9cdb:function(t,e,r){"use strict";r.r(e);var o=r("bbfc"),a=r.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(n);e["default"]=a.a},dcf4:function(t,e,r){},e41b:function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteByIds=l,e.GetPartsImportTemplate=s,e.GetPartsList=i,e.GetProjectAreaTreeList=n,e.ImportParts=d,e.SaveProjectAreaSort=u;var a=o(r("b775"));function n(t){return(0,a.default)({url:"/PRO/PartsList/GetProjectAreaTreeList",method:"post",data:t})}function u(t){return(0,a.default)({url:"/PRO/PartsList/SaveProjectAreaSort",method:"post",data:t})}function i(t){return(0,a.default)({url:"/PRO/PartsList/GetPartsList",method:"post",data:t})}function l(t){return(0,a.default)({url:"/PRO/PartsList/DeleteByIds",method:"post",data:t})}function d(t){return(0,a.default)({url:"/PRO/PartsList/ImportParts",method:"post",data:t})}function s(t){return(0,a.default)({url:"/PRO/PartsList/GetPartsImportTemplate",method:"post",data:t})}},ea13:function(t,e,r){"use strict";var o=r("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeleteGroup=d,e.DeleteGroupRole=S,e.DeleteGroupUser=c,e.DeleteUserRole=I,e.GetGroupEntity=i,e.GetGroupList=n,e.GetGroupRole=p,e.GetGroupTree=u,e.GetGroupUser=f,e.GetGroupUserByRole=O,e.GetRoleListCanAdd=m,e.GetShuttleUserList=y,e.GetWorkingObjTreeListByGroupId=R,e.SaveGroup=l,e.SaveGroupObject=P,e.SaveGroupRole=h,e.SaveGroupUser=s,e.SaveUserRoles=g;var a=o(r("b775"));function n(){return(0,a.default)({url:"/SYS/UserGroup/GetGroupList",method:"post"})}function u(t){return(0,a.default)({url:"/SYS/UserGroup/GetGroupTree",method:"post",data:t})}function i(t){return(0,a.default)({url:"/SYS/UserGroup/GetGroupEntity",method:"post",data:t})}function l(t){return(0,a.default)({url:"/SYS/UserGroup/SaveGroup",method:"post",data:t})}function d(t){return(0,a.default)({url:"/SYS/UserGroup/DeleteGroup",method:"post",data:t})}function s(t){return(0,a.default)({url:"/SYS/UserGroup/SaveGroupUser",method:"post",data:t})}function c(t){return(0,a.default)({url:"/SYS/UserGroup/DeleteGroupUser",method:"post",data:t})}function f(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetGroupUser",data:t})}function p(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetGroupRole",data:t})}function m(t){return(0,a.default)({method:"post",url:"sys/UserGroup/GetRoleListCanAdd",data:t})}function S(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/DeleteGroupRole",data:t})}function h(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/SaveGroupRole",data:t})}function P(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/SaveGroupObject",data:t})}function R(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetWorkingObjTreeListByGroupId",data:t})}function O(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetGroupUserByRole",data:t})}function y(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/GetShuttleUserList",data:t})}function g(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/SaveUserRoles",data:t})}function I(t){return(0,a.default)({method:"post",url:"/SYS/UserGroup/DeleteUserRole",data:t})}},f382:function(t,e,r){"use strict";function o(t){return t.filter((function(t){return!!t.Is_Directory&&(t.Children&&t.Children.length&&(t.Children=o(t.Children)),!0)}))}function a(t){t.map((function(t){if(t.Is_Directory||!t.Children)return a(t.Children);delete t.Children}))}function n(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function o(t,e,a){for(var n=0;n<t.length;n++){var u=t[n];if(u.Id===e)return r&&a.push(u),a;if(u.Children&&u.Children.length){if(a.push(u),o(u.Children,e,a).length)return a;a.pop()}}return[]}return o(t,e,[])}function u(t){return t.Children&&t.Children.length>0?u(t.Children[0]):t}function i(t){t.map((function(t){t.Is_Directory&&(t.disabled=!0,t.Children&&t.Children.length>0&&i(t.Children))}))}Object.defineProperty(e,"__esModule",{value:!0}),e.clearLeafChildren=a,e.disableDirectory=i,e.findAllParentNode=n,e.findFirstNode=u,e.getDirectoryTree=o,r("4de4"),r("d81d"),r("14d9"),r("e9f5"),r("910d"),r("ab43"),r("d3b7")}}]);