(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-62bd9ccb"],{"209b":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CheckPackCode=c,e.ExportComponentStockInInfo=f,e.ExportPackingInInfo=m,e.ExportWaitingStockIn2ndList=R,e.FinishCollect=C,e.From_Stock_Status_TYPES=void 0,e.GetComponentStockInEntity=u,e.GetLocationList=l,e.GetPackingDetailList=p,e.GetPackingEntity=y,e.GetPackingGroupByDirectDetailList=d,e.GetStockInDetailList=s,e.GetStockMoveDetailList=b,e.GetWarehouseListOfCurFactory=o,e.HandleInventoryItem=k,e.PackingTypes=e.PackingStatus=e.InventoryComponentTypes=e.InventoryCheckStatus=e.InventoryCheckExceptions=void 0,e.RemoveMain=g,e.SaveComponentScrap=_,e.SaveInventory=P,e.SavePacking=v,e.SaveStockIn=r,e.SaveStockMove=I,e.StockInTypes=void 0,e.UnzipPacking=h,e.UpdateBillReady=S,e.UpdateMaterialReady=D;var i=n(a("b775"));n(a("4328")),e.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],e.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],e.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],e.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],e.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],e.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],e.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function o(t){return(0,i.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function r(t){return(0,i.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:t}})}function s(t,e){return(0,i.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:t,isEdit:e}})}function c(t,e){return(0,i.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:t,id:e}})}function d(t){return(0,i.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:t}})}function m(t){return(0,i.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:t}})}function g(t){return(0,i.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:t}})}function v(t){return(0,i.default)({url:"/PRO/Packing/SavePacking",method:"post",data:t})}function h(t){var e=t.id,a=t.locationId;return(0,i.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:e,locationId:a}})}function y(t){var e=t.id,a=t.code;return(0,i.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:e,code:a}})}function b(t){return(0,i.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:t}})}function I(t){return(0,i.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:t})}function P(t){return(0,i.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:t})}function k(t){var e=t.id,a=t.type,n=t.value;return(0,i.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:e,type:a,value:n}})}function _(t){return(0,i.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:t})}function C(t){return(0,i.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:t}})}function S(t){var e=t.installId,a=t.isReady;return(0,i.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:e,isReady:a}})}function D(t){return(0,i.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:t})}function R(t){return(0,i.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:t})}},"4ed6":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("e9f5"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("159b");var i=n(a("0f97")),o=n(a("b775")),l=a("209b"),r=a("ed08");e.default={name:"PrepareDetail",components:{DynamicDataTable:i.default},data:function(){return{apis:{GetMaterialPrepareDetail:"/PRO/OperationPlan/GetMaterialPrepareDetail"},filterData:{Page:1,TotalCount:0,PageSize:0},tbConfig:{},columns:[],data:[],checkedRows:[],unit:{}}},created:function(){var t,e;this.unit=null!==(t=null===(e=this.$route.params)||void 0===e?void 0:e.row)&&void 0!==t?t:{},this.setGrid({Data_Url:this.apis.GetMaterialPrepareDetail,Height:0,Is_Auto_Width:!1,Is_Select:!0,Is_Filter:!1,Is_Page:!1,Width:0}),this.setCols([{Align:"center",Code:"name",Digit_Number:2,Display_Name:"物料名称",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:0},{Align:"center",Code:"spec",Digit_Number:2,Display_Name:"规格",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100},{Align:"center",Code:"model",Digit_Number:2,Display_Name:"材质/颜色/强度",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100},{Align:"center",Code:"unit",Digit_Number:2,Display_Name:"单位",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100},{Align:"center",Code:"Number",Digit_Number:2,Display_Name:"需求数量",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:240},{Align:"center",Code:"remark",Digit_Number:2,Display_Name:"备注",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100},{Align:"center",Code:"Is_Ready",Digit_Number:2,Display_Name:"是否齐套",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100}]),this.getTableData()},methods:{tagBack:function(){(0,r.closeTagView)(this.$store,this.$route)},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120}),this.filterData.PageSize=this.tbConfig.Row_Number},setCols:function(t){this.columns=t},setGridData:function(t){this.data=t.Details,this.filterData.TotalCount=t.TotalCount},getTableData:function(){var t=this;return(0,o.default)({url:this.tbConfig.Data_Url,method:"post",params:{installId:this.unit.Id}}).then((function(e){e.IsSucceed&&(t.setGridData(e.Data),t.filterData.TotalCount=Number(e.Data.TotalCount))}))},prepareCheck:function(t,e){var a=this,n=t.map((function(t){return t.Id}));(0,l.UpdateMaterialReady)({installId:this.unit.Id,Ids:n,isReady:e}).then((function(n){var i,o;n.IsSucceed?(t.forEach((function(t){t.Is_Ready=e})),a.$message.success(null!==(i=n.Message)&&void 0!==i?i:"操作成功"),a.getTableData()):a.$message.warning(null!==(o=n.Message)&&void 0!==o?o:"操作失败")}))},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.getTableData()},gridSizeChange:function(){},multiSelectedChange:function(t){this.checkedRows=t},updatePrepare:function(t){var e=this,a=this.checkedRows.map((function(t){return t.Id}));(0,l.UpdateMaterialReady)({installId:this.unit.Id,Ids:a,isReady:t}).then((function(a){var n,i;a.IsSucceed?(e.checkedRows.forEach((function(e){e.Is_Ready=t})),e.$message.success(null!==(n=a.Message)&&void 0!==n?n:"操作成功"),e.getTableData()):e.$message.warning(null!==(i=a.Message)&&void 0!==i?i:"操作失败")}))}}}},"5b4f":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),a("span",[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))]),a("el-button",{staticStyle:{"margin-left":"32px"},attrs:{round:"",size:"small",type:"success"}},[t._v("物料齐套率: "+t._s(Number(t.unit.Percentage).toFixed(2))+"%")]),a("div",{staticClass:"right-fix"},[a("el-button",{attrs:{type:"primary",disabled:"否"!==t.unit.Material_Ready||t.checkedRows.length<=0},on:{click:function(e){return t.updatePrepare(!0)}}},[t._v("确认齐套")]),a("el-button",{attrs:{type:"warning",disabled:"否"===t.unit.Material_Ready||t.checkedRows.length<=0},on:{click:function(e){return t.updatePrepare(!1)}}},[t._v("取消齐套")])],1)],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,total:t.filterData.TotalCount,page:t.filterData.Page,border:""},on:{gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange,multiSelectedChange:t.multiSelectedChange},scopedSlots:t._u([{key:"Is_Ready",fn:function(e){var n=e.column,i=e.row;e.$index;return[i[n.Code]?a("el-button",{attrs:{type:"success",round:"",size:"small"}},[t._v("是")]):a("el-button",{attrs:{type:"danger",round:"",size:"small"}},[t._v("否")])]}}])})],1)])])},i=[]},6188:function(t,e,a){"use strict";a.r(e);var n=a("5b4f"),i=a("67f1");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("c29f8");var l=a("2877"),r=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"7d55a02b",null);e["default"]=r.exports},67764:function(t,e,a){},"67f1":function(t,e,a){"use strict";a.r(e);var n=a("4ed6"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},c29f8:function(t,e,a){"use strict";a("67764")}}]);