(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-632162f1"],{6930:function(e,n,t){"use strict";t.r(n);var u=t("7d8a"),r=t.n(u);for(var a in u)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(a);n["default"]=r.a},"7d8a":function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,t("ac1f"),t("5319");n.default={created:function(){var e=this.$route,n=e.params,t=e.query,u=n.path;this.$router.replace({path:"/"+u,query:t})},render:function(e){return e()}}},ef3c:function(e,n,t){"use strict";t.r(n);var u=t("6930");for(var r in u)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(r);var a,f,c=t("2877"),o=Object(c["a"])(u["default"],a,f,!1,null,null,null);n["default"]=o.exports}}]);