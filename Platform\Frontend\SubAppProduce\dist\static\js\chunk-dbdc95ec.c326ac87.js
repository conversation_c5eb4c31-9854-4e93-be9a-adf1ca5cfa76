(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-dbdc95ec"],{"0461":function(n,t,e){"use strict";e.d(t,"a",(function(){return u})),e.d(t,"b",(function(){return r}));var u=function(){var n=this,t=n.$createElement,e=n._self._c||t;return e("detail")},r=[]},7194:function(n,t,e){"use strict";e.r(t);var u=e("f879"),r=e.n(u);for(var a in u)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(a);t["default"]=r.a},"8d41":function(n,t,e){"use strict";e.r(t);var u=e("0461"),r=e("7194");for(var a in r)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(a);var f=e("2877"),c=Object(f["a"])(r["default"],u["a"],u["b"],!1,null,"7e85f488",null);t["default"]=c.exports},f879:function(n,t,e){"use strict";var u=e("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=u(e("74f4"));t.default={components:{detail:r.default},data:function(){return{}}}}}]);