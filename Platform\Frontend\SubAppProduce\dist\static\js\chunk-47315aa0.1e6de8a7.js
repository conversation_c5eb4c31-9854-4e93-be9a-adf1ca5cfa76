(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-47315aa0"],{"03ed":function(e,t,n){"use strict";n.r(t);var a=n("64fc"),i=n("7393");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"bc5fdf86",null);t["default"]=s.exports},"0629":function(e,t,n){},"0ced":function(e,t,n){"use strict";n("dfc3")},1550:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{height:"calc(100vh - 300px)"}},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","max-height":"100%",align:"left",stripe:"",data:e.tbData,resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{"show-overflow":"tooltip",sortable:"",field:"Length",title:"构件长度","min-width":"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("span",[e._v(" "+e._s(a.TypeTag)+" "+e._s(a.Length)+" ")])]}}])}),n("vxe-column",{attrs:{"show-overflow":"tooltip",sortable:"","min-width":"150",align:"left",field:"Demand",title:"公差要求"}}),n("vxe-column",{attrs:{"show-overflow":"tooltip",sortable:"",field:"Modify_Date",title:"编辑时间","min-width":"150",align:"center"}}),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"200","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editEvent(a)}}},[e._v("编辑")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.removeEvent(a)}}},[e._v("删除")])]}}])})],1)],1)},i=[]},"15fd":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,n("a4d3");var a=i(n("ccb5"));function i(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(null==e)return{};var n,i,o=(0,a.default)(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(i=0;i<r.length;i++)n=r[i],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}},"1d9a":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{height:"calc(100vh - 300px)"}},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","max-height":"100%",align:"left",stripe:"",data:e.tbData,resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{"show-overflow":"tooltip",sortable:"",field:"Check_Content",title:"检查项内容",width:"calc(100vh-200px)/2"}}),n("vxe-column",{attrs:{"show-overflow":"tooltip",sortable:"",field:"Eligibility_Criteria",title:"合格标准",width:"calc(100vh-200px)/2"}}),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"200","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editEvent(a)}}},[e._v("编辑")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.removeEvent(a)}}},[e._v("删除")])]}}])})],1)],1)},i=[]},"2b52":function(e,t,n){"use strict";n.r(t);var a=n("63cb"),i=n("c9fc");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"755738d6",null);t["default"]=s.exports},"2ef3":function(e,t,n){"use strict";n.r(t);var a=n("a91e"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"350b":function(e,t,n){"use strict";n.r(t);var a=n("1550"),i=n("b8ad");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"60abb997",null);t["default"]=s.exports},"355d":function(e,t,n){"use strict";n.r(t);var a=n("e57a"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"36ba":function(e,t,n){"use strict";n.r(t);var a=n("dd23"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},4381:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"120px"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"检查项内容",prop:"Check_Content"}},[n("el-input",{model:{value:e.form.Check_Content,callback:function(t){e.$set(e.form,"Check_Content",t)},expression:"form.Check_Content"}})],1)],1),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"合格标准",prop:"Eligibility_Criteria"}},[n("el-input",{model:{value:e.form.Eligibility_Criteria,callback:function(t){e.$set(e.form,"Eligibility_Criteria",t)},expression:"form.Eligibility_Criteria"}})],1)],1),n("el-col",{attrs:{span:24}},[n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)],1)],1)],1)},i=[]},4618:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("c14f")),o=a(n("5530")),r=a(n("15fd")),s=a(n("1da1"));n("d9e2"),n("caad"),n("14d9"),n("2532");var c=n("7de9"),l=["Demand_Spot_Check_Rate"];t.default={data:function(){return{mode:"",ProjectId:"",Check_Object_Id:"",form:{Node_Code:"",Change_Check_Type:[],Display_Name:"",TC_UserId:"",ZL_UserId:"",Demand_Spot_Check_Rate:void 0,TC_UserIds:[],ZL_UserIds:[],Check_Style:""},rules:{Display_Name:[{required:!0,message:"请填写完整表单",trigger:"change"}],Check_Type:[{required:!0,message:"请填写完整表单",trigger:"change"}],Change_Check_Type:[{required:!0,validator:this.Check_Type_rules,message:"请填写完整表单",trigger:"change"}],Check_Style:[{required:!0,message:"请填写完整表单",trigger:"change"}]},rules_Zl:{required:!0,message:"请填写完整表单",trigger:"bur"},rules_Tc:{required:!0,message:"请填写完整表单",trigger:"bur"},ZL_UserIds_Rules:[{required:!0,validator:this.Check_ZL_UserIds,message:"请填写完整表单",trigger:"change"}],TC_UserIds_Rules:[{required:!0,validator:this.Check_TC_UserIds,message:"请填写完整表单",trigger:"change"}],title:"",editInfo:{},QualityNodeList:[{Name:"入库"},{Name:"出库"}],CheckTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],UserList:[],CheckStyleList:[{Name:"抽检",Id:0},{Name:"全检",Id:1}]}},computed:{Node_Code_Com:function(){return!!this.form.Node_Code}},mounted:function(){this.getFactoryPeoplelist()},methods:{Check_ZL_UserIds:function(e,t,n){this.form.Change_Check_Type.includes(1)&&0===this.form.ZL_UserIds.length?n(new Error("请填写完整表单")):n()},Check_TC_UserIds:function(e,t,n){this.Node_Code_Com||2!=this.form.Change_Check_Type[0]&&2!=this.form.Change_Check_Type.length||0!==this.form.TC_UserIds.length?n():n(new Error("请填写完整表单"))},Check_Type_rules:function(e,t,n){0===this.form.Change_Check_Type.length?n(new Error("请填写完整表单")):n()},SelectType:function(e){this.$forceUpdate(),this.form.Change_Check_Type=e,1==e.length?this.form.Check_Type=e[0]:2==e.length&&(this.form.Check_Type=-1),e.includes(1)||(this.form.ZL_UserId="",this.form.ZL_UserIds=[]),e.includes(2)||(this.form.TC_UserId="",this.form.TC_UserIds=[])},removeType:function(e){},clearType:function(e){this.form.ZL_UserId="",this.form.TC_UserId="",this.form.ZL_UserIds=[],this.form.TC_UserIds=[]},init:function(e,t,n){this.Check_Object_Id=t,this.title=e,"编辑"==e&&(this.form.Id=n.Id,this.getEntityNode(n)),this.getCheckNode()},addCheckNode:function(){var e=this;return(0,s.default)((0,i.default)().m((function t(){var n,a,s,u;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return n=e.form,a=n.Demand_Spot_Check_Rate,s=(0,r.default)(n,l),u=(0,o.default)((0,o.default)({},s),{},{Check_Object_Id:e.Check_Object_Id}),0===e.form.Check_Style&&(u.Demand_Spot_Check_Rate=a),t.n=1,(0,c.SaveNode)(u).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"保存成功"}),e.$emit("close"),e.dialogData={}):e.$message({type:"error",message:t.Message})}));case 1:return t.a(2)}}),t)})))()},getFactoryPeoplelist:function(){var e=this;(0,c.GetFactoryPeoplelist)().then((function(t){t.IsSucceed?e.UserList=t.Data:e.$message({type:"error",message:t.Message})}))},getCheckNode:function(){var e=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform");"2"===e&&(this.mode="factory");"factory"===this.mode?localStorage.getItem("CurReferenceId"):this.ProjectId},changeNodeCode:function(e){this.form={Node_Code:"",Change_Check_Type:[],Display_Name:"",TC_UserId:"",ZL_UserId:"",TC_UserIds:[],ZL_UserIds:[],Check_Style:""},this.form.Display_Name=e,this.form.Node_Code=null},changeZLUser:function(e){this.$forceUpdate(),this.form.ZL_UserId="";for(var t=0;t<e.length;t++)t==e.length-1?this.form.ZL_UserId+=e[t]:this.form.ZL_UserId+=e[t]+","},changeTCUser:function(e){this.$forceUpdate(),this.form.TC_UserId="";for(var t=0;t<e.length;t++)t==e.length-1?this.form.TC_UserId+=e[t]:this.form.TC_UserId+=e[t]+","},getEntityNode:function(e){var t=this;(0,c.GetEntityNode)({id:e.Id}).then((function(e){e.IsSucceed?(t.form=e.Data[0],t.form.Change_Check_Type=[],1==t.form.Check_Type||2==t.form.Check_Type?t.form.Change_Check_Type.push(t.form.Check_Type):-1==t.form.Check_Type?t.form.Change_Check_Type=[1,2]:t.form.Change_Check_Type=[],t.form.ZL_UserIds=t.form.ZL_UserId?t.form.ZL_UserId.split(","):[],t.form.TC_UserIds=t.form.TC_UserId?t.form.TC_UserId.split(","):[]):t.$message({type:"error",message:e.Message})}))},handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.addCheckNode()}))}}}},"4a38":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{height:"calc(100vh - 300px)"}},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","max-height":"100%",data:e.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t,a){return n("vxe-column",{key:a,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("span",[e._v(e._s(i[t.Code]||"-"))])]}}],null,!0)})})),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"160","show-overflow":"",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[!a.Node_Code||a.Node_Code&&"抽检"===a.Check_Style?n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editEvent(a)}}},[e._v("编辑")]):e._e(),a.Node_Code?e._e():n("el-divider",{attrs:{direction:"vertical"}}),a.Node_Code?e._e():n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.removeEvent(a)}}},[e._v("删除")])]}}])})],2)],1)},i=[]},"4eb8":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4de4"),n("7db0"),n("d81d"),n("e9f5"),n("910d"),n("f665"),n("ab43"),n("dca8"),n("d3b7"),n("c7cd");var i=a(n("c14f")),o=a(n("1da1")),r=n("6186"),s=n("fd31"),c=n("7de9"),l=n("8975");t.default={props:{checkType:{}},data:function(){return{columns:null,tbLoading:!1,TypeId:"",typeOption:"",tbData:[]}},watch:{checkType:{handler:function(e,t){this.checkType=e,this.getQualityList()},deep:!0}},mounted:function(){this.getQualityList(),this.getTypeList()},methods:{getTypeList:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n,a,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return n=null,a=null,t.n=1,(0,s.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:n=t.v,a=n.Data,n.IsSucceed?(e.typeOption=Object.freeze(a),e.typeOption.length>0&&(e.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id,e.fetchData())):e.$message({message:n.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},fetchData:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("Check_item_combination");case 1:return t.a(2)}}),t)})))()},getTableConfig:function(e){var t=this;(0,r.GetGridByCode)({code:e+","+this.typeOption.find((function(e){return e.Id===t.TypeId})).Code}).then((function(e){var n=e.IsSucceed,a=e.Data,i=e.Message;if(n){if(!a)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);t.tbLoading=!1;var o=a.ColumnList||[];t.columns=o.filter((function(e){return e.Is_Display})).map((function(e){return"CheckName"===e.Code&&(e.fixed="left"),e}))}else t.$message({message:i,type:"error"})}))},getQualityList:function(){var e=this;this.tbLoading=!0,(0,c.QualityList)({check_object_id:this.checkType.Id}).then((function(t){t.IsSucceed?(e.tbData=t.Data.map((function(e){switch(e.Check_Type){case 1:e.Check_Type="质量";break;case 2:e.Check_Type="探伤";break;case-1:e.Check_Type="质量、探伤";break;default:e.Check_Type=""}return e.Create_Date=(0,l.timeFormat)(e.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.tbLoading=!1):(e.$message({type:"error",message:t.Message}),e.tbLoading=!1)}))},removeEvent:function(e){var t=this;this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.DelQualityList)({id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.getQualityList()):t.$message({type:"error",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},editEvent:function(e){this.$emit("CombinationEdit",e)}}}},"51f3":function(e,t,n){"use strict";n.r(t);var a=n("1d9a"),i=n("b75c");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"666bbae6",null);t["default"]=s.exports},"5a96":function(e,t,n){},"5c15":function(e,t,n){"use strict";n("7293")},"63cb":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"90px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"质检节点",prop:"Display_Name"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.Node_Code_Com,clearable:"",filterable:"","allow-create":"",placeholder:"请输入质检节点"},on:{change:e.changeNodeCode},model:{value:e.form.Display_Name,callback:function(t){e.$set(e.form,"Display_Name",t)},expression:"form.Display_Name"}},e._l(e.QualityNodeList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Name,value:e.Name}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"质检类型",prop:"Change_Check_Type"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择质检类型",multiple:"",disabled:e.Node_Code_Com},on:{change:e.SelectType,"remove-tag":e.removeType},model:{value:e.form.Change_Check_Type,callback:function(t){e.$set(e.form,"Change_Check_Type",t)},expression:"form.Change_Check_Type"}},e._l(e.CheckTypeList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"质量员",prop:"ZL_UserIds",rules:e.ZL_UserIds_Rules}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",multiple:"",placeholder:"请选择质量员",disabled:e.Node_Code_Com||1!=e.form.Change_Check_Type[0]&&2!=e.form.Change_Check_Type.length},on:{change:e.changeZLUser},model:{value:e.form.ZL_UserIds,callback:function(t){e.$set(e.form,"ZL_UserIds",t)},expression:"form.ZL_UserIds"}},e._l(e.UserList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"探伤员",prop:"TC_UserIds",rules:e.TC_UserIds_Rules}},[n("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",multiple:"",disabled:e.Node_Code_Com||2!=e.form.Change_Check_Type[0]&&2!=e.form.Change_Check_Type.length,placeholder:"请选择探伤员"},on:{change:e.changeTCUser},model:{value:e.form.TC_UserIds,callback:function(t){e.$set(e.form,"TC_UserIds",t)},expression:"form.TC_UserIds"}},e._l(e.UserList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"质检方式",prop:"Check_Style"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",disabled:e.Node_Code_Com,placeholder:"请选择质检方式"},model:{value:e.form.Check_Style,callback:function(t){e.$set(e.form,"Check_Style",t)},expression:"form.Check_Style"}},e._l(e.CheckStyleList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1),0===e.form.Check_Style?n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"要求合格率",prop:"Demand_Spot_Check_Rate"}},[n("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0,max:100,placeholder:"请输入",clearable:""},model:{value:e.form.Demand_Spot_Check_Rate,callback:function(t){e.$set(e.form,"Demand_Spot_Check_Rate",t)},expression:"form.Demand_Spot_Check_Rate"}})],1)],1):e._e(),n("el-col",{attrs:{span:24}},[n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)],1)],1)],1)},i=[]},"64fc":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"80px"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"检查类型",prop:"Name"}},[n("el-input",{model:{value:e.form.Name,callback:function(t){e.$set(e.form,"Name",t)},expression:"form.Name"}})],1)],1),n("el-col",{attrs:{span:24}},[n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)],1)],1)],1)},i=[]},"685d":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("c14f")),o=a(n("1da1"));n("99af"),n("7db0"),n("caad"),n("d81d"),n("14d9"),n("a434"),n("e9f5"),n("d866"),n("f665"),n("7d54"),n("ab43"),n("e9c4"),n("b64b"),n("d3b7"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("159b"),n("ddb0");var r=n("7de9");t.default={data:function(){return{mode:"",ProjectId:"",Check_Object_Id:"",checkType:{},form:{Object_Type_Ids:[]},rules:{Check_Content:[{required:!0,message:"请填写完整表单",trigger:"blur"}],Eligibility_Criteria:[{required:!0,message:"请填写完整表单",trigger:"blur"}],Group_Name:[{required:!0,message:"请填写完整表单",trigger:"blur"}],Questionlab_Ids:[{required:!0,message:"请填写完整表单",trigger:"blur"}]},title:"",options:[],ProcessFlow:[],CheckTypeList:[],CheckItemList:[],Change_Check_Type:[],QualityTypeList:[{Name:"质量",Id:1},{Name:"探伤",Id:2}],ProCategoryList:[],CheckNodeList:[],verification:!1,ProCategoryCode:"",Eligibility_Criteria:"",ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Id"}},Isdisable:!1}},watch:{ProcessFlow:{handler:function(e,t){var n=this;this.form.Questionlab_Ids=[],this.ProcessFlow.forEach((function(e){e.Questionlab_Id&&!n.form.Questionlab_Ids.includes(e.Questionlab_Id)&&n.form.Questionlab_Ids.push(e.Questionlab_Id)}))},deep:!0}},mounted:function(){},methods:{init:function(e,t,n){var a=this;return(0,o.default)((0,i.default)().m((function o(){return(0,i.default)().w((function(i){while(1)switch(i.n){case 0:return i.n=1,a.getProfessionalType();case 1:return a.Check_Object_Id=t.Id,a.checkType=t,a.title=e,a.form.Check_Object_Id=t.Id,i.n=2,a.getCheckTypeList();case 2:return i.n=3,a.getCheckItemList();case 3:return i.n=4,a.getNodeList(n);case 4:return i.a(2)}}),o)})))()},addCheckItemCombination:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,r.AddCheckItemCombination)({Group:e.form,Items:e.ProcessFlow}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"保存成功"}),e.$emit("close"),e.dialogData={}):e.$message({type:"error",message:t.Message})}));case 1:return t.a(2)}}),t)})))()},removeTagFn:function(e,t){},SelectType:function(e){1===e.length?this.form.Check_Type=e[0]:this.form.Check_Type=-1},changeNode:function(e){e?(this.form.Check_Type=this.CheckNodeList.find((function(t){return t.Id===e})).Check_Type,this.Change_Check_Type=[],1===this.form.Check_Type||2===this.form.Check_Type?(this.Isdisable=!0,this.Change_Check_Type.push(this.form.Check_Type)):-1===this.form.Check_Type?(this.Isdisable=!1,this.Change_Check_Type=[1,2]):(this.Change_Check_Type=[],this.Isdisable=!1)):this.Change_Check_Type=[]},getEntityCheckType:function(e){var t=this;(0,r.EntityQualityList)({id:e.Id,check_object_id:this.Check_Object_Id}).then((function(e){e.IsSucceed?(t.form=e.Data[0].Group,t.ProcessFlow=e.Data[0].Items,t.Change_Check_Type=[],1===t.form.Check_Type||2===t.form.Check_Type?(t.Change_Check_Type.push(t.form.Check_Type),t.Isdisable=!0):-1===t.form.Check_Type?(t.Change_Check_Type=[1,2],t.Isdisable=!1):(t.Change_Check_Type=[],t.Isdisable=!1)):t.$message({type:"error",message:e.Message})}))},handleSubmit:function(e){var t=this,n=!0;if(0===this.ProcessFlow.length?n=!1:this.ProcessFlow.forEach((function(e){for(var t in e)""===e[t]&&(n=!1)})),n){var a=JSON.parse(JSON.stringify(this.ProcessFlow)),i=[];a.forEach((function(e){var t={};t.Check_Item_Id=e.Check_Item_Id,t.Eligibility_Criteria=e.Eligibility_Criteria,t.Questionlab_Id=e.Questionlab_Id,i.push(t)}));var o=i.map((function(e){return JSON.stringify(e)}));if(new Set(o).size===o.length){var r=this.ProcessFlow.map((function(e){return e.Questionlab_Id})),s=this.form.Questionlab_Ids.every((function(e){return r.includes(e)}));s?this.$refs[e].validate((function(e){if(!e)return!1;t.addCheckItemCombination()})):this.$message({type:"error",message:"检查项设置必须包含已选检查类型"})}else this.$message({type:"error",message:"检查项设置内容不能完全相同"})}else this.$message({type:"error",message:"请填写完整检查项设置内容"})},getProfessionalType:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(n=localStorage.getItem("Platform")||localStorage.getItem("CurPlatform"),"2"!==n){t.n=1;break}return e.mode="factory",t.n=1,(0,r.GetFactoryProfessionalByCode)().then((function(t){t.IsSucceed?e.ProCategoryList=t.Data:e.$message({type:"error",message:t.Message})}));case 1:"factory"===e.mode?localStorage.getItem("CurReferenceId"):e.ProjectId;case 2:return t.a(2)}}),t)})))()},getCheckTypeList:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,r.GetCheckTypeList)({check_object_id:e.Check_Object_Id}).then((function(t){t.IsSucceed?e.CheckTypeList=t.Data:e.$message({type:"error",message:t.Message})}));case 1:return t.a(2)}}),t)})))()},getCheckItemList:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,r.GetCheckItemList)({check_object_id:e.Check_Object_Id}).then((function(t){t.IsSucceed?e.CheckItemList=t.Data:e.$message({type:"error",message:t.Message})}));case 1:return t.a(2)}}),t)})))()},changeCategory:function(e){this.form.Object_Type_Ids=[],this.chooseType(e)},chooseType:function(e){this.ProCategoryCode=this.ProCategoryList.find((function(t){return t.Id===e})).Code,this.getObjectTypeList(this.ProCategoryCode)},ChangeCheckType:function(e){var t=this,n=Object.assign([],e);if(this.ProcessFlow.length>n.length){var a=n.map((function(e){var n={Check_Item_Id:"",Eligibility_Criteria:"",Questionlab_Id:e};return t.ProcessFlow.forEach((function(t){t.Questionlab_Id===e&&(n.Check_Item_Id=t.Check_Item_Id,n.Eligibility_Criteria=t.Eligibility_Criteria)})),n}));this.ProcessFlow=[].concat(a)}else for(var i=0;i<n.length;i++){var o=this.ProcessFlow.find((function(e){return e.Questionlab_Id===n[i]}));o||this.ProcessFlow.push({Questionlab_Id:n[i],Check_Item_Id:"",Eligibility_Criteria:""})}},removeCheckType:function(e){var t=this.ProcessFlow.find((function(t){return t.Questionlab_Id===e})),n=this.ProcessFlow.indexOf(t);t&&this.ProcessFlow.splice(n,1)},ChangeItem:function(e,t,n){var a;n.Eligibility_Criteria="",this.Eligibility_Criteria="",this.Eligibility_Criteria=null===(a=this.CheckItemList.find((function(t){return t.Id===e})))||void 0===a?void 0:a.Eligibility_Criteria,this.$set(this.ProcessFlow[t],"Eligibility_Criteria",this.Eligibility_Criteria),this.$set(this.ProcessFlow[t],"sort",t)},abc:function(e){var t=this;return(0,o.default)((0,i.default)().m((function n(){return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:if("编辑"!==t.title){n.n=1;break}return t.form.Id=e.Id,t.getEntityCheckType(e),n.n=1,t.chooseType(e.Pro_Category_Id);case 1:return n.a(2)}}),n)})))()},getNodeList:function(e){var t=this;return(0,o.default)((0,i.default)().m((function n(){return(0,i.default)().w((function(n){while(1)switch(n.n){case 0:return n.n=1,(0,r.GetNodeList)({check_object_id:t.Check_Object_Id}).then((function(n){n.IsSucceed?(t.CheckNodeList=n.Data,t.abc(e)):t.$message({type:"error",message:n.Message})}));case 1:return n.a(2)}}),n)})))()},getObjectTypeList:function(e){var t=this;"构件"===this.checkType.Display_Name?(0,r.GetCompTypeTree)({professional:e}).then((function(e){e.IsSucceed?(t.ObjectTypeList.data=e.Data,t.$nextTick((function(n){t.$refs.treeSelectObjectType.treeDataUpdateFun(e.Data)}))):t.$message({type:"error",message:e.Message})})):"物料"===this.checkType.Display_Name&&(0,r.GetMaterialType)({}).then((function(e){t.ObjectTypeList=e.Data}))},addTableData:function(){this.ProcessFlow.push({Check_Item_Id:"",Eligibility_Criteria:"",Questionlab_Id:""})},deleteRow:function(e,t){t.splice(e,1),this.ProcessFlow.length>0&&e!==this.ProcessFlow.length&&this.$set(this.ProcessFlow[e],"sort",e)},moveUpward:function(e,t){var n=this.ProcessFlow[t-1];this.ProcessFlow.splice(t-1,1),this.ProcessFlow.splice(t,0,n),this.$set(this.ProcessFlow[t-1],"sort",t-1),this.$set(this.ProcessFlow[t],"sort",t)},moveDown:function(e,t){var n=this.ProcessFlow[t+1];this.ProcessFlow.splice(t+1,1),this.ProcessFlow.splice(t,0,n),this.$set(this.ProcessFlow[t],"sort",t),this.$set(this.ProcessFlow[t+1],"sort",t+1)}}}},"6dd1":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{height:"calc(100vh - 300px)"}},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","max-height":"100%",data:e.tbData,stripe:"",resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t,a){return n("vxe-column",{key:a,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction:"","show-overflow":"tooltip",sortable:"",align:t.Align,field:t.Code,title:t.Display_Name},scopedSlots:e._u([{key:"default",fn:function(a){var i=a.row;return[n("span",[e._v(e._s(i[t.Code]||"-"))])]}}],null,!0)})})),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"150","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editEvent(a)}}},[e._v("编辑")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.removeEvent(a)}}},[e._v("删除")])]}}])})],2)],1)},i=[]},"6e1e":function(e,t,n){"use strict";n.r(t);var a=n("f6bec"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"720b":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n("7de9");t.default={props:{checkType:{}},data:function(){return{tbLoading:!1,tbData:[]}},watch:{checkType:{handler:function(e,t){this.checkType=e,this.getCheckItemList()},deep:!0}},mounted:function(){this.getCheckItemList()},methods:{getCheckItemList:function(){var e=this;this.tbLoading=!0,(0,a.GetCheckItemList)({check_object_id:this.checkType.Id}).then((function(t){t.IsSucceed?(e.tbData=t.Data,e.tbLoading=!1):(e.$message({type:"error",message:t.Message}),e.tbLoading=!1)}))},removeEvent:function(e){var t=this;this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,a.DeleteCheckItem)({id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.getCheckItemList()):t.$message({type:"error",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},editEvent:function(e){this.$emit("ItemEdit",e)}}}},7293:function(e,t,n){},7393:function(e,t,n){"use strict";n.r(t);var a=n("e266"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"76a1":function(e,t,n){"use strict";n.r(t);var a=n("d956"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"7de9":function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=h,t.AddCheckItemCombination=y,t.AddCheckType=c,t.DelNode=L,t.DelQualityList=I,t.DeleteCheckItem=f,t.DeleteCheckType=l,t.EntityCheckItem=m,t.EntityCheckType=r,t.EntityQualityList=k,t.ExportInspsectionSummaryInfo=D,t.GetCheckGroupList=g,t.GetCheckItemList=d,t.GetCheckTypeList=s,t.GetCompTypeTree=S,t.GetDictionaryDetailListByCode=o,t.GetEntityNode=w,t.GetFactoryPeoplelist=b,t.GetFactoryProfessionalByCode=O,t.GetMaterialType=P,t.GetNodeList=T,t.GetProEntities=_,t.GetProcessCodeList=C,t.QualityList=v,t.SaveCheckItem=p,t.SaveCheckType=u,t.SaveNode=x;var i=a(n("b775"));function o(e){return(0,i.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function r(e){return(0,i.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function h(e){return(0,i.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function m(e){return(0,i.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function p(e){return(0,i.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function g(e){return(0,i.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function y(e){return(0,i.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function _(e){return(0,i.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function b(e){return(0,i.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function C(e){return(0,i.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function k(e){return(0,i.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function I(e){return(0,i.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function T(e){return(0,i.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function w(e){return(0,i.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function L(e){return(0,i.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function x(e){return(0,i.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function S(e){return(0,i.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function O(e){return(0,i.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function P(e){return(0,i.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function D(e){return(0,i.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},8193:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"app-container abs100"},[n("div",{staticClass:"h100 wrapper-c parent"},[n("div",{staticClass:"title"},e._l(e.title,(function(t,a){return n("span",{key:a,class:e.spanCurr==a?"clickindex":"index",staticStyle:{cursor:"pointer"},on:{click:function(n){return e.handelIndex(a,t)}}},[e._v(e._s(t.Display_Name))])})),0),n("div",{staticClass:"detail"},[[n("el-tabs",{staticStyle:{width:"100%",height:"100%"},attrs:{type:"card"},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"检查类型",name:"检查类型"}},[n("CheckType",{ref:"checkTypeRef",attrs:{"check-type":e.checkType},on:{optionFn:e.optionEdit}})],1),n("el-tab-pane",{attrs:{label:"检查项",name:"检查项"}},[n("CheckItem",{ref:"checkItemRef",attrs:{"check-type":e.checkType},on:{ItemEdit:e.ItemEdit}})],1),n("el-tab-pane",{attrs:{label:"检查项组合",name:"检查项组合"}},[n("CheckCombination",{ref:"checkCombinationRef",attrs:{"check-type":e.checkType},on:{CombinationEdit:e.CombinationEdit}})],1),n("el-tab-pane",{attrs:{label:"质检节点配置",name:"质检节点配置"}},[n("CheckNode",{ref:"checkNodeRef",attrs:{"check-type":e.checkType},on:{NodeEdit:e.NodeEdit}})],1),e.isCom?n("el-tab-pane",{attrs:{label:"公差配置",name:"公差配置"}},[n("ToleranceConfig",{ref:"toleranceConfigRef",attrs:{"check-type":e.checkType},on:{edit:e.addToleranceConfig}})],1):e._e(),n("el-button",{staticClass:"addbtn",attrs:{type:"primary"},on:{click:e.addData}},[e._v("新增")])],1)]],2)])]),e.dialogVisible?n("el-dialog",{directives:[{name:"el-drag-dialog",rawName:"v-el-drag-dialog"}],ref:"content",staticClass:"z-dialog",attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:e.width},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[n(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-data":e.dialogData},on:{ToleranceRefresh:e.ToleranceRefresh,close:e.handleClose}})],1):e._e()],1)},i=[]},"898c":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d81d"),n("e9f5"),n("ab43"),n("e9c4"),n("b64b"),n("d3b7");var a=n("d51a");t.default={data:function(){return{tbLoading:!1,tbData:[],dialogVisible:!1,form:{Id:"",Length:0,Demand:"",Type:1}}},mounted:function(){this.getToleranceList()},methods:{getToleranceList:function(){var e=this;this.tbLoading=!0,(0,a.GetToleranceSettingList)({}).then((function(t){t.IsSucceed?e.tbData=t.Data.map((function(e){return e.Modify_Date=e.Modify_Date||e.Create_Date,e.TypeTag=1===e.Type?"<":2===e.Type?"<=":3===e.Type?">":4===e.Type?">=":"=",e})):e.$message({type:"error",message:t.Message})})).finally((function(){e.tbLoading=!1}))},editEvent:function(e){var t=JSON.parse(JSON.stringify(e));this.$emit("edit",t)},removeEvent:function(e){var t=this;this.$confirm("此操作将永久删除该公差配置, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,a.DeleteToleranceSetting)({id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.getToleranceList()):t.$message({type:"error",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))}}}},"8e5c":function(e,t,n){"use strict";n.r(t);var a=n("4eb8"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"8ff3":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-form",{ref:"form",attrs:{rules:e.rules,model:e.form,"label-width":"120px"}},[n("el-row",[n("el-form-item",{attrs:{label:"检查项组合名称",prop:"Group_Name"}},[n("el-input",{model:{value:e.form.Group_Name,callback:function(t){e.$set(e.form,"Group_Name",t)},expression:"form.Group_Name"}})],1),n("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择质检节点"},on:{change:e.changeNode},model:{value:e.form.Check_Node_Id,callback:function(t){e.$set(e.form,"Check_Node_Id",t)},expression:"form.Check_Node_Id"}},e._l(e.CheckNodeList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",multiple:"",disabled:e.Isdisable,placeholder:"请选择质检类型"},on:{change:e.SelectType},model:{value:e.Change_Check_Type,callback:function(t){e.Change_Check_Type=t},expression:"Change_Check_Type"}},e._l(e.QualityTypeList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1),n("el-form-item",{attrs:{label:"专业类别",prop:"Pro_Category_Id"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择专业类别"},on:{change:e.changeCategory},model:{value:e.form.Pro_Category_Id,callback:function(t){e.$set(e.form,"Pro_Category_Id",t)},expression:"form.Pro_Category_Id"}},e._l(e.ProCategoryList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1),"零件"!==e.checkType.Display_Name&&"部件"!==e.checkType.Display_Name?n("el-form-item",{attrs:{label:"对象类型",prop:"Object_Type_Ids"}},[n("el-tree-select",{ref:"treeSelectObjectType",staticClass:"cs-tree-x",attrs:{disabled:!Boolean(e.form.Pro_Category_Id),"tree-params":e.ObjectTypeList,"value-key":"Id"},on:{removeTag:e.removeTagFn},model:{value:e.form.Object_Type_Ids,callback:function(t){e.$set(e.form,"Object_Type_Ids",t)},expression:"form.Object_Type_Ids"}})],1):e._e(),n("el-form-item",{attrs:{label:"检查类型",prop:"Questionlab_Ids"}},[n("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择检查类型"},on:{change:e.ChangeCheckType,"remove-tag":e.removeCheckType},model:{value:e.form.Questionlab_Ids,callback:function(t){e.$set(e.form,"Questionlab_Ids",t)},expression:"form.Questionlab_Ids"}},e._l(e.CheckTypeList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)],1),n("el-col",{attrs:{span:24}},[n("h3",[e._v("检查项设置")]),n("el-form-item",{staticClass:"checkItem",attrs:{label:"",prop:""}},[n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.ProcessFlow,border:""}},[n("el-table-column",{attrs:{prop:"",label:"*检查类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:a.Questionlab_Id,callback:function(t){e.$set(a,"Questionlab_Id",t)},expression:"row.Questionlab_Id"}},e._l(e.CheckTypeList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Name,value:e.Id}})})),1)]}}])}),n("el-table-column",{attrs:{prop:"",label:"*检查项内容",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,i=t.$index;return[n("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},on:{change:function(t){return e.ChangeItem(t,i,a)}},model:{value:a.Check_Item_Id,callback:function(t){e.$set(a,"Check_Item_Id",t)},expression:"row.Check_Item_Id"}},e._l(e.CheckItemList,(function(e,t){return n("el-option",{key:t,attrs:{label:e.Check_Content,value:e.Id}})})),1)]}}])}),n("el-table-column",{attrs:{prop:"",label:"*合格标准",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-input",{attrs:{disabled:""},model:{value:t.row.Eligibility_Criteria,callback:function(n){e.$set(t.row,"Eligibility_Criteria",n)},expression:"scope.row.Eligibility_Criteria"}})]}}])}),n("el-table-column",{attrs:{prop:"address",label:"操作",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row,i=t.$index;return[n("el-button",{attrs:{type:"text",icon:"el-icon-top",disabled:0==i},on:{click:function(t){return e.moveUpward(a,i)}}}),n("el-button",{attrs:{type:"text",icon:"el-icon-bottom",disabled:i==e.ProcessFlow.length-1},on:{click:function(t){return e.moveDown(a,i)}}}),n("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",icon:"el-icon-delete"},nativeOn:{click:function(t){return t.preventDefault(),e.deleteRow(i,e.ProcessFlow)}}})]}}])})],1)],1)],1),n("el-col",{attrs:{span:24}},[n("el-button",{staticClass:"addcheckItem",attrs:{type:"text"},on:{click:e.addTableData}},[e._v("+ 新增检查项")])],1),n("el-col",{staticStyle:{"text-align":"right"},attrs:{span:24}},[n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("关 闭")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit("form")}}},[e._v("确 定")])],1)],1)],1)],1)],1)},i=[]},"92e4":function(e,t,n){"use strict";n.r(t);var a=n("ade6"),i=n("355d");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("5c15");var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"098d35a8",null);t["default"]=s.exports},9852:function(e,t,n){"use strict";n.r(t);var a=n("6dd1"),i=n("8e5c");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"6ac1c22c",null);t["default"]=s.exports},9903:function(e,t,n){"use strict";n("0629")},a25e:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{height:"calc(100vh - 300px)"}},[n("vxe-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","element-loading-spinner":"el-icon-loading","element-loading-text":"拼命加载中","empty-text":"暂无数据","max-height":"100%",align:"left",stripe:"",data:e.tbData,resizable:"","auto-resize":!0,"tooltip-config":{enterable:!0}}},[n("vxe-column",{attrs:{"show-overflow":"tooltip",sortable:"",field:"Name",title:"检查类型",width:"calc(100vh-200px)"}}),n("vxe-column",{attrs:{fixed:"right",title:"操作",width:"200","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.editEvent(a)}}},[e._v("编辑")]),n("el-divider",{attrs:{direction:"vertical"}}),n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.removeEvent(a)}}},[e._v("删除")])]}}])})],1)],1)},i=[]},a272:function(e,t,n){"use strict";n.r(t);var a=n("8193"),i=n("2ef3");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("9903");var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"8e1bf2f8",null);t["default"]=s.exports},a501:function(e,t,n){"use strict";n.r(t);var a=n("4381"),i=n("76a1");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"46c5b523",null);t["default"]=s.exports},a888:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("d565")),o=function(e){e.directive("el-drag-dialog",i.default)};window.Vue&&(window["el-drag-dialog"]=i.default,Vue.use(o)),i.default.install=o;t.default=i.default},a91e:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("7db0"),n("e9f5"),n("f665"),n("d3b7");var i=a(n("d0099")),o=a(n("9852")),r=a(n("bfba")),s=a(n("51f3")),c=a(n("03ed")),l=a(n("a501")),u=a(n("e0da")),d=a(n("2b52")),f=a(n("350b")),h=n("7de9"),m=a(n("a888")),p=a(n("92e4"));t.default={name:"PLMFactoryGroupList",directives:{elDragDialog:m.default},components:{CheckType:i.default,ToleranceConfig:f.default,CheckCombination:o.default,CheckNode:r.default,CheckItem:s.default,TypeDialog:c.default,ItemDialog:l.default,CombinationDialog:u.default,NodeDialog:d.default,ToleranceDialog:p.default},data:function(){return{spanCurr:0,title:[],activeName:"检查类型",checkType:{},tbLoading:!1,tbData:[],dialogVisible:!1,currentComponent:"",dialogTitle:"",isCom:!1,width:"60%",dialogData:{}}},created:function(){},mounted:function(){this.getCheckType()},methods:{getCheckType:function(){var e=this;(0,h.GetDictionaryDetailListByCode)({dictionaryCode:"Quality_Code"}).then((function(t){t.IsSucceed?(e.title=t.Data,e.checkType=e.title[0],e.isCom=t.Data.find((function(e){return"0"===e.Value}))):e.$message({type:"error",message:"res.Message"})}))},handelIndex:function(e,t){this.isCom="0"===t.Value,this.isCom||"公差配置"!==this.activeName||(this.activeName="检查类型"),this.checkType=t,this.spanCurr=e},addData:function(){switch(this.activeName){case"检查类型":this.addCheckType();break;case"检查项":this.addCheckItem();break;case"检查项组合":this.addCheckCombination();break;case"质检节点配置":this.addCheckNode();break;case"公差配置":this.addToleranceConfig();break;default:this.addCheckType()}},addCheckType:function(){var e=this;this.width="30%",this.generateComponent("新增检查类型","TypeDialog"),this.$nextTick((function(t){e.$refs["content"].init("新增",e.checkType.Id)}))},editCheckType:function(e){var t=this;this.width="30%",this.generateComponent("编辑检查类型","TypeDialog"),this.$nextTick((function(n){t.$refs["content"].init("编辑",t.checkType.Id,e)}))},addCheckItem:function(){var e=this;this.width="30%",this.generateComponent("新增检查项","ItemDialog"),this.$nextTick((function(t){e.$refs["content"].init("新增",e.checkType.Id)}))},editCheckItem:function(e){var t=this;this.width="30%",this.generateComponent("编辑检查项","ItemDialog"),this.$nextTick((function(n){t.$refs["content"].init("编辑",t.checkType.Id,e)}))},addCheckCombination:function(){var e=this;this.width="40%",this.generateComponent("新增检查项组合","CombinationDialog"),this.$nextTick((function(t){e.$refs["content"].init("新增",e.checkType)}))},editCheckCombination:function(e){var t=this;this.width="40%",this.generateComponent("编辑检查项组合","CombinationDialog"),this.$nextTick((function(n){t.$refs["content"].init("编辑",t.checkType,e)}))},addCheckNode:function(){var e=this;this.width="45%",this.generateComponent("新增质检节点配置","NodeDialog"),this.$nextTick((function(t){e.$refs["content"].init("新增",e.checkType.Id)}))},editCheckNode:function(e){var t=this;this.width="45%",this.generateComponent("编辑质检节点配置","NodeDialog"),this.$nextTick((function(n){t.$refs["content"].init("编辑",t.checkType.Id,e)}))},addToleranceConfig:function(e){var t=this;this.width="45%",this.generateComponent(null!==e&&void 0!==e&&e.Id?"编辑公差配置":"新增公差配置","ToleranceDialog"),this.$nextTick((function(n){t.$refs["content"].init(null!==e&&void 0!==e&&e.Id?"编辑":"新增",t.checkType.Id,e)}))},handleClose:function(){switch(this.activeName){case"检查类型":this.$refs.checkTypeRef.getCheckTypeList();break;case"检查项":this.$refs.checkItemRef.getCheckItemList();break;case"检查项组合":this.$refs.checkCombinationRef.getQualityList();break;case"质检节点配置":this.$refs.checkNodeRef.getNodeList();break}this.dialogVisible=!1},generateComponent:function(e,t){this.dialogTitle=e,this.currentComponent=t,this.dialogVisible=!0},optionEdit:function(e){this.editCheckType(e)},ItemEdit:function(e){this.editCheckItem(e)},CombinationEdit:function(e){this.editCheckCombination(e)},NodeEdit:function(e){this.editCheckNode(e)},ToleranceRefresh:function(e){this.$refs.toleranceConfigRef.getToleranceList()}}}},ade6:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-form",{ref:"formRef",attrs:{model:e.form,"label-width":"120px",rules:e.rules}},[n("el-form-item",{attrs:{label:"构件长度",prop:"Length"}},[n("div",{staticClass:"cs-flex"},[n("el-select",{staticStyle:{"margin-right":"10px",width:"100px"},attrs:{placeholder:"请选择"},model:{value:e.form.Type,callback:function(t){e.$set(e.form,"Type",t)},expression:"form.Type"}},e._l(e.typeOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),n("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{min:0,clearble:""},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length",e._n(t))},expression:"form.Length"}})],1)]),n("el-form-item",{attrs:{label:"公差要求",prop:"Demand"}},[n("el-input",{attrs:{placeholder:"请输入公差要求"},model:{value:e.form.Demand,callback:function(t){e.$set(e.form,"Demand",t)},expression:"form.Demand"}})],1),n("el-form-item",{staticStyle:{"text-align":"right"}},[n("el-button",{on:{click:e.handleClose}},[e._v("取消")]),n("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.submitForm}},[e._v("确定")])],1)],1)},i=[]},af88:function(e,t,n){"use strict";n.r(t);var a=n("685d"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},b75c:function(e,t,n){"use strict";n.r(t);var a=n("720b"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},b8ad:function(e,t,n){"use strict";n.r(t);var a=n("898c"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},bfba:function(e,t,n){"use strict";n.r(t);var a=n("4a38"),i=n("36ba");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"5bd46cd0",null);t["default"]=s.exports},c9fc:function(e,t,n){"use strict";n.r(t);var a=n("4618"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},ccb5:function(e,t,n){"use strict";function a(e,t){if(null==e)return{};var n={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;n[a]=e[a]}return n}Object.defineProperty(t,"__esModule",{value:!0}),t.default=a},d0099:function(e,t,n){"use strict";n.r(t);var a=n("a25e"),i=n("6e1e");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("0ced");var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"9342a916",null);t["default"]=s.exports},d51a:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddLanch=s,t.BatchManageSaveCheck=_,t.DelLanch=y,t.DeleteToleranceSetting=D,t.EntityQualityManagement=d,t.ExportQISummary=x,t.GetCheckingEntity=C,t.GetCompPartForSpotCheckPageList=T,t.GetCompQISummary=L,t.GetDictionaryDetailListByCode=c,t.GetEditById=b,t.GetFactoryPeoplelist=h,t.GetNodeList=l,t.GetPageFeedBack=v,t.GetPageQualityManagement=o,t.GetPartAndSteelBacrode=u,t.GetSheetDwg=m,t.GetSpotCheckingEntity=w,t.GetToleranceSettingList=$,t.ImportQISummary=S,t.ManageAdd=r,t.RectificationRecord=I,t.SaveFeedBack=k,t.SavePass=p,t.SaveQIReportData=O,t.SaveTesting=f,t.SaveToleranceSetting=P,t.SubmitLanch=g;var i=a(n("b775"));function o(e){return(0,i.default)({url:"/PRO/Inspection/GetPageQualityManagement",method:"post",data:e})}function r(e){return(0,i.default)({url:"/PRO/Inspection/ManageAdd",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PRO/Inspection/AddLanch",method:"post",data:e})}function c(e){return(0,i.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function l(e){return(0,i.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PRO/Inspection/GetPartAndSteelBacrode",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/Inspection/EntityQualityManagement",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PRO/Inspection/SaveTesting",method:"post",data:e})}function h(e){return(0,i.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function m(e){return(0,i.default)({url:"/PRO/Inspection/GetSheetDwg",method:"post",data:e})}function p(e){return(0,i.default)({url:"/PRO/Inspection/SavePass",method:"post",data:e,timeout:12e5})}function g(e){return(0,i.default)({url:"/PRO/Inspection/SubmitLanch",method:"post",data:e})}function y(e){return(0,i.default)({url:"/PRO/Inspection/DelLanch",method:"post",data:e})}function _(e){return(0,i.default)({url:"/PRO/Inspection/BatchManageSaveCheck",method:"post",data:e})}function b(e){return(0,i.default)({url:"/PRO/Inspection/GetEditById",method:"post",data:e})}function C(e){return(0,i.default)({url:"/PRO/Inspection/GetCheckingEntity",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PRO/Inspection/GetPageFeedBack",method:"post",data:e})}function k(e){return(0,i.default)({url:"/PRO/Inspection/SaveFeedBack",method:"post",data:e})}function I(e){return(0,i.default)({url:"/PRO/Inspection/RectificationRecord",method:"post",data:e})}function T(e){return(0,i.default)({url:"/PRO/Inspection/GetCompPartForSpotCheckPageList",method:"post",data:e})}function w(e){return(0,i.default)({url:"/PRO/Inspection/GetSpotCheckingEntity",method:"post",data:e})}function L(e){return(0,i.default)({url:"/PRO/Inspection/GetCompQISummary",method:"post",data:e})}function x(e){return(0,i.default)({url:"/PRO/Inspection/ExportQISummary",method:"post",data:e})}function S(e){return(0,i.default)({url:"/PRO/Inspection/ImportQISummary",method:"post",data:e})}function O(e){return(0,i.default)({url:"/PRO/Inspection/SaveQIReportData",method:"post",data:e})}function P(e){return(0,i.default)({url:"/pro/Inspection/SaveToleranceSetting",method:"post",data:e})}function D(e){return(0,i.default)({url:"/pro/Inspection/DeleteToleranceSetting",method:"post",data:e})}function $(e){return(0,i.default)({url:"/pro/Inspection/GetToleranceSettingList",method:"post",data:e})}},d565:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("caad"),n("ac1f"),n("2532"),n("5319");t.default={bind:function(e,t,n){var a=e.querySelector(".el-dialog__header"),i=e.querySelector(".el-dialog");a.style.cssText+=";cursor:move;",i.style.cssText+=";top:0px;";var o=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();a.onmousedown=function(e){var t=e.clientX-a.offsetLeft,r=e.clientY-a.offsetTop,s=i.offsetWidth,c=i.offsetHeight,l=document.body.clientWidth,u=document.body.clientHeight,d=i.offsetLeft,f=l-i.offsetLeft-s,h=i.offsetTop,m=u-i.offsetTop-c,p=o(i,"left"),g=o(i,"top");p.includes("%")?(p=+document.body.clientWidth*(+p.replace(/\%/g,"")/100),g=+document.body.clientHeight*(+g.replace(/\%/g,"")/100)):(p=+p.replace(/\px/g,""),g=+g.replace(/\px/g,"")),document.onmousemove=function(e){var a=e.clientX-t,o=e.clientY-r;-a>d?a=-d:a>f&&(a=f),-o>h?o=-h:o>m&&(o=m),i.style.cssText+=";left:".concat(a+p,"px;top:").concat(o+g,"px;"),n.child.$emit("dragDialog")},document.onmouseup=function(e){document.onmousemove=null,document.onmouseup=null}}}}},d956:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("c14f")),o=a(n("5530")),r=a(n("1da1")),s=n("7de9");t.default={data:function(){return{check_object_id:"",form:{},rules:{Check_Content:[{required:!0,message:"请填写完整表单",trigger:"blur"}],Eligibility_Criteria:[{required:!0,message:"请填写完整表单",trigger:"blur"}]},title:"",editInfo:{}}},mounted:function(){},methods:{init:function(e,t,n){this.title=e,"新增"==e?this.Check_Object_Id=t:(this.Check_Object_Id=t,this.editInfo=n,this.getEntityCheckType(n))},addCheckType:function(){var e=this;return(0,r.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.AddCheckItem)((0,o.default)((0,o.default)({},e.form),{},{Check_Object_Id:e.Check_Object_Id})).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"保存成功"}),e.$emit("close"),e.dialogData={}):e.$message({type:"error",message:t.Message})}));case 1:return t.a(2)}}),t)})))()},getEntityCheckType:function(e){var t=this;(0,s.EntityCheckItem)({id:e.Id}).then((function(e){e.IsSucceed?t.form=e.Data[0]:t.$message({type:"error",message:e.Message})}))},editCheckType:function(){var e=this;(0,s.SaveCheckItem)((0,o.default)((0,o.default)({Id:this.editInfo.Id},this.form),{},{Check_Object_Id:this.Check_Object_Id})).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"编辑成功"}),e.$emit("close")):e.$message({type:"error",message:t.Message})}))},handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;"新增"==t.title?t.addCheckType():t.editCheckType()}))}}}},dca8:function(e,t,n){"use strict";var a=n("23e7"),i=n("bb2f"),o=n("d039"),r=n("861d"),s=n("f183").onFreeze,c=Object.freeze,l=o((function(){c(1)}));a({target:"Object",stat:!0,forced:l,sham:!i},{freeze:function(e){return c&&r(e)?c(s(e)):e}})},dd23:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("7db0"),n("d81d"),n("e9f5"),n("f665"),n("ab43"),n("dca8"),n("d3b7");var i=a(n("c14f")),o=a(n("1da1")),r=n("6186"),s=n("7de9"),c=n("fd31"),l=n("8975");t.default={props:{checkType:{}},data:function(){return{tbData:[],columns:[],tbLoading:!1}},watch:{checkType:{handler:function(e,t){this.checkType=e,this.getNodeList()},deep:!0}},mounted:function(){this.getNodeList(),this.getTypeList()},methods:{getTypeList:function(){var e=this;return(0,o.default)((0,i.default)().m((function t(){var n,a,o;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,c.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")});case 1:n=t.v,a=n.Data,n.IsSucceed?(e.typeOption=Object.freeze(a),e.typeOption.length>0&&(e.TypeId=null===(o=e.typeOption[0])||void 0===o?void 0:o.Id,e.fetchData())):e.$message({message:n.Message,type:"error"});case 2:return t.a(2)}}),t)})))()},fetchData:function(){this.getTableConfig("Quality_Inspection_Node")},getNodeList:function(){var e=this;this.tbLoading=!0,(0,s.GetNodeList)({check_object_id:this.checkType.Id}).then((function(t){t.IsSucceed?(e.tbData=t.Data.map((function(e){switch(e.Check_Style){case 0:e.Check_Style="抽检";break;case 1:e.Check_Style="全检";break;default:e.Check_Style=""}switch(e.Check_Type){case 1:e.Check_Type="质量";break;case 2:e.Check_Type="探伤";break;case-1:e.Check_Type="质量/探伤";break;default:e.Check_Type=""}return e.Create_Date=(0,l.timeFormat)(e.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"),e})),e.tbLoading=!1):(e.$message({type:"error",message:t.Message}),e.tbLoading=!1)}))},getTableConfig:function(e){var t=this;(0,r.GetGridByCode)({code:e+","+this.typeOption.find((function(e){return e.Id===t.TypeId})).Code}).then((function(e){var n=e.IsSucceed,a=e.Data,i=e.Message;if(n){if(!a)return t.$message.error("当前专业没有配置相对应表格"),void(t.tbLoading=!0);var o=a.ColumnList||[];t.columns=o,t.tbLoading=!1}else t.$message({message:i,type:"error"})}))},removeEvent:function(e){var t=this;this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,s.DelNode)({id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.getNodeList()):t.$message({type:"error",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},editEvent:function(e){this.$emit("NodeEdit",e)}}}},dfc3:function(e,t,n){},e0da:function(e,t,n){"use strict";n.r(t);var a=n("8ff3"),i=n("af88");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("f2b5");var r=n("2877"),s=Object(r["a"])(i["default"],a["a"],a["b"],!1,null,"8b0f1ff8",null);t["default"]=s.exports},e266:function(e,t,n){"use strict";var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("5530")),o=a(n("c14f")),r=a(n("1da1")),s=n("7de9");t.default={data:function(){return{check_object_id:"",form:{},rules:{Name:[{required:!0,message:"请填写完整表单",trigger:"blur"}]},title:"",editInfo:{}}},mounted:function(){},methods:{init:function(e,t,n){this.title=e,"新增"==e?this.Check_Object_Id=t:(this.Check_Object_Id=t,this.editInfo=n,this.getEntityCheckType(n))},addCheckType:function(){var e=this;return(0,r.default)((0,o.default)().m((function t(){return(0,o.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.AddCheckType)({Name:e.form.Name,Check_Object_Id:e.Check_Object_Id}).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"保存成功"}),e.$emit("close"),e.dialogData={}):e.$message({type:"error",message:t.Message})}));case 1:return t.a(2)}}),t)})))()},getEntityCheckType:function(e){var t=this;(0,s.EntityCheckType)({id:e.Id}).then((function(e){e.IsSucceed?t.form=e.Data[0]:t.$message({type:"error",message:e.Message})}))},editCheckType:function(){var e=this;(0,s.SaveCheckType)((0,i.default)((0,i.default)({Id:this.editInfo.Id},this.form),{},{Check_Object_Id:this.Check_Object_Id})).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"编辑成功"}),e.$emit("close")):e.$message({type:"error",message:t.Message})}))},handleSubmit:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;"新增"==t.title?t.addCheckType():t.editCheckType()}))}}}},e57a:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d3b7");var a=n("d51a");t.default={data:function(){return{loading:!1,typeOptions:[{label:"<",value:1},{label:"<=",value:2},{label:">",value:3},{label:">=",value:4},{label:"=",value:5}],form:{Length:"",Demand:"",Type:1},rules:{Length:[{required:!0,message:"请选择构件长度"}],Demand:[{required:!0,message:"请输入公差要求"}]}}},methods:{init:function(e,t,n){"编辑"===e&&n&&(this.form.Id=n.Id,this.form.Length=n.Length,this.form.Demand=n.Demand,this.form.Type=n.Type)},handleClose:function(){this.$emit("close")},submitForm:function(){var e=this;this.$refs.formRef.validate((function(t){t&&(e.loading=!0,(0,a.SaveToleranceSetting)(e.form).then((function(t){t.IsSucceed?(e.$message.success("保存成功"),e.$emit("ToleranceRefresh"),e.handleClose()):e.$message.error(t.Message)})).finally((function(){e.loading=!1})))}))}}}},f2b5:function(e,t,n){"use strict";n("5a96")},f6bec:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n("7de9");t.default={props:{checkType:{}},data:function(){return{tbLoading:!1,tbData:[]}},watch:{checkType:{handler:function(e,t){this.checkType=e,this.getCheckTypeList()},deep:!0}},mounted:function(){this.getCheckTypeList()},methods:{getCheckTypeList:function(){var e=this;this.tbLoading=!0,(0,a.GetCheckTypeList)({check_object_id:this.checkType.Id}).then((function(t){t.IsSucceed?(e.tbData=t.Data,e.tbLoading=!1):(e.$message({type:"error",message:t.Message}),e.tbLoading=!1)}))},removeEvent:function(e){var t=this;this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,a.DeleteCheckType)({id:e.Id}).then((function(e){e.IsSucceed?(t.$message({type:"success",message:"删除成功!"}),t.getCheckTypeList()):t.$message({type:"error",message:e.Message})}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},editEvent:function(e){this.$emit("optionFn",e)}}}}}]);