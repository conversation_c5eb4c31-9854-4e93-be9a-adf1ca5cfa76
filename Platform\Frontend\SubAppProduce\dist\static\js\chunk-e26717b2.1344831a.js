(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-e26717b2"],{1020:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{size:"mini",icon:"el-icon-arrow-left",circle:""},on:{click:t.tagBack}}),a("span",[t._v("新增成品报废单")]),a("div",{staticClass:"right-fix"},[a("el-button",{attrs:{loading:t.btnLoading,type:"primary",disabled:t.isClicked},on:{click:t.save}},[t._v("保存")]),a("el-button",{on:{click:t.tagBack}},[t._v("取消")])],1)],1),a("div",{staticClass:"header"},[a("el-form",{staticClass:"h-adv-form",attrs:{inline:!0,model:t.fiterArrObj}},[a("el-form-item",{attrs:{label:"项目名称",required:""}},[a("el-select",{attrs:{placeholder:"选择项目",disabled:""},model:{value:t.fiterArrObj.Project_Id,callback:function(e){t.$set(t.fiterArrObj,"Project_Id",e)},expression:"fiterArrObj.Project_Id"}},t._l(t.projects,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("el-form-item",{staticClass:"flex-fix",staticStyle:{flex:"1"},attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"备注..."},model:{value:t.fiterArrObj.Remark,callback:function(e){t.$set(t.fiterArrObj,"Remark",e)},expression:"fiterArrObj.Remark"}})],1)],1)],1),a("div",{staticClass:"header",staticStyle:{padding:"16px 0"}},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.openDialog({title:"添加明细",component:"AddDetail",width:"750px",props:{project:t.project,used:t.data}})}}},[t._v("添加明细")]),a("el-button",{attrs:{disabled:t.checkedRows.length<=0,type:"primary",size:"mini"},on:{click:t.deleteRows}},[t._v("删除明细")])],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""},on:{multiSelectedChange:t.multiSelectedChange}},[a("template",{slot:"append"},[a("div",{staticClass:"tb-status"},[a("strong",[t._v("合计: ")]),a("span",[t._v(t._s(t.data.length)+" 个")]),a("span")])])],2)],1)]),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},o=[]},"209b":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CheckPackCode=d,e.ExportComponentStockInInfo=p,e.ExportPackingInInfo=m,e.ExportWaitingStockIn2ndList=j,e.FinishCollect=y,e.From_Stock_Status_TYPES=void 0,e.GetComponentStockInEntity=c,e.GetLocationList=r,e.GetPackingDetailList=f,e.GetPackingEntity=g,e.GetPackingGroupByDirectDetailList=u,e.GetStockInDetailList=s,e.GetStockMoveDetailList=k,e.GetWarehouseListOfCurFactory=i,e.HandleInventoryItem=P,e.PackingTypes=e.PackingStatus=e.InventoryComponentTypes=e.InventoryCheckStatus=e.InventoryCheckExceptions=void 0,e.RemoveMain=v,e.SaveComponentScrap=I,e.SaveInventory=C,e.SavePacking=h,e.SaveStockIn=l,e.SaveStockMove=S,e.StockInTypes=void 0,e.UnzipPacking=b,e.UpdateBillReady=O,e.UpdateMaterialReady=_;var o=n(a("b775"));n(a("4328")),e.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],e.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],e.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],e.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],e.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],e.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],e.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function i(t){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function r(t){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:t})}function c(t){return(0,o.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:t}})}function s(t,e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:t,isEdit:e}})}function d(t,e){return(0,o.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:t,id:e}})}function u(t){return(0,o.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:t}})}function m(t){return(0,o.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:t}})}function v(t){return(0,o.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:t}})}function h(t){return(0,o.default)({url:"/PRO/Packing/SavePacking",method:"post",data:t})}function b(t){var e=t.id,a=t.locationId;return(0,o.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:e,locationId:a}})}function g(t){var e=t.id,a=t.code;return(0,o.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:e,code:a}})}function k(t){return(0,o.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:t}})}function S(t){return(0,o.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:t})}function C(t){return(0,o.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:t})}function P(t){var e=t.id,a=t.type,n=t.value;return(0,o.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:e,type:a,value:n}})}function I(t){return(0,o.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:t})}function y(t){return(0,o.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:t}})}function O(t){var e=t.installId,a=t.isReady;return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:e,isReady:a}})}function _(t){return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:t})}function j(t){return(0,o.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:t})}},2950:function(t,e,a){},3179:function(t,e,a){"use strict";a.r(e);var n=a("1020"),o=a("a42b");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("fb56"),a("8611a");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"163f9c27",null);e["default"]=l.exports},"5fb3":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("5530"));a("99af"),a("4de4"),a("d81d"),a("e9f5"),a("910d"),a("ab43"),a("d3b7"),a("25f0"),a("3ca3"),a("ddb0");var i=a("1b69"),r=n(a("0f97")),l=n(a("ef75")),c=a("6186"),s=a("209b"),d=a("ed08");e.default={name:"AddStock",components:{DynamicDataTable:r.default,AddDetail:l.default},data:function(){return{loading:!1,btnLoading:!1,isClicked:!1,gridCode:"pro_component_scrap_detail_list",projects:[],fiterArrObj:{Project_Id:"",Remark:""},tbConfig:{},columns:[],data:[],checkedRows:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},computed:{project:function(){var t;return null===(t=this.$route.params)||void 0===t?void 0:t.project}},created:function(){var t=this;this.fiterArrObj.Project_Id=this.project.Id,Promise.all([(0,i.GetProjectList)({}).then((function(e){e.IsSucceed&&(t.projects=e.Data)}))]).then((function(){(0,c.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))}))}))},methods:{tagBack:function(){(0,d.closeTagView)(this.$store,this.$route)},multiSelectedChange:function(t){this.checkedRows=t},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120,Is_Select:!0})},setCols:function(t){this.columns=t.concat([])},setGridData:function(t){this.data=t.Data},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=t.type,a=t.data;switch(this.dialogCancel(),e){case"reload":break;case"merge":this.mergeItems(a);break}},mergeItems:function(t){this.data=this.data.concat(t)},save:function(){var t=this;if(!this.project||!this.fiterArrObj.Project_Id)return this.$message.warning("当前不存在有效项目");if(this.data.length<=0)return this.$message.warning("没有要报废的明细条目");this.isClicked=!0;var e=this.formatPostData();this.btnLoading=!0,(0,s.SaveComponentScrap)(e).then((function(e){var a;e.IsSucceed?t.tagBack():(t.isClicked=!1,t.$message.warning(null!==(a=e.Message)&&void 0!==a?a:""));t.btnLoading=!1}))},formatPostData:function(){var t,e,a,n,i={entity:{Project_Id:null!==(t=this.fiterArrObj["Project_Id"])&&void 0!==t?t:null===(e=this.project)||void 0===e?void 0:e.Id,Project_Name:null===(a=this.project)||void 0===a?void 0:a.Name,Project_Code:null===(n=this.project)||void 0===n?void 0:n.Code,Remark:this.fiterArrObj.Remark},details:[]};return i.details=this.data.map((function(t){return(0,o.default)({},t)})),i},deleteRows:function(){var t=this.checkedRows.map((function(t){return t.Component_Id}));this.data=this.data.filter((function(e){return t.indexOf(e.Component_Id)<0}))}}}},"5fefd":function(t,e,a){},"8611a":function(t,e,a){"use strict";a("2950")},a42b:function(t,e,a){"use strict";a.r(e);var n=a("5fb3"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},fb56:function(t,e,a){"use strict";a("5fefd")}}]);