(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5ace52b8"],{"1e28":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100",staticStyle:{padding:"16px"}},[a("div",{staticClass:"sch-detail"},[a("header",[a("el-button",{attrs:{circle:"",icon:"el-icon-arrow-left",size:"mini"},on:{click:t.tagBack}}),a("span",[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))]),a("el-button",{staticStyle:{"margin-left":"32px"},attrs:{type:t.Is_Ready?"success":"danger",round:"",size:"small"}},[t._v("清单"+t._s(t.Is_Ready?"已":"未")+"齐套 ")]),a("div",{staticClass:"right-fix"},[a("el-button",{attrs:{disabled:t.Is_Ready,type:"primary"},on:{click:function(e){return t.updatePrepare(!0)}}},[t._v("确认齐套 ")]),a("el-button",{attrs:{disabled:!t.Is_Ready,type:"warning"},on:{click:function(e){return t.updatePrepare(!1)}}},[t._v("取消齐套 ")])],1)],1),a("div",{staticClass:"twrap"},[a("DynamicDataTable",{ref:"table",attrs:{columns:t.columns,config:t.tbConfig,data:t.data,page:t.filterData.Page,total:t.filterData.TotalCount,border:""},on:{gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange,multiSelectedChange:t.multiSelectedChange},scopedSlots:t._u([{key:"Create_Date",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Create_Date,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])})],1)]),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},i=[]},"209b":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CheckPackCode=c,e.ExportComponentStockInInfo=f,e.ExportPackingInInfo=g,e.ExportWaitingStockIn2ndList=R,e.FinishCollect=I,e.From_Stock_Status_TYPES=void 0,e.GetComponentStockInEntity=u,e.GetLocationList=l,e.GetPackingDetailList=p,e.GetPackingEntity=y,e.GetPackingGroupByDirectDetailList=d,e.GetStockInDetailList=s,e.GetStockMoveDetailList=b,e.GetWarehouseListOfCurFactory=o,e.HandleInventoryItem=P,e.PackingTypes=e.PackingStatus=e.InventoryComponentTypes=e.InventoryCheckStatus=e.InventoryCheckExceptions=void 0,e.RemoveMain=m,e.SaveComponentScrap=k,e.SaveInventory=_,e.SavePacking=h,e.SaveStockIn=r,e.SaveStockMove=C,e.StockInTypes=void 0,e.UnzipPacking=v,e.UpdateBillReady=S,e.UpdateMaterialReady=D;var i=n(a("b775"));n(a("4328")),e.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],e.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],e.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],e.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],e.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],e.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],e.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function o(t){return(0,i.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function l(t){return(0,i.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function r(t){return(0,i.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:t})}function u(t){return(0,i.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:t}})}function s(t,e){return(0,i.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:t,isEdit:e}})}function c(t,e){return(0,i.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:t,id:e}})}function d(t){return(0,i.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function p(t){return(0,i.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:t})}function f(t){return(0,i.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:t}})}function g(t){return(0,i.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:t}})}function m(t){return(0,i.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:t}})}function h(t){return(0,i.default)({url:"/PRO/Packing/SavePacking",method:"post",data:t})}function v(t){var e=t.id,a=t.locationId;return(0,i.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:e,locationId:a}})}function y(t){var e=t.id,a=t.code;return(0,i.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:e,code:a}})}function b(t){return(0,i.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:t}})}function C(t){return(0,i.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:t})}function _(t){return(0,i.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:t})}function P(t){var e=t.id,a=t.type,n=t.value;return(0,i.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:e,type:a,value:n}})}function k(t){return(0,i.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:t})}function I(t){return(0,i.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:t}})}function S(t){var e=t.installId,a=t.isReady;return(0,i.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:e,isReady:a}})}function D(t){return(0,i.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:t})}function R(t){return(0,i.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:t})}},3242:function(t,e,a){"use strict";a("83c6")},"64cc":function(t,e,a){"use strict";a.r(e);var n=a("ab2a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"83c6":function(t,e,a){},"9dbf":function(t,e,a){"use strict";a.r(e);var n=a("1e28"),i=a("64cc");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("3242");var l=a("2877"),r=Object(l["a"])(i["default"],n["a"],n["b"],!1,null,"0cd09ecf",null);e["default"]=r.exports},ab2a:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("5530"));a("a9e3"),a("d3b7"),a("25f0");var o=n(a("0f97")),l=n(a("b775")),r=(a("2dd9"),a("209b")),u=a("ed08");e.default={name:"PrepareCheck",components:{DynamicDataTable:o.default},data:function(){return{apis:{GetBillPrepareDetail:"/PRO/OperationPlan/GetBillPrepareDetail"},fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},Is_Ready:!1,tbConfig:{},columns:[],data:[],dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},checkedRows:[],unit:{}}},created:function(){var t,e;this.unit=null!==(t=null===(e=this.$route.params)||void 0===e?void 0:e.row)&&void 0!==t?t:{},this.setGrid({Data_Url:this.apis.GetBillPrepareDetail,Height:0,Is_Auto_Width:!1,Is_Filter:!1,Width:0}),this.setCols([{Align:"center",Code:"File_Name",Digit_Number:2,Display_Name:"文件名称",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:0},{Align:"center",Code:"Count",Digit_Number:2,Display_Name:"构件数",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100},{Align:"center",Code:"NetWeight",Digit_Number:2,Display_Name:"重量",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100},{Align:"center",Code:"Volume",Digit_Number:2,Display_Name:"体积",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:100},{Align:"center",Code:"Create_Username",Digit_Number:2,Display_Name:"上传人",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:240},{Align:"center",Code:"Create_Date",Digit_Number:2,Display_Name:"上传时间",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:200}]),this.getTableData()},methods:{tagBack:function(){(0,u.closeTagView)(this.$store,this.$route)},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120}),this.filterData.PageSize=Number(this.tbConfig.Row_Number)},setCols:function(t){this.columns=t},setGridData:function(t){this.data=t.Details,this.filterData.TotalCount=t.TotalCount},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.getTableData()},gridSizeChange:function(t){var e=t.size;this.filterData.Page=1,this.filterData.PageSize=e,this.tbConfig=(0,i.default)((0,i.default)({},this.tbConfig),{},{Row_Number:Number(e)}),this.getTableData()},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},getTableData:function(){var t=this;return(0,l.default)({url:this.tbConfig.Data_Url,method:"post",params:{installId:this.unit.Id}}).then((function(e){e.IsSucceed&&(t.Is_Ready=e.Data.Is_Ready,t.setGridData(e.Data),t.filterData.TotalCount=e.Data.TotalCount)}))},multiSelectedChange:function(t){this.checkedRows=t},updatePrepare:function(t){var e=this;(0,r.UpdateBillReady)({installId:this.unit.Id,isReady:t}).then((function(a){var n,o;a.IsSucceed?(e.unit=(0,i.default)((0,i.default)({},e.unit),{},{Is_Ready:t}),e.$message.success(null!==(n=a.Message)&&void 0!==n?n:"操作成功")):e.$message.warning(null!==(o=a.Message)&&void 0!==o?o:"操作失败")}))}}}}}]);