(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-5a08eecd"],{"0d3f":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af");var o=n(a("0f97"));e.default={name:"Detail",components:{DynamicDataTable:o.default},data:function(){return{tbConfig:{},columns:[],data:[],checkedRows:[]}},created:function(){this.setGrid({Data_Url:"",Height:480,Is_Auto_Width:!1,Is_Select:!1,Is_Filter:!1,Is_Page:!1,Width:0,Is_Edit:!0}),this.setCols([{Align:"center",Code:"Code",Digit_Number:2,Display_Name:"编号",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:0},{Align:"center",Code:"Name",Digit_Number:2,Display_Name:"名称",Filter_Type:"text",Formatter:null,Is_Display:!0,Is_Filter:!0,Range:null,Type:"text",Width:0}]),this.setGridData({Data:[{},{},{}]})},methods:{cancel:function(){this.$emit("dialogCancel")},multiSelectedChange:function(t){this.checkedRows=t},setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120})},setCols:function(t){this.columns=t.concat([])},setGridData:function(t){this.data=t.Data}}}},"101e":function(t,e,a){"use strict";a("c292")},"209b":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CheckPackCode=s,e.ExportComponentStockInInfo=p,e.ExportPackingInInfo=h,e.ExportWaitingStockIn2ndList=O,e.FinishCollect=_,e.From_Stock_Status_TYPES=void 0,e.GetComponentStockInEntity=u,e.GetLocationList=r,e.GetPackingDetailList=f,e.GetPackingEntity=b,e.GetPackingGroupByDirectDetailList=d,e.GetStockInDetailList=c,e.GetStockMoveDetailList=C,e.GetWarehouseListOfCurFactory=i,e.HandleInventoryItem=P,e.PackingTypes=e.PackingStatus=e.InventoryComponentTypes=e.InventoryCheckStatus=e.InventoryCheckExceptions=void 0,e.RemoveMain=g,e.SaveComponentScrap=k,e.SaveInventory=S,e.SavePacking=v,e.SaveStockIn=l,e.SaveStockMove=y,e.StockInTypes=void 0,e.UnzipPacking=m,e.UpdateBillReady=D,e.UpdateMaterialReady=I;var o=n(a("b775"));n(a("4328")),e.StockInTypes=[{label:"生产入库",value:"生产入库"},{label:"退货入库",value:"退货入库"},{label:"退库入库",value:"退库入库"}],e.From_Stock_Status_TYPES=[{label:"生产待入库",value:0},{label:"退库待入库",value:1},{label:"退货待入库",value:2},{label:"已入库",value:3}],e.PackingTypes=[{label:"直发件包",value:"直发件包"},{label:"构件包",value:"构件包"}],e.PackingStatus=[{label:"已入库",value:"已入库"},{label:"待入库",value:"待入库"}],e.InventoryCheckStatus=[{label:"有效",value:1},{label:"采集开始",value:2},{label:"采集结束",value:3}],e.InventoryCheckExceptions=[{label:"采集前出库",value:"采集前出库",Status:"盘亏"},{label:"漏扫",value:"漏扫",Status:"盘亏"},{label:"出库时未扫",value:"出库时未扫",Status:"盘亏"},{label:"其他",value:"其他",Status:"盘亏"},{label:"待入库状态",value:"待入库状态",Status:"盘盈"},{label:"已出库状态",value:"已出库状态",Status:"盘盈"}],e.InventoryComponentTypes=[{label:"PC构件",value:"PC构件",icon:"icon-pre-concrete"},{label:"钢构构件",value:"钢构构件",icon:"icon-steel"},{label:"打包件",value:"打包件",icon:"icon-expressbox"},{label:"直发件",value:"直发件",icon:"icon-layers"}];function i(t){return(0,o.default)({url:"/PRO/Warehouse/GetWarehouseListOfCurFactory",method:"post",data:t})}function r(t){return(0,o.default)({url:"/PRO/Location/GetLocationList",method:"post",data:t})}function l(t){return(0,o.default)({url:"/PRO/ComponentStockIn/SaveStockIn",method:"post",data:t})}function u(t){return(0,o.default)({url:"/PRO/ComponentStockIn/GetComponentStockInEntity",method:"post",params:{id:t}})}function c(t,e){return(0,o.default)({url:"/PRO/ComponentStockIn/GetStockInDetailList",method:"post",params:{stockInId:t,isEdit:e}})}function s(t,e){return(0,o.default)({url:"/PRO/Packing/CheckPackCode",method:"post",params:{code:t,id:e}})}function d(t){return(0,o.default)({url:"/PRO/Packing/GetPackingGroupByDirectDetailList",method:"post",data:t})}function f(t){return(0,o.default)({url:"/PRO/Packing/GetPackingDetailList",method:"post",data:t})}function p(t){return(0,o.default)({url:"/PRO/ComponentStockIn/ExportComponentStockInInfo",method:"post",params:{id:t}})}function h(t){return(0,o.default)({url:"/PRO/Packing/ExportPackingInInfo",method:"post",params:{id:t}})}function g(t){return(0,o.default)({url:"/PRO/ComponentStockIn/RemoveMain",method:"post",params:{id:t}})}function v(t){return(0,o.default)({url:"/PRO/Packing/SavePacking",method:"post",data:t})}function m(t){var e=t.id,a=t.locationId;return(0,o.default)({url:"/PRO/Packing/UnzipPacking",method:"post",params:{id:e,locationId:a}})}function b(t){var e=t.id,a=t.code;return(0,o.default)({url:"/PRO/Packing/GetPackingEntity",method:"post",params:{id:e,code:a}})}function C(t){return(0,o.default)({url:"/PRO/ComponentStockMove/GetStockMoveDetailList",method:"post",params:{billId:t}})}function y(t){return(0,o.default)({url:"/PRO/ComponentStockMove/SaveStockMove",method:"post",data:t})}function S(t){return(0,o.default)({url:"/PRO/ComponentInventory/SaveInventory",method:"post",data:t})}function P(t){var e=t.id,a=t.type,n=t.value;return(0,o.default)({url:"/PRO/ComponentInventory/HandleInventoryItem",method:"post",params:{keyValue:e,type:a,value:n}})}function k(t){return(0,o.default)({url:"/PRO/ComponentScrap/SaveComponentScrap",method:"post",data:t})}function _(t){return(0,o.default)({url:"/PRO/ComponentInventory/FinishCollect",method:"post",params:{keyValue:t}})}function D(t){var e=t.installId,a=t.isReady;return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateBillReady",method:"post",params:{installId:e,isReady:a}})}function I(t){return(0,o.default)({url:"/PRO/ProductionPrepare/UpdateMaterialReady",method:"post",data:t})}function O(t){return(0,o.default)({url:"/PRO/componentstockin/ExportWaitingStockIn2ndList",method:"post",data:t})}},"2dd9":function(t,e,a){"use strict";function n(t,e){t||(t={}),e||(e=[]);var a=[],n=function(n){var o;o="[object Array]"===Object.prototype.toString.call(t[n])?t[n]:[t[n]];var i=o.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(i.filter((function(t){return null!==t})).length<=0&&(i=null),i){var r={Key:n,Value:i,Type:"",Filter_Type:""},l=e.find((function(t){return t.Code===n}));r.Type=null===l||void 0===l?void 0:l.Type,r.Filter_Type=null===l||void 0===l?void 0:l.Filter_Type,a.push(r)}};for(var o in t)n(o);return a}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=n,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("25f0")},3050:function(t,e,a){"use strict";a("fc72")},3326:function(t,e,a){"use strict";a("dad09")},"3ac6":function(t,e,a){"use strict";a.r(e);var n=a("0d3f"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},"4e82":function(t,e,a){"use strict";var n=a("23e7"),o=a("e330"),i=a("59ed"),r=a("7b0b"),l=a("07fa"),u=a("083a"),c=a("577e"),s=a("d039"),d=a("addb"),f=a("a640"),p=a("3f7e"),h=a("99f4"),g=a("1212"),v=a("ea83"),m=[],b=o(m.sort),C=o(m.push),y=s((function(){m.sort(void 0)})),S=s((function(){m.sort(null)})),P=f("sort"),k=!s((function(){if(g)return g<70;if(!(p&&p>3)){if(h)return!0;if(v)return v<603;var t,e,a,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)m.push({k:e+n,v:a})}for(m.sort((function(t,e){return e.v-t.v})),n=0;n<m.length;n++)e=m[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),_=y||!S||!P||!k,D=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:_},{sort:function(t){void 0!==t&&i(t);var e=r(this);if(k)return void 0===t?b(e):b(e,t);var a,n,o=[],c=l(e);for(n=0;n<c;n++)n in e&&C(o,e[n]);d(o,D(t)),a=l(o),n=0;while(n<a)e[n]=o[n++];while(n<c)u(e,n++);return e}})},5171:function(t,e,a){"use strict";a.r(e);var n=a("f241"),o=a("3ac6");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("3050"),a("101e");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"467744e2",null);e["default"]=l.exports},"59e4":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 flex-pd16-wrap"},[a("div",{staticClass:"page-main-content cs-z-shadow"},[a("el-container",{staticStyle:{height:"100%"}},[a("el-header",{staticStyle:{height:"auto","margin-bottom":"4px",display:"flex","flex-direction":"row","flex-wrap":"wrap"}},[a("div",{staticClass:"btns"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.openPageTab("PROPKCheckAdd")}}},[t._v("新增盘库单")])],1)]),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0"}},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:"",total:t.filterData.TotalCount,page:t.filterData.Page},on:{tableSearch:t.tableSearch,gridSizeChange:t.gridSizeChange,gridPageChange:t.gridPageChange},scopedSlots:t._u([{key:"Inventory_Date",fn:function(e){var a=e.row;return[t._v(" "+t._s(t._f("timeFormat")(a.Inventory_Date))+" ")]}},{key:"Status",fn:function(e){var n=e.row,o=e.column;e.$index;return[a("span",[a("span",{staticClass:"dot",style:{background:t.getStateColor(n[o.Code])}}),t._v(t._s(n[o.Code]<3?"盘点中":"盘点结束")+" ")])]}},{key:"op",fn:function(e){var n=e.row;e.column,e.$index;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openPageTab("PROPKCheckDetail",n.Id,{row:n})}}},[t._v("查看详情")]),t._v(" | "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.confirmRow(n)}}},[t._v("采集结束")])]}}])},[a("template",{slot:"hsearch_Status"},[a("el-select",{ref:"hsearch_Status",attrs:{value:"",placeholder:"请选择"},on:{change:t.statusFilter}},[a("el-option",{attrs:{label:"全部",value:"0,3"}}),a("el-option",{attrs:{label:"盘点中",value:"0,2"}}),a("el-option",{attrs:{label:"盘点结束",value:"3"}})],1)],1)],2)],1)],1)],1),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},o=[]},"69db":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("ab43"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("ddb0");var o=n(a("0f97")),i=a("6186"),r=n(a("b775")),l=a("209b"),u=n(a("2082")),c=n(a("5171")),s=a("2dd9");e.default={name:"PROInventoryCheck",components:{DynamicDataTable:o.default,Detail:c.default},mixins:[u.default],data:function(){return{addPageArray:[{path:this.$route.path+"/add",hidden:!0,component:function(){return a.e("chunk-47647057").then(a.bind(null,"3598"))},name:"PROPKCheckAdd",meta:{title:"新增盘库单"}},{path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-45e43836").then(a.bind(null,"f7ab"))},name:"PROPKCheckDetail",meta:{title:"查看盘库单详情"}}],gridCode:"pro_stock_inventory_bill_list",loading:!1,apis:{},tbConfig:{},columns:[],data:[],fiterArrObj:{},filterData:{Page:1,TotalCount:0,PageSize:0},dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"}}},beforeRouteEnter:function(t,e,a){a((function(t){t.tbConfig.Data_Url&&t.getTableData()}))},created:function(){var t=this;(0,i.GetGridByCode)({Code:this.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData()}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:160}),this.filterData.PageSize=Number(this.tbConfig.Row_Number)},setGridData:function(t){this.data=t.Data,this.filterData.TotalCount=t.TotalCount},setCols:function(t){this.columns=t},getTableData:function(){var t=this;return this.dynTblOptBak&&this.resetDynTblOpts(),this.tbConfig.Data_Url?(0,r.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData,{ParameterJson:(0,s.setParameterJson)(this.fiterArrObj,this.columns)})}).then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1})):Promise.reject("invalid data api...")},tableSearch:function(t){this.fiterArrObj=t,this.filterData.Page=1,this.getTableData()},gridSizeChange:function(t){var e=t.size;this.filterData.PageSize=e,this.filterData.Page=1,this.getTableData()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.getTableData()},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},openPageTab:function(t,e,a){this.$router.push({name:t,query:{pg_redirect:this.$route.name,id:e},params:a})},dialogCancel:function(){this.dialogShow=!1},getStateColor:function(t){var e="gray";return e=t<3?"#F1B430":"#3ECC93",e},confirmRow:function(t){var e=this;this.$confirm("确定要结束采集吗?结束后将无法继续进行本次盘库操作","提示",{confirmButtonText:"结束采集",cancelButtonText:"取消",confirmButtonClass:"my-confirm-btn",type:"warning",center:!0}).then((function(){(0,l.FinishCollect)(t.Id).then((function(a){var n,o;a.IsSucceed?(e.$message({type:"success",message:null!==(n=a.Message)&&void 0!==n?n:"操作成功"}),t.Status=3):e.$message({type:"warning",message:null!==(o=a.Message)&&void 0!==o?o:""})}))})).catch((function(){e.$message({type:"info",message:"已取消"})}))},statusFilter:function(t){this.$refs.hsearch_Status.value=t,this.fiterArrObj["Status"]=this.$refs.table.searchedField["Status"]=t.split(",").map((function(t){return Number(t)})),this.$refs.table.showSearch()}}}},8207:function(t,e,a){"use strict";a("eb32")},c292:function(t,e,a){},dad09:function(t,e,a){},eb32:function(t,e,a){},f241:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"stockin-details"},[a("DynamicDataTable",{ref:"table",attrs:{config:t.tbConfig,columns:t.columns,data:t.data,border:""},on:{multiSelectedChange:t.multiSelectedChange}},[a("template",{slot:"append"},[a("div",{staticClass:"tb-status"},[a("strong",[t._v("已选: ")]),a("span",[t._v(t._s(t.checkedRows.length))])])])],2),a("div",{staticStyle:{"text-align":"center","margin-top":"24px"}},[a("el-button",{on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.cancel}},[t._v("确定")])],1)],1)},o=[]},f9a3:function(t,e,a){"use strict";a.r(e);var n=a("69db"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},fc72:function(t,e,a){},ff68:function(t,e,a){"use strict";a.r(e);var n=a("59e4"),o=a("f9a3");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("8207"),a("3326");var r=a("2877"),l=Object(r["a"])(o["default"],n["a"],n["b"],!1,null,"2694c2c1",null);e["default"]=l.exports}}]);