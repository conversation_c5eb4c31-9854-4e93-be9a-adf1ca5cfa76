(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-4c038d99"],{"04b3":function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("ab43"),a("d3b7");var s=n(a("5530")),i=(a("f4e9"),a("ed08")),r=n(a("bbc2")),o=a("6186"),l=a("7015f"),c=a("586a");t.default={components:{OSSUpload:r.default},data:function(){return{form:{Type:"",Id:"",File_Name:""},userList:[],allowFile:".xlsx,.xls,",fileList:[],Attachments:[],btnLoading:!1,type:0}},mounted:function(){},methods:{init:function(e){this.type=e},getTemplate:function(){var e=this,t={ProfessionalCode:"Steel",Type:1};(0,c.ComponentImportTemplate)((0,s.default)({},t)).then((function(t){t.IsSucceed?window.open((0,i.combineURL)(e.$baseUrl,t.Data),"_blank"):e.$message({message:t.Message,type:"error"})}))},uploadSuccess:function(e,t,a){var n=this;this.Attachments=[],a.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl.split("?")[0]:t.File_Url=e.url,n.Attachments.push(t)})),this.form.File_Name=this.Attachments[0].File_Name},uploadExceed:function(e,t){this.$message({type:"warning",message:"已超过文件上传最大数量"})},uploadRemove:function(e,t){var a=this;this.Attachments=[],t.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl.split("?")[0]:t.File_Url=e.url,a.Attachments.push(t)})),this.form.File_Name=""},handlePreview:function(e){var t=null;t=e.hasOwnProperty("response")?e.response.encryptionUrl.split("?")[0]:e.url,this.openGetOssUrl(t)},openGetOssUrl:function(e){(0,o.GetOssUrl)({url:e,day:30}).then((function(e){window.open(e.Data)}))},submitForm:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;if(0==e.Attachments.length)return e.$message({type:"warning",message:"请上传文件清单"}),void(e.btnLoading=!1);var a={Type:1,AttachmentList:[{File_Name:e.form.File_Name,File_Url:e.Attachments[0].File_Url}]};e.form.Id?(0,l.ImportChangeDeependFile)(a).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"保存成功"}),e.$emit("close"),e.$emit("refresh")):e.$message({type:"warning",message:t.Message})})).catch(console.error).finally((function(){e.btnLoading=!1})):(0,l.ImportChangeDeependFile)(a).then((function(t){t.IsSucceed?(e.$message({type:"success",message:"保存成功"}),e.$emit("getData",t.Data),e.$emit("close"),e.$emit("refresh")):e.$message({type:"warning",message:t.Message})})).catch(console.error).finally((function(){e.btnLoading=!1}))}))},resetForm:function(){this.form.Id="",this.form.Type="",this.form.File_Name="",this.fileList=[],this.Attachments=[]}}}},4927:function(e,t,a){"use strict";var n=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(a("c14f")),i=n(a("1da1"));a("7db0"),a("d81d"),a("14d9"),a("fb6a"),a("b0c0"),a("e9f5"),a("f665"),a("7d54"),a("ab43"),a("d3b7"),a("159b");var r=n(a("0f97")),o=(a("d51a"),n(a("bbc2"))),l=a("ed08"),c=a("7015f"),d=a("1b69"),p=a("6186"),u=n(a("7610")),_=n(a("34e9")),f=n(a("641e"));t.default={name:"PROChangeManagement",components:{DynamicDataTable:r.default,OSSUpload:o.default,addFile:u.default,TopHeader:_.default},mixins:[f.default],data:function(){return{circulationList:[],loading:!1,btnLoading:!1,dialogVisible:!1,radioCode:"plm_component_page_list",isAllowCreate:!0,circulationType:0,ProfessionalType:[],tbConfig:{},columns:[],tbData:[],total:0,selectList:[],form:{Bill_No:"",Moc_Type_Id:"",Sys_Project_Id:"",CC_UserId:[],Remark:"",Instance_Id:"",Approve_Result:"",Is_Change_Model:"",Change_Reason_Ids:[],Other_Reason:"",Moc_Type_Name:"",ProjectName:"",ProjectCode:"",Handle_UserName:"",CC_UserName:"",PageInfo:{Page:1,PageSize:20}},rules:{Bill_No:[{required:!0,message:"请输入",trigger:"blur"}],Other_Reason:[{required:!0,message:"请输入",trigger:"blur"}],Moc_Type_Id:[{required:!0,message:"请选择",trigger:"change"}],Sys_Project_Id:[{required:!0,message:"请选择",trigger:"change"}],Is_Change_Model:[{required:!0,message:"请选择",trigger:"change"}],Change_Reason_Ids:[{required:!0,message:"请选择",trigger:"change"}]},fileList:[],Attachments:[],typeList:[],projectList:[],userList:[],reasonList:[],Is_Other_Reason:"",queryHistoriesList:[],changeCopyHistoryList:[],allowFile:"image/*,video/*,.txt,.pdf,.xlsx,.xls,.docx,.doc,.zip,.rar,.dwg,.nwd,.rvt,.ifc,.bzip,.bzip2",typeData:{Id:"",type:0},OrdeDetail:{},Comp_Change_Records:[],Part_Change_Records:[]}},watch:{columns:function(e){"plm_component_page_list"===this.radioCode&&e.map((function(e){"SH"!==e.Code&&"Part"!==e.Code||(e.Is_Display=!1)}))}},created:function(){var e=this;return(0,i.default)((0,s.default)().m((function t(){return(0,s.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getBasicData();case 1:e.typeData.Id=e.$route.query.data.Id,e.typeData.type=e.$route.query.data.type,0!=e.typeData.type&&e.getChangeOrdeDetail(e.typeData.Id),e.getFactoryTypeOption(e.radioCode);case 2:return t.a(2)}}),t)})))()},methods:{getBasicData:function(){var e=this;(0,c.GetChangeType)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.typeList=t.Data.Data)})),(0,d.GetProjectPageList)({PageSize:-1}).then((function(t){t.IsSucceed&&(e.projectList=t.Data.Data)})),(0,c.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data)})),(0,c.GetChangeReason)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed&&(e.reasonList=t.Data.Data,e.reasonList.map((function(e){e.Content.length>10&&(e.Content=e.Content.slice(0,10),e.Content=e.Content+"...")})))}))},getChangeOrdeDetail:function(e){var t=this;(0,c.GetChangeOrdeDetail)({id:e}).then((function(e){if(e.IsSucceed){if(t.OrdeDetail=e.Data,t.form.Bill_No=t.OrdeDetail.Bill_No,t.form.Moc_Type_Id=t.OrdeDetail.Moc_Type_Id,t.form.Sys_Project_Id=t.OrdeDetail.Sys_Project_Id,t.form.Is_Change_Model=t.OrdeDetail.Is_Change_Model,t.form.CC_UserId=t.OrdeDetail.CC_UserId.split(","),t.form.CC_UserId.pop(),t.form.Change_Reason_Ids=t.OrdeDetail.Change_Reason_Ids.split(","),t.form.Change_Reason_Ids.pop(),t.form.Remark=t.OrdeDetail.Remark,t.form.Other_Reason=t.OrdeDetail.Other_Reason,t.form.Instance_Id=t.OrdeDetail.Instance_Id,t.form.Approve_Result=t.OrdeDetail.Approve_Result,t.Comp_Change_Records=t.OrdeDetail.Comp_Change_Records.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e})),t.Part_Change_Records=t.OrdeDetail.Part_Change_Records.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e})),t.fetchData(),0!==t.OrdeDetail.AttachmentList.length&&t.OrdeDetail.AttachmentList.map((function(e){var a={File_Name:"",File_Url:""},n={name:"",url:""};a.File_Name=e.File_Name,a.File_Url=e.File_Url,n.name=e.File_Name,n.url=e.File_Url,t.Attachments.push(a),t.fileList.push(n)})),1==t.typeData.type||3==t.typeData.type){var a=t.typeList.find((function(e){return e.Id==t.form.Moc_Type_Id}));t.form.Moc_Type_Name=a.Display_Name;var n=t.projectList.find((function(e){return e.Sys_Project_Id==t.form.Sys_Project_Id}));t.form.ProjectName=n.Short_Name,t.form.ProjectCode=n.Code}4==t.typeData.type&&(t.form.CC_UserId=[]),1==t.typeData.type&&t.getQueryHistories(t.typeData.Id),t.form.Moc_Type_Id&&(0,c.GetTypeReason)({typeId:t.form.Moc_Type_Id}).then((function(e){e.IsSucceed&&(t.reasonList=e.Data,t.reasonList.map((function(e){e.Content.length>10&&(e.Content=e.Content.slice(0,10),e.Content=e.Content+"...")})),t.reasonList.unshift({Id:"其他",Content:"其他"}))})),t.form.Other_Reason&&(t.form.Change_Reason_Ids.push("其他"),t.Is_Other_Reason="其他")}else t.$message({message:e.Message,type:"error"})}))},getQueryHistories:function(e){var t=this;(0,c.QueryHistories)("FlowInstanceId=".concat(this.form.Instance_Id)).then((function(e){e.IsSucceed&&(t.queryHistoriesList=e.Data.reverse(),t.queryHistoriesList=t.queryHistoriesList.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Create_Date,e})))})),(0,c.GetChangeCopyHistoryList)({changeId:e}).then((function(e){e.IsSucceed&&(t.changeCopyHistoryList=e.Data.reverse(),t.changeCopyHistoryList=t.changeCopyHistoryList.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d} {h}:{i}:{s}"):e.Create_Date,e})))}))},getComponentData:function(e){this.handleRes(),this.Comp_Change_Records=e.Comp_Change_Records.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e})),this.Part_Change_Records=e.Part_Change_Records.map((function(e){return e.Create_Date=e.Create_Date?(0,l.parseTime)(new Date(e.Create_Date),"{y}-{m}-{d}"):e.Create_Date,e})),this.fetchData()},fetchData:function(){0===this.form.PageInfo.PageSize&&(this.form.PageInfo.PageSize=20);var e={startIndex:this.form.PageInfo.Page*this.form.PageInfo.PageSize-this.form.PageInfo.PageSize,endIndex:this.form.PageInfo.Page*this.form.PageInfo.PageSize};"plm_component_page_list"===this.radioCode?(0!==this.Comp_Change_Records.length?this.tbData=this.Comp_Change_Records.slice(e.startIndex,e.endIndex):this.tbData=this.Comp_Change_Records,this.total=this.Comp_Change_Records.length):"plm_parts_page_list"===this.radioCode&&(0!==this.Part_Change_Records.length?this.tbData=this.Part_Change_Records.slice(e.startIndex,e.endIndex):this.tbData=this.Part_Change_Records,this.total=this.Part_Change_Records.length)},projectChange:function(e){},typeChange:function(){var e=this;this.form.Change_Reason_Ids=[],this.form.Moc_Type_Id&&(0,c.GetTypeReason)({typeId:this.form.Moc_Type_Id}).then((function(t){t.IsSucceed&&(e.reasonList=t.Data,e.reasonList.map((function(e){e.Content.length>10&&(e.Content=e.Content.slice(0,10),e.Content=e.Content+"...")})),e.reasonList.unshift({Id:"其他",Content:"其他"}))}))},typeClear:function(){this.form.Change_Reason_Ids=[]},reasonChange:function(){this.Is_Other_Reason=this.form.Change_Reason_Ids.find((function(e){return"其他"==e})),"其他"!==this.Is_Other_Reason&&(this.form.Other_Reason="")},handleImp:function(){var e=this;this.dialogVisible=!0,"plm_component_page_list"===this.radioCode?this.$nextTick((function(){e.$refs.addFile.init(0)})):this.$nextTick((function(){e.$refs.addFile.init(1)}))},handleRes:function(){this.Comp_Change_Records=[],this.Part_Change_Records=[],this.tbData=[],this.total=0},handleClose:function(){this.$refs.addFile.resetForm(),this.dialogVisible=!1},radioChange:function(){this.form.PageInfo.Page=1,this.getFactoryTypeOption(this.radioCode)},radioChange2:function(){},uploadSuccess:function(e,t,a){var n=this;this.Attachments=[],a.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,n.Attachments.push(t)}))},uploadExceed:function(e,t){this.$message({type:"warning",message:"已超过文件上传最大数量"})},uploadRemove:function(e,t){var a=this;this.Attachments=[],t.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,a.Attachments.push(t)}))},handlePreview:function(e){var t=null;t=e.hasOwnProperty("response")?e.response.encryptionUrl:e.url,this.openGetOssUrl(t)},openGetOssUrl:function(e){(0,p.GetOssUrl)({url:e,day:30}).then((function(e){window.open(e.Data)}))},submit:function(e){var t=this;this.$refs.form.validate((function(a){var n=!0;if(a||(n=a),t.$refs.form2.validate((function(e){e||(n=e)})),n){t.btnLoading=!0;var s=t.form,i=s.Bill_No,r=s.Moc_Type_Id,o=s.Sys_Project_Id,d=s.CC_UserId,p=s.Change_Reason_Ids,u=s.Is_Change_Model,_=s.Remark,f=s.Instance_Id,m=s.Other_Reason,h={Bill_No:i,Moc_Type_Id:r,Sys_Project_Id:o,CC_UserId:d,Change_Reason_Ids:p,Is_Change_Model:u,Remark:_,AttachmentList:t.Attachments,Id:t.typeData.Id||"",Is_Draft:e,Web_Id:e?"":"changeFrom",Instance_Id:4==t.typeData.type?f:"",Comp_Change_Records:t.Comp_Change_Records,Part_Change_Records:t.Part_Change_Records,Other_Reason:m},y="",g="";h.CC_UserId.length>0&&h.CC_UserId.forEach((function(e){y+=e+","})),h.Change_Reason_Ids.length>0&&h.Change_Reason_Ids.forEach((function(e){"其他"!==e&&(g+=e+",")})),h.CC_UserId=y,h.Change_Reason_Ids=g,(0,c.SaveChangeOrder)(h).then((function(a){a.IsSucceed?(t.$message({message:e?"保存成功":"提交成功",type:"success"}),(0,l.closeTagView)(t.$store,t.$route)):t.$message({message:a.Message,type:"error"}),t.btnLoading=!1}))}}))},handlePrint:function(){},handleExport:function(){},handlePass:function(){},handleReturn:function(){},toBack:function(){var e=this;1==this.typeData.type?(0,l.closeTagView)(this.$store,this.$route):this.$confirm("此操作不会保存编辑数据，是否退出？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,l.closeTagView)(e.$store,e.$route)})).catch((function(){e.$message({type:"info",message:"已取消"})}))},handlePageChange2:function(e){this.form.PageInfo?this.form.PageInfo.Page=e.page:this.form.Page=e.page,this.fetchData()},handleSizeChange2:function(e){this.form.PageInfo?(this.form.PageInfo.Page=1,this.form.PageInfo.PageSize=e.size||20):(this.form.Page=1,this.form.PageSize=e.size||20),this.fetchData()},multiSelectedChange:function(e){this.selectList=e}}}},"74af":function(e,t,a){},7610:function(e,t,a){"use strict";a.r(t);var n=a("bec4"),s=a("df11");for(var i in s)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(i);a("9a79");var r=a("2877"),o=Object(r["a"])(s["default"],n["a"],n["b"],!1,null,"d2e4a938",null);t["default"]=o.exports},"852a":function(e,t,a){"use strict";a("f330")},"9609b":function(e,t,a){"use strict";a.r(t);var n=a("f3df"),s=a("cad6");for(var i in s)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return s[e]}))}(i);a("852a");var r=a("2877"),o=Object(r["a"])(s["default"],n["a"],n["b"],!1,null,"1f747a9f",null);t["default"]=o.exports},"9a79":function(e,t,a){"use strict";a("74af")},bec4:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return s}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[a("div",{staticClass:"cs-alert"},[a("i",{staticClass:"el-icon-warning-outline"}),e._v(" 注意：请先 "),a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.getTemplate()}}},[e._v("下载构零件导入模板")])],1),a("el-form-item",{attrs:{label:"文件清单名",prop:"File_Name",rules:{required:!0,message:"请输入",trigger:"blur"}}},[a("el-input",{staticStyle:{width:"360px"},attrs:{type:"text",placeholder:"请输入",clearable:""},model:{value:e.form.File_Name,callback:function(t){e.$set(e.form,"File_Name",t)},expression:"form.File_Name"}})],1),a("el-form-item",{attrs:{label:"上传附件"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:e.allowFile,"file-list":e.fileList,limit:1,"on-success":function(t,a,n){e.uploadSuccess(t,a,n)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.uploadExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])])],1),a("el-form-item",[a("div",{staticClass:"btn-footer"},[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submitForm()}}},[e._v("保存")])],1)])],1)],1)},s=[]},cad6:function(e,t,a){"use strict";a.r(t);var n=a("4927"),s=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=s.a},df11:function(e,t,a){"use strict";a.r(t);var n=a("04b3"),s=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=s.a},f330:function(e,t,a){},f3df:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return s}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100"},[a("div",{staticClass:"app-container h100"},[a("div",{staticClass:"top-btn",on:{click:e.toBack}},[a("el-button",[e._v("返回")])],1),a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-main"},[a("div",{staticClass:"basic-information"},[a("header",[e._v("基础信息")]),a("el-form",{ref:"form",staticClass:"demo-form-inline",staticStyle:{"padding-left":"20px"},attrs:{inline:!0,model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"变更单号",prop:"Bill_No"}},[a("el-input",{attrs:{type:"text",disabled:1==e.typeData.type||3==e.typeData.type,placeholder:"请输入"},on:{input:function(t){e.form.Bill_No=e.form.Bill_No.replace(/[^\w\.\/]/gi,"")}},model:{value:e.form.Bill_No,callback:function(t){e.$set(e.form,"Bill_No","string"===typeof t?t.trim():t)},expression:"form.Bill_No"}})],1),a("el-form-item",{attrs:{label:"是否有变更任务",prop:"Is_Change_Model"}},[0==e.typeData.type||2==e.typeData.type||4==e.typeData.type?a("el-select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.form.Is_Change_Model,callback:function(t){e.$set(e.form,"Is_Change_Model",t)},expression:"form.Is_Change_Model"}},[a("el-option",{attrs:{label:"是",value:!0}}),a("el-option",{attrs:{label:"否",value:!1}})],1):a("el-input",{attrs:{value:e.form.Is_Change_Model?"是":"否",type:"text",disabled:""}})],1),0==e.typeData.type||2==e.typeData.type||4==e.typeData.type?a("el-form-item",{attrs:{label:"选择抄送人",prop:"CC_UserId"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:"",disabled:1==e.typeData.type||3==e.typeData.type},model:{value:e.form.CC_UserId,callback:function(t){e.$set(e.form,"CC_UserId",t)},expression:"form.CC_UserId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"变更类型",prop:"Moc_Type_Id"}},[0==e.typeData.type||2==e.typeData.type||4==e.typeData.type?a("el-select",{attrs:{placeholder:"请选择",clearable:"",filterable:""},on:{change:function(t){return e.typeChange()},clear:function(t){return e.typeClear()}},model:{value:e.form.Moc_Type_Id,callback:function(t){e.$set(e.form,"Moc_Type_Id",t)},expression:"form.Moc_Type_Id"}},e._l(e.typeList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1):a("el-input",{attrs:{value:e.form.Moc_Type_Name,type:"text",disabled:""}})],1),1==e.typeData.type?a("el-form-item",{attrs:{label:"审批结果",prop:"Approve_Result"}},[a("el-input",{attrs:{value:e.form.Approve_Result,type:"text",disabled:""}})],1):e._e()],1)],1),a("div",{staticClass:"basic-information last-item"},[a("header",[e._v("变更业务")]),a("el-form",{ref:"form2",staticClass:"demo-form-inline",staticStyle:{"padding-left":"20px"},attrs:{inline:!0,model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"项目名称",prop:"Sys_Project_Id"}},[0==e.typeData.type||2==e.typeData.type||4==e.typeData.type?a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.projectChange},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Short_Name}})})),1):a("el-input",{attrs:{value:e.form.ProjectName,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"项目编号",prop:"Sys_Project_Id"}},[0==e.typeData.type||2==e.typeData.type||4==e.typeData.type?a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Sys_Project_Id,callback:function(t){e.$set(e.form,"Sys_Project_Id",t)},expression:"form.Sys_Project_Id"}},e._l(e.projectList,(function(e){return a("el-option",{key:e.Sys_Project_Id,attrs:{value:e.Sys_Project_Id,label:e.Code}})})),1):a("el-input",{attrs:{value:e.form.ProjectCode,type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"变更原因",prop:"Change_Reason_Ids"}},[a("el-select",{ref:"reason",attrs:{placeholder:"请选择",clearable:"",filterable:"",multiple:"",disabled:1==e.typeData.type||3==e.typeData.type||!e.form.Moc_Type_Id},on:{change:e.reasonChange},model:{value:e.form.Change_Reason_Ids,callback:function(t){e.$set(e.form,"Change_Reason_Ids",t)},expression:"form.Change_Reason_Ids"}},e._l(e.reasonList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Content}})})),1)],1),"其他"==e.Is_Other_Reason?a("el-form-item",{attrs:{label:"其他变更原因",prop:"Other_Reason"}},[a("el-input",{attrs:{type:"text",disabled:1==e.typeData.type||3==e.typeData.type||"其他"!==e.Is_Other_Reason,placeholder:"请输入",maxlength:"100"},model:{value:e.form.Other_Reason,callback:function(t){e.$set(e.form,"Other_Reason","string"===typeof t?t.trim():t)},expression:"form.Other_Reason"}})],1):e._e()],1)],1),a("div",{staticClass:"change-remark first-item"},[a("header",[e._v("备注")]),a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",rows:4,maxlength:"100",placeholder:"请输入",disabled:1==e.typeData.type||3==e.typeData.type},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1),a("div",{staticClass:"change-content"},[a("header",[e._v("变更内容")]),a("top-header",{staticStyle:{"margin-bottom":"10px"},scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header",staticStyle:{"margin-bottom":"20px"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:e.radioChange},model:{value:e.radioCode,callback:function(t){e.radioCode=t},expression:"radioCode"}},[a("el-radio-button",{attrs:{label:"plm_component_page_list"}},[e._v("构件清单")]),a("el-radio-button",{attrs:{label:"plm_parts_page_list"}},[e._v("零件清单")])],1)],1)]},proxy:!0},{key:"right",fn:function(){return[a("el-button",{attrs:{size:"mini",type:"danger",disabled:1==e.typeData.type||3==e.typeData.type},on:{click:function(t){return e.handleImp()}}},[e._v("导入清单")]),a("el-button",{attrs:{size:"mini",type:"primary",disabled:1==e.typeData.type||3==e.typeData.type},on:{click:function(t){return e.handleRes()}}},[e._v("重置")])]},proxy:!0}])}),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0 20px",height:"550px"}},[a("DynamicDataTable",{ref:"table",staticClass:"cs-plm-dy-table",attrs:{config:e.tbConfig,columns:e.columns,data:e.tbData,total:e.total,page:e.form.PageInfo.Page,stripe:"",border:""},on:{gridPageChange:e.handlePageChange2,gridSizeChange:e.handleSizeChange2,multiSelectedChange:e.multiSelectedChange},scopedSlots:e._u([{key:"Is_Component",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Is_Component?"是":"否"))])]}},{key:"Is_Main",fn:function(t){var n=t.row;return[a("div",[e._v(e._s(n.Is_Main?"是":"否"))])]}}])})],1)],1),a("div",{staticClass:"file-items"},[a("header",[e._v("资料附件")]),a("div",{staticClass:"file-up"},[0==e.typeData.type||2==e.typeData.type||4==e.typeData.type?a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:e.allowFile,"file-list":e.fileList,multiple:"",limit:10,"on-success":function(t,a,n){e.uploadSuccess(t,a,n)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview,"on-exceed":e.uploadExceed,disabled:1==e.typeData.type||3==e.typeData.type}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 文件上传数量最多为10个 ")])]):a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.fileList,border:""}},[a("el-table-column",{attrs:{prop:"name",label:"文件名",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("div",{staticStyle:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},[e._v(e._s(n.name||"-"))])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"83",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handlePreview(n)}}},[e._v("下载")])]}}])})],1)],1)]),1==e.typeData.type||3==e.typeData.type?a("div",{staticClass:"circulation-information last-item"},[a("header",[e._v("流转信息")]),a("top-header",{staticStyle:{"margin-bottom":"10px"},scopedSlots:e._u([{key:"left",fn:function(){return[a("div",{staticClass:"cs-header",staticStyle:{"margin-bottom":"20px"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:e.radioChange2},model:{value:e.circulationType,callback:function(t){e.circulationType=t},expression:"circulationType"}},[a("el-radio-button",{attrs:{label:0}},[e._v("流转意见")]),a("el-radio-button",{attrs:{label:1}},[e._v("抄送历史")])],1)],1)]},proxy:!0}],null,!1,1299808335)}),0===e.circulationType?a("div",{staticClass:"circulation-content"},[0!==e.queryHistoriesList.length?e._l(e.queryHistoriesList,(function(t,n){return a("ul",{key:n},[0==t.Verification_Status?[e._m(0,!0),a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Create_UserName))]),a("span",[e._v(e._s(t.Create_Date))])])]:[e._m(1,!0),a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Create_UserName))]),1==t.Verification_Status?a("span",{staticClass:"left-title"},[e._v(e._s("通过"))]):3==t.Verification_Status?a("span",{staticClass:"left-title"},[e._v(e._s("退回"))]):e._e(),a("span",[e._v(e._s(t.Create_Date))])]),a("li",[a("span",{staticClass:"left-title subtitle"},[e._v("处理意见")]),a("span",[e._v(e._s(t.Verification_Opinion))])])]],2)})):a("div",[e._v("暂无数据")])],2):a("div",{staticClass:"circulation-content"},[0!==e.changeCopyHistoryList.length?e._l(e.changeCopyHistoryList,(function(t,n){return a("ul",{key:n},[e._m(2,!0),a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Create_UserName))]),a("span",[e._v(e._s(t.Create_Date))])]),e._m(3,!0),a("li",[a("span",{staticClass:"left-title"},[e._v(e._s(t.Cc_UserName))]),a("span",[e._v(e._s(t.Create_Date))])])])})):a("div",[e._v("暂无数据")])],2)],1):e._e()]),a("div",{staticClass:"submit-btn"},[0==e.typeData.type||2==e.typeData.type||4==e.typeData.type?a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submit(!1)}}},[e._v("提交")]):e._e(),0==e.typeData.type||2==e.typeData.type?a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.submit(!0)}}},[e._v("保存草稿")]):e._e(),3==e.typeData.type?a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handlePass()}}},[e._v("通过")]):e._e(),3==e.typeData.type?a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:function(t){return e.handleReturn()}}},[e._v("退回")]):e._e()],1)])]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"导入清单",visible:e.dialogVisible,width:"576px"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("add-File",{ref:"addFile",on:{close:e.handleClose,getData:e.getComponentData}})],1)],1)},s=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("li",{staticClass:"top"},[a("span",{staticClass:"left-title"},[a("i"),e._v("发起人")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("li",{staticClass:"top"},[a("span",{staticClass:"left-title"},[a("i"),e._v("审核人")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("li",{staticClass:"top"},[a("span",{staticClass:"left-title"},[a("i"),e._v("发起人")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("li",{staticClass:"top"},[a("span",{staticClass:"left-title"},[a("i"),e._v("抄送人")])])}]}}]);