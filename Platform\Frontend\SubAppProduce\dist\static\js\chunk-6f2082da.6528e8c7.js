(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6f2082da"],{"01b6f":function(t,e,a){},"02c3":function(t,e,a){"use strict";a.r(e);var n=a("b7cf"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"05b4":function(t,e,a){"use strict";a("13b3a")},"0627":function(t,e,a){"use strict";a.r(e);var n=a("7d42"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"0b13":function(t,e,a){"use strict";a("b76c")},"13b3a":function(t,e,a){},"1bb25":function(t,e,a){"use strict";a("46be")},"1f72":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"req"},[0===t.mode?a("div",[a("header",[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))]),a("section",t._l(t.needs,(function(e,n){return a("div",{key:e.Id,staticClass:"item"},[a("label",[t._v(t._s(e.Component_Type_Name))]),a("div",{staticClass:"amount"},[a("span",[t._v("总量：")]),a("em",[t._v(t._s(e.Plan_Amount_All)+t._s(e.Unit))])]),a("div",{staticClass:"wx"},[a("span",[t._v("外协量：")]),a("el-input",{attrs:{placeholder:"请输入内容",size:"mini",type:"number"},model:{value:t.formData[n].Plan_Amount,callback:function(e){t.$set(t.formData[n],"Plan_Amount",e)},expression:"formData[i].Plan_Amount"}},[a("template",{slot:"append"},[t._v(t._s(e.Unit))])],2)],1)])})),0),a("footer",[a("el-button",{attrs:{size:"small"},on:{click:t.cancel}},[t._v("取消外协")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.confirmSubmit}},[t._v("确认外协 ")])],1)]):t._e(),1===t.mode?a("div",[a("div",{staticClass:"tb-head"},[a("div",{staticClass:"item"},[a("label",[t._v("已选：")]),a("em",[t._v(" "+t._s(t.selectList.length)+"/"+t._s(t.Components.length)+" ")])]),a("div",{staticClass:"item"},[a("label",[t._v("外协负责人：")]),a("el-select",{attrs:{placeholder:"请选择",size:"mini"},model:{value:t.principal,callback:function(e){t.principal=e},expression:"principal"}},t._l(t.userList,(function(t,e){return a("el-option",{key:e,attrs:{label:t.Display_Name,value:t.Id}})})),1)],1),a("div",{staticClass:"proj"},[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))])]),t.tbLoading?t._e():a("div",{staticClass:"tb-content",staticStyle:{height:"300px"}},[a("DynamicDataTable",{attrs:{columns:[{Code:"type_name",Display_Name:"构件大类",Type:"string",Is_Display:!0},{Code:"sub_type_name",Display_Name:"构件小类",Type:"string",Is_Display:!0},{Code:"code",Display_Name:"构件编号",Type:"string",Is_Display:!0},{Code:"component_count",Display_Name:"构件数量",Type:"number",Is_Display:!0}],config:{Is_Select:!0,Is_Page:!1,Pager_Align:"center",Height:0,Tree_Key:"code",Pager_Layout:"prev, pager, next"},data:t.Components},on:{multiSelectedChange:t.multiSelectedChange}})],1),a("div",{staticClass:"tb-foot"},[a("el-button",{attrs:{size:"small"},on:{click:t.cancel}},[t._v("取消外协")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.confirmSubmit}},[t._v("确认外协 ")])],1)]):t._e()])},i=[]},2831:function(t,e,a){"use strict";a("6f4f5")},"2dd9":function(t,e,a){"use strict";function n(t,e){t||(t={}),e||(e=[]);var a=[],n=function(n){var i;i="[object Array]"===Object.prototype.toString.call(t[n])?t[n]:[t[n]];var s=i.map((function(t){var e=Object.prototype.toString.call(t);return["[object Boolean]","[object Number]"].indexOf(e)>-1||t?t:null}));if(s.filter((function(t){return null!==t})).length<=0&&(s=null),s){var o={Key:n,Value:s,Type:"",Filter_Type:""},l=e.find((function(t){return t.Code===n}));o.Type=null===l||void 0===l?void 0:l.Type,o.Filter_Type=null===l||void 0===l?void 0:l.Filter_Type,a.push(o)}};for(var i in t)n(i);return a}Object.defineProperty(e,"__esModule",{value:!0}),e.setParameterJson=n,a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7"),a("25f0")},"3c88":function(t,e,a){},"44ab4":function(t,e,a){},"46be":function(t,e,a){},"477a":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.GetBillPrepareDetail=o,e.GetFactoryAlloctCount=l;var i=n(a("b775")),s=n(a("4328"));function o(t){return(0,i.default)({url:"/PRO/OperationPlan/GetBillPrepareDetail",method:"post",data:s.default.stringify(t)})}function l(t){return(0,i.default)({url:"/PRO/OperationPlan/GetFactoryAlloctCount",method:"post",data:s.default.stringify(t)})}},"47d9":function(t,e,a){"use strict";a.r(e);var n=a("9de5"),i=a("adad");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("05b4"),a("bad7");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"5a031be4",null);e["default"]=l.exports},"4e82":function(t,e,a){"use strict";var n=a("23e7"),i=a("e330"),s=a("59ed"),o=a("7b0b"),l=a("07fa"),r=a("083a"),c=a("577e"),u=a("d039"),d=a("addb"),f=a("a640"),p=a("3f7e"),m=a("99f4"),_=a("1212"),h=a("ea83"),g=[],b=i(g.sort),v=i(g.push),y=u((function(){g.sort(void 0)})),C=u((function(){g.sort(null)})),D=f("sort"),I=!u((function(){if(_)return _<70;if(!(p&&p>3)){if(m)return!0;if(h)return h<603;var t,e,a,n,i="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)g.push({k:e+n,v:a})}for(g.sort((function(t,e){return e.v-t.v})),n=0;n<g.length;n++)e=g[n].k.charAt(0),i.charAt(i.length-1)!==e&&(i+=e);return"DGBEFHACIJK"!==i}})),S=y||!C||!D||!I,A=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};n({target:"Array",proto:!0,forced:S},{sort:function(t){void 0!==t&&s(t);var e=o(this);if(I)return void 0===t?b(e):b(e,t);var a,n,i=[],c=l(e);for(n=0;n<c;n++)n in e&&v(i,e[n]);d(i,A(t)),a=l(i),n=0;while(n<a)e[n]=i[n++];while(n<c)r(e,n++);return e}})},"54b0":function(t,e,a){"use strict";a("44ab4")},5540:function(t,e,a){"use strict";a("f32d")},"6821d":function(t,e,a){"use strict";a("81222")},"6f4f5":function(t,e,a){},"7a4b":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"abs100 flex-pd16-wrap"},[a("div",{staticClass:"page-main-content cs-z-shadow"},[a("el-container",{staticStyle:{height:"100%"}},[a("el-header",{staticStyle:{height:"auto","margin-bottom":"4px","margin-top":"16px",display:"flex","flex-direction":"row","flex-wrap":"wrap"}},[a("el-form",{ref:"form",attrs:{inline:!0,"label-width":"80px",size:"mini"}},[a("el-form-item",{attrs:{label:""}},[a("el-select",{staticStyle:{width:"150px"},attrs:{clearable:"",filterable:"",placeholder:"选择项目",value:""},on:{change:function(e){return t.filterChange(1)}},model:{value:t.fiterArrObj.Project_Name,callback:function(e){t.$set(t.fiterArrObj,"Project_Name",e)},expression:"fiterArrObj.Project_Name"}},t._l(t.projs,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})})),1)],1),a("el-form-item",{attrs:{label:""}},[a("el-select",{staticStyle:{width:"120px"},attrs:{clearable:"",filterable:"",placeholder:"选择工厂",value:""},on:{change:function(e){return t.filterChange(1)}},model:{value:t.fiterArrObj.Total_Factory_Name,callback:function(e){t.$set(t.fiterArrObj,"Total_Factory_Name",e)},expression:"fiterArrObj.Total_Factory_Name"}},t._l(t.factoryies,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Name}})})),1)],1),a("el-form-item",{attrs:{label:"是否满足需求","label-width":"102px"}},[a("el-select",{staticStyle:{width:"76px"},attrs:{filterable:"",placeholder:"所有",value:""},on:{change:function(e){return t.filterChange(1)}},model:{value:t.fiterArrObj.Is_Accord_With,callback:function(e){t.$set(t.fiterArrObj,"Is_Accord_With",e)},expression:"fiterArrObj.Is_Accord_With"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"是",value:"是"}}),a("el-option",{attrs:{label:"否",value:"否"}})],1)],1),a("el-form-item",{attrs:{label:"变更时间","label-width":"76px"}},[a("el-date-picker",{attrs:{"end-placeholder":"结束日期","range-separator":"至",size:"small","start-placeholder":"开始日期",type:"daterange"},on:{change:function(e){return t.filterChange(1)}},model:{value:t.fiterArrObj.Adjust_Date,callback:function(e){t.$set(t.fiterArrObj,"Adjust_Date",e)},expression:"fiterArrObj.Adjust_Date"}})],1)],1),a("div",{staticClass:"btns"},[a("el-button",{attrs:{size:"small",type:"warning"},on:{click:t.viewFuhe}},[t._v("负荷查看 ")]),a("el-button",{attrs:{disabled:t.checkedRows.length<=0,size:"small",type:"success"},on:{click:t.exportChecks}},[t._v(t._s(0===t.checkedRows.length?"导出":"导出所选")+" ")]),a("el-button",{attrs:{size:"small",type:"success"},on:{click:function(e){t.openDialog({title:"导入",component:"CUpload",width:"480px",props:{multiple:!1,exts:["xls","xlsx"],action:t.apis.ImportFactoryFeedbackDate,tmpl:{title:"计划反馈导入模板",IDs:t.checkedRows.map((function(t){return t.Id}))}}})}}},[t._v("导入 ")]),a("el-button",{attrs:{disabled:0===t.checkedRows.length,size:"small",type:"primary"},on:{click:t.confirmPlan}},[t._v("计划确认 ")])],1)],1),a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"no-v-padding",staticStyle:{padding:"0"}},[a("DynamicDataTable",{ref:"table",attrs:{columns:t.columns,config:t.tbConfig,data:t.data,page:t.filterData.Page,total:t.filterData.TotalCount,border:""},on:{cellEditorChanged:t.cellEditorChanged,columnSearchChange:t.columnSearchChange,gridPageChange:t.gridPageChange,gridSizeChange:t.gridSizeChange,multiSelectedChange:t.multiSelectedChange,tableSearch:t.tableSearch},scopedSlots:t._u([{key:"hsearch_Code",fn:function(e){var n=e.column;return[a("el-input",{key:n.Code,attrs:{placeholder:"搜索"},on:{focus:t.$refs.table.showSearch},model:{value:t.fiterArrObj.Code,callback:function(e){t.$set(t.fiterArrObj,"Code",e)},expression:"fiterArrObj.Code"}})]}},{key:"op",fn:function(e){var n=e.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.openPageTab(n.Id)}}},[t._v("详情 ")])]}},{key:"innertip-Plan_Date",fn:function(e){var n=e.row;return[n.Exception_Adjust?a("el-popover",{attrs:{placement:"bottom","popper-class":"dark-popover",trigger:"click"},on:{show:function(e){return t.getAdjustInfo(n)}}},[a("div",{staticClass:"tip-content"},[a("div",{staticClass:"row"},[a("label",[t._v("调整前: ")]),a("div",[t._v(" "+t._s(t.formatDate(t.planAdjustInfo.plan_begin_date,"yyyy-M-d"))+" ~ "+t._s(t.formatDate(t.planAdjustInfo.plan_end_date,"yyyy-M-d"))+" ")])]),a("div",{staticClass:"row"},[a("label",[t._v("调整后: ")]),a("div",[t._v(" "+t._s(t.formatDate(t.planAdjustInfo.late_begin_date,"yyyy-M-d"))+" ~ "+t._s(t.formatDate(t.planAdjustInfo.late_end_date,"yyyy-M-d"))+" ")])]),a("div",{staticClass:"row"},[a("label",[t._v("调整原因: ")]),a("span",{staticClass:"cs-p"},[t._v(t._s(t.planAdjustInfo.adjust_reason))])]),a("div",{staticClass:"row"},[a("label",[t._v("操作记录: ")]),a("span",{staticClass:"cs-p"},[a("span",[t._v(t._s(t.planAdjustInfo.adjust_username))]),t._v(" | "+t._s(t.formatDate(t.planAdjustInfo.adjust_date,"yyyy-M-d"))+" ")])])]),a("el-link",{staticStyle:{"margin-left":"2px"},attrs:{slot:"reference",underline:!1,type:"warning"},slot:"reference"},[a("i",{staticClass:"el-icon-warning"})])],1):t._e()]}},t._l(["Demand_Content","Complete_Content","External_Details"],(function(e){return{key:"innertip-"+e,fn:function(n){var i=n.row,s=n.$index,o=n.column;return[i[o.Code]?a("el-popover",{key:e+"_"+s,attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.getPlanDetails(i,o)}}},[a("div",{staticClass:"tooltip-content"},[a("el-table",{staticStyle:{width:"100%"},attrs:{"cell-style":{border:"none"},data:t.tipDetails,"header-cell-style":{backgroundColor:"#14234E",color:"#FFF"}}},[a("el-table-column",{attrs:{label:"类别",prop:"component_type_name",width:"60"}}),a("el-table-column",{attrs:{label:"需求量",prop:"total",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row[e.column.property])+t._s(e.row["unit"])+" ")]}}],null,!0)}),a("el-table-column",{attrs:{label:"已完成",prop:"complete_amount",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row[e.column.property])+t._s(e.row["unit"])+" ")]}}],null,!0)}),a("el-table-column",{attrs:{label:"完成率",prop:"percent"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("el-progress",{staticStyle:{"text-direction":"rtl"},attrs:{percentage:t.row[t.column.property]}})]}}],null,!0)})],1)],1),a("el-link",{staticStyle:{"margin-left":"2px"},attrs:{slot:"reference",underline:!1,type:"primary"},slot:"reference"},[a("i",{staticClass:"el-icon-chat-line-square"})])],1):t._e()]}}}))],null,!0)})],1)],1)],1),a("el-drawer",{staticClass:"cus-drawer",staticStyle:{position:"absolute"},attrs:{visible:t.drawerShow,"destroy-on-close":"",direction:"btt",size:"300px"},on:{"update:visible":function(e){t.drawerShow=e}}},[a("template",{slot:"title"},[a("el-menu",{staticClass:"el-menu-demo",attrs:{"default-active":t.activedDrawerTab,mode:"horizontal"},on:{select:t.menuSelect}},[a("el-menu-item",{attrs:{index:""}},[t._v("全部负荷查看")]),t._l(t.factoryies,(function(e){return a("el-menu-item",{key:e.Id,attrs:{index:e.Id}},[t._v(t._s(e.Name)+" ")])}))],2)],1),a("el-container",{staticClass:"drawer-content"},[a("el-main",[a("div",{staticClass:"scrolled"},[a("div",{staticClass:"sider"},[a("ul",[a("li",[t._v("目标产能")]),a("li",[t._v("平衡前产能")]),a("li",[t._v("平衡后产能")]),a("li",[t._v("上月剩余")]),a("li",[a("strong",[t._v("总剩余")])])])]),a("div",{staticClass:"scroll-inner",staticStyle:{"margin-left":"16px"}},t._l(t.drawerData,(function(e){return a("div",{key:e.Component_Type_Id,staticClass:"tb-wrapper"},[a("div",{staticClass:"p-tb"},[a("div",{staticClass:"r thd"},[t._v(" "+t._s(e.Component_Type_Name)+" "),a("span",{staticClass:"ut"},[t._v("(t)")])]),a("div",{staticClass:"r ths"},t._l(e.Children,(function(e,n){return a("div",{key:n,staticClass:"th"},[t._v(" "+t._s(e.Month)+"月 ")])})),0),t._l(["Production_Capacity","Plan_Load","Act_load","History_Surplus","Surplus_Load"],(function(n){return a("div",{key:n,staticClass:"r trs"},t._l(e.Children,(function(e,i){return a("div",{key:i,staticClass:"td",style:[{color:"Surplus_Load"===n&&"#3ECC93"}]},[a("span",{style:{color:"Surplus_Load"===n&&(Number(e[n])>=0?"#3ECC93":"#FB6B7F")}},[t._v(t._s(Number(e[n]).toFixed(2)))])])})),0)}))],2)])})),0)])])],1)],2),a("el-dialog",{attrs:{title:t.dialogCfgs.title,visible:t.dialogShow,width:t.dialogCfgs.width,"destroy-on-close":""},on:{"update:visible":function(e){t.dialogShow=e}}},[a("keep-alive",[t.dialogShow?a(t.dialogCfgs.component,t._b({tag:"component",on:{dialogCancel:t.dialogCancel,dialogFormSubmitSuccess:t.dialogFormSubmitSuccess}},"component",t.dialogCfgs.props,!1)):t._e()],1)],1)],1)},i=[]},"7d42":function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("d81d"),a("e9f5"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var i=n(a("b775")),s=n(a("0f97")),o=n(a("4328")),l=a("6186");e.default={name:"CWaiXieReq",components:{DynamicDataTable:s.default},props:{mode:{type:Number,default:0},unit:{type:Object,default:function(){return{}}},factoryies:{type:Array,default:function(){return[]}}},data:function(){return{apis:{SetFactoryAlloct:"/PRO/OperationPlan/SetFactoryAlloct",SaveFactoryAlloct:"/PRO/OperationPlan/SaveFactoryAlloct",CancelFactoryAlloct:"/PRO/OperationPlan/CancelFactoryAlloct"},principal:"",person_type_code:"f51fea39-a837-45e8-a642-e8744b82207a",userList:[],selectList:[],checkedRows:[],tbLoading:!1,needs:[],Components:[],formData:null}},created:function(){this.getAlloctData(),this.getUserList()},methods:{multiSelectedChange:function(t){this.selectList=t},getUserList:function(){var t=this;(0,l.GetUserList)({departmentId:this.person_type_code}).then((function(e){e.IsSucceed?t.userList=e.Data:t.$message({message:e.Message,type:"error"})}))},cancel:function(){var t=this;this.cancelFactoryAlloct().then((function(e){e.IsSucceed?t.$message.success(e.Message):t.$message.warning(e.Message)})).finally((function(){t.$emit("dialogCancel")}))},cancelFactoryAlloct:function(){var t;return(0,i.default)({url:this.apis.CancelFactoryAlloct,method:"post",paramsSerializer:function(t){return o.default.stringify(t,{arrayFormat:"repeat"})},params:{installUnitId:this.unit.Id,compIds:null===(t=this.unit.Components)||void 0===t?void 0:t.map((function(t){return t.Id}))}})},confirmSubmit:function(){var t=this,e={InstallUnit_Id:this.unit.Id,Factory_Id:this.unit.Factory_Id};0===this.mode?(e.Allot_Detail=this.formData,e["Factory_Id"]=""):(e.comps=this.selectList.map((function(t){return t.code})),e.PIC_UserId=this.principal),e.Allot_Factory_Type=1,(0,i.default)({url:this.apis.SaveFactoryAlloct,method:"post",data:e}).then((function(e){if(!e.IsSucceed)return t.$message.warning(e.Message)})).finally((function(){t.$emit("dialogCancel"),t.$emit("dialogFormSubmitSuccess",{type:"reload"})}))},getExternalAmount:function(t,e){var a;if(t&&t.length)return null===(a=t.find((function(t){return t.Component_Type_Id===e})))||void 0===a?void 0:a.Plan_Amount},getAlloctData:function(){var t=this;this.tbLoading=!0,(0,i.default)({url:this.apis.SetFactoryAlloct,method:"post",params:{installUntilId:this.unit.Id,factoryType:1}}).then((function(e){if(!e.IsSucceed)return t.$message.warning(e.Message);t.needs=e.Data.Needs,t.Components=e.Data.Components,0===t.mode?t.formData=e.Data.Needs.map((function(a){var n=t.getExternalAmount(e.Data.Factory_Allot_Detail,a.Component_Type_Id);return t.$set(a,"Plan_Amount_All",a.Plan_Amount||0),t.$set(a,"Plan_Amount",n||0),Object.assign({},a)})):t.formData=t.checkedRows.map((function(t){return t.Id})),t.tbLoading=!1}))}}}},81222:function(t,e,a){},"828c":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.tbLoading,expression:"tbLoading "}],staticClass:"req"},[0===t.mode?[a("section",[a("el-row",{staticClass:"sec-row",attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("label",[t._v("现工厂：")])]),a("el-col",{attrs:{span:7}},[a("div",{staticClass:"grid-content bg-purple"},[t._v(" "+t._s(t.Factory_Name)+" ")])]),a("el-col",{attrs:{span:5}},[a("label",[t._v("调整到：")])]),a("el-col",{attrs:{span:7}},[a("el-select",{attrs:{value:t.unit.Factory_Id,placeholder:"请选择"},model:{value:t.Factory_Id,callback:function(e){t.Factory_Id=e},expression:"Factory_Id"}},[t._l(t.factoryies,(function(t){return[a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})]}))],2)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("label",[t._v("调整说明：")])]),a("el-col",{attrs:{span:19}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入内容"},model:{value:t.Adjust_Content,callback:function(e){t.Adjust_Content=e},expression:"Adjust_Content"}})],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("label",[t._v("调整详情：")])]),a("el-col",{attrs:{span:19}},t._l(t.needs,(function(e,n){return a("div",{key:n,staticClass:"item"},[a("label",[t._v(t._s(e.Component_Type_Name))]),a("div",{staticClass:"amount"},[a("span",[t._v("总量：")]),a("em",[t._v(t._s(e.Plan_Amount_All)+t._s(e.Unit))])]),a("div",{staticClass:"wx"},[a("span",[t._v("外协量：")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:"请输入内容"},model:{value:t.formData[n].Plan_Amount,callback:function(e){t.$set(t.formData[n],"Plan_Amount",e)},expression:"formData[i].Plan_Amount"}},[a("template",{slot:"append"},[t._v(t._s(e.Unit))])],2)],1)])})),0)],1)],1),a("footer",[a("el-button",{attrs:{size:"small"},on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.confirmSubmit}},[t._v("确认调整")])],1)]:t._e(),1===t.mode?[a("header",[t._v(" 成都自然博物馆 / C馆地上二节柱 ")]),a("section",[a("el-table",{attrs:{data:t.factoryObj.list,"cell-style":{border:"none"},"header-cell-style":{border:"none",background:"#FFF"}}},[a("el-table-column",{attrs:{prop:"name",label:""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("strong",[t._v(t._s(e.row.name))])]}}],null,!1,3978836702)}),t._l(t.factoryObj.config,(function(e,n){return a("el-table-column",{key:n,attrs:{label:e},scopedSlots:t._u([{key:"default",fn:function(n){return[a("strong",[t._v(t._s(n.row[e]))])]}}],null,!0)})}))],2)],1),a("footer",[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.cancel}},[t._v("关闭")])],1)]:t._e(),2===t.mode?[a("el-alert",{staticStyle:{border:"1px solid #f1b430",color:"rgba(34, 40, 52, 0.65)","margin-bottom":"12px"},attrs:{type:"warning",closable:!1}},[a("div",{staticClass:"tb-head"},[a("div",{staticClass:"item"},[a("label",[t._v("现工厂：")]),a("strong",[t._v(" "+t._s(t.Factory_Name)+" ")])]),a("div",{staticClass:"item"},[a("label",[t._v("调整到：")]),a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:t.Factory_Id,callback:function(e){t.Factory_Id=e},expression:"Factory_Id"}},t._l(t.factoryies,(function(t){return a("el-option",{key:t.Id,attrs:{label:t.Name,value:t.Id}})})),1)],1),a("div",{staticClass:"item"},[a("label",[t._v("调整说明：")]),a("el-input",{staticStyle:{width:"360px"},attrs:{placeholder:"请输入内容"},model:{value:t.Adjust_Content,callback:function(e){t.Adjust_Content=e},expression:"Adjust_Content"}})],1)])]),a("div",{staticClass:"tb-head"},[a("div",{staticClass:"item"},[a("label",[t._v("已选：")]),a("em",[t._v(" "+t._s(t.selectList.length)+"/"+t._s(t.Components.length)+" ")])]),t._m(0)]),t.tbLoading?t._e():a("div",{staticClass:"tb-content",staticStyle:{height:"300px"}},[a("DynamicDataTable",{attrs:{columns:[{Code:"type_name",Display_Name:"构件大类",Type:"string",Is_Display:!0},{Code:"sub_type_name",Display_Name:"构件小类",Type:"string",Is_Display:!0},{Code:"code",Display_Name:"构件编号",Type:"string",Is_Display:!0},{Code:"component_count",Display_Name:"构件数量",Type:"number",Is_Display:!0}],config:{Is_Select:!0,Is_Page:!1,Pager_Align:"center",Height:0,Tree_Key:"code",Pager_Layout:"prev, pager, next"},data:t.Components},on:{multiSelectedChange:t.multiSelectedChange}})],1),a("div",{staticClass:"tb-foot"},[a("el-button",{attrs:{size:"small"},on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.confirmSubmit}},[t._v("确定")])],1)]:t._e()],2)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"summary"},[a("div",{staticClass:"item"})])}]},8742:function(t,e,a){"use strict";a("d2b8")},"9de5":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"req"},[0===t.mode?a("div",[a("header",[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))]),a("section",t._l(t.needs,(function(e,n){return a("div",{key:e.Id,staticClass:"item"},[a("label",[t._v(t._s(e.Component_Type_Name))]),a("div",{staticClass:"amount"},[a("span",[t._v("总量：")]),a("em",[t._v(t._s(e.Plan_Amount)+t._s(e.Unit))])]),a("div",{staticClass:"wx"},[a("span",[t._v("外协量：")]),a("em",[t._v(t._s(t.getExternalAmount(e.Component_Type_Id)||0)+t._s(e.Unit))])])])})),0),a("footer",[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.$emit("dialogCancel")}}},[t._v("关闭")])],1)]):t._e(),1===t.mode?a("div",[a("div",{staticClass:"tb-head"},[a("div",{staticClass:"item"},[a("label",[t._v("外协负责人：")]),a("em",[t._v(" "+t._s(t.PIC_UserName)+" ")])]),a("div",{staticClass:"proj"},[t._v(t._s(t.unit.Project_Name)+" / "+t._s(t.unit.Name))])]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"tb-content",staticStyle:{height:"360px"}},[a("DynamicDataTable",{attrs:{columns:[{Code:"type_name",Display_Name:"构件大类",Type:"string",Is_Display:!0},{Code:"sub_type_name",Display_Name:"构件小类",Type:"string",Is_Display:!0},{Code:"code",Display_Name:"构件编号",Type:"string",Is_Display:!0},{Code:"component_count",Display_Name:"构件数量",Type:"number",Is_Display:!0}],config:{Pager_Align:"center",Height:0,Tree_Key:"code"},data:t.Components}})],1),a("div",{staticClass:"tb-foot"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.$emit("dialogCancel")}}},[t._v("关闭")])],1)]):t._e()])},i=[]},a556:function(t,e,a){"use strict";a.r(e);var n=a("1f72"),i=a("0627");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("ef72"),a("0b13");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"5db3cb1e",null);e["default"]=l.exports},adad:function(t,e,a){"use strict";a.r(e);var n=a("c730"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},b76c:function(t,e,a){},b7cf:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af"),a("4de4"),a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("7d54"),a("ab43"),a("a9e3"),a("d3b7"),a("25f0"),a("159b");var i=n(a("b775")),s=n(a("4360")),o=a("5f87"),l=a("ed08");e.default={name:"CUpload",props:{action:{type:String,default:""},exts:{type:Array,default:function(){return[]}},tmpl:{type:Object,default:function(){return{}}},multiple:{type:Boolean,default:!1},filesize:{type:Number,default:5}},data:function(){return{apis:{ExportInstallUnitPlan:"/PRO/OperationPlan/ExportInstallUnitPlan"},templateUrl:"",fileList:[],progresses:{},reqHeader:null}},created:function(){this.reqHeader={},s.default.getters.token&&(this.reqHeader["Authorization"]=(0,o.getToken)()),s.default.getters.Last_Working_Object_Id&&(this.reqHeader.Last_Working_Object_Id=s.default.getters.Last_Working_Object_Id),this.tmpl.templateUrl&&(this.templateUrl=this.tmpl.templateUrl)},methods:{uploadRequest:function(t){var e=new FormData;return e.append("files",t.file),(0,i.default)({url:this.action,processData:!1,contentType:!1,method:"post",data:e,onUploadProgress:this.uploadProgressChange})},cancel:function(){this.$refs.upload.clearFiles(),this.fileList=[],this.$emit("dialogCancel")},downTmpl:function(){var t=this;(0,i.default)({url:this.apis.ExportInstallUnitPlan,method:"post",data:this.tmpl.IDs}).then((function(e){if(e.IsSucceed&&e.Data){var a=(0,l.combineURL)(t.$baseUrl,e.Data.split("/").map((function(t){return encodeURIComponent(t.toString())})).join("/"));window.open(a,"_blank")}else t.$message.error("导出文件失败!")}))},beforeUpload:function(t){var e=t.name.split(".").pop(),a=!0;this.exts.length>0&&(a=this.exts.indexOf(e)>-1);var n=!0;return n=t.size/1024/1024<this.filesize,a||this.$message.error("上传文件格式错误!"),n||this.$message.error("上传文件大小不能超过 "+this.filesize+"MB!"),a&&n},uploadProgressChange:function(t,e,a){var n=this;this.progresses[e.name]=t.percent,100===t.percent&&setTimeout((function(){n.progresses[e.name]=0,n.progresses=Object.assign({},n.progresses)}),600)},uploadStatusChange:function(t,e){"ready"===t.status?this.fileList.push(t):"fail"===t.status&&(this.fileList=this.fileList.concat([]));var a=!0;this.fileList.forEach((function(t){"success"!==t.status&&(a=!1)})),a&&(this.$emit("dialogFormSubmitSuccess",{type:"reload",data:null}),this.cancel())},uploadError:function(t,e,a){},uploadSuccess:function(t,e,a){t.IsSucceed?this.$message({message:"导入成功",type:"success"}):(e.status="fail",this.$message.error(t.Message))},reUpload:function(t){t.status="ready",this.$refs.upload.submit()},removeFile:function(t){this.fileList=this.fileList.filter((function(e){return e.name!==t.name}))},beginUpload:function(){this.fileList=this.fileList.map((function(t){return t.status="ready",t})),this.$refs.upload.submit()},extIcon:function(t){var e="document_unknown_icon";switch(t.name.split(".").pop()){case"xls":case"xlsx":e="document_form_icon";break;case"txt":e="document_txt_icon";break;case"doc":case"docx":e="document_word_icon";break;case"zip":case"7z":case"rar":e="document_zip_icon";break;case"png":case"jpg":case"jpeg":case"gif":case"bmp":e="multimedia_image_icon";break;case"ppt":case"pptx":e="document_ppt_icon";break;case"pdf":e="document_pdf_icon";break}return e}}}},b7f0:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return i}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"c-upload"},[a("div",{staticStyle:{background:"#F7F8F9",padding:"20px",border:"1px solid #D9DBE2"}},[a("div",{staticStyle:{"margin-bottom":"16px"}},[t._v(" 编辑工厂反馈时间字段后，重新上传。注意，其他字段禁止修改 ")]),a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:"",action:t.action,headers:t.reqHeader,multiple:t.multiple,accept:t.exts.map((function(t){return"."+t})).join(","),"file-list":t.fileList,"before-upload":t.beforeUpload,"on-progress":t.uploadProgressChange,"on-change":t.uploadStatusChange,"on-error":t.uploadError,"on-success":t.uploadSuccess,"auto-upload":!1,name:"files"},scopedSlots:t._u([{key:"file",fn:function(e){var n=e.file;return a("div",{},[a("div",{class:{"up-item":!0,error:"fail"===n.status}},[t.progresses[n.name]>0?a("div",{staticClass:"percent",style:{width:t.progresses[n.name]+"%"}}):t._e(),a("div",{staticClass:"bar"},[a("div",{staticClass:"title"},[a("svg-icon",{staticStyle:{height:"30px",width:"30px"},attrs:{name:t.extIcon(n),"icon-class":t.extIcon(n)}}),t._v(" "+t._s(n.name)+" ")],1),a("div",{staticClass:"remove"},["fail"===n.status?a("i",{staticClass:"el-icon-refresh-right",attrs:{title:"重新上传"},on:{click:function(e){return t.reUpload(n)}}}):t._e(),a("i",{staticClass:"el-icon-close",attrs:{title:"移除文件"},on:{click:function(e){return t.removeFile(n)}}})])])])])}}])},[a("svg-icon",{staticClass:"icon-svg",attrs:{name:"upload-icon","icon-class":"upload-icon",width:"200",height:"200"}}),a("div",{staticClass:"el-upload__text"},[t._v(" 将文件拖到此处，或"),a("em",[t._v("点击选择")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(" 支持格式："+t._s(t.exts.join("、"))+"，最大文件限制："+t._s(t.filesize)+"M ")])])],1)],1),a("div",{staticStyle:{"text-align":"right","margin-top":"20px"}},[a("el-button",{attrs:{size:"mini"},on:{click:t.cancel}},[t._v("取消")]),a("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.beginUpload}},[t._v("导入")])],1)])},i=[]},ba0b:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("7d54"),a("ab43"),a("e9c4"),a("a9e3"),a("d3b7"),a("25f0"),a("3ca3"),a("159b"),a("ddb0");var i=n(a("0f97")),s=n(a("c222")),o=n(a("a556")),l=n(a("47d9")),r=n(a("bffa")),c=n(a("2082")),u=a("6186"),d=a("1b69"),f=n(a("b775")),p=n(a("4328")),m=a("6f23"),_=a("2dd9"),h=a("ed08"),g=window.open;e.default={name:"PROSchedulesMajor",components:{DynamicDataTable:i.default,CUpload:s.default,CWaiXieReq:o.default,CWaiXieList:l.default,CFactoryChange:r.default},mixins:[c.default],data:function(){return{apis:{ImportFactoryFeedbackDate:(0,h.combineURL)(this.$baseUrl,"/PRO/OperationPlan/ImportFactoryFeedbackDate"),ConfirmInstallUnitPlan:"/PRO/OperationPlan/ConfirmInstallUnitPlan",UpdateBalanceDate:"/PRO/OperationPlan/UpdateBalanceDate",GetAdjustPlanException:"/PRO/OperationPlan/GetAdjustPlanException",GetCompletePercent:"/PRO/InstallUnit/GetCompletePercent",ExportInstallUnitPlan:"/PRO/OperationPlan/ExportInstallUnitPlan",GetCalculateProLoad:"/PRO/OperationPlan/GetCalculateProLoad"},loading:!0,gridCode:"Plan_List",tbConfig:{},columns:[],data:[],drawerShow:!1,activedDrawerTab:"",drawerData:{},dialogShow:!1,dialogCfgs:{component:"",title:"",width:"360px"},checkedRows:[],addPageArray:[{path:this.$route.path+"/detail",hidden:!0,component:function(){return a.e("chunk-3410f962").then(a.bind(null,"b8e7"))},name:"ScheduleDetail",meta:{title:"详情"}}],projs:[],factoryies:[],fiterArrObj:{Project_Name:"",Total_Factory_Name:"",Is_Accord_With:"",Adjust_Date:"",Code:""},filterData:{Page:1,TotalCount:0,PageSize:0},planAdjustInfo:{},tipDetails:[]}},created:function(){var t=this;Promise.all([(0,d.GetProjectList)({}).then((function(e){t.projs=null===e||void 0===e?void 0:e.Data})),(0,d.GetFactoryList)({}).then((function(e){t.factoryies=null===e||void 0===e?void 0:e.Data}))]).then((function(){(0,u.GetGridByCode)({Code:t.gridCode}).then((function(e){e.IsSucceed&&(t.setGrid(e.Data.Grid),t.setCols(e.Data.ColumnList))})).then((function(){t.getTableData().then((function(e){if(e.IsSucceed)return t.setGridData(e.Data)})).catch(console.error).finally((function(){t.loading=!1}))}))}))},methods:{setGrid:function(t){this.tbConfig=Object.assign({},t,{Pager_Align:"center",Op_Width:120}),this.filterData.PageSize=this.tbConfig.Row_Number},setCols:function(t){var e=this;t.forEach((function(t){"Adjust_Date"===t.Code&&(t.Type="date",t.Formatter="yyyy-M-d"),"Plan_Date"!==t.Code&&"Feedback_Date1"!==t.Code&&"Ultimate_Demand_Date"!==t.Code||(t.Type="daterange",t.Formatter="yyyy-M-d"),"Project_Name"===t.Code&&(t.Range=JSON.stringify(e.projs.map((function(t){return{label:t.Name,value:t.Name}})))),"Factory_Name"===t.Code&&(t.Range=JSON.stringify(e.factoryies.map((function(t){return{label:t.Name,value:t.Name}}))))})),this.columns=t},setGridData:function(t){this.filterData.TotalCount=t.TotalCount,this.data=t.Data},getTableData:function(){return this.tbConfig.Data_Url?(0,f.default)({url:this.tbConfig.Data_Url,method:"post",data:Object.assign({},this.filterData)}):Promise.reject("invalid data api...")},filterChange:function(t){var e=this;this.filterData=Object.assign({},this.filterData,{Page:Number(t||1)}),this.filterData.ParameterJson=(0,_.setParameterJson)(this.fiterArrObj,this.columns),this.loading=!0,this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)})).finally((function(){e.loading=!1}))},cellEditorChanged:function(t){var e=t.index,a=t.row,n=t.key,i=t.value;"Balance_Date"===n&&this.updateBalanceDate(a.Id,i,e)},updateBalanceDate:function(t,e,a){var n=this;(0,f.default)({url:this.apis.UpdateBalanceDate,method:"post",params:{installId:t,date:e.join("~")}}).then((function(t){t.IsSucceed||n.$message.warning("平衡时间更新失败"),n.$refs.table.closeEditRow(a)}))},viewFuhe:function(){var t=this;this.loadFuhe(this.activedDrawerTab).finally((function(){t.drawerShow=!0}))},loadFuhe:function(t){var e=this;return(0,f.default)({url:this.apis.GetCalculateProLoad,method:"post",params:{factoryId:t}}).then((function(t){t.IsSucceed?e.setDrawerData(t.Data):e.drawerData={}}))},menuSelect:function(t,e){this.activedDrawerTab=t,this.loadFuhe(t)},setDrawerData:function(t){var e={};t.forEach((function(t){var a=e[t.Component_Type_Id];a||(a=e[t.Component_Type_Id]={}),a["Component_Type_Name"]=t.Component_Type_Name,a["Component_Type_Id"]=t.Component_Type_Id;var n=a.Children;n||(n=a.Children={}),n[t.Month]=Object.assign({},t)})),this.drawerData=e},openDialog:function(t){t&&"[object Object]"===Object.prototype.toString.call(t)||(t={}),this.dialogCfgs=Object.assign({},this.dialogCfgs,t,{}),this.dialogShow=!0},dialogCancel:function(){this.dialogShow=!1},dialogFormSubmitSuccess:function(t){var e=this,a=t.type;t.data;switch(a){case"reload":this.getTableData().then((function(t){if(t.IsSucceed)return e.setGridData(t.Data)}));break}},openPageTab:function(t){this.$router.push({name:"ScheduleDetail",query:{id:t,pg_redirect:this.$route.name}})},multiSelectedChange:function(t){this.checkedRows=t},exportChecks:function(){var t=this;(0,f.default)({url:this.apis.ExportInstallUnitPlan,method:"post",data:this.checkedRows.map((function(t){return t.Id}))}).then((function(e){if(e.IsSucceed&&e.Data){var a=(0,h.combineURL)(t.$baseUrl,e.Data.split("/").map((function(t){return encodeURIComponent(t.toString())})).join("/"));g(a)}else t.$message.error("导出文件失败!")}))},columnSearchChange:function(t){t.column,t.value},tableSearch:function(t){this.fiterArrObj=Object.assign({},this.fiterArrObj,t),this.filterChange()},gridPageChange:function(t){var e=t.page;this.filterData.Page=e,this.filterChange(e)},gridSizeChange:function(t){var e=t.size;this.tbConfig.Row_Number=e,this.filterData.Page=1,this.filterData.PageSize=e,this.filterChange()},confirmPlan:function(){var t=this;if(this.checkedRows.length<=0)return this.$message.warning("先选择要确认的行");(0,f.default)({url:this.apis.ConfirmInstallUnitPlan,method:"post",paramsSerializer:function(t){return p.default.stringify(t,{arrayFormat:"repeat"})},params:{installIds:this.checkedRows.map((function(t){return t.Id}))}}).then((function(e){e.IsSucceed?(t.filterChange(t.filterData.Page),t.$message.success("确认完成")):t.$message.warning(String(e.Message||e.IsSucceed))}))},getAdjustInfo:function(t){var e=this;this.planAdjustInfo={},(0,f.default)({url:this.apis.GetAdjustPlanException,method:"post",params:{installunitId:t.Id}}).then((function(t){var a;t.IsSucceed?e.planAdjustInfo=null!==(a=t.Data[0])&&void 0!==a?a:{}:e.$message.warning(String(t.Message||t.IsSucceed))}))},getPlanDetails:function(t,e){var a=this,n=2;"Demand_Content"===e.Code&&(n=2),"External_Details"===e.Code&&(n=1),(0,f.default)({url:this.apis.GetCompletePercent,method:"post",params:{installunitId:t.Id,type:n}}).then((function(t){t.IsSucceed?a.tipDetails=t.Data:a.$message.warning(String(t.Message||t.IsSucceed))}))},formatDate:function(t){return(0,m.formatDate)(t)}}}},bad7:function(t,e,a){"use strict";a("3c88")},bffa:function(t,e,a){"use strict";a.r(e);var n=a("828c"),i=a("eefe");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("2831"),a("5540");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"4e78de65",null);e["default"]=l.exports},c222:function(t,e,a){"use strict";a.r(e);var n=a("b7f0"),i=a("02c3");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("6821d"),a("1bb25");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"3520b974",null);e["default"]=l.exports},c350:function(t,e,a){"use strict";a.r(e);var n=a("7a4b"),i=a("e3c2");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("8742"),a("54b0");var o=a("2877"),l=Object(o["a"])(i["default"],n["a"],n["b"],!1,null,"545e6906",null);e["default"]=l.exports},c730:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("a9e3"),a("d3b7");var i=n(a("b775")),s=n(a("0f97"));n(a("4328")),e.default={name:"CWaiXieList",components:{DynamicDataTable:s.default},props:{mode:{type:Number,default:0},unit:{type:Object,default:function(){return{}}}},data:function(){return{apis:{GetFactoryAlloct:"/PRO/OperationPlan/GetFactoryAlloct",SetFactoryAlloct:"/PRO/OperationPlan/SetFactoryAlloct"},loading:!0,gridCode:"Priority_Analyse_List",tbConfig:{},columns:[],data:[],needs:[],Components:[],Allot_Detail:[],PIC_UserName:""}},created:function(){this.getAlloctData()},methods:{getExternalAmount:function(t){return this.Allot_Detail.find((function(e){return e.Component_Type_Id===t})).Plan_Amount},getAlloctData:function(){var t=this;this.loading=!0,(0,i.default)({url:this.apis.GetFactoryAlloct,method:"post",params:{installUntilId:this.unit.Id,factoryType:1}}).then((function(e){var a;if(!e.IsSucceed)return t.$message.warning(e.Message);t.needs=e.Data.Needs,t.Components=e.Data.Components,t.Allot_Detail=e.Data.Allot_Detail||[],t.PIC_UserName=null===(a=e.Data)||void 0===a?void 0:a.PIC_UserName,t.loading=!1}))}}}},d2b8:function(t,e,a){},da03:function(t,e,a){"use strict";var n=a("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("7db0"),a("c740"),a("d81d"),a("a434"),a("e9f5"),a("f665"),a("ab43"),a("a9e3"),a("d3b7");var i=n(a("b775")),s=n(a("0f97")),o=a("1b69"),l=a("477a");e.default={name:"CFactoryChange",components:{DynamicDataTable:s.default},props:{mode:{type:Number,default:0},unit:{type:Object,default:function(){return{}}},category:{type:String,default:""}},data:function(){return{factoryObj:{},apis:{SetFactoryAlloct:"/PRO/OperationPlan/SetFactoryAlloct",SaveFactoryAlloct:"/PRO/OperationPlan/SaveFactoryAlloct"},needs:[],Components:[],selectList:[],factoryies:[],tbLoading:!1,Adjust_Content:"",Factory_Name:"",Factory_Id:"",formData:null}},created:function(){1!==this.mode?(this.getAlloctData(),this.getFactory()):this.getDetail()},methods:{getDetail:function(){var t=this;(0,l.GetFactoryAlloctCount)({installUntilId:this.unit.Id}).then((function(e){e.IsSucceed?t.factoryObj=e.Data.Allot_Count:t.$message({message:e.Message,type:"error"})}))},getFactory:function(){var t=this;(0,o.GetFactoryList)({Category:this.category}).then((function(e){if(null!==e&&void 0!==e&&e.Data.length){var a=null===e||void 0===e?void 0:e.Data.findIndex((function(e){return e.Id===t.unit.Factory_Id}));-1!==a&&(null===e||void 0===e||e.Data.splice(a,1)),t.factoryies=null===e||void 0===e?void 0:e.Data}}))},cancel:function(){this.$emit("dialogCancel")},multiSelectedChange:function(t){this.selectList=t},getAlloctData:function(){var t=this;this.tbLoading=!0,(0,i.default)({url:this.apis.SetFactoryAlloct,method:"post",params:{installUntilId:this.unit.Id}}).then((function(e){if(!e.IsSucceed)return t.$message.warning(e.Message);0===t.mode?(t.needs=e.Data.Needs,t.formData=e.Data.Needs.map((function(a){var n=t.getExternalAmount(e.Data.Factory_Allot_Detail,a.Component_Type_Id);return t.$set(a,"Plan_Amount_All",a.Plan_Amount||0),t.$set(a,"Plan_Amount",n||0),Object.assign({},a)}))):(t.Components=e.Data.Components,t.Adjust_Content=e.Data.Adjust_Content),t.Factory_Name=e.Data.Default_Factory_Name,t.tbLoading=!1}))},getExternalAmount:function(t,e){var a;if(t&&t.length)return null===(a=t.find((function(t){return t.Component_Type_Id===e})))||void 0===a?void 0:a.Plan_Amount},confirmSubmit:function(){var t=this;if(this.Factory_Id){var e={InstallUnit_Id:this.unit.Id,Factory_Id:this.Factory_Id};0===this.mode?(e.Allot_Detail=this.formData,e.Adjust_Content=this.Adjust_Content):(e.comps=this.selectList.map((function(t){return t.code})),e.Adjust_Content=this.Adjust_Content),(0,i.default)({url:this.apis.SaveFactoryAlloct,method:"post",data:e}).then((function(e){if(!e.IsSucceed)return t.$message.warning(e.Message)})).finally((function(){t.cancel(),t.$emit("dialogFormSubmitSuccess",{type:"reload"})}))}else this.$message({message:"请选择要调整的工厂",type:"warning"})}}}},e3c2:function(t,e,a){"use strict";a.r(e);var n=a("ba0b"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},eefe:function(t,e,a){"use strict";a.r(e);var n=a("da03"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},ef72:function(t,e,a){"use strict";a("01b6f")},f32d:function(t,e,a){}}]);