(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-6bb995da"],{"1bb2":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddApproach=ue,t.AddBound=V,t.AddCheck=_e,t.AddComponentOperation=te,t.AddLocation=N,t.AddMaterial=_,t.AddMaterialType=d,t.AddSettlement=re,t.AddVendor=O,t.AddWareHouse=P,t.DeleteApproach=ce,t.DeleteBound=q,t.DeleteCheck=ke,t.DeleteLocation=w,t.DeleteMaterial=k,t.DeleteMaterialType=f,t.DeleteSettlement=oe,t.DeleteVendor=B,t.DeleteWareHouse=C,t.EditApproach=de,t.EditBound=U,t.EditCheck=Le,t.EditLocation=R,t.EditMaterial=L,t.EditMaterialType=c,t.EditSettlement=ie,t.EditVendor=$,t.EditWareHouse=v,t.ExportApproach=me,t.ExportCheck=be,t.ExportInBound=Pe,t.ExportOutBound=ve,t.ExportSettlement=se,t.GetApproach=fe,t.GetBoundDetailList=W,t.GetBoundEntity=F,t.GetBoundPageList=j,t.GetCheckDetailList=Me,t.GetComponentLog=K,t.GetDetailEntity=ye,t.GetDictionaryDetailListByCode=J,t.GetLocationEntity=S,t.GetLocationList=I,t.GetLocationPageList=D,t.GetLocationTree=G,t.GetMaterialInfoEntity=p,t.GetMaterialList=m,t.GetMaterialPageList=h,t.GetMaterialTypeEntity=u,t.GetMaterialTypeList=n,t.GetMaterialTypePageList=s,t.GetMaterialTypeTree=l,t.GetProfessionalType=Q,t.GetProjectsNodeList=Y,t.GetProjectsNodebyType=z,t.GetSettlement=le,t.GetStockList=he,t.GetStockPageList=H,t.GetStorageData=ee,t.GetStorageSteelData=Z,t.GetTraceData=X,t.GetTraceDetail=ae,t.GetVendorEntity=x,t.GetVendorList=A,t.GetVendorPageList=E,t.GetWareHouseEntity=g,t.GetWareHouseList=b,t.GetWareHousePageList=y,t.GetWareHouseTree=T,t.GetWorking_ObjectList=o,t.ImportApproach=pe,t.ImportCheck=ge,t.ImportMaterial=M,t.ImportSettlement=ne;var i=r(a("b775"));function o(e){return(0,i.default)({url:"/PLM/Plm_Projects/GetWorking_ObjectList",method:"post",data:e})}function l(){return(0,i.default)({url:"/PLM/MaterialType/GetTree",method:"post"})}function n(e){return(0,i.default)({url:"/PLM/MaterialType/GetList",method:"post",data:e})}function s(e){return(0,i.default)({url:"/PLM/MaterialType/GetPageList",method:"post",data:e})}function u(e){return(0,i.default)({url:"/PLM/MaterialType/GetEntity",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PLM/MaterialType/Add",method:"post",data:e})}function c(e){return(0,i.default)({url:"/PLM/MaterialType/Edit",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PLM/MaterialType/Delete",method:"post",data:e})}function p(e){return(0,i.default)({url:"/PLM/MaterialInfo/GetEntity",method:"post",data:e})}function m(e){return(0,i.default)({url:"/PLM/MaterialInfo/GetList",method:"post",data:e})}function h(e){return(0,i.default)({url:"/PLM/MaterialInfo/GetPageList",method:"post",data:e})}function _(e){return(0,i.default)({url:"/PLM/MaterialInfo/Add",method:"post",data:e})}function L(e){return(0,i.default)({url:"/PLM/MaterialInfo/Edit",method:"post",data:e})}function k(e){return(0,i.default)({url:"/PLM/MaterialInfo/Delete",method:"post",data:e})}function M(e){return(0,i.default)({url:"/PLM/MaterialInfo/ImportMaterial",method:"post",data:e})}function g(e){return(0,i.default)({url:"/PLM/MaterialWareHouse/GetEntity",method:"post",data:e})}function b(e){return(0,i.default)({url:"/PLM/MaterialWareHouse/GetList",method:"post",data:e})}function y(e){return(0,i.default)({url:"/PLM/MaterialWareHouse/GetPageList",method:"post",data:e})}function P(e){return(0,i.default)({url:"/PLM/MaterialWareHouse/Add",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PLM/MaterialWareHouse/Edit",method:"post",data:e})}function C(e){return(0,i.default)({url:"/PLM/MaterialWareHouse/Delete",method:"post",data:e})}function S(e){return(0,i.default)({url:"/PLM/MaterialLocation/GetEntity",method:"post",data:e})}function D(e){return(0,i.default)({url:"/PLM/MaterialLocation/GetPageList",method:"post",data:e})}function I(e){return(0,i.default)({url:"/PLM/MaterialLocation/GetList",method:"post",data:e})}function G(e){return(0,i.default)({url:"/PLM/MaterialLocation/GetAppTree",method:"post",data:e})}function N(e){return(0,i.default)({url:"/PLM/MaterialLocation/Add",method:"post",data:e})}function R(e){return(0,i.default)({url:"/PLM/MaterialLocation/Edit",method:"post",data:e})}function w(e){return(0,i.default)({url:"/PLM/MaterialLocation/Delete",method:"post",data:e})}function T(){return(0,i.default)({url:"/PLM/MaterialLocation/GetTree",method:"post"})}function x(e){return(0,i.default)({url:"/PLM/MaterialVendor/GetEntity",method:"post",data:e})}function E(e){return(0,i.default)({url:"/PLM/MaterialVendor/GetPageList",method:"post",data:e})}function A(){return(0,i.default)({url:"/PLM/MaterialVendor/GetList",method:"post"})}function O(e){return(0,i.default)({url:"/PLM/MaterialVendor/Add",method:"post",data:e})}function $(e){return(0,i.default)({url:"/PLM/MaterialVendor/Edit",method:"post",data:e})}function B(e){return(0,i.default)({url:"/PLM/MaterialVendor/Delete",method:"post",data:e})}function F(e){return(0,i.default)({url:"/PLM/MaterialBound/GetEntity",method:"post",data:e})}function j(e){return(0,i.default)({url:"/PLM/MaterialBound/GetPageList",method:"post",data:e})}function V(e){return(0,i.default)({url:"/PLM/MaterialBound/Add",method:"post",data:e})}function U(e){return(0,i.default)({url:"/PLM/MaterialBound/Edit",method:"post",data:e})}function q(e){return(0,i.default)({url:"/PLM/MaterialBound/Delete",method:"post",data:e})}function W(e){return(0,i.default)({url:"/PLM/MaterialBound/GetDetailList",method:"post",data:e})}function H(e){return(0,i.default)({url:"/PLM/MaterialBound/GetStockPageList",method:"post",data:e})}function J(e){return(0,i.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function Q(e){return(0,i.default)({url:"/PLM/Plm_Professional_Type/GetAllEntities",method:"post",data:e})}function z(e){return(0,i.default)({url:"/PLM/Plm_Projects_Node/GetEntities",method:"post",data:e})}function Y(e){return(0,i.default)({url:"/PLM/Plm_Projects_Node/GetNodeList",method:"post",data:e})}function K(e){return(0,i.default)({url:"/PLM/ComponentLog/GetComponentLog",method:"post",data:e})}function X(e){return(0,i.default)({url:"/PLM/ComponentLog/GetTraceData",method:"post",data:e})}function Z(e){return(0,i.default)({url:"/PLM/ComponentLog/GetStorageSteelData",method:"post",data:e})}function ee(e){return(0,i.default)({url:"/PLM/ComponentLog/GetStorageData",method:"post",data:e})}function te(e){return(0,i.default)({url:"/PRO/Component/AddComponentOperation",method:"post",data:e})}function ae(e){return(0,i.default)({url:"/PLM/ComponentLog/GetTraceDetail",method:"post",data:e})}function re(e){return(0,i.default)({url:"/PLM/MaterialRegister/Add",method:"post",data:e})}function ie(e){return(0,i.default)({url:"/PLM/MaterialRegister/Edit",method:"post",data:e})}function oe(e){return(0,i.default)({url:"/PLM/MaterialRegister/Delete",method:"post",data:e})}function le(e){return(0,i.default)({url:"/PLM/MaterialRegister/GetEntity",method:"post",data:e})}function ne(e){return(0,i.default)({url:"/PLM/MaterialRegister/ImportSettlement",method:"post",data:e})}function se(e){return(0,i.default)({url:"/PLM/MaterialRegister/ExportSettlement",method:"post",data:e})}function ue(e){return(0,i.default)({url:"/PLM/MaterialRegister/AddApproach",method:"post",data:e})}function de(e){return(0,i.default)({url:"/PLM/MaterialRegister/EditApproach",method:"post",data:e})}function ce(e){return(0,i.default)({url:"/PLM/MaterialRegister/DeleteApproach",method:"post",data:e})}function fe(e){return(0,i.default)({url:"/PLM/MaterialRegister/GetApproachEntity",method:"post",data:e})}function pe(e){return(0,i.default)({url:"/PLM/MaterialRegister/ImportApproach",method:"post",data:e})}function me(e){return(0,i.default)({url:"/PLM/MaterialRegister/ExportApproach",method:"post",data:e})}function he(e){return(0,i.default)({url:"/PLM/MaterialCheck/GetStockList",method:"post",data:e})}function _e(e){return(0,i.default)({url:"/PLM/MaterialCheck/AddCheck",method:"post",data:e})}function Le(e){return(0,i.default)({url:"/PLM/MaterialCheck/EditCheck",method:"post",data:e})}function ke(e){return(0,i.default)({url:"/PLM/MaterialCheck/DeleteCheck",method:"post",data:e})}function Me(e){return(0,i.default)({url:"/PLM/MaterialCheck/GetCheckDetailList",method:"post",data:e})}function ge(e){return(0,i.default)({url:"/PLM/MaterialCheck/ImportCheck",method:"post",data:e})}function be(e){return(0,i.default)({url:"/PLM/MaterialCheck/ExportCheck",method:"post",data:e})}function ye(e){return(0,i.default)({url:"/PLM/MaterialBound/GetDetailEntity",method:"post",data:e})}function Pe(e){return(0,i.default)({url:"/PLM/MaterialBound/ExportInBound",method:"post",data:e})}function ve(e){return(0,i.default)({url:"/PLM/MaterialBound/ExportOutBound",method:"post",data:e})}},2140:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100"},[a("div",{staticClass:"app-container h100"},[a("div",{staticClass:"top-btn",on:{click:e.toBack}},[a("el-button",[e._v("返回")])],1),a("div",{staticClass:"h100 wrapper-c"},[a("div",{staticClass:"wrapper-top"},[a("div",{staticClass:"top-title"},[a("span",[e._v("项目名称："+e._s(e.form.Short_Name||"—"))]),a("span",[e._v("质检单号："+e._s(e.form.Number||"—"))])])]),a("div",{staticClass:"wrapper-main"},[e.isInfo?e._e():a("div",{staticClass:"rectification-content"},[a("el-form",{ref:"form2",staticClass:"demo-form2",attrs:{model:e.form2,rules:e.rules2,"label-width":"100px"}},[e.isReview?a("el-form-item",{attrs:{label:"复核状态",prop:"IsPass"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form2.IsPass,callback:function(t){e.$set(e.form2,"IsPass",t)},expression:"form2.IsPass"}},[a("el-option",{attrs:{label:"合格",value:!0}}),a("el-option",{attrs:{label:"不合格",value:!1}})],1)],1):e._e(),a("el-form-item",{attrs:{label:e.isRectification?"整改内容":"复核内容",prop:"Suggestion"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",rows:4,placeholder:"请输入内容"},model:{value:e.form2.Suggestion,callback:function(t){e.$set(e.form2,"Suggestion",t)},expression:"form2.Suggestion"}})],1),a("el-form-item",{attrs:{label:"上传附件",prop:"fileList2"}},[a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:"image/*","file-list":e.fileList2,multiple:"",limit:5,"on-success":function(t,a,r){e.uploadSuccess(t,a,r)},"on-remove":e.uploadRemove,"on-preview":e.handlePreview2,"on-exceed":e.uploadExceed}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v(" 文件上传数量最多为5个 ")])])],1)],1)],1),a("div",{staticClass:"basic-information"},[a("header",[e._v("构件信息")]),a("el-form",{ref:"form",staticClass:"demo-form-inline",staticStyle:{"padding-left":"20px"},attrs:{inline:!0,model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"SteelName"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.SteelName,callback:function(t){e.$set(e.form,"SteelName",t)},expression:"form.SteelName"}})],1),a("el-form-item",{attrs:{label:"质检对象",prop:"Check_Object_Type"}},[a("el-input",{attrs:{value:0==e.form.Check_Object_Type?"构件":1==e.form.Check_Object_Type?"零件":"部件",type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"质检节点",prop:"Check_Node_Name"}},[a("el-input",{attrs:{type:"text",disabled:""},model:{value:e.form.Check_Node_Name,callback:function(t){e.$set(e.form,"Check_Node_Name",t)},expression:"form.Check_Node_Name"}})],1),a("el-form-item",{attrs:{label:"质检类型",prop:"Check_Type"}},[a("el-input",{attrs:{value:1==e.form.Check_Type?"质量":"探伤",type:"text",disabled:""}})],1),a("el-form-item",{attrs:{label:"质检结果",prop:"Sheet_Result"}},[0!==e.CheckFeeback.length||e.isSee?a("el-input",{attrs:{value:e.form.Sheet_Result,type:"text",disabled:""}}):a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Sheet_Result,callback:function(t){e.$set(e.form,"Sheet_Result",t)},expression:"form.Sheet_Result"}},[a("el-option",{attrs:{label:"合格",value:"合格"}}),a("el-option",{attrs:{label:"不合格",value:"不合格"}})],1)],1),"合格"!==e.form.Sheet_Result?a("el-form-item",{attrs:{label:"整改时限",prop:"Rectify_Date"}},[e.isSee?a("el-input",{attrs:{value:e.form.Rectify_Date,type:"text",disabled:""}}):a("el-date-picker",{attrs:{align:"right",type:"date",placeholder:"选择日期"},model:{value:e.form.Rectify_Date,callback:function(t){e.$set(e.form,"Rectify_Date",t)},expression:"form.Rectify_Date"}})],1):e._e(),"合格"!==e.form.Sheet_Result?a("el-form-item",{attrs:{label:"整改人",prop:"Rectifier_Id"}},[e.isSee?a("el-input",{attrs:{value:e.form.Rectifier_Name,type:"text",disabled:""}}):a("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.form.Rectifier_Id,callback:function(t){e.$set(e.form,"Rectifier_Id",t)},expression:"form.Rectifier_Id"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1):e._e(),"合格"!==e.form.Sheet_Result?a("el-form-item",{attrs:{label:"参与人",prop:"Participant_Id"}},[e.isSee?a("el-input",{attrs:{value:e.form.Participant_Name,type:"text",disabled:""}}):a("el-select",{attrs:{placeholder:"请选择",clearable:""},on:{change:e.changeCategory},model:{value:e.form.Participant_Id,callback:function(t){e.$set(e.form,"Participant_Id",t)},expression:"form.Participant_Id"}},e._l(e.userList,(function(e){return a("el-option",{key:e.Id,attrs:{value:e.Id,label:e.Display_Name}})})),1)],1):e._e()],1)],1),a("div",{staticClass:"inspection-type"},[a("header",[e._v("检查类型")]),0!==e.CheckFeeback.length?a("div",{staticClass:"radio-items"},e._l(e.CheckFeeback,(function(t){return a("div",{key:t.CheckId,staticClass:"radio-item"},[a("span",[e._v(e._s(t.CheckName))]),a("el-radio-group",{attrs:{disabled:e.isSee},on:{change:e.changePass},model:{value:t.isPass,callback:function(a){e.$set(t,"isPass",a)},expression:"item.isPass"}},[a("el-radio",{attrs:{label:!0}},[e._v("合格")]),a("el-radio",{attrs:{label:!1}},[e._v("不合格")])],1)],1)})),0):a("div",{staticClass:"no-radio-items"},[e._v("暂无检查类型")])]),a("div",{staticClass:"detailed-drawings"},[a("header",[e._v("深化图纸")]),0!==e.steelCadList.length?a("div",{staticClass:"deep-img"},e._l(e.steelCadList,(function(t,r){return a("div",{key:r,staticClass:"dwg_ico",on:{click:function(a){return e.openDwg(t.url)}}})})),0):a("div",{staticClass:"font-cad"},[e._v("无cad")])]),a("div",{staticClass:"inspection-description"},[a("header",[e._v("质检描述")]),a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",rows:4,placeholder:"请输入内容",disabled:e.isSee},model:{value:e.form.Suggestion,callback:function(t){e.$set(e.form,"Suggestion",t)},expression:"form.Suggestion"}})],1),"合格"!==e.form.Sheet_Result?a("div",{staticClass:"rectification"},[a("header",[e._v("整改问题")]),a("el-input",{staticStyle:{width:"50%"},attrs:{type:"textarea",rows:4,placeholder:"请输入内容",disabled:e.isSee},model:{value:e.form.Rectify_Description,callback:function(t){e.$set(e.form,"Rectify_Description",t)},expression:"form.Rectify_Description"}})],1):e._e(),a("div",{staticClass:"img-up"},[a("header",[e._v("图片上传")]),e.isSee?a("div",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.fileList,border:""}},[a("el-table-column",{attrs:{prop:"name",label:"文件名",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("div",{staticStyle:{overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},[e._v(" "+e._s(r.name||"-")+" ")])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"83",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.handlePreview(r)}}},[e._v("下载")])]}}])})],1)],1):a("OSSUpload",{staticClass:"upload-demo",attrs:{drag:"",action:"alioss",accept:"image/*","file-list":e.fileList,multiple:"",limit:5,disabled:e.isSee,"on-preview":e.handlePreview}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v(" 将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("文件上传数量最多为5个")])])],1),a("div",{staticClass:"check-items"},[a("header",[e._v("检查项")]),0!==e.CheckFeeback.length?e._l(e.CheckFeeback,(function(t){return a("div",{key:t.CheckId,staticClass:"check-table"},[a("div",{staticClass:"check-table-title"},[e._v(" 检查类型："+e._s(t.CheckName)+" ")]),a("el-table",{staticStyle:{width:"50%"},attrs:{data:t.Check_Item,stripe:""}},[a("el-table-column",{attrs:{prop:"Check_Content",label:"检查项",width:"180"}}),a("el-table-column",{attrs:{prop:"Eligibility_Criteria",label:"合格标准",width:"180"}}),a("el-table-column",{attrs:{prop:"Actual_Measurement",label:"检查内容"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("div",[a("el-input",{staticStyle:{width:"80%",border:"1px solid #eee","border-radius":"4px"},attrs:{disabled:e.isSee,type:"text"},on:{blur:function(t){e.inputBlur(t,r.Actual_Measurement,r)}},model:{value:r.Actual_Measurement,callback:function(t){e.$set(r,"Actual_Measurement",t)},expression:"row.Actual_Measurement"}})],1)]}}],null,!0)})],1)],1)})):a("div",{staticClass:"no-check-table"},[e._v("暂无检查项")])],2)]),e.isInfo?e._e():a("div",{staticClass:"submit-btn"},[a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("保存")])],1)])])])},i=[]},8979:function(e,t,a){"use strict";a.r(t);var r=a("2140"),i=a("ebd1");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("d972");var l=a("2877"),n=Object(l["a"])(i["default"],r["a"],r["b"],!1,null,"2f46020c",null);t["default"]=n.exports},bb2c:function(e,t,a){},cf72:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("a15b"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("d866"),a("f665"),a("ab43"),a("e9c4"),a("b64b"),a("d3b7");var i=r(a("ade3")),o=(a("1bb2"),a("d51a")),l=r(a("bbc2")),n=a("ed08");t.default={name:"PROQualityManagement",components:{OSSUpload:l.default},data:function(){var e;return e={btnLoading:!1,form2:{Sheet_Id:"",IsPass:"",Suggestion:"",fileList2:[]},rules2:{IsPass:[{required:!0,message:"请复核",trigger:"change"}]},form:{SteelName:"",Check_Object_Type:"",Check_Object_Type_Id:"",Check_Node_Id:"",Check_Node_Name:"",Check_Type:"",Sheet_Result:"合格",Rectifier_Id:"",Rectifier_Name:"",Rectify_Date:"",Participant_Id:"",Participant_Name:"",Participant_Ids:[],Rectify_Description:"",Suggestion:"",dateValue:"",Short_Name:"",Number:""},rules:{SteelName:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Check_Node_Name:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Check_Object_Type:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Check_Type:[{required:!0,message:"必填字段不能为空",trigger:"blur"}],Sheet_Result:[{required:!0,message:"请选择",trigger:"change"}],Rectifier_Id:[{required:!0,message:"请选择",trigger:"change"}],dateValue:[{required:!0,message:"请选择",trigger:"change"}]},comType:[]},(0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)(e,"form2",{}),"CheckFeeback",[]),"textarea1",""),"textarea2",""),"fileList",[]),"fileList2",[]),"Attachments",[]),"steelCadList",[]),"isEdit",!0),"checkDateList",[]),(0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)((0,i.default)(e,"Group_Ids",[]),"isAllPass",!0),"isSee",!1),"isRectification",!1),"isReview",!1),"isInfo",!1)},created:function(){this.form2.Sheet_Id=this.$route.query.sheetId,this.isSee=Boolean(this.$route.query.isSee),this.isRectification=this.$route.query.isRectification,this.isReview=this.$route.query.isReview,this.isInfo=this.$route.query.isInfo,this.getFactoryPeoplelist()},methods:{getFactoryPeoplelist:function(){var e=this;(0,o.GetFactoryPeoplelist)().then((function(t){t.IsSucceed&&(e.userList=t.Data,e.getEntityQualityManagement(e.$route.query.sheetId))}))},getEntityQualityManagement:function(e){var t=this;(0,o.EntityQualityManagement)({sheetid:e}).then((function(e){if(e.IsSucceed){if(t.isSee){if(t.checkDateList=e.Data,t.form.Short_Name=t.checkDateList.Short_Name,t.form.Number=t.checkDateList.Number,t.form.SteelName=t.checkDateList.SteelName,t.form.Check_Object_Type=t.checkDateList.Check_Object_Type,t.form.Check_Object_Type_Id=t.checkDateList.Check_Object_Type_Id,t.form.Check_Node_Id=t.checkDateList.Check_Node_Id,t.form.Check_Node_Name=t.checkDateList.Check_Node_Name,t.form.Check_Type=t.checkDateList.Check_Type,t.form.Sheet_Result=t.checkDateList.Sheet_Result,t.form.Rectifier_Id=t.checkDateList.Rectifier_Id,t.checkDateList.Rectify_Date?t.form.Rectify_Date=(0,n.parseTime)(new Date(t.checkDateList.Rectify_Date),"{y}-{m}-{d}"):t.form.Rectify_Date="",t.form.Participant_Ids=t.checkDateList.Participant_Ids,t.form.Rectify_Description=t.checkDateList.Rectify_Description,t.form.Suggestion=t.checkDateList.Suggestion,t.CheckFeeback=JSON.parse("null"===t.checkDateList.Check_Type_Result_Json?"[]":t.checkDateList.Check_Type_Result_Json),"不合格"===t.form.Sheet_Result){var a=t.userList.find((function(e){return e.Id===t.form.Rectifier_Id}));t.form.Rectifier_Name=null===a||void 0===a?void 0:a.Display_Name;var r=t.userList.find((function(e){return e.Id===t.form.Participant_Ids[0]}));t.form.Participant_Name=null===r||void 0===r?void 0:r.Display_Name}0!==t.checkDateList.Attachments.length&&t.checkDateList.Attachments.map((function(e){var a={name:"",url:""};a.name=e.File_Name,a.url=e.File_Url,t.fileList.push(a)}))}else t.checkDateList=e.Data,t.form.Short_Name=t.checkDateList.Short_Name,t.form.Number=t.checkDateList.Number,t.form.SteelName=t.checkDateList.SteelName,t.form.Check_Object_Type=t.checkDateList.Check_Object_Type,t.form.Check_Object_Type_Id=t.checkDateList.Check_Object_Type_Id,t.form.Check_Node_Id=t.checkDateList.Check_Node_Id,t.form.Check_Node_Name=t.checkDateList.Check_Node_Name,t.form.Check_Type=t.checkDateList.Check_Type,t.Group_Ids=t.checkDateList.Group_Ids,0!==e.Data.CheckFeebacks.length&&(t.CheckFeeback=e.Data.CheckFeebacks.map((function(e){return e.isPass=!0,0!==e.Check_Item.length&&e.Check_Item.map((function(t){t.Actual_Measurement="",t.Check_Type_Id=e.CheckId})),e}))),t.CheckFeeback=JSON.parse(JSON.stringify(t.CheckFeeback));t.getSheetDwg(t.$route.query.sheetId)}else t.$message({message:e.Message,type:"error"})}))},changePass:function(e){var t=this.CheckFeeback.every((function(e){return e.isPass}));this.form.Sheet_Result=t?"合格":"不合格"},changeCategory:function(e){e&&this.form.Participant_Ids.push(e)},uploadSuccess:function(e,t,a){var r=this;this.Attachments=[],a.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,r.Attachments.push(t)}))},uploadExceed:function(e,t){this.$message({type:"warning",message:"已超过文件上传最大数量"})},uploadRemove:function(e,t){var a=this;this.Attachments=[],t.map((function(e){var t={File_Name:"",File_Url:""};t.File_Name=e.name,e.hasOwnProperty("response")?t.File_Url=e.response.encryptionUrl:t.File_Url=e.url,a.Attachments.push(t)}))},handlePreview:function(e){var t=null;t=e.hasOwnProperty("response")?e.response.encryptionUrl:e.url,window.open(t,"_blank")},handlePreview2:function(e){var t=null;t=e.hasOwnProperty("response")?e.response.encryptionUrl:e.url,window.open(t,"_blank")},inputBlur:function(e,t,a){},getSheetDwg:function(e){var t=this;this.form.Check_Object_Type,(0,o.GetSheetDwg)({id:e}).then((function(e){e.IsSucceed&&(t.steelCadList=e.Data||[])}))},openDwg:function(e){var t=e.lastIndexOf("."),a=e.lastIndexOf("?"),r=e.substring(t,a);if(".pdf"===r)window.open(e+"#toolbar=0","_blank");else{var i=e.split(".com/");i[1]=encodeURIComponent(i[1]),e=i.join(".com/"),window.open("http://dwgv1.bimtk.com:5432/?CadUrl="+e,"_blank")}},submit:function(){var e=this,t=this.form2,a=t.Sheet_Id,r=t.IsPass,i=t.Suggestion,l={plm_Sheetreply:{Sheet_Id:a||"",IsPass:r,Suggestion:i||""},_Attachments:this.Attachments};this.isReview||delete l.plm_Sheetreply["IsPass"],this.$refs.form2.validate((function(t){if(!t)return!1;e.btnLoading=!0,(0,o.SaveFeedBack)(l).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),(0,n.closeTagView)(e.$store,e.$route)):e.$message({message:t.Message,type:"error"}),e.btnLoading=!1}))}))},toBack:function(){var e=this;this.isInfo?(0,n.closeTagView)(this.$store,this.$route):this.$confirm("此操作不会保存编辑数据，是否退出？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,n.closeTagView)(e.$store,e.$route)})).catch((function(){e.$message({type:"info",message:"已取消"})}))}}}},d972:function(e,t,a){"use strict";a("bb2c")},ebd1:function(e,t,a){"use strict";a.r(t);var r=a("cf72"),i=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);t["default"]=i.a}}]);