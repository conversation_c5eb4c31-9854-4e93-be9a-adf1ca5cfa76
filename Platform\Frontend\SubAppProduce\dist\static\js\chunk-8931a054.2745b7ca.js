(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-8931a054"],{"047a":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"receive-tb"},[e.isView||e.isReturn?e._e():a("div",{staticClass:"toolbar-container",staticStyle:{"margin-bottom":"8px"}},[a("vxe-toolbar",{scopedSlots:e._u([{key:"buttons",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.openAddDialog(null)}}},[e._v("新增")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,type:"danger",loading:e.deleteLoading},on:{click:e.handleDelete}},[e._v("删除 ")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length},on:{click:function(t){e.batchDialogVisible=!0}}},[e._v("批量编辑领用项目")]),a("PickSelect",{staticStyle:{"margin-left":"10px"},attrs:{"selected-list":e.currentTbData,"material-type":0},on:{addList:e.setTbData}}),e.isOutsourcing?a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.multipleSelection.length,type:"primary"},on:{click:e.BulkEdit}},[e._v("整车含税单价")]):e._e(),a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.init}})]},proxy:!0}],null,!1,1076660065)})],1),a("div",{staticClass:"tb-x"},[e.renderComponent?a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"","auto-resize":"",loading:e.tbLoading,stripe:"",size:"medium",data:e.currentTbData,resizable:"","edit-config":{enabled:e.enabledEdit,trigger:"click",mode:"cell",showIcon:!e.isView,showStatus:!0},"edit-rules":e.validRules,"tooltip-config":{enterable:!0},"show-footer":"","footer-method":e.footerMethod},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[e.isView?e._e():a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([t.Style.tips?{key:"header",fn:function(){return[a("span",[e._v(e._s(t.Display_Name))]),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(t.Style.tips)},slot:"content"}),a("i",{staticClass:"el-icon-question",staticStyle:{cursor:"pointer","font-size":"16px"}})])]},proxy:!0}:null,{key:"default",fn:function(r){var i=r.row;return["Warehouse_Location"===t.Code?a("span",[e._v(" "+e._s(i.WarehouseName)+"/"+e._s(i.LocationName)+" ")]):"InStoreDate"===t.Code?a("span",[e._v(" "+e._s(e._f("timeFormat")(i.InStoreDate))+" ")]):"RawName"===t.Code?[a("div",[i.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),i.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),i.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(i.RawName))],1)]:"OutStoreWeight"===t.Code?[e._v(" "+e._s(e._f("getFormatNum")(i.OutStoreWeight,e.WEIGHT_DECIMAL))+" ")]:"Voucher_Weight"===t.Code?[e._v(" "+e._s(e._f("getFormatNum")(i.Voucher_Weight,3))+" ")]:a("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(r){var i=r.row;return["Actual_Thick"===t.Code?a("div",[a("el-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})],1):"Width"===t.Code||"Length"===t.Code?a("div",[a("el-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.checkWeight(i)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})],1):"OutStoreCount"===t.Code?a("div",[a("el-input",{directives:[{name:"inp-num",rawName:"v-inp-num",value:{toFixed:e.COUNT_DECIMAL,min:0},expression:"{ toFixed: COUNT_DECIMAL, min: 0 }"}],attrs:{min:0,disabled:!!i.PickSubId,max:i.AvailableCount},on:{change:function(t){return e.checkCount(i)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})],1):"OutStoreWeight"===t.Code?a("div",[a("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]):"Pick_Project_Name"===t.Code?[a("vxe-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",transfer:"",clearable:"",filterable:"",disabled:!!i.PickSubId},on:{change:function(t){return e.changeProject(t,i)}},model:{value:i.Pick_Sys_Project_Id,callback:function(t){e.$set(i,"Pick_Sys_Project_Id",t)},expression:"row.Pick_Sys_Project_Id"}},e._l(e.projectOptions,(function(e){return a("vxe-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1)]:a("div",[a("el-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2):e._e()],1),e.isView?e._e():a("footer",[a("div",{staticClass:"data-info"},[e.isReturn?e._e():a("el-tag",{staticClass:"info-x",attrs:{size:"medium"}},[e._v("已选"+e._s(e.multipleSelection.length)+"条数据 ")])],1),a("div",[e._t("default")],2)]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"选择含税单价",visible:e.dialogVisible,width:"30%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?a("BatchEdit",{on:{close:e.handleClose,taxUnitPrice:e.getTaxUnitPrice}}):e._e()],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"批量编辑领用项目",visible:e.batchDialogVisible,top:"10vh",width:"350px"},on:{"update:visible":function(t){e.batchDialogVisible=t},close:e.closeBatchDialog}},[a("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.batchProjectId,callback:function(t){e.batchProjectId=t},expression:"batchProjectId"}},e._l(e.projectOptions,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Short_Name,value:e.Sys_Project_Id}})})),1),a("p",{staticStyle:{margin:"20px"}},[a("i",[e._v("注：仅能批量编辑公共库存的领用项目")])]),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{on:{click:e.closeBatchDialog}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.batchChangeProject}},[e._v("确定")])],1)],1)],1)},i=[]},"0884":function(e,t,a){"use strict";a("a927")},"15f6":function(e,t,a){"use strict";a("6bdc")},"15fd":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,a("a4d3");var r=i(a("ccb5"));function i(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(null==e)return{};var a,i,n=(0,r.default)(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],-1===t.indexOf(a)&&{}.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}},"183b":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-title"},[e._v("出库单信息")]),a("div",[a("el-button",{attrs:{type:"text"},on:{click:function(t){e.showSearch=!e.showSearch}}},[e._v(e._s(e.showSearch?"收起":"展开"))]),e.showSearch?a("i",{staticClass:"el-icon-caret-top",staticStyle:{color:"#409EFF"}}):a("i",{staticClass:"el-icon-caret-bottom",staticStyle:{color:"#409EFF"}})],1)]),a("el-divider",{staticClass:"cs-divider"}),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"出库类型",prop:"OutStoreType"}},[a("SelectMaterialStoreType",{attrs:{disabled:e.isView||e.isEdit||e.isReturn,type:"RawOutStoreType"},on:{change:e.typeChange},model:{value:e.form.OutStoreType,callback:function(t){e.$set(e.form,"OutStoreType",t)},expression:"form.OutStoreType"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"出库日期",prop:"OutStoreDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,"picker-options":{disabledDate:function(t){return t.getTime()<new Date(e.statisticTime).getTime()}},"value-format":"yyyy-MM-dd",type:"date"},model:{value:e.form.OutStoreDate,callback:function(t){e.$set(e.form,"OutStoreDate",t)},expression:"form.OutStoreDate"}})],1)],1),e.isReceive||e.isManual?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"领料部门",prop:e.isReceive?"Pick_Department_Id":""}},["true"===e.isProductweight?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.isView||e.isReturn},on:{change:e.departmentChange},model:{value:e.form.Pick_Department_Id,callback:function(t){e.$set(e.form,"Pick_Department_Id",t)},expression:"form.Pick_Department_Id"}},e._l(e.departmentOption,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1):a("el-tree-select",{ref:"treeSelectDepart",staticClass:"cs-tree-x",attrs:{"select-params":{clearable:!0,disabled:e.isReturn},"tree-params":e.treeParamsDepart},on:{"select-clear":e.departmentChange,"node-click":e.departmentChange},model:{value:e.form.Pick_Department_Id,callback:function(t){e.$set(e.form,"Pick_Department_Id",t)},expression:"form.Pick_Department_Id"}})],1)],1):e._e(),a("transition",{attrs:{name:"fade"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}]},[e.isReceive||e.isManual?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"领料人",prop:e.isReceive?"ReceiveUserId":""}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.isView||!e.form.Pick_Department_Id||e.isReturn},on:{change:e.userChange},model:{value:e.form.ReceiveUserId,callback:function(t){e.$set(e.form,"ReceiveUserId",t)},expression:"form.ReceiveUserId"}},e._l(e.factoryPeopleList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1):e._e(),e.isReceive?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"领料班组",prop:"WorkingTeamId"}},[a("SelectTeam",{model:{value:e.form.WorkingTeamId,callback:function(t){e.$set(e.form,"WorkingTeamId",t)},expression:"form.WorkingTeamId"}})],1)],1):e._e(),e.isReceive?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"使用工序",prop:"Use_Processing_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:"",disabled:e.isView||e.isReturn},model:{value:e.form.Use_Processing_Id,callback:function(t){e.$set(e.form,"Use_Processing_Id",t)},expression:"form.Use_Processing_Id"}},e._l(e.ProcessList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1)],1):e._e(),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"整单磅重(t)",prop:"Pound_Weight"}},[a("el-input",{attrs:{disabled:e.isView||e.isReturn,type:"number",placeholder:"请输入"},model:{value:e.form.Pound_Weight,callback:function(t){e.$set(e.form,"Pound_Weight",t)},expression:"form.Pound_Weight"}})],1)],1),e.isReturn?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"磅重(t)",prop:"Car_Pound_Weight"}},[a("el-input",{attrs:{disabled:e.isView,type:"number",placeholder:"请输入"},model:{value:e.form.Car_Pound_Weight,callback:function(t){e.$set(e.form,"Car_Pound_Weight",t)},expression:"form.Car_Pound_Weight"}})],1)],1):e._e(),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.isView||e.isReturn,rows:1,"show-word-limit":"",maxlength:100,placeholder:"备注",type:"textarea"},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1),e.isReturn?e._e():a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"factory-img",attrs:{label:"附件"}},[a("OSSUpload",{ref:"upload",staticClass:"cs-upload",attrs:{disabled:e.isView||e.isReturn,"on-preview":e.handlePreview,"on-remove":e.uploadRemove,multiple:"","file-list":e.fileListData,action:"",limit:5,"on-success":e.uploadSuccess}},[e.isView||e.isReturn?e._e():a("el-button",{attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1)],1)],1),e.isReturn?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退库部门",prop:"Return_Dept_Id"}},[a("SelectDepartment",{attrs:{disabled:e.isView},model:{value:e.form.Return_Dept_Id,callback:function(t){e.$set(e.form,"Return_Dept_Id",t)},expression:"form.Return_Dept_Id"}})],1)],1):e._e(),e.isReturn?a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"退库人",prop:"Return_Person_Id "}},[a("SelectDepartmentUser",{attrs:{"department-id":e.form.Return_Dept_Id},model:{value:e.form.Return_Person_Id,callback:function(t){e.$set(e.form,"Return_Person_Id",t)},expression:"form.Return_Person_Id"}})],1)],1):e._e()],1)])],1)],1)],1)},i=[]},1981:function(e,t,a){"use strict";a("487e")},"21ce":function(e,t,a){"use strict";a.r(t);var r=a("047a"),i=a("f3fc");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("4f51");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"04ccf721",null);t["default"]=s.exports},2239:function(e,t,a){"use strict";a.r(t);var r=a("528c"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},2638:function(e,t,a){"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t,a=1;a<arguments.length;a++)for(var r in t=arguments[a],t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)}var i=["attrs","props","domProps"],n=["class","style","directives"],o=["on","nativeOn"],s=function(e){return e.reduce((function(e,t){for(var a in t)if(e[a])if(-1!==i.indexOf(a))e[a]=r({},e[a],t[a]);else if(-1!==n.indexOf(a)){var s=e[a]instanceof Array?e[a]:[e[a]],u=t[a]instanceof Array?t[a]:[t[a]];e[a]=[].concat(s,u)}else if(-1!==o.indexOf(a))for(var c in t[a])if(e[a][c]){var d=e[a][c]instanceof Array?e[a][c]:[e[a][c]],f=t[a][c]instanceof Array?t[a][c]:[t[a][c]];e[a][c]=[].concat(d,f)}else e[a][c]=t[a][c];else if("hook"===a)for(var m in t[a])e[a][m]=e[a][m]?l(e[a][m],t[a][m]):t[a][m];else e[a]=t[a];else e[a]=t[a];return e}),{})},l=function(e,t){return function(){e&&e.apply(this,arguments),t&&t.apply(this,arguments)}};e.exports=s},"280f":function(e,t,a){"use strict";a.r(t);var r=a("3605"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},"2a6c":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getThousandValue=t.getSummaryInfo=t.formatNum=void 0,a("99af"),a("e9f5"),a("7d54"),a("b680"),a("d3b7"),a("38cf"),a("159b");var i=r(a("6612")),n=t.formatNum=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return"number"!==typeof e?0:e.toFixed(t)/1};t.getThousandValue=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e)return 0;var r="0".repeat(t),n=(0,i.default)(e).divide(1e3).format("".concat(a?"0,":"","0.[").concat(r,"]"));return parseFloat(n)},t.getSummaryInfo=function(e){var t={OutStoreCountTotal:0,OutStoreWeightTotal:0,PoundWeightTotal:0,VoucherWeightTotal:0},a=0,r=0;e.forEach((function(e){t.OutStoreCountTotal+=e.OutStoreCount||0,t.OutStoreWeightTotal+=e.OutStoreWeight||0,t.PoundWeightTotal+=e.Pound_Weight||0,t.VoucherWeightTotal+=e.Voucher_Weight_KG||0,a+=(e.OutStoreWeight||0)*(e.TaxUnitPrice||0),r+=e.TaxUnitPrice*e.OutStoreWeight/(1+e.Tax_Rate/100)||0}));var i={includingTax:n(a,3),excludingTax:n(r,3),OutStoreCountTotal:t.OutStoreCountTotal,OutStoreWeightTotal:t.OutStoreWeightTotal,PoundWeightTotal:t.PoundWeightTotal,VoucherWeightTotal:t.VoucherWeightTotal/1e3};return i}},3605:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("c14f")),n=r(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("13d5"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("9485"),a("a9e3"),a("d3b7"),a("25f0"),a("159b");var o=r(a("287a")),s=(a("cf45"),a("3166")),l=a("4744"),u=a("93aa"),c=a("6186"),d=a("3c4a"),f=a("a024"),m=a("ed08"),h=r(a("e989")),p=r(a("5d4b")),g=r(a("8c02")),v=r(a("c13a"));t.default={components:{SelectDepartmentUser:v.default,SelectDepartment:g.default,SelectMaterialStoreType:p.default,SelectTeam:h.default,OSSUpload:o.default},props:{pageType:{type:Number,default:void 0},isView:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},isReturn:{type:Boolean,default:!1}},data:function(){return{form:{OutStoreType:+this.$route.query.OutStoreType||1,OutStoreDate:this.getDate(),SysProjectId:"",Pick_Project_Name:"",ReceiveUserId:"",Use_Processing_Id:"",Pick_Department_Id:"",Remark:"",Attachment:""},rawOutboundTypeList:[],projectList:[],departmentOption:[],factoryPeopleList:[],fileListData:[],ProcessList:[],isProductweight:"",statisticTime:"",treeParamsDepart:{"default-expand-all":!0,filterable:!1,clickParent:!0,data:[],props:{disabled:"disabled",children:"Children",label:"Label",value:"Id"}},showSearch:!0,rules:{OutStoreDate:[{required:!0,message:"请选择",trigger:"change"}],ReceiveUserId:[{required:!0,message:"请选择",trigger:"change"}],Pick_Department_Id:[{required:!0,message:"请选择",trigger:"change"}]}}},computed:{isReceive:function(){return 1==this.form.OutStoreType},isManual:function(){return 3==this.form.OutStoreType}},watch:{"form.OutStoreType":{handler:function(e){this.$emit("typeChange",e)},immediate:!0}},mounted:function(){this.getBaseInfo()},methods:{userChange:function(e){var t=this;(0,d.GetTeamListByUserForMateriel)({id:e}).then((function(e){var a;1===(null===(a=e.Data)||void 0===a?void 0:a.length)?t.$set(t.form,"WorkingTeamId",e.Data[0].Id):t.$set(t.form,"WorkingTeamId","")}))},typeChange:function(e){this.$emit("typeChange",e),this.$emit("initTb")},setForm:function(e){var t=e.Attachment.split(",");this.fileListData=t.filter((function(e){return!!e})).map((function(e,t){var a=e.split("?")[0],r=a.split("/"),i=r[r.length-1];return{name:decodeURIComponent(i),url:a}})),Object.assign(this.form,e),this.getUserPageList(this.form.Pick_Department_Id)},getBaseInfo:function(){var e=this;(0,f.GetProcessListBase)().then((function(t){t.IsSucceed?e.ProcessList=t.Data.filter((function(e){return e.Is_Enable})):e.$message({message:t.Message,type:"error"})})),(0,s.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){var a;t.IsSucceed?e.projectList=(null===(a=t.Data)||void 0===a?void 0:a.Data)||[]:e.$message({message:t.Message,type:"error"})})),(0,l.GetPreferenceSettingValue)({code:"Productweight"}).then((function(t){t.IsSucceed?(e.isProductweight=t.Data,"true"===e.isProductweight?e.getDepartmentOption():e.getDepartmentTree()):e.$message({message:t.Message,type:"error"})})),(0,u.GetOMALatestStatisticTime)().then((function(t){t.IsSucceed&&(e.statisticTime=t.Data)}))},getDepartmentOption:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:a=localStorage.getItem("CurReferenceId"),(0,d.GetFirstLevelDepartsUnderFactory)({FactoryId:a}).then((function(t){t.IsSucceed?e.departmentOption=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getDepartmentTree:function(){var e=this,t=function(e){e&&e.forEach((function(e){var a=e.Children;!0===e.Data.Is_Company||"1"==e.Data.Type?e.disabled=!0:e.disabled=!1,a.length>0&&t(a)}))};(0,c.GetCompanyDepartTree)({isAll:!0}).then((function(a){if(a.IsSucceed){var r=a.Data;t(r),e.treeParamsDepart.data=r,e.$nextTick((function(t){var a;null===(a=e.$refs)||void 0===a||null===(a=a.treeSelectDepart)||void 0===a||a.treeDataUpdateFun(r)}))}else e.$message({message:a.Message,type:"error"})}))},departmentChange:function(){this.form.ReceiveUserId="",this.form.Pick_Department_Id&&this.getUserPageList(this.form.Pick_Department_Id)},getUserPageList:function(e){var t=this;return(0,n.default)((0,i.default)().m((function a(){return(0,i.default)().w((function(a){while(1)switch(a.n){case 0:return a.n=1,(0,d.GetUserPage)({PageSize:-1,DepartmentId:e}).then((function(e){e.IsSucceed?t.factoryPeopleList=e.Data.Data:t.$message({message:e.Message,type:"error"})}));case 1:return a.a(2)}}),a)})))()},getDate:function(){return(0,m.parseTime)(new Date,"{y}-{m}-{d}")},projectChange:function(e){var t;this.form.Pick_Project_Name=null===(t=this.projectList.find((function(t){return t.Sys_Project_Id===e})))||void 0===t?void 0:t.Short_Name,this.$emit("initTb")},getForm:function(){var e=this.fileListData.reduce((function(e,t){var a,r=(null===t||void 0===t||null===(a=t.response)||void 0===a?void 0:a.encryptionUrl)||t.url;return e.push(r),e}),[]);return this.form.Attachment=e.filter((function(e){return!!e})).toString(),this.form},handleClick:function(){},checkForm:function(){return this.$refs["form"].validate((function(e){if(!e)return!1})),!this.form.Pick_Department_Id&&this.isReceive?(this.$message({message:"领料部门不能为空！",type:"warning"}),!1):!(!this.form.ReceiveUserId&&this.isReceive)||(this.$message({message:"领料人不能为空！",type:"warning"}),!1)},uploadSuccess:function(e,t,a){this.fileListData=a},uploadRemove:function(e,t){this.fileListData=t},handlePreview:function(e){return(0,n.default)((0,i.default)().m((function t(){var a,r,n;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(r=(null===e||void 0===e||null===(a=e.response)||void 0===a?void 0:a.encryptionUrl)||e.url,r){t.n=1;break}return t.a(2);case 1:return t.n=2,(0,c.GetOssUrl)({url:r});case 2:n=t.v,window.open(n.Data);case 3:return t.a(2)}}),t)})))()},handleExceed:function(){this.$message({type:"warning",message:"附件数量不能超过5个"})}}}},"487e":function(e,t,a){},"48cc":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("5530")),n=r(a("c14f")),o=r(a("1da1"));a("4de4"),a("7db0"),a("d81d"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("d3b7");var s=a("9002"),l=a("93aa"),u=a("3c4a"),c=a("ed08"),d=a("2a6c"),f=a("e144"),m=r(a("bad9")),h=r(a("29d9")),p=r(a("b5b0")),g=a("8fea");a("90d1"),t.default={components:{SelectLocation:p.default,SelectWarehouse:h.default,SelectProject:m.default},props:{formData:{type:Object,default:function(){}},joinedItems:{type:Array,default:function(){return[]}}},data:function(){return{form:{Raw_FullName:"",RawName:"",Thick:void 0,Spec:"",Material:"",Supplier:"",SupplierName:"",PartyUnit:"",PartyUnitName:"",Raw_Property:"",Category_Id:"",Width:void 0,Length:void 0,SysProjectId:"",Location:"",Warehouse:""},addLoading:!1,tbLoading:!1,saveLoading:!1,treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Id"}},columns:[],originalData:[],multipleSelection:[]}},computed:{fTable:function(){var e=this;return this.originalData.filter((function(t){return!e.joinedItems.find((function(e){return e.Store_Sub_Id===t.Store_Sub_Id}))}))}},mounted:function(){var e=this.formData,t=e.Supplier,a=e.PartyUnit;(t||a)&&(this.form.Supplier=t,this.form.PartyUnit=a),this.getCategoryTreeList(),this.getConfig()},methods:{getConfig:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.tbLoading=!0,t.n=1,(0,s.getTableConfig)("pro_raw_material_outbound_detail_list,Steel");case 1:e.columns=t.v,e.fetchData();case 2:return t.a(2)}}),t)})))()},getCategoryTreeList:function(){var e=this;(0,l.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$refs.treeSelect.treeDataUpdateFun(t.Data)):e.$message({message:t.Message,type:"error"})}))},handleSearch:function(){this.fetchData()},resetForm:function(e){this.$refs[e].resetFields(),this.fetchData()},handleSelect:function(e){this.multipleSelection=e},tbSelectChange:function(e){this.multipleSelection=e.records},fetchData:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:e.tbLoading=!0,(0,u.GetOutFromSourceData)((0,i.default)({},e.form)).then((function(t){t.IsSucceed?(e.originalData=t.Data.map((function(e){return e.OutStoreCount=e.AvailableCount,e.TaxUnitPrice=(0,d.formatNum)(e.TaxUnitPrice,g.UNIT_PRICE_DECIMAL),e.NoTaxUnitPrice=(0,d.formatNum)(e.NoTaxUnitPrice,g.UNIT_PRICE_DECIMAL),e.Tax_All_Price=(0,d.formatNum)(e.Tax_All_Price),e})),e.multipleSelection=[]):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}));case 1:return t.a(2)}}),t)})))()},addToList:function(e){var t=this;this.addLoading=!0,setTimeout((function(){var a=(0,c.deepClone)(t.multipleSelection),r=a.map((function(e){return e.Pick_Project_Name=t.formData.Pick_Project_Name,e.uuid=(0,f.v4)(),delete e._X_ROW_KEY,e}));t.$emit("getAddList",r),t.multipleSelection=[],t.$refs.xTable1.clearCheckboxRow(),t.addLoading=!1,t.saveLoading=!1,e&&t.handleClose()}),0)},handleSave:function(){this.saveLoading=!0,this.addToList(!0)},handleClose:function(){this.$emit("close")}}}},"4cbed":function(e,t,a){"use strict";a.r(t);var r=a("4e92"),i=a("7600");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("0884");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"68d59a3f",null);t["default"]=s.exports},"4d8b":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getToken=i,a("d3b7");var r=a("6186");function i(){return new Promise((function(e,t){(0,r.SecurityToken)({}).then((function(a){if(a.IsSucceed){var r=a.Data,i=r.regionId,n=r.AccessKeyId,o=r.AccessKeySecret,s=r.SecurityToken,l=r.bucket;e({regionId:i,AccessKeyId:n,AccessKeySecret:o,SecurityToken:s,bucket:l})}else t()}))}))}},"4e82":function(e,t,a){"use strict";var r=a("23e7"),i=a("e330"),n=a("59ed"),o=a("7b0b"),s=a("07fa"),l=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),m=a("3f7e"),h=a("99f4"),p=a("1212"),g=a("ea83"),v=[],b=i(v.sort),_=i(v.push),y=c((function(){v.sort(void 0)})),S=c((function(){v.sort(null)})),P=f("sort"),C=!c((function(){if(p)return p<70;if(!(m&&m>3)){if(h)return!0;if(g)return g<603;var e,t,a,r,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(r=0;r<47;r++)v.push({k:t+r,v:a})}for(v.sort((function(e,t){return t.v-e.v})),r=0;r<v.length;r++)t=v[r].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}})),I=y||!S||!P||!C,w=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};r({target:"Array",proto:!0,forced:I},{sort:function(e){void 0!==e&&n(e);var t=o(this);if(C)return void 0===e?b(t):b(t,e);var a,r,i=[],u=s(t);for(r=0;r<u;r++)r in t&&_(i,t[r]);d(i,w(e)),a=s(i),r=0;while(r<a)t[r]=i[r++];while(r<u)l(t,r++);return t}})},"4e92":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"statistics-container"},[a("div",{staticClass:"statistics-item"},[a("span",{staticClass:"item-label"},[e._v("数量")]),a("span",{staticClass:"item-num"},[e._v(e._s(e.formatNum(e.statisticsData.OutStoreCountTotal)))])]),a("div",{staticClass:"statistics-item"},[a("span",{staticClass:"item-label"},[e._v("出库理重(t)")]),a("span",{staticClass:"item-num"},[e._v(e._s(e.formatNum(e.statisticsData.OutStoreWeightTotal,5)))])]),a("div",{staticClass:"statistics-item"},[a("span",{staticClass:"item-label"},[e._v("出库磅重(t)")]),a("span",{staticClass:"item-num"},[e._v(e._s(e.formatNum(e.statisticsData.PoundWeightTotal,3)))])])])},i=[]},"4f51":function(e,t,a){"use strict";a("b047")},"528c":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=r(a("c14f")),n=r(a("1da1")),o=r(a("15fd")),s=r(a("5530")),l=r(a("8468"));a("d81d"),a("e9f5"),a("ab43"),a("a732"),a("a9e3"),a("d3b7");var u=r(a("c7ab")),c=r(a("c540")),d=r(a("c602")),f=r(a("21ce")),m=r(a("e231")),h=r(a("dfb1")),p=r(a("4cbed")),g=a("3c4a"),v=a("e144"),b=a("ed08"),_=["OutStoreWeight","Voucher_Weight","TaxUnitPrice","AvailableWeight"];t.default={components:{DialogInfo:h.default,SummaryInfo:p.default,OSSUpload:u.default,InfoSearch:c.default,DetailSearch:d.default,ReceiveTb:f.default,ReturnedTb:m.default},props:{pageType:{type:Number,default:void 0}},data:function(){return{returning:!1,saveLoading:!1,submitLoading:!1,showDialog:!1,formData:{OutStoreType:1},tbData:[]}},computed:{isAdd:function(){return 1===this.pageType},isEdit:function(){return 2===this.pageType},isView:function(){return 3===this.pageType},isReturn:function(){return 8===this.pageType},isOutsourcing:function(){return 3===this.formData.OutStoreType},isReceive:function(){return 1===this.formData.OutStoreType}},mounted:function(){this.isAdd||this.getInfo()},methods:{setInfo:function(e){e&&(this.$refs.infoRef.form.WorkingTeamId=e.Pick_Team_Id,this.$refs.infoRef.form.Use_Processing_Id=e.Pick_Process_Id)},initGrid:function(){this.$refs.returnTbRef.init()},typeChange:function(e){this.formData.OutStoreType=e},closeView:function(){(0,b.closeTagView)(this.$store,this.$route)},submit:function(e,t,a){var r=this,i=Object.assign({},((0,l.default)(t),t)),n=(0,s.default)({Status:e,ReceiveUserId:t.ReceiveUserId,Pick_Department_Id:t.Pick_Department_Id},i),u=a.map((function(e){var t=e.OutStoreWeight,a=e.Voucher_Weight,i=e.TaxUnitPrice,n=e.AvailableWeight,l=(0,o.default)(e,_),u=(0,s.default)({OutStoreWeight:t,AvailableWeight:n,Voucher_Weight:a,TaxUnitPrice:i},l);if(r.isReturn){var c=e.ReturnStoreWeight,d=e.ReturnVoucherWeight,f=e.InStoreWeight;Object.assign(u,{ReturnStoreWeight:c,ReturnVoucherWeight:d,InStoreWeight:f})}return u})),c="";this.isReturn?(c=g.RawReturnByReceipt,this.returning=!0):c=1===n.OutStoreType?g.PickUpOutStore:g.OutSourcingOutStore,c({Receipt:n,Sub:u}).then((function(e){e.IsSucceed?(r.$message({message:"保存成功",type:"success"}),r.closeView()):r.$message({message:e.Message,type:"error"}),r.returning=!1,r.saveLoading=!1,r.submitLoading=!1}))},saveDraft:function(){this.formData=this.$refs["infoRef"].getForm();var e=this.$refs["infoRef"].checkForm();if(e){this.saveLoading=!0;var t=this.$refs["receiveTbRef"].getTbData();t.some((function(e){return!e.Pick_Sys_Project_Id}))?this.$message.warning("领用项目必须填写"):this.submit(1,this.formData,t)}},handleSubmit:function(){var e=this;this.formData=this.$refs["infoRef"].getForm();var t=this.$refs["infoRef"].checkForm(),a=this.$refs["receiveTbRef"].getTbData();if(a.some((function(e){return!e.Pick_Sys_Project_Id})))this.$message.warning("领用项目必须填写");else if(t){var r="";a=a.map((function(e){return(e.OutStoreWeight>=e.AvailableWeight&&e.OutStoreCount!=e.AvailableCount||e.OutStoreWeight!=e.AvailableWeight&&e.OutStoreCount>=e.AvailableCount)&&(r="明细数据中数量或重量有一个值为库存最大值但另一个值非最大值，非最大值将自动被改为最大值",e.OutStoreCount=e.AvailableCount),e})),r&&this.$message.info(r),this.$confirm("确认提交出库单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.submitLoading=!0,e.submit(3,e.formData,a)})).catch((function(){e.$message({type:"info",message:"已取消"})}))}},handleReturn:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.formData=e.$refs["infoRef"].getForm(),e.returning=!0,t.n=1,e.$refs["returnTbRef"].checkTbDate();case 1:if(a=t.v,a){t.n=2;break}return e.returning=!1,t.a(2);case 2:e.$confirm("是否提交退库信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t=e.$refs["returnTbRef"].getTbData();e.submit(3,e.formData,t)})).catch((function(t){e.$message({type:"info",message:"已取消"})}));case 3:return t.a(2)}}),t)})))()},initTb:function(){this.$refs["detailSearchRef"].handleReset(),this.$refs["receiveTbRef"].clearTb()},handleClose:function(){this.showDialog=!1},getAddList:function(e){this.$refs["receiveTbRef"].setTbData(e,!0)},getInfo:function(){var e,t=this,a={};this.isReturn?(e=g.GetRawDetailByReceipt,a.Id=this.$route.query.id):(e=1===+this.$route.query.OutStoreType?g.PickUpOutStoreDetail:g.OutSourcingOutStoreDetail,a.outStoreNo=this.$route.query.OutStoreNo),e(a).then((function(e){if(e.IsSucceed){var a=e.Data.Receipt,r=e.Data.Sub,i=a.OutStoreDate,n=a.Remark,o=a.SysProjectId,s=a.Pick_Department_Id,l=a.ReceiveUserId,u=a.Use_Processing_Id,c=a.Attachment,d=a.Status;t.$refs["infoRef"].setForm({OutStoreNo:t.$route.query.OutStoreNo,OutStoreDate:i,Remark:n,SysProjectId:o,Pick_Department_Id:s,ReceiveUserId:l,Use_Processing_Id:u,Attachment:c,Status:d});var f=r.map((function(e){return e.uuid=(0,v.v4)(),e}));t.isReturn?t.$refs["returnTbRef"].setData(f):t.$refs["receiveTbRef"].setTbData(f,!1)}else t.$message({message:e.Message,type:"error"})}))},addNew:function(){var e=this;this.formData=this.$refs["infoRef"].getForm(),this.tbData=this.$refs["receiveTbRef"].getTbData(),this.$nextTick((function(t){e.showDialog=!0}))},getDetailInfo:function(e){var t,a;null===(t=this.$refs["receiveTbRef"])||void 0===t||t.filterMethod(e),null===(a=this.$refs["returnTbRef"])||void 0===a||a.filterMethod(e)},changeWarehouse:function(){}}}},"52a9":function(e,t,a){"use strict";a.r(t);var r=a("747a"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},"5d35":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("e9f5"),a("f665"),a("a9e3"),a("d3b7");var r=a("9643");t.default={props:{pageType:{type:Number,default:void 0}},data:function(){return{warehouses:[],locations:[],form:{Warehouse_Id:"",Location_Id:""},btnLoading:!1,rules:{Warehouse_Id:[{required:!0,message:"请选择",trigger:"change"}],Location_Id:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){this.getWarehouseListOfCurFactory()},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){if(!t)return!1;var a=e.warehouses.find((function(t){return t.Id===e.form.Warehouse_Id})),r=e.locations.find((function(t){return t.Id===e.form.Location_Id}));e.$emit("warehouse",{warehouse:a,location:r}),e.btnLoading=!1,e.handleClose()}))},getWarehouseListOfCurFactory:function(){var e=this;(0,r.GetWarehouseListOfCurFactory)({type:0===this.pageType?"原材料仓库":"辅料仓库"}).then((function(t){if(t.IsSucceed){if(e.warehouses=t.Data,e.warehouses.length){var a=e.warehouses.find((function(e){return e.Is_Default}));a&&(e.form.Warehouse_Id=a.Id,e.getLocationList(!0))}}else e.$message({message:t.Message,type:"error"})}))},wareChange:function(e){this.form.Location_Id="",e&&this.getLocationList(!0)},getLocationList:function(e){var t=this;(0,r.GetLocationList)({Warehouse_Id:this.form.Warehouse_Id}).then((function(a){if(a.IsSucceed){if(t.locations=a.Data,t.locations.length&&e){var r=t.locations.find((function(e){return e.Is_Default}));r&&(t.form.Location_Id=r.Id)}}else t.$message({message:a.Message,type:"error"})}))},handleClose:function(){this.$emit("close")}}}},"606b":function(e,t,a){"use strict";a.r(t);var r=a("c04a"),i=a("2239");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("f6d3");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"39a896d0",null);t["default"]=s.exports},6104:function(e,t,a){"use strict";a("e880")},6836:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},on:{change:e.wareChange},model:{value:e.form.Warehouse_Id,callback:function(t){e.$set(e.form,"Warehouse_Id",t)},expression:"form.Warehouse_Id"}},e._l(e.warehouses,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"库位",prop:"Location_Id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.form.Warehouse_Id,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.form.Location_Id,callback:function(t){e.$set(e.form,"Location_Id",t)},expression:"form.Location_Id"}},e._l(e.locations,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Display_Name,value:e.Id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading},on:{click:e.submit}},[e._v("确 定")])],1)],1)},i=[]},69071:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"return-tb"},[a("DynamicTableFields",{staticStyle:{"margin-left":"auto"},attrs:{title:"表格配置","table-config-code":e.gridCode},on:{updateColumn:e.init}}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"tb-x"},[e.tbLoading?e._e():a("vxe-table",{ref:"xTable",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","row-config":{isCurrent:!0,isHover:!0},align:"left",height:"auto","show-overflow":"",loading:e.tbLoading,"auto-resize":!0,stripe:"",size:"medium",data:e.currentTbData,resizable:"","edit-config":{enabled:e.enabledEdit,trigger:"click",mode:"cell",showIcon:!e.isView,showStatus:!0},"edit-rules":e.validRules,"tooltip-config":{enterable:!0}}},[e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?t.Frozen_Dirction||"left":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width,"edit-render":t.Is_Edit?{}:null},scopedSlots:e._u([{key:"default",fn:function(r){var i=r.row;return["AvailableWeight"===t.Code?a("span",[e._v(" "+e._s(i[t.Code]||0===i[t.Code]?(i[t.Code]/1e3).toFixed(5):"-")+" ")]):"Warehouse_Location"===t.Code?a("span",[e._v(" "+e._s(i.WarehouseName)+"/"+e._s(i.LocationName)+" ")]):"Voucher_Weight"===t.Code?a("span",[e._v(" "+e._s(e._f("getFormatNum")(i.Voucher_Weight,3))+" ")]):"OutStoreWeight"===t.Code?a("span",[e._v(" "+e._s(e._f("getFormatNum")(i.OutStoreWeight,5))+" ")]):"InStoreDate"===t.Code?a("span",[e._v(" "+e._s(e._f("timeFormat")(i.InStoreDate))+" ")]):"RawName"===t.Code?[a("div",[i.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),i.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),i.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(i.RawName))],1)]:"TaxUnitPrice"===t.Code?a("span",[e._v(" "+e._s(i[t.Code]||0===i[t.Code]?i[t.Code].toFixed(2):"")+" ")]):a("span",[e._v(" "+e._s(e._f("displayValue")(i[t.Code])))])]}},t.Is_Edit?{key:"edit",fn:function(r){var i=r.row;return["Actual_Spec"===t.Code?a("div",[a("vxe-input",{attrs:{type:"text"},on:{change:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})],1):["ReturnPoundWeight","ReturnVoucherWeight"].includes(t.Code)?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"ReturnCount"===t.Code?a("div",[a("vxe-input",{attrs:{min:0,max:i.OutStoreCount,type:"number"},on:{change:function(t){return e.checkOutWeight(i)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):["ReturnWidth","ReturnLength"].includes(t.Code)?a("div",[a("vxe-input",{attrs:{min:0,type:"number"},on:{change:function(t){return e.checkOutWeight(i)}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})],1):"Warehouse_Location_Return"===t.Code?a("span",[e._v(" "+e._s(i.ReturnWarehoueseName)+"/"+e._s(i.ReturnLocationName)+" "),a("i",{staticClass:"el-icon-edit pointer",on:{click:function(t){return e.handleWarehouse(i)}}})]):a("div",[a("vxe-input",{attrs:{type:"text"},on:{blur:function(t){return e.$emit("updateRow")}},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,a)},expression:"row[item.Code]"}})],1)]}}:null],null,!0)})]}))],2)],1),e.isView?e._e():a("footer",[e._t("default")],2),e.dialogVisible?a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"选择仓库/库位",visible:e.dialogVisible,width:"30%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?a("Warehouse",{attrs:{"page-type":0},on:{close:e.handleClose,warehouse:e.getWarehouse}}):e._e()],1):e._e()],1)},i=[]},6992:function(e,t,a){},"6bdc":function(e,t,a){},"6f88":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("7db0"),a("c740"),a("caad"),a("d81d"),a("14d9"),a("b0c0"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a9e3"),a("b680"),a("b64b"),a("d3b7"),a("25f0"),a("2532"),a("159b");var i=r(a("2909")),n=r(a("c14f")),o=r(a("1da1")),s=a("5480"),l=a("2a6c"),u=r(a("ff46")),c=r(a("fc45")),d=r(a("bad9")),f=a("3166"),m=a("90d1"),h=r(a("a657")),p=r(a("98b2"));t.default={components:{PickSelect:p.default,DynamicTableFields:h.default,SelectProject:d.default,Warehouse:u.default,BatchEdit:c.default},filters:{getFormatNum:function(e,t){return(0,l.formatNum)(e,t)}},props:{isView:{type:Boolean,default:!1},isReturn:{type:Boolean,default:!1},isOutsourcing:{type:Boolean,default:!1}},data:function(){return{COUNT_DECIMAL:m.COUNT_DECIMAL,WEIGHT_DECIMAL:m.WEIGHT_DECIMAL,dialogVisible:!1,enabledEdit:!1,deleteLoading:!1,tbLoading:!1,currentTbData:[],columns:[],multipleSelection:[],bigTypeData:1,validRules:{OutStoreCount:[{required:!0,type:"number",min:0,message:"请输入"}],Pick_Sys_Project_Id:[{required:!0,type:"string",min:0,message:"请选择"}]},projectOptions:[],batchDialogVisible:!1,batchProjectId:"",excludedRoutes:["PRORawMaterialOutboundView"],gridCode:"PRORawReceiveOutList",renderComponent:!0}},mounted:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.getProject(),e.tbData=[],t.n=1,e.init();case 1:return t.a(2)}}),t)})))()},methods:{forceRerender:function(){var e=this;this.renderComponent=!1,this.$nextTick((function(){e.renderComponent=!0}))},closeBatchDialog:function(){this.batchProjectId="",this.batchDialogVisible=!1},batchChangeProject:function(){var e=this;this.multipleSelection.forEach((function(t,a){var r=e.tbData.find((function(e){return e.uuid===t.uuid})),i=e.tbData.findIndex((function(e){return e.uuid===t.uuid}));r.Pick_Sys_Project_Id=e.batchProjectId,e.$set(e.tbData,i,r)})),this.closeBatchDialog()},getProject:function(){var e=this;(0,f.GetProjectPageList)({Page:1,PageSize:-1}).then((function(t){t.IsSucceed?e.projectOptions=t.Data.Data:e.$message({message:t.Message,type:"error"})}))},handleClose:function(){this.dialogVisible=!1},getTaxUnitPrice:function(e){this.multipleSelection.forEach((function(t){t.TaxUnitPrice=e}))},init:function(){var e=this;return(0,o.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return e.enabledEdit=!e.isView,e.tbLoading=!0,t.n=1,(0,s.getTableConfig)(e.gridCode);case 1:e.columns=t.v,e.columns=e.columns.map((function(e){return e.Style=e.Style?JSON.parse(e.Style):"",e})),e.forceRerender(),e.tbLoading=!1;case 2:return t.a(2)}}),t)})))()},setTbData:function(e,t,a){var r,n=this;e.forEach((function(e){n.checkCount(e,t),e.Pick_Project_Name=e.Project_Name})),(r=this.tbData).push.apply(r,(0,i.default)(e)),this.filterMethod(),this.$emit("setInfo",a)},changeProject:function(e,t){var a;t.Pick_Project_Name=null===(a=this.projectOptions.find((function(t){return t.Sys_Project_Id===e.value})))||void 0===a?void 0:a.Short_Name},tbSelectChange:function(e){this.multipleSelection=e.records},checkWeight:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.excludedRoutes.includes(this.$route.name)||(e.OutStoreWeight=(e.Unit_Weight*e.OutStoreCount).toFixed(m.WEIGHT_DECIMAL)/1,e.OutStoreWeight>=e.AvailableWeight&&t&&(e.OutStoreWeight=e.AvailableWeight,e.OutStoreCount=e.AvailableCount))},checkCount:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(e.OutStoreCount>=e.AvailableCount&&t)return e.OutStoreCount=e.AvailableCount,void(e.OutStoreWeight=e.AvailableWeight);this.checkWeight(e,!1)},getTbData:function(){return this.tbData},openAddDialog:function(){this.$emit("openAddDialog")},handleDelete:function(){var e=this;this.deleteLoading=!0,setTimeout((function(){var t=e.multipleSelection.map((function(e){return e.uuid}));e.tbData=e.tbData.filter((function(e){return!t.includes(e.uuid)})),e.multipleSelection=[],e.deleteLoading=!1,e.filterMethod()}),0)},BulkEdit:function(){this.dialogVisible=!0},clearTb:function(){this.tbData=[],this.filterMethod()},mergeData:function(){this.currentTbData=this.tbData},filterMethod:function(e){var t=function(e,t){return e.filter((function(e){for(var a=!0,r=0;r<t.length;r++){var i=t[r],n=e[i.key]||"";if(""===i.value&&(a=!0),"string"!==typeof n&&(n=n.toString()),!n.includes(i.value)){a=!1;break}a=!0}return a}))};if(e){var a=[];for(var r in e)a.push({key:r,value:e[r]});this.currentTbData=t(this.tbData,a)}else this.currentTbData=this.tbData},checkValidate:function(e){if(!e.length)return this.$message({message:"数据不能为空",type:"warning"}),{status:!1};var t=e.filter((function(e){return e.OutStoreCount>0}));return t.length?void 0:(this.$message({message:"出库数量不能为0",type:"warning"}),{status:!1})},footerMethod:function(e){var t=this,a=e.columns,r=e.data,i=[a.map((function(e,a){return m.OutBOUND_DETAIL_SUMMARY_FIELDS.includes(e.field)?t.sumNum(r,e.field,5):0===a?"合计":null}))];return i},sumNum:function(e,t,a){for(var r=0,i=0;i<e.length;i++)r+=Number(e[i][t])||0;return r.toFixed(a)/1}}}},"747a":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("14d9");var i=a("93aa"),n=r(a("29d9")),o=r(a("b5b0"));t.default={name:"DetailSearch",components:{SelectLocation:o.default,SelectWarehouse:n.default},data:function(){return{treeParams:{data:[],filterable:!1,clickParent:!0,props:{children:"Children",label:"Label",value:"Label"}},filterVisible:!1,partyUnitList:[],supplierList:[],searchForm:{Raw_FullName:"",RawName:"",Spec:"",Material:"",CategoryName:"",Length:"",Width:"",SupplierName:"",PartyUnitName:"",WarehouseId:"",LocationId:""}}},mounted:function(){this.getBaseInfo()},methods:{init:function(){this.$emit("initGrid")},getBaseInfo:function(){var e=this,t=function(){(0,i.GetSuppliers)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var r in t.Data){var i={Id:r,Name:t.Data[r]};a.push(i)}e.supplierList=a}else e.$message({message:t.Message,type:"error"})}))},a=function(){(0,i.GetPartyAs)({Page:1,PageSize:-1}).then((function(t){if(t.IsSucceed){var a=[];for(var r in t.Data){var i={Id:r,Name:t.Data[r]};a.push(i)}e.partyUnitList=a}else e.$message({message:t.Message,type:"error"})}))},r=function(){(0,i.GetCategoryTreeList)({}).then((function(t){t.IsSucceed?(e.treeParams.data=t.Data,e.$refs.treeSelect.treeDataUpdateFun(t.Data)):e.$message({message:t.Message,type:"error"})}))};t(),r(),a()},handleSearch:function(){this.$emit("detailInfo",this.searchForm)},handleReset:function(){this.$refs["searchAllForm"].resetFields(),this.$emit("detailInfo",this.searchForm)}}}},"753d":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{input:void 0,bloading:!1}},methods:{submit:function(){void 0!==this.input?(this.$emit("taxUnitPrice",this.input),this.$emit("close")):this.$message({message:"单价不能为空",type:"warning"})}}}},7568:function(e,t,a){"use strict";a.r(t);var r=a("48cc"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},7600:function(e,t,a){"use strict";a.r(t);var r=a("eb98"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},"773b":function(e,t,a){"use strict";a("6992")},"7ae8":function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("caad"),a("14d9"),a("e9f5"),a("910d"),a("a732"),a("d3b7"),a("25f0"),a("2532");var i=r(a("c14f")),n=r(a("1da1")),o=a("5480"),s=r(a("ff46")),l=a("2a6c"),u=r(a("a657"));t.default={components:{Warehouse:s.default,DynamicTableFields:u.default},filters:{getFormatNum:function(e,t){return(0,l.formatNum)(e,t)}},props:{isView:{type:Boolean,default:!1},isReturn:{type:Boolean,default:!1}},data:function(){return{dialogVisible:!1,enabledEdit:!1,tbLoading:!1,tbData:[],columns:[],validRules:{ReturnCount:[{required:!0,type:"number",min:0,message:"请输入"}],Warehouse_Location_Return:[{required:!0,message:"请选择"}]},currentTbData:[],gridCode:"PRORawReceiveOutReturnList"}},mounted:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.init();case 1:return t.a(2)}}),t)})))()},methods:{filterMethod:function(e){var t=function(e,t){return e.filter((function(e){for(var a=!0,r=0;r<t.length;r++){var i=t[r],n=e[i.key]||"";if(""===i.value&&(a=!0),"string"!==typeof n&&(n=n.toString()),!n.includes(i.value)){a=!1;break}a=!0}return a}))};if(e){var a=[];for(var r in e)a.push({key:r,value:e[r]});this.currentTbData=t(this.tbData,a)}else this.currentTbData=this.tbData},getSummaryInfo:function(){var e=(0,l.getSummaryInfo)(this.tbData);this.$emit("getSummary",e)},init:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:return e.enabledEdit=!e.isView,e.tbLoading=!0,t.n=1,(0,o.getTableConfig)("PRORawReceiveOutReturnList");case 1:e.columns=t.v,e.tbLoading=!1;case 2:return t.a(2)}}),t)})))()},setData:function(e){this.tbData=e,this.filterMethod(),this.getSummaryInfo()},getTbData:function(){return this.tbData},checkTbDate:function(){var e=this;return(0,n.default)((0,i.default)().m((function t(){var a;return(0,i.default)().w((function(t){while(1)switch(t.n){case 0:if(a=e.tbData.filter((function(e){return e.ReturnCount})),a.length){t.n=1;break}return e.$message({message:"退库数量不能为空",type:"warning"}),t.a(2,!1);case 1:if(!a.some((function(e){return!e.Warehouse_Location_Return}))){t.n=2;break}return e.$message({message:"退库仓库/库位不能为空",type:"warning"}),t.a(2,!1);case 2:return t.a(2,!0)}}),t)})))()},handleWarehouse:function(e){this.currentRow=e,this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},getWarehouse:function(e){var t=e.warehouse,a=e.location;this.currentRow&&(this.currentRow.ReturnWarehoueseId=t.Id,this.currentRow.ReturnLocationId=a.Id,this.$set(this.currentRow,"ReturnWarehoueseName",t.Display_Name),this.$set(this.currentRow,"ReturnLocationName",a.Display_Name),this.$set(this.currentRow,"Warehouse_Location_Return",t.Display_Name+"/"+a.Display_Name)),this.handleClose()},checkOutWeight:function(e){var t=e.ReturnCount,a=e.ReturnWidth,r=e.ReturnLength,i=e.PerWeight,n=e.Specific_Gravity,o=e.Spec,s=1;s=n?1===e.BigType?"花纹板"===e.CategoryName?t*a*r*n:t*a*r*n*o:2===e.BigType?t*r*n:t*i:t*i,e.ReturnStoreWeight=(0,l.getThousandValue)(s,5),e.ReturnVoucherWeight=(0,l.getThousandValue)(e.ReturnCount*e.PerVoucherWeight,5)}}}},8468:function(e,t,a){"use strict";function r(e){if(null==e)throw new TypeError("Cannot destructure "+e)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,a("d9e2")},"90d1":function(e,t){e.exports={WEIGHT_DECIMAL:5,INBOUND_DETAIL_UNIT_PRICE_DECIMAL:6,DETAIL_TOTAL_PRICE_DECIMAL:2,COUNT_DECIMAL:2,UNIT_WEIGHT_DECIMAL:100,TAX_MODE:0,SUMMARY_FIELDS:["Theory_Weight","InStoreWeight","Voucher_Weight","InStoreCount","TaxTotalPrice","NoTaxTotalPrice","TaxPrice"],INBOUND_DETAIL_HIDE_FIELDS:{isPurchase:["PartyUnitName"],isCustomer:["PurchaseNo","SupplierName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"],isManual:["PurchaseNo","PartyUnitName","OrderTaxUnitPrice","OrderNoTaxUnitPrice"]},INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS:["SupplierName","ProjectName","Material","Tax_Rate","NoTaxUnitPrice","TaxUnitPrice"],INBOUND_DETAIL_SUMMARY_FIELDS:["InStoreCount","InStoreWeight","Voucher_Weight","NoTaxAllPrice","Tax_All_Price","Adjust_Amount","Tax","Theory_Weight"],OutBOUND_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","AvailableCount","AvailableWeight","NoTaxAllPrice","Tax_All_Price","Out_Store_Weight","Out_Store_Count","Returned_Weight","Returned_Count","InStoreCount","InStoreWeight"],Return_DETAIL_SUMMARY_FIELDS:["OutStoreCount","OutStoreWeight","NoTaxAllPrice","Tax_All_Price","AvailableCount","AvailableWeight","Voucher_Weight"]}},"91ad":function(e,t,a){"use strict";a("cfeb")},"9e84":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"contentBox"},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{clearable:"",placeholder:"通配符%",type:"text"},model:{value:e.form.Raw_FullName,callback:function(t){e.$set(e.form,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"form.Raw_FullName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料名称:",prop:"RawName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.RawName,callback:function(t){e.$set(e.form,"RawName","string"===typeof t?t.trim():t)},expression:"form.RawName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料分类",prop:"Category_Id"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.form.Category_Id,callback:function(t){e.$set(e.form,"Category_Id",t)},expression:"form.Category_Id"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"材质:",prop:"Material"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Material,callback:function(t){e.$set(e.form,"Material","string"===typeof t?t.trim():t)},expression:"form.Material"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"供应商:",prop:"SupplierName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.SupplierName,callback:function(t){e.$set(e.form,"SupplierName","string"===typeof t?t.trim():t)},expression:"form.SupplierName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"甲方单位:",prop:"PartyUnitName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.PartyUnitName,callback:function(t){e.$set(e.form,"PartyUnitName","string"===typeof t?t.trim():t)},expression:"form.PartyUnitName"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"原料属性",prop:"Raw_Property"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择"},model:{value:e.form.Raw_Property,callback:function(t){e.$set(e.form,"Raw_Property",t)},expression:"form.Raw_Property"}},[a("el-option",{attrs:{label:"自采",value:1}}),a("el-option",{attrs:{label:"甲供",value:2}}),a("el-option",{attrs:{label:"代购",value:3}}),a("el-option",{attrs:{label:"余料",value:4}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"规格:",prop:"Spec"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入",type:"text"},model:{value:e.form.Spec,callback:function(t){e.$set(e.form,"Spec","string"===typeof t?t.trim():t)},expression:"form.Spec"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"理论厚度:",prop:"Thick"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{clearable:"",placeholder:"请输入"},model:{value:e.form.Thick,callback:function(t){e.$set(e.form,"Thick",t)},expression:"form.Thick"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{clearable:"",placeholder:"宽度"},model:{value:e.form.Width,callback:function(t){e.$set(e.form,"Width",t)},expression:"form.Width"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input-number",{staticClass:"cs-number-btn-hidden w100",attrs:{clearable:"",placeholder:"长度"},model:{value:e.form.Length,callback:function(t){e.$set(e.form,"Length",t)},expression:"form.Length"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"所属项目",prop:"SysProjectId"}},[a("SelectProject",{model:{value:e.form.SysProjectId,callback:function(t){e.$set(e.form,"SysProjectId",t)},expression:"form.SysProjectId"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"仓库",prop:"Warehouse"}},[a("SelectWarehouse",{model:{value:e.form.Warehouse,callback:function(t){e.$set(e.form,"Warehouse",t)},expression:"form.Warehouse"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"库位",prop:"Location"}},[a("SelectLocation",{attrs:{"warehouse-id":e.form.Warehouse},model:{value:e.form.Location,callback:function(t){e.$set(e.form,"Location",t)},expression:"form.Location"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{staticClass:"last-btn",attrs:{"label-width":"10px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{on:{click:function(t){return e.resetForm("form")}}},[e._v("重置")]),a("el-button",{attrs:{disabled:!e.multipleSelection.length,loading:e.addLoading,type:"primary"},on:{click:function(t){return e.addToList(!1)}}},[e._v("加入列表")])],1)],1)],1)],1),a("div",{staticClass:"tb-wrapper"},[a("vxe-table",{ref:"xTable1",staticClass:"cs-vxe-table",attrs:{"empty-render":{name:"NotData"},"show-header-overflow":"","empty-text":"暂无数据",height:"auto","show-overflow":"",loading:e.tbLoading,"row-config":{isCurrent:!0,isHover:!0},"radio-config":{highlight:!0},align:"left",stripe:"",data:e.fTable,resizable:"","tooltip-config":{enterable:!0}},on:{"checkbox-all":e.tbSelectChange,"checkbox-change":e.tbSelectChange}},[a("vxe-column",{attrs:{fixed:"left",type:"checkbox",width:"60"}}),e._l(e.columns,(function(t){return[a("vxe-column",{key:t.Code,attrs:{fixed:t.Is_Frozen?"right":"","show-overflow":"tooltip",align:t.Align,field:t.Code,visible:t.Is_Display,title:t.Display_Name,"min-width":t.Width},scopedSlots:e._u(["InStoreDate"===t.Code?{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("timeFormat")(r[t.Code],"{y}-{m}-{d}"))+" ")]}}:"Warehouse_Location"===t.Code?{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.WarehouseName)+"/"+e._s(a.LocationName)+" ")]}}:"OutStoreCount"===t.Code?{key:"default",fn:function(r){var i=r.row;return[a("vxe-input",{attrs:{min:0,max:i.AvailableCount,type:"number"},model:{value:i[t.Code],callback:function(a){e.$set(i,t.Code,e._n(a))},expression:"row[item.Code]"}})]}}:"RawName"===t.Code?{key:"default",fn:function(t){var r=t.row;return[a("div",[r.Is_PartA?a("el-tag",{attrs:{type:"danger",effect:"dark",size:"mini"}},[e._v("甲供")]):e._e(),r.Is_Replace_Purchase?a("el-tag",{attrs:{type:"success",effect:"dark",size:"mini"}},[e._v("代购")]):e._e(),r.Is_Surplus?a("el-tag",{attrs:{type:"warning",effect:"dark",size:"mini"}},[e._v("余料")]):e._e(),e._v(" "+e._s(r.RawName)+" ")],1)]}}:{key:"default",fn:function(a){var r=a.row;return[e._v(" "+e._s(e._f("displayValue")(r[t.Code]))+" ")]}}],null,!0)})]}))],2)],1),a("div",{staticClass:"button"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",disabled:!e.multipleSelection.length,loading:e.saveLoading},on:{click:e.handleSave}},[e._v("保存 ")])],1)],1)},i=[]},a036:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-input-number",{staticClass:"w100 cs-number-btn-hidden ",attrs:{min:0,placeholder:"请输入",clearable:""},model:{value:e.input,callback:function(t){e.input=t},expression:"input"}}),a("footer",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.bloading},on:{click:e.submit}},[e._v("确 认")])],1)],1)},i=[]},a927:function(e,t,a){},ac6b:function(e,t,a){"use strict";var r=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.ExportTeamProcessingTask=p,t.FindMatBillSumPageList=g,t.GetAreaPageList=u,t.GetCompanyFactoryPageList=d,t.GetEntities=s,t.GetFactoryPageList=c,t.GetGetMonomerList=l,t.GetMatBillSumSubList=v,t.GetProcessingProgress=n,t.GetProcessingProgressTask=o,t.GetSummaryTeamProcessingTask=h,t.GetTeamProcessingTask=f,t.GetWorkingTeams=m;var i=r(a("b775"));function n(e){return(0,i.default)({url:"/PRO/ProductionReport/GetProcessingProgress",method:"post",data:e})}function o(e){return(0,i.default)({url:"/PRO/ProductionReport/GetProcessingProgressTask",method:"post",data:e})}function s(e){return(0,i.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_projects/GetEntities"),method:"post",data:e})}function l(e){return(0,i.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetGetMonomerList"),method:"post",data:e})}function u(e){return(0,i.default)({url:"".concat(document.querySelector("html").dataset.promiseProjectBaseUrl,"PLM/Plm_Project_Areas/GetAreaPageList"),method:"post",data:e})}function c(e){return(0,i.default)({url:"/PRO/Factory/GetFactoryPageList",method:"post",data:e})}function d(e){return(0,i.default)({url:"/PRO/Factory/GetCompanyFactoryPageList",method:"post",data:e})}function f(e){return(0,i.default)({url:"/PRO/ProductionReport/GetTeamProcessingTask",method:"post",data:e})}function m(e){return(0,i.default)({url:"/PRO/ProductionReport/GetWorkingTeams",method:"post",data:e})}function h(e){return(0,i.default)({url:"/PRO/ProductionReport/GetSummaryTeamProcessingTask",method:"post",data:e})}function p(e){return(0,i.default)({url:"/PRO/ProductionReport/ExportTeamProcessingTask",method:"post",data:e})}function g(e){return(0,i.default)({url:"/PRO/ProductionReport/FindMatBillSumPageList",method:"post",data:e})}function v(e){return(0,i.default)({url:"/PRO/ProductionReport/GetMatBillSumSubList",method:"post",data:e})}},b047:function(e,t,a){},c04a:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container abs100"},[a("el-card",{staticClass:"box-card"},[a("InfoSearch",{ref:"infoRef",attrs:{"is-view":e.isView,"is-edit":e.isEdit,"is-return":e.isReturn},on:{typeChange:e.typeChange,initTb:e.initTb}})],1),a("el-card",{staticClass:"box-card cs-main"},[a("div",{staticClass:"toolbar-container"},[a("div",{staticClass:"toolbar-title"},[e._v("出库单明细")])]),a("div",[a("el-divider",{staticClass:"cs-divider "})],1),a("div",{staticClass:"search-x"},[a("DetailSearch",{ref:"detailSearchRef",on:{detailInfo:e.getDetailInfo,initGrid:e.initGrid}})],1),a("el-divider",{staticClass:"cs-divider mt-0"}),a("div",{staticClass:"tb-wrapper"},[e.isReturn?a("ReturnedTb",{ref:"returnTbRef",attrs:{"is-view":e.isView},on:{changeWarehouse:e.changeWarehouse}},[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{loading:e.returning,type:"primary"},on:{click:e.handleReturn}},[e._v("确认退库")])],1):a("ReceiveTb",{ref:"receiveTbRef",attrs:{"is-outsourcing":e.isOutsourcing,"is-view":e.isView},on:{openAddDialog:e.addNew,setInfo:e.setInfo}},[a("el-button",{on:{click:e.closeView}},[e._v("取消")]),a("el-button",{attrs:{loading:e.saveLoading},on:{click:e.saveDraft}},[e._v("保存草稿")]),a("el-button",{attrs:{loading:e.submitLoading,type:"primary"},on:{click:e.handleSubmit}},[e._v("提交出库")])],1)],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"plm-custom-dialog",attrs:{title:"新增",visible:e.showDialog,width:"70%",top:"10vh"},on:{"update:visible":function(t){e.showDialog=t},close:e.handleClose}},[e.showDialog?[a("DialogInfo",{ref:"draft",attrs:{"form-data":e.formData,"joined-items":e.tbData},on:{getAddList:e.getAddList,close:e.handleClose}})]:e._e()],2)],1)},i=[]},c2ed:function(e,t,a){},c540:function(e,t,a){"use strict";a.r(t);var r=a("183b"),i=a("280f");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("773b");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"f0c528c0",null);t["default"]=s.exports},c602:function(e,t,a){"use strict";a.r(t);var r=a("d965"),i=a("52a9");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"a4b4c45a",null);t["default"]=s.exports},c7d4:function(e,t,a){"use strict";a.r(t);var r=a("753d"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},c7ed:function(e,t,a){"use strict";a.r(t);var r=a("5d35"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},ccb5:function(e,t,a){"use strict";function r(e,t){if(null==e)return{};var a={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;a[r]=e[r]}return a}Object.defineProperty(t,"__esModule",{value:!0}),t.default=r},cfeb:function(e,t,a){},d069:function(e,t,a){"use strict";a.r(t);var r=a("7ae8"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},d965:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"searchAllForm",staticClass:"search-form",attrs:{inline:"",model:e.searchForm}},[a("el-form-item",{staticClass:"cs-item",attrs:{label:"原料全名",prop:"Raw_FullName"}},[a("el-input",{attrs:{placeholder:"通配符%",clearable:""},model:{value:e.searchForm.Raw_FullName,callback:function(t){e.$set(e.searchForm,"Raw_FullName","string"===typeof t?t.trim():t)},expression:"searchForm.Raw_FullName"}})],1),a("el-form-item",{attrs:{label:"原料名称",prop:"RawName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.RawName,callback:function(t){e.$set(e.searchForm,"RawName",t)},expression:"searchForm.RawName"}})],1),a("el-form-item",{attrs:{label:"规格/厚度(理论)",prop:"Spec","label-width":"120px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Spec,callback:function(t){e.$set(e.searchForm,"Spec",t)},expression:"searchForm.Spec"}})],1),a("el-form-item",{attrs:{label:"材质",prop:"Material"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Material,callback:function(t){e.$set(e.searchForm,"Material",t)},expression:"searchForm.Material"}})],1),a("transition",{attrs:{name:"fade"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.filterVisible,expression:"filterVisible"}]},[a("el-form-item",{attrs:{label:"原料分类",prop:"CategoryName"}},[a("el-tree-select",{ref:"treeSelect",staticClass:"input",attrs:{"tree-params":e.treeParams},model:{value:e.searchForm.CategoryName,callback:function(t){e.$set(e.searchForm,"CategoryName",t)},expression:"searchForm.CategoryName"}})],1),a("el-form-item",{attrs:{label:"宽度",prop:"Width"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Width,callback:function(t){e.$set(e.searchForm,"Width",t)},expression:"searchForm.Width"}})],1),a("el-form-item",{attrs:{label:"长度",prop:"Length"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入",clearable:""},model:{value:e.searchForm.Length,callback:function(t){e.$set(e.searchForm,"Length",t)},expression:"searchForm.Length"}})],1),a("el-form-item",{attrs:{label:"供应商",prop:"SupplierName"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商",clearable:"",filterable:""},model:{value:e.searchForm.SupplierName,callback:function(t){e.$set(e.searchForm,"SupplierName",t)},expression:"searchForm.SupplierName"}},e._l(e.supplierList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"甲方单位",prop:"PartyUnitName"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"甲方单位",clearable:"",filterable:""},model:{value:e.searchForm.PartyUnitName,callback:function(t){e.$set(e.searchForm,"PartyUnitName",t)},expression:"searchForm.PartyUnitName"}},e._l(e.partyUnitList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Name}})})),1)],1),a("el-form-item",{attrs:{label:"仓库",prop:"WarehouseId"}},[a("SelectWarehouse",{model:{value:e.searchForm.WarehouseId,callback:function(t){e.$set(e.searchForm,"WarehouseId",t)},expression:"searchForm.WarehouseId"}})],1),a("el-form-item",{attrs:{label:"库位",prop:"LocationId"}},[a("SelectLocation",{attrs:{"warehouse-id":e.searchForm.WarehouseId},model:{value:e.searchForm.LocationId,callback:function(t){e.$set(e.searchForm,"LocationId",t)},expression:"searchForm.LocationId"}})],1)],1)]),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")]),a("el-button",{on:{click:function(t){e.filterVisible=!e.filterVisible}}},[e._v(e._s(e.filterVisible?"收起筛选":"展开筛选"))])],1)],1)},i=[]},dfb1:function(e,t,a){"use strict";a.r(t);var r=a("9e84"),i=a("7568");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("15f6");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"6712c71d",null);t["default"]=s.exports},e231:function(e,t,a){"use strict";a.r(t);var r=a("69071"),i=a("d069");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("6104");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"6c6a6926",null);t["default"]=s.exports},e880:function(e,t,a){},eb98:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a("2a6c");t.default={name:"SummaryInfo",props:{statisticsData:{type:Object,default:function(){return{OutStoreCountTotal:0,OutStoreWeightTotal:0,PoundWeightTotal:0,VoucherWeightTotal:0,includingTax:0,excludingTax:0}}}},methods:{formatNum:function(e,t){return(0,r.formatNum)(e,t)}}}},f3fc:function(e,t,a){"use strict";a.r(t);var r=a("6f88"),i=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=i.a},f6d3:function(e,t,a){"use strict";a("c2ed")},fc45:function(e,t,a){"use strict";a.r(t);var r=a("a036"),i=a("c7d4");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("1981");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"2a398a03",null);t["default"]=s.exports},ff46:function(e,t,a){"use strict";a.r(t);var r=a("6836"),i=a("c7ed");for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);a("91ad");var o=a("2877"),s=Object(o["a"])(i["default"],r["a"],r["b"],!1,null,"a81bfb86",null);t["default"]=s.exports}}]);