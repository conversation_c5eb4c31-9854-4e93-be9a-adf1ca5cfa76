(window["webpackJsonp_produce"]=window["webpackJsonp_produce"]||[]).push([["chunk-344d1cc2"],{"15ac":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("4de4"),a("d81d"),a("14d9"),a("e9f5"),a("910d"),a("ab43"),a("d3b7");var o=a("6186"),n=a("c685");t.default={methods:{getTableConfig:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise((function(r){(0,o.GetGridByCode)({code:e,IsAll:a}).then((function(e){var o=e.IsSucceed,i=e.Data,s=e.Message;if(o){if(!i)return void t.$message({message:"表格配置不存在",type:"error"});var l=[];t.tbConfig=Object.assign({},t.tbConfig,i.Grid),l=a?(null===i||void 0===i?void 0:i.ColumnList)||[]:(null===i||void 0===i?void 0:i.ColumnList.filter((function(e){return e.Is_Display})))||[],t.columns=l.map((function(e){return e.Is_Resizable=!0,e.Is_Sortable=!0,e.Align=e.Align||"left",e})),t.queryInfo&&(t.queryInfo.PageSize=+i.Grid.Row_Number||n.tablePageSize[0]),r(t.columns)}else t.$message({message:s,type:"error"})}))}))},handlePageChange:function(e){var t=e.page,a=e.size;this.tbConfig.Row_Number=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.PageSize=parseInt(a||this.tbConfig.Row_Number),this.queryInfo.Page=a?1:t,this.fetchData()},pageChange:function(e){var t=e.page,a=e.limit,o=e.type;this.queryInfo.Page="limit"===o?1:t,this.queryInfo.PageSize=a,this.fetchData()},tableSearch:function(e){for(var t in this.queryInfo.ParameterJson=[],e){var a={Key:t,Type:"",Filter_Type:""};e[t]instanceof Array?a.Value=e[t]:a.Value=[e[t]];for(var o=0;o<this.columns.length;o++){var n=this.columns[o];if(n.Code===t){a.Type=n.Type,a.Filter_Type=n.Filter_Type;break}}e[t]&&e[t].length&&this.queryInfo.ParameterJson.push(a)}this.queryInfo.Page=1,this.fetchData()},showSearchBtn:function(e){this.$refs.dyTable.showSearch()},columnSearchChange:function(){this.showSearchBtn()}}}},"1c46":function(e,t,a){},"1d94":function(e,t,a){"use strict";a("34a0")},2426:function(e,t,a){"use strict";a.r(t);var o=a("64bb"),n=a("9b61");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("1d94");var i=a("2877"),s=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"ab7b65b8",null);t["default"]=s.exports},"34a0":function(e,t,a){},"4e82":function(e,t,a){"use strict";var o=a("23e7"),n=a("e330"),r=a("59ed"),i=a("7b0b"),s=a("07fa"),l=a("083a"),u=a("577e"),c=a("d039"),d=a("addb"),f=a("a640"),p=a("3f7e"),h=a("99f4"),m=a("1212"),y=a("ea83"),g=[],b=n(g.sort),P=n(g.push),_=c((function(){g.sort(void 0)})),v=c((function(){g.sort(null)})),C=f("sort"),k=!c((function(){if(m)return m<70;if(!(p&&p>3)){if(h)return!0;if(y)return y<603;var e,t,a,o,n="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(o=0;o<47;o++)g.push({k:t+o,v:a})}for(g.sort((function(e,t){return t.v-e.v})),o=0;o<g.length;o++)t=g[o].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}})),T=_||!v||!C||!k,D=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:u(t)>u(a)?1:-1}};o({target:"Array",proto:!0,forced:T},{sort:function(e){void 0!==e&&r(e);var t=i(this);if(k)return void 0===e?b(t):b(t,e);var a,o,n=[],u=s(t);for(o=0;o<u;o++)o in t&&P(n,t[o]);d(n,D(e)),a=s(n),o=0;while(o<a)t[o]=n[o++];while(o<u)l(t,o++);return t}})},"64bb":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"abs100 cs-z-flex-pd16-wrap"},[a("div",{staticClass:"cs-z-page-main-content"},[a("top-header",{attrs:{padding:"0"},scopedSlots:e._u([{key:"right",fn:function(){return[a("el-form",{attrs:{"label-width":"80px",inline:!0}},[a("el-form-item",{attrs:{label:"工序"}},[a("el-input",{attrs:{placeholder:"请输入",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.keywords,callback:function(t){e.keywords=t},expression:"keywords"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")]),a("el-button",{on:{click:e.reset}},[e._v("重置")])],1)],1)]},proxy:!0},{key:"left",fn:function(){return[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增定额")]),a("el-button",{attrs:{type:"danger",disabled:!e.selectList.length},on:{click:function(t){return e.handleDelete(!0)}}},[e._v("批量删除")])]},proxy:!0}])}),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.tbLoading,expression:"tbLoading"}],staticClass:"fff cs-z-tb-wrapper"},[a("dynamic-data-table",{ref:"dyTable",staticClass:"cs-plm-dy-table",attrs:{columns:e.columns,config:e.tbConfig,data:e.tbData,page:e.queryInfo.Page,total:e.total,border:"",stripe:""},on:{multiSelectedChange:e.handleSelectionChange,gridPageChange:e.handlePageChange,gridSizeChange:e.handlePageChange,tableSearch:e.tableSearch},scopedSlots:e._u([{key:"Working_Process_Type",fn:function(t){var o=t.row;return[a("div",[e._v(e._s(1===o.Working_Process_Type?"构件工序":"零件工序"))])]}},{key:"Valuation_Unit",fn:function(t){var o=t.row;return[a("div",[e._v(e._s(1===o.Valuation_Unit?"重量(t)":2===o.Valuation_Unit?"长度(m)":"数量(件)"))])]}},{key:"op",fn:function(t){var o=t.row;return[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(o)}}},[e._v("编辑")]),a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(t){return e.handleDelete(!1,o)}}},[e._v("删除")])]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"}],staticClass:"cs-dialog",attrs:{title:e.title,visible:e.dialogVisible,"custom-class":"dialogCustomClass",width:"40%",top:"10vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[e.dialogVisible?a(e.currentComponent,{ref:"content",tag:"component",attrs:{"dialog-visible":e.dialogVisible},on:{close:e.handleClose,refresh:e.fetchData}}):e._e()],1)],1)},n=[]},"6e33":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("7db0"),a("a15b"),a("d81d"),a("14d9"),a("a434"),a("e9f5"),a("f665"),a("ab43"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7");var n=o(a("5530")),r=o(a("c14f")),i=o(a("1da1")),s=a("a024"),l=o(a("d7a3")),u=a("7de9"),c=a("f4e9");a("d7b0"),t.default={mixins:[l.default],data:function(){return{isEdit:!1,isValuationUnitDisable:!1,form:{Id:"",Working_Process_Type:"",Working_Process_Id:"",Valuation_Unit:"",Unit_Price:"",Remark:""},rules:{Working_Process_Type:[{required:!0,message:"请选择",trigger:"change"}],Working_Process_Id:[{required:!0,message:"请选择",trigger:"change"}],Valuation_Unit:[{required:!0,message:"请选择",trigger:"change"}],Unit_Price:[{required:!0,message:"请输入",trigger:"blur"}]},treeSelectParams:{placeholder:"请选择"},ObjectTypeList:{"check-strictly":!0,"default-expand-all":!0,filterable:!0,clickParent:!0,data:[],props:{children:"Children",label:"Label",value:"Data",Id:"Id"}},variableData:[],processList:[]}},created:function(){var e=this;return(0,i.default)((0,r.default)().m((function t(){return(0,r.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getFactoryTypeOption();case 1:return t.n=2,e.getBaseData();case 2:return t.a(2)}}),t)})))()},methods:{initData:function(e){var t=this;this.isEdit=!0,(0,c.GetQuotaDetail)({Id:e.Id}).then((function(e){if(e.IsSucceed){var a=e.Data.Header,o=a.Id,n=a.Working_Process_Type,r=a.Working_Process_Id,i=a.Valuation_Unit,s=a.Unit_Price,l=a.Remark;t.form={Id:o,Working_Process_Type:n,Working_Process_Id:r,Valuation_Unit:i,Unit_Price:s,Remark:l},setTimeout((function(){e.Data.Item_List.map((function(e){delete e["Detail_Id"],delete e["Salary_Quota_Id"],6===e.Custome_Symbol?e.Custome_Value=e.Custome_Value.split(","):e.Custome_Value=Number(e.Custome_Value),t.variableData.push(e)}))}),500),t.processTypeChange()}else t.$message({message:e.Message,type:"error"})}))},getBaseData:function(){var e=this;(0,u.GetCompTypeTree)({professional:"Steel"}).then((function(t){t.IsSucceed?e.ObjectTypeList.data=t.Data:e.$message({type:"error",message:t.Message})}))},managerChange:function(e){},processTypeChange:function(){var e=this;this.isEdit||(this.form.Working_Process_Id=""),this.processChange(""),this.form.Working_Process_Type&&(0,s.GetProcessListBase)({type:this.form.Working_Process_Type}).then((function(t){t.IsSucceed?e.processList=t.Data:e.$message({message:t.Message,type:"error"})}))},processChange:function(e){var t=this;this.isValuationUnitDisable=!1,e&&(0,c.GetQuotaPageList)({Working_Process_Id:e,PageSize:-1}).then((function(e){e.IsSucceed?e.Data.Data.length>0?(t.form.Valuation_Unit=e.Data.Data[0].Valuation_Unit,t.isValuationUnitDisable=!0):t.form.Valuation_Unit="":t.$message({message:e.Message,type:"error"})}))},_searchFun:function(e,t){this.$refs["treeSelectObjectType"+t][0].filterFun(e)},changeCustomeKey:function(e,t){4===e?(this.variableData[t].Custome_Symbol=6,this.variableData[t].Custome_Value=[]):(this.variableData[t].Custome_Symbol="",this.variableData[t].Custome_Value="")},handleAdd:function(){this.variableData.length>=10?this.$message({message:"变量最多10个",type:"warning"}):this.variableData.push({Custome_Key:"",Custome_Symbol:"",Custome_Value:""})},handleDele:function(e){this.variableData.splice(e,1)},handleSubmit:function(){var e=this;this.$refs.form.validate((function(t){if(!t)return!1;var a=(0,n.default)({},e.form),o=e.variableData.find((function(e){return!e.Custome_Key||!e.Custome_Symbol||!e.Custome_Value}));if(o)e.$message({message:"请把变量补充完整",type:"warning"});else{a.Unit_Price=Number(a.Unit_Price);var r=JSON.parse(JSON.stringify(e.variableData));r.map((function(e){6===e.Custome_Symbol&&(e.Custome_Value=e.Custome_Value.join(","))})),(0,c.SaveQuotaEntity)((0,n.default)((0,n.default)({},a),{},{Detail_List:r})).then((function(t){t.IsSucceed?(e.$message({message:"保存成功",type:"success"}),e.$emit("close"),e.$emit("refresh")):e.$message({message:t.Message,type:"error"})}))}}))}}}},7196:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteWorkshop=u,t.GetFactoryPeoplelist=r,t.GetWorkshopEntity=l,t.GetWorkshopPageList=s,t.SaveEntity=i;var n=o(a("b775"));function r(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Workshop/SaveEntity",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopPageList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Workshop/GetWorkshopEntity",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Workshop/DeleteWorkshop",method:"post",data:e})}},"7de9":function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddCheckItem=p,t.AddCheckItemCombination=g,t.AddCheckType=l,t.DelNode=L,t.DelQualityList=k,t.DeleteCheckItem=f,t.DeleteCheckType=u,t.EntityCheckItem=h,t.EntityCheckType=i,t.EntityQualityList=C,t.ExportInspsectionSummaryInfo=R,t.GetCheckGroupList=y,t.GetCheckItemList=d,t.GetCheckTypeList=s,t.GetCompTypeTree=I,t.GetDictionaryDetailListByCode=r,t.GetEntityNode=D,t.GetFactoryPeoplelist=P,t.GetFactoryProfessionalByCode=O,t.GetMaterialType=S,t.GetNodeList=T,t.GetProEntities=b,t.GetProcessCodeList=_,t.QualityList=v,t.SaveCheckItem=m,t.SaveCheckType=c,t.SaveNode=G;var n=o(a("b775"));function r(e){return(0,n.default)({url:"/SYS/Dictionary/GetDictionaryDetailListByCode",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckType",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckTypeList",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckType",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckType",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckType",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckItemList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/Inspection/DeleteCheckItem",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItem",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/Inspection/EntityCheckItem",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/Inspection/SaveCheckItem",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function g(e){return(0,n.default)({url:"/PRO/Inspection/AddCheckItemCombination",method:"post",data:e})}function b(e){return(0,n.default)({url:"/PLM/Plm_Professional_Type/GetEntities",method:"post",data:e})}function P(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function _(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessCodeList",method:"post",data:e})}function v(e){return(0,n.default)({url:"/PRO/Inspection/QualityList",method:"post",data:e})}function C(e){return(0,n.default)({url:"/PRO/Inspection/EntityQualityList",method:"post",data:e})}function k(e){return(0,n.default)({url:"/PRO/Inspection/DelQualityList",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/Inspection/GetNodeList",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/Inspection/EntityNode",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/Inspection/DelNode",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/Inspection/SaveNode",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/ComponentType/GetCompTypeTree",method:"post",data:e})}function O(e){return(0,n.default)({url:"/PRO/ProfessionalType/GetFactoryProfessionalByCode",method:"post",data:e})}function S(e){return(0,n.default)({url:"/PRO/Inspection/GetMaterialType",method:"post",data:e})}function R(e){return(0,n.default)({url:"/PRO/Inspection/ExportInspsectionSummaryInfo",method:"post",data:e})}},"9acf":function(e,t,a){"use strict";a("1c46")},"9b61":function(e,t,a){"use strict";a.r(t);var o=a("c2eb"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);t["default"]=n.a},a023:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return n}));var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"form-list"},[a("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"工序类型",prop:"Working_Process_Type"}},[a("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择",disabled:e.isEdit},on:{change:e.processTypeChange},model:{value:e.form.Working_Process_Type,callback:function(t){e.$set(e.form,"Working_Process_Type",t)},expression:"form.Working_Process_Type"}},[a("el-option",{attrs:{label:"构件工序",value:1}}),a("el-option",{attrs:{label:"零件工序",value:2}})],1)],1),a("el-form-item",{attrs:{label:"工序",prop:"Working_Process_Id"}},[a("el-select",{staticClass:"w100",attrs:{clearable:"",filterable:"",placeholder:"请选择",disabled:!e.form.Working_Process_Type||e.isEdit},on:{change:e.processChange},model:{value:e.form.Working_Process_Id,callback:function(t){e.$set(e.form,"Working_Process_Id",t)},expression:"form.Working_Process_Id"}},e._l(e.processList,(function(e){return a("el-option",{key:e.Id,attrs:{label:e.Name,value:e.Id}})})),1)],1),a("el-form-item",{attrs:{label:"核价单位",prop:"Valuation_Unit"}},[a("el-select",{staticClass:"w100",attrs:{clearable:"",placeholder:"请选择",disabled:e.isValuationUnitDisable||e.isEdit},model:{value:e.form.Valuation_Unit,callback:function(t){e.$set(e.form,"Valuation_Unit",t)},expression:"form.Valuation_Unit"}},[a("el-option",{attrs:{label:"重量(t)",value:1}}),a("el-option",{attrs:{label:"长度(m)",value:2}}),a("el-option",{attrs:{label:"数量件",value:3}})],1)],1),a("el-form-item",{attrs:{label:"单位单价(元)",prop:"Unit_Price"}},[a("el-input",{staticClass:"input-number",attrs:{step:"any",type:"number",min:"0"},model:{value:e.form.Unit_Price,callback:function(t){e.$set(e.form,"Unit_Price",t)},expression:"form.Unit_Price"}})],1),a("el-form-item",{staticStyle:{width:"96%"},attrs:{label:"备注",prop:"Remark"}},[a("el-input",{staticClass:"input-number",attrs:{type:"textarea",rows:2},model:{value:e.form.Remark,callback:function(t){e.$set(e.form,"Remark",t)},expression:"form.Remark"}})],1)],1)],1),a("div",{staticClass:"variable-list"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("添加变量")]),a("div",{staticClass:"variable-container"},e._l(e.variableData,(function(t,o){return a("div",{key:o,staticClass:"variable-content"},[a("label",[e._v("变量"+e._s(o+1))]),a("el-select",{staticClass:"selete-first",attrs:{placeholder:"请选择"},on:{change:function(a){return e.changeCustomeKey(t.Custome_Key,o)}},model:{value:t.Custome_Key,callback:function(a){e.$set(t,"Custome_Key",a)},expression:"item.Custome_Key"}},[a("el-option",{attrs:{label:"构件单重",value:1}}),a("el-option",{attrs:{label:"构件长度",value:2}}),a("el-option",{attrs:{label:"构件板厚",value:3}}),a("el-option",{attrs:{label:"构件类型",value:4}})],1),4!==t.Custome_Key?[a("el-select",{staticClass:"select-second",attrs:{placeholder:"请选择"},model:{value:t.Custome_Symbol,callback:function(a){e.$set(t,"Custome_Symbol",a)},expression:"item.Custome_Symbol"}},[a("el-option",{attrs:{label:"小于",value:1}}),a("el-option",{attrs:{label:"小于等于",value:2}}),a("el-option",{attrs:{label:"大于",value:3}}),a("el-option",{attrs:{label:"大于等于",value:4}}),a("el-option",{attrs:{label:"等于",value:5}})],1),a("el-input",{staticClass:"input-last",attrs:{step:"any",placeholder:"请输入",type:"number",min:"0"},model:{value:t.Custome_Value,callback:function(a){e.$set(t,"Custome_Value",a)},expression:"item.Custome_Value"}},[a("template",{slot:"suffix"},[a("span",{staticClass:"unit"},[e._v(e._s(1===t.Custome_Key?"kg":2===t.Custome_Key||3===t.Custome_Key?"mm":"-"))])]),a("template",{slot:"append"},[a("el-button",{attrs:{type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.handleDele(o)}}})],1)],2)]:[a("el-tree-select",{ref:"treeSelectObjectType"+(o+1),refInFor:!0,staticClass:"cs-tree-x select-tree",attrs:{"select-params":e.treeSelectParams,"tree-params":e.ObjectTypeList,"value-key":"Id"},on:{searchFun:function(t){return e._searchFun(t,o+1)}},model:{value:t.Custome_Value,callback:function(a){e.$set(t,"Custome_Value",a)},expression:"item.Custome_Value"}}),a("div",{staticClass:"el-input-group__append",staticStyle:{position:"relative"}},[a("button",{staticClass:"el-button el-button--danger el-button--small",staticStyle:{position:"absolute",top:"10px",left:"20px"},attrs:{"data-v-560a3969":"",type:"button"},on:{click:function(t){return e.handleDele(o)}}},[a("i",{staticClass:"el-icon-delete"})])])]],2)})),0)],1),a("footer",[a("el-button",{on:{click:function(t){return e.$emit("close")}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)])},n=[]},a024:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.AddProcessFlow=u,t.AddProessLib=W,t.AddTechnology=l,t.AddWorkingProcess=s,t.DelLib=x,t.DeleteProcess=k,t.DeleteProcessFlow=v,t.DeleteTechnology=C,t.DeleteWorkingTeams=G,t.GetAllProcessList=f,t.GetCheckGroupList=w,t.GetChildComponentTypeList=V,t.GetFactoryAllProcessList=p,t.GetFactoryPeoplelist=F,t.GetFactoryWorkingTeam=g,t.GetGroupItemsList=_,t.GetLibList=i,t.GetLibListType=E,t.GetProcessFlow=h,t.GetProcessFlowListWithTechnology=m,t.GetProcessList=c,t.GetProcessListBase=d,t.GetProcessListTeamBase=S,t.GetProcessListWithUserBase=R,t.GetProcessWorkingTeamBase=$,t.GetTeamListByUser=U,t.GetTeamProcessList=P,t.GetWorkingTeam=b,t.GetWorkingTeamBase=O,t.GetWorkingTeamInfo=I,t.GetWorkingTeams=T,t.GetWorkingTeamsPageList=D,t.SaveWorkingTeams=L,t.UpdateProcessTeam=y;var n=o(a("b775")),r=o(a("4328"));function i(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibList",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddWorkingProcess",method:"post",data:r.default.stringify(e)})}function l(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddTechnology",method:"post",data:r.default.stringify(e)})}function u(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProcessFlow",method:"post",data:r.default.stringify(e)})}function c(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessList",method:"post",data:r.default.stringify(e)})}function d(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListBase",method:"post",data:r.default.stringify(e)})}function f(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetAllProcessList",method:"post",data:r.default.stringify(e)})}function p(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryAllProcessList",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlow",method:"post",data:r.default.stringify(e)})}function m(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessFlowListWithTechnology",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/TechnologyLib/UpdateProcessTeam",method:"post",data:r.default.stringify(e)})}function g(){return(0,n.default)({url:"/PRO/TechnologyLib/GetFactoryWorkingTeam",method:"post"})}function b(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeam",method:"post",data:r.default.stringify(e)})}function P(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamProcessList",method:"post",data:r.default.stringify(e)})}function _(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetGroupItemsList",method:"post",data:r.default.stringify(e)})}function v(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcessFlow",method:"post",data:r.default.stringify(e)})}function C(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteTechnology",method:"post",data:r.default.stringify(e)})}function k(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteProcess",method:"post",data:e})}function T(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeams",method:"post",data:e})}function D(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamsPageList",method:"post",data:e})}function L(e){return(0,n.default)({url:"/PRO/TechnologyLib/SaveWorkingTeams",method:"post",data:e})}function G(e){return(0,n.default)({url:"/PRO/TechnologyLib/DeleteWorkingTeams",method:"post",data:e})}function I(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamInfo",method:"post",data:r.default.stringify(e)})}function O(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetWorkingTeamBase",method:"post",data:r.default.stringify(e)})}function S(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListTeamBase",method:"post",data:r.default.stringify(e)})}function R(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessListWithUserBase",method:"post",data:e})}function F(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function w(e){return(0,n.default)({url:"/PRO/Inspection/GetCheckGroupList",method:"post",data:e})}function W(e){return(0,n.default)({url:"/PRO/TechnologyLib/AddProessLib",method:"post",data:e})}function V(e){return(0,n.default)({url:"/PRO/ComponentType/GetChildComponentTypeList",method:"post",data:e})}function x(e){return(0,n.default)({url:"/PRO/TechnologyLib/DelLib",method:"post",data:e})}function E(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetLibListType",method:"post",data:e})}function $(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetProcessWorkingTeamBase",method:"post",data:e})}function U(e){return(0,n.default)({url:"/PRO/TechnologyLib/GetTeamListByUser",method:"post",data:e})}},bbc3:function(e,t,a){"use strict";a.r(t);var o=a("6e33"),n=a.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(r);t["default"]=n.a},c2eb:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("d81d"),a("e9f5"),a("ab43"),a("d3b7"),a("25f0");var n=o(a("c14f")),r=o(a("1da1")),i=o(a("15ac")),s=o(a("0f97")),l=o(a("34e9")),u=o(a("f89d")),c=a("f4e9");a("7196"),t.default={name:"PROSalaryQuotaAllocation",components:{DynamicDataTable:s.default,TopHeader:l.default,detail:u.default},mixins:[i.default],data:function(){return{tbConfig:{Pager_Align:"center"},queryInfo:{Page:1,PageSize:20},currentComponent:"",title:"",columns:[],tbData:[],total:0,tbLoading:!1,dialogVisible:!1,selectList:[],keywords:""}},created:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,e.getTableConfig("pro_quota_allocation_list,Steel");case 1:e.fetchData();case 2:return t.a(2)}}),t)})))()},methods:{fetchData:function(){var e=this;this.tbLoading=!0,(0,c.GetQuotaPageList)({Working_Process_Name:this.keywords,PageSize:-1}).then((function(t){t.IsSucceed?(e.tbData=t.Data.Data,e.total=t.Data.TotalCount):e.$message({message:t.Message,type:"error"}),e.tbLoading=!1}))},handleClose:function(){this.dialogVisible=!1},handleSelectionChange:function(e){this.selectList=e},handleAdd:function(){this.currentComponent="detail",this.title="新增定额",this.dialogVisible=!0},handleEdit:function(e){var t=this;this.currentComponent="detail",this.title="编辑定额",this.dialogVisible=!0,this.$nextTick((function(a){t.$refs["content"].initData(e)}))},handleDelete:function(e,t){var a=this,o=e?this.selectList.map((function(e){return e.Id})):[t.Id];this.$confirm("是否删除选中定额配置?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){(0,c.DeleteQuotaList)({Id_List:o}).then((function(e){e.IsSucceed?(a.$message({type:"success",message:"删除成功!"}),a.fetchData()):a.$message({message:e.Message,type:"error"})}))})).catch((function(){a.$message({type:"info",message:"已取消删除"})}))},handleSearch:function(){this.fetchData()},reset:function(){this.keywords="",this.fetchData()}}}},d7a3:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(a("c14f")),r=o(a("1da1")),i=a("5e99"),s=a("fd31"),l=a("7196");t.default={data:function(){return{FactoryDetailData:{},ProfessionalType:[],factoryPeoplelist:[]}},methods:{getCurFactory:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,i.GetCurFactory)({}).then((function(t){t.IsSucceed?e.FactoryDetailData=t.Data[0]:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryTypeOption:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,s.GetFactoryProfessionalByCode)({factoryId:localStorage.getItem("CurReferenceId")}).then((function(t){t.IsSucceed?e.ProfessionalType=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getFactoryPeoplelist:function(){var e=this;return(0,r.default)((0,n.default)().m((function t(){return(0,n.default)().w((function(t){while(1)switch(t.n){case 0:return t.n=1,(0,l.GetFactoryPeoplelist)({}).then((function(t){t.IsSucceed?e.factoryPeoplelist=t.Data:e.$message({message:t.Message,type:"error"})}));case 1:return t.a(2)}}),t)})))()},getManageCycle:function(){if(this.FactoryDetailData.Manage_Cycle_Enabled){var e,t,a,o,n,r,i=(new Date).getFullYear(),s=(new Date).getMonth()+1;1===this.FactoryDetailData.Manage_Cycle_Begin_Type?s-1===0?(e=i-1,t=12):(e=i,t=s-1):2===this.FactoryDetailData.Manage_Cycle_Begin_Type?(e=i,t=s):3===this.FactoryDetailData.Manage_Cycle_Begin_Type&&(s+1===13?(e=i+1,t=1):(e=i,t=s+1)),a=this.checkDate(e,t,this.FactoryDetailData.Manage_Cycle_Begin_Date),1===this.FactoryDetailData.Manage_Cycle_End_Type?s-1===0?(o=i-1,n=12):(o=i,n=s-1):2===this.FactoryDetailData.Manage_Cycle_End_Type?(o=i,n=s):3===this.FactoryDetailData.Manage_Cycle_End_Type&&(s+1===13?(o=i+1,n=1):(o=i,n=s+1)),r=this.checkDate(o,n,this.FactoryDetailData.Manage_Cycle_End_Date);var l=e+"-"+this.zeroFill(t)+"-"+this.zeroFill(a),u=o+"-"+this.zeroFill(n)+"-"+this.zeroFill(r);return[l,u]}return!1},checkDate:function(e,t,a){var o;return o=e%4===0?2===t?a>29?29:a:(4===t||6===t||9===t||11===t)&&a>30?30:a:2===t?a>28?28:a:(4===t||6===t||9===t||11===t)&&a>30?30:a,o},zeroFill:function(e){return e<10?"0"+e:e}}}},f4e9:function(e,t,a){"use strict";var o=a("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.DeleteQuotaList=d,t.DeleteSalary=s,t.ExportPlanSalary=y,t.GetFactoryPeoplelist=u,t.GetFactorySalaryPageList=l,t.GetPlanSalaryDetailList=m,t.GetPlanSalaryPageList=h,t.GetQuotaDetail=p,t.GetQuotaPageList=f,t.ImportSalaryFiles=r,t.SaveQuotaEntity=c,t.UpdateImportSalaryFile=i;var n=o(a("b775"));o(a("4328"));function r(e){return(0,n.default)({url:"/PRO/Factory/ImportSalaryFiles",method:"post",data:e})}function i(e){return(0,n.default)({url:"/PRO/Factory/UpdateImportSalaryFile",method:"post",data:e})}function s(e){return(0,n.default)({url:"/PRO/Factory/DeleteSalary",method:"post",data:e})}function l(e){return(0,n.default)({url:"/PRO/Factory/GetFactorySalaryPageList",method:"post",data:e})}function u(e){return(0,n.default)({url:"/PRO/Factory/GetFactoryPeoplelist",method:"post",data:e})}function c(e){return(0,n.default)({url:"/PRO/ProductionSalary/SaveQuotaEntity",method:"post",data:e})}function d(e){return(0,n.default)({url:"/PRO/ProductionSalary/DeleteQuotaList",method:"post",data:e})}function f(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetQuotaPageList",method:"post",data:e})}function p(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetQuotaDetail",method:"post",data:e})}function h(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetPlanSalaryPageList",method:"post",data:e})}function m(e){return(0,n.default)({url:"/PRO/ProductionSalary/GetPlanSalaryDetailList",method:"post",data:e})}function y(e){return(0,n.default)({url:"/PRO/ProductionSalary/ExportPlanSalary",method:"post",data:e})}},f89d:function(e,t,a){"use strict";a.r(t);var o=a("a023"),n=a("bbc3");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("9acf");var i=a("2877"),s=Object(i["a"])(n["default"],o["a"],o["b"],!1,null,"1bad5384",null);t["default"]=s.exports}}]);